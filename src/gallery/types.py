from typing import TypeAlias, TYPE_CHECKING, Union

if TYPE_CHECKING:

    from gallery.models import (
        <PERSON>y,
        <PERSON>pleBox,
        <PERSON>tty,
        <PERSON><PERSON>,
    )
    from services.models import AdditionalService

FurnitureType: TypeAlias = Union['Jetty', 'Sotty', 'Watty']
SellableItemType = FurnitureType | 'SampleBox'
LineItemType = FurnitureType | 'AdditionalService'
