from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import TYPE_CHECKING, Optional

from gallery.types import SellableItemType
from promotions.models import Promotion
from regions.cached_region import CachedRegionData
from regions.mixins import RegionCalculationsObject
from regions.models import (
    CurrencyRate,
    Region,
)
from regions.types import RegionLikeObject

if TYPE_CHECKING:
    from carts.models import (
        Cart,
    )
    from orders.models import (
        Order,
    )


class RegionCurrencySerializerMixin:
    @property
    def region(self) -> Optional[RegionLikeObject]:
        return self.context.get('region', None)

    @property
    def region_calculations_object(self):
        return self.context.get('region_calculations_object', None)

    @property
    def currency_rate(self) -> Decimal:
        currency_rate = self.context.get('currency_rate', None)
        if not currency_rate:
            return get_currency_rate(self.region)
        return currency_rate


# providing region_calculations_object from view context reduces the number of queries
def get_region_price(
    furniture: SellableItemType,
    region: Optional[CachedRegionData],
    region_calculations_object: Optional[RegionCalculationsObject] = None,
) -> Decimal:
    region_price = furniture.get_regionalized_price(
        region=region,
        region_calculations_object=region_calculations_object,
    )
    return Decimal(region_price).quantize(Decimal('1.'), rounding=ROUND_HALF_UP)


def get_price_in_euro(price: Decimal, currency_rate: Decimal) -> Decimal:
    price_in_euro = price / currency_rate
    return price_in_euro.quantize(Decimal('1.'), rounding=ROUND_HALF_UP)


def get_region_price_in_euro(
    furniture: SellableItemType,
    currency_rate: Decimal,
    region: Optional[CachedRegionData],
    region_calculations_object: Optional[RegionCalculationsObject] = None,
) -> Decimal:
    region_price = get_region_price(
        furniture=furniture,
        region=region,
        region_calculations_object=region_calculations_object,
    )
    return get_price_in_euro(region_price, currency_rate)


def get_region_price_with_discount(
    furniture: SellableItemType,
    region: RegionLikeObject,
    parent: Optional['Cart | Order'] = None,
    region_calculations_object: RegionCalculationsObject | None = None,
    promotion: Promotion | None = None,
) -> Decimal:
    regular_price = get_region_price(
        furniture=furniture,
        region=region,
        region_calculations_object=region_calculations_object,
    )

    sale_price = furniture.get_sale_price(
        regular_price=regular_price,
        promotion=promotion,
        parent=parent,
    )
    return sale_price.quantize(Decimal('1.'), rounding=ROUND_HALF_UP)


def get_region_price_with_discount_from_price(
    region_price: Decimal,
    furniture: SellableItemType,
    promotion: Promotion,
) -> Decimal:
    """The region price is passed to this function to avoid calculating the region price
    again.
    """
    sale_price = furniture.get_sale_price(
        regular_price=region_price,
        promotion=promotion,
    )
    return sale_price.quantize(Decimal('1.'), rounding=ROUND_HALF_UP)


def get_region_price_with_discount_in_euro(
    furniture: SellableItemType,
    currency_rate: Decimal,
    region: Optional[CachedRegionData],
    promotion: Optional[Promotion] = None,
    region_calculations_object: Optional[RegionCalculationsObject] = None,
) -> Decimal:
    sale_price = get_region_price_with_discount(
        furniture=furniture,
        region=region,
        region_calculations_object=region_calculations_object,
        promotion=promotion,
    )
    return get_price_in_euro(sale_price, currency_rate)


def get_currency_rate(region: Optional[CachedRegionData]) -> Decimal:
    region = region or Region.get_other().cached_region_data
    currency_rate = CurrencyRate.objects.filter(currency__id=region.currency_id).first()
    return currency_rate.rate
