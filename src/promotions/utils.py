from dataclasses import dataclass
from typing import (
    TYPE_CHECKING,
    Union,
)

from django.db.models import (
    Q,
    QuerySet,
)
from django.utils import timezone

from promotions.decorators import cached_promo
from promotions.models import (
    Promotion,
    PromotionConfig,
)
from regions.types import RegionLikeObject
from vouchers.enums import VoucherType
from vouchers.models import Voucher

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order


def strikethrough_promo_value(
    region: RegionLikeObject | None = None,
) -> float:
    if active_promotion := strikethrough_promo(region=region):
        return active_promotion.promo_code.value
    return 0


@cached_promo()
def strikethrough_promo(
    region: RegionLikeObject | None = None,
) -> Promotion | None:
    now = timezone.now()
    query = Q(
        strikethrough_pricing=True,
        active=True,
        start_date__lte=now,
        end_date__gte=now,
    )
    if region:
        query &= Q(configs__enabled_regions__id=region.id)
    active_promotion = (
        Promotion.objects.filter(query)
        .select_related('promo_code', 'promo_code__bundle')
        .prefetch_related('promo_code__discounts', 'promo_code__bundle__item_discounts')
        .distinct()
        .first()
    )
    # check if promotion is setup correctly - has suitable promo code assigned
    if (
        active_promotion
        and active_promotion.promo_code
        and active_promotion.promo_code.kind_of == VoucherType.PERCENTAGE
    ):
        return active_promotion
    return None


@dataclass
class StrikethroughPromoData:
    strikethrough_promo: Promotion | None
    strikethrough_voucher: Voucher | None
    has_strikethrough_promo_applied: bool


def get_strikethrough_promo_data(
    instance: Union['Cart', 'Order'],
) -> StrikethroughPromoData:
    strikethrough_promotion = strikethrough_promo(instance.region if instance else None)
    has_strikethrough_promo_applied = False
    strikethrough_voucher = None

    if strikethrough_promotion:
        strikethrough_voucher = strikethrough_promotion.promo_code

        if instance and instance.vouchers.filter(id=strikethrough_voucher.id).exists():
            has_strikethrough_promo_applied = True

    return StrikethroughPromoData(
        strikethrough_promotion,
        strikethrough_voucher,
        has_strikethrough_promo_applied,
    )


def get_active_promotions(
    region: RegionLikeObject | None = None,
) -> QuerySet[Promotion]:
    now = timezone.now()
    filter_query = Q(active=True, start_date__lte=now, end_date__gte=now)
    if region:
        filter_query &= Q(configs__enabled_regions__id=region.id)

    return (
        Promotion.objects.filter(filter_query)
        .select_related(
            'promo_code',
            'promo_code__bundle',
        )
        .prefetch_related(
            'promo_code__discounts',
            'promo_code__bundle__item_discounts',
            'configs',
            'configs__countdown',
            'configs__copies',
            'configs__grid_picture',
            'configs__enabled_regions',
            'configs__copies__ribbon_lines',
        )
    )


@cached_promo()
def get_active_promotion(
    region: RegionLikeObject | None = None,
) -> Promotion | None:
    return get_active_promotions(region=region).last()


def get_active_promotion_config(
    region: RegionLikeObject | None = None,
) -> PromotionConfig | None:
    if active_promotion := get_active_promotion(region=region):
        return active_promotion.configs.prefetch_related('enabled_regions').first()


def clear_promo_cache():
    strikethrough_promo.clear_cache()
    get_active_promotion.clear_cache()
