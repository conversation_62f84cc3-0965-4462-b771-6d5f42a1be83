from dataclasses import (
    astuple,
    dataclass,
)

INVOICE_BCC_EMAILS = ['<EMAIL>']


@dataclass
class VoucherValue:
    value: int = 0
    amount_starts: int = 0

    def __iter__(self):
        return iter(astuple(self))


@dataclass
class SampleVoucherValue:
    value: int
    limit: int


MAILING_VOUCHER_REGIONALIZED_VALUES = {
    'nwl': {
        'EUR': VoucherValue(200, 1000),
        'GBP': VoucherValue(160, 800),
        'CHF': VoucherValue(265, 1300),
        'NOK': VoucherValue(2250, 11300),
        'PLN': VoucherValue(800, 4000),
        'SEK': VoucherValue(2400, 10800),
        'DKK': VoucherValue(1600, 7500),
    },
    's4l': {
        'EUR': VoucherValue(400, 1500),
        'PLN': VoucherValue(1700, 6300),
        'GBP': VoucherValue(425, 1600),
        'CHF': VoucherValue(500, 1900),
        'NOK': VoucherValue(5000, 18800),
        'SEK': VoucherValue(5000, 19000),
        'DKK': VoucherValue(3600, 13500),
    },
    'ca': {
        'EUR': VoucherValue(400, 1500),
        'PLN': VoucherValue(1700, 6300),
        'GBP': VoucherValue(425, 1600),
        'CHF': VoucherValue(500, 1900),
        'NOK': VoucherValue(5000, 18800),
        'SEK': VoucherValue(5000, 19000),
        'DKK': VoucherValue(3600, 13500),
    },
    'spd': {
        'EUR': VoucherValue(400, 1500),
        'PLN': VoucherValue(1700, 6300),
        'GBP': VoucherValue(425, 1600),
        'CHF': VoucherValue(500, 1900),
        'NOK': VoucherValue(5000, 18800),
        'SEK': VoucherValue(5000, 19000),
        'DKK': VoucherValue(3600, 13500),
    },
    'wf0': {
        'EUR': VoucherValue(300, 1250),
        'PLN': VoucherValue(1250, 5200),
        'GBP': VoucherValue(325, 1350),
        'CHF': VoucherValue(380, 1580),
        'NOK': VoucherValue(3800, 15800),
        'SEK': VoucherValue(3800, 15800),
        'DKK': VoucherValue(2700, 11200),
    },
    'wf1': {
        'EUR': VoucherValue(300, 1250),
        'PLN': VoucherValue(1250, 5200),
        'GBP': VoucherValue(325, 1350),
        'CHF': VoucherValue(380, 1580),
        'NOK': VoucherValue(3800, 15800),
        'SEK': VoucherValue(3800, 15800),
        'DKK': VoucherValue(2700, 11200),
    },
    'wf2': {
        'EUR': VoucherValue(400, 1500),
        'PLN': VoucherValue(1700, 6300),
        'GBP': VoucherValue(425, 1600),
        'CHF': VoucherValue(500, 1900),
        'NOK': VoucherValue(5000, 18800),
        'SEK': VoucherValue(5000, 19000),
        'DKK': VoucherValue(3600, 13500),
    },
    's4n': {
        'EUR': VoucherValue(300, 1250),
        'PLN': VoucherValue(1250, 5200),
        'GBP': VoucherValue(325, 1350),
        'CHF': VoucherValue(380, 1580),
        'NOK': VoucherValue(3800, 15800),
        'SEK': VoucherValue(3800, 15800),
        'DKK': VoucherValue(2700, 11200),
    },
    'p0f': {
        'EUR': VoucherValue(650, 1_750),
        'PLN': VoucherValue(2_700, 7_300),
        'GBP': VoucherValue(650, 1_750),
        'CHF': VoucherValue(650, 1_750),
        'NOK': VoucherValue(8_700, 23_300),
        'SEK': VoucherValue(8_700, 23_350),
        'DKK': VoucherValue(5_400, 14_600),
    },
    'p0n': {
        'EUR': VoucherValue(700, 1_750),
        'PLN': VoucherValue(2_900, 7_300),
        'GBP': VoucherValue(700, 1_750),
        'CHF': VoucherValue(700, 1_750),
        'NOK': VoucherValue(9_350, 23_300),
        'SEK': VoucherValue(9_350, 23_350),
        'DKK': VoucherValue(5_850, 14_600),
    },
    'p0s': {
        'EUR': VoucherValue(450, 1_600),
        'PLN': VoucherValue(1_900, 6_700),
        'GBP': VoucherValue(450, 1_600),
        'CHF': VoucherValue(450, 1_600),
        'NOK': VoucherValue(6_000, 21_300),
        'SEK': VoucherValue(6_000, 21_350),
        'DKK': VoucherValue(3_750, 13_350),
    },
    'frnd1': {
        'EUR': VoucherValue(50, 0),
        'PLN': VoucherValue(220, 0),
        'GBP': VoucherValue(45, 0),
        'CHF': VoucherValue(50, 0),
        'NOK': VoucherValue(780, 0),
        'SEK': VoucherValue(550, 0),
        'DKK': VoucherValue(390, 0),
    },
    'frnd2': {
        'EUR': VoucherValue(50, 0),
        'PLN': VoucherValue(220, 0),
        'GBP': VoucherValue(45, 0),
        'CHF': VoucherValue(50, 0),
        'NOK': VoucherValue(780, 0),
        'SEK': VoucherValue(550, 0),
        'DKK': VoucherValue(390, 0),
    },
    'TSHB': {
        'EUR': VoucherValue(50, 300),
    },
    'TSHN': {
        'EUR': VoucherValue(50, 300),
    },
    'TSDR': {
        'EUR': VoucherValue(50, 300),
    },
    'TSFR': {
        'EUR': VoucherValue(50, 300),
    },
    'TSMU': {
        'EUR': VoucherValue(50, 300),
    },
    'TSWN': {
        'EUR': VoucherValue(50, 300),
    },
}

STACKABLE_VOUCHERS_PREFIXES: set[str] = {'TSHB', 'TSHN', 'TSDR', 'TSFR', 'TSMU', 'TSWN'}

NEWSLETTER_VOUCHER_VALUES = MAILING_VOUCHER_REGIONALIZED_VALUES['wf1']

MAILING_VOUCHER_SAMPLE_LIMITS = {
    'wfs': {  # limit samples to 3
        'EUR': SampleVoucherValue(50, 15),
        'GBP': SampleVoucherValue(50, 15),
        'DKK': SampleVoucherValue(50, 135),
        'CHF': SampleVoucherValue(50, 18),
        'NOK': SampleVoucherValue(50, 189),
        'SEK': SampleVoucherValue(50, 135),
        'PLN': SampleVoucherValue(50, 63),
    },
}
