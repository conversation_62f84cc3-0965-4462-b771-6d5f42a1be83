from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    TYPE_CHECKING,
    Optional,
    Union,
)

from django.db.models import QuerySet

from regions.types import RegionLikeObject
from vouchers.models import Voucher
from vouchers.utils import convert_absolute_voucher_to_percentage

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from orders.models import (
        Order,
        OrderItem,
    )


class VoucherCalculator:
    """
    Calculates the total promo amount for a given cart/order
    after applying all applicable vouchers.
    """

    def __init__(
        self,
        instance: Union['Cart', 'Order'],
        vouchers: QuerySet[Voucher],
        region: Optional[RegionLikeObject] = None,
    ) -> None:
        self.instance = instance
        self.region = region
        self.vouchers = vouchers

    def __call__(self) -> Decimal:
        return self._calculate_region_promo_value()

    def _calculate_region_promo_value(self) -> Decimal:
        """
        Calculates the total price by stacking all applicable vouchers.

        The discount calculator works as follows:
        total_price = initial_price * (1 - (percentage_voucher_1+percentage_voucher_2))

        - For example, two 10% discounts result in a 20% total discount.
        """
        region_total_price = self.instance.get_total_value()
        return min(
            sum(
                (
                    voucher.calculate_promo_amount(self.instance, self.region)
                    for voucher in self.vouchers
                ),
                Decimal('0.00'),
            ),
            region_total_price,
        )


class VoucherItemCalculator:
    """
    Calculates the total promo amount for a given cartItem/orderItem
    after applying all applicable vouchers.
    """

    def __init__(
        self,
        item: Union['CartItem', 'OrderItem'],
    ) -> None:
        self.item = item
        self.instance = getattr(self.item, 'cart', None) or self.item.order
        self.vouchers = self.instance.vouchers.all()

    @staticmethod
    def _round_up_to_hundredth(value: Decimal) -> Decimal:
        return Decimal(value).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def _round_up_to_integer(value: Decimal) -> Decimal:
        return Decimal(value).quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    def _check_if_voucher_apply(self, voucher: Voucher) -> bool:
        return voucher.check_item_applicability(item=self.item)

    def calculate_delivery_promo_value(self):
        return sum(
            (
                self._calculate_delivery_promo_value(voucher)
                for voucher in self.vouchers
            ),
            Decimal('0.00'),
        )

    def _calculate_delivery_promo_value(self, voucher: Voucher):
        return self.item.region_delivery_price - self._round_up_to_integer(
            voucher.get_delivery_price_with_discount(self.item.region_delivery_price)
        )

    def calculate_region_promo_value(self) -> Decimal:
        return sum(
            (
                self._calculate_region_promo_value(voucher)
                for voucher in self.vouchers
                if self._check_if_voucher_apply(voucher)
            ),
            Decimal('0.00'),
        )

    def _calculate_region_promo_value(self, voucher) -> Decimal:
        if voucher.is_percentage():
            return self._round_up_to_hundredth(
                self.item.region_price
                - voucher.calculate_price_for_furniture(
                    furniture=self.item.sellable_item,
                    price=self.item.region_price,
                    instance=self.instance,
                )
            )
        else:
            promo_value_rate = convert_absolute_voucher_to_percentage(
                instance=self.instance, voucher=voucher
            )
            return self._round_up_to_hundredth(
                self.item.region_price * promo_value_rate
            )
