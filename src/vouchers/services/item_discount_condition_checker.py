from decimal import Decimal
from functools import lru_cache
from typing import TYPE_CHECKING

from django.db.models import QuerySet

from gallery.types import SellableItemType
if TYPE_CHECKING:
    from vouchers.models import ItemDiscount, Voucher


def get_bundle_discount_price(
    voucher: 'Voucher',
    parent: 'Cart | Order',
    sellable_item: SellableItemType,
    regular_price: Decimal,
) -> Decimal | None:
    if not (voucher.has_bundle and parent):
        return

    bundle_discounts = voucher.bundle.item_discounts.all()
    items = (item.sellable_item for item in parent.items.all())

    if not does_bundle_apply_to_items(voucher, items, bundle_discounts):
        return

    for discount in bundle_discounts:
        if does_discount_apply(discount, sellable_item):
            return discount.calculate_price(regular_price)


@lru_cache(maxsize=1000)
def does_bundle_apply_to_items(
    voucher: 'Voucher',
    items: tuple[SellableItemType],
    bundle_discounts: QuerySet['ItemDiscount'],
) -> bool:
    """Bundle applies when all conditions from various ItemDiscounts are met
    for given items."""
    if not voucher.bundle:
        return False

    return all(
        any(does_discount_apply(discount, item) for item in items)
        for discount in bundle_discounts
    )

@lru_cache(maxsize=5000)
def does_discount_apply(
    discount: 'ItemDiscount',
    item: SellableItemType,
) -> bool:
    """If all filter fields are the same at self and item - then the discount is
    valid. In other words - filters are validated on AND logic.
    If ItemDiscount is about a service - it's not counted for one item, but for
    whole order.
    Made a separate function to make caching easier.
    """
    if discount.service_type:
        return False
    for filter_field in discount.FILTER_FIELDS:
        if getattr(discount, filter_field) in {None, ''}:
            # if instance does not store value in given field - skip
            continue
        try:
            if getattr(item, filter_field) != getattr(discount, filter_field):
                # if discount value is different from item quality -
                # discount is not valid
                return False
        except AttributeError:
            # item might be a jetty, watty or sample box, and they vary in fields
            # we're filtering on. So if we want to discount box_variant this should
            # not apply to jetty, etc.
            return False
    return True
