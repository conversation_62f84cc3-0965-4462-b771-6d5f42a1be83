from datetime import (
    datetime,
    timedelta,
)
from decimal import Decimal
from typing import Any

from django.utils import timezone
from rest_framework.exceptions import ValidationError

import pytest

from freezegun import freeze_time

from custom.enums import (
    Furniture,
    ShelfType,
)
from mailing.constants import (
    MAILING_VOUCHER_REGIONALIZED_VALUES,
    MAILING_VOUCHER_SAMPLE_LIMITS,
    VoucherValue,
)
from regions.models import Region
from vouchers.enums import (
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import Voucher
from vouchers.serializers import (
    MailingAbsoluteVoucherSerializer,
    MailingPercentageVoucherSerializer,
    MailingSampleSerializer,
)


@pytest.fixture
def end_date(serializer_data):
    return timezone.now() + timedelta(days=serializer_data['duration'])


@pytest.mark.django_db
class TestMailingAbsoluteVoucherSerializer:
    @pytest.fixture
    def serializer_data(self) -> dict[str, Any]:
        return {
            'prefix': 'test',
            'duration': 3,
            'for_email': '<EMAIL>',
            'exclude_wardrobes': False,
            'exclude_samples': False,
        }

    @pytest.fixture
    def create_voucher_data(self, serializer_data) -> dict[str, Any]:
        return {
            'origin': VoucherOrigin.MAILING,
            'amount_starts': 100,
            'quantity': 1,
            'quantity_left': 1,
            'amount_limit': Decimal('1000000'),
            'kind_of': VoucherType.ABSOLUTE,
            'value': 10,
            'for_email': '<EMAIL>',
            'end_date': timezone.now() + timedelta(days=serializer_data['duration']),
            'code': 'test123',
        }

    @pytest.fixture
    def patch_mailing_voucher_regionalized_values(self, monkeypatch, serializer_data):
        region_voucher_values = {
            'EUR': VoucherValue(10, 100),
            'GBP': VoucherValue(11, 111),
        }
        return monkeypatch.setitem(
            MAILING_VOUCHER_REGIONALIZED_VALUES,
            serializer_data['prefix'],
            region_voucher_values,
        )

    def test_created_voucher_valid_data(
        self,
        serializer_data,
        end_date,
        admin_user,
        patch_mailing_voucher_regionalized_values,
    ):
        voucher_values = MAILING_VOUCHER_REGIONALIZED_VALUES[serializer_data['prefix']][
            'EUR'
        ]
        today = timezone.now()
        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)

        assert serializer.is_valid()

        voucher = serializer.create(validated_data=serializer_data)

        assert voucher == Voucher.objects.last()
        assert voucher.value == Decimal(voucher_values.value)
        assert voucher.amount_starts == Decimal(voucher_values.amount_starts)
        assert voucher.kind_of == VoucherType.ABSOLUTE
        assert voucher.end_date.year == end_date.year
        assert voucher.end_date.month == end_date.month
        assert voucher.end_date.day == end_date.day
        assert voucher.code.startswith(serializer_data['prefix'])
        assert voucher.origin == VoucherOrigin.MAILING
        assert voucher.quantity == 1
        assert voucher.quantity_left == 1
        assert voucher.creator == admin_user
        assert voucher.amount_limit == Decimal(1000000)
        assert voucher.start_date.year == today.year
        assert voucher.start_date.month == today.month
        assert voucher.start_date.day == today.day

        assert serializer.to_representation(voucher) == {
            'Promocode': voucher.code,
            'end_date': end_date.strftime('%d.%m.%Y'),
            'days_left': 2,
        }

    def test_created_voucher_valid_data_declared_end_date(
        self,
        serializer_data,
        admin_user,
        patch_mailing_voucher_regionalized_values,
    ):
        del serializer_data['duration']
        end_date = timezone.now() + timedelta(days=10)

        serializer_data['end_date'] = end_date

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        assert serializer.is_valid()

        voucher = serializer.create(validated_data=serializer_data)
        assert voucher.end_date == end_date

    def test_created_voucher_invalid_data_no_end_date_or_duration(
        self,
        serializer_data,
        admin_user,
        patch_mailing_voucher_regionalized_values,
    ):
        del serializer_data['duration']

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)

        assert not serializer.is_valid()
        assert (
            'You need to pass "duration" or "end_date" parameter'
            in serializer.errors['non_field_errors']
        )

    def test_is_not_valid_when_prefix_not_in_mailing_voucher_regionalized_values(
        self,
        serializer_data,
    ):
        assert serializer_data['prefix'] not in MAILING_VOUCHER_REGIONALIZED_VALUES

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)

        assert not serializer.is_valid()
        assert (
            serializer.errors['prefix']['Invalid voucher configuration']
            == 'Invalid prefix for predefined voucher.'
        )

    def test_return_voucher_for_given_params_if_found(
        self,
        serializer_data,
        create_voucher_data,
        patch_mailing_voucher_regionalized_values,
        voucher_factory,
        admin_user,
    ):
        voucher = voucher_factory(**create_voucher_data)

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.id == voucher.id
        assert Voucher.objects.count() == 1

    def test_create_new_voucher_if_not_found_for_given_params(
        self,
        serializer_data,
        patch_mailing_voucher_regionalized_values,
        admin_user,
    ):
        assert Voucher.objects.count() == 0

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert Voucher.objects.count() == 1
        assert Voucher.objects.last() == serializer_voucher

    @pytest.mark.parametrize(
        ('prefix', 'stackable'), [('TSHB', True), ('frnd1', False)]
    )
    def test_create_new_stackable_voucher(
        self, serializer_data, prefix, stackable, admin_user
    ):
        serializer_data['prefix'] = prefix

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)

        assert serializer.is_valid()

        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.is_stackable == stackable

    def test_get_end_date_based_on_duration_value(self):
        duration = 1
        with freeze_time(datetime.now()):
            end_date = timezone.now() + timedelta(days=duration)

            assert (
                MailingAbsoluteVoucherSerializer._get_end_date({'duration': duration})
                == end_date
            )

    def test_create_new_voucher_if_end_date_is_later_then_in_found_voucher(
        self,
        serializer_data,
        create_voucher_data,
        patch_mailing_voucher_regionalized_values,
        voucher_factory,
        admin_user,
    ):
        voucher = voucher_factory(**create_voucher_data)
        serializer_data['duration'] += 1
        end_date = timezone.now() + timedelta(days=serializer_data['duration'])

        assert end_date.date() > voucher.end_date.date()
        assert Voucher.objects.count() == 1

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.id != voucher.id
        assert Voucher.objects.count() == 2
        assert serializer_voucher.end_date > voucher.end_date
        assert serializer_voucher.end_date.date() == end_date.date()

    def test_return_found_voucher_with_the_closest_later_end_date(
        self,
        serializer_data,
        end_date,
        voucher_factory,
        create_voucher_data,
        patch_mailing_voucher_regionalized_values,
        admin_user,
    ):
        voucher_data_with_later_end_date = create_voucher_data.copy()
        voucher_data_with_later_end_date['end_date'] = end_date + timedelta(days=1)
        voucher_data_with_later_end_date['code'] = 'test456'

        voucher = voucher_factory(**create_voucher_data)
        voucher_with_later_end_date = voucher_factory(
            **voucher_data_with_later_end_date
        )

        assert Voucher.objects.count() == 2

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.id == voucher.id
        assert Voucher.objects.count() == 2
        assert voucher_with_later_end_date.end_date > serializer_voucher.end_date

    def test_create_region_entries_if_voucher_created(
        self,
        serializer_data,
        patch_mailing_voucher_regionalized_values,
        admin_user,
        region_factory,
    ):
        # delete auto-created regions
        Region.objects.all().delete()

        germany = region_factory(germany=True)
        uk = region_factory(united_kingdom=True)

        assert (
            germany.currency.code
            in (MAILING_VOUCHER_REGIONALIZED_VALUES[serializer_data['prefix']])
        )
        assert (
            uk.currency.code
            in (MAILING_VOUCHER_REGIONALIZED_VALUES[serializer_data['prefix']])
        )

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert Voucher.objects.count() == 1
        assert serializer_voucher.region_entries.count() == 2

    def test_region_entries_are_not_created_when_voucher_found(
        self,
        serializer_data,
        voucher_factory,
        create_voucher_data,
        end_date,
        patch_mailing_voucher_regionalized_values,
        admin_user,
    ):
        voucher = voucher_factory(**create_voucher_data)

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.id == voucher.id
        assert Voucher.objects.count() == 1
        assert serializer_voucher.region_entries.count() == 0

    def test_apply_excludes_for_wardrobe_if_exclude_wardrobes_is_true_and_created(
        self,
        serializer_data,
        patch_mailing_voucher_regionalized_values,
        admin_user,
    ):
        serializer_data['exclude_wardrobes'] = True

        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.item_conditionals == {
            'exclude': [
                {'shelf_types': [ShelfType.TYPE03]},
                {'shelf_types': [ShelfType.SOFA_TYPE01]},
            ]
        }

    def test_apply_excludes_for_samples_if_exclude_samples_is_true_and_created(
        self,
        serializer_data,
        patch_mailing_voucher_regionalized_values,
        admin_user,
    ):
        serializer_data['exclude_samples'] = True
        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.item_conditionals == {
            'exclude': [
                {'furniture_types': [Furniture.sample_box.value]},
                {'shelf_types': [ShelfType.SOFA_TYPE01]},
            ],
        }

    @pytest.mark.parametrize(
        'input_condition, output_condition',  # noqa: PT006
        [
            (
                [ShelfType.TYPE01.value],
                {
                    'exclude': [
                        {'shelf_types': [ShelfType.TYPE01.value]},
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
            (
                [ShelfType.TYPE02.value],
                {
                    'exclude': [
                        {'shelf_types': [ShelfType.TYPE02.value]},
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
            (
                [ShelfType.VENEER_TYPE01.value],
                {
                    'exclude': [
                        {'shelf_types': [ShelfType.VENEER_TYPE01.value]},
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
            (
                [ShelfType.TYPE03.value],
                {
                    'exclude': [
                        {'shelf_types': [ShelfType.TYPE03.value]},
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
            (
                [ShelfType.TYPE13.value],
                {
                    'exclude': [
                        {'shelf_types': [ShelfType.TYPE13.value]},
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
            (
                [ShelfType.TYPE01.value, ShelfType.TYPE02.value],
                {
                    'exclude': [
                        {
                            'shelf_types': [
                                ShelfType.TYPE01.value,
                                ShelfType.TYPE02.value,
                            ]
                        },
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
            (
                [
                    ShelfType.TYPE01.value,
                    ShelfType.TYPE02.value,
                    ShelfType.VENEER_TYPE01.value,
                ],
                {
                    'exclude': [
                        {
                            'shelf_types': [
                                ShelfType.TYPE01.value,
                                ShelfType.TYPE02.value,
                                ShelfType.VENEER_TYPE01.value,
                            ]
                        },
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
            (
                [
                    ShelfType.TYPE01.value,
                    ShelfType.TYPE02.value,
                    ShelfType.VENEER_TYPE01.value,
                    ShelfType.TYPE03.value,
                    ShelfType.TYPE13.value,
                ],
                {
                    'exclude': [
                        {
                            'shelf_types': [
                                ShelfType.TYPE01.value,
                                ShelfType.TYPE02.value,
                                ShelfType.VENEER_TYPE01.value,
                                ShelfType.TYPE03.value,
                                ShelfType.TYPE13.value,
                            ]
                        },
                        {'shelf_types': [ShelfType.SOFA_TYPE01.value]},
                    ]
                },
            ),
        ],
    )
    def test_apply_excludes_for_types(
        self,
        serializer_data,
        patch_mailing_voucher_regionalized_values,
        admin_user,
        input_condition,
        output_condition,
    ):
        serializer_data['exclude_types'] = input_condition
        serializer = MailingAbsoluteVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.item_conditionals == output_condition


class TestMailingPercentageVoucherSerializer:
    @pytest.fixture
    def serializer_data(self):
        return {
            'prefix': 'test',
            'duration': 3,
            'amount': 20,
            'for_email': '<EMAIL>',
            'exclude_wardrobes': False,
            'exclude_samples': False,
        }

    @pytest.fixture
    def create_voucher_data(self, serializer_data):
        return {
            'origin': VoucherOrigin.MAILING,
            'amount_starts': 0,
            'quantity': 1,
            'quantity_left': 1,
            'amount_limit': Decimal('1000000'),
            'kind_of': VoucherType.PERCENTAGE,
            'value': serializer_data['amount'],
            'for_email': '<EMAIL>',
            'code': 'test123',
            'end_date': timezone.now() + timedelta(days=serializer_data['duration']),
        }

    @pytest.fixture
    def patch_mailing_voucher_regionalized_values(self, monkeypatch, serializer_data):
        region_voucher_values = {
            'EUR': VoucherValue(10, 100),
            'GBP': VoucherValue(11, 111),
        }
        return monkeypatch.setitem(
            MAILING_VOUCHER_REGIONALIZED_VALUES,
            serializer_data['prefix'],
            region_voucher_values,
        )

    def test_created_voucher_valid_data(
        self,
        serializer_data,
        end_date,
        admin_user,
    ):
        today = datetime.today()
        serializer = MailingPercentageVoucherSerializer(data=serializer_data)
        serializer.is_valid(raise_exception=True)

        voucher = serializer.save()

        assert voucher.value == serializer_data['amount']
        assert voucher.amount_starts == 0
        assert voucher.kind_of == VoucherType.PERCENTAGE
        assert voucher.end_date.date().isoformat() == end_date.date().isoformat()
        assert voucher.code.startswith(serializer_data['prefix'])
        assert voucher.origin == VoucherOrigin.MAILING
        assert voucher.quantity == 1
        assert voucher.quantity_left == 1
        assert voucher.creator == admin_user
        assert voucher.amount_limit == Decimal(1_000_000)
        assert voucher.start_date.date().isoformat() == today.date().isoformat()

        assert serializer.to_representation(voucher) == {
            'Promocode': voucher.code,
            'end_date': end_date.strftime('%d.%m.%Y'),
            'days_left': 2,
            'value': '20.0%',
        }

    def test_create_voucher_with_item_discounts(
        self, serializer_data, admin_user, item_discount_factory
    ):
        type01_discount = item_discount_factory(value=40, shelf_type=ShelfType.TYPE01)
        sotty_discount = item_discount_factory(
            value=5, furniture_type=Furniture.sotty.value
        )
        serializer_data['item_discounts'] = [type01_discount.id, sotty_discount.id]
        serializer = MailingPercentageVoucherSerializer(data=serializer_data)
        serializer.is_valid(raise_exception=True)

        voucher = serializer.save()

        type01_discount.refresh_from_db()
        sotty_discount.refresh_from_db()
        assert type01_discount.voucher_set.first() == voucher
        assert sotty_discount.voucher_set.first() == voucher

    def test_validate_not_existing_item_discount(self, serializer_data, admin_user):
        serializer_data['item_discounts'] = [999]
        serializer = MailingPercentageVoucherSerializer(data=serializer_data)

        with pytest.raises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_return_voucher_for_given_params_if_found(
        self,
        serializer_data,
        create_voucher_data,
        voucher_factory,
        admin_user,
    ):
        voucher = voucher_factory(**create_voucher_data)

        serializer = MailingPercentageVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert serializer_voucher.id == voucher.id
        assert Voucher.objects.count() == 1

    def test_create_new_voucher_if_not_found_for_given_params(
        self,
        serializer_data,
        patch_mailing_voucher_regionalized_values,
        admin_user,
    ):
        assert Voucher.objects.count() == 0

        serializer = MailingPercentageVoucherSerializer(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert Voucher.objects.count() == 1
        assert Voucher.objects.last() == serializer_voucher


@pytest.mark.django_db
class TestMailingSampleSerializer:
    serializer_class = MailingSampleSerializer

    @pytest.fixture
    def serializer_data(self) -> dict[str, Any]:
        return {
            'prefix': 'wfs',
            'duration': 10,
            'end_date': '2025-08-21T06:57:04.580Z',
            'for_email': '<EMAIL>',
        }

    def test_create_new_voucher(self, serializer_data, admin_user):
        serializer = self.serializer_class(data=serializer_data)
        serializer_voucher = serializer.create(validated_data=serializer_data)

        assert Voucher.objects.count() == 1
        assert Voucher.objects.last() == serializer_voucher
        assert (
            serializer_voucher.value
            == MAILING_VOUCHER_SAMPLE_LIMITS[serializer_data['prefix']]['EUR'].value
        )
