import datetime
import random

from decimal import Decimal

from django.db.models.signals import post_save
from django.utils import timezone

import factory

from factory import fuzzy

from vouchers.enums import (
    ServiceType,
    VoucherOrigin,
    VoucherType,
)


class VoucherSettingsFactory(factory.django.DjangoModelFactory):
    _b2b_voucher_percentage_value = factory.Faker('pyint', min_value=0, max_value=99)

    class Meta:
        model = 'vouchers.VoucherSettings'


class VoucherGroupFactory(factory.django.DjangoModelFactory):
    code = factory.Faker('text', max_nb_chars=255)
    region = factory.RelatedFactory('regions.tests.factories.RegionFactory')

    class Meta:
        model = 'vouchers.VoucherGroup'


@factory.django.mute_signals(post_save)
class VoucherFactory(factory.django.DjangoModelFactory):
    code = factory.Sequence(lambda counter: f'voucher{counter}')
    value = fuzzy.FuzzyFloat(low=1, high=100)
    kind_of = fuzzy.FuzzyChoice(
        choices=[type_value for type_value, _ in VoucherType.choices],
    )
    creator = factory.SubFactory(
        'user_profile.tests.factories.UserFactory',
        is_admin=True,
    )
    origin = fuzzy.FuzzyChoice(
        choices=[origin_value for origin_value, _ in VoucherOrigin.choices],
    )
    start_date = fuzzy.FuzzyDateTime(
        start_dt=datetime.datetime(
            2020,
            7,
            30,
            8,
            22,
            tzinfo=datetime.UTC,
        ),
    )
    quantity = fuzzy.FuzzyInteger(low=1, high=1000)
    amount_starts = 0
    amount_limit = 1_000_000
    group = None
    active = True

    class Meta:
        model = 'vouchers.Voucher'
        django_get_or_create = ('code',)

    class Params:
        is_percentage = factory.Trait(kind_of=VoucherType.PERCENTAGE)
        is_absolute = factory.Trait(
            kind_of=VoucherType.ABSOLUTE,
            value=fuzzy.FuzzyFloat(low=50, high=300),
        )
        is_b2b = factory.Trait(
            active=True,
            origin=VoucherOrigin.B2B,
            kind_of=VoucherType.PERCENTAGE,
            code=factory.Sequence(lambda counter: f'b2b-voucher{counter}'),
        )

    @factory.lazy_attribute
    def end_date(self):
        if self.start_date:
            return timezone.now() + datetime.timedelta(days=14)
        return None

    @factory.lazy_attribute
    def quantity_left(self):
        return random.randint(1, self.quantity)  # noqa: S311


@factory.django.mute_signals(post_save)
class VoucherRegionEntryFactory(factory.django.DjangoModelFactory):
    value = fuzzy.FuzzyDecimal(low=5, high=1000)
    amount_starts = fuzzy.FuzzyDecimal(low=0, high=100)
    amount_limit = Decimal(1_000_000)
    region = factory.SubFactory('regions.tests.factories.RegionFactory')
    voucher = factory.SubFactory('vouchers.tests.factories.VoucherFactory')

    class Meta:
        model = 'vouchers.VoucherRegionEntry'
        django_get_or_create = ('region', 'voucher')


class ItemDiscountConditionalsFactory(factory.DictFactory):
    shelf_type = 0


class ItemDiscountFactory(factory.django.DjangoModelFactory):
    value = fuzzy.FuzzyFloat(low=1, high=100)

    class Meta:
        model = 'vouchers.ItemDiscount'

    class Params:
        is_half_price_delivery = factory.Trait(
            value=50,
            kind_of=VoucherType.PERCENTAGE,
            service_type=ServiceType.DELIVERY,
        )


class VoucherBundleFactory(factory.django.DjangoModelFactory):
    voucher = factory.SubFactory('vouchers.tests.factories.VoucherFactory')

    class Meta:
        model = 'vouchers.VoucherBundle'

    @factory.post_generation
    def item_discounts(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for item_discount in extracted:
                self.item_discounts.add(item_discount)


class VoucherBarterDealFactory(factory.django.DjangoModelFactory):
    value = fuzzy.FuzzyFloat(low=1, high=100)
    currency = factory.SubFactory('regions.tests.factories.CurrencyFactory')
    voucher = factory.SubFactory('vouchers.tests.factories.VoucherFactory')

    class Meta:
        model = 'vouchers.VoucherBarterDeal'
