from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from catalogue.views import CatalogueMixin
from custom.api_clients.synerise.client import synerise_client_factory
from marketing.serializers import RecommendationSerializer
from marketing.services.recommendations import RecommendationService


class RecommendationView(CatalogueMixin, GenericAPIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        viewed_products_limit = self.request.GET.get('viewedProductsLimit') or 0
        add_to_cart_products_limit = self.request.GET.get('addToCartProductsLimit') or 0
        favorite_products_limit = self.request.GET.get('favoriteProductsLimit') or 0

        synerise_client = synerise_client_factory()
        recommendation_service = RecommendationService(
            user=request.user,
            user_uuid=request.COOKIES.get('_snrs_uuid'),
            recommender_client=synerise_client,
            viewed_products_limit=viewed_products_limit,
            add_to_cart_products_limit=add_to_cart_products_limit,
            favorite_products_limit=favorite_products_limit,
        )
        items, recommendation_type = recommendation_service.get_recommendations()
        serializer = RecommendationSerializer(
            items, many=True, context=self.get_serializer_context()
        )
        return Response(
            data={'products': serializer.data, 'type': recommendation_type},
            status=status.HTTP_200_OK,
        )
