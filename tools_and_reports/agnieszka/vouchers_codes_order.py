"""Marketing order for vouchers' codes, 3 packs of 500 vouchers
"""

from datetime import datetime

from django.contrib.auth.models import User

from custom.utils.exports import dump_list_as_csv
from vouchers.models import ItemDiscount, Voucher


end_date = datetime(2022, 12, 21)           # End day for all packs is 21.12.2022
user = User.objects.get(username='aga')     # TODO: Change it
discount_for_samples_excluded, _ = ItemDiscount.objects.get_or_create(
    item_conditionals={'furniture_type': 'sample_box'},
    value=0,
    kind_of=1
)


# PACK NR 1 (35% jetties, 15% watties)

codes_pack_1 = []
discount_for_watties_pack_1, _ = ItemDiscount.objects.get_or_create(
    item_conditionals={'shelf_type': 3},
    value=15,                                   # 15% discount for watties
    kind_of=1
)

for i in range(500):
    code = Voucher.generate_code(
        inside_string='DTP',                     # Prefix 'DTP'
        inside_string_at_beginning=True
    )
    voucher = Voucher.objects.create(
        origin=5,                                # Mailing origin
        kind_of=1,                               # Voucher percentage
        creator=user,
        code=code,
        value=35,                               # 35% discount for jetties
        quantity=-1,                            # Quantity is unlimited
        amount_starts=0,                        # No bottom limit
        amount_limit=1000000,                   # Upper limit 1000000
        end_date=end_date,
    )
    voucher.discounts.add(
        discount_for_watties_pack_1.pk,
        discount_for_samples_excluded.pk
    )
    codes_pack_1.append(code)


# PACK NR 2 (22% jetties, 11% watties)

codes_pack_2 = []

discount_for_watties_pack_2, _ = ItemDiscount.objects.get_or_create(
    item_conditionals={'shelf_type': 3},
    value=11,                                   # 11% discount for watties
    kind_of=1
)

for i in range(500):
    code = Voucher.generate_code(
        inside_string='RTP',                    # Prefix 'RTP'
        inside_string_at_beginning=True
    )
    voucher = Voucher.objects.create(
        origin=7,                               # Referral origin
        kind_of=1,                              # Voucher percentage
        creator=user,
        code=code,
        value=22,                               # 22% discount for jetties
        quantity=1,                             # One-time voucher
        amount_starts=100,                      # Bottom limit 100 Euro
        quantity_left=1,
        amount_limit=100000,                    # Upper limit 100 000 Euro
        end_date=end_date,
    )
    voucher.discounts.add(
        discount_for_watties_pack_2.pk,
        discount_for_samples_excluded.pk
    )
    codes_pack_2.append(code)


# PACK NR 3 (free samples)

codes_pack_3 = []
discount_for_jetties_excluded, _ = ItemDiscount.objects.get_or_create(
    item_conditionals={'furniture_type': 'jetty'},
    value=0,
    kind_of=1
)
discount_for_watties_excluded, _ = ItemDiscount.objects.get_or_create(
    item_conditionals={'shelf_type': 3},
    value=0,
    kind_of=1
)

# FIXME: Enable order of a wardrobe or jetty with free sample boxes (problem with limit)
for i in range(500):
    code = Voucher.generate_code(
        inside_string='MSD',                    # Prefix 'MSD'
        inside_string_at_beginning=True
    )
    voucher = Voucher.objects.create(
        origin=5,                               # Mailing origin
        kind_of=1,                              # Voucher percentage
        creator=user,
        code=code,
        value=100,                              # 100% discount
        quantity=1,                             # One-time voucher
        amount_starts=5,                        # Sample box costs 5 Euros
        quantity_left=1,
        amount_limit=80,                        # Amount limit 80 Euro (16 sample boxes)
        end_date=end_date,
    )
    voucher.discounts.add(
        discount_for_watties_excluded.pk,
        discount_for_jetties_excluded.pk
    )
    codes_pack_3.append(code)


dump_list_as_csv(codes_pack_1, open('/tmp/codes_pack_1.csv', 'w'))
dump_list_as_csv(codes_pack_2, open('/tmp/codes_pack_2.csv', 'w'))
dump_list_as_csv(codes_pack_3, open('/tmp/codes_pack_3.csv', 'w'))
