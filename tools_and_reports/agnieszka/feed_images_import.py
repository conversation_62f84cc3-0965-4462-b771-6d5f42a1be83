from django.contrib.contenttypes.models import ContentType
from django.db.models import QuerySet

from feeds.models import FeedImage
from feeds.models import ImageConfigOption
from feeds.models import FeedCategory as NewFeedCategory
from feeds.models import FeedItem as NewFeedItem
from gallery.enums import FurnitureCategory
from product_feeds.models import FeedItemImage


old_to_new_categories_mapping = {
    'Real photos - Bedside Table (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.BEDSIDE_TABLE,
        'new_name': 'Bedside Table - Real Photos'
    },
    'Real photos - Sideboard (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.SIDEBOARD,
        'new_name': 'Sideboard - Real Photos',
    },
    'Real photos - Bookcase (DE) (NL) (EN) (FR) (ES)': {
        'category': FurnitureCategory.BOOKCASE,
        'new_name': 'Bookcase - Real Photos',
    },
    'Real photos - Wall storage (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.WALL_STORAGE,
        'new_name': 'Wall Storage - Real Photos',
    },
    'Real photos - Wardrobe (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.WARDROBE,
        'new_name': 'Wardrobe - Real Photos',
    },
    'Real photos - Desk (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.DESK,
        'new_name': 'Desk - Real Photos',
    },
    'Real photos - TV Stand (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.TV_STAND,
        'new_name': 'TV Stand - Real Photos',
    },
    'Real photos - Vinyl Shelf (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.VINYL_STORAGE,
        'new_name': 'Vinyl Storage - Real Photos',
    },
    'Real photos - Chest of drawers (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.CHEST,
        'new_name': 'Chest of Drawers - Real Photos'
    },
    'Real photos - Shoe rack (DE) (EN) (FR) (NL) (ES)': {
        'category': FurnitureCategory.SHOERACK,
        'new_name': 'Shoe Rack - Real Photos',
    },
}

background_color_mapping = {
    'white': '1',
    'grey': '2',
    'transparent': '3',
}
watty = ContentType.objects.get(model='watty')
jetty = ContentType.objects.get(model='jetty')

new_feed_item_ids = []
new_feed_images_ids = []


def get_images_to_import(images: QuerySet[FeedItemImage]) -> list[FeedItemImage]:
    result = []
    bad_names = {'front', 'left', 'right'}
    for feed_image_candidate in images:
        for bad_name in bad_names:
            if bad_name in feed_image_candidate.image.name:
                break
        result.append(feed_image_candidate)
    return result


for old_category_name, new_category_data in old_to_new_categories_mapping.items():
    all_images = FeedItemImage.objects.filter(
        item__category__name=old_category_name
    )
    images_to_import = get_images_to_import(all_images)
    new_category_category = new_category_data['category']
    new_category_name = new_category_data['new_name']
    new_category = NewFeedCategory.objects.create(
        name=new_category_name,
        furniture_category=new_category_category,
    )
    for feed_item_image in images_to_import:
        object_id = feed_item_image.item.furniture_in_category.furniture_id
        content_type = watty if new_category_category == FurnitureCategory.WARDROBE \
            else jetty
        feed_item = NewFeedItem.objects.create(
            category=new_category,
            object_id=object_id,
            content_type=content_type,
        )
        new_feed_item_ids.append(feed_item.id)

        old_config = feed_item_image.config

        feed_image = FeedImage.objects.create(
            config=ImageConfigOption.REAL_PHOTO,
            image=feed_item_image.image,
        )
        feed_image.items.add(feed_item)
        new_feed_images_ids.append(feed_image.id)
