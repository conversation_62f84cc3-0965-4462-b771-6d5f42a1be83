import csv
import datetime

from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from product_feeds.choices import UGCTypes
from product_feeds.models import OlapicFeedItem
from product_feeds.utils import get_id_from_string


def _get_date(string_date):
    if not string_date:
        return None
    year, month = string_date.split('.')
    date = datetime.datetime(year=int(year), month=int(month), day=1)
    return date


def create_olapic_feeds_from_csv(file_csv):
    """Creates OlapicFeedItems from data from csv_file
    """
    with open(file_csv, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            product_url = row['ID LINK'].partition('?sharing=true')[0]
            description = row['DESCRIPTION'].replace('\n', ' ')
            user_nickname = row['NICK']
            date_from_csv = row['DATE']
            posted_at = _get_date(date_from_csv)
            instagram_link = row['IG POST']
            google_drive_link = row['DRIVE LINK']
            white_list = row['WHITE LIST '] == 'yes'
            ugc_type = None
            ugc_t = row['UGC TYPE']
            if ugc_t == 'collab':
                ugc_type = UGCTypes.COLLAB
            elif ugc_t == 'ugc':
                ugc_type = UGCTypes.UGC
            try:
                id_from_url = get_id_from_string(product_url)
            except IndexError:
                print(f'This item has not been created: product url: {product_url},'
                      f'user nickname: {user_nickname}, '
                      f'instagram link: {instagram_link}')
                continue
            furniture_model = Jetty if 'wardrobe' not in product_url else Watty
            try:
                furniture = furniture_model.objects.get(pk=id_from_url)
            except furniture_model.DoesNotExist:
                print(
                    f'The furniture of type {furniture_model} with id {id_from_url} '
                    f'does not exist.')
                continue

            OlapicFeedItem.objects.create(
                user_nickname=user_nickname,
                posted_at=posted_at,
                instagram_link=instagram_link,
                google_drive_link=google_drive_link,
                white_list=white_list,
                furniture=furniture,
                description=description,
                ugc_type=ugc_type,
            )


csv_file = '/home/<USER>/plik.csv'
create_olapic_feeds_from_csv(csv_file)
