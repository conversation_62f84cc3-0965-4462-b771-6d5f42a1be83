"""Order for emails of customers, who have on their wishlist or in their
cart a furniture with one of the following features (and after 10.08.2021):
- high doors (over 382 mm)
- legs/plinth
- insert
- cable management
"""


import datetime

from custom.utils.exports import dump_list_as_txt
from gallery.enums import FurnitureStatusEnum
from gallery.models import Jetty


start_date = datetime.datetime(2021, 8, 10)


def _jetty_has_high_doors(jetty):
    for door in jetty.doors:
        height = door.get('height')
        if height and height > 382:
            return True
    return False


def _get_email(user):
    if user.profile and user.profile.email:
        return user.profile.email
    return user.email


def get_requested_emails(date, status):
    qs = Jetty.objects.filter(created_at__gt=date, furniture_status=status)\
        .select_related('owner', 'owner__profile')
    emails = set()

    for jetty in qs:
        if jetty.legs or jetty.plinth or jetty.inserts or jetty.cable_management:
            email = _get_email(jetty.owner)
            if email:
                emails.add(email)
                continue
        if jetty.doors and _jetty_has_high_doors(jetty):
            email = _get_email(jetty.owner)
            if email:
                emails.add(email)
    return emails


cart_emails = get_requested_emails(start_date, FurnitureStatusEnum.DRAFT.value)
wishlist_emails = get_requested_emails(start_date, FurnitureStatusEnum.SAVED.value)

# In case a client has a shelf both in the cart and on the wishlist he is supposed
# to be on the cart list
wishlist_emails = wishlist_emails - cart_emails

dump_list_as_txt(
    list(cart_emails),
    output='emails_from_carts.txt',
    mail=('<EMAIL>',),
    mail_body='Emails for discounts from carts',
    mail_subject='Emails from carts',
)

dump_list_as_txt(
    list(wishlist_emails),
    output='emails_from_wishlist.txt',
    mail=('<EMAIL>',),
    mail_body='Emails for discounts from wishlist',
    mail_subject='Emails from wishlist',
)
