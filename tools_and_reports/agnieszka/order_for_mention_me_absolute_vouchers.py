""" Order for absolute vouchers for Mention Me, excluding wardrobes and sample boxes
"""

from datetime import datetime
from decimal import Decimal

from django.contrib.auth import get_user_model

from custom.utils.exports import dump_list_as_txt
from regions.models import Region
from vouchers.models import (
    Voucher,
    VoucherRegionEntry,
)


User = get_user_model()


def exclude_wardrobes_and_samples(voucher):
    voucher.item_conditionals = {
        'exclude': [
            {'shelf_types': [3]},
            {'furniture_types': ['sample_box']},
        ]
    }
    voucher.save()


def update_region_entries(voucher):
    voucher.region_entries.all().delete()
    region_entries = []
    voucher_values = {
        'CHF': (225, 1200),
        'EUR': (150, 800),
        'GBP': (130, 700),
        'NOK': (1800, 10000),
        'PLN': (650, 3500),
    }
    for region in Region.objects.select_related('currency').all():
        value, amount_starts = voucher_values.get(
            region.currency.code, (False, False)
        )
        if value:
            region_entries.append(
                VoucherRegionEntry(
                    voucher_id=voucher.id,
                    region=region,
                    value=Decimal(value),
                    amount_starts=Decimal(amount_starts),
                    amount_limit=Decimal(1000000),
                )
            )
    VoucherRegionEntry.objects.bulk_create(region_entries)


codes = []
for i in range(5000):
    code = Voucher.generate_code(
        inside_string='mtn',
        inside_string_at_beginning=True
    )
    voucher = Voucher.objects.create(
        origin=7,                                            # Origin Referral
        kind_of=VoucherType.ABSOLUTE,
        creator=User.objects.get(username='admin'),
        code=code,
        value=Decimal(150),
        quantity=1,
        amount_starts=Decimal(800),
        quantity_left=1,
        amount_limit=Decimal(1000000),
        start_date=datetime.today(),
        end_date=datetime(2023, 12, 31),
    )
    exclude_wardrobes_and_samples(voucher)
    update_region_entries(voucher)
    codes.append(code)


dump_list_as_txt(
    codes,
    output='codes_for_mention_me.txt',
    mail=('<EMAIL>',),
    mail_body='Codes for Mention Me vouchers',
    mail_subject='Codes for Mention Me vouchers',
)
