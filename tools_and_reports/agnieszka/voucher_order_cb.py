from custom.utils.exports import dump_list_as_txt
from tools_and_reports.voucher_orders.helpers import *
from vouchers.enums import VoucherOrigin, VoucherType

# Creating new 500 vouchers for Corporate Benefits with prefix 'cbma'
codes = []
vouchers = []
for i in range(500):
    voucher = create_voucher(
        value=25,
        kind_of=VoucherType.PERCENTAGE,
        start_date=datetime(2022, 3, 1),
        end_date=datetime(2022, 3, 31),
        origin=VoucherOrigin.CORPORATE,
        prefix='cbma',
        item_conditionals=exclude_wardrobes_and_samples(),
    )
    codes.append(voucher.code)
    vouchers.append(voucher)

Voucher.objects.bulk_create(vouchers)

dump_list_as_txt(
    codes,
    output='codes_for_corporate_vouchers.txt',
    mail=('<EMAIL>',),
    mail_body='Codes for Corporate Benefits vouchers',
    mail_subject='Codes for Corporate Benefits vouchers',
)


# Creating new 500 vouchers for Corporate Benefits with prefix 'clma'
codes = []
vouchers = []
for i in range(500):
    voucher = create_voucher(
        value=25,
        kind_of=VoucherType.PERCENTAGE,
        start_date=datetime(2022, 3, 1),
        end_date=datetime(2022, 4, 30),
        origin=VoucherOrigin.CORPORATE,
        prefix='clma',
        item_conditionals=exclude_wardrobes_and_samples(),
    )
    codes.append(voucher.code)
    vouchers.append(voucher)

Voucher.objects.bulk_create(vouchers)

dump_list_as_txt(
    codes,
    output='codes_for_corplife.txt',
    mail=('<EMAIL>',),
    mail_body='Codes for Corplife vouchers',
    mail_subject='Codes for Corplife vouchers',
)
