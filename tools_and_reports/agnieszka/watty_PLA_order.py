"""Marketing PLA order for ids of watties.
"""


from custom.utils.exports import dump_list_as_csv
from gallery.models import Watty


CATEGORIES = ['Single', 'Double', '3 door']
COLOURS = ['white', 'beige', 'graphite', 'beige + pink']
HEIGHTS = ['200', '237', '237+']
DEPTHS = ['53', '63']
COUNTS = ['', 45, 50, 45, 50, 45, 50, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
          45, 45, 45, 45, 45, 45, 60, 65, 60, 65, 60, 65, 50, 50, 50, 50, 50, 50, 60,
          65, 60, 65, 60, 65, 50, 50, 50, 50, 50, 50, 60, 65, 60, 65, 60, 65, 50, 50,
          50, 50, 50, 50, 60, 65, 60, 65, 60, 65, 50, 50, 50, 50, 50, 50]


categories_to_int = {'Single': 1, 'Double': 2, '3 door': 3}
colours_to_material = {'white': 0, 'beige': 1, 'graphite': 2, 'beige + pink': 3}
depths_in_mm = {'53': 530, '63': 630}


def count_top_doors(watty):
    """
    Counts top doors, which gives us information how many segments
    does the watty have: single has one top door, double has two etc.
    """
    top_doors = 0
    for door in watty.doors:
        try:
            if 'top' in door['extremes']:
                top_doors += 1
        except KeyError:
            continue
    return top_doors


def get_watty_ids(category, colour, height, depth, max_count):
    """
    Filters watties to match given conditions.

    Returns:
        list: a list of watty ids in string format
    """
    material = colours_to_material[colour]
    depth = depths_in_mm[depth]
    qs = None
    if height == '200':
        qs = Watty.objects.filter(height__lte=2000, material=material, depth=depth)
    elif height == '237':
        qs = Watty.objects.filter(
            height__gt=2000,
            height__lte=2370,
            material=material,
            depth=depth
        )
    elif height == '237+':
        qs = Watty.objects.filter(height__gt=2370, material=material, depth=depth)
    if not qs:
        return []
    watties = []
    for watty in qs:
        if count_top_doors(watty) == categories_to_int[category]:
            watties.append(watty)
        if len(watties) == max_count:
            break
    return [str(watty.id) for watty in watties]


def create_table(categories, colours, heights, depths):
    """
    Creates table in list format
    """
    rows = []
    row_count = 0
    all_ids_count = 0
    for category in categories:
        for colour in colours:
            for height in heights:
                for depth in depths:
                    row_count += 1
                    max_count = COUNTS[row_count]
                    ids = get_watty_ids(category, colour, height, depth, max_count)
                    ids_count = len(ids)
                    all_ids_count += ids_count
                    ids = ','.join(ids)
                    row = [
                        row_count,
                        category,
                        colour,
                        height,
                        depth,
                        ids,
                        max_count,
                        ids_count
                    ]
                    rows.append(row)
    print(f'We included {all_ids_count} watties out of {Watty.objects.count()}')
    return rows


table = create_table(CATEGORIES, COLOURS, HEIGHTS, DEPTHS)
email = ('<EMAIL>',)


dump_list_as_csv(
    table,
    output='watty_ids.csv',
    mail=email,
    delimiter=';',
    headers=[
        'Row',
        'Category',
        'Colour',
        'Height',
        'Depth',
        'Ids',
        'Max Count',
        'Actual Count'
    ]
)
