"""Updates vouchers for designers that had been created before threshold vouchers
were deployed
"""

from datetime import <PERSON><PERSON>ta
from decimal import Decimal

from django.contrib.auth.models import User

from vouchers.enums import VoucherOrigin
from vouchers.models import (
    Voucher,
    VoucherGroup,
)


codes = ['DTPowv25', 'DTPa5giv', 'DTP0sxic', 'DTP2mnr8', 'DTPx16pv', 'DTP0b25d',
         'DTP7wo8p', 'DTP9yk96', 'DTPanrzh', 'DTP2jqqv', 'DTP63v1x', 'DTPawfhp',
         'DTPxecl4', 'DTP2f74n', 'DTPt2o70', 'DTPfgr03', 'DTPola6s', 'DTPquzm3',
         'DTPyt9wb', 'DTPtj8fp', 'DTP7jyv1', 'DTP46v1u', 'DTPzn2iw', 'DTP1maew',
         'DTPk1hum', 'DTPnpk9f', 'DTPvesxb', 'DTPbvt0b', 'DTP7cpf0', 'DTP6o608',
         'DTPl2qvk', 'DTPcvlqc', 'DTP7gk3a', 'DTPjojsb', 'DTPfxlpc', 'DTP9yifw',
         'DTPuemsj', 'DTPsdc61', 'DTPaqq4x', 'DTPf891r', 'DTPrcuw6', 'DTPinfav',
         'DTPanxdy', 'DTPwmpq4', 'DTPn9z3t', 'DTPi1tjm', 'DTP5ukbp', 'DTPj5fvf',
         'DTPxlxv0', 'DTPocnyg', 'DTP5dav4', 'DTPtghlg', 'DTP4k9dr', 'DTP44eqp',
         'DTPkm3yy', 'DTP61pab', 'DTPopr2h', 'DTPx2vj4', 'DTPdixq9', 'DTPbsmyw',
         'DTPf762c', 'DTPc3s3t', 'DTP7fhrp', 'DTPv5cke', 'DTPjr4n9']


for code in codes:
    voucher = Voucher.objects.get(code=code)
    voucher.quantity = -1
    voucher.quantity_left = 0
    voucher.save()

    creator = User.objects.get(username='admin')
    transition_date = voucher.end_date
    end_date = transition_date + timedelta(days=306)
    email = voucher.affiliate

    create_parameters = {
        'kind_of': VoucherType.PERCENTAGE,
        'quantity': -1,
        'creator': creator,
        'affiliate': email,
        'origin': VoucherOrigin.COLLABS,
    }

    group = VoucherGroup.objects.create(code=code)

    for value, amount_starts, amount_limit in (
        (20, 1, 2000),
        (25, 2000, 10000),
        (30, 10000, 1000000),
    ):
        Voucher.objects.create(
            code=Voucher.generate_code(
                inside_string='THR',
                inside_string_at_beginning=True,
                character_count=8,
            ),
            value=Decimal(value),
            start_date=transition_date,
            end_date=end_date,
            group=group,
            amount_starts=Decimal(amount_starts),
            amount_limit=Decimal(amount_limit),
            **create_parameters,
        )
