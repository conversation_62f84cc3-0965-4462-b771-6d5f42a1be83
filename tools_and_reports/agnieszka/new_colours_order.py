"""Marketing order for ids of jetties in new colours.
"""


from custom.enums import Type02Color
from custom.utils.exports import dump_list_as_csv
from gallery.enums import FurnitureCategory, ShelfPatternEnum
from gallery.models import Jetty


COLOURS = {
    'Burgundy red': Type02Color.BURGUNDY.value,     # 8
    'Cotton beige': Type02Color.COTTON.value,       # 9
    'Blue': Type02Color.SKY_BLUE.value,             # 7
}
CATEGORIES = {
    'Bookcase': FurnitureCategory.BOOKCASE.value,
    'Sideboard': FurnitureCategory.SIDEBOARD.value,
    'TV Stand': FurnitureCategory.TV_STAND.value,
    'Shoe racks': FurnitureCategory.SHOERACK.value,
    'Wall storage': FurnitureCategory.WALL_STORAGE.value,
    'Chest of drawers': FurnitureCategory.CHEST.value,
}

COUNTS = ['', 15, 24, 6, 3, 9, 3, 38, 60, 15, 8, 23, 8, 38, 60, 15, 8, 23, 8, 10, 16, 4,
          2, 6, 2, 26, 42, 11, 5, 16, 5, 66, 105, 26, 13, 39, 13, 66, 105, 26, 13, 39,
          13, 18, 28, 7, 4, 11, 4, 11, 18, 5, 2, 7, 2, 28, 45, 11, 6, 17, 6, 28, 45, 11,
          6, 17, 6, 8, 12, 3, 2, 5, 2]


def get_jetty_ids(colour, pattern, category, max_count):
    """
    Filters jetties to match given conditions.

    Returns:
        list: a list of jetty ids in string format
    """
    jetty_ids = []
    qs = Jetty.objects.filter(
        material=COLOURS[colour],
        pattern=ShelfPatternEnum.get_value_by_label(pattern),
        shelf_type=1
    )
    for jetty in qs.iterator():
        if jetty.furniture_category == CATEGORIES[category]:
            jetty_ids.append(str(jetty.id))
            max_count -= 1
        if max_count == 0:
            break
    return jetty_ids


def create_table(colours, patterns, categories):
    """
    Creates table in list format
    """
    rows = []
    row_count = 0

    for colour in colours:
        for pattern in patterns:
            for category in categories:
                row_count += 1
                max_count = COUNTS[row_count]
                ids = get_jetty_ids(colour, pattern, category, max_count)
                ids_count = len(ids)
                ids = ','.join(ids)
                row = [
                    row_count,
                    colour,
                    pattern,
                    category,
                    ids,
                    max_count,
                    ids_count
                ]
                rows.append(row)
    return rows


table = create_table(COLOURS.keys(), [item.label for item in ShelfPatternEnum], CATEGORIES.keys())
email = ('<EMAIL>',)


dump_list_as_csv(
    table,
    output='jetty_ids.csv',
    mail=email,
    delimiter=';',
    headers=[
        'Row',
        'Colour',
        'Pattern',
        'Category',
        'Ids',
        'Max Count',
        'Actual Count'
    ]
)


# For local tests
# for row in table:
#     print(row[1], row[2], row[5], row[6])
#
# dump_list_as_csv(
#     table,
#     open('cstm/tools_and_reports/agnieszka/jetty_ids.csv', 'w'),
#     delimiter=';',
#     headers=['Row', 'Colour', 'Pattern', 'Category', 'Ids', 'Max Count', 'Actual Count']
# )
#
#
# def print_jetty_values(jetty_pk):
#     j = Jetty.objects.get(pk=jetty_pk)
#     print(f'Color: {j.material}, pattern: {j.get_pattern_name()}, '
#           f'type: {j.shelf_type}, '
#           f'category: {j.furniture_category.name}')
#
#
# jetty_ids = 'pasted list of ids'
#
# for jetty_id in jetty_ids:
#     print_jetty_values(jetty_id)
