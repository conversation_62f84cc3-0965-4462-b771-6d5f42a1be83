from producers.models import ProductBatchComplaint

pbc_ids = list(range(15950, 15957))
pbc = ProductBatchComplaint.objects.filter(id__in=pbc_ids)

for batch in pbc:
    for product in batch.batch_items.all():
        complaint = product.reproduction_complaints.first()
        print(
            f'batch {batch.id} '
            f'manufactor {batch.manufactor} '
            f'complaint {complaint.id} '
            f'man fault {complaint.manufactor_fault}'
        )
