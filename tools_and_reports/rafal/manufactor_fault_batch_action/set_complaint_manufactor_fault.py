from producers.models import ProductBatchComplaint

pbc_ids = list(range(15950, 15957))
pbc = ProductBatchComplaint.objects.filter(id__in=pbc_ids)

for batch in pbc:
    for product in batch.batch_items.all():
        complaint = product.reproduction_complaints.first()
        print(f'batch {batch.id} complaint {complaint.id}')
        complaint.manufactor_fault = True
        complaint.save()


"""
Batch 15954 ma dwa complainty
- 13288 - ustawiamy man_fault=False
- 13236 - ustawiamy man_fault=True
"""
