from logistic.enums import DeliveryTimeFrameProposalStatus
from logistic.models import CmrDocument
from logistic.tasks import send_dtf_confirmation_mail

# Change this before apply
CMR_DOCUMENT_ID = None

cmr_document = CmrDocument.objects.get(id=CMR_DOCUMENT_ID)

for cmr_unload_place in cmr_document.cmrdocumentunloadplace_set.all():
    print(f'CMR Unload Place: {cmr_unload_place.id}')
    logistic_order = cmr_unload_place.logistic_model
    if logistic_order:
        print(f'Logistic Order: {logistic_order.id}')
        dtf = logistic_order.dtf_proposals.filter(
            status=DeliveryTimeFrameProposalStatus.FINISHED,
            is_confirmed=True,
        ).last()
        if dtf:
            print(f'DTF: {dtf.id}')
            send_dtf_confirmation_mail.delay(dtf.id)
            print(f'Send!')
