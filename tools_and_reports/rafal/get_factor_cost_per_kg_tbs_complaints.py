from logistic.models import LogisticOrder
from tqdm import tqdm


def get_factor():
    """
    Count factor that is avarage of weight/total price for Logistic Order
    It will be once counted and can be changed after about 1year or even more
    Factor value: 10.655478416952644
    """
    logistic_orders = LogisticOrder.objects.filter(
        order__total_price__gt=0,
    )
    avarage_list = []
    for logistic_order in tqdm(logistic_orders):
        try:
            weight = logistic_order.total_brutto_weight()
            order_value = logistic_order.get_order_value_euro()

            if weight != 0 and order_value != 0:
                weight = float(weight)
                order_value = float(order_value)
                avarage_list.append(
                    order_value/weight
                )
        except Exception:
            pass
    print(f'Items amount: {len(avarage_list)}')
    return sum(avarage_list)/len(avarage_list)
