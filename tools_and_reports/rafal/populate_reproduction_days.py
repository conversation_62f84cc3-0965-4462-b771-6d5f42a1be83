from producers.models import ProductComplaint


def populate_reproduction_days():
    products_complaint = ProductComplaint.objects.all()
    products_to_update = []
    for product in products_complaint:
        if product.details and product.details.cached_serialization:
            fittings_only = False
            complaint = product.reproduction_complaints.first()
            if complaint and complaint.is_fittings_only:
                fittings_only = True

            product.reproduction_time_in_days = (
                ProductComplaint.get_reproduction_time_calculated(
                    product.details.cached_serialization, fittings_only=fittings_only
                )
            )
            products_to_update.append(product)
    ProductComplaint.objects.bulk_update(
        products_to_update, fields=['reproduction_time_in_days']
    )
