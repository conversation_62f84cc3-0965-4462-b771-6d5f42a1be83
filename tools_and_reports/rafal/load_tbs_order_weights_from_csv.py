import csv
import os

from django.db import IntegrityError

from logistic.models import ToBeShippedOrderWeight
from producers.models import Manufactor
from regions.models import Country


def load_data():
    print(os.getcwd())
    with open('initial_data.csv') as file:
        csv_reader = csv.reader(file, delimiter=',')
        line_count = 0
        for row in csv_reader:
            if line_count == 0:
                print(f'Column names are {", ".join(row)}')
                line_count += 1
            else:
                print(
                    f'Producer {row[0]} country {row[1]} weight {row[3]} preffix {row[2]}'
                )
                producer_name = row[0]
                country_name = row[1]
                prefix = row[2] or ''
                weight = row[3]
                line_count += 1
                try:
                    producer = Manufactor.objects.get(name__iexact=producer_name)
                    country = Country.objects.get(name__iexact=country_name)
                except Manufactor.DoesNotExist:
                    print(f'Producer does not exists for line: {line_count}')
                    continue
                except Country.DoesNotExist:
                    print(f'Country does not exists for line: {line_count}')
                    continue

                if not weight:
                    print(f'Weight not provided for line: {line_count}')
                    continue

                try:
                    ToBeShippedOrderWeight.objects.create(
                        producer=producer,
                        country=country,
                        weight=weight,
                        postal_code_prefix=prefix
                    )
                except IntegrityError as exc:
                    print(f"Probably duplicate result. Exception detail: {exc}")
                    continue
        print(f'Processed {line_count} lines.')
