import csv
from collections import OrderedDict

from invoice.models import Invoice


def get_invoice_pretty_ids_from_file():
    with open('naliczenia.csv') as csv_file:
        csv_reader = csv.reader(csv_file, delimiter=',')
        ids = []
        for row in csv_reader:
            ids.append(row[2])
    return ids


def convert_pretty_ids_to_tracking_numbers(invoice_pretty_ids):
    pretty_id_to_tracking_number = OrderedDict()

    for invoice_pretty_id in invoice_pretty_ids:
        if not invoice_pretty_id:  # skipping ex. headers which have no value
            continue

        invoices = Invoice.objects.filter(pretty_id=invoice_pretty_id)

        if not invoices:
            pretty_id_to_tracking_number[invoice_pretty_id] = ''
            continue

        invoices_tracking_number = invoices.values_list(
            'order__logistic_info__tracking_number', flat=True
        )
        if not invoices_tracking_number:
            pretty_id_to_tracking_number[invoice_pretty_id] = 'No tracking numbers'
            continue

        pretty_id_to_tracking_number[invoice_pretty_id] = ', '.join(
            [track_nr for track_nr in invoices_tracking_number if track_nr]
        )
    return pretty_id_to_tracking_number


def create_csv_file(pretty_id_to_tracking_number):
    with open('pretty_ids_to_tracking_numbers.csv', mode='w') as file:
        employee_writer = csv.writer(file, delimiter=',', quotechar='"',
                                     quoting=csv.QUOTE_MINIMAL)

        for pretty_id, tracking_number in pretty_id_to_tracking_number.items():
            employee_writer.writerow([pretty_id, tracking_number])


ids = get_invoice_pretty_ids_from_file()
pretty_ids_to_tracking_number = convert_pretty_ids_to_tracking_numbers(ids)
create_csv_file(pretty_ids_to_tracking_number)
