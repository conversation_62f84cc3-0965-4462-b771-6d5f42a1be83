from tqdm import tqdm

from customer_service.models import ReturningCustomer
from orders.models import Order

qs_returning_customer = ReturningCustomer.objects.all()
qs_order = Order.objects.filter(returning_client=True)


def set_default_orders_data():
    orders_data = {
        'customer_order_details': {
            'ids': [],
            'orders_count': 0,
            'assembly': [],
            'order_source': [],
            'countries': [],
        }
    }
    qs_returning_customer.update(orders_data=orders_data)


def set_order_data():
    for order in qs_order:
        returning_customer = qs_returning_customer.filter(email=order.email).first()
        if not returning_customer:
            continue

        returning_customer.add_order_data(order)


set_default_orders_data()
set_order_data()
