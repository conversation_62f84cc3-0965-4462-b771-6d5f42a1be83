import csv

from warehouse.enums import SampleBoxElementType
from warehouse.models import StockS<PERSON>Box, SampleBoxElement


def load_data_from_csv(file_path):
    with open(file_path, 'r') as file:
        csv_reader = csv.reader(
            file,
            delimiter=',',
            quoting=csv.QUOTE_ALL,
        )
        skip_lines = {0, 1}
        for i, line in enumerate(csv_reader):
            if i in skip_lines:
                continue

            stock_sample_box_pk = line[0]
            element_old_name = line[1]
            element_display_name = line[2]

            try:
                stock_sample_box_pk = int(stock_sample_box_pk)
                try:
                    stock_sample_box = StockSampleBox.objects.get(
                        pk=stock_sample_box_pk,
                    )
                    sample_box_element = stock_sample_box.type_resource
                    update_fields = ['display_name']
                    if element_display_name == '-':
                        sample_box_element.display_name = element_old_name
                        sample_box_element.is_active = False
                        update_fields.append('is_active')
                    else:
                        sample_box_element = stock_sample_box.type_resource
                        sample_box_element.display_name = element_display_name
                    sample_box_element.save(update_fields=update_fields)
                except StockSampleBox.DoesNotExist:
                    continue

            except ValueError:
                if stock_sample_box_pk == 'NEW':
                    SampleBoxElement.objects.get_or_create(
                        name=element_display_name,
                        display_name=element_display_name,
                        element_type=SampleBoxElementType.OTHERS.value,
                    )
