import datetime
from logistic.models import <PERSON>gis<PERSON><PERSON>rder
from gallery.models import Jetty
from orders.enums import OrderStatus
from orders.models import Order, OrderItem
from pricing_v3.services.price_calculators import OrderPriceCalculator
from reviews.models import Review
from tqdm import tqdm
from django.utils import timezone



"""Sample functions used for update orders with new prices"""

all_carts = Order.objects.filter(status=OrderStatus.CART, total_price__gt=0
                                 , updated_at__gte='2019-01-01', updated_at__lte='2020-01-01').order_by('-id')

with tqdm(total=all_carts.count()) as pbar:
    for o in all_carts:
        pbar.update(1)
        aa = OrderPriceCalculator(o).calculate()

all_carts = Order.objects.filter(status=OrderStatus.CART, total_price__gt=0, assembly=True).order_by('-id')

with tqdm(total=all_carts.count()) as pbar:
    for o in all_carts:
        pbar.update(1)
        o.assembly = False
        OrderPriceCalculator(o).calculate()


jetty_ids = Jetty.objects.filter(width__gte=295, shelf_type=1).values_list('id', flat=True)
orders_ids = OrderItem.objects.filter(object_id__in=jetty_ids, order__status=9).values_list('order__id', flat=True).distinct()
orders = Order.objects.filter(id__in=orders_ids, updated_at__gte='2020-01-01').order_by('-id')

with tqdm(total=orders.count()) as pbar:
    for o in orders:
        pbar.update(1)
        OrderPriceCalculator(o).calculate()


start = datetime.datetime(year=2020, month=3, day=24)
stop = datetime.datetime(year=2020, month=4, day=21)

orders_delivered = LogisticOrder.objects.filter(order__status=OrderStatus.DELIVERED,
                                                delivered_date__gt=start,
                                                delivered_date__lte=stop,
                                                undelivered=False,
                                                order__email__isnull=False)
objects_filtered_noreviews = []
for of in orders_delivered:
    order = of.order
    if not Review.objects.filter(email=order.email, created_at__gt=start).exists():
        objects_filtered_noreviews.append(of)

objects_filtered_noreviews = []
for of in orders_delivered:
    order = of.order
    if not Review.objects.filter(email=order.email).exists():
        objects_filtered_noreviews.append(of)
