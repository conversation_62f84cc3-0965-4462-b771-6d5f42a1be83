from django.core.paginator import Paginator
from tqdm import tqdm
from django.db.models import Q

from customer_service.models import CSOrder
from orders.models import Order


def update_cs_orders():
    cs_orders = CSOrder.objects.filter(
        Q(owner_email='') | Q(promo_text='') | Q(promo_text__isnull=True),
    )
    for page in tqdm(Paginator(cs_orders, 1000)):
        for cs_order in page.object_list:
            order = Order.objects.get(pk=cs_order.id)
            cs_order.owner_email = order.owner.email or ''
            cs_order.promo_text = order.promo_text or ''
            cs_order.save(update_fields=['owner_email', 'promo_text'])