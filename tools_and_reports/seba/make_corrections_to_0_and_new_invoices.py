from invoice.choices import InvoiceStatus
from invoice.models import Invoice
from datetime import datetime
from django.utils import timezone
from invoice.enums import InvoiceItemTag

start_date = datetime.strptime('2022-08-16 11:07', '%Y-%m-%d %H:%M')
end_date = datetime.strptime('2022-08-18 10:27', '%Y-%m-%d %H:%M')


class InvoiceCorrector:
    def __init__(self, orders):
        self.orders = orders

    def make_corrections_to_zero(self):
        corrections_ids = []
        for order in self.orders:
            # get last normal or correcting invoice in between start and end date
            invoice = order.invoice_set.filter(
                created_at__gte=start_date,
                created_at__lte=end_date,
                status__in=[InvoiceStatus.ENABLED, InvoiceStatus.CORRECTING],
            ).last()
            # create correction invoice
            correction = Invoice()
            correction.issued_at = timezone.now()
            correction.sell_at = invoice.sell_at
            correction.status = InvoiceStatus.CORRECTING
            if invoice.corrected_invoice is not None:
                correction.corrected_invoice = invoice.corrected_invoice
            else:
                correction.corrected_invoice = invoice
            correction.order = invoice.order
            correction.exported_to_symfonia = None
            correction.exported_to_symfonia_f = None
            correction.currency_symbol = invoice.currency_symbol
            correction.save()

            # set invoice items to 0, dont change vat
            for item in invoice.invoice_items.all():
                old_pk = item.pk
                item.pk = None
                item.net_value = 0
                item.discount_value = 0
                item.net_price = 0
                item.gross_price = 0
                item.vat_status = 0
                # item.vat_rate = 0.23
                item.vat_amount = 0.0
                item.net_weight = 0
                item.gross_weight = 0

                item.gross_price = item.net_value
                item.invoice_id = correction.id
                item.corrected_invoice_item_id = old_pk
                item.save()
            correction.save()

            # FINALIZE CORRECTION:
            # _add_tag_to_invoice_item

            item_tag = InvoiceItemTag.CANCELLATION.value

            for invoice_item in correction.invoice_items.all():
                invoice_item.tag = item_tag
                invoice_item.save()

            correction.corrected_notes = (
                'Wrong company name/Niewłaściwa nazwa firmy' # for neutralization its 'Order cancelled / Zamówienie anulowane', TODO: should we change it?
            )
            correction.save(update_fields=['corrected_notes'])
            correction.create_pdf()
            corrections_ids.append(correction.id)
        print(corrections_ids)
        print('Wygenerowano %s korekt' % len(corrections_ids))

    def create_new_invoices(self):
        new_invoices_ids = []
        for order in self.orders:
            if order.is_klarna_payment():
                print('Klarna payment! id=%s' % order.id)

            invoice = Invoice.objects.create(
                order=order,
                sell_at=order.paid_at,
                corrected_sell_at=order.paid_at,
                issued_at=order.paid_at,
                currency_symbol=order.get_region().currency.symbol,
            )

            invoice.create_pdf()
            new_invoices_ids.append(invoice.id)
        print(new_invoices_ids)
        print('Wygenerowano %s nowych faktur' % len(new_invoices_ids))


invoices_to_change = Invoice.objects.filter(
    created_at__gte=start_date,
    created_at__lte=end_date,
    status__in=[InvoiceStatus.ENABLED, InvoiceStatus.CORRECTING],
)
# get matched orders without duplications
orders = {x.order for x in invoices_to_change}

corrector = InvoiceCorrector(orders)
# create corrections to zero
corrector.make_corrections_to_zero()
# create new invoices with good company name
corrector.create_new_invoices()
