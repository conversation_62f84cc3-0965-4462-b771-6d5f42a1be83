
from logistic.models import LogisticOrder


logistic_orders_to_fix = LogisticOrder.objects.filter(
    carrier='UPS',
    sent_to_customer__isnull=False,
    delivered_date__isnull=True,
    undelivered=False,
)

for lo in logistic_orders_to_fix:
    if type(lo.tracking_numbers) == 'str' and len(lo.tracking_numbers) > 0:
        lo.tracking_numbers = lo.tracking_numbers.split(' ')
        lo.save(update_fields=['tracking_numbers'])

