from customer_service.models import KlarnaAdjustment
from invoice.models import Invoice


def print_refunds_via_correction_invoices() -> None:
    invoices = Invoice.objects.filter(
        created_at__year=2024,
        created_at__month=12,
        invoice_items__tag=3,
    )
    ids = []
    for invoice in invoices:
        if invoice.id in ids:
            continue

        ids.append(invoice.id)
        klarna_adjustment = KlarnaAdjustment.objects.filter(
            invoice__order=invoice.order
        )

        price = invoice.corrected_invoice.cached_to_dict_decoded['total_value'] - invoice.cached_to_dict_decoded['total_value']
        klarna_reason = klarna_adjustment.first().reason if klarna_adjustment.first() else ''
        klarna_price = klarna_adjustment.first().amount if klarna_adjustment.first() else ''

        print(
            f'{invoice.order.id},{invoice.id},{invoice.corrected_notes},{klarna_reason},{price},{klarna_price},{invoice.currency_symbol},{invoice.created_at.strftime("%Y-%m-%d")}'
        )

