from complaints.models import Complaint
from customer_service.models import CSCorrectionRequest
from free_returns.models import FreeReturn
from invoice.choices import InvoiceItemType
from invoice.models import Invoice


def get_return_price(fr, order_id):
    notes = 'Order returned / Zwrot zamówienia'
    last_free_return_correction = Invoice.objects.filter(
        corrected_notes=notes,
        order__id=order_id,
        status=4,
    ).last()
    if last_free_return_correction:
        last_invoice = last_free_return_correction.previous_correction if last_free_return_correction.previous_correction else last_free_return_correction.corrected_invoice
    else:
        last_invoice = Invoice.objects.filter(
            order__id=order_id,
            status__in=[0, 4],
        ).last()
    if not last_invoice:
        return ('NO INVOICE', 'NO INVOICE')

    free_return_items = fr.orderitem_set.all()

    deleted_invoice_items = last_invoice.invoice_items.filter(
        order_item_id__in=free_return_items,
        item_type=InvoiceItemType.ITEM,
    )

    gross_price_sum = 0
    for item in deleted_invoice_items:
        gross_price_sum += item.gross_price

    return gross_price_sum, last_invoice.currency_symbol


def print_complaints():
    complaints = Complaint.objects.filter(
        free_return_prevention=True,
        reported_date__gte='2024-12-01',
        reported_date__lte='2024-12-31',
    )

    for complaint in complaints:
        costs = complaint.complaint_costs
        print(
            f'{complaint.id};{complaint.reporter};{complaint.reported_date};{costs.refund_amount};{costs.currency};{complaint.refund_reason}')


def print_free_returns():
    free_returns = FreeReturn.objects.filter(
        created_at__gte='2024-12-01',
        created_at__lte='2024-12-31',
    )

    for free_return in free_returns:
        order_ids = free_return.get_order().split('<br/>')
        reason = free_return.reason.replace('\r\n', ' ')
        for order_id in order_ids:
            cs_correction_request = CSCorrectionRequest.objects.filter(invoice__order=order_id,
                                                                       tag=5).distinct()
            if cs_correction_request.count() != 1:
                return_price, currency = get_return_price(free_return, order_id)
            else:
                cs_correction_request = cs_correction_request.first()
                return_price, currency = get_return_price(free_return, order_id)
                if not return_price == cs_correction_request.correction_amount_gross:
                    return_price = cs_correction_request.correction_amount_gross
            print(
                f'{free_return.id}<><>{order_id}<><>{free_return.created_at}<><>{return_price}<><>{currency}<><>{reason}<><>{free_return.reason_tag}<><>{free_return.finished_at}')
