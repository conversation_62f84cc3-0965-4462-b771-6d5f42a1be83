
# SCRIPT TO GET DATA FROM PREVIOUS IMPLEMENTATION BEFOR REFACTOR

# from custom.enums.enums import SampleBoxVariant
# from warehouse.models import SampleBoxElement
#
# sample_box_mapping = []
# for sample_box_variant, name in SampleBoxVariant.choices():
#     box_elements_definition = SampleBoxVariant(sample_box_variant).get_sample_definition()
#     elements = []
#     for element_material, element_color in box_elements_definition:
#         for sample_box_element in SampleBoxElement.objects.filter(
#             material_color=element_color,
#             material_type=element_material,
#         ):
#             elements.append(sample_box_element.display_name)
#     sample_box_mapping.append({
#         'box_variant': sample_box_variant,
#         'elements': elements,
#     })

# OUTPUT:
sample_box_mapping = [
    {
        'box_variant': 1,
        'elements': [
            'Type01 Black',
            'Sticker Type01 Black',
            'Type01 Grey',
            'Sticker Type01 Grey',
            'Type01 White',
            'Sticker Type01 White',
            'Type01 Ash veneer',
            'Sticker Type01 Ash veneer',
            'Type01 Oak veneer',
            'Sticker Type01 Oak veneer',
        ],
    },
    {
        'box_variant': 2,
        'elements': [
            'Type01 Yellow',
            'Sticker Type01 Yellow',
            'Type01 Classic Red',
            'Sticker Type01 Classic Red',
            'Type01 Dusty Pink',
            'Sticker Type01 Dusty Pink',
            'Type01 Ash veneer',
            'Sticker Type01 Ash veneer',
            'Type01 Oak veneer',
            'Sticker Type01 Oak veneer',
        ],
    },
    {
        'box_variant': 3,
        'elements': [
            'Type02 White',
            'Sticker Type02 White',
            'Type02 Midnight Blue',
            'Sticker Type02 Midnight Blue',
            'Type01 Ash veneer',
            'Sticker Type01 Ash veneer',
            'Type02 Terracotta',
            'Sticker Type02 Terracotta',
            'Type02 Sand + Midnight Blue',
            'Sticker Type02 Sand + Midnight Blue',
        ],
    },
    {
        'box_variant': 4,
        'elements': [
            'Type01 White',
            'Sticker Type01 White',
            'Type01 Grey',
            'Sticker Type01 Grey',
            'Type01 Black',
            'Sticker Type01 Black',
            'Type01 Dusty Pink',
            'Sticker Type01 Dusty Pink',
            'Type01 Yellow',
            'Sticker Type01 Yellow',
        ],
    },
    {
        'box_variant': 5,
        'elements': [
            'Type02 White',
            'Sticker Type02 White',
            'Type02 Midnight Blue',
            'Sticker Type02 Midnight Blue',
            'Type02 Terracotta',
            'Sticker Type02 Terracotta',
            'Type02 Sand + Midnight Blue',
            'Sticker Type02 Sand + Midnight Blue',
            'Type01 Black',
            'Sticker Type01 Black',
        ],
    },
    {
        'box_variant': 6,
        'elements': [
            'Type01 Yellow',
            'Sticker Type01 Yellow',
            'Type01 Classic Red',
            'Sticker Type01 Classic Red',
            'Type01 Dusty Pink',
            'Sticker Type01 Dusty Pink',
            'Type02 Terracotta',
            'Sticker Type02 Terracotta',
            'Type02 Midnight Blue',
            'Sticker Type02 Midnight Blue',
        ],
    },
    {
        'box_variant': 7,
        'elements': [
            'Type02 White',
            'Sticker Type02 White',
            'Type02 Midnight Blue',
            'Sticker Type02 Midnight Blue',
            'Type02 Terracotta',
            'Sticker Type02 Terracotta',
            'Type02 Sand + Midnight Blue',
            'Sticker Type02 Sand + Midnight Blue',
            'Type01 Classic Red',
            'Sticker Type01 Classic Red',
        ],
    },
    {
        'box_variant': 8,
        'elements': [
            'Type02 White',
            'Sticker Type02 White',
            'Type02 Midnight Blue',
            'Sticker Type02 Midnight Blue',
            'Type02 Terracotta',
            'Sticker Type02 Terracotta',
            'Type02 Sand + Midnight Blue',
            'Sticker Type02 Sand + Midnight Blue',
            'Type02 Matte Black',
            'Sticker Type02 Matte Black',
        ],
    },
    {
        'box_variant': 101,
        'elements': [
            'Type01 Black',
            'Sticker Type01 Black',
            'Type01 Classic Red',
            'Sticker Type01 Classic Red',
            'Type01 Yellow',
            'Sticker Type01 Yellow',
            'Type01 Dusty Pink',
            'Sticker Type01 Dusty Pink',
        ],
    },
    {
        'box_variant': 102,
        'elements': [
            'Type01 Oak veneer',
            'Sticker Type01 Oak veneer',
            'Type01 Ash veneer',
            'Sticker Type01 Ash veneer',
            'Type01 Grey',
            'Sticker Type01 Grey',
            'Type01 White',
            'Sticker Type01 White',
        ],
    },
    {
        'box_variant': 103,
        'elements': [
            'Type02 Cotton Beige',
            'Sticker Type02 Cotton Beige',
            'Type02 Sky Blue',
            'Sticker Type02 Sky Blue',
            'Type02 Burgundy Red',
            'Sticker Type02 Burgundy Red',
            'Type02 Matte Black',
            'Sticker Type02 Matte Black',
        ],
    },
    {
        'box_variant': 104,
        'elements': [
            'Type02 White',
            'Sticker Type02 White',
            'Type02 Midnight Blue',
            'Sticker Type02 Midnight Blue',
            'Type02 Sand + Midnight Blue',
            'Sticker Type02 Sand + Midnight Blue',
            'Type02 Terracotta',
            'Sticker Type02 Terracotta',
        ],
    },
    {
        'box_variant': 105,
        'elements': [
            'Type02 Grey',
            'Sticker Type02 Grey',
            'Type02 Grey + Dark Grey',
            'Sticker Type02 Grey + Dark Grey',
            'Type02 Sand + Mustard Yellow',
            'Sticker Type02 Sand + Mustard Yellow',
            'Type02 Sand + Midnight Blue',
            'Sticker Type02 Sand + Midnight Blue',
        ],
    },
    {
        'box_variant': 106,
        'elements': [
            'Type02 Forest Green',
            'Sticker Type02 Forest Green',
            'Type02 Lilac',
            'Sticker Type02 Lilac',
            'Type02 Cotton Beige',
            'Sticker Type02 Cotton Beige',
            'Type02 Grey',
            'Sticker Type02 Grey',
        ],
    },
    {
        'box_variant': 1001,
        'elements': [
            'Type03 WHITE Matte 12',
            'Sticker Type03 WHITE Matte 12',
            'Type03 WHITE aluminum',
            'Sticker Type03 WHITE aluminum',
            'Type03 WHITE Standard 12',
            'Sticker Type03 WHITE Standard 12',
            'Type03 WHITE Standard 18',
            'Sticker Type03 WHITE Standard 18',
        ],
    },
    {
        'box_variant': 1002,
        'elements': [
            'Type03 CASHMERE BEIGE Matte 12',
            'Sticker Type03 CASHMERE BEIGE Matte 12',
            'Type03 CASHMERE BEIGE aluminum',
            'Sticker Type03 CASHMERE BEIGE aluminum',
            'Type03 CASHMERE BEIGE Standard 12',
            'Sticker Type03 CASHMERE BEIGE Standard 12',
            'Type03 CASHMERE BEIGE Standard 18',
            'Sticker Type03 CASHMERE BEIGE Standard 18',
        ],
    },
    {
        'box_variant': 1004,
        'elements': [
            'Type03 GRAPHITE GREY Matte 12',
            'Sticker Type03 GRAPHITE GREY Matte 12',
            'Type03 GRAPHITE GREY aluminum',
            'Sticker Type03 GRAPHITE GREY aluminum',
            'Type03 GRAPHITE GREY Standard 12',
            'Sticker Type03 GRAPHITE GREY Standard 12',
            'Type03 GRAPHITE GREY Standard 18',
            'Sticker Type03 GRAPHITE GREY Standard 18',
        ],
    },
    # CUSTOM VALUES:
    {
        'box_variant': 1003,
        'elements': [
            'Type03 Antique Pink Standard 18',
            'Type03 Antique Pink Standard 12',
            'Type03 Cashmere Beige Matte 12',
            'Type03 Cashmere Beige aluminum',
            'Type03 Antique Pink aluminum',
            'Sticker Type03 Antique Pink Standard 18',
            'Sticker Type03 Antique Pink Standard 12',
            'Sticker Type03 Cashmere Beige Matte 12',
            'Sticker Type03 Cashmere Beige aluminum',
            'Sticker Type03 Antique Pink Aluminium',
        ],
    },
    {'box_variant': 1005, 'elements': [
        'Type03 Antique Pink Standard 18',
        'Type03 Antique Pink Standard 12',
        'Type03 White Matte 12',
        'Type03 Antique Pink aluminum',
        'Type03 White aluminum',
        'Sticker Type03 Antique Pink Standard 18',
        'Sticker Type03 Antique Pink Standard 12',
        'Sticker Type03 White Matte 12',
        'Sticker Type03 Antique Pink Aluminium',
        'Sticker Type03 White aluminum',
    ]},
    {'box_variant': 1006, 'elements': [
        'Type03 Antique Pink Standard 18',
        'Type03 Antique Pink Standard 12',
        'Type03 Graphite Grey Matte 12',
        'Type03 Antique Pink aluminum',
        'Type03 Graphite Grey aluminum',
        'Sticker Type03 Antique Pink Standard 18',
        'Sticker Type03 Antique Pink Standard 12',
        'Sticker Type03 Graphite Grey Matte 12',
        'Sticker Type03 Antique Pink Aluminium',
        'Sticker Type03 Graphite Grey aluminum',
    ]},
    {'box_variant': 99, 'elements': []},
]

for sample_info in sample_box_mapping:
    box_variant = SampleBoxVariant.objects.get(variant_type=sample_info['box_variant'])
    for element in sample_info['elements']:
        element_to_update = SampleBoxElement.objects.get(
            element_name__iexact=element,
            is_active=True,
        )
        element_to_update.box_variant.add(box_variant)

# ADDITIONAL_ELEMENTS
type_01_02_other_elements = [
    'Box Insert Type01/02',
    'Box Type01/02/03',
    'Inside Card Type01/02',
    'Outside Sticker Type01/02'
]
type_03_other_elements = [
    'Box Insert Type03',
    'Box Type01/02/03',
    'Inside Card Type03',
    'Outside Sticker Type03',
]
from custom.enums.enums import SampleBoxVariantEnum
from warehouse.models import SampleBoxVariant, SampleBoxElement

for box_variant in SampleBoxVariant.objects.all():
    if box_variant.is_type_03_variant:
        for element in type_03_other_elements:
            element_to_update = SampleBoxElement.objects.get(
                element_name__iexact=element,
                is_active=True,
            )
            element_to_update.box_variant.add(box_variant)
    elif box_variant.variant_type != SampleBoxVariantEnum.CUSTOM.value:
        for element in type_01_02_other_elements:
            element_to_update = SampleBoxElement.objects.get(
                element_name__iexact=element,
                is_active=True,
            )
            element_to_update.box_variant.add(box_variant)

