from logistic.models import CmrDocumentUnloadPlace,CmrDocument
from logistic import enums

for cmr_unload_place in CmrDocumentUnloadPlace.objects.all():
    last_dtf = cmr_unload_place.logistic_model.dtf_proposals.exclude(
            status=enums.DeliveryTimeFrameProposalStatus.CANCELLED.value,
        ).last()
    if last_dtf and last_dtf.confirmation_mail_time:
        cmr_unload_place.confirmation_email_sent_at = last_dtf.confirmation_mail_time
        cmr_unload_place.save(update_fields=['confirmation_email_sent_at'])


def deliveries_confirmed_old(cmr_doc):
    deliveries_confirmed_list = []
    for cmr_unload_place in cmr_doc.cmrdocumentunloadplace_set.all():
        deliveries_confirmed_list.extend(
            list(
                cmr_unload_place.logistic_model.dtf_proposals.exclude(
                    status=enums.DeliveryTimeFrameProposalStatus.CANCELLED.value,
                ).values_list('is_confirmed', flat=True)
            )
        )
    return all(deliveries_confirmed_list) if deliveries_confirmed_list else False


def deliveries_confirmed_new(cmr_doc):
    for cmr_unload_place in cmr_doc.cmrdocumentunloadplace_set.all():
        if not cmr_unload_place.confirmation_email_sent_at:
            return False
    return True


def check_deliveries_confirmed():
    for cmr_document in CmrDocument.objects.all():
        if deliveries_confirmed_old(cmr_document) != deliveries_confirmed_new(cmr_document):
            # There can be confirmed DTF but without date of confirmation sent
            print(cmr_document.id)
