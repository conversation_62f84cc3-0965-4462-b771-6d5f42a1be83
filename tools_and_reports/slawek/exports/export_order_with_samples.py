from __future__ import print_function

from django.contrib.contenttypes.models import ContentType

from gallery.models import SampleBox

from custom.utils.exports import dump_list_as_csv
from orders.models import Order

sample_box = ContentType.objects.get(model='samplebox')
orders_with_samples = Order.objects.filter(items__content_type=sample_box).exclude(status=9).distinct()

entries = []

for order in orders_with_samples:
    for item in order.items.all():
        item_entry = item.order_item
        if isinstance(item_entry, SampleBox):
            entries.append([
                order.id,
                order.paid_at,
                order.email,
                order.country,
                order.total_price,
                order.promo_amount,
                order.owner.profile.language,
                order.first_name,
                order.last_logistic_order.sent_to_customer if order.last_logistic_order else '',
                order.last_logistic_order.delivered_date if order.last_logistic_order else '',
                item.price,
                item_entry.__class__.__name__,
                item_entry.box_variant.variant_type,
                item_entry.get_variant_name()
            ])
        else:
            entries.append([
                order.id,
                order.paid_at,
                order.email,
                order.country,
                order.total_price,
                order.promo_amount,
                order.owner.profile.language,
                order.first_name,
                order.last_logistic_order.sent_to_customer if order.last_logistic_order else '',
                order.last_logistic_order.delivered_date if order.last_logistic_order else '',
                item.price,
                item_entry.__class__.__name__,
                item_entry.shelf_type,
                item_entry.material
            ])
dump_list_as_csv(entries, open('/tmp/sample_export.csv', 'w'))
