from __future__ import print_function
from gallery.models import Jetty
from tqdm import tqdm
from custom.utils.exports import dump_list_as_csv
print('raz')
dump_list_as_csv([[x.id, x.created_at, x.get_created_platform_display(), x.price, x.shelf_type] for x in Jetty.objects.filter(id__gte=500000, id__lte=650000, furniture_status__in=[Jetty.DRAFT, Jetty.ORDERED])], open('/tmp/itemy_carty_1.csv', 'w'))
print('dwa')
dump_list_as_csv([[x.id, x.created_at, x.get_created_platform_display(), x.price, x.shelf_type] for x in Jetty.objects.filter(id__gte=350000, id__lte=500000, furniture_status__in=[Jetty.DRAFT, Jetty.ORDERED])], open('/tmp/itemy_carty_2.csv', 'w'))

print('trzy')
dump_list_as_csv([[x.id, x.created_at, x.get_created_platform_display(), x.price, x.shelf_type] for x in Jetty.objects.filter(id__gte=200000, id__lte=350000, furniture_status__in=[Jetty.DRAFT, Jetty.ORDERED])], open('/tmp/itemy_carty_3.csv', 'w'))