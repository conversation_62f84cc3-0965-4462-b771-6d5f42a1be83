from custom.enums import PhysicalProductVersion
from custom.utils.exports import dump_list_as_csv
from producers.models import Product

sideboard_plus_products = Product.objects.filter(
    cached_physical_product_version__in=[PhysicalProductVersion.RAPTOR.value, PhysicalProductVersion.DIPLO.value]
)

dump_list_as_csv(
    [
        [
            x.id,
            x.order_id,
            x.order_item.order_item.id,
            x.order_item.order_item.shelf_type,
            x.order_item.order_item.configurator_type,
        ]
        for x in sideboard_plus_products
    ],
    open('/tmp/splus_janek.csv', 'w'),
)
