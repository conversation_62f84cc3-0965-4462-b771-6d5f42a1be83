from kpi.models import MarketingNonperformanceCost
from orders.models import PaidOrders
import tqdm
'''
    name = models.CharField(max_length=64)
    date_at = models.DateField(default=timezone.now)
    description = models.CharField(max_length=256, blank=True, null=True)
    cost_amount = models.DecimalField(max_digits=12, decimal_places=2)
    countries = models.CharField(choices=COUNTRIES_MARKETING, null=True, blank=True, max_length=70)
    author = models.ForeignKey(User)
'''

from custom.utils.exports import dump_list_as_csv

dump_list_as_csv([[x.name, x.date_at, x.description, x.cost_amount, x.countries, x.author] for x in MarketingNonperformanceCost.objects.all()],
                 open('/tmp/non_perfo.csv', 'w'))