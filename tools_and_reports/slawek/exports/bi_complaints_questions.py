from __future__ import print_function
from collections import Counter

from complaints.models import Complaint


all_complaints_details = [[x.id, x.get_elements_details(as_string=False)] for x in Complaint.objects.all()]

# complaints_with_elemens = filter(lambda x: len(x) > 0, [x[1] for x in all_complaints_details])
#
# print "Mamy {} complaints, z czego elementy maja {}".format(len(all_complaints_details), len(complaints_with_elemens))
#
# print "Łacznie elementów do reprodukcji: {}, z czego horizontali jest {}".format(sum([len(x) for x in complaints_with_elemens]), sum([sum([1 for y in x if y['element_type'] == 'H']) for x in complaints_with_elemens]))
# print "Procent reklamacji bez horizontali {}/{}".format(sum([1 for x in complaints_with_elemens if len([1 for z in x if z['element_type'] == "H"]) == 0]), len(complaints_with_elemens))
#
# list_of_list_of_elements = [[y['element_type'] for y in x if y['element_type'] != 'H'] for x in complaints_with_elemens]
# flat_list = [item for sublist in list_of_list_of_elements for item in sublist]
#
# print "Rózne elementy: {}".format(Counter(flat_list))
#
#
# list_of_list_of_elements = [[y['width'] for y in x if y['element_type'] == 'H'] for x in complaints_with_elemens]
# flat_list = [item for sublist in list_of_list_of_elements for item in sublist]
#
# print "Rózne długości horizontali: {}".format(Counter(flat_list))
#
# flat_list = ['2200<' if x >= 2200 else '1600-2200' if x >= 1600 else '1200-1600' if x >= 1200 else '<1200' for x in flat_list]
#
# print "Rózne długości horizontali: {}".format(Counter(flat_list))


#czy male paczki sie psuja bardzo?

all_complaints_details = [x[1] for x in all_complaints_details]
complaints_with_elemens = [x for x in all_complaints_details if len(x) > 0]
complaints_with_sure_elements = [x for x in complaints_with_elemens if all([y['only_in_one_package'] for y in x],)]
print("Wszystkich complaints gdzie sa elementy: {}, reklamacje gdzie nie ma watpliwosci gdzie są elementy, {}".format(len(complaints_with_elemens), len(complaints_with_sure_elements)))


list_of_list_of_elements = [[y['possible_packages'] for y in x if y['only_in_one_package'] is True] for x in complaints_with_sure_elements ]
flat_list = [item for sublist in list_of_list_of_elements for item in sublist]

#tutaj sa jeszcze kilka razy te same paczki - nie powinno ich byc, sprwadz 155 - jest 14 paczek, powinno być dużo mniej, max 6

unique_flatten_packages = []
for list_of_packs in flat_list:
    for pack in list_of_packs:  #tutaj po numerze paczki tylko, nie po in bo caly obiekt sie zmienia
        if {'pack_id': pack['pack_id'], 'product_id': pack['product_id']} not in [{'pack_id': x['pack_id'], 'product_id': x['product_id']} for x in unique_flatten_packages]:
            unique_flatten_packages.append(pack)

print("bylo {} paczek, po usunieciu duplikatow jest {} paczek".format(len(flat_list), len(unique_flatten_packages)))

#print Counter([x['product_id'] for x in unique_flatten_packages])



packages = [{'pack_id': x['pack_id'],
             'pack_level': x['pack_level'],
             'pack_type': x['pack_type'],
             'weight': x['weight'],
             'width': x['width'],
             'max_size': max(x['width'],x['depth'],x['height']),
             'width/weight ratio': x['weight']/float(x['width'])} for x in unique_flatten_packages]
from text_histogram import histogram

print("weight")
histogram([x['weight'] for x in packages])

print("ilosc poziomow")
histogram([x['pack_level'] for x in packages])


# print "ilosc poziomow ale same paczki z horizontalami"
# histogram([x['pack_level'] for x in packages if x['pack_type'] == 'costam'])



# print "typy paczek"
# histogram([x['pack_type'] for x in packages])

print("width")
histogram([x['width'] for x in packages])

print("wagowe mniejsze niz 500")
histogram([x['weight'] for x in packages if x['width'] <= 500])


[x for x in unique_flatten_packages if x['pack_level'] > 10]


#czy ciezsze paczki sie psuja barzdiej

