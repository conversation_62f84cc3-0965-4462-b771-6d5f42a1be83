from __future__ import print_function
from orders.models import PaidOrders

testgroup = PaidOrders.objects.filter(country='france')[:600]

testgroup = [x for x in testgroup if  x.ab_tests and 'blind' in x.ab_tests]

len(testgroup)

#so, lets split that group with 65 orders bracket
number_of_orders = len(testgroup)

i = 0

for x in range(0, number_of_orders-65):
    max_diff = 0
    responses = [x.ab_tests['blind'] for x in testgroup[x:x+65] if x.ab_tests and 'blind' in x.ab_tests]
    print('{}:{}'.format(sum([1 for y in responses if y is True]), sum([1 for y in responses if y is False])))
    if sum([1 for y in responses if y is True]) - sum([1 for y in responses if y is False]) > max_diff:
        max_diff = sum([1 for y in responses if y is True]) - sum([1 for y in responses if y is False])

print(max_diff)