from __future__ import print_function
import pandas as pd
import json

from orders.models import Order
'''
Search for orders based on first/last name and  email

'''

df = pd.read_csv('/tmp/samples.csv')
aa = [x.tolist() for x in df[[1,8]].values]


json_string = ''

entries = json.loads(json_string)

not_found = []
found = []

from tqdm import tqdm
for x in tqdm(entries):
    first_name = x[0].split(' ')[0]
    last_name = x[0].split(' ')[-1]

    orders_with_this_user = Order.objects.filter(first_name=first_name, last_name=last_name, status_paid=True).count()
    orders_with_this_email = Order.objects.filter(email=x[1], status_paid=True).count()
    if orders_with_this_email + orders_with_this_email > 0:
        found.append(x[1])
    else:
        not_found.append(x[1])

print("was {}, found {}, not found {}".format(len(entries), len(found), len(not_found)))

print(found)





