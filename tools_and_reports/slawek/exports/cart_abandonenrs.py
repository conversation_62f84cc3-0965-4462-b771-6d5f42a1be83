'''
Used to get all cart abandonners
'''

from __future__ import print_function
from custom.utils.exports import dump_list_as_csv
from orders.models import Order

from tqdm import tqdm
from datetime import datetime, timedelta

cart_brackets = {}

today = datetime.now()
cart_brackets['0-36'] = Order.objects.filter(status=9, updated_at__range=[today - timedelta(days=36), today]).exclude(email__isnull=True).exclude(email='').exclude(phone__isnull=True).exclude(phone='').exclude(total_price=0)
cart_brackets['37-67'] = Order.objects.filter(status=9, updated_at__range=[today - timedelta(days=67), today - timedelta(days=37)]).exclude(email__isnull=True).exclude(email='').exclude(phone__isnull=True).exclude(phone='').exclude(total_price=0)
cart_brackets['68-97'] = Order.objects.filter(status=9, updated_at__range=[today - timedelta(days=97), today - timedelta(days=68)]).exclude(email__isnull=True).exclude(email='').exclude(phone__isnull=True).exclude(phone='').exclude(total_price=0)

print([len(x) for x in cart_brackets.values()])

for cart_name, cart_bracket in cart_brackets.items():
    not_customers = []
    print(cart_name)
    print(len(cart_bracket))
    for cart in cart_bracket:
        orders_with_this_user = Order.objects.filter(owner=cart.owner, status_paid=True).count()
        orders_with_this_email = Order.objects.filter(email=cart.email, status_paid=True).count()
        if orders_with_this_email + orders_with_this_email == 0:
            not_customers.append(cart)
    print(len(not_customers))
    cart_brackets[cart_name] = not_customers

print([len(x) for x in cart_brackets.values()])



whole_list = []
for cart_name, cart_bracket in cart_brackets.items():
    for cart in cart_bracket:
        whole_list.append([cart.updated_at.date(), cart.email, cart.first_name, cart.last_name, cart.owner.profile.language, cart.country, cart.phone, cart_name, cart.total_price])

dump_list_as_csv(whole_list, open('/tmp/cart_abandon.csv', 'w'))

