from custom.utils.adyen import adyen_directory_lookup
from regions.models import Region

for region in Region.objects.all():
    country = region.get_country()
    currency = region.get_currency()
    if country is None:
        print('no country for {} region'.format(region))
        continue

    print('{}: {}'.format(country, adyen_directory_lookup(country_name=country.name, amount=700,
                                                          live=True, currency_code=currency.code)))


