from __future__ import print_function
from google.cloud import bigquery
import tqdm

from complaints.models import Complaint
from complaints.serializers import ComplaintSerializer
from kpi.big_query import export_serializer_to_big_query, export_list_to_big_query

#export_serializer_to_big_query('complaints', 'complaints', model=Complaint, serializer=ComplaintSerializer, write=bigquery.WriteDisposition.WRITE_TRUNCATE)

packagingList = []
failed = 0

for x in tqdm.tqdm(Product.objects.filter(id__gte=14321)):
    try:
        packagingList.append(x.get_packaging_structure_for_export())
    except Exception:
        failed += 1
packagingList = [item for sublist in packagingList for item in sublist]
export_list_to_big_query('production_info', 'packaging', packagingList, write=bigquery.WriteDisposition.WRITE_TRUNCATE)
print(failed)



'''

                
[
  {
    "mode": "NULLABLE",
    "name": "product_id",
    "type": "INTEGER"
  },
  {
    "mode": "NULLABLE",
    "name": "order_id",
    "type": "INTEGER"
  },
  {
    "mode": "NULLABLE",
    "name": "item_id",
    "type": "INTEGER"
  },
  {
    "mode": "NULLABLE",
    "name": "pack_id",
    "type": "INTEGER"
  },
  {
    "mode": "NULLABLE",
    "name": "width",
    "type": "INTEGER"
  },
  {
    "mode": "NULLABLE",
    "name": "height",
    "type": "INTEGER"
  },
  {
    "mode": "NULLABLE",
    "name": "depth",
    "type": "INTEGER"
  },
  {
    "mode": "NULLABLE",
    "name": "weight",
    "type": "FLOAT"
  },
  {
    "mode": "NULLABLE",
    "name": "pack_type",
    "type": "STRING"
  }, 
  
    {
    "mode": "NULLABLE",
    "name": "pack_level",
    "type": "INTEGER"
  }, 
  {
    "mode": "NULLABLE",
    "name": "elements",
    "type": "STRING"
  }, 
  
  {
    "mode": "NULLABLE",
    "name": "paid_at",
    "type": "DATETIME"
  }, 
  {
    "mode": "NULLABLE",
    "name": "sent_to_customer",
    "type": "DATETIME"
  }, 
  {
    "mode": "NULLABLE",
    "name": "delivered_to_customer",
    "type": "DATETIME"
  }, 
]







'''