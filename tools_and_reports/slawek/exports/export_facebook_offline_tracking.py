from orders.models import PaidOrders
from custom.utils.exports import dump_list_as_csv


headers = ['email', 'phone', 'fn', 'ln', 'zip', 'ct', 'country', 'event_name', 'event_time', 'value', 'currency']
dump_list_as_csv([[x.email,
                   x.phone,
                   x.first_name,
                   x.last_name,
                   x.postal_code,
                   x.city,
                   x.region.get_country().code if x.region and x.region.get_country() else '-',  #iso
                   'Purchase',
                   x.paid_at.isoformat() if x.paid_at else '-',
                   x.total_price,
                   x.region.currency.code if x.region and x.region.currency else '-',
                   ] for x in PaidOrders.objects.all().order_by('-id')[:10000]],
                 open('/tmp/offline.csv', 'w'), headers)