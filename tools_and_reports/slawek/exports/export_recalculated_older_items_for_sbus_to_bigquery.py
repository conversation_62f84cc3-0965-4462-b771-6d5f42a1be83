# Script to prepare cogs entries with actual serialization, actual pricing
# and actual pricing factors
import json
import logging
import os
from datetime import date, datetime

from producers.models import Product
from producers.serializers import ProductChangesBackfillBigQuerySerializer

logger = logging.getLogger('cstm')

try:
    from google.cloud import bigquery
except ImportError:
    logger.exception("Error while importing bigquery from google.cloud")

UPDATED_AT_RANGE = (date(2020, 3, 1), date(2020, 7, 1))

data = ProductChangesBackfillBigQuerySerializer(
    Product.objects.filter(updated_at__range=UPDATED_AT_RANGE), many=True
).data

client = bigquery.Client()
dataset_ref = client.dataset('production_info')
table_ref = dataset_ref.table('production_cogs')
job_config = bigquery.LoadJobConfig()
job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
job_config.write_disposition = bigquery.WriteDisposition.WRITE_APPEND

result = [json.dumps(record) for record in data]

file_name = (
    datetime.today().isoformat().replace(':', '_').replace('.', '_').replace('-', '_')
)
dir = 'biquery'
path = '{}/{}'.format(os.path.expanduser("~"), dir)
try:
    os.makedirs(path)
except OSError:
    if not os.path.isdir(path):
        raise
file_with_path = '{}/cogs_baseline_{}.json'.format(path, file_name)
with open(file_with_path, 'w') as outfile:
    outfile.write('\n'.join(result))
with open(file_with_path, 'rb') as source_file:
    job = client.load_table_from_file(
        source_file, table_ref, location='EU', job_config=job_config
    )
job.result()
