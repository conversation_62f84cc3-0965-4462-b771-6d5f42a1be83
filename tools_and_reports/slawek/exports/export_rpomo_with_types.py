from datetime import timed<PERSON><PERSON>, date
from django.db.models import Count, Q

a = PaidOrders.objects.filter(paid_at__gte="2015-01-01").exclude(used_promo__isnull=True).select_related('used_promo')

for order in a:
    order.invoice_count = order.invoice_set.filter(status=0).count()

to_correct = []
for order in a:
    to_correct.append([order.id, order.paid_at, order.total_price, order.promo_amount, order.promo_text, order.used_promo.get_kind_of_display(), order.used_promo.get_origin_display(), order.used_promo.start_date])

len(to_correct)


#

from collections import Counter
Counter([x[2] for x in to_correct])

import unicodecsv as csv

with open('/tmp/za_ursusa.csv', mode='w') as f:
    employee_writer = csv.writer(f, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
    for line in to_correct:
        employee_writer.writerow(line)
