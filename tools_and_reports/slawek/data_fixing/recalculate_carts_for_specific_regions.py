from gallery.models import Jetty
from orders.models import Order
from pricing_v3.services.price_calculators import OrderPriceCalculator
from regions.models import Region
from tqdm import tqdm
from django.utils import timezone

import json

from orders.enums import OrderStatus

#lets take all carts with ivies

# saved items

# tutaj trzeba przed wylaczyc loggera bo bedzie 2 razy wolniej z shella

all_carts = Order.objects.filter(status=OrderStatus.CART, total_price__gt=0, region=Region.objects.get(name='united_kingdom'), updated_at__gte='2019-01-01').order_by('-id')

with tqdm(total=all_carts.count()) as pbar:
    for o in all_carts:
        pbar.update(1)
        OrderPriceCalculator(o).calculate()
