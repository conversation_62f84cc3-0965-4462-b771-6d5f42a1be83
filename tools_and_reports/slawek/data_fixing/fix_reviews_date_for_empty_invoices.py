from custom.models import ExchangeRate
from invoice.models import Invoice
from decimal import Decimal, ROUND_HALF_UP
import datetime

invoice_to_change = [['18829', '2018-11-05'],
                     ['18831', '2018-11-05'],
                     ['18832', '2018-11-05'],
                     ['18833', '2018-11-05'],
                     ['18834', '2018-11-05'],
                     ['18835', '2018-11-05'],
                     ['18836', '2018-11-05'],
                     ['18838', '2018-11-05'],
                     ['18839', '2018-11-05'],
                     ['18840', '2018-11-05'],
                     ['18841', '2018-11-05'],
                     ['18842', '2018-11-05'],
                     ['18843', '2018-11-05'],
                     ['18844', '2018-11-05'],
                     ['18845', '2018-11-05'],
                     ['18846', '2018-11-05']]
exchange_date = datetime.datetime(year=2018, month=11, day=5)
currency_rates = ExchangeRate._get_cached_exchange(exchange_date.year, exchange_date.month, exchange_date.day)
for entry in invoice_to_change:
    inv = Invoice.objects.get(pk=entry[0])
    exchange_rate = Decimal(currency_rates[inv.order.get_region().currency.code]).quantize(Decimal('.0001'), rounding=ROUND_HALF_UP)
    inv.exchange_date = exchange_date
    inv.exchange_rate = exchange_rate
    inv.save()
    # print exchange_date, exchange_rate, entry[0]



