from __future__ import print_function
from orders.models import Order
from tqdm import tqdm
from django.utils import timezone

from pricing_v3.services.price_calculators import OrderPriceCalculator
from regions.models import Region

from orders.enums import OrderStatus

print('items to be done: ', Order.objects.filter(country='austria', status=OrderStatus.CART).count())

print("items with price ", Order.objects.filter(country='austria', status=OrderStatus.CART, total_price__gt=0).count())

print("items without price ", Order.objects.filter(country='austria', status=OrderStatus.CART).exclude(total_price__gt=0).count())

print(timezone.datetime.now())
Order.objects.filter(country='austria', status=OrderStatus.CART).exclude(total_price__gt=0).update(region_vat=True)

print(timezone.datetime.now())

for o in tqdm(Order.objects.filter(country='austria', status=OrderStatus.CART, total_price__gt=0, region_vat=False)):
    o.region_vat = True
    if o.region is None:
        o.region = Region.objects.filter(name='austria').last()
    if o.total_price > 0:
        OrderPriceCalculator(o).calculate()
    o.price_updated_at = timezone.datetime.now()
    o.save()

print(timezone.datetime.now())
