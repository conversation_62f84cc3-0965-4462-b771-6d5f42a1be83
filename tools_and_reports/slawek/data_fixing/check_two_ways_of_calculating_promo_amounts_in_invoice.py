from decimal import Decimal, ROUND_HALF_UP

from past.utils import old_div

# lets take last 5k invoices
from invoice.models import Invoice

invoices = Invoice.objects.order_by('-id')[:5000]
good = 0
bad = 0
bad_but_ok = 0
no_promo = 0
for invoice in invoices:
    self = invoice
    promo_amount = self.order.region_promo_amount
    if promo_amount == 0:
        no_promo += 1
        continue
    oi_for_vouchers = self.order.get_items_for_voucher()
    try:
        new_promo_percentage = old_div(promo_amount,sum([x.region_price for x in oi_for_vouchers])) if promo_amount > 0 else 0
    except ZeroDivisionError:
        print('o, zero', invoice.id)
    new_promo_per_item = []
    for oi in self.order.items.all().order_by('price'):
        if oi in oi_for_vouchers:
            item_promo = (Decimal(oi.get_price_number()) * Decimal(new_promo_percentage)).quantize(Decimal('.01'),rounding=ROUND_HALF_UP)
        else:
            item_promo = 0
        new_promo_per_item.append(item_promo)
    promo_percentage = old_div(promo_amount, (self.order.get_total_value() + promo_amount)) if promo_amount > 0 else 0
    promo_per_item = []
    for oi in self.order.items.all().order_by('price'):
        item_promo = (Decimal(oi.get_price_number()) * Decimal(promo_percentage)).quantize(Decimal('.01'),rounding=ROUND_HALF_UP)
        promo_per_item.append(item_promo)
    if sum(promo_per_item) == sum(new_promo_per_item):
        good += 1
    else:
        bad += 1
        if sum(new_promo_per_item) == promo_amount:
            bad_but_ok += 1
        else:
            print('invoice {}, roznica: stare- {} vs nowe- {} vs {} -promoamount z faktury'.format(invoice, sum(promo_per_item), sum(new_promo_per_item), promo_amount))

print('Good: {}, bad: {}, bad_but_ok: {},  no promo: {}, all {}'.format(good, bad, bad_but_ok, no_promo, good + bad))
