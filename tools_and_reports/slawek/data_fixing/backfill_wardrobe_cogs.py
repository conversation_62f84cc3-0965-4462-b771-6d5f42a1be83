import json
from datetime import datetime

from google.cloud import bigquery
from rest_framework import serializers
from rest_framework.fields import Read<PERSON>nlyField, SerializerMethodField

from kpi.big_query import export_serializer_to_big_query
from producers.models import Product

MAX_PRICING_ETL_BATCH = 500
LAST_INVOICE_OF_2016_ID = 13838
MAX_INVOICES_ETL_BATCH = 10000
LAST_PRODUCT_WE_IGNORE_ID = 12000
MAX_COGS_ETL_BATCH = 3000


class ProductBigQuerySerializer(serializers.ModelSerializer):
    item_id = ReadOnlyField(source='order_item.id')
    product_id = ReadOnlyField(source='id')
    order_id = ReadOnlyField(source='order.id')
    gallery_id = ReadOnlyField(source='order_item.order_item.id')
    material = ReadOnlyField(source='order_item.order_item.material')
    shelf_type = ReadOnlyField(source='order_item.order_item.shelf_type')
    weight = SerializerMethodField(method_name='get_weight_from_serialization')
    number_of_packages = SerializerMethodField(
        method_name='get_number_of_packages_from_serialization'
    )
    status = ReadOnlyField(source='get_status_display')

    features = SerializerMethodField(method_name='get_features_from_serialization')
    cogs_costs = SerializerMethodField(
        method_name='get_cogs_categories_from_serialization'
    )

    exported_at = SerializerMethodField()

    def get_weight_from_serialization(self, obj):
        try:
            return obj.details.cached_serialization['item']['weight']
        except (AttributeError, KeyError):
            return None

    def get_number_of_packages_from_serialization(self, obj):
        try:
            return len(obj.details.cached_serialization['item']['packs'])
        except Exception:
            return None

    def get_features_from_serialization(self, obj):
        try:
            return json.dumps(obj.details.cached_serialization['features_categorical'])
        except Exception:
            return None

    def get_cogs_categories_from_serialization(self, obj):
        try:
            return json.dumps(
                obj.details.cached_serialization['margins']['cogs_dict_mat_categories']
            )
        except Exception:
            return None

    def get_exported_at(self, obj):
        return datetime.now().isoformat()

    class Meta(object):
        model = Product
        fields = (
            'item_id',
            'product_id',
            'order_id',
            'material',
            'shelf_type',
            'weight',
            'number_of_packages',
            'status',
            'features',
            'cogs_costs',
            'exported_at',
            'updated_at',
            'created_at',
            'gallery_id',
            'batch',
            'manufactor',
        )


class ProductChangesBigQuerySerializer(serializers.ModelSerializer):
    item_id = ReadOnlyField(source='order_item.id')
    product_id = ReadOnlyField(source='id')
    order_id = ReadOnlyField(source='order.id')
    gallery_id = ReadOnlyField(source='order_item.order_item.id')
    shelf_type = ReadOnlyField(source='order_item.order_item.shelf_type')
    material = ReadOnlyField(source='order_item.order_item.material')
    weight = SerializerMethodField(method_name='get_weight_from_serialization')
    status = ReadOnlyField(source='get_status_display')
    region_price_net = ReadOnlyField(source='order_item.region_price_net')
    region_price = ReadOnlyField(source='order_item.region_price')
    region = ReadOnlyField(source='order_item.region.name')
    price = ReadOnlyField(source='order_item.price')
    price_net = ReadOnlyField(source='order_item.price_net')

    features = SerializerMethodField(method_name='get_features_from_serialization')
    cogs = SerializerMethodField(method_name='get_full_cogs_from_serialization')

    export_type = serializers.ReadOnlyField(default='standard')
    exported_at = SerializerMethodField()

    def get_exported_at(self, obj):
        return datetime.now().isoformat()

    def get_features_from_serialization(self, obj):
        try:
            return json.dumps(obj.details.cached_serialization['features_categorical'])
        except Exception:
            return None

    def get_weight_from_serialization(self, obj):
        try:
            return obj.details.cached_serialization['item']['weight']
        except (AttributeError, KeyError):
            return None

    def get_full_cogs_from_serialization(self, obj):
        try:
            #  item is more about item domains, not needed for cogs export
            return json.dumps(
                {
                    key: value
                    for key, value in obj.details.cached_serialization.items()
                    if key != 'item'
                }
            )
        except TypeError:
            return None

    # TODO: to properly change decimal to floats, for json dumps
    # As better solution, move to DRF json encoder in bigquery exports
    def to_representation(self, obj):
        data = super().to_representation(obj)
        data['region_price_net'] = float(data['region_price_net'])
        data['region_price'] = float(data['region_price'])
        data['price_net'] = float(data['price_net'])
        data['price'] = float(data['price'])
        return data

    class Meta(object):
        model = Product
        fields = (
            'item_id',
            'product_id',
            'order_id',
            'material',
            'shelf_type',
            'weight',
            'status',
            'features',
            'cogs',
            'exported_at',
            'updated_at',
            'created_at',
            'gallery_id',
            'batch',
            'manufactor',
            'region_price',
            'region_price_net',
            'price',
            'price_net',
            'region',
            'export_type',
        )


client = bigquery.Client()

queryset = Product.objects.filter(cached_product_type='watty').order_by('updated_at')

if queryset.count() > 3000:
    queryset = queryset[:3000]

export_serializer_to_big_query(
    'production_info',
    'products',
    model=Product,
    serializer=ProductBigQuerySerializer,
    write=bigquery.WriteDisposition.WRITE_APPEND,
    queryset=queryset,
)


client = bigquery.Client()


queryset = Product.objects.filter(cached_product_type='watty').order_by('updated_at')

queryset = queryset[:MAX_COGS_ETL_BATCH]

export_serializer_to_big_query(
    'production_info',
    'production_cogs',
    model=Product,
    serializer=ProductChangesBigQuerySerializer,
    write=bigquery.WriteDisposition.WRITE_APPEND,
    queryset=queryset,
)
