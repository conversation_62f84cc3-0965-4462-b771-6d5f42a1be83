from __future__ import print_function
from product_feeds.models import Feed
from tqdm import tqdm

#so, lets take all feeds:

feeds = Feed.objects.all()

#for each feed, lets take all of its items

items = []

for f in feeds:
    items += [int(x.jetty_id()) for x in f.feed_items]

items = list(set(items))
print('So we have {} unique items to check'.format(len(items)))
print(items[:5])

print('Lets check how many have strange prices:')
to_be_changed = []
for item in tqdm(items):
    jetty = Jetty.objects.get(pk=item)
    if jetty.price != jetty.get_shelf_price_as_number():
        to_be_changed.append(jetty)

print('And we have {} of {} with different prices'.format(len(to_be_changed), len(items)))

print('lets fix those!')

for jetty in tqdm(to_be_changed):
    jetty.price = jetty.get_shelf_price_as_number()
    jetty.save()
