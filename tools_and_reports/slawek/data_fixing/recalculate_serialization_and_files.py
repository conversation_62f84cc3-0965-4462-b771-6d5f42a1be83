from __future__ import print_function
from tqdm import tqdm
from producers.models import Product

'''
Can be used to recalculate all production files and everything

'''

batches_to_be_recalculated = [] # ProductBatch.objects.filter(id__gte=2130, id__lte=2169)

shelves_to_be_recalculated = Product.objects.filter(status=ProductStatus.NEW)

#lets add shelves from batches

shelves_from_batches = Product.objects.filter(batch_id__in=[x.id for x in batches_to_be_recalculated])

print('Batches: {}, shelves not batched {}, shelves in batches {}'.format(len(batches_to_be_recalculated), len(shelves_to_be_recalculated), len(shelves_from_batches)))

#first step, lets recalculate serialization

unique_items = list(set(list(shelves_from_batches) + list(shelves_to_be_recalculated)))

print('Serialization')

for product in tqdm(unique_items):
    product.update_product_serialization()

#second step, lets recalculate all files on products


print("Files for products")

for product in tqdm(unique_items):
    pdj = product.details
    pdj.generate_front_view()
    pdj.packaging_json = None
    pdj.generate_packaging_instruction()
    pdj.generate_connections_dxf()
    pdj.generate_connections_zip()
    # ... and instructions
    pdj.generate_instruction()

#third step, lets recalculate all batch files

print("Files for batches")
for batch in tqdm(batches_to_be_recalculated):
    print('now {}'.format(batch.id))
    try:
        # 3.5
        batch.generate_labels_for_elements()
        # 3.6
        batch.generate_labels_for_packaging()
        # 3.8
        batch.generate_nesting_packaging()

        # 3.9
        batch.generate_nesting_front_drawers()

    except Exception as x:
        print('Failed batch {}'.format(batch))
        print(x)


