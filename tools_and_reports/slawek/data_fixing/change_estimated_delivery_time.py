from datetime import datetime

orders_to_change = [(12460886, '2019-01-11'),
                    (12461471, '2019-01-11'),
                    (12461471, '2019-01-11'),
                    (12574195, '2019-01-11'),
                    (12497413, '2019-01-11'),
                    (11888352, '2019-01-11'),
                    (12647128, '2019-01-11'),
                    (10495510, '2019-01-11'),
                    (12464199, '2019-01-11'),
                    (7273, '2019-01-11'),
                    (12800407, '2019-01-11'),
                    (12577819, '2019-01-11'),
                    (12802473, '2019-01-11'),
                    (11328238, '2019-01-11'),
                    (12540012, '2019-01-11'),
                    (12540012, '2019-01-11'),
                    (12454761, '2019-01-11'),
                    (12454823, '2019-01-11'),
                    (12545283, '2019-01-11'),
                    (12621147, '2019-01-11'),
                    (12534889, '2019-01-11'),
                    (12603094, '2019-01-11'),
                    (12479854, '2019-01-11'),
                    (12769383, '2019-01-11'),
                    (12774674, '2019-01-11'),
                    (12269014, '2019-01-11'),
                    (12779848, '2019-01-11'),
                    (12711865, '2019-01-11'),
                    (12727090, '2019-01-11'),
                    (12562933, '2019-01-11'),
                    (12773560, '2019-01-11'),
                    (12700101, '2019-01-11'),
                    (12737698, '2019-01-11'),
                    (12728649, '2019-01-11'),
                    (12757980, '2019-01-11'),
                    (12837905, '2019-01-11'),
                    (11919377, '2019-01-11'),
                    (11251573, '2019-01-11'),
                    (12728886, '2019-01-11'),
                    (12518472, '2019-01-11'),
                    (12100854, '2019-01-11'),
                    (8197494, '2019-01-11'),
                    (12758921, '2019-01-11'),
                    (12769322, '2019-01-11'),
                    (12713298, '2019-01-11'),
                    (12483979, '2019-01-11'),
                    (12761164, '2019-01-11'),
                    (12804017, '2019-01-11'),
                    (12813081, '2019-01-11'),
                    (11213364, '2019-01-11'),
                    (12529765, '2019-01-11'),
                    (12458868, '2019-01-11'),
                    (12721926, '2019-01-11'),
                    (11535255, '2019-01-11'),
                    (12516601, '2019-01-11'),
                    (11370847, '2019-01-11'),
                    (12527970, '2019-01-11'),
                    (12597711, '2019-01-11'),
                    (12355822, '2019-01-11'),
                    (12095556, '2019-01-11'),
                    (12465553, '2019-01-11'),
                    (12733822, '2019-01-11'),
                    (12048572, '2019-01-11'),
                    (12735160, '2019-01-11'),
                    (12737084, '2019-01-11'),
                    (12539758, '2019-01-11'),
                    (12729938, '2019-01-11'),
                    (11887450, '2019-01-11'),
                    (12752244, '2019-01-11'),
                    (12683369, '2019-01-11'),
                    (12750146, '2019-01-11'),
                    (12758659, '2019-01-11'),
                    (5473715, '2019-01-11'),
                    (11291414, '2019-01-11'),
                    (12421165, '2019-01-11'),
                    (12421929, '2019-01-11'),
                    (12306489, '2019-01-11'),
                    (12306489, '2019-01-11'),
                    (12572848, '2019-01-11'),
                    (12765878, '2019-01-11'),
                    (12768261, '2019-01-11'),
                    (12758921, '2019-01-11'),
                    (12771203, '2019-01-11'),
                    (12772445, '2019-01-11'),
                    (12381972, '2019-01-11'),
                    (12546705, '2019-01-11'),
                    (12808095, '2019-01-11'),
                    (12808420, '2019-01-11'),
                    (11448724, '2019-01-11'),
                    (10929078, '2019-01-11'),
                    (12196039, '2019-01-11'),
                    (10743075, '2019-01-11'),
                    (12157684, '2019-01-11'),
                    (10563933, '2019-01-11'),
                    (12763242, '2019-01-11'),
                    (12774499, '2019-01-11'),
                    (12789287, '2019-01-11'),
                    (11948991, '2019-01-18'),
                    (11948991, '2019-01-18'),
                    (12486805, '2019-01-18'),
                    (12497599, '2019-01-18'),
                    (12457161, '2019-01-18'),
                    (12457161, '2019-01-18'),
                    (12439989, '2019-01-18'),
                    (12609829, '2019-01-18'),
                    (12575226, '2019-01-18'),
                    (12754449, '2019-01-18'),
                    (12869570, '2019-01-18'),
                    (12864309, '2019-01-18'),
                    (12823434, '2019-01-18'),
                    (8093564, '2019-01-18'),
                    (12831909, '2019-01-18'),
                    (12757546, '2019-01-18'),
                    (8758224, '2019-01-18'),
                    (13014842, '2019-01-18'),
                    (12689718, '2019-01-18'),
                    (12194855, '2019-01-18'),
                    (10544578, '2019-01-18'),
                    (12384109, '2019-01-18'),
                    (12535286, '2019-01-18'),
                    (12048301, '2019-01-18'),
                    (12533846, '2019-01-18'),
                    (11198123, '2019-01-18'),
                    (12915683, '2019-01-18'),
                    (12891003, '2019-01-18'),
                    (659248, '2019-01-18'),
                    (12813594, '2019-01-18'),
                    (12964782, '2019-01-18'),
                    (12769226, '2019-01-18'),
                    (11745963, '2019-01-18'),
                    (12207220, '2019-01-18'),
                    (12207220, '2019-01-18'),
                    (11846407, '2019-01-18'),
                    (12218489, '2019-01-18'),
                    (11133961, '2019-01-18'),
                    (12496597, '2019-01-18'),
                    (4870445, '2019-01-18'),
                    (12951667, '2019-01-18'),
                    (10854553, '2019-01-18'),
                    (9514717, '2019-01-18'),
                    (12425562, '2019-01-18'),
                    (11423239, '2019-01-18'),
                    (12910547, '2019-01-18'),
                    (12776903, '2019-01-18'),
                    (12822049, '2019-01-18'),
                    (12881130, '2019-01-18'),
                    (12901477, '2019-01-18'),
                    (12941844, '2019-01-18'),
                    (12881130, '2019-01-18'),
                    (12936499, '2019-01-18'),
                    (12205763, '2019-01-18'),
                    (12204096, '2019-01-18'),
                    (1649805, '2019-01-18'),
                    (1649805, '2019-01-18'),
                    (12089860, '2019-01-18'),
                    (12462426, '2019-01-18'),
                    (10282904, '2019-01-18'),
                    (12902210, '2019-01-18'),
                    (12859972, '2019-01-18'),
                    (12609829, '2019-01-18'),
                    (12406024, '2019-01-18'),
                    (8758780, '2019-01-18'),
                    (12199596, '2019-01-18'),
                    (12199596, '2019-01-18'),
                    (12533755, '2019-01-18'),
                    (11968361, '2019-01-18'),
                    (12439836, '2019-01-18'),
                    (12827220, '2019-01-18'),
                    (12798930, '2019-01-18'),
                    (12980285, '2019-01-18'),
                    (12979985, '2019-01-18'),
                    (13018660, '2019-01-18'),
                    (6999127, '2019-01-18'),
                    (12655946, '2019-01-18'),
                    (12950549, '2019-01-18'),
                    (12898024, '2019-01-18'),
                    (10188025, '2019-01-18'),
                    (12920238, '2019-01-18'),
                    (12985703, '2019-01-18'),
                    (13026323, '2019-01-18'),
                    (12920938, '2019-01-18'),
                    (13067891, '2019-01-18'),
                    (11948448, '2019-01-18'),
                    (11922107, '2019-01-18'),
                    (12999458, '2019-01-18'),
                    (13026620, '2019-01-18'),
                    (13000738, '2019-01-25'),
                    (12820490, '2019-01-25'),
                    (11561331, '2019-01-25'),
                    (13018947, '2019-01-25'),
                    (13007883, '2019-01-25'),
                    (12403992, '2019-01-25'),
                    (13027273, '2019-01-25'),
                    (12947018, '2019-01-25'),
                    (12984515, '2019-01-25'),
                    (9358241, '2019-01-25'),
                    (9358241, '2019-01-25'),
                    (12903075, '2019-01-25'),
                    (13047787, '2019-01-25'),
                    (13064782, '2019-01-25'),
                    (13064782, '2019-01-25'),
                    (13029416, '2019-01-25'),
                    (12987904, '2019-01-25'),
                    (13100530, '2019-01-25'),
                    (13122415, '2019-01-25'),
                    (13181649, '2019-01-25'),
                    (13189663, '2019-01-25'),
                    (13215195, '2019-01-25'),
                    (13227404, '2019-01-25'),
                    (13227404, '2019-01-25'),
                    (13234066, '2019-01-25'),
                    (13266943, '2019-01-25'),
                    (13271491, '2019-01-25'),
                    (13236760, '2019-01-25'),
                    (13280314, '2019-01-25'),
                    (13285009, '2019-01-25'),
                    (13287902, '2019-01-25'),
                    (13287902, '2019-01-25'),
                    (13182270, '2019-01-25'),
                    (13299431, '2019-01-25'),
                    (13303276, '2019-01-25'),
                    (13303276, '2019-01-25'),
                    (13217875, '2019-01-25'),
                    (13185774, '2019-01-25'),
                    (13086258, '2019-01-25'),
                    (13322514, '2019-01-25'),
                    (13326697, '2019-01-25'),
                    (13328700, '2019-01-25'),
                    (13038516, '2019-01-25'),
                    (13135087, '2019-01-25'),
                    (13301422, '2019-01-25'),
                    (13390456, '2019-01-25'),
                    (13296417, '2019-01-25'),
                    (12641423, '2019-01-25'),
                    (10821714, '2019-01-25'),
                    (12894434, '2019-01-25'),
                    (13144258, '2019-01-25'),
                    (13218086, '2019-01-25'),
                    (13071839, '2019-01-25'),
                    (13253075, '2019-01-25'),
                    (13369939, '2019-01-25'),
                    (13181038, '2019-01-25'),
                    (13181038, '2019-01-25'),
                    (13030523, '2019-01-25'),
                    (13044268, '2019-01-25'),
                    (13076865, '2019-01-25'),
                    (13076865, '2019-01-25'),
                    (13076865, '2019-01-25'),
                    (11313490, '2019-01-25'),
                    (13103484, '2019-01-25'),
                    (12993298, '2019-01-25'),
                    (13067061, '2019-01-25'),
                    (13078643, '2019-01-25'),
                    (13078020, '2019-01-25'),
                    (13128795, '2019-01-25'),
                    (13161361, '2019-01-25'),
                    (13096850, '2019-01-25'),
                    (12949431, '2019-01-25'),
                    (12949431, '2019-01-25'),
                    (13188302, '2019-01-25'),
                    (13220940, '2019-01-25'),
                    (13145596, '2019-01-25'),
                    (13146422, '2019-01-25'),
                    (13136089, '2019-01-25'),
                    (13206513, '2019-01-25'),
                    (13214865, '2019-01-25'),
                    (13220585, '2019-01-25'),
                    (13220940, '2019-01-25'),
                    (13257330, '2019-01-25'),
                    (12524575, '2019-01-25'),
                    (13173200, '2019-01-25'),
                    (13145596, '2019-01-25'),
                    (12864272, '2019-01-25'),
                    (13077339, '2019-01-25'),
                    (13033135, '2019-01-25'),
                    (13053360, '2019-01-25'),
                    (13108360, '2019-01-25'),
                    (13008000, '2019-01-25'),
                    (13126231, '2019-01-25'),
                    (13147941, '2019-01-25'),
                    (13173200, '2019-01-25'),
                    (13173200, '2019-01-25'),
                    (13184803, '2019-01-25'),
                    (13184803, '2019-01-25'),
                    (13184803, '2019-01-25'),
                    (13181038, '2019-01-25'),
                    (13194154, '2019-01-25'),
                    (13183218, '2019-01-25'),
                    (13205948, '2019-01-25'),
                    (13244532, '2019-01-25'),
                    (13246451, '2019-01-25'),
                    (11609063, '2019-01-25'),
                    (13271266, '2019-01-25'),
                    (13257330, '2019-01-25'),
                    (13307513, '2019-01-25'),
                    (12919515, '2019-01-25'),
                    (13332460, '2019-01-25'),
                    (13333312, '2019-01-25'),
                    (13338513, '2019-01-25'),
                    (13338513, '2019-01-25'),
                    (13340291, '2019-01-25'),
                    (13257038, '2019-01-25'),
                    (13355445, '2019-01-25'),
                    (13356038, '2019-01-25'),
                    (13389026, '2019-01-25'),
                    (5813499, '2019-01-25'),
                    (13399220, '2019-01-25'),
                    (13413238, '2019-01-25'),
                    (12103302, '2019-01-25'),
                    (13075841, '2019-01-25'),
                    (13395283, '2019-01-25'),
                    (13395283, '2019-01-25'),
                    (13184493, '2019-01-25'),
                    (13215003, '2019-01-25'),
                    (13191911, '2019-01-25'),
                    (13240995, '2019-01-25'),
                    (13289813, '2019-01-25'),
                    (12103302, '2019-01-25'),
                    (13147941, '2019-01-25'),
                    (13399220, '2019-01-25'),
                    (13229444, '2019-01-25'),
                    (13229444, '2019-01-25'),
                    (13326495, '2019-01-25'),
                    (13304562, '2019-01-25'),
                    (12882673, '2019-01-25'),
                    (13399466, '2019-01-25'),
                    (13404236, '2019-01-25'),
                    (13125299, '2019-01-25'),
                    (12990265, '2019-01-25')]

for entry in orders_to_change:
    date = entry[1].split('-')
    # date in format 2018-01-02
    order = Order.objects.get(pk=entry[0])
    order.estimated_delivery_time = datetime(year=int(date[0]), month=int(date[1]), day=int(date[2]), hour=18)
    order.save(update_fields=['estimated_delivery_time'])
