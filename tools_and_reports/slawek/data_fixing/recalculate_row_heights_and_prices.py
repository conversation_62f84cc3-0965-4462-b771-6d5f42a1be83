from __future__ import print_function
from gallery.models import Jetty
from orders.enums import OrderStatus
from orders.models import Order
from tqdm import tqdm
from django.utils import timezone
import json

from pricing_v3.services.price_calculators import OrderPriceCalculator

#lets take all carts with ivies

# saved items

all_items = Jetty.objects.filter(shelf_type=0).order_by('-id')

# all_items = Jetty.objects.filter(id=344804)
# all_items = Jetty

with tqdm(total=all_items.count()) as pbar:
    for item in all_items:
        pbar.update(1)
        for i, value in enumerate(item.rows):
            if value in [200, 300, 400]:
                continue
            if abs(value-208) < 2:
                item.rows[i] = 200
            elif abs(value-278) < 2:
                item.rows[i] = 300
            elif abs(value-398) < 2:
                item.rows[i] = 400
            else:
                pass
        try:
            item.recalculate_with_actual_dna(modules_update=True)
        except Exception as ex:
            print("ok, blad w ", item.id)
            print(str(ex))


# tutaj trzeba przed wylaczyc loggera bo bedzie 2 razy wolniej z shella
all_carts = Order.objects.filter(status=OrderStatus.CART, total_price__gt=0).order_by('-id')

with tqdm(total=all_carts.count()) as pbar:
    for o in all_carts:
        pbar.update(1)
        for orderItem in o.items.filter(content_type=23):
            item = orderItem.order_item
            if item is None or item.shelf_type == 1:
                continue
            for i, value in enumerate(item.rows):
                if value in [200, 300, 400]:
                    continue
                if abs(value-208) < 2:
                    item.rows[i] = 200
                elif abs(value-278) < 2:
                    item.rows[i] = 300
                elif abs(value-398) < 2:
                    item.rows[i] = 400
                else:
                    pass
            try:
                item.recalculate_with_actual_dna(modules_update=True)
            except Exception as ex:
                print("ok, blad w ", item.id)
                print(str(ex))


        if o.total_price > 0:
            aa = OrderPriceCalculator(o).calculate()
        # tutaj przejscie na updated_fields zeby nie przesuwac updated_at daty w koszykach
        o.price_updated_at = timezone.datetime.now()
        o.save()
