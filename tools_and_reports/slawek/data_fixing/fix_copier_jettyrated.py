from __future__ import print_function
import tqdm

from rating_tool.models import FurnitureInCategory

res = []
different_material_with_base = 0
same_color_as_base = 0
missing_parent = 0

for category in tqdm.tqdm(FurnitureInCategory.objects.filter(id__in=[13, 14, 15, 16])):
    for rated_jetty in category.furnitureincategory_set.filter(jetty__owner__username__contains='tylkocopy'):
        try:
            base_item = Jetty.objects.get(pk=rated_jetty.jetty.base_preset)
            if rated_jetty.jetty.material != base_item.material:
                rated_jetty.jetty.material = base_item.material
                rated_jetty.jetty.save()

        except Jetty.DoesNotExist:
            print('brak base preset', rated_jetty.jetty_id)
            missing_parent += 1
print(len(res))
print(different_material_with_base)
print(same_color_as_base)
print(missing_parent)
