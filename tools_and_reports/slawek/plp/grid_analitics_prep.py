import csv
import functools

raw_file_from_backend = "~/Downloads/grid_data_200615_1348.csv"
bq_export = "~/Downloads/results-20200615-150829.csv"

items = []
import pandas as pd

raw_file = pd.read_table(raw_file_from_backend, sep=";")
bg_export = pd.read_table(bq_export, sep=",")


# print(raw_file)
filter_df = bg_export["productlistname"].str.contains("_92")
bg_export = bg_export[filter_df]
bg_export["productlistname"] = bg_export.apply(
    lambda x: x["productlistname"].split("__id")[0], axis=1
)
bg_export["oldposition"] = bg_export.apply(
    lambda x: ((x["productlistposition"] // 10) - 1) * 3
    + (x["productlistposition"] % 10)
    - 1,
    axis=1,
)
entries_from_be = raw_file.to_dict(orient="records")
entries_from_bg = bg_export.to_dict(orient="records")

for entry in entries_from_bg:
    entry_to_change = list(filter(lambda x: entry['productlistname'] == x['board'] and entry['productsku'] == x['id'], entries_from_be))
    if len(entry_to_change) == 1:
        entry_to_change = entry_to_change[0]
        entry_to_change['clicks'] = entry_to_change.get('clicks', 0) + entry['clicks']
        entry_to_change['views'] = entry_to_change.get('views', 0) + entry['views']
    elif len(entry_to_change) == 0:
        print("BRAKUJE WPISU DLA", entry)
    else:
        get_distance_from_position = lambda entry_bg: min(
            abs(entry_bg['position'] - (((entry['productlistposition'] // 10) - 1) * 3 + (entry['productlistposition'] % 10) - 1)),
            abs(entry_bg['position'] - (((entry['productlistposition'] // 10) - 1) * 2 + (entry['productlistposition'] % 10) - 1))
        )
        entry_to_change = sorted(
            entry_to_change,
            key=get_distance_from_position)
        entry_to_change = entry_to_change[0]
        entry_to_change['clicks'] = entry_to_change.get('clicks', 0) + entry['clicks']
        entry_to_change['views'] = entry_to_change.get('views', 0) + entry['views']

pd.DataFrame(entries_from_be).to_csv('results2.csv')
# tutaj nie pyknie, nie merge a przeszukanie drugiego i dopisanie wartosci dla itemXpozycja

merged_df = pd.merge(
    raw_file,
    bg_export,
    left_on=["board", "id"],
    right_on=["productlistname", "productsku"],
)
merged_df = merged_df.drop(
    ["productlistname", "productsku", "oldposition", "productlistposition", "clicks_x"],
    axis=1,
)
# print(merged_df)
merged_df.to_csv("results.csv")

BOARD_EXPORT_LIST = []
for category in ["cat_all",] + ["cat_{}".format(x) for x in range(1, 7)]:
    for shelf_type_variant in [
        "",
        "__type_all",
        "__type_2",
        "__type_3",
    ]:
        BOARD_EXPORT_LIST.append("{}{}".format(category, shelf_type_variant))
print(BOARD_EXPORT_LIST)
