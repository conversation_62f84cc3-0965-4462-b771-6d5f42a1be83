import openpyxl
import requests

REGION_DICT = {
    'Others': '',
    'Switzerland': 'switzerland',
    'Netherlands': 'netherlands',
    'UK': 'united_kingdom',  # ?
    'France': 'france',
    'Germany': 'germany',
}

CATEGORY_DICT = {
    'All products': '',
    'Sideboards': 'sideboard',
    'Shoe racks': 'shoerack',
    'Bookcases': 'bookcase',
    'TV stands': 'tvstand',
    'Wallstorage': 'wallstorage',
}


def get_board_name(country, category):
    features = [
        'abtest=ff_plp_version_b',
    ]

    if category:
        features.append('category=' + category)

    if country:
        features.append('region=' + country)

    return '__'.join(sorted(features)) or ''


path = "/home/<USER>/Downloads/Manual_merch.xlsx"

wb = openpyxl.load_workbook(path)

rows = []
for sheet in wb.worksheets:
    m_row = sheet.max_row
    for i in range(1, m_row + 1):
        row = [cell.value for cell in sheet[i]]
        rows.append(row)

rows = [x for x in rows if x[0] != 'Pozycja']
top_sellers = list(set([x[3] for x in rows if x[4] is not None]))

print(f'We have {len(top_sellers)}')
print(
    'Top sellers: ?object_id=' + ','.join([str(x.split(' ')[1]) for x in top_sellers])
)

overrides_to_add = []

for row in rows:
    if row[1] not in REGION_DICT.keys() or row[2] not in CATEGORY_DICT.keys():
        print(row[1] not in REGION_DICT.keys())
        print(row[2] not in CATEGORY_DICT.keys())
        print('brakuje tlumaczen dla ', row)
        exit(-1)
    furniture_type, object_id = row[3].split(' ')

    overrides_to_add.append(
        {
            'order': row[0],
            'board_name': get_board_name(REGION_DICT[row[1]], CATEGORY_DICT[row[2]]),
            'furniture_type': 'watty' if 'watty' in furniture_type else 'jetty',
            'furniture_id': int(object_id),
        }
    )

print(f'We have {len(overrides_to_add)} entries to add')

for entry in overrides_to_add:
    r = requests.post(
        "https://eskel.ecom.oklyt.pl/api/v1/catalogue/board_override/",
        json=entry,
    )
    if r.status_code != 201:
        print(r.status_code)
        print(r.content)
    else:
        print('.')
