import requests, json

data = {
  "name": "id_4272 20210709 matte black",
  "description": "Black matte test proposition",
  "parent": 4272,
  "changes": [
    {
      "board": "cat_1__type_all",
      "position": 1,
      "id": "1504422",
      "shelf_type": 1,
      "material": 6,
      "copy_from_id": "1504422"
    },
    {
      "board": "cat_all__type_all",
      "position": 2,
      "id": "1480084",
      "shelf_type": 1,
      "material": 6,
      "copy_from_id": "1480084"
    },
    {
      "board": "cat_2__type_all",
      "position": 3,
      "id": "1480095",
      "shelf_type": 1,
      "material": 6,
      "copy_from_id": "1480095"
    },
    {
      "board": "cat_6__type_all",
      "position": 3,
      "id": "1166350",
      "shelf_type": 1,
      "material": 6,
      "copy_from_id": "1166350"
    },
    {
      "board": "cat_3__type_all",
      "position": 4,
      "id": "1453032",
      "shelf_type": 1,
      "material": 6,
      "copy_from_id": "1453032"
    },
    {
      "board": "cat_5__type_all",
      "position": 5,
      "id": "318819",
      "shelf_type": 1,
      "material": 6,
      "copy_from_id": "318819"
    },
    {
      "board": "cat_3__type_all",
      "position": 14,
      "id": "1862510",
      "shelf_type": 1,
      "material": 6,
      "copy_from_id": "1862510"
    }
  ],
  "save": True
}



token = "91e5deaedc993029b2a949ebbaf8ef42faa6a8dd"
r = requests.post('https://staging.a.tylko.com/api/v1/rating_tool/board_category/',
                  data=json.dumps(data),
                  headers={
                      'authorization': "TOKEN {}".format(token),
                      'content-type': "application/json",
                  })
print(json.dumps(data))
print(r.status_code)
