import redis
import pickle
from collections import Counter

from django.conf import settings

CHUNK_SIZE = 50
LIST_NAME = "huey.redis.cstm"

r = redis.Redis(
    host=settings.HUEY["connection"]["host"],
    port=settings.HUEY["connection"]["port"],
    db=settings.HUEY["connection"]["db"],
)

max_number = r.llen(LIST_NAME)
actual_number = 0
task_list = []

batch_create_tasks = []
batch_numbers = []

while actual_number < max_number:
    parts = r.lrange(LIST_NAME, actual_number, actual_number + CHUNK_SIZE)
    actual_number += CHUNK_SIZE
    for single_task in parts:
        actual_task = pickle.loads(single_task)
        if actual_task[1] == 'queuecmd_generate_all_batch_files_task':
            batch_create_tasks.append(single_task)
            batch_numbers.append(actual_task[5][0])
        task_list.append(actual_task[1])


counter = Counter(task_list)
print(f"So we have {len(task_list)}, most common:")
print(counter.most_common())
print("First 10 tasks:")
print(*task_list[:10], sep="\n")
print("Batch numbers counter")
print(Counter(batch_numbers).most_common())

# for EXTREME CASES, removing code
# for batch_to_remove in batch_create_tasks:
#     r.lrem(LIST_NAME, 1, batch_to_remove)

