import redis
import json
from collections import Counter

from django.conf import settings

CHUNK_SIZE = 50
LIST_NAME = "celery"

r = redis.Redis(
    host=settings.HUEY["connection"]["host"],
    port=settings.HUEY["connection"]["port"],
    db=settings.HUEY["connection"]["db"],
)

max_number = r.llen(LIST_NAME)
actual_number = 0
task_list = []

batch_create_tasks = []
batch_numbers = []

while actual_number < max_number:
    parts = r.lrange(LIST_NAME, actual_number, actual_number + CHUNK_SIZE)
    actual_number += CHUNK_SIZE
    for single_task in parts:
        actual_task = json.loads(single_task)
#        if actual_task['headers']['task'] == 'producers.tasks.generate_instruction_task':
#            batch_create_tasks.append(single_task)
#            batch_numbers.append()
        task_list.append(actual_task['headers']['task'])


counter = Counter(task_list)
print(f"So we have {len(task_list)}, most common:")
print(counter.most_common())
print("First 10 tasks:")
print(*task_list[:10], sep="\n")
print("Batch numbers counter")
print(Counter(batch_numbers).most_common())

# for EXTREME CASES, removing code
# for batch_to_remove in batch_create_tasks:
#     r.lrem(LIST_NAME, 1, batch_to_remove)

