import json, requests, csv
import time
from rich.progress import track
from rich import print

ids_for_translations = json.load(open('data/0001_keys_nuxt.json', 'r'))
# ids_for_django = json.load(open('data/0001_keys_django.json', 'r'))

file_with_translations = list(csv.reader(open('translations/all_django_and_nuxt_nl.csv', 'r'), delimiter=';'))

new_file = []
number_of_not_found = 0
number_of_found = 0

for ix, entry in enumerate(track(file_with_translations)):

        found_key = next((x for x in ids_for_translations if entry[0] == x['name']), None)
        if found_key is None:
            number_of_not_found += 1
            continue
        else:
            number_of_found += 1
            new_file.append(entry)

with open('translations/nuxt_nl_stripped.csv', 'w') as fp:
    csv_writer = csv.writer(fp)
    csv_writer.writerows(new_file)

print(f'Found keys {number_of_found}, number of not found: {number_of_not_found}')