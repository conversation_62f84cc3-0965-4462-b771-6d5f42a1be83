import json, requests, csv
import time
from rich.progress import track, SpinnerColumn, TimeElapsedColumn, TextColumn
from rich.progress import Progress
from rich import print
from settings import AUTHORIZATION_KEY, get_project_settings

api_key = AUTHORIZATION_KEY

TARGET_PROJECT = 'django'

project_info = get_project_settings(TARGET_PROJECT)
project_id = project_info['project_id']

# target locale:

TARGET_LOCALE = 'nl'

if TARGET_LOCALE == 'es':
    target_locale = "da8c1374fdb5206dbe31357cd8884596"
    target_language = 'es'

elif TARGET_LOCALE == 'nl':
    target_locale = "88732dd9b9dc05dfbda9588868dcbb00"
    target_language = 'nl'

target_branch = 'SPANISH'


locale_list = ['es', 'nl']
ids_for_translations = json.load(open(f'data/0001_keys_{TARGET_PROJECT}.json', 'r'))

for key in track(ids_for_translations):
    request = requests.get(
        f'https://api.phrase.com/v2/projects/{project_id}/keys/{key["id"]}/translations?branch={target_branch}',
        headers={'Content-Type': 'application/json',
                 'Authorization': f'token {api_key}',
                 'User-Agent': 'Example test app for tylko account'
                 })
    response = request.json()
    # print(response)
    actual_translations = [x for x in response if x['locale']['code'] in locale_list]

    if request.status_code != 200:
        print(f'{request.status_code} -> {response}')

    for translation in actual_translations:
        #print(translation)
        key[f'id_for_{translation["locale"]["code"]}'] = translation['id']

    if int(request.headers['X-Rate-Limit-Remaining']) < 10:
        print(f'going to sleep - after some elements')
        time.sleep(60)


json.dump(ids_for_translations, open(f'data/0003_keys_with_translations_{TARGET_PROJECT}.csv', 'w'))
