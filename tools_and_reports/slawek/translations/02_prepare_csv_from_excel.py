import csv

import phrase_api, json
import click
from phrase_api.rest import ApiException
from settings import get_configuration, get_project_settings
import openpyxl
import requests


@click.command()
@click.option('--locale', required=True, help='en/es')
@click.option('--filename', required=True, help='nuxt/django')
@click.option('--header', required=True, help='yes/no')
def main_function(locale, filename, header):

    wb = openpyxl.load_workbook(filename)

    rows = []
    for sheet in wb.worksheets:
        m_row = sheet.max_row
        for i in range(2 if header == 'yes' else 1, m_row + 1):
            row = [cell.value for cell in sheet[i]]
            if row[0]:
                rows.append(row)
    new_name = filename.split('/')[-1].replace("xlsx", "csv").replace(' ', '_')
    new_name = f'source_{locale}/{new_name}'
    with open(new_name, 'w') as f:
        writer = csv.writer(f, delimiter=';')
        writer.writerows(rows)

if __name__ == '__main__':
    main_function()
