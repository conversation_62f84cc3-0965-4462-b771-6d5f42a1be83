import csv

import phrase_api, json
import click
from rich import print
from phrase_api.rest import ApiException
from settings import get_configuration, get_project_settings
import openpyxl
import requests

django_keys = json.load(open('data/0001_keys_django.json', 'r'))
django_base_en = list(csv.reader(open('data/0002_en_django.csv', 'r'), delimiter=','))

nuxt_keys = json.load(open('data/0001_keys_nuxt.json', 'r'))
nuxt_base_en = list(csv.reader(open('data/0002_en_nuxt.csv', 'r'), delimiter=','))


@click.command()
@click.option('--locale', required=True, help='en/es')
def main_function(locale):

    total_to_translate = 0
    found_in_nuxt = 0
    found_in_django = 0
    found_many = 0
    not_found = []
    reader = csv.reader(open(f'source_{locale}/combined.csv', 'r'), delimiter=';')

    final_django_lines = []
    final_nuxt_lines = []

    for line in reader:
        line_found = False
        if len(line) < 2:
            continue
        total_to_translate += 1
        found_keys = [x[0] for x in django_base_en if x[1].strip() == line[1].strip()]
        if len(found_keys) > 0:
            #print('found many!', line[:3], found_keys)
            for key in found_keys:
                if key not in [x[0] for x in final_django_lines]:
                    final_django_lines.append(
                        [key, line[1], line[2]]
                    )
            if len(found_keys) > 1:
                found_many += 1
                found_in_django += len(found_keys)
            else:
                found_in_django += 1
            line_found = True

        found_keys = [x[0] for x in nuxt_base_en if x[1].strip() == line[1].strip()]
        if len(found_keys) > 0:
            #print('found many!', line[:3], found_keys)
            for key in found_keys:
                if key not in [x[0] for x in final_nuxt_lines]:
                    final_nuxt_lines.append(
                        [key, line[1], line[2]]
                    )
            if len(found_keys) > 1:
                found_many += 1
                found_in_nuxt += len(found_keys)
            else:
                found_in_nuxt += 1
        else:
            if not line_found:
                not_found.append(line)
    print(f'Entries {total_to_translate}, found many {found_many}, not found {len(not_found)}, in django {len(final_django_lines)}, in nuxt {len(final_nuxt_lines)}')
    print(f'So we have missing: in django {len(django_keys)}/{len(final_django_lines)}')
    print(f'So we have missing: in nuxt {len(nuxt_keys)}/{len(final_nuxt_lines)}')
    non_empty_not_found = [x for x in not_found if x[1] ]
    print('From not found - not empty is: ', len(non_empty_not_found))

    with open(f'final_{locale}/missing_keys.csv', 'w') as fp:
        writer = csv.writer(fp)
        writer.writerows(non_empty_not_found)

    with open(f'final_{locale}/django_{locale}.csv', 'w') as fp:
        writer = csv.writer(fp)
        writer.writerows(final_django_lines)

    with open(f'final_{locale}/nuxt_{locale}.csv', 'w') as fp:
        writer = csv.writer(fp)
        writer.writerows(final_nuxt_lines)

if __name__ == '__main__':
    main_function()
