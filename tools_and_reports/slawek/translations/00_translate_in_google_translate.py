import csv
import json
from google.cloud import translate_v2 as translate

translate_client = translate.Client()

dictionary_to_translate = []
for x in csv.reader(open('en.csv', 'r')):
    if x[0] == 'key_name':
        continue
    dictionary_to_translate.append({
        'key_name': x[0],
        'en': x[1]
    })

print(len(dictionary_to_translate))
for i, x in enumerate(dictionary_to_translate[1000:]):
    if i % 10 == 0:
        print(i)
    result = translate_client.translate(x['en'], target_language='pl')
    x['pl'] = result['translatedText']

json.dump(dictionary_to_translate[1000:], open('wynik_z_pl_1000_koniec.json', 'w'))

