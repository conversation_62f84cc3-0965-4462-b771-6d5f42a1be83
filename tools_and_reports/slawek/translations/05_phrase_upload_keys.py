import json, requests, csv
import time
from rich.progress import track, SpinnerColumn, TimeElapsedColumn, TextColumn
from rich.progress import Progress
from rich import print
from settings import AUTHORIZATION_KEY, get_project_settings

api_key = AUTHORIZATION_KEY
TARGET_PROJECT_TYPE = 'django'
TARGET_LOCALE = 'nl'



project_info = get_project_settings(TARGET_PROJECT_TYPE)
project_id = project_info['project_id']


target_branch = 'SPANISH'

messed_keys = True

ids_for_translations = json.load(open(f'data/0003_keys_with_translations_{TARGET_PROJECT_TYPE}.csv', 'r'))

file_with_translations = list(csv.reader(open(f'final_{TARGET_LOCALE}/{TARGET_PROJECT_TYPE}_{TARGET_LOCALE}.csv', 'r'), delimiter=','))


number_of_already_done = 0
number_of_not_found = 0
number_of_found = 0

number_of_404 = 0
number_of_200 = 0

number_of_translated = 0
number_of_nottranslated = 0


def create_translation(ix, key_update_parameters):
    request = requests.post(f'https://api.phrase.com/v2/projects/{project_id}/translations',
                            data=json.dumps(key_update_parameters),
                            headers={'Content-Type': 'application/json',
                                     'Authorization': f'token {api_key}',
                                     'User-Agent': 'Example test app for tylko account'
                                     })

    if int(request.headers['X-Rate-Limit-Remaining']) < 10:
        print(f'going to sleep - after {ix} elements')
        time.sleep(60)

    if request.status_code == 201:
        return 'added'
    elif request.status_code == 422:
        return 'skipped'
    else:
        print(request.status_code)
        print(request.content)
        return None


def update_translation(ix, translation_id, branch, content):

    key_update_parameters = {
        'content': content,
        'branch': branch
    }
    # print(f'https://api.phrase.com/v2/projects/{project_id}/translations/{translation_id}')
    # print(json.dumps(key_update_parameters))
    request = requests.patch(
        f'https://api.phrase.com/v2/projects/{project_id}/translations/{translation_id}',
        data=json.dumps(key_update_parameters),
        headers={'Content-Type': 'application/json',
                 'Authorization': f'token {api_key}',
                 'User-Agent': 'Example test app for tylko account'
                 })
    # print(request.json())
    if 'X-Rate-Limit-Remaining' in request.headers and int(request.headers['X-Rate-Limit-Remaining']) < 10:
        print(f'going to sleep - after {ix} elements')
        time.sleep(60)

    # print(request.status_code)
    if request.status_code == 200:
        return 'added'
    elif request.status_code == 422 or request.status_code == 404:
        return 'skipped'
    else:
        print(request.status_code)
        print(request.content)
        return None


with Progress(
    SpinnerColumn(),
    *Progress.get_default_columns(),
    TimeElapsedColumn(),
) as progress:

    main_task = progress.add_task("[red]Updating...", total=len(file_with_translations))
    for ix, entry in enumerate(file_with_translations):

        progress.update(main_task, advance=1)
        if len(entry) < 2:
            continue
        found_key = next((x for x in ids_for_translations if entry[0] == x['name']), None)
        if found_key is None:
            number_of_not_found += 1
            continue
        else:
            number_of_found += 1
        # progress.console.print(f'{entry[0]}, {found_key["name"]} ,{entry[2]}')
        if f'id_for_{TARGET_LOCALE}' in found_key:
            status = update_translation(ix,
                                        translation_id=found_key[f'id_for_{TARGET_LOCALE}'],
                                        branch=target_branch,
                                        content=entry[2])
            if status == 'added':
                number_of_translated += 1
            elif status == 'skipped':
                number_of_nottranslated += 1
        else:
            number_of_nottranslated += 1

        # if number_of_translated % 10 == 0:
        #     progress.console.print(f'Added: {number_of_translated}, skipped: {number_of_nottranslated}')


        if number_of_translated % 100 == 0:
            progress.console.print(
                f'Ok, we have {number_of_translated} added translations, and {number_of_nottranslated} was already done')

print(f'Found keys {number_of_found}, number of not found: {number_of_not_found}')
