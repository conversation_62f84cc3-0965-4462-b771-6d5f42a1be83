import hashlib
import json

import requests
from django.conf import settings
from django.utils.encoding import force_bytes

from mailing.enums import MailchimpListStatus
from regions.models import Region

MAILCHIMP_API_KEY = ''


list_id = settings.MAILCHIMP_T03_OTHER_COUNTRIES_LIST

email = '<EMAIL>'
subscriber_hash = hashlib.md5(force_bytes(email.lower())).hexdigest()

data = {
    'email_address': email,
    'language': 'en',
    'status': MailchimpListStatus.SUBSCRIBED.value,
}

# url = (
#     f'https://us10.api.mailchimp.com/3.0/lists/{list_id}/members/{subscriber_hash}'
# )
#
# response = requests.put(
#     url,
#     data=json.dumps(data),
#     auth=('apikey', settings.MAILCHIMP_API_KEY),
#     verify=True,
# )
# countries = Region.objects.all()
# country_list = list(set([x.name for x in countries if x.name != '_default']))

# countries interest group = a17f248735

INTEREST_COUNTRIES_GROUP = 'a17f248735'

countries = Region.objects.all()
country_list = list(set([x.name for x in countries if x.name != '_default']))

url = f'https://us10.api.mailchimp.com/3.0/lists/{list_id}/interest-categories/{INTEREST_COUNTRIES_GROUP}/interests?count=40'

# for country_entry in country_list:
#     response = requests.post(
#         url,
#         data=json.dumps({'name': country_entry, 'display_order': 0}),
#         auth=('apikey', MAILCHIMP_API_KEY),
#         verify=True,
#     )

response = requests.get(
    url,
    data=json.dumps({'name': 'nope', 'display_order': 0}),
    auth=('apikey', MAILCHIMP_API_KEY),
    verify=True,
)

all_interests = response.json()

country_interests_list = {}

for inter in all_interests['interests']:
    country_interests_list[inter['name']] = inter['id']

countries_interests = {
    'greece': '9941c63d87',
    '_other': '28890e7b65',
    'italy': 'ba58f3e538',
    'czech': 'd360b24f02',
    'denmark': 'd6c81bd838',
    'romania': '59d3678e3b',
    'austria': 'bc50b29d37',
    'belgium': '96a4b2bc51',
    'slovenia': '2d0af66ffd',
    'germany': 'b669622d53',
    'ireland': 'd0c6f795dc',
    'poland': '50a67da651',
    'switzerland': '15b65c788b',
    'finland': '59afcbba07',
    'netherlands': '6f61bf0d3f',
    'france': '2428efdfc1',
    'croatia': '81f5e36673',
    'estonia': '2e84f0bc00',
    'latvia': '229ed2cdce',
    'bulgaria': 'a7792a182c',
    'united_kingdom': 'cadf934760',
    'norway': 'c77cc146f1',
    'slovakia': '8e279af27c',
    'lithuania': 'f126202b3b',
    'luxembourg': 'ba777c00f7',
    'spain': '6caff4c749',
    'hungary': '70faf7fe38',
    'sweden': '6dbfa677f4',
    'portugal': '8f647fed1b',
}
