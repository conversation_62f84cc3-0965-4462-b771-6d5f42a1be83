from datetime import datetime
from decimal import Decimal

from django.contrib.auth import get_user_model

from custom.utils.exports import dump_list_as_csv
from regions.models import Region
from vouchers.enums import VoucherOrigin, VoucherType
from vouchers.models import (
    Voucher,
    VoucherRegionEntry,
)

User = get_user_model()


def create_region_entries(voucher):
    region_entries = []
    voucher_values = {
        'GBP': 4,
        'NOK': 54,
        'PLN': 17,
        'CHF': 6,
        'DKK': 36,
        'SEK': 51,
    }
    for currency_code, value in voucher_values.items():
        region = Region.objects.filter(currency__code=currency_code).first()
        region_entries.append(
            VoucherRegionEntry(
                voucher_id=voucher.id,
                region=region,
                value=Decimal(str(value)),
                amount_limit=Decimal('10000000')
            )
        )
    VoucherRegionEntry.objects.bulk_create(region_entries)


def create_vouchers(quantity):
    codes = []
    while quantity:
        code = Voucher.generate_code(
            inside_string='SPRC',
            inside_string_at_beginning=True,
            character_count=10,
        )
        voucher = Voucher.objects.create(
            code=code,
            value=Decimal('4'),
            kind_of=VoucherType.ABSOLUTE,
            quantity=1,
            quantity_left=1,
            start_date=datetime.now(),
            end_date=datetime(2023, 12, 31, 23, 59, 59),
            origin=VoucherOrigin.MAILING,
            notes='sample promo',
            creator=User.objects.get(username='admin'),
            amount_starts=Decimal('0'),
            amount_limit=Decimal('10000000'),
            item_conditionals={'exclude': [{'shelf_types': [0, 1, 2, 3, 4]}]},
        )
        create_region_entries(voucher)
        quantity -= 1
        codes.append(code)
    return codes


def send_codes_to_aleksander(codes):
    dump_list_as_csv(
        [codes],
        output='sample_promo_vouchers.csv',
        mail=('<EMAIL>',),
        mail_body='Sample promo vouchers',
        mail_subject='Sample promo vouchers',
    )


# Maybe do it in batches
# codes = create_vouchers(100000)
# send_codes_by_email(codes)
