from datetime import datetime
from decimal import Decimal

from django.contrib.auth import get_user_model

from custom.utils.exports import dump_list_as_txt
from vouchers.enums import VoucherType, VoucherOrigin
from vouchers.models import (
    Voucher,
    VoucherGroup,
)


User = get_user_model()
start_date = datetime(2022, 11, 7)
end_date = datetime(2022, 12, 5, 23, 59, 59)
prefix = 'b2bbf'
group_codes = []
voucher_codes = []
voucher_groups = []
vouchers = []
quantity = 1000

while quantity > 0:
    group_code = Voucher.generate_code(
        inside_string=prefix,
        inside_string_at_beginning=True,
        character_count=9,
    )
    if group_code in group_codes:
        continue
    voucher_group = VoucherGroup(code=group_code)
    voucher_groups.append(voucher_group)
    group_codes.append(group_code)
    for value, amount_starts, amount_limit in (
            ('33', '1', '10000'),
            ('35', '10000', '1000000'),
    ):
        code = Voucher.generate_code(
            inside_string='thr',
            inside_string_at_beginning=True,
            character_count=10,
        )
        while code in voucher_codes:
            code = Voucher.generate_code(
                inside_string='thr',
                inside_string_at_beginning=True,
                character_count=10,
            )
        voucher = Voucher(
            code=code,
            value=Decimal(value),
            kind_of=VoucherType.PERCENTAGE,
            quantity=1,
            quantity_left=1,
            start_date=start_date,
            end_date=end_date,
            group=voucher_group,
            origin=VoucherOrigin.MANUAL,
            notes='early bird bf',
            creator=User.objects.get(username='admin'),
            amount_starts=Decimal(amount_starts),
            amount_limit=Decimal(amount_limit),
        )
        vouchers.append(voucher)
        voucher_codes.append(code)
    quantity -= 1
VoucherGroup.objects.bulk_create(voucher_groups)
Voucher.objects.bulk_create(vouchers)

dump_list_as_txt(
    group_codes,
    output='black_friday_vouchers.txt',
    mail=('<EMAIL>',),
    mail_body='Black Friday vouchers',
    mail_subject='Black Friday vouchers',
)
