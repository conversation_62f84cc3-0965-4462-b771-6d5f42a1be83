from datetime import datetime
from decimal import Decimal

from django.contrib.auth import get_user_model

from vouchers.models import Voucher


User = get_user_model()


def exclude_wardrobes_and_samples():
    item_conditionals = {
        'exclude': [
            {'shelf_types': [3]},
            {'furniture_types': ['sample_box']},
        ]
    }
    return item_conditionals


def create_voucher(
    value,
    kind_of,
    end_date,
    origin,
    prefix='',
    quantity=1,
    quantity_left=1,
    start_date=datetime.today(),
    amount_starts=Decimal('0'),
    amount_limit=Decimal('10000000'),
    item_conditionals=None,
):
    code = Voucher.generate_code(
            inside_string=prefix,
            inside_string_at_beginning=True,
            character_count=8,
        )
    creator = User.objects.get(username='admin')
    voucher = Voucher(
        code=code,
        kind_of=kind_of,
        quantity=quantity,
        quantity_left=quantity_left,
        creator=creator,
        origin=origin,
        value=Decimal(str(value)),
        start_date=start_date,
        end_date=end_date,
        amount_starts=amount_starts,
        amount_limit=amount_limit,
        item_conditionals=item_conditionals,
    )
    return voucher
