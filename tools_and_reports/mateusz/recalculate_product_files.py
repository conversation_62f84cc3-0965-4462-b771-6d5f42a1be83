import datetime
from tqdm import tqdm

from producers.services import ProductBatchService
from producers.models import ProductBatch
from django.core.paginator import Paginator
from producers.production_system_utils.client import ProductionSystemClient


def recalculate_files(batch_ids=None):
    service = ProductBatchService()
    if batch_ids:
        query = ProductBatch.objects.filter(
            id__in=batch_ids
        ).order_by('created_at')
    else:
        query = ProductBatch.objects.filter(
            created_at__gte=datetime.datetime(2022, 1, 31)
        ).order_by('created_at')
    paginator = Paginator(query, 100)
    products = []
    batches = []
    with ProductionSystemClient(suppress_errors=False) as ps_client:
        with tqdm(total=query.count()) as pbar:
            for page in tqdm(paginator):
                for batch in page.object_list:
                    errors = False
                    for product in batch.batch_items.all():
                        try:
                            service._remove_old_files(product)
                            service._generate_files_for(product, ps_client=ps_client)
                        except Exception as e:
                            errors = True
                            products.append(product.id)
                            print('%s, %s', product.id, e)
                            break
                    if errors:
                        batches.append(batch.id)
                        continue
                    try:
                        batch.generate_all_files()
                    except Exception as e:
                        batches.append(batch.id)
                        print('%s, %s', batch.id, e)
                    pbar.update(1)
    print('product: ', products)
    print('batch: ', batches)
    return products
