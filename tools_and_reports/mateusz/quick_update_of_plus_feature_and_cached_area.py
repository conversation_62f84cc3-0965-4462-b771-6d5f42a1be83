from django.core.paginator import Paginator
from producers.models import Product


def update_all_products():
    products = Product.objects.filter(
        cached_product_type__in=Product.JETTY_WATTY, order_item__isnull=False
    ).select_related(
        'order_item', 'order_item__content_type', 'order'
    ).order_by('id')
    update_products_query(products)


def update_products_query(products_query):
    paginator = Paginator(products_query, 100)
    for page in range(1, paginator.num_pages + 1):
        for product in paginator.page(page).object_list:
            quick_update(product)


def quick_update(product):
    item_features = product.order_item.order_item.get_item_features(
        product.order.order_type,
    )
    product.has_plus_feature = 'plus_feature' in item_features
    product.cached_area = product.get_m2()
    product.save(update_fields=['has_plus_feature', 'cached_area'])
