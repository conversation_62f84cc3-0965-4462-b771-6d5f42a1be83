from producers.choices import ProductPriority, DeliveryPriority, SourcePriority
from producers.models import Product


def migrate_source_priority():
    Product.objects.filter(priority=ProductPriority.VIP.value).update(
        source_priority=SourcePriority.VIP.value
    )
    Product.objects.filter(priority=ProductPriority.B2B.value).update(
        source_priority=SourcePriority.B2B.value
    )
    Product.objects.filter(priority=ProductPriority.INFLU.value).update(
        source_priority=SourcePriority.INFLU.value
    )
    Product.objects.filter(priority=ProductPriority.R_AND_D.value).update(
        source_priority=SourcePriority.R_AND_D.value
    )
    Product.objects.filter(priority=ProductPriority.BIG_ORDERS.value).update(
        source_priority=SourcePriority.BIG_ORDER.value
    )


def migrate_delivery_priority():
    products = Product.objects.filter(priority=ProductPriority.ASSEMBLY)
    products.update(delivery_priority=DeliveryPriority.ASSEMBLY.value)
