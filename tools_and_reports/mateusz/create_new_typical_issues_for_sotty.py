from complaints.models import TypicalIssues


def create_new_typical_issues_for_sotty():
    issues = [
        "manufacturing defect",
        "damage",
        "incomplete product",
        "order fulfillment error",
        "malfunction",
        "material defects",
        "improper use",
        "additional element",
        "other"
    ]
    for issue in issues:
        TypicalIssues.objects.get_or_create(
            name=issue,
            is_sotty_issue=True
        )
