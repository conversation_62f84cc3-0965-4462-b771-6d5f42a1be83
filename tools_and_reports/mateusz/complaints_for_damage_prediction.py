import csv
import io

from django.contrib.admin.templatetags.admin_list import pagination
from django.core.paginator import Paginator
from rest_framework import serializers

from complaints.models import Complaint
from custom.utils.report_file import ReportFile
from producers.models import Product


class ComplaintSerializer(serializers.ModelSerializer):
    complaint_id = serializers.IntegerField(source='id')
    responsibility = serializers.CharField(source='responsibility.name')
    product_id = serializers.IntegerField()
    created_at = serializers.DateTimeField()



    class Meta:
        model = Complaint
        fields = [
            'complaint_id',
            'responsibility',
            'product_id',
            'created_at',
        ]



def get_queryset():
    # get only with courirer responsibility from 2023
    return Complaint.objects.filter(
        created_at__year__gte=2022,
        responsibility__name__in=[
            'TNT',
            'DT',
            'UPS',
            'DPD',
            'weak packaging',
            'ZADBANO',
        ],
    )


def get_elements(complaint: Complaint):
    results = []
    if complaint.elements and isinstance(complaint.elements, dict):
        features = complaint.elements.get('features', [])
        elements = complaint.elements.get('elements', [])
        other = complaint.elements.get('other', [])
        for element in features + elements + other:
            try:
                pack_id = int(element.split(':')[1])
                name = element.split(' - ')[0]
            except Exception:
                print(complaint.id, element)
                continue
            results.append({'element_name': name, 'pack_id': pack_id})
        fitting = complaint.elements.get('fitting', [])
        for element in fitting:
            results.append({'element_name': element, 'pack_id': None})
    return results


def get_complaint_data():
    complaints = get_queryset()
    complaints_data = []
    # add pagination
    paginator = Paginator(complaints, 1000)
    for page in paginator:
        for complaint in page:
            complaint_data = ComplaintSerializer(complaint).data
            for element in get_elements(complaint):
                complaint_data.update(**element)
                complaints_data.append(complaint_data.copy())
    return complaints_data



def send_csv(complaints_data, email):
    keys = complaints_data[0].keys()
    stream = io.StringIO()
    writer = csv.DictWriter(stream, fieldnames=keys)
    writer.writeheader()
    writer.writerows(complaints_data)
    output_bytes = stream.getvalue().encode()
    report_file = ReportFile(name='complaints_2024.csv', content=output_bytes)
    report_file.send_as_email_attachment([email], subject='Complaints 2024')




# packaging_elements, but only product_id and lo_id, because other fields are not used


def get_packaging_elements():
    products = Product.objects.filter(created_at__year=2024).order_by('id')
    results = []
    paginator = Paginator(products, 1000)
    for page in paginator:
        for product in page:
            results.append([product.id, product.logistic_order])
    headers = ['product_id', 'lo_id']
    report = ReportFile.load_list_as_csv_file(
        results,
        headers=headers,
        file_name='packaging_elements_2024.csv',
    )
    report.send_as_email_attachment(
        ['<EMAIL>'], subject='Packaging elements 2024'
    )
