from customer_service.models import CSCorrectionRequest, CSCorrectionRequestStatus
from orders.models import Order
from orders.switch_status import SwitchStatus
from pricing_v3.services.price_calculators import \
    OrderPriceCalculatorForPriceUpdatedAtPricing
from producers.choices import ProductPriority
from producers.internal_api.events import ProductDeletedEvent, ProductRefreshEvent, \
    ProductCreatedEvent
from producers.models import Product


def revert_switch(order):
    remove_correction_request(order)
    remove_target_product(order)
    revert_changes_in_source_product(order)
    rollback_item_replacement(order)
    Order.objects.filter(id=order.id).update(switch_status=SwitchStatus.BLANK.value)


def remove_target_product(order):
    target_product = order.target_order_item.product_set.first()
    if not target_product:
        return
    if target_product.batch:
        raise Exception('Target product already batched')
    target_product.delete()
    ProductDeletedEvent(target_product)


def remove_correction_request(order):
    if CSCorrectionRequest.objects.filter(invoice__order=order).count() > 1:
        print('To many corrections')
        raise Exception('To many corrections')
    if CSCorrectionRequest.objects.filter(invoice__order=order).count() == 1:
        correction_request = CSCorrectionRequest.objects.get(invoice__order=order)
        if correction_request.status != CSCorrectionRequestStatus.STATUS_NEW:
            print('Correction processed')
            raise Exception('Correction processed')
        correction_request.correction_invoice.delete()
        correction_request.delete()
        print('Correction request removed')


def revert_changes_in_source_product(order):
    source_product = Product.all_objects.get(order_item=order.source_order_item)
    if source_product.deleted:
        source_product.deleted = None
        order_type = order.source_order_item.get_furniture_type()
        logistic_order = order.get_logistic_order_by_order_type(order_type)
        source_product.logistic_order = logistic_order.id
        source_product.save()
        ProductCreatedEvent(source_product)
    source_product.priority_updater.change_priority(ProductPriority.NORMAL)
    ProductRefreshEvent(source_product)


def rollback_item_replacement(order):
    order._delete_order_item(order.target_order_item)
    order.target_order_item = None

    order._undelete_order_item(order.source_order_item)

    order.source_total_price = None
    order.source_total_price_net = None
    order.source_region_total_price = None
    order.source_region_total_price_net = None
    calculator = OrderPriceCalculatorForPriceUpdatedAtPricing(order)
    calculator.calculate(recalculate_items=False)
    return calculator.message_status, calculator.message_text
