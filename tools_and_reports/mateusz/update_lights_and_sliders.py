from logistic.models import LogisticOrder
from producers.internal_api.serializers import ForLogisticProductSerializer
from producers.models import Product


def update():
    queryset = Product.objects.filter(created_at__year=2023, created_at__month__gte=5)
    for product in queryset:
        sliders = product.get_sliders()
        has_light = product.has_lighting
        if has_light or sliders:
            try:
                logistic_order = LogisticOrder.objects.get(id=product.logistic_order)
                logistic_order.set_product_cache(
                    ForLogisticProductSerializer(product).data
                )
            except LogisticOrder.DoesNotExist as e:
                print(f'{logistic_order.id} {e}')
