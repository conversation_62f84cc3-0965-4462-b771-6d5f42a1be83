from complaints.models import Complaint
from complaints.reproduction_days_elements import get_elements_categories


def update_categories():
    complaints = Complaint.objects.all()
    for complaint in complaints:
        categories = get_elements_categories(complaint.elements)
        complaint.reproduction_element_categories = categories
        complaint.save(update_fields=['reproduction_element_categories'])
