import requests

from time import sleep

from django.core.paginator import Paginator

from custom.enums import ShelfType
from dixa.api_client.client import DixaConversationApi, DixaApiClient
from orders.models import Order


def get_carriers(order):
    return ', '.join([lo.carrier for lo in order.logistic_info if lo.carrier])


def get_delivered_time(order):
    dates = [lo.delivered_date for lo in order.logistic_info if lo.delivered_date]
    if dates:
        date = max([lo.delivered_date for lo in order.logistic_info if lo.delivered_date])
        return date.strftime('%Y-%m-%d')
    return ''


def get_man(order):
    return ', '.join(p.batch.manufactor.name for p in order.product_set.all() if p.batch)


class DixaWithGet(DixaConversationApi):
    def get_tags(self, detail_id: str):
        endpoint = f'{self.ENDPOINT}/{detail_id}/tags'
        url = self.get_url(endpoint)
        return self.process_get_request(url)


def get_color(products):
    results = []
    for product in products:
        colors = ShelfType.from_production_code(product.cached_shelf_type).colors
        for color_enum in colors:
            if color_enum.hex == product.cached_material_color:
                results.append(color_enum.name)
    return ', '.join(results) if results else '-'

def get_shelf_types(products):
    results = []
    for product in products:
        results.append(product.cached_shelf_type)
    return ', '.join(results) if results else '-'


from dixa.models import DixaWebHookEvent


def get_report_with_tags_and_courier():
    events = DixaWebHookEvent.objects.filter(created_at__year=2025, created_at__month__gte=3)
    client = DixaWithGet()
    results = ['Order ID', 'Email', 'Carriers', 'Tags', 'Conversation started']
    paginator = Paginator(events, 1000)
    for page in paginator:
        for event in page:
            if not Order.objects.filter_with_proxy_by_email(email=event.requester_email).exists():
                continue
            try:
                tags = client.get_tags(event.conversation_id)
            except Exception as e:
                print(f'{event.conversation_id} Error:', e)
                sleep(10)
                continue
            for order in Order.objects.filter_with_proxy_by_email(email=event.requester_email).prefetch_related('product_set'):
                tags_str = ', '.join([t['name'] for t in tags])
                try:
                    results.append(
                        [
                            order.id,
                            order.email,
                            get_carriers(order),
                            tags_str,
                            event.created_at.strftime('%Y-%m-%d') if event.created_at else '-',
                            get_man(order),
                            order.country,
                            order.cached_items_type,
                            get_shelf_types(order.product_set.all()),
                            get_color(order.product_set.all()),
                        ])
                except Exception as e:
                    print(f'Error with order {order.id}. Error:', e)
                    continue
            sleep(0.1)
    return results
