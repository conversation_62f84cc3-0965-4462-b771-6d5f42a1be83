import json

from datetime import datetime
from urllib.parse import (
    parse_qs,
    urlparse,
)

import pytz

from rest_framework import serializers

from intercom_data.models import (
    IntercomConversation,
    IntercomConversationPart,
    IntercomUser,
)


def get_agent_id(user: IntercomUser):
    author_id = user.dixa_id
    if not author_id:
        admin_dixa_id = '36e9e09d-a32d-4560-880e-854b6cbfb3c0'
        return admin_dixa_id
    return author_id


class IntercomUserToDixa(serializers.ModelSerializer):
    displayName = serializers.CharField(source='name')
    firstName = serializers.CharField(source='name')
    email = serializers.EmailField()
    phoneNumber = serializers.SerializerMethodField()

    def get_phoneNumber(self, instance):
        raw_data = json.loads(instance.json)
        return raw_data.get('phone')

    class Meta:
        model = IntercomUser
        fields = ('displayName', 'firstName', 'email', 'phoneNumber')


class IntercomConversationToDixaConversation(serializers.ModelSerializer):
    emailIntegrationId = serializers.ReadOnlyField(default='<EMAIL>')
    _type = serializers.ReadOnlyField(default='Email')
    language = serializers.SerializerMethodField()
    requesterId = serializers.SerializerMethodField()
    message = serializers.SerializerMethodField()
    subject = serializers.SerializerMethodField()

    def get_subject(self, instance):
        raw_data = json.loads(instance.json)
        first_message = raw_data['conversation_message']
        subject = first_message.get('subject') or ''
        subject = subject.replace('<p>', '').replace('</p>', '').strip()
        return subject

    def get_message(self, instance):
        raw_data = json.loads(instance.json)
        first_message = raw_data['conversation_message']
        content = {'value': first_message['body'], '_type': 'Html'}
        attachments = []
        for attachment in first_message['attachments']:
            if datetime.now() > self.get_expire_date(attachment['url']):
                continue
            attachments.append(
                {
                    'url': attachment['url'],
                    'prettyName': attachment['name'],
                }
            )
        dixa_message = {
            'content': content,
            'attachments': attachments,
            '_type': 'Inbound',
        }
        return dixa_message

    @staticmethod
    def get_expire_date(url):
        parsed_url = urlparse(url)
        params = parse_qs(parsed_url.query)
        ts = params.get('expires', [None])[0]
        return datetime.fromtimestamp(int(ts)) if ts else datetime(2000, 1, 1)

    @staticmethod
    def is_outbound(email):
        return email.endswith('tylko.com')

    def get_requesterId(self, instance):
        if (
            instance.message_author.email
            and not instance.message_author.email.endswith('tylko.com')
        ):
            return instance.message_author.dixa_id
        if instance.user.email and not instance.user.email.endswith('tylko.com'):
            return instance.user.dixa_id
        if instance.customers and not instance.customers.first().email.endswith(
            'tylko.com'
        ):
            return instance.customers[0].dixa_id
        return instance.message_author.dixa_id

    def get_language(self, instance):
        language = instance.language or 'english'
        lang_to_code = {
            'english': 'en',
            'german': 'de',
            'french': 'fr',
            'spanish': 'es',
            'dutch': 'nl',
        }
        return lang_to_code.get(language, 'en')

    class Meta:
        model = IntercomConversation
        fields = (
            'emailIntegrationId',
            'language',
            'message',
            'subject',
            'requesterId',
            '_type',
        )


class IntercomConversationPartToDixaNote(serializers.ModelSerializer):
    createdAt = serializers.SerializerMethodField()
    message = serializers.CharField(source='body')
    agentId = serializers.SerializerMethodField()

    def get_agentId(self, instance):
        return get_agent_id(instance.author)

    def get_createdAt(self, instance):
        return pytz.utc.localize(datetime.now()).strftime('%Y-%m-%dT%H:%M:%S.000Z[%Z]')

    class Meta:
        model = IntercomConversationPart
        fields = ('createdAt', 'message', 'agentId')


class IntercomConversationPartToDixaMessage(serializers.ModelSerializer):

    content = serializers.SerializerMethodField()
    attachments = serializers.SerializerMethodField()
    _type = serializers.SerializerMethodField()

    def get_content(self, instance):
        return {'value': instance.body, '_type': 'Html'}

    def get_attachments(self, instance):
        raw_message = json.loads(instance.json)
        attachments = []
        for attachment in raw_message['attachments']:
            attachments.append(
                {
                    'url': attachment['url'],
                    'prettyName': attachment['name'],
                }
            )

    class Meta:
        model = IntercomConversationPart
        fields = ('content', 'attachments', '_type')


class IntercomConversationPartToDixaInboundMessage(
    IntercomConversationPartToDixaMessage
):
    _type = serializers.ReadOnlyField(default='Inbound')


class IntercomConversationPartToDixaOutboundMessage(
    IntercomConversationPartToDixaMessage
):
    _type = serializers.ReadOnlyField(default='Outbound')
    agentId = serializers.SerializerMethodField()

    def get_agentId(self, instance):
        return get_agent_id(instance.author)

    class Meta:
        model = IntercomConversationPart
        fields = ('content', 'attachments', '_type', 'agentId')
