import logging

from time import sleep
from typing import Union

from django.core.paginator import Paginator
from django.db.models import (
    Q,
    QuerySet,
)

from requests import HTTPError
from tqdm import tqdm

from dixa.api_client.manager import DixaManager
from intercom_data.models import (
    IntercomConversation,
    IntercomUser,
)

from . import serializers

logger = logging.getLogger('cstm')


def migrate_instance(instance, serializer_class, migrate_function, parent_id=None):
    if instance.dixa_id:
        return
    serializer = serializer_class()
    payload = serializer.to_representation(instance)
    try:
        args = [parent_id, payload] if parent_id else [payload]
        dixa_instance = migrate_function(*args)
    except HTTPError as e:
        logger.warning(payload)
        logger.warning(
            '%s %s migration error: %s',
            instance._meta.object_name,
            instance.id,
            e.response.text,
        )
        return
    instance.dixa_id = (
        dixa_instance['id'] if 'id' in dixa_instance else dixa_instance['messageId']
    )
    instance.save(update_fields=['dixa_id'])


class MigrateUsersToDixa:
    def __init__(self, user_queryset: Union[QuerySet, IntercomUser]):
        self.users = user_queryset.exclude(email__contains='tylko.com').distinct()
        self.manager = DixaManager()

    def migrate_users(self):
        paginator = Paginator(self.users.order_by('id'), per_page=1000)
        for intercom_users in tqdm(paginator, total=paginator.num_pages):
            for user in intercom_users:
                self.migrate_user(user)

    def migrate_user(self, user: IntercomUser):
        migrate_instance(
            user,
            serializers.IntercomUserToDixa,
            self.manager.get_or_create_user,
        )


class SetAgentIdOnIntercomUsers:
    def __init__(self):
        self.manager = DixaManager()

    def update_tylko_users(self) -> None:
        intercom_tylko_users = self.get_tylko_intercom_users()
        dixa_agents_dict = self.get_dixa_agents_dict()
        for user in intercom_tylko_users:
            self.set_dixa_id(user, dixa_agents_dict.get(user.email, ''))

    def get_dixa_agents_dict(self) -> dict:
        agents = self.manager.get_agents()
        return {agent['email']: agent['id'] for agent in agents}

    @classmethod
    def get_tylko_intercom_users(cls) -> Union[list, IntercomUser]:
        return IntercomUser.objects.filter(email__endswith='tylko.com')

    @classmethod
    def set_dixa_id(cls, user: IntercomUser, dixa_id: str) -> None:
        if not dixa_id:
            return
        user.dixa_id = dixa_id
        user.save(update_fields=['dixa_id'])

class MigrateUserConversationToDixa:
    def __init__(self):
        self.manager = DixaManager()

    def migrate_conversations(self, user: IntercomUser) -> None:
        conversations = IntercomConversation.objects.filter(
            Q(user=user) | Q(customers=user) | Q(message_author=user)
        ).order_by('created_at')
        for conversation in conversations:
            self.create_conversation(conversation)

    def create_conversation(self, conversation: IntercomConversation) -> None:
        migrate_instance(
            conversation,
            serializers.IntercomConversationToDixaConversation,
            self.manager.create_conversation,
        )
        self.migrate_conversation_parts(conversation)
        self.set_conversation_status(conversation)

    def migrate_conversation_parts(self, conversation: IntercomConversation) -> None:
        if not conversation.dixa_id:
            return
        parts = (
            conversation.parts.exclude(body=None)
            .exclude(body='')
            .order_by('created_at')
        )
        for part in parts:
            if 'note' in part.part_type:
                self.create_note(part, conversation.dixa_id)
            else:
                self.create_message(part, conversation.dixa_id)
            sleep(1)

    def create_note(self, part, conversation_dixa_id) -> None:
        migrate_instance(
            part,
            serializers.IntercomConversationPartToDixaNote,
            self.manager.create_note,
            parent_id=conversation_dixa_id,
        )

    def create_message(self, part, conversation_dixa_id) -> None:
        try:
            part.author
        except IntercomUser.DoesNotExist:
            logger.warning('User with %s not founded', part.author_id)
            self.create_note(part, conversation_dixa_id)
            return
        if part.author.is_tylko_user:
            # sending outbound message sends email to customer
            self.create_note(part, conversation_dixa_id)
        else:
            self.create_inbound_message(part, conversation_dixa_id)

    def create_inbound_message(self, part, conversation_dixa_id) -> None:
        migrate_instance(
            part,
            serializers.IntercomConversationPartToDixaInboundMessage,
            self.manager.create_message,
            parent_id=conversation_dixa_id,
        )

    def create_outbound_message(self, part, conversation_dixa_id) -> None:
        migrate_instance(
            part,
            serializers.IntercomConversationPartToDixaOutboundMessage,
            self.manager.create_message,
            parent_id=conversation_dixa_id,
        )

    def set_conversation_status(self, conversation: IntercomConversation) -> None:
        if conversation.state == conversation.CLOSED:
            try:
                self.manager.close_conversation(conversation.dixa_id)
            except HTTPError as e:
                logger.warning(
                    'Conversation %s setting status error: %',
                    conversation.id,
                    e.response.text,
                )
                return
