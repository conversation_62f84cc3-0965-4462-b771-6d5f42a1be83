'''
First go to BigQuery and run the following query:
SELECT
  codename,
  manufacturer_name,
  tylko_price,
  SUM(tylko_quantity) as tylko_quantity,
  SUM(tylko_material_consumption_with_losses) as tylko_material_consumption_with_losses,
  SUM(tylko_cost_with_losses) as tylko_cost_with_losses,
  factory_price,
  SUM(factory_quantity) as factory_quantity,
  SUM(factory_material_consumption_with_losses) as factory_material_consumption_with_losses,
  SUM(factory_cost_with_losses) factory_cost_with_losses,
  SUM(factory_total_cost) factory_total_cost,
  SUM(summary_total_cost) summary_total_cost,
FROM `tylko-bi-200712.production_info.production_cashflow`
WHERE
         elements_order_id <= <last_id_from_given_year>
     AND elements_order_id >= <first_id_from_given_year>
GROUP BY
     codename, tylko_price, factory_price, manufacturer_name, factory_total_cost >= 0
ORDER BY codename
download the results as production_data.csv, upload to production env
 and run the following code:
'''
import io

import pandas as pd
from django.db.models import Q

from custom.utils.report_file import ReportFile
from production_margins.models import PricingFactorItem, CustomPricingFactor

df = pd.read_csv('production_data.csv')

def get_weight_unit_and_manufactor_code(row):
    try:
        pfi = PricingFactorItem.objects.get(codename=row['codename'])
        results = [pfi.weight, pfi.get_measurement_unit_display()]
    except:
        results = ['no_pfi', 'no_pfi']

    if pd.isna(row['factory_price']):
        results.append('no_factory_price')
        return results
    cpfs = CustomPricingFactor.objects.filter(
        manufactor__name=row['manufacturer_name'],
        price= row['factory_price'],
        pricing_factor_item=pfi,
    ).filter(
        Q(date_to=None) | Q(date_to__year__gte=2024),
        Q(date_from__year__gte=2024)
    ).select_related('manufacturer_code').order_by('manufacturer_code__code').distinct('manufacturer_code__code')
    if cpfs.count() > 1:
        results.append(', '.join(list(cpfs.values_list('manufacturer_code__code', flat=True))))
    elif cpfs.count() == 0:
        results.append('no_cpf')
    else:
        cpf = cpfs.first()
        results.append(cpf.manufacturer_code.code if cpf.manufacturer_code else '')
    return pd.Series(results)


df[['weight', 'unit', 'manufacturer_code']] = df.apply(
    get_weight_unit_and_manufactor_code, axis=1
)

buffer = io.BytesIO()
df.to_csv(buffer, index=False)
buffer.seek(0)
ReportFile(
    name='production_data_with_weight_unit_and_manufacturer_code.csv',
    content=buffer.read()
).send_as_email_attachment([<email>])
