#!/bin/bash


# set credential

if [[ -z "${AWS_ACCESS_KEY_ID}" ]]; then
  echo "Set AWS_ACCESS_KEY_ID"
  exit
fi
if [[ -z "${AWS_SECRET_ACCESS_KEY}" ]]; then
  echo "Set AWS_SECRET_ACCESS_KEY"
  exit
fi
if [[ -z "${AWS_S3_REGION_NAME}" ]]; then
  echo "Set AWS_S3_REGION_NAME"
  exit
fi

aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
aws configure set region $AWS_S3_REGION_NAME

# sync catalogs

if [[ -z "${AWS_S3_MEDIA_BUCKET_NAME}" ]]; then
  echo "Set AWS_S3_MEDIA_BUCKET_NAME"
  exit
fi

if [[ -z "${MEDIA_DIR}" ]]; then
  echo "Set MEDIA_DIR, example: /home/<USER>/app/media  (without slash at the end)"
  exit
fi


#340K	checkout
aws s3 sync $MEDIA_DIR/checkout s3://$AWS_S3_MEDIA_BUCKET_NAME/media/checkout

#569M	custom_audiences
aws s3 sync $MEDIA_DIR/custom_audiences/custom_audience_batch/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/custom_audiences/custom_audience_batch/2021/09

#312G	gallery
#46M	gallery/custom_dna
aws s3 sync $MEDIA_DIR/gallery/custom_dna s3://$AWS_S3_MEDIA_BUCKET_NAME/media/gallery/custom_dna
#968M	gallery/furniture_abstract
aws s3 sync $MEDIA_DIR/gallery/furniture_abstract/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/gallery/furniture_abstract/2021/09
#795M	gallery/furniture_grid_image
aws s3 sync $MEDIA_DIR/gallery/furniture_grid_image/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/gallery/furniture_grid_image/2021/09
#13G	gallery/furniture_image
aws s3 sync $MEDIA_DIR/gallery/furniture_image/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/gallery/furniture_image/2021/09
#297G	gallery/sellable_item_abstract
aws s3 sync $MEDIA_DIR/gallery/sellable_item_abstract/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/gallery/sellable_item_abstract/2021/09

#16G	invoice
aws s3 sync $MEDIA_DIR/invoice/invoice/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/invoice/invoice/2021/09

#139M	items_for_render
aws s3 sync $MEDIA_DIR/items_for_render s3://$AWS_S3_MEDIA_BUCKET_NAME/media/items_for_render

#5.8G	logistic
#678M	logistic/additional_delivery_tnt_confirmation - last from 04.2021
aws s3 sync $MEDIA_DIR/logistic/additional_delivery_tnt_confirmation/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic/additional_delivery_tnt_confirmation/2021/09
#logistic/assembly_service_request
aws s3 sync $MEDIA_DIR/logistic/assembly_service_request s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic/assembly_service_request
#679M	logistic/cmr_document
aws s3 sync $MEDIA_DIR/logistic/cmr_document/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic/cmr_document/2021/09
#1.8G	logistic/consignment_group
aws s3 sync $MEDIA_DIR/logistic/consignment_group/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic/consignment_group/2021/09
#95M	logistic/dedicated_transport_label
aws s3 sync $MEDIA_DIR/logistic/dedicated_transport_label s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic/dedicated_transport_label
#64K	logistic/labels
aws s3 sync $MEDIA_DIR/logistic/labels s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic/labels
#2.5G	logistic/logistic_order
aws s3 sync $MEDIA_DIR/logistic/logistic_order/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/logistic/logistic_order/2021/09

#165G	producers
#23G	producers/batch
#4.4G	producers/batch/horizontal_pdfs_zip
aws s3 sync $MEDIA_DIR/producers/batch/horizontal_pdfs_zip/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/horizontal_pdfs_zip/2021/09
#5.2G	producers/batch/labels_elements
aws s3 sync $MEDIA_DIR/producers/batch/labels_elements/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/labels_elements/2021/09
#363M	producers/batch/labels_logistic
aws s3 sync $MEDIA_DIR/producers/batch/labels_logistic s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/labels_logistic
#4.1G	producers/batch/labels_packaging
aws s3 sync $MEDIA_DIR/producers/batch/labels_packaging/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/labels_packaging/2021/09
#190M	producers/batch/labels_verticals
aws s3 sync $MEDIA_DIR/producers/batch/labels_verticals s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/labels_verticals
#8.2G	producers/batch/meblepl_zip
aws s3 sync $MEDIA_DIR/producers/batch/meblepl_zip/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/meblepl_zip/2021/09
#224M	producers/batch/nesting
aws s3 sync $MEDIA_DIR/producers/batch/nesting s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting
#37M	producers/batch/nesting_backs
aws s3 sync $MEDIA_DIR/producers/batch/nesting_backs s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_backs
#1.2M	producers/batch/nesting_bar
aws s3 sync $MEDIA_DIR/producers/batch/nesting_bar s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_bar
#13M	producers/batch/nesting_bottom_drawers
aws s3 sync $MEDIA_DIR/producers/batch/nesting_bottom_drawers s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_bottom_drawers
#68M	producers/batch/nesting_drawers
aws s3 sync $MEDIA_DIR/producers/batch/nesting_drawers s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_drawers
#1.2M	producers/batch/nesting_drawer_synchro
aws s3 sync $MEDIA_DIR/producers/batch/nesting_drawer_synchro s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_drawer_synchro
#73M	producers/batch/nesting_front_drawers
aws s3 sync $MEDIA_DIR/producers/batch/nesting_front_drawers s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_front_drawers
#1.2M	producers/batch/nesting_mask
aws s3 sync $MEDIA_DIR/producers/batch/nesting_mask s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_mask
#47M	producers/batch/nesting_zip
aws s3 sync $MEDIA_DIR/producers/batch/nesting_zip s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/nesting_zip
#164M	producers/batch/packaging_csv
aws s3 sync $MEDIA_DIR/producers/batch/packaging_csv s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/packaging_csv
#1.2M	producers/batch/packaging_csvs
aws s3 sync $MEDIA_DIR/producers/batch/packaging_csvs s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/batch/packaging_csvs

#142G	producers/product
#4.5G	producers/product/cnc_connections
aws s3 sync $MEDIA_DIR/producers/product/cnc_connections/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/cnc_connections/2021/09
#24G	producers/product/cnc_connections_zip
aws s3 sync $MEDIA_DIR/producers/product/cnc_connections_zip/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/cnc_connections_zip/2021/09
#20G	producers/product/front_view
aws s3 sync $MEDIA_DIR/producers/product/front_view/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/front_view/2021/09
#1.2G	producers/product/front_view_svg
aws s3 sync $MEDIA_DIR/producers/product/front_view_svg/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/front_view_svg/2021/09
#5.3G	producers/product/horizontals_pdf
aws s3 sync $MEDIA_DIR/producers/product/horizontals_pdf/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/horizontals_pdf/2021/09
#80G	producers/product/instruction
aws s3 sync $MEDIA_DIR/producers/product/instruction/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/instruction/2021/09
#8.3G	producers/product/packaging_instruction
aws s3 sync $MEDIA_DIR/producers/product/packaging_instruction/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/packaging_instruction/2021/09
#187M	producers/product/production_drawings
aws s3 sync $MEDIA_DIR/producers/product/production_drawings s3://$AWS_S3_MEDIA_BUCKET_NAME/media/producers/product/production_drawings

#107G	product_feeds
#105G	product_feeds/feed_item_image
aws s3 sync $MEDIA_DIR/product_feeds/feed_item_image/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/product_feeds/feed_item_image/2021/09
#1.9G	product_feeds/feed_variant
aws s3 sync $MEDIA_DIR/product_feeds/feed_variant/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/product_feeds/feed_variant/2021/09

#182M	production_margins
aws s3 sync $MEDIA_DIR/production_margins s3://$AWS_S3_MEDIA_BUCKET_NAME/media/production_margins

#40M	promotions
aws s3 sync $MEDIA_DIR/promotions s3://$AWS_S3_MEDIA_BUCKET_NAME/media/promotions

#7.7G	reviews
aws s3 sync $MEDIA_DIR/reviews/review_photos/2021/09 s3://$AWS_S3_MEDIA_BUCKET_NAME/media/reviews/review_photos/2021/09

#9.7M	warehouse
aws s3 sync $MEDIA_DIR/warehouse s3://$AWS_S3_MEDIA_BUCKET_NAME/media/warehouse
