from __future__ import print_function
from collections import defaultdict
import json
from gallery.models import Jetty
import pandas as pd
import itertools


def calculate_backpanel_size(vertical_coordinates):
    vertical_df = pd.DataFrame(vertical_coordinates)
    vertical_df.sort_values(by=['y1', 'x2'], inplace=True)
    vertical_df['next_x'] = vertical_df.groupby('y1').x2.shift(-1)
    vertical_df['width'] = vertical_df.next_x - vertical_df.x2
    vertical_df.dropna(axis=0, inplace=True)
    return vertical_df


def check_if_openings_are_connected(possible_errors, horizontals):
    horizontal_df = pd.DataFrame(horizontals)
    horizontal_df['x1'] = horizontal_df.x1 + 9
    horizontal_df['x2'] = horizontal_df.x2 - 9
    horizontal_df['next_x'] = horizontal_df.groupby('y1').x1.shift(-1)
    horizontal_spaces = horizontal_df.dropna()[['x2', 'next_x', 'y2']].rename(columns={'x2': 'x1'})

    horizontal_maybe_spaces = possible_errors.groupby(by=['x1', 'next_x'])['y2'].min() + 9
    horizontal_maybe_spaces = horizontal_maybe_spaces.reset_index()

    connected_openings = pd.merge(horizontal_spaces, horizontal_maybe_spaces, on=['x1', 'next_x', 'y2'], how='inner')
    return len(connected_openings) == len(possible_errors) / 2


def check_problematic_params(jetty, width, density, jetty_type):
    elements = jetty.recalculate_with_actual_dna(
        save=False,
        shelf_type=jetty_type,
        shelf_pattern=2,
        row_heights=[300] * 10,
        row_amount=10,
        backpanels_rows=list(range(11)),
        row_styles=["11"] * 11,
        property1=density,
        width_mm=width,
    )
    backpanel_sizes = calculate_backpanel_size(elements['verticals'])
    erroneous_entries = backpanel_sizes.loc[backpanel_sizes['width'] <= 149]
    # openings can be only 1 on 2 rows, so if the number of entries is odd, there is an error for sure
    if erroneous_entries.empty:
        return False
    if len(erroneous_entries) % 2 != 0:
        return True
    return not check_if_openings_are_connected(erroneous_entries, elements['horizontals'])


def group_tuples_by_density(parameter_tuples):
    problematic_groups = defaultdict(set)
    for width, density in parameter_tuples:
        problematic_groups[width].add(density)

    return problematic_groups


def create_mapping(groups):
    density_mapping = defaultdict(dict)
    for width, densities in groups.iteritems():
        available_densities = set(range(101)) - densities
        for density in densities:
            next_available_density = min(available_densities, key=lambda x: abs(x - density))
            density_mapping[width][density] = next_available_density
    return density_mapping


def compute_parameters_for_jetty_type(jetty, jetty_type, min_width=700, max_width=4500):
    problematic_parameters = []
    for density, width in itertools.product(list(range(101)), list(range(min_width, max_width + 1, 10))):
        print(('Density: {}, width: {}'.format(density, width)))
        if check_problematic_params(jetty, width, density, jetty_type):
            problematic_parameters.append((width, density))
    return problematic_parameters


for jetty_type in [0, 1]:
    jetty = Jetty.objects.filter(pattern=2, shelf_type=jetty_type).first()
    parameters = compute_parameters_for_jetty_type(jetty, jetty_type)
    problematic_groups = group_tuples_by_density(parameters)
    mapping = create_mapping(problematic_groups)
    file_name = 'mapping_type01.json' if jetty_type == 0 else 'mapping_type02.json'

    with open(file_name, 'w') as f:
        json.dump(mapping, f)


def verify_if_no_regression():
    with open('mapping_type02.json') as f:
        new_mapping = json.load(f)

    with open('../frontend_src/src_webgl/ivy/mapping_type02.json') as f:
        old_mapping = json.load(f)

    regression = set(old_mapping.keys()) - set(new_mapping.keys())
    if regression:
        print(regression)
        return

    for k in old_mapping.keys():
        if set(old_mapping[k]) - set(new_mapping[k]):
            print(set(old_mapping[k]) - set(new_mapping[k]))
    
