from gallery.models import Jetty


j1 = Jetty.objects.get(id=2237833)
j2 = Jetty.objects.get(id=2237832)

excluded_fields = [
    'id',
    'preview',
    'preview_webp',
    'furniture_status',
    'created_at',
    'updated_at',
    'preset',
    'created_platform',
    'description',
]

def compare_fields(f1, f2):
    if type(f1) != type(f2):
        return False
    if isinstance(f1, dict):
        for elem in f1.keys():
            if f1.get(elem) != f2.get(elem):
                return False
    elif isinstance(f1, list):
        if len(f1) != len(f2):
            return False
        for elem in f1:
            if elem not in f2:
                return False
    else:
        return f1 == f2
    return True


for field in Jetty._meta.fields:
    if field.name not in excluded_fields:
        field1 = getattr(j1, field.name)
        field2 = getattr(j2, field.name)
        if field1 != field2:
            print(field)
