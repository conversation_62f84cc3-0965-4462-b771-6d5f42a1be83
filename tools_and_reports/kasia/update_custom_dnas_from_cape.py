import json
import os

import requests
from django.conf import settings

from gallery.enums import ConfiguratorTypeEnum
from gallery.management.commands.regenerate_dna_entries import DNA_TO_BE_USED

for dna in DNA_TO_BE_USED:
    if dna.get('configurator_type') == ConfiguratorTypeEnum.COLUMN:
        filename = dna.get('dna_file_path')
        mesh_id = filename.split('/')[1].split('_')[1].split('.')[0]
        url = (
            f'http://cape.tylko/api/serialization_decoder/mesh/{mesh_id}/?data-for-configurator=True'
        )
        resp = requests.get(url)
        resp.raise_for_status()
        dna = resp.json()
        dna_file_path = os.path.join(
            settings.ROOT_PATH,
            'gallery',
            'static',
            filename,
        )
        with open(dna_file_path, 'w') as fp:
            json.dump(dna, fp)
