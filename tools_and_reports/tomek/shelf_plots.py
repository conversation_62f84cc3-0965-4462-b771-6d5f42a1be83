from matplotlib import patches
import matplotlib.pyplot as plt

# colors for matplotlib patch plot
color_standart_1 = '#D0D0D0'
color_standart_2 = '#E8E8E8'
color_fill_1 = '#ff3c00'
color_fill_2 = '#00c3ff'
plt_patch_colors = {
    'V': color_standart_1,
    'H': color_standart_1,
    'S': color_standart_1,
    'D': color_fill_1,
    'T': color_fill_2,
    'B': color_fill_1,
}
plt_patch_alpha = {
    'V': 1,
    'H': 1,
    'S': 0.75,
    'D': 0.5,
    'T': 0.5,
    'B': 0.2,
}


def get_shelf_plt_patch(self, move_shelf=(0, 0), center_x=True):
    elements = self.get_elements_domains()
    colors = plt_patch_colors
    colors_alpha = plt_patch_alpha
    elem_patches = []
    for ele in elements:
        color = colors[ele['elem_type']]
        alpha = colors_alpha[ele['elem_type']]
        # geometry = self.move_geometry(ele, move_to=move_shelf)
        _center = (
            (ele['x_domain'][1] - ele['x_domain'][0]) / 2.0 if center_x else 0
        )

        xy = [
            [ele['x_domain'][0], ele['y_domain'][0]],
            [ele['x_domain'][1], ele['y_domain'][0]],
            [ele['x_domain'][1], ele['y_domain'][1]],
            [ele['x_domain'][0], ele['y_domain'][1]],
        ]

        _patch = patches.Polygon(xy, color=color, closed=True, alpha=alpha)
        elem_patches.append(_patch)

    return elem_patches


def get_shelf_plt(
    fig_size=(8, 8),
    xy_lim=(5000, 5000),
    background_color='#f7f9f9',
    clip_bounds=True,
    ax=None,
    hide_border=False,
    plot_title='',
    title_font_size=8,
):
    if clip_bounds:
        xy_lim = (self.get_width() * 10, self.get_height() * 10)
    ax = get_plot_base(
        fig_size=fig_size,
        xy_lim=xy_lim,
        background_color=background_color,
        ax=ax,
        plot_title=plot_title,
        title_font_size=title_font_size,
    )
    elem_patches = self.get_shelf_plt_patch(center_x=False)
    for e in elem_patches:
        ax.add_patch(e)
    if hide_border:
        ax.axis('off')
    return ax


def plot_shelfes(jettys, columns=3, grid_indexes=None, fig_size_multiply=1):
    """
    plot shelfes on a grid
    :param jettys:
    :param columns:
    :param grid_indexes: optional - insert shelfes on a specific cells by index, len(grid_indexes) must == len(jettys)
    :return:
    """
    import math

    # overwrite positions of shelfes in a grid
    if not grid_indexes:
        grid_indexes = list(range(1, len(jettys) + 1))

    rows_spacing = 5
    fig = plt.figure(
        figsize=(
            columns * 3 * fig_size_multiply,
            5 * rows_spacing * fig_size_multiply,
        )
    )

    cells_number = len(jettys) * columns
    rows = int(math.ceil(len(jettys) / float(columns)))
    # print cells_number, len(jettys), rows

    axs = [fig.add_subplot(rows, columns, x, aspect=1) for x in grid_indexes]
    for i, jetty in enumerate(jettys):
        jetty.get_shelf_plt(ax=axs[i], clip_bounds=False, xy_lim=(5000, 5000))

    return fig


def get_plot_base(
    fig_size=(8, 8),
    xy_lim=(5000, 5000),
    background_color='#fbfcfc',
    ax=None,
    plot_title='',
    title_font_size=8,
):
    if ax is None:
        fig1 = plt.figure(figsize=fig_size)
        ax = fig1.add_subplot(111, aspect='equal', facecolor=background_color)
    if plot_title:
        ax.set_title(plot_title, fontsize=title_font_size)
    ax.set_xlim([0, xy_lim[0]])
    ax.set_ylim([0, xy_lim[1]])
    ax.grid(False)
    ax.get_xaxis().set_visible(False)
    ax.get_yaxis().set_visible(False)
    return ax
