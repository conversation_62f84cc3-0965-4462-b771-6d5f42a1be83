from __future__ import print_function
import csv
from decimal import Decimal
result = []
errors = []
match = []
x = []


with open('/home/<USER>/Dokumenty/Beata_kursy/sprzedaż.csv') as csvfile:
    ii = 0

    spamreader = csv.reader(csvfile, delimiter=',', quotechar='"')
    for invoice, desc, brutto in spamreader:
        x.append(invoice)
        if 'Klarna' in invoice:
            continue
        if 'storno' in invoice:
            continue
        # print i
        ii += 1
        if ii == 1:
            continue
        brutto = Decimal(brutto.replace(',','.'))
        # result.append(invoice, desc, Decimal(brutto))
        try:
            i = Invoice.objects.get(Q(pretty_id__endswith=invoice.replace(' ', '')) | Q(pretty_id=invoice.replace(' ', '')))
        except:
            errors.append((invoice, 'no invoice', ''))
            print('no invoice', invoice)
            continue
        d = i.to_dict()
        if brutto != d['total_value_in_pln']:
            result.append((invoice, brutto,  d['total_value_in_pln']))
            print(invoice, brutto,  d['total_value_in_pln'])
        else:
            match.append((invoice, brutto,  d['total_value_in_pln']))


from custom.utils.exports import dump_list_as_csv
print(len(x))
dump_list_as_csv(result, output=open('/home/<USER>/Dokumenty/Beata_kursy/diff_vat.csv', 'w'), mail=None,  delimiter=',')
dump_list_as_csv(errors, output=open('/home/<USER>/Dokumenty/Beata_kursy/no_match.csv', 'w'), mail=None, delimiter=',')
dump_list_as_csv(match, output=open('/home/<USER>/Dokumenty/Beata_kursy/match.csv', 'w'), mail=None, delimiter=',')