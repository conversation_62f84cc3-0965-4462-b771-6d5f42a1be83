from __future__ import print_function
from custom.models import FurnitureAbstract
from mailing.templates import OldLeadsExtensionMail, OldLeadsReactivationKlarnaMail, OldLeadsReactivationMail
from django.utils.translation import gettext
from django.utils import translation
from time import sleep
from tqdm import tqdm

from orders.enums import OrderStatus

bl_list = []
items_list = []
didnt_found = []
test = not True


def send_20(email, lang, user_id, region=None):
    if not user_id:
        user = User.objects.filter(email=email).last()
        if user is None:
            didnt_found.append(email)
            print('didnt found {}'.format(email))
            return
        user_id = user.id
    else:
        user = User.objects.get(id=user_id)
    cart = Order.objects.filter(status=OrderStatus.CART, owner=user_id).first()

    if cart and cart.items.count() > 0:
        items = [item.order_item for item in cart.items.all() if item.order_item and item.order_item.preview
                 ]
        link = 'front-cart'
    else:
        items = Jetty.objects.filter(owner=user_id, furniture_status=FurnitureAbstract.SAVED,
                                     preview__isnull=False).exclude(preview__exact='').order_by('-id')
        link = 'front-library'
    if len(items) == 0:
        print('no items', user_id)
        items_list.append(user_id)
        return
    if RetargetingBlacklist.objects.filter(email__icontains=email).count() > 0 and not test:
        print('black list', email)
        bl_list.append(email)
        return
    voucher = {'value_str': '20%', 'amount_starts': '50PLN', 'code': 'rgt5s8'}
    context = {'cart_or_wishlist_link': link,
               'blacklist_token': RetargetingBlacklistToken.get_or_create_for_email(email=email).token,
               'login_access_token': LoginAccessToken.get_or_create_for_user(user),
               'items': items,
               'voucher':voucher,
               }

    translation.activate(lang)
    topic = gettext('mailing_old_leads_extension_mail_promo_subject_line_1')
    if test:
        email = '<EMAIL>'

    mail =  OldLeadsExtensionMail(email, context)
    language = lang
    mail.topic = topic
    mail.send(language=language)
    # else:
    #     email = '<EMAIL>'
        # translation.activate(lang)
        # topic = 'Nur jetzt: 20%% Rabatt auf JEDE Bestellung!'
        # mail = OldLeadsReactivationKlarnaMail(email, context)
        # language = lang
        # mail.topic = topic
        # mail.send(language=language)


import csv
result = []
with open('/tmp/reaktywacja_francja_extension_08_07.csv') as csvfile:
    spamreader = csv.reader(csvfile, delimiter=',', quotechar='"')
    print(spamreader)
    # for mail, user, lang, region in spamreader: // im sorry ms jackson, different import file
    for mail, lang in spamreader:
        # if user == 'uid':
        #     continue
        result.append((mail, lang, None, None))
# result = []
# if test:
#     result = [('<EMAIL>', 'en',14549002,	'denmark',),
#               ('<EMAIL>'	,'de',15335446,	'switzerland',),
#               ('<EMAIL>'	,'fr',12976235,	'switzerland',),
#               ('<EMAIL>'	,'de', 13500440,	'germany',)
#     ]

i = 0
ff = []
actually_send = 0


for mail, lang, user_id, region in tqdm(result):
# for mail, user_id, lang in result:
    # if i <= 200:
    #     i+=1
    #     print 'sleep 10 minut'
    #     continue
    try:
        send_20(mail, lang, user_id, region)
        actually_send += 1
    except Exception as e:
        print(e)
        ff.append((mail, ))
    if test and i == 10:
        break;
    # if i == 20:
    #     print 'sleep 10 minut'
    #     sleep(200)
    if i % 50 == 0:
        print('sleep na minute co 50')
        sleep(60)

    i += 1
    print('sending to {}'.format(mail))
    if i % 200 == 0 and i != 200:
        print('going to bed {}'.format(i))
        sleep(5)
        # break
print('done')
