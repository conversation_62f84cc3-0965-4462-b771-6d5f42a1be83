from __future__ import print_function
import random
import string

_rng = random.SystemRandom()

# v = Voucher.objects.filter(code='cbefpl7p6')[0]
prefixes = ['ca3o', 'ca3p']

for prefix in prefixes:
    tmp = []
    v = Voucher.objects.filter(code__startswith=prefix)[0]
    rn = VoucherRegionEntry.objects.filter(voucher=v)
    while len(tmp) != 300:
        z = ''.join(_rng.choice(string.ascii_lowercase) for _ in range(4))
        if z not in tmp:
            v.pk = None
            v.code = prefix + z
            v.end_date = '2019-06-30'
            try:
                v.save()
                tmp.append([prefix + z, None])
            except:
                continue
            rm = VoucherRegionEntry.objects.filter(voucher=v)
            rm.delete()

            for r in rn:
                r.pk=None
                r.voucher=v
                r.save()
            if not len(tmp) % 10:
                print(len(tmp))
            # breaknier

    from custom.utils.exports import dump_list_as_csv

    dump_list_as_csv(tmp, output=open('/tmp/rafaello_'+prefix+'.csv', 'w'), mail=None)
