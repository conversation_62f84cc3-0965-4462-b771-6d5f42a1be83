
from __future__ import print_function
from tqdm import tqdm

def do_it():
    invs_old = Invoice.objects.filter(issued_at__gte='2019-03-01', status__in=[InvoiceStatus.ENABLED, InvoiceStatus.CORRECTING],
                                  order__region_vat=False).\
                                filter(order__country__in=['austria','denmark','netherlands']).order_by('issued_at')
    for i in invs_old:
        o = i.order
        o.region_vat=True
        o.save(update_fields=['region_vat', ])
        i.pretty_id = ''
        # i.invoice_items.all().delete()
        # i._create_items()
        i.save(**{'special': True})


    invs = Invoice.objects.filter(issued_at__gte='2019-02-14',status__in=[InvoiceStatus.ENABLED, InvoiceStatus.CORRECTING],
                                  order__region_vat=True).exclude(pretty_id__startswith='VC')\
                                .exclude(pretty_id__startswith='FKS').\
                                filter(order__country__in=['austria','denmark','netherlands']).order_by('issued_at')

    for i in tqdm(invs):
        print('---')
        print(i.pretty_id)
        i.pretty_id = ''
        i.invoice_items.all().delete()
        i._create_items()
        i.save(**{'special': True})
    for i in tqdm(invs):
        i.save()
        # print i.pretty_id
        # print '+++'
        try:
            i.create_pdf()
        except:
            pass

    invs = Invoice.objects.filter(issued_at__gte='2019-02-14',
                                  status__in=[InvoiceStatus.ENABLED, InvoiceStatus.CORRECTING],
                                  order__region_vat=True).exclude(pretty_id__startswith='VC') \
        .exclude(pretty_id__startswith='FKS'). \
        filter(order__country__in=['austria', 'denmark', 'netherlands']).order_by('issued_at')

    for i in invs:
        print(i.pk)

    # invs = Invoice.objec??
