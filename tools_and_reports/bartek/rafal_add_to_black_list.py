import csv
from datetime import datetime


# if you encounter a "year is out of range" error the timestamp
# may be in milliseconds, try `ts /= 1000` in that case
# print(datetime.utcfromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S'))

result = []
with open('/tmp/to_be_unsubscribed.csv') as csvfile:
     spamreader = csv.reader(csvfile, delimiter=',', quotechar='"')
     for mail, timestamp in spamreader:
         ts = (datetime.utcfromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S'))
         result.append((mail, ts))
         if RetargetingBlacklist.objects.filter(email = mail).count() == 0:
            RetargetingBlacklist(email=mail, date_added=ts).save(from_mailchimp=True)

