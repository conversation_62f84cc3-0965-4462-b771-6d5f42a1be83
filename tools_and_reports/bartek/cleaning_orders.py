from __future__ import print_function

from django.contrib.auth.models import User
from django.core.paginator import Paginator
from django.db.models import Count

from orders.models import Order
from user_profile.models import UserProfile


def clean():
    last_update = '2019-11-20'
    query_orders = (
        Order.objects.annotate(
            order_items=Count('items'),
            inv=Count('invoice'),
            tran=Count('transactions'),
            prod=Count('product'),
            jetty=Count('jetties'),
            log=Count('logistic_info'),
        )
            .filter(total_price=0, order_items=0, inv=0, tran=0, prod=0, log=0, jetty=0)
            .filter(updated_at__lt=(last_update))
    )

    query_users = User.objects.annotate(
        number_of_orders=Count('order'),
        jettys=Count('jetty'),
        sampleboxes=Count('samplebox'),
        userdevices=Count('devices'),
    ).filter(
        last_login__lt=last_update,
        sampleboxes=0,
        number_of_orders=1,
        jettys=0,
        userdevices=0,
        is_staff=False,
    )

    before = (query_orders.count(), query_users.count())
    before_total = (
        Order.objects.all().count(),
        User.objects.all().count(),
        UserProfile.objects.all().count(),
    )
    print(before)
    # print(before_total[0] - before[0], before_total[1] - before[1])
    print(before_total)
    query_orders._raw_delete(query_orders.db)
    paginator = Paginator(query_users, 1000)

    for page in range(1, paginator.num_pages + 1):
        for row in paginator.page(page).object_list:
            row.delete()
        print("done processing page {} of {}".format(page, paginator.num_pages))

    after = (
        Order.objects.all().count(),
        User.objects.all().count(),
        UserProfile.objects.all().count(),
    )

    print(before)
    print(before_total)
    print(after)
