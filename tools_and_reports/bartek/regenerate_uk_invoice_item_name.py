from datetime import datetime

from django.utils import translation
from tqdm import tqdm

from invoice.choices import InvoiceStatus
from invoice.models import Invoice

ISSUED_AT_NEW = datetime(2021, 1, 1, 0, 1)

translation.activate('en')

def get_invoices():
    return Invoice.objects.filter(
        order__country='united_kingdom',
        status=InvoiceStatus.ENABLED,
        issued_at=ISSUED_AT_NEW,
    )

def get_items(self):
    """
    Creates :class:`InvoiceItem`s base on :class:`Order`'s :class:`OrderItem`s
    :return:
    """
    items = []
    for oi_id, oi in enumerate(self.order.items.all().order_by('price')):
        oi_description = oi.order_item.get_item_description()
        oi_material = oi_description['material']
        oi_name = (
            oi_description['name']
            if not oi.invoice_product_name
            else oi.invoice_product_name
        )

        PL_ADD = {
                '00': {
                    'materials': {
                        0: '',
                    },
                    'name': ' / Próbki materiałowe',
                },
                '01_TYPE01': {
                    'materials': {
                        0: ' / Biała sklejka',
                        1: ' / <PERSON><PERSON>na sklejka',
                        2: ' / Klejonka',
                        3: ' / Szara sklejka',
                        6: ' / Sklejka z laminatem HPL w kolorze classic-red',
                        7: ' / Sklejka z laminatem HPL w kolorze yellow',
                        8: ' / Sklejka z laminatem HPL w kolorze dusty-pink',
                    },
                    'name': ' / Regał Tylko',
                },
                '01_TYPE02': {
                    'materials': {
                        0: ' / Biała płyta wiórowa',
                        1: ' / Płyta wiórowa',
                        2: ' / Granatowa płyta wiórowa',
                        3: ' / Piaskowa płyta wiórowa',
                        4: ' / Płyta wiórowa',
                    },
                    'name': ' / Regał Tylko',
                },
                '01_TYPE01v': {
                    'materials': {
                        0: ' / Fornir',
                        1: ' / Fornir',
                    },
                    'name': ' / Regał Tylko',
                },
            }
        oi_item_type = oi.order_item.get_project_id()
        oi_material_raw = oi.order_item.material if oi_item_type != "00" else 0
        if oi_item_type == '01':
            if oi.order_item.shelf_type == 0:
                oi_item_type = "01_TYPE01"
            elif oi.order_item.shelf_type == 2:
                oi_item_type = "01_TYPE01v"
            else:
                oi_item_type = "01_TYPE02"

        oi_name = "{}{}".format(
            oi_name,
            PL_ADD[oi_item_type]['name'],
        )
        oi_material = "{}{}".format(
            oi_material,
            PL_ADD[oi_item_type]['materials'][oi_material_raw],
        )
        items.append([oi_name, oi_material])
    return items




def recalculate(invoices):
    res = []
    for invoice in tqdm(invoices):
        items = invoice.invoice_items.all().order_by('id')
        items_names = get_items(invoice)
        if len(items_names) != items.count():
            res.append(invoice.pk)
            continue
        for i, item in enumerate(items):
            item.item_name = items_names[i][0]
            item.item_material = items_names[i][1]
            item.save()
        invoice.create_pdf()
    print(res)

print(get_invoices().count())
recalculate(get_invoices())
