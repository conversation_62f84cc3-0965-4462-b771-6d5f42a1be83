from orders.choices import OrderSource
from custom.utils.exports import dump_list_as_csv

notifications = Notification.objects.filter(success=False, transaction__isnull=False, transaction__order__created_at__gte='2018-01-01').order_by('id')
notifications = Notification.objects.filter(code="AUTHORISATION").filter(
            success=False).filter(transaction__isnull=False, transaction__order__created_at__gte='2018-01-01').order_by('id')

res = []

for z in notifications:
    res.append([z.id, z.transaction.created_at, z.transaction.updated_at, z.reason, z.transaction.order.owner.pk, z.amount_value, z.amount_currency, z.transaction.payment_method, dict(Order.STATUS_CHOICES)[z.transaction.order.status], dict(OrderSource)[z.transaction.order.order_source], z.transaction.order.country, z.transaction.order.used_promo.code if z.transaction.order.used_promo else '', z.transaction.order.owner.profile.language, z.transaction.order.id, z.transaction.order.owner.profile.email, z.transaction.order.email,
                ])
headers = ['Notification id', 'Created at', 'Updated at', 'Transaction status', 'User id', 'Amount value', 'Amount currency', 'Payment method', 'Order status', 'Order source', 'Country', 'used promo', 'language', 'Order id', 'User email', 'Order email']
dump_list_as_csv(res, output=open('/tmp/mateusz.csv', 'w'), mail=None, headers=headers)
