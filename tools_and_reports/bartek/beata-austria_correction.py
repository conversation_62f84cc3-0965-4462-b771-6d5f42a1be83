from __future__ import print_function
import csv
from decimal import Decimal, ROUND_HALF_UP
from django.utils import timezone
from datetime import datetime

result = []
# with open('/home/<USER>/sprzedaz_austria.csv') as csvfile:
with open('/home/<USER>/Dokumenty/sprzedaz_austria.csv') as csvfile:
    spamreader = csv.reader(csvfile, delimiter=',', quotechar='"')
    orders = []
    all = []
    for quoter, year, date, invoice_number, name, account, net_20, vat_20, brutto_20, \
        currency, net_23, vat_23, brutto_23 in spamreader:
        if net_20 in [None, "", "net"]:
            continue

        # print net_23
        invoice_number = invoice_number.split(' ')[0]
        if Invoice.objects.filter(pretty_id=invoice_number).count() != 1:
            # print invoice_number
            continue

        i = Invoice.objects.filter(pretty_id=invoice_number).last()
        if i.status not in [0, 4]:
            print(i.status, i.pk)
        if i.invoice_items.all()[0].vat_status != 0 or i.invoice_items.all()[0].vat_rate == 0:
            print(i.status, i.pk)

        correction = Invoice.objects.filter(order=i.order, status=InvoiceStatus.CORRECTING).count()
        if correction > 0:
            i = Invoice.objects.filter(order=i.order,
                                             status=InvoiceStatus.CORRECTING).order_by('issued_at').last()

        d = i.to_dict()
        if d['total_value'] == 0 and i.order.pk not in [3603020, 8901560]:
            continue
        elif d['vat_value'] == 0 and i.order.pk not in [3603020, 8901560]:
            continue



        if i.order.pk not in orders:
            orders.append(i.order.pk)

        else:
            all.append(i.order.pk)
            continue
        result.append(
            (invoice_number, i, Decimal(brutto_23.replace(',', '.')), Decimal(net_20.replace(',', '.')),
        Decimal(brutto_20.replace(',', '.')))
        )
        # break

            # print '@', i.order.pk

    print(set(all))

    # 1/0
# import sys
# sys.exit(0)


def make_correnction2_to0(result):
    issued_at = datetime(2018,11,13,12,00,00)
    for i, invoice, gross, n, b in result:
        o = invoice.order
        o.region_vat = False
        o.save(update_fields=['region_vat'])
        if not invoice.order.region:
            invoice.order.region = Region.objects.filter(name='austria')[0]
            invoice.order.save(update_fields=['region'])
        correction = Invoice.objects.filter(order=invoice.order, status=InvoiceStatus.CORRECTING).count()
        if correction > 0:
            invoice = Invoice.objects.filter(order=invoice.order,
                                             status=InvoiceStatus.CORRECTING).order_by('issued_at').last()
        correction = Invoice()
        previous_invoice = correction.get_previous_correction()
        if not previous_invoice:
            previous_invoice = invoice

        correction.issued_at = issued_at
        correction.sell_at = invoice.sell_at

        correction.status = InvoiceStatus.CORRECTING
        correction.order = invoice.order
        correction.corrected_invoice = invoice
        correction.corrected_notes = u'Wrong vat calculation/ Niewłaściwa wartość VAT'
        correction.pretty_id = correction._generate_pretty_id()
        correction.save()

        for item in invoice.invoice_items.all():
            old_pk = item.pk
            item.pk = None
            item.net_value = 0
            item.discount_value = 0
            item.net_price = 0
            item.gross_price = 0
            item.vat_status = 0
            item.vat_rate = 0.23
            item.vat_amount = 0.0
            item.net_weight = 0
            item.gross_weight = 0

            item.gross_price = item.net_value
            item.invoice_id = correction.id
            item.corrected_invoice_item_id = old_pk
            item.save()
        correction.save()
        # correction.order.region_vat = True
        correction.create_pdf()
        o = invoice.order
        o.region_vat = True
        o.save(update_fields=['region_vat'])
        print('0',o.pk)
        # break


def generate_pretty_id(self, issued_at=None):
    """
    Generates pretty id, new standard after new year
    :return: :class:`string` representing pretty id
    """
    if issued_at:
        issue_date = issued_at
    elif self.issued_at:
        issue_date = self.issued_at
    else:
        issue_date = timezone.now()
    if issue_date is not None and (issue_date.tzinfo is None or issue_date.tzinfo.utcoffset(issue_date) is None):
        issue_date = timezone.make_aware(issue_date)

    if self.order is None:
        return "00000/00/0000/00000/00"  # id/month/year/ordernumber/country

    invoices_from_selected_year = Invoice.objects. \
        filter(issued_at__year=issue_date.year). \
        order_by('issued_at', 'id')

    invoices_from_selected_year = invoices_from_selected_year.filter(pretty_id__startswith='VC')
    # else:
    #     invoices_from_selected_year = invoices_from_selected_year.filter(order__region_vat=False)

    invoice_dates = []
    for x in list(invoices_from_selected_year):
        if x.issued_at.tzinfo is None or x.issued_at.tzinfo.utcoffset(x.issued_at) is None:
            x.issued_at = timezone.make_aware(x.issued_at)
        x.order.region_vat = True
        invoice_dates.append((x, x.issued_at, x.get_invoice_number_in_year()))
    invoice_dates = [x for x in invoice_dates if x[2] is not None]
    invoice_dates.sort(key=lambda x: x[1])

    try:
        if issued_at is not None:
            if issued_at.tzinfo is None or issued_at.tzinfo.utcoffset(issued_at) is None:
                issued_at = timezone.make_aware(issued_at)
            place = [x[1] for x in invoice_dates].index(issued_at)
        else:
            place = [x[0].id for x in invoice_dates].index(self.id)
    except ValueError:
        place = None

    if len(invoice_dates) == 0:
        print('?')
        invoice_no = 1
    elif place is not None and invoice_dates[place][0].id == self.id:
        print(place, invoice_dates[place][2])
        invoice_no = invoice_dates[place][2]
    else:
        invoice_no = invoice_dates[-1][2] + 1
    from regions.utils import Countries
    try:
        country_code = Countries.__members__[str(self.order.country.lower())].code
    except Exception:
        print(self.order.country)
        country_code = "AT"

    prefix = ""
    # if self.order.region_vat:
    prefix = 'VC/'
    return "%s%05d/%s/%d/%s" % (prefix, invoice_no, issue_date.strftime('%m/%Y'), self.order.id, country_code)


def make_invoices_special_type(result):
    from django.utils import timezone
    issued_at = datetime(2018,11,13,12,00,00)
    for i, invoice, gross, n ,b in result:
        if not invoice.order.region:
            invoice.order.region = Region.objects.filter(name='austria')[0]
            invoice.order.save(update_fields=['region'])
        invoice.order.region_vat = True
        o = invoice.order
        if o.payable_booking_date is not None:
            sell_at = o.payable_booking_date
        elif o.settled_at is not None:
            sell_at = o.settled_at
        elif o.paid_at is not None:
            sell_at = o.paid_at
        elif o.placed_at is not None:
            sell_at = o.placed_at
        else:
            sell_at = o.created_at
        correction = Invoice.objects.filter(order=o, status=InvoiceStatus.CORRECTING).count()
        c = None
        if correction > 0:
            c = Invoice.objects.filter(order=o,
                                       status=InvoiceStatus.CORRECTING).order_by('issued_at').last()
        else:
            c = invoice
        # else:
        items = c.invoice_items.all()
        inv = c
        inv.pk = None
        inv.issued_at = issued_at
        inv.corrected_invoice = None
        inv.previous_correction = None
        inv.corrected_issued_at = None
        inv.status = InvoiceStatus.ENABLED_VAT_REGION_CORRECTION
        inv.pretty_id = generate_pretty_id(inv)
        inv.save(**{'special': True})

        # print c.invoice_items.all()

        for i in items:
            i.pk = None
            i.invoice = inv
            i.vat_rate = Decimal(0.2)
            i.vat_amount = ((i.gross_price / (1 + i.vat_rate)) * i.vat_rate).quantize(Decimal('.01'),
                                                                                      rounding=ROUND_HALF_UP)
            i.net_value = i.gross_price - i.vat_amount
            i.net_price = (i.net_value - i.discount_value) / (i.quantity if i.quantity > 0 else 1)
            i.region_price_net = i.net_value
            i.save()


        o.region_vat = True
        # o.save(update_fields=['region_vat',])
        inv.order.region_vat = True

        inv.create_pdf()
        d = inv.to_dict()
        # o.region_vat = False
        # o.save(update_fields=['region_vat', ])
        print(o.pk,  b == d['total_value'], n == d['net_value'])
        # if not (b == d['total_value'] and n == d['net_value']) and inv.order.pk not in [3680773, 9808006, 6225802, 10217998, 3680016, 2183449, 3594266, 4586269, 4385829, 3685671, 6482221, 3162544, 6893748, 6239413, 7127223, 5772475, 2014658, 1445318, 1363145, 3744205, 2817360, 4707544, 4141145, 6650683, 2350442, 5317105, 5267835]:
        #     print b, '==', d['total_value'], n, '==', d['net_value']
        #     break
        # inv.delete()
        # break

from copy import deepcopy
res_zero = deepcopy(result)
make_invoices_special_type(result)
make_correnction2_to0(res_zero)
