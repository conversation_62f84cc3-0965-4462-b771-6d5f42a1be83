from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP

from django.db import models
from django.db.models import OuterRef, Count, Subquery
from past.utils import old_div
from tqdm import tqdm

from invoice.choices import InvoiceStatus
from invoice.models import Invoice


class SQCount(Subquery):
    template = "(SELECT count(*) FROM (%(subquery)s) _count)"
    output_field = models.IntegerField()


enabled = Invoice.objects.filter(
    status=InvoiceStatus.ENABLED, order__id=OuterRef('order__id')
).values('pk')

germany = (
    Invoice.objects.filter(
        status=InvoiceStatus.PROFORMA,
        # created_at__gte='2020-10-01',
        order__pk__gte=********,
        # order__placed_at__gte='2020-10-01',
        order__placed_at__lte='2021-01-01',
        order__country='germany',
        order__vat_type=0,
        order__chosen_payment_method__in=['klarna', 'klarna_account'],
        # order__pk=********,
    )
    .annotate(enabled=SQCount(enabled))
    .filter(enabled=0)
)
#
for g in tqdm(germany):
    for item in g.invoice_items.all():
        item.vat_rate = Decimal(0.19)
        vat_value = (
            (old_div(item.gross_price, (item.vat_rate + 1))) * item.vat_rate
        ).quantize(
            Decimal('.01'),
            rounding=ROUND_HALF_UP,
        )
        item.net_value = item.gross_price - vat_value
        item.vat_amount = vat_value
        org_gross = (item.net_price * Decimal(1.16)).quantize(
            Decimal('.01'),
            rounding=ROUND_HALF_UP,
        )
        org_vat_value = ((old_div(org_gross, Decimal(1.19))) * Decimal(0.19)).quantize(
            Decimal('.01'),
            rounding=ROUND_HALF_UP,
        )
        if item.discount_value:
            item.net_price = org_gross - org_vat_value
            item.discount_value = item.net_price - item.net_value
        else:
            item.net_price = item.net_value
        print(g.pk, item.net_value, item.vat_amount, item.net_price)
        # print(org_gross, org_vat_value)

        item.save()
    g.create_pdf()

    # break
    # g.to_dict()
#     print(g.order.pk, g.order.paid_at,)

germany.count()
