vouchers = ['wstw250f1h', 'wstw2502o2', 'wstw250bnn', 'wstw250iri', 'wstw250dok', 'wstw250cxv', 'wstw25051o',
            'wstw2504s1', 'wstw250h01', 'wstw250gsl', 'wstw250vff', 'wstw250ugv', 'wstw250e45', 'wstw250ovi',
            'wstw250l3r', 'wstw250s0v', 'wstw2503b3', 'wstw25007g', 'wstw2503lg', 'wstw250so5', 'wstw250il0',
            'wstw250gob', 'wstw250ct0', 'wstw250t7d', 'wstw2501hq', 'wstw250gi0', 'wstw2505nk', 'wstw250aox',
            'wstw2500ym', 'wstw250qmc', 'wstw250hns', 'wstw250r9b', 'wstw2502kb', 'wstw250wq7', 'wstw2508z4',
            'wstw250xb0', 'wstw2500gy', 'wstw250avg', 'wstw25078m', 'wstw250bti', 'wstw250bud', 'wstw2507xx',
            'wstw250opk', 'wstw250c6o', 'wstw2502mf', 'wstw250wd0', 'wstw2503e3', 'wstw250kbk', 'wstw250lde',
            'wstw250etp', 'wstw250mzs', 'wstw2507vp', 'wstw250t94', 'wstw250t1w', 'wstw250fc0', 'wstw250m8r',
            'wstw250dkq', 'wstw2500hv', 'wstw2507ar', 'wstw250xsp', 'wstw250fs1', 'wstw250uvv', 'wstw250t0f',
            'wstw250agk', 'wstw250g4c', 'wstw2507lm', 'wstw250si7', 'wstw25062k', 'wstw2503jx', 'wstw250056',
            'wstw250bnp', 'wstw250vsy', 'wstw2508fi', 'wstw250i98', 'wstw250yxm', 'wstw2507fe', 'wstw250eo2',
            'wstw250et2', 'wstw25069c', 'wstw2509in', 'wstw250i99', 'wstw250q8g', 'wstw250yjd', 'wstw250dau',
            'wstw250jdd', 'wstw2507n4', 'wstw25028c', 'wstw2505dz', 'wstw250oie', 'wstw250w47', 'wstw250bd0',
            'wstw2507mf', 'wstw2501lx', 'wstw250aaa', 'wstw250bzm', 'wstw25023v', 'wstw25068b', 'wstw250ebu',
            'wstw50086t', 'wstw5006vf', 'wstw500pd1', 'wstw500khd', 'wstw500k9i', 'wstw50096l', 'wstw50086y',
            'wstw5002uf', 'wstw5008zf', 'wstw500yhb', 'wstw500ltk', 'wstw500xm8', 'wstw500keu', 'wstw500gzx',
            'wstw500w91', 'wstw500uw6', 'wstw5000l7', 'wstw50077h', 'wstw500t6x', 'wstw500cjz', 'wstw500hsx',
            'wstw500ewf', 'wstw500cpc', 'wstw500k68', 'wstw500ibm', 'wstw500i6w', 'wstw500qd8', 'wstw500eia',
            'wstw500vu2', 'wstw500bxt', 'wstw500ncr', 'wstw500viq', 'wstw500lqk', 'wstw500atq', 'wstw5006lf',
            'wstw500xmd', 'wstw500p77', 'wstw500hbk', 'wstw500brj', 'wstw500lpn', 'wstw5009vh', 'wstw500ww2',
            'wstw500dh3', 'wstw500gq8', 'wstw500hhm', 'wstw500oty', 'wstw500q0i', 'wstw500qxr', 'wstw5001dm',
            'wstw5004a6', 'wstw500jal', 'wstw500btg', 'wstw500b3c', 'wstw500knn', 'wstw500xwb', 'wstw500kh5',
            'wstw500l0e', 'wstw500oes', 'wstw5004rv', 'wstw500lvk', 'wstw500v00', 'wstw500e38', 'wstw500l6p',
            'wstw500sdq', 'wstw500l4g', 'wstw5005z6', 'wstw5008aa', 'wstw50065b', 'wstw500yj5', 'wstw5009y1',
            'wstw500bq2', 'wstw500a05', 'wstw500dcr', 'wstw500x5o', 'wstw500qa7', 'wstw500j6a', 'wstw500hoj',
            'wstw5001hf', 'wstw500yxu', 'wstw500zn9', 'wstw500drx', 'wstw5005sh', 'wstw500hrp', 'wstw500ogp',
            'wstw500knb', 'wstw500yhl', 'wstw500aao', 'wstw50001h', 'wstw500a1w', 'wstw500whm', 'wstw500577',
            'wstw500go7', 'wstw500xu2', 'wstw5006d2', 'wstw5008cl', 'wstw500pmc', 'wstw1000pt', 'wstw10007s',
            'wstw1000kh', 'wstw1000rk', 'wstw1000l6', 'wstw1000bb', 'wstw1000qu', 'wstw1000uh', 'wstw1000ul',
            'wstw1000sm', 'wstw1000mx', 'wstw1000po', 'wstw1000ue', 'wstw1000s4', 'wstw1000h9', 'wstw1000kf',
            'wstw1000tz', 'wstw1000bx', 'wstw1000g1', 'wstw10001k', 'wstw1000ui', 'wstw10002w', 'wstw10001u',
            'wstw1000cj', 'wstw1000w3', 'wstw1000n7', 'wstw1000ph', 'wstw1000lu', 'wstw1000w8', 'wstw100022',
            'wstw1000af', 'wstw1000is', 'wstw1000m6', 'wstw1000s7', 'wstw10006b', 'wstw10008t', 'wstw1000vi',
            'wstw10005o', 'wstw1000h6', 'wstw10008e', 'wstw1000js', 'wstw100094', 'wstw10004o', 'wstw1000dj',
            'wstw1000v6', 'wstw1000dx', 'wstw1000we', 'wstw1000qc', 'wstw100076', 'wstw10008m', 'wstw10009x',
            'wstw1000wn', 'wstw1000qd', 'wstw100059', 'wstw1000cw', 'wstw1000tv', 'wstw1000wr', 'wstw1000ru',
            'wstw1000gh', 'wstw1000u3', 'wstw1000ac', 'wstw1000l0', 'wstw1000fw', 'wstw1000c7', 'wstw10005l',
            'wstw100054', 'wstw10009a', 'wstw1000n5', 'wstw1000t4', 'wstw10008i', 'wstw1000vo', 'wstw1000io',
            'wstw1000ix', 'wstw1000yc', 'wstw1000gc', 'wstw10000k', 'wstw1000jt', 'wstw1000se', 'wstw100029',
            'wstw100091', 'wstw1000ly', 'wstw1000e4', 'wstw1000vj', 'wstw1000fo', 'wstw1000fm', 'wstw10003z',
            'wstw1000y5', 'wstw10004v', 'wstw1000eu', 'wstw10005g', 'wstw10006s', 'wstw1000rn', 'wstw1000y7',
            'wingsplwq', 'wingsik2d', 'wingseen7', 'wingsv5sl', 'wingsgu27', 'wings4113', 'wings1a13', 'wingsc6p7',
            'wingspmhm', 'wings0lwa', 'wingsccoa', 'wingsh6c0', 'wings7r6b', 'wings143m', 'wingsd44v', 'wingstdjf',
            'wingsquw1', 'wingselwo', 'wingsu80t', 'wings562f', 'wingsnsj9', 'wingsnpim', 'wings0fs6', 'wings7qsb',
            'wingsyloe', 'wingsuguy', 'wingsjd9g', 'wings549p', 'wingsifjq', 'wings0j77', 'wingshxvo', 'wingsnq0y',
            'wingsquyx', 'wingsssrb', 'wingszxab', 'wingsva7q', 'wingsml56', 'wingsbkbr', 'wings8zj9', 'wingsj5mg',
            'wingsi487', 'wingsjzm3', 'wingsx29s', 'wingsycqf', 'wings7l6g', 'wingsorn6', 'wingsahdb', 'wingsdok6',
            'wingsly5w', 'wingslamp', 'wings3w1v', 'wingsp6l4', 'wingshlqr', 'wingsoc44', 'wingsc4jl', 'wingsmjjh',
            'wingsih2u', 'wings6z14', 'wings3xcd', 'wingsgb2u', 'wingseh8l', 'wingsldlt', 'wingsi1sg', 'wingsyqjl',
            'wings4q3a', 'wingsbtbj', 'wings3xxb', 'wingscpsb', 'wings70s3', 'wingsphvy', 'wings4xx0', 'wingsmqvi',
            'wings7m8l', 'wingsiboi', 'wingsja91', 'wingsdeuy', 'wings2pbx', 'wingsilsa', 'wingsbm9y', 'wings26cu',
            'wingsbl5m', 'wings55p4', 'wings6qlj', 'wingst99i', 'wingsxrji', 'wingsk6to', 'wingso8xw', 'wings962e',
            'wings5m7y', 'wings7xml', 'wingswxxj', 'wingsx33l', 'wings4n21', 'wings0gx3', 'wingsx5a7', 'wingssa4w',
            'wingsocnb', 'wingssfm6', 'wingsrykd', 'wingsjegv', ]

vs = Voucher.objects.filter(code__in=vouchers)
for v in vs:
    v.active=False
    v.save()
