from __future__ import print_function
import random
import string
from time import sleep

_rng = random.SystemRandom()

# v = Voucher.objects.filter(code='cbefpl7p6')[0]
prefixes = [('cbf', '2019-04-30'), ('cbef', '2019-03-31 23:59:59')]

for prefix in prefixes:
    tmp = []
    vs = Voucher.objects.filter(code__startswith=prefix[0], end_date=prefix[1])
    print(prefix, vs.count())
    for idx, v in enumerate(vs):
        if idx % 10 == 0:
            print(idx, 'going to bed')
            sleep(2)
        v.end_date = '2019-09-30'
        v.save()
