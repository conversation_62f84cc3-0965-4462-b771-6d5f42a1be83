from __future__ import print_function
from custom.models import FurnitureAbstract
from mailing.templates import OldLeadsReactivationMail, OldLeadsReactivationMailPlainText, \
    OldLeadsReactivationKlarnaMailA, OldLeadsReactivationKlarnaMail, OldLeadsExtensionMail, \
    OldLeadsReactivationMailPlainText, NewHeightUpdateNoDrawers, NewHeightUpdateLastChance
from django.utils.translation import gettext
from django.utils import translation
from time import sleep

from orders.enums import OrderStatus

bl_list = []
items_list = []

test = False


def send_20(email, user_id):
    cart = Order.objects.filter(status=OrderStatus.CART, owner=user_id).first()
    user = User.objects.get(id=user_id)
    items = Jetty.objects.filter(owner=user_id, furniture_status=FurnitureAbstract.SAVED,
                                     preview__isnull=False).exclude(preview__exact='').order_by('-id')
    if cart and cart.items.count():
        items = [item.order_item for item in cart.items.all() if item.order_item and item.order_item.preview
                 ]
    context = {
        'ifcart': True if cart and cart.items.count() else False,
        'blacklist_token': RetargetingBlacklistToken.get_or_create_for_email(email=email).token,
        'access_token': LoginAccessToken.get_or_create_for_user(user),
        'items': items,
    }
    # print context, cart2, whistlist
    if test == True:
        email = '<EMAIL>'
        # email = '<EMAIL>'
        mail = NewHeightUpdateLastChance(email, context)
    else:
        mail = NewHeightUpdateLastChance(email, context)
    language = 'en'

    if user.profile and user.profile.language:
        language = user.profile.language
    # if user.profile.language == 'fr':
    #     return
    translation.activate(language)
    topic = gettext('mailing_new_heights_last_chance_mail_subject_line_1')
    mail.topic = topic
    mail.send(language=language)


import csv

result = []
with open('/tmp/last_chance.csv') as csvfile:
    spamreader = csv.reader(csvfile, delimiter=',', quotechar='"')
    for mail, user in spamreader:
        if user == 'uid':
            continue
        result.append((mail, int(user)))

i = 0
ff = []
for mail, user_id in result:
    # if i <= 400:
    #     i += 1
    #     continue
    try:
        send_20(mail, user_id)
    except:
        ff.append((mail, user_id))
    # if i >= 10:
    #     i += 1
    #     break

    if i == 200:
        print('sleep 10 minut')
        sleep(600)
    i += 1
    print('sending to {}'.format(mail))
    if i % 200 == 0 and i != 200:
        print('going to bed {}'.format(i))
        sleep(120)
        # break
print('done')
