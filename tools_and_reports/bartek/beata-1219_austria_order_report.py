from custom.utils.exports import dump_list_as_csv
from invoice.choices import InvoiceStatus
from orders.models import PaidOrders

o = PaidOrders.objects.filter(country__icontains='Austria', paid_at__gte='2018-10-01').order_by('paid_at')
headers = [['placed', 'settled', 'amount', 'email', 'order id', 'invoice pretty id', 'ic'], ]
o = headers + [[p.placed_at.date() if p.placed_at else '', p.paid_at.date(), p.total_price,
                p.email if len(p.email) > 2 else p.owner.profile.email, p.id, p.invoice_set.filter(status=InvoiceStatus.ENABLED).first().pretty_id if p.invoice_set.filter(status=InvoiceStatus.ENABLED).count()  else '', p.invoice_set.count()] for p in o]

dump_list_as_csv(o, output=open('/tmp/beata2.csv', 'w'), mail=None)
