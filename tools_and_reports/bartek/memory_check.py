import datetime
import os
import subprocess

import psutil
import requests

memory = psutil.virtual_memory()
SLACK_WEBHOOK = os.getenv('SLACK_WEBHOOK')
SLACK_USERNAME = os.getenv('SLACK_USERNAME', 'killer')

if memory.percent > 80:
    pids = psutil.pids()
    processes_list = []
    for pid in pids:
        process = psutil.Process(pid)
        processes_list.append([process, process.memory_percent()])
    processes_list = sorted(processes_list, key=lambda p: p[1], reverse=True)
    print('-' * 20)
    print(datetime.datetime.now())
    for user in psutil.users():
        print(user)
    print('processes:')
    for i, process in enumerate(processes_list):
        print(
            f'memory in percent {process[1]}',
            process[0].pid,
            ' '.join(process[0].cmdline()),
        )
        if i > 20:
            break
    print('+' * 20)

    if memory.percent > 95:
        print('-' * 20)
        print(datetime.datetime.now())
        process = processes_list[0]
        text = f'killing pid: {process[0].pid} memory: {process[1]:.2f}% ' + ' '.join(
            process[0].cmdline()
        )
        print(text)
        command = [
            'timeout',
            '10',
            'strace',
            '-e',
            'trace=read,write,recvfrom,sendto',
            '-vvvvv',
            '-x',
            '-p',
            f'{process[0].pid}',
            '-o',
            f'{process[0].pid}.dump',
        ]
        subprocess.run(command)
        process[0].kill()
        print('+' * 20)
        alarm = {
            'text': text,
            'channel': 'platforma-priv',
            'username': SLACK_USERNAME,
            'icon_emoji': ':let-it-burn:',
        }
        q = requests.post(
            SLACK_WEBHOOK,
            json=alarm,
        )
        print('+' * 20)
