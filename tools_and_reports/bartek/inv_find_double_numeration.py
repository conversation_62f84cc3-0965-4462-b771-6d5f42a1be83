from __future__ import print_function
from datetime import date
def func():
    i = date(2019, 0o5, 1)

    x = Invoice.objects. \
        filter(Q(issued_at__year=i.year)). \
        filter(Q(issued_at__month=i.month)).filter(status=0)

    test = {}
    for inv in x:
        p = inv.pretty_id.split('/')
        key = '{}/{}'.format('/'.join(p[:2]), p[-1])
        if not key in test:
            test[key] = 1
        else:
            test[key] += 1

    result = {}
    for k,v in test.iteritems():
        if v == 1:
            continue
        else:
            result[k] = v
    print(result)
