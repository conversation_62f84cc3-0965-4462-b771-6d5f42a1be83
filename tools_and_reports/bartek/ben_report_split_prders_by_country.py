from custom.utils.exports import dump_list_as_csv
from datetime import date
from dateutil import relativedelta
from collections import OrderedDict

start = date(2016,0o1,0o1)
dates = []
cdate = start
while cdate < date(2018,9,1):
    dates.append(cdate)
    cdate = cdate + relativedelta.relativedelta(months=+1)

result = OrderedDict()
c = []
for d in dates:
    tmp = []
    orders = Order.objects.filter(paid_at__gte=d, paid_at__lt=d+relativedelta.relativedelta(months=+1))
    value = len(orders)
    order_countries = orders.values('country').annotate(count=Count('country')).order_by('-count','country')
    result[d] = {'all': value, 'v': order_countries}
    tmp.append(str(d))
    tmp.append(value)
    for o in order_countries:
        tmp.append(o['country'])
        tmp.append(o['count'])
    c.append(tmp)

dump_list_as_csv(c, output=open('/tmp/ben.csv', 'w'),mail=None)