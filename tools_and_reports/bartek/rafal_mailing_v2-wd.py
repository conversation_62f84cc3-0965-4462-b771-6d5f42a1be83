from __future__ import print_function
from custom.models import FurnitureAbstract
from mailing.templates import OldLeadsReactivationMail, OldLeadsReactivationMailPlainText, \
    OldLeadsReactivationKlarnaMailA, OldLeadsReactivationKlarnaMail, OldLeadsExtensionMail, \
    OldLeadsReactivationMailPlainText, NewHeightUpdateNoDrawers, NewHeightUpdateWithDrawers
from django.utils.translation import gettext
from django.utils import translation
from time import sleep

bl_list = []
items_list = []

test = True

def send_20(email, user_id, cart2, whistlist):
    user = User.objects.get(id=user_id)
    context = {
        'ifcart': True if cart2 else False,
        'blacklist_token': RetargetingBlacklistToken.get_or_create_for_email(email=email).token,
        'access_token': LoginAccessToken.get_or_create_for_user(user),
    }
    # print context, cart2, whistlist
    if test == True:
        # email = 'rafal.l<PERSON><PERSON><PERSON><PERSON>@tylko.com'
        email = '<EMAIL>'
        mail = NewHeightUpdateWithDrawers(email, context)
    else:
        mail = NewHeightUpdateWithDrawers(email, context)
    language = 'en'

    if user.profile and user.profile.language:
        language = user.profile.language
    translation.activate(language)
    topic = gettext('mailing_new_heights_drawers_mail_subject_line_1')
    mail.topic = topic
    # <AUTHOR> <EMAIL>'
    # print language
    # if language == 'de':
    # <AUTHOR> <EMAIL>'
    # if language == 'fr':
    # <AUTHOR> <EMAIL>'
    mail.send(language=language)


'email	uid	lang	Cart item count	Wishlist items'
import csv

result = []
with open('/tmp/dr.csv') as csvfile:
    spamreader = csv.reader(csvfile, delimiter=',', quotechar='"')
    for mail, user, lang, cart, whlist in spamreader:
        if user == 'uid':
            continue
        result.append((mail, int(user), int(cart), int(whlist)))

i = 0
ff = []
for mail, user_id, cart, wl in result:
    if i <= 400:
        i += 1
        continue
    try:
        send_20(mail, user_id, cart, wl)
    except:
        ff.append((mail, user_id))
    if i == 100:
        print('sleep 10 minut')
        sleep(600)
    i += 1
    print('sending to {}'.format(mail))
    if i % 200 == 0 and i != 200:
        print('going to bed {}'.format(i))
        sleep(60)
        # break
print('done')
