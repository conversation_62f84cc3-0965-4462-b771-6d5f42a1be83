from django.db import transaction

from gallery.models import SampleBox
from logistic.models import LogisticOrder
from orders.models import Order

ids = [59634769, 58238977, 58647046]

orders = Order.objects.filter(id__in=ids)
with transaction.atomic():
    for order in orders:
        for item in order.items.all():
            item_entry = item.order_item
            if isinstance(item_entry, SampleBox):
                print(item_entry.pk, order.pk)
                order.parent_order_id = order.pk
                order.pk = None
                order.save()
                print('new', order.pk)
                item.order = order
                item.save()
                logistic_entry = LogisticOrder()
                logistic_entry.order = order
                logistic_entry.save()
