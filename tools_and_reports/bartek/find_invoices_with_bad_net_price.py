from custom.utils.exports import dump_list_as_csv
from invoice.models import Invoice

a = Invoice.objects.filter(status__in=[0, ]).order_by("pk")

to_correct = []
for inv in a:
    dd = inv.to_dict()
    d = inv.get_reporting_dict()
    if sum([x['net_price'] for x in dd['items']]) != d['Cena jednostkowa przed rabatem EUR']:
        to_correct.append(['ZLA', inv.pretty_id, sum([x['net_price'] for x in dd['items']]), dd['net_value'], d['Cena jednostkowa przed rabatem EUR']])
    else:
        to_correct.append(['Dobra', inv.pretty_id, sum([x['net_price'] for x in dd['items']]), dd['net_value'], d['Cena jednostkowa przed rabatem EUR']])


headers = ['typ', 'pretty', 'net_price', 'net_value', 'good net_price']

dump_list_as_csv(to_correct, output=open('/tmp/net_price_fault_0.csv', 'w'), mail=None, headers=headers)