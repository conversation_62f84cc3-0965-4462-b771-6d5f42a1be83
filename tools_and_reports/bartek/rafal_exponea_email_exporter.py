from custom.utils.exports import dump_list_as_csv
from datetime import date
import time
# UserProfile.objects.filter(profile_email__isnull=False, profile_email__contains='@')

users = User.objects.filter(Q(profile__user_type__in=[2,3], email__contains='@')|Q(profile__email__isnull=False, profile__email__contains='@'))

user_list = []
bl = [x.email for x in RetargetingBlacklist.objects.all()]
bl2 = [x.email for x in Blacklist.objects.all()]
bl3 = [x.email for x in ProductDeliveredBlacklistedEmail.objects.all()]

black_list = list(tuple(bl + bl2 + bl3))


# date

for u in users:
    obj = [u.email if '@' in u.email else u.profile.email if '@' in u.profile.email else 1/0,
           'accept' ,'newsletter', 'import','unlimited', int(time.mktime(u.date_joined.timetuple()))]
    if obj[0] in black_list:
        obj[1] = 'rejected'
        # print obj
        black_list.remove(obj[0])

    user_list.append(obj)
    del obj
for b in black_list:
    obj = [b ,
           'rejected' ,'newsletter', 'import','unlimited', int(time.mktime(date(2017,0o1,0o1).timetuple()))]
    user_list.append(obj)
    del obj

headers = ['email_id', 'action', 'category', 'source', 'valid_until', 'timestamp']

dump_list_as_csv(user_list, output=open('/tmp/exponea.csv', 'w'), mail=None, headers=headers)