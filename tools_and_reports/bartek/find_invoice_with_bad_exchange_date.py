from datetime import timedelta, date
from custom.utils.exports import dump_list_as_csv
from invoice.models import Invoice

a = Invoice.objects.filter(sell_at__gte="2018-01-01", status__in=[0, 4]).filter(pdf='').order_by("pk")

to_correct = []
for b in a:
    if (b.sell_at.date() != date(2018, 11, 0o2) and b.sell_at.weekday() not in [6,0]):
        if (b.sell_at.date() - b.exchange_date).days != 1:
            #Should be day before'
            to_correct.append((b, b.sell_at.date() - timedelta(days=1)))
    elif b.sell_at.date() == date(2018, 11, 0o2) and b.exchange_date != date(2018, 10, 31):
        #SWIETO
        to_correct.append((b, date(2018, 10, 31)))
    elif b.sell_at.weekday() in [6, 0]:
        #weekend
        if (b.sell_at.weekday() == 0 and (b.sell_at.date() - b.exchange_date).days != 3) or \
                (b.sell_at.weekday() == 6 and (b.sell_at.date() - b.exchange_date).days != 2):
            if b.sell_at.weekday() == 0:
                to_correct.append((b, b.sell_at.date() - timedelta(days=3)))
            else:
                to_correct.append((b, b.sell_at.date() - timedelta(days=2)))

# prosba tak samo csv z tym pierwszym to_correct,  z numerem zamowienia, numerem faktury, sell_at, data kursu z faktury, data kursu która powinna być wg nas
result = []
for inv,days_diff in to_correct:
    result.append((inv.order.id, inv.id, inv.pretty_id, inv.sell_at, inv.exchange_date, days_diff))

headers = ['Order id', 'invoice id', 'Invoice', 'Sell at', 'Current exchange date', 'Proper exchange date']

dump_list_as_csv(result, output=open('/tmp/exchange_diff.csv', 'w'), mail=None, headers=headers)