from __future__ import print_function
from datetime import timedelta, date
from django.db.models import Count, Q

a = PaidOrders.objects.filter(paid_at__gte="2018-01-01").exclude(total_price=0)

for order in a:
    order.invoice_count = order.invoice_set.filter(status=0).count()

to_correct = []
for order in a:
    if order.invoice_count == 0 and order.chosen_payment_method not in ['klarna', 'klarna_account']:
        to_correct.append([order.id, order.paid_at, order.chosen_payment_method, order.total_price, order.invoice_set.count(), ','.join(['%s %s' % (x.id, x.get_status_display()) for x in order.product_set.all()])])

print(to_correct)
len(to_correct)


#

from collections import Counter
Counter([x[2] for x in to_correct])

import unicodecsv as csv

with open('/tmp/missing_invoice.csv', mode='w') as f:
    employee_writer = csv.writer(f, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
    for line in to_correct:
        employee_writer.writerow(line)
