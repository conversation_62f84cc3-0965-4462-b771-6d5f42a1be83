from __future__ import print_function
from datetime import datetime
from decimal import Decimal

from tqdm import tqdm

from custom.utils.exports import dump_list_as_csv
from invoice.models import Invoice, SymfoniaFConfiguration

vc = Invoice.objects.filter(pretty_id__startswith='VC', order__country='united_kingdom')
corr = Invoice.objects.filter(issued_at__gte='2019-07-31', issued_at__lte='2019-08-01', order__country='united_kingdom', status =4)
rc = Invoice.objects.filter(pretty_id__startswith='RV', order__country='united_kingdom', status =0)

def export_to_fka(invoices, name):
    from subprocess import Popen
    headers = 'data_wystawienia', 'data_sprzedazy', 'data_platnosci', 'skrot', 'typ_dokumentu', 'numer_ewidencyjny', \
              'numer_faktury', 'tresc_dokumentu', 'netto_calego_dokumentu', 'brutto_calego_dokumentu', \
              'vat_calego_dokumentu', 'waluta', 'kurs', 'konto_1', 'kwota_1', 'konto_2', 'kwota_2', 'konto_3',\
              'kwota_3', 'konto_4', 'kwota_4', 'konto_5', 'kwota_5', 'netto_27', 'vat_27', 'netto_26', 'vat_26',\
              'netto_25', 'vat_25', 'netto_24', 'vat_24', 'netto_23', 'vat_23', 'netto_22', 'vat_22', 'netto_21', \
              'vat_21', 'netto_20', 'vat_20', 'netto_19', 'vat_19', 'netto_3', 'vat_3', 'netto_0', 'vat_0', \
              'dok_powiazany', 'data_powiazana', \
              'rodzaj_transakcji', 'okres sprawozdawczy', 'okres rejestru', 'NIP', 'Ulica', 'kod_pocztowy', \
              'miasto', 'kraj'

    doc = []
    for i in  tqdm(invoices):
        export = i.generate_date_for_symfonia_export()
        to_dict = i.to_dict()
        nazwa = export[0]['kodOdKH']
        document_type = export[0]['typ_dk']
        try:
            attach = Invoice.objects.get(order=i.order.pk, status=InvoiceStatus.ENABLED)
        except:
            attach = i.corrected_invoice
        if document_type.lower() in ['wdt', 'dex']:
            dk = document_type
            register_date = i.issued_at.strftime('%d.%m.%Y')
        else:
            dk = document_type.split('_')
            TWOPLACES = Decimal(10) ** -2
            register_date = i.sell_at.strftime('%d.%m.%Y')
            if Decimal(to_dict['vat_rate']).quantize(TWOPLACES) in [Decimal(0.23).quantize(TWOPLACES), Decimal(0)]:
                dk = u'F_{}'.format(dk[0])
            else:
                dk = u'F2{}'.format(dk[0])
        dk_fks = False
        if i.status == InvoiceStatus.CORRECTING:
            att, att_date = attach.pretty_id, attach.issued_at.strftime('%d.%m.%Y')
            diff = i.to_diff_dict()
            netto, brutto, vat = diff['net_value'], diff['total_value'], diff['total_value'] - diff['net_value']
            reason = i.corrected_notes
            dk_fks = True
            name = 'FKS'
            if dk in ['DEX', 'WDT'] or '2' in dk:
                name = dk
        else:
            netto, brutto, vat = to_dict['net_value'], to_dict['total_value'], to_dict['vat_value']
            reason = ''
            att, att_date = '', ''
        reporting_date = i.issued_at.strftime('%d.%m.%Y')
        fk = SymfoniaFConfiguration.objects.filter(document_type=dk).last()
        if not fk:
            fk = SymfoniaFConfiguration.objects.filter(document_type=dk)
            print(fk.query)
            fk = SymfoniaFConfiguration.objects.filter(document_type=dk).last()
            print(dk)
        #     self.message_user(request, "No SymfoniaFConfiguration (https://tylko.com/admin/invoice/symfoniafconfiguration/) for document type {} generated by invoice {}".format(dk, i.pretty_id), level=logging.ERROR)
        #     # continue;
        #     return
        if not reason:
            opis = 'Order no.{}'.format(i.order.pk)
        else:
            opis = reason
        unformated_row = {'data_wystawienia': i.issued_at.strftime('%d.%m.%Y'),
                          'data_sprzedazy': i.sell_at.strftime('%d.%m.%Y'),
                          'data_platnosci': i.sell_at.strftime('%d.%m.%Y'),
                          'skrot': nazwa, 'typ_dokumentu': fk.document_type if not dk_fks else name,
                          'numer_faktury': i.pretty_id,
                          'tresc_dokumentu': opis,
                          'netto_calego_dokumentu': netto, 'brutto_calego_dokumentu': brutto,
                          'vat_calego_dokumentu': vat, 'waluta': export[0]['waluta'],
                          'kurs': to_dict['exchange_rate'], 'konto_1': '203-3-1-', 'kwota_1': brutto,
                          'konto_2': fk.account, 'kwota_2': netto,
                          'konto_3': fk.vat_account,
                          'kwota_3': vat,
                          'dok_powiazany': att, 'data_powiazana': att_date,
                          'rodzaj_transakcji': 'sprzedaż', 'okres sprawozdawczy': reporting_date,
                          'okres rejestru': register_date,
                          'NIP': export[1]['nip'] if 'nip' in export[1] else '' if not export[1]['osfiz'] else '',
                          'Ulica': export[1]['ulica'], 'kod_pocztowy': export[1]['kod'],
                          'miasto': export[1]['miejscowosc'],
                          'kraj': export[0]['khKrajKod']
                          }
        vat_rate = to_dict['vat_rate']
        netto_k, vat_k = 'netto_{0},vat_{0}'.format(int(vat_rate*100)).split(',')
        unformated_row[netto_k] = netto
        unformated_row[vat_k] = vat
        row = [(lambda a:unformated_row[a] if a in unformated_row else '')(x) for x in headers]
        doc.append(row)
    symfonia_export = '/tmp/symfonia_export_big'
    dump_list_as_csv(doc, output=open('{}_{}.csv_tmp'.format(symfonia_export, name), 'w'), mail=None, headers=headers,
                     delimiter=';')
    f_name = '{}_{}.csv'.format(symfonia_export, name)
    call = Popen('iconv -c -f UTF-8 -t cp1250 {0}_tmp >{0}'.format(
        f_name), shell=True)
    call.wait()
    invoices.update(exported_to_symfonia_f=datetime.now())
    # response = HttpResponse(content_type='text/plain; charset=iso-8859-2')
    # response['Content-Disposition'] = 'attachment; filename=%s' % smart_str('symfonia_{}.csv'.\
    #                                                                         format(datetime.today().isoformat()))
    # with open(f_name, 'r') as fp:
    #     data = fp.read()
    # response.write(data)
    # return response

vc = Invoice.objects.filter(pretty_id__startswith='VC', order__country='united_kingdom').order_by('id')
corr = Invoice.objects.filter(issued_at__gte='2019-07-31', issued_at__lte='2019-08-01', order__country='united_kingdom', status =4).order_by('id')
rv = Invoice.objects.filter(pretty_id__startswith='RV', order__country='united_kingdom', status =0).order_by('id')

export_to_fka(vc, 'vc')
export_to_fka(corr, 'corr')
export_to_fka(rv , 'rv')
