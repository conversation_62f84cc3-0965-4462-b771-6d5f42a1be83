from django.db import (
    connection,
)
from custom.constants import VAT_NORMAL
from invoice.choices import NumerationType
from invoice.models import Invoice
from invoice.submodels.models1 import InvoiceSequence

corrections = """Order: 21194232, FKS0017/05/2020/21194232/ES - korekta do usunięcia
Order: 17029457, FKS0016/05/2020/17029457/UK - korekta do usunięcia
Order: 21423614, RV/FKS0013/05/2020/21423614/UK - korekta do usunięcia
Order: 21388727, RV/FKS0006/05/2020/21388727/FR - korekta do usunięcia
Order: 14996835, FKS0015/05/2020/14996835/CH - korekta do usunięcia
Order: 10496835, RV/FKS0012/05/2020/10496835/UK - korekta do usunięcia
Order: 20100792, RV/FKS0002/05/2020/20100792/NL - korekta do usunięcia
Order: 29902075, RV/FKS0006/05/2020/29902075/BE - korekta do usunięcia
Order: 31065054, RV/FKS0011/05/2020/31065054/UK - korekta do usunięcia
Order: 28417028, RV/FKS0010/05/2020/28417028/UK - korekta do usunięcia
Order: 31521746, RV/FKS0009/05/2020/31521746/UK - korekta do usunięcia
Order: 25851235, FKS0014/05/2020/25851235/ES - korekta do usunięcia
Order: 31739293, FKS0013/05/2020/31739293/LU - korekta do usunięcia
Order: 28244728, RV/FKS0008/05/2020/28244728/UK - korekta do usunięcia
Order: 31733885, RV/FKS0005/05/2020/31733885/BE - korekta do usunięcia
Order: 31839144, RV/FKS0005/05/2020/31839144/FR - korekta do usunięcia
Order: 31191950, RV/FKS0024/05/2020/31191950/DE - korekta do usunięcia
Order: 31346388, RV/FKS0014/05/2020/31346388/UK - korekta do usunięcia
Order: 30443725, RV/FKS0023/05/2020/30443725/DE - korekta do usunięcia
Order: 25724006, RV/FKS0022/05/2020/25724006/DE - korekta do usunięcia
Order: 30042447, RV/FKS0021/05/2020/30042447/DE - korekta do usunięcia
Order: 24816636, RV/FKS0008/05/2020/24816636/BE - korekta do usunięcia
Order: 31092250, RV/FKS0020/05/2020/31092250/DE - korekta do usunięcia
Order: 31414656, FKS0024/05/2020/31414656/AT - korekta do usunięcia
Order: 12248354, FKS0023/05/2020/12248354/CH - korekta do usunięcia
Order: 21191694, RV/FKS0008/05/2020/21191694/FR - korekta do usunięcia
Order: 17459215, FKS0022/05/2020/17459215/DE - korekta do usunięcia
Order: 20087330, FKS0021/05/2020/20087330/CH - korekta do usunięcia
Order: 10297307, FKS0020/05/2020/10297307/BE - korekta do usunięcia
Order: 20627416, RV/FKS0007/05/2020/20627416/FR - korekta do usunięcia
Order: 18884893, RV/FKS0019/05/2020/18884893/DE - korekta do usunięcia
Order: 18991455, RV/FKS0018/05/2020/18991455/DE - korekta do usunięcia
Order: 18847131, RV/FKS0017/05/2020/18847131/DE - korekta do usunięcia
Order: 5803613,  FKS0019/05/2020/5803613/CH - korekta do usunięcia
Order: 17160116, RV/FKS0016/05/2020/17160116/DE - korekta do usunięcia
Order: 20681469, FKS0018/05/2020/20681469/CH - korekta do usunięcia
Order: 20839733, RV/FKS0015/05/2020/20839733/DE - korekta do usunięcia
Order: 20856940, RV/FKS0014/05/2020/20856940/DE - korekta do usunięcia
Order: 21272662, RV/FKS0007/05/2020/21272662/BE - korekta do usunięcia
Order: 28819979, RV/FKS0025/05/2020/28819979/DE - korekta do usunięcia
"""


# corrections = """Order: 20100792, RV/FKS0002/05/2020/20100792/NL - korekta do usunięcia
# """

def cleaning(minus=0):
    total = 0
    for correction_line in corrections.splitlines():
        inv_number = correction_line.split(',')[1].strip().split('-')[0].strip()
        inv = Invoice.objects.filter(pretty_id=inv_number).last()
        if not inv:
            continue
        print(inv_number)
        if inv.order.region_vat and inv.order.vat_type == VAT_NORMAL:
            numeration_type = NumerationType.RV
            prefix = 'RV/'
        else:
            numeration_type = NumerationType.NORMAL

        invoice_country = (
            inv.order.country
            if numeration_type != NumerationType.NORMAL
            else 'poland'
        )
        seq = InvoiceSequence.objects.get(
            country__name=invoice_country,
            invoice_type=inv.status,
            numeration_type=numeration_type,
        )
        month = inv.issued_at.month
        year = inv.issued_at.year
        seq = '_invoice_{}_{}_{}_{}_{}'.format(seq.country.name, seq.numeration_type,
                                               seq.invoice_type,
                                               year, month)

        with connection.cursor() as cursor:
            cursor.execute('select last_value from ' + seq)
            result = cursor.fetchone()

        inv_number = inv_number.replace('RV/', '')
        inv_number = inv_number.split('/')[0]
        inv_number = inv_number.replace('FKS', '')
        inv_number = int(inv_number)
        print('{}, {}'.format(inv_number, result[0] - minus))
        if inv_number == result[0] - minus:
            total += 1
            print('in')
            sql = 'alter sequence {} restart with {}'.format(seq, result[0] - minus)
            # next call for sequence return this value, because is not called
            with connection.cursor() as cursor:
                cursor.execute(sql)
            print(inv.pk, sql)
            inv.delete()
    if total:
        cleaning(minus=1)
