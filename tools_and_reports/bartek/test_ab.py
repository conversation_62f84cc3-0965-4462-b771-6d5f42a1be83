
from __future__ import print_function
def test(name):
    from django_redis import get_redis_connection
    r = get_redis_connection()

    for z in ['total', 'request', 'respone']:
        print(z)
        results = r.lrange('cookie_{}_{}'.format(z,name),0, r.llen('cookie_{}_{}'.format(z, name)))

        ok, nook = 0,0

        for element in results:
            if element == 'ok':
                ok +=1
            else:
                nook +=1
        print(ok,nook, len(results))
        if results:
            print(ok/float(len(results)), nook/float(len(results)))
