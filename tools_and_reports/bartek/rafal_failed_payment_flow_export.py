from datetime import timedelta, date
from custom.utils.exports import dump_list_as_csv
from orders.enums import OrderStatus

t = Transaction.objects.filter(order__status__in=[OrderStatus.PAYMENT_FAILED, OrderStatus.DRAFT, OrderStatus.PAYMENT_PENDING],
                                                    notification__success=False,
                                                    updated_at__lt=timezone.now() - timedelta(minutes=30),
                                                    updated_at__gte="2018-10-29",
                                                    order__email__isnull=False).\
                exclude(payment_method__in=['Bank Transfer',]).distinct('order').distinct('order__owner')
result = []

for r in t:
    result.append((r.order.id, r.order.owner.id, r.order.email, r.order.total_price, r.notification_set.last().event_date))

headers = ['order id', 'owner id', 'email', 'total-price', 'date']

dump_list_as_csv(result, output=open('/home/<USER>/Dokumenty/adyen/rafal.csv', 'w'), mail=None, headers=headers)