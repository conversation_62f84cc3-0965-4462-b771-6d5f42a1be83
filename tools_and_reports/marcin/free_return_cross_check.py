# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from __future__ import print_function
import os

fh = open("asd.csv", "r")

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cstm_be.settings")
os.environ.setdefault("DJANGO_IS_DEV", '1')
os.chdir('../../src')

import django
django.setup()

from free_returns.models import FreeReturn
import csv
from orders.models import Order

# def main():
#     with open('')
#
#     fr = FreeReturn.objects.all()
#     for item in fr:
#         print item


if __name__ == '__main__':
    csv_iterator = csv.reader(fh, delimiter=str(';'))
    state = None
    order = None
    for line in csv_iterator:
        if line[0] == 'Order':
            state = line[1]
            order = Order.objects.get(pk=int(line[1]))
        else:
            if line[8].strip() == "Free Return":
                le = order.looseend_set.filter(category="free_return")
            elif line[8].strip() == "Free Return Packaging Kit":
                le = order.looseend_set.filter(category="free_return_kit")
            else:
                print('brak loose enda dla {}'.format(order))
            if le.count() > 0:
                if float(list(le.values('value'))[0]['value']) - float(line[5]) != 0:
                    print('{} - {} - {} - {}'.format(order, list(le.values('value'))[0]['value'], line[5], float(list(le.values('value'))[0]['value']) - float(line[5])))

        # print line
