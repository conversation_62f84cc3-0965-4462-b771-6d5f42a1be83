# -*- coding: utf-8 -*-
import pandas as pd
from django.db import connection
from collections import OrderedDict

def dictfetchall(cursor):
    # type: (cursor.connection) -> [{}]
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        OrderedDict(list(zip(columns, row)))
        for row in cursor.fetchall()
    ]

sql = """SELECT 
lo.tracking_number, o.owner_id, to_char(lo.delivered_date,'MM') as MM,
       extract(year from lo.delivered_date) as YYYY, count(*) as cnt
	FROM public.logistic_logisticorder as lo
	LEFT JOIN public.orders_order as o 
		ON (o.id = lo.order_id)
		WHERE lo.delivered_date BETWEEN '2018-10-01' AND '2019-04-01'
	GROUP BY 1,2,4,3
	HAVING count(*) > 1
	ORDER BY 4,3 ASC"""



with connection.cursor() as cursor:
    cursor.execute(sql)
    df = pd.DataFrame.read_records(dictfetchall(cursor))
    df.to_excel('file.xlsx', sheet_name='Sheet1')