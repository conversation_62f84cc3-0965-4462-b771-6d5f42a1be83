import asyncio
import base64
import io
import json
import aiohttp
import time
from base64 import encode

# CHANGE THIS

CONSUMER_NAME = 'Slawek - Dell'
CSTM_API_BASE_URL = 'https://chmiel.b.tylko.com'

# RATHER DONT CHANGE THAT

BLENDER_API_BASE_URL = 'http://localhost:6400'
SLACK_WEBHOOK = '*******************************************************************************'

headers = {
    'User-Agent': CONSUMER_NAME
}


async def check_for_task(client: aiohttp.ClientSession):
    i = 0
    tasks_performance = []

    no_task_info_on = 10
    task_info_on = 1

    await send_to_slack(f'Starting up, getting tasks!', client)
    while True:
        try:
            task_response = await client.post(
                f'{CSTM_API_BASE_URL}/api/v1/render_tasks/get_task/',
                headers=headers,
                raise_for_status=True,
            )
        except (
            aiohttp.client_exceptions.ClientConnectorError,
            aiohttp.ClientResponseError,
            aiohttp.client_exceptions.ClientOSError,
        ) as e:
            print(f'Something went wrong: {e}')
            await asyncio.sleep(5)
            continue
        if task_response.status == 200:
            task = await task_response.json()
            if i == task_info_on:
                await send_to_slack(get_perfo_report(tasks_performance), client)
                if task_info_on == 1:
                    task_info_on = 10
                elif task_info_on == 10:
                    task_info_on = 100
                else:
                    task_info_on += 100

            print("Got task", task['id'], task['description'])
            gallery_json = task['furniture_json']
            render_parameters = task['image_config']
            render_start_time = time.time()
            image_result = await run_blender_render(
                client=client,
                gallery_json=gallery_json,
                render_parameters=render_parameters,
            )
            tasks_performance.append(int(time.time() - render_start_time))
            await publish_result(result_image=image_result, task=task, client=client)
        elif task_response.status == 204:
            print("no_tasks")
            if i == no_task_info_on:
                await send_to_slack('No tasks :(', client)
                if no_task_info_on == 1:
                    no_task_info_on = 10
                elif no_task_info_on == 10:
                    no_task_info_on = 100
                else:
                    no_task_info_on += 100
            await asyncio.sleep(1)
        i += 1


async def publish_result(result_image, task, client: aiohttp.ClientSession):
    response = await client.put(
        f'{CSTM_API_BASE_URL}/api/v1/render_tasks/{task["id"]}/finish_task/', json={
            'image': base64.b64encode(result_image).decode('utf-8'),
            'furniture_json': task['furniture_json'],
            'image_config': task['image_config'],
        })
    print('Published result', task['description'])


async def run_blender_render(
    client: aiohttp.ClientSession,
    gallery_json: dict,
    render_parameters: dict
) -> bytes:
    blender_response = await client.post(url=BLENDER_API_BASE_URL + '/render', json={
        "jetty": gallery_json,
        "parameters": render_parameters,
    })
    # todo handle 400 / 503
    blender_response.raise_for_status()
    blender_response_json = await blender_response.json()
    task_id = blender_response_json.get('id')
    task_state = blender_response_json.get('state')
    while task_state != 'COMPLETED':
        await asyncio.sleep(10)
        blender_response = await client.get(
            url=BLENDER_API_BASE_URL + f'/render/{task_id}/status'
        )
        task_state = (await blender_response.json()).get('state')

    # get image:
    blender_response = await client.get(
        url=BLENDER_API_BASE_URL + f'/render/{task_id}'
    )
    return await blender_response.content.read()


async def send_to_slack(message, client):
    formatted_message = f'<{CONSUMER_NAME} -> {CSTM_API_BASE_URL}> {message}'
    print(formatted_message)
    await client.post(SLACK_WEBHOOK, json={'text': formatted_message})


def get_perfo_report(task_lists):
    number_of_tasks_done = len(task_lists)
    avg = sum(task_lists) / number_of_tasks_done
    return f'Perfo after {number_of_tasks_done} renders: avg: {avg:.2f}s, min: {min(task_lists)}s, max: {max(task_lists)}s'


async def get_sessions():
    print('Creating client')
    return aiohttp.ClientSession()


if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    _client = loop.run_until_complete(get_sessions())
    loop.create_task(check_for_task(client=_client))
    loop.run_forever()
