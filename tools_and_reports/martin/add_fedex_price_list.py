import datetime
from decimal import Decimal

from logistic.submodels.shipping import Shipper, ShipmentPriceList, \
    ShipmentPrice
from regions.models import Country

FEDEX_PRICE_LIST = (
    (Dec<PERSON>l('1'), Decimal('31')),
    (Decimal('2'), Decimal('31')),
    (Decimal('3'), Decimal('31')),
    (Decimal('4'), Decimal('44.38')),
    (Decimal('5'), Dec<PERSON>l('47.8')),
    (Decimal('6'), Decimal('51.21')),
    (Decimal('7'), Decimal('54.62')),
    (Dec<PERSON><PERSON>('8'), Decimal('58.03')),
    (<PERSON><PERSON><PERSON>('9'), <PERSON><PERSON><PERSON>('61.45')),
    (<PERSON><PERSON><PERSON>('10'), <PERSON><PERSON><PERSON>('64.86')),
    (Dec<PERSON><PERSON>('11'), Dec<PERSON>l('102.98')),
    (<PERSON><PERSON><PERSON>('12'), Dec<PERSON><PERSON>('103.56')),
    (<PERSON><PERSON><PERSON>('13'), Dec<PERSON>l('104.14')),
    (<PERSON><PERSON><PERSON>('14'), <PERSON><PERSON><PERSON>('104.71')),
    (<PERSON><PERSON><PERSON>('15'), <PERSON><PERSON><PERSON>('105.29')),
    (<PERSON><PERSON><PERSON>('16'), Decimal('105.86')),
    (Decimal('17'), Decimal('106.44')),
    (Decimal('18'), Decimal('107.02')),
    (Decimal('19'), Decimal('107.59')),
    (Decimal('20'), Decimal('108.17')),
    (Decimal('21'), Decimal('108.74')),
    (Decimal('22'), Decimal('109.32')),
    (Decimal('23'), Decimal('109.9')),
    (Decimal('24'), Decimal('110.47')),
    (Decimal('25'), Decimal('111.05')),
    (Decimal('26'), Decimal('111.62')),
    (Decimal('27'), Decimal('112.2')),
    (Decimal('28'), Decimal('112.78')),
    (Decimal('29'), Decimal('113.35')),
    (Decimal('30'), Decimal('113.93')),
    (Decimal('40'), Decimal('126.64')),
    (Decimal('45'), Decimal('132.98')),
    (Decimal('50'), Decimal('139.33')),
    (Decimal('55'), Decimal('145.68')),
    (Decimal('60'), Decimal('152.03')),
    (Decimal('65'), Decimal('158.38')),
    (Decimal('70'), Decimal('164.72')),
    (Decimal('80'), Decimal('171.46')),
    (Decimal('90'), Decimal('191.92')),
    (Decimal('100'), Decimal('212.38')),
)


shipper = Shipper.objects.get(name='FEDEX')

price_list = ShipmentPriceList.objects.create(
    shipper=shipper,
    valid_to_date=datetime.date(2030, 1, 1),
    country=Country.objects.filter(name='austria').last()
)

for weight, price in FEDEX_PRICE_LIST:
    ShipmentPrice.objects.create(price_list=price_list, weight=weight, price=price)
