import datetime

from logistic.submodels.shipping import ShipperConfiguration, Shipper

from regions.models import Country

list = {
    'czech': {
        'sample_box_5_kg': 33.5,
        'sample_box_10_kg': 35.6,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'germany': {
        'sample_box_5_kg': 33.5,
        'sample_box_10_kg': 35.6,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'lithuania': {
        'sample_box_5_kg': 33.5,
        'sample_box_10_kg': 35.6,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'slovakia': {
        'sample_box_5_kg': 33.5,
        'sample_box_10_kg': 35.6,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'austria': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'belgium': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'denmark': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'france': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'hungary': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'italy': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'luxembourg': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'netherlands': {
        'sample_box_5_kg': 38.74,
        'sample_box_10_kg': 41.86,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'croatia': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'estonia': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'finland': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'greece': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'ireland': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'latvia': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'portugal': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'spain': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'sweden': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.28,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'norway': {
        'sample_box_5_kg': 109.94,
        'sample_box_10_kg': 115.15,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'switzerland': {
        'sample_box_5_kg': 109.94,
        'sample_box_10_kg': 115.15,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
    'united_kingdom': {
        'sample_box_5_kg': 47.12,
        'sample_box_10_kg': 50.26,
        'additional_surcharge': 4.1,
        'fuel_surcharge': 1.1675,
    },
}


shipper = Shipper.objects.get(name='FEDEX')

for key, value in list.items():
    country = Country.objects.filter(name=key).last()

    ShipperConfiguration.objects.create(
        country=country,
        shipper=shipper,
        valid_to_date=datetime.date(2030, 1, 1),
        configuration=value,
    )

