"""Template for generating presets in new geometry in case we cannot recalculate
geometry in production.
In other case we can simply run NewPresetsGenerator and then recalculate geometry
of the new furniture in screentool action directly on production:
tylko.com/admin/grid_screentool(_cplus)/?recalculateGeometry=true&ids=<new_furniture_ids>
"""

from catalogue.models import CatalogueEntry
from catalogue.services.presets_production.entry_from_furniture_creator import \
    EntriesCreator
from catalogue.services.presets_production.new_presets_generator import \
    NewPresetsGenerator
from gallery.enums import (
    FurnitureCategory,
    ShelfPatternEnum,
)
from gallery.models import Jetty


# TEST ENVIRONMENT #####################################################################

# 1. Get base entries from which we will change geometry, f.ex.:
qs = CatalogueEntry.objects.filter(
    category__in={FurnitureCategory.BOOKCASE, FurnitureCategory.WALL_STORAGE},
    material=0,
)
base_entries = [entry for entry in qs if entry.furniture.pattern == ShelfPatternEnum.SLANT]

# 2. Generate furniture in new geometry
new_furniture_ids = NewPresetsGenerator(
        base_entries=base_entries,
        new_styles=[ShelfPatternEnum.MOSAIC, ShelfPatternEnum.PIXEL],
        only_base_new_geometry_furniture=True,
).run()

# 3. Recalculate geometry in screentool action
# <env.url>/admin/grid_screentool(_cplus)/?recalculateGeometry=true&ids=<new_furniture_ids>

# 4. Make sure that the geometry has been recalculated properly.

# 5. Send furniture to bag by admin action in gallery


# BAG ##################################################################################
# 6. Send furniture to cstm by admin action in https://bag.oklyt.pl/admin/presets/presetdefinition/


# PRODUCTION ###########################################################################
# 7. Create new presets in all colors for the imported furniture with ordering blender
new_production_ids = [] # ids of new presets in production
jetties = Jetty.objects.filter(id__in=new_production_ids)
EntriesCreator(jetties, enabled=False).run()   # blender images are ordered by default in this class


# PRODUCTION AFTER MERGING THE CONFIGURATOR ############################################
# 8. Run screentool actions for new_production_ids
# tylko.com/admin/grid_screentool(_cplus)/?(libitemsEnabled=true&)ids=<new_production_ids>
