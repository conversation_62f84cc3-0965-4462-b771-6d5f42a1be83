"""Template for generating presets from furniture designed by our designers."""
from catalogue.models import CatalogueEntry
from catalogue.services.presets_production.entry_from_furniture_creator import \
    EntriesCreator
from gallery.enums import (
    FurnitureCategory,
    ShelfPatternEnum,
)
from gallery.models import Jetty


# TEST ENVIRONMENT #####################################################################
# 1. Get base furniture created by our designers, f.ex.:
designer_jetties = Jetty.objects.filter(
    pattern__in={ShelfPatternEnum.PIXEL, ShelfPatternEnum.MOSAIC},
    preset=True,
    category=FurnitureCategory.SIDEBOARD,
)
# 1.a Optionally you can create veneer furniture from the designer furniture.
# 1.b Recalculate geometry in screentool action
# <env.url>/admin/grid_screentool(_cplus)/?recalculateGeometry=true&ids=<veneer_furniture_ids>

# 2. Send furniture to bag by admin action in gallery

# BAG ##################################################################################
# 3. Send furniture to cstm by admin action in https://bag.oklyt.pl/admin/presets/presetdefinition/

# PRODUCTION ###########################################################################
# 4. Create new presets in all colors for the imported furniture with ordering blender
new_production_ids = []  # ids of new presets in production
jetties = Jetty.objects.filter(id__in=new_production_ids)
EntriesCreator(jetties, enabled=False).run()   # blender images are ordered by default in this class

# 5. Check the filters from this spreadsheet:
# https://docs.google.com/spreadsheets/d/1yscT229EjKGQWTS6bnHQexhWdm96WS8Fl3RrrqDFNXU/edit#gid=0
# and decide if we need to create more presets (f.ex. if the lowest furniture are missing)
# after filtering the catalogue, f.ex.:
sideboard_pixel_entries = CatalogueEntry.objects.filter(
    category=FurnitureCategory.SIDEBOARD,
    enabled=False,
    attributes__name='Pixel',
)
sideboard_pixel_entries.filter(height__lte=750).count()

# 5.a If more presets need to be created, return to the test environment and
# re-run steps 1-5. You can manually create any missing presets if only few are missing,
# or automatically. In the latter case, ensure that the geometry is recalculated and
# create the screentool images to check if the new presets are esthetically acceptable

# PRODUCTION AFTER MERGING THE CONFIGURATOR ############################################
# 8. Run screentool actions for new_production_ids
# tylko.com/admin/grid_screentool(_cplus)/?(libitemsEnabled=true&)ids=<new_production_ids>
