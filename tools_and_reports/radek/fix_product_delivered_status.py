from orders.enums import OrderStatus
from orders.models import Order
from producers.models import Product
from producers.choices import ProductStatus

def fix_product_delivered_to_customer_status_by_order():
    delivered_orders = Order.objects.filter(status=OrderStatus.DELIVERED)

    for delivered_order in delivered_orders:
        for logistic_order in delivered_order.logistic_info.all():
            product_ids = [
                product['id'] for product in logistic_order.get_active_products()
            ]
            products = Product.objects.filter(id__in=product_ids).exclude(status=ProductStatus.DELIVERED_TO_CUSTOMER)
            for product in products:
                print(product.pk)
                product.status_updater.change_status(ProductStatus.DELIVERED_TO_CUSTOMER)


def fix_product_delivered_to_customer_status_by_product():
    not_delivered_products = Product.objects.filter(order__status=OrderStatus.DELIVERED).exclude(status=ProductStatus.DELIVERED_TO_CUSTOMER)

    for product in not_delivered_products:
        print(product.pk)
        product.status_updater.change_status(ProductStatus.DELIVERED_TO_CUSTOMER)
