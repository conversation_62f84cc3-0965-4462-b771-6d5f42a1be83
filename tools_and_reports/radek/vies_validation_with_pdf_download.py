import os
import zipfile

from checkout.utils import get_vies_vat_validation_pdf
from checkout.vat.validation import decompose_vat_number
from checkout.vat.vies_client import get_vies_response


def save_and_zip_uploaded_file(
    uploaded_file,
    save_dir="/tmp/vies/",
    zip_path="/tmp/vies/files.zip",
):
    os.makedirs(save_dir, exist_ok=True)

    file_path = os.path.join(save_dir, uploaded_file.name)

    with open(file_path, 'wb') as f:
        f.write(uploaded_file.read())

    with zipfile.ZipFile(zip_path, 'a', zipfile.ZIP_DEFLATED) as zip_file:
        zip_file.write(file_path, arcname=uploaded_file.name)

    return zip_path


def validate_vies_and_save_pdfs(vat_numbers: list[str]) -> list:
    retry_requested = []
    for vat in vat_numbers:
        country_code, vat_number = decompose_vat_number(vat)
        try:
            vies_response = get_vies_response(country_code, vat_number)
        except (ConnectionError, TimeoutError, Exception) as e:
            retry_requested.append(vat)
            print(vat, e)
            continue
        print(vat, vies_response['valid'])
        confirmation_pdf = get_vies_vat_validation_pdf(country_code, vat_number)
        save_and_zip_uploaded_file(confirmation_pdf)
    return retry_requested


vats = []
retry_vat_numbers = validate_vies_and_save_pdfs(vats)
if retry_vat_numbers:
    print(f"Retry requested: {retry_vat_numbers}")
