from orders.enums import OrderStatus
from orders.models import Order
from orders.tasks import upgrade_user_profile_to_business_type
from orders.utils import is_business_order_or_email

orders = Order.objects.filter(status__in=[
    OrderStatus.IN_PRODUCTION,
    OrderStatus.SHIPPED,
    OrderStatus.TO_BE_SHIPPED,
    OrderStatus.DELIVERED,
])

for order in orders:
    if is_business_order_or_email(order):
        upgrade_user_profile_to_business_type.delay(order.pk)
