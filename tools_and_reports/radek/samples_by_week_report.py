from datetime import datetime, timedelta
import pandas as pd

from dateutil.rrule import rrule, WEEKLY
from django.contrib.contenttypes.models import ContentType

from custom.utils.report_file import ReportFile
from gallery.models import SampleBox
from orders.models import OrderItem
from warehouse.models import SampleBoxVariant

sample_content_type = ContentType.objects.get_for_model(SampleBox)
start_date = datetime(2023, 1, 1)
end_date = datetime(2024, 9, 30)

weekly_rules = rrule(freq=WEEKLY, dtstart=start_date, until=end_date)

weekly_ranges = [
    (week_start, week_start + timedelta(days=7)) for week_start in weekly_rules
]


def group_by_type(samples):
    samples_by_type = {
        variant.name: 0 for variant in SampleBoxVariant.objects.order_by('id')
    }
    samples_by_type['deleted'] = 0
    for sample in samples:
        if not sample.order_item or not sample.order_item.box_variant:
            samples_by_type['deleted'] += 1
        else:
            samples_by_type[sample.order_item.box_variant.name] += 1
    return samples_by_type


def group_by_weeks():
    by_week = {}
    for start_dt, end_dt in weekly_ranges:
        samples_by_week = OrderItem.objects.filter(
            content_type=sample_content_type,
            order__paid_at__gte=start_dt,
            order__paid_at__lt=end_dt,
        )

        # print(f"{start_dt.date()} to {end_dt.date()}: {samples_by_week.count()}")
        by_week[f"{start_dt.date()} to {end_dt.date()}"] = group_by_type(samples_by_week)

    return by_week


def display_samples_by_weeks(by_week: dict):
    for variant in SampleBoxVariant.objects.order_by('id'):
        print(variant.name)

    for date_range, samples_by_type in by_week.items():
        print(date_range)
        for samples_amount in samples_by_type.values():
            print(samples_amount)


def send_as_mail(samples_by_weeks: dict[dict], email):
    csv = pd.DataFrame(samples_by_weeks).to_csv()
    report_file = ReportFile(content=csv.encode(), name='samples_by_weeks.csv')
    report_file.send_as_email_attachment([email])


samples_by_weeks = group_by_weeks()
display_samples_by_weeks(samples_by_weeks)
