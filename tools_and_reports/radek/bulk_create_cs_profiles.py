from django.core.paginator import Paginator
from tqdm import tqdm

from customer_service.models import CSUserProfile
from user_profile.choices import UserType
from user_profile.models import UserProfile


def bulk_create_cs_user_profiles(user_profiles):
    for page in tqdm(Paginator(user_profiles, 100000)):
        print(page.number)
        cs_user_profiles = []
        for user_profile in page.object_list:
            if not CSUserProfile.should_update_or_create_from_user_profile(user_profile):
                continue

            cs_user_profile = CSUserProfile(
                id=user_profile.id,
                user_id=user_profile.user.id,
                user_type=user_profile.user_type,
                first_name=user_profile.first_name or '',
                last_name=user_profile.last_name or '',
                email=user_profile.email or '',
                user_email=user_profile.user.email or '',
                user_username=user_profile.user.username or '',
                phone=user_profile.phone or '',
                city=user_profile.city or '',
                invoice_city=user_profile.invoice_city or '',
                street_address_1=user_profile.street_address_1 or '',
                invoice_street_address_1=user_profile.invoice_street_address_1 or '',
                street_address_2=user_profile.street_address_2 or '',
                invoice_street_address_2=user_profile.invoice_street_address_2 or '',
            )

            cs_user_profiles.append(cs_user_profile)
        CSUserProfile.objects.bulk_create(cs_user_profiles, ignore_conflicts=True)


user_profiles = UserProfile.objects.exclude(user_type=UserType.GUEST_CUSTOMER).order_by('-id')
bulk_create_cs_user_profiles(user_profiles)

guests = UserProfile.objects.filter(user_type=UserType.GUEST_CUSTOMER).order_by('-id')
bulk_create_cs_user_profiles(guests)

