from django.core.paginator import Paginator
from jsondiff import diff
# poetry add jsondiff

from logistic.models import LogisticOrder
from producers.logistic_serializers import ProductForLogisticOrderSerializer
from producers.models import Product


def check_or_refresh_logistic_order_serialized_products():
    logistic_orders = LogisticOrder.objects.all()

    for page in Paginator(logistic_orders, 1000):
        for logistic_order in page.object_list:
            products = logistic_order.serialized_products
            for from_serialization_product in products:
                try:
                    real_product = Product.objects.get(pk=from_serialization_product['id'])
                except Product.DoesNotExist:
                    print(f'{from_serialization_product["id"]} Product doesnt exist')
                else:
                    serialized_product = ProductForLogisticOrderSerializer(real_product).data

                    difference = diff(serialized_product, from_serialization_product)
                    if difference:
                        print(f'Diff serialization: {real_product.pk}', diff(serialized_product, from_serialization_product))
                        # logistic_order.set_product_cache(serialized_product)
                    else:
                        print(f'Equal serialization: {real_product.pk}')
