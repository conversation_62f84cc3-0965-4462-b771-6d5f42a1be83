from orders.enums import OrderStatus
from orders.models import Order

waiting_for_payments_orders = Order.objects.filter(status=OrderStatus.PAYMENT_PENDING)
in_production_orders = Order.objects.filter(status=OrderStatus.IN_PRODUCTION)
shipped_orders = Order.objects.filter(status=OrderStatus.SHIPPED)
delivered_orders = Order.objects.filter(status=OrderStatus.DELIVERED)

courier_orders = Order.objects.filter(
    assembly=False,
    logistic_info__assembly_service__isnull=True,
    logistic_info__dtf_proposals=None,
)

dedicated_delivery_orders = Order.objects.filter(
    assembly=False,
    logistic_info__assembly_service__isnull=True,
    logistic_info__dtf_proposals__isnull=False,
)


with_assembly_service = Order.objects.filter(
    assembly=True
)
