from decimal import Decimal

import pandas as pd
from openpyxl.reader.excel import load_workbook

from cstm_be.media_storage import private_media_storage
from production_margins.models import ElementsOrder

elements_orders = ElementsOrder.objects.exclude(total_cost=Decimal('0.0')).order_by('-id')


def read_values(f):
    if f.name.endswith('xls'):
        data = pd.read_excel(f)
        return data.values

    wb = load_workbook(filename=f)
    return wb.worksheets[0].values


for elements_order in elements_orders:
    with private_media_storage.open(elements_order.order_file.name, 'rb') as file:
        try:
            values = read_values(file)
        except Exception:
            print(f"Can't open file {elements_order}")
            continue

        for row in list(values):
            if 'Koszt - serwis' in row:
                service_cost = row[-1].replace(' zł', '')
                print(elements_order.pk, service_cost)
                elements_order.service_cost = service_cost
                elements_order.save(update_fields=['service_cost'])
                break
        else:
            print('not found', elements_order.pk, elements_order.batches.all().values_list('batch_type'))

