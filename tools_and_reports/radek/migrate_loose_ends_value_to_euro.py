from custom.models import ExchangeRate
from loose_ends.models import LooseEnd

loose_ends = LooseEnd.objects.all()


def raw_euro_value(loose_end):
    # It's copied from model - just not cached
    try:
        exrate = ExchangeRate.get_safe_exchange(
            loose_end.accounting_date.year,
            loose_end.accounting_date.month,
            loose_end.accounting_date.day,
        )
    except Exception as e:
        print('error', loose_end.pk)
        return 0.0
    # NOTE: in case we need to use PLN it should look like this
    # val_in_PLN = round(er.rates[self.currency] * float(self.value), 2)
    if loose_end.currency == 'EUR':
        return loose_end.value
    if loose_end.currency == 'PLN':
        return round(float(loose_end.value) / exrate['EUR'], 2)
    return round(
        (float(exrate[loose_end.currency.upper()]) * float(loose_end.value))
        / float(exrate['EUR']),
        2,
    )


for idx, loose_end in enumerate(loose_ends):
    LooseEnd.objects.filter(pk=loose_end.pk).update(value_in_euro=raw_euro_value(loose_end))
