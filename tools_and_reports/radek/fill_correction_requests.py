from customer_service.correction_request_strategies import \
    get_correction_request_strategy
from customer_service.enums import CSCorrectionRequestStatus, CSCorrectionRequestType
from customer_service.models import CSCorrectionRequest
from invoice.choices import InvoiceStatus
from invoice.models import InvoicePreview


def generate_pdfs_for_drafts():
    invoice_drafts = InvoicePreview.objects.filter(status=InvoiceStatus.CORRECTING_DRAFT)
    for invoice in invoice_drafts:
        print(invoice)
        try:
            invoice.create_pdf()
        except Exception as e:
            print('error', e)


def generate_correction_invoices_for_new():
    correction_requests = CSCorrectionRequest.objects.filter(
        status=CSCorrectionRequestStatus.STATUS_NEW,
        correction_invoice=None
    ).exclude(type_cs=CSCorrectionRequestType.TYPE_SWITCH)
    for correction_request in correction_requests:
        print(correction_request)
        strategy = get_correction_request_strategy(correction_request)
        try:
            strategy.prepare_correction_request()
        except Exception as e:
            print('error', e)
