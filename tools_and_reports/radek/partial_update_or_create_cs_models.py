import datetime

from customer_service.models import <PERSON><PERSON>rder, CSUserProfile
from orders.models import Order
from user_profile.models import UserProfile

START_DATE = datetime.date(2022, 8, 1)


def partial_update_or_create_cs_orders():
    orders = Order.objects.filter(updated_at__date__gte=START_DATE).order_by('-id')
    for order in orders:
        print(order.pk)
        if CSOrder.should_update_or_create_from_order(order):
            CSOrder.objects.update_or_create_from_order(order)


def partial_update_or_create_cs_user_profiles():
    user_profiles = UserProfile.objects.filter(updated_at__date__gte=START_DATE).order_by('-id')
    for user_profile in user_profiles:
        print(user_profile.pk)
        if CSUserProfile.should_update_or_create_from_user_profile(user_profile):
            CSUserProfile.objects.update_or_create_from_user_profile(user_profile)
