Ecotax
======

This tool allows to calculate distribution of packaging materials per country.

Sales report and pricing factors are required for calculations.

File structure
--------------

File `run.py` contains terminal interface for script invocation.
`ecotax.py` contains calculations functions.

Running script
--------------

File `run.py` is terminal interface for running this script.

```
usage: run.py [-h] --report REPORT_PATH [--pricing-factors PRICING_FACTORS] [--output OUTPUT]

optional arguments:
  -h, --help            show this help message and exit
  --report REPORT_PATH, -r REPORT_PATH
                        Path to report file or directory
  --pricing-factors PRICING_FACTORS, -p PRICING_FACTORS
                        Path to pricing factor file
  --output OUTPUT, -o OUTPUT
                        Path to result file
```

<PERSON><PERSON><PERSON> has one required argument: `--report`/`-r` which should point to excel file
or directory containing sales report in format `xls` or `xlsx`.
Pricing factors file can be passed using argument `--pricing-factors`/`-p`.
If pricing factor isn't given as argument, script will download current
version from Google Sheets.


Requirements
------------

```
pandas==0.23.*
openpyxl==2.6.4
requests==2.25.1  # Unless pricing factor file is loaded locally
```
