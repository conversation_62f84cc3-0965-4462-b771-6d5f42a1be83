import argparse
import os
from typing import Optional

import pandas as pd

from ecotax import calculate_ecotax


def get_pricing_factors_df_from_google_drive(guid='52314163'):
    # Function copied from: production_margins.default_data.DefaultDataMixin

    import requests
    from io import BytesIO

    # Zmienne i importy
    import_header_row = 2  # Powyżej tego rzędu dane są pomijane
    import_index_col = 0
    key = '1IMsD3rG32EHiu1rU1Y_4K1XqvO6eK9du72hfmeawNho'
    import_link = (
        f'https://docs.google.com/spreadsheets/d/{key}/export'
        + f'?gid={guid}&format=csv&id={key}'
    )

    # Load spreadsheet
    pricing_factors_response = requests.get(import_link)
    pricing_factors_data = pricing_factors_response.content
    pricing_factors_df = pd.read_csv(
        BytesIO(pricing_factors_data),
        header=import_header_row,
        index_col=import_index_col,
        skip_blank_lines=True,
        encoding='utf-8',
        error_bad_lines=False,
        warn_bad_lines=True,
    )
    pricing_factors_df.index.name = 'codename'

    return pricing_factors_df


def load_report(report_path: str) -> pd.DataFrame:
    """
    Function loading report file to pandas DataFrame.

    If path given as argument points to file function loads that file.
    If path given as argument points to directory all excel files in that directory
    are loaded.
    """

    if os.path.isdir(report_path):
        paths = [
            os.path.join(report_path, filename)
            for filename in os.listdir(report_path)
            if filename.endswith('csv')
        ]
    else:
        paths = [report_path]
    data = pd.concat(
        pd.read_csv(
            path,
        )
        for path in paths
    )
    return data


def load_pricing_factors(pf_path: Optional[str] = None) -> pd.DataFrame:
    """
    Function for loading pricing factors.

    If path is given it loads pricing factors from pointed file, otherwise pricing
    factors file is downloaded from google drive.
    """

    if not pf_path:
        return get_pricing_factors_df_from_google_drive()
    return pd.read_excel(
        pf_path,
        sheet_name='CODENAMES',
        header=2,
        index_col=0,
        dtype={
            'weight_per_unit': str,
        },
    )


def main(arguments: argparse.Namespace) -> None:
    report_data = load_report(arguments.report_path)
    pricing_factors = load_pricing_factors(arguments.pricing_factors)

    ecotax: pd.DataFrame = calculate_ecotax(
        report_data,
        pricing_factors,
    )

    ecotax.to_excel(
        arguments.output,
        sheet_name='ecotax',
    )


def get_argparser() -> argparse.ArgumentParser:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--report', '-r',
        help='Path to report file or directory',
        type=str,
        required=True,
        dest='report_path',
    )
    parser.add_argument(
        '--pricing-factors', '-p',
        help='Path to pricing factor file',
        type=str,
        required=False,
        default=None,
    )
    parser.add_argument(
        '--output', '-o',
        help='Path to result file',
        type=str,
        required=False,
        default='./ecotax.xlsx',
    )
    return parser


if __name__ == '__main__':
    args = get_argparser().parse_args()
    main(args)
