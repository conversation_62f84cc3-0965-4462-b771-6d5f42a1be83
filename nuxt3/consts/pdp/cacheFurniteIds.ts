const csv = `j,3516417
j,3516419
j,4665821
j,4955688
j,3777616
j,4749004
j,1638492
w,956299
w,277526
s,739
w,62131
w,235218
j,4954803
j,3034451
j,4665744
j,3690556
j,4662199
j,3765910
j,2990566
w,520909
w,92243
j,3516465
s,319
j,7222624
j,3236078
j,3516448
w,61920
j,3516421
j,3363975
s,583
j,4590930
j,3516482
j,7222619
s,851
w,1011681
j,486320
j,3516441
u,3516463
w,277529
w,92289
j,4662013
j,3762451
w,656837
j,3767369
j,3686978
j,3516438
j,4954622
w,175144
j,4782964
s,1065
w,92286
j,3236080
j,3768835
w,208349
j,3337926
j,6018079
w,658456
w,657413
w,658484
j,3516463
w,519975
j,4665555
w,521163
j,3319909
w,522072
j,3841865
j,4957312
j,3516437
j,3516462
w,907980
w,521152
j,3057323
w,522006
j,1173788
j,4955042
u,3516462
j,3690566
j,4665438
w,234521
u,3747220
w,521050
j,6018237
j,3516416
j,7197610
j,3236002
s,750
w,92319
w,657365
j,4954881
u,3236002
j,3748278
j,1854782
s,737
w,277493
j,4973261
j,4954882
j,4767357
j,5140026
j,6017704
j,3528108
w,521668
j,7222625
j,3747220
w,92581
j,3222816
j,1596070
j,4955689
j,3690535
j,547648
j,2998387
w,657682
w,657961
w,522709
j,3516459
w,532153
u,2998387
j,2990551
w,235219
w,521294
w,208361
u,3516493
j,3762179
w,657749
u,3516477
w,521592
j,3516477
j,4662204
w,277583
j,3516493
j,4075271
j,3690551
j,3516427
u,3690551
w,277571
j,3516442
w,656527
j,3765907
u,4662204
w,234858
j,4956988
u,3516427
w,532160
u,3516442
j,5140128
s,1743
w,484
j,3764907
w,70134
j,3933126
s,1771
s,758
j,3727514
w,657984
j,4665796
j,3647707
s,539
j,7196225
w,482539
j,5141643
j,3527648
w,656976
j,3763711
j,5142030
j,4661971
j,4955259
s,403
u,3647707
j,904503
u,3727514
j,2465113
j,4590116
w,658462
j,3747197
w,522171
j,3516484
w,657777
w,657149
j,3516429
j,6018337
w,657770
w,521053
w,978500
j,5139899
j,3764949
w,656805
w,521449
j,5142946
j,905398
w,260215
w,521882
w,522711
j,4972919
s,880
j,4953332
j,3236038
w,521927
j,3867156
j,7222628
j,3236037
j,3747226
j,4955735
w,523069
j,3766241
j,4621092
j,3320241
w,657968
j,3765655
j,3516466
u,3516418
j,3516420
w,657733
j,1366970
j,2990489
w,235172
w,488918
j,7218355
w,658190
w,978469
w,178090
w,658047
u,1429438
w,235157
j,4980425
j,3527637
s,1063
j,3763873
j,3746954
w,978852
j,5140025
w,39068
j,4953324
w,657450
j,1368582
j,3516418
j,2249110
j,2995807
j,2995793
j,3516475
j,6018205
j,546450
j,5142919
j,4590650
j,3764912
w,233771
j,4954542
u,905398
s,1503
w,657638
w,521658
w,532154
j,7197613
j,3516405
j,4954587
j,1173651
j,2990468
j,4662341
w,235212
w,92485
j,1413692
j,3114555
u,4662341
j,3322003
j,4955185
j,3527628
s,455
j,5142952
j,4957296
j,7222623
j,5130152
j,7197562
j,3516411
s,582
s,321
j,5143581
j,3516408
j,4955687
j,6017708
w,539946
w,657892
s,1083
w,176652
j,3319879
j,3761601
s,767
j,3779104
j,3322031
j,3319857
j,7197274
j,1429438
j,2995773
w,656796
j,4955266
j,7196405
j,7222621
j,4957019
j,6018168
w,656868
s,972
j,1596673
j,4955338
j,3764043
w,657021
u,7197274
u,3516461
w,521338
w,208348
u,3516464
w,184959
j,2248042
w,277582
w,233452
w,521590
s,1229
w,520546
j,4954570
j,3854739
j,3763732
j,5168816
j,4980946
w,657518
u,7196225
j,4955490
w,250774
j,4665373
w,657385
w,657907
j,905336
w,521448
j,5141755
w,522156
j,4977882
s,707
j,4665782
s,1666
w,657651
j,3705292
j,3516432
s,732
j,3765912
w,539941
s,315
w,519853
w,522521
j,1183734
w,657479
w,521593
w,657788
w,540015
j,2809826
j,3319915
u,7196405
j,3533436
u,905336
j,2990578
w,539944
j,7389326
j,4972870
w,657513
j,3516464
j,3761428
w,521889
w,235211
j,3121515
w,250623
s,1062
j,4972705
j,4955669
j,2474016
j,3516409
j,7595177
u,4972939
j,7197219
w,657750
s,846
j,5958061
j,4953352
j,3764909
w,532127
u,7197219
w,656666
w,234520
j,4551083
j,4751398
w,234904
j,4957041
j,3716771
j,3516414
w,540049
w,657574
w,658110
s,290
w,502298
u,4751670
j,4665612
j,3766062
w,658391
w,532129
w,657683
j,4767586
w,658485
j,3516461
u,4551083
w,656879
w,21476
s,479
s,161
s,591
s,923
u,4665733
w,92275
s,1709
w,540471
w,92290
j,3766236
j,6017877
w,532147
j,5143592
s,656
j,4590117
u,3236075
s,852
s,1349
j,3516415
w,532162
w,657516
s,1366
w,225
j,3516481
j,5140338
j,1225343
j,3683755
u,3851638
w,656601
j,3516453
w,657530
w,657324
j,4953277
w,522092
j,2995818
j,3690549
j,7197668
s,426
j,4665733
j,329800
j,3464607
u,2829736
j,6213558
w,656702
j,3518146
u,3527653
j,2809872
j,3872527
w,532132
j,4980535
j,4972899
s,1252
j,7582955
j,4972945
j,3852732
w,657223
j,4751670
w,539916
j,4972676
j,4957021
u,7196852
j,3319907
j,5140028
j,3779106
w,173817
j,4955258
w,521204
w,522211
w,521612
j,3328343
j,4785440
s,1383
j,3825748
j,904518
w,532148
w,233531
w,656931
j,4782963
j,3766660
j,7389395
j,1854831
j,3057321
j,7389332
j,6213724
w,657710
j,2829736
j,3716779
u,3464607
w,657940
j,2998343
u,3763292
j,3762222
u,3516415
j,5140283
j,4972939
u,3319831
w,175752
u,3516481
w,61922
u,3321934
j,3527653
u,3328343
u,1596825
w,233765
w,536
s,1037
w,658423
j,3319831
s,1190
j,7196852
w,658122
w,519877
w,521635
j,295502
j,3762483
j,4980229
w,658295
j,5958954
j,4972874
w,521660
u,3841841
w,980283
j,5139905
w,92332
j,3236075
w,657035
w,657085
j,3187503
s,1335
w,540012
w,522817
w,522048
j,4751658
j,6018002
j,2998341
w,521162
u,3852732
s,743
w,521164
w,540031
j,5143275
u,329800
w,540515
w,520140
s,919
j,3530360
j,4957266
u,4665612
u,3516447
j,3555979
j,6017718
u,4956924
u,3121515
j,4751655
j,2998355
j,3763292
j,1413822
u,3555979
w,92321
j,3516486
s,1428
j,4954666
u,7196188
j,2942974
s,664
j,4785461
j,5140407
u,3716779
j,6018182
u,335863
j,2991658
j,4953316
j,3533435
j,2998388
u,5142364
j,2995720
w,656736
w,658326
s,859
j,5143452
w,657728
w,657504
j,4551187
j,6018194
u,2998385
j,3716770
j,3319842
w,657748
w,522248
w,235201
w,658101
j,3349343
j,7196188
j,3650041
j,4954588
w,521206
w,522725
u,3533435
w,521334
w,657267
j,3763717
u,3650136
j,2990484
j,3650136
j,1854675
j,5142364
s,341
j,3761583
j,3765911
j,3763816
s,466
j,3841841
j,4954670
j,6017970
w,657848
s,1782
j,5140130
j,6017910
w,520139
s,843
j,3516460
j,4957265
j,3767101
s,293
w,521514
j,6017710
w,92259
w,657901
w,522893
w,522389
j,3763867
j,5141704
j,5142963
w,173515
j,3825551
j,4589988
j,335863
w,521462
j,3761603
w,235182
j,3516447
u,4589988
w,658069
u,2809826
s,872
u,295502
s,1097
w,92291
w,657976
j,1173569
s,299
w,521439
j,3516478
j,3236047
j,4955470
s,298
u,7197006
j,7196984
j,3806532
s,1369
w,657218
w,657987
w,750605
w,657506
j,5158300
j,1596825
u,2998390
w,657603
u,3236047
j,4786498
s,830
u,3319842
s,1794
j,3765248
j,6018109
w,521836
w,656612
u,5140407
j,5143587
s,289
j,3764283
s,382
u,7196984
j,4665425
j,7197006
j,7196601
j,2998385
w,521547
j,3716781
j,4665663
j,3890599
j,4953344
w,657258
w,539984
s,645
w,522991
j,3649867
s,412
w,658177
w,657169
w,521488
w,92316
j,1173776
s,276
j,4955665
j,3763367
w,657216
j,3235897
u,5139859
w,482529
j,6018398
j,4957190
w,656611
j,3483119
j,7196908
j,5139859
u,4589985
w,522269
j,7197597
u,3516478
j,5139867
u,3850687
w,215955
w,61921
w,532128
j,6213774
j,5143442
w,657214
j,4751665
w,522235
j,5143460
j,3761147
j,4749005
w,522736
j,1596810
u,5139831
w,235134
j,5140302
j,4953230
u,3765629
j,4972438
u,3649867
w,657948
w,657005
j,3806509
j,3760492
w,522769
w,100756
u,3767101
w,656792
s,145
u,3322022
j,6018327
j,6017709
j,835004
w,656766
j,4972836
j,1414026
w,657697
w,658504
j,5143020
j,3763621
w,100748
s,1740
u,4953230
j,4589985
u,4953857
u,3763867
j,1173627
j,3762253
u,3320243
u,2998356
w,657698
w,657917
j,2998356
s,810
j,4953857
w,522749
u,7196916
s,816
u,7196601
s,1738
w,657018
w,521447
w,658457
j,2998390
w,656930
s,1253
j,4665630
j,3516424
j,1372963
w,869502
j,1372677
j,3236039
j,5140604
w,658195
w,260211
w,92279
j,3762244
w,657893
s,1341
j,5139831
s,122
j,3851638
s,1418
u,3516473
w,251159
j,5142956
j,3690554
u,3683757
u,7196158
u,3852516
j,4980323
j,3763812
w,657022
w,522767
j,3761014
w,658226
w,488912
w,521620
j,5139904
w,522356
w,657888
u,3236039
j,7196554
u,7196554
j,5158306
j,6018340
j,4972189
u,3516471
u,3761014
j,4665772
w,656748
j,3114361
w,521616
w,235173
j,3851639
w,522205
j,2990589
w,657592
w,574293
j,3516473
j,3744724
j,3276483
j,3762190
u,5140302
w,522194
j,7196189
s,1466
j,4955041
w,521130
j,4956924
j,3322022
s,411
w,657785
w,171635
s,1350
j,4954574
j,3763219
j,3763850
j,3516428
w,522185
j,5142978
w,522854
j,4973328
j,3516445
s,1417
u,3767263
u,1372677
s,1159
j,5140027
w,523070
j,4956435
w,522879
u,3851163
j,4665395
w,521527
j,4955040
w,208359
j,4590644
u,7196167
w,657011
w,657186
u,4751660
w,208378
w,657900
j,7389386
w,522160
s,1340
w,658237
w,657864
u,3767152
w,521648
j,3683757
j,2991666
w,657522
j,3716776
j,4972677
w,522703
j,4661957
u,3763812
w,657505
w,522249
j,2995865
u,3890599
j,3765629
j,4955491
w,522208
j,3765897
s,1338
u,7196189
w,176015
j,6018149
j,5158416
s,654
w,657384
u,3761871
j,3767263
j,6017883
j,4590928
j,5139863
j,3762290
j,4957097
u,3483119
w,658170
j,4588022
w,520153
w,520096
j,3760569
w,521510
j,7196918
w,522831
j,5143564
u,7196908
j,5143598
j,7389392
j,3852551
s,286
j,7196167
j,5140092
u,3516428
u,3760578
w,656741
w,521934
w,522118
w,657102
j,4955725
j,4980543
w,658199
w,519866
w,657101
w,521185
j,3763725
w,478
w,656787
j,5143446
j,7389335
s,141
j,3764921
w,502301
w,656706
u,4980543
w,657703
w,656557
s,710
w,656795
w,173437
w,61917
s,404
s,296
u,4767171
j,3850687
u,4980097
w,522133
w,520425
j,5142953
u,7196461
j,5143162
j,3763852
j,3236073
j,3748287
j,4954475
w,657964
w,657366
w,521176
s,410
w,105291
s,901
s,580
j,4973184
j,6018141
j,5141664
w,522261
j,6018289
j,6017706
j,3766804
u,3760569
j,4980097
s,399`;

function csvStringToSet (csvString:string) {
  const lines = csvString.trim().split('\n');
  const values = lines.map(line => line.split(',').reverse().join(','))
    .filter(value => value);

  return new Set(values);
}

const furnitureSet = csvStringToSet(csv);

const isFurnitureEligibleForCache = (id: string | number, type: string) => {
  return furnitureSet.has(`${id},${type}`);
};

export { isFurnitureEligibleForCache };
