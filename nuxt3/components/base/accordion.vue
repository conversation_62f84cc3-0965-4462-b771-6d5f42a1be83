<template>
  <component
    :is="as"
    class="flex flex-col w-full"
    :class="{ 'base-accordion--expanded': model }"
  >
    <label :class="labelClasses">
      <slot
        name="title"
        v-bind="{ isExpanded: model }"
      />

      <input
        v-model="model"
        type="checkbox"
        name="collapsibles"
        class="opacity-0 absolute w-0 h-0"
        v-on:change="(e) => $emit('update:modelValue', (e.target as HTMLInputElement).checked)"
      >
    </label>
    <slot />

    <Transition :duration="200">
      <div
        v-show="model"
        class="grid overflow-hidden transition-[grid-template-rows] short-transition"
        :class="[accordionWrapperClasses, model ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]']"
      >
        <div class="min-h-0">
          <slot name="content" />
        </div>
      </div>
    </Transition>
  </component>
</template>

<script setup lang="ts">
const model = defineModel<boolean>({ default: false });

const props = defineProps({
  accordionWrapperClasses: {
    type: String,
    default: 'normal-12 lg:normal-14'
  },
  labelClasses: {
    type: String,
    default: 'cursor-pointer'
  },
  as: {
    type: String,
    default: 'div'
  },
  isExpanded: {
    type: Boolean,
    default: false
  }
});

if (props.isExpanded) {
  model.value = props.isExpanded;
}

defineEmits<{
  'update:modelValue': [value: boolean]
}>();

</script>
