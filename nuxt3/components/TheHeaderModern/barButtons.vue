<template>
  <div
    ref="mainNav"
    class="lg:relative group h-full"
  >
    <!-- Floating pill under navigation buttons -->
    <div
      class="absolute top-0 h-full rounded-full pointer-events-none lg-max:hidden
              bg-[var(--bg-color)] text-[var(--text-color)]
              transition-all ease-out duration-[var(--duration)]
              opacity-0 group-hover:opacity-100
              translate-x-[var(--x)] w-[var(--width)]
              [.mega-menu-opened_&]:!opacity-100"
      :class="{
        'hidden': !isTransitionInited
      }"
    />

    <!-- Buttons list -->
    <ul class="flex h-full">
      <li
        v-for="(item, index) in buttonsList"
        :key="`navigation-item-${index}-${item.testId}`"
        class="h-full flex items-center relative lg:z-1
                text-[var(--bg-color)] hover:text-[var(--text-color)]
                transition-color ease-out duration-500
                lg-max:hover:bg-[var(--bg-color)] lg-max:rounded-full"
        :class="[
          item.extraClasses,
          {
            'lg:hover:bg-[var(--bg-color)] lg:rounded-full': !isTransitionInited
          }
        ]"
        v-on:mouseover="onLinkHover"
        v-on:mouseenter.once="initHoverTransition"
        v-on:mouseout.once="initHoverTransition"
      >
        <component
          :is="item.href ? BaseLink : BaseButton"
          variant="custom"
          prefetch-on="interaction"
          class="flex items-center justify-center lg-max:rounded-full h-full"
          v-bind="{
            ...item.attributes,
            'data-testid': item.testId,
            href: item.href,
            class: linkExtraClasses,
            trackData: item.trackData,
          }"
          v-on="item.handlers"
        >
          <template v-if="item?.label">
            {{ item.label }}
          </template>
          <template v-if="item.icon">
            <component
              :is="item.icon"
              class="w-24 h-24"
              v-bind="{
                is: item.icon,
                class: item.iconExtraClasses
              }"
            />
          </template>
          <ClientOnly>
            <BadgeCounter
              v-if="item.badge && item.badge > 0"
              class="top-8 right-8"
              v-bind="{ count: item.badge }"
            />
          </ClientOnly>
        </component>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { useCssVar } from '@vueuse/core';
import BaseLink from '~/components/base/link.vue';
import BaseButton from '~/components/base/button.vue';
import { type MenuButtonConfig } from '~/components/TheHeaderModern/bar.vue';

withDefaults(defineProps<{
  linkExtraClasses?: string;
  buttonsList:Array<MenuButtonConfig>
}>(), {
  linkExtraClasses: 'px-24'
});

const isTransitionInited = ref(false);

const mainNav = ref();
const mainNavX = useCssVar('--x', mainNav);
const mainNavWidth = useCssVar('--width', mainNav);

const onLinkHover = (event:PointerEvent) => {
  const { offsetLeft, offsetWidth } = event.currentTarget;
  mainNavX.value = `${offsetLeft}px`;
  mainNavWidth.value = `${offsetWidth}px`;
};

function initHoverTransition (event:PointerEvent) {
  if (isTransitionInited.value) { return; }

  isTransitionInited.value = true;
  onLinkHover(event);
}

</script>
