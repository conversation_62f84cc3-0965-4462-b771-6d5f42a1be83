<template>
  <aside
    class="ribbon-text-color-override relative z-5 overflow-hidden
            pointer-events-auto normal-12 lg:normal-14 bg-orange text-white"
    data-section="header-ribbon"
    v-bind:style="{
      backgroundColor: ribbonData?.backgroundColor,
      color: ribbonData?.textColor,
    }"
  >
    <div class="grid-container h-52 flex items-center gap-x-16 justify-center">
      <Component
        v-bind:is="!isRibbonLinkDisabled ? BaseLink : 'div'"
        v-if="finalText?.length > 0"
        class="shrink"
        v-bind="!isRibbonLinkDisabled && {
          href: ribbonData?.link,
          trackData: { eventLabel: 'ribbon-link' },
          variant: 'custom'
        }"
      >
        <span>
          <Transition
            name="custom"
            mode="out-in"
            appear
            enter-from-class="opacity-0 transform [transform:rotateX(90deg)]"
            enter-active-class="transition-all duration-500 ease-out perspective-dramatic perspective-origin-center"
            leave-active-class="transition-all duration-500 ease-in perspective-dramatic perspective-origin-center"
            leave-to-class="opacity-0 transform [transform:rotateX(-90deg)]"
          >
            <span
              v-bind:key="currentLine"
              v-bind:class="isCountdownEnabled && shouldRenderCounter ? '' : 'text-center'"
              v-html="finalText[currentLine]"
            />
          </Transition>
        </span>
      </Component>

      <Transition
        name="custom"
        mode="out-in"
        appear
        enter-from-class="opacity-0 transform [transform:rotateX(90deg)]"
        enter-active-class="transition-all duration-100 ease-out perspective-dramatic perspective-origin-center"
        leave-active-class="transition-all duration-100 ease-in perspective-dramatic perspective-origin-center"
        leave-to-class="opacity-0 transform [transform:rotateX(-90deg)]"
      >
        <TheHeaderRibbonCountdown
          v-if="delayedRenderCounter"
          class="transition-opacity duration-500 ease-out"
          v-bind="{
            endDate: countdownData?.endDate,
            secondaryColor: countdownData?.textColor,
          }"
        />
      </Transition>
    </div>
  </aside>
</template>

<script lang="ts" setup>
import { useCssVar } from '@vueuse/core';
import useHeader from '~/composables/useHeader';

const route = useRoute();
const { ribbon: ribbonData, countdown: countdownData } = storeToRefs(useGlobal());

const { isMobileOrTabletViewport } = useMq('lg');

const splitedTextDesktop = computed(() => ribbonData.value?.lines.map(line => line.copyHeader) || []);
const splitedTextMobile = computed(() => ribbonData.value?.lines.map(line => line.copyHeaderMobile) || []);

const finalText = computed(() => isMobileOrTabletViewport.value ? splitedTextMobile.value : splitedTextDesktop.value);

const currentLine = ref(0);

const routeName = computed(() => String(route.name)?.split('___')[0] || '');
const ribbonLinksDisabledRoutes = ['grid', 'furniture-category-furniture'];
const isRibbonLinkDisabled = computed(() => !ribbonData.value?.link || ribbonData.value?.link?.length === 0 || ribbonLinksDisabledRoutes.includes(routeName.value));
const BaseLink = resolveComponent('BaseLink');

const { ribbonHeight } = useHeader();
const ribbonHeightCssVar = useCssVar('--ribbon-height');
ribbonHeight.value = 52;
ribbonHeightCssVar.value = '52px';

const isCountdownEnabled = computed(() =>
  (countdownData.value?.showOnPdp || routeName.value !== 'furniture-category-furniture') && countdownData.value?.endDate
);

const shouldRenderCounter = computed(() => {
  return isCountdownEnabled.value && ribbonData.value?.lines[currentLine.value]?.showTimer;
});

const delayedRenderCounter = ref(shouldRenderCounter.value);

watch(shouldRenderCounter, (newValue) => {
  setTimeout(() => {
    delayedRenderCounter.value = newValue;
  }, newValue ? 500 : 50);
});

const updateTextLoop = () => {
  currentLine.value++;

  if (currentLine.value >= finalText.value?.length) {
    currentLine.value = 0;
  }
};

let interval: ReturnType<typeof setInterval> | null = null;

onMounted(() => {
  if (finalText.value?.length > 1) {
    interval = setInterval(updateTextLoop, 5000);
  }
});

onBeforeUnmount(() => {
  if (interval) {
    clearInterval(interval);
  }
});

</script>
