import React from 'react';
import ReactDOM from 'react-dom';
import MainStatsRender from './components/MainStatsRender'


import { Provider } from 'react-redux';

const filter_list_configuration = [
            {"column_name": "id", "column_type":"text", "column_width":60, "fixed": false, "label": "Order id"},
            {"column_name": "order_type", "column_type":"text", "column_width":70, "fixed": false, "label": "Type"},
            {"column_name": "order_source", "column_type":"text", "column_width":70, "fixed": false, "label": "Source"},
            {"column_name": "country", "column_type":"text", "column_width":70, "fixed": false, "label": "country"},
            {"column_name": "total_price", "column_type":"text", "column_width":80, "fixed": false, "label": "total price"},
            {"column_name": "promo_amount", "column_type":"text", "column_width":70, "fixed": false, "label": "promo amount"},
            {"column_name": "promo_text", "column_type":"text", "column_width":90, "fixed": false, "label": "promo_text"},
            {"column_name": "product_images", "column_type":"image_list", "column_width":120, "fixed": false, "label": "products"},
        ];

const filtering_configuration = [
    {"filter_name": "price_min_filter", 'label':'Price min', "filter_type":"less_equal", 'filter_field':'price', 'option_type': 'input'},
    {"filter_name": "price_max_filter", 'label':'Price max', "filter_type":"greater_equal", 'filter_field':'price', 'option_type': 'input'},
    {"filter_name": "created_min_filter", 'label':'Created min', "filter_type":"less_equal_date", 'filter_field':'created_at', 'option_type': 'input-date'},
    {"filter_name": "created_max_filter", 'label':'Created max', "filter_type":"greater_equal_date", 'filter_field':'created_at', 'option_type': 'input-date'}
];

const report_configuration = [
    {'group_name': "Overall", 'group_id':'overall', 'entries':[
        {'info_type': 'one-dimension-info', 'data_key':'total_price', 'reduction_type':'count', 'bucket_size':5},
        {'info_type': 'one-dimension-info', 'data_key':'country', 'reduction_type':'countWithoutEmpty', 'bucket_size':1},
        {'info_type': 'one-dimension-info', 'data_key':'promo_amount', 'reduction_type':'count', 'bucket_size':5},
        {'info_type': 'one-dimension-info', 'data_key':'promo_text', 'reduction_type':'countWithoutEmpty', 'bucket_size':1},
        {'info_type': 'one-dimension-info', 'data_key':'margin', 'reduction_type':'count', 'bucket_size':1},
        {'info_type': 'one-dimension-info', 'data_key':'order_type', 'reduction_type':'countWithoutEmpty', 'bucket_size':1},
        {'info_type': 'one-dimension-info', 'data_key':'order_source', 'reduction_type':'countWithoutEmpty', 'bucket_size':1},
    ]},
    {'group_name': "Acquire", 'group_id':'acquire', 'entries':[
        {'info_type': 'one-dimension-info', 'data_key':'order_lead_time', 'reduction_type':'count', 'bucket_size':5},
        {'info_type': 'one-dimension-info', 'data_key':'first_referral', 'reduction_type':'countWithoutEmpty', 'bucket_size':1},
        {'info_type': 'one-dimension-info', 'data_key':'last_referral', 'reduction_type':'countWithoutEmpty', 'bucket_size':1},
    ]},
    {'group_name': "Costs", 'group_id':'costs', 'entries':[
        {'info_type': 'one-dimension-info', 'data_key':'production_cost', 'reduction_type':'count', 'bucket_size':10},
        {'info_type': 'one-dimension-info', 'data_key':'logistic_cost', 'reduction_type':'count', 'bucket_size':10},
    ]},
    {'group_name': "Times", 'group_id':'times', 'entries':[
        {'info_type': 'one-dimension-info', 'data_key':'delivery_time', 'reduction_type':'count', 'bucket_size':1},
        {'info_type': 'one-dimension-info', 'data_key':'production_time', 'reduction_type':'count', 'bucket_size':0},
        {'info_type': 'one-dimension-info', 'data_key':'logistic_time', 'reduction_type':'count', 'bucket_size':0},
    ]},
    {'group_name': "Dimension x dimension", 'group_id':'corelations', 'entries':[
        {'info_type': 'two-dimension-info', 'data_key':'total_price', 'data_key_2':'promo_amount', 'reduction_type':'count', 'bucket_size':1},
    ]},
];


ReactDOM.render(
    <MainStatsRender filter_list_configuration={filter_list_configuration} filtering_configuration={filtering_configuration} report_configuration={report_configuration}/>
    ,
    document.getElementById('root')
);

