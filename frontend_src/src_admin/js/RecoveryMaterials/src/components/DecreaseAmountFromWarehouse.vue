<template>
  <section>
    <div class="text-h6">
      <PERSON>dej<PERSON><PERSON> ze stanu
    </div>
    <q-input
      v-model="amount"
      label="Ilość do zdjęcia"
      type="number"
      min="1"
    />
    <q-btn
      class="q-mt-lg bg-indigo-9 text-white"
      label="Zapisz"
      v-bind:loading="loading"
      v-on:click="decreaseAmountFromWarehouse"
    />
  </section>
</template>
<script>
export default {
  name: 'DecreaseAmountFromWarehouse',
  props: {
    closeModal: {
      type: Function,
      required: true,
    },
    warehouseElementsIds: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      amount: null,
    };
  },
  methods: {
    preparePayload() {
      return this.warehouseElementsIds.reduce((acc, id) => {
        acc.push({
          id,
          amount: parseInt(this.amount, 10),
        });
        return acc;
      }, []);
    },
    decreaseAmountFromWarehouse() {
      this.loading = true;
      this.$axios.post('/api/v1/product_material_recovery/warehouse/decrease_amount/',
        this.preparePayload())
        .then(() => {
          this.$q.notify({ message: 'Zapisano', type: 'positive' });
          this.closeModal();
        }).catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        }).finally(() => {
          this.loading = false;
        });
    },
  },
};

</script>
