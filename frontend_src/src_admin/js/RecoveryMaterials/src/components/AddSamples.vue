<template>
  <section>
    <div class="text-h6">
      Dodaj sample
    </div>
    <q-input
      v-model="amount"
      label="Ilość sampli"
      type="number"
      min="1"
    />
    <q-select
      v-model="color"
      label="Kolor"
      v-bind:options="availableColors"
    />
    <q-btn
      class="q-mt-lg bg-indigo-9 text-white"
      label="Dodaj sample"
      v-bind:loading="loading"
      v-on:click="addSamples"
    />
  </section>
</template>
<script>
export default {
  name: 'AddSamples',
  props: {
    closeModal: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      amount: null,
      color: null,
      availableColors: [],
    };
  },
  created() {
    this.getAvailableColors();
  },
  methods: {
    getAvailableColors() {
      this.$axios.get('/api/v1/product_material_recovery/product_material_recovery_configuration/')
        .then(resp => {
          this.availableColors = resp.data.color_options.map(({ name, value }) => ({
            label: name,
            value,
          }));
        }).catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        });
    },
    addSamples() {
      this.$axios.post('/api/v1/product_material_recovery/samples/', {
        amount: this.amount,
        color: this.color.value,
      })
        .then(() => {
          this.$q.notify({ message: 'Pomyślnie dodano sample', type: 'positive' });
          this.closeModal();
        })
        .catch(e => {
          if (e.response.status === 400) {
            const responseMessage = e.response.data.color && e.response.data.color.join(', ');
            this.$q.notify({ message: responseMessage || 'Wystąpił błąd', type: 'negative' });
          } else {
            this.$q.notify({ message: `Wystąpił błąd - ${e.message}`, type: 'negative' });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};

</script>
