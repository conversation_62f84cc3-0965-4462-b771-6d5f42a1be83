<template>
  <q-card
    flat
    bordered
    class="q-pa-lg"
  >
    <q-card-section
      v-for="productData in productRecoveryData"
      v-bind:key="productData.id"
      class="flex column"
    >
      <p class="text-subtitle1 text-center ">
        Produkt ID={{ productData.id }} Color={{ productData.color }}
      </p>
      <div
        v-for="(tableName, index) in availableTablesForProduct[productData.id]"
        v-bind:key="`report-table-${index}`"
        class="q-my-md"
      >
        <q-table
          v-bind="{
            key: `report-table-${index}`,
            title: tableName.toUpperCase(),
            data: productData.elements[tableName],
            hidePagination: true,
            loading,
            columns,
            pagination,
          }"
          title-class="text-subtitle1"
          row-key="name"
          selection="multiple"
          v-bind:selected.sync="selectedInfo[productData.id][tableName]"
        >
          <template #body-cell-amount="{ row }">
            <q-td>
              <q-input
                v-model.number="row.amount"
                type="number"
                min="0"
                v-bind:max="maxAvailableValuesPerProduct[productData.id][row.name]"
                center
                v-bind:rules="[ val => val <= maxAvailableValuesPerProduct[productData.id][row.name] || 'więcej niż max' ]"
              />
            </q-td>
          </template>
        </q-table>
      </div>
    </q-card-section>
    <q-btn
      v-bind:loading="loading"
      class="q-mt-lg bg-indigo-9 text-white"
      label="Generuj raport"
      v-on:click="generateReport"
    />
  </q-card>
</template>

<script>
export default {
  name: 'CreateRecoveryReport',
  props: {
    productIds: {
      type: Array,
      required: true,
    },
    closeModal: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      pagination: {
        page: 1,
        rowsPerPage: 0,
      },
      maxAvailableValuesPerProduct: {},
      tablesNames: ['fittings', 'supports', 'verticals'],
      availableTablesForProduct: {},
      selectedInfo: {},
      columns: [
        {
          name: 'name',
          align: 'center',
          label: 'Nazwa elementu',
          field: ({ name }) => name,
        },
        {
          name: 'amount',
          align: 'center',
          label: 'Ilość',
          field: ({ amount }) => amount,
        },
      ],
      productRecoveryData: [],
      loading: false,
    };
  },
  created() {
    this.setInitialSelectedInfo();
    this.getRecoveryInfo();
  },
  methods: {
    setInitialSelectedInfo() {
      const selectedInfo = {};
      this.productIds.forEach(productId => {
        selectedInfo[productId] = {
          fittings: [],
          supports: [],
          verticals: [],
        };
      });
      this.selectedInfo = selectedInfo;
    },
    getMaxValuesForElements(data) {
      return data.reduce((acc, productInfo) => {
        const elements = [
          ...productInfo.elements.fittings,
          ...productInfo.elements.supports,
          ...productInfo.elements.verticals,
        ];
        if (!acc[productInfo.id]) {
          acc[productInfo.id] = {};
        }
        elements.forEach(element => {
          acc[productInfo.id][element.name] = element.amount;
        });
        return acc;
      }, {});
    },
    getDataForReport() {
      return this.productIds.reduce((acc, productId) => {
        acc.push({
          product_id: productId,
          elements: [
            ...this.selectedInfo[productId].fittings,
            ...this.selectedInfo[productId].supports,
            ...this.selectedInfo[productId].verticals,
          ],
        });
        return acc;
      }, []);
    },
    getRecoveryInfo() {
      this.loading = true;
      this.$axios.get(`/api/v1/product_material_recovery?ids=${this.productIds.join(',')}`)
        .then(response => {
          this.productRecoveryData = response.data;
          this.maxAvailableValuesPerProduct = this.getMaxValuesForElements(response.data);
          response.data.forEach(recoveryInfo => {
            this.availableTablesForProduct[recoveryInfo.id] = this.tablesNames.filter(tableName => recoveryInfo.elements[tableName].length > 0);
          });
        })
        .catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    validateMaxAmount(reportData) {
      let isValid = true;
      reportData.forEach(productData => {
        if (productData.elements.some(element => element.amount > this.maxAvailableValuesPerProduct[productData.product_id][element.name])) {
          isValid = false;
        }
      });
      return isValid;
    },
    validateMinAmount(reportData) {
      let isValid = true;
      reportData.forEach(productData => {
        if (productData.elements.some(element => element.amount < 0)) {
          isValid = false;
        }
      });
      return isValid;
    },
    generateReport() {
      const reportData = this.getDataForReport();
      if (!this.validateMaxAmount(reportData)) {
        this.$q.notify({ message: 'Wybrano ujemną liczbę elementów', type: 'negative' });
        return;
      }
      if (!this.validateMinAmount(reportData)) {
        this.$q.notify({ message: 'Wybrano mniej elementów niż 0', type: 'negative' });
        return;
      }
      this.loading = true;
      this.$axios({
        method: 'POST',
        url: '/api/v1/product_material_recovery/create_report/',
        data: reportData,
      }).then(() => {
        this.$q.notify({ message: 'Pomyślnie wygenerowano raport', type: 'positive' });
        this.closeModal();
      })
        .catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
