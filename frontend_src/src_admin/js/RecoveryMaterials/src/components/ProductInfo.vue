<template>
  <section>
    <div class="column">
      <q-input
        v-model="searchedProductId"
        label="ID Produktu"
        dense
        autofocus
      />
      <q-btn
        class="q-my-sm bg-indigo-9 text-white"
        v-bind:loading="loading"
        label="Szukaj"
        v-on:click="getProductInfo"
      />
    </div>
    <div
      v-if="productInfo.id"
      class="column items-center"
    >
      <q-input
        v-bind:value="productInfo.id"
        class="q-my-sm"
        label="ID Produktu"
        dense
        disable
        outlined
        readonly
      />
      <q-input
        v-bind:value="productInfo.manufactor"
        class="q-my-sm"
        label="Producent"
        dense
        disable
        outlined
        readonly
      />
      <q-input
        v-bind:value="productInfo.color"
        class="q-my-sm"
        label="Kolor"
        dense
        disable
        outlined
        readonly
      />
      <q-input
        v-bind:value="productInfo.depth"
        class="q-my-sm"
        label="Głębokość"
        dense
        disable
        outlined
        readonly
      />
      <q-input
        v-bind:value="getFormattedDate(productInfo.created_at)"
        class="q-my-sm"
        label="Utworzony"
        dense
        disable
        outlined
        readonly
      />
      <q-input
        v-bind:value="productInfo.carrier"
        class="q-my-sm"
        label="Typ wysyłki"
        dense
        disable
        outlined
        readonly
      />
    </div>
  </section>
</template>
<script>
import { date } from 'quasar';

export default {
  name: 'ProductInfo',
  props: {
    setSearchedProductId: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      productInfo: {
        id: null,
        manufactor: null,
        color: null,
        depth: null,
        created_at: null,
        carrier: null,
      },
      searchedProductId: null,
    };
  },
  methods: {
    getFormattedDate(dateString) {
      return date.formatDate(dateString, 'DD-MM-YYYY HH:mm');
    },
    getProductInfo() {
      this.loading = true;
      this.$axios.get(`/api/v1/product_material_recovery/${this.searchedProductId}/product_info/`)
        .then(resp => {
          /* eslint-disable camelcase */
          const {
            id, manufactor, color, depth, created_at, carrier,
          } = resp.data;
          this.productInfo = {
            id, manufactor, color, depth, created_at, carrier,
          };
          this.setSearchedProductId(this.searchedProductId);
        })
        .catch(e => {
          if (e.response.status === 404) {
            this.$q.notify({ message: 'Nie znaleziono produktu o podanym ID', type: 'negative' });
          } else {
            this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};

</script>
