const routes = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/recovery-materials/MainPage.vue'), props: { } },
    ],
  },
  {
    path: '/samples',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/recovery-materials/Samples.vue'), props: { } },
    ],
  },
  {
    path: '/shelf-market',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/recovery-materials/ShelfMarket.vue'), props: { } },
    ],
  },
  {
    path: '/reports',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/recovery-materials/Reports.vue'), props: { } },
    ],
  },
  {
    path: '/warehouse',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/recovery-materials/Warehouse.vue'), props: { } },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '*',
    component: () => import('pages/Error404.vue'),
  },
];

export default routes;
