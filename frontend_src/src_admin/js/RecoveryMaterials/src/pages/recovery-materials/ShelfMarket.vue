<template>
  <q-page class="q-pa-lg q-mx-auto">
    <BaseRecoveryMaterialsTable
      ref="table"
      title="Shelf market"
      v-bind="{
        tableButtons,
        fetchData,
        tableData,
        loading,
        columns
      }"
    >
      <template #body-cell-report="props">
        <q-td
          v-bind:props="props"
          class="table-details q-gutter-sm"
        >
          <a
            v-if="props.row.recovery_report"
            v-bind:href="`${props.row.recovery_report.manufactor_report}`"
          >
            {{ props.row.recovery_report.id }}
          </a>
          <span v-else>-</span>
        </q-td>
      </template>
    </BaseRecoveryMaterialsTable>
    <q-dialog v-model="addItemModal">
      <div class="modal-wrapper q-pa-lg bg-white">
        <AddProductForShelfMarket
          v-bind:close-modal="closeModals"
        />
      </div>
    </q-dialog>
  </q-page>
</template>

<script>
import BaseRecoveryMaterialsTable from 'components/BaseRecoveryMaterialsTable.vue';
import AddProductForShelfMarket from 'components/AddProductForShelfMarket.vue';
import { date } from 'quasar';

export default {
  name: 'ShelfMarket',
  components: {
    BaseRecoveryMaterialsTable,
    AddProductForShelfMarket,
  },
  data() {
    return {
      tableButtons: [
        {
          label: 'Dodaj pozycję',
          icon: 'add',
          handleClick: () => { this.addItemModal = true; },
          enableOnOnlySelectedItems: false,
        },
        {
          label: 'Usuń pozycję',
          icon: 'delete',
          handleClick: selectedIds => this.deleteItems(selectedIds),
          enableOnOnlySelectedItems: true,
          disableFunction: selectedItems => selectedItems.some(item => item.recovery_report),
        },
        {
          label: 'Wygeneruj raport',
          icon: 'description',
          handleClick: selectedIds => this.generateReport(selectedIds),
          enableOnOnlySelectedItems: true,
          disableFunction: selectedItems => selectedItems.some(item => item.recovery_report),
        },
      ],
      tableData: [],
      columns: [
        {
          name: 'id',
          align: 'center',
          label: 'ID pozycji',
          field: ({ id }) => id,
        },
        {
          name: 'productId',
          align: 'center',
          label: 'ID regału',
          // eslint-disable-next-line camelcase
          field: ({ product }) => product.id,
        },
        {
          name: 'manufactor',
          align: 'center',
          label: 'Producent',
          field: ({ product }) => product.manufactor,
        },
        {
          name: 'color',
          align: 'center',
          label: 'Kolor',
          field: ({ product }) => product.color,
        },
        {
          name: 'cachedDepth',
          align: 'center',
          label: 'Głębokość',
          // eslint-disable-next-line camelcase
          field: ({ product }) => product.depth,
        },
        {
          name: 'createdAt',
          align: 'center',
          label: 'Data utworzenia',
          // eslint-disable-next-line camelcase
          field: ({ product }) => date.formatDate(product.created_at, 'DD-MM-YYYY HH:mm'),
        },
        {
          name: 'carrier',
          align: 'center',
          label: 'Typ wysyłki',
          field: ({ product }) => product.carrier || '-',
        },
        {
          name: 'qualityFactor',
          align: 'center',
          label: 'Współczynnik jakościowy',
          // eslint-disable-next-line camelcase
          field: ({ quality_factor }) => quality_factor || '-',
        },
        {
          name: 'report',
          align: 'center',
          label: 'Nr raportu',
          // eslint-disable-next-line camelcase
          field: ({ recovery_report }) => (recovery_report ? recovery_report.id : '-'),
        },
        {
          name: 'reportCreatedAt',
          align: 'center',
          label: 'Data utworzenia raportu',
          // eslint-disable-next-line camelcase
          field: ({ recovery_report }) => (recovery_report ? date.formatDate(recovery_report.created_at, 'YYYY-MM-DD HH:mm') : '-'),
        },
      ],
      loading: true,
      addItemModal: false,
    };
  },
  methods: {
    fetchData() {
      this.loading = true;
      this.$axios.get('/api/v1/product_material_recovery/shelf_market/')
        .then(resp => {
          this.tableData = resp.data;
          this.loading = false;
        })
        .catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        });
    },
    deleteItems(selectedIds) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Czy na pewno chcesz usunąć pozycję?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        this.$axios.delete(`/api/v1/product_material_recovery/shelf_market?ids=${selectedIds.join(',')}`)
          .then(() => {
            this.fetchData();
          })
          .catch(e => {
            this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
          });
      });
    },
    generateReport(selectedIds) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Czy na pewno chcesz wygenerować raport?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        this.$axios({
          method: 'POST',
          url: '/api/v1/product_material_recovery/shelf_market/create_report/',
          data: selectedIds.reduce((acc, id) => {
            acc.push({ shelf_market_id: id });
            return acc;
          }, []),
        })
          .then(() => {
            this.$q.notify({ message: 'Pomyślnie wygenerowano raport', type: 'positive' });
            this.$refs.table.resetSelection();
            this.fetchData();
          })
          .catch(e => {
            this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
          });
      });
    },
    closeModals() {
      this.addItemModal = false;
      this.$refs.table.resetSelection();
      this.fetchData();
    },
  },
};
</script>

<style scoped lang="scss">
  .modal-wrapper {
    min-width: 400px;
    width: 50vw;
  }
</style>
