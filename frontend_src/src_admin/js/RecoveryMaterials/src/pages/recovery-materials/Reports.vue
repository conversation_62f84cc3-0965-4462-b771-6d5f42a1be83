<template>
  <q-page class="q-pa-lg">
    <BaseRecoveryMaterialsTable
      title="Sample"
      v-bind="{
        tableButtons,
        fetchData,
        tableData,
        loading,
        columns,
        selection: 'none'
      }"
    >
      <template #body-cell-report="props">
        <q-td
          v-bind:props="props"
          class="table-details q-gutter-sm"
        >
          <a
            v-bind:href="`${props.row.manufactor_report}`"
          >
            {{ props.row.id }}
          </a>
        </q-td>
      </template>
    </BaseRecoveryMaterialsTable>
  </q-page>
</template>

<script>
import BaseRecoveryMaterialsTable from 'components/BaseRecoveryMaterialsTable.vue';
import { date } from 'quasar';

export default {
  name: 'Reports',
  components: {
    BaseRecoveryMaterialsTable,
  },
  data() {
    return {
      tableButtons: [
        {
          label: 'Usuń pozycję',
          icon: 'delete',
          handleClick: selectedIds => this.deleteSelectedItems(selectedIds),
          enableOnOnlySelectedItems: true,
        },
      ],
      selectedIds: [],
      tableData: [],
      columns: [
        {
          name: 'report',
          align: 'center',
          label: 'Numer raportu',
          field: ({ id }) => id,
          style: 'width: 150px',
          headerStyle: 'width: 150px',
        },
        {
          name: 'createdAt',
          align: 'center',
          label: 'Utworzono',
          // eslint-disable-next-line camelcase
          field: ({ created_at }) => date.formatDate(created_at, 'DD-MM-YYYY HH:mm'),
          style: 'width: 300px',
          headerStyle: 'width: 300px',
        },
        {
          name: 'type',
          align: 'center',
          label: 'Typ',
          // eslint-disable-next-line camelcase
          field: ({ get_report_type_display }) => get_report_type_display,
          style: 'width: 300px',
          headerStyle: 'width: 300px',
        },
      ],
      loading: true,
    };
  },
  methods: {
    fetchData() {
      this.loading = true;
      this.$axios.get('/api/v1/product_material_recovery/reports/')
        .then(resp => {
          this.tableData = resp.data;
          this.loading = false;
        })
        .catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        });
    },
    deleteSelectedItems(selectedIds) {
      this.loading = true;
      this.$q.dialog({
        title: 'Confirm',
        message: 'Czy na pewno chcesz usunąć zaznaczone pozycje?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        this.$axios.delete(`/api/v1/product_material_recovery/reports?ids=${selectedIds.join(',')}`)
          .then(() => {
            this.fetchData();
          })
          .catch(e => {
            this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
  },
};
</script>
