<template>
  <q-page class="q-pa-lg">
    <BaseRecoveryMaterialsTable
      v-bind="{
        tableButtons,
        fetchData,
        tableData,
        loading,
        columns
      }"
      ref="table"
      title="Sample"
    >
      <template #body-cell-report="props">
        <q-td
          v-bind:props="props"
          class="table-details q-gutter-sm"
        >
          <a
            v-if="props.row.recovery_report"
            v-bind:href="`${props.row.recovery_report.manufactor_report}`"
          >
            {{ props.row.recovery_report.id }}
          </a>
          <span v-else>-</span>
        </q-td>
      </template>
    </BaseRecoveryMaterialsTable>
    <q-dialog v-model="addItemModal">
      <div class="modal-wrapper q-pa-lg bg-white">
        <AddSamples
          v-bind:close-modal="closeModal"
        />
      </div>
    </q-dialog>
  </q-page>
</template>

<script>
import BaseRecoveryMaterialsTable from 'components/BaseRecoveryMaterialsTable.vue';
import AddSamples from 'components/AddSamples.vue';
import { date } from 'quasar';

export default {
  name: 'Samples',
  components: {
    BaseRecoveryMaterialsTable,
    AddSamples,
  },
  data() {
    return {
      tableButtons: [
        {
          label: 'Dodaj pozycję',
          icon: 'add',
          handleClick: () => { this.addItemModal = true; },
          enableOnOnlySelectedItems: false,
        },
        {
          label: 'Usuń pozycję',
          icon: 'delete',
          handleClick: selectedIds => this.deleteSelectedItems(selectedIds),
          enableOnOnlySelectedItems: true,
          disableFunction: selectedItems => selectedItems.some(item => item.recovery_report),
        },
        {
          label: 'Wygeneruj raport',
          icon: 'description',
          handleClick: selectedIds => this.generateReport(selectedIds),
          enableOnOnlySelectedItems: true,
          disableFunction: selectedItems => selectedItems.some(item => item.recovery_report),
        },
      ],
      tableData: [],
      columns: [
        {
          name: 'id',
          align: 'center',
          label: 'ID',
          field: ({ id }) => id,
        },
        {
          name: 'color',
          align: 'center',
          label: 'Kolor',
          field: ({ color }) => color,
        },
        {
          name: 'amount',
          align: 'center',
          label: 'Ilość sampli',
          field: ({ amount }) => amount,
        },
        {
          name: 'createdAt',
          align: 'center',
          label: 'Data utworzenia',
          // eslint-disable-next-line camelcase
          field: ({ created_at }) => date.formatDate(created_at, 'DD-MM-YYYY HH:mm'),
        },
        {
          name: 'report',
          align: 'center',
          label: 'Nr raportu',
          // eslint-disable-next-line camelcase
          field: ({ recovery_report }) => (recovery_report ? recovery_report.id : '-'),
        },
        {
          name: 'reportCreatedAt',
          align: 'center',
          label: 'Data utworzenia raportu',
          // eslint-disable-next-line camelcase
          field: ({ recovery_report }) => (recovery_report ? date.formatDate(recovery_report.created_at, 'DD-MM-YYYY HH:mm') : '-'),
        },
      ],
      loading: true,
      addItemModal: false,
    };
  },
  methods: {
    closeModal() {
      this.addItemModal = false;
      this.$refs.table.resetSelection();
      this.fetchData();
    },
    fetchData() {
      this.loading = true;
      this.$axios.get('/api/v1/product_material_recovery/samples/')
        .then(resp => {
          this.tableData = resp.data;
          this.loading = false;
        })
        .catch(e => {
          this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
        });
    },
    deleteSelectedItems(selectedIds) {
      this.loading = true;
      this.$q.dialog({
        title: 'Confirm',
        message: 'Czy na pewno chcesz usunąć zaznaczone pozycje?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        this.$axios.delete(`/api/v1/product_material_recovery/samples?ids=${selectedIds.join(',')}`)
          .then(() => {
            this.fetchData();
          })
          .catch(e => {
            this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    generateReport(selectedIds) {
      this.$q.dialog({
        title: 'Confirm',
        message: 'Czy na pewno chcesz wygenerować raport?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        this.$axios({
          method: 'POST',
          url: '/api/v1/product_material_recovery/samples/create_report/',
          data: selectedIds.reduce((acc, id) => {
            acc.push({ sample_box_id: id });
            return acc;
          }, []),
        })
          .then(() => {
            this.loading = false;
            this.$q.notify({ message: 'Pomyślnie wygenerowano raport', type: 'positive' });
            this.$refs.table.resetSelection();
            this.fetchData();
          })
          .catch(e => {
            this.$q.notify({ message: `Nastąpił błąd - ${e.message}`, type: 'negative' });
          });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.modal-wrapper {
  min-width: 400px;
  width: 50vw;
}

</style>
