module.exports = {
  root: true,
  parserOptions: {
    parser: 'babel-eslint',
    sourceType: 'module'
  },
  env: {
    browser: true,
    es6: true,
  },
  plugins: [
    'vue',
  ],
  extends: [
    'airbnb-base',
    'plugin:vue/recommended',
  ],
  rules: {
    semi: ['error', 'always'],
    'func-names': ['error', 'never'],
    'max-len': ['error', { code: 200, ignoreUrls: true, ignoreComments: true, ignoreStrings: true }],
    'arrow-parens': ['error', 'as-needed'],
    'space-before-function-paren': ['error', 'never'],
    'vue/no-v-html': 'off',
    'import/prefer-default-export': 'off',
    'no-tabs': 'off',
    'no-param-reassign': 'off',
    'no-shadow': 'off',
    'multiline-ternary': 'off',
    'linebreak-style': 0,
    'import/extensions': 'off',
    'vue/v-bind-style': ['error', 'longform'],
    'vue/v-on-style': ['error', 'longform'],
    'no-plusplus': 'off',
    'vue/object-curly-spacing': ['error', 'always'],
    'no-console': ['error', { allow: ['warn', 'error'] }],
    'import/no-extraneous-dependencies': 'off',
     "import/no-unresolved": [
      2,
      { "caseSensitive": false }
   ]
  },
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
        moduleDirectory: ['node_modules', 'src/'],
      },
    },
  }
}
