{"name": "recoverymaterials", "version": "0.0.1", "description": "App for recovery shelf materials", "productName": "Recovery Materials App", "author": "<PERSON> <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "test": "echo \"No test specified\" && exit 0", "build": "quasar build && cp -R ./dist/spa/* '../../../../src/producers/static/recovery_materials/'"}, "dependencies": {"@quasar/extras": "^1.0.0", "axios": "^0.27.2", "core-js": "^3.6.5", "quasar": "^1.0.0"}, "devDependencies": {"babel-eslint": "^10.0.1", "eslint": "^7.21.0", "eslint-webpack-plugin": "^2.4.0", "eslint-plugin-vue": "^7.7.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.20.1", "@quasar/app": "^2.0.0"}, "browserslist": ["last 10 Chrome versions", "last 10 Firefox versions", "last 4 Edge versions", "last 7 Safari versions", "last 8 Android versions", "last 8 ChromeAndroid versions", "last 8 FirefoxAndroid versions", "last 10 iOS versions", "last 5 Opera versions"], "engines": {"node": ">= 10.18.1", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}