const BABEL_CONFIG = {
    presets: [
        ['@babel/preset-env', {
            modules: 'commonjs',
            targets: {
                chrome: '58',
                ie: '11',
            },
        }],
    ],
    plugins: [
        '@babel/plugin-transform-destructuring',
        '@babel/plugin-proposal-object-rest-spread',
        '@babel/plugin-transform-arrow-functions',
    ],
};

require('@babel/register')(BABEL_CONFIG);

const gulp = require('gulp');
const concat = require('gulp-concat');
const uglify = require('gulp-uglify');
const sourcemaps = require('gulp-sourcemaps');
const autoprefixer = require('gulp-autoprefixer');
const sass = require('gulp-sass')(require('node-sass'));
const gutil = require('gulp-util');
const minify = require('gulp-minify-css');
const flatten = require('gulp-flatten');
const stripDebug = require('gulp-strip-debug');
const jasmine = require('gulp-jasmine');
const plumber = require('gulp-plumber');
const browserify = require('browserify');
const source = require('vinyl-source-stream');
const buffer = require('vinyl-buffer');
const wrap = require('gulp-wrap');
const babel = require('gulp-babel');
const stream = require('event-stream');
const notify = require('gulp-notify');
const gulpif = require('gulp-if');

const paths = {
    scripts: [
        'src/js/plugins.min.js',
    ],
    scripts_es6: [
        'src/js/components/**/*.js',
        'src/js/site.js',
    ],

    webgl: [
        'src_webgl/**/*',
        '!src_webgl/ivy/renderer/methods/raytrace',
        '!src_webgl/ivy/renderer/methods/raytrace/**',
    ],
    plugins: [
        'src/js/vendors/EventEmitter.js',
        'src/js/vendors/eventie.js',
        'src/js/vendors/imagesloaded.js',
        'src/js/vendors/fastclick.js',
        'src/js/vendors/underscore-min.js',
        'src/js/vendors/parsley.min.js',
        'src/js/vendors/parsley-i18n/de.js',
        'src/js/vendors/parsley-i18n/en.js',
        'src/js/vendors/parsley-i18n/fr.js',
        'src/js/vendors/parsley-i18n/es.js',
        'src/js/vendors/jquery.history.js',
        'src/js/vendors/TweenLite.js',
        'src/js/vendors/CSSPlugin.js',
        'src/js/vendors/EasePack.js',
        'src/js/vendors/picturefill.min.js',
        'src/js/configurator/vendor/nouislider.js',
    ],
    stylesheets: ['src/scss/style.scss', 'src/scss/**/*.scss'],
    stylesheetsVendors: ['src/scss/styleVendors.scss'],
    stylesheetsConfig: ['src/scss/styleConfig.scss'],
    cplus: ['src/scss/cplus/cplus.scss'],
    ds: ['src/scss/common-design-system/ds.scss'],
    legacyDsForConfigurators: ['src/scss/common-design-system/legacy-ds-for-configurators/legacy-ds-for-configurators.scss'],
    crow: ['src/scss/crow/crow.scss'],
    cwar: ['src/scss/cwar/cwar.scss'],
    cwatcol: ['src/scss/cwatcol/cwatcol.scss'],
    exitMobileSection: ['src/scss/exit-mobile-section/exit-mobile-section.scss'],
    ecommerceService: ['src/scss/ecommerce-service/ecommerce-service.scss'],
    dtf: ['src/scss/delivery-time-frames/delivery-time-frames.scss'],
    email24: ['src/scss/email-24/email-24.scss'],
    images: 'src/images/**/*',
    head: [
        'src/js/vendors/respond.min.js',
        'src/js/vendors/html5shiv.min.js',
        '../src/frontend_cms/static/dist/js/modernizr.js',
    ],
    destination: '../src/frontend_cms/static',
    configurator: [
        'src/js/configurator/vendor/three.min.js',
        'src/js/configurator/vendor/tween.min.js',
        'src/js/configurator/vendor/stats.js',
        'src/js/configurator/loaders/OBJLoader.js',
        'src/js/configurator/vendor/threex.renderstats.js',
        'src/js/configurator/vendor/pubsub.min.js',
    ],
};

gulp.task('clean', cb => {
    // del(['build'], cb);
    cb();
});

gulp.task('plugins', gulp.series('clean', () => gulp.src(paths.plugins)
    // .pipe(sourcemaps.init())
    .pipe(stripDebug())// remove all cosole.logs
    .pipe(uglify())
    .pipe(concat('plugins.min.js'))
    // .pipe(sourcemaps.write('public/dist/js/maps'))
    .pipe(gulp.dest('src/js'))));

gulp.task('scripts', gulp.series('plugins', 'clean', () => {
    // Minify and copy all JavaScript (except vendor scripts).
    // Also push es6 scripts through babel.
    const scripts = gulp.src(paths.scripts)
        .pipe(plumber())
        .pipe(sourcemaps.init())
        .pipe(uglify().on('error', gutil.log));

    // eslint-disable-next-line camelcase
    const scripts_es6 = gulp.src(paths.scripts_es6)
        .pipe(plumber())
        .pipe(babel(BABEL_CONFIG))
        .pipe(sourcemaps.init())
        .pipe(uglify().on('error', gutil.log));

    // with sourcemaps all the way down
    // eslint-disable-next-line camelcase
    return stream.merge([scripts, scripts_es6])
        .pipe(concat('scripts.min.js'))
        .pipe(sourcemaps.write('public/dist/js/maps'))
        .pipe(gulp.dest(`${paths.destination}/dist/js`));
}));

gulp.task('head', gulp.series('clean', () => gulp
    .src(paths.head)
    .pipe(plumber())
    .pipe(sourcemaps.init())
    .pipe(uglify())
    .pipe(concat('head.min.js'))
    .pipe(gulp.dest(`${paths.destination}/dist/js`))));

// Generate the configurator setup
gulp.task('configurator', gulp.series('clean', () => gulp.src(paths.configurator)
    .pipe(plumber())
    .pipe(stripDebug())// remove all cosole.logs
    .pipe(sourcemaps.init())
    .pipe(uglify())
    .pipe(concat('configurator.min.js'))
    .pipe(gulp.dest(`${paths.destination}/dist/js`))));

// Copy all static images
gulp.task('images', gulp.series('clean', done => {
    gulp.src(paths.images)
        .pipe(gulp.dest(`${paths.destination}/dist/images`));
    gulp.src('src_admin/js/RatingVue/assets/**/*')
        .pipe(gulp.dest(`${paths.destination}/dist/images/rating-tool`));
    done();
}));

function onError(err, title = 'RANDOM GULP ERROR', isProduction) {
    if (isProduction) {
        console.error(title);
        console.error(err.message);
        process.exit(1);
    } else {
        notify.onError({
            title,
            message: err.message,
        })(err);
    }
}

function processStylesheets(isProduction, path) {
    // isProduction = true;
    let cssStream = gulp.src(path)
        .pipe(gulpif(isProduction, plumber()))
        .pipe(sass())
        .on('error', err => onError(err, 'processStylesheets', isProduction))
        .pipe(autoprefixer({
            // browsers: ['last 2 versions', '> 1%', 'Firefox >= 20'],
            cascade: false,
            grid: false,
        }));

    if (isProduction) {
        cssStream = cssStream.pipe(minify());
    }

    return cssStream.pipe(gulp.dest(`${paths.destination}/dist/css`));
}

gulp.task('stylesheets', () => processStylesheets(false, paths.stylesheets[0]));
gulp.task('stylesheetsVendors', () => processStylesheets(false, paths.stylesheetsVendors[0]));
gulp.task('stylesheetsConfig', () => processStylesheets(false, paths.stylesheetsConfig[0]));

gulp.task('cplus', () => processStylesheets(false, paths.cplus[0]));
gulp.task('cplus-prod', () => processStylesheets(true, paths.cplus[0]));

gulp.task('cwar', () => processStylesheets(false, paths.cwar[0]));

gulp.task('cwatcol', () => processStylesheets(false, paths.cwatcol[0]));

gulp.task('crow', () => processStylesheets(false, paths.crow[0]));
gulp.task('crow-prod', () => processStylesheets(true, paths.crow[0]));

gulp.task('ecommerce-service', () => processStylesheets(false, paths.ecommerceService[0]));
gulp.task('ecommerce-service-prod', () => processStylesheets(false, paths.ecommerceService[0]));

gulp.task('exitMobileSection', () => processStylesheets(false, paths.exitMobileSection[0]));
gulp.task('exitMobileSection-prod', () => processStylesheets(true, paths.exitMobileSection[0]));

gulp.task('dtf', () => processStylesheets(false, paths.dtf[0]));
gulp.task('email24', () => processStylesheets(false, paths.email24[0]));
gulp.task('ds', () => processStylesheets(false, paths.ds[0]));
gulp.task('ds-prod', () => processStylesheets(true, paths.ds[0]));
gulp.task('stylesheets-prod', () => processStylesheets(true, paths.stylesheets[0]));
gulp.task('stylesheetsVendors-prod', () => processStylesheets(true, paths.stylesheetsVendors[0]));
gulp.task('stylesheetsConfig-prod', () => processStylesheets(true, paths.stylesheetsConfig[0]));

/**
 * WEBGL
 */

function mergeWebglModels(name, destionation) {
    return gulp.src(`./src_webgl/${name}/**/*.obj`)
        .pipe(wrap('o=}===># <%= file.relative %>\n<%= contents %>'))
        .pipe(concat(`${name}5.objx`))
        .pipe(gulp.dest(destionation || paths.destination));
}

function buildNodeRest(path, destionation) {
    return browserify(path)
        .bundle()
        .pipe(plumber())
        .pipe(source(path))
        .pipe(flatten())
        .pipe(gulp.dest(destionation || paths.destination));
}

function buildWebglDev(path, destionation) {
    return browserify(path).transform('babelify', {
        presets: [
            [
                '@babel/preset-env',
                {
                    modules: 'commonjs',
                    targets: {
                        chrome: '58',
                        ie: '11',
                    },
                },
            ],
        ],
        plugins: [
            '@babel/plugin-transform-destructuring',
            '@babel/plugin-proposal-object-rest-spread',
        ],
    })
        .bundle()
        .pipe(plumber())
        .pipe(source(path))
        .pipe(gulp.dest(destionation || paths.destination));
}

function buildWebgl(path, destionation) {
    return browserify(path).transform('babelify', {
        presets: [
            [
                '@babel/preset-env',
                {
                    modules: 'commonjs',
                    targets: {
                        chrome: '58',
                        ie: '11',
                    },
                },
            ],
        ],
        plugins: [
            '@babel/plugin-transform-destructuring',
            '@babel/plugin-proposal-object-rest-spread',
        ],
    })
        .bundle()
        .pipe(plumber())
        .pipe(source(path))
        .pipe(buffer())
        .pipe(sourcemaps.init())
        .pipe(stripDebug())// remove all cosole.logs
        // .pipe(uglify())
        .pipe(sourcemaps.write('../../../frontend_src/maps'))
        .pipe(gulp.dest(destionation || paths.destination));
}

gulp.task('webgl-ivy-merge-obj', () => mergeWebglModels('ivy'));

gulp.task('webgl-dev-ivy', gulp.series('webgl-ivy-merge-obj', () => buildWebglDev('./src_webgl/ivy/app.js')));


gulp.task('webgl-dev-ivy-tests', gulp.series('webgl-dev-ivy', () => {
    return; // disabling tests for now
    // eslint-disable-next-line no-unreachable
    gulp.src('./src_webgl/ivy/specs/spec.js')
        .pipe(jasmine({
            console: false,
            verbose: true,
            errorOnFail: false,
            includeStackTrace: true,
        }));
}));

// PRODUCTION

gulp.task('webgl-build-ivy', gulp.series('webgl-ivy-merge-obj', () => buildWebgl('./src_webgl/ivy/app.js')));
gulp.task('dna-rest', () => buildNodeRest('./src/js/node_rest/node_pricing.js', '../src/'));

// Rerun the task when a file changes
gulp.task('watch', () => {
    gulp.watch(paths.scripts_es6[0], gulp.series('scripts'));
    gulp.watch(paths.stylesheets[1], gulp.series('stylesheets', 'stylesheetsConfig', 'cplus', 'cwar', 'cwatcol', 'crow', 'exitMobileSection', 'dtf', 'ds', 'ecommerce-service'));
});
// The default task (called when you run `gulp` from cli)
gulp.task('default', gulp.series('stylesheets', 'cplus', 'cwar', 'cwatcol', 'crow', 'exitMobileSection', 'ecommerce-service', 'dtf', 'email24', 'ds', 'stylesheetsVendors', 'scripts', 'images', 'watch'));
gulp.task('django', gulp.series('stylesheets', 'cwar', 'cwatcol', 'crow', 'exitMobileSection', 'dtf', 'email24', 'stylesheetsVendors', 'stylesheetsConfig', 'cplus', 'ecommerce-service', 'ds', 'plugins', 'scripts', 'images', 'watch'));
gulp.task('build', gulp.series('webgl-ivy-merge-obj', 'stylesheets-prod', 'cwar', 'cwatcol', 'crow', 'exitMobileSection', 'dtf', 'email24', 'cplus-prod', 'ecommerce-service-prod', 'ds-prod', 'stylesheetsVendors-prod', 'stylesheetsConfig-prod', 'plugins', 'scripts', 'images', 'head'));
