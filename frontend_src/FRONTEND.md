# tylko

Frontend code structure for tylko.

Generated 16 April 2015 using [Frontend.md](http://github.com/animade/frontend-md)

---

### Stylesheets

````
..
|- .. _______________________________ #
|
|  |- src/
|
|    |- scss/
|      |- style.scss _________________________ #
|
|      |- vendors/
|        |- _slick.scss ______________________ # Slick.js vendor styling (Slider)
|
|      |- utils/
|        |- _helpers.scss ____________________ # Various 'utility' classes
|        |- _mixins.scss _____________________ # Mixins
|        |- _variables.scss __________________ # Global Variables
|
|      |- pages/
|        |- _account.scss ____________________ # todo: combine with checkout
|        |- _cart.scss _______________________ #
|        |- _checkout.scss ___________________ #
|        |- _confirmation.scss _______________ # Order confirmaton page
|        |- _contact.scss ____________________ #
|        |- _full.scss _______________________ # Full-window height pages
|        |- _home.scss _______________________ #
|        |- _library.scss ____________________ # My Library
|        |- _login.scss ______________________ #
|        |- _pdp.scss ________________________ # PDP
|        |- _review.scss _____________________ # checkout step 2 -- review order
|        |- _terms.scss ______________________ # Terms page styling -- mainly the old lists
|
|      |- layout/
|        |- _footer.scss _____________________ # Footer element
|        |- _grid.scss _______________________ # Grid
|        |- _header.scss _____________________ # Header element
|        |- _hero.scss _______________________ # Hero component
|
|      |- components/
|        |- _accordeon.scss __________________ # Mobile Accordeon Component: FAQ & accordeon
|        |- _buttons.scss ____________________ #
|        |- _dropdowns.scss __________________ # Dropdown component
|        |- _effects.scss ____________________ # css used for animating content
|        |- _forms.scss ______________________ # Global Form styling
|        |- _icons.scss ______________________ # Icons
|        |- _map.scss ________________________ # Map
|        |- _modal.scss ______________________ # Modal component
|        |- _more.scss _______________________ # More
|        |- _notifications.scss ______________ # notifications
|        |- _secondary.scss __________________ # secondary navigation
|        |- _share.scss ______________________ # Share component
|        |- _specs.scss ______________________ # Specs [sheet]
|        |- _swipe.scss ______________________ # Swipe: using native browser capabilities to build sliders on mobile devices
|        |- _table.scss ______________________ # table
|        |- _ui.scss _________________________ # Global UI elements
|
|      |- base/
|        |- _base.scss _______________________ # Main
|        |- _reset.scss ______________________ # YUI 3.5.0 reset.css (http://developer.yahoo.com/yui/3/cssreset/) - http://cssreset.com
|        |- _typography.scss _________________ # Global typography settings & Webfonts
|        |- _webfonts.scss ___________________ # Webfonts
````

### Javascripts

````
..
|- .. ___________________________ #
|
|  |- src/
|
|    |- js/
|      |- plugins.min.js _____________________ #
|      |- site.js ____________________________ # Main JS file
|
|      |- views/
|        |- hero.js __________________________ # Hero component
|        |- home.js __________________________ # Home Page view
|        |- library.js _______________________ # Library view
|        |- mobileAccordeon.js _______________ # Mobile Accordeon: Used by account and faq
|        |- pdp.js ___________________________ # Cart Page view
|
|      |- components/
|        |- cartController.js ________________ # cart -- add / remove / sync
|        |- dropdowns.js _____________________ # Dropdown component
|        |- filter.js ________________________ # Filter component (listing page)
|        |- forms.js _________________________ # Forms component
|        |- fullscreen.js ____________________ # Fullscreen page handling
|        |- images.js ________________________ # Smooth fade in of images on load
|        |- infinite.js ______________________ # Infinite load component
|        |- map.js ___________________________ # Google Map component
|        |- modals.js ________________________ # Modals component
|        |- more.js __________________________ # Read more component
|        |- niceScroll.js ____________________ #
|        |- notify.js ________________________ # Notifications handling
|        |- polyfills.js _____________________ #
|        |- scrolling.js _____________________ # onScroll animations, using requestAnimFrame
|        |- selects.js _______________________ # Select component
|        |- shop.js __________________________ # Cart Page view
|        |- slider.js ________________________ # Sliders [using slick.js]
|        |- swipe.js _________________________ # Swipe component -- uses native browser scroll
|        |- tests.js _________________________ # Global tests
````
