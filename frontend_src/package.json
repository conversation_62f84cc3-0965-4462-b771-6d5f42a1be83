{"name": "tylko", "version": "0.1.0", "description": "tylko", "frontend": {"name": "tylko", "stylesheets": "../../src/scss", "javascripts": "../../src/js"}, "browserslist": ["> 1%", "ie >= 11", "edge >= 15", "ff >= 65", "chrome >= 65", "safari >= 9", "opera >= 23", "ios >= 10", "android >= 4", "bb >= 10"], "engines": {"node": ">=16.20.0", "npm": ">=8.11.0"}, "watch": {"lint": {"patterns": ["src_vue/FrontContact"], "extensions": "js,vue", "quiet": false}}, "scripts": {"dev": "webpack --watch --no-progress --hide-modules", "dev-no-watch": "webpack --no-progress --hide-modules", "dev:ratingTool": "cross-env BUILD_CHUNK=ratingTool npm run dev", "dev:feedsAddToCategoryVue": "cross-env BUILD_CHUNK=feedsAddToCategoryVue npm run dev", "dev:feedsViewCategory": "cross-env BUILD_CHUNK=feedsViewCategory npm run dev", "dev:configurator": "cross-env BUILD_CHUNK=configurator npm run dev", "dev:vueCplus": "cross-env BUILD_CHUNK=vueCplus,vueCrow,ecommerceService npm run dev", "dev:cplusOffscreen": "cross-env BUILD_CHUNK=cplusOffscreen npm run dev", "dev:cWatWar": "cross-env NODE_ENV=development BUILD_CHUNK=cWatWar npm run dev", "dev:cwatcol": "cross-env NODE_ENV=development BUILD_CHUNK=cWatCol,ecommerceService npm run dev", "dev:vueCrow": "cross-env BUILD_CHUNK=vueCrow,ecommerceService npm run dev", "lint": "npm run lint:site && npm run lint:vue", "lint:watch": "npm-watch", "lint:site": "eslint './src/js/components/**/*.js' './src/js/site.js'", "lint:vue": "eslint './src_vue/**/*.*'", "lint:vue:cplus": "eslint './src_vue/Configurators/FrontCplus/**/*.*'", "lint:vue:ui": "eslint './src_vue/tylko_ui/**/*.*'", "lint:delivery-time-frames": "eslint './src_vue/DeliveryTimeFrames/**/*.*'", "lint:mail-24": "eslint './src_vue/Email24/**/*.*'", "test:cWatWar": "jest './src_vue/cWatWar/tests/cWatWar.test.js' --watch", "prod": "cross-env NODE_ENV=production NODE_OPTIONS='--max-old-space-size=8192' webpack && cross-env NODE_ENV=production NODE_OPTIONS='--max-old-space-size=8192' webpack --es5 --no-clean && cross-env NODE_ENV=production NODE_OPTIONS='--max-old-space-size=8192' webpack --config webpack-node.config.js", "prod:cookie": "cross-env NODE_ENV=production BUILD_CHUNK=vueConsents webpack", "prod:part1": "cross-env NODE_ENV=production NODE_OPTIONS='--max-old-space-size=8192' BUILD_CHUNK=ratingTool,feedsAddToCategoryVue,feedsViewCategory,offscreen,elevatorPitchAnimation,blackShelf3dHero,vueEmail24,atupale_2,vueConsents,items,vueDeliveryTimeFrames,priceFormatCore,priceFormatAutoInit,priceFormatAutoInitMultiple webpack", "prod:part2": "cross-env NODE_ENV=production NODE_OPTIONS='--max-old-space-size=8192' BUILD_CHUNK=wardrobes,vueCplusV2,vueCrowV2 webpack --no-clean", "prod:part3": "cross-env NODE_ENV=production NODE_OPTIONS='--max-old-space-size=8192' BUILD_CHUNK=empty webpack --no-clean", "prod-node-only": "cross-env NODE_ENV=production webpack --config webpack-node.config.js", "gulp": "gulp"}, "repository": "", "author": "tylko.com", "license": "BSD", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-export-default-from": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-arrow-functions": "^7.12.1", "@babel/plugin-transform-async-to-generator": "^7.12.1", "@babel/plugin-transform-destructuring": "^7.12.1", "@babel/preset-env": "^7.12.11", "@babel/preset-react": "^7.12.10", "@babel/register": "^7.12.10", "@types/lodash-es": "^4.17.6", "@types/three": "^0.155.0", "@typescript-eslint/eslint-plugin": "^4.14.2", "@typescript-eslint/parser": "^4.14.2", "axios": "^0.21.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "babel-loader": "^8.2.2", "babelify": "^10.0.0", "body-parser": "^1.19.0", "bower": "^1.8.12", "browser-sync": "^2.26.14", "browser-sync-webpack-plugin": "^2.3.0", "browserify": "^17.0.0", "clean-webpack-plugin": "^0.1.19", "cross-env": "^7.0.3", "css-loader": "^0.28.7", "del": "^6.0.0", "eslint": "^5.16.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-airbnb-base": "^13.2.0", "eslint-import-resolver-webpack": "^0.11.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-vue": "^5.2.3", "event-stream": "^4.0.1", "express": "^4.17.1", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^6.2.0", "fixed-data-table": "^0.6.5", "fp-ts": "^2.9.3", "frontend-md": "latest", "glob": "^7.1.6", "grunt-bowercopy": "^1.2.5", "grunt-contrib-connect": "^3.0.0", "grunt-contrib-jshint": "^3.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-jsonlint": "~2.1.3", "grunt-newer": "~1.3.0", "gulp": "^4.0.2", "gulp-autoprefixer": "7.0.1", "gulp-browserify": "^0.5.1", "gulp-concat": "latest", "gulp-flatten": "^0.4.0", "gulp-imagemin": "latest", "gulp-jasmine": "^4.0.0", "gulp-livereload": "^4.0.2", "gulp-minify-css": "~0.3.11", "gulp-plumber": "^1.2.1", "gulp-postcss": "~9.0", "gulp-purgecss": "^4.0.0", "gulp-rename": "latest", "gulp-run-command": "0.0.10", "gulp-sass": "5.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-strip-debug": "^3.0.0", "gulp-uglify": "^3.0.2", "gulp-util": "^3.0.5", "gulp-watch": "^5.0.1", "gulp-wrap": "^0.15.0", "gulp.spritesmith": "^6.11.0", "io-ts": "^2.2.13", "jasmine": "^3.6.4", "jest": "^26.6.3", "jest-without-globals": "0.0.3", "jquery": "^3.5.1", "load-grunt-tasks": "^5.1.0", "lodash": "^4.17.20", "merge-stream": "^2.0.0", "mini-css-extract-plugin": "^0.9.0", "node-sass": "6.0.1", "npm-watch": "^0.7.0", "optimize-css-assets-webpack-plugin": "^5.0.4", "react": "^17.0.1", "react-dom": "^17.0.1", "react-redux": "^7.2.2", "recharts": "^2.0.4", "redux": "^4.0.5", "resize-observer-polyfill": "^1.5.1", "resolve-url-loader": "^3.1.2", "run-sequence": "^2.2.1", "sass-loader": "^10.2.0", "script-loader": "^0.7.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^2.3.8", "time-grunt": "~1.4.0", "ts-loader": "^8.0.14", "typescript": "^4.1.3", "uglifyjs-webpack-plugin": "^2.2.0", "vinyl-buffer": "^1.0.1", "vinyl-named": "^1.1.0", "vinyl-source-stream": "^2.0.0", "vue-loader": "^15.9.6", "vue-style-loader": "^4.1.2", "vue-svg-inline-loader": "^1.5.1", "vue-template-compiler": "^2.6.12", "webpack": "^4.44.2", "webpack-bundle-tracker": "^1.3.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0", "webpack-fix-style-only-entries": "^0.4.0", "webpack-node-externals": "^1.7.2", "webpack-stream": "^4.0.1"}, "dependencies": {"@adyen/adyen-web": "^5.28.1", "@adyen/adyen-web3": "npm:@adyen/adyen-web@^3.21.0", "@babel/polyfill": "^7.11.5", "@primer-io/checkout-web": "^2.19.1", "@sentry/tracing": "^7.51.2", "@sentry/vue": "^7.43.0", "accounting": "^0.4.1", "axios": "^0.21.1", "body-scroll-lock": "^3.1.5", "chroma-js": "^2.1.0", "clone-deep": "^4.0.1", "core-js": "^3.8.3", "country-telephone-data": "^0.6.3", "css-raw-loader": "^0.1.2", "dat.gui": "^0.7.7", "date-fns": "^2.25.0", "es6-tween": "^5.5.10", "gl": "^4.9.0", "gl-vec2": "^1.3.0", "glsl-quad": "^1.0.0", "gsap": "3.6.0", "gulp": "^4.0.0", "gulp-babel": "^8.0.0", "gulp-if": "^3.0.0", "gulp-notify": "^4.0.0", "gulp-sequence": "^1.0.0", "hammerjs": "^2.0.8", "html-webpack-plugin": "^4.5.1", "lodash": "^4.17.20", "lodash-es": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "merge-jsons-webpack-plugin": "^2.0.1", "messageformat": "^2.3.0", "mixin": "^0.2.0", "moment": "^2.29.1", "moment-business-days": "^1.2.0", "nouislider": "^12.1.0", "orbit-controls-es6": "^2.0.1", "parsleyjs": "^2.9.2", "pixi.js": "^5.3.7", "portal-vue": "^2.1.7", "pubsub-js": "^1.9.2", "regl": "^2.0.1", "scrollmagic": "^2.0.8", "scrollmagic-plugin-gsap": "^1.0.4", "smoothstep": "^1.0.1", "three": "^0.155.0", "troika-three-text": "^0.52.4", "tweakpane": "^3.1.0", "url-loader": "^4.1.1", "v-click-outside": "^3.1.2", "v-mask": "^2.2.4", "vee-validate": "^3.4.5", "vue": "^2.6.12", "vue-awesome": "^4.1.0", "vue-cookies": "^1.7.4", "vue-flatpickr-component": "^8.1.6", "vue-fragment": "^1.5.1", "vue-i18n": "^8.22.4", "vue-js-modal": "^1.3.35", "vue-nouislider": "^1.0.1", "vue-nouislider-component": "0.0.5", "vue-router": "^3.5.1", "vue-slider-component": "^3.2.11", "vuex": "^3.6.2", "webpack-bundle-analyzer": "^4.4.0", "worker-loader": "^2.0.0"}}