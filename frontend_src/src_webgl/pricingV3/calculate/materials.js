function getColorGroup(shelfType, material) {
  if (shelfType === 0) {
    if ([0, 1, 3].includes(material)) {
      return 'group_1';
    }
    if (material >= 4 && material <= 11) {
      return 'group_2';
    }
  }
  if (shelfType === 1) {
    if ([0, 9, 10, 17].includes(material)) {
      return 'group_1';
    }
    if ([3, 11, 12, 18].includes(material)) {
      return 'group_2';
    }
    if ([1, 2, 7, 8, 16, 19].includes(material)) {
      return 'group_3';
    }
    if ([6, 15].includes(material)) {
      return 'group_4';
    }
  }
  // couldn't find a group, but that's not a problem
  return '';
}

export const calculateMaterialPrice = (geom, jettyPrice, priceCoefficients) => {
  const shelfTypeName = {
    0: 't01',
    1: 't02',
  }[geom.shelf_type];
  const colorGroup = getColorGroup(geom.shelf_type, geom.material);
  if (!shelfTypeName || !colorGroup) {
    return 0;
  }
  const coefName = `${shelfTypeName}_${colorGroup}`;
  return (priceCoefficients[coefName] || 0) * jettyPrice;
};
