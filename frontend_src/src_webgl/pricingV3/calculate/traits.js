import { ShelfType, Type02Color } from './enums';

const calculateShelfTypePrice = (shelfType, price, coefs) => {
    if (shelfType === ShelfType.TYPE02) return price * coefs.t02_factor;
    if (shelfType === ShelfType.VENEER_TYPE01) return price * coefs.t01v_factor;
    return 0;
};

const calculateDepthPrice = (depth, price, coefs) => {
    if (depth > 500) return price * coefs.depth_factor_600;
    if (depth > 400) return price * coefs.depth_factor_500;
    if (depth > 320) return price * coefs.depth_factor;
    return 0;
};


const calculateCategoryPrice = (category, price, coefs) => {
    const categoryName = `category_${category}_factor`;
    return price * coefs[categoryName];
};

export const calculateTraitsPrice = (data, price, coefs, includeCategory) => {
    const traitsPrice = {
        shelfType: calculateShelfTypePrice(data.shelf_type, price, coefs),
        depth: calculateDepthPrice(data.depth, price, coefs),
    };
    if (includeCategory) {
        traitsPrice.category = calculateCategoryPrice(data.shelf_category, price, coefs);
    }
    return Object.values(traitsPrice).reduce((total, item) => total + item, 0);
};

export const calculateWattyTraitsPrice = (data, price, coefs) => {
    const traitsPrice = {
      category: calculateCategoryPrice(data.shelf_category, price, coefs),
    };
    return Object.values(traitsPrice).reduce((total, item) => total + item, 0);
};
