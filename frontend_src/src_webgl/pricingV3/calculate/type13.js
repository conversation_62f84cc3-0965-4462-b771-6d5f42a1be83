import { calculateLengthPrice, sumLengths, computeLength } from './elements';
import {Type13Color} from "./enums";

export class Type13PriceCalculator {
    constructor(data, coefs, includeCategory) {
        this.watty = data;
        this.coefficients = coefs;
        this.includeCategory = includeCategory;  //unsed for now, will be probably needed in traits
        this.plywoodColors = [
            Type13Color.WHITE_PLYWOOD,
            Type13Color.GRAY_PLYWOOD,
            Type13Color.BLACK_PLYWOOD,
        ];
    }

    getPrice() {
        const basePrice = this.getBasePrice();
        const elementsPrice = this.getElementsPrice();
        const baseElementsPrice = basePrice + elementsPrice;

        const traitsPrice = this.getTraitsPrice(baseElementsPrice);
        const baseElementsTraitsPrice = baseElementsPrice + traitsPrice;

        const marginPrice = this.getMarginsPrice(baseElementsTraitsPrice);
        const logisticsPrice = this.getLogicsticsPrice();

        const genericPriceSum = basePrice + elementsPrice + traitsPrice + marginPrice + logisticsPrice;
        const regionalIncrease = this.getRegionalIncreasedPrice(genericPriceSum);

        return basePrice + elementsPrice + traitsPrice + marginPrice + logisticsPrice + regionalIncrease;
    }

    getBasePrice() {
        const width = this.watty.width / 1000;
        return (
            this.coefficients.type13_base_unit + (
                width * this.coefficients.type13_base_area
            )
        );
    }

    getElementsPrice() {
        const elementsPrice = {
            walls: this.calculateWallsPrice(this.watty.walls),
            slabs: this.calculateSlabsPrice(this.watty.slabs),
            doors: this.calculateDoorsPrice(this.watty.doors),
            backs: this.calculateBacksPrice(this.watty.backs),
            bars: this.calculateBarsPrice(this.watty.bars),
            drawers: this.calculateDrawersPrice(this.watty.drawers),
            cable_management: this.calculateCableManagementPrice(this.watty.cable_management),
        };
        return Object.values(elementsPrice).reduce((total, item) => total + item, 0);
    }

    getTraitsPrice(basePrice) {
        return 0;
    }

    getMarginsPrice(basePrice) {
        const category = this.watty.shelf_category || 'wardrobe';
        return basePrice * this.coefficients[`type13_margin_${category}`];
    }

    getLogicsticsPrice() {
        const width = this.watty.width / 1000;
        const height = this.watty.height / 1000;
        const depth = this.watty.depth / 1000;
        const volume = width * height * depth;
        const drawers_count = this.watty.drawers.length;
        return (
            volume * this.coefficients.type13_logs_vol
            + (drawers_count * this.coefficients.type13_logs_drawers)
            + (this.coefficients.type13_logs_base)
        );
    }

    getRegionalIncreasedPrice(basePrice) {
        return basePrice * this.coefficients.type_13_additional_increase
    }

    isMadeFromPlywood() {
        return this.plywoodColors.includes(this.watty.material);
    }

    calculateArea(elements, axis1, axis2) {
        return elements.reduce((total, elem) => total + (computeLength(elem, axis1) * computeLength(elem, axis2)), 0);
    }

    calculatePerimeter(elements, axis1, axis2) {
        return elements.reduce((total, elem) => total + (computeLength(elem, axis1) + computeLength(elem, axis2)) * 2, 0);
    }

    calculateWallsPrice(elements) {
        if (!elements) return 0;

        const areaCoefficient = (
            this.isMadeFromPlywood() ? this.coefficients.type13_t01_wall_area : this.coefficients.type13_t02_wall_area
        );
        const areaPrice = calculateArea(elements, 'y', 'z') * areaCoefficient;
        const unitPrice = this.coefficients.type13_wall_unit * elements.length;
        return unitPrice + areaPrice;
    }

    calculateSlabsPrice(elements) {
        if (!elements) return 0;

        const unitPrice = elements.length * this.coefficients.type13_slab_unit;
        const areaPrice = calculateArea(elements, 'x', 'z') * this.coefficients.type13_slab_area;
        return unitPrice + areaPrice;
    }

    calculateDoorsPrice(elements) {
        if (!elements) return 0;

        const unitPrice = elements.length * this.coefficients.type13_door_unit;
        const perimeterPrice = calculatePerimeter(elements, 'x', 'y') * this.coefficients.type13_door_perimeter;
        return unitPrice + perimeterPrice;
    }

    calculateBacksPrice(elements) {
        if (!elements) return 0;

        const unitPrice = elements.length * this.coefficients.type13_backs_unit;
        const areaPrice = calculateArea(elements, 'x', 'y') * this.coefficients.type13_backs_area;
        return unitPrice + areaPrice;
    }

    calculateBarsPrice(elements) {
        const regularBars = elements.filter(bar => bar.subtype !== 'z');
        const crossBars = elements.filter(bar => bar.subtype === 'z');
        return (
            this.coefficients.type13_bar_unit * regularBars.length
      + sumLengths(regularBars, 'x') * this.coefficients.type13_bar_length
      + this.coefficients.type13_crossbar_unit * crossBars.length
        );
    }

    calculateDrawersPrice(elements) {
        // NOTE: internal and extrernal means here if they're behind doors or not

        const externalDrawers = elements.filter(drawer => drawer.subtype === 'i' || drawer.subtype === 'b' || drawer.subtype === 'e');
        const internalDrawers = elements.filter(drawer => drawer.subtype === 'd');
        const externalDrawersPrice = calculateLengthPrice(
            externalDrawers,
            this.coefficients.type13_drawer_width,
            this.coefficients.type13_external_drawer_unit,
            'x',
        );
        const internalDrawersPrice = calculateLengthPrice(
            internalDrawers,
            this.coefficients.type13_drawer_width,
            this.coefficients.type13_internal_drawer_unit,
            'x',
        );
        return externalDrawersPrice + internalDrawersPrice;
    }

    calculateCableManagementPrice(elements) {
       if (!elements) return 0;
       return elements.length * (this.coefficients.type13_cable_management_unit || 0);
    }
}
