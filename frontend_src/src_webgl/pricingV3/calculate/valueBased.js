import { ShelfType, Type02Color } from './enums';

const calculateWidthValue = (type, width, price, coefs) => {
    if (type === ShelfType.TYPE01 || type === ShelfType.TYPE02) {
        const increaseStart = 2950;
        const increaseEnd = 3100;
        const increaseFactor = (width - increaseStart) / (increaseEnd - increaseStart);
        const increaseFactorNormalized = Math.max(Math.min(increaseFactor, 1), 0);
        return price * increaseFactorNormalized * coefs.width_300;
    }
    return 0;
};

const calculateHeightValue = (type, version, height, price, coefs) => {
    if ((type === ShelfType.TYPE01 || type === ShelfType.TYPE02)
        && height >= 2500) {
        return price * coefs.height_250;
    }
    return 0;
};

const calculateDepthValue = (type, version, depth, price, coefs) => {
    if (type === ShelfType.TYPE01 || type === ShelfType.TYPE02) {
        if (depth === 400) return price * coefs.depth_400;
        if (depth === 500) return price * coefs.depth_500;
        if (depth === 600) return price * coefs.depth_600;
    }
    return 0;
};

export const calculateValueBasedPrice = (data, price, coefs) => {
    const valueBasedPrice = {
        width: calculateWidthValue(data.shelf_type, data.width, price, coefs),
        height: calculateHeightValue(data.shelf_type, data.digital_product_version, data.height, price, coefs),
        depth: calculateDepthValue(data.shelf_type, data.digital_product_version, data.depth, price, coefs),
    };
    return Math.max(...Object.values(valueBasedPrice));
};


export const calculateAdditionalIncrease = (data, price, coefs) => {
  if (data.shelf_type === 0) return price * coefs.type_01_additional_increase;
  if (data.shelf_type === 1) return price * coefs.type_02_additional_increase;
  if (data.shelf_type === 2) return price * coefs.type_01v_additional_increase;
  return 0;
}
