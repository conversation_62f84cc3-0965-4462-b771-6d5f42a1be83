export const removeOverlappingElements = (target, source) => target.filter(targetItem => {
    const targetBounds = bounds(targetItem);
    const overlaps = source.filter(sourceItem => {
        const sourceBounds = bounds(sourceItem);
        const overlapMinY = checkOverlap(sourceBounds.minY, targetBounds.minY, targetBounds.maxY);
        const overlapMaxY = checkOverlap(sourceBounds.maxY, targetBounds.minY, targetBounds.maxY);
        const overlapMinX = checkOverlap(sourceBounds.minX, targetBounds.minX, targetBounds.maxX);
        const overlapMaxX = checkOverlap(sourceBounds.maxX, targetBounds.minX, targetBounds.maxX);

        return ((overlapMinY && overlapMaxY) && (overlapMinX || overlapMaxX));
    });
    return overlaps.length === 0;
});

const bounds = element => {
    const minX = Math.min(element.x1, element.x2);
    const maxX = Math.max(element.x1, element.x2);
    const minY = Math.min(element.y1, element.y2);
    const maxY = Math.max(element.y1, element.y2);
    return {
        minX, maxX, minY, maxY,
    };
};

const checkOverlap = (targetValue, sourceMin, sourceMax) => targetValue >= sourceMin && targetValue <= sourceMax;
