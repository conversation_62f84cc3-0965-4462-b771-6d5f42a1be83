import {
    capacityParams,
    frameParams,
    gradientParams,
    gridParams,
    patternParams,
    slantParams,
} from '@src_webgl/pricingV3/helpers/capacityParameters';

export const totalJettyCapacity = geom => calculateTotalCapacity(geom);
export const totalWattyCapacity = 250;

const calculateTotalCapacity = geom => {
    const cleanedHorizontals = cleanHorizontals(geom.horizontals);
    const hasDoorsOrDrawers = geom.doors.length > 0 || geom.drawers.length > 0;
    const parameters = parametersSwitch(geom.shelf_type, geom.pattern);
    const width = geom.width || calculateLength(geom.horizontals);
    const typeModifier = geom.shelf_type === 1 ? 0.66 : 1;

    const rowAmount = cleanedHorizontals.length - 1;
    const flatWidth = Math.floor(rowAmount * width / 1000);
    const adjustment = computeAdjustment(parameters, hasDoorsOrDrawers, rowAmount);

    const allWidths = openingWidths(geom.verticals, cleanedHorizontals);

    const openingCount = allWidths.length;
    const openingCountForAvg = Math.floor(openingCount * parameters.subset_len) || 1;
    const widthsForAvg = allWidths.slice(0, openingCountForAvg);
    const avgWidth = Math.floor(widthsForAvg.reduce((a, b) => a + b, 0) / widthsForAvg.length);

    const capacityForAvgWidth = calculateCapacity(parameters, avgWidth);

    const total = capacityForAvgWidth * adjustment * openingCount * parameters.param_3;

    const totalBounds = Math.max(
        Math.min(parameters.p_max * flatWidth, total),
        parameters.p_min * flatWidth,
        openingCount * parameters.o_min,
    );

    const totalNormalized = Math.floor(totalBounds * typeModifier / 10) * 10;

    const minWidth = Math.min(...allWidths);
    const maxWidth = Math.max(...allWidths);

    const compartmentMin = calculateCapacity(parameters, minWidth);
    const compartmentMax = calculateCapacity(parameters, maxWidth);

    const minBounds = Math.min(parameters.o_min, compartmentMin);
    const maxBounds = Math.max(parameters.o_max, compartmentMax);

    const minNormalized = Math.floor(minBounds * typeModifier / 10) * 10;
    const maxNormalized = Math.floor(maxBounds * typeModifier / 10) * 10;

    return {
        totalMaxCapacity: totalNormalized,
        compartmentCapacityMin: minNormalized,
        compartmentCapacityMax: maxNormalized,
    };
};

const cleanHorizontals = horizontals => {
    const levels = [...new Set(horizontals.map(item => item.y1))];
    const sorted = levels.map(level => horizontals.filter(item => item.y1 === level)).sort((a, b) => b - a);
    const joined = sorted.map(level => {
        const single = [];
        const multiple = [];
        level.forEach((item, index) => {
            const nextExists = level[index + 1] !== undefined;
            const prevExists = level[index - 1] !== undefined;

            const nextTouching = nextExists && level[index + 1].x1 === item.x2;
            const prevTouching = prevExists && level[index - 1].x2 === item.x1;

            if (nextTouching || prevTouching) {
                multiple.push(item);
            } else {
                single.push(item);
            }
        });
        const multipleChunks = [];
        const splitIndex = [];
        multiple.forEach((item, index) => {
            const nextExists = multiple[index + 1] !== undefined;
            const nextTouching = nextExists && level[index + 1].x1 === item.x2;

            if (nextExists && !nextTouching) {
                splitIndex.push(index);
            }
        });
        if (multiple.length > 0) {
            if (splitIndex.length === 0) {
                const chunk = calculateExtremes(multiple);
                multipleChunks.push(chunk);
            } else {
                // Currently there is maximum only one splitIndex, dividing multiple list in two parts.
                const id = splitIndex.pop();
                const firstChunk = calculateExtremes(multiple.slice(0, id));
                const secondChunk = calculateExtremes(multiple.slice(id));
                multipleChunks.push(firstChunk, secondChunk);
            }
        }

        return [...single, ...multipleChunks].sort((a, b) => a.x1 - b.x1);
    });
    return joined;
};

const calculateExtremes = elements => {
    const xValues = elements.flatMap(({ x1, x2 }) => [x1, x2]);
    const yValues = elements.flatMap(({ y1, y2 }) => [y1, y2]);
    const x1 = Math.min(...xValues);
    const x2 = Math.max(...xValues);
    const y1 = Math.min(...yValues);
    const y2 = Math.max(...yValues);
    return {
        x1, x2, y1, y2,
    };
};

const calculateLength = elements => {
    const xValues = elements.flatMap(({ x1, x2 }) => [x1, x2]);
    const xMin = Math.min(...xValues);
    const xMax = Math.max(...xValues);
    return xMax - xMin;
};

const parametersSwitch = (type, pattern) => {
    const typeInt = parseInt(type, 10);

    switch (pattern) {
    case 0:
        return slantParams;
    case 1:
        return typeInt === 0 ? gradientParams : gridParams;
    case 2:
        return patternParams;
    case 3:
        return typeInt === 0 ? gridParams : gradientParams;
    case 4:
        return typeInt === 0 ? slantParams : frameParams;
    default:
        return slantParams;
    }
};

const computeAdjustment = (parameters, hasDoorsOrDrawers, rowAmount) => {
    const doorAdjustment = hasDoorsOrDrawers ? parameters.doors : 1;
    return parameters.param_1 * (parameters.param_2 ** rowAmount) * doorAdjustment;
};

const openingWidths = (verticals, horizontals) => {
    const axisOffset = 9;
    const flatHorizontals = horizontals.flat();

    const verticalsByHorizontals = flatHorizontals.map(horizontal => verticals.filter(vertical => {
        const sameLevel = vertical.y1 === horizontal.y1 + axisOffset;
        const inRange = vertical.x1 >= horizontal.x1 && vertical.x1 <= horizontal.x2;
        return sameLevel && inRange;
    }).sort((a, b) => b.x1 - a.x1)).filter(item => item.length > 0);

    const openingsLevels = verticalsByHorizontals.map(level => level.map((item, index) => {
        const nextExists = level[index + 1] !== undefined;
        return nextExists ? Math.abs(level[index + 1].x1 - item.x1) : null;
    }).filter(item => item !== null));

    return openingsLevels.flat().sort((a, b) => b - a);
};

const calculateCapacity = (params, value) => {
    for (const key in capacityParams) {
        if (parseInt(key, 10) >= value) return capacityParams[key];
    }
    return params.default;
};
