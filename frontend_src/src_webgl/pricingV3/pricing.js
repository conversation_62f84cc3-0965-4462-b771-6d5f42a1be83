import { totalJettyCapacity, totalWattyCapacity } from '@src_webgl/pricingV3/helpers/capacity';
import {
  calculateBasePrice,
  calculateLogisticsPrice,
  calculateWattyBasePrice,
  calculateWattyLogisticsPrice,
} from './calculate/base';
import { calculateElementsPrice, calculateWattyElementsPrice } from './calculate/elements';
import { calculateTraitsPrice, calculateWattyTraitsPrice } from './calculate/traits';
import {
  calculateMarginsPrice,
  calculateWattyMarginsPrice,
  calculateRegionalIncrease,
} from './calculate/margins';
import { calculateMaterialPrice } from './calculate/materials';
import { calculateAdditionalIncrease, calculateValueBasedPrice } from './calculate/valueBased';
import { Type13PriceCalculator } from './calculate/type13';
import { ShelfType } from './calculate/enums';
import {Type13VeneerPriceCalculator} from "./calculate/type13Veneer";

class Pricing {
  constructor(data, coefs) {
    this.geometry = data;
    this.coefficients = coefs;
    this.vatFactor = 1.23;
    this.eurFactor = 4.30;
  }

  calculateJettyPrice(geom, includeCategory) {
    const basePrice = calculateBasePrice(geom, this.coefficients);
    const elementsPrice = calculateElementsPrice(geom, this.coefficients);
    const baseElementsPrice = basePrice + elementsPrice;

    const traitsPrice = calculateTraitsPrice(geom, baseElementsPrice, this.coefficients, includeCategory);

    const baseElementsTraitsPrice = baseElementsPrice + traitsPrice;

    const marginsPrice = calculateMarginsPrice(geom, baseElementsTraitsPrice, this.coefficients);

    const logisticPrice = calculateLogisticsPrice(geom, this.coefficients);
    const baseElementsTraitsMarginLogisticPrice = baseElementsTraitsPrice + marginsPrice + logisticPrice;

    const valueBasedIncrease = calculateValueBasedPrice(geom, baseElementsTraitsMarginLogisticPrice, this.coefficients);
    let priceSum = basePrice + elementsPrice + traitsPrice + marginsPrice + logisticPrice + valueBasedIncrease;
    const materialPrice = calculateMaterialPrice(geom, priceSum, this.coefficients);
    priceSum += materialPrice;
    const additionalIncrease = calculateAdditionalIncrease(geom, priceSum, this.coefficients);
    priceSum += additionalIncrease;

    return Math.trunc(priceSum * this.vatFactor / this.eurFactor);
  }

  calculateWattyPrice(geom) {
    const basePrice = calculateWattyBasePrice(geom, this.coefficients);
    const [elementsPrice, lightingPrice] = calculateWattyElementsPrice(geom, this.coefficients);

    const baseElementsPrice = basePrice + elementsPrice;
    const traitsPrice = calculateWattyTraitsPrice(
      geom,
      baseElementsPrice,
      this.coefficients,
    );
    const baseElementsTraitsPrice = baseElementsPrice + traitsPrice;

    const marginPrice = calculateWattyMarginsPrice(geom, baseElementsTraitsPrice, lightingPrice, this.coefficients);
    const logisticsPrice = calculateWattyLogisticsPrice;
    const genericPriceSum = basePrice + elementsPrice + traitsPrice + marginPrice + logisticsPrice;
    const regionalIncrease = calculateRegionalIncrease(genericPriceSum, this.coefficients);

    const priceSum = basePrice + elementsPrice + traitsPrice + marginPrice + logisticsPrice + regionalIncrease;
    return Math.trunc(priceSum * this.vatFactor / this.eurFactor);
  }

  calculateType13Price(geom, includeCategory) {
    const calculator = new Type13PriceCalculator(geom, this.coefficients, includeCategory);
    return Math.trunc(calculator.getPrice() * this.vatFactor / this.eurFactor);
  }

  calculateType13VeneerPrice(geom, includeCategory) {
    const calculator = new Type13VeneerPriceCalculator(geom, this.coefficients, includeCategory);
    return Math.trunc(calculator.getPrice() * this.vatFactor / this.eurFactor);  }

  getPrice(geom, furnitureModel = 'jetty') {
    const roundWithFixedPrecision = n => {
      const SAFE_PRECISION = 8;
      return Math.round(n.toFixed(SAFE_PRECISION));
    };
    let price;
    if (furnitureModel === 'jetty') {
      price = this.calculateJettyPrice(geom, true);
    } else if (geom.shelf_type === ShelfType.TYPE03) {
      price = this.calculateWattyPrice(geom);
    } else if (geom.shelf_type === ShelfType.TYPE13) {
      price = this.calculateType13Price(geom, true);
    } else {
      price = this.calculateType13VeneerPrice(geom, true);
    }
    let totalRoundedGrossRegional = roundWithFixedPrecision(price * (window.cstm.prices.priceeee || 1));

    const totalcapacity = furnitureModel === 'jetty' ? totalJettyCapacity(geom) : totalWattyCapacity;
    return {
      priceGrossRegional: totalRoundedGrossRegional,
      weight: 0,
      currencySymbol: window.cstm.prices.priceeel,
      priceGrossEur: price,
      capacity: totalcapacity,
    };
  }
}

const initPricing = async (data, originUrl) => {
  const coefs = await fetchCoefficientsVariables(originUrl);
  return new Pricing(data, coefs);
};

const fetchCoefficientsVariables = async url => {
  const response = await fetch(`${url === 'http://localhost:3000' ? 'http://127.0.0.1' : url}/api/v1/pricing/coefficients/`, {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
  });
  return response.json();
};

export { initPricing };
