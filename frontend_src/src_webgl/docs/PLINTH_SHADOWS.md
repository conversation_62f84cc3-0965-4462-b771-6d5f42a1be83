### Instrukcja otrzymana od Rafała

Mamy 3 stany - nazwijmy je **wide** (ten, kt<PERSON><PERSON> był wcześniej 110 i powyżej), **medium** (od 45 do 109 cm) i **narrow** (od 30 do 44 cm):
* wide - tu zostaje wszystko jak było - trzeba tylko podmienić wszystkie modele i tekstury, bo zmieniły się UVki)
* medium - tutaj też wszystko zostaje tak jak już zrobiliście - spłaszczyłem końcówki plinth_M_bar, żeby nie było nieprzyjemnego skalowania przy długich elementach
* narrow - tutaj zostaje tak jak zrobiliście element na szerokość z plinth_M_bar, który przebija się przez poprzeczki, a na głębokość mebla zamiast używania plinth_M_bar użyj "plinth_L_narrow.obj" i "plinth_R_narrow.obj" (odpowiednio dla lewej i prawej strony) - p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bę<PERSON>ziesz musiał naprawić rotację, żeby była taka jak w "plinth_M.obj" dla innych szerokości, bo teraz korzystasz z modelu, który jest domyślnie obrócony w stosunku do niego o 90*. Generalnie tak to ma wyglądać:

<p align="center"><img alt="Figure 1" src="images/figure_1.png"></p>

Co do cienia to tak - w wersji **wide** zostaje jak jest - dla przypomnienia jest to tak zbudowane w tej chwili - shadow_plinth_L.obj, shadow_plinth_R.obj i shadow_plinth_M.obj jest przesuwane zgodnie z pivotem elementów plinth_M.obj (i skalowane tak jak ten element - tylko na głębokość w zależności od głębokości mebla - 3,2 lub 4,0, albo 32,0 i 40,0, albo 320% i 400% - nie wiem gdzie macie przecinek :)); natomiast elementy shadow_plinth_M_bar.obj jest dodatkowo skalowany względem szerokości pomiędzy poprzeczkami:

<p align="center"><img alt="Figure 2" src="images/figure_2.png"></p>

Dla wersji **narrow** mamy dwa nowe obiekty: shadow_plinth_L_narrow.obj i shadow_plinth_R_narrow.obj - zachowują się one tak samo jak shadow_plinth_L.obj, shadow_plinth_R.obj w wersji **wide**, czyli skalujemy je tylko na głębokość i przesuwamy razem z pivotem odpowiednio plinth_L_narrow.obj i plinth_R_narrow.obj. W środku tak jak w **wide** jest shadow_plinth_M_bar.obj i jego skalujemy na szerokość. I teraz tak - jeśli chodzi o matematykę za tym: to szerokość tego środkowego elementu cienia powinna być o 10 cm mniejsza niż odległość między osiami "poprzeczek" ("plinth_L_narrow.obj" i "plinth_R_narrow.obj")
powinieneś móc wyciągnąć koordynaty "poprzeczek" i ich odległość od osi mebla i z tego wyliczyć skalę dla cienia. Dla przygładu to powinno wyglądać tak: w meblu szerokości 40 cm oś poprzeczki jest oddalona od środka mebla o 20,3 cm, czyli pomiędzy osiami poprzeczek jest 40,6 cm - od tego odejmujemy 10 cm i zostaje 30,6 cm (taką szerokość powinien mieć cień shadow_plinth_M_bar.obj - czyli dostaje skalę 3,06 (chyba w tym miejscu przecinek :))

<p align="center"><img alt="Figure 3" src="images/figure_3.png"></p>

Na deser najbardziej skomplikowany cień pod wersją **medium**:
na środku shadow_plinth_M_bar.obj analogicznie do wersji **narrow** czyli węższy o 10 cm od odległości między "poprzeczkami" (plinth_M.obj)
elementy shadow_plinth_L_medium.obj i shadow_plinth_R_medium.obj w miejscu pivotów od plinth_M.obj i analogicznie tylko skalowanie na głębokość regału
element shadow_plinth_R_medium_end.obj i shadow_plinth_L_medium_end.obj powinny mieć pozycję przesuniętą o 0,9 cm w prawo lub w lewo (prawy w prawo, lewy w lewo) od poprzeczki (0,9 cm czyli połowa grubości sklejki, która ma 1,8 cm). Natomiast skala na szerokość (w osi X - chyba) równą temu kawałkowi plinth_M_bar, który wystaje poza poprzeczki (czyli np. dla 10 cm wystający skala równa 1,0, a dla 6 cm skala równa 0,6). I teraz tak - wiem że nie masz długości, która wystaje poza poprzeczkę, ale możesz ją łatwo policzyć, bo masz długość plinth_M_bar i pozycje plinth_M, więc jak od długości plinth_M_bar odejmiesz dystans między dwoma plinth_M i jeszcze od tego odejmiejsz 1,8 cm (czyli grubość sklejki) to po podzieleniu wyniku przez dwa powinna być długość tych końcówek. Powinno działać ;-)

<p align="center"><img alt="Figure 4" src="images/figure_4.png"></p>

tutaj jeszcze pozycja pivotów - i z czego wynika to przesunięcie 0,9 cm

<p align="center">
    <img alt="Figure 4" src="images/figure_5.png">
    <img alt="Figure 4" src="images/figure_6.png">
</p>