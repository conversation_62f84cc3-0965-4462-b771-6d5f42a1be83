import {snapToEdge} from 'utils'

const box = new THREE.Box3()

function itemsAndFillingsOnRow(bottom_y, top_y, items, shelf) { // rest from window right now
    let fittings = shelf.getFillings()
    let takenPlaces = []
    for (let fitting of fittings) {
        if (fitting.y1 <= bottom_y && top_y <= fitting.y2) {
            takenPlaces.push([fitting.x1, fitting.x2])
        }
    }
    if (items) {
        for (let item of items) {
            let objectSize = box.setFromObject(item.controller).getSize()
            if (item.controller.position.y == bottom_y) { // && top_y >= item.controller.position.y + objectSize.y
                const halfWidth = (objectSize.x / 2) + 9
                takenPlaces.push([item.controller.position.x - halfWidth, item.controller.position.x + halfWidth])
            }
        }
    }

    return takenPlaces
}

function calculateFreeSpace(allTakenPlaces, verticals) {
    let listOfAllPosibilites = []
    let verticalTakenPlaces = []
    for (let i = 0; i < verticals.length; i++) {
        verticalTakenPlaces.push([verticals[i] - 9, verticals[i] + 9])
    }

    allTakenPlaces = allTakenPlaces.concat(verticalTakenPlaces)

    allTakenPlaces = allTakenPlaces.sort((a, b) => a[0] - b[0])

    for (let i = 0; i < allTakenPlaces.length - 1; i++) {
        listOfAllPosibilites.push([allTakenPlaces[i][1], allTakenPlaces[i + 1][0]])
    }

    // cleaning, lets remove those with length <2
    listOfAllPosibilites = listOfAllPosibilites.sort((a, b) => a[0] - b[0]).filter(x => x[1] - x[0] > 2)

    return listOfAllPosibilites
}

function snapToY({value, controller, shelf}) {
    let objectHight = box.setFromObject(controller).getSize().y + controller.data.margin_min_top * 10
    let horizontals = shelf.getHorizontals().map(el => el.y1 + 9).sort((a, b) => a - b)

    let smallestDist = Infinity
    let selectedHeight = 0
    horizontals.forEach((h, index) => {
        let dist = Math.abs(value - h)
        // if its last horizontal and item cant be placed there - skip
        if (!controller.data.can_be_on_top_horizontal && index == horizontals.length -1) {
            return
        }
        if (dist < smallestDist && (index == horizontals.length - 1 || (horizontals[index + 1] - h) >= objectHight)) {
            // lets filter possible horizontals here, instead on full list, to avoid strange height issues
            if (controller.data.min_y * 10 <= h && h <= controller.data.max_y * 10) {
                smallestDist = dist
                selectedHeight = h
            }
        }
    })
    smallestDist = Infinity
    controller.position.y = selectedHeight
    return selectedHeight
}

function snapToX({value, controller, shelf, items}) {
    let objectSize = box.setFromObject(controller).getSize()
    let snappingApproach = controller.data.snapping

    const halfWidth = (objectSize.x / 2) + 9
    const leftItemEdge = value - halfWidth - controller.data.margin_min_left * 10
    const rightItemEdge = value + halfWidth + controller.data.margin_min_right * 10


    let verticalsOnLowerLevel = shelf.getVerticals().filter(el => el.y1 === 9).map(el => el.x1).sort((a, b) => a - b)
    let verticals = shelf.getVerticals().filter(el => el.y1 === controller.position.y).map(el => el.x1).sort((a, b) => a - b)
    let isOnTop = shelf.getHorizontals().map(el => el.y2).sort((a, b) => b - a)[0] == controller.position.y - 9  // better reverse needed
    let takenPlaces = itemsAndFillingsOnRow(controller.position.y,
        controller.position.y + objectSize.y + (isOnTop ? 500 : 0),
        items.filter(x => x.controller.addToScene && x.controller.name != controller.name),shelf)
    let freeSpaceToSnap = calculateFreeSpace(takenPlaces, (!isOnTop ? verticals : [verticalsOnLowerLevel[0], verticalsOnLowerLevel[verticalsOnLowerLevel.length - 1]]))
    let selectedWidth = null

    let closestVerticalPair = null

    if (freeSpaceToSnap.length > 0) {
        // first check, lets see if its inside comparment
        for (let checkPair of freeSpaceToSnap) {
            const leftEdge = checkPair[0]
            const rightEdge = checkPair[1]
            let isInsideCompartment = leftEdge <= leftItemEdge && rightItemEdge <= rightEdge

            if (isInsideCompartment) {
                selectedWidth = snapToEdge(leftItemEdge, rightItemEdge, leftEdge, rightEdge, snappingApproach)
            }
        }
        // if not, lets find closest free space
        if (selectedWidth === null) {
            for (let checkPair of freeSpaceToSnap) {
                const leftEdge = checkPair[0]
                const rightEdge = checkPair[1]
                // check if its enough place to use this free space
                if ((rightEdge - leftEdge) >= objectSize.x + controller.data.margin_min_left * 10 + controller.data.margin_min_right * 10) {
                    // its first, so first candidate
                    if (closestVerticalPair === null) {
                        closestVerticalPair = [leftEdge, rightEdge]
                    } else {
                        // lets check if that hole is closer from initial than previous one
                        if (Math.min(Math.abs(value - leftEdge), Math.abs(value - rightEdge)) <
                            Math.min(Math.abs(value - closestVerticalPair[0]), Math.abs(value - closestVerticalPair[1]))) {
                            closestVerticalPair = [leftEdge, rightEdge]
                        }
                    }
                }
            }
            if (closestVerticalPair !== null) {
                selectedWidth = snapToEdge(leftItemEdge, rightItemEdge, closestVerticalPair[0], closestVerticalPair[1], snappingApproach)
            }
        }
    }
    if (selectedWidth !== null) {
        controller.position.x = selectedWidth
    }
    return selectedWidth
}

export function snap(el, scene, items, shelf) {
    // even before snapping, set proper depth for item
    el.controller.position.z = shelf.getDepth()
    // first, snap to y to get proper fittings and objects
    let new_width = null;
    let down_or_top = 1;
    for (let offset_on_y = 0; offset_on_y < 1500 && new_width === null; offset_on_y += 50) {
        snapToY({value: el.controller.position.y + down_or_top * offset_on_y, controller: el.controller, shelf})
        new_width = snapToX({value: el.controller.position.x, controller: el.controller, shelf, items})
        down_or_top *= -1
    }
    if (new_width !== null) {
        return true // We found place to put this item
    } else {
        console.error("MISSING place to put item ", el.controller.name)
        scene.removeItem(el.controller)
        return false // Items removed from all scenes, as snapping was not possible
    }
}