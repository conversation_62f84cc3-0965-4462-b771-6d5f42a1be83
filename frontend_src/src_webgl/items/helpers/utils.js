export function getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min)) + min;
}

// will return center of object to set (position x) to use in this snapping
export function snapToEdge(leftItemEdge, rightItemEdge, leftSpaceEdge, rightSpaceEdge, snapping) {
    let halfWidth = (rightItemEdge - leftItemEdge)/2
    if (snapping === 'SNAP TO CENTER') {
        return (rightSpaceEdge + leftSpaceEdge)/2
    } else if (snapping === 'SNAP TO LEFT') {
        return leftSpaceEdge + (rightItemEdge-leftItemEdge)/2
    } else if (snapping === 'SNAP TO RIGHT') {
        return rightSpaceEdge - (rightItemEdge-leftItemEdge)/2
    } else if (snapping === 'SNAP TO LEFT OR RIGHT') {
        if (Math.abs(leftSpaceEdge - rightItemEdge) > Math.abs(rightSpaceEdge - leftItemEdge)) {
            return rightSpaceEdge - halfWidth
        } else {
            return leftSpaceEdge + halfWidth
        }
    } else { // no snapping, two cases, inside or outside space
        if (leftSpaceEdge <= leftItemEdge && rightItemEdge <= rightSpaceEdge){
            return (leftItemEdge + rightItemEdge)/2
        } else {

            if (Math.abs(leftSpaceEdge - rightItemEdge) > Math.abs(rightSpaceEdge - leftItemEdge)) {
                return rightSpaceEdge - halfWidth
            } else {
                return leftSpaceEdge + halfWidth
            }
        }
    }
}