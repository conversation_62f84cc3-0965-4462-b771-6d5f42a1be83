import { snapToEdge } from './utils'

const box = new THREE.Box3()

let getDistanceFromPivotToBox = function(x,y, box) {
    return Math.min(
        Math.hypot(x-box.x1, y - box.y1),
        Math.hypot(x-box.x2, y - box.y1),
    )
}

export function snap(el, scene, items, shelf, strategy) {

    // even before snapping, set proper depth for item
    el.controller.position.z = shelf.getDepth()
    let objectSize = box.setFromObject(el.controller).getSize()

    let boxes = getAllFreeBoxes(shelf, items, el)

    // first, find all boxes that have enough space for this item and are in min_y, max_y
    boxes = boxes.filter(box=> box.y2-box.y1 >= (objectSize.y + el.controller.data.margin_min_top * 10)
        && (box.x2 - box.x1) >= (objectSize.x + (el.controller.data.margin_min_left + el.controller.data.margin_min_right) * 10))

    if (strategy && strategy.free_space_selection_approach === 'SMALLEST BOX FIRST') { // smollest possible boxes first
        boxes = boxes.sort((a,b) => ((a.y2-a.y1) + (a.x2-a.x1)/4) - ((b.y2-b.y1) + (b.x2-b.x1)/4)) // smallest, but height is more important
    } else { // closest strategy, min distance from pivot to right or left edge of box
        boxes = boxes.sort((a,b) => getDistanceFromPivotToBox(el.controller.position.x, el.controller.position.y, a) -
            getDistanceFromPivotToBox(el.controller.position.x, el.controller.position.y, b)) // normal distance
    }

    if (boxes.length > 0) {
        el.controller.position.y = boxes[0].y1
        el.controller.position.x = snapToEdge(el.controller.position.x - (objectSize.x/2) - el.controller.data.margin_min_left * 10,
            el.controller.position.x + (objectSize.x/2) + el.controller.data.margin_min_right * 10,
            boxes[0].x1, boxes[0].x2,
            el.controller.data.snapping
            )
        return true // We found place to put this item
    } else {
        console.error("MISSING place to put item ", el.controller.name)
        scene.removeItem(el.controller)
        return false // Items removed from all scenes, as snapping was not possible
    }
}


const halfMaterial = 9
const SMALLEST_OPENING = 100

function getItemsAndFillings(items, shelf) { // rest from window right now
    let takenPlaces = shelf.getFillings()
    if (items) {
        for (let item of items) {
            let objectSize = box.setFromObject(item.controller).getSize()
            const halfWidth = (objectSize.x / 2)
            takenPlaces.push({
                x1: item.controller.position.x - halfWidth - item.controller.data.margin_min_left * 10,
                x2: item.controller.position.x + halfWidth + item.controller.data.margin_min_right * 10,
                y1: item.controller.position.y,
                y2: item.controller.position.y + objectSize.y + item.controller.data.margin_min_top * 10
            })
        }
    }
    return takenPlaces
}

function getAllFreeBoxes(shelf, allItems, item){
    let items = allItems.filter(el => el.controller.addToScene)
    let boxes = []
    let horizontals = shelf.getHorizontals().sort((a, b) => a.y1 == b.y1 ? a.x1 - b.x1 : a.y1 - b.y1)
    let maxHeight = horizontals[horizontals.length - 1].y1
    let verticals = shelf.getVerticals().sort((a, b) => a.y1 == b.y1 ? a.x1 - b.x1 : a.y1 - b.y1)
    let itemsAndFeetings = getItemsAndFillings(items, shelf)


    horizontals.forEach((horizontal, horizontal_index) => {
        let connectedVerticals = verticals.filter(vertical => vertical.y1 - halfMaterial == horizontal.y1 && horizontal.x1 - 18 <= vertical.x1 && vertical.x1 <= horizontal.x2 + 18)
        connectedVerticals = connectedVerticals.concat(itemsAndFeetings.filter(item => item.y1 - halfMaterial == horizontal.y1 && horizontal.x1  - 18 <= item.x1 && item.x2 <= horizontal.x2 + 18))
        connectedVerticals = connectedVerticals.sort((a, b) => a.y1 == b.y1 ? a.x1 - b.x1 : a.y1 - b.y1)
        for (let i = 0; i < connectedVerticals.length - 1; i++) {
            if (connectedVerticals[i].x1 != connectedVerticals[i].x2) { // this is item or fitting
                let horizontalOnTop = horizontals.filter(h => {
                    let horizontalLeftEdge = h.x1 - 18
                    let horizontalRightEdge = h.x2 + 18
                    return h.y1 > horizontal.y1 // it must be higher than this one
                                && horizontalLeftEdge <= connectedVerticals[i].x2  && connectedVerticals[i + 1].x1 <= horizontalRightEdge
                    }
                )[0].y1
                if (horizontal.y1 == maxHeight) {
                    horizontalOnTop += 2000
                }
                if (connectedVerticals[i + 1].x1 - connectedVerticals[i].x2 <= SMALLEST_OPENING) {
                    continue
                }
                // if left edge of next item is inside the actual item we are checking - skip this one
                let rightItemEdge = null
                if (connectedVerticals[i + 1].x1 <= connectedVerticals[i].x2){
                    for (let j=0; j < connectedVerticals.length - 1 && rightItemEdge === null; j++ ) {
                        if (connectedVerticals[i + 1 + j].x1 > connectedVerticals[i].x2) {
                            rightItemEdge = connectedVerticals[i + 1 + j].x1
                        }
                    }
                }
                else {
                    rightItemEdge = connectedVerticals[i + 1].x1
                }

                boxes.push({
                    x1: connectedVerticals[i].x2,
                    x2: rightItemEdge,
                    y1: horizontal.y1 + 9,
                    y2: horizontalOnTop + 9   // i hope it will always be at least one
                })
            } else
            {
                if (connectedVerticals[i + 1].x1 - connectedVerticals[i].x1 <= SMALLEST_OPENING) {
                    continue
                }
                // lets find top horizontal for this pair of verticals, for special double opening pattern case
                let horizontalOnTop = horizontals.filter(h => {
                        let horizontalLeftEdge = h.x1 - 9
                        let horizontalRightEdge = h.x2 + 9
                        return h.y1 > horizontal.y1 // it must be higher than this one
                            && ((horizontalLeftEdge <= connectedVerticals[i].x1 && connectedVerticals[i + 1].x1 <= horizontalRightEdge) ||
                                (horizontalLeftEdge <= connectedVerticals[i].x1 && connectedVerticals[i + 1].x1 >= horizontalRightEdge))
                    }
                )[0].y1
                if (horizontal.y1 == maxHeight) {
                    horizontalOnTop += 2000
                }

                boxes.push({
                    x1: connectedVerticals[i].x1,
                    x2: connectedVerticals[i + 1].x1,
                    y1: horizontal.y1 + 9,
                    y2: horizontalOnTop + 9   // i hope it will always be at least one
                })
            }
        }
        if (item && connectedVerticals.length === 0 && horizontal.y1 === maxHeight && item.controller.data.can_be_on_top_horizontal){
            let verticalsByX = verticals.sort((a,b) => a.x1 - b.x1)
            boxes.push({
                x1: verticalsByX[0].x1,
                x2: verticalsByX[verticalsByX.length-1].x1,
                y1: horizontal.y1 + 9,
                y2: horizontal.y1 + 9 + 2000,
            })
        }
    })
    if (item) {
        boxes = boxes.filter(h => item.controller.data.min_y * 10 <= h.y1 && h.y1 <= item.controller.data.max_y * 10)
    }
    return boxes
}




export function drawEmptyPlaces(scene, items, shelf) {
    let boxes = getAllFreeBoxes(shelf, items)
    boxes.map( b =>
        scene.addBox(b.x1,b.y1,b.x2,b.y2)
    )

}
