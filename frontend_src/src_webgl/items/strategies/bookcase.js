import {snap} from "../helpers/snapping_box_version";
import getRandomInt from '../helpers/utils'

export function bookcase_strategy(scene, items, groups, strategy, shelf) {
    const numberOfRandomElementsFromMain = 5
    const numberOfRandomElementsFromAdditional = 2

    let addedElements = []

    scene.clearItems(items)

    // ok, lets take from two groups - books and additions
    let books = groups[0].items
    let additions = groups[1].items

    // get random numberOfRandomElements amount of items
    let randomElementIds = books.sort(() => Math.random() - Math.random()).slice(0, numberOfRandomElementsFromMain)
    randomElementIds = randomElementIds.concat(additions.sort(() => Math.random() - Math.random()).slice(0, numberOfRandomElementsFromAdditional))


    let randomElements = randomElementIds.map(randomElementIds => items.find(x=> x.controller.data.id == randomElementIds))
    // for now, lets add total random value based splitting
    let shelfWidth = shelf.getWidth()
    let shelfHeight = shelf.getHeight()

    randomElements.forEach(el => {
        if (el) {

            el.controller.position.x = getRandomInt(-shelfWidth/2, shelfWidth/2);
            el.controller.position.y = getRandomInt(0, shelfHeight);
            if (snap(el, scene, items, shelf)) {
                scene.addItem(el.controller)
            }
        }
    })
}