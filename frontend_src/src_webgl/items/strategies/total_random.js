import {snap} from "../helpers/snapping_box_version";
import getRandomInt from '../helpers/utils'

export function randomize(scene, items, groups, strategy, shelf) {
    const numberOfRandomElements = 5
    let addedElements = []


    scene.clearItems(items)

    // get random numberOfRandomElements amount of items
    const randomElements = items.sort(() => Math.random() - Math.random())

    // for now, lets add total random value based splitting

    let shelfWidth = shelf.getWidth()
    let shelfHeight = shelf.getHeight()

    for (let i=0; i < randomElements.length && addedElements.length < numberOfRandomElements; i++) {
        let el = randomElements[i];
        el.controller.position.x = getRandomInt(-shelfWidth / 2, shelfWidth / 2);
        el.controller.position.y = getRandomInt(0, shelfHeight);
        if (snap(el, scene, items, shelf)) {
            scene.addItem(el.controller)
            addedElements.push(el)
        }
    }
}