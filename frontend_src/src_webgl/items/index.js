import * as dat from 'dat.gui'
import OBJLoader from '../../src/js/configurator/loaders/OBJLoader'

import {snap} from './helpers/snapping_box_version'
import {drawEmptyPlaces} from './helpers/snapping_box_version'
import {randomize} from './strategies/total_random'
import {bookcase_strategy} from './strategies/bookcase'
import {backend_strategy} from './strategies/backend'
import {initItems} from './loaders/loadItem'

import SceneService from "./services/SceneService";
import ShelfService from "./services/ShelfService";



// TODO: better encapsulate scope of items here, right now its used in all places
window.items = {
    CONTROLLER_LIST: [],
    noItems: false,
}

function getJSON() {
    return new Promise((resolve, reject) => {
        const ajax = new XMLHttpRequest()
        ajax.open("GET", '/items/all/', false)
        ajax.send(null)
        if (ajax.status === 200) {
            resolve(JSON.parse(ajax.responseText))
        } else {
            console.error(ajax.statusText)
            reject(ajax.statusText)
        }
    })
}

window.addEventListener('load', () => {
    getJSON().then(json => {

        const scene = new SceneService("scene", "ivyScreenshot.scene", true)
        const shelf = new ShelfService('ivy', true)

        // filter only one group if possible
        let urlParams = new URLSearchParams(window.location.href);
        let filteredItems = json.items
        // TODO: when we have more items - move filtering to backend
        if (urlParams.has('items_group')){
            let selectedGroupId = urlParams.get('items_group')
            let selectedGroup = json.groups.filter(x => x.id == selectedGroupId)
            if (selectedGroup.length == 1) {
                filteredItems = json.items.filter(i => selectedGroup[0].items.includes(i.id) )
            } else {
                console.error("didnt find group or more than one group found")
            }
        }



        // TODO: whole gui is not required in feed tool mode, to be removed
        const gui = new dat.GUI()
        // Screenshot button
        const makeScreenshot = {screenshot: false}

        // Randomize button
        let guiSetup = {old_randomize: false, distance: 0, showBox: false, old_bookcase: false, empty_places: false}

        gui.add(makeScreenshot, "screenshot").onChange(() => window.customEditor.CEScreenshotRenderer())


        gui.add(guiSetup, "empty_places").onChange(() => drawEmptyPlaces(scene, window.items.CONTROLLER_LIST.filter(el => el.controller.addToScene), shelf))



        gui.add(guiSetup, "showBox").onChange(value => {
            scene.toggleBoundingBoxes(value, window.items.CONTROLLER_LIST)
        })

        const folder = gui.addFolder('Strategies')

        gui.add(guiSetup, "old_randomize").onChange(() => {
            randomize(scene, window.items.CONTROLLER_LIST, json.groups, null, shelf)
        })

        gui.add(guiSetup, "old_bookcase").onChange(() => {
            bookcase_strategy(scene, window.items.CONTROLLER_LIST, json.groups, null, shelf)
        })



        json.strategies.map(strategy => {
            let strategy_name = `${strategy['id']}:${strategy['name']}`.replace(' ','_')
            guiSetup[strategy_name] = false;
            folder.add(guiSetup, strategy_name).onChange(() => {
                backend_strategy(scene, window.items.CONTROLLER_LIST, json.groups, strategy, shelf)
            })
        })

        // Fix dat.gui z-index
        $('.dg.ac').css('z-index', 9999)

        // Wait for json to load, then init. If no items - lets publish loaded event right now,
        // and set flag (for things that may be loaded after that pubsub. Lets allow loading before and after this
        if (filteredItems.length === 0) {
            window.items.noItems = true;
            window.PubSub.publish("allItemsLoaded");
        } else {
            initItems(filteredItems, gui, scene, window.items.CONTROLLER_LIST, shelf);
        }

        window.PubSub.subscribe('shelfChangedSnapped', () => {
            window.items.CONTROLLER_LIST.filter(el => el.controller.addToScene).forEach(el => snap(el, scene, window.items.CONTROLLER_LIST, shelf))
        })
        window.PubSub.subscribe('itemsRandomize', (name, data) => {
            if (data.strategy) {
                console.log('Selected strategy: ', data.strategy)
                let strategy = json.strategies.find(strategy => strategy.id === data.strategy)
                backend_strategy(scene, window.items.CONTROLLER_LIST, json.groups, strategy, shelf)
            } else {
                randomize(scene, window.items.CONTROLLER_LIST, json.groups,null, shelf)
            }

        })
        window.PubSub.subscribe('allItemsLoaded', () => {
            console.log('allItemsLoaded!')
        })

        window.PubSub.subscribe('item_added', (name, data) => {
            let nameWithoutGroup = data.name.replace('_group','')
            gui.__folders[nameWithoutGroup].name += ' ADDED'
        })

        window.PubSub.subscribe('item_removed', (name, data) => {
            let nameWithoutGroup = data.name.replace('_group','')
            gui.__folders[nameWithoutGroup].name = nameWithoutGroup
        })

    })
})
