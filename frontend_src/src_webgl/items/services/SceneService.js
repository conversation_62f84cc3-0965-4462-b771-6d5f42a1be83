// threejs, configurator + offscreent, typical bundle + feed tool
export default class SceneService {
    constructor(configuratorScene, offscreenScene, lazyCheck) {
        this.configuratorScene = configuratorScene
        this.offscreenScene = offscreenScene
        this.lazyCheck = lazyCheck
        this.box_list = []
    }
    get allScenes() {
        if (this.lazyCheck && typeof(this.configuratorScene) === 'string') {
            if (typeof window[this.configuratorScene] !== 'undefined') {
                this.configuratorScene = eval(`window.${this.configuratorScene}`)
            }
        }
        if (this.lazyCheck && typeof(this.offscreenScene) === 'string') {
            if (typeof eval(`window.${this.offscreenScene}`) !== 'undefined') {
                this.offscreenScene = eval(`window.${this.offscreenScene}`)
            }
        }
        return [this.configuratorScene, this.offscreenScene].filter(x=>
            x && typeof(x) !== 'string')
    }
    addItem(item){
        item.addToScene = true
        window.PubSub.publish('item_added', {'name': item.name})
        let possibleScenes = this.allScenes;
        let mainScene = possibleScenes.slice(0,1)
        let otherScenes = possibleScenes.slice(1,)

        mainScene.map(s => s.add(item))
        otherScenes.map(s => s.add(item.clone()))

    }
    removeItem(item){
        item.addToScene = false
        window.PubSub.publish('item_removed', {'name': item.name})
        this.allScenes.map(s => {
            let itemToBeRemovedOnThisScene = s.getObjectByName(item.name)
            s.remove(itemToBeRemovedOnThisScene)
        })
    }
    clearItems(items){
        items.forEach(el => {
            if (el.controller.addToScene) {
                this.removeItem(el.controller)
            }
        })
    }
    toggleBoundingBoxes(value, items){
        // clear scene
        if (value) {
            // add required boxes
            items.forEach(el => {
                if (el.controller.addToScene){
                    let box = new THREE.BoxHelper(el.controller, 0xffff00);
                    this.allScenes[0].add(box);
                    this.box_list.push(box);
                }
            })
        } else {
            this.box_list.forEach(el => {
                this.allScenes[0].remove(el);
            })
            this.box_list = []
        }
    }
    addBox(x1,y1,x2,y2){
        const depth = 400
        let geometry = new THREE.BoxGeometry( (x2-x1)-50, (y2-y1)-50, depth );
        let material = new THREE.MeshBasicMaterial( {color: 0x00ff00, wireframe: true} );
        let cube = new THREE.Mesh( geometry, material );
        cube.position.x = (x1+x2)/2
        cube.position.y = (y1+y2)/2
        cube.position.z = depth/2

        this.allScenes[0].add(cube);
        this.box_list.push(cube);
    }
}
