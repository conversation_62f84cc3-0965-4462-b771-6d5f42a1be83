// threejs, configurator + offscreent, typical bundle + feed tool
export default class ShelfService {
    constructor(shelf, lazyCheck) {
        if (lazyCheck) {
            this._shelf = null
            this.shelfString = shelf
            this.lazyCheck = true
        } else {
            this._shelf = shelf
            this.lazyCheck = false
        }
    }
    get shelf() {
        if (!this._shelf && this.lazyCheck) {
            if (typeof window[this.shelfString] !== 'undefined') {
                this._shelf = window[this.shelfString]
            }
        }
        return this._shelf
    }
    getDepth() {
        return this.shelf.depth
    }
    getWidth() {
        return this.shelf.getWidth()
    }
    getHeight() {
        return this.shelf.getHeight()
    }
    getHorizontals() {
        return this.shelf.points.horizontals
    }
    getVerticals() {
        return this.shelf.points.verticals
    }
    getOpeningsNumber() {
        // TODO: change to something more accurate, this one is ROUGH estimation
        return this.shelf.points.additional_elements.shadow_middle.length - this.shelf.points.drawers.length
            - (this.shelf.points.doors.length / 2)
    }
    getFillings() {
        return this.shelf.points.drawers.concat(this.shelf.points.doors)
    }


}
