import {snap} from '../helpers/snapping_box_version'

const BASE_PATH = '/r_static/items_on_shelves/'
const MOVEMENT_AMOUNT = 2500



export function initItems(OBJ_LIST, gui, scene, itemsList, shelf) {
    const objectLoader = new THREE.OBJLoader()
    const textureLoader = new THREE.TextureLoader()
    const cubeLoader = new THREE.CubeTextureLoader()


    function generateItem(index) {
        const item = OBJ_LIST[index]
        const box = new THREE.Box3()
        let objectSize = null,
            controller = null
        if (item.textures.length === 0) {
            console.log('missing textures for item ', index)
            return
        }
        const loaderPromise = new Promise(resolve => {
            const textureMap = textureLoader.load(item.textures[0].texture_file)
            textureMap.needsUpdate = true

            // TODO: Delete or start using - after tests on actual items, check PBR approach first with Rafal
            // const roughnessMap = textureLoader.load(BASE_PATH + 'textures/ENV/' + 'Hay_Sonos_roughness.png')
            // roughnessMap.magFilter = THREE.NearestFilter

            resolve({textureMap})
        })

        loaderPromise.then(function ({textureMap}) {
            objectLoader.load(item.model, object => {
                object.rotation.x = -Math.PI / 2
                object.name = item.name
                object.traverse(child => {
                    if (child instanceof THREE.Mesh) {
                        child.material = new THREE.MeshBasicMaterial({
                            map: textureMap,
                            // reflectivity: .4,
                            transparent: item.is_transparent,
                        })
                    }
                })

                generateShadow(item).then(({shadow}) => {
                    if (shadow) {
                        const group = new THREE.Group()
                        group.add(object)
                        group.add(shadow)
                        group.name = `${item.name}_group`
                        controller = group
                    } else {
                        controller = object
                    }
                    controller.addToScene = false
                    controller.data = item
                    objectSize = box.setFromObject(controller).getSize()
                    const el = {
                        controller,
                        objectSize,
                    }
                    hookToGui({controller, item, objectSize, itemsList})
                })

            })
        })
    }

    function generateShadow(item) {
        return new Promise((resolve, reject) => {
            if (!!item.shadow_model) {
                textureLoader.load(item.shadow_texture, texture => {
                    objectLoader.load(item.shadow_model, object => {
                            object.name = `${item.name}_shadow`
                            object.traverse(child => {
                                if (child.isMesh) {
                                    child.material.map = texture
                                    child.material.transparent = true
                                    child.material.alphaTest = 0.05
                                }
                            })
                            object.rotation.x = -Math.PI / 2
                            resolve({shadow: object})
                        },
                        undefined, err => {
                            console.error(err)
                            reject(err)
                        }
                    )
                }, undefined, err => {
                    console.error(err)
                    reject(err)
                })
            } else {
                resolve({shadow: false})
            }
        })
    }

    function hookToGui({controller, item, objectSize, itemsList}) {
        controller.addToScene = false
        controller.edited = false
        itemsList.push({controller, objectSize})
        if (itemsList.length == OBJ_LIST.length) {
            window.PubSub.publish("allItemsLoaded");
        }
        const folder = gui.addFolder(item.name)
        // X
        folder.add(controller.position, "x", -MOVEMENT_AMOUNT, MOVEMENT_AMOUNT).onChange(value => {
            controller.position.x = value
            snap({controller}, scene, itemsList, shelf)
        }).listen()

        // Y
        folder.add(controller.position, "y", -MOVEMENT_AMOUNT, MOVEMENT_AMOUNT).onChange(value => {
            controller.position.y = value
            snap({controller}, scene, itemsList, shelf)
        }
        ).listen()

        // Display object
        folder.add(controller, "addToScene").onChange((value) => {
            // its after change here
            if (!value) {
                scene.removeItem(controller)
            } else {
                if(snap({controller}, scene, itemsList, shelf)){
                    scene.addItem(controller)
                }
            }
        }).listen()
        folder.add(controller, 'edited').onChange(() => {
            window.open(`/admin/items_for_render/item/${controller.data.id}/change/`, '_blank');
        }).name('Edit item')
    }

    // Generate a number of items
    for (let i = 0; i < OBJ_LIST.length; i++) {
        generateItem(i)
    }

}