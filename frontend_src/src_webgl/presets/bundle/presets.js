function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }

  return obj;
}

function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);

  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) symbols = symbols.filter(function (sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    });
    keys.push.apply(keys, symbols);
  }

  return keys;
}

function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};

    if (i % 2) {
      ownKeys(source, true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(source).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }

  return target;
}

// General 'Black Color' material settings
var material_id = 1;
var color = 'black';
var materialSettings = {
  material_id: material_id,
  color: color,
  hex_color: 0x474747,
  hex_handler_color: 0x555555,
  backs_color: 0x393939,
  opacity: 0.6,
  reflectivityValue: 0.18,
  reflectivityValueDoors: 0.5
};
var shadowSettings = {
  lightPoint: {
    x: 100,
    y: 200,
    z: 300
  },
  liveShadowEnabled: false
};

var settings = _objectSpread2({
  id: material_id
}, materialSettings, {}, shadowSettings);

// General 'White Color' material settings
var material_id$1 = 0;
var color$1 = 'white';
var materialSettings$1 = {
  material_id: material_id$1,
  color: color$1,
  // hex_color: 0xffffff,
  // hex_handler_color: 0xFFFFFF,
  // backs_color: 0xDADADA,
  // opacity: 0.35,
  // reflectivityValue: 0.02,
  // reflectivityValueDoors: 0.06
  hex_color: 0xFFFFFF,
  hex_handler_color: 0xFFFFFF,
  backs_color: 0xfffefa,
  opacity: 0,
  reflectivityValue: 0,
  reflectivityValueDoors: 0
};
var shadowSettings$1 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$1 = _objectSpread2({
  id: material_id$1
}, materialSettings$1, {}, shadowSettings$1);

// General 'Gray Color' material settings
var material_id$2 = 3;
var color$2 = 'grey';
var materialSettings$2 = {
  material_id: material_id$2,
  color: color$2,
  hex_color: 0xcfcfcf,
  hex_handler_color: 0xC9C9C9,
  backs_color: 0x818181,
  opacity: 0.55,
  reflectivityValue: 0.14,
  reflectivityValueDoors: 0.17
};
var shadowSettings$2 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$2 = _objectSpread2({
  id: material_id$2
}, materialSettings$2, {}, shadowSettings$2);

// General 'Ash Color' material settings
var material_id$3 = 5;
var color$3 = 'ash';
var materialSettings$3 = {
  material_id: material_id$3,
  color: color$3,
  hex_color: 0x011615a,
  hex_handler_color: 0x015b55,
  backs_color: 0x013e3c,
  opacity: 0.55,
  reflectivityValue: 0.07,
  reflectivityValueDoors: 0.3
};
var shadowSettings$3 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$3 = _objectSpread2({
  id: material_id$3
}, materialSettings$3, {}, shadowSettings$3);

// General 'Maple Color' material settings
var material_id$4 = 2;
var color$4 = 'maple';
var materialSettings$4 = {
  material_id: material_id$4,
  color: color$4,
  hex_color: 0x34ff88,
  hex_handler_color: 0x646464,
  backs_color: 0x313131,
  opacity: 0.35,
  reflectivityValue: 0.18,
  reflectivityValueDoors: 0.17
};
var shadowSettings$4 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$4 = _objectSpread2({
  id: material_id$4
}, materialSettings$4, {}, shadowSettings$4);

// General 'Purple Color' material settings
var material_id$5 = 4;
var color$5 = 'purple';
var materialSettings$5 = {
  material_id: material_id$5,
  color: color$5,
  hex_color: 0x856b76,
  hex_handler_color: 0x67525e,
  backs_color: 0x5a464f,
  opacity: 0.35,
  reflectivityValue: 0.18,
  reflectivityValueDoors: 0.29
};
var shadowSettings$5 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$5 = _objectSpread2({
  id: material_id$5
}, materialSettings$5, {}, shadowSettings$5);

// General 'White Color' material settings
var material_id$6 = 0;
var color$6 = 'basic_white';
var materialSettings$6 = {
  material_id: material_id$6,
  color: color$6,
  hex_color: 0xFFFFFF,
  hex_handler_color: 0xFFFFFF,
  backs_color: 0xfffefa,
  opacity: 0,
  reflectivityValue: 0,
  reflectivityValueDoors: 0
};
var shadowSettings$6 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$6 = _objectSpread2({
  id: material_id$6
}, materialSettings$6, {}, shadowSettings$6);

// General 'White Color' material settings
var material_id$7 = 3;
var color$7 = 'beige';
var materialSettings$7 = {
  material_id: material_id$7,
  color: color$7,
  hex_color: 0xf3e9db,
  hex_handler_color: 0xf3e9db,
  backs_color: 0xebd8c7,
  opacity: 0.35,
  reflectivityValue: 0.08,
  reflectivityValueDoors: 0.08
};
var shadowSettings$7 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$7 = _objectSpread2({
  id: material_id$7
}, materialSettings$7, {}, shadowSettings$7);

// General 'White Color' material settings
var material_id$8 = 2;
var color$8 = 'indygo';
var materialSettings$8 = {
  material_id: material_id$8,
  color: color$8,
  hex_color: 0x323C4A,
  hex_handler_color: 0x323C4A,
  backs_color: 0x1a1f2b,
  opacity: 0.6,
  reflectivityValue: 0.18,
  reflectivityValueDoors: 0.18
};
var shadowSettings$8 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$8 = _objectSpread2({
  id: material_id$8
}, materialSettings$8, {}, shadowSettings$8);

// General 'White Color' material settings
var material_id$9 = 4;
var color$9 = 'mint';
var materialSettings$9 = {
  material_id: material_id$9,
  color: color$9,
  hex_color: 0xF0F7F3,
  hex_handler_color: 0xF0F7F3,
  backs_color: 0x7c9393,
  opacity: 0.35,
  reflectivityValue: 0.07,
  reflectivityValueDoors: 0.07
};
var shadowSettings$9 = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$9 = _objectSpread2({
  id: material_id$9
}, materialSettings$9, {}, shadowSettings$9);

// General 'White Color' material settings
var material_id$a = 1;
var color$a = 'orange';
var materialSettings$a = {
  material_id: material_id$a,
  color: color$a,
  hex_color: 0xBE5A45,
  hex_handler_color: 0xBE5A45,
  backs_color: 0x742a1e,
  opacity: 0.35,
  reflectivityValue: 0.12,
  reflectivityValueDoors: 0.12
};
var shadowSettings$a = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$a = _objectSpread2({
  id: material_id$a
}, materialSettings$a, {}, shadowSettings$a);

// General 'ashVeneer Color' material settings
var material_id$b = 0;
var color$b = 'ash_veneer';
var materialSettings$b = {
  material_id: material_id$b,
  color: color$b,
  hex_color: 0x011615a,
  hex_handler_color: 0x015b55,
  backs_color: 0x013e3c,
  opacity: 0.55,
  reflectivityValue: 0.07,
  reflectivityValueDoors: 0.09
};
var shadowSettings$b = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$b = _objectSpread2({
  id: material_id$b
}, materialSettings$b, {}, shadowSettings$b);

// General 'oakVeneer Color' material settings
var material_id$c = 1;
var color$c = 'oak_veneer';
var materialSettings$c = {
  material_id: material_id$c,
  color: color$c,
  hex_color: 0x011615a,
  hex_handler_color: 0x015b55,
  backs_color: 0x013e3c,
  opacity: 0.55,
  reflectivityValue: 0.07,
  reflectivityValueDoors: 0.12
};
var shadowSettings$c = {
  lightPoint: {
    x: 0,
    y: 0,
    z: 0
  },
  liveShadowEnabled: false
};

var settings$c = _objectSpread2({
  id: material_id$c
}, materialSettings$c, {}, shadowSettings$c);

var enabledMaterials = [settings$1, settings, settings$4, settings$2, settings$5, settings$3];
var enabledBasicMaterials = [settings$6, settings$7, settings$8, settings$9, settings$a];
var enabledVeneerMaterials = [settings$b, settings$c];

var getMaterialSettings = function getMaterialSettings(materialId, itemType) {
  var m;

  switch (itemType) {
    case 0:
      m = enabledMaterials.find(function (o) {
        return o.material_id == materialId;
      });
      console.log('!!', materialId, itemType, m);
      break;

    case 1:
      m = enabledBasicMaterials.find(function (o) {
        return o.material_id == materialId;
      });
      break;

    case 2:
      m = enabledVeneerMaterials.find(function (o) {
        return o.material_id == materialId;
      });
      break;

    default:
      m = enabledMaterials.find(function (o) {
        return o.material_id == materialId;
      });
      break;
  }

  if (m == undefined) {
    console.warn("UNDEFINED MATERIAL, taking 0 as fallback", materialId, itemType, 'toooo');
    m = itemType == 1 ? enabledBasicMaterials.find(function (o) {
      return o.material_id == 0;
    }) : enabledMaterials.find(function (o) {
      return o.material_id == 0;
    });
  }

  return [m.material_id, m.color, m.opacity, m.hex_color, m.hex_handler_color, m.backs_color, m.reflectivityValue, m.reflectivityValueDoors];
};

var data = { flags:{ ao:false },
  shadows:null };

console.log(data);

var scenePresets = function getScenePresetsByColor(_ref) {
  var _ref$color = _ref.color;
  return data;
};

export { getMaterialSettings as getDefinitionForMaterial, getMaterialSettings as getDefinitionForMaterialOld, scenePresets as getSceneSettings };
