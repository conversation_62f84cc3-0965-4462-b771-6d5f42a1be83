import { black } from './colors/type01/black.js';
import { white } from './colors/type01/white.js';
import { grey } from './colors/type01/grey.js';
import { ash } from './colors/type01/ash.js';
import { maple } from './colors/type01/maple.js';
import { purple } from './colors/type01/purple.js';
import { red } from './colors/type01/red.js';
import { yellow } from './colors/type01/yellow.js';
import { dustyPink } from './colors/type01/dustyPink.js';

import { basicWhite } from './colors/type02/basic-white.js';
import { beige } from './colors/type02/beige.js';
import { indygo } from './colors/type02/indygo.js';
import { mint } from './colors/type02/mint.js';
import { orange } from './colors/type02/orange.js';
import { black_t2 } from './colors/type02/black_t2.js';
import { cotton } from './colors/type02/cotton';
import { sky_blue } from './colors/type02/sky_blue';
import { burgundy } from './colors/type02/burgundy';

import { type02June2022Release, type02September2022Release } from './colors/releases/type02';

import { ashVeneer } from './colors/type01Veneer/ashVeneer.js';
import { oakVeneer } from './colors/type01Veneer/oakVeneer.js';

const enabledMaterials = [white, black, maple, grey, purple, ash, red, yellow, dustyPink];
const enabledBasicMaterials = [basicWhite, beige, indygo, mint, orange, black_t2, cotton, sky_blue, burgundy,
    ...type02June2022Release(true),
    ...type02September2022Release(true),
];
const enabledVeneerMaterials = [ashVeneer, oakVeneer];

const getMaterialSettings = (materialId, itemType) => {
    let m;
    switch (itemType) {
    case 0:
        m = enabledMaterials.find(o => o.material_id == materialId);
        break;
    case 1:
        m = enabledBasicMaterials.find(o => o.material_id == materialId);
        break;
    case 2:
        m = enabledVeneerMaterials.find(o => o.material_id == materialId);
        break;
    default:
        m = enabledMaterials.find(o => o.material_id == materialId);
        break;
    }

    if (m == undefined) {
        console.warn('UNDEFINED MATERIAL, taking 0 as fallback', materialId, itemType, enabledMaterials);
        m = itemType == 1 ? enabledBasicMaterials.find(o => o.material_id == 0) : enabledMaterials.find(o => o.material_id == 0);
    }

    return [
        m.material_id,
        m.color,
        m.opacity,
        m.hex_color,
        m.hex_handler_color,
        m.backs_color,
        m.reflectivityValue,
        m.reflectivityValueDoors,
        m.reflectivityValueGrommet,
    ];
};

export {
    getMaterialSettings as getDefinitionForMaterialOld,
    getMaterialSettings as getDefinitionForMaterial,
};
