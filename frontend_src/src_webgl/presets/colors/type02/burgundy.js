// General 'White Color' material settings

const material_id = 8;
const color = 'burgundy';

const materialSettings = {

    material_id,
    color,

    hex_color: 0x6C2627,
    hex_handler_color: 0x6C2627,
    backs_color: 0x6C2627,
    opacity: 0.35,
    reflectivityValue: 0.18,
    reflectivityValueDoors: 0.14,
    reflectivityValueGrommet: 0.25,

};

const shadowSettings = {

    lightPoint: {
        x: 0,
        y: 0,
        z: 0,
    },

    liveShadowEnabled: false,
};

const settings = {

    id: material_id,

    ...materialSettings,
    ...shadowSettings,
};

export { settings as burgundy };
