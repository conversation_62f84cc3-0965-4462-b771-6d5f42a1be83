const DEV_MODE = false;

export const deprecatedRendererAPI = {
  wattyCanvasInteraction: {
    setDoorsAndDrawerDefaultState: () => {
      if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! setDoorsAndDrawerDefaultState in deprecated canvas interaction renderer API');
    }
  },

  clearHoverBoxes: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! clearHoverBoxes in deprecated renderer API');
  },

  updateMaterial: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! updateMaterial in deprecated renderer API');
  },

  updateComponentHoverBox: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! updateComponentHoverBox in deprecated renderer API');
  },

  updateHoverBoxWithActiveComponent: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! updateHoverBoxWithActiveComponent in deprecated renderer API');
  },

  setInteractionStates: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! setInteractionStates in deprecated renderer API');
  },

  setActiveInteractionState: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! setActiveInteractionState in deprecated renderer API');
  },

  setInactiveState: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! setInactiveState in deprecated renderer API');
  },

  handleDoorClose: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! handleDoorClose in deprecated renderer API');
  },

  handleDoorOpen: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! handleDoorOpen in deprecated renderer API');
  },

  getScreenshotForCart: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! getScreenshotForCart in deprecated renderer API');
  },

  setComponentView: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! setComponentView in deprecated renderer API');
  },

  setShelfView: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! setShelfView in deprecated renderer API');
  },

  getCoordinatesForButtons: () => {
    if (DEV_MODE) console.log('!!! NEW RENDER WARN !!! getCoordinatesForButtons in deprecated renderer API');
    return {
      mobile: [],
      desktop: [],
    };
  }
}

export const deprecatedOffscreenRendererAPI = {
  updateGeometry: () => {
    if (DEV_MODE) console.log('!!! NEW OFFSCREEN RENDER WARN !!! updateGeometry in deprecated offscreen renderer API');
  },
  getScreenshotForMobileDim: () => {
    if (DEV_MODE) console.log('!!! NEW OFFSCREEN RENDER WARN !!! getScreenshotForMobileDim in deprecated offscreen renderer API');
  },

  dimensions: {
    drawDimensions: () => {
      if (DEV_MODE) console.log('!!! NEW OFFSCREEN RENDER WARN !!! drawDimensions in deprecated dimensions offscreen renderer API');
    },
    removeDimensions: () => {
      if (DEV_MODE) console.log('!!! NEW OFFSCREEN RENDER WARN !!! removeDimensions in deprecated dimensions offscreen renderer API');
    }
  }
}

export const deprecatedDimensionsAPI = {
  drawDimensions: () => {
    if (DEV_MODE) console.log('!!! NEW DIMENSIONS WARN !!! drawDimensions in deprecated dimensions API');
  },
  removeDimensions: () => {
    if (DEV_MODE) console.log('!!! NEW DIMENSIONS WARN !!! removeDimensions in deprecated dimensions API');
  }
}
