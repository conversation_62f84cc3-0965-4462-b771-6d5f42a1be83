import {
    BufferGeometry, DoubleSide,
    Group,
    LineBasicMaterial,
    Material,
    Mesh,
    MeshBasicMaterial,
    MeshStandardMaterial, SkinnedMesh,
    Texture
} from "three";
import {Modifier} from "../common/modifiers";
import {flow} from "lodash-es";
import {RendererSettings} from "../common/config";
import {GLTF} from "three/examples/jsm/loaders/GLTFLoader";
import {GeometryAssets} from "./geometry";


export class AssetFactory {
    public static createNewGroup = (name: string, children: (Group | Mesh)[]): Group => {
        return flow(
            Modifier.name(name),
            Modifier.push(children)
        )(new Group()) as Group;
    }

    public static createNewMesh = (name: string, geometry: BufferGeometry, material: Material): Mesh => {
        return Modifier.name(name)(new Mesh(geometry, material)) as Mesh;
    }

    public static createNewBasicMaterial = (name: string): MeshBasicMaterial => {
        return flow(
            Modifier.setAlbedoMaterialProperties(RendererSettings.getColorsConfig().default, null),
            Modifier.setNameMaterialProperties(name)
        )(new MeshBasicMaterial()) as MeshBasicMaterial;
    }

    public static createNewLineMaterial = (name: string): LineBasicMaterial => {
        return flow(
            Modifier.setAlbedoMaterialProperties(RendererSettings.getColorsConfig().default, null),
            Modifier.setNameMaterialProperties(name)
        )(new LineBasicMaterial()) as LineBasicMaterial;
    }

    public static createNewPhysicalMaterial = (name: string): MeshStandardMaterial => {
        return flow(
            Modifier.setAlbedoMaterialProperties(RendererSettings.getColorsConfig().default, null),
            Modifier.setNameMaterialProperties(name)
        )(new MeshStandardMaterial()) as MeshStandardMaterial;
    }

    public static createNewTexture = (name: string): Texture => {
        return Modifier.nameTexture(name)(new Texture()) as Texture;
    }


    public static basicLine() {
        const group = new Group();
        const basicMaterial = new MeshBasicMaterial({
            color: 0x484444
        });
        const basicGeometry = GeometryAssets.Pool.getGeometryBox();
        const basicMesh = new Mesh(basicGeometry, basicMaterial);
        group.add(basicMesh)
        return group;
    }
    public static basicTextGeometry(textNumber: string, _cache: Group[]) {
        const group = new Group();
        const matDark = new LineBasicMaterial({
            color: 0xF9F9F9,
            side: DoubleSide,
        });
        const geometry = GeometryAssets.Pool.getShapeGeometryForDigits(textNumber.split('').map(Number));
        const text = new Mesh(geometry, matDark);
        group.add(text);
        _cache.push(group);
        return group;
    }
    public static basicPillGeometry() {
        const group = new Group();
        const material = new MeshBasicMaterial({
            color: 0x484444,
            side: DoubleSide,
            transparent: true,
            opacity: 0.7
        });
        const geometry = GeometryAssets.Pool.getPillShapeGeometry();
        const mesh = new Mesh(geometry, material);
        const size = .034;
        mesh.position.x = -size / 2;
        group.add(mesh);
        return group;
    }
    public static basicModel() {
        const basicGeometry = GeometryAssets.Pool.getGeometryBox();

        const basicMaterial = flow(
            Modifier.setAlbedoMaterialProperties(RendererSettings.getColorsConfig().hover, null),
            Modifier.setOpacityMaterialProperties(0, null),
            Modifier.setDepthMaterialProperties(false),
        )(AssetFactory.createNewBasicMaterial('hoverMaterial'));

        const basicMesh = AssetFactory.createNewMesh('boxGeometry', basicGeometry, basicMaterial);

        return AssetFactory.createNewGroup('basicBoundingBoxModel', [ basicMesh ]);
    }

    public static createMeshFromGLTF = (gltfFile: GLTF, name: string, unpacked: Group): void => {
        unpacked.userData.assetName = name;
        unpacked.userData.skeleton = false;
        unpacked.children = gltfFile.scene.children;
        unpacked.children.forEach((child) => {
            (child as Mesh).material = new MeshStandardMaterial({ color: 0xff0000 });
            ((child as Mesh).material as MeshStandardMaterial).map = null;
        })
    }

    public static createSkinnedMeshFromGLTF = (gltfFile: GLTF, name: string, unpacked: Group): void => {
        gltfFile.scene.children.forEach((object3D) => {
            object3D.children.forEach((obj3D) => {
                if (obj3D.name === "horizontal_sample") {
                    obj3D.children.forEach((mesh) =>{
                        if (mesh.name === "h_cube") mesh.name = "edge";
                        if (mesh.name === "h_cube_1") mesh.name = "top";
                        if (mesh.name === "h_cube_2") mesh.name = "bottom";
                        (mesh as SkinnedMesh).material = new MeshStandardMaterial({ color: 0xff0000 });
                        ((mesh as SkinnedMesh).material as MeshStandardMaterial).map = null;
                    })
                }
                if (obj3D.name === "vertical_sample") {
                    obj3D.children.forEach((mesh) =>{
                        if (mesh.name === "v_cube") mesh.name = "edge";
                        if (mesh.name === "v_cube_1") mesh.name = "top";
                        if (mesh.name === "v_cube_2") mesh.name = "bottom";
                        (mesh as SkinnedMesh).material = new MeshStandardMaterial({ color: 0xff0000 });
                        ((mesh as SkinnedMesh).material as MeshStandardMaterial).map = null;
                    })
                }
                if (obj3D.name === "front_sample") {
                    obj3D.children.forEach((mesh) =>{
                        if (mesh.name === "f_cube") mesh.name = "edge";
                        if (mesh.name === "f_cube_1") mesh.name = "top";
                        if (mesh.name === "f_cube_2") mesh.name = "bottom";
                        (mesh as SkinnedMesh).material = new MeshStandardMaterial({ color: 0xff0000 });
                        ((mesh as SkinnedMesh).material as MeshStandardMaterial).map = null;
                    })
                }
            })

            unpacked.userData.assetName = name;
            unpacked.userData.skeleton = true;
            unpacked.children = object3D.children;
            unpacked.children.forEach((child) => {
                (child as Mesh).material = new MeshStandardMaterial({ color: 0xff0000 });
                ((child as Mesh).material as MeshStandardMaterial).map = null;
            })
        })
    }
}