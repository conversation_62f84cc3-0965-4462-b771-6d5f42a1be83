import {GLT<PERSON>, GLTFLoader} from "three/examples/jsm/loaders/GLTFLoader.js";
import * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js'
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';
import {
    BoxGeometry,
    Group,
    Mesh,
    SkinnedMesh,
    Shape,
    ShapeGeometry,
    BufferGeometry,
} from "three";
import { mergeBufferGeometries } from "three/examples/jsm/utils/BufferGeometryUtils.js";
import {FONT_3D, ASSETS_SOURCE} from "../common/config"
import {AssetFactory} from "./factory";

const createPillShape = (size: number) => {
    const shape = new Shape();
    shape.absarc(0, 0, size, -Math.PI / 2, Math.PI / 2, true);
    shape.lineTo(size, size);
    shape.absarc(size, 0, size, Math.PI / 2, -Math.PI / 2, true);
    return new ShapeGeometry(shape);
}

export namespace GeometryAssets {
    export const identifier = {
        HORIZONTAL_PANEL: 'horizontalPanel',
        CHANNEL_SLAB: 'channelSlab',
        VERTICAL_PANEL: 'verticalPanel',
        FRONTAL_PANEL: 'frontalPanel',
        FRONT_WITH_GROOVED_HANDLE: 'frontWithGroovedHandle',
        FULL_HANDLE_TOP: 'fullHandleTop',
        FULL_HANDLE_RIGHT: 'fullHandleRight',
        FULL_HANDLE_LEFT: 'fullHandleLeft',
        GRIP_HANDLE: 'gripHandle',
        SLIM_HANDLE: 'slimHandle',
        HINGE_ANCHOR: 'hingeAnchor',
        HINGE_WING: 'hingeWing',
        SHORT_LEG: 'shortLeg',
        CROSS_WARDROBE_BAR: 'crossWardrobeBar',
        FRONT_WARDROBE_BAR: 'frontWardrobeBar',
        MASKING_PROFILE: 'maskingProfile',
        LIGHT_CHANNEL: 'lightBar',
        LIGHT_DIFFUSER: 'lightDiffuser',
        INNER_LIGHT_BOX: 'fakedLightRays',
        INNER_BOX_AO: 'ambientOcclusionBox',
        BG: 'background',
        SHADOW_MAN: 'shadowMan',
    }

    const _configs = [
        { identifier: identifier.HORIZONTAL_PANEL, fileName: `${ASSETS_SOURCE}/geometry/horizontal_sample.glb` },
        // Replace fileName in following entry to proper model
        { identifier: identifier.CHANNEL_SLAB, fileName: `${ASSETS_SOURCE}/geometry/horizontal_sample.glb` },
        { identifier: identifier.VERTICAL_PANEL, fileName: `${ASSETS_SOURCE}/geometry/vertical_sample.glb` },
        { identifier: identifier.FRONTAL_PANEL, fileName: `${ASSETS_SOURCE}/geometry/front_sample.glb` },
        { identifier: identifier.FRONT_WITH_GROOVED_HANDLE, fileName: `${ASSETS_SOURCE}/geometry/front_with_ grooved_handle.glb` },
        { identifier: identifier.FULL_HANDLE_TOP, fileName: `${ASSETS_SOURCE}/geometry/horizontal_handle_50.glb` },
        { identifier: identifier.FULL_HANDLE_LEFT, fileName: `${ASSETS_SOURCE}/geometry/vertical_handle_30.glb` },
        { identifier: identifier.FULL_HANDLE_RIGHT, fileName: `${ASSETS_SOURCE}/geometry/vertical_handle_50.glb` },
        { identifier: identifier.GRIP_HANDLE, fileName: `${ASSETS_SOURCE}/geometry/grip_handle.glb` },
        { identifier: identifier.SLIM_HANDLE, fileName: `${ASSETS_SOURCE}/geometry/slim_handle.glb` },
        { identifier: identifier.HINGE_WING, fileName: `${ASSETS_SOURCE}/geometry/hinge_wing.glb` },
        { identifier: identifier.HINGE_ANCHOR, fileName: `${ASSETS_SOURCE}/geometry/hinge_anchor.glb` },
        { identifier: identifier.SHORT_LEG, fileName: `${ASSETS_SOURCE}/geometry/short_leg.glb` },
        { identifier: identifier.CROSS_WARDROBE_BAR, fileName: `${ASSETS_SOURCE}/geometry/cross_wardrobe_bar.glb` },
        { identifier: identifier.FRONT_WARDROBE_BAR, fileName: `${ASSETS_SOURCE}/geometry/front_wardrobe_bar.glb` },
        { identifier: identifier.MASKING_PROFILE, fileName: `${ASSETS_SOURCE}/geometry/masking_profile.glb` },
        // Replace fileName in following entries to proper model
        { identifier: identifier.LIGHT_CHANNEL, fileName: `${ASSETS_SOURCE}/geometry/light_profile.glb` },
        { identifier: identifier.LIGHT_DIFFUSER, fileName: `${ASSETS_SOURCE}/geometry/light_diffuser.glb` },
        { identifier: identifier.INNER_LIGHT_BOX, fileName: `${ASSETS_SOURCE}/geometry/fake_light_rays.glb` },

        { identifier: identifier.INNER_BOX_AO, fileName: `${ASSETS_SOURCE}/geometry/innerBox_ao.glb` },
        { identifier: identifier.BG, fileName: `${ASSETS_SOURCE}/geometry/bg.glb` },
        { identifier: identifier.SHADOW_MAN, fileName: `${ASSETS_SOURCE}/geometry/shadow_man.glb` },
    ]

    const _gltfLoader = new GLTFLoader();
    const _fontLoader = new FontLoader();

    export namespace Pool {
        const _geometryBox = new BoxGeometry(1, 1, 1);
        const _pillShape = createPillShape(FONT_3D.SIZE);
        const _fontShapes: Map<string, Shape[]> = new Map();
        const _reusableAssets: Map<string, Group[]> = new Map();

        export const fetch = (): Promise<void> => {
            return new Promise((resolve) => {
                _fontLoader.load(`${ASSETS_SOURCE}/fonts/helvetiker_regular.typeface.json`, font => {
                    for (let index = 0; index < 10; index++) {
                        _fontShapes.set(index.toString(), font.generateShapes(index.toString(), FONT_3D.SIZE));
                    }
                });
                _configs.map((config, idx) => {
                    _gltfLoader.load(config.fileName, (gltf: GLTF) => {
                        _reusableAssets.set(config.identifier, [ unpackAssets(gltf, config.identifier) ]);
                        if (idx === _configs.length - 1) resolve();
                    });
                })
            })
        }

        export const fill = (poolSize: number, assetKey: string) => {
            for (let i = 1; i < poolSize; i++) {
                _reusableAssets.get(assetKey)!.push(_clone(assetKey));
            }
        }

        export const acquire = (assetKey: string): Group => {
            if (_reusableAssets.get(assetKey)!.length <= 1) fill(5, assetKey);
            return _reusableAssets.get(assetKey)!.pop() as Group;
        }

        export const release = (sample: Group, assetKey: string) => {
            _reusableAssets.get(assetKey)!.push(sample);
        }

        export const getGeometryBox = () => _geometryBox;
        export const getPillShapeGeometry = () => _pillShape;
        export const getShapesForDigit = (number: number) => _fontShapes.get(number.toString())!;
        export const getShapeGeometryForDigits = (digits: number[]) => processDigitsToShapes(digits);

        const _clone = (assetKey: string): Group => {
            const originAsset = _reusableAssets.get(assetKey)![0];
            const cloned = originAsset.userData.skeleton
                ? (SkeletonUtils as any).clone(originAsset)
                : originAsset.clone();

            cloned.traverse((child: any) => {
                if (child instanceof Mesh || child instanceof SkinnedMesh) {
                    child.material = child.material.clone();
                }
            })

            return cloned;
        }
    }
}

const processDigitsToShapes = (digits: number[]): BufferGeometry => {
    const oneDigitShapeSize = 0.5;
    const twoDigitShapeSize = 0.25;
    const threeDigitShapeSize = 0.175;
    const spacing = 0.01;

    const calculateOffset = (order: number, size: number) => {
        if (digits.length === 2 && order === 0) return (-size * twoDigitShapeSize) - size/4;
        if (digits.length === 2 && order === 1) return (-size * twoDigitShapeSize) + size/4;
        if (digits.length === 3 && order === 0) return (-size * threeDigitShapeSize) - size/3;
        if (digits.length === 3 && order === 1) return (-size * threeDigitShapeSize);
        if (digits.length === 3 && order === 2) return (-size * threeDigitShapeSize) + size/3;
        return -size * oneDigitShapeSize;
    }

    const shapes = digits.map((digit: number, idx: number) => {
        const shapes: Shape[] = GeometryAssets.Pool.getShapesForDigit(digit);
        const geometry = new ShapeGeometry(shapes);
        geometry.computeBoundingBox();
        return geometry;
    })

    const shapesSize = shapes.map((shape) => shape!.boundingBox!.max.x - shape!.boundingBox!.min.x);
    const fullSize = shapesSize.reduce((acc, curr) => acc + curr, 0) + (shapesSize.length - 1) * spacing;

    shapes.forEach((shape, idx) => {
        const offset = calculateOffset(idx, fullSize);
        shape.translate(offset, -FONT_3D.SIZE/2, 0.006);
    });

    return mergeBufferGeometries(shapes);
}

const unpackAssets = (asset: GLTF, assetName: string): Group => {
    let unpacked = new Group();

    if (assetName === GeometryAssets.identifier.SHORT_LEG
        || assetName === GeometryAssets.identifier.CROSS_WARDROBE_BAR
        || assetName === GeometryAssets.identifier.FRONT_WARDROBE_BAR
        || assetName === GeometryAssets.identifier.MASKING_PROFILE
        || assetName === GeometryAssets.identifier.GRIP_HANDLE
        || assetName === GeometryAssets.identifier.SLIM_HANDLE
        || assetName === GeometryAssets.identifier.LIGHT_CHANNEL
        || assetName === GeometryAssets.identifier.LIGHT_DIFFUSER
        || assetName === GeometryAssets.identifier.INNER_LIGHT_BOX
        || assetName === GeometryAssets.identifier.BG
        || assetName === GeometryAssets.identifier.INNER_BOX_AO
        || assetName === GeometryAssets.identifier.SHADOW_MAN
        || assetName === GeometryAssets.identifier.HINGE_ANCHOR
        || assetName === GeometryAssets.identifier.HINGE_WING) {
        AssetFactory.createMeshFromGLTF(asset, assetName, unpacked);
    } else {
        AssetFactory.createSkinnedMeshFromGLTF(asset, assetName, unpacked);
    }

    return unpacked;
}
