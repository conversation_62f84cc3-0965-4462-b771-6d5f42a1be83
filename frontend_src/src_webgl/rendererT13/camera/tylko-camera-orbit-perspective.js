import * as THREE from 'three';
import { throttle } from 'lodash-es';


/**
 * @type Class
 */
 export const OrbitControls2 = function(object, domElement) {
    this.object = object;
    this.domElement = (domElement !== undefined) ? domElement : document;
    // this.autoZoomObj = new tylkoCameraAutoZoom(scene);
    this.points = [
        new THREE.Vector3(-1, -1, -1),
        new THREE.Vector3(0, 0, 0),
        new THREE.Vector3(0, 0, 0),
        new THREE.Vector3(0, 0, 0),
        new THREE.Vector3(0, 0, 0),
        new THREE.Vector3(0, 0, 0),
        new THREE.Vector3(0, 0, 0),
        new THREE.Vector3(1, 1, 1),
    ];

    this.enabled = true;
    this.noZoom = false;
    this.noAutoZoom = true;
    this.noLifeZoom = true;
    this.noTransitionAnimation = true;
    this.noRotate = false;
    this.noPan = false;
    this.directionLock = false;
    this.noYPan = false;
    this.noSnap = true;
    this.noSnapReal = this.noSnap; // faktyczna kontrola snappingu tymczasowo nadpisywana przy ustawianiu widoku

    this.target = new THREE.Vector3();
    this.snapTo = [
        { theta: 0.62, phi: 1.1, x: 0 },
        { theta: -0.4, phi: 1.4, x: 0 },
        { theta: 0, phi: Math.PI / 2, x: 0 },
    ];

    this.zoomSpeed = 1.0;
    this.rotateSpeed = 2.0;
    this.animationStepParameters = {
        distance: .040, // "cm" - skala jak w scenie
        targetDistance: .012, // "cm" - skala jak w scenie
        angle: 0.02, // rad
    };

    this.zoomRange = { min: 0, max: Infinity };
    this.polarAngle = { min: 0, max: Math.PI };
    this.azimuthAngle = { min: -Infinity, max: Infinity };

    this.mouseButtons = { ORBIT: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.RIGHT };
    this.touchMode = {
        ORBIT: 1, ZOOM: 2, PAN: 3, ZOOMPAN: 4,
    };

    // interfejs do narzucania katow obrotu
    this.new_theta = null; // AzimuthalAngle
    this.new_phi = null;
    this.new_target = null;

    // okresla czy geometria jest w trakcie zmiany - do scalenia z `state` ?
    this.geometryFixed = true;

    this.screenEdgeOffset = { // Offset krawedzi w pixelach
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    };

    this.throttledTrack = throttle(() => {
        // PubSub.publish('atupaleLayer_configurator', { method: 'cameraInteraction', payload: { eventLabel: 'center' } });
    }, 5000);

    // //////////
    // internals

    const that = this;
    const EasingFunctions = {
        linear(t) { return t },
        easeInQuad(t) { return t * t },
        easeOutQuad(t) { return t * (2 - t) },
        easeInOutQuad(t) { return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t },
        easeInCubic(t) { return t * t * t },
        easeOutCubic(t) { return (--t) * t * t + 1 },
        easeInOutCubic(t) { return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1 },
        easeInQuart(t) { return t * t * t * t },
        easeOutQuart(t) { return 1 - (--t) * t * t * t },
        easeInOutQuart(t) { return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t },
        easeInQuint(t) { return t * t * t * t * t },
        easeOutQuint(t) { return 1 + (--t) * t * t * t * t },
        easeInOutQuint(t) { return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t },
    };

    const EPS = 0.000001;

    const rotateStart = new THREE.Vector2();
    const rotateEnd = new THREE.Vector2();
    let rotateDelta = new THREE.Vector2();

    const panStart = new THREE.Vector2();
    const panEnd = new THREE.Vector2();
    let panDelta = new THREE.Vector2();
    const panOffset = new THREE.Vector3();

    const offset = new THREE.Vector3();

    const dollyStart = new THREE.Vector2();
    const dollyEnd = new THREE.Vector2();
    const dollyDelta = new THREE.Vector2();

    let snapped;

    let zoom;
    let properZoom;
    let theta; // AzimuthalAngle
    let phi; // PolarAngle

    let newZoom;
    let newTheta;
    let newPhi;
    let newTarget;

    let thetaDelta = 0;
    let phiDelta = 0;

    let scale = 1;
    const pan = new THREE.Vector3();
    const tempPoint = new THREE.Vector3();

    let animationParameters = {
        counter: 0,
        thetaDiff: 0,
        phiDiff: 0,
        zoomDiff: 0,
        theta: 0,
        phi: 0,
        zoom: 0,
    };

    const STATE = {
        NONE: -1, ROTATE: 0, DOLLY: 1, PAN: 2, TOUCH_ROTATE: 3, TOUCH_DOLLY: 4, TOUCH_PAN: 5, TOUCH_ZOOMPAN: 6, ANIMATE: 7,
    };
    let state = STATE.NONE;
    let animationEndEvent = false; // TODO: Do zmiany na cos ludzkiego, np. zintegrowania z STATE

    // so camera.up is the orbit axis
    const quat = new THREE.Quaternion().setFromUnitVectors(object.up, new THREE.Vector3(0, 1, 0));
    const quatInverse = quat.clone().invert();

    // events
    // var startEvent = { type: 'start' };
    // var endEvent = { type: 'end' };
    const changeEvent = { type: 'change' };
    const animateEvent = { type: 'render' };
    const animateEndEvent = { type: 'animationEnd' };


    this.getCameraZoom = () => properZoom;
    this.getState = () => state;

    // pass in distance in world space to move left
    this.panLeft = function(distance) {
        const te = this.object.matrix.elements;
        // get X column of matrix
        panOffset.set(te[0], te[1], te[2]);
        panOffset.multiplyScalar(-distance);
        pan.add(panOffset);
    };

    // pass in distance in world space to move up
    this.panUp = function(distance) {
        const te = this.object.matrix.elements;
        // get Y column of matrix
        panOffset.set(te[4], te[5], te[6]);
        panOffset.multiplyScalar(distance);
        pan.add(panOffset);
    };

    // pass in x,y of change desired in pixel space,
    // right and down are positive
    this.pan = function(deltaX, deltaY) {
        const element = that.domElement === document ? that.domElement.body : that.domElement;

        const { position } = that.object;
        const offset = position.clone().sub(that.target);
        let targetDistance = offset.length();
        // half of the fov is center to top of screen
        targetDistance *= Math.tan((that.object.fov / 2) * Math.PI / 180.0);
        // we actually don't use screenWidth, since perspective camera is fixed to screen height
        that.panLeft(2 * deltaX * targetDistance / element.clientHeight);
        if (!that.noYPan) that.panUp(2 * deltaY * targetDistance / element.clientHeight);
    };

    this.dollyIn = function(dollyScale) {
        if (dollyScale === undefined) dollyScale = getZoomScale();
        scale /= dollyScale;
    };

    this.dollyOut = function(dollyScale) {
        if (dollyScale === undefined) dollyScale = getZoomScale();
        scale *= dollyScale;
    };

    this.updateCameraRange = function() {
        let dist = 0;
        that.points.forEach(p => {
            dist = Math.max(that.target.distanceTo(p), dist)
        });

        const distTargetToCam = that.object.position.distanceTo(that.target);
        this.object.near = 0.1; // Math.max(distTargetToCam - dist, 0.1);
        this.object.far = 50000; // Math.max(distTargetToCam + dist, 1000);
        this.object.updateProjectionMatrix()
    };

    function checkRange() {
        let distT = 100000;
        let distC = 100000;
        that.points.forEach(p => {
            distT = Math.min(that.target.distanceTo(p), distT);
            distC = Math.min(that.object.position.distanceTo(p), distC)
        });
    }

    function calculateAcutalCamParams() {
        offset.copy(that.object.position).sub(that.target);
        offset.applyQuaternion(quat); // rotate offset to "y-axis-is-up" space
        theta = Math.atan2(offset.x, offset.z);
        phi = Math.atan2(Math.sqrt(offset.x * offset.x + offset.z * offset.z), offset.y);

        snapped = false;
        zoom = offset.length();
        newZoom = properZoom || zoom;
        newTheta = theta;
        newPhi = phi;
        newTarget = that.new_target || that.target;
    }

    function calculateStaticCamParams() {
        const snap = getClosestSnap(phi, theta, that.target.x, that.snapTo)
        if (!that.noSnapReal && checkCamDiff(snap.theta, snap.phi, undefined, snap.x)) {
            newTheta = snap.theta;
            newPhi = snap.phi;
            if (snap.x) {
                newTarget = that.target.clone();
                newTarget.x = snap.x
            }
            snapped = true
        } else if (checkCamDiff(that.new_theta, that.new_phi)) {
            newTheta = that.new_theta;
            newPhi = that.new_phi
        }
        if (that.new_target !== null) newTarget = that.new_target;
    }

    this.updateZoom = function() {
        calculateUpfrontZoom()
    };

    this.forceUpdate = function() {
        state = STATE.NONE
        this.update()
    }

    function calculateRealZoom() {
        const pixelWidth = 2 / that.domElement.clientWidth;
        const pixelHeight = 2 / that.domElement.clientHeight;

        let distX = [];
        let distY = [];

        that.points.forEach(p => {
            tempPoint.copy(p).project(that.object);
            distX.push(tempPoint.x + (tempPoint.x < 0 ? -pixelWidth * that.screenEdgeOffset.left : pixelWidth * that.screenEdgeOffset.right))
            distY.push(tempPoint.y + (tempPoint.y < 0 ? -pixelHeight * that.screenEdgeOffset.bottom : pixelHeight * that.screenEdgeOffset.top))
        });
        distX = distX.filter(isFinite)
        distY = distY.filter(isFinite)
        let distTargetToCam = that.object.position.distanceTo(that.target)
        const max = Math.max(...distX, ...distY, Math.abs(Math.min(...distX, ...distY)))
        distTargetToCam *= max
        newZoom = distTargetToCam || zoom
    }

    function calculateUpfrontZoom() {
        function getMaxFinite(array) {
            return Math.max(...array.filter(isFinite))
        }
        const clientWitdh = that.domElement ? that.domElement.clientWidth : 800;
        const clientHeight = that.domElement ? that.domElement.clientHeight : 600;
        const target = that.new_target === null ? that.target : that.new_target;
        const fovLeft = that.object.fov * that.object.aspect * (1 - that.screenEdgeOffset.left / clientWitdh);
        const fovRight = that.object.fov * that.object.aspect * (1 - that.screenEdgeOffset.right / clientWitdh);
        const fovTop = that.object.fov * (1 - that.screenEdgeOffset.top / clientHeight);
        const fovBottom = that.object.fov * (1 - that.screenEdgeOffset.bottom / clientHeight);

        const [distXleft, distXright, distYtop, distYbottom, distZ] = [[], [], [], [], []];

        that.points.forEach(p => {
            if (p.x < target.x) {
                distXleft.push(Math.abs(p.x - target.x))
            } else {
                distXright.push(Math.abs(p.x - target.x))
            }

            if (p.y - target.y) {
                distYbottom.push(Math.abs(p.y - target.y))
            } else {
                distYtop.push(Math.abs(p.y - target.y))
            }

            if (p.z > target.z) distZ.push(Math.abs(p.z - target.z));
        });

        const fitLeftWidthDistance = getMaxFinite(distXleft) / (2 * Math.atan(Math.PI * fovLeft / 360));
        const fitRightWidthDistance = getMaxFinite(distXright) / (2 * Math.atan(Math.PI * fovRight / 360));
        const fitTopHeightDistance = getMaxFinite(distYtop) / (2 * Math.atan(Math.PI * fovTop / 360));
        const fitBottomHeightDistance = getMaxFinite(distYbottom) / (2 * Math.atan(Math.PI * fovBottom / 360));
        properZoom = getMaxFinite(distZ) + 2 * Math.max(
            fitLeftWidthDistance, fitRightWidthDistance, fitTopHeightDistance, fitBottomHeightDistance,
        );
    }

    function restrictCamParams() {
        // restrict theta to be between desired limits
        // newTheta = Math.max(that.azimuthAngle.min, Math.min(that.azimuthAngle.max, newTheta));
        // restrict phi to be between desired limits
        newPhi = Math.max(that.polarAngle.min, Math.min(that.polarAngle.max, newPhi));
        // restrict newZoom to be between desired limits
        newZoom = Math.max(that.zoomRange.min, Math.min(that.zoomRange.max, newZoom));
    }

    function checkCamDiff(testTheta, testPhi, testZoom, testTargetX, testTargetY) {
        const t = testTheta !== undefined ? Math.abs(theta - testTheta) > that.animationStepParameters.angle : false;
        const p = testPhi !== undefined ? Math.abs(phi - testPhi) > that.animationStepParameters.angle : false;
        const z = testZoom !== undefined ? Math.abs(zoom - testZoom) > .1 : false;
        const tx = testTargetX !== undefined ? Math.abs(that.target.x - testTargetX) > .1 : false;
        const ty = testTargetY !== undefined ? Math.abs(that.target.y - testTargetY) > .1 : false;
        return t || p || z || tx || ty
    }

    function calculateAnimationFirstStep() {
        // Obliczam parametry
        const animationSpeed = Math.round(
            Math.min(
                Math.max(
                    Math.abs(newTheta - theta) / that.animationStepParameters.angle,
                    Math.abs(newPhi - phi) / that.animationStepParameters.angle,
                    Math.abs(newZoom - zoom) / that.animationStepParameters.distance,
                    Math.abs(newTarget.x - that.target.x) / that.animationStepParameters.targetDistance,
                    Math.abs(newTarget.y - that.target.y) / that.animationStepParameters.targetDistance,
                    5, // Nie mniej niż 5 klatek
                ),
                35, // Nie więcej niż 40 klatek
            ),
        )
        state = STATE.ANIMATE;
        animationParameters = {
            counter: 1,
            total: animationSpeed,
            thetaDiff: (newTheta - theta),
            phiDiff: (newPhi - phi),
            zoomDiff: (newZoom - zoom),
            targetDiff: {
                x: (newTarget.x - that.target.x),
                y: (newTarget.y - that.target.y),
            },
            theta: newTheta,
            phi: newPhi,
            zoom: newZoom,
            target: newTarget.clone(),
            thetaStart: theta,
            phiStart: phi,
            zoomStart: zoom,
            targetStart: that.target.clone(),
        };
        // Obliczam pierwszy krok
        calculateAnimationNextStep()
    }


    function calculateDynamicCamParams() {
        newTheta = Math.atan2(offset.x, offset.z) + thetaDelta;
        newPhi = Math.atan2(Math.sqrt(offset.x * offset.x + offset.z * offset.z), offset.y) + phiDelta;
        newZoom = zoom * scale;
        newTarget = that.target.clone();
        newTarget.add(pan);
    }

    function calculateAnimationNextStep() {
        if (animationParameters.counter === animationParameters.total) {
            newTheta = animationParameters.theta;
            newPhi = animationParameters.phi;
            newZoom = animationParameters.zoom;
            newTarget = animationParameters.target;
            state = STATE.NONE;
            animationEndEvent = true;
        } else {
            let current = animationParameters.counter / animationParameters.total;
            current = EasingFunctions.easeInOutCubic(current);
            newTheta = animationParameters.thetaStart + animationParameters.thetaDiff * current;
            newPhi = animationParameters.phiStart + animationParameters.phiDiff * current;
            newZoom = animationParameters.zoomStart + animationParameters.zoomDiff * current;
            newTarget.x = animationParameters.targetStart.x + animationParameters.targetDiff.x * current;
            newTarget.y = animationParameters.targetStart.y + animationParameters.targetDiff.y * current;
            animationParameters.counter += 1
        }
    }

    function setCamParams() {
        // ---------- Ustawiam kamerę ------------------------------------------------
        // Ograniczam phi zgodnie z parametrem EPS
        newPhi = Math.max(EPS, Math.min(Math.PI - EPS, newPhi));
        // Kalkuluje poprawny offset
        offset.x = newZoom * Math.sin(newPhi) * Math.sin(newTheta);
        offset.y = newZoom * Math.cos(newPhi);
        offset.z = newZoom * Math.sin(newPhi) * Math.cos(newTheta);
        offset.applyQuaternion(quatInverse); // rotate offset back to "camera-up-vector-is-up" space
        // Move target to panned location
        that.target = newTarget;
        // Set cam params
        that.object.position.copy(that.target).add(offset);
        // that.object.near = Math.max(1, that.object.near - (zoom - newZoom));
        // that.object.far = that.object.far - (zoom - newZoom);
        that.object.lookAt(that.target);

        // Reset values
        thetaDelta = 0;
        phiDelta = 0;
        that.new_theta = newTheta;
        that.new_phi = newPhi;
        that.new_target = null;
        scale = 1;
        pan.set(0, 0, 0);
    }

    this.resetAnimation = function() {
        state = STATE.NONE;
    };

    this.update = function() {
        // Brak zmian, jesli geometria jest w trakcie zmian oraz jest wylaczony AutoZoom
        if (!this.geometryFixed && this.noLifeZoom) return;
        calculateAcutalCamParams(); // Uaktualniam stan zmiennych offset, theta, phi
        switch (state) {
        case STATE.NONE:
            calculateStaticCamParams();
            if (
                !snapped
					&&					(
					    !this.noAutoZoom
						|| (!this.noLifeZoom && this.geometryFixed === true)
						|| (!this.noLifeZoom && state === STATE.NONE)
					)
            ) {
                calculateRealZoom();
            }
            if (!this.noTransitionAnimation && checkCamDiff(newTheta, newPhi, newZoom, newTarget.x, newTarget.y)) {
                calculateAnimationFirstStep()
            } else {
                animationEndEvent = true;
            }

            break;

        case STATE.ANIMATE:
            calculateAnimationNextStep();
            break;

        default: // W trakcie pracy z kamera przez usera
            calculateDynamicCamParams();
            restrictCamParams();
            if (!this.noAutoZoom && !this.noLifeZoom) calculateRealZoom();
        }
        setCamParams();
        switch (state) {
        case STATE.ANIMATE:
            that.dispatchEvent(animateEvent);
            break;
        default:
            if (animationEndEvent === true) {
                animationEndEvent = false;
                that.dispatchEvent(animateEndEvent)
            }
            that.dispatchEvent(changeEvent)
        }
    };

    function getZoomScale() {
        return Math.pow(0.95, that.zoomSpeed);
    }

    function getClosestSnap(currentPhi, currentTheta, currentX, availbleSnappingStates) {
        let closest; let diff; let
            optionDiff;
        ([closest, ...availbleSnappingStates] = availbleSnappingStates);
        diff = Math.abs(closest.theta - currentTheta) + (closest.x ? Math.abs(closest.x - currentX) : 0);
        availbleSnappingStates.forEach(option => {
            optionDiff = Math.abs(option.theta - currentTheta) + (option.x ? Math.abs(option.x - currentX) : 0);
            if (diff > optionDiff) {
                closest = option;
                diff = optionDiff;
            }
        });
        return closest
    }

    function onMouseDown(event) {
        if (that.enabled === false) return;
        event.preventDefault();

        if (event.button === that.mouseButtons.ORBIT) {
            if (that.noRotate === true) return;

            state = STATE.ROTATE;
            rotateStart.set(event.clientX, event.clientY);
        } else if (event.button === that.mouseButtons.ZOOM) {
            if (that.noZoom === true) return;

            state = STATE.DOLLY;
            dollyStart.set(event.clientX, event.clientY);
        } else if (event.button === that.mouseButtons.PAN) {
            if (that.noPan === true) return;

            state = STATE.PAN;
            panStart.set(event.clientX, event.clientY);
        }
        if (state !== STATE.NONE) {
            document.addEventListener('mousemove', onMouseMove, false);
            document.addEventListener('mouseup', onMouseUp, false);
            // that.dispatchEvent(startEvent);
        }
    }

    function onMouseMove(event) {
        if (that.enabled === false) return;
        event.preventDefault();

        const element = that.domElement === document ? that.domElement.body : that.domElement;

        if (state === STATE.ROTATE) {
            if (that.noRotate === true) return;

            rotateEnd.set(event.clientX, event.clientY);
            rotateDelta.subVectors(rotateEnd, rotateStart);

            // rotating across whole screen goes 360 degrees around
            thetaDelta -= 2 * Math.PI * rotateDelta.x / element.clientWidth * that.rotateSpeed;

            // rotating up and down along whole screen attempts to go 360, but limited to 180
            phiDelta -= 2 * Math.PI * rotateDelta.y / element.clientHeight * that.rotateSpeed;

            rotateStart.copy(rotateEnd);

            if (that.new_theta >= -0.05 && that.new_theta <= 0.05) {
                setTimeout(() => { that.throttledTrack(); }, 4000);
            }
        } else if (state === STATE.DOLLY) {
            if (that.noZoom === true) return;

            dollyEnd.set(event.clientX, event.clientY);
            dollyDelta.subVectors(dollyEnd, dollyStart);

            if (dollyDelta.y > 0) {
                that.dollyIn();
            } else if (dollyDelta.y < 0) {
                that.dollyOut();
            }

            dollyStart.copy(dollyEnd);
        } else if (state === STATE.PAN) {
            if (that.noPan === true) return;

            panEnd.set(event.clientX, event.clientY);
            panDelta.subVectors(panEnd, panStart);

            that.pan(panDelta.x, panDelta.y);

            panStart.copy(panEnd);
        }
        if (state !== STATE.NONE) {
            that.update();
            that.noSnapReal = that.noSnap
        }
    }

    function onMouseUp(/* event */) {
        if (that.enabled === false) return;

        document.removeEventListener('mousemove', onMouseMove, false);
        document.removeEventListener('mouseup', onMouseUp, false);

        // if (that.new_phi === that.polarAngle.min) {
        //     PubSub.publish('atupaleLayer_configurator', { method: 'cameraInteraction', payload: { eventLabel: 'top' } });
        // }
        // if (that.new_theta === that.azimuthAngle.min) {
        //     PubSub.publish('atupaleLayer_configurator', { method: 'cameraInteraction', payload: { eventLabel: 'left' } });
        // }
        // if (that.new_theta === that.azimuthAngle.max) {
        //     PubSub.publish('atupaleLayer_configurator', { method: 'cameraInteraction', payload: { eventLabel: 'right' } });
        // }

        state = STATE.NONE;
        that.update()
    }

    function onMouseWheel(event) {
        if (that.enabled === false || that.noZoom === true || state !== STATE.NONE) return;

        event.preventDefault();
        event.stopPropagation();
        let delta = 0;

        if (event.wheelDelta !== undefined) { // WebKit / Opera / Explorer 9
            delta = event.wheelDelta;
        } else if (event.detail !== undefined) { // Firefox
            delta = -event.detail;
        }
        if (delta > 0) {
            that.dollyOut();
        } else if (delta < 0) {
            that.dollyIn();
        }
        that.noSnapReal = that.noSnap;
        that.update();
        // that.dispatchEvent(startEvent);
        // that.dispatchEvent(endEvent);
    }

    function touchstart(event) {
        if (that.enabled === false) return;

        switch (event.touches.length) {
        case that.touchMode.ORBIT:	// one-fingered touch: rotate

            if (that.noRotate === true) return;

            state = STATE.TOUCH_ROTATE;

            rotateStart.set(event.touches[0].pageX, event.touches[0].pageY);
            break;

        case that.touchMode.ZOOM:	// two-fingered touch: dolly

            if (that.noZoom === true) return;

            state = STATE.TOUCH_DOLLY;

            var dx = event.touches[0].pageX - event.touches[1].pageX;
            var dy = event.touches[0].pageY - event.touches[1].pageY;
            var distance = Math.sqrt(dx * dx + dy * dy);
            dollyStart.set(0, distance);
            break;

        case that.touchMode.PAN: // three-fingered touch: pan

            if (that.noPan === true) return;

            state = STATE.TOUCH_PAN;

            panStart.set(event.touches[0].pageX, event.touches[0].pageY);
            break;

        case that.touchMode.ZOOMPAN: // three-fingered touch: pan
            state = STATE.TOUCH_ZOOMPAN;
            that.directionLock = true;

            panStart.set(event.touches[0].pageX, event.touches[0].pageY);
            break;

        default:

            state = STATE.NONE;
        }

        // if (state !== STATE.NONE) that.dispatchEvent(startEvent);
    }

    function lockDirection(delta) {
        switch (that.directionLock) {
        case false:
            return delta;
        case 'horizontal':
            delta.setY(0);
            return delta;
        case 'vertrical':
            delta.setX(0);
            return delta;
        default:
            that.directionLock = Math.abs(delta.x) > Math.abs(delta.y) ? 'horizontal' : 'vertrical';
            return lockDirection(delta)
        }
    }

    function touchmove(event) {
        if (that.enabled === false) return;

        event.preventDefault();
        //	event.stopPropagation();

        const element = that.domElement === document ? that.domElement.body : that.domElement;

        switch (event.touches.length) {
        case that.touchMode.ORBIT: // one-fingered touch: rotate

            if (that.noRotate === true) return;
            if (state !== STATE.TOUCH_ROTATE) return;

            rotateEnd.set(event.touches[0].pageX, event.touches[0].pageY);
            rotateDelta.subVectors(rotateEnd, rotateStart);
            rotateDelta = lockDirection(rotateDelta)

            // rotating across whole screen goes 360 degrees around
            thetaDelta -= 2 * Math.PI * rotateDelta.x / element.clientWidth * that.rotateSpeed;
            // rotating up and down along whole screen attempts to go 360, but limited to 180
            phiDelta -= 2 * Math.PI * rotateDelta.y / element.clientHeight * that.rotateSpeed;

            rotateStart.copy(rotateEnd);

            that.noSnapReal = that.noSnap;
            that.update();
            break;

        case that.touchMode.ZOOM: // two-fingered touch: dolly

            if (that.noZoom === true) return;
            if (state !== STATE.TOUCH_DOLLY) return;

            var dx = event.touches[0].pageX - event.touches[1].pageX;
            var dy = event.touches[0].pageY - event.touches[1].pageY;
            var distance = Math.sqrt(dx * dx + dy * dy);

            dollyEnd.set(0, distance);
            dollyDelta.subVectors(dollyEnd, dollyStart);

            if (dollyDelta.y > 0) {
                that.dollyOut();
            } else if (dollyDelta.y < 0) {
                that.dollyIn();
            }

            dollyStart.copy(dollyEnd);

            that.noSnapReal = that.noSnap;
            that.update();
            break;

        case that.touchMode.PAN: // three-fingered touch: pan
            if (that.noPan === true) return;
            if (state !== STATE.TOUCH_PAN) return;

            panEnd.set(event.touches[0].pageX, event.touches[0].pageY);
            panDelta.subVectors(panEnd, panStart);
            panDelta = lockDirection(panDelta);

            that.pan(panDelta.x, panDelta.y);
            panStart.copy(panEnd);

            that.noSnapReal = that.noSnap;
            that.update();
            break;

        case that.touchMode.ZOOMPAN:
            if (that.noPan === true) return;
            if (state !== STATE.TOUCH_ZOOMPAN) return;
            panEnd.set(event.touches[0].pageX, event.touches[0].pageY);
            panDelta.subVectors(panEnd, panStart);
            panDelta = lockDirection(panDelta);
            switch (that.directionLock) {
            case false:
            case 'horizontal':
                that.pan(panDelta.x, panDelta.y);
                panStart.copy(panEnd);

                that.noSnapReal = that.noSnap;
                that.update();
                break;
            case 'vertrical':
                // rotating across whole screen goes 360 degrees around
                thetaDelta -= 2 * Math.PI * panDelta.x / element.clientWidth * that.rotateSpeed;
                // rotating up and down along whole screen attempts to go 360, but limited to 180
                phiDelta -= 2 * Math.PI * panDelta.y / element.clientHeight * that.rotateSpeed;

                panStart.copy(panEnd);

                that.noSnapReal = that.noSnap;
                that.update();
                break;
            default:
                state = STATE.NONE;
                break;
            }

            break;

        default:
            state = STATE.NONE;
        }
    }

    function touchend(/* event */) {
        if (that.enabled === false) return;
        // that.dispatchEvent(endEvent);
        if (that.directionLock !== false) that.directionLock = true;
        state = STATE.NONE;
        that.update();
    }
    if (this.domElement) {
        this.domElement.addEventListener('contextmenu', event => {
            event.preventDefault();
        }, false);
        this.domElement.addEventListener('mousedown', onMouseDown, false);
        this.domElement.addEventListener('mousewheel', onMouseWheel, false);
        this.domElement.addEventListener('DOMMouseScroll', onMouseWheel, false); // firefox

        this.domElement.addEventListener('touchstart', touchstart, false);
        this.domElement.addEventListener('touchend', touchend, false);
        this.domElement.addEventListener('touchmove', touchmove, false);
    }

    // force an update at start
    // this.update();
};

OrbitControls2.prototype = Object.create(THREE.EventDispatcher.prototype);
OrbitControls2.prototype.constructor = OrbitControls2;
