import { TylkoCamera } from "./tylko-camera.js";
import { customSettings, defaultSettings } from "./settings.js";
import { CanvasContainer } from '../common/models/utils';
import { SpaceRenderFormat } from '../common/models/formats';

export type CameraSettings = 'wardrobe' | 'wardrobeCart' | 'wardrobeSave' | 'wardrobeDimensions' | 'grid'
  | 'wardrobe_feed_left_30' | 'wardrobe_feed_front' | 'wardrobe_feed_right_30' | 'row';

export namespace Camera {
  let _cameraWrapper: TylkoCamera;
  let _viewMode: 'shelf' | 'component' = 'shelf';
  const _boxOffset = { x: 1.1, y: 0, z: 0 };

  export const getInstance = () => _cameraWrapper.camera;
  export const getControls = () => _cameraWrapper.controls as any;

  export const setup = (container: CanvasContainer, instantZoom: boolean, preset: CameraSettings) => {
    if (_cameraWrapper) return;
    const settings = buildCameraSettings(container, preset);

    _cameraWrapper = new TylkoCamera({
      view: instantZoom ? false : container.domElement,
      settings,
    });

    _cameraWrapper.noTransitionAnimation = instantZoom;

    updateAspectRatio(container.width, container.height);
    _cameraWrapper.controls.update();
  }

  export const updateContols = () => {
    _cameraWrapper.controls.update();
  }

  export const updateCameraSettings = (container: CanvasContainer, cameraSettings: string) => {
    _cameraWrapper.updateSettings(buildCameraSettings(container, cameraSettings))
    _cameraWrapper.camera.updateProjectionMatrix();
    updateContols();
  }

  export const focusShelf = (spaceRenderFormat: SpaceRenderFormat, instantZoom: boolean = false) => {
    if (!spaceRenderFormat) {
      return;
    }

    const shelfRenderFormat = spaceRenderFormat.shelves[0];

    const boxSize = {
        x: shelfRenderFormat.size.x + _boxOffset.x,
        y: shelfRenderFormat.size.y + _boxOffset.y,
        z: shelfRenderFormat.size.z + _boxOffset.z,
    }

    _cameraWrapper.updateGeometry(
      {
        x: -boxSize.x / 2,
        y: 0,
        z: -boxSize.z / 2,
      },
      {
        x: boxSize.x / 2,
        y: boxSize.y,
        z: boxSize.z / 2,
      },
      instantZoom,
    );
  }

  export const updateAspectRatio = (width: number, height: number) => {
    _cameraWrapper.camera.aspect = width / height;
    _cameraWrapper.camera.updateProjectionMatrix();
  }

  export const setOffscreenMode = (settingsKey: CameraSettings) => {
    _cameraWrapper.noTransitionAnimation = true;
    _cameraWrapper.updateSettings(customSettings[settingsKey]);
  }

  export const setOnscreenMode = (settingsKey: CameraSettings) => {
    _cameraWrapper.noTransitionAnimation = false;
    _cameraWrapper.updateSettings(customSettings[settingsKey]);
  }

  export const setShelfView = (size: { x: number, y: number, z: number }, tripleSnap: boolean) => {
    _viewMode = 'shelf';

    const boxSize = {
      x: size.x + _boxOffset.x,
      y: size.y + _boxOffset.y,
      z: size.z + _boxOffset.z,
    }

    _cameraWrapper.setShelfViewFinal(
      { x: -boxSize.x / 2, y: 0, z: 0 },
      { x: boxSize.x / 2, y: boxSize.y, z: boxSize.z / 2 },
      false,
      tripleSnap,
    );
  }

  export const setSectionView = () => {
    _viewMode = 'component';
    _cameraWrapper.setViewFinal('frontTripleSnap');
  }

  export const setTabSpecificView = (viewPreset: string) => {
    if (_viewMode === 'shelf') _cameraWrapper.setShelfMode();
    else  _cameraWrapper.setComponentMode();

    switch (viewPreset) {
      case 'width':
        _cameraWrapper.setViewFinal('side');
        break;
      case 'height':
        _cameraWrapper.setViewFinal('side');
        break;
      case 'depth':
        _cameraWrapper.setViewFinal('side');
        break;
      case 'material':
        _cameraWrapper.setViewFinal('side');
        break;
      case 'interior':
        _cameraWrapper.setViewFinal('front');
        break;
      case 'lighting':
        _cameraWrapper.setViewFinal('front');
        break;
      case 'layout':
        _cameraWrapper.setViewFinal('front');
        break;
      case 'column_width':
        _cameraWrapper.setViewFinal('front');
        break;
      case 'door_direction':
        _cameraWrapper.setViewFinal('front');
        break;
      default:
        _cameraWrapper.setViewFinal('side');
    }
  }
}

const buildCameraSettings = (container: CanvasContainer, cameraPreset: string = 'wardrobe') => {
  let FOV = 50;
  let FAR = 12;
  let NEAR = 0.4;
  const lowResolution = container.width <= 768;
  const mediumResolution = container.width > 768 && container.width <= 1080;
  const highResolution = container.width > 1080;

  if (lowResolution) {
      FOV = 50
      FAR = 12
  } else if (mediumResolution) {
      FOV = 50
      FAR = 14
  } else if (highResolution) {
      FOV = 40
      FAR = 18
  }

  const settingsPreset = (customSettings as any)[cameraPreset] || defaultSettings;

  const settings = {
    ...settingsPreset,
    range: {
      min: NEAR,
      max: FAR,
    },
    fov: FOV,
  }

  return settings
}
