export const defaultSettings = {
    name: 'default',
    range: { min: 100, max: 30000 },
    fov: 30,
    snapping: [
        { theta: 0.52, phi: 1.52, x: 0 },
        { theta: -0.52, phi: 1.52, x: 0 },
        { theta: 0, phi: 1.52, x: 0 },
    ],
    globalPhi: 1.52,
    geometryMargins: {
        left: .100, right: .100, top: 0, bottom: .050, front: 0, back: 0,
    },
    screenMargins: {
        left: 0, right: 0, top: 0, bottom: 0,
    },
    component: {
        geometryMargins: {
          left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
    },
    animationSpeed: 1,
    shelfPolarAngles: { min: Math.PI / 6, max: Math.PI / 2 },
}

export const wardrobeSettings = {
    ...defaultSettings,
    name: 'wardrobe',
    geometryMargins: {
        left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
    },
    snapping: [
        { theta: -0.52, phi: 1.57, x: 0 },
        { theta: 0, phi: 1.57, x: 0 },
        { theta: 0.52, phi: 1.57, x: 0 },
    ],
    globalPhi: 1.57,
    shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
}

export const customSettings = {
    grid: {
        ...defaultSettings,
        name: 'grid',
        fov: 20,
        snapping: [
            { theta: -0.43, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: -.300, bottom: .200, front: 0, back: 0,
        },
    },
    cplus: {
        ...defaultSettings,
        geometryMargins: {
            left: .450, right: .450, top: .200, bottom: .200, front: 0, back: 0,
        },
    },
    desk: {
        ...defaultSettings,
        snapping: [
            { theta: 0.52, phi: 1.3, x: 0 },
            { theta: -0.52, phi: 1.3, x: 0 },
            { theta: 0, phi: 1.3, x: 0 },
        ],
        globalPhi: 1.3,
        geometryMargins: {
            left: .450, right: .450, top: .200, bottom: .200, front: 0, back: 0,
        },
    },
    chest: {
        ...defaultSettings,
        geometryMargins: {
            left: .450, right: .450, top: .200, bottom: .200, front: 0, back: 0,
        },
    },
    wardrobe: {
        ...defaultSettings,
        name: 'wardrobe',
        geometryMargins: {
            left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
        snapping: [
            { theta: -0.52, phi: 1.57, x: 0 },
            { theta: 0, phi: 1.57, x: 0 },
            { theta: 0.52, phi: 1.57, x: 0 },
        ],
        globalPhi: 1.57,
        shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
    },
    wardrobeCart: {
        ...defaultSettings,
        name: 'wardrobeCart',
        geometryMargins: {
            left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
        snapping: [
            { theta: -0.52, phi: 1.57, x: 0 },
        ],
        globalPhi: 1.57,
        shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
    },
    wardrobeSave: {
        ...defaultSettings,
        name: 'wardrobeSave',
        geometryMargins: {
            left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
        snapping: [
            { theta: 0, phi: 1.57, x: 0 },
        ],
        globalPhi: 1.57,
        shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
    },
    wardrobeDimensions: {
        ...defaultSettings,
        name: 'wardrobeDimensions',
        geometryMargins: {
            left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
        snapping: [
            { theta: 0, phi: 1.59, x: 0 }
        ],
        globalPhi: 1.57,
        shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
    },
    wardrobe_feed_left_30: {
        ...defaultSettings,
        name: 'wardrobe_feed_left_30',
        snapping: [
            { theta: -0.43, phi: 1.57, x: 0 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
    },
    wardrobe_feed_right_30: {
        ...defaultSettings,
        name: 'wardrobe_feed_right_30',
        snapping: [
            { theta: 0.43, phi: 1.57, x: 0 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
    },
    wardrobe_feed_front: {
        ...defaultSettings,
        name: 'wardrobe_feed_front',
        snapping: [
            { theta: 0, phi: 1.57, x: 0 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: .400, bottom: .400, front: 0, back: 0,
        },
    },
    row: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0, phi: 1.59 },
            { theta: -0.43, phi: 1.59 },
            { theta: 0.43, phi: 1.59 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: .150, bottom: .200, front: 0, back: 0,
        },
        animationSpeed: 1.5,
        globalPhi: 1.59,
        furnitureHeight: {
            min: .230,
            max: 4.030,
        },
        marginBottomRange: {
            min: -.400,
            max: .200,
        },
    },
    rowDim: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0, phi: 1.59 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: .150, bottom: .200, front: 0, back: 0,
        },
        animationSpeed: 1.5,
        globalPhi: 1.59,
        furnitureHeight: {
            min: .230,
            max: 4.030,
        },
        marginBottomRange: {
            min: -.400,
            max: .200,
        },
    },
    row_feed_left_30: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: -0.43, phi: 1.59 },
        ],
        geometryMargins: {
            left: .500, right: .500, top: .500, bottom: .500, front: 0, back: 0,
        },
        globalPhi: 1.59,
        furnitureHeight: { min: .230, max: 4.030 },
        marginBottomRange: { min: -.400, max: .200 },
        heightRange: { min: 1.000, max: 2.200 },
        phiRange: { min: 1.58, max: 1.61 },
        marginTopRange: { min: -.300, max: .500 },
    },
    row_feed_right_30: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0.43, phi: 1.59 },
        ],
        geometryMargins: {
            left: .500, right: .500, top: .500, bottom: .500, front: 0, back: 0,
        },
        globalPhi: 1.59,
        furnitureHeight: { min: .230, max: 4.030 },
        marginBottomRange: { min: -.400, max: .200 },
        heightRange: { min: 1.000, max: 2.200 },
        phiRange: { min: 1.58, max: 1.61 },
        marginTopRange: { min: -.300, max: .500 },
    },
    row_feed_front: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0, phi: 1.59 },
        ],
        geometryMargins: {
            left: .500, right: .500, top: .500, bottom: .500, front: 0, back: 0,
        },
        globalPhi: 1.59,
        furnitureHeight: { min: .230, max: 4.030 },
        marginBottomRange: { min: -.400, max: .200 },
        heightRange: { min: 1.000, max: 2.200 },
        phiRange: { min: 1.58, max: 1.61 },
        marginTopRange: { min: -.300, max: .500 },
    },
    feed_left_30: {
        ...defaultSettings,
        snapping: [
            { theta: -0.43, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: -.300, bottom: .200, front: 0, back: 0,
        },
    },
    feed_right_30: {
        ...defaultSettings,
        snapping: [
            { theta: 0.43, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: -.300, bottom: .200, front: 0, back: 0,
        },
    },
    feed_front: {
        ...defaultSettings,
        snapping: [
            { theta: 0, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: .450, right: .450, top: -.300, bottom: .200, front: 0, back: 0,
        },
    },
};
