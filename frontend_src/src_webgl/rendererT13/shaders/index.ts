import { ShaderChunk } from 'three';
import { pcssSetup, SoftShadowsProps } from './soft-shadows/pcss';
import { pcssSet, pcssGet } from './soft-shadows/pcssDeprecated';

export namespace Shader {
    let _softShadowsBuilt = false;
    
    export const buildSoftShadows = (settings: SoftShadowsProps & { isWebGL2: boolean }) => {
        if (_softShadowsBuilt) return;
        let shader = ShaderChunk.shadowmap_pars_fragment;

        if (settings.isWebGL2) {
            shader = shader.replace(
                '#ifdef USE_SHADOWMAP',
                '#ifdef USE_SHADOWMAP\n' + pcssSetup(settings)
            );
            shader = shader.replace(
                '#if defined( SHADOWMAP_TYPE_PCF )',
                '\nreturn PCSS(shadowMap, shadowCoord);\n#if defined( SHADOWMAP_TYPE_PCF )'
            );
        } else {
            shader = shader.replace(
                '#ifdef USE_SHADOWMAP',
                '#ifdef USE_SHADOWMAP' + pcssSet
            );
            shader = shader.replace(
                '#if defined( SHADOWMAP_TYPE_PCF )',
                pcssGet + '#if defined( SHADOWMAP_TYPE_PCF )'
            );
        }

        ShaderChunk.shadowmap_pars_fragment = shader;
        _softShadowsBuilt = true;
    }
}