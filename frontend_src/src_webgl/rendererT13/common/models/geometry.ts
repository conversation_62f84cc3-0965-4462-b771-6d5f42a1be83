import {AssetId, ColorId, MaterialId} from "./identifiers";

export interface EnvelopeBox {
    readonly x1: number;
    readonly x2: number;
    readonly y1: number;
    readonly y2: number;
    readonly z1: number;
    readonly z2: number;
}

export interface VectorXYZ {
    x: number,
    y: number,
    z: number,
}

export type Axis = "x" | "y" | "z";

export type Plane = "xy" | "yx" | "yz" | "zy" | "zx" | "xz";

export type UVInfo = { repeat: { x: number, y: number }, offset: { x: number, y: number }, rotation: number };

export const Direction = {
    TOP: 'top',
    BOTTOM: 'bottom',
    LEFT: 'left',
    RIGHT: 'right',
}

export const CubeFace = {
    TOP: ["3", "2", "7", "6"],
    BOTTOM: ["0", "1", "4", "5"],
    RIGHT: ["0", "1", "2", "3"],
    LEFT: ["4", "5", "6", "7"],
    BACK: ["1", "2", "5", "6"],
    FRONT: ["0", "3", "4", "7"],
}

export interface DrawableSegment {
    position: VectorXYZ;
    size: VectorXYZ;
    name: string;
    segmentTag: Record<string, string>;
    geometries: DrawableGeometry[];
}

export interface DrawableGeometry {
    asset: AssetId;
    groupTag: string;
    position: VectorXYZ;
    size: VectorXYZ;
    rotation: { axis: Axis, angle: number } | null;
    mirror: Axis | null;
    material: DrawableMaterial | null;
}

export interface DrawableMaterial {
    name: MaterialId;
    colors: { primary: ColorId, secondary: ColorId } | null;
    mapRatio: { u: number, v: number, w: number } | null;
    mapAngle: number;
}
