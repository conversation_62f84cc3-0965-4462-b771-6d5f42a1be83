import { ColorRepresentation, Texture, CubeTexture } from "three";

export interface BasicMaterialSettings {
  name: string,
  albedo: { color: ColorRepresentation, map: Texture | null },
  opacity: { value: number, map: Texture | null },
  faceSide: { value: 'front' | 'back' | 'double' },
  depth: { write: boolean },
}

export interface PhysicalMaterialSettings {
  name: string,
  albedo: { color: ColorRepresentation, map: Texture | null },
  opacity: { value: number, map: Texture | null },
  faceSide: { value: 'front' | 'back' | 'double' },
  depth: { write: boolean },
  roughness: { value: number, map: Texture | null },
  metalness: { value: number, map: Texture | null },
  bump: { value: number, map: Texture | null },
  normal: { scale: { x: number, y: number }, map: Texture | null },
  environment: { value: number, map: CubeTexture | null },
  emissive: { value: number, color: ColorRepresentation, map: Texture | null },
}