export type ShelfPartId =
    "openings" |
    "hovers" |
    "dimensions" |
    "horizontals" |
    "slabs" |
    "verticals" |
    "walls" |
    "horizontalInserts" |
    "verticalInserts" |
    "backs" |
    "supports" |
    "doors" |
    "drawers" |
    "plinth" |
    "frame" |
    "wardrobeBars" |
    "deskBeams" |
    "grommets" |
    "legs" |
    "wheels" |
    "lighting";

export type AssetId =
    "ambientOcclusionBox" |
    "boundingBox" |
    "line" |
    "pill" |
    "text" |
    "horizontalPanel" |
    "verticalPanel" |
    "frontalPanel" |
    "gripHandle" |
    "slimHandle" |
    "fullHandleRight" |
    "fullHandleLeft" |
    "fullHandleTop" |
    "grommet" |
    "shortLeg" |
    "longLeg" |
    "deskBeam" |
    "maskingProfile" |
    "frontWardrobeBar" |
    "crossWardrobeBar" |
    "hingeAnchor" |
    "hingeWing" |
    "channelSlab" |
    "lightBar" |
    "lightDiffuser" |
    "fakedLightRays";

export type MaterialId =
    "basic" |
    "hover" |
    "stroke" |
    "fill" |
    "laminate" |
    "laminatedEdge" |
    "laminatedPlywood" |
    "laminatedVeneer" |
    "veneer" |
    "lacquer" |
    "plywood" |
    "paintedMetal" |
    "chrome";

export type ColorId =
    "void" |
    "grayLine" |
    "graphiteFill" |
    "stormGrayFill" |
    "neutral" |
    "white" |
    "black" |
    "gray" |
    "red" |
    "yellow" |
    "blue" |
    "aubergine" |
    "natural" |
    "dustyPink" |
    "terracotta" |
    "midnightBlue" |
    "sand" |
    "mint" |
    "matteBlack" |
    "skyBlue" |
    "burgundy" |
    "cotton" |
    "darkGray" |
    "darkBrown" |
    "mustardYellow" |
    "forestGreen" |
    "lilac" |
    "beige" |
    "graphite" |
    "pink" |
    "stoneGray" |
    "mistyBlue" |
    "sageGreen" |
    "clayBrown" |
    "oliveGreen" |
    "ledWhite" |
    "ledGraphite" |
    "ledBeige" |
    "ledPink" |
    "ledStone" |
    "ledGreen" |
    "ledBlue";
