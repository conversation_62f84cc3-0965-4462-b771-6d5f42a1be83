import {Axis, DrawableSegment, VectorXYZ} from "./geometry";
import {ShelfPartId} from "./identifiers";

export interface SpaceRenderFormat {
    position: VectorXYZ;
    size: VectorXYZ;
    name: string;
    shelves: ShelfRenderFormat[];
    items: ItemsRenderFormat[];
    interior: InteriorRenderFormat;
}

export interface ShelfRenderFormat {
    position: VectorXYZ;
    size: VectorXYZ;
    rotation: { axis: Axis, angle: number } | null;
    name: string;
    schema: { key: ShelfPartId, children: DrawableSegment[] }[];
}

export interface ItemsRenderFormat {
    children: DrawableSegment[]
}

export interface InteriorRenderFormat {

}
