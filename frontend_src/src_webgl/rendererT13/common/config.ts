export const ASSETS_SOURCE = '/r_static/src_webgl/t13'

export const VIEWPORT_ID = 'conf'

export const SCENE = {
    SCALAR: 0.001,
}

export const CANVAS = {
    DEFAULT_WIDTH: 800,
    DEFAULT_HEIGHT: 800,
    OFFSCREEN_WIDTH: 600,
    OFFSCREEN_HEIGHT: 600,
}

export const FONT_3D = {
    SIZE: .034
}



export namespace RendererSettings {
    const _lights: Record<string, any> = {
        primary: {
            position: { x: 1.52, y: 1.52, z: 5.26 },
            intensity: 3.1,
            color: 0xffffff,
        },
        secondary: {
            position: { x: -3, y: 3, z: 3 },
            intensity: 0.15,
            color: 0xffffff,
        },
        fill: {
            intensity: 0.89,
            color: 0xffffff,
        }
    };
    const _shadow: Record<string, any> = {
        camera: {
            near: 1,
            far: 8.7,
            right: 5,
            left: -5,
            top: 4,
            bottom: -4,
        },
        soften: {
            focus: 0,
            samples: 10,
            size: 25
        },
        map: {
            size: 2048,
            bias: 0.0003,
        }
    };
    const _background: Record<string, any> = {
        color: 0xbfbfbf,
        envMapIntensity: .88,
        aoMapIntensity: 1,
        lightMapIntensity: 0,
        opacity: .66,
        roughness: 0.44,
        metalness: 0,
    }
    const _colors: Record<string, number> = {
        "default": 0xaaaaaa,
        "hover": 0xFF3C00,
        "void": 0x000000,
        "shadow": 0x444444,
        "grayLine": 0x4a4a4a,
        "graphiteFill": 0x484444,
        "stormGrayFill": 0x7C7D81,
        "neutral": 0xffffff,
        "white": 0xf8f2ea,
        "black": 0x393939,
        "gray": 0xb5b3b2,
        "red": 0xc52b2b,
        "yellow": 0xdba81e,
        "blue": 0x1d3d92,
        "dustyPink": 0xbc9b9b,
        "midnightBlue": 0x1d2f43,
        "sand": 0xcfc0b1,
        "darkGray": 0x5c5757,
        "darkBrown" : 0x693f1d,
        "mustardYellow": 0x9f8f5a,
        "graphite": 0x4C4C49,
        "beige": 0xb8ac9e,
        "pink": 0x9f7f7c,
        "sageGreen": 0xa3af9a,
        "stoneGray": 0x797166,
        "stoneLightGray": 0x999287,
        "mistyBlue": 0x7d8f9f,
        "clayBrown": 0xb57e5b,
        "oliveGreen": 0x5c6c3c,
        "reisingerPink": 0xd88b9d,
        "ash": 0xe8d6b5,
        "oak": 0xd5b190,
        "walnut": 0x7f5f42,

        "ledWhite": 0xdfd2c1,
        "ledGraphite": 0xdfd2c1,
        "ledBeige": 0xd8c2a5,
        "ledPink": 0xdfb5c6,
        "ledStone": 0xdfd2c1,
        "ledGreen": 0xa1cf8e,
        "ledBlue": 0x8eaacf,
    };
    const _materials: Record<string, any> = {
        LAMINATE: {
            albedo: { map: null, color: 0xffffff },
            roughness: { map: null, value: 0.47 },
            metalness: { map: null, value: 0 },
            bump: { map: null, value: 0.0 },
            normal: { map: null, scale: { x: 0.0, y: 0.0 } },
            environment: { map: 'interior_environment', value: 0.28 }
        },
        PLYWOOD: {
            albedo: { map: null, color: 0xffffff },
            roughness: { map: null, value: 0.8 },
            metalness: { map: null, value: 0.0 },
            bump: { map: null, value: 0.0 },
            normal: { map: null, scale: { x: 0.0, y: 0.0 } },
            environment: { map: 'interior_environment', value: 0.8 }
        },
        PLYWOOD_EDGE: {
            albedo: { map: 'plywood_edge_albedo', color: 0xffffff },
            roughness: { map: null, value: 0.5 },
            metalness: { map: null, value: 0.0 },
            bump: { map: null, value: 0.0 },
            normal: { map: null, scale: { x: 0.0, y: 0.0 } },
            environment: { map: 'interior_environment', value: 0.4 }
        },
        VENEER: {
            albedo: { map: 'veneer_face_albedo', color: 0xffffff },
            roughness: { map: null, value: 0.38 },
            metalness: { map: null, value: 0.0 },
            bump: { map: 'veneer_bump', value: 0.02 },
            normal: { map: null, scale: { x: 0.0, y: 0.0 } },
            environment: { map: 'interior_environment', value: 0.8 }
        },
        LACQUER: {
            albedo: { map: null, color: 0xffffff },
            roughness: { map: null, value: 0.8 },
            metalness: { map: null, value: 0.0 },
            bump: { map: null, value: 0.0 },
            normal: { map: null, scale: { x: 0.0, y: 0.0 } },
            environment: { map: 'interior_environment', value: 0.8 }
        },
        PAINTED_METAL: {
            albedo: { map: null, color: 0xffffff },
            roughness: { map: null, value: 0.34 },
            metalness: { map: null, value: 0.1 },
            bump: { map: null, value: 0.0 },
            normal: { map: null, scale: { x: 0.0, y: 0.0 } },
            environment: { map: 'interior_environment', value: 0.88 }
        },
        CHROME: {
            albedo: { map: null, color: 0x777777 },
            roughness: { map: null, value: 0.42 },
            metalness: { map: null, value: 0.95 },
            bump: { map: null, value: 0.0 },
            normal: { map: null, scale: { x: 0.0, y: 0.0 } },
            environment: { map: 'interior_environment', value: 0.78 }
        },
        AMBIENT_OCCLUSION: {
            opacity: { map: 'innerAOMap', value: 0.22 },
        },
        FAKED_LIGHT_RAYS: {
            albedo: { map: null, color: 0xffffff },
            opacity: { map: 'fake_rays', value: 0.5 },
            opacityForColor: {
                "ledWhite": 0.76,
                "ledGraphite": 0.28,
                "ledBeige": 0.65,
                "ledPink": 0.33,
                "ledStone": 0.32,
                "ledGreen": 0.60,
                "ledBlue": 0.50,
            },
            emissive: { map: null, value: 1 },
        },
    };

    export const getColorsConfig = () => _colors;
    export const getMaterialsConfig = () => _materials;
    export const getLightsConfig = () => _lights;
    export const getShadowConfig = () => _shadow;
    export const getBackgroundConfig = () => _background;
    export const updateColorsConfig = (colorPayload: { name: string, value: any }) => {
        _colors[colorPayload.name] = colorPayload.value;
    };
    export const updateMaterialsConfig = (materialName: string, propertyName: string, payload: { name: string, value: any }) => {
        _materials[materialName][propertyName][payload.name] = payload.value;
    };
    export const updateLightsConfig = (lightName: string, payload: { name: string, value: any }) => {
        _lights[lightName][payload.name] = payload.value;
    }
    export const updateShadowConfig = (propertyName: string, payload: { name: string, value: any }) => {
        _shadow[propertyName][payload.name] = payload.value;
    }
    export const updateBackgroundConfig = (payload: { name: string, value: any }) => {
        _background[payload.name] = payload.value;
    }
}
