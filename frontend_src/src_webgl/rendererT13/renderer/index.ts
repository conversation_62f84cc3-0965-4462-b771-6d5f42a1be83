import {
  WebGLRenderer,
  sRGBEncoding,
  ACESFilmicToneMapping,
  ReinhardToneMapping,
  CineonToneMapping,
  PCFSoftShadowMap,
  Scene,
  PerspectiveCamera,
  Color,
  SRGBColorSpace
} from 'three';
import { CanvasContainer } from '../common/models/utils';

export namespace PrimeScreenRenderer {
  let _engine: WebGLRenderer;

  export const getInstance = () => _engine;

  export const setup = (container: CanvasContainer) => {
    _engine = createRendererContext(container);
  }

  export const updateSize = (width: number, height: number) => {
    _engine.setSize( width, height, false );
  }

  export const render = (scene: Scene, camera: PerspectiveCamera, updateShadowMap: boolean = true) => {
    _engine.shadowMap.needsUpdate = true;
    _engine.render(scene, camera);
  }

  export const getInfo = () => _engine.info;

  export const getContextCapabilities = () => _engine.capabilities;
}

export namespace OffScreenRenderer {
  let _engine: WebGLRenderer;

  export const setup = (container: CanvasContainer) => {
    _engine = createRendererContext(container);
  }

  export const updateSize = (width: number, height: number) => {
    _engine.setSize( width, height, false );
  }

  export const updateBackground = (backgroundColor:number , transparency: number, scene: Scene) => {
    _engine.setClearColor(backgroundColor, transparency);
    scene.background = new Color(backgroundColor);
  }

  export const screenshot = (scene: Scene, camera: PerspectiveCamera) => {
    _engine.shadowMap.needsUpdate = true;
    _engine.render(scene, camera);
    return _engine.domElement.toDataURL('image/png');
  }

  export const getInfo = () => _engine.info;

  export const getContextCapabilities = () => _engine.capabilities;
}

const createRendererContext = (container: CanvasContainer) => {
  const instance = new WebGLRenderer({
    alpha: true,
    antialias: true,
    powerPreference: "high-performance",
    canvas: container.domElement || undefined,
  });
  instance.setClearColor(0xE4E6E6, 1);
  instance.setSize( container.width, container.height, false );
  instance.toneMapping = ACESFilmicToneMapping;
  instance.outputColorSpace = SRGBColorSpace;
  instance.toneMappingExposure = 1.0;
  instance.shadowMap.enabled = true;
  instance.shadowMap.autoUpdate = false;
  instance.shadowMap.type = PCFSoftShadowMap;
  return instance;
}
