import { GeometryAssets } from "../assets/geometry";
import { TextureAssets } from "../assets/texture";
import { SpaceRenderFormat } from "../common/models/formats";
import { VIEWPORT_ID } from "../common/config";
import { Space } from "../space";
import { Camera } from "../camera";
import { Interaction, OffscreenInteraction } from "./interaction";
import { PerformanceMonitor } from "../metrics/performance";

export namespace Level {
  const _performanceMonitor = PerformanceMonitor.getInstance();

  const _screenInteractionSettings: { primeScreenActive: boolean, offScreenActive: boolean } 
    = { primeScreenActive: true, offScreenActive: false };

  export const setScreenInteractionSettings = (primeScreenActive: boolean, offScreenActive: boolean) => {
    _screenInteractionSettings.primeScreenActive = primeScreenActive;
    _screenInteractionSettings.offScreenActive = offScreenActive;
  }

  export const loadAssets = async () => {
    _performanceMonitor.registerActionStart('assetsLoadTime');
    await GeometryAssets.Pool.fetch();
    await TextureAssets.Repository.fetch();
    document.getElementById(VIEWPORT_ID)!.dispatchEvent(new CustomEvent('loadCanvas'));
    _performanceMonitor.registerActionEnd('assetsLoadTime');
  }

  export const compose = (spaceRenderFormat: SpaceRenderFormat) => {
    Space.setRenderFormat(spaceRenderFormat);
    Space.Interior.compose();
    Space.Shelf.compose();
    Space.Items.compose();
  }

  export const draw = (spaceRenderFormat: SpaceRenderFormat, updateCamera: boolean = true) => {
    _performanceMonitor.registerActionStart('levelBuildTime');
    Space.setRenderFormat(spaceRenderFormat);
    Space.Shelf.update();
    Space.Interior.update();
    Space.Items.update();
    console.log('Space', Space.getScene());
    if (updateCamera) Camera.focusShelf(spaceRenderFormat);
    if (_screenInteractionSettings.primeScreenActive) Interaction.adaptSpaceToCurrentState();
    if (_screenInteractionSettings.offScreenActive) OffscreenInteraction.adaptSpaceToCurrentState();
    _performanceMonitor.registerActionEnd('levelBuildTime');
  }

  export const reDraw = () => {
    Space.Shelf.update();
    Space.Interior.update();
    Space.Interior.updateLights();
    Space.Interior.updateShadow();
    Space.Interior.updateBackground();
    if (_screenInteractionSettings.primeScreenActive) Interaction.adaptSpaceToCurrentState();
    if (_screenInteractionSettings.offScreenActive) OffscreenInteraction.adaptSpaceToCurrentState();
  }
}
