import { Frame } from "./frame";
import { Space } from "../space";
import { PrimeScreenRenderer } from "../renderer";
import { Camera } from "../camera";
import { OldCanvasInteraction } from "../interactions/listener";
import { CanvasInteraction } from "../interactions/canvas";
import { ShelfContext, SectionEvent } from "../interactions/strategy";
import { PerformanceMonitor } from "../metrics/performance";

export namespace OffscreenInteraction {
  let _shelfContext: ShelfContext;

  export const initialize = () => {
    _shelfContext = new ShelfContext(false);
  }

  export const adaptSpaceToCurrentState = () => {
    _shelfContext.forceUpdateSectionStateToIdle();
    _shelfContext.setupLightingLayer(true);
  };
}

export namespace Interaction {
  const _canvasInteraction = new CanvasInteraction();
  let _shelfContext: ShelfContext;
  let _deprecateInteraction: OldCanvasInteraction;
  const _performanceMonitor = PerformanceMonitor.getInstance();

  export const setMobileModeGetter = (isMobilePredicate: () => boolean) => {
    _canvasInteraction.setMobileModeFunc(isMobilePredicate);
  }

  export const initialize = (target: HTMLElement, strategy: any) => {
    _shelfContext = new ShelfContext();
    _deprecateInteraction = new OldCanvasInteraction();

    const size = Frame.getSize();
    _deprecateInteraction.setup(
      { canvas: target, width: size.width, height: size.height },
      Camera.getInstance(),
      Space.getShelf(),
      Space.getScene(),
        {
          render: PrimeScreenRenderer.render,
          domElement: PrimeScreenRenderer.getInstance().domElement,
        },
      strategy,
      Space.getSpaceFormat(),
      Camera.getControls()
    );

    _canvasInteraction.initialize(
      { canvas: target, width: size.width, height: size.height },
      Camera.getInstance(),
      Space.getShelf()
    );
    _canvasInteraction.setupListeners();
    _canvasInteraction.setConfiguratorDispatcher(strategy);

    const subscribedEvents: SectionEvent[] = [
      'hover-in', 'hover-out', 'hover-other',
      'click-in', 'click-out', 'click-other',
    ];

    subscribedEvents.forEach((eventName: SectionEvent) => {
      _canvasInteraction.addEventListener(eventName, (data: any) => {
        _performanceMonitor.registerActionStart('shelfStateChange');
        _shelfContext.sectionStateUpdate(data.id.toString(), eventName);
        if (eventName === 'click-in') Camera.setSectionView();
        if (eventName === 'click-out') Camera.setShelfView(Space.getShelfRenderFormat().size, true);
        _performanceMonitor.registerActionEnd('shelfStateChange');
      })
    });
  };

  export const adaptSpaceToCurrentState = () => {
    _shelfContext.updateSectionsState();
    _shelfContext.setupLightingLayer();
  };

  // FIXME: there are not events, just key string to trigger some action in shelf state machine
  export const dispatch = (eventName: string, payload: any) => {
    _performanceMonitor.registerActionStart('interactionDispatch');
    if (eventName === 'shelf-opened') {
      const currentInteractionLayer = _shelfContext.getInteractionLayer();
      if (currentInteractionLayer === 'shelf_closed_dimensions_hidden') {
        _shelfContext.changeInteractionLayer('shelf_opened_dimensions_hidden');
      }
      if (currentInteractionLayer === 'shelf_closed_dimensions_visible') {
        _shelfContext.changeInteractionLayer('shelf_opened_dimensions_visible');
      }
    }
    if (eventName === 'shelf-closed') {
      const currentInteractionLayer = _shelfContext.getInteractionLayer();
      if (currentInteractionLayer === 'shelf_opened_dimensions_hidden') {
        _shelfContext.changeInteractionLayer('shelf_closed_dimensions_hidden');
      }
      if (currentInteractionLayer === 'shelf_opened_dimensions_visible') {
        _shelfContext.changeInteractionLayer('shelf_closed_dimensions_visible');
      }
    }
    if (eventName === 'dimensions-visible') {
      const currentInteractionLayer = _shelfContext.getInteractionLayer();
      if (currentInteractionLayer === 'shelf_opened_dimensions_hidden') {
        _shelfContext.changeInteractionLayer('shelf_opened_dimensions_visible');
        Space.Shelf.update();
      }
      if (currentInteractionLayer === 'shelf_closed_dimensions_hidden') {
        _shelfContext.changeInteractionLayer('shelf_closed_dimensions_visible');
        Space.Shelf.update();
      }
    }
    if (eventName === 'dimensions-hidden') {
      const currentInteractionLayer = _shelfContext.getInteractionLayer();
      if (currentInteractionLayer === 'shelf_opened_dimensions_visible') {
        _shelfContext.changeInteractionLayer('shelf_opened_dimensions_hidden');
        Space.Shelf.update();
      }
      if (currentInteractionLayer === 'shelf_closed_dimensions_visible') {
        _shelfContext.changeInteractionLayer('shelf_closed_dimensions_hidden');
        Space.Shelf.update();
      }
    }
    if (eventName === 'section-unselected') {
      _canvasInteraction.setClickedSectionId(null);
      _shelfContext.resetSectionsState();
    }
    if (eventName === 'section-selected') {
      const segmentUUID = getSegmentUUID(payload.id);
      _canvasInteraction.setClickedSectionId(segmentUUID);
      _shelfContext.resetSectionsState();
      _shelfContext.setSectionState(segmentUUID, 'section_selected');
    }
    _performanceMonitor.registerActionEnd('interactionDispatch');
  };

  // FIXME: viewMode ha 3 states 'side' | 'deepSide' | 'front';
  export const setConcreteView = (view: string) => {
    Camera.setTabSpecificView(view);
  }

  export const setSectionView = () => {
    Camera.setSectionView();
  }

  export const isDimensionHidden = () =>
      _shelfContext === undefined ||
      _shelfContext.getInteractionLayer() === 'shelf_closed_dimensions_hidden' ||
      _shelfContext.getInteractionLayer() === 'shelf_opened_dimensions_hidden';

  export const setShelfView = (tripleSnap: boolean) => {
    const shelf = Space.getShelfRenderFormat();
    Camera.setShelfView(shelf.size, tripleSnap);
  }

  export const getSegmentUUID = (sectionId: number): string => {
    const shelf = Space.getShelfRenderFormat();
    const hovers = shelf.schema.find((record) => record.key === 'hovers');
    const pickedSections = hovers!.children.filter((hover) => Number(hover.segmentTag.sectionId) === sectionId);
    const selectableSection = pickedSections.find((hover) => hover.segmentTag.selectable);
    return selectableSection ? selectableSection.segmentTag.segmentId : 'none';
  }
}
