import { CanvasContainer } from '../common/models/utils';
import { PrimeScreenRenderer, OffScreenRenderer } from '../renderer';
import { Camera, CameraSettings } from '../camera';
import { Space } from '../space';
import { Shader } from '../shaders';
import { PerformanceMonitor } from '../metrics/performance';

export namespace Frame {
  const _primeScreenContainer = {} as CanvasContainer;
  const _offScreenContainer = {} as CanvasContainer;
  let _loopActive = false;
  let _updateShadowMap = true;
  const _performanceMonitor = PerformanceMonitor.getInstance();
  let _cameraPreset: CameraSettings = "row";
  let _state: 'idle' | 'rendering' = 'idle';

  export const setupPrimeScreen = (container: CanvasContainer) => {
    setContainer(container, 'primeScreen');
    Camera.setup(_primeScreenContainer, false, _cameraPreset);
    PrimeScreenRenderer.setup(_primeScreenContainer);
    Shader.buildSoftShadows({
      focus: 0,
      samples: 10,
      size: 25,
      isWebGL2: PrimeScreenRenderer.getContextCapabilities().isWebGL2
    });
  };

  export const setupOffScreen = (container: CanvasContainer) => {
    setContainer(container, 'offScreen');
    Camera.setup(_primeScreenContainer, true, _cameraPreset);
    OffScreenRenderer.setup(_offScreenContainer);
    Shader.buildSoftShadows({
      focus: 0,
      samples: 10,
      size: 25,
      isWebGL2: OffScreenRenderer.getContextCapabilities().isWebGL2
    });
  };

  export const changeSize = (width: number, height: number, containerMode: string) => {
    if (containerMode == 'offScreen') {
      _offScreenContainer.width = width;
      _offScreenContainer.height = height;
    } else {
      _primeScreenContainer.width = width;
      _primeScreenContainer.height = height;
    }
    Camera.updateAspectRatio(width, height);
    Camera.focusShelf(Space.getSpaceFormat());
    if (containerMode == 'offScreen') {
      OffScreenRenderer.updateSize(width, height);
    } else {
      PrimeScreenRenderer.updateSize(width, height);
      PrimeScreenRenderer.render(Space.getScene(), Camera.getInstance());
    }
  };

  export const updateCanvasColor = (backgroundColor:number , transparency: number) => {
    OffScreenRenderer.updateBackground(backgroundColor, transparency, Space.getScene());
  }

  export const updateCameraSettings = (cameraSettings: string) => {
    Camera.updateCameraSettings(_offScreenContainer, cameraSettings);
  }

  export const getSize = () =>
    ({ width: _primeScreenContainer.width, height: _primeScreenContainer.height });

  export const update = () => {
    _performanceMonitor.registerActionStart('renderTime');
    PrimeScreenRenderer.render(Space.getScene(), Camera.getInstance(), _updateShadowMap);
    _performanceMonitor.registerActionEnd('renderTime');
  };

  export const updateLoop = () => {
    if (_loopActive) {
      _performanceMonitor.registerActionStart('animationTine');
      _loopActive = false;
      Camera.updateContols();
      PrimeScreenRenderer.render(Space.getScene(), Camera.getInstance(), _updateShadowMap);
      _updateShadowMap = true;
      _performanceMonitor.registerActionEnd('animationTine');
    }
    _performanceMonitor.registerTick();
    requestAnimationFrame( () => updateLoop() );
  }

  export const setUpdateLoopActive = (updateShadowMap = true) => {
    _loopActive = true;
    _updateShadowMap = updateShadowMap;
  }

  export const getScreenshot = (mode: CameraSettings, skipPrime: boolean = false) => {
    Camera.updateAspectRatio(_offScreenContainer.width, _offScreenContainer.height);
    Camera.setOffscreenMode(mode);
    Camera.focusShelf(Space.getSpaceFormat(), true);
    const screenshot = OffScreenRenderer.screenshot(Space.getScene(), Camera.getInstance());
    if (skipPrime) return screenshot;
    Camera.updateAspectRatio(_primeScreenContainer.width, _primeScreenContainer.height);
    Camera.setOnscreenMode(_cameraPreset);
    Camera.focusShelf(Space.getSpaceFormat());
    return screenshot;
  }

  export const hideInterior = () => {
    Space.Interior.toggleVisibility(false);
  };

  export const showInterior = () => {
    Space.Interior.toggleVisibility(true);
  };

  const setContainer = (container: CanvasContainer, mode: 'primeScreen' | 'offScreen') => {
    const containerByMode = { primeScreen: _primeScreenContainer, offScreen: _offScreenContainer };
    containerByMode[mode].width = container.width;
    containerByMode[mode].height = container.height;
    containerByMode[mode].domElement = container.domElement || document.createElement("CANVAS");
  };
}
