import {
    EventDispatcher,
    Raycaster,
    Vector2,
    Vector3,
    PerspectiveCamera,
    Group,
    Intersection,
    Object3D,
    Mesh
} from 'three';
import { VectorXYZ } from "../common/models/geometry";
type ViewContainer = { canvas: HTMLElement, width: number, height: number };

export class CanvasInteraction extends EventDispatcher {
  _viewContainer!: ViewContainer;
  _camera!: PerspectiveCamera;
  _shelf!: Group;
  _rayCaster = new Raycaster();
  _mousePosition = new Vector2();
  _prevHoveredSectionId: string | null = null;
  _prevClickedSectionId:  string | null = null;

  _configuratorDispatcher: any;
  _isMobilePredicate!: () => boolean;

  constructor() { super() };

  initialize = (container: ViewContainer, camera: PerspectiveCamera, shelf: Group) => {
    this._viewContainer = container;
    this._camera = camera;
    this._shelf = shelf;
  };

  setClickedSectionId = (id: string | null) => {
    this._prevClickedSectionId = id;
  }

  setHoveredSectionId = (id: string | null) => {
    this._prevHoveredSectionId = id;
  }

  setConfiguratorDispatcher = (configuratorDispatcher: any) => {
    this._configuratorDispatcher = configuratorDispatcher;
  }

  setMobileModeFunc = (isMobilePredicate: () => boolean) => {
    this._isMobilePredicate = isMobilePredicate;
  }

  setupListeners = () => {
    this._viewContainer.canvas.addEventListener('mousemove', (event: MouseEvent) => {
      event.preventDefault();

      const intersectedSection = this.checkSectionIntersected(event);

      const nowHover = intersectedSection.id !== null;
      const noHover = intersectedSection.id === null;
      const anyHovered = this._prevHoveredSectionId !== null;
      const newHovered = this._prevHoveredSectionId !== intersectedSection.id;
      const anyClicked = this._prevClickedSectionId !== null;
      const hoveredOtherThanClicked = anyClicked && newHovered && this._prevClickedSectionId !== intersectedSection.id;

      const hoverInEvent = {
        id: intersectedSection.id,
        type: 'hover-in',
        condition: nowHover && newHovered,
        sectionId: intersectedSection.sectionId,
        selectable: intersectedSection.selectable,
      };

      const hoverOutEvent = {
        id: this._prevHoveredSectionId,
        type: 'hover-out',
        condition: anyHovered && (noHover || newHovered),
      };

      const hoverOutClickedEvent = {
        id: this._prevClickedSectionId,
        type: 'hover-out',
        condition: anyClicked && noHover && newHovered,
      };

      const hoverOtherEvent = {
        id: this._prevClickedSectionId,
        type: 'hover-other',
        condition: !anyHovered && hoveredOtherThanClicked,
      }

      if (anyClicked && hoverOutEvent.id === this._prevClickedSectionId && nowHover) {
        hoverOutEvent.type = 'hover-other';
      }

      if (nowHover) {
        this._configuratorDispatcher('UPDATE_ACTIVE_ELEMENT', Number(intersectedSection.sectionId));
        this._configuratorDispatcher('ui/UPDATE_CLOUD_POSITION', this.calculateCloudPosition(intersectedSection.section));
      }
      if (noHover) {
        this._configuratorDispatcher('UPDATE_ACTIVE_ELEMENT', null);
        this._configuratorDispatcher('ui/HIDE_ADJUST_SIGNIFIER');
      }

      if (hoverInEvent.condition) this.dispatchEvent(hoverInEvent);
      if (hoverOutEvent.condition) this.dispatchEvent(hoverOutEvent);
      if (hoverOutClickedEvent.condition) this.dispatchEvent(hoverOutClickedEvent);
      if (hoverOtherEvent.condition) this.dispatchEvent(hoverOtherEvent);

      this._prevHoveredSectionId = intersectedSection.id;
    });

    this._viewContainer.canvas.addEventListener('click', (event: MouseEvent) => {
      // event.preventDefault();
      //
      // const intersectedSection = this.checkSectionIntersected(event);
      //
      // const nowClick = intersectedSection.id !== null;
      // const noClick = intersectedSection.id === null;
      // const anyClicked = this._prevClickedSectionId !== null;
      // const newClicked = this._prevClickedSectionId !== intersectedSection.id;
      //
      // const isNotSelectable = nowClick && intersectedSection.selectable === false;
      //
      // const clickInEvent = {
      //   id: isNotSelectable ? intersectedSection.linked : intersectedSection.id,
      //   type: 'click-in',
      //   condition: nowClick,
      //   sectionId: intersectedSection.sectionId,
      //   selectable: intersectedSection.selectable,
      // };

      // const clickOutEvent = {
      //   id: this._prevClickedSectionId,
      //   type: 'click-out',
      //   condition: anyClicked && noClick && !this._isMobilePredicate(),
      // };
      //
      // const clickOtherEvent = {
      //   id: this._prevClickedSectionId,
      //   type: 'click-other',
      //   condition: anyClicked && newClicked && nowClick,
      // };

      // if (nowClick) {
      //   this._configuratorDispatcher('ACTIVE_COMPONENT', `${intersectedSection.sectionId}`);
      //   if (this._isMobilePredicate()) this._configuratorDispatcher('ui/SET_LAYOUT_TAB_FROM_RENDERER_TEMP');
      // }
      // if (noClick) {
      //   if (!this._isMobilePredicate()) this._configuratorDispatcher('DEACTIVATE_COMPONENT');
      //   if (this._isMobilePredicate()) this._configuratorDispatcher('renderer/RESET_TAB_SPECIFIC_VIEW');
      //   else this._configuratorDispatcher('renderer/RENDERER_SET_SHELF_VIEW');
      // }

      // if (clickOutEvent.condition) this.dispatchEvent(clickOutEvent);
      // if (clickOtherEvent.condition) this.dispatchEvent(clickOtherEvent);
      // if (clickInEvent.condition) this.dispatchEvent(clickInEvent);

      // if (isNotSelectable) {
      //   this.dispatchEvent({
      //     id: intersectedSection.linked,
      //     type: 'hover-in',
      //     sectionId: intersectedSection.sectionId,
      //     selectable: intersectedSection.selectable,
      //   });
      //   this._prevHoveredSectionId = intersectedSection.linked;
      // }

      // this._prevClickedSectionId = isNotSelectable ? intersectedSection.linked : intersectedSection.id;
    })
  };

  projectMousePosition = (mousePosition: { x: number, y: number }) => ({
    x: (mousePosition.x / this._viewContainer.canvas.offsetWidth) * 2 - 1,
    y: -(mousePosition.y / this._viewContainer.canvas.offsetHeight) * 2 + 1,
  });

  getIntersects = (mousePosition: { x: number, y: number }, camera: PerspectiveCamera, shelf: Group) => {
    this._mousePosition.set(mousePosition.x, mousePosition.y);
    this._rayCaster.setFromCamera(this._mousePosition, camera);
    return this._rayCaster.intersectObject(shelf, true) as Intersection<Object3D<Event>>[];
  };

  getHoveredBox = (intersects: Intersection<Object3D<Event>>[]) => {
    const hovers = intersects.filter(intersection =>
      intersection && intersection.object && intersection.object.name === "boxGeometry" && intersection.object!.parent!.userData.hoverIsActive
    );
    return hovers.length > 0 ? hovers[0] : null;
  };

  getIdFromHoveredBox = (hoveredBox: Intersection<Object3D<Event>>) => ({
    id: hoveredBox.object!.parent!.parent!.userData.segmentId,
    linked: hoveredBox.object!.parent!.parent!.userData.linkedSegment,
    sectionId: hoveredBox.object!.parent!.parent!.userData.sectionId,
    section: hoveredBox.object,
    selectable: hoveredBox.object!.parent!.parent!.userData.selectable,
  })

  checkSectionIntersected = (event: MouseEvent) => {
    const projectedPoint = this.projectMousePosition({ x: event.offsetX, y: event.offsetY });
    const intersects = this.getIntersects(projectedPoint, this._camera, this._shelf);
    const hoveredBox = this.getHoveredBox(intersects);

    return hoveredBox
        ? this.getIdFromHoveredBox(hoveredBox)
        : { id: null, linked: null, sectionId: null, section: null, selectable: false };
  };

  calculateCloudPosition = (mesh: any) => {
    const scale = mesh.parent.scale;
    const position = mesh.parent.parent.position;

    const offset = -.1;
    const positionOnScene = {
      x: position.x + scale.x/2 + offset,
      y: position.y,
      z: position.z + scale.z/2,
    }

      const width = this._viewContainer.canvas.offsetWidth;
      const height = this._viewContainer.canvas.offsetHeight;
      const point = new Vector3()
      point.set(positionOnScene.x, positionOnScene.y, positionOnScene.z)
      point.project(this._camera);
      return {
          x: Math.round((point.x + 1) / 2 * width),
          y: Math.round(-(point.y - 1) / 2 * height),
      }
  }
};
