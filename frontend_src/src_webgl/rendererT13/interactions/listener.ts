import { PerspectiveC<PERSON>ra, Group, Scene, WebGLRenderer, EventDispatcher } from 'three';
import { Frame } from './../api/frame';

type ViewContainer = { canvas: HTMLElement, width: number, height: number };

export class OldCanvasInteraction extends EventDispatcher {
  setup = (
    viewContainer: ViewContainer,
    camera: PerspectiveCamera,
    shelf: Group,
    scene: Scene,
    renderer: {
        render: (scene: Scene, camera: PerspectiveCamera, updateShadows?: boolean) => void;
        domElement: HTMLCanvasElement;
    },
    dispatcher: any,
    spaceRenderFormat: any,
    controls: any,
  ) => {

    (controls as any).addEventListener("change", () => {
      renderer.render(scene, camera, false);
    });

    (controls as any).addEventListener('render', () => {
      Frame.setUpdateLoopActive(false);
        // dispatcher('interactions/SET_IS_CAMERA_ANIMATING', true);
    });

    // (controls as any).addEventListener('animationEnd', () => {
    //   setTimeout(() => {
    //     dispatcher('ui/UPDATE_COORDINATES_FOR_BUTTONS');
    //     dispatcher('interactions/SET_IS_CAMERA_ANIMATING', false);
    //   }, 100);
    // });

    // viewContainer.canvas.addEventListener('mousedown', () => (
    //   dispatcher('interactions/SET_IS_ORBIT_MOVING', true)
    // ), false);
    //
    // document.body.addEventListener('mouseup', () => {
    //     setTimeout(() => {
    //         dispatcher('interactions/SET_IS_ORBIT_MOVING', false);
    //     }, 100);
    // }, false);
    //
    // viewContainer.canvas.addEventListener('touchstart', () => (
    //   dispatcher('interactions/SET_IS_ORBIT_MOVING', true)
    // ), false);
    //
    // document.body.addEventListener('touchend', () => {
    //     setTimeout(() => {
    //         dispatcher('interactions/SET_IS_ORBIT_MOVING', false);
    //     }, 100);
    // }, false);

    const setUpScreenshotShortcut = () => {
      let keysPressed: string[] = [];
      document.onkeydown = event => {
          if (!keysPressed.includes(event.key)) {
              keysPressed.push(event.key);
          }
          const shortcutIsActive = keysPressed.length === 3
              && keysPressed.includes('z')
              && keysPressed.includes('x')
              && keysPressed.includes('p');

          const renderFormatKeys = keysPressed.length === 3
              && keysPressed.includes('z')
              && keysPressed.includes('x')
              && keysPressed.includes('o');

          if (shortcutIsActive) {
            renderer.render(scene, camera, false);
            renderer.domElement.toBlob(blob => createAndClickOffscreenBlobLink(blob));
          }

          if (renderFormatKeys) createAndClickOffscreenJSONLink(spaceRenderFormat)
      };
      document.onkeyup = () => {
          keysPressed = [];
      };
    }

    setUpScreenshotShortcut();
  }
}

// eslint-disable-next-line class-methods-use-this
const createAndClickOffscreenBlobLink = (blob: any) => {
  const a = document.createElement('a');
  const date = new Date();
  const utc = date.toJSON().slice(0, 10).replace(/-/g, '/');
  document.body.appendChild(a);
  a.style.display = 'none';
  const url = window.URL.createObjectURL(blob);
  a.href = url;
  a.download = `Wardrobe_screenshot_(${utc}-${date.getHours()}:${date.getMinutes()})`;
  a.click();
}

const createAndClickOffscreenJSONLink = (geometry: any) => {
  const dataStr = 'data:text/json;charset=utf-8,' + encodeURIComponent(JSON.stringify(geometry));
  const downloadAnchorNode = document.createElement('a');
  downloadAnchorNode.setAttribute('href', dataStr);
  downloadAnchorNode.setAttribute('download', `space_render_format.json`);
  downloadAnchorNode.click();
  downloadAnchorNode.remove();
}
