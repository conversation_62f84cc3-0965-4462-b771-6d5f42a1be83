import { Space } from "../space";
import { Animation } from "../animations";
import { Frame } from "../api/frame";
import { debounce } from "lodash-es";

export type SectionEvent =
  'hover-in' |
  'hover-other' |
  'hover-out' |
  'click-in' |
  'click-other' |
  'click-out';

type InteractionLayer =
  'shelf_opened_dimensions_hidden' |
  'shelf_opened_dimensions_visible' |
  'shelf_closed_dimensions_hidden' |
  'shelf_closed_dimensions_visible';

type SectionState =
  'section_idle' |
  'section_unselected_hovered' |
  'section_selected' |
  'section_selected_hovered' |
  'section_selected_hovered_other';

type LightingLayer = 'dayLight' | 'eveningLight';

type TransitionAction = (event: SectionEvent) => { state: SectionState, changed: boolean };

type SectionTransitionMap = Record<SectionState, TransitionAction>;

const sectionTransitionMap: SectionTransitionMap = {
  section_idle: (event: SectionEvent) => {
    if (event === "hover-in") return { state: "section_unselected_hovered", changed: true };
    if (event === "click-in") return { state: "section_selected", changed: true };
    else return { state: "section_idle", changed: false };
  },
  section_unselected_hovered: (event: SectionEvent) => {
    if (event === "hover-out") return { state: "section_idle", changed: true };
    if (event === "click-in") return { state: "section_selected", changed: true };
    else return { state: "section_unselected_hovered", changed: false };
  },
  section_selected: (event: SectionEvent) => {
    if (event === "hover-in") return { state: "section_selected_hovered", changed: true };
    if (event === "hover-other") return { state: "section_selected_hovered_other", changed: true };
    if (event === "click-out") return { state: "section_idle", changed: true };
    else return { state: "section_selected", changed: false };
  },
  section_selected_hovered: (event: SectionEvent) => {
    if (event === "hover-other") return { state: "section_selected_hovered_other", changed: true };
    if (event === "hover-out") return { state: "section_selected", changed: true };
    else return { state: "section_selected_hovered", changed: false };
  },
  section_selected_hovered_other: (event: SectionEvent) => {
    if (event === "hover-in") return { state: "section_selected_hovered", changed: true };
    if (event === "hover-out") return { state: "section_selected", changed: true };
    if (event === "click-other") return { state: "section_idle", changed: true };
    if (event === "click-out") return { state: "section_idle", changed: true };
    else return { state: "section_selected_hovered_other", changed: false };
  }
}

export class ShelfContext {
  _lightingLayer: LightingLayer;
  _interactionLayer: InteractionLayer;
  _sectionsState: Map<string, SectionState>;

  constructor(offScreenSetupState: boolean = true) {
    this._interactionLayer = "shelf_closed_dimensions_hidden";
    this._lightingLayer = 'dayLight';
    this._sectionsState = new Map();
    if (offScreenSetupState) this.updateSectionsState();
    this.setupLightingLayer(offScreenSetupState);
  }

  getInteractionLayer = () => this._interactionLayer;

  changeInteractionLayer(interactionLayer: InteractionLayer) {
    this._interactionLayer = interactionLayer;
    this.reloadSectionsState();
  }

  setupLightingLayer(instant: boolean = false) {
    // TO FIX: temporary disabled coz poor quality and functionality not clear to user

    // const shelfFormat = Space.getShelfRenderFormat();
    // const lightingSchema = shelfFormat.schema.find((schema) => schema.key === "lighting");
    // if (lightingSchema && lightingSchema.children.length > 0) {
    //   this._lightingLayer = 'eveningLight';
    //   Animation.setEveningLight(instant);
    // }
    // else {
    //   this._lightingLayer = 'dayLight';
    //   Animation.setDayLight(instant);
    // }
  }

  forceUpdateSectionStateToIdle() {
    const shelfFormat = Space.getShelfRenderFormat();
    shelfFormat.schema
      .find((schema) => schema.key === "hovers")!.children
      .map((section) => section.segmentTag.sectionId)
      .forEach((sectionId) => {
        if (!this._sectionsState.has(sectionId)) this._sectionsState.set(sectionId, "section_idle");
        sectionInstantBehaviourMap[this._interactionLayer]["section_idle"](sectionId);
      });
  }

  updateSectionsState() {
    const shelfFormat = Space.getShelfRenderFormat();
    shelfFormat.schema
      .find((schema) => schema.key === "hovers")!.children
      .map((section) => section.segmentTag.segmentId)
      .forEach((sectionId) => {
        if (!this._sectionsState.has(sectionId)) this._sectionsState.set(sectionId, "section_idle")
        sectionInstantBehaviourMap[this._interactionLayer][<SectionState>this._sectionsState.get(sectionId)](sectionId);
      });
  }

  reloadSectionsState() {
    this._sectionsState.forEach((_, sectionId) => {
      this.transitionPlay(sectionId);
    });
  }

  resetSectionsState() {
    this._sectionsState.forEach((_, sectionId) => {
      const needToTransition = this._sectionsState.get(sectionId) !== "section_idle";
      this._sectionsState.set(sectionId, "section_idle");
      if (needToTransition) this.transitionPlay(sectionId);
    });
  }

  setSectionState(sectionId: string, state: SectionState) {
    this._sectionsState.set(sectionId, state);
    this.transitionPlay(sectionId);
  }

  sectionStateUpdate(sectionId: string, interactionEvent: SectionEvent) {
    const sectionState = <SectionState>this._sectionsState.get(sectionId);
    const transitionFunc = sectionTransitionMap[sectionState];
    const transition = transitionFunc(interactionEvent);
    if (transition.changed) this.setSectionState(sectionId, transition.state);
  }

  transitionPlay(sectionId: string) {
    const targetState = <SectionState>this._sectionsState.get(sectionId);
    const behaviourFunc = sectionBehaviourMap[this._interactionLayer][targetState];
    behaviourFunc(sectionId);
  }
}



const sectionInstantBehaviourMap = {
  shelf_opened_dimensions_hidden: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
      Animation.ajarDoorsInSectionInstant(sectionId);
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSectionInstant(sectionId);
      Animation.ajarDrawersInSectionInstant(sectionId);
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSectionInstant(sectionId);
      Animation.openDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSectionInstant(sectionId);
      Animation.openDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
      Animation.ajarDoorsInSectionInstant(sectionId);
    },
  },
  shelf_closed_dimensions_hidden: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSectionInstant(sectionId);
      Animation.ajarDrawersInSectionInstant(sectionId);
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSectionInstant(sectionId);
      Animation.openDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSectionInstant(sectionId);
      Animation.openDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
      Animation.ajarDoorsInSectionInstant(sectionId);
    },
  },
  shelf_opened_dimensions_visible: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
  },
  shelf_closed_dimensions_visible: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenClosed');
      Animation.showSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenClosed');
      Animation.showSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSectionInstant(sectionId);
      Animation.closeDrawersInSectionInstant(sectionId);
    },
  }
}

const sectionBehaviourMap = {
  shelf_opened_dimensions_hidden: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.ajarDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.ajarDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.openDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.openDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.ajarDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
  },
  shelf_closed_dimensions_hidden: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.ajarDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.openDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.openDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.openDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.hideDimensions();
      Animation.showSegment("door", sectionId);
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.ajarDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
  },
  shelf_opened_dimensions_visible: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
  },
  shelf_closed_dimensions_visible: {
    section_idle: (sectionId: string) => {
      Animation.unHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenClosed');
      Animation.showSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_unselected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenClosed');
      Animation.showSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered: (sectionId: string) => {
      Animation.fullHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
    section_selected_hovered_other: (sectionId: string) => {
      Animation.halfHighlightSection(sectionId);
      Animation.showDimensions(['exterior', 'interior', 'feature', 'silhouette'], 'whenOpen');
      Animation.hideSegment("door", sectionId);
      Animation.closeDoorsInSection(sectionId, { duration: 0.3, delay: 0 });
      Animation.closeDrawersInSection(sectionId, { duration: 0.3, delay: 0 });
      Frame.update();
    },
  }
}
