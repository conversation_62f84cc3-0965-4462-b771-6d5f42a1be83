export type DoorState = 'opened' | 'ajared' | 'closed' | 'hidden';
export type DrawerState = 'opened' | 'ajared' | 'closed';
export type HoverState = 'fullHighlighted' | 'halfHighlighted' | 'hidden';
export type LightingState = 'visible' | 'hidden';

export class ShelfState {
    private _doors: Map<string, DoorState>;
    private _drawers: Map<string, DrawerState>;
    private _hovers: Map<string, HoverState>;
    private _lighting: Map<string, LightingState>

    constructor() {
        this._doors = new Map();
        this._drawers = new Map();
        this._hovers = new Map();
        this._lighting = new Map();
    }
}