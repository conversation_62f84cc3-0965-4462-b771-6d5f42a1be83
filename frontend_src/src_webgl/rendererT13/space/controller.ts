import {
    DoubleSide,
    Group, Mesh, LineBasicMaterial,
    MeshStandardMaterial, SkinnedMesh,
    Texture, MeshBasicMaterial, LinearEncoding, ColorRepresentation
} from "three";
import { flow } from "lodash-es";
import { Modifier } from "../common/modifiers";
import { GeometryAssets } from "../assets/geometry";
import { getUVWrapper, TextureAssets } from "../assets/texture";
import { ShelfRenderFormat } from "../common/models/formats";
import { DrawableGeometry, DrawableMaterial, DrawableSegment } from "../common/models/geometry";
import { RendererSettings } from "../common/config";
import { BuildStrategy, PaintStrategy } from "./buildStrategy";
import { Interaction } from "../api/interaction";
import { AssetFactory } from "../assets/factory";


export const pickStrategy = (drawable: DrawableGeometry) => {
    const shapeGeometries = [ "pill", "text", "line" ];
    const boundingBoxGeometries = [ "boundingBox", "ambientOcclusionBox", "lightDiffuser", "fakedLightRays" ];
    const extendableLineGeometries = [ "fullHandleRight", "fullHandleLeft", "fullHandleTop" ];
    const solidGeometries = [ "hingeWing", "hingeAnchor", "frontWardrobeBar", "crossWardrobeBar", "maskingProfile", "gripHandle", "lightBar", "shortLeg", "slimHandle" ];
    const stretchableCubeGeometries = [ "horizontalPanel", "verticalPanel", "frontalPanel", "channelSlab", "frontWithGroovedHandle" ];

    if (shapeGeometries.includes(drawable.asset)) return BuildStrategy.shapeGeometry(drawable);
    if (boundingBoxGeometries.includes(drawable.asset)) return BuildStrategy.boundingBoxGeometry(drawable);
    if (extendableLineGeometries.includes(drawable.asset)) return BuildStrategy.extendableLinearGeometry(drawable);
    if (solidGeometries.includes(drawable.asset)) return BuildStrategy.solidGeometry(drawable);
    if (stretchableCubeGeometries.includes(drawable.asset)) return BuildStrategy.stretchableCubeGeometry(drawable);
    return BuildStrategy.solidGeometry(drawable);
}

const _drawableDict: Map<string, Group[]> = new Map();
const _dimensionsCache: Group[] = [];

const isUnique = (group: Group, groups: Group[]) => groups.every(g => g.uuid !== group.uuid);

// TODO: move to GeometryAssets Cache
const hideGeometry = (shelf: Group) => {
    shelf.traverse((child) => {
        // TODO: find better way to identify cached assets
        const isAsset = [ "horizontalPanel", "channelSlab", "verticalPanel", "frontalPanel", "frontWithGroovedHandle",
            "fullHandleRight", "fullHandleLeft", "fullHandleTop",  "hingeAnchor", "hingeWing", "boundingBox", "frontWardrobeBar",
            "crossWardrobeBar", "maskingProfile", "pill", "line", "ambientOcclusionBox", "gripHandle",
            "lightBar", "shortLeg", "lightDiffuser", "fakedLightRays", "slimHandle" ].includes(child.name);

        const isText = child.name.includes("text");

        // TODO: extract this => reset all the transformations to default asset state
        if (isAsset || isText) {

            child.visible = false;
            child.scale.set(1, 1, 1);
            child.rotation.set(0, 0, 0);
            child.position.set(0, 0, 0);

            if (child.name === "boundingBox") {
                child.userData['hoverIsActive'] = false;
                child.traverse((boxMesh: any) => {
                    if (boxMesh.isMesh) {
                        boxMesh.material.opacity = 0;
                        boxMesh.material.transparent = true;
                        if (isText) {
                            boxMesh.material.dispose();
                            boxMesh.geometry.dispose();
                        }
                    }
                })
            }
            // const name = `${geometry.asset}-${geometry.material?.name}`
            if (!_drawableDict.has(child.name)) _drawableDict.set(child.name, [ child as Group ]);
            else if (isUnique(child as Group, _drawableDict.get(child.name)!)) _drawableDict.get(child.name)!.push(child as Group);
        }
    })


    _dimensionsCache.forEach((textGeometryGroup) => {
        const segmentParent = textGeometryGroup.parent;
        if (segmentParent) segmentParent.remove(textGeometryGroup);
    });
    _dimensionsCache.length = 0;
}

const resolveGeometry = (geometry: DrawableGeometry) => {
    const buildStrategy = pickStrategy(geometry);
    const isCached = _drawableDict.has(geometry.asset) && _drawableDict.get(geometry.asset)!.length > 0;
    const isHover = geometry.asset === "boundingBox";
    const isPill = geometry.asset === "pill";
    const isText = geometry.asset === "text";
    const isLine = geometry.asset === "line";
    const isAOBox = geometry.asset === "ambientOcclusionBox";
    const isLightDiffuser = geometry.asset === "lightDiffuser";
    const isFakedLightRays = geometry.asset === "fakedLightRays";

    // if geometry is cached, use it, otherwise create new one
    // TODO: fix factory for new geometries
    const groupGeometry = isCached
        ? _drawableDict.get(geometry.asset)!.pop()!
        : isHover ? AssetFactory.basicModel()
        : isPill ? AssetFactory.basicPillGeometry()
        : isText ? AssetFactory.basicTextGeometry(geometry.groupTag, _dimensionsCache)
        : isLine ? AssetFactory.basicLine()
        : GeometryAssets.Pool.acquire(geometry.asset);

    const isCubeBased = [ "horizontalPanel", "channelSlab", "verticalPanel", "frontalPanel" ].includes(geometry.asset);

    groupGeometry.traverse((child) => {
        if ((child instanceof Mesh || child instanceof SkinnedMesh) && !isCubeBased && !isHover && !isPill && !isText && !isLine && !isAOBox && !isLightDiffuser && !isFakedLightRays) {
            updateMaterial(geometry.material!, 'none', child.material)
        }
        if ((child instanceof Mesh || child instanceof SkinnedMesh) && isCubeBased) {
            if (child.name === "top" || child.name === "bottom")
                updateMaterial(geometry.material!, 'plane', child.material)
            if (child.name === "edge")
                updateMaterial(geometry.material!, 'edge', child.material)
        }
        if ((child instanceof Mesh || child instanceof SkinnedMesh) && isPill) {
            updateBasicMaterial(geometry.material!, child.material)
        }
        if ((child instanceof Mesh || child instanceof SkinnedMesh) && isAOBox) {
            updateAOMaterial(geometry.material!, child.material)
        }
        if ((child instanceof Mesh || child instanceof SkinnedMesh) && (isLightDiffuser || isFakedLightRays)) {
            updateEmissiveMaterial(geometry.material!, child.material )
        }
    })

    return buildStrategy(groupGeometry)
}

const resolveSegment = (segment: DrawableSegment, group: Group) => {
    const segmentGroup = flow(
        Modifier.name(segment.name),
        Modifier.push(segment.geometries.map(resolveGeometry)),
        Modifier.addTagInfo(segment.segmentTag),
        Modifier.move(segment.position),
    )(group);
    return segmentGroup;
}

export const createShelfDataStructure = (renderFormat: ShelfRenderFormat) => flow(
    Modifier.name(renderFormat.name),
    Modifier.push(renderFormat.schema.map(config =>
        Modifier.name(config.key)(new Group()))),
    Modifier.move(renderFormat.position),
)(new Group());

export class ShelfController {
    private _shelfRef: any = new Group();

    buildShelf = (renderFormat: ShelfRenderFormat) => {
        this._shelfRef = createShelfDataStructure(renderFormat);
        return this._shelfRef as Group;
    }

    updateShelf = (renderFormat: ShelfRenderFormat) => {
        hideGeometry(this._shelfRef);
        renderFormat.schema.forEach((config) => {
            const segmentGroup = this._shelfRef.getObjectByName(config.key) as Group;
            const hideDimensions = Interaction.isDimensionHidden() && config.key === "dimensions";

            const drawableSegments = hideDimensions ? [] : config.children;

            drawableSegments.forEach((drawableSegment: DrawableSegment, idx: number) => {
                if (segmentGroup.children[idx]) resolveSegment(drawableSegment, segmentGroup.children[idx] as Group);
                else segmentGroup.add(resolveSegment(drawableSegment, new Group()) as Group);
            });
        })
    }

    getEnvironmentMap = () => {
        return TextureAssets.Repository.getEnvironmentMap();
    }
}

export const updateAOMaterial = (drawable: DrawableMaterial, material: MeshBasicMaterial) => {
    return PaintStrategy.basicMaterial({
        name: drawable.name,
        albedo: { color: RendererSettings.getColorsConfig()["shadow"], map: null },
        opacity: { value: RendererSettings.getMaterialsConfig().AMBIENT_OCCLUSION.opacity.value, map: buildSimpleTexture('innerAOMap', material?.alphaMap || new Texture()) },
        faceSide: { value: 'front' },
        depth: { write: false },
    })(material);
}

export const updateEmissiveMaterial = (drawable: DrawableMaterial, material: MeshStandardMaterial) => {
    return PaintStrategy.physicalMaterial({
        name: drawable.name,
        albedo: { color: RendererSettings.getColorsConfig()[drawable.colors!.primary], map: null },
        roughness: { value: 0, map: null },
        metalness: { value: 0, map: null },
        opacity: {
            value: RendererSettings.getMaterialsConfig().FAKED_LIGHT_RAYS.opacityForColor[drawable.colors!.primary],
            map: buildSimpleTexture('fake_rays', material?.alphaMap || new Texture()) },
        bump: { value: 0, map: null },
        normal: { scale: { x: 0, y: 0 }, map: null },
        environment: { value: 0, map: null },
        faceSide: { value: 'front' },
        depth: { write: true },
        emissive: {
            value: RendererSettings.getMaterialsConfig().FAKED_LIGHT_RAYS.emissive.value,
            color: RendererSettings.getColorsConfig()[drawable.colors!.primary],
            map: null
        },
    })(material);
}

export const updateBasicMaterial = (drawable: DrawableMaterial, material: MeshBasicMaterial) => {
    return PaintStrategy.basicMaterial({
        name: drawable.name,
        albedo: { color: RendererSettings.getColorsConfig()[drawable.colors!.primary], map: null },
        opacity: { value: 0.7, map: null },
        faceSide: { value: 'double' },
        depth: { write: true },
    })(material);
}

const materialMapSettings = {
    chrome: RendererSettings.getMaterialsConfig().CHROME,
    paintedMetal: RendererSettings.getMaterialsConfig().PAINTED_METAL,
    laminate: RendererSettings.getMaterialsConfig().LAMINATE,
    laminatedEdge: RendererSettings.getMaterialsConfig().LAMINATE,
    laminatedPlywood: RendererSettings.getMaterialsConfig().LAMINATE,
    lacquer: RendererSettings.getMaterialsConfig().LACQUER,
    plywoodEdge: RendererSettings.getMaterialsConfig().PLYWOOD_EDGE,
    plywood: RendererSettings.getMaterialsConfig().PLYWOOD,
    veneer: RendererSettings.getMaterialsConfig().VENEER,
}
// TODO: add adapter from drawable material to Texture Settings and then Material Settings
// Single, Double, Triple Materials options
export type UVMode = 'plane' | 'edge'
export const updateMaterial = (drawable: DrawableMaterial, mode: UVMode | 'none', material: MeshStandardMaterial) => {
    const isPlywoodEdge = (drawable.name === 'laminatedPlywood' && mode === 'edge') || (drawable.name === 'plywood' && mode === 'edge');
    const isVeneerEdge = (drawable.name === 'laminatedVeneer' && mode === 'edge');
    const isLaminate = (drawable.name === 'laminatedVeneer' && mode === 'plane');

    const materialName = ['laminatedPlywood', 'laminatedEdge'].includes(drawable.name)
        ? `${drawable.name}_${mode}`
        : drawable.name;

    const materialKey =
        isPlywoodEdge ? 'plywoodEdge':
        isVeneerEdge ? 'veneer':
        isLaminate ? 'laminate':
        drawable.name;

    const materialSettings = (materialMapSettings as any)[materialKey];

    const texturesPack = buildTexturesPack(drawable, mode, material);

    const colorId: string = mode === 'edge'
        ? drawable!.colors!.secondary
        : drawable!.colors!.primary;

    return PaintStrategy.physicalMaterial({
        name: materialName,
        albedo: { color: RendererSettings.getColorsConfig()[colorId], map: texturesPack.albedo },
        roughness: { value: materialSettings.roughness.value, map: texturesPack.roughness },
        metalness: { value: materialSettings.metalness.value, map: texturesPack.metalness },
        opacity: { value: 1, map: null },
        bump: { value: materialSettings.bump.value, map: texturesPack.bump },
        normal: { scale: materialSettings.normal.scale, map: texturesPack.normal },
        environment: { value: materialSettings.environment.value, map: texturesPack.environment },
        faceSide: { value: 'front' },
        depth: { write: true },
        emissive: { value: 0, color: RendererSettings.getColorsConfig()[drawable.colors!.primary], map: null },
    })(material);
}

const buildTexturesPack = (drawable: DrawableMaterial, uvMode: UVMode | 'none', material?: MeshStandardMaterial) => {
    const isPlywoodEdge = (drawable.name === 'laminatedPlywood' && uvMode === 'edge') || (drawable.name === 'plywood' && uvMode === 'edge');
    const isVeneerEdge = (drawable.name === 'laminatedVeneer' && uvMode === 'edge');
    const isLaminate = (drawable.name === 'laminatedVeneer' && uvMode === 'plane');
    const isVeneerFront = drawable.name === 'veneer' && uvMode === 'none';

    const materialKey =
        isPlywoodEdge ? 'plywoodEdge':
        isVeneerEdge ? 'veneer':
        isLaminate ? 'laminate':
        drawable.name;

    const settingsForMaterialId = (materialMapSettings as any)[materialKey];

    if (isVeneerFront) uvMode = 'plane';
    const uvCords = getUVWrapper(drawable.mapRatio as any, uvMode === 'plane' ? 0.9 : 0.05, uvMode);
    uvCords.rotation = Math.PI * drawable.mapAngle / 180;

    return {
        albedo: settingsForMaterialId.albedo
            ? buildTexture(settingsForMaterialId.albedo.map, uvCords,material?.map || new Texture())
            : null,
        normal: settingsForMaterialId.normal
            ? buildTexture(settingsForMaterialId.normal.map, uvCords,material?.normalMap || new Texture())
            : null,
        bump: settingsForMaterialId.bump
            ? buildTexture(settingsForMaterialId.bump.map, uvCords,material?.bumpMap || new Texture())
            : null,
        metalness: settingsForMaterialId.metalness
            ? buildTexture(settingsForMaterialId.metalness.map, uvCords, material?.metalnessMap || new Texture())
            : null,
        roughness: settingsForMaterialId.roughness
            ? buildTexture(settingsForMaterialId.roughness.map, uvCords, material?.roughnessMap || new Texture())
            : null,
        environment: settingsForMaterialId.environment !== null
            ? TextureAssets.Repository.getEnvironmentMap()
            : null,
    }
}

const buildTexture = (name: string | null, uvCords: any, texture: Texture) => {
    if (name === null) return null;
    return flow(
        Modifier.assignImageTexture(TextureAssets.Repository.getImageSourceByName(name)),
        Modifier.unwrapUVTexture(uvCords)
    )(texture)
}

const buildSimpleTexture = (name: string, texture: Texture) => {
    return flow(
        Modifier.assignImageTexture(TextureAssets.Repository.getImageSourceByName(name)),
    )(texture)
}
