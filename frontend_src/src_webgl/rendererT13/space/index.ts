import {Scene, Group, Light, DirectionalLight, Mesh, Color, MeshStandardMaterial, Material} from 'three';
import { ShelfController } from './controller';
import { createLightProbe } from './lights-builder/index';
import { createInterior, updateInterior, createShadowMan, updateShadowMan } from "./interior-builder/index";
import { TextureAssets } from "../assets/texture";
import { SpaceRenderFormat } from '../common/models/formats';
import { RendererSettings } from "../common/config";
import {resolveItem} from "../items/controller";

export namespace Space {
  const _scene: Scene = new Scene();
  const _shelfController = new ShelfController();
  let _spaceFormat: SpaceRenderFormat;
  let _shelf: Group = new Group();
  let _room: Group = new Group();
  let _shadowMan: Group = new Group();
  let _lights: Group = new Group();

  export const setRenderFormat = (spaceRenderFormat: SpaceRenderFormat) => {
    _spaceFormat = spaceRenderFormat;
  }
  export const getScene = () => _scene;
  export const getSpaceFormat = () => _spaceFormat;
  export const getShelfRenderFormat = () => _spaceFormat.shelves[0];
  export const getShelf = () => _shelf;
  export const getLights = () => _lights;

  export namespace Interior {
    export const compose = () => {
      setupInterior();
    }

    export const update = () => {
      const shelfRenderFormat = _spaceFormat.shelves[0];
      updateInterior(shelfRenderFormat.size, _room);
      updateShadowMan(shelfRenderFormat.size, _shadowMan);
    }

    export const updateBackground = () => {
      const wallAndFloor = _room.getObjectByName('wallAndFloor') as Mesh;
      if (wallAndFloor) {
        (wallAndFloor.material as MeshStandardMaterial).color = new Color(RendererSettings.getBackgroundConfig().color).convertSRGBToLinear();
        (wallAndFloor.material as MeshStandardMaterial).aoMapIntensity = RendererSettings.getBackgroundConfig().aoMapIntensity;
        (wallAndFloor.material as MeshStandardMaterial).envMapIntensity = RendererSettings.getBackgroundConfig().envMapIntensity;
        (wallAndFloor.material as MeshStandardMaterial).lightMapIntensity = RendererSettings.getBackgroundConfig().lightMapIntensity;
        (wallAndFloor.material as MeshStandardMaterial).opacity = RendererSettings.getBackgroundConfig().opacity;
        (wallAndFloor.material as MeshStandardMaterial).roughness = RendererSettings.getBackgroundConfig().roughness;
        (wallAndFloor.material as MeshStandardMaterial).metalness = RendererSettings.getBackgroundConfig().metalness;
      }
    }

    export const updateLights = () => {
      const lightsKey = Object.keys(RendererSettings.getLightsConfig());
      lightsKey.forEach((key) => {
        const light = _lights.getObjectByName(key) as Light;
        if (light) {
          const config = RendererSettings.getLightsConfig()[key];
          if (config.position) light.position.set(config.position.x, config.position.y, config.position.z);
          if (config.intensity) light.intensity = config.intensity;
          if (config.color) light.color.setHex(config.color);
        }
      });
    }

    export const updateShadow = () => {
      const shadowConfig = RendererSettings.getShadowConfig();
      const shadowPropertyKeys = Object.keys(shadowConfig);
      const light = _lights.getObjectByName('primary') as DirectionalLight;
      if (light) {
        shadowPropertyKeys.forEach((key) => {
            if (key === 'map') {
              light.shadow.bias = shadowConfig[key].bias;
              light.shadow.needsUpdate = true;
            } else if (key === 'camera') {
              light.shadow.camera.near = shadowConfig[key].near;
              light.shadow.camera.far = shadowConfig[key].far;
              light.shadow.camera.right = shadowConfig[key].right;
              light.shadow.camera.left = shadowConfig[key].left;
              light.shadow.camera.top = shadowConfig[key].top;
              light.shadow.camera.bottom = shadowConfig[key].bottom;
              light.shadow.camera.updateProjectionMatrix();
            }
        });
      }
    }

    export const toggleVisibility = (visible: boolean) => {
      _scene.traverse((child) => {
        if (child.name === 'room') { child.visible = visible };
        if (child.name === 'typeK') { child.visible = visible };
      });
    }
  }

  export namespace Shelf {
    export const compose = () => {
      const shelfRenderFormat = _spaceFormat.shelves[0];
      _shelf.add(_shelfController.buildShelf(shelfRenderFormat));
      _scene.add(_shelf);
    }

    export const update = () => {
      const shelfRenderFormat = _spaceFormat.shelves[0];
      _shelfController.updateShelf(shelfRenderFormat);
    }
  }

  export namespace Items {
    export const _items = new Group();

    export const compose = () => {
      _items.name = 'items';
      _scene.add(_items);
    }

    export const update = () => {
      const itemsRenderFormat = _spaceFormat.items[0];
      resolveItem(itemsRenderFormat, _items)
    }

  }

  const setupInterior = () => {
    const shelfRenderFormat = _spaceFormat.shelves[0];
    const cubeMap = TextureAssets.Repository.getEnvironmentMap();
    const { lightProbe, directionalLight, secondaryLight } = createLightProbe(cubeMap);
    _room = createInterior(cubeMap, shelfRenderFormat.size);
    _shadowMan = createShadowMan(shelfRenderFormat.size);
    _lights.add(lightProbe, directionalLight, secondaryLight);

    _scene.add(_lights);
    _scene.add(_room);
    _scene.add(_shadowMan);
  }
}