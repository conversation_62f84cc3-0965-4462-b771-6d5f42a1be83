import {
    <PERSON>sh,
    Skinned<PERSON>esh,
    MeshBasicMaterial,
    LinearEncoding,
    Color,
    CubeTexture,
    Texture,
    Group,
    MeshStandardMaterial
} from "three";
import { TextureAssets } from "../../assets/texture";
import { GeometryAssets } from "../../assets/geometry";
import { Modifier } from "../../common/modifiers";
import { VectorXYZ } from '../../common/models/geometry';
import { RendererSettings } from "../../common/config";
import { flow } from "lodash-es";

export const createShadowMan = (shelfSize: VectorXYZ): Group => {
    const typeK = flow(
        Modifier.name('typeK'),
        Modifier.scale({ x: 0.01, y: 0.01, z: 0.01 }),
        Modifier.move({ x: -(shelfSize.x/2 + 0.66), y: 0, z: 0.03 }),
        Modifier.updateShadowSettings({ castShadow: false, receiveShadow: false }),
        Modifier.toggleVisibility(true),
    )(GeometryAssets.Pool.acquire('shadowMan'));

    typeK.traverse((child) => {
        if (child instanceof Mesh || child instanceof SkinnedMesh) {
            child.material = new MeshStandardMaterial();
            child.material.color = new Color(0xFFFFFF);
            child.material.emissive = new Color(0xFFFFFF);
            child.material.emissiveIntensity = 1;
        }
    })

    return typeK as Group;
}

export const createInterior = (map: CubeTexture, shelfSize: VectorXYZ): Group => {
    const bg = flow(
        Modifier.name('room'),
        Modifier.scale(shelfSize),
        Modifier.updateShadowSettings({ castShadow: false, receiveShadow: true }),
        Modifier.toggleVisibility(true),
    )(GeometryAssets.Pool.acquire('background'));

    const bgAlpha = flow(
        Modifier.assignImageTexture(TextureAssets.Repository.getImageSourceByName('bg_alpha')),
    )(new Texture());

    const bgShadow = flow(
        Modifier.assignImageTexture(TextureAssets.Repository.getImageSourceByName('bg_shadow')),
    )(new Texture());

    const bgAmbientOcclusion = flow(
        Modifier.assignImageTexture(TextureAssets.Repository.getImageSourceByName('bg_ao')),
    )(new Texture());

    bg.traverse((child) => {
        if (child instanceof Mesh || child instanceof SkinnedMesh) {
            child.material.color = new Color(RendererSettings.getBackgroundConfig().color).convertSRGBToLinear();
            child.material.map = bgShadow;
            child.material.alphaMap = bgAlpha;
            child.material.aoMap = bgAmbientOcclusion;
            child.material.aoMapIntensity = RendererSettings.getBackgroundConfig().aoMapIntensity;
            child.material.lightMap = bgAmbientOcclusion;
            child.material.lightMapIntensity = RendererSettings.getBackgroundConfig().lightMapIntensity;
            child.material.transparent = true;
            child.material.opacity = RendererSettings.getBackgroundConfig().opacity;
            child.material.roughness = RendererSettings.getBackgroundConfig().roughness;
            child.material.metalness = RendererSettings.getBackgroundConfig().metalness;
            child.material.envMap = map;
            child.material.envMapIntensity = RendererSettings.getBackgroundConfig().envMapIntensity;
            child.name = 'wallAndFloor';
        }
    })
    return bg as Group;
}

export const updateInterior = (shelfSize: VectorXYZ, bg: Group): Group =>
    flow( Modifier.scale(shelfSize) )(bg)

export const updateShadowMan = (shelfSize: VectorXYZ, shadowMan: Group): Group =>
    flow( Modifier.move({ x: -(shelfSize.x/2 + 0.66), y: 0, z: 0.03 }), )(shadowMan)
    