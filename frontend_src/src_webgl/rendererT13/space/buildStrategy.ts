import { Modifier } from "../common/modifiers";
import { DrawableGeometry } from "../common/models/geometry";
import {BasicMaterialSettings, PhysicalMaterialSettings} from "../common/models/material";
import { flow } from "lodash-es";

import { Group, Mesh } from "three";

export type Composable = {
  name: string;
  children: (Group | Mesh)[];
  tag: Record<string, any>;
  position: { x: number; y: number; z: number };
}

export namespace ComposeStrategy {
  export const segmentGroup = (composable: Composable) => flow(
    Modifier.name(composable.name),
    Modifier.push(composable.children),
    Modifier.addTagInfo(composable.tag),
    Modifier.move(composable.position),
  )
}

export namespace BuildStrategy {
  export const shapeGeometry = (drawable: DrawableGeometry) => flow(
    Modifier.name(drawable.asset === "text" ? `text|${drawable.groupTag}` : drawable.asset),
    Modifier.scale(drawable.size),
    Modifier.rotate(drawable.rotation),
    Modifier.move(drawable.position),
    Modifier.toggleVisibility(true),
  )

  export const boundingBoxGeometry = (drawable: DrawableGeometry) => flow(
    Modifier.name(drawable.asset),
    Modifier.addTagInfo({ id: drawable.groupTag, hoverIsActive: true }),
    Modifier.scale(drawable.size),
    Modifier.mirror(drawable.mirror),
    Modifier.rotate(drawable.rotation),
    Modifier.move(drawable.position),
    Modifier.toggleVisibility(true),
  )

  export const stretchableCubeGeometry = (drawable: DrawableGeometry) => flow(
    Modifier.name(drawable.asset),
    Modifier.addTagInfo({ groupTag: drawable.groupTag }),
    Modifier.stretchCubeBones(drawable.size),
    Modifier.rotate(drawable.rotation),
    Modifier.move(drawable.position),
    Modifier.updateShadowSettings({ castShadow: true, receiveShadow: true }),
    Modifier.toggleVisibility(true),
  )

  export const extendableLinearGeometry = (drawable: DrawableGeometry) => flow(
    Modifier.name(drawable.asset),
    Modifier.addTagInfo({ groupTag: drawable.groupTag }),
    Modifier.extendLineBones(drawable.size),
    Modifier.mirror(drawable.mirror),
    Modifier.rotate(drawable.rotation),
    Modifier.move(drawable.position),
    Modifier.updateShadowSettings({ castShadow: true, receiveShadow: true }),
    Modifier.toggleVisibility(true),
  )

  export const solidGeometry = (drawable: DrawableGeometry) => flow(
    Modifier.name(drawable.asset),
    Modifier.addTagInfo({ groupTag: drawable.groupTag }),
    Modifier.mirror(drawable.mirror),
    Modifier.rotate(drawable.rotation),
    Modifier.scale(drawable.size),
    Modifier.move(drawable.position),
    Modifier.updateShadowSettings({ castShadow: true, receiveShadow: true }),
    Modifier.toggleVisibility(true),
  )
}

export namespace PaintStrategy {
  export const physicalMaterial = (material: PhysicalMaterialSettings) => flow(
    Modifier.setNameMaterialProperties(material.name),
    Modifier.setAlbedoMaterialProperties(material.albedo.color, material.albedo.map),
    Modifier.setRoughnessMaterialProperties(material.roughness.value, material.roughness.map),
    Modifier.setMetalnessMaterialProperties(material.metalness.value, material.metalness.map),
    Modifier.setBumpMaterialProperties(material.bump.value, material.bump.map),
    Modifier.setNormalMaterialProperties(material.normal.scale, material.normal.map),
    Modifier.setEnvironmentMaterialProperties(material.environment.value, material.environment.map),
    Modifier.setEmissiveMaterialProperties(material.emissive.value, material.emissive.color, material.emissive.map),
    Modifier.setOpacityMaterialProperties(material.opacity.value, material.opacity.map),
  )

  export const basicMaterial = (material: BasicMaterialSettings) => flow(
    Modifier.setNameMaterialProperties(material.name),
    Modifier.setAlbedoMaterialProperties(material.albedo.color, material.albedo.map),
    Modifier.setOpacityMaterialProperties(material.opacity.value, material.opacity.map),
    Modifier.setSideMaterialProperties(material.faceSide.value),
    Modifier.setDepthMaterialProperties(material.depth.write),
  )
}

export namespace UnwrapStrategy {
  export const imageBasedTexture = (texture: any) => flow(
    Modifier.assignImageTexture(texture.imageSource),
    Modifier.repeatTexture(texture.repeat),
    Modifier.offsetTexture(texture.offset),
    Modifier.rotateTexture(texture.rotation),
  )
}
