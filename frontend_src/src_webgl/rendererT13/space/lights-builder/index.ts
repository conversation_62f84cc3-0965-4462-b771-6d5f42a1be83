import { CubeTexture, DirectionalLight, LightProbe } from "three";
import { LightProbeGenerator } from 'three/examples/jsm/lights/LightProbeGenerator.js';
import { RendererSettings } from "../../common/config";

export const createLightProbe = (cubeTexture: CubeTexture) => {
    const lightProbe = new LightProbe();
    lightProbe.copy( LightProbeGenerator.fromCubeTexture( cubeTexture ) );
    lightProbe.intensity = RendererSettings.getLightsConfig()['fill'].intensity;
    lightProbe.name = 'fill';

    const secondaryLight = new DirectionalLight(
        RendererSettings.getLightsConfig()['secondary'].color,
        RendererSettings.getLightsConfig()['secondary'].intensity
    );
    secondaryLight.position.setX(RendererSettings.getLightsConfig()['secondary'].position.x);
    secondaryLight.position.setY(RendererSettings.getLightsConfig()['secondary'].position.y);
    secondaryLight.position.setZ(RendererSettings.getLightsConfig()['secondary'].position.z);
    secondaryLight.castShadow = false;
    secondaryLight.name = 'secondary';

    const directionalLight = new DirectionalLight(
        RendererSettings.getLightsConfig()['primary'].color,
        RendererSettings.getLightsConfig()['primary'].intensity
    );
    directionalLight.position.setX(RendererSettings.getLightsConfig()['primary'].position.x);
    directionalLight.position.setY(RendererSettings.getLightsConfig()['primary'].position.y);
    directionalLight.position.setZ(RendererSettings.getLightsConfig()['primary'].position.z);
    directionalLight.castShadow = true;
    directionalLight.shadow.bias = RendererSettings.getShadowConfig()['map'].bias;
    directionalLight.shadow.camera.near = RendererSettings.getShadowConfig()['camera'].near;
    directionalLight.shadow.camera.far = RendererSettings.getShadowConfig()['camera'].far;
    directionalLight.shadow.camera.right = RendererSettings.getShadowConfig()['camera'].right;
    directionalLight.shadow.camera.left = RendererSettings.getShadowConfig()['camera'].left;
    directionalLight.shadow.camera.top = RendererSettings.getShadowConfig()['camera'].top
    directionalLight.shadow.camera.bottom = RendererSettings.getShadowConfig()['camera'].bottom;
    directionalLight.shadow.mapSize.width = RendererSettings.getShadowConfig()['map'].size;
    directionalLight.shadow.mapSize.height = RendererSettings.getShadowConfig()['map'].size;
    directionalLight.name = 'primary';

    return { lightProbe, directionalLight, secondaryLight };
}