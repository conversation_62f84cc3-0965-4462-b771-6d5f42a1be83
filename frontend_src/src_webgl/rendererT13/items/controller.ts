import { CubeTexture, Group, Mesh } from "three";
import { createGLTFMesh } from "./loader";
import { DrawableGeometry } from "../common/models/geometry";
import { TextureAssets } from "../assets/texture";
import { ItemsRenderFormat } from "../common/models/formats";

const cacheContainers: Record<string, Group> = {};
const cacheModels: Record<string, Promise<Mesh>> = {};
export const createItem = (id: string, position: {x: number, y: number, z: number}, size: {x: number, y: number, z: number}, env: CubeTexture) => {
    let itemContainer: Group | null;
    let model: Promise<Mesh> | null;

    if (!cacheContainers[id]) {
        itemContainer = new Group();
        model = createGLTFMesh(id, itemContainer, env);
        cacheModels[id] = model;
        cacheContainers[id] = itemContainer;
    } else {
        itemContainer = cacheContainers[id];
        model = cacheModels[id];
    }

    itemContainer.position.z = position.z;
    itemContainer.position.x = position.x;
    itemContainer.position.y = position.y;

    itemContainer.scale.z = size.z;
    itemContainer.scale.x = size.x;
    itemContainer.scale.y = size.y;

    return { container: itemContainer, modelPromise: model };
}

export const createItemFromModel = (drawable: DrawableGeometry, itemsGroup: Group) => {
    const env = TextureAssets.Repository.getEnvironmentMap()
    const item = createItem(drawable.asset, drawable.position, drawable.size, env);
    itemsGroup.add(item.container);
}

export const flushItems = (group: Group) => {
    let len = group.children.length - 1;
    for (let i = len; i >= 0; i--) {
        let obj = group.children[i];
        group.remove(obj);
    }
}

export const resolveItem = async (itemsSRF: ItemsRenderFormat, ItemsGroup: Group) => {
    flushItems(ItemsGroup);

    for (const segment of itemsSRF.children) {
        ItemsGroup.position.x = segment.position.x;
        ItemsGroup.position.y = segment.position.y;
        ItemsGroup.position.z = segment.position.z;

        for (const drawable of segment.geometries) {
            createItemFromModel(drawable, ItemsGroup);
        }
    }
}