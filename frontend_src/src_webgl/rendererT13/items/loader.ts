import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader.js";
import { KTX2Loader } from "three/examples/jsm/loaders/KTX2Loader.js";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import {
    CubeTexture,
    DoubleSide,
    Group,
    LoadingManager, Mesh,
    MeshBasicMaterial,
    MeshPhysicalMaterial,
} from "three";
import {ASSETS_SOURCE} from "../common/config";
import {LinearToSRGB} from "three/src/math/ColorManagement";

let loaderService: GLTFLoader | null = null;
let loadingManager: LoadingManager | null = null;
let basisLoader: KTX2Loader | null = null;
let dracoLoader: DRACOLoader | null = null;

export const getLoaderService = (): Promise<GLTFLoader> => {
    return new Promise(resolve => {
        if (!dracoLoader) {
            dracoLoader = new DRACOLoader();
            dracoLoader.setDecoderPath(`${ASSETS_SOURCE}/items/vendor/draco/`);
        }

        if (!basisLoader) {
            basisLoader = new KTX2Loader();
            basisLoader.setTranscoderPath(`${ASSETS_SOURCE}/items/vendor/basisu/transcoder/`);
        }

        if (!loadingManager) {
            loadingManager = new LoadingManager();
            loadingManager.addHandler(/\.basis$/, basisLoader);
        }

        loaderService = new GLTFLoader(loadingManager);
        loaderService.setDRACOLoader(dracoLoader);
        loaderService.setKTX2Loader(basisLoader);

        resolve(loaderService);
    });
};

export const GLTFModel = async (url: string, itemContainer: Group, env: CubeTexture) => {
    return new Promise(async done => {
        let loader = loaderService ? loaderService : await getLoaderService();
        loader.load(url, modelInThreeJS => {
            modelInThreeJS.scene.traverse(node => modifyMaterials(node, env));

            itemContainer.add(modelInThreeJS.scene);

            done(modelInThreeJS.scene);
        });
    });
}

export const createGLTFMesh = async (id: string, itemContainer: Group, env: CubeTexture) => {
    return GLTFModel(`${ASSETS_SOURCE}/items/web/${id}.glb`, itemContainer, env) as Promise<Mesh>
};

const modifyMaterials = (node: any, env: CubeTexture) => {
    if (node.isMesh) {
        let expectBaked = true;
        let { type: materialType, name: materialName } = parseMaterialName(node.material.name);

        node.castShadow = true;
        node.receiveShadow = true;

        switch (materialType) {
            case "live":
                expectBaked = false;
                switch (materialName) {
                    case "lcd":
                        node.material = createMaterial({
                            color: 0x333333,
                            envMap: env,
                            envMapIntensity: 0.175,
                            roughness: 0.275,
                            metalness: 0.1,
                            clearcoat: 1
                        });
                        break;
                    case "metal_dark_grey":
                        node.material = createMaterial({
                            color: 0x888888,
                            envMap: env,
                            envMapIntensity: 0.175,
                            roughness: 0.275,
                            metalness: 0.1,
                            clearcoat: 1
                        });
                        break;
                    case "metal_brushed_matte":
                        node.material = createMaterial({
                            color: 0x888888,
                            envMap: env,
                            envMapIntensity: 0.175,
                            roughness: 0.275,
                            metalness: 0.1,
                            clearcoat: 1
                        });
                        break;
                }
                break;
            case "custom":
                node.material.envMap = env;
                node.material.envMapIntensity = 0.175;
                node.material.needsUpdate = true;
                break;
            default:
                if (expectBaked && node.material && node.material.map) {
                    node.material = new MeshBasicMaterial({ map: node.material.map });
                }
                break;
        }
    }
};

const parseMaterialName = (currentMaterialName: string) => {
    let type, name;
    let materialNameSplit = currentMaterialName.split("-");
    if (materialNameSplit.length > 1) {
        type = materialNameSplit[0];
        name = materialNameSplit[1];
    }
    return { type, name };
};

const createMaterial = (settings: any) => {
    var material = new MeshPhysicalMaterial({
        side: DoubleSide,
        ...settings
    });

    return material;
};