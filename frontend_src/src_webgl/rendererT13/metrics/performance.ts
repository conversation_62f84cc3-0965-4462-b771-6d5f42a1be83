import { Clock } from "three";

type ActionName =
    'fps' | 'renderTime' | 'animationTine' | 'assetsLoadTime' | 'levelBuildTime' | 'shelfStateChange' | 'interactionDispatch';
type Metric = { min: number, max: number, average: number, p90: number };
type PerformanceReport = Record<ActionName, Metric> & { sessionTime: number }

export class PerformanceMonitor {
    private static _instance: PerformanceMonitor;

    private _frameCounter = 0;
    private _frameTimeElapsed = 0;

    private _actionsClocks: Map<ActionName, Clock> = new Map();
    private _sessionClock: Clock;
    private _sessionTime = 0;

    private _cache:  Record<ActionName, number[]> = {
        fps: [],
        renderTime: [],
        animationTine: [],
        assetsLoadTime: [],
        levelBuildTime: [],
        shelfStateChange: [],
        interactionDispatch: [],
    }
    private _metrics: Record<ActionName, number> = {
        fps: 0,
        renderTime: 0,
        animationTine: 0,
        assetsLoadTime: 0,
        levelBuildTime: 0,
        shelfStateChange: 0,
        interactionDispatch: 0,
    };

    private constructor() {
        this._actionsClocks.set('fps', new Clock());
        this._sessionClock = new Clock();
        this.startSession();
    }

    public startSession = (): void => {
        this._sessionClock.start();
    }

    public endSession = (): void => {
        this._sessionClock.stop();
        this._sessionTime = this._sessionClock.getElapsedTime() / 60;
        this._sessionClock = new Clock();
    }

    public registerTick = (): void => {
        this._frameCounter++;
        this._frameTimeElapsed += this._actionsClocks.get('fps')!.getDelta();

        if (this._frameTimeElapsed >= 1) {
            this._metrics.fps = this._frameCounter;
            this._cache.fps.push(this._frameCounter);
            this._frameCounter = 0;
            this._frameTimeElapsed = 0;
        }
    }

    public registerActionStart = (actionName: ActionName): void => {
        const actionClock = new Clock();
        this._actionsClocks.set(actionName, actionClock);
        actionClock.start();
    }

    public registerActionEnd = (actionName: ActionName): void => {
        const actionClock = this._actionsClocks.get(actionName);
        if (actionClock) {
            this._metrics[actionName] = actionClock.getElapsedTime() * 1000;
            this._cache[actionName].push(actionClock.getElapsedTime() * 1000);
            actionClock.stop();
        }
    }

    public getMetrics = (): Record<ActionName, number> => this._metrics;

    public getAnalyzedSession = (): PerformanceReport => {
        this.endSession();

        const analyzedSession: PerformanceReport = {
            fps: { min: 0, max: 0, average: 0, p90: 0 },
            renderTime: { min: 0, max: 0, average: 0, p90: 0 },
            animationTine: { min: 0, max: 0, average: 0, p90: 0 },
            assetsLoadTime: { min: 0, max: 0, average: 0, p90: 0 },
            levelBuildTime: { min: 0, max: 0, average: 0, p90: 0 },
            shelfStateChange: { min: 0, max: 0, average: 0, p90: 0 },
            interactionDispatch: { min: 0, max: 0, average: 0, p90: 0 },
            sessionTime: this._sessionTime,
        };

        Object.keys(this._cache).forEach((key: any) => {
            const cache = this._cache[key as ActionName];
            if (cache.length) {
                analyzedSession[key as ActionName] = {
                    min: Math.min(...cache),
                    max: Math.max(...cache),
                    p90: PerformanceMonitor.quantile(cache, 0.9),
                    average: cache.reduce((a, b) => a + b, 0) / cache.length,
                };
            }
        });

        return analyzedSession;
    };

    private static quantile = (values: number[], ratio: number): number => {
        values.sort();
        const position = (values.length - 1) * ratio;
        const base = Math.floor(position);
        const rest = position - base;
        const anyNext = values[base + 1] !== undefined;
        return anyNext
            ? values[base] + rest * (values[base + 1] - values[base])
            : values[base];
    };

    public static getInstance = (): PerformanceMonitor => {
        if (!PerformanceMonitor._instance) PerformanceMonitor._instance = new PerformanceMonitor();
        return PerformanceMonitor._instance;
    }
}