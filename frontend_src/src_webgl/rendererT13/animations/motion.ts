import {Group, Light, MathUtils} from 'three';
import { Frame } from '../api/frame';
import { Power0, TweenLite } from 'gsap';
import { Modifier } from "../common/modifiers";

type MotionSettings = {
  instant: boolean,
  duration: number,
  delay: number,
  target: number,
}

type LightSettings = {
  instant: boolean,
  duration: number,
  delay: number,
  target: {
    intensity: number,
    color: number,
    position: { x: number, y: number, z: number },
  },
}

export const rotateDoorWing = (door: Group, settings: MotionSettings, onCompleteFunc?: () => void) => {
  if (settings.instant) {
    Modifier.rotate({ axis: 'y', angle: MathUtils.degToRad(settings.target) })(door);
    if (onCompleteFunc) onCompleteFunc()
  } else {
    TweenLite.to(door.rotation, settings.duration, {
      ease: <any>Power0.easeNone,
      y: MathUtils.degToRad(settings.target),
      delay: settings.delay,
    })
    .eventCallback('onUpdate', () => { Frame.setUpdateLoopActive() })
    .eventCallback('onComplete', () => { if (onCompleteFunc) onCompleteFunc() });
  }
}

export const slideDrawer = (drawer: Group, settings: MotionSettings, onCompleteFunc?: () => void) => {
  if (settings.instant) {
    Modifier.move({ ...drawer.position, z: settings.target })(drawer);
    if (onCompleteFunc) onCompleteFunc()
  } else {
    TweenLite.to(drawer.position, settings.duration, {
      ease: <any>Power0.easeNone,
      z: settings.target,
      delay: settings.delay,
    })
    .eventCallback('onUpdate', () => { Frame.setUpdateLoopActive() })
    .eventCallback('onComplete', () => { if (onCompleteFunc) onCompleteFunc() });
  }
}

export const transitLight = (light: Light, settings: LightSettings) => {
  if (settings.instant) {
    light.position.set(settings.target.position.x, settings.target.position.y, settings.target.position.z);
    light.intensity = settings.target.intensity;
    light.color.setHex(settings.target.color);
  } else {
    TweenLite.to(light.position, settings.duration, {
      ease: <any>Power0.easeNone,
      x: settings.target.position.x,
      y: settings.target.position.y,
      z: settings.target.position.z,
      delay: settings.delay,
    })
    .eventCallback('onUpdate', () => { Frame.setUpdateLoopActive() })

    TweenLite.to(light, settings.duration, {
      ease: <any>Power0.easeNone,
      intensity: settings.target.intensity,
      delay: settings.delay,
    })
    .eventCallback('onUpdate', () => { Frame.setUpdateLoopActive() })
  }
}

export const transitFillLight = (light: Light, settings: LightSettings) => {
  if (settings.instant) {
    light.intensity = settings.target.intensity;
  } else {
    TweenLite.to(light, settings.duration, {
      ease: <any>Power0.easeNone,
      intensity: settings.target.intensity,
      delay: settings.delay,
    })
    .eventCallback('onUpdate', () => { Frame.setUpdateLoopActive() })
  }
}
