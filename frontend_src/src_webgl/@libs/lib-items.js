import { Quaternion, Interpolant, Loader, LoaderUtils, FileLoader, Color, SpotLight, PointLight, DirectionalLight, MeshBasicMaterial, MeshPhysicalMaterial, Vector2, sRGBEncoding, TangentSpaceNormalMap, ImageBitmapLoader, TextureLoader, InterleavedBuffer, InterleavedBufferAttribute, BufferAttribute, LinearFilter, LinearMipmapLinearFilter, RepeatWrapping, PointsMaterial, Material, LineBasicMaterial, MeshStandardMaterial, DoubleSide as DoubleSide$1, PropertyBinding, BufferGeometry, SkinnedMesh, Mesh, LineSegments, Line, LineLoop, Points, Group, PerspectiveCamera, MathUtils, OrthographicCamera, InterpolateLinear, AnimationClip, Bone, Object3D, Matrix4, Skeleton, TriangleFanDrawMode, NearestFilter, NearestMipmapNearestFilter, LinearMipmapNearestFilter, NearestMipmapLinearFilter, ClampToEdgeWrapping, MirroredRepeatWrapping, InterpolateDiscrete, FrontSide, Texture, TriangleStripDrawMode, VectorKeyframeTrack, QuaternionKeyframeTrack, NumberKeyframeTrack, Box3, Vector3, Sphere, RGBAFormat, RGBA_ASTC_4x4_Format, RGBA_BPTC_Format, RGBA_ETC2_EAC_Format, RGBA_PVRTC_4BPPV1_Format, RGBA_S3TC_DXT5_Format, RGB_ETC1_Format, RGB_ETC2_Format, RGB_PVRTC_4BPPV1_Format, RGB_S3TC_DXT1_Format, CompressedTexture, UnsignedByteType, LinearEncoding as LinearEncoding$1, LoadingManager } from 'three';

var StrategyModes;
(function (StrategyModes) {
    StrategyModes["Area"] = "area";
    StrategyModes["Random"] = "random";
    StrategyModes["Max"] = "max";
    StrategyModes["betaTvStandTester"] = "betaTvStandTester";
})(StrategyModes || (StrategyModes = {}));
var Spaces;
(function (Spaces) {
    Spaces["LivingRoom"] = "livingRoom";
    Spaces["DinningRoom"] = "dinningRoom";
    Spaces["BedRoom"] = "bedRoom";
    Spaces["KidsRoom"] = "kidsRoom";
    Spaces["Hallway"] = "hallway";
    Spaces["Office"] = "office";
})(Spaces || (Spaces = {}));
var Category;
(function (Category) {
    Category["Chest"] = "chest";
    Category["TvStand"] = "tvstand";
    Category["SideBoard"] = "sideboard";
    Category["Bookcase"] = "bookcase";
    Category["Shoerack"] = "shoerack";
    Category["Wallstorage"] = "wallstorage";
    Category["Desk"] = "desk";
    Category["Unknown"] = "unknown";
})(Category || (Category = {}));
const strategySpecification = {
    mode: StrategyModes.Area,
    space: Spaces.LivingRoom,
    activeAreas: {
        top: true,
        body: true
    }
};

var areaStrategies = {
	"tvStand_v1.0.0": {
	id: "tvStand_v1.0.0",
	globalProperties: {
		density: {
			min: 20,
			max: 80
		},
		spaces: [
			"office",
			"livingRoom"
		],
		categories: [
			"tvstand"
		],
		types: [
			"type_01",
			"type_02",
			"type_01_veneer"
		]
	},
	areas: {
		shelfTop: {
			itemsCount: {
				min: 1,
				max: 8
			},
			items: [
				{
					id: "ST_001",
					isActive: true,
					priorityRate: 8,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											2091,
											2540
										],
										value: 1050
									},
									{
										from: "left",
										inRange: [
											2541,
											2960
										],
										value: 1480
									}
								],
								relative: [
									{
										inRange: [
											1400,
											2090
										],
										value: 50
									},
									{
										inRange: [
											2961,
											4500
										],
										value: 50
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 500,
								min: 50
							},
							height: {
								max: 760,
								min: 600
							},
							width: {
								max: 1240,
								min: 800
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#elec0069",
							".electronics",
							"TV_55",
							"black"
						]
					}
				},
				{
					id: "ST_002",
					isActive: true,
					priorityRate: 5,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											2301,
											2410
										],
										value: 2030
									},
									{
										from: "left",
										inRange: [
											2701,
											3230
										],
										value: 440
									},
									{
										from: "right",
										inRange: [
											3231,
											3450
										],
										value: 2800
									}
								],
								relative: [
									{
										inRange: [
											3451,
											4500
										],
										value: 19
									},
									{
										inRange: [
											2411,
											2700
										],
										value: 16.2
									},
									{
										inRange: [
											2100,
											2300
										],
										value: 12
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 400,
								min: 100
							},
							height: {
								max: 200,
								min: 140
							},
							width: {
								max: 290,
								min: 179
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#cera0008",
							".ceramics",
							"vase",
							"terracotta",
							"orange",
							"round",
							"balls"
						]
					}
				},
				{
					id: "ST_003",
					isActive: true,
					priorityRate: 3,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "left",
										inRange: [
											3510,
											3710
										],
										value: 420
									}
								],
								relative: [
									{
										inRange: [
											3711,
											4500
										],
										value: 11.5
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 900,
								min: 100
							},
							height: {
								max: 800,
								min: 700
							},
							width: {
								max: 620,
								min: 550
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#plant0014",
							".plants",
							"monstera",
							"grey",
							"pot"
						]
					}
				},
				{
					id: "ST_004",
					isActive: true,
					priorityRate: 10,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											300,
											400
										],
										value: 150
									},
									{
										from: "right",
										inRange: [
											3810,
											4500
										],
										value: 300
									},
									{
										from: "left",
										inRange: [
											401,
											1090
										],
										value: 250
									}
								],
								relative: [
									{
										inRange: [
											1091,
											1390
										],
										value: 23.6
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 500,
								min: 100
							},
							height: {
								max: 400,
								min: 172
							},
							width: {
								max: 260,
								min: 196
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#lamp0056",
							".lamps",
							"classic",
							"metal",
							"silver",
							"standing",
							"design"
						]
					}
				},
				{
					id: "ST_005",
					isActive: true,
					priorityRate: 9,
					slotRequirements: {
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										inRange: [
											710,
											1390
										],
										value: 68.7
									},
									{
										inRange: [
											4060,
											4500
										],
										value: 29.1
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 500,
								min: 100
							},
							height: {
								max: 50,
								min: 20
							},
							width: {
								max: 340,
								min: 196
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#book0021",
							".books",
							"horizontal",
							"album",
							"design",
							"bauhaus"
						]
					}
				},
				{
					id: "ST_006",
					isActive: true,
					priorityRate: 4,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											2810,
											2990
										],
										value: 220
									},
									{
										from: "left",
										inRange: [
											3401,
											3800
										],
										value: 3160
									}
								],
								relative: [
									{
										inRange: [
											3801,
											4500
										],
										value: 83.1
									},
									{
										inRange: [
											2991,
											3400
										],
										value: 92.8
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 100
							},
							height: {
								max: 500,
								min: 350
							},
							width: {
								max: 360,
								min: 200
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#plant0038",
							".plants",
							"rubber",
							"beige",
							"pot"
						]
					}
				},
				{
					id: "ST_007",
					isActive: true,
					priorityRate: 6,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											2051,
											2300
										],
										value: 290
									},
									{
										from: "left",
										inRange: [
											3811,
											4090
										],
										value: 2810
									},
									{
										from: "left",
										inRange: [
											1901,
											2050
										],
										value: 1770
									},
									{
										from: "left",
										inRange: [
											2631,
											3140
										],
										value: 2310
									}
								],
								relative: [
									{
										inRange: [
											4091,
											4500
										],
										value: 68.4
									},
									{
										inRange: [
											1600,
											1900
										],
										value: 92.8
									},
									{
										inRange: [
											3141,
											3810
										],
										value: 73.4
									},
									{
										inRange: [
											2301,
											2630
										],
										value: 87.7
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 400,
								min: 40
							},
							height: {
								max: 40,
								min: 9
							},
							width: {
								max: 100,
								min: 70
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#elec0068",
							".electronics",
							"apple_tv",
							"dark_gray",
							"station"
						]
					}
				},
				{
					id: "ST_008",
					isActive: true,
					priorityRate: 7,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											1460,
											1590
										],
										value: 50
									},
									{
										from: "right",
										inRange: [
											2021,
											2110
										],
										value: 190
									},
									{
										from: "left",
										inRange: [
											3741,
											4090
										],
										value: 2890
									},
									{
										from: "left",
										inRange: [
											1881,
											2020
										],
										value: 1840
									},
									{
										from: "left",
										inRange: [
											2621,
											3090
										],
										value: 2390
									}
								],
								relative: [
									{
										inRange: [
											4091,
											4500
										],
										value: 70.4
									},
									{
										inRange: [
											1591,
											1880
										],
										value: 97.5
									},
									{
										inRange: [
											3091,
											3740
										],
										value: 77.2
									},
									{
										inRange: [
											2111,
											2620
										],
										value: 91
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 400,
								min: 10
							},
							height: {
								max: 10,
								min: 5
							},
							width: {
								max: 40,
								min: 10
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#elec0021",
							".electronics",
							"apple_tv",
							"grey",
							"remote"
						]
					}
				}
			]
		},
		shelfBody: {
			itemsCount: {
				min: 0,
				max: 12
			},
			items: [
				{
					id: "SB_001",
					isActive: true,
					priorityRate: 16,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 80,
										inRange: [
											1400,
											2900
										],
										start: 20
									},
									{
										end: 75,
										inRange: [
											2901,
											3910
										],
										start: 25
									},
									{
										end: 70,
										inRange: [
											3911,
											4500
										],
										start: 30
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1350
										],
										start: 30
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 551,
								min: 300
							},
							height: {
								max: 390,
								min: 170
							},
							width: {
								max: 870,
								min: 520
							}
						},
						types: [
							"expoBackCable",
							"expoHollow",
							"expoInsertsCable",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#elec0003",
							".electronics",
							"audio_video_receiver",
							"black"
						]
					}
				},
				{
					id: "SB_002",
					isActive: true,
					priorityRate: 14,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 0,
										from: "right",
										inRange: [
											3491,
											3900
										],
										start: 2730
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1400,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
									{
										end: 1000,
										from: "bottom",
										inRange: [
											1101,
											1350
										],
										start: 0
									}
								],
								relative: [
									{
										end: 85,
										inRange: [
											400,
											1100
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5
						],
						size: {
							depth: {
								max: 500,
								min: 200
							},
							height: {
								max: 450,
								min: 300
							},
							width: {
								max: 850,
								min: 160
							}
						},
						types: [
							"expoBackCable",
							"expoHollow",
							"expoInsertsCable",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#elec0014",
							".electronics",
							"xbox",
							"vertical",
							"black",
							"gaming"
						]
					}
				},
				{
					id: "SB_003",
					isActive: true,
					priorityRate: 12,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 96.5,
										inRange: [
											0,
											2000
										],
										start: 0
									},
									{
										end: 93,
										inRange: [
											2001,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 85,
										inRange: [
											500,
											900
										],
										start: 0
									},
									{
										end: 80,
										inRange: [
											901,
											1350
										],
										start: 8
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 500,
								min: 200
							},
							height: {
								max: 850,
								min: 400
							},
							width: {
								max: 850,
								min: 270
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport",
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#cera0011",
							".ceramics",
							"tall",
							"vertical",
							"vase",
							"design"
						]
					}
				},
				{
					id: "SB_004",
					isActive: true,
					priorityRate: 13,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											2340
										],
										start: 0
									},
									{
										end: 90,
										inRange: [
											2341,
											4500
										],
										start: 10
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 88,
										inRange: [
											700,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 500,
								min: 300
							},
							height: {
								max: 850,
								min: 650
							},
							width: {
								max: 850,
								min: 270
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#cera0022",
							".ceramics",
							"tall",
							"vertical",
							"vase",
							"decorative",
							"colorful",
							"design"
						]
					}
				},
				{
					id: "SB_005",
					isActive: true,
					priorityRate: 9,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1180,
										from: "right",
										inRange: [
											1301,
											1530
										],
										start: 0
									},
									{
										end: 2500,
										from: "right",
										inRange: [
											3301,
											3690
										],
										start: 170
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											1300
										],
										start: 10
									},
									{
										end: 95,
										inRange: [
											1531,
											3300
										],
										start: 24
									},
									{
										end: 95,
										inRange: [
											3691,
											4500
										],
										start: 32.5
									}
								]
							},
							y: {
								absolute: [
									{
										end: 550,
										from: "bottom",
										inRange: [
											751,
											1350
										],
										start: 0
									}
								],
								relative: [
									{
										end: 74,
										inRange: [
											400,
											750
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 500,
								min: 250
							},
							height: {
								max: 390,
								min: 350
							},
							width: {
								max: 850,
								min: 230
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoSupport",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 65
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0022",
							".books",
							"vertical",
							"design",
							"fasion",
							"culture",
							"style",
							"colors"
						]
					}
				},
				{
					id: "SB_006",
					isActive: true,
					priorityRate: 8,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1180,
										from: "left",
										inRange: [
											1301,
											1780
										],
										start: 0
									},
									{
										end: 2180,
										from: "left",
										inRange: [
											3301,
											3690
										],
										start: 0
									}
								],
								relative: [
									{
										end: 90,
										inRange: [
											300,
											1300
										],
										start: 0
									},
									{
										end: 66,
										inRange: [
											1781,
											3300
										],
										start: 0
									},
									{
										end: 59,
										inRange: [
											3691,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
									{
										end: 530,
										from: "top",
										inRange: [
											601,
											1040
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											260,
											600
										],
										start: 13
									},
									{
										end: 100,
										inRange: [
											1041,
											1350
										],
										start: 45
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 500,
								min: 200
							},
							height: {
								max: 440,
								min: 250
							},
							width: {
								max: 850,
								min: 240
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoSupport",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 65
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0023",
							".books",
							"vertical",
							"design",
							"fasion",
							"culture",
							"style",
							"colors"
						]
					}
				},
				{
					id: "SB_007",
					isActive: true,
					priorityRate: 7,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1085,
										from: "right",
										inRange: [
											1601,
											2750
										],
										start: 0
									},
									{
										end: 1460,
										from: "right",
										inRange: [
											3701,
											4500
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											1600
										],
										start: 33
									},
									{
										end: 100,
										inRange: [
											2751,
											3700
										],
										start: 60.5
									}
								]
							},
							y: {
								absolute: [
									{
										end: 400,
										from: "top",
										inRange: [
											701,
											1350
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											500,
											700
										],
										start: 40
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5
						],
						size: {
							depth: {
								max: 500,
								min: 250
							},
							height: {
								max: 440,
								min: 160
							},
							width: {
								max: 850,
								min: 300
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoSupport",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 85
							},
							z: {
								mode: "explicit",
								percent: 25
							}
						},
						tags: [
							"#book0007",
							".books",
							"horizontal",
							"design",
							"fasion",
							"culture",
							"style",
							"pastel"
						]
					}
				},
				{
					id: "SB_008",
					isActive: true,
					priorityRate: 15,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 800,
										from: "left",
										inRange: [
											2300,
											4500
										],
										start: 0
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 500,
								min: 220
							},
							height: {
								max: 800,
								min: 270
							},
							width: {
								max: 850,
								min: 170
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport",
							"expoInsertsCable",
							"expoInserts",
							"expoLeftOpen"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#elec0071",
							".electronics",
							"speaker",
							"audio",
							"vertical",
							"aluminium",
							"design",
							"classic"
						]
					}
				},
				{
					id: "SB_009",
					isActive: true,
					priorityRate: 10,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 700,
										from: "right",
										inRange: [
											3000,
											4500
										],
										start: 0
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5
						],
						size: {
							depth: {
								max: 500,
								min: 220
							},
							height: {
								max: 800,
								min: 270
							},
							width: {
								max: 850,
								min: 170
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport",
							"expoInsertsCable",
							"expoInserts",
							"expoRightOpen"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#elec0072",
							".electronics",
							"speaker",
							"audio",
							"vertical",
							"aluminium",
							"cover",
							"black",
							"design",
							"classic"
						]
					}
				},
				{
					id: "SB_010",
					isActive: true,
					priorityRate: 6,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											340,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											400,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 500,
								min: 250
							},
							height: {
								max: 500,
								min: 360
							},
							width: {
								max: 850,
								min: 120
							}
						},
						types: [
							"expoInserts",
							"expoInsertsCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#book0024",
							".books",
							"magazines",
							"thin",
							"vertical",
							"many",
							"colorful",
							"high",
							"dense"
						]
					}
				},
				{
					id: "SB_011",
					isActive: true,
					priorityRate: 5,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											200,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 500,
								min: 100
							},
							height: {
								max: 500,
								min: 150
							},
							width: {
								max: 360,
								min: 90
							}
						},
						types: [
							"expoLeftOpen",
							"expoRightOpen"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 75
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#acces0003",
							".accesories",
							"rubik's cube"
						]
					}
				},
				{
					id: "SB_012",
					isActive: true,
					priorityRate: 11,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 88,
										inRange: [
											0,
											4500
										],
										start: 12
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											500,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 500,
								min: 200
							},
							height: {
								max: 850,
								min: 450
							},
							width: {
								max: 850,
								min: 220
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#cera0023",
							".ceramics",
							"tall",
							"vertical",
							"vase",
							"decorative",
							"colorful",
							"design"
						]
					}
				},
				{
					id: "SB_013",
					isActive: true,
					priorityRate: 4,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 73,
										inRange: [
											2400,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 70,
										inRange: [
											400,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1
						],
						size: {
							depth: {
								max: 500,
								min: 350
							},
							height: {
								max: 450,
								min: 350
							},
							width: {
								max: 850,
								min: 170
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport",
							"expoInserts",
							"expoInsertsCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#acces0010",
							".accesories",
							"binder",
							"documents",
							"tall",
							"vertical",
							"colorful",
							"office"
						]
					}
				},
				{
					id: "SB_014",
					isActive: true,
					priorityRate: 3,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 65,
										inRange: [
											2400,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 79.9,
										inRange: [
											400,
											1350
										],
										start: 16.8
									}
								]
							},
							z: "center"
						},
						rows: [
							2
						],
						size: {
							depth: {
								max: 500,
								min: 350
							},
							height: {
								max: 850,
								min: 100
							},
							width: {
								max: 850,
								min: 390
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 25
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#book0025",
							".books",
							"horozontal",
							"black",
							"album"
						]
					}
				}
			]
		}
	}
},
	"desk_v1.0.0": {
	id: "desk_v1.0.0",
	globalProperties: {
		density: {
			min: 20,
			max: 80
		},
		spaces: [
			"office",
			"livingRoom"
		],
		categories: [
			"desk"
		],
		types: [
			"type_01",
			"type_02",
			"type_01_veneer"
		]
	},
	areas: {
		shelfTop: {
			itemsCount: {
				min: 1,
				max: 8
			},
			items: [
				{
					id: "ST_001",
					isActive: true,
					priorityRate: 8,
					slotRequirements: {
						position: {
							x: {
								absolute: [
								],
								relative: [
								],
								special: [
									{
										anchor: "center",
										offset: {
											x: 0,
											z: 0
										},
										type: "workPlaceZone",
										zoneDepth: {
											max: 600,
											min: 50
										},
										zoneWidth: {
											max: 2000,
											min: 300
										}
									}
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 50
							},
							height: {
								max: 203,
								min: 160
							},
							width: {
								max: 305,
								min: 140
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "special"
							},
							z: {
								mode: "special"
							}
						},
						tags: [
							"#elec0073",
							".electronics",
							"macbook_13",
							"silver",
							"aluminium"
						]
					}
				},
				{
					id: "ST_002",
					isActive: true,
					priorityRate: 7,
					slotRequirements: {
						position: {
							x: {
								absolute: [
								],
								relative: [
								],
								special: [
									{
										anchor: "cableManagementLeft",
										offset: {
											x: 16,
											z: 88
										},
										type: "workPlaceZone",
										zoneDepth: {
											max: 600,
											min: 50
										},
										zoneWidth: {
											max: 2000,
											min: 620
										}
									}
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 50
							},
							height: {
								max: 500,
								min: 160
							},
							width: {
								max: 305,
								min: 140
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "special"
							},
							z: {
								mode: "special"
							}
						},
						tags: [
							"#lamp0058",
							".lamps",
							"classic",
							"metal",
							"silver",
							"standing",
							"design"
						]
					}
				},
				{
					id: "ST_003",
					isActive: true,
					priorityRate: 6,
					slotRequirements: {
						position: {
							x: {
								absolute: [
								],
								relative: [
								],
								special: [
									{
										anchor: "cableManagementRight",
										offset: {
											x: -16,
											z: 88
										},
										type: "workPlaceZone",
										zoneDepth: {
											max: 600,
											min: 50
										},
										zoneWidth: {
											max: 2000,
											min: 620
										}
									}
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 50
							},
							height: {
								max: 500,
								min: 160
							},
							width: {
								max: 305,
								min: 140
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "special"
							},
							z: {
								mode: "special"
							}
						},
						tags: [
							"#lamp0059",
							".lamps",
							"classic",
							"metal",
							"silver",
							"standing",
							"design"
						]
					}
				},
				{
					id: "ST_004",
					isActive: false,
					priorityRate: 5,
					slotRequirements: {
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										inRange: [
											1500,
											4500
										],
										value: 80
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 100
							},
							height: {
								max: 500,
								min: 350
							},
							width: {
								max: 360,
								min: 200
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#plant0038",
							".plants",
							"rubber",
							"beige",
							"pot"
						]
					}
				}
			]
		},
		shelfBody: {
			itemsCount: {
				min: 0,
				max: 12
			},
			items: [
				{
					id: "SB_001",
					isActive: true,
					priorityRate: 8,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
									{
										end: 530,
										from: "top",
										inRange: [
											601,
											1040
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											260,
											1500
										],
										start: 13
									}
								]
							},
							z: "center"
						},
						rows: [
							1
						],
						size: {
							depth: {
								max: 500,
								min: 200
							},
							height: {
								max: 440,
								min: 250
							},
							width: {
								max: 850,
								min: 240
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoSupport",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 65
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0023",
							".books",
							"vertical",
							"design",
							"fasion",
							"culture",
							"style",
							"colors"
						]
					}
				},
				{
					id: "SB_002",
					isActive: true,
					priorityRate: 6,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											200,
											1500
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							2,
							3
						],
						size: {
							depth: {
								max: 500,
								min: 250
							},
							height: {
								max: 440,
								min: 160
							},
							width: {
								max: 850,
								min: 300
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoSupport",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 85
							},
							z: {
								mode: "explicit",
								percent: 25
							}
						},
						tags: [
							"#book0007",
							".books",
							"horizontal",
							"design",
							"fasion",
							"culture",
							"style",
							"pastel"
						]
					}
				},
				{
					id: "SB_003",
					isActive: true,
					priorityRate: 5,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											200,
											1350
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							3
						],
						size: {
							depth: {
								max: 500,
								min: 100
							},
							height: {
								max: 500,
								min: 150
							},
							width: {
								max: 900,
								min: 90
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoSupport",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 75
							},
							z: {
								mode: "explicit",
								percent: 40
							}
						},
						tags: [
							"#acces0003",
							".accesories",
							"rubik's cube"
						]
					}
				},
				{
					id: "SB_004",
					isActive: true,
					priorityRate: 7,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											400,
											1500
										],
										start: 55
									}
								]
							},
							z: "center"
						},
						rows: [
							2,
							3
						],
						size: {
							depth: {
								max: 500,
								min: 350
							},
							height: {
								max: 850,
								min: 100
							},
							width: {
								max: 850,
								min: 390
							}
						},
						types: [
							"expoBack",
							"expoHollow",
							"expoBackCable",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 25
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#book0025",
							".books",
							"horozontal",
							"black",
							"album"
						]
					}
				}
			]
		}
	}
},
	"sideboard_v1.0.0": {
	id: "sideboard_v1.0.0",
	globalProperties: {
		density: {
			min: 20,
			max: 80
		},
		spaces: [
			"office",
			"livingRoom"
		],
		categories: [
			"sideboard"
		],
		types: [
			"type_01",
			"type_02",
			"type_01_veneer"
		]
	},
	areas: {
		shelfTop: {
			itemsCount: {
				min: 1,
				max: 8
			},
			items: [
				{
					id: "ST_001",
					isActive: true,
					priorityRate: 10,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											300,
											400
										],
										value: 150
									},
									{
										from: "left",
										inRange: [
											2201,
											2800
										],
										value: 520
									},
									{
										from: "left",
										inRange: [
											401,
											1090
										],
										value: 250
									}
								],
								relative: [
									{
										inRange: [
											1091,
											2200
										],
										value: 23.6
									},
									{
										inRange: [
											2801,
											4500
										],
										value: 18.5
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 500,
								min: 100
							},
							height: {
								max: 400,
								min: 172
							},
							width: {
								max: 260,
								min: 196
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#lamp0056",
							".lamps",
							"classic",
							"metal",
							"silver",
							"standing",
							"design"
						]
					}
				},
				{
					id: "ST_002",
					isActive: true,
					priorityRate: 9,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											1291,
											4500
										],
										value: 408
									}
								],
								relative: [
									{
										inRange: [
											710,
											1290
										],
										value: 68.7
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 100
							},
							height: {
								max: 138,
								min: 20
							},
							width: {
								max: 301,
								min: 135
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 65
							}
						},
						tags: [
							"#book0029",
							".books",
							"horizontal",
							"album",
							"design",
							"bauhaus",
							"sculpture"
						]
					}
				},
				{
					id: "ST_003",
					isActive: true,
					priorityRate: 8,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "left",
										inRange: [
											2351,
											3600
										],
										value: 1050
									}
								],
								relative: [
									{
										inRange: [
											3601,
											4500
										],
										value: 29
									},
									{
										inRange: [
											1600,
											2350
										],
										value: 45
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 700,
								min: 350
							},
							height: {
								max: 113,
								min: 9
							},
							width: {
								max: 423,
								min: 70
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#elec0076",
							".electronics",
							"turntable",
							"dark_gray",
							"vinyl"
						]
					}
				},
				{
					id: "ST_004",
					isActive: true,
					priorityRate: 7,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "left",
										inRange: [
											2351,
											3600
										],
										value: 885
									}
								],
								relative: [
									{
										inRange: [
											3601,
											4500
										],
										value: 24.6
									},
									{
										inRange: [
											1200,
											2350
										],
										value: 37.6
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 340,
								min: 30
							},
							height: {
								max: 130,
								min: 9
							},
							width: {
								max: 105,
								min: 60
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#plant0040",
							".plants",
							"agave",
							"small"
						]
					}
				},
				{
					id: "ST_005",
					isActive: true,
					priorityRate: 6,
					slotRequirements: {
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										inRange: [
											1200,
											1599
										],
										value: 37.6
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 700,
								min: 350
							},
							height: {
								max: 130,
								min: 9
							},
							width: {
								max: 105,
								min: 60
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#plant0040",
							".plants",
							"agave",
							"small"
						]
					}
				},
				{
					id: "ST_006",
					isActive: true,
					priorityRate: 5,
					slotRequirements: {
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										inRange: [
											2330,
											4500
										],
										value: 75
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 900,
								min: 280
							},
							height: {
								max: 806,
								min: 700
							},
							width: {
								max: 576,
								min: 500
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "gravityBack"
							}
						},
						tags: [
							"#deco0006",
							".deco",
							"poster",
							"yellow",
							"orange"
						]
					}
				},
				{
					id: "ST_007",
					isActive: true,
					priorityRate: 4,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "left",
										inRange: [
											2901,
											3140
										],
										value: 2111
									},
									{
										from: "right",
										inRange: [
											4401,
											4500
										],
										value: 1461
									}
								],
								relative: [
									{
										inRange: [
											2600,
											2900
										],
										value: 72.9
									},
									{
										inRange: [
											3141,
											4400
										],
										value: 66.7
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 700,
								min: 70
							},
							height: {
								max: 90,
								min: 50
							},
							width: {
								max: 198,
								min: 120
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#cera0024",
							".ceramics",
							"vase",
							"terracotta",
							"orange",
							"round"
						]
					}
				},
				{
					id: "ST_008",
					isActive: true,
					priorityRate: 3,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "right",
										inRange: [
											2900,
											3200
										],
										value: 1256
									},
									{
										from: "right",
										inRange: [
											4201,
											4500
										],
										value: 1625
									}
								],
								relative: [
									{
										inRange: [
											3201,
											4200
										],
										value: 61.1
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 100
							},
							height: {
								max: 500,
								min: 350
							},
							width: {
								max: 360,
								min: 200
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "center"
							}
						},
						tags: [
							"#plant0039",
							".plants",
							"rubber",
							"grey",
							"pot"
						]
					}
				},
				{
					id: "ST_009",
					isActive: true,
					priorityRate: 2,
					slotRequirements: {
						position: {
							x: {
								absolute: [
									{
										from: "left",
										inRange: [
											3180,
											3400
										],
										value: 2430
									},
									{
										from: "right",
										inRange: [
											3401,
											4500
										],
										value: 764
									}
								],
								relative: [
									{
										inRange: [
											3150,
											3779
										],
										value: 79.8
									}
								],
								special: [
								]
							},
							y: "bottom",
							z: "center"
						},
						size: {
							depth: {
								max: 600,
								min: 10
							},
							height: {
								max: 72,
								min: 5
							},
							width: {
								max: 76,
								min: 10
							}
						},
						types: [
							"expoTop"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: true,
							parameterName: false
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 70
							}
						},
						tags: [
							"#deco0007",
							".deco",
							"sculpture",
							"wooden",
							"geometric"
						]
					}
				}
			]
		},
		shelfBody: {
			itemsCount: {
				min: 0,
				max: 12
			},
			items: [
				{
					id: "SB_001",
					isActive: true,
					priorityRate: 25,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 50,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 339,
								min: 130
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0001",
							".vinyl"
						]
					}
				},
				{
					id: "SB_002",
					isActive: true,
					priorityRate: 25,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 50
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 376,
								min: 124
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0002",
							".vinyl"
						]
					}
				},
				{
					id: "SB_003",
					isActive: true,
					priorityRate: 26,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 110,
								min: 50
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0003",
							".vinyl"
						]
					}
				},
				{
					id: "SB_004",
					isActive: true,
					priorityRate: 25,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 50,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							2
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 557,
								min: 126
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0004",
							".vinyl"
						]
					}
				},
				{
					id: "SB_005",
					isActive: true,
					priorityRate: 25,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 50
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							2
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 643,
								min: 121
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0005",
							".vinyl"
						]
					}
				},
				{
					id: "SB_006",
					isActive: true,
					priorityRate: 26,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							2
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 681,
								min: 128
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0007",
							".vinyl"
						]
					}
				},
				{
					id: "SB_007",
					isActive: true,
					priorityRate: 25,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 50,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 571,
								min: 126
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0006",
							".vinyl"
						]
					}
				},
				{
					id: "SB_008",
					isActive: true,
					priorityRate: 25,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 50
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 664,
								min: 123
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0008",
							".vinyl"
						]
					}
				},
				{
					id: "SB_009",
					isActive: true,
					priorityRate: 26,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 370
							},
							height: {
								max: 520,
								min: 320
							},
							width: {
								max: 529,
								min: 127
							}
						},
						types: [
							"expoInserts"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#vinyl0009",
							".vinyl"
						]
					}
				},
				{
					id: "SB_010",
					isActive: true,
					priorityRate: 40,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 65,
										inRange: [
											1600,
											4500
										],
										start: 15
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 460
							},
							height: {
								max: 520,
								min: 170
							},
							width: {
								max: 920,
								min: 470
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#elec0078",
							".electronics"
						]
					}
				},
				{
					id: "SB_011",
					isActive: true,
					priorityRate: 39,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 500,
										from: "left",
										inRange: [
											1600,
											4500
										],
										start: 0
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 220
							},
							height: {
								max: 820,
								min: 620
							},
							width: {
								max: 920,
								min: 270
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBackCable",
							"expoBack"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#elec0077",
							".electronics"
						]
					}
				},
				{
					id: "SB_012",
					isActive: true,
					priorityRate: 39,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 500,
										from: "left",
										inRange: [
											1600,
											4500
										],
										start: 0
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 220
							},
							height: {
								max: 620,
								min: 420
							},
							width: {
								max: 920,
								min: 270
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBackCable",
							"expoBack"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#elec0081",
							".electronics"
						]
					}
				},
				{
					id: "SB_013",
					isActive: true,
					priorityRate: 39,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 500,
										from: "left",
										inRange: [
											1600,
											4500
										],
										start: 0
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 220
							},
							height: {
								max: 420,
								min: 220
							},
							width: {
								max: 920,
								min: 270
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBackCable",
							"expoBack"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#elec0080",
							".electronics"
						]
					}
				},
				{
					id: "SB_014",
					isActive: true,
					priorityRate: 37,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 33.3,
										inRange: [
											0,
											1599
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 220
							},
							height: {
								max: 420,
								min: 120
							},
							width: {
								max: 920,
								min: 230
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBackCable",
							"expoBack"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 62
							},
							z: {
								mode: "explicit",
								percent: 66
							}
						},
						tags: [
							"#elec0079",
							".electronics"
						]
					}
				},
				{
					id: "SB_015",
					isActive: true,
					priorityRate: 38,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1099
										],
										start: 0
									},
									{
										end: 100,
										inRange: [
											1100,
											2300
										],
										start: 50
									},
									{
										end: 100,
										inRange: [
											2301,
											4500
										],
										start: 64
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 820,
								min: 420
							},
							width: {
								max: 920,
								min: 160
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 53
							},
							z: {
								mode: "explicit",
								percent: 63
							}
						},
						tags: [
							"#cera0028",
							".ceramics"
						]
					}
				},
				{
					id: "SB_016",
					isActive: true,
					priorityRate: 38,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 50,
										inRange: [
											1100,
											2300
										],
										start: 0
									},
									{
										end: 66.6,
										inRange: [
											2301,
											4500
										],
										start: 30
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 820,
								min: 530
							},
							width: {
								max: 920,
								min: 230
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 55
							}
						},
						tags: [
							"#cera0027",
							".ceramics"
						]
					}
				},
				{
					id: "SB_017",
					isActive: true,
					priorityRate: 27,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 49,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2301,
											3500
										],
										start: 0
									},
									{
										end: 25,
										inRange: [
											3501,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 420,
								min: 320
							},
							width: {
								max: 920,
								min: 325
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0039",
							".books"
						]
					}
				},
				{
					id: "SB_018",
					isActive: true,
					priorityRate: 27,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 51,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2301,
											3500
										],
										start: 0
									},
									{
										end: 25,
										inRange: [
											3501,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 320,
								min: 220
							},
							width: {
								max: 920,
								min: 155
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0051",
							".books"
						]
					}
				},
				{
					id: "SB_019",
					isActive: true,
					priorityRate: 27,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 51,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2301,
											3500
										],
										start: 0
									},
									{
										end: 25,
										inRange: [
											3501,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 220,
								min: 120
							},
							width: {
								max: 920,
								min: 289
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0037",
							".books"
						]
					}
				},
				{
					id: "SB_020",
					isActive: true,
					priorityRate: 29,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											2300
										],
										start: 50
									},
									{
										end: 100,
										inRange: [
											2301,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 40
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 420,
								min: 120
							},
							width: {
								max: 920,
								min: 270
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 60
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#acces0012",
							".accessories"
						]
					}
				},
				{
					id: "SB_021",
					isActive: true,
					priorityRate: 29,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 49,
										inRange: [
											0,
											2300
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2301,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 50
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 320,
								min: 120
							},
							width: {
								max: 920,
								min: 130
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable",
							"expoLeftOpen",
							"expoRightOpen"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 12
							},
							z: {
								mode: "explicit",
								percent: 80
							}
						},
						tags: [
							"#deco0008",
							".decorations"
						]
					}
				},
				{
					id: "SB_022",
					isActive: true,
					priorityRate: 31,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 100,
										inRange: [
											1351,
											2300
										],
										start: 50
									},
									{
										end: 100,
										inRange: [
											2301,
											3500
										],
										start: 66.7
									},
									{
										end: 100,
										inRange: [
											3501,
											4500
										],
										start: 75.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 420,
								min: 320
							},
							width: {
								max: 920,
								min: 380
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0031",
							".books"
						]
					}
				},
				{
					id: "SB_023",
					isActive: true,
					priorityRate: 31,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 100,
										inRange: [
											1351,
											2300
										],
										start: 50.1
									},
									{
										end: 100,
										inRange: [
											2301,
											3500
										],
										start: 66.7
									},
									{
										end: 100,
										inRange: [
											3501,
											4500
										],
										start: 75.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 320,
								min: 220
							},
							width: {
								max: 920,
								min: 286
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0040",
							".books"
						]
					}
				},
				{
					id: "SB_024",
					isActive: true,
					priorityRate: 31,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 100,
										inRange: [
											1351,
											2300
										],
										start: 50.1
									},
									{
										end: 100,
										inRange: [
											2301,
											3500
										],
										start: 66.7
									},
									{
										end: 100,
										inRange: [
											3501,
											4500
										],
										start: 75.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 220,
								min: 120
							},
							width: {
								max: 920,
								min: 310
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0032",
							".books"
						]
					}
				},
				{
					id: "SB_025",
					isActive: true,
					priorityRate: 32,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											2301,
											3500
										],
										start: 0
									},
									{
										end: 50,
										inRange: [
											3501,
											4500
										],
										start: 25.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 420,
								min: 320
							},
							width: {
								max: 920,
								min: 285
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0036",
							".books"
						]
					}
				},
				{
					id: "SB_026",
					isActive: true,
					priorityRate: 32,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											2301,
											3500
										],
										start: 0
									},
									{
										end: 50,
										inRange: [
											3501,
											4500
										],
										start: 25.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 320,
								min: 220
							},
							width: {
								max: 920,
								min: 356
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0045",
							".books"
						]
					}
				},
				{
					id: "SB_027",
					isActive: true,
					priorityRate: 32,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											2301,
											3500
										],
										start: 0
									},
									{
										end: 50,
										inRange: [
											3501,
											4500
										],
										start: 25.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 220,
								min: 120
							},
							width: {
								max: 920,
								min: 295
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0038",
							".books"
						]
					}
				},
				{
					id: "SB_028",
					isActive: true,
					priorityRate: 33,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								1,
								2
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 33.3,
										inRange: [
											0,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 420,
								min: 220
							},
							width: {
								max: 920,
								min: 165
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 70
							},
							z: {
								mode: "explicit",
								percent: 66
							}
						},
						tags: [
							"#cera0025",
							".ceramics"
						]
					}
				},
				{
					id: "SB_029",
					isActive: true,
					priorityRate: 34,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 66.6,
										inRange: [
											2300,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 53
									}
								]
							},
							z: "center"
						},
						rows: [
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 175
							},
							height: {
								max: 220,
								min: 120
							},
							width: {
								max: 920,
								min: 200
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 75
							},
							z: {
								mode: "explicit",
								percent: 80
							}
						},
						tags: [
							"#deco0009",
							".decorations"
						]
					}
				},
				{
					id: "SB_030",
					isActive: true,
					priorityRate: 35,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 66.6,
										inRange: [
											0,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 40,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1
						],
						size: {
							depth: {
								max: 520,
								min: 240
							},
							height: {
								max: 420,
								min: 220
							},
							width: {
								max: 920,
								min: 200
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 85
							},
							z: {
								mode: "explicit",
								percent: 80
							}
						},
						tags: [
							"#acces0011",
							".accessories"
						]
					}
				},
				{
					id: "SB_031",
					isActive: true,
					priorityRate: 30,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 66.6,
										inRange: [
											2310,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 520,
								min: 140
							},
							height: {
								max: 820,
								min: 220
							},
							width: {
								max: 920,
								min: 135
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 58
							},
							z: {
								mode: "explicit",
								percent: 62
							}
						},
						tags: [
							"#cera0026",
							".ceramics"
						]
					}
				},
				{
					id: "SB_032",
					isActive: true,
					priorityRate: 36,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 66.6,
										inRange: [
											2301,
											3500
										],
										start: 33.4
									},
									{
										end: 75,
										inRange: [
											3501,
											4500
										],
										start: 50.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 550,
								min: 440
							},
							width: {
								max: 920,
								min: 120
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0034",
							".books"
						]
					}
				},
				{
					id: "SB_033",
					isActive: true,
					priorityRate: 36,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 66.6,
										inRange: [
											2301,
											3500
										],
										start: 33.4
									},
									{
										end: 75,
										inRange: [
											3501,
											4500
										],
										start: 50.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 320,
								min: 220
							},
							width: {
								max: 920,
								min: 270
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0047",
							".books"
						]
					}
				},
				{
					id: "SB_034",
					isActive: true,
					priorityRate: 36,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 0,
										inRange: [
											0,
											1350
										],
										start: 0
									},
									{
										end: 0,
										inRange: [
											1351,
											2300
										],
										start: 0
									},
									{
										end: 66.6,
										inRange: [
											2301,
											3500
										],
										start: 33.4
									},
									{
										end: 75,
										inRange: [
											3501,
											4500
										],
										start: 50.1
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4
						],
						size: {
							depth: {
								max: 520,
								min: 200
							},
							height: {
								max: 220,
								min: 120
							},
							width: {
								max: 920,
								min: 426
							}
						},
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#book0033",
							".books"
						]
					}
				}
			]
		}
	}
},
	"bookcase+wallstorage_v1.0.0": {
	id: "bookcase+wallstorage_v1.0.0",
	globalProperties: {
		density: {
			min: 20,
			max: 80
		},
		spaces: [
			"office",
			"livingRoom"
		],
		categories: [
			"bookcase",
			"wallstorage"
		],
		types: [
			"type_01",
			"type_02",
			"type_01_veneer"
		]
	},
	areas: {
		shelfTop: {
			itemsCount: {
				min: 0,
				max: 8
			},
			items: [
			]
		},
		shelfBody: {
			itemsCount: {
				min: 0,
				max: 50
			},
			items: [
				{
					id: "SB_001",
					isActive: true,
					priorityRate: 100,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1560,
										from: "left",
										inRange: [
											1801,
											4500
										],
										start: 1200
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1200,
											1800
										],
										start: 68
									}
								]
							},
							y: {
								absolute: [
									{
										end: 1300,
										from: "bottom",
										inRange: [
											1301,
											4030
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											700,
											1300
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 1000,
								min: 620
							},
							width: {
								max: 1074,
								min: 140
							}
						},
						types: [
							"expoHollow",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#elec0077",
							".electronics",
							"speaker",
							"vertical",
							"silver"
						]
					}
				},
				{
					id: "SB_002",
					isActive: true,
					priorityRate: 98,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1560,
										from: "left",
										inRange: [
											1801,
											4500
										],
										start: 1200
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1200,
											1800
										],
										start: 68
									}
								]
							},
							y: {
								absolute: [
									{
										end: 1200,
										from: "bottom",
										inRange: [
											1201,
											4030
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											470,
											1200
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 619,
								min: 460
							},
							width: {
								max: 1039,
								min: 140
							}
						},
						types: [
							"expoHollow",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#elec0081",
							".electronics",
							"speaker",
							"vertical",
							"silver"
						]
					}
				},
				{
					id: "SB_003",
					isActive: true,
					priorityRate: 96,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											3600,
											4500
										],
										start: 76
									}
								]
							},
							y: {
								absolute: [
									{
										end: 1300,
										from: "bottom",
										inRange: [
											1301,
											4030
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											700,
											1300
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 1000,
								min: 620
							},
							width: {
								max: 1042,
								min: 140
							}
						},
						types: [
							"expoHollow",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#elec0082",
							".electronics",
							"speaker",
							"vertical",
							"silver"
						]
					}
				},
				{
					id: "SB_004",
					isActive: true,
					priorityRate: 94,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											3600,
											4500
										],
										start: 76
									}
								]
							},
							y: {
								absolute: [
									{
										end: 1200,
										from: "bottom",
										inRange: [
											1201,
											4030
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											470,
											1200
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 619,
								min: 460
							},
							width: {
								max: 1078,
								min: 140
							}
						},
						types: [
							"expoHollow",
							"expoBackCable"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#elec0083",
							".electronics",
							"speaker",
							"vertical",
							"silver"
						]
					}
				},
				{
					id: "SB_005",
					isActive: true,
					priorityRate: 92,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 900,
										from: "left",
										inRange: [
											1001,
											4500
										],
										start: 400
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											600,
											1000
										],
										start: 40
									}
								]
							},
							y: {
								absolute: [
									{
										end: 1600,
										from: "bottom",
										inRange: [
											1601,
											4030
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											700,
											1600
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 1000,
								min: 420
							},
							width: {
								max: 1020,
								min: 150
							}
						},
						types: [
							"expoHollow"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 65
							}
						},
						tags: [
							"#cera0028",
							".ceramics",
							"curves",
							"white"
						]
					}
				},
				{
					id: "SB_006",
					isActive: true,
					priorityRate: 90,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 500,
										from: "left",
										inRange: [
											1001,
											4500
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1000
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1400,
											4030
										],
										start: 21
									}
								]
							},
							z: "center"
						},
						rows: [
							6,
							7
						],
						size: {
							depth: {
								max: 600,
								min: 260
							},
							height: {
								max: 1000,
								min: 420
							},
							width: {
								max: 1062,
								min: 260
							}
						},
						types: [
							"expoHollow",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 56
							}
						},
						tags: [
							"#lamp0056",
							".lamps"
						]
					}
				},
				{
					id: "SB_007",
					isActive: true,
					priorityRate: 88,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2650,
										from: "left",
										inRange: [
											2651,
											4500
										],
										start: 1900
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											2200,
											2650
										],
										start: 75
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											700,
											1600
										],
										start: 0
									},
									{
										end: 100,
										inRange: [
											1601,
											4030
										],
										start: 20
									}
								]
							},
							z: "center"
						},
						rows: [
							2,
							3,
							6,
							7
						],
						size: {
							depth: {
								max: 600,
								min: 290
							},
							height: {
								max: 1000,
								min: 500
							},
							width: {
								max: 1032,
								min: 200
							}
						},
						types: [
							"expoHollow",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 56
							}
						},
						tags: [
							"#plant0041",
							".plants"
						]
					}
				},
				{
					id: "SB_008",
					isActive: true,
					priorityRate: 86,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2650,
										from: "left",
										inRange: [
											2651,
											4500
										],
										start: 1900
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1300,
											3500
										],
										start: 0
									},
									{
										end: 100,
										inRange: [
											3501,
											4500
										],
										start: 20
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 90,
										inRange: [
											1300,
											4030
										],
										start: 10
									}
								]
							},
							z: "center"
						},
						rows: [
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 220
							},
							height: {
								max: 1000,
								min: 530
							},
							width: {
								max: 1023,
								min: 220
							}
						},
						types: [
							"expoHollow",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 60
							}
						},
						tags: [
							"#cera0027",
							".ceramics"
						]
					}
				},
				{
					id: "SB_009",
					isActive: true,
					priorityRate: 84,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2650,
										from: "left",
										inRange: [
											2651,
											4500
										],
										start: 1900
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1300,
											3500
										],
										start: 0
									},
									{
										end: 100,
										inRange: [
											3501,
											4500
										],
										start: 40
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1300,
											4030
										],
										start: 38
									}
								]
							},
							z: "center"
						},
						rows: [
							8,
							9
						],
						size: {
							depth: {
								max: 600,
								min: 270
							},
							height: {
								max: 1000,
								min: 440
							},
							width: {
								max: 1015,
								min: 140
							}
						},
						types: [
							"expoHollow",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0054",
							".books"
						]
					}
				},
				{
					id: "SB_010",
					isActive: true,
					priorityRate: 82,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 3000,
										from: "left",
										inRange: [
											3001,
											4500
										],
										start: 2100
									},
									{
										end: 800,
										from: "right",
										inRange: [
											1501,
											2600
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											2601,
											3000
										],
										start: 70
									},
									{
										end: 100,
										inRange: [
											300,
											1500
										],
										start: 45
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 95,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1
						],
						size: {
							depth: {
								max: 600,
								min: 235
							},
							height: {
								max: 1000,
								min: 260
							},
							width: {
								max: 1007,
								min: 190
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#acces0011",
							".accessories"
						]
					}
				},
				{
					id: "SB_011",
					isActive: true,
					priorityRate: 80,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1807,
										from: "left",
										inRange: [
											2000,
											4500
										],
										start: 800
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											995,
											1999
										],
										start: 50
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 95,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5
						],
						size: {
							depth: {
								max: 600,
								min: 289
							},
							height: {
								max: 1000,
								min: 450
							},
							width: {
								max: 1029,
								min: 137
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0066",
							".books"
						]
					}
				},
				{
					id: "SB_012",
					isActive: true,
					priorityRate: 79,
					slotRequirements: {
						indexInRow: {
							from: "right",
							index: [
								0,
								1,
								2
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											3597,
											4500
										],
										start: 80
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4
						],
						size: {
							depth: {
								max: 600,
								min: 131
							},
							height: {
								max: 429,
								min: 144
							},
							width: {
								max: 1038,
								min: 166
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#deco0009",
							".deco"
						]
					}
				},
				{
					id: "SB_013",
					isActive: true,
					priorityRate: 78,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 999,
										from: "left",
										inRange: [
											1000,
											4500
										],
										start: 0
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											999
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							5
						],
						size: {
							depth: {
								max: 600,
								min: 172
							},
							height: {
								max: 1000,
								min: 154
							},
							width: {
								max: 1003,
								min: 370
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 8
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#box0017",
							".boxes"
						]
					}
				},
				{
					id: "SB_014",
					isActive: true,
					priorityRate: 77,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2200,
										from: "left",
										inRange: [
											2201,
											4500
										],
										start: 1450
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1800,
											2200
										],
										start: 65
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							8
						],
						size: {
							depth: {
								max: 600,
								min: 150
							},
							height: {
								max: 900,
								min: 230
							},
							width: {
								max: 1081,
								min: 160
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 72
							},
							z: {
								mode: "explicit",
								percent: 75
							}
						},
						tags: [
							"#cera0025",
							".ceramics"
						]
					}
				},
				{
					id: "SB_015",
					isActive: true,
					priorityRate: 76,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1950,
										from: "left",
										inRange: [
											1951,
											4500
										],
										start: 1150
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1200,
											1950
										],
										start: 60
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							3
						],
						size: {
							depth: {
								max: 600,
								min: 100
							},
							height: {
								max: 900,
								min: 250
							},
							width: {
								max: 1081,
								min: 129
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 28
							},
							z: {
								mode: "explicit",
								percent: 68
							}
						},
						tags: [
							"#cera0026",
							".ceramics"
						]
					}
				},
				{
					id: "SB_016",
					isActive: true,
					priorityRate: 74,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 770,
										from: "left",
										inRange: [
											341,
											4500
										],
										start: 0
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
									{
										end: 1633,
										from: "bottom",
										inRange: [
											0,
											4030
										],
										start: 0
									}
								],
								relative: [
								]
							},
							z: "center"
						},
						rows: [
							1
						],
						size: {
							depth: {
								max: 600,
								min: 300
							},
							height: {
								max: 450,
								min: 310
							},
							width: {
								max: 1047,
								min: 340
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#box0011",
							".boxes"
						]
					}
				},
				{
					id: "SB_017",
					isActive: true,
					priorityRate: 73,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 770,
										from: "left",
										inRange: [
											341,
											4500
										],
										start: 0
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
									{
										end: 1633,
										from: "bottom",
										inRange: [
											0,
											4030
										],
										start: 0
									}
								],
								relative: [
								]
							},
							z: "center"
						},
						rows: [
							1
						],
						size: {
							depth: {
								max: 600,
								min: 260
							},
							height: {
								max: 450,
								min: 250
							},
							width: {
								max: 1085,
								min: 395
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "gravityFront"
							}
						},
						tags: [
							"#box0013",
							".boxes"
						]
					}
				},
				{
					id: "SB_018",
					isActive: true,
					priorityRate: 72,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2501,
										from: "left",
										inRange: [
											2501,
											4500
										],
										start: 1735
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1900,
											2500
										],
										start: 70
									}
								]
							},
							y: {
								absolute: [
									{
										end: 1633,
										from: "bottom",
										inRange: [
											0,
											4030
										],
										start: 0
									}
								],
								relative: [
								]
							},
							z: "center"
						},
						rows: [
							2
						],
						size: {
							depth: {
								max: 600,
								min: 188
							},
							height: {
								max: 461,
								min: 50
							},
							width: {
								max: 1092,
								min: 114
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport",
							"expoRightOpen"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 85
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#plant0040",
							".plants"
						]
					}
				},
				{
					id: "SB_019",
					isActive: true,
					priorityRate: 70,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2301,
										from: "left",
										inRange: [
											2301,
											4500
										],
										start: 1664
									}
								],
								relative: [
									{
										end: 100,
										inRange: [
											1801,
											2300
										],
										start: 70
									}
								]
							},
							y: {
								absolute: [
									{
										end: 2009,
										from: "bottom",
										inRange: [
											0,
											4030
										],
										start: 697
									}
								],
								relative: [
								]
							},
							z: "center"
						},
						rows: [
							5
						],
						size: {
							depth: {
								max: 600,
								min: 150
							},
							height: {
								max: 450,
								min: 200
							},
							width: {
								max: 1145,
								min: 220
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 40
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#acces0013",
							".accessories"
						]
					}
				},
				{
					id: "SB_020",
					isActive: true,
					priorityRate: 68,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 807,
										from: "left",
										inRange: [
											0,
											4500
										],
										start: 300
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
									{
										end: 4030,
										from: "bottom",
										inRange: [
											0,
											4030
										],
										start: 1670
									}
								],
								relative: [
								]
							},
							z: "center"
						},
						rows: [
							8
						],
						size: {
							depth: {
								max: 600,
								min: 100
							},
							height: {
								max: 350,
								min: 130
							},
							width: {
								max: 1149,
								min: 130
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 70
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#acces0017",
							".accessories"
						]
					}
				},
				{
					id: "SB_021",
					isActive: true,
					priorityRate: 66,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 3172,
										from: "left",
										inRange: [
											2501,
											4500
										],
										start: 2501
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
									{
										end: 2104,
										from: "bottom",
										inRange: [
											701,
											4030
										],
										start: 701
									}
								],
								relative: [
								]
							},
							z: "center"
						},
						rows: [
							4
						],
						size: {
							depth: {
								max: 600,
								min: 260
							},
							height: {
								max: 453,
								min: 100
							},
							width: {
								max: 1144,
								min: 270
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "center"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#acces0012",
							".accessories"
						]
					}
				},
				{
					id: "SB_022",
					isActive: true,
					priorityRate: 64,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2565,
										from: "left",
										inRange: [
											1803,
											4500
										],
										start: 1803
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1802,
											4030
										],
										start: 70.9
									}
								]
							},
							z: "center"
						},
						rows: [
							9
						],
						size: {
							depth: {
								max: 600,
								min: 250
							},
							height: {
								max: 455,
								min: 320
							},
							width: {
								max: 1166,
								min: 370
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#acces0015",
							".accessories"
						]
					}
				},
				{
					id: "SB_023",
					isActive: true,
					priorityRate: 62,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 903,
										from: "left",
										inRange: [
											300,
											4500
										],
										start: 300
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2257,
											4030
										],
										start: 75
									}
								]
							},
							z: "center"
						},
						rows: [
							10
						],
						size: {
							depth: {
								max: 600,
								min: 185
							},
							height: {
								max: 455,
								min: 190
							},
							width: {
								max: 1097,
								min: 370
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 15
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#box0016",
							".boxes"
						]
					}
				},
				{
					id: "SB_024",
					isActive: true,
					priorityRate: 60,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 2900,
										from: "left",
										inRange: [
											2301,
											4500
										],
										start: 2301
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2257,
											4030
										],
										start: 75
									}
								]
							},
							z: "center"
						},
						rows: [
							9
						],
						size: {
							depth: {
								max: 600,
								min: 170
							},
							height: {
								max: 455,
								min: 210
							},
							width: {
								max: 1085,
								min: 265
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 60
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#box0015",
							".boxes"
						]
					}
				},
				{
					id: "SB_025",
					isActive: true,
					priorityRate: 58,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 3100,
										from: "left",
										inRange: [
											2501,
											4500
										],
										start: 2501
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1915,
											4030
										],
										start: 60
									}
								]
							},
							z: "center"
						},
						rows: [
							7
						],
						size: {
							depth: {
								max: 600,
								min: 100
							},
							height: {
								max: 455,
								min: 350
							},
							width: {
								max: 1037,
								min: 390
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 55
							},
							z: {
								mode: "gravityBack"
							}
						},
						tags: [
							"#acces0014",
							".accessories"
						]
					}
				},
				{
					id: "SB_026",
					isActive: true,
					priorityRate: 56,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 3502,
										from: "left",
										inRange: [
											2801,
											4500
										],
										start: 2801
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							8
						],
						size: {
							depth: {
								max: 600,
								min: 170
							},
							height: {
								max: 455,
								min: 172
							},
							width: {
								max: 1131,
								min: 295
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 40
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#box0014",
							".boxes"
						]
					}
				},
				{
					id: "SB_027",
					isActive: true,
					priorityRate: 54,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 4500,
										from: "left",
										inRange: [
											3701,
											4500
										],
										start: 3701
									}
								],
								relative: [
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1683,
											4030
										],
										start: 55.7
									}
								]
							},
							z: "center"
						},
						rows: [
							6
						],
						size: {
							depth: {
								max: 600,
								min: 250
							},
							height: {
								max: 455,
								min: 320
							},
							width: {
								max: 1110,
								min: 280
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#acces0016",
							".accessories"
						]
					}
				},
				{
					id: "SB_028",
					isActive: true,
					priorityRate: 40,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 49,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 240
							},
							height: {
								max: 455,
								min: 350
							},
							width: {
								max: 1069,
								min: 217
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0063",
							".books"
						]
					}
				},
				{
					id: "SB_029",
					isActive: true,
					priorityRate: 39,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 51,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 170
							},
							height: {
								max: 455,
								min: 240
							},
							width: {
								max: 1105,
								min: 350
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0045",
							".books"
						]
					}
				},
				{
					id: "SB_030",
					isActive: true,
					priorityRate: 38,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 49,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 240
							},
							height: {
								max: 350,
								min: 165
							},
							width: {
								max: 1106,
								min: 308
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 58
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0055",
							".books"
						]
					}
				},
				{
					id: "SB_031",
					isActive: true,
					priorityRate: 37,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 51,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 250
							},
							height: {
								max: 455,
								min: 301
							},
							width: {
								max: 1039,
								min: 375
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0042",
							".books"
						]
					}
				},
				{
					id: "SB_032",
					isActive: true,
					priorityRate: 36,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 49,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 190
							},
							height: {
								max: 455,
								min: 270
							},
							width: {
								max: 1109,
								min: 177
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0067",
							".books"
						]
					}
				},
				{
					id: "SB_033",
					isActive: true,
					priorityRate: 35,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 49,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 250
							},
							height: {
								max: 351,
								min: 110
							},
							width: {
								max: 1073,
								min: 305
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 43
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0032",
							".books"
						]
					}
				},
				{
					id: "SB_034",
					isActive: true,
					priorityRate: 34,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 49,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 236
							},
							height: {
								max: 455,
								min: 325
							},
							width: {
								max: 1061,
								min: 410
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0064",
							".books"
						]
					}
				},
				{
					id: "SB_035",
					isActive: true,
					priorityRate: 33,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 51,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 185
							},
							height: {
								max: 455,
								min: 265
							},
							width: {
								max: 1179,
								min: 483
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0058",
							".books"
						]
					}
				},
				{
					id: "SB_036",
					isActive: true,
					priorityRate: 32,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											1200
										],
										start: 0
									},
									{
										end: 51,
										inRange: [
											1201,
											2700
										],
										start: 0
									},
									{
										end: 33.3,
										inRange: [
											2701,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 225
							},
							height: {
								max: 351,
								min: 130
							},
							width: {
								max: 1046,
								min: 285
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 29
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0038",
							".books"
						]
					}
				},
				{
					id: "SB_037",
					isActive: true,
					priorityRate: 31,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 51
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 240
							},
							height: {
								max: 455,
								min: 300
							},
							width: {
								max: 1055,
								min: 196
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0044",
							".books"
						]
					}
				},
				{
					id: "SB_038",
					isActive: true,
					priorityRate: 30,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 51
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 185
							},
							height: {
								max: 455,
								min: 270
							},
							width: {
								max: 1021,
								min: 220
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0061",
							".books"
						]
					}
				},
				{
					id: "SB_039",
					isActive: true,
					priorityRate: 29,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 49
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 238
							},
							height: {
								max: 335,
								min: 120
							},
							width: {
								max: 1096,
								min: 307
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 70
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0035",
							".books"
						]
					}
				},
				{
					id: "SB_040",
					isActive: true,
					priorityRate: 28,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 51
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 240
							},
							height: {
								max: 455,
								min: 295
							},
							width: {
								max: 1044,
								min: 285
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0040",
							".books"
						]
					}
				},
				{
					id: "SB_041",
					isActive: true,
					priorityRate: 27,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 51
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 455,
								min: 265
							},
							width: {
								max: 1105,
								min: 452
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0059",
							".books"
						]
					}
				},
				{
					id: "SB_042",
					isActive: true,
					priorityRate: 26,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 49
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 335,
								min: 150
							},
							width: {
								max: 1041,
								min: 310
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 64
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0029",
							".books"
						]
					}
				},
				{
					id: "SB_043",
					isActive: true,
					priorityRate: 25,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 49
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 241
							},
							height: {
								max: 455,
								min: 308
							},
							width: {
								max: 1179,
								min: 287
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0036",
							".books"
						]
					}
				},
				{
					id: "SB_044",
					isActive: true,
					priorityRate: 24,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 51
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 170
							},
							height: {
								max: 455,
								min: 260
							},
							width: {
								max: 1049,
								min: 225
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0052",
							".books"
						]
					}
				},
				{
					id: "SB_045",
					isActive: true,
					priorityRate: 23,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											1201,
											2700
										],
										start: 51
									},
									{
										end: 66.6,
										inRange: [
											2701,
											4500
										],
										start: 33.4
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 252
							},
							height: {
								max: 348,
								min: 120
							},
							width: {
								max: 1034,
								min: 307
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 64
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0007",
							".books"
						]
					}
				},
				{
					id: "SB_046",
					isActive: true,
					priorityRate: 22,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 233
							},
							height: {
								max: 455,
								min: 323
							},
							width: {
								max: 1018,
								min: 442
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0060",
							".books"
						]
					}
				},
				{
					id: "SB_047",
					isActive: true,
					priorityRate: 21,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 184
							},
							height: {
								max: 455,
								min: 236
							},
							width: {
								max: 1041,
								min: 436
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0062",
							".books"
						]
					}
				},
				{
					id: "SB_048",
					isActive: true,
					priorityRate: 20,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3
						],
						size: {
							depth: {
								max: 600,
								min: 250
							},
							height: {
								max: 351,
								min: 110
							},
							width: {
								max: 1077,
								min: 340
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 70
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0021",
							".books"
						]
					}
				},
				{
					id: "SB_049",
					isActive: true,
					priorityRate: 19,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 248
							},
							height: {
								max: 455,
								min: 302
							},
							width: {
								max: 1114,
								min: 532
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0056",
							".books"
						]
					}
				},
				{
					id: "SB_050",
					isActive: true,
					priorityRate: 18,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 200
							},
							height: {
								max: 455,
								min: 265
							},
							width: {
								max: 1132,
								min: 235
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0057",
							".books"
						]
					}
				},
				{
					id: "SB_051",
					isActive: true,
					priorityRate: 17,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4,
							5,
							6
						],
						size: {
							depth: {
								max: 600,
								min: 260
							},
							height: {
								max: 351,
								min: 100
							},
							width: {
								max: 1155,
								min: 431
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 70
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0033",
							".books"
						]
					}
				},
				{
					id: "SB_052",
					isActive: true,
					priorityRate: 16,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 225
							},
							height: {
								max: 455,
								min: 320
							},
							width: {
								max: 1083,
								min: 284
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0065",
							".books"
						]
					}
				},
				{
					id: "SB_053",
					isActive: true,
					priorityRate: 15,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 170
							},
							height: {
								max: 455,
								min: 250
							},
							width: {
								max: 1186,
								min: 265
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0047",
							".books"
						]
					}
				},
				{
					id: "SB_054",
					isActive: true,
					priorityRate: 14,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											2701,
											4500
										],
										start: 66.7
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 214
							},
							height: {
								max: 351,
								min: 100
							},
							width: {
								max: 1063,
								min: 290
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 78
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0037",
							".books"
						]
					}
				},
				{
					id: "SB_055",
					isActive: true,
					priorityRate: 13,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 500,
										from: "left",
										inRange: [
											1201,
											4500
										],
										start: 0
									}
								],
								relative: [
									{
										end: 42,
										inRange: [
											0,
											1200
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							4
						],
						size: {
							depth: {
								max: 600,
								min: 222
							},
							height: {
								max: 455,
								min: 275
							},
							width: {
								max: 1083,
								min: 190
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0030",
							".books"
						]
					}
				},
				{
					id: "SB_056",
					isActive: true,
					priorityRate: 12,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 80,
										inRange: [
											0,
											1200
										],
										start: 12
									},
									{
										end: 46,
										inRange: [
											1201,
											2100
										],
										start: 14
									},
									{
										end: 38,
										inRange: [
											2101,
											4500
										],
										start: 16
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							6
						],
						size: {
							depth: {
								max: 600,
								min: 165
							},
							height: {
								max: 455,
								min: 250
							},
							width: {
								max: 1083,
								min: 150
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityLeft"
							},
							z: {
								mode: "explicit",
								percent: 92
							}
						},
						tags: [
							"#book0051",
							".books"
						]
					}
				},
				{
					id: "SB_057",
					isActive: true,
					priorityRate: 11,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
									{
										end: 1750,
										from: "left",
										inRange: [
											2251,
											4500
										],
										start: 0
									}
								],
								relative: [
									{
										end: 79,
										inRange: [
											0,
											2250
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							9
						],
						size: {
							depth: {
								max: 600,
								min: 165
							},
							height: {
								max: 455,
								min: 245
							},
							width: {
								max: 1083,
								min: 170
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "gravityRight"
							},
							z: {
								mode: "explicit",
								percent: 85
							}
						},
						tags: [
							"#book0046",
							".books"
						]
					}
				},
				{
					id: "SB_058",
					isActive: true,
					priorityRate: 10,
					slotRequirements: {
						indexInRow: {
							from: "left",
							index: [
								0,
								1,
								2,
								3,
								4,
								5,
								6,
								7,
								8,
								9,
								10,
								11,
								12,
								13,
								14,
								15,
								16
							]
						},
						maxCount: 1,
						position: {
							x: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											300,
											4500
										],
										start: 0
									}
								]
							},
							y: {
								absolute: [
								],
								relative: [
									{
										end: 100,
										inRange: [
											0,
											4030
										],
										start: 0
									}
								]
							},
							z: "center"
						},
						rows: [
							1,
							2,
							3,
							4,
							5,
							6,
							7,
							8,
							9,
							10
						],
						size: {
							depth: {
								max: 600,
								min: 130
							},
							height: {
								max: 230,
								min: 110
							},
							width: {
								max: 1083,
								min: 130
							}
						},
						types: [
							"expoHollow",
							"expoBack",
							"expoSupport"
						]
					},
					specification: {
						customBehavior: {
							ignoreSlotDepth: false,
							parameterName: false
						},
						localDensityTarget: {
							max: 100,
							min: 0
						},
						localOrientation: {
							x: {
								mode: "explicit",
								percent: 19
							},
							z: {
								mode: "explicit",
								percent: 77
							}
						},
						tags: [
							"#deco0008",
							".deco"
						]
					}
				}
			]
		}
	}
}
};

var expoTvStand001 = {
	id: "expoTvStand_001",
	globalProperties: {
		density: {
			min: 20,
			max: 80
		},
		spaces: [
			"office",
			"livingRoom",
			"hallway"
		],
		categories: [
			"tvstand",
			"unknown"
		],
		types: [
			"type_01",
			"type_02",
			"type_01_veneer"
		]
	},
	areas: {
		shelfTop: {
			itemsCount: {
				min: 0,
				max: 1
			},
			items: [
				{
					priorityRate: 10,
					slotRequirements: {
						types: [
							"expoTop"
						],
						item_size: {
							width: {
								min: 1000,
								max: 4500
							},
							height: {
								min: 200,
								max: 1200
							},
							depth: {
								min: 100,
								max: 500
							}
						},
						position: {
							x: {
								relative: {
									value: 50,
									inRange: [
										0,
										4500
									]
								},
								absolute: {
									end: "left",
									value: null,
									inRange: [
									]
								}
							},
							y: "bottom",
							z: "middle"
						}
					},
					tags: [
						"TV",
						"black"
					]
				}
			]
		},
		shelfBody: {
			itemsCount: {
				min: 1,
				max: 10
			},
			items: [
				{
					priorityRate: 3,
					slotRequirements: {
						types: [
							"expoHollow",
							"expoSupport",
							"expoBack",
							"expoBackCable",
							"expoInserts",
							"expoInsertsCable"
						],
						maxCount: 10
					},
					tags: [
						"vase",
						"ceramic",
						"gentle",
						"grey",
						"deco"
					]
				}
			]
		}
	}
};
var betaTvTester = {
	expoTvStand001: expoTvStand001
};

const selectStrategy = (slotFormat, strategySpecification) => {
    const shelfSpecification = extractShelfSpecification$1(slotFormat);
    let strategy = {};
    if (strategySpecification.mode === StrategyModes.Area) {
        strategy = findAreaStrategy(shelfSpecification, strategySpecification.space);
    }
    else if (strategySpecification.mode === StrategyModes.betaTvStandTester) {
        strategy = betaTvTester.expoTvStand001;
    }
    return { ...strategySpecification, ...strategy };
};
const extractShelfSpecification$1 = (slotFormat) => {
    const { shelfCategory = Category.Unknown } = slotFormat;
    return {
        shelfType: slotFormat.shelfType,
        shelfCategory: shelfCategory,
        shelfMaterial: slotFormat.shelfMaterial
    };
};
const findAreaStrategy = (shelf, space) => {
    const strategies = Object.values(areaStrategies);
    return strategies.filter(strategy => isShelfMatchingStrategyGlobalProperties(strategy, shelf, space))[0];
};
const isShelfMatchingStrategyGlobalProperties = (strategy, shelf, space) => {
    return isSpaceMatching(strategy, space) && isTypeMatching(strategy, shelf) && isCategoryMatching(strategy, shelf);
};
const isSpaceMatching = (strategy, space) => strategy.globalProperties.spaces.includes(space);
const isTypeMatching = (strategy, { shelfType }) => strategy.globalProperties.types.includes(shelfType);
const isCategoryMatching = (strategy, shelf) => {
    let isMatching = false;
    if (Object.keys(shelf).includes('shelfCategory')) {
        isMatching = strategy.globalProperties.categories.includes(shelf.shelfCategory);
    }
    return isMatching;
};

// EXPO SLOT TYPES
var EXPO;
(function (EXPO) {
    EXPO["Top"] = "expoTop";
    EXPO["RightOpen"] = "expoRightOpen";
    EXPO["LeftOpen"] = "expoLeftOpen";
    EXPO["Hollow"] = "expoHollow";
    EXPO["Support"] = "expoSupport";
    EXPO["Back"] = "expoBack";
    EXPO["BackCable"] = "expoBackCable";
    EXPO["Inserts"] = "expoInserts";
    EXPO["InsertsCable"] = "expoInsertsCable";
})(EXPO || (EXPO = {}));
// STORAGE SLOT TYPES
var STORAGE;
(function (STORAGE) {
    STORAGE["Door"] = "storageDoor";
    STORAGE["DoorCable"] = "storageDoorCable";
    STORAGE["Drawer"] = "storageDrawer";
    STORAGE["Inserts"] = "storageInserts";
    STORAGE["InsertsCable"] = "storageInsertsCable";
})(STORAGE || (STORAGE = {}));
// GROUPS OF SLOT TYPES
const SLOT_TYPE = {
    EXPO,
    STORAGE
};
// CLOSED OPENINGS TYPES
var ClosedOpeningTypes;
(function (ClosedOpeningTypes) {
    ClosedOpeningTypes["Doors"] = "closedDoors";
    ClosedOpeningTypes["Drawers"] = "closedDrawer";
})(ClosedOpeningTypes || (ClosedOpeningTypes = {}));

var Positioning;
(function (Positioning) {
    Positioning["Absolute"] = "absolute";
    Positioning["Relative"] = "relative";
    Positioning["BeyondShelfRange"] = "beyondShelfRange";
})(Positioning || (Positioning = {}));
var Anchor;
(function (Anchor) {
    Anchor["Left"] = "left";
    Anchor["Right"] = "right";
    Anchor["Bottom"] = "bottom";
    Anchor["Top"] = "top";
})(Anchor || (Anchor = {}));
var ItemsArea;
(function (ItemsArea) {
    ItemsArea["ShelfTop"] = "shelfTop";
    ItemsArea["ShelfBody"] = "shelfBody";
})(ItemsArea || (ItemsArea = {}));
var Axis$1;
(function (Axis) {
    Axis["X"] = "x";
    Axis["Y"] = "y";
    Axis["Z"] = "z";
})(Axis$1 || (Axis$1 = {}));
const findActiveRange = (positionRanges, width, area) => {
    const activeRange = { mode: Positioning.BeyondShelfRange };
    if (positionRanges.absolute.length > 0) {
        const activeAbsoluteRange = positionRanges.absolute.filter((position) => isValueInRange(position.inRange, width));
        if (activeAbsoluteRange.length === 1) {
            if (area === ItemsArea.ShelfTop) {
                activeRange.mode = Positioning.Absolute;
                activeRange.value = activeAbsoluteRange[0].value;
                activeRange.from = activeAbsoluteRange[0].from;
            }
            else if (area === ItemsArea.ShelfBody) {
                activeRange.mode = Positioning.Absolute;
                activeRange.start = activeAbsoluteRange[0].start;
                activeRange.end = activeAbsoluteRange[0].end;
                activeRange.from = activeAbsoluteRange[0].from;
            }
        }
    }
    if (positionRanges.relative.length > 0) {
        const activeRelativeRange = positionRanges.relative.filter((position) => isValueInRange(position.inRange, width));
        if (activeRelativeRange.length === 1) {
            if (area === ItemsArea.ShelfTop) {
                activeRange.mode = Positioning.Relative;
                activeRange.value = activeRelativeRange[0].value;
            }
            else if (area === ItemsArea.ShelfBody) {
                activeRange.mode = Positioning.Relative;
                activeRange.start = activeRelativeRange[0].start;
                activeRange.end = activeRelativeRange[0].end;
            }
        }
    }
    return activeRange;
};
const roundResult$1 = (result) => {
    const decimalPlacesPrecision = 10;
    return Math.round(result * decimalPlacesPrecision) / decimalPlacesPrecision;
};
const sortItemsByPriority = (items) => {
    return items.sort((a, b) => b.priorityRate - a.priorityRate);
};
const isValueInRange = (range, value) => {
    return range[0] <= value && value <= range[1];
};
const calculateRelativePosition = (relativeVal, slotFormat, axis) => {
    const { width, height, bodyHeight } = slotFormat;
    if (axis === Axis$1.X) {
        const distance = width * relativeVal / 100;
        const dim = { min: 0, max: width };
        const coordsX = { min: width / -2, max: width / 2 };
        return Math.round(coordsX.min + (coordsX.max - coordsX.min) * (distance - dim.min) / (dim.max - dim.min));
    }
    else {
        const configurationHeight = getConfigurationHeight(height, bodyHeight);
        const distance = configurationHeight * relativeVal / 100;
        const dim = { min: 0, max: bodyHeight };
        const coordsY = { min: height - bodyHeight, max: height };
        return Math.round(coordsY.min + (coordsY.max - coordsY.min) * (distance - dim.min) / (dim.max - dim.min));
    }
};
const calculateAbsolutePosition = (absoluteRange, slotFormat, axis) => {
    const { width, height, bodyHeight } = slotFormat;
    if (axis === Axis$1.X) {
        if (absoluteRange.from === Anchor.Left) {
            return width / -2 + absoluteRange.value;
        }
        else {
            return width / 2 - absoluteRange.value;
        }
    }
    if (axis === Axis$1.Y) {
        if (absoluteRange.from === Anchor.Bottom) {
            const shelfBodyBottom = height - bodyHeight;
            return shelfBodyBottom + absoluteRange.value;
        }
        else {
            return height - absoluteRange.value;
        }
    }
};
const getConfigurationHeight = (height, bodyHeight) => {
    let configurationHeight = height;
    if (height !== bodyHeight) {
        configurationHeight = height - 100;
    }
    return configurationHeight;
};
const isItemFittingToSlotDepth = (slotDepth, itemDepthRange, canIgnoreSlotDepth) => {
    if (canIgnoreSlotDepth) {
        return true;
    }
    else {
        return itemDepthRange.min <= slotDepth && slotDepth <= itemDepthRange.max;
    }
};

const composeTopPortalData = (precalculatedPosition, topSlot, item) => {
    return {
        id: `${topSlot.bottomAnchor.y}${precalculatedPosition.x}_${item.id}`,
        area: ItemsArea.ShelfTop,
        item: {
            configId: item.id,
            priorityRate: item.priorityRate,
            size: item.slotRequirements.size,
            position: {
                default: {
                    x: precalculatedPosition.x,
                    y: topSlot.bottomAnchor.y,
                    z: precalculatedPosition.z
                }
            },
            localOrientation: item.specification.localOrientation,
            tags: item.specification.tags
        },
        slot: {
            id: topSlot.id,
            x1: topSlot.x1,
            x2: topSlot.x2,
            y1: topSlot.y1,
            y2: topSlot.y2,
            z1: topSlot.z1,
            z2: topSlot.x2,
            width: topSlot.width,
            height: topSlot.height,
            depth: topSlot.depth,
            type: topSlot.type,
            positionalContext: topSlot.positionalContext,
            bottomAnchor: topSlot.bottomAnchor,
            itemsOrientations: topSlot.itemsOrientations,
        }
    };
};
const composeBodyPortalData = (slot, item) => {
    return {
        id: `${slot.id}_${item.id}`,
        area: ItemsArea.ShelfBody,
        item: {
            configId: item.id,
            priorityRate: item.priorityRate,
            size: {
                width: {
                    min: 0,
                    max: slot.width
                },
                height: {
                    min: 0,
                    max: slot.height
                },
                depth: {
                    min: 0,
                    max: slot.depth
                }
            },
            position: {
                default: {
                    x: slot.bottomAnchor.x,
                    y: slot.bottomAnchor.y,
                    z: slot.bottomAnchor.z
                }
            },
            localOrientation: item.specification.localOrientation,
            localDensityTarget: item.specification.localDensityTarget,
            tags: item.specification.tags
        },
        slot: {
            id: slot.id,
            x1: slot.x1,
            x2: slot.x2,
            y1: slot.y1,
            y2: slot.y2,
            z1: slot.z1,
            z2: slot.x2,
            width: slot.width,
            height: slot.height,
            depth: slot.depth,
            type: slot.type,
            positionalContext: slot.positionalContext,
            inRows: slot.inRows,
            bottomAnchor: slot.bottomAnchor,
            itemsOrientations: slot.itemsOrientations,
            hasCableManagement: slot.hasCableManagement
        }
    };
};

const executeAreaStrategyOnShelfTop = (slotFormat, strategy) => {
    if (strategy.activeAreas.top) {
        const topStrategy = strategy.areas.shelfTop;
        const hasTopSlot = slotFormat.slots.map((slot) => slot.type).includes(SLOT_TYPE.EXPO.Top);
        if (hasTopSlot) {
            return createPortalsForShelfTopItems(topStrategy, slotFormat);
        }
        else {
            return [];
        }
    }
    else {
        return [];
    }
};
const createPortalsForShelfTopItems = (topStrategy, slotFormat) => {
    const itemsByPriority = sortItemsByPriority(topStrategy.items);
    const shelfTopPortals = [];
    for (let item of itemsByPriority) {
        if (item.isActive) {
            const position = calculateTopItemPosition(item, slotFormat);
            if ((position.x || position.x === 0) && (position.z || position.z === 0)) {
                const shelfTopPortal = composeTopPortalData(position, getTopSlot(slotFormat), item);
                shelfTopPortals.push(shelfTopPortal);
            }
        }
    }
    return shelfTopPortals;
};
const calculateTopItemPosition = (item, slotFormat) => {
    const positionRanges = item.slotRequirements.position.x;
    const position = { x: null, z: null };
    const topSlot = getTopSlot(slotFormat);
    // temporary assumption of desk having only one work place zone
    const workPlaceZone = (topSlot.workPlaceZones.length > 0) ? topSlot.workPlaceZones[0] : null;
    const requiredSlotDepth = item.slotRequirements.size.depth;
    const ignoreSlotDepth = item.specification.customBehavior.ignoreSlotDepth;
    if (positionRanges.relative.length + positionRanges.absolute.length === 0) {
        if (workPlaceZone !== null && positionRanges.special.length > 0) {
            const { anchor: anchorName, zoneWidth, zoneDepth, offset } = positionRanges.special[0];
            const hasZoneRequiredAnchor = Object.keys(workPlaceZone.anchors).includes(anchorName);
            const isZoneInWidthRange = zoneWidth.min <= workPlaceZone.width && workPlaceZone.width <= zoneWidth.max;
            const isZoneInDepthRange = isItemFittingToSlotDepth(workPlaceZone.depth, zoneDepth, ignoreSlotDepth);
            if (hasZoneRequiredAnchor && isZoneInWidthRange && isZoneInDepthRange) {
                position.x = workPlaceZone.anchors[anchorName].x + offset.x;
                position.z = workPlaceZone.anchors[anchorName].z + offset.z;
            }
        }
    }
    else {
        const activeRange = findActiveRange(positionRanges, slotFormat.width, ItemsArea.ShelfTop);
        const positionX = calcItemXPosition(activeRange, slotFormat);
        const hasSlotAcceptedDepth = isItemFittingToSlotDepth(topSlot.depth, requiredSlotDepth, ignoreSlotDepth);
        if (isItemNotInWorkPlaceZone(workPlaceZone, positionX) && hasSlotAcceptedDepth) {
            position.x = positionX;
            position.z = topSlot.bottomAnchor.z;
        }
    }
    return position;
};
const isItemNotInWorkPlaceZone = (workPlaceZone, positionX) => {
    if (workPlaceZone !== null) {
        const isItemInWorkPlaceZone = isValueInRange([workPlaceZone.x1, workPlaceZone.x2], positionX);
        if (isItemInWorkPlaceZone) {
            return false;
        }
    }
    return true;
};
const calcItemXPosition = (activeRange, slotFormat) => {
    if (activeRange.mode === Positioning.Relative) {
        return calculateRelativePosition(activeRange.value, slotFormat, Axis$1.X);
    }
    else {
        return calculateAbsolutePosition(activeRange, slotFormat, Axis$1.X);
    }
};
const getTopSlot = (slotFormat) => (slotFormat.slots.filter((slot) => slot.type === SLOT_TYPE.EXPO.Top)[0]);

const executeAreaStrategyOnShelfBody = (slotFormat, strategy) => {
    if (strategy.activeAreas.body) {
        const bodyStrategy = strategy.areas.shelfBody;
        const itemsByPriority = sortItemsByPriority(bodyStrategy.items);
        return createPortalsForShelfBodyItems(itemsByPriority, slotFormat);
    }
    else {
        return [];
    }
};
const createPortalsForShelfBodyItems = (itemsByPriority, slotFormat) => {
    const occupiedSlotsId = [];
    const shelfBodyPortals = [];
    for (let item of itemsByPriority) {
        if (item.isActive) {
            const itemSlotTypes = item.slotRequirements.types;
            const maxSlotCount = item.slotRequirements.maxCount;
            let foundSlotsCount = 0;
            for (let itemSlotType of itemSlotTypes) {
                if (foundSlotsCount < maxSlotCount) {
                    const qualifiedSlots = qualifyBodySlots(itemSlotType, item, slotFormat);
                    if (qualifiedSlots.length === 1 && isSlotFree(occupiedSlotsId, qualifiedSlots[0].id)) {
                        const qualifiedSlot = qualifiedSlots.pop();
                        const shelfBodyPortal = composeBodyPortalData(qualifiedSlot, item);
                        shelfBodyPortals.push(shelfBodyPortal);
                        occupiedSlotsId.push(shelfBodyPortal.slot.id);
                        foundSlotsCount++;
                    }
                    if (qualifiedSlots.length > 1) {
                        // maybe random sorting is also worth checking
                        const sortedSlots = sortSlotsByDistanceToAreaCenter(qualifiedSlots, item, slotFormat);
                        sortedSlots.forEach((slot) => {
                            if (isSlotFree(occupiedSlotsId, slot.id) && (foundSlotsCount < maxSlotCount)) {
                                const shelfBodyPortal = composeBodyPortalData(slot, item);
                                shelfBodyPortals.push(shelfBodyPortal);
                                occupiedSlotsId.push(shelfBodyPortal.slot.id);
                                foundSlotsCount++;
                            }
                        });
                    }
                }
            }
        }
    }
    return shelfBodyPortals;
};
const isSlotFree = (occupiedSlotsId, id) => {
    return !occupiedSlotsId.includes(id);
};
const sortSlotsByDistanceToAreaCenter = (slots, item, slotFormat) => {
    const areaCenter = calcItemAreaCenter(item, slotFormat);
    return slots.sort((a, b) => (calcDistanceToAreaCenter(a, areaCenter) - calcDistanceToAreaCenter(b, areaCenter)));
};
const calcItemAreaCenter = (item, slotFormat) => {
    const areaX = item.slotRequirements.position.x;
    const areaY = item.slotRequirements.position.y;
    const activeAreaXRange = findActiveRange(areaX, slotFormat.width, ItemsArea.ShelfBody);
    const activeAreaYRange = findActiveRange(areaY, slotFormat.bodyHeight, ItemsArea.ShelfBody);
    let areaCenter = {};
    if (activeAreaXRange.mode === Positioning.Relative) {
        const relativeCenter = Math.round((activeAreaXRange.start + activeAreaXRange.end) / 2);
        areaCenter.x = calculateRelativePosition(relativeCenter, slotFormat, Axis$1.X);
    }
    if (activeAreaXRange.mode === Positioning.Absolute) {
        activeAreaXRange.value = Math.round((activeAreaXRange.start + activeAreaXRange.end) / 2);
        areaCenter.x = calculateAbsolutePosition(activeAreaXRange, slotFormat, Axis$1.X);
    }
    if (activeAreaYRange.mode === Positioning.Relative) {
        const relativeCenter = Math.round((activeAreaYRange.start + activeAreaYRange.end) / 2);
        areaCenter.y = calculateRelativePosition(relativeCenter, slotFormat, Axis$1.Y);
    }
    if (activeAreaYRange.mode === Positioning.Absolute) {
        activeAreaYRange.value = Math.round((activeAreaYRange.start + activeAreaYRange.end) / 2);
        areaCenter.y = calculateAbsolutePosition(activeAreaYRange, slotFormat, Axis$1.Y);
    }
    return areaCenter;
};
const calcDistanceToAreaCenter = (slot, areaCenter) => {
    const distanceX = roundResult$1(Math.abs(slot.bottomAnchor.x - areaCenter.x));
    const distanceY = roundResult$1(Math.abs(Math.round(slot.bottomAnchor.y + slot.height / 2) - areaCenter.y));
    if (distanceX === 0) {
        return distanceY;
    }
    else if (distanceY === 0) {
        return distanceX;
    }
    else {
        return Math.hypot(distanceX, distanceY);
    }
};
const qualifyBodySlots = (itemSlotType, item, slotFormat) => {
    // filter by type
    const slotsWithQualifiedTypes = slotFormat.slots.filter((slot) => slot.type === itemSlotType);
    // filter by size
    const slotsWithQualifiedSizes = slotsWithQualifiedTypes.filter((slot) => hasQualifiedSize(slot, item));
    // filter by row
    const slotsInQualifiedRows = slotsWithQualifiedSizes.filter((slot) => isInQualifiedRows(slot, item.slotRequirements.rows, slotFormat));
    // filter by index
    const slotsWithQualifiedIndexes = slotsInQualifiedRows.filter((slot) => hasQualifiedIndex(slot, item.slotRequirements.indexInRow));
    // filter by area
    return slotsWithQualifiedIndexes.filter((slot) => isInItemSearchArea(slot, item.slotRequirements.position, slotFormat));
};
const hasQualifiedSize = (slot, item) => {
    const itemSlotSize = item.slotRequirements.size;
    const ignoreSlotDepth = item.specification.customBehavior.ignoreSlotDepth;
    const hasProperWidth = itemSlotSize.width.min <= slot.width && slot.width <= itemSlotSize.width.max;
    const hasProperHeight = itemSlotSize.height.min <= slot.height && slot.height <= itemSlotSize.height.max;
    const hasProperDepth = isItemFittingToSlotDepth(slot.depth, itemSlotSize.depth, ignoreSlotDepth);
    return hasProperWidth && hasProperHeight && hasProperDepth;
};
const isInQualifiedRows = (slot, itemSlotRows, slotFormat) => {
    if (slot.inRows.length > 0) {
        const slotRows = (slotFormat.stackStorage.bottom.active) ? slot.inRows.map((row) => row - 1) : slot.inRows;
        return slotRows.every((row) => itemSlotRows.includes(row));
    }
    else {
        return false;
    }
};
const hasQualifiedIndex = (slot, acceptedIndexesInRow) => {
    const { indexInRow: slotIndex, slotCountInRow } = slot;
    if (acceptedIndexesInRow.from === Anchor.Left) {
        return acceptedIndexesInRow.index.includes(slotIndex);
    }
    if (acceptedIndexesInRow.from === Anchor.Right) {
        return acceptedIndexesInRow.index.includes(reversedIndexOf(slotIndex, slotCountInRow));
    }
    else {
        return false;
    }
};
const reversedIndexOf = (index, numberOfSlotsInRow) => {
    return numberOfSlotsInRow - index - 1;
};
const isInItemSearchArea = (slot, itemSlotPosition, slotFormat) => {
    let isSlotXInSearchArea = false;
    let isSlotYInSearchArea = false;
    const activeAreaXRange = findActiveRange(itemSlotPosition.x, slotFormat.width, ItemsArea.ShelfBody);
    const activeAreaYRange = findActiveRange(itemSlotPosition.y, slotFormat.bodyHeight, ItemsArea.ShelfBody);
    if (activeAreaXRange.mode === Positioning.Relative) {
        isSlotXInSearchArea = isValueInRange([activeAreaXRange.start, activeAreaXRange.end], slot.positionalContext.width.center);
    }
    if (activeAreaYRange.mode === Positioning.Relative) {
        isSlotYInSearchArea = isValueInRange([activeAreaYRange.start, activeAreaYRange.end], slot.positionalContext.height.center);
    }
    if (activeAreaXRange.mode === Positioning.Absolute) {
        isSlotXInSearchArea = isInAbsoluteXRange(slot, activeAreaXRange, slotFormat);
    }
    if (activeAreaYRange.mode === Positioning.Absolute) {
        isSlotYInSearchArea = isInAbsoluteYRange(slot, activeAreaYRange, slotFormat);
    }
    return isSlotXInSearchArea && isSlotYInSearchArea;
};
const isInAbsoluteXRange = (slot, absoluteRange, { width }) => {
    const slotXCenter = roundResult$1((slot.x1 + slot.x2) / 2);
    if (absoluteRange.from === Anchor.Left) {
        return isValueInRange([width / -2 + absoluteRange.start, width / -2 + absoluteRange.end], slotXCenter);
    }
    else {
        return isValueInRange([width / 2 - absoluteRange.end, width / 2 - absoluteRange.start], slotXCenter);
    }
};
const isInAbsoluteYRange = (slot, absoluteRange, slotFormat) => {
    const { height, bodyHeight } = slotFormat;
    const slotYCenter = roundResult$1((slot.y1 + slot.y2) / 2);
    if (absoluteRange.from === Anchor.Bottom) {
        const shelfBodyBottom = height - bodyHeight;
        return isValueInRange([shelfBodyBottom + absoluteRange.start, shelfBodyBottom + absoluteRange.end], slotYCenter);
    }
    else {
        return isValueInRange([height - absoluteRange.end, height - absoluteRange.start], slotYCenter);
    }
};

const executeStrategy = (strategy, slotFormat) => {
    let shelfTopResults = [];
    let shelfBodyResults = [];
    const isStrategyFound = Object.keys(strategy).includes('id');
    if (isStrategyFound && strategy.mode === StrategyModes.Area) {
        shelfTopResults = executeAreaStrategyOnShelfTop(slotFormat, strategy);
        shelfBodyResults = executeAreaStrategyOnShelfBody(slotFormat, strategy);
    }
    return [...shelfTopResults, ...shelfBodyResults];
};

var type_01 = {
	main_plate_thickness: 18,
	front_plate_thickness: 16,
	back_plate_thickness: 12,
	insert_plate_thickness: 18,
	support_plate_thickness: 18,
	plinth_height: 109
};
var type_01_veneer = {
	main_plate_thickness: 18,
	front_plate_thickness: 18,
	back_plate_thickness: 18,
	insert_plate_thickness: 18,
	support_plate_thickness: 18,
	plinth_height: 109
};
var type_02 = {
	main_plate_thickness: 18,
	front_plate_thickness: 18,
	back_plate_thickness: 18,
	insert_plate_thickness: 18,
	support_plate_thickness: 18,
	long_legs_height: 109
};
var Type_03 = {
	OFFSET_BETWEEN: 2,
	OFFSET_FROM_FRAME: 4,
	OFFSET_FROM_MASKING_BAR: 6,
	OFFSET_FROM_SLAB: 10,
	OFFSET_TO_SLAB: 30,
	DRAWER_FRONT_HEIGHT_A: 150,
	DRAWER_FRONT_HEIGHT_S: 167,
	DRAWER_FRONT_HEIGHT_M: 224,
	DRAWER_FRONT_HEIGHT_L: 338,
	WARDROBE_HANDLE_POS: 1101,
	WARDROBE_HANDLE_H: 240,
	WARDROBE_HANDLE_W: 12,
	WARDROBE_HANDLE_D: 20,
	WARDROBE_HANDLE_OFFSET: 16
};
var standards = {
	type_01: type_01,
	type_01_veneer: type_01_veneer,
	type_02: type_02,
	Type_03: Type_03
};

var bodyTop = {
	height: 800,
	heightClamp: {
		min: 0,
		max: 1350
	}
};
var slots = {
	bodyTop: bodyTop
};

var TypeNames;
(function (TypeNames) {
    TypeNames["T01"] = "type_01";
    TypeNames["T02"] = "type_02";
    TypeNames["T01_VENEER"] = "type_01_veneer";
    TypeNames["T03"] = "type_03";
})(TypeNames || (TypeNames = {}));
var InfluentialModuleTypes;
(function (InfluentialModuleTypes) {
    InfluentialModuleTypes["Backs"] = "backs";
    InfluentialModuleTypes["Doors"] = "doors";
    InfluentialModuleTypes["Drawers"] = "drawers";
    InfluentialModuleTypes["Inserts"] = "inserts";
    InfluentialModuleTypes["Supports"] = "supports";
    InfluentialModuleTypes["CableManagement"] = "cable_management";
    InfluentialModuleTypes["DeskBeams"] = "desk_beams";
})(InfluentialModuleTypes || (InfluentialModuleTypes = {}));
var ModuleSubtypes;
(function (ModuleSubtypes) {
    ModuleSubtypes["H"] = "h";
    ModuleSubtypes["D"] = "d";
    ModuleSubtypes["V"] = "v";
})(ModuleSubtypes || (ModuleSubtypes = {}));
const getShelfType = (typeID = 0) => {
    const shelfTypes = {
        0: TypeNames.T01,
        1: TypeNames.T02,
        2: TypeNames.T01_VENEER,
        3: TypeNames.T03
    };
    return shelfTypes[typeID];
};
const accessData$1 = (shelf) => {
    if (Object.keys(shelf).length === 1) {
        return Object.values(shelf)[0];
    }
    else {
        return shelf;
    }
};
const getStandards = (shelfType) => standards[shelfType];
const readConfig = () => slots;

function prepareModuleInitData(moduleData) {
    const { x1, x2, y1, y2, z1, z2, subtype = null, rotation_value = 0, pull_value = 0 } = moduleData;
    const data = {
        x1: x1,
        x2: x2,
        y1: y1,
        y2: y2,
        z1: z1,
        z2: z2,
        subtype: subtype,
        rotation_value: rotation_value,
        pull_value: pull_value,
    };
    return data;
}
const createModule = (data) => ({
    x1: data.x1,
    x2: data.x2,
    y1: data.y1,
    y2: data.y2,
    z1: data.z1,
    z2: data.z2,
    subtype: data.subtype,
    rotation: data.rotation_value,
    pull: data.pull_value,
    center: getModuleCenter(data),
});
const getModuleCenter = (data) => {
    const isCableManagement = (data.x1 === data.x2 && data.y1 === data.y2 && data.z1 === data.z2);
    const centerX = isCableManagement ? data.x1 : data.x2 - Math.round(Math.abs((data.x2 - data.x1) / 2));
    const centerY = isCableManagement ? data.y1 : data.y2 - Math.round(Math.abs((data.y2 - data.y1) / 2));
    return { x: centerX, y: centerY };
};

const accessData = (shelfData) => {
    const shelf = {};
    if (Object.keys(shelfData).length === 1) {
        shelf.id = Object.keys(shelfData)[0];
        shelf.data = Object.values(shelfData)[0];
    }
    else {
        shelf.id = shelfData.id;
        shelf.data = shelfData;
    }
    return shelf;
};
const collectStructuralData = (data) => {
    const shelfData = accessData(data);
    const shelfSpecs = extractShelfSpecification(shelfData);
    const rowsBottomY = getAllRowsBottomY(shelfSpecs.horizontals);
    const horizontalsInRows = getHorizontalsInRows(rowsBottomY, shelfSpecs.horizontals);
    const stackStorage = getStackStorageConfigurationData(shelfSpecs.configurator_params);
    const verticalsInRows = getVerticalsInRows(rowsBottomY, shelfSpecs.verticals, shelfSpecs.halfMainPlate);
    const influentialModules = findInfluentialModules(shelfData.data);
    const workPlaceZones = getWorkPlaceZoneWithAnchors(shelfData.data);
    const structuralData = {
        rowsBottomY: rowsBottomY,
        rowCount: rowsBottomY.length - 1,
        horizontalsInRows: horizontalsInRows,
        verticalsInRows: verticalsInRows,
        influentialModuleTypes: InfluentialModuleTypes,
        influentialModules: influentialModules,
        workPlaceZones: workPlaceZones,
        stackStorage: stackStorage
    };
    return { ...shelfSpecs, ...structuralData };
};
const extractShelfSpecification = ({ id, data }) => {
    const shelfType = getShelfType(data.shelf_type);
    const standards = getStandards(shelfType);
    const bodyHeight = getShelfBodyHeight(data, standards);
    const specification = {
        id: id,
        width: data.width,
        widthRange: [data.width / -2, data.width / 2],
        height: data.height,
        bodyHeight: bodyHeight,
        heightRange: [data.height - bodyHeight, data.height],
        depth: data.depth,
        shelfType: shelfType,
        standards: standards,
        halfMainPlate: Math.floor(standards.main_plate_thickness / 2),
        category: data.category,
        material: data.material,
        horizontals: data.horizontals,
        verticals: data.verticals,
        configurator_params: data.configurator_params
    };
    return specification;
};
const sortArray = (array) => array.sort((a, b) => a - b);
const getShelfBodyHeight = (shelfData, standards) => {
    const hasShelfPlinth = Object.keys(shelfData).includes('plinth') && shelfData.plinth.length > 0;
    const hasShelfLongLegs = Object.keys(shelfData).includes('long_legs') && shelfData.long_legs.length > 0;
    if (hasShelfPlinth) {
        return shelfData.height - standards.plinth_height;
    }
    if (hasShelfLongLegs) {
        return shelfData.height - standards.long_legs_height;
    }
    else {
        return shelfData.height;
    }
};
const getAllRowsBottomY = (horizontals) => {
    const allHorizontalsY1 = horizontals.map((horizontal) => horizontal.y1);
    const uniqueHorizontalY1 = Array.from(new Set(allHorizontalsY1));
    return sortArray(uniqueHorizontalY1);
};
const getHorizontalsInRows = (rowsBottomY, horizontals) => {
    const horizontalsInRows = {};
    for (let rowBottomY of rowsBottomY) {
        const horizontalEnds = [];
        for (let horizontal of horizontals) {
            if (horizontal.y1 === rowBottomY) {
                horizontalEnds.push(horizontal.x1, horizontal.x2);
            }
        }
        horizontalsInRows[rowBottomY] = sortArray(horizontalEnds);
    }
    return horizontalsInRows;
};
const getStackStorageConfigurationData = (configurator_params) => {
    let stackStorage = { bottom: { active: false }, top: { active: false } };
    if (Object.keys(configurator_params).includes('stack')) {
        stackStorage.bottom = Object.values(configurator_params.stack.bottom)[0];
        stackStorage.top = Object.values(configurator_params.stack.top)[0];
    }
    return stackStorage;
};
const getVerticalsInRows = (rowsBottomY, verticals, halfMainPlate) => {
    const verticalsInRows = {};
    for (let rowBottomY of rowsBottomY) {
        const verticalsInRow = [];
        for (let vertical of verticals) {
            const verticalBottom = vertical.y1 - halfMainPlate;
            if (verticalBottom === rowBottomY) {
                const rowVertical = {
                    x: vertical.x1,
                    yTop: vertical.y2 + halfMainPlate
                };
                verticalsInRow.push(rowVertical);
            }
        }
        verticalsInRows[rowBottomY] = verticalsInRow.sort((a, b) => a.x - b.x);
    }
    return verticalsInRows;
};
const findInfluentialModules = (shelfData) => {
    const influentialModules = {};
    const typeNames = Object.values(InfluentialModuleTypes);
    for (let typeName of typeNames) {
        influentialModules[typeName] = getModules(shelfData, typeName);
    }
    return influentialModules;
};
const getModules = (shelfData, moduleType) => {
    const modules = (Object.keys(shelfData).includes(moduleType)) ? shelfData[moduleType] : [];
    return modules.map((moduleData) => {
        const initData = prepareModuleInitData(moduleData);
        return createModule(initData);
    });
};
const getWorkPlaceZoneWithAnchors = (shelfData) => {
    const workPlaceZones = findWorkPlaceZones(shelfData);
    const horizontalCableManagements = findHorizontalCableManagements(shelfData);
    return workPlaceZones.map((zone) => (findItemAnchors(zone, horizontalCableManagements)));
};
const findWorkPlaceZones = (shelfData) => {
    const moduleType = InfluentialModuleTypes.DeskBeams;
    const hasDeskBeams = hasModules(shelfData, moduleType);
    if (hasDeskBeams) {
        const deskBeams = shelfData[moduleType];
        return deskBeams.map((deskBeam) => ({
            x1: deskBeam.x1,
            x2: deskBeam.x2,
            width: Math.abs(deskBeam.x2 - deskBeam.x1),
            depth: shelfData.depth
        }));
    }
    return [];
};
const findHorizontalCableManagements = (shelfData) => {
    const moduleType = InfluentialModuleTypes.CableManagement;
    const hasCableManagements = hasModules(shelfData, moduleType);
    if (hasCableManagements) {
        return shelfData[moduleType].filter((cableManagement) => (Object.keys(cableManagement).includes('subtype') && cableManagement.subtype === ModuleSubtypes.H));
    }
    return [];
};
const hasModules = (shelfData, moduleType) => {
    return Object.keys(shelfData).includes(moduleType) && shelfData[moduleType].length > 0;
};
const findItemAnchors = (zone, horizontalCableManagements) => {
    let anchors = {
        center: {
            x: Math.round(zone.x1 + Math.abs(zone.x2 - zone.x1) / 2),
            z: Math.round(zone.depth / 2)
        }
    };
    if (horizontalCableManagements.length > 0) {
        horizontalCableManagements.forEach((cableMan) => {
            if (zone.x1 < cableMan.x1 && cableMan.x1 < anchors.center.x) {
                anchors.cableManagementLeft = {
                    x: cableMan.x1,
                    z: cableMan.z1
                };
            }
            if (anchors.center.x < cableMan.x1 && cableMan.x1 < zone.x2) {
                anchors.cableManagementRight = {
                    x: cableMan.x1,
                    z: cableMan.z1
                };
            }
        });
    }
    return { ...zone, anchors };
};

var OpeningClassification;
(function (OpeningClassification) {
    // first stage classification
    OpeningClassification["TopOpen"] = "topOpen";
    OpeningClassification["BodyRightOpen"] = "bodyRightOpen";
    OpeningClassification["BodyLeftOpen"] = "bodyLeftOpen";
    OpeningClassification["BodyClosed"] = "bodyClosed";
})(OpeningClassification || (OpeningClassification = {}));
const initiateOpening = (initData) => {
    return {
        x1: initData.x1,
        x2: initData.x2,
        y1: initData.y1,
        y2: initData.y2,
        z1: initData.z1,
        z2: initData.z2,
        classification: initData.classification,
        inRows: initData.inRows,
        isValid: checkValidity(initData),
        workPlaceZones: initData.workPlaceZones
    };
};
const checkValidity = (initData) => {
    return !Object.values(initData).includes(null);
};

const range = require('lodash/range');
const prepareOpeningsLayout = (shelfStructure) => {
    const openings = createOpeningsLayout(shelfStructure);
    return {
        shelf: shelfStructure,
        openings: openings
    };
};
const createOpeningsLayout = (shelf) => {
    const openings = [];
    const lastRowNum = shelf.rowsBottomY.length;
    for (let rowNum = 1; rowNum <= lastRowNum; rowNum++) {
        const rowBottomY = shelf.rowsBottomY[rowNum - 1];
        const bottomHorizontalsMinX = Math.min(...shelf.horizontalsInRows[rowBottomY]);
        const bottomHorizontalsMaxX = Math.max(...shelf.horizontalsInRows[rowBottomY]);
        let parameters = {
            rowBottomY: rowBottomY,
            rowTopY: shelf.rowsBottomY[rowNum],
            bottomHorizontalsDomainX: shelf.horizontalsInRows[rowBottomY],
            upperHorizontalsDomainX: [],
            classification: OpeningClassification.TopOpen,
            leftEnd: 0,
            rightEnd: 0
        };
        if (rowNum !== lastRowNum) {
            const rowVerticals = shelf.verticalsInRows[rowBottomY];
            for (let index = 0; index < rowVerticals.length; index++) {
                const vertical = rowVerticals[index];
                const lastVerticalIndex = rowVerticals.length - 1;
                const upperRowBottomY = shelf.rowsBottomY[rowNum];
                parameters.upperHorizontalsDomainX = shelf.horizontalsInRows[upperRowBottomY];
                if (index === 0 && bottomHorizontalsMinX < vertical.x - shelf.halfMainPlate) {
                    parameters.leftEnd = bottomHorizontalsMinX - shelf.halfMainPlate;
                    parameters.rightEnd = vertical.x;
                    parameters.classification = OpeningClassification.BodyLeftOpen;
                    addOpening(createOpening(parameters, shelf), openings);
                }
                if (index !== lastVerticalIndex) {
                    parameters.leftEnd = vertical.x;
                    parameters.rightEnd = rowVerticals[index + 1].x;
                    parameters.classification = OpeningClassification.BodyClosed;
                    addOpening(createOpening(parameters, shelf), openings);
                }
                if (index === lastVerticalIndex && bottomHorizontalsMaxX > vertical.x + shelf.halfMainPlate) {
                    parameters.leftEnd = vertical.x;
                    parameters.rightEnd = bottomHorizontalsMaxX + shelf.halfMainPlate;
                    parameters.classification = OpeningClassification.BodyRightOpen;
                    addOpening(createOpening(parameters, shelf), openings);
                }
            }
        }
        else {
            parameters.leftEnd = bottomHorizontalsMinX - shelf.halfMainPlate;
            parameters.rightEnd = bottomHorizontalsMaxX + shelf.halfMainPlate;
            addOpening(createOpening(parameters, shelf), openings);
        }
    }
    return openings;
};
const addOpening = (opening, openings) => {
    if (typeof opening !== 'undefined' && opening.isValid) {
        openings.push(opening);
    }
};
const createOpening = (params, shelf) => {
    const openingCenterX = params.rightEnd - Math.round(Math.abs((params.rightEnd - params.leftEnd) / 2));
    const hasBottomHorizontalInRow = isInsideDomain(openingCenterX, params.bottomHorizontalsDomainX);
    const hasTopHorizontalInRow = isInsideDomain(openingCenterX, params.upperHorizontalsDomainX);
    let x1 = null;
    let x2 = null;
    let y1 = null;
    let y2 = null;
    let inRows = [];
    let workPlaceZones = [];
    if (hasBottomHorizontalInRow) {
        x1 = params.leftEnd + shelf.halfMainPlate;
        x2 = params.rightEnd - shelf.halfMainPlate;
        y1 = params.rowBottomY + shelf.halfMainPlate;
        // single row opening
        if (hasTopHorizontalInRow) {
            y2 = params.rowTopY - shelf.halfMainPlate;
            inRows = getOpeningRows(y1, y2, shelf.rowsBottomY);
        }
        // shelf top opening
        else if (params.classification === OpeningClassification.TopOpen) {
            const slotConfig = readConfig();
            if (y1 <= slotConfig.bodyTop.heightClamp.max) {
                y2 = y1 + slotConfig.bodyTop.height;
                workPlaceZones = shelf.workPlaceZones;
            }
        }
        // multi-row opening
        else {
            const horizontalAboveY = findClosestHorizontalAbove(params.rowBottomY, openingCenterX, shelf);
            if (typeof horizontalAboveY !== 'undefined') {
                y2 = horizontalAboveY - shelf.halfMainPlate;
                inRows = getOpeningRows(y1, y2, shelf.rowsBottomY);
            }
        }
    }
    const openingInitData = {
        x1: x1,
        x2: x2,
        y1: y1,
        y2: y2,
        z1: 0,
        z2: shelf.depth,
        classification: params.classification,
        inRows: inRows,
        workPlaceZones: workPlaceZones
    };
    return initiateOpening(openingInitData);
};
const findClosestHorizontalAbove = (currentRowBottomY, XCenter, shelf) => {
    const allRowsY = shelf.rowsBottomY;
    const rowsYAbove = allRowsY.filter((rowBottomY) => currentRowBottomY < rowBottomY);
    return rowsYAbove.find((rowY) => isInsideDomain(XCenter, shelf.horizontalsInRows[rowY]));
};
const getOpeningRows = (y1, y2, rowsBottomY) => {
    const inRows = [];
    for (let row = 1; row <= rowsBottomY.length; row++) {
        if (rowsBottomY[row - 1] < y1 && y1 < rowsBottomY[row]) {
            inRows.push(row);
            for (let nextRow = row + 1; nextRow <= rowsBottomY.length; nextRow++) {
                if (rowsBottomY[nextRow - 1] < y2) {
                    inRows.push(nextRow);
                }
            }
        }
    }
    return inRows;
};
const isInsideDomain = (parameter, domainsArray) => {
    const evenDomainIndices = range(0, domainsArray.length, 2);
    const domainPairs = evenDomainIndices.map(i => [domainsArray[i], domainsArray[i + 1]]);
    const isParameterInsidePair = ([first, second]) => first <= parameter && parameter <= second;
    return domainPairs.some(isParameterInsidePair);
};

var ItemsOrientations;
(function (ItemsOrientations) {
    ItemsOrientations["FreeStanding"] = "freeStanding";
    ItemsOrientations["LeaningLeft"] = "leaningLeft";
    ItemsOrientations["LeaningRight"] = "leaningRight";
    ItemsOrientations["LeaningBack"] = "leaningBack";
})(ItemsOrientations || (ItemsOrientations = {}));
class Slot {
    constructor(opening) {
        this.x1 = opening.x1;
        this.x2 = opening.x2;
        this.y1 = opening.y1;
        this.y2 = opening.y2;
        this.z1 = opening.z1;
        this.z2 = opening.z2;
        this.bottomAnchor = this.findBottomAnchor();
        this.id = this.generateId(this.bottomAnchor.y, this.bottomAnchor.x);
        this.width = this.getDistance(this.x1, this.x2);
        this.height = this.getDistance(this.y1, this.y2);
        this.depth = this.getDistance(this.z1, this.z2);
        this.inRows = opening.inRows;
        this.classification = opening.classification;
        this.type = this.translateOpeningClassificationToSlotType();
        this.workPlaceZones = opening.workPlaceZones;
    }
    findBottomAnchor() {
        return {
            x: Math.round((this.x1 + this.x2) / 2),
            y: this.y1,
            z: Math.round((this.z1 + this.z2) / 2)
        };
    }
    getDistance(val1, val2) {
        return Math.abs(val1 - val2);
    }
    translateOpeningClassificationToSlotType() {
        switch (this.classification) {
            case OpeningClassification.BodyLeftOpen:
                return SLOT_TYPE.EXPO.LeftOpen;
            case OpeningClassification.BodyRightOpen:
                return SLOT_TYPE.EXPO.RightOpen;
            case OpeningClassification.TopOpen:
                return SLOT_TYPE.EXPO.Top;
            default:
                return OpeningClassification.BodyClosed;
        }
    }
    generateId(valueA, valueB) {
        return `${valueA}${valueB}`;
    }
    detectModulesInSlot(influentialModules) {
        const moduleTypesInSlot = new Set();
        for (let moduleType of Object.keys(influentialModules)) {
            for (let module of influentialModules[moduleType]) {
                if (this.isModuleInSlot(module)) {
                    if (moduleType === InfluentialModuleTypes.Doors) {
                        if (module.rotation !== 0) {
                            moduleTypesInSlot.add(moduleType);
                        }
                        else {
                            moduleTypesInSlot.add(ClosedOpeningTypes.Doors);
                        }
                    }
                    else if (moduleType === InfluentialModuleTypes.Drawers) {
                        if (module.pull !== 0) {
                            moduleTypesInSlot.add(moduleType);
                        }
                        else {
                            moduleTypesInSlot.add(ClosedOpeningTypes.Drawers);
                        }
                    }
                    else {
                        moduleTypesInSlot.add(moduleType);
                    }
                }
            }
        }
        return moduleTypesInSlot;
    }
    isModuleInSlot(module) {
        const isInWidthRange = this.x1 < module.center.x && module.center.x < this.x2;
        const isInHeightRange = this.y1 < module.center.y && module.center.y < this.y2;
        return isInWidthRange && isInHeightRange;
    }
    specifySlotType(moduleTypesInSlot) {
        if (moduleTypesInSlot.has(ClosedOpeningTypes.Doors)) {
            this.type = ClosedOpeningTypes.Doors;
        }
        else if (moduleTypesInSlot.has(ClosedOpeningTypes.Drawers)) {
            this.type = ClosedOpeningTypes.Drawers;
        }
        else {
            if (moduleTypesInSlot.has(InfluentialModuleTypes.Doors) || moduleTypesInSlot.has(InfluentialModuleTypes.Drawers)) {
                this.specifyStorageType(moduleTypesInSlot);
            }
            else {
                this.specifyExpoType(moduleTypesInSlot);
            }
        }
    }
    specifyStorageType(moduleTypesInSlot) {
        if (moduleTypesInSlot.has(InfluentialModuleTypes.Doors)) {
            this.type = SLOT_TYPE.STORAGE.Door;
            if (moduleTypesInSlot.has(InfluentialModuleTypes.Inserts)) {
                this.type = SLOT_TYPE.STORAGE.Inserts;
                if (moduleTypesInSlot.has(InfluentialModuleTypes.CableManagement)) {
                    this.type = SLOT_TYPE.STORAGE.InsertsCable;
                }
            }
            else if (moduleTypesInSlot.has(InfluentialModuleTypes.CableManagement)) {
                this.type = SLOT_TYPE.STORAGE.DoorCable;
            }
        }
        else if (moduleTypesInSlot.has(InfluentialModuleTypes.Drawers)) {
            this.type = SLOT_TYPE.STORAGE.Drawer;
        }
    }
    specifyExpoType(moduleTypesInSlot) {
        if (moduleTypesInSlot.has(InfluentialModuleTypes.Backs)) {
            this.type = SLOT_TYPE.EXPO.Back;
            if (moduleTypesInSlot.has(InfluentialModuleTypes.Inserts)) {
                this.type = SLOT_TYPE.EXPO.Inserts;
                if (moduleTypesInSlot.has(InfluentialModuleTypes.CableManagement)) {
                    this.type = SLOT_TYPE.EXPO.InsertsCable;
                }
            }
            else if (moduleTypesInSlot.has(InfluentialModuleTypes.CableManagement)) {
                this.type = SLOT_TYPE.EXPO.BackCable;
            }
        }
        else if (moduleTypesInSlot.has(InfluentialModuleTypes.Supports)) {
            this.type = SLOT_TYPE.EXPO.Support;
        }
        else {
            this.type = SLOT_TYPE.EXPO.Hollow;
        }
    }
    updateSlotDepth(moduleTypesInSlot, standards) {
        if (moduleTypesInSlot.has(InfluentialModuleTypes.Backs)) {
            this.z1 += standards.back_plate_thickness;
        }
        if (moduleTypesInSlot.has(InfluentialModuleTypes.Doors) || moduleTypesInSlot.has(InfluentialModuleTypes.Drawers)) {
            this.z2 -= standards.front_plate_thickness;
        }
        if (moduleTypesInSlot.has(InfluentialModuleTypes.Supports)) {
            this.z1 += standards.support_plate_thickness;
        }
        this.depth = Math.abs(this.z1 - this.z2);
    }
    findSubdivisions(influentialModules) {
        const insertsInSlot = this.getModulesInSlot(influentialModules[InfluentialModuleTypes.Inserts]);
        const subSlotsCoordinates = this.findSubSlotsCoordinates(insertsInSlot);
        const subSlots = this.createSubSlots(subSlotsCoordinates);
        const cableManagementsInSlot = this.getModulesInSlot(influentialModules[InfluentialModuleTypes.CableManagement]);
        this.updateSubSlotsTypes(subSlots, cableManagementsInSlot);
        return subSlots;
    }
    getModulesInSlot(modules) {
        const modulesInSlot = [];
        for (let module of modules) {
            if (this.isModuleInSlot(module)) {
                modulesInSlot.push(module);
            }
        }
        return modulesInSlot;
    }
    findSubSlotsCoordinates(insertsInSlot) {
        const allSubSlotCoors = {
            x1: this.getAllCoors(insertsInSlot, 'x1'),
            x2: this.getAllCoors(insertsInSlot, 'x2'),
            y1: this.getAllCoors(insertsInSlot, 'y1'),
            y2: this.getAllCoors(insertsInSlot, 'y2'),
        };
        return allSubSlotCoors;
    }
    sortArray(array) {
        return array.sort((a, b) => a - b);
    }
    getAllCoors(insertsInSlot, coordinateName) {
        let allCoors = [];
        if (coordinateName === 'x1') {
            allCoors = [this.x1];
            for (let insert of insertsInSlot) {
                if (insert.subtype === 'v') {
                    allCoors.push(insert.x2);
                }
            }
        }
        if (coordinateName === 'x2') {
            allCoors = [this.x2];
            for (let insert of insertsInSlot) {
                if (insert.subtype === 'v') {
                    allCoors.push(insert.x1);
                }
            }
        }
        if (coordinateName === 'y1') {
            allCoors = [this.y1];
            for (let insert of insertsInSlot) {
                if (insert.subtype === 'h') {
                    allCoors.push(insert.y2);
                }
            }
        }
        if (coordinateName === 'y2') {
            allCoors = [this.y2];
            for (let insert of insertsInSlot) {
                if (insert.subtype === 'h') {
                    allCoors.push(insert.y1);
                }
            }
        }
        return this.sortArray(allCoors);
    }
    createSubSlots(subSlotsCoordinates) {
        const subSlots = [];
        let iteratorX1 = subSlotsCoordinates.x1.values();
        let iteratorX2 = subSlotsCoordinates.x2.values();
        let iteratorY1 = subSlotsCoordinates.y1.values();
        let iteratorY2 = subSlotsCoordinates.y2.values();
        const coorsCount = [];
        for (let key of Object.keys(subSlotsCoordinates)) {
            coorsCount.push(subSlotsCoordinates[key].length);
        }
        const iterationCount = Math.max(...coorsCount);
        for (let iteration = 0; iteration < iterationCount; iteration++) {
            const x1 = this.findCoordinate(iteratorX1, this.x1);
            const x2 = this.findCoordinate(iteratorX2, this.x2);
            const y1 = this.findCoordinate(iteratorY1, this.y1);
            const y2 = this.findCoordinate(iteratorY2, this.y2);
            const subSlot = {
                x1: x1,
                x2: x2,
                y1: y1,
                y2: y2,
                z1: this.z1,
                z2: this.z2,
                bottomAnchor: {
                    x: Math.round((x1 + x2) / 2),
                    y: y1,
                    z: Math.round((this.z1 + this.z2) / 2)
                },
                id: this.generateId(y1, Math.round((x1 + x2) / 2)),
                width: this.getDistance(x1, x2),
                height: this.getDistance(y1, y2),
                depth: this.getDistance(this.z1, this.z2),
                inRows: this.inRows,
                type: this.type
            };
            subSlots.push(subSlot);
        }
        return subSlots;
    }
    findCoordinate(iterator, defaultValue) {
        const nextValue = iterator.next().value;
        if (typeof nextValue !== 'undefined') {
            return nextValue;
        }
        return defaultValue;
    }
    updateSubSlotsTypes(subSlots, cableManagementsInSlot) {
        subSlots.forEach((subSlot) => {
            if (subSlot.type === SLOT_TYPE.EXPO.InsertsCable) {
                subSlot.type = SLOT_TYPE.EXPO.Inserts;
            }
            if (subSlot.type === SLOT_TYPE.STORAGE.InsertsCable) {
                subSlot.type = SLOT_TYPE.STORAGE.Inserts;
            }
        });
        if (cableManagementsInSlot.length > 0) {
            for (let subSlot of subSlots) {
                for (let cableManagement of cableManagementsInSlot) {
                    const isInWidthRange = subSlot.x1 < cableManagement.center.x && cableManagement.center.x < subSlot.x2;
                    const isInHeightRange = subSlot.y1 < cableManagement.center.y && cableManagement.center.y < subSlot.y2;
                    if (isInWidthRange && isInHeightRange) {
                        if (subSlot.type === SLOT_TYPE.EXPO.Inserts) {
                            subSlot.type = SLOT_TYPE.EXPO.InsertsCable;
                        }
                        else if (subSlot.type === SLOT_TYPE.STORAGE.Inserts) {
                            subSlot.type = SLOT_TYPE.STORAGE.InsertsCable;
                        }
                    }
                }
            }
        }
    }
}

const interpretShelf = (shelfLayout) => {
    const { slots, closedStorageCount } = createSlots(shelfLayout);
    const slotCount = slots.length;
    const openingsCount = shelfLayout.openings.length;
    const expoSlotsCount = calcExpoSlotsCount(slots);
    const exposurePercentage = calcExposurePercentage(openingsCount, closedStorageCount);
    const expoSlotsPercentage = calcExpoSlotPercentage(expoSlotsCount, slotCount);
    return {
        shelf: shelfLayout.shelf,
        slots: slots,
        slotCount: slotCount,
        closedStorageCount: closedStorageCount,
        exposurePercentage: exposurePercentage,
        expoSlotsCount: expoSlotsCount,
        storageSlotsCount: roundResult(slotCount - expoSlotsCount),
        expoSlotsPercentage: expoSlotsPercentage,
        storageSlotsPercentage: roundResult(100 - expoSlotsPercentage)
    };
};
const createSlots = ({ shelf, openings }) => {
    let closedStorageCounter = 0;
    const slots = [];
    for (let opening of openings) {
        const slot = new Slot(opening);
        // analyze solid openings in shelf body (closed from all sides)
        if (slot.classification === OpeningClassification.BodyClosed) {
            const moduleTypesInSlot = slot.detectModulesInSlot(shelf.influentialModules);
            slot.specifySlotType(moduleTypesInSlot);
            slot.updateSlotDepth(moduleTypesInSlot, shelf.standards);
            // analyze further if slot is not closed storage
            if (slot.type !== ClosedOpeningTypes.Doors && slot.type !== ClosedOpeningTypes.Drawers) {
                // search for potential slot subdivisions
                if (moduleTypesInSlot.has(InfluentialModuleTypes.Inserts)) {
                    const subSlots = slot.findSubdivisions(shelf.influentialModules);
                    slots.push(...subSlots);
                }
                else {
                    slots.push(slot);
                }
            }
            else {
                closedStorageCounter++;
            }
        }
        else {
            // add non-solid slots --> half open types or top slot
            slots.push(slot);
        }
    }
    const createdSlots = [...slots];
    return {
        slots: slots.map(slot => ({ ...slot, ...createContextualDataForSlot(slot, shelf, createdSlots) })),
        closedStorageCount: closedStorageCounter
    };
};
const calcExposurePercentage = (openingsCount, closedStorageCount) => {
    const exposedOpeningsCount = openingsCount - closedStorageCount;
    return roundResult(exposedOpeningsCount / openingsCount * 100);
};
const calcExpoSlotsCount = (slots) => {
    const expoSlots = slots.filter(({ type }) => (Object.values(SLOT_TYPE.EXPO).includes(type)));
    return expoSlots.length;
};
const calcExpoSlotPercentage = (expoSlotsCount, slotCount) => {
    return roundResult(expoSlotsCount / slotCount * 100);
};
const hasCableManagement = ({ type }) => {
    const cableManagementTypes = [
        SLOT_TYPE.EXPO.BackCable,
        SLOT_TYPE.EXPO.InsertsCable,
        SLOT_TYPE.STORAGE.DoorCable,
        SLOT_TYPE.STORAGE.InsertsCable
    ];
    return cableManagementTypes.includes(type);
};
const extractItemsOrientations = ({ type }) => {
    const orientations = Object.values(ItemsOrientations);
    let excluded = [];
    if (type === SLOT_TYPE.EXPO.Hollow || type === SLOT_TYPE.EXPO.Support) {
        excluded = [ItemsOrientations.LeaningBack];
    }
    else if (type === SLOT_TYPE.EXPO.LeftOpen) {
        excluded = [ItemsOrientations.LeaningBack, ItemsOrientations.LeaningLeft];
    }
    else if (type === SLOT_TYPE.EXPO.RightOpen) {
        excluded = [ItemsOrientations.LeaningBack, ItemsOrientations.LeaningRight];
    }
    else if (type === SLOT_TYPE.EXPO.Top) {
        excluded = [ItemsOrientations.LeaningLeft, ItemsOrientations.LeaningRight];
    }
    return orientations.filter(orientation => !excluded.includes(orientation));
};
const createContextualDataForSlot = (slot, shelf, allSlots) => ({
    slotCountInRow: calcSlotsCountInRow(slot, allSlots),
    indexInRow: calcSlotIndexInRow(slot, allSlots),
    positionalContext: calcPositionalContext(slot, shelf),
    itemsOrientations: extractItemsOrientations(slot),
    hasCableManagement: hasCableManagement(slot)
});
const calcPositionalContext = (slot, { widthRange, heightRange }) => ({
    width: calcDimensionContext(slot.x1, slot.x2, widthRange),
    height: calcDimensionContext(slot.y1, slot.y2, heightRange),
});
const calcDimensionContext = (startCoordinate, endCoordinate, dimensionRange) => {
    const slotStartPercent = calcValuePercentInRange(startCoordinate, dimensionRange);
    const slotEndPercent = calcValuePercentInRange(endCoordinate, dimensionRange);
    return {
        start: slotStartPercent,
        end: slotEndPercent,
        center: calcAverage([slotStartPercent, slotEndPercent])
    };
};
const calcSlotsCountInRow = (slot, allSlots) => {
    const slotsInRow = findAllSlotsInRow(slot.inRows[0], allSlots);
    return slotsInRow.length;
};
const findAllSlotsInRow = (row, allSlots) => {
    return allSlots.filter((anotherSlot) => (anotherSlot.inRows[0] === row));
};
const calcSlotIndexInRow = (slot, allSlots) => {
    const slotsX1InRow = findAllSlotsInRow(slot.inRows[0], allSlots).map((slotInRow) => slotInRow.x1);
    return sortSlotsByX(slotsX1InRow).indexOf(slot.x1);
};
const sortSlotsByX = (slotsInRow) => slotsInRow.sort((a, b) => a.x1 - b.x1);
const calcValuePercentInRange = (val, range) => {
    const percent = ((val - Math.min(...range)) * 100) / (Math.max(...range) - Math.min(...range));
    return roundResult(percent);
};
const calcAverage = (range) => {
    const sum = range.reduce((a, b) => a + b, 0);
    return roundResult(sum / range.length);
};
const roundResult = (result) => {
    const decimalPlacesPrecision = 10;
    return Math.round(result * decimalPlacesPrecision) / decimalPlacesPrecision;
};

const composeSlotFormat = (data) => {
    return {
        shelfId: data.shelf.id,
        shelfType: data.shelf.shelfType,
        shelfMaterial: data.shelf.material,
        shelfCategory: data.shelf.category,
        width: data.shelf.width,
        height: data.shelf.height,
        bodyHeight: data.shelf.bodyHeight,
        depth: data.shelf.depth,
        rowCount: data.shelf.rowCount,
        exposurePercentage: data.exposurePercentage,
        slotsCount: {
            expo: data.expoSlotsCount,
            storage: data.storageSlotsCount,
            total: data.slotCount
        },
        slotsRatio: {
            expo: data.expoSlotsPercentage,
            storage: data.storageSlotsPercentage
        },
        closedStorageCount: data.closedStorageCount,
        stackStorage: data.shelf.stackStorage,
        slots: composeSlotsData(data.slots)
    };
};
const composeSlotsData = (slotsData) => {
    return slotsData.map((slotData) => ({
        id: slotData.id,
        x1: slotData.x1,
        x2: slotData.x2,
        y1: slotData.y1,
        y2: slotData.y2,
        z1: slotData.z1,
        z2: slotData.z2,
        width: slotData.width,
        height: slotData.height,
        depth: slotData.depth,
        type: slotData.type,
        positionalContext: slotData.positionalContext,
        inRows: slotData.inRows,
        slotCountInRow: slotData.slotCountInRow,
        indexInRow: slotData.indexInRow,
        bottomAnchor: slotData.bottomAnchor,
        itemsOrientations: slotData.itemsOrientations,
        hasCableManagement: slotData.hasCableManagement,
        workPlaceZones: slotData.workPlaceZones
    }));
};

const extractSlots = (shelf) => {
    // access data depending on data format (id: jetty || jetty)
    const shelfData = accessData$1(shelf);
    if (getShelfType(shelfData.shelf_type) !== TypeNames.T03) {
        // gather shelf generic data and extract structural data
        const shelfStructure = collectStructuralData(shelfData);
        const openingsLayout = prepareOpeningsLayout(shelfStructure);
        // interpret shelf openings --> create slots
        const interpretedShelf = interpretShelf(openingsLayout);
        return composeSlotFormat(interpretedShelf);
    }
    else {
        console.log(`=== shelves: ${TypeNames.T03} are not supported ===`);
    }
};

function dummySolver(item, portal) {

  let size = item.item_space.dimensions_mm;

  // drop if it's not fitting
  if (size.widthx > portal.item.size.width.max) return false;
  if (size.widthx < portal.item.size.width.min) return false;
  if (size.heighty < portal.item.size.height.min) return false;
  if (size.heighty > portal.item.size.height.max) return false;
  if (size.depthz < portal.item.size.depth.min) return false;
  if (size.depthz > portal.item.size.depth.max) return false;
  // filter through  tags...


  // TODO: filter on special tags priorites
  // #_ID_
  // ._CATEGORY_
  // own props
  // 

  //  

  return true;
}

var categories = [
	"ceramics",
	"books",
	"boxes",
	"electronics",
	"lamps",
	"shoes",
	"deco",
	"plants",
	"flowers",
	"accessories",
	"posters",
	"vinyl"
];
var items = [
	{
		item_identity: {
			item_id: "acces0003",
			objfilename: "acces0003",
			bakedtexturefilename: "acces0003",
			blendfilename: "acces0003",
			category: "accessories",
			source_licence: "rubics"
		},
		item_space: {
			dimensions_mm: {
				widthx: 79,
				heighty: 56,
				depthz: 79
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0010",
			objfilename: "acces0010",
			bakedtexturefilename: "acces0010",
			blendfilename: "acces0010",
			category: "accessories",
			source_licence: "binders"
		},
		item_space: {
			dimensions_mm: {
				widthx: 159,
				heighty: 320,
				depthz: 290
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0011",
			objfilename: "acces0011",
			bakedtexturefilename: "acces0011",
			blendfilename: "acces0011",
			category: "accessories",
			source_licence: "boxes"
		},
		item_space: {
			dimensions_mm: {
				widthx: 171,
				heighty: 247,
				depthz: 229
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0012",
			objfilename: "acces0012",
			bakedtexturefilename: "acces0012",
			blendfilename: "acces0012",
			category: "accessories",
			source_licence: "jewellery_case"
		},
		item_space: {
			dimensions_mm: {
				widthx: 250,
				heighty: 108,
				depthz: 250
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0013",
			objfilename: "acces0013",
			bakedtexturefilename: "acces0013",
			blendfilename: "acces0013",
			category: "accessories",
			source_licence: "clock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 192,
				heighty: 192,
				depthz: 51
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0014",
			objfilename: "acces0014",
			bakedtexturefilename: "acces0014",
			blendfilename: "acces0014",
			category: "accessories",
			source_licence: "frames"
		},
		item_space: {
			dimensions_mm: {
				widthx: 383,
				heighty: 343,
				depthz: 73
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0015",
			objfilename: "acces0015",
			bakedtexturefilename: "acces0015",
			blendfilename: "acces0015",
			category: "accessories",
			source_licence: "binders"
		},
		item_space: {
			dimensions_mm: {
				widthx: 367,
				heighty: 310,
				depthz: 248
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0016",
			objfilename: "acces0016",
			bakedtexturefilename: "acces0016",
			blendfilename: "acces0016",
			category: "accessories",
			source_licence: "binders"
		},
		item_space: {
			dimensions_mm: {
				widthx: 275,
				heighty: 310,
				depthz: 248
			}
		}
	},
	{
		item_identity: {
			item_id: "acces0017",
			objfilename: "acces0017",
			bakedtexturefilename: "acces0017",
			blendfilename: "acces0017",
			category: "accessories",
			source_licence: "wooden_sculpture"
		},
		item_space: {
			dimensions_mm: {
				widthx: 109,
				heighty: 126,
				depthz: 113
			}
		}
	},
	{
		item_identity: {
			item_id: "book0007",
			objfilename: "book0007",
			bakedtexturefilename: "book0007",
			blendfilename: "book0007",
			category: "books",
			source_licence: "horizontal_many"
		},
		item_space: {
			dimensions_mm: {
				widthx: 297,
				heighty: 103,
				depthz: 252
			}
		}
	},
	{
		item_identity: {
			item_id: "book0021",
			objfilename: "book0021",
			bakedtexturefilename: "book0021",
			blendfilename: "book0021",
			category: "books",
			source_licence: "horizontal"
		},
		item_space: {
			dimensions_mm: {
				widthx: 339,
				heighty: 48,
				depthz: 250
			}
		}
	},
	{
		item_identity: {
			item_id: "book0022",
			objfilename: "book0022",
			bakedtexturefilename: "book0022",
			blendfilename: "book0022",
			category: "books",
			source_licence: "vertical_many"
		},
		item_space: {
			dimensions_mm: {
				widthx: 225,
				heighty: 339,
				depthz: 224
			}
		}
	},
	{
		item_identity: {
			item_id: "book0023",
			objfilename: "book0023",
			bakedtexturefilename: "book0023",
			blendfilename: "book0023",
			category: "books",
			source_licence: "vertical_many"
		},
		item_space: {
			dimensions_mm: {
				widthx: 238,
				heighty: 260,
				depthz: 186
			}
		}
	},
	{
		item_identity: {
			item_id: "book0024",
			objfilename: "book0024",
			bakedtexturefilename: "book0024",
			blendfilename: "book0024",
			category: "books",
			source_licence: "vertical_many"
		},
		item_space: {
			dimensions_mm: {
				widthx: 115,
				heighty: 338,
				depthz: 252
			}
		}
	},
	{
		item_identity: {
			item_id: "book0025",
			objfilename: "book0025",
			bakedtexturefilename: "book0025",
			blendfilename: "book0025",
			category: "books",
			source_licence: "horizontal"
		},
		item_space: {
			dimensions_mm: {
				widthx: 381,
				heighty: 61,
				depthz: 294
			}
		}
	},
	{
		item_identity: {
			item_id: "book0029",
			objfilename: "book0029",
			bakedtexturefilename: "book0029",
			blendfilename: "book0029",
			category: "books",
			source_licence: "horizontal_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 300,
				heighty: 137,
				depthz: 189
			}
		}
	},
	{
		item_identity: {
			item_id: "book0030",
			objfilename: "book0030",
			bakedtexturefilename: "book0030",
			blendfilename: "book0030",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 181,
				heighty: 270,
				depthz: 222
			}
		}
	},
	{
		item_identity: {
			item_id: "book0031",
			objfilename: "book0031",
			bakedtexturefilename: "book0031",
			blendfilename: "book0031",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 371,
				heighty: 290,
				depthz: 229
			}
		}
	},
	{
		item_identity: {
			item_id: "book0032",
			objfilename: "book0032",
			bakedtexturefilename: "book0032",
			blendfilename: "book0032",
			category: "books",
			source_licence: "horizontal_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 297,
				heighty: 65,
				depthz: 242
			}
		}
	},
	{
		item_identity: {
			item_id: "book0033",
			objfilename: "book0033",
			bakedtexturefilename: "book0033",
			blendfilename: "book0033",
			category: "books",
			source_licence: "horizontal"
		},
		item_space: {
			dimensions_mm: {
				widthx: 416,
				heighty: 58,
				depthz: 257
			}
		}
	},
	{
		item_identity: {
			item_id: "book0034",
			objfilename: "book0034",
			bakedtexturefilename: "book0034",
			blendfilename: "book0034",
			category: "books",
			source_licence: "vertical"
		},
		item_space: {
			dimensions_mm: {
				widthx: 75,
				heighty: 418,
				depthz: 257
			}
		}
	},
	{
		item_identity: {
			item_id: "book0035",
			objfilename: "book0035",
			bakedtexturefilename: "book0035",
			blendfilename: "book0035",
			category: "books",
			source_licence: "horizontal"
		},
		item_space: {
			dimensions_mm: {
				widthx: 300,
				heighty: 89,
				depthz: 238
			}
		}
	},
	{
		item_identity: {
			item_id: "book0036",
			objfilename: "book0036",
			bakedtexturefilename: "book0036",
			blendfilename: "book0036",
			category: "books",
			source_licence: "vertical_many"
		},
		item_space: {
			dimensions_mm: {
				widthx: 278,
				heighty: 299,
				depthz: 241
			}
		}
	},
	{
		item_identity: {
			item_id: "book0037",
			objfilename: "book0037",
			bakedtexturefilename: "book0037",
			blendfilename: "book0037",
			category: "books",
			source_licence: "horizontal_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 285,
				heighty: 65,
				depthz: 214
			}
		}
	},
	{
		item_identity: {
			item_id: "book0038",
			objfilename: "book0038",
			bakedtexturefilename: "book0038",
			blendfilename: "book0038",
			category: "books",
			source_licence: "horizontal_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 279,
				heighty: 96,
				depthz: 219
			}
		}
	},
	{
		item_identity: {
			item_id: "book0039",
			objfilename: "book0039",
			bakedtexturefilename: "book0039",
			blendfilename: "book0039",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 322,
				heighty: 286,
				depthz: 238
			}
		}
	},
	{
		item_identity: {
			item_id: "book0040",
			objfilename: "book0040",
			bakedtexturefilename: "book0040",
			blendfilename: "book0040",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 275,
				heighty: 281,
				depthz: 240
			}
		}
	},
	{
		item_identity: {
			item_id: "book0042",
			objfilename: "book0042",
			bakedtexturefilename: "book0042",
			blendfilename: "book0042",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 370,
				heighty: 300,
				depthz: 247
			}
		}
	},
	{
		item_identity: {
			item_id: "book0044",
			objfilename: "book0044",
			bakedtexturefilename: "book0044",
			blendfilename: "book0044",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 184,
				heighty: 289,
				depthz: 240
			}
		}
	},
	{
		item_identity: {
			item_id: "book0045",
			objfilename: "book0045",
			bakedtexturefilename: "book0045",
			blendfilename: "book0045",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 342,
				heighty: 226,
				depthz: 158
			}
		}
	},
	{
		item_identity: {
			item_id: "book0046",
			objfilename: "book0046",
			bakedtexturefilename: "book0046",
			blendfilename: "book0046",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 164,
				heighty: 241,
				depthz: 163
			}
		}
	},
	{
		item_identity: {
			item_id: "book0047",
			objfilename: "book0047",
			bakedtexturefilename: "book0047",
			blendfilename: "book0047",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 260,
				heighty: 240,
				depthz: 165
			}
		}
	},
	{
		item_identity: {
			item_id: "book0051",
			objfilename: "book0051",
			bakedtexturefilename: "book0051",
			blendfilename: "book0051",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 145,
				heighty: 242,
				depthz: 164
			}
		}
	},
	{
		item_identity: {
			item_id: "book0052",
			objfilename: "book0052",
			bakedtexturefilename: "book0052",
			blendfilename: "book0052",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 218,
				heighty: 243,
				depthz: 165
			}
		}
	},
	{
		item_identity: {
			item_id: "book0054",
			objfilename: "book0054",
			bakedtexturefilename: "book0054",
			blendfilename: "book0054",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 135,
				heighty: 436,
				depthz: 269
			}
		}
	},
	{
		item_identity: {
			item_id: "book0055",
			objfilename: "book0055",
			bakedtexturefilename: "book0055",
			blendfilename: "book0055",
			category: "books",
			source_licence: "horizontal_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 299,
				heighty: 159,
				depthz: 238
			}
		}
	},
	{
		item_identity: {
			item_id: "book0056",
			objfilename: "book0056",
			bakedtexturefilename: "book0056",
			blendfilename: "book0056",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 531,
				heighty: 300,
				depthz: 247
			}
		}
	},
	{
		item_identity: {
			item_id: "book0057",
			objfilename: "book0057",
			bakedtexturefilename: "book0057",
			blendfilename: "book0057",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 226,
				heighty: 261,
				depthz: 195
			}
		}
	},
	{
		item_identity: {
			item_id: "book0058",
			objfilename: "book0058",
			bakedtexturefilename: "book0058",
			blendfilename: "book0058",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 480,
				heighty: 258,
				depthz: 180
			}
		}
	},
	{
		item_identity: {
			item_id: "book0059",
			objfilename: "book0059",
			bakedtexturefilename: "book0059",
			blendfilename: "book0059",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 450,
				heighty: 257,
				depthz: 194
			}
		}
	},
	{
		item_identity: {
			item_id: "book0060",
			objfilename: "book0060",
			bakedtexturefilename: "book0060",
			blendfilename: "book0060",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 440,
				heighty: 314,
				depthz: 233
			}
		}
	},
	{
		item_identity: {
			item_id: "book0061",
			objfilename: "book0061",
			bakedtexturefilename: "book0061",
			blendfilename: "book0061",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 211,
				heighty: 267,
				depthz: 185
			}
		}
	},
	{
		item_identity: {
			item_id: "book0062",
			objfilename: "book0062",
			bakedtexturefilename: "book0062",
			blendfilename: "book0062",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 434,
				heighty: 227,
				depthz: 184
			}
		}
	},
	{
		item_identity: {
			item_id: "book0063",
			objfilename: "book0063",
			bakedtexturefilename: "book0063",
			blendfilename: "book0063",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 209,
				heighty: 331,
				depthz: 238
			}
		}
	},
	{
		item_identity: {
			item_id: "book0064",
			objfilename: "book0064",
			bakedtexturefilename: "book0064",
			blendfilename: "book0064",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 405,
				heighty: 312,
				depthz: 234
			}
		}
	},
	{
		item_identity: {
			item_id: "book0065",
			objfilename: "book0065",
			bakedtexturefilename: "book0065",
			blendfilename: "book0065",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 273,
				heighty: 309,
				depthz: 225
			}
		}
	},
	{
		item_identity: {
			item_id: "book0066",
			objfilename: "book0066",
			bakedtexturefilename: "book0066",
			blendfilename: "book0066",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 131,
				heighty: 435,
				depthz: 278
			}
		}
	},
	{
		item_identity: {
			item_id: "book0067",
			objfilename: "book0067",
			bakedtexturefilename: "book0067",
			blendfilename: "book0067",
			category: "books",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 164,
				heighty: 267,
				depthz: 185
			}
		}
	},
	{
		item_identity: {
			item_id: "box0011",
			objfilename: "box0011",
			bakedtexturefilename: "box0011",
			blendfilename: "box0011",
			category: "boxes",
			source_licence: "single"
		},
		item_space: {
			dimensions_mm: {
				widthx: 311,
				heighty: 303,
				depthz: 280
			}
		}
	},
	{
		item_identity: {
			item_id: "box0013",
			objfilename: "box0013",
			bakedtexturefilename: "box0013",
			blendfilename: "box0013",
			category: "boxes",
			source_licence: "single"
		},
		item_space: {
			dimensions_mm: {
				widthx: 390,
				heighty: 243,
				depthz: 255
			}
		}
	},
	{
		item_identity: {
			item_id: "box0014",
			objfilename: "box0014",
			bakedtexturefilename: "box0014",
			blendfilename: "box0014",
			category: "boxes",
			source_licence: "single"
		},
		item_space: {
			dimensions_mm: {
				widthx: 284,
				heighty: 142,
				depthz: 166
			}
		}
	},
	{
		item_identity: {
			item_id: "box0015",
			objfilename: "box0015",
			bakedtexturefilename: "box0015",
			blendfilename: "box0015",
			category: "boxes",
			source_licence: "single"
		},
		item_space: {
			dimensions_mm: {
				widthx: 245,
				heighty: 205,
				depthz: 229
			}
		}
	},
	{
		item_identity: {
			item_id: "box0016",
			objfilename: "box0016",
			bakedtexturefilename: "box0016",
			blendfilename: "box0016",
			category: "boxes",
			source_licence: "stack"
		},
		item_space: {
			dimensions_mm: {
				widthx: 352,
				heighty: 179,
				depthz: 169
			}
		}
	},
	{
		item_identity: {
			item_id: "box0017",
			objfilename: "box0017",
			bakedtexturefilename: "box0017",
			blendfilename: "box0017",
			category: "boxes",
			source_licence: "single"
		},
		item_space: {
			dimensions_mm: {
				widthx: 360,
				heighty: 142,
				depthz: 165
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0008",
			objfilename: "cera0008",
			bakedtexturefilename: "cera0008",
			blendfilename: "cera0008",
			category: "ceramics",
			source_licence: "vase"
		},
		item_space: {
			dimensions_mm: {
				widthx: 290,
				heighty: 199,
				depthz: 261
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0011",
			objfilename: "cera0011",
			bakedtexturefilename: "cera0011",
			blendfilename: "cera0011",
			category: "ceramics",
			source_licence: "vases"
		},
		item_space: {
			dimensions_mm: {
				widthx: 228,
				heighty: 375,
				depthz: 165
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0022",
			objfilename: "cera0022",
			bakedtexturefilename: "cera0022",
			blendfilename: "cera0022",
			category: "ceramics",
			source_licence: "vases"
		},
		item_space: {
			dimensions_mm: {
				widthx: 200,
				heighty: 525,
				depthz: 200
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0023",
			objfilename: "cera0023",
			bakedtexturefilename: "cera0023",
			blendfilename: "cera0023",
			category: "ceramics",
			source_licence: "vases"
		},
		item_space: {
			dimensions_mm: {
				widthx: 205,
				heighty: 415,
				depthz: 146
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0024",
			objfilename: "cera0024",
			bakedtexturefilename: "cera0024",
			blendfilename: "cera0024",
			category: "ceramics",
			source_licence: "bowl"
		},
		item_space: {
			dimensions_mm: {
				widthx: 197,
				heighty: 89,
				depthz: 197
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0025",
			objfilename: "cera0025",
			bakedtexturefilename: "cera0025",
			blendfilename: "cera0025",
			category: "ceramics",
			source_licence: "vase"
		},
		item_space: {
			dimensions_mm: {
				widthx: 141,
				heighty: 225,
				depthz: 143
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0026",
			objfilename: "cera0026",
			bakedtexturefilename: "cera0026",
			blendfilename: "cera0026",
			category: "ceramics",
			source_licence: "vase"
		},
		item_space: {
			dimensions_mm: {
				widthx: 114,
				heighty: 247,
				depthz: 108
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0027",
			objfilename: "cera0027",
			bakedtexturefilename: "cera0027",
			blendfilename: "cera0027",
			category: "ceramics",
			source_licence: "vase"
		},
		item_space: {
			dimensions_mm: {
				widthx: 200,
				heighty: 524,
				depthz: 200
			}
		}
	},
	{
		item_identity: {
			item_id: "cera0028",
			objfilename: "cera0028",
			bakedtexturefilename: "cera0028",
			blendfilename: "cera0028",
			category: "ceramics",
			source_licence: "sculpture"
		},
		item_space: {
			dimensions_mm: {
				widthx: 134,
				heighty: 378,
				depthz: 72
			}
		}
	},
	{
		item_identity: {
			item_id: "deco0006",
			objfilename: "deco0006",
			bakedtexturefilename: "deco0006",
			blendfilename: "deco0006",
			category: "deco",
			source_licence: "large_picture"
		},
		item_space: {
			dimensions_mm: {
				widthx: 575,
				heighty: 805,
				depthz: 73
			}
		}
	},
	{
		item_identity: {
			item_id: "deco0007",
			objfilename: "deco0007",
			bakedtexturefilename: "deco0007",
			blendfilename: "deco0007",
			category: "deco",
			source_licence: "mini_sculpture"
		},
		item_space: {
			dimensions_mm: {
				widthx: 75,
				heighty: 71,
				depthz: 82
			}
		}
	},
	{
		item_identity: {
			item_id: "deco0008",
			objfilename: "deco0008",
			bakedtexturefilename: "deco0008",
			blendfilename: "deco0008",
			category: "deco",
			source_licence: "cube_of_balls"
		},
		item_space: {
			dimensions_mm: {
				widthx: 113,
				heighty: 101,
				depthz: 113
			}
		}
	},
	{
		item_identity: {
			item_id: "deco0009",
			objfilename: "deco0009",
			bakedtexturefilename: "deco0009",
			blendfilename: "deco0009",
			category: "deco",
			source_licence: "pipe_piramid"
		},
		item_space: {
			dimensions_mm: {
				widthx: 158,
				heighty: 141,
				depthz: 173
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0003",
			objfilename: "elec0003",
			bakedtexturefilename: "elec0003",
			blendfilename: "elec0003",
			category: "electronics",
			source_licence: "audio_video_receiver"
		},
		item_space: {
			dimensions_mm: {
				widthx: 440,
				heighty: 134,
				depthz: 303
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0014",
			objfilename: "elec0014",
			bakedtexturefilename: "elec0014",
			blendfilename: "elec0014",
			category: "electronics",
			source_licence: "Xbox"
		},
		item_space: {
			dimensions_mm: {
				widthx: 150,
				heighty: 289,
				depthz: 152
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0021",
			objfilename: "elec0021",
			bakedtexturefilename: "elec0021",
			blendfilename: "elec0021",
			category: "electronics",
			source_licence: "apple_tv"
		},
		item_space: {
			dimensions_mm: {
				widthx: 36,
				heighty: 9,
				depthz: 136
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0068",
			objfilename: "elec0068",
			bakedtexturefilename: "elec0068",
			blendfilename: "elec0068",
			category: "electronics",
			source_licence: "apple_tv"
		},
		item_space: {
			dimensions_mm: {
				widthx: 98,
				heighty: 35,
				depthz: 98
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0069",
			objfilename: "elec0069",
			bakedtexturefilename: "elec0069",
			blendfilename: "elec0069",
			category: "electronics",
			source_licence: "TV_55"
		},
		item_space: {
			dimensions_mm: {
				widthx: 1230,
				heighty: 740,
				depthz: 232
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0071",
			objfilename: "elec0071",
			bakedtexturefilename: "elec0071",
			blendfilename: "elec0071",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 162,
				heighty: 268,
				depthz: 220
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0072",
			objfilename: "elec0072",
			bakedtexturefilename: "elec0072",
			blendfilename: "elec0072",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 162,
				heighty: 268,
				depthz: 220
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0073",
			objfilename: "elec0073",
			bakedtexturefilename: "elec0073",
			blendfilename: "elec0073",
			category: "electronics",
			source_licence: "macbook_pro_13"
		},
		item_space: {
			dimensions_mm: {
				widthx: 304,
				heighty: 202,
				depthz: 287
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0076",
			objfilename: "elec0076",
			bakedtexturefilename: "elec0076",
			blendfilename: "elec0076",
			category: "electronics",
			source_licence: "turntable"
		},
		item_space: {
			dimensions_mm: {
				widthx: 422,
				heighty: 112,
				depthz: 343
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0077",
			objfilename: "elec0077",
			bakedtexturefilename: "elec0077",
			blendfilename: "elec0077",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 132,
				heighty: 607,
				depthz: 200
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0078",
			objfilename: "elec0078",
			bakedtexturefilename: "elec0078",
			blendfilename: "elec0078",
			category: "electronics",
			source_licence: "amplifier"
		},
		item_space: {
			dimensions_mm: {
				widthx: 450,
				heighty: 143,
				depthz: 474
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0079",
			objfilename: "elec0079",
			bakedtexturefilename: "elec0079",
			blendfilename: "elec0079",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 161,
				heighty: 69,
				depthz: 78
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0080",
			objfilename: "elec0080",
			bakedtexturefilename: "elec0080",
			blendfilename: "elec0080",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 132,
				heighty: 251,
				depthz: 200
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0081",
			objfilename: "elec0081",
			bakedtexturefilename: "elec0081",
			blendfilename: "elec0081",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 132,
				heighty: 444,
				depthz: 200
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0082",
			objfilename: "elec0082",
			bakedtexturefilename: "elec0082",
			blendfilename: "elec0082",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 132,
				heighty: 607,
				depthz: 200
			}
		}
	},
	{
		item_identity: {
			item_id: "elec0083",
			objfilename: "elec0083",
			bakedtexturefilename: "elec0083",
			blendfilename: "elec0083",
			category: "electronics",
			source_licence: "speaker"
		},
		item_space: {
			dimensions_mm: {
				widthx: 132,
				heighty: 444,
				depthz: 200
			}
		}
	},
	{
		item_identity: {
			item_id: "lamp0056",
			objfilename: "lamp0056",
			bakedtexturefilename: "lamp0056",
			blendfilename: "lamp0056",
			category: "lamps",
			source_licence: "classic"
		},
		item_space: {
			dimensions_mm: {
				widthx: 250,
				heighty: 390,
				depthz: 250
			}
		}
	},
	{
		item_identity: {
			item_id: "lamp0057",
			objfilename: "lamp0057",
			bakedtexturefilename: "lamp0057",
			blendfilename: "lamp0057",
			category: "lamps",
			source_licence: "classic"
		},
		item_space: {
			dimensions_mm: {
				widthx: 250,
				heighty: 390,
				depthz: 250
			}
		}
	},
	{
		item_identity: {
			item_id: "lamp0058",
			objfilename: "lamp0058",
			bakedtexturefilename: "lamp0058",
			blendfilename: "lamp0058",
			category: "lamps",
			source_licence: "classic"
		},
		item_space: {
			dimensions_mm: {
				widthx: 223,
				heighty: 426,
				depthz: 301
			}
		}
	},
	{
		item_identity: {
			item_id: "lamp0059",
			objfilename: "lamp0059",
			bakedtexturefilename: "lamp0059",
			blendfilename: "lamp0059",
			category: "lamps",
			source_licence: "classic"
		},
		item_space: {
			dimensions_mm: {
				widthx: 223,
				heighty: 426,
				depthz: 301
			}
		}
	},
	{
		item_identity: {
			item_id: "plant0014",
			objfilename: "plant0014",
			bakedtexturefilename: "plant0014",
			blendfilename: "plant0014",
			category: "plants",
			source_licence: "monstera"
		},
		item_space: {
			dimensions_mm: {
				widthx: 619,
				heighty: 791,
				depthz: 659
			}
		}
	},
	{
		item_identity: {
			item_id: "plant0038",
			objfilename: "plant0038",
			bakedtexturefilename: "plant0038",
			blendfilename: "plant0038",
			category: "plants",
			source_licence: "rubber"
		},
		item_space: {
			dimensions_mm: {
				widthx: 360,
				heighty: 499,
				depthz: 263
			}
		}
	},
	{
		item_identity: {
			item_id: "plant0039",
			objfilename: "plant0039",
			bakedtexturefilename: "plant0039",
			blendfilename: "plant0039",
			category: "plants",
			source_licence: "rubber"
		},
		item_space: {
			dimensions_mm: {
				widthx: 359,
				heighty: 500,
				depthz: 263
			}
		}
	},
	{
		item_identity: {
			item_id: "plant0040",
			objfilename: "plant0040",
			bakedtexturefilename: "plant0040",
			blendfilename: "plant0040",
			category: "plants",
			source_licence: "agave"
		},
		item_space: {
			dimensions_mm: {
				widthx: 103,
				heighty: 126,
				depthz: 97
			}
		}
	},
	{
		item_identity: {
			item_id: "plant0041",
			objfilename: "plant0041",
			bakedtexturefilename: "plant0041",
			blendfilename: "plant0041",
			category: "plants",
			source_licence: "cactus"
		},
		item_space: {
			dimensions_mm: {
				widthx: 189,
				heighty: 488,
				depthz: 289
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0001",
			objfilename: "vinyl0001",
			bakedtexturefilename: "vinyl0001",
			blendfilename: "vinyl0001",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 120,
				heighty: 320,
				depthz: 339
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0002",
			objfilename: "vinyl0002",
			bakedtexturefilename: "vinyl0002",
			blendfilename: "vinyl0002",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 120,
				heighty: 318,
				depthz: 342
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0003",
			objfilename: "vinyl0003",
			bakedtexturefilename: "vinyl0003",
			blendfilename: "vinyl0003",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 122,
				heighty: 315,
				depthz: 330
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0004",
			objfilename: "vinyl0004",
			bakedtexturefilename: "vinyl0004",
			blendfilename: "vinyl0004",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 121,
				heighty: 320,
				depthz: 337
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0005",
			objfilename: "vinyl0005",
			bakedtexturefilename: "vinyl0005",
			blendfilename: "vinyl0005",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 76,
				heighty: 320,
				depthz: 336
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0006",
			objfilename: "vinyl0006",
			bakedtexturefilename: "vinyl0006",
			blendfilename: "vinyl0006",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 100,
				heighty: 320,
				depthz: 330
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0007",
			objfilename: "vinyl0007",
			bakedtexturefilename: "vinyl0007",
			blendfilename: "vinyl0007",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 120,
				heighty: 315,
				depthz: 327
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0008",
			objfilename: "vinyl0008",
			bakedtexturefilename: "vinyl0008",
			blendfilename: "vinyl0008",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 105,
				heighty: 316,
				depthz: 326
			}
		}
	},
	{
		item_identity: {
			item_id: "vinyl0009",
			objfilename: "vinyl0009",
			bakedtexturefilename: "vinyl0009",
			blendfilename: "vinyl0009",
			category: "vinyl",
			source_licence: "vertical_stock"
		},
		item_space: {
			dimensions_mm: {
				widthx: 119,
				heighty: 311,
				depthz: 323
			}
		}
	}
];
var itemsRepositoryRelease = {
	categories: categories,
	items: items
};

//import { threadId } from 'worker_threads';

const repo = itemsRepositoryRelease; //merge(itemsRepository, itemsRepositoryCurrent);

class Picker {
	// TODO:
	// Remeber items
	// Currently on the scene
	//

	constructor() { }

	find(portal) {
		let tags = portal.item.tags;
		let hasExplicitID = tags.some((tag) => tag[0] == '#');

		let categoryTag = tags.filter((tag) => tag[0] == '.')[0];
		let category = categoryTag.substring(1, categoryTag.length);

		let categoryFiltered = repo.items.filter((item) => item.item_identity.category === category);

		let sizeFiltered = categoryFiltered.filter(this.matchToPortal(portal));

		if (hasExplicitID) {
			let explicitID = tags.filter((tag) => tag[0] == '#');
			let hash = explicitID[0];
			let id = hash.substring(1, hash.length);
			let item = repo.items.find((item) => {
				if (item == undefined || !item.item_identity) return false;
				return item.item_identity.item_id == id;
			});

			return [item];
		} else {
			return sizeFiltered;
		}
	}

	matchToPortal(portal) {
		return (item) => dummySolver(item, portal);
	}
}

const pickerService = new Picker();

class GLTFLoader extends Loader {

	constructor( manager ) {

		super( manager );

		this.dracoLoader = null;
		this.ktx2Loader = null;
		this.meshoptDecoder = null;

		this.pluginCallbacks = [];

		this.register( function ( parser ) {

			return new GLTFMaterialsClearcoatExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFTextureBasisUExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFTextureWebPExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFMaterialsSheenExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFMaterialsTransmissionExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFMaterialsVolumeExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFMaterialsIorExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFMaterialsSpecularExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFLightsExtension( parser );

		} );

		this.register( function ( parser ) {

			return new GLTFMeshoptCompression( parser );

		} );

	}

	load( url, onLoad, onProgress, onError ) {

		const scope = this;

		let resourcePath;

		if ( this.resourcePath !== '' ) {

			resourcePath = this.resourcePath;

		} else if ( this.path !== '' ) {

			resourcePath = this.path;

		} else {

			resourcePath = LoaderUtils.extractUrlBase( url );

		}

		// Tells the LoadingManager to track an extra item, which resolves after
		// the model is fully loaded. This means the count of items loaded will
		// be incorrect, but ensures manager.onLoad() does not fire early.
		this.manager.itemStart( url );

		const _onError = function ( e ) {

			if ( onError ) {

				onError( e );

			} else {

				console.error( e );

			}

			scope.manager.itemError( url );
			scope.manager.itemEnd( url );

		};

		const loader = new FileLoader( this.manager );

		loader.setPath( this.path );
		loader.setResponseType( 'arraybuffer' );
		loader.setRequestHeader( this.requestHeader );
		loader.setWithCredentials( this.withCredentials );

		loader.load( url, function ( data ) {

			try {

				scope.parse( data, resourcePath, function ( gltf ) {

					onLoad( gltf );

					scope.manager.itemEnd( url );

				}, _onError );

			} catch ( e ) {

				_onError( e );

			}

		}, onProgress, _onError );

	}

	setDRACOLoader( dracoLoader ) {

		this.dracoLoader = dracoLoader;
		return this;

	}

	setDDSLoader() {

		throw new Error(

			'THREE.GLTFLoader: "MSFT_texture_dds" no longer supported. Please update to "KHR_texture_basisu".'

		);

	}

	setKTX2Loader( ktx2Loader ) {

		this.ktx2Loader = ktx2Loader;
		return this;

	}

	setMeshoptDecoder( meshoptDecoder ) {

		this.meshoptDecoder = meshoptDecoder;
		return this;

	}

	register( callback ) {

		if ( this.pluginCallbacks.indexOf( callback ) === - 1 ) {

			this.pluginCallbacks.push( callback );

		}

		return this;

	}

	unregister( callback ) {

		if ( this.pluginCallbacks.indexOf( callback ) !== - 1 ) {

			this.pluginCallbacks.splice( this.pluginCallbacks.indexOf( callback ), 1 );

		}

		return this;

	}

	parse( data, path, onLoad, onError ) {

		let content;
		const extensions = {};
		const plugins = {};

		if ( typeof data === 'string' ) {

			content = data;

		} else {

			const magic = LoaderUtils.decodeText( new Uint8Array( data, 0, 4 ) );

			if ( magic === BINARY_EXTENSION_HEADER_MAGIC ) {

				try {

					extensions[ EXTENSIONS.KHR_BINARY_GLTF ] = new GLTFBinaryExtension( data );

				} catch ( error ) {

					if ( onError ) onError( error );
					return;

				}

				content = extensions[ EXTENSIONS.KHR_BINARY_GLTF ].content;

			} else {

				content = LoaderUtils.decodeText( new Uint8Array( data ) );

			}

		}

		const json = JSON.parse( content );

		if ( json.asset === undefined || json.asset.version[ 0 ] < 2 ) {

			if ( onError ) onError( new Error( 'THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.' ) );
			return;

		}

		const parser = new GLTFParser( json, {

			path: path || this.resourcePath || '',
			crossOrigin: this.crossOrigin,
			requestHeader: this.requestHeader,
			manager: this.manager,
			ktx2Loader: this.ktx2Loader,
			meshoptDecoder: this.meshoptDecoder

		} );

		parser.fileLoader.setRequestHeader( this.requestHeader );

		for ( let i = 0; i < this.pluginCallbacks.length; i ++ ) {

			const plugin = this.pluginCallbacks[ i ]( parser );
			plugins[ plugin.name ] = plugin;

			// Workaround to avoid determining as unknown extension
			// in addUnknownExtensionsToUserData().
			// Remove this workaround if we move all the existing
			// extension handlers to plugin system
			extensions[ plugin.name ] = true;

		}

		if ( json.extensionsUsed ) {

			for ( let i = 0; i < json.extensionsUsed.length; ++ i ) {

				const extensionName = json.extensionsUsed[ i ];
				const extensionsRequired = json.extensionsRequired || [];

				switch ( extensionName ) {

					case EXTENSIONS.KHR_MATERIALS_UNLIT:
						extensions[ extensionName ] = new GLTFMaterialsUnlitExtension();
						break;

					case EXTENSIONS.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS:
						extensions[ extensionName ] = new GLTFMaterialsPbrSpecularGlossinessExtension();
						break;

					case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:
						extensions[ extensionName ] = new GLTFDracoMeshCompressionExtension( json, this.dracoLoader );
						break;

					case EXTENSIONS.KHR_TEXTURE_TRANSFORM:
						extensions[ extensionName ] = new GLTFTextureTransformExtension();
						break;

					case EXTENSIONS.KHR_MESH_QUANTIZATION:
						extensions[ extensionName ] = new GLTFMeshQuantizationExtension();
						break;

					default:

						if ( extensionsRequired.indexOf( extensionName ) >= 0 && plugins[ extensionName ] === undefined ) {

							console.warn( 'THREE.GLTFLoader: Unknown extension "' + extensionName + '".' );

						}

				}

			}

		}

		parser.setExtensions( extensions );
		parser.setPlugins( plugins );
		parser.parse( onLoad, onError );

	}

	parseAsync( data, path ) {

		const scope = this;

		return new Promise( function ( resolve, reject ) {

			scope.parse( data, path, resolve, reject );

		} );

	}

}

/* GLTFREGISTRY */

function GLTFRegistry() {

	let objects = {};

	return	{

		get: function ( key ) {

			return objects[ key ];

		},

		add: function ( key, object ) {

			objects[ key ] = object;

		},

		remove: function ( key ) {

			delete objects[ key ];

		},

		removeAll: function () {

			objects = {};

		}

	};

}

/*********************************/
/********** EXTENSIONS ***********/
/*********************************/

const EXTENSIONS = {
	KHR_BINARY_GLTF: 'KHR_binary_glTF',
	KHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',
	KHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',
	KHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',
	KHR_MATERIALS_IOR: 'KHR_materials_ior',
	KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS: 'KHR_materials_pbrSpecularGlossiness',
	KHR_MATERIALS_SHEEN: 'KHR_materials_sheen',
	KHR_MATERIALS_SPECULAR: 'KHR_materials_specular',
	KHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',
	KHR_MATERIALS_UNLIT: 'KHR_materials_unlit',
	KHR_MATERIALS_VOLUME: 'KHR_materials_volume',
	KHR_TEXTURE_BASISU: 'KHR_texture_basisu',
	KHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',
	KHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',
	EXT_TEXTURE_WEBP: 'EXT_texture_webp',
	EXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression'
};

/**
 * Punctual Lights Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual
 */
class GLTFLightsExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;

		// Object3D instance caches
		this.cache = { refs: {}, uses: {} };

	}

	_markDefs() {

		const parser = this.parser;
		const nodeDefs = this.parser.json.nodes || [];

		for ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {

			const nodeDef = nodeDefs[ nodeIndex ];

			if ( nodeDef.extensions
					&& nodeDef.extensions[ this.name ]
					&& nodeDef.extensions[ this.name ].light !== undefined ) {

				parser._addNodeRef( this.cache, nodeDef.extensions[ this.name ].light );

			}

		}

	}

	_loadLight( lightIndex ) {

		const parser = this.parser;
		const cacheKey = 'light:' + lightIndex;
		let dependency = parser.cache.get( cacheKey );

		if ( dependency ) return dependency;

		const json = parser.json;
		const extensions = ( json.extensions && json.extensions[ this.name ] ) || {};
		const lightDefs = extensions.lights || [];
		const lightDef = lightDefs[ lightIndex ];
		let lightNode;

		const color = new Color( 0xffffff );

		if ( lightDef.color !== undefined ) color.fromArray( lightDef.color );

		const range = lightDef.range !== undefined ? lightDef.range : 0;

		switch ( lightDef.type ) {

			case 'directional':
				lightNode = new DirectionalLight( color );
				lightNode.target.position.set( 0, 0, - 1 );
				lightNode.add( lightNode.target );
				break;

			case 'point':
				lightNode = new PointLight( color );
				lightNode.distance = range;
				break;

			case 'spot':
				lightNode = new SpotLight( color );
				lightNode.distance = range;
				// Handle spotlight properties.
				lightDef.spot = lightDef.spot || {};
				lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0;
				lightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0;
				lightNode.angle = lightDef.spot.outerConeAngle;
				lightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;
				lightNode.target.position.set( 0, 0, - 1 );
				lightNode.add( lightNode.target );
				break;

			default:
				throw new Error( 'THREE.GLTFLoader: Unexpected light type: ' + lightDef.type );

		}

		// Some lights (e.g. spot) default to a position other than the origin. Reset the position
		// here, because node-level parsing will only override position if explicitly specified.
		lightNode.position.set( 0, 0, 0 );

		lightNode.decay = 2;

		if ( lightDef.intensity !== undefined ) lightNode.intensity = lightDef.intensity;

		lightNode.name = parser.createUniqueName( lightDef.name || ( 'light_' + lightIndex ) );

		dependency = Promise.resolve( lightNode );

		parser.cache.add( cacheKey, dependency );

		return dependency;

	}

	createNodeAttachment( nodeIndex ) {

		const self = this;
		const parser = this.parser;
		const json = parser.json;
		const nodeDef = json.nodes[ nodeIndex ];
		const lightDef = ( nodeDef.extensions && nodeDef.extensions[ this.name ] ) || {};
		const lightIndex = lightDef.light;

		if ( lightIndex === undefined ) return null;

		return this._loadLight( lightIndex ).then( function ( light ) {

			return parser._getNodeRef( self.cache, lightIndex, light );

		} );

	}

}

/**
 * Unlit Materials Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit
 */
class GLTFMaterialsUnlitExtension {

	constructor() {

		this.name = EXTENSIONS.KHR_MATERIALS_UNLIT;

	}

	getMaterialType() {

		return MeshBasicMaterial;

	}

	extendParams( materialParams, materialDef, parser ) {

		const pending = [];

		materialParams.color = new Color( 1.0, 1.0, 1.0 );
		materialParams.opacity = 1.0;

		const metallicRoughness = materialDef.pbrMetallicRoughness;

		if ( metallicRoughness ) {

			if ( Array.isArray( metallicRoughness.baseColorFactor ) ) {

				const array = metallicRoughness.baseColorFactor;

				materialParams.color.fromArray( array );
				materialParams.opacity = array[ 3 ];

			}

			if ( metallicRoughness.baseColorTexture !== undefined ) {

				pending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture ) );

			}

		}

		return Promise.all( pending );

	}

}

/**
 * Clearcoat Materials Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat
 */
class GLTFMaterialsClearcoatExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;

	}

	getMaterialType( materialIndex ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;

		return MeshPhysicalMaterial;

	}

	extendMaterialParams( materialIndex, materialParams ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {

			return Promise.resolve();

		}

		const pending = [];

		const extension = materialDef.extensions[ this.name ];

		if ( extension.clearcoatFactor !== undefined ) {

			materialParams.clearcoat = extension.clearcoatFactor;

		}

		if ( extension.clearcoatTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'clearcoatMap', extension.clearcoatTexture ) );

		}

		if ( extension.clearcoatRoughnessFactor !== undefined ) {

			materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;

		}

		if ( extension.clearcoatRoughnessTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture ) );

		}

		if ( extension.clearcoatNormalTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture ) );

			if ( extension.clearcoatNormalTexture.scale !== undefined ) {

				const scale = extension.clearcoatNormalTexture.scale;

				materialParams.clearcoatNormalScale = new Vector2( scale, scale );

			}

		}

		return Promise.all( pending );

	}

}

/**
 * Sheen Materials Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen
 */
class GLTFMaterialsSheenExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_MATERIALS_SHEEN;

	}

	getMaterialType( materialIndex ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;

		return MeshPhysicalMaterial;

	}

	extendMaterialParams( materialIndex, materialParams ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {

			return Promise.resolve();

		}

		const pending = [];

		materialParams.sheenColor = new Color( 0, 0, 0 );
		materialParams.sheenRoughness = 0;
		materialParams.sheen = 1;

		const extension = materialDef.extensions[ this.name ];

		if ( extension.sheenColorFactor !== undefined ) {

			materialParams.sheenColor.fromArray( extension.sheenColorFactor );

		}

		if ( extension.sheenRoughnessFactor !== undefined ) {

			materialParams.sheenRoughness = extension.sheenRoughnessFactor;

		}

		if ( extension.sheenColorTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'sheenColorMap', extension.sheenColorTexture ) );

		}

		if ( extension.sheenRoughnessTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture ) );

		}

		return Promise.all( pending );

	}

}

/**
 * Transmission Materials Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission
 * Draft: https://github.com/KhronosGroup/glTF/pull/1698
 */
class GLTFMaterialsTransmissionExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;

	}

	getMaterialType( materialIndex ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;

		return MeshPhysicalMaterial;

	}

	extendMaterialParams( materialIndex, materialParams ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {

			return Promise.resolve();

		}

		const pending = [];

		const extension = materialDef.extensions[ this.name ];

		if ( extension.transmissionFactor !== undefined ) {

			materialParams.transmission = extension.transmissionFactor;

		}

		if ( extension.transmissionTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'transmissionMap', extension.transmissionTexture ) );

		}

		return Promise.all( pending );

	}

}

/**
 * Materials Volume Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume
 */
class GLTFMaterialsVolumeExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_MATERIALS_VOLUME;

	}

	getMaterialType( materialIndex ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;

		return MeshPhysicalMaterial;

	}

	extendMaterialParams( materialIndex, materialParams ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {

			return Promise.resolve();

		}

		const pending = [];

		const extension = materialDef.extensions[ this.name ];

		materialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0;

		if ( extension.thicknessTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'thicknessMap', extension.thicknessTexture ) );

		}

		materialParams.attenuationDistance = extension.attenuationDistance || 0;

		const colorArray = extension.attenuationColor || [ 1, 1, 1 ];
		materialParams.attenuationColor = new Color( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ] );

		return Promise.all( pending );

	}

}

/**
 * Materials ior Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior
 */
class GLTFMaterialsIorExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_MATERIALS_IOR;

	}

	getMaterialType( materialIndex ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;

		return MeshPhysicalMaterial;

	}

	extendMaterialParams( materialIndex, materialParams ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {

			return Promise.resolve();

		}

		const extension = materialDef.extensions[ this.name ];

		materialParams.ior = extension.ior !== undefined ? extension.ior : 1.5;

		return Promise.resolve();

	}

}

/**
 * Materials specular Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular
 */
class GLTFMaterialsSpecularExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;

	}

	getMaterialType( materialIndex ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;

		return MeshPhysicalMaterial;

	}

	extendMaterialParams( materialIndex, materialParams ) {

		const parser = this.parser;
		const materialDef = parser.json.materials[ materialIndex ];

		if ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {

			return Promise.resolve();

		}

		const pending = [];

		const extension = materialDef.extensions[ this.name ];

		materialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0;

		if ( extension.specularTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'specularIntensityMap', extension.specularTexture ) );

		}

		const colorArray = extension.specularColorFactor || [ 1, 1, 1 ];
		materialParams.specularColor = new Color( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ] );

		if ( extension.specularColorTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'specularColorMap', extension.specularColorTexture ).then( function ( texture ) {

				texture.encoding = sRGBEncoding;

			} ) );

		}

		return Promise.all( pending );

	}

}

/**
 * BasisU Texture Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu
 */
class GLTFTextureBasisUExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.KHR_TEXTURE_BASISU;

	}

	loadTexture( textureIndex ) {

		const parser = this.parser;
		const json = parser.json;

		const textureDef = json.textures[ textureIndex ];

		if ( ! textureDef.extensions || ! textureDef.extensions[ this.name ] ) {

			return null;

		}

		const extension = textureDef.extensions[ this.name ];
		const source = json.images[ extension.source ];
		const loader = parser.options.ktx2Loader;

		if ( ! loader ) {

			if ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {

				throw new Error( 'THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures' );

			} else {

				// Assumes that the extension is optional and that a fallback texture is present
				return null;

			}

		}

		return parser.loadTextureImage( textureIndex, source, loader );

	}

}

/**
 * WebP Texture Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp
 */
class GLTFTextureWebPExtension {

	constructor( parser ) {

		this.parser = parser;
		this.name = EXTENSIONS.EXT_TEXTURE_WEBP;
		this.isSupported = null;

	}

	loadTexture( textureIndex ) {

		const name = this.name;
		const parser = this.parser;
		const json = parser.json;

		const textureDef = json.textures[ textureIndex ];

		if ( ! textureDef.extensions || ! textureDef.extensions[ name ] ) {

			return null;

		}

		const extension = textureDef.extensions[ name ];
		const source = json.images[ extension.source ];

		let loader = parser.textureLoader;
		if ( source.uri ) {

			const handler = parser.options.manager.getHandler( source.uri );
			if ( handler !== null ) loader = handler;

		}

		return this.detectSupport().then( function ( isSupported ) {

			if ( isSupported ) return parser.loadTextureImage( textureIndex, source, loader );

			if ( json.extensionsRequired && json.extensionsRequired.indexOf( name ) >= 0 ) {

				throw new Error( 'THREE.GLTFLoader: WebP required by asset but unsupported.' );

			}

			// Fall back to PNG or JPEG.
			return parser.loadTexture( textureIndex );

		} );

	}

	detectSupport() {

		if ( ! this.isSupported ) {

			this.isSupported = new Promise( function ( resolve ) {

				const image = new Image();

				// Lossy test image. Support for lossy images doesn't guarantee support for all
				// WebP images, unfortunately.
				image.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';

				image.onload = image.onerror = function () {

					resolve( image.height === 1 );

				};

			} );

		}

		return this.isSupported;

	}

}

/**
 * meshopt BufferView Compression Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression
 */
class GLTFMeshoptCompression {

	constructor( parser ) {

		this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;
		this.parser = parser;

	}

	loadBufferView( index ) {

		const json = this.parser.json;
		const bufferView = json.bufferViews[ index ];

		if ( bufferView.extensions && bufferView.extensions[ this.name ] ) {

			const extensionDef = bufferView.extensions[ this.name ];

			const buffer = this.parser.getDependency( 'buffer', extensionDef.buffer );
			const decoder = this.parser.options.meshoptDecoder;

			if ( ! decoder || ! decoder.supported ) {

				if ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {

					throw new Error( 'THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files' );

				} else {

					// Assumes that the extension is optional and that fallback buffer data is present
					return null;

				}

			}

			return Promise.all( [ buffer, decoder.ready ] ).then( function ( res ) {

				const byteOffset = extensionDef.byteOffset || 0;
				const byteLength = extensionDef.byteLength || 0;

				const count = extensionDef.count;
				const stride = extensionDef.byteStride;

				const result = new ArrayBuffer( count * stride );
				const source = new Uint8Array( res[ 0 ], byteOffset, byteLength );

				decoder.decodeGltfBuffer( new Uint8Array( result ), count, stride, source, extensionDef.mode, extensionDef.filter );
				return result;

			} );

		} else {

			return null;

		}

	}

}

/* BINARY EXTENSION */
const BINARY_EXTENSION_HEADER_MAGIC = 'glTF';
const BINARY_EXTENSION_HEADER_LENGTH = 12;
const BINARY_EXTENSION_CHUNK_TYPES = { JSON: 0x4E4F534A, BIN: 0x004E4942 };

class GLTFBinaryExtension {

	constructor( data ) {

		this.name = EXTENSIONS.KHR_BINARY_GLTF;
		this.content = null;
		this.body = null;

		const headerView = new DataView( data, 0, BINARY_EXTENSION_HEADER_LENGTH );

		this.header = {
			magic: LoaderUtils.decodeText( new Uint8Array( data.slice( 0, 4 ) ) ),
			version: headerView.getUint32( 4, true ),
			length: headerView.getUint32( 8, true )
		};

		if ( this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC ) {

			throw new Error( 'THREE.GLTFLoader: Unsupported glTF-Binary header.' );

		} else if ( this.header.version < 2.0 ) {

			throw new Error( 'THREE.GLTFLoader: Legacy binary file detected.' );

		}

		const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;
		const chunkView = new DataView( data, BINARY_EXTENSION_HEADER_LENGTH );
		let chunkIndex = 0;

		while ( chunkIndex < chunkContentsLength ) {

			const chunkLength = chunkView.getUint32( chunkIndex, true );
			chunkIndex += 4;

			const chunkType = chunkView.getUint32( chunkIndex, true );
			chunkIndex += 4;

			if ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON ) {

				const contentArray = new Uint8Array( data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength );
				this.content = LoaderUtils.decodeText( contentArray );

			} else if ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN ) {

				const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;
				this.body = data.slice( byteOffset, byteOffset + chunkLength );

			}

			// Clients must ignore chunks with unknown types.

			chunkIndex += chunkLength;

		}

		if ( this.content === null ) {

			throw new Error( 'THREE.GLTFLoader: JSON content not found.' );

		}

	}

}

/**
 * DRACO Mesh Compression Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression
 */
class GLTFDracoMeshCompressionExtension {

	constructor( json, dracoLoader ) {

		if ( ! dracoLoader ) {

			throw new Error( 'THREE.GLTFLoader: No DRACOLoader instance provided.' );

		}

		this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;
		this.json = json;
		this.dracoLoader = dracoLoader;
		this.dracoLoader.preload();

	}

	decodePrimitive( primitive, parser ) {

		const json = this.json;
		const dracoLoader = this.dracoLoader;
		const bufferViewIndex = primitive.extensions[ this.name ].bufferView;
		const gltfAttributeMap = primitive.extensions[ this.name ].attributes;
		const threeAttributeMap = {};
		const attributeNormalizedMap = {};
		const attributeTypeMap = {};

		for ( const attributeName in gltfAttributeMap ) {

			const threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();

			threeAttributeMap[ threeAttributeName ] = gltfAttributeMap[ attributeName ];

		}

		for ( const attributeName in primitive.attributes ) {

			const threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();

			if ( gltfAttributeMap[ attributeName ] !== undefined ) {

				const accessorDef = json.accessors[ primitive.attributes[ attributeName ] ];
				const componentType = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];

				attributeTypeMap[ threeAttributeName ] = componentType;
				attributeNormalizedMap[ threeAttributeName ] = accessorDef.normalized === true;

			}

		}

		return parser.getDependency( 'bufferView', bufferViewIndex ).then( function ( bufferView ) {

			return new Promise( function ( resolve ) {

				dracoLoader.decodeDracoFile( bufferView, function ( geometry ) {

					for ( const attributeName in geometry.attributes ) {

						const attribute = geometry.attributes[ attributeName ];
						const normalized = attributeNormalizedMap[ attributeName ];

						if ( normalized !== undefined ) attribute.normalized = normalized;

					}

					resolve( geometry );

				}, threeAttributeMap, attributeTypeMap );

			} );

		} );

	}

}

/**
 * Texture Transform Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform
 */
class GLTFTextureTransformExtension {

	constructor() {

		this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;

	}

	extendTexture( texture, transform ) {

		if ( transform.texCoord !== undefined ) {

			console.warn( 'THREE.GLTFLoader: Custom UV sets in "' + this.name + '" extension not yet supported.' );

		}

		if ( transform.offset === undefined && transform.rotation === undefined && transform.scale === undefined ) {

			// See https://github.com/mrdoob/three.js/issues/21819.
			return texture;

		}

		texture = texture.clone();

		if ( transform.offset !== undefined ) {

			texture.offset.fromArray( transform.offset );

		}

		if ( transform.rotation !== undefined ) {

			texture.rotation = transform.rotation;

		}

		if ( transform.scale !== undefined ) {

			texture.repeat.fromArray( transform.scale );

		}

		texture.needsUpdate = true;

		return texture;

	}

}

/**
 * Specular-Glossiness Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Archived/KHR_materials_pbrSpecularGlossiness
 */

/**
 * A sub class of StandardMaterial with some of the functionality
 * changed via the `onBeforeCompile` callback
 * @pailhead
 */
class GLTFMeshStandardSGMaterial extends MeshStandardMaterial {

	constructor( params ) {

		super();

		this.isGLTFSpecularGlossinessMaterial = true;

		//various chunks that need replacing
		const specularMapParsFragmentChunk = [
			'#ifdef USE_SPECULARMAP',
			'	uniform sampler2D specularMap;',
			'#endif'
		].join( '\n' );

		const glossinessMapParsFragmentChunk = [
			'#ifdef USE_GLOSSINESSMAP',
			'	uniform sampler2D glossinessMap;',
			'#endif'
		].join( '\n' );

		const specularMapFragmentChunk = [
			'vec3 specularFactor = specular;',
			'#ifdef USE_SPECULARMAP',
			'	vec4 texelSpecular = texture2D( specularMap, vUv );',
			'	// reads channel RGB, compatible with a glTF Specular-Glossiness (RGBA) texture',
			'	specularFactor *= texelSpecular.rgb;',
			'#endif'
		].join( '\n' );

		const glossinessMapFragmentChunk = [
			'float glossinessFactor = glossiness;',
			'#ifdef USE_GLOSSINESSMAP',
			'	vec4 texelGlossiness = texture2D( glossinessMap, vUv );',
			'	// reads channel A, compatible with a glTF Specular-Glossiness (RGBA) texture',
			'	glossinessFactor *= texelGlossiness.a;',
			'#endif'
		].join( '\n' );

		const lightPhysicalFragmentChunk = [
			'PhysicalMaterial material;',
			'material.diffuseColor = diffuseColor.rgb * ( 1. - max( specularFactor.r, max( specularFactor.g, specularFactor.b ) ) );',
			'vec3 dxy = max( abs( dFdx( geometryNormal ) ), abs( dFdy( geometryNormal ) ) );',
			'float geometryRoughness = max( max( dxy.x, dxy.y ), dxy.z );',
			'material.roughness = max( 1.0 - glossinessFactor, 0.0525 ); // 0.0525 corresponds to the base mip of a 256 cubemap.',
			'material.roughness += geometryRoughness;',
			'material.roughness = min( material.roughness, 1.0 );',
			'material.specularColor = specularFactor;',
		].join( '\n' );

		const uniforms = {
			specular: { value: new Color().setHex( 0xffffff ) },
			glossiness: { value: 1 },
			specularMap: { value: null },
			glossinessMap: { value: null }
		};

		this._extraUniforms = uniforms;

		this.onBeforeCompile = function ( shader ) {

			for ( const uniformName in uniforms ) {

				shader.uniforms[ uniformName ] = uniforms[ uniformName ];

			}

			shader.fragmentShader = shader.fragmentShader
				.replace( 'uniform float roughness;', 'uniform vec3 specular;' )
				.replace( 'uniform float metalness;', 'uniform float glossiness;' )
				.replace( '#include <roughnessmap_pars_fragment>', specularMapParsFragmentChunk )
				.replace( '#include <metalnessmap_pars_fragment>', glossinessMapParsFragmentChunk )
				.replace( '#include <roughnessmap_fragment>', specularMapFragmentChunk )
				.replace( '#include <metalnessmap_fragment>', glossinessMapFragmentChunk )
				.replace( '#include <lights_physical_fragment>', lightPhysicalFragmentChunk );

		};

		Object.defineProperties( this, {

			specular: {
				get: function () {

					return uniforms.specular.value;

				},
				set: function ( v ) {

					uniforms.specular.value = v;

				}
			},

			specularMap: {
				get: function () {

					return uniforms.specularMap.value;

				},
				set: function ( v ) {

					uniforms.specularMap.value = v;

					if ( v ) {

						this.defines.USE_SPECULARMAP = ''; // USE_UV is set by the renderer for specular maps

					} else {

						delete this.defines.USE_SPECULARMAP;

					}

				}
			},

			glossiness: {
				get: function () {

					return uniforms.glossiness.value;

				},
				set: function ( v ) {

					uniforms.glossiness.value = v;

				}
			},

			glossinessMap: {
				get: function () {

					return uniforms.glossinessMap.value;

				},
				set: function ( v ) {

					uniforms.glossinessMap.value = v;

					if ( v ) {

						this.defines.USE_GLOSSINESSMAP = '';
						this.defines.USE_UV = '';

					} else {

						delete this.defines.USE_GLOSSINESSMAP;
						delete this.defines.USE_UV;

					}

				}
			}

		} );

		delete this.metalness;
		delete this.roughness;
		delete this.metalnessMap;
		delete this.roughnessMap;

		this.setValues( params );

	}

	copy( source ) {

		super.copy( source );

		this.specularMap = source.specularMap;
		this.specular.copy( source.specular );
		this.glossinessMap = source.glossinessMap;
		this.glossiness = source.glossiness;
		delete this.metalness;
		delete this.roughness;
		delete this.metalnessMap;
		delete this.roughnessMap;
		return this;

	}

}


class GLTFMaterialsPbrSpecularGlossinessExtension {

	constructor() {

		this.name = EXTENSIONS.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS;

		this.specularGlossinessParams = [
			'color',
			'map',
			'lightMap',
			'lightMapIntensity',
			'aoMap',
			'aoMapIntensity',
			'emissive',
			'emissiveIntensity',
			'emissiveMap',
			'bumpMap',
			'bumpScale',
			'normalMap',
			'normalMapType',
			'displacementMap',
			'displacementScale',
			'displacementBias',
			'specularMap',
			'specular',
			'glossinessMap',
			'glossiness',
			'alphaMap',
			'envMap',
			'envMapIntensity',
			'refractionRatio',
		];

	}

	getMaterialType() {

		return GLTFMeshStandardSGMaterial;

	}

	extendParams( materialParams, materialDef, parser ) {

		const pbrSpecularGlossiness = materialDef.extensions[ this.name ];

		materialParams.color = new Color( 1.0, 1.0, 1.0 );
		materialParams.opacity = 1.0;

		const pending = [];

		if ( Array.isArray( pbrSpecularGlossiness.diffuseFactor ) ) {

			const array = pbrSpecularGlossiness.diffuseFactor;

			materialParams.color.fromArray( array );
			materialParams.opacity = array[ 3 ];

		}

		if ( pbrSpecularGlossiness.diffuseTexture !== undefined ) {

			pending.push( parser.assignTexture( materialParams, 'map', pbrSpecularGlossiness.diffuseTexture ) );

		}

		materialParams.emissive = new Color( 0.0, 0.0, 0.0 );
		materialParams.glossiness = pbrSpecularGlossiness.glossinessFactor !== undefined ? pbrSpecularGlossiness.glossinessFactor : 1.0;
		materialParams.specular = new Color( 1.0, 1.0, 1.0 );

		if ( Array.isArray( pbrSpecularGlossiness.specularFactor ) ) {

			materialParams.specular.fromArray( pbrSpecularGlossiness.specularFactor );

		}

		if ( pbrSpecularGlossiness.specularGlossinessTexture !== undefined ) {

			const specGlossMapDef = pbrSpecularGlossiness.specularGlossinessTexture;
			pending.push( parser.assignTexture( materialParams, 'glossinessMap', specGlossMapDef ) );
			pending.push( parser.assignTexture( materialParams, 'specularMap', specGlossMapDef ) );

		}

		return Promise.all( pending );

	}

	createMaterial( materialParams ) {

		const material = new GLTFMeshStandardSGMaterial( materialParams );
		material.fog = true;

		material.color = materialParams.color;

		material.map = materialParams.map === undefined ? null : materialParams.map;

		material.lightMap = null;
		material.lightMapIntensity = 1.0;

		material.aoMap = materialParams.aoMap === undefined ? null : materialParams.aoMap;
		material.aoMapIntensity = 1.0;

		material.emissive = materialParams.emissive;
		material.emissiveIntensity = 1.0;
		material.emissiveMap = materialParams.emissiveMap === undefined ? null : materialParams.emissiveMap;

		material.bumpMap = materialParams.bumpMap === undefined ? null : materialParams.bumpMap;
		material.bumpScale = 1;

		material.normalMap = materialParams.normalMap === undefined ? null : materialParams.normalMap;
		material.normalMapType = TangentSpaceNormalMap;

		if ( materialParams.normalScale ) material.normalScale = materialParams.normalScale;

		material.displacementMap = null;
		material.displacementScale = 1;
		material.displacementBias = 0;

		material.specularMap = materialParams.specularMap === undefined ? null : materialParams.specularMap;
		material.specular = materialParams.specular;

		material.glossinessMap = materialParams.glossinessMap === undefined ? null : materialParams.glossinessMap;
		material.glossiness = materialParams.glossiness;

		material.alphaMap = null;

		material.envMap = materialParams.envMap === undefined ? null : materialParams.envMap;
		material.envMapIntensity = 1.0;

		material.refractionRatio = 0.98;

		return material;

	}

}

/**
 * Mesh Quantization Extension
 *
 * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization
 */
class GLTFMeshQuantizationExtension {

	constructor() {

		this.name = EXTENSIONS.KHR_MESH_QUANTIZATION;

	}

}

/*********************************/
/********** INTERPOLATION ********/
/*********************************/

// Spline Interpolation
// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation
class GLTFCubicSplineInterpolant extends Interpolant {

	constructor( parameterPositions, sampleValues, sampleSize, resultBuffer ) {

		super( parameterPositions, sampleValues, sampleSize, resultBuffer );

	}

	copySampleValue_( index ) {

		// Copies a sample value to the result buffer. See description of glTF
		// CUBICSPLINE values layout in interpolate_() function below.

		const result = this.resultBuffer,
			values = this.sampleValues,
			valueSize = this.valueSize,
			offset = index * valueSize * 3 + valueSize;

		for ( let i = 0; i !== valueSize; i ++ ) {

			result[ i ] = values[ offset + i ];

		}

		return result;

	}

}

GLTFCubicSplineInterpolant.prototype.beforeStart_ = GLTFCubicSplineInterpolant.prototype.copySampleValue_;

GLTFCubicSplineInterpolant.prototype.afterEnd_ = GLTFCubicSplineInterpolant.prototype.copySampleValue_;

GLTFCubicSplineInterpolant.prototype.interpolate_ = function ( i1, t0, t, t1 ) {

	const result = this.resultBuffer;
	const values = this.sampleValues;
	const stride = this.valueSize;

	const stride2 = stride * 2;
	const stride3 = stride * 3;

	const td = t1 - t0;

	const p = ( t - t0 ) / td;
	const pp = p * p;
	const ppp = pp * p;

	const offset1 = i1 * stride3;
	const offset0 = offset1 - stride3;

	const s2 = - 2 * ppp + 3 * pp;
	const s3 = ppp - pp;
	const s0 = 1 - s2;
	const s1 = s3 - pp + p;

	// Layout of keyframe output values for CUBICSPLINE animations:
	//   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]
	for ( let i = 0; i !== stride; i ++ ) {

		const p0 = values[ offset0 + i + stride ]; // splineVertex_k
		const m0 = values[ offset0 + i + stride2 ] * td; // outTangent_k * (t_k+1 - t_k)
		const p1 = values[ offset1 + i + stride ]; // splineVertex_k+1
		const m1 = values[ offset1 + i ] * td; // inTangent_k+1 * (t_k+1 - t_k)

		result[ i ] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;

	}

	return result;

};

const _q = new Quaternion();

class GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {

	interpolate_( i1, t0, t, t1 ) {

		const result = super.interpolate_( i1, t0, t, t1 );

		_q.fromArray( result ).normalize().toArray( result );

		return result;

	}

}


/*********************************/
/********** INTERNALS ************/
/*********************************/

/* CONSTANTS */

const WEBGL_CONSTANTS = {
	FLOAT: 5126,
	//FLOAT_MAT2: 35674,
	FLOAT_MAT3: 35675,
	FLOAT_MAT4: 35676,
	FLOAT_VEC2: 35664,
	FLOAT_VEC3: 35665,
	FLOAT_VEC4: 35666,
	LINEAR: 9729,
	REPEAT: 10497,
	SAMPLER_2D: 35678,
	POINTS: 0,
	LINES: 1,
	LINE_LOOP: 2,
	LINE_STRIP: 3,
	TRIANGLES: 4,
	TRIANGLE_STRIP: 5,
	TRIANGLE_FAN: 6,
	UNSIGNED_BYTE: 5121,
	UNSIGNED_SHORT: 5123
};

const WEBGL_COMPONENT_TYPES = {
	5120: Int8Array,
	5121: Uint8Array,
	5122: Int16Array,
	5123: Uint16Array,
	5125: Uint32Array,
	5126: Float32Array
};

const WEBGL_FILTERS = {
	9728: NearestFilter,
	9729: LinearFilter,
	9984: NearestMipmapNearestFilter,
	9985: LinearMipmapNearestFilter,
	9986: NearestMipmapLinearFilter,
	9987: LinearMipmapLinearFilter
};

const WEBGL_WRAPPINGS = {
	33071: ClampToEdgeWrapping,
	33648: MirroredRepeatWrapping,
	10497: RepeatWrapping
};

const WEBGL_TYPE_SIZES = {
	'SCALAR': 1,
	'VEC2': 2,
	'VEC3': 3,
	'VEC4': 4,
	'MAT2': 4,
	'MAT3': 9,
	'MAT4': 16
};

const ATTRIBUTES = {
	POSITION: 'position',
	NORMAL: 'normal',
	TANGENT: 'tangent',
	TEXCOORD_0: 'uv',
	TEXCOORD_1: 'uv2',
	COLOR_0: 'color',
	WEIGHTS_0: 'skinWeight',
	JOINTS_0: 'skinIndex',
};

const PATH_PROPERTIES = {
	scale: 'scale',
	translation: 'position',
	rotation: 'quaternion',
	weights: 'morphTargetInfluences'
};

const INTERPOLATION = {
	CUBICSPLINE: undefined, // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each
		                        // keyframe track will be initialized with a default interpolation type, then modified.
	LINEAR: InterpolateLinear,
	STEP: InterpolateDiscrete
};

const ALPHA_MODES = {
	OPAQUE: 'OPAQUE',
	MASK: 'MASK',
	BLEND: 'BLEND'
};

/**
 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material
 */
function createDefaultMaterial( cache ) {

	if ( cache[ 'DefaultMaterial' ] === undefined ) {

		cache[ 'DefaultMaterial' ] = new MeshStandardMaterial( {
			color: 0xFFFFFF,
			emissive: 0x000000,
			metalness: 1,
			roughness: 1,
			transparent: false,
			depthTest: true,
			side: FrontSide
		} );

	}

	return cache[ 'DefaultMaterial' ];

}

function addUnknownExtensionsToUserData( knownExtensions, object, objectDef ) {

	// Add unknown glTF extensions to an object's userData.

	for ( const name in objectDef.extensions ) {

		if ( knownExtensions[ name ] === undefined ) {

			object.userData.gltfExtensions = object.userData.gltfExtensions || {};
			object.userData.gltfExtensions[ name ] = objectDef.extensions[ name ];

		}

	}

}

/**
 * @param {Object3D|Material|BufferGeometry} object
 * @param {GLTF.definition} gltfDef
 */
function assignExtrasToUserData( object, gltfDef ) {

	if ( gltfDef.extras !== undefined ) {

		if ( typeof gltfDef.extras === 'object' ) {

			Object.assign( object.userData, gltfDef.extras );

		} else {

			console.warn( 'THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras );

		}

	}

}

/**
 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets
 *
 * @param {BufferGeometry} geometry
 * @param {Array<GLTF.Target>} targets
 * @param {GLTFParser} parser
 * @return {Promise<BufferGeometry>}
 */
function addMorphTargets( geometry, targets, parser ) {

	let hasMorphPosition = false;
	let hasMorphNormal = false;

	for ( let i = 0, il = targets.length; i < il; i ++ ) {

		const target = targets[ i ];

		if ( target.POSITION !== undefined ) hasMorphPosition = true;
		if ( target.NORMAL !== undefined ) hasMorphNormal = true;

		if ( hasMorphPosition && hasMorphNormal ) break;

	}

	if ( ! hasMorphPosition && ! hasMorphNormal ) return Promise.resolve( geometry );

	const pendingPositionAccessors = [];
	const pendingNormalAccessors = [];

	for ( let i = 0, il = targets.length; i < il; i ++ ) {

		const target = targets[ i ];

		if ( hasMorphPosition ) {

			const pendingAccessor = target.POSITION !== undefined
				? parser.getDependency( 'accessor', target.POSITION )
				: geometry.attributes.position;

			pendingPositionAccessors.push( pendingAccessor );

		}

		if ( hasMorphNormal ) {

			const pendingAccessor = target.NORMAL !== undefined
				? parser.getDependency( 'accessor', target.NORMAL )
				: geometry.attributes.normal;

			pendingNormalAccessors.push( pendingAccessor );

		}

	}

	return Promise.all( [
		Promise.all( pendingPositionAccessors ),
		Promise.all( pendingNormalAccessors )
	] ).then( function ( accessors ) {

		const morphPositions = accessors[ 0 ];
		const morphNormals = accessors[ 1 ];

		if ( hasMorphPosition ) geometry.morphAttributes.position = morphPositions;
		if ( hasMorphNormal ) geometry.morphAttributes.normal = morphNormals;
		geometry.morphTargetsRelative = true;

		return geometry;

	} );

}

/**
 * @param {Mesh} mesh
 * @param {GLTF.Mesh} meshDef
 */
function updateMorphTargets( mesh, meshDef ) {

	mesh.updateMorphTargets();

	if ( meshDef.weights !== undefined ) {

		for ( let i = 0, il = meshDef.weights.length; i < il; i ++ ) {

			mesh.morphTargetInfluences[ i ] = meshDef.weights[ i ];

		}

	}

	// .extras has user-defined data, so check that .extras.targetNames is an array.
	if ( meshDef.extras && Array.isArray( meshDef.extras.targetNames ) ) {

		const targetNames = meshDef.extras.targetNames;

		if ( mesh.morphTargetInfluences.length === targetNames.length ) {

			mesh.morphTargetDictionary = {};

			for ( let i = 0, il = targetNames.length; i < il; i ++ ) {

				mesh.morphTargetDictionary[ targetNames[ i ] ] = i;

			}

		} else {

			console.warn( 'THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.' );

		}

	}

}

function createPrimitiveKey( primitiveDef ) {

	const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ];
	let geometryKey;

	if ( dracoExtension ) {

		geometryKey = 'draco:' + dracoExtension.bufferView
				+ ':' + dracoExtension.indices
				+ ':' + createAttributesKey( dracoExtension.attributes );

	} else {

		geometryKey = primitiveDef.indices + ':' + createAttributesKey( primitiveDef.attributes ) + ':' + primitiveDef.mode;

	}

	return geometryKey;

}

function createAttributesKey( attributes ) {

	let attributesKey = '';

	const keys = Object.keys( attributes ).sort();

	for ( let i = 0, il = keys.length; i < il; i ++ ) {

		attributesKey += keys[ i ] + ':' + attributes[ keys[ i ] ] + ';';

	}

	return attributesKey;

}

function getNormalizedComponentScale( constructor ) {

	// Reference:
	// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data

	switch ( constructor ) {

		case Int8Array:
			return 1 / 127;

		case Uint8Array:
			return 1 / 255;

		case Int16Array:
			return 1 / 32767;

		case Uint16Array:
			return 1 / 65535;

		default:
			throw new Error( 'THREE.GLTFLoader: Unsupported normalized accessor component type.' );

	}

}

/* GLTF PARSER */

class GLTFParser {

	constructor( json = {}, options = {} ) {

		this.json = json;
		this.extensions = {};
		this.plugins = {};
		this.options = options;

		// loader object cache
		this.cache = new GLTFRegistry();

		// associations between Three.js objects and glTF elements
		this.associations = new Map();

		// BufferGeometry caching
		this.primitiveCache = {};

		// Object3D instance caches
		this.meshCache = { refs: {}, uses: {} };
		this.cameraCache = { refs: {}, uses: {} };
		this.lightCache = { refs: {}, uses: {} };

		this.textureCache = {};

		// Track node names, to ensure no duplicates
		this.nodeNamesUsed = {};

		// Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the
		// expensive work of uploading a texture to the GPU off the main thread.
		if ( typeof createImageBitmap !== 'undefined' && /Firefox|^((?!chrome|android).)*safari/i.test( navigator.userAgent ) === false ) {

			this.textureLoader = new ImageBitmapLoader( this.options.manager );

		} else {

			this.textureLoader = new TextureLoader( this.options.manager );

		}

		this.textureLoader.setCrossOrigin( this.options.crossOrigin );
		this.textureLoader.setRequestHeader( this.options.requestHeader );

		this.fileLoader = new FileLoader( this.options.manager );
		this.fileLoader.setResponseType( 'arraybuffer' );

		if ( this.options.crossOrigin === 'use-credentials' ) {

			this.fileLoader.setWithCredentials( true );

		}

	}

	setExtensions( extensions ) {

		this.extensions = extensions;

	}

	setPlugins( plugins ) {

		this.plugins = plugins;

	}

	parse( onLoad, onError ) {

		const parser = this;
		const json = this.json;
		const extensions = this.extensions;

		// Clear the loader cache
		this.cache.removeAll();

		// Mark the special nodes/meshes in json for efficient parse
		this._invokeAll( function ( ext ) {

			return ext._markDefs && ext._markDefs();

		} );

		Promise.all( this._invokeAll( function ( ext ) {

			return ext.beforeRoot && ext.beforeRoot();

		} ) ).then( function () {

			return Promise.all( [

				parser.getDependencies( 'scene' ),
				parser.getDependencies( 'animation' ),
				parser.getDependencies( 'camera' ),

			] );

		} ).then( function ( dependencies ) {

			const result = {
				scene: dependencies[ 0 ][ json.scene || 0 ],
				scenes: dependencies[ 0 ],
				animations: dependencies[ 1 ],
				cameras: dependencies[ 2 ],
				asset: json.asset,
				parser: parser,
				userData: {}
			};

			addUnknownExtensionsToUserData( extensions, result, json );

			assignExtrasToUserData( result, json );

			Promise.all( parser._invokeAll( function ( ext ) {

				return ext.afterRoot && ext.afterRoot( result );

			} ) ).then( function () {

				onLoad( result );

			} );

		} ).catch( onError );

	}

	/**
	 * Marks the special nodes/meshes in json for efficient parse.
	 */
	_markDefs() {

		const nodeDefs = this.json.nodes || [];
		const skinDefs = this.json.skins || [];
		const meshDefs = this.json.meshes || [];

		// Nothing in the node definition indicates whether it is a Bone or an
		// Object3D. Use the skins' joint references to mark bones.
		for ( let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex ++ ) {

			const joints = skinDefs[ skinIndex ].joints;

			for ( let i = 0, il = joints.length; i < il; i ++ ) {

				nodeDefs[ joints[ i ] ].isBone = true;

			}

		}

		// Iterate over all nodes, marking references to shared resources,
		// as well as skeleton joints.
		for ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {

			const nodeDef = nodeDefs[ nodeIndex ];

			if ( nodeDef.mesh !== undefined ) {

				this._addNodeRef( this.meshCache, nodeDef.mesh );

				// Nothing in the mesh definition indicates whether it is
				// a SkinnedMesh or Mesh. Use the node's mesh reference
				// to mark SkinnedMesh if node has skin.
				if ( nodeDef.skin !== undefined ) {

					meshDefs[ nodeDef.mesh ].isSkinnedMesh = true;

				}

			}

			if ( nodeDef.camera !== undefined ) {

				this._addNodeRef( this.cameraCache, nodeDef.camera );

			}

		}

	}

	/**
	 * Counts references to shared node / Object3D resources. These resources
	 * can be reused, or "instantiated", at multiple nodes in the scene
	 * hierarchy. Mesh, Camera, and Light instances are instantiated and must
	 * be marked. Non-scenegraph resources (like Materials, Geometries, and
	 * Textures) can be reused directly and are not marked here.
	 *
	 * Example: CesiumMilkTruck sample model reuses "Wheel" meshes.
	 */
	_addNodeRef( cache, index ) {

		if ( index === undefined ) return;

		if ( cache.refs[ index ] === undefined ) {

			cache.refs[ index ] = cache.uses[ index ] = 0;

		}

		cache.refs[ index ] ++;

	}

	/** Returns a reference to a shared resource, cloning it if necessary. */
	_getNodeRef( cache, index, object ) {

		if ( cache.refs[ index ] <= 1 ) return object;

		const ref = object.clone();

		// Propagates mappings to the cloned object, prevents mappings on the
		// original object from being lost.
		const updateMappings = ( original, clone ) => {

			const mappings = this.associations.get( original );
			if ( mappings != null ) {

				this.associations.set( clone, mappings );

			}

			for ( const [ i, child ] of original.children.entries() ) {

				updateMappings( child, clone.children[ i ] );

			}

		};

		updateMappings( object, ref );

		ref.name += '_instance_' + ( cache.uses[ index ] ++ );

		return ref;

	}

	_invokeOne( func ) {

		const extensions = Object.values( this.plugins );
		extensions.push( this );

		for ( let i = 0; i < extensions.length; i ++ ) {

			const result = func( extensions[ i ] );

			if ( result ) return result;

		}

		return null;

	}

	_invokeAll( func ) {

		const extensions = Object.values( this.plugins );
		extensions.unshift( this );

		const pending = [];

		for ( let i = 0; i < extensions.length; i ++ ) {

			const result = func( extensions[ i ] );

			if ( result ) pending.push( result );

		}

		return pending;

	}

	/**
	 * Requests the specified dependency asynchronously, with caching.
	 * @param {string} type
	 * @param {number} index
	 * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}
	 */
	getDependency( type, index ) {

		const cacheKey = type + ':' + index;
		let dependency = this.cache.get( cacheKey );

		if ( ! dependency ) {

			switch ( type ) {

				case 'scene':
					dependency = this.loadScene( index );
					break;

				case 'node':
					dependency = this.loadNode( index );
					break;

				case 'mesh':
					dependency = this._invokeOne( function ( ext ) {

						return ext.loadMesh && ext.loadMesh( index );

					} );
					break;

				case 'accessor':
					dependency = this.loadAccessor( index );
					break;

				case 'bufferView':
					dependency = this._invokeOne( function ( ext ) {

						return ext.loadBufferView && ext.loadBufferView( index );

					} );
					break;

				case 'buffer':
					dependency = this.loadBuffer( index );
					break;

				case 'material':
					dependency = this._invokeOne( function ( ext ) {

						return ext.loadMaterial && ext.loadMaterial( index );

					} );
					break;

				case 'texture':
					dependency = this._invokeOne( function ( ext ) {

						return ext.loadTexture && ext.loadTexture( index );

					} );
					break;

				case 'skin':
					dependency = this.loadSkin( index );
					break;

				case 'animation':
					dependency = this.loadAnimation( index );
					break;

				case 'camera':
					dependency = this.loadCamera( index );
					break;

				default:
					throw new Error( 'Unknown type: ' + type );

			}

			this.cache.add( cacheKey, dependency );

		}

		return dependency;

	}

	/**
	 * Requests all dependencies of the specified type asynchronously, with caching.
	 * @param {string} type
	 * @return {Promise<Array<Object>>}
	 */
	getDependencies( type ) {

		let dependencies = this.cache.get( type );

		if ( ! dependencies ) {

			const parser = this;
			const defs = this.json[ type + ( type === 'mesh' ? 'es' : 's' ) ] || [];

			dependencies = Promise.all( defs.map( function ( def, index ) {

				return parser.getDependency( type, index );

			} ) );

			this.cache.add( type, dependencies );

		}

		return dependencies;

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views
	 * @param {number} bufferIndex
	 * @return {Promise<ArrayBuffer>}
	 */
	loadBuffer( bufferIndex ) {

		const bufferDef = this.json.buffers[ bufferIndex ];
		const loader = this.fileLoader;

		if ( bufferDef.type && bufferDef.type !== 'arraybuffer' ) {

			throw new Error( 'THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.' );

		}

		// If present, GLB container is required to be the first buffer.
		if ( bufferDef.uri === undefined && bufferIndex === 0 ) {

			return Promise.resolve( this.extensions[ EXTENSIONS.KHR_BINARY_GLTF ].body );

		}

		const options = this.options;

		return new Promise( function ( resolve, reject ) {

			loader.load( LoaderUtils.resolveURL( bufferDef.uri, options.path ), resolve, undefined, function () {

				reject( new Error( 'THREE.GLTFLoader: Failed to load buffer "' + bufferDef.uri + '".' ) );

			} );

		} );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views
	 * @param {number} bufferViewIndex
	 * @return {Promise<ArrayBuffer>}
	 */
	loadBufferView( bufferViewIndex ) {

		const bufferViewDef = this.json.bufferViews[ bufferViewIndex ];

		return this.getDependency( 'buffer', bufferViewDef.buffer ).then( function ( buffer ) {

			const byteLength = bufferViewDef.byteLength || 0;
			const byteOffset = bufferViewDef.byteOffset || 0;
			return buffer.slice( byteOffset, byteOffset + byteLength );

		} );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors
	 * @param {number} accessorIndex
	 * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}
	 */
	loadAccessor( accessorIndex ) {

		const parser = this;
		const json = this.json;

		const accessorDef = this.json.accessors[ accessorIndex ];

		if ( accessorDef.bufferView === undefined && accessorDef.sparse === undefined ) {

			// Ignore empty accessors, which may be used to declare runtime
			// information about attributes coming from another source (e.g. Draco
			// compression extension).
			return Promise.resolve( null );

		}

		const pendingBufferViews = [];

		if ( accessorDef.bufferView !== undefined ) {

			pendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.bufferView ) );

		} else {

			pendingBufferViews.push( null );

		}

		if ( accessorDef.sparse !== undefined ) {

			pendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.indices.bufferView ) );
			pendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.values.bufferView ) );

		}

		return Promise.all( pendingBufferViews ).then( function ( bufferViews ) {

			const bufferView = bufferViews[ 0 ];

			const itemSize = WEBGL_TYPE_SIZES[ accessorDef.type ];
			const TypedArray = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];

			// For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.
			const elementBytes = TypedArray.BYTES_PER_ELEMENT;
			const itemBytes = elementBytes * itemSize;
			const byteOffset = accessorDef.byteOffset || 0;
			const byteStride = accessorDef.bufferView !== undefined ? json.bufferViews[ accessorDef.bufferView ].byteStride : undefined;
			const normalized = accessorDef.normalized === true;
			let array, bufferAttribute;

			// The buffer is not interleaved if the stride is the item size in bytes.
			if ( byteStride && byteStride !== itemBytes ) {

				// Each "slice" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer
				// This makes sure that IBA.count reflects accessor.count properly
				const ibSlice = Math.floor( byteOffset / byteStride );
				const ibCacheKey = 'InterleavedBuffer:' + accessorDef.bufferView + ':' + accessorDef.componentType + ':' + ibSlice + ':' + accessorDef.count;
				let ib = parser.cache.get( ibCacheKey );

				if ( ! ib ) {

					array = new TypedArray( bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes );

					// Integer parameters to IB/IBA are in array elements, not bytes.
					ib = new InterleavedBuffer( array, byteStride / elementBytes );

					parser.cache.add( ibCacheKey, ib );

				}

				bufferAttribute = new InterleavedBufferAttribute( ib, itemSize, ( byteOffset % byteStride ) / elementBytes, normalized );

			} else {

				if ( bufferView === null ) {

					array = new TypedArray( accessorDef.count * itemSize );

				} else {

					array = new TypedArray( bufferView, byteOffset, accessorDef.count * itemSize );

				}

				bufferAttribute = new BufferAttribute( array, itemSize, normalized );

			}

			// https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors
			if ( accessorDef.sparse !== undefined ) {

				const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;
				const TypedArrayIndices = WEBGL_COMPONENT_TYPES[ accessorDef.sparse.indices.componentType ];

				const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;
				const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;

				const sparseIndices = new TypedArrayIndices( bufferViews[ 1 ], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices );
				const sparseValues = new TypedArray( bufferViews[ 2 ], byteOffsetValues, accessorDef.sparse.count * itemSize );

				if ( bufferView !== null ) {

					// Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.
					bufferAttribute = new BufferAttribute( bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized );

				}

				for ( let i = 0, il = sparseIndices.length; i < il; i ++ ) {

					const index = sparseIndices[ i ];

					bufferAttribute.setX( index, sparseValues[ i * itemSize ] );
					if ( itemSize >= 2 ) bufferAttribute.setY( index, sparseValues[ i * itemSize + 1 ] );
					if ( itemSize >= 3 ) bufferAttribute.setZ( index, sparseValues[ i * itemSize + 2 ] );
					if ( itemSize >= 4 ) bufferAttribute.setW( index, sparseValues[ i * itemSize + 3 ] );
					if ( itemSize >= 5 ) throw new Error( 'THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.' );

				}

			}

			return bufferAttribute;

		} );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures
	 * @param {number} textureIndex
	 * @return {Promise<THREE.Texture>}
	 */
	loadTexture( textureIndex ) {

		const json = this.json;
		const options = this.options;
		const textureDef = json.textures[ textureIndex ];
		const source = json.images[ textureDef.source ];

		let loader = this.textureLoader;

		if ( source.uri ) {

			const handler = options.manager.getHandler( source.uri );
			if ( handler !== null ) loader = handler;

		}

		return this.loadTextureImage( textureIndex, source, loader );

	}

	loadTextureImage( textureIndex, source, loader ) {

		const parser = this;
		const json = this.json;
		const options = this.options;

		const textureDef = json.textures[ textureIndex ];

		const cacheKey = ( source.uri || source.bufferView ) + ':' + textureDef.sampler;

		if ( this.textureCache[ cacheKey ] ) {

			// See https://github.com/mrdoob/three.js/issues/21559.
			return this.textureCache[ cacheKey ];

		}

		const URL = self.URL || self.webkitURL;

		let sourceURI = source.uri || '';
		let isObjectURL = false;

		if ( source.bufferView !== undefined ) {

			// Load binary image data from bufferView, if provided.

			sourceURI = parser.getDependency( 'bufferView', source.bufferView ).then( function ( bufferView ) {

				isObjectURL = true;
				const blob = new Blob( [ bufferView ], { type: source.mimeType } );
				sourceURI = URL.createObjectURL( blob );
				return sourceURI;

			} );

		} else if ( source.uri === undefined ) {

			throw new Error( 'THREE.GLTFLoader: Image ' + textureIndex + ' is missing URI and bufferView' );

		}

		const promise = Promise.resolve( sourceURI ).then( function ( sourceURI ) {

			return new Promise( function ( resolve, reject ) {

				let onLoad = resolve;

				if ( loader.isImageBitmapLoader === true ) {

					onLoad = function ( imageBitmap ) {

						const texture = new Texture( imageBitmap );
						texture.needsUpdate = true;

						resolve( texture );

					};

				}

				loader.load( LoaderUtils.resolveURL( sourceURI, options.path ), onLoad, undefined, reject );

			} );

		} ).then( function ( texture ) {

			// Clean up resources and configure Texture.

			if ( isObjectURL === true ) {

				URL.revokeObjectURL( sourceURI );

			}

			texture.flipY = false;

			if ( textureDef.name ) texture.name = textureDef.name;

			const samplers = json.samplers || {};
			const sampler = samplers[ textureDef.sampler ] || {};

			texture.magFilter = WEBGL_FILTERS[ sampler.magFilter ] || LinearFilter;
			texture.minFilter = WEBGL_FILTERS[ sampler.minFilter ] || LinearMipmapLinearFilter;
			texture.wrapS = WEBGL_WRAPPINGS[ sampler.wrapS ] || RepeatWrapping;
			texture.wrapT = WEBGL_WRAPPINGS[ sampler.wrapT ] || RepeatWrapping;

			parser.associations.set( texture, { textures: textureIndex } );

			return texture;

		} ).catch( function () {

			console.error( 'THREE.GLTFLoader: Couldn\'t load texture', sourceURI );
			return null;

		} );

		this.textureCache[ cacheKey ] = promise;

		return promise;

	}

	/**
	 * Asynchronously assigns a texture to the given material parameters.
	 * @param {Object} materialParams
	 * @param {string} mapName
	 * @param {Object} mapDef
	 * @return {Promise<Texture>}
	 */
	assignTexture( materialParams, mapName, mapDef ) {

		const parser = this;

		return this.getDependency( 'texture', mapDef.index ).then( function ( texture ) {

			// Materials sample aoMap from UV set 1 and other maps from UV set 0 - this can't be configured
			// However, we will copy UV set 0 to UV set 1 on demand for aoMap
			if ( mapDef.texCoord !== undefined && mapDef.texCoord != 0 && ! ( mapName === 'aoMap' && mapDef.texCoord == 1 ) ) {

				console.warn( 'THREE.GLTFLoader: Custom UV set ' + mapDef.texCoord + ' for texture ' + mapName + ' not yet supported.' );

			}

			if ( parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] ) {

				const transform = mapDef.extensions !== undefined ? mapDef.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] : undefined;

				if ( transform ) {

					const gltfReference = parser.associations.get( texture );
					texture = parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ].extendTexture( texture, transform );
					parser.associations.set( texture, gltfReference );

				}

			}

			materialParams[ mapName ] = texture;

			return texture;

		} );

	}

	/**
	 * Assigns final material to a Mesh, Line, or Points instance. The instance
	 * already has a material (generated from the glTF material options alone)
	 * but reuse of the same glTF material may require multiple threejs materials
	 * to accommodate different primitive types, defines, etc. New materials will
	 * be created if necessary, and reused from a cache.
	 * @param  {Object3D} mesh Mesh, Line, or Points instance.
	 */
	assignFinalMaterial( mesh ) {

		const geometry = mesh.geometry;
		let material = mesh.material;

		const useDerivativeTangents = geometry.attributes.tangent === undefined;
		const useVertexColors = geometry.attributes.color !== undefined;
		const useFlatShading = geometry.attributes.normal === undefined;

		if ( mesh.isPoints ) {

			const cacheKey = 'PointsMaterial:' + material.uuid;

			let pointsMaterial = this.cache.get( cacheKey );

			if ( ! pointsMaterial ) {

				pointsMaterial = new PointsMaterial();
				Material.prototype.copy.call( pointsMaterial, material );
				pointsMaterial.color.copy( material.color );
				pointsMaterial.map = material.map;
				pointsMaterial.sizeAttenuation = false; // glTF spec says points should be 1px

				this.cache.add( cacheKey, pointsMaterial );

			}

			material = pointsMaterial;

		} else if ( mesh.isLine ) {

			const cacheKey = 'LineBasicMaterial:' + material.uuid;

			let lineMaterial = this.cache.get( cacheKey );

			if ( ! lineMaterial ) {

				lineMaterial = new LineBasicMaterial();
				Material.prototype.copy.call( lineMaterial, material );
				lineMaterial.color.copy( material.color );

				this.cache.add( cacheKey, lineMaterial );

			}

			material = lineMaterial;

		}

		// Clone the material if it will be modified
		if ( useDerivativeTangents || useVertexColors || useFlatShading ) {

			let cacheKey = 'ClonedMaterial:' + material.uuid + ':';

			if ( material.isGLTFSpecularGlossinessMaterial ) cacheKey += 'specular-glossiness:';
			if ( useDerivativeTangents ) cacheKey += 'derivative-tangents:';
			if ( useVertexColors ) cacheKey += 'vertex-colors:';
			if ( useFlatShading ) cacheKey += 'flat-shading:';

			let cachedMaterial = this.cache.get( cacheKey );

			if ( ! cachedMaterial ) {

				cachedMaterial = material.clone();

				if ( useVertexColors ) cachedMaterial.vertexColors = true;
				if ( useFlatShading ) cachedMaterial.flatShading = true;

				if ( useDerivativeTangents ) {

					// https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995
					if ( cachedMaterial.normalScale ) cachedMaterial.normalScale.y *= - 1;
					if ( cachedMaterial.clearcoatNormalScale ) cachedMaterial.clearcoatNormalScale.y *= - 1;

				}

				this.cache.add( cacheKey, cachedMaterial );

				this.associations.set( cachedMaterial, this.associations.get( material ) );

			}

			material = cachedMaterial;

		}

		// workarounds for mesh and geometry

		if ( material.aoMap && geometry.attributes.uv2 === undefined && geometry.attributes.uv !== undefined ) {

			geometry.setAttribute( 'uv2', geometry.attributes.uv );

		}

		mesh.material = material;

	}

	getMaterialType( /* materialIndex */ ) {

		return MeshStandardMaterial;

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials
	 * @param {number} materialIndex
	 * @return {Promise<Material>}
	 */
	loadMaterial( materialIndex ) {

		const parser = this;
		const json = this.json;
		const extensions = this.extensions;
		const materialDef = json.materials[ materialIndex ];

		let materialType;
		const materialParams = {};
		const materialExtensions = materialDef.extensions || {};

		const pending = [];

		if ( materialExtensions[ EXTENSIONS.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS ] ) {

			const sgExtension = extensions[ EXTENSIONS.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS ];
			materialType = sgExtension.getMaterialType();
			pending.push( sgExtension.extendParams( materialParams, materialDef, parser ) );

		} else if ( materialExtensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ] ) {

			const kmuExtension = extensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ];
			materialType = kmuExtension.getMaterialType();
			pending.push( kmuExtension.extendParams( materialParams, materialDef, parser ) );

		} else {

			// Specification:
			// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material

			const metallicRoughness = materialDef.pbrMetallicRoughness || {};

			materialParams.color = new Color( 1.0, 1.0, 1.0 );
			materialParams.opacity = 1.0;

			if ( Array.isArray( metallicRoughness.baseColorFactor ) ) {

				const array = metallicRoughness.baseColorFactor;

				materialParams.color.fromArray( array );
				materialParams.opacity = array[ 3 ];

			}

			if ( metallicRoughness.baseColorTexture !== undefined ) {

				pending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture ) );

			}

			materialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0;
			materialParams.roughness = metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0;

			if ( metallicRoughness.metallicRoughnessTexture !== undefined ) {

				pending.push( parser.assignTexture( materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture ) );
				pending.push( parser.assignTexture( materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture ) );

			}

			materialType = this._invokeOne( function ( ext ) {

				return ext.getMaterialType && ext.getMaterialType( materialIndex );

			} );

			pending.push( Promise.all( this._invokeAll( function ( ext ) {

				return ext.extendMaterialParams && ext.extendMaterialParams( materialIndex, materialParams );

			} ) ) );

		}

		if ( materialDef.doubleSided === true ) {

			materialParams.side = DoubleSide$1;

		}

		const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;

		if ( alphaMode === ALPHA_MODES.BLEND ) {

			materialParams.transparent = true;

			// See: https://github.com/mrdoob/three.js/issues/17706
			materialParams.depthWrite = false;

		} else {

			materialParams.transparent = false;

			if ( alphaMode === ALPHA_MODES.MASK ) {

				materialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5;

			}

		}

		if ( materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial ) {

			pending.push( parser.assignTexture( materialParams, 'normalMap', materialDef.normalTexture ) );

			materialParams.normalScale = new Vector2( 1, 1 );

			if ( materialDef.normalTexture.scale !== undefined ) {

				const scale = materialDef.normalTexture.scale;

				materialParams.normalScale.set( scale, scale );

			}

		}

		if ( materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial ) {

			pending.push( parser.assignTexture( materialParams, 'aoMap', materialDef.occlusionTexture ) );

			if ( materialDef.occlusionTexture.strength !== undefined ) {

				materialParams.aoMapIntensity = materialDef.occlusionTexture.strength;

			}

		}

		if ( materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial ) {

			materialParams.emissive = new Color().fromArray( materialDef.emissiveFactor );

		}

		if ( materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial ) {

			pending.push( parser.assignTexture( materialParams, 'emissiveMap', materialDef.emissiveTexture ) );

		}

		return Promise.all( pending ).then( function () {

			let material;

			if ( materialType === GLTFMeshStandardSGMaterial ) {

				material = extensions[ EXTENSIONS.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS ].createMaterial( materialParams );

			} else {

				material = new materialType( materialParams );

			}

			if ( materialDef.name ) material.name = materialDef.name;

			// baseColorTexture, emissiveTexture, and specularGlossinessTexture use sRGB encoding.
			if ( material.map ) material.map.encoding = sRGBEncoding;
			if ( material.emissiveMap ) material.emissiveMap.encoding = sRGBEncoding;

			assignExtrasToUserData( material, materialDef );

			parser.associations.set( material, { materials: materialIndex } );

			if ( materialDef.extensions ) addUnknownExtensionsToUserData( extensions, material, materialDef );

			return material;

		} );

	}

	/** When Object3D instances are targeted by animation, they need unique names. */
	createUniqueName( originalName ) {

		const sanitizedName = PropertyBinding.sanitizeNodeName( originalName || '' );

		let name = sanitizedName;

		for ( let i = 1; this.nodeNamesUsed[ name ]; ++ i ) {

			name = sanitizedName + '_' + i;

		}

		this.nodeNamesUsed[ name ] = true;

		return name;

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry
	 *
	 * Creates BufferGeometries from primitives.
	 *
	 * @param {Array<GLTF.Primitive>} primitives
	 * @return {Promise<Array<BufferGeometry>>}
	 */
	loadGeometries( primitives ) {

		const parser = this;
		const extensions = this.extensions;
		const cache = this.primitiveCache;

		function createDracoPrimitive( primitive ) {

			return extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ]
				.decodePrimitive( primitive, parser )
				.then( function ( geometry ) {

					return addPrimitiveAttributes( geometry, primitive, parser );

				} );

		}

		const pending = [];

		for ( let i = 0, il = primitives.length; i < il; i ++ ) {

			const primitive = primitives[ i ];
			const cacheKey = createPrimitiveKey( primitive );

			// See if we've already created this geometry
			const cached = cache[ cacheKey ];

			if ( cached ) {

				// Use the cached geometry if it exists
				pending.push( cached.promise );

			} else {

				let geometryPromise;

				if ( primitive.extensions && primitive.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ] ) {

					// Use DRACO geometry if available
					geometryPromise = createDracoPrimitive( primitive );

				} else {

					// Otherwise create a new geometry
					geometryPromise = addPrimitiveAttributes( new BufferGeometry(), primitive, parser );

				}

				// Cache this geometry
				cache[ cacheKey ] = { primitive: primitive, promise: geometryPromise };

				pending.push( geometryPromise );

			}

		}

		return Promise.all( pending );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes
	 * @param {number} meshIndex
	 * @return {Promise<Group|Mesh|SkinnedMesh>}
	 */
	loadMesh( meshIndex ) {

		const parser = this;
		const json = this.json;
		const extensions = this.extensions;

		const meshDef = json.meshes[ meshIndex ];
		const primitives = meshDef.primitives;

		const pending = [];

		for ( let i = 0, il = primitives.length; i < il; i ++ ) {

			const material = primitives[ i ].material === undefined
				? createDefaultMaterial( this.cache )
				: this.getDependency( 'material', primitives[ i ].material );

			pending.push( material );

		}

		pending.push( parser.loadGeometries( primitives ) );

		return Promise.all( pending ).then( function ( results ) {

			const materials = results.slice( 0, results.length - 1 );
			const geometries = results[ results.length - 1 ];

			const meshes = [];

			for ( let i = 0, il = geometries.length; i < il; i ++ ) {

				const geometry = geometries[ i ];
				const primitive = primitives[ i ];

				// 1. create Mesh

				let mesh;

				const material = materials[ i ];

				if ( primitive.mode === WEBGL_CONSTANTS.TRIANGLES ||
						primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ||
						primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ||
						primitive.mode === undefined ) {

					// .isSkinnedMesh isn't in glTF spec. See ._markDefs()
					mesh = meshDef.isSkinnedMesh === true
						? new SkinnedMesh( geometry, material )
						: new Mesh( geometry, material );

					if ( mesh.isSkinnedMesh === true && ! mesh.geometry.attributes.skinWeight.normalized ) {

						// we normalize floating point skin weight array to fix malformed assets (see #15319)
						// it's important to skip this for non-float32 data since normalizeSkinWeights assumes non-normalized inputs
						mesh.normalizeSkinWeights();

					}

					if ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ) {

						mesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleStripDrawMode );

					} else if ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ) {

						mesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleFanDrawMode );

					}

				} else if ( primitive.mode === WEBGL_CONSTANTS.LINES ) {

					mesh = new LineSegments( geometry, material );

				} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_STRIP ) {

					mesh = new Line( geometry, material );

				} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_LOOP ) {

					mesh = new LineLoop( geometry, material );

				} else if ( primitive.mode === WEBGL_CONSTANTS.POINTS ) {

					mesh = new Points( geometry, material );

				} else {

					throw new Error( 'THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode );

				}

				if ( Object.keys( mesh.geometry.morphAttributes ).length > 0 ) {

					updateMorphTargets( mesh, meshDef );

				}

				mesh.name = parser.createUniqueName( meshDef.name || ( 'mesh_' + meshIndex ) );

				assignExtrasToUserData( mesh, meshDef );

				if ( primitive.extensions ) addUnknownExtensionsToUserData( extensions, mesh, primitive );

				parser.assignFinalMaterial( mesh );

				meshes.push( mesh );

			}

			for ( let i = 0, il = meshes.length; i < il; i ++ ) {

				parser.associations.set( meshes[ i ], {
					meshes: meshIndex,
					primitives: i
				} );

			}

			if ( meshes.length === 1 ) {

				return meshes[ 0 ];

			}

			const group = new Group();

			parser.associations.set( group, { meshes: meshIndex } );

			for ( let i = 0, il = meshes.length; i < il; i ++ ) {

				group.add( meshes[ i ] );

			}

			return group;

		} );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras
	 * @param {number} cameraIndex
	 * @return {Promise<THREE.Camera>}
	 */
	loadCamera( cameraIndex ) {

		let camera;
		const cameraDef = this.json.cameras[ cameraIndex ];
		const params = cameraDef[ cameraDef.type ];

		if ( ! params ) {

			console.warn( 'THREE.GLTFLoader: Missing camera parameters.' );
			return;

		}

		if ( cameraDef.type === 'perspective' ) {

			camera = new PerspectiveCamera( MathUtils.radToDeg( params.yfov ), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6 );

		} else if ( cameraDef.type === 'orthographic' ) {

			camera = new OrthographicCamera( - params.xmag, params.xmag, params.ymag, - params.ymag, params.znear, params.zfar );

		}

		if ( cameraDef.name ) camera.name = this.createUniqueName( cameraDef.name );

		assignExtrasToUserData( camera, cameraDef );

		return Promise.resolve( camera );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins
	 * @param {number} skinIndex
	 * @return {Promise<Object>}
	 */
	loadSkin( skinIndex ) {

		const skinDef = this.json.skins[ skinIndex ];

		const skinEntry = { joints: skinDef.joints };

		if ( skinDef.inverseBindMatrices === undefined ) {

			return Promise.resolve( skinEntry );

		}

		return this.getDependency( 'accessor', skinDef.inverseBindMatrices ).then( function ( accessor ) {

			skinEntry.inverseBindMatrices = accessor;

			return skinEntry;

		} );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations
	 * @param {number} animationIndex
	 * @return {Promise<AnimationClip>}
	 */
	loadAnimation( animationIndex ) {

		const json = this.json;

		const animationDef = json.animations[ animationIndex ];

		const pendingNodes = [];
		const pendingInputAccessors = [];
		const pendingOutputAccessors = [];
		const pendingSamplers = [];
		const pendingTargets = [];

		for ( let i = 0, il = animationDef.channels.length; i < il; i ++ ) {

			const channel = animationDef.channels[ i ];
			const sampler = animationDef.samplers[ channel.sampler ];
			const target = channel.target;
			const name = target.node !== undefined ? target.node : target.id; // NOTE: target.id is deprecated.
			const input = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.input ] : sampler.input;
			const output = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.output ] : sampler.output;

			pendingNodes.push( this.getDependency( 'node', name ) );
			pendingInputAccessors.push( this.getDependency( 'accessor', input ) );
			pendingOutputAccessors.push( this.getDependency( 'accessor', output ) );
			pendingSamplers.push( sampler );
			pendingTargets.push( target );

		}

		return Promise.all( [

			Promise.all( pendingNodes ),
			Promise.all( pendingInputAccessors ),
			Promise.all( pendingOutputAccessors ),
			Promise.all( pendingSamplers ),
			Promise.all( pendingTargets )

		] ).then( function ( dependencies ) {

			const nodes = dependencies[ 0 ];
			const inputAccessors = dependencies[ 1 ];
			const outputAccessors = dependencies[ 2 ];
			const samplers = dependencies[ 3 ];
			const targets = dependencies[ 4 ];

			const tracks = [];

			for ( let i = 0, il = nodes.length; i < il; i ++ ) {

				const node = nodes[ i ];
				const inputAccessor = inputAccessors[ i ];
				const outputAccessor = outputAccessors[ i ];
				const sampler = samplers[ i ];
				const target = targets[ i ];

				if ( node === undefined ) continue;

				node.updateMatrix();
				node.matrixAutoUpdate = true;

				let TypedKeyframeTrack;

				switch ( PATH_PROPERTIES[ target.path ] ) {

					case PATH_PROPERTIES.weights:

						TypedKeyframeTrack = NumberKeyframeTrack;
						break;

					case PATH_PROPERTIES.rotation:

						TypedKeyframeTrack = QuaternionKeyframeTrack;
						break;

					case PATH_PROPERTIES.position:
					case PATH_PROPERTIES.scale:
					default:

						TypedKeyframeTrack = VectorKeyframeTrack;
						break;

				}

				const targetName = node.name ? node.name : node.uuid;

				const interpolation = sampler.interpolation !== undefined ? INTERPOLATION[ sampler.interpolation ] : InterpolateLinear;

				const targetNames = [];

				if ( PATH_PROPERTIES[ target.path ] === PATH_PROPERTIES.weights ) {

					node.traverse( function ( object ) {

						if ( object.morphTargetInfluences ) {

							targetNames.push( object.name ? object.name : object.uuid );

						}

					} );

				} else {

					targetNames.push( targetName );

				}

				let outputArray = outputAccessor.array;

				if ( outputAccessor.normalized ) {

					const scale = getNormalizedComponentScale( outputArray.constructor );
					const scaled = new Float32Array( outputArray.length );

					for ( let j = 0, jl = outputArray.length; j < jl; j ++ ) {

						scaled[ j ] = outputArray[ j ] * scale;

					}

					outputArray = scaled;

				}

				for ( let j = 0, jl = targetNames.length; j < jl; j ++ ) {

					const track = new TypedKeyframeTrack(
						targetNames[ j ] + '.' + PATH_PROPERTIES[ target.path ],
						inputAccessor.array,
						outputArray,
						interpolation
					);

					// Override interpolation with custom factory method.
					if ( sampler.interpolation === 'CUBICSPLINE' ) {

						track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline( result ) {

							// A CUBICSPLINE keyframe in glTF has three output values for each input value,
							// representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()
							// must be divided by three to get the interpolant's sampleSize argument.

							const interpolantType = ( this instanceof QuaternionKeyframeTrack ) ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;

							return new interpolantType( this.times, this.values, this.getValueSize() / 3, result );

						};

						// Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.
						track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;

					}

					tracks.push( track );

				}

			}

			const name = animationDef.name ? animationDef.name : 'animation_' + animationIndex;

			return new AnimationClip( name, undefined, tracks );

		} );

	}

	createNodeMesh( nodeIndex ) {

		const json = this.json;
		const parser = this;
		const nodeDef = json.nodes[ nodeIndex ];

		if ( nodeDef.mesh === undefined ) return null;

		return parser.getDependency( 'mesh', nodeDef.mesh ).then( function ( mesh ) {

			const node = parser._getNodeRef( parser.meshCache, nodeDef.mesh, mesh );

			// if weights are provided on the node, override weights on the mesh.
			if ( nodeDef.weights !== undefined ) {

				node.traverse( function ( o ) {

					if ( ! o.isMesh ) return;

					for ( let i = 0, il = nodeDef.weights.length; i < il; i ++ ) {

						o.morphTargetInfluences[ i ] = nodeDef.weights[ i ];

					}

				} );

			}

			return node;

		} );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy
	 * @param {number} nodeIndex
	 * @return {Promise<Object3D>}
	 */
	loadNode( nodeIndex ) {

		const json = this.json;
		const extensions = this.extensions;
		const parser = this;

		const nodeDef = json.nodes[ nodeIndex ];

		// reserve node's name before its dependencies, so the root has the intended name.
		const nodeName = nodeDef.name ? parser.createUniqueName( nodeDef.name ) : '';

		return ( function () {

			const pending = [];

			const meshPromise = parser._invokeOne( function ( ext ) {

				return ext.createNodeMesh && ext.createNodeMesh( nodeIndex );

			} );

			if ( meshPromise ) {

				pending.push( meshPromise );

			}

			if ( nodeDef.camera !== undefined ) {

				pending.push( parser.getDependency( 'camera', nodeDef.camera ).then( function ( camera ) {

					return parser._getNodeRef( parser.cameraCache, nodeDef.camera, camera );

				} ) );

			}

			parser._invokeAll( function ( ext ) {

				return ext.createNodeAttachment && ext.createNodeAttachment( nodeIndex );

			} ).forEach( function ( promise ) {

				pending.push( promise );

			} );

			return Promise.all( pending );

		}() ).then( function ( objects ) {

			let node;

			// .isBone isn't in glTF spec. See ._markDefs
			if ( nodeDef.isBone === true ) {

				node = new Bone();

			} else if ( objects.length > 1 ) {

				node = new Group();

			} else if ( objects.length === 1 ) {

				node = objects[ 0 ];

			} else {

				node = new Object3D();

			}

			if ( node !== objects[ 0 ] ) {

				for ( let i = 0, il = objects.length; i < il; i ++ ) {

					node.add( objects[ i ] );

				}

			}

			if ( nodeDef.name ) {

				node.userData.name = nodeDef.name;
				node.name = nodeName;

			}

			assignExtrasToUserData( node, nodeDef );

			if ( nodeDef.extensions ) addUnknownExtensionsToUserData( extensions, node, nodeDef );

			if ( nodeDef.matrix !== undefined ) {

				const matrix = new Matrix4();
				matrix.fromArray( nodeDef.matrix );
				node.applyMatrix4( matrix );

			} else {

				if ( nodeDef.translation !== undefined ) {

					node.position.fromArray( nodeDef.translation );

				}

				if ( nodeDef.rotation !== undefined ) {

					node.quaternion.fromArray( nodeDef.rotation );

				}

				if ( nodeDef.scale !== undefined ) {

					node.scale.fromArray( nodeDef.scale );

				}

			}

			if ( ! parser.associations.has( node ) ) {

				parser.associations.set( node, {} );

			}

			parser.associations.get( node ).nodes = nodeIndex;

			return node;

		} );

	}

	/**
	 * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes
	 * @param {number} sceneIndex
	 * @return {Promise<Group>}
	 */
	loadScene( sceneIndex ) {

		const json = this.json;
		const extensions = this.extensions;
		const sceneDef = this.json.scenes[ sceneIndex ];
		const parser = this;

		// Loader returns Group, not Scene.
		// See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172
		const scene = new Group();
		if ( sceneDef.name ) scene.name = parser.createUniqueName( sceneDef.name );

		assignExtrasToUserData( scene, sceneDef );

		if ( sceneDef.extensions ) addUnknownExtensionsToUserData( extensions, scene, sceneDef );

		const nodeIds = sceneDef.nodes || [];

		const pending = [];

		for ( let i = 0, il = nodeIds.length; i < il; i ++ ) {

			pending.push( buildNodeHierarchy( nodeIds[ i ], scene, json, parser ) );

		}

		return Promise.all( pending ).then( function () {

			// Removes dangling associations, associations that reference a node that
			// didn't make it into the scene.
			const reduceAssociations = ( node ) => {

				const reducedAssociations = new Map();

				for ( const [ key, value ] of parser.associations ) {

					if ( key instanceof Material || key instanceof Texture ) {

						reducedAssociations.set( key, value );

					}

				}

				node.traverse( ( node ) => {

					const mappings = parser.associations.get( node );

					if ( mappings != null ) {

						reducedAssociations.set( node, mappings );

					}

				} );

				return reducedAssociations;

			};

			parser.associations = reduceAssociations( scene );

			return scene;

		} );

	}

}

function buildNodeHierarchy( nodeId, parentObject, json, parser ) {

	const nodeDef = json.nodes[ nodeId ];

	return parser.getDependency( 'node', nodeId ).then( function ( node ) {

		if ( nodeDef.skin === undefined ) return node;

		// build skeleton here as well

		let skinEntry;

		return parser.getDependency( 'skin', nodeDef.skin ).then( function ( skin ) {

			skinEntry = skin;

			const pendingJoints = [];

			for ( let i = 0, il = skinEntry.joints.length; i < il; i ++ ) {

				pendingJoints.push( parser.getDependency( 'node', skinEntry.joints[ i ] ) );

			}

			return Promise.all( pendingJoints );

		} ).then( function ( jointNodes ) {

			node.traverse( function ( mesh ) {

				if ( ! mesh.isMesh ) return;

				const bones = [];
				const boneInverses = [];

				for ( let j = 0, jl = jointNodes.length; j < jl; j ++ ) {

					const jointNode = jointNodes[ j ];

					if ( jointNode ) {

						bones.push( jointNode );

						const mat = new Matrix4();

						if ( skinEntry.inverseBindMatrices !== undefined ) {

							mat.fromArray( skinEntry.inverseBindMatrices.array, j * 16 );

						}

						boneInverses.push( mat );

					} else {

						console.warn( 'THREE.GLTFLoader: Joint "%s" could not be found.', skinEntry.joints[ j ] );

					}

				}

				mesh.bind( new Skeleton( bones, boneInverses ), mesh.matrixWorld );

			} );

			return node;

		} );

	} ).then( function ( node ) {

		// build node hierachy

		parentObject.add( node );

		const pending = [];

		if ( nodeDef.children ) {

			const children = nodeDef.children;

			for ( let i = 0, il = children.length; i < il; i ++ ) {

				const child = children[ i ];
				pending.push( buildNodeHierarchy( child, node, json, parser ) );

			}

		}

		return Promise.all( pending );

	} );

}

/**
 * @param {BufferGeometry} geometry
 * @param {GLTF.Primitive} primitiveDef
 * @param {GLTFParser} parser
 */
function computeBounds( geometry, primitiveDef, parser ) {

	const attributes = primitiveDef.attributes;

	const box = new Box3();

	if ( attributes.POSITION !== undefined ) {

		const accessor = parser.json.accessors[ attributes.POSITION ];

		const min = accessor.min;
		const max = accessor.max;

		// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.

		if ( min !== undefined && max !== undefined ) {

			box.set(
				new Vector3( min[ 0 ], min[ 1 ], min[ 2 ] ),
				new Vector3( max[ 0 ], max[ 1 ], max[ 2 ] )
			);

			if ( accessor.normalized ) {

				const boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );
				box.min.multiplyScalar( boxScale );
				box.max.multiplyScalar( boxScale );

			}

		} else {

			console.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );

			return;

		}

	} else {

		return;

	}

	const targets = primitiveDef.targets;

	if ( targets !== undefined ) {

		const maxDisplacement = new Vector3();
		const vector = new Vector3();

		for ( let i = 0, il = targets.length; i < il; i ++ ) {

			const target = targets[ i ];

			if ( target.POSITION !== undefined ) {

				const accessor = parser.json.accessors[ target.POSITION ];
				const min = accessor.min;
				const max = accessor.max;

				// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.

				if ( min !== undefined && max !== undefined ) {

					// we need to get max of absolute components because target weight is [-1,1]
					vector.setX( Math.max( Math.abs( min[ 0 ] ), Math.abs( max[ 0 ] ) ) );
					vector.setY( Math.max( Math.abs( min[ 1 ] ), Math.abs( max[ 1 ] ) ) );
					vector.setZ( Math.max( Math.abs( min[ 2 ] ), Math.abs( max[ 2 ] ) ) );


					if ( accessor.normalized ) {

						const boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );
						vector.multiplyScalar( boxScale );

					}

					// Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative
					// to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets
					// are used to implement key-frame animations and as such only two are active at a time - this results in very large
					// boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.
					maxDisplacement.max( vector );

				} else {

					console.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );

				}

			}

		}

		// As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.
		box.expandByVector( maxDisplacement );

	}

	geometry.boundingBox = box;

	const sphere = new Sphere();

	box.getCenter( sphere.center );
	sphere.radius = box.min.distanceTo( box.max ) / 2;

	geometry.boundingSphere = sphere;

}

/**
 * @param {BufferGeometry} geometry
 * @param {GLTF.Primitive} primitiveDef
 * @param {GLTFParser} parser
 * @return {Promise<BufferGeometry>}
 */
function addPrimitiveAttributes( geometry, primitiveDef, parser ) {

	const attributes = primitiveDef.attributes;

	const pending = [];

	function assignAttributeAccessor( accessorIndex, attributeName ) {

		return parser.getDependency( 'accessor', accessorIndex )
			.then( function ( accessor ) {

				geometry.setAttribute( attributeName, accessor );

			} );

	}

	for ( const gltfAttributeName in attributes ) {

		const threeAttributeName = ATTRIBUTES[ gltfAttributeName ] || gltfAttributeName.toLowerCase();

		// Skip attributes already provided by e.g. Draco extension.
		if ( threeAttributeName in geometry.attributes ) continue;

		pending.push( assignAttributeAccessor( attributes[ gltfAttributeName ], threeAttributeName ) );

	}

	if ( primitiveDef.indices !== undefined && ! geometry.index ) {

		const accessor = parser.getDependency( 'accessor', primitiveDef.indices ).then( function ( accessor ) {

			geometry.setIndex( accessor );

		} );

		pending.push( accessor );

	}

	assignExtrasToUserData( geometry, primitiveDef );

	computeBounds( geometry, primitiveDef, parser );

	return Promise.all( pending ).then( function () {

		return primitiveDef.targets !== undefined
			? addMorphTargets( geometry, primitiveDef.targets, parser )
			: geometry;

	} );

}

/**
 * @param {BufferGeometry} geometry
 * @param {Number} drawMode
 * @return {BufferGeometry}
 */
function toTrianglesDrawMode( geometry, drawMode ) {

	let index = geometry.getIndex();

	// generate index if not present

	if ( index === null ) {

		const indices = [];

		const position = geometry.getAttribute( 'position' );

		if ( position !== undefined ) {

			for ( let i = 0; i < position.count; i ++ ) {

				indices.push( i );

			}

			geometry.setIndex( indices );
			index = geometry.getIndex();

		} else {

			console.error( 'THREE.GLTFLoader.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.' );
			return geometry;

		}

	}

	//

	const numberOfTriangles = index.count - 2;
	const newIndices = [];

	if ( drawMode === TriangleFanDrawMode ) {

		// gl.TRIANGLE_FAN

		for ( let i = 1; i <= numberOfTriangles; i ++ ) {

			newIndices.push( index.getX( 0 ) );
			newIndices.push( index.getX( i ) );
			newIndices.push( index.getX( i + 1 ) );

		}

	} else {

		// gl.TRIANGLE_STRIP

		for ( let i = 0; i < numberOfTriangles; i ++ ) {

			if ( i % 2 === 0 ) {

				newIndices.push( index.getX( i ) );
				newIndices.push( index.getX( i + 1 ) );
				newIndices.push( index.getX( i + 2 ) );


			} else {

				newIndices.push( index.getX( i + 2 ) );
				newIndices.push( index.getX( i + 1 ) );
				newIndices.push( index.getX( i ) );

			}

		}

	}

	if ( ( newIndices.length / 3 ) !== numberOfTriangles ) {

		console.error( 'THREE.GLTFLoader.toTrianglesDrawMode(): Unable to generate correct amount of triangles.' );

	}

	// build final geometry

	const newGeometry = geometry.clone();
	newGeometry.setIndex( newIndices );

	return newGeometry;

}

const _taskCache$1 = new WeakMap();

class DRACOLoader extends Loader {

	constructor( manager ) {

		super( manager );

		this.decoderPath = '';
		this.decoderConfig = {};
		this.decoderBinary = null;
		this.decoderPending = null;

		this.workerLimit = 4;
		this.workerPool = [];
		this.workerNextTaskID = 1;
		this.workerSourceURL = '';

		this.defaultAttributeIDs = {
			position: 'POSITION',
			normal: 'NORMAL',
			color: 'COLOR',
			uv: 'TEX_COORD'
		};
		this.defaultAttributeTypes = {
			position: 'Float32Array',
			normal: 'Float32Array',
			color: 'Float32Array',
			uv: 'Float32Array'
		};

	}

	setDecoderPath( path ) {

		this.decoderPath = path;

		return this;

	}

	setDecoderConfig( config ) {

		this.decoderConfig = config;

		return this;

	}

	setWorkerLimit( workerLimit ) {

		this.workerLimit = workerLimit;

		return this;

	}

	load( url, onLoad, onProgress, onError ) {

		const loader = new FileLoader( this.manager );

		loader.setPath( this.path );
		loader.setResponseType( 'arraybuffer' );
		loader.setRequestHeader( this.requestHeader );
		loader.setWithCredentials( this.withCredentials );

		loader.load( url, ( buffer ) => {

			const taskConfig = {
				attributeIDs: this.defaultAttributeIDs,
				attributeTypes: this.defaultAttributeTypes,
				useUniqueIDs: false
			};

			this.decodeGeometry( buffer, taskConfig )
				.then( onLoad )
				.catch( onError );

		}, onProgress, onError );

	}

	/** @deprecated Kept for backward-compatibility with previous DRACOLoader versions. */
	decodeDracoFile( buffer, callback, attributeIDs, attributeTypes ) {

		const taskConfig = {
			attributeIDs: attributeIDs || this.defaultAttributeIDs,
			attributeTypes: attributeTypes || this.defaultAttributeTypes,
			useUniqueIDs: !! attributeIDs
		};

		this.decodeGeometry( buffer, taskConfig ).then( callback );

	}

	decodeGeometry( buffer, taskConfig ) {

		// TODO: For backward-compatibility, support 'attributeTypes' objects containing
		// references (rather than names) to typed array constructors. These must be
		// serialized before sending them to the worker.
		for ( const attribute in taskConfig.attributeTypes ) {

			const type = taskConfig.attributeTypes[ attribute ];

			if ( type.BYTES_PER_ELEMENT !== undefined ) {

				taskConfig.attributeTypes[ attribute ] = type.name;

			}

		}

		//

		const taskKey = JSON.stringify( taskConfig );

		// Check for an existing task using this buffer. A transferred buffer cannot be transferred
		// again from this thread.
		if ( _taskCache$1.has( buffer ) ) {

			const cachedTask = _taskCache$1.get( buffer );

			if ( cachedTask.key === taskKey ) {

				return cachedTask.promise;

			} else if ( buffer.byteLength === 0 ) {

				// Technically, it would be possible to wait for the previous task to complete,
				// transfer the buffer back, and decode again with the second configuration. That
				// is complex, and I don't know of any reason to decode a Draco buffer twice in
				// different ways, so this is left unimplemented.
				throw new Error(

					'THREE.DRACOLoader: Unable to re-decode a buffer with different ' +
					'settings. Buffer has already been transferred.'

				);

			}

		}

		//

		let worker;
		const taskID = this.workerNextTaskID ++;
		const taskCost = buffer.byteLength;

		// Obtain a worker and assign a task, and construct a geometry instance
		// when the task completes.
		const geometryPending = this._getWorker( taskID, taskCost )
			.then( ( _worker ) => {

				worker = _worker;

				return new Promise( ( resolve, reject ) => {

					worker._callbacks[ taskID ] = { resolve, reject };

					worker.postMessage( { type: 'decode', id: taskID, taskConfig, buffer }, [ buffer ] );

					// this.debug();

				} );

			} )
			.then( ( message ) => this._createGeometry( message.geometry ) );

		// Remove task from the task list.
		// Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)
		geometryPending
			.catch( () => true )
			.then( () => {

				if ( worker && taskID ) {

					this._releaseTask( worker, taskID );

					// this.debug();

				}

			} );

		// Cache the task result.
		_taskCache$1.set( buffer, {

			key: taskKey,
			promise: geometryPending

		} );

		return geometryPending;

	}

	_createGeometry( geometryData ) {

		const geometry = new BufferGeometry();

		if ( geometryData.index ) {

			geometry.setIndex( new BufferAttribute( geometryData.index.array, 1 ) );

		}

		for ( let i = 0; i < geometryData.attributes.length; i ++ ) {

			const attribute = geometryData.attributes[ i ];
			const name = attribute.name;
			const array = attribute.array;
			const itemSize = attribute.itemSize;

			geometry.setAttribute( name, new BufferAttribute( array, itemSize ) );

		}

		return geometry;

	}

	_loadLibrary( url, responseType ) {

		const loader = new FileLoader( this.manager );
		loader.setPath( this.decoderPath );
		loader.setResponseType( responseType );
		loader.setWithCredentials( this.withCredentials );

		return new Promise( ( resolve, reject ) => {

			loader.load( url, resolve, undefined, reject );

		} );

	}

	preload() {

		this._initDecoder();

		return this;

	}

	_initDecoder() {

		if ( this.decoderPending ) return this.decoderPending;

		const useJS = typeof WebAssembly !== 'object' || this.decoderConfig.type === 'js';
		const librariesPending = [];

		if ( useJS ) {

			librariesPending.push( this._loadLibrary( 'draco_decoder.js', 'text' ) );

		} else {

			librariesPending.push( this._loadLibrary( 'draco_wasm_wrapper.js', 'text' ) );
			librariesPending.push( this._loadLibrary( 'draco_decoder.wasm', 'arraybuffer' ) );

		}

		this.decoderPending = Promise.all( librariesPending )
			.then( ( libraries ) => {

				const jsContent = libraries[ 0 ];

				if ( ! useJS ) {

					this.decoderConfig.wasmBinary = libraries[ 1 ];

				}

				const fn = DRACOWorker.toString();

				const body = [
					'/* draco decoder */',
					jsContent,
					'',
					'/* worker */',
					fn.substring( fn.indexOf( '{' ) + 1, fn.lastIndexOf( '}' ) )
				].join( '\n' );

				this.workerSourceURL = URL.createObjectURL( new Blob( [ body ] ) );

			} );

		return this.decoderPending;

	}

	_getWorker( taskID, taskCost ) {

		return this._initDecoder().then( () => {

			if ( this.workerPool.length < this.workerLimit ) {

				const worker = new Worker( this.workerSourceURL );

				worker._callbacks = {};
				worker._taskCosts = {};
				worker._taskLoad = 0;

				worker.postMessage( { type: 'init', decoderConfig: this.decoderConfig } );

				worker.onmessage = function ( e ) {

					const message = e.data;

					switch ( message.type ) {

						case 'decode':
							worker._callbacks[ message.id ].resolve( message );
							break;

						case 'error':
							worker._callbacks[ message.id ].reject( message );
							break;

						default:
							console.error( 'THREE.DRACOLoader: Unexpected message, "' + message.type + '"' );

					}

				};

				this.workerPool.push( worker );

			} else {

				this.workerPool.sort( function ( a, b ) {

					return a._taskLoad > b._taskLoad ? - 1 : 1;

				} );

			}

			const worker = this.workerPool[ this.workerPool.length - 1 ];
			worker._taskCosts[ taskID ] = taskCost;
			worker._taskLoad += taskCost;
			return worker;

		} );

	}

	_releaseTask( worker, taskID ) {

		worker._taskLoad -= worker._taskCosts[ taskID ];
		delete worker._callbacks[ taskID ];
		delete worker._taskCosts[ taskID ];

	}

	debug() {

		console.log( 'Task load: ', this.workerPool.map( ( worker ) => worker._taskLoad ) );

	}

	dispose() {

		for ( let i = 0; i < this.workerPool.length; ++ i ) {

			this.workerPool[ i ].terminate();

		}

		this.workerPool.length = 0;

		return this;

	}

}

/* WEB WORKER */

function DRACOWorker() {

	let decoderConfig;
	let decoderPending;

	onmessage = function ( e ) {

		const message = e.data;

		switch ( message.type ) {

			case 'init':
				decoderConfig = message.decoderConfig;
				decoderPending = new Promise( function ( resolve/*, reject*/ ) {

					decoderConfig.onModuleLoaded = function ( draco ) {

						// Module is Promise-like. Wrap before resolving to avoid loop.
						resolve( { draco: draco } );

					};

					DracoDecoderModule( decoderConfig ); // eslint-disable-line no-undef

				} );
				break;

			case 'decode':
				const buffer = message.buffer;
				const taskConfig = message.taskConfig;
				decoderPending.then( ( module ) => {

					const draco = module.draco;
					const decoder = new draco.Decoder();
					const decoderBuffer = new draco.DecoderBuffer();
					decoderBuffer.Init( new Int8Array( buffer ), buffer.byteLength );

					try {

						const geometry = decodeGeometry( draco, decoder, decoderBuffer, taskConfig );

						const buffers = geometry.attributes.map( ( attr ) => attr.array.buffer );

						if ( geometry.index ) buffers.push( geometry.index.array.buffer );

						self.postMessage( { type: 'decode', id: message.id, geometry }, buffers );

					} catch ( error ) {

						console.error( error );

						self.postMessage( { type: 'error', id: message.id, error: error.message } );

					} finally {

						draco.destroy( decoderBuffer );
						draco.destroy( decoder );

					}

				} );
				break;

		}

	};

	function decodeGeometry( draco, decoder, decoderBuffer, taskConfig ) {

		const attributeIDs = taskConfig.attributeIDs;
		const attributeTypes = taskConfig.attributeTypes;

		let dracoGeometry;
		let decodingStatus;

		const geometryType = decoder.GetEncodedGeometryType( decoderBuffer );

		if ( geometryType === draco.TRIANGULAR_MESH ) {

			dracoGeometry = new draco.Mesh();
			decodingStatus = decoder.DecodeBufferToMesh( decoderBuffer, dracoGeometry );

		} else if ( geometryType === draco.POINT_CLOUD ) {

			dracoGeometry = new draco.PointCloud();
			decodingStatus = decoder.DecodeBufferToPointCloud( decoderBuffer, dracoGeometry );

		} else {

			throw new Error( 'THREE.DRACOLoader: Unexpected geometry type.' );

		}

		if ( ! decodingStatus.ok() || dracoGeometry.ptr === 0 ) {

			throw new Error( 'THREE.DRACOLoader: Decoding failed: ' + decodingStatus.error_msg() );

		}

		const geometry = { index: null, attributes: [] };

		// Gather all vertex attributes.
		for ( const attributeName in attributeIDs ) {

			const attributeType = self[ attributeTypes[ attributeName ] ];

			let attribute;
			let attributeID;

			// A Draco file may be created with default vertex attributes, whose attribute IDs
			// are mapped 1:1 from their semantic name (POSITION, NORMAL, ...). Alternatively,
			// a Draco file may contain a custom set of attributes, identified by known unique
			// IDs. glTF files always do the latter, and `.drc` files typically do the former.
			if ( taskConfig.useUniqueIDs ) {

				attributeID = attributeIDs[ attributeName ];
				attribute = decoder.GetAttributeByUniqueId( dracoGeometry, attributeID );

			} else {

				attributeID = decoder.GetAttributeId( dracoGeometry, draco[ attributeIDs[ attributeName ] ] );

				if ( attributeID === - 1 ) continue;

				attribute = decoder.GetAttribute( dracoGeometry, attributeID );

			}

			geometry.attributes.push( decodeAttribute( draco, decoder, dracoGeometry, attributeName, attributeType, attribute ) );

		}

		// Add index.
		if ( geometryType === draco.TRIANGULAR_MESH ) {

			geometry.index = decodeIndex( draco, decoder, dracoGeometry );

		}

		draco.destroy( dracoGeometry );

		return geometry;

	}

	function decodeIndex( draco, decoder, dracoGeometry ) {

		const numFaces = dracoGeometry.num_faces();
		const numIndices = numFaces * 3;
		const byteLength = numIndices * 4;

		const ptr = draco._malloc( byteLength );
		decoder.GetTrianglesUInt32Array( dracoGeometry, byteLength, ptr );
		const index = new Uint32Array( draco.HEAPF32.buffer, ptr, numIndices ).slice();
		draco._free( ptr );

		return { array: index, itemSize: 1 };

	}

	function decodeAttribute( draco, decoder, dracoGeometry, attributeName, attributeType, attribute ) {

		const numComponents = attribute.num_components();
		const numPoints = dracoGeometry.num_points();
		const numValues = numPoints * numComponents;
		const byteLength = numValues * attributeType.BYTES_PER_ELEMENT;
		const dataType = getDracoDataType( draco, attributeType );

		const ptr = draco._malloc( byteLength );
		decoder.GetAttributeDataArrayForAllPoints( dracoGeometry, attribute, dataType, byteLength, ptr );
		const array = new attributeType( draco.HEAPF32.buffer, ptr, numValues ).slice();
		draco._free( ptr );

		return {
			name: attributeName,
			array: array,
			itemSize: numComponents
		};

	}

	function getDracoDataType( draco, attributeType ) {

		switch ( attributeType ) {

			case Float32Array: return draco.DT_FLOAT32;
			case Int8Array: return draco.DT_INT8;
			case Int16Array: return draco.DT_INT16;
			case Int32Array: return draco.DT_INT32;
			case Uint8Array: return draco.DT_UINT8;
			case Uint16Array: return draco.DT_UINT16;
			case Uint32Array: return draco.DT_UINT32;

		}

	}

}

/**
 * <AUTHOR> / https://github.com/deepkolos
 */

class WorkerPool {

	constructor( pool = 4 ) {

		this.pool = pool;
		this.queue = [];
		this.workers = [];
		this.workersResolve = [];
		this.workerStatus = 0;

	}

	_initWorker( workerId ) {

		if ( ! this.workers[ workerId ] ) {

			const worker = this.workerCreator();
			worker.addEventListener( 'message', this._onMessage.bind( this, workerId ) );
			this.workers[ workerId ] = worker;

		}

	}

	_getIdleWorker() {

		for ( let i = 0; i < this.pool; i ++ )
			if ( ! ( this.workerStatus & ( 1 << i ) ) ) return i;

		return - 1;

	}

	_onMessage( workerId, msg ) {

		const resolve = this.workersResolve[ workerId ];
		resolve && resolve( msg );

		if ( this.queue.length ) {

			const { resolve, msg, transfer } = this.queue.shift();
			this.workersResolve[ workerId ] = resolve;
			this.workers[ workerId ].postMessage( msg, transfer );

		} else {

			this.workerStatus ^= 1 << workerId;

		}

	}

	setWorkerCreator( workerCreator ) {

		this.workerCreator = workerCreator;

	}

	setWorkerLimit( pool ) {

		this.pool = pool;

	}

	postMessage( msg, transfer ) {

		return new Promise( ( resolve ) => {

			const workerId = this._getIdleWorker();

			if ( workerId !== - 1 ) {

				this._initWorker( workerId );
				this.workerStatus |= 1 << workerId;
				this.workersResolve[ workerId ] = resolve;
				this.workers[ workerId ].postMessage( msg, transfer );

			} else {

				this.queue.push( { resolve, msg, transfer } );

			}

		} );

	}

	dispose() {

		this.workers.forEach( ( worker ) => worker.terminate() );
		this.workersResolve.length = 0;
		this.workers.length = 0;
		this.queue.length = 0;
		this.workerStatus = 0;

	}

}

/**
 * Loader for KTX 2.0 GPU Texture containers.
 *
 * KTX 2.0 is a container format for various GPU texture formats. The loader
 * supports Basis Universal GPU textures, which can be quickly transcoded to
 * a wide variety of GPU texture compression formats. While KTX 2.0 also allows
 * other hardware-specific formats, this loader does not yet parse them.
 *
 * References:
 * - KTX: http://github.khronos.org/KTX-Specification/
 * - DFD: https://www.khronos.org/registry/DataFormat/specs/1.3/dataformat.1.3.html#basicdescriptor
 */

const KTX2TransferSRGB = 2;
const KTX2_ALPHA_PREMULTIPLIED = 1;
const _taskCache = new WeakMap();

let _activeLoaders = 0;

class KTX2Loader extends Loader {

	constructor( manager ) {

		super( manager );

		this.transcoderPath = '';
		this.transcoderBinary = null;
		this.transcoderPending = null;

		this.workerPool = new WorkerPool();
		this.workerSourceURL = '';
		this.workerConfig = null;

		if ( typeof MSC_TRANSCODER !== 'undefined' ) {

			console.warn(

				'THREE.KTX2Loader: Please update to latest "basis_transcoder".'
				+ ' "msc_basis_transcoder" is no longer supported in three.js r125+.'

			);

		}

	}

	setTranscoderPath( path ) {

		this.transcoderPath = path;

		return this;

	}

	setWorkerLimit( num ) {

		this.workerPool.setWorkerLimit( num );

		return this;

	}

	detectSupport( renderer ) {

		this.workerConfig = {
			astcSupported: renderer.extensions.has( 'WEBGL_compressed_texture_astc' ),
			etc1Supported: renderer.extensions.has( 'WEBGL_compressed_texture_etc1' ),
			etc2Supported: renderer.extensions.has( 'WEBGL_compressed_texture_etc' ),
			dxtSupported: renderer.extensions.has( 'WEBGL_compressed_texture_s3tc' ),
			bptcSupported: renderer.extensions.has( 'EXT_texture_compression_bptc' ),
			pvrtcSupported: renderer.extensions.has( 'WEBGL_compressed_texture_pvrtc' )
				|| renderer.extensions.has( 'WEBKIT_WEBGL_compressed_texture_pvrtc' )
		};


		if ( renderer.capabilities.isWebGL2 ) {

			// https://github.com/mrdoob/three.js/pull/22928
			this.workerConfig.etc1Supported = false;

		}

		return this;

	}

	dispose() {

		this.workerPool.dispose();
		if ( this.workerSourceURL ) URL.revokeObjectURL( this.workerSourceURL );

		return this;

	}

	init() {

		if ( ! this.transcoderPending ) {

			// Load transcoder wrapper.
			const jsLoader = new FileLoader( this.manager );
			jsLoader.setPath( this.transcoderPath );
			jsLoader.setWithCredentials( this.withCredentials );
			const jsContent = jsLoader.loadAsync( 'basis_transcoder.js' );

			// Load transcoder WASM binary.
			const binaryLoader = new FileLoader( this.manager );
			binaryLoader.setPath( this.transcoderPath );
			binaryLoader.setResponseType( 'arraybuffer' );
			binaryLoader.setWithCredentials( this.withCredentials );
			const binaryContent = binaryLoader.loadAsync( 'basis_transcoder.wasm' );

			this.transcoderPending = Promise.all( [ jsContent, binaryContent ] )
				.then( ( [ jsContent, binaryContent ] ) => {

					const fn = KTX2Loader.BasisWorker.toString();

					const body = [
						'/* constants */',
						'let _EngineFormat = ' + JSON.stringify( KTX2Loader.EngineFormat ),
						'let _TranscoderFormat = ' + JSON.stringify( KTX2Loader.TranscoderFormat ),
						'let _BasisFormat = ' + JSON.stringify( KTX2Loader.BasisFormat ),
						'/* basis_transcoder.js */',
						jsContent,
						'/* worker */',
						fn.substring( fn.indexOf( '{' ) + 1, fn.lastIndexOf( '}' ) )
					].join( '\n' );

					this.workerSourceURL = URL.createObjectURL( new Blob( [ body ] ) );
					this.transcoderBinary = binaryContent;

					this.workerPool.setWorkerCreator( () => {

						const worker = new Worker( this.workerSourceURL );
						const transcoderBinary = this.transcoderBinary.slice( 0 );

						worker.postMessage( { type: 'init', config: this.workerConfig, transcoderBinary }, [ transcoderBinary ] );

						return worker;

					} );

				} );

			if ( _activeLoaders > 0 ) {

				// Each instance loads a transcoder and allocates workers, increasing network and memory cost.

				console.warn(

					'THREE.KTX2Loader: Multiple active KTX2 loaders may cause performance issues.'
					+ ' Use a single KTX2Loader instance, or call .dispose() on old instances.'

				);

			}

			_activeLoaders ++;

		}

		return this.transcoderPending;

	}

	load( url, onLoad, onProgress, onError ) {

		if ( this.workerConfig === null ) {

			throw new Error( 'THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.' );

		}

		const loader = new FileLoader( this.manager );

		loader.setResponseType( 'arraybuffer' );
		loader.setWithCredentials( this.withCredentials );

		const texture = new CompressedTexture();

		loader.load( url, ( buffer ) => {

			// Check for an existing task using this buffer. A transferred buffer cannot be transferred
			// again from this thread.
			if ( _taskCache.has( buffer ) ) {

				const cachedTask = _taskCache.get( buffer );

				return cachedTask.promise.then( onLoad ).catch( onError );

			}

			this._createTexture( [ buffer ] )
				.then( function ( _texture ) {

					texture.copy( _texture );
					texture.needsUpdate = true;

					if ( onLoad ) onLoad( texture );

				} )
				.catch( onError );

		}, onProgress, onError );

		return texture;

	}

	_createTextureFrom( transcodeResult ) {

		const { mipmaps, width, height, format, type, error, dfdTransferFn, dfdFlags } = transcodeResult;

		if ( type === 'error' ) return Promise.reject( error );

		const texture = new CompressedTexture( mipmaps, width, height, format, UnsignedByteType );
		texture.minFilter = mipmaps.length === 1 ? LinearFilter : LinearMipmapLinearFilter;
		texture.magFilter = LinearFilter;
		texture.generateMipmaps = false;
		texture.needsUpdate = true;
		texture.encoding = dfdTransferFn === KTX2TransferSRGB ? sRGBEncoding : LinearEncoding$1;
		texture.premultiplyAlpha = !! ( dfdFlags & KTX2_ALPHA_PREMULTIPLIED );

		return texture;

	}

	/**
	 * @param {ArrayBuffer[]} buffers
	 * @param {object?} config
	 * @return {Promise<CompressedTexture>}
	 */
	_createTexture( buffers, config = {} ) {

		const taskConfig = config;
		const texturePending = this.init().then( () => {

			return this.workerPool.postMessage( { type: 'transcode', buffers, taskConfig: taskConfig }, buffers );

		} ).then( ( e ) => this._createTextureFrom( e.data ) );

		// Cache the task result.
		_taskCache.set( buffers[ 0 ], { promise: texturePending } );

		return texturePending;

	}

	dispose() {

		URL.revokeObjectURL( this.workerSourceURL );
		this.workerPool.dispose();

		_activeLoaders --;

		return this;

	}

}


/* CONSTANTS */

KTX2Loader.BasisFormat = {
	ETC1S: 0,
	UASTC_4x4: 1,
};

KTX2Loader.TranscoderFormat = {
	ETC1: 0,
	ETC2: 1,
	BC1: 2,
	BC3: 3,
	BC4: 4,
	BC5: 5,
	BC7_M6_OPAQUE_ONLY: 6,
	BC7_M5: 7,
	PVRTC1_4_RGB: 8,
	PVRTC1_4_RGBA: 9,
	ASTC_4x4: 10,
	ATC_RGB: 11,
	ATC_RGBA_INTERPOLATED_ALPHA: 12,
	RGBA32: 13,
	RGB565: 14,
	BGR565: 15,
	RGBA4444: 16,
};

KTX2Loader.EngineFormat = {
	RGBAFormat: RGBAFormat,
	RGBA_ASTC_4x4_Format: RGBA_ASTC_4x4_Format,
	RGBA_BPTC_Format: RGBA_BPTC_Format,
	RGBA_ETC2_EAC_Format: RGBA_ETC2_EAC_Format,
	RGBA_PVRTC_4BPPV1_Format: RGBA_PVRTC_4BPPV1_Format,
	RGBA_S3TC_DXT5_Format: RGBA_S3TC_DXT5_Format,
	RGB_ETC1_Format: RGB_ETC1_Format,
	RGB_ETC2_Format: RGB_ETC2_Format,
	RGB_PVRTC_4BPPV1_Format: RGB_PVRTC_4BPPV1_Format,
	RGB_S3TC_DXT1_Format: RGB_S3TC_DXT1_Format,
};


/* WEB WORKER */

KTX2Loader.BasisWorker = function () {

	let config;
	let transcoderPending;
	let BasisModule;

	const EngineFormat = _EngineFormat; // eslint-disable-line no-undef
	const TranscoderFormat = _TranscoderFormat; // eslint-disable-line no-undef
	const BasisFormat = _BasisFormat; // eslint-disable-line no-undef

	self.addEventListener( 'message', function ( e ) {

		const message = e.data;

		switch ( message.type ) {

			case 'init':
				config = message.config;
				init( message.transcoderBinary );
				break;

			case 'transcode':
				transcoderPending.then( () => {

					try {

						const { width, height, hasAlpha, mipmaps, format, dfdTransferFn, dfdFlags } = transcode( message.buffers[ 0 ] );

						const buffers = [];

						for ( let i = 0; i < mipmaps.length; ++ i ) {

							buffers.push( mipmaps[ i ].data.buffer );

						}

						self.postMessage( { type: 'transcode', id: message.id, width, height, hasAlpha, mipmaps, format, dfdTransferFn, dfdFlags }, buffers );

					} catch ( error ) {

						console.error( error );

						self.postMessage( { type: 'error', id: message.id, error: error.message } );

					}

				} );
				break;

		}

	} );

	function init( wasmBinary ) {

		transcoderPending = new Promise( ( resolve ) => {

			BasisModule = { wasmBinary, onRuntimeInitialized: resolve };
			BASIS( BasisModule ); // eslint-disable-line no-undef

		} ).then( () => {

			BasisModule.initializeBasis();

			if ( BasisModule.KTX2File === undefined ) {

				console.warn( 'THREE.KTX2Loader: Please update Basis Universal transcoder.' );

			}

		} );

	}

	function transcode( buffer ) {

		const ktx2File = new BasisModule.KTX2File( new Uint8Array( buffer ) );

		function cleanup() {

			ktx2File.close();
			ktx2File.delete();

		}

		if ( ! ktx2File.isValid() ) {

			cleanup();
			throw new Error( 'THREE.KTX2Loader:	Invalid or unsupported .ktx2 file' );

		}

		const basisFormat = ktx2File.isUASTC() ? BasisFormat.UASTC_4x4 : BasisFormat.ETC1S;
		const width = ktx2File.getWidth();
		const height = ktx2File.getHeight();
		const levels = ktx2File.getLevels();
		const hasAlpha = ktx2File.getHasAlpha();
		const dfdTransferFn = ktx2File.getDFDTransferFunc();
		const dfdFlags = ktx2File.getDFDFlags();

		const { transcoderFormat, engineFormat } = getTranscoderFormat( basisFormat, width, height, hasAlpha );

		if ( ! width || ! height || ! levels ) {

			cleanup();
			throw new Error( 'THREE.KTX2Loader:	Invalid texture' );

		}

		if ( ! ktx2File.startTranscoding() ) {

			cleanup();
			throw new Error( 'THREE.KTX2Loader: .startTranscoding failed' );

		}

		const mipmaps = [];

		for ( let mip = 0; mip < levels; mip ++ ) {

			const levelInfo = ktx2File.getImageLevelInfo( mip, 0, 0 );
			const mipWidth = levelInfo.origWidth;
			const mipHeight = levelInfo.origHeight;
			const dst = new Uint8Array( ktx2File.getImageTranscodedSizeInBytes( mip, 0, 0, transcoderFormat ) );

			const status = ktx2File.transcodeImage(
				dst,
				mip,
				0,
				0,
				transcoderFormat,
				0,
				- 1,
				- 1,
			);

			if ( ! status ) {

				cleanup();
				throw new Error( 'THREE.KTX2Loader: .transcodeImage failed.' );

			}

			mipmaps.push( { data: dst, width: mipWidth, height: mipHeight } );

		}

		cleanup();

		return { width, height, hasAlpha, mipmaps, format: engineFormat, dfdTransferFn, dfdFlags };

	}

	//

	// Optimal choice of a transcoder target format depends on the Basis format (ETC1S or UASTC),
	// device capabilities, and texture dimensions. The list below ranks the formats separately
	// for ETC1S and UASTC.
	//
	// In some cases, transcoding UASTC to RGBA32 might be preferred for higher quality (at
	// significant memory cost) compared to ETC1/2, BC1/3, and PVRTC. The transcoder currently
	// chooses RGBA32 only as a last resort and does not expose that option to the caller.
	const FORMAT_OPTIONS = [
		{
			if: 'astcSupported',
			basisFormat: [ BasisFormat.UASTC_4x4 ],
			transcoderFormat: [ TranscoderFormat.ASTC_4x4, TranscoderFormat.ASTC_4x4 ],
			engineFormat: [ EngineFormat.RGBA_ASTC_4x4_Format, EngineFormat.RGBA_ASTC_4x4_Format ],
			priorityETC1S: Infinity,
			priorityUASTC: 1,
			needsPowerOfTwo: false,
		},
		{
			if: 'bptcSupported',
			basisFormat: [ BasisFormat.ETC1S, BasisFormat.UASTC_4x4 ],
			transcoderFormat: [ TranscoderFormat.BC7_M5, TranscoderFormat.BC7_M5 ],
			engineFormat: [ EngineFormat.RGBA_BPTC_Format, EngineFormat.RGBA_BPTC_Format ],
			priorityETC1S: 3,
			priorityUASTC: 2,
			needsPowerOfTwo: false,
		},
		{
			if: 'dxtSupported',
			basisFormat: [ BasisFormat.ETC1S, BasisFormat.UASTC_4x4 ],
			transcoderFormat: [ TranscoderFormat.BC1, TranscoderFormat.BC3 ],
			engineFormat: [ EngineFormat.RGB_S3TC_DXT1_Format, EngineFormat.RGBA_S3TC_DXT5_Format ],
			priorityETC1S: 4,
			priorityUASTC: 5,
			needsPowerOfTwo: false,
		},
		{
			if: 'etc2Supported',
			basisFormat: [ BasisFormat.ETC1S, BasisFormat.UASTC_4x4 ],
			transcoderFormat: [ TranscoderFormat.ETC1, TranscoderFormat.ETC2 ],
			engineFormat: [ EngineFormat.RGB_ETC2_Format, EngineFormat.RGBA_ETC2_EAC_Format ],
			priorityETC1S: 1,
			priorityUASTC: 3,
			needsPowerOfTwo: false,
		},
		{
			if: 'etc1Supported',
			basisFormat: [ BasisFormat.ETC1S, BasisFormat.UASTC_4x4 ],
			transcoderFormat: [ TranscoderFormat.ETC1 ],
			engineFormat: [ EngineFormat.RGB_ETC1_Format ],
			priorityETC1S: 2,
			priorityUASTC: 4,
			needsPowerOfTwo: false,
		},
		{
			if: 'pvrtcSupported',
			basisFormat: [ BasisFormat.ETC1S, BasisFormat.UASTC_4x4 ],
			transcoderFormat: [ TranscoderFormat.PVRTC1_4_RGB, TranscoderFormat.PVRTC1_4_RGBA ],
			engineFormat: [ EngineFormat.RGB_PVRTC_4BPPV1_Format, EngineFormat.RGBA_PVRTC_4BPPV1_Format ],
			priorityETC1S: 5,
			priorityUASTC: 6,
			needsPowerOfTwo: true,
		},
	];

	const ETC1S_OPTIONS = FORMAT_OPTIONS.sort( function ( a, b ) {

		return a.priorityETC1S - b.priorityETC1S;

	} );
	const UASTC_OPTIONS = FORMAT_OPTIONS.sort( function ( a, b ) {

		return a.priorityUASTC - b.priorityUASTC;

	} );

	function getTranscoderFormat( basisFormat, width, height, hasAlpha ) {

		let transcoderFormat;
		let engineFormat;

		const options = basisFormat === BasisFormat.ETC1S ? ETC1S_OPTIONS : UASTC_OPTIONS;

		for ( let i = 0; i < options.length; i ++ ) {

			const opt = options[ i ];

			if ( ! config[ opt.if ] ) continue;
			if ( ! opt.basisFormat.includes( basisFormat ) ) continue;
			if ( hasAlpha && opt.transcoderFormat.length < 2 ) continue;
			if ( opt.needsPowerOfTwo && ! ( isPowerOfTwo( width ) && isPowerOfTwo( height ) ) ) continue;

			transcoderFormat = opt.transcoderFormat[ hasAlpha ? 1 : 0 ];
			engineFormat = opt.engineFormat[ hasAlpha ? 1 : 0 ];

			return { transcoderFormat, engineFormat };

		}

		console.warn( 'THREE.KTX2Loader: No suitable compressed texture format found. Decoding to RGBA32.' );

		transcoderFormat = TranscoderFormat.RGBA32;
		engineFormat = EngineFormat.RGBAFormat;

		return { transcoderFormat, engineFormat };

	}

	function isPowerOfTwo( value ) {

		if ( value <= 2 ) return true;

		return ( value & ( value - 1 ) ) === 0 && value !== 0;

	}

};

const DoubleSide = 2;
const LinearEncoding = 3000;

var loaderService = null;
var loadingManager = null;
var basisLoader = null;
var dracoLoader = null;

const getLoaderService = function(renderer) {
  if (loaderService != null) return loaderService;
  return new Promise(resolve => {
    if (!dracoLoader) {
      dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath("/r_static/ios/vendor/draco/");
    }

    // Specify path to a folder containing WASM/JS decoding libraries.

    if (!basisLoader) {
      basisLoader = new KTX2Loader();
      basisLoader.setTranscoderPath("/r_static/ios/vendor/basisu/transcoder/");
      basisLoader.useAlpha = false;
      //	basisLoader.detectSupport(renderer);
    }
    if (!loadingManager) {
      loadingManager = new LoadingManager();
      loadingManager.addHandler(/\.basis$/, basisLoader);
    }

    loaderService = new GLTFLoader(loadingManager);
    loaderService.setDRACOLoader(dracoLoader);
    loaderService.setKTX2Loader(basisLoader);

    resolve(loaderService);
  });
};

const parseMaterialName = (currentMaterialName) => {
  let type, name;
  let materialNameSplit = currentMaterialName.split("-");
  if (materialNameSplit.length > 1) {
    type = materialNameSplit[0];
    name = materialNameSplit[1];
  }
  return { type, name };
};

const modifyMaterials = (node, env) => {
  if (node.isMesh) {
    let expectBaked = true;
    let { type: materialType, name: materialName } = parseMaterialName(node.material.name);

    switch (materialType) {
      case "live":
        expectBaked = false;
        switch (materialName) {
          case "lcd":
            node.material = createMaterial({
              color: 0x333333,
              envMap: env,
              roughness: 0.275,
              metalness: 0.1,
              clearcoat: 1
            });
            break;
          case "metal_dark_grey":
            node.material = createMaterial({
              color: 0x888888,
              envMap: env,
              roughness: 0.275,
              metalness: 0.1,
              clearcoat: 1
            });
            break;
          case "metal_brushed_matte":
            node.material = createMaterial({
              color: 0x888888,
              envMap: env,
              roughness: 0.275,
              metalness: 0.1,
              clearcoat: 1
            });
            break;
        }
        break;
      case "custom":
        node.material.envMap = env;
        node.material.needsUpdate = true;
        break;
      default:
        if (expectBaked && node.material && node.material.map) {
          node.material.map.encoding = LinearEncoding;
          node.material = new MeshBasicMaterial({ map: node.material.map });
          node.material.map.encoding = LinearEncoding;
        }
        break;
    }
  }
};

const createMaterial = settings => {
  var material = new MeshPhysicalMaterial({
    side: DoubleSide,
    ...settings
  });

  return material;
};

class GLTFModel {
  constructor(url, target, env) {
    return new Promise(async done => {
      let loader = await getLoaderService();
      loader.load(url, modelInThreeJS => {
        modelInThreeJS.scene.traverse(node => modifyMaterials(node, env));
        // const box = new BoxHelper( modelInThreeJS.scene, 0xffff00 );
        // target.add(box);
        target.add(modelInThreeJS.scene);
        done(modelInThreeJS.scene);
      });
    });
  }
}

const createGLTFMesh = async (id, itemContainer, env) => {
  return new GLTFModel(`/r_static/ios/web/${id}.glb`, itemContainer, env);
};

const cacheContainers = [];
const cacheModels = [];

const preloadAllModelsForSet = envMap => {
  return new Promise(done => {
    let jobs = itemsRepositoryRelease.items.map(item => {
      let id = item.item_identity.item_id;
      let [, promise] = new ProgressiveItem().createItem(
        id,
        {
          x: 0,
          y: 0,
          z: 0,
        },
        null,
        envMap
      );

      return promise;
    });
    Promise.all(jobs).then(done);
  });
};

class ProgressiveItem {
  constructor(assetDescription) {}

  createItem(id, position, size, env) {
    let itemContainer, model;

    if (!cacheContainers[id]) {
      itemContainer = new Group();
      model = createGLTFMesh(id, itemContainer, env);
      cacheModels[id] = model;
      cacheContainers[id] = itemContainer;
    } else {
      itemContainer = cacheContainers[id];
      model = cacheModels[id];
    }

    itemContainer.position.z = position.z;
    itemContainer.position.x = position.x;
    itemContainer.position.y = position.y;

    return [itemContainer, model];
  }
}

var LocalItemOrientation;
(function (LocalItemOrientation) {
    LocalItemOrientation["Center"] = "center";
    LocalItemOrientation["GravityLeft"] = "gravityLeft";
    LocalItemOrientation["GravityRight"] = "gravityRight";
    LocalItemOrientation["GravityFront"] = "gravityFront";
    LocalItemOrientation["GravityBack"] = "gravityBack";
    LocalItemOrientation["Explicit"] = "explicit";
    LocalItemOrientation["Special"] = "special";
    LocalItemOrientation["Random"] = "random";
})(LocalItemOrientation || (LocalItemOrientation = {}));
var slotOrientation;
(function (slotOrientation) {
    slotOrientation["FreeStanding"] = "freeStanding";
    slotOrientation["LeaningLeft"] = "leaningLeft";
    slotOrientation["LeaningRight"] = "leaningRight";
    slotOrientation["LeaningBack"] = "leaningBack";
})(slotOrientation || (slotOrientation = {}));
var Axis;
(function (Axis) {
    Axis["X"] = "x";
    Axis["Y"] = "y";
    Axis["Z"] = "z";
})(Axis || (Axis = {}));

var offset = {
	left: 4,
	right: 4,
	front: 6,
	back: 1
};
var margin = {
	offset: offset
};

const calculateXPosition = (positionalData) => {
    if (positionalData.itemOrientation.x.mode === LocalItemOrientation.Center) {
        return positionalData.defaultPosition.x;
    }
    else if (positionalData.itemOrientation.x.mode === LocalItemOrientation.GravityLeft) {
        if (positionalData.allowedOrientationsInSlot.includes(slotOrientation.LeaningLeft)) {
            return calculateMaxLeftPosition(positionalData);
        }
        else {
            return positionalData.defaultPosition.x;
        }
    }
    else if (positionalData.itemOrientation.x.mode === LocalItemOrientation.GravityRight) {
        if (positionalData.allowedOrientationsInSlot.includes(slotOrientation.LeaningRight)) {
            return calculateMaxRightPosition(positionalData);
        }
        else {
            return positionalData.defaultPosition.x;
        }
    }
    else if (positionalData.itemOrientation.x.mode === LocalItemOrientation.Random) {
        return calculateRandomPosition(positionalData, Axis$1.X);
    }
    else if (positionalData.itemOrientation.x.mode === LocalItemOrientation.Explicit) {
        return calculateExplicitPosition(positionalData, Axis$1.X);
    }
    else {
        return positionalData.defaultPosition.x;
    }
};
const calculateZPosition = (positionalData) => {
    if (positionalData.itemOrientation.z.mode === LocalItemOrientation.Center) {
        return positionalData.defaultPosition.z;
    }
    else if (positionalData.itemOrientation.z.mode === LocalItemOrientation.GravityFront) {
        return calculateMaxFrontPosition(positionalData);
    }
    else if (positionalData.itemOrientation.z.mode === LocalItemOrientation.GravityBack) {
        return calculateMaxBackPosition(positionalData);
    }
    else if (positionalData.itemOrientation.z.mode === LocalItemOrientation.Random) {
        return calculateRandomPosition(positionalData, Axis$1.Z);
    }
    else if (positionalData.itemOrientation.z.mode === LocalItemOrientation.Explicit) {
        return calculateExplicitPosition(positionalData, Axis$1.Z);
    }
    else {
        return positionalData.defaultPosition.z;
    }
};
const calculateMaxLeftPosition = (data) => {
    const offset = margin.offset.left;
    const maxLeft = data.defaultPosition.x - data.slotDim.width / 2 + data.itemDim.width / 2 + offset;
    return roundResult$1(maxLeft);
};
const calculateMaxRightPosition = (data) => {
    const offset = margin.offset.right;
    const maxRight = data.defaultPosition.x + data.slotDim.width / 2 - data.itemDim.width / 2 - offset;
    return roundResult$1(maxRight);
};
const calculateMaxFrontPosition = (data) => {
    const offset = margin.offset.front;
    const maxRight = data.defaultPosition.z + data.slotDim.depth / 2 - data.itemDim.depth / 2 - offset;
    return roundResult$1(maxRight);
};
const calculateMaxBackPosition = (data) => {
    const offset = margin.offset.back;
    const maxRight = data.defaultPosition.z - data.slotDim.depth / 2 + data.itemDim.depth / 2 + offset;
    return roundResult$1(maxRight);
};
const calculateRandomPosition = (data, axis) => {
    if (axis === Axis$1.X) {
        const minX = calculateMaxLeftPosition(data);
        const maxX = calculateMaxRightPosition(data);
        return getRandomIntInclusive(minX, maxX);
    }
    if (axis === Axis$1.Z) {
        const minZ = calculateMaxBackPosition(data);
        const maxZ = calculateMaxFrontPosition(data);
        return getRandomIntInclusive(minZ, maxZ);
    }
};
const calculateExplicitPosition = (data, axis) => {
    if (axis === Axis$1.X) {
        const percent = data.itemOrientation.x.percent;
        const minX = calculateMaxLeftPosition(data);
        const maxX = calculateMaxRightPosition(data);
        return calculateProportionalValue(minX, maxX, percent);
    }
    if (axis === Axis$1.Z) {
        const percent = data.itemOrientation.z.percent;
        const minZ = calculateMaxBackPosition(data);
        const maxZ = calculateMaxFrontPosition(data);
        return calculateProportionalValue(minZ, maxZ, percent);
    }
};
const getRandomIntInclusive = (minVal, maxVal) => {
    const min = Math.ceil(minVal);
    const max = Math.floor(maxVal);
    return Math.floor(Math.random() * (max - min + 1)) + min;
};
const calculateProportionalValue = (valMin, valMax, percent) => {
    return Math.round((valMin + Math.abs(valMax - valMin) * (percent / 100)));
};

const extractPositionalData = (portal, item) => {
    return {
        defaultPosition: getInitialPosition(portal),
        slotDim: getSlotDimensions(portal),
        itemDim: getItemDimensions(item),
        allowedOrientationsInSlot: portal.slot.itemsOrientations,
        itemOrientation: portal.item.localOrientation
    };
};
const getInitialPosition = (portal) => {
    const defaultX = portal.item.position.default.x;
    const defaultY = portal.item.position.default.y;
    const defaultZ = portal.item.position.default.z;
    return {
        x: defaultX,
        y: defaultY,
        z: defaultZ
    };
};
const getSlotDimensions = (portal) => {
    const slotWidth = portal.slot.width;
    const slotHeight = portal.slot.height;
    const slotDepth = portal.slot.depth;
    return {
        width: slotWidth,
        height: slotHeight,
        depth: slotDepth
    };
};
const getItemDimensions = (item) => {
    const itemWidth = item.item_space.dimensions_mm.widthx;
    const itemHeight = item.item_space.dimensions_mm.heighty;
    const itemDepth = item.item_space.dimensions_mm.depthz;
    return {
        width: itemWidth,
        height: itemHeight,
        depth: itemDepth
    };
};

const calculateItemPosition = (portal, item) => {
    const positionalData = extractPositionalData(portal, item);
    if (portal.area === ItemsArea.ShelfTop) {
        const finalPosition = {
            x: positionalData.defaultPosition.x,
            y: positionalData.defaultPosition.y,
            z: calculateZPosition(positionalData)
        };
        portal.item.position.final = finalPosition;
        return finalPosition;
    }
    if (portal.area === ItemsArea.ShelfBody) {
        const finalPosition = {
            x: calculateXPosition(positionalData),
            y: positionalData.defaultPosition.y,
            z: calculateZPosition(positionalData)
        };
        portal.item.position.final = finalPosition;
        return finalPosition;
    }
};

class ItemsPlacer {
	constructor() { }

	update(portals, picker, env) {
		this.flushItems(this._group);

		portals.forEach(portal => {
			let matches = picker.find(portal);
			// note for future: here run some heuristic method to pick the best item from all found
			let foundItem = matches[0];
			let namedId = foundItem.item_identity.item_id;

			let finalPosition = calculateItemPosition(portal, foundItem);

			let { x, y, z } = finalPosition;

			let position = new Vector3(x, y, z);
			let item = new ProgressiveItem();
			let [itemObject3D, promised] = item.createItem(namedId, position, portal.item.size, env);

			this._group.add(itemObject3D);
		});
	}

	flushItems(group) {
		let currentGroup = group ? group : this._group;
		let len = currentGroup.children.length - 1;
		for (let i = len; i >= 0; i--) {
			let obj = currentGroup.children[i];
			currentGroup.remove(obj);
		}
	}

	getRootGroup() {
		return this._group ? this._group : false;
	}

	setRootGroup(group) {
		this._group = group;
	}
}

const placerService = new ItemsPlacer();

const ios = {
    extractSlots,
    selectStrategy,
    executeStrategy,
    StrategyModes,
    Spaces,
};
const executeInRenderingLoop = (jetty) => {
    // strategy settings --> space and strategy as external parameters
    let currentGeometrySlotFormat = extractSlots(jetty);
    let strategy = selectStrategy(currentGeometrySlotFormat, strategySpecification);
    return executeStrategy(strategy, currentGeometrySlotFormat);
};
const placeItems = (group, jetty, envMap) => {
    placerService.setRootGroup(group);
    placerService.update(executeInRenderingLoop(jetty), pickerService, envMap);
};
const changeItemsVisibility = (state) => {
    let group = placerService.getRootGroup();
    if (group !== false) {
        group.visible = state;
    }
};
const hideItems = () => changeItemsVisibility(false);
const showItems = () => changeItemsVisibility(true);
const toggleItems = () => {
    let group = placerService.getRootGroup();
    let state = group ? group.visible : false;
    changeItemsVisibility(!state);
};

export { executeInRenderingLoop, hideItems, ios, placeItems, preloadAllModelsForSet, showItems, toggleItems };
