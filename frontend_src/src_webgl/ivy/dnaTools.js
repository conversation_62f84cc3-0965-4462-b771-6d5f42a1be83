import density_mapping_type01 from './mapping_type01.json';
import density_mapping_type02 from './mapping_type02.json';
import { processBestShelfSplit } from './shelf-splitter.js'
 
function range(start, stop, step) {
        if (typeof stop == 'undefined') {
            // one param defined
            stop = start;
            start = 0;
        }

        if (typeof step == 'undefined') {
            step = 1;
        }

        if ((step > 0 && start >= stop) || (step < 0 && start <= stop)) {
            return [];
        }

        var result = [];
        for (var i = start; step > 0 ? i < stop : i > stop; i += step) {
            result.push(i);
        }
        return result;
}

function division_by_substraction(dividend, divisor){
        let start = parseInt(dividend / divisor) - 2;
        let result = start
        let reminder = dividend - divisor * result;
        //# Dzielenie bez reszty
        while (reminder >= divisor) {
            reminder -= divisor
            result += 1
            if (result - start == 10) {
                throw("blad");
            }
        }
        //# Zaokraglenie wyniku
        if (reminder * 2 >= divisor) {
            result += 1;
        }
        return result
    }

class Rows{
  constructor(ilosc_rzedow, wysokosci_rzedow) {

    this.layout = null;
    this.fill_a = null;
    this.fill_b = null;
    this.amount = parseInt(ilosc_rzedow);
    this.heights = wysokosci_rzedow.map(x=>parseInt(x));
    this.bottom = [0];
    this.top = [0];

    let _wys = 0;
    range(this.amount).map(x=>{
        this.bottom.push(_wys);
        _wys += parseInt(this.heights[x]);
        this.top.push(_wys);
    });
  }

}

class MultitypeObject{
    constructor(pos, row, lock, hori, vert, supp, doorL, doorR, doorS, doorS_force, legs, support_size, pp, door_settings){
        this.x = pos;
        this.row = row;
        this.lock = lock == 1 ? true : false;
        this.horizontal = hori;
        this.vertical = vert;
        this.support = supp;
        this.doorL = doorL;
        this.doorR = doorR;
        this.legs = legs;
        this.doorS = [0, pos - support_size - pp, pos + support_size + pp, pos, pos + door_settings[8]][parseInt(doorS)];
        this.doorS_force = doorS_force == "1" ? true : false;
        this.door_settings = door_settings;

    }
    get_horizontal_points(plyty_pol){
        if (this.horizontal == 1){
            return [this.x]
        }
        else if(this.horizontal == 2){
            return [this.x, this.x - 2 * plyty_pol]
        }
        else if (this.horizontal == 3){
            return [this.x, this.x + 2 * plyty_pol]
        }
        else if (this.horizontal == 4){
            return [this.x - plyty_pol, this.x + plyty_pol]
        }
        else{
            return []
        }
    }
    get_door_object(){
        return new Fill(this.x, this.doorL, this.doorR, this.row);
    }

    get_verticals_geometry(shelf_rows, plyty_pol){
        return this.gen_verticals_geometry(this.x, [this.row], shelf_rows, plyty_pol,
                                           this.vertical == 2 ? true : false)
    }

    get_support_geometry(shelf_rows, plyty_pol, support_size) {
        let flip = [1, 3].indexOf(this.support) > -1 ? -1 : 1;
        return {
            "x1": this.x + (support_size + plyty_pol) * flip,
            "y1": shelf_rows.bottom[this.row] + plyty_pol,
            "z1": 0,
            "x2": this.x + plyty_pol * flip,
            "y2": shelf_rows.top[[3, 4].indexOf(this.support) > -1 && this.row < shelf_rows.amount ? this.row + 1 : this.row] - plyty_pol,
            "z2": 0
        }
    }

    get_legs_geometry(shelf_rows, depth){
        return this.gen_legs_geometry(this.x, shelf_rows, depth, this.row);
    }

    gen_verticals_geometry(pos_x, element_rows, shelf_rows, plyty_pol, double=false){
        return element_rows.filter(row=> row != 0).map(row =>
            {return {"x1": pos_x,
                 "y1": shelf_rows.bottom[row] + plyty_pol,
                 "x2": pos_x,
                 "y2": shelf_rows.top[row < shelf_rows.amount && double ? row + 1 : row] - plyty_pol}});
    }

    gen_horizontal_geometry(x1, x2, y, plyty_pol){
        return {"x1":x1-plyty_pol,
        'y1':y,
        'x2':x2+plyty_pol,
        'y2':y};
    }

    gen_legs_geometry(pos_x, shelf_rows, shelf_depth, element_row=0, y_pos=-20, z_pos=20){
      return [z_pos,shelf_depth-z_pos].map(z=>{
          return {"x1": pos_x,
                 "y1": shelf_rows.bottom[element_row] + y_pos,
                 "z1": z}
      });
    }
}

/*
const min_size = 260;
const max_size = 1000;
const gap = 2; // Tylko parzyste wartosci !!!
const thickness = 19;
*/
class Fill{

  constructor(pos, left, right, row) {
    this.x = pos;
    this.left = left;
    this.right = right;
    this.row = row;
  }

    get_fill_geometry(next_fill, section, force, shelf_rows, depth, plyty_pol, d_sett) {
        let space = Math.abs(next_fill.x - this.x) - 2 * plyty_pol;

        if (this.right === 0 || next_fill.left === 0 || !(d_sett[0] < space && space < d_sett[1])){
            return {};
        }

        let fill_style = this.right === 3 ? shelf_rows.fill_b[this.row] : shelf_rows.fill_a[this.row];
        let middle_point = (space < d_sett[2] + 2 * plyty_pol && !(space > d_sett[3] && force) ? null : (section != null ? section : this.x + plyty_pol + division_by_substraction(space,2)));
        let x1 = this.x + plyty_pol;
        let x2 = next_fill.x - plyty_pol;
        let y1 = shelf_rows.bottom[this.row] + plyty_pol;
        let y2 = shelf_rows.top[this.row] - plyty_pol;
        let z1 = depth;
        let z2 = depth - d_sett[6];

        let geom = {};
        if ([2].indexOf(fill_style) > -1) {
            geom['drawers'] = [{
                'x1': x1,
                'x2': x2,
                'y1': y1,
                'y2': y2,
                'z1': z1,
                'z2': z2,
                'type': 0,
                'flip': 0
            }];
        }
        if ([1].indexOf(fill_style) > -1 && middle_point == null) {
            geom['doors'] = [{
                'x1': x1,
                'x2': x2,
                'y1': y1,
                'y2': y2,
                'z1': z1,
                'z2': z2,
                'type': 0,
                'flip': [1, 3].indexOf(this.right) > -1 ? 0 : 1
            }];
        }

        if ([1].indexOf(fill_style) > -1 && middle_point != null) {
            geom['doors'] = [{
                'x1': x1,
                'x2': middle_point,
                'y1': y1,
                'y2': y2,
                'z1': z1,
                'z2': z2,
                'type': [1, 3].indexOf(this.right) > -1 ? 1 : 0,
                'flip': 1
            }, {
                'x1': middle_point,
                'x2': x2,
                'y1': y1,
                'y2': y2,
                'z1': z1,
                'z2': z2,
                'type': [1, 3].indexOf(this.right) === -1 ? 1 : 0,
                'flip': 0
            }];
        }
        if ([1, 2, 3].indexOf(fill_style) > -1) {
            geom['backs'] = [{
                'x1': x1,
                'x2': x2,
                'y1': y1,
                'y2': y2,
                'z1': d_sett[7],
                'z2': 0,
            }];
        }
        return geom;
    }
}


module.exports = {

    independent_support_cleaner: function(supports, backs){
        let backs_sorted = {};
        backs.map(b=>{
            let b_min = min(b["y1"], b["y2"]);
            if (object.keys(backs).indexOf(b_min) === -1){
                backs_sorted[b_min] = [];
                backs_sorted[b_min].push(b)
            }
        });

        let new_supports = [];
        let support_check = (s_x,b) => min(b["x1"], b["x2"]) <= x && x <= max(b["x1"], b["x2"])

        supports.map(s=>{
            let s_min = min(s["y1"], s["y2"]);
            let was_any = false;
            [s["x1"], s["x2"]].map(x=>{
                backs_sorted[s_min].map(b=>{
                   if (support_check(x, b)){
                       was_any=true;
                   }
                })
            });
            if (!was_any){
                new_supports.push(s);
            }
        });
        return new_supports;

    },
    get_presets_amount: function(data){
        return Math.max([(Object.keys(data).indexOf("style_list") > -1 ? data['style_list'] : []).length -1, 0]);
    },
    get_pld: function (list_, index_, default_=null) {
          return list_.length > index_ ? list_[index_] : (list_.length > 0 ? list_[list_.length-1] : default_);
    },
    get_rows_obj_with_styles: function(styles_list, input_data, styles_slider, row_amount, row_heights, dk_sett){
        let styles_def = (Object.keys(input_data).indexOf("style_list") > -1 ? input_data['style_list'] : [{}])[0];
        let default_value = this.range(row_amount).map(x=> 441);

        let shelf_rows = new Rows(row_amount, row_heights);
        if (styles_slider > -1){
            styles_list = this.get_pld(this.get_pld((Object.keys(input_data).indexOf("style_list") > -1 ? input_data['style_list'] : [{}]).slice(1), styles_slider, [default_value]), row_amount)
        }

        let proper_styles = [];
        range(shelf_rows.amount).map(row_index=>{
           proper_styles.push(this.convert_to_proper_style(this.get_pld(styles_list, row_index),
                                                     row_index,
                                                     shelf_rows.heights[row_index],
                                                     shelf_rows.top[row_index + 1],
                                                     styles_def, dk_sett));
        });

        shelf_rows.styles = proper_styles;
        shelf_rows.layout = [1, ...proper_styles.map(x=>parseInt(x) % 10)];
        shelf_rows.fill_a = [1, ...proper_styles.map(x=>(Math.floor(parseInt(x) / 10.0) % 10) || 1)];
        shelf_rows.fill_b = [1, ...proper_styles.map(x=>(Math.floor(parseInt(x) / 100.0)) || 1)];

        return shelf_rows;
    },
    convert_to_proper_style: function(style, row_index, row_height, row_top_coor, styles_def, dk_sett) {
        style = parseInt(style);
        let proper_doors = dk_sett['proper_door_heights'];
        let min_dr = dk_sett['drawer_max_position'];

        let dd_check = function (s, h = row_height, p = row_top_coor) {

            let s_check = function(s_, h_, p_) {
                if (s_ == 3){
                    return 3;
                }
                if (s_ == 2 && p_ <= min_dr){
                    return 2;
                }
                if (s_ == 1 && proper_doors.indexOf(h_) > -1 ){
                    return 1;
                }

              return 4;
            };
            return (s_check(((Math.floor(parseInt(s) / 100.0))), h, p) * 100 + s_check((Math.floor(parseInt(s) / 10.0) % 10), h, p) * 10 + s % 10); // float/int problem?
        };
        let proper_style = 441;

        let simple_none = this.get_pld(styles_def['11'], row_index, 441);
        style = parseInt(style);
        if ([1, 11].indexOf(style) > -1) {
            proper_style = simple_none;
        }
        else if ([2, 12].indexOf(style) > -1) {
            proper_style = proper_doors.indexOf(row_height) > -1 ? this.get_pld(styles_def['12'], row_index, 412) : simple_none;
        }
        else if ([3, 13].indexOf(style) > -1) {
            proper_style = proper_doors.indexOf(row_height) > -1 ? this.get_pld(styles_def['13'], row_index, 113) : simple_none;
        }
        else if (style === 21) {
            proper_style = row_top_coor <= min_dr ? this.get_pld(styles_def['21'], row_index, 242) : simple_none;
        }
        else if (style === 31) {
            proper_style = row_top_coor <= min_dr ? this.get_pld(styles_def['31'], row_index, 223) : simple_none;
        }
        else if (style === 22) {
            if (proper_doors.indexOf(row_height) > -1) {
                proper_style = row_top_coor <= min_dr ? this.get_pld(styles_def['22'], row_index, 213) : this.get_pld(styles_def['12'], row_index, 412);
            }
            else {
                proper_style = row_top_coor <= min_dr ? this.get_pld(styles_def['21'], row_index, 242) : simple_none;
            }
        }
        else if (style.toString().length === 3) {
            proper_style = style;
        }
        return dd_check(proper_style);
    },

    get_styles_preset: function (data, slider_pos, row_amount, row_heights_list, max_row_amount) {
        //Zwraca style dla aktualnych parametrow. Przyklad struktury danych.
        /*
         let data = [
         "00000000.00000000.00000000",  //Pozycja 0 slidera, 3 wersje zalezne od ilosci rzedow
         "00000000",  // Pozycja 1 slidera, jedna wersja niezaleznie od ilosci rzedow
         ];
         */
        if (!data || data.length == 0) {
            return this.range(row_amount+1).map(i => 1);
        }
        var current = data.length > slider_pos ? data[slider_pos] : data[data.length-1];
        current = current.length >= max_row_amount - row_amount ? current[max_row_amount - row_amount] : current[current.length-1];
        current = [1, ...current.map((x,index) => 0 < x < 4 && [278,398].indexOf(row_heights_list[index]) > -1 ? x : 1)];
        return current
    },

    is_inside_domain: function(parameter,list_of_domains){
        let it_is_inside = false;
        this.range(1,list_of_domains.length,2).forEach(index=>{
            if (list_of_domains[index-1] <= parameter && parameter <= list_of_domains[index] && it_is_inside == false){
                it_is_inside = true;
            }
        });
        return it_is_inside;
    },
    round_python: function(value){
        let x = Math.round(Math.abs(value));
        if (value < 0){
            return -1*x;
        } else {
            return x
        }
    },
    range: function(start, stop, step) {
        if (typeof stop == 'undefined') {
            // one param defined
            stop = start;
            start = 0;
        }

        if (typeof step == 'undefined') {
            step = 1;
        }

        if ((step > 0 && start >= stop) || (step < 0 && start <= stop)) {
            return [];
        }

        var result = [];
        for (var i = start; step > 0 ? i < stop : i > stop; i += step) {
            result.push(i);
        }
        return result;
    },
    // get onscreen coordinates of each handler

    generate_objects: function(pos, rows, obj_dict, width, row_styles, motion, support_size, plyty_pol, door_settings){
        let h_rows, v_rows, s_rows, dl_rows, dr_rows, l_rows, s_style, doorR, dl, dr, ds, df, ds_rows,drs;
        let dl_style = [];
        let dr_style = [];
        let ds_style = [];
        let drs_style = [];
        let drs_rows = [];
        let obj_def = obj_dict['E'].split('_')[2];
        let horizontal = parseInt(obj_def[2]);
        if (horizontal != 0){
            h_rows = Object.keys(obj_dict).indexOf("H") == -1 || (obj_dict['H'][2] === false || obj_dict['H'][2] == -1) ? rows : obj_dict['H'].slice(2);
            if (!(Object.keys(obj_dict).indexOf("H") == -1 || (obj_dict['H'][0] === false || obj_dict['H'][0] == -1) || (-width / 2 + obj_dict['H'][0] < pos && pos < width / 2 - obj_dict['H'][1]))){
                horizontal = 0;
            }
        }
        let vertical = parseInt(obj_def[3]);
        if (vertical != 0){
            v_rows = Object.keys(obj_dict).indexOf("V") == -1 || (obj_dict['V'][2] === false || obj_dict['V'][2] == -1) ? rows : obj_dict['V'].slice(2);
            if (!(Object.keys(obj_dict).indexOf("V") == -1 || (obj_dict['V'][0] === false || obj_dict['V'][0] == -1) || (-width / 2 + obj_dict['V'][0] < pos && pos < width / 2 - obj_dict['V'][1]))){
                vertical = 0;
            }
        }

        let support = parseInt(obj_def[4]);
        if (support != 0){
            s_rows = Object.keys(obj_dict).indexOf("S") == -1 || (obj_dict['S'][2] === false || obj_dict['S'][2] == -1) ? rows : obj_dict['S'].slice(2);
            if (!(Object.keys(obj_dict).indexOf("S") == -1 || (obj_dict['S'][0] === false || obj_dict['S'][0] == -1) || (-width / 2 + obj_dict['S'][0] < pos && pos  < width / 2 - obj_dict['S'][1]))){
                support = 0;
            }
            s_style = Object.keys(obj_dict).indexOf("Ss") > -1 ? obj_dict["Ss"] : obj_dict['E'].substring(6,8).split('').map(x=>parseInt(x));
            s_style = s_style.indexOf(0) == -1 ? s_style : this.range(0, 5);
        }

        let legs = parseInt(obj_def[5]);
        if (legs != 0){
            l_rows = Object.keys(obj_dict).indexOf("L") == -1 || (obj_dict['L'][2] === false || obj_dict['L'][2] == -1) ? rows : obj_dict['L'].slice(2);
            if (!(Object.keys(obj_dict).indexOf("L") == -1 || (obj_dict['L'][0] === false || obj_dict['L'][0] == -1) || -width / 2 + obj_dict['L'][0] < pos < width / 2 - obj_dict['L'][1])){
                legs = 0;
            }
        }
        let door_def = obj_dict['E'].split('_').length == 4 ? obj_dict['E'].split('_')[3] : [];
        if (!door_def){
            dl, dr, ds, df, drs = 0, 0, 0, 0, 0;
        }
        else {
            //# Doors on LEFT
            if (door_def[1] == '1' || (door_def[1] == '2' && this.is_inside_domain(motion, obj_dict["DL2"]))){

                dl_style.push(2);
            }

            if (door_def[2] == '1' || (door_def[2] == '2' && this.is_inside_domain(motion, obj_dict["DL3"]))){
                dl_style.push(3)
            }
            if (door_def[3] == '1' || (door_def[3] == '2' && this.is_inside_domain(motion, obj_dict["DL4"]))){
                dl_style.push(4)
            }
            if (dl_style.length > 0){
                dl_rows = Object.keys(obj_dict).indexOf('DLr') > -1 ? obj_dict['DLr'] :rows;
            }
            //# Doors on RIGHT

            if (door_def[4] == '1' || (door_def[4] == '2' && this.is_inside_domain(motion, obj_dict["DR2"])) ||
            door_def[7] == '1' || (door_def[7] == '2' && this.is_inside_domain(motion, obj_dict["DRS2"]))){
                dr_style.push(2);
            }
            if (door_def[5] == '1' || (door_def[5] == '2' && this.is_inside_domain(motion, obj_dict["DR3"])) ||
            door_def[8] == '1' || (door_def[8] == '2' && this.is_inside_domain(motion, obj_dict["DRS3"]))){
                dr_style.push(3);
            }
            if (door_def[6] == '1' || (door_def[6] == '2' && this.is_inside_domain(motion, obj_dict["DR4"])) ||
            door_def[9] == '1' || (door_def[9] == '2' && this.is_inside_domain(motion, obj_dict["DRS4"]))){
                dr_style.push(4);
            }
            if (dr_style.length > 0){  //# Jesli lista nie pusta
                dr_rows = Object.keys(obj_dict).indexOf('DRr') > -1 ? obj_dict['DRr'] :rows;
                //# Kierunek drzwi
                if (door_def[10] == '1' || (door_def[10] == '2' && this.is_inside_domain(motion, obj_dict["DRf"]))){
                    doorR = 2;
                } else {
                    doorR = 1;
                }
            }
        // Drawers on RIGHT:
        if (door_def[7] == '1' || (door_def[7] == '2' && this.is_inside_domain(motion, obj_dict['DRS2']))){
            drs_style.push(2);
        }
        if (door_def[8] == '1' || (door_def[8] == '2' && this.is_inside_domain(motion, obj_dict['DRS3']))){
            drs_style.push(3);
        }
        if (door_def[9] == '1' || (door_def[9] == '2' && this.is_inside_domain(motion, obj_dict['DRS4']))){
            drs_style.push(4);
        }
        if (drs_style.length > 0){
            drs_rows = Object.keys(obj_dict).indexOf('DRSr') > -1 ? obj_dict['DRSr'] : rows;
        }

        df = door_def[14];
        if (door_def[11] != 0 && (Object.keys(obj_dict).indexOf("DS2") == -1 || this.is_inside_domain(motion, obj_dict["DS2"]))){
            ds_style.push(2);
        }
        if (door_def[12] != 0 && (Object.keys(obj_dict).indexOf("DS3") == -1 || this.is_inside_domain(motion, obj_dict["DS3"]))){
            ds_style.push(3);
        }
        if (door_def[13] != 0 && (Object.keys(obj_dict).indexOf("DS3") == -1 || this.is_inside_domain(motion, obj_dict["DS3"]))){
            ds_style.push(4);
        }
        if (ds_style.length > 0){
            ds_rows = Object.keys(obj_dict).indexOf('DSr') > -1 ? obj_dict['DSr'] : rows;
        }

        }
        /*
        let doorL = parseInt(obj_dict['E'][7]);
        if (doorL != 0){
            dl_rows = ((!doorL) || Object.keys(obj_dict).indexOf("DL") == -1) ? rows : obj_dict['DL'];
        }
        let doorR = parseInt(obj_dict['E'][8]);
        if (doorR != 0){
            dr_rows = ((!doorR) || Object.keys(obj_dict).indexOf("DR") == -1) ? rows : obj_dict['DL'];
        }
        */


        let objects = [];
        rows.forEach(row=>{
            let h = horizontal && h_rows.indexOf(row) > -1 ? horizontal : 0;
            let v = vertical && v_rows.indexOf(row) > -1 ? vertical : 0;
            let s = support && s_rows.indexOf(row) > -1  && s_style.indexOf(row_styles[row]) > -1? support : 0;
            let l = legs && l_rows.indexOf(row) > -1 ? legs : 0;
            let dl = 0;
            let dr = 0;
            let ds;
            if (door_def){
                dl = dl_style.length > 0 && dl_rows.indexOf(row) > -1 && dl_style.indexOf(row_styles[row]) > -1 ? 1 : 0;
                dr = dr_style.length > 0 && dr_rows.indexOf(row) > -1 && dr_style.indexOf(row_styles[row]) > -1 ? doorR : 0;
                drs = drs_style.length > 0 && drs_rows.indexOf(row) > -1 && drs_style.indexOf(row_styles[row]) > -1 ? 1 : 0;
                if (drs){
                    dr = 3;
                }
            }
            if (ds_style.length > 0 && ds_rows.indexOf(row) > -1 && ds_style.indexOf(row_styles[row]) > -1){
                ds = row_styles[row] == 2 ? door_def[6] : door_def[7];
                if (row_styles[row] == 2){
                    ds = door_def[11];
                } else if (row_styles[row] == 3){
                    ds = door_def[12];
                } else {
                    ds = door_def[13];
                }
            } else{
                ds = 0;
            }
            if (h || v || s || dl || dr || ds || l){
                objects.push(new MultitypeObject(pos, row, parseInt(obj_def[1]), h, v, s, dl, dr, ds, df, l, support_size, plyty_pol, door_settings));
            }
        });
        return objects;
    },

    generate_fills: function(objects, shelf_rows, shelf_depth, plyty_pol, d_sett, mode_density_factor){
        let doors = {};
        let sections = {};
        this.range(1, shelf_rows.amount + 1).forEach(row=>{

            doors[row] = [];
            let _doors = objects.filter(o=> o.row == row && (o.vertical != 0 ||o.doorL != 0 || o.doorR != 0 )).map(obj=>obj.get_door_object());

            _doors = _doors.sort((x,y)=>x.x - y.x);

            let index = 0;
            while (index < _doors.length){
                let _obj = _doors[index];

                while (index < _doors.length - 1 && _obj.x == _doors[index + 1].x){
                    index += 1;
                    [_obj.left, _obj.right] = [Math.max(_obj.left, _doors[index].left), Math.max(_obj.right, _doors[index].right)];
                }
                doors[row].push(_obj);
                index += 1;
            }

            sections[row] = objects.filter(x=>x.row == row && x.doorS != 0).map(o=> [o.doorS, o.doorS_force]).sort((x,y)=>x[0]-y[0]);
        });



        let geometry = {'doors': [],'drawers': [], 'backs': [], 'verticals': []};
        Object.keys(doors).forEach(row_index=>{
           let obj_group = doors[row_index];
           if (obj_group.length <= 1){
               return;
           }
           this.range(obj_group.length -1).forEach(index=>{
               let section = null;
               let force = false;
               let whut = sections[row_index].filter(s=>obj_group[index].x + d_sett[4] + plyty_pol < s[0] && s[0] < obj_group[index + 1].x - d_sett[4] - plyty_pol);
               if (whut.length > 0){
                   [section,force] = [whut[0][0],whut[0][1]];
               }

                /*
                let _doors, _backs, _drawers;

                [_doors,_drawers, _backs] = obj_group[index].get_door_geometry(obj_group[index + 1],section, force, shelf_rows, shelf_depth, plyty_pol, d_sett);
                geometry["doors"] = geometry["doors"].concat(_doors);
                geometry[_doors.length > 0 ? "backs_doors" : "backs_drawers"] = geometry[_doors.length > 0 ? "backs_doors" : "backs_drawers"].concat(_backs);
                geometry["drawers"] = geometry["drawers"].concat(_drawers);
                */
                let new_geom = obj_group[index].get_fill_geometry(obj_group[index + 1], section, force, shelf_rows, shelf_depth, plyty_pol, d_sett);

                if (mode_density_factor !== -1) { // Type_02_FIX
                    new_geom = this.type_02_fix_door_double_to_single(new_geom, mode_density_factor);
                }

                Object.keys(new_geom).forEach(function (key) {
                    let value = new_geom[key];
                    geometry[key] = geometry[key].concat(value);
                });

           });
        });

        return geometry;
    },
    // Type_02_FIX
    type_02_fix_door_double_to_single: function(new_geom, mode_density_factor){ // Zmienia drzwi dwuskrzydłowe na dwoje drzwi jednoskrzydłowych z pionem
        // doors = sorted(new_geom.get('doors', []), key=lambda x: x.get('x1', 0));

        let doors = (new_geom.doors || []).sort((a, b) => a['x1'] - b['x1']);
        if (doors.length < 2){
            return new_geom
        }
        if (doors[0]['x2'] != doors[1]['x1'] || Math.abs(doors[1]['x2'] - doors[0]['x1']) < mode_density_factor){
            return new_geom;
        }
        let split = doors[0]['x2'];
        new_geom['verticals'] = [{'x1':split, 'x2':split, 'y1':doors[0]['y1'], 'y2':doors[0]['y2']}];
        doors[0]['x2'] -= 9;
        doors[1]['x1'] += 9;
        [doors[0]['type'], doors[0]['flip'], doors[1]['type'], doors[1]['flip']] = [0, 0, 0, 0];
        if (new_geom.backs !== undefined || new_geom.backs.length == 1) {
            var back_1 = Object.assign({}, new_geom.backs[0]);
            back_1['x2'] = doors[0]['x2'];
            var back_2 = Object.assign({}, new_geom.backs[0]);
            back_2['x1'] = doors[1]['x1'];
            new_geom.backs = [back_1, back_2];
        }
        return new_geom
    },

    // Type_02_FIX - wersja porzucona - zostaje na wszelki wypadek
    // type_02_fix_narrow: function(output_data, ratio){
    //     for (var key in output_data) {
    //             if (key == 'additional_elements') {
    //                 for (var sub_key in output_data[key]) {
    //                     if (sub_key != 'styles') {
    //                         for (var elem_id in output_data[key][sub_key]) {
    //                             output_data[key][sub_key][elem_id]['x1'] = output_data[key][sub_key][elem_id]['x1'] / ratio;
    //                             output_data[key][sub_key][elem_id]['x2'] = output_data[key][sub_key][elem_id]['x2'] / ratio;
    //                         }
    //                     }
    //                 }
    //
    //             } else {
    //                 for (var elem_id in output_data[key]) {
    //                     output_data[key][elem_id]['x1'] = output_data[key][elem_id]['x1'] / ratio;
    //                     output_data[key][elem_id]['x2'] = output_data[key][elem_id]['x2'] / ratio;
    //                 }
    //             }
    //         }
    //     return output_data
    // },

    generate_legs: function(elements, shelf_rows, depth){
        let hori = elements[0].sort((x,y) => x-y);
        let legs = [];
        this.range(Math.floor(hori.length/2.0)).map(i => 2*i+1).map(i=> [hori[i-1], hori[i]]).map(x=>{
            let [h1,h2] = x;
            let _pts = elements[1].filter(item=>h1+20 < item && item < h2-20).sort((x,y) => x-y);
            _pts = _pts.length > 0 ? _pts : [h1 + 20];
            _pts.forEach((pt,nr) => {
                if (nr==0 && Math.abs(h1-pt) > 150){
                    legs.push(h1+20);
                }
                if (legs.length == 0 || Math.abs(legs[legs.length-1] - pt) > 130) {

                    legs.push(pt);
                    if (legs.length > 1 && Math.abs( legs[legs.length-2] - legs[legs.length-1]) > 600) {
                        legs.splice(legs.length-2,0, parseInt(Math.floor((legs[legs.length-2] + legs[legs.length-1])/ 2))); // tutaj insert na -2
                    }
                }
                if (nr == _pts.length - 1 && Math.abs(h2-pt) > 150){
                    legs.push(h2-20);
                    if (Math.abs(legs[legs.length-2] - legs[legs.length-1]) > 600){
                        legs.splice(legs.length-2,0,parseInt(Math.floor((legs[legs.length-2] + legs[legs.length-1])/ 2))); // tutaj insert na -2
                    }
                }
            })
        });
        let legs_out = [];
        legs.forEach(pt=>{
            legs_out = legs_out.concat(MultitypeObject.prototype.gen_legs_geometry(pt, shelf_rows, depth));
        });
        return legs_out;
    },

    generate_horizontals: function(objects, shelf_rows, plyty_pol){
        let points = {};
        this.range(shelf_rows.amount+1).forEach(row=>{
            points[row] = [];
            objects.filter(o=>o.row == row && o.horizontal != 0).forEach(obj=>{
               points[row] = points[row].concat(obj.get_horizontal_points(plyty_pol));
            });
            points[row] = points[row].sort((x,y)=>x-y);
        });
        let geometry = [];
        Object.keys(points).forEach(row=> {
            let pts = points[row];
            if (pts.length <= 1){
                return;
            }
            this.range(1,pts.length,2).forEach(x=>{
                geometry = geometry.concat(MultitypeObject.prototype.gen_horizontal_geometry(pts[x - 1], pts[x], shelf_rows.top[row], plyty_pol));
            })
        });
        return geometry;
    },
    /*
    get_row_coor: function(ilosc_rzedow, wysokosci_rzedow) {
        let wsp_rzedow_dol = [0];
        let wsp_rzedow_gora = [0];
        let wys = 0;
        for (var x = 0; x < parseInt(ilosc_rzedow); x++){
            wsp_rzedow_dol.push(wys);
            wys += parseInt(wysokosci_rzedow[x]);
            wsp_rzedow_gora.push(wys);
        }
        return new Rows(wsp_rzedow_dol, wsp_rzedow_gora, ilosc_rzedow);
    },
    */
    check_element_occurrence: function(proper_rows, row_style, element_rows, element_top, element_style) {
        let rows = [];
        element_rows.map(row=> {
            row = parseInt(row);
            if (proper_rows[element_top].indexOf(row) > -1 && (element_style.indexOf('0') > -1 || element_style.indexOf(row_style[row].toString()) > -1)){
                rows.push(row);
            }
        });
        return rows;
    },

    division_by_substraction: function(dividend, divisor){
        let start = parseInt(dividend / divisor) - 2;
        let result = start
        let reminder = dividend - divisor * result;
        //# Dzielenie bez reszty
        while (reminder >= divisor) {
            reminder -= divisor
            result += 1
            if (result - start == 10) {
                throw("blad");
            }
        }
        //# Zaokraglenie wyniku
        if (reminder * 2 >= divisor) {
            result += 1;
        }
        return result
    },

    get_pos_from_table: function (val, table, precision=100000000){
        // magic line, i dont know why for indexof 0 == "0.00"
        val = parseFloat(val);

        if (table.map(x=>x[0]).indexOf(val) > -1){
            //let find_first = table.find(x=> x[0] == val);
            let find_first = table.filter(x=> x[0] == val);

            return find_first.length == 0 ? null : find_first[0][1];
        }

        var index = table.map((x,i) =>  x[0] >= val ? i : null).filter(x=> x != null);

        if (index.length == 0 || index[0] == 0){
            return null;
        }
        index = index[0];

        let d_in = [table[index - 1][0], table[index][0]];
        let d_out = [table[index - 1][1], table[index][1]];

        let proportion = this.division_by_substraction((val - d_in[0]) * precision, (d_in[1] - d_in[0]));
        let addition = (d_out[1] - d_out[0]) * proportion;
        let total = d_out[0] + this.division_by_substraction(addition, precision);
        return total;
    },
    get_base_pos: function(width,val,table) {
        let pozycja = this.get_pos_from_table(val, table);
        if (pozycja != null) {
            return pozycja - width/2;
        } else {
            return null;
        }
    },
    get_adjustment: function(width,val,table,system, plyty_pol){
        if (system[2] == '1') {
           return this.get_pos_from_table(val, table);
       } else if (system[2] == '2') {
           let pos = this.get_pos_from_table(val, table);
           return pos == null ? null : this.division_by_substraction(pos * (width - 2 * plyty_pol), 10000);
       } else {
            return null;
       }
    },
    get_element_pos: function (width, motion, system, minima, table_pos, table_adjust, plyty_pol){
        var table_pos = table_pos.map(x => [parseInt(x[0]), parseInt(x[1])]);
        //var table_adjust = table_adjust.map(x => [parseInt(x[0]), parseInt(x[1])]);
        if (system[1] == '0'){
            var [val_pos, val_adjust] = [parseInt(width), parseInt(motion)];
            }
        else if (system[1] == '1') {
            var [val_pos, val_adjust] = [parseInt(motion), parseInt(width)];
        }
        else {
            return null;
        }
        let pozycja = this.get_base_pos(width, val_pos, table_pos);
        if (pozycja == null) {
            return null
        }
        if (table_adjust != null){
            table_adjust = table_adjust.map(x => [parseInt(x[0]), parseInt(x[1])]);
            let korekta = this.get_adjustment(width, val_adjust, table_adjust, system, plyty_pol);
            if (korekta == null){
                return null;
            }
            pozycja += korekta;
        }
        let left = parseInt(-width / 2 + minima[0]);
        let right = parseInt(width / 2 - minima[1]);
        return left <= pozycja && pozycja <= right ? pozycja : null;
    },



    elements_snapping: function(row,elements,row_amount,rounding){
        if (row==row_amount){
            return;
        }
        let next_row = row + 1;
        elements.filter(e=>e.row==row).forEach(element=> {
            let element_above_list = elements.filter(e => e.row == row + 1 && Math.abs(element.x - e.x) < rounding);
            element_above_list.forEach(element_above => {
                if (element_above && (0 < Math.abs(element.x - element_above.x) && Math.abs(element.x - element_above.x) < rounding)) {
                    if (!element_above.lock) {
                        element_above.x = element.x;
                        element_above.lock = true;
                    } else if (!element.lock) {
                        element.x = element_above.x;
                        element.lock = true;
                        next_row = row > 0 ? row - 1 : row + 1;
                    }
                }
            });
        })
        return this.elements_snapping(next_row, elements, row_amount, rounding);
    },

    generate_elements_simplified_data: function(geometry, rows, plyty_pol){
        let exploded_horizontals = geometry["horizontals"];
        let exploded_verticals = geometry['verticals'].filter(v=>rows.bottom.slice(1).indexOf(v['y1'] - 9) + 1 == rows.top.indexOf(v['y2'] + 9));

        geometry['verticals'].filter(e => rows.bottom.slice(1).indexOf(e['y1'] - 9) + 1 != rows.top.indexOf(e['y2'] + 9)).forEach(v => {
            let index_bottom = rows.bottom.slice(1).indexOf(parseInt(v['y1']) - 9) + 1;
            let index_top = rows.top.indexOf(parseInt(v['y2']) + 9);
            exploded_verticals = exploded_verticals.concat(MultitypeObject.prototype.gen_verticals_geometry(v['x1'], this.range(index_bottom, index_top + 1), rows, plyty_pol));
            this.range(index_bottom,index_top+1).forEach(row=>{
                exploded_horizontals = exploded_horizontals.concat(MultitypeObject.prototype.gen_horizontal_geometry(v['x1'], v['x1'], rows.top[row], plyty_pol));
            });
        });
        let elements = rows.top.map(x=>[[],[]]);
        rows.top.forEach((r,nr)=>{
            exploded_horizontals.filter(h=>h['y1'] == r).forEach(hori=>{
                elements[nr][0] = elements[nr][0].concat([hori["x1"], hori["x2"]]);
            });
            exploded_verticals.filter(v=>v['y2'] + 9 == r).forEach(ver=>{
                elements[nr][1].push(ver["x1"]);
            });
        });

        elements.forEach(element_row=>{
            element_row[0] = element_row[0].sort((x,y)=>x-y);
            element_row[1] = element_row[1].sort((x,y)=>x-y);
            }
        )
        return elements;
    },



    get_shadows_openings: function(elements,width,plyty_pol){
        let openings = [];
        elements.forEach((elem,nr) =>
        {
            let hori = elem[0];
            let vert = elem[1];
            if (hori){
                hori.push(-width/2);
                hori.push(width/2);
                hori = hori.filter(x=> hori.filter(z=>{ return z === x;}).length == 1).sort((x,y)=>x-y);
                if (hori.length > 0){
                    hori = this.range(1, hori.length, 2).map(x=> [hori[x-1], hori[x]]);
                }
            }
            if (vert){
                vert = vert.sort((x,y)=>x-y);
                if (vert.length > 0 && vert[0] != -width /2 + plyty_pol){
                    vert.splice(0,0,-width/2 - plyty_pol);
                }
                if (vert.length > 0 && vert[vert.length-1] != width /2 - plyty_pol){
                    vert.push(width/2 + plyty_pol);
                }
                if (vert.length > 0) {
                    vert = this.range(1,vert.length).map(x => [vert[x-1] + plyty_pol, vert[x] - plyty_pol]);
                }
            }
            openings.push([hori,vert]);
        });
        return openings;
    },

    check_multirow_shadow: function(lewa, prawa, nr_poziomu, openings, rows, plyty_pol, tolerancja=2) {
        if (nr_poziomu < openings.length - 1 && openings[nr_poziomu][0].length >= 1 && openings[nr_poziomu + 1][1].length >= 1){
                    let _min = openings[nr_poziomu][0][openings[nr_poziomu][0].map(l=>Math.abs(lewa - l[0])).indexOf(Math.min(...openings[nr_poziomu][0].map(l=>Math.abs((lewa - l[0])))))];
                    if (Math.abs(lewa - _min[0]) < tolerancja && Math.abs(prawa - _min[1]) < tolerancja){
                        _min = openings[nr_poziomu + 1][1][openings[nr_poziomu + 1][1].map(l=>Math.abs(lewa - l[0])).indexOf(Math.min(...openings[nr_poziomu + 1][1].map(l=>Math.abs(lewa - l[0]))))];
                        if (Math.abs(lewa - _min[0]) < tolerancja && Math.abs(prawa - _min[1]) < tolerancja){
                            openings[nr_poziomu + 1][1].splice(openings[nr_poziomu + 1][1].indexOf(_min),1);
                            return this.check_multirow_shadow(lewa,prawa,nr_poziomu+1, openings, rows, plyty_pol, tolerancja);
                        }
                    }
                }
        return rows.top[nr_poziomu] - plyty_pol;
    },

    get_shadows_geometry: function(openings, rows, width, plyty_pol, depth, shadow_settings){
        if (shadow_settings == undefined){
            shadow_settings = [2, 2, 4, 2];
        }
        let out = {
            'shadow_left': [],
            'shadow_middle': [],
            'shadow_right': [],
            'shadow_side': []
        };

        this.range(openings.length).forEach(nr_poziomu=>{
            if (!openings[nr_poziomu][1]){
                return true;
            }
            //let y = [rows.bottom[nr_poziomu] + 9, rows.top[nr_poziomu] - 9];
            /*
            if (openings[nr_poziomu][1].map(x=>x[0]).indexOf(parseInt(-width / 2 + plyty_pol * 2)) > -1){
                out['shadow_side'].push({'x1': -width / 2 - plyty_pol / 2, 'y1': rows.bottom[nr_poziomu] + 9 + offset, 'z1': 0,
                                       'x2': -width / 2 - 2, 'y2': rows.top[nr_poziomu] - 9 - offset, 'z2': depth - offset})
            }
            */
            openings[nr_poziomu][1].forEach(x => {
                let [lewa,prawa] = x;
                let typ = 'shadow_middle';
                if (lewa == -width / 2){
                    typ = 'shadow_left'}
                else if (prawa == width / 2){
                    typ = 'shadow_right'
                    }

                let _y = [rows.bottom[nr_poziomu] + plyty_pol, this.check_multirow_shadow(lewa, prawa, nr_poziomu, openings, rows, plyty_pol)];
                out[typ].push({'x1': lewa  + shadow_settings[0], 'y1': _y[0] + shadow_settings[2], 'z1': 0, 'x2': prawa - shadow_settings[1], 'y2': _y[1] - shadow_settings[3], 'z2': depth})
            });
        });
        return out
    },


    split_inserts_by_row: function(inserts, shelf_rows, plyty_pol){
        let splitted = this.range(shelf_rows.amount).map(x=>[[], [], [], []]);
        let key_names = ['doors', 'drawers', 'backs_doors', 'backs_drawers'];
        key_names.forEach((key,group_index) => {
            inserts[key].forEach(elem => {
                let index = null;
                shelf_rows.bottom.forEach((h,i) => {
                    if (index != null || Math.abs(h - elem["y1"]) != plyty_pol){
                        return;
                    }
                    index = (i || 1) - 1;
                });
                if (index == null){
                    return;
                }
                splitted[index][group_index].push(elem);
            })
        });
        return splitted;
    },


    mix_inserts_by_row_style: function(inserts, shelf_rows, plyty_pol){
        const zip = (...arrays) => {
            const length = Math.min(...arrays.map(arr => arr.length));
            return Array.from({ length }, (value, index) => arrays.map((array => array[index])));
        };

        const doors_to_drawers = function(doors){
            let drawers = [];
            while (doors.length > 0){
                    let door = doors.shift();
                    let da = [door["x1"], door["x2"]];
                    let lets_stop = false;
                    doors.forEach((d,i)=>{
                        if (!lets_stop && (da.indexOf(d["x1"]) > -1 || da.indexOf(d["x2"]) > -1)){
                            da = da.concat([d["x1"], d["x2"]]);
                            doors.splice(i,1);
                            lets_stop = true;
                        };
                    });
                    door['x1'] = Math.min.apply(null, da);
                    door['x2'] = Math.max.apply(null, da);
                    door['type'] = 0;
                    drawers.push(door);

            }
            return drawers;
        };
        inserts = this.split_inserts_by_row(inserts, shelf_rows, plyty_pol);

        let proper_elements = {'doors':[], 'drawers':[], 'backs':[]};
        let row_counter = 1;
        zip(shelf_rows.fill_b.slice(1),shelf_rows.fill_a.slice(1), inserts).forEach(x => {
            let door_proper_height = shelf_rows.top[row_counter] - shelf_rows.bottom[row_counter] < 220 ? false: true;
            let fb,fa,fill_a, fill_a_backs, fill_b, fill_b_backs;

            [fb, fa, [fill_a, fill_b, fill_a_backs, fill_b_backs]] = x;
            let table = [
                [fa, fill_a, fill_a_backs],
                [fb, fill_b, fill_b_backs],
            ];
            table.map(y=>{
                let filtr,geom,backs;
                [filtr,geom,backs] = y;
                if (filtr == 1 && door_proper_height){
                    proper_elements['backs'] = proper_elements['backs'].concat(backs);
                    proper_elements['doors'] = proper_elements['doors'].concat(geom);
                }
                if (filtr == 2){
                    proper_elements['drawers'] = proper_elements['drawers'].concat(doors_to_drawers(geom));
                }
                if ([2, 3].indexOf(filtr) > -1){
                    proper_elements['backs'] = proper_elements['backs'].concat(backs);
                }

            });
            row_counter += 1;
        });
        return proper_elements;
    },

    get_available_styles_list: function(doors, drawers, shadows, shelf_rows, s_sett, d_sett, dk_sett, plyty_pol){
        let styles = [];
        this.range(1, shelf_rows.amount + 1).forEach(row_nr=>{
            let _y1, _y2;
            [_y1, _y2] = [shelf_rows.bottom[row_nr], shelf_rows.top[row_nr]];

            let [small, low] = [_y2 - _y1 < 210, _y2 <= dk_sett['drawer_max_position']];
            let available_openings = shadows.filter(s=>s["y1"] == _y1 + plyty_pol + s_sett[2]
                                            && s["y2"] == _y2 - plyty_pol - s_sett[3]
                                            && s["x2"] - s["x1"] >= d_sett[0]).length;

            let doors_in_openings = doors.filter(d=>d["type"] == 0 && d["y1"] == _y1 + plyty_pol).length;
            let drawers_in_openings = drawers.filter(d=>d["y1"] == _y1 + plyty_pol).length;
            let filled_openings = doors_in_openings + drawers_in_openings;

            styles.push([
                [doors_in_openings, available_openings].indexOf(0) == -1 && !small ? 1 : 2,  // None
                [0, 1].indexOf(available_openings) > -1 || small ? 0 : (available_openings > doors_in_openings && doors_in_openings != 0 ? 2 : 1),
                available_openings == 0 || small ? 0 : (available_openings > doors_in_openings ? 1 : 2),
                //# Drawers
                [drawers_in_openings, available_openings].indexOf(0) == -1 && low ? 1 : 2,  // None
                [0, 1].indexOf(available_openings) > -1 || !low ? 0 : (available_openings > drawers_in_openings && drawers_in_openings != 0 ? 2 : 1),
                available_openings == 0 || !low ? 0 : (available_openings > drawers_in_openings ? 1 : 2),
            ]);
        });
        return styles
    },

    generate_backpanels: function(output_data, rows, plyty_pol, d_sett, selected_rows){
        let bkp_geom = output_data['backs'];
        let verticals = rows.top.map(y1=>{
            return output_data['verticals'].filter(v=> v['y1'] == y1 + plyty_pol).sort((a,b) => a['x1'] - b['x1']);
        });
        let horizontals = rows.top.map(y1=>{
            return output_data['horizontals'].filter(v=> v['y1'] == y1).sort((a,b) => a['x1'] - b['x1']);
        });
        let backpanels = rows.top.map(y1=>{
            return output_data['backs'].filter(v=> v['y1'] == y1 + plyty_pol).sort((a,b) => a['x1'] - b['x1']);
        });
        rows.top.slice(0,rows.top.length-1).forEach((row_top, row_index) => {
            if (selected_rows && selected_rows.indexOf(row_index) == -1){
                return;
            }
            verticals[row_index].slice(1).forEach((v_right, index)=>{
                let v_left = verticals[row_index][index];
                let center = (v_right['x1'] + v_left['x1']) / 2;
                let back = backpanels[row_index].filter(b=> b['x1'] == v_left['x1'] + plyty_pol);
                back = back.length > 0 ? back[0] : null;
                let h_below = horizontals[row_index].filter(h=>h['x1'] < center && center < h['x2']);
                h_below = h_below.length > 0 ? h_below[0] : null;
                let h_above = horizontals[row_index+1].filter(h=> h['x1'] < center && center < h['x2']);
                h_above = h_above.length > 0 ? h_above[0] : null;
                if (back === null && h_above != null && h_below != null){
                    bkp_geom.push({
                        'x1': v_left['x1'] + plyty_pol,
                        'x2': v_right['x1'] - plyty_pol,
                        'y1': h_below['y1'] + plyty_pol,
                        'y2': h_above['y1'] - plyty_pol,
                        'z1': d_sett[7],
                        'z2': 0});
                }
            })
        });
    },


    get_elements_inner: function(input_data, motion, width, row_amount, row_heights, row_styles, shelf_depth=320,
                                 plyty_pol=9, support_size=125, snapping=false, shadow_settings=[2,2,4,2],
                                 gen_row_styles_for_buttons=true, styles_slider=-1, backpanels_rows=null,
                                 mode_density_factor=-1, dekoder_settings){
        plyty_pol = parseInt(plyty_pol);
        support_size = parseInt(support_size);
        shelf_depth = parseInt(shelf_depth);
        row_amount = parseInt(row_amount);
        motion = parseInt(motion);

        let door_settings = input_data["door_settings"];
        if (backpanels_rows == null) {
            backpanels_rows = [];
        }

        let shelf_rows = this.get_rows_obj_with_styles(row_styles, input_data, styles_slider, row_amount, row_heights, dekoder_settings);


        let horizontals = this.range(row_amount+1).map(i => []);
        let proper_rows = [
            this.range(row_amount+1),  // Uniwersalne
            this.range(row_amount),  // Srodkowe
            [row_amount]  // Topowe
        ];
        var output_data = {};
        let objects = [];
        let element_types = ["", "horizontals", "verticals", "supports", "legs", "doors", "boxes"];
        let keys = Object.keys(input_data["elements"]).sort((x,y)=> parseInt(x) - parseInt(y));

        let element_table = this.range(0, keys.length, 1).map(x=>false);
        keys.forEach(key => {
            let e = input_data["elements"][key];
            if (!(parseInt(e["M"][2]) <= width && width <= parseInt(e["M"][3])) || (e["M"].length > 4 && !(this.is_inside_domain(motion,e["M"].slice(4))))){
                return;
            }
            //# Sprawdz zaleznosci
            if(Object.keys(e).indexOf("jeslitak") > -1 && !element_table[e["jeslitak"]]){
                return;
            }
            if (Object.keys(e).indexOf("jeslinie") && element_table[e["jeslinie"]]){
                return;
            }
            // [str(10 * int(e['E'].split('_')[1][5 + x]) + int(e['E'].split('_')[1][6 + x])) for x in range(0, len(e['E'].split("_")[1][5:]), 2)]
            let rzedy = this.range(0, e['E'].split("_")[1].substring(5).length, 2).map(x=> "" + (10 * parseInt(e['E'].split('_')[1][5 + x]) + parseInt(e['E'].split('_')[1][6 + x])));

            let element_rows = this.check_element_occurrence(proper_rows, shelf_rows.layout, rzedy, parseInt(e['E'][5]), e['E'].substring(6,9));
            if (element_rows == null || element_rows.length == 0) {
                return;
            }

            var _typ = parseInt(e["E"][1]);

            let _pos = this.get_element_pos(width, motion, e['E'].substring(0,3), e["M"].slice(0,2), e["P1"], Object.keys(e).indexOf("A1") > -1 ? e["A1"] : null, plyty_pol);
            if (_pos == null) {
                return;
            } else {
                element_table[parseInt(key)] = true;
            }
            objects = objects.concat(this.generate_objects(_pos, element_rows, e, width, shelf_rows.layout, motion, support_size, plyty_pol,door_settings));

        });
        if ([0,'0'].indexOf(input_data['snapping']) == -1 && snapping) {
            this.elements_snapping(1, objects, shelf_rows.amount, input_data['snapping']);
        }

        output_data = {
            'verticals': objects.filter(o=> o.vertical).map(_obj=> _obj.get_verticals_geometry(shelf_rows, plyty_pol)).reduce((x,y)=>x.concat(y), []),
            'supports': objects.filter(o=> o.support).map(_obj=> _obj.get_support_geometry(shelf_rows, plyty_pol, support_size)).reduce((x,y)=>x.concat(y), []),
            'legs': objects.filter(o=> o.legs).map(_obj=> _obj.get_legs_geometry(shelf_rows, shelf_depth)).reduce((x,y)=>x.concat(y), []),
            'horizontals': this.generate_horizontals(objects, shelf_rows, plyty_pol),
            'doors': [],
            'backs': [],
            'drawers': [],
        };
        /*
        if ((shelf_rows.layout.reduce((x,y)=> y+x,0)) -1 > row_amount){
            let ddb_geom = this.generate_doors(objects, shelf_rows, shelf_depth, plyty_pol, door_settings);
            ddb_geom = this.mix_inserts_by_row_style(ddb_geom, shelf_rows, plyty_pol);
            output_data = Object.assign({},output_data, ddb_geom);
        }*/

        let ddb_geom = this.generate_fills(objects, shelf_rows, shelf_depth, plyty_pol, door_settings, mode_density_factor);
        //output_data = Object.assign({},output_data, ddb_geom);
        for (const [key, value] of Object.entries(ddb_geom)){
                    output_data[key] = output_data[key].concat(ddb_geom[key]);
        }

        let simplified_data = this.generate_elements_simplified_data(output_data, shelf_rows, plyty_pol);



        if ([1,'1'].indexOf(input_data['legs']) > -1 ) {
            output_data["legs"] = output_data["legs"].concat(this.generate_legs([simplified_data[0][0], simplified_data[1][1]], shelf_rows, shelf_depth))

        }

        let openings = this.get_shadows_openings(simplified_data, width, plyty_pol);
        output_data["additional_elements"] = this.get_shadows_geometry(openings, shelf_rows, width, plyty_pol, shelf_depth, shadow_settings);

        if (gen_row_styles_for_buttons){
            output_data["additional_elements"]["styles"] = this.get_available_styles_list(
            output_data["doors"], output_data["drawers"],
            output_data["additional_elements"]["shadow_middle"], shelf_rows,
            shadow_settings, door_settings,
            dekoder_settings, plyty_pol);
        }
        if (backpanels_rows != null && backpanels_rows.length > 0){
            this.generate_backpanels(output_data, shelf_rows, plyty_pol, door_settings, backpanels_rows);
        }

        let shelf_type = dekoder_settings['shelf_type'];
        output_data["additional_elements"]['shelf_type'] = shelf_type;
        // row_data added only for dna tests, when testing use shelf_type with negative value type01 == -1, type02 == -2
        if (shelf_type < 0 ){
            output_data["additional_elements"]["row_data"] = {};
            output_data["additional_elements"]["row_data"]['layout'] = shelf_rows.layout;
            output_data["additional_elements"]["row_data"]['fill_a'] = shelf_rows.fill_a;
            output_data["additional_elements"]["row_data"]['fill_b'] = shelf_rows.fill_b;
            output_data["additional_elements"]["row_data"]['amount'] = shelf_rows.amount;
            output_data["additional_elements"]["row_data"]['bottom'] = shelf_rows.bottom;
            output_data["additional_elements"]["row_data"]['top'] = shelf_rows.top;
        }

        return output_data;
    },


    output_cleaner: function(geom, outputDataStructure){
        if (outputDataStructure === 'flat'){
            delete geom.additional_elements.row_data;
            geom = {...geom, ...geom['additional_elements']};
            delete geom.additional_elements;
        }
        // TEMP CHECK ALL ROWS!
        geom.backs = geom.backs.filter( x =>  Math.abs(x.x2 - x.x1) > 129 );

        return geom;
    },


    styles_cleaner: function(geom, shelf_depth){
        if (shelf_depth === 240) {
            geom["additional_elements"]["styles"].forEach((row, row_i) => {
                geom["additional_elements"]["styles"][row_i][4] = 0;
                geom["additional_elements"]["styles"][row_i][5] = 0;
                }
            )
        }
        return geom;

    },

    // output_data["additional_elements"]["styles"]

    get_dna_name: function(input_data) {
        if (input_data['dna_name'].includes('GRID')) return 'GRID'
        if (input_data['dna_name'].includes('GRADIENT')) return 'GRADIENT'
        if (input_data['dna_name'].includes('SLANT')) return 'SLANT'
        if (input_data['dna_name'].includes('PATTERN')) return 'PATTERN'
        else return ''
    },

    get_elements: function(input_data, motion, width, row_amount, row_heights, row_styles, shelf_depth=320,
                           plyty_pol=9, support_size=125, snapping=false, shadow_settings=[2,2,4,2],
                           gen_row_styles_for_buttons=true, styles_slider=-1, backpanels_rows=null, shelf_type=0,
                           outputDataStructure=null
    ) {
        // TEMP CHECK ALL ROWS!

        const shelf_type_name = this.get_dna_name(input_data)

        // Fix wartosci jesli plecy
        if (input_data['dna_name'].includes('PATTERN') && snapping !== false && backpanels_rows && backpanels_rows.length) {
            let density_mapping = ((shelf_type === 0) ? density_mapping_type01 : density_mapping_type02);
            if (width in density_mapping && motion in density_mapping[width]) {
                motion = density_mapping[width][motion];
            }
        }

        //Fix styli jestli 24 cm
        const mapStylesByDepth = (style, depth) => {
            let isDrawer = style < 40 && style > 19
            if (isDrawer && depth === 240) return style - 20
            return style
        }
    
        let newStyles =  row_styles.map((s) => mapStylesByDepth(s, shelf_depth))
        row_styles = [...newStyles]

        //////////////////////////////////  TEMP: Do przeniesienia jako input funkcji
        let dna_type = input_data['dna_name'].includes('PATTERN') ? 2 : input_data['dna_name'].includes('SLANT') ? 0 : input_data['dna_name'].includes('GRADIENT') ? 1 : 3;

        ////////////////////////////////// VARIABLES
        let inner_dekoder_settings = Object.keys(input_data).indexOf("dekoder_settings") > -1 ? input_data["dekoder_settings"] : {'drawer_max_position':1578};
        const real_door_heights = [278, 398, 300, 400];
        const all_door_heights = [208, 278, 398, 200, 300, 400];
        inner_dekoder_settings['proper_door_heights'] = real_door_heights;
        inner_dekoder_settings['shelf_type'] = shelf_type;

        ////////////////////////////////// TYPE_01
        if (shelf_type === 0){
            let output_geom =  this.get_elements_inner(
                input_data, motion, width, row_amount, row_heights, row_styles, shelf_depth,
                plyty_pol, support_size, snapping, shadow_settings,
                gen_row_styles_for_buttons, styles_slider, backpanels_rows, -1, inner_dekoder_settings);

            // output_geom = this.styles_cleaner(output_geom, shelf_depth)
            output_geom = this.output_cleaner(output_geom, outputDataStructure)
            processBestShelfSplit(output_geom, shelf_type_name)
            return output_geom;
        }

        ////////////////////////////////// SLIDER TRIM
        if ([0, 3].indexOf(dna_type) > -1){
            let table;
            if (dna_type === 0) { // SLANT
                table = [[700, 50], [1020, 34], [1060, 0], [1360, 50], [1440, 34], [1710, 25], [1980, 20],
                    [2000, 40], [2410, 25], [2610, 50], [2820, 25], [3180, 50], [3400, 25], [3970, 20], [4320, 40]];
            } else { // GRID
                table = [[700, 50], [870, 0], [1160, 34], [1430, 25], [1710, 20], [1730, 40], [1810, 25],
                    [1990, 21], [2280, 18], [2300, 34], [2560, 29], [2760, 18], [2840, 15], [2860, 29], [3120, 25],
                    [3410, 23], [3430, 34], [3500, 25], [3690, 22], [3990, 12], [4000, 23], [4250, 13], [4410, 17]];
            }
            const min_motion = table.reverse().find(function(x) {return x[0] <= width})[1];
            const max_motion = 100;
            motion = Math.max(0, Math.floor(min_motion + (max_motion - min_motion) * motion / 100));
        }

        /////////////////////////////////// GET NORMAL GEOMETRY
        let normal_geometry = this.get_elements_inner(
            input_data, motion, width, row_amount, row_heights, row_styles, shelf_depth,
            plyty_pol, support_size, snapping, shadow_settings,
            gen_row_styles_for_buttons, styles_slider, backpanels_rows, -1, inner_dekoder_settings);

        /////////////////////////////////// GET DENSE MAX DOORS GEOMETRY
        inner_dekoder_settings['proper_door_heights'] = all_door_heights;
        let mode_density_factor = 551;
        let dense_geometry = this.get_elements_inner(
            input_data, motion, width, row_amount,
            row_heights, [13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13],
            shelf_depth, plyty_pol, support_size, snapping, shadow_settings,
            gen_row_styles_for_buttons, styles_slider, backpanels_rows, mode_density_factor, inner_dekoder_settings);

        /////////////////////////////////// SPLIT GEOMETRY BY ROW
        // Współrzędne rzędów
        let r_coor = [];
        row_heights.reduce(function(a,b,i) { return r_coor[i] = a+b; }, 9);
        r_coor.unshift(plyty_pol);
        // Podział drzwi, szuflad, pleców i faktycznych filli na rzędy
        let fill_by_row = r_coor.map(i => dense_geometry['doors'].filter((value, index, array) => value['y1'] === i));
        let bfill_by_row = r_coor.map(i => dense_geometry['backs'].filter((value, index, array) => value['y1'] === i));
        let door_by_row = r_coor.map(i => normal_geometry['doors'].filter((value, index, array) => value['y1'] === i));
        let drawer_by_row = r_coor.map(i => normal_geometry['drawers'].filter((value, index, array) => value['y1'] === i));
        let back_by_row = r_coor.map(i => normal_geometry['backs'].filter((value, index, array) => value['y1'] === i));

        /////////////////////////////////// ADD GEOMETRY
        dense_geometry['doors'] = [];
        dense_geometry['drawers'] = [];
        dense_geometry['backs'] = [];
        let p = 2;
        let doors = [];
        let drawers = [];
        let inproper_drawers = [];
        let backs = [];
        for (let index = 0; index < row_amount; ++index) {
            doors = fill_by_row[index].filter(fill => {
                return door_by_row[index].some(door => fill['x1'] -p <= door['x1'] && fill['x2'] + p >= door['x2']) || door_by_row[index].some(door => door['x1'] - p <= fill['x1'] && door['x2'] + p >= fill['x2'])
            });
            drawers = fill_by_row[index].filter(fill => {
                return drawer_by_row[index].some(door => fill['x1'] -p  <= door['x1'] && fill['x2'] + p >= door['x2']) || drawer_by_row[index].some(door => door['x1'] - p <= fill['x1'] && door['x2'] + p >= fill['x2'])
            });
            inproper_drawers = [];
            for (let di = 0; di < drawers.length - 1; ++di) {
                if (drawers[di]['x2'] === drawers[di+1]['x1']){
                    drawers[di]['x2'] = drawers[di+1]['x2'];
                    inproper_drawers.push(di+1);
                }
            }
            drawers = drawers.filter((d, i) => inproper_drawers.indexOf(i)<0);

            backs = bfill_by_row[index].filter(fill => {
                return back_by_row[index].some(door => fill['x1'] - p <= door['x1'] && fill['x2'] + p >= door['x2']) || back_by_row[index].some(door => door['x1'] - p <= fill['x1'] && door['x2'] + p >= fill['x2'])
            });
            dense_geometry['drawers'].push(...drawers);
            dense_geometry['doors'].push(...doors);
            dense_geometry['backs'].push(...backs);
        }

        ///////////////////////////////////  Nadpisanie geometrii z bazowego dna
        dense_geometry['supports'] = normal_geometry['supports'];
        dense_geometry['additional_elements']['styles'] = normal_geometry['additional_elements']['styles'];

        /////////////////////////////////  Dodanie brakujących pionów (tryb some)
        let missing_v = normal_geometry['verticals'].filter(v => !dense_geometry['verticals'].some(
            dv => Math.abs(v['x1'] - dv['x1']) < 40 && Math.abs(v['x2'] - dv['x2']) < 40 && v['y1'] === dv['y1'] && v['y2'] === dv['y2']
        ));
        if (missing_v.length !== 0) {
            let v_by_row = r_coor.map(i => missing_v.filter((value, index, array) => value['y1'] === i));
            let current_shadow_by_row = r_coor.map(i => dense_geometry['additional_elements']['shadow_middle'].filter((value, index, array) => value['y1'] === i));
            let missin_shadows_by_row = r_coor.map(i => normal_geometry['additional_elements']['shadow_middle'].filter((value, index, array) => value['y1'] === i));
            let missing_shadows = [];
            for (let r_index = 0; r_index < row_amount; ++r_index) {
                v_by_row[r_index].forEach((v) => {
                    current_shadow_by_row[r_index] = current_shadow_by_row[r_index].filter(s => !(s['x1'] < v['x1'] && s['x2'] > v['x1']));
                    missing_shadows.push(...missin_shadows_by_row[r_index].filter(s => !(s['x2'] === v['x1'] - 9 || s['x1'] === v['x1'] + 9)));
                });
            }
            dense_geometry['additional_elements']['shadow_middle'] = [].concat.apply([], current_shadow_by_row);
            dense_geometry['additional_elements']['shadow_middle'].push(...missing_shadows);
            dense_geometry['verticals'].push(...missing_v);
        }

        // dense_geometry = this.styles_cleaner(dense_geometry, shelf_depth)
        dense_geometry = this.output_cleaner(dense_geometry, outputDataStructure)
        processBestShelfSplit(dense_geometry, shelf_type_name)
        return dense_geometry;
    },



};

module.exports.Rows = Rows;
//module.exports.Box = Box;
//module.exports.Legs = Legs;
//module.exports.Door = Door;
module.exports.MultitypeObject = MultitypeObject;
