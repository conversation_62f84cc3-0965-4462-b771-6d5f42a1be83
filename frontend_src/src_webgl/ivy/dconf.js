window.dConf = {

    rebuildWalls: function() {
        if (typeof ivy !== 'undefined') {
            ivy.buildFunctions.rebuildWalls();
        }
    },

    updateShelfTechnicalDetails: function() {
        if (typeof cstm_updateShelfTechnicalDetails !== 'undefined') {
            window.cstm_updateShelfTechnicalDetails();
        }
    },

    // Used to be for manipulating shelf verticals - left to right
    customModeOn: false,

    state: {

        isVisible: false, // Turns true on dimension-canvas-button click in PDP configurator

        setVisibility: function( boolean ) {
                dConf.state.isVisible = boolean;
        },

        toggleVisibility: function() {

            var $pdp = $('.pdp-product');
            var $canvas = $('.canvas-dimensions');
            var $shelfDetails = $('.shelf-details');

            if ( !$canvas.length || !$shelfDetails.length ) {
                return false;
            }

            dConf.state.isVisible = !dConf.state.isVisible;

            // Adds to pdp container, a class responsible for canvas-dimensions-related styles, while canvas-dimensions is in view.
            $pdp.toggleClass('dimensions-in-view');

            dConf.rebuildWalls();
            dConf.updateShelfTechnicalDetails();
            $canvas.fadeToggle();
            $shelfDetails.fadeToggle();
        }
    },

    line: { // same line width as in main ivy model - gets modified if needed, using dConf.modifier.zoom
        strokeWidth: 18,
        stroke: '#FFFFFF'
    },

    width: 0,
    height: 0,

    support: {
        fill: '#ebebeb'
    },

    font: {
        horizontal: '#000000',
        vertical: '#a9abae'
    },

    shelfOverflow: {
        flag: false,
        indicatorAppeared: false,
    },

    mirrorMode: false,

    maker: {

        Verticals: fabric.util.createClass( fabric.Line, {
            type: 'Verticals',
            initialize: function (points, options) {
                options || (options = {} );
                this.callSuper('initialize', points, options);
                this.set({
                    strokeWidth: dConf.modifier.getStrokeWidth(),
                    stroke: dConf.line.stroke,
                    hasBorders: false, // changes on mouse-down
                    padding: dConf.modifier.getStrokeWidth(),
                    borderColor: '#A9ABAE',
                    hasRotatingPoint: false,
                    hasControls: false,
                    lockScalingX: true,
                    lockScalingY: true,
                    lockRotation: true,
                    originX: 'center',
                    lockMovementY: false, // required for horizontal group-moving
                    lockMovementX: false, // required for horizontal group-moving and individual move
                    selectable: true // required for horizontal group-moving
                });
            },

            toObject: function () {
                return fabric.util.object.extend(this.callSuper('toObject'), {
                });
            },

            _render: function (ctx) {
                this.callSuper('_render', ctx);
            }
        }),

        Horizontals: fabric.util.createClass( fabric.Line, {
            type: 'Horizontals',
            initialize: function (points, options) {
                options || (options = {} );
                this.callSuper('initialize', points, options);
                this.set({
                    strokeWidth: dConf.modifier.getStrokeWidth(),
                    stroke: dConf.line.stroke,
                    hasBorders: false, // changes on mouse-down
                    padding: dConf.modifier.getStrokeWidth(),
                    borderColor: '#A9ABAE',
                    hasRotatingPoint: false,
                    hasControls: false,
                    lockScalingX: true,
                    lockScalingY: true,
                    lockRotation: true,
                    originY: 'center',
                    lockMovementY: false, // required for horizontal group-moving
                    lockMovementX: false, // required for horizontal group-moving and individual move
                    selectable: true // required for horizontal group-moving
                });
            },

            toObject: function () {
                return fabric.util.object.extend(this.callSuper('toObject'), {
                });
            },

            _render: function (ctx) {
                this.callSuper('_render', ctx);
            }
        }),

        Supports: fabric.util.createClass( fabric.Rect, {
            type: 'Supports',
            initialize: function (options) {
                options || (options = {} );
                this.callSuper('initialize', options);
                this.set({
                    fill: dConf.support.fill,
                    borderColor: '#ff3c00',
                    hasBorders: false, // change to true if selection-indicator needed
                    hasRotatingPoint: false,
                    hasControls: false,
                    lockScalingX: true,
                    lockScalingY: true,
                    lockRotation: true,
                    lockMovementY: false, // required for horizontal group-moving
                    lockMovementX: false, // required for horizontal group-moving and individual move
                    selectable: true // required for horizontal group-moving

                });
            },

            toObject: function () {
                return fabric.util.object.extend(this.callSuper('toObject'), {
                });
            },

            _render: function (ctx) {
                this.callSuper('_render', ctx);
            }
        })

    },

    customChanges: {
        verticals: {},
        horizontals: {},
        supports: {}
    },

    modifier: { // affects canvas size (zoom factor - 1 is normal 1:1 with ivy.points - gets very big. Zoom 4 is optimal
        zoom: 3.25, // default value which works well, gets modified with shelf's dimensions change - fn adjustZoom() - to fit the viewport
        containerPadding: 50, // adds space around shelf model on cavas - half of it is used as shelf offsetX and offsetY to center it
        containerWidth: 900, // gets changed with generateCanvas
        containerHeight: 800, // gets changed with generateCanvas
        shelfOverflowFlag: false, // gets changed when rendered canvas shelf is bigger than the viewport.
        getOffsetX: function () {
            return (dConf.modifier.containerWidth - (dConf.width / dConf.modifier.zoom) - this.containerPadding )  / 2; //removed -this.containerPadding from within the brackets
        },
        getOffsetY: function () {
            return (dConf.modifier.containerHeight - (dConf.height / dConf.modifier.zoom)  ) / 2; //removed -this.containerPadding from within the brackets
        },

        adjustZoom: function() {

            var zoomMax = 6.75; // last 5.75 //historically 4.5 - 7.5 - when incremented, bigger zoom can be achieved.
            var zoomMin = 1.75; // last 1.5

            var viewportWidth = (dConf.modifier.containerWidth - 2.5 * dConf.modifier.containerPadding);
            var viewportHeight = dConf.modifier.containerHeight; // Before: - 2.5 * ...

            // perfect zoom factors, to keep the shelf inside the viewport
            var maxWidthZoom = parseFloat( (dConf.width / viewportWidth).toFixed(2) );
            var maxHeightZoom = parseFloat( (dConf.height / viewportHeight).toFixed(2) );

            // Depending on the shelf size, matches correct 'zoom' factor so that the shelf fills the entire viewport.
            // When Shelf becomes extra wide, incremental zooming stops at certain point ( var zoomMin )
            dConf.modifier.zoom = Math.min( Math.max(maxWidthZoom, maxHeightZoom, zoomMin), zoomMax);
        },

        getStrokeWidth: function () {
            return 18 / this.zoom;
        },

        calculateAllCoords: function( coordinates ) {
            var calculatedArr = [
                dConf.modifier.calculateSingleCoordX( coordinates.x1 ),
                dConf.modifier.calculateSingleCoordY( coordinates.y1 ),
                dConf.modifier.calculateSingleCoordX( coordinates.x2 ),
                dConf.modifier.calculateSingleCoordY( coordinates.y2 )
            ];
            return calculatedArr;
        },

        calculateSingleCoordX: function( coordinate ) {
            return coordinate / dConf.modifier.zoom + dConf.modifier.getOffsetX();
        },
        calculateSingleCoordY: function( coordinate ) {
            return coordinate / dConf.modifier.zoom + dConf.modifier.getOffsetY();
        }

    },

    // for manipulating verticals - left-right
    listenerIsInitialized: false,
    editingEnabled: false,

    fn: {

        showNumbers: false,

        enableSelectionOfAll: function() {

            if ( dConf.customModeOn ) {

                window.dimensionsCanvas.set({
                    selectable: true,
                    hasBorders: false,
                    hasControls: false,
                    lockMovementY: false,
                    lockMovementX: false,
                    hoverCursor: 'ew-resize'

                });

                // Fabric iterates through all of the individual objects and adds specific offset to each object individually
                for (var i = 0; i < window.dimensionsCanvas.getObjects().length; i++) {

                    window.dimensionsCanvas.getObjects()[i]
                        .set({
                            selectable: true,
                            hasBorders: false,
                            hasControls: false,
                            lockMovementY: false,
                            lockMovementX: false,
                            hoverCursor: 'move'
                        })
                }

                // Needs to be re-rendered
                window.dimensionsCanvas.renderAll();
            }
        },

        // for manipulating verticals - left-right
        moveListeners: {

            initialized: false,
            // After canvas generates for the first time, it creates Listeners responsible for horizontal movement of the entire canvas group.

        },

        createShelfEventListeners: function() {

            // After shelf element is moved, on mouse-up,
            // updating element coordinates in dConf.coordinates object,
            // based on the coordinate diffefence between initial mouse-down and final mouse-up ( delta~ variable ).
            // If canvas has dConf.modifier.zoom, the delta~ variable is affected (multiplication by zoom~ variable)

            // setting vertical's color to red, on hover
            window.dimensionsCanvas.on('mouse:over', function(e) {
                if (e.target !== null) {

                    if (e.target.uniqueVerticals) {
                        e.target.set('stroke', '#ff3c00');
                        dConf.recentlyHoveredItem = e.target;

                        window.dimensionsCanvas.renderAll();
                    }
                }
            });

            window.dimensionsCanvas.on('mouse:out', function(e) {

                if ( dConf.recentlyHoveredItem ) {
                    dConf.recentlyHoveredItem.set('stroke', '#ffffff');
                    dConf.recentlyHoveredItem.set('hasBorders', false);
                    dConf.recentlyHoveredItem = '';

                    window.dimensionsCanvas.renderAll();
                }
            });

            window.dimensionsCanvas.on('mouse:down', function(e) {

                if (e.target !== null) {

                    // Saving coordinates of clicked shelf element
                    e.target.mouseDownX = e.target.left;
                    e.target.mouseDownY = e.target.top;

                    //initializing potential delta value
                    window.dConf.customChanges.verticals[ e.target.uniqueID ] =  { x1: 0, x2: 0 };
                }
            });

            window.dimensionsCanvas.on('mouse:up', function(e) {

                if (e.target !== null) {

                    e.target.setCoords();

                    var deltaX;
                    var deltaY;
                    var deltaSupportX;

                    // VERTICALS - left to right
                    if ( typeof e.target.uniqueVerticals !== 'undefined') {

                        deltaX = ( e.target.left - e.target.mouseDownX ) * dConf.modifier.zoom;
                        // var currentVertical = window.dConf.customChanges.verticals[ e.target.uniqueID ];
                        // currentVertical.x1 = currentVertical.x1 + deltaX;
                        // currentVertical.x2 = currentVertical.x2 + deltaX;
                    }

                    // HORIZONTALS - top to bottom
                    if ( typeof e.target.uniqueHorizontals !== 'undefined' ) {

                        deltaY = ( e.target.top - e.target.mouseDownY ) * dConf.modifier.zoom;
                        // var currentH = dConf.coordinates.horizontals[e.target.uniqueID];
                        // currentH.y1 = currentH.y1 + deltaY;
                        // currentH.y2 = currentH.y2 + deltaY;
                    }

                    // SUPPORTS - left to right
                    if ( typeof e.target.uniqueSupports !== 'undefined') {
                        var currentS = dConf.coordinates.supports[e.target.uniqueID];
                        deltaSupportX = ( e.target.left - e.target.mouseDownX ) * dConf.modifier.zoom;
                        currentS.x1 = currentS.x1 + deltaSupportX ;
                        currentS.x2 = currentS.x2 + deltaSupportX ;
                    }

                    /*  --- alternative moment for updating the ivy model - live, on every canvas-element change -- can cause lag
                    //updating the Ivy model
                        dConf.fn.loadShelfFromCanvas();
                    */
                }
            });

            // sets the init flag to true, need to be done only once per pdp load.
            dConf.listenerIsInitialized = true;

        }, // end of createShelfEventListeners();

        removeShelfEventListeners: function() {
            window.dimensionsCanvas.off('mouse:over');
            window.dimensionsCanvas.off('mouse:out');
            window.dimensionsCanvas.off('mouse:down');
            window.dimensionsCanvas.off('mouse:up');
        },

        saveCanvasAsPng: function(canvasId) {
            if (!window.localStorage) {
                alert("This function is not supported by your browser.");
                return;
            }
            window.open(document.getElementById(canvasId).toDataURL('png'));
        }

    } // end of fn

}; // end of dConf