class Node {
  constructor(posX, posY, level, direction) {
    this.uid = `${posY}|${posX}`;
    this.edges = [];
    this.posX = posX;
    this.posY = posY;
    this.direction = direction;
    this.isValid = false;
    this.isLastLevel = false;
    this.level = level;
    this.slices = this.calculateSlices(level);
  }

  visibilityCost (factor=0) {
    const distFromVisibiltyLine = this.posY - 1600;
    const cost = Math.pow(distFromVisibiltyLine, 2) * factor;

    if (this.direction === 'UP') {
      if (this.posY < 1600) return cost
      else return 0
    }
    if (this.direction === 'DOWN') {
      if (this.posY > 1600) return cost
      else return 0
    }
    if (this.direction === 'BOTH') {
      return -100
    }
  }

  calculateSlices (level) {
    const firstSlice = {
      start: level.start,
      end: this.posX,
      size: level.size - (this.posX - level.start),
    };
    const secondSlice = {
      start: this.posX,
      end: level.end,
      size: level.size - (level.end - this.posX),
    };
    return [firstSlice, secondSlice]
  }

  createEdge (node) {
    this.edges.push(node);
  }

  draw (func) {
    if (func) func(this);
  }

  traverse (func) {
    if (func) func(this);
    this.edges.forEach(n => n.traverse(func));
  }
}

class Level {
  constructor (posY, start, end, position, validateNodes=false) {
    this.posY = posY;
    this.start = start;
    this.end = end;
    this.size = end - start;
    this.position = position;
    this.allValid = validateNodes;
    this.nextLevel = null;
    this.nodes = [];
    this.compartmentEdges = [];
  }

  get validRange () {
    this.compartmentEdges.sort((a, b) => a - b);
    const lastIdx = this.compartmentEdges.length-2;
    return [this.compartmentEdges[1], this.compartmentEdges[lastIdx]]
  }

  get validNodes () {
    if (this.position === 'BOTTOM') return this.nodes.filter(n => n.isValid)
    return this.nodes.filter(n => n.posX > this.validRange[0] && n.posX < this.validRange[1] && n.isValid)
  }

  get nodesByRange () {
    this.compartmentEdges.sort((a, b) => a - b);
    const compartments = {};
    this.compartmentEdges.forEach((posY, idx) => {
      if (idx === 0 || idx === this.compartmentEdges.length-2) return
      if (idx < this.compartmentEdges.length-1) {
        const range = [posY, this.compartmentEdges[idx+1]];
        const rangeKey = `${range[0]}|${range[1]}`;
        compartments[rangeKey] = this.validNodes.filter(n => n.posX > range[0] && n.posX < range[1]);
      }
    });
    return compartments
  }

  setNextLevel (level) {
    this.nextLevel = level;
  }

  addNodes (verticals, direction) {
    verticals.forEach(v => {
      const sizeX = v.x2 - v.x1;
      const posX = v.x1 + (sizeX / 2);
      this.addNode(posX, direction);
    });
  } 

  addNode (posX, direction) {
    if (direction === 'UP') this.compartmentEdges.push(posX);

    const leftKey = `${this.posY}|${posX-9}`;
    if (this.isNodesContain(leftKey)) this.updateNode(leftKey, {key: 'direction', value: 'BOTH'});
    else this.createNode(posX-9, direction);
    
    const rigthKey = `${this.posY}|${posX+9}`;
    if (this.isNodesContain(rigthKey)) this.updateNode(rigthKey, {key: 'direction', value: 'BOTH'});
    else this.createNode(posX+9, direction);
  }

  createNode (posX, direction) {
    const slices = [posX - this.start, this.end - posX];
    const node = new Node(posX, this.posY, this, direction);
    this.nodes.push(node);
    if (slices.every(s => s <= 2400) || this.allValid) node.isValid = true;
  }

  updateNode (uid, payload) {
    const node = this.nodes.find(n => n.uid === uid);
    node[payload.key] = payload.value;
  }

  inRange(val, range) {
    return val > range[0] && val < range[1]
  }

  isNodesContain (uid) {
    if (this.nodes.find(n => n.uid === uid)) return true
  }
}

class Graph {
  constructor (shelf, doublePath=false) {
    this.origin = shelf;
    this.verticalsGeom = shelf.verticals.sort(this.orderByYX);
    this.horizontalsGeom = shelf.horizontals.sort(this.orderByYX);
    this.doublePath = doublePath;
    this.rootLevel = null;
    this.lastLevel = null;
    this.levels = {};
    this.tempEdges = [];
  }

  buildStructure () {
    this.buildLevels();
    this.buildNodes();
    this.buildEdges();
  }

  buildLevels () {
    this.horizontalsGeom.forEach((h, i)=> {
      let level = {};
      if (i === 0) 
        level = new Level(h.y1, h.x1, h.x2, 'UP', this.doublePath);
      else if (i === this.horizontalsGeom.length-1) 
        level = new Level(h.y1, h.x1, h.x2, 'BOTTOM', this.doublePath);
      else 
        level = new Level(h.y1, h.x1, h.x2, 'MIDDLE', this.doublePath);
      this.createOrUpdate(this.levels, h.y1, level);
    });
  }

  buildNodes () {
    this.verticalsGeom.forEach(v => {
      const sizeX = v.x2 - v.x1;
      const posX = v.x1 + (sizeX / 2);
      this.fillLevels(posX, v.y2+9, v.y1-9);
    });

    const levelKeysInOrder =  Object.keys(this.levels).sort((a, b) => parseInt(b) - parseInt(a));
    levelKeysInOrder.forEach((lvl, idx) => {
      if (idx < levelKeysInOrder.length-1) {
        const currents = this.levels[lvl];
        const nexts = this.levels[levelKeysInOrder[idx+1]];

        if (idx === 0) this.rootLevel = this.levels[lvl];

        if (nexts.length === 1) currents.forEach(lvl => lvl.setNextLevel(nexts));
        else currents.forEach(lvl => lvl.setNextLevel( nexts.filter(n => !(n.end < lvl.start || n.start > lvl.end)) ));
        
      } else {
        this.lastLevel = this.levels[lvl];
        this.levels[lvl].isLast = true;
        this.levels[lvl].forEach(lvl => lvl.nodes.forEach(n => n.isLastLevel = true));
      }
    });
  }

  buildEdges (func) {

    Object.keys(this.levels).forEach(lvlKey => {
      const allLevels = this.levels[lvlKey];
      allLevels.forEach(level => {
        const ranges = level.nodesByRange;
        Object.keys(ranges).forEach(range => {
          const [from, to] = range.split('|');
          const nextLevel = level.nextLevel.filter(lvl => lvl.start < from && lvl.end > to);

          let bottom = {};
          if (nextLevel.length === 1) {
            bottom = nextLevel[0];
          }
          else {
            const nextNextLevel = level.nextLevel.flatMap(l => l.nextLevel);
            bottom = nextNextLevel.filter(lvl => lvl.start < from && lvl.end > to)[0];
          }

          const topnNodes = ranges[range];
          const bottomNodes = bottom.nodes.filter(n => n.posX > from && n.posX < to && n.isValid);

          topnNodes.forEach(n => {
            bottomNodes.forEach(m => {
              if (func) func(n,m);
              n.createEdge(m);
              this.tempEdges.push([n, m]);
            });
          });
        });
      });
    });

  }

  getRoots () {
    return this.rootLevel.flatMap(lvl => lvl.validNodes)
  }

  getLastNodes () {
    return this.lastLevel.flatMap(lvl => lvl.validNodes)
  }

  getAllNodes () {
    return Object.values(this.levels).flatMap(n => n.flatMap(l => l.nodes))
  }

  findLevel (posX, posY) {
    const currentLevels = this.levels[posY];
    return currentLevels.find(l => l.start <= posX && posX <= l.end)
  }

  fillLevels (posX, top, bottom) {
    const topLvl = this.findLevel(posX, top);
    const bottomLvl = this.findLevel(posX, bottom);

    topLvl.addNode(posX, 'UP');
    bottomLvl.addNode(posX, 'DOWN');
  }

  orderByYX (firstPt, secondPt) {
    if (firstPt.y1 > secondPt.y1) return -1
    if (firstPt.y1 < secondPt.y1) return 1
    if (firstPt.x1 > secondPt.x1) return 1
    if (firstPt.x1 < secondPt.x1) return -1
    else return 0
  }

  createOrUpdate(struct, key, item) {
    if (struct.hasOwnProperty(key)) struct[key].push(item);
    else struct[key] = [item];
  }
}

class AStar {
  constructor (rootNode, visibilityFactor=0, lastNodes=[]) {
    this.root = rootNode;
    this.lastNodes = lastNodes;
    this.factor = visibilityFactor;

    this.visitedNodes = [];
    this.nodesToVisit = [rootNode];

    this.cameFromMap = {};
    this.gScoreMap = { [rootNode.uid]: 0 };
    this.finalCostMap = { [rootNode.uid]: this.heuristic(rootNode) };
  }

  isPathFinished (node) {
    return node.isLastLevel
  }

  isNodeVisited (node) {
    return this.visitedNodes.filter(n => n.uid === node.uid).length > 0
  }

  isNodeEvaluated (node) {
    return this.nodesToVisit.filter(n => n.uid === node.uid).length > 0
  }

  anyNodesToVisit () {
    return this.nodesToVisit.length > 0
  }

  findClosestNode (node) {
    let closest = null;
    let lowestDist = Infinity;
    
    this.lastNodes.forEach(n => {
      const dist = Math.abs(n.posX-node.posX);
      if (lowestDist > dist) {
        lowestDist = dist;
        closest = n;
      }
    });

    return closest
  }

  heuristic (node) {
    const closest = this.findClosestNode(node);
    const distance = Math.sqrt(Math.pow(closest.posX-node.posX, 2) + Math.pow(closest.posY-node.posY, 2));
    return distance + node.visibilityCost(this.factor)
  }

  distance (from, to) {
    return Math.abs(to.posX-from.posX)
  }

  getNodeWithLowestCost () {
    let idxOfLowestCost = 0;
    this.nodesToVisit.forEach((node, idx) => {
      const lowestCost = this.finalCostMap[this.nodesToVisit[idxOfLowestCost].uid];
      const currentCost = this.finalCostMap[node.uid];
      if (currentCost < lowestCost) idxOfLowestCost = idx;
    });
    return { idx: idxOfLowestCost, node: this.nodesToVisit[idxOfLowestCost] }
  }

  constructPath (node) {
    const totalPath = {
      nodes: [ node ],
      finalCost: this.finalCostMap[node.uid],
      isSuccess: true
    };

    let currentUID = node.uid;
    while (currentUID in this.cameFromMap) {
      let parentNode = this.cameFromMap[currentUID];
      totalPath.nodes.push(parentNode);
      totalPath.finalCost += this.finalCostMap[parentNode.uid];
      currentUID = parentNode.uid;
    }

    return totalPath
  }

  searchPath () {
    while (this.anyNodesToVisit()) {
      let current = this.getNodeWithLowestCost();

      if (this.isPathFinished(current.node)) return this.constructPath(current.node)
      
      this.nodesToVisit.splice(current.idx, 1);
      this.visitedNodes.push(current.node);

      current.node.edges.forEach(nextNode => {
        if (this.isNodeVisited(nextNode)) return
  
        const currentGScore = this.gScoreMap[current.node.uid];
        const temGScore = currentGScore + this.distance(current.node, nextNode);
  
        if (this.isNodeEvaluated(nextNode)) {
          const nextNodeGScore = this.gScoreMap[nextNode.uid];
          if (temGScore < nextNodeGScore) this.gScoreMap[nextNode.uid] = temGScore;
        } else {
          this.gScoreMap[nextNode.uid] = temGScore;
          this.nodesToVisit.push(nextNode);
        }
        
        this.cameFromMap[nextNode.uid] = current.node;
        this.finalCostMap[nextNode.uid] = this.gScoreMap[nextNode.uid] + this.heuristic(nextNode);
      });
    }
    return { nodes: [], finalCost: Infinity, isSuccess: false }
  }
}

class SimpleDivide {
  constructor (shelf) {
    this.verticalsGeom = shelf.verticals.sort(this.orderByYX);
    this.horizontalsGeom = shelf.horizontals.sort(this.orderByYX);
    this.allLevels = [];
  }

  search () {
    const start = this.horizontalsGeom[0].x1;
    const end = this.horizontalsGeom[0].x2;

    const verticalsLvl = {};
    this.verticalsGeom.forEach(v => {
      this.createOrUpdate(verticalsLvl, v.x1-9, [v.x1-9, v.y2+9, v.y1-9]);
      this.createOrUpdate(verticalsLvl, v.x1+9, [v.x1+9, v.y2+9, v.y1-9]);
    });

    const order = Object.keys(verticalsLvl).sort((a,b) => parseInt(a) - parseInt(b));

    const validVerticals = [];
    const rowsCount = this.horizontalsGeom.length - 1;
    
    order.forEach((key, idx) => {
      if (verticalsLvl[key].length === rowsCount) {
        validVerticals.push(verticalsLvl[key]);
      }
    });

    const allPaths = [];

    validVerticals.forEach((verticals, i) => {
      if (i > 2 && i < validVerticals.length-3) {
        const rootX = verticals[0][0];
        const isValidForSinglePath = [rootX - start, end - rootX].every(slice => slice < 2400);
        this.buildPath(allPaths, verticals, isValidForSinglePath);
      }
    });

    if (allPaths.length) return { allPaths, bestPaths: [ allPaths[0].nodes ] }
    
    validVerticals.forEach((verticals, i) => {
      if (i > 2 && i < validVerticals.length-3) {
        this.buildPath(allPaths, verticals, true);
      }
    });

    return this.chosePaths(allPaths)
  }

  chosePaths (allPaths) {
    const len = allPaths.length;
    
    if (len > 6) {
      return { allPaths, bestPaths: [ allPaths[1].nodes, allPaths[len-2].nodes ] }
    } else if (len <= 6 && len > 0) {
      return { allPaths, bestPaths: [ allPaths[0].nodes, allPaths[len-1].nodes ] }
    }
    return { allPaths: [], bestPaths: [] }
  }

  buildPath (allPaths, verticals, valid=false) {
    const path = { nodes: [], isSuccess: false };
    if (valid) {
      verticals.forEach((val, idx) => {
        const node = { posX: val[0], posY: val[1], uid: `${val[1]}|${val[0]}` };
        path.nodes.push(node);
  
        if (idx === verticals.length-1)
          path.nodes.push({posX: val[0], posY: val[2], uid: `${val[2]}|${val[0]}`});
      });
      path.isSuccess = true;
      allPaths.push(path);
    }
  }

  orderByYX (firstPt, secondPt) {
    if (firstPt.y1 > secondPt.y1) return -1
    if (firstPt.y1 < secondPt.y1) return 1
    if (firstPt.x1 > secondPt.x1) return 1
    if (firstPt.x1 < secondPt.x1) return -1
    else return 0
  }

  createOrUpdate(struct, key, item) {
    if (struct.hasOwnProperty(key)) struct[key].push(item);
    else struct[key] = [item];
  }
}

const processBestShelfSplit = (shelf, shelfType) => {
    let result = {};

    if (shelf.horizontals.some(validToSplit)) {
        if (shelfType === 'GRID' || shelfType === 'GRADIENT') {
            result = processSimpleDivide(shelf);
        } else if (shelfType === 'SLANT') {
            result = processGrpahSearch(shelf, 0.01);
        } else {
            result = processGrpahSearch(shelf, 0.001);
        }
        divideGeometry(shelf, result.bestPaths);
    }

    return result
};

const validToSplit = (horizontal) =>  Math.abs(horizontal.x2 - horizontal.x1) > 2400;

const processSimpleDivide = (shelf) => {
    const simpleSplitter = new SimpleDivide(shelf);
    return simpleSplitter.search()
};

const processGrpahSearch = (shelf, factor=0) => {
    const graph = buildGraph(shelf);
    const allPaths = calculatePaths(graph, factor);
    const bestPath = bestPathFromResult(allPaths);

    if (allPaths.length) return { graph, allPaths, bestPaths: [ bestPath ] }

    const graphForDouble = buildGraph(shelf, true);
    const allPathsForDouble = calculatePaths(graphForDouble, factor);
    const rootLevel = graphForDouble.rootLevel[0];
    const middlePosX = rootLevel.start + rootLevel.size/2;
    const bestPaths = bestTwoPathsFromResult(allPathsForDouble, middlePosX);

    return { graph: graphForDouble, allPaths: allPathsForDouble, bestPaths }
};
 
const buildGraph = (shelf, doublePath=false) => {
    const graph = new Graph(shelf, doublePath);
    graph.buildStructure();
    return graph
};

const calculatePaths = (graph, factor) => {
    let paths = [];

    const lastNodes = graph.getLastNodes();
    if (lastNodes.length) {
        graph.getRoots().forEach(r => {
            const astar = new AStar(r, factor, lastNodes);
            const path = astar.searchPath();
            if (path.isSuccess) paths.push(path);
        });
    }

    return paths
};

const bestTwoPathsFromResult = (paths, midPosX) => {
    let lowestLeftCost = Infinity;
    let bestLeftPath = {};

    let lowestRightCost = Infinity;
    let bestRightPath = {};

    paths.forEach(path => {
        let mostRight = -1 * Infinity;
        path.nodes.forEach(n => { if (n.posX > mostRight) mostRight = n.posX; });

        if (mostRight < midPosX) {
            if (path.finalCost < lowestLeftCost) {
                lowestLeftCost = path.finalCost;
                bestLeftPath = path.nodes;
            }
        } else {
            if (path.finalCost < lowestRightCost) {
                lowestRightCost = path.finalCost;
                bestRightPath = path.nodes;
            }
        }
    });

    return [ bestLeftPath, bestRightPath ]
};

const bestPathFromResult = (paths) => {
    const evaluatedResult = () => paths.sort((a, b) => a.finalCost - b.finalCost)[0];
    return paths.length === 0 ? [] : evaluatedResult().nodes
};

const divideGeometry = (shelf, paths=[]) => {
    const horizontals = [ ...shelf.horizontals ];

    paths.forEach(path => {
        path.forEach(node => {
            const matching = (h) => h.y1 === node.posY && h.x1 < node.posX && h.x2 > node.posX;
            const idx = horizontals.findIndex(matching);
    
            const firstHalf = { ...horizontals[idx] };
            firstHalf.x2 = node.posX;
    
            const secondHalf = { ...horizontals[idx] };
            secondHalf.x1 = node.posX;
    
            horizontals.splice(idx, 1, firstHalf, secondHalf);
        });
    });

    shelf.horizontals = horizontals;
};

export { bestPathFromResult, bestTwoPathsFromResult, buildGraph, calculatePaths, divideGeometry, processBestShelfSplit, processGrpahSearch, processSimpleDivide, validToSplit };
