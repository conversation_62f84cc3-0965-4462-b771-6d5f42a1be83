// TEMP - import new renderer class
import FurnitureProjectManager from '@libs/fpm-original';
import { initPricing } from '../pricingV3/pricing';
import { Renderer } from './renderer/renderer.js';

import { getDefinitionForMaterial } from '../presets/bundle.js';


// TEMP - inport new settings/presets provider

const FurnitureModel = require('../furnitureModel');

let pricing;

const touchDevice = ('ontouchstart' in window);

window.tracking = new (require('../configurator_analytics'))();
// window.dna_gen = new (require('./t2DnaGen'))();
window.dataLayer = window.dataLayer || [];
window.requestAnimFrame = window.requestAnimFrame || (function() {
  return window.requestAnimationFrame
        || window.webkitRequestAnimationFrame
        || window.mozRequestAnimationFrame
        || false;
}());

const callbackGridFlag = false;

// TEMP - test always on, ab_test

function creator(loader, required_build3d, callbackGrid) {
  PubSub.subscribe('initFinished', (msg, data) => {
    if (callbackGrid == false) {
      callbackGrid();
      callbackGrid = true;
    }
    PubSub.publish('mobileApply', () => { });
  });

  Array.prototype._distinctPoints = function() {
    let uniquePointsMap = [];
    const uniquePoints = [];
    for (let i = 0; i < this.length; i++) {
      if (uniquePointsMap.indexOf(`${this[i].x}-${this[i].y}-${this[i].z}`) == -1) {
        uniquePointsMap.push(`${this[i].x}-${this[i].y}-${this[i].z}`);
        uniquePoints.push(this[i]);
      }
    }
    uniquePointsMap = null;
    return uniquePoints;
  };

  let customization_was_sent = false;

  function send_customization() {
    if (customization_was_sent == false && timeout_for_mobile_track_customization == true) {
      customization_was_sent = true;
      Site.Track.trackCustomization();
    }
  }

  const isMobile = $('.webglconfigurator').hasClass('mobile');
  const cached_style_buttons = isMobile ? $('ul.row-door-params li, ul.row-drawer-params li') : $('ul.row-style-params li, ul.row-door-params li, ul.row-filter-params li');
  var timeout_for_mobile_track_customization = !isMobile;
  const rowHandlers = require('./rowHandlers');
  window.rowHandlers = rowHandlers;

  let newDnaList = [];


  const tweens = [];

  const elements = loader.getElements();
  const build3d = new (required_build3d)(elements, FurnitureProjectManager.dnaToolsAdapter(), send_customization, 'main', isMobile || window.is_tablet_loaded);

  const animations = new (require('./animations'))();

  const infoTable = $('.info-table').length > 0 ? $('.info-table')[0] : null;

  let camera; let scene; let
    renderer;
  let rendererProxy;
  let screenshotRenderer;
  let geometry; let material; let
    mesh;
  let shadow; let vertical; let
    horizontal;

  // $('#div-slider-popup').width() / $('#div-slider-popup').height()


  const pixelRatio = window.devicePixelRatio;
  const $pdpSlidesWrap = $('.pdp-slides-wrap');
  const $pdpSlides = $('.pdp-slides');
  const $mobileCanvas = $('#div-slider-popup canvas');
  let should_i_start_tweens = false;

  let cameraClass;

  // just for debugging purposes.

  const furniture = [];
  let pivot;
  let pivotAngle = -25 * Math.PI / 180;
  let startX; let
    startY;
  const apiServer = '';
  // var firstRunForSvg = true;
  let firstRunForCanvas = true;
  const canvasInitialized = false;
  let skipCreateBlobs = true;
  const actual_dna_name = '';
  let noFreeView = null;
  let joystick = null;

  // keep the scale of wood thickness
  const plankScale = 0.18; // TODO: to be removed

  let ivyShelf;
  let triggerClickFirst = true;
  let controlPositionNoTween = false;

  // mouse event variables for raycasting
  const projector = new THREE.Projector();
  const mouse_vector = new THREE.Vector3();
  const mouse = { x: 0, y: 0, z: 1 };
  const ray = new THREE.Raycaster(new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, 0, 0));
  let intersects = [];
  let intersectPlane;
  let rowBox; let rowBoxClone; let
    rowBoxHover;
  const handlers = [];
  // DELETE

  let positionRight = 0;
  let canvasAbsoluteWidth;
  let canvasAbsoluteHeight;

  const stopRendering = false;
  let canvasIsVisible = true;
  let showControls = false;
  let previous_selected_row = null;

  const default_row_styles = [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2];
  let previous_row_styles = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
  let use_default_row_styles = true;
  const row_styles_presets = -1;

  let row_a = 208;
  let row_b = 278;
  let row_c = 398;

  // temporary
  function switchType01HeightsToType02Heights(rows) {
    return rows.map(x => (x == 208 ? 200 : (x == 278 ? 300 : (x == 398 ? 400 : x))));
  }

  function getRowHeight(rowType) {
    if (rowType == 'a') {
      return row_a;
    } if (rowType == 'b') {
      return row_b;
    } if (rowType == 'c') {
      return row_c;
    }
  }

  row_a = 200;
  row_b = 300;
  row_c = 400;
  cstm.item.rows = switchType01HeightsToType02Heights(cstm.item.rows);


  let initialRun = true;
  let initialCounter = false;
  let saveforlaterIsVisible = false;

  function showSaveForLater() {
    if (initialCounter && !saveforlaterIsVisible) {
      $('.configurator_save_button').addClass('red-outline');
      saveforlaterIsVisible = true;
    }
  }

  // for scene
  window.sceneOn = false;
  const sceneGroup = null;
  const sceneSpace = {};
  const items = [];
  const itemsGroup = null;

  window.loadPresetFlag = false;

  // detects if browser supports webgl
  if (require('../webgl_detect')(1)) {
    // append loader animation and after loading all assets init + run animate
    if (!isMobile) {
      $pdpSlidesWrap.before('<div class="vertical-center loader-icon" style="position: absolute; top: 42%"><div class="vertical-center-content"><div class="spinner-loader-wrap"><div id="floatingCirclesG"><div class="spinner"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div></div></div></div></div></div>');
    }
    $('.loader-icon').hide();
    $('.loader-icon').fadeIn();
    loader.addOnLoadedEvent(loadDna);
    // loader.addOnLoadedEvent(animate);
    // loader.addOnLoadedEvent(rowHandlersSetup);
  } else if (!isMobile) {
    // if not mobile show warnings
    $('#webglFailOverlay').removeClass('visually-hidden');
    $('#furnitureName').addClass('visually-hidden');
    $('#description').addClass('visually-hidden');
    $('#temp_image').addClass('visually-hidden');
    $('.webglconfigurator').removeClass('visually-hidden');
    $('.nowebglconfigurator').addClass('visually-hidden');
    generateGui();
  } else {
    return false;
  }


  function map(arg, from1, to1, from2, to2) {
    return (arg - from1) / (to1 - from1) * (to2 - from2) + from2;
  }

  const localStorageSupported = isLocalStorageNameSupported();
  function loadDna() {
    // let dnas = new Promise()

    if (window.override_dnas != undefined) {
      newDnaList = window.override_dnas;

      init();
      // init grid/screenshot renderer

      animate();
      rowHandlersSetup();
    } else {
      $.ajax(`/shelf_dna/?${+new Date()}&shelf_type=${cstm.item.shelf_type}`, {
        contentType: 'application/json',
        dataType: 'json',
        method: 'get',
        success(resp) {
          newDnaList = resp;
          init();
          animate();
          rowHandlersSetup();
        },
      });
    }
  }

  async function init() {
    PubSub.subscribe('shelfChanged', (msg, data) => {
      if (ivyShelf) {
        assignPrice(ivyShelf.calculatePrice(data));
      }
      if (rendererProxy) {
        rendererProxy.bake();
      }
      showSaveForLater();
    });

    PubSub.subscribe('mobileApply', (msg, callback) => {
      ivyShelf.createBlobs(false, () => {
        callback();
      });
    });

    PubSub.subscribe('initFinished', (msg, data) => {
      ivyShelf.createBlobs();
      if (window.ivy.material !== 4 && window.ivy.material !== 5) {
        $('.configurator_addtocart_button').removeClass('button-disable');
        $('.configurator_save_button.button.text-center').removeClass('button-disable');
      }
      $('#row-button-wrapper').fadeIn();
      $('.loader-icon').fadeOut();
      PubSub.publishSync('shelfChanged');
      setTimeout(() => { initialCounter = true; }, 1000);
    });

    PubSub.subscribe('shelfChangedSnapped', (msg, data) => {
      assignPrice(ivyShelf.calculatePrice(data));
      ivy.changeDynamicDelivery();
      cstm_updateShelfTechnicalDetails();
      $('.shelf-card').removeClass('loaded');

      // set with and height in specification
      $('.height-value span.value').html(`${Math.floor(ivy.getHeight() / 10)} cm `);
      $('.width-value span.value').html(`${Math.floor(ivy.getWidth() / 10)} cm `);
      // ITS ONLY FOR CUSTOM EDITOR
      if (window.its_custom_editor) {
        const myIvy = ivy.generateJSON();
        $('.row-style-custom-config').html(`row_heigh: <br>${myIvy.rows}<br> row_styles: <br> ${myIvy.row_styles}`);
      }
      if (initialRun) {
        initialRun = false;
      } else {

      }
      if (rendererProxy) {
        rendererProxy.bake();
      }
    });
    PubSub.subscribe('rowChanged', (msg, data) => {
      console.log('row changed, ', data);
      // tutaj row availbity changes i takie tam
      // tutaj przeliczenia?
    });


    // Site.Track.sendDataLayer("webgl_finished_loading_ivy");


    $('.webglconfigurator').removeClass('visually-hidden');
    $('.nowebglconfigurator').addClass('visually-hidden');


    // DoorButtonOn = $('.button-on');
    // DoorButtonOff = $('.button-off');

    // append configurator holds
    for (let i = 1; i < 11; i++) {
      if (isMobile) {
        $pdpSlides.append(`<a href="#" id="row-${i}" class="configurator-hold"></a>`);
      } else {
        $pdpSlides.first().append(`<a href="#" id="row-${i}" class="configurator-hold"></a>`);
      }
    }

    var viewportHeight = parseFloat($(window).height()) - (parseFloat($('.header').height()) + parseFloat($('.tab-selection').height()) + parseFloat($('.configurator-nav').height()));
    // Setting canvas height and width;
    if (isMobile) {
      var viewportHeight = parseFloat($(window).height()) - (parseFloat($('.header').height()) + parseFloat($('.tab-selection').height()) + parseFloat($('.configurator-nav').height()));
      viewportHeight = parseFloat(viewportHeight * 0.65);
      $pdpSlidesWrap.parent().css('height', viewportHeight);
      $pdpSlides.css('height', viewportHeight);
      $pdpSlidesWrap.css('padding', 0);
      $pdpSlidesWrap.css('height', viewportHeight);
      // addMouseHandler(renderer.domElement);
      canvasAbsoluteHeight = viewportHeight;
      canvasAbsoluteWidth = $pdpSlides.width();
      $mobileCanvas.css('display', 'block');
      $mobileCanvas.fadeIn(1500);
    } else {
      // addMouseHandler(renderer.domElement);
      canvasAbsoluteHeight = $pdpSlides.width() * 0.75;
      $('.pdp-slides').height(canvasAbsoluteHeight);
      $('.pdp-slides-wrap').height(canvasAbsoluteHeight);
      canvasAbsoluteWidth = $pdpSlides.width();
    }

    cameraClass = new (require('./camera.js'))(canvasAbsoluteHeight, canvasAbsoluteWidth);
    window.cameraClass = cameraClass;

    // prepare base elements: scale, rotate, set transparency etc
    build3d.setShelfType(cstm.item.shelf_type);
    build3d.init3d();
    scene = build3d.getScene();
    camera = cameraClass.getCamera();


    //  var helper = new THREE.CameraHelper( camera );
    //  scene.add( helper );


    // temp
    //   window.camera = camera;
    window.scene = scene;
    ivyShelf = new createFurniture(cstm);
    ivyShelf.shelf_type = cstm.item.shelf_type;
    window.ivy = ivyShelf;
    window.ivyShelf = ivyShelf;

    const rendererProviders = {


    };

    // set proper renderer if retina display detected

    if (window.is_ie_loaded) {
      rendererProxy = new Renderer({
        renderScene: false,
        textures: loader.getElements(),
      }, camera, build3d, ivyShelf);
      renderer = rendererProxy.threeRenderer;
    } else if (pixelRatio && pixelRatio > 1) {
      // For Safari browser pixel ratio is always set to 1 even on retina display. Safari can't handle pixelratio >= 2
      rendererProxy = new Renderer({
        devicePixelRatio: pixelRatio || 1,
        renderScene: true,
        textures: loader.getElements(),
      }, camera, build3d, ivyShelf);

      renderer = rendererProxy.threeRenderer;

      renderer.setPixelRatio(pixelRatio || 1);
    } else {
      rendererProxy = new Renderer({
        renderScene: true,
        textures: loader.getElements(),
      }, camera, build3d, ivyShelf);
      renderer = rendererProxy.threeRenderer;
    }


    if (isMobile) {
      $('#div-slider-popup').append(renderer.domElement);
      // $('#div-slider').append(renderer.domElement);
      camera.aspect = $('#div-slider-popup').width() / (0.9 * viewportHeight);
      // $('.temp-custom').after(renderer.domElement);
      addMouseHandler(renderer.domElement);
    } else {
      $('#div-slider').append(renderer.domElement);
      addMouseHandler(renderer.domElement);
      // camera.position.y = -300;
      camera.aspect = canvasAbsoluteWidth / canvasAbsoluteHeight;
    }


    // Site.Track.sendDataLayer("webgl_start_ivy");
    renderer.setSize(canvasAbsoluteWidth, canvasAbsoluteHeight);
    // renderer.setClearColor(0xE6E6E6, 1);

    if (!isMobile) {
      renderer.setClearColor(0xececec, 1);
    } else {
      renderer.setClearColor(0xedf0f0, 1);
    }
    // create screenshotrenderer with proper dimensions after calculations
    screenshotRenderer = new THREE.WebGLRenderer({ antialias: (window.devicePixelRatio == 1 || !(cstm_i18n.user_agent.indexOf('safari') > -1 && cstm_i18n.user_agent.indexOf('chrome') == -1)), preserveDrawingBuffer: false, alpha: true });
    screenshotRenderer.setSize(canvasAbsoluteWidth * 2, canvasAbsoluteHeight * 2);

    pivot = cameraClass.getPivot();
    window.pivot = pivot;

    Site.Customize.setModel(window.ivy);

    // MOVED add pivot object with camera attached for easier rotation, aka camera on a stick.

    pivot.add(camera);
    // pivot.position.setY(map(ivyShelf.getHeight(), 300, 2300, 600, 1100));


    // TO DO Probably should be moved somewhere
    cameraClass.init(ivyShelf.getHeight());

    scene.add(pivot);
    pivot.rotation.set(0, pivotAngle, 0, 'YXZ');
    // scene.add(camera);


    cameraClass.setCameraInitialPosition(ivyShelf, true);

    renderer.domElement.id = 'configuratorCanvas';
    renderer.domElement.addEventListener('mousemove', onMouseHover, false);
    renderer.domElement.addEventListener('mousedown', e => {
      onMouseDown(e);
    }, false);

    addMouseHandler(renderer.domElement);

    // add raycasting plane;
    const geometry = new THREE.PlaneGeometry(10000, 10000, 32);
    const material = new THREE.MeshBasicMaterial({
      color: 0xffff00, side: THREE.DoubleSide, opacity: 0, transparent: true,
    });
    intersectPlane = new THREE.Mesh(geometry, material);
    intersectPlane.name = 'intersect plane';
    scene.add(intersectPlane);

    // add box for row detection
    const rowBoxGeometry = new THREE.BoxGeometry(500, 500, 500);
    const rowBoxMaterial = new THREE.MeshBasicMaterial({ color: 0xFF3C00, opacity: 0, transparent: true });
    rowBox = new THREE.Mesh(rowBoxGeometry, rowBoxMaterial);
    rowBoxClone = rowBox.clone();

    const rowBoxHoverGeometry = new THREE.BoxGeometry(500, 500, 505);
    if (window.render_test && !window.is_mobile_loaded) {
      var rowBoxHoverMaterial = new THREE.MeshBasicMaterial({ color: 0xFF3C00, opacity: 0.1, transparent: true });
    } else {
      var rowBoxHoverMaterial = new THREE.MeshBasicMaterial({ color: 0xFF3C00, opacity: 0.1, transparent: true });
    }

    rowBoxHover = new THREE.Mesh(rowBoxHoverGeometry, rowBoxHoverMaterial);
    rowBoxHover.renderOrder = 100000;
    window.rowBoxHover = rowBoxHover;


    generateGui();

    window.addEventListener('resize', onWindowResize, false);


    // append hold events
    $('.configurator-hold').on('click', function() {
      ivyShelf.selectedRow = $(this).index('.configurator-hold');
      if (ivy.constants.rowHeight[ivy.selectedRow] == row_a) {
        $('.row-door-params').removeClass('visible').addClass('hidden');
        $('.m-row-must-be-taller').removeClass('hidden').addClass('visilbe');
      } else {
        $('.row-door-params').removeClass('hidden').addClass('visible');
        $('.m-row-must-be-taller').removeClass('visible').addClass('hidden');
      }
      if (previous_selected_row == ivyShelf.selectedRow) {
        return false;
      }
      previous_selected_row = ivyShelf.selectedRow;

      // tween na drzwi
      if (should_i_start_tweens) {
        animations.set_tweens_for_row(ivyShelf.selectedRow, build3d.door_lists, build3d.drawers_lists, ivyShelf.depth);
      } else {
        should_i_start_tweens = true;
      }

      if (!isMobile) {
        $('.configurator-hold').removeClass('active');

        $(this).addClass('active');
        $('#row-selector-wrapper').css('top', $(this).css('top'));

        if (ivy.constants.rowHeight[ivy.selectedRow] == row_a && !(window.its_custom_editor)) {
          $('#row-selector-wrapper .row-style-params').removeClass('visible').addClass('hidden');
          $('#row-selector-wrapper .row-must-be-taller').removeClass('hidden').addClass('visible');
        } else {
          $('#row-selector-wrapper .row-style-params').removeClass('hidden').addClass('visible');
          $('#row-selector-wrapper .row-must-be-taller').removeClass('visible').addClass('hidden');
        }
      }
      let actual_row_top_height = 0;
      for (let i = 0; i <= ivy.selectedRow; i++) {
        actual_row_top_height += ivy.constants.rowHeight[i];
      }
      if (actual_row_top_height >= 1578 && !(window.its_custom_editor)) {
        $('.drawers-only-to-6-row, .m-drawers-only-to-6-row').removeClass('hidden').addClass('visible');
        $('#row-selector-wrapper .row-filter-params').removeClass('visible').addClass('hidden');
        $('.row-drawer-params').removeClass('visible').addClass('hidden');
      } else {
        $('.drawers-only-to-6-row, .m-drawers-only-to-6-row').removeClass('visible').addClass('hidden');
        $('#row-selector-wrapper .row-filter-params').removeClass('hidden').addClass('visible');
        $('.row-drawer-params').removeClass('hidden').addClass('visible');
      }

      // If 24cm is selected hide drawers option in side menu
      // and display a new message

      const currentDepth = Site.Customize.get('depth');

      if (currentDepth === 240 && !(window.its_custom_editor)) {
        $('.drawers-only-for-32-and-40, .m-drawers-only-for-32-and-40').removeClass('hidden').addClass('visible');
        $('.drawers-only-to-6-row, .m-drawers-only-to-6-row').removeClass('visible').addClass('hidden');
        $('#row-selector-wrapper .row-filter-params').removeClass('visible').addClass('hidden');
        $('.row-drawer-params').removeClass('visible').addClass('hidden');
      } else {
        $('.drawers-only-for-32-and-40, .m-drawers-only-for-32-and-40').removeClass('visible').addClass('hidden');
      }

      var eq;
      if (ivyShelf.constants.rowHeight[ivyShelf.selectedRow] == row_a) {
        eq = 0;
      } else if (ivyShelf.constants.rowHeight[ivyShelf.selectedRow] == row_b) {
        eq = 1;
      } else {
        eq = 2;
      }

      $('#row-selector .row-height-params li').removeClass('active');
      $('#row-selector ul.row-height-params').find(`li:eq(${eq})`).addClass('active');


      $('#row-selector .height-images li').removeClass('active');
      $('#row-selector ul.height-images').find(`li:eq(${eq})`).addClass('active');


      const actual_style = ivyShelf.constants.rowStyles[ivyShelf.selectedRow + 1] - 1;
      let actual_style_availability;
      actual_style_availability = build3d.row_styles_availability[ivyShelf.selectedRow];


      if (typeof actual_style_availability === 'undefined') {
        return;
      }

      if (cstm && cstm.custom_configurator) {
        $('ul.row-fillers > li.active').removeClass('active');
        const actualFillersStyle = ivyShelf.constants.rowStyles[ivyShelf.selectedRow];
        var eq = (actualFillersStyle % 10);
        $('ul.row-fillers').find(`[data-margin="${eq}"]`).parent().addClass('active');
        eq = Math.floor(actualFillersStyle % 100 / 10);
        $('ul.row-fillers').find(`[data-margin="${eq * 10}"]`).parent().addClass('active');
        eq = Math.floor(actualFillersStyle / 100);
        $('ul.row-fillers').find(`[data-margin="${eq * 100}"]`).parent().addClass('active');
      }


      if (cstm && cstm.dont_translate_rows) {
        var eq = (actual_style % 10);
        eq = eq == 0 ? 1 : eq;
        $('ul.row-style-params, ul.row-door-params').find(`li:eq(${eq})`).addClass('active');
        eq = Math.floor(actual_style % 100 / 10);
        eq = eq == 0 ? 1 : eq;
        $('ul.row-filter-params.first').find(`li:eq(${eq - 1})`).addClass('active');
        eq = Math.floor(actual_style / 100);
        eq = eq == 0 ? 1 : eq;
        $('ul.row-filter-params.second').find(`li:eq(${eq - 1})`).addClass('active');
      } else {
        rowHandlers.setRowAvailability(actual_style_availability, cached_style_buttons);
      }
      // if (isMobile){
      //     tracking.trackRowSelect(ivyShelf.selectedRow);
      // }
      if (isMobile) {
        // set proper hover on click
        scene.remove(rowBoxHover);
        const sizeBox = new THREE.Box3().setFromObject(rowBoxClone);
        const scale = ivyShelf.constants.rowHeight[ivyShelf.selectedRow] / sizeBox.size().y;
        let height = 0;
        for (var i = 0; i < ivyShelf.selectedRow; i++) {
          height += ivyShelf.constants.rowHeight[i];
        }
        height += ivyShelf.constants.rowHeight[i] / 2;
        const scaleX = ivyShelf.width / sizeBox.size().x;
        const scaleZ = (ivyShelf.depth * 2) / sizeBox.size().z;
        rowBoxHover.scale.setY(scale);
        rowBoxHover.scale.setX(scaleX);
        rowBoxHover.scale.setZ(scaleZ);
        rowBoxHover.position.setY(height);
        rowBoxHover.name = 'rowboxhover';
        if ($('a.tab-nav[data-target="row-heights"]').hasClass('active')) scene.add(rowBoxHover);
        if ($('a.tab-nav[data-target="row-doors"]').hasClass('active')) scene.add(rowBoxHover);
        if ($('a.tab-nav[data-target="row-drawers"]').hasClass('active')) scene.add(rowBoxHover);
        // ivyShelf.createBlobs(true);
      }

      // ivyShelf.setGenerateSVGText();
      // ivyShelf.setGenerateCanvasText();


      return false;
    });

    if (!isMobile) rowHandlers.hideShowHandlers(true);

    $('.js-custom-gui__carousel, .js-custom-gui__carousel_large').each(function() {
      if (this.slick) $(this).slick('setPosition');
    });


    $('#custom-depth-description').text(parseInt(ivyShelf.depth / 10));
    // moblie settings
    if (isMobile) {
      $('.custom-gui__param').hide();
      $('.tab.width').find('.custom-gui__param').show();
      // most of this is tabs functionality
      $('a.tab-nav').on('click', function(ev) {
        if (isMobile && ($(ev.target).parent().data('target') != 'row-heights' || $(ev.target).parent().data('target') != 'row-doors' || $(ev.target).parent().data('target') != 'row-drawers') && $('.configurator-hold').is(':visible')) {
          $('.configurator-hold').hide();
          showControls = false;
          scene.remove(rowBoxHover);
          // ivyShelf.createBlobs(true);
        }

        if (isMobile && ($(ev.target).parent().data('target') == 'row-heights' || $(ev.target).parent().data('target') == 'row-doors' || $(ev.target).parent().data('target') == 'row-drawers')) {
          noFreeView = false;
        } else {
          noFreeView = true;
        }


        $('.custom-gui__param').hide();

        const that = $(this);
        $(`.tab.${that.data('target')}`).find('.custom-gui__param').show();
        if ($('.button.edit-rows').is(':visible')) {
          $('.button.edit-rows').hide();
        } else {
          $('.configurator-hold').hide();
          $('.joystick-box').hide();
          showControls = false;
          // $('.pdp-slides canvas').css('opacity', 0);
          scene.remove(rowBoxHover);
          // ivyShelf.createBlobs(true);
        }
      });
      $('a.tab-nav[data-target="row-heights"], a.tab-nav[data-target="row-doors"], a.tab-nav[data-target="row-drawers"]').on('click', () => {
        // select height for a row from tab, set hover size to display nicely on changed height
        const sizeBox = new THREE.Box3().setFromObject(rowBoxClone);
        const scaleX = ivyShelf.width / sizeBox.size().x;
        let height = 0;
        for (var i = 0; i < ivyShelf.selectedRow; i++) {
          height += ivyShelf.constants.rowHeight[i];
        }
        height += ivyShelf.constants.rowHeight[i] / 2;
        rowBoxHover.scale.setX(scaleX);
        rowBoxHover.position.setY(height);
        rowBoxHover.name = 'hover na onclick';
        // show only once before editting the button telling to edit rows, get active only once, later dont show it anymore
        if ($('.button.edit-rows').length > 0 && !$('.button.edit-rows').hasClass('active') && $('.js-slider')[0].slick.currentSlide < 2) {
          $('.button.edit-rows').show().on('click', function() {
            $(this).addClass('active').fadeOut('fast');
            $('.configurator-hold').show();
            showControls = true;
            scene.add(rowBoxHover);
            return false;
          });
        } else {
          $('.joystick-box').show();
          scene.add(rowBoxHover);
        }
      });

      $('#div-slider-popup').append($(renderer.domElement));
      rendererProxy.bake();
    }

    // create screenshots
    $('#configurator_check_dna_button').click(() => {
      ivy.testThisJson(ivyShelf.createJson());
    });
    $('#configurator_find_errors_button').click(() => {
      ivy.testAllJson();
    });

    if (isMobile) {
      joystickCreate(canvasAbsoluteHeight);
      joystickBind(ivyShelf);
      disablePullDownToRefresh();
    }
    if (!isMobile) {
      // fade out temp shelf image
      // setMarginForCanvas();

    }
    $('.shelf-card').each(function(e) {
      const that = $(this);
      const price = window.ivy.getPriceFromData(that.data('shelf'));
      that.find('.price').text(price.priceGrossRegional + price.currencySymbol);
    });

    setTimeout(() => { timeout_for_mobile_track_customization = true; }, 5000);

    $('.camera-angle').on('click', event => window.ivy.changeAngle(event, -25 * Math.PI / 180));
    $('.camera-front').on('click', event => window.ivy.changeAngle(event, 0 * Math.PI / 180));

    pricing = await initPricing({ shelfType: ivyShelf.shelf_type }, window.location.origin);
    PubSub.publish('initFinished');
  }

  function resetFurniture(object) {
    ivyShelf = new createFurniture({ item: object });
  }

  const cameraPosition = new THREE.Vector3();
  // event listener
  function onMouseDown(event) {
    // stop any other event listener from recieving this event
    // this where begin to transform the mouse cordinates to three,js cordinates
    mouse.x = event.offsetX == undefined ? event.layerX : event.offsetX;
    mouse.y = event.offsetY == undefined ? event.layerY : event.of;
    // this vector caries the mouse click cordinates
    mouse_vector.set((mouse.x / canvasAbsoluteWidth) * 2 - 1, -(mouse.y / canvasAbsoluteHeight) * 2 + 1, 1);


    // the final step of the transformation process, basically this method call
    // creates a point in 3d space where the mouse click occurd
    projector.unprojectVector(mouse_vector, camera);

    // var cameraPosition = new THREE.Vector3();
    cameraPosition.setFromMatrixPosition(camera.matrixWorld);

    const direction = mouse_vector.sub(cameraPosition).normalize();

    // ray = new THREE.Raycaster( camera.position, direction );
    ray.set(cameraPosition, direction);

    // asking the raycaster if the mouse click touched the plane object
    intersects = ray.intersectObject(intersectPlane);
    // the ray will return an array with length of 1 or greater if the mouse click
    // does touch the sphere object
    if (intersects.length) {
      // check if inside furniture X boundaries
      if ((intersects[0].point.x > ivyShelf.constants.minX) && (intersects[0].point.x < ivyShelf.constants.maxX)) {
        if ((intersects[0].point.y > 0) && (intersects[0].point.y < ivyShelf.maxY())) {
          let min = 0;
          let max = 0;
          let i = 0;
          // calculate height by adding rows from the buttom till the clicked point
          for (i = 0; i < ivyShelf.constants.rowHeight.length; i++) {
            if (max < intersects[0].point.y) {
              max += ivyShelf.constants.rowHeight[i];
              if (i > 0) {
                min += ivyShelf.constants.rowHeight[i - 1];
              }
            } else {
              break;
            }
          }
          // mark the selected row
          ivyShelf.selectedRow = i - 1;
          $(`.configurator-hold:eq(${ivyShelf.selectedRow})`).trigger('click');
          return;
        }
      }
    }
    if (!window.cstm.is_mobile && $('.touch').length == 0) {
      ivyShelf.selectedRow = -1;
      previous_selected_row = -1;
    }
  }

  function onMouseHover(event) {
    // return;
    // stop any other event listener from recieving this event
    if (mouseDown) {
      return;
    }

    const tolerance = 250;
    mouse.x = event.offsetX == undefined ? event.layerX : event.offsetX;
    mouse.y = event.offsetY == undefined ? event.layerY : event.offsetY;

    // this vector caries the mouse click cordinates
    mouse_vector.set((mouse.x / canvasAbsoluteWidth) * 2 - 1, -(mouse.y / canvasAbsoluteHeight) * 2 + 1, 1);

    // the final step of the transformation process, basically this method call
    // creates a point in 3d space where the mouse click occurd

    const cameraPosition = new THREE.Vector3();
    cameraPosition.setFromMatrixPosition(camera.matrixWorld);

    projector.unprojectVector(mouse_vector, camera);
    const direction = mouse_vector.sub(cameraPosition).normalize();

    ray.set(cameraPosition, direction);

    // asking the raycaster if the mouse click touched the plane object
    intersects = ray.intersectObject(intersectPlane);
    // the ray will return an array with length of 1 or greater if the mouse click
    // does touch the sphere object
    if (intersects.length) {
      // check if inside furniture X boundaries
      if ((intersects[0].point.x > -1 * (ivyShelf.width / 2) - tolerance) && (intersects[0].point.x < (ivyShelf.width / 2) + tolerance)) {
        if ((intersects[0].point.y > 0) && (intersects[0].point.y < ivyShelf.maxY())) {
          if ($('#row-button-wrapper').is(':visible')) {
            $('#row-button-wrapper').fadeOut('fast');
          }
          scene.remove(rowBoxHover);
          let min = 0;
          let max = 0;
          let i = 0;
          // same as earlier, calculating height
          for (i = 0; i < ivyShelf.constants.rowHeight.length; i++) {
            if (max < intersects[0].point.y) {
              max += ivyShelf.constants.rowHeight[i];
              if (i > 0) {
                min += ivyShelf.constants.rowHeight[i - 1];
              }
            } else {
              break;
            }
          }

          min += plankScale * 100;
          // calculate sizes for the hover box accoring to the position
          const sizeBox = build3d.boundingBox.setFromObject(rowBoxClone);
          const scale = (max - min) / sizeBox.size().y;
          const scaleX = (ivyShelf.width + 3) / sizeBox.size().x;
          const scaleZ = (ivyShelf.depth) / sizeBox.size().z;
          // scale the initial object and set position
          rowBoxHover.scale.setY(scale);
          rowBoxHover.scale.setX(scaleX);
          rowBoxHover.scale.setZ(scaleZ);
          rowBoxHover.position.setY(max - (plankScale * 100) / 2 - (max - min) / 2);
          rowBoxHover.position.setZ(ivyShelf.depth / 2);
          rowBoxHover.material.opacity = window.render_test ? 0.1 : 0.1;
          scene.add(rowBoxHover);
          ivyShelf.selectedRow = i - 1;
          $(`.configurator-hold:eq(${ivyShelf.selectedRow})`).trigger('click');
          $('#row-selector').addClass('visible');
        } else {
          // if not in the furniture area remove the boxhover
          scene.remove(rowBoxHover);
          ivyShelf.selectedRow = -1;
          $('#row-selector').removeClass('visible');
        }
      } else if ((intersects[0].point.x < -1 * (ivyShelf.width / 2) - tolerance) || (intersects[0].point.x > (ivyShelf.width / 2) + tolerance)) {
        scene.remove(rowBoxHover);
        $('#row-selector').removeClass('visible');
        ivyShelf.selectedRow = -1;
      }

      if (ivyShelf.selectedRow == -1) {
        animations.set_tweens_for_row(ivyShelf.selectedRow, build3d.door_lists, build3d.drawers_lists, ivyShelf.depth);
        previous_selected_row = ivyShelf.selectedRow;
      }
    }
  }


  if (!isMobile) {
    // if not mobile add extra functionality

    $('#row-selector').hover(() => {
      scene.add(rowBoxHover);
    });

    $('#row-selector').mouseleave(function() {
      scene.remove(rowBoxHover);
      $(this).removeClass('visible');
    });
  }


  function animate(time) {
    TWEEN.update(time);

    // rendererProxy.bake();
    renderer.render(scene, camera);
    // renderer.clearDepth();
    // softRenderer.render();
    skipCreateBlobs = false;
    window.requestAnimFrame(animate);
  }

  function setRowHandlerPosition() {
    const screenVector = new THREE.Vector3();
    for (let i = 1; i < 11; i++) {
      rowHandlers.getCoordinates(build3d.handlers[i - 1], i, camera, renderer, `row-${i}`, ivyShelf, positionRight, isMobile, screenVector);
    }
  }

  function rowHandlersSetup() {
    // update handlers position
    $('.webglconfigurator').delegate('#configuratorCanvas', 'mouseover', setRowHandlerPosition);


    if (isMobile) {
      setRowHandlerPosition();
    }

    // this needs to get triggered only on the first frame to set the proper position for row height selection box, creating screenshots etc
    if (triggerClickFirst) {
      $(`.configurator-hold:eq(${ivyShelf.selectedRow})`).addClass('active');
      $('.configurator-hold.active').trigger('click');
      triggerClickFirst = false;
      $('#row-selector-wrapper').css('left', parseFloat($('.configurator-hold').css('left')) - 90);
      if (isMobile) {
        scene.remove(rowBoxHover);
        skipCreateBlobs = false;
        // ivyShelf.createBlobs();
        screenshotRenderer.render(scene, camera);
      }
    }
  }

  function onWindowResize() {
    positionRight = 0;
    $('#row-selector-wrapper').css('top', $('.configurator-hold.active').css('top'));
    if (!isMobile) {
      canvasAbsoluteHeight = $pdpSlides.width() * 0.75;
    }
    $('.pdp-slides').height(canvasAbsoluteHeight);
    $('.pdp-slides-wrap').height(canvasAbsoluteHeight);
    canvasAbsoluteWidth = $pdpSlides.width();
  }

  var mouseDown = false;
  let mouseX = 0;
  let mouseY = 0;


  // mouse moving camera rotation events
  function onMouseMoveCamera(evt) {
    if (!mouseDown) {
      return;
    }
    if (noFreeView === false) {
      return;
    }
    evt.preventDefault();
    if (pivot.rotation.y != pivotAngle) {
      rowHandlers.hideShowHandlers(false);
      $('#row-selector').removeClass('visible');
      $('#rotate-ico').hide();
    }
    if (isMobile) {
      const touch = evt.touches[0];
      var deltaX = touch.clientX - mouseX;
      var deltaY = touch.clientY - mouseY;
      mouseX = touch.clientX;
      mouseY = touch.clientY;
    } else {
      var deltaX = evt.clientX - mouseX;
      var deltaY = evt.clientY - mouseY;
      mouseX = evt.clientX;
      mouseY = evt.clientY;
    }
    rotateScene(deltaX, deltaY);
  }

  function onMouseDownCamera(evt) {
    TWEEN.removeAll();
    tweens.length = 0;

    evt.preventDefault();

    mouseDown = true;
    if (isMobile) {
      const touch = evt.touches[0];
      mouseX = touch.clientX;
      mouseY = touch.clientY;
    } else {
      mouseX = evt.clientX;
      mouseY = evt.clientY;
    }

    // Each 'Change-angle icon' click affect these.
    startY = parseFloat(pivot.rotation.y);
    startX = parseFloat(pivot.rotation.x);
  }

  // when mousebutton up reset the camera to initial state
  function onMouseUpCamera(evt) {
    mouseDown = false;
    if (!!cstm.options && cstm.options.block_camera) {
      return;
    }

    let tweenCamera;
    // abtest
    if (!window.is_mobile_loaded) {
      if (pivot.rotation.y < (-25 * Math.PI / 180)) {
        startY = -25 * Math.PI / 180;
      } else if (pivot.rotation.y >= (-25 * Math.PI / 180) && pivot.rotation.y <= (25 * Math.PI / 180)) {
        startY = 0;
      } else {
        startY = 25 * Math.PI / 180;
      }
    }

    tweenCamera = new TWEEN.Tween({ x: pivot.rotation.x, y: pivot.rotation.y })
      .to({ x: startX, y: startY }, 300)
      .easing(TWEEN.Easing.Quadratic.Out)
      .onUpdate(function() {
        pivot.rotation.x = this.x;
        pivot.rotation.y = this.y;

        // tempcam();
      })
      .onComplete(() => {
        tweens.length = 0;
      });
    tweens.push(tweenCamera);
    startTween();

    if (pivot.rotation.y != pivotAngle && !isMobile) {
      rowHandlers.hideShowHandlers(true);
    }
    document.removeEventListener('mousemove', onMouseMoveCamera, false);
    document.removeEventListener('mouseup', onMouseUpCamera, false);
  }

  function addMouseHandler(canvas) {
    if (isMobile) {
      canvas.addEventListener('touchstart', e => {
        onMouseDownCamera(e);


        document.addEventListener('touchmove', onMouseMoveCamera, false);
        document.addEventListener('touchend', onMouseUpCamera, false);
      }, false);
    } else {
      canvas.addEventListener('mousedown', e => {
        onMouseDownCamera(e);

        document.addEventListener('mousemove', onMouseMoveCamera, false);
        document.addEventListener('mouseup', onMouseUpCamera, false);
      }, false);
    }
  }

  function rotateScene(deltaX, deltaY) {
    pivot.rotation.y -= deltaX / 125;
    pivot.rotation.x -= deltaY / 125;
    // set maximum angles for rotation

    if (!sceneOn) {
      if (pivot.rotation.x > 0) {
        pivot.rotation.x = 0;
      }

      if (pivot.rotation.x < (-45 * Math.PI / 180)) {
        pivot.rotation.x = -45 * Math.PI / 180;
      }

      if (pivot.rotation.y > 60 * Math.PI / 180) {
        pivot.rotation.y = 60 * Math.PI / 180;
      }

      if (pivot.rotation.y < (-60 * Math.PI / 180)) {
        pivot.rotation.y = -60 * Math.PI / 180;
      }
    } else {
      if (pivot.rotation.x > 0) {
        pivot.rotation.x = 0;
      }

      if (pivot.rotation.x < (-20 * Math.PI / 180)) {
        pivot.rotation.x = -20 * Math.PI / 180;
      }

      if (pivot.rotation.y > 0 * Math.PI / 180) {
        pivot.rotation.y = 0 * Math.PI / 180;
      }

      if (pivot.rotation.y < (-50 * Math.PI / 180)) {
        pivot.rotation.y = -50 * Math.PI / 180;
      }
    }

    // tempcam();
  }


  function tempcam() {
    camera.position.x = pivot.rotation.y * 5000;
    camera.position.y = -pivot.rotation.x * 5000;
    camera.lookAt(new THREE.Vector3(0, 0, 0));

    const ivySize = window.ivysize();

    camera.position.y = ivySize[1];
  }

  // fire all tweens
  function startTween() {
    for (let i = 0; i < tweens.length; i++) {
      tweens[i].start();
    }
  }

  function createFurniture(object) {
    FurnitureModel.call(this, renderer, scene, camera);

    const self = this;
    self.project_id = '04';
    self.type = 'jetty';

    self.id = object.item.id;
    self.data = object.item;
    self.preset = object.item.preset;
    self.index = 0;
    self.pattern = 0;
    self.Property1 = 0.45;
    self.width = 0;
    self.rows = 0;
    console.log('create material, ', self.data.material);
    self.material = self.data.material;
    self.selectedRow = 0;
    self.selectedRowHeight = 27.7;
    self.handlers = handlers;

    if (self.preset) {
      self.base_preset = object.item.id;
    }
    if (object.item.base_preset) {
      self.base_preset = object.item.id;
    }
    self.testThisJson = function(dna_result, callback, error_callback, motion, width, row) {
      motion = motion || self.Property1;
      row = row || self.rows;
      width = width || self.width;
      $.ajax('/api/v1/dna_check', {
        data: JSON.stringify({
          output: {
            verticals: dna_result.verticals,
            supports: dna_result.supports,
            horizontals: dna_result.horizontals,
          },
          dna_name: 'wlasne',
          motion,
          width,
          rows: row,
          rows_list: self.constants.rowHeight,
          dna_id: window.custom_dna_id,
        }),
        contentType: 'application/json',
        dataType: 'json',
        method: 'post',
        async: false,
        success(resp) {
          if (callback == undefined) {
            /*
                        generateSVG(self.width, self.getHeight(), self.points.horizontals, self.points.verticals, self.points.supports, true,
                            {
                                iterable_item_added:resp.iterable_item_added,
                                iterable_item_removed:resp.iterable_item_removed
                            });
                        */

            // console.log('================= GenerateCanvas Call -- fn self test this json==================');
            // generateCanvas(self.width, self.getHeight(), self.points.horizontals, self.points.verticals, self.points.supports);

          } else {
            callback(resp);
          }
        },
        error() {
          console.log('FAIL');
          if (error_callback != undefined) {
            error_callback();
          }
        },
      });
    };
    self.testAllJson = function() {
      // dnaTools.get_elements(self.patterns[self.pattern].json, self.Property1/100.0, self.width, self.rows, self.constants.rowHeight, self.constants.rowStyles, self.depth);
      const number_of_rows = 8;
      const width_in_mm = 1000;
      const motion_to_100 = 0;
      const row_heights = self.constants.rowHeight;
      const row_styles = self.constants.rowStyles;
      const { depth } = self;
      console.log(`start for ${100 * (240 - 70) * 8}`);
      console.time();
      let stop_this = false;
      const initial_run = true;
      for (let motion = initial_run ? self.Property1 : 0.0; motion <= 100 && !stop_this; motion++) {
        for (let width = 700; width <= 2400; width += 10) {
          for (let row = 1; row <= 8 && !stop_this; row++) {
            const dna_result = self.getDnaTool.get_elements(self.patterns[self.pattern].json, motion, width, row, row_heights, row_styles, depth);
            if (stop_this) {
              return -1;
            }
            console.log('Check for motion, width, row', motion, width, row);
            self.testThisJson(dna_result, resp => {
              if (resp.errors == true) {
                console.log('Found errors on ', resp.motion, resp.width, resp.rows, resp);
                alert(`Znaleziono błędy dla Motion ${resp.motion}, width: ${resp.width} i rows: ${resp.rows}`);
                stop_this = true;
              }
            }, () => {
              stop_this = true;
            }, motion, width, row);
          }
        }
      }
      console.log('end');
      console.timeEnd();
    };

    self.createJson = function() {
      return self.generateJSON();
    };

    self.readJson = function(jsonString) {
    };


    self.constants = {
      minX: 0,

      maxX: 250,

      gradientSupportWidth: 125,

      // material width
      borderWidth: 1.8,

      // min support spacing

      minSupportSpacing() {
        return self.constants.gradientSupportWidth + 125 + self.constants.borderWidth;
      },

      // supports - shift to front [mm]

      supportsShift: 2,

      minDistToPlaceSupport() {
        return parseInt(self.constants.minDist * 1.4 + self.constants.borderWidth);
      },
    };


    self.patterns = [...newDnaList];

    self.show_canvas = function() {
      if (isMobile) {
        return;
      }
      if (canvasIsVisible) {

      } else {
        $('.pdp-slides canvas').fadeTo(2, 1);
        $('.ivy-slider-angle img').fadeTo(2, 0);
        $('.ivy-slider-front img').fadeTo(2, 0);
        canvasIsVisible = true;
      }
    };

    // configurator setters

    self.setWidth = function(value) {
      if (isMobile) {
        // if mobile and on further slides, come back to the first slide
        self.show_canvas();
      }
      controlPositionNoTween = true;
      rowHandlers.hideShowHandlers(false);
      self.width = Math.round(value) * 10;
      build3d.width = self.width;

      self.buildFunctions.rebuildWalls(false, true);
      build3d.createCastShadows(self.width);
      tracking.trackWidth();
    };

    self.getWidth = function() {
      return self.width;
    };

    self.getProperty = function() {
      return self.Property1;
    };

    self.getDepth = function() {
      return parseInt(self.depth / 10);
    };

    self.getPrice = function() {
      return self.calculatePrice(self.points)[0];
    };

    self.getPriceFromData = function(data) {
      return self.calculatePrice(build3d.get_only_points(ivyShelf, data.pattern, data.motion, data.width, data.height, data.row_height, data.row_styles), data.color, data.width * 10, data.height);
    };

    /*
        self.setPrice = function(){
            assignPrice(self.calculatePrice(self.generatedWalls));
            return true;
        };
        */

    self.setHeight = function(value) {
      if (isMobile) {
        // if mobile and on further slides, come back to the first slide
        self.show_canvas();
      }
      controlPositionNoTween = true;
      $('#ivy-height').val(getDisplayHeight(value, self.constants.rowHeight));
      self.rows = value;
      self.selectedRow = 0;
      $(`.configurator-hold:eq(${self.selectedRow})`).trigger('click');

      self.buildFunctions.rebuildWalls();
      positionRight = 0;
      tracking.trackHeight();
    };

    self.setDepth = function(value) {
      value *= 10;
      self.depth = parseInt(value / 10) * 10;
      build3d.depth = self.depth;
      build3d.createCastShadows(self.width);
      rowHandlers.hideShowHandlers(false);
      $('#custom-depth-description').text(parseInt(self.depth / 10));
      self.changeDynamicDelivery();
      animations.drawer_depth = self.depth;
      self.buildFunctions.rebuildWalls(false, true);
      self.createBlobs();
      tracking.trackDepthChange(self.depth);
    };

    self.setBackpanels = function(value) {
      if (value) {
        self.backpanel_styles = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      } else {
        self.backpanel_styles = self.data.backpanels_styles;
      }
      self.buildFunctions.rebuildWalls(false, true);
      self.createBlobs();
      tracking.trackBackpanelsChange(value);
    };

    // TODO: to be moved to build3d/
    self.changeDynamicDelivery = function() {
      if (!cstm.delivery) return;
      let extended_delivery_times = (build3d.points.doors && build3d.points.doors.length > 0) ? 1 : 0;
      if (build3d.points.drawers && build3d.points.drawers.length > 0) {
        extended_delivery_times = 2;
      }
      const delivery_time = cstm.delivery[extended_delivery_times][self.material];
      $('.delivery_time .delivery_time_text').html(cstm_i18n.delivery_dynamic_text.replace('{0}', delivery_time.min).replace('{1}', delivery_time.max));
    };

    self.toggleShadowCast = function() {
      build3d.toggleShadowCast();
    };


    self.setColor = (_color, tracking) => {
      const [, colorName] = getDefinitionForMaterial(_color, self.shelf_type | self.data.shelf_type);
      PubSub.publish('changeColorDna', { materialName: colorName.toLowerCase() });
      Promise.all(loader.loadColorTextures(colorName, window.cstm.item.shelf_type)).then(() => {
        // self.texture = color;
        self.material = _color;
        self.materialId = _color; // getByMat
        build3d.setMaterialColor({ color: _color, shelf_type: window.cstm.item.shelf_type, tracking });
        self.materialHasChanged(colorName, tracking);
        showSaveForLater();
      });
    };

    self.materialHasChanged = function(color, skipTracking) {
      if (isMobile) {
        // if mobile and on further slides, come back to the first slide
        self.show_canvas();
      }

      self.changeDynamicDelivery();

      // change screenshots based on color
      let path = null;
      let pathDrawers = null;
      path = '/r_static/material-zoom/IVY/doors/';
      pathDrawers = '/r_static/material-zoom/IVY/drawers/';
      const abTest = true;
      let ext;
      if ($('.webglconfigurator').hasClass('temp') || $('.webglconfigurator').hasClass('mobile')) {
        ext = 'cu-';
      } else {
        ext = 'closeup-ivy-';
      }
      if (color == 'maple') {
        color = 'wood';
      }
      path = '/r_static/material-zoom/IVY/clean/';

      $('.material-zoom-material').each(function() {
        $(this).attr('src', `${path}min/m-material_${color}.jpg`);
      });

      $('.material-zoom-leg').each(function() {
        $(this).attr('src', `${path}min/m-leg_${color}.jpg`);
      });

      $('.material-zoom-leg-doors').each(function() {
        $(this).attr('src', `${path}min/m-handles_${color}.jpg`);
      });

      $('.material-zoom-drawer-doors').each(function() {
        $(this).attr('src', `${path}min/m-drawer_${color}.jpg`);
      });

      $('.material-material').each(function() {
        $(this).attr('src', `${path}material_${color}.jpg`);
      });

      $('.material-leg').each(function() {
        $(this).attr('src', `${path}leg_${color}.jpg`);
      });

      $('.material-leg-doors').each(function() {
        $(this).attr('src', `${path}handles_${color}.jpg`);
      });

      $('.material-drawer-doors').each(function() {
        $(this).attr('src', `${path}drawer_${color}.jpg`);
      });

      // change visible text info
      $('.ivy-material-info').find('.text-info').each(function() {
        $(this).removeClass('visually-hidden');
        if (!$(this).hasClass(color)) {
          $(this).addClass('visually-hidden');
        }
      });
      if (!window.loadPresetFlag) {
        if (!isMobile) self.createBlobs();
      }
      if (!skipTracking) {
        tracking.trackColorChange(color);
        send_customization();
      }

      if (!window.loadPresetFlag) {
        assignPrice(self.calculatePrice(build3d.points));
      }
    };

    self.setCamera = function(value) {
      if (window.loadPresetFlag === false) {
        cameraClass.setCameraInitialPosition(self, false);
        self.buildFunctions.rebuildWalls(false, true);
        positionRight = null;
        tracking.trackEnd(self);
      }
    };

    self.setTitle = function(value) {
      self.title = value;
    };

    self.setPattern = function(value) {
      if (isMobile) {
        self.show_canvas();
      }
      if (value < self.patterns.length) {
        self.pattern = value;
      } else {
        self.pattern = 0;
      }
      self.buildFunctions.rebuildWalls(false, true);
      // self.createBlobs();
      // self.setGenerateSVGText();
      tracking.trackDnaChange(self.actual_dna_name);

      showSaveForLater();
    };

    self.setProperty = function(value) {
      if (isMobile) {
        self.show_canvas();
      }
      self.Property1 = value;
      self.buildFunctions.rebuildWalls();
      tracking.trackProperty();
    };
    // get height by adding rows
    self.getDnaTool = function() {
      return FurnitureProjectManager.dnaToolsAdapter();
    };
    self.getWeight = function() {
      return 0;
    };
    self.getHeight = function(limitHeight, rowsList) {
      if (limitHeight !== undefined) {
        // old way of calculating for dna and not know why it looks like this
        var tmp = 0;
        for (var i = 0; tmp < limitHeight; i++) {
          if (Math.abs(tmp + rowsList[i] - limitHeight) < Math.abs(tmp - limitHeight)) {
            tmp += rowsList[i];
          } else {
            break;
          }
        }
        return tmp;
      }

      var tmp = 18;
      for (var i = 0; i < self.rows; i++) {
        tmp += self.constants.rowHeight[i];
      }
      return tmp + 12; // added footers
    };

    self.getCompartmentCapacity = function() {
      return build3d.compartmentCapacity;
    };

    self.getCapacity = function() {
      return build3d.capacity;
    };


    self.changeAngle = function(ev, angle) {
      // change camera by setting angle, tweening for desktop
      if (!isMobile) {
        $('.camera-angle').removeClass('active');
        $(ev.target).parent().addClass('active');
        pivotAngle = parseFloat(angle);
        rowHandlers.hideShowHandlers(false);
        const tweenCamera = new TWEEN.Tween({ y: pivot.rotation.y })
          .to({ y: pivotAngle }, 300)
          .easing(TWEEN.Easing.Quadratic.Out)
          .onUpdate(function() {
            pivot.rotation.y = this.y;
          })
          .onComplete(() => {
            tweens.length = 0;
            rowHandlers.hideShowHandlers(true);
          });
        tweens.push(tweenCamera);
        startTween();
      } else {
        pivot.rotation.y = parseFloat(angle);
      }
    };

    self.createRenderForBar = function() {
      return renderer.domElement.toDataURL('image/png');
    };

    // create screenshots
    self._createBlobs = function(leaveUi, onPreloadFinished) {
      console.log('createBlobs');
      if (skipCreateBlobs) {
        console.log('skipped createBlobs');
        return;
      }

      function getRenders(settings) {
        let renders;
        renders = window.ivyScreenshot.createBlobs(
          self.getShelf(),
          true,
          settings,
        );

        return renders;
      }


      function appendRenders() {
        let renders;

        if (isMobile) {
          // OFF SCREEN
          if (rowBoxHover) {
            scene.remove(rowBoxHover);
            noFreeView = true;
          }
          renderer.render(scene, camera);
          // SCREEN FROM VISIABLE CANVAS - WITH SCENE
          const screenshot = renderer.domElement.toDataURL('image/png');

          // alert(screenshot.length + " / " + isMobile + renderer.domElement);
          if (onPreloadFinished) {
            // alert("pre");
            const preloadImage = new Image();
            preloadImage.addEventListener('load', () => {
              $('.temp-custom').attr('src', screenshot);
              onPreloadFinished();

              setTimeout(() => {
                renders = self.getRenders([
                  [0, 800, 600, 0xffffff, 0, 800 / 600],
                ]);
                $('#specification .temp-custom-2').attr('src', renders[0]);
                $('.save-furniture-popup-screenshot').attr('src', renders[0]);
              }, 0);
            });
            preloadImage.src = screenshot;
          } else {
            $('.temp-custom').attr('src', screenshot);
          }
        } else {
          // OFF SCREEN
          renders = self.getRenders([
            [-40, 160, 160, 0xffffff, 0, 1],
            [0, 300, 300, 0xedf0f0, 1, 1],
            [0, 800, 600, 0xffffff, 0, 800 / 600],
          ]);

          // append render for angle view
          $('.ivy-view-angled').attr('src', renders[0]);
          window.shelfPreviewSrc = renders[0];

          // append render for spec
          $('#specification .temp-custom-2').attr('src', renders[1]);

          if (localStorageSupported) {
            // Create  render for spec exit pop, save4later popup
            const json = self.generateJSON();
            delete json.id;

            json.magic_preview = renders[2].split(',')[1];
            localStorage.setItem('screenshot', renders[2]);
            localStorage.setItem('json', JSON.stringify(json));
          }

          const saveFurniturePopupNouser = $('.popup-product_save_nouser');
          const saveFurniturePopup = $('.popup-product_save');

          if (saveFurniturePopupNouser.length > 0) {
            const imgContainer = saveFurniturePopupNouser.find('.img-container');
            const img = document.createElement('img');
            img.setAttribute('src', renders[2]);
            imgContainer.html(img);
          }

          if (saveFurniturePopup.length > 0) {
            const imgContainer = saveFurniturePopup.find('.img-container');
            const img = document.createElement('img');
            img.setAttribute('src', renders[2]);
            imgContainer.html(img);
          }
        }
      }

      if (typeof window.ivyScreenshot !== 'undefined') {
        appendRenders();
      } else {
        setTimeout(() => {
          if (typeof window.ivyScreenshot !== 'undefined') {
            appendRenders();
          }
        }, 200);
      }
    };

    self.createBlobs = _.debounce(self._createBlobs, 200);

    self.getRendersForPinterest = function() {
      const renders = self.getRenders([
        [0, 800, 600, 0xffffff, 0, 800 / 600],
        [-30, 800, 600, 0xffffff, 0, 800 / 600],
        [30, 800, 600, 0xffffff, 0, 800 / 600],
      ]);

      const img0 = $('.left-render');
      const img1 = $('.right-render');
      const img2 = $('.center-render');

      img0.attr('src', renders[0]);
      img1.attr('src', renders[1]);
      img2.attr('src', renders[2]);

      return renders;
    };


    self.getRenders = function(settings) {
      /*
                atribte "settings" is array of arrays.
                Returns arrays of screenshots. Before usage please check is window.ivyScreenshot is defined
                usage:
                self.getRenders([
                        [-40, 160, 160, 0xffffff, 0, 1],
                        [0, 300, 300, 0xedf0f0, 1, 1],
                        [0, 800, 600, 0xffffff, 0, 800 / 600]
                    ]);

                in array: [angle, width, height, color, transparency, ratio]
            */

      //
      // alert("getRenders");
      let renders;
      renders = window.ivyScreenshot.createBlobs(
        self.getShelf(),
        true,
        settings,
      );

      return renders;
    };

    self.assignPropertiesFromJson = function(json) {
      console.log('json');
      self.data = json;
      self.assignProperties(true);
      cameraClass.setCameraInitialPosition(self, false);
      self.setColor(self.material, true);
      self.buildFunctions.rebuildWalls(true, false, true);
      build3d.createCastShadows(self.width);
    };

    window.cstm.resetJson = self.assignPropertiesFromJson;

    self.assignProperties = function(skip_dna = false) {
      //  We have old apps running, which is saving only used rowhegiths and rowStyles.
      //  It is not working well on desktop configurator, as it assumes you have history even for rows
      //  that are not visibiel right now. So lets add default row heights and styles
      for (let i = self.data.rows.length - 1; i < 10; i++) {
        self.data.rows[i] = 300;
      }
      for (let i = self.data.row_styles.length - 1; i < 10; i++) {
        self.data.row_styles[i] = 1;
      }

      self.Property1 = self.data.property1;
      $('#ivy-property1').val(self.Property1);
      $('#ivy-property1').change();


      if (self.data.modules.length > 0) {
        self.constants.minX = self.data.modules[0].posx;
        self.constants.maxX = Math.abs(self.data.modules[0].posx);
        self.width = Math.abs(self.data.modules[0].sizex + 18);
      } else {
        const minX = Math.min.apply(Math, self.data.horizontals.map(x => Math.min(x.x1, x.x2)));
        const maxX = Math.max.apply(Math, self.data.horizontals.map(x => Math.max(x.x1, x.x2)));

        self.constants.minX = (maxX * 2) - 18;
        self.constants.maxX = (maxX * 2) - 18;
        self.width = self.data.width;
      }

      // assign width to slider;
      $('#ivy-width').val(self.width / 10);
      $('#ivy-width').change();

      if (self.data.modules.length > 0) {
        var height = self.getHeight(self.data.modules[0].sizey, self.data.rows);
      } else {
        var height = self.getHeight(Math.max.apply(Math, self.data.horizontals.map(x => x.y1)), self.data.rows);
      }

      if (height == 0 && self.data.row_amount) {
        self.rows = self.data.row_amount;
      } else {
        let tmpHeight = 0;
        let i = 0;
        while (tmpHeight < height - 18) {
          tmpHeight += self.data.rows[i];
          i++;
        }
        self.rows = i;
      }

      $('#ivy-height').val(self.rows);
      $('#ivy-height').change();

      for (let i = 0; i < self.data.rows.length; i++) {
        self.data.rows[i] = self.data.rows[i];
      }

      self.constants.rowHeight = self.data.rows;
      self.constants.rowStyles = self.data.row_styles;

      if (self.data.row_styles.length == 0) {
        self.constants.rowStyles = new Array(self.constants.rowHeight.length);
        for (let i = 0; i < self.constants.rowStyles.length; i++) {
          self.constants.rowStyles[i] = 1;
        }
        previous_row_styles = self.constants.rowStyles;
      } else if (self.data.drawers.length == 0 && self.data.doors.length == 0) {
        self.constants.rowStyles = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
      } else if (shouldHaveDoorOn(self.rows, self.constants.rowHeight, self.constants.rowStyles)) {
        self.constants.rowStyles = self.data.row_styles;
        previous_row_styles = self.constants.rowStyles;
        use_default_row_styles = false;
      } else {
        previous_row_styles = self.constants.rowStyles;
      }
      self.pattern = self.data.pattern;
      self.material = self.data.material;
      self.title = self.data.title;
      self.depth = self.data.depth;
      self.shelf_category = self.data.shelf_category;
      self.shelf_type = self.data.shelf_type;

      if (self.shelf_category == 'vinyl_storage') {
        self.backpanel_styles = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      } else {
        self.backpanel_styles = self.data.backpanel_styles;
      }

      self.generatedWalls = {
        doors: [],
        horizontals: [],
        legs: [],
        verticals: [],
        supports: [],
        backs: [],
        additional_elements: {
          shadow_left: [],
          shadow_middle: [],
          shadow_right: [],
          shadow_side: [],
        },
      };
      if (skip_dna == true) {
        return;
      }
      if (self.patterns[self.pattern].json == undefined) {
        const own_walls = self.patterns[self.pattern].generateWalls(self.Property1, self.width, self.rows, self.constants.rowHeight, self.depth, self.minX, self.maxX);
        self.points = {
          doors: [],
          horizontals: [],
          legs: [],
          verticals: [],
          supports: [],
          backs: [],

          additional_elements: {
            shadow_left: [],
            shadow_middle: [],
            shadow_right: [],
            shadow_side: [],
          },
        };
        self.actual_dna_name = 'old chaos';
      } else {
        self.points = self.getDnaTool().get_elements(self.patterns[self.pattern].json, self.Property1, self.width, self.rows, self.constants.rowHeight, self.constants.rowStyles, self.depth, 9, 125, true, [2, 2, 4, 2],
          true, -1, self.backpanel_styles, self.shelf_type);
        if (self.patterns[self.pattern].json.dna_name != undefined) {
          self.actual_dna_name = self.patterns[self.pattern].json.dna_name || '';
        }
      }
      self.changeDynamicDelivery();
    };

    self.rowAbsolute = function() {
      // return each unique row value to get all walls from the same plane
      let rows = build3d.walls.horizontals.map(obj => obj.position.y);
      rows = rows.filter((a, b) => rows.indexOf(a) == b);
      // sort rows according to height
      return rows.sort((a, b) => a - b);
    };

    this.maxY = function() {
      const rows = this.rowAbsolute();
      return rows[rows.length - 1];
    };

    self.generateJSON = function() {
      if (Object.keys(build3d.points).length > 0) {
        var modules = [];
        var horizontalBox = build3d.boundingBox.setFromObject(build3d.walls.horizontals[0]);
        modules.push({
          sizex: self.getWidth() - 18,
          sizey: getHeightByRows(self.rows, self.constants.rowHeight) - 18,
          posx: Math.round(self.getWidth() - 18),
          posy: 0,
        });
        var rowHeight = [];

        for (var i = 0; i < self.constants.rowHeight.length; i++) {
          rowHeight.push(self.constants.rowHeight[i]);
        }
        var data = {
          id: self.data.id,
          dna_name: self.actual_dna_name,
          preset: self.preset,
          modules,
          verticals: build3d.points.verticals,
          horizontals: build3d.points.horizontals,
          supports: build3d.points.supports,

          rows: rowHeight,
          depth: self.depth,
          color: self.material,
          material: self.material,
          property1: self.Property1,
          pattern: self.pattern,
          title: self.title,
          description: '',
          shelf_category: self.shelf_category,
          shelf_type: self.shelf_type,

          row_styles: self.constants.rowStyles,
          backpanel_styles: self.backpanel_styles,
          backs: build3d.points.backs,
          joints: build3d.points.joints,
          doors: build3d.points.doors,
          legs: build3d.points.legs,
          drawers: build3d.points.drawers,
          snapping: self.patterns[self.pattern].json.snapping,
          width: self.width,
          height: getHeightByRows(self.rows, self.constants.rowHeight),
          row_amount: self.rows,
          max_capacity: self.getCapacity(),
        };
        if (self.base_preset) {
          data.base_preset = self.base_preset;
        }
        return data;
      }
      // create "modules" property;
      var modules = [];
      var horizontalBox = build3d.boundingBox.setFromObject(build3d.walls.horizontals[0]);
      modules.push({
        sizex: (horizontalBox.size().x).toFixed(3) - 18,
        sizey: getHeightByRows(self.rows, self.constants.rowHeight) - 18,
        posx: Math.round(parseFloat((horizontalBox.min.x).toFixed(3)) + 9),
        posy: 0,
      });

      // create "verticals" property;
      const verticals = [];
      for (var i = 0; i < self.walls.verticals.length; i++) {
        const verticalBox = build3d.boundingBox.setFromObject(self.walls.verticals[i]);
        verticals.push({
          y1: parseFloat((verticalBox.min.y).toFixed(3)),
          y2: parseFloat((verticalBox.max.y).toFixed(3)),
          x1: parseFloat((self.walls.verticals[i].position.x).toFixed(3)),
          x2: parseFloat((self.walls.verticals[i].position.x).toFixed(3)),
        });
      }
      // create "horizontals" property;
      const horizontals = [];
      for (var i = 0; i < self.walls.horizontals.length; i++) {
        horizontalBox = build3d.boundingBox.setFromObject(self.walls.horizontals[i]);
        horizontals.push({
          y1: parseFloat((horizontalBox.min.y + horizontalBox.size().y / 2).toFixed(3)),
          y2: parseFloat((horizontalBox.min.y + horizontalBox.size().y / 2).toFixed(3)),
          x1: parseFloat((horizontalBox.min.x).toFixed(3)),
          x2: parseFloat((horizontalBox.max.x).toFixed(3)),
        });
      }
      // create "supports" property
      const supports = [];
      for (var i = 0; i < self.walls.supports.length; i++) {
        supportBox = build3d.boundingBox.setFromObject(self.walls.supports[i]);
        supports.push({
          y1: parseFloat((supportBox.min.y).toFixed(3)),
          y2: parseFloat((supportBox.max.y).toFixed(3)),
          x1: parseFloat((supportBox.min.x).toFixed(3)),
          x2: parseFloat((supportBox.max.x).toFixed(3)),
          z1: parseFloat((self.walls.supports[i].position.z).toFixed(3)),
          z2: parseFloat((self.walls.supports[i].position.z).toFixed(3)),
        });
      }
      var rowHeight = [];

      for (var i = 0; i < self.constants.rowHeight.length; i++) {
        rowHeight.push(self.constants.rowHeight[i]);
      }
      var data = {
        id: self.data.id,
        preset: true,
        modules,
        verticals,
        horizontals,
        supports,
        rows: rowHeight,
        depth: self.depth,
        color: self.material,
        material: self.material,
        property1: self.Property1,
        pattern: self.pattern,
        title: self.title,
        description: '',
      };
      if (self.base_preset) {
        data.base_preset = self.base_preset;
      }
      return data;
    };


    self.calculatePrice = function(points, material_override, width_override, number_of_rows_override) {
      if (typeof (points) === 'undefined') {
        points = build3d.points;
      }
      points.depth = self.depth;
      points.material = parseInt(self.material);
      points.pattern = self.pattern;
      points.width = self.width;
      points.height = getHeightByRows(self.rows, self.constants.rowHeight);

      if (typeof (points.shelf_type) === 'undefined') {
        points.shelf_type = self.shelf_type;
      }
      if (typeof (points.digital_product_version) === 'undefined') {
        points.digital_product_version = self.digital_product_version || 1;
      }
      if (!pricing || !pricing.coefficients) return 0; // it's for first run
      return pricing.getPrice(points);
    };


    self.walls = {
      verticals: [],
      horizontals: [],
      supports: [],
      shadows: [[], [], [], []],
      castShadows: [],
      legs: [],
      doors: [],
      door_groups: [],
      backs: [],
      boxes: [],
      drawers: [],
    };
    /*
        self.door_lists = [[],[],[],[],[],[],[],[],[],[]];
        self.door_lists_elements = [[],[],[],[],[],[],[],[],[],[]];
        */
    self.buildFunctions = {
      // creating single entities: horizontals, verticals etc, all use more less the same method: differs with scaling and which points are used
      // compare old and new points
      // each points is then marked if it is new, scaled or just moved
      // based on this operations are made on previously created items or creates new if necessary

      // rebuild all walls
      rebuildWalls(initial, snapping = false, skip_dna = false) {
        build3d.setDnaTool(self.getDnaTool());

        build3d.rebuildWalls(initial, snapping, skip_dna, self, true);
      },


      // rescaling function used if we get smaller units at the beginning (there were some differences earlier with mobile app and desktop)
    };

    self.setShelf = function(item, index, entry) {
      window.loadPresetFlag = true;

      // TODO: change it later
      if (item.width > 240) {
        $('.custom-gui__label2').trigger('click');
        $('#get-wider').trigger('touchstart');
      } else {
        $('.custom-gui__label__narrower').trigger('click');
        $('#get-norrower').trigger('touchstart');
      }
      previous_selected_row = null;
      self.setRowHeight(item.row_height);
      self.setRowStyles(item.row_styles);
      // TODO: Refactor needed (args as obj)
      Site.CustomizeGui.setGuiValue(item.motion, item.width, item.height, item.color, item.pattern, item.row_height[0], item.row_styles[0]);
      self.setWidth(item.width);
      self.setHeight(item.height);
      self.setProperty(item.motion);
      self.setPattern(item.pattern);
      self.setColor(item.material);


      window.loadPresetFlag = false;
      self.buildFunctions.rebuildWalls();


      build3d.createCastShadows(self.width);
      cameraClass.setCameraInitialPosition(self, false);


      tracking.trackPresetClick(window.cstm.pdp_category, index);
    };

    self.getShelf = function() {
      const shelfDataObject = {
        pattern: self.pattern,
        motion: self.Property1,
        width: self.width / 10,
        height: self.rows,
        color: self.materialId,
        row_height: self.constants.rowHeight,
        row_styles: self.constants.rowStyles,
        backpanel_styles: self.backpanel_styles,
        depth: self.depth,
        shelf_type: self.shelf_type,
      };
      return shelfDataObject;
    };

    self.setRowHeight = function(rowHeight) {
      self.constants.rowHeight = rowHeight;
    };

    self.setRowStyles = function(rowStyles) {
      self.constants.rowStyles = rowStyles;
    };

    self.assignProperties();

    // build walls
    self.buildFunctions.rebuildWalls(true);

    // set color
    self.setColor(self.material, true);

    // create shadows
    build3d.createCastShadows(self.width);
    // set the height value in configurator gui
    $('#ivy-height').val(getDisplayHeight(self.rows, self.constants.rowHeight));
  }

  createFurniture.prototype = FurnitureModel.prototype;

  // utils
  function setMarginForCanvas() {
    if ($('.pdp-product').hasClass('dimensions-in-view')) {
      return;
    }

    const canvasHeight = $('#div-slider').height();
    const rightColumnHeight = $('#custom-form').height();
    let valueToSet = (rightColumnHeight - canvasHeight) / 2;

    if (!canvasHeight) { // height == 0 ( When in dimensions mode ).
      return; // To avoid setting excessive margin-top.
    }
    if (canvasHeight >= rightColumnHeight) {
      valueToSet = 0;
    }

    $('#configuratorCanvas').css('margin-top', valueToSet);
    $('#row-selector-wrapper').css('margin-top', valueToSet + 18);
  }

  // calculate distance between 2 pointsfa
  function distance(object) {
    const x = object.x2 - object.x1;
    const y = object.y2 - object.y1;

    return Math.sqrt((x * x) + (y * y));
  }

  // get url parameter


  // requests + ui

  function saveFurnitureForSharing(token, callback) {
    const request = new XMLHttpRequest();
    request.open('POST', `${apiServer}/api/v1/contest/`, true);
    request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');

    request.onload = function() {
      if (request.status >= 200 && request.status < 400) {
        // Success!
        const data = JSON.parse(request.responseText);
        if (callback) {
          callback(data);
        } else {
          Site.Notify.onSuccess(cstm_i18n.shelf_saved);
        }
      } else if (request.status == 401) {
        $('#login-register-modal').fadeIn(300);
      } else {
        console.log('error request', request.responseText);
        console.log(request.responseText);
        Site.Notify.onError(cstm_i18n.shelf_not_saved);
      }
    };
    renderer.setSize(800, 600);
    renderer.alpha = false;
    renderer.setClearColor(0xedf0f0, 1);
    renderer.render(scene, camera);
    const screenshot = renderer.domElement.toDataURL('image/png');
    renderer.setSize(canvasAbsoluteHeight, canvasAbsoluteWidth);
    renderer.render(scene, camera);
    renderer.alpha = true;
    renderer.setClearColor(0xedf0f0, 1);

    const dataWithPreview = ivyShelf.generateJSON();
    dataWithPreview.magic_preview = screenshot.split(',')[1];

    request.send(JSON.stringify(dataWithPreview));
  }


  function uploadPreview(id, token, callback) {
    renderer.setSize(800, 600);
    renderer.alpha = false;
    renderer.setClearColor(0xedf0f0, 1);
    renderer.render(scene, camera);
    const screenshot = renderer.domElement.toDataURL('image/png');
    renderer.setSize(canvasAbsoluteHeight, canvasAbsoluteWidth);
    renderer.render(scene, camera);
    renderer.alpha = true;
    renderer.setClearColor(0xedf0f0, 1);


    upload(screenshot.split(',')[1]);

    function upload(blob) {
      const request = new XMLHttpRequest();
      request.open('PATCH', `${apiServer}/api/v1/jetty/preview_raw/${id}/`, true);
      request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
      request.setRequestHeader('Authorization', token);
      request.onload = function() {
        if (request.status >= 200 && request.status < 400) {
          // Success!
          if (callback) {
            callback(data);
          }
        } else {
          // We reached our target server, but it returned an error
          console.log('error request', request.responseText);
          // alert('error '+request.responseText);
        }
      };
      request.send(JSON.stringify({ preview: blob }));
    }
  }

  // check when changing rows, if you set less rows than the selected hold index, set the maximum hold available
  function checkActiveHolds(furniture) {
    const active = $('.configurator-hold.active');
    if ((active.index('.configurator-hold') + 1) > furniture.rows) {
      $('.configurator-hold').removeClass('active');
      $(`.configurator-hold:eq(${furniture.rows - 1})`).trigger('click');
      furniture.selectedRow = $(`.configurator-hold:eq(${furniture.rows - 1})`).index('.configurator-hold');
    }
  }

  async function assignPrice(price) {
    if (!price) return;
    const currentPrice = await price;

    const $elDiscountValue = $('.promo').find('.discount-value');
    const $elSaved = $('.promo').find('.saved');
    const $elDynamicDiscount = $('.dynamic-discount-span');
    const $elPromoSlot1 = $('.dynamic-discount-slot-1');
    const $elPromoSlot2 = $('.dynamic-discount-slot-2');
    const $elPromoSlot3 = $('.dynamic-discount-slot-3');

    const $elPromoBarSlot1 = $('.promo-bar-slot-1');
    const $elPromoBarSlot2 = $('.promo-bar-slot-2');
    const $elPromoBarSlot3 = $('.promo-bar-slot-3');

    const $elBeforeDiscount = $('.promo > .before-discount');
    const $elPrice = $('.custom-price');
    const $designerPrice = $('.designer-discount');
    const priceVal = Math.ceil(parseFloat(currentPrice.priceGrossRegional));
    const discountAmount = Math.ceil(priceVal * 0.1);
    if (cstm_i18n.sale_enabled && cstm_i18n.promo_vouchers.length > 0) {
      for (let i = 0; i < cstm_i18n.promo_vouchers.length; i++) {
        if (priceVal >= cstm_i18n.promo_vouchers[i].floor) {
          $elPromoSlot1.html(cstm_i18n.promo_vouchers[i].configurator_promo_alert_popup_paragraph_1);
          $elPromoSlot2.html(cstm_i18n.promo_vouchers[i].configurator_promo_alert_popup_paragraph_2);
          $elPromoSlot3.html(cstm_i18n.promo_vouchers[i].configurator_promo_alert_popup_paragraph_3);

          $elPromoBarSlot1.html(cstm_i18n.promo_vouchers[i].configurator_promo_ribbon_popup_left_text);
          $elPromoBarSlot3.html(cstm_i18n.promo_vouchers[i].configurator_promo_ribbon_popup_right_text);
          $elPromoBarSlot2.html(cstm_i18n.promo_vouchers[i].configurator_promo_ribbon_popup_left_red);

          break;
        }
      }
    }
    if (window.cstm_i18n.strikethrough_pricing_value) {
      const promotion = parseFloat(window.cstm_i18n.strikethrough_pricing_value);
      if (!$elPrice.hasClass('old-configurator-crossed-out-price')) {
        $elPrice.addClass('old-configurator-crossed-out-price');
        $('.slimmer--configurator').addClass('crossed-out-price');
      }
      let $percentage = $elPrice.find('.percentage');
      let $regularPrice = $elPrice.find('.regular-price');
      let $priceWithDiscount = $elPrice.find('.price-width-discount');
      if ($percentage.length === 0) {
        $percentage = $('<span class="percentage normal-10 md:normal-12 text-orange border-orange px-4 mr-4"></span>');
        $elPrice.append($percentage);
      }
      if ($regularPrice.length === 0) {
        $regularPrice = $('<span class="regular-price normal-12 md:normal-14 text-offblack-600"></span>');
        $elPrice.append($regularPrice);
      }
      if ($priceWithDiscount.length === 0) {
        $priceWithDiscount = $('<span class="price-width-discount normal-24 md:normal-28 text-offblack-800"></span>');
        $elPrice.append($priceWithDiscount);
      }
      $regularPrice.text(currentPrice.priceGrossRegional + currentPrice.currencySymbol);
      $priceWithDiscount.text(Math.round(currentPrice.priceGrossRegional - (currentPrice.priceGrossRegional * (promotion / 100))) + currentPrice.currencySymbol);
      $percentage.text(`-${promotion}%`);
    } else {
      $elPrice.text(currentPrice.priceGrossRegional + currentPrice.currencySymbol);
    }

    $designerPrice.text(Math.ceil(currentPrice.priceGrossRegional + 0.25 * currentPrice.priceGrossRegional));
    if (currentPrice.length > 1) {
      const calculatedWeight = Math.round(currentPrice.weight);
      $('#custom-weight').text(calculatedWeight);
      // console.log( calculatedWeight );
    }
  }
  function getHeight(limitHeight, rowsList) {
    let tmp = 0;
    for (let i = 0; tmp < limitHeight; i++) {
      if (Math.abs(tmp + rowsList[i] - limitHeight) < Math.abs(tmp - limitHeight)) {
        tmp += rowsList[i];
      } else {
        break;
      }
    }
    return tmp;
  }

  function getHeightByRows(rowsNumber, rowsList) {
    let tmp = 18;
    for (let i = 0; i < rowsNumber; i++) {
      tmp += rowsList[i];
    }
    return tmp + 12; // added footers
  }


  function getDisplayHeight(rowsNumber, rowsList) {
    return Math.round(getHeightByRows(rowsNumber, rowsList) / 10.0);
  }

  // create gui
  function generateGui(selector) {
    $('#description').hide();
    $('#furnitureParameters').show();

    $('#furnitureButtonsPdp').hide();
    $('#furnitureButtonsConfigurator').show();
    $('#buttonShare').on('click', () => {
      saveFurnitureForSharing('', data => {
        window.location.href = `https://www.facebook.com/dialog/feed?app_id=739339722848856&display=popup&caption=Gerade%20habe%20ich%20mein%20perfektes%20Regal%20mit%20Tylko%20erstellt.%20Mach%20auch%20mit%20beim%20Tylko-Wettbewerb%20und%20erstell%20dein%20eigenes.&link=http%3A%2F%2Fwww.tylko.com%2Fsizematters%2Fitem%2F${data.id}%2F&redirect_uri=http://tylko.com/sizematters/&picture=${data.preview}`;
      });
      return false;
    });

    $('#buttonShareTW').on('click', () => {
      saveFurnitureForSharing('', data => {
        window.location.href = `https://twitter.com/share?url=https%3A%2F%2Ftylko.com%2Fsizematters%2Fitem%2F${data.id}%2F&hashtags=FitTheSize&text=Nicht nur bei @tylko_furniture kommt es auf die richtige Größe an...`;
      });
      return false;
    });


    $('#buttonSentByEmail').on('click', () => {
      saveFurnitureForSharing('', data => {
        $('#contest-modal').show();
        $('#contest-modal-hidden-id').val(data.id);
        $('#contest-popup-image').attr('src', data.preview);
      });
      return false;
    });

    $('#buttonReset').on('click', () => {
      window.cstm.resetJson(cstm.item_reset);
      $('#buttonReset').remove();
      $('.creator-overlay').removeClass('creator-overlay');
      $('.transparent').removeClass('transparent');
      return false;
    });

    // setting row style event

    $('.row-style').on('click', function() {
      const margin_value = parseInt($(this).data('margin'));


      if ((margin_value + Math.floor(ivyShelf.constants.rowStyles[ivyShelf.selectedRow] / 10)) <= 4 || (cstm && cstm.dont_translate_rows)) {
        ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = (Math.floor(ivyShelf.constants.rowStyles[ivyShelf.selectedRow] / 10.0) * 10) + margin_value;
      } else {
        ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = (4 - margin_value) * 10 + margin_value;
        $('ul.row-filter-params').find('li').removeClass('active');
        $('ul.row-filter-params').find(`li:eq(${(4 - margin_value) - 1})`).addClass('active');
      }
      if (ivyShelf.constants.rowStyles[ivyShelf.selectedRow] < 10) {
        ivyShelf.constants.rowStyles[ivyShelf.selectedRow] += 10;
      }
      // console.log('setting to ', ivyShelf.constants.rowStyles[ivyShelf.selectedRow]);
      tracking.trackDoorChange(ivyShelf.selectedRow, ivyShelf.constants.rowStyles[ivyShelf.selectedRow]);

      ivyShelf.buildFunctions.rebuildWalls(false, true);


      Site.CustomizeGui.onValueChange();
      // set proper height in gui
      // addclass
      $('#row-selector .row-style-params li').removeClass('active');
      $('#row-selector ul.row-style-params ').find(`li:eq(${$(this).parent().index()})`).addClass('active');

      // set camera
      setTimeout(() => {
        if (!isMobile) ivyShelf.createBlobs();
      }, 500);
      // open drawers after adding them
      ivyShelf.changeDynamicDelivery();
      animations.set_tweens_for_row(ivyShelf.selectedRow, build3d.door_lists, build3d.drawers_lists, ivyShelf.depth);

      showSaveForLater();
      return false;
    });
    $('.row-filler').on('click', function(e) {
      if (use_default_row_styles = true) {
        use_default_row_styles = false;
        ivyShelf.constants.rowStyles = previous_row_styles;
      }
      const margin_value = parseInt($(this).data('margin'));
      $(e.target).parent().parent().find('li.active')
        .removeClass('active');
      $('ul.row-fillers').find(`[data-margin="${margin_value}"]`).parent().addClass('active');

      let actual_value = ivyShelf.constants.rowStyles[ivyShelf.selectedRow].toString();
      for (let i = 0; i < 3 - actual_value.length; i++) {
        actual_value = `1${actual_value}`;
      }
      if (margin_value <= 9) {
        actual_value = actual_value[0] + actual_value[1] + margin_value.toString();
      } else if (margin_value <= 99) {
        actual_value = actual_value[0] + Math.floor(margin_value / 10).toString() + actual_value[2];
      } else {
        actual_value = Math.floor(margin_value / 100).toString() + actual_value[1] + actual_value[2];
      }

      ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = parseInt(actual_value);
      ivyShelf.buildFunctions.rebuildWalls(false, true);
      ivyShelf.changeDynamicDelivery();

      animations.set_tweens_for_row(ivyShelf.selectedRow, build3d.door_lists, build3d.drawers_lists, ivyShelf.depth);
      showSaveForLater();
      return false;
    });

    $('.row-filter').on('click', function() {
      const margin_value = parseInt($(this).data('margin'));
      if (((margin_value / 10 + Math.floor(ivyShelf.constants.rowStyles[ivyShelf.selectedRow] % 10)) <= 4) || (cstm && cstm.dont_translate_rows)) {
        if (margin_value >= 100) {
          ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = margin_value + Math.floor(ivyShelf.constants.rowStyles[ivyShelf.selectedRow] % 100);
        } else {
          ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = Math.floor(ivyShelf.constants.rowStyles[ivyShelf.selectedRow] / 100) * 100 + margin_value + Math.floor(ivyShelf.constants.rowStyles[ivyShelf.selectedRow] % 10);
        }
      } else {
        ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = margin_value + (4 - (margin_value / 10));

        $('ul.row-style-params').find('li').removeClass('active');
        $('ul.row-style-params').find(`li:eq(${4 - (margin_value / 10) - 1})`).addClass('active');
      }

      $(this).parent().parent().find('li')
        .removeClass('active');
      $(this).parent().parent().find(`li:eq(${$(this).parent().index()})`)
        .addClass('active');

      tracking.trackDrawerChange(ivyShelf.selectedRow, ivyShelf.constants.rowStyles[ivyShelf.selectedRow]);

      ivyShelf.buildFunctions.rebuildWalls(false, true);

      Site.CustomizeGui.onValueChange();
      ivyShelf.changeDynamicDelivery();

      setTimeout(() => {
        if (!isMobile) ivyShelf.createBlobs();
      }, 500);

      animations.set_tweens_for_row(ivyShelf.selectedRow, build3d.door_lists, build3d.drawers_lists, ivyShelf.depth);
      showSaveForLater();
      return false;
    });
    // adding backpanels - mobile
    if ($('.row-backpanels-trigger')[0]) {
      $('.row-backpanels-trigger')[0].addEventListener('click', function() {
        this.classList.add('active');
        $('.row-backpanels-trigger')[1].classList.remove('active');
        Site.Customize.set('backpanels', false);
      });
      $('.row-backpanels-trigger')[1].addEventListener('click', function() {
        this.classList.add('active');
        $('.row-backpanels-trigger')[0].classList.remove('active');
        Site.Customize.set('backpanels', true);
      });
    }

    // adding doors - mobile
    const doorsTriggers = $('.row-doors-trigger');
    doorsTriggers.on('click', function(e) {
      e.preventDefault();
      // doorsTriggers.removeClass('active');

      // $('.configurator-hold').show();
      if ($('.edit-rows').is(':visible')) {
        $('.edit-rows').addClass('active').fadeOut('fast');
        $('.configurator-hold').show();
        // $('.pdp-slides canvas').css('opacity', 0);
        showControls = true;
      }
      console.log('DOOR TRIGGER, lets check selectedRow here :', ivyShelf.selectedRow);
      const proper_style_value = rowHandlers.getRowStyle(parseInt($(this).data('margin')), ivyShelf.constants.rowStyles[ivyShelf.selectedRow]);
      ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = proper_style_value;

      tracking.trackDoorChange(ivyShelf.selectedRow, ivyShelf.constants.rowStyles[ivyShelf.selectedRow]);
      // console.log(ivyShelf.selectedRow+1, parseInt($(this).html().split(" ")[1]));

      ivyShelf.buildFunctions.rebuildWalls(false, true);

      let actual_style_availability;

      actual_style_availability = build3d.row_styles_availability[ivyShelf.selectedRow];

      if (typeof actual_style_availability === 'undefined') {
        return;
      }

      // $('#row-selector ul.row-style-params').find('li:eq('+actual_style+')').addClass('active');
      rowHandlers.setRowAvailability(actual_style_availability, cached_style_buttons);

      Site.CustomizeGui.onValueChange();
      // addclass
      // $(this).addClass('active');
      showSaveForLater();
      return false;
    });


    // adding drawers - mobile
    const drawersTriggers = $('.row-drawers-trigger');
    drawersTriggers.on('click', function(e) {
      e.preventDefault();
      // drawersTriggers.removeClass('active');

      // $('.configurator-hold').show();
      if ($('.edit-rows').is(':visible')) {
        $('.edit-rows').addClass('active').fadeOut('fast');
        $('.configurator-hold').show();
        // $('.pdp-slides canvas').css('opacity', 0);
        showControls = true;
      }

      const proper_style_value = rowHandlers.getRowStyle(parseInt($(this).data('margin')), ivyShelf.constants.rowStyles[ivyShelf.selectedRow]);
      ivyShelf.constants.rowStyles[ivyShelf.selectedRow] = proper_style_value;


      tracking.trackDrawerChange(ivyShelf.selectedRow, ivyShelf.constants.rowStyles[ivyShelf.selectedRow]);

      ivyShelf.buildFunctions.rebuildWalls(false, true);

      let actual_style_availability;
      actual_style_availability = build3d.row_styles_availability[ivyShelf.selectedRow];

      if (typeof actual_style_availability === 'undefined') {
        return;
      }

      // $('#row-selector ul.row-style-params').find('li:eq('+actual_style+')').addClass('active');
      rowHandlers.setRowAvailability(actual_style_availability, cached_style_buttons);

      Site.CustomizeGui.onValueChange();
      ivyShelf.changeDynamicDelivery();
      // addclass
      // $(this).addClass('active');
      showSaveForLater();
      return false;
    });

    $('#custom-row-height-button').on('click', () => {
      if (typeof ivy !== 'undefined') {
        const promptInput = prompt('Please enter custom row height', "e.g. '50' ( in centimeters )");
        if (promptInput != null) {
          ivy.constants.rowHeight[ivyShelf.selectedRow] = promptInput * 10 + 18;

          setTimeout(() => {
            ivy.buildFunctions.rebuildWalls(false, true);
          }, 100);
        }
      }
    });

    $('.m-row-must-be-taller').on('click', () => {
      $('.row-heights').trigger('click');
    });


    $('.row-height').on('click', function() {
      if ($('.edit-rows').is(':visible')) {
        $('.edit-rows').addClass('active').fadeOut('fast');
        $('.configurator-hold').show();
        // $('.pdp-slides canvas').css('opacity', 0);
        showControls = true;
      }

      ivyShelf.constants.rowHeight[ivyShelf.selectedRow] = getRowHeight($(this).data('row-height'));
      // rebuild all walls
      ivyShelf.buildFunctions.rebuildWalls(false, true);
      if ($(this).data('row-height') == 'a') {
        $('#row-selector-wrapper .row-style-params').removeClass('visible').addClass('hidden');
        $('#row-selector-wrapper .row-must-be-taller').removeClass('hidden').addClass('visible');

        $('.m-row-must-be-taller').removeClass('hidden').addClass('visible');
        $('.row-door-params').removeClass('visible').addClass('hidden');
      } else {
        $('#row-selector-wrapper .row-style-params').removeClass('hidden').addClass('visible');
        $('#row-selector-wrapper .row-must-be-taller').removeClass('visible').addClass('hidden');

        $('.m-row-must-be-taller').removeClass('visible').addClass('hidden');
        $('.row-door-params').removeClass('hidden').addClass('visible');
      }

      let actual_style_availability;
      actual_style_availability = build3d.row_styles_availability[ivyShelf.selectedRow];

      if (typeof actual_style_availability === 'undefined') {
        return;
      }

      // $('#row-selector ul.row-style-params').find('li:eq('+actual_style+')').addClass('active');
      rowHandlers.setRowAvailability(actual_style_availability, cached_style_buttons);

      Site.CustomizeGui.onValueChange();
      // set proper height in gui
      $('#furnitureHeightValue').html(getDisplayHeight(ivyShelf.rows, ivyShelf.constants.rowHeight));
      $('#ivy-height').val(getDisplayHeight(ivyShelf.rows, ivyShelf.constants.rowHeight));
      $('div[name=height] .value').html(`${getDisplayHeight(ivyShelf.rows, ivyShelf.constants.rowHeight)}<span class="unit">cm</span>`);
      // set proper height active
      let eq;
      if (ivyShelf.constants.rowHeight[ivyShelf.selectedRow] == row_a) {
        eq = 0;
      } else if (ivyShelf.constants.rowHeight[ivyShelf.selectedRow] == row_b) {
        eq = 1;
      } else {
        eq = 2;
      }

      $('#row-selector .row-height-params li').removeClass('active');
      $('#row-selector ul.row-height-params').find(`li:eq(${eq})`).addClass('active');


      $('#row-selector .height-images li').removeClass('active');
      $('#row-selector ul.height-images').find(`li:eq(${eq})`).addClass('active');

      // fit selected row highlight to height
      scene.remove(rowBoxHover);
      const sizeBox = new THREE.Box3().setFromObject(rowBoxClone);
      const scale = ivyShelf.constants.rowHeight[ivyShelf.selectedRow] / sizeBox.size().y;
      let height = 0;
      for (var i = 0; i < ivyShelf.selectedRow; i++) {
        height += ivyShelf.constants.rowHeight[i];
      }
      height += ivyShelf.constants.rowHeight[i] / 2;
      const scaleX = (ivyShelf.width + 3) / sizeBox.size().x;
      rowBoxHover.scale.setY(scale);
      rowBoxHover.scale.setX(scaleX);
      rowBoxHover.position.setY(height);
      scene.add(rowBoxHover);
      tracking.trackRowChange(ivyShelf.selectedRow, eq);

      // set camera
      if (isMobile) {
        cameraClass.setCameraInitialPosition(ivyShelf, true, true);
      } else {
        cameraClass.setCameraInitialPosition(ivyShelf, false, true);
        // ivyShelf.setGenerateSVGText();
      }
      showSaveForLater();
      return false;
    });
  } // end of customProductPage check

  $('.row-drawers, #depth--small, #depth--medium, #depth--large').on('click', () => {
    const currentDepth = Site.Customize.get('depth');
    if (currentDepth === 240) {
      $('.drawers-only-for-32-and-40, .m-drawers-only-for-32-and-40').removeClass('hidden').addClass('visible');
      $('.drawers-only-to-6-row, .m-drawers-only-to-6-row').removeClass('visible').addClass('hidden');
      $('#row-selector-wrapper .row-filter-params').removeClass('visible').addClass('hidden');
      $('.row-drawer-params').removeClass('visible').addClass('hidden');
    } else {
      $('.drawers-only-for-32-and-40, .m-drawers-only-for-32-and-40').removeClass('visible').addClass('hidden');
      $('#row-selector-wrapper .row-filter-params').removeClass('hidden').addClass('visible');
      $('.row-drawer-params').removeClass('hidden').addClass('visible');
    }
    let actual_row_top_height = 0;
    for (let i = 0; i <= ivy.selectedRow; i++) {
      actual_row_top_height += ivy.constants.rowHeight[i];
    }
    if (actual_row_top_height >= 1578 && !(window.its_custom_editor)) {
      $('.drawers-only-to-6-row, .m-drawers-only-to-6-row').removeClass('hidden').addClass('visible');
      $('#row-selector-wrapper .row-filter-params').removeClass('visible').addClass('hidden');
      $('.row-drawer-params').removeClass('visible').addClass('hidden');
    }
    if ($('.drawers-only-for-32-and-40, .m-drawers-only-for-32-and-40').hasClass('visible')) {
      $('.drawers-only-to-6-row, .m-drawers-only-to-6-row').removeClass('visible').addClass('hidden');
    }
  });


  function generateCanvas(width, height, horizontals, verticals, supports, isTextGenerated) {
    if (typeof dConf === 'undefined' || dConf.state.isVisible == false) {
      return false;
    }

    dConf.width = width;
    dConf.height = height;


    if (isMobile) {
      return;
    }

    // console.log('generate canvas start');

    const self = this;
    let t1 = 0;
    let t2 = 0;
    const generateText = isTextGenerated;

    // timerStart();

    if (typeof dimensionsCanvas === 'undefined') {
      window.dimensionsCanvas = new fabric.Canvas('dCanvas', {
        width,
        height,
        backgroundColor: '#f0f0f0',
        selection: false,
        // hoverCursor: 'pointer',
        renderOnAddRemove: false,

        // non-customModeOn settings for moving canvas left to right
        selectable: true,
        hasBorders: false,
        hasControls: false,
        lockMovementY: false,
        lockMovementX: false,
        hoverCursor: 'ew-resize',
      });

      var element = window.dimensionsCanvas;
    } else {
      //        console.log('clearing Current Canvas And Rendering');
      var element = window.dimensionsCanvas;
      element.clear();
      element.renderAll();
    }


    //   console.log('copying Coordinates - json parse stringify');
    const strokeWidth = 18;
    const coordinates = {
      width,
      height,
      horizontals: JSON.parse(JSON.stringify(horizontals)),
      verticals: JSON.parse(JSON.stringify(verticals)),
      supports: JSON.parse(JSON.stringify(supports)),
    };
    const dimentionsH = [];
    const dimentionsW = [];

    function timerStart() {
      t1 = new Date().getTime();
      return t1;
    }
    function timerStop() {
      t2 = new Date().getTime();
      const result = t2 - t1;
      //        console.log('Canvas Render Time: ' + result + ' ms');
      return result;
    }

    function translateCoordinates(coordinates, strokeWidth) {
      // console.log('fn --- translateCoordinates');
      const translatedHorizontals = coordinates.horizontals;
      const lenH = translatedHorizontals.length;
      const { mirrorMode } = dConf;

      for (let i = 0; i < lenH; i++) {
        const horizontal = translatedHorizontals[i];

        if (!mirrorMode) {
          horizontal.x1 += width / 2;
          horizontal.x2 += width / 2;
        } else {
          horizontal.x1 = -horizontal.x1 + width / 2;
          horizontal.x2 = -horizontal.x2 + width / 2;
        }

        horizontal.y1 = height - horizontal.y1 - strokeWidth / 2;
        horizontal.y2 = height - horizontal.y2 - strokeWidth / 2;
      }

      const translatedVerticals = coordinates.verticals;
      const lenV = translatedVerticals.length;

      for (let j = 0; j < lenV; j++) {
        const vertical = translatedVerticals[j];

        if (!mirrorMode) {
          vertical.x1 += width / 2;
          vertical.x2 += width / 2;
        } else {
          vertical.x1 = -vertical.x1 + width / 2;
          vertical.x2 = -vertical.x2 + width / 2;
        }

        vertical.y1 = height - vertical.y1 - strokeWidth / 2;
        vertical.y2 = height - vertical.y2 - strokeWidth / 2;
      }
      return coordinates;
    }


    function drawCanvas(element, coordinates, isTextGenerated) {
      if (firstRunForCanvas === true) {
        const $customGui = $('#custom-form');
        const $detailsTable = $('.shelf-details');

        dConf.modifier.containerWidth = $('.canvas-container').parent().width(); /* - widthModifier */
        dConf.modifier.containerHeight = $customGui.outerHeight() - 1.5 * $detailsTable.outerHeight(); /* - heightModifier */
        // for static canvas only dConf.modifier.containerWidth = $('#dCanvas').parent().width();
        // for static canvas only dConf.modifier.containerHeight = $('#dCanvas').parent().height();
        dConf.modifier.containerPadding = 60;
        element.setWidth(dConf.modifier.containerWidth - dConf.modifier.containerPadding);
        element.setHeight(dConf.modifier.containerHeight /* - dConf.modifier.containerPadding */);
      }


      dConf.modifier.adjustZoom();

      drawSupports(element, width, height, coordinates.supports);
      drawLinesVerticals(element, coordinates.verticals);
      drawLinesHorizontals(element, coordinates.horizontals);


      if (!isTextGenerated || firstRunForCanvas) {
        // console.log('draws Numbers Horizontal and Vertical');
        firstRunForCanvas = false;
        $('.canvas-dimensions .canvas-container').css('margin', '0 auto');


        let fontsize = 16; // Default font-size for zoom factor < 4.5
        if (dConf.modifier.zoom > 4.5) fontsize = 15;
        if (dConf.modifier.zoom > 5) fontsize = 14;


        firstRunForCanvas = false;
        drawNumbersVerticals(element, coordinates.verticals, fontsize);
        drawNumbersHorizontals(element, coordinates.dimentionsH, fontsize);
      }

      // removes listeners for horizontal-moving, before adding new ones
      if (!firstRunForCanvas) {
        window.dimensionsCanvas.off('object:moving');

        window.dimensionsCanvas.off('mouse:up');
        window.dimensionsCanvas.off('mouse:down');
      }


      // Creates a group from all the shelf canvas-individual-elements

      const objs = window.dimensionsCanvas.getObjects().map(o => o.set('active', true));

      const group = new fabric.Group(objs, {
        originX: 'center',
        originY: 'center',
        selectable: true,
        hasBorders: false,
        hasControls: false,
        // lockMovementY: true

      });

      // when rendered canvas reaches the treshold width, the 'icon-resize' image pops up.
      const renderedCanvasWidth = Math.floor(group.get('width'));
      const renderedCanvasHeight = Math.floor(group.get('height'));
      const overflowTresholdWidth = dConf.modifier.containerWidth - (dConf.modifier.containerPadding + 15);
      const overflowTresholdHeight = dConf.modifier.containerHeight;
      const canvas = $('.canvas-dimensions');

      const iconResize = $('.icon-resize');

      // console.log('container width : ' + dConf.modifier.containerWidth, 'rendered canvas width: ' + renderedCanvasWidth );
      // Handles canvas overflowing the viewport and toggling canvas-user-interaction.
      if (renderedCanvasWidth >= overflowTresholdWidth || renderedCanvasHeight >= overflowTresholdHeight) {
        // console.log('shelf is bigger than viewport');
        dConf.shelfOverflow.flag = true;
        canvas.removeClass('canvas-static');
      }

      if ((renderedCanvasWidth < overflowTresholdWidth && renderedCanvasHeight < overflowTresholdHeight)) {
        // console.log('shelf fits in viewport');
        dConf.shelfOverflow.flag = false;
        canvas.addClass('canvas-static');
      }


      if (!dConf.shelfOverflow.indicatorAppeared && dConf.shelfOverflow.flag && !iconResize.hasClass('has-appeared')) {
        // console.log('Icon-resize shows up. On disappear, it doesnt show up again.');
        iconResize.addClass('has-appeared');
        dConf.shelfOverflow.indicatorAppeared = true;
      } else if (iconResize.hasClass('has-appeared') && !dConf.shelfOverflow.flag) {
        iconResize.removeClass('has-appeared');
      }


      window.dimensionsCanvas._activeObject = null;
      window.dimensionsCanvas.setActiveGroup(group.setCoords()).renderAll();


      // Adds event listeners for horizontal moving of the group

      window.dimensionsCanvas.on('object:moving', e => {
        // console.log( 'x: ' + e.target.left + ' --- y: ' + e.target.top );
        // console.log(e.target);
        if (typeof e.target === 'object') {
          dConf.movingObject = e.target;
        }
      });


      window.dimensionsCanvas.on('mouse:up', e => {
        if (e.target === dConf.movingObject) {
          const canvasCenterX = window.dimensionsCanvas.width / 2;
          const canvasCenterY = (window.dimensionsCanvas.height / 2);

          // console.log('mouseup');
          dConf.movingObject.animate({
            left: canvasCenterX,
            top: canvasCenterY,
          },

          {
            duration: 500,
            onChange: window.dimensionsCanvas.renderAll.bind(window.dimensionsCanvas),
            onComplete() {
              // console.log('animationCompleted');
            },
            easing: fabric.util.ease.easeOutBack,
          });
        }
      });


      window.dimensionsCanvas.on('mouse:down', e => {
        if (e.target === null) {
          // Creates a group from all the shelf canvas-individual-elements

          const objs = window.dimensionsCanvas.getObjects().map(o => o.set('active', true));

          const group = new fabric.Group(objs, {
            originX: 'center',
            originY: 'center',
            selectable: true,
            hasBorders: false,
            hasControls: false,
            // lockMovementY: true

          });
          window.dimensionsCanvas._activeObject = null;
          window.dimensionsCanvas.setActiveGroup(group.setCoords()).renderAll();
        }
      });

      element.renderAll();
    }


    function drawLinesVerticals(element, coordinates) {
      const len = coordinates.length;

      for (let i = 0; i < len; i++) {
        const points = dConf.modifier.calculateAllCoords(coordinates[i]);

        const lineV = new window.dConf.maker.Verticals(points, {
          uniqueID: i,
          uniqueVerticals: coordinates[i],
        });

        element.add(
          lineV,
        );
      } // end of for loop
    } // end of drawLines()


    function drawLinesHorizontals(element, coordinates) {
      const len = coordinates.length;

      for (let i = 0; i < len; i++) {
        const points = dConf.modifier.calculateAllCoords(coordinates[i]);

        const lineH = new window.dConf.maker.Horizontals(points, {
          uniqueID: i,
          uniqueHorizontals: coordinates[i],
        });

        // console.log('added Horizontal');

        element.add(
          lineH,
        );
        // Adding coords to the corresponding key in canvasObject
        // dConf.coordinatesverticals[i] = point;
      } // end of for loop
    } // end of drawLinesHorizontals()

    // Draws dimensions for verticals [in centimeters]
    function drawNumbersVerticals(element, coordinates, fontsize) {
      fontsize = typeof fontsize !== 'undefined' ? fontsize : 16;

      const len = coordinates.length;

      for (let i = 0; i < len; i++) {
        const point = coordinates[i];
        const y1 = dConf.modifier.calculateSingleCoordY(point.y1);
        const y2 = dConf.modifier.calculateSingleCoordY(point.y2);
        const yLength = (y1 - y2) * dConf.modifier.zoom / 10;

        const dText = new fabric.Text('', {
          fontFamily: "'Lettera Text Pro Web', 'Lettera Text Pro Web Alt', Arial, sans-serif",
          fontWeight: '400',
          fontSize: fontsize, // 70 / dConf.modifier.zoom,
          fill: '#b9bbbe',
          left: dConf.modifier.calculateSingleCoordX(point.x1) + 3 * dConf.modifier.getStrokeWidth(),
          top: y2 + Math.abs((y1 - y2) / 2),
          originX: 'center',
          originY: 'center',
          text: `${yLength.toFixed(0)}`,
          hasRotatingPoint: false,
          hasBorders: false,
          hasControls: false,
          lockScalingX: true,
          lockScalingY: true,
          lockRotation: true,
          borderColor: 'transparent',
          hoverCursor: 'default',
          lockMovementY: false, // required for horizontal group-moving
          lockMovementX: false, // required for horizontal group-moving and individual move
          selectable: true, // required for horizontal group-moving

          uniqueID: i,
          uniqueText: true,
        });

        //       console.log('added number vertical');

        element.add(
          dText,
        );
      } // end of for loop
    } // end of numbers verticals

    function drawNumbersHorizontals(element, coordinates, fontsize) {
      // x2 coordinate of the last splittedHorizontal in row.
      // If it makes an opening in the shelf, its dimension number needs to be offseted to the right.
      const openEndX2 = width - 9; // closedEndX2 = width - 18.

      let dynamicX2; // will be used to override the original item.x2 if needed ( LastNumberInRow case ).
      const offsetXforLastNumberInRow = 40;

      fontsize = typeof fontsize !== 'undefined' ? fontsize : 16;

      const len = coordinates.length;

      for (let i = 0; i < len; i++) {
        const splitLengthRounded = Math.round((coordinates[i].x2 - coordinates[i].x1) / 10);
        // console.log(splitLengthRounded);

        // Treshold for small, irrelevant numbers ( Slant-dna-sides case ).
        if (splitLengthRounded < 5) {

          // 'return true' - for jQuery's $.each method.

        } else {
          dynamicX2 = coordinates[i].x2;

          if (dynamicX2 === openEndX2) {
            dynamicX2 += offsetXforLastNumberInRow;
          }

          const dTextSplits = new fabric.Text('', {
            fontFamily: "'Lettera Text Pro Web', 'Lettera Text Pro Web Alt', Arial, sans-serif",
            fontWeight: '400',
            fontSize: fontsize, // 70 / dConf.modifier.zoom,
            fill: '#7c7d81',
            left: ((dynamicX2 + coordinates[i].x1) / 2 - 10) / dConf.modifier.zoom + dConf.modifier.getOffsetX(),
            top: (coordinates[i].y2 - 45) / dConf.modifier.zoom + dConf.modifier.getOffsetY(),
            originX: 'center',
            originY: 'center',
            text: `${splitLengthRounded}`,
            hasRotatingPoint: false,
            hasControls: false,
            lockScalingX: true,
            lockScalingY: true,
            lockRotation: true,
            borderColor: 'transparent',
            hoverCursor: 'default',
            lockMovementY: false, // required for horizontal group-moving
            lockMovementX: false, // required for horizontal group-moving and individual move
            selectable: true, // required for horizontal group-moving

            uniqueID: i,
            uniqueText: true,
          });

          //          console.log('added number horizontal');

          element.add(
            dTextSplits,
          );
        }
      } // end of for loop
    } // end of drawNumbersHorizontals

    function drawSupports(element, width, height, coordinates) {
      const len = coordinates.length;
      const { mirrorMode } = dConf;

      for (let i = 0; i < len; i++) {
        const point = coordinates[i];

        if (point.x1 > point.x2) {
          [point.x1, point.x2] = [point.x2, point.x1];
        }

        if (point.y1 > point.y2) {
          [point.y1, point.y2] = [point.y2, point.y1];
        }
        const support = new window.dConf.maker.Supports({
          left: dConf.modifier.calculateSingleCoordX(point.x1 + width / 2),
          top: dConf.modifier.calculateSingleCoordY(height - point.y2 - strokeWidth / 2),
          width: (point.x2 - point.x1) / dConf.modifier.zoom,
          height: (point.y2 - point.y1) / dConf.modifier.zoom,
          uniqueID: i,
          uniqueHorizontals: coordinates[i],
        });

        // translates X coordinates - mirror mode
        if (mirrorMode) {
          support.set({
            left: dConf.modifier.calculateSingleCoordX(-point.x1 - (point.x2 - point.x1) + width / 2),
          });
        }


        //                     console.log('added support');

        element.add(
          support,
        );

        // Adding coords to the corresponding key in canvasObject
        // coordinates[i] = point;
      } // end of for loop
    } // end of drawSupports()


    function splitHorizontals(coordinates) {
      //          console.log('fn --- splitHorizontals');
      let result = [];
      let connected_horizontals = [];

      const horis = coordinates.horizontals.sort((x, y) => (x.y1 != y.y1 ? x.y1 - y.y1 : x.x1 - y.x1));
      for (let i = 0; i < horis.length; i++) {
        if (connected_horizontals.length == 0) {
          connected_horizontals = [{
            x1: horis[0].x1,
            x2: horis[0].x2,
            y1: horis[0].y1,
            y2: horis[0].y2,
          }];
          continue;
        }
        const last_hori = connected_horizontals[connected_horizontals.length - 1];
        if (last_hori.y1 == horis[i].y1 && Math.abs(last_hori.x2 - horis[i].x1) < 2) {
          last_hori.x2 = horis[i].x2;
        } else {
          connected_horizontals.push({
            x1: horis[i].x1,
            x2: horis[i].x2,
            y1: horis[i].y1,
            y2: horis[i].y2,
          });
        }
      }

      // console.log('connected_horiz: ', connected_horizontals);
      // console.log('=====connected_horizontals=======');

      $.each(connected_horizontals, (indexH, itemH) => {
        const partial = [];
        let firstInRow;
        let lastInRow;
        let startsOnEdge;
        let endsOnEdge;

        // console.log('. . . verticals cut ' + indexH + ' . . . ');

        $.each(coordinates.verticals, (indexV, itemV) => {
          if (itemH.x1 <= itemV.x1 && itemH.x2 >= itemV.x1 && itemH.y1 === itemV.y1 + 9) {
            // console.log('. Partial start and the Cut', itemH.x1, itemV.x1 + 9);
            partial.push(itemV.x1 + 9);

            // Four conditions to determine which partials need to be extended with 0/100% width values
            // to add extra number-dimensions for open/closed-rows.
            if (typeof firstInRow === 'undefined' && itemH.x1 === 0) {
              firstInRow = true;
            }
            if (typeof lastInRow === 'undefined' && itemH.x2 === width) {
              lastInRow = true;
            }

            if (typeof startsOnEdge === 'undefined' && itemV.x1 + 9 === 18) {
              startsOnEdge = true;
            } else if (typeof endsOnEdge === 'undefined' && itemV.x1 + 9 === width) {
              endsOnEdge = true;
            }
          }
        });

        partial.sort((a, b) => a - b);
        // console.log('. Sorted partial', partial);

        // Adds the 'initial' value as the first coordinate in partial,
        // after checking if the partial is first in Row and does not start in-line with starting-edge.
        // Applied to show first dimension-number, when a row has an open start.  --->  __|_____|___|____|
        if (firstInRow && !startsOnEdge) {
          partial.unshift(9);
        }

        // Adds the 'width' value as the last coordinate in partial,
        // after checking if the partial is last in Row and does not end in-line with ending-edge.
        // Applied to show a last dimension-number, when a row has an open end.  |_____|___|____|__  <---
        if (lastInRow && !endsOnEdge) {
          partial.push(width + 9);
        }

        result = getCoorinatesPartials(partial, itemH.y1, itemH.y2, result);
      });

      return result;
    }
    function getCoorinatesPartials(partial, y1, y2, result) {
      //       console.log('getCoorinatesPartial');
      var y1 = y1;
      var y2 = y2;
      var result = result;
      const partialLength = partial.length;
      for (let i = 0; i < partialLength - 1; i++) {
        result.push({
          y1,
          y2,
          x1: partial[i],
          x2: partial[i + 1] - 18,
        });
      }

      return result;
    }


    function loadShelfFromCanvas() {
      const json = ivy.createJson();

      Object.assign(json, {
        horizontals: dConf.coordinates.horizontals,
        verticals: dConf.coordinates.verticals,
        supports: dConf.coordinates.supports,
      });

      ivy.assignPropertiesFromJson(json);

      //       console.log('fired');
    }


    const translatedCoordinates = translateCoordinates(coordinates, strokeWidth);
    coordinates.dimentionsH = splitHorizontals(translatedCoordinates);

    drawCanvas(dimensionsCanvas, translatedCoordinates, generateText);
    // console.log('dCanvas object', dimensionsCanvas);


    if (dConf.customModeOn && typeof dimensionsCanvas !== 'undefined' && !dConf.listenerIsInitialized) {
      // Add the visual effects of moving individual shelf sides horizontally.
      // E.g. setting hover color to red.
      dConf.fn.createShelfEventListeners();
    }
  } // end of generate canvas

  // CONFIGURATOR GALLERY

  const productGallery = document.getElementsByClassName('product-gallery')[0];
  if (productGallery) {
    $(document).keyup(e => {
      if (e.keyCode === 27) closeGallery();
    });

    productGallery.addEventListener('click', e => {
      if (e.target == productGallery) {
        closeGallery();
      }
    });

    function closeGallery() {
      $('.product-gallery').removeClass('show-gallery');
      $('#configuratorCanvas').show();
      $body.removeClass('hidden-overflow');
      $('.preview-controls ul li').removeClass('active');
      $('.preview-controls ul li').eq(0).addClass('active');
    }
  }


  // Joystick
  function joystickCreate(height) {
    joystick = document.getElementById('joystickSlider');
    const joystickBox = $('.joystick-box');

    joystickBox.height(height - 140);

    noUiSlider.create(joystick, {
      start: 0,
      range: {
        min: -9,
        max: 9,
      },
      orientation: 'vertical',
      // step: 1
    });
  }

  function joystickBind(ivyShelf) {
    let sliderValue = 0; // 0
    let tempRow = null;
    let currentStartRow = null;
    let currentChangedRow = null;
    let previousSliderValue = null; // 3

    joystick.noUiSlider.on('change', (values, handle) => {
      if (values[handle] < 0) {
        joystick.noUiSlider.set(0);
      } else if (values[handle] > 0) {
        joystick.noUiSlider.set(0);
      }
    });

    joystick.noUiSlider.on('slide', (values, handle) => {
      const
        currentRow = ivyShelf.selectedRow; const // 0
        { rows } = ivy;
      sliderValue = -parseInt(Math.round(joystick.noUiSlider.get()));

      if ((previousSliderValue == null && sliderValue != 0) || (sliderValue != previousSliderValue)) {
        tempRow = currentChangedRow + (sliderValue - (previousSliderValue || 0));
        if (tempRow < 0) {
          tempRow = 0;
        } else if (tempRow >= rows) {
          tempRow = rows - 1;
        }

        // ZAZNACZ rząd [tempRow]
        $(`.configurator-hold:eq(${tempRow})`).trigger('click');
        previousSliderValue = sliderValue;
        currentChangedRow = tempRow;
      }
    });
    joystick.noUiSlider.on('start', (values, handle) => {
      currentStartRow = ivyShelf.selectedRow;
      currentChangedRow = currentStartRow;
    });
    joystick.noUiSlider.on('end', (values, handle) => {
      tracking.trackRowSelectJoystick(currentChangedRow);
      currentStartRow = null;
      currentChangedRow = null;
      previousSliderValue = null;
    });
  }

  // Mobile Android Device - Google Chrome - disabling scroll down gesture causing page refresh.
  // Applied to remove the collision with joystick 'scroll-down' interaction.
  function disablePullDownToRefresh() {
    $(window).scroll(() => {
      const $html = $('html');

      if ($(document).scrollTop() >= 1) {
        $html.css({ 'touch-action': 'auto' });
      } else {
        $html.css({ 'touch-action': 'pan-down' });
      }
    });
  }

  function shouldHaveDoorOn(rows, rowHeights, rowStyles) {
    for (let i = 0; i < rows; i++) {
      if ((rowHeights[i] != row_a && (rowStyles[i] % 10 > 1)) || (Math.floor(rowStyles[i] / 10.0) > 1)) {
        return true;
      }
    }
    return false;
  }

  function CEScreenshotRenderer() {
    screenshotRenderer.setSize(3200, 2400);
    camera.aspect = 52 / 55;
    screenshotRenderer.render(scene, camera);
    renderer.alpha = false;
    screenshotRenderer.setClearColor(0xedf0f0);
    screenshotRenderer.render(scene, camera);
    const screenshot1 = screenshotRenderer.domElement.toDataURL('image/png');
    const newTab = window.open();
    newTab.document.body.innerHTML = `<img src="${screenshot1}" width="3200px" height="2400px">`;
  }

  // window.generateSVG = generateSVG;
  window.generateCanvas = generateCanvas;
  window.dimensionsCanvas;

  if (typeof fabric === 'undefined') {
    return;
  }

  window.dConf = {

    customModeOn: false,

    state: {
      isVisible: false, // Turns true on dimension-canvas-button click in PDP configurator

      setVisibility(boolean) {
        dConf.state.isVisible = boolean;
      },

      toggleVisibility() {
        if (typeof ivy !== 'undefined') {
          const $pdp = $('.pdp-product');
          const $canvas = $('.canvas-dimensions');
          const $shelfDetails = $('.shelf-details');

          dConf.state.isVisible = !dConf.state.isVisible;

          // Adds to pdp container, a class responsible for canvas-dimensions-related styles, while canvas-dimensions is in view.
          $pdp.toggleClass('dimensions-in-view');

          ivy.buildFunctions.rebuildWalls(false, true);
          $canvas.fadeToggle();
          $shelfDetails.fadeToggle();
          cstm_updateShelfTechnicalDetails();
        }
      },
    },

    line: { // same line width as in main ivy model - gets modified if needed, using dConf.modifier.zoom
      strokeWidth: 18,
      stroke: '#FFFFFF',
    },

    width: 0,
    height: 0,

    support: {
      fill: '#e6e6e6', // little darker - '#d8d8d8'
    },

    font: {
      horizontal: '#000000',
      vertical: '#a9abae',
    },

    shelfOverflow: {
      flag: false,
      indicatorAppeared: false,
      check() {

      },
    },

    mirrorMode: false,

    maker: {

      Verticals: fabric.util.createClass(fabric.Line, {
        type: 'Verticals',
        initialize(points, options) {
          options || (options = {});
          this.callSuper('initialize', points, options);
          this.set({
            strokeWidth: dConf.modifier.getStrokeWidth(),
            stroke: dConf.line.stroke,
            hasBorders: false, // changes on mouse-down
            padding: dConf.modifier.getStrokeWidth(),
            borderColor: '#A9ABAE',
            hasRotatingPoint: false,
            hasControls: false,
            lockScalingX: true,
            lockScalingY: true,
            lockRotation: true,
            originX: 'center',
            lockMovementY: false, // required for horizontal group-moving
            lockMovementX: false, // required for horizontal group-moving and individual move
            selectable: true, // required for horizontal group-moving
          });
        },

        toObject() {
          return fabric.util.object.extend(this.callSuper('toObject'), {
          });
        },

        _render(ctx) {
          this.callSuper('_render', ctx);
        },
      }),

      Horizontals: fabric.util.createClass(fabric.Line, {
        type: 'Horizontals',
        initialize(points, options) {
          options || (options = {});
          this.callSuper('initialize', points, options);
          this.set({
            strokeWidth: dConf.modifier.getStrokeWidth(),
            stroke: dConf.line.stroke,
            hasBorders: false, // changes on mouse-down
            padding: dConf.modifier.getStrokeWidth(),
            borderColor: '#A9ABAE',
            hasRotatingPoint: false,
            hasControls: false,
            lockScalingX: true,
            lockScalingY: true,
            lockRotation: true,
            originY: 'center',
            lockMovementY: false, // required for horizontal group-moving
            lockMovementX: false, // required for horizontal group-moving and individual move
            selectable: true, // required for horizontal group-moving
          });
        },

        toObject() {
          return fabric.util.object.extend(this.callSuper('toObject'), {
          });
        },

        _render(ctx) {
          this.callSuper('_render', ctx);
        },
      }),

      Supports: fabric.util.createClass(fabric.Rect, {
        type: 'Supports',
        initialize(options) {
          options || (options = {});
          this.callSuper('initialize', options);
          this.set({
            fill: dConf.support.fill,
            borderColor: '#000000',
            hasBorders: false, // change to true if selection-indicator needed
            hasRotatingPoint: false,
            hasControls: false,
            lockScalingX: true,
            lockScalingY: true,
            lockRotation: true,
            lockMovementY: false, // required for horizontal group-moving
            lockMovementX: false, // required for horizontal group-moving and individual move
            selectable: true, // required for horizontal group-moving

          });
        },

        toObject() {
          return fabric.util.object.extend(this.callSuper('toObject'), {
          });
        },

        _render(ctx) {
          this.callSuper('_render', ctx);
        },
      }),


    },

    customChanges: {
      verticals: {},
      horizontals: {},
      supports: {},
    },

    modifier: { // affects canvas size (zoom factor - 1 is normal 1:1 with ivy.points - gets very big. Zoom 4 is optimal
      zoom: 3.25, // default value which works well, gets modified with shelf's dimensions change - fn adjustZoom() - to fit the viewport
      containerPadding: 50, // adds space around shelf model on cavas - half of it is used as shelf offsetX and offsetY to center it
      containerWidth: 900, // gets changed with generateCanvas
      containerHeight: 800, // gets changed with generateCanvas
      shelfOverflowFlag: false, // gets changed when rendered canvas shelf is bigger than the viewport.
      getOffsetX() {
        return (dConf.modifier.containerWidth - (dConf.width / dConf.modifier.zoom) - this.containerPadding) / 2; // removed -this.containerPadding from within the brackets
      },
      getOffsetY() {
        return (dConf.modifier.containerHeight - (dConf.height / dConf.modifier.zoom)) / 2; // removed -this.containerPadding from within the brackets
      },

      adjustZoom() {
        const zoomMax = 6.75; // last 5.75 //historically 4.5 - 7.5 - when incremented, bigger zoom can be achieved.
        const zoomMin = 1.75; // last 1.5

        const viewportWidth = (dConf.modifier.containerWidth - 2.5 * dConf.modifier.containerPadding);
        const viewportHeight = dConf.modifier.containerHeight; // Before: - 2.5 * ...

        // perfect zoom factors, to keep the shelf inside the viewport
        const maxWidthZoom = parseFloat((dConf.width / viewportWidth).toFixed(2));
        const maxHeightZoom = parseFloat((dConf.height / viewportHeight).toFixed(2));

        // console.log('maxwidthzoom', maxWidthZoom, 'maxHeightZoom', maxHeightZoom, 'currentZoom', dConf.modifier.zoom );

        // Depending on the shelf size, matches correct 'zoom' factor so that the shelf fills the entire viewport.
        // When Shelf becomes extra wide, incremental zooming stops at certain point ( var zoomMin )
        dConf.modifier.zoom = Math.min(Math.max(maxWidthZoom, maxHeightZoom, zoomMin), zoomMax);
      },


      getStrokeWidth() {
        return 18 / this.zoom;
      },

      calculateAllCoords(coordinates) {
        const calculatedArr = [
          dConf.modifier.calculateSingleCoordX(coordinates.x1),
          dConf.modifier.calculateSingleCoordY(coordinates.y1),
          dConf.modifier.calculateSingleCoordX(coordinates.x2),
          dConf.modifier.calculateSingleCoordY(coordinates.y2),
        ];
        return calculatedArr;
      },

      calculateSingleCoordX(coordinate) {
        return coordinate / dConf.modifier.zoom + dConf.modifier.getOffsetX();
      },
      calculateSingleCoordY(coordinate) {
        return coordinate / dConf.modifier.zoom + dConf.modifier.getOffsetY();
      },
    },
    listenerIsInitialized: false,

    editingEnabled: false,

    fn: {

      showNumbers: false,

      enableSelectionOfAll() {
        if (dConf.customModeOn) {
          window.dimensionsCanvas.set({
            selectable: true,
            hasBorders: false,
            hasControls: false,
            lockMovementY: false,
            lockMovementX: false,
            hoverCursor: 'ew-resize',

          });

          // Fabric iterates through all of the individual objects and adds specific offset to each object individually

          for (let i = 0; i < window.dimensionsCanvas.getObjects().length; i++) {
            window.dimensionsCanvas.getObjects()[i]
              .set({
                selectable: true,
                hasBorders: false,
                hasControls: false,
                lockMovementY: false,
                lockMovementX: false,
                hoverCursor: 'move',
              });
          }
          // Needs to be re-rendered
          window.dimensionsCanvas.renderAll();
        }
      },

      moveListeners: {
        initialized: false,
        // After canvas generates for the first time, it creates Listeners responsible for horizontal movement of the entire canvas group.
        create() {
          // dConf.moveListeners.initialized = true;
        },
      },

      createShelfEventListeners() {
        // console.log('fn --- createShelfEventListeners');

        // After shelf element is moved, on mouse-up,
        // updating element coordinates in dConf.coordinates object,
        // based on the coordinate diffefence between initial mouse-down and final mouse-up ( delta~ variable ).
        // If canvas has dConf.modifier.zoom, the delta~ variable is affected (multiplication by zoom~ variable)

        // setting vertical's color to red, on hover
        window.dimensionsCanvas.on('mouse:over', e => {
          if (e.target !== null) {
            if (e.target.uniqueVerticals) {
              e.target.set('stroke', '#ff3c00');
              dConf.recentlyHoveredItem = e.target;
              // console.log('mouse:over renderAll()');
              window.dimensionsCanvas.renderAll();
            }
          }
        });

        window.dimensionsCanvas.on('mouse:out', e => {
          if (dConf.recentlyHoveredItem) {
            dConf.recentlyHoveredItem.set('stroke', '#ffffff');
            dConf.recentlyHoveredItem.set('hasBorders', false);
            dConf.recentlyHoveredItem = '';
            // console.log('mouse:out renderAll()');
            window.dimensionsCanvas.renderAll();
          }
        });

        window.dimensionsCanvas.on('mouse:down', e => {
          if (e.target !== null) {
            // Saving coordinates of clicked shelf element
            e.target.mouseDownX = e.target.left;
            e.target.mouseDownY = e.target.top;

            // initializing potential delta value
            window.dConf.customChanges.verticals[e.target.uniqueID] = { x1: 0, x2: 0 };
          }
        });

        window.dimensionsCanvas.on('mouse:up', e => {
          if (e.target !== null) {
            e.target.setCoords();

            let deltaX;
            let deltaY;
            let deltaSupportX;

            // VERTICALS - left to right
            if (typeof e.target.uniqueVerticals !== 'undefined') {
              deltaX = (e.target.left - e.target.mouseDownX) * dConf.modifier.zoom;
            }

            // HORIZONTALS - top to bottom
            if (typeof e.target.uniqueHorizontals !== 'undefined') {
              deltaY = (e.target.top - e.target.mouseDownY) * dConf.modifier.zoom;
            }

            // SUPPORTS - left to right
            if (typeof e.target.uniqueSupports !== 'undefined') {
              deltaSupportX = (e.target.left - e.target.mouseDownX) * dConf.modifier.zoom;
              const currentS = dConf.coordinates.supports[e.target.uniqueID];

              currentS.x1 += deltaSupportX;
              currentS.x2 += deltaSupportX;
            }
            /*  --- alternative moment for updating the ivy model - live, on every canvas-element change -- can cause lag
                        //updating the Ivy model
                            dConf.fn.loadShelfFromCanvas();
                        */
          }
        });
        // sets the init flag to true.
        dConf.listenerIsInitialized = true;
      }, // end of createShelfEventListeners();


      removeShelfEventListeners() {
        window.dimensionsCanvas.off('mouse:over');
        window.dimensionsCanvas.off('mouse:out');
        window.dimensionsCanvas.off('mouse:down');
        window.dimensionsCanvas.off('mouse:up');
      }, // end of removeShelfEventListeners();

      saveCanvasAsPng(canvasId) {
        if (!window.localStorage) {
          alert('This function is not supported by your browser.');
          return;
        }
        window.open(document.getElementById(canvasId).toDataURL('png'));
      },
    }, // end of fn
  }; // end of dConf

  // if(cstm_i18n.custom_editor_mode) {
  window.customEditor = {
    CEScreenshotRenderer,
  };
  // }

  $(window).on('resize', () => {
    if (typeof ivy !== 'undefined') {
      // console.log('resize');
      firstRunForCanvas = true;
      ivy.buildFunctions.rebuildWalls(false, true);
      if (!isMobile) {
        // setMarginForCanvas();
      }
    }
  });

  $('#dimensions-save-button').on('click', () => {
    window.dConf.fn.saveCanvasAsPng('dCanvas');
  });

  // Handling display of canvas-dimensions.
  $('.preview-controls ul li').on('click', function() {
    // console.log('click preview controls');
    if (
      (($(this).hasClass('camera-angle') || (!$(this).hasClass('dimension-switch') && !$(this).hasClass('preview-gallery'))) && dConf.state.isVisible)
            || ($(this).hasClass('dimension-switch') && !dConf.state.isVisible)
    ) {
      dConf.state.toggleVisibility();
    }
  });
}// end of creator();

module.exports = creator;
