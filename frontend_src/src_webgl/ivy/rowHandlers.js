module.exports = {
    // get onscreen coordinates of each handler
    rowSelector: $('#row-selector-wrapper'),
    getCoordinates: function(object, row, camera, renderer, div, ivy, positionRight, isMobile, screenVector) {
        // if row should be shown
        var offset = 55;

        //condition for a/b test
        // if(window.drawers){
        //     offset = 55;
        // } else {
        //     offset = 0;
        // }

        if (row <= ivy.rows) {
            // get screen coordinates from local
            let element = object;
            //console.log(element.localToWorld(screenVector))

            element.localToWorld(screenVector);
            screenVector.x = screenVector.x;
            screenVector.y = screenVector.y;

            screenVector.project(camera);

            // multiply * 2 to get position of the side of the furniture
            let posx = Math.round((screenVector.x + 1) * renderer.domElement.offsetWidth / 2);
            if (positionRight == null || positionRight == 0 || positionRight < posx) {
                // fix position by pixels, mobile need to be a bit more to the side
                if (isMobile) {
                    positionRight = posx - 40;
                } else {
                    positionRight = posx - 10;
                }
            }

            $('#row-selector-wrapper').css('left', positionRight + 30);
            // set Y position
            var posy = Math.round((1 - screenVector.y) * renderer.domElement.offsetHeight / 2) - 5;
            if (isMobile) {
                posy = posy - 10;
            } else {
                posy = posy - offset;
            }
            $('#' + div).css('left', positionRight + "px").css('top', posy + "px");

        } else {
            $('#' + div).hide();
        }
    },
    // add row handlers to the scene, regular 3dObjects to get coordinates from to the screen

    // hide or show handlers
    hideShowHandlers: function(show) {
        if ($('.configurator-hold').is(":visible") && !show) {
            $('.configurator-hold').fadeOut('fast');
        } else if (show) {
            $('.configurator-hold').fadeIn('fast');
            this.rowSelector.css('top', $('.configurator-hold.active').css('top'));
            this.rowSelector.css('top');
        }
    },
    getRowStyle: function(new_value, previous_value, dont_translate_rows = false) {
        let margin_value = new_value;
        let return_value;

        if (margin_value >= 10) {

            if (((margin_value / 10 + Math.floor(previous_value % 10)) <= 4) || (dont_translate_rows)) {
                if (margin_value >= 100) {
                    return_value =  margin_value + Math.floor(previous_value % 100);
                }
                else {
                    return_value =  Math.floor(previous_value / 100) * 100 + margin_value + Math.floor(previous_value % 10);
                }
            } else {
                return_value = margin_value + (4 - (margin_value / 10));
            }
        }
        else {

            if ((margin_value + Math.floor(previous_value / 10)) <= 4 || (dont_translate_rows)) {
                return_value = (Math.floor(previous_value / 10.0) * 10) + margin_value;
            } else {
                return_value = (4 - margin_value) * 10 + margin_value;
                /*
                $('ul.row-filter-params').find('li').removeClass('active');
                $('ul.row-filter-params').find('li:eq(' + ((4 - margin_value) - 1) + ')').addClass('active');
                */
            }
            if (return_value < 10) {
                return_value += 10;
            }
        }
        return return_value;
    },
    setRowAvailability: function (styles_for_row, controls_to_be_changed, dont_translate_rows=false) {
        if (window.its_custom_editor !== undefined && window.its_custom_editor === true) {
            dont_translate_rows = true;
        }
        // controls_to_be_changed - 6 items, 3 for doors, 3 for drawers
        // dont_translate_rows - legace for custom editor
        //console.log(styles_for_row, controls_to_be_changed);
        controls_to_be_changed.each((nr,elem) => {
            if (styles_for_row[nr] == 0 && !(dont_translate_rows)) {
                $(elem).removeClass('active').addClass('disable');
            } else if (styles_for_row[nr] == 2){
                $(elem).removeClass('disable').addClass('active');
            } else {
                $(elem).removeClass('active disable');
            }
         });
        /*
        $('ul.row-filter-params li').each((nr,elem) => {
            if (actual_style_availability[3 + nr] == 0 && !(cstm && cstm.dont_translate_rows)) {
                $(elem).addClass('disable');
            } else if (actual_style_availability[3 + nr] == 2) {
                $(elem).addClass('active');
            }
        }*/

    }
    // TODO: add proper row_avialbilety and changing highliting for selected items. Also setting door button should be here
}
