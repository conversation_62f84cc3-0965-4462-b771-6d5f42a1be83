
const prepare_preloader = function(colorName) {
    const preloader_config = {};

    preloader_config['2-vertical_edge.obj'] = ['vertical', `${colorName}-vert`];
    preloader_config['2-horizontal_edge.obj'] = ['horizontal', `${colorName}-hori`];
    preloader_config['2-horizontal_edge_plug.obj'] = ['horizontal-plug', `${colorName}-hori`];
    preloader_config['2-support.obj'] = ['support', `${colorName}-support`];
    preloader_config['2-support_drawer.obj'] = ['support-drawer', `${colorName}-support-drawer`];
    preloader_config['2-shadow_box.obj'] = ['shadow', `${colorName}-shadowbox`];
    preloader_config['2-shadow_box_left.obj'] = ['shadow-left', `${colorName}-shadowbox`];
    preloader_config['2-shadow_box_right.obj'] = ['shadow-right', `${colorName}-shadowbox`];
    preloader_config['2-left_right.obj'] = ['left-right', `${colorName}-vert`];
    preloader_config['2-top_bottom.obj'] = ['top-bottom', `${colorName}-hori`];
    preloader_config['insert.obj'] = ['insert', `${colorName}-support`];

    preloader_config['cast_shadow_center2.obj'] = ['cast-shadow-center', 'cast_shadow'];
    preloader_config['cast_shadow_left.obj'] = ['cast-shadow-right', 'cast_shadow'];
    preloader_config['cast_shadow_right.obj'] = ['cast-shadow-left', 'cast_shadow'];

    preloader_config['doors-fornir.obj'] = ['fdoors', `${colorName}_fdoors`];
    preloader_config['drawers-fornir.obj'] = ['fdrawer', `${colorName}_fdrawer`];
    preloader_config['fornir_drawer_handle.obj'] = ['fhandle', `${colorName}_fdrawer`];

    preloader_config['doors.obj'] = ['door', 'doors_open', 'doors_close'];
    preloader_config['drawers.obj'] = ['drawer_front', 'doors_open'];


    preloader_config['leg.obj'] = ['leg', 'leg_texture'];
    preloader_config['handle_big.obj'] = ['handle_big', 'doors_open', 'doors_close'];
    preloader_config['handle_small.obj'] = ['handle_small', 'doors_open', 'doors_close'];
    preloader_config['handle_drawer.obj'] = ['handle_drawer', 'doors_open', 'doors_close'];
    preloader_config['handle_short_left.obj'] = ['handle_short_left', 'handle_short_left_texture'];
    preloader_config['handle_short_left_shadow.obj'] = ['handle_short_left_shadow', 'handle_short_left_texture'];

    return preloader_config;
};
export { prepare_preloader as ObjectsConfig };
