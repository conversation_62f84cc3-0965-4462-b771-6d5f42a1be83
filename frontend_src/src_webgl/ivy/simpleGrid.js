$(function () {
    var preLoader = require("../loader");

    var modelsPath = "/r_static/src_webgl/ivy/models/";

    var texturesPath = "textures/cubemap/";
    var format = ".jpg";
    var urls = [
        modelsPath + texturesPath + "px" + format,
        modelsPath + texturesPath + "nx" + format,
        modelsPath + texturesPath + "py" + format,
        modelsPath + texturesPath + "ny" + format,
        modelsPath + texturesPath + "pz" + format,
        modelsPath + texturesPath + "nz" + format
    ];

    // white texture
    preLoader.loadTexture("white-hori", modelsPath + "textures/white_top_bottom.jpg");
    preLoader.loadTexture("white-vert", modelsPath + "textures/white_left_right.jpg");
    preLoader.loadTexture("white-support", modelsPath + "textures/white_support.jpg");
    preLoader.loadTexture("white-support-drawer", modelsPath + "textures/white_support_drawer.jpg");
    preLoader.loadTexture("white-shadowbox", modelsPath + "textures/white_shadow_box.jpg");

    //grey texture
    preLoader.loadTexture("gray-hori", modelsPath + "textures/grey_top_bottom.jpg");
    preLoader.loadTexture("gray-vert", modelsPath + "textures/grey_left_right.jpg");
    preLoader.loadTexture("gray-support", modelsPath + "textures/grey_support.jpg");
    preLoader.loadTexture("gray-support-drawer", modelsPath + "textures/grey_support_drawer.jpg");
    preLoader.loadTexture("gray-shadowbox", modelsPath + "textures/grey_shadow_box.jpg");

    //black texture
    preLoader.loadTexture("black-hori", modelsPath + "textures/black_top_bottom.jpg");
    preLoader.loadTexture("black-vert", modelsPath + "textures/black_left_right.jpg");
    preLoader.loadTexture("black-support", modelsPath + "textures/black_support.jpg");
    preLoader.loadTexture("black-support-drawer", modelsPath + "textures/black_support_drawer.jpg");
    preLoader.loadTexture("black-shadowbox", modelsPath + "textures/black_shadow_box.jpg");

    //purple texture
    preLoader.loadTexture("purple-hori", modelsPath + "textures/purple_top_bottom.jpg");
    preLoader.loadTexture("purple-vert", modelsPath + "textures/purple_left_right.jpg");
    preLoader.loadTexture("purple-support", modelsPath + "textures/purple_support.jpg");
    preLoader.loadTexture("purple-support-drawer", modelsPath + "textures/purple_support_drawer.jpg");
    preLoader.loadTexture("purple-shadowbox", modelsPath + "textures/purple_shadow_box.jpg");

    //ash texture
    preLoader.loadTexture("ash-hori", modelsPath + "textures/ash_top_bottom.jpg");
    preLoader.loadTexture("ash-vert", modelsPath + "textures/ash_left_right.jpg");
    preLoader.loadTexture("ash-support", modelsPath + "textures/ash_support.jpg");
    preLoader.loadTexture("ash-support-drawer", modelsPath + "textures/ash_support_drawer.jpg");
    preLoader.loadTexture("ash-shadowbox", modelsPath + "textures/ash_shadow_box.jpg");

    // other textures
    preLoader.loadTexture("cast_shadow", modelsPath + "textures/cast_shadow3.jpg");
    preLoader.loadTexture("leg_texture", modelsPath + "textures/leg.jpg");
    //preLoader.loadTexture("drawers_texture", modelsPath + "textures/drawers.jpg");
    preLoader.loadTexture("doors_open", modelsPath + "textures/doors_open.jpg?1");
    preLoader.loadTexture("doors_close", modelsPath + "textures/doors_close.jpg?1");

    // cubemap
    var ulrForCubeMap = modelsPath + texturesPath;
    preLoader.loadCubemap('cubemap', urls);

    preLoader.loadPlane('wall_compartment_shadow', modelsPath + "textures/wall_shadow.jpg");


    var preloader_config = {};

    preloader_config["2-vertical_edge.obj"] = ["vertical", "white-vert"];
    preloader_config["2-horizontal_edge.obj"] = ["horizontal", "white-hori"];
    preloader_config["2-horizontal_edge_plug.obj"] = ["horizontal-plug", "white-hori"];
    preloader_config["2-support.obj"] = ["support", "white_support"];
    preloader_config["2-support_drawer.obj"] = ["support-drawer", "white_support_drawer"];
    preloader_config["2-shadow_box.obj"] = ["shadow", "white-shadowbox"];
    preloader_config["2-shadow_box_left.obj"] = ["shadow-left", "white-shadowbox"];
    preloader_config["2-shadow_box_right.obj"] = ["shadow-right", "white-shadowbox"];
    preloader_config["2-left_right.obj"] = ["left-right", "white-vert"];
    preloader_config["2-top_bottom.obj"] = ["top-bottom", "white-hori"];


    preloader_config["cast_shadow_center2.obj"] = ["cast-shadow-center", "cast_shadow"];
    preloader_config["cast_shadow_left.obj"] = ["cast-shadow-right", "cast_shadow"];
    preloader_config["cast_shadow_right.obj"] = ["cast-shadow-left", "cast_shadow"];

    preloader_config["doors.obj"] = ["door", "doors_open", "doors_close"];
    preloader_config["drawers.obj"] = ["drawer_front", "doors_open"];
    preloader_config["leg.obj"] = ["leg", "leg_texture"];
    preloader_config["handle_big.obj"] = ["handle_big", "doors_open", "doors_close"];
    preloader_config["handle_small.obj"] = ["handle_small", "doors_open", "doors_close"];
    preloader_config["handle_drawer.obj"] = ["handle_drawer", "doors_open", "doors_close"];
    preloader_config["handle_short_left.obj"] = ["handle_short_left", "doors_handle_short"];

    preLoader.loadBatch("/r_static/ivy5.objx", preloader_config);


    var build = require('./build3d');
    require('./grid')(preLoader, build);

});
