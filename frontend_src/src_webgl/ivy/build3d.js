/* eslint-disable no-param-reassign, camelcase, consistent-return */

import { getDefinitionForMaterial } from './../presets/bundle.js';
import { preparePoints } from './decoder_old';

THREE = THREE || {};

let elements = null;
const boundingBox = new THREE.Box3();
const plankScale = 0.18;
const plankScaleMM = 18;
let shadowCastEnabled = true;
let dnaStartWasSent = false;

let sizePlane = [];

let _points;
let reflectionCube;

let color;
let opacity;
let hex_color;
let hex_handler_color;
let backs_color;
let reflectivityValue;
let reflectivityValueDoors;
let material_id;


class Build3d {
    constructor(
        elements_in,
        dnaTools,
        sendCustomization,
        context_in,
        isMobile,
    ) {
        [
            material_id,
            color,
            opacity,
            hex_color,
            hex_handler_color,
            backs_color,
            reflectivityValue,
            reflectivityValueDoors,
        ] = getDefinitionForMaterial(window.cstm.item.material, window.cstm.item.shelf_type);

        this.context = context_in;

        this.send_customization = sendCustomization;
        this.elements = elements_in;
        elements = this.elements;
        window.elements = this.elements;

        this.dnaTools = dnaTools;
        this.boundingBox = boundingBox;
        this.scene = null;
        this.isMobile = isMobile;
        this.backpanel_rows = null;

        this.wallShadowsObjects = [];

        this.row_a = 200;
        this.row_b = 300;
        this.row_c = 400;
        this.shelf_type = window.cstm.item.shelf_type;

        this.magical_materials_for_rows = {
            doors: [],
            handlers: [],
            shadows: [],
            backs: [],
        };
        this.walls = {
            verticals: [],
            horizontals: [],
            supports: [],
            shadows: [[], [], [], [], []],
            castShadows: [],
            legs: [],
            doors: [],
            door_groups: [],
            backs: [],
            boxes: [],
            drawers: [],
            topBottomWalls: [],
            leftRightWalls: [],
            additionalHorizontalElements: [],
            wallCompartmentShadow: [],
        };

        this.points = {};

        this.handlers = [];

        this.depth = 320;
        this.depth_previous = 320;
        this.depth_changed = false;
        this.width = 1200;

        this.row_styles_presets = -1;
        this.empty_row_styles = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];


        // temporary geometries
        let boxGeometry = new THREE.BoxGeometry(1, 1, 1);
        let boxMaterial = new THREE.MeshBasicMaterial({
            color: 0xb6b6b7,
            wireframe: false,
        });
        elements.backs = new THREE.Mesh(boxGeometry, boxMaterial);

        elements.backs.renderOrder = 38;

        // box POF
        boxGeometry = new THREE.BoxGeometry(1, 1, 1);
        boxMaterial = new THREE.MeshBasicMaterial({
            color: 0x67717f,
            wireframe: false,
        });
        elements.storage_box = new THREE.Mesh(boxGeometry, boxMaterial);

        // spots POF
        const spotGeometry = new THREE.BoxGeometry(1, 1, 1);
        const spotMaterial = new THREE.MeshBasicMaterial({
            color: 0x550088,
            wireframe: false,
        });
        elements.drawers = new THREE.Mesh(spotGeometry, spotMaterial);

        // door POF
        /*
             let doorGeometry = new THREE.BoxGeometry( 1, 1, 1);
             let doorMaterial = new THREE.MeshBasicMaterial( { color: 0xf2f2f2, wireframe: false } );
             elements['door'] = new THREE.Mesh( doorGeometry, doorMaterial );
             */
    }

    init3d() {
        const { elements } = this;
        window.elements = elements;
        for (let i = 0; i < 12; i++) {
            let material = new THREE.ShaderMaterial({
                vertexShader: $('#vertexshader').text(),
                fragmentShader: $('#fragmentshader').text(),
                uniforms: {
                    foreground: {
                        type: 't',
                        value: elements.doors_open,
                    },
                    bg: {
                        type: 't',
                        value: elements.doors_close,
                    },
                    blending_ratio: {
                        type: 'f',
                        value: 0.0,
                    },
                    blending_color: {
                        type: 'c',
                        value: new THREE.Vector3(1.0, 1.0, 1.0),
                    },
                },
            });
            this.magical_materials_for_rows.doors.push(material);

            material = new THREE.ShaderMaterial({
                vertexShader: $('#vertexshader').text(),
                fragmentShader: $('#fragmentshader').text(),
                uniforms: {
                    foreground: {
                        type: 't',
                        value: elements.doors_open,
                    },
                    bg: {
                        type: 't',
                        value: elements.doors_close,
                    },
                    blending_ratio: {
                        type: 'f',
                        value: 0,
                    },
                    blending_color: {
                        type: 'c',
                        value: new THREE.Vector3(1.0, 1.0, 1.0),
                    },
                },
            });
            this.magical_materials_for_rows.handlers.push(material);
        }
        this.scene = new THREE.Scene();
        // window.scene = this.scene;
        reflectionCube = elements.cubemap;
        let shadows = ['shadow', 'shadow-left', 'shadow-right'];
        for (let i = 0; i < shadows.length; i++) {
            elements[shadows[i]].traverse(child => {
                if (child instanceof THREE.Mesh) {
                    // child.rotation.y = Math.PI / 2;
                    child.material.transparent = true;
                    child.material.opacity = 1;
                    child.material.side = THREE.FrontSide;
                    child.material.envMap = reflectionCube;
                    // child.material.combine = THREE.AdditiveBlending;
                    child.material.reflectivity = reflectivityValue;
                }
            });
            elements[shadows[i]].renderOrder = 0;
        }

        shadows = [
            'cast-shadow-right',
            'cast-shadow-left',
            'cast-shadow-center',
        ];
        for (let i = 0; i < shadows.length; i++) {
            elements[shadows[i]].traverse(child => {
                if (child instanceof THREE.Mesh) {
                    child.material.transparent = true;
                    child.material.opacity = 0.5;
                }
            });
            elements[shadows[i]].renderOrder = 15;
        }
        // moved to separate function, as drawers and doors are different depending on shelf_type, so those will be
        // set to new models after each shelf_type change (used in screen_tool)
        this.setProperDoorsAndDrawersModel(window.cstm.item.material);


        elements.vertical.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.envMap = reflectionCube;
                // child.material.combine = THREE.AdditiveBlending;
                child.material.reflectivity = reflectivityValue;
            }
        });

        elements.support.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.envMap = reflectionCube;
                // child.material.combine = THREE.AdditiveBlending;
                child.material.reflectivity = reflectivityValue;
            }
        });

        elements.support.renderOrder = 22;

        elements['support-drawer'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.envMap = reflectionCube;
                // child.material.combine = THREE.AdditiveBlending;
                child.material.reflectivity = reflectivityValue;
            }
        });
        elements['support-drawer'].renderOrder = 21;

        elements.vertical.renderOrder = 2;
        elements.horizontal.traverse(child => {
            if (child instanceof THREE.Mesh) {
                // child.rotation.x = Math.PI;
                // child.rotation.z = Math.PI;
                child.material.polygonOffset = true;
                child.material.polygonOffsetFactor = 1.0;
                child.material.polygonOffsetUnits = -1.0;
                child.material.envMap = reflectionCube;
                // child.material.combine = THREE.AdditiveBlending;
                child.material.reflectivity = reflectivityValue;
            }
        });
        elements.horizontal.renderOrder = 20;

        elements.handle_big.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.rotation.x = -Math.PI / 2;
                child.material.envMap = reflectionCube;
                // child.material.combine = THREE.AdditiveBlending;
                child.material.reflectivity = reflectivityValue;
            }
        });

        elements.backs.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.color = new THREE.Color(backs_color);
                child.material.reflectivity = reflectivityValue;
            }
        });
        elements.backs.renderOrder = 18;

        elements.handle_big.renderOrder = 40;

        elements.handle_small.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.rotation.x = -Math.PI / 2;
                child.material.envMap = reflectionCube;
                // child.material.combine = THREE.AdditiveBlending;
                child.material.reflectivity = reflectivityValue;
            }
        });
        elements.handle_small.renderOrder = 60;

        elements.handle_short_left.traverse(child => {
            if (child instanceof THREE.Mesh) {
                // child.rotation.x = -Math.PI / 2;
                child.material.map = elements.handle_short_left_texture;
                // child.material.envMap =  reflectionCube;
                // child.material.reflectivity = reflectivityValue;
            }
        });
        elements.handle_short_left.renderOrder = 1061;

        elements.handle_short_left_shadow.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.map = elements.handle_short_left_texture;
                child.material.transparent = true;
                child.material.depthTest = true;
                child.material.depthWrite = false;
                child.material.polygonOffset = true;
                child.material.polygonOffsetFactor = 1.0;
                child.material.polygonOffsetUnits = -1.0;
            }
        });
        elements.handle_short_left_shadow.renderOrder = 100000;

        elements['top-bottom'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                // child.material.polygonOffset = true;
                // child.material.polygonOffsetFactor = 1.0;
                // child.material.polygonOffsetUnits = -1.0;
                child.material.envMap = reflectionCube;
                // child.material.combine = THREE.AdditiveOperation;


                child.material.reflectivity = reflectivityValue;
            }
        });

        elements['left-right'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.polygonOffset = true;
                child.material.polygonOffsetFactor = 1.0;
                child.material.polygonOffsetUnits = -1.0;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = reflectivityValue;
            }
        });
    }

    setProperDoorsAndDrawersModel(color) {
        let material_id,
            opacity,
            hex_color,
            hex_handler_color,
            backs_color,
            reflectivityValue,
            reflectivityValueDoors;

        [
            material_id,
            color,
            opacity,
            hex_color,
            hex_handler_color,
            backs_color,
            reflectivityValue,
            reflectivityValueDoors,
        ] = getDefinitionForMaterial(color, this.shelf_type);

        if (this.shelf_type === 1 || this.shelf_type === 0) {
            elements.door.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    if (parseInt(material_id, 10) === 0) {
                        // eslint-disable-next-line no-param-reassign
                        child.material.map = elements.doors_open_white;
                    }
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.color = new THREE.Color(hex_color);
                    // child.material.alphaTest = 0.1;
                }
            });
            elements.door.renderOrder = 35;

            elements.drawer_front.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    if (parseInt(material_id) === 0) {
                        child.material.map = elements.doors_open_white;
                    }
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.color = new THREE.Color(hex_color);
                }
            });
            elements.drawer_front.renderOrder = 35;
            elements['handle_short_left'].traverse(function (child) {
                if (child instanceof THREE.Mesh) {
                    child.material.color = new THREE.Color(hex_handler_color);

                }
            });
        } else if (this.shelf_type === 2) {
            elements.fdoors.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.map = elements[`${color}-fdoors`];
                    child.material.needsUpdate = true;
                }
            });
            elements.fdoors.renderOrder = 35;

            elements.fdrawer.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.map = elements[`${color}-fdrawer`];
                    child.material.needsUpdate = true;
                }
            });
            elements.fdrawer.renderOrder = 35;
            elements.fhandle.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    child.material.dispose();
                    child.material.transparent = false;
                    child.material.map = elements[`${color}-fdrawer`];
                    child.material.needsUpdate = true;
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                }
            });
        }
    }

    getScene() {
        return this.scene;
    }


    setShelfType(shelfType = 0) {
        if (shelfType === 0) {
            this.row_a = 200;
            this.row_b = 300;
            this.row_c = 400;
            this.shelf_type = 0;
        } else if (shelfType === 1) {
            this.row_a = 200;
            this.row_b = 300;
            this.row_c = 400;
            this.shelf_type = 1;
        } else if (shelfType === 2) {
            this.row_a = 200;
            this.row_b = 300;
            this.row_c = 400;
            this.shelf_type = 2;
        }
        this.setProperDoorsAndDrawersModel(window.cstm.item.material);
    }

    setDnaTool(dnaTools) {
        this.dnaTools = dnaTools;
    }

    createSingleVertical(index, oldPoints, newPoints) {
        let dif;
        const { scene } = this;
        // check if there are any old points, used for initial run
        if (typeof oldPoints !== 'undefined') {
            dif = this.compare_dnas(oldPoints, newPoints);
        } else {
            dif = {
                newitem: true,
                scaled: true,
                only_moved: true,
            };
        }
        if (this.depth != this.depth_previous) {
            dif.scaled = true;
            dif.same = false;
        }

        // if its the same quit
        if (dif.same === true) {
            return true;
        }
        let vertical;
        // if new item
        if (dif.newitem) {
            // clone the base element
            vertical = elements.vertical.clone();
        } else {
            // else take a vertical already existing
            vertical = this.walls.verticals[index];
        }

        const distance = Math.abs(newPoints.bottom.y - newPoints.top.y) + 3;

        // if scaled set proper height
        if (dif.scaled) {
            const verticalBox = boundingBox.setFromObject(elements.vertical);
            vertical.scale.setX(1);
            vertical.scale.setY(this.depth / 100);
            vertical.scale.setZ(distance / 100);
            vertical.rotation.x = -Math.PI / 2;
            if (dif.newitem) {
                // if its a new item it also needs width and depth
                vertical.scale.setX(1);
                vertical.scale.setY(this.depth / 100);
                vertical.scale.setZ(distance / 100);
            }
            // set proper position
            vertical.position.setX(newPoints.bottom.x);
            vertical.position.setY(
                Math.min(newPoints.bottom.y, newPoints.top.y) - 1.5,
            );
        }

        if (dif.only_moved) {
            // if it was only moved changed x,y. Z is always the same
            vertical.position.setX(newPoints.bottom.x);
            vertical.position.setY(
                Math.min(newPoints.bottom.y, newPoints.top.y) - 1.5,
            );
            vertical.scale.setY(this.depth / 100);
            vertical.scale.setZ(distance / 100);
        }

        if (dif.newitem) {
            // if it is a new item add it to scene and to the furniture parts pool
            vertical.name = 'vertical';
            scene.add(vertical);
            this.walls.verticals.push(vertical);
        }
    }

    createSingleBack(index, oldPoints, newPoints) {
        let dif;
        const { scene } = this;

        if (typeof oldPoints !== 'undefined') {
            dif = this.compare_dnas(oldPoints, newPoints);
        } else {
            dif = {
                newitem: true,
                scaled: true,
                only_moved: true,
            };
        }
        if (dif.same === true) {
            return true;
        }

        let back;
        if (dif.newitem) {
            back = elements['top-bottom'].clone();
        } else {
            back = this.walls.backs[index];
        }
        const backBox = boundingBox.setFromObject(elements['top-bottom']);

        let scaleX; let
            scaleY;
        back.rotation.x = -Math.PI;

        scaleY = Math.abs(newPoints.bottom.y - newPoints.top.y - 18) / backBox.size().x;
        scaleX = Math.abs(newPoints.bottom.x - newPoints.top.x - 18) / backBox.size().x;

        back.position.setX((newPoints.bottom.x + newPoints.top.x) / 2);
        back.position.setY(newPoints.bottom.y - 9);
        back.position.setZ(22);
        back.scale.setY(scaleY);
        back.scale.setX(scaleX);

        if (dif.newitem) {
            back.name = 'backss';
            scene.add(back);
            if (this.walls.backs === undefined) {
                this.walls.backs = [];
            }
            this.walls.backs.push(back);
        }
    }

    createSingleSupport(index, oldPoints, newPoints, supportType) {
        let dif;
        const { scene } = this;
        if (typeof oldPoints !== 'undefined') {
            dif = this.compare_dnas(oldPoints, newPoints);
        } else {
            dif = {
                newitem: true,
                scaled: true,
                only_moved: true,
            };
        }
        if (this.depth != this.depth_previous) {
            dif.scaled = true;
            dif.same = false;
        }
        if (dif.same === true) {
            return true;
        }

        let support;
        if (dif.newitem) {
            support = elements.support.clone();
        } else {
            support = this.walls.supports[index];
        }
        let supportBox = boundingBox.setFromObject(elements.horizontal);

        let scaleX; let
            scaleZ;
        if (supportType == 'left') {
            support.rotation.x = -Math.PI / 2;
        } else {
            support.rotation.x = Math.PI / 2;
        }
        if (dif.newitem) {

        }
        scaleZ = Math.abs(newPoints.bottom.y - newPoints.top.y) / supportBox.size().x;
        // scaleX = this.constants.gradientSupportWidth / supportBox.size().x;
        scaleX = Math.abs(newPoints.bottom.x - newPoints.top.x) / supportBox.size().x;

        if (dif.scaled) {
            support.scale.setZ(scaleZ);
            support.scale.setX(scaleX);
            if (dif.newitem) {
                support.scale.setY(1);
            }
            supportBox = boundingBox.setFromObject(support);
            support.position.set(
                Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,
                Math.min(newPoints.bottom.y, newPoints.top.y)
                + Math.abs(newPoints.bottom.y - newPoints.top.y) / 2,
                0,
            );
        }

        if (dif.only_moved) {
            supportBox = boundingBox.setFromObject(support);
            support.position.set(
                Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.size().x / 2,
                Math.min(newPoints.bottom.y, newPoints.top.y)
                + Math.abs(newPoints.bottom.y - newPoints.top.y) / 2,
                9,
            );
        }

        if (dif.newitem) {
            support.name = 'supports';
            scene.add(support);
            if (this.walls.supports === undefined) {
                this.walls.supports = [];
            }
            this.walls.supports.push(support);
        }
    }

    createHorizontal(index, oldPoints, newPoints) {
        let dif;
        const { scene } = this;
        if (typeof oldPoints !== 'undefined') {
            dif = this.compare_dnas(oldPoints, newPoints);
        } else {
            dif = {
                newitem: true,
                scaled: true,
                only_moved: true,
            };
        }
        if (this.depth != this.depth_previous) {
            dif.scaled = true;
            dif.same = false;
        }
        if (dif.same === true) {
            return true;
        }
        let horizontal;
        if (dif.newitem) {
            horizontal = elements.horizontal.clone();
        } else {
            horizontal = this.walls.horizontals[index];
        }

        const distance = Math.abs(newPoints.bottom.x - newPoints.top.x);
        if (dif.scaled) {
            const horizontalBox = boundingBox.setFromObject(elements.horizontal);
            const scale = distance / horizontalBox.size().x;
            horizontal.scale.setX(scale);
            horizontal.rotation.x = -Math.PI / 2;

            horizontal.scale.setZ(1);
            horizontal.scale.setY(this.depth / horizontalBox.size().y);
            horizontal.position.setY(newPoints.bottom.y);
            horizontal.position.setX(
                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2,
            );
        }

        if (dif.only_moved) {
            horizontal.position.setY(newPoints.bottom.y);
            horizontal.position.setX(
                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2,
            );
        }

        if (dif.newitem) {
            horizontal.name = 'horizontal';
            this.scene.add(horizontal);
            this.walls.horizontals.push(horizontal);
        }
    }

    createLegs(position) {
        const leg = elements.leg.clone();
        leg.position.setX(position.x1);
        leg.position.setZ(position.z1);
        leg.position.setY(-10);
        leg.rotation.x = leg.rotation.x + 90 * Math.PI / 180;
        leg.name = 'leg';
        this.scene.add(leg);
        this.walls.legs.push(leg);
    }

    // the same as verticals, horizontals but also takes what type of shadow it is to know which element to clone

    createDrawers(position, size, rowHeights, shelf_type) {
        if (shelf_type === 0) {
            const door_group = new THREE.Group();
            const { scene } = this;
            door_group.name = 'drawer group';
            const offset = 2;
            const drawer_cutout = 13;
            const front_handling_size = 20;
            const front_offset_from_top = 4;
            const front_height_offset = 37;
            const front_thickness = 18;

            const bottom_offset = 14;
            const bottom_thickness = 13;
            const bottom_depth = this.depth == 320 ? 245 : 325;

            const back_width_offset = 14;
            const back_thickness = 13;
            const back_height_a = 120;
            const back_height_b = 190;
            const back_height_c = 380 - 70;

            const sides_length = this.depth == 320 ? 245 : 325;
            const sides_thickness = 13;
            const sides_height_a = 120;
            const sides_height_b = 190;
            const sides_height_c = 380 - 70;

            // whole group
            door_group.position.setX(position.x);
            door_group.position.setY(position.y);
            door_group.position.setZ(position.z + 0);

            const row_number = getRowByY(position.y, rowHeights);

            const row_height = rowHeights[row_number];

            // front
            const door = elements.drawer_front.clone();
            const self = this;
            door.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    /* child.material = self.magical_materials_for_rows['doors'][row_number]; */

                }
            });
            door.name = 'szufladaFront';
            // console.log('color szuflady', ivy.material);

            door.position.setX(0);
            door.position.setY(
                position.y - front_handling_size / 2 - door_group.position.y,
            );
            door.position.setZ(0);

            let doorBox = boundingBox.setFromObject(elements.drawer_front);

            door.scale.setX((size.x + offset * 2) / doorBox.size().x);
            door.scale.setY((size.y - front_handling_size - offset) / doorBox.size().y);
            door.scale.setZ(size.z / doorBox.size().z);

            door_group.add(door);

            // handler
            const door_handler = elements.handle_drawer.clone();
            door_handler.name = 'Trzymadelko';

            door_handler.rotation.z = door_handler.rotation.z - 90 * Math.PI / 180;

            door_handler.rotation.y = door_handler.rotation.y + 90 * Math.PI / 180;

            // door_handler.position.setX(position.x+(handling_flip*((size.x/2)-(handling_size))) + (handling_flip *offset) - door_group.position.x);
            door_handler.position.setY(
                position.y
                + (size.y / 2 - front_handling_size)
                + offset
                - door_group.position.y + 3,
            );

            door_handler.position.setX(0);
            door_handler.position.setZ(0);

            doorBox = boundingBox.setFromObject(door_handler);
            const overscale = 1;
            door_handler.scale.setY(
                10,
            );
            door_handler.scale.setZ(size.x * overscale / doorBox.size().x);
            door_handler.scale.setX(size.z * overscale / doorBox.size().z);

            door_handler.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    child.material = self.magical_materials_for_rows.handlers[row_number];
                    child.material.needsUpdate = true;
                    child.geometry.center();
                }
            });
            door_group.add(door_handler);

            // Bottom

            const bottom = elements['support-drawer'].clone();

            bottom.name = 'szufladaBottom';

            bottom.position.setX(0);
            bottom.position.setY(
                position.y - size.y / 2 + drawer_cutout - door_group.position.y,
            );
            bottom.position.setZ(-bottom_depth / 2);
            bottom.rotation.x = Math.PI / 360;

            doorBox = boundingBox.setFromObject(bottom);
            bottom.scale.setX((size.x - bottom_offset) / doorBox.size().x);
            bottom.scale.setY(bottom_thickness / doorBox.size().y);
            bottom.scale.setZ(bottom_depth / doorBox.size().z);

            door_group.add(bottom);

            // Back

            const back = elements['support-drawer'].clone();

            back.name = 'szufladaBack';
            back.renderOrder = 10;
            // back.rotation.y = back.rotation.y + 90 * Math.PI / 180;
            back.rotation.x = -90 * Math.PI / 180;

            back.position.setX(0);
            // back.position.setY(((position.y - (front_handling_size / 2)) + drawer_cutout) - door_group.position.y);
            back.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? back_height_b : back_height_c)
                / 2,
            );
            back.position.setZ(-bottom_depth);

            doorBox = boundingBox.setFromObject(back);
            back.scale.setX(
                (size.x - bottom_offset + drawer_cutout) / doorBox.size().x,
            ); // was xyz before rotation
            back.scale.setZ(
                (row_height == this.row_a
                    ? back_height_a
                    : row_height == this.row_b ? back_height_b : back_height_c)
                / doorBox.size().y,
            );
            // back.scale.setX(back_thickness / doorBox.size().z);

            door_group.add(back);

            // side left

            const sideA = elements['support-drawer'].clone();

            sideA.name = 'szufladaSideA';

            // back.rotation.y =
            sideA.rotation.z = -90 * Math.PI / 180;
            sideA.rotation.x = Math.PI;
            sideA.position.setX(-(size.x / 2) + offset * 4);
            sideA.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / 2,
            );
            sideA.position.setZ(-bottom_depth / 2);

            doorBox = boundingBox.setFromObject(sideA);
            sideA.scale.setX(
                (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / doorBox.size().y,
            );
            sideA.scale.setZ(sides_length / doorBox.size().z);

            door_group.add(sideA);

            // side right

            const sideB = elements['support-drawer'].clone();

            sideB.name = 'szufladaSideB';

            sideB.rotation.z = 90 * Math.PI / 180;
            sideB.rotation.x = Math.PI;
            sideB.position.setX(size.x / 2 - offset * 4);
            sideB.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / 2,
            );
            sideB.position.setZ(-bottom_depth / 2);

            doorBox = boundingBox.setFromObject(sideB);

            // sideB.scale.setZ(sides_thickness / doorBox.size().x);
            sideB.scale.setX(
                (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / doorBox.size().y,
            );
            sideB.scale.setZ(sides_length / doorBox.size().z);

            door_group.add(sideB);

            this.walls.drawers.push(door_group);
            door_group.position.setY(position.y + size.y / 2);

            // animation here
            this.drawers_lists[row_number].push(door_group);
            this.scene.add(door_group);
            scene.add(door_group);
            // console.log(this.drawers_lists);
        } if (shelf_type === 2) {
            const door_group = new THREE.Group();
            const { scene } = this;
            door_group.name = 'drawer group';
            const offset = 2;
            const drawer_cutout = 13;
            const front_handling_size = 20;
            const front_offset_from_top = 4;
            const front_height_offset = 37;
            const front_thickness = 18;

            const bottom_offset = 14;
            const bottom_thickness = 13;
            const bottom_depth = this.depth == 320 ? 245 : 325;

            const back_width_offset = 14;
            const back_thickness = 13;
            const back_height_a = 120;
            const back_height_b = 190;
            const back_height_c = 380 - 70;

            const sides_length = this.depth == 320 ? 245 : 325;
            const sides_thickness = 13;
            const sides_height_a = 120;
            const sides_height_b = 190;
            const sides_height_c = 380 - 70;

            // whole group
            door_group.position.setX(position.x);
            door_group.position.setY(position.y);
            door_group.position.setZ(position.z + 0);

            const row_number = getRowByY(position.y, rowHeights);

            const row_height = rowHeights[row_number];

            const door = elements.fdoors.clone();
            // let row_number = getRowByY(position.y, rowHeights);
            door.name = 'Drzwi';

            door.position.setX(
                position.x - door_group.position.x,
            );
            door.position.setY(-front_handling_size / 2);
            door.position.setZ(0);

            let doorBox = boundingBox.setFromObject(elements.door);

            // door.scale.setY(((size.y - offset) / doorBox.size().y)/10);
            door.scale.setY(((size.y - front_handling_size - offset) / doorBox.size().y) / 10);
            door.scale.setX(((size.x - offset) / doorBox.size().x) / 10);
            door.scale.setZ((size.z / doorBox.size().z) / 10);

            door_group.add(door);
            this.walls.doors.push(door);

            // Create door handle
            const door_handler = elements.fhandle.clone();
            door_handler.rotation.z = -Math.PI / 2;
            door_handler.scale.setX(0.1);
            door_handler.scale.setY(((size.x - offset) / doorBox.size().x));
            door_handler.scale.setZ(0.10);

            door_handler.position.setX(position.x - door_group.position.x);
            door_handler.position.setY(size.y / 2 - front_handling_size * 2);

            door_group.add(door_handler);
            door_handler.name = 'fhandle';


            // Bottom

            const bottom = elements['support-drawer'].clone();

            bottom.name = 'szufladaBottom';

            bottom.position.setX(0);
            bottom.position.setY(
                position.y - size.y / 2 + drawer_cutout - door_group.position.y,
            );
            bottom.position.setZ(-bottom_depth / 2);
            bottom.rotation.x = Math.PI / 360;

            doorBox = boundingBox.setFromObject(bottom);
            bottom.scale.setX((size.x - bottom_offset) / doorBox.size().x);
            bottom.scale.setY(bottom_thickness / doorBox.size().y);
            bottom.scale.setZ(bottom_depth / doorBox.size().z);

            door_group.add(bottom);

            // Back

            const back = elements['support-drawer'].clone();

            back.name = 'szufladaBack';
            back.renderOrder = 10;
            // back.rotation.y = back.rotation.y + 90 * Math.PI / 180;
            back.rotation.x = -90 * Math.PI / 180;

            back.position.setX(0);
            // back.position.setY(((position.y - (front_handling_size / 2)) + drawer_cutout) - door_group.position.y);
            back.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? back_height_b : back_height_c)
                / 2,
            );
            back.position.setZ(-bottom_depth);

            doorBox = boundingBox.setFromObject(back);
            back.scale.setX(
                (size.x - bottom_offset + drawer_cutout) / doorBox.size().x,
            ); // was xyz before rotation
            back.scale.setZ(
                (row_height == this.row_a
                    ? back_height_a
                    : row_height == this.row_b ? back_height_b : back_height_c)
                / doorBox.size().y,
            );
            // back.scale.setX(back_thickness / doorBox.size().z);

            door_group.add(back);

            // side left

            const sideA = elements['support-drawer'].clone();

            sideA.name = 'szufladaSideA';

            // back.rotation.y =
            sideA.rotation.z = -90 * Math.PI / 180;
            sideA.rotation.x = Math.PI;
            sideA.position.setX(-(size.x / 2) + offset * 6);
            sideA.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / 2,
            );
            sideA.position.setZ(-bottom_depth / 2);

            doorBox = boundingBox.setFromObject(sideA);
            sideA.scale.setX(
                (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / doorBox.size().y,
            );
            sideA.scale.setZ(sides_length / doorBox.size().z);

            door_group.add(sideA);

            // side right

            const sideB = elements['support-drawer'].clone();

            sideB.name = 'szufladaSideB';

            sideB.rotation.z = 90 * Math.PI / 180;
            sideB.rotation.x = Math.PI;
            sideB.position.setX(size.x / 2 - offset * 6);
            sideB.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / 2,
            );
            sideB.position.setZ(-bottom_depth / 2);

            doorBox = boundingBox.setFromObject(sideB);

            // sideB.scale.setZ(sides_thickness / doorBox.size().x);
            sideB.scale.setX(
                (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / doorBox.size().y,
            );
            sideB.scale.setZ(sides_length / doorBox.size().z);

            door_group.add(sideB);

            this.walls.drawers.push(door_group);
            door_group.position.setY(position.y + size.y / 2);

            // animation here
            this.drawers_lists[row_number].push(door_group);
            this.scene.add(door_group);
            scene.add(door_group);
            // console.log(this.drawers_lists);
        } else if (shelf_type) {
            const door_group = new THREE.Group();
            const { scene } = this;
            const door_handler_width = 130;
            const door_handler_height = 20;
            const door_handler_depth = 30;
            const handling_overlap = 20;

            door_group.name = 'drawer group';
            const offset = 2;
            const drawer_cutout = 13;
            const front_handling_size = 20;
            const front_offset_from_top = 4;
            const front_height_offset = 37;
            const front_thickness = 18;

            const bottom_offset = 14;
            const bottom_thickness = 13;
            const bottom_depth = this.depth == 320 ? 245 : 325;

            const back_width_offset = 14;
            const back_thickness = 13;
            const back_height_a = 120;
            const back_height_b = 190;
            const back_height_c = 380 - 70;

            const sides_length = this.depth == 320 ? 245 : 325;
            const sides_thickness = 13;
            const sides_height_a = 120;
            const sides_height_b = 190;
            const sides_height_c = 380 - 70;

            // whole group
            door_group.position.setX(position.x);
            door_group.position.setY(position.y);
            door_group.position.setZ(position.z + 0);

            const row_number = getRowByY(position.y, rowHeights);

            const row_height = rowHeights[row_number];

            // front
            const door = elements.door.clone();
            const self = this;
            door.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    /* child.material = self.magical_materials_for_rows['doors'][row_number]; */

                }
            });
            door.name = 'szufladaFront';
            // console.log('color szuflady', ivy.material);

            door.position.setX(0);
            door.position.setY(
                position.y - door_group.position.y,
            );
            door.position.setZ(0);

            let doorBox = boundingBox.setFromObject(elements.drawer_front);

            door.scale.setX((size.x - offset) / doorBox.size().x);
            door.scale.setY((size.y - offset) / doorBox.size().y);
            door.scale.setZ(size.z / doorBox.size().z);

            door_group.add(door);

            // handler

            const drawer_handler = elements.handle_short_left.clone();
            drawer_handler.name = 'Handler';
            // console.log('posy', size.y);
            drawer_handler.rotation.x = -Math.PI / 2;
            drawer_handler.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset);
            drawer_handler.position.setY(size.y / 2 - door_handler_height / 2);
            drawer_handler.position.setZ(size.z - plankScaleMM / 2 - 1);

            doorBox = boundingBox.setFromObject(drawer_handler);
            drawer_handler.scale.setY(1);
            drawer_handler.scale.setX(1);
            drawer_handler.scale.setZ(1);

            drawer_handler.traverse(child => {
                if (
                    child instanceof THREE.Mesh
                    && typeof child.material !== 'undefined'
                ) {
                    child.material.needsUpdate = true;
                    child.geometry.center();
                }
            });
            door_group.add(drawer_handler);

            const door_handler_shadow = elements.handle_short_left_shadow.clone();
            door_handler_shadow.name = 'Handler shadow';

            door_handler_shadow.rotation.x = -Math.PI / 2;
            door_handler_shadow.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset * 2);
            door_handler_shadow.position.setY(size.y / 2 - door_handler_height / 2);
            door_handler_shadow.position.setZ(size.z + 2 + 5);

            doorBox = boundingBox.setFromObject(door_handler_shadow);
            door_handler_shadow.scale.setY(1);
            door_handler_shadow.scale.setX(1);
            door_handler_shadow.scale.setZ(1);

            door_handler_shadow.traverse(child => {
                if (
                    child instanceof THREE.Mesh
                    && typeof child.material !== 'undefined'
                ) {
                    child.material.needsUpdate = true;
                    child.geometry.center();
                }
            });

            door_group.add(door_handler_shadow);

            // Bottom

            const bottom = elements['support-drawer'].clone();

            bottom.name = 'szufladaBottom';

            bottom.position.setX(0);
            bottom.position.setY(
                position.y - size.y / 2 + drawer_cutout - door_group.position.y,
            );
            bottom.position.setZ(-bottom_depth / 2);
            bottom.rotation.x = Math.PI / 360;

            doorBox = boundingBox.setFromObject(bottom);
            bottom.scale.setX((size.x - bottom_offset) / doorBox.size().x);
            bottom.scale.setY(bottom_thickness / doorBox.size().y);
            bottom.scale.setZ(bottom_depth / doorBox.size().z);

            door_group.add(bottom);

            // Back

            const back = elements['support-drawer'].clone();

            back.name = 'szufladaBack';

            // back.rotation.y = back.rotation.y + 90 * Math.PI / 180;
            back.rotation.x = -90 * Math.PI / 180;

            back.position.setX(0);
            // back.position.setY(((position.y - (front_handling_size / 2)) + drawer_cutout) - door_group.position.y);
            back.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? back_height_b : back_height_c)
                / 2,
            );
            back.position.setZ(-bottom_depth);

            doorBox = boundingBox.setFromObject(back);
            back.scale.setX(
                (size.x - bottom_offset + drawer_cutout - offset * 6) / doorBox.size().x,
            ); // was xyz before rotation
            back.scale.setZ(
                (row_height == this.row_a
                    ? back_height_a
                    : row_height == this.row_b ? back_height_b : back_height_c)
                / doorBox.size().y,
            );
            // back.scale.setX(back_thickness / doorBox.size().z);

            door_group.add(back);

            // side left

            const sideA = elements['support-drawer'].clone();

            sideA.name = 'szufladaSideA';

            // back.rotation.y =
            sideA.rotation.z = -90 * Math.PI / 180;
            sideA.rotation.x = Math.PI;
            sideA.position.setX(-(size.x / 2) + offset * 5);
            sideA.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / 2,
            );
            sideA.position.setZ(-bottom_depth / 2);

            doorBox = boundingBox.setFromObject(sideA);
            sideA.scale.setX(
                (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / doorBox.size().y,
            );
            sideA.scale.setZ(sides_length / doorBox.size().z);

            door_group.add(sideA);

            // side right

            const sideB = elements['support-drawer'].clone();

            sideB.name = 'szufladaSideB';

            sideB.rotation.z = 90 * Math.PI / 180;
            sideB.rotation.x = Math.PI;
            sideB.position.setX(size.x / 2 - offset * 5);
            sideB.position.setY(
                position.y
                - size.y / 2
                + drawer_cutout
                - door_group.position.y
                + (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / 2,
            );
            sideB.position.setZ(-bottom_depth / 2);

            doorBox = boundingBox.setFromObject(sideB);

            // sideB.scale.setZ(sides_thickness / doorBox.size().x);
            sideB.scale.setX(
                (row_height == this.row_a
                    ? sides_height_a
                    : row_height == this.row_b ? sides_height_b : sides_height_c)
                / doorBox.size().y,
            );
            sideB.scale.setZ(sides_length / doorBox.size().z);

            door_group.add(sideB);

            this.walls.drawers.push(door_group);
            door_group.position.setY(position.y + size.y / 2);

            // animation here
            this.drawers_lists[row_number].push(door_group);
            this.scene.add(door_group);
            scene.add(door_group);
            scene.updateMatrix();
        }
    }

    getLeftRightWallPosition(points) {
        // console.log('getSidePanelsPosition', points.verticals);
        const topLeft = [];
        let tempMax = 0;
        points.verticals.map(item => {
            if (Math.abs(item.x1) > tempMax || Math.abs(item.x2) > tempMax) {
                tempMax = Math.abs(item.x1) > Math.abs(item.x2) ? item.x1 : item.x2
            }
        });

        points.verticals.map(item => {
            if (Math.abs(item.x1) === tempMax) {
                topLeft.push(item);
            }
        });

        return topLeft
    }

    getTopBottomWallPosition(points) {
        let topWallY = 18;
        const bottomWallY = 0;
        points.horizontals.forEach(item => {
            if (topWallY > item.y1) return
            topWallY = item.y1
        });
        const topBottom = points.horizontals.filter(item => item.y1 === topWallY || item.y1 === bottomWallY);
        return topBottom;
    }

    getAdditionalHorizontalPanelPosition(points) {
        const minX = _.min(points.horizontals.map(x => _.min([x.x1, x.x2])));
        const maxX = _.max(points.horizontals.map(x => _.max([x.x1, x.x2])));
        const panelPoints = [];

        for (const i of points.horizontals) {
            const localMinX = _.min([i.x1, i.x2]);
            const localMaxX = _.max([i.x1, i.x2]);
            if (localMinX > minX) {
                panelPoints.push({ x: localMinX - 1, y: i.y1 })
            }
            if (localMaxX < maxX) {
                panelPoints.push({ x: localMaxX + 1, y: i.y1 })
            }
        }
        return panelPoints; // topBottom;
    }

    createVerticalPanels(index, oldPoints, newPoints, pattern) {
        let dif;
        const { scene } = this;

        if (typeof oldPoints !== 'undefined') {
            dif = this.compare_dnas(oldPoints, newPoints);
        } else {
            dif = {
                newitem: true,
                scaled: true,
                only_moved: true,
            };
        }
        if (this.depth != this.depth_previous) {
            dif.scaled = true;
            dif.same = false;
        }
        if (dif.same === true) {
            return true;
        }

        let wall;

        if (dif.newitem) {
            wall = elements['left-right'].clone();
        } else {
            wall = this.walls.leftRightWalls[index];
        }

        const distance = Math.abs(newPoints.bottom.y - newPoints.top.y);
        const scale = (distance + 3) / 100;
        // LEFT/RIGHT - half of horizontal edge width
        let factor = 0;

        if (newPoints.bottom.x < 0) {
            // Left panel
            factor = -7.5;
        } else {
            // Right panel
            factor = 7.5;
        }
        // chowanie dla slanta scianki
        if (pattern == 0) {
            factor = -factor;
        }

        if (dif.scaled) {
            const leftRightWall = boundingBox.setFromObject(elements['left-right']);
            wall.rotation.x = -Math.PI / 2;
            wall.scale.setY(this.depth / 100);
            wall.scale.setZ(scale);


            wall.position.setY(newPoints.bottom.y - 1.5);
            wall.position.setX(newPoints.bottom.x + factor);
        }
        //
        if (dif.only_moved) {
            wall.position.setY(newPoints.bottom.y - 1.5);
            wall.position.setX(newPoints.bottom.x + factor);
            wall.scale.setY(this.depth / 100);
            wall.scale.setZ(scale);
        }
        if (dif.newitem) {
            wall.name = 'Left-right-wall';
            this.scene.add(wall);
            this.walls.leftRightWalls.push(wall);
        }
    }


    createAdditionalHorizontalPanels(newPoints) {
        let dif;
        const { scene } = this;


        const panel = elements['horizontal-plug'].clone();
        panel.rotation.x = -Math.PI / 2;

        panel.position.setY(newPoints.y);
        panel.position.setX(newPoints.x);
        // panel.position.setZ();

        const panelBox = boundingBox.setFromObject(elements['horizontal-plug']);

        panel.scale.setY(this.depth / panelBox.size().y);


        panel.name = 'additionalHorizontalPanel';
        this.scene.add(panel);
        this.walls.additionalHorizontalElements.push(panel);
    }

    createHorizontalPanels(index, oldPoints, newPoints) {
        let dif;
        const { scene } = this;
        if (typeof oldPoints !== 'undefined') {
            dif = this.compare_dnas(oldPoints, newPoints);
        } else {
            dif = {
                newitem: true,
                scaled: true,
                only_moved: true,
            };
        }
        if (this.depth != this.depth_previous) {
            dif.scaled = true;
            dif.same = false;
        }
        if (dif.same === true) {
            return true;
        }

        let wall;

        const distance = Math.abs(newPoints.bottom.x - newPoints.top.x);

        // wall = elements["top-bottom"].clone();
        // wall.scale.y = 3.2;
        // wall.rotation.x = - Math.PI/2;
        // wall.position.y = index ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;
        // wall.name = "wall";
        // this.scene.add(wall);

        if (dif.newitem) {
            wall = elements['top-bottom'].clone();
        } else {
            wall = this.walls.topBottomWalls[index];
        }
        //
        // let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);
        if (dif.scaled) {
            const topBottomWall = boundingBox.setFromObject(elements['top-bottom']);
            const scale = distance / topBottomWall.size().x;
            wall.scale.setX(scale);
            wall.rotation.x = -Math.PI / 2;

            if (dif.newitem) {
                wall.scale.setZ(1);
            }
            wall.scale.setY(this.depth / topBottomWall.size().y);

            wall.position.y = newPoints.bottom.y !== 0 ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;
            wall.position.setX(
                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2,
            );
        }
        //
        if (dif.only_moved) {
            // wall.position.setY(newPoints.bottom.y);
            wall.position.y = newPoints.bottom.y !== 0 ? (newPoints.bottom.y + 7.5) : (newPoints.bottom.y) - 7.5;
            wall.position.setX(
                Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2,
            );
        }
        //
        if (dif.newitem) {
            wall.name = 'wall';
            this.scene.add(wall);
            this.walls.topBottomWalls.push(wall);
        }
    }

    createDoor(position, size, door_type, door_flip, rowHeights, shelf_type) {
        const handling_flip = door_flip === 1 ? 1 : -1;
        if (shelf_type === 0) {
            const door_group = new THREE.Group();
            door_group.name = 'door group';

            const handling_overlap = 20;
            // let handling_overlap = 20;
            const handling_size = door_type === 0 ? 30 : 10;
            const handling_flip = door_flip === 1 ? 1 : -1;
            const offset = 2;
            const item_to_use = door_type === 0 ? elements.handle_big : elements.handle_small;

            door_group.position.setX(position.x - handling_flip * (size.x / 2));
            door_group.position.setY(position.y + size.y / 2);
            door_group.position.setZ(position.z);

            const door = elements.door.clone();
            const row_number = getRowByY(position.y, rowHeights);
            const self = this;
            door.name = 'Drzwi';

            door.position.setX(
                position.x - handling_flip * handling_size / 2 - door_group.position.x,
            );
            door.position.setY(0);
            door.position.setZ(0);

            let doorBox = boundingBox.setFromObject(elements.door);

            door.scale.setY((size.y - offset) / doorBox.size().y);
            door.scale.setX((size.x - handling_size - offset) / doorBox.size().x);
            door.scale.setZ(size.z / doorBox.size().z);

            door_group.add(door);
            this.walls.doors.push(door);

            const door_handler = item_to_use.clone();
            door_handler.name = 'Trzymadelko';

            door_handler.position.setX(
                position.x
                + handling_flip * (size.x / 2 - handling_size)
                + handling_flip * offset
                - door_group.position.x,
            );
            if (handling_flip === 1) {
                door_handler.position.x -= offset * 2;
            }

            door_handler.position.setY(0);
            door_handler.position.setZ(0);
            doorBox = boundingBox.setFromObject(item_to_use);
            door_handler.scale.setY((size.y - offset) / doorBox.size().y);
            door_handler.scale.setX(
                (handling_size + handling_overlap) / doorBox.size().x,
            );
            door_handler.scale.setZ(1);

            door_handler.traverse(child => {
                if (
                    child instanceof THREE.Mesh
                    && typeof child.material !== 'undefined'
                ) {
                    child.material = self.magical_materials_for_rows.handlers[row_number];
                    child.material.needsUpdate = true;
                    if (handling_flip > 0) {
                        child.rotation.y = child.rotation.y + 180 * Math.PI / 180;
                    }
                    child.geometry.center();
                }
            });

            door_group.add(door_handler);
            this.walls.doors.push(door_handler);

            // animation here
            door_group.rotate_sign = handling_flip;
            this.door_lists[row_number].push(door_group);
            this.walls.door_groups.push(door_group);
            this.scene.add(door_group);
        } else if (shelf_type === 2) {
            const handling_overlap = 20;
            const front_handling_size = 20;
            const offset = 2;

            // Create door group
            const door_group = new THREE.Group();
            door_group.name = 'door2 group';

            door_group.position.setX(position.x - handling_flip * (size.x / 2));
            door_group.position.setY(position.y + size.y / 2);
            door_group.position.setZ(position.z);

            // Create door front
            const door = elements.fdoors.clone();
            const row_number = getRowByY(position.y, rowHeights);
            door.name = 'Drzwi';

            door.position.setX(
                position.x - door_group.position.x,
            );
            door.position.setY(-front_handling_size / 2);
            door.position.setZ(0);

            const doorBox = boundingBox.setFromObject(elements.door);

            // door.scale.setY(((size.y - offset) / doorBox.size().y)/10);
            door.scale.setY(((size.y - front_handling_size - offset) / doorBox.size().y) / 10);
            door.scale.setX(((size.x - offset) / doorBox.size().x) / 10);
            door.scale.setZ((size.z / doorBox.size().z) / 10);

            door_group.add(door);
            this.walls.doors.push(door);

            // Create door handle
            const door_handler = elements.fhandle.clone();
            door_handler.rotation.z = -Math.PI / 2;
            door_handler.scale.setX(0.1);
            door_handler.scale.setY(((size.x - offset) / doorBox.size().x));
            door_handler.scale.setZ(0.10);

            door_handler.position.setX(position.x - door_group.position.x);
            door_handler.position.setY(
                size.y / 2 - front_handling_size * 2,
            );


            door_group.add(door_handler);

            door_handler.name = 'fhandle';

            // animation here
            door_group.rotate_sign = handling_flip;
            this.door_lists[row_number].push(door_group);
            this.walls.door_groups.push(door_group);
            this.scene.add(door_group);
        } else {
            //  TYPE-02
            const door_group = new THREE.Group();
            door_group.name = 'door group';
            const door_handler_width = 130;
            const door_handler_height = 20;
            const door_handler_depth = 30;
            const handling_overlap = 20;
            // let handling_overlap = 20;
            const handling_size = 0;
            const handling_flip = door_flip === 1 ? 1 : -1;
            const offset = 2;


            door_group.position.setX(position.x - handling_flip * (size.x / 2));
            door_group.position.setY(position.y + size.y / 2);
            door_group.position.setZ(position.z);

            const door = elements.door.clone();
            const row_number = getRowByY(position.y, rowHeights);
            const self = this;
            door.name = 'Drzwi';

            door.position.setX(
                position.x - handling_flip - door_group.position.x,
            );
            door.position.setY(0);
            door.position.setZ(0);

            let doorBox = boundingBox.setFromObject(elements.door);

            door.scale.setX((size.x - offset) / doorBox.size().x);
            door.scale.setY((size.y - offset) / doorBox.size().y);
            door.scale.setZ(size.z / doorBox.size().z);

            door_group.add(door);
            this.walls.doors.push(door);
            if (handling_flip === -1) {
                const door_handler = elements.handle_short_left.clone();
                door_handler.name = 'Handler';
                // console.log('posy', size.y);
                door_handler.rotation.x = -Math.PI / 2;
                door_handler.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset);
                door_handler.position.setY(size.y / 2 - door_handler_height / 2);
                door_handler.position.setZ(size.z - plankScaleMM / 2 - 1);

                doorBox = boundingBox.setFromObject(door_handler);
                door_handler.scale.setY(1);
                door_handler.scale.setX(1);
                door_handler.scale.setZ(1);

                door_handler.traverse(child => {
                    if (
                        child instanceof THREE.Mesh
                        && typeof child.material !== 'undefined'
                    ) {
                        child.material.needsUpdate = true;
                        child.geometry.center();
                    }
                });
                door_group.add(door_handler);
                this.walls.doors.push(door_handler);

                const door_handler_shadow = elements.handle_short_left_shadow.clone();
                door_handler_shadow.name = 'Handler shadow';

                door_handler_shadow.rotation.x = -Math.PI / 2;
                door_handler_shadow.position.setX(position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset * 2);
                door_handler_shadow.position.setY(size.y / 2 - door_handler_height / 2);
                door_handler_shadow.position.setZ(size.z + 2 + 5);

                doorBox = boundingBox.setFromObject(door_handler_shadow);
                door_handler_shadow.scale.setY(1);
                door_handler_shadow.scale.setX(1);
                door_handler_shadow.scale.setZ(1);

                door_handler_shadow.traverse(child => {
                    if (
                        child instanceof THREE.Mesh
                        && typeof child.material !== 'undefined'
                    ) {
                        child.material.needsUpdate = true;
                        child.geometry.center();
                    }
                });
                door_group.add(door_handler_shadow);
                this.walls.doors.push(door_handler_shadow);
            }

            // animation here
            door_group.rotate_sign = handling_flip;
            this.door_lists[row_number].push(door_group);
            this.walls.door_groups.push(door_group);
            this.scene.add(door_group);
        }
    }

    setDnaTool(dnaTools) {
        this.dnaTools = dnaTools;
    }

    setMaterialColor({
        color,
        shelf_type,
        skipTracking = true,

    }) {
        const loaclElements = this.elements;
        const { walls } = this;
        const magicalElements = this.magical_materials_for_rows;

        /* if (!window.render_test) {
             return self.setOldColor(value,skipTracking);
         }
         */

        skipTracking = skipTracking || false;

        let opacity;
        let hex_color;
        let hex_handler_color;
        let backs_color;
        let reflectivityValue;
        let reflectivityValueDoors;
        let material_id;
        [
            material_id,
            color,
            opacity,
            hex_color,
            hex_handler_color,
            backs_color,
            reflectivityValue,
            reflectivityValueDoors,
        ] = getDefinitionForMaterial(color, shelf_type || window.cstm.item.shelf_type);
        // change base elements based on color
        const reflectionCube = loaclElements.cubemap;

        loaclElements.vertical.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-vert`];
                child.material.needsUpdate = true;
                // child.material.color = new THREE.Color(1,1,1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
            }
        });


        loaclElements.horizontal.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-hori`];
                child.material.needsUpdate = true;
                // child.material.color = new THREE.Color(1,1,1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
            }
        });

        loaclElements['horizontal-plug'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-hori`];
                child.material.needsUpdate = true;
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
            }
        });

        loaclElements.support.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = elements[`${color}-support`];
                child.material.needsUpdate = true;
                child.material.color = new THREE.Color(1, 1, 1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
                child.renderOrder = 40;
            }
        });

        loaclElements['support-drawer'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-support-drawer`];
                child.material.needsUpdate = true;
                child.material.color = new THREE.Color(1, 1, 1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
            }
        });

        loaclElements.shadow.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-shadowbox`];
                child.material.needsUpdate = true;
                child.material.color = new THREE.Color(1, 1, 1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
                child.renderOrder = 0;
            }
        });

        loaclElements['shadow-left'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-shadowbox`];
                child.material.needsUpdate = true;
                child.material.color = new THREE.Color(1, 1, 1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
            }
        });

        this.setProperDoorsAndDrawersModel(material_id);

        loaclElements['shadow-right'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-shadowbox`];
                child.material.needsUpdate = true;
                child.material.color = new THREE.Color(1, 1, 1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
            }
        });

        loaclElements['left-right'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-vert`];
                child.material.needsUpdate = true;
                // child.material.color = new THREE.Color(1,1,1);
                child.material.transparent = false;
                child.material.reflectivity = reflectivityValue;
            }
        });

        loaclElements['top-bottom'].traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.dispose();
                child.material.map = loaclElements[`${color}-hori`];
                child.material.needsUpdate = true;
                // child.material.color = new THREE.Color(1,1,1);
                child.material.reflectivity = reflectivityValue;
            }
        });

        loaclElements.backs.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.color = new THREE.Color(backs_color);
                // child.material.reflectivity = reflectivityValue;
                child.renderOrder = -2000;
            }
        });


        for (let i = 0; i < magicalElements.doors.length; i++) {
            // build3d.magical_materials_for_rows['doors'][i].uniforms.blending_color.value = new THREE.Color(hex_color);
            magicalElements.handlers[i].uniforms.blending_color.value = new THREE.Color(hex_handler_color);
        }
    }

    createShadows(index, oldPoints, newPoints, type) {
        const { scene } = this;
        if (window.location.href.indexOf('noshadow') > -1) {
            return;
        }
        let dif;
        if (typeof oldPoints !== 'undefined') {
            dif = this.compare_shadows(oldPoints, newPoints);
        } else {
            dif = {
                newitem: true,
                scaled: true,
                only_moved: true,
            };
        }

        if (dif.same === true) {
            return true;
        }
        let shadow;


        const isShadowIn = (
            (this.context !== 'grid' && !this.isMobile)
            || (window.cstm_i18n.gridView === true)
            || (window.cstm_i18n.configuratorView && (window.is_mobile_loaded || window.is_tablet_loaded))

        );

        // let isShadowIn = true;
        let shadowIn;
        let shadowBox;

        if (dif.newitem) {
            switch (type) {
            case 0:
                shadow = elements.shadow.clone();
                shadowBox = boundingBox.setFromObject(elements.shadow);
                if (isShadowIn) shadowIn = elements.wall_compartment_shadow.clone();
                break;

            case 1:
                shadow = elements['shadow-left'].clone();
                shadowBox = boundingBox.setFromObject(elements['shadow-left']);
                break;

            case 2:
                shadow = elements['shadow-right'].clone();
                shadow.name = 'shadow-right';
                shadowBox = boundingBox.setFromObject(elements['shadow-right']);
                break;
            }
        } else {
            switch (type) {
            case 0:
                shadow = this.walls.shadows[0][index];
                shadowBox = boundingBox.setFromObject(elements.shadow);
                break;

            case 1:
                shadow = this.walls.shadows[1][index];
                shadowBox = boundingBox.setFromObject(elements['shadow-left']);
                break;

            case 2:
                shadow = this.walls.shadows[2][index];
                shadowBox = boundingBox.setFromObject(elements['shadow-right']);
                break;

            case 3:
                shadow = elements['support-left'].clone();
                shadowBox = boundingBox.setFromObject(elements.shadow);
                break;

            case 4:
                shadow = elements['support-right'].clone();
                shadowBox = boundingBox.setFromObject(elements.shadow);
                break;
            }
        }

        if (dif.scaled) {
            shadow.rotation.x = -Math.PI / 2;
            // scale for proper types of shadows
            shadow.scale.setX((newPoints.size.x + 3) / 100);
            shadow.scale.setZ((newPoints.size.y + 3) / 100);

            // ShadowIN
            if (typeof shadowIn !== 'undefined' && isShadowIn === true) {
                shadowIn.scale.setX((newPoints.size.x + 24) / 100);
                shadowIn.scale.setY((newPoints.size.y + 24) / 100);
                shadowIn.position.setX(newPoints.position.x + 7);
                shadowIn.position.setY(newPoints.position.y + newPoints.size.y / 2);
                shadowIn.position.setZ(-5);
            }

            if (type === 3 || type === 4) {
                shadow.scale.setZ(newPoints.size.z / shadowBox.size().z);
                shadow.position.setZ(newPoints.position.z);
            } else {
                shadow.scale.setY(this.depth / shadowBox.size().z);
            }
            shadow.position.setX(newPoints.position.x);
            shadow.position.setY(newPoints.position.y);
        }

        if (dif.moved) {
            shadow.position.setX(newPoints.position.x);
            shadow.position.setY(newPoints.position.y);
        }

        shadow.renderOrder = 10000000;

        if (dif.newitem) {
            scene.add(shadow);
            if (isShadowIn) scene.add(shadowIn);
            switch (type) {
            case 0:
                this.walls.shadows[0].push(shadow);
                if (isShadowIn) this.walls.shadows[4].push(shadowIn);
                break;
            case 1:
                this.walls.shadows[1].push(shadow);
                break;
            case 2:
                this.walls.shadows[2].push(shadow);
                break;
            case 3:
                shadow = elements['support-left'].clone();
                shadowBox = boundingBox.setFromObject(elements.shadow);
                break;
            case 4:
                shadow = elements['support-right'].clone();
                shadowBox = boundingBox.setFromObject(elements.shadow);
                break;
            }
        }

        if (isShadowIn) {
            //   this.wallShadowsObjects.push(shadowIn);
        }
    }

    getIndents(shelfSize) {
        /*
         return [
           ..._points.additional_elements.shadow_left,
           ..._points.additional_elements.shadow_right,
          // ..._points.backs,
         ].map((v) => [ v.x1, v.y1,  v.x2,  v.y2, 0]);
       */

        const delta = { x: -25, y: 150 - 25 };
        const shelfPosition = { x: -1, y: -1 };

        return [
            ..._points.additional_elements.shadow_left,
            ..._points.additional_elements.shadow_right,
        ]
            .map(shdw => ({
                x: shdw.x1,
                y: shdw.y1,
                z: shdw.x2,
                w: shdw.y2,
            }))
            .map(indent => ({
                w: indent.z - indent.x,
                h: indent.w - indent.y,
                x: indent.z,
                y: indent.w,
            }))
            .map((size, n) => {
                const half = 0.5;
                const fixWarp = { y: -16, x: 10 };

                const left = -size.x + size.w * half;
                const top = shelfSize[1] - Math.abs(size.y - size.h * half - fixWarp.y) - 30;

                return [left, top,
                    size.w * half + fixWarp.x,
                    size.h * half - fixWarp.y,
                ];
            });
    }

    // create the casted shadows which consists of 3 parts: sides and center, positioned flat on the ground below the shelf
    createCastShadows(width) {
        const { scene } = this;
        if (width === undefined) {
            width = this.width;
        }
        let shadowCenter; let shadowLeft; let
            shadowRight;
        shadowCenter = elements['cast-shadow-center'].clone();
        shadowRight = elements['cast-shadow-right'].clone();
        shadowLeft = elements['cast-shadow-left'].clone();

        for (let i = 0; i < this.walls.castShadows.length; i++) {
            scene.remove(this.walls.castShadows[i]);
        }

        this.walls.castShadows.length = 0;
        /*
            if (!shadowCastEnabled) {
                return;
            } */
        const positionBottom = -22;
        const positionZ = this.depth / 2;
        let shadowBox = boundingBox.setFromObject(elements['cast-shadow-center']);
        const scaleZ = this.depth * 2 / shadowBox.size().z;
        shadowCenter.scale.setX(width / shadowBox.size().x);
        shadowCenter.scale.setZ(scaleZ);
        shadowCenter.position.setY(positionBottom);
        shadowCenter.position.setZ(positionZ);
        this.walls.castShadows.push(shadowCenter);

        shadowCenter.renderOrder = 10000;

        shadowBox = boundingBox.setFromObject(elements['cast-shadow-right']);
        const scaleX = this.depth / 2 / shadowBox.size().x;

        shadowRight.scale.setX(scaleX);
        shadowRight.scale.setZ(scaleZ);
        shadowRight.position.setX(-width / 2 + this.depth / 2 / 2);
        shadowRight.position.setY(positionBottom);
        shadowRight.position.setZ(positionZ);
        this.walls.castShadows.push(shadowRight);

        shadowLeft.scale.setX(scaleX);
        shadowLeft.scale.setZ(scaleZ);
        shadowLeft.position.setX(width / 2 - this.depth / 2 / 2);
        shadowLeft.position.setY(positionBottom);
        shadowLeft.position.setZ(positionZ);
        this.walls.castShadows.push(shadowLeft);

        this.scene.add(shadowCenter);
        this.scene.add(shadowLeft);
        this.scene.add(shadowRight);
    }

    // compare dna points
    compare_dnas(old_one, new_one) {
        const result = {};

        // check if it is the same
        if (
            old_one.bottom.x === new_one.bottom.x
            && old_one.bottom.y === new_one.bottom.y
            && old_one.top.x === new_one.top.x
            && old_one.top.y === new_one.top.y
        ) {
            result.same = true;
            return result;
        }
        // check if it was only moved on X
        if (
            Math.abs(old_one.bottom.x - old_one.top.x)
            === Math.abs(new_one.bottom.x - new_one.top.x)
            && Math.abs(old_one.bottom.y - old_one.top.y)
            === Math.abs(new_one.bottom.y - new_one.top.y)
        ) {
            result.only_moved = true;
        }
        // check if it was scaled
        if (
            Math.abs(old_one.top.y - old_one.bottom.y)
            !== Math.abs(new_one.top.y - new_one.bottom.y)
            || Math.abs(old_one.top.x - old_one.bottom.x)
            !== Math.abs(new_one.top.x - new_one.bottom.x)
        ) {
            result.scaled = true;
        }

        return result;
    }

    // same as earlier, only the points are bit easier to compare
    compare_shadows(old_one, new_one) {
        const result = {};

        if (
            old_one.position.x === new_one.position.x
            && old_one.position.y === new_one.position.y
            && old_one.size.x === new_one.size.x
            && old_one.size.y === new_one.size.y
        ) {
            result.same = true;
            return result;
        }
        if (
            old_one.position.x !== new_one.position.x
            || old_one.position.y !== new_one.position.y
        ) {
            result.moved = true;
        }
        if (
            old_one.size.x !== new_one.size.x
            || old_one.size.y !== new_one.size.y
        ) {
            result.scaled = true;
        }

        return result;
    }

    get_only_points(ivy, pattern, motion, width, rows, rowHeight, rowStyles) {
        const points = this.dnaTools.get_elements(
            ivy.patterns[pattern].json,
            motion,
            width * 10,
            rows,
            rowHeight,
            rowStyles,
            320,
            9,
            125,
            true,
            [0, 0, 0, 0],
        );
        return points;
    }

    rebuildWalls(initial, snapping = false, skip_dna = false, ivy) {
        this.depth = ivy.depth;

        if (window.loadPresetFlag) {
            return;
        }
        const { scene } = this;
        if (sizePlane.length > 0) {
            for (let i = 0; i < sizePlane.length; i++) {
                scene.remove(sizePlane[i]);
            }
            sizePlane = [];
        }

        const initialRun = initial || false;
        if (initialRun && dnaStartWasSent == false) {
            if (typeof tracking !== 'undefined') {
                tracking.trackDnaStart(ivy.actual_dna_name);
                dnaStartWasSent = true;
            }
        } else {
            // this.send_customization();
        }
        const generateCounter = getUrlParameter('generateWalls') || 1;
        let points;
        if (skip_dna === true) {
            this.walls.shadows.forEach(shadow_list => {
                for (let i = 0; i < shadow_list.length; i++) {
                    this.scene.remove(shadow_list[i]);
                }
                shadow_list.length = 0;
            });
            const typical_elements = ['verticals', 'horizontals', 'supports', 'top-bottom'];

            typical_elements.forEach(element_type => {
                // check if there are more elements than on a previous run, if so remove them from scene
                for (let j = 0; j < this.walls[element_type].length; j++) {
                    this.scene.remove(this.walls[element_type][j]);
                }
                this.walls[element_type].length = 0;
            });
            points = {
                doors: [],
                horizontals: ivy.data.horizontals,
                legs: [],
                verticals: ivy.data.verticals,
                supports: ivy.data.supports,
                additional_elements: {
                    shadow_left: [],
                    shadow_middle: [],
                    shadow_right: [],
                    shadow_side: [],
                },
            };
            const shelf_rows = this.dnaTools.get_row_coor(
                ivy.rows,
                ivy.constants.rowHeight,
            );
            const simplified_data = this.dnaTools.generate_elements_simplified_data(
                points,
                shelf_rows,
                9,
            );
            const openings = this.dnaTools.get_shadows_openings(
                simplified_data,
                ivy.width,
                9,
            );
            points.additional_elements = this.dnaTools.get_shadows_geometry(
                openings,
                shelf_rows,
                ivy.width,
                9,
                ivy.depth,
            );
            points.legs = this.dnaTools.generate_legs(
                [[-ivy.width / 2, ivy.width / 2], simplified_data[1][1]],
                shelf_rows,
                ivy.depth,
            );
        } else {
            for (let i = 0; i < generateCounter; i++) {
                // old chaos
                if (ivy.patterns[ivy.pattern].json === undefined) {
                    const own_walls = ivy.patterns[4].generateWalls(
                        ivy.Property1,
                        ivy.width,
                        ivy.rows,
                        ivy.constants.rowHeight,
                        ivy.depth,
                        ivy.minX,
                        ivy.maxX,
                    );
                    points = {
                        doors: [],
                        horizontals: [],
                        legs: [],
                        verticals: [],
                        supports: [],
                        additional_elements: {
                            shadow_left: [],
                            shadow_middle: [],
                            shadow_right: [],
                            shadow_side: [],
                        },
                    };
                    for (let i = 0; i < own_walls.ptHorizontalsAll.length; i += 2) {
                        points.horizontals.push({
                            x1: own_walls.ptHorizontalsAll[i].x,
                            y1: own_walls.ptHorizontalsAll[i].y,
                            z1: own_walls.ptHorizontalsAll[i].z,
                            x2: own_walls.ptHorizontalsAll[i + 1].x,
                            y2: own_walls.ptHorizontalsAll[i + 1].y,
                            z2: own_walls.ptHorizontalsAll[i + 1].z,
                        });
                    }
                    for (let i = 0; i < own_walls.ptVerticalsAll.length; i += 2) {
                        points.verticals.push({
                            x2: own_walls.ptVerticalsAll[i].x,
                            y2: own_walls.ptVerticalsAll[i].y,
                            z2: own_walls.ptVerticalsAll[i].z,
                            x1: own_walls.ptVerticalsAll[i + 1].x,
                            y1: own_walls.ptVerticalsAll[i + 1].y,
                            z1: own_walls.ptVerticalsAll[i + 1].z,
                        });
                    }
                    for (let i = 0; i < own_walls.ptSupportsLeft.length; i += 2) {
                        points.supports.push({
                            x2: own_walls.ptSupportsLeft[i].x,
                            y2: own_walls.ptSupportsLeft[i].y,
                            z2: own_walls.ptSupportsLeft[i].z,
                            x1: own_walls.ptSupportsLeft[i + 1].x,
                            y1: own_walls.ptSupportsLeft[i + 1].y,
                            z1: own_walls.ptSupportsLeft[i + 1].z,
                        });
                    }
                    for (let i = 0; i < own_walls.ptSupportsRight.length; i += 2) {
                        points.supports.push({
                            x2: own_walls.ptSupportsRight[i].x,
                            y2: own_walls.ptSupportsRight[i].y,
                            z2: own_walls.ptSupportsRight[i].z,
                            x1: own_walls.ptSupportsRight[i + 1].x,
                            y1: own_walls.ptSupportsRight[i + 1].y,
                            z1: own_walls.ptSupportsRight[i + 1].z,
                        });
                    }
                    const shelf_rows = this.dnaTools.get_row_coor(
                        ivy.rows,
                        ivy.constants.rowHeight,
                    );
                    const simplified_data = this.dnaTools.generate_elements_simplified_data(
                        points,
                        shelf_rows,
                        9,
                    );
                    const openings = this.dnaTools.get_shadows_openings(
                        simplified_data,
                        ivy.width,
                        9,
                    );
                    points.additional_elements = this.dnaTools.get_shadows_geometry(
                        openings,
                        shelf_rows,
                        ivy.width,
                        9,
                        ivy.depth,
                    );
                    points.legs = this.dnaTools.generate_legs(
                        [[-this.width / 2, this.width / 2], simplified_data[1][1]],
                        shelf_rows,
                        ivy.depth,
                    );
                    ivy.actual_dna_name = 'old chaos';
                } else {
                    if (this.row_styles_presets !== -1) {
                        ivy.constants.rowStyles = this.dnaTools.get_row_styles_list(
                            this.dnaTools.get_row_styles(ivy.patterns[ivy.pattern].json),
                            row_styles_presets,
                            ivy.rows,
                        );
                    }
                    points = preparePoints(this.dnaTools,
                        ivy.patterns[ivy.pattern].json,
                        ivy.Property1,
                        ivy.width,
                        ivy.rows,
                        ivy.constants.rowHeight,
                        ivy.constants.rowStyles,
                        ivy.depth,
                        9,
                        125,
                        snapping || initial,
                        [0, 0, 0, 0], true, -1, ivy.backpanel_styles, ivy.shelf_type);
                    points.topBottomWalls = this.getTopBottomWallPosition(points);
                    points.leftRightWalls = this.getLeftRightWallPosition(points);
                    points.additionalHorizontalElements = this.getAdditionalHorizontalPanelPosition(points);

                    _points = points;

                    if (ivy.patterns[ivy.pattern].json.dna_name !== undefined) {
                        ivy.actual_dna_name = ivy.patterns[ivy.pattern].json.dna_name || '';
                    }
                }
            }
        }
        const rebuildCounter = getUrlParameter('rebuild') || 1;
        const shadow_translator = {
            shadow_middle: 0,
            shadow_left: 1,
            shadow_right: 2,
            shadow_side: 0,
        };
        /* if (isMobile) { doorFlagOn = true } */
        for (let j = 0; j < rebuildCounter; j++) {
            const typical_elements = ['verticals', 'horizontals', 'supports', 'backs', 'topBottomWalls', 'leftRightWalls'];
            typical_elements.forEach(element_type => {
                // check if there are more elements than on a previous run, if so remove them from scene
                if (this.points[element_type] === undefined) {
                    this.points[element_type] = [];
                }
                if (element_type == 'horizontals') {
                    this.points[element_type].sort((x, y) => (x.y1 != y.y1 ? x.y1 - y.y1 : x.x1 - y.x1));
                    points[element_type].sort((x, y) => (x.y1 != y.y1 ? x.y1 - y.y1 : x.x1 - y.x1));
                }

                if (this.points[element_type] && this.points[element_type].length > 0 && points[element_type].length < this.points[element_type].length) {
                    const dif = this.points[element_type].length - points[element_type].length;

                    if (this.walls[element_type]) {
                        for (let j = this.walls[element_type].length - dif; j < this.walls[element_type].length; j++) {
                            scene.remove(this.walls[element_type][j]);
                        }
                        this.walls[element_type].length = this.walls[element_type].length - dif < 0 ? 0 : this.walls[element_type].length - dif;
                    }
                }

                // arrays for holding points
                const newPoints = [];
                const oldPoints = [];
                // build points

                for (let i = 0; i < points[element_type].length; i++) {
                    // if there are points push them for comparison
                    if (
                        typeof this.points[element_type][i] !== 'undefined'
                        && !initialRun
                    ) {
                        newPoints.push({
                            bottom: new THREE.Vector3(
                                points[element_type][i].x1,
                                points[element_type][i].y1,
                                points[element_type][i].z1,
                            ),
                            top: new THREE.Vector3(
                                points[element_type][i].x2,
                                points[element_type][i].y2,
                                points[element_type][i].z2,
                            ),
                        });
                        oldPoints.push({
                            bottom: new THREE.Vector3(
                                this.points[element_type][i].x1,
                                this.points[element_type][i].y1,
                                this.points[element_type][i].z1,
                            ),
                            top: new THREE.Vector3(
                                this.points[element_type][i].x2,
                                this.points[element_type][i].y2,
                                this.points[element_type][i].z2,
                            ),
                        });
                    } else {
                        // if there is more points add new ones
                        newPoints.push({
                            bottom: new THREE.Vector3(
                                points[element_type][i].x1,
                                points[element_type][i].y1,
                                points[element_type][i].z1,
                            ),
                            top: new THREE.Vector3(
                                points[element_type][i].x2,
                                points[element_type][i].y2,
                                points[element_type][i].z2,
                            ),
                        });
                    }
                }
                // creating points for top and bottom;

                // create verticals from these points aray
                for (let i = 0; i < newPoints.length; i++) {
                    if (element_type === 'verticals') {
                        this.createSingleVertical(i, oldPoints[i], newPoints[i]);
                    } else if (element_type === 'horizontals') {
                        this.createHorizontal(i, oldPoints[i], newPoints[i]);
                    } else if (element_type === 'supports') {
                        let supportType;
                        if ((points[element_type][i].x2 + points[element_type][i].x1) < 0) {
                            supportType = Math.abs(points[element_type][i].x2) > Math.abs(points[element_type][i].x1) ? 'left' : 'right';
                        } else {
                            supportType = Math.abs(points[element_type][i].x2) < Math.abs(points[element_type][i].x1) ? 'left' : 'right';
                        }
                        this.createSingleSupport(i, oldPoints[i], newPoints[i], supportType);
                    } else if (element_type === 'backs') {
                        this.createSingleBack(i, oldPoints[i], newPoints[i]);
                    } else if (element_type === 'topBottomWalls') {
                        this.createHorizontalPanels(i, oldPoints[i], newPoints[i]);
                    } else if (element_type === 'leftRightWalls') {
                        this.createVerticalPanels(i, oldPoints[i], newPoints[i], ivy.pattern);
                    }
                }
            });

            // create shadows


            const shadows = points.additional_elements;
            const getSizeFromPoint = function(item) {
                return new THREE.Vector3(
                    Math.abs(item.x1 - item.x2),
                    Math.abs(item.y1 - item.y2),
                    Math.abs(item.z1 - item.z2),
                );
            };
            const getPositionFromPoint = function(item) {
                return new THREE.Vector3(
                    (item.x1 + item.x2) / 2,
                    item.y1 - 1.5,
                    (item.z1 + item.z2) / 2,
                );
            };
            this.walls.shadows.forEach(shadow_list => {
                for (let i = 0; i < shadow_list.length; i++) {
                    scene.remove(shadow_list[i]);
                }
                shadow_list.length = 0;
            });
            for (const shadow_type in shadows) {
                if (shadow_type === 'styles') {
                    continue; // as its style for doors, not shadow
                }
                const shadow_list = shadows[shadow_type];

                const newPoints = [];
                for (let i = 0; i < shadow_list.length; i++) {
                    newPoints.push({
                        size: getSizeFromPoint(shadow_list[i]),
                        position: getPositionFromPoint(shadow_list[i]),
                    });
                }
                for (let i = 0; i < newPoints.length; i++) {
                    this.createShadows(
                        i,
                        undefined,
                        newPoints[i],
                        shadow_translator[shadow_type],
                    );
                }
            }


            for (let i = 0; i < this.walls.additionalHorizontalElements.length; i++) {
                scene.remove(this.walls.additionalHorizontalElements[i]);
            }
            this.walls.additionalHorizontalElements.length = 0;
            // create double openings
            for (let i = 0; i < points.additionalHorizontalElements.length; i++) {
                this.createAdditionalHorizontalPanels(points.additionalHorizontalElements[i]);
            }

            // remove legs because there could be less
            for (let i = 0; i < this.walls.legs.length; i++) {
                scene.remove(this.walls.legs[i]);
            }
            this.walls.legs.length = 0;
            // create new legs
            for (let i = 0; i < points.legs.length; i++) {
                this.createLegs(points.legs[i]);
            }

            this.door_lists = [[], [], [], [], [], [], [], [], [], [], [], []];
            this.door_lists_elements = [
                [],
                [],
                [],
                [],
                [],
                [],
                [],
                [],
                [],
                [],
                [],
                [],
            ];

            this.drawers_lists = [[], [], [], [], [], [], [], [], [], [], [], []];

            for (let i = 0; i < this.walls.doors.length; i++) {
                scene.remove(this.walls.doors[i]);
            }

            for (let i = 0; i < this.walls.door_groups.length; i++) {
                scene.remove(this.walls.door_groups[i]);
            }
            this.walls.door_groups.length = 0;
            this.walls.doors.length = 0;

            for (let i = 0; i < points.doors.length; i++) {
                this.createDoor(
                    getPositionFromPoint(points.doors[i]),
                    getSizeFromPoint(points.doors[i]),
                    points.doors[i].type,
                    points.doors[i].flip,
                    ivy.constants.rowHeight,
                    this.shelf_type,
                );
            }
            // drawers

            for (let i = 0; i < this.walls.drawers.length; i++) {
                scene.remove(this.walls.drawers[i]);
            }
            this.walls.drawers.length = 0;
            // create new legs
            for (let i = 0; i < points.drawers.length; i++) {
                this.createDrawers(
                    getPositionFromPoint(points.drawers[i]),
                    getSizeFromPoint(points.drawers[i]),
                    ivy.constants.rowHeight,
                    this.shelf_type,
                );
            }

            // handlers
            for (let i = 0; i < this.handlers.length; i++) {
                scene.remove(this.handlers[i]);
            }
            for (let i = 0; i < ivy.rows; i++) {
                let height = 0;
                for (let j = 0; j < i; j++) {
                    height += ivy.constants.rowHeight[j];
                }
                height += ivy.constants.rowHeight[i] / 2;
                const obj = new THREE.Object3D();

                this.handlers.push(obj);
                this.handlers[i].position.setX(ivy.width / 2 + 40);
                this.handlers[i].position.setY(height);
                this.handlers[i].position.setZ(250);
                this.handlers[i].matrixWorldNeedsUpdate = true;
                scene.add(this.handlers[i]);
            }
        }

        const calculatedCapacity = getTotalCapacity(
            points,
            ivy.rows,
            ivy.pattern,
            ivy.width,
            ivy.shelf_type,
        );

        this.capacity = calculatedCapacity[0]; // Shelf total load
        this.compartmentCapacity = [calculatedCapacity[1], calculatedCapacity[2]]; // [ Capacity Min, Capacity Max ]

        this.points = points;
        ivy.points = points;
        this.row_styles_availability = points.additional_elements.styles;
        this.depth_previous = this.depth;

        // generateSVG(this.width, this.getHeight(), this.points.horizontals, this.points.verticals, this.points.supports, !snapping);
        // console.log('================= GenerateCanvas Call -- fn RebuildWalls=============================');
        // window.dConf.fn.timerStop();

        if (typeof generateCanvas !== 'undefined') {
            generateCanvas(
                ivy.width,
                ivy.getHeight(),
                this.points.horizontals,
                this.points.verticals,
                this.points.supports,
            );
        }

        // window.dConf.fn.timerStart();

        for (let i = 0; i < this.magical_materials_for_rows.doors.length; i++) {
            // this.magical_materials_for_rows["doors"][
            //   i
            // ].uniforms.blending_ratio.value = 0;
            // this.magical_materials_for_rows["handlers"][
            //   i
            // ].uniforms.blending_ratio.value = 0;
        }
        // console.log("ok, here pubsub", snapping, initial, snapping || initial, PubSub);

        if (snapping || initial) {
            // TODO: assign price as pubsub
            PubSub.publish('shelfChangedSnapped');
        } else {
            PubSub.publishSync('shelfChanged');
        }

        // if(window.sceneOn && !window.sliderMoving){
        //     this.createScene(this.width, this.getHeight());
        //     this.createItems();
        // } else {
        //     this.removeScene();
        //     this.removeItems(items);
        //     items = [];
        // }
    }

    toggleShadowCast() {
        shadowCastEnabled = !shadowCastEnabled;
        this.createCastShadows();
    }
}

function getUrlParameter(sParam) {
    const sPageURL = decodeURIComponent(window.location.search.substring(1));
    const sURLVariables = sPageURL.split('&');
    let sParameterName;
    let i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : sParameterName[1];
        }
    }
}

function getRowByY(value_y, rowsList) {
    let tmp = 0;
    let row = 0;
    while (!(tmp < value_y && tmp + rowsList[row] > value_y)) {
        tmp += rowsList[row];
        row++;
        // security check
        if (row >= 15) {
            return -1;
        }
    }
    return row;
}

function getTotalCapacity(shelfGeometry, rowAmount, dnaID, width, shelfType) {
    const capacityTable = {
        300: 50,
        400: 40,
        500: 35,
        600: 30,
        700: 25,
        800: 20,
        900: 15,
        1000: 10,
    };

    const setTableType02 = {
        // Slant

        0: {
            default: 8,
            subset_len: 0.2,
            doors: 0.7,
            param_1: 0.85,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 5,
            p_max: 60,
            o_min: 10,
            o_max: 40,
        },
        // Grid

        1: {
            default: 10,
            subset_len: 0.15,
            doors: 0.95,
            param_1: 1,
            param_2: 0.98,
            param_3: 0.87,
            p_min: 20,
            p_max: 80,
            o_min: 20,
            o_max: 60,
        },

        // Pattern

        2: {
            default: 6,
            subset_len: 0.25,
            doors: 0.8,
            param_1: 1,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 10,
            p_max: 60,
            o_min: 20,
            o_max: 45,
        },


        // Gradient

        3: {
            default: 7,
            subset_len: 0.3,
            doors: 0.9,
            param_1: 1.2,
            param_2: 0.96,
            param_3: 0.88,
            p_min: 15,
            p_max: 65,
            o_min: 20,
            o_max: 50,
        },


    };

    const setTable = {
        // Slant

        0: {
            default: 8,
            subset_len: 0.2,
            doors: 0.7,
            param_1: 0.85,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 5,
            p_max: 60,
            o_min: 10,
            o_max: 40,
        },

        // Gradient

        1: {
            default: 7,
            subset_len: 0.3,
            doors: 0.9,
            param_1: 1.2,
            param_2: 0.96,
            param_3: 0.88,
            p_min: 15,
            p_max: 65,
            o_min: 20,
            o_max: 50,
        },

        // Pattern

        2: {
            default: 6,
            subset_len: 0.25,
            doors: 0.8,
            param_1: 1,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 10,
            p_max: 60,
            o_min: 20,
            o_max: 45,
        },

        // Grid

        3: {
            default: 10,
            subset_len: 0.15,
            doors: 0.95,
            param_1: 1,
            param_2: 0.98,
            param_3: 0.87,
            p_min: 20,
            p_max: 80,
            o_min: 20,
            o_max: 60,
        },
    };

    let parameters = setTable['0'];

    if (shelfType === 1 || shelfType === 2) {
        if (dnaID in setTableType02) {
            parameters = setTableType02[dnaID];
        }
    } else if (dnaID in setTable) {
        parameters = setTable[dnaID];
    }

    if (shelfGeometry.additional_elements.shadow_middle.length === 0) {
        // console.log( 'shelfGeometry.additional_elements.shadow_middle is an empty array. getTotalCapacity() Skipped.' );
        return false;
    }

    const allSorted = shelfGeometry.additional_elements.shadow_middle
        .map(el => el.x2 - el.x1)
        .sort((first, second) => second - first);
    // console.log('allSorted', allSorted);
    const lengthAll = allSorted.length;
    // console.log('lengthAll', lengthAll);
    const shelfAmount = parseInt(Math.floor(lengthAll * parameters.subset_len), 10) || 1;
    const allSum = allSorted
        .slice(0, shelfAmount)
        .reduce((accumulator, currentValue) => accumulator + currentValue);
    // console.log('allSum',allSum);
    const slicedCount = shelfAmount <= allSorted.length ? shelfAmount : allSorted.length;
    const sumAverage = allSum / slicedCount;

    function getCapacity(k) {
        let capacity = parameters.default;

        for (const key in capacityTable) {
            if (parseInt(key) >= k) {
                capacity = capacityTable[key];
                break;
            }
        }
        return capacity;
    }

    const doors = shelfGeometry.doors || [];
    const drawers = shelfGeometry.drawers || [];
    const doorsAndDrawers = doors.concat(drawers);

    let adjustment = doorsAndDrawers.length > 0 ? parameters.doors : 1;
    adjustment *= Math.pow(parameters.param_1 * parameters.param_2, rowAmount);

    // console.log('allSorted', allSorted);

    const openings = [
        roundTo(getCapacity(allSorted[0]) * adjustment, 5),
        roundTo(getCapacity(allSorted[allSorted.length - 1] * adjustment), 5),
    ];

    // console.log('openings', openings);

    // Check for Slant and Grid DNA Cases.
    function checkIfOpeningsAreEqual(array) {
        // For even openings, JS sometimes calculates almost identical values, but not entirely identical ( e.g. 379mm, 381mm ).
        const maxDifferenceBetweenOpenings = 2;
        for (let i = 0; i < array.length; i++) {
            if (Math.abs(array[0] - array[i]) > maxDifferenceBetweenOpenings) {
                return false;
            }
        }
        return true;
    }

    const capMin = checkIfOpeningsAreEqual(allSorted)
        ? null
        : Math.max(parameters.o_min, Math.min.apply(Math, openings));
    // console.log('capMin', capMin);
    const capMax = Math.min(parameters.o_max, Math.max.apply(Math, openings));
    // console.log('capMax', capMax);

    const parameterWidth = rowAmount * width / 1000; // Przejscie na metro rzedy
    const total = getCapacity(sumAverage) * adjustment * lengthAll * parameters.param_3;

    const totalMax = Math.max(
        Math.min(parameters.p_max * parameterWidth, total),
        parameters.p_min * parameterWidth,
        lengthAll * parameters.o_min,
    );

    function roundTo(value, multiplicity) {
        const rounded = parseInt(value / multiplicity, 10) * multiplicity;
        return rounded;
    }

    const totalMaxFlat = roundTo(totalMax, 10); // 264.444 -> 260

    if (shelfType == 1 || shelfType == 2) {
        return [roundTo(totalMaxFlat * 0.66, 10), roundTo(capMin * 0.66, 1), roundTo(capMax * 0.66, 1)];
    }
    return [totalMaxFlat, capMin, capMax];
}

module.exports = Build3d;
