// //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- CONSTANTS ---- /////////////////////////////////////////////////////////////////////////////////////


export const MAT_THICKNESS = 18;
export const INSERT_THICKNESS = 18;
export const SUPPORT_WIDTH = 125;

export const BACK = 'B';
export const FRONT = 'Fr';
export const COMPONENT = 'C';
export const DOOR = 'D';
export const DOOR_F = 'Df';
export const SLIDER = 'Ds';
export const DRAWER = 'T';
export const HORIZONTAL = 'H';
export const INSERT_H = 'Ih';
export const INSERT_V = 'Iv';
export const ERASER_H = 'Eh';
export const ERASER_V = 'Ev';
export const LEG = 'L';
export const MARK = 'M';
export const OPENING = 'O';
export const PLINTH = 'P';
export const SPACER = 'X';
export const SUPPORT = 'S';
export const VERTICAL = 'V';
export const TOP_BOX = 'TB';
export const FRONT_BOX = 'FB';

export const SHADOW_INNER = 'Si';
export const SHADOW_OUTER = 'So';
export const SHADOW_RIGHT = 'Sr';
export const SHADOW_LEFT = 'Sl';

export const ELEM_TYPE_CODENAMES = {
    verticals: VERTICAL,
    horizontals: HORIZONTAL,
    backs: BACK,
    fronts: FRONT,
    components: COMPONENT,
    doors: DOOR,
    doors_front: DOOR_F,
    sliders: SLIDER,
    drawers: DRAWER,
    legs: LEG,
    mark: MARK,
    opening: OPENING,
    plinth: PLINTH,
    spacers: SPACER,
    supports: SUPPORT,
    inserts_horizontal: INSERT_H,
    inserts_vertical: INSERT_V,
    erasers_horizontal: ERASER_H,
    erasers_vertical: ERASER_V,

    shadows_inner: SHADOW_INNER,
    shadows_outer: SHADOW_OUTER,
    shadows_right: SHADOW_RIGHT,
    shadows_left: SHADOW_LEFT,
};

export const ELEM_TYPE_NAMES = Object.keys(ELEM_TYPE_CODENAMES).reduce((dict, key) => {
    dict[ELEM_TYPE_CODENAMES[key]] = key;
    return dict;
}, {});

// //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- HELPERS ---- ///////////////////////////////////////////////////////////////////////////////////

export const smartLog = function consoleLoggerWithDisablingGroupingAndCollapsing(
    test = true, groupStart = false, groupEnd = false, lines = [], expand = false,
) {
    if (test !== true) {
        return;
    }
    if (groupStart !== false && expand === true) {
        console.group(...groupStart);
    } else if (groupStart !== false) {
        console.groupCollapsed(...groupStart);
    }
    lines.forEach(line => console.log('>>>', ...line));
    if (groupEnd !== false) {
        console.log('> |');
        console.groupEnd();
    }
};

export const isString = value => typeof value === 'string' || value instanceof String;

// //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- TRANSLATORS ---- ///////////////////////////////////////////////////////////////////////////////////

const calculateStylesStatus = function calculateStatusOfStyleButtonsForEachRow(
    geom, row_heights, shelf_depth
) {
    let styles = Array(10).fill([2, 0, 0, 2, 1, 1]);
    if (row_heights === null) return styles;

    // wspolrzedne rzedow narastajaca
    let r_coor = [];
    row_heights.reduce(function(a,b,i) { return r_coor[i] = a+b; }, 9); // TODO: Ta dziewiątka to taka słaba zmienna ;)
    r_coor.unshift(9); // TODO: Ta dziewiątka to taka słaba zmienna też tutaj...
    // podzial elementow wg rzedu
    let door_by_row = r_coor.map(i => geom.doors.filter((value, index, array) => value['y1'] === i));
    let drawer_by_row = r_coor.map(i => geom.drawers.filter((value, index, array) => value['y1'] === i));
    let shadow_by_row = r_coor.map(i => geom.shadows_inner.filter((value, index, array) => value['y1'] === i));

    // Wyliczanie styli
    r_coor.forEach((coor, row) =>{
        const doors = door_by_row[row].length;
        const drawers = drawer_by_row[row].length;
        const shadows = shadow_by_row[row].length;
        styles[row] = [
            doors === 0 ? 2 : 1,
            shadows === 1 ? 0 : (doors !== 0 && shadows !== doors ? 2 : 1),
            shadows === doors ? 2 : 1,
            drawers === 0 ? 2 : 1,
            shadows === 1 ? 0 : (drawers !== 0 && shadows !== drawers ? 2 : 1),
            shadows === drawers ? 2 : 1,
        ]
    });
    // stylesCleanByDepth(styles, shelf_depth)
    return styles
};

function stylesCleanByDepth(styles, shelf_depth) {
    if (shelf_depth === 240) {
        styles.forEach((row, row_i) => {
            styles[row_i][4] = 0;
            styles[row_i][5] = 0;
            }
        )
    }
}


export const convertToGalleryFormat = function translateFromWebDesignerFormatToGalleryFormat(
    geom, xOffset = null, yOffset = null, additionalElements = true, paramA = false, row_heights=null
) {
    xOffset = xOffset || -geom.width / 2 + 9;
    yOffset = 0; //9; // Oś najniższego hori domyślnie na 0 -> renderer na stronie wymaga dodatnich coor_y
    const convert = { true: 1, false: 0, 1: 1, 0: 0, };
    const galleryData = {
        horizontals: [],
        verticals: [],
        supports: [],
        backs: [],
        legs: [],
        doors: [],
        drawers: [],
        plinth_y: [],
        plinth_x: [],
        boxes: [],
        material: 0,
        height: geom.height,
        modules: [],
        row_styles: Array(10).fill(1),
        rows: Array(10).fill(190),
    };

    geom.geometry.horizontals.forEach((e) => {
        galleryData.horizontals.push({
            x1: xOffset + e.x1,
            x2: xOffset + e.x2,
            y1: Math.abs(e.y1 + e.y2) / 2 + yOffset,
            y2: Math.abs(e.y1 + e.y2) / 2 + yOffset,
            z1: e.z2,
            z2: e.z1,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.verticals.forEach((e) => {
        galleryData.verticals.push({
            x1: xOffset + Math.abs(e.x1 + e.x2) / 2,
            x2: xOffset + Math.abs(e.x1 + e.x2) / 2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: e.z2,
            z2: e.z1,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.supports.forEach((e) => {
        galleryData.supports.push({
            x1: xOffset + e.x1,
            x2: xOffset + e.x2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: 12,
            z2: 0,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.backs.forEach((e) => {
        galleryData.backs.push({
            x1: xOffset + e.x1,
            x2: xOffset + e.x2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: 12,
            z2: 0,
            c_config_id: e.c_config_id || null,
        });
    });

    // TODO [LOW]: LEGS


    geom.geometry.doors.concat(geom.geometry.doors_front).forEach((e) => { // FIXME: Inne współrzędne dla doors/doors_front ?
        galleryData.doors.push({
            x1: xOffset + e.x1,
            x2: xOffset + e.x2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: e.z2,
            z2: e.z2 - 19,
            flip: convert[e.flip] || 0,
            type: 0,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.drawers.forEach((e) => {
        galleryData.drawers.push({
            x1: xOffset + e.x1,
            x2: xOffset + e.x2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: e.z2,
            z2: e.z2 - 19,
            flip: 0,
            type: 0,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.plinth.filter(e => e.type === 'Py').forEach((e) => {
        galleryData.plinth_y.push({
            x1: xOffset + Math.abs(e.x1 + e.x2) / 2,
            x2: xOffset + Math.abs(e.x1 + e.x2) / 2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: e.z2,
            z2: e.z1,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.plinth.filter(e => e.type === 'Px').forEach((e) => {
        galleryData.plinth_x.push({
            x1: xOffset + Math.abs(e.x1 + e.x2) / 2,
            x2: xOffset + Math.abs(e.x1 + e.x2) / 2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: e.z2,
            z2: e.z1,
            c_config_id: e.c_config_id || null,
        });
    });


    geom.geometry.inserts_horizontal.filter(e => e.type === 'Ih').forEach((e) => {
        galleryData.boxes.push({
            x1: xOffset + e.x1,
            x2: xOffset + e.x2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: e.z2,
            z2: e.z1,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.inserts_vertical.filter(e => e.type === 'Iv').forEach((e) => {
        galleryData.boxes.push({
            x1: xOffset + e.x1,
            x2: xOffset + e.x2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: e.z2,
            z2: e.z1,
            c_config_id: e.c_config_id || null,
        });
    });

    geom.geometry.legs.forEach((e) => {
        galleryData.legs.push({
            x1: xOffset + Math.abs(e.x1 + e.x2) / 2,
            x2: xOffset + Math.abs(e.x1 + e.x2) / 2,
            y1: e.y1 + yOffset,
            y2: e.y2 + yOffset,
            z1: Math.abs(e.z1 + e.z2) / 2,
            z2: Math.abs(e.z1 + e.z2) / 2,
            c_config_id: e.c_config_id || null,
        });
    });


    if (additionalElements === true) {
        galleryData.additional_elements = {
            styles: calculateStylesStatus(geom.geometry, row_heights, geom.depth),
            shadow_left: [],
            shadow_middle: [],
            shadow_right: [],
            shadow_side: [],
        };

        geom.geometry.shadows_outer.forEach((e) => {
            galleryData.additional_elements.shadow_middle.push({
                x1: xOffset + e.x1,
                x2: xOffset + e.x2,
                y1: e.y1 + yOffset,
                y2: e.y2 + yOffset,
                z1: e.z1,
                z2: e.z2,
            });
        });

        if (paramA === true) {
            geom.geometry.shadows_inner.forEach((e) => {
                galleryData.additional_elements.shadow_middle.push({
                    x1: xOffset + e.x1,
                    x2: xOffset + e.x2,
                    y1: e.y1 + yOffset,
                    y2: e.y2 + yOffset,
                    z1: e.z1,
                    z2: e.z2,
                });
            });
        }
        geom.geometry.shadows_left.forEach((e) => {
            galleryData.additional_elements.shadow_left.push({
                x1: xOffset + e.x1,
                x2: xOffset + e.x2,
                y1: e.y1 + yOffset,
                y2: e.y2 + yOffset,
                z1: e.z1,
                z2: e.z2,
            });
        });

        geom.geometry.shadows_right.forEach((e) => {
            galleryData.additional_elements.shadow_right.push({
                x1: xOffset + e.x1,
                x2: xOffset + e.x2,
                y1: e.y1 + yOffset,
                y2: e.y2 + yOffset,
                z1: e.z1,
                z2: e.z2,
            });
        });
    }
    return galleryData;
};


function translateElementByType(elem, elemType, xOffset, thickness, zBack) {
    const components = [];

    if (['horizontals', 'verticals', 'supports'].includes(elemType)) {
        if ('opening' in elem && elem.opening.length > 0) {
            const openings = elem.opening.sort(o => o[0]);
            openings.push([elem.x2, null]);
            components.push({
                x_domain: [elem.x1 * 100 + xOffset, openings[0][0] * 100 + xOffset],
                y_domain: [elem.y1 * 100, elem.y2 * 100],
                z_domain: [elem.z1 * 100, zBack * 100],
            });
            for (let i = 0; i < openings.length - 1; i += 1) {
                const currOp = openings[i];
                const nextOp = openings[i + 1];
                components.push({
                        x_domain: [currOp[0] * 100 + xOffset, currOp[1] * 100 + xOffset],
                        y_domain: [elem.y1 * 100, elem.y2 * 100],
                        z_domain: [elem.z1 * 100, elem.z1 * 100 + thickness],
                    },
                    {
                        x_domain: [currOp[0] * 100 + xOffset, currOp[1] * 100 + xOffset],
                        y_domain: [elem.y1 * 100, elem.y2 * 100],
                        z_domain: [elem.z2 * 100 - thickness, elem.z2 * 100],
                    },
                    {
                        x_domain: [currOp[1] * 100 + xOffset, nextOp[0] * 100 + xOffset],
                        y_domain: [elem.y1 * 100, elem.y2 * 100],
                        z_domain: [elem.z1 * 100, zBack * 100],
                    });
            }
        } else {
            components.push({
                x_domain: [elem.x1 * 100 + xOffset, elem.x2 * 100 + xOffset],
                y_domain: [elem.y1 * 100, elem.y2 * 100],
                z_domain: [elem.z1 * 100, zBack * 100],
                type: elemType,
            });
        }
    } else if (['inserts_vertical', 'inserts_horizontal'].includes(elemType)) {
        components.push({
            x_domain: [elem.x1 * 100 + xOffset, elem.x2 * 100 + xOffset],
            y_domain: [elem.y1 * 100, elem.y2 * 100],
            z_domain: [elem.z1 * 100, zBack * 100],
            type: elemType,
        });
    } else if (['backs', 'fronts'].includes(elemType)) {
        components.push({
            x_domain: [elem.x1 * 100 + xOffset, elem.x2 * 100 + xOffset],
            y_domain: [elem.y1 * 100, elem.y2 * 100],
            z_domain: [elem.z1 * 100 + thickness + 200, elem.z1 * 100 + 200],
            type: elemType,
        });
    } else if (elemType === 'drawers') {
        const handleType = elem.handle || 0;
        const thicknessFront = handleType !== 2 ? 1640 : thickness;
        const handleWidthWithOffset = [3300, 3300, 450][handleType];

        // TODO: zmienic
        components.push({
            x_domain: [elem.x1 * 100 + xOffset + 450, elem.x2 * 100 + xOffset - 450],
            y_domain: [elem.y1 * 100 + 300, elem.y2 * 100 - handleWidthWithOffset],
            z_domain: [elem.z2 * 100 - 100, elem.z2 * 100 - 100 - thicknessFront],
            type: 'T_front',
        });
        components.push({
            x_domain: [elem.x1 * 100 + xOffset + 800, elem.x1 * 100 + xOffset + 800 + thickness],
            y_domain: [elem.y1 * 100 + 1650, elem.y2 * 100 - 5350],
            z_domain: [elem.z2 * 100 - 100 - thicknessFront, elem.z2 * 100 - 100 - thicknessFront - 24000],
            type: 'T_side1',
        });
        components.push({
            x_domain: [elem.x2 * 100 + xOffset - 800, elem.x2 * 100 + xOffset - 800 - thickness],
            y_domain: [elem.y1 * 100 + 1650, elem.y2 * 100 - 5350],
            z_domain: [elem.z2 * 100 - 100 - thicknessFront, elem.z2 * 100 - 100 - thicknessFront - 24000],
            type: 'T_side2',
        });
        components.push({
            x_domain: [elem.x1 * 100 + xOffset + 450, elem.x2 * 100 + xOffset - 450],
            y_domain: [elem.y1 * 100 + 3050, elem.y2 * 100 - 3600],
            z_domain: [elem.z2 * 100 - 100 - 24000 - thicknessFront, elem.z2 * 100 - 100 - thicknessFront - 24000 - thickness],
            type: 'T_back',
        });
        components.push({
            x_domain: [elem.x1 * 100 + xOffset + 800 + thickness, elem.x2 * 100 + xOffset - 800 - thickness],
            y_domain: [elem.y1 * 100 + 3050, elem.y1 * 100 + 3050 + thickness],
            z_domain: [elem.z2 * 100 - 100 - thicknessFront, elem.z2 * 100 - 100 - 24000 - thicknessFront],
            type: 'T_bottom',
        });
        if (handleType !== 2) {
            components.push({
                x_domain: [elem.x1 * 100 + xOffset + 450, elem.x2 * 100 + xOffset - 450],
                y_domain: [elem.y2 * 100 - handleWidthWithOffset, elem.y2 * 100 - 300],
                z_domain: [elem.z2 * 100 - 100, elem.z2 * 100 - 100 - thicknessFront],
                type: 'T_handle',
            });
        } else {
            components.push({
                x_domain: [elem.x1 * 100 + xOffset, elem.x1 * 100 + xOffset + 13000],
                y_domain: [elem.y2 * 100 - 300, elem.y2 * 100 - 150],
                z_domain: [elem.z2 * 100 - thicknessFront, elem.z2 * 100 - thicknessFront + 3900],
                type: 'T_handle',
            });
        }
    } else if (elemType === 'doors') {
        const handle = elem.handle || 0;
        const thicknessFront = handle !== 2 ? 1640 : thickness;
        let handleWidthWithOffset = [3300, 3300, 0][handle];

        if (elem.flip == 1) { // TODO: Do naprawienia zmiana typu uchwytu w drzwiach dwuskrzydłowych!
            if (handleWidthWithOffset === 3300 && elem.double === 1) handleWidthWithOffset = 1000;

            const xOffsetDoor = handleWidthWithOffset !== 1000 ? xOffset : xOffset + 300;
            components.push({
                x_domain: [elem.x1 * 100 + xOffset + 300, elem.x2 * 100 + xOffsetDoor - handleWidthWithOffset - 300],
                y_domain: [elem.y1 * 100 + 300, elem.y2 * 100 - 300],
                z_domain: [elem.z2 * 100, elem.z2 * 100 - thicknessFront],
                type: 'door',
            });

            if (handleWidthWithOffset) {
                components.push({
                    x_domain: [elem.x2 * 100 + xOffsetDoor - handleWidthWithOffset - 300, elem.x2 * 100 + xOffsetDoor - 300],
                    y_domain: [elem.y1 * 100 + 300, elem.y2 * 100 - 300],
                    z_domain: [elem.z2 * 100, elem.z2 * 100 - thicknessFront],
                    // 'handleType': 0,
                    type: 'D_handle',
                });
            }
        } else {
            components.push({
                x_domain: [elem.x1 * 100 + xOffset + handleWidthWithOffset, elem.x2 * 100 + xOffset - 300],
                y_domain: [elem.y1 * 100 + 300, elem.y2 * 100 - 300],
                z_domain: [elem.z2 * 100, elem.z2 * 100 - thicknessFront],
                type: 'door',
            });
            if (handleWidthWithOffset) {
                components.push({
                    x_domain: [elem.x1 * 100 + xOffset + 300, elem.x1 * 100 + xOffset + handleWidthWithOffset],
                    y_domain: [elem.y1 * 100 + 300, elem.y2 * 100 - 300],
                    z_domain: [elem.z2 * 100, elem.z2 * 100 - thicknessFront],
                    // 'handleType': 0,
                    type: 'D_handle',
                });
            } else {
                components.push({
                    x_domain: [elem.x1 * 100 + xOffset, elem.x1 * 100 + xOffset + 13000],
                    y_domain: [elem.y2 * 100 - 300, elem.y2 * 100 - 150],
                    z_domain: [elem.z2 * 100 - thicknessFront, elem.z2 * 100 - thicknessFront
                    + 3900],
                    // 'handleType': 2,
                    type: 'D_handle',
                });
            }
        }
    } else if (elemType === 'plinth') {
        elem.components.forEach((c) => {
            components.push({
                x_domain: [c.x1 * 100 + xOffset, c.x2 * 100 + xOffset],
                y_domain: [elem.y1 * 100, elem.y2 * 100],
                z_domain: [c.z1 * 100, c.z2 * 100],
                type: c.type,
            });
        });
    } else if (elemType === 'legs') {
        components.push({
            x_domain: [elem.x1 * 100 + xOffset, elem.x2 * 100 + xOffset],
            y_domain: [elem.y1 * 100, elem.y2 * 100],
            z_domain: [elem.z1 * 100, elem.z2 * 100],
            type: elem.type,
        });
    }
    return components;
}

export const convertToProductionFormat = function translateToProductionFormat(geom, xOffset = 0, thickness = 1800) {
    const elements = [];
    const labels = {
        doors: 'Door',
        drawers: 'Drawer',
        opening: 'Opening',
        mark: 'Mark',
        plinth: 'Plinth',
        inserts_vertical: 'Insert',
        inserts_horizontal: 'Insert',
        legs: 'Leg',
    };
    const shallow = []; // ['doors', 'drawers', 'opening']
    Object.entries(geom.geometry).forEach(([elemType, elemList]) => {
        const surname = labels.elemType || '';
        const zCorrections = shallow.includes(elemType) ? 0.2 : 0;
        elemList.forEach((elem) => {
            const zBack = elem.z2 - Math.abs(Number(elem.z1 - elem.z2) * zCorrections); // Wypelnienia glebiej
            const proper = elem.proper || true;
            const elemTypeE = elemType.includes('plinth') || elemType.includes('insert') ? 'supports' : elemType;

            const newElement = {
                components: translateElementByType(elem, elemType, xOffset * 100, thickness, zBack),
                elem_type: ELEM_TYPE_CODENAMES[elemTypeE] || 'S',
                surname: elem.surname || proper ? surname : 'improper',
                // Te współrzędne nie wiem czy są wykorzystywane w 3D
                x_domain: [elem.x1 * 100 + xOffset, elem.x2 * 100 + xOffset],
                y_domain: [elem.y1 * 100, elem.y2 * 100],
                z_domain: [elem.z1 * 100, elem.z2 * 100],
                c_subconfig_id: elem.c_subconfig_id || null,
                c_config_id: elem.c_config_id || null,
                m_config_id: elem.m_config_id || null,
            };
            if (elem.flip) {
                newElement.flip = elem.flip ? 1 : 0;
            }
            elements.push(newElement);
        });
    });

    return { item: { elements } };
};
