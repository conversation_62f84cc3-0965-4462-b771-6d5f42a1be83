import {
    BACK,
    COMPONENT,
    DOOR,
    DOOR_F,
    DRA<PERSON>ER,
    ELEM_TYPE_CODENAMES,
    ELEM_TYPE_NAMES,
    FRONT,
    HORIZONTAL,
    INSERT_H,
    INSERT_V,
    ERASER_H,
    ERASER_V,
    MARK,
    MAT_THICKNESS,
    OPENING,
    <PERSON><PERSON><PERSON>TH,
    SHADOW_OUTER,
    SHADOW_INNER,
    SHADOW_LEFT,
    SHADOW_RIGHT,
    SUPPORT,
    VERTICAL,
    LEG,
} from './dnaToolsWdUtils';


const INSERT_THICKNESS = 18;

function gatherAxisLines(geometry) {
    const linesX = new Set();
    const linesY = new Set();
    Object.values(geometry).forEach(elements => elements.forEach((e) => {
        if ([PLINTH, MARK, COMPONENT].includes(e.type)) return;
        linesX.add(Number(e.x1));
        linesX.add(Number(e.x2));
        linesY.add(Number(e.y1));
        linesY.add(Number(e.y2));
    }));
    const asNumber = (a, b) => a - b;
    return [[...linesX].sort(asNumber), [...linesY].sort(asNumber)];
}

function createIntersectionMatrix(linesX, linesY) {
    return {
        linesX,
        linesY,
        points: linesX.reduce((mX, x) => {
            mX[x] = linesY.reduce((mY, y) => {
                mY[y] = { x, y }; // debug {}
                return mY;
            }, {});
            return mX;
        }, {}),
    };
}

function getPoint(matrix, x, y) {
        return matrix.points[x][y];
}

function getSliceX(matrix, x1, x2, y) {
        return matrix.linesX.filter(x => x >= x1 && x <= x2).map(x => matrix.points[x][y]);
}

function getSliceY(matrix, x, y1, y2) {
        return matrix.linesY.filter(y => y >= y1 && y <= y2).map(y => matrix.points[x][y]);
}

function getSlice2d(matrix, x1, x2, y1, y2) {
        const ySlice = matrix.linesY.filter(y => y >= y1 && y <= y2);
        return matrix.linesX.filter(x => x >= x1 && x <= x2).map(x => ySlice.map(y => matrix.points[x][y]));
}


// -----------------------  FUNKCJE POMOCNICZE --------------------------------------------------------------------------

function mergeHorizontal(element, intersectionMatrix, existingElements, thickness, expand) {
    const points = getSliceX(intersectionMatrix, element.x1, element.x2, element.y1);
    if (points.length === 0) return existingElements;
    const existing = points.map(p => !(p.top && p.bottom) ? (p.left || p.right || null) : null).reduce((p, n) => (p || n), null);
    if (existing != null && existing.x2 > element.x2) {
        // overlapping
        return existingElements;
    } if (existing != null && existingElements.includes(existing) && existing.x2 < element.x2) {
        // merging
        existing.x2 = element.x2 + (expand ? (thickness / 2) : (-thickness / 2));
        if (element.opening) existing.opening = (existing.opening || []).concat(element.opening);

        points.forEach((p, i) => {
            if (i > 0) p.left = existing;
            if (i < points.length - 1) p.right = existing;
        });
        return existingElements;
    }
    // adding
    const vLeft = points[0].top || points[0].bottom;
    const vRight = points[points.length - 1].top || points[points.length - 1].bottom;
    element.y1 -= thickness / 2;
    element.y2 += thickness / 2;
    element.x1 = vLeft ? vLeft.x2 : (element.x1 - (expand ? (thickness / 2) : (-thickness / 2)));
    element.x2 = vRight ? vRight.x1 : (element.x2 + (expand ? (thickness / 2) : (-thickness / 2)));

    points.forEach((p, i) => {
        if (i > 0) p.left = element;
        if (i < points.length - 1) p.right = element;
    });
    existingElements.push(element);
    return existingElements;
}

function splitAndMergeVertical(element, intersectionMatrix, existingElements, thickness) {
    const points = getSliceY(intersectionMatrix, element.x1, element.y1, element.y2);
    element.x1 -= thickness / 2;
    element.x2 += thickness / 2;
    element.y1 -= thickness / 2;
    element.y2 += thickness / 2;
    const splitVertical = [element];

    for (let i = 0; i < points.length; i += 1) {
        const p = points[i];
        const ihLeft = (p.left !== undefined && p.left.x2 > element.x1) ? p.left : null;
        const ihRight = (p.right !== undefined && p.right.x1 < element.x2) ? p.right : null;
        const ih = ihLeft || ihRight;
        const lastV = splitVertical[splitVertical.length - 1];
        const existing = (p.bottom !== lastV && existingElements.includes(p.bottom)) ? p.bottom : null;
        if (ih && lastV.y1 < ih.y1) {
            const nextV = { ...lastV }; // copy
            if (existing != null) splitVertical.splice(splitVertical.indexOf(lastV), 1);
            else {
                lastV.y2 = ih.y1;
                p.bottom = lastV;
            }
            if (nextV.y2 > ih.y2) {
                nextV.y1 = ih.y2;
                splitVertical.push(nextV);
                p.top = nextV;
            }
        } else if (ih && lastV.y1 >= ih.y1) {
            lastV.y1 = ih.y2;
            p.top = lastV;
        } else if (!ih && existing != null) {
            splitVertical.splice(splitVertical.indexOf(lastV), 1);
            splitVertical.push(existing);
            existing.y2 = lastV.y2;
            existingElements.splice(existingElements.indexOf(existing), 1);
        } else {
            p.bottom = lastV;
            p.top = lastV;
        }
    }
    existingElements = existingElements.concat(splitVertical);
    return existingElements;
}

function eraseHorizontal(element, intersectionMatrix, existing_elements, thickness) {
    const points = getSliceX(intersectionMatrix, element.x1, element.x2, element.y1);
    element.x1 -= thickness / 2;
    element.x2 += thickness / 2;

    let hori_to_remove = [];
    let hori_to_add = [];

    points.reduce((hori_list, point) => {
        if (point.left && !hori_list.includes(point.left)) hori_list.push(point.left);
        if (point.right && !hori_list.includes(point.right)) hori_list.push(point.right);
        return hori_list;
    }, []).forEach(hori => {
       if (hori.x1 >= element.x1 && hori.x2 <= element.x2)
           hori_to_remove.push(hori);
       else if (hori.x1 >= element.x1 && hori.x2 > element.x2)
           hori.x1 = element.x2 - thickness;
       else if (hori.x1 < element.x1 && hori.x2 <= element.x2)
           hori.x2 = element.x1 + thickness;
       else if (hori.x1 < element.x1 && hori.x2 > element.x2) {
           const right_hori = { ...hori }; // copy
           right_hori.x1 = element.x2 - thickness;
           hori.x2 = element.x1 + thickness;
           hori_to_add.push(right_hori);
       }
    });

    hori_to_add.forEach(hori => {
        getSliceX(intersectionMatrix, hori.x1 + thickness / 2,
            hori.x2 - thickness / 2, hori.y1 + thickness / 2).forEach((p, i) => {
            if (i > 0) p.left = hori;
            if (i < points.length - 1) p.right = hori;
        });
    });

    points.forEach((p, i) => {
        if (i > 0) p.left = undefined;
        if (i < points.length - 1) p.right = undefined;
    });

    return existing_elements.filter(hori => !hori_to_remove.includes(hori)).concat(hori_to_add);
}

function eraseVertical(element, intersectionMatrix, existing_elements, thickness) {
    const points = getSliceY(intersectionMatrix, element.x1, element.y1, element.y2);
    const first_point_hori = points[0].left !== undefined || points[0].right !== undefined;
    const last_point_hori = points[points.length-1].left !== undefined || points[points.length-1].right !== undefined;
    element.y1 -= thickness / 2;
    element.y2 += thickness / 2;
    let vert_to_remove = [];
    let vert_to_add = [];

    points.reduce((vert_list, point) => {
        if (point.bottom && !vert_list.includes(point.bottom)) vert_list.push(point.bottom);
        if (point.top && !vert_list.includes(point.top)) vert_list.push(point.top);
        return vert_list;
    }, []).forEach(vert => {
        if (vert.y1 >= element.y1 && vert.y2 <= element.y2)
            vert_to_remove.push(vert);
        else if (vert.y1 >= element.y1 && vert.y2 > element.y2)
            vert.y1 = element.y2 - (last_point_hori ? 0 : thickness);
        else if (vert.y1 < element.y1 && vert.y2 <= element.y2)
            vert.y2 = element.y1 + (first_point_hori ? 0 : thickness);
        else if (vert.y1 < element.y1 && vert.y2 > element.y2) {
            const top_vert = { ...vert }; // copy
            top_vert.y1 = element.y2 - (last_point_hori ? 0 : thickness);
            vert.y2 = element.y1 + (first_point_hori ? 0 : thickness);
            vert_to_add.push(top_vert);
        }
    });

    vert_to_add.forEach(vert => {
        getSliceY(intersectionMatrix, vert.x1 + thickness / 2,
            vert.y1 + thickness / 2, vert.y1 - thickness / 2).forEach((p, i) => {
            if (i > 0) p.bottom = vert;
            if (i < points.length - 1) p.top = vert;
        });
    });

    points.forEach((p, i) => {
        if (i > 0) p.bottom = undefined;
        if (i < points.length - 1) p.top = undefined;
    });

    return existing_elements.filter(vert => !vert_to_remove.includes(vert)).concat(vert_to_add);
}


function splitFill(element, intersectionMatrix, layer) {
    const points = layer === 'front'
        ? [[getPoint(intersectionMatrix, element.x1, element.y1),
            getPoint(intersectionMatrix, element.x1, element.y2)],
            [getPoint(intersectionMatrix, element.x2, element.y1),
                getPoint(intersectionMatrix, element.x2, element.y2)]]
        : getSlice2d(intersectionMatrix, element.x1, element.x2, element.y1, element.y2);
    const lenH = points.length;
    const lenV = lenH !== 0 ? points[0].length : 0;

    const fillSplit = [];
    for (let j = 1; j < lenV; j += 1) {
        for (let i = 1; i < lenH; i += 1) {
            const fill = JSON.parse(JSON.stringify(element));
            if (layer === 'back') {
                points[i - 1][j].backbottomright = fill;
                points[i][j - 1].backtopleft = fill;
            } else {
                points[i - 1][j].bottomright = fill;
                points[i][j - 1].topleft = fill;
            }
            fill.p1 = points[i - 1][j - 1];
            fill.p2 = points[i - 1][j];
            fill.p3 = points[i][j];
            fill.p4 = points[i][j - 1];
            fillSplit.push(fill);
        }
    }
    return fillSplit;
}

/*
function pointToString(p) {
    let neighbours = '';
    if (p.left) neighbours += ' L';
    if (p.right) neighbours += ' R';
    if (p.top) neighbours += ' T';
    if (p.bottom) neighbours += ' B';
    if (p.backtopleft) neighbours += ' bTL';
    if (p.backbottomright) neighbours += ' bBR';
    if (p.topleft) neighbours += ' TL';
    if (p.bottomright) neighbours += ' BR';
    return p ? `<<x: ${p.x}, y: ${p.y}${neighbours}>>` : null;
}

function fillToString(f) {
    if (!f) return null;
    let str = '';
    str += `x1: ${f.x1}`;
    str += `, x2: ${f.x2}`;
    str += `, y1: ${f.y1}`;
    str += `, y2: ${f.y2}`;
    str += `, f1: ${pointToString(f.p1)}`;
    str += `, f2: ${pointToString(f.p2)}`;
    str += `, f3: ${pointToString(f.p3)}`;
    str += `, f4: ${pointToString(f.p4)}`;
    return `<${str}>`;
} */

function mergeFillH(fillSplit, existingElements = null, back = false) {
    existingElements = existingElements || [];
    const mergedFillH = [];
    for (let i = 0; i < fillSplit.length; i += 1) {
        const f = fillSplit[i];
        const { p1 } = f;
        let nLeft = back ? p1.backtopleft : p1.topleft;
        if (!existingElements.includes(p1.backtopleft) && !mergedFillH.includes(p1.backtopleft)) nLeft = null;
        if (!p1.top && nLeft && nLeft.y1 === f.y1 && nLeft.y2 === f.y2 && nLeft.flip === f.flip) {
            nLeft.x2 = f.x2;
            nLeft.p3 = f.p3;
            nLeft.p2 = f.p2;
            nLeft.type = f.type;
            if (back) {
                f.p2.backbottomright = nLeft;
                f.p4.backtopleft = nLeft;
            } else {
                f.p2.bottomright = nLeft;
                f.p4.topleft = nLeft;
            }
        } else {
            mergedFillH.push(f);
        }
    }
    return mergedFillH;
}

function mergeFillV(fillSplit, existingElements = null, back = false) {
    existingElements = existingElements || [];
    const mergedFillV = [];
    for (let i = 0; i < fillSplit.length; i += 1) {
        const f = fillSplit[i];
        const { p1 } = f;
        let nBottom = back ? p1.backbottomright : p1.bottomright;
        if (!existingElements.includes(p1.backbottomright) && !mergedFillV.includes(p1.backbottomright)) nBottom = null;

        if (!p1.right && nBottom && nBottom.x1 === f.x1 && nBottom.x2 === f.x2) {
            nBottom.y2 = f.y2;
            nBottom.p3 = f.p3;
            nBottom.p2 = f.p2;
            nBottom.type = f.type;
            if (back) {
                f.p2.backbottomright = nBottom;
                f.p4.backtopleft = nBottom;
            } else {
                f.p2.bottomright = nBottom;
                f.p4.topleft = nBottom;
            }
        } else {
            mergedFillV.push(f);
        }
    }
    return mergedFillV;
}

function assignLeftAndRightShadows(elements) {
    let shadowsOuter = [];
    let shadowsLeft = [];
    let shadowsRight = [];

    elements[SHADOW_OUTER].forEach(s => {
        if (s.p1.top && s.p3.bottom) shadowsOuter.push(s);
        else if (s.p1.top) {
            s.x2 += MAT_THICKNESS / 2;
            shadowsRight.push(s);
        }
        else if (s.p3.bottom) {
            s.x1 -= MAT_THICKNESS / 2;
            shadowsLeft.push(s);
        }
    });

    elements[SHADOW_OUTER] = shadowsOuter;
    elements[SHADOW_RIGHT] = shadowsRight;
    elements[SHADOW_LEFT] = shadowsLeft;
    return elements;
}

function trimFills(elements, fillTypes) {
    fillTypes.forEach(key => elements[key].forEach((f) => {
        if ('p1' in f) {
            if (f.p1.right) f.y1 = f.p1.right.y2;
            if (f.p1.top) f.x1 = f.p1.top.x2;
            if (f.type === SUPPORT && f.p1.top && (f.p3 === undefined || !f.p3.bottom))
                f.x1 = f.x2;
            if ('p3' in f) {
                if (f.p3.left) f.y2 = f.p3.left.y1;
                if (f.p3.bottom) f.x2 = f.p3.bottom.x1;
                delete f.p3;
            }
            if (f.type === SUPPORT && f.p1.top && (f.p3 === undefined || !f.p3.bottom))
                f.x2 = f.p1.top.x2;
            delete f.p1;
            delete f.p2;
            delete f.p4;
        }
    }));
    return elements;
}

function clearElementTempTypes(elements, translation) {
    Object.entries(translation).forEach(([changeFrom, changeTo]) => {
        elements[changeFrom].forEach((f) => {
            f.type = changeTo;
            elements[changeTo].push(f);
        });
        elements[changeFrom] = [];
    });
    return elements;
}

function splitAndMergeFills(element, intersectionMatrix, newElements, existingElements, layer = null) {
    let fillSplit = splitFill(element, intersectionMatrix, layer);
    if (layer !== 'front') {
        fillSplit = mergeFillH(fillSplit, null, layer === 'back');
        fillSplit = mergeFillV(fillSplit, null, layer === 'back');
    }
    fillSplit = mergeFillH(fillSplit, existingElements.concat(newElements), layer === 'back');
    fillSplit = mergeFillV(fillSplit, existingElements.concat(newElements), layer === 'back');
    newElements = newElements.concat(fillSplit);
    return newElements;
}

// -----------------------  FUNKCJE GENERUJACE ELEMENTY ------------------------

function byIntKey(key1, key2 = null) {
    return (a, b) => {
        const dif1 = Number(a[key1]) - Number(b[key1]);
        return (dif1 !== 0 || key2 === null) ? dif1 : Number(a[key2]) - Number(b[key2]);
    };
}

function generateHorizontals(data, intersectionMatrix, elements) {
    let horizontals = [];
    const rawHorizontals = data.sort(byIntKey('x1'));
    for (let i = 0; i < rawHorizontals.length; i += 1) {
        horizontals = mergeHorizontal(rawHorizontals[i], intersectionMatrix, horizontals, MAT_THICKNESS, true);
    }
    elements[HORIZONTAL] = horizontals;
    return elements;
}

function generateVerticals(data, intersectionMatrix, elements) {
    let verticals = [];
    const rawVerticals = data.sort(byIntKey('y1'));
    for (let i = 0; i < rawVerticals.length; i += 1) {
        verticals = splitAndMergeVertical(rawVerticals[i], intersectionMatrix, verticals, MAT_THICKNESS);
    }
    elements[VERTICAL] = verticals;
    return elements;
}

function generateInsertsHorizontal(data, intersectionMatrix, elements) {
    let inserts = [];
    const rawInserts = data.sort(byIntKey('x1'));
    for (let i = 0; i < rawInserts.length; i += 1) {
        inserts = mergeHorizontal(rawInserts[i], intersectionMatrix, inserts, INSERT_THICKNESS, false);
    }
    elements[INSERT_H] = inserts;
    return elements;
}

function generateInsertsVertical(data, intersectionMatrix, elements) {
    let inserts = [];
    const rawInserts = data.sort(byIntKey('y1'));
    for (let i = 0; i < rawInserts.length; i += 1) {
        inserts = splitAndMergeVertical(rawInserts[i], intersectionMatrix, inserts, INSERT_THICKNESS);
    }
    elements[INSERT_V] = inserts;
    return elements;
}

function generateErasersHorizontal(data, intersectionMatrix, elements) {
    let horizontals = elements[HORIZONTAL];
    const rawErasers = data.sort(byIntKey('x1'));
    for (let i = 0; i < rawErasers.length; i += 1) {
        horizontals = eraseHorizontal(rawErasers[i], intersectionMatrix, horizontals, MAT_THICKNESS);
    }
    elements[HORIZONTAL] = horizontals;
    return elements;
}

function generateErasersVertical(data, intersectionMatrix, elements) {
    let verticals = elements[VERTICAL];
    const rawErasers = data.sort(byIntKey('y1'));
    for (let i = 0; i < rawErasers.length; i += 1) {
        verticals = eraseVertical(rawErasers[i], intersectionMatrix, verticals, MAT_THICKNESS);
    }
    elements[VERTICAL] = verticals;
    return elements;
}

function generateFills(data, intersectionMatrix, elements, fillType, combine_with_types = [], layer = null) {
    const fills = combine_with_types.reduce((sum, key) => sum.concat(elements[ELEM_TYPE_NAMES[key]]), []);
    let newFills = [];
    const rawFills = data[ELEM_TYPE_NAMES[fillType]].sort(byIntKey('x1', 'y1'));
    for (let i = 0; i < rawFills.length; i += 1) {
        newFills = splitAndMergeFills(rawFills[i], intersectionMatrix, newFills, fills, layer);
    }
    elements[fillType] = newFills;
    return elements;
}


function generateLegs(elements) {
    // nie ma rączek, nie ma ciasteczek
    if (elements[HORIZONTAL].length === 0 || elements[VERTICAL].length === 0 || elements[PLINTH].length > 0)
        return elements;

    const sO = 20;         // side offset minimum
    const minDist = 130;   // min dist between legs
    const maxDist = 600;   // max dist without legs
    const legHeight = 20;  // y1
    const zO = 20;         // z offset +/=

    const lowestY2 = elements[HORIZONTAL].map(h => h.y2).sort((x, y) => x - y)[0];
    const lowestHoris = elements[HORIZONTAL].filter(h => (h.y2 === lowestY2));
    const lowestVerts = elements[VERTICAL].filter(e => (e.y1 === lowestY2));

    let legs = [];

    lowestHoris.forEach(h => {
       const {x1, x2} = h;
       let _pts = lowestVerts.map(v => (v.x1 + v.x2) / 2).sort((x, y) => x - y).filter(x => (x >= x1 + sO && x <= x2 - sO));
       _pts = _pts.length > 0 ? _pts : [x1 + sO];
       _pts.forEach((pt, i) => {
           if (i === 0 && (pt - x1 - sO) > minDist)
               legs.push(x1 + sO);
           if (legs.length === 0)
               legs.push(pt);
           else {
               let distToPrevious = pt - legs[legs.length-1];
               if (distToPrevious > maxDist)
                   legs.push(parseInt(Math.floor(pt - distToPrevious / 2)));
               if (distToPrevious > minDist)
                   legs.push(pt);
           }
           if (i === _pts.length - 1) {
               let distToLast = (x2 - sO) - legs[legs.length-1];
               if (distToLast > maxDist)
                   legs.push(parseInt(Math.floor(pt + distToLast / 2)));
               if (distToLast > minDist)
                   legs.push(x2 - sO)
           }
       })
    });

    let legsElements = [];
    legs.forEach(l => {
        [lowestHoris[0].z1 + zO, lowestHoris[0].z2 - zO].forEach(z => {
            legsElements.push({
                x1: l - 10,
                x2: l + 10,
                y1: lowestHoris[0].y1 - legHeight,
                y2: lowestHoris[0].y1,
                z1: z - 10,
                z2: z + 10,
                type: LEG
            });
        });
    });

    elements[LEG] = legsElements;
    return elements;
}



// -----------------------  FUNKCJE SPRAWDZAJACE ------------------------------------------------------------------------

// def check_geometry(geom, base_elements):
//     from webdesigner.range_obj import StRange
//     for elements in geom.values():
//         for elem in elements:
//             if 'proper' in elem or elem.get('base_eId', None) is None:  // Jesli wiadomo, ze zly lub brak odniesienia do base_elem
//                 continue
//             if any((key not in elem for key in ['x1', 'x2', 'y1', 'y2'])):  // Jesli niestandardowy format
//                 continue
//             elem_base = base_elements.get(elem['base_eId'])
//             proper = (int(elem['x2'] - elem['x1']) in StRange(elem_base['dim_x']) and
//                       int(elem['y2'] - elem['y1']) in StRange(elem_base['dim_y']))
//             // TODO: Rozkminic test na glebokosc
//             elem['proper'] = proper
//     return geom


// -----------------------  KORYGOWANIE GEOMETRII ------------------------------

export const buildObjectFinalGeometry = function mergeAndCleanGeometry(rawData) {
    //  ---------------- 1: Przygotowanie danych ----------------

    const { geometry, width, height, depth } = rawData;
    const [linesX, linesY] = gatherAxisLines(geometry);
    const intersectionMatrix = createIntersectionMatrix(linesX, linesY);

    // ---------------- 2: Generowanie elementow ----------------
    let elements = {};
    // H, V
    // Eh, Ev
    elements = generateHorizontals(geometry.horizontals, intersectionMatrix, elements);
    elements = generateErasersHorizontal(geometry.erasers_horizontal, intersectionMatrix, elements);
    elements = generateVerticals(geometry.verticals, intersectionMatrix, elements);
    elements = generateErasersVertical(geometry.erasers_vertical, intersectionMatrix, elements);
    // Ih, Iv
    elements = generateInsertsHorizontal(geometry.inserts_horizontal, intersectionMatrix, elements);
    elements = generateInsertsVertical(geometry.inserts_vertical, intersectionMatrix, elements);

    // O, D, T
    elements = generateFills(geometry, intersectionMatrix, elements, OPENING);
    elements = generateFills(geometry, intersectionMatrix, elements, FRONT, [], 'front');
    elements = generateFills(geometry, intersectionMatrix, elements, DOOR, [OPENING]);
    elements = generateFills(geometry, intersectionMatrix, elements, DRAWER, [OPENING, DOOR]);
    elements = generateFills(geometry, intersectionMatrix, elements, DOOR_F, [OPENING], 'front');
    elements = generateFills(geometry, intersectionMatrix, elements, SHADOW_OUTER, [], 'front');
    elements = generateFills(geometry, intersectionMatrix, elements, SHADOW_INNER, [], 'front');
    elements = generateFills(geometry, intersectionMatrix, elements, SHADOW_LEFT, [], 'front');
    elements = generateFills(geometry, intersectionMatrix, elements, SHADOW_RIGHT, [], 'front');
    // S, B
    elements = generateFills(geometry, intersectionMatrix, elements, SUPPORT, [], 'back');
    elements = generateFills(geometry, intersectionMatrix, elements, BACK, [SUPPORT], 'back');

    // P
    elements[PLINTH] = geometry.plinth;
    // elements.extend(p for p in data if p['type'] == PLINTH)

    // ---------------- 2: Ostateczne wymiary i typy ----------------
    elements = assignLeftAndRightShadows(elements);
    elements = trimFills(elements, [OPENING, FRONT, DOOR, DRAWER, DOOR_F, BACK, SUPPORT,
                                            SHADOW_OUTER, SHADOW_INNER, SHADOW_LEFT, SHADOW_RIGHT]);
    elements = clearElementTempTypes(elements, { [DOOR_F]: DOOR, [FRONT]: BACK });
    elements = generateLegs(elements);

    // elements = {n: filter(lambda e: e['type'] == c, elements) for n, c in ELEM_TYPE_CODENAMES.items()}

    // data['other'] = filter(lambda e: e['type'] not in 'VTDHLSB', data)
    // HACK dodane wymiary zeby skalowac wg nich wymiar rysunku matplotlib dla svg
    const data = {
        width,
        height,
        depth,
        components: { components: geometry.components },
        geometry: Object.entries(ELEM_TYPE_CODENAMES).reduce((typeList, [name, code]) => {
            typeList[name] = elements[code] || [];
            return typeList;
        }, {}),
    };
    // data = dict(
    //     components={'components': filter(lambda e: e['type'] == COMPONENT, data)},
    //     geometry={n: filter(lambda e: e['type'] == c, data) for n, c in ELEM_TYPE_CODENAMES.items()}
    // )
    if (Object.keys(elements).includes('horizontals')
        && Object.keys(elements).includes('verticals')) {
        data.height = elements.horizontals.reduce((max, h) => (h.y2 > max ? h.y2 : max),
            elements.verticals.reduce((max, v) => (v.y2 > max ? v.y2 : max), 0));
    }
    //
    // if elements['horizontals'] and elements['verticals']:
    //     data['height'] = max([h['y2'] for h in elements['horizontals']] +
    //                          [h['y2'] for h in elements['verticals']])

    // ---------------- 3:  Sprawdzanie poprawnoÅ›ci elementÃ³w
    // data['geometry'] = check_geometry(elements, serializedData['element'])
    return data;
};
