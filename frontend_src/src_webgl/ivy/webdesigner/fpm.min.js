const e={BACK:"B",FRONT:"Fr",COMPONENT:"C",DOOR:"D",DOOR_F:"Df",SLIDER:"Ds",DRAWER:"T",HORIZONTAL:"H",INSERT_H:"Ih",INSERT_V:"Iv",ERASER_H:"Eh",ERASER_V:"Ev",ERASER_B:"Eb",ERASER_D:"Ed",LEG:"L",LONG_LEG:"Ll",MARK:"M",OPENING:"O",PLINTH:"P",SPACER:"X",SUPPORT:"S",VERTICAL:"V",TOP_BOX:"TB",FRONT_BOX:"FB",CABLE_OPENING:"Co",BAR:"Br",HINGE:"Hg",WALL:"W",SLAB_BASE:"Sb",SLAB_STD:"Ss",INSERT_W:"Iw",FRAME_TOP:"Ft",FRAME_SIDE:"Fs",DOOR_EXT:"Dex",MASKING_BAR:"Mb",SHADOW_INNER:"Si",SHADOW_OUTER:"So",SHADOW_RIGHT:"Sr",SHADOW_LEFT:"Sl"},t={verticals:e.VERTICAL,horizontals:e.HORIZONTAL,backs:e.BACK,fronts:e.FRONT,components:e.COMPONENT,doors:e.DOOR,doors_front:e.DOOR_F,sliders:e.SLIDER,drawers:e.DRAWER,legs:e.LEG,long_legs:e.LONG_LEG,mark:e.MARK,opening:e.OPENING,plinth:e.PLINTH,spacers:e.SPACER,supports:e.SUPPORT,inserts_horizontal:e.INSERT_H,inserts_vertical:e.INSERT_V,erasers_horizontal:e.ERASER_H,erasers_vertical:e.ERASER_V,erasers_back:e.ERASER_B,erasers_door:e.ERASER_D,cable_openings:e.CABLE_OPENING,bars:e.BAR,hinges:e.HINGE,walls:e.WALL,slabs_base:e.SLAB_BASE,slabs_standard:e.SLAB_STD,inserts_wardrobe:e.INSERT_W,frame_tops:e.FRAME_TOP,frame_sides:e.FRAME_SIDE,doors_exterior:e.DOOR_EXT,masking_bars:e.MASKING_BAR,shadows_inner:e.SHADOW_INNER,shadows_outer:e.SHADOW_OUTER,shadows_right:e.SHADOW_RIGHT,shadows_left:e.SHADOW_LEFT},s=[e.VERTICAL,e.HORIZONTAL,e.INSERT_H,e.INSERT_V,e.DOOR_F,e.DRAWER,e.SLAB_BASE,e.SLAB_STD,e.INSERT_W,e.WALL],i={SHADOW_THICKNESS:3,MAT_THICKNESS:18,FRAME_THICKNESS:12,FRAME_OFFSET:14,INSERT_THICKNESS:18,SUPPORT_WIDTH:125,T01_BACK_WIDTH:550,T02_BACK_WIDTH:570,T01_V_BACK_WIDTH:550,LEGS_DISTANCE:1e3,LONG_LEGS_RADIUS:10,MIDDLE_POSITION:"1/2",CABLE_RADIUS:30,INSERT_Z_SIDEBOARD_OFFSET:30,INSERT_Z_WARDROBE_OFFSET:0,PLINTH_SIZE:100,SINGLE_BACK_H:382,SIDEBOARD_AXIS_OFFSET:18,SIDEBOARD_FINAL_HEIGHT_OFFSET:30,BAR_WIDTH:20,BAR_HEIGHT:40,EXTERIOR_DOOR_MAX:610,EXTERIOR_DOOR_OFFSET:2,EXTERIOR_DOOR_THICKNESS:18,HINGE_WIDTH:39,HINGE_HEIGHT:32,HINGE_OFFSET:{TOP:147,BOTTOM:162,MID:940},WARDROBE_HANDLE_POS:1121,WARDROBE_HANDLE_H:240,WARDROBE_HANDLE_W:12,WARDROBE_HANDLE_D:20,WARDROBE_HANDLE_OFFSET:16,WARDROBE_AXIS_OFFSET:42,DRAWER_RANGES:[[389.4,400],[778.8,800],[1168.2,1200]],DRAWER_COUNT:[2,4,6],DRAWER_HEIGHT:147,DRAWER_OFFSET:4,EXTERIOR_DRAWER_HEIGHT:200,M_BAR_HEIGHT:35,PLINTH_SPACE:29,WARDROBE_ELEMENT_MAX_WIDTH:2300,WARDROBE_WALL_MAX_HEIGHT:1976,WARDROBE_RAISER_MAX_HEIGHT:935,WARDROBE_BOTTOM_BACK_MAX_HEIGHT:815},n={"#aa":100,"#a":200,"#b":300,"#c":400,"#d":500,"#e":600,"#f":700,"#g":800,"#h":900,"#i":1e3,"#j":1100,"#height_aa":100,"#height_a":200,"#height_b":300,"#height_c":400,"#height_d":500,"#height_e":600,"#height_f":700,"#height_g":800,"#height_h":900,"#height_i":1e3,"#height_j":1100,"#default_back":0,"#mat":18,"#insert_thickness":18,"#support_width":125,"#plinth_height":90,"#elemDoor":283,"#elemDrawer":282,"#elemOpen":285},o=["t","u","v","w","x","y","z"],r={O:{left:1,right:1,top:1,bottom:1,back:-1,front:-1},D:{left:1,right:1,top:1,bottom:1,back:1,front:-1},T:{left:1,right:1,top:1,bottom:1,back:1,front:-1},FB:{left:1,right:1,top:1,bottom:1,back:1,front:-1},TB:{left:1,right:1,top:3,bottom:1,back:1,front:1},0:{left:1,right:1,top:1,bottom:1,back:-1,front:-1},1:{left:1,right:1,top:1,bottom:1,back:1,front:-1},2:{left:1,right:1,top:1,bottom:1,back:1,front:-1},3:{left:1,right:1,top:1,bottom:1,back:1,front:-1},4:{left:1,right:1,top:3,bottom:1,back:1,front:1}},a={ALL:["Sideboard","ShoeRack","SzafexMLP","Wardrobe"],WARDROBE:["SzafexMLP","Wardrobe"],WITH_DEPTH_MOD:["ShoeRack","SzafexMLP","Wardrobe"],WITH_STEP_HEIGHT:["Sideboard","ShoeRack"],WITH_HEIGHT_TOGGLE:["SzafexMLP","Wardrobe"]},l="ShoeRack",h="Sideboard",_="Wardrobe",p={T01:0,T02:1,VENEER_T01:2,T03:3},d=1,u=2,f=1,m=2,g=function(e,t,s,i,n,o){return{x1:e,x2:e+t,y1:s,y2:s+i,z1:n,z2:n+o}},y=function(e,t,s){let i=[],n={},o={x1:n,x2:n};return e.forEach(e=>{i.push(Object.assign(Object.assign({},e),function(e,t,s,i){let n={};return e.type,["x1","x2"].forEach(o=>{if(!(o in e)||Number.isInteger(e[o]))return;if(o in t&&e[o]in t[o])return void(n[o]=t[o][e[o]]);o in t||(t[o]={});let r=Math.round(e[o]);"x1"===o&&Math.abs(s-r)<1&&(r=s),"x2"===o&&Math.abs(i-r)<1&&(r=i),t[o][e[o]]=r,n[o]=t[o][e[o]]}),n}(e,o,t,s)))}),i};const x=function(t,s,i,n,o,r,a,l){return Object.assign(Object.assign({},a),{x1:t,x2:t+n,y1:s,y2:s+o,z1:i,z2:i+r,type:e.MARK,message:l})},b=function(e,t=null,s=null){let i={};if(e.components&&e.components.length)return e.components.reduce((n,o)=>{if(n){let r=[n.channel_id,o.channel_id].join("_"),a=n.x2+t,l=n.line_right||0,c=o.x2-o.x1,h=n.x2-n.x1,_=Math.min(c+l-o.w_min,n.w_max-(h-l)),p=-Math.min(h-l-n.w_min,o.w_max-(c+l));i[r]={x:a,value:l,v_min:p,v_max:_,y:e.height/2+s}}return o}),i},E=function(e){let t=e.reduce((e,t)=>(e.push(t.x1,t.x2),e),[]).sort((e,t)=>e-t),s=t[0]+t.pop();return e.forEach(e=>{let t=s-e.x1,i=s-e.x2;e.x1=t<i?t:i,e.x2=t>i?t:i,e.hasOwnProperty("opening")&&e.opening.length>0&&(e.opening=e.opening.map(([e,t])=>(e=s-e)<(t=s-t)?[e,t]:[t,e]).sort((e,t)=>e[0]-t[0])),"S"===e.type&&(e.flip=!e.flip)}),e};function O(e){const t=b(e);let s={};e.components.forEach(n=>{s[n.m_config_id]=function(e,t,s){let n=e.x1-i.MAT_THICKNESS/2,o=e.x2+i.MAT_THICKNESS/2,r=0-i.MAT_THICKNESS/2,a=e.y2+i.MAT_THICKNESS/2,l=e.z1-i.MAT_THICKNESS/2,c=e.z2+i.MAT_THICKNESS/2;const h=n+(o-n)/2;t&&Object.keys(t).forEach(s=>{const i=s.split("_").indexOf(e.channel_id.toString());0===i&&(o+=t[s].v_max-t[s].value),1===i&&(n-=Math.abs(t[s].v_min-t[s].value))});const _=h-n,p=o-h;return n-=s/2,o-=s/2,{p1:[n,r,l],p2:[n,a,l],p3:[o,a,l],p4:[o,r,l],p5:[n,r,c],p6:[n,a,c],p7:[o,a,c],p8:[o,r,c],pMin:[n,r,l],pMax:[o,a,c],toLeft:_,toRight:p}}(n,t,e.width)});const n=Object.values(s).map(e=>e.toLeft),o=Object.values(s).map(e=>e.toRight);return Object.keys(s).forEach(e=>{s[e].maxToLeft=Math.max(...n),s[e].maxToRight=Math.max(...o)}),s}function S(e){const t=i.MAT_THICKNESS/2;let s=Math.max(...e.components.map(e=>e.x2))+t,n=Math.min(...e.components.map(e=>e.x1))-t,o=Math.max(...e.components.map(e=>e.y2))+t,r=0-t,a=Math.max(...e.components.map(e=>e.z2))-t,l=Math.min(...e.components.map(e=>e.z1))+t;return{p1:[n,r,l],p2:[n,o,l],p3:[s,o,l],p4:[s,r,l],p5:[n,r,a],p6:[n,o,a],p7:[s,o,a],p8:[s,r,a],pMin:[n,r,l],pMax:[s,o,a]}}function T(e,t="all",s=0,i=0){let n=[],o=[],r=[],a={height:[],width:[]};const l=e=>{e.x=parseInt(e.x),e.y=parseInt(e.y),e.z=parseInt(e.z)};return e.forEach(e=>{if(e&&e.width&&("all"===t||"width"===t)){l(e.width);let t=[e.width.value,e.width.x,e.width.y].join(":"),s=[e.width.value,e.width.x].join(":");n.includes(t)||r.includes(s)||(n.push(t),r.push(s),e.width.y+=i,a.width.push(e.width))}if(e&&e.height&&("all"===t||"height"===t)){l(e.height);let t=[e.height.value,e.height.x,e.height.y].join(":");o.includes(t)||(o.push(t),e.height.x+=s,a.height.push(e.height))}}),a}var A={2050:{19:18,31:30,32:30,33:35,34:35,70:69,71:69,72:69,73:69,74:69,75:69,76:69,77:69,78:69,79:69,80:69,81:69,82:69,83:69,84:69,85:69,86:69,87:69,88:69,89:69,90:69,91:69,92:69,93:69,94:69,95:69,96:69,97:69,98:69,99:69,100:69},4100:{19:18},2060:{19:18,32:31,33:31,34:36,35:36,75:74,76:74,77:74,78:74,79:74,80:74,81:74,82:74,83:74,84:74,85:74,86:74,87:74,88:74,89:74,90:74,91:74,92:74,93:74,94:74,95:74,96:74,97:74,98:74,99:74,100:74},4110:{19:18},2070:{19:18,34:33,35:33,36:38,37:38,84:83,85:83,86:83,87:83,88:83,89:83,90:83,91:83,92:83,93:83,94:83,95:83,96:83,97:83,98:83,99:83,100:83},4120:{19:18},2080:{96:92,97:92,98:92,99:92,36:35,37:35,38:39,19:18,100:92,93:92,94:92,95:92},4130:{19:18},2090:{40:41,19:18,37:36,38:36,39:41},4140:{19:18},2100:{40:38,41:42,19:18,39:38},4150:{19:18},2110:{40:39,41:39,42:44,19:18,43:44},4160:{19:18},2120:{19:18,42:41,43:41,44:46,45:46},4170:{19:18},2130:{19:18,43:42,44:42,45:47,46:47},4180:{19:18},2140:{48:49,19:18,45:44,46:44,47:49},4190:{19:18},2150:{48:50,49:50,19:18,46:45,47:45},4200:{19:18},2160:{48:47,49:47,50:52,19:18,51:52},4210:{19:18},2170:{19:18,50:49,51:49,52:53},4220:{19:18},2180:{19:18,51:50,52:50,53:55,54:55},4230:{19:18},2190:{56:57,19:18,53:52,54:52,55:57},4240:{19:18},2200:{56:58,57:58,19:18,54:53,55:53},4250:{19:18},2210:{56:55,57:55,58:60,19:18,59:60},4260:{19:18},2220:{57:56,58:56,19:18,60:61,59:61},4270:{19:18},2230:{66:65,19:18,59:58,60:58,61:63,62:63},4280:{19:18},2240:{19:18,61:60,62:60,63:64},4290:{19:18},2250:{0:1,64:66,65:66,19:18,62:61,63:61},4300:{19:18},2260:{64:63,1:0,66:68,67:68,65:63,19:18},4310:{19:18},2270:{19:18,65:64,66:64,67:69,68:69,3:2},4320:{19:18},2280:{67:66,4:3,69:71,70:71,19:18,68:66},4330:{19:18},2290:{68:67,69:67,70:72,71:72,19:18,5:4},4340:{19:18},2300:{70:69,7:6,72:74,73:74,71:69,19:18},4350:{19:18},2310:{68:67,8:7,73:71,74:75,72:71,19:18},4360:{19:18},2320:{19:18,73:72,74:72,75:77,76:77},4370:{19:18},2330:{11:10,75:74,19:18,76:74,77:78},4380:{19:18},2340:{12:11,76:75,77:75,78:80,79:80,19:18},4390:{19:18},2350:{14:13,79:77,80:82,81:82,19:18,78:77},4400:{19:18},2360:{15:14,80:78,81:83,82:83,19:18,79:78},4410:{19:18,68:67},2370:{19:18,16:15,81:80,82:80,83:85,84:85},4420:{19:18},2380:{19:20,18:17,82:81,83:81,84:86,85:86},4430:{19:18},2390:{19:18,84:83,85:83,86:88,87:88},4440:{19:18},2400:{75:74,76:74,77:79,78:79,19:18,86:85,87:85,88:89},4450:{19:18},2410:{19:18},4460:{19:18},2420:{19:18},4470:{19:18},2430:{19:18,69:68},4480:{19:18},2440:{19:18},4490:{19:18,69:68},2450:{19:18},4500:{19:18},2460:{19:18},2470:{19:18},2480:{19:18},2490:{19:18},2500:{19:18},2510:{19:18},2520:{19:18},2530:{72:71,73:71,74:75,19:18},2540:{19:18,82:81,83:81,84:85},2550:{19:18,90:89,91:89,92:94,93:94},2560:{19:18,100:99},2570:{19:18},2580:{19:18},2590:{19:18},2600:{7:6,40:38,41:38,42:38,43:38,44:49,45:49,46:49,47:49,48:49,19:18,39:38},2610:{19:18,43:42,44:42,45:42,46:42,47:42,48:53,49:53,50:53,51:53,52:53},2620:{19:18,8:7,47:46,48:46,49:46,50:46,51:55,52:55,53:55,54:55},2630:{19:18,51:50,52:50,53:50,54:57,55:57,56:57},2640:{9:8,19:18,54:53,55:53,56:53,57:60,58:60,59:60},2650:{19:18,56:55,57:55,58:55,59:62,60:62,61:62},2660:{19:18,59:58,60:58,61:58,62:64,63:64},2670:{64:66,65:66,10:9,19:18,61:60,62:60,63:60},2680:{64:62,65:62,66:69,67:69,68:69,19:18,63:62},2690:{65:64,66:64,67:64,68:71,69:71,70:71,11:10,19:18},2700:{67:66,68:66,69:66,70:73,71:73,72:73,19:18},2710:{69:68,70:68,71:68,72:75,73:75,74:75,12:11,19:18},2720:{72:71,73:71,74:71,75:77,76:77,19:18},2730:{74:73,75:73,76:73,77:79,78:79,19:18},2740:{76:75,77:75,78:75,79:82,80:82,81:82,19:18},700:{8:7,9:10,42:41,43:41,44:41,45:41,46:41,47:51,48:51,49:51,50:51,19:18},2750:{19:18,78:77,79:77,80:77,81:84,82:84,83:84},710:{19:18,12:11,48:47,49:47,50:47,51:47,52:56,53:56,54:56,55:56,28:27,29:27,30:31},2760:{19:18,17:18,14:13,15:13,16:18,81:80,82:80,83:80,84:86,85:86},720:{37:36,38:36,39:41,8:7,15:14,40:41,19:18,54:53,55:53,56:53,57:53,58:62,59:62,60:62,61:62},2770:{19:18,22:24,23:24,83:82,20:18,21:18,86:88,87:88,84:82,85:82},730:{64:67,65:67,66:67,18:17,47:46,48:46,49:51,50:51,19:20,59:58,60:58,61:58,62:58,63:67},2780:{19:18,85:84,86:84,87:89,88:89,25:24,26:24,27:24,28:30,29:30},740:{65:64,66:64,67:64,68:64,69:73,70:73,71:73,72:73,9:8,19:18,21:20,22:23,57:56,58:56,59:61,60:61},2790:{32:29,33:35,34:35,19:18,87:86,88:86,89:91,90:91,30:29,31:29},750:{67:66,68:66,69:66,70:66,71:66,72:66,73:79,74:79,75:79,76:79,77:79,78:79,19:18,25:24},2800:{36:35,37:35,38:35,39:41,40:41,19:18,89:88,90:88,91:93,92:93},760:{19:18,76:75,77:75,78:75,79:75,80:84,81:84,82:84,83:84,28:27},2810:{42:41,43:41,44:41,45:47,46:47,19:18,90:89,91:89,92:89,93:95,94:95},770:{19:18,82:81,83:81,84:81,85:81,86:81,87:91,88:91,89:91,90:91,31:30},2820:{19:18,47:46,48:46,49:46,50:53,51:53,52:53,92:91,93:91,94:96,95:96},780:{97:96,34:33,99:96,100:96,98:96,19:18,87:86,88:86,89:86,90:86,91:95,92:95,93:95,94:95},2830:{96:98,97:98,19:18,53:52,54:52,55:52,56:58,57:58,94:93,95:93},790:{96:92,97:92,98:92,99:92,100:92,37:36,11:10,19:18,93:92,94:92,95:92},2840:{96:94,97:94,98:100,99:100,63:64,19:18,59:58,60:58,61:58,62:64,95:94},800:{40:39,19:18,98:97,99:97,100:97},2850:{19:18,65:64,66:64,67:64,68:70,69:70,97:96,98:96,99:96,100:96},810:{19:18,44:45,43:42,12:11},2860:{99:98,100:98,70:69,71:69,72:69,73:76,74:76,75:76,19:18},820:{19:18,46:45,47:48},2870:{76:75,77:75,78:75,79:81,80:81,19:18},830:{32:26,33:26,34:26,35:26,36:26,37:46,38:46,39:46,40:46,41:46,42:46,43:46,44:46,45:46,50:49,19:18,27:26,28:26,29:26,30:26,31:26},2880:{19:18,82:81,83:81,84:81,85:87,86:87},840:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:48,39:48,40:48,41:48,42:48,43:48,44:48,45:48,46:48,47:48,53:52},2890:{19:18,88:87,89:87,90:87,91:93,92:93},850:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:50,40:50,41:50,42:50,43:50,44:50,45:50,46:50,47:50,48:50,49:50,56:55},2900:{96:98,97:98,19:18,93:92,94:92,95:92},860:{14:13,15:16,19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:52,41:52,42:52,43:52,44:52,45:52,46:52,47:52,48:52,49:52,50:52,51:52,59:58},2910:{19:18,99:98,100:98},870:{18:17,19:17,20:22,21:22,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:54,42:54,43:54,44:54,45:54,46:54,47:54,48:54,49:54,50:54,51:54,52:54,53:54,62:61},2920:{19:18},880:{19:18,23:22,24:22,25:22,26:22,27:22,28:22,29:22,30:22,31:22,32:22,33:22,34:22,35:22,36:22,37:22,38:22,39:22,40:56,41:56,42:56,43:56,44:56,45:56,46:56,47:56,48:56,49:56,50:56,51:56,52:56,53:56,54:56,55:56,65:64,66:67},2930:{19:18},890:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:26,42:56,43:56,44:56,45:56,46:56,47:56,48:56,49:56,50:56,51:56,52:56,53:56,54:56,55:56,68:67,69:70},2940:{19:18},900:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:26,42:56,43:56,44:56,45:56,46:56,47:56,48:56,49:56,50:56,51:56,52:56,53:56,54:56,55:56,72:71},2950:{19:18},910:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:55,42:55,43:55,44:55,45:55,46:55,47:55,48:55,49:55,50:55,51:55,52:55,53:55,54:55,75:74},2960:{19:18},920:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:55,42:55,43:55,44:55,45:55,46:55,47:55,48:55,49:55,50:55,51:55,52:55,53:55,54:55,78:77},2970:{19:18},930:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:55,42:55,43:55,44:55,45:55,46:55,47:55,48:55,49:55,50:55,51:55,52:55,53:55,54:55,81:80},2980:{19:18},940:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:54,42:54,43:54,44:54,45:54,46:54,47:54,48:54,49:54,50:54,51:54,52:54,53:54,57:56,58:56,59:60,84:83,85:86},2990:{19:18},950:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:54,42:54,43:54,44:54,45:54,46:54,47:54,48:54,49:54,50:54,51:54,52:54,53:54,62:61,63:61,64:66,65:66,87:86,88:89},3e3:{19:18},960:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:26,41:54,42:54,43:54,44:54,45:54,46:54,47:54,48:54,49:54,50:54,51:54,52:54,53:54,68:67,69:67,70:71,91:90},3010:{19:18},970:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:53,41:53,42:53,43:53,44:53,45:53,46:53,47:53,48:53,49:53,50:53,51:53,52:53,73:72,74:72,75:77,76:77,94:93},3020:{19:18},980:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:53,41:53,42:53,43:53,44:53,45:53,46:53,47:53,48:53,49:53,50:53,51:53,52:53,78:77,79:77,80:82,81:82},3030:{19:18},990:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:53,41:53,42:53,43:53,44:53,45:53,46:53,47:53,48:53,49:53,50:53,51:53,52:53,84:83,85:83,86:88,87:88,99:98,100:98},3040:{19:18},1e3:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:52,41:52,42:52,43:52,44:52,45:52,46:52,47:52,48:52,49:52,50:52,51:52,90:89,91:89,92:93},3050:{19:18},1010:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:52,41:52,42:52,43:52,44:52,45:52,46:52,47:52,48:52,49:52,50:52,51:52,95:94,96:94,97:98},3060:{19:18},1020:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:52,41:52,42:52,43:52,44:52,45:52,46:52,47:52,48:52,49:52,50:52,51:52},3070:{19:18},1030:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:52,41:52,42:52,43:52,44:52,45:52,46:52,47:52,48:52,49:52,50:52,51:52},3080:{19:18},1040:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:26,40:52,41:52,42:52,43:52,44:52,45:52,46:52,47:52,48:52,49:52,50:52,51:52},3090:{19:18},1050:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:51,40:51,41:51,42:51,43:51,44:51,45:51,46:51,47:51,48:51,49:51,50:51},3100:{19:18},1060:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:51,40:51,41:51,42:51,43:51,44:51,45:51,46:51,47:51,48:51,49:51,50:51},3110:{19:18},1070:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:51,40:51,41:51,42:51,43:51,44:51,45:51,46:51,47:51,48:51,49:51,50:51},3120:{19:18},1080:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:51,40:51,41:51,42:51,43:51,44:51,45:51,46:51,47:51,48:51,49:51,50:51},3130:{19:18},1090:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:50,40:50,41:50,42:50,43:50,44:50,45:50,46:50,47:50,48:50,49:50},3140:{19:18},1100:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:50,40:50,41:50,42:50,43:50,44:50,45:50,46:50,47:50,48:50,49:50},3150:{19:18},1110:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:50,40:50,41:50,42:50,43:50,44:50,45:50,46:50,47:50,48:50,49:50},3160:{19:18},1120:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:50,40:50,41:50,42:50,43:50,44:50,45:50,46:50,47:50,48:50,49:50},3170:{19:18},1130:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:50,40:50,41:50,42:50,43:50,44:50,45:50,46:50,47:50,48:50,49:50},3180:{19:18},1140:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:49,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49},3190:{19:18},1150:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:49,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49},3200:{19:18},1160:{19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:49,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49},3210:{19:18},1170:{0:1,19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:49,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49},3220:{19:18},1180:{0:8,1:8,2:8,3:8,4:8,5:8,6:8,7:8,19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:49,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49},3230:{19:18},1190:{2:1,3:1,4:1,5:1,6:1,7:1,8:14,9:14,10:14,11:14,12:14,13:14,19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:49,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49},3240:{19:18},1200:{9:8,10:8,11:8,12:8,13:8,14:8,15:20,16:20,17:20,18:20,19:20,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:49,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49},3250:{19:18},1210:{15:14,16:14,17:14,18:14,19:14,20:14,21:14,22:14,23:14,24:14,25:14,26:14,27:14,28:14,29:14,30:14,31:14,32:48,33:48,34:48,35:48,36:48,37:48,38:48,39:48,40:48,41:48,42:48,43:48,44:48,45:48,46:48,47:48},3260:{0:2,1:2,19:18},1220:{19:18,22:21,23:21,24:21,25:21,26:21,27:21,28:21,29:21,30:21,31:21,32:21,33:21,34:21,35:48,36:48,37:48,38:48,39:48,40:48,41:48,42:48,43:48,44:48,45:48,46:48,47:48},3270:{0:8,1:8,2:8,3:8,4:8,5:8,6:8,7:8,19:18},1230:{32:26,33:26,34:26,35:26,36:26,37:47,38:47,39:47,40:47,41:47,42:47,43:47,44:47,45:47,46:47,19:18,27:26,28:26,29:26,30:26,31:26},3280:{0:15,1:15,2:15,3:15,4:15,5:15,6:15,7:15,8:15,9:15,10:15,11:15,12:15,13:15,14:15,19:18},1240:{32:26,33:26,34:26,35:26,36:26,37:47,38:47,39:47,40:47,41:47,42:47,43:47,44:47,45:47,46:47,19:18,27:26,28:26,29:26,30:26,31:26},3290:{4:3,5:3,6:3,7:3,8:3,9:3,10:3,11:3,12:3,13:21,14:21,15:21,16:21,17:21,18:21,19:21,20:21},1250:{0:7,1:7,2:7,3:7,4:7,5:7,6:7,19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:48,39:48,40:48,41:48,42:48,43:48,44:48,45:48,46:48,47:48},3300:{11:10,12:10,13:10,14:10,15:10,16:10,17:10,18:10,19:10,20:28,21:28,22:28,23:28,24:28,25:28,26:28,27:28},1260:{0:13,1:13,2:13,3:13,4:13,5:13,6:13,7:13,8:13,9:13,10:13,11:13,12:13,19:18,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:26,38:26,39:51,40:51,41:51,42:51,43:51,44:51,45:51,46:51,47:51,48:51,49:51,50:51},3310:{32:34,33:34,18:17,19:17,20:17,21:17,22:17,23:17,24:17,25:17,26:34,27:34,28:34,29:34,30:34,31:34},1270:{2:1,3:1,4:1,5:1,6:1,7:1,8:1,9:1,10:1,11:20,12:20,13:20,14:20,15:20,16:20,17:20,18:20,19:20,27:26,28:26,29:26,30:26,31:26,32:26,33:26,34:26,35:26,36:26,37:47,38:47,39:47,40:47,41:47,42:47,43:47,44:47,45:47,46:47,48:47,49:47,50:47,51:47,52:55,53:55,54:55},3320:{32:34,33:34,19:18,25:24,26:24,27:24,28:24,29:24,30:34,31:34},1280:{9:8,10:8,11:8,12:8,13:8,14:8,15:8,16:8,17:8,18:8,19:8,20:8,21:8,22:8,23:8,24:8,25:8,26:8,27:8,28:46,29:46,30:46,31:46,32:46,33:46,34:46,35:46,36:46,37:46,38:46,39:46,40:46,41:46,42:46,43:46,44:46,45:46,52:51,53:51,54:51,55:58,56:58,57:58},3330:{32:31,33:34,19:18},1290:{16:15,17:15,18:15,19:15,20:15,21:15,22:15,23:15,24:15,25:15,26:15,27:15,28:15,29:15,30:15,31:46,32:46,33:46,34:46,35:46,36:46,37:46,38:46,39:46,40:46,41:46,42:46,43:46,44:46,45:46,55:54,56:54,57:54,58:61,59:61,60:61},3340:{19:18},1300:{19:18,23:22,24:22,25:22,26:22,27:22,28:22,29:22,30:22,31:22,32:22,33:22,34:22,35:46,36:46,37:46,38:46,39:46,40:46,41:46,42:46,43:46,44:46,45:46,58:57,59:57,60:57,61:64,62:64,63:64},3350:{19:18},1310:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:27,38:27,39:49,40:49,41:49,42:49,43:49,44:49,45:49,46:49,47:49,48:49,62:61,63:61,64:61,65:68,66:68,67:68},3360:{19:18},1320:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:46,38:46,39:46,40:46,41:46,42:46,43:46,44:46,45:46,52:51,65:64,66:64,67:64,68:71,69:71,70:71},3370:{19:18},1330:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:46,38:46,39:46,40:46,41:46,42:46,43:46,44:46,45:46,56:55,57:58,68:67,69:67,70:67,71:67,72:75,73:75,74:75},3380:{19:18},1340:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:45,38:45,39:45,40:45,41:45,42:45,43:45,44:45,72:71,73:71,74:71,75:78,76:78,77:78},3390:{19:18},1350:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:45,38:45,39:45,40:45,41:45,42:45,43:45,44:45,75:74,76:74,77:74,78:81,79:81,80:81},3400:{19:18},1360:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:45,38:45,39:45,40:45,41:45,42:45,43:45,44:45,79:78,80:78,81:78,82:85,83:85,84:85},3410:{19:18},1370:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:45,38:45,39:45,40:45,41:45,42:45,43:45,44:45,47:46,82:81,83:81,84:81,85:88,86:88,87:88},3420:{19:18},1380:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:45,38:45,39:45,40:45,41:45,42:45,43:45,44:45,47:46,85:84,86:84,87:84,88:91,89:91,90:91},3430:{19:18},1390:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:27,37:45,38:45,39:45,40:45,41:45,42:45,43:45,44:45,47:46,88:87,89:87,90:87,91:87,92:95,93:95,94:95},3440:{19:18},1400:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:44,37:44,38:44,39:44,40:44,41:44,42:44,43:44,47:46,48:49,92:91,93:91,94:91,95:98,96:98,97:98},3450:{19:18},1410:{19:18,28:27,29:27,30:27,31:27,32:27,33:27,34:27,35:27,36:44,37:44,38:44,39:44,40:44,41:44,42:44,43:44,48:47,95:94,96:94,97:94,98:94,99:94,100:94},3460:{19:18},1420:{32:27,33:27,34:27,35:27,36:44,37:44,38:44,39:44,40:44,41:44,42:44,43:44,48:47,99:98,100:98,31:27,28:27,29:27,30:27,19:18},3470:{19:18},1430:{32:27,33:27,34:27,35:27,36:44,37:44,38:44,39:44,40:44,41:44,42:44,43:44,48:47,49:50,19:18,28:27,29:27,30:27,31:27},3480:{19:18},1440:{32:27,33:27,34:27,35:27,36:44,37:44,38:44,39:44,40:44,41:44,42:44,43:44,48:47,49:50,19:18,28:27,29:27,30:27,31:27},3490:{19:18},1450:{32:27,33:27,34:27,35:27,36:43,37:43,38:43,39:43,40:43,41:43,42:43,19:18,28:27,29:27,30:27,31:27},3500:{19:18},1460:{32:27,33:27,34:27,35:27,36:43,37:43,38:43,39:43,40:43,41:43,42:43,50:49,19:18,28:27,29:27,30:27,31:27},3510:{19:18},1470:{32:27,33:27,34:27,35:27,36:43,37:43,38:43,39:43,40:43,41:43,42:43,50:49,19:18,28:27,29:27,30:27,31:27},3520:{19:18},1480:{32:27,33:27,34:27,35:27,36:43,37:43,38:43,39:43,40:43,41:43,42:43,50:49,19:18,28:27,29:27,30:27,31:27},3530:{19:18},1490:{32:27,33:27,34:27,35:27,36:43,37:43,38:43,39:43,40:43,41:43,42:43,50:49,51:52,31:27,28:27,29:27,30:27,19:18},3540:{19:18},1500:{32:27,33:27,34:27,35:42,36:42,37:42,38:42,39:42,40:42,41:42,51:50,31:27,28:27,29:27,30:27,19:18},3550:{19:18},1510:{32:27,33:27,34:27,35:42,36:42,37:42,38:42,39:42,40:42,41:42,51:50,31:27,28:27,29:27,30:27,19:18},3560:{19:18},1520:{32:27,33:27,34:27,35:42,36:42,37:42,38:42,39:42,40:42,41:42,19:18,52:51,28:27,29:27,30:27,31:27},3570:{19:18},1530:{32:31,35:34,37:36,38:36,39:36,40:42,41:42,19:18,52:51},3580:{19:18},1540:{32:31,35:34,37:36,38:36,39:36,40:42,41:42,19:18,52:51},3590:{19:18},1550:{19:18,53:52},3600:{19:18},1560:{19:18,53:52},3610:{19:18},1570:{19:18,53:52},3620:{19:18},1580:{19:18,54:53},3630:{34:33,19:18},1590:{19:18,54:53},3640:{19:18},1600:{19:18,54:53,55:56},3650:{19:18},1610:{19:18,55:54},3660:{19:18,35:34},1620:{41:40,19:18,55:54},3670:{19:18},1630:{56:57,19:18,55:54},3680:{19:18},1640:{19:18,56:55,57:55,58:55,59:61,60:61},3690:{19:18,36:35},1650:{64:65,19:18,56:55,57:55,58:55,59:55,60:55,61:65,62:65,63:65},3700:{19:18},1660:{64:69,65:69,66:69,67:69,68:69,19:18,56:55,57:55,58:55,59:55,60:55,61:55,62:55,63:69},3710:{19:18},1670:{64:58,65:58,66:73,67:73,68:73,69:73,70:73,71:73,72:73,19:18,59:58,60:58,61:58,62:58,63:58},3720:{19:18,37:36},1680:{64:62,65:62,66:62,67:62,68:62,69:62,70:77,71:77,72:77,73:77,74:77,75:77,76:77,19:18,63:62,51:50},3730:{19:18},1690:{67:66,68:66,69:66,70:66,71:66,72:66,73:66,74:81,75:81,76:81,77:81,78:81,79:81,80:81,19:18},3740:{19:18},1700:{19:18,71:70,72:70,73:70,74:70,75:70,76:70,77:70,78:85,79:85,80:85,81:85,82:85,83:85,84:85},3750:{19:18,38:37},1710:{19:18,75:74,76:74,77:74,78:74,79:74,80:74,81:74,82:89,83:89,84:89,85:89,86:89,87:89,88:89},3760:{19:18},1720:{64:63,19:18,79:78,80:78,81:78,82:78,83:78,84:78,85:78,86:93,87:93,88:93,89:93,90:93,91:93,92:93},3770:{19:18},1730:{96:97,19:18,82:81,83:81,84:81,85:81,86:81,87:81,88:81,89:81,90:97,91:97,92:97,93:97,94:97,95:97},3780:{19:18,39:38},1740:{96:86,97:86,98:86,99:86,100:86,71:70,19:18,87:86,88:86,89:86,90:86,91:86,92:86,93:86,94:86,95:86},3790:{19:18},1750:{96:90,97:90,98:90,99:90,100:90,19:18,91:90,92:90,93:90,94:90,95:90},3800:{19:18},1760:{96:94,97:94,98:94,99:94,100:94,78:77,19:18,95:94},3810:{40:39,19:18},1770:{19:18,99:98,100:98},3820:{19:18},1780:{19:18},3830:{19:18},1790:{19:18},3840:{41:40,19:18},1800:{19:18,91:90},3850:{19:18},1810:{19:18},3860:{19:18},1820:{19:18,98:97,48:47,49:47,50:52,51:52},3870:{19:18},1830:{49:48,50:48,19:18,52:53,51:53},3880:{19:18,42:41,43:41,44:45},1840:{19:18,50:49,51:49,52:54,53:54},3890:{19:18,45:44,46:44,47:48},1850:{19:18,51:50,52:50,53:55,54:55},3900:{48:47,49:47,50:51,19:18},1860:{19:18,52:51,53:51,54:56,55:56},3910:{19:18,52:51,53:51,54:55},1870:{56:57,19:18,53:52,54:52,55:57},3920:{56:54,57:58,19:18,55:54},1880:{56:58,57:58,19:18,54:53,55:53},3930:{19:18,58:57,59:57,60:61},1890:{56:54,57:58,19:18,55:54},3940:{19:18,61:60,62:60,63:64},1900:{56:55,57:55,58:59,19:18},3950:{64:63,65:63,66:68,19:18,67:68},1910:{57:56,58:56,19:18,59:60},3960:{19:18,68:67,69:67,70:71},1920:{19:18,58:57,59:57,60:61},3970:{72:70,73:75,74:75,19:18,71:70},1930:{19:18,59:58,60:58,61:62},3980:{19:18,75:74,76:74,77:78},1940:{19:18,60:59,61:59,62:63},3990:{80:82,81:82,19:18,78:77,79:77},1950:{19:18,61:60,62:60,63:64},4e3:{19:18,82:81,83:81,84:85},1960:{64:65,19:18,62:61,63:61},4010:{19:18,85:84,86:84,87:88},1970:{64:66,65:66,19:18,62:61,63:61},4020:{88:87,89:87,90:91,19:18},1980:{64:62,65:67,66:67,19:18,63:62},4030:{19:18,91:90,92:90,93:94},1990:{64:63,65:63,66:68,19:18,67:68},4040:{96:97,19:18,94:93,95:93},2e3:{19:18,65:64,66:64,67:69,68:69},4050:{19:18,97:96,98:96,99:96,100:96},2010:{66:65,67:65,68:70,69:70,19:18,25:24,26:24,27:28},4060:{19:18},2020:{67:66,68:66,69:66,70:72,71:72,19:18,26:25,27:25,28:30,29:30},4070:{19:18},2030:{68:67,69:67,70:67,71:67,72:67,73:67,74:67,75:81,76:81,77:81,78:81,79:81,80:81,19:18,28:27,29:27,30:31},4080:{19:18},2040:{19:18,29:28,30:28,31:33,32:33,69:68,70:68,71:68,72:68,73:68,74:68,75:68,76:68,77:68,78:68,79:68,80:90,81:90,82:90,83:90,84:90,85:90,86:90,87:90,88:90,89:90},4090:{19:18}},I={2050:{19:18,31:30,32:30,33:35,34:35,70:69,71:69,72:69,73:69,74:69,75:69,76:69,77:69,78:69,79:69,80:69,81:69,82:69,83:69,84:69,85:69,86:69,87:69,88:69,89:69,90:69,91:69,92:69,93:69,94:69,95:69,96:69,97:69,98:69,99:69,100:69},4100:{19:18},2060:{19:18,32:31,33:31,34:36,35:36,75:74,76:74,77:74,78:74,79:74,80:74,81:74,82:74,83:74,84:74,85:74,86:74,87:74,88:74,89:74,90:74,91:74,92:74,93:74,94:74,95:74,96:74,97:74,98:74,99:74,100:74},4110:{19:18},2070:{19:18,34:33,35:33,36:38,37:38,84:83,85:83,86:83,87:83,88:83,89:83,90:83,91:83,92:83,93:83,94:83,95:83,96:83,97:83,98:83,99:83,100:83},4120:{19:18},2080:{96:92,97:92,98:92,99:92,36:35,37:35,38:39,19:18,100:92,93:92,94:92,95:92},4130:{19:18},2090:{40:41,19:18,37:36,38:36,39:41},4140:{19:18},2100:{40:38,41:42,19:18,39:38},4150:{19:18},2110:{40:39,41:39,42:44,19:18,43:44},4160:{19:18},2120:{19:18,42:41,43:41,44:46,45:46},4170:{19:18},2130:{19:18,43:42,44:42,45:47,46:47},4180:{19:18},2140:{48:49,19:18,45:44,46:44,47:49},4190:{19:18},2150:{48:50,49:50,19:18,46:45,47:45},4200:{19:18},2160:{48:47,49:47,50:52,19:18,51:52},4210:{19:18},2170:{19:18,50:49,51:49,52:53},4220:{19:18},2180:{19:18,51:50,52:50,53:55,54:55},4230:{19:18},2190:{56:57,19:18,53:52,54:52,55:57},4240:{19:18},2200:{56:58,57:58,19:18,54:53,55:53},4250:{19:18},2210:{56:55,57:55,58:60,19:18,59:60},4260:{19:18},2220:{57:56,58:56,19:18,60:61,59:61},4270:{19:18},2230:{66:65,19:18,59:58,60:58,61:63,62:63},4280:{19:18},2240:{19:18,61:60,62:60,63:64},4290:{19:18},2250:{0:1,64:66,65:66,19:18,62:61,63:61},4300:{19:18},2260:{64:63,1:0,66:68,67:68,65:63,19:18},4310:{19:18},2270:{19:18,65:64,66:64,67:69,68:69,3:2},4320:{19:18},2280:{67:66,4:3,69:71,70:71,19:18,68:66},4330:{19:18},2290:{68:67,69:67,70:72,71:72,19:18,5:4},4340:{19:18},2300:{70:69,7:6,72:74,73:74,71:69,19:18},4350:{19:18},2310:{68:67,8:7,73:71,74:75,72:71,19:18},4360:{19:18},2320:{19:18,73:72,74:72,75:77,76:77},4370:{19:18},2330:{11:10,75:74,19:18,76:74,77:78},4380:{19:18},2340:{12:11,76:75,77:75,78:80,79:80,19:18},4390:{19:18},2350:{14:13,79:77,80:82,81:82,19:18,78:77},4400:{19:18},2360:{15:14,80:78,81:83,82:83,19:18,79:78},4410:{19:18,68:67},2370:{19:18,16:15,81:80,82:80,83:85,84:85},4420:{19:18},2380:{19:20,18:17,82:81,83:81,84:86,85:86},4430:{19:18},2390:{19:18,84:83,85:83,86:88,87:88},4440:{19:18},2400:{75:74,76:74,77:79,78:79,19:18,86:85,87:85,88:89},4450:{19:18},2410:{19:18},4460:{19:18},2420:{19:18},4470:{19:18},2430:{19:18,69:68},4480:{19:18},2440:{19:18},4490:{19:18,69:68},2450:{19:18},4500:{19:18},2460:{19:18},2470:{19:18},2480:{19:18},2490:{19:18},2500:{19:18},2510:{19:18},2520:{19:18},2530:{72:71,73:71,74:75,19:18},2540:{19:18,82:81,83:81,84:85},2550:{19:18,90:89,91:89,92:94,93:94},2560:{19:18,100:99},2570:{19:18},2580:{19:18},2590:{19:18},2600:{19:18,7:6},2610:{19:18},2620:{8:7,19:18},2630:{19:18},2640:{9:8,19:18},2650:{19:18},2660:{19:18,68:67},2670:{10:9,19:18},2680:{19:18},2690:{11:10,19:18},2700:{19:18},2710:{19:18,12:11},2720:{19:18},2730:{19:18},2740:{19:18},700:{64:65,58:54,59:54,9:8,61:65,60:65,56:54,19:18,57:54,55:54,88:87,89:87,90:87,91:94,92:94,93:94,62:65,63:65},2750:{39:38,40:38,41:38,42:38,43:38,44:48,45:48,46:48,47:48,19:18},710:{64:60,65:60,66:71,67:71,68:71,69:71,70:71,12:11,98:97,29:27,99:97,30:31,100:97,63:60,28:27,61:60,62:60,19:18},2760:{19:18,16:18,17:18,43:42,44:42,45:42,14:13,15:13,48:52,49:52,50:52,51:52,46:42,47:42},720:{68:67,37:36,38:36,39:41,40:41,73:67,74:79,75:79,76:79,77:79,78:79,15:14,72:67,19:18,71:67,70:67,69:67},2770:{19:18,22:24,23:24,47:46,48:46,49:46,50:46,51:46,20:18,21:18,54:56,55:56,52:56,53:56},730:{19:20,48:46,18:17,49:51,79:74,75:74,76:74,50:51,78:74,77:74,80:85,81:85,82:85,83:85,84:85,47:46},2780:{51:49,25:24,29:30,50:49,19:18,52:49,53:49,54:49,55:59,56:59,57:59,58:59,27:24,28:30,26:24},740:{83:81,59:61,22:23,57:56,82:81,19:18,84:81,21:20,86:81,87:92,88:92,89:92,58:56,91:92,60:61,90:92,85:81},2790:{32:29,33:35,34:35,19:18,30:29,54:53,55:53,56:53,57:53,58:53,59:63,60:63,61:63,62:63,31:29},750:{96:99,97:99,98:99,67:66,68:66,69:71,70:71,19:18,25:24,89:88,90:88,91:88,92:88,93:88,94:99,95:99},2800:{64:66,65:66,36:35,37:35,38:35,39:41,40:41,19:18,57:56,58:56,59:56,60:56,61:56,62:66,63:66},760:{96:94,97:94,98:94,99:94,100:94,7:6,77:76,78:76,79:81,80:81,19:18,28:27,95:94},2810:{19:18,42:41,43:41,44:41,45:47,46:47,61:60,62:60,63:60,64:60,65:60,66:60,67:60,68:60,69:60,70:60,71:81,72:81,73:81,74:81,75:81,76:81,77:81,78:81,79:81,80:81},770:{19:18,87:86,88:86,89:91,90:91,31:30},2820:{19:18,47:46,48:46,49:46,50:53,51:53,52:53,65:64,66:64,67:64,68:70,69:70,71:70,72:70,73:70,74:70,75:70,76:70,77:70,78:70,79:70,80:70,81:70,82:70,83:95,84:95,85:95,86:95,87:95,88:95,89:95,90:95,91:95,92:95,93:95,94:95},780:{97:96,34:33,99:96,100:96,98:96,19:18},2830:{19:18,53:52,54:52,55:52,56:58,57:58,73:72,74:72,75:72,76:72,77:72,78:72,79:72,80:72,81:72,82:72,83:72,84:72,85:72,86:72,87:72,88:72,89:72,90:72,91:72,92:72,93:72,94:72,95:72,96:72,97:72,98:72,99:72,100:72},790:{9:8,19:18,37:36},2840:{96:87,97:87,98:87,99:87,100:87,72:71,60:58,63:64,61:58,59:58,19:18,62:64,88:87,89:87,90:87,91:87,92:87,93:87,94:87,95:87},800:{40:39,19:18},2850:{65:64,66:64,67:64,68:70,69:70,73:72,19:18},810:{19:18,43:42,44:45},2860:{70:69,71:69,72:69,73:76,74:76,75:76,19:18},820:{11:10,19:18,46:45,47:48},2870:{76:75,77:75,78:75,79:81,80:81,19:18},830:{50:49,19:18,12:11},2880:{19:18,82:81,83:81,84:81,85:87,86:87},840:{19:18,53:52},2890:{19:18,88:87,89:87,90:87,91:93,92:93},850:{56:55,19:18},2900:{96:98,97:98,19:18,93:92,94:92,95:92},860:{19:18,59:58,14:13,15:16},2910:{19:18,99:98,100:98},870:{18:17,19:17,20:22,21:22,62:61},2920:{19:18},880:{65:64,66:67,19:18,23:22,24:22,25:27,26:27},2930:{19:18},890:{32:33,68:67,69:70,19:18,29:28,30:28,31:33},2940:{19:18},900:{34:33,35:33,36:38,37:38,72:71,19:18},2950:{19:18},910:{40:39,41:39,42:43,19:18,75:74},2960:{19:18},920:{48:49,19:18,46:45,78:77,47:45},2970:{19:18},930:{19:18,81:80,51:50,52:50,53:54},2980:{19:18},940:{19:18,84:83,85:86,57:56,58:56,59:60},2990:{19:18},950:{64:66,65:66,19:18,87:86,88:89,62:61,63:61},3e3:{19:18},960:{19:18,91:90,68:67,69:67,70:71},3010:{19:18},970:{73:72,74:72,75:77,76:77,19:18,94:93},3020:{19:18},980:{80:82,81:82,19:18,78:77,79:77},3030:{19:18},990:{99:98,100:98,19:18,84:83,85:83,86:88,87:88},3040:{19:18},1e3:{19:18,90:89,91:89,92:93},3050:{19:18},1010:{96:94,97:98,19:18,95:94},3060:{19:18},1020:{19:18},3070:{19:18},1030:{19:18},3080:{19:18},1040:{19:18},3090:{19:18},1050:{19:18},3100:{19:18},1060:{19:18},3110:{19:18},1070:{19:18},3120:{19:18},1080:{19:18},3130:{19:18},1090:{19:18},3140:{19:18},1100:{19:18},3150:{19:18},1110:{19:18},3160:{19:18},1120:{19:18},3170:{19:18},1130:{19:18},3180:{19:18},1140:{19:18},3190:{19:18},1150:{19:18},3200:{19:18},1160:{19:18},3210:{19:18},1170:{0:1,19:18},3220:{19:18},1180:{0:8,1:8,2:8,3:8,4:8,5:8,6:8,7:8,19:18},3230:{19:18},1190:{2:1,3:1,4:1,5:1,6:1,7:1,8:14,9:14,10:14,11:14,12:14,13:14,19:18},3240:{19:18},1200:{9:8,10:8,11:8,12:8,13:8,14:8,15:20,16:20,17:20,18:20,19:20},3250:{19:18},1210:{15:14,16:14,17:14,18:14,19:14,20:14,21:27,22:27,23:27,24:27,25:27,26:27},3260:{0:2,1:2,19:18},1220:{32:34,33:34,19:18,22:21,23:21,24:21,25:21,26:21,27:21,28:34,29:34,30:34,31:34},3270:{0:8,1:8,2:8,3:8,4:8,5:8,6:8,7:8,19:18},1230:{32:27,33:27,34:40,35:40,36:40,37:40,38:40,39:40,19:18,28:27,29:27,30:27,31:27},3280:{0:15,1:15,2:15,3:15,4:15,5:15,6:15,7:15,8:15,9:15,10:15,11:15,12:15,13:15,14:15,19:18},1240:{34:33,35:33,36:33,37:33,38:33,39:44,40:44,41:44,42:44,43:44,19:18},3290:{4:3,5:3,6:3,7:3,8:3,9:3,10:3,11:3,12:3,13:21,14:21,15:21,16:21,17:21,18:21,19:21,20:21},1250:{0:7,1:7,2:7,3:7,4:7,5:7,6:7,41:40,42:40,43:40,44:40,45:48,46:48,47:48,19:18},3300:{11:10,12:10,13:10,14:10,15:10,16:10,17:10,18:10,19:10,20:28,21:28,22:28,23:28,24:28,25:28,26:28,27:28},1260:{0:13,1:13,2:13,3:13,4:13,5:13,6:13,7:13,8:13,9:13,10:13,11:13,12:13,45:44,46:44,47:44,48:51,49:51,50:51,19:18},3310:{32:34,33:34,18:17,19:17,20:17,21:17,22:17,23:17,24:17,25:17,26:34,27:34,28:34,29:34,30:34,31:34},1270:{2:1,3:1,4:1,5:1,6:1,7:1,8:1,9:1,10:1,11:20,12:20,13:20,14:20,15:20,16:20,17:20,18:20,19:20,29:28,30:28,31:32,48:47,49:47,50:47,51:47,52:55,53:55,54:55},3320:{32:34,33:34,19:18,25:24,26:24,27:24,28:24,29:24,30:34,31:34},1280:{9:8,10:8,11:8,12:8,13:8,14:8,15:8,16:8,17:8,18:27,19:27,20:27,21:27,22:27,23:27,24:27,25:27,26:27,33:32,34:35,52:51,53:51,54:51,55:58,56:58,57:58},3330:{32:31,33:34,19:18},1290:{16:15,17:15,18:15,19:15,20:15,21:15,22:15,23:15,24:15,25:34,26:34,27:34,28:34,29:34,30:34,31:34,32:34,33:34,36:35,37:35,38:40,39:40,55:54,56:54,57:54,58:61,59:61,60:61},3340:{19:18},1300:{19:18,23:22,24:22,25:22,26:22,27:22,28:22,29:34,30:34,31:34,32:34,33:34,40:39,41:39,42:44,43:44,58:57,59:57,60:57,61:64,62:64,63:64},3350:{19:18},1310:{32:34,33:34,66:68,67:68,65:68,64:61,45:44,46:44,47:49,48:49,19:18,30:29,31:29,62:61,63:61},3360:{19:18},1320:{65:64,66:64,67:64,68:71,69:71,70:71,19:18,52:51,53:54},3370:{19:18},1330:{68:67,69:67,70:67,71:67,72:75,73:75,74:75,19:18,56:55,57:58},3380:{19:18},1340:{72:71,73:71,74:71,75:78,76:78,77:78,19:18},3390:{19:18},1350:{75:74,76:74,77:74,78:81,79:81,80:81,19:18},3400:{19:18},1360:{19:18,79:78,80:78,81:78,82:85,83:85,84:85},3410:{19:18},1370:{19:18,47:46,82:81,83:81,84:81,85:88,86:88,87:88},3420:{19:18},1380:{47:46,19:18,85:84,86:84,87:84,88:91,89:91,90:91},3430:{19:18},1390:{47:46,19:18,88:87,89:87,90:87,91:87,92:95,93:95,94:95},3440:{19:18},1400:{96:98,97:98,47:46,48:49,19:18,92:91,93:91,94:91,95:98},3450:{19:18},1410:{96:94,97:94,98:94,99:94,100:94,48:47,19:18,95:94},3460:{19:18},1420:{48:47,19:18,99:98,100:98},3470:{19:18},1430:{48:47,49:50,19:18},3480:{19:18},1440:{48:47,49:50,19:18},3490:{19:18},1450:{19:18},3500:{19:18},1460:{50:49,19:18},3510:{19:18},1470:{50:49,19:18},3520:{19:18},1480:{50:49,19:18},3530:{19:18},1490:{19:18,50:49,51:52,37:36},3540:{19:18},1500:{19:18,51:50},3550:{19:18},1510:{19:18,51:50},3560:{19:18},1520:{19:18,52:51},3570:{19:18},1530:{19:18,52:51},3580:{19:18},1540:{19:18,52:51},3590:{19:18},1550:{19:18,53:52},3600:{19:18},1560:{19:18,53:52},3610:{19:18},1570:{19:18,53:52},3620:{19:18},1580:{19:18,54:53},3630:{34:33,19:18},1590:{19:18,54:53},3640:{19:18},1600:{19:18,54:53,55:56},3650:{19:18},1610:{19:18,55:54},3660:{19:18,35:34},1620:{41:40,19:18,55:54},3670:{19:18},1630:{56:57,19:18,55:54},3680:{19:18},1640:{19:18,56:55,57:55,58:55,59:61,60:61},3690:{19:18,36:35},1650:{64:65,19:18,56:55,57:55,58:55,59:55,60:55,61:65,62:65,63:65},3700:{19:18},1660:{64:69,65:69,66:69,67:69,68:69,19:18,56:55,57:55,58:55,59:55,60:55,61:55,62:55,63:69},3710:{19:18},1670:{64:58,65:58,66:73,67:73,68:73,69:73,70:73,71:73,72:73,19:18,59:58,60:58,61:58,62:58,63:58},3720:{19:18,37:36},1680:{64:62,65:62,66:62,67:62,68:62,69:62,70:77,71:77,72:77,73:77,74:77,75:77,76:77,19:18,63:62,51:50},3730:{19:18},1690:{67:66,68:66,69:66,70:66,71:66,72:66,73:66,74:81,75:81,76:81,77:81,78:81,79:81,80:81,19:18},3740:{19:18},1700:{19:18,71:70,72:70,73:70,74:70,75:70,76:70,77:70,78:85,79:85,80:85,81:85,82:85,83:85,84:85},3750:{19:18,38:37},1710:{19:18,75:74,76:74,77:74,78:74,79:74,80:74,81:74,82:89,83:89,84:89,85:89,86:89,87:89,88:89},3760:{19:18},1720:{64:63,19:18,79:78,80:78,81:78,82:78,83:78,84:78,85:78,86:93,87:93,88:93,89:93,90:93,91:93,92:93},3770:{19:18},1730:{96:97,19:18,82:81,83:81,84:81,85:81,86:81,87:81,88:81,89:81,90:97,91:97,92:97,93:97,94:97,95:97},3780:{19:18,39:38},1740:{96:86,97:86,98:86,99:86,100:86,71:70,19:18,87:86,88:86,89:86,90:86,91:86,92:86,93:86,94:86,95:86},3790:{19:18},1750:{96:90,97:90,98:90,99:90,100:90,19:18,91:90,92:90,93:90,94:90,95:90},3800:{19:18},1760:{96:94,97:94,98:94,99:94,100:94,78:77,19:18,95:94},3810:{40:39,19:18},1770:{19:18,99:98,100:98},3820:{19:18},1780:{19:18},3830:{19:18},1790:{19:18},3840:{41:40,19:18},1800:{19:18,91:90},3850:{19:18},1810:{19:18},3860:{19:18},1820:{19:18,98:97,48:47,49:47,50:52,51:52},3870:{19:18},1830:{49:48,50:48,19:18,52:53,51:53},3880:{19:18,42:41,43:41,44:45},1840:{19:18,50:49,51:49,52:54,53:54},3890:{19:18,45:44,46:44,47:48},1850:{19:18,51:50,52:50,53:55,54:55},3900:{48:47,49:47,50:51,19:18},1860:{19:18,52:51,53:51,54:56,55:56},3910:{19:18,52:51,53:51,54:55},1870:{56:57,19:18,53:52,54:52,55:57},3920:{56:54,57:58,19:18,55:54},1880:{56:58,57:58,19:18,54:53,55:53},3930:{19:18,58:57,59:57,60:61},1890:{56:54,57:58,19:18,55:54},3940:{19:18,61:60,62:60,63:64},1900:{56:55,57:55,58:59,19:18},3950:{64:63,65:63,66:68,19:18,67:68},1910:{57:56,58:56,19:18,59:60},3960:{19:18,68:67,69:67,70:71},1920:{19:18,58:57,59:57,60:61},3970:{72:70,73:75,74:75,19:18,71:70},1930:{19:18,59:58,60:58,61:62},3980:{19:18,75:74,76:74,77:78},1940:{19:18,60:59,61:59,62:63},3990:{80:82,81:82,19:18,78:77,79:77},1950:{19:18,61:60,62:60,63:64},4e3:{19:18,82:81,83:81,84:85},1960:{64:65,19:18,62:61,63:61},4010:{19:18,85:84,86:84,87:88},1970:{64:66,65:66,19:18,62:61,63:61},4020:{88:87,89:87,90:91,19:18},1980:{64:62,65:67,66:67,19:18,63:62},4030:{19:18,91:90,92:90,93:94},1990:{64:63,65:63,66:68,19:18,67:68},4040:{96:97,19:18,94:93,95:93},2e3:{19:18,65:64,66:64,67:69,68:69},4050:{19:18,97:96,98:96,99:96,100:96},2010:{66:65,67:65,68:70,69:70,19:18,25:24,26:24,27:28},4060:{19:18},2020:{67:66,68:66,69:66,70:72,71:72,19:18,26:25,27:25,28:30,29:30},4070:{19:18},2030:{68:67,69:67,70:67,71:67,72:67,73:67,74:67,75:81,76:81,77:81,78:81,79:81,80:81,19:18,28:27,29:27,30:31},4080:{19:18},2040:{19:18,29:28,30:28,31:33,32:33,69:68,70:68,71:68,72:68,73:68,74:68,75:68,76:68,77:68,78:68,79:68,80:90,81:90,82:90,83:90,84:90,85:90,86:90,87:90,88:90,89:90},4090:{19:18}};function R(e,t,s){if(void 0===t&&(t=e,e=0),void 0===s&&(s=1),s>0&&e>=t||s<0&&e<=t)return[];for(var i=[],n=e;s>0?n<t:n>t;n+=s)i.push(n);return i}function N(e,t){let s=parseInt(e/t)-2,i=s,n=e-t*i;for(;n>=t;)if(n-=t,i+=1,i-s==10)throw"blad";return 2*n>=t&&(i+=1),i}class Rows{constructor(e,t){this.layout=null,this.fill_a=null,this.fill_b=null,this.amount=parseInt(e),this.heights=t.map(e=>parseInt(e)),this.bottom=[0],this.top=[0];let s=0;R(this.amount).map(e=>{this.bottom.push(s),s+=parseInt(this.heights[e]),this.top.push(s)})}}class MultitypeObject{constructor(e,t,s,i,n,o,r,a,l,c,h,_,p,d){this.x=e,this.row=t,this.lock=1==s,this.horizontal=i,this.vertical=n,this.support=o,this.doorL=r,this.doorR=a,this.legs=h,this.doorS=[0,e-_-p,e+_+p,e,e+d[8]][parseInt(l)],this.doorS_force="1"==c,this.door_settings=d}get_horizontal_points(e){return 1==this.horizontal?[this.x]:2==this.horizontal?[this.x,this.x-2*e]:3==this.horizontal?[this.x,this.x+2*e]:4==this.horizontal?[this.x-e,this.x+e]:[]}get_door_object(){return new Fill(this.x,this.doorL,this.doorR,this.row)}get_verticals_geometry(e,t){return this.gen_verticals_geometry(this.x,[this.row],e,t,2==this.vertical)}get_support_geometry(e,t,s){let i=[1,3].indexOf(this.support)>-1?-1:1;return{x1:this.x+(s+t)*i,y1:e.bottom[this.row]+t,z1:0,x2:this.x+t*i,y2:e.top[[3,4].indexOf(this.support)>-1&&this.row<e.amount?this.row+1:this.row]-t,z2:0}}get_legs_geometry(e,t){return this.gen_legs_geometry(this.x,e,t,this.row)}gen_verticals_geometry(e,t,s,i,n=!1){return t.filter(e=>0!=e).map(t=>({x1:e,y1:s.bottom[t]+i,x2:e,y2:s.top[t<s.amount&&n?t+1:t]-i}))}gen_horizontal_geometry(e,t,s,i){return{x1:e-i,y1:s,x2:t+i,y2:s}}gen_legs_geometry(e,t,s,i=0,n=-20,o=20){return[o,s-o].map(s=>({x1:e,y1:t.bottom[i]+n,z1:s}))}}class Fill{constructor(e,t,s,i){this.x=e,this.left=t,this.right=s,this.row=i}get_fill_geometry(e,t,s,i,n,o,r){let a=Math.abs(e.x-this.x)-2*o;if(0===this.right||0===e.left||!(r[0]<a&&a<r[1]))return{};let l=3===this.right?i.fill_b[this.row]:i.fill_a[this.row],c=a<r[2]+2*o&&!(a>r[3]&&s)?null:null!=t?t:this.x+o+N(a,2),h=this.x+o,_=e.x-o,p=i.bottom[this.row]+o,d=i.top[this.row]-o,u=n,f=n-r[6],m={};return[2].indexOf(l)>-1&&(m.drawers=[{x1:h,x2:_,y1:p,y2:d,z1:u,z2:f,type:0,flip:0}]),[1].indexOf(l)>-1&&null==c&&(m.doors=[{x1:h,x2:_,y1:p,y2:d,z1:u,z2:f,type:0,flip:[1,3].indexOf(this.right)>-1?0:1}]),[1].indexOf(l)>-1&&null!=c&&(m.doors=[{x1:h,x2:c,y1:p,y2:d,z1:u,z2:f,type:[1,3].indexOf(this.right)>-1?1:0,flip:1},{x1:c,x2:_,y1:p,y2:d,z1:u,z2:f,type:-1===[1,3].indexOf(this.right)?1:0,flip:0}]),[1,2,3].indexOf(l)>-1&&(m.backs=[{x1:h,x2:_,y1:p,y2:d,z1:r[7],z2:0}]),m}}const z=(e,t,s=null)=>e.length>t?e[t]:e.length>0?e[e.length-1]:s,w=(e,t,s,i,n,o)=>{e=parseInt(e);let r=o.proper_door_heights,a=o.drawer_max_position,l=441,c=z(n[11],t,441);return e=parseInt(e),[1,11].indexOf(e)>-1?l=c:[2,12].indexOf(e)>-1?l=r.indexOf(s)>-1?z(n[12],t,412):c:[3,13].indexOf(e)>-1?l=r.indexOf(s)>-1?z(n[13],t,113):c:21===e?l=i<=a?z(n[21],t,242):c:31===e?l=i<=a?z(n[31],t,223):c:22===e?l=r.indexOf(s)>-1?i<=a?z(n[22],t,213):z(n[12],t,412):i<=a?z(n[21],t,242):c:3===e.toString().length&&(l=e),function(e,t=s,n=i){let o=function(e,t,s){return 3==e?3:2==e&&s<=a?2:1==e&&r.indexOf(t)>-1?1:4};return 100*o(Math.floor(parseInt(e)/100),t,n)+10*o(Math.floor(parseInt(e)/10)%10,t,n)+e%10}(l)},M=(e,t)=>{let s=!1;return R(1,t.length,2).forEach(i=>{t[i-1]<=e&&e<=t[i]&&0==s&&(s=!0)}),s},P=(e,t)=>{let s=(e.doors||[]).sort((e,t)=>e.x1-t.x1);if(s.length<2)return e;if(s[0].x2!=s[1].x1||Math.abs(s[1].x2-s[0].x1)<t)return e;let i=s[0].x2;if(e.verticals=[{x1:i,x2:i,y1:s[0].y1,y2:s[0].y2}],s[0].x2-=9,s[1].x1+=9,[s[0].type,s[0].flip,s[1].type,s[1].flip]=[0,0,0,0],void 0!==e.backs||1==e.backs.length){var n=Object.assign({},e.backs[0]);n.x2=s[0].x2;var o=Object.assign({},e.backs[0]);o.x1=s[1].x1,e.backs=[n,o]}return e},H=(e,t,s)=>{let i=e[0].sort((e,t)=>e-t),n=[];R(Math.floor(i.length/2)).map(e=>2*e+1).map(e=>[i[e-1],i[e]]).map(t=>{let[s,i]=t,o=e[1].filter(e=>s+20<e&&e<i-20).sort((e,t)=>e-t);o=o.length>0?o:[s+20],o.forEach((e,t)=>{0==t&&Math.abs(s-e)>150&&n.push(s+20),(0==n.length||Math.abs(n[n.length-1]-e)>130)&&(n.push(e),n.length>1&&Math.abs(n[n.length-2]-n[n.length-1])>600&&n.splice(n.length-2,0,parseInt(Math.floor((n[n.length-2]+n[n.length-1])/2)))),t==o.length-1&&Math.abs(i-e)>150&&(n.push(i-20),Math.abs(n[n.length-2]-n[n.length-1])>600&&n.splice(n.length-2,0,parseInt(Math.floor((n[n.length-2]+n[n.length-1])/2))))})});let o=[];return n.forEach(e=>{o=o.concat(MultitypeObject.prototype.gen_legs_geometry(e,t,s))}),o},D=(e,t,s)=>{let i={};R(t.amount+1).forEach(t=>{i[t]=[],e.filter(e=>e.row==t&&0!=e.horizontal).forEach(e=>{i[t]=i[t].concat(e.get_horizontal_points(s))}),i[t]=i[t].sort((e,t)=>e-t)});let n=[];return Object.keys(i).forEach(e=>{let o=i[e];o.length<=1||R(1,o.length,2).forEach(i=>{n=n.concat(MultitypeObject.prototype.gen_horizontal_geometry(o[i-1],o[i],t.top[e],s))})}),n},v=(e,t,s=1e8)=>{if(e=parseFloat(e),t.map(e=>e[0]).indexOf(e)>-1){let s=t.filter(t=>t[0]==e);return 0==s.length?null:s[0][1]}var i=t.map((t,s)=>t[0]>=e?s:null).filter(e=>null!=e);if(0==i.length||0==i[0])return null;let n=[t[(i=i[0])-1][0],t[i][0]],o=[t[i-1][1],t[i][1]],r=N((e-n[0])*s,n[1]-n[0]);return o[0]+N((o[1]-o[0])*r,s)},C=(e,t,s,i,n,o,r)=>{n=n.map(e=>[parseInt(e[0]),parseInt(e[1])]);if("0"==s[1])var[a,l]=[parseInt(e),parseInt(t)];else{if("1"!=s[1])return null;var[a,l]=[parseInt(t),parseInt(e)]}let c=((e,t,s)=>{let i=v(t,s);return null!=i?i-e/2:null})(e,a,n);if(null==c)return null;if(null!=o){let t=((e,t,s,i,n)=>{if("1"==i[2])return v(t,s);if("2"==i[2]){let i=v(t,s);return null==i?null:N(i*(e-2*n),1e4)}return null})(e,l,o=o.map(e=>[parseInt(e[0]),parseInt(e[1])]),s,r);if(null==t)return null;c+=t}let h=parseInt(-e/2+i[0]),_=parseInt(e/2-i[1]);return h<=c&&c<=_?c:null},L=(e,t,s,i)=>{if(e==s)return;let n=e+1;return t.filter(t=>t.row==e).forEach(s=>{t.filter(t=>t.row==e+1&&Math.abs(s.x-t.x)<i).forEach(t=>{t&&0<Math.abs(s.x-t.x)&&Math.abs(s.x-t.x)<i&&(t.lock?s.lock||(s.x=t.x,s.lock=!0,n=e>0?e-1:e+1):(t.x=s.x,t.lock=!0))})}),L(n,t,s,i)},B=(e,t,s)=>{let i=e.horizontals,n=e.verticals.filter(e=>t.bottom.slice(1).indexOf(e.y1-9)+1==t.top.indexOf(e.y2+9));e.verticals.filter(e=>t.bottom.slice(1).indexOf(e.y1-9)+1!=t.top.indexOf(e.y2+9)).forEach(e=>{let o=t.bottom.slice(1).indexOf(parseInt(e.y1)-9)+1,r=t.top.indexOf(parseInt(e.y2)+9);n=n.concat(MultitypeObject.prototype.gen_verticals_geometry(e.x1,R(o,r+1),t,s)),R(o,r+1).forEach(n=>{i=i.concat(MultitypeObject.prototype.gen_horizontal_geometry(e.x1,e.x1,t.top[n],s))})});let o=t.top.map(e=>[[],[]]);return t.top.forEach((e,t)=>{i.filter(t=>t.y1==e).forEach(e=>{o[t][0]=o[t][0].concat([e.x1,e.x2])}),n.filter(t=>t.y2+9==e).forEach(e=>{o[t][1].push(e.x1)})}),o.forEach(e=>{e[0]=e[0].sort((e,t)=>e-t),e[1]=e[1].sort((e,t)=>e-t)}),o},j=(e,t,s)=>{let i=[];return e.forEach((e,n)=>{let o=e[0],r=e[1];o&&(o.push(-t/2),o.push(t/2),o=o.filter(e=>1==o.filter(t=>t===e).length).sort((e,t)=>e-t),o.length>0&&(o=R(1,o.length,2).map(e=>[o[e-1],o[e]]))),r&&(r=r.sort((e,t)=>e-t),r.length>0&&r[0]!=-t/2+s&&r.splice(0,0,-t/2-s),r.length>0&&r[r.length-1]!=t/2-s&&r.push(t/2+s),r.length>0&&(r=R(1,r.length).map(e=>[r[e-1]+s,r[e]-s]))),i.push([o,r])}),i},k=(e,t,s,i,n,o,r=2)=>{if(s<i.length-1&&i[s][0].length>=1&&i[s+1][1].length>=1){let a=i[s][0][i[s][0].map(t=>Math.abs(e-t[0])).indexOf(Math.min(...i[s][0].map(t=>Math.abs(e-t[0]))))];if(Math.abs(e-a[0])<r&&Math.abs(t-a[1])<r&&(a=i[s+1][1][i[s+1][1].map(t=>Math.abs(e-t[0])).indexOf(Math.min(...i[s+1][1].map(t=>Math.abs(e-t[0]))))],Math.abs(e-a[0])<r&&Math.abs(t-a[1])<r))return i[s+1][1].splice(i[s+1][1].indexOf(a),1),k(e,t,s+1,i,n,o,r)}return n.top[s]-o},W=(e,t,s,i,n,o)=>{null==o&&(o=[2,2,4,2]);let r={shadow_left:[],shadow_middle:[],shadow_right:[],shadow_side:[]};return R(e.length).forEach(a=>{if(!e[a][1])return!0;e[a][1].forEach(l=>{let[c,h]=l,_="shadow_middle";c==-s/2?_="shadow_left":h==s/2&&(_="shadow_right");let p=[t.bottom[a]+i,k(c,h,a,e,t,i)];r[_].push({x1:c+o[0],y1:p[0]+o[2],z1:0,x2:h-o[1],y2:p[1]-o[3],z2:n})})}),r},G=(e,t,s,i,n,o,r=320,a=9,l=125,c=!1,h=[2,2,4,2],_=!0,p=-1,d=null,u=-1,f)=>{a=parseInt(a),l=parseInt(l),r=parseInt(r),i=parseInt(i),t=parseInt(t);let m=e.door_settings;null==d&&(d=[]);let g=((e,t,s,i,n,o)=>{let r=(Object.keys(t).indexOf("style_list")>-1?t.style_list:[{}])[0],a=R(i).map(e=>441),l=new Rows(i,n);s>-1&&(e=z(z((Object.keys(t).indexOf("style_list")>-1?t.style_list:[{}]).slice(1),s,[a]),i));let c=[];return R(l.amount).map(t=>{c.push(w(z(e,t),t,l.heights[t],l.top[t+1],r,o))}),l.styles=c,l.layout=[1,...c.map(e=>parseInt(e)%10)],l.fill_a=[1,...c.map(e=>Math.floor(parseInt(e)/10)%10||1)],l.fill_b=[1,...c.map(e=>Math.floor(parseInt(e)/100)||1)],l})(o,e,p,i,n,f),y=(R(i+1).map(e=>[]),[R(i+1),R(i),[i]]);var x={};let b=[],E=Object.keys(e.elements).sort((e,t)=>parseInt(e)-parseInt(t)),O=R(0,E.length,1).map(e=>!1);E.forEach(i=>{let n=e.elements[i];if(!(parseInt(n.M[2])<=s&&s<=parseInt(n.M[3]))||n.M.length>4&&!M(t,n.M.slice(4)))return;if(Object.keys(n).indexOf("jeslitak")>-1&&!O[n.jeslitak])return;if(Object.keys(n).indexOf("jeslinie")&&O[n.jeslinie])return;let o=R(0,n.E.split("_")[1].substring(5).length,2).map(e=>""+(10*parseInt(n.E.split("_")[1][5+e])+parseInt(n.E.split("_")[1][6+e]))),r=((e,t,s,i,n)=>{let o=[];return s.map(s=>{s=parseInt(s),e[i].indexOf(s)>-1&&(n.indexOf("0")>-1||n.indexOf(t[s].toString())>-1)&&o.push(s)}),o})(y,g.layout,o,parseInt(n.E[5]),n.E.substring(6,9));if(null==r||0==r.length)return;parseInt(n.E[1]);let c=C(s,t,n.E.substring(0,3),n.M.slice(0,2),n.P1,Object.keys(n).indexOf("A1")>-1?n.A1:null,a);null!=c&&(O[parseInt(i)]=!0,b=b.concat(((e,t,s,i,n,o,r,a,l)=>{let c,h,_,p,d,u,f,m,g,y,x,b=[],E=[],O=[],S=[],T=[],A=s.E.split("_")[2],I=parseInt(A[2]);0!=I&&(c=-1==Object.keys(s).indexOf("H")||!1===s.H[2]||-1==s.H[2]?t:s.H.slice(2),-1==Object.keys(s).indexOf("H")||!1===s.H[0]||-1==s.H[0]||-i/2+s.H[0]<e&&e<i/2-s.H[1]||(I=0));let N=parseInt(A[3]);0!=N&&(h=-1==Object.keys(s).indexOf("V")||!1===s.V[2]||-1==s.V[2]?t:s.V.slice(2),-1==Object.keys(s).indexOf("V")||!1===s.V[0]||-1==s.V[0]||-i/2+s.V[0]<e&&e<i/2-s.V[1]||(N=0));let z=parseInt(A[4]);0!=z&&(_=-1==Object.keys(s).indexOf("S")||!1===s.S[2]||-1==s.S[2]?t:s.S.slice(2),-1==Object.keys(s).indexOf("S")||!1===s.S[0]||-1==s.S[0]||-i/2+s.S[0]<e&&e<i/2-s.S[1]||(z=0),f=Object.keys(s).indexOf("Ss")>-1?s.Ss:s.E.substring(6,8).split("").map(e=>parseInt(e)),f=-1==f.indexOf(0)?f:R(0,5));let w=parseInt(A[5]);0!=w&&(u=-1==Object.keys(s).indexOf("L")||!1===s.L[2]||-1==s.L[2]?t:s.L.slice(2),-1==Object.keys(s).indexOf("L")||!1===s.L[0]||-1==s.L[0]||-i/2+s.L[0]<e<i/2-s.L[1]||(w=0));let P=4==s.E.split("_").length?s.E.split("_")[3]:[];P?(("1"==P[1]||"2"==P[1]&&M(o,s.DL2))&&b.push(2),("1"==P[2]||"2"==P[2]&&M(o,s.DL3))&&b.push(3),("1"==P[3]||"2"==P[3]&&M(o,s.DL4))&&b.push(4),b.length>0&&(p=Object.keys(s).indexOf("DLr")>-1?s.DLr:t),("1"==P[4]||"2"==P[4]&&M(o,s.DR2)||"1"==P[7]||"2"==P[7]&&M(o,s.DRS2))&&E.push(2),("1"==P[5]||"2"==P[5]&&M(o,s.DR3)||"1"==P[8]||"2"==P[8]&&M(o,s.DRS3))&&E.push(3),("1"==P[6]||"2"==P[6]&&M(o,s.DR4)||"1"==P[9]||"2"==P[9]&&M(o,s.DRS4))&&E.push(4),E.length>0&&(d=Object.keys(s).indexOf("DRr")>-1?s.DRr:t,m="1"==P[10]||"2"==P[10]&&M(o,s.DRf)?2:1),("1"==P[7]||"2"==P[7]&&M(o,s.DRS2))&&S.push(2),("1"==P[8]||"2"==P[8]&&M(o,s.DRS3))&&S.push(3),("1"==P[9]||"2"==P[9]&&M(o,s.DRS4))&&S.push(4),S.length>0&&(T=Object.keys(s).indexOf("DRSr")>-1?s.DRSr:t),g=P[14],0==P[11]||-1!=Object.keys(s).indexOf("DS2")&&!M(o,s.DS2)||O.push(2),0==P[12]||-1!=Object.keys(s).indexOf("DS3")&&!M(o,s.DS3)||O.push(3),0==P[13]||-1!=Object.keys(s).indexOf("DS3")&&!M(o,s.DS3)||O.push(4),O.length>0&&(y=Object.keys(s).indexOf("DSr")>-1?s.DSr:t)):x=0;let H=[];return t.forEach(t=>{let s,i=I&&c.indexOf(t)>-1?I:0,o=N&&h.indexOf(t)>-1?N:0,R=z&&_.indexOf(t)>-1&&f.indexOf(n[t])>-1?z:0,M=w&&u.indexOf(t)>-1?w:0,D=0,v=0;P&&(D=b.length>0&&p.indexOf(t)>-1&&b.indexOf(n[t])>-1?1:0,v=E.length>0&&d.indexOf(t)>-1&&E.indexOf(n[t])>-1?m:0,x=S.length>0&&T.indexOf(t)>-1&&S.indexOf(n[t])>-1?1:0,x&&(v=3)),O.length>0&&y.indexOf(t)>-1&&O.indexOf(n[t])>-1?(s=2==n[t]?P[6]:P[7],s=2==n[t]?P[11]:3==n[t]?P[12]:P[13]):s=0,(i||o||R||D||v||s||M)&&H.push(new MultitypeObject(e,t,parseInt(A[1]),i,o,R,D,v,s,g,M,r,a,l))}),H})(c,r,n,s,g.layout,t,l,a,m)))}),-1==[0,"0"].indexOf(e.snapping)&&c&&L(1,b,g.amount,e.snapping),x={verticals:b.filter(e=>e.vertical).map(e=>e.get_verticals_geometry(g,a)).reduce((e,t)=>e.concat(t),[]),supports:b.filter(e=>e.support).map(e=>e.get_support_geometry(g,a,l)).reduce((e,t)=>e.concat(t),[]),legs:b.filter(e=>e.legs).map(e=>e.get_legs_geometry(g,r)).reduce((e,t)=>e.concat(t),[]),horizontals:D(b,g,a),doors:[],backs:[],drawers:[]};let S=((e,t,s,i,n,o)=>{let r={},a={};R(1,t.amount+1).forEach(t=>{r[t]=[];let s=e.filter(e=>e.row==t&&(0!=e.vertical||0!=e.doorL||0!=e.doorR)).map(e=>e.get_door_object());s=s.sort((e,t)=>e.x-t.x);let i=0;for(;i<s.length;){let e=s[i];for(;i<s.length-1&&e.x==s[i+1].x;)i+=1,[e.left,e.right]=[Math.max(e.left,s[i].left),Math.max(e.right,s[i].right)];r[t].push(e),i+=1}a[t]=e.filter(e=>e.row==t&&0!=e.doorS).map(e=>[e.doorS,e.doorS_force]).sort((e,t)=>e[0]-t[0])});let l={doors:[],drawers:[],backs:[],verticals:[]};return Object.keys(r).forEach(e=>{let c=r[e];c.length<=1||R(c.length-1).forEach(r=>{let h=null,_=!1,p=a[e].filter(e=>c[r].x+n[4]+i<e[0]&&e[0]<c[r+1].x-n[4]-i);p.length>0&&([h,_]=[p[0][0],p[0][1]]);let d=c[r].get_fill_geometry(c[r+1],h,_,t,s,i,n);-1!==o&&(d=P(d,o)),Object.keys(d).forEach((function(e){let t=d[e];l[e]=l[e].concat(t)}))})}),l})(b,g,r,a,m,u);for(const[e,t]of Object.entries(S))x[e]=x[e].concat(S[e]);let T=B(x,g,a);[1,"1"].indexOf(e.legs)>-1&&(x.legs=x.legs.concat(H([T[0][0],T[1][1]],g,r)));let A=j(T,s,a);x.additional_elements=W(A,g,s,a,r,h),_&&(x.additional_elements.styles=((e,t,s,i,n,o,r,a)=>{let l=[];return R(1,i.amount+1).forEach(c=>{let h,_;[h,_]=[i.bottom[c],i.top[c]];let[p,d]=[_-h<210,_<=r.drawer_max_position],u=s.filter(e=>e.y1==h+a+n[2]&&e.y2==_-a-n[3]&&e.x2-e.x1>=o[0]).length,f=e.filter(e=>0==e.type&&e.y1==h+a).length,m=t.filter(e=>e.y1==h+a).length;l.push([-1!=[f,u].indexOf(0)||p?2:1,[0,1].indexOf(u)>-1||p?0:u>f&&0!=f?2:1,0==u||p?0:u>f?1:2,-1==[m,u].indexOf(0)&&d?1:2,[0,1].indexOf(u)>-1||!d?0:u>m&&0!=m?2:1,0!=u&&d?u>m?1:2:0])}),l})(x.doors,x.drawers,x.additional_elements.shadow_middle,g,h,m,f,a)),null!=d&&d.length>0&&((e,t,s,i,n)=>{let o=e.backs,r=t.top.map(t=>e.verticals.filter(e=>e.y1==t+s).sort((e,t)=>e.x1-t.x1)),a=t.top.map(t=>e.horizontals.filter(e=>e.y1==t).sort((e,t)=>e.x1-t.x1)),l=t.top.map(t=>e.backs.filter(e=>e.y1==t+s).sort((e,t)=>e.x1-t.x1));t.top.slice(0,t.top.length-1).forEach((e,t)=>{n&&-1==n.indexOf(t)||r[t].slice(1).forEach((e,n)=>{let c=r[t][n],h=(e.x1+c.x1)/2,_=l[t].filter(e=>e.x1==c.x1+s);_=_.length>0?_[0]:null;let p=a[t].filter(e=>e.x1<h&&h<e.x2);p=p.length>0?p[0]:null;let d=a[t+1].filter(e=>e.x1<h&&h<e.x2);d=d.length>0?d[0]:null,null===_&&null!=d&&null!=p&&o.push({x1:c.x1+s,x2:e.x1-s,y1:p.y1+s,y2:d.y1-s,z1:i[7],z2:0})})})})(x,g,a,m,d);let I=f.shelf_type;return x.additional_elements.shelf_type=I,I<0&&(x.additional_elements.row_data={},x.additional_elements.row_data.layout=g.layout,x.additional_elements.row_data.fill_a=g.fill_a,x.additional_elements.row_data.fill_b=g.fill_b,x.additional_elements.row_data.amount=g.amount,x.additional_elements.row_data.bottom=g.bottom,x.additional_elements.row_data.top=g.top),x},F=(e,t)=>("flat"===t&&(delete e.additional_elements.row_data,delete(e=Object.assign(Object.assign({},e),e.additional_elements)).additional_elements),e.backs=e.backs.filter(e=>Math.abs(e.x2-e.x1)>129),e);class PositionBox{constructor(e){this.x1=PositionBox.positionCoalesce(Math.round(e.x1),Math.round(e.x2)),this.x2=PositionBox.positionCoalesce(Math.round(e.x2),Math.round(e.x1)),this.y1=PositionBox.positionCoalesce(Math.round(e.y1),Math.round(e.y2)),this.y2=PositionBox.positionCoalesce(Math.round(e.y2),Math.round(e.y1)),this.z1=PositionBox.positionCoalesce(Math.round(e.z1),Math.round(e.z2)),this.z2=PositionBox.positionCoalesce(Math.round(e.z2),Math.round(e.z1)),this.width=this.x2-this.x1,this.height=this.y2-this.y1,this.depth=this.z2-this.z1}static positionCoalesce(e,t){return Number.isInteger(e)?e:Number.isInteger(t)?t:0}}class PositionBoxWithParent extends PositionBox{constructor(e){super(e),this.c_config_id=e.c_config_id||-1,this.m_config_id=e.m_config_id||-1}}class PositionBoxWithParentAndSubtype extends PositionBoxWithParent{constructor(e){super(e),this.subtype=e.subtype}}class LongLeg extends PositionBoxWithParent{constructor(e){super(e),this.rotation_z=e.rotation_z}}class CableManagement extends PositionBoxWithParent{constructor(e){super(e),this.radius=e.radius}}class Hinge extends PositionBoxWithParent{constructor(e){super(e),this.direction=e.direction}}class Plinth extends PositionBoxWithParentAndSubtype{constructor(e){super(e),this.flip=e.flip}}class Door extends PositionBoxWithParentAndSubtype{constructor(e){super(e),this.type=e.type,this.flip=e.flip,this.innerOffset=e.innerOffset,this.direction=e.direction,this.main_direction=e.main_direction,this.handle_cfg=e.handle_cfg,this.offset_cfg=e.offset_cfg}}class Drawer extends PositionBoxWithParentAndSubtype{constructor(e){super(e),this.type=e.type,this.door_handler_width=e.door_handler_width,this.door_handler_height=e.door_handler_height,this.drawer_cutout=e.drawer_cutout,this.bottom_offset=e.bottom_offset,this.bottom_thickness=e.bottom_thickness,this.bottom_depth=e.bottom_depth,this.back_height=e.back_height,this.sides_length=e.sides_length,this.sides_height=e.sides_height,this.front_handling_size=e.front_handling_size,this.innerOffset=e.innerOffset,this.exterior=e.exterior}}class AdditionalElements{constructor(e){this.styles=e.styles,this.shadow_left=e.shadow_left.map(e=>new PositionBox(e)),this.shadow_middle=e.shadow_middle.map(e=>new PositionBox(e)),this.shadow_right=e.shadow_right.map(e=>new PositionBox(e)),this.shadow_side=e.shadow_side.map(e=>new PositionBox(e))}}class Component extends PositionBox{constructor(e){super(e),this.hover_left=e.hover_left?new PositionBox(e.hover_left):null,this.hover_right=e.hover_right?new PositionBox(e.hover_right):null,this.line_left=e.line_left,this.line_right=e.line_right,this.m_config_id=e.m_config_id,this.series_id=e.series_id,this.table_id=e.table_id,this.channel_id=e.channel_id,this.component_id=e.component_id,this.bounding_box=e.bounding_box?new PositionBox(e.bounding_box):null,this.bi_info=e.bi_info,this.channel_configuration=new ChannelConfig(e.channel_configuration),this.local_x=e.local_x}}class ConfigurationState{constructor(e){this.geom_id=e.geom_id,this.geom_type=e.geom_type||"mesh",this.width=e.width,this.height=e.height,this.depth=e.depth,this.material=e.material,this.density=e.density,this.distortion=e.distortion,this.mesh_setup=e.mesh_setup,this.plinth=e.plinth,this.configurator_custom_params=e.configurator_custom_params?new LocalConfigParams(e.configurator_custom_params):null,this.additional_parameters=e.additional_parameters}}class LocalConfigParams{constructor(e){this.channels=this.buildChannels(e.channels),this.lines=e.lines?Object.assign({},e.lines):{}}buildChannels(e){const t={};return e&&Object.keys(e).forEach(s=>{t[s]=new ChannelConfig(e[s])}),t}}class ChannelConfig{constructor(e){this.door_flip=e.door_flip||null,this.series_id=e.series_id,this.cables=null==e.cables?null:e.cables,this.distortion=e.distortion||null,this.order=e.order||-1}}class OldConfigurationState{constructor(e){this.motion=Number.isInteger(e.motion)?e.motion:e.property1,this.width=e.width,this.row_amount=Number.isInteger(e.rows)?e.rows:e.row_amount,this.row_heights=e.rowHeights||e.row_heights||e.rows,this.row_styles=e.rowStyles||e.row_styles,this.depth=e.depth,this.half_material=Number.isInteger(e.half_material)?e.half_material:9,this.support_size=Number.isInteger(e.support_size)?e.support_size:125,this.snapping=e.snapping,this.shadow_settings=e.shadow_settings||[2,2,4,2],this.gen_row_styles_for_buttons=e.gen_row_styles_for_buttons||!0,this.styles_slider=Number.isInteger(e.styles_slider)?e.styles_slider:-1,this.backpanels_rows=e.backpanels_rows||e.backpanel_styles,this.shelf_type=e.shelf_type}}class GalleryFormat{constructor(e){this.configurator_params=new ConfigurationState(e.configurator_params),this.components=e.components.map(e=>new Component(e)),this.horizontals=e.horizontals.map(e=>new PositionBoxWithParent(e)),this.verticals=e.verticals.map(e=>new PositionBoxWithParent(e)),this.supports=e.supports.map(e=>new PositionBoxWithParent(e)),this.backs=e.backs.map(e=>new PositionBoxWithParent(e)),this.legs=e.legs.map(e=>new PositionBoxWithParent(e)),this.long_legs=e.long_legs.map(e=>new LongLeg(e)),this.doors=e.doors.map(e=>new Door(e)),this.drawers=e.drawers.map(e=>new Drawer(e)),this.plinth=e.plinth.map(e=>new Plinth(e)),this.inserts=e.inserts.map(e=>new PositionBoxWithParentAndSubtype(e)),this.buttons=e.buttons.map(e=>new PositionBoxWithParent(e)),this.cable_management=e.cable_management.map(e=>new CableManagement(e)),this.shelf_type=e.shelf_type,this.physical_product_version=e.physical_product_version,this.furniture_type=e.furniture_type,this.pattern=e.pattern,this.configurator_type=e.configurator_type,this.material=e.material,this.height=e.height,this.width=e.width,this.depth=e.depth,this.additional_elements=new AdditionalElements(e.additional_elements),this.cast_shadows=e.cast_shadows}}class WardrobeGalleryFormat{constructor(e){this.configurator_params=new ConfigurationState(e.configurator_params),this.components=e.components.map(e=>new Component(e)),this.walls=e.walls.map(e=>new PositionBoxWithParent(e)),this.backs=e.backs.map(e=>new PositionBoxWithParentAndSubtype(e)),this.slabs=e.slabs.map(e=>new PositionBoxWithParentAndSubtype(e)),this.frame=e.frame.map(e=>new PositionBoxWithParentAndSubtype(e)),this.inserts=e.inserts.map(e=>new PositionBoxWithParentAndSubtype(e)),this.bars=e.bars.map(e=>new PositionBoxWithParent(e)),this.drawers=e.drawers.map(e=>new Drawer(e)),this.doors_exterior=e.doors_exterior.map(e=>new Door(e)),this.hinges=e.hinges.map(e=>new Hinge(e)),this.buttons=e.buttons.map(e=>new PositionBoxWithParent(e)),this.masking_bars=e.masking_bars.map(e=>new PositionBoxWithParent(e)),this.shelf_type=e.shelf_type,this.physical_product_version=e.physical_product_version,this.furniture_type=e.furniture_type,this.pattern=e.pattern,this.configurator_type=e.configurator_type,this.material=e.material,this.height=e.height,this.width=e.width,this.depth=e.depth,this.setup_id=e.setup_id,this.additional_elements=new AdditionalElements(e.additional_elements)}}var K,X,V;!function(e){e[e.GRASSHOPPER_BOOKCASE=0]="GRASSHOPPER_BOOKCASE",e[e.CAPE_BOOKCASE=1]="CAPE_BOOKCASE",e[e.CAPE_LOWBOARD=2]="CAPE_LOWBOARD",e[e.CAPE_SIDEBOARD=3]="CAPE_SIDEBOARD",e[e.CAPE_WARDROBE=4]="CAPE_WARDROBE"}(K||(K={})),function(e){e[e.GRASSHOPPER=0]="GRASSHOPPER",e[e.CAPE=1]="CAPE"}(X||(X={})),function(e){e[e.ROW=1]="ROW",e[e.COLUMN=2]="COLUMN",e[e.WARDROBE=3]="WARDROBE"}(V||(V={}));class PresetBuilder{constructor(){}get isWardrobeShelf(){return["SzafexMLP","Wardrobe"].includes(this._shelfCollection)}get isSideBoardShelf(){return"Sideboard"===this._shelfCollection}get isLowBoardShelf(){return"ShoeRack"===this._shelfCollection}get isBookcaseShefl(){return"Bookcase"===this._shelfCollection}get isGrassProject(){return this._projectType===X.GRASSHOPPER}get isCapeProject(){return this._projectType===X.CAPE}get shelfId(){return this._shelfId}get DNA(){return this._dna}setDNA(e){"dna_name"in e&&(e.dna_name.includes("GRID")&&(this._shelfType="GRID"),e.dna_name.includes("GRADIENT")&&(this._shelfType="GRADIENT"),e.dna_name.includes("SLANT")&&(this._shelfType="SLANT"),e.dna_name.includes("PATTERN")&&(this._shelfType="PATTERN"),this._shelfCollection="Bookcase",this._projectType=X.GRASSHOPPER),"superior_object_type"in e&&"superior_object_ids"in e&&(this._shelfType=e.superior_object_type,this._shelfId||(this._shelfId=parseInt(e.superior_object_ids[0])),this._shelfCollection=e.superior_object_collection,"container"===this._shelfType&&(this._shelfCollection="Bookcase"),this._projectType=X.CAPE,this._shelfLine=this.parseShelfLine(e.superior_object_line)),this._dna=e}setShelfId(e){this._shelfId=e}createPreset(e){if(this.isGrassProject&&this.isBookcaseShefl){const t=new OldConfigurationState(e);return this.addInfoToPreset(t),{type:K.GRASSHOPPER_BOOKCASE,parameters:t,dna:this._dna}}if(this.isCapeProject&&this.isBookcaseShefl){const t=new OldConfigurationState(e);return this.addInfoToPreset(t),{type:K.CAPE_BOOKCASE,parameters:t,dna:this._dna}}if(this.isCapeProject&&this.isLowBoardShelf){const t=new ConfigurationState(e);return this.addInfoToPreset(t),{type:K.CAPE_LOWBOARD,parameters:t,dna:this._dna}}if(this.isCapeProject&&this.isSideBoardShelf){const t=new ConfigurationState(e);return this.addInfoToPreset(t),{type:K.CAPE_SIDEBOARD,parameters:t,dna:this._dna}}if(this.isCapeProject&&this.isWardrobeShelf)return{type:K.CAPE_WARDROBE,parameters:e,dna:this._dna}}loadFromDNA(e){let t=this._dna.serialization[this._shelfType][this._shelfId].presets[e];return t?(this.addInfoToPreset(t),new ConfigurationState(t)):{}}loadFromJetty(e){if(e){if(parseInt(e.configurator_type)===V.ROW)return new OldConfigurationState(e);if(parseInt(e.configurator_type)===V.COLUMN)return e.shelf_type===p.VENEER_T01&&(this._shelfLine=p.VENEER_T01),null!=e.color&&(e.configurator_params.material=e.color),this.addInfoToPreset(e.configurator_params),new ConfigurationState(e.configurator_params);if(parseInt(e.configurator_type)===V.WARDROBE)return this.addInfoToPreset(e.configurator_params),new ConfigurationState(e.configurator_params)}return{}}loadFromGeometry(e,t){const{channel_id:s,channel_configuration:i}=e.components[t],n=new ConfigurationState(e.configurator_params);return n.configurator_custom_params.channels[s]||(n.configurator_custom_params.channels[s]=Object.assign({},i)),n}updateLocalConfig(e,t,s,i){switch(s){case"door_flip":e.configurator_custom_params.channels[t].door_flip=i;break;case"thumbnail":e.configurator_custom_params.channels[t].series_id=i;break;case"cables":e.configurator_custom_params.channels[t].cables=i;break;case"local_edge":e.configurator_custom_params.lines=Object.assign(Object.assign({},e.configurator_custom_params.lines),i)}return e}addInfoToPreset(e){e.geom_id=this._shelfId,e.geom_type=this._shelfType,e.geom_line=this._shelfLine,e.additional_parameters=Object.assign(Object.assign({},e.additional_parameters),{collection_type:this._shelfCollection})}parseShelfLine(e){return"type_01"===e?p.T01:"type_02"===e?p.T02:"veneer_01"===e?p.VENEER_T01:"type_03"===e?p.T03:e}}class PresetUtils{static compareConfigStates(e,t){const s=[],i=e?Object.keys(e):[],n=t?Object.keys(t):[];return i.length!==n.length?{isEqual:!1,diff:new Set(["keys"])}:(Object.keys(e).forEach(i=>{"configurator_custom_params"===i?(PresetUtils.compareChannels(e[i],t[i],s),PresetUtils.compareLines(e[i],t[i],s)):"additional_parameters"!==i&&e[i]!==t[i]&&s.push(i)}),{isEqual:0===s.length,diff:new Set(s)})}static compareChannels(e,t,s){const i=e.channels?Object.keys(e.channels):[],n=t.channels?Object.keys(t.channels):[];return i.length!==n.length?(s.push("channels"),s):(i.forEach(i=>{if(i in t.channels){e.channels[i].cables===t.channels[i].cables||s.push("channel_cables");e.channels[i].door_flip===t.channels[i].door_flip||s.push("channel_doorFlip");e.channels[i].series_id===t.channels[i].series_id||s.push("channel_seriesId")}else s.push("channel_id")}),s)}static compareLines(e,t,s){const i=e.lines?Object.keys(e.lines):[],n=t.lines?Object.keys(t.lines):[];return i.length!==n.length?(s.push("lines"),s):(i.forEach(i=>{if(i in t.lines){e.lines[i]===t.lines[i]||s.push("line_distortion")}else s.push("line_id")}),s)}}const U=function(e,t){let s=Array(10).fill([2,0,0,2,1,1]);if(null==t||null==t.row_heights)return s;let i=[];t.row_heights.reduce((function(e,t,s){return i[s]=e+t}),9),i.unshift(9);let n=i.map(t=>e.doors.filter((e,s,i)=>e.y1===t)),o=i.map(t=>e.drawers.filter((e,s,i)=>e.y1===t)),r=i.map(t=>e.shadows_inner.filter((e,s,i)=>e.y1===t));return i.forEach((e,t)=>{const i=n[t].length,a=o[t].length,l=r[t].length;s[t]=[0===i?2:1,1===l?0:0!==i&&l!==i?2:1,l===i?2:1,0===a?2:1,1===l?0:0!==a&&l!==a?2:1,l===a?2:1]}),s},Y=function(e,t=!0){let s=-e.width/2+i.MAT_THICKNESS/2,n=t&&e.furniture_type===f?i.MAT_THICKNESS/2:0;const o={true:1,false:0,1:1,0:0},r={configurator_params:e.params,components:[],horizontals:[],verticals:[],supports:[],legs:[],long_legs:[],doors:[],drawers:[],plinth:[],boxes:[],buttons:[],inserts:[],cable_management:[],doors_full:[],frame_top:[],frame_side:[],walls:[],backs:[],slabs:[],frame:[],bars:[],drawers:[],doors_exterior:[],hinges:[],masking_bars:[],modules:[],lines:{},row_styles:Array(10).fill(1),rows:Array(10).fill(190),setup_id:null,density_options:[0],height:e.height,width:e.width,depth:e.depth,pattern:e.pattern,material:e.material,shelf_type:e.shelf_type,physical_product_version:e.physical_product_version,furniture_type:e.furniture_type,configurator_type:2,additional_height:[i.MAT_THICKNESS,12]};if(function(e){e&&Object.keys(e).forEach(t=>{let s=[];e[t].forEach(e=>{e.y2-e.y1>1&&s.push(e)}),e[t]=s})}(e.geometry),e.geometry.walls.forEach(e=>{r.walls.push({x1:e.x1+s,x2:e.x2+s,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.slabs_base.forEach(e=>{r.slabs.push({x1:e.x1+s,x2:e.x2+s,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,subtype:"b",c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.slabs_standard.forEach(e=>{r.slabs.push({x1:e.x1+s,x2:e.x2+s,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,subtype:e.subtype||"s",c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.inserts_wardrobe.forEach(e=>{r.inserts.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,subtype:"w",c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.bars.forEach(e=>{r.bars.push(new PositionBoxWithParent({x1:e.x1+s,x2:e.x2+s,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null}))}),e.geometry.hinges.forEach(e=>{r.hinges.push({x1:e.x1+s,x2:e.x2+s,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,direction:e.direction,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.masking_bars.forEach(e=>{r.masking_bars.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.frame_tops&&e.geometry.frame_sides&&(e.geometry.frame_tops.forEach(e=>{r.frame.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,direction:e.direction,subtype:"t",c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.frame_sides.forEach(e=>{r.frame.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,direction:e.direction,subtype:"s",c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})})),e.geometry&&e.geometry.plinth.forEach(e=>e.components.filter(e=>"Pz"===e.type||"Px"===e.type||"Pe"===e.type).forEach(e=>{r.additional_height[1]=Math.max(r.additional_height[1],e.y2-e.y1+12),r.plinth.push({x1:s+e.x1,x2:s+e.x2,y1:n+e.y1,y2:n+e.y2,z1:e.z1,z2:e.z2,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,flip:e.flip,subtype:$(e.type)})})),e.geometry.doors_exterior.forEach(e=>{e.handleConfig.x+=s,e.handleConfig.y+=n,r.doors_exterior.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,subtype:e.subtype,flip:o[e.flip]||0,type:e.handle,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,innerOffset:0,direction:e.direction,main_direction:e.main_direction,offset_cfg:e.offsetConfig||null,handle_cfg:e.handleConfig||null})}),e.components&&(r.lines=b(e,s,n+r.additional_height[1]-12),e.components.forEach((t,o)=>{r.setup_id=t.m_setup_id||r.setup_id,"density_options"in t&&(r.density_options=t.density_options);let a=[];a="mesh"===e.geom_type?e.geometry.opening.filter(e=>e.m_config_id===t.m_config_id):e.geometry.opening,a=a.map(e=>({type:e.type,width:{value:Math.abs(e.x2-e.x1),x:(e.x2+e.x1)/2+s,y:Math.min(e.y2,e.y1)+n,z:Math.max(t.z2,t.z1),openingRange:{start:{x:e.x1+s,y:e.y1+n,z:e.z1},end:{x:e.x2+s,y:e.y2+n,z:e.z2}}},height:{value:Math.abs(e.y2-e.y1),x:Math.min(e.x2,e.x1)+s,y:(e.y2+e.y1)/2+n,z:Math.max(t.z2,t.z1),openingRange:{start:{x:e.x1+s,y:e.y1+n,z:e.z1},end:{x:e.x2+s,y:e.y2+n,z:e.z2}}}}));let l=o>0?{x1:s-i.MAT_THICKNESS,x2:s+t.x1,y1:t.y1+n-i.MAT_THICKNESS-150,y2:t.y2+n+i.MAT_THICKNESS,z1:t.z1+i.MAT_THICKNESS,z2:t.z2-i.MAT_THICKNESS}:null,c=o<e.components.length-1?{x1:s+t.x2,x2:s+Math.max(...e.components.map(e=>e.x2))+i.MAT_THICKNESS,y1:t.y1+n-i.MAT_THICKNESS-150,y2:t.y2+n+i.MAT_THICKNESS,z1:t.z1+i.MAT_THICKNESS,z2:t.z2-i.MAT_THICKNESS}:null,h=Object.keys(r.lines).filter(e=>e.split("_")[1]===""+t.channel_id)[0],_=Object.keys(r.lines).filter(e=>e.split("_")[0]===""+t.channel_id)[0],p={x1:s+t.x1,x2:s+t.x2,y1:t.y1+n,y2:t.y2+n,z1:t.z1,z2:t.z2,hover_left:l,hover_right:c,line_left:h||null,line_right:_||null,m_config_id:t.m_config_id||null,series_id:t.series_id||null,table_id:t.table_id||null,door_flip:t.door_flip||null,door_flippable:t.door_flippable||!1,cables:t.cables,cables_available:t.cables_available,channel_id:t.channel_id,order:t.order||0,component_id:t.id,distortion:t.distortion,bi_info:t.bi_info,compartments:a,local_x:t.local_x,channel_configuration:{door_flip:t.door_flip||null,series_id:t.series_id||null,cables:t.cables,distortion:t.distortion||null}};r.components.push(p);let d=-150+n,u=s+(t.x1+t.x2)/2;r.buttons.push(Object.assign(Object.assign({},p),{x1:u-10,x2:u+10,y2:d-10,y1:d+10,z1:t.z2,z2:t.z2-20,r:10}))})),e.geometry.horizontals.forEach(e=>{let t=Math.abs(e.x1+e.x2)/2,i=Math.abs(e.y1+e.y2)/2,o=Math.abs(e.z1+e.z2)/2;r.horizontals.push({x1:e.x1+s,x2:e.x2+s,y1:i+n,y2:i+n,z1:e.z1,z2:e.z2,centroid:[t,i,o],c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.verticals.forEach(e=>{let t=Math.abs(e.x1+e.x2)/2,i=Math.abs(e.y1+e.y2)/2,o=Math.abs(e.z1+e.z2)/2;r.verticals.push({x1:t+s,x2:t+s,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2,centroid:[t,i,o],c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.supports.forEach(e=>{r.supports.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:0,z2:12,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.backs.forEach(e=>{r.backs.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:0,z2:12,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,cable_visible:e.cable_visible||0,cable_up:!!e.cable_up,cable_down:!!e.cable_down,subtype:e.subtype})}),e.geometry.doors.concat(e.geometry.doors_front,e.geometry.doors_exterior).forEach(e=>{r.doors.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z2-19,z2:e.z2,subtype:e.subtype,flip:o[e.flip]||0,type:e.handle,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,innerOffset:0})}),e.geometry.drawers.forEach(e=>{let t=Math.abs(e.y2-e.y1),i=e.depth?e.depth:Math.abs(e.z2-e.z1);Math.abs(e.x2-e.x1);r.drawers.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z2-19,z2:e.z2,type:e.shelf_type,door_handler_width:130,door_handler_height:20,drawer_cutout:28,bottom_offset:22,bottom_thickness:13,bottom_depth:i-50,back_height:t-92,sides_length:i-50,sides_height:t-72,front_handling_size:20,divisions:e.divisions||null,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,innerOffset:0,exterior:e.exterior,subtype:e.subtype})}),e.geometry.inserts_horizontal.filter(e=>"Ih"===e.type).forEach(t=>{r.inserts.push({x1:s+t.x1,x2:s+t.x2,y1:t.y1+n,y2:t.y2+n,z1:t.z1,z2:t.z2,subtype:"h",temp_type:e.collection_type,c_config_id:t.c_config_id||null,m_config_id:t.m_config_id||null})}),e.geometry.inserts_vertical.filter(e=>"Iv"===e.type).forEach(t=>{r.inserts.push({x1:s+t.x1,x2:s+t.x2,y1:t.y1+n,y2:t.y2+n,z1:t.z1,z2:t.z2,subtype:"v",temp_type:e.collection_type,c_config_id:t.c_config_id||null,m_config_id:t.m_config_id||null})}),e.geometry.legs.forEach(e=>{r.legs.push({x1:s+Math.abs(e.x1+e.x2)/2,x2:s+Math.abs(e.x1+e.x2)/2,y1:e.y1+n,y2:e.y2+n,z1:Math.abs(e.z1+e.z2)/2,z2:Math.abs(e.z1+e.z2)/2,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.long_legs.forEach(e=>{r.long_legs.push({x1:s+Math.abs(e.x1+e.x2)/2,x2:s+Math.abs(e.x1+e.x2)/2,y1:e.y1+n,y2:e.y2+n,z1:Math.abs(e.z1+e.z2)/2,z2:Math.abs(e.z1+e.z2)/2,rotation_z:e.rotation_z,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}),e.geometry.cable_openings.forEach(e=>{if(e.active){let t=Math.abs(e.x1+e.x2)/2,o=Math.abs(e.y1+e.y2)/2,a=Math.abs(e.z1+e.z2)/2;r.cable_management.push({x1:t+s,x2:t+s,y1:o+n+i.MAT_THICKNESS/2,y2:o+n+i.MAT_THICKNESS/2,z1:a,z2:a,radius:e.radius,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})}}),r.additional_elements={styles:U(e.geometry,e.params),shadow_left:[],shadow_middle:[],shadow_right:[],shadow_side:[]},e.geometry.shadows_outer.forEach(e=>{r.additional_elements.shadow_middle.push({x1:s+e.x1,x2:s+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2})}),e.geometry.shadows_left.forEach(e=>{r.additional_elements.shadow_left.push({x1:s+e.x1,x2:s+e.x2+i.SHADOW_THICKNESS,y1:e.y1+n-i.SHADOW_THICKNESS,y2:e.y2+n+i.SHADOW_THICKNESS,z1:e.z1,z2:e.z2})}),e.geometry.shadows_right.forEach(e=>{r.additional_elements.shadow_right.push({x1:s+e.x1-i.SHADOW_THICKNESS,x2:s+e.x2,y1:e.y1+n-i.SHADOW_THICKNESS,y2:e.y2+n+i.SHADOW_THICKNESS,z1:e.z1,z2:e.z2})}),r.cast_shadows={long_legs_shadow:r.long_legs.length?Z(r.long_legs,r.depth):null,plinth_shadow:r.plinth.length?q():null},"mesh"===e.geom_type){S(r);const t=O(e);r.components.forEach(e=>{const s=t[e.m_config_id],i=e.x1+(e.x2-e.x1)/2;e.bounding_box={x1:i-s.maxToLeft,y1:s.pMin[1],z1:s.pMin[2],x2:i+s.maxToRight,y2:s.pMax[1],z2:s.pMax[2]}})}return function(e){e.components.forEach(e=>{let t=[];e.compartments.forEach(e=>{let s=[e.width.value,e.width.x,e.width.y].join(":");t.includes(s)?e.width=null:t.push(s)})})}(r),r},$=e=>"Pz"===e?"z":"Pe"===e?"e":"Px"===e?"x":"P",Z=(e,t)=>{let s;switch(t){case 240:s=1;break;case 320:s=3.04;break;case 400:s=3.84}const i=(e[1].z1-e[0].z1)/2+10,n=e.reduce((e,t,s)=>s%2!=0?[...e,t]:e,[]);let o=[],r=[];return n.forEach((e,t)=>{0!==t&&(o=[...o,{geom:{x:n[t-1].x1+(e.x1-n[t-1].x1)/2,y:1,z:i,scaleX:(Math.abs((e.x1-n[t-1].x1)/10)-28)/10}}],t!==n.length-1&&(r=[...r,{geom:{x:e.x1,y:1,z:i}}]))}),{shadow_leg_L:{geom:{x:e[1].x1,y:1,z:i}},shadow_leg_R:{geom:{x:e[e.length-1].x1,y:1,z:i}},shadow_between_legs:o,shadow_leg_M:r,modifier:s}},q=e=>["elo520Plinth"];function J(e){const t=new Set,i=new Set;Object.values(e).forEach(e=>e.forEach(e=>{s.includes(e.type)&&(t.add(Number(e.x1)),t.add(Number(e.x2)),i.add(Number(e.y1)),i.add(Number(e.y2)))}));const n=(e,t)=>e-t;return[[...t].sort(n),[...i].sort(n)]}function Q(e,t){let s=0;return{linesX:e,linesY:t,points:e.reduce((e,i)=>(e[i]=t.reduce((e,t)=>(e[t]={x:i,y:t,i:s},s++,e),{}),e),{})}}function ee(e,t,s){return e.points[t][s]}function te(e,t,s,i){return e.linesX.filter(e=>e>=t&&e<=s).map(t=>e.points[t][i])}function se(e,t,s,i){return e.linesY.filter(e=>e>=s&&e<=i).map(s=>e.points[t][s])}const ie=e=>"string"==typeof e||e instanceof String,ne=Object.keys(t).reduce((e,s)=>(e[t[s]]=s,e),{}),oe=function(e,t){let s=e.map(e=>Math.floor(e));const i=s.reduce((e,t)=>e+t,0);for(let n=0;n<t-i;n++)s[n%e.length]+=1;return s};function re(e,t=null){return(s,i)=>{const n=Number(s[e])-Number(i[e]);return 0!==n||null===t?n:Number(s[t])-Number(i[t])}}const ae=(e,t)=>e[t+"2"]-e[t+"1"],le=(e,t)=>{const s={isSuccess:!1,idx:1/0,distance:1/0};return t.forEach((t,i)=>{const n=Math.abs(e-t);s.distance>=n&&(s.distance=n,s.idx=i,s.isSuccess=!0)}),s},ce=(e,t,s)=>{const i=[],n=t.length-1;let o=null;return t.forEach((t,r)=>{const a=o||Object.assign({},e),l=Object.assign({},e);a[s+"2"]=t,l[s+"1"]=t,r===n?i.push(a,l):(i.push(a),o=l)}),i};function he(e,t,s,i){const n=se(t,e.x1,e.y1,e.y2);e.x1-=i/2,e.x2+=i/2,e.y1-=i/2,e.y2+=i/2,e.p1=n[0],e.p3=n[n.length-1];const o=[e];for(let t=0;t<n.length;t+=1){const i=n[t],r=void 0!==i.left&&i.left.x2>e.x1?i.left:null,a=void 0!==i.right&&i.right.x1<e.x2?i.right:null,l=r||a,c=o[o.length-1],h=i.bottom&&i.bottom!==c&&s.includes(i.bottom)?i.bottom:null,_=i.bottom&&i.bottom!==c&&!s.includes(i.bottom)?i.bottom:null;if(l&&c.y1<l.y1){const e=Object.assign({},c);h||_?o.splice(o.indexOf(c),1):(c.y2=l.y1,c.p3=i,i.bottom=c),e.y2>l.y2&&(c.p1=i,e.y1=l.y2,o.push(e),i.top=e)}else l&&c.y1>=l.y1?(c.p1=i,c.y1=l.y2,i.top=c):!l&&h?(o.splice(o.indexOf(c),1),o.push(h),h.y2=c.y2,h.p3=c.p3,s.splice(s.indexOf(h),1)):_&&c&&_.y1<=c.y1&&_.y2>=c.y2?o.splice(o.indexOf(c),1):_&&c&&_.y1<=c.y1&&_.y2<=c.y2?(c.y1=_.y2,c.p1=i):_&&c&&_.y1>=c.y1&&_.y2>=c.y2?(c.p3=i,c.y2=_.y1):(i.bottom=c,i.top=c)}return s=s.concat(o)}function _e(e,t,s,i,n){const o=se(t,e.x1,e.y1,e.y2),r=void 0!==o[0].left||void 0!==o[0].right,a=void 0!==o[o.length-1].left||void 0!==o[o.length-1].right;e.y1-=n/2,e.y2+=n/2;let l=[],c=[];return o.reduce((e,t)=>(t.bottom&&!e.includes(t.bottom)&&e.push(t.bottom),t.top&&!e.includes(t.top)&&e.push(t.top),e),[]).forEach(t=>{if(t.y1>=e.y1&&t.y2<=e.y2)l.push(t);else if(t.y1>=e.y1&&t.y2>e.y2)t.y1=e.y2-(a?0:n),t.p1=o[o.length-1];else if(t.y1<e.y1&&t.y2<=e.y2)t.y2=e.y1+(r?0:n),t.p3=o[0];else if(t.y1<e.y1&&t.y2>e.y2){const s=Object.assign({},t);s.y1=e.y2-(a?0:n),s.p1=o[o.length-1],t.y2=e.y1+(r?0:n),t.p3=o[0],c.push(s)}}),c.forEach(e=>{se(t,e.x1+n/2,e.y1+n/2,e.y1-n/2).forEach((t,s)=>{s>0&&(t.bottom=e),s<o.length-1&&(t.top=e)})}),o.forEach((e,t)=>{t>0&&(e.bottom=void 0),t<o.length-1&&(e.top=void 0)}),i.push(Object.assign(Object.assign({},e),{p1:o[0],p3:o[o.length-1]})),[s=s.filter(e=>!l.includes(e)).concat(c),i]}function pe(e,t,s,n,o=!0){let r=[];const a=e.sort(re("x1"));for(let e=0;e<a.length;e+=1)r=ue(a[e],t,r,i.MAT_THICKNESS,o);return s[n]=r,s}function de(t,s,n,o){let r=n[o],a=[];const l=t.sort(re("x1"));for(let e=0;e<l.length;e+=1)[r,a]=fe(l[e],s,r,a,i.MAT_THICKNESS);return n[o]=r,n[e.ERASER_H]=a,n}function ue(e,t,s,i,n){const o=te(t,e.x1,e.x2,e.y1);if(0===o.length)return s;const r=o.map(e=>e.top&&e.bottom?null:e.left||e.right||null).reduce((e,t)=>e||t,null);if(null!=r&&r.x2>e.x2)return s;if(null!=r&&s.includes(r)&&r.x2<e.x2&&n)return r.x2=e.x2+(n?i/2:-i/2),e.opening&&(r.opening=(r.opening||[]).concat(e.opening)),o.forEach((e,t)=>{t>0&&(e.left=r),t<o.length-1&&(e.right=r)}),r.p3=o[o.length-1],s;const a=o[0].top||o[0].bottom,l=o[o.length-1].top||o[o.length-1].bottom;return e.y1-=i/2,e.y2+=i/2,e.x1=a?a.x2:e.x1-(n?i/2:-i/2),e.x2=l?l.x1:e.x2+(n?i/2:-i/2),o.forEach((t,s)=>{s>0&&(t.left=e),s<o.length-1&&(t.right=e)}),e.p1=o[0],e.p3=o[o.length-1],s.push(e),s}function fe(e,t,s,i,n){const o=te(t,e.x1,e.x2,e.y1);e.x1-=n/2,e.x2+=n/2;let r=[],a=[];return o.reduce((e,t)=>(t.left&&!e.includes(t.left)&&e.push(t.left),t.right&&!e.includes(t.right)&&e.push(t.right),e),[]).forEach(t=>{if(t.x1>=e.x1&&t.x2<=e.x2)r.push(t);else if(t.x1>=e.x1&&t.x2>e.x2&&t.x1<e.x2)t.x1=e.x2-n,t.p1=o[o.length-1];else if(t.x1<e.x1&&t.x2<=e.x2&&t.x2>e.x1)t.x2=e.x1+n,t.p3=o[0];else if(t.x1<e.x1&&t.x2>e.x2){const s=Object.assign({},t);s.x1=e.x2-n,s.p1=o[o.length-1],t.x2=e.x1+n,t.p3=o[0],a.push(s)}}),a.forEach(e=>{te(t,e.x1+n/2,e.x2-n/2,e.y1+n/2).forEach((t,s)=>{s>0&&(t.left=e),s<o.length-1&&(t.right=e)})}),o.forEach((e,t)=>{t>0&&(e.left=void 0),t<o.length-1&&(e.right=void 0)}),i.push(Object.assign(Object.assign({},e),{p1:o[0],p3:o[o.length-1]})),[s=s.filter(e=>!r.includes(e)).concat(a),i]}function me(e,t,s,i,n=[],o=null){const r=n.reduce((e,t)=>e.concat(s[t]),[]);let a=[];const l=e[ne[i]].sort(re("x1","y1"));for(let e=0;e<l.length;e+=1)a=xe(l[e],t,a,r,o);return s[i]=a,s}function ge(t){const s=[];return t[e.BACK].forEach(e=>{if(e.split&&(e=>e.y2-e.y1>i.SINGLE_BACK_H)(e)){const t=Object.assign(Object.assign({},e),{x2:e.split}),i=Object.assign(Object.assign({},e),{x1:e.split});s.push(t,i)}else s.push(e)}),t[e.BACK]=s,t}function ye(t,s){return s.forEach(s=>t[s].forEach(t=>{"p1"in t&&!t.exterior&&(!t.p1.right||t.type===e.BACK&&t.p1.right.type===e.INSERT_H||(t.y1=t.p1.right.y2),!t.p1.top||t.type===e.DOOR_F&&t.p1.top.type===e.INSERT_V||t.type===e.BACK&&t.p1.top.type===e.INSERT_V||t.type===e.SUPPORT&&!0===t.flip||(t.x1=t.p1.top.x2)),"p3"in t&&!t.exterior&&(!t.p3.left||t.type===e.BACK&&t.p3.left.type===e.INSERT_H||(t.y2=t.p3.left.y1),!t.p3.bottom||t.type===e.DOOR_F&&t.p3.bottom.type===e.INSERT_V||t.type===e.BACK&&t.p3.bottom.type===e.INSERT_V||t.type===e.SUPPORT&&!1===t.flip||(t.x2=t.p3.bottom.x1))})),t}function xe(e,t,s,i,n=null){let o=function(e,t,s){const i="front"===s?[[ee(t,e.x1,e.y1),ee(t,e.x1,e.y2)],[ee(t,e.x2,e.y1),ee(t,e.x2,e.y2)]]:function(e,t,s,i,n){const o=e.linesY.filter(e=>e>=i&&e<=n);return e.linesX.filter(e=>e>=t&&e<=s).map(t=>o.map(s=>e.points[t][s]))}(t,e.x1,e.x2,e.y1,e.y2),n=i.length,o=0!==n?i[0].length:0,r=[];for(let t=1;t<o;t+=1)for(let o=1;o<n;o+=1){const n=Object.assign({},e);if("back"===s){let s=i[o-1][t-1].backtopright;s&&s.type===e.type||(i[o-1][t-1].backtopright=n,i[o-1][t].backbottomright=n,i[o][t-1].backtopleft=n,i[o][t].backbottomleft=n)}else{let s=i[o-1][t-1].topright;s&&s.type===e.type||(i[o-1][t-1].topright=n,i[o-1][t].bottomright=n,i[o][t-1].topleft=n,i[o][t].bottomleft=n)}n.p1=i[o-1][t-1],n.p2=i[o-1][t],n.p3=i[o][t],n.p4=i[o][t-1],r.push(n)}return r}(e,t,n);return"front"!==n&&(o=be(o,null,"back"===n),o=be(o,null,"back"===n,"horizontal"),o=be(o,null,"back"===n,"vertical")),o=be(o,i.concat(s),"back"===n),o=be(o,i.concat(s),"back"===n,"horizontal"),o=be(o,i.concat(s),"back"===n,"vertical"),s=s.concat(o)}function be(e,t=null,s=!1,i=null){t=t||[];const n=[];for(let o=0;o<e.length;o+=1){const r=e[o],{p1:a}=r;let l=Oe(a,s,i);t.includes(l)||n.includes(l)||(l=null),!!l&&Se(l,r,i)?Ee(l,r,s,i):n.push(r)}return n}function Ee(e,t,s,i){e.x2=t.x2,e.y2=t.y2,"horizontal"===i?(e.p3=t.p3,e.p4=t.p4):"vertical"===i?(e.p2=t.p2,e.p3=t.p3):(e.p1=t.p1,e.p2=t.p2,e.p3=t.p3,e.p4=t.p4),e.type=t.type,s?(t.p1.backtopright=e,t.p2.backbottomright=e,t.p3.backbottomleft=e,t.p4.backtopleft=e):(t.p1.topright=e,t.p2.bottomright=e,t.p3.bottomleft=e,t.p4.topleft=e),"horizontal"===i&&e.drawer_divisions&&t.drawer_divisions&&(e.drawer_divisions=[...e.drawer_divisions,...t.drawer_divisions.filter(t=>!(t in e.drawer_divisions))])}function Oe(e,t,s){switch(s){case"horizontal":return t?e.backtopleft:e.topleft;case"vertical":return t?e.backbottomright:e.bottomright;default:return t?e.backtopright:e.topright}}function Se(t,s,i){if(!t)return!1;const n=t.x1===s.x1&&t.x2===s.x2,o=t.y1===s.y1&&t.y2===s.y2,r=s.p1.right&&s.p1.right.type===e.INSERT_H,a=s.p1.top&&s.p1.top.type===e.INSERT_V,l=s.type===e.BACK&&r,c=s.type===e.BACK&&a,h=!s.p1.right||l,_=!s.p1.top||c,p=s.type===e.DRAWER&&s.exterior&&t.type===e.DRAWER&&t.exterior;s.splitted&&t.splitted;switch(i){case"horizontal":return o&&_&&t.flip===s.flip;case"vertical":return n&&h&&!p;default:return n&&o}}const Te=(e,t)=>{const s=t.x1+(t.x2-t.x1)/2;return e.x1<=s&&e.x2>=s};function Ae(e,t,s,i){const n=se(t,e.x1,e.y1,e.y2);e.x1-=e.flip?e.size+i/2:-i/2,e.x2+=e.flip?-i/2:e.size+i/2,e.p1=n[0],e.p3=n[n.length-1];const o=[e];for(let t=0;t<n.length;t+=1){const i=n[t],r=void 0!==i.left&&i.left.x2>e.x1?i.left:null,a=void 0!==i.right&&i.right.x1<e.x2?i.right:null,l=e.flip?r:a,c=o[o.length-1];let h=null,_=null;if(e.flip?(h=i.backbottomleft&&i.backbottomleft!==c&&s.includes(i.backbottomleft)?i.backbottomleft:null,_=i.backbottomleft&&i.backbottomleft!==c&&!s.includes(i.backbottomleft)?i.backbottomleft:null):(h=i.backbottomright&&i.backbottomright!==c&&s.includes(i.backbottomright)?i.backbottomright:null,_=i.backbottomright&&i.backbottomright!==c&&!s.includes(i.backbottomright)?i.backbottomright:null),l&&c.y1<l.y1){const t=Object.assign({},c);h||_?o.splice(o.indexOf(c),1):(c.y2=l.y1,c.p3=i,e.flip?i.backbottomleft=c:i.backbottomright=c),t.y2>l.y2&&(c.p1=i,t.y1=l.y2,o.push(t),e.flip?i.backtopleft=c:i.backtopright=c)}else l&&c.y1>=l.y1?(c.p1=i,c.y1=l.y2,e.flip?i.backtopleft=c:i.backtopright=c):!l&&h?(o.splice(o.indexOf(c),1),o.push(h),h.y2=c.y2,h.p3=c.p3,s.splice(s.indexOf(h),1)):_?o.splice(o.indexOf(c),1):e.flip?(i.backbottomleft=c,i.backtopleft=c):(i.backbottomright=c,i.backtopright=c)}return s=s.concat(o)}function Ie(t,s){return t[s].forEach(t=>{const i=!t.p1.top||s===e.DOOR_F&&t.p1.top.type===e.INSERT_V?t.p1.topleft:null,n=!t.p3.bottom||s===e.DOOR_F&&t.p3.bottom.type===e.INSERT_V?t.p3.bottomright:null;t.door_parity="single",i&&i.type===s&&(1===i.flip&&1!==t.flip?t.door_parity="double":1!==i.flip&&1===t.flip&&(t.door_parity="wrong")),n&&n.type===s&&(1!==n.flip&&1===t.flip?t.door_parity="double":1===n.flip&&1!==t.flip&&(t.door_parity="wrong")),"single"===t.door_parity&&"custom_flip"in t&&(t.flip=t.custom_flip),i&&i.type===s&&"wrong"===i.door_parity&&"wrong"===t.door_parity&&(i.flip=1,i.door_parity="double",i.handle=1,t.flip=0,t.door_parity="double"),t.handle=1===t.flip&&"double"===t.door_parity?1:0,delete t.custom_flip}),t}function Re(t,s,i=!1){return t.forEach(t=>{let n=[];t.bi_info={},i&&e.DOOR_EXT in s?n=[...s[e.DOOR_EXT]]:e.DOOR in s&&(n=[...s[e.DOOR]]),t.bi_info.doors=null,t.bi_info.doubleDoors=!1,t.door_flippable=n.filter(e=>e.m_config_id==t.m_config_id).reduce((e,s)=>(t.bi_info.doubleDoors="single"!==s.door_parity,"single"===s.door_parity&&(t.door_flip=["right","left"][s.flip||0],t.bi_info.doors=t.door_flip),e||"single"===s.door_parity),!1),i||(t.cables_available=s[e.CABLE_OPENING].some(e=>e.m_config_id==t.m_config_id),t.cables_available&&(t.cables=s[e.CABLE_OPENING].find(e=>e.m_config_id==t.m_config_id).active),t.bi_info.cables=!!t.cables)}),t}function Ne(e){return Object.values(e).forEach(e=>e.forEach(e=>{"p1"in e&&(e.p1={i:e.p1.i}),"p2"in e&&(e.p2={i:e.p2.i}),"p3"in e&&(e.p3={i:e.p3.i}),"p4"in e&&(e.p4={i:e.p4.i})})),e}const ze=t=>(Object.keys(t).forEach(s=>{if(s===e.INSERT_H||s===e.INSERT_V||s===e.INSERT_W||s===e.SLAB_BASE||s===e.SLAB_STD){t[s].forEach(s=>{const i=t[e.BACK].find(e=>e.m_config_id===s.m_config_id);i&&(s.z1=i.z2)})}}),t);function we(s,n=!1){const{geometry:o,width:r,height:a,depth:c}=s,[h,_]=J(o),p=Q(h,_);let d={};if(d=function(t,s,n,o=!1){let r=[];const a=t.sort(re("x1"));for(let e=0;e<a.length;e+=1)r=ue(a[e],s,r,i.MAT_THICKNESS,!o);return n[e.HORIZONTAL]=r,n}(o.horizontals,p,d),d=function(t,s,n){let o=n[e.HORIZONTAL],r=[];const a=t.sort(re("x1"));for(let e=0;e<a.length;e+=1)[o,r]=fe(a[e],s,o,r,i.MAT_THICKNESS);return n[e.HORIZONTAL]=o,n[e.ERASER_H]=r,n}(o.erasers_horizontal,p,d),d=function(t,s,n){let o=[];const r=t.sort(re("y1"));for(let e=0;e<r.length;e+=1)o=he(r[e],s,o,i.MAT_THICKNESS);return n[e.VERTICAL]=o,n}(o.verticals,p,d),d=function(t,s,n){let o=n[e.VERTICAL],r=[];const a=t.sort(re("y1"));for(let e=0;e<a.length;e+=1)[o,r]=_e(a[e],s,o,r,i.MAT_THICKNESS);return n[e.VERTICAL]=o,n[e.ERASER_V]=r,n}(o.erasers_vertical,p,d),d=function(t,s,n){let o=[];const r=t.sort(re("x1"));for(let e=0;e<r.length;e+=1)o=ue(r[e],s,o,i.INSERT_THICKNESS,!1);return n[e.INSERT_H]=o,n}(o.inserts_horizontal,p,d),d=function(t,s,n){let o=[];const r=t.sort(re("y1"));for(let e=0;e<r.length;e+=1)o=he(r[e],s,o,i.INSERT_THICKNESS);return n[e.INSERT_V]=o,n}(o.inserts_vertical,p,d),d=me(o,p,d,e.OPENING),d=me(o,p,d,e.FRONT,[],"front"),d=me(o,p,d,e.DOOR,[e.OPENING]),d=me(o,p,d,e.DRAWER,[e.OPENING,e.DOOR]),d=me(o,p,d,e.DOOR_F,[e.OPENING],"front"),d=me(o,p,d,e.SHADOW_OUTER,[],"front"),d=me(o,p,d,e.SHADOW_INNER,[],"front"),d=me(o,p,d,e.SHADOW_LEFT,[],"front"),d=me(o,p,d,e.SHADOW_RIGHT,[],"front"),d=me(o,p,d,e.BACK,[],"back"),d=function(t,s,n){let o=[];const r=t.sort(re("x1","y1"));for(let e=0;e<r.length;e+=1)o=Ae(r[e],s,o,i.MAT_THICKNESS);return n[e.SUPPORT]=o,n}(o.supports,p,d),d=function(t,s){return t.forEach(t=>{s[e.INSERT_V].forEach(e=>{if(e.m_config_id===t.m_config_id&&Te(t,e)){const s=e.x1+(e.x2-e.x1)/2;t.x1=s-i.MAT_THICKNESS/2-110,t.x2=s-i.MAT_THICKNESS/2-50}})}),s[e.CABLE_OPENING]=[...t],s}(o.cable_openings,d),d[e.PLINTH]=o.plinth,d[e.LEG]=o.legs,d[e.LONG_LEG]=function(t,s){if(s.geometry.long_legs&&s.geometry.long_legs.length){const n=(s.configurator_params.plinth?100:0)+i.MAT_THICKNESS,o=t[e.VERTICAL].filter(e=>e.y1===n),r=o.map(e=>e.x1),a=[],l=[];s.geometry.long_legs.forEach(e=>{if(r.includes(e.x1))a.push(e);else{let t=1/0,s=[0,0];o.forEach(i=>{const n=Math.abs(i.x1-e.x1);n<t&&(t=n,s=[i.x1,i.x2])}),l.push({leg:e,position:s})}});const c=a.map(e=>e.x1);return l.forEach(e=>{if(!c.includes(e.position[0])){const t=Object.assign({},e.leg);t.x1=e.position[0],t.x2=e.position[1],a.push(t)}}),a.sort((e,t)=>e.x1-t.x1),a}return[]}(d,s),d[e.MARK]=o.mark,"mesh"===s.geom_type){const t=s.configurator_params.additional_parameters.collection_type;d=function(t,s){const i=[l],n=[e.BACK,e.DOOR,e.DOOR_F,e.DRAWER];return i.includes(s)&&n.forEach(e=>{let s=[];t[e].forEach(e=>{e.y2-e.y1>400&&s.push(e)}),t[e]=t[e].filter(e=>!s.includes(e))}),t}(d,t)}d=function(t){let s=[],n=[],o=[];return t[e.SHADOW_OUTER].forEach(e=>{e.p1.top&&e.p3.bottom?s.push(e):e.p1.top?(e.x2+=i.MAT_THICKNESS/2,o.push(e)):e.p3.bottom&&(e.x1-=i.MAT_THICKNESS/2,n.push(e))}),t[e.SHADOW_OUTER]=s,t[e.SHADOW_RIGHT]=o,t[e.SHADOW_LEFT]=n,t}(d),d=Ie(d,e.DOOR),d=Ie(d,e.DOOR_F),d=ye(d,[e.OPENING,e.FRONT,e.DOOR,e.DRAWER,e.DOOR_F,e.BACK,e.SUPPORT,e.SHADOW_OUTER,e.SHADOW_INNER,e.SHADOW_LEFT,e.SHADOW_RIGHT]),d=ge(d),d=function(e,t){return t.forEach(t=>e[t].forEach(e=>{e.flip&&([e.p1,e.p3]=[e.p3,e.p1])})),e}(d,[e.DOOR,e.DOOR_F,e.SUPPORT]),d=function(e,t){return Object.entries(t).forEach(([t,s])=>{e[t].forEach(t=>{t.type=s,e[s].push(t)}),e[t]=[]}),e}(d,{[e.DOOR_F]:e.DOOR,[e.FRONT]:e.BACK}),d=ze(d),d=Ne(d),d=function(t){if(0===t[e.HORIZONTAL].length||0===t[e.VERTICAL].length||t[e.LEG].length>0||t[e.LONG_LEG].length>0)return t;const s=t[e.HORIZONTAL].map(e=>e.y2).sort((e,t)=>e-t)[0],i=t[e.HORIZONTAL].filter(e=>e.y2===s),n=t[e.VERTICAL].filter(e=>e.y1===s);let o=[];i.forEach(e=>{const{x1:t,x2:s}=e;let i=n.map(e=>(e.x1+e.x2)/2).sort((e,t)=>e-t).filter(e=>e>=t+25&&e<=s-25);i=i.length>0?i:[t+25],i.forEach((e,n)=>{if(0===n&&e-t-25>130&&o.push(t+25),0===o.length)o.push(e);else{let t=e-o[o.length-1];t>600&&o.push(parseInt(Math.floor(e-t/2))),t>130&&o.push(e)}if(n===i.length-1){let t=s-25-o[o.length-1];t>600&&o.push(parseInt(Math.floor(e+t/2))),t>130&&o.push(s-25)}})});let r=[];return o.forEach(t=>{[i[0].z1+25,i[0].z2-25].forEach(s=>{r.push({x1:t-10,x2:t+10,y1:i[0].y1-20,y2:i[0].y1,z1:s-10,z2:s+10,type:e.LEG})})}),t[e.LEG]=r,t}(d);const u={width:r,height:a,depth:c,components:Re(o.components,d),linesX:h,linesY:_,geometry:Object.entries(t).reduce((e,[t,s])=>(e[t]=d[s]||[],e),{})};return u.density_mode=s.density_mode,u.distortion_mode=s.distortion_mode,u.pattern=s.pattern,u.geom_type=s.geom_type,u.params=s.configurator_params,u.shelf_type=s.shelf_type,u.physical_product_version=s.physical_product_version,u.furniture_type=s.furniture_type,u.material=s.material,u.collection_type=n?"Wardrobe":"Sideboard",u}function Me(t,s){s[e.DOOR_EXT]=[];const n=t.geometry.erasers_door?[...t.geometry.erasers_door]:[],o=[],r=2===t.geometry.doors_exterior.length,a=t.geometry.doors_exterior.length-1;return t.geometry.doors_exterior.forEach((e,s)=>{let n=Object.assign({},e);n.y1=0,0===s&&(n.x1=n.x1-i.MAT_THICKNESS/2),s===a&&(n.x2=n.x2+i.MAT_THICKNESS/2),n.y2=n.y2+i.MAT_THICKNESS/2,n.subtype="full",n.direction=t.configurator_params.additional_parameters.doors_direction,n.main_direction=t.configurator_params.additional_parameters.doors_direction,n.offsetConfig={top:2,left:2,right:2,bottom:0};let l=ae(e,"x"),c=ae(e,"y");const h=e.splitCfg.starts.x,_=e.splitCfg.values.x,p=function(e=[]){let t=[];e.length&&0!==e[0]&&e.splice(0,0,0);return e.forEach(e=>{0===e&&t.push([]);const s=t.length-1;t[s].push(e)}),t.forEach(e=>e.reverse()),t}(e.splitCfg.values.y),d=e.splitCfg.starts?[...e.splitCfg.starts.y].reverse():[0];if(r){const e=t.geometry.doors_exterior.every(e=>ae(e,"x")<=e.splitCfg.starts.x);0===s&&e&&(n.main_direction="left",n.direction="left")}if(l<=h){n.door_parity="single";let e=n;p.length?p[0].forEach((t,s)=>{const i=Math.min(s,d.length-1);if(c>=d[i]){const i=Pe(e,t);i.subtype="t",s===d.length-1&&(i.subtype="e"),o.push(i)}}):o.push(n)}else{let e=Object.assign({},n);e.door_parity="double",e.direction="left",e.x2=e.x1+_;let t=Object.assign({},n);t.door_parity="double",t.direction="right",t.x1=e.x2;let s=e,i=t;if(p.length){p[0].forEach((e,t)=>{const i=Math.min(t,d.length-1);if(c>=d[i]){const i=Pe(s,e);i.subtype="t",t===d.length-1&&(i.subtype="e"),o.push(i)}});p[Math.min(1,p.length-1)].forEach((e,t)=>{const s=Math.min(t,d.length-1);if(c>=d[s]){const s=Pe(i,e);s.subtype="t",t===d.length-1&&(s.subtype="e"),o.push(s)}})}else o.push(e,t)}}),o.forEach(t=>{let i=function(e,t=[]){const s=t.filter(t=>t.x1>=e.x1&&t.x1<e.x2||t.x2<=e.x2&&t.x2>e.x1);if(s.length){const t=[];return s.forEach(s=>{if(s.y1>=e.y1&&s.y2<=e.y2){let i=Object.assign({},e);i.y1=s.y2,t.push(i);let n=Object.assign({},e);n.y2=s.y1,t.push(n)}else if(s.y2>e.y2&&s.y1<e.y2){let i=Object.assign({},e);i.y2=s.y1,t.push(i)}else if(s.y1<e.y1&&s.y2>e.y1){let i=Object.assign({},e);i.y1=s.y2,t.push(i)}else t.push(e)}),t}return[e]}(t,n);i.forEach(He),s[e.DOOR_EXT].push(...i)}),s}function Pe(e,t){const s=Object.assign({},e);let n=0;return t>0&&(n=t+i.MAT_THICKNESS/2),s.y1=n,e.y2=n,s.offsetConfig=Object.assign({},e.offsetConfig),s.offsetConfig.bottom=t>0?2:0,s}function He(e){const t=i.WARDROBE_HANDLE_OFFSET+i.EXTERIOR_DOOR_OFFSET;var s;e.handleConfig={available:(s=e.y1,s<i.WARDROBE_HANDLE_POS-i.WARDROBE_HANDLE_H/2),x:(e=>"right"===e.main_direction&&"right"===e.direction?e.x1+t:"left"===e.main_direction&&"left"===e.direction?e.x2-t:void 0)(e),y:i.WARDROBE_HANDLE_POS,z:e.z1+i.EXTERIOR_DOOR_THICKNESS+i.WARDROBE_HANDLE_D,width:i.WARDROBE_HANDLE_W,height:i.WARDROBE_HANDLE_H,depth:i.WARDROBE_HANDLE_D},e.main_direction!==e.direction&&(e.handleConfig.available=!1)}function De(t){const s=e=>{e.y1=Math.max(e.y1,0)+i.PLINTH_SPACE,e.y2=e.y2+i.PLINTH_SPACE-Math.min(e.y1,0)},n=e=>{e.y2=e.y2+i.PLINTH_SPACE};t[e.FRAME_SIDE].forEach(n),t[e.WALL].forEach(n),t[e.BACK].forEach(n),t[e.DOOR_EXT].forEach(e=>{var t;0===e.y1?((t=e).y1=t.y1+i.M_BAR_HEIGHT,t.y2=t.y2+i.PLINTH_SPACE):s(e)}),t[e.FRAME_TOP].forEach(s),t[e.INSERT_W].forEach(s),t[e.SLAB_BASE].forEach(s),t[e.SLAB_STD].forEach(s),t[e.BAR].forEach(s),t[e.DRAWER].forEach(s);const o=Math.min(...t[e.SLAB_BASE].map(e=>e.y1)),r=[];return t[e.SLAB_BASE].forEach(e=>{e.y1===o&&r.push(e)}),t[e.SLAB_BASE]=r,t}function ve(e,t){const s=[],i=[];return e[t].forEach(e=>{const t=(e=>`${e.x1}|${e.x2}|${e.y1}|${e.y2}`)(e);s.includes(t)||(s.push(t),i.push(e))}),e[t]=[...i],e}function Ce(t){const s=e=>e>i.WARDROBE_ELEMENT_MAX_WIDTH,n=(e,t)=>e+(t-e)/2,o=t[e.WALL].map(e=>n(e.x1,e.x2)),r=t[e.FRAME_TOP],a=[];return r.filter(e=>s(e.x2-e.x1)).forEach(e=>{const t=o.filter((e,t)=>0!==t&&t!==o.length-1),i={left:e.x1,right:e.x2},r=n(e.x1,e.x2),l=((e,t)=>{const s=t.map(t=>({position:t,distances:{toLeftEdge:t-e.left,toRightEdge:e.right-t}}));return s.sort((e,t)=>e.position-t.position),s})(i,t),c=l.filter(e=>{return t=e.distances,!s(t.toLeftEdge)&&!s(t.toRightEdge);var t});if(c.length){const t=le(r,c.map(e=>e.position));if(t.isSuccess){const s=[c[t.idx].position];a.push(...ce(e,s,"x"))}}else{const t=l.filter(e=>!s(e.distances.toLeftEdge)),i=l.filter(e=>!s(e.distances.toRightEdge)),n=t[t.length-1],o=i[0],r=[n.position,o.position];a.push(...ce(e,r,"x"))}}),a.length&&(t[e.FRAME_TOP]=a),t}function Le(s){const{geometry:n,width:o,height:r,depth:a}=s,[l,c]=J(n),h=Q(l,c);let _={};_=pe(n.slabs_base,h,_,e.SLAB_BASE,!1),_=de(n.erasers_horizontal,h,_,e.SLAB_BASE),_=pe(n.slabs_standard,h,_,e.SLAB_STD,!1),_=de(n.erasers_horizontal,h,_,e.SLAB_STD),_=function(e,t,s,n){let o=[];const r=e.sort(re("y1"));for(let e=0;e<r.length;e+=1)o=he(r[e],t,o,i.MAT_THICKNESS);return s[n]=o,s}(n.walls,h,_,e.WALL),_=function(t,s,n,o){let r=n[o],a=[];const l=t.sort(re("y1"));for(let e=0;e<l.length;e+=1)[r,a]=_e(l[e],s,r,a,i.MAT_THICKNESS);return n[o]=r,n[e.ERASER_V]=a,n}(n.erasers_vertical,h,_,e.WALL),_=pe(n.inserts_wardrobe,h,_,e.INSERT_W,!1),_=me(n,h,_,e.OPENING),_=me(n,h,_,e.DRAWER),_=me(n,h,_,e.BACK,[],"back"),_=me(n,h,_,e.SHADOW_OUTER,[],"front"),_=me(n,h,_,e.SHADOW_INNER,[],"front"),_=me(n,h,_,e.SHADOW_LEFT,[],"front"),_=me(n,h,_,e.SHADOW_RIGHT,[],"front"),_=function(t,s){return t.forEach(e=>{e.x1=e.x1+i.MAT_THICKNESS/2,e.x2=e.x2-i.MAT_THICKNESS/2}),s[e.BAR]=[...t],s}(n.bars,_),_=Me(s,_),_=function(t){const s=Math.max(...t[e.SLAB_STD].map(e=>e.y2)),i=[...new Set(t[e.DRAWER].map(e=>e.m_config_id))];return t[e.SLAB_STD].forEach(e=>{s===e.y2&&(e.subtype="t")}),i.forEach(s=>{const i=[...new Set(t[e.DRAWER].filter(e=>e.m_config_id===s).map(e=>e.originalBox.bottom))];t[e.SLAB_STD].filter(e=>e.m_config_id===s).forEach(e=>{i.forEach(t=>{Math.abs(e.y2-t)<100&&(e.subtype="d")})})}),t}(_),"mesh"===s.geom_type&&(_=function(t,s){const{width:n,depth:o}=t,r=Math.max(...t.geometry.walls.map(e=>e.y2));Math.max(...t.geometry.walls.map(e=>e.z2));let a={c_config_id:null,c_subconfig_id:null,m_config_id:null,p1:{},p3:{},type:e.FRAME_SIDE,direction:"y",x1:0-i.MAT_THICKNESS/2-i.FRAME_THICKNESS,x2:0-i.MAT_THICKNESS/2,y1:0,y2:r+i.FRAME_THICKNESS,z1:0,z2:o},l=Object.assign({},a);l.x1=n-i.WARDROBE_AXIS_OFFSET+i.MAT_THICKNESS/2,l.x2=n-i.WARDROBE_AXIS_OFFSET+i.MAT_THICKNESS/2+i.FRAME_THICKNESS;let c=Object.assign({},l);return c.x1=a.x2,c.x2=l.x1,c.y1=l.y2-i.FRAME_THICKNESS,c.type=e.FRAME_TOP,c.direction="x",s[e.FRAME_SIDE]=[a,l],s[e.FRAME_TOP]=[c],s}(s,_),_=De(_),_=Ce(_),_=function(t){const s=[];return t[e.FRAME_TOP].forEach(e=>{const t=Object.assign({},e);t.z1=t.z2-i.FRAME_THICKNESS,t.y1=0,t.y2=i.M_BAR_HEIGHT,s.push(t)}),t[e.MASKING_BAR]=[...s],t}(_)),_=function(t){const s=[];return(t[e.DOOR_EXT].length?t[e.DOOR_EXT]:[]).forEach(e=>{let t=e.y2-e.y1,n=e.x1+i.MAT_THICKNESS/2;"right"===e.direction&&(n=e.x2-i.MAT_THICKNESS/2-i.HINGE_WIDTH);const o={x1:n,x2:n+i.HINGE_WIDTH,z1:e.z1-2*i.HINGE_HEIGHT,z2:e.z1,direction:e.direction,c_config_id:e.c_config_id,m_config_id:e.m_config_id},r=Object.assign({y1:e.y2-i.HINGE_OFFSET.TOP-i.HINGE_HEIGHT,y2:e.y2-i.HINGE_OFFSET.TOP},o),a=Object.assign({y1:e.y1+i.HINGE_OFFSET.BOTTOM,y2:e.y1+i.HINGE_OFFSET.BOTTOM+i.HINGE_HEIGHT},o),l=Object.assign({y1:e.y1+i.HINGE_OFFSET.MID,y2:e.y1+i.HINGE_OFFSET.MID+i.HINGE_HEIGHT},o);t>1.5*i.HINGE_OFFSET.MID?s.push(r,l,a):t>i.HINGE_OFFSET.BOTTOM+i.HINGE_OFFSET.TOP+2*i.HINGE_HEIGHT&&s.push(r,a)}),t[e.HINGE]=[...s],t}(_),_=function(t,s){const n={};t.geometry.backs.forEach(e=>{e.m_config_id in n?n[e.m_config_id].push(e):n[e.m_config_id]=[e]});const o=[],r=[e.SLAB_BASE,e.SLAB_STD].map(t=>{const i=Math.min(...s[t].map(e=>e.y1)),n=Math.max(...s[t].map(e=>e.y2));return t===e.SLAB_BASE?i:t===e.SLAB_STD?n:void 0});return Object.keys(n).forEach(e=>{let t=Object.assign({},n[e][0]);t.y1=r[0],t.y2=r[1],t.x1=t.x1+i.MAT_THICKNESS/2,t.x2=t.x2-i.MAT_THICKNESS/2,o.push(t)}),s[e.BACK]=[...o],s}(s,_),_=ze(_),_=ye(_,[e.OPENING,e.DRAWER,e.SHADOW_OUTER,e.SHADOW_INNER,e.SHADOW_LEFT,e.SHADOW_RIGHT]),_=function(t){const s=[...new Set(t[e.SLAB_STD].map(e=>e.y2))],n=i.WARDROBE_WALL_MAX_HEIGHT,o=le(n,s),r=i.WARDROBE_WALL_MAX_HEIGHT+i.WARDROBE_RAISER_MAX_HEIGHT,a=le(r,s),l=[];return t[e.WALL].forEach(e=>{const t=e.y2-e.y1;if(o.isSuccess&&t>n){const i=[s[o.idx]];a.isSuccess&&t>r&&i.push(s[a.idx]),l.push(...ce(e,i,"y"))}}),l.length&&(t[e.WALL]=l),t}(_),_=function(t){const s=[...new Set(t[e.SLAB_STD].map(e=>e.y2))],n=i.WARDROBE_WALL_MAX_HEIGHT,o=le(n,s),r=[];return t[e.BACK].forEach(e=>{e.y1=0;const t=e.y2-e.y1;if(t>i.WARDROBE_BOTTOM_BACK_MAX_HEIGHT){const a=[i.WARDROBE_BOTTOM_BACK_MAX_HEIGHT];o.isSuccess&&t>n&&a.push(s[o.idx]);const l=ce(e,a,"y");l.forEach(e=>{0===e.y1&&(e.subtype="b"),e.y1===i.WARDROBE_BOTTOM_BACK_MAX_HEIGHT&&(e.subtype="m"),e.y1>i.WARDROBE_BOTTOM_BACK_MAX_HEIGHT&&(e.subtype="t")}),r.push(...l)}}),r.length&&(t[e.BACK]=r),o.isSuccess&&t[e.SLAB_STD].forEach(i=>{i.y2===s[o.idx]&&(i.z1=t[e.BACK][0].z1,t[e.BACK].forEach(e=>{"m"===e.subtype&&e.m_config_id===i.m_config_id&&(e.y2=i.y1)}))}),t}(_),_=Ne(_),_=ve(_,e.SLAB_BASE),_=ve(_,e.SLAB_STD);const d={width:o,height:r,depth:a,components:Re(n.components,_,!0),linesX:l,linesY:c,geometry:Object.entries(t).reduce((e,[t,s])=>(e[t]=_[s]||[],e),{})};return d.density_mode=s.density_mode,d.distortion_mode=s.distortion_mode,d.geom_type=s.geom_type,d.params=s.configurator_params,d.shelf_type=p.T03,d.physical_product_version=s.physical_product_version,d.furniture_type=s.furniture_type,d.material=s.material,d.collection_type="Wardrobe",d}const Be=function(e,t,s={},i=0,n=0,o=!1,r=null,a=null){const l=function(e){if(void 0===e)return[e];switch(e.constructor){case Array:return e;case Object:throw new Error("Obiekt w parserze!");default:return[e]}};let c;if(void 0===e||!e||!(e in t)||void 0===t[e]||null!==a&&t[e]===a)return c=l(je(o,i,n,s)),c;c=t[e],!0===c&&null!==r&&(c=r),("string"==typeof c||c instanceof String)&&(c=c.replace(/ /g,""));let h=je(c,i,n,s);return h=l(h),h},je=function(t,s=0,i=0,r=null){const a=["","middle","single","double","triple","quadruple","quint","sex","sept","gradient",e.BACK,e.FRONT,e.DOOR,e.DOOR_F,e.HORIZONTAL,e.INSERT_H,e.INSERT_V,e.MARK,e.OPENING,e.PLINTH,e.SUPPORT,e.DRAWER,e.VERTICAL,e.SPACER,e.COMPONENT,e.TOP_BOX,e.FRONT_BOX];if(null==t||"boolean"==typeof t||a.indexOf(t)>-1)return t;if(r=Object.assign(Object.assign({},n),r),t.constructor===Array)switch(function(e){const t=o.filter(t=>e.some(e=>ie(e)&&e.includes(t)&&!e.startsWith("#"))).length,s=e.filter(e=>ie(e)&&(e.startsWith("max")||e.startsWith("*"))).length;return 0===t&&0===s?"fixed":1===t&&0===s?"ratio":0===t&&1===s?"flexible":"other"}(t)){case"fixed":return t.map(e=>je(e,s,i,r));case"ratio":return function(e,t,s,i){const n=o.find(t=>e.some(e=>ie(e)&&!e.startsWith("#")&&e.includes(t))),r=e=>ie(e)&&!e.startsWith("#")&&e.includes(n),a=e.filter(e=>!r(e)).map(e=>je(e,0,s,i));let l=e.filter(e=>r(e)).map(e=>Number(e.replace(n,""))||1);const c=(s-a.reduce((e,t)=>e+t,0))/l.reduce((e,t)=>e+t,0);return l=l.map(e=>e*c),l=oe(l,s-a.reduce((e,t)=>e+t,0)),(e=e.slice().reverse()).map(e=>r(e)?t+l.pop():t+a.pop()).reverse()}(t,s,i,r);case"flexible":return function(e,t,s,i){const n=["*","min","max"],o=e=>ie(e)&&!e.startsWith("#")&&n.some(t=>e.includes(t)),r=e.filter(e=>!o(e)).map(e=>je(e,0,s,i)[0]).reduce((e,t)=>e+t,0);return e.map(e=>o(e)?je(e,t,s-r,i)[0]:je(e,0,s,i)[0])}(t,s,i,r);default:throw new Error("Cannot parse list: "+t)}if("string"==typeof t||t instanceof String){if((t=t.toLowerCase().replace(/ /g,"").replace(/--/g,"")).includes(","))return t.split(",").map(e=>je(e,s,i,r));if(/^\d+\.?\d+$/.test(t))return s+Math.round(t);if(/^-\d+\.?\d+$/.test(t))return s+i+Math.round(t);if(t.startsWith("#"))return je(r[t],s,i,r);if(t.startsWith("-#"))return je("-"+r[t.substr(1)],s,i,r);if("x"===t)t=i;else{if("t"===t)return!0;if("f"===t)return!1;"r"===t||"top"===t?t=i:"l"===t||"bottom"===t?t=s:"m"===t?t=i/2:t.endsWith("x")?t=parseInt(t.replace(/x/g,""),10):t.endsWith("mm")?t=parseInt(t.replace(/mm/g,""),10):t.endsWith("cm")?t=10*parseInt(t.replace(/cm/g,""),10):t.endsWith("dm")?t=100*parseInt(t.replace(/dm/g,""),10):t.endsWith("%")?t=.01*parseFloat(t.replace(/%/g,""))*i:t.includes("/")&&(t=parseInt(t.split("/")[0],10)*i/parseInt(t.split("/")[1],10))}isNaN(t)||(t=Number(t))}if("number"==typeof t&&Number.isFinite(t)&&t>=0)return s+Math.round(t);if("number"==typeof t&&Number.isFinite(t)&&t<0)return s+i+Math.round(t);if("number"==typeof t)return t;throw new Error("Cannot parse value -> "+t)},ke=function(t,s,n,o,r,a,l,c,h,_,p,d,u,f){const m=[],{x1:y,x2:x,y1:b,y2:E,z1:O,z2:S}=g(t,s,n,o,r,a);if(_>=0&&m.push(Object.assign(Object.assign({x1:y,x2:x,y1:b,z1:O,z2:S-(2===_?f:0)},u),{y2:b,type:[e.ERASER_H,e.HORIZONTAL,e.INSERT_H,e.SLAB_BASE,e.SLAB_STD][_]})),l>=0&&m.push(Object.assign(Object.assign({x1:y,y1:b,y2:E,z1:O,z2:S-(2===l?f:0)},u),{x2:y,type:[e.ERASER_V,e.VERTICAL,e.INSERT_V,e.WALL][l]})),h>=0&&m.push(Object.assign(Object.assign({x2:x,y1:b,y2:E,z1:O,z2:S-(2===h?f:0)},u),{x1:x,type:[e.ERASER_V,e.VERTICAL,e.INSERT_V,e.WALL][h]})),c>=0){const t=3===c?[[y+i.MAT_THICKNESS/2,x-i.MAT_THICKNESS/2]]:[];m.push(Object.assign(Object.assign({x1:y,x2:x,y2:E,z1:O,z2:S-(2===c?f:0)},u),{y1:E,type:[e.ERASER_H,e.HORIZONTAL,e.INSERT_H,e.HORIZONTAL,e.SLAB_STD,e.SLAB_STD][c],opening:t}))}return m.push(Object.assign(Object.assign({x1:y,x2:x,y1:b,y2:E,z1:O,z2:S},u),{type:e.OPENING})),m.push(Object.assign(Object.assign({x1:y,x2:x,y1:b,y2:E,z1:O,z2:S},u),{type:e.SHADOW_OUTER})),m},We=function(t,s,n,o,r,a,l,c,h,_,d,u,f,m,y,x,b,E,O,S){let T=30;const A="Wardrobe"===S.collection_type;T=A?i.INSERT_Z_WARDROBE_OFFSET:i.INSERT_Z_SIDEBOARD_OFFSET;let[I]=Be("insert__offset",t,s,0,0,T),R=Be("type",t,s,0,0,e.OPENING)[0];const N=!!s&&"#back_panels"in s,z=N&&s["#back_panels"],w=N&&!s["#back_panels"];R===e.OPENING&&z&&(R=e.FRONT_BOX),R===e.DRAWER&&w&&240===c&&(R=e.OPENING),[e.DOOR,e.DRAWER,e.FRONT_BOX].includes(R)&&(b=1);const M=Be("fill__flip",t,s)[0]||0;let P=S&&"door_flip"in S&&null!==S.door_flip?{left:1,right:0}[S.door_flip]:null,H=S&&"cables"in S&&null!==S.cables?S.cables:null;const D=null!=s["#object_type"]?s["#object_type"]:p.T02,v=[];if(A){let e=f>0?f+2:f,t=m>0?m+3:m,s=y>0?y+2:y,i=x>0?x+2:x;v.push(...ke(n,o,r,a,l,c,e,t,s,i,0,0,O,I))}v.push(...ke(n,o,r,a,l,c,f,m,y,x,0,0,O,I));let{subsections:C,lines_x:L,lines_y:B}=Fe(t,s,n,o,r,a);void 0!==L&&0!==L.length||void 0!==B&&0!==B.length||(I=0);let j=I;return Be("insert__dom_share",t,s,0,0,!1)[0]||(C=[[n,o,r,a,c]],j=0,R===e.DOOR&&(R=e.DOOR_F)),C.forEach(r=>{const[a,h,_,d,u]=r;let f=Be("fill__split",t,s,n,o,"50%")[0],m=Be("cable__pos_y",t,s,0,d,[!1]),y=Be("cable__pos_x",t,s,0,h,[!1]),x=i.T02_BACK_WIDTH;if(s&&null!=s["#object_type"]){x={[p.T01]:i.T01_BACK_WIDTH,[p.T02]:i.T02_BACK_WIDTH,[p.VENEER_T01]:i.T01_V_BACK_WIDTH}[s["#object_type"]]}const S=Be("fill__split_start",t,s,0,0,x)[0],T=Be("fill__split_end",t,s,0,0,Number.POSITIVE_INFINITY)[0];const I={drawer_divisions:Be("drawer__divisions",t,s,a,h,[]),drawer_height:Be("drawer_height",t,s,0,0,!1)[0],drawer_autofill:Be("drawer_autofill",t,s,0,0,!1)[0],drawer_exterior:Be("drawer_exterior",t,s,0,0,!1)[0]};let N=Be("rod__pos_y",t,s,0,d,[!1]),z=Be("rod__pos_z",t,s,0,u,[!1]);(S>=h||h>=T||a+h<f||a>f)&&(f=!1),N[0]&&v.push(...Ge(N,z,a,h,_,d,l,c-j,O)),v.push(...function(t,s,n,o,r,a,l,c,h,_,d=null,u=!1,f=null,m=!1,y=p.T02,x=[],b=null,E=[],O={},S=!1){const T=[],{x1:A,x2:I,y1:R,y2:N,z1:z,z2:w}=g(t,s,n,o,r,a),M=Object.assign(Object.assign({x1:A,x2:I,y1:R,y2:N,z1:z,z2:w},_),{type:h,base_e_id:d,shelf_type:y});if(m&&h!==e.DRAWER){const e=Object.assign({},M);e.x1=m,T.push(e),M.x2=m,M.flip=1}u&&(M.flip=u),null!==f&&(M.custom_flip=f),h===e.DRAWER&&O.drawer_divisions&&(M.drawer_divisions=O.drawer_divisions.filter(e=>A<e&&e<I));const P=h===e.DRAWER&&a<=240;if(h!==e.FRONT&&h!==e.OPENING&&!P)if(h===e.DRAWER&&S){const t=N-R;let s=0;M.originalBox={bottom:R,top:N,left:A,right:I};for(let e=0;e<i.DRAWER_RANGES.length;e++){const n=i.DRAWER_RANGES[e];t>=n[0]&&t<=n[1]&&(s=i.DRAWER_COUNT[e])}if(O.drawer_exterior){const t=Object.assign({},M);t.type=e.ERASER_D,T.push(t);for(let e=0;e<s;e++){const t=Object.assign({},M);t.exterior=O.drawer_exterior,t.y1=R+e*i.EXTERIOR_DRAWER_HEIGHT,t.y2=t.y1+i.EXTERIOR_DRAWER_HEIGHT,t.z1=r+a+i.MAT_THICKNESS/2,t.z2=t.z1+i.FRAME_THICKNESS,t.depth=w-z,t.subtype="e",T.push(t)}}else{const e=i.DRAWER_HEIGHT+((t-i.MAT_THICKNESS)/s-i.DRAWER_HEIGHT);for(let t=0;t<s;t++){const s=Object.assign({},M);s.exterior=O.drawer_exterior,s.y1=R+i.MAT_THICKNESS/2+t*e+i.DRAWER_OFFSET,s.y2=s.y1+i.DRAWER_HEIGHT,s.subtype="i",T.push(s)}}}else T.push(M);c>0&&(M.z1=w-i.MAT_THICKNESS-4,T.push(Object.assign(Object.assign({x1:A,y1:R,y2:N,z1:w-i.MAT_THICKNESS-4},_),{x2:I,z2:w,type:e.FRONT})));let[H,D]=[!1,!1];switch(l){case 4:s>2*i.SUPPORT_WIDTH+i.MAT_THICKNESS/2?[H,D]=[!0,!0]:s>i.SUPPORT_WIDTH+i.MAT_THICKNESS/2&&(D=!0);break;case 3:s>i.SUPPORT_WIDTH+i.MAT_THICKNESS/2&&(D=!0);break;case 2:s>i.SUPPORT_WIDTH+i.MAT_THICKNESS/2&&(H=!0);break;case 1:const t=S?i.FRAME_THICKNESS:i.MAT_THICKNESS,n=Object.assign({x1:A,x2:I,y1:R,y2:N,z1:z,z2:z+t,split:m,type:e.BACK},_);T.push(n),!1!==x[0]&&h!==e.DRAWER&&x.forEach((t,s)=>{let n=A;!1===E[0]?n+=m?Math.round((I-A)/4):Math.round((I-A)/2):n+=E[s];let o=R+t;T.push(Object.assign({x1:n-i.CABLE_RADIUS,x2:n+i.CABLE_RADIUS,y1:o-i.CABLE_RADIUS,y2:o+i.CABLE_RADIUS,z1:z-1,z2:z+1+i.MAT_THICKNESS,radius:i.CABLE_RADIUS,active:!0===b,type:e.CABLE_OPENING},_))})}return!0===H&&T.push(Object.assign(Object.assign({x1:A,y1:R,y2:N,z1:z},_),{x2:A,z2:z+i.MAT_THICKNESS,size:i.SUPPORT_WIDTH,flip:!1,type:e.SUPPORT})),!0===D&&T.push(Object.assign(Object.assign({x2:I,y1:R,y2:N,z1:z},_),{x1:I,size:i.SUPPORT_WIDTH,flip:!0,z2:z+i.MAT_THICKNESS,type:e.SUPPORT})),T.push(Object.assign(Object.assign({x1:A,x2:I,y1:R,y2:N,z1:z,z2:w},_),{type:e.SHADOW_INNER})),T}(a,h,_,d,l,c-j,b,E,R,O,null,M,P,f,D,m,H,y,I,A))}),L.forEach(t=>{const[s,i,n]=t;v.push(Object.assign(Object.assign({},O),{x1:i,x2:i,y1:s,y2:n,z1:l,z2:c-I,type:e.INSERT_V}))}),B.forEach(t=>{const[s,i,n]=t;let o=e.INSERT_H;A&&(o=e.SLAB_STD),v.push(Object.assign(Object.assign({},O),{x1:s,x2:n,y1:i,y2:i,z1:l,z2:c-I,type:o}))}),v},Ge=(t,s,n,o,r,a,l,c,h)=>{const _=[],{x1:p,x2:d,y1:u,y2:f,z1:m,z2:y}=g(n,o,r,a,l,c);return t.forEach((t,n)=>{let o=u+t,r=m+(y-m)/2;!1!==s[0]&&(r=m+s[n]),_.push(Object.assign({x1:p,x2:d,y1:o-i.BAR_HEIGHT/2,y2:o+i.BAR_HEIGHT/2,z1:r-i.BAR_WIDTH/2,z2:r+i.BAR_WIDTH/2,type:e.BAR},h))}),_},Fe=function(e,t,s,i,n,o){const r=function(e){const t=null===e[0]||!1===e[0]?[null]:[],s=(null===e[e.length-1]||!1===e[e.length-1])&&e.length>1?[null]:[];return e=1!==(e=t.concat(e.slice(t.length,e.length-s.length).sort((e,t)=>e-t),s)).length?e:[null,e[0],null]},a=function(e,t,s){const i=e=>e[2],n=e=>e[2]+e[3],o=e=>e[4],r=e=>e[4]+e[5];return t.forEach(t=>{const a=[];e.forEach(e=>{"x"===s&&i(e)<t[1]&&t[1]<n(e)&&t[0]<r(e)&&t[2]>o(e)?a.push([i(e),t[1]-i(e),e[4],e[5]],[t[1],n(e)-t[1],e[4],e[5]]):"y"===s&&o(e)<t[1]&&t[1]<r(e)&&t[0]<n(e)&&t[2]>i(e)?a.push([e[2],e[3],e[4],t[1]-o(e)],[e[2],e[3],t[1],r(e)-t[1]]):a.push(e)}),e=a}),e};let[l,c,h]=[[],[],[]];if(!("insert__dom_x"in e)&&!("insert__dom_y"in e))return l=[[0,0,s,i,n,o]],{subsections:l,lines_x:c,lines_y:h};const _=r(Be("insert__dom_x",e,t,s,i,[null,null])),p=r(Be("insert__dom_y",e,t,n,o,[null,null])),[d,u,f]=[_.filter(e=>e),_[0]||s,_[_.length-1]||s+i],[m,g,y]=[p.filter(e=>e),p[0]||n,p[p.length-1]||n+o];return c=d.map(e=>[g,e,y]).sort((e,t)=>e-t),h=m.map(e=>[u,e,f]).sort((e,t)=>e-t),l=[[u,f-u,g,y-g]],l=a(l,c,"x"),l=a(l,h,"y"),{subsections:l,lines_x:c,lines_y:h}},Ke=function(t,s,n,o,r=0,a=0,l=0,c=!0,h=!0,_=null,p={},d={},u=null,f=null,m=null,g=null,y=null){if(void 0===t.configs||0===t.configs.length)return[x(r,a,l,s,300,n,_,"Missing component configs for component -> "+t.name)];const b=[];let O=a,S=t.dim_x&&2===(""+t.dim_x).split("-").length?t.dim_x:"100-1000",T={};t.configs.forEach((e,i)=>{const o=Ue(e,s,n,r,O,l,0===i,i>=t.configs.length-1,c,h,{m_config_id:_?_.m_config_id:null},p,d);T=Object.assign(Object.assign(Object.assign({},e.constants),p),d),o.length>0&&(Be("trans__mirror",e.parameters,Object.assign(Object.assign({},e.constants),p))[0]&&E(o),b.push(...o),O=Math.max(...o.map(e=>e.y2)))}),b.push(Object.assign(Object.assign({x1:r,x2:r+s,y1:a,y2:O===a?a+100:O,z1:l,z2:l+n,id:o,type:e.COMPONENT},_),{w_min:m||Number(S.split("-")[0]),w_max:g||Number(S.split("-")[1]),door_flip:d?d.door_flip:null,cables:d?d.cables:null,line_left:u,line_right:f,local_x:y}));let A=Be("exterior_doors_split_y_start",t.parameters,T,a,O,[],null,""),I=Be("exterior_doors_split_y_value",t.parameters,T,a,O,[],null,""),R={starts:{y:A,x:Be("exterior_doors_split_x_start",t.parameters,T,0,s,i.EXTERIOR_DOOR_MAX,null,"")[0]},values:{y:I,x:Be("exterior_doors_split_x_value",t.parameters,T,0,s,"50%",null,"")[0]}};return b.push(Xe(r,a,l,s,O,n,_,R)),b},Xe=(t,s,n,o,r,a,l,c)=>{const h=i.FRAME_OFFSET-i.FRAME_THICKNESS;return Object.assign({x1:t,x2:t+o,y1:s,y2:r,z1:n+a+h,z2:n+a+i.FRAME_OFFSET,type:e.DOOR_EXT,splitCfg:c},l)},Ve=function(e,t,s,i,n,o,r=0,a=0,l=0,c=!0,h=!0,_=null,p=null,d=null,u=null,f=null,m=null,g=null,y=null){const b=e.setups?e.setups[i]:void 0,E=t[b];return void 0===E?[x(r,a,l,s,300,n,_,`Missing Component in Series ${e.parameters.name} for height -> ${i}. Available: ${Object.keys(e.setups||{}).join(", ")}`)]:Ke(E,s,n,b,r,a,l,c,h,Object.assign(Object.assign({},_),{series_id:e.parameters.series_id}),p,d,u,f,m,g,y)},Ue=function(e,t,s,i,n,o,a,l,c,h,_,p,d){const u=[],f=Object.assign(Object.assign(Object.assign({},e.constants),p),d),m=Ye(e.parameters,f,i,t),g=Be("e_size_y",e.parameters,f,0,0,0)[0];return m.forEach(t=>{const[i,a,l,c,h,p,m,y,x]=t,b=l-a,E=[];e.subconfigs.forEach(e=>{const t=Object.assign(Object.assign({},e.constants),f),s=Be("part__value",e.parameters,t,0,0,[0]);let n=!0;if(e.parameters.optional_dim_y){const t=e.parameters.optional_dim_y.split("-");if(2===t.length&&t.every(e=>!isNaN(e))){const e=parseInt(t[0]),s=parseInt(t[1]);n=g>e&&g<=s}else n=!0}s.indexOf(i)<0&&(s.indexOf(0)<0||-1===i)||b<Be("trans__start",e.parameters,t,0,0,0)[0]||b>Be("trans__stop",e.parameters,t,0,0,Number.POSITIVE_INFINITY)[0]||n&&E.push(e)}),-1===[-1,0].indexOf(i)&&0===E.length&&u.push(...We(e.parameters,f,a,b,n,g,o,s,0,0,0,0,c,h,p,m,y,x,Object.assign(Object.assign({},_),{c_config_id:e.parameters.c_config_id,c_subconfig_id:e.parameters.c_config_id}),d)),E.forEach(t=>{const i=Object.assign(Object.assign({},t.constants),f),l=Be("type",t.parameters,i,0,0,"O")[0],{left:c,top:h,right:p,bottom:m,back:y,front:x}=r[l];let E=Be("face__s_left",t.parameters,i,0,0,c)[0],O=Be("face__s_right",t.parameters,i,0,0,p)[0],S=Be("face__s_top",t.parameters,i,0,0,h)[0],T=Be("face__s_bottom",t.parameters,i,0,0,m)[0],A=Be("face__s_back",t.parameters,i,0,0,y)[0],I=Be("face__s_front",t.parameters,i,0,0,x)[0];u.push(...We(t.parameters,i,a,b,n,g,o,s,0,0,0,0,E,S,O,T,A,I,Object.assign(Object.assign({},_),{c_config_id:e.parameters.c_config_id,c_subconfig_id:t.parameters.c_config_id}),d))})}),u},Ye=function(e,t,s,i){const n=Be("type",e,t,0,0,"O")[0],o=r[n],a=t&&"#open_back"in t?t.open_back:o.back,l=Be("face__s_left",e,t,0,0,o.left)[0],c=Be("face__s_right",e,t,0,0,o.right)[0],h=Be("face__s_top",e,t,0,0,o.top)[0],_=Be("face__s_bottom",e,t,0,0,o.bottom)[0],p=1===a?1:Be("face__s_back",e,t,0,0,o.back)[0],d=Be("face__s_front",e,t,0,0,o.front)[0],u=[[-1,s,s+i,l,h,c,_,p,d]];if(Be("triple__dim",e,t,s,i)[0]&&Be("triple__start",e,t,0,0,0)[0]<=i&&i<Be("triple__stop",e,t,0,0,Number.POSITIVE_INFINITY)[0]){const n=Be("face__t_top_l",e,t,0,0,h)[0],o=Be("face__t_top_m",e,t,0,0,h)[0],r=Be("face__t_top_r",e,t,0,0,h)[0],a=Be("face__t_bottom_l",e,t,0,0,_)[0],f=Be("face__t_bottom_m",e,t,0,0,_)[0],m=Be("face__t_bottom_r",e,t,0,0,_)[0],g=Be("face__t_middle_l",e,t,0,0,1)[0],y=Be("face__t_middle_r",e,t,0,0,1)[0],x=Be("face__t_back_l",e,t,0,0,p)[0],b=Be("face__t_back_m",e,t,0,0,p)[0],E=Be("face__t_back_r",e,t,0,0,p)[0],O=Be("face__t_front_l",e,t,0,0,d)[0],S=Be("face__t_front_m",e,t,0,0,d)[0],T=Be("face__t_front_r",e,t,0,0,d)[0],A=Be("triple__dim",e,t,s,i);let[I=null,R=null]=A;I=Number.isNaN(parseInt(I,10))?s+i/3:I,R=Number.isNaN(parseInt(R,10))?s+i/3*2:R,R<I&&([I,R]=[R,I]),u.push([4,s,I,l,n,g,a,x,O],[5,I,R,g,o,y,f,b,S],[6,R,s+i,y,r,c,m,E,T])}else if(Be("double__dim",e,t,s,i)[0]&&Be("double__start",e,t,0,0,0)[0]<=i&&i<Be("double__stop",e,t,0,0,Number.POSITIVE_INFINITY)[0]){const n=Be("face__d_top_l",e,t,0,0,h)[0],o=Be("face__d_top_r",e,t,0,0,h)[0],r=Be("face__d_bottom_l",e,t,0,0,_)[0],a=Be("face__d_bottom_r",e,t,0,0,_)[0],f=Be("face__d_middle",e,t,0,0,1)[0],m=Be("face__d_back_l",e,t,0,0,p)[0],g=Be("face__d_back_r",e,t,0,0,p)[0],y=Be("face__d_front_l",e,t,0,0,d)[0],x=Be("face__d_front_r",e,t,0,0,d)[0],b=Be("double__dim",e,t,s,i,null,i/2)[0];u.push([2,s,b,l,n,f,r,m,y],[3,b,s+i,f,o,c,a,g,x])}else u.push([1,s,s+i,l,h,c,_,p,d]);return u.sort((e,t)=>e-t)},$e=(e,t,s=-1,i=!0)=>{const n={"1x":0,"2x":100,eq:50},o=[...new Set(e)];e.forEach((e,r)=>{t[r].localX=o.length>1?{value:n[e],width:0,available:i}:{value:n.eq,width:0,available:i},t[r].localX.changed=s===r})},Ze=function(e,t,s=0){let i=je(e,s,t),n=[];return i.reduce((e,t,s)=>n[s]=e+t,0),n.unshift(0),i.map((e,t)=>[n[t],e])},qe=function(e,t,s,n=0,o=null,r=null,a=null){let l=[];const c=!e.parameters||null==e.parameters.distortion_available||e.parameters.distortion_available;e.configs.forEach((e,t)=>{let{division_ratio:s,distorted_ratio:n,comp_id:o,channel:a,table_dim_x:c,calc_table_dim_x:h}=e.parameters;n=n||s,n=(""+n).split(",");let _=r&&r[a]?r[a].distortion:null,p=!(!r||!r[a])&&r[a].changed,d=h?(""+h).split("-")[0]:null,u=h?(""+h).split("-")[1]:null;(""+s).split(",").forEach((e,s)=>{l.push(Object.assign({ratio:(""+e).toLowerCase(),distorted_ratio:(""+(n[s]||n[0])).toLowerCase(),local_ratio:_,line_right:null,line_left:null,localX:null,changed:p,channel:a,comp_id:o,index:t},((e,t)=>{let s={min:270+i.MAT_THICKNESS,max:840+i.MAT_THICKNESS};return e&&(s.min=parseInt(e)),t&&(s.max=parseInt(t)),s})(d,u)))})});let h=[];switch(o){case"gradient":h=function(e,t,s){let i=e.filter(e=>e.includes("x")).map(e=>Number(e.replace("x",""))),n=e.filter(e=>!e.includes("x")).map(e=>je(e,0,t)),o=i.reduce((e,t)=>t+e,0),r=n.reduce((e,t)=>t+e,0),a=function(e,t,s=.4,i=.9){let n=[0];for(let o=1;o<e;o++){let r=1/e*o,a=s+(i-s)*(r<t?(t-r)/t:(r-t)/(1-t));n.push(t+(r-t)*a)}return n.push(1),n}(o,s).map(e=>e*(t-r));a=oe(a,t-r);let l=[],c=0,h=0;for(let t=0;t<e.length;t++){if(e[t].includes("x")){let e=i.shift();h+=e,l.push([c,a[h]-a[h-e]])}else l.push([c,n.shift()]);c=l[t][0]+l[t][1]}return l}(l.map(e=>e.ratio),t,s);break;case"ratio":h=Ze(l.map(e=>e.ratio.includes("x")&&e.distorted_ratio.includes("x")?Number(e.ratio.replace("x",""))*(1-s)+Number(e.distorted_ratio.replace("x",""))*s+"x":e.ratio.includes("%")&&e.distorted_ratio.includes("%")?Number(e.ratio.replace("%",""))*(1-s)+Number(e.distorted_ratio.replace("%",""))*s+"%":e.ratio*(1-s)+e.distorted_ratio*s),t,n);break;case"local_x":h=Ze(((e,t)=>{const s=e.map(e=>e.ratio),i=e.map(e=>e.channel).sort((e,t)=>t-e),n=e.map(e=>e.local_ratio),o=n.findIndex(e=>null!=e);if(-1===o||1===e.length)return $e(s,e,o,t),s;if(t){const t=n[o],r=e[o];let a=0;const l=e.findIndex(e=>e.changed);if(l>-1)a=l;else{let t=i[0];t===r.channel&&(t=i[1]),a=e.findIndex(e=>e.channel===t)}switch(t){case 0:s.forEach((e,t)=>{s[t]=t===o?"1x":"2x"});break;case 50:s.forEach((e,t)=>s[t]="1x");break;case 100:s.forEach((e,t)=>{s[t]=t===o?"2x":t===a?"1x":"2x"})}}return $e(s,e,o,t),s})(l,c),t,n);break;case"local_all":if(h=Ze(l.map(e=>e.ratio),t,n),1===h.length)break;let e=h.map(e=>e[1]),i=e.map((t,s)=>l[s].local_ratio?l[s].local_ratio*e[s]/100:0);e=e.map((t,s)=>t-i.reduce((e,t,i)=>i===s?e:e+t,0)/(e.length-1)+i[s]),h=Ze(e,t,n);break;case"proportional":h=function(e,t,s){let i=Ze(e.map(e=>e.ratio),t,s);if(1===i.length)return i;let n=i.map(e=>e[1]),o=n.map((t,s)=>e[s].local_ratio?e[s].local_ratio*n[s]/100:0),r=1;for(;0!==r;)if(r=0,n=n.map((t,s)=>{let i=t-o.reduce((e,t,i)=>i===s?e:e+t,0)/(n.length-1)+o[s];return i<e[s].min&&(r+=Math.floor(e[s].min-i),o[s]-=Math.floor(e[s].min-i)),i>e[s].max&&(r+=Math.floor(i-e[s].max),o[s]-=Math.floor(i-e[s].max)),i=Math.min(Math.max(i,e[s].min),e[s].max),i}),r>0){let t=o.filter((t,s)=>t+n[s]<e[s].max).length;o=o.map((s,i)=>s+n[i]<e[i].max?s+r/t:s)}else if(r<0){let t=o.filter((t,s)=>t+n[s]>e[s].min).length;o=o.map((s,i)=>s+n[i]>e[i].min?s+r/t:s)}return i=Ze(n,t,s),i}(l,t,n);break;case"edge":h=Ze(l.map(e=>e.ratio),t,n),h=h.map((e,t)=>{let{channel:s}=l[t],i=l[t-1]?l[t-1].channel:null,n=l[t+1]?l[t+1].channel:null,o=a&&Math.round(a[i+"_"+s])||null,r=a&&Math.round(a[s+"_"+n])||null;return l[t].line_left=o,l[t].line_right=r,o&&(e[1]-=o,e[0]+=o),r&&(e[1]+=r),e}),[h,l]=function(e,t){return t.reduce((t,s,i)=>{if(e[i-1][1]>t.max){let n=e[i-1][1]-t.max;e[i-1][1]-=n,t.line_right-=n,s.line_left-=n,e[i][0]-=n,e[i][1]+=n}else if(e[i-1][1]<t.min){let n=t.min-e[i-1][1];e[i-1][1]+=n,t.line_right+=n,s.line_left+=n,e[i][0]+=n,e[i][1]-=n}return s}),t.reduceRight((t,s,i)=>{if(e[i+1][1]>t.max){let n=e[i+1][1]-t.max;e[i+1][0]+=n,e[i+1][1]-=n,t.line_left+=n,s.line_right+=n,e[i][1]+=n}else if(e[i+1][1]<t.min){let n=t.min-e[i+1][1];e[i+1][0]-=n,e[i+1][1]+=n,t.line_left-=n,s.line_right-=n,e[i][1]-=n}return s}),[e,t]}(h,l);break;default:h=Ze(l.map(e=>e.ratio),t,n)}return h.map(([e,t],s)=>{let{comp_id:i,index:n,line_right:o,line_left:r,min:a,max:c,localX:h,channel:_}=l[s];return{mc_start_x:e,mc_size_x:t,comp_id:i,index:n,line_right:o,line_left:r,min:a,max:c,localX:h?Object.assign(Object.assign({},h),{width:t}):null,channel:_}})};function Je(e,t,s){let i=[];for(let n=1;n<=t;n++)if(1===n)i.push(e);else{const e=i[i.length-1];i.push(e+s)}return i}const Qe=function(t,s,n,o,r,a,l){function c(t,s,i,n,a,l){if(u===p.T02)return;if(t.push({x1:i,x2:n,y1:0,y2:f,z1:(o-r)/2,z2:(o+r)/2,type:"x"===a?"Px":"Pe",flip:{left:0,mid:null,right:1}[l],subtype:a}),"x"===a){const t=o/2;n-i>3*x?(s.push({x1:i+x-S/2,x2:i+x+S/2,y1:-10,y2:0,z1:t-S/2,z2:t+S/2,modelType:u,type:e.LEG}),s.push({x1:n-x-S/2,x2:n-x+S/2,y1:-10,y2:0,z1:t-S/2,z2:t+S/2,modelType:u,type:e.LEG})):n-i>2*x&&s.push({x1:(i+n)/2-S/2,x2:(i+n)/2+S/2,y1:-10,y2:0,z1:t-S/2,z2:t+S/2,modelType:u,type:e.LEG})}}function h(t,s,a,l,c=!1,h=0){let d=a-r/2,g=a+r/2,x=d-y<-r/2?-r/2:d-y;x<l[0]&&(l[0]=x);let E=d+y>n+r/2?n+r/2:d+y;if(E>l[1]&&(l[1]=E),!t.some(e=>e.x1===d&&e.x2===g))if([p.T01,p.VENEER_T01].includes(u)){const i=d+(g-d)/2;t.push({x1:d,x2:g,y1:0,y2:f,z1:0+m,z2:o-m,type:"Pz",subtype:"z",flip:null}),s.push({x1:i-S/2,x2:i+S/2,y1:T,y2:0,z1:0+m+30-S/2,z2:0+m+30+S/2,modelType:u,type:e.LEG}),s.push({x1:i-S/2,x2:i+S/2,y1:T,y2:0,z1:o-m-30-S/2,z2:o-m-30+S/2,modelType:u,type:e.LEG})}else{const t=_(c,b+i.MAT_THICKNESS/2,h>0);s.push({x1:d,x2:g,y1:T,y2:f,z1:0+t,z2:0+t,rotation_z:0,type:e.LONG_LEG}),s.push({x1:d,x2:g,y1:T,y2:f,z1:o-t,z2:o-t,rotation_z:0,type:e.LONG_LEG})}}const _=(e,t,s=!1)=>e[0]?e.length>1&&s?e[1]:e[0]:t;function d(e,t,s,i=!0,o,r){u===p.T02?(i&&(!0===s||""===s)&&e<=x&&h(I,R,x,N),i&&(!0===s||""===s)&&t>=n-x?h(I,R,n-x,N):"boolean"!=typeof s&&""!==s&&h(I,R,Be("x",{x:s},l,e,t-e)[0],N,o,r)):(i&&(!0===s||""===s)&&e<=g&&h(I,R,g,N),i&&(!0===s||""===s)&&t>=n-g?h(I,R,n-g,N):"boolean"!=typeof s&&""!==s&&h(I,R,Be("x",{x:s},l,e,t-e)[0],N))}const u=null!=l["#object_type"]?l["#object_type"]:p.T02,f=Be("plinth__height",a,l,0,0,100)[0],m=Be("plinth__offset_z",a,l,0,0,30)[0],g=Be("plinth__offset_x",a,l,0,0,320)[0],y=Be("plinth__offset_long",a,l,0,0,130)[0],x=Be("plinth__legs_offset_x",a,l,0,0,140)[0],b=Be("plinth__legs_offset_z",a,l,0,0,0)[0],E=f+i.MAT_THICKNESS/2,O=function(e,t,s=1200){const i=e-2*t,n=Math.floor(i/s),o=2+n,r=n+1,a=Math.floor(i/r);return{numberOfAllCrosses:o,middleSpacing:a,restSpace:i-a*r}}(n,g,1200),S=14,T=-9;let A=Be("plinth__setup",a,l,0,n,[]);if((A||A.length)&&(A=[p.T01,p.VENEER_T01].includes(u)?Je(g,O.numberOfAllCrosses,O.middleSpacing):Je(x,2,n-2*x)),!1===A[0]||!s.some(e=>""!==e.plinth[0]&&!1!==e.plinth[0]))return t;let I=[],R=[],N=[g-y,n-g+y+r/2];s.forEach(e=>{let{x1:t,x2:s,plinth:i,legs_offset_z:n}=e;i.forEach((e,i)=>d(t,s,e,0===A.length,n,i))}),A.forEach(e=>d(0,n-r,e));const z=I.length;z>2&&(N[1]=N[1]-O.restSpace);let w=[];return I.sort((e,t)=>e.x1-t.x1).reduce((e,t,s)=>e||t?(0===s&&t.x1>N[0]?c(w,R,N[0],t.x1,"e","left"):e&&t&&c(w,R,e.x2,t.x1,"x","mid"),s===z-1&&t.x2<N[1]&&c(w,R,t.x2,N[1],"e","right"),t):null,null),t.forEach(e=>{e.y1+=E,e.y2+=E}),R=function(e){let t=[],s=[];for(let i=0;i<e.length;i++){const n=`${e[i].z1}${e[i].x1}`;t.includes(n)||(t.push(n),s.push(e[i]))}return s}(R),R=function(e){let t=e.sort((e,t)=>e.x1-t.x1||e.z1-t.z1);return t.forEach((s,i)=>{0===i?s.rotation_z=90:1===i?s.rotation_z=0:i===t.length-2?s.rotation_z=180:i===t.length-1?s.rotation_z=270:i%2==0&&e[i+1].x1==s.x1?s.rotation_z=135:s.rotation_z=315}),t}(R),t=(t=t.concat(...R)).concat({x1:N[0],x2:N[1],y1:0,y2:f,z1:0+m,z2:o-m,components:[...I,...w],type:e.PLINTH})};const et=function(e,t,s,n,o,r,a,l,c=0,h=0,d=0,u=null,f=null,m=null,g=null,y=null,b=!0,O=null,S=null){let T=e.parameters?e.parameters.density_mode:void 0;y=y||m,g=g||m,o<100&&Object.keys(e.setups).includes(""+o)&&(o=e.setups[o].parameters.dim_x[0]||o);let[A,I]=function(e,t,s,i,n){if(n){const t=parseInt(n);for(let s in e)if(e[s].parameters.setup_id===t)return[e[s],[0]];return[null,[]]}switch(i){case"setup_range_slider":case"setup_range_stepper":let i=Object.keys(e).filter(s=>"parameters"in e[s]&&"dim_x"in e[s].parameters&&e[s].parameters.dim_x[0]<=t&&t<=e[s].parameters.dim_x[1]).sort((e,t)=>e-t),n=Math.max(Math.ceil(s/(100/i.length)-1),0),o=i.map((t,s)=>[Math.min(Math.ceil(100*s/i.length+1),100),e[t].configs.length]);return o[n]&&(o[n][0]=s||0),[e[i[n]],o];default:let r=!e||Object.keys(e).some(t=>null==e[t].parameters.dim_x),a=Object.keys(e).filter(s=>function(e,t,s,i){if(s)return t<=i;{const s=e[t].parameters.dim_x,n=s[0],o=s[1];return n<=i&&i<(o!==i?o:o+1)}}(e,s,r,t)).sort((e,t)=>e-t).reverse()[0];return void 0===a?[null,[]]:[e[a],[0]]}}(e.setups,o,g,T,f);if(null==A)return[x(c,h,d,o,r,a,{},"Missing mesh setup for width -> "+o)];if(void 0===A.configs||0===A.configs.length)return[x(c,h,d,o,r,a,{},"Missing mesh configs for width -> "+o)];let R=e.parameters?e.parameters.distortion_mode:void 0,N=null,z=null;O&&(O.channels&&(N=O.channels),O.lines&&(z=O.lines));let w=o;if(S.collection_type===_){w=o-i.WARDROBE_AXIS_OFFSET,a-=i.FRAME_OFFSET;const e=2e3;r>=e&&(S=Object.assign(Object.assign({},S),{"#pawlacz":r-e}),r=300)}else w=o-i.SIDEBOARD_AXIS_OFFSET;let M=Be("motion",e.parameters,u,0,0)[0],P=Be("distortion",e.parameters,u)[0],H=qe(A,w,.01*(y||M),0,R||P,N,z);const D=H.find(e=>e.localX&&0===e.localX.value);if(D){const e=((e,t)=>{const s={};return e.configs.forEach(e=>{const i=e.parameters.channel,n=t[i];s[i]=Object.assign(Object.assign({},n),{series_id:e.parameters.series_pick})}),Object.assign(Object.assign({},t),s)})(A,N),t=(e=>{const t=e.map(e=>e.channel);return t.sort((e,t)=>t-e),t[0]})(H),s=D.channel;s!==t&&(((e,t,s,i)=>{const n=Object.assign({},t[s]),o=Object.assign({},t[i]),r=e.configs.find(e=>e.parameters.channel===s),a=e.configs.find(e=>e.parameters.channel===i);r.parameters.channel=i,a.parameters.channel=s,t[i]=n,t[s]=o})(A,e,s,t),N=e,O.channels=N)}H.length;let v=[],C=[],L=[];return e.parameters.object_type===p.T02&&(L=tt(H)),H.forEach(({mc_start_x:i,mc_size_x:o,comp_id:l,index:c,line_left:_,line_right:f,min:m,max:g,localX:y})=>{let x=function(e,t,s,i,n,o){let r=n;e.configs[o].parameters.series_pick&&(r=e.configs[o].parameters.series_pick);let a=null;e.configs[0]&&"series"in e.configs[0].parameters?a=e.configs[0].parameters.series[o]||e.configs[0].parameters.series:e&&"series"in e.parameters&&(a=e.parameters.series[o]||e.parameters.series),a&&""+n in t&&(r=""+t[n].configs[a]in s?t[n].configs[a]:Object.values(t[n].configs)[0]);let l=e.configs[o].parameters.config_id,c=e.configs[o].parameters.channel;return i&&l in i&&(r=i[l].series_id||r),i&&c in i&&(r=i[c].series_id||r),r in s?s[r]:Object.values(s)[0]}(A,t,s,N,l,c),b=null,O=A.configs[c].parameters.channel;N&&(b=N[O]||null);let T,R=Ve(x,n,o,r,a,0,i,h,d,0===c,c===H.length-1,{m_config_id:A.configs[c].parameters.config_id,m_setup_id:A.parameters.setup_id,table_id:A.configs[c].parameters.table||null,channel_id:A.configs[c].parameters.channel,order:c,distortion:b&&b.distortion?b.distortion:0,density_options:I},Object.assign(Object.assign(Object.assign({},A.configs[c].constants),u||{}),S||{}),Object.assign(Object.assign({},b),S),_,f,m,g,y);Be("trans__mirror",A.configs[c].parameters,u)[0]&&(R=E(R)),C.push(...R),T=e.parameters.object_type!==p.T02||L[c];let z=Be("plinth__value",A.configs[c].parameters,u,0,o,T);v.push({x1:i,x2:i+o,plinth:z,plinth__offset_z:Be("plinth__offset_z",A.configs[c].parameters,u,0,o,e.parameters.plinth__offset_z),legs_offset_z:Be("legs_offset_z",A.configs[c].parameters,u,0,a,!1)})}),!1!==b&&(C=Qe(C,v,w,a,i.MAT_THICKNESS,Object.assign(Object.assign({},e.parameters),A.parameters),Object.assign(Object.assign({},e.constants),u))),Be("trans__mirror",e.parameters,u)[0]&&(C=E(C)),C},tt=(e=[])=>{let t=new Array(e.length).fill(!0),s=Math.round(t.length/2),n=Math.floor(t.length/2),o=(e,t=0)=>{!0===e[t]?e[t]=[i.MIDDLE_POSITION]:e[t].includes(i.MIDDLE_POSITION)||e[t].push(i.MIDDLE_POSITION)},r=(e,t,s)=>{!0===e[t]?e[t]=[s]:e[t].push(s)};try{let a=0;for(let n=0;n<=s;n++)a+=e[n].mc_size_x,a>=i.LEGS_DISTANCE&&(n>0&&r(t,n,0),a=e[n].mc_size_x),e[n].mc_size_x>=i.LEGS_DISTANCE&&o(t,n);let l=0;for(let s=t.length-1;s>=n-1;s--)l+=e[s].mc_size_x,l>=i.LEGS_DISTANCE&&(s<t.length-1&&r(t,s,e[s].mc_size_x),l=e[s].mc_size_x),e[s].mc_size_x>=i.LEGS_DISTANCE&&o(t,s)}catch(e){}return t},st=function(e,t,s,i,n,o,r,a,l,c=0,h=0,_=0,p=null,d,u){switch("parameters"in e&&"container_mode"in e.parameters?e.parameters.container_mode:null){case"old_configurator":return function(e,t,s,i,n,o,r,a,l,c=0,h=0,_=0,p,d,u){let f=[],m=h;const g=[278,398,300,400],y=e.setups["single-setup"],{row_amount:x,row_heights:b,row_styles:E,motion:O,backpanels_rows:S}=(e.parameters.density_mode,e.parameters.distortion_mode,p),T=y.configs.length;for(let e=0;e<x;e++){const r=y.configs[T>e?e:e%T],l=b[e];let h=E[e];h=h<10?h+10:h,g.indexOf(l)<0&&(h=Number(String(h)[0]+"1")),m+l>1578&&(h=Number("1"+String(h)[1]));const p=Object.assign(Object.assign({},r.parameters.row_style_table[h]),r.constants);p["#open_h"]=l,p["#back_panels"]=!!S.length;const x=et(t[r.parameters.mesh_id],s,i,n,o,300,a,r.parameters.mesh_id,c,m,_,p,null,O,O,0,!1,d,u);f.push(...x),m=Math.max(...x.map(e=>e.y2))}return f}(e,t,s,i,n,o,0,a,0,c,h,_,p,d,u);default:return null}};function it(e,s,i=!1){let n;e.serialization&&(e=e.serialization);const o=["GRID","GRADIENT","SLANT","PATTERN"].includes(s.geom_type);switch(o||(!function(e){let t=e.component;if(e.mesh&&e.component_series){let s=e.component_series;Object.keys(s).forEach(e=>{let i=[],n=s[e].setups;Object.values(n).forEach(e=>{let s=t[e].parameters.dim_x.split("-").map(e=>Number(e));i.push(s)});const o=Math.max(...i.map(e=>e[0])),r=Math.min(...i.map(e=>e[1]));s[e].dim_x=`${o}-${r}`});let i=e.component_table;Object.keys(i).forEach(e=>{let t=[],n=i[e].configs;Object.values(n).forEach(e=>{let i=s[e].dim_x.split("-").map(e=>Number(e));t.push(i)});const o=Math.max(...t.map(e=>e[0])),r=Math.min(...t.map(e=>e[1]));i[e].dim_x=`${o}-${r}`});let n=e.mesh;Object.keys(n).forEach(e=>{let t=n[e].setups;Object.keys(t).forEach(e=>{t[e].configs.forEach(e=>{e.parameters.calc_table_dim_x=i[e.parameters.table].dim_x})})})}}(e),s.material&&isNaN(s.material)&&(s.material.includes(":")?s.material=s.material.split(":")[1]:isNaN(s.material)||(s.material=parseInt(s.material)))),s.geom_type){case"GRID":case"GRADIENT":case"SLANT":case"PATTERN":n=((e,t,s,i,n,o,r=320,a=9,l=125,c=!1,h=[2,2,4,2],_=!0,p=-1,d=null,u=0,f=null)=>{(e=>{e.dna_name.includes("GRID")||(e.dna_name.includes("GRADIENT")||(e.dna_name.includes("SLANT")||e.dna_name.includes("PATTERN")))})(e);if(e.dna_name.includes("PATTERN")&&!1!==c&&d&&d.length){let e=0===u?A:I;s in e&&t in e[s]&&(t=e[s][t])}let m=o.map(e=>{return s=r,(t=e)<40&&t>19&&240===s?t-20:t;var t,s});o=[...m];let g=e.dna_name.includes("PATTERN")?2:e.dna_name.includes("SLANT")?0:e.dna_name.includes("GRADIENT")?1:3,y=Object.keys(e).indexOf("dekoder_settings")>-1?e.dekoder_settings:{drawer_max_position:1578};if(y.proper_door_heights=[278,398,300,400],y.shelf_type=u,0===u){let u=G(e,t,s,i,n,o,r,a,l,c,h,_,p,d,-1,y);return u=F(u,f),u}if([0,3].indexOf(g)>-1){let e;e=0===g?[[700,50],[1020,34],[1060,0],[1360,50],[1440,34],[1710,25],[1980,20],[2e3,40],[2410,25],[2610,50],[2820,25],[3180,50],[3400,25],[3970,20],[4320,40]]:[[700,50],[870,0],[1160,34],[1430,25],[1710,20],[1730,40],[1810,25],[1990,21],[2280,18],[2300,34],[2560,29],[2760,18],[2840,15],[2860,29],[3120,25],[3410,23],[3430,34],[3500,25],[3690,22],[3990,12],[4e3,23],[4250,13],[4410,17]];const i=e.reverse().find((function(e){return e[0]<=s}))[1],n=100;t=Math.max(0,Math.floor(i+(n-i)*t/100))}let x=G(e,t,s,i,n,o,r,a,l,c,h,_,p,d,-1,y);y.proper_door_heights=[208,278,398,200,300,400];let b=G(e,t,s,i,n,[13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13],r,a,l,c,h,_,p,d,551,y),E=[];n.reduce((function(e,t,s){return E[s]=e+t}),9),E.unshift(a);let O=E.map(e=>b.doors.filter((t,s,i)=>t.y1===e)),S=E.map(e=>b.backs.filter((t,s,i)=>t.y1===e)),T=E.map(e=>x.doors.filter((t,s,i)=>t.y1===e)),R=E.map(e=>x.drawers.filter((t,s,i)=>t.y1===e)),N=E.map(e=>x.backs.filter((t,s,i)=>t.y1===e));b.doors=[],b.drawers=[],b.backs=[];let z=[],w=[],M=[],P=[];for(let e=0;e<i;++e){z=O[e].filter(t=>T[e].some(e=>t.x1-2<=e.x1&&t.x2+2>=e.x2)||T[e].some(e=>e.x1-2<=t.x1&&e.x2+2>=t.x2)),w=O[e].filter(t=>R[e].some(e=>t.x1-2<=e.x1&&t.x2+2>=e.x2)||R[e].some(e=>e.x1-2<=t.x1&&e.x2+2>=t.x2)),M=[];for(let e=0;e<w.length-1;++e)w[e].x2===w[e+1].x1&&(w[e].x2=w[e+1].x2,M.push(e+1));w=w.filter((e,t)=>M.indexOf(t)<0),P=S[e].filter(t=>N[e].some(e=>t.x1-2<=e.x1&&t.x2+2>=e.x2)||N[e].some(e=>e.x1-2<=t.x1&&e.x2+2>=t.x2)),b.drawers.push(...w),b.doors.push(...z),b.backs.push(...P)}b.supports=x.supports,b.additional_elements.styles=x.additional_elements.styles;let H=x.verticals.filter(e=>!b.verticals.some(t=>Math.abs(e.x1-t.x1)<40&&Math.abs(e.x2-t.x2)<40&&e.y1===t.y1&&e.y2===t.y2));if(0!==H.length){let e=E.map(e=>H.filter((t,s,i)=>t.y1===e)),t=E.map(e=>b.additional_elements.shadow_middle.filter((t,s,i)=>t.y1===e)),s=E.map(e=>x.additional_elements.shadow_middle.filter((t,s,i)=>t.y1===e)),n=[];for(let o=0;o<i;++o)e[o].forEach(e=>{t[o]=t[o].filter(t=>!(t.x1<e.x1&&t.x2>e.x1)),n.push(...s[o].filter(t=>!(t.x2===e.x1-9||t.x1===e.x1+9)))});b.additional_elements.shadow_middle=[].concat.apply([],t),b.additional_elements.shadow_middle.push(...n),b.verticals.push(...H)}return b=F(b,f),b})(e,s.motion,s.width,s.row_amount,s.row_heights,s.row_styles,s.depth,s.half_material,s.support_size,s.snapping,s.shadow_settings,s.gen_row_styles_for_buttons,s.styles_slider,s.backpanels_rows||[],s.shelf_type||0);break;case"container":const t={row_amount:s.row_amount,row_heights:s.row_heights,row_styles:s.row_styles,backpanels_rows:s.backpanels_rows,motion:s.motion};n=st(e.container[s.geom_id],e.mesh,e.component_table,e.component_series,e.component,s.width,s.height,s.depth,s.geom_id,0,0,0,t,s.configurator_custom_params,s.additional_parameters);break;case"mesh":n=et(e.mesh[s.geom_id],e.component_table,e.component_series,e.component,s.width,s.height,s.depth,s.geom_id,0,0,0,null,s.mesh_setup,s.motion,s.density,s.distortion,s.plinth,s.configurator_custom_params,s.additional_parameters);break;case"component-set":case"component-table":case"component-series":n=Ve(e.component_series[s.geom_id],e.component,s.width,s.height,s.depth,s.geom_id);break;case"component":n=Ke(e.component[s.geom_id],s.width,s.depth,s.geom_id,0,0,0,!0,!0,null,null,Object.assign(Object.assign({cables:!0},s.custom_configurator_params),s.additional_parameters),null,null,null,null,null);break;default:throw new Error("Nieznany typ geometrii!")}if(o){if(!i){const e=s.shelf_type===p.VENEER_T01,t=((e,t)=>{let s=0;for(var i=0;i<parseInt(e);i++)s+=parseInt(t[i]);return new Rows(e,t)})(s.row_amount,s.row_heights),i=B(n,t,9),o=j(i,s.width,9),r=W(o,t,s.width,9,s.depth);n.additional_elements=Object.assign(Object.assign({},n.additional_elements),r),n.physical_product_version=d,n.furniture_type=f,e&&(n.additional_elements.shelf_type=p.VENEER_T01)}}else n=y(n),n=function(e,s,i,n){const o={geometry:{},width:s,height:i,depth:n};return Object.keys(t).forEach(s=>{o.geometry[s]=e.filter(e=>e.type===t[s])}),o}(n,s.width,s.height,s.depth),function(e,t,s){if(e.geom_type=t.geom_type,"mesh"===t.geom_type&&(e.density_mode=s.mesh[t.geom_id].parameters.density_mode,e.distortion_mode=s.mesh[t.geom_id].parameters.distortion_mode,s.mesh[t.geom_id].constants)){const i=s.mesh[t.geom_id].constants["#object_type"];[0,1,2].includes(i)&&(e.shelf_type=i),e.pattern=s.mesh[t.geom_id].constants.pattern}if("mesh"===t.geom_type||"container"===t.geom_type){const s=t.additional_parameters.collection_type;e.physical_product_version=s===h?u:d,e.furniture_type=s===_?m:f}null!=t.geom_line&&(e.shelf_type=t.geom_line),t.material&&(e.material=t.material),e.configurator_params=t}(n,s,e);return n}const nt=function(e,s,n,o=null,r=!1){if(!s.serialization.hasOwnProperty("configurator_data"))return null;const a=e.components.find(e=>e.m_config_id==n);if(!a)return[];const{table_id:l,series_id:c,x1:h,x2:_,y1:p,y2:d,z1:u,z2:f,door_flip:m,cables:g}=a;let y={},x=d-p;o&&(x=o.height),s.serialization.configurator_data.table[l][x].forEach(({component_id:e,series_id:t,series_name:s,series_groups:i,order:n,inconsequent:o})=>!(c===t||!o)||(!(!y.hasOwnProperty(e)||c===t)||void(y[e]={component_id:e,series_id:t,series_name:s,series_groups:i,order:n,inconsequent:o})));let b=_-h,E=f-u,S={door_flip:m,cables:g};const T=O(e)[n],A=function(e,s,n,o,r){const{serialization:a}=e;let l=a.serialization?a.serialization:a,c={},h="Sideboard";"mesh"===a.superior_object_type&&(h=a.superior_object_collection);return Object.values(s).forEach(e=>{let s,a={width:n,depth:o,geom_id:e.component_id,geom_type:"component",generate_thumbnails:!1,custom_configurator_params:r,additional_parameters:{collection_type:h}},_=it(l,a);s="Wardrobe"===h?Le(_):we(_,"Wardrobe"===h);let p=[];const d=s.components[0];d.door_flippable&&p.push("door_flippable"),d.cables_available&&p.push("cables_available");let u=function(e,t=0){let s=0;const n=e=>{let t=0;return e&&("double"===e.door_parity?t+=(e.x2-e.x1)*(e.y2-e.y1):t+=(e.x2-e.x1+i.MAT_THICKNESS)*(e.y2-e.y1+i.MAT_THICKNESS)),t};return s+=e.geometry.doors.map(e=>n(e)).reduce((e,t)=>e+t,0),s+=e.geometry.drawers.map(e=>n(e)).reduce((e,t)=>e+t,0),s+t}(s,function(e,t=0){let s=0;return s+=50*e.geometry.inserts_horizontal.length,s+=50*e.geometry.inserts_vertical.length,s+=300*e.geometry.doors.length,s+=704*e.geometry.drawers.length,s+t}(s)),f=function(e,s=!0){let n="";return e.hasOwnProperty("linesX")&&e.hasOwnProperty("linesY")&&s&&(n+=e.linesX.reduce((e,t)=>e+" "+t,"X")+"\n",n+=e.linesY.reduce((e,t)=>e+" "+t,"Y")+"\n"),Object.entries(e.geometry).filter(e=>!e[0].includes("shadows")&&e[1].length>0&&"p1"in e[1][0]).forEach(e=>{const s=t[e[0]];n+=e[1].reduce((e,t)=>{let s=`${e} ${t.p1.i}:${t.p3.i}`;return"Ih"===e&&(s+="|"+(t.y1+i.MAT_THICKNESS/2)),"Iv"===e&&(s+="|"+(t.x1+i.MAT_THICKNESS/2)),s},s)+"\n"}),n}(s,!1);c[""+e.component_id]=Object.assign(Object.assign({},e),{cleanGeometry:s,strGeometry:f,areaScore:u,availableOptions:p})}),c}(s,y,b,E,S);if("Wardrobe"===s.serialization.superior_object_collection)return ot(b,A,T,r,!0);return ot(b,function(e){let t=[],s=[],i=[];return Object.entries(e).forEach(e=>{let n=e[1].strGeometry;t.includes(n)?i.push(e[0]):(t.push(n),s.push(e[0]))}),i.forEach(t=>{let i=e[t],n=s.findIndex(t=>e[t].strFormat===i.strFormat);e[s[n]].inconsequent&&!i.inconsequent&&s.splice(n,1,t)}),s.map(t=>e[t])}(A),T,r,!1)};function ot(e,t,s,i=!1,n=!1){let o=[];return Object.values(t).sort((e,t)=>e.areaScore-t.areaScore).forEach(({component_id:t,series_id:r,series_name:a,series_groups:l,order:c,availableOptions:h,cleanGeometry:_})=>{let p=function(e){const t={600:3,700:2.6,800:2.3,900:2,1e3:1.7},s=Math.max(e.height,e.width),i=100*Math.ceil(s/100);let n=1.7;t.hasOwnProperty(i.toString())?n=t[i.toString()]:i<600&&(n=3);return[0,n]}(_);o.push({component_id:t,series_id:r,series_name:a,series_groups:l,order:c,render:!0,availableOptions:h,thumbnail:rt(_,!0,p,e,!0,i,n),boundingBox:s,format:Y(_)})}),o}const rt=(e,t=!0,s=[0,1.5],i=550,n=!0,o=!1,r=!1)=>{let a=null;if(e.temporaryfix){let t,s={width:i,geom_id:e.id,geom_type:"component",generate_thumbnails:!1,custom_configurator_params:null},n=it(e.serialization,s);t=r?we(n,r):Le(n),a=Y(t)}else a=Y(e);let l=(e,t,s)=>(e-t[0])*(s[1]-s[0])/(t[1]-t[0])+s[0],c=[0,1200],h={lines:[],backs:[],fronts:[]},_={keysPoly:{horizontals:5,verticals:7},keysPolyInserts:{inserts:8},keysRect:{backs:1,doors:2,supports:3,drawers:4}},p=Object.keys(_.keysPoly).concat(Object.keys(_.keysPolyInserts)).concat(Object.keys(_.keysRect)),d=[];Object.entries(a).filter(e=>p.includes(e[0])).forEach(e=>{e[1].forEach(e=>{d.push(Math.max(e.y1,e.y2))})});let u=Math.max(...d)/2,f=Object.entries(a).filter(e=>Object.keys(_.keysPoly).includes(e[0])),m=Object.entries(a).filter(e=>Object.keys(_.keysPolyInserts).includes(e[0])),g=Object.entries(a).filter(e=>Object.keys(_.keysRect).includes(e[0]));return f.forEach(e=>{e[1].forEach(i=>{let n={geom:[[i.x1+0,i.y1-u],[i.x2+0,i.y2-u]],color:_.keysPoly[e[0]],mode:0};!0===t&&(n.geom[0]=n.geom[0].map(e=>l(e,c,s)),n.geom[1]=n.geom[1].map(e=>l(e,c,s))),h.lines.push(n)})}),m.forEach(e=>{e[1].forEach(i=>{let n,o,r,a;i.x2-i.x1!=18?(n=i.x1,o=i.x2,r=(i.y1+i.y1)/2-u,a=r-u):(n=(i.x1+i.x2)/2,o=n,r=i.y1-u,a=i.y2-u);let p={geom:[[n+0,r],[o+0,a]],color:_.keysPolyInserts[e[0]],mode:0};!0===t&&(p.geom[0]=p.geom[0].map(e=>l(e,c,s)),p.geom[1]=p.geom[1].map(e=>l(e,c,s))),h.lines.push(p)})}),g.forEach(e=>{let o=e[0];e[1].forEach(e=>{let r;switch(r=!0!==n||"doors"!==o&&"drawers"!==o&&"backs"!==o?{geom:[[e.x1+0,e.y1-u],[e.x2+0,e.y1-u],[e.x2+0,e.y2-u],[e.x1+0,e.y2-u]]}:ct({elemType:o,elem:e,componentSize:[i,u]}),r.color=_.keysRect[o],r.mode=2,!0===t&&(r.geom[0]=r.geom[0].map(e=>l(e,c,s)),r.geom[1]=r.geom[1].map(e=>l(e,c,s)),r.geom[2]=r.geom[2].map(e=>l(e,c,s)),r.geom[3]=r.geom[3].map(e=>l(e,c,s))),o){case"doors":case"drawers":h.fronts.push(r);break;case"backs":case"supports":h.backs.push(r)}})}),o?at(h):[...h.fronts,...h.lines,...h.backs]},at=e=>{let t={};return Object.keys(e).forEach(s=>{t[s]=[],e[s].forEach(e=>{let i={};e.geom.forEach((e,t)=>{let s={};for(let t=0;t<e.length;t++)s[""+lt(t)]=Math.floor(1e5*e[t]);i["p"+(t+1)]=s}),t[s].push({geom:i})})}),t},lt=e=>{switch(e){case 0:return"x";case 1:return"y"}},ct=e=>{let t={},s=(e.componentSize[0],e.componentSize[1]);if("drawers"===e.elemType){let i=25,n=45;t={geom:[[e.elem.x1-n,e.elem.y1-s-i],[e.elem.x2+n,e.elem.y1-s-i],[e.elem.x2+n,e.elem.y2-s-i],[e.elem.x1-n,e.elem.y2-s-i]]}}else if("doors"===e.elemType){let i,n=e.elem.flip,o=45,r=15,a=0,l=e.elem.x2-e.elem.x1;i=l<200?100:l<400?150:200,t=1===n?{geom:[[e.elem.x1,e.elem.y1-s-r],[e.elem.x1+i-a,e.elem.y1-s-o-r],[e.elem.x1+i-a,e.elem.y2-s-o+r],[e.elem.x1,e.elem.y2-s+r]]}:{geom:[[e.elem.x2-i+a,e.elem.y1-s-o-r],[e.elem.x2,e.elem.y1-s-r],[e.elem.x2,e.elem.y2-s+r],[e.elem.x2-i+a,e.elem.y2-s-o+r]]}}else if("backs"===e.elemType){let i=30;t=e.elem.cable_up?{geom:[[e.elem.x1,e.elem.y1-s],[e.elem.x2,e.elem.y1-s],[e.elem.x2,e.elem.y2-s-i],[e.elem.x1,e.elem.y2-s-i]]}:e.elem.cable_down?{geom:[[e.elem.x1,e.elem.y1-s+i],[e.elem.x2,e.elem.y1-s+i],[e.elem.x2,e.elem.y2-s],[e.elem.x1,e.elem.y2-s]]}:{geom:[[e.elem.x1,e.elem.y1-s],[e.elem.x2,e.elem.y1-s],[e.elem.x2,e.elem.y2-s],[e.elem.x1,e.elem.y2-s]]}}return t};class Node{constructor(e,t,s,i){this.uid=`${t}|${e}`,this.edges=[],this.posX=e,this.posY=t,this.direction=i,this.isValid=!1,this.isLastLevel=!1,this.level=s,this.slices=this.calculateSlices(s)}visibilityCost(e=0){const t=this.posY-1600,s=Math.pow(t,2)*e;return"UP"===this.direction?this.posY<1600?s:0:"DOWN"===this.direction?this.posY>1600?s:0:"BOTH"===this.direction?-100:void 0}calculateSlices(e){return[{start:e.start,end:this.posX,size:e.size-(this.posX-e.start)},{start:this.posX,end:e.end,size:e.size-(e.end-this.posX)}]}createEdge(e){this.edges.push(e)}draw(e){e&&e(this)}traverse(e){e&&e(this),this.edges.forEach(t=>t.traverse(e))}}class Level{constructor(e,t,s,i,n=!1){this.posY=e,this.start=t,this.end=s,this.size=s-t,this.position=i,this.allValid=n,this.nextLevel=null,this.nodes=[],this.compartmentEdges=[]}get validRange(){this.compartmentEdges.sort((e,t)=>e-t);const e=this.compartmentEdges.length-2;return[this.compartmentEdges[1],this.compartmentEdges[e]]}get validNodes(){return"BOTTOM"===this.position?this.nodes.filter(e=>e.isValid):this.nodes.filter(e=>e.posX>this.validRange[0]&&e.posX<this.validRange[1]&&e.isValid)}get nodesByRange(){this.compartmentEdges.sort((e,t)=>e-t);const e={};return this.compartmentEdges.forEach((t,s)=>{if(0!==s&&s!==this.compartmentEdges.length-2&&s<this.compartmentEdges.length-1){const i=[t,this.compartmentEdges[s+1]],n=`${i[0]}|${i[1]}`;e[n]=this.validNodes.filter(e=>e.posX>i[0]&&e.posX<i[1])}}),e}setNextLevel(e){this.nextLevel=e}addNodes(e,t){e.forEach(e=>{const s=e.x2-e.x1,i=e.x1+s/2;this.addNode(i,t)})}addNode(e,t){"UP"===t&&this.compartmentEdges.push(e);const s=`${this.posY}|${e-9}`;this.isNodesContain(s)?this.updateNode(s,{key:"direction",value:"BOTH"}):this.createNode(e-9,t);const i=`${this.posY}|${e+9}`;this.isNodesContain(i)?this.updateNode(i,{key:"direction",value:"BOTH"}):this.createNode(e+9,t)}createNode(e,t){const s=[e-this.start,this.end-e],i=new Node(e,this.posY,this,t);this.nodes.push(i),(s.every(e=>e<=2400)||this.allValid)&&(i.isValid=!0)}updateNode(e,t){this.nodes.find(t=>t.uid===e)[t.key]=t.value}inRange(e,t){return e>t[0]&&e<t[1]}isNodesContain(e){if(this.nodes.find(t=>t.uid===e))return!0}}class Graph{constructor(e,t=!1){this.origin=e,this.verticalsGeom=e.verticals.sort(this.orderByYX),this.horizontalsGeom=e.horizontals.sort(this.orderByYX),this.doublePath=t,this.rootLevel=null,this.lastLevel=null,this.levels={},this.tempEdges=[]}buildStructure(){this.buildLevels(),this.buildNodes(),this.buildEdges()}buildLevels(){this.horizontalsGeom.forEach((e,t)=>{let s={};s=0===t?new Level(e.y1,e.x1,e.x2,"UP",this.doublePath):t===this.horizontalsGeom.length-1?new Level(e.y1,e.x1,e.x2,"BOTTOM",this.doublePath):new Level(e.y1,e.x1,e.x2,"MIDDLE",this.doublePath),this.createOrUpdate(this.levels,e.y1,s)})}buildNodes(){this.verticalsGeom.forEach(e=>{const t=e.x2-e.x1,s=e.x1+t/2;this.fillLevels(s,e.y2+9,e.y1-9)});const e=Object.keys(this.levels).sort((e,t)=>parseInt(t)-parseInt(e));e.forEach((t,s)=>{if(s<e.length-1){const i=this.levels[t],n=this.levels[e[s+1]];0===s&&(this.rootLevel=this.levels[t]),1===n.length?i.forEach(e=>e.setNextLevel(n)):i.forEach(e=>e.setNextLevel(n.filter(t=>!(t.end<e.start||t.start>e.end))))}else this.lastLevel=this.levels[t],this.levels[t].isLast=!0,this.levels[t].forEach(e=>e.nodes.forEach(e=>e.isLastLevel=!0))})}buildEdges(e){Object.keys(this.levels).forEach(t=>{this.levels[t].forEach(t=>{const s=t.nodesByRange;Object.keys(s).forEach(i=>{const[n,o]=i.split("|"),r=t.nextLevel.filter(e=>e.start<n&&e.end>o);let a={};if(1===r.length)a=r[0];else{a=t.nextLevel.flatMap(e=>e.nextLevel).filter(e=>e.start<n&&e.end>o)[0]}const l=s[i],c=a.nodes.filter(e=>e.posX>n&&e.posX<o&&e.isValid);l.forEach(t=>{c.forEach(s=>{e&&e(t,s),t.createEdge(s),this.tempEdges.push([t,s])})})})})})}getRoots(){return this.rootLevel.flatMap(e=>e.validNodes)}getLastNodes(){return this.lastLevel.flatMap(e=>e.validNodes)}getAllNodes(){return Object.values(this.levels).flatMap(e=>e.flatMap(e=>e.nodes))}findLevel(e,t){return this.levels[t].find(t=>t.start<=e&&e<=t.end)}fillLevels(e,t,s){const i=this.findLevel(e,t),n=this.findLevel(e,s);i.addNode(e,"UP"),n.addNode(e,"DOWN")}orderByYX(e,t){return e.y1>t.y1?-1:e.y1<t.y1||e.x1>t.x1?1:e.x1<t.x1?-1:0}createOrUpdate(e,t,s){e.hasOwnProperty(t)?e[t].push(s):e[t]=[s]}}class AStar{constructor(e,t=0,s=[]){this.root=e,this.lastNodes=s,this.factor=t,this.visitedNodes=[],this.nodesToVisit=[e],this.cameFromMap={},this.gScoreMap={[e.uid]:0},this.finalCostMap={[e.uid]:this.heuristic(e)}}isPathFinished(e){return e.isLastLevel}isNodeVisited(e){return this.visitedNodes.filter(t=>t.uid===e.uid).length>0}isNodeEvaluated(e){return this.nodesToVisit.filter(t=>t.uid===e.uid).length>0}anyNodesToVisit(){return this.nodesToVisit.length>0}findClosestNode(e){let t=null,s=1/0;return this.lastNodes.forEach(i=>{const n=Math.abs(i.posX-e.posX);s>n&&(s=n,t=i)}),t}heuristic(e){const t=this.findClosestNode(e);return Math.sqrt(Math.pow(t.posX-e.posX,2)+Math.pow(t.posY-e.posY,2))+e.visibilityCost(this.factor)}distance(e,t){return Math.abs(t.posX-e.posX)}getNodeWithLowestCost(){let e=0;return this.nodesToVisit.forEach((t,s)=>{const i=this.finalCostMap[this.nodesToVisit[e].uid];this.finalCostMap[t.uid]<i&&(e=s)}),{idx:e,node:this.nodesToVisit[e]}}constructPath(e){const t={nodes:[e],finalCost:this.finalCostMap[e.uid],isSuccess:!0};let s=e.uid;for(;s in this.cameFromMap;){let e=this.cameFromMap[s];t.nodes.push(e),t.finalCost+=this.finalCostMap[e.uid],s=e.uid}return t}searchPath(){for(;this.anyNodesToVisit();){let e=this.getNodeWithLowestCost();if(this.isPathFinished(e.node))return this.constructPath(e.node);this.nodesToVisit.splice(e.idx,1),this.visitedNodes.push(e.node),e.node.edges.forEach(t=>{if(this.isNodeVisited(t))return;const s=this.gScoreMap[e.node.uid]+this.distance(e.node,t);if(this.isNodeEvaluated(t)){s<this.gScoreMap[t.uid]&&(this.gScoreMap[t.uid]=s)}else this.gScoreMap[t.uid]=s,this.nodesToVisit.push(t);this.cameFromMap[t.uid]=e.node,this.finalCostMap[t.uid]=this.gScoreMap[t.uid]+this.heuristic(t)})}return{nodes:[],finalCost:1/0,isSuccess:!1}}}class SimpleDivide{constructor(e,t=!1){this.verticalsGeom=e.verticals.sort(this.orderByYX),this.horizontalsGeom=e.horizontals.sort(this.orderByYX),this.legsGeom=e.long_legs&&e.long_legs.length?e.long_legs.sort(this.orderByYX):null,this.allLevels=[],this.isColumnConfiguration=t,this.start=this.horizontalsGeom[0].x1,this.end=this.horizontalsGeom[0].x2,this.size=this.end-this.start}search(){const e={};this.verticalsGeom.forEach(t=>{this.createOrUpdate(e,t.x1-9,[t.x1-9,t.y2+9,t.y1-9]),this.createOrUpdate(e,t.x1+9,[t.x1+9,t.y2+9,t.y1-9])});const t=[];if(this.isColumnConfiguration){const s=this.horizontalsGeom.sort((e,t)=>parseInt(e.y1)-parseInt(t.y1)),i=[...new Set(s.map(e=>parseInt(e.y1)))],n=i[0],o=i[i.length-1],r={},a=(e,t,s)=>{t===s.y1&&r[s.y1].push(parseInt(e))};s.forEach((t,s)=>{s>0&&(r[t.y1]=[],Object.keys(e).forEach(s=>e[s].forEach(e=>a(s,e[1],t))),r[t.y1].sort((e,t)=>e-t))}),Object.keys(e).forEach(i=>{const r=e[i];let a=1/0,l=!0,c=1/0;r.forEach((e,t)=>{s.filter(t=>((e,t)=>(e.x1+18===t[0]||e.x2-18===t[0])&&((e,t)=>e.y1===t[1]||e.y1===t[2])(e,t))(t,e)).length||(0===t&&(c=e[1]),t===r.length-1&&(a=e[2]),t<r.length-1&&e[2]!==r[t+1][1]&&(l=!1))}),a===n&&c===o&&l&&t.push(r)})}else{const s=Object.keys(e).sort((e,t)=>parseInt(e)-parseInt(t)),i=this.horizontalsGeom.length-1;s.forEach(s=>{e[s].length===i&&t.push(e[s])})}t.sort((e,t)=>e[0][0]-t[0][0]);const s=[];return t.forEach((e,t)=>{const i=e[0][0],n=[i-this.start,this.end-i].every(e=>e<=2400);this.buildPath(s,e,n)}),s.length?this.choosePaths(s,!1):(t.forEach((e,t)=>{this.buildPath(s,e,!0)}),this.choosePaths(s,!0))}buildPath(e,t,s=!1){const i={nodes:[],isSuccess:!1};s&&(t.forEach((e,s)=>{const n={posX:e[0],posY:e[1],uid:`${e[1]}|${e[0]}`};i.nodes.push(n),s===t.length-1&&i.nodes.push({posX:e[0],posY:e[2],uid:`${e[2]}|${e[0]}`})}),i.isSuccess=!0,e.push(i))}choosePaths(e,t=!1){let s=e;if(this.legsGeom){const t=e=>this.legsGeom.some(t=>Math.abs(t.x1-e.nodes[0].posX)<36);s=e.filter(t)}s.length||(s=e);const i=this.size/2,n=this.start+i;let o=1/0,r=null;return s.forEach(e=>{const t=e.nodes[0].posX;Math.abs(n-t)<o&&(o=Math.abs(n-t),r=e)}),t?this.chooseTwoPaths(e):{allPaths:s,bestPaths:[r.nodes]}}chooseTwoPaths(e=[]){const t=this.size/3,s=this.start+t,i=this.end-t;let n=1/0,o=1/0,r=null,a=null;return e.forEach(e=>{const t=e.nodes[0].posX;Math.abs(s-t)<n&&(n=Math.abs(s-t),r=e),Math.abs(i-t)<o&&(o=Math.abs(i-t),a=e)}),{allPaths:e,bestPaths:[r.nodes,a.nodes]}}orderByYX(e,t){return e.y1>t.y1?-1:e.y1<t.y1||e.x1>t.x1?1:e.x1<t.x1?-1:0}createOrUpdate(e,t,s){e.hasOwnProperty(t)?e[t].push(s):e[t]=[s]}}const ht=e=>Math.abs(e.x2-e.x1)>2400,_t=(e,t)=>new SimpleDivide(e,t).search(),pt=(e,t=0)=>{const s=dt(e),i=ut(s,t),n=mt(i);if(i.length)return{graph:s,allPaths:i,bestPaths:[n]};const o=dt(e,!0),r=ut(o,t),a=o.rootLevel[0],l=a.start+a.size/2;return{graph:o,allPaths:r,bestPaths:ft(r,l)}},dt=(e,t=!1)=>{const s=new Graph(e,t);return s.buildStructure(),s},ut=(e,t)=>{let s=[];const i=e.getLastNodes();return i.length&&e.getRoots().forEach(e=>{const n=new AStar(e,t,i).searchPath();n.isSuccess&&s.push(n)}),s},ft=(e,t)=>{let s=1/0,i={},n=1/0,o={};return e.forEach(e=>{let r=-1/0;e.nodes.forEach(e=>{e.posX>r&&(r=e.posX)}),r<t?e.finalCost<s&&(s=e.finalCost,i=e.nodes):e.finalCost<n&&(n=e.finalCost,o=e.nodes)}),[i,o]},mt=e=>0===e.length?[]:e.sort((e,t)=>e.finalCost-t.finalCost)[0].nodes,gt=(e,t=[])=>{const s=[...e.horizontals];t.forEach(e=>{e.forEach(e=>{const t=s.findIndex(t=>t.y1===e.posY&&t.x1<e.posX&&t.x2>e.posX);if(t>-1){const i=Object.assign({},s[t]);i.x2=e.posX;const n=Object.assign({},s[t]);n.x1=e.posX,s.splice(t,1,i,n)}})}),e.horizontals=s};class GeometryProvider{constructor(){this.dnaToolsAdapterMode=!1,this.setShelfSplitterEnabled(!0),this.setPerformanceLogEnabled(!0)}get lastPresetParameters(){return this._lastPresetParameters}get raw(){return this._raw}get clean(){return this._clean}get format(){return this._format}get final(){return this._final}get thumbnails(){return this._thumbnails}get splitResult(){return this._splitResult}get performanceLog(){return this.buildPerformanceLog()}setShelfSplitterEnabled(e){this._shelfSplitterEnabled=e,this._splitResult=e?{}:null}setPerformanceLogEnabled(e){this._performanceLogEnabled=e,this._performanceLog=e?{}:null}calculateGeometry(e){switch(this.performanceStamp("calc-start"),e.type){case K.GRASSHOPPER_BOOKCASE:this.calculateGrassGeometry(e.dna,e.parameters);break;case K.CAPE_BOOKCASE:case K.CAPE_LOWBOARD:case K.CAPE_SIDEBOARD:this.calculateLowBoardGeometry(e.dna,e.parameters);break;case K.CAPE_WARDROBE:this.calculateWardrobeGeometry(e.dna,e.parameters)}this.performanceStamp("calc-end"),this._lastPresetParameters=e.parameters}calculateLowBoardGeometry(e,t){this._raw=it(e,t,this.dnaToolsAdapterMode),this._clean=we(this._raw),this._format=Y(this._clean,!this.dnaToolsAdapterMode),this.calclulateSplit("GRID",!0),this._final=new GalleryFormat(this._format)}calculateWardrobeGeometry(e,t){this._raw=it(e,t),this._clean=Le(this._raw),this._format=Y(this._clean),this._final=new WardrobeGalleryFormat(this._format)}calculateGrassGeometry(e,t){this._format=it(e,t,this.dnaToolsAdapterMode),this.calclulateSplit(t.geom_type,!1),this._final=this._format}calculateThumbnails(e,t,s){let i={serialization:t.dna};this._thumbnails=nt(this._clean,i,e,t.parameters,s)}flagThumbnailsForRender(e,t){this._thumbnails&&this._thumbnails.length&&this._thumbnails.forEach(s=>{e(s)&&(s.render=t)})}calclulateSplit(e,t){this._shelfSplitterEnabled&&(this.performanceStamp("split"),this._splitResult=((e,t,s=!1)=>{let i={};return e.horizontals.some(ht)&&(i="GRID"===t||"GRADIENT"===t?_t(e,s):pt(e,"SLANT"===t?.01:.001),gt(e,i.bestPaths)),i})(this._format,e,t))}performanceStamp(e){this._performanceLogEnabled&&(this._performanceLog[e]=Date.now())}buildPerformanceLog(){const e={decoderCalc:0,splitCalc:0};if(this._performanceLogEnabled){const t=(e,t)=>(this._performanceLog[t]-this._performanceLog[e])/100;e.decoderCalc=t("calc-start","calc-end"),e.splitCalc=t("split","calc-end")}return e}}class FurnitureProjectManager{constructor(){this._geometryProvider=new GeometryProvider,this._presetBuilder=new PresetBuilder}withShelfSplitter(e){return this._geometryProvider.setShelfSplitterEnabled(e),this}withPerformanceLog(e){return this._geometryProvider.setPerformanceLogEnabled(e),this}withShelfId(e){return this._presetBuilder.setShelfId(e),this}withDNA(e){return this._presetBuilder.setDNA(e),this}getPresetFromSerialization(e){return this._presetBuilder.loadFromDNA(e)}getPresetFromJetty(e){return this._presetBuilder.loadFromJetty(e)}calculateAndGetGeometry(e){const t=this._presetBuilder.createPreset(e);return this._geometryProvider.calculateGeometry(t),this._geometryProvider.final}calculateAndGetThumbnails(e,t){const s=this._presetBuilder.createPreset(e);return this._geometryProvider.thumbnails&&this._geometryProvider.thumbnails.length?this.tryGetThumbnailsFromCache(s,t):this._geometryProvider.calculateThumbnails(t,s,!1),this._lastParameters=s.parameters,this._lastActiveMcfgId=t,this._geometryProvider.thumbnails}calculateAndGetThumbnailsForIOS(e,t){const s=this._presetBuilder.createPreset(e);return this._geometryProvider.calculateThumbnails(t,s,!0),this._geometryProvider.thumbnails}getSplitResult(){return this._geometryProvider.splitResult}getPerformanceLog(){return this._geometryProvider.performanceLog}getGlobalUIConfigParams(){return function(e,t){const s=(e.serialization.serialization?e.serialization.serialization:e.serialization).mesh[t].parameters;let n=s.size_x,o={variable:"width",label:"Width",default:1500,min:n[0],max:n[1]},r=s.size_y,a={variable:"height",label:"Height",plus:{matWithPlinth:(i.SIDEBOARD_FINAL_HEIGHT_OFFSET+i.PLINTH_SIZE)/10,mat:i.SIDEBOARD_FINAL_HEIGHT_OFFSET/10},default:r[0],min:r[0],max:r[r.length-1],steps:r},l={variable:"height",label:"Height",default:2400,options:[{value:2e3,label:"200cm"},{value:3600,label:"360cm"}]},c={variable:"depth",label:"Depths",default:400};e.superior_object_collection===h?c.options=[{value:400,label:"40cm"}]:e.superior_object_collection===_?(c.default=530,c.options=[{value:530,label:"53cm"},{value:630,label:"63cm"}]):c.options=[{value:240,label:"24cm"},{value:320,label:"32cm"},{value:400,label:"40cm"}];let d={widthSlider:o,heightSlider:a,depthToogle:c,shelfBaseToogle:{avialable:"Sideboard"===e.superior_object_collection,variable:"plinth",type:s.object_type===p.TYPE02?"LONG_LEGS":"PLINTH",default:!0,options:[{value:!1,label:"Feet"},{value:!0,label:"Plinth"}]}};return e.superior_object_collection===_&&(d={widthSlider:o,heightToggle:l,depthToogle:c}),e.uiConfigParams=d,d}(this._presetBuilder.DNA,this._presetBuilder.shelfId)}getUIAvailableModifiersConfig(){return function(e,t){const s=(e.serialization.serialization?e.serialization.serialization:e.serialization).mesh[t].parameters;return{widthSliderAvailable:a.ALL.includes(e.superior_object_collection),heightSliderAvailable:a.WITH_STEP_HEIGHT.includes(e.superior_object_collection),heightToogleAvailable:a.WITH_HEIGHT_TOGGLE.includes(e.superior_object_collection),depthToogleAvailable:a.WITH_DEPTH_MOD.includes(e.superior_object_collection),flipDoorToggleAvailable:a.WITH_STEP_HEIGHT.includes(e.superior_object_collection),cablesToggleAvailable:"Sideboard"===e.superior_object_collection,localEdgeAvailable:"edge"===s.distortion_mode,densityStepperAvailable:"setup_range_stepper"===s.density_mode,distortionSliderAvailable:["gradient","ratio"].includes(s.distortion_mode)}}(this._presetBuilder.DNA,this._presetBuilder.shelfId)}getRelativeUIConfigParams(e){return function(e,t){let s={};const n=(e.serialization.serialization?e.serialization.serialization:e.serialization).mesh[t.configurator_params.geom_id].parameters,o=n.density_mode,r=n.distortion_mode,a=t.configurator_params.plinth?i.PLINTH_SIZE:0,l=a+i.SIDEBOARD_FINAL_HEIGHT_OFFSET;let c=n.size_y.map(e=>e+l);if(s.heightSlider={variable:"height",label:"Height",default:c[0],range:{min:c[0],max:c[c.length-1]},steps:c},"setup_range_stepper"===o||"edge"===r){if("edge"===r&&t.lines){s.lines=Object.keys(t.lines);const e=t.components.reduce((e,t,s,i)=>Object.assign(e,{[t.channel_id]:t}),{});let i={};Object.keys(t.lines).forEach(s=>{const n=s.split("_").map(e=>parseInt(e)),o=t.lines[s],r={variable:s,label:"Local Edge",state:o.value,min:o.v_min,max:o.v_max,x:o.x,y:o.y+a,z:t.configurator_params.depth,left:o.x-o.value+o.v_min,right:o.x-o.value+o.v_max,bottom:18+a,top:t.configurator_params.height+a};n.forEach(t=>{const n=e[t],o=["LEFT","RIGHT"][[n.line_left,n.line_right].indexOf(s)];i.hasOwnProperty(n.m_config_id)?i[n.m_config_id].push(Object.assign(Object.assign({},r),{side:o})):i[n.m_config_id]=[Object.assign(Object.assign({},r),{side:o})]})}),s.local_edge_distortion=i}if("setup_range_stepper"===o&&t.density_options&&t.density_options.length>0){let e={variable:"density",label:"Columns",options:[]};t.density_options.forEach(t=>{e.options.push({value:t[0],label:t[1]})}),s.density_stepper=e}}return s}(this._presetBuilder.DNA,this._geometryProvider.format)}getLocalEdgeConfigParams(e){return function(e,t=null){const s=e.components.reduce((e,t,s,i)=>Object.assign(e,{[t.channel_id]:t}),{}),i={},n=e.configurator_params.plinth?100:0;return Object.keys(e.lines).forEach(t=>{const o=e.lines[t],r={variable:t,label:"Local Edge",state:o.value,min:o.v_min,max:o.v_max,x:o.x,y:o.y+n,z:e.configurator_params.depth,left:o.x-o.value+o.v_min,right:o.x-o.value+o.v_max,bottom:18+n,top:e.configurator_params.height+n};t.split("_").map(e=>parseInt(e)).forEach(e=>{const n=s[e],o=n.m_config_id,a=["LEFT","RIGHT"][[n.line_left,n.line_right].indexOf(t)];i.hasOwnProperty(o)?i[o].push(Object.assign(Object.assign({},r),{side:a})):i[o]=[Object.assign(Object.assign({},r),{side:a})]})}),t?i[t]:i}(this._geometryProvider.format,e)}getLocalUIConfigParams(){return function(e){let t={};return e.components&&(e.furniture_type===f?e.components.forEach(e=>{t[e.m_config_id]={flip_door_toggle:{available:!1,state:e.door_flip,variable:"door_flip",label:"Doors direction",options:[{value:"left",label:"Left"},{value:"right",label:"Right"}]},cables_toggle:{available:!1,state:e.cables,variable:"cables",label:"Cable opening",options:[{value:!1,label:"Off"},{value:!0,label:"On"}]}},e.door_flippable&&(t[e.m_config_id].flip_door_toggle.available=!0,t[e.m_config_id].flip_door_toggle.state=e.door_flip),e.cables_available&&(t[e.m_config_id].cables_toggle.available=!0,t[e.m_config_id].cables_toggle.state=e.cables)}):t[c.m_config_id]={flip_door_toggle:{available:!0,state:"right",variable:"doors_direction",label:"Doors direction",options:[{value:"left",label:"Left"},{value:"right",label:"Right"}]}}),t}(this._geometryProvider.format)}getDimensions(e,t,s,i){return function(e,t="all_dimension",s=0,i=0,n=null){n&&(n=parseInt(n));let o=[];const r=e.components.find(e=>e.m_config_id===n);switch(t){case"all_dimension":return o=e.components.map(e=>e.compartments).flat(),T(o,"all",s,i);case"component":return r?(o=r.compartments,T(o,"all",s,i)):{height:[],width:[]};case"local_edge_left":if(r&&r.line_left){const t=e.components.indexOf(r);return o=[t-1,t].map(t=>e.components[t]).map(e=>e.compartments).flat(),T(o,"width",s,i)}return{height:[],width:[]};case"local_edge_center":return r?(o=r.compartments,T(o,"width",s,i)):{height:[],width:[]};case"local_edge_right":if(r&&r.line_right){const t=e.components.indexOf(r);return o=[t,t+1].map(t=>e.components[t]).map(e=>e.compartments).flat(),T(o,"width",s,i)}return{height:[],width:[]}}}(this._geometryProvider.format,e,t,s,i)}getBoundingBoxForMesh(){return S(this._geometryProvider.format)}getIdentifierBoxes(){return(e=>{const t=e.final,s=e.lastPresetParameters,n=s.row_amount||s.rows,o=[];if(n){const e=[...new Set(t.horizontals.map(e=>e.y1))],n=t.horizontals.flatMap(e=>[e.x1,e.x2]),r=Math.min(...n)-i.MAT_THICKNESS/2,a=Math.max(...n)+i.MAT_THICKNESS/2,l=0,c=s.depth;for(let t=0;t<e.length-1;t++){const s=e[t]-i.MAT_THICKNESS/2,n=e[t+1]+i.MAT_THICKNESS/2,h=new PositionBox({x1:r,x2:a,y1:s,y2:n,z1:l,z2:c});o.push(h)}}else t.components.forEach(e=>{const t=new PositionBoxWithParent(Object.assign(Object.assign({},e),{x1:e.x1-30,x2:e.x2+30,y1:e.y1-30,y2:e.y2+30,z1:e.z1-30,z2:e.z2+30}));o.push(t)});return o})(this._geometryProvider)}getMainBoundingBox(){return(e=>{const t=e.final,s=e.lastPresetParameters;if(s.row_amount||s.rows){const e=t.horizontals.flatMap(e=>[e.x1,e.x2]),n=t.horizontals.flatMap(e=>[e.y1,e.y2]),o=Math.min(...e)-i.MAT_THICKNESS/2,r=Math.max(...e)+i.MAT_THICKNESS/2,a=Math.min(...n)-i.MAT_THICKNESS/2,l=Math.max(...n)+i.MAT_THICKNESS/2,c=0,h=s.depth;return new PositionBox({x1:o,x2:r,y1:a,y2:l,z1:c,z2:h})}{let e=0,n=0,o=0,r=0;if(t.horizontals){const s=t.horizontals.flatMap(e=>[e.x1,e.x2]),a=t.horizontals.flatMap(e=>[e.y1,e.y2]);e=Math.min(...s)-i.MAT_THICKNESS/2,n=Math.max(...s)+i.MAT_THICKNESS/2,o=Math.min(...a)-i.MAT_THICKNESS/2,r=Math.max(...a)+i.MAT_THICKNESS/2}else{const s=t.walls.flatMap(e=>[e.x1,e.x2]),a=t.walls.flatMap(e=>[e.y1,e.y2]),l=i.MAT_THICKNESS/2+i.FRAME_THICKNESS;e=Math.min(...s)-l,n=Math.max(...s)+l,o=Math.min(...a),r=Math.max(...a)+i.FRAME_THICKNESS}return new PositionBox({x1:e,x2:n,y1:o,y2:r,z1:0,z2:s.depth})}})(this._geometryProvider)}tryGetThumbnailsFromCache(e,t){const s=PresetUtils.compareConfigStates(this._lastParameters,e.parameters),i=this._lastActiveMcfgId===t;this._geometryProvider.calculateThumbnails(t,e,!1),s.isEqual||(s.diff.has("channel_doorFlip")&&i&&this._geometryProvider.flagThumbnailsForRender(e=>!e.availableOptions.includes("door_flippable"),!1),s.diff.has("channel_cables")&&i&&this._geometryProvider.flagThumbnailsForRender(e=>!e.availableOptions.includes("cables_available"),!1),s.diff.has("channel_seriesId")&&i&&this._geometryProvider.flagThumbnailsForRender(e=>!0,!1))}buildConfigurationState(e,t,s){const{channel_id:i}=this._geometryProvider.final.components[e],n=this._presetBuilder.loadFromGeometry(this._geometryProvider.final,e);return this._presetBuilder.updateLocalConfig(n,i,t,s)}static dnaToolsAdapter(){const e=new FurnitureProjectManager,t={get_elements:function(t,s,i,n,o,r,a,l,c,h,_,p,d,u=[],f=0){e.withDNA(t);const m={motion:s,width:i,row_amount:n,row_heights:o,row_styles:r,depth:a,half_material:l,support_size:c,snapping:h,shadow_settings:_,gen_row_styles_for_buttons:p,styles_slider:d,backpanels_rows:u,shelf_type:f};return e._geometryProvider.dnaToolsAdapterMode=!0,e.calculateAndGetGeometry(m)}};if(e._presetBuilder.isBookcaseShefl){return Object.assign({generate_elements_simplified_data:function(e,t,s){return B(e,t,s)},get_shadows_openings:function(e,t,s){return j(e,t,s)},get_shadows_geometry:function(e,t,s,i,n,o){return W(e,t,s,i,n,o)},generate_legs:function(e,t,s){return H(e,t,s)}},t)}return t}}export default class Middleware extends FurnitureProjectManager{constructor(e){super()}calculateAndGetGeometry(e){return super.calculateAndGetGeometry(e)}}
