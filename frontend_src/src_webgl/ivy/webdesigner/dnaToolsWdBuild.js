import {
    BAC<PERSON>,
    COMPONENT,
    DO<PERSON>,
    DOOR_F,
    DRAWER,
    ELEM_TYPE_CODENAMES,
    FRONT,
    HORIZONTAL,
    INSERT_H,
    INSERT_V,
    ERASER_H,
    ERASER_V,
    isString,
    MARK,
    MAT_THICKNESS, // TODO: <PERSON><PERSON><PERSON><PERSON> się tej zmiennej z tego modułu (to ustawienia elementu)
    OPENING,
    PLINTH,
    SHADOW_INNER,
    SHADOW_OUTER,
    smartLog,
    SPACER,
    SUPPORT,
    SUPPORT_WIDTH, // TODO: <PERSON><PERSON><PERSON>ć się tej zmiennej z tego modułu (to ustawienia elementu)
    VERTICAL,
    TOP_BOX,
    FRONT_BOX
} from './dnaToolsWdUtils';

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- DEKLARACJE STAŁYCH ---- ///////////////////////////////////////////////////////////////////////////

const CONSTANTS = {
    '#aa': 100,
    '#a': 200,
    '#b': 300,
    '#c': 400,
    '#d': 500,
    '#e': 600,
    '#f': 700,
    '#g': 800,
    '#h': 900,
    '#i': 1000,
    '#j': 1100,
    '#height_aa': 100,
    '#height_a': 200,
    '#height_b': 300,
    '#height_c': 400,
    '#height_d': 500,
    '#height_e': 600,
    '#height_f': 700,
    '#height_g': 800,
    '#height_h': 900,
    '#height_i': 1000,
    '#height_j': 1100,
    '#default_back': 0,
    '#mat': 18,
    '#insert_thickness': 18,
    '#support_width': 125,
    '#plinth_height': 90,
    '#elemDoor': 283,
    '#elemDrawer': 282,
    '#elemOpen': 285,
};

const ratioSymbols = ['t', 'u', 'v', 'w', 'x', 'y', 'z'];

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- FUNKCJE POMOCNICZE ---- ///////////////////////////////////////////////////////////////////////////

// const invalidGeometryPreview = function buildReplacementForGeometryWithIncorrectParameters (
// x, y, z, message='', log='', sx=0, sy=0, sz=0
// ){
//     let [mat, a, b] = [MAT_THICKNESS/2, x*0.25, x*0.75];
//     if (log !== ''){console.log('!!!!!!!!! ERROR GEOM', log);}
//     return [
//                 // 'horizontals'
//                 {'y1':sy+mat,'y2':sy+mat,'x2':sx+x,'x1':sx,'z1':sz+z,'z2':sz, 'type': HORIZONTAL},
//                 {'y1':sy+y-mat,'y2':sy+y-mat,'x2':sx+x,'x1':sx,'z1':sz+z,'z2':sz, 'type': HORIZONTAL},
//                 // 'verticals'
//                 {'y1':sy+mat*2,'y2':sy+y-mat*2,'x2':sx+a,'x1':sx+a,'z1':sz+z,'z2':sz, 'type': VERTICAL},
//                 {'y1':sy+mat*2,'y2':sy+y-mat*2,'x2':sx+b,'x1':sx+b,'z1':sz+z,'z2':sz, 'type': VERTICAL},
//                 // 'shadows'
//                 {'y1':sy+mat*2,'x1':sx+a+mat,'x2':sx+b-mat,'y2':sy+y-mat*2,'z1':sz,'z2':sz+z, 'type': SHADOW_OUTER},
//                 {'y1':sy+mat*2,'x1':sx+x,'x2':sx+b+mat,'y2':sy+y-mat*2,'z1':sz,'z2':sz+z, 'type': 'Sr'},
//                 {'y1':sy+mat*2,'x1':sx,'x2':sx+a-mat,'y2':sy+y-mat*2,'z1':sz,'z2':sz+z, 'type': 'Sl'},
//             ];
// }

const floorAndAddReminder = function distributeReminderAcrossRoundedDownValuesFromLeftToRight(values, total) {
    let newValues = values.map(r => Math.floor(r));
    const sum = newValues.reduce((sum, n) => sum + n, 0);
    for (let i = 0; i < total - sum; i++)
        newValues[i % values.length] += 1;
    return newValues;
};

const parseParam = function computeAndParseParameterBasedOnObjectsData( // aka _parse_values
    value, start = 0, max_val = 0, constants = null,
) {

    const checkFlexibleList = function checkForListTypeBasedOnAmountOfFlexibleAndRatioValues(values) { // aka check_flexible_list
        // '''Sprawdza rodzaj wartosci w liscie: fixed - tylko stale, ratio - +proporcje (x/y/z), flexible - +min/max/*x'''

        // # dopuszczalna jedna wartosc nieokreslona - x/y/z powtorzona dowolna ilosc razy
        const ratioSymbolsCount = ratioSymbols.filter(
            rs => values.some(v => isString(v) && v.includes(rs) && !v.startsWith('#')),
        ).length;
        // # alternatywnie - dopuszczalna tylko jedna wartosc nieokreslona min/max/ *
        const flexibleValuesCount = values.filter(
            v => isString(v) && (v.startsWith('max') || v.startsWith('*')),
        ).length;
        if (ratioSymbolsCount === 0 && flexibleValuesCount === 0) return 'fixed';
        if (ratioSymbolsCount === 1 && flexibleValuesCount === 0) return 'ratio';
        if (ratioSymbolsCount === 0 && flexibleValuesCount === 1) return 'flexible';
        return 'other';
    };

    const parseListWithRatios = function parseListWithRatiosValues(values, start, size, constants) { // aka check_flexible_list
        // '''Oblicza wartosci dla listy zawierajacej proporcje (x/y/z etc)'''
        const ratioSymbol = ratioSymbols.find(
            rs => values.some(v => isString(v) && !v.startsWith('#') && v.includes(rs)),
        );
        const isFlexible = v => isString(v) && !v.startsWith('#') && v.includes(ratioSymbol);
        const fixed = values.filter(v => !isFlexible(v)).map(v => parseParam(v, 0, size, constants));
        let ratio = values.filter(v => isFlexible(v)).map(v => Number(v.replace(ratioSymbol, '')) || 1);
        const ratio_value = (size - fixed.reduce((a, b) => a + b, 0)) / ratio.reduce((a, b) => a + b, 0);
        ratio = ratio.map(r => r * ratio_value);
        ratio = floorAndAddReminder(ratio, size - fixed.reduce((sum, n) => sum + n, 0));
        values = values.slice().reverse();
        return values.map((v) => {
            return isFlexible(v) ? start + ratio.pop() : start + fixed.pop();
        }).reverse();
    };

    const parseListWithFlexibleValue = function parseListWithFlexibleValue(values, start, size, constants) {
        // '''Oblicza wartosci dla listy zawierajacej element elastyczny: min/max/mnozenie *x'''
        const flexible_stuff = ['*', 'min', 'max'];
        const isFlexible = v => isString(v) && !v.startsWith('#') && flexible_stuff.some(fs => v.includes(fs));
        //
        // flexible = [_flexible(v) for v in values]
        const total = values.filter(v => !isFlexible(v)).map(v => parseParam(v, 0, size, constants)[0])
            .reduce((a, b) => a + b, 0);
        return values.map((v) => {
            if (isFlexible(v)) {
                return parseParam(v, start, size - total, constants)[0];
            } else {
                return parseParam(v, 0, size, constants)[0];
            }
        });
    };

    /* def _mult(start, total, number): TODO: Mult
        '''Dzieli zadana wielkosc na okreslona ilosc czesci, uzupelniajac je o reszte z podzialu'''
        values = [total / number] * number
        _add_reminder(values, total)
        return [start + v for v in values] */

    const DO_NOT_PARSE = ['', 'middle', 'single', 'double', 'triple', 'quadruple', 'quint', 'sex', 'sept', 'gradient',
        BACK, FRONT, DOOR, DOOR_F, HORIZONTAL, INSERT_H, INSERT_V, MARK, OPENING, PLINTH, SUPPORT,
        DRAWER, VERTICAL, SPACER, COMPONENT, TOP_BOX, FRONT_BOX];

    if (value === undefined || value === null || typeof value === 'boolean' || DO_NOT_PARSE.indexOf(value) > -1) {
        return value;
    }

    constants = {...CONSTANTS, ...constants};  // FIXME: Zamiast za każdym razem nadpisywać dodać OR przy zapytaniu o klucz?

    if (value.constructor === Array) {
        switch (checkFlexibleList(value)) {
            case 'fixed':
                // return [_parse_values(v, start, max_val, constants) for v in value]
                return value.map(v => parseParam(v, start, max_val, constants));
            case 'ratio':
                return parseListWithRatios(value, start, max_val, constants);
            case 'flexible':
                return parseListWithFlexibleValue(value, start, max_val, constants);
            default:
                throw new Error(`Cannot parse list: ${value}`);
        }
    }

    if (typeof value === 'string' || value instanceof String) {
        value = value.toLowerCase().replace(/ /g, '').replace(/--/g, '');
        if (value.includes(',')) {
            return value.split(',').map(v => parseParam(v, start, max_val, constants));
        }
        if (/^\d+\.?\d+$/.test(value)) {
            return start + Math.round(value);
        }
        if (/^-\d+\.?\d+$/.test(value)) {
            return start + max_val + Math.round(value);
        }
        if (value.startsWith('#')) {
            return parseParam(constants[value], start, max_val, constants);
        }
        if (value.startsWith('-#')) {
            return parseParam(`-${constants[value.substr(1)]}`, start, max_val, constants);
        }
        if (value === 'x') {
            value = max_val;
        } else if (value === 'r') { // TODO [CLEAN]: Do przeniesienia do serializacji
            value = max_val;
        } else if (value === 'l') { // TODO [CLEAN]: Do przeniesienia do serializacji
            value = start;
        } else if (value === 'm') { // TODO [CLEAN]: Do przeniesienia do serializacji
            value = max_val / 2;
        } else if (value.endsWith('x')) {
            value = parseInt(value.replace(/x/g, ''), 10);
        } else if (value.endsWith('mm')) { // TODO [CLEAN]: Do przeniesienia do serializacji
            value = parseInt(value.replace(/mm/g, ''), 10);
        } else if (value.endsWith('cm')) { // TODO [CLEAN]: Do przeniesienia do serializacji
            value = parseInt(value.replace(/cm/g, ''), 10) * 10;
        } else if (value.endsWith('dm')) { // TODO [CLEAN]: Do przeniesienia do serializacji
            value = parseInt(value.replace(/dm/g, ''), 10) * 100;
        } else if (value.endsWith('%')) {
            value = parseFloat(value.replace(/%/g, '')) * 0.01 * max_val;
        } else if (value.includes('/')) {
            value = parseInt(value.split('/')[0], 10) * max_val / parseInt(value.split('/')[1], 10);
        }
        if (!isNaN(value)) {
            value = Number(value);
        }
        /* TODO [HIGH]: Multiply
        elif value.startswith('*'):
            number = int(_parse_values(value.strip('*'), 0, max_val, constants))
            return _mult(start, max_val, number)
        elif value.startswith('min'):
            number = int(max_val // int(_parse_values(value.strip('min'), 0, max_val, constants)))
            return _mult(start, max_val, number)
        elif value.startswith('max'):
            number = int(max_val // int(_parse_values(value.strip('max'), 0, max_val, constants)) + 1)
            return _mult(start, max_val, number) */
    }

    if (typeof value === 'number' && Number.isFinite(value) && value >= 0) {
        return start + Math.round(value);
    }
    if (typeof value === 'number' && Number.isFinite(value) && value < 0) {
        return start + max_val + Math.round(value);
    }
    if (typeof value === 'number') {
        return value;
    }

    throw new Error(`Cannot parse value -> ${value}`);
    /* except Exception as e:
        raise ValueError('Error when parsing value', value, *e.args) */
};


const getParam = function findComputeAndParseParameterBasedOnObjectsData( // aka get_param
    key, parameters, constants = {}, start = 0, max_val = 0, default_val = false, true_default = null, replace_with_default=null
) { // FIMXE: 1. Zawsze array -> czy tak zostaje?
    const asArray = function converVariableToArray(value) { // aka as_iter
        if (value === undefined) {
            return [value];
        }
        switch (value.constructor) {
            case Array:
                return value;
            case Object:
                throw new Error('Obiekt w parserze!');
            default:
                return [value];
        }
    };
    let value;

    smartLog(['log_parser'].some(k => k in parameters),
        `### getParam - KEY: ${key} - START: ${start} - MAX: ${max_val} ${'#'.repeat(20)}`,
        false,
        [['data', parameters, 'default', default_val, 'true_def', true_default, 'constants', constants]]);


    if (
        key === undefined
        || !key
        || !(key in parameters)
        || parameters[key] === undefined
        || (replace_with_default !== null && parameters[key] === replace_with_default)
    ) {
        value = asArray(parseParam(default_val, start, max_val, constants));
        smartLog(['log_parser'].some(k => k in parameters), false, true,
            [['DEFAULD OUT ', value]]);
        return value;
    }

    value = parameters[key];

    /* NOTE: Prefix i suffix, makra
    value = data.get(PREFIX_KEEP + key, default)    # znajdz ?param
    value = data.get(key, value)                    # znajdz  param
    value = data.get(PREFIX_OVERRIDE + key, value)  # znajdz !param

    if use_macro:
        value = _use_macro(key, value, data) */

    if (value === true && true_default !== null) {
        value = true_default;
    }
    if (typeof value === 'string' || value instanceof String) {
        value = value.replace(/ /g, '');
    }

    let parsed = parseParam(value, start, max_val, constants);


    /* NOTE: To 'mam nadzieję' zniechęca do tłumaczenia :P
    # nadpisuje (mam nadzieje) wartosci w danych, tak zeby przy kolejnych odwolaniach byly zinterpretowane
    # nie jestem na 100% pewien czy to dobry pomysl, ale przy zachowaniu kolejnosci odwolan do parametrow powinno byc ok
    if type(parsed) is int and save:
        if turbokey and turbokey[0] in data['config_params']:  # turbochipsy
            data['config_params'][turbokey[0]][turbokey[1]] = parsed - start
        else:
            data[key] = parsed - start */

    parsed = asArray(parsed);
    smartLog(['log_parser'].some(k => k in parameters), false, true,
        [['PARSED OUT', parsed, 'VALUE', value]]);
    return parsed;
};

const roundCoors = function roundObjectCoordinates(
    geometry, min_x, max_x
){
    const round = function round(
        elem, coors, min_x, max_x
    ){
        let rounded = {};

        switch (elem.type){
            default:
                ['x1', 'x2'].forEach((i) => {
                    if (!(i in elem) || Number.isInteger(elem[i])) return;
                    if (i in coors && elem[i] in coors[i]){
                        rounded[i] = coors[i][elem[i]];
                        return;
                    }
                    if (!(i in coors)) coors[i] = {};
                    let val = Math.round(elem[i]);
                    if (i === 'x1' && Math.abs(min_x - val) < 1) val = min_x;
                    if (i === 'x2' && Math.abs(max_x - val) < 1) val = max_x;
                    coors[i][elem[i]] = val;
                    rounded[i] = coors[i][elem[i]];
              } )
        }
        return rounded
    };

    let rounded_geom = [];
    let hori = {};
    let coors = {'x1': hori, 'x2': hori};
    geometry.forEach((elem) => {
        rounded_geom.push({...elem, ...round(elem, coors, min_x, max_x)});
    });
    // console.log(">>>>>", coors);
    return rounded_geom
};
// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- GEOMETRIA ELEMENTÓW ---- //////////////////////////////////////////////////////////////////////////

const calculateEnvelope = function calculateCoordinatesOfGivenSpace( // aka calculate_envelope
    start_x, size_x, start_y, size_y, start_z, size_z,
) {
    const x1 = start_x;
    const x2 = start_x + size_x;
    const y1 = start_y;
    const y2 = start_y + size_y;
    const z1 = start_z;
    const z2 = start_z + size_z;

    return {
        x1, x2, y1, y2, z1, z2,
    };
};

const buildOuterGeom = function buildOuterElementsGeometry( // aka get_outer_geometry
    start_x, size_x, start_y, size_y, start_z, size_z, left, top, right, bottom, front, y_first,
    c_subconfig_id, c_config_id, m_config_id, offset_z
) {
    const geom = [];
    const {x1, x2, y1, y2, z1, z2,} = calculateEnvelope(start_x, size_x, start_y, size_y, start_z, size_z);

    // ----------------- VERTICALE / HORIZONTALE -----------------
    if (bottom >= 0) { // Pierwszy na dole - dodaj hori pod
        geom.push({
            x1,
            x2,
            y1,
            z1,
            z2: z2 - (bottom === 2 ? offset_z : 0),
            c_subconfig_id,
            c_config_id,
            m_config_id,
            y2: y1,
            type: [ERASER_H, HORIZONTAL, INSERT_H][bottom],
        });
    }

    if (left >= 0) { // Każdy compartment ma piony po lewej
        geom.push({
            x1,
            y1,
            y2,
            z1,
            z2: z2 - (left === 2 ? offset_z : 0),
            c_subconfig_id,
            c_config_id,
            m_config_id,
            x2: x1,
            type: [ERASER_V, VERTICAL, INSERT_V][left],
        });
    }

    if (right >= 0) { // Vert po prawej elementu w ostatnim
        geom.push({
            x2,
            y1,
            y2,
            z1,
            z2: z2 - (right === 2 ? offset_z : 0),
            c_subconfig_id,
            c_config_id,
            m_config_id,
            x1: x2,
            type: [ERASER_V, VERTICAL, INSERT_V][right],
        });
    }

    // ----------------- HORIZONTAL NAD -----------------
    if (top >= 0) { // Dodaj hori nad elementem
        const opentop = top === 3 ? [[x1 + MAT_THICKNESS / 2, x2 - MAT_THICKNESS / 2]] : [];
        geom.push({
            x1,
            x2,
            y2,
            z1,
            z2: z2 - (top === 2 ? offset_z : 0),
            c_subconfig_id,
            c_config_id,
            m_config_id,
            y1: y2,
            type: [ERASER_H, HORIZONTAL, INSERT_H, HORIZONTAL][top],
            opening: opentop,
        });
    }
    geom.push({
        x1,
        x2,
        y1,
        y2,
        z1,
        z2,
        c_subconfig_id,
        c_config_id,
        m_config_id,
        type: SHADOW_OUTER,
    });

    return geom;
};

const buildInnerGeom = function buildInnerElementsGeometry( // aka get_inner_geometry
    start_x, size_x, start_y, size_y, start_z, size_z, back, front, e_type,
    c_subconfig_id, c_config_id, m_config_id,
    geom_id = null, flip = false, split = false, handle = 1,
) {
    const geom = [];
    const {x1, x2, y1, y2, z1, z2} = calculateEnvelope(start_x, size_x, start_y, size_y, start_z, size_z);
    // ----------------- GEOMETRIA ELEMENTU -----------------
    const elem_geom = {
        x1,
        x2,
        y1,
        y2,
        z1,
        z2,
        c_subconfig_id,
        c_config_id,
        m_config_id,
        type: e_type,
        base_e_id: geom_id,
    };
    if (handle) {
        elem_geom.handle = handle;
    }
    if (split && e_type !== DRAWER) {
        const elem_geom2 = Object.assign({}, elem_geom); // deepcopy niepotrzebne!
        elem_geom2.x1 = split;
        geom.push(elem_geom2);
        elem_geom.x2 = split;
        elem_geom.flip = 1;
    }
    if (flip) {
        elem_geom.flip = flip;
    }
    if (e_type !== FRONT) {
        geom.push(elem_geom);
    }

    // ----------------- PLECY i FRONTY -----------------
    if (front > 0) {
        elem_geom.z1 = z2 - MAT_THICKNESS - 4;
        geom.push({
            x1,
            y1,
            y2,
            z1: z2 - MAT_THICKNESS - 4,
            c_subconfig_id,
            c_config_id,
            m_config_id,
            x2: x2,
            z2: z2,
            type: FRONT,
        });
    }
    let [l_sup, r_sup] = [false, false];
    switch (back){
        case 4:
            if (size_x > SUPPORT_WIDTH * 2 + MAT_THICKNESS / 2){
                 [l_sup, r_sup] = [true, true];
            } else if  (size_x > SUPPORT_WIDTH + MAT_THICKNESS / 2){
                r_sup = true;
            }
            break;
        case 3:
            if (size_x > SUPPORT_WIDTH + MAT_THICKNESS / 2) r_sup = true;
            break;
        case 2:
            if (size_x > SUPPORT_WIDTH + MAT_THICKNESS / 2) l_sup = true;
            break;
        case 1:
            geom.push({
            x1,
            x2,
            y1,
            y2,
            z1,
            c_subconfig_id,
            c_config_id,
            m_config_id,
            z2: z1 + MAT_THICKNESS,
            type: BACK,
        });
            break;
    }

    if (l_sup === true) {
        geom.push({
            x1,
            y1,
            y2,
            z1,
            c_subconfig_id,
            c_config_id,
            m_config_id,
            x2: x1 + SUPPORT_WIDTH + MAT_THICKNESS / 2,
            z2: z1 + MAT_THICKNESS,
            type: SUPPORT,
        });
    }
    if (r_sup === true) {
        geom.push({
            x2,
            y1,
            y2,
            z1,
            c_subconfig_id,
            c_config_id,
            m_config_id,
            x1: x2 - SUPPORT_WIDTH - MAT_THICKNESS / 2,
            z2: z1 + MAT_THICKNESS,
            type: SUPPORT,
        });
    }

    geom.push({
        x1,
        x2,
        y1,
        y2,
        z1,
        z2,
        c_subconfig_id,
        c_config_id,
        m_config_id,
        type: SHADOW_INNER,
    });

    return geom;
};


const defineSubsections = function defineSectionsOfComponentConfiguration( // aka define_subsections
    parameters, constants, start_x, size_x, start_y, size_y,
) {
    smartLog(['log_flow'].some(k => k in parameters),
        `### defineSubsections ${'#'.repeat(20)}`, false,
        [[parameters, start_x, size_x, start_y, size_y]], ['log_expand'].some(k => k in parameters));

    const sortKeepingNulls = function sortArrayKeepingNullsAtBothEndsAndAddingNullsIfSingleValue(dom) { // aka sort_keeping_nones
        const first_none = dom[0] === null || dom[0] === false ? [null] : [];
        const last_none = (dom[dom.length - 1] === null || dom[dom.length - 1] === false) && dom.length > 1 ? [null] : [];
        dom = first_none.concat(dom.slice(first_none.length, dom.length - last_none.length).sort((a, b) => a - b), last_none);
        dom = dom.length !== 1 ? dom : [null, dom[0], null];
        return dom;
    };

    const divideSubsections = function divideSubsectionsByLinesBasedOnDirection(subsections, lines, direction) { // aka divide_subsections
        const x0 = s => s[2];
        const x1 = s => s[2] + s[3];
        const y0 = s => s[4];
        const y1 = s => s[4] + s[5];
        lines.forEach((l) => {
            const divided_subsections = [];
            subsections.forEach((s) => {
                if (direction === 'x' && x0(s) < l[1] && l[1] < x1(s) && l[0] < y1(s) && l[2] > y0(s)) {
                    divided_subsections.push(...[
                        [x0(s), l[1] - x0(s), s[4], s[5]], // `${s[0]}_1`, s[1],
                        [l[1], x1(s) - l[1], s[4], s[5]], // `${s[0]}_2`, s[1],
                    ]);
                } else if (direction === 'y' && y0(s) < l[1] && l[1] < y1(s) && l[0] < x1(s) && l[2] > x0(s)) {
                    divided_subsections.push(...[
                        [s[2], s[3], s[4], l[1] - y0(s)], // s[0], `${s[1]}_1`,
                        [s[2], s[3], l[1], y1(s) - l[1]], // s[0], `${s[1]}_2`,
                    ]);
                } else {
                    divided_subsections.push(s);
                }
            });
            subsections = divided_subsections;
        });
        return subsections;
    };

    let [subsections, lines_x, lines_y] = [[], [], []];
    if (!('insert__dom_x' in parameters) && !('insert__dom_y' in parameters)) {
        subsections = [[0, 0, start_x, size_x, start_y, size_y]];
        smartLog(['log_flow'].some(k => k in parameters), false, true, [['short', subsections, [], []]]);
        return {subsections, lines_x, lines_y};
    }

    const dom_x = sortKeepingNulls(getParam('insert__dom_x', parameters, constants, start_x, size_x, [null, null]));

    const dom_y = sortKeepingNulls(getParam('insert__dom_y', parameters, constants, start_y, size_y, [null, null]));

    const [x_vals, x_s, x_e] = [dom_x.filter(x => x), dom_x[0] || start_x, dom_x[dom_x.length - 1] || (start_x + size_x)];
    const [y_vals, y_s, y_e] = [dom_y.filter(x => x), dom_y[0] || start_y, dom_y[dom_y.length - 1] || (start_y + size_y)];

    lines_x = x_vals.map(x => [y_s, x, y_e]).sort((a, b) => a - b);
    lines_y = y_vals.map(y => [x_s, y, x_e]).sort((a, b) => a - b);
    subsections = [[x_s, x_e - x_s, y_s, y_e - y_s]]; // 0, 0,

    subsections = divideSubsections(subsections, lines_x, 'x');
    subsections = divideSubsections(subsections, lines_y, 'y');

    smartLog(['log_flow'].some(k => k in parameters), false, true, [['long', subsections, lines_x, lines_y, dom_x, dom_y]]);
    return {subsections, lines_x, lines_y};
};


const buildGeometry = function buildCompartmentGeometryInGivenSpace( // aka generate_geometry
    parameters, constants, start_x, size_x, start_y, size_y, start_z, size_z,
    y_first, y_last, x_first, x_last, left, top, right, bottom, back, front,
    m_config_id, c_config_id, c_subconfig_id,
) {
    smartLog(['log_flow'].some(k => k in parameters),
        `### buildGeometry ${'#'.repeat(20)}`, false,
        [[start_x, size_x, start_y, size_y, start_z, size_z, y_first, y_last, x_last],
            [left, top, right, bottom, back, m_config_id, c_config_id, c_subconfig_id],
            [parameters, constants]],
        ['log_expand'].some(k => k in parameters));
    const log2 = ['log_flow'].some(k => k in parameters);

    let [offset_z] = getParam('insert__offset', parameters, constants, 0, 0, 30);
    // TODO: nie jestem pewien, ale 'type' powinno chyba zmieniac sie na type__value w serializacji. Na razie nie zmienia sie, wiec zostawiam
    let e_type = getParam('type', parameters, constants, 0, 0, OPENING)[0];
    if ([DOOR, DRAWER, FRONT_BOX].includes(e_type))
        back = 1;

    const flip = getParam('fill__flip', parameters, constants)[0] || 0;
    const handle = getParam('fill__handle', parameters, constants)[0]; // || 'handle' in parameters ? parameters.handle : 1;

    const geom = [];
    geom.push(...buildOuterGeom(
        start_x, size_x, start_y, size_y, start_z, size_z, left, top, right, bottom, front, y_first,
        c_config_id, c_config_id, m_config_id, offset_z
    ));

    let {subsections, lines_x, lines_y} = defineSubsections(parameters, constants, start_x, size_x, start_y, size_y);
    if ((lines_x === undefined || lines_x.length === 0) && (lines_y === undefined || lines_y.length === 0)) {
        offset_z = 0;
    }
    let front_offset_z = offset_z;

    if (!getParam('insert__dom_share', parameters, constants, 0, 0, false)[0]) {
        subsections = [[start_x, size_x, start_y, size_y]]; // 0, 0,
        front_offset_z = 0;
        if (e_type === DOOR) {
            e_type = DOOR_F;
        }
    }
    subsections.forEach((sub_s) => {
        const [_start_x, _size_x, _start_y, _size_y] = sub_s; // i_x, i_y,
        let split = getParam('fill__split', parameters, constants, start_x, size_x, false, '50%')[0];
        if (
            getParam('fill__split_start', parameters, constants, 0, 0, 0)[0] >= _size_x
            || _size_x >= getParam('fill__split_end', parameters, constants, 0, 0, Number.POSITIVE_INFINITY)[0]
            || _start_x + _size_x < split || _start_x > split
        ) split = false;
        geom.push(...buildInnerGeom(
            _start_x, _size_x, _start_y, _size_y, start_z, size_z -front_offset_z,
            back, front, e_type, c_subconfig_id, c_config_id, m_config_id,
            null, flip, split, handle
        ));
    });

    lines_x.forEach((lx) => {
        const [ys, axis, ye] = lx;
        geom.push({
            c_subconfig_id, c_config_id, m_config_id,
            x1: axis,
            x2: axis,
            y1: ys,
            y2: ye,
            z1: start_z,
            z2: size_z - offset_z,
            type: INSERT_V,
        });
    });

    lines_y.forEach((ly) => {
        const [xs, axis, xe] = ly;
        geom.push({
            c_subconfig_id, c_config_id, m_config_id,
            x1: xs,
            x2: xe,
            y1: axis,
            y2: axis,
            z1: start_z,
            z2: size_z - offset_z,
            type: INSERT_H,
        });
    });

    smartLog(['log_flow'].some(k => k in parameters), false, true, [[geom]]);
    return geom;
};

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- KOMPONENTY ---- ///////////////////////////////////////////////////////////////////////////////////

const facesDefaultsByType = {
    "O": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": -1, "front": -1},  // OPENING
    "D": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": 1, "front": -1},  // DOOR
    "T": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": 1, "front": -1},  // DRAWER
    "FB": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": 1, "front": -1},  // FRONT BOX
    "TB": {"left": 1, "right": 1, "top": 3, "bottom": 1, "back": 1, "front": 1},  // TOP BOX
    "0": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": -1, "front": -1},  // OPENING
    "1": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": 1, "front": -1},  // DOOR
    "2": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": 1, "front": -1},  // DRAWER
    "3": {"left": 1, "right": 1, "top": 1, "bottom": 1, "back": 1, "front": -1},  // FRONT BOX
    "4": {"left": 1, "right": 1, "top": 3, "bottom": 1, "back": 1, "front": 1},  // TOP BOX
};

const defineSections = function defineHorizontalSectionsOfComponent( // aka define_comp_config_sections
    parameters, constants, start_x, size_x,
) {
    smartLog(['log_flow'].some(k => k in parameters),
        `### defineSections ${'#'.repeat(20)}`, false,
        [[parameters, constants, start_x, size_x]], ['log_expand'].some(k => k in parameters));

    const type = getParam('type', parameters, constants, 0, 0, "O")[0];
    const defaultFaces = facesDefaultsByType[type];
    // NOTE: bez skoplikowanych defaultow (nie potrzebne)
    const left = getParam('face__s_left', parameters, constants, 0, 0, defaultFaces['left'])[0];
    const right = getParam('face__s_right', parameters, constants, 0, 0, defaultFaces['right'])[0];
    const top = getParam('face__s_top', parameters, constants, 0, 0, defaultFaces['top'])[0];
    const bottom = getParam('face__s_bottom', parameters, constants, 0, 0, defaultFaces['bottom'])[0];
    const back = (defaultFaces['back'] === 1 ? 1 :
        getParam('face__s_back', parameters, constants, 0, 0, defaultFaces['back'])[0]
    );
    const front = getParam('face__s_front', parameters, constants, 0, 0, defaultFaces['front'])[0];
    let sections = [[-1, start_x, start_x + size_x, left, top, right, bottom, back, front]];

    if (getParam('triple__dim', parameters, constants, start_x, size_x)[0]
        && getParam('triple__start', parameters, constants, 0, 0, 0)[0] <= size_x
        && size_x < getParam('triple__stop', parameters, constants, 0, 0, Number.POSITIVE_INFINITY)[0]) {
        const top_l = getParam('face__t_top_l', parameters, constants, 0, 0, top)[0];
        const top_m = getParam('face__t_top_m', parameters, constants, 0, 0, top)[0];
        const top_r = getParam('face__t_top_r', parameters, constants, 0, 0, top)[0];
        const bottom_l = getParam('face__t_bottom_l', parameters, constants, 0, 0, bottom)[0];
        const bottom_m = getParam('face__t_bottom_m', parameters, constants, 0, 0, bottom)[0];
        const bottom_r = getParam('face__t_bottom_r', parameters, constants, 0, 0, bottom)[0];
        const middle_left = getParam('face__t_middle_l', parameters, constants, 0, 0, 1)[0];
        const middle_right = getParam('face__t_middle_r', parameters, constants, 0, 0, 1)[0];
        const back_l = getParam('face__t_back_l', parameters, constants, 0, 0, back)[0];
        const back_m = getParam('face__t_back_m', parameters, constants, 0, 0, back)[0];
        const back_r = getParam('face__t_back_r', parameters, constants, 0, 0, back)[0];
        const front_l = getParam('face__t_front_l', parameters, constants, 0, 0, front)[0];
        const front_m = getParam('face__t_front_m', parameters, constants, 0, 0, front)[0];
        const front_r = getParam('face__t_front_r', parameters, constants, 0, 0, front)[0];

        const val = getParam('triple__dim', parameters, constants, start_x, size_x);
        let [val_a = null, val_b = null] = val;
        val_a = !Number.isNaN(parseInt(val_a, 10)) ? val_a : start_x + size_x / 3;
        val_b = !Number.isNaN(parseInt(val_b, 10)) ? val_b : start_x + size_x / 3 * 2;
        if (val_b < val_a) { // Bo dziewczyny nie umieją wpisywać wartości triple w kolejności rosnącej
            [val_a, val_b] = [val_b, val_a];
        }

        sections.push(...[
            [4, start_x, val_a, left, top_l, middle_left, bottom_l, back_l, front_l],
            [5, val_a, val_b, middle_left, top_m, middle_right, bottom_m, back_m, front_m],
            [6, val_b, start_x + size_x, middle_right, top_r, right, bottom_r, back_r, front_r],
        ]);

    } else if (
        getParam('double__dim', parameters, constants, start_x, size_x)[0]
        && getParam('double__start', parameters, constants, 0, 0, 0)[0] <= size_x
        && size_x < getParam('double__stop', parameters, constants, 0, 0, Number.POSITIVE_INFINITY)[0]
    ) {
        const top_l = getParam('face__d_top_l', parameters, constants, 0, 0, top)[0];
        const top_r = getParam('face__d_top_r', parameters, constants, 0, 0, top)[0];
        const bottom_l = getParam('face__d_bottom_l', parameters, constants, 0, 0, bottom)[0];
        const bottom_r = getParam('face__d_bottom_r', parameters, constants, 0, 0, bottom)[0];
        const middle = getParam('face__d_middle', parameters, constants, 0, 0, 1)[0];
        const back_l = getParam('face__d_back_l', parameters, constants, 0, 0, back)[0];
        const back_r = getParam('face__d_back_r', parameters, constants, 0, 0, back)[0];
        const front_l = getParam('face__d_front_l', parameters, constants, 0, 0, front)[0];
        const front_r = getParam('face__d_front_r', parameters, constants, 0, 0, front)[0];
        const val = getParam('double__dim', parameters, constants, start_x, size_x, null, size_x / 2)[0];

        sections.push(...[
            [2, start_x, val, left, top_l, middle, bottom_l, back_l, front_l],
            [3, val, start_x + size_x, middle, top_r, right, bottom_r, back_r, front_r],
        ]);

    } else {
        sections.push([1, start_x, start_x + size_x, left, top, right, bottom, back, front]);
    }

    return sections.sort((a, b) => a - b);
};

const buildCompConfig = function buildGeometryOfComponentConfigurationInGivenSpace( // aka get_geometry_from_comp_config
    config, size_x, size_z, start_x, start_y, start_z, y_first, y_last, x_first, x_last, m_config_id, m_constants, backpanels_rows=[]
) {
    smartLog(['log_flow'].some(k => k in config.parameters),
        `### buildCompConfig ${'#'.repeat(20)}`, false,
        [[config], [size_x, size_z, start_x, start_y, start_z, y_first, y_last, x_first, x_last, m_config_id, m_constants]],
        ['log_expand'].some(k => k in config.parameters));

    const geom = [];
    const constants = {...config.constants, ...m_constants};
    const sections = defineSections(config.parameters, constants, start_x, size_x);
    const size_y = getParam('e_size_y', config.parameters, constants, 0, 0, 0)[0];
    // ---- Dla każdej sekcji
    sections.forEach((section_data) => {
        let [section, pos_l, pos_r, left, top, right, bottom, back, front] = section_data;
        if (backpanels_rows && backpanels_rows.length > 0) back = 1
        const section_size_x = pos_r - pos_l;
        const subconfigs = [];

        // -- Dla każdej subkonfiguracji zdefiniowanej w projekcie
        config.subconfigs.forEach((subconfig) => {
            const sub_constants = {...subconfig.constants, ...constants};
            const part = getParam('part__value', subconfig.parameters, sub_constants, 0, 0, [0]);

            // POMIŃ SUBKONFIG jeśli niezgodna sekcja oraz (brak sekcji 0 w part lub sekcja to nie -1)
            if (part.indexOf(section) < 0 && (part.indexOf(0) < 0 || section === -1)) return;

            // POMIŃ SUBKONFIG jeśli wykluczony poprzez trans__start lub trans__stop
            if (section_size_x < getParam('trans__start', subconfig.parameters, sub_constants, 0, 0, 0)[0]
                || section_size_x > getParam('trans__stop', subconfig.parameters, sub_constants, 0, 0, Number.POSITIVE_INFINITY)[0]
            ) return;

            subconfigs.push(subconfig);
        });
        // -- Jeśli sekcja to nie -1 lub 0 oraz nie ma subkonfiguracji -> dodaj subkonfig na podstawie konfiguracji
        if ([-1, 0].indexOf(section) === -1 && subconfigs.length === 0) {

            geom.push(...buildGeometry(
                config.parameters, constants, pos_l, section_size_x, start_y, size_y, start_z, size_z,
                y_first, y_last, x_first, x_last, left, top, right, bottom, back, front,
                m_config_id, config.parameters.c_config_id, config.parameters.c_config_id
            ));
        }

        // Dla każdej subkonfiguracji
        subconfigs.forEach((subconfig) => {
            const sub_constants = {...subconfig.constants, ...constants};
            const sub_type = getParam('type', subconfig.parameters, sub_constants, 0, 0, "O")[0];
            const { left, top, right, bottom, back, front } = facesDefaultsByType[sub_type];
            let s_left = getParam('face__s_left', subconfig.parameters, sub_constants, 0, 0, left)[0];
            let s_right = getParam('face__s_right', subconfig.parameters, sub_constants, 0, 0, right)[0];
            let s_top = getParam('face__s_top', subconfig.parameters, sub_constants, 0, 0, top)[0];
            let s_bottom = getParam('face__s_bottom', subconfig.parameters, sub_constants, 0, 0, bottom)[0];
            let s_back = getParam('face__s_back', subconfig.parameters, sub_constants, 0, 0, back)[0];
            let s_front = getParam('face__s_front', subconfig.parameters, sub_constants, 0, 0, front)[0];

            /* TODO [LOW]: Multiply
            for multconfig in multiply([subconfig], 'e_size_y', 'e_start_y'):
                new_geom = get_geometry(**multconfig)
                geom.extend(new_geom) */

            geom.push(...buildGeometry(
                subconfig.parameters, sub_constants, pos_l, section_size_x, start_y, size_y, start_z, size_z,
                y_first, y_last, x_first, x_last, s_left, s_top, s_right, s_bottom, s_back, s_front,
                m_config_id, config.parameters.c_config_id, subconfig.parameters.c_config_id
            ));
        });
    });
    smartLog(['log_flow'].some(k => k in config.parameters), false, true, [[geom]]);
    return geom;
};

const buildComponent = function buildGeometryOfComponentInGivenSpace( // aka get_geometry_from_component
    component, size_x, setup_name, size_z, comp_id, backpanels_rows=[], start_x = 0, start_y = 0, start_z = 0,
    x_first = true, x_last = true, m_config_id = null, m_constants = null,
) {
    /*
    let mirror_vert = !(data.get('from_mesh', False))
    c_geom_id = next(get_param('c_geom_id', data)) */

    smartLog(['log_flow'].some(k => k in (component.parameters || {})),
        `### buildComponent ${'#'.repeat(20)}`, false,
        [[component, size_x, setup_name, size_z, comp_id, start_x, start_y, start_z, m_config_id]],
        ['log_expand'].some(k => k in component));

    // -- Znajduję setup komponentu
    const setup = component.setups[setup_name];
    if (setup === undefined) throw new Error(`Missing compSetup for height -> ${setup_name}. Available: ${Object.keys(component.setups).join(', ')}`);
    if (setup.configs === undefined || setup.configs.length === 0) throw new Error(`Missing compSetup configs for setup -> ${setup_name}`);

    /* TODO [LOW]: Multiply
    elements = multiply(elements, 'e_size_y', multisize=True) */

    // -- Konstruuję geometrię konfiguracji
    const geom = [];
    let current_y = start_y;
    setup.configs.forEach((config, i) => {

        const new_geom = buildCompConfig(
            config, size_x, size_z, start_x, current_y, start_z,
            i === 0, i >= setup.configs.length - 1, x_first, x_last,
            m_config_id, m_constants, backpanels_rows
        );

        if (new_geom.length > 0) {
            /* TODO [HIGH]: trans__mirror
            if next(get_param('trans__mirror', component_config), False) is True:
                new = mirror_x(new, mirror_vert=mirror_vert) */

            geom.push(...new_geom);
            current_y = Math.max(...new_geom.map(e => e.y2));
        }
    });

    geom.push({
        x1: start_x,
        x2: start_x + size_x,
        y1: start_y,
        y2: current_y === start_y ? start_y + setup_name : current_y,
        z1: start_z,
        z2: start_z + size_z,
        id: comp_id,
        type: COMPONENT,
        m_config_id,
    });

    smartLog(['log_flow'].some(k => k in (component.parameters || {})), false, true, [[geom]]);
    return geom;
};


// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- MESHE ---- ////////////////////////////////////////////////////////////////////////////////////////

const getGradientDivisions = function getGradientDivisionsFromMeshRatiosAndMotion(meshConfigRatios, sizeX, motion) {
    function applyMotionToEqualDivisions(divisions, motion, domainStart=0.4, domainEnd=0.9) {
        let moved = [0];
        for(let ix = 1; ix <divisions; ix++){
            let x = 1 / divisions * ix;
            let e1 = domainStart + (domainEnd - domainStart) * (x < motion ? (motion - x) / motion : (x - motion) / (1 - motion));
            moved.push(motion + (x - motion) * e1);
        }
        moved.push(1);
        return moved;
    }

    // NOTE:  na razie tylko dla 'x'
    let ratios = meshConfigRatios.filter(r => r.includes('x')).map(r => Number(r.replace('x', '')));
    let constants = meshConfigRatios.filter(r => !r.includes('x')).map(r => parseParam(r, 0, sizeX));
    let divisionsNumber = ratios.reduce((sum, r) => r + sum, 0);
    let constantsSum = constants.reduce((sum, r) => r + sum, 0);
    let moved = applyMotionToEqualDivisions(divisionsNumber, motion).map(x => x * (sizeX - constantsSum));
    moved = floorAndAddReminder(moved, sizeX - constantsSum);
    let finalDivisions = [];
    let currentX = 0;
    let divisionsSum = 0;
    for (let i = 0; i < meshConfigRatios.length; i++) {
        let r = meshConfigRatios[i];
        if (r.includes('x')) {
            let currentDivision = ratios.shift();
            divisionsSum += currentDivision;
            finalDivisions.push([currentX, moved[divisionsSum] - moved[divisionsSum - currentDivision]]);
        } else {
            finalDivisions.push([currentX, constants.shift()]);
        }
        currentX = finalDivisions[i][0] + finalDivisions[i][1];
    }
    return finalDivisions;

};

const getDivisionsFromMeshRatio = function get_divisions_from_ratio(mesh_configs_ratios, size_x, offset = 0) {
    // """Oblicza podzialy mesha na podstawie listy szerokosci konfiguracji"""
    let width_array = parseParam(mesh_configs_ratios, offset, size_x);
    let start_array = [];
    width_array.reduce((a, b, i) => {
        return start_array[i] = a + b;
    }, 0);
    start_array.unshift(0);
    return width_array.map((w, i) => [start_array[i], w])
};


const getMeshDivisions = function getMeshDivisionsUsingSomeKindOfOrNoDistortion(setup, sizeX, motion, offset=0, distortionMode=null) {

    let meshConfigRatios = [];
    setup.configs.forEach((c, index) => {
        let {division_ratio, comp_id} = c.parameters;
        division_ratio.split(',').forEach(ratio => {
            meshConfigRatios.push({ ratio, comp_id, index });
        });
    });
    let meshDivisions = [];
    switch (distortionMode) {
        case 'gradient':
             meshDivisions = getGradientDivisions(meshConfigRatios.map(r => r.ratio), sizeX, motion);
             break;
        default:
             meshDivisions = getDivisionsFromMeshRatio(meshConfigRatios.map(r => r.ratio), sizeX, offset);
    }
    return meshDivisions.map(([mc_start_x, mc_size_x], i) => {
        let {comp_id, index} = meshConfigRatios[i];
        return { mc_start_x, mc_size_x, comp_id, index };
    });
};


const pickSetup = function pickSetupBasedOnWidthOrMotion(setups, width, motion, densityMode) {
    switch(densityMode) {
        case 'grid':
            let proper_setups = Object.keys(setups).filter((name) => {
                return (
                    'parameters' in setups[name]
                    && 'x_range' in setups[name].parameters
                    && setups[name].parameters.x_range[0] <= width
                    && width <= setups[name].parameters.x_range[1]
                )
            }).sort((a, b) => a - b);
            let pick = Math.max(Math.ceil(motion / (100 / proper_setups.length)) - 1, 0);
            return setups[proper_setups[pick]] ;


        default:
            let setup_name = Object.keys(setups).filter((v) => {
                return v <= width
            }).sort((a, b) => a - b).reverse()[0];
            // -- Znajduje setup
            if (setup_name === undefined) {
                throw new Error(`Missing mesh setup for width -> ${width}`)
            }
            // -- Znajduje konfiguracje mesha
            return setups[setup_name];
    }
};


const mirrorGeometryX = function simpleMirrorAlongXAxis(geom) {
    let sortedX = geom.reduce((allX, next) => {
        allX.push(next.x1, next.x2);
        return allX;
        }, []).sort((a, b) => a - b);
    let maxPlusMin = sortedX[0] + sortedX.pop();
    geom.forEach(e => {
       let x1 = maxPlusMin - e.x1;
       let x2 = maxPlusMin - e.x2;
       e.x1 = x1 < x2 ? x1 : x2;
       e.x2 = x1 > x2 ? x1 : x2;
       if (e.hasOwnProperty('opening') && e.opening.length > 0) {
           e.opening = e.opening.map(([o1, o2]) => {
               o1 = maxPlusMin - o1;
               o2 = maxPlusMin - o2;
               return o1 < o2 ? [o1, o2] : [o2, o1];
           }).sort((a, b) => a[0] - b[0]);
       }
    });
    return geom;
};

const makePlinth = function createPlinthGeometryBasedOnAggregatedPlinthSettingsFromMeshConfigurations(
    data, plinthSettings, width, depth, thickness, meshParams, meshConstants
) {

    function appendCrossPlinth(geom, axis, longX) {
        let x1 = axis - thickness / 2;
        let x2 = axis + thickness / 2;
        let longX0 = x1 - plinthOffsetLong < -thickness / 2 ? -thickness / 2 : x1 - plinthOffsetLong;
        if(longX0 < longX[0]) longX[0] = longX0;
        let longX1 = x1 + plinthOffsetLong > width + thickness / 2 ? width + thickness / 2 : x1 + plinthOffsetLong;
        if(longX1 > longX[1]) longX[1] = longX1;

        if (geom.some(c => c.x1 === x1 && c.x2 === x2))
            return;

        geom.push({
            x1: x1,
            x2: x2,
            y1: 0,
            y2: plinthHeight,
            z1: 0 + plinthOffsetZ,
            z2: (depth - thickness) / 2,
            type: 'Py'
        });

        geom.push({
            x1: x1,
            x2: x2,
            y1: 0,
            y2: plinthHeight,
            z1: (depth + thickness) / 2,
            z2: depth - plinthOffsetZ,
            type: 'Py'
        });
    }

    function getPlinthPosOptions(setting, start, end) {
        let returnPos = [];
        if (!(setting in defaultPropDict))
            return returnPos;
        let correction = setting === 'double' ? 0 : plinthOffsetX;
        let pos = (width - 2 * correction) / defaultPropDict[setting];
        for (let x = 1; x < defaultPropDict; x++)
            returnPos.push(pos * x + correction);
        return returnPos.filter(x => start <= x && x <= end);
    }

    function parsePlinthSetting(x1, x2, setting) {
        if ((setting === true || setting === '' || setting in defaultPropDict) && x1 <= plinthOffsetX)
            appendCrossPlinth(geom, plinthOffsetX, longX);
        if ((setting === true || setting === '' || setting in defaultPropDict) && x2 >= width - plinthOffsetX)
            appendCrossPlinth(geom, width - plinthOffsetX, longX);
        else if (typeof setting !== "boolean" && !(setting in defaultPropDict) && setting !== '')
            appendCrossPlinth(geom, getParam('x', { x: setting }, meshConstants, x1, x2 - x1)[0], longX);
        getPlinthPosOptions(setting, x1, x2).forEach(pos => {
           appendCrossPlinth(geom, pos, longX);
        });
    }

    const plinthHeight = getParam('plinth__height', meshParams, meshConstants, 0, 0, 90)[0]
    const plinthOffsetZ = getParam('plinth__offset_z', meshParams, meshConstants, 0, 0, 30)[0]
    const plinthOffsetX = getParam('plinth__offset_x', meshParams, meshConstants, 0, 0, 320)[0]
    const plinthOffsetLong = getParam('plinth__offset_long', meshParams, meshConstants, 0, 0, 130)[0]

    // jesli zadna z konfiguracji nie ma cokolu
    if (!plinthSettings.some(c => c.plinth[0] !== ''))
        return data;

    let geom = [];
    let longX = [plinthOffsetX - plinthOffsetLong, width - plinthOffsetX + plinthOffsetLong];
    let defaultPropDict = { middle: 2, single: 2, double: 3, triple: 4, quadruple: 5, quint: 6, sex: 7, sept: 8 };

    // poprzeczki
    plinthSettings.forEach(config => {
        let { x1, x2, plinth } = config;
        plinth.forEach(setting => parsePlinthSetting(x1, x2, setting));
    });

    // podluzny cokol
    geom.push({
        x1: longX[0],
        x2: longX[1],
        y1: 0,
        y2: plinthHeight,
        z1: (depth - thickness)/2,
        z2: (depth + thickness)/2,
        type: 'Px'
    });

    geom.reverse();
    data = data.concat({
        x1: longX[0],
        x2: longX[1],
        y1: -plinthHeight,
        y2: 0,
        z1: 0 + plinthOffsetZ,
        z2: depth - plinthOffsetZ,
        components: geom,
        type: PLINTH

    });

    data.forEach(e => {
        e.y1 += plinthHeight;
        e.y2 += plinthHeight;
    });
    return data;
};

const buildMesh = function getGeometryFromMesh(
    mesh, components, m_size_x, m_size_y, m_size_z, geom_id, backpanels_rows=[], m_start_x = 0, m_start_y = 0, m_start_z = 0,
    c_constants=null, motion=null, densityMode=null, distortionMode=null
) {
    /*NOTE: W inpucie:
    m_geom_id = next(get_param('m_geom_id', data))
    data.update({k: v for k, v in serialized_data['mesh'][m_geom_id].items() if k != 'configurations'})

    m_size_x = next(get_param('m_size_x', data))
    m_size_y = next(get_param('m_size_y', data))
    m_size_z = next(get_param('m_size_z', data))
    m_start_x = next(get_param('m_start_x', data, default=0))
    m_start_y = next(get_param('m_start_y', data, default=0))
    m_start_z = next(get_param('m_start_z', data, default=0))*/

    let setup_name = Object.keys(mesh.setups).filter((v) => {
        return v <= m_size_x
    }).sort((a, b) => a - b).reverse()[0];
    // -- Znajduje setup
    if (setup_name === undefined) {
        throw new Error(`Missing mesh setup for width -> ${m_size_x}`)
    }
    // -- Znajduje konfiguracje mesha
    let setup = pickSetup(mesh.setups, m_size_x, motion, densityMode);
    if (setup.configs === undefined || setup.configs.length === 0) throw new Error(`Missing mesh configs for width -> ${m_size_x}`);

    let motionChips = getParam('motion', mesh.parameters, c_constants, 0, 0)[0];
    let distortionModeChips = getParam('distortion', mesh.parameters, c_constants)[0];
    let configurationsTable = getMeshDivisions(
        setup, m_size_x - MAT_THICKNESS,
        (motion || motionChips) * 0.01, 0,
        distortionMode || distortionModeChips
    );

    /*TODO: multiply
    mesh_configs = multiply(mesh_configs, start_key='mc_start_x', size_key='mc_size_x')*/

    let plinthSettings = [];
    // -- Konstruuję geometrię konfiguracji
    let geom = [];
    // setup.configs.forEach((config, config_id) => {

    configurationsTable.forEach(({mc_start_x, mc_size_x, comp_id, index}) => {

        let geom_c = buildComponent(
            components[comp_id],
            mc_size_x,
            m_size_y,
            m_size_z,
            comp_id,
            backpanels_rows,
            mc_start_x,
            m_start_y,
            m_start_z,
            index === 0,
            index === configurationsTable.length - 1,
            setup.configs[index].config_id,
            {...setup.configs[index].constants, ...c_constants || {}},
        );

        /*TODO: Mirror
        if next(get_param('trans__mirror', mesh_config_params)):
            geom_c = mirror_x(geom_c, True)*/

        geom.push(...geom_c);

        plinthSettings.push({
            x1: mc_start_x,
            x2: mc_start_x + mc_size_x,
            plinth: getParam('plinth__value', setup.configs[index].parameters, c_constants, 0, mc_size_x, '')
        });

        /*NOTE: Try except
        except (MissingGeom, ValueError) as error:
            if len(error.args) == 9:
                geom += MissingGeom.get_missing_geom(*error.args[1:])
            else:
                print "Mesh EXCEPTION", error
            print traceback.print_exc()
        except Exception as e:
            print "Mesh EXCEPTION", e
            print traceback.print_exc()*/
    });

    /*TODO: Plinth
    geom = make_plinth(geom, plinth_settings, m_size_x - MAT_THICKNESS, m_size_z, MAT_THICKNESS,
                       int(serialized_data['mesh'][m_geom_id].get('plinth_h', 90)), data)*/

    geom = makePlinth(geom, plinthSettings, m_size_x - MAT_THICKNESS, m_size_z, MAT_THICKNESS,
        mesh.parameters, c_constants);

    if (getParam('trans__mirror', mesh.parameters, c_constants)[0]) {
        //console.log('mirror, mirror on the wall!');
        geom = mirrorGeometryX(geom);
    }

    return geom;
};

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- CONTAINERS ---- ///////////////////////////////////////////////////////////////////////////////////
const buildOldConfiguratorContainer = function getGeometryFromContainerWithOldConfiguratorProject(
    container, meshes, components,
    c_size_x, c_size_y, c_size_z, container_id, c_start_x = 0, c_start_y = 0, c_start_z = 0,
    old_configurator_params
) {
    // Zmienne na dobry poczatek
    let geom = [];
    let current_y = c_start_y;
    const door_proper_heights = [278, 398, 300, 400];
    const drawersMaxHeight = 1578;
    const setup = container.setups[c_size_y];
    const densityMode = container.params.density_mode;
    const distortionMode = container.params.distortion_mode;
    const {row_amount, row_heights, row_styles, motion, backpanels_rows} = old_configurator_params;
    const configs_amount = setup.configs.length;

    // Generowanie rzedow
    for (let row = 0; row < row_amount; row++) {
        const row_config = setup.configs[configs_amount > row ? row : row % configs_amount];
        const row_height = row_heights[row];
        let row_style = row_styles[row];
        // Podmiana stylu jeśli stary format zapisu
        row_style = row_style < 10 ? row_style + 10 : row_style;
        // Podmiana stylu drzwiowego jeśli wysokość nie_drzwiowa
        if (door_proper_heights.indexOf(row_height) < 0){
            row_style = Number(String(row_style)[0] + '1');
        }
        // Podmiana stylu szulfadowego jeśli za wysoko na szuflady
        if (current_y + row_height > drawersMaxHeight){
            row_style = Number('1' + String(row_style)[1]);
        }
        // Wczytywanie constants dla wypełnień na podstawie stylu rzędu
        let row_constants = {...row_config.parameters.row_style_table[row_style], ...row_config.constants};
        if (row_config.parameters.height.startsWith('#')){row_constants[row_config.parameters.height] = row_height;}

        const m_geom = buildMesh(
            meshes[row_config.parameters.mesh_id], components,
            c_size_x, row_config.parameters.mesh_setup, c_size_z, row_config.parameters.mesh_id, backpanels_rows,
            c_start_x, current_y, c_start_z, row_constants, motion, densityMode, distortionMode
            );

        geom.push(...m_geom);
        current_y = Math.max(...m_geom.map(o => o.y2));
    }


    return geom;
};



const buildContainer = function getGeometryFromContainer(
    container, meshes, components,
    c_size_x, c_size_y, c_size_z, container_id, c_start_x = 0, c_start_y = 0, c_start_z = 0,
    old_configurator_params=null
) {
    const mode = 'params' in container && 'container_mode' in container.params ? container.params.container_mode : null;
    switch(mode) {
        case 'old_configurator':
            return buildOldConfiguratorContainer(
                container, meshes, components, c_size_x, c_size_y, c_size_z, container_id,
                c_start_x, c_start_y, c_start_z, old_configurator_params);
        default:
            return null;
    }
};

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// //////////// ---- FUNKCJE GŁÓWNE ---- ///////////////////////////////////////////////////////////////////////////////

export const buildObjectRawGeometry = function buildObjectGeometryForGivenDimensions( // aka get_raw_geometry
    serialized_data, x, y, geom_id, geom_type, setup, z = 320, old_configurator_params=null,
) {
    smartLog(['log_flow'].some(k => k in (serialized_data[geom_type][geom_id].parameters || {})),
        `### buildObjectRawGeometry ${'#'.repeat(20)}`, false,
        [[serialized_data, x, y, z, geom_id, geom_type, setup]], true);

    const mapStylesByDepth = (style, depth) => {
        let isDrawer = style < 40 && style > 19
        if (isDrawer && depth === 240) return style - 20
        return style
    }

    let newStyles =  old_configurator_params.row_styles.map((s) => mapStylesByDepth(s, z))
    old_configurator_params.row_styles = [...newStyles]

    let geometry;
    if (geom_type === 'mesh') {
        geometry = buildMesh(
            serialized_data.mesh[geom_id], serialized_data.component, x, setup, z, geom_id, old_configurator_params.backpanels_rows)
    } else if (geom_type === 'container') {
        geometry = buildContainer(
            serialized_data.container[geom_id], serialized_data.mesh, serialized_data.component,
            x, setup, z, geom_id, 0, 0, 0, old_configurator_params);
    } else if (geom_type === 'component') {
        geometry = buildComponent(serialized_data.component[geom_id], x, setup, z, geom_id, old_configurator_params.backpanels_rows);
    } else {
        throw new Error('Nieznany typ geometrii!');
    }

    // catch (err){
    //     geometry = error_geometry(x, y, z, ':( GEOM','Get raw geometry error: [' + err.name + '] ' + err.message)
    //     // except (MissingGeom, ValueError) as error:
    //     //     if len(error.args) == 9:
    //     //         data = MissingGeom.get_missing_geom(*error.args[1:])
    //     //     else:
    //     //         print 'Geom ERROR', error
    //     //     print traceback.print_exc()
    //     // except Exception as e:
    //     //     print 'Geom EXCEPTION', e
    //     //     print traceback.print_exc()
    // }

    geometry = roundCoors(geometry);

    const groupByType = function groupObjectsByType(data, dim_x, dim_y, dim_z) {
        const data_out = {
            geometry: {},
            width: dim_x,
            height: dim_y,
            depth: dim_z,
        };
        Object.keys(ELEM_TYPE_CODENAMES).forEach((key) => {
            data_out.geometry[key] = data.filter(e => e.type === ELEM_TYPE_CODENAMES[key]);
        });
        return data_out;
    };

    geometry = groupByType(geometry, x, y, z);

    smartLog(['log_flow'].some(k => k in (serialized_data[geom_type][geom_id].parameters || {})),
        false, true, [[geometry]]);
    
    return geometry; // [geometry, axis_lines_x, axis_lines_y]
};
