import FurnitureProjectManager from '@libs/fpm-original';
import { buildObjectRawGeometry } from './dnaToolsWdBuild';
import { buildObjectFinalGeometry } from './dnaToolsWdSimplify';
import { convertToGalleryFormat, convertToProductionFormat } from './dnaToolsWdUtils';
import { processBestShelfSplit } from '../shelf-splitter';

const FPM = new FurnitureProjectManager();

export const get_elements_webdesigner = function buildSimplifyAndPrepareGeometryForTylkoWebdesigner(
  input_data, width, height, shelf_depth = 320, format = 'gallery', old_configurator_params = null,
) {
  // FIXME: tu nie powinno byc spacji, tymczasem... " 800"!
  height = String(height).replace(' ', '');

  // //////////////////////// DANE Z BE
  // input_data
  const serialization = JSON.parse(input_data.serialization);
  const { geom_id } = input_data;
  const { geom_type } = input_data;
  console.log(`DEKODER TYP ${geom_type}, ID ${geom_id}, X ${width} Y ${height}`);

  // ////////////////////// PART A
  const geom_a = buildObjectRawGeometry(
    serialization.serialization, width, height, geom_id, geom_type, height, shelf_depth, old_configurator_params,
  );
    //  ////////////////////// PART B
  const geom_b = buildObjectFinalGeometry(geom_a);

  //
  switch (format) {
  case 'gallery':
    return convertToGalleryFormat(geom_b);
  case 'raw':
    return geom_b;
  case 'production':
  default:
    return convertToProductionFormat(geom_b, -width / 2);
  }
};

export const get_geometry_for_post_payload = function getGeometryAndParametersForSVG(
  input_data, width, height, shelf_depth = 320, mode = 1,
) {
  let geom = {};
  let production_geom = {};
  let properMode = Number(mode) - 1;

  switch (properMode) {
  case 6:
    properMode = 1;
  case 0: // single simple
  case 1: // single with dimensions
  case 2: // single with components
    geom = get_elements_webdesigner(input_data, width, height, shelf_depth, 'raw');
    production_geom = convertToProductionFormat(geom, -width / 2);
    break;
  case 3: // all heights
  case 4: // all widths x all heights
  case 5: // all widths x all heights w/o dimensions
    geom = {};
    break;
  }

  return {
    mode: properMode,
    geom,
    production_geom,
    geom_id: input_data.geom_id,
    geom_type: input_data.geom_type,
    width,
    height,
  };
};

export const get_elements = function buildSimplifyAndPrepareGeometryForTylkoConfigurator(
  input_data, motion, width, row_amount, row_heights, row_styles, shelf_depth = 320,
  plyty_pol = 9, support_size = 125, snapping = false, shadow_settings = [2, 2, 4, 2],
  gen_row_styles_for_buttons = true, styles_slider = -1, backpanels_rows = null, shelf_type = 0,
  outputDataStructure = null,
) {
  // let wd_data = {
  //     'serialization': JSON.stringify(input_data),
  //     'geom_id': input_data['superior_object_ids'][0],
  //     'geom_type': input_data['superior_object_type'],
  // };

  const old_configurator_params = {
    shelf_type,
    row_amount,
    row_heights,
    row_styles,
    motion,
    backpanels_rows: backpanels_rows || [],
    width,
    geom_id: input_data.superior_object_ids[0],
    geom_type: input_data.superior_object_type,
    height: 300,
    depth: shelf_depth,
  };

  let geometry = FPM.withDNA(input_data)
    .calculateAndGetGeometry(old_configurator_params);

  // let raw_geometry = get_elements_webdesigner(wd_data, width, 1, shelf_depth, 'raw',
  //     old_configurator_params);
  // let geometry =  convertToGalleryFormat(
  //         raw_geometry, null, null, true, false, row_heights);


  if (outputDataStructure === 'flat') {
    geometry = { ...geometry, ...geometry.additional_elements };
    delete geometry.additional_elements;
  }

  processBestShelfSplit(geometry, 'GRID');
  return geometry;
};
