var dnaTools = require('../dnaTools.js');

describe("Dna tools - DekoderDNARows", function() {
    let d;
    let g;

    beforeEach(function () {
        d = [10, 20.0, '30', 40];
        g = [15.667, 29, 34, 45];
    });

    it("test_podstawowy", function () {
        let _obj = new dnaTools.Rows(d, g, 4);
        let output = [..._obj.bottom,..._obj.top];
        let test_result = [10, 20, 30, 40, 15, 29, 34, 45];
        expect(output).toEqual(test_result);
    });
});


describe("Dna tools - DekoderDNAMultipleObjectClass", function() {
    let obj_all_simple, shelf_rows_simple;

    beforeEach(function () {
        obj_all_simple = new dnaTools.MultitypeObject(10, 1, 0, 1, 1, 1, 1, 1, 1);
        shelf_rows_simple = new dnaTools.Rows([0, 0, 10, 20, 30, 40, 50, 60], [0, 10, 20, 30, 40, 50, 60], 6);

    });

    it("test_get_horizontal_points", function () {
        let output = obj_all_simple.get_horizontal_points(9);
        let test_result = [10];
        expect(output).toEqual(test_result);
    });

    it("test_get_door_object", function () {
        let output = obj_all_simple.get_door_object();
        expect(output instanceof dnaTools.Door).toEqual(true);
    });

    it("test_get_verticals_geometry", function () {
        let output = obj_all_simple.get_verticals_geometry(shelf_rows_simple, 9);
        let test_result = [{'y1': 9, 'x2': 10, 'x1': 10, 'y2': 1}];
        expect(output).toEqual(test_result);
    });

    it("test_get_support_geometry", function () {
        let output = obj_all_simple.get_support_geometry(shelf_rows_simple, 9, 125);
        let test_result = {'y2': 1, 'x2': 1, 'y1': 9, 'x1': -124, 'z1': 0, 'z2': 0};
        expect(output).toEqual(test_result);
    });

    it("test_get_legs_geometry", function () {
        let output = obj_all_simple.get_legs_geometry(shelf_rows_simple, 320);
        let test_result = [{'y1': -13, 'x1': 10, 'z1': 20}, {'y1': -13, 'x1': 10, 'z1': 300}];
        expect(output).toEqual(test_result);
    });

    it("test_gen_verticals_geometry", function () {
        let output = obj_all_simple.gen_verticals_geometry(10, [1, 2], shelf_rows_simple, 9);
        let test_result = [{'y1': 9, 'x2': 10, 'x1': 10, 'y2': 1}, {'y1': 19, 'x2': 10, 'x1': 10, 'y2': 11}];
        expect(output).toEqual(test_result);
    });

    it("test_gen_horizontal_geometry", function () {
        let output = obj_all_simple.gen_horizontal_geometry(10, 20, 123, 9);
        let test_result = {'y1': 123, 'x2': 29, 'x1': 1, 'y2': 123};
        expect(output).toEqual(test_result);
    });

    it("test_gen_legs_geometry", function () {
        let output = obj_all_simple.gen_legs_geometry(10, shelf_rows_simple, 320);
        let test_result = [{'y1': -13, 'x1': 10, 'z1': 20}, {'y1': -13, 'x1': 10, 'z1': 300}];
        expect(output).toEqual(test_result);
    });
});



describe("Dna tools - DekoderDNADoorsClass", function() {
    let door_obj_l,door_obj_r, shelf_rows_simple;

    beforeEach(function () {
        door_obj_l = new dnaTools.Door(10, 0, 1, 1);
        door_obj_r = new dnaTools.Door(1000, 1, 0, 1);
        shelf_rows_simple = new dnaTools.Rows([0, 0, 10, 20, 30, 40, 50, 60], [0, 10, 20, 30, 40, 50, 60], 6);
    });

    it("test_podstawowy_typ_0", function () {
        let output = door_obj_l.get_door_geometry(0, door_obj_r, shelf_rows_simple, 320, 9);
        let test_result = [{'y2': 0, 'x2': 990, 't': 0, 'y1': 10, 'x1': 20, 'z1': 319, 'z2': 301}];
        expect(output).toEqual(test_result);
    });

    it("test_podstawowy_typ_1", function () {
        let output = door_obj_l.get_door_geometry(1, door_obj_r, shelf_rows_simple, 320, 9);
        let test_result = [{'y2': -1, 'x2': 480, 't': 1, 'y1': 11, 'x1': 20, 'z1': 319, 'z2': 301},
                       {'y2': 0, 'x2': 565, 't': 0, 'y1': 10, 'x1': 445, 'z1': 300, 'z2': 282},
                       {'y2': -1, 'x2': 990, 't': 1, 'y1': 11, 'x1': 530, 'z1': 319, 'z2': 301}];
        expect(output).toEqual(test_result);
    });

    it("test_podstawowy_typ_2", function () {
        let output = door_obj_l.get_door_geometry(2, door_obj_r, shelf_rows_simple, 320, 9);
        let test_result = [{'y2': -1, 'x2': 480, 't': 1, 'y1': 11, 'x1': 20, 'z1': 319, 'z2': 301},
                       {'y2': 0, 'x2': 504, 't': 0, 'y1': 10, 'x1': 384, 'z1': 320, 'z2': 302},
                       {'y2': 0, 'x2': 626, 't': 0, 'y1': 10, 'x1': 506, 'z1': 320, 'z2': 302},
                       {'y2': -1, 'x2': 990, 't': 1, 'y1': 11, 'x1': 530, 'z1': 319, 'z2': 301}];
        expect(output).toEqual(test_result);
    });

    it("test_podstawowy_typ_3", function () {
        let output = door_obj_l.get_door_geometry(3, door_obj_r, shelf_rows_simple, 320, 9);
        let test_result = [{'y2': 0, 'x2': 625.472, 't': 0, 'y1': 10, 'x1': 20, 'z1': 319, 'z2': 301},
                       {'y2': 0, 'x2': 990, 't': 0, 'y1': 10, 'x1': 629.472, 'z1': 319, 'z2': 301}];
        expect(output).toEqual(test_result);
    });

});


describe("Dna tools - DekoderDNAGenerateObjects", function() {
    let json_object, shelf_rows_simple;
    beforeEach(function () {
        json_object = json_object = {
            "E": "e011211111", "M": [0, 0, 0, 10000, 9, 78], "L": [60, 70, 6, 7], "H": [10, 20, 1, 2], "A1": [],
            "DL": [4, 5], "V": [20, 30, 2, 3], "S": [30, 40, 3, 4], "R": [0, 1, 2, 3, 4, 5, 6, 7, 8],
            "P1": [[700, 350.0], [2400, 988.82]], "S1": "s00", "DR": [5, 6]};

        shelf_rows_simple = new dnaTools.Rows([0, 0, 10, 20, 30, 40, 50, 60], [0, 10, 20, 30, 40, 50, 60], 6);
    });

  it("test_podstawowy_v", function() {
        let output = new dnaTools.generate_objects(10, [0, 2, 4, 6, 8], json_object, 1450).map(x=> x.get_verticals_geometry(shelf_rows_simple, 9));
        let test_result = [[{'y1': 19, 'x2': 10, 'x1': 10, 'y2': 11}],
                       [{'y1': 39, 'x2': 10, 'x1': 10, 'y2': 31}],
                       [{'y1': 59, 'x2': 10, 'x1': 10, 'y2': 51}]];
        expect(output).toEqual(test_result);
  });
      it("test_podstawowy_s", function() {
        let output = new dnaTools.generate_objects(10, [0, 2, 4, 6, 8], json_object, 1450).map(x=> x.get_support_geometry(shelf_rows_simple, 9, 125));;
        let test_result = [{'y2': 11, 'x2': 19, 'y1': 19, 'x1': 144, 'z1': 0, 'z2': 0},
                       {'y2': 31, 'x2': 1, 'y1': 39, 'x1': -124, 'z1': 0, 'z2': 0},
                       {'y2': 51, 'x2': 19, 'y1': 59, 'x1': 144, 'z1': 0, 'z2': 0}];
        expect(output).toEqual(test_result);
  });
});


describe("Dna tools - DekoderDNAGenerateDoors", function() {
    let objects, shelf_rows_simple;
    beforeEach(function () {
        objects = [
            new dnaTools.MultitypeObject(10, 2, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(1000, 5, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(-340, 3, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(100, 3, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(300, 5, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(456, 2, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(110, 3, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(10, 2, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(-1234, 1, 0, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(2049, 3, 0, 0, 1, 0, 1, 1, 0)
        ];

        shelf_rows_simple = new dnaTools.Rows([0, 0, 10, 20, 30, 40, 50, 60], [0, 10, 20, 30, 40, 50, 60], 6);
    });

  it("test_podstawowy", function() {
        let output = dnaTools.generate_doors(objects, shelf_rows_simple, 320, 9, 2);
        let test_result = [{'y2': 10, 'x2': 140, 't': 0, 'y1': 20, 'x1': 20, 'z1': 320, 'z2': 302},
                       {'y2': 9, 'x2': 446, 't': 1, 'y1': 21, 'x1': 86, 'z1': 319, 'z2': 301},
                       {'y2': 20, 'x2': -210, 't': 0, 'y1': 30, 'x1': -330, 'z1': 320, 'z2': 302},
                       {'y2': 19, 'x2': 90, 't': 1, 'y1': 31, 'x1': -270, 'z1': 319, 'z2': 301},
                       {'y2': 39, 'x2': 570, 't': 1, 'y1': 51, 'x1': 310, 'z1': 319, 'z2': 301},
                       {'y2': 40, 'x2': 599, 't': 0, 'y1': 50, 'x1': 479, 'z1': 320, 'z2': 302},
                       {'y2': 40, 'x2': 721, 't': 0, 'y1': 50, 'x1': 601, 'z1': 320, 'z2': 302},
                       {'y2': 39, 'x2': 990, 't': 1, 'y1': 51, 'x1': 630, 'z1': 319, 'z2': 301}];
        expect(output).toEqual(test_result);
  });
});


describe("Dna tools - DekoderDNAGenerateLegs", function() {

  it("test_1", function() {
      let elements = [[-938, -407, -12, 472], [157, -535, -784]];
        let output = dnaTools.generate_legs(elements, dnaTools.get_row_coor(7, Array(8).fill(278.0)), 320);
        let test_result = [{'y1': -13, 'x1': -918, 'z1': 20}, {'y1': -13, 'x1': -918, 'z1': 300},
                       {'y1': -13, 'x1': -784, 'z1': 20}, {'y1': -13, 'x1': -784, 'z1': 300},
                       {'y1': -13, 'x1': -535, 'z1': 20}, {'y1': -13, 'x1': -535, 'z1': 300},
                       {'y1': -13, 'x1': 8, 'z1': 20}, {'y1': -13, 'x1': 8, 'z1': 300},
                       {'y1': -13, 'x1': 157, 'z1': 20}, {'y1': -13, 'x1': 157, 'z1': 300},
                       {'y1': -13, 'x1': 452, 'z1': 20}, {'y1': -13, 'x1': 452, 'z1': 300}];
        expect(test_result.length).toEqual(output.length);
        test_result.forEach((item,nr)=>{
          expect(item['x1']).toEqual(output[nr]['x1']);
          expect(item['y1']).toEqual(output[nr]['y1']);
          expect(item['z1']).toEqual(output[nr]['z1']);
        });

  });
  it("test_2", function() {
      let elements = [[-1200, 1200], [-1191, 231, 311, -541]];
        let output = dnaTools.generate_legs(elements, dnaTools.get_row_coor(7, Array(8).fill(278.0)), 320);
        let test_result = [{'y1': -13, 'x1': -861, 'z1': 20}, {'y1': -13, 'x1': -861, 'z1': 300},
                       {'y1': -13, 'x1': -1180, 'z1': 20}, {'y1': -13, 'x1': -1180, 'z1': 300},
                       {'y1': -13, 'x1': -155, 'z1': 20}, {'y1': -13, 'x1': -155, 'z1': 300},
                       {'y1': -13, 'x1': -541, 'z1': 20}, {'y1': -13, 'x1': -541, 'z1': 300},
                       {'y1': -13, 'x1': 705, 'z1': 20}, {'y1': -13, 'x1': 705, 'z1': 300},
                       {'y1': -13, 'x1': 231, 'z1': 20}, {'y1': -13, 'x1': 231, 'z1': 300},
                       {'y1': -13, 'x1': 1180, 'z1': 20}, {'y1': -13, 'x1': 1180, 'z1': 300}];
        expect(test_result.length).toEqual(output.length);
        test_result.forEach((item,nr)=>{
          expect(item['x1']).toEqual(output[nr]['x1']);
          expect(item['y1']).toEqual(output[nr]['y1']);
          expect(item['z1']).toEqual(output[nr]['z1']);
        });
  });
});


describe("Dna tools - DekoderDNAGenerateHorizontals", function() {
    let objects, shelf_rows_simple;
    beforeEach(function () {
        objects = [
            new dnaTools.MultitypeObject(10, 2, 0, 1, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(1000, 5, 0, 1, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(-340, 3, 0, 2, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(100, 3, 0, 1, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(300, 5, 0, 1, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(456, 2, 0, 1, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(110, 3, 0, 1, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(-400, 2, 0, 3, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(-1234, 1, 1, 0, 1, 0, 1, 1, 0),
            new dnaTools.MultitypeObject(2049, 3, 0, 1, 1, 0, 1, 1, 0)];

        shelf_rows_simple = new dnaTools.Rows([0, 0, 10, 20, 30, 40, 50, 60], [0, 10, 20, 30, 40, 50, 60], 6);
    });

  it("test_podstawowy", function() {
        let output = dnaTools.generate_horizontals(objects, shelf_rows_simple, 9);
        let test_result = [{'y1': 20, 'x2': -373, 'x1': -409, 'y2': 20}, {'y1': 20, 'x2': 465, 'x1': 1, 'y2': 20},
                       {'y1': 30, 'x2': -331, 'x1': -367, 'y2': 30}, {'y1': 30, 'x2': 119, 'x1': 91, 'y2': 30},
                       {'y1': 50, 'x2': 1009, 'x1': 291, 'y2': 50}];
        expect(output).toEqual(test_result);
  });
});


describe("Dna tools - DekoderDNAGetRowCoor", function() {
    let input_data;
    beforeEach(function () {
        input_data = [210, 100, 210, 300, 234, 123, 23, 90, 23456];
    });
  it("test_len", function() {
        let output = dnaTools.get_row_coor(4, input_data).top.length;
        let test_result = 5;
        expect(output).toBe(test_result);
  });

  it("test_len2", function() {
        let output = dnaTools.get_row_coor(5, input_data).bottom.length;
        let test_result = 6;
        expect(output).toBe(test_result);
  });

  it("test_bottom", function() {
        let output = dnaTools.get_row_coor(8, input_data).bottom;
        let test_result = [0, 0, 210, 310, 520, 820, 1054, 1177, 1200];
        expect(output).toEqual(test_result);
  });

  it("test_top", function() {
        let output = dnaTools.get_row_coor(6, input_data).top;
        let test_result = [0, 210, 310, 520, 820, 1054, 1177];
        expect(output).toEqual(test_result);
  });

});



describe("Dna tools - DekoderDNACheckPosOccurrence", function() {
  it("test_middle", function() {
        let output = dnaTools.check_pos_occurrence(1000, [20, 50], -45.5);
        let test_result = -46;
        expect(output).toBe(test_result);
  });
  it("test_middle2", function() {
        let output = dnaTools.check_pos_occurrence(500, [20, 50], -45.4);
        let test_result = -45;
        expect(output).toBe(test_result);
  });
      it("test_left", function() {
        let output = dnaTools.check_pos_occurrence(1000, [0, 0], -500.2);
        let test_result = -500;
        expect(output).toBe(test_result);
  });
      it("test_left_min", function() {
        let output = dnaTools.check_pos_occurrence(1000, [32, 0], -468.6);
        let test_result = null;
        expect(output).toBe(test_result);
  });
      it("test_left_out", function() {
        let output = dnaTools.check_pos_occurrence(1000, [10, 0], -490.9);
        let test_result = null;
        expect(output).toBe(test_result);
  });
      it("test_right", function() {
        let output = dnaTools.check_pos_occurrence(800, [0, 0], 400.4);
        let test_result = 400;
        expect(output).toBe(test_result);
  });
      it("test_right_min", function() {
        let output = dnaTools.check_pos_occurrence(800, [0, 50], 350.4);
        let test_result = 350;
        expect(output).toBe(test_result);
  });
      it("test_right_out", function() {
        let output = dnaTools.check_pos_occurrence(800, [0, 50], 350.6);
        let test_result = null;
        expect(output).toBe(test_result);
  });
});

describe("Dna tools - DekoderDNAGetPosFromTable", function() {
    let tab1;
    let tab2;
    beforeEach(function () {
        tab1 = [[0, 10],
            [100, 700],
            [780, 0],
            [880, 100],
            [999, 234]];

        tab2 = [[0.1, 30],
            [0.4, 30],
            [0.569, 300],
            [0.9, 400]];
    });

  it("test_parametr_poczatkowy", function() {
        let output = dnaTools.get_pos_from_table(0.0, tab1);
        let test_result = 10;
        expect(output).toBe(test_result);
  });
      it("test_parametr_koncowy", function() {
        let output = dnaTools.get_pos_from_table(999.0, tab1);
        let test_result = 234;
        expect(output).toBe(test_result);
  });
      it("test_parametr_z_tablicy", function() {
        let output = dnaTools.get_pos_from_table(880.0, tab1);
        let test_result = 100;
        expect(output).toBe(test_result);
  });
      it("test_parametr_z_proporcji", function() {
        let output = dnaTools.get_pos_from_table(830.0, tab1);
        let test_result = 50;
        expect(output).toBe(test_result);
  });
      it("test_parametr_poza_zakresem", function() {
        let output = dnaTools.get_pos_from_table(1001, tab1);
        let test_result = null;
        expect(output).toBe(test_result);
  });
      it("test_parametr_z_tablicy_motion", function() {
        let output = dnaTools.get_pos_from_table(0.569, tab2);
        let test_result = 300;
        expect(output).toBe(test_result);
  });
      it("test_parametr_z_proporcji_motion", function() {
        let output = dnaTools.get_pos_from_table(0.22222, tab2);
        let test_result = 30;
        expect(output).toBe(test_result);
  });
      it("test_parametr_poza_zakresem_motion", function() {
        let output = dnaTools.get_pos_from_table(0.03, tab2);
        let test_result = null;
        expect(output).toBe(test_result);
  });
});


describe("Dna tools - DekoderDNAGetBasePos", function() {
    let tab1;
    let tab2;
        beforeEach(function () {
        tab1 = [[0, 10],
                     [100, 700],
                     [780, 0],
                     [800, 400],
                     [880, 440],
                     [999, 234]];

        tab2 = [[0.1, 30],
                     [0.4, 30],
                     [0.569, 300],
                     [0.9, 400]];
    });

  it("test_width", function() {
        let output = dnaTools.get_base_pos(1000, 100.0, tab1);
        let test_result = 200;
        expect(output).toBe(test_result);
  });
  it("test_width_from_proportion_0", function() {
        let output = dnaTools.get_base_pos(820, 820.0, tab1);
        let test_result = 0;
        expect(output).toBe(test_result);
  });
      it("test_width_from_proportion", function() {
        let output = dnaTools.get_base_pos(790, 790.0, tab1);
        let test_result = -195;
        expect(output).toBe(test_result);
  });
      it("test_motion", function() {
        let output = dnaTools.get_base_pos(500, 0.4, tab2);
        let test_result = -220;
        expect(output).toBe(test_result);
  });
          it("test_motion_from_proportion_0", function() {
        let output = dnaTools.get_base_pos(500, 0.4, tab2);
        let test_result = -220;
        expect(output).toBe(test_result);
  });
          it("test_motion_from_proporion", function() {
        let output = dnaTools.get_base_pos(660, 0.6683, tab2);
        let test_result = 0;
        expect(output).toBe(test_result);
  });
          it("test_out_value", function() {
        let output = dnaTools.get_base_pos(500, 0.95, tab2);
        let test_result = null;
        expect(output).toBe(test_result);
  });

});

describe("Dna tools - DekoderDNAGetAdjustment", function() {
    let tab1;
    let tab2;
    let tab3;
    beforeEach(function () {
        tab1 = [[0, 10],
            [800, 410]];

        tab2 = [[0.1, 0],
            [0.4, 30]];

        tab3 = [[0.2, 0.1],
            [0.5, 0.2]];
    });

  it("test_no_adjustment", function() {
        let output = dnaTools.get_adjustment(0, 400, tab1, "S00", 9);
        let test_result = 0;
        expect(output).toBe(test_result);
  });
  it("test_width", function() {
        let output = dnaTools.get_adjustment(0, 400, tab1, "S01", 9);
        let test_result = 210;
        expect(output).toBe(test_result);
  });
      it("test_motion", function() {
        let output = dnaTools.get_adjustment(0, 0.2, tab2, "S01", 9);
        let test_result = 10;
        expect(output).toBe(test_result);
  });
       it("test_prop1", function() {
        let output = dnaTools.get_adjustment(700, 0.2, tab3, "S02",9);
        let test_result = 68.2;
        expect(output).toBe(test_result);
  });
          it("test_prop2", function() {
        let output = dnaTools.get_adjustment(1500, 0.4, tab3, "S02", 9);
        let test_result = 247.00000000000003;
        expect(output).toBe(test_result);
  });
          it("test_prop3", function() {
        let output = dnaTools.get_adjustment(700, 0.1, tab3, "S02", 9);
        let test_result = null;
        expect(output).toBe(test_result);
  });
      it("test_inproper_system", function() {
        let output = dnaTools.get_adjustment(0, 400, tab1, "S03", 9);
        let test_result = null;
        expect(output).toBe(test_result);
  });
});


describe("Dna tools - DekoderDNACheckElementOccurrence", function() {
    let proper_rows;
    let styles;
    beforeEach(function () {
        proper_rows = [[0, 1, 2, 3],
                            [0, 1, 2],
                            [3]];
        styles =  [0, 0, 1, 2, 0, 0, 1];
    });
  it("test_typeA_styleA", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [2], 0, 0);
        let test_result = [2];
        expect(output).toEqual(test_result);
  });

  it("test_typeA_styleB", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [2], 0, 1);
        let test_result = [2];
        expect(output).toEqual(test_result);
  });

  it("test_typeA_styleX", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [2], 0, 2);
        let test_result = [];
        expect(output).toEqual(test_result);
  });

  it("test_typeB_styleA", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [2], 1, 0);
        let test_result = [2];
        expect(output).toEqual(test_result);
  });

  it("test_typeX_styleA", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [2], 2, 0);
        let test_result = [];
        expect(output).toEqual(test_result);
  });

  it("test_typeC_styleA", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [3], 2, 0);
        let test_result = [3];
        expect(output).toEqual(test_result);
  });

  it("test_multi_styleA", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [0, 1, 2, 3, 4, 5, 6, 7, 8], 0, 0);
        let test_result = [0,1,2,3];
        expect(output).toEqual(test_result);
  });

  it("test_multi_styleB", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [0, 1, 2, 3, 4, 5, 6, 7, 8], 0, 1);
        let test_result = [2];
        expect(output).toEqual(test_result);
  });

  it("test_multi_styleB_topB", function() {
        let output = dnaTools.check_element_occurrence(proper_rows, styles, [0, 1, 2, 3, 4, 5, 6, 7, 8], 2, 2);
        let test_result = [3];
        expect(output).toEqual(test_result);
  });
});


describe("Dna tools - DekoderDNAGetElementPos", function() {
    let lewy_staly;
    let srodkowy_motion;
    let nieznany_motion;
    beforeEach(function () {
        lewy_staly =["s00", [9, 9], [[700.0, 9], [2400.0, 9]], [], 9];
        srodkowy_motion = ["s11", [9, 60], [["0.024", 876], ["0.432", 1541], ["1.000", 1700]], [[645, -580], [1350, -346], [2300, 0]],9];
        nieznany_motion = ["s30", [9, 60], [["0.024", 876], ["0.432", 1541], ["1.000", 1700]], [[645, -580], [1350, -346], [2300, 0]],9];
    });
  it("test_lewy_skrajny_min", function() {
        let output = dnaTools.get_element_pos(700, 0.28,...lewy_staly);
        let test_result = -341;
        expect(output).toBe(test_result);
  });

  it("test_lewy_skrajny_max", function() {
        let output = dnaTools.get_element_pos(2400, 0,...lewy_staly);
        let test_result = -1191;
        expect(output).toBe(test_result);
  });

  it("test_srodkowy_poza_zakresem", function() {
        let output = dnaTools.get_element_pos(700, 0.26,...srodkowy_motion);
        let test_result = null;
        expect(output).toBe(test_result);
  });

  it("test_srodkowy_poza_zakresem_motion", function() {
        let output = dnaTools.get_element_pos(1400, 0.02,...srodkowy_motion);
        let test_result = null;
        expect(output).toBe(test_result);
  });

  it("test_srodkowy_poza_zakresem_motion2", function() {
        let output = dnaTools.get_element_pos(1400, 0.9,...srodkowy_motion);
        let test_result = null;
        expect(output).toBe(test_result);
  });

  it("test_srodkowy_poza_zakresem_szer", function() {
        let output = dnaTools.get_element_pos(2400, 0.9,...srodkowy_motion);
        let test_result = null;
        expect(output).toBe(test_result);
  });

  it("test_srodkowy", function() {
        let output = dnaTools.get_element_pos(1710, 0.6,...srodkowy_motion);
        let test_result = 518;
        expect(output).toBe(test_result);
  });

  it("test_nieznany", function() {
        let output = dnaTools.get_element_pos(1710, 0.6,...nieznany_motion);
        let test_result = null;
        expect(output).toBe(test_result);
  });
  it("test_slawek", function() {
        let output = dnaTools.get_element_pos(2000, 0.50, 's02', [9, 9], [[700, 442.69], [2400, 659.31]],
                                    [['0.000', '-0.04964'], ['1.000', '0.00000'], ['1.343', '-0.25186']], 9);
        let test_result = -441;
        expect(output).toBe(test_result);
  });

});


describe("Dna tools - DekoderDNAElementsSnapping", function() {
    let elements, shelf_rows;
    beforeEach(function () {
        elements = [
            new dnaTools.MultitypeObject(-604, 1, 0, 0, 1, 0, 0, 0, 0),
            new dnaTools.MultitypeObject(-608, 2, 1, 0, 1, 0, 0, 0, 0),
            new dnaTools.MultitypeObject(-579, 3, 0, 0, 1, 0, 0, 0, 0),
            new dnaTools.MultitypeObject(-599, 4, 0, 0, 1, 0, 0, 0, 0),
            new dnaTools.MultitypeObject(-592, 5, 1, 0, 1, 0, 0, 0, 0),
            new dnaTools.MultitypeObject(-604, 6, 0, 0, 1, 0, 0, 0, 0),
            new dnaTools.MultitypeObject(-608, 7, 0, 0, 1, 0, 0, 0, 0),
            new dnaTools.MultitypeObject(-604, 8, 0, 0, 1, 0, 0, 0, 0)];

        shelf_rows = dnaTools.get_row_coor(8, Array(8).fill(278.0));
    });

  it("test_main", function() {
      dnaTools.elements_snapping(1,elements,8,30);
        let output = elements.map(obj=>obj.get_verticals_geometry(shelf_rows, 9));
        let test_result = [[{'y1': 9, 'x2': -608, 'x1': -608, 'y2': 269}], [{'y1': 287, 'x2': -608, 'x1': -608, 'y2': 547}],
                       [{'y1': 565, 'x2': -608, 'x1': -608, 'y2': 825}],
                       [{'y1': 843, 'x2': -608, 'x1': -608, 'y2': 1103}],
                       [{'y1': 1121, 'x2': -592, 'x1': -592, 'y2': 1381}],
                       [{'y1': 1399, 'x2': -592, 'x1': -592, 'y2': 1659}],
                       [{'y1': 1677, 'x2': -592, 'x1': -592, 'y2': 1937}],
                       [{'y1': 1955, 'x2': -592, 'x1': -592, 'y2': 2215}]];
        expect(output).toEqual(test_result);
  });
});




describe("Dna tools - DekoderDNAGenerateElementsSimplifedData", function() {
  it("test_simple", function() {
        let geom = {'horizontals':[{'y2':556, 'y1':556, 'x1':-810, 'x2':810}, {'y2':834, 'y1':834, 'x1':-810, 'x2':810}],
                    'verticals':[{'y2':825, 'y1':565, 'x1':801, 'x2':801}, {'y2':825, 'y1':565, 'x1':-801, 'x2':-801}]};
        let output = dnaTools.generate_elements_simplified_data(geom, dnaTools.get_row_coor(3, Array(8).fill(278.0)));
        let test_result = [[[], []],
                       [[], []],
                       [[-810, 810], []],
                       [[-810, 810], [-801, 801]]];
        expect(test_result).toEqual(output);
  });

  it("test_main", function() {
        let geom = {"additional_elements": {
            "shadow_right": [{"y2": 1103, "y1": 843, "x1": 655, "x2": 780, "z1": 0, "z2": 320},
                             {"y2": 1937, "y1": 1677, "x1": 655, "x2": 780, "z1": 0, "z2": 320}],
            "shadow_side": [{"y2": 269, "y1": 9, "x1": -784, "x2": -776, "z1": 0, "z2": 320},
                            {"y2": 547, "y1": 287, "x1": -784, "x2": -776, "z1": 0, "z2": 320},
                            {"y2": 1103, "y1": 843, "x1": -784, "x2": -776, "z1": 0, "z2": 320},
                            {"y2": 1381, "y1": 1121, "x1": -784, "x2": -776, "z1": 0, "z2": 320},
                            {"y2": 1937, "y1": 1677, "x1": -784, "x2": -776, "z1": 0, "z2": 320}],
            "shadow_left": [{"y2": 825, "y1": 565, "x1": -780, "x2": -655, "z1": 0, "z2": 320},
                            {"y2": 1659, "y1": 1399, "x1": -780, "x2": -655, "z1": 0, "z2": 320}],
            "shadow_middle": [{"y2": 269, "y1": 9, "x1": -762, "x2": -237, "z1": 0, "z2": 320},
                              {"y2": 269, "y1": 9, "x1": -219, "x2": 442, "z1": 0, "z2": 320},
                              {"y2": 269, "y1": 9, "x1": 460, "x2": 762, "z1": 0, "z2": 320},
                              {"y2": 547, "y1": 287, "x1": -762, "x2": -310, "z1": 0, "z2": 320},
                              {"y2": 825, "y1": 287, "x1": -292, "x2": 149, "z1": 0, "z2": 320},
                              {"y2": 547, "y1": 287, "x1": 167, "x2": 442, "z1": 0, "z2": 320},
                              {"y2": 547, "y1": 287, "x1": 460, "x2": 762, "z1": 0, "z2": 320},
                              {"y2": 825, "y1": 565, "x1": -637, "x2": -310, "z1": 0, "z2": 320},
                              {"y2": 825, "y1": 565, "x1": 167, "x2": 762, "z1": 0, "z2": 320},
                              {"y2": 1103, "y1": 843, "x1": -762, "x2": -459, "z1": 0, "z2": 320},
                              {"y2": 1103, "y1": 843, "x1": -441, "x2": 202, "z1": 0, "z2": 320},
                              {"y2": 1103, "y1": 843, "x1": 220, "x2": 637, "z1": 0, "z2": 320},
                              {"y2": 1381, "y1": 1121, "x1": -762, "x2": -188, "z1": 0, "z2": 320},
                              {"y2": 1381, "y1": 1121, "x1": -170, "x2": 202, "z1": 0, "z2": 320},
                              {"y2": 1381, "y1": 1121, "x1": 220, "x2": 762, "z1": 0, "z2": 320},
                              {"y2": 1659, "y1": 1399, "x1": -637, "x2": -419, "z1": 0, "z2": 320},
                              {"y2": 1937, "y1": 1399, "x1": -401, "x2": 41, "z1": 0, "z2": 320},
                              {"y2": 1659, "y1": 1399, "x1": 59, "x2": 762, "z1": 0, "z2": 320},
                              {"y2": 1937, "y1": 1677, "x1": -762, "x2": -419, "z1": 0, "z2": 320},
                              {"y2": 1937, "y1": 1677, "x1": 59, "x2": 637, "z1": 0, "z2": 320}]},
            "horizontals": [{"y2": 0, "y1": 0, "x1": -780, "x2": 780},
                            {"y2": 278, "y1": 278, "x1": -780, "x2": 780},
                            {"y2": 556, "y1": 556, "x1": -780, "x2": -293},
                            {"y2": 556, "y1": 556, "x1": 149, "x2": 780},
                            {"y2": 834, "y1": 834, "x1": -780, "x2": 780},
                            {"y2": 1112, "y1": 1112, "x1": -780, "x2": 780},
                            {"y2": 1390, "y1": 1390, "x1": -780, "x2": 780},
                            {"y2": 1668, "y1": 1668, "x1": -780, "x2": -401},
                            {"y2": 1668, "y1": 1668, "x1": 41, "x2": 780},
                            {"y2": 1946, "y1": 1946, "x1": -780, "x2": 780}],
            "supports": [{"y2": 1103, "y1": 843, "x1": 77, "x2": 202, "z1": 0, "z2": 0},
                         {"y2": 1103, "y1": 843, "x1": 512, "x2": 637, "z1": 0, "z2": 0},
                         {"y2": 1381, "y1": 1121, "x1": -762, "x2": -637, "z1": 0, "z2": 0},
                         {"y2": 1659, "y1": 1399, "x1": 59, "x2": 184, "z1": 0, "z2": 0},
                         {"y2": 269, "y1": 9, "x1": -362, "x2": -237, "z1": 0, "z2": 0},
                         {"y2": 1937, "y1": 1677, "x1": -762, "x2": -637, "z1": 0, "z2": 0},
                         {"y2": 1659, "y1": 1399, "x1": 637, "x2": 762, "z1": 0, "z2": 0},
                         {"y2": 269, "y1": 9, "x1": 317, "x2": 442, "z1": 0, "z2": 0},
                         {"y2": 547, "y1": 287, "x1": -762, "x2": -637, "z1": 0, "z2": 0},
                         {"y2": 825, "y1": 565, "x1": -435, "x2": -310, "z1": 0, "z2": 0}],
            "verticals": [{"y2": 1381, "y1": 1121, "x1": -179, "x2": -179},
                          {"y2": 547, "y1": 287, "x1": -771, "x2": -771},
                          {"y2": 1103, "y1": 843, "x1": 646, "x2": 646},
                          {"y2": 1659, "y1": 1399, "x1": 771, "x2": 771},
                          {"y2": 1659, "y1": 1399, "x1": -410, "x2": -410},
                          {"y2": 1937, "y1": 1677, "x1": -410, "x2": -410},
                          {"y2": 547, "y1": 287, "x1": 158, "x2": 158},
                          {"y2": 825, "y1": 565, "x1": 158, "x2": 158},
                          {"y2": 1937, "y1": 1677, "x1": -771, "x2": -771},
                          {"y2": 1937, "y1": 1677, "x1": 646, "x2": 646},
                          {"y2": 547, "y1": 287, "x1": 771, "x2": 771},
                          {"y2": 1103, "y1": 843, "x1": -450, "x2": -450},
                          {"y2": 825, "y1": 565, "x1": -646, "x2": -646},
                          {"y2": 547, "y1": 287, "x1": -301, "x2": -301},
                          {"y2": 825, "y1": 565, "x1": -301, "x2": -301},
                          {"y2": 1103, "y1": 843, "x1": -771, "x2": -771},
                          {"y2": 1103, "y1": 843, "x1": 211, "x2": 211},
                          {"y2": 1381, "y1": 1121, "x1": 211, "x2": 211},
                          {"y2": 1659, "y1": 1399, "x1": -646, "x2": -646},
                          {"y2": 269, "y1": 9, "x1": -771, "x2": -771}, {"y2": 269, "y1": 9, "x1": 771, "x2": 771},
                          {"y2": 269, "y1": 9, "x1": 451, "x2": 451}, {"y2": 547, "y1": 287, "x1": 451, "x2": 451},
                          {"y2": 825, "y1": 565, "x1": 771, "x2": 771},
                          {"y2": 269, "y1": 9, "x1": -228, "x2": -228},
                          {"y2": 1381, "y1": 1121, "x1": 771, "x2": 771},
                          {"y2": 1659, "y1": 1399, "x1": 50, "x2": 50},
                          {"y2": 1937, "y1": 1677, "x1": 50, "x2": 50},
                          {"y2": 1381, "y1": 1121, "x1": -771, "x2": -771}]};
        let output = dnaTools.generate_elements_simplified_data(geom, dnaTools.get_row_coor(7, Array(8).fill(278.0)))
        let test_result = [[[-780, 780], []], [[-780, 780], [-771, -228, 451, 771]],
                       [[-780, -293, 149, 780], [-771, -301, 158, 451, 771]], [[-780, 780], [-646, -301, 158, 771]],
                       [[-780, 780], [-771, -450, 211, 646]], [[-780, 780], [-771, -179, 211, 771]],
                       [[-780, -401, 41, 780], [-646, -410, 50, 771]], [[-780, 780], [-771, -410, 50, 646]]];
        expect(test_result).toEqual(output);
        /*
        test_result.forEach((row,i) => {
            expect(test_result[i].length).toEqual(output[i].length);
            row.forEach((row_entry,j) => {
                expect(test_result[i][j].length).toEqual(output[i][j].length);
                row_entry.forEach((row_entry_deep,k) => {
                    expect(test_result[i][j][k]).toEqual(output[i][j][k]);
                })
            })
        });
        */

  });
    it("test_double_height", function() {
        let geom = {'legs': [], 'horizontals': [],
                'supports': [{'y2': 547, 'y1': 287, 'x1': -328, 'x2': -203, 'z1': 0, 'z2': 0},
                             {'y2': 825, 'y1': 287, 'x1': -60, 'x2': -185, 'z1': 0, 'z2': 0}],
                'verticals': [{'y2': 825, 'y1': 287, 'x1': 79, 'x2': 79},
                              {'y2': 825, 'y1': 287, 'x1': -561, 'x2': -561},
                              {'y2': 547, 'y1': 287, 'x1': -194, 'x2': -194}]};
        let output = dnaTools.generate_elements_simplified_data(geom, dnaTools.get_row_coor(3, Array(8).fill(278.0)),9);
        let test_result = [[[], []], [[], []], [[-570, -552, 70, 88], [-561, -194, 79]], [[-570, -552, 70, 88], [-561, 79]]];
        expect(output).toEqual(test_result);
        /*
        test_result.forEach((row,i) => {
            expect(test_result[i].length).toEqual(output[i].length);
            row.forEach((row_entry,j) => {
                expect(test_result[i][j].length).toEqual(output[i][j].length);
                row_entry.forEach((row_entry_deep,k) => {
                    expect(test_result[i][j][k]).toEqual(output[i][j][k]);
                })
            })
        });
        */

  });

});





describe("Dna tools - DekoderDNAGetShadowsOpenings", function() {

    it("test_simple", function () {
        let elements = [[[-500, 500], []], [[-500, 500], [-211, 491, -491]], [[-500, -75, 186, 500], [-84, 491, -491, 195]],
                    [[-500, 500], [-84, 491, -491, 195]]];
        let output = dnaTools.get_shadows_openings(elements, 1000, 9);
        let test_result = [[[], []], [[], [[-482, -220], [-202, 482]]],
                       [[[-75, 186]], [[-482, -93], [-75, 186], [204, 482]]],
                       [[], [[-482, -93], [-75, 186], [204, 482]]]];
        expect(test_result.length).toEqual(output.length);

        Object.keys(test_result).forEach((key) => {
            expect(test_result[key]).toEqual(output[key]);
        });
    });
    it("test_main", function () {
        let elements = [[[-1000, 1000], []], [[-1000, 411, 754, 1000], [402, -991, 991, -379, 763]],
                    [[-1000, -464, -38, 1000], [-29, -991, 402, 763, 991, -473]],
                    [[-1000, 1000], [-29, 991, -866, 765, -473]], [[-1000, 62, 428, 1000], [437, 53, -597, 866, -991]],
                    [[-1000, 1000], [437, -991, 53, -352, 991]],
                    [[-1000, -553, -114, 1000], [507, 991, -866, -105, -562]],
                    [[-1000, 1000], [866, 507, -991, -105, -562]], [[-1000, 1000], [-991, 991, -105, 688]]];
        let output = dnaTools.get_shadows_openings(elements, 2000, 9);
        let test_result = [[[], []], [[[411, 754]], [[-982, -388], [-370, 393], [411, 754], [772, 982]]],
                       [[[-464, -38]], [[-982, -482], [-464, -38], [-20, 393], [411, 754], [772, 982]]],
                       [[], [[-1000, -875], [-857, -482], [-464, -38], [-20, 756], [774, 982]]],
                       [[[62, 428]], [[-982, -606], [-588, 44], [62, 428], [446, 857], [875, 1000]]],
                       [[], [[-982, -361], [-343, 44], [62, 428], [446, 982]]],
                       [[[-553, -114]], [[-1000, -875], [-857, -571], [-553, -114], [-96, 498], [516, 982]]],
                       [[], [[-982, -571], [-553, -114], [-96, 498], [516, 857], [875, 1000]]],
                       [[], [[-982, -114], [-96, 679], [697, 982]]]];
        expect(test_result.length).toEqual(output.length);

        Object.keys(test_result).forEach((key) => {
            expect(test_result[key]).toEqual(output[key]);
        });
    });
});


describe("Dna tools - DekoderDNACheckMultirowShadow", function() {

    it("test_one_row", function () {
        let rows = dnaTools.get_row_coor(7, Array(8).fill(278.0));
        let openings = [[[], []], [[], [(-932, 932)]], [[(-932, 932)], [(-932, 932)]]];
        let output = dnaTools.check_multirow_shadow(-932, 932, 1, openings, rows);
        let test_result = 269;
        expect(test_result).toEqual(output);
    });

    it("test_two_rows", function () {
        let rows = dnaTools.get_row_coor(7, Array(8).fill(278.0));
        let openings = [[[], []], [[[-932, 932]], [[-932, 932]]], [[[-932, 932]], [[-932, 932]]]];
        let output = dnaTools.check_multirow_shadow(-932, 932, 1, openings, rows);
        let test_result = 547;
        expect(test_result).toEqual(output);
    });

    it("test_multi_rows_with_toleracne", function () {
        let rows = dnaTools.get_row_coor(7, Array(8).fill(278.0));
        let openings = [[[], []], [[[-932, 932]], [[-931, 933]]], [[[-932, 931]], [[-933, 932]]],
                    [[[-933.4, 931.1]], [[-930.8, 931]]], [[[-932, 932]], [[-932, 932]]]];
        let output = dnaTools.check_multirow_shadow(-932, 932, 1, openings, rows);
        let test_result = 1103;
        expect(test_result).toEqual(output);
    });

    it("test_multi_rows_with_wrong_toleracne", function () {
        let rows = dnaTools.get_row_coor(7, Array(8).fill(278.0));
        let openings = [[[], []], [[[-932, 932]], [[-931, 933]]], [[[-932, 931]], [[-933, 932]]],
                    [[[-933.4, 931.1]], [[-929, 931]]], [[[-932, 932]], [[-932, 932]]]];
        let output = dnaTools.check_multirow_shadow(-932, 932, 1, openings, rows);
        let test_result = 547;
        expect(test_result).toEqual(output);
    });
});


describe("Dna tools - DekoderDNAGetShadowsGeometry", function() {

    it("test_simple", function () {
        let rows = dnaTools.get_row_coor(7, Array(8).fill(278.0));
        let openings = [[[], []], [[], []], [[[-810, -500], [500, 810]], []],
                    [[[-810, -500], [500, 810]], [[-500, -810], [-792, 792], [810, 500]]], [[], []], [[], []], [[], []],
                    [[], []]];
        let output = dnaTools.get_shadows_geometry(openings, rows, 1000, 9, 320);
        let test_result = {'shadow_middle': [{'y2': 825, 'x2': 792, 'y1': 565, 'x1': -792, 'z1': 0, 'z2': 320}],
                       'shadow_right': [{'y2': 825, 'x2': 500, 'y1': 565, 'x1': 810, 'z1': 0, 'z2': 320}],
                       'shadow_side': [],
                       'shadow_left': [{'y2': 825, 'x2': -810, 'y1': 565, 'x1': -500, 'z1': 0, 'z2': 320}]};

        Object.keys(test_result).forEach((key) => {
            test_result[key].forEach((item, nr) => {
                expect(item['x1']).toEqual(output[key][nr]['x1']);
                expect(item['y1']).toEqual(output[key][nr]['y1']);
                expect(item['z1']).toEqual(output[key][nr]['z1']);
            });
        });

    });
});


//didnt changed yet, still not
describe("Dna tools - DekoderDNAGetElements", function() {
    let maxDiff;

    beforeEach(function () {
        maxDiff = null;
    });
  it("test_new_chaos", function() {
      let dna = {"doors_type": 0, "legs": 1, "groups": {}, "snapping": 30, "elements": {
            "0": {"R": [0], "E": "e010100000", "A1": [], "P1": [[700, 9.0], [2400, 9.0]], "S1": "s00",
                  "M": [0, 0, 0, 10000, 0, 100]},
            "1": {"R": [0], "E": "e010100000", "A1": [], "P1": [[700, 691.0], [2400, 2391.0]], "S1": "s00",
                  "M": [0, 0, 0, 10000, 0, 100]},
            "2": {"R": [1], "E": "e010110000", "A1": [], "P1": [[700, 9.0], [2400, 9.0]], "S1": "s00",
                  "M": [0, 0, 0, 10000, 0, 100]},
            "3": {"R": [1], "E": "e010110000", "A1": [], "P1": [[700, 691.0], [2400, 2391.0]], "S1": "s00",
                  "M": [0, 0, 0, 10000, 0, 100]}, "4": {"R": [1], "E": "e010011000",
                                                        "A1": [[0.0, -0.0, 5], [0.68200000000000005, -0.0, 5],
                                                               [1.0, -0.0, 5], [2.685, 1.0, 5]],
                                                        "P1": [[700, 494.75], [2400, 659.31]], "S1": "s02",
                                                        "M": [0, 0, 0, 10000, 0, 100]},
            "5": {"R": [1, 2], "E": "e010110000",
                  "A1": [[0.34999999999999998, -0.0, 5], [0.437, -0.0, 5], [0.55000000000000004, -0.0, 5],
                         [1.0, -0.0, 5], [2.679, 0.0, 5]], "H": [false, false, 1],
                  "P1": [[1918, 1745.38], [2066, 1798.68], [2400, 1822.82]], "S1": "s02", "V": [false, false, 1, 2],
                  "M": [0, 0, 0, 10000, 0, 100]}, "6": {"R": [1, 2], "E": "e010100000",
                                                        "A1": [[0.0, -0.0, 5], [0.35199999999999998, -0.0, 5],
                                                               [1.0, -0.0, 5], [2.576, -0.0, 5]],
                                                        "H": [false, false, 1],
                                                        "P1": [[1918, 1407.85], [2066, 1418.91], [2400, 1442.82]],
                                                        "S1": "s02", "M": [0, 0, 0, 10000, 35, 100]},
            "7": {"R": [1, 2], "E": "e010011000",
                  "A1": [[0.0, -0.0, 5], [0.35199999999999998, -0.0, 5], [1.0, -0.0, 5], [2.576, -0.0, 5]],
                  "P1": [[1348, 1338.64], [1881, 1405.13], [1918, 1407.85]], "S1": "s02", "V": [0, 150, false],
                  "M": [0, 0, 0, 10000, 0, 100], "S": [0, 150, 1]}, "8": {"R": [1, 2], "E": "e010011000",
                                                                          "A1": [[0.0, -0.0, 5],
                                                                                 [0.35199999999999998, -0.0, 5],
                                                                                 [1.0, -0.0, 5], [2.576, -0.0, 5]],
                                                                          "P1": [[1918, 1407.85], [2066, 1418.91],
                                                                                 [2400, 1442.82]], "S1": "s02",
                                                                          "V": [false, false, 1, 2],
                                                                          "M": [0, 0, 0, 10000, 0, 100],
                                                                          "S": [false, false, 1]},
            "9": {"R": [1], "E": "e010010000", "A1": [[0.0, -0.0, 5], [0.45800000000000002, 0.0, 5]],
                  "P1": [[1949, 1691.75], [2400, 1709.34]], "S1": "s02", "M": [0, 150, 0, 10000, 0, 100]},
            "10": {"R": [2], "E": "e010110000", "A1": [], "P1": [[700, 691.0], [2400, 2391.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "11": {"R": [2], "E": "e010112000", "A1": [], "P1": [[700, 9.0], [2400, 9.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "12": {"R": [2], "E": "e010001000", "A1": [], "P1": [[2094, 2084.79], [2400, 2391.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "13": {"R": [2, 3], "E": "e010010000", "A1": [[0.0, 0.0, 5], [0.82699999999999996, 1.0, 5]],
                   "P1": [[1834, 1643.27], [2400, 1825.78]], "S1": "s02", "M": [0, 260, 0, 10000, 0, 100]},
            "14": {"R": [2, 3], "E": "e010010000",
                   "A1": [[0.0, -0.0, 5], [0.68200000000000005, -0.0, 5], [1.0, -0.0, 5], [2.685, 1.0, 5]],
                   "P1": [[802, 498.62], [1158, 512.2], [2400, 534.43]], "S1": "s02", "V": [false, false, 2, 3],
                   "M": [0, 0, 0, 10000, 0, 100]}, "15": {"R": [2, 3], "E": "e010010000",
                                                          "A1": [[0.14999999999999999, -0.0, 5], [0.186, -0.0, 5],
                                                                 [0.308, -0.0, 5], [0.68200000000000005, -0.0, 5],
                                                                 [1.0, -0.0, 5], [2.674, 1.0, 5]],
                                                          "P1": [[1091, 800.5], [1248, 926.86], [2400, 994.43]],
                                                          "S1": "s02", "V": [false, false, 2, 3],
                                                          "M": [0, 0, 0, 10000, 0, 100]},
            "16": {"R": [2, 3], "E": "e010010000", "A1": [[0.0, -0.0, 5], [1.0, 0.0, 5], [1.214, 2.0, 5]],
                   "P1": [[700, 494.75], [802, 498.62]], "S1": "s02", "V": [0, 150, false],
                   "M": [0, 0, 0, 10000, 0, 100]},
            "17": {"R": [2], "E": "e010001000", "A1": [], "P1": [[2094, 2084.79], [2400, 2391.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]}, "18": {"R": [2], "E": "e110100000",
                                                          "A1": [[0.14999999999999999, -0.0, 5], [0.186, -0.0, 5],
                                                                 [0.308, -0.0, 5], [0.68200000000000005, -0.0, 5],
                                                                 [1.0, -0.0, 5], [2.674, 1.0, 5]],
                                                          "P1": [[1091, 800.5], [1248, 926.86], [2400, 994.43]],
                                                          "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "19": {"R": [2], "E": "e110100000",
                   "A1": [[0.0, -0.0, 5], [0.68200000000000005, -0.0, 5], [1.0, -0.0, 5], [2.685, 1.0, 5]],
                   "P1": [[1091, 509.64], [1158, 512.2], [2400, 534.43]], "S1": "s02", "M": [0, 0, 0, 10000, 15, 100]},
            "20": {"R": [2], "E": "e110000000", "A1": [],
                   "P1": [[1348, 1338.64], [1881, 1405.13], [2066, 1418.91], [2400, 1442.82]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "21": {"R": [2], "E": "e210010000", "A1": [[0.0, -0.0, 5], [1.0, -0.0, 5], [2.685, 1.0, 5]],
                   "P1": [[700, 494.75], [1158, 600.06], [2400, 719.25]], "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "22": {"R": [3], "E": "e010110000", "A1": [], "P1": [[700, 691.0], [2400, 2391.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "23": {"R": [3], "E": "e010100000", "A1": [], "P1": [[700, 9.0], [2400, 9.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "24": {"R": [3], "E": "e010010000", "A1": [], "P1": [[700, 134.0], [2400, 134.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "25": {"R": [3], "E": "e010011000", "A1": [[0.0, -0.0, 5], [1.0, 0.0, 5]],
                   "P1": [[1356, 1181.64], [2400, 1406.25]], "S1": "s02", "M": [0, 260, 0, 10000, 0, 100]},
            "26": {"R": [3], "E": "e210010000",
                   "A1": [[0.0, -0.0, 5], [0.68200000000000005, -0.0, 5], [1.0, -0.0, 5], [2.685, 1.0, 5]],
                   "P1": [[802, 498.62], [1158, 512.2], [2400, 534.43]], "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "27": {"R": [3], "E": "e210010000",
                   "A1": [[0.14999999999999999, -0.0, 5], [0.186, -0.0, 5], [0.308, -0.0, 5],
                          [0.68200000000000005, -0.0, 5], [1.0, -0.0, 5], [2.674, 1.0, 5]],
                   "P1": [[1091, 800.5], [1248, 926.86], [2400, 994.43]], "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "28": {"R": [4], "E": "e010110000", "A1": [], "P1": [[700, 9.0], [2400, 9.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "29": {"R": [4], "E": "e010100000", "A1": [], "P1": [[700, 691.0], [2400, 2391.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]}, "30": {"R": [4], "E": "e010010000",
                                                          "A1": [[0.25600000000000001, -0.0, 5],
                                                                 [0.30599999999999999, -0.0, 5],
                                                                 [0.38800000000000001, -0.0, 5],
                                                                 [0.75800000000000001, -0.0, 5], [1.0, -0.0, 5],
                                                                 [1.726, -2.0, 5]],
                                                          "P1": [[1768, 1353.31], [1925, 1435.34], [2400, 1443.1]],
                                                          "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "31": {"R": [4], "E": "e010011000", "A1": [], "P1": [[700, 566.0], [2400, 2266.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "32": {"R": [4], "E": "e010010000", "A1": [[0.0, 0.0, 5], [0.33800000000000002, 0.0, 5]],
                   "P1": [[1848, 1506.3], [2400, 1565.44]], "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "33": {"R": [4], "E": "e110010000", "A1": [[0.0, -0.0, 5], [1.0, -0.0, 5], [1.612, 1.0, 5]],
                   "P1": [[700, 383.06], [2400, 409.43]], "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "34": {"R": [4], "E": "e110011000", "A1": [], "P1": [[1536, 1041.2], [2400, 1063.1]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]}, "35": {"R": [4], "E": "e210011000",
                                                          "A1": [[0.0, -0.0, 5], [0.25600000000000001, -0.0, 5],
                                                                 [0.623, -0.0, 5], [1.0, -0.0, 5], [1.851, -2.0, 5]],
                                                          "P1": [[1536, 851.7], [2400, 926.0]], "S1": "s02",
                                                          "M": [0, 0, 0, 10000, 0, 100]},
            "36": {"R": [5], "E": "e010010000", "A1": [[0.0, -0.0, 5], [1.0, -0.0, 5], [1.754, 1.0, 5]],
                   "P1": [[802, 585.35], [2400, 669.1]], "S1": "s02", "M": [0, 0, 0, 10000, 0, 100]},
            "37": {"R": [5], "E": "e010100000", "A1": [], "P1": [[700, 691.0], [2400, 2391.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]},
            "38": {"R": [5], "E": "e010112000", "A1": [], "P1": [[700, 9.0], [2400, 9.0]], "S1": "s00",
                   "M": [0, 0, 0, 10000, 0, 100]}}, "created_at": "2017-03-13 23:59:10", "DNA_Range": [70, 240]};
        let output = dnaTools.get_elements(dna, 0.5, 2000, 8, Array(8).fill(278), null, 320, 9, 125, true);
        let test_result = {
            'horizontals': [{'y1': 0, 'x2': 1000, 'x1': -1000, 'y2': 0}, {'y1': 278, 'x2': 423, 'x1': -1000, 'y2': 278},
                            {'y1': 278, 'x2': 1000, 'x1': 766, 'y2': 278},
                            {'y1': 556, 'x2': -464, 'x1': -1000, 'y2': 556},
                            {'y1': 556, 'x2': 1000, 'x1': -38, 'y2': 556},
                            {'y1': 834, 'x2': 1000, 'x1': -1000, 'y2': 834},
                            {'y1': 1112, 'x2': 1000, 'x1': -1000, 'y2': 1112},
                            {'y1': 1390, 'x2': 1000, 'x1': -1000, 'y2': 1390}],
            'verticals': [{'y1': 565, 'x2': -866, 'x1': -866, 'y2': 825}, {'y1': 843, 'x2': 437, 'x1': 437, 'y2': 1103},
                          {'y1': 565, 'x2': 320, 'x1': 320, 'y2': 825}, {'y1': 565, 'x2': 991, 'x1': 991, 'y2': 825},
                          {'y1': 1121, 'x2': -352, 'x1': -352, 'y2': 1381},
                          {'y1': 1121, 'x2': -991, 'x1': -991, 'y2': 1381},
                          {'y1': 287, 'x2': -991, 'x1': -991, 'y2': 547}, {'y1': 287, 'x2': 991, 'x1': 991, 'y2': 547},
                          {'y1': 287, 'x2': -29, 'x1': -29, 'y2': 547}, {'y1': 565, 'x2': -29, 'x1': -29, 'y2': 825},
                          {'y1': 287, 'x2': -473, 'x1': -473, 'y2': 547},
                          {'y1': 565, 'x2': -473, 'x1': -473, 'y2': 825}, {'y1': 843, 'x2': 866, 'x1': 866, 'y2': 1103},
                          {'y1': 843, 'x2': -991, 'x1': -991, 'y2': 1103}, {'y1': 843, 'x2': 53, 'x1': 53, 'y2': 1103},
                          {'y1': 9, 'x2': 991, 'x1': 991, 'y2': 269}, {'y1': 9, 'x2': -991, 'x1': -991, 'y2': 269},
                          {'y1': 9, 'x2': 775, 'x1': 775, 'y2': 269}, {'y1': 287, 'x2': 775, 'x1': 775, 'y2': 547},
                          {'y1': 9, 'x2': -379, 'x1': -379, 'y2': 269}, {'y1': 9, 'x2': 414, 'x1': 414, 'y2': 269},
                          {'y1': 287, 'x2': 414, 'x1': 414, 'y2': 547},
                          {'y1': 843, 'x2': -597, 'x1': -597, 'y2': 1103}], 'doors': [],
            'legs': [{'y1': -13, 'x1': -680, 'z1': 20}, {'y1': -13, 'x1': -680, 'z1': 300},
                     {'y1': -13, 'x1': -980, 'z1': 20}, {'y1': -13, 'x1': -980, 'z1': 300},
                     {'y1': -13, 'x1': 17, 'z1': 20}, {'y1': -13, 'x1': 17, 'z1': 300},
                     {'y1': -13, 'x1': -379, 'z1': 20}, {'y1': -13, 'x1': -379, 'z1': 300},
                     {'y1': -13, 'x1': 414, 'z1': 20}, {'y1': -13, 'x1': 414, 'z1': 300},
                     {'y1': -13, 'x1': 775, 'z1': 20}, {'y1': -13, 'x1': 775, 'z1': 300},
                     {'y1': -13, 'x1': 980, 'z1': 20}, {'y1': -13, 'x1': 980, 'z1': 300}],
            'supports': [{'y2': 825, 'x2': 311, 'y1': 565, 'x1': 186, 'z1': 0, 'z2': 0},
                         {'y2': 1381, 'x2': -982, 'y1': 1121, 'x1': -857, 'z1': 0, 'z2': 0},
                         {'y2': 547, 'x2': -982, 'y1': 287, 'x1': -857, 'z1': 0, 'z2': 0},
                         {'y2': 1103, 'x2': 857, 'y1': 843, 'x1': 732, 'z1': 0, 'z2': 0},
                         {'y2': 1103, 'x2': 44, 'y1': 843, 'x1': -81, 'z1': 0, 'z2': 0},
                         {'y2': 269, 'x2': -388, 'y1': 9, 'x1': -513, 'z1': 0, 'z2': 0},
                         {'y2': 269, 'x2': 405, 'y1': 9, 'x1': 280, 'z1': 0, 'z2': 0}], 'additional_elements': {
                'shadow_middle': [{'y2': 269, 'x2': -388, 'y1': 9, 'x1': -982, 'z1': 0, 'z2': 320},
                                  {'y2': 269, 'x2': 405, 'y1': 9, 'x1': -370, 'z1': 0, 'z2': 320},
                                  {'y2': 547, 'x2': 766, 'y1': 9, 'x1': 423, 'z1': 0, 'z2': 320},
                                  {'y2': 269, 'x2': 982, 'y1': 9, 'x1': 784, 'z1': 0, 'z2': 320},
                                  {'y2': 547, 'x2': -482, 'y1': 287, 'x1': -982, 'z1': 0, 'z2': 320},
                                  {'y2': 825, 'x2': -38, 'y1': 287, 'x1': -464, 'z1': 0, 'z2': 320},
                                  {'y2': 547, 'x2': 405, 'y1': 287, 'x1': -20, 'z1': 0, 'z2': 320},
                                  {'y2': 547, 'x2': 982, 'y1': 287, 'x1': 784, 'z1': 0, 'z2': 320},
                                  {'y2': 825, 'x2': -482, 'y1': 565, 'x1': -857, 'z1': 0, 'z2': 320},
                                  {'y2': 825, 'x2': 311, 'y1': 565, 'x1': -20, 'z1': 0, 'z2': 320},
                                  {'y2': 825, 'x2': 982, 'y1': 565, 'x1': 329, 'z1': 0, 'z2': 320},
                                  {'y2': 1103, 'x2': -606, 'y1': 843, 'x1': -982, 'z1': 0, 'z2': 320},
                                  {'y2': 1103, 'x2': 44, 'y1': 843, 'x1': -588, 'z1': 0, 'z2': 320},
                                  {'y2': 1103, 'x2': 428, 'y1': 843, 'x1': 62, 'z1': 0, 'z2': 320},
                                  {'y2': 1103, 'x2': 857, 'y1': 843, 'x1': 446, 'z1': 0, 'z2': 320},
                                  {'y2': 1381, 'x2': -361, 'y1': 1121, 'x1': -982, 'z1': 0, 'z2': 320}],
                'shadow_right': [{'y2': 1103, 'x2': 1000, 'y1': 843, 'x1': 875, 'z1': 0, 'z2': 320},
                                 {'y2': 1381, 'x2': 1000, 'y1': 1121, 'x1': -343, 'z1': 0, 'z2': 320}],
                'shadow_side': [{'y2': 269, 'x2': -996, 'y1': 9, 'x1': -1004, 'z1': 0, 'z2': 320},
                                {'y2': 547, 'x2': -996, 'y1': 287, 'x1': -1004, 'z1': 0, 'z2': 320},
                                {'y2': 1103, 'x2': -996, 'y1': 843, 'x1': -1004, 'z1': 0, 'z2': 320},
                                {'y2': 1381, 'x2': -996, 'y1': 1121, 'x1': -1004, 'z1': 0, 'z2': 320}],
                'shadow_left': [{'y2': 825, 'x2': -875, 'y1': 565, 'x1': -1000, 'z1': 0, 'z2': 320}]}};
      expect(test_result).toEqual(output);
        /*

        ['verticals','horizontals'].forEach(elem_typ => {
           expect(output[elem_typ].length).toEqual(test_result[elem_typ].length);
           output[elem_typ] = output[elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));
           test_result[elem_typ] = test_result[elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));

           output[elem_typ].forEach((_,i)=>{
               expect(output[elem_typ][i]['x1']).toEqual(test_result[elem_typ][i]['x1']);
               expect(output[elem_typ][i]['y1']).toEqual(test_result[elem_typ][i]['y1']);
               expect(output[elem_typ][i]['x2']).toEqual(test_result[elem_typ][i]['x2']);
               expect(output[elem_typ][i]['y2']).toEqual(test_result[elem_typ][i]['y2']);
           })
        });

        ['supports',].forEach(elem_typ => {
           expect(output[elem_typ].length).toEqual(test_result[elem_typ].length);
           output[elem_typ] = output[elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));
           test_result[elem_typ] = test_result[elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));
           output[elem_typ].forEach((_,i)=>{
               expect(output[elem_typ][i]['x1']).toEqual(test_result[elem_typ][i]['x1']);
               expect(output[elem_typ][i]['y1']).toEqual(test_result[elem_typ][i]['y1']);
               expect(output[elem_typ][i]['x2']).toEqual(test_result[elem_typ][i]['x2']);
               expect(output[elem_typ][i]['y2']).toEqual(test_result[elem_typ][i]['y2']);
               expect(output[elem_typ][i]['z1']).toEqual(test_result[elem_typ][i]['z1']);
               expect(output[elem_typ][i]['z2']).toEqual(test_result[elem_typ][i]['z2']);
           })
        });

        ['legs',].forEach(elem_typ => {
           expect(output[elem_typ].length).toEqual(test_result[elem_typ].length);
           output[elem_typ] = output[elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));
           test_result[elem_typ] = test_result[elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));
           output[elem_typ].forEach((_,i)=>{
               expect(output[elem_typ][i]['x1']).toEqual(test_result[elem_typ][i]['x1']);
               expect(output[elem_typ][i]['y1']).toEqual(test_result[elem_typ][i]['y1']);
               expect(output[elem_typ][i]['z1']).toEqual(test_result[elem_typ][i]['z1']);
           })
        });

        ['shadow_middle','shadow_right','shadow_side','shadow_left',].forEach(elem_typ => {
           expect(output['additional_elements'][elem_typ].length).toEqual(test_result['additional_elements'][elem_typ].length);
            output['additional_elements'][elem_typ] = output['additional_elements'][elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));
            test_result['additional_elements'][elem_typ] = test_result['additional_elements'][elem_typ].sort((x,y)=> (x['y1'] != y['y1'] ? x['y1']-y['y1']: x['x1']-y['x1']));
            console.log(elem_typ);
            console.log(output['additional_elements'][elem_typ].length);
            console.log(test_result['additional_elements'][elem_typ].length);
            output['additional_elements'][elem_typ].forEach(x=>console.log(x));
            test_result['additional_elements'][elem_typ].forEach(x=>console.log(x));
           output['additional_elements'][elem_typ].forEach((_,i)=>{

               expect(output['additional_elements'][elem_typ][i]['x1']).toEqual(test_result['additional_elements'][elem_typ][i]['x1']);
               expect(output['additional_elements'][elem_typ][i]['y1']).toEqual(test_result['additional_elements'][elem_typ][i]['y1']);
               expect(output['additional_elements'][elem_typ][i]['z1']).toEqual(test_result['additional_elements'][elem_typ][i]['z1']);
           })
        });
    */

  });
});
