THREE = THREE || {};
let isMobile = $('.webglconfigurator').hasClass('mobile');

class Camera {
    constructor(height, width, name="Main", ivyShelf){
        this.height = height;
        this.width = width;
        this.ratio = this.width / this.height;
       this.camera = new THREE.PerspectiveCamera(30, this.ratio, 100, 35000);

      // this.camera = new THREE.OrthographicCamera( width / - 2, width / 2, height / 2, height / - 2, 100, 35000);

        this.camera.name = name;
        this.pivot = new THREE.Object3D();

        //TO DO move to other file
        this.tweens = [];
        this.positionRight = 0;
    }

    init(shelfHeight){
        this.addCameraToPivot(shelfHeight);
        this.camera.aspect = this.ratio;

        //TODO moved from main.js - probable should be eliminated from here
        this.camera.position.y = isMobile ? 300 : -200;
    }

    getCamera(){
        return this.camera;
    }

    getPivot(){
        return this.pivot;
    }

    addCameraToPivot(height){
        this.pivot.add(this.camera);
        //this.pivot.position.setY(map(height, 300, 2300, 600, 1100));
    }

    setCameraHeight(height, noTween) {
        let that = this;
        let yPosition = map(height, 300, 2600, 600, 1400);

        if (isMobile){
            yPosition -= (window.mobileoffset_yposition || 200);

        }
        if(noTween) {
            that.pivot.position.y = yPosition
        } else {
            let tween = new TWEEN.Tween( { y: that.pivot.position.y } )
                    .to( { y: yPosition } , 300)
                    .easing(TWEEN.Easing.Quadratic.Out)
                    .onUpdate( function () {
                        that.pivot.position.y = this.y;
                    });
            that.tweens.push(tween);
        }
    }

    //
    setCameraInitialPosition(furniture ,noTween, leaveUi) {
        console.log('setCameraInitialPosition');
        let that = this;
        let distance;
        let shelf_number = 1;

        //ABTEST

        if (isMobile) {
            distance = Math.max(map(furniture.getHeight(), 300, 2300, 3700, 6700), map(furniture.width, 1200, 2400, 3700, 6500)) - (window.mobileoffset_distance || 300);
        } else {
            distance = Math.max(map(furniture.getHeight(), 300, 2300, 4500, 6700), map(furniture.width * shelf_number, 1200, 2400, 3700, 5100));
            if (furniture.getHeight() > 3110) {
                distance = distance * 1.08;
            }
            if (furniture.getHeight() > 3900) {
                distance = distance * 1.02;
            }
            if (furniture.getHeight() > 3410 && furniture.width > 2000) {
                distance = distance * 1.06;
            }
        }
        this.setCameraHeight(furniture.getHeight(), noTween);
        // if no tween just set the camera
        //window.cameraClass.getCamera().setViewOffset(800,600,0,150, 800,600);
        if(noTween) {
            this.camera.position.z = distance;
            if(isMobile) {
                checkActiveHolds(furniture);
                furniture.createBlobs(leaveUi);
            }
        } else {
            // if tween just start a tween
            let tween = new TWEEN.Tween( { z: that.camera.position.z } )
                    .to( { z: distance } , 300)
                    .easing(TWEEN.Easing.Quadratic.Out)
                    .onUpdate( function () {
                        that.camera.position.z = this.z;
                        that.positionRight = 0;
                        /*if(leaveUi && isMobile) furniture.createBlobs(leaveUi);*/
                    })
                    .onComplete(function(){
                        that.tweens.length = 0;
                        if(isMobile){
                            //checkActiveHolds(furniture);
                            if(!leaveUi) furniture.createBlobs();
                        } else {
                            furniture.createBlobs();
                        }
                    });
            this.tweens.push(tween);
            this.startTween();
        }
    }

    setCameraHeightRenderer(height, noTween) {
        let that = this;
        let yPosition = map(height, 300, 2600, 600, 1400);

        if (isMobile){
            yPosition -= (window.mobileoffset_yposition || 200);
        }
        if(noTween) {
            that.pivot.position.y = yPosition
        } else {
            let tween = new TWEEN.Tween( { y: that.pivot.position.y } )
                    .to( { y: yPosition } , 300)
                    .easing(TWEEN.Easing.Quadratic.Out)
                    .onUpdate( function () {
                        that.pivot.position.y = this.y;
                    });
            that.tweens.push(tween);
        }
    }

    setCameraInitialPositionRender(furniture ,noTween, leaveUi) {
        console.log('setCameraInitialPositionRenderer');
        let that = this;
        let distance;
        let shelf_number = 1;
        distance = Math.max(map(furniture.getHeight(), 300, 2300, 4500, 6700), map(furniture.width * shelf_number, 1200, 2400, 3700, 5300));


        if (furniture.getHeight() > 3110) {
            distance = distance * 1.08;
        }
        if (furniture.getHeight() > 3410 && furniture.width > 2000) {
            distance = distance * 1.06;
        }
        if (furniture.width > 2000 && window.feed) {
            distance = distance * 1.11;
        }
        if (window.cstm_i18n.zoomEnabled) {
            if (furniture.getHeight() > 3410 && furniture.width > 2000) {
                distance *= 0.95;
            } else if (furniture.getHeight() > 2810) {
                distance *= 1.1;
            } else if (furniture.getHeight() > 1300) {
                distance *= 0.85;
            } else if (furniture.width > 2000) {
                distance *= 0.95;
            } else {
                distance *= 0.75;
            }
        }
        this.setCameraHeightRenderer(
            window.cstm_i18n.zoomEnabled && furniture.getHeight() > 1150 ? furniture.getHeight() * 0.70: furniture.getHeight(), noTween);
        // if no tween just set the camera
        this.camera.position.z = distance;
        this.camera.setViewOffset(1280,960, -(furniture.width * 0.00344) ,0, 1280,960);


    }

    startTween() {
        let that = this;
        for(let i=0; i< that.tweens.length; i++) {
            that.tweens[i].start();
        }
    }

    setZ(z) {
        this.camera.position.z = z;
    };
    setY(y) {
        this.camera.position.y = y;
    };
}

function map(arg, from1, to1, from2, to2) {
    return (arg - from1) / (to1 - from1) * (to2 - from2) + from2
}

function checkActiveHolds(furniture){
    var active = $('.configurator-hold.active');
    if((active.index('.configurator-hold')+1) > furniture.rows) {
        $('.configurator-hold').removeClass('active');
        $('.configurator-hold:eq('+(furniture.rows-1)+')').trigger('click');
        furniture.selectedRow = $('.configurator-hold:eq('+(furniture.rows-1)+')').index('.configurator-hold');
    }
}

module.exports = Camera;