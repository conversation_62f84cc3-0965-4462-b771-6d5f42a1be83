module.exports = { 
	generateWalls: function (parameter,shelfWidth,rows,rowsH,shelfDepth) {

		var Point3d = Vector3d = THREE.Vector3;
    var i = 0;
    var j = 0;
    var mat = 18;
    var matHalf = mat / 2;
    var backWidth = 125;
    var minx = shelfWidth / 2 * (-1);
    var maxx = shelfWidth / 2;
    var supportsShift = 2;
    var locy;
    var snapParamSide = 5;
    var snapParamMiddle = 5;
    var ptHorizontalLeft = [];
    var ptHorizontalRight = [];
    var ptHorizontalsAll = [];
    var ptVerticalBottomLeft = [];
    var ptVerticalBottomRight = [];
    var ptVerticalsAll = [];
    var ptSupportsLeft = [];
    var ptSupportsRight = [];

    var shadowLeftLocation = [];
    var shadowLeftSize = [];
    var shadowMiddleLocation = [];
    var shadowMiddleSize = [];
    var shadowRightLocation = [];
    var shadowRightSize = [];
    shelfWidth = parseInt(shelfWidth * 0.1) * 10;

    if (parameter < (0 + snapParamSide))
    {
      parameter = 0;
    }
    if ((parameter > (50 - snapParamMiddle)) && (parameter < (50 + snapParamMiddle)))
    {
      parameter = 50;
    }
    if (parameter > (100 - snapParamSide))
    {
      parameter = 100;
    }
    var ptLoc = new Point3d(shelfWidth / 2 * (-1), 0, 0);
    ptHorizontalLeft.push(ptLoc);
    for (i = 0; i < rows; i += 1)
    {
      locy = ptLoc.y + rowsH[i];
      ptLoc = new Point3d(shelfWidth / 2 * (-1), locy, 0);
      ptHorizontalLeft.push(ptLoc);
    }
    for (i = 0; i <= rows; i += 1)
    {
      ptHorizontalRight.push(new Point3d(shelfWidth / 2, ptHorizontalLeft[i].y, 0));
    }
    for (i = 0; i < ptHorizontalLeft.length; i++)
    {
      ptHorizontalsAll.push(ptHorizontalLeft[i]);
      ptHorizontalsAll.push(ptHorizontalRight[i]);
    }
    var div;
    if (shelfWidth <= 1020)
      div = 1;
    else if (shelfWidth > 1020 & shelfWidth <= 1500)
      div = 2;
    else if (shelfWidth > 1500 & shelfWidth <= 2020)
      div = 3;
    else
      div = 4;
    var indent;
    if (div == 1)
      indent = 150;
    else if (div == 2)
      indent = 170;
    else
      indent = 180;
    var modWidth;
    modWidth = parseInt((ptHorizontalRight[0].x - indent - (ptHorizontalLeft[0].x + indent)) / div);
    var maxMove;
    var move;
    if (div == 1)
      maxMove = modWidth / 6;
    else
      maxMove = modWidth / 4;
    move = (parameter * maxMove) / 100;
    var ptVerticalBottom = [];
    var ptVerticalTop = [];
    var locPt = new Point3d();
    for (i = 0; i <= ptHorizontalLeft.length - 2; i++)
    {
      var factor;
      if (i % 2 == 0)
        factor = 1;
      else
        factor = -1;
      locPt = new Point3d(parseInt(ptHorizontalLeft[i].x + indent + move * factor), parseInt(ptHorizontalLeft[i].y + matHalf), 0);
      ptVerticalBottom.push(locPt);
      shadowLeftLocation.push(new Point3d(locPt.x - (indent + move * factor) / 2, locPt.y + (rowsH[i] - mat) / 2, shelfDepth / 2));
      shadowLeftSize.push(new Point3d(indent + move * factor - mat, rowsH[i] - mat, shelfDepth));
      ptVerticalTop.push(new Point3d(locPt.x, locPt.y + rowsH[i] - mat, 0));
      for (j = 1; j <= div - 1; j += 1)
      {
        locPt = new Point3d(parseInt(ptHorizontalLeft[i].x + indent + modWidth * j + move * factor), parseInt(ptHorizontalLeft[i].y + matHalf), 0);
        ptVerticalBottom.push(locPt);
        ptVerticalTop.push(new Point3d(locPt.x, locPt.y + rowsH[i] - mat, 0));
      }
      locPt = new Point3d(parseInt(ptHorizontalRight[i].x - indent + move * factor), parseInt(ptHorizontalRight[i].y + matHalf), 0);
      ptVerticalBottom.push(locPt);
      shadowRightLocation.push(new Point3d(locPt.x + (indent - move * factor) / 2, locPt.y + (rowsH[i] - mat) / 2, shelfDepth / 2));
      shadowRightSize.push(new Point3d(indent - move * factor - mat, rowsH[i] - mat, shelfDepth));
      ptVerticalTop.push(new Point3d(locPt.x, locPt.y + rowsH[i] - mat, 0));
      for (j = 0; j <= div - 1; j += 1)
      {
        shadowMiddleLocation.push(new Point3d(ptHorizontalLeft[i].x + indent + modWidth * j + move * factor + (modWidth / 2), ptHorizontalLeft[i].y + rowsH[i] / 2, shelfDepth / 2));
        shadowMiddleSize.push(new Point3d(modWidth - mat, rowsH[i] - mat, shelfDepth));
      }
    }
    for (i = 0; i < ptVerticalBottom.length; i++)
    {
      ptVerticalsAll.push(ptVerticalBottom[i]);
      ptVerticalsAll.push(ptVerticalTop[i]);
    }
    for (i = 1; i < rows; i += 2)
    {
      ptSupportsLeft.push(new Point3d(ptVerticalBottom[i * (div + 1)].x + matHalf, ptVerticalBottom[i * (div + 1)].y, supportsShift));
      ptSupportsLeft.push(new Point3d(ptVerticalBottom[i * (div + 1)].x + matHalf + backWidth, ptVerticalBottom[i * (div + 1)].y + rowsH[i] - mat, supportsShift));
    }
    for (i = 0; i < rows; i += 2)
    {
      ptSupportsRight.push(new Point3d(ptVerticalBottom[i * (div + 1) + div].x - matHalf - backWidth, ptVerticalBottom[i * (div + 1) + div].y, supportsShift));
      ptSupportsRight.push(new Point3d(ptVerticalBottom[i * (div + 1) + div].x - matHalf, ptVerticalBottom[i * (div + 1) + div].y + rowsH[i] - mat, supportsShift));
    }
	return {
		ptSupportsLeft: ptSupportsLeft,
		ptSupportsRight: ptSupportsRight,
		ptVerticalsAll: ptVerticalsAll,
		ptHorizontalsAll: ptHorizontalsAll,
		shadowLeftLocation: shadowLeftLocation,
		shadowLeftSize: shadowLeftSize,
		shadowMiddleLocation: shadowMiddleLocation,
		shadowMiddleSize: shadowMiddleSize,
		shadowRightLocation: shadowRightLocation,
		shadowRightSize: shadowRightSize
	};
}};