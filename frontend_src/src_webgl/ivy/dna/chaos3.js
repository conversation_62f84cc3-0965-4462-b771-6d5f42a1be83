module.exports = { 
	generateWalls: function (parameter,shelfWidth,rows,rowsH,shelfDepth) {

    var Point3d = THREE.Vector3;
    var Vector3d = THREE.Vector3;
    var maxWidth = 2400;
    var dVertMinWidth = 125;
    var dVertThreshold = 125;
    var singleVerticalThreshold = 125;
    var singleBackThreshold = 134;
    var sideUndercut = 125;
    var backWidth = 125;
    var mat = 18;
    var rows = 8;
    var shelfMax = 2400;
    var snapParamSide = 5;
    var factorsingle = 0.4;
    var dVertRows = [1, 5, 0, 3, 5, 4, 1];
    var doubleVertLocx = [375, 0, 824, 772, 1268, 1466, 1605];
    var dVertSize = [290, 400, 325, 400, 325, 375, 317];
    var dVertLeftSpeed = [1, 0, 1, 1, 1, 1, 2];
    var dVertRightSpeed = [1, 1, 1, 1, 1, 1, 2];
    var sVertLocx = [455, 1331, 2058, 1160, 1726, 1259, 2000, 515, 507, 1668, 507, 1849, 580, 1065, 1920];
    var sVertSpeed = [1, 1, 0, 1, 0, 3, 0, 1, 3, 0, 6, 5, 2, 1, 0];

    var wciecieBorderLeft = [1, 1, 2, 1, 2, 1, 1, 2];
    var wciecieBorderRight = [1, 1, 1, 2, 1, 1, 2, 1];
    var i = 0;
    var index1 = 0;
    var index2 = 0;
    var indexDoubleVert = 0;
    var indexDoubleVert2 = 0;
    var k = 0;
    var locali = 0;
    var locy = 0;
    var locx = 0;
    var loczakres = 0;
    var factordouble = 0.2;
    var pointLeft = new Point3d();
    var pointRight = new Point3d();
    var ptLoc = new Point3d();

    var indexHor = [3,7,8];
    var sVertRows = [0,0,0,2,2,3,3,4,5,5,6,6,7,7,7];
    var indexSingleVertical = [0,1,3,8,10,14];
    var sVertOrientation = [-1,-1,1,1,-1,1];
    var backRows = [0,0,2,5,6,7];
    var doubleRowLocation = [1,5,0,3,5,4,1];
    var arri = [0,1,2,3,4,5,6];
    var undercutOpening = [0,sideUndercut,0,sideUndercut,sideUndercut,0,0];

    var boolBorderRight = [];
    var singleVertExists = [];
    var singleBackExists = [];
    var locxLeftArr = [];
    var locxRightArr = [];
    var zakresy = [];
    var sVertAdd = [];
    var dVertLeftBase = [];
    var dVertRightBase = [];
    var doubleVertLeftALL = [];
    var doubleVertRightALL = [];
    var ptLeftTemp = [];
    var ptRightTemp = [];
    var doubleBackBottomCURRENT = [];
    var doubleBackTopCURRENT = [];

    var doubleVertLeftCURRENTTop = [];
    var doubleVertLeftCURRENTBottom = [];
    var doubleVertRightCURRENTTop = [];
    var doubleVertRightCURRENTBottom = [];
    var doubleVertClosed = [];
    var doubleVertExists = [];
    var sideRightBool = [];
    var basePointHorLeft = [];
    var basePointHorRight = [];
    var basePointHorLeftFull = [];
    var basePointHorRightFull = [];
    var ptSideLeftBot = [];
    var pointSideLeftTop = [];
    var pointSideRightBottom = [];
    var ptSideRightTop = [];
    var widthBorderLeft = [];
    var heightBorderLeft = [];
    var widthBorderRight = [];
    var heightBorderRight = [];
    var widthMiddle = [];
    var heightMiddle = [];
    var pointHorizontalLeft = [];
    var pointHorizontalRight = [];
    var pointBorderLeftBottom = [];
    var pointBorderLeftTop = [];
    var pointBorderRightBottom = [];
    var pointBorderRightTop = [];
    var singlePointBackBottom = [];
    var singlePointBackTop = [];
    var pointSingleBottom = [];
    var pointSingleTop = [];
    var pointSingleBottomCURRENT = [];
    var pointSingleTopCURRENT = [];
    var pointSingleBackBottom = [];
    var pointSingleBackTop = [];
    var singleBackBottomCURRENT = [];
    var singleBackTopCURRENT = [];
    var verticalsPoints = [];
    var verticalsPointsRow0 = [];
    var verticalsPointsRow1 = [];
    var verticalsPointsRow2 = [];
    var verticalsPointsRow3 = [];
    var verticalsPointsRow4 = [];
    var verticalsPointsRow5 = [];
    var verticalsPointsRow6 = [];
    var verticalsPointsRow7 = [];
    var shadowMiddleLocation = [];
    var shadowMiddleSize = [];
    var shadowDoubleSize = [];
    var shadowDoubleLocation = [];
    var shadowLeftLocation = [];
    var shadowRightLocation = [];
    var shadowLeftSize = [];
    var shadowRightSize = [];
    if (parameter < (0 + snapParamSide))
    {
      parameter = 0;
    }
    if (parameter > (100 - snapParamSide))
    {
      parameter = 100;
    }
    var currentWidth = (parseInt(shelfWidth / 10)) * 10;
    ptLoc = new Point3d(parseInt((maxWidth / 2) - (parseFloat(currentWidth) / 2)), 0, 0);
    basePointHorLeft.push(ptLoc);
    for ( i = 0; i <= rows - 1; i++)
    {
      locx = parseInt((parseFloat(maxWidth) / 2) - (parseFloat(currentWidth) / 2));
      locy = parseInt(ptLoc.y) + rowsH[i];
      ptLoc = new Point3d(locx, locy, 0);
      basePointHorLeft.push(ptLoc);
    }
    for ( i = 0; i <= rows; i++)
    {
      ptLoc = new Point3d(parseInt((parseFloat(maxWidth) / 2) + (parseFloat(currentWidth) / 2)), basePointHorLeft[i].y, 0);
      basePointHorRight.push(ptLoc);
    }
    var rowsAbsolute = [];
    for ( i = 0; i < basePointHorLeft.length; i++)
    {
      rowsAbsolute.push(parseInt(basePointHorLeft[i].y));
    }
    locx = 0;
    locy = 0;
    for (i = 0; i < wciecieBorderLeft.length; i++)
    {
      if (wciecieBorderLeft[i] == 2)
      {
        locx = parseInt(basePointHorLeft[i].x + parseFloat(sideUndercut));
      }
      else
      {
        locx = parseInt(basePointHorLeft[i].x);
      }
      locy = parseInt(basePointHorLeft[i].y + mat / 2);
      ptSideLeftBot.push(new Point3d(locx + mat / 2, locy, 0));

      locy = parseInt(basePointHorLeft[i + 1].y - mat / 2);
      pointSideLeftTop.push(new Point3d(locx + mat / 2, locy, 0));
    }
    for (i = 0; i < wciecieBorderRight.length; i++)
    {
      if (wciecieBorderRight[i] == 2)
      {
        locx = parseInt(basePointHorRight[i].x - sideUndercut);
      }
      else
      {
        locx = parseInt(basePointHorRight[i].x);
      }
      locy = parseInt(basePointHorRight[i].y + mat / 2);
      pointSideRightBottom.push(new Point3d(locx - mat / 2, locy, 0));

      locy = parseInt(basePointHorRight[i + 1].y - mat / 2);
      ptSideRightTop.push(new Point3d(locx - mat / 2, locy, 0));
    }

    locx = 0;
    locy = 0;
    for(i = 0; i <= doubleVertLocx.length; i++)
    {
      loczakres = 100 / (doubleVertLocx.length);
      zakresy.push(loczakres * i);
    }
    for (i = 0; i < dVertRows.length; i++)
    {
      var locxLeft = 0;
      var locxRight = 0;
      locxLeft = doubleVertLocx[i] + (maxWidth / 2 - currentWidth / 2);
      locxRight = locxLeft;
      if (parameter > zakresy[i + 1])
      {
        locxRight += dVertSize[i];
      }
      else if (parameter >= zakresy[i] && parameter <= zakresy[i + 1])
      {
        locxRight += parseInt(((parameter - zakresy[i]) * dVertSize[i]) / loczakres);
      }
      else
      {}
      locxLeft += dVertLeftSpeed[i] * parameter;
      locxRight += dVertRightSpeed[i] * parameter;
      locxLeftArr.push(locxLeft);
      locxRightArr.push(locxRight);
    }

    locxLeftArr[2] += parseInt(doubleVertLocx[0] * factordouble);
    locxLeftArr[4] += parseInt(doubleVertLocx[1] * factordouble);
    locxLeftArr[5] += parseInt(doubleVertLocx[1] * factordouble + doubleVertLocx[3] * factordouble + doubleVertLocx[4] * factordouble);
    locxLeftArr[6] += parseInt(doubleVertLocx[0] * factordouble + doubleVertLocx[2] * factordouble);
    locxRightArr[2] += parseInt(doubleVertLocx[0] * factordouble);
    locxRightArr[4] += parseInt(doubleVertLocx[1] * factordouble);
    locxRightArr[5] += parseInt(doubleVertLocx[1] * factordouble + doubleVertLocx[3] * factordouble + doubleVertLocx[4] * factordouble);
    locxRightArr[6] += parseInt(doubleVertLocx[0] * factordouble + doubleVertLocx[2] * factordouble);
    for (i = 0; i < dVertRows.length; i++)
    {
      if (locxRightArr[i] - locxLeftArr[i] < dVertMinWidth)
      {
        locxRightArr[i] = locxLeftArr[i];
      }
      var locvalx = 0;
      if (pointSideRightBottom[dVertRows[i]].x < pointSideRightBottom[dVertRows[i] + 1].x)
      {
        locvalx = parseFloat(pointSideRightBottom[dVertRows[i]].x);
      }
      else
      {
        locvalx = parseFloat(pointSideRightBottom[dVertRows[i] + 1].x);
      }
      if (locvalx - locxRightArr[i] <= dVertThreshold)
      {
        locxRightArr[i] = parseInt(locvalx);
      }
      if (locvalx - locxLeftArr[i] <= dVertThreshold)
      {
        locxLeftArr[i] = parseInt(locvalx);
        doubleVertExists.push(false);
      }
      else
      {
        doubleVertExists.push(true);
      }
      if (locxLeftArr[i] == locxRightArr[i])
      {
        doubleVertClosed.push(true);
      }
      else
      {
        doubleVertClosed.push(false);
      }
      dVertLeftBase.push(new Point3d(locxLeftArr[i] + mat / 2, basePointHorLeft[dVertRows[i]].y + mat / 2, 0));
      dVertRightBase.push(new Point3d(locxRightArr[i], basePointHorLeft[dVertRows[i]].y + mat / 2, 0));
    }
    for (i = 0; i < dVertLeftBase.length; i++)
    {
      if (doubleVertExists[i] == true)
      {
        doubleVertLeftALL.push(dVertLeftBase[i]);
        doubleVertLeftALL.push(new Point3d(dVertLeftBase[i].x, basePointHorLeft[dVertRows[i] + 1].y - mat / 2, 0));
        doubleVertLeftALL.push(new Point3d(dVertLeftBase[i].x, basePointHorLeft[dVertRows[i] + 1].y + mat / 2, 0));
        doubleVertLeftALL.push(new Point3d(dVertLeftBase[i].x, basePointHorLeft[dVertRows[i] + 2].y - mat / 2, 0));
      }
    }
    for (i = 0; i < dVertRightBase.length; i++)
    {
      if (doubleVertExists[i] == true && doubleVertClosed[i] == false)
      {
        doubleVertRightALL.push(dVertRightBase[i]);
        doubleVertRightALL.push(new Point3d(dVertRightBase[i].x, basePointHorLeft[dVertRows[i] + 1].y - mat / 2, 0));
        doubleVertRightALL.push(new Point3d(dVertRightBase[i].x, basePointHorLeft[dVertRows[i] + 1].y + mat / 2, 0));
        doubleVertRightALL.push(new Point3d(dVertRightBase[i].x, basePointHorLeft[dVertRows[i] + 2].y - mat / 2, 0));
      }
    }
    for (k = 0; k < doubleVertLeftALL.length; k += 4)
    {
      i = k / 4;
      if (doubleVertExists[i] == true)
      {
        var localH1 = parseFloat(doubleVertLeftALL[locali].y + rowsH[doubleRowLocation[i]] + rowsH[doubleRowLocation[i] + 1]);
        var localH2 = parseFloat(doubleVertLeftALL[locali].y + rowsH[doubleRowLocation[i]]);
        if (i == 1 && doubleVertClosed[i] == false && rows >= 7)
        {
          ptLeftTemp.push(doubleVertLeftALL[locali]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 3]);
        }
        else if (localH1 < (basePointHorLeft[rows].y + mat))
        {
          ptLeftTemp.push(doubleVertLeftALL[locali]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 1]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 2]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 3]);
        }
        else if (localH2 < (basePointHorLeft[rows].y + mat))
        {
          ptLeftTemp.push(doubleVertLeftALL[locali]);
          ptLeftTemp.push(doubleVertLeftALL[locali + 1]);
        }
        if (doubleVertClosed[i] == false)
        {
          if (i == arri[i] && doubleVertClosed[i] == false && rows >= (doubleRowLocation[i] + 2))
          {
            if (doubleVertRightALL[locali].x == basePointHorLeft[0].x + currentWidth - mat / 2 - undercutOpening[i])
            {
              ptRightTemp.push(doubleVertRightALL[locali]);
              ptRightTemp.push(doubleVertRightALL[locali + 3]);
            }
            else
            {
              ptRightTemp.push(doubleVertRightALL[locali]);
              ptRightTemp.push(doubleVertRightALL[locali + 1]);
              ptRightTemp.push(doubleVertRightALL[locali + 2]);
              ptRightTemp.push(doubleVertRightALL[locali + 3]);
            }
          }
          else if (localH1 < (basePointHorLeft[rows].y + mat))
          {
            ptRightTemp.push(doubleVertRightALL[locali]);
            ptRightTemp.push(doubleVertRightALL[locali + 1]);
            ptRightTemp.push(doubleVertRightALL[locali + 2]);
            ptRightTemp.push(doubleVertRightALL[locali + 3]);
          }
          else if (localH2 < (basePointHorLeft[rows].y + mat))
          {
            ptRightTemp.push(doubleVertRightALL[locali]);
            ptRightTemp.push(doubleVertRightALL[locali + 1]);
          }
        }
      }
      locali += 4;
    }
    if (doubleVertExists[1] == true && doubleVertClosed[1] == false)
    {
      if ((locxRightArr[1] - basePointHorLeft[6].x) > dVertThreshold * 2)
      {
        if (rows >= 7)
        {
          doubleBackBottomCURRENT.push(new Point3d(basePointHorLeft[5].x + mat, basePointHorLeft[5].y + mat / 2, 0));
          doubleBackTopCURRENT.push(new Point3d(basePointHorLeft[5].x + mat / 2 + backWidth, basePointHorLeft[7].y - mat / 2, 0));
        }
        else if (rows == 6)
        {
          doubleBackBottomCURRENT.push(new Point3d(basePointHorLeft[5].x + mat, basePointHorLeft[5].y + mat / 2, 0));
          doubleBackTopCURRENT.push(new Point3d(basePointHorLeft[5].x + mat / 2 + backWidth, basePointHorLeft[6].y - mat / 2, 0));
        }
      }
    }
    if (doubleVertExists[5] == true && doubleVertClosed[5] == false)
    {
      if ((locxRightArr[5] - locxLeftArr[5]) > dVertThreshold * 2)
      {
        if (rows >= 6)
        {
          doubleBackBottomCURRENT.push(new Point3d(locxRightArr[5] - mat / 2 - backWidth, pointSideRightBottom[4].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locxRightArr[5] - mat / 2, pointSideRightBottom[6].y - mat, 0));
        }
        else if (rows == 5)
        {
          doubleBackBottomCURRENT.push(new Point3d(locxRightArr[5] - mat / 2 - backWidth, pointSideRightBottom[4].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locxRightArr[5] - mat / 2, pointSideRightBottom[5].y - mat, 0));
        }
      }
    }
    if (doubleVertExists[6] == true && doubleVertClosed[6] == false)
    {
      if ((locxRightArr[6] - locxLeftArr[6]) > dVertThreshold * 2)
      {
        if (rows >= 3)
        {
          doubleBackBottomCURRENT.push(new Point3d(locxRightArr[6] - mat / 2 - backWidth, pointSideRightBottom[1].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locxRightArr[6] - mat / 2, pointSideRightBottom[3].y - mat, 0));
        }
        else if (rows == 2)
        {
          doubleBackBottomCURRENT.push(new Point3d(locxRightArr[6] - mat / 2 - backWidth, pointSideRightBottom[1].y, 0));
          doubleBackTopCURRENT.push(new Point3d(locxRightArr[6] - mat / 2, pointSideRightBottom[2].y - mat, 0));
        }
      }
    }
    for (i = 0; i < ptLeftTemp.length; i += 2)
    {
      doubleVertLeftCURRENTBottom.push(new Point3d(ptLeftTemp[i].x, ptLeftTemp[i].y, ptLeftTemp[i].z));
    }
    for (i = 1; i < ptLeftTemp.length; i += 2)
    {
      doubleVertLeftCURRENTTop.push(new Point3d(ptLeftTemp[i].x, ptLeftTemp[i].y, ptLeftTemp[i].z));
    }
    for (i = 0; i < ptRightTemp.length; i += 2)
    {
      doubleVertRightCURRENTBottom.push(new Point3d(ptRightTemp[i].x, ptRightTemp[i].y, ptRightTemp[i].z));
    }
    for (i = 1; i < ptRightTemp.length; i += 2)
    {
      doubleVertRightCURRENTTop.push(new Point3d(ptRightTemp[i].x, ptRightTemp[i].y, ptRightTemp[i].z));
    }
    sVertAdd.push(sVertSpeed[0] * parameter);
    sVertAdd.push(doubleVertLocx[2] * factorsingle + sVertSpeed[1] * parameter);
    sVertAdd.push(doubleVertLocx[2] * factorsingle + sVertSpeed[2] * parameter);
    sVertAdd.push(doubleVertLocx[0] * factorsingle + sVertSpeed[3] * parameter);
    sVertAdd.push(doubleVertLocx[0] * factorsingle + doubleVertLocx[6] * factorsingle + sVertSpeed[4] * parameter);
    sVertAdd.push(doubleVertLocx[3] * factorsingle + sVertSpeed[5] * parameter);
    sVertAdd.push(doubleVertLocx[3] * factorsingle + sVertSpeed[6] * parameter);
    sVertAdd.push(sVertSpeed[7] * parameter);
    sVertAdd.push(doubleVertLocx[1] * factorsingle + sVertSpeed[8] * parameter);
    sVertAdd.push(doubleVertLocx[1] * factorsingle + doubleVertLocx[4] * factorsingle + doubleVertLocx[5] * factorsingle + sVertSpeed[9] * parameter);
    sVertAdd.push(doubleVertLocx[1] * factorsingle + sVertSpeed[10] * parameter);
    sVertAdd.push(doubleVertLocx[1] * factorsingle + doubleVertLocx[4] * factorsingle + sVertSpeed[11] * parameter);
    sVertAdd.push(sVertSpeed[12] * parameter);
    sVertAdd.push(sVertSpeed[13] * parameter);
    sVertAdd.push(sVertSpeed[14] * parameter);
    for (i = 0; i <= sVertLocx.length - 1; i++)
    {
      var locvalx = 0;
      var locvaly = 0;
      locvalx = parseInt(sVertLocx[i] + sVertAdd[i] + basePointHorLeft[0].x);
      locvaly = parseInt(basePointHorLeft[sVertRows[i]].y + mat / 2);
      pointSingleBottom.push(new Point3d(locvalx, locvaly, 0));
      locvaly = parseInt(basePointHorLeft[sVertRows[i] + 1].y - mat / 2);
      pointSingleTop.push(new Point3d(locvalx, locvaly, 0));
      singleVertExists.push(true);
    }
    for (i = 0; i <= pointSingleTop.length - 1; i++)
    {
      if (pointSingleTop[i].y > basePointHorLeft[rows].y)
      {
        singleVertExists[i] = false;
      }
      if (pointSingleTop[i].x > (pointSideRightBottom[sVertRows[i]].x - singleVerticalThreshold))
      {
        singleVertExists[i] = false;
      }
    }
    for (i = 0; i <= singleVertExists.length - 1; i++)
    {
      if (singleVertExists[i] == true)
      {
        pointSingleBottomCURRENT.push(pointSingleBottom[i]);
        pointSingleTopCURRENT.push(pointSingleTop[i]);
      }
    }
    for (i = 0; i <= indexSingleVertical.length - 1; i++)
    {
      if (sVertOrientation[i] == -1)
      {
        pointSingleBackTop.push(new Point3d(pointSingleTop[indexSingleVertical[i]].x - mat / 2, pointSingleTop[indexSingleVertical[i]].y, 0));
        pointSingleBackBottom.push(new Point3d(pointSingleBottom[indexSingleVertical[i]].x - mat / 2 - backWidth, pointSingleBottom[indexSingleVertical[i]].y, 0));
      }
      else
      {
        pointSingleBackTop.push(new Point3d(pointSingleTop[indexSingleVertical[i]].x + mat / 2 + backWidth, pointSingleTop[indexSingleVertical[i]].y, 0));
        pointSingleBackBottom.push(new Point3d(pointSingleBottom[indexSingleVertical[i]].x + mat / 2, pointSingleBottom[indexSingleVertical[i]].y, 0));
      }
      singleBackExists.push(true);
    }
    for (i = 0; i <= pointSingleBackTop.length - 1; i++)
    {
      if (pointSingleBackTop[i].y > basePointHorLeft[rows].y)
      {
        singleBackExists[i] = false;
      }
      if (pointSingleBackTop[i].x > (pointSideRightBottom[backRows[i]].x - singleBackThreshold))
      {
        singleBackExists[i] = false;
      }
    }
    for (i = 0; i <= singleBackExists.length - 1; i++)
    {
      if (singleBackExists[i] == true)
      {
        singleBackBottomCURRENT.push(pointSingleBackBottom[i]);
        singleBackTopCURRENT.push(pointSingleBackTop[i]);
      }
    }

    locx = 0;
    locy = 0;
    for (i = 0; i <= basePointHorLeft.length - 2; i++)
    {
      boolBorderRight.push(true);
    }
    pointHorizontalLeft.push(new Point3d((maxWidth / 2 - currentWidth / 2), locy, 0));
    pointHorizontalRight.push(new Point3d((maxWidth / 2 + currentWidth / 2), locy, 0));
    for (i = 0; i <= indexHor.length - 1; i++)
    {
      if (rows >= indexHor[i])
      {
        locy = parseFloat(pointSideLeftTop[indexHor[i] - 1].y + mat / 2);
        pointHorizontalLeft.push(new Point3d((maxWidth / 2 - currentWidth / 2), locy, 0));
        pointHorizontalRight.push(new Point3d((maxWidth / 2 + currentWidth / 2), locy, 0));
      }
    }

    index1 = 2;
    pointLeft = basePointHorLeft[1];
    pointRight = basePointHorRight[1];
    indexDoubleVert = 8;
    if ((doubleVertExists[index1] == false) | (doubleVertClosed[index1] == true) | (rows == 1))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL[indexDoubleVert].x + mat / 2 == pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      boolBorderRight[0] = false;
      boolBorderRight[1] = false;
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL[indexDoubleVert].x < pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    index1 = 3;
    pointLeft = basePointHorLeft[4];
    pointRight = basePointHorRight[4];
    indexDoubleVert = 12;
    if (rows <= 3)
    {
    }
    else if ((doubleVertExists[index1] == false) | (doubleVertClosed[index1] == true) | (rows == 4) | (doubleVertRightALL.length == indexDoubleVert))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL.length > indexDoubleVert))
    {
      if (doubleVertRightALL[indexDoubleVert].x + mat / 2 == pointRight.x - sideUndercut)
      {
        pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
        pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
        boolBorderRight[3] = false;
        boolBorderRight[4] = false;
      }
      else
      {
        pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
        pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
        pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointLeft.y, 0));
        pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
      }
    }
    index1 = 5;
    pointLeft = basePointHorLeft[5];
    pointRight = basePointHorRight[5];
    indexDoubleVert = 20;
    if (rows <= 4)
    {
    }
    else if ((doubleVertExists[index1] == false) | (doubleVertClosed[index1] == true) | (rows == 5))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL[indexDoubleVert].x + mat / 2 == pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      boolBorderRight[4] = false;
      boolBorderRight[5] = false;
    }
    else if ((doubleVertExists[index1] == true) && (doubleVertClosed[index1] == false) && (doubleVertRightALL[indexDoubleVert].x < pointRight.x))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }

    index1 = 0;
    index2 = 6;
    pointLeft = basePointHorLeft[2];
    pointRight = basePointHorRight[2];
    indexDoubleVert = 0;
    indexDoubleVert2 = 24;
    if (rows <= 1)
    {
    }
    else if (doubleVertExists[index1] == false | (doubleVertExists[index1] == true && doubleVertClosed[index1] == true && doubleVertExists[index2] == false) | (doubleVertClosed[index1] == true && doubleVertClosed[index2] == true) | rows == 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x == pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      boolBorderRight[1] = false;
      boolBorderRight[2] = false;
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x < pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && doubleVertRightALL[indexDoubleVert2].x < pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert2].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && doubleVertRightALL[indexDoubleVert2].x == pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      boolBorderRight[1] = false;
      boolBorderRight[2] = false;
    }
    index1 = 1;
    index2 = 4;
    pointLeft = basePointHorLeft[6];
    pointRight = basePointHorRight[6];
    indexDoubleVert = 4;
    indexDoubleVert2 = 16;
    if (rows < 6)
    {
    }
    else if ((rows == 6) | (doubleVertExists[index1] == false) | (doubleVertExists[index1] == true && doubleVertClosed[index1] == true && doubleVertExists[index2] == false) | (doubleVertClosed[index1] == true && doubleVertClosed[index2] == true))
    {
      pointHorizontalLeft.push(new Point3d(pointLeft.x, pointLeft.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x == pointRight.x - mat / 2 - sideUndercut)
    {
      boolBorderRight[5] = false;
      boolBorderRight[6] = false;
    }
    else if ( doubleVertExists[index1] == true && doubleVertClosed[index1] == false && (doubleVertExists[index2] == false | doubleVertClosed[index2] == true) && doubleVertRightALL[indexDoubleVert].x < pointRight.x - mat / 2)
    {
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if ( doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && ((pointRight.x - mat / 2 - sideUndercut) - doubleVertRightALL[indexDoubleVert2].x > 1))
    {
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert2].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(pointRight.x, pointRight.y, 0));
    }
    else if (( doubleVertExists[index1] == true && doubleVertClosed[index1] == false && doubleVertExists[index2] == true && doubleVertClosed[index2] == false && ((pointRight.x - mat / 2 - sideUndercut) - doubleVertRightALL[indexDoubleVert2].x < 1)))
    {
      pointHorizontalLeft.push(new Point3d(doubleVertRightALL[indexDoubleVert].x - mat / 2, pointRight.y, 0));
      pointHorizontalRight.push(new Point3d(doubleVertLeftALL[indexDoubleVert2].x + mat / 2, pointRight.y, 0));
      boolBorderRight[5] = false;
      boolBorderRight[6] = false;
    }
    for ( i = 0; i <= rows - 1; i++)
    {
      if ( i < 5 | i > 6)
      {
        if (wciecieBorderLeft[i] == 1)
        {
          pointBorderLeftBottom.push(new Point3d(basePointHorLeft[i].x + mat / 2, basePointHorLeft[i].y + mat / 2, 0));
          pointBorderLeftTop.push(new Point3d(basePointHorLeft[i].x + mat / 2, basePointHorLeft[i + 1].y - mat / 2, 0));
        }
        else
        {
          pointBorderLeftBottom.push(new Point3d(basePointHorLeft[i].x + mat / 2 + sideUndercut, basePointHorLeft[i].y + mat / 2, 0));
          pointBorderLeftTop.push(new Point3d(basePointHorLeft[i].x + mat / 2 + sideUndercut, basePointHorLeft[i + 1].y - mat / 2, 0));
        }
      }
    }
    for (i = 0; i <= rows - 1; i++)
    {
      if (boolBorderRight[i] == true)
      {
        if (wciecieBorderRight[i] == 1)
        {
          pointBorderRightBottom.push(new Point3d(basePointHorRight[i].x - mat / 2, basePointHorRight[i].y + mat / 2, 0));
          pointBorderRightTop.push(new Point3d(basePointHorRight[i].x - mat / 2, basePointHorRight[i + 1].y - mat / 2, 0));
        }
        else
        {
          pointBorderRightBottom.push(new Point3d(basePointHorRight[i].x - mat / 2 - sideUndercut, basePointHorRight[i].y + mat / 2, 0));
          pointBorderRightTop.push(new Point3d(basePointHorRight[i].x - mat / 2 - sideUndercut, basePointHorRight[i + 1].y - mat / 2, 0));
        }
      }
    }
    for (i = 0; i <= rows - 1; i++)
    {
      if(i == 1 | i == 3)
      {
        singlePointBackBottom.push(new Point3d(basePointHorLeft[i].x + mat, basePointHorLeft[i].y + mat / 2, 0));
        singlePointBackTop.push(new Point3d(basePointHorLeft[i].x + mat + backWidth, basePointHorLeft[i + 1].y - mat / 2, 0));
      }
      else if (i == 4 | i == 7)
      {
        singlePointBackBottom.push(new Point3d(basePointHorLeft[i].x + mat + sideUndercut, basePointHorLeft[i].y + mat / 2, 0));
        singlePointBackTop.push(new Point3d(basePointHorLeft[i].x + mat + backWidth + sideUndercut, basePointHorLeft[i + 1].y - mat / 2, 0));
      }
    }
    if ((doubleVertExists[6] == true) && (doubleVertClosed[6] == false) && (rows > 1))
    {
      singlePointBackBottom.push(new Point3d(doubleVertLeftALL[24].x - mat / 2 - backWidth, doubleVertLeftALL[24].y, 0));
      singlePointBackTop.push(new Point3d(doubleVertLeftALL[24].x - mat / 2, doubleVertLeftALL[24 + 1].y, 0));
    }
    if ((doubleVertExists[5] == true) && (doubleVertClosed[5] == false) && (rows > 5))
    {
      singlePointBackBottom.push(new Point3d(doubleVertLeftALL[22].x - mat / 2 - backWidth, doubleVertLeftALL[22].y, 0));
      singlePointBackTop.push(new Point3d(doubleVertLeftALL[22].x - mat / 2, doubleVertLeftALL[22 + 1].y, 0));
    }
    if ((doubleVertExists[0] == true) && (doubleVertClosed[0] == false) && (rows > 2))
    {
      if ((doubleVertLeftALL[2].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold))
      {
        singlePointBackBottom.push(new Point3d(doubleVertLeftALL[2].x - mat / 2 - backWidth, doubleVertLeftALL[2].y, 0));
        singlePointBackTop.push(new Point3d(doubleVertLeftALL[2].x - mat / 2, doubleVertLeftALL[2 + 1].y, 0));
      }
    }
    else if (doubleVertExists[0] == true && doubleVertClosed[0] == true && rows > 2)
    {
      if ((doubleVertLeftALL[2].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold))
      {
        singlePointBackBottom.push(new Point3d(doubleVertLeftALL[2].x + mat / 2 + backWidth, doubleVertLeftALL[2].y, 0));
        singlePointBackTop.push(new Point3d(doubleVertLeftALL[2].x + mat / 2, doubleVertLeftALL[2 + 1].y, 0));
      }
    }
    if (doubleVertExists[4] == true && doubleVertClosed[4] == false && rows > 6)
    {
      if((doubleVertRightALL[18].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold - sideUndercut))
      {
        singlePointBackBottom.push(new Point3d(doubleVertRightALL[18].x + mat / 2 + backWidth, doubleVertRightALL[18].y, 0));
        singlePointBackTop.push(new Point3d(doubleVertRightALL[18].x + mat / 2, doubleVertRightALL[18 + 1].y, 0));
      }
      else if (doubleVertExists[4] == true && doubleVertClosed[4] == true && rows > 6)
      {
        if ((doubleVertLeftALL[18].x + mat / 2 + backWidth) < (basePointHorRight[0].x - singleVerticalThreshold - sideUndercut))
        {
          singlePointBackBottom.push(new Point3d(doubleVertLeftALL[18].x + mat / 2 + backWidth, doubleVertLeftALL[18].y, 0));
          singlePointBackTop.push(new Point3d(doubleVertLeftALL[18].x + mat / 2, doubleVertLeftALL[18 + 1].y, 0));
        }
      }
    }

    var ptHorizontalsAll = [];
    for (i = 0; i < pointHorizontalLeft.length; i++)
    {
      ptHorizontalsAll.push(new Point3d(pointHorizontalLeft[i].x, pointHorizontalLeft[i].y, pointHorizontalLeft[i].z));
      ptHorizontalsAll.push(new Point3d(pointHorizontalRight[i].x, pointHorizontalRight[i].y, pointHorizontalRight[i].z));
    }
    var supportsShift = 2;
    var ptSupportsAllTop = [];
    for ( i = 0; i < singlePointBackTop.length; i++)
    {
      ptSupportsAllTop.push(new Point3d(singlePointBackTop[i].x, singlePointBackTop[i].y, supportsShift));
    }
    for ( i = 0; i < singleBackTopCURRENT.length; i++)
    {
      ptSupportsAllTop.push(new Point3d(singleBackTopCURRENT[i].x, singleBackTopCURRENT[i].y, supportsShift));
    }
    for ( i = 0; i < doubleBackTopCURRENT.length; i++)
    {
      ptSupportsAllTop.push(new Point3d(doubleBackTopCURRENT[i].x, doubleBackTopCURRENT[i].y, supportsShift));
    }
    var ptSupportsAllBottom = [];
    for ( i = 0; i < singlePointBackBottom.length; i++)
    {
      ptSupportsAllBottom.push(new Point3d(singlePointBackBottom[i].x, singlePointBackBottom[i].y, supportsShift));
    }
    for ( i = 0; i < singleBackBottomCURRENT.length; i++)
    {
      ptSupportsAllBottom.push(new Point3d(singleBackBottomCURRENT[i].x, singleBackBottomCURRENT[i].y, supportsShift));
    }
    for ( i = 0; i < doubleBackBottomCURRENT.length; i++)
    {
      ptSupportsAllBottom.push(new Point3d(doubleBackBottomCURRENT[i].x, doubleBackBottomCURRENT[i].y, supportsShift));
    }
    var ptSupportsAll = [];
    for ( i = 0; i < ptSupportsAllTop.length; i++)
    {
      ptSupportsAll.push(new Point3d(ptSupportsAllTop[i].x, ptSupportsAllTop[i].y, ptSupportsAllTop[i].z));
      ptSupportsAll.push(new Point3d(ptSupportsAllBottom[i].x, ptSupportsAllBottom[i].y, ptSupportsAllBottom[i].z));
    }
    var ptVerticalsAllTop = [];
    for ( i = 0; i < pointSingleTopCURRENT.length; i++)
    {
      ptVerticalsAllTop.push(new Point3d(pointSingleTopCURRENT[i].x, pointSingleTopCURRENT[i].y, pointSingleTopCURRENT[i].z));
    }
    for ( i = 0; i < pointBorderLeftTop.length; i++)
    {
      ptVerticalsAllTop.push(new Point3d(pointBorderLeftTop[i].x, pointBorderLeftTop[i].y, pointBorderLeftTop[i].z));
    }
    for ( i = 0; i < pointBorderRightTop.length; i++)
    {
      ptVerticalsAllTop.push(new Point3d(pointBorderRightTop[i].x, pointBorderRightTop[i].y, pointBorderRightTop[i].z));
    }
    for ( i = 0; i < doubleVertLeftCURRENTTop.length; i++)
    {
      ptVerticalsAllTop.push(new Point3d(doubleVertLeftCURRENTTop[i].x, doubleVertLeftCURRENTTop[i].y, doubleVertLeftCURRENTTop[i].z));
    }
    for ( i = 0; i < doubleVertRightCURRENTTop.length; i++)
    {
      ptVerticalsAllTop.push(new Point3d(doubleVertRightCURRENTTop[i].x, doubleVertRightCURRENTTop[i].y, doubleVertRightCURRENTTop[i].z));
    }
    var ptVerticalsAllBottom = [];
    for ( i = 0; i < pointSingleBottomCURRENT.length; i++)
    {
      ptVerticalsAllBottom.push(new Point3d(pointSingleBottomCURRENT[i].x, pointSingleBottomCURRENT[i].y, pointSingleBottomCURRENT[i].z));
    }
    for ( i = 0; i < pointBorderLeftBottom.length; i++)
    {
      ptVerticalsAllBottom.push(new Point3d(pointBorderLeftBottom[i].x, pointBorderLeftBottom[i].y, pointBorderLeftBottom[i].z));
    }
    for ( i = 0; i < pointBorderRightBottom.length; i++)
    {
      ptVerticalsAllBottom.push(new Point3d(pointBorderRightBottom[i].x, pointBorderRightBottom[i].y, pointBorderRightBottom[i].z));
    }
    for ( i = 0; i < doubleVertLeftCURRENTBottom.length; i++)
    {
      ptVerticalsAllBottom.push(new Point3d(doubleVertLeftCURRENTBottom[i].x, doubleVertLeftCURRENTBottom[i].y, doubleVertLeftCURRENTBottom[i].z));
    }
    for ( i = 0; i < doubleVertRightCURRENTBottom.length; i++)
    {
      ptVerticalsAllBottom.push(new Point3d(doubleVertRightCURRENTBottom[i].x, doubleVertRightCURRENTBottom[i].y, doubleVertRightCURRENTBottom[i].z));
    }
    var ptVerticalsAll = [];
    for (i = 0; i < ptVerticalsAllTop.length; i++)
    {
      ptVerticalsAll.push(new Point3d(ptVerticalsAllTop[i].x, ptVerticalsAllTop[i].y, ptVerticalsAllTop[i].z));
      ptVerticalsAll.push(new Point3d(ptVerticalsAllBottom[i].x, ptVerticalsAllBottom[i].y, ptVerticalsAllBottom[i].z));
    }

    locx = 0;
    locy = 0;
    for (i = 0; i < pointBorderLeftBottom.length; i++)
    {
      if ( i < rows && (pointBorderLeftBottom[i].x == pointBorderLeftBottom[0].x + sideUndercut))
      {
        widthBorderLeft.push(sideUndercut);
        heightBorderLeft.push(parseInt(pointBorderLeftTop[i].y - pointBorderLeftBottom[i].y));
        locx = parseInt(pointBorderLeftBottom[i].x - sideUndercut / 2 - mat / 2);
        locy = parseInt(pointBorderLeftTop[i].y - (pointBorderLeftTop[i].y - pointBorderLeftBottom[i].y) / 2);
        shadowLeftLocation.push(new Vector3d(locx - shelfMax / 2, locy, 0));
      }
    }
    for (i = 0; i < widthBorderLeft.length; i++)
    {
      shadowLeftSize.push(new Vector3d(widthBorderLeft[i], heightBorderLeft[i], shelfDepth));
    }
    for (  i = 0; i < pointBorderRightBottom.length; i++ )
    {
      if ( pointBorderRightBottom[i].x == pointBorderRightBottom[0].x - sideUndercut )
      {
        widthBorderRight.push(sideUndercut);
        heightBorderRight.push(parseInt(pointBorderRightTop[i].y - pointBorderRightBottom[i].y));
        locx = parseInt(pointBorderRightBottom[i].x + sideUndercut / 2 + mat / 2);
        locy = parseInt(pointBorderRightTop[i].y - (pointBorderRightTop[i].y - pointBorderRightBottom[i].y) / 2);
        shadowRightLocation.push(new Vector3d(locx - shelfMax / 2, locy, 0));
      }
    }
    for ( i = 0; i < widthBorderRight.length; i++ )
    {
      shadowRightSize.push(new Vector3d(widthBorderRight[i], heightBorderRight[i], shelfDepth));
    }

    for ( i = 0; i < doubleVertExists.length; i++ )
    {
      if ( doubleVertExists[i] == true && doubleVertClosed[i] == false )
      {
        locali = i * 4;
        if ( rows > dVertRows[i] + 1 )
        {
          widthMiddle.push(parseInt(doubleVertRightALL[locali].x - doubleVertLeftALL[locali].x - mat));
          heightMiddle.push(parseInt(doubleVertLeftALL[locali + 3].y - doubleVertLeftALL[locali].y));
          locx = parseInt(doubleVertLeftALL[locali].x + (doubleVertRightALL[locali].x - doubleVertLeftALL[locali].x) / 2);
          locy = parseInt(doubleVertLeftALL[locali].y + (doubleVertLeftALL[locali + 3].y - doubleVertLeftALL[locali].y) / 2);
          shadowDoubleLocation.push(new Point3d(locx - shelfMax / 2, locy, 0));
          shadowMiddleLocation.push(shadowDoubleLocation[shadowDoubleLocation.length - 1]);
        }
        else if ( rows > dVertRows[i] )
        {
          widthMiddle.push(parseInt(doubleVertRightALL[locali].x - doubleVertLeftALL[locali].x - mat));
          heightMiddle.push(parseInt(doubleVertLeftALL[locali + 1].y - doubleVertLeftALL[locali].y));
          locx = parseInt(doubleVertLeftALL[locali].x + (doubleVertRightALL[locali].x - doubleVertLeftALL[locali].x) / 2);
          locy = parseInt(doubleVertLeftALL[locali].y + (doubleVertLeftALL[locali + 1].y - doubleVertLeftALL[locali].y) / 2);
          shadowDoubleLocation.push(new Point3d(locx - shelfMax / 2, locy, 0));
          shadowMiddleLocation.push(shadowDoubleLocation[shadowDoubleLocation.length - 1]);
        }
      }
    }
    for ( i = 0; i < widthMiddle.length; i++ ) {
      shadowDoubleSize.push(new Vector3d(widthMiddle[i], heightMiddle[i], shelfDepth));
      shadowMiddleSize.push(shadowDoubleSize[shadowDoubleSize.length - 1]);
    }

    for (var i = 0, point; (point = pointBorderLeftBottom[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = pointSingleBottomCURRENT[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = pointBorderRightBottom[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = doubleVertLeftALL[i]); i++)
      verticalsPoints.push(point);
    for (var i = 0, point; (point = doubleVertRightALL[i]); i++)
      verticalsPoints.push(point);
    var verticalsPointsClear = verticalsPoints._distinctPoints();
    verticalsPointsClear.sort(function(a,b) { return a.x - b.x });;

    for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      if ( parseInt(point.y) == rowsAbsolute[0] + mat / 2)
      {
        verticalsPointsRow0.push(point);
      }
    if ( doubleVertClosed[2] == true )
    {
      for ( i = 0; i <= verticalsPointsRow0.length - 2; i++ )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow0[i].x + (verticalsPointsRow0[i + 1].x - verticalsPointsRow0[i].x) / 2 - shelfMax / 2, verticalsPointsRow0[i].y + (rowsH[0] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow0[i + 1].x - verticalsPointsRow0[i].x - mat, rowsH[0] - mat, shelfDepth));
      }
    }
    if ( doubleVertClosed[2] == false )
    {
      for (  i = 0; i <= 1; i++ )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow0[i].x + (verticalsPointsRow0[i + 1].x - verticalsPointsRow0[i].x) / 2 - shelfMax / 2, verticalsPointsRow0[i].y + (rowsH[0] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow0[i + 1].x - verticalsPointsRow0[i].x - mat, rowsH[0] - mat, shelfDepth));
      }
      for (  i = 3; i <= verticalsPointsRow0.length - 2; i++ )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow0[i].x + (verticalsPointsRow0[i + 1].x - verticalsPointsRow0[i].x) / 2 - shelfMax / 2, verticalsPointsRow0[i].y + (rowsH[0] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow0[i + 1].x - verticalsPointsRow0[i].x - mat, rowsH[0] - mat, shelfDepth));
      }
    }
    if ( rows > 1 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[1] + mat / 2)
        {
          verticalsPointsRow1.push(point);
        }
      }
      if ( doubleVertClosed[0] == true )
      {
        for (  i = 0; i <= verticalsPointsRow1.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[i].x + (verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x) / 2 - shelfMax / 2, verticalsPointsRow1[i].y + (rowsH[1] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x - mat, rowsH[1] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[0] == false && doubleVertClosed[2] == true )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[0].x + (verticalsPointsRow1[1].x - verticalsPointsRow1[0].x) / 2 - shelfMax / 2, verticalsPointsRow1[0].y + (rowsH[1] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[1].x - verticalsPointsRow1[0].x - mat, rowsH[1] - mat, shelfDepth));
        for (  i = 2; i <= verticalsPointsRow1.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[i].x + (verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x) / 2 - shelfMax / 2, verticalsPointsRow1[i].y + (rowsH[1] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x - mat, rowsH[1] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[2] == false && doubleVertClosed[6] == true )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[0].x + (verticalsPointsRow1[1].x - verticalsPointsRow1[0].x) / 2 - shelfMax / 2, verticalsPointsRow1[0].y + (rowsH[1] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[1].x - verticalsPointsRow1[0].x - mat, rowsH[1] - mat, shelfDepth));
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[2].x + (verticalsPointsRow1[3].x - verticalsPointsRow1[2].x) / 2 - shelfMax / 2, verticalsPointsRow1[2].y + (rowsH[1] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[3].x - verticalsPointsRow1[2].x - mat, rowsH[1] - mat, shelfDepth));
        if ( verticalsPointsRow1.length > 5 )
        {
          for ( i = 4; i <= verticalsPointsRow1.length - 2; i++ )
          {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[i].x + (verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x) / 2 - shelfMax / 2, verticalsPointsRow1[i].y + (rowsH[1] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x - mat, rowsH[1] - mat, shelfDepth));
          }
        }
      }
      if ( doubleVertClosed[6] == false )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[0].x + (verticalsPointsRow1[1].x - verticalsPointsRow1[0].x) / 2 - shelfMax / 2, verticalsPointsRow1[0].y + (rowsH[1] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[1].x - verticalsPointsRow1[0].x - mat, rowsH[1] - mat, shelfDepth));
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[2].x + (verticalsPointsRow1[3].x - verticalsPointsRow1[2].x) / 2 - shelfMax / 2, verticalsPointsRow1[2].y + (rowsH[1] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[3].x - verticalsPointsRow1[2].x - mat, rowsH[1] - mat, shelfDepth));
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[4].x + (verticalsPointsRow1[5].x - verticalsPointsRow1[4].x) / 2 - shelfMax / 2, verticalsPointsRow1[4].y + (rowsH[1] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[5].x - verticalsPointsRow1[4].x - mat, rowsH[1] - mat, shelfDepth));
        if ( verticalsPointsRow1.length > 6 )
        {
          for (  i = 6; i <= verticalsPointsRow1.length - 2; i++ )
          {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow1[i].x + (verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x) / 2 - shelfMax / 2, verticalsPointsRow1[i].y + (rowsH[1] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow1[i + 1].x - verticalsPointsRow1[i].x - mat, rowsH[1] - mat, shelfDepth));
          }
        }
      }
    }
    if ( rows > 2 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[2] + mat / 2)
        {
          verticalsPointsRow2.push(point);
        }
      }
      if ( doubleVertClosed[0] == true )
      {
        for ( i = 0; i <= verticalsPointsRow2.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow2[i].x + (verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x) / 2 - shelfMax / 2, verticalsPointsRow2[i].y + (rowsH[2] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x - mat, rowsH[2] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[0] == false && doubleVertClosed[6] == true )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow2[0].x + (verticalsPointsRow2[1].x - verticalsPointsRow2[0].x) / 2 - shelfMax / 2, verticalsPointsRow2[0].y + (rowsH[2] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow2[1].x - verticalsPointsRow2[0].x - mat, rowsH[2] - mat, shelfDepth));
        if ( verticalsPointsRow2.length > 3 )
        {
          for (  i = 2; i <= verticalsPointsRow2.length - 2; i++ )
          {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow2[i].x + (verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x) / 2 - shelfMax / 2, verticalsPointsRow2[i].y + (rowsH[2] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x - mat, rowsH[2] - mat, shelfDepth));
          }
        }
      }
      if ( doubleVertClosed[6] == false )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow2[0].x + (verticalsPointsRow2[1].x - verticalsPointsRow2[0].x) / 2 - shelfMax / 2, verticalsPointsRow2[0].y + (rowsH[2] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow2[1].x - verticalsPointsRow2[0].x - mat, rowsH[2] - mat, shelfDepth));
        if ( verticalsPointsRow2.length > 5 )
        {
          for (  i = 2; i <= 3; i++ )
          {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow2[i].x + (verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x) / 2 - shelfMax / 2, verticalsPointsRow2[i].y + (rowsH[2] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x - mat, rowsH[2] - mat, shelfDepth));
          }
          for (  i = 5; i <= verticalsPointsRow2.length - 2; i++ )
          {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow2[i].x + (verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x) / 2 - shelfMax / 2, verticalsPointsRow2[i].y + (rowsH[2] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow2[i + 1].x - verticalsPointsRow2[i].x - mat, rowsH[2] - mat, shelfDepth));
          }
        }
      }
    }
    if ( rows > 3 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[3] + mat / 2)
          verticalsPointsRow3.push(point);
      }
      if ( doubleVertClosed[3] == true )
      {
        for ( i = 0; i <= verticalsPointsRow3.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow3[i].x + (verticalsPointsRow3[i + 1].x - verticalsPointsRow3[i].x) / 2 - shelfMax / 2, verticalsPointsRow3[i].y + (rowsH[3] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow3[i + 1].x - verticalsPointsRow3[i].x - mat, rowsH[3] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[3] == false )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow3[0].x + (verticalsPointsRow3[1].x - verticalsPointsRow3[0].x) / 2 - shelfMax / 2, verticalsPointsRow3[0].y + (rowsH[3] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow3[1].x - verticalsPointsRow3[0].x - mat, rowsH[3] - mat, shelfDepth));
        if ( verticalsPointsRow3.length > 2 )
        {
          for (  i = 2; i <= verticalsPointsRow3.length - 2; i++ )
          {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow3[i].x + (verticalsPointsRow3[i + 1].x - verticalsPointsRow3[i].x) / 2 - shelfMax / 2, verticalsPointsRow3[i].y + (rowsH[3] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow3[i + 1].x - verticalsPointsRow3[i].x - mat, rowsH[3] - mat, shelfDepth));
          }
        }
      }
    }
    if ( rows > 4 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[4] + mat / 2)
          verticalsPointsRow4.push(point);
      }
      if ( doubleVertClosed[3] == true )
      {
        for (  i = 0; i <= verticalsPointsRow4.length - 2; i++ ) {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow4[i].x + (verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x) / 2 - shelfMax / 2, verticalsPointsRow4[i].y + (rowsH[4] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x - mat, rowsH[4] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[3] == false && doubleVertClosed[5] == true )
      {
        for ( i = 0; i <= 1; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow4[i].x + (verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x) / 2 - shelfMax / 2, verticalsPointsRow4[i].y + (rowsH[4] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x - mat, rowsH[4] - mat, shelfDepth));
        }
        for ( i = 3; i <= verticalsPointsRow4.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow4[i].x + (verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x) / 2 - shelfMax / 2, verticalsPointsRow4[i].y + (rowsH[4] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x - mat, rowsH[4] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[5] == false )
      {
        for ( i = 0; i <= 1; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow4[i].x + (verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x) / 2 - shelfMax / 2, verticalsPointsRow4[i].y + (rowsH[4] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow4[i + 1].x - verticalsPointsRow4[i].x - mat, rowsH[4] - mat, shelfDepth));
        }
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow4[3].x + (verticalsPointsRow4[4].x - verticalsPointsRow4[3].x) / 2 - shelfMax / 2, verticalsPointsRow4[3].y + (rowsH[4] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow4[4].x - verticalsPointsRow4[3].x - mat, rowsH[4] - mat, shelfDepth));
        if ( verticalsPointsRow4.length == 7 )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow4[5].x + (verticalsPointsRow4[6].x - verticalsPointsRow4[5].x) / 2 - shelfMax / 2, verticalsPointsRow4[5].y + (rowsH[4] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow4[6].x - verticalsPointsRow4[5].x - mat, rowsH[4] - mat, shelfDepth));
        }
      }
    }
    if ( rows > 5 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[5] + mat / 2)
        {
          verticalsPointsRow5.push(point);
        }
      }
      if ( doubleVertClosed[1] == true )
      {
        for (  i = 0; i <= verticalsPointsRow5.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[i].x + (verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x) / 2 - shelfMax / 2, verticalsPointsRow5[i].y + (rowsH[5] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x - mat, rowsH[5] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[1] == false && doubleVertClosed[4] == true )
      {
        for (  i = 1; i <= verticalsPointsRow5.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[i].x + (verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x) / 2 - shelfMax / 2, verticalsPointsRow5[i].y + (rowsH[5] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x - mat, rowsH[5] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[4] == false && doubleVertClosed[5] == true ) {
        for (  i = 1; i <= 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[i].x + (verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x) / 2 - shelfMax / 2, verticalsPointsRow5[i].y + (rowsH[5] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x - mat, rowsH[5] - mat, shelfDepth));
        }
        if ( verticalsPointsRow5.length == 6 )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[4].x + (verticalsPointsRow5[5].x - verticalsPointsRow5[4].x) / 2 - shelfMax / 2, verticalsPointsRow5[4].y + (rowsH[5] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[5].x - verticalsPointsRow5[4].x - mat, rowsH[5] - mat, shelfDepth));
        }
        if ( verticalsPointsRow5.length == 7 )
        {
          for (  i = 4; i <= 5; i++ )
          {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[i].x + (verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x) / 2 - shelfMax / 2, verticalsPointsRow5[i].y + (rowsH[5] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x - mat, rowsH[5] - mat, shelfDepth));
          }
        }
      }
      if ( doubleVertClosed[5] == false )
      {
        for (  i = 1; i <= 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[i].x + (verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x) / 2 - shelfMax / 2, verticalsPointsRow5[i].y + (rowsH[5] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x - mat, rowsH[5] - mat, shelfDepth));
        }
        if ( verticalsPointsRow5.length >= 7 ) {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[4].x + (verticalsPointsRow5[5].x - verticalsPointsRow5[4].x) / 2 - shelfMax / 2, verticalsPointsRow5[4].y + (rowsH[5] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[5].x - verticalsPointsRow5[4].x - mat, rowsH[5] - mat, shelfDepth));
        }
        if ( verticalsPointsRow5.length == 8 ) {
          for (  i = 6; i <= 6; i++ ) {
            shadowMiddleLocation.push(new Point3d(verticalsPointsRow5[i].x + (verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x) / 2 - shelfMax / 2, verticalsPointsRow5[i].y + (rowsH[5] - mat) / 2, 0));
            shadowMiddleSize.push(new Vector3d(verticalsPointsRow5[i + 1].x - verticalsPointsRow5[i].x - mat, rowsH[5] - mat, shelfDepth));
          }
        }
      }
    }
    if ( rows > 6 )
    {
      for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
      {
        if ( parseInt(point.y) == rowsAbsolute[6] + mat / 2)
        {
          verticalsPointsRow6.push(point);
        }
      }
      if ( doubleVertClosed[1] == true )
      {
        for (  i = 0; i <= verticalsPointsRow6.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow6[i].x + (verticalsPointsRow6[i + 1].x - verticalsPointsRow6[i].x) / 2 - shelfMax / 2, verticalsPointsRow6[i].y + (rowsH[6] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow6[i + 1].x - verticalsPointsRow6[i].x - mat, rowsH[6] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[1] == false && doubleVertClosed[4] == true )
      {
        for (  i = 1; i <= verticalsPointsRow6.length - 2; i++ )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow6[i].x + (verticalsPointsRow6[i + 1].x - verticalsPointsRow6[i].x) / 2 - shelfMax / 2, verticalsPointsRow6[i].y + (rowsH[6] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow6[i + 1].x - verticalsPointsRow6[i].x - mat, rowsH[6] - mat, shelfDepth));
        }
      }
      if ( doubleVertClosed[4] == false )
      {
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow6[1].x + (verticalsPointsRow6[2].x - verticalsPointsRow6[1].x) / 2 - shelfMax / 2, verticalsPointsRow6[1].y + (rowsH[6] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow6[2].x - verticalsPointsRow6[1].x - mat, rowsH[6] - mat, shelfDepth));
        shadowMiddleLocation.push(new Point3d(verticalsPointsRow6[2].x + (verticalsPointsRow6[3].x - verticalsPointsRow6[2].x) / 2 - shelfMax / 2, verticalsPointsRow6[2].y + (rowsH[6] - mat) / 2, 0));
        shadowMiddleSize.push(new Vector3d(verticalsPointsRow6[3].x - verticalsPointsRow6[2].x - mat, rowsH[6] - mat, shelfDepth));
        if ( verticalsPointsRow6.length > 5 )
        {
          shadowMiddleLocation.push(new Point3d(verticalsPointsRow6[4].x + (verticalsPointsRow6[5].x - verticalsPointsRow6[4].x) / 2 - shelfMax / 2, verticalsPointsRow6[4].y + (rowsH[6] - mat) / 2, 0));
          shadowMiddleSize.push(new Vector3d(verticalsPointsRow6[5].x - verticalsPointsRow6[4].x - mat, rowsH[6] - mat, shelfDepth));
        }
      }
    }

    for (var i = 0, point; (point = verticalsPointsClear[i]); i++)
    {
      if ( parseInt(point.y) == rowsAbsolute[7] + mat / 2)
      {
        verticalsPointsRow7.push(point);
      }
    }
    for (  i = 0; i <= verticalsPointsRow7.length - 2; i++ )
    {
      shadowMiddleLocation.push(new Point3d(verticalsPointsRow7[i].x + (verticalsPointsRow7[i + 1].x - verticalsPointsRow7[i].x) / 2 - shelfMax / 2, verticalsPointsRow7[i].y + (rowsH[7] - mat) / 2, 0));
      shadowMiddleSize.push(new Vector3d(verticalsPointsRow7[i + 1].x - verticalsPointsRow7[i].x - mat, rowsH[7] - mat, shelfDepth));
    }
	return {
		ptHorizontalsAll: ptHorizontalsAll,
		ptVerticalsAll: ptVerticalsAll,
		ptSupportsAll: ptSupportsAll,
		shadowLeftLocation: shadowLeftLocation,
		shadowLeftSize: shadowLeftSize,
		shadowMiddleLocation: shadowMiddleLocation,
		shadowMiddleSize: shadowMiddleSize,
		shadowRightLocation: shadowRightLocation,
		shadowRightSize: shadowRightSize
	};
}};