module.exports = { 
	generateWalls: function (parameter,shelfWidth,rows,rowsH,shelfDepth) {

    var Point3d = THREE.Vector3;
    var Vector3d = THREE.Vector3;
    var shelfWidthSnapped = parseInt(shelfWidth / 10) * 10;
    var i = 0;
    var j = 0;
    var n = 0;
    var cellWidth = 0;
    var columnsNumber = 0;
    var rowAbsolute = [];
    rowAbsolute.push(0);
    for ( i = 0; i < rowsH.length; i++)
    {
      var res = 0;
      for( j = 0; j <= i; j++)
      {
        res += rowsH[j];
      }
      rowAbsolute.push(res);
    }

    var maxx = shelfWidthSnapped / 2;
    var minx = maxx * (-1);
    var gradientSupportWidth = 125;
    var mat = 18;
    var matHalf = mat / 2;
    var minSupportSpacing = gradientSupportWidth + 125 + mat;
    var supportsShift = 2;
    var div = 1;
    var verticalsStartNumber = 1;
    if (shelfWidthSnapped > 1020 && shelfWidthSnapped <= 1500)
    {
      div = 2;
      verticalsStartNumber = 1;
    }
    else if (shelfWidthSnapped > 1500 && shelfWidthSnapped <= 2020)
    {
      div = 3;
      verticalsStartNumber = 2;
    }
    else if (shelfWidthSnapped > 2020)
    {
      div = 4;
      verticalsStartNumber = 3;
    }
    var verticalsAdditional = 0;
    if (parameter < 33)
    {
      verticalsAdditional = 0;
    }
    else if (parameter < 67)
    {
      verticalsAdditional = 1;
    }
    else
    {
      if (div == 1)
      {
        verticalsAdditional = 1;
      }
      else
        verticalsAdditional = 2;
    }
    columnsNumber = verticalsStartNumber + verticalsAdditional;
    cellWidth = (maxx - minx - mat) / columnsNumber;
    var ptHorizontalLeft = [];
    var ptHorizontalRight = [];
    var ptHorizontalsAll = [];
    var ptLoc = new Point3d(-shelfWidthSnapped / 2, 0, 0);
    ptHorizontalLeft.push(ptLoc);
    var locy;
    for (i = 0; i < rows; i += 1)
    {
      locy = ptLoc.y + rowsH[i];
      ptLoc = new Point3d(-shelfWidthSnapped / 2, locy, 0);
      ptHorizontalLeft.push(ptLoc);
    }
    for (i = 0; i <= rows; i += 1)
    {
      ptLoc = new Point3d(shelfWidthSnapped / 2, ptHorizontalLeft[i].y, 0);
      ptHorizontalRight.push(ptLoc);
    }
    for ( i = 0; i < ptHorizontalLeft.length; i++)
    {
      ptHorizontalsAll.push(ptHorizontalLeft[i]);
      ptHorizontalsAll.push(ptHorizontalRight[i]);
    }

    var locationPoint = new Point3d();
    var ptVerticalsBottom = [];
    var ptVerticalsTop = [];
    var ptVerticalsAll = [];
    var ptVerticalsTopSideLeft = [];
    var ptVerticalsBottomSideLeft = [];
    var ptVerticalsTopSideRight = [];
    var ptVerticalsBottomSideRight = [];
    for( i = 0; i < rows; i++)
    {
      locationPoint = new Point3d(minx + matHalf, rowAbsolute[i] + matHalf, 0);
      ptVerticalsBottom.push(locationPoint);
      ptVerticalsBottomSideLeft.push(locationPoint);
      locationPoint = new Point3d(minx + matHalf, rowAbsolute[i + 1] - matHalf, 0);
      ptVerticalsTop.push(locationPoint);
      ptVerticalsTopSideLeft.push(locationPoint);
      if (columnsNumber > 0)
      {
        for( n = 1; n < columnsNumber; n++)
        {
          locationPoint = new Point3d(minx + matHalf + (n * cellWidth), rowAbsolute[i] + matHalf, 0);
          ptVerticalsBottom.push(locationPoint);
          locationPoint = new Point3d(minx + matHalf + (n * cellWidth), rowAbsolute[i + 1] - matHalf, 0);
          ptVerticalsTop.push(locationPoint);
        }
      }
      locationPoint = new Point3d(maxx - matHalf, rowAbsolute[i] + matHalf, 0);
      ptVerticalsBottom.push(locationPoint);
      ptVerticalsBottomSideRight.push(locationPoint);
      locationPoint = new Point3d(maxx - matHalf, rowAbsolute[i + 1] - matHalf, 0);
      ptVerticalsTop.push(locationPoint);
      ptVerticalsTopSideRight.push(locationPoint);
    }
    for ( j = 0; j < ptVerticalsBottom.length; j++)
    {
      ptVerticalsAll.push(ptVerticalsBottom[j]);
      ptVerticalsAll.push(ptVerticalsTop[j]);
    }

    var ptSupportsBottom = [];
    var ptSupportsTop = [];
    var ptSupportsAll = [];
    for( i = 0; i < rows; i++)
    {

      locationPoint = new Point3d(minx + mat, rowAbsolute[i] + matHalf, supportsShift);
      ptSupportsBottom.push(locationPoint);
      locationPoint = new Point3d(maxx - mat - gradientSupportWidth, rowAbsolute[i] + matHalf, supportsShift);
      ptSupportsBottom.push(locationPoint);

      locationPoint = new Point3d(minx + mat + gradientSupportWidth, rowAbsolute[i + 1] - matHalf, supportsShift);
      ptSupportsTop.push(locationPoint);
      locationPoint = new Point3d(maxx - mat, rowAbsolute[i + 1] - matHalf, supportsShift);
      ptSupportsTop.push(locationPoint);
    }
    for ( i = 0; i < ptSupportsBottom.length; i++)
    {
      ptSupportsAll.push(ptSupportsBottom[i]);
      ptSupportsAll.push(ptSupportsTop[i]);
    }
    var ptVerticalsBottom0 = [];
    var shadowMiddleLocation = [];
    var shadowMiddleSize = [];

    for ( i = 0; i < ptVerticalsBottom.length; i++)
    {
      if (ptVerticalsBottom[i].y == mat / 2)
      {
        ptVerticalsBottom0.push(ptVerticalsBottom[i]);
      }
    }
    for ( n = 0; n < rows; n++)
    {
      for ( i = 0; i < ptVerticalsBottom0.length - 1; i++)
      {
        shadowMiddleLocation.push(new Vector3d(ptVerticalsBottom0[i].x + (ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x) / 2, rowAbsolute[n] + (rowAbsolute[n + 1] - rowAbsolute[n]) / 2, shelfDepth / 2));
        shadowMiddleSize.push(new Vector3d(ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x - mat, rowAbsolute[n + 1] - rowAbsolute[n] - mat, shelfDepth));
      }
    }
	return {
		ptHorizontalsAll: ptHorizontalsAll,
		ptVerticalsAll: ptVerticalsAll,
		ptSupportsAll: ptSupportsAll,
		shadowMiddleLocation: shadowMiddleLocation,
		shadowMiddleSize: shadowMiddleSize
	};
}};