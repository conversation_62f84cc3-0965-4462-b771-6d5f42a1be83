{"auto_complete": {"selected_items": []}, "buffers": [], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "file_history": [], "find": {"height": 25.0}, "find_in_files": {"height": 0.0, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": ["\\n"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": true, "replace_history": ["|"], "reverse": false, "show_context": true, "use_buffer2": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": []}], "incremental_find": {"height": 25.0}, "input": {"height": 0.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.find_results": {"height": 0.0}, "pinned_build_system": "", "project": "grid2.sublime-project", "replace": {"height": 46.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 150.0, "status_bar_visible": true, "template_settings": {}}