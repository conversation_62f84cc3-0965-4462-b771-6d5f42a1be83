module.exports = { 
	generateWalls: function (parameter,shelfWidth,rows,rowsH,shelfDepth) {

		var Point3d = Vector3d = THREE.Vector3;
    var i = 0;
    shelfWidth = parseInt(shelfWidth * 0.1) * 10;
    var rowAbsolute = [];
    rowAbsolute.push(0);
    for (i = 0; i < rowsH.length; i++)
    {
      rowAbsolute.push(rowAbsolute[i] + rowsH[i]);
    }

    var snapParamSide = 5;
    var snapParamMiddle = 5;
    var parameterSnapped = parameter;

    if (parameter < (0 + snapParamSide))
    {
      parameterSnapped = 0;
    }
    if ((parameter > (50 - snapParamMiddle)) && (parameter < (50 + snapParamMiddle)))
    {
      parameterSnapped = 50;
    }
    if (parameter > (100 - snapParamSide))
    {
      parameterSnapped = 100;
    }
    var ptHorizontalsAll = [];
    for (i = 0; i <= rows; i++) {
      ptHorizontalsAll.push(new Point3d(-shelfWidth / 2, rowAbsolute[i], 0));
      ptHorizontalsAll.push(new Point3d(shelfWidth / 2, rowAbsolute[i], 0));
    }

    var maxX = shelfWidth / 2;
    var minX = shelfWidth / 2 * (-1);
    var gradientSupportsWidth = 125;
    var mat = 18;
    var matHalf = mat / 2;
    var minSupportsSpacing = gradientSupportsWidth + 125 + mat;
    var supportsShift = 2;
    var pos = 0;
    var leftDiv;
    var rightDiv;
    var minDist = 0;
    var ptVerticalsAll = [];
    var ptSupportsLeft = [];
    var ptSupportsRight = [];
    var rowRelative = [];
    for ( i = 0; i < rowsH.length; i++)
    {
      rowRelative.push(parseFloat(rowsH[i]));
    }
    var div0 = [];
    var div1 = [0.407843];
    var div2 = [0.229412, 0.5625];
    var div3 = [0.148784, 0.364807, 0.648546];
    var div4 = [0.095296, 0.233659, 0.415395, 0.640501];
    if(maxX - minX < 1100 - mat){
      minDist = 102;
      if((parameterSnapped * 0.01) * (maxX - minX) < (minSupportsSpacing + minDist / 2)){
        leftDiv = div0;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2)){
        leftDiv = div0;
        rightDiv = div0;
      } else {
        leftDiv = div1;
        rightDiv = div0;
      }
    } else if(maxX - minX < 1450 - mat){
      minDist = 102;
      if((parameterSnapped * 0.01) * (maxX - minX) < (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div0;
        rightDiv = div2;
      } else if(parameterSnapped * 0.01 < 0.44){
        leftDiv = div0;
        rightDiv = div1;
      } else if(parameterSnapped * 0.01 < 0.56){
        leftDiv = div1;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div1;
        rightDiv = div0;
      } else {
        leftDiv = div2;
        rightDiv = div0;
      }
    } else if (maxX - minX < 2200 - mat){
      minDist = 126;
      if(parameterSnapped * 0.01 * (maxX - minX) < (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div0;
        rightDiv = div3;
      } else if(parameterSnapped * 0.01 < 0.4){
        leftDiv = div0;
        rightDiv = div2;
      } else if(parameterSnapped * 0.01 < 0.6){
        leftDiv = div1;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div2;
        rightDiv = div0;
      } else {
        leftDiv = div3;
        rightDiv = div0;
      }
    } else {
      minDist = 150;
      if(parameterSnapped * 0.01 * (maxX - minX) < (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div0;
        rightDiv = div4;
      } else if(parameterSnapped * 0.01 < 0.25){
        leftDiv = div0;
        rightDiv = div3;
      } else if(parameterSnapped * 0.01 < 0.42){
        leftDiv = div1;
        rightDiv = div3;
      } else if(parameterSnapped * 0.01 < 0.58){
        leftDiv = div2;
        rightDiv = div2;
      }  else if(parameterSnapped * 0.01 < 0.75){
        leftDiv = div3;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxX - minX) > (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div3;
        rightDiv = div0;
      } else {
        leftDiv = div4;
        rightDiv = div0;
      }
    }
    var minDistToPlaceSupports = parseInt(minDist * 1.4 + mat);

    pos = parseInt(minX + ((parameterSnapped * 0.01) * (maxX - minX)));
    if(pos < minX + (minSupportsSpacing + minDist / 2)) {
      pos = minX + minDist / 2 + matHalf;
    }
    else if (pos > maxX - (minSupportsSpacing + minDist / 2)) {
      pos = maxX - (minDist / 2) - matHalf;
    }
    var currentBottomVert;
    var currentTopVert;
    var prevBottomVert;
    var prevTopVert;
    for( i = 0; i < rows; i++)
    {
      if(pos - minDist / 2 > minX + minDist)
      {
        ptVerticalsAll.push(new Point3d(minX + matHalf, rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(minX + matHalf, rowAbsolute[i + 1] - matHalf, 0));
      }
      for(var d = leftDiv.length - 1; d >= 0; d--)
      {
        ptVerticalsAll.push(new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minX) * leftDiv[d], rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minX) * leftDiv[d], rowAbsolute[i + 1] - matHalf, 0));
        if((d == 0 && leftDiv.length >= 2) || (d == 2 && leftDiv.length >= 4))
        {
          currentBottomVert = ptVerticalsAll.length - 2;
          currentTopVert = currentBottomVert + 1;
          prevBottomVert = currentBottomVert - 2;
          if(Math.abs(ptVerticalsAll[prevBottomVert].x - ptVerticalsAll[currentBottomVert].x) > minDistToPlaceSupports)
          {
            ptSupportsLeft.push(new Point3d ((ptVerticalsAll[currentBottomVert].x - matHalf) - gradientSupportsWidth, ptVerticalsAll[currentBottomVert].y, supportsShift));
            ptSupportsLeft.push(new Point3d (ptVerticalsAll[currentTopVert].x - matHalf, ptVerticalsAll[currentTopVert].y, supportsShift));
          }
        }
      }
      ptVerticalsAll.push(new Point3d(pos - minDist / 2, rowAbsolute[i] + matHalf, 0));
      ptVerticalsAll.push(new Point3d(pos - minDist / 2, rowAbsolute[i + 1] - matHalf, 0));
      ptVerticalsAll.push(new Point3d(pos + minDist / 2, rowAbsolute[i] + matHalf, 0));
      ptVerticalsAll.push(new Point3d(pos + minDist / 2, rowAbsolute[i + 1] - matHalf, 0));
      for(var d = 0; d < rightDiv.length; d++)
      {
        ptVerticalsAll.push(new Point3d(((maxX - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(((maxX - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i + 1] - matHalf, 0));
        if((d == 1 && rightDiv.length >= 2) || (d == 3 && rightDiv.length >= 4))
        {
          currentBottomVert = ptVerticalsAll.length - 2;
          prevBottomVert = currentBottomVert - 2;
          prevTopVert = ptVerticalsAll.length - 3;
          if(Math.abs(ptVerticalsAll[prevBottomVert].x - ptVerticalsAll[currentBottomVert].x) > minDistToPlaceSupports)
          {
            ptSupportsRight.push(new Point3d ((ptVerticalsAll[prevBottomVert].x + matHalf), ptVerticalsAll[prevBottomVert].y, supportsShift));
            ptSupportsRight.push(new Point3d (ptVerticalsAll[prevTopVert].x + matHalf + gradientSupportsWidth, ptVerticalsAll[prevTopVert].y, supportsShift));
          }
        }
      }
      if(maxX - pos > minDist)
      {
        ptVerticalsAll.push(new Point3d(maxX - matHalf, rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(maxX - matHalf, rowAbsolute[i + 1] - matHalf, 0));
      }
      if(pos > minX + (minDist * 2 + mat) && (parameterSnapped * 0.01 > 0.49 || maxX - minX >= 90))
      {
        ptSupportsLeft.push(new Point3d (minX + mat, rowAbsolute[i] + matHalf, supportsShift));
        ptSupportsLeft.push(new Point3d (minX + mat + gradientSupportsWidth, rowAbsolute[i + 1] - matHalf, supportsShift));
      }
      if(pos < maxX - (minDist * 2 + mat) && (parameterSnapped * 0.01 < 0.51 || maxX - minX >= 90))
      {
        ptSupportsRight.push(new Point3d (maxX - mat - gradientSupportsWidth, rowAbsolute[i] + matHalf, supportsShift));
        ptSupportsRight.push(new Point3d (maxX - mat, rowAbsolute[i + 1] - matHalf, supportsShift));
      }
    }
    var ptVerticalsBottom0 = [];
    var shadowMiddleLocation = [];
    var shadowMiddleSize = [];

    for ( i = 0; i < ptVerticalsAll.length; i++)
    {
      if (ptVerticalsAll[i].y == mat / 2) {
        ptVerticalsBottom0.push(ptVerticalsAll[i]);
      }
    }
    for ( var n = 0; n < rows; n++)
    {
      for ( i = 0; i < ptVerticalsBottom0.length - 1; i++)
      {
        shadowMiddleLocation.push(new Vector3d(ptVerticalsBottom0[i].x + (ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x) / 2, rowAbsolute[n] + (rowAbsolute[n + 1] - rowAbsolute[n]) / 2, shelfDepth / 2));
        shadowMiddleSize.push(new Vector3d(ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x - mat, rowAbsolute[n + 1] - rowAbsolute[n] - mat, shelfDepth));
      }
    }
    var legsOffset = 20;
    var legsCount = 0;
    var ptLegsTemp = new Point3d(0, 0, 0);
    if ((parseInt(Math.abs(ptHorizontalsAll[1].x - ptHorizontalsAll[0].x))) >= 1500)
      legsCount = 4;
    else
      legsCount = 3;
    var ptLegs = [];
    for (i = 0; i < legsCount; i++){
      ptLegsTemp = new Point3d(((shelfWidth - legsOffset * 2) / (legsCount - 1)) * i, 0, legsOffset);
      ptLegs.push(ptLegsTemp);
      ptLegsTemp = new Point3d(((shelfWidth - legsOffset * 2) / (legsCount - 1)) * i, 0, shelfDepth - legsOffset);
      ptLegs.push(ptLegsTemp);
    }
    for (i = 0; i < ptLegs.length; i++){
      ptLegs[i] = new Point3d(ptLegs[i].x - shelfWidth / 2 + legsOffset, ptLegs[i].y, ptLegs[i].z);
    }
	return {
		ptHorizontalsAll: ptHorizontalsAll,
		ptVerticalsAll: ptVerticalsAll,
		ptSupportsLeft: ptSupportsLeft,
		ptSupportsRight: ptSupportsRight,
		shadowMiddleLocation: shadowMiddleLocation,
		shadowMiddleSize: shadowMiddleSize,
        ptLegs: ptLegs
	};
}};