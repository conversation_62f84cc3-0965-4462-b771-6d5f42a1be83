module.exports =  {
            generateWalls: function(parameter, shelfWidth, rowsNum, rowsH, shelfDepth) {

                  /////////////////// INPUTS //////////////////////////////
                    // int shelftDepth = glebokosc polki
                    // int shelfWidth = szerokosc polki
                    // int rowsNum = ilosc rzedow
                    // var rowsH = wykosci rzedow
                    // int parameter = parametr odkształcenia (0-100)

                    var Point3d = Vector3d = THREE.Vector3;

                    // KONWERTOWANIE WEJŚĆ
                    var shelfWidthSnapped = parseInt(shelfWidth / 10) * 10;

                    // ZMIENNE POMOCNICZE
                    var i = 0;
                    var j = 0;
                    var n = 0;

                    // cell shelfWidthSnapped (initial)
                    var cellWidth = 0;

                    // ilosc elementow pionowych (initial)
                    var columnsNumber = 0;

                    // ROW ABSOLUTE
                    var rowAbsolute = [];
                    rowAbsolute.push(0);
                    for ( i = 0; i < rowsH.length; i++)
                    {
                      var res = 0;
                      for( j = 0; j <= i; j++)
                      {
                        res += rowsH[j];
                      }
                      rowAbsolute.push(res);
                    }

                    //////////////// WYMIARY /////////////////

                    // szerokosc szafki w prawo
                    var maxX = shelfWidthSnapped / 2;

                    // punkt zero
                    var minX = maxX * (-1);

                    // szerokosc plecow
                    var gradientSupportsWidth = 125;

                    // szerokosc materialu
                    var mat = 18;
                    var matHalf = mat / 2;

                    // min Supports spacing
                    var minSupportsSpacing = gradientSupportsWidth + 125 + mat;

                    // Supportss - shift to front [cm]
                    var SupportssShift = 2;


                    /////////////////// WARUNKI /////////////////
                    // BREAKPOvarS

                    var div = 1; // initial
                    var verticalsStartNumber = 1;

                    if (shelfWidthSnapped > 1020 && shelfWidthSnapped <= 1500)
                    {
                      div = 2;
                      verticalsStartNumber = 1;
                    }
                    else if (shelfWidthSnapped > 1500 && shelfWidthSnapped <= 2020)
                    {
                      div = 3;
                      verticalsStartNumber = 2;
                    }
                    else if (shelfWidthSnapped > 2020)
                    {
                      div = 4;
                      verticalsStartNumber = 3;
                    }

                    // dodatkowe verticale (dodawane przez parameter)
                    var verticalspushitional = 0;
                    // wyznaczanie breakpointów dla verticalspushitional
                    if (parameter < 33)
                    {
                      verticalspushitional = 0;
                    }
                    else if (parameter < 67)
                    {
                      verticalspushitional = 1;
                    }
                    else
                    {
                      if (div == 1)
                      {
                        verticalspushitional = 1;
                      }
                      else
                        verticalspushitional = 2;
                    }

                    // columnsNumber definition
                    columnsNumber = verticalsStartNumber + verticalspushitional;

                    // nadanie wartoscie zmiennej cellWidth
                    cellWidth = (maxX - minX - mat) / columnsNumber;


                    ////////////////////////////// HorizontalsS //////////////////////////////

                    var ptHorizontalsLeft = [];
                    var ptHorizontalsRight = [];
                    var ptHorizontalsAll = [];
                    var ptLoc = new Point3d(-shelfWidthSnapped / 2, 0, 0);

                    // pierwszy punkt, dolny rzad lewa strona, jako (-shelfWidthSnapped/2,0,0)
                    ptHorizontalsLeft.push(ptLoc);

                    // pozostale rzedy lewa strona
                    var locY;

                    for (i = 0; i < rowsNum; i += 1)
                    {
                      locY = ptLoc.y + rowsH[i];
                      ptLoc = new Point3d(-shelfWidthSnapped / 2, locY, 0);
                      ptHorizontalsLeft.push(ptLoc);
                    }

                    //wszystkie rzedy prawa strona
                    for (i = 0; i <= rowsNum; i += 1)
                    {
                      ptLoc = new Point3d(shelfWidthSnapped / 2, ptHorizontalsLeft[i].y, 0);
                      ptHorizontalsRight.push(ptLoc);
                    }

                    // HorizontalsS OUTPUT WEAVED LIST - left, right, left, right...
                    for ( i = 0; i < ptHorizontalsLeft.length; i++)
                    {
                      ptHorizontalsAll.push(ptHorizontalsLeft[i]);
                      ptHorizontalsAll.push(ptHorizontalsRight[i]);
                    }

                    ////////////////////////////////// VERICALS ///////////////////////////////////

                    // VERTICALS - lists
                    var locationPoint = new Point3d();
                    // listy docelowe
                    var ptVerticalsBottom = [];
                    var ptVerticalsTop = [];
                    var ptVerticalsAll = [];
                    // punkty do wstawiania Supportsow
                    var ptVerticalsTopSideLeft = [];
                    var ptVerticalsBottomSideLeft = [];
                    var ptVerticalsTopSideRight = [];
                    var ptVerticalsBottomSideRight = [];


                    for( i = 0; i < rowsNum; i++)
                    {
                      // skrajny lewy vertical
                      locationPoint = new Point3d(minX + matHalf, rowAbsolute[i] + matHalf, 0);
                      ptVerticalsBottom.push(locationPoint);
                      ptVerticalsBottomSideLeft.push(locationPoint);

                      locationPoint = new Point3d(minX + matHalf, rowAbsolute[i + 1] - matHalf, 0);
                      ptVerticalsTop.push(locationPoint);
                      ptVerticalsTopSideLeft.push(locationPoint);

                      // internal verticals - tylko jeśli są (columnsNumber > 0), jeśli nie, skip this part
                      if (columnsNumber > 0)
                      {
                        for( n = 1; n < columnsNumber; n++)
                        {
                          locationPoint = new Point3d(minX + matHalf + (n * cellWidth), rowAbsolute[i] + matHalf, 0);
                          ptVerticalsBottom.push(locationPoint);

                          locationPoint = new Point3d(minX + matHalf + (n * cellWidth), rowAbsolute[i + 1] - matHalf, 0);
                          ptVerticalsTop.push(locationPoint);
                        }
                      }
                      // skrajny prawy vertical
                      locationPoint = new Point3d(maxX - matHalf, rowAbsolute[i] + matHalf, 0);
                      ptVerticalsBottom.push(locationPoint);
                      ptVerticalsBottomSideRight.push(locationPoint);

                      locationPoint = new Point3d(maxX - matHalf, rowAbsolute[i + 1] - matHalf, 0);
                      ptVerticalsTop.push(locationPoint);
                      ptVerticalsTopSideRight.push(locationPoint);
                    }

                    // VERTICALS - OUTPUT WEAVED LIST - punkty bottom, top, bottom, top, bottom, top...

                    for ( j = 0; j < ptVerticalsBottom.length; j++)
                    {
                      ptVerticalsAll.push(ptVerticalsBottom[j]);
                      ptVerticalsAll.push(ptVerticalsTop[j]);
                    }

                    ////////////////////////////////////// SupportsS ////////////////////////////////////

                    // SupportsS - lists
                    var ptSupportsBottom = [];
                    var ptSupportsTop = [];
                    var ptSupportsAll = [];

                    //SupportsS - lokalizacja tylko przy skrajnych lewych i prawych vericals
                    for( i = 0; i < rowsNum; i++)
                    {
                      // BOTTOM Supports POINTS
                      // left Supportss
                      locationPoint = new Point3d(minX + mat, rowAbsolute[i] + matHalf, SupportssShift);
                      ptSupportsBottom.push(locationPoint);
                      // right Supportss
                      locationPoint = new Point3d(maxX - mat - gradientSupportsWidth, rowAbsolute[i] + matHalf, SupportssShift);
                      ptSupportsBottom.push(locationPoint);

                      //TOP Supports points
                      // left Supportss
                      locationPoint = new Point3d(minX + mat + gradientSupportsWidth, rowAbsolute[i + 1] - matHalf, SupportssShift);
                      ptSupportsTop.push(locationPoint);
                      // right Supportss
                      locationPoint = new Point3d(maxX - mat, rowAbsolute[i + 1] - matHalf, SupportssShift);
                      ptSupportsTop.push(locationPoint);
                    }

                    // SupportsS OUTPUT WEAVED LIST - punkty bottom, top, bottom, top, bottom, top...
                    for ( i = 0; i < ptSupportsBottom.length; i++)
                    {
                      ptSupportsAll.push(ptSupportsBottom[i]);
                      ptSupportsAll.push(ptSupportsTop[i]);
                    }

                    ////////////////////// SHADOWS ////////////////////////////

                    var ptVerticalsBottom0 = []; // spody verticals na poziomie 0
                    var shadowLocation = []; // srodki cieni
                    var shadowSize = []; // wielkosc cieni

                    // VERTICALS - bottom - pierwszy rzad [0]
                    for ( i = 0; i < ptVerticalsBottom.length; i++)
                    {
                      if (ptVerticalsBottom[i].y == mat / 2)
                      {
                        ptVerticalsBottom0.push(ptVerticalsBottom[i]);
                      }
                    }
                    // SHADOWS - location & size
                    for ( n = 0; n < rowsNum; n++)
                    {
                      for ( i = 0; i < ptVerticalsBottom0.length - 1; i++)
                      {
                        shadowLocation.push(new Vector3d(ptVerticalsBottom0[i].x + (ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x) / 2, rowAbsolute[n] + (rowAbsolute[n + 1] - rowAbsolute[n]) / 2, shelfDepth / 2));
                        shadowSize.push(new Vector3d(ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x - mat, rowAbsolute[n + 1] - rowAbsolute[n] - mat, shelfDepth));
                      }
                    }

                    return {
                      ptHorizontalsAll: ptHorizontalsAll,
                      ptVerticalsAll: ptVerticalsAll,
                      ptSupportsAll: ptSupportsAll,
                      shadowCenters: shadowLocation,
                      shadowSizes: shadowSize
                    }

                    // var ptHorizontalssAll - weaved ends of Horizontalss (left[0], right[0], left[1], right[1], ...)
                    // var ptVerticalsAll - weaved ends of verticals (bottom[0], top[0], bottom[1], top[1], ...)
                    // var ptSupportsAll - weaved ends of verticals (bottom[0], top[0], bottom[1], top[1], ...)
                    // var shadowLocation - location of shadows boxes
                    // var shadowSize - sizes of shadows boxes</item>
                }

}