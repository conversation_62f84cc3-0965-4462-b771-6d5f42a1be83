module.exports =         {
 generateWalls: function(property, shelfWidth, rowsNum, rowsH, shelfDepth) {

 /////////////////// INPUTS //////////////////////////////
    // int shelftDepth = glebokosc polki
    // int shelfWidth = szerokosc polki
    // int rowsNum = ilosc rzedow
    // var rowsH = wykosci rzedow
    // int parameter = parametr odkształcenia (0-100)
    var Point3d = Vector3d = THREE.Vector3;

    // definiowanie zmiennych
    var i = 0;
    var j = 0;
    // grubość materiału
    var mat = 18;
    // szerokość pleców (netto, w widoku, bez 8mm schowanych)
    var backWidth = 125;

    var minX = shelfWidth / 2 * (-1);
    var maxX = shelfWidth / 2;

    var locY;

    // parametry snapowania kluczowych pozycji
    var snapParamSide = 5;
    var snapParamMiddle = 5;

    // listy do rysowania horizontals
    var ptHorizontalsLeft = [];
    var ptHorizontalsRight = [];
    var ptHorizontalsAll = [];

    var ptVerticalsBottomLeft = [];
    var ptVerticalsBottomRight = [];
    var ptVerticalsAll = [];

    // listy do rysowania cieni
    // left
    var shadowLeftLocation = [];
    var shadowLeftSize = [];
    // middle
    var shadowMiddleLocation = [];
    var shadowMiddleSize = [];
    // right
    var shadowRightLocation = [];
    var shadowRightSize = [];

    // przeliczenie szerokości na podzielną przez 10
    shelfWidth = parseInt(shelfWidth * 0.1) * 10;

    // snapping parametru dla strategicznych wartosci
    // dla 0
    if (property < (0 + snapParamSide))
    {
      property = 0;
    }
    // dla 50
    if ((property > (50 - snapParamMiddle)) && (property < (50 + snapParamMiddle)))
    {
      property = 50;
    }
    // dla 100
    if (property > (100 - snapParamSide))
    {
      property = 100;
    }

    ////////////////////////////////////////// HORIZONTALS //////////////////////////////////////

    // pierwszy punkt, dolny rzad lewa strona, jako (shelfWidth / 2 * (-1),0,0)
    var ptLoc = new Point3d(shelfWidth / 2 * (-1), 0, 0);
    ptHorizontalsLeft.push(ptLoc);

    // pozostale rzedy lewa strona
    for (i = 0; i < rowsNum; i += 1)
    {
      locY = ptLoc.y + rowsH[i];
      ptLoc = new Point3d(shelfWidth / 2 * (-1), locY, 0);
      ptHorizontalsLeft.push(ptLoc);
    }

    // punkty koncowe praca strona
    for (i = 0; i <= rowsNum; i += 1)
    {
      ptHorizontalsRight.push(new Point3d(shelfWidth / 2, ptHorizontalsLeft[i].y, 0));
    }

    // ptHorizontalsAll - weave list
    for (i = 0; i < ptHorizontalsLeft.length; i++)
    {
      ptHorizontalsAll.push(ptHorizontalsLeft[i]);
      ptHorizontalsAll.push(ptHorizontalsRight[i]);
    }

    /////////////////////////////////////////////  VERTICALS & SHADOWS ///////////////////////////////////

    //1// okreslenie ilosci podzialow w zaleznosci od shelfWidth

    var div;

    if (shelfWidth <= 1020)
      div = 1;
    else if (shelfWidth > 1020 & shelfWidth <= 1500)
      div = 2;
    else if (shelfWidth > 1500 & shelfWidth <= 2020)
      div = 3;
    else
      div = 4;

    //2// boczne wciecia na poczatku

    var indent;

    if (div == 1)
      indent = 150;
    else if (div == 2)
      indent = 170;
    else
      indent = 180;

    //3// wyznaczanie przesuniecia w lewo lub prawo dla kazdego rzedu w zaleznosci od property

    var modWidth;
    modWidth = parseInt((ptHorizontalsRight[0].x - indent - (ptHorizontalsLeft[0].x + indent)) / div); //aktualna szerokosc modulu

    var maxMove; // maksymalne przesuniecie linii poziomych wzgledem property
    var move; // aktualne przesuniecie

    if (div == 1)
      maxMove = modWidth / 6;
    else
      maxMove = modWidth / 4;

    move = (property * maxMove) / 100;

    //4// rysowanie punktow
    var ptVerticalsBottom = [];//TEST ARRAY
    var ptVerticalsTop = [];//TEST ARRAY
    var locPt = new Point3d();

    for (i = 0; i <= ptHorizontalsLeft.length - 2; i++)
    { //co drugi rząd factor jest ujemny, wpływ na przesunięcie pionów w jedną lub w drugą
      var factor;
      if (i % 2 == 0)
        factor = 1;
      else
        factor = -1;

      // VERTICALS - left - bottom
      locPt = new Point3d(parseInt(ptHorizontalsLeft[i].x + indent + move * factor), parseInt(ptHorizontalsLeft[i].y + mat / 2), 0);
      ptVerticalsBottom.push(locPt);
      // SHADOWS LEFT
      shadowLeftLocation.push(new Point3d(locPt.x - (indent + move * factor) / 2, locPt.y + (rowsH[i] - mat) / 2, shelfDepth / 2));
      shadowLeftSize.push(new Point3d(indent + move * factor - mat, rowsH[i] - mat, shelfDepth));
      // VERTICALS - left - top
      ptVerticalsTop.push(new Point3d(locPt.x, locPt.y + rowsH[i] - mat, 0));

      //punkty po środku
      for (j = 1; j <= div - 1; j += 1)
      {
        //print(ptHorizontalsLeft(i).x + indent + modWidth * j)
        locPt = new Point3d(parseInt(ptHorizontalsLeft[i].x + indent + modWidth * j + move * factor), parseInt(ptHorizontalsLeft[i].y + mat / 2), 0);
        ptVerticalsBottom.push(locPt);
        ptVerticalsTop.push(new Point3d(locPt.x, locPt.y + rowsH[i] - mat, 0));
      }

      // VERTICALS - right - bottom
      locPt = new Point3d(parseInt(ptHorizontalsRight[i].x - indent + move * factor), parseInt(ptHorizontalsRight[i].y + mat / 2), 0);
      ptVerticalsBottom.push(locPt);
      // SHADOWS RIGHT
      shadowRightLocation.push(new Point3d(locPt.x + (indent - move * factor) / 2, locPt.y + (rowsH[i] - mat) / 2, shelfDepth / 2));
      shadowRightSize.push(new Point3d(indent - move * factor - mat, rowsH[i] - mat, shelfDepth));
      // VERTICALS - right - top
      ptVerticalsTop.push(new Point3d(locPt.x, locPt.y + rowsH[i] - mat, 0));

      // SHADOWS - middle
      for (j = 0; j <= div - 1; j += 1)
      {
        // SHADOWS - location
        shadowMiddleLocation.push(new Point3d(ptHorizontalsLeft[i].x + indent + modWidth * j + move * factor + (modWidth / 2), ptHorizontalsLeft[i].y + rowsH[i] / 2, shelfDepth / 2));
        // SHADOWS - size
        shadowMiddleSize.push(new Point3d(modWidth - mat, rowsH[i] - mat, shelfDepth));
      }
    }

    // ptVerticalsAll - weave list
    for (i = 0; i < ptVerticalsBottom.length; i++)
    {
      ptVerticalsAll.push(ptVerticalsBottom[i]);
      ptVerticalsAll.push(ptVerticalsTop[i]);
    }

    //SUPPORTS - START
    //definicja, gdzie sie znajduja
    var locout;
    var sideout;
    var ptSupportsBottom = [];
    var ptSupportsTop = [];

    var loc1 = [1,0,1,0,1,0,1,0];
    var side1 = [-1,1,-1,1,-1,1,-1,1];

    var loc2 = [0,2,0,1,2,0,2,1];
    var side2 = [1,-1,1,1,-1,1,-1,-1];

    var loc3 = [0,3,1,3,1,0,2,1];
    var side3 = [1,-1,-1,-1,1,1,1,-1];

    var loc4 = [2,3,0,2,3,0,2,4];
    var side4 = [-1,1,1,1,1,1,1,-1];


    if (div == 1)
    {
      locout = loc1;
      sideout = side1;
    }
    else if (div == 2)
    {
      locout = loc2;
      sideout = side2;
    }
    else if (div == 3)
    {
      locout = loc3;
      sideout = side3;
    }
    else
    {
      locout = loc4;
      sideout = side4;
    }

    for (i = 0; i <= ptHorizontalsLeft.length - 2; i += 1)
    {
      var locadd = i * (div + 1); //przeliczanie tablicy na rzędy i kolumny tak aby znaleźć odpowiedni pion
      var hadd = parseInt(ptHorizontalsLeft[i + 1].y - ptHorizontalsLeft[i].y); //dodawanie wysokości rzędów
      var loci = locout[i] + locadd;

      if (sideout[i] == 1)
      {
        ptSupportsBottom.push(new Point3d(ptVerticalsBottom[loci].x + mat / 2, ptVerticalsBottom[loci].y, ptVerticalsBottom[loci].Z));
        ptSupportsTop.push(new Point3d(ptVerticalsBottom[loci].x + mat / 2 + backWidth, ptVerticalsBottom[loci].y + hadd - mat, ptVerticalsBottom[loci].Z));
      }
      else
      {
        ptSupportsBottom.push(new Point3d(ptVerticalsBottom[loci].x - mat / 2, ptVerticalsBottom[loci].y, ptVerticalsBottom[loci].Z));
        ptSupportsTop.push(new Point3d(ptVerticalsBottom[loci].x - mat / 2 - backWidth, ptVerticalsBottom[loci].y + hadd - mat, ptVerticalsBottom[loci].Z));
      }
    }

    // SUPPORTS - weave list
    var ptSupportsAll = [];
    for (i = 0; i < ptSupportsTop.length; i++)
    {
      ptSupportsAll.push(ptSupportsBottom[i]);
      ptSupportsAll.push(ptSupportsTop[i]);
    }

    /////////////////////// OUTPUTS ///////////////////////

    // var ptHorizontalsAll - weaved ends of horizontals (left[0], right[0], left[1], right[1], ...)
    // var ptVerticalsAll - weaved ends of verticals (bottom[0], top[0], bottom[1], top[1], ...)
    // var ptSupportsAll - weaved ends of verticals (bottom[0], top[0], bottom[1], top[1], ...)

    // var shadowLeftLocation - location of shadows boxes (open, left-hand side)
    // var shadowLeftSize - sizes of shadows boxes (open, left-hand side)

    // var shadowMiddleLocation - location of shadows boxes (closed, middle)
    // var shadowMiddleSize - sizes of shadows boxes (closed, middle)

    // var shadowRightLocation - location of shadows boxes (open, right-hand side)
    // var shadowRightSize - sizes of shadows boxes (open, right-hand side)</item>

    return {
      ptHorizontalsAll: ptHorizontalsAll,
      ptVerticalsAll: ptVerticalsAll,
      ptSupportsAll: ptSupportsAll,

      shadowLeftCenters: shadowLeftLocation,
      shadowLeftSizes: shadowLeftSize,
      shadowCenters: shadowMiddleLocation,
      shadowSizes: shadowMiddleSize,

      shadowRightCenters: shadowRightLocation,
      shadowRightSizes: shadowRightSize
    }
  }

}