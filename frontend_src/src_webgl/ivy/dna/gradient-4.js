module.exports = { 
	generateWalls: function (parameter,shelfWidth,rows,rowsH,shelfDepth) {

		var Point3d = Vector3d = THREE.Vector3;
    var i = 0;
    shelfWidth = parseInt(shelfWidth * 0.1) * 10;
    var rowAbsolute = [];
    rowAbsolute.push(0);
    for (i = 0; i < rowsH.length; i++)
    {
      rowAbsolute.push(rowAbsolute[i] + rowsH[i]);
    }

    var snapParamSide = 5;
    var snapParamMiddle = 5;
    var parameterSnapped = parameter;

    if (parameter < (0 + snapParamSide))
    {
      parameterSnapped = 0;
    }
    if ((parameter > (50 - snapParamMiddle)) && (parameter < (50 + snapParamMiddle)))
    {
      parameterSnapped = 50;
    }
    if (parameter > (100 - snapParamSide))
    {
      parameterSnapped = 100;
    }
    var ptHorizontalsAll = [];
    for (i = 0; i <= rows; i++) {
      ptHorizontalsAll.push(new Point3d(-shelfWidth / 2, rowAbsolute[i], 0));
      ptHorizontalsAll.push(new Point3d(shelfWidth / 2, rowAbsolute[i], 0));
    }

    var maxx = shelfWidth / 2;
    var minx = shelfWidth / 2 * (-1);
    var gradientSupportsWidth = 125;
    var mat = 18;
    var matHalf = mat / 2;
    var minSupportsSpacing = gradientSupportsWidth + 125 + mat;
    var supportsShift = 2;
    var pos = 0;
    var leftDiv;
    var rightDiv;
    var minDist = 0;
    var locationPoint = new Point3d();
    var ptVerticalsAll = [];
    var ptSupportsAll = [];
    var rowRelative = [];
    for ( i = 0; i < rowsH.length; i++)
    {
      rowRelative.push(parseFloat(rowsH[i]));
    }
    var div0 = [];
    var div1 = [0.407843];
    var div2 = [0.229412, 0.5625];
    var div3 = [0.148784, 0.364807, 0.648546];
    var div4 = [0.095296, 0.233659, 0.415395, 0.640501];
    if(maxx - minx < 1100 - mat){
      minDist = 102;
      if((parameterSnapped * 0.01) * (maxx - minx) < (minSupportsSpacing + minDist / 2)){
        leftDiv = div0;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxx - minx) > (minSupportsSpacing + minDist / 2)){
        leftDiv = div0;
        rightDiv = div0;
      } else {
        leftDiv = div1;
        rightDiv = div0;
      }
    } else if(maxx - minx < 1450 - mat){
      minDist = 102;
      if((parameterSnapped * 0.01) * (maxx - minx) < (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div0;
        rightDiv = div2;
      } else if(parameterSnapped * 0.01 < 0.44){
        leftDiv = div0;
        rightDiv = div1;
      } else if(parameterSnapped * 0.01 < 0.56){
        leftDiv = div1;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxx - minx) > (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div1;
        rightDiv = div0;
      } else {
        leftDiv = div2;
        rightDiv = div0;
      }
    } else if (maxx - minx < 2200 - mat){
      minDist = 126;
      if(parameterSnapped * 0.01 * (maxx - minx) < (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div0;
        rightDiv = div3;
      } else if(parameterSnapped * 0.01 < 0.4){
        leftDiv = div0;
        rightDiv = div2;
      } else if(parameterSnapped * 0.01 < 0.6){
        leftDiv = div1;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxx - minx) > (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div2;
        rightDiv = div0;
      } else {
        leftDiv = div3;
        rightDiv = div0;
      }
    } else {
      minDist = 150;
      if(parameterSnapped * 0.01 * (maxx - minx) < (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div0;
        rightDiv = div4;
      } else if(parameterSnapped * 0.01 < 0.25){
        leftDiv = div0;
        rightDiv = div3;
      } else if(parameterSnapped * 0.01 < 0.42){
        leftDiv = div1;
        rightDiv = div3;
      } else if(parameterSnapped * 0.01 < 0.58){
        leftDiv = div2;
        rightDiv = div2;
      }  else if(parameterSnapped * 0.01 < 0.75){
        leftDiv = div3;
        rightDiv = div1;
      } else if (((100 - parameterSnapped) * 0.01) * (maxx - minx) > (minSupportsSpacing + minDist / 2.0)){
        leftDiv = div3;
        rightDiv = div0;
      } else {
        leftDiv = div4;
        rightDiv = div0;
      }
    }
    var minDistToPlaceSupports = parseInt(minDist * 1.4 + mat);

    pos = parseInt(minx + ((parameterSnapped * 0.01) * (maxx - minx)));
    if(pos < minx + (minSupportsSpacing + minDist / 2)) {
      pos = minx + minDist / 2 + matHalf;
    }
    else if (pos > maxx - (minSupportsSpacing + minDist / 2)) {
      pos = maxx - (minDist / 2) - matHalf;
    }
    var currentBottomVert;
    var currentTopVert;
    var prevBottomVert;
    var prevTopVert;
    for( i = 0; i < rows; i++)
    {
      if(pos - minDist / 2 > minx + minDist)
      {
        ptVerticalsAll.push(new Point3d(minx + matHalf, rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(minx + matHalf, rowAbsolute[i + 1] - matHalf, 0));
      }
      for(var d = leftDiv.length - 1; d >= 0; d--)
      {
        ptVerticalsAll.push(new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minx) * leftDiv[d], rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(pos - minDist / 2 - ((pos - minDist / 2) - minx) * leftDiv[d], rowAbsolute[i + 1] - matHalf, 0));
        if((d == (leftDiv.length - 2) && leftDiv.length >= 2) || (d == (leftDiv.length - 4) && leftDiv.length >= 4))
        {
          currentBottomVert = ptVerticalsAll.length - 2;
          currentTopVert = currentBottomVert + 1;
          prevBottomVert = currentBottomVert - 2;
          prevTopVert = ptVerticalsAll.length - 3;
          if(Math.abs(ptVerticalsAll[prevBottomVert].x - ptVerticalsAll[currentBottomVert].x) > minDistToPlaceSupports)
          {
            ptSupportsAll.push(new Point3d ((ptVerticalsAll[currentBottomVert].x - matHalf) - gradientSupportsWidth, ptVerticalsAll[currentBottomVert].y, supportsShift));
            ptSupportsAll.push(new Point3d (ptVerticalsAll[currentTopVert].x - matHalf, ptVerticalsAll[currentTopVert].y, supportsShift));
          }
        }
      }
      ptVerticalsAll.push(new Point3d(pos - minDist / 2, rowAbsolute[i] + matHalf, 0));
      ptVerticalsAll.push(new Point3d(pos - minDist / 2, rowAbsolute[i + 1] - matHalf, 0));
      ptVerticalsAll.push(new Point3d(pos + minDist / 2, rowAbsolute[i] + matHalf, 0));
      ptVerticalsAll.push(new Point3d(pos + minDist / 2, rowAbsolute[i + 1] - matHalf, 0));
      for(var d = 0; d < rightDiv.length; d++)
      {
        ptVerticalsAll.push(new Point3d(((maxx - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(((maxx - (pos + minDist / 2)) * rightDiv[d]) + ( pos + minDist / 2), rowAbsolute[i + 1] - matHalf, 0));
        if((d == 1 && rightDiv.length >= 2) || (d == 3 && rightDiv.length >= 4))
        {
          currentBottomVert = ptVerticalsAll.length - 2;
          prevBottomVert = currentBottomVert - 2;
          prevTopVert = ptVerticalsAll.length - 3;
          if(Math.abs(ptVerticalsAll[prevBottomVert].x - ptVerticalsAll[currentBottomVert].x) > minDistToPlaceSupports)
          {
            ptSupportsAll.push(new Point3d ((ptVerticalsAll[prevBottomVert].x + matHalf), ptVerticalsAll[prevBottomVert].y, supportsShift));
            ptSupportsAll.push(new Point3d (ptVerticalsAll[prevTopVert].x + matHalf + gradientSupportsWidth, ptVerticalsAll[prevTopVert].y, supportsShift));
          }
        }
      }
      if(maxx - pos > minDist)
      {
        ptVerticalsAll.push(new Point3d(maxx - matHalf, rowAbsolute[i] + matHalf, 0));
        ptVerticalsAll.push(new Point3d(maxx - matHalf, rowAbsolute[i + 1] - matHalf, 0));
      }
      if(pos > minx + (minDist * 2 + mat) && (parameterSnapped * 0.01 > 0.49 || maxx - minx >= 90))
      {
        ptSupportsAll.push(new Point3d (minx + mat, rowAbsolute[i] + matHalf, supportsShift));
        ptSupportsAll.push(new Point3d (minx + mat + gradientSupportsWidth, rowAbsolute[i + 1] - matHalf, supportsShift));
      }
      if(pos < maxx - (minDist * 2 + mat) && (parameterSnapped * 0.01 < 0.51 || maxx - minx >= 90))
      {
        ptSupportsAll.push(new Point3d (maxx - mat - gradientSupportsWidth, rowAbsolute[i] + matHalf, supportsShift));
        ptSupportsAll.push(new Point3d (maxx - mat, rowAbsolute[i + 1] - matHalf, supportsShift));
      }
    }
    var ptVerticalsBottom0 = [];
    var shadowMiddleLocation = [];
    var shadowMiddleSize = [];

    for ( i = 0; i < ptVerticalsAll.length; i++)
    {
      if (ptVerticalsAll[i].y == mat / 2) {
        ptVerticalsBottom0.push(ptVerticalsAll[i]);
      }
    }
    for ( var n = 0; n < rows; n++)
    {
      for ( i = 0; i < ptVerticalsBottom0.length - 1; i++)
      {
        shadowMiddleLocation.push(new Vector3d(ptVerticalsBottom0[i].x + (ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x) / 2, rowAbsolute[n] + (rowAbsolute[n + 1] - rowAbsolute[n]) / 2, shelfDepth / 2));
        shadowMiddleSize.push(new Vector3d(ptVerticalsBottom0[i + 1].x - ptVerticalsBottom0[i].x - mat, rowAbsolute[n + 1] - rowAbsolute[n] - mat, shelfDepth));
      }
    }
	return {
		ptHorizontalsAll: ptHorizontalsAll,
		ptVerticalsAll: ptVerticalsAll,
		ptSupportsAll: ptSupportsAll,
		shadowMiddleLocation: shadowMiddleLocation,
		shadowMiddleSize: shadowMiddleSize
	};
}};