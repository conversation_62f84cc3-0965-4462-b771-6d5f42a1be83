$(function(){

    var preLoader = require('../loader');
    window.preLoader = preLoader;

	var modelsPath = '/r_static/src_webgl/ivy/models/';

	var texturesPath = 'textures/cubemap/';
	var format = '.jpg';
    var urls = [
            modelsPath + texturesPath + 'nx' + format, modelsPath + texturesPath + 'px' + format,
            modelsPath + texturesPath + 'ny' + format, modelsPath + texturesPath + 'py' + format,
            modelsPath + texturesPath + 'nz' + format, modelsPath + texturesPath + 'pz' + format
	];


    preLoader.loadTexture('maple-hori', modelsPath + 'textures/maple_hori_fix.jpg');
    preLoader.loadTexture('maple-vert', modelsPath + 'textures/maple_vert_fix.jpg');
    preLoader.loadTexture('white-hori', modelsPath + 'textures/white_hori-fix.jpg');
    preLoader.loadTexture('white-vert', modelsPath + 'textures/white_vert-fix.jpg');
    preLoader.loadTexture('black-hori', modelsPath + 'textures/black_hori_fix2.jpg');
    preLoader.loadTexture('black-vert', modelsPath + 'textures/black_vert_fix2.jpg');
    preLoader.loadTexture('gray-hori', modelsPath + 'textures/grey_hori_wo_wood.jpg');
    preLoader.loadTexture('gray-vert', modelsPath + 'textures/grey_vert_wo_wood.jpg');
    preLoader.loadTexture('shadowbox', modelsPath + 'textures/jetty_shadowbox_diff_512.png');
    preLoader.loadTexture('cast_shadow', modelsPath + 'textures/cast_shadow3.jpg');
    preLoader.loadTexture('leg_texture', modelsPath + 'textures/leg.jpg');
    preLoader.loadTexture('drawers_texture', modelsPath + 'textures/drawers_old.jpg?1');

    preLoader.loadTexture('doors_open', modelsPath + 'textures/doors_open_old.jpg?1');
    preLoader.loadTexture('doors_close', modelsPath + 'textures/doors_close_old.jpg?1');

    //textures for scene
    // preLoader.loadTexture('wall', modelsPath + 'textures/scene/walls.jpg');
    // preLoader.loadTexture('sufit2', modelsPath + 'textures/scene/sufit.jpg');
    // preLoader.loadTexture('shadows1024', modelsPath + 'textures/scene/shadows1024.png');
    // preLoader.loadTexture('shadows1024_alpha', modelsPath + 'textures/scene/shadows1024_alpha.jpg');
    // preLoader.loadTexture('podloga2', modelsPath + 'textures/scene/podloga.jpg');
    // preLoader.loadTexture('fotel_obrazek', modelsPath + 'textures/scene/fotel_obrazek.jpg');



    //preLoader.loadCubemap('cubemap', urls);

	var preloader_config = {};

    preloader_config['jetty_vert_wall.obj'] = ['vertical', 'white_vert'];
    preloader_config['doors.obj'] = ['door', 'doors_open','doors_close'];
    preloader_config['drawers.obj'] = ['drawer_front', 'drawers_texture'];
    preloader_config['jetty_hori_wall.obj'] = ['horizontal', 'white_hori'];
    preloader_config['ivy_shadow.obj'] = ['shadow', 'shadowbox'];
    preloader_config['ivy_shadow_RIGHT.obj'] = ['shadow-left', 'shadowbox'];
    preloader_config['cast_shadow_center2.obj'] = ['cast-shadow-center', 'cast_shadow'];
    preloader_config['cast_shadow_left.obj'] = ['cast-shadow-right', 'cast_shadow'];
    preloader_config['cast_shadow_right.obj'] = ['cast-shadow-left', 'cast_shadow'];
    preloader_config['ivy_shadow_LEFT.obj'] = ['shadow-right', 'shadowbox'];
    preloader_config['support_right_shadow.obj'] = ['support-right', 'shadowbox'];
    preloader_config['support_left_shadow.obj'] = ['support-left', 'shadowbox'];
    preloader_config['leg.obj'] = ['leg', 'leg_texture'];
    preloader_config['handle_big.obj'] = ['handle_big', 'doors_open', 'doors_close'];
    preloader_config['handle_small.obj'] = ['handle_small', 'doors_open', 'doors_close'];
    preloader_config['handle_drawer.obj'] = ['handle_drawer', 'doors_open', 'doors_close'];
    preloader_config["handle_short_left.obj"] = ["handle_short_left", "doors_handle_short"];

     //for scene
    // preloader_config['fotel_low_poly.obj'] = ['fotel_low_poly','fotel_obrazek'];
    // preloader_config['fotel_low_poly001.obj'] = ['fotel_low_poly001','fotel_obrazek'];
    // preloader_config['fotel_shadow.obj'] = ['fotel_shadow', 'shadows1024',];
    // preloader_config['fotel_shadow001.obj'] = ['fotel_shadow001','shadows1024'];
    // preloader_config['obraz.obj'] = ['obraz','fotel_obrazek'];
    // preloader_config['Plane005.obj'] = ['Plane005','shadows1024'];
    // preloader_config['podloga.obj'] = ['podloga','podloga2'];
    // preloader_config['sufit.obj'] = ['sufit','sufit2'];
    // preloader_config['wall_behind_shelf.obj'] = ['wall_behind_shelf','wall'];
    // preloader_config['wall_corner001.obj'] = ['wall_corner001','wall'];
    // preloader_config['window_shadow001.obj'] = ['window_shadow001','shadows1024_alpha'];
    // preloader_config['window_shadow002.obj'] = ['window_shadow002','shadows1024_alpha'];
    // preloader_config['window_shadow003.obj'] = ['window_shadow003','shadows1024_alpha'];
    // preloader_config['window001.obj'] = ['window001','wall', 'shadows1024_alpha'];
    // preloader_config['window002.obj'] = ['window002','wall'];
    // preloader_config['window003.obj'] = ['window003','wall'];









    Site.Track.sendDataLayer("webgl_start_loading_ivy");
	preLoader.loadBatch("/r_static/ivy5.objx", preloader_config);

	var build = require('./build3dold');
	require('./main')(preLoader, build);
	require('./grid')(preLoader, build);

});
