function preparePoints(dnaTools, dnaJson, motion, width, rows, rowHeights, rowStyles,
                       depth, half_material, support_size, initial,
                       shadow_settings=[2,2,4,2], gen_row_styles_for_buttons=true,
                       styles_slider=-1, backpanels_rows=null) {

    return fetch('/api/dekoder', {'motion':motion, 'width': width * 10}).then(response => response.json());
}

export { preparePoints as preparePoints};
