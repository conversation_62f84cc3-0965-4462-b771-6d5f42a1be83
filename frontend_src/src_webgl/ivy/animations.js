THREE = THREE || {};
let isMobile = $('.webglconfigurator').hasClass('mobile');

const TWEEN_OPENING = 1;
const TWEEN_CLOSING = -1;
const TWEEN_STOPPED = 0;


const rotation_max = 40.0; // doors max rotation
const drawer_tween_time = 900;

let drawer_depth = 320;
let door_tweens = [null,null,null,null,null,null,null,null,null,null,null];
let door_tweens_direction = [0,0,0,0,0,0,0,0,0,0,0,0];
let drawer_tweens = [null,null,null,null,null,null,null,null,null,null,null];
let drawer_tweens_direction = [0,0,0,0,0,0,0,0,0,0,0,0];
let shelf_depth = 320;


class Animation {
    constructor(){
    }

   set_tweens_for_row(selected_row, door_lists, drawer_list, depth){
        shelf_depth = depth;
        drawer_depth = depth;
        this.set_tweens_for_doors(selected_row, door_lists);
        this.set_tweens_for_drawers(selected_row, drawer_list);
   }
   set_tweens_for_drawers(selected_row, drawers_lists) {

        for (let row_i=0; row_i< drawer_tweens.length ;row_i++) {
            if (row_i == selected_row){
                continue
            }
            if (drawer_tweens_direction[row_i] == -1){
                //skip as its already going back
                continue;
            }
            // stop tweens
            if (drawer_tweens[row_i] != null){
                drawer_tweens[row_i].stop();
                TWEEN.remove(drawer_tweens[row_i]);
                drawer_tweens_direction[row_i] = TWEEN_STOPPED;
            }
            if (drawers_lists[row_i].length == 0 ){
                continue;
            }
            drawer_tweens_direction[selected_row] = TWEEN_CLOSING;
            let drawer_group = drawers_lists[row_i];
            let tweenDrawer = new TWEEN.Tween( { moving_progress: drawer_group[0].position.z - drawer_depth, row_selected:row_i, drawer_group: drawer_group} )
            .to( { moving_progress: (320 - drawer_depth) + (shelf_depth - 320)}, drawer_tween_time)
            .easing(TWEEN.Easing.Quadratic.Out)
            .onUpdate( function () {
                if (this.drawer_group) {
                    for (let i=0; i< this.drawer_group.length ;i++) {
                        this.drawer_group[i].position.z = drawer_depth + this.moving_progress;
                    }
                }
            }).onComplete(function(){
                drawer_tweens_direction[this.row_selected] = TWEEN_STOPPED;
            });
            drawer_tweens[row_i] = tweenDrawer;
            tweenDrawer.start();
        }


        if (selected_row == -1 || drawer_tweens_direction[selected_row] == 1 ||
            (drawers_lists[selected_row].length == 0 )){
            // no drawers or already opening
        } else {
            if (drawer_tweens[selected_row] != null){
                drawer_tweens[selected_row].stop();
                TWEEN.remove(drawer_tweens[selected_row]);
                drawer_tweens_direction[selected_row] = TWEEN_STOPPED;
            }
            drawer_tweens_direction[selected_row] = TWEEN_OPENING;
            let drawer_group = drawers_lists[selected_row];
            var tweenDrawer = new TWEEN.Tween( { moving_progress: drawer_group[0].position.z - drawer_depth,row_selected:selected_row, drawer_group: drawer_group} )
                    .to( { moving_progress: 220 + (shelf_depth -320) }, 400)
                    .easing(TWEEN.Easing.Quadratic.Out)
                    .onUpdate( function () {
                        if (this.drawer_group) {
                            for (let i=0; i< this.drawer_group.length ;i++) {
                                this.drawer_group[i].position.z = drawer_depth + this.moving_progress;
                            }
                        }
                    })
                    .onComplete(function(){
                        drawer_tweens_direction[this.row_selected] = TWEEN_STOPPED;
                    });
            drawer_tweens[selected_row] = tweenDrawer;
            tweenDrawer.start();
        }

   }

   set_tweens_for_doors(selected_row, door_lists){
       for (let row_i=0; row_i< door_tweens.length ;row_i++) {

            if (row_i == selected_row){
                continue
            }
            if (door_tweens_direction[row_i] == -1){
                //skip as its already going back
                continue;
            }
            // stop tweens
            if (door_tweens[row_i] != null){
                door_tweens[row_i].stop();
                TWEEN.remove(door_tweens[row_i]);
                door_tweens_direction[row_i] = 0;

            }

            if (door_lists[row_i].length == 0 || (door_lists[row_i].length > 0 && door_lists[row_i][0] && door_lists[row_i][0].rotation.y == 0)){
                continue;
            }
            door_tweens_direction[selected_row] = -1;
            var tweenDoor = new TWEEN.Tween( { rotation_progress: -door_lists[row_i][0].rotate_sign * ((door_lists[row_i][0].rotation.y * (180/Math.PI))/rotation_max) *100,row_selected:row_i, door_group: door_lists[row_i]} )
            .to( { rotation_progress: 0}, 1100)
            //.easing(TWEEN.Easing.Quadratic.InOut)
            .onUpdate( function () {
                if (this.door_group) {
                    for (let i=0; i< this.door_group.length ;i++) {
                        let rotation = (-this.door_group[i].rotate_sign * rotation_max) * (this.rotation_progress/100.0);
                        this.door_group[i].rotation.y = rotation * Math.PI / 180;
                        this.door_group[i].traverse(function (child) {
                            if (child instanceof THREE.Mesh) {
                                if (child.material.uniforms) {
                                    child.material.uniforms.blending_ratio.value = Math.abs(rotation) / rotation_max;
                                    // child.material.uniforms.blending_ratio.value = 0;
                                }
                            }
                        });
                    }
                }
            }).onComplete(function(){
                door_tweens_direction[this.row_selected] = 0;
            });
            door_tweens[row_i] = tweenDoor;
            tweenDoor.start();
        }

        if (selected_row == -1 || /*door_tweens_direction[selected_row] == 1 ||*/
            (door_lists[selected_row].length == 0 /*|| (door_lists[selected_row].length > 0 && door_lists[selected_row][0] && Math.abs(door_lists[selected_row][0].rotation.y) == (rotation_max * Math.PI / 180))*/)){
            // no doors or already opening
        } else {
            if (door_tweens[selected_row] != null){
                door_tweens[selected_row].stop();
                TWEEN.remove(door_tweens[selected_row]);
                door_tweens_direction[selected_row] = 0;
            }
            door_tweens_direction[selected_row] = 1;
            var tweenDoor = new TWEEN.Tween( { rotation_progress: -door_lists[selected_row][0].rotate_sign * ((door_lists[selected_row][0].rotation.y * (180/Math.PI))/rotation_max) *100,row_selected:selected_row, door_group: door_lists[selected_row]} )
                    .to( { rotation_progress: 100}, 500)
                    //.easing(TWEEN.Easing.Quadratic.InOut)
                    .onUpdate( function () {
                        if (this.door_group) {
                            for (let i=0; i< this.door_group.length ;i++) {
                                let rotation = (-this.door_group[i].rotate_sign * rotation_max) * (this.rotation_progress/100.0);
                                this.door_group[i].rotation.y = rotation * Math.PI / 180;
                                this.door_group[i].traverse(function (child) {
                                    if (child instanceof THREE.Mesh) {
                                        if (child.material.uniforms) {
                                            child.material.uniforms.blending_ratio.value = Math.abs(rotation) / rotation_max;
                                        }
                                    }
                                });
                            }
                        }
                    })
                    .onComplete(function(){
                        door_tweens_direction[this.row_selected] = 0;
                    });
            door_tweens[selected_row] = tweenDoor;
            tweenDoor.start();
        }
   }
}


module.exports = Animation;