var preparePoints = function(dnaTools, dnaJson, motion, width, rows, rowHeights, rowStyles,
                       depth, half_material, support_size, snapping,
                       shadow_settings=[2,2,4,2], gen_row_styles_for_buttons=true,
                       styles_slider=-1, backpanels_rows=null, shelf_type=0) {
    let points = dnaTools.get_elements(
      dnaJson,
      motion,
      width,
      rows,
      rowHeights,
      rowStyles,
      depth,
      half_material,
      support_size,
      snapping,
      shadow_settings,
    gen_row_styles_for_buttons,
    styles_slider,
    backpanels_rows || [],
    shelf_type || 0
    );
    return points;
};


export { preparePoints as preparePoints};
