{"name": "shadow-raytrace", "version": "1.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@types/acorn": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/@types/acorn/-/acorn-4.0.3.tgz", "integrity": "sha512-gou/kWQkGPMZjdCKNZGDpqxLm9+ErG/pFZKPX4tvCjr0Xf4FCYYX3nAsu7aDVKJV3KUe27+mvqqyWT/9VZoM/A==", "requires": {"@types/estree": "*"}}, "@types/estree": {"version": "0.0.38", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-0.0.38.tgz", "integrity": "sha512-F/v7t1LwS4vnXuPooJQGBRKRGIoxWUTmA4VHfqjOccFsNDThD5bfUNpITive6s352O7o384wcpEaDV8rHCehDA=="}, "Base64": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/Base64/-/Base64-0.2.1.tgz", "integrity": "sha1-ujpCMHCOGGcFBl5mur3Uw1z2ACg="}, "JSONStream": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/JSONStream/-/JSONStream-1.3.2.tgz", "integrity": "sha1-wQI3G27Dp887hHygDCC7D85Mbeo=", "requires": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}}, "abbrev": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q=="}, "acorn": {"version": "5.5.3", "resolved": "https://registry.npmjs.org/acorn/-/acorn-5.5.3.tgz", "integrity": "sha1-9HPdR+AnegjijpvsWu6wR1HwuMk="}, "acorn-dynamic-import": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/acorn-dynamic-import/-/acorn-dynamic-import-3.0.0.tgz", "integrity": "sha512-zVWV8Z8lislJoOKKqdNMOB+s6+XV5WERty8MnKBeFgwA+19XJjJHs2RP5dzM57FftIs+jQnRToLiWazKr6sSWg==", "requires": {"acorn": "^5.0.0"}}, "acorn-jsx": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.1.tgz", "integrity": "sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=", "dev": true, "requires": {"acorn": "^3.0.4"}, "dependencies": {"acorn": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz", "integrity": "sha1-ReN/s56No/JbruP/U2niu18iAXo=", "dev": true}}}, "acorn-node": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/acorn-node/-/acorn-node-1.3.0.tgz", "integrity": "sha512-efP54n3d1aLfjL2UMdaXa6DsswwzJeI5rqhbFvXMrKiJ6eJFpf+7R0zN7t8IC+XKn2YOAFAv6xbBNgHUkoHWLw==", "requires": {"acorn": "^5.4.1", "xtend": "^4.0.1"}}, "ajv": {"version": "5.5.2", "resolved": "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz", "integrity": "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=", "requires": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.3.0"}}, "ajv-keywords": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.1.1.tgz", "integrity": "sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I=", "dev": true}, "amdefine": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU="}, "ansi-colors": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-1.1.0.tgz", "integrity": "sha512-SFKX67auSNoVR38N3L+nvsPjOE0bybKTYbkf5tRvushrAPQ9V75huw0ZxBkKVeRU9kqH3d6HA4xTckbwZ4ixmA==", "requires": {"ansi-wrap": "^0.1.0"}}, "ansi-cyan": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-cyan/-/ansi-cyan-0.1.1.tgz", "integrity": "sha1-U4rlKK+JgvKK4w2G8vF0VtJgmHM=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-escapes": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.1.0.tgz", "integrity": "sha512-UgAb8H9D41AQnu/PbWlCofQVcnV4Gs2bBJi9eZPxfU/hgglFh3SMDMENRIqdr7H6XFnXdoknctFByVsCOotTVw==", "dev": true}, "ansi-gray": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-gray/-/ansi-gray-0.1.1.tgz", "integrity": "sha1-KWLPVOyXksSFEKPetSRDaGHvclE=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-red": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ansi-red/-/ansi-red-0.1.1.tgz", "integrity": "sha1-jGOPnRCAgAo1PJwoyKgcpHBdlGw=", "requires": {"ansi-wrap": "0.1.0"}}, "ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="}, "ansi-wrap": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/ansi-wrap/-/ansi-wrap-0.1.0.tgz", "integrity": "sha1-qCJQ3bABXponyoLoLqYDu/pF768="}, "anymatch": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz", "integrity": "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA==", "requires": {"micromatch": "^2.1.5", "normalize-path": "^2.0.0"}, "dependencies": {"arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "requires": {"is-posix-bracket": "^0.1.0"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "requires": {"is-extglob": "^1.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}}}}, "append-buffer": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/append-buffer/-/append-buffer-1.0.2.tgz", "integrity": "sha1-2CIM9GYIFSXv6lBhTz3mUU36WPE=", "requires": {"buffer-equal": "^1.0.0"}, "dependencies": {"buffer-equal": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/buffer-equal/-/buffer-equal-1.0.0.tgz", "integrity": "sha1-WWFrSYME1Var1GaWayLu2j7KX74="}}}, "aproba": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="}, "archy": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/archy/-/archy-1.0.0.tgz", "integrity": "sha1-+cjBN1fMHde8N5rHeyxipcKGjEA="}, "are-we-there-yet": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.4.tgz", "integrity": "sha1-u13KOCu5TwXhUZQ3PRb9O6HKEQ0=", "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "argparse": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}, "aria-query": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/aria-query/-/aria-query-0.7.1.tgz", "integrity": "sha1-Jsu1r/ZBRLCoJb4YRuCxbPoAsR4=", "dev": true, "requires": {"ast-types-flow": "0.0.7", "commander": "^2.11.0"}, "dependencies": {"commander": {"version": "2.15.1", "resolved": "https://registry.npmjs.org/commander/-/commander-2.15.1.tgz", "integrity": "sha512-VlfT9F3V0v+jr4yxPc5gg9s62/fIVWsd2Bk2iD435um1NlGMYdVCq+MjcXnhYq2icNOizHr1kK+5TI6H0Hy0ag==", "dev": true}}}, "arr-diff": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="}, "arr-filter": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/arr-filter/-/arr-filter-1.1.2.tgz", "integrity": "sha1-Q/3d0JHo7xGqTEXZzcGOLf8XEe4=", "requires": {"make-iterator": "^1.0.0"}}, "arr-flatten": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="}, "arr-map": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/arr-map/-/arr-map-2.0.2.tgz", "integrity": "sha1-Onc0X/wc814qkYJWAfnljy4kysQ=", "requires": {"make-iterator": "^1.0.0"}}, "arr-union": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="}, "array-differ": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/array-differ/-/array-differ-1.0.0.tgz", "integrity": "sha1-7/UuN1gknTO+QCuLuOVkuytdQDE="}, "array-each": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/array-each/-/array-each-1.0.1.tgz", "integrity": "sha1-p5SvDAWrF1KEbudTofIRoFugxE8="}, "array-filter": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/array-filter/-/array-filter-0.0.1.tgz", "integrity": "sha1-fajPLiZijtcygDWB/SH2fKzS7uw="}, "array-find-index": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.2.tgz", "integrity": "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E="}, "array-includes": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/array-includes/-/array-includes-3.0.3.tgz", "integrity": "sha1-GEtI9i2S10UrsxsyMWXH+L0CJm0=", "dev": true, "requires": {"define-properties": "^1.1.2", "es-abstract": "^1.7.0"}}, "array-initial": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/array-initial/-/array-initial-1.1.0.tgz", "integrity": "sha1-L6dLJnOTccOUe9enrcc74zSz15U=", "requires": {"array-slice": "^1.0.0", "is-number": "^4.0.0"}, "dependencies": {"is-number": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz", "integrity": "sha512-rSklcAIlf1OmFdyAqbnWTLVelsQ58uvZ66S/ZyawjWqIviTWCjg2PzVGw8WUA+nNuPTqb4wgA+NszrJ+08LlgQ=="}}}, "array-last": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/array-last/-/array-last-1.3.0.tgz", "integrity": "sha512-eOCut5rXlI6aCOS7Z7kCplKRKyiFQ6dHFBem4PwlwKeNFk2/XxTrhRh5T9PyaEWGy/NHTZWbY+nsZlNFJu9rYg==", "requires": {"is-number": "^4.0.0"}, "dependencies": {"is-number": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz", "integrity": "sha512-rSklcAIlf1OmFdyAqbnWTLVelsQ58uvZ66S/ZyawjWqIviTWCjg2PzVGw8WUA+nNuPTqb4wgA+NszrJ+08LlgQ=="}}}, "array-map": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/array-map/-/array-map-0.0.0.tgz", "integrity": "sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI="}, "array-reduce": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/array-reduce/-/array-reduce-0.0.0.tgz", "integrity": "sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys="}, "array-slice": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/array-slice/-/array-slice-1.1.0.tgz", "integrity": "sha1-42jqFfibxwaff/uJrsOmx9SsItQ="}, "array-sort": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/array-sort/-/array-sort-1.0.0.tgz", "integrity": "sha512-ihLeJkonmdiAsD7vpgN3CRcx2J2S0TiYW+IS/5zHBI7mKUq3ySvBdzzBfD236ubDBQFiiyG3SWCPc+msQ9KoYg==", "requires": {"default-compare": "^1.0.0", "get-value": "^2.0.6", "kind-of": "^5.0.2"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "array-union": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "requires": {"array-uniq": "^1.0.1"}}, "array-uniq": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="}, "array-unique": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="}, "arrify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true}, "asap": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "dev": true}, "asn1": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.3.tgz", "integrity": "sha1-2sh4dxPJlmhJ/IGAd36+nB3fO4Y="}, "asn1.js": {"version": "4.10.1", "resolved": "https://registry.npmjs.org/asn1.js/-/asn1.js-4.10.1.tgz", "integrity": "sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==", "requires": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "assert": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/assert/-/assert-1.4.1.tgz", "integrity": "sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE=", "requires": {"util": "0.10.3"}}, "assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="}, "assign-symbols": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="}, "ast-types-flow": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.7.tgz", "integrity": "sha1-9wtzXGvKGlycItmCw+Oef+ujva0=", "dev": true}, "astw": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/astw/-/astw-2.2.0.tgz", "integrity": "sha1-e9QXhNMkk5h66yOba04cV6hzuRc=", "requires": {"acorn": "^4.0.3"}, "dependencies": {"acorn": {"version": "4.0.13", "resolved": "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz", "integrity": "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c="}}}, "async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha1-trvgsGdLnXGXCMo43owjfLUmw9E="}, "async-done": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/async-done/-/async-done-1.2.4.tgz", "integrity": "sha512-mxc+yISkb0vjsuvG3dJCIZXzRWjKndQ9Zo9zNDJ1K2wh9eP0E0oGmOWm+4cFOvW4dA0tGFImTW5tQJHCtn1kIQ==", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.2", "process-nextick-args": "^1.0.7", "stream-exhaust": "^1.0.1"}, "dependencies": {"process-nextick-args": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "integrity": "sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M="}}}, "async-each": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.1.tgz", "integrity": "sha1-GdOGodntxufByF04iu28xW0zYC0="}, "async-settle": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/async-settle/-/async-settle-1.0.0.tgz", "integrity": "sha1-HQqRS7Aldb7IqPOnTlCA9yssDGs=", "requires": {"async-done": "^1.2.2"}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "asyncro": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/asyncro/-/asyncro-2.0.1.tgz", "integrity": "sha1-RBeDwTTQ4TWIr4lL1PjRyDRjDV0="}, "atob": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/atob/-/atob-2.1.0.tgz", "integrity": "sha1-qysVDlHXsSK578jXNAwGtsQQdrw="}, "aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="}, "aws4": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.7.0.tgz", "integrity": "sha512-32NDda82rhwD9/JBCCkB+MRYDp0oSvlo2IL6rQWA10PQi7tDUM3eqMSltXmY+Oyl/7N3P3qNtAlv7X0d9bI28w=="}, "axobject-query": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/axobject-query/-/axobject-query-0.1.0.tgz", "integrity": "sha1-YvWdvFnJ+SQnWco0mWDnov48NsA=", "dev": true, "requires": {"ast-types-flow": "0.0.7"}}, "babel-code-frame": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.26.0.tgz", "integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "requires": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}}, "babel-core": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-core/-/babel-core-6.26.0.tgz", "integrity": "sha1-rzL3izGm/O8RnIew/Y2XU/A6C7g=", "requires": {"babel-code-frame": "^6.26.0", "babel-generator": "^6.26.0", "babel-helpers": "^6.24.1", "babel-messages": "^6.23.0", "babel-register": "^6.26.0", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "convert-source-map": "^1.5.0", "debug": "^2.6.8", "json5": "^0.5.1", "lodash": "^4.17.4", "minimatch": "^3.0.4", "path-is-absolute": "^1.0.1", "private": "^0.1.7", "slash": "^1.0.0", "source-map": "^0.5.6"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "babel-generator": {"version": "6.26.1", "resolved": "https://registry.npmjs.org/babel-generator/-/babel-generator-6.26.1.tgz", "integrity": "sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA=", "requires": {"babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "detect-indent": "^4.0.0", "jsesc": "^1.3.0", "lodash": "^4.17.4", "source-map": "^0.5.7", "trim-right": "^1.0.1"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "babel-helper-builder-react-jsx": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-helper-builder-react-jsx/-/babel-helper-builder-react-jsx-6.26.0.tgz", "integrity": "sha1-Of+DE7dci2Xc7/HzHTg+D/KkCKA=", "requires": {"babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "esutils": "^2.0.2"}}, "babel-helper-call-delegate": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helper-call-delegate/-/babel-helper-call-delegate-6.24.1.tgz", "integrity": "sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=", "requires": {"babel-helper-hoist-variables": "^6.24.1", "babel-runtime": "^6.22.0", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-helper-define-map": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-helper-define-map/-/babel-helper-define-map-6.26.0.tgz", "integrity": "sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=", "requires": {"babel-helper-function-name": "^6.24.1", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "lodash": "^4.17.4"}, "dependencies": {"lodash": {"version": "4.17.5", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha1-maktZcAnLevoyWtgV7yPv6O+1RE="}}}, "babel-helper-function-name": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz", "integrity": "sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=", "requires": {"babel-helper-get-function-arity": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-helper-get-function-arity": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz", "integrity": "sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-helper-hoist-variables": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.24.1.tgz", "integrity": "sha1-HssnaJydJVE+rbyZFKc/VAi+enY=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-helper-optimise-call-expression": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz", "integrity": "sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-helper-regex": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-helper-regex/-/babel-helper-regex-6.26.0.tgz", "integrity": "sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=", "requires": {"babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "lodash": "^4.17.4"}, "dependencies": {"lodash": {"version": "4.17.5", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha1-maktZcAnLevoyWtgV7yPv6O+1RE="}}}, "babel-helper-replace-supers": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz", "integrity": "sha1-v22/5Dk40XNpohPKiov3S2qQqxo=", "requires": {"babel-helper-optimise-call-expression": "^6.24.1", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-helpers": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-helpers/-/babel-helpers-6.24.1.tgz", "integrity": "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=", "requires": {"babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-messages": {"version": "6.23.0", "resolved": "https://registry.npmjs.org/babel-messages/-/babel-messages-6.23.0.tgz", "integrity": "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-check-es2015-constants": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz", "integrity": "sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-external-helpers": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-external-helpers/-/babel-plugin-external-helpers-6.22.0.tgz", "integrity": "sha1-IoX0iwK9Xe3oUXXK+MYuhq3M76E=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-syntax-flow": {"version": "6.18.0", "resolved": "https://registry.npmjs.org/babel-plugin-syntax-flow/-/babel-plugin-syntax-flow-6.18.0.tgz", "integrity": "sha1-TDqyCiryaqIM0lmVw5jE63AxDI0="}, "babel-plugin-syntax-jsx": {"version": "6.18.0", "resolved": "https://registry.npmjs.org/babel-plugin-syntax-jsx/-/babel-plugin-syntax-jsx-6.18.0.tgz", "integrity": "sha1-CvMqmm4Tyno/1QaeYtew9Y0NiUY="}, "babel-plugin-transform-es2015-arrow-functions": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz", "integrity": "sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-block-scoped-functions": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz", "integrity": "sha1-u8UbSflk1wy42OC5ToICRs46YUE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-block-scoping": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz", "integrity": "sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=", "requires": {"babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "lodash": "^4.17.4"}, "dependencies": {"lodash": {"version": "4.17.5", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha1-maktZcAnLevoyWtgV7yPv6O+1RE="}}}, "babel-plugin-transform-es2015-classes": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz", "integrity": "sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=", "requires": {"babel-helper-define-map": "^6.24.1", "babel-helper-function-name": "^6.24.1", "babel-helper-optimise-call-expression": "^6.24.1", "babel-helper-replace-supers": "^6.24.1", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-computed-properties": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz", "integrity": "sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=", "requires": {"babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-destructuring": {"version": "6.23.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz", "integrity": "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-duplicate-keys": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz", "integrity": "sha1-c+s9MQypaePvnskcU3QabxV2Qj4=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-for-of": {"version": "6.23.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.23.0.tgz", "integrity": "sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-function-name": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.24.1.tgz", "integrity": "sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=", "requires": {"babel-helper-function-name": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-literals": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.22.0.tgz", "integrity": "sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-modules-amd": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz", "integrity": "sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=", "requires": {"babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-modules-commonjs": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.0.tgz", "integrity": "sha1-DYOUApt9xqvhqX7xgeAHWN0uXYo=", "requires": {"babel-plugin-transform-strict-mode": "^6.24.1", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-types": "^6.26.0"}}, "babel-plugin-transform-es2015-modules-systemjs": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz", "integrity": "sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=", "requires": {"babel-helper-hoist-variables": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-modules-umd": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz", "integrity": "sha1-rJl+YoXNGO1hdq22B9YCNErThGg=", "requires": {"babel-plugin-transform-es2015-modules-amd": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-object-super": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.24.1.tgz", "integrity": "sha1-JM72muIcuDp/hgPa0CH1cusnj40=", "requires": {"babel-helper-replace-supers": "^6.24.1", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-parameters": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz", "integrity": "sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=", "requires": {"babel-helper-call-delegate": "^6.24.1", "babel-helper-get-function-arity": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-shorthand-properties": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz", "integrity": "sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-spread": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz", "integrity": "sha1-1taKmfia7cRTbIGlQujdnxdG+NE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-sticky-regex": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz", "integrity": "sha1-AMHNsaynERLN8M9hJsLta0V8zbw=", "requires": {"babel-helper-regex": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-template-literals": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.22.0.tgz", "integrity": "sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-typeof-symbol": {"version": "6.23.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz", "integrity": "sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-unicode-regex": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz", "integrity": "sha1-04sS9C6nMj9yk4fxinxa4frrNek=", "requires": {"babel-helper-regex": "^6.24.1", "babel-runtime": "^6.22.0", "regexpu-core": "^2.0.0"}}, "babel-plugin-transform-flow-strip-types": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-flow-strip-types/-/babel-plugin-transform-flow-strip-types-6.22.0.tgz", "integrity": "sha1-hMtnKTXUNxT9wyvOhFaNh0Qc988=", "requires": {"babel-plugin-syntax-flow": "^6.18.0", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-react-display-name": {"version": "6.25.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-react-display-name/-/babel-plugin-transform-react-display-name-6.25.0.tgz", "integrity": "sha1-Z+K/Hx6ck6sI25Z5LgU5K/LMKNE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-react-jsx": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-react-jsx/-/babel-plugin-transform-react-jsx-6.24.1.tgz", "integrity": "sha1-hAoCjn30YN/DotKfDA2R9jduZqM=", "requires": {"babel-helper-builder-react-jsx": "^6.24.1", "babel-plugin-syntax-jsx": "^6.8.0", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-react-jsx-self": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-react-jsx-self/-/babel-plugin-transform-react-jsx-self-6.22.0.tgz", "integrity": "sha1-322AqdomEqEh5t3XVYvL7PBuY24=", "requires": {"babel-plugin-syntax-jsx": "^6.8.0", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-react-jsx-source": {"version": "6.22.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-react-jsx-source/-/babel-plugin-transform-react-jsx-source-6.22.0.tgz", "integrity": "sha1-ZqwSFT9c0tF7PBkmj0vwGX9E7NY=", "requires": {"babel-plugin-syntax-jsx": "^6.8.0", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-regenerator": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.26.0.tgz", "integrity": "sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=", "requires": {"regenerator-transform": "^0.10.0"}}, "babel-plugin-transform-strict-mode": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.24.1.tgz", "integrity": "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-preset-es2015": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-preset-es2015/-/babel-preset-es2015-6.24.1.tgz", "integrity": "sha1-1EBQ1rwsn+6nAqrzjXJ6AhBTiTk=", "requires": {"babel-plugin-check-es2015-constants": "^6.22.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoped-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.24.1", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-destructuring": "^6.22.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.24.1", "babel-plugin-transform-es2015-for-of": "^6.22.0", "babel-plugin-transform-es2015-function-name": "^6.24.1", "babel-plugin-transform-es2015-literals": "^6.22.0", "babel-plugin-transform-es2015-modules-amd": "^6.24.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-plugin-transform-es2015-modules-systemjs": "^6.24.1", "babel-plugin-transform-es2015-modules-umd": "^6.24.1", "babel-plugin-transform-es2015-object-super": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-sticky-regex": "^6.24.1", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.22.0", "babel-plugin-transform-es2015-unicode-regex": "^6.24.1", "babel-plugin-transform-regenerator": "^6.24.1"}}, "babel-preset-es2015-rollup": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/babel-preset-es2015-rollup/-/babel-preset-es2015-rollup-3.0.0.tgz", "integrity": "sha1-hUtj7N4u6YysQOiC9nv88YWx8ko=", "requires": {"babel-plugin-external-helpers": "^6.18.0", "babel-preset-es2015": "^6.3.13", "require-relative": "^0.8.7"}}, "babel-preset-flow": {"version": "6.23.0", "resolved": "https://registry.npmjs.org/babel-preset-flow/-/babel-preset-flow-6.23.0.tgz", "integrity": "sha1-5xIYiHCFrpoktb5Baa/7WZgWxJ0=", "requires": {"babel-plugin-transform-flow-strip-types": "^6.22.0"}}, "babel-preset-react": {"version": "6.24.1", "resolved": "https://registry.npmjs.org/babel-preset-react/-/babel-preset-react-6.24.1.tgz", "integrity": "sha1-umnfrqRfw+xjm2pOzqbhdwLJE4A=", "requires": {"babel-plugin-syntax-jsx": "^6.3.13", "babel-plugin-transform-react-display-name": "^6.23.0", "babel-plugin-transform-react-jsx": "^6.24.1", "babel-plugin-transform-react-jsx-self": "^6.22.0", "babel-plugin-transform-react-jsx-source": "^6.22.0", "babel-preset-flow": "^6.23.0"}}, "babel-register": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-register/-/babel-register-6.26.0.tgz", "integrity": "sha1-btAhFz4vy0htestFxgCahW9kcHE=", "requires": {"babel-core": "^6.26.0", "babel-runtime": "^6.26.0", "core-js": "^2.5.0", "home-or-tmp": "^2.0.0", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "source-map-support": "^0.4.15"}}, "babel-runtime": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "requires": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "babel-template": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-template/-/babel-template-6.26.0.tgz", "integrity": "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=", "requires": {"babel-runtime": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "lodash": "^4.17.4"}, "dependencies": {"lodash": {"version": "4.17.5", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha1-maktZcAnLevoyWtgV7yPv6O+1RE="}}}, "babel-traverse": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-traverse/-/babel-traverse-6.26.0.tgz", "integrity": "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=", "requires": {"babel-code-frame": "^6.26.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "debug": "^2.6.8", "globals": "^9.18.0", "invariant": "^2.2.2", "lodash": "^4.17.4"}, "dependencies": {"lodash": {"version": "4.17.5", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha1-maktZcAnLevoyWtgV7yPv6O+1RE="}}}, "babel-types": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-types/-/babel-types-6.26.0.tgz", "integrity": "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=", "requires": {"babel-runtime": "^6.26.0", "esutils": "^2.0.2", "lodash": "^4.17.4", "to-fast-properties": "^1.0.3"}, "dependencies": {"lodash": {"version": "4.17.5", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha1-maktZcAnLevoyWtgV7yPv6O+1RE="}}}, "babelify": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/babelify/-/babelify-8.0.0.tgz", "integrity": "sha512-xVr63fKEvMWUrrIbqlHYsMcc5Zdw4FSVesAHgkgajyCE1W8gbm9rbMakqavhxKvikGYMhEcqxTwB/gQmQ6lBtw=="}, "babylon": {"version": "6.18.0", "resolved": "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz", "integrity": "sha1-ry87iPpvXB5MY00aD46sT1WzleM="}, "bach": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/bach/-/bach-1.2.0.tgz", "integrity": "sha1-Szzpa/JxNPeaG0FKUcFONMO9mIA=", "requires": {"arr-filter": "^1.1.1", "arr-flatten": "^1.0.1", "arr-map": "^2.0.0", "array-each": "^1.0.0", "array-initial": "^1.0.0", "array-last": "^1.1.1", "async-done": "^1.2.2", "async-settle": "^1.0.0", "now-and-later": "^2.0.0"}}, "balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "base": {"version": "0.11.2", "resolved": "https://registry.npmjs.org/base/-/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-js": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.2.3.tgz", "integrity": "sha512-MsAhsUW1GxCdgYSO6tAfZrNapmUKk7mWx/k5mFY/A1gBtkaCaNapTg+FExCw1r9yeaZhqx/xPg43xgTFH6KL5w=="}, "bcrypt-pbkdf": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz", "integrity": "sha1-Y7xdy2EzG5K8Bf1SiVPDNGKgb40=", "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "beeper": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/beeper/-/beeper-1.1.1.tgz", "integrity": "sha1-5tXqjF2tABMEpwsiY4RH9pyy+Ak="}, "binary-extensions": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.11.0.tgz", "integrity": "sha1-RqoXUftqL5PuXmibsQh9SxTGwgU="}, "bindings": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/bindings/-/bindings-1.3.0.tgz", "integrity": "sha512-DpLh5EzMR2kzvX1KIlVC0VkC3iZtHKTgdtZ0a3pglBZdaQFjt5S9g9xd1lE+YvXyfd6mtCeRnrUfOLYiTMlNSw=="}, "bit-twiddle": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bit-twiddle/-/bit-twiddle-1.0.2.tgz", "integrity": "sha1-DGwfq+KyPRcXPZpht7cJPrnhdp4="}, "bl": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/bl/-/bl-1.2.2.tgz", "integrity": "sha512-e8tQYnZodmebYDWGH7KMRvtzKXaJHx3BbilrgZCfvyLUYdKpK1t5PSPmpkny/SgiTSCnjfLW7v5rlONXVFkQEA==", "requires": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "block-stream": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/block-stream/-/block-stream-0.0.9.tgz", "integrity": "sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=", "requires": {"inherits": "~2.0.0"}}, "bn.js": {"version": "4.11.8", "resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.11.8.tgz", "integrity": "sha512-ItfYfPLkWHUjckQCk8xC+LwxgK8NYcXywGigJgSwOP8Y2iyWT4f2vsZnoOXTTbo+o5yXmIUJ4gn5538SO5S3gA=="}, "boom": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/boom/-/boom-4.3.1.tgz", "integrity": "sha1-T4owBctKfjiJ90kDD9JbluAdLjE=", "requires": {"hoek": "4.x.x"}}, "brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "brfs": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/brfs/-/brfs-1.6.1.tgz", "integrity": "sha512-OfZpABRQQf+Xsmju8XE9bDjs+uU4vLREGolP7bDgcpsI17QREyZ4Bl+2KLxxx1kCgA0fAIhKQBaBYh+PEcCqYQ==", "requires": {"quote-stream": "^1.0.1", "resolve": "^1.1.5", "static-module": "^2.2.0", "through2": "^2.0.0"}, "dependencies": {"duplexer2": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/duplexer2/-/duplexer2-0.1.4.tgz", "integrity": "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=", "requires": {"readable-stream": "^2.0.2"}}, "object-inspect": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.4.1.tgz", "integrity": "sha512-wqdhLpfCUbEsoEwl3FXwGyv8ief1k/1aUdIPCqVnupM6e8l63BEJdiF/0swtn04/8p05tG/T0FrpTlfwvljOdw=="}, "quote-stream": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/quote-stream/-/quote-stream-1.0.2.tgz", "integrity": "sha1-hJY/jJwmuULhU/7rU6rnRlK34LI=", "requires": {"buffer-equal": "0.0.1", "minimist": "^1.1.3", "through2": "^2.0.0"}}, "static-module": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/static-module/-/static-module-2.2.4.tgz", "integrity": "sha512-qlzhn8tYcfLsXK2RTWtkx1v/cqiPtS9eFy+UmQ9UnpEDYcwtgbceOybnKp5JncsOnLI/pyGeyzI9Bej9tv0xiA==", "requires": {"concat-stream": "~1.6.0", "convert-source-map": "^1.5.1", "duplexer2": "~0.1.4", "escodegen": "~1.9.0", "falafel": "^2.1.0", "has": "^1.0.1", "magic-string": "^0.22.4", "merge-source-map": "1.0.4", "object-inspect": "~1.4.0", "quote-stream": "~1.0.2", "readable-stream": "~2.3.3", "shallow-copy": "~0.0.1", "static-eval": "^2.0.0", "through2": "~2.0.3"}}}}, "brorand": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="}, "browser-pack": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/browser-pack/-/browser-pack-6.1.0.tgz", "integrity": "sha512-erYug8XoqzU3IfcU8fUgyHqyOXqIE4tUTTQ+7mqUjQlvnXkOO6OlT9c/ZoJVHYoAaqGxr09CN53G7XIsO4KtWA==", "requires": {"JSONStream": "^1.0.3", "combine-source-map": "~0.8.0", "defined": "^1.0.0", "safe-buffer": "^5.1.1", "through2": "^2.0.0", "umd": "^3.0.0"}}, "browser-resolve": {"version": "1.11.2", "resolved": "https://registry.npmjs.org/browser-resolve/-/browser-resolve-1.11.2.tgz", "integrity": "sha1-j/CbCixCFxihBRwmCzLkj0QpOM4=", "requires": {"resolve": "1.1.7"}, "dependencies": {"resolve": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz", "integrity": "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs="}}}, "browserify": {"version": "16.2.0", "resolved": "https://registry.npmjs.org/browserify/-/browserify-16.2.0.tgz", "integrity": "sha512-yotdAkp/ZbgDesHQBYU37zjc29JDH4iXT8hjzM1fdUVWogjARX0S1cKeX24Ci6zZ+jG+ADmCTRt6xvtmJnI+BQ==", "requires": {"JSONStream": "^1.0.3", "assert": "^1.4.0", "browser-pack": "^6.0.1", "browser-resolve": "^1.11.0", "browserify-zlib": "~0.2.0", "buffer": "^5.0.2", "cached-path-relative": "^1.0.0", "concat-stream": "^1.6.0", "console-browserify": "^1.1.0", "constants-browserify": "~1.0.0", "crypto-browserify": "^3.0.0", "defined": "^1.0.0", "deps-sort": "^2.0.0", "domain-browser": "^1.2.0", "duplexer2": "~0.1.2", "events": "^2.0.0", "glob": "^7.1.0", "has": "^1.0.0", "htmlescape": "^1.1.0", "https-browserify": "^1.0.0", "inherits": "~2.0.1", "insert-module-globals": "^7.0.0", "labeled-stream-splicer": "^2.0.0", "mkdirp": "^0.5.0", "module-deps": "^6.0.0", "os-browserify": "~0.3.0", "parents": "^1.0.1", "path-browserify": "~0.0.0", "process": "~0.11.0", "punycode": "^1.3.2", "querystring-es3": "~0.2.0", "read-only-stream": "^2.0.0", "readable-stream": "^2.0.2", "resolve": "^1.1.4", "shasum": "^1.0.0", "shell-quote": "^1.6.1", "stream-browserify": "^2.0.0", "stream-http": "^2.0.0", "string_decoder": "^1.1.1", "subarg": "^1.0.0", "syntax-error": "^1.1.1", "through2": "^2.0.0", "timers-browserify": "^1.0.1", "tty-browserify": "0.0.1", "url": "~0.11.0", "util": "~0.10.1", "vm-browserify": "^1.0.0", "xtend": "^4.0.0"}, "dependencies": {"duplexer2": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/duplexer2/-/duplexer2-0.1.4.tgz", "integrity": "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=", "requires": {"readable-stream": "^2.0.2"}}, "events": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/events/-/events-2.0.0.tgz", "integrity": "sha512-r/M5YkNg9zwI8QbSf7tsDWWJvO3PGwZXyG7GpFAxtMASnHL2eblFd7iHiGPtyGKKFPZ59S63NeX10Ws6WqGDcg=="}}}, "browserify-aes": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz", "integrity": "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==", "requires": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "browserify-cipher": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "integrity": "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==", "requires": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "browserify-des": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.1.tgz", "integrity": "sha512-zy0Cobe3hhgpiOM32Tj7KQ3Vl91m0njwsjzZQK1L+JDf11dzP9qIvjreVinsvXrgfjhStXwUWAEpB9D7Gwmayw==", "requires": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1"}}, "browserify-rsa": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.0.1.tgz", "integrity": "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=", "requires": {"bn.js": "^4.1.0", "randombytes": "^2.0.1"}}, "browserify-shim": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/browserify-shim/-/browserify-shim-2.0.10.tgz", "integrity": "sha1-dKDtW5t4SlooeQZROoltMfVKhLg=", "requires": {"through": "~2.3.4"}}, "browserify-sign": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.0.4.tgz", "integrity": "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=", "requires": {"bn.js": "^4.1.1", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.2", "elliptic": "^6.0.0", "inherits": "^2.0.1", "parse-asn1": "^5.0.0"}}, "browserify-transform-tools": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/browserify-transform-tools/-/browserify-transform-tools-1.7.0.tgz", "integrity": "sha1-g+J3Ih9jJZvtLn6yooOpcKUB9MQ=", "requires": {"falafel": "^2.0.0", "through": "^2.3.7"}}, "browserify-zlib": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "integrity": "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==", "requires": {"pako": "~1.0.5"}}, "buffer": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/buffer/-/buffer-5.1.0.tgz", "integrity": "sha512-YkIRgwsZwJWTnyQrsBTWefizHh+8GYj3kbL1BTiAQ/9pwpino0G7B2gp5tx/FUBqUlvtxV85KNR3mwfAtv15Yw==", "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}}, "buffer-equal": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/buffer-equal/-/buffer-equal-0.0.1.tgz", "integrity": "sha1-kbx0sR6kBbyRa8aqkI+q+ltKrEs="}, "buffer-from": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.0.0.tgz", "integrity": "sha1-TLiDLSNhJYmwQG6eKVbBfwb99TE="}, "buffer-xor": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="}, "builtin-modules": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/builtin-modules/-/builtin-modules-1.1.1.tgz", "integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8="}, "builtin-status-codes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="}, "builtins": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/builtins/-/builtins-0.0.7.tgz", "integrity": "sha1-NVIZzWzxjb58Acx/0tznZc/cVJo="}, "bytes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="}, "cache-base": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "cached-path-relative": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cached-path-relative/-/cached-path-relative-1.0.1.tgz", "integrity": "sha1-0JxLUoAKpMB44t2BqGmqyQ0uVOc="}, "caller-path": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/caller-path/-/caller-path-0.1.0.tgz", "integrity": "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=", "dev": true, "requires": {"callsites": "^0.2.0"}}, "callsite": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/callsite/-/callsite-1.0.0.tgz", "integrity": "sha1-KAOY5dZkvXQDi28JBRU+borxvCA="}, "callsites": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz", "integrity": "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=", "dev": true}, "camel-case": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz", "integrity": "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=", "requires": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "camelcase": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "integrity": "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk="}, "camelcase-keys": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz", "integrity": "sha1-MIvur/3ygRkFHvodkyITyRuPkuc=", "requires": {"camelcase": "^2.0.0", "map-obj": "^1.0.0"}, "dependencies": {"camelcase": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz", "integrity": "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8="}}}, "caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="}, "chalk": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "chardet": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/chardet/-/chardet-0.4.2.tgz", "integrity": "sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=", "dev": true}, "chokidar": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-2.0.3.tgz", "integrity": "sha512-zW8iXYZtXMx4kux/nuZVXjkLP+CyIK5Al5FHnj1OgTKGZfp4Oy6/ymtMSKFv3GD8DviEmUPmJg9eFdJ/JzudMg==", "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "fsevents": "^1.1.2", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "upath": "^1.0.0"}, "dependencies": {"anymatch": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "is-glob": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.0.tgz", "integrity": "sha1-lSHHaEXMJhCoUgPd8ICpWML/q8A=", "requires": {"is-extglob": "^2.1.1"}}}}, "chownr": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/chownr/-/chownr-1.0.1.tgz", "integrity": "sha1-4qdQQqlVGQi+vSW4Uj1fl2nXkYE="}, "cipher-base": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz", "integrity": "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==", "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "circular-json": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/circular-json/-/circular-json-0.3.3.tgz", "integrity": "sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A==", "dev": true}, "class-utils": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "clean-css": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-4.1.11.tgz", "integrity": "sha1-Ls3xRaujj1R0DybO/Q/z4D4SXWo=", "requires": {"source-map": "0.5.x"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "cli-cursor": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "dev": true, "requires": {"restore-cursor": "^2.0.0"}}, "cli-width": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-2.2.0.tgz", "integrity": "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=", "dev": true}, "cliui": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-3.2.0.tgz", "integrity": "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=", "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}}, "clone": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="}, "clone-buffer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-buffer/-/clone-buffer-1.0.0.tgz", "integrity": "sha1-4+JbIHrE5wGvch4staFnksrD3Fg="}, "clone-stats": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-0.0.1.tgz", "integrity": "sha1-uI+UqCzzi4eR1YBG6kAprYjKmdE="}, "cloneable-readable": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/cloneable-readable/-/cloneable-readable-1.1.2.tgz", "integrity": "sha512-Bq6+4t+lbM8vhTs/Bef5c5AdEMtapp/iFb6+s4/Hh9MVTt8OLKH7ZOOZSCT+Ys7hsHvqv0GuMPJ1lnQJVHvxpg==", "requires": {"inherits": "^2.0.1", "process-nextick-args": "^2.0.0", "readable-stream": "^2.3.5"}}, "co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "code-point-at": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="}, "collection-map": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/collection-map/-/collection-map-1.0.0.tgz", "integrity": "sha1-rqDwb40mx4DCt1SUOFVEsiVa8Yw=", "requires": {"arr-map": "^2.0.2", "for-own": "^1.0.0", "make-iterator": "^1.0.0"}}, "collection-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.1.tgz", "integrity": "sha512-mjGanIiwQJskCC18rPR6OmrZ6fm2Lc7PeGFYwCmy5J34wC6F1PzdGL6xeMfmgicfYcNLGuVFA3WzXtIDCQSZxQ==", "dev": true, "requires": {"color-name": "^1.1.1"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "color-support": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz", "integrity": "sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI="}, "colors": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/colors/-/colors-0.6.2.tgz", "integrity": "sha1-JCP+ZnisDF2uiFLl0OW+CMmXq8w="}, "combine-source-map": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/combine-source-map/-/combine-source-map-0.8.0.tgz", "integrity": "sha1-pY0N8ELBhvz4IqjoAV9UUNLXmos=", "requires": {"convert-source-map": "~1.1.0", "inline-source-map": "~0.6.0", "lodash.memoize": "~3.0.3", "source-map": "~0.5.3"}, "dependencies": {"convert-source-map": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.1.3.tgz", "integrity": "sha1-SCnId+n+SbMWHzvzZziI4gRpmGA="}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "combined-stream": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.6.tgz", "integrity": "sha1-cj599ugBrFYTETp+RFqbactjKBg=", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.1.0.tgz", "integrity": "sha1-0SG7roYNmZKj1Re6lvVliOR8Z4E="}, "commondir": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/commondir/-/commondir-0.0.1.tgz", "integrity": "sha1-ifAP3NUbUZxXhzP+xWPmptp/W+I="}, "component-emitter": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz", "integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY="}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "console-browserify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz", "integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "requires": {"date-now": "^0.1.4"}}, "console-control-strings": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4="}, "constants-browserify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="}, "contains-path": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/contains-path/-/contains-path-0.1.0.tgz", "integrity": "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=", "dev": true}, "convert-source-map": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.5.1.tgz", "integrity": "sha1-uCeAl7m8IpNl3lxiz1/K7YtVmeU="}, "copy-descriptor": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="}, "copy-props": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/copy-props/-/copy-props-2.0.1.tgz", "integrity": "sha1-Zl/DIEbKhKiYq6o8WUXn8kjMugA=", "requires": {"each-props": "^1.3.0", "is-plain-object": "^2.0.1"}}, "core-js": {"version": "2.5.5", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.5.5.tgz", "integrity": "sha1-sU3ek2xkDAV5prUMq8wTLdYSfjs="}, "core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "create-ecdh": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.1.tgz", "integrity": "sha512-iZvCCg8XqHQZ1ioNBTzXS/cQSkqkqcPs8xSX4upNB+DAk9Ht3uzQf2J32uAHNCne8LDmKr29AgZrEs4oIrwLuQ==", "requires": {"bn.js": "^4.1.0", "elliptic": "^6.0.0"}}, "create-hash": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz", "integrity": "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==", "requires": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "create-hmac": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz", "integrity": "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==", "requires": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "cross-spawn": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"lru-cache": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.2.tgz", "integrity": "sha512-wgeVXhrDwAWnIF/yZARsFnMBtdFXOg1b8RIrhilp+0iDYN4mdQcNZElDZ0e4B64BhaxeQ5zN7PMyvu7we1kPeQ==", "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}}}, "cryptiles": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-3.1.2.tgz", "integrity": "sha1-qJ+7Ig9c4l7FboxKqKT9e1sNKf4=", "requires": {"boom": "5.x.x"}, "dependencies": {"boom": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/boom/-/boom-5.2.0.tgz", "integrity": "sha512-Z5BTk6ZRe4tXXQlkqftmsAUANpXmuwlsF5Oov8ThoMbQRzdGTA1ngYRW160GexgOgjsFOKJz0LYhoNi+2AMBUw==", "requires": {"hoek": "4.x.x"}}}}, "crypto-browserify": {"version": "3.12.0", "resolved": "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "integrity": "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==", "requires": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}}, "cssauron": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/cssauron/-/cssauron-1.0.0.tgz", "integrity": "sha1-PJiRVBB1vpDuWcDCo9QA5P/+xC0=", "requires": {"through": "X.X.X"}}, "cssauron-glsl": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/cssauron-glsl/-/cssauron-glsl-1.0.0.tgz", "integrity": "sha1-mHNkFEK1K64IuRA/hUaIWvacdMw=", "requires": {"cssauron": "~1.0.0"}}, "currently-unhandled": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/currently-unhandled/-/currently-unhandled-0.4.1.tgz", "integrity": "sha1-mI3zP+qxke95mmE2nddsF635V+o=", "requires": {"array-find-index": "^1.0.1"}}, "d": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/d/-/d-1.0.0.tgz", "integrity": "sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8=", "requires": {"es5-ext": "^0.10.9"}}, "damerau-levenshtein": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.4.tgz", "integrity": "sha1-AxkcQyy27qFou3fzpV/9zLiXhRQ=", "dev": true}, "dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "requires": {"assert-plus": "^1.0.0"}}, "dat.gui": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/dat.gui/-/dat.gui-0.7.1.tgz", "integrity": "sha512-fTv1JQqjwA1Rtjy/i0ThdLgUOSWA3hOJrvNEl3XrfaMvCcFdV0h01bo3aGXknLx3okJ7AO9qqgVz6TbuRHxYAw=="}, "date-now": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz", "integrity": "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs="}, "date-time": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/date-time/-/date-time-2.1.0.tgz", "integrity": "sha512-/9+C44X7lot0IeiyfgJmETtRMhBidBYM2QFFIkGa0U1k+hSyY87Nw7PY3eDqpvCBm7I3WCSfPeZskW/YYq6m4g==", "requires": {"time-zone": "^1.0.0"}}, "dateformat": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/dateformat/-/dateformat-2.2.0.tgz", "integrity": "sha1-QGXiATz5+5Ft39gu+1Bq1MZ2kGI="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="}, "decode-uri-component": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="}, "decompress-response": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/decompress-response/-/decompress-response-3.3.0.tgz", "integrity": "sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=", "requires": {"mimic-response": "^1.0.0"}}, "deep-equal": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="}, "deep-extend": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.4.2.tgz", "integrity": "sha1-SLaZwn4zS/ifEIkr5DL25MfTSn8="}, "deep-is": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="}, "default-compare": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/default-compare/-/default-compare-1.0.0.tgz", "integrity": "sha512-QWfXlM0EkAbqOCbD/6HjdwT19j7WCkMyiRhWilc4H9/5h/RzTF9gv5LYh1+CmDV5d1rki6KAWLtQale0xt20eQ==", "requires": {"kind-of": "^5.0.2"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "default-resolution": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/default-resolution/-/default-resolution-2.0.0.tgz", "integrity": "sha1-vLgrqnKtebQmp2cy8aga1t8m1oQ="}, "define-properties": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.2.tgz", "integrity": "sha1-g6c/L+pWmJj7c3GTyPhzyvbUXJQ=", "requires": {"foreach": "^2.0.5", "object-keys": "^1.0.8"}}, "define-property": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "defined": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/defined/-/defined-1.0.0.tgz", "integrity": "sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM="}, "del": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/del/-/del-2.2.2.tgz", "integrity": "sha1-wSyYHQZ4RshLyvhiz/kw2Qf/0ag=", "dev": true, "requires": {"globby": "^5.0.0", "is-path-cwd": "^1.0.0", "is-path-in-cwd": "^1.0.0", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "rimraf": "^2.2.8"}, "dependencies": {"object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "delegates": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="}, "denodeify": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/denodeify/-/denodeify-1.2.1.tgz", "integrity": "sha1-OjYof1A05pnnV3kBBSwubJQlFjE="}, "depd": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "deps-sort": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/deps-sort/-/deps-sort-2.0.0.tgz", "integrity": "sha1-CRckkC6EZYJg65EHSMzNGvbiH7U=", "requires": {"JSONStream": "^1.0.3", "shasum": "^1.0.0", "subarg": "^1.0.0", "through2": "^2.0.0"}}, "derequire": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/derequire/-/derequire-0.8.0.tgz", "integrity": "sha1-wffx2izt5Ere3gRzePA/RE6cTA0=", "requires": {"esprima-fb": "^3001.1.0-dev-harmony-fb", "esrefactor": "~0.1.0", "estraverse": "~1.5.0"}, "dependencies": {"esprima-fb": {"version": "3001.1.0-dev-harmony-fb", "resolved": "https://registry.npmjs.org/esprima-fb/-/esprima-fb-3001.0001.0000-dev-harmony-fb.tgz", "integrity": "sha1-t303q8046gt3Qmu4vCkizmtCZBE="}, "estraverse": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-1.5.1.tgz", "integrity": "sha1-hno+jlip+EYYr7bC3bzZFrfLr3E="}}}, "des.js": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/des.js/-/des.js-1.0.0.tgz", "integrity": "sha1-wHTS4qpqipoH29YfmhXCzYPsjsw=", "requires": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "detect-file": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc="}, "detect-indent": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/detect-indent/-/detect-indent-4.0.0.tgz", "integrity": "sha1-920GQ1LN9Docts5hnE7jqUdd4gg=", "requires": {"repeating": "^2.0.0"}}, "detect-libc": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups="}, "detective": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/detective/-/detective-5.1.0.tgz", "integrity": "sha512-TFHMqfOvxlgrfVzTEkNBSh9SvSNX/HfF4OFI2QFGCyPm02EsyILqnUeb5P6q7JZ3SFNTBL5t2sePRgrN4epUWQ==", "requires": {"acorn-node": "^1.3.0", "defined": "^1.0.0", "minimist": "^1.1.1"}}, "diffie-hellman": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "integrity": "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==", "requires": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "doctrine": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz", "integrity": "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==", "dev": true, "requires": {"esutils": "^2.0.2"}}, "domain-browser": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz", "integrity": "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA=="}, "duplexer": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/duplexer/-/duplexer-0.1.1.tgz", "integrity": "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E="}, "duplexer2": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/duplexer2/-/duplexer2-0.0.2.tgz", "integrity": "sha1-xhTc9n4vsUmVqRcR5aYX6KYKMds=", "requires": {"readable-stream": "~1.1.9"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}}}, "duplexify": {"version": "3.5.4", "resolved": "https://registry.npmjs.org/duplexify/-/duplexify-3.5.4.tgz", "integrity": "sha1-S7RsF5bqvr7sTKmi5muAjLej2LQ=", "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "each-props": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/each-props/-/each-props-1.3.1.tgz", "integrity": "sha1-/BOPUeOid0KG1IWOAtbn3kYt4Vg=", "requires": {"is-plain-object": "^2.0.1", "object.defaults": "^1.1.0"}}, "ecc-jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz", "integrity": "sha1-D8c6ntXw1Tw4GTOYUj735UN3dQU=", "optional": true, "requires": {"jsbn": "~0.1.0"}}, "elliptic": {"version": "6.4.0", "resolved": "https://registry.npmjs.org/elliptic/-/elliptic-6.4.0.tgz", "integrity": "sha1-ysmvh2LIWDYYcAPI3+GT5eLq5d8=", "requires": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}}, "emoji-regex": {"version": "6.5.1", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.5.1.tgz", "integrity": "sha512-PAHp6TxrCy7MGMFidro8uikr+zlJJKJ/Q6mm2ExZ7HwkyR9lSVFfE3kt36qcwa24BQL7y0G9axycGjK1A/0uNQ==", "dev": true}, "encoding": {"version": "0.1.12", "resolved": "https://registry.npmjs.org/encoding/-/encoding-0.1.12.tgz", "integrity": "sha1-U4tm8+5izRq1HsMjgp0flIDHS+s=", "dev": true, "requires": {"iconv-lite": "~0.4.13"}}, "end-of-stream": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.1.tgz", "integrity": "sha1-7SljTRm6ukY7bOa4CjchPqtx7EM=", "requires": {"once": "^1.4.0"}}, "error-ex": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.1.tgz", "integrity": "sha1-+FWobOYa3E6GIcPNoh56dhLDqNw=", "requires": {"is-arrayish": "^0.2.1"}}, "es-abstract": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.11.0.tgz", "integrity": "sha1-zOh9UY8Elok7GjDNhGGDVTVIBoE=", "requires": {"es-to-primitive": "^1.1.1", "function-bind": "^1.1.1", "has": "^1.0.1", "is-callable": "^1.1.3", "is-regex": "^1.0.4"}}, "es-to-primitive": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.1.1.tgz", "integrity": "sha1-RTVSSKiJeQNLZ5Lhm7gfK3l13Q0=", "requires": {"is-callable": "^1.1.1", "is-date-object": "^1.0.1", "is-symbol": "^1.0.1"}}, "es5-ext": {"version": "0.10.42", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.42.tgz", "integrity": "sha512-AJxO1rmPe1bDEfSR6TJ/FgMFYuTBhR5R57KW58iCkYACMyFbrkqVyzXSurYoScDGvgyMpk7uRF/lPUPPTmsRSA==", "requires": {"es6-iterator": "~2.0.3", "es6-symbol": "~3.1.1", "next-tick": "1"}}, "es6-iterator": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "integrity": "sha1-p96IkUGgWpSwhUQDstCg+/qY87c=", "requires": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "es6-promise": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/es6-promise/-/es6-promise-3.3.1.tgz", "integrity": "sha1-oIzd6EzNvzTQJ6FFG8kdS80ophM="}, "es6-symbol": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.1.tgz", "integrity": "sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc=", "requires": {"d": "1", "es5-ext": "~0.10.14"}}, "es6-weak-map": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/es6-weak-map/-/es6-weak-map-2.0.2.tgz", "integrity": "sha1-XjqzIlH/0VOKH45f+hNXdy+S2W8=", "requires": {"d": "1", "es5-ext": "^0.10.14", "es6-iterator": "^2.0.1", "es6-symbol": "^3.1.1"}}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "escodegen": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/escodegen/-/escodegen-1.9.1.tgz", "integrity": "sha1-264X75bI5L7bE1b0UE+kzC98t+I=", "requires": {"esprima": "^3.1.3", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1", "source-map": "~0.6.1"}}, "escope": {"version": "0.0.16", "resolved": "https://registry.npmjs.org/escope/-/escope-0.0.16.tgz", "integrity": "sha1-QYx6CvynIdr+ZZGT/Zhig+dGU48=", "requires": {"estraverse": ">= 0.0.2"}}, "eslint": {"version": "4.19.1", "resolved": "https://registry.npmjs.org/eslint/-/eslint-4.19.1.tgz", "integrity": "sha512-bT3/1x1EbZB7phzYu7vCr1v3ONuzDtX8WjuM9c0iYxe+cq+pwcKEoQjl7zd3RpC6YOLgnSy3cTN58M2jcoPDIQ==", "dev": true, "requires": {"ajv": "^5.3.0", "babel-code-frame": "^6.22.0", "chalk": "^2.1.0", "concat-stream": "^1.6.0", "cross-spawn": "^5.1.0", "debug": "^3.1.0", "doctrine": "^2.1.0", "eslint-scope": "^3.7.1", "eslint-visitor-keys": "^1.0.0", "espree": "^3.5.4", "esquery": "^1.0.0", "esutils": "^2.0.2", "file-entry-cache": "^2.0.0", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.0.1", "ignore": "^3.3.3", "imurmurhash": "^0.1.4", "inquirer": "^3.0.6", "is-resolvable": "^1.0.0", "js-yaml": "^3.9.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.4", "minimatch": "^3.0.2", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "pluralize": "^7.0.0", "progress": "^2.0.0", "regexpp": "^1.0.1", "require-uncached": "^1.0.3", "semver": "^5.3.0", "strip-ansi": "^4.0.0", "strip-json-comments": "~2.0.1", "table": "4.0.2", "text-table": "~0.2.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.0.tgz", "integrity": "sha512-Wr/w0f4o9LuE7K53cD0qmbAMM+2XNLzR29vFn5hqko4sxGlUsyy363NvmyGIyk5tpe9cjTr9SJYbysEyPkRnFw==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "dev": true, "requires": {"ms": "2.0.0"}}, "globals": {"version": "11.4.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.4.0.tgz", "integrity": "sha512-Dyzmifil8n/TmSqYDEXbm+C8yitzJQqQIlJQLNRMwa+BOUJpRC19pyVeN12JAjt61xonvXjtff+hJruTRXn5HA==", "dev": true}, "semver": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.5.0.tgz", "integrity": "sha512-4SJ3dm0WAwWy/NVeioZh5AntkdJoWKxHxcmyP622fOkgHa4z3R0TdBJICINyaSDE6uNwVc8gZr+ZinwZAH4xIA==", "dev": true}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}, "supports-color": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.4.0.tgz", "integrity": "sha512-zjaXglF5nnWpsq470jSv6P9DwPvgLkuapYmfDm3JWOm0vkNTVF2tI4UrN2r6jH1qM/uc/WtxYY1hYoA2dOKj5w==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "eslint-config-airbnb": {"version": "16.1.0", "resolved": "https://registry.npmjs.org/eslint-config-airbnb/-/eslint-config-airbnb-16.1.0.tgz", "integrity": "sha512-zLyOhVWhzB/jwbz7IPSbkUuj7X2ox4PHXTcZkEmDqTvd0baJmJyuxlFPDlZOE/Y5bC+HQRaEkT3FoHo9wIdRiw==", "dev": true, "requires": {"eslint-config-airbnb-base": "^12.1.0"}}, "eslint-config-airbnb-base": {"version": "12.1.0", "resolved": "https://registry.npmjs.org/eslint-config-airbnb-base/-/eslint-config-airbnb-base-12.1.0.tgz", "integrity": "sha512-/vjm0Px5ZCpmJqnjIzcFb9TKZrKWz0gnuG/7Gfkt0Db1ELJR51xkZth+t14rYdqWgX836XbuxtArbIHlVhbLBA==", "dev": true, "requires": {"eslint-restricted-globals": "^0.1.1"}}, "eslint-import-resolver-node": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.2.tgz", "integrity": "sha512-sfmTqJfPSizWu4aymbPr4Iidp5yKm8yDkHp+Ir3YiTHiiDfxh69mOUsmiqW6RZ9zRXFaF64GtYmN7e+8GHBv6Q==", "dev": true, "requires": {"debug": "^2.6.9", "resolve": "^1.5.0"}}, "eslint-module-utils": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.2.0.tgz", "integrity": "sha1-snA2LNiLGkitMIl2zn+lTphBF0Y=", "dev": true, "requires": {"debug": "^2.6.8", "pkg-dir": "^1.0.0"}}, "eslint-plugin-import": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.11.0.tgz", "integrity": "sha1-Fa7qN6Z0mdhI6OmBgG1GJ7VQOBY=", "dev": true, "requires": {"contains-path": "^0.1.0", "debug": "^2.6.8", "doctrine": "1.5.0", "eslint-import-resolver-node": "^0.3.1", "eslint-module-utils": "^2.2.0", "has": "^1.0.1", "lodash": "^4.17.4", "minimatch": "^3.0.3", "read-pkg-up": "^2.0.0", "resolve": "^1.6.0"}, "dependencies": {"doctrine": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-1.5.0.tgz", "integrity": "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=", "dev": true, "requires": {"esutils": "^2.0.2", "isarray": "^1.0.0"}}, "find-up": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "load-json-file": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-2.0.0.tgz", "integrity": "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "strip-bom": "^3.0.0"}}, "path-type": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-2.0.0.tgz", "integrity": "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=", "dev": true, "requires": {"pify": "^2.0.0"}}, "read-pkg": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/read-pkg/-/read-pkg-2.0.0.tgz", "integrity": "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=", "dev": true, "requires": {"load-json-file": "^2.0.0", "normalize-package-data": "^2.3.2", "path-type": "^2.0.0"}}, "read-pkg-up": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-2.0.0.tgz", "integrity": "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^2.0.0"}}}}, "eslint-plugin-jsx-a11y": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.0.3.tgz", "integrity": "sha1-VFg9GuRCSDFi4EDhPMMYZUZRAOU=", "dev": true, "requires": {"aria-query": "^0.7.0", "array-includes": "^3.0.3", "ast-types-flow": "0.0.7", "axobject-query": "^0.1.0", "damerau-levenshtein": "^1.0.0", "emoji-regex": "^6.1.0", "jsx-ast-utils": "^2.0.0"}}, "eslint-plugin-react": {"version": "7.7.0", "resolved": "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.7.0.tgz", "integrity": "sha512-KC7Snr4YsWZD5flu6A5c0AcIZidzW3Exbqp7OT67OaD2AppJtlBr/GuPrW/vaQM/yfZotEvKAdrxrO+v8vwYJA==", "dev": true, "requires": {"doctrine": "^2.0.2", "has": "^1.0.1", "jsx-ast-utils": "^2.0.1", "prop-types": "^15.6.0"}}, "eslint-restricted-globals": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/eslint-restricted-globals/-/eslint-restricted-globals-0.1.1.tgz", "integrity": "sha1-NfDVy8ZMLj7WLpO0saevBbp+1Nc=", "dev": true}, "eslint-scope": {"version": "3.7.1", "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-3.7.1.tgz", "integrity": "sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "eslint-visitor-keys": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.0.0.tgz", "integrity": "sha512-qzm/XxIbxm/FHyH341ZrbnMUpe+5Bocte9xkmFMzPMjRaZMcXww+MpBptFvtU+79L362nqiLhekCxCxDPaUMBQ==", "dev": true}, "espree": {"version": "3.5.4", "resolved": "https://registry.npmjs.org/espree/-/espree-3.5.4.tgz", "integrity": "sha512-yAcIQxtmMiB/jL32dzEp2enBeidsB7xWPLNiw3IIkpVds1P+h7qF9YwJq1yUNzp2OKXgAprs4F61ih66UsoD1A==", "dev": true, "requires": {"acorn": "^5.5.0", "acorn-jsx": "^3.0.0"}}, "esprima": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/esprima/-/esprima-3.1.3.tgz", "integrity": "sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM="}, "esquery": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.0.1.tgz", "integrity": "sha512-SmiyZ5zIWH9VM+SRUReLS5Q8a7GxtRdxEBVZpm98rJM7Sb+A9DVCndXfkeFUd3byderg+EbDkfnevfCwynWaNA==", "dev": true, "requires": {"estraverse": "^4.0.0"}}, "esrecurse": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz", "integrity": "sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ==", "dev": true, "requires": {"estraverse": "^4.1.0"}}, "esrefactor": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/esrefactor/-/esrefactor-0.1.0.tgz", "integrity": "sha1-0UJ5WigjOauB6Ta1t6IbEb8ZexM=", "requires": {"escope": "~0.0.13", "esprima": "~1.0.2", "estraverse": "~0.0.4"}, "dependencies": {"esprima": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/esprima/-/esprima-1.0.4.tgz", "integrity": "sha1-n1V+CPw7TSbs6d00+Pv0drYlha0="}, "estraverse": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-0.0.4.tgz", "integrity": "sha1-AaCTLf7ldGhKWYr1pnw7+bZCjbI="}}}, "estraverse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM="}, "estree-walker": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-0.3.1.tgz", "integrity": "sha1-5rGlHPcpJSTnI3wxLl/mZgwc4ao="}, "esutils": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs="}, "events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ="}, "evp_bytestokey": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "integrity": "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==", "requires": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "expand-brackets": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "expand-range": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz", "integrity": "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=", "requires": {"fill-range": "^2.1.0"}, "dependencies": {"fill-range": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-2.2.3.tgz", "integrity": "sha1-ULd9/X5Gm8dJJHCWNpn+eoSFpyM=", "requires": {"is-number": "^2.1.0", "isobject": "^2.0.0", "randomatic": "^1.1.3", "repeat-element": "^1.1.2", "repeat-string": "^1.5.2"}}, "is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "requires": {"kind-of": "^3.0.2"}}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "expand-template": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/expand-template/-/expand-template-1.1.0.tgz", "integrity": "sha512-kkjwkMqj0h4w/sb32ERCDxCQkREMCAgS39DscDnSwDsbxnwwM1BTZySdC3Bn1lhY7vL08n9GoO/fVTynjDgRyQ=="}, "expand-tilde": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=", "requires": {"homedir-polyfill": "^1.0.1"}}, "extend": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.1.tgz", "integrity": "sha1-p1Xqe8Gt/MWjHOfnYtuq3F5jZEQ="}, "extend-shallow": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "requires": {"is-plain-object": "^2.0.4"}}}}, "external-editor": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/external-editor/-/external-editor-2.2.0.tgz", "integrity": "sha512-bSn6gvGxKt+b7+6TKEv1ZycHleA7aHhRHyAqJyp5pbUFuYYNIzpZnQDk7AsYckyWdEnTeAnay0aCy2aV6iTk9A==", "dev": true, "requires": {"chardet": "^0.4.0", "iconv-lite": "^0.4.17", "tmp": "^0.0.33"}}, "extglob": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="}, "falafel": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/falafel/-/falafel-2.1.0.tgz", "integrity": "sha1-lrsXdh2rqU9G0AFzizzt86Z/4Gw=", "requires": {"acorn": "^5.0.0", "foreach": "^2.0.5", "isarray": "0.0.1", "object-keys": "^1.0.6"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}}}, "fancy-log": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/fancy-log/-/fancy-log-1.3.2.tgz", "integrity": "sha1-9BEl49hPLn2JpD0G2VjI94vha+E=", "requires": {"ansi-gray": "^0.1.1", "color-support": "^1.1.3", "time-stamp": "^1.0.0"}}, "fast-deep-equal": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz", "integrity": "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ="}, "fast-json-stable-stringify": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="}, "fbjs": {"version": "0.8.16", "resolved": "https://registry.npmjs.org/fbjs/-/fbjs-0.8.16.tgz", "integrity": "sha1-XmdDL1UNxBtXK/VYR7ispk5TN9s=", "dev": true, "requires": {"core-js": "^1.0.0", "isomorphic-fetch": "^2.1.1", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^0.7.9"}, "dependencies": {"core-js": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz", "integrity": "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=", "dev": true}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}}}, "figures": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/figures/-/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "file-entry-cache": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz", "integrity": "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=", "dev": true, "requires": {"flat-cache": "^1.2.1", "object-assign": "^4.0.1"}, "dependencies": {"object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}}}, "filename-regex": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.1.tgz", "integrity": "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY="}, "fill-range": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "find-up": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz", "integrity": "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=", "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "findup": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/findup/-/findup-0.1.5.tgz", "integrity": "sha1-itkpozk7rGJ5V6fl3kYjsGsOLOs=", "requires": {"colors": "~0.6.0-1", "commander": "~2.1.0"}}, "findup-sync": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-2.0.0.tgz", "integrity": "sha1-kyaxSIwi0aYIhlCoaQGy2akKLLw=", "requires": {"detect-file": "^1.0.0", "is-glob": "^3.1.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}}, "fined": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fined/-/fined-1.1.0.tgz", "integrity": "sha1-s33IRLdqL15wgeiE98CuNE8VNHY=", "requires": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}}, "flagged-respawn": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/flagged-respawn/-/flagged-respawn-1.0.0.tgz", "integrity": "sha1-Tnmumy6zi/hrO7Vr8+ClaqX8q9c="}, "flat-cache": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-1.3.0.tgz", "integrity": "sha1-0wMLMrOBVPTjt+nHCfSQ9++XxIE=", "dev": true, "requires": {"circular-json": "^0.3.1", "del": "^2.0.2", "graceful-fs": "^4.1.2", "write": "^0.2.1"}}, "flush-write-stream": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.0.3.tgz", "integrity": "sha512-calZMC10u0FMUqoiunI2AiGIIUtUIvifNwkHhNupZH4cbNnW1Itkoh/Nf5HFYmDrwWPjrUxpkZT0KhuCq0jmGw==", "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "for-each": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/for-each/-/for-each-0.3.2.tgz", "integrity": "sha1-LEBFC5NI6X8oEyJZO6lnBLmr1NQ=", "requires": {"is-function": "~1.0.0"}}, "for-in": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="}, "for-own": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/for-own/-/for-own-1.0.0.tgz", "integrity": "sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs=", "requires": {"for-in": "^1.0.1"}}, "foreach": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/foreach/-/foreach-2.0.5.tgz", "integrity": "sha1-C+4AUBiusmDQo6865ljdATbsG5k="}, "forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="}, "form-data": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.3.2.tgz", "integrity": "sha1-SXBJi+YEwgwAXU9cI67NIda0kJk=", "requires": {"asynckit": "^0.4.0", "combined-stream": "1.0.6", "mime-types": "^2.1.12"}}, "fragment-cache": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "requires": {"map-cache": "^0.2.2"}}, "from2": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "fs-extra": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-5.0.0.tgz", "integrity": "sha512-66Pm4RYbjzdyeuqudYqhFiNBbCIuI9kgRqLPSHIlXHidW8NIQtVdkM1yeZ4lXwuhbTETv3EUGMNHAAw6hiundQ==", "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs-mkdirp-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs-mkdirp-stream/-/fs-mkdirp-stream-1.0.0.tgz", "integrity": "sha1-C3gV/DIBxqaeFNuYzgmMFpNSWes=", "requires": {"graceful-fs": "^4.1.11", "through2": "^2.0.3"}}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-1.1.3.tgz", "integrity": "sha512-WIr7iDkdmdbxu/Gh6eKEZJL6KPE74/5MEsf2whTOFNxbIoIixogroLdKYqB6FDav4Wavh/lZdzzd3b2KxIXC5Q==", "optional": true, "requires": {"nan": "^2.3.0", "node-pre-gyp": "^0.6.39"}, "dependencies": {"abbrev": {"version": "1.1.0", "bundled": true, "optional": true}, "ajv": {"version": "4.11.8", "bundled": true, "optional": true, "requires": {"co": "4.6.0", "json-stable-stringify": "1.0.1"}}, "ansi-regex": {"version": "2.1.1", "bundled": true}, "aproba": {"version": "1.1.1", "bundled": true, "optional": true}, "are-we-there-yet": {"version": "1.1.4", "bundled": true, "optional": true, "requires": {"delegates": "1.0.0", "readable-stream": "2.2.9"}}, "asn1": {"version": "0.2.3", "bundled": true, "optional": true}, "assert-plus": {"version": "0.2.0", "bundled": true, "optional": true}, "asynckit": {"version": "0.4.0", "bundled": true, "optional": true}, "aws-sign2": {"version": "0.6.0", "bundled": true, "optional": true}, "aws4": {"version": "1.6.0", "bundled": true, "optional": true}, "balanced-match": {"version": "0.4.2", "bundled": true}, "bcrypt-pbkdf": {"version": "1.0.1", "bundled": true, "optional": true, "requires": {"tweetnacl": "0.14.5"}}, "block-stream": {"version": "0.0.9", "bundled": true, "optional": true, "requires": {"inherits": "2.0.3"}}, "boom": {"version": "2.10.1", "bundled": true, "requires": {"hoek": "2.16.3"}}, "brace-expansion": {"version": "1.1.7", "bundled": true, "requires": {"balanced-match": "0.4.2", "concat-map": "0.0.1"}}, "buffer-shims": {"version": "1.0.0", "bundled": true, "optional": true}, "caseless": {"version": "0.12.0", "bundled": true, "optional": true}, "co": {"version": "4.6.0", "bundled": true, "optional": true}, "code-point-at": {"version": "1.1.0", "bundled": true, "optional": true}, "combined-stream": {"version": "1.0.5", "bundled": true, "optional": true, "requires": {"delayed-stream": "1.0.0"}}, "concat-map": {"version": "0.0.1", "bundled": true}, "console-control-strings": {"version": "1.1.0", "bundled": true, "optional": true}, "core-util-is": {"version": "1.0.2", "bundled": true, "optional": true}, "cryptiles": {"version": "2.0.5", "bundled": true, "optional": true, "requires": {"boom": "2.10.1"}}, "dashdash": {"version": "1.14.1", "bundled": true, "optional": true, "requires": {"assert-plus": "1.0.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "optional": true}}}, "debug": {"version": "2.6.8", "bundled": true, "optional": true, "requires": {"ms": "2.0.0"}}, "deep-extend": {"version": "0.4.2", "bundled": true, "optional": true}, "delayed-stream": {"version": "1.0.0", "bundled": true, "optional": true}, "delegates": {"version": "1.0.0", "bundled": true, "optional": true}, "detect-libc": {"version": "1.0.2", "bundled": true, "optional": true}, "ecc-jsbn": {"version": "0.1.1", "bundled": true, "optional": true, "requires": {"jsbn": "0.1.1"}}, "extend": {"version": "3.0.1", "bundled": true, "optional": true}, "extsprintf": {"version": "1.0.2", "bundled": true, "optional": true}, "forever-agent": {"version": "0.6.1", "bundled": true, "optional": true}, "form-data": {"version": "2.1.4", "bundled": true, "optional": true, "requires": {"asynckit": "0.4.0", "combined-stream": "1.0.5", "mime-types": "2.1.15"}}, "fs.realpath": {"version": "1.0.0", "bundled": true}, "fstream": {"version": "1.0.11", "bundled": true, "requires": {"graceful-fs": "4.1.11", "inherits": "2.0.3", "mkdirp": "0.5.1", "rimraf": "2.6.1"}}, "fstream-ignore": {"version": "1.0.5", "bundled": true, "optional": true, "requires": {"fstream": "1.0.11", "inherits": "2.0.3", "minimatch": "3.0.4"}}, "gauge": {"version": "2.7.4", "bundled": true, "optional": true, "requires": {"aproba": "1.1.1", "console-control-strings": "1.1.0", "has-unicode": "2.0.1", "object-assign": "4.1.1", "signal-exit": "3.0.2", "string-width": "1.0.2", "strip-ansi": "3.0.1", "wide-align": "1.1.2"}}, "getpass": {"version": "0.1.7", "bundled": true, "optional": true, "requires": {"assert-plus": "1.0.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "optional": true}}}, "glob": {"version": "7.1.2", "bundled": true, "requires": {"fs.realpath": "1.0.0", "inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}, "graceful-fs": {"version": "4.1.11", "bundled": true}, "har-schema": {"version": "1.0.5", "bundled": true, "optional": true}, "har-validator": {"version": "4.2.1", "bundled": true, "optional": true, "requires": {"ajv": "4.11.8", "har-schema": "1.0.5"}}, "has-unicode": {"version": "2.0.1", "bundled": true, "optional": true}, "hawk": {"version": "3.1.3", "bundled": true, "optional": true, "requires": {"boom": "2.10.1", "cryptiles": "2.0.5", "hoek": "2.16.3", "sntp": "1.0.9"}}, "hoek": {"version": "2.16.3", "bundled": true}, "http-signature": {"version": "1.1.1", "bundled": true, "optional": true, "requires": {"assert-plus": "0.2.0", "jsprim": "1.4.0", "sshpk": "1.13.0"}}, "inflight": {"version": "1.0.6", "bundled": true, "requires": {"once": "1.4.0", "wrappy": "1.0.2"}}, "inherits": {"version": "2.0.3", "bundled": true}, "ini": {"version": "1.3.4", "bundled": true, "optional": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "optional": true, "requires": {"number-is-nan": "1.0.1"}}, "is-typedarray": {"version": "1.0.0", "bundled": true, "optional": true}, "isarray": {"version": "1.0.0", "bundled": true, "optional": true}, "isstream": {"version": "0.1.2", "bundled": true, "optional": true}, "jodid25519": {"version": "1.0.2", "bundled": true, "optional": true, "requires": {"jsbn": "0.1.1"}}, "jsbn": {"version": "0.1.1", "bundled": true, "optional": true}, "json-schema": {"version": "0.2.3", "bundled": true, "optional": true}, "json-stable-stringify": {"version": "1.0.1", "bundled": true, "optional": true, "requires": {"jsonify": "0.0.0"}}, "json-stringify-safe": {"version": "5.0.1", "bundled": true, "optional": true}, "jsonify": {"version": "0.0.0", "bundled": true, "optional": true}, "jsprim": {"version": "1.4.0", "bundled": true, "optional": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.0.2", "json-schema": "0.2.3", "verror": "1.3.6"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "optional": true}}}, "mime-db": {"version": "1.27.0", "bundled": true, "optional": true}, "mime-types": {"version": "2.1.15", "bundled": true, "optional": true, "requires": {"mime-db": "1.27.0"}}, "minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true}, "mkdirp": {"version": "0.5.1", "bundled": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "bundled": true, "optional": true}, "node-pre-gyp": {"version": "0.6.39", "bundled": true, "optional": true, "requires": {"detect-libc": "1.0.2", "hawk": "3.1.3", "mkdirp": "0.5.1", "nopt": "4.0.1", "npmlog": "4.1.0", "rc": "1.2.1", "request": "2.81.0", "rimraf": "2.6.1", "semver": "5.3.0", "tar": "2.2.1", "tar-pack": "3.4.0"}}, "nopt": {"version": "4.0.1", "bundled": true, "optional": true, "requires": {"abbrev": "1.1.0", "osenv": "0.1.4"}}, "npmlog": {"version": "4.1.0", "bundled": true, "optional": true, "requires": {"are-we-there-yet": "1.1.4", "console-control-strings": "1.1.0", "gauge": "2.7.4", "set-blocking": "2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "optional": true}, "oauth-sign": {"version": "0.8.2", "bundled": true, "optional": true}, "object-assign": {"version": "4.1.1", "bundled": true, "optional": true}, "once": {"version": "1.4.0", "bundled": true, "requires": {"wrappy": "1.0.2"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "optional": true}, "os-tmpdir": {"version": "1.0.2", "bundled": true, "optional": true}, "osenv": {"version": "0.1.4", "bundled": true, "optional": true, "requires": {"os-homedir": "1.0.2", "os-tmpdir": "1.0.2"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true}, "performance-now": {"version": "0.2.0", "bundled": true, "optional": true}, "process-nextick-args": {"version": "1.0.7", "bundled": true, "optional": true}, "punycode": {"version": "1.4.1", "bundled": true, "optional": true}, "qs": {"version": "6.4.0", "bundled": true, "optional": true}, "rc": {"version": "1.2.1", "bundled": true, "optional": true, "requires": {"deep-extend": "0.4.2", "ini": "1.3.4", "minimist": "1.2.0", "strip-json-comments": "2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "bundled": true, "optional": true}}}, "readable-stream": {"version": "2.2.9", "bundled": true, "optional": true, "requires": {"buffer-shims": "1.0.0", "core-util-is": "1.0.2", "inherits": "2.0.3", "isarray": "1.0.0", "process-nextick-args": "1.0.7", "string_decoder": "1.0.1", "util-deprecate": "1.0.2"}}, "request": {"version": "2.81.0", "bundled": true, "optional": true, "requires": {"aws-sign2": "0.6.0", "aws4": "1.6.0", "caseless": "0.12.0", "combined-stream": "1.0.5", "extend": "3.0.1", "forever-agent": "0.6.1", "form-data": "2.1.4", "har-validator": "4.2.1", "hawk": "3.1.3", "http-signature": "1.1.1", "is-typedarray": "1.0.0", "isstream": "0.1.2", "json-stringify-safe": "5.0.1", "mime-types": "2.1.15", "oauth-sign": "0.8.2", "performance-now": "0.2.0", "qs": "6.4.0", "safe-buffer": "5.0.1", "stringstream": "0.0.5", "tough-cookie": "2.3.2", "tunnel-agent": "0.6.0", "uuid": "3.0.1"}}, "rimraf": {"version": "2.6.1", "bundled": true, "requires": {"glob": "7.1.2"}}, "safe-buffer": {"version": "5.0.1", "bundled": true, "optional": true}, "semver": {"version": "5.3.0", "bundled": true, "optional": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "optional": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "optional": true}, "sntp": {"version": "1.0.9", "bundled": true, "optional": true, "requires": {"hoek": "2.16.3"}}, "sshpk": {"version": "1.13.0", "bundled": true, "optional": true, "requires": {"asn1": "0.2.3", "assert-plus": "1.0.0", "bcrypt-pbkdf": "1.0.1", "dashdash": "1.14.1", "ecc-jsbn": "0.1.1", "getpass": "0.1.7", "jodid25519": "1.0.2", "jsbn": "0.1.1", "tweetnacl": "0.14.5"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true, "optional": true}}}, "string-width": {"version": "1.0.2", "bundled": true, "optional": true, "requires": {"code-point-at": "1.1.0", "is-fullwidth-code-point": "1.0.0", "strip-ansi": "3.0.1"}}, "string_decoder": {"version": "1.0.1", "bundled": true, "optional": true, "requires": {"safe-buffer": "5.0.1"}}, "stringstream": {"version": "0.0.5", "bundled": true, "optional": true}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "2.1.1"}}, "strip-json-comments": {"version": "2.0.1", "bundled": true, "optional": true}, "tar": {"version": "2.2.1", "bundled": true, "optional": true, "requires": {"block-stream": "0.0.9", "fstream": "1.0.11", "inherits": "2.0.3"}}, "tar-pack": {"version": "3.4.0", "bundled": true, "optional": true, "requires": {"debug": "2.6.8", "fstream": "1.0.11", "fstream-ignore": "1.0.5", "once": "1.4.0", "readable-stream": "2.2.9", "rimraf": "2.6.1", "tar": "2.2.1", "uid-number": "0.0.6"}}, "tough-cookie": {"version": "2.3.2", "bundled": true, "optional": true, "requires": {"punycode": "1.4.1"}}, "tunnel-agent": {"version": "0.6.0", "bundled": true, "optional": true, "requires": {"safe-buffer": "5.0.1"}}, "tweetnacl": {"version": "0.14.5", "bundled": true, "optional": true}, "uid-number": {"version": "0.0.6", "bundled": true, "optional": true}, "util-deprecate": {"version": "1.0.2", "bundled": true, "optional": true}, "uuid": {"version": "3.0.1", "bundled": true, "optional": true}, "verror": {"version": "1.3.6", "bundled": true, "optional": true, "requires": {"extsprintf": "1.0.2"}}, "wide-align": {"version": "1.1.2", "bundled": true, "optional": true, "requires": {"string-width": "1.0.2"}}, "wrappy": {"version": "1.0.2", "bundled": true}}}, "fstream": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/fstream/-/fstream-1.0.11.tgz", "integrity": "sha1-XB+x8RdHcRTwYyoOtLcbPLD9MXE=", "requires": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true}, "gauge": {"version": "2.7.4", "resolved": "https://registry.npmjs.org/gauge/-/gauge-2.7.4.tgz", "integrity": "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=", "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}, "dependencies": {"object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}}}, "get-caller-file": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.2.tgz", "integrity": "sha1-9wLmMSfn4jHBYKgMFVSstw1QR+U="}, "get-own-enumerable-property-symbols": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-2.0.1.tgz", "integrity": "sha512-TtY/sbOemiMKPRUDDanGCSgBYe7Mf0vbRsWnBZ+9yghpZ1MvcpSpuZFjHdEeY/LZjZy0vdLjS77L6HosisFiug==", "dev": true}, "get-stdin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz", "integrity": "sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4="}, "get-value": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="}, "getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "requires": {"assert-plus": "^1.0.0"}}, "github-from-package": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz", "integrity": "sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4="}, "gl": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/gl/-/gl-4.0.4.tgz", "integrity": "sha512-J/l/QSjbW9UHQjWqmCtXe2H1Gt8ednfC1tofmwABg9z+36DMNBs8W3i/2NgLg4Mdg+bED/DKnU1B3HIpSSidFQ==", "requires": {"bindings": "^1.2.1", "bit-twiddle": "^1.0.2", "glsl-tokenizer": "^2.0.2", "nan": "^2.6.2", "node-gyp": "^3.6.2", "prebuild-install": "^2.1.1"}}, "gl-mat4": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/gl-mat4/-/gl-mat4-1.1.4.tgz", "integrity": "sha1-HolbVYkuVqiWhnq9g30483oXgIY="}, "glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha1-wZyd+aAocC1nhhI4SmVSQExjbRU=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-base": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz", "integrity": "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=", "requires": {"glob-parent": "^2.0.0", "is-glob": "^2.0.0"}, "dependencies": {"glob-parent": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=", "requires": {"is-glob": "^2.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}}}, "glob-parent": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "glob-stream": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/glob-stream/-/glob-stream-6.1.0.tgz", "integrity": "sha1-cEXJlBOz65SIjYOrRtC0BMx73eQ=", "requires": {"extend": "^3.0.0", "glob": "^7.1.1", "glob-parent": "^3.1.0", "is-negated-glob": "^1.0.0", "ordered-read-streams": "^1.0.0", "pumpify": "^1.3.5", "readable-stream": "^2.1.5", "remove-trailing-separator": "^1.0.1", "to-absolute-glob": "^2.0.0", "unique-stream": "^2.0.2"}}, "glob-watcher": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/glob-watcher/-/glob-watcher-5.0.1.tgz", "integrity": "sha512-fK92r2COMC199WCyGUblrZKhjra3cyVMDiypDdqg1vsSDmexnbYivK1kNR4QItiNXLKmGlqan469ks67RtNa2g==", "requires": {"async-done": "^1.2.0", "chokidar": "^2.0.0", "just-debounce": "^1.0.0", "object.defaults": "^1.1.0"}}, "global-modules": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=", "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}, "global-prefix": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=", "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}}, "globals": {"version": "9.18.0", "resolved": "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz", "integrity": "sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo="}, "globby": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/globby/-/globby-5.0.0.tgz", "integrity": "sha1-69hGZ8oNuzMLmbz8aOrCvFQ3Dg0=", "dev": true, "requires": {"array-union": "^1.0.1", "arrify": "^1.0.0", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "dependencies": {"object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}}}, "glogg": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/glogg/-/glogg-1.0.1.tgz", "integrity": "sha1-3PdY5EeJzD89MsHzVio2duajSBA=", "requires": {"sparkles": "^1.0.0"}}, "glsl-camera-ray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-camera-ray/-/glsl-camera-ray-1.0.0.tgz", "integrity": "sha1-5a3+EjwCvIGMfds+QJwnOBepVe0=", "requires": {"glsl-look-at": "^1.0.0"}}, "glsl-combine-chamfer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-combine-chamfer/-/glsl-combine-chamfer-1.0.0.tgz", "integrity": "sha1-6bg1XaLtrmI7lrhanw3kA05lZ2Y="}, "glsl-combine-smooth": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-combine-smooth/-/glsl-combine-smooth-1.0.0.tgz", "integrity": "sha1-PIdBNwnsBvvFzefSMGZM5JMy2eM="}, "glsl-deparser": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-deparser/-/glsl-deparser-1.0.0.tgz", "integrity": "sha1-Xn6zY+xUr5KRbM/8gu0D6b/jPgw=", "requires": {"cssauron-glsl": "X.X.X", "through": "~1.1.2"}, "dependencies": {"through": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/through/-/through-1.1.2.tgz", "integrity": "sha1-NEpUJaN3MxTKfg62US+6+vdsC/4="}}}, "glsl-inject-defines": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/glsl-inject-defines/-/glsl-inject-defines-1.0.3.tgz", "integrity": "sha1-3RqswsF/yyvT/DJBHGYz0Ne2D9Q=", "requires": {"glsl-token-inject-block": "^1.0.0", "glsl-token-string": "^1.0.1", "glsl-tokenizer": "^2.0.2"}}, "glsl-look-at": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-look-at/-/glsl-look-at-1.0.0.tgz", "integrity": "sha1-YzuxeVQcL9Xo9ZHW0t2dRR4on3s="}, "glsl-min-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-min-stream/-/glsl-min-stream-1.0.0.tgz", "integrity": "sha1-tr/mNTpCsVsfrDVCkMs1UeBYKBI=", "requires": {"cssauron-glsl": "X.X.X", "shortest": "0.0.0", "through": "~1.1.2"}, "dependencies": {"through": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/through/-/through-1.1.2.tgz", "integrity": "sha1-NEpUJaN3MxTKfg62US+6+vdsC/4="}}}, "glsl-noise": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/glsl-noise/-/glsl-noise-0.0.0.tgz", "integrity": "sha1-NndF86MzgsDu7Ey1S36Zz8HXZws="}, "glsl-parser": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glsl-parser/-/glsl-parser-2.0.0.tgz", "integrity": "sha1-MKanCu30mlp3TiyoXttjvzSoaaQ=", "requires": {"glsl-tokenizer": "^2.0.0", "through": "2.3.4", "through2": "^0.6.3"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}, "through": {"version": "2.3.4", "resolved": "https://registry.npmjs.org/through/-/through-2.3.4.tgz", "integrity": "sha1-SV5A6Nio6uvHwnXqiMK4/BTFZFU="}, "through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "glsl-quad": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-quad/-/glsl-quad-1.0.0.tgz", "integrity": "sha1-yd4AJqXhGmfXaxw/US7m4l3TD/4="}, "glsl-raytrace": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-raytrace/-/glsl-raytrace-1.0.0.tgz", "integrity": "sha1-X9AiGAo21pUi4mRNMA/yCWaweN4="}, "glsl-resolve": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/glsl-resolve/-/glsl-resolve-0.0.1.tgz", "integrity": "sha1-iUvvc5ENeSyBtRQxgANdCnivdtM=", "requires": {"resolve": "^0.6.1", "xtend": "^2.1.2"}, "dependencies": {"resolve": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/resolve/-/resolve-0.6.3.tgz", "integrity": "sha1-3ZV5gufnNt699TtYpN2RdUV13UY="}, "xtend": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/xtend/-/xtend-2.2.0.tgz", "integrity": "sha1-7vax8ZjByN6vrYsXZaBNrUoBxak="}}}, "glsl-sdf-box": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-sdf-box/-/glsl-sdf-box-1.0.0.tgz", "integrity": "sha1-cZaY4YYmHFuucsYlmbM9fvzkMFY="}, "glsl-sdf-normal": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-sdf-normal/-/glsl-sdf-normal-1.0.0.tgz", "integrity": "sha1-Dj6QYXqSn2ieyr/3PdZuKCEUOu8="}, "glsl-sdf-ops": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/glsl-sdf-ops/-/glsl-sdf-ops-0.0.3.tgz", "integrity": "sha1-anxAkclA2tTGXOY8hdix6CHZ3iE="}, "glsl-specular-phong": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-specular-phong/-/glsl-specular-phong-1.0.0.tgz", "integrity": "sha1-oEUICORinuZMy0YLfuBCWpe/YgQ="}, "glsl-square-frame": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/glsl-square-frame/-/glsl-square-frame-1.0.1.tgz", "integrity": "sha1-YPA7nBMJrNpDW+hB14GU2JZthTM="}, "glsl-token-assignments": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/glsl-token-assignments/-/glsl-token-assignments-2.0.2.tgz", "integrity": "sha1-pdgqt4SZwuimuDy2lJXm5mXOAZ8="}, "glsl-token-defines": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-token-defines/-/glsl-token-defines-1.0.0.tgz", "integrity": "sha1-y4kqqVmTYjFyhHDU90AySJaX+p0=", "requires": {"glsl-tokenizer": "^2.0.0"}}, "glsl-token-depth": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/glsl-token-depth/-/glsl-token-depth-1.1.2.tgz", "integrity": "sha1-I8XjDuK9JViEtKKLyFC495HpXYQ="}, "glsl-token-descope": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/glsl-token-descope/-/glsl-token-descope-1.0.2.tgz", "integrity": "sha1-D8kKsyYYa4L1l7LnfcniHvzTIHY=", "requires": {"glsl-token-assignments": "^2.0.0", "glsl-token-depth": "^1.1.0", "glsl-token-properties": "^1.0.0", "glsl-token-scope": "^1.1.0"}}, "glsl-token-inject-block": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/glsl-token-inject-block/-/glsl-token-inject-block-1.1.0.tgz", "integrity": "sha1-4QFfWYDBCRgkraomJfHf3ovQADQ="}, "glsl-token-properties": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/glsl-token-properties/-/glsl-token-properties-1.0.1.tgz", "integrity": "sha1-SD3D2Dnw1LXGFx0VkfJJvlPCip4="}, "glsl-token-scope": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/glsl-token-scope/-/glsl-token-scope-1.1.2.tgz", "integrity": "sha1-oXKOeN8kRE+cuT/RjvD3VQOmQ7E="}, "glsl-token-string": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/glsl-token-string/-/glsl-token-string-1.0.1.tgz", "integrity": "sha1-WUQdL4V958NEnJRWZgIezjWOSOw="}, "glsl-token-whitespace-trim": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-token-whitespace-trim/-/glsl-token-whitespace-trim-1.0.0.tgz", "integrity": "sha1-RtHf6Yx1vX1QTAXX0RsbPpzJOxA="}, "glsl-tokenizer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/glsl-tokenizer/-/glsl-tokenizer-2.1.2.tgz", "integrity": "sha1-cgMHUi4DxXrzXABVGVDEpw7y37k=", "requires": {"through2": "^0.6.3"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}, "through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "glsl-turntable-camera": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glsl-turntable-camera/-/glsl-turntable-camera-1.0.0.tgz", "integrity": "sha1-qwdL6TrD9gN7mSg5ob7LujJFJG8=", "requires": {"glsl-camera-ray": "^1.0.0", "glsl-square-frame": "^1.0.1"}}, "glslify": {"version": "6.1.1", "resolved": "https://registry.npmjs.org/glslify/-/glslify-6.1.1.tgz", "integrity": "sha1-9e4+efMUu9uO3tt6J4MNs/LffkA=", "requires": {"bl": "^1.0.0", "concat-stream": "^1.5.2", "duplexify": "^3.4.5", "falafel": "^2.0.0", "from2": "^2.3.0", "glsl-resolve": "0.0.1", "glsl-token-whitespace-trim": "^1.0.0", "glslify-bundle": "^5.0.0", "glslify-deps": "^1.2.5", "minimist": "^1.2.0", "resolve": "^1.1.5", "stack-trace": "0.0.9", "static-eval": "^2.0.0", "tape": "^4.6.0", "through2": "^2.0.1", "xtend": "^4.0.0"}}, "glslify-bundle": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/glslify-bundle/-/glslify-bundle-5.0.0.tgz", "integrity": "sha1-AlKtoe+d8wtmAAbguyH9EwtIbkI=", "requires": {"glsl-inject-defines": "^1.0.1", "glsl-token-defines": "^1.0.0", "glsl-token-depth": "^1.1.1", "glsl-token-descope": "^1.0.2", "glsl-token-scope": "^1.1.1", "glsl-token-string": "^1.0.1", "glsl-token-whitespace-trim": "^1.0.0", "glsl-tokenizer": "^2.0.2", "murmurhash-js": "^1.0.0", "shallow-copy": "0.0.1"}}, "glslify-deps": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/glslify-deps/-/glslify-deps-1.3.0.tgz", "integrity": "sha1-CyI0yOqePT/X9rPLfwOuWea1Glk=", "requires": {"events": "^1.0.2", "findup": "^0.1.5", "glsl-resolve": "0.0.1", "glsl-tokenizer": "^2.0.0", "graceful-fs": "^4.1.2", "inherits": "^2.0.1", "map-limit": "0.0.1", "resolve": "^1.0.0"}}, "glslify-hex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/glslify-hex/-/glslify-hex-2.1.1.tgz", "integrity": "sha1-+YRx/b3pWNxoT3wFb7LgdEwQ9r0=", "dev": true}, "glslify-import": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/glslify-import/-/glslify-import-3.1.0.tgz", "integrity": "sha512-3n7gI7mDcpkpGMFrbCEFhbyUU70sNrih5OrU6oN6EcgTsTVjrbHpQVjjG78Ta2kzZGEqQX0OD2BgFgglm2DZPg==", "requires": {"glsl-resolve": "0.0.1", "glsl-token-string": "^1.0.1", "glsl-tokenizer": "^2.0.2"}}, "graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg="}, "graceful-readlink": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/graceful-readlink/-/graceful-readlink-1.0.1.tgz", "integrity": "sha1-TK+tdrxi8C+gObL5Tpo906ORpyU="}, "gulp": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/gulp/-/gulp-4.0.0.tgz", "integrity": "sha1-lXZsYB2t5Kd+0+eyttwDiBtZY2Y=", "requires": {"glob-watcher": "^5.0.0", "gulp-cli": "^2.0.0", "undertaker": "^1.0.0", "vinyl-fs": "^3.0.0"}, "dependencies": {"camelcase": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz", "integrity": "sha1-MvxLn82vhF/N9+c7uXysImHwqwo="}, "gulp-cli": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/gulp-cli/-/gulp-cli-2.0.1.tgz", "integrity": "sha512-RxujJJdN8/O6IW2nPugl7YazhmrIEjmiVfPKrWt68r71UCaLKS71Hp0gpKT+F6qOUFtr7KqtifDKaAJPRVvMYQ==", "requires": {"ansi-colors": "^1.0.1", "archy": "^1.0.0", "array-sort": "^1.0.0", "color-support": "^1.1.3", "concat-stream": "^1.6.0", "copy-props": "^2.0.1", "fancy-log": "^1.3.2", "gulplog": "^1.0.0", "interpret": "^1.1.0", "isobject": "^3.0.1", "liftoff": "^2.5.0", "matchdep": "^2.0.0", "mute-stdout": "^1.0.0", "pretty-hrtime": "^1.0.0", "replace-homedir": "^1.0.0", "semver-greatest-satisfied-range": "^1.1.0", "v8flags": "^3.0.1", "yargs": "^7.1.0"}}, "yargs": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-7.1.0.tgz", "integrity": "sha1-a6MY6xaWFyf10oT46gA+jWFU0Mg=", "requires": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.0"}}}}, "gulp-brfs": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/gulp-brfs/-/gulp-brfs-0.1.0.tgz", "integrity": "sha1-N8exeB1/XKFxZjuAVjwdDm2qLIY=", "dev": true, "requires": {"bl": "^0.9.3", "brfs": "^1.2.0", "gulp-util": "^3.0.1", "through2": "^0.6.3"}, "dependencies": {"bl": {"version": "0.9.5", "resolved": "https://registry.npmjs.org/bl/-/bl-0.9.5.tgz", "integrity": "sha1-wGt5evCF6gC8Unr8jvzxHeIjIFQ=", "dev": true, "requires": {"readable-stream": "~1.0.26"}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "gulp-browserify": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/gulp-browserify/-/gulp-browserify-0.5.1.tgz", "integrity": "sha1-ggEIrCVUqVStuL4X0jlYsMBL4IM=", "requires": {"browserify": "3.x", "browserify-shim": "~2.0.10", "gulp-util": "~2.2.5", "readable-stream": "~1.1.10", "through2": "~0.4.0"}, "dependencies": {"JSONStream": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/JSONStream/-/JSONStream-0.7.4.tgz", "integrity": "sha1-c0KQ5BUR7qfCz+FR+/mlY6l7l4Y=", "requires": {"jsonparse": "0.0.5", "through": ">=2.2.7 <3"}}, "acorn": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-2.7.0.tgz", "integrity": "sha1-q259nYhqrKiwhbwzEreaGYQz8Oc="}, "ansi-regex": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-0.2.1.tgz", "integrity": "sha1-DY6UaWej2BQ/k+JOKYUl/BsiNfk="}, "ansi-styles": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.1.0.tgz", "integrity": "sha1-6uy/Zs1waIJ2Cy9GkVgrj1XXp94="}, "assert": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/assert/-/assert-1.1.2.tgz", "integrity": "sha1-raoExGu1jG3R8pTaPrJuYijrbkQ=", "requires": {"util": "0.10.3"}}, "base64-js": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.8.tgz", "integrity": "sha1-EQHpVE9KdrG8OybUUsqW16NeeXg="}, "browser-pack": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/browser-pack/-/browser-pack-2.0.1.tgz", "integrity": "sha1-XRxSf1bFgmd0EcTbKhKGSP9r8VA=", "requires": {"JSONStream": "~0.6.4", "combine-source-map": "~0.3.0", "through": "~2.3.4"}, "dependencies": {"JSONStream": {"version": "0.6.4", "resolved": "https://registry.npmjs.org/JSONStream/-/JSONStream-0.6.4.tgz", "integrity": "sha1-SyyAY/j1Enh7I3X37p22kgj6Lcs=", "requires": {"jsonparse": "0.0.5", "through": "~2.2.7"}, "dependencies": {"through": {"version": "2.2.7", "resolved": "https://registry.npmjs.org/through/-/through-2.2.7.tgz", "integrity": "sha1-bo4hIAGR1OtqmfbwEN9Gqhxusr0="}}}}}, "browser-resolve": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/browser-resolve/-/browser-resolve-1.2.4.tgz", "integrity": "sha1-Wa54IKgpVezTL1+3xGisIcRyOAY=", "requires": {"resolve": "0.6.3"}}, "browserify": {"version": "3.46.1", "resolved": "https://registry.npmjs.org/browserify/-/browserify-3.46.1.tgz", "integrity": "sha1-LC5Kfy9AgXjnjCI7W1ezfCGFrY4=", "requires": {"JSONStream": "~0.7.1", "assert": "~1.1.0", "browser-pack": "~2.0.0", "browser-resolve": "~1.2.1", "browserify-zlib": "~0.1.2", "buffer": "~2.1.4", "builtins": "~0.0.3", "commondir": "0.0.1", "concat-stream": "~1.4.1", "console-browserify": "~1.0.1", "constants-browserify": "~0.0.1", "crypto-browserify": "~1.0.9", "deep-equal": "~0.1.0", "defined": "~0.0.0", "deps-sort": "~0.1.1", "derequire": "~0.8.0", "domain-browser": "~1.1.0", "duplexer": "~0.1.1", "events": "~1.0.0", "glob": "~3.2.8", "http-browserify": "~1.3.1", "https-browserify": "~0.0.0", "inherits": "~2.0.1", "insert-module-globals": "~6.0.0", "module-deps": "~2.0.0", "os-browserify": "~0.1.1", "parents": "~0.0.1", "path-browserify": "~0.0.0", "process": "^0.7.0", "punycode": "~1.2.3", "querystring-es3": "0.2.0", "resolve": "~0.6.1", "shallow-copy": "0.0.1", "shell-quote": "~0.0.1", "stream-browserify": "~0.1.0", "stream-combiner": "~0.0.2", "string_decoder": "~0.0.0", "subarg": "0.0.1", "syntax-error": "~1.1.0", "through2": "~0.4.1", "timers-browserify": "~1.0.1", "tty-browserify": "~0.0.0", "umd": "~2.0.0", "url": "~0.10.1", "util": "~0.10.1", "vm-browserify": "~0.0.1", "xtend": "^3.0.0"}, "dependencies": {"string_decoder": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.0.1.tgz", "integrity": "sha1-9UctCo0WUOyCN1LSTm/WJ7Ob8UE="}}}, "browserify-zlib": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.1.4.tgz", "integrity": "sha1-uzX4pRn2AOD6a4SFJByXnQFB+y0=", "requires": {"pako": "~0.2.0"}}, "buffer": {"version": "2.1.13", "resolved": "https://registry.npmjs.org/buffer/-/buffer-2.1.13.tgz", "integrity": "sha1-yIg46/efMLi0pwd4hHC+qKYsI1U=", "requires": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}}, "chalk": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha1-Zjs6ZItotV0EaQ1JFnqoN4WPIXQ=", "requires": {"ansi-styles": "^1.1.0", "escape-string-regexp": "^1.0.0", "has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "supports-color": "^0.2.0"}}, "combine-source-map": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/combine-source-map/-/combine-source-map-0.3.0.tgz", "integrity": "sha1-2edPWT2c1DgHMSy12EbUUe+qnrc=", "requires": {"convert-source-map": "~0.3.0", "inline-source-map": "~0.3.0", "source-map": "~0.1.31"}}, "concat-stream": {"version": "1.4.11", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.4.11.tgz", "integrity": "sha512-X3JMh8+4je3U1cQpG87+f9lXHDrqcb2MVLg9L7o8b1UZ0DzhRrUpdn65ttzu10PpJPPI3MQNkis+oha6TSA9Mw==", "requires": {"inherits": "~2.0.1", "readable-stream": "~1.1.9", "typedarray": "~0.0.5"}}, "console-browserify": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.0.3.tgz", "integrity": "sha1-04mNLDqTEC82QZf4h0tPkrUoao4="}, "constants-browserify": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/constants-browserify/-/constants-browserify-0.0.1.tgz", "integrity": "sha1-kld9tSe6bEzwpFaNhLwDH0QeIfI="}, "convert-source-map": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-0.3.5.tgz", "integrity": "sha1-8dgClQr33SYxof6+BZZVDIarMZA="}, "crypto-browserify": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-1.0.9.tgz", "integrity": "sha1-zFRJaF37hesRyYKKzHy4erW7/MA="}, "dateformat": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/dateformat/-/dateformat-1.0.12.tgz", "integrity": "sha1-nxJLZ1lMk3/3BpMuSmQsyo27/uk=", "requires": {"get-stdin": "^4.0.1", "meow": "^3.3.0"}}, "deep-equal": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.1.2.tgz", "integrity": "sha1-skbCuApXCkfBG+HZvRBw7IeLh84="}, "defined": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/defined/-/defined-0.0.0.tgz", "integrity": "sha1-817qfXBekzuvE7LwOz+D2SFAOz4="}, "deps-sort": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/deps-sort/-/deps-sort-0.1.2.tgz", "integrity": "sha1-2qL7YUoXyWN9gB4vVTOa43DzYRo=", "requires": {"JSONStream": "~0.6.4", "minimist": "~0.0.1", "through": "~2.3.4"}, "dependencies": {"JSONStream": {"version": "0.6.4", "resolved": "https://registry.npmjs.org/JSONStream/-/JSONStream-0.6.4.tgz", "integrity": "sha1-SyyAY/j1Enh7I3X37p22kgj6Lcs=", "requires": {"jsonparse": "0.0.5", "through": "~2.2.7"}, "dependencies": {"through": {"version": "2.2.7", "resolved": "https://registry.npmjs.org/through/-/through-2.2.7.tgz", "integrity": "sha1-bo4hIAGR1OtqmfbwEN9Gqhxusr0="}}}, "minimist": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz", "integrity": "sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8="}}}, "detective": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/detective/-/detective-3.1.0.tgz", "integrity": "sha1-d3gkRKt1K4jKG+Lp0KA5Xx2iXu0=", "requires": {"escodegen": "~1.1.0", "esprima-fb": "3001.1.0-dev-harmony-fb"}}, "domain-browser": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/domain-browser/-/domain-browser-1.1.7.tgz", "integrity": "sha1-hnqksJP6oF8d4IwG9NeyH9+GmLw="}, "escodegen": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/escodegen/-/escodegen-1.1.0.tgz", "integrity": "sha1-xmOSP24gqtSNDA+knzHG1PSTYM8=", "requires": {"esprima": "~1.0.4", "estraverse": "~1.5.0", "esutils": "~1.0.0", "source-map": "~0.1.30"}, "dependencies": {"esprima": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/esprima/-/esprima-1.0.4.tgz", "integrity": "sha1-n1V+CPw7TSbs6d00+Pv0drYlha0="}}}, "esprima-fb": {"version": "3001.1.0-dev-harmony-fb", "resolved": "https://registry.npmjs.org/esprima-fb/-/esprima-fb-3001.0001.0000-dev-harmony-fb.tgz", "integrity": "sha1-t303q8046gt3Qmu4vCkizmtCZBE="}, "estraverse": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-1.5.1.tgz", "integrity": "sha1-hno+jlip+EYYr7bC3bzZFrfLr3E="}, "esutils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/esutils/-/esutils-1.0.0.tgz", "integrity": "sha1-gVHTWOIMisx/t0XnRywAJf5JZXA="}, "events": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/events/-/events-1.0.2.tgz", "integrity": "sha1-dYSdz+k9EPsFfDAFWv29UdBqjiQ="}, "glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz", "integrity": "sha1-Spc/Y1uRkPcV0QmH1cAP0oFevj0=", "requires": {"inherits": "2", "minimatch": "0.3"}}, "gulp-util": {"version": "2.2.20", "resolved": "https://registry.npmjs.org/gulp-util/-/gulp-util-2.2.20.tgz", "integrity": "sha1-1xRuVyiRC9jwR6awseVJvCLb1kw=", "requires": {"chalk": "^0.5.0", "dateformat": "^1.0.7-1.2.3", "lodash._reinterpolate": "^2.4.1", "lodash.template": "^2.4.1", "minimist": "^0.2.0", "multipipe": "^0.1.0", "through2": "^0.5.0", "vinyl": "^0.2.1"}, "dependencies": {"readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "through2": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/through2/-/through2-0.5.1.tgz", "integrity": "sha1-390BLrnHAOIyP9M084rGIqs3Lac=", "requires": {"readable-stream": "~1.0.17", "xtend": "~3.0.0"}}}}, "has-ansi": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-0.1.0.tgz", "integrity": "sha1-hPJlqujA5qiKEtcCKJS3VoiUxi4=", "requires": {"ansi-regex": "^0.2.0"}}, "https-browserify": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/https-browserify/-/https-browserify-0.0.1.tgz", "integrity": "sha1-P5E2XKvmC3ftDruiS0VOPgnZWoI="}, "inline-source-map": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/inline-source-map/-/inline-source-map-0.3.1.tgz", "integrity": "sha1-pSi1FOaJ/OkNswiehw2S9Sestes=", "requires": {"source-map": "~0.3.0"}, "dependencies": {"source-map": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.3.0.tgz", "integrity": "sha1-hYb7mloAXltQHiHNGLbyG0V60fk=", "requires": {"amdefine": ">=0.0.4"}}}}, "insert-module-globals": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/insert-module-globals/-/insert-module-globals-6.0.0.tgz", "integrity": "sha1-7orrne4WgZ4zqhRYilWIJK8MFdw=", "requires": {"JSONStream": "~0.7.1", "concat-stream": "~1.4.1", "lexical-scope": "~1.1.0", "process": "~0.6.0", "through": "~2.3.4", "xtend": "^3.0.0"}, "dependencies": {"process": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/process/-/process-0.6.0.tgz", "integrity": "sha1-fdm+gP+q7dTLYo8YJ/HLq23AkY8="}}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "jsonparse": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/jsonparse/-/jsonparse-0.0.5.tgz", "integrity": "sha1-MwVCrT8KZUZlt3jz6y2an6UHrGQ="}, "lexical-scope": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/lexical-scope/-/lexical-scope-1.1.1.tgz", "integrity": "sha1-3rrBBnQ18TWdkPz9npS8su5Hsr8=", "requires": {"astw": "^2.0.0"}}, "lodash._reinterpolate": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-2.4.1.tgz", "integrity": "sha1-TxInqlqHEfxjL1sHofRgequLMiI="}, "lodash.escape": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.escape/-/lodash.escape-2.4.1.tgz", "integrity": "sha1-LOEsXghNsKV92l5dHu659dF1o7Q=", "requires": {"lodash._escapehtmlchar": "~2.4.1", "lodash._reunescapedhtml": "~2.4.1", "lodash.keys": "~2.4.1"}}, "lodash.keys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.4.1.tgz", "integrity": "sha1-SN6kbfj/djKxDXBrissmWR4rNyc=", "requires": {"lodash._isnative": "~2.4.1", "lodash._shimkeys": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "lodash.template": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.template/-/lodash.template-2.4.1.tgz", "integrity": "sha1-nmEQB+32KRKal0qzxIuBez4c8g0=", "requires": {"lodash._escapestringchar": "~2.4.1", "lodash._reinterpolate": "~2.4.1", "lodash.defaults": "~2.4.1", "lodash.escape": "~2.4.1", "lodash.keys": "~2.4.1", "lodash.templatesettings": "~2.4.1", "lodash.values": "~2.4.1"}}, "lodash.templatesettings": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-2.4.1.tgz", "integrity": "sha1-6nbHXRHrhtTb6JqDiTu4YZKaxpk=", "requires": {"lodash._reinterpolate": "~2.4.1", "lodash.escape": "~2.4.1"}}, "minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha1-J12O2qxPG7MyZHIInnlJyDlGmd0=", "requires": {"lru-cache": "2", "sigmund": "~1.0.0"}}, "minimist": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.2.0.tgz", "integrity": "sha1-Tf/lJdriuGTGbC4jxicdev3s784="}, "module-deps": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/module-deps/-/module-deps-2.0.6.tgz", "integrity": "sha1-uZkyHHOsM1gPAHEsDzB1/cpCVj8=", "requires": {"JSONStream": "~0.7.1", "browser-resolve": "~1.2.4", "concat-stream": "~1.4.5", "detective": "~3.1.0", "duplexer2": "0.0.2", "inherits": "~2.0.1", "minimist": "~0.0.9", "parents": "0.0.2", "readable-stream": "^1.0.27-1", "resolve": "~0.6.3", "stream-combiner": "~0.1.0", "through2": "~0.4.1"}, "dependencies": {"minimist": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz", "integrity": "sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8="}, "parents": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/parents/-/parents-0.0.2.tgz", "integrity": "sha1-ZxR4JuSX1AdZqvW6TJllm2A00wI="}, "stream-combiner": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/stream-combiner/-/stream-combiner-0.1.0.tgz", "integrity": "sha1-DcOJo8ID+PTVY2j5Xd5S65Jptb4=", "requires": {"duplexer": "~0.1.1", "through": "~2.3.4"}}}}, "object-keys": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-0.4.0.tgz", "integrity": "sha1-KKaq50KN0sOpLz2V8hM13SBOAzY="}, "os-browserify": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.1.2.tgz", "integrity": "sha1-ScoCk+CxlZCl9d4Qx/JlphfY/lQ="}, "pako": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/pako/-/pako-0.2.9.tgz", "integrity": "sha1-8/dSL073gjSNqBYbrZ7P1Rv4OnU="}, "parents": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/parents/-/parents-0.0.3.tgz", "integrity": "sha1-+iEvAk2fpjGNu2tM5nbIvkk7nEM=", "requires": {"path-platform": "^0.0.1"}}, "path-platform": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/path-platform/-/path-platform-0.0.1.tgz", "integrity": "sha1-tVhdfDxGPYmqAGDYZhHPGv1hfio="}, "process": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/process/-/process-0.7.0.tgz", "integrity": "sha1-xSIIFho0rfOBI0SuhdPmFQRpOJ0="}, "punycode": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.2.4.tgz", "integrity": "sha1-VACKyXKux0F13vnLpt9/qdORh0A="}, "querystring-es3": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.0.tgz", "integrity": "sha1-w2WgimnEQ6zP6zqd6rNePwq6pHY="}, "readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "resolve": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/resolve/-/resolve-0.6.3.tgz", "integrity": "sha1-3ZV5gufnNt699TtYpN2RdUV13UY="}, "shell-quote": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/shell-quote/-/shell-quote-0.0.1.tgz", "integrity": "sha1-GkEZbzwDM8SCMjWT1ohuzxU92YY="}, "source-map": {"version": "0.1.43", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "requires": {"amdefine": ">=0.0.4"}}, "stream-browserify": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/stream-browserify/-/stream-browserify-0.1.3.tgz", "integrity": "sha1-lc8bNpdy4nra9GNSJlFSaJxsS+k=", "requires": {"inherits": "~2.0.1", "process": "~0.5.1"}, "dependencies": {"process": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/process/-/process-0.5.2.tgz", "integrity": "sha1-FjjYqONML0QKkduVq5rrZ3/Bhc8="}}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}, "strip-ansi": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "integrity": "sha1-JfSOoiynkYfzF0pNuHWTR7sSYiA=", "requires": {"ansi-regex": "^0.2.1"}}, "subarg": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/subarg/-/subarg-0.0.1.tgz", "integrity": "sha1-PVawfaz7xFu7Y/dnK0O2PkY2jjo=", "requires": {"minimist": "~0.0.7"}, "dependencies": {"minimist": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz", "integrity": "sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8="}}}, "supports-color": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-0.2.0.tgz", "integrity": "sha1-2S3iaU6z9nMjlz1649i1W0wiGQo="}, "syntax-error": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/syntax-error/-/syntax-error-1.1.6.tgz", "integrity": "sha1-tFSXBtOGzBwdx8JCPxhXm2yt5xA=", "requires": {"acorn": "^2.7.0"}}, "through2": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/through2/-/through2-0.4.2.tgz", "integrity": "sha1-2/WGYDEVHsg1K7bE22SiKSqEC5s=", "requires": {"readable-stream": "~1.0.17", "xtend": "~2.1.1"}, "dependencies": {"readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "xtend": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-2.1.2.tgz", "integrity": "sha1-bv7MKk2tjmlixJAbM3znuoe10os=", "requires": {"object-keys": "~0.4.0"}}}}, "timers-browserify": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/timers-browserify/-/timers-browserify-1.0.3.tgz", "integrity": "sha1-/7pwycEu7ZFv1nMY5imsbzIpVVE=", "requires": {"process": "~0.5.1"}, "dependencies": {"process": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/process/-/process-0.5.2.tgz", "integrity": "sha1-FjjYqONML0QKkduVq5rrZ3/Bhc8="}}}, "umd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/umd/-/umd-2.0.0.tgz", "integrity": "sha1-dJaDsNUUcorg4bYZX1d0r8CtT48=", "requires": {"rfile": "~1.0.0", "ruglify": "~1.0.0", "through": "~2.3.4", "uglify-js": "~2.4.0"}}, "url": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/url/-/url-0.10.3.tgz", "integrity": "sha1-Ah5NnHcF8hu/N9A861h2dAJ3TGQ=", "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="}}}, "vinyl": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-0.2.3.tgz", "integrity": "sha1-vKk4IJWC7FpJrVOKAPofEl5RMlI=", "requires": {"clone-stats": "~0.0.1"}}, "vm-browserify": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/vm-browserify/-/vm-browserify-0.0.4.tgz", "integrity": "sha1-XX6kW7755Kb/ZflUOOCofDV9WnM=", "requires": {"indexof": "0.0.1"}}, "xtend": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/xtend/-/xtend-3.0.0.tgz", "integrity": "sha1-XM50B7r2Qsunvs2laBEcST9ZZlo="}}}, "gulp-debug": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/gulp-debug/-/gulp-debug-4.0.0.tgz", "integrity": "sha512-cn/GhMD2nVZCVxAl5vWao4/dcoZ8wUJ8w3oqTvQaGDmC1vT7swNOEbhQTWJp+/otKePT64aENcqAQXDcdj5H1g==", "dev": true, "requires": {"chalk": "^2.3.0", "fancy-log": "^1.3.2", "plur": "^3.0.0", "stringify-object": "^3.0.0", "through2": "^2.0.0", "tildify": "^1.1.2"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.1.tgz", "integrity": "sha512-ObN6h1v2fTJSmUXoS3nMQ92LbDK9be4TV+6G+omQlGJFdcUX5heKi1LZ1YnRMIgwTLEj3E24bT6tYni50rlCfQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "irregular-plurals": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/irregular-plurals/-/irregular-plurals-2.0.0.tgz", "integrity": "sha512-Y75zBYLkh0lJ9qxeHlMjQ7bSbyiSqNW/UOPWDmzC7cXskL1hekSITh1Oc6JV0XCWWZ9DE8VYSB71xocLk3gmGw==", "dev": true}, "plur": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/plur/-/plur-3.0.1.tgz", "integrity": "sha512-lJl0ojUynAM1BZn58Pas2WT/TXeC1+bS+UqShl0x9+49AtOn7DixRXVzaC8qrDOIxNDmepKnLuMTH7NQmkX0PA==", "dev": true, "requires": {"irregular-plurals": "^2.0.0"}}, "supports-color": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.4.0.tgz", "integrity": "sha512-zjaXglF5nnWpsq470jSv6P9DwPvgLkuapYmfDm3JWOm0vkNTVF2tI4UrN2r6jH1qM/uc/WtxYY1hYoA2dOKj5w==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "gulp-each": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/gulp-each/-/gulp-each-0.5.0.tgz", "integrity": "sha1-z4F5TksXvCJY2wv5n3lqNJIznV8=", "requires": {"read-vinyl-file-stream": "^2.0.2"}}, "gulp-flatmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/gulp-flatmap/-/gulp-flatmap-1.0.2.tgz", "integrity": "sha512-xm+Ax2vPL/xiMBqLFI++wUyPtncm3b55ztGHewmRcoG/sYb0OUTatjSacOud3fee77rnk+jOgnDEHhwBtMHgFA==", "requires": {"plugin-error": "0.1.2", "through2": "2.0.3"}}, "gulp-glslify": {"version": "git+https://github.com/yuichiroharai/gulp-glslify.git#253b9e6bb309170a50cbfb3b2cc0ad72fd720109", "from": "git+https://github.com/yuichiroharai/gulp-glslify.git", "dev": true, "requires": {"glslify": "^5.0.2", "glslify-hex": "^2.0.1", "glslify-import": "^3.0.0", "gulp-util": "^3.0.7", "through2": "^2.0.1"}, "dependencies": {"glslify": {"version": "5.1.0", "resolved": "http://registry.npmjs.org/glslify/-/glslify-5.1.0.tgz", "integrity": "sha1-GTW7tWMhWv0+Y2tugvSLyhpqAio=", "dev": true, "requires": {"bl": "^1.0.0", "glsl-resolve": "0.0.1", "glslify-bundle": "^5.0.0", "glslify-deps": "^1.2.5", "minimist": "^1.2.0", "resolve": "^1.1.5", "static-module": "^1.1.2", "through2": "^0.6.3", "xtend": "^4.0.0"}, "dependencies": {"through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "requires": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}}}, "gulp-tap": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/gulp-tap/-/gulp-tap-1.0.1.tgz", "integrity": "sha1-5nESThJZtM6iGe0cqXt/WFwzRpA=", "requires": {"through2": "^2.0.3"}}, "gulp-util": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/gulp-util/-/gulp-util-3.0.8.tgz", "integrity": "sha1-AFTh50RQLifATBh8PsxQXdVLu08=", "requires": {"array-differ": "^1.0.0", "array-uniq": "^1.0.2", "beeper": "^1.0.0", "chalk": "^1.0.0", "dateformat": "^2.0.0", "fancy-log": "^1.1.0", "gulplog": "^1.0.0", "has-gulplog": "^0.1.0", "lodash._reescape": "^3.0.0", "lodash._reevaluate": "^3.0.0", "lodash._reinterpolate": "^3.0.0", "lodash.template": "^3.0.0", "minimist": "^1.1.0", "multipipe": "^0.1.2", "object-assign": "^3.0.0", "replace-ext": "0.0.1", "through2": "^2.0.0", "vinyl": "^0.5.0"}, "dependencies": {"vinyl": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-0.5.3.tgz", "integrity": "sha1-sEVbOPxeDPMNQyUTLkYZcMIJHN4=", "requires": {"clone": "^1.0.0", "clone-stats": "^0.0.1", "replace-ext": "0.0.1"}}}}, "gulp-watch": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/gulp-watch/-/gulp-watch-5.0.0.tgz", "integrity": "sha512-q+HLppxXd11z9ndqql4Z0sd5xOAesJjycl0PRaq6ImK7b1BqBRL37YvxEE8ngUdIfpfHa0O9OCoovoggcFpCaQ==", "requires": {"anymatch": "^1.3.0", "chokidar": "^2.0.0", "glob-parent": "^3.0.1", "gulp-util": "^3.0.7", "object-assign": "^4.1.0", "path-is-absolute": "^1.0.1", "readable-stream": "^2.2.2", "slash": "^1.0.0", "vinyl": "^2.1.0", "vinyl-file": "^2.0.0"}, "dependencies": {"clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="}, "clone-stats": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz", "integrity": "sha1-s3gt/4u1R04Yuba/D9/ngvh3doA="}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "replace-ext": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.0.tgz", "integrity": "sha1-3mMSg3P8v3w8z6TeWkgMRaZ5WOs="}, "vinyl": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-2.1.0.tgz", "integrity": "sha1-Ah+cLPlR1rk5lDyJ617lrdT9kkw=", "requires": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}}}}, "gulplog": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/gulplog/-/gulplog-1.0.0.tgz", "integrity": "sha1-4oxNRdBey77YGDY86PnFkmIp/+U=", "requires": {"glogg": "^1.0.0"}}, "har-schema": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="}, "har-validator": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-5.0.3.tgz", "integrity": "sha1-ukAsJmGU8VlW7xXg/PJCmT9qff0=", "requires": {"ajv": "^5.1.0", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/has/-/has-1.0.1.tgz", "integrity": "sha1-hGFzP1OLCDfJNh45qauelwTcLyg=", "requires": {"function-bind": "^1.0.2"}}, "has-ansi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "requires": {"ansi-regex": "^2.0.0"}}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-gulplog": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/has-gulplog/-/has-gulplog-0.1.0.tgz", "integrity": "sha1-ZBTIKRNpfaUVkDl9r7EvIpZ4Ec4=", "requires": {"sparkles": "^1.0.0"}}, "has-symbols": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.0.tgz", "integrity": "sha1-uhqPGvKg/DllD1yFA2dwQSIGO0Q="}, "has-unicode": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk="}, "has-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "requires": {"is-buffer": "^1.1.5"}}}}, "hash-base": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/hash-base/-/hash-base-3.0.4.tgz", "integrity": "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=", "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "hash.js": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.3.tgz", "integrity": "sha512-/UETyP0W22QILqS+6HowevwhEFJ3MBJnwTf75Qob9Wz9t0DPuisL8kW8YZMK62dHAKE1c1p+gY1TtOLY+USEHA==", "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.0"}}, "hawk": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/hawk/-/hawk-6.0.2.tgz", "integrity": "sha512-miowhl2+U7Qle4vdLqDdPt9m09K6yZhkLDTWGoUiUzrQCn+mHHSmfJgAyGaLRZbPmTqfFFjRV1QWCW0VWUJBbQ==", "requires": {"boom": "4.x.x", "cryptiles": "3.x.x", "hoek": "4.x.x", "sntp": "2.x.x"}}, "he": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/he/-/he-1.1.1.tgz", "integrity": "sha1-k0EP0hsAlzUVH4howvJx80J+I/0="}, "hmac-drbg": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "hoek": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/hoek/-/hoek-4.2.1.tgz", "integrity": "sha512-QLg82fGkfnJ/4iy1xZ81/9SIJiq1NGFUMGs6ParyjBZr6jW2Ufj/snDqTHixNlHdPNwN2RLVD0Pi3igeK9+JfA=="}, "home-or-tmp": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-2.0.0.tgz", "integrity": "sha1-42w/LSyufXRqhX440Y1fMqeILbg=", "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.1"}}, "homedir-polyfill": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/homedir-polyfill/-/homedir-polyfill-1.0.1.tgz", "integrity": "sha1-TCu8inWJmP7r9e1oWA921GdotLw=", "requires": {"parse-passwd": "^1.0.0"}}, "hosted-git-info": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.6.0.tgz", "integrity": "sha512-lIbgIIQA3lz5XaB6vxakj6sDHADJiZadYEJB+FgA+C4nubM1NwcuvUr9EJPmnH1skZqpqUzWborWo8EIUi0Sdw=="}, "html-minifier": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/html-minifier/-/html-minifier-3.5.2.tgz", "integrity": "sha1-1zvD/0SJQkCIGM5gm/P7DqfvTrc=", "requires": {"camel-case": "3.0.x", "clean-css": "4.1.x", "commander": "2.9.x", "he": "1.1.x", "ncname": "1.0.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.0.x"}, "dependencies": {"commander": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz", "integrity": "sha1-nJkJQXbhIkDLItbFFGCYQA/g99Q=", "requires": {"graceful-readlink": ">= 1.0.0"}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}, "uglify-js": {"version": "3.0.28", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-3.0.28.tgz", "integrity": "sha512-0h/qGay016GG2lVav3Kz174F3T2Vjlz2v6HCt+WDQpoXfco0hWwF5gHK9yh88mUYvIC+N7Z8NT8WpjSp1yoqGA==", "requires": {"commander": "~2.11.0", "source-map": "~0.5.1"}, "dependencies": {"commander": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.11.0.tgz", "integrity": "sha512-b0553uYA5YAEGgyYIGYROzKQ7X5RAqedkfjiZxwi0kL1g3bOaBNNZfYkzt/CL0umgD5wc9Jec2FbB98CjkMRvQ=="}}}}}, "htmlescape": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/htmlescape/-/htmlescape-1.1.1.tgz", "integrity": "sha1-OgPtwiFLyjtmQko+eVk0lQnLA1E="}, "http-browserify": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/http-browserify/-/http-browserify-1.3.2.tgz", "integrity": "sha1-tWLDRHk0mmkNemWX30la76jGBPU=", "requires": {"Base64": "~0.2.0", "inherits": "~2.0.1"}}, "http-errors": {"version": "1.6.3", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "http-signature": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "https-browserify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="}, "iconv-lite": {"version": "0.4.21", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.21.tgz", "integrity": "sha512-En5V9za5mBt2oUA03WGD3TwDv0MKAruqsuxstbMUZaj9W9k/m1CV/9py3l0L5kw9Bln8fdHQmzHSYtvpvTLpKw==", "dev": true, "requires": {"safer-buffer": "^2.1.0"}}, "ieee754": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.11.tgz", "integrity": "sha512-VhDzCKN7K8ufStx/CLj5/PDTMgph+qwN5Pkd5i0sGnVwk56zJ0lkT8Qzi1xqWLS0Wp29DgDtNeS7v8/wMoZeHg=="}, "ignore": {"version": "3.3.7", "resolved": "https://registry.npmjs.org/ignore/-/ignore-3.3.7.tgz", "integrity": "sha512-YGG3ejvBNHRqu0559EOxxNFihD0AjpvHlC/pdGKd3X3ofe+CoJkYazwNJYTNebqpPKN+VVQbh4ZFn1DivMNuHA==", "dev": true}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "indent-string": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz", "integrity": "sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=", "requires": {"repeating": "^2.0.0"}}, "indexof": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/indexof/-/indexof-0.0.1.tgz", "integrity": "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10="}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "ini": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/ini/-/ini-1.3.5.tgz", "integrity": "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc="}, "inline-js": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/inline-js/-/inline-js-0.6.1.tgz", "integrity": "sha512-7Jpjx+TFfy8FORszG9jGqYyQrI+zaDqVbds/Mtr2nSVqomZ1HG3kwaV/oraXiDaVoIrQSHMGUhwThmgDrCEelw==", "requires": {"asyncro": "^2.0.1", "clean-css": "^4.1.9", "fs-extra": "^5.0.0", "is-binary-path": "^2.0.0", "js-tokens": "^3.0.1", "mime": "^2.1.0", "neodoc": "^1.4.0", "treeify": "^1.0.1"}, "dependencies": {"is-binary-path": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.0.0.tgz", "integrity": "sha1-DmHOppdLJN2ovMg2bOWKaSZdGjY=", "requires": {"binary-extensions": "^1.0.0"}}}}, "inline-source-map": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/inline-source-map/-/inline-source-map-0.6.2.tgz", "integrity": "sha1-+Tk0ccGKedFyT4Y/o4tYY3Ct4qU=", "requires": {"source-map": "~0.5.3"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "inquirer": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/inquirer/-/inquirer-3.3.0.tgz", "integrity": "sha512-h+xtnyk4EwKvFWHrUYsWErEVR+igKtLdchu+o0Z1RL7VU/jVMFbYir2bp6bAj8efFNxWqHX0dIss6fJQ+/+qeQ==", "dev": true, "requires": {"ansi-escapes": "^3.0.0", "chalk": "^2.0.0", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^2.0.4", "figures": "^2.0.0", "lodash": "^4.3.0", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rx-lite": "^4.0.8", "rx-lite-aggregates": "^4.0.8", "string-width": "^2.1.0", "strip-ansi": "^4.0.0", "through": "^2.3.6"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.0.tgz", "integrity": "sha512-Wr/w0f4o9LuE7K53cD0qmbAMM+2XNLzR29vFn5hqko4sxGlUsyy363NvmyGIyk5tpe9cjTr9SJYbysEyPkRnFw==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "string-width": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}, "supports-color": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.4.0.tgz", "integrity": "sha512-zjaXglF5nnWpsq470jSv6P9DwPvgLkuapYmfDm3JWOm0vkNTVF2tI4UrN2r6jH1qM/uc/WtxYY1hYoA2dOKj5w==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "insert-module-globals": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/insert-module-globals/-/insert-module-globals-7.0.6.tgz", "integrity": "sha512-R3sidKJr3SsggqQQ5cEwQb3pWG8RNx0UnpyeiOSR6jorRIeAOzH2gkTWnNdMnyRiVbjrG047K7UCtlMkQ1Mo9w==", "requires": {"JSONStream": "^1.0.3", "combine-source-map": "^0.8.0", "concat-stream": "^1.6.1", "is-buffer": "^1.1.0", "lexical-scope": "^1.2.0", "path-is-absolute": "^1.0.1", "process": "~0.11.0", "through2": "^2.0.0", "xtend": "^4.0.0"}}, "interpret": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.1.0.tgz", "integrity": "sha1-ftGxQQxqDg94z5XTuEQMY/eLhhQ="}, "invariant": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "requires": {"loose-envify": "^1.0.0"}}, "invert-kv": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/invert-kv/-/invert-kv-1.0.0.tgz", "integrity": "sha1-EEqOSqym09jNFXqO+L+rLXo//bY="}, "irregular-plurals": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/irregular-plurals/-/irregular-plurals-1.4.0.tgz", "integrity": "sha1-LKmwM2UREYVUEvFr5dd8YqRYp2Y="}, "is-absolute": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-absolute/-/is-absolute-1.0.0.tgz", "integrity": "sha1-OV4a6EsR8mrReV5zwXN45IowFXY=", "requires": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="}, "is-binary-path": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "requires": {"binary-extensions": "^1.0.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4="}, "is-builtin-module": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-builtin-module/-/is-builtin-module-1.0.0.tgz", "integrity": "sha1-VAVy0096wxGfj3bDDLwbHgN6/74=", "requires": {"builtin-modules": "^1.0.0"}}, "is-callable": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.1.3.tgz", "integrity": "sha1-hut1OSgF3cM69xySoO7fdO52BLI="}, "is-data-descriptor": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.1.tgz", "integrity": "sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY="}, "is-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="}}}, "is-dotfile": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.3.tgz", "integrity": "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE="}, "is-equal-shallow": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz", "integrity": "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=", "requires": {"is-primitive": "^2.0.0"}}, "is-extendable": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="}, "is-finite": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.0.2.tgz", "integrity": "sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko=", "requires": {"number-is-nan": "^1.0.0"}}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "requires": {"number-is-nan": "^1.0.0"}}, "is-function": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-function/-/is-function-1.0.1.tgz", "integrity": "sha1-Es+5i2W1fdPRk6MSH19uL0N2ArU="}, "is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}, "is-module": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-module/-/is-module-1.0.0.tgz", "integrity": "sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE="}, "is-negated-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-negated-glob/-/is-negated-glob-1.0.0.tgz", "integrity": "sha1-aRC8pdqMleeEtXUbl2z1oQ/uNtI="}, "is-number": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-obj": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz", "integrity": "sha1-PkcprB9f3gJc19g6iW2rn09n2w8=", "dev": true}, "is-odd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-odd/-/is-odd-2.0.0.tgz", "integrity": "sha1-dkZiRnH9fqVYzNmieVGC8pWPGyQ=", "requires": {"is-number": "^4.0.0"}, "dependencies": {"is-number": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz", "integrity": "sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8="}}}, "is-path-cwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "integrity": "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=", "dev": true}, "is-path-in-cwd": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz", "integrity": "sha512-FjV1RTW48E7CWM7eE/J2NJvAEEVektecDBVBE5Hh3nM1Jd0kvhHtX68Pr3xsDf857xt3Y4AkwVULK1Vku62aaQ==", "dev": true, "requires": {"is-path-inside": "^1.0.0"}}, "is-path-inside": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz", "integrity": "sha1-jvW33lBDej/cprToZe96pVy0gDY=", "dev": true, "requires": {"path-is-inside": "^1.0.1"}}, "is-plain-object": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "requires": {"isobject": "^3.0.1"}}, "is-posix-bracket": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz", "integrity": "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q="}, "is-primitive": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz", "integrity": "sha1-IHurkWOEmcB7Kt8kCkGochADRXU="}, "is-promise": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-2.1.0.tgz", "integrity": "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=", "dev": true}, "is-reference": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-reference/-/is-reference-1.1.0.tgz", "integrity": "sha512-h37O/IX4efe56o9k41II1ECMqKwtqHa7/12dLDEzJIFux2x15an4WCDb0/eKdmUgRpLJ3bR0DrzDc7vOrVgRDw==", "requires": {"@types/estree": "0.0.38"}}, "is-regex": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.0.4.tgz", "integrity": "sha1-VRdIm1RwkbCTDglWVM7SXul+lJE=", "requires": {"has": "^1.0.1"}}, "is-regexp": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-regexp/-/is-regexp-1.0.0.tgz", "integrity": "sha1-/S2INUXEa6xaYz57mgnof6LLUGk=", "dev": true}, "is-relative": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-relative/-/is-relative-1.0.0.tgz", "integrity": "sha1-obtpNc6MXboei5dUubLcwCDiJg0=", "requires": {"is-unc-path": "^1.0.0"}}, "is-resolvable": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.1.0.tgz", "integrity": "sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg==", "dev": true}, "is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true}, "is-symbol": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.1.tgz", "integrity": "sha1-PMWfAAJRlLarLjjbrmaJJWtmBXI="}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "is-unc-path": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-unc-path/-/is-unc-path-1.0.0.tgz", "integrity": "sha1-1zHoiY7QkKEsNSrS6u1Qla0yLJ0=", "requires": {"unc-path-regex": "^0.1.2"}}, "is-utf8": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz", "integrity": "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI="}, "is-valid-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-valid-glob/-/is-valid-glob-1.0.0.tgz", "integrity": "sha1-Kb8+/3Ab4tTTFdusw5vDn+j2Aao="}, "is-windows": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="}, "isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}, "isomorphic-fetch": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz", "integrity": "sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=", "dev": true, "requires": {"node-fetch": "^1.0.1", "whatwg-fetch": ">=0.10.0"}}, "isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="}, "js-tokens": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls="}, "js-yaml": {"version": "3.11.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.11.0.tgz", "integrity": "sha512-saJstZWv7oNeOyBh3+Dx1qWzhW0+e6/8eDzo7p5rDFqxntSztloLtuKu+Ejhtq82jsilwOIZYsCz+lIjthg1Hw==", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "dependencies": {"esprima": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.0.tgz", "integrity": "sha512-oftTcaMu/EGrEIu904mWteKIv8vMuOgGYo7EhVJJN00R/EED9DCua/xxHRdYnKtcECzVg7xOWhflvJMnqcFZjw==", "dev": true}}}, "jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "optional": true}, "jsesc": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-1.3.0.tgz", "integrity": "sha1-RsP+yMGJKxKwgz25vHYiF226s0s="}, "json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="}, "json-schema-traverse": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz", "integrity": "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A="}, "json-stable-stringify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "requires": {"jsonify": "~0.0.0"}}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="}, "json5": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz", "integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="}, "jsonfile": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "requires": {"graceful-fs": "^4.1.6"}}, "jsonify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM="}, "jsonparse": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz", "integrity": "sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA="}, "jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "jsx-ast-utils": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-2.0.1.tgz", "integrity": "sha1-6AGxs5mF4g//yHtA43SAgOLcrH8=", "dev": true, "requires": {"array-includes": "^3.0.3"}}, "just-debounce": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/just-debounce/-/just-debounce-1.0.0.tgz", "integrity": "sha1-h/zPrv/AtozRnVX2cilD+SnqNeo="}, "kind-of": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha1-ARRrNqYhjmTljzqNZt5df8b20FE="}, "labeled-stream-splicer": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/labeled-stream-splicer/-/labeled-stream-splicer-2.0.1.tgz", "integrity": "sha512-MC94mHZRvJ3LfykJlTUipBqenZz1pacOZEMhhQ8dMGcDHs0SBE5GbsavUXV7YtP3icBW17W0Zy1I0lfASmo9Pg==", "requires": {"inherits": "^2.0.1", "isarray": "^2.0.4", "stream-splicer": "^2.0.0"}, "dependencies": {"isarray": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.4.tgz", "integrity": "sha512-GMxXOiUirWg1xTKRipM0Ek07rX+ubx4nNVElTJdNLYmNO/2YrDkgJGw9CljXn+r4EWiDQg/8lsRdHyg2PJuUaA=="}}}, "last-run": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/last-run/-/last-run-1.1.1.tgz", "integrity": "sha1-RblpQsF7HHnHchmCWbqUO+v4yls=", "requires": {"default-resolution": "^2.0.0", "es6-weak-map": "^2.0.1"}}, "lazystream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/lazystream/-/lazystream-1.0.0.tgz", "integrity": "sha1-9plf4PggOS9hOWvolGJAe7dxaOQ=", "requires": {"readable-stream": "^2.0.5"}}, "lcid": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/lcid/-/lcid-1.0.0.tgz", "integrity": "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=", "requires": {"invert-kv": "^1.0.0"}}, "lead": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/lead/-/lead-1.0.0.tgz", "integrity": "sha1-bxT5mje+Op3XhPVJVpDlkDRm7kI=", "requires": {"flush-write-stream": "^1.0.2"}}, "levn": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "lexical-scope": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/lexical-scope/-/lexical-scope-1.2.0.tgz", "integrity": "sha1-/Ope3HBKSzqHls3KQZw6CvryLfQ=", "requires": {"astw": "^2.0.0"}}, "liftoff": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/liftoff/-/liftoff-2.5.0.tgz", "integrity": "sha1-IAkpG7Mc6oYbvxCnwVooyvdcMew=", "requires": {"extend": "^3.0.0", "findup-sync": "^2.0.0", "fined": "^1.0.1", "flagged-respawn": "^1.0.0", "is-plain-object": "^2.0.4", "object.map": "^1.0.0", "rechoir": "^0.6.2", "resolve": "^1.1.7"}}, "load-json-file": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz", "integrity": "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=", "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}, "dependencies": {"strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "requires": {"is-utf8": "^0.2.0"}}}}, "locate-character": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/locate-character/-/locate-character-2.0.5.tgz", "integrity": "sha512-n2GmejDXtOPBAZdIiEFy5dJ5N38xBCXLNOtw2WpB9kGh6pnrEuKlwYI+Tkpofc4wDtVXHtoAOJaMRlYG/oYaxg=="}, "locate-path": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "dependencies": {"path-exists": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}}}, "lodash": {"version": "4.17.10", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.10.tgz", "integrity": "sha512-UejweD1pDoXu+AD825lWwp4ZGtSwgnpZxb3JDViD7StjQz+Nb/6l093lx4OQ0foGWNRoc19mWy7BzL+UAK2iVg=="}, "lodash._basecopy": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz", "integrity": "sha1-jaDmqHbPNEwK2KVIghEd08XHyjY="}, "lodash._basetostring": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._basetostring/-/lodash._basetostring-3.0.1.tgz", "integrity": "sha1-0YYdh3+CSlL2aYMtyvPuFVZqB9U="}, "lodash._basevalues": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._basevalues/-/lodash._basevalues-3.0.0.tgz", "integrity": "sha1-W3dXYoAr3j0yl1A+JjAIIP32Ybc="}, "lodash._escapehtmlchar": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._escapehtmlchar/-/lodash._escapehtmlchar-2.4.1.tgz", "integrity": "sha1-32fDu2t+jh6DGrSL+geVuSr+iZ0=", "requires": {"lodash._htmlescapes": "~2.4.1"}}, "lodash._escapestringchar": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._escapestringchar/-/lodash._escapestringchar-2.4.1.tgz", "integrity": "sha1-7P4iYYoq3lC/7qQ5N+Ud9m8O23I="}, "lodash._getnative": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz", "integrity": "sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U="}, "lodash._htmlescapes": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._htmlescapes/-/lodash._htmlescapes-2.4.1.tgz", "integrity": "sha1-MtFL8IRLbeb4tioFG09nwii2JMs="}, "lodash._isiterateecall": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz", "integrity": "sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw="}, "lodash._isnative": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._isnative/-/lodash._isnative-2.4.1.tgz", "integrity": "sha1-PqZAS3hKe+g2x7V1gOHN95sUgyw="}, "lodash._objecttypes": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._objecttypes/-/lodash._objecttypes-2.4.1.tgz", "integrity": "sha1-fAt/admKH3ZSn4kLDNsbTf7BHBE="}, "lodash._reescape": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._reescape/-/lodash._reescape-3.0.0.tgz", "integrity": "sha1-Kx1vXf4HyKNVdT5fJ/rH8c3hYWo="}, "lodash._reevaluate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._reevaluate/-/lodash._reevaluate-3.0.0.tgz", "integrity": "sha1-WLx0xAZklTrgsSTYBpltrKQx4u0="}, "lodash._reinterpolate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz", "integrity": "sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0="}, "lodash._reunescapedhtml": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._reunescapedhtml/-/lodash._reunescapedhtml-2.4.1.tgz", "integrity": "sha1-dHxPxAED6zu4oJduVx96JlnpO6c=", "requires": {"lodash._htmlescapes": "~2.4.1", "lodash.keys": "~2.4.1"}, "dependencies": {"lodash.keys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.4.1.tgz", "integrity": "sha1-SN6kbfj/djKxDXBrissmWR4rNyc=", "requires": {"lodash._isnative": "~2.4.1", "lodash._shimkeys": "~2.4.1", "lodash.isobject": "~2.4.1"}}}}, "lodash._root": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._root/-/lodash._root-3.0.1.tgz", "integrity": "sha1-+6HEUkwZ7ppfgTa0YJ8BfPTe1pI="}, "lodash._shimkeys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._shimkeys/-/lodash._shimkeys-2.4.1.tgz", "integrity": "sha1-bpzJZm/wgfC1psl4uD4kLmlJ0gM=", "requires": {"lodash._objecttypes": "~2.4.1"}}, "lodash.defaults": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-2.4.1.tgz", "integrity": "sha1-p+iIXwXmiFEUS24SqPNngCa8TFQ=", "requires": {"lodash._objecttypes": "~2.4.1", "lodash.keys": "~2.4.1"}, "dependencies": {"lodash.keys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.4.1.tgz", "integrity": "sha1-SN6kbfj/djKxDXBrissmWR4rNyc=", "requires": {"lodash._isnative": "~2.4.1", "lodash._shimkeys": "~2.4.1", "lodash.isobject": "~2.4.1"}}}}, "lodash.escape": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash.escape/-/lodash.escape-3.2.0.tgz", "integrity": "sha1-mV7g3BjBtIzJLv+ucaEKq1tIdpg=", "requires": {"lodash._root": "^3.0.0"}}, "lodash.isarguments": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz", "integrity": "sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo="}, "lodash.isarray": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz", "integrity": "sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U="}, "lodash.isobject": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isobject/-/lodash.isobject-2.4.1.tgz", "integrity": "sha1-Wi5H/mmVPx7mMafrof5k0tBlWPU=", "requires": {"lodash._objecttypes": "~2.4.1"}}, "lodash.keys": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz", "integrity": "sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=", "requires": {"lodash._getnative": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0"}}, "lodash.memoize": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-3.0.4.tgz", "integrity": "sha1-LcvSwofLwKVcxCMovQxzYVDVPj8="}, "lodash.restparam": {"version": "3.6.1", "resolved": "https://registry.npmjs.org/lodash.restparam/-/lodash.restparam-3.6.1.tgz", "integrity": "sha1-k2pOMJ7zMKdkXtQUWYbIWuWyCAU="}, "lodash.template": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/lodash.template/-/lodash.template-3.6.2.tgz", "integrity": "sha1-+M3sxhaaJVvpCYrosMU9N4kx0U8=", "requires": {"lodash._basecopy": "^3.0.0", "lodash._basetostring": "^3.0.0", "lodash._basevalues": "^3.0.0", "lodash._isiterateecall": "^3.0.0", "lodash._reinterpolate": "^3.0.0", "lodash.escape": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.restparam": "^3.0.0", "lodash.templatesettings": "^3.0.0"}}, "lodash.templatesettings": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-3.1.1.tgz", "integrity": "sha1-+zB4RHU7Zrnxr6VOJix0UwfbqOU=", "requires": {"lodash._reinterpolate": "^3.0.0", "lodash.escape": "^3.0.0"}}, "lodash.values": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.values/-/lodash.values-2.4.1.tgz", "integrity": "sha1-q/UUQ2s8twUAFieXjLzzCxKA7qQ=", "requires": {"lodash.keys": "~2.4.1"}, "dependencies": {"lodash.keys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.4.1.tgz", "integrity": "sha1-SN6kbfj/djKxDXBrissmWR4rNyc=", "requires": {"lodash._isnative": "~2.4.1", "lodash._shimkeys": "~2.4.1", "lodash.isobject": "~2.4.1"}}}}, "loose-envify": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.1.tgz", "integrity": "sha1-0aitM/qc4OcT1l/dCsi3SNR4yEg=", "requires": {"js-tokens": "^3.0.0"}}, "loud-rejection": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz", "integrity": "sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=", "requires": {"currently-unhandled": "^0.4.1", "signal-exit": "^3.0.0"}}, "lower-case": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz", "integrity": "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="}, "lru-cache": {"version": "2.7.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.3.tgz", "integrity": "sha1-bUUk6LlV+V1PW1iFHOId1y+06VI="}, "magic-string": {"version": "0.22.5", "resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.5.tgz", "integrity": "sha512-oreip9rJZkzvA8Qzk9HFs8fZGF/u7H/gtrE8EN6RjKJ9kh2HlC+yQ2QezifqTZfGyiuAV0dRv5a+y/8gBb1m9w==", "requires": {"vlq": "^0.2.2"}}, "make-iterator": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/make-iterator/-/make-iterator-1.0.1.tgz", "integrity": "sha1-KbM/MSqo9UfEpeSQ9Wr87JkTOtY=", "requires": {"kind-of": "^6.0.2"}}, "map-cache": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="}, "map-limit": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/map-limit/-/map-limit-0.0.1.tgz", "integrity": "sha1-63lhAxwPDo0AG/LVb6toXViCLzg=", "requires": {"once": "~1.3.0"}, "dependencies": {"once": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/once/-/once-1.3.3.tgz", "integrity": "sha1-suJhVXzkwxTsgwTz+oJmPkKXyiA=", "requires": {"wrappy": "1"}}}}, "map-obj": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz", "integrity": "sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0="}, "map-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "requires": {"object-visit": "^1.0.0"}}, "matchdep": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/matchdep/-/matchdep-2.0.0.tgz", "integrity": "sha1-xvNINKDY28OzfCfui7yyfHd1WC4=", "requires": {"findup-sync": "^2.0.0", "micromatch": "^3.0.4", "resolve": "^1.4.0", "stack-trace": "0.0.10"}, "dependencies": {"stack-trace": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz", "integrity": "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA="}}}, "md5.js": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/md5.js/-/md5.js-1.3.4.tgz", "integrity": "sha1-6b296UogpawYsENA/Fdk1bCdkB0=", "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "meow": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/meow/-/meow-3.7.0.tgz", "integrity": "sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=", "requires": {"camelcase-keys": "^2.0.0", "decamelize": "^1.1.2", "loud-rejection": "^1.0.0", "map-obj": "^1.0.1", "minimist": "^1.1.3", "normalize-package-data": "^2.3.4", "object-assign": "^4.0.1", "read-pkg-up": "^1.0.1", "redent": "^1.0.0", "trim-newlines": "^1.0.0"}, "dependencies": {"object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}}}, "merge-source-map": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/merge-source-map/-/merge-source-map-1.0.4.tgz", "integrity": "sha1-pd5GU42uhNQRTMXqArR3KmNGcB8=", "requires": {"source-map": "^0.5.6"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "micromatch": {"version": "3.1.10", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "miller-rabin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz", "integrity": "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==", "requires": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}}, "mime": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/mime/-/mime-2.3.1.tgz", "integrity": "sha512-OEUllcVoydBHGN1z84yfQDimn58pZNNNXgZlHXSboxMlFvgI6MXSWpWKpFRra7H1HxpVhHTkrghfRW49k6yjeg=="}, "mime-db": {"version": "1.33.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.33.0.tgz", "integrity": "sha512-BHJ/EKruNIqJf/QahvxwQZXKygOQ256myeN/Ew+THcAa5q+PjyTTMMeNQC4DZw5AwfvelsUrA6B67NKMqXDbzQ=="}, "mime-types": {"version": "2.1.18", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.18.tgz", "integrity": "sha512-lc/aahn+t4/SWV/qcmumYjymLsWfN3ELhpmVuUFjgsORruuZPVSwAQryq+HHGvO/SI2KVX26bx+En+zhM8g8hQ==", "requires": {"mime-db": "~1.33.0"}}, "mimic-fn": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz", "integrity": "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==", "dev": true}, "mimic-response": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.0.tgz", "integrity": "sha1-3z02Uqc/3ta5sLJBRub9BSNTRY4="}, "minimalistic-assert": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="}, "minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="}, "minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="}, "mixin-deep": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.1.tgz", "integrity": "sha1-pJ5yaNzhoNlpjkUybFYm3zVD0P4=", "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="}}}, "module-deps": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/module-deps/-/module-deps-6.0.2.tgz", "integrity": "sha512-KWBI3009iRnHjRlxRhe8nJ6kdeBTg4sMi5N6AZgg5f1/v5S7EBCRBOY854I4P5Anl4kx6AJH+4bBBC2Gi3nkvg==", "requires": {"JSONStream": "^1.0.3", "browser-resolve": "^1.7.0", "cached-path-relative": "^1.0.0", "concat-stream": "~1.6.0", "defined": "^1.0.0", "detective": "^5.0.2", "duplexer2": "^0.1.2", "inherits": "^2.0.1", "parents": "^1.0.0", "readable-stream": "^2.0.2", "resolve": "^1.4.0", "stream-combiner2": "^1.1.1", "subarg": "^1.0.0", "through2": "^2.0.0", "xtend": "^4.0.0"}, "dependencies": {"duplexer2": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/duplexer2/-/duplexer2-0.1.4.tgz", "integrity": "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=", "requires": {"readable-stream": "^2.0.2"}}}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "multipipe": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/multipipe/-/multipipe-0.1.2.tgz", "integrity": "sha1-Ko8t33Du1WTf8tV/HhoTfZ8FB4s=", "requires": {"duplexer2": "0.0.2"}}, "murmurhash-js": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/murmurhash-js/-/murmurhash-js-1.0.0.tgz", "integrity": "sha1-sGJ44h/Gw3+lMTcysEEry2rhX1E="}, "mute-stdout": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/mute-stdout/-/mute-stdout-1.0.0.tgz", "integrity": "sha1-WzLqB+tDyd7WEwQ0z5JvRrKn/U0="}, "mute-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true}, "nan": {"version": "2.10.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.10.0.tgz", "integrity": "sha512-bAdJv7fBLhWC+/Bls0Oza+mvTaNQtP+1RyhhhvD95pgUJz6XM5IzgmxOkItJ9tkoCiplvAnXI1tNmmUD/eScyA=="}, "nanomatch": {"version": "1.2.9", "resolved": "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.9.tgz", "integrity": "sha1-h59xUMstq3pHElkGbBBO7m4Pp8I=", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-odd": "^2.0.0", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "ncname": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ncname/-/ncname-1.0.0.tgz", "integrity": "sha1-W1etGLHKCShk72Kwse2BlPODtxw=", "requires": {"xml-char-classes": "^1.0.0"}}, "neodoc": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/neodoc/-/neodoc-1.4.0.tgz", "integrity": "sha1-Uwyph33gcp/9XWQg+1KxBPCIjQU=", "requires": {"ansi-regex": "^2.0.0"}}, "new-from": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/new-from/-/new-from-0.0.3.tgz", "integrity": "sha1-HErRNhPePhXWMhtw7Vwjk36iXmc=", "requires": {"readable-stream": "~1.1.8"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}}}, "next-tick": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/next-tick/-/next-tick-1.0.0.tgz", "integrity": "sha1-yobR/ogoFpsBICCOPchCS524NCw="}, "no-case": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz", "integrity": "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==", "requires": {"lower-case": "^1.1.1"}}, "node-abi": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/node-abi/-/node-abi-2.4.0.tgz", "integrity": "sha512-hRUz0vG+eJfSqwU6rOgW6wNyX85ec8OEE9n4A+u+eoiE8oTePhCkUFTNmwQ+86Kyu429PCLNNyI2P2jL9qKXhw==", "requires": {"semver": "^5.4.1"}, "dependencies": {"semver": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.5.0.tgz", "integrity": "sha512-4SJ3dm0WAwWy/NVeioZh5AntkdJoWKxHxcmyP622fOkgHa4z3R0TdBJICINyaSDE6uNwVc8gZr+ZinwZAH4xIA=="}}}, "node-fetch": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-1.7.3.tgz", "integrity": "sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==", "dev": true, "requires": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}}, "node-gyp": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/node-gyp/-/node-gyp-3.6.2.tgz", "integrity": "sha1-m/vlRWIoYoSDjnUOrAUpWFP6HGA=", "requires": {"fstream": "^1.0.0", "glob": "^7.0.3", "graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "mkdirp": "^0.5.0", "nopt": "2 || 3", "npmlog": "0 || 1 || 2 || 3 || 4", "osenv": "0", "request": "2", "rimraf": "2", "semver": "~5.3.0", "tar": "^2.0.0", "which": "1"}, "dependencies": {"semver": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.3.0.tgz", "integrity": "sha1-myzl094C0XxgEq0yaqa00M9U+U8="}}}, "node-readfiles": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/node-readfiles/-/node-readfiles-0.2.0.tgz", "integrity": "sha1-271K8SE04uY1wkXvk//Pb2BnOl0=", "requires": {"es6-promise": "^3.2.1"}}, "node-stream": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/node-stream/-/node-stream-1.7.0.tgz", "integrity": "sha1-FRO8TAgLiJGTt0cJD57jL1ruhuo=", "requires": {"lodash": "^4.17.2", "readable-stream": "^2.3.3", "split2": "^2.1.0", "stream-combiner2": "^1.1.1", "through2": "^2.0.1"}}, "noop-fn": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/noop-fn/-/noop-fn-1.0.0.tgz", "integrity": "sha1-XzPUfxPSFQ35PgywNmmemC94/78="}, "noop-logger": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/noop-logger/-/noop-logger-0.1.1.tgz", "integrity": "sha1-lKKxYzxPExdVMAfYlm/Q6EG2pMI="}, "nopt": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "integrity": "sha1-xkZdvwirzU2zWTF/eaxopkayj/k=", "requires": {"abbrev": "1"}}, "normalize-package-data": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.4.0.tgz", "integrity": "sha512-9jjUFbTPfEy3R/ad/2oNbKtW9Hgovl5O1FvFWKkKblNXoN/Oou6+9+KKohPK13Yc3/TyunyWhJp6gvRNR/PPAw==", "requires": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}, "now-and-later": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/now-and-later/-/now-and-later-2.0.0.tgz", "integrity": "sha1-vGHLtFbXnLMiB85HygUTb/Ln1u4=", "requires": {"once": "^1.3.2"}}, "npm": {"version": "5.8.0", "resolved": "https://registry.npmjs.org/npm/-/npm-5.8.0.tgz", "integrity": "sha512-DowXzQwtSWDtbAjuWecuEiismR0VdNEYaL3VxNTYTdW6AGkYxfGk9LUZ/rt6etEyiH4IEk95HkJeGfXE5Rz9xQ==", "requires": {"JSONStream": "^1.3.2", "abbrev": "~1.1.1", "ansi-regex": "~3.0.0", "ansicolors": "~0.3.2", "ansistyles": "~0.1.3", "aproba": "~1.2.0", "archy": "~1.0.0", "bin-links": "^1.1.0", "bluebird": "~3.5.1", "cacache": "^10.0.4", "call-limit": "~1.1.0", "chownr": "~1.0.1", "cli-table2": "~0.2.0", "cmd-shim": "~2.0.2", "columnify": "~1.5.4", "config-chain": "~1.1.11", "debuglog": "*", "detect-indent": "~5.0.0", "detect-newline": "^2.1.0", "dezalgo": "~1.0.3", "editor": "~1.0.0", "find-npm-prefix": "^1.0.2", "fs-vacuum": "~1.2.10", "fs-write-stream-atomic": "~1.0.10", "gentle-fs": "^2.0.1", "glob": "~7.1.2", "graceful-fs": "~4.1.11", "has-unicode": "~2.0.1", "hosted-git-info": "^2.6.0", "iferr": "~0.1.5", "imurmurhash": "*", "inflight": "~1.0.6", "inherits": "~2.0.3", "ini": "^1.3.5", "init-package-json": "^1.10.3", "is-cidr": "~1.0.0", "json-parse-better-errors": "^1.0.1", "lazy-property": "~1.0.0", "libcipm": "^1.6.0", "libnpx": "^10.0.1", "lockfile": "~1.0.3", "lodash._baseindexof": "*", "lodash._baseuniq": "~4.6.0", "lodash._bindcallback": "*", "lodash._cacheindexof": "*", "lodash._createcache": "*", "lodash._getnative": "*", "lodash.clonedeep": "~4.5.0", "lodash.restparam": "*", "lodash.union": "~4.6.0", "lodash.uniq": "~4.5.0", "lodash.without": "~4.4.0", "lru-cache": "~4.1.1", "meant": "~1.0.1", "mississippi": "^3.0.0", "mkdirp": "~0.5.1", "move-concurrently": "^1.0.1", "nopt": "~4.0.1", "normalize-package-data": "~2.4.0", "npm-cache-filename": "~1.0.2", "npm-install-checks": "~3.0.0", "npm-lifecycle": "^2.0.1", "npm-package-arg": "~6.0.0", "npm-packlist": "~1.1.10", "npm-profile": "^3.0.1", "npm-registry-client": "^8.5.1", "npm-user-validate": "~1.0.0", "npmlog": "~4.1.2", "once": "~1.4.0", "opener": "~1.4.3", "osenv": "^0.1.5", "pacote": "^7.6.1", "path-is-inside": "~1.0.2", "promise-inflight": "~1.0.1", "qrcode-terminal": "~0.11.0", "query-string": "^5.1.0", "qw": "~1.0.1", "read": "~1.0.7", "read-cmd-shim": "~1.0.1", "read-installed": "~4.0.3", "read-package-json": "^2.0.13", "read-package-tree": "~5.1.6", "readable-stream": "^2.3.5", "readdir-scoped-modules": "*", "request": "~2.83.0", "retry": "~0.10.1", "rimraf": "~2.6.2", "safe-buffer": "~5.1.1", "semver": "^5.5.0", "sha": "~2.0.1", "slide": "~1.1.6", "sorted-object": "~2.0.1", "sorted-union-stream": "~2.1.3", "ssri": "^5.2.4", "strip-ansi": "~4.0.0", "tar": "^4.4.0", "text-table": "~0.2.0", "uid-number": "0.0.6", "umask": "~1.1.0", "unique-filename": "~1.1.0", "unpipe": "~1.0.0", "update-notifier": "~2.3.0", "uuid": "^3.2.1", "validate-npm-package-license": "*", "validate-npm-package-name": "~3.0.0", "which": "~1.3.0", "worker-farm": "^1.5.4", "wrappy": "~1.0.2", "write-file-atomic": "^2.3.0"}, "dependencies": {"JSONStream": {"version": "1.3.2", "bundled": true, "requires": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}, "dependencies": {"jsonparse": {"version": "1.3.1", "bundled": true}, "through": {"version": "2.3.8", "bundled": true}}}, "abbrev": {"version": "1.1.1", "bundled": true}, "ansi-regex": {"version": "3.0.0", "bundled": true}, "ansicolors": {"version": "0.3.2", "bundled": true}, "ansistyles": {"version": "0.1.3", "bundled": true}, "aproba": {"version": "1.2.0", "bundled": true}, "archy": {"version": "1.0.0", "bundled": true}, "bin-links": {"version": "1.1.0", "bundled": true, "requires": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "fs-write-stream-atomic": "^1.0.10", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "slide": "^1.1.6"}}, "bluebird": {"version": "3.5.1", "bundled": true}, "cacache": {"version": "10.0.4", "bundled": true, "requires": {"bluebird": "^3.5.1", "chownr": "^1.0.1", "glob": "^7.1.2", "graceful-fs": "^4.1.11", "lru-cache": "^4.1.1", "mississippi": "^2.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.2", "ssri": "^5.2.4", "unique-filename": "^1.1.0", "y18n": "^4.0.0"}, "dependencies": {"mississippi": {"version": "2.0.0", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^2.0.1", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.1", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.4", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "y18n": {"version": "4.0.0", "bundled": true}}}, "call-limit": {"version": "1.1.0", "bundled": true}, "chownr": {"version": "1.0.1", "bundled": true}, "cli-table2": {"version": "0.2.0", "bundled": true, "requires": {"colors": "^1.1.2", "lodash": "^3.10.1", "string-width": "^1.0.1"}, "dependencies": {"colors": {"version": "1.1.2", "bundled": true, "optional": true}, "lodash": {"version": "3.10.1", "bundled": true}, "string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}}}}}, "cmd-shim": {"version": "2.0.2", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}}, "columnify": {"version": "1.5.4", "bundled": true, "requires": {"strip-ansi": "^3.0.0", "wcwidth": "^1.0.0"}, "dependencies": {"strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}, "wcwidth": {"version": "1.0.1", "bundled": true, "requires": {"defaults": "^1.0.3"}, "dependencies": {"defaults": {"version": "1.0.3", "bundled": true, "requires": {"clone": "^1.0.2"}, "dependencies": {"clone": {"version": "1.0.2", "bundled": true}}}}}}}, "config-chain": {"version": "1.1.11", "bundled": true, "requires": {"ini": "^1.3.4", "proto-list": "~1.2.1"}, "dependencies": {"proto-list": {"version": "1.2.4", "bundled": true}}}, "debuglog": {"version": "1.0.1", "bundled": true}, "detect-indent": {"version": "5.0.0", "bundled": true}, "detect-newline": {"version": "2.1.0", "bundled": true}, "dezalgo": {"version": "1.0.3", "bundled": true, "requires": {"asap": "^2.0.0", "wrappy": "1"}, "dependencies": {"asap": {"version": "2.0.5", "bundled": true}}}, "editor": {"version": "1.0.0", "bundled": true}, "find-npm-prefix": {"version": "1.0.2", "bundled": true}, "fs-vacuum": {"version": "1.2.10", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "path-is-inside": "^1.0.1", "rimraf": "^2.5.2"}}, "fs-write-stream-atomic": {"version": "1.0.10", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "gentle-fs": {"version": "2.0.1", "bundled": true, "requires": {"aproba": "^1.1.2", "fs-vacuum": "^1.2.10", "graceful-fs": "^4.1.11", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "path-is-inside": "^1.0.2", "read-cmd-shim": "^1.0.1", "slide": "^1.1.6"}}, "glob": {"version": "7.1.2", "bundled": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "dependencies": {"fs.realpath": {"version": "1.0.0", "bundled": true}, "minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.8", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}, "path-is-absolute": {"version": "1.0.1", "bundled": true}}}, "graceful-fs": {"version": "4.1.11", "bundled": true}, "has-unicode": {"version": "2.0.1", "bundled": true}, "hosted-git-info": {"version": "2.6.0", "bundled": true}, "iferr": {"version": "0.1.5", "bundled": true}, "imurmurhash": {"version": "0.1.4", "bundled": true}, "inflight": {"version": "1.0.6", "bundled": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true}, "ini": {"version": "1.3.5", "bundled": true}, "init-package-json": {"version": "1.10.3", "bundled": true, "requires": {"glob": "^7.1.1", "npm-package-arg": "^4.0.0 || ^5.0.0 || ^6.0.0", "promzard": "^0.3.0", "read": "~1.0.1", "read-package-json": "1 || 2", "semver": "2.x || 3.x || 4 || 5", "validate-npm-package-license": "^3.0.1", "validate-npm-package-name": "^3.0.0"}, "dependencies": {"promzard": {"version": "0.3.0", "bundled": true, "requires": {"read": "1"}}}}, "is-cidr": {"version": "1.0.0", "bundled": true, "requires": {"cidr-regex": "1.0.6"}, "dependencies": {"cidr-regex": {"version": "1.0.6", "bundled": true}}}, "json-parse-better-errors": {"version": "1.0.1", "bundled": true}, "lazy-property": {"version": "1.0.0", "bundled": true}, "libcipm": {"version": "1.6.0", "bundled": true, "requires": {"bin-links": "^1.1.0", "bluebird": "^3.5.1", "find-npm-prefix": "^1.0.2", "graceful-fs": "^4.1.11", "lock-verify": "^2.0.0", "npm-lifecycle": "^2.0.0", "npm-logical-tree": "^1.2.1", "npm-package-arg": "^6.0.0", "pacote": "^7.5.1", "protoduck": "^5.0.0", "read-package-json": "^2.0.12", "rimraf": "^2.6.2", "worker-farm": "^1.5.4"}, "dependencies": {"lock-verify": {"version": "2.0.0", "bundled": true, "requires": {"npm-package-arg": "^5.1.2", "semver": "^5.4.1"}, "dependencies": {"npm-package-arg": {"version": "5.1.2", "bundled": true, "requires": {"hosted-git-info": "^2.4.2", "osenv": "^0.1.4", "semver": "^5.1.0", "validate-npm-package-name": "^3.0.0"}}}}, "npm-logical-tree": {"version": "1.2.1", "bundled": true}, "protoduck": {"version": "5.0.0", "bundled": true, "requires": {"genfun": "^4.0.1"}, "dependencies": {"genfun": {"version": "4.0.1", "bundled": true}}}, "worker-farm": {"version": "1.5.4", "bundled": true, "requires": {"errno": "~0.1.7", "xtend": "~4.0.1"}, "dependencies": {"errno": {"version": "0.1.7", "bundled": true, "requires": {"prr": "~1.0.1"}, "dependencies": {"prr": {"version": "1.0.1", "bundled": true}}}, "xtend": {"version": "4.0.1", "bundled": true}}}}}, "libnpx": {"version": "10.0.1", "bundled": true, "requires": {"dotenv": "^5.0.1", "npm-package-arg": "^6.0.0", "rimraf": "^2.6.2", "safe-buffer": "^5.1.0", "update-notifier": "^2.3.0", "which": "^1.3.0", "y18n": "^4.0.0", "yargs": "^11.0.0"}, "dependencies": {"dotenv": {"version": "5.0.1", "bundled": true}, "y18n": {"version": "4.0.0", "bundled": true}, "yargs": {"version": "11.0.0", "bundled": true, "requires": {"cliui": "^4.0.0", "decamelize": "^1.1.1", "find-up": "^2.1.0", "get-caller-file": "^1.0.1", "os-locale": "^2.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^9.0.2"}, "dependencies": {"cliui": {"version": "4.0.0", "bundled": true, "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}, "dependencies": {"wrap-ansi": {"version": "2.1.0", "bundled": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}}}}}, "decamelize": {"version": "1.2.0", "bundled": true}, "find-up": {"version": "2.1.0", "bundled": true, "requires": {"locate-path": "^2.0.0"}, "dependencies": {"locate-path": {"version": "2.0.0", "bundled": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "dependencies": {"p-locate": {"version": "2.0.0", "bundled": true, "requires": {"p-limit": "^1.1.0"}, "dependencies": {"p-limit": {"version": "1.2.0", "bundled": true, "requires": {"p-try": "^1.0.0"}, "dependencies": {"p-try": {"version": "1.0.0", "bundled": true}}}}}, "path-exists": {"version": "3.0.0", "bundled": true}}}}}, "get-caller-file": {"version": "1.0.2", "bundled": true}, "os-locale": {"version": "2.1.0", "bundled": true, "requires": {"execa": "^0.7.0", "lcid": "^1.0.0", "mem": "^1.1.0"}, "dependencies": {"execa": {"version": "0.7.0", "bundled": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "bundled": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"shebang-command": {"version": "1.2.0", "bundled": true, "requires": {"shebang-regex": "^1.0.0"}, "dependencies": {"shebang-regex": {"version": "1.0.0", "bundled": true}}}}}, "get-stream": {"version": "3.0.0", "bundled": true}, "is-stream": {"version": "1.1.0", "bundled": true}, "npm-run-path": {"version": "2.0.2", "bundled": true, "requires": {"path-key": "^2.0.0"}, "dependencies": {"path-key": {"version": "2.0.1", "bundled": true}}}, "p-finally": {"version": "1.0.0", "bundled": true}, "signal-exit": {"version": "3.0.2", "bundled": true}, "strip-eof": {"version": "1.0.0", "bundled": true}}}, "lcid": {"version": "1.0.0", "bundled": true, "requires": {"invert-kv": "^1.0.0"}, "dependencies": {"invert-kv": {"version": "1.0.0", "bundled": true}}}, "mem": {"version": "1.1.0", "bundled": true, "requires": {"mimic-fn": "^1.0.0"}, "dependencies": {"mimic-fn": {"version": "1.2.0", "bundled": true}}}}}, "require-directory": {"version": "2.1.1", "bundled": true}, "require-main-filename": {"version": "1.0.1", "bundled": true}, "set-blocking": {"version": "2.0.0", "bundled": true}, "string-width": {"version": "2.1.1", "bundled": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "dependencies": {"is-fullwidth-code-point": {"version": "2.0.0", "bundled": true}}}, "which-module": {"version": "2.0.0", "bundled": true}, "y18n": {"version": "3.2.1", "bundled": true}, "yargs-parser": {"version": "9.0.2", "bundled": true, "requires": {"camelcase": "^4.1.0"}, "dependencies": {"camelcase": {"version": "4.1.0", "bundled": true}}}}}}}, "lockfile": {"version": "1.0.3", "bundled": true}, "lodash._baseindexof": {"version": "3.1.0", "bundled": true}, "lodash._baseuniq": {"version": "4.6.0", "bundled": true, "requires": {"lodash._createset": "~4.0.0", "lodash._root": "~3.0.0"}, "dependencies": {"lodash._createset": {"version": "4.0.3", "bundled": true}, "lodash._root": {"version": "3.0.1", "bundled": true}}}, "lodash._bindcallback": {"version": "3.0.1", "bundled": true}, "lodash._cacheindexof": {"version": "3.0.2", "bundled": true}, "lodash._createcache": {"version": "3.1.2", "bundled": true, "requires": {"lodash._getnative": "^3.0.0"}}, "lodash._getnative": {"version": "3.9.1", "bundled": true}, "lodash.clonedeep": {"version": "4.5.0", "bundled": true}, "lodash.restparam": {"version": "3.6.1", "bundled": true}, "lodash.union": {"version": "4.6.0", "bundled": true}, "lodash.uniq": {"version": "4.5.0", "bundled": true}, "lodash.without": {"version": "4.4.0", "bundled": true}, "lru-cache": {"version": "4.1.1", "bundled": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}, "dependencies": {"pseudomap": {"version": "1.0.2", "bundled": true}, "yallist": {"version": "2.1.2", "bundled": true}}}, "meant": {"version": "1.0.1", "bundled": true}, "mississippi": {"version": "3.0.0", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.1", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.4", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "3.0.0", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "mkdirp": {"version": "0.5.1", "bundled": true, "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "bundled": true}}}, "move-concurrently": {"version": "1.0.1", "bundled": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}, "dependencies": {"copy-concurrently": {"version": "1.0.5", "bundled": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "run-queue": {"version": "1.0.3", "bundled": true, "requires": {"aproba": "^1.1.1"}}}}, "nopt": {"version": "4.0.1", "bundled": true, "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "normalize-package-data": {"version": "2.4.0", "bundled": true, "requires": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"is-builtin-module": {"version": "1.0.0", "bundled": true, "requires": {"builtin-modules": "^1.0.0"}, "dependencies": {"builtin-modules": {"version": "1.1.1", "bundled": true}}}}}, "npm-cache-filename": {"version": "1.0.2", "bundled": true}, "npm-install-checks": {"version": "3.0.0", "bundled": true, "requires": {"semver": "^2.3.0 || 3.x || 4 || 5"}}, "npm-lifecycle": {"version": "2.0.1", "bundled": true, "requires": {"byline": "^5.0.0", "graceful-fs": "^4.1.11", "node-gyp": "^3.6.2", "resolve-from": "^4.0.0", "slide": "^1.1.6", "uid-number": "0.0.6", "umask": "^1.1.0", "which": "^1.3.0"}, "dependencies": {"byline": {"version": "5.0.0", "bundled": true}, "node-gyp": {"version": "3.6.2", "bundled": true, "requires": {"fstream": "^1.0.0", "glob": "^7.0.3", "graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "mkdirp": "^0.5.0", "nopt": "2 || 3", "npmlog": "0 || 1 || 2 || 3 || 4", "osenv": "0", "request": "2", "rimraf": "2", "semver": "~5.3.0", "tar": "^2.0.0", "which": "1"}, "dependencies": {"fstream": {"version": "1.0.11", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}}, "minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}, "nopt": {"version": "3.0.6", "bundled": true, "requires": {"abbrev": "1"}}, "semver": {"version": "5.3.0", "bundled": true}, "tar": {"version": "2.2.1", "bundled": true, "requires": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "dependencies": {"block-stream": {"version": "0.0.9", "bundled": true, "requires": {"inherits": "~2.0.0"}}}}}}, "resolve-from": {"version": "4.0.0", "bundled": true}}}, "npm-package-arg": {"version": "6.0.0", "bundled": true, "requires": {"hosted-git-info": "^2.5.0", "osenv": "^0.1.4", "semver": "^5.4.1", "validate-npm-package-name": "^3.0.0"}}, "npm-packlist": {"version": "1.1.10", "bundled": true, "requires": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}, "dependencies": {"ignore-walk": {"version": "3.0.1", "bundled": true, "requires": {"minimatch": "^3.0.4"}, "dependencies": {"minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.8", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}}}, "npm-bundled": {"version": "1.0.3", "bundled": true}}}, "npm-profile": {"version": "3.0.1", "bundled": true, "requires": {"aproba": "^1.1.2", "make-fetch-happen": "^2.5.0"}, "dependencies": {"make-fetch-happen": {"version": "2.6.0", "bundled": true, "requires": {"agentkeepalive": "^3.3.0", "cacache": "^10.0.0", "http-cache-semantics": "^3.8.0", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.1.0", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "node-fetch-npm": "^2.0.2", "promise-retry": "^1.1.1", "socks-proxy-agent": "^3.0.1", "ssri": "^5.0.0"}, "dependencies": {"agentkeepalive": {"version": "3.3.0", "bundled": true, "requires": {"humanize-ms": "^1.2.1"}, "dependencies": {"humanize-ms": {"version": "1.2.1", "bundled": true, "requires": {"ms": "^2.0.0"}, "dependencies": {"ms": {"version": "2.1.1", "bundled": true}}}}}, "http-cache-semantics": {"version": "3.8.1", "bundled": true}, "http-proxy-agent": {"version": "2.0.0", "bundled": true, "requires": {"agent-base": "4", "debug": "2"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "2.6.9", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "https-proxy-agent": {"version": "2.1.1", "bundled": true, "requires": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "3.1.0", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "mississippi": {"version": "1.3.1", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^1.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.0", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.3", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "1.0.3", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "node-fetch-npm": {"version": "2.0.2", "bundled": true, "requires": {"encoding": "^0.1.11", "json-parse-better-errors": "^1.0.0", "safe-buffer": "^5.1.1"}, "dependencies": {"encoding": {"version": "0.1.12", "bundled": true, "requires": {"iconv-lite": "~0.4.13"}, "dependencies": {"iconv-lite": {"version": "0.4.19", "bundled": true}}}, "json-parse-better-errors": {"version": "1.0.1", "bundled": true}}}, "promise-retry": {"version": "1.1.1", "bundled": true, "requires": {"err-code": "^1.0.0", "retry": "^0.10.0"}, "dependencies": {"err-code": {"version": "1.1.2", "bundled": true}}}, "socks-proxy-agent": {"version": "3.0.1", "bundled": true, "requires": {"agent-base": "^4.1.0", "socks": "^1.1.10"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "socks": {"version": "1.1.10", "bundled": true, "requires": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}, "dependencies": {"ip": {"version": "1.1.5", "bundled": true}, "smart-buffer": {"version": "1.1.15", "bundled": true}}}}}}}}}, "npm-registry-client": {"version": "8.5.1", "bundled": true, "requires": {"concat-stream": "^1.5.2", "graceful-fs": "^4.1.6", "normalize-package-data": "~1.0.1 || ^2.0.0", "npm-package-arg": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0", "npmlog": "2 || ^3.1.0 || ^4.0.0", "once": "^1.3.3", "request": "^2.74.0", "retry": "^0.10.0", "safe-buffer": "^5.1.1", "semver": "2 >=2.2.1 || 3.x || 4 || 5", "slide": "^1.1.3", "ssri": "^5.2.4"}, "dependencies": {"concat-stream": {"version": "1.6.1", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}}}, "npm-user-validate": {"version": "1.0.0", "bundled": true}, "npmlog": {"version": "4.1.2", "bundled": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}, "dependencies": {"are-we-there-yet": {"version": "1.1.4", "bundled": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "dependencies": {"delegates": {"version": "1.0.0", "bundled": true}}}, "console-control-strings": {"version": "1.1.0", "bundled": true}, "gauge": {"version": "2.7.4", "bundled": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}, "dependencies": {"object-assign": {"version": "4.1.1", "bundled": true}, "signal-exit": {"version": "3.0.2", "bundled": true}, "string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}, "wide-align": {"version": "1.1.2", "bundled": true, "requires": {"string-width": "^1.0.2"}}}}, "set-blocking": {"version": "2.0.0", "bundled": true}}}, "once": {"version": "1.4.0", "bundled": true, "requires": {"wrappy": "1"}}, "opener": {"version": "1.4.3", "bundled": true}, "osenv": {"version": "0.1.5", "bundled": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}, "dependencies": {"os-homedir": {"version": "1.0.2", "bundled": true}, "os-tmpdir": {"version": "1.0.2", "bundled": true}}}, "pacote": {"version": "7.6.1", "bundled": true, "requires": {"bluebird": "^3.5.1", "cacache": "^10.0.4", "get-stream": "^3.0.0", "glob": "^7.1.2", "lru-cache": "^4.1.1", "make-fetch-happen": "^2.6.0", "minimatch": "^3.0.4", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "normalize-package-data": "^2.4.0", "npm-package-arg": "^6.0.0", "npm-packlist": "^1.1.10", "npm-pick-manifest": "^2.1.0", "osenv": "^0.1.5", "promise-inflight": "^1.0.1", "promise-retry": "^1.1.1", "protoduck": "^5.0.0", "rimraf": "^2.6.2", "safe-buffer": "^5.1.1", "semver": "^5.5.0", "ssri": "^5.2.4", "tar": "^4.4.0", "unique-filename": "^1.1.0", "which": "^1.3.0"}, "dependencies": {"get-stream": {"version": "3.0.0", "bundled": true}, "make-fetch-happen": {"version": "2.6.0", "bundled": true, "requires": {"agentkeepalive": "^3.3.0", "cacache": "^10.0.0", "http-cache-semantics": "^3.8.0", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.1.0", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "node-fetch-npm": "^2.0.2", "promise-retry": "^1.1.1", "socks-proxy-agent": "^3.0.1", "ssri": "^5.0.0"}, "dependencies": {"agentkeepalive": {"version": "3.4.0", "bundled": true, "requires": {"humanize-ms": "^1.2.1"}, "dependencies": {"humanize-ms": {"version": "1.2.1", "bundled": true, "requires": {"ms": "^2.0.0"}, "dependencies": {"ms": {"version": "2.1.1", "bundled": true}}}}}, "http-cache-semantics": {"version": "3.8.1", "bundled": true}, "http-proxy-agent": {"version": "2.1.0", "bundled": true, "requires": {"agent-base": "4", "debug": "3.1.0"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "3.1.0", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "https-proxy-agent": {"version": "2.2.0", "bundled": true, "requires": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "3.1.0", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "mississippi": {"version": "1.3.1", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^1.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.1", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.4", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "1.0.3", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "node-fetch-npm": {"version": "2.0.2", "bundled": true, "requires": {"encoding": "^0.1.11", "json-parse-better-errors": "^1.0.0", "safe-buffer": "^5.1.1"}, "dependencies": {"encoding": {"version": "0.1.12", "bundled": true, "requires": {"iconv-lite": "~0.4.13"}, "dependencies": {"iconv-lite": {"version": "0.4.19", "bundled": true}}}, "json-parse-better-errors": {"version": "1.0.1", "bundled": true}}}, "socks-proxy-agent": {"version": "3.0.1", "bundled": true, "requires": {"agent-base": "^4.1.0", "socks": "^1.1.10"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "socks": {"version": "1.1.10", "bundled": true, "requires": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}, "dependencies": {"ip": {"version": "1.1.5", "bundled": true}, "smart-buffer": {"version": "1.1.15", "bundled": true}}}}}}}, "minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}, "npm-pick-manifest": {"version": "2.1.0", "bundled": true, "requires": {"npm-package-arg": "^6.0.0", "semver": "^5.4.1"}}, "promise-retry": {"version": "1.1.1", "bundled": true, "requires": {"err-code": "^1.0.0", "retry": "^0.10.0"}, "dependencies": {"err-code": {"version": "1.1.2", "bundled": true}}}, "protoduck": {"version": "5.0.0", "bundled": true, "requires": {"genfun": "^4.0.1"}, "dependencies": {"genfun": {"version": "4.0.1", "bundled": true}}}}}, "path-is-inside": {"version": "1.0.2", "bundled": true}, "promise-inflight": {"version": "1.0.1", "bundled": true}, "qrcode-terminal": {"version": "0.11.0", "bundled": true}, "query-string": {"version": "5.1.0", "bundled": true, "requires": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}, "dependencies": {"decode-uri-component": {"version": "0.2.0", "bundled": true}, "object-assign": {"version": "4.1.1", "bundled": true}, "strict-uri-encode": {"version": "1.1.0", "bundled": true}}}, "qw": {"version": "1.0.1", "bundled": true}, "read": {"version": "1.0.7", "bundled": true, "requires": {"mute-stream": "~0.0.4"}, "dependencies": {"mute-stream": {"version": "0.0.7", "bundled": true}}}, "read-cmd-shim": {"version": "1.0.1", "bundled": true, "requires": {"graceful-fs": "^4.1.2"}}, "read-installed": {"version": "4.0.3", "bundled": true, "requires": {"debuglog": "^1.0.1", "graceful-fs": "^4.1.2", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "semver": "2 || 3 || 4 || 5", "slide": "~1.1.3", "util-extend": "^1.0.1"}, "dependencies": {"util-extend": {"version": "1.0.3", "bundled": true}}}, "read-package-json": {"version": "2.0.13", "bundled": true, "requires": {"glob": "^7.1.1", "graceful-fs": "^4.1.2", "json-parse-better-errors": "^1.0.1", "normalize-package-data": "^2.0.0", "slash": "^1.0.0"}, "dependencies": {"json-parse-better-errors": {"version": "1.0.1", "bundled": true}, "slash": {"version": "1.0.0", "bundled": true}}}, "read-package-tree": {"version": "5.1.6", "bundled": true, "requires": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "once": "^1.3.0", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0"}}, "readable-stream": {"version": "2.3.5", "bundled": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.0.3", "util-deprecate": "~1.0.1"}, "dependencies": {"core-util-is": {"version": "1.0.2", "bundled": true}, "isarray": {"version": "1.0.0", "bundled": true}, "process-nextick-args": {"version": "2.0.0", "bundled": true}, "string_decoder": {"version": "1.0.3", "bundled": true, "requires": {"safe-buffer": "~5.1.0"}}, "util-deprecate": {"version": "1.0.2", "bundled": true}}}, "readdir-scoped-modules": {"version": "1.0.2", "bundled": true, "requires": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "graceful-fs": "^4.1.2", "once": "^1.3.0"}}, "request": {"version": "2.83.0", "bundled": true, "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.6.0", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.1", "forever-agent": "~0.6.1", "form-data": "~2.3.1", "har-validator": "~5.0.3", "hawk": "~6.0.2", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "performance-now": "^2.1.0", "qs": "~6.5.1", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "uuid": "^3.1.0"}, "dependencies": {"aws-sign2": {"version": "0.7.0", "bundled": true}, "aws4": {"version": "1.6.0", "bundled": true}, "caseless": {"version": "0.12.0", "bundled": true}, "combined-stream": {"version": "1.0.5", "bundled": true, "requires": {"delayed-stream": "~1.0.0"}, "dependencies": {"delayed-stream": {"version": "1.0.0", "bundled": true}}}, "extend": {"version": "3.0.1", "bundled": true}, "forever-agent": {"version": "0.6.1", "bundled": true}, "form-data": {"version": "2.3.1", "bundled": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "dependencies": {"asynckit": {"version": "0.4.0", "bundled": true}}}, "har-validator": {"version": "5.0.3", "bundled": true, "requires": {"ajv": "^5.1.0", "har-schema": "^2.0.0"}, "dependencies": {"ajv": {"version": "5.2.3", "bundled": true, "requires": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "dependencies": {"co": {"version": "4.6.0", "bundled": true}, "fast-deep-equal": {"version": "1.0.0", "bundled": true}, "json-schema-traverse": {"version": "0.3.1", "bundled": true}, "json-stable-stringify": {"version": "1.0.1", "bundled": true, "requires": {"jsonify": "~0.0.0"}, "dependencies": {"jsonify": {"version": "0.0.0", "bundled": true}}}}}, "har-schema": {"version": "2.0.0", "bundled": true}}}, "hawk": {"version": "6.0.2", "bundled": true, "requires": {"boom": "4.x.x", "cryptiles": "3.x.x", "hoek": "4.x.x", "sntp": "2.x.x"}, "dependencies": {"boom": {"version": "4.3.1", "bundled": true, "requires": {"hoek": "4.x.x"}}, "cryptiles": {"version": "3.1.2", "bundled": true, "requires": {"boom": "5.x.x"}, "dependencies": {"boom": {"version": "5.2.0", "bundled": true, "requires": {"hoek": "4.x.x"}}}}, "hoek": {"version": "4.2.0", "bundled": true}, "sntp": {"version": "2.0.2", "bundled": true, "requires": {"hoek": "4.x.x"}}}}, "http-signature": {"version": "1.2.0", "bundled": true, "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true}, "jsprim": {"version": "1.4.1", "bundled": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}, "dependencies": {"extsprintf": {"version": "1.3.0", "bundled": true}, "json-schema": {"version": "0.2.3", "bundled": true}, "verror": {"version": "1.10.0", "bundled": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "dependencies": {"core-util-is": {"version": "1.0.2", "bundled": true}}}}}, "sshpk": {"version": "1.13.1", "bundled": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}, "dependencies": {"asn1": {"version": "0.2.3", "bundled": true}, "bcrypt-pbkdf": {"version": "1.0.1", "bundled": true, "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "dashdash": {"version": "1.14.1", "bundled": true, "requires": {"assert-plus": "^1.0.0"}}, "ecc-jsbn": {"version": "0.1.1", "bundled": true, "optional": true, "requires": {"jsbn": "~0.1.0"}}, "getpass": {"version": "0.1.7", "bundled": true, "requires": {"assert-plus": "^1.0.0"}}, "jsbn": {"version": "0.1.1", "bundled": true, "optional": true}, "tweetnacl": {"version": "0.14.5", "bundled": true, "optional": true}}}}}, "is-typedarray": {"version": "1.0.0", "bundled": true}, "isstream": {"version": "0.1.2", "bundled": true}, "json-stringify-safe": {"version": "5.0.1", "bundled": true}, "mime-types": {"version": "2.1.17", "bundled": true, "requires": {"mime-db": "~1.30.0"}, "dependencies": {"mime-db": {"version": "1.30.0", "bundled": true}}}, "oauth-sign": {"version": "0.8.2", "bundled": true}, "performance-now": {"version": "2.1.0", "bundled": true}, "qs": {"version": "6.5.1", "bundled": true}, "stringstream": {"version": "0.0.5", "bundled": true}, "tough-cookie": {"version": "2.3.3", "bundled": true, "requires": {"punycode": "^1.4.1"}, "dependencies": {"punycode": {"version": "1.4.1", "bundled": true}}}, "tunnel-agent": {"version": "0.6.0", "bundled": true, "requires": {"safe-buffer": "^5.0.1"}}}}, "retry": {"version": "0.10.1", "bundled": true}, "rimraf": {"version": "2.6.2", "bundled": true, "requires": {"glob": "^7.0.5"}}, "safe-buffer": {"version": "5.1.1", "bundled": true}, "semver": {"version": "5.5.0", "bundled": true}, "sha": {"version": "2.0.1", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}}, "slide": {"version": "1.1.6", "bundled": true}, "sorted-object": {"version": "2.0.1", "bundled": true}, "sorted-union-stream": {"version": "2.1.3", "bundled": true, "requires": {"from2": "^1.3.0", "stream-iterate": "^1.1.0"}, "dependencies": {"from2": {"version": "1.3.0", "bundled": true, "requires": {"inherits": "~2.0.1", "readable-stream": "~1.1.10"}, "dependencies": {"readable-stream": {"version": "1.1.14", "bundled": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}, "dependencies": {"core-util-is": {"version": "1.0.2", "bundled": true}, "isarray": {"version": "0.0.1", "bundled": true}, "string_decoder": {"version": "0.10.31", "bundled": true}}}}}, "stream-iterate": {"version": "1.2.0", "bundled": true, "requires": {"readable-stream": "^2.1.5", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}}}, "ssri": {"version": "5.2.4", "bundled": true, "requires": {"safe-buffer": "^5.1.1"}}, "strip-ansi": {"version": "4.0.0", "bundled": true, "requires": {"ansi-regex": "^3.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true}}}, "tar": {"version": "4.4.0", "bundled": true, "requires": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "dependencies": {"fs-minipass": {"version": "1.2.5", "bundled": true, "requires": {"minipass": "^2.2.1"}}, "minipass": {"version": "2.2.1", "bundled": true, "requires": {"yallist": "^3.0.0"}}, "minizlib": {"version": "1.1.0", "bundled": true, "requires": {"minipass": "^2.2.1"}}, "yallist": {"version": "3.0.2", "bundled": true}}}, "text-table": {"version": "0.2.0", "bundled": true}, "uid-number": {"version": "0.0.6", "bundled": true}, "umask": {"version": "1.1.0", "bundled": true}, "unique-filename": {"version": "1.1.0", "bundled": true, "requires": {"unique-slug": "^2.0.0"}, "dependencies": {"unique-slug": {"version": "2.0.0", "bundled": true, "requires": {"imurmurhash": "^0.1.4"}}}}, "unpipe": {"version": "1.0.0", "bundled": true}, "update-notifier": {"version": "2.3.0", "bundled": true, "requires": {"boxen": "^1.2.1", "chalk": "^2.0.1", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-installed-globally": "^0.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}, "dependencies": {"boxen": {"version": "1.2.1", "bundled": true, "requires": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^1.0.0"}, "dependencies": {"ansi-align": {"version": "2.0.0", "bundled": true, "requires": {"string-width": "^2.0.0"}}, "camelcase": {"version": "4.1.0", "bundled": true}, "cli-boxes": {"version": "1.0.0", "bundled": true}, "string-width": {"version": "2.1.1", "bundled": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "dependencies": {"is-fullwidth-code-point": {"version": "2.0.0", "bundled": true}}}, "term-size": {"version": "1.2.0", "bundled": true, "requires": {"execa": "^0.7.0"}, "dependencies": {"execa": {"version": "0.7.0", "bundled": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "bundled": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"shebang-command": {"version": "1.2.0", "bundled": true, "requires": {"shebang-regex": "^1.0.0"}, "dependencies": {"shebang-regex": {"version": "1.0.0", "bundled": true}}}}}, "get-stream": {"version": "3.0.0", "bundled": true}, "is-stream": {"version": "1.1.0", "bundled": true}, "npm-run-path": {"version": "2.0.2", "bundled": true, "requires": {"path-key": "^2.0.0"}, "dependencies": {"path-key": {"version": "2.0.1", "bundled": true}}}, "p-finally": {"version": "1.0.0", "bundled": true}, "signal-exit": {"version": "3.0.2", "bundled": true}, "strip-eof": {"version": "1.0.0", "bundled": true}}}}}, "widest-line": {"version": "1.0.0", "bundled": true, "requires": {"string-width": "^1.0.1"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}}}}}}}, "chalk": {"version": "2.1.0", "bundled": true, "requires": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.0", "bundled": true, "requires": {"color-convert": "^1.9.0"}, "dependencies": {"color-convert": {"version": "1.9.0", "bundled": true, "requires": {"color-name": "^1.1.1"}, "dependencies": {"color-name": {"version": "1.1.3", "bundled": true}}}}}, "escape-string-regexp": {"version": "1.0.5", "bundled": true}, "supports-color": {"version": "4.4.0", "bundled": true, "requires": {"has-flag": "^2.0.0"}, "dependencies": {"has-flag": {"version": "2.0.0", "bundled": true}}}}}, "configstore": {"version": "3.1.1", "bundled": true, "requires": {"dot-prop": "^4.1.0", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}, "dependencies": {"dot-prop": {"version": "4.2.0", "bundled": true, "requires": {"is-obj": "^1.0.0"}, "dependencies": {"is-obj": {"version": "1.0.1", "bundled": true}}}, "make-dir": {"version": "1.0.0", "bundled": true, "requires": {"pify": "^2.3.0"}, "dependencies": {"pify": {"version": "2.3.0", "bundled": true}}}, "unique-string": {"version": "1.0.0", "bundled": true, "requires": {"crypto-random-string": "^1.0.0"}, "dependencies": {"crypto-random-string": {"version": "1.0.0", "bundled": true}}}}}, "import-lazy": {"version": "2.1.0", "bundled": true}, "is-installed-globally": {"version": "0.1.0", "bundled": true, "requires": {"global-dirs": "^0.1.0", "is-path-inside": "^1.0.0"}, "dependencies": {"global-dirs": {"version": "0.1.0", "bundled": true, "requires": {"ini": "^1.3.4"}}, "is-path-inside": {"version": "1.0.0", "bundled": true, "requires": {"path-is-inside": "^1.0.1"}}}}, "is-npm": {"version": "1.0.0", "bundled": true}, "latest-version": {"version": "3.1.0", "bundled": true, "requires": {"package-json": "^4.0.0"}, "dependencies": {"package-json": {"version": "4.0.1", "bundled": true, "requires": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}, "dependencies": {"got": {"version": "6.7.1", "bundled": true, "requires": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "dependencies": {"create-error-class": {"version": "3.0.2", "bundled": true, "requires": {"capture-stack-trace": "^1.0.0"}, "dependencies": {"capture-stack-trace": {"version": "1.0.0", "bundled": true}}}, "duplexer3": {"version": "0.1.4", "bundled": true}, "get-stream": {"version": "3.0.0", "bundled": true}, "is-redirect": {"version": "1.0.0", "bundled": true}, "is-retry-allowed": {"version": "1.1.0", "bundled": true}, "is-stream": {"version": "1.1.0", "bundled": true}, "lowercase-keys": {"version": "1.0.0", "bundled": true}, "timed-out": {"version": "4.0.1", "bundled": true}, "unzip-response": {"version": "2.0.1", "bundled": true}, "url-parse-lax": {"version": "1.0.0", "bundled": true, "requires": {"prepend-http": "^1.0.1"}, "dependencies": {"prepend-http": {"version": "1.0.4", "bundled": true}}}}}, "registry-auth-token": {"version": "3.3.1", "bundled": true, "requires": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}, "dependencies": {"rc": {"version": "1.2.1", "bundled": true, "requires": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"deep-extend": {"version": "0.4.2", "bundled": true}, "minimist": {"version": "1.2.0", "bundled": true}, "strip-json-comments": {"version": "2.0.1", "bundled": true}}}}}, "registry-url": {"version": "3.1.0", "bundled": true, "requires": {"rc": "^1.0.1"}, "dependencies": {"rc": {"version": "1.2.1", "bundled": true, "requires": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"deep-extend": {"version": "0.4.2", "bundled": true}, "minimist": {"version": "1.2.0", "bundled": true}, "strip-json-comments": {"version": "2.0.1", "bundled": true}}}}}}}}}, "semver-diff": {"version": "2.1.0", "bundled": true, "requires": {"semver": "^5.0.3"}}, "xdg-basedir": {"version": "3.0.0", "bundled": true}}}, "uuid": {"version": "3.2.1", "bundled": true}, "validate-npm-package-license": {"version": "3.0.1", "bundled": true, "requires": {"spdx-correct": "~1.0.0", "spdx-expression-parse": "~1.0.0"}, "dependencies": {"spdx-correct": {"version": "1.0.2", "bundled": true, "requires": {"spdx-license-ids": "^1.0.2"}, "dependencies": {"spdx-license-ids": {"version": "1.2.2", "bundled": true}}}, "spdx-expression-parse": {"version": "1.0.4", "bundled": true}}}, "validate-npm-package-name": {"version": "3.0.0", "bundled": true, "requires": {"builtins": "^1.0.3"}, "dependencies": {"builtins": {"version": "1.0.3", "bundled": true}}}, "which": {"version": "1.3.0", "bundled": true, "requires": {"isexe": "^2.0.0"}, "dependencies": {"isexe": {"version": "2.0.0", "bundled": true}}}, "worker-farm": {"version": "1.5.4", "bundled": true, "requires": {"errno": "~0.1.7", "xtend": "~4.0.1"}, "dependencies": {"errno": {"version": "0.1.7", "bundled": true, "requires": {"prr": "~1.0.1"}, "dependencies": {"prr": {"version": "1.0.1", "bundled": true}}}, "xtend": {"version": "4.0.1", "bundled": true}}}, "wrappy": {"version": "1.0.2", "bundled": true}, "write-file-atomic": {"version": "2.3.0", "bundled": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "dependencies": {"signal-exit": {"version": "3.0.2", "bundled": true}}}}}, "npmlog": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/npmlog/-/npmlog-4.1.2.tgz", "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="}, "oauth-sign": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha1-Rqarfwrq2N6unsBWV4C31O/rnUM="}, "object-assign": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-3.0.0.tgz", "integrity": "sha1-m+3VygiXlJvKR+f/QIBi1Un1h/I="}, "object-copy": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "object-inspect": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.5.0.tgz", "integrity": "sha1-nYdsEeQPSFx5IVZwKBt2dIj5v+M="}, "object-keys": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.11.tgz", "integrity": "sha1-xUYBd4rVYPEULODgG8yotW0TQm0="}, "object-visit": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.0.tgz", "integrity": "sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w==", "requires": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}}, "object.defaults": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/object.defaults/-/object.defaults-1.1.0.tgz", "integrity": "sha1-On+GgzS0B96gbaFtiNXNKeQ1/s8=", "requires": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}}, "object.map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/object.map/-/object.map-1.0.1.tgz", "integrity": "sha1-z4Plncj8wK1fQlDh94s7gb2AHTc=", "requires": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}}, "object.omit": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz", "integrity": "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=", "requires": {"for-own": "^0.1.4", "is-extendable": "^0.1.1"}, "dependencies": {"for-own": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/for-own/-/for-own-0.1.5.tgz", "integrity": "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=", "requires": {"for-in": "^1.0.1"}}}}, "object.pick": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "requires": {"isobject": "^3.0.1"}}, "object.reduce": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/object.reduce/-/object.reduce-1.0.1.tgz", "integrity": "sha1-b+NI8qx/oPlcpiEiZZkJaCW7A60=", "requires": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "optimist": {"version": "0.3.7", "resolved": "https://registry.npmjs.org/optimist/-/optimist-0.3.7.tgz", "integrity": "sha1-yQlBrVnkJzMokjB00s8ufLxuwNk=", "requires": {"wordwrap": "~0.0.2"}, "dependencies": {"wordwrap": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz", "integrity": "sha1-o9XabNXAvAAI03I0u68b7WMFkQc="}}}, "optionator": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "integrity": "sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=", "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.4", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "wordwrap": "~1.0.0"}}, "ordered-read-streams": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/ordered-read-streams/-/ordered-read-streams-1.0.1.tgz", "integrity": "sha1-d8DLN8QVJdZBZtmQ/61+xqDhNj4=", "requires": {"readable-stream": "^2.0.1"}}, "os-browserify": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="}, "os-homedir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="}, "os-locale": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/os-locale/-/os-locale-1.4.0.tgz", "integrity": "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=", "requires": {"lcid": "^1.0.0"}}, "os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="}, "osenv": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/osenv/-/osenv-0.1.5.tgz", "integrity": "sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==", "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "p-limit": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-1.2.0.tgz", "integrity": "sha512-Y/OtIaXtUPr4/YpMv1pCL5L5ed0rumAaAeBSj12F+bSlMdys7i8oQF/GUJmfpTS/QoaRrS/k6pma29haJpsMng==", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true}, "pako": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/pako/-/pako-1.0.6.tgz", "integrity": "sha512-lQe48YPsMJAig+yngZ87Lus+NF+3mtu7DVOBu6b/gHO1YpKwIj5AWjZ/TOS7i46HD/UixzWb1zeWDZfGZ3iYcg=="}, "param-case": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/param-case/-/param-case-2.1.1.tgz", "integrity": "sha1-35T9jPZTHs915r75oIWPvHK+Ikc=", "requires": {"no-case": "^2.2.0"}}, "parents": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parents/-/parents-1.0.1.tgz", "integrity": "sha1-/t1NK/GTp3dF/nHjcdc8MwfZx1E=", "requires": {"path-platform": "~0.11.15"}}, "parse-asn1": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.1.tgz", "integrity": "sha512-KPx7flKXg775zZpnp9SxJlz00gTd4BmJ2yJufSc44gMCRrRQ7NSzAcSJQfifuOLgW6bEi+ftrALtsgALeB2Adw==", "requires": {"asn1.js": "^4.0.0", "browserify-aes": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3"}}, "parse-filepath": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/parse-filepath/-/parse-filepath-1.0.2.tgz", "integrity": "sha1-pjISf1Oq89FYdvWHLz/6x2PWyJE=", "requires": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}}, "parse-glob": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz", "integrity": "sha1-ssN2z7EfNVE7rdFz7wu246OIORw=", "requires": {"glob-base": "^0.3.0", "is-dotfile": "^1.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.0"}, "dependencies": {"is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}}}, "parse-json": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "requires": {"error-ex": "^1.2.0"}}, "parse-ms": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parse-ms/-/parse-ms-1.0.1.tgz", "integrity": "sha1-VjRtR0nXjyNDDKDHE4UK75GqNh0="}, "parse-passwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY="}, "pascalcase": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="}, "path-browserify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.0.tgz", "integrity": "sha1-oLhwcpquIUAFt9UDLsLLuw+0RRo="}, "path-dirname": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="}, "path-exists": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz", "integrity": "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=", "requires": {"pinkie-promise": "^2.0.0"}}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-is-inside": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "path-parse": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.5.tgz", "integrity": "sha1-PBrfhx6pzWyUMbbqK9dKD/BVxME="}, "path-platform": {"version": "0.11.15", "resolved": "https://registry.npmjs.org/path-platform/-/path-platform-0.11.15.tgz", "integrity": "sha1-6GQhf3TDaFDwhSt43Hv31KVyG/I="}, "path-root": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/path-root/-/path-root-0.1.1.tgz", "integrity": "sha1-mkpoFMrBwM1zNgqV8yCDyOpHRbc=", "requires": {"path-root-regex": "^0.1.0"}}, "path-root-regex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/path-root-regex/-/path-root-regex-0.1.2.tgz", "integrity": "sha1-v8zcjfWxLcUsi0PsONGNcsBLqW0="}, "path-type": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz", "integrity": "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=", "requires": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "pbkdf2": {"version": "3.0.14", "resolved": "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.0.14.tgz", "integrity": "sha512-gjsZW9O34fm0R7PaLHRJmLLVfSoesxztjPjE9o6R+qtVJij90ltg1joIovN9GKrRW3t1PzhDDG3UMEMFfZ+1wA==", "requires": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="}, "pify": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="}, "pinkie": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="}, "pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-1.0.0.tgz", "integrity": "sha1-ektQio1bstYp1EcFb/TpyTFM89Q=", "dev": true, "requires": {"find-up": "^1.0.0"}}, "plugin-error": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/plugin-error/-/plugin-error-0.1.2.tgz", "integrity": "sha1-O5uzM1zPAPQl4HQ34ZJ2ln2kes4=", "requires": {"ansi-cyan": "^0.1.1", "ansi-red": "^0.1.1", "arr-diff": "^1.0.1", "arr-union": "^2.0.1", "extend-shallow": "^1.1.2"}, "dependencies": {"arr-diff": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-1.1.0.tgz", "integrity": "sha1-aHwydYFjWI/vfeezb6vklesaOZo=", "requires": {"arr-flatten": "^1.0.1", "array-slice": "^0.2.3"}}, "arr-union": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/arr-union/-/arr-union-2.1.0.tgz", "integrity": "sha1-IPnqtexw9cfSFbEHexw5Fh0pLH0="}, "array-slice": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/array-slice/-/array-slice-0.2.3.tgz", "integrity": "sha1-3Tz7gO15c6dRF82sabC5nshhhvU="}, "extend-shallow": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-1.1.4.tgz", "integrity": "sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE=", "requires": {"kind-of": "^1.1.0"}}, "kind-of": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-1.1.0.tgz", "integrity": "sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ="}}}, "plur": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/plur/-/plur-2.1.2.tgz", "integrity": "sha1-dIJFLBoPUI4+NE6uwxLJHCncZVo=", "requires": {"irregular-plurals": "^1.0.0"}}, "pluralize": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/pluralize/-/pluralize-7.0.0.tgz", "integrity": "sha512-ARhBOdzS3e41FbkW/XWrTEtukqqLoK5+Z/4UeDaLuSW+39JPeFgs4gCGqsrJHVZX0fUrx//4OF0K1CUGwlIFow==", "dev": true}, "posix-character-classes": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="}, "prebuild-install": {"version": "2.5.3", "resolved": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.5.3.tgz", "integrity": "sha512-/rI36cN2g7vDQnKWN8Uzupi++KjyqS9iS+/fpwG4Ea8d0Pip0PQ5bshUNzVwt+/D2MRfhVAplYMMvWLqWrCF/g==", "requires": {"detect-libc": "^1.0.3", "expand-template": "^1.0.2", "github-from-package": "0.0.0", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "node-abi": "^2.2.0", "noop-logger": "^0.1.1", "npmlog": "^4.0.1", "os-homedir": "^1.0.1", "pump": "^2.0.1", "rc": "^1.1.6", "simple-get": "^2.7.0", "tar-fs": "^1.13.0", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0"}}, "prelude-ls": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="}, "preserve": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz", "integrity": "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks="}, "pretty-hrtime": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/pretty-hrtime/-/pretty-hrtime-1.0.3.tgz", "integrity": "sha1-t+PqQkNaTJsnWdmeDyAesZWALuE="}, "pretty-ms": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/pretty-ms/-/pretty-ms-3.1.0.tgz", "integrity": "sha1-6crJx2v27lL+lC3ZxsQhMVOxKIE=", "requires": {"parse-ms": "^1.0.0", "plur": "^2.1.2"}}, "private": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/private/-/private-0.1.8.tgz", "integrity": "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8="}, "process": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="}, "process-nextick-args": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.0.tgz", "integrity": "sha1-o31zL0JxtKsa0HDTVQjoKQeI/6o="}, "progress": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/progress/-/progress-2.0.0.tgz", "integrity": "sha1-ihvjZr+Pwj2yvSPxDG/pILQ4nR8=", "dev": true}, "promise": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz", "integrity": "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==", "dev": true, "requires": {"asap": "~2.0.3"}}, "prop-types": {"version": "15.6.1", "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.6.1.tgz", "integrity": "sha512-4ec7bY1Y66LymSUOH/zARVYObB23AT2h8cf6e/O6ZALB/N0sqZFEx7rq6EYPX2MkOdKORuooI/H5k9TlR4q7kQ==", "dev": true, "requires": {"fbjs": "^0.8.16", "loose-envify": "^1.3.1", "object-assign": "^4.1.1"}, "dependencies": {"object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}}}, "pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true}, "public-encrypt": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.2.tgz", "integrity": "sha512-4kJ5Esocg8X3h8YgJsKAuoesBgB7mqH3eowiDzMUPKiRDDE7E/BqqZD1hnTByIaAFiwAw246YEltSq7tdrOH0Q==", "requires": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1"}}, "pump": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz", "integrity": "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/pumpify/-/pumpify-1.4.0.tgz", "integrity": "sha512-2kmNR9ry+Pf45opRVirpNuIFotsxUGLaYqxIwuR77AYrYRMuFCz9eryHBS52L360O+NcR383CL4QYlMKPq4zYA==", "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4="}, "qs": {"version": "6.5.1", "resolved": "https://registry.npmjs.org/qs/-/qs-6.5.1.tgz", "integrity": "sha512-eRzhrN1WSINYCDCbrz796z37LOe3m5tmW7RQf6oBntukAG1nmovJvhnwHHRMAfeoItc1m2Hk02WER2aQ/iqs+A=="}, "querystring": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="}, "querystring-es3": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="}, "quote-stream": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/quote-stream/-/quote-stream-0.0.0.tgz", "integrity": "sha1-zeKelMQJsW4Z3HCYuJtmWPlyHTs=", "dev": true, "requires": {"minimist": "0.0.8", "through2": "~0.4.1"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "minimist": {"version": "0.0.8", "resolved": "http://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "object-keys": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-0.4.0.tgz", "integrity": "sha1-KKaq50KN0sOpLz2V8hM13SBOAzY=", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "through2": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/through2/-/through2-0.4.2.tgz", "integrity": "sha1-2/WGYDEVHsg1K7bE22SiKSqEC5s=", "dev": true, "requires": {"readable-stream": "~1.0.17", "xtend": "~2.1.1"}}, "xtend": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-2.1.2.tgz", "integrity": "sha1-bv7MKk2tjmlixJAbM3znuoe10os=", "dev": true, "requires": {"object-keys": "~0.4.0"}}}}, "randomatic": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/randomatic/-/randomatic-1.1.7.tgz", "integrity": "sha512-D5JUjPyJbaJDkuAazpVnSfVkLlpeO3wDlPROTMLGKG1zMFNFRgrciKo1ltz/AzNTkqE0HzDx655QOL51N06how==", "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "requires": {"is-buffer": "^1.1.5"}}}}, "randombytes": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.6.tgz", "integrity": "sha512-CIQ5OFxf4Jou6uOKe9t1AOgqpeU5fd70A8NPdHSGeYXqXsPe6peOwI0cUl88RWZ6sP1vPMV3avd/R6cZ5/sP1A==", "requires": {"safe-buffer": "^5.1.0"}}, "randomfill": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz", "integrity": "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==", "requires": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "raw-body": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.3.tgz", "integrity": "sha512-9esiElv1BrZoI3rCDuOuKCBRbuApGGaDPQfjSflGxdy4oyzqghxu6klEkkVIvBje+FF0BX9coEv8KqW6X/7njw==", "requires": {"bytes": "3.0.0", "http-errors": "1.6.3", "iconv-lite": "0.4.23", "unpipe": "1.0.0"}, "dependencies": {"iconv-lite": {"version": "0.4.23", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.23.tgz", "integrity": "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}}}, "rc": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/rc/-/rc-1.2.6.tgz", "integrity": "sha1-6xiYnG1PTxYsOZ953dKfODVWgJI=", "requires": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}}, "read-only-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/read-only-stream/-/read-only-stream-2.0.0.tgz", "integrity": "sha1-JyT9aoET1zdkrCiNQ4YnDB2/F/A=", "requires": {"readable-stream": "^2.0.2"}}, "read-pkg": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz", "integrity": "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=", "requires": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}}, "read-pkg-up": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz", "integrity": "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=", "requires": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}}, "read-vinyl-file-stream": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/read-vinyl-file-stream/-/read-vinyl-file-stream-2.0.3.tgz", "integrity": "sha1-FOObSs4DOtUtvDlnGLHV3KbYKRU=", "requires": {"node-stream": "^1.5.0", "through2": "^2.0.1"}}, "readable-stream": {"version": "2.3.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "readdirp": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz", "integrity": "sha1-TtCtBg3zBzMAxIRANz9y0cxkLXg=", "requires": {"graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "readable-stream": "^2.0.2", "set-immediate-shim": "^1.0.1"}}, "rechoir": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=", "requires": {"resolve": "^1.1.6"}}, "redent": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz", "integrity": "sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=", "requires": {"indent-string": "^2.1.0", "strip-indent": "^1.0.1"}}, "regenerate": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/regenerate/-/regenerate-1.3.3.tgz", "integrity": "sha1-DDNtOYBVPXVcObWGrjsgqknIK38="}, "regenerator-runtime": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="}, "regenerator-transform": {"version": "0.10.1", "resolved": "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.10.1.tgz", "integrity": "sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0=", "requires": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}}, "regex-cache": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz", "integrity": "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ==", "requires": {"is-equal-shallow": "^0.1.3"}}, "regex-not": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "regexpp": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/regexpp/-/regexpp-1.1.0.tgz", "integrity": "sha512-LOPw8FpgdQF9etWMaAfG/WRthIdXJGYp4mJ2Jgn/2lpkbod9jPn0t9UqN7AxBOKNfzRbYyVfgc7Vk4t/MpnXgw==", "dev": true}, "regexpu-core": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-2.0.0.tgz", "integrity": "sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=", "requires": {"regenerate": "^1.2.1", "regjsgen": "^0.2.0", "regjsparser": "^0.1.4"}}, "regjsgen": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.2.0.tgz", "integrity": "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="}, "regjsparser": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.5.tgz", "integrity": "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=", "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="}}}, "regl": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/regl/-/regl-1.3.1.tgz", "integrity": "sha1-KZXmOnmExSDvLaD28QJ/cFEzgUA="}, "relateurl": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="}, "remove-bom-buffer": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/remove-bom-buffer/-/remove-bom-buffer-3.0.0.tgz", "integrity": "sha512-8v2rWhaakv18qcvNeli2mZ/TMTL2nEyAKRvzo1WtnZBl15SHyEhrCu2/xKlJyUFKHiHgfXIyuY6g2dObJJycXQ==", "requires": {"is-buffer": "^1.1.5", "is-utf8": "^0.2.1"}}, "remove-bom-stream": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/remove-bom-stream/-/remove-bom-stream-1.2.0.tgz", "integrity": "sha1-BfGlk/FuQuH7kOv1nejlaVJflSM=", "requires": {"remove-bom-buffer": "^3.0.0", "safe-buffer": "^5.1.0", "through2": "^2.0.3"}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="}, "repeat-element": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.2.tgz", "integrity": "sha1-7wiaF40Ug7quTZPrmLT55OEdmQo="}, "repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc="}, "repeating": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz", "integrity": "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=", "requires": {"is-finite": "^1.0.0"}}, "replace-ext": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-0.0.1.tgz", "integrity": "sha1-KbvZIHinOfC8zitO5B6DeVNSKSQ="}, "replace-homedir": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/replace-homedir/-/replace-homedir-1.0.0.tgz", "integrity": "sha1-6H9tUTuSjd6AgmDBK+f+xv9ueYw=", "requires": {"homedir-polyfill": "^1.0.1", "is-absolute": "^1.0.0", "remove-trailing-separator": "^1.1.0"}}, "request": {"version": "2.85.0", "resolved": "https://registry.npmjs.org/request/-/request-2.85.0.tgz", "integrity": "sha512-8H7Ehijd4js+s6wuVPLjwORxD4zeuyjYugprdOXlPSqaApmL/QOy+EB/beICHVCHkGMKNh5rvihb5ov+IDw4mg==", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.6.0", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.1", "forever-agent": "~0.6.1", "form-data": "~2.3.1", "har-validator": "~5.0.3", "hawk": "~6.0.2", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "performance-now": "^2.1.0", "qs": "~6.5.1", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "uuid": "^3.1.0"}}, "require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="}, "require-main-filename": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz", "integrity": "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="}, "require-relative": {"version": "0.8.7", "resolved": "https://registry.npmjs.org/require-relative/-/require-relative-0.8.7.tgz", "integrity": "sha1-eZlTn8ngR6N5KPoZb44VY9q9Nt4="}, "require-uncached": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/require-uncached/-/require-uncached-1.0.3.tgz", "integrity": "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=", "dev": true, "requires": {"caller-path": "^0.1.0", "resolve-from": "^1.0.0"}}, "resolve": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.7.0.tgz", "integrity": "sha1-K99TdIESByhd8N9lK3jxGKuPPF4=", "requires": {"path-parse": "^1.0.5"}}, "resolve-dir": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=", "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}}, "resolve-from": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz", "integrity": "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=", "dev": true}, "resolve-options": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/resolve-options/-/resolve-options-1.1.0.tgz", "integrity": "sha1-MrueOcBtZzONyTeMDW1gdFZq0TE=", "requires": {"value-or-function": "^3.0.0"}}, "resolve-url": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="}, "restore-cursor": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "dev": true, "requires": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}, "resumer": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/resumer/-/resumer-0.0.0.tgz", "integrity": "sha1-8ej0YeQGS6Oegq883CqMiT0HZ1k=", "requires": {"through": "~2.3.4"}}, "ret": {"version": "0.1.15", "resolved": "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="}, "rfile": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/rfile/-/rfile-1.0.0.tgz", "integrity": "sha1-WXCM+Qyh50xUw8/Fw2/bmBBDUmE=", "requires": {"callsite": "~1.0.0", "resolve": "~0.3.0"}, "dependencies": {"resolve": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/resolve/-/resolve-0.3.1.tgz", "integrity": "sha1-NMY0R8ZkxwWY0cmxJvxDsqJDEKQ="}}}, "rimraf": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.2.tgz", "integrity": "sha512-lreewLK/BlghmxtfH36YYVg1i8IAce4TI7oao75I1g245+6BctqTVQiBP3YUJ9C6DQOXJmkYR9X9fCLtCOJc5w==", "requires": {"glob": "^7.0.5"}}, "ripemd160": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.1.tgz", "integrity": "sha1-D0WEKVxTo2KK9+bXmsohzlfRxuc=", "requires": {"hash-base": "^2.0.0", "inherits": "^2.0.1"}, "dependencies": {"hash-base": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hash-base/-/hash-base-2.0.2.tgz", "integrity": "sha1-ZuodhW206KVHDK32/OI65SRO8uE=", "requires": {"inherits": "^2.0.1"}}}}, "rollup": {"version": "0.57.1", "resolved": "https://registry.npmjs.org/rollup/-/rollup-0.57.1.tgz", "integrity": "sha512-I18GBqP0qJoJC1K1osYjreqA8VAKovxuI3I81RSk0Dmr4TgloI0tAULjZaox8OsJ+n7XRrhH6i0G2By/pj1LCA==", "requires": {"@types/acorn": "^4.0.3", "acorn": "^5.5.3", "acorn-dynamic-import": "^3.0.0", "date-time": "^2.1.0", "is-reference": "^1.1.0", "locate-character": "^2.0.5", "pretty-ms": "^3.1.0", "require-relative": "^0.8.7", "rollup-pluginutils": "^2.0.1", "signal-exit": "^3.0.2", "sourcemap-codec": "^1.4.1"}}, "rollup-plugin-babel": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/rollup-plugin-babel/-/rollup-plugin-babel-3.0.4.tgz", "integrity": "sha512-TGhQbliTZnRoUhd2214K3r4KJUBu9J1DPzcrAnkluVXOVrveU9OvAaYQ16KyOmujAoq+LMC1+x6YF2xBrU7t+g==", "dev": true, "requires": {"rollup-pluginutils": "^1.5.0"}, "dependencies": {"estree-walker": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-0.2.1.tgz", "integrity": "sha1-va/oCVOD2EFNXcLs9MkXO225QS4=", "dev": true}, "rollup-pluginutils": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/rollup-pluginutils/-/rollup-pluginutils-1.5.2.tgz", "integrity": "sha1-HhVud4+UtyVb+hs9AXi+j1xVJAg=", "dev": true, "requires": {"estree-walker": "^0.2.1", "minimatch": "^3.0.2"}}}}, "rollup-plugin-browserify-transform": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/rollup-plugin-browserify-transform/-/rollup-plugin-browserify-transform-1.0.1.tgz", "integrity": "sha512-z6jlG14YYNO0mxz7JcjodFro8wkMiE9MvsXXSWwC4buZ7jlg5HgblBRGrS9ZeynqBY0j7/lYAKWCajn5xZr7sg==", "dev": true, "requires": {"concat-stream": "^1.5.1", "object-assign": "^4.0.1", "rollup-pluginutils": "^2.0.1", "source-map-url": "^0.4.0"}, "dependencies": {"object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}}}, "rollup-plugin-commonjs": {"version": "9.1.0", "resolved": "https://registry.npmjs.org/rollup-plugin-commonjs/-/rollup-plugin-commonjs-9.1.0.tgz", "integrity": "sha512-NrfE0g30QljNCnlJr7I2Xguz+44mh0dCxvfxwLnCwtaCK2LwFUp1zzAs8MQuOfhH4mRskqsjfOwGUap/L+WtEw==", "requires": {"estree-walker": "^0.5.1", "magic-string": "^0.22.4", "resolve": "^1.5.0", "rollup-pluginutils": "^2.0.1"}, "dependencies": {"estree-walker": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-0.5.1.tgz", "integrity": "sha512-7HgCgz1axW7w5aOvgOQkoR1RMBkllygJrssU3BvymKQ95lxXYv6Pon17fBRDm9qhkvXZGijOULoSF9ShOk/ZLg=="}}}, "rollup-plugin-inline-js": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/rollup-plugin-inline-js/-/rollup-plugin-inline-js-0.1.0.tgz", "integrity": "sha512-6si/0gt+DxbUQpkU+vvTBkaq0HlAf/NIkvkVA6ZpHCFUZ/UjxV0NxiHFQSZv8xVW+Nohzt+HzvkuSmTe8RDGNQ==", "requires": {"inline-js": "^0.6.0"}}, "rollup-plugin-node-resolve": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/rollup-plugin-node-resolve/-/rollup-plugin-node-resolve-3.3.0.tgz", "integrity": "sha512-9zHGr3oUJq6G+X0oRMYlzid9fXicBdiydhwGChdyeNRGPcN/majtegApRKHLR5drboUvEWU+QeUmGTyEZQs3WA==", "requires": {"builtin-modules": "^2.0.0", "is-module": "^1.0.0", "resolve": "^1.1.6"}, "dependencies": {"builtin-modules": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/builtin-modules/-/builtin-modules-2.0.0.tgz", "integrity": "sha512-3U5kUA5VPsRUA3nofm/BXX7GVHKfxz0hOBAPxXrIvHzlDRkQVqEn6yi8QJegxl4LzOHLdvb7XF5dVawa/VVYBg=="}}}, "rollup-pluginutils": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/rollup-pluginutils/-/rollup-pluginutils-2.0.1.tgz", "integrity": "sha1-fslbNXP2VDpGpkYb2afFRFJdD8A=", "requires": {"estree-walker": "^0.3.0", "micromatch": "^2.3.11"}, "dependencies": {"arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "requires": {"arr-flatten": "^1.0.1"}}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM="}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "requires": {"is-posix-bracket": "^0.1.0"}}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "requires": {"is-extglob": "^1.0.0"}}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "requires": {"is-extglob": "^1.0.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}}}}, "rollupify": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/rollupify/-/rollupify-0.4.0.tgz", "integrity": "sha512-uez1k5qBc5AWskE+Al2oOYjfKcLh7BHASOY67v1825n2zvdt+GD17v0WPCd689fftYRVkVSvQLdvVdLg0hIlMA==", "requires": {"denodeify": "^1.2.1", "noop-fn": "^1.0.0", "rollup": "^0.43.0", "through2": "^2.0.1"}, "dependencies": {"rollup": {"version": "0.43.1", "resolved": "https://registry.npmjs.org/rollup/-/rollup-0.43.1.tgz", "integrity": "sha512-Y/7r6bE1sSpvBoMNZpSWKGHM2q67YxzBADbsfVqMf+nM6SbsQnU7BPMdrE3m/GiT46BQMN8BwFGhjukQP8Yy0A==", "requires": {"source-map-support": "^0.4.0", "weak": "^1.0.1"}}}}, "ruglify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ruglify/-/ruglify-1.0.0.tgz", "integrity": "sha1-3Ikw4qlUSidDAcyZcldMDQmGtnU=", "requires": {"rfile": "~1.0", "uglify-js": "~2.2"}, "dependencies": {"source-map": {"version": "0.1.43", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "requires": {"amdefine": ">=0.0.4"}}, "uglify-js": {"version": "2.2.5", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.2.5.tgz", "integrity": "sha1-puAqcNg5eSuXgEiLe4sYTAlcmcc=", "requires": {"optimist": "~0.3.5", "source-map": "~0.1.7"}}}}, "run-async": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/run-async/-/run-async-2.3.0.tgz", "integrity": "sha1-A3GrSuC91yDUFm19/aZP96RFpsA=", "dev": true, "requires": {"is-promise": "^2.1.0"}}, "rx-lite": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/rx-lite/-/rx-lite-4.0.8.tgz", "integrity": "sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=", "dev": true}, "rx-lite-aggregates": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz", "integrity": "sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=", "dev": true, "requires": {"rx-lite": "*"}}, "safe-buffer": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "integrity": "sha1-iTMSr2myEj3vcfV4iQAWce6yyFM="}, "safe-regex": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "semver": {"version": "4.3.6", "resolved": "https://registry.npmjs.org/semver/-/semver-4.3.6.tgz", "integrity": "sha1-MAvG4OhjdPe6YQaLWx7NV/xlMto="}, "semver-greatest-satisfied-range": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/semver-greatest-satisfied-range/-/semver-greatest-satisfied-range-1.1.0.tgz", "integrity": "sha1-E+jCZYq5aRywzXEJMkAoDTb3els=", "requires": {"sver-compat": "^1.5.0"}}, "set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="}, "set-immediate-shim": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz", "integrity": "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E="}, "set-value": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-value/-/set-value-2.0.0.tgz", "integrity": "sha1-ca5KiPD+77v1LR6mBPP7MV67YnQ=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true}, "setprototypeof": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="}, "sha.js": {"version": "2.4.11", "resolved": "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==", "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "shallow-copy": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/shallow-copy/-/shallow-copy-0.0.1.tgz", "integrity": "sha1-QV9CcC1z2BAzApLMXuhurhoRoXA="}, "shasum": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/shasum/-/shasum-1.0.2.tgz", "integrity": "sha1-5wEjENj0F/TetXEhUOVni4euVl8=", "requires": {"json-stable-stringify": "~0.0.0", "sha.js": "~2.4.4"}, "dependencies": {"json-stable-stringify": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-0.0.1.tgz", "integrity": "sha1-YRwj6BTbN1Un34URk9tZ3Sryf0U=", "requires": {"jsonify": "~0.0.0"}}}}, "shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "shell-quote": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.1.tgz", "integrity": "sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c=", "requires": {"array-filter": "~0.0.0", "array-map": "~0.0.0", "array-reduce": "~0.0.0", "jsonify": "~0.0.0"}}, "shortest": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/shortest/-/shortest-0.0.0.tgz", "integrity": "sha1-3EyNByLnqSCOx9IJjkzajKxuVn0="}, "sigmund": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz", "integrity": "sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA="}, "signal-exit": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0="}, "simple-concat": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.0.tgz", "integrity": "sha1-c0TLuLbib7J9ZrL8hvn21Zl1IcY="}, "simple-get": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/simple-get/-/simple-get-2.8.1.tgz", "integrity": "sha512-lSSHRSw3mQNUGPAYRqo7xy9dhKmxFXIjLjp4KHpf99GEH2VH7C3AM+Qfx6du6jhfUi6Vm7XnbEVEf7Wb6N8jRw==", "requires": {"decompress-response": "^3.3.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "slash": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="}, "slice-ansi": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-1.0.0.tgz", "integrity": "sha512-POqxBK6Lb3q6s047D/XsDVNPnF9Dl8JSaqe9h9lURl0OdNqy/ujDrOiIHtsqXMGbWWTIomRzAMaTyawAU//Reg==", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0"}, "dependencies": {"is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}}}, "snapdragon": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "sntp": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/sntp/-/sntp-2.1.0.tgz", "integrity": "sha512-FL1b58BDrqS3A11lJ0zEdnJ3UOKqVxawAkF3k7F0CVN7VQ34aZrV+G8BZ1WC9ZL7NyrwsW0oviwsWDgRuVYtJg==", "requires": {"hoek": "4.x.x"}}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "optional": true}, "source-map-resolve": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.1.tgz", "integrity": "sha1-etD1k/IoFZjoVN+A8ZquS5LXoRo=", "requires": {"atob": "^2.0.0", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.4.18", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.18.tgz", "integrity": "sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8=", "requires": {"source-map": "^0.5.6"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "source-map-url": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="}, "sourcemap-codec": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.1.tgz", "integrity": "sha512-hX1eNBNuilj8yfFnECh0DzLgwKpBLMIvmhgEhixXNui8lMLBInTI8Kyxt++RwJnMNu7cAUo635L2+N1TxMJCzA=="}, "sparkles": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/sparkles/-/sparkles-1.0.0.tgz", "integrity": "sha1-Gsu/tZJDbRC76PeFt8xvgoFQEsM="}, "spdx-correct": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.0.0.tgz", "integrity": "sha512-N19o9z5cEyc8yQQPukRCZ9EUmb4HUpnrmaL/fxS2pBo2jbfcFRVuFZ/oFC+vZz0MNNk0h80iMn5/S6qGZOL5+g==", "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.1.0.tgz", "integrity": "sha512-4K1NsmrlCU1JJgUrtgEeTVyfx8VaYea9J9LvARxhbHtVtohPs/gFGG5yy49beySjlIMhhXZ4QqujIZEfS4l6Cg=="}, "spdx-expression-parse": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.0.tgz", "integrity": "sha512-Yg6D3XpRD4kkOmTpdgbUiEJFKghJH03fiC1OPll5h/0sO6neh2jqRDVHOQ4o/LMea0tgCkbMgea5ip/e+MkWyg==", "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.0.tgz", "integrity": "sha512-2+EPwgbnmOIl8HjGBXXMd9NAu02vLjOO1nWw4kmeRDFyHn+M/ETfHxQUK0oXg8ctgVnl9t3rosNVsZ1jG61nDA=="}, "split-string": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "requires": {"extend-shallow": "^3.0.0"}}, "split2": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/split2/-/split2-2.2.0.tgz", "integrity": "sha512-RAb22TG39LhI31MbreBgIuKiIKhVsawfTgEGqKHTK87aG+ul/PB8Sqoi3I7kVdRWiCfrKxK3uo4/YUkpNvhPbw==", "requires": {"through2": "^2.0.2"}}, "sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "sshpk": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.14.1.tgz", "integrity": "sha1-Ew9Zde3a2WPx1W+SuaxsUfqfg+s=", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}}, "stack-trace": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.9.tgz", "integrity": "sha1-qPbq7KkGdMMz58Q5U/J1tFFRBpU="}, "static-eval": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/static-eval/-/static-eval-2.0.0.tgz", "integrity": "sha1-DoIfiSaEfe97S1DNpdVcBKmxOGQ=", "requires": {"escodegen": "^1.8.1"}}, "static-extend": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "static-module": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/static-module/-/static-module-1.5.0.tgz", "integrity": "sha1-J9qYg8QajNCSNvhC8MHrxu32PYY=", "dev": true, "requires": {"concat-stream": "~1.6.0", "duplexer2": "~0.0.2", "escodegen": "~1.3.2", "falafel": "^2.1.0", "has": "^1.0.0", "object-inspect": "~0.4.0", "quote-stream": "~0.0.0", "readable-stream": "~1.0.27-1", "shallow-copy": "~0.0.1", "static-eval": "~0.2.0", "through2": "~0.4.1"}, "dependencies": {"escodegen": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/escodegen/-/escodegen-1.3.3.tgz", "integrity": "sha1-8CQBb1qI4Eb9EgBQVek5gC5sXyM=", "dev": true, "requires": {"esprima": "~1.1.1", "estraverse": "~1.5.0", "esutils": "~1.0.0", "source-map": "~0.1.33"}}, "esprima": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/esprima/-/esprima-1.1.1.tgz", "integrity": "sha1-W28VR/TRAuZw4UDFCb5ncdautUk=", "dev": true}, "estraverse": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-1.5.1.tgz", "integrity": "sha1-hno+jlip+EYYr7bC3bzZFrfLr3E=", "dev": true}, "esutils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/esutils/-/esutils-1.0.0.tgz", "integrity": "sha1-gVHTWOIMisx/t0XnRywAJf5JZXA=", "dev": true}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "object-inspect": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-0.4.0.tgz", "integrity": "sha1-9RV8EWwUVbJDsG7pdwM5LFrYn+w=", "dev": true}, "object-keys": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-0.4.0.tgz", "integrity": "sha1-KKaq50KN0sOpLz2V8hM13SBOAzY=", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "source-map": {"version": "0.1.43", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "dev": true, "optional": true, "requires": {"amdefine": ">=0.0.4"}}, "static-eval": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/static-eval/-/static-eval-0.2.4.tgz", "integrity": "sha1-t9NNg4k3uWn5ZBygfUj47eJj6ns=", "dev": true, "requires": {"escodegen": "~0.0.24"}, "dependencies": {"escodegen": {"version": "0.0.28", "resolved": "https://registry.npmjs.org/escodegen/-/escodegen-0.0.28.tgz", "integrity": "sha1-Dk/xcV8yh3XWyrUaxEpAbNer/9M=", "dev": true, "requires": {"esprima": "~1.0.2", "estraverse": "~1.3.0", "source-map": ">= 0.1.2"}}, "esprima": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/esprima/-/esprima-1.0.4.tgz", "integrity": "sha1-n1V+CPw7TSbs6d00+Pv0drYlha0=", "dev": true}, "estraverse": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-1.3.2.tgz", "integrity": "sha1-N8K4k+8T1yPydth41g2FNRUqbEI=", "dev": true}}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "through2": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/through2/-/through2-0.4.2.tgz", "integrity": "sha1-2/WGYDEVHsg1K7bE22SiKSqEC5s=", "dev": true, "requires": {"readable-stream": "~1.0.17", "xtend": "~2.1.1"}}, "xtend": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-2.1.2.tgz", "integrity": "sha1-bv7MKk2tjmlixJAbM3znuoe10os=", "dev": true, "requires": {"object-keys": "~0.4.0"}}}}, "statuses": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "stream-browserify": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.1.tgz", "integrity": "sha1-ZiZu5fm9uZQKTkUUyvtDu3Hlyds=", "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "stream-buffers": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/stream-buffers/-/stream-buffers-3.0.2.tgz", "integrity": "sha512-DQi1h8VEBA/lURbSwFtEHnSTb9s2/pwLEaFuNhXwy1Dx3Sa0lOuYT2yNUr4/j2fs8oCAMANtrZ5OrPZtyVs3MQ=="}, "stream-combiner": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/stream-combiner/-/stream-combiner-0.0.4.tgz", "integrity": "sha1-TV5DPBhSYd3mI8o/RMWGvPXErRQ=", "requires": {"duplexer": "~0.1.1"}}, "stream-combiner2": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/stream-combiner2/-/stream-combiner2-1.1.1.tgz", "integrity": "sha1-+02KFCDqNidk4hrUeAOXvry0HL4=", "requires": {"duplexer2": "~0.1.0", "readable-stream": "^2.0.2"}, "dependencies": {"duplexer2": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/duplexer2/-/duplexer2-0.1.4.tgz", "integrity": "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=", "requires": {"readable-stream": "^2.0.2"}}}}, "stream-exhaust": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/stream-exhaust/-/stream-exhaust-1.0.2.tgz", "integrity": "sha512-b/qaq/GlBK5xaq1yrK9/zFcyRSTNxmcZwFLGSTG0mXgZl/4Z6GgiyYOXOvY7N3eEvFRAG1bkDRz5EPGSvPYQlw=="}, "stream-http": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/stream-http/-/stream-http-2.8.1.tgz", "integrity": "sha512-cQ0jo17BLca2r0GfRdZKYAGLU6JRoIWxqSOakUMuKOT6MOK7AAlE856L33QuDmAy/eeOrhLee3dZKX0Uadu93A==", "requires": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.3", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "stream-shift": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.0.tgz", "integrity": "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI="}, "stream-splicer": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/stream-splicer/-/stream-splicer-2.0.0.tgz", "integrity": "sha1-G2O+Q4oTPktnHMGTUZdgAXWRDYM=", "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.2"}}, "string-to-stream": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string-to-stream/-/string-to-stream-1.1.1.tgz", "integrity": "sha512-QySF2+3Rwq0SdO3s7BAp4x+c3qsClpPQ6abAmb0DGViiSBAkT5kL6JT2iyzEVP+T1SmzHrQD1TwlP9QAHCc+Sw==", "requires": {"inherits": "^2.0.1", "readable-stream": "^2.1.0"}}, "string-width": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "string.prototype.trim": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.1.2.tgz", "integrity": "sha1-0E3iyJ4Tf019IG8Ia17S+ua+jOo=", "requires": {"define-properties": "^1.1.2", "es-abstract": "^1.5.0", "function-bind": "^1.0.2"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}, "stringify": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/stringify/-/stringify-5.2.0.tgz", "integrity": "sha1-8iux8xrBCwRk637mDxqp9x5UZRI=", "requires": {"browserify-transform-tools": "^1.5.3", "html-minifier": "3.5.2"}}, "stringify-object": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/stringify-object/-/stringify-object-3.2.2.tgz", "integrity": "sha512-O696NF21oLiDy8PhpWu8AEqoZHw++QW6mUv0UvKZe8gWSdSvMXkiLufK7OmnP27Dro4GU5kb9U7JIO0mBuCRQg==", "dev": true, "requires": {"get-own-enumerable-property-symbols": "^2.0.1", "is-obj": "^1.0.1", "is-regexp": "^1.0.0"}}, "stringstream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz", "integrity": "sha1-TkhM1N5aC7vuGORjB3EKioFiGHg="}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "requires": {"ansi-regex": "^2.0.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "dev": true}, "strip-bom-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom-stream/-/strip-bom-stream-2.0.0.tgz", "integrity": "sha1-+H217yYT9paKpUWr/h7HKLaoKco=", "requires": {"first-chunk-stream": "^2.0.0", "strip-bom": "^2.0.0"}, "dependencies": {"first-chunk-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/first-chunk-stream/-/first-chunk-stream-2.0.0.tgz", "integrity": "sha1-G97NuOCDwGZLkZRVgVd6Q6nzHXA=", "requires": {"readable-stream": "^2.0.2"}}, "strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "requires": {"is-utf8": "^0.2.0"}}}}, "strip-indent": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz", "integrity": "sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=", "requires": {"get-stdin": "^4.0.1"}}, "strip-json-comments": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo="}, "subarg": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/subarg/-/subarg-1.0.0.tgz", "integrity": "sha1-9izxdYHplrSPyWVpn1TAauJouNI=", "requires": {"minimist": "^1.1.0"}}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="}, "sver-compat": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/sver-compat/-/sver-compat-1.5.0.tgz", "integrity": "sha1-PPh9/rTQe0o/FIJ7wYaz/QxkXNg=", "requires": {"es6-iterator": "^2.0.1", "es6-symbol": "^3.1.1"}}, "syntax-error": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/syntax-error/-/syntax-error-1.4.0.tgz", "integrity": "sha512-YPPlu67mdnHGTup2A8ff7BC2Pjq0e0Yp/IyTFN03zWO0RcK07uLcbi7C2KpGR2FvWbaB0+bfE27a+sBKebSo7w==", "requires": {"acorn-node": "^1.2.0"}}, "table": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/table/-/table-4.0.2.tgz", "integrity": "sha512-UUkEAPdSGxtRpiV9ozJ5cMTtYiqz7Ni1OGqLXRCynrvzdtR1p+cfOWe2RJLwvUG8hNanaSRjecIqwOjqeatDsA==", "dev": true, "requires": {"ajv": "^5.2.3", "ajv-keywords": "^2.1.0", "chalk": "^2.1.0", "lodash": "^4.17.4", "slice-ansi": "1.0.0", "string-width": "^2.1.1"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.0.tgz", "integrity": "sha512-Wr/w0f4o9LuE7K53cD0qmbAMM+2XNLzR29vFn5hqko4sxGlUsyy363NvmyGIyk5tpe9cjTr9SJYbysEyPkRnFw==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "string-width": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}, "supports-color": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.4.0.tgz", "integrity": "sha512-zjaXglF5nnWpsq470jSv6P9DwPvgLkuapYmfDm3JWOm0vkNTVF2tI4UrN2r6jH1qM/uc/WtxYY1hYoA2dOKj5w==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "tape": {"version": "4.9.0", "resolved": "https://registry.npmjs.org/tape/-/tape-4.9.0.tgz", "integrity": "sha1-hVwINgOVEzcJ000/v57zQetzymo=", "requires": {"deep-equal": "~1.0.1", "defined": "~1.0.0", "for-each": "~0.3.2", "function-bind": "~1.1.1", "glob": "~7.1.2", "has": "~1.0.1", "inherits": "~2.0.3", "minimist": "~1.2.0", "object-inspect": "~1.5.0", "resolve": "~1.5.0", "resumer": "~0.0.0", "string.prototype.trim": "~1.1.2", "through": "~2.3.8"}, "dependencies": {"resolve": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.5.0.tgz", "integrity": "sha1-HwmsznlsmnYlefMbLBzEw83fnzY=", "requires": {"path-parse": "^1.0.5"}}}}, "tar": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/tar/-/tar-2.2.1.tgz", "integrity": "sha1-jk0qJWwOIYXGsYrWlK7JaLg8sdE=", "requires": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}}, "tar-fs": {"version": "1.16.0", "resolved": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.16.0.tgz", "integrity": "sha512-I9rb6v7mjWLtOfCau9eH5L7sLJyU2BnxtEZRQ5Mt+eRKmf1F0ohXmT/Jc3fr52kDvjJ/HV5MH3soQfPL5bQ0Yg==", "requires": {"chownr": "^1.0.1", "mkdirp": "^0.5.1", "pump": "^1.0.0", "tar-stream": "^1.1.2"}, "dependencies": {"pump": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/pump/-/pump-1.0.3.tgz", "integrity": "sha512-8k0JupWme55+9tCVE+FS5ULT3K6AbgqrGa58lTT49RpyfwwcGedHqaC5LlQNdEAumn/wFsu6aPwkuPMioy8kqw==", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "tar-stream": {"version": "1.5.5", "resolved": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.5.tgz", "integrity": "sha512-mQdgLPc/Vjfr3VWqWbfxW8yQNiJCbAZ+Gf6GDu1Cy0bdb33ofyiNGBtAY96jHFhDuivCwgW1H9DgTON+INiXgg==", "requires": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}}, "text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true}, "through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "through2": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/through2/-/through2-2.0.3.tgz", "integrity": "sha1-AARWmzfHx0ujnEPzzteNGtlBQL4=", "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "through2-filter": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/through2-filter/-/through2-filter-2.0.0.tgz", "integrity": "sha1-YLxVoNrLdghdsfna6Zq0P4PWIuw=", "requires": {"through2": "~2.0.0", "xtend": "~4.0.0"}}, "tildify": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/tildify/-/tildify-1.2.0.tgz", "integrity": "sha1-3OwD9V3Km3qj5bBPIYF+tW5jWIo=", "dev": true, "requires": {"os-homedir": "^1.0.0"}}, "time-stamp": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/time-stamp/-/time-stamp-1.1.0.tgz", "integrity": "sha1-dkpaEa9QVhkhsTPztE5hhofg9cM="}, "time-zone": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/time-zone/-/time-zone-1.0.0.tgz", "integrity": "sha1-mcW/VZWJZq9tBtg73zgA3IL67F0="}, "timers-browserify": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/timers-browserify/-/timers-browserify-1.4.2.tgz", "integrity": "sha1-ycWLV1voQHN1y14kYtrO50NZ9B0=", "requires": {"process": "~0.11.0"}}, "tmp": {"version": "0.0.33", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "dev": true, "requires": {"os-tmpdir": "~1.0.2"}}, "to-absolute-glob": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/to-absolute-glob/-/to-absolute-glob-2.0.2.tgz", "integrity": "sha1-GGX0PZ50sIItufFFt4z/fQ98hJs=", "requires": {"is-absolute": "^1.0.0", "is-negated-glob": "^1.0.0"}}, "to-arraybuffer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="}, "to-fast-properties": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz", "integrity": "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="}, "to-object-path": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "to-through": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/to-through/-/to-through-2.0.0.tgz", "integrity": "sha1-/JKtq6ByZHvAtn1rA2ZKoZUJOvY=", "requires": {"through2": "^2.0.3"}}, "tough-cookie": {"version": "2.3.4", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.4.tgz", "integrity": "sha512-TZ6TTfI5NtZnuyy/Kecv+CnoROnyXn2DN97LontgQpCwsX2XyLYCC0ENhYkehSOwAp8rTQKc/NUIF7BkQ5rKLA==", "requires": {"punycode": "^1.4.1"}}, "treeify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/treeify/-/treeify-1.1.0.tgz", "integrity": "sha512-1m4RA7xVAJrSGrrXGs0L3YTwyvBs2S8PbRHaLZAkFw7JR8oIFwYtysxlBZhYIa7xSyiYJKZ3iGrrk55cGA3i9A=="}, "trim-newlines": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz", "integrity": "sha1-WIeWa7WCpFA6QetST301ARgVphM="}, "trim-right": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM="}, "tty-browserify": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.1.tgz", "integrity": "sha512-C3TaO7K81YvjCgQH9Q1S3R3P3BtN3RIM8n+OvX4il1K1zgE8ZhI0op7kClgkxtutIE8hQrcrHBXvIheqKUUCxw=="}, "tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "optional": true}, "type-check": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "requires": {"prelude-ls": "~1.1.2"}}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="}, "ua-parser-js": {"version": "0.7.17", "resolved": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.17.tgz", "integrity": "sha512-uRdSdu1oA1rncCQL7sCj8vSyZkgtL7faaw9Tc9rZ3mGgraQ7+Pdx7w5mnOSF3gw9ZNG6oc+KXfkon3bKuROm0g==", "dev": true}, "uglify-js": {"version": "2.4.24", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.4.24.tgz", "integrity": "sha1-+tV1XB4Vd2WLsG/5q25UjJW+vW4=", "requires": {"async": "~0.2.6", "source-map": "0.1.34", "uglify-to-browserify": "~1.0.0", "yargs": "~3.5.4"}, "dependencies": {"source-map": {"version": "0.1.34", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.34.tgz", "integrity": "sha1-p8/omux7FoLDsZjQrPtH19CQVms=", "requires": {"amdefine": ">=0.0.4"}}}}, "uglify-to-browserify": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "integrity": "sha1-bgkk1r2mta/jSeOabWMoUKD4grc="}, "umd": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/umd/-/umd-3.0.3.tgz", "integrity": "sha512-4IcGSufhFshvLNcMCV80UnQVlZ5pMOC8mvNPForqwA4+lzYQuetTESLDQkeLmihq8bRcnpbQa48Wb8Lh16/xow=="}, "unc-path-regex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha1-5z3T17DXxe2G+6xrCufYxqadUPo="}, "undertaker": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/undertaker/-/undertaker-1.2.0.tgz", "integrity": "sha1-M52kZGJS0ILcN45wgGcpl1DhG0k=", "requires": {"arr-flatten": "^1.0.1", "arr-map": "^2.0.0", "bach": "^1.0.0", "collection-map": "^1.0.0", "es6-weak-map": "^2.0.1", "last-run": "^1.1.0", "object.defaults": "^1.0.0", "object.reduce": "^1.0.0", "undertaker-registry": "^1.0.0"}}, "undertaker-registry": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/undertaker-registry/-/undertaker-registry-1.0.1.tgz", "integrity": "sha1-XkvaMI5KiirlhPm5pDWaSZglzFA="}, "union-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/union-value/-/union-value-1.0.0.tgz", "integrity": "sha1-XHHDTLW61dzr4+oM0IIHulqhrqQ=", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}}}, "unique-stream": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/unique-stream/-/unique-stream-2.2.1.tgz", "integrity": "sha1-WqADz76Uxf+GbE59ZouxxNuts2k=", "requires": {"json-stable-stringify": "^1.0.0", "through2-filter": "^2.0.0"}}, "universalify": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.1.tgz", "integrity": "sha1-+nG63UQ3r0wUiEHjs7Fl+enlkLc="}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "unset-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}}}, "upath": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/upath/-/upath-1.0.4.tgz", "integrity": "sha512-d4SJySNBXDaQp+DPrziv3xGS6w3d2Xt69FijJr86zMPBy23JEloMCEOUBBzuN7xCtjLCnmB9tI/z7SBCahHBOw=="}, "upper-case": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz", "integrity": "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="}, "urix": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="}, "url": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/url/-/url-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="}}}, "use": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/use/-/use-3.1.0.tgz", "integrity": "sha1-FHFr8D/f79AwQK71jYtLhfOnxUQ=", "requires": {"kind-of": "^6.0.2"}}, "util": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/util/-/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "requires": {"inherits": "2.0.1"}, "dependencies": {"inherits": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="}}}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "uuid": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.2.1.tgz", "integrity": "sha512-jZnMwlb9Iku/O3smGWvZhauCf6cvvpKi4BKRiliS3cxnI+Gz9j5MEpTz2UFuXiKPJocb7gnsLHwiS05ige5BEA=="}, "v8flags": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/v8flags/-/v8flags-3.0.2.tgz", "integrity": "sha512-6sgSKoFw1UpUPd3cFdF7QGnrH6tDeBgW1F3v9gy8gLY0mlbiBXq8soy8aQpY6xeeCjH5K+JvC62Acp7gtl7wWA==", "requires": {"homedir-polyfill": "^1.0.1"}}, "validate-npm-package-license": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.3.tgz", "integrity": "sha512-63ZOUnL4SIXj4L0NixR3L1lcjO38crAbgrTpl28t8jjrfuiOBL5Iygm+60qPs/KsZGzPNg6Smnc/oY16QTjF0g==", "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "value-or-function": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/value-or-function/-/value-or-function-3.0.0.tgz", "integrity": "sha1-HCQ6ULWVwb5Up1S/7OhWO5/42BM="}, "verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "vinyl": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-2.1.0.tgz", "integrity": "sha1-Ah+cLPlR1rk5lDyJ617lrdT9kkw=", "requires": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}, "dependencies": {"clone": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.1.tgz", "integrity": "sha1-0hfR6WERjjrJpLi7oyhVU79kfNs="}, "clone-stats": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz", "integrity": "sha1-s3gt/4u1R04Yuba/D9/ngvh3doA="}, "replace-ext": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.0.tgz", "integrity": "sha1-3mMSg3P8v3w8z6TeWkgMRaZ5WOs="}}}, "vinyl-buffer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/vinyl-buffer/-/vinyl-buffer-1.0.1.tgz", "integrity": "sha1-lsGjR5uMU5JULGEgKQE7Wyf4i78=", "requires": {"bl": "^1.2.1", "through2": "^2.0.3"}}, "vinyl-file": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/vinyl-file/-/vinyl-file-2.0.0.tgz", "integrity": "sha1-p+v1/779obfRjRQPyweyI++2dRo=", "requires": {"graceful-fs": "^4.1.2", "pify": "^2.3.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0", "strip-bom-stream": "^2.0.0", "vinyl": "^1.1.0"}, "dependencies": {"strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "requires": {"is-utf8": "^0.2.0"}}, "vinyl": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-1.2.0.tgz", "integrity": "sha1-XIgDbPVl5d8FVYv8kR+GVt8hiIQ=", "requires": {"clone": "^1.0.0", "clone-stats": "^0.0.1", "replace-ext": "0.0.1"}}}}, "vinyl-fs": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/vinyl-fs/-/vinyl-fs-3.0.2.tgz", "integrity": "sha512-AUSFda1OukBwuLPBTbyuO4IRWgfXmqC4UTW0f8xrCa8Hkv9oyIU+NSqBlgfOLZRoUt7cHdo75hKQghCywpIyIw==", "requires": {"fs-mkdirp-stream": "^1.0.0", "glob-stream": "^6.1.0", "graceful-fs": "^4.0.0", "is-valid-glob": "^1.0.0", "lazystream": "^1.0.0", "lead": "^1.0.0", "object.assign": "^4.0.4", "pumpify": "^1.3.5", "readable-stream": "^2.3.3", "remove-bom-buffer": "^3.0.0", "remove-bom-stream": "^1.2.0", "resolve-options": "^1.1.0", "through2": "^2.0.0", "to-through": "^2.0.0", "value-or-function": "^3.0.0", "vinyl": "^2.0.0", "vinyl-sourcemap": "^1.1.0"}, "dependencies": {"clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="}, "clone-stats": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz", "integrity": "sha1-s3gt/4u1R04Yuba/D9/ngvh3doA="}, "replace-ext": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.0.tgz", "integrity": "sha1-3mMSg3P8v3w8z6TeWkgMRaZ5WOs="}, "vinyl": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-2.1.0.tgz", "integrity": "sha1-Ah+cLPlR1rk5lDyJ617lrdT9kkw=", "requires": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}}}}, "vinyl-source-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/vinyl-source-stream/-/vinyl-source-stream-2.0.0.tgz", "integrity": "sha1-84pa+53R6Ttl1VBGmsYYKsT1S44=", "requires": {"through2": "^2.0.3", "vinyl": "^2.1.0"}, "dependencies": {"clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="}, "clone-stats": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz", "integrity": "sha1-s3gt/4u1R04Yuba/D9/ngvh3doA="}, "replace-ext": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.0.tgz", "integrity": "sha1-3mMSg3P8v3w8z6TeWkgMRaZ5WOs="}, "vinyl": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-2.1.0.tgz", "integrity": "sha1-Ah+cLPlR1rk5lDyJ617lrdT9kkw=", "requires": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}}}}, "vinyl-sourcemap": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/vinyl-sourcemap/-/vinyl-sourcemap-1.1.0.tgz", "integrity": "sha1-kqgAWTo4cDqM2xHYswCtS+Y7PhY=", "requires": {"append-buffer": "^1.0.2", "convert-source-map": "^1.5.0", "graceful-fs": "^4.1.6", "normalize-path": "^2.1.1", "now-and-later": "^2.0.0", "remove-bom-buffer": "^3.0.0", "vinyl": "^2.0.0"}, "dependencies": {"clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="}, "clone-stats": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-1.0.0.tgz", "integrity": "sha1-s3gt/4u1R04Yuba/D9/ngvh3doA="}, "replace-ext": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-1.0.0.tgz", "integrity": "sha1-3mMSg3P8v3w8z6TeWkgMRaZ5WOs="}, "vinyl": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-2.1.0.tgz", "integrity": "sha1-Ah+cLPlR1rk5lDyJ617lrdT9kkw=", "requires": {"clone": "^2.1.1", "clone-buffer": "^1.0.0", "clone-stats": "^1.0.0", "cloneable-readable": "^1.0.0", "remove-trailing-separator": "^1.0.1", "replace-ext": "^1.0.0"}}}}, "vinyl-transform": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/vinyl-transform/-/vinyl-transform-1.0.0.tgz", "integrity": "sha1-BQ5OUVogdzz0uvjc0h794XioOvY=", "requires": {"bl": "~0.7.0", "new-from": "0.0.3", "through2": "~0.4.1"}, "dependencies": {"bl": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/bl/-/bl-0.7.0.tgz", "integrity": "sha1-P7BnBgKsKHjrdw3CA58YNr5irls=", "requires": {"readable-stream": "~1.0.2"}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "object-keys": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-0.4.0.tgz", "integrity": "sha1-KKaq50KN0sOpLz2V8hM13SBOAzY="}, "readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}, "through2": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/through2/-/through2-0.4.2.tgz", "integrity": "sha1-2/WGYDEVHsg1K7bE22SiKSqEC5s=", "requires": {"readable-stream": "~1.0.17", "xtend": "~2.1.1"}}, "xtend": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-2.1.2.tgz", "integrity": "sha1-bv7MKk2tjmlixJAbM3znuoe10os=", "requires": {"object-keys": "~0.4.0"}}}}, "vlq": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/vlq/-/vlq-0.2.3.tgz", "integrity": "sha512-DRibZL6DsNhIgYQ+wNdWDL2SL3bKPlVrRiBqV5yuMm++op8W4kGFtaQfCs4KEJn0wBZcHVHJ3eoywX8983k1ow=="}, "vm-browserify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/vm-browserify/-/vm-browserify-1.0.1.tgz", "integrity": "sha512-EqzLchIMYLBjRPoqVsEkZOa/4Vr2RfOWbd58F+I/Gj79AYTrsseMunxbbSkbYfrqZaXSuPBBXNSOhtJgg0PpmA=="}, "weak": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/weak/-/weak-1.0.1.tgz", "integrity": "sha1-q5mqswcGlZqgIAy4z1RbucszuZ4=", "optional": true, "requires": {"bindings": "^1.2.1", "nan": "^2.0.5"}}, "whatwg-fetch": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-2.0.4.tgz", "integrity": "sha512-dcQ1GWpOD/eEQ97k66aiEVpNnapVj90/+R+SXTPYGHpYBBypfKJEQjLrvMZ7YXbKm21gXd4NcuxUTjiv1YtLng==", "dev": true}, "which": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/which/-/which-1.3.0.tgz", "integrity": "sha1-/wS9/AEO5UfXgL7DjhrBwnd9JTo=", "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/which-module/-/which-module-1.0.0.tgz", "integrity": "sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8="}, "which-pm-runs": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/which-pm-runs/-/which-pm-runs-1.0.0.tgz", "integrity": "sha1-Zws6+8VS4LVd9rd4DKdGFfI60cs="}, "wide-align": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.2.tgz", "integrity": "sha512-ijDLlyQ7s6x1JgCLur53osjm/UXUYD9+0PbYKrBsYisYXzCxN+HC3mYDNy/dWdmf3AwqwU3CXwDCvsNgGK1S0w==", "requires": {"string-width": "^1.0.2"}}, "window-size": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "integrity": "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0="}, "wordwrap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus="}, "wrap-ansi": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz", "integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/write/-/write-0.2.1.tgz", "integrity": "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=", "dev": true, "requires": {"mkdirp": "^0.5.1"}}, "xml-char-classes": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/xml-char-classes/-/xml-char-classes-1.0.0.tgz", "integrity": "sha1-ZGV4SKIP/F31g6Qq2KJ3tFErvE0="}, "xtend": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68="}, "y18n": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz", "integrity": "sha1-bRX7qITAhnnA136I53WegR4H+kE="}, "yallist": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "yargs": {"version": "3.5.4", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.5.4.tgz", "integrity": "sha1-2K/49mXpTDS9JZvevRv68N3TU2E=", "requires": {"camelcase": "^1.0.2", "decamelize": "^1.0.0", "window-size": "0.1.0", "wordwrap": "0.0.2"}, "dependencies": {"wordwrap": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8="}}}, "yargs-parser": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-5.0.0.tgz", "integrity": "sha1-J17PDX/+Bcd+ZOfIbkzZS/DhIoo=", "requires": {"camelcase": "^3.0.0"}, "dependencies": {"camelcase": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz", "integrity": "sha1-MvxLn82vhF/N9+c7uXysImHwqwo="}}}}}