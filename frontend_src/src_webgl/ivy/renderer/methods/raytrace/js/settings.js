// Settings provider 
// for soft shadows renderer
// 

import dat from 'dat.gui';

let sheet = document.createElement('style')
sheet.innerHTML = ".dg { z-index: 10000 !important; }";
document.body.appendChild(sheet);

const gui = new dat.GUI();

/*
var settings = { 
    x: -1404, 
    y: 5698.5, 
    z: 6625, 
    power: 4.425, 
    shelfGap: 31.02, 
    iterations: 14,
    cameraz: 1961.4//3000
};
*/

var settings = { 
    x: -2330.25, 
    y: 7860, 
    z: 7860, 
    power: 0.455,
    shelfGap: 0,
    iterations: 14,
    cameraz: 262.965
};

let shadowSource = gui.addFolder("Shadow source");
let sceneSettings = gui.addFolder("Scene settings");

shadowSource.add(settings, 'x').min(-4000).max(24000).step(0.25);
shadowSource.add(settings, 'y').min(-4000).max(24000).step(0.25);
shadowSource.add(settings, 'z').min(-4000).max(24000).step(0.5);
shadowSource.add(settings, 'power').min(0).max(2).step(0.0001); // prev max 10
shadowSource.add(settings, 'iterations').min(0).max(30).step(1);

sceneSettings.add(settings, 'shelfGap').min(0).max(2000).step(0.005);
sceneSettings.add(settings, 'cameraz').min(0).max(7000).step(0.005);

export { settings, shadowSource as gui };