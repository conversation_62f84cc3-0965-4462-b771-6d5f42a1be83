precision mediump float;

varying vec3 rayPosition;
varying vec3 rayDirection;

uniform vec4 shadowSource;
uniform float shadowPower;

uniform vec3 shelfSize;
uniform vec4 idents[20];

uniform float shelfGap;

#pragma glslify: unionElements = require('glsl-sdf-ops/union')
#pragma glslify: box = require('glsl-sdf-box') 

float vmax (vec2 v) {
	return max(v.x, v.y);
}

float boxFast (vec2 p, vec2 b) {
	return vmax(abs(p)-b);
}

float fOpIntersectionRound (float a, float b, float r) {
	vec2 u = max(vec2(r + a,r + b), vec2(0));
	return min(-r, max (a, b)) + length(u);
}

vec2 scene (vec3 p) {

    p.y -= shelfSize.y - 20.;
    p.z -= shelfSize.z;

    float scena = 0.;
    vec3 ss = shelfSize;

    ss.y-=20.;

    float  szafka = box(p + vec3(0, 0, 0), ss);
    float podloga = dot(p - vec3(0, -shelfSize.y - shelfGap, -shelfGap), vec3(0, 1., 0));
    float  sciana = dot(p - vec3(0, 0, -(shelfSize.z + shelfGap)), vec3(0, 0, 1.));

    scena = unionElements(sciana, podloga);

    for(int i = 0; i < 20; i++) {
        float renders = dot(idents[i], vec4(1.0));
        if(renders != 0.0) {
            vec2 position = idents[i].xy;
            vec2 size = idents[i].zw;
            szafka = max(-boxFast(p.xy + position, size), szafka);
        }
    }
    
    scena = unionElements(scena, szafka);
    return vec2(scena, 0);
}

#pragma glslify: raytrace = require('glsl-raytrace', map = scene, steps = 140)
#pragma glslify: normal = require('glsl-sdf-normal', map = scene)

float shadowSoft (vec3 ro, vec3 rd, float mint, float maxt, float k )
{
    float res = 2.5;
    float ph = 0.0;
    float t = 0.1;
    for (int i = 0; i < 26; ++i)
    {
        float h = scene(ro + rd*t).x;
        if( h < 0.001 )
            return .0;
        float y = h * h / (2. * ph);
        float d = sqrt(h*h-y*y);
        res = min(res,k*d/max(0.0,t-y) );
        ph = h;
        t += h;
    }
    return res;
}

vec3 shade (vec3 pos, vec3 nrm, vec4 light)
{
    vec3 toLight = light.xyz - pos;
    float toLightLen = length(toLight);
    toLight = normalize(toLight);
    float comb = 2.0;
    float vis = shadowSoft(pos, toLight, .625, toLightLen, shadowPower);
    return vec3(vis);
}

void main () {
    vec3 color = vec3(0.,0.,0.);
    vec3 rd, ro;

    ro = rayPosition;
    rd = rayDirection;
    
    vec2 t = raytrace(rayPosition, rd, 35000., 0.001);

    if (t.x > 0.5) {
        vec3 pos = ro + rd * t.x;
        vec3 nor = normal(pos);
        vec3 shaded = shade(pos, nor, vec4(shadowSource.xyz, shadowPower));
        color = shaded;
        color = vec3(dot(color, vec3(.3, .9, .0)));
    }

    gl_FragColor.rgb = color;
    gl_FragColor.a   = 1.0 - smoothstep(1.0, 0.0, length(color));
}