precision mediump float;

// Front projection

#define clip 35000.0

attribute vec2 position;
attribute vec2 uvs;

varying vec4 ndc; // normalizedDeviceCoords
varying vec3 rayDirection;
varying vec3 rayPosition;

uniform vec3 cameraPosition;
uniform vec2 warp;

void main() {
    vec2 tmp = uvs;
    tmp = (tmp * 2.0) - vec2(1.0,0.0);
    ndc = vec4(tmp, 1.0, 1.0) * clip;
    rayDirection = vec3(0,0,-1);
    rayPosition = vec3(0);
    rayPosition.z += cameraPosition.z;
    rayPosition.xy += ndc.xy * warp;
    gl_Position = vec4(position, 0.0, 1.0);
}