export class Canvas {

    constructor({
        width,
        height,
        transparent = true,
        alpha = true,
        debug = false
    }) {

        let self = this;
        let canvasElement = document.createElement('canvas');

        canvasElement.width = width;
        canvasElement.height = height;
        canvasElement.transparent = transparent;
       // canvasElement.setAttribute('id', 'flatShadowCanvas');

        if(debug) {
            document.body.appendChild(canvasElement);
            canvasElement.style['z-index'] = "10000 !important;";
            canvasElement.style.position = "fixed";
            canvasElement.style.top = "0px";

        }
        
        return { 
            creator: self,
            element: canvasElement
        }
    }

    debug() {
        /*
        canvas.style['z-index']= 1000;
        canvas.style['pointer-events'] = 'none';
        canvas.style['position']= 'fixed';
        canvas.style['bottom']= '0px';
        */
    }
}