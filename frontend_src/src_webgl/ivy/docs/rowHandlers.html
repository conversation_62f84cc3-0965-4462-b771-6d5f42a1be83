<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="content-type" content="text/html;charset=utf-8">
  <title>rowHandlers.js</title>
  <link rel="stylesheet" href="pycco.css">
</head>
<body>
<div id='container'>
  <div id="background"></div>
  <div class='section'>
    <div class='docs'><h1>rowHandlers.js</h1></div>
  </div>
  <div class='clearall'>
  <div class='section' id='section-0'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-0'>#</a>
      </div>
      
    </div>
    <div class='code'>
      <div class="highlight"><pre><span></span><span class="nx">module</span><span class="p">.</span><span class="nx">exports</span> <span class="o">=</span> <span class="p">{</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-1'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-1'>#</a>
      </div>
      <p>get onscreen coordinates of each handler</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>    <span class="nx">getCoordinates</span><span class="o">:</span> <span class="kd">function</span><span class="p">(</span><span class="nx">object</span><span class="p">,</span> <span class="nx">row</span><span class="p">,</span> <span class="nx">camera</span><span class="p">,</span> <span class="nx">renderer</span><span class="p">,</span> <span class="nx">div</span><span class="p">,</span> <span class="nx">ivy</span><span class="p">,</span> <span class="nx">positionRight</span><span class="p">,</span> <span class="nx">isMobile</span><span class="p">)</span> <span class="p">{</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-2'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-2'>#</a>
      </div>
      <p>if row should be shown</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>        <span class="k">if</span> <span class="p">(</span><span class="nx">row</span> <span class="o">&lt;=</span> <span class="nx">ivy</span><span class="p">.</span><span class="nx">rows</span><span class="p">)</span> <span class="p">{</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-3'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-3'>#</a>
      </div>
      <p>get screen coordinates from local</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>            <span class="kd">var</span> <span class="nx">element</span> <span class="o">=</span> <span class="nx">object</span><span class="p">;</span>
            <span class="kd">var</span> <span class="nx">screenVector</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">THREE</span><span class="p">.</span><span class="nx">Vector3</span><span class="p">();</span>
            <span class="nx">element</span><span class="p">.</span><span class="nx">localToWorld</span><span class="p">(</span><span class="nx">screenVector</span><span class="p">);</span>
            <span class="nx">screenVector</span><span class="p">.</span><span class="nx">x</span> <span class="o">=</span> <span class="nx">screenVector</span><span class="p">.</span><span class="nx">x</span><span class="p">;</span>
            <span class="nx">screenVector</span><span class="p">.</span><span class="nx">y</span> <span class="o">=</span> <span class="nx">screenVector</span><span class="p">.</span><span class="nx">y</span><span class="p">;</span>

            <span class="nx">screenVector</span><span class="p">.</span><span class="nx">project</span><span class="p">(</span><span class="nx">camera</span><span class="p">);</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-4'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-4'>#</a>
      </div>
      <p>multiply * 2 to get position of the side of the furniture</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>            <span class="kd">var</span> <span class="nx">posx</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">round</span><span class="p">((</span><span class="nx">screenVector</span><span class="p">.</span><span class="nx">x</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="nx">renderer</span><span class="p">.</span><span class="nx">domElement</span><span class="p">.</span><span class="nx">offsetWidth</span> <span class="o">/</span> <span class="mi">2</span><span class="p">);</span>
            <span class="k">if</span> <span class="p">(</span><span class="nx">positionRight</span> <span class="o">==</span> <span class="kc">null</span> <span class="o">||</span> <span class="nx">positionRight</span> <span class="o">==</span> <span class="mi">0</span> <span class="o">||</span> <span class="nx">positionRight</span> <span class="o">&lt;</span> <span class="nx">posx</span><span class="p">)</span> <span class="p">{</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-5'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-5'>#</a>
      </div>
      <p>fix position by pixels, mobile need to be a bit more to the side</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>                <span class="k">if</span> <span class="p">(</span><span class="nx">isMobile</span><span class="p">)</span> <span class="p">{</span>
                    <span class="nx">positionRight</span> <span class="o">=</span> <span class="nx">posx</span> <span class="o">-</span> <span class="mi">40</span><span class="p">;</span>
                <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
                    <span class="nx">positionRight</span> <span class="o">=</span> <span class="nx">posx</span> <span class="o">-</span> <span class="mi">10</span><span class="p">;</span>
                <span class="p">}</span>
            <span class="p">}</span>

            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;#row-selector-wrapper&#39;</span><span class="p">).</span><span class="nx">css</span><span class="p">(</span><span class="s1">&#39;left&#39;</span><span class="p">,</span> <span class="nx">positionRight</span> <span class="o">+</span> <span class="mi">30</span><span class="p">);</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-6'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-6'>#</a>
      </div>
      <p>set Y position</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>            <span class="kd">var</span> <span class="nx">posy</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">round</span><span class="p">((</span><span class="mi">1</span> <span class="o">-</span> <span class="nx">screenVector</span><span class="p">.</span><span class="nx">y</span><span class="p">)</span> <span class="o">*</span> <span class="nx">renderer</span><span class="p">.</span><span class="nx">domElement</span><span class="p">.</span><span class="nx">offsetHeight</span> <span class="o">/</span> <span class="mi">2</span><span class="p">)</span> <span class="o">-</span> <span class="mi">5</span><span class="p">;</span>
            <span class="k">if</span> <span class="p">(</span><span class="nx">isMobile</span><span class="p">)</span> <span class="p">{</span>
                <span class="nx">posy</span> <span class="o">=</span> <span class="nx">posy</span> <span class="o">-</span> <span class="mi">10</span><span class="p">;</span>
            <span class="p">}</span>
            <span class="kd">var</span> <span class="nx">elementStyle</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nx">getElementById</span><span class="p">(</span><span class="nx">div</span><span class="p">).</span><span class="nx">style</span><span class="p">;</span>
            <span class="nx">elementStyle</span><span class="p">.</span><span class="nx">left</span> <span class="o">=</span> <span class="nx">positionRight</span> <span class="o">+</span> <span class="s2">&quot;px&quot;</span><span class="p">;</span>
            <span class="nx">elementStyle</span><span class="p">.</span><span class="nx">top</span> <span class="o">=</span> <span class="nx">posy</span> <span class="o">+</span> <span class="s2">&quot;px&quot;</span><span class="p">;</span>
        <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;#&#39;</span> <span class="o">+</span> <span class="nx">div</span><span class="p">).</span><span class="nx">hide</span><span class="p">();</span>
        <span class="p">}</span>
    <span class="p">},</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-7'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-7'>#</a>
      </div>
      <p>add row handlers to the scene, regular 3dObjects to get coordinates from to the screen</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>    <span class="nx">createRowHandlers</span><span class="o">:</span> <span class="kd">function</span><span class="p">(</span><span class="nx">array</span><span class="p">,</span> <span class="nx">ivy</span><span class="p">,</span> <span class="nx">scene</span><span class="p">,</span> <span class="nx">isMobile</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">width</span> <span class="o">=</span> <span class="nx">ivy</span><span class="p">.</span><span class="nx">width</span> <span class="o">/</span> <span class="mi">2</span><span class="p">;</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-8'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-8'>#</a>
      </div>
      <p>width has to be negative, because on mobile handlers are on the other side</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>        <span class="k">if</span> <span class="p">(</span><span class="nx">isMobile</span><span class="p">)</span> <span class="nx">width</span> <span class="o">=</span> <span class="nx">width</span> <span class="o">*</span> <span class="o">-</span><span class="mi">1</span><span class="p">;</span>
        <span class="k">for</span> <span class="p">(</span><span class="kd">var</span> <span class="nx">i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nx">i</span> <span class="o">&lt;</span> <span class="mi">8</span><span class="p">;</span> <span class="nx">i</span><span class="o">++</span><span class="p">)</span> <span class="p">{</span>
            <span class="nx">height</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
            <span class="k">for</span> <span class="p">(</span><span class="kd">var</span> <span class="nx">j</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nx">j</span> <span class="o">&lt;</span> <span class="nx">i</span><span class="p">;</span> <span class="nx">j</span><span class="o">++</span><span class="p">)</span> <span class="p">{</span>
                <span class="nx">height</span> <span class="o">+=</span> <span class="nx">ivy</span><span class="p">.</span><span class="nx">constants</span><span class="p">.</span><span class="nx">rowHeight</span><span class="p">[</span><span class="nx">j</span><span class="p">];</span>
            <span class="p">}</span>
            <span class="nx">height</span> <span class="o">+=</span> <span class="nx">ivy</span><span class="p">.</span><span class="nx">constants</span><span class="p">.</span><span class="nx">rowHeight</span><span class="p">[</span><span class="nx">i</span><span class="p">]</span> <span class="o">/</span> <span class="mi">2</span><span class="p">;</span>
            <span class="nx">array</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="k">new</span> <span class="nx">THREE</span><span class="p">.</span><span class="nx">Object3D</span><span class="p">());</span>
            <span class="nx">array</span><span class="p">[</span><span class="nx">i</span><span class="p">].</span><span class="nx">position</span><span class="p">.</span><span class="nx">setX</span><span class="p">(</span><span class="nx">width</span><span class="p">);</span>
            <span class="nx">array</span><span class="p">[</span><span class="nx">i</span><span class="p">].</span><span class="nx">position</span><span class="p">.</span><span class="nx">setY</span><span class="p">(</span><span class="nx">height</span><span class="p">);</span>
            <span class="nx">array</span><span class="p">[</span><span class="nx">i</span><span class="p">].</span><span class="nx">position</span><span class="p">.</span><span class="nx">setZ</span><span class="p">(</span><span class="mi">250</span><span class="p">);</span>
            <span class="nx">array</span><span class="p">[</span><span class="nx">i</span><span class="p">].</span><span class="nx">matrixWorldNeedsUpdate</span> <span class="o">=</span> <span class="kc">true</span><span class="p">;</span>
            <span class="nx">scene</span><span class="p">.</span><span class="nx">add</span><span class="p">(</span><span class="nx">array</span><span class="p">[</span><span class="nx">i</span><span class="p">]);</span>
        <span class="p">}</span>
    <span class="p">},</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-9'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-9'>#</a>
      </div>
      <p>update handlers position</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>    <span class="nx">updateHandlers</span><span class="o">:</span> <span class="kd">function</span><span class="p">(</span><span class="nx">array</span><span class="p">,</span> <span class="nx">ivy</span><span class="p">,</span> <span class="nx">isMobile</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">width</span> <span class="o">=</span> <span class="nx">ivy</span><span class="p">.</span><span class="nx">width</span> <span class="o">/</span> <span class="mi">2</span><span class="p">;</span>
        <span class="k">if</span> <span class="p">(</span><span class="nx">isMobile</span><span class="p">)</span> <span class="nx">width</span> <span class="o">=</span> <span class="nx">width</span> <span class="o">*</span> <span class="o">-</span><span class="mi">1</span><span class="p">;</span>
        <span class="k">for</span> <span class="p">(</span><span class="kd">var</span> <span class="nx">i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nx">i</span> <span class="o">&lt;</span> <span class="mi">8</span><span class="p">;</span> <span class="nx">i</span><span class="o">++</span><span class="p">)</span> <span class="p">{</span>
            <span class="nx">height</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
            <span class="k">for</span> <span class="p">(</span><span class="kd">var</span> <span class="nx">j</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nx">j</span> <span class="o">&lt;</span> <span class="nx">i</span><span class="p">;</span> <span class="nx">j</span><span class="o">++</span><span class="p">)</span> <span class="p">{</span>
                <span class="nx">height</span> <span class="o">+=</span> <span class="nx">ivy</span><span class="p">.</span><span class="nx">constants</span><span class="p">.</span><span class="nx">rowHeight</span><span class="p">[</span><span class="nx">j</span><span class="p">];</span>
            <span class="p">}</span>
            <span class="nx">height</span> <span class="o">+=</span> <span class="nx">ivy</span><span class="p">.</span><span class="nx">constants</span><span class="p">.</span><span class="nx">rowHeight</span><span class="p">[</span><span class="nx">i</span><span class="p">]</span> <span class="o">/</span> <span class="mi">2</span><span class="p">;</span>
            <span class="nx">array</span><span class="p">[</span><span class="nx">i</span><span class="p">].</span><span class="nx">position</span><span class="p">.</span><span class="nx">setX</span><span class="p">(</span><span class="nx">width</span><span class="p">);</span>
            <span class="nx">array</span><span class="p">[</span><span class="nx">i</span><span class="p">].</span><span class="nx">position</span><span class="p">.</span><span class="nx">setY</span><span class="p">(</span><span class="nx">height</span><span class="p">);</span>
        <span class="p">}</span>
    <span class="p">},</span></pre></div>
    </div>
  </div>
  <div class='clearall'></div>
  <div class='section' id='section-10'>
    <div class='docs'>
      <div class='octowrap'>
        <a class='octothorpe' href='#section-10'>#</a>
      </div>
      <p>hide or show handlers</p>
    </div>
    <div class='code'>
      <div class="highlight"><pre>    <span class="nx">hideShowHandlers</span><span class="o">:</span> <span class="kd">function</span><span class="p">(</span><span class="nx">show</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">if</span> <span class="p">(</span><span class="nx">$</span><span class="p">(</span><span class="s1">&#39;.configurator-hold&#39;</span><span class="p">).</span><span class="nx">is</span><span class="p">(</span><span class="s2">&quot;:visible&quot;</span><span class="p">)</span> <span class="o">&amp;&amp;</span> <span class="o">!</span><span class="nx">show</span><span class="p">)</span> <span class="p">{</span>
            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;.configurator-hold&#39;</span><span class="p">).</span><span class="nx">fadeOut</span><span class="p">(</span><span class="s1">&#39;fast&#39;</span><span class="p">);</span>
        <span class="p">}</span> <span class="k">else</span> <span class="k">if</span> <span class="p">(</span><span class="nx">show</span><span class="p">)</span> <span class="p">{</span>
            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;.configurator-hold&#39;</span><span class="p">).</span><span class="nx">fadeIn</span><span class="p">(</span><span class="s1">&#39;fast&#39;</span><span class="p">);</span>
            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;#row-selector-wrapper&#39;</span><span class="p">).</span><span class="nx">css</span><span class="p">(</span><span class="s1">&#39;transition&#39;</span><span class="p">,</span> <span class="s1">&#39;none&#39;</span><span class="p">);</span>
            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;#row-selector-wrapper&#39;</span><span class="p">).</span><span class="nx">css</span><span class="p">(</span><span class="s1">&#39;top&#39;</span><span class="p">,</span> <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;.configurator-hold.active&#39;</span><span class="p">).</span><span class="nx">css</span><span class="p">(</span><span class="s1">&#39;top&#39;</span><span class="p">));</span>
            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;#row-selector-wrapper&#39;</span><span class="p">).</span><span class="nx">css</span><span class="p">(</span><span class="s1">&#39;top&#39;</span><span class="p">);</span>
            <span class="nx">$</span><span class="p">(</span><span class="s1">&#39;#row-selector-wrapper&#39;</span><span class="p">).</span><span class="nx">css</span><span class="p">(</span><span class="s1">&#39;transition&#39;</span><span class="p">,</span> <span class="s1">&#39;top 0.5s&#39;</span><span class="p">);</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

</pre></div>
    </div>
  </div>
  <div class='clearall'></div>
</div>
</body>
