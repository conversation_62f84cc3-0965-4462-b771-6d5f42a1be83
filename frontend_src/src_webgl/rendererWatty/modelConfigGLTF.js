import { materialName as materialNameHelper } from './materials';

export default materialId => {
    const {
        interiorHex, exteriorHex, lightMapIntensity, lightMapIntensityInterior, reflectivity, hingeColor,
    } = materialNameHelper(materialId);
    return {
        backpanel: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_backpanel.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_vertical_horizontal_backpanel.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        bar: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_bar.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_bar.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        box_door_raiser_left: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box_door_L.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_external_drawer.jpg',
            color: exteriorHex,
            lightMapIntensity,
            reflectivity,
        },
        box_door_raiser_right: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box_door_R.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_external_drawer.jpg',
            color: exteriorHex,
            lightMapIntensity,
            reflectivity,
        },
        box_door_left: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box_door_L.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_external_drawer.jpg',
            color: exteriorHex,
            lightMapIntensity,
            reflectivity,
        },
        box_door_right: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box_door_R.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_external_drawer.jpg',
            color: exteriorHex,
            lightMapIntensity,
            reflectivity,
        },
        box_drawer_back: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_drawer_back.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity,
            reflectivity,
        },
        box_drawer_bottom: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_drawer_bottom.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        box_drawer_front_interior: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_drawer_front.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        box_drawer_front_exterior: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_external_drawer.jpg',
            color: exteriorHex,
            colorInterior: false,
            lightMapIntensity,
            reflectivity,
        },
        box_drawer_left_side: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_drawer_left_side.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        box_drawer_right_side: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_drawer_right_side.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        box_left_right_frame: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_left_right_frame.jpg',
            color: exteriorHex,
            lightMapIntensity,
            reflectivity,
        },
        // Whoot? MASK!!
        box_plinth: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_top_frame_plinth.jpg',
            color: exteriorHex,
            colorInterior: false,
            lightMapIntensity,
            reflectivity,
        },
        box_top_frame: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_box.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_box_top_frame_plinth.jpg',
            color: exteriorHex,
            lightMapIntensity,
            reflectivity,
        },
        handle_shadow: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_handle_shadow.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_handle_shadow.png',
            color: exteriorHex,
            transparent: true,
            lightMapIntensity,
            reflectivity,
        },
        handle: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_handle.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_handle.jpg',
            color: exteriorHex,
            lightMapIntensity,
            reflectivity,
        },
        interior_dark: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_interior.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_interior_dark.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        interior: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_interior.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_interior.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        wall: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_vertical_stripe.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_vertical_horizontal_backpanel.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        slab: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_horizontal_stripe.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_vertical_horizontal_backpanel.jpg',
            color: interiorHex,
            colorInterior: true,
            lightMapIntensity: lightMapIntensityInterior,
            reflectivity,
        },
        shadow_wall_left: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_shadow_wall_L.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_shadow.jpg',
        },
        shadow_wall_right: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_shadow_wall_R.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_shadow.jpg',
        },
        shadow_wall_middle: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_shadow_wall_M.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_shadow.jpg',
        },
        shadow_floor_left: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_shadow_floor_L.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_shadow.jpg',
        },
        shadow_floor_right: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_shadow_floor_R.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_shadow.jpg',
        },
        shadow_floor_middle: {
            model: '/r_static/src_webgl/watty/modelsGFTL/T03_shadow_floor_M.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_shadow.jpg',
        },
        typek: {
            model: '/r_static/src_webgl/watty/modelsGFTL/Ludek.gltf',
            texture: '/r_static/src_webgl/watty/textures/T03_shadow_typek.jpg',
            reflectivity: 0,
        },
        hinge_door_part_left: {
            model: '/r_static/src_webgl/watty/modelsGFTL/hinge_door_part_L.gltf',
            hingeColor,
        },
        hinge_door_part_right: {
            model: '/r_static/src_webgl/watty/modelsGFTL/hinge_door_part_R.gltf',
            hingeColor,
        },
        hinge_frame_part_left: {
            model: '/r_static/src_webgl/watty/modelsGFTL/hinge_frame_part_L.gltf',
            hingeColor,
        },
        hinge_frame_part_right: {
            model: '/r_static/src_webgl/watty/modelsGFTL/hinge_frame_part_R.gltf',
            hingeColor,
        },
    };
};
