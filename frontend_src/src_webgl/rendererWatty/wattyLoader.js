/* eslint-disable no-param-reassign */
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { shadowItems, hinges } from '../build3dWatty/buildFunctions/config';
import { datGui } from './gui';

import modelConfigGLTF from './modelConfigGLTF';

class WattyLoader {
  constructor(materialId = 0) {
    this.materialId = materialId;
    this.manager = new THREE.LoadingManager();
    this.cubeTextureLoader = new THREE.CubeTextureLoader();
    this.manager.onStart = (url, itemsLoaded, itemsTotal) => console.log(`Started loading file: ${url}. \nLoaded ${itemsLoaded} of ${itemsTotal} files.`);
    this.manager.onLoad = () => setTimeout(() => {
      document.getElementById('conf').dispatchEvent(new CustomEvent('loadCanvas'));
      console.log('%cLoading complete! 🌟', 'color: lightgreen;');
    }, 200);
    this.GLTFLoader = new GLTFLoader(this.manager);
    this.textureLoader = new THREE.TextureLoader(this.manager);
    this.elements = {};
    this.cubeMap = null;
  }


  async loadModelsAndTextures() {
    this.modelsConfigByMaterial = modelConfigGLTF(this.materialId);

    this.cubeMap = await this.cubeTextureLoader.loadAsync([
      '/r_static/src_webgl/ivy/models/textures/cubemap/px.jpg',
      '/r_static/src_webgl/ivy/models/textures/cubemap/nx.jpg',
      '/r_static/src_webgl/ivy/models/textures/cubemap/py.jpg',
      '/r_static/src_webgl/ivy/models/textures/cubemap/ny.jpg',
      '/r_static/src_webgl/ivy/models/textures/cubemap/pz.jpg',
      '/r_static/src_webgl/ivy/models/textures/cubemap/nz.jpg',
    ]);
    this.cubeMapHinges = await this.cubeTextureLoader.loadAsync([
      '/r_static/src_webgl/watty/cubemap-hinges/px.jpg',
      '/r_static/src_webgl/watty/cubemap-hinges/nx.jpg',
      '/r_static/src_webgl/watty/cubemap-hinges/py.jpg',
      '/r_static/src_webgl/watty/cubemap-hinges/ny.jpg',
      '/r_static/src_webgl/watty/cubemap-hinges/pz.jpg',
      '/r_static/src_webgl/watty/cubemap-hinges/nz.jpg',
    ]);

    const models = Object.keys(this.modelsConfigByMaterial);
    await Promise.all(
      models.map(async key => {
        const scene = await this.GLTFLoader.loadAsync(this.modelsConfigByMaterial[key].model);

        let texture;
        if (this.modelsConfigByMaterial[key].texture) {
          texture = await this.textureLoader.loadAsync(this.modelsConfigByMaterial[key].texture).catch(() => {
            throw new Error('Could not load texture: wattyLoader');
          });
        } else {
          texture = new THREE.Texture();
        }

        texture.generateMipmaps = false;
        texture.minFilter = THREE.NearestFilter;
        // const env = await this.textureLoader.load('/r_static/src_webgl/watty/textures/env.jpg');
        texture.flipY = false;
        const material = new THREE.MeshBasicMaterial();
        scene.scene.traverse(child => {
          if (child instanceof THREE.Mesh) {
            child.material = material;
            if (shadowItems.some(i => i === key)) {
              child.material.map = texture;
            } else if (hinges.some(i => i === key)) {
              this.loadHinge(child, key, material, this.cubeMapHinges);
            } else {
              child.material.envMap = this.cubeMap;
              child.material.reflectivity = this.modelsConfigByMaterial[key].reflectivity;
              child.material.lightMapIntensity = this.modelsConfigByMaterial[key].lightMapIntensity;
              child.material.lightMap = texture;
              child.material.color = new THREE.Color(this.modelsConfigByMaterial[key].color);
              if (this.modelsConfigByMaterial[key].transparent) {
                child.material.transparent = true;
              }
            }
          }
        });
        scene.name = key;
        scene.typeName = key;
        this.elements[key] = scene.scene;
      }),
    );
  }

  loadHinge(hinge, modelKey, material, cubeMap) {
    hinge.material.envMap = cubeMap;
    hinge.material.reflectivity = 0.8;
    hinge.material.color = new THREE.Color(this.modelsConfigByMaterial[modelKey].hingeColor);
  }

  getElements() {
    return this.elements;
  }
}

function wardrobeLoaderInstance(material) {
  const loader = new WattyLoader(material);
  return loader
    .loadModelsAndTextures()
    .then(() => loader)
    .catch(err => {
      throw err;
    });
}

export { wardrobeLoaderInstance as wardrobeLoader };
