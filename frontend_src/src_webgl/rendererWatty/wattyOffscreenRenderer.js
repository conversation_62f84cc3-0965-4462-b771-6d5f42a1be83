/* eslint-disable no-param-reassign */

import * as THREE from 'three';
import { Color } from 'three';
import Build3d from '../build3dWatty/build3dWatty';
import { TylkoCamera } from '../tylkoCamera/tylko-camera';
import { customSettings } from '../tylkoCamera/tylko-camera-settings';

class WattyOffscreenRenderer {
    constructor({
        domElement = false,
        canvasWidth,
        canvasHeight,
        loader,
        geom,
        shelfType = 0,
        shelfColor = 0,
        cameraType = 'simpleCamera',
        cameraSettings = 'wardrobe',
        renderScene,
    }) {
        this.rendererName = 'wattyOffscreenRenderer';
        this.domElement = domElement;
        this.cameraSettings = customSettings[cameraSettings];
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        this.depth = null;
        this.loader = loader;
        this.elements = this.loader.getElements();
        this.geom = geom;
        this.shelfType = shelfType;
        this.shelfColor = shelfColor;
        this.sceneOffscreen = new THREE.Scene();
        this.build3dOffscreen = new Build3d({
            elements: this.elements,
            scene: this.sceneOffscreen,
            isOffscreen: true,
            shadowRendered: renderScene ? 0 : -1,
        });
        this.cameraType = cameraType;
        this.currentView = 'shelf';
        this.shouldRender = true;
        this.isAnimating = false;

        if (this.domElement) {
            this.container = domElement;
        }

        this.createTylkoCamera();
        this.initBuild3d();
        this.createSimpleRenderer();
    }

    initBuild3d() {
        this.build3dOffscreen.init3d();
    }

    createSimpleCamera() {
        this.simpleCamera = new THREE.PerspectiveCamera(20, this.canvasWidth / this.canvasHeight, 1, 10000);
        this.simpleCamera.position.setZ(9000);
        this.simpleCamera.name = 'simpleCamera';
        this.sceneOffscreen.add(this.simpleCamera);
    }

    createTylkoCamera() {
        this.createSimpleCamera();
        this.tylkoCamera = new TylkoCamera({
            view: false,
            settings: this.cameraSettings,
        });
        // Ustawienia - Animacja + Offset
        this.tylkoCamera.noTransitionAnimation = false;
        this.tylkoCamera.shelfScreenEdgeOffset = {
            left: 80, right: 80, top: 0, bottom: 0,
        };
        this.tylkoCamera.componentScreenEdgeOffset = {
            left: 0, right: 0, top: 0, bottom: 0,
        };

        this.tylkoCamera.updateAspect(this.canvasWidth / this.canvasHeight);

        this.tylkoCamera.controls.addEventListener('render', () => {
            this.shouldRender = true;
        });

        this.tylkoCamera.controls.addEventListener('change', () => {
            this.renderer.render(this.sceneOffscreen, this.tylkoCamera.camera);
        });

        this.tylkoCamera.controls.addEventListener('animationEnd', () => {
        });
    }

    getScene() {
        return this.sceneOffscreen;
    }

    getContainer() {
        return this.container;
    }

    getCamera() {
        return this.tylkoCamera;
    }

    createSimpleRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            logarithmicDepthBuffer: false,
            alpha: true,
            devicePixelRatio: window.devicePixelRatio * 2,
        });
        this.renderer.setClearColor(0xedf0f0, 1);
        this.renderer.setSize(this.canvasWidth, this.canvasHeight, false);
    }

    toggleDoorsVisibility(val) {
        this.build3dOffscreen.toggleDoorsVisibility(val);
        if (val === false) {
            this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState();
        }
    }

    updateGeometry({
        geom,
        width,
        height,
        depth,
        cameraUpdate = true,
    }) {
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.geom = geom;


        this.build3dOffscreen.rebuildWallsFromJson(...arguments);
        if (!cameraUpdate) return;

        const slabs = [...this.geom.slabs.map(h => h.y1)];

        if (this.currentView === 'shelf') {
            this.tylkoCamera.updateGeometry(
                { x: -width / 2, y: 0, z: -depth / 2 },
                { x: width / 2, y: Math.max(...slabs), z: depth / 2 },
                true,
            );
        }

        this.renderer.render(this.sceneOffscreen, this.cameraType === 'tylkoCamera' ? this.tylkoCamera.camera : this.simpleCamera);
    }

    setComponentView(geom, activeComponent, isMobile) {
        if (!isMobile) {
            this.tylkoCamera.setViewFinal('frontTripleSnap');
            return;
        }
        this.currentView = 'component';
        const component = geom.components.find(obj => obj.m_config_id == activeComponent);
        // console.log('>> CPLUS - setComponentView', geom, activeComponent, component);
        if (component.bounding_box) {
            this.tylkoCamera.setComponentViewFinal(
                { x: component.bounding_box.x1, y: component.bounding_box.y1, z: component.bounding_box.z1 },
                { x: component.bounding_box.x2, y: component.bounding_box.y2, z: component.bounding_box.z2 },
            );
        }
        this.renderer.render(this.sceneOffscreen, this.simpleCamera);
    }

    setShelfView(geom, isMobile) {
        if (!isMobile) return;
        this.currentView = 'shelf';
        this.tylkoCamera.setShelfViewFinal(
            { x: -geom.width / 2, y: 0, z: 0 },
            { x: geom.width / 2, y: geom.height, z: 410 },
            false,
        );
        this.renderer.render(this.sceneOffscreen, this.simpleCamera);
    }

    setTabSpecificView(tab) {
        if (this.currentView === 'shelf') {
            this.tylkoCamera.setShelfMode();
        } else {
            this.tylkoCamera.setComponentMode();
        }

        switch (tab) {
        case 2: // depth
            this.tylkoCamera.setViewFinal('side');
            break;
        case 'base': // przyszly cokol
            this.tylkoCamera.setViewFinal('lowSide');
            break;
        case 3: // columns
            this.tylkoCamera.setViewFinal('front');
            break;
        case 5: // details
            this.tylkoCamera.setFxedMode();
            this.tylkoCamera.setViewFinal('front');
            return; // AND RETURN
        default: // width, height, colors
            this.tylkoCamera.setViewFinal('frontTripleSnap');
        }
    }

    getScreenshotForCart() {
        this.renderer.render(this.sceneOffscreen, this.tylkoCamera.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }

    getScreenshotForSave() {
        this.tylkoCamera.controls.snapTo = customSettings.wardrobeSave.snapping;
        this.renderer.render(this.sceneOffscreen, this.tylkoCamera.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }

    updateCanvasColor({ backgroundColor, transparency }) {
        this.renderer.setClearColor(backgroundColor, transparency);
    }

    updateCanvasSize({ canvasWidth, canvasHeight }) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        this.renderer.setSize(this.canvasWidth, this.canvasHeight, false);
    }

    updateMaterial(materialId) {
        this.build3dOffscreen.updateMaterial(materialId);
    }

    updateCameraSettings(presetName) {
        this.tylkoCamera.updateSettings(customSettings[presetName]);
        this.tylkoCamera.controls.snapTo = customSettings[presetName].snapping;
    }

    toggleDoorClosed(state) {
        this.build3dOffscreen.toggleDoorsClosed(state);
        this.build3dOffscreen.drawWalls();
    }

    getScreenshotForSetup({
        backgroundColor = 0xedf0f0,
        transparency = 0,
        cameraSettings = 'feed_left_30',
        canvasWidth = 600,
        canvasHeight = 600,
    }) {
        this.updateCameraSettings(cameraSettings);
        this.updateCanvasSize({ canvasWidth, canvasHeight });
        this.updateCanvasColor({ backgroundColor, transparency });
        this.tylkoCamera.camera.updateProjectionMatrix();
        this.renderer.render(this.sceneOffscreen, this.tylkoCamera.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }

    getScreenshotForMobileDim() {
        this.cameraSettings.snapping = [{ theta: 0, phi: 1.59 }];
        this.updateCameraSettings('wardrobe');
        this.updateCanvasSize({ canvasWidth: window.innerWidth * 4, canvasHeight: window.innerHeight * 4 });
        this.renderer.render(this.sceneOffscreen, this.tylkoCamera.camera);
        this.tylkoCamera.camera.updateProjectionMatrix();
        return this.renderer.domElement.toDataURL('image/png');
    }
}

export { WattyOffscreenRenderer };
