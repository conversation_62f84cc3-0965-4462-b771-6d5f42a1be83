/* eslint-disable no-param-reassign */

import * as THREE from 'three';
import Build3d from '../build3dWatty/build3dWatty';
import WattyCanvasInteractions from '../build3dWatty/wattyCanvasInteractions';
import { TylkoCamera } from '../tylkoCamera/tylko-camera';
import { customSettings } from '../tylkoCamera/tylko-camera-settings';
import { convert3dToPixels } from '../utils/heplers/convert3dToPixels';
import {
    doorsAnimationConfig,
} from '../build3dWatty/wattyCanvasAnimationsConfig';

class TheRenderer {
    constructor({
        container,
        canvasWidth,
        canvasHeight,
        loader,
        geom,
        shelfType = 0,
        shelfColor = 0,
        cameraType = 'simpleCamera',
        dispatcher,
        store,
        cameraSettings = 'wardrobe',
        debug = true,
    }) {
        this.rendererName = 'wattyRenderer';
        this.cameraSettings = customSettings[cameraSettings];
        this.container = container;
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        this.depth = null;
        this.loader = loader;
        this.elements = this.loader.getElements();
        this.geom = geom;
        this.shelfType = shelfType;
        this.shelfColor = shelfColor;
        this.scene = new THREE.Scene();
        this.build3d = new Build3d({
            elements: this.elements,
            scene: this.scene,
        });
        this.wattyCanvasInteraction = new WattyCanvasInteractions({
            scene: this.scene,
            animationConfig: doorsAnimationConfig,
            build3d: this.build3d,
            store,
        });
        this.cameraType = cameraType;
        this.currentView = 'shelf';
        this.dispatcher = dispatcher;
        this.store = store;
        this.shouldRender = true;
        this.initialRender = true;
        this.isOrbitActive = false;
        this.isAnimating = false;

        this.createTylkoCamera();
        this.initBuild3d();
        this.createSimpleRenderer();
        this.setupCanvasInteraction();
        this.setupListeners();
        this.setMobileDefaultSnap();

        this.renderLoop();
    }

    // LEVEL
    initBuild3d() {
        this.build3d.init3d();
    }

    // LEVEL
    updateMaterial(materialId) {
        this.build3d.updateMaterial(materialId);
    }

    // FRAME
    onWindowResize(width, height) {
        this.tylkoCamera.camera.aspect = width / height;
        this.tylkoCamera.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height, false);
    }

    // FRAME
    createSimpleCamera() {
        this.simpleCamera = new THREE.PerspectiveCamera(20, this.canvasWidth / this.canvasHeight, 1, 10000);
        this.simpleCamera.position.setZ(9000);
        this.simpleCamera.name = 'simpleCamera';
        this.scene.add(this.simpleCamera);
    }

    // FRAME
    createTylkoCamera() {
        this.createSimpleCamera();
        this.tylkoCamera = new TylkoCamera({
            view: this.container,
            settings: this.cameraSettings,
        });
        // Ustawienia - Animacja + Offset
        this.tylkoCamera.noTransitionAnimation = false;
        this.tylkoCamera.shelfScreenEdgeOffset = {
            left: 80, right: 80, top: 0, bottom: 0,
        };
        this.tylkoCamera.componentScreenEdgeOffset = {
            left: 0, right: 0, top: 0, bottom: 0,
        };

        this.tylkoCamera.updateAspect(this.canvasWidth / this.canvasHeight);

        this.tylkoCamera.controls.addEventListener('render', () => {
            this.shouldRender = true;
            this.dispatcher('interactions/SET_IS_CAMERA_ANIMATING', true);
        });

        this.tylkoCamera.controls.addEventListener('change', () => {
            this.renderer.render(this.scene, this.tylkoCamera.camera);
        });

        this.tylkoCamera.controls.addEventListener('animationEnd', () => {
            setTimeout(() => {
                this.dispatcher('ui/UPDATE_COORDINATES_FOR_BUTTONS');
                this.dispatcher('interactions/SET_IS_CAMERA_ANIMATING', false);
            }, 100);
        });
    }

    // INTERACTION
    setupListeners() {
        this.container.addEventListener('mousedown', () => (this.dispatcher('interactions/SET_IS_ORBIT_MOVING', true)), false);
        document.body.addEventListener('mouseup', () => {
            setTimeout(() => {
                this.dispatcher('interactions/SET_IS_ORBIT_MOVING', false);
            }, 100);
        }, false);
        this.container.addEventListener('touchstart', () => (this.dispatcher('interactions/SET_IS_ORBIT_MOVING', true)), false);
        document.body.addEventListener('touchend', () => {
            setTimeout(() => {
                this.dispatcher('interactions/SET_IS_ORBIT_MOVING', false);
            }, 100);
        }, false);

        this.setUpScreenshotShortcut();
    }

    // INTERACTION
    setUpScreenshotShortcut() {
        let keysPressed = [];
        document.onkeydown = event => {
            if (!keysPressed.includes(event.key)) {
                keysPressed.push(event.key);
            }
            const shortcutIsActive = keysPressed.length === 3
                && keysPressed.includes('z')
                && keysPressed.includes('x')
                && keysPressed.includes('p');
            if (shortcutIsActive) {
                this.renderer.render(this.scene, this.tylkoCamera.camera);
                this.renderer.domElement.toBlob(blob => this.createAndClickOffscreenLink(blob));
            }
        };
        document.onkeyup = () => {
            keysPressed = [];
        };
    }

    // eslint-disable-next-line class-methods-use-this // FRAME utils
    createAndClickOffscreenLink(blob) {
        const a = document.createElement('a');
        const date = new Date();
        const utc = date.toJSON().slice(0, 10).replace(/-/g, '/');
        document.body.appendChild(a);
        a.style.display = 'none';
        const url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = `Wardrobe_screenshot_(${utc}-${date.getHours()}:${date.getMinutes()})`;
        a.click();
    }

    // LEVEL
    getScene() {
        return this.scene;
    }

    // FRAME
    getCamera() {
        return this.tylkoCamera;
    }

    // FRAME
    getContainer() {
        return this.container;
    }

    // INTERACTION
    setupCanvasInteraction() {
        let selectedObject = null;
        let doorsAreClosed = !this.store.renderer.isDoorsOpen;
        let dimensionsAreVisible = this.store.dimensions.show;
        const raycaster = new THREE.Raycaster();
        const mouseVector = new THREE.Vector3();
        const getIntersects = (x, y) => {
            const vecX = (x / this.container.offsetWidth) * 2 - 1;
            const vecY = -(y / this.container.offsetHeight) * 2 + 1;
            mouseVector.set(vecX, vecY, 0);
            raycaster.setFromCamera(mouseVector, this.tylkoCamera.camera);
            return raycaster.intersectObject(this.scene, true);
        };

        this.container.addEventListener('mousemove', event => {
            event.preventDefault();

            // Update ui flags
            doorsAreClosed = !this.store.renderer.isDoorsOpen;
            dimensionsAreVisible = this.store.dimensions.show;

            if (selectedObject && selectedObject.name.split(':')[0] === 'hoverBox') {
                if (selectedObject.name.split(':')[1] !== this.store.activeComponent) {
                    selectedObject.material.opacity = 0;
                    selectedObject = null;
                }
            }
            if (selectedObject && selectedObject.name.split(':')[1] === this.store.activeComponent && selectedObject.name.split(':')[0] === 'hoverBox') {
                selectedObject.material.opacity = 0.2;
                selectedObject = null;
            }

            const intersects = getIntersects(event.layerX, event.layerY);
            if (intersects.length > 0) {
                // COMPONENT
                const res = intersects.filter(intersection => intersection && intersection.object && intersection.object.name.split(':')[0] === 'hoverBox')[0];
                if (res && res.object) {
                    selectedObject = res.object;
                    selectedObject.material.opacity = 0.2; // 0.5
                    this.dispatcher('UPDATE_HOVER_COMPONENT', res.object.name.split(':')[1]);
                    if (!dimensionsAreVisible) this.wattyCanvasInteraction.handleComponentHover({ configId: res.object.name.split(':')[1], doorsAreOpened: this.store.renderer.isDoorsOpen });
                } else {
                    this.dispatcher('UPDATE_HOVER_COMPONENT', null);
                    if (!dimensionsAreVisible) {
                        this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState({
                            doorsAreOpened: this.store.renderer.isDoorsOpen,
                            raiserUpdate: false,
                            dimensionsAreVisible: this.store.dimensions.show,
                        });
                    }
                }
                // RAISER
                const res2 = intersects.filter(intersection => intersection && intersection.object && intersection.object.name.split(':')[0] === 'hoverBoxR')[0];
                if (res2 && res2.object) {
                    selectedObject = res2.object;
                    if (!dimensionsAreVisible) this.wattyCanvasInteraction.handleRaiserHover({ configId: res2.object.name.split(':')[1], doorsAreClosed });
                } else {
                    this.wattyCanvasInteraction.closeAllRaisers({ doorsAreClosed });
                }
            } else {
                // eslint-disable-next-line no-lonely-if
                if (!dimensionsAreVisible) {
                    this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState({
                        doorsAreOpened: this.store.renderer.isDoorsOpen,
                        dimensionsAreVisible: this.store.dimensions.show,
                    });
                }
            }
        }, false);

        this.container.addEventListener('click', event => {
            event.preventDefault();
            doorsAreClosed = !this.store.renderer.isDoorsOpen;
            dimensionsAreVisible = this.store.dimensions.show;

            // eslint-disable-next-line no-return-assign
            this.build3d.sceneItems.components.forEach(box => box.material.opacity = 0);
            if (selectedObject && selectedObject.name.split(':')[0] === 'hoverBox') {
                selectedObject.material.opacity = 0;
                selectedObject = null;
            }
            const intersects = getIntersects(event.layerX, event.layerY);
            if (intersects.length > 0) {
                // eslint-disable-next-line no-shadow
                const res = intersects.filter(res => res && res.object && res.object.name.split(':')[0] === 'hoverBox')[0];
                const res2 = intersects.filter(intersection => intersection && intersection.object && intersection.object.name.split(':')[0] === 'hoverBoxR')[0];
                if ((res && res.object) || (res2 && res2.object)) {
                    const object = res ? res.object : res2.object;
                    selectedObject = object;
                    if (res) selectedObject.material.opacity = 0.2;
                    this.dispatcher(
                        'ACTIVE_COMPONENT',
                        object.name.split(':')[1],
                    );
                    const eventCustom = new Event('mousemove');
                    this.container.dispatchEvent(eventCustom);
                    this.wattyCanvasInteraction.setSelectedShelfId({ id: object.name.split(':')[1] });
                    if (dimensionsAreVisible && doorsAreClosed) {
                        // Open doors
                        this.dispatcher('renderer/UPDATE_DOORS_OPEN', true);
                        this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState({ doorsAreOpened: this.store.renderer.isDoorsOpen, dimensionsAreVisible: this.store.dimensions.show });
                    }
                    if (!dimensionsAreVisible) this.wattyCanvasInteraction.handleComponentHover({ configId: object.name.split(':')[1], doorsAreOpened: this.store.renderer.isDoorsOpen });
                } else {
                    this.dispatcher('DEACTIVATE_COMPONENT');
                    if (this.store.ui.isMobile) {
                        this.dispatcher('renderer/RESET_TAB_SPECIFIC_VIEW');
                    } else {
                        this.dispatcher('renderer/RENDERER_SET_SHELF_VIEW');
                    }
                    this.wattyCanvasInteraction.setSelectedShelfId({ id: null });
                    if (!dimensionsAreVisible) this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState({ doorsAreOpened: this.store.renderer.isDoorsOpen, dimensionsAreVisible: this.store.dimensions.show });
                }
                this.wattyCanvasInteraction.setInteractionState({
                    default: {
                        doors: doorsAreClosed ? 'closed' : 'halfOpen',
                        drawers: 'closed',
                    },
                    hover: {
                        doors: 'fullOpen',
                        drawers: doorsAreClosed ? 'closed' : 'halfOpen',
                    },
                });
            } else {
                this.dispatcher('DEACTIVATE_COMPONENT');
                if (this.store.ui.isMobile) {
                    this.dispatcher('renderer/RESET_TAB_SPECIFIC_VIEW');
                } else {
                    this.dispatcher('renderer/RENDERER_SET_SHELF_VIEW');
                }
                this.wattyCanvasInteraction.setSelectedShelfId({ id: null });
                if (!dimensionsAreVisible) this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState({ doorsAreOpened: this.store.renderer.isDoorsOpen, dimensionsAreVisible: this.store.dimensions.show });
            }
        }, false);
    }

    // FRAME
    createSimpleRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            logarithmicDepthBuffer: false,
            alpha: true,
            canvas: this.container,
            devicePixelRatio: window.devicePixelRatio * 2,
        });
        this.renderer.setClearColor(0xE4E6E6, 1);
    }

    // ANIMATION
    updateComponentHoverBox(id) {
        this.build3d.sceneItems.components.forEach(box => {
            if (box.name.split(':')[1] === id) {
                box.material.opacity = 0.5;
                if (!this.store.dimensions.show) this.wattyCanvasInteraction.handleComponentHover({ configId: id, doorsAreOpened: this.store.renderer.isDoorsOpen });
            } else {
                box.material.opacity = 0;
            }
        });
    }

    // ANIMATION
    clearHoverBoxes() {
        this.build3d.sceneItems.components.forEach(box => {
            box.material.opacity = 0;
        });
    }

    // ANIMATION
    updateHoverBoxWithActiveComponent(activeComponentId, hoverComponentId) {
        this.build3d.sceneItems.components.forEach(box => {
            if (box.name.split(':')[1] === hoverComponentId) {
                box.material.opacity = 0.5;
                if (!this.store.dimensions.show) this.wattyCanvasInteraction.handleComponentHover({ configId: hoverComponentId, doorsAreOpened: this.store.renderer.isDoorsOpen });
            } else if (box.name.split(':')[1] === activeComponentId) {
                box.material.opacity = 0.2;
            } else {
                box.material.opacity = 0;
            }
        });
    }

    // ANIMATION
    toggleDoorsVisibility(val) {
        this.build3d.toggleDoorsVisibility(val);
    }

    // FRAME
    renderLoop() {
        if (this.shouldRender) {
            this.shouldRender = false;
            this.tylkoCamera.controls.update();
        }
        this.renderer.render(this.scene, this.tylkoCamera.camera);

        window.requestAnimationFrame(this.renderLoop.bind(this));
    }

    // utils projection
    getCoordinatesForButtons() {
        const mobileCoords = [];
        const desktopCoords = [];
        const buttonsArray = this.build3d.sceneItems.buttons;
        const hoversArray = this.build3d.sceneItems.components;
        const tempV = new THREE.Vector3();
        const calculateProd = item => {
            item.updateMatrixWorld(true, false);
            item.getWorldPosition(tempV);
            tempV.project(this.tylkoCamera.camera);
            const x = (tempV.x * 0.5 + 0.5) * this.container.clientWidth;
            const y = (tempV.y * -0.5 + 0.5) * this.container.clientHeight;
            return { x, y };
        };
        buttonsArray.forEach(item => {
            desktopCoords.push(calculateProd(item));
        });

        hoversArray.forEach(item => {
            mobileCoords.push(calculateProd(item));
        });
        return {
            mobile: mobileCoords,
            desktop: desktopCoords,
        };
    }

    // LEVEL
    updateGeometry({
        geom,
        width,
        height,
        depth,
        cameraUpdate = true,
    }) {
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.geom = geom;


        this.build3d.rebuildWallsFromJson(...arguments);
        this.wattyCanvasInteraction.setInitDoorsPosition({
            doors_exterior: this.build3d.sceneItems.doors_exterior.map(el => ({
                valX: el.position.x,
                valZ: el.position.z,
                name: el.name,
            })),
            doors_raiser: this.build3d.sceneItems.doors_raiser.map(el => ({
                valX: el.position.x,
                valZ: el.position.z,
                name: el.name,
            })),
        });
        this.build3d.sceneItems.doors_exterior.map(el => (
            el.position.z += this.build3d.isDoorClosed ? 0 : -doorsAnimationConfig.zOffset));
        this.build3d.sceneItems.doors_raiser.map(el => (
            el.position.z += this.build3d.isDoorClosed ? 0 : -doorsAnimationConfig.zOffset));

        if (!cameraUpdate) return;

        const slabs = [...this.geom.slabs.map(h => h.y1)];

        if (this.currentView === 'shelf') {
            this.tylkoCamera.updateGeometry(
                { x: -width / 2, y: 0, z: -depth / 2 },
                { x: width / 2, y: Math.max(...slabs), z: depth / 2 },
                false,
            );
        }
        this.renderer.render(this.scene, this.cameraType === 'tylkoCamera' ? this.tylkoCamera.camera : this.simpleCamera);
    }

    // INTERACTION
    setComponentView() {
        this.tylkoCamera.setViewFinal('frontTripleSnap');
    }

    setComponentViewMobile(geom, activeComponent) {
        this.currentView = 'component';
        const component = geom.components[activeComponent];
        if (component.bounding_box) {
            this.tylkoCamera.setComponentViewFinal(
                { x: component.bounding_box.x1, y: component.bounding_box.y1, z: component.bounding_box.z1 },
                { x: component.bounding_box.x2, y: component.bounding_box.y2, z: component.bounding_box.z2 },
            );
        }
        this.renderer.render(this.scene, this.simpleCamera);
    }

    // INTERACTION
    setShelfView(geom, isMobile, tripleSnap = false) {
        if (!isMobile) return;
        this.currentView = 'shelf';
        this.tylkoCamera.setShelfViewFinal(
            { x: -geom.width / 2, y: 0, z: 0 },
            { x: geom.width / 2, y: geom.height, z: 410 },
            false,
            tripleSnap,
        );
        this.renderer.render(this.scene, this.simpleCamera);
    }

    // INTERACTION
    setMobileDefaultSnap() {
        if (this.store.ui.isMobile) this.tylkoCamera.setViewFinal('side');
    }

    // INTERACTION
    setTabSpecificView(tab) {
        if (this.currentView === 'shelf') {
            this.tylkoCamera.setShelfMode();
        } else {
            this.tylkoCamera.setComponentMode();
        }

        switch (tab) {
        case 'width':
            this.tylkoCamera.setViewFinal('side');
            break;
        case 'height':
            this.tylkoCamera.setViewFinal('side');
            break;
        case 'depth':
            this.tylkoCamera.setViewFinal('deepSide');
            break;
        case 'material':
            this.tylkoCamera.setViewFinal('side');
            break;
        case 'interior':
            this.tylkoCamera.setViewFinal('front');
            break;
        case 'layout':
            this.tylkoCamera.setViewFinal('front');
            break;
        case 'column_width':
            this.tylkoCamera.setViewFinal('front');
            break;
        case 'door_direction':
            this.tylkoCamera.setViewFinal('front');
            break;
        default:
            this.tylkoCamera.setViewFinal('side');
        }
    }

    getScreenshotForCart() {
        this.renderer.render(this.scene, this.tylkoCamera.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }

    handleDoorClose() {
        this.wattyCanvasInteraction.setDoorsAndDrawersToClosed({ dimensionsAreVisible: this.store.dimensions.show });
        this.build3d.toggleDoorsClosed(true);
    }

    handleDoorOpen() {
        if (!this.store.dimensions.show) this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState({ doorsAreOpened: this.store.renderer.isDoorsOpen, dimensionsAreVisible: this.store.dimensions.show });
        this.build3d.toggleDoorsClosed(false);
    }

    setInteractionStates(config) {
        this.wattyCanvasInteraction.setInteractionState(config);
    }

    setActiveInteractionState(id) {
        this.wattyCanvasInteraction.setSelectedShelfId({ id });
        this.wattyCanvasInteraction.setInteractionState({
            default: {
                doors: this.store.renderer.isDoorsOpen ? 'halfOpen' : 'closed',
                drawers: 'closed',
            },
            hover: {
                doors: 'fullOpen',
                drawers: this.store.renderer.isDoorsOpen ? 'halfOpen' : 'closed',
            },
        });
        if (!this.store.dimensions.show) this.wattyCanvasInteraction.handleComponentHover({ configId: id, doorsAreOpened: this.store.renderer.isDoorsOpen });
    }

    setInactiveState() {
        this.wattyCanvasInteraction.setInteractionState({
            default: {
                doors: this.store.renderer.isDoorsOpen ? 'halfOpen' : 'closed',
                drawers: 'closed',
            },
            hover: {
                doors: 'fullOpen',
                drawers: this.store.renderer.isDoorsOpen ? 'halfOpen' : 'closed',
            },
        });
        this.wattyCanvasInteraction.setSelectedShelfId({ id: null });
        if (!this.store.dimensions.show) this.wattyCanvasInteraction.setDoorsAndDrawerDefaultState({ doorsAreOpened: this.store.renderer.isDoorsOpen, dimensionsAreVisible: this.store.dimensions.show });
    }
    getDepthSnackbarPosition() {
        return convert3dToPixels(0, this.height, this.depth, this.container, this.tylkoCamera.camera)
    }
}

export { TheRenderer };
