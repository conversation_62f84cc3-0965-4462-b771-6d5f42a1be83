import { MathUtils } from 'three';

const xFull = 4;
const xHalf = 2;
const xClosed = 0;

const zFull = -18;
const zHalf = -18;
const zClosed = 0;

const doorsAnimationConfig = {
    doors_exterior: {
        left: {
            rotation: {
                fullOpen: MathUtils.degToRad(-94),
                halfOpen: MathUtils.degToRad(-80),
                closed: MathUtils.degToRad(0),
            },
            x: {
                fullOpen: xFull,
                halfOpen: xHalf,
                closed: xClosed,
            },
            z: {
                fullOpen: zFull,
                halfOpen: zHalf,
                closed: zClosed,
            },
        },
        right: {
            rotation: {
                fullOpen: MathUtils.degToRad(94),
                halfOpen: MathUtils.degToRad(80),
                closed: MathUtils.degToRad(0),
            },
            x: {
                fullOpen: -xFull,
                halfOpen: -xHalf,
                closed: -xClosed,
            },
            z: {
                fullOpen: zFull,
                halfOpen: zHalf,
                closed: zClosed,
            },
        },
    },
    doors_raiser: {
        left: {
            rotation: {
                fullOpen: MathUtils.degToRad(-94),
                halfOpen: MathUtils.degToRad(-78),
                closed: MathUtils.degToRad(0),
            },
            x: {
                fullOpen: xFull,
                halfOpen: xHalf,
                closed: xClosed,
            },
            z: {
                fullOpen: zFull,
                halfOpen: zHalf,
                closed: zClosed,
            },
        },
        right: {
            rotation: {
                fullOpen: MathUtils.degToRad(94),
                halfOpen: MathUtils.degToRad(78),
                closed: MathUtils.degToRad(0),
            },
            x: {
                fullOpen: -xFull,
                halfOpen: -xHalf,
                closed: -xClosed,
            },
            z: {
                fullOpen: zFull,
                halfOpen: zHalf,
                closed: zClosed,
            },
        },
    },
    zOffset: 18,
};

const posXAnimationStart = 50;
const posXAnimationOffset = 20;

const drawersAnimationConfig = {
    row_1: posXAnimationStart + posXAnimationOffset,
    row_2: posXAnimationStart + posXAnimationOffset * 2,
    row_3: posXAnimationStart + posXAnimationOffset * 3,
    row_4: posXAnimationStart + posXAnimationOffset * 4,
    row_5: posXAnimationStart + posXAnimationOffset * 5,
    row_6: posXAnimationStart + posXAnimationOffset * 6,
    closed: 0,
    halfOpen: 1,
    fullOpen: 2,
};

export {
    doorsAnimationConfig,
    drawersAnimationConfig,
};
