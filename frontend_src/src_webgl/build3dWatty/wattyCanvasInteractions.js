/* eslint-disable class-methods-use-this */
import { Power0, TweenLite } from 'gsap';
import { doorsAnimationConfig, drawersAnimationConfig } from './wattyCanvasAnimationsConfig';

class WattyCanvasInteractions {
    constructor({
        scene,
        animationConfig,
        build3d,
    }) {
        this.scene = scene;
        this.animationConfig = animationConfig;
        this.build3d = build3d;
        this.selectedShelfId = null;
        this.interactions = {
            default: {
                doors: 'halfOpen',
                drawers: 'closed',
            },
            hover: {
                doors: 'fullOpen',
                drawers: 'halfOpen',
            },
        };
        this.animationStatus = {
            doorsFinished: false,
            drawersFinished: false,
        };
        this.initDoorsPosition = {
            doors_exterior: [],
            doors_raiser: [],
        };
    }

    handleComponentHover({ configId, doorsAreOpened = true }) {
        // ? DOORS
        let doorsToOpen = this.build3d.sceneItems.doors_exterior.filter(item => item.name === `door_exterior_group:${configId}:left` || item.name === `door_exterior_group:${configId}:right`);
        if (this.selectedShelfId) doorsToOpen = doorsToOpen.filter(item => item.name !== `door_exterior_group:${this.selectedShelfId}:left` && item.name !== `door_exterior_group:${this.selectedShelfId}:right`);
        this.rotateDoors({ doors: doorsToOpen, state: this.interactions.hover.doors });
        let doorsToClose = this.build3d.sceneItems.doors_exterior.filter(item => item.name !== `door_exterior_group:${configId}:left` && item.name !== `door_exterior_group:${configId}:right`);
        if (this.selectedShelfId) doorsToClose = doorsToClose.filter(item => item.name !== `door_exterior_group:${this.selectedShelfId}:left` && item.name !== `door_exterior_group:${this.selectedShelfId}:right`);
        this.rotateDoors({ doors: doorsToClose, state: this.interactions.default.doors });

        if (this.selectedShelfId) {
            const selectedDoor = this.build3d.sceneItems.doors_exterior.filter(item => (item.name === `door_exterior_group:${this.selectedShelfId}:left` || item.name === `door_exterior_group:${this.selectedShelfId}:right`));
            // If hover is on selected go fullOpen and stay
            // If hover is not on selected make selected halfOpen and hovered fullOpen
            if (configId === this.selectedShelfId) {
                if (!this.animationStatus.doorsFinished) {
                    this.rotateDoors({ doors: selectedDoor, state: 'fullOpen' });
                    this.animationStatus.doorsFinished = true;
                }
            } else {
                this.rotateDoors({ doors: selectedDoor, state: 'halfOpen' });
                this.animationStatus.doorsFinished = false;
            }
        }

        // ? DRAWERS
        let drawersToOpen = this.build3d.sceneItems.drawers.filter(item => item.name === `drawer_group:${configId}`);
        if (this.selectedShelfId) drawersToOpen = drawersToOpen.filter(item => item.name !== `drawer_group:${this.selectedShelfId}`);
        this.moveDrawers({ drawers: drawersToOpen, state: this.interactions.hover.drawers });
        let drawersToClose = this.build3d.sceneItems.drawers.filter(item => item.name !== `drawer_group:${configId}`);
        if (this.selectedShelfId) drawersToClose = drawersToClose.filter(item => item.name !== `drawer_group:${this.selectedShelfId}`);
        this.moveDrawers({ drawers: drawersToClose, state: this.interactions.default.drawers });

        if (this.selectedShelfId) {
            const selectedDrawer = this.build3d.sceneItems.drawers.filter(item => item.name === `drawer_group:${this.selectedShelfId}`);
            // If hover is on selected go fullOpen and stay
            // If hover is not on selected make selected halfOpen and hovered fullOpen
            if (configId === this.selectedShelfId) {
                if (!this.animationStatus.drawersFinished) {
                    this.moveDrawers({ drawers: selectedDrawer, state: doorsAreOpened ? 'fullOpen' : 'closed' });
                    this.animationStatus.drawersFinished = true;
                }
            } else {
                this.moveDrawers({ drawers: selectedDrawer, state: 'closed' });
                this.animationStatus.drawersFinished = false;
            }
        }
    }

    handleRaiserHover({ configId, doorsAreClosed = true }) {
        const doorsToOpen = this.build3d.sceneItems.doors_raiser
            .filter(item => item.name === `door_raiser_group:${configId}:left` || item.name === `door_raiser_group:${configId}:right`);
        const doorsToClose = this.build3d.sceneItems.doors_raiser
            .filter(item => (item.name !== `door_raiser_group:${configId}:left` && item.name !== `door_raiser_group:${configId}:right`) && item.name.split(':')[0] === 'door_raiser_group');
        this.rotateDoors({ doors: doorsToOpen });
        this.rotateDoors({ doors: doorsToClose, state: doorsAreClosed ? 'closed' : 'halfOpen' });
    }

    rotateDoors({ doors, state = 'fullOpen' }) {
        if (doors.length === 0) return;
        const dictTranslate = {
            door_exterior_group: 'doors_exterior',
            door_raiser_group: 'doors_raiser',
        };
        const doorType = dictTranslate[doors[0].name.split(':')[0]];

        // Filter so that both arrs contain the same number of elements
        const initPositionArr = this.initDoorsPosition[doorType].filter(el => {
            let newEl = false;
            doors.forEach(el2 => {
                if (el2.name === el.name) {
                    newEl = { x: el.valX, z: el.valZ };
                }
            });
            return newEl;
        });
        doors.forEach((i, index) => {
            const direction = i.name.split(':')[2];
            TweenLite.to(i.rotation, 0.2, { ease: Power0.easeNone, y: doorsAnimationConfig[doorType][direction].rotation[state] });
            TweenLite.to(i.position, 0.2, {
                ease: Power0.easeNone,
                x: initPositionArr[index].valX + doorsAnimationConfig[doorType][direction].x[state],
                z: initPositionArr[index].valZ + doorsAnimationConfig[doorType][direction].z[state],
            });
        });
    }

    moveDrawers({ drawers, state = 'fullOpen' }) {
        if (drawers.length === 0) return;
        drawers.forEach(i => {
            const multi = parseInt((1200 - i.position.y) / 200, 10);
            const targetPosition = ((100 + 30 * multi) * drawersAnimationConfig[state]) + 18;
            TweenLite.to(i.position, 0.3, { ease: Power0.easeNone, z: targetPosition });
        });
    }

    openAllDoorsAndDrawers() {
        this.moveDrawers({ drawers: this.build3d.sceneItems.drawers, state: 'fullOpen' });
        this.rotateDoors({ doors: this.build3d.sceneItems.doors_exterior });
        this.rotateDoors({ doors: this.build3d.sceneItems.doors_raiser });
    }

    setDoorsAndDrawerDefaultState({ doorsAreOpened = true, raiserUpdate = true, dimensionsAreVisible = false }) {
        // eslint-disable-next-line prefer-destructuring
        let drawers = this.build3d.sceneItems.drawers;
        if (this.selectedShelfId) drawers = drawers.filter(item => item.name !== `drawer_group:${this.selectedShelfId}`);
        this.moveDrawers({ drawers, state: this.interactions.default.drawers });

        let doors = this.build3d.sceneItems.doors_exterior;
        if (doors.length > 0) {
            if (this.selectedShelfId) doors = doors.filter(item => item.name !== `door_exterior_group:${this.selectedShelfId}:left` && item.name !== `door_exterior_group:${this.selectedShelfId}:right`);
            this.rotateDoors({ doors, state: this.interactions.default.doors });
        }
        if (raiserUpdate && this.build3d.sceneItems.doors_raiser.length > 0) {
            this.rotateDoors({ doors: this.build3d.sceneItems.doors_raiser, state: doorsAreOpened ? this.interactions.default.doors : 'closed' });
        }

        if (!dimensionsAreVisible && this.selectedShelfId) {
            const selectedDrawers = this.build3d.sceneItems.drawers.filter(item => item.name === `drawer_group:${this.selectedShelfId}`);
            const selectedDoors = this.build3d.sceneItems.doors_exterior.filter(item => item.name === `door_exterior_group:${this.selectedShelfId}:left` || item.name === `door_exterior_group:${this.selectedShelfId}:right`);
            this.moveDrawers({ drawers: selectedDrawers, state: doorsAreOpened ? 'fullOpen' : 'closed' });
            this.rotateDoors({ doors: selectedDoors, state: 'fullOpen' });
        }
    }

    setDoorsAndDrawersToClosed({ dimensionsAreVisible = false }) {
        // eslint-disable-next-line prefer-destructuring
        let drawers = this.build3d.sceneItems.drawers;
        if (this.selectedShelfId) drawers = drawers.filter(item => item.name !== `drawer_group:${this.selectedShelfId}`);
        let doors = this.build3d.sceneItems.doors_exterior;
        if (this.selectedShelfId) doors = doors.filter(item => item.name !== `door_exterior_group:${this.selectedShelfId}:left` && item.name !== `door_exterior_group:${this.selectedShelfId}:right`);

        if (dimensionsAreVisible) {
            this.moveDrawers({ drawers: this.build3d.sceneItems.drawers, state: 'closed' });
            if (this.build3d.sceneItems.doors_exterior.length > 0) {
                this.rotateDoors({ doors: this.build3d.sceneItems.doors_exterior, state: 'closed' });
            }
        } else {
            this.moveDrawers({ drawers, state: 'closed' });
            if (this.build3d.sceneItems.doors_exterior.length > 0) {
                this.rotateDoors({ doors, state: 'closed' });
            }
        }

        if (this.build3d.sceneItems.doors_raiser.length > 0) {
            this.rotateDoors({ doors: this.build3d.sceneItems.doors_raiser, state: 'closed' });
        }
    }

    closeAllRaisers({ doorsAreClosed = true }) {
        if (this.build3d.sceneItems.doors_raiser.length > 0) {
            this.rotateDoors({ doors: this.build3d.sceneItems.doors_raiser, state: doorsAreClosed ? 'closed' : 'halfOpen' });
        }
    }

    setInteractionState(config) {
        this.interactions = {
            ...config,
        };
    }

    setSelectedShelfId({ id }) {
        this.selectedShelfId = id;
    }

    setInitDoorsPosition(arr) {
        this.initDoorsPosition = arr;
    }
}

export default WattyCanvasInteractions;
