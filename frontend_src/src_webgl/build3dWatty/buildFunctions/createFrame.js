export default ({
    geom,
    scene,
    elements,
    sceneItems,
}) => {
    let elementName = 'box_left_right_frame';
    if (geom.subtype === 'top-left' || geom.subtype === 'top-middle' || geom.subtype === 'top-right') elementName = 'box_top_frame';

    const elementType = 'frame';
    const element = elements[elementName].clone();
    const scale = 10;

    element.scale.y = geom.scale.y / scale;
    element.scale.x = geom.scale.x / scale;
    element.scale.z = geom.scale.z / scale;
    element.position.x = geom.centroid.x;
    element.position.y = geom.centroid.y;
    element.position.z = geom.z1;
    element.name = elementName;

    sceneItems[elementType].push(element);
    scene.add(element);
};
