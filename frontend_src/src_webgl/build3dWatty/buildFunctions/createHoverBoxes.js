import {
    PlaneGeometry,
    MeshBasicMaterial,
    Mesh,
} from 'three';

export default ({
    geom,
    scene,
    sceneItems,
}) => {
    const OVERLAP = 2;
    const elementType = 'components';
    const geometry = new PlaneGeometry(geom.hover.scale.x, geom.hover.scale.y, 1);
    const material = new MeshBasicMaterial({
        color: 0xFF3C00,
        transparent: true,
        opacity: 0,
        depthWrite: false,
    });

    const element = new Mesh(geometry, material);
    element.renderOrder = 10000;
    element.position.set(
        geom.hover.centroid.x,
        geom.hover.centroid.y,
        geom.hover.z1 + geom.hover.z2 + OVERLAP,
    );
    element.name = `hoverBox:${geom.hover.m_config_id}`;

    scene.add(element);
    sceneItems[elementType].push(element);
};
