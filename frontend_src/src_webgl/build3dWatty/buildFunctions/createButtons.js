import {
    BoxGeometry,
    MeshBasicMaterial,
    Mesh,
} from 'three';

export default ({
    geom,
    sceneItems,
    scene,
}) => {
    const geometry = new BoxGeometry(100, 200, 2);
    const material = new MeshBasicMaterial({ color: 0xff3c00 });
    const element = new Mesh(geometry, material);
    const elementType = 'buttons';
    const OFFSET = 50;

    element.position.set(
        geom.centroid.x,
        geom.centroid.y - OFFSET,
        geom.centroid.z,
    );
    element.name = `button:${geom.m_config_id}`;
    element.visible = false;
    scene.add(element);
    sceneItems[elementType].push(element);
};
