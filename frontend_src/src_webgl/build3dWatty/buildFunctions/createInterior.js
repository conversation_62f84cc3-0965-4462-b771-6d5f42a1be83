export default ({
    geom,
    scene,
    elements,
    sceneItems,
}) => {
    const elementName = geom.subtype === 'drawers'
        ? 'interior_dark'
        : 'interior';
    const elementType = 'interiors';
    const element = elements[elementName].clone();
    const scale = 10;

    element.scale.x = (geom.scale.x + 3) / scale;
    element.scale.y = (geom.scale.y + 3) / scale;
    element.scale.z = (geom.scale.z - 6) / scale;
    element.position.x = geom.centroid.x;
    element.position.y = geom.centroid.y;
    element.position.z = geom.z1 + 6;
    element.name = elementName;
    sceneItems[elementType].push(element);
    scene.add(element);
};
