import { Group, Vector3 } from 'three';
import { doorsAnimationConfig } from '../wattyCanvasAnimationsConfig';
import createDoorHinges from './createDoorHinges';


export default ({
    geom,
    scene,
    elements,
    sceneItems,
    isDoorVisible,
    isDoorClosed,
}) => {
    const {
        direction, m_config_id, handle, hinges,
    } = geom;
    const scale = 10;
    const elementName = 'door_exterior_group';
    const elementType = 'doors_exterior';
    const doorsExteriorGroup = new Group();
    let doorFront;
    let groupPivot = {};
    let rotation = {};
    let frontPosition = null;
    const handleOffset = 25;
    const handleShadowOffset = 30;
    const rotationState = isDoorClosed ? 'closed' : 'halfOpen';

    switch (direction) {
    case 'right':
        doorFront = elements.box_door_right.clone();
        groupPivot = new Vector3(geom.x2, geom.y1, geom.z1);
        rotation = doorsAnimationConfig.doors_exterior.right.rotation[rotationState];
        frontPosition = -1;

        break;

    case 'left':
        doorFront = elements.box_door_left.clone();
        groupPivot = new Vector3(geom.x1, geom.y1, geom.z1);
        rotation = doorsAnimationConfig.doors_exterior.left.rotation[rotationState];
        frontPosition = 1;
        break;
    default:
    }

    doorsExteriorGroup.name = `${elementName}:${m_config_id}:${direction}`;
    doorsExteriorGroup.position.set(groupPivot.x, groupPivot.y, groupPivot.z + doorsAnimationConfig.zOffset);

    doorFront.scale.x = (geom.scale.x / scale);
    doorFront.scale.y = (geom.scale.y / scale);
    doorFront.scale.z = (geom.scale.z / scale);
    doorFront.position.x = frontPosition * Math.abs(geom.x1 - geom.x2) / 2;
    doorFront.position.y = Math.abs(geom.y1 - geom.y2) / 2;
    doorFront.position.z = 0;
    doorFront.visible = isDoorVisible;

    doorsExteriorGroup.add(doorFront);
    if (handle) {
        const doorHandleY = handle.y - geom.y1 - 80;

        const doorHandle = elements.handle.clone();
        doorHandle.position.x = frontPosition * Math.abs(geom.x1 - geom.x2) - frontPosition * handleOffset + 4;
        doorHandle.position.y = doorHandleY;
        doorHandle.position.z = 0;
        doorHandle.renderOrder = 10;
        doorHandle.visible = isDoorVisible;
        doorsExteriorGroup.add(doorHandle);

        const handleShadow = elements.handle_shadow.clone();
        handleShadow.position.x = frontPosition * Math.abs(geom.x1 - geom.x2) - frontPosition * handleShadowOffset;
        handleShadow.position.y = doorHandleY;
        handleShadow.position.z = 0;
        handleShadow.scale.x = 0.3;
        handleShadow.scale.y = 0.9;
        handleShadow.renderOrder = 4;
        handleShadow.visible = isDoorVisible;
        doorsExteriorGroup.add(handleShadow);
    }
    hinges.forEach(hinge => {
        createDoorHinges({
            door: geom,
            geom: hinge,
            scene: doorsExteriorGroup,
            elements,
            direction,
        });
    });

    doorsExteriorGroup.rotation.y = rotation;
    sceneItems[elementType].push(doorsExteriorGroup);
    scene.add(doorsExteriorGroup);
};
