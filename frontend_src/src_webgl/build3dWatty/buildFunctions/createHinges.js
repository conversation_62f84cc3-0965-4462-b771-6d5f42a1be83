import { Group } from 'three';

export default ({
    geom, scene, elements, sceneItems,
}) => {
    const { m_config_id, pivot: { x, y, z }, direction } = geom;
    const elementName = 'hinges_interior_group';
    const elementType = 'hinges';
    const hingeGroup = new Group();

    const offsetX = (direction === 'left' ? 1 : -1) * 20;
    const offsetZ = 70;

    hingeGroup.name = `${elementName}:${m_config_id}`;
    hingeGroup.position.set(x - offsetX, y, z + offsetZ);
    const element = elements[`hinge_frame_part_${direction}`].clone();
    hingeGroup.add(element);

    sceneItems[elementType].push(hingeGroup);
    scene.add(hingeGroup);
};
