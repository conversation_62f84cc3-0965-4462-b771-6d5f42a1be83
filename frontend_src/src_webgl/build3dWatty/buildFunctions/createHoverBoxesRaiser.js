import {
    PlaneGeometry,
    MeshBasicMaterial,
    Mesh,
} from 'three';

export default ({
    geom,
    scene,
    sceneItems,
}) => {
    const OVERLAP = 2;
    const elementType = 'hovers';
    const geometry = new PlaneGeometry(geom.scale.x, geom.scale.y, 1);
    const material = new MeshBasicMaterial({
        color: 0xFF3C00,
        transparent: true,
        opacity: 0,
        depthWrite: false,
    });

    const element = new Mesh(geometry, material);
    element.renderOrder = 10000;
    element.position.set(
        geom.centroid.x,
        geom.centroid.y,
        geom.z1 + geom.z2 + OVERLAP,
    );
    element.name = `hoverBoxR:${geom.m_config_id}`;

    scene.add(element);
    sceneItems[elementType].push(element);
};
