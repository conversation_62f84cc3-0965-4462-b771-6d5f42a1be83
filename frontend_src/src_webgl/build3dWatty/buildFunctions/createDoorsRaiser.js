import { Group, Vector3 } from 'three';
import { doorsAnimationConfig } from '../wattyCanvasAnimationsConfig';
import createDoorHinges from './createDoorHinges';

export default ({
    geom,
    scene,
    elements,
    sceneItems,
    isDoorVisible,
    isDoorClosed,
}) => {
    const { direction, m_config_id, hinges } = geom;
    const scale = 10;
    const elementName = 'door_raiser_group';
    const elementType = 'doors_raiser';
    const doorsRaiserGroup = new Group();
    let doorFront;
    let groupPivot = {};
    let rotation = {};
    let frontPosition = null;
    const rotationState = isDoorClosed ? 'closed' : 'halfOpen';

    switch (direction) {
    case 'right':
        doorFront = elements.box_door_raiser_right.clone();
        groupPivot = new Vector3(geom.x2, geom.y1, geom.z1);
        rotation = doorsAnimationConfig.doors_raiser.right.rotation[rotationState];
        frontPosition = -1;
        break;

    case 'left':
        doorFront = elements.box_door_raiser_left.clone();
        groupPivot = new Vector3(geom.x1, geom.y1, geom.z1);
        rotation = doorsAnimationConfig.doors_raiser.left.rotation[rotationState];
        frontPosition = 1;
        break;
    default:
    }

    doorsRaiserGroup.name = `${elementName}:${m_config_id}:${direction}`;
    doorsRaiserGroup.position.set(groupPivot.x, groupPivot.y, groupPivot.z + doorsAnimationConfig.zOffset);

    doorFront.scale.x = (geom.scale.x / scale);
    doorFront.scale.y = (geom.scale.y / scale);
    doorFront.scale.z = (geom.scale.z / scale);
    doorFront.position.x = frontPosition * Math.abs(geom.x1 - geom.x2) / 2;
    doorFront.position.y = Math.abs(geom.y1 - geom.y2) / 2;
    doorFront.position.z = 0;
    doorFront.visible = isDoorVisible;
    doorsRaiserGroup.add(doorFront);

    // TODO: uncomment, when proper pivots come out
    // hinges.forEach(hinge => {
    //     createDoorHinges({
    //         geom: hinge,
    //         scene: doorsRaiserGroup,
    //         elements,
    //         direction,
    //     });
    // });
    hinges.forEach(hinge => {
        const element = elements[`hinge_door_part_${direction}`].clone();
        // TODO: Proper hinge position - should be in line with door plane
        const offsetX = (direction === 'left' ? 1 : -1) * 20;
        element.position.z = -15;
        element.position.x = offsetX;
        element.position.y = hinge.pivot.y - 1960;
        doorsRaiserGroup.add(element);
    });


    doorsRaiserGroup.rotation.y = rotation;
    sceneItems[elementType].push(doorsRaiserGroup);
    scene.add(doorsRaiserGroup);
};
