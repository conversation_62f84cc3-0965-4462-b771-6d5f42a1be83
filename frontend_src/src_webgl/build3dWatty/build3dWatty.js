/* eslint-disable no-param-reassign */
import {
    Vector3,
    Box3,
    Color,
    Mesh,
} from 'three';
import modelConfigGLTF from '../rendererWatty/modelConfigGLTF';
import clearScene from './buildFunctions/clearScene';
import {
    SceneItems,
    itemFunctions,
    buildFunctions,
    shadowItems, hinges,
} from './buildFunctions/config';

class Build3DWatty {
    constructor({
        elements,
        isOffscreen = false,
        shadowRendered = 0,
        scene,
    }) {
        this.elements = elements;
        window.elements = this.elements;
        this.scene = scene;

        // GLTFLoader

        this.typek = [];
        this.target = new Vector3();
        this.boundingBox = new Box3();
        this.sceneItems = new SceneItems().items;
        this.itemFunctions = itemFunctions;
        this.shadowItems = [...shadowItems];
        this.shadowsOnScene = {};
        this.geom = {};
        this.isOffscreen = isOffscreen;
        this.attachBuildFunctions();
        this.shadowRendered = shadowRendered; // -1 don't render shadow ever, 0 not yet rendered, 1 rendered.
        this.isDoorVisible = true;
        this.isDoorClosed = false;
    }

    attachBuildFunctions() {
        Object.values(this.itemFunctions).forEach(item => {
            this[item] = buildFunctions[item];
        });
    }

    addSceneShadows() {
        if (this.shadowRendered === -1) return;
        this.shadowItems.forEach(i => {
            const element = this.elements[i].clone();
            element.name = i;
            element.position.z = -2;
            this.shadowsOnScene = Object.assign(this.shadowsOnScene, { [i]: element });
            this.scene.add(element);
        });
        this.shadowRendered = 1;
    }

    scaleAndPositionShadows() {
        if (this.shadowRendered === -1) return;
        if (this.shadowRendered === 0) this.addSceneShadows();
        const offsetX = 130;
        const offsetY = 30;
        const scaleX = (this.width - offsetX) / 100;
        const scaleY = (this.height - offsetY) / 100;
        const scaleZ = (this.depth) / 100;
        const depthFactor = this.depth === 630 ? 1 : 530 / 630;
        // floor middle
        this.shadowsOnScene.shadow_floor_middle.scale.x = scaleX;
        this.shadowsOnScene.shadow_floor_middle.scale.z = scaleZ;
        // floor left
        this.shadowsOnScene.shadow_floor_left.scale.z = scaleZ;
        this.shadowsOnScene.shadow_floor_left.position.x = -this.width / 2 + offsetX / 2;
        // floor right
        this.shadowsOnScene.shadow_floor_right.scale.z = scaleZ;
        this.shadowsOnScene.shadow_floor_right.position.x = this.width / 2 - offsetX / 2;
        // wall middle
        this.shadowsOnScene.shadow_wall_middle.scale.x = scaleX;
        this.shadowsOnScene.shadow_wall_middle.scale.y = scaleY;
        // wall left
        this.shadowsOnScene.shadow_wall_left.scale.y = scaleY;
        this.shadowsOnScene.shadow_wall_left.position.x = -this.width / 2 + offsetX / 2;
        // wall right
        this.shadowsOnScene.shadow_wall_right.scale.y = scaleY;
        this.shadowsOnScene.shadow_wall_right.scale.x = depthFactor;
        this.shadowsOnScene.shadow_wall_right.position.x = this.width / 2 - offsetX / 2;
    }

    init3d() {

    }

    updateMaterial(materialId) {
        Object.keys(this.elements).forEach(key => {
            const materialConfig = modelConfigGLTF(materialId)[key];
            if (hinges.some(i => i === key)) {
                this.elements[key].traverse(child => {
                    if (child instanceof Mesh) {
                        child.material.color = new Color(materialConfig.hingeColor);
                    }
                });
                return;
            }
            this.elements[key].traverse(child => {
                if (child instanceof Mesh) {
                    child.material.color = new Color(materialConfig.color);
                    child.material.lightMapIntensity = materialConfig.lightMapIntensity;
                    child.material.reflectivity = materialConfig.reflectivity;
                }
            });
        });
    }

    toggleDoorsVisibility(val) {
        this.isDoorVisible = val;
        this.sceneItems.doors_exterior.forEach(i => {
            i.traverse(obj => {
                obj.visible = val;
            });
        });
        this.sceneItems.doors_raiser.forEach(i => {
            i.traverse(obj => {
                obj.visible = val;
            });
        });
    }

    toggleDoorsClosed(val) {
        this.isDoorClosed = val;
    }


    moveTypek() {
        const leftOffest = 400;
        if (!this.typek.length) this.addTypek();
        this.typek[0].position.z = 2;
        this.typek[0].position.x = -(this.width / 2 + leftOffest);
    }

    addTypek() {
        this.elements.typek.traverse(child => {
            if (child.type === 'Mesh') {
                child.material.opacity = 0.8;
                child.material.transparent = true;
            }
        });
        const typek = this.elements.typek.clone();
        typek.name = 'typek';
        typek.scale.setX(10);
        typek.scale.setY(10);
        this.scene.add(typek);
        this.typek.push(typek);
    }

    rebuildWallsFromJson({
        geom,
        width,
        height,
        depth,
    }) {
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.geom = geom;
        this.drawWalls();
        this.scaleAndPositionShadows();
        if (!this.isOffscreen) this.moveTypek();
    }

    drawWalls() {
        this.clearScene();
        Object.keys(this.sceneItems).forEach(item => {
            const functionNameToCall = this.itemFunctions[item];
            // ownConsole.log('drawWalls -->', item, this.elements);
            this.geom[item].forEach(geom => {
                // ownConsole.log('element', item, geom)
                this[functionNameToCall]({
                    geom,
                    elements: this.elements,
                    scene: this.scene,
                    sceneItems: this.sceneItems,
                    isDoorVisible: this.isDoorVisible,
                    isDoorClosed: this.isDoorClosed,
                });
            });
        });
    }

    clearScene() {
        clearScene({
            scene: this.scene,
            sceneItems: this.sceneItems,
        });
    }
}

export default Build3DWatty;
