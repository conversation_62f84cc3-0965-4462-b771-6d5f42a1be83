

var FurnitureModel = function(renderer, scene, camera) {

     this.renderer = renderer;
     this.scene = scene;
     this.camera = camera;

     this.id = 0;
     this.preset = false;
     this.has_assembly = false;
     this.status = null;
     this.type = null;
     this.project_id = null;
     this.updated_at = null;
     this.preview = null;
     this.title = null;
     this.depth = 0;
     this.width = 0;
     this.height = 0;

     return this;
 };


 FurnitureModel.prototype = {

    getHeight: function() {
        return -1;
    },
    getWidth: function() {
        return -1;
    },
    getDepth: function() {
        return -1;
    },

     setPrice: function() {

     },

    createJson: function(add_preview) {
        var output = {};
        if (this.id > 0 && this.preset == false) {
            output["id"] = this.id;
        }
        output["furniture_status"] = this.status;
        output["preset"] = this.preset;
        output["type"] = this.type;
        output["title"] = this.title;
        if (this.add_preview && this.preview != null) {
            output["preview"] = this.preview;
        }
        return output;
    },

    readJson: function (jsonString) {
        _.extend(this, jsonString);
    },

    generateScreenshot: function(share) {
        let render = window.ivyScreenshot.createRenderForCart(window.ivy.getShelf(), window.doorFlagOn);
        return render
    },

    shareItem: function(callback) {
        $('.popup-product_share').trigger('force-open');
    },

    saveToLibrary: function(callback) {
        var request = new XMLHttpRequest();
        if (Site.CartController.isLoggedIn() == false) {
            window.jQuery('.popup-product_save').trigger('force-open');
            return;
        }
        if ((this.preset == false || cstm.savePreset == true) && cstm.item.status == 'saved' ) {
            request.open('PUT', '/api/v1/gallery/' + this.type + '/' + this.id + '/?mm=true', true);
        } else {
            request.open('POST', '/api/v1/gallery/' + this.type + '/?mm=true', true);
        }

        request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');

        request.onload = function () {
            if (request.status >= 200 && request.status < 400) {
                // Success!
                var data = JSON.parse(request.responseText);
                console.log('save data response', data);
                // Site.Track.trackAddToWishlist(data);
                if (data.similar) {
                    window.content_ids.push(data.similar.toString());
                }
                if (callback) {
                    callback(data);
                }
                if ($('.webglconfigurator').hasClass('mobile')){
                    Site.Notify.onSuccess(cstm_i18n.shelf_saved);
                }
                var overlay = $('.menu-overlay-box.library');
                var background = $('.overlay-wishlist-cover');
                overlay.find('tr').empty();
                overlay.find('tr').append('<td style="min-width: 100px !important"><img src="'+data.preview+'"></td>');
                overlay.find('tr').append('<td class="info"><p>Tylko Shelf</p><span>'+data.region_price_display+'</span></td>');
                overlay.addClass('visible');
                background.addClass('visible');
                setTimeout(function(){
                    overlay.removeClass('visible');
                    background.removeClass('visible');
                }, 3000);
                console.log('to!');
                //add mobile
                var overlayMobile = $('#wishlist-mobile-overlay').find('.input-container');
                var list = overlayMobile.find('ul');
                if(!list.hasClass('items-inside')) {
                    overlayMobile.find('.recently-added').removeClass('visually-hidden');
                    list.empty();
                    list.addClass('items-inside');
                    $('.more_items_in_wishlist').removeClass('visually-hidden');

                }
                list.append('<li><table> <tr> <td><img src="'+data.preview+'"></td> <td class="info"><p>'+data.title+'</p><span>'+data.region_price_display+'<br></span> <p>H158cm, W223cm, D32cm</p> <p></p> </td> </tr> </table> </li>');
                $('#wishlist-mobile-overlay').fadeIn();
            } else {
                // We reached our target server, but it returned an error
                if (request.status !== 401) {
                    console.log('error request', request.responseText);
                    console.log(request.responseText);
                    Site.Notify.onError(cstm_i18n.shelf_not_saved);
                    //alert('error '+request.responseText);
                }
            }
        };

        var dataWithPreview = this.createJson();
        var screenshot = this.generateScreenshot(true);
        dataWithPreview["magic_preview"] = screenshot.split(',')[1];
        request.send(JSON.stringify(dataWithPreview));

    },

    saveToCart: function(callback) {
        $('.pdp-slides').append('<div class="vertical-center loader-icon"><div class="vertical-center-content"><div class="spinner-loader-wrap"><div id="floatingCirclesG"><div class="spinner"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div></div></div></div></div></div>');


        var request = new XMLHttpRequest();
        request.open('POST', '/api/v1/gallery/' + this.type + '/add_to_cart/', true);
        request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');

        request.onload = function () {
            if (request.status >= 200 && request.status < 400) {
                // Success!
                var data = JSON.parse(request.responseText);
                if (callback) {
                    callback(data);
                    if (data.similar) {
                        window.content_ids.push(data.similar.toString());
                    }
                    Site.Track.trackAddToCart(Site.Track.jettyECommerce(data));
                } else {
                    Site.Notify.onSuccess(cstm_i18n.added_to_cart);
                }
                Site.sidebarCartAssembly.showCartHandler();

                Site.sidebarCartAssembly.fadeInLastCartItem(0);

                if (cstm.is_mobile && typeof ga != 'undefined') {
                    ga('send', 'pageview', '/cart/');
                }


            } else {
                // We reached our target server, but it returned an error
                if (request.status !== 401) {
                    console.log('error request', request.responseText);
                    console.log(request.responseText);
                    Site.Notify.onError(cstm_i18n.shelf_not_saved);
                }
            }
            $('.pdp-slides').find('.loader-icon').remove();
        };

        var dataWithPreview = this.createJson();
        var screenshot = this.generateScreenshot();
        dataWithPreview["magic_preview"] = screenshot.split(',')[1];
        request.send(JSON.stringify(dataWithPreview));
        ivy.createBlobs();
    },


    getPrice: function() {
        return 0;
    },

    getWeight: function() {},

     generateFurnitureData: function() {

        var screenshot = this.generateScreenshot(true);

        var dataWithPreview = this.createJson();

        dataWithPreview["magic_preview"] = screenshot;
        return dataWithPreview;
     },

    checkoutCustom: function(callback) {
        $('#furnitureButtonsConfigurator').append('<div class="vertical-center loader-icon"><div class="vertical-center-content"><div class="spinner-loader-wrap"><div id="floatingCirclesG"> <div class="f_circleG" id="frotateG_01"></div><div class="f_circleG" id="frotateG_03"></div><div class="f_circleG" id="frotateG_04"></div><div class="f_circleG" id="frotateG_05"></div><div class="f_circleG" id="frotateG_02"></div><div class="f_circleG" id="frotateG_06"></div><div class="f_circleG" id="frotateG_07"></div><div class="f_circleG" id="frotateG_08"></div></div></div></div></div>');
        var request = new XMLHttpRequest();
        request.open('POST', '/api/v1/gallery/' + this.type + '/checkout_custom/', true);
        request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
        request.onload = function () {
            if (201 === request.status) {
                var data = JSON.parse(request.responseText);
                if (callback) {
                    callback(data);
                }
                var checkout_url = '/checkout/' + data.order_id + '/';
                if ('en' !== cstm_i18n.language){
                    checkout_url = ['/', cstm_i18n.language, checkout_url].join('')
                }
                location.href = checkout_url;
            } else {
                if (request.status !== 401) {
                    console.log('error request', request.responseText);
                    console.log(request.responseText);
                    Site.Notify.onError(cstm_i18n.shelf_not_saved);
                }
            }
            $('#furnitureButtonsConfigurator').find('.loader-icon').remove();
        };
        var dataWithPreview = this.createJson();
        var screenshot = this.generateScreenshot(false);
        dataWithPreview['magic_preview'] = screenshot.split(',')[1];
        request.send(JSON.stringify(dataWithPreview));
    }
 };

module.exports = FurnitureModel;
