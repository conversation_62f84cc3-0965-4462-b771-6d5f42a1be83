function createVerticalPanels(index, oldPoints, newPoints, pattern) {
    const bevelDifference = 1.5;
    const { elements } = this;
    let dif;
    const { scene } = this;

    if (typeof oldPoints !== 'undefined') {
        dif = this.compare_dnas(oldPoints, newPoints);
    } else {
        dif = {
            newitem: true,
            scaled: true,
            only_moved: true,
        };
    }
    if (this.depth != this.depth_previous) {
        dif.scaled = true;
        dif.same = false;
    }
    if (dif.same === true) {
        return true;
    }

    let wall;

    if (dif.newitem) {
        wall = elements['left-right'].clone();
    } else {
        wall = this.walls.leftRightWalls[index];
    }

    const distance = Math.abs(newPoints.bottom.y - newPoints.top.y) + bevelDifference * 2;
    const scale = (distance) / 100;

    // LEFT/RIGHT - half of horizontal edge width
    let factor = newPoints.bottom.x < 0 ? -7.5 : 7.5;
    // chowanie dla slanta scianki
    if (pattern == 0) {
        factor = -factor;
    }

    wall.position.setX((newPoints.bottom.x + newPoints.top.x) / 2 + factor);

    if (dif.scaled) {
        const leftRightWall = this.boundingBox.setFromObject(elements['left-right']);
        wall.rotation.x = -Math.PI / 2;
        wall.scale.setY(this.depth / 100);
        wall.scale.setZ(scale);


        wall.position.setY(newPoints.bottom.y - bevelDifference);
        // wall.position.setX(newPoints.bottom.x + factor);
    }
    //
    if (dif.only_moved) {
        wall.position.setY(newPoints.bottom.y - bevelDifference);
        // wall.position.setX(newPoints.bottom.x + factor);
        wall.scale.setY(this.depth / 100);
        wall.scale.setZ(scale);
    }
    if (dif.newitem) {
        wall.name = 'Left-right-wall';
        this.scene.add(wall);
        this.walls.leftRightWalls.push(wall);
    }

    return null
}


export { createVerticalPanels }
