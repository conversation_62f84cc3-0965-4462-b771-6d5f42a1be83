function createSupport(index, oldPoints, newPoints, supportType) {
    let dif;
    const { elements } = this;
    const { scene } = this;
    if (typeof oldPoints !== 'undefined') {
        dif = this.compare_dnas(oldPoints, newPoints);
    } else {
        dif = {
            newitem: true,
            scaled: true,
            only_moved: true,
        };
    }
    if (this.depth != this.depth_previous) {
        dif.scaled = true;
        dif.same = false;
    }
    if (dif.same === true) {
        return true;
    }

    let support;
    if (dif.newitem) {
        support = elements.support.clone();
    } else {
        support = this.walls.supports[index];
    }
    let supportBox = this.boundingBox.setFromObject(elements.horizontal);

    let scaleX; let
        scaleZ;
    if (supportType === 'left') {
        support.rotation.x = -Math.PI / 2;
    } else {
        support.rotation.x = Math.PI / 2;
    }
    scaleZ = Math.abs(newPoints.bottom.y - newPoints.top.y) / supportBox.getSize(this.target).x;
    // scaleX = this.constants.gradientSupportWidth / supportBox.size().x;
    scaleX = Math.abs(newPoints.bottom.x - newPoints.top.x) / supportBox.getSize(this.target).x;

    if (dif.scaled) {
        support.scale.setZ(scaleZ);
        support.scale.setX(scaleX);
        if (dif.newitem) {
            support.scale.setY(1);
        }
        supportBox = this.boundingBox.setFromObject(support);
        support.position.set(
            Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.getSize(this.target).x / 2,
            Math.min(newPoints.bottom.y, newPoints.top.y) + Math.abs(newPoints.bottom.y - newPoints.top.y) / 2,
            18,
        );
    }

    if (dif.only_moved) {
        supportBox = this.boundingBox.setFromObject(support);
        support.position.set(
            Math.min(newPoints.bottom.x, newPoints.top.x) + supportBox.getSize(this.target).x / 2,
            Math.min(newPoints.bottom.y, newPoints.top.y)
            + Math.abs(newPoints.bottom.y - newPoints.top.y) / 2,
            18,
        );
    }

    if (dif.newitem) {
        support.name = 'supports';
        scene.add(support);
        if (this.walls.supports === undefined) {
            this.walls.supports = [];
        }
        this.walls.supports.push(support);
    }
}

export { createSupport };
