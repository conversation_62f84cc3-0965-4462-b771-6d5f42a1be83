// Horizontal has 15mm, but plank has 18mm, missing 4mm are in shadows and panels
import consts from './consts'

function createHorizontal(index, oldPoints, newPoints, addAsBoxes) {
    let elements = this.elements;
    let dif;
    if (typeof oldPoints !== "undefined") {
        dif = this.compare_dnas(oldPoints, newPoints);
    } else {
        dif = {
            newitem: true,
            scaled: true,
            only_moved: true
        };
    }
    if (this.depth != this.depth_previous) {
        dif.scaled = true;
        dif.same = false;
    }
    if (dif.same === true) {
        return true;
    }
    let horizontal;
    if (dif.newitem) {
        horizontal = elements["horizontal"].clone();
    } else {
        horizontal = this.walls.horizontals[index];
    }

    const distance = Math.abs(newPoints.bottom.x - newPoints.top.x );
    const horizontalBox = this.boundingBox.setFromObject(elements["horizontal"]);
    horizontal.position.setY(
        Math.abs(newPoints.bottom.y + newPoints.top.y ) / 2
    );

    if (dif.scaled) {
        let scale = distance / horizontalBox.getSize(this.target).x;
        horizontal.scale.setX(scale);
        horizontal.rotation.x = -Math.PI / 2;

        horizontal.scale.setZ(1);
        horizontal.scale.setY(this.depth / horizontalBox.getSize(this.target).y);
        horizontal.position.setX(
            Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2
        );
    }

    if (dif.only_moved) {
        horizontal.position.setX(
            Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2
        );
    }

    if (addAsBoxes) {
        horizontal.scale.setY((((Math.abs(newPoints.bottom.z - newPoints.top.z)))) / horizontalBox.getSize(this.target).y);
    }

    if (dif.newitem) {
        horizontal.name = 'horizontal';
        this.scene.add(horizontal);
        if (addAsBoxes) {
            this.walls.boxes.push(horizontal);
        } else {
            this.walls.horizontals.push(horizontal);
        }
    }
}

export { createHorizontal }