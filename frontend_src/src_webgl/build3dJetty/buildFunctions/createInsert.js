function createInsert(newPoints) {
    const insert = this.elements.insert.clone();
    const { subtype } = newPoints.raw;
    let insertBox = this.boundingBox.setFromObject(this.elements.insert);
    let scaleZ; let scaleX; let scaleY; let posX; let posY; let posZ;

    if (subtype === 'h' || subtype === 't') {
        scaleZ = Math.abs(newPoints.bottom.y - newPoints.top.y) / insertBox.getSize(this.target).x;
        scaleX = Math.abs(newPoints.bottom.x - newPoints.top.x) / insertBox.getSize(this.target).x;
        scaleY = Math.abs(newPoints.bottom.z - newPoints.top.z) / insertBox.getSize(this.target).x;
        insert.rotation.x = -Math.PI / 2;
        insert.scale.setZ(scaleZ);
        insert.scale.setX(scaleX);
        insert.scale.setY(scaleY);
        insertBox = this.boundingBox.setFromObject(insert);
        posX = Math.min(newPoints.bottom.x, newPoints.top.x) + insertBox.getSize(this.target).x / 2;
        posY = Math.min(newPoints.bottom.y, newPoints.top.y) + Math.abs(newPoints.bottom.y - newPoints.top.y) / 2;
        posZ = Math.abs(newPoints.bottom.z + newPoints.top.z) / 2;
    } else if (subtype === 'v') {
        insert.rotation.x = -Math.PI / 2;
        insert.rotation.y = -Math.PI / 2;
        scaleX = Math.abs(newPoints.bottom.y - newPoints.top.y) / 100;
        scaleY = Math.abs(newPoints.bottom.z - newPoints.top.z) / 100;
        scaleZ = Math.abs(newPoints.bottom.x - newPoints.top.x) / 100;

        insert.scale.setZ(scaleZ);
        insert.scale.setX(scaleX);
        insert.scale.setY(scaleY);
        insertBox = this.boundingBox.setFromObject(insert);
        posX = Math.min(newPoints.bottom.x, newPoints.top.x) + insertBox.getSize(this.target).x / 2;
        posY = Math.min(newPoints.bottom.y, newPoints.top.y) + Math.abs(newPoints.bottom.y - newPoints.top.y) / 2;
        posZ = Math.abs(newPoints.bottom.z + newPoints.top.z) / 2;
    }
    insert.position.set(posX, posY, posZ);
    insert.name = 'insert';

    this.scene.add(insert);
    if (this.walls.boxes === undefined) {
        this.walls.boxes = [];
    }
    this.walls.boxes.push(insert);
}

export { createInsert };
