import { Group } from 'three'

const plankScaleMM = 18;

function createDrawers(position, size, rowHeights, shelf_type, drawer_description) {
    const { scene } = this;
    const door_group = new Group();
    const offset = drawer_description.innerOffset;
    const innerOffsetZ = 9;
    const innerOffsetYSides = 4;

    door_group.name = `drawer_group:${drawer_description.m_config_id}`;

    const {
        door_handler_width,
        door_handler_height,
        drawer_cutout,
        front_handling_size,
        bottom_offset,
        bottom_thickness,
        bottom_depth,
        back_height,
        sides_length,
        sides_height,
    } = drawer_description;

    let doorBox;

    door_group.position.set(position.x, position.y, position.z);


    // handler
    if (shelf_type === 1) {
        // front
        const door = this.elements.door.clone();

        door.name = 'szufladaFront';
        // console.log('color szuflady', ivy.material);
        door.position.set(0, position.y - door_group.position.y, 0);
        doorBox = this.boundingBox.setFromObject(this.elements.drawer_front);

        door.scale.setX((size.x - offset) / doorBox.getSize(this.target).x);
        door.scale.setY((size.y - offset) / doorBox.getSize(this.target).y);
        door.scale.setZ(size.z / doorBox.getSize(this.target).z);
        door_group.add(door);

        const drawer_handler = this.elements.handle_short_left.clone();
        drawer_handler.name = 'Handler';
        // console.log('posy', size.y);
        drawer_handler.rotation.x = -Math.PI / 2;

        const leftHandleXPosition = position.x - size.x / 2 - door_group.position.x + door_handler_width / 2;
        const rightHandleXPosition = position.x + size.x / 2 - door_group.position.x - door_handler_width / 2;
        const handleXPosition = drawer_description.flip === 1 ? (rightHandleXPosition - offset) : (leftHandleXPosition + offset);

        drawer_handler.position.setX(handleXPosition);
        drawer_handler.position.setY(size.y / 2 - door_handler_height / 2 + 2);
        drawer_handler.position.setZ(size.z - plankScaleMM / 2 - 1);

        doorBox = this.boundingBox.setFromObject(drawer_handler);
        drawer_handler.scale.set(1, 1, 1);

        drawer_handler.traverse(child => {
            if (child.type === 'Mesh' && typeof child.material !== 'undefined') {
                child.material.needsUpdate = true;
                child.geometry.center();
            }
        });

        door_group.add(drawer_handler);

        const drawer_handler_shadow = this.elements.handle_short_left_shadow.clone();
        drawer_handler_shadow.name = 'Handler shadow';

        drawer_handler_shadow.rotation.x = -Math.PI / 2;

        const shadowHandleXPosition = drawer_description.flip === 1 ? 
            (rightHandleXPosition - offset * 2) : (leftHandleXPosition + offset * 2);

        drawer_handler_shadow.position.setX(shadowHandleXPosition);
        drawer_handler_shadow.position.setY(size.y / 2 - door_handler_height / 2 - 2);
        drawer_handler_shadow.position.setZ(size.z - plankScaleMM / 2);

        drawer_handler_shadow.traverse(child => {
            if (child.type === 'Mesh' && typeof child.material !== 'undefined') {
                child.material.needsUpdate = true;
                child.geometry.center();
            }
        });

        doorBox = this.boundingBox.setFromObject(drawer_handler_shadow);
        drawer_handler_shadow.scale.setY(1);
        drawer_handler_shadow.scale.setX(1);
        drawer_handler_shadow.scale.setZ(1);
        drawer_handler_shadow.renderOrder = 100;

        door_group.add(drawer_handler_shadow);
    } else if (shelf_type === 2) {
        let door;
        if (size.y >= 300) {
            door = this.elements.fdoors_big.clone();
        } else {
            door = this.elements.fdoors.clone();
        }
        // let row_number = getRowByY(position.y, rowHeights);
        door.name = 'DrawerFrontT01V';
        door.position.setX(
            position.x - door_group.position.x,
        );
        door.position.setY(-front_handling_size / 2);
        door.position.setZ(0);

        let doorBox = this.boundingBox.setFromObject(this.elements.door);
        // door.scale.setY(((size.y - offset) / doorBox.size().y)/10);
        door.scale.setY(((size.y - front_handling_size - offset) / doorBox.getSize(this.target).y) / 10);
        door.scale.setX(((size.x - offset) / doorBox.getSize(this.target).x) / 10);
        door.scale.setZ((size.z / doorBox.getSize(this.target).z) / 10);
        door_group.add(door);
        this.walls.doors.push(door);

        // Create door handle
        const door_handler = this.elements.fhandle.clone();
        door_handler.rotation.z = -Math.PI / 2;
        door_handler.scale.setX(0.1);
        door_handler.scale.setY(((size.x - offset) / doorBox.getSize(this.target).x));
        door_handler.scale.setZ(0.10);

        door_handler.position.setX(position.x - door_group.position.x);
        door_handler.position.setY(size.y / 2 - front_handling_size * 2);

        door_group.add(door_handler);
        door_handler.name = 'fhandle';
    } else {
        // front
        const door = this.elements.drawer_front.clone();
        door.traverse(child => {
            if (child.type === 'Mesh') {
                // child.material = self.magical_materials_for_rows['doors'][0];
            }
        });
        door.name = 'szufladaFront';
        // console.log('color szuflady', ivy.material);

        door.position.setX(0);
        door.position.setY(position.y - front_handling_size / 2 - door_group.position.y);
        door.position.setZ(0);

        doorBox = this.boundingBox.setFromObject(this.elements.drawer_front);

        door.scale.setX((size.x + offset * 2) / doorBox.getSize(this.target).x);
        door.scale.setY((size.y - front_handling_size - offset - 3) / doorBox.getSize(this.target).y);
        door.scale.setZ(size.z / doorBox.getSize(this.target).z);
        door_group.add(door);
        // handler
        const door_handler = this.elements.handle_drawer.clone();
        door_handler.name = 'Trzymadelko';

        door_handler.rotation.z -= 90 * Math.PI / 180;
        door_handler.rotation.y += 90 * Math.PI / 180;

        // door_handler.position.setX(position.x+(handling_flip*((size.x/2)-(handling_size))) + (handling_flip *offset) - door_group.position.x);
        door_handler.position.setY(
            position.y
            + (size.y / 2 - front_handling_size)
            + offset
            - door_group.position.y + 6,
        );

        door_handler.position.setX(0);
        door_handler.position.setZ(0);

        doorBox = this.boundingBox.setFromObject(door_handler);
        const overscale = 1;
        door_handler.scale.setY(
            12,
        );
        door_handler.scale.setZ(size.x * overscale / doorBox.getSize(this.target).x);
        door_handler.scale.setX(size.z * overscale / doorBox.getSize(this.target).z);

        door_handler.traverse(child => {
            if (child.type === 'Mesh') {
                // child.material = self.magical_materials_for_rows["handlers"][0];
                child.material.needsUpdate = true;
                child.geometry.center();
            }
        });
        door_group.add(door_handler);
    }

    // Bottom

    const bottom = this.elements['support-drawer'].clone();

    bottom.name = 'szufladaBottom';

    bottom.position.setX(0);
    bottom.position.setY(position.y - size.y / 2 + drawer_cutout - door_group.position.y + 13);
    bottom.position.setZ(-bottom_depth / 2 - innerOffsetZ);
    bottom.rotation.x = Math.PI / 360;

    doorBox = this.boundingBox.setFromObject(bottom);
    bottom.scale.setX((size.x - bottom_offset - innerOffsetYSides * 2 - plankScaleMM) / doorBox.getSize(this.target).x);
    bottom.scale.setY(bottom_thickness / doorBox.getSize(this.target).y);
    bottom.scale.setZ(bottom_depth / doorBox.getSize(this.target).z);

    door_group.add(bottom);

    // Back

    const back = this.elements['support-drawer'].clone();

    back.name = 'szufladaBack';

    // back.rotation.y = back.rotation.y + 90 * Math.PI / 180;
    back.rotation.x = -90 * Math.PI / 180;
    back.position.setX(0);
    // back.position.setY(((position.y - (front_handling_size / 2)) + drawer_cutout) - door_group.position.y);
    back.position.setY(position.y - size.y / 2 + plankScaleMM + drawer_cutout - door_group.position.y + back_height / 2);
    back.position.setZ(-bottom_depth);
    doorBox = this.boundingBox.setFromObject(back);
    back.scale.setX(
        (size.x - plankScaleMM * 2) / doorBox.getSize(this.target).x,
    ); // was xyz before rotation
    back.scale.setZ(back_height / doorBox.getSize(this.target).y );
    // back.scale.setX(back_thickness / doorBox.getSize(this.target).z);
    door_group.add(back);


    // side left

    const sideA = this.elements['support-drawer'].clone();

    sideA.name = 'szufladaSideA';

    // back.rotation.y =
    sideA.rotation.z = -90 * Math.PI / 180;
    sideA.rotation.x = Math.PI;
    sideA.position.setX(-(size.x / 2 - plankScaleMM / 2) + innerOffsetYSides);
    sideA.position.setY(position.y - size.y / 2 + drawer_cutout - door_group.position.y + sides_height / 2);
    sideA.position.setZ(-bottom_depth / 2 - innerOffsetZ);

    doorBox = this.boundingBox.setFromObject(sideA);
    sideA.scale.setX((sides_height ) / doorBox.getSize(this.target).y);
    sideA.scale.setZ(sides_length / doorBox.getSize(this.target).z);
    // console.log('sideA.scale.setZ', sides_length / doorBox.getSize(this.target).z);

    door_group.add(sideA);

    // side right

    const sideB = this.elements['support-drawer'].clone();

    sideB.name = 'szufladaSideB';

    sideB.rotation.z = 90 * Math.PI / 180;
    sideB.rotation.x = Math.PI;
    sideB.position.setX(size.x / 2 - plankScaleMM / 2 - innerOffsetYSides);
    sideB.position.setY(position.y - size.y / 2 + drawer_cutout - door_group.position.y + sides_height / 2);
    sideB.position.setZ(-bottom_depth / 2 - innerOffsetZ);

    doorBox = this.boundingBox.setFromObject(sideB);

    // sideB.scale.setZ(sides_thickness / doorBox.getSize(this.target).x);
    sideB.scale.setX(sides_height / doorBox.getSize(this.target).y);
    sideB.scale.setZ(sides_length / doorBox.getSize(this.target).z);

    door_group.add(sideB);

    this.walls.drawers.push(door_group);
    door_group.position.setY(position.y + size.y / 2);

    // animation here
    this.scene.add(door_group);
    scene.add(door_group);
    scene.updateMatrix();
}

export { createDrawers }
