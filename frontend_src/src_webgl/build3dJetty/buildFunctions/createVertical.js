function createVertical(index, oldPoints, newPoints, addAsBoxes) {
    const bevelDifference = 1.5;
    let dif;
    const { scene } = this;
    const { elements } = this;
    // check if there are any old points, used for initial run
    if (typeof oldPoints !== 'undefined') {
        dif = this.compare_dnas(oldPoints, newPoints);
    } else {
        dif = {
            newitem: true,
            scaled: true,
            only_moved: true,
        };
    }
    if (this.depth !== this.depth_previous) {
        dif.scaled = true;
        dif.same = false;
    }

    // if its the same quit
    if (dif.same === true) {
        return true;
    }
    let vertical;
    // if new item
    if (dif.newitem) {
        // clone the base element
        vertical = this.elements.vertical.clone();
    } else {
        // else take a vertical already existing
        vertical = this.walls.verticals[index];
    }
    vertical.position.setX((newPoints.bottom.x + newPoints.top.x) / 2);

    const distance = Math.abs(newPoints.bottom.y - newPoints.top.y) + bevelDifference * 2;

    // if scaled set proper height
    if (dif.scaled) {
        const verticalBox = this.boundingBox.setFromObject(elements.vertical);
        vertical.scale.setX(1);
        vertical.scale.setY(this.depth / 100);
        vertical.scale.setZ(distance / 100);
        vertical.rotation.x = -Math.PI / 2;
        if (dif.newitem) {
            // if its a new item it also needs width and depth
            vertical.scale.setX(1);
            vertical.scale.setY(this.depth / 100);
            vertical.scale.setZ(distance / 100);
        }
        // set proper position

        vertical.position.setY(
            Math.min(newPoints.bottom.y, newPoints.top.y) - bevelDifference,
        );
    }

    if (dif.only_moved) {
        // if it was only moved changed x,y. Z is always the same
        vertical.position.setY(
            Math.min(newPoints.bottom.y, newPoints.top.y) - bevelDifference,
        );
        vertical.scale.setY(this.depth / 100);
        vertical.scale.setZ(distance / 100);
    }
    if (addAsBoxes) {
        vertical.position.setZ((Math.abs(newPoints.bottom.z - newPoints.top.z)) - this.depth + 9);
        // vertical.position.setX(newPoints.bottom.x + 8);
    }

    if (dif.newitem) {
        // if it is a new item add it to scene and to the furniture parts pool
        vertical.name = 'vertical';
        scene.add(vertical);
        if (addAsBoxes) {
            this.walls.boxes.push(vertical);
        } else {
            this.walls.verticals.push(vertical);
        }
    }

    return null
}

export { createVertical }
