import {
    BoxGeometry,
    MeshBasicMaterial,
    Mesh,
} from 'three';

function createButton(newPoints,) {
    const geometry = new BoxGeometry(100, 200, 2);
    const material = new MeshBasicMaterial({ color: (newPoints.raw && newPoints.raw.selected ? 0xff3c00 : 0xCAD0D0) });
    const sphere = new Mesh(geometry, material);

    sphere.position.set(
        (newPoints.bottom.x + newPoints.top.x) / 2,
        (newPoints.bottom.y + newPoints.top.y) / 2 + 50,
        (newPoints.bottom.z + newPoints.top.z) / 2,
    );
    sphere.name = `button:${newPoints.raw.m_config_id}`;
    sphere.visible = false;

    this.scene.add(sphere);
    if (this.walls.boxes === undefined) {
        this.walls.boxes = [];
    }
    this.walls.boxes.push(sphere);
}

export { createButton }
