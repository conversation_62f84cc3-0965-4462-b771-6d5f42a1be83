function createHorizontalPanels(index, oldPoints, newPoints) {
    let dif;
    const { elements } = this;
    if (typeof oldPoints !== 'undefined') {
        dif = this.compare_dnas(oldPoints, newPoints);
    } else {
        dif = {
            newitem: true,
            scaled: true,
            only_moved: true,
        };
    }
    if (this.depth !== this.depth_previous) {
        dif.scaled = true;
        dif.same = false;
    }
    if (dif.same === true) {
        return true;
    }

    let wall;
    const distance = Math.abs(newPoints.bottom.x - newPoints.top.x);

    if (dif.newitem) {
        wall = elements['top-bottom'].clone();
    } else {
        wall = this.walls.topBottomWalls[index];
    }
    //
    // let distance = Math.abs(newPoints.bottom.x - newPoints.top.x);
    const offset = 8;

    if (dif.scaled) {
        const topBottomWall = this.boundingBox.setFromObject(elements['top-bottom']);
        const scale = distance / topBottomWall.getSize(this.target).x;
        wall.scale.setX(scale);
        wall.rotation.x = -Math.PI / 2;

        if (dif.newitem) {
            wall.scale.setZ(1);
        }
        wall.scale.setY(this.depth / topBottomWall.getSize(this.target).y);

        wall.position.y = newPoints.bottom.y !== 0 ? (newPoints.bottom.y + offset) : (newPoints.bottom.y) - offset;
        wall.position.setX(
            Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2,
        );
    }
    //
    if (dif.only_moved) {
        wall.position.y = newPoints.bottom.y !== 0 ? (newPoints.bottom.y + offset) : (newPoints.bottom.y) - offset;
        wall.position.setX(
            Math.min(newPoints.bottom.x, newPoints.top.x) + distance / 2,
        );
    }
    //
    if (dif.newitem) {
        wall.name = 'wall';
        this.scene.add(wall);
        this.walls.topBottomWalls.push(wall);
    }
}

export { createHorizontalPanels }
