function createShadows(index, oldPoints, newPoints, type) {
    let scene = this.scene;
    let elements = this.elements;
    const offest = 2;
    let dif;
    if (typeof oldPoints !== 'undefined') {
        dif = this.compare_shadows(oldPoints, newPoints);
    } else {
        dif = {
            newitem: true,
            scaled: true,
            only_moved: true,
        };
    }

    if (dif.same === true) {
        return true;
    }

    let shadow;
    let isShadowIn = (
        (this.context !== 'grid' && !this.isMobile) ||
        (window.cstm_i18n.gridView === true) ||
        (window.cstm_i18n.configuratorView && (window.is_mobile_loaded || window.is_tablet_loaded))
    );

    let shadowIn;
    let shadowBox;
    if (dif.newitem) {
        switch (type) {
        case 0:
            shadow = elements['shadow'].clone();
            shadowBox= this.boundingBox.setFromObject(elements['shadow']);
            if (isShadowIn) {
              shadowIn = elements['wall_compartment_shadow'].clone();
              shadowIn.name = 'shadowIn';
            }
            break;

        case 1:
            shadow = elements['shadow-left'].clone();
            shadowBox= this.boundingBox.setFromObject(elements['shadow-left']);
            break;

        case 2:
            shadow = elements['shadow-right'].clone();
            shadow.name = 'shadow-right';
            shadowBox= this.boundingBox.setFromObject(elements['shadow-right']);
            break;

        case 5:
            shadow = elements['shadow-bottom'].clone();
            shadowBox= this.boundingBox.setFromObject(elements['shadow']);
            if (isShadowIn) {
              shadowIn = elements['wall_compartment_shadow'].clone();
              shadowIn.name = 'shadowIn';
            }
            break;
        }
    } else {
        switch (type) {
        case 0:
            shadow = this.walls.shadows[0][index];
            shadowBox= this.boundingBox.setFromObject(elements['shadow']);
        break;

        case 1:
            shadow = this.walls.shadows[1][index];
            shadowBox = this.boundingBox.setFromObject(elements['shadow-left']);
        break;

        case 2:
            shadow = this.walls.shadows[2][index];
            shadowBox= this.boundingBox.setFromObject(elements['shadow-right']);
            break;

        case 3:
            shadow = elements['support-left'].clone();
            shadowBox= this.boundingBox.setFromObject(elements['shadow']);
            break;

        case 4:
            shadow = elements['support-right'].clone();
            shadowBox= this.boundingBox.setFromObject(elements['shadow']);
            break;

        case 5:
            shadow = this.walls.shadows[0][index];
            shadowBox= this.boundingBox.setFromObject(elements['shadow']);
            break;

        }
    }

    if (dif.scaled) {

        shadow.rotation.x = -Math.PI / 2;
        // scale for proper types of shadows
        shadow.scale.setX((newPoints.size.x + 3) / 100);
        shadow.scale.setZ((newPoints.size.y + 3) / 100);
        //ShadowIN
        if (typeof shadowIn !== 'undefined') {
            shadowIn.scale.setX((newPoints.size.x + 3) / 100);
            shadowIn.scale.setY((newPoints.size.y ) / 100);
            shadowIn.position.setX(newPoints.position.x);
            shadowIn.position.setY(newPoints.position.y + newPoints.size.y / 2);
            shadowIn.position.setZ(0);
        }

        if (type === 3 || type === 4) {
            shadow.scale.setZ(newPoints.size.z + 3 / shadowBox.getSize(this.target).z);
            shadow.position.setZ(newPoints.position.z);
        } else {
            shadow.scale.setY(this.depth / shadowBox.getSize(this.target).z);
        }
        shadow.position.setX(newPoints.position.x);
        shadow.position.setY(newPoints.position.y - 1.5);
        if (type === 5) {
            shadow.scale.setZ((newPoints.size.y + 18) / 100);
            shadow.position.setY(newPoints.position.y - 9);
        }
    }

    if (dif.moved) {
        shadow.position.setX(newPoints.position.x);
        shadow.position.setY(newPoints.position.y - 1.5);
        if (type === 5) shadow.position.setY(newPoints.position.y - 18);
    }

    if (dif.newitem) {
        scene.add(shadow);
        if (shadowIn) scene.add(shadowIn);
        switch (type) {
        case 0:
            this.walls.shadows[0].push(shadow);
            if (isShadowIn) this.walls.shadows[4].push(shadowIn);
            break;

        case 1:
            this.walls.shadows[1].push(shadow);
            break;

        case 2:
            this.walls.shadows[2].push(shadow);
            break;

        case 3:
            shadow = elements['support-left'].clone();
            shadowBox = this.boundingBox.setFromObject(elements['shadow']);
            break;

        case 4:
            shadow = elements['support-right'].clone();
            shadowBox = this.boundingBox.setFromObject(elements['shadow']);
            break;

        case 5:
            this.walls.shadows[0].push(shadow);
            if (isShadowIn) this.walls.shadows[4].push(shadowIn);
            break;
        }
    }
}

export { createShadows }
