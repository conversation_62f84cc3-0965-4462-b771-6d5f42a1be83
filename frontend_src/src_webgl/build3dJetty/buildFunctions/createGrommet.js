function createGrommet(position, color) {
    let grommet;
    if (color === 'ash_veneer' || color === 'oak_veneer') {
        grommet = this.elements.grommet_plug_veneer.clone();
    } else {
        grommet = this.elements.grommet_plug.clone();
    }

    grommet.position.setX(position.x1);
    grommet.position.setY(position.y1);
    grommet.position.setZ(position.z1);

    if (position.subtype === 'h') {
        grommet.position.setY(position.y1 + 9);
        grommet.rotation.x = -Math.PI / 2
    }

    const envMap = this.elements.cubemap;
    grommet.children[0].material.envMap = envMap;
    grommet.children[0].material.reflectivity = 0.5;

    grommet.name = 'grommet';
    this.scene.add(grommet);
    this.walls.cable_management.push(grommet);
}

export { createGrommet };
