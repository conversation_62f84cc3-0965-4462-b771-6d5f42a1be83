import { Group } from 'three';
import getRowByY from '../helpers/getTowByY';

const plankScaleMM = 18;

function createDoor(position, size, door_type, door_flip, door_parity, rowHeights, shelf_type, selected, innerOffset, handleYPos, m_config_id) {
    const row_number = getRowByY(position.y, rowHeights);
    const door_group = new Group();
    door_group.name = `door_group:${m_config_id}:${door_flip}`;

    if (shelf_type === 0) {
        const handling_overlap = 20;
        const handling_size = door_type === 0 ? 30 : 10;
        const handling_flip = door_flip === 1 ? 1 : -1;
        const offset = 0;
        const item_to_use = door_type === 0 ? this.elements.handle_big : this.elements.handle_small;

        door_group.position.setX(position.x - handling_flip * (size.x / 2));
        door_group.position.setY(position.y + size.y / 2);
        door_group.position.setZ(position.z);

        const door = this.elements.door.clone();
        door.name = 'DoorsType01';

        door.position.setX(position.x - handling_flip * handling_size / 2 - door_group.position.x);
        door.position.setY(0);
        door.position.setZ(0);

        let doorBox = this.boundingBox.setFromObject(this.elements.door);

        door.scale.setY((size.y - offset) / doorBox.getSize(this.target).y);
        door.scale.setX((size.x - handling_size - offset) / doorBox.getSize(this.target).x);
        door.scale.setZ(size.z / doorBox.getSize(this.target).z);
        door_group.add(door);

        this.walls.doors.push(door);

        const door_handler = item_to_use.clone();
        door_handler.name = 'HandlerType01';

        door_handler.position.setX(position.x + handling_flip * (size.x / 2 - handling_size) - door_group.position.x - door_flip * 2);

        if (handling_flip === 1) {
            door_handler.position.x -= offset * 2;
        } else {
            door_handler.position.x += offset * 2;
        }

        door_handler.position.setY(0);
        door_handler.position.setZ(0);
        doorBox = this.boundingBox.setFromObject(item_to_use);
        door_handler.scale.setY((size.y - offset) / doorBox.getSize(this.target).y);
        door_handler.scale.setX(
            (handling_size + handling_overlap) / doorBox.getSize(this.target).x,
        );
        door_handler.scale.setZ(1);
        door_handler.traverse(child => {
            if (child.type === 'Mesh' && typeof child.material !== 'undefined') { // child.material = self.magical_materials_for_rows["handlers"][row_number];
                child.material.needsUpdate = true;
                if (handling_flip > 0) {
                    child.rotation.y = child.rotation.y + 180 * Math.PI / 180;
                }
                child.geometry.center();
            }
        });
        door_group.add(door_handler);
        this.walls.doors.push(door_handler);

        // animation here
        door_group.rotate_sign = handling_flip;
    } else if (shelf_type === 2) {
        const handling_overlap = 20;
        const front_handling_size = 20;
        const handling_flip = door_flip === 1 ? 1 : -1;
        const handler_y_mult = handleYPos === 1 ? -1 : 1;
        const offset = 2;

        // Create door group

        door_group.position.setX(position.x - handling_flip * (size.x / 2));
        door_group.position.setY(position.y + size.y / 2);
        door_group.position.setZ(position.z);

        // Create door front
        let door;
        if (size.y >= 300) {
            door = this.elements.fdoors_big.clone();
        } else {
            door = this.elements.fdoors.clone();
        }
        const row_number = getRowByY(position.y, rowHeights);
        door.name = 'Drzwi';

        door.position.setX(
            position.x - door_group.position.x,
        );
        door.position.setY(handler_y_mult * -front_handling_size / 2);
        door.position.setZ(0);

        const doorBox = this.boundingBox.setFromObject(this.elements.door);

        door.scale.setY(((size.y - front_handling_size - offset) / doorBox.getSize(this.target).y) / 10);
        door.scale.setX(((size.x - offset) / doorBox.getSize(this.target).x) / 10);
        door.scale.setZ((size.z / doorBox.getSize(this.target).z) / 10);

        door_group.add(door);
        this.walls.doors.push(door);

        // Create door handle
        const door_handler = this.elements.fhandle.clone();
        door_handler.rotation.z = handler_y_mult * -Math.PI / 2;
        door_handler.scale.setX(0.1);
        door_handler.scale.setY(((size.x - offset) / doorBox.getSize(this.target).x));
        door_handler.scale.setZ(0.10);

        door_handler.position.setX(position.x - door_group.position.x);
        door_handler.position.setY(handler_y_mult * (size.y / 2 - front_handling_size * 2));


        door_group.add(door_handler);

        door_handler.name = 'fhandle';

        // animation here
        door_group.rotate_sign = handling_flip;
        this.door_lists[row_number].push(door_group);
        this.walls.door_groups.push(door_group);
        this.scene.add(door_group);
    } else {
        //  TYPE-02
        const door_handler_width = 130;
        const door_handler_height = 20;
        const handling_flip = door_flip === 1 ? 1 : -1;
        const handler_y_mult = handleYPos === 1 ? -1 : 1;
        const offset = innerOffset;


        door_group.position.setX(position.x - handling_flip * (size.x / 2));
        door_group.position.setY(position.y + size.y / 2);
        door_group.position.setZ(position.z);

        const door = this.elements.door.clone();
        door.name = 'DoorsType02';

        door.position.setX(
            position.x - handling_flip - door_group.position.x,
        );
        door.position.setY(0);
        door.position.setZ(0);

        let doorBox = this.boundingBox.setFromObject(this.elements.door);

        door.scale.setX((size.x - offset) / doorBox.getSize(this.target).x);
        door.scale.setY((size.y - offset) / doorBox.getSize(this.target).y);
        door.scale.setZ(size.z / doorBox.getSize(this.target).z);
        door_group.add(door);

        this.walls.doors.push(door);
        if (!(door_parity === 'double' && door_flip === 1)) {
            const door_handler = this.elements.handle_short_left.clone();
            door_handler.name = 'HandlerType02';
            door_handler.rotation.x = -Math.PI / 2;

            let posX = null;
            if (handling_flip === -1) {
                posX = position.x - size.x / 2 - door_group.position.x + door_handler_width / 2 + offset;
            } else if (handling_flip === 1) {
                posX = position.x + size.x / 2 - door_group.position.x - door_handler_width / 2 - offset;
            }
            door_handler.rotation.y = handler_y_mult === -1 ? -Math.PI : 0;
            door_handler.position.setX(posX);
            door_handler.position.setY(handler_y_mult * (size.y / 2 - door_handler_height / 2 + 2));
            door_handler.position.setZ(size.z - plankScaleMM / 2 - 1);

            doorBox = this.boundingBox.setFromObject(door_handler);

            door_handler.traverse(child => {
                if (child.type === 'Mesh' && typeof child.material !== 'undefined') {
                    child.material.needsUpdate = true;
                    child.geometry.center();
                }
            });

            door_group.add(door_handler);
            this.walls.doors.push(door_handler);

            const door_handler_shadow = this.elements.handle_short_left_shadow.clone();
            door_handler_shadow.name = 'HandlerType02Shadow';
            door_handler_shadow.rotation.x = -Math.PI / 2;
            door_handler_shadow.rotation.y = handler_y_mult === -1 ? -Math.PI : 0;
            door_handler_shadow.position.setX(posX);
            door_handler_shadow.position.setY(handler_y_mult * (size.y / 2 - door_handler_height / 2 - 2));
            door_handler_shadow.position.setZ(size.z - plankScaleMM / 2);

            doorBox = this.boundingBox.setFromObject(door_handler_shadow);

            door_handler_shadow.traverse(child => {
                if (child.type === 'Mesh' && typeof child.material !== 'undefined') {
                    child.material.needsUpdate = true;
                    child.geometry.center();
                }
            });
            door_group.add(door_handler_shadow);
            this.walls.doors.push(door_handler_shadow);

            // animation here
            door_group.rotate_sign = handling_flip;
        }
    }
    if (row_number >= 0) {
        this.door_lists[row_number].push(door_group);
    }

    this.walls.door_groups.push(door_group);
    this.scene.add(door_group);
}

export { createDoor };
