function createDeskBeam(newPoints) {
  const box = { 
    top: { x: newPoints.x2, y: newPoints.y2, z: newPoints.z2 },
    bottom: { x: newPoints.x1, y: newPoints.y1, z: newPoints.z1 },
  }

  const back = this.elements['top-bottom'].clone();
  const backBox = this.boundingBox.setFromObject(this.elements['top-bottom']);

  back.rotation.x = -Math.PI;

  const scaleY = Math.abs(box.bottom.y - box.top.y - 18) / backBox.getSize(this.target).x;
  const scaleX = Math.abs(box.bottom.x - box.top.x - 18) / backBox.getSize(this.target).x;

  back.position.setX((box.bottom.x + box.top.x) / 2);
  back.position.setY(box.bottom.y - 9);
  back.position.setZ(box.bottom.z);
  back.scale.setY(scaleY);
  back.scale.setX(scaleX);

  back.name = 'deskBeam';
  this.scene.add(back);
  if (this.walls.deskBeams === undefined) {
      this.walls.deskBeams = [];
  }
  this.walls.deskBeams.push(back);
}

export { createDeskBeam }
