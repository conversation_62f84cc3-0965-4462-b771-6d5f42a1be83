function createComponentHoverBox({ points, THREE }) {
    const PLANKSIZEMM = 18;
    const OVERLAP = 2; // geometry has exact points you need to use 1mm offset to avoid z-fights

    const width = points.x1 < points.x2 ? points.x2 - points.x1 + PLANKSIZEMM + OVERLAP : points.x1 - points.x2 + PLANKSIZEMM;
    const height = Math.abs(points.y1 - points.y2) + PLANKSIZEMM + OVERLAP * 2;
    const depth = Math.abs(points.z1 - points.z2) + OVERLAP * 2;

    const geometry = new THREE.BoxGeometry(width, height, depth);
    const material = new THREE.MeshBasicMaterial({
        color: 0xFF3C00,
        transparent: true,
        opacity: 0,
        depthWrite: false,
        // depthTest: false,
    });

    const box = new THREE.Mesh(geometry, material);
    box.name = `hoverBox:${points.m_config_id}`;
    box.renderOrder = 10000;
    box.position.set(
        (points.x1 + points.x2) / 2,
        (points.y1 + points.y2) / 2,
        (points.z1 + points.z2) / 2,
    );

    if (!this.scene) {
        this.scene = new THREE.Scene();
    }
    this.scene.add(box);

    box.no = this.componentHoverBoxes.length;
    this.componentHoverBoxes.push(box);
}

export { createComponentHoverBox }
