import consts from './consts'
function createHorizontalPlug(newPoints) {
    let dif;
    let scene = this.scene;
    let elements = this.elements;


    let panel = elements["horizontal-plug"].clone();
    panel.rotation.x = -Math.PI / 2;

    panel.position.setY(newPoints.y);
    panel.position.setX(newPoints.x);
    //panel.position.setZ();

    let panelBox = this.boundingBox.setFromObject(elements["horizontal-plug"]);
    panel.scale.setY(this.depth / panelBox.getSize(this.target).y);
    panel.scale.setZ(consts.horizontalSize / consts.plankScaleMM);


    panel.name = "additionalHorizontalPanel";
    this.scene.add(panel);
    this.walls.additionalHorizontalElements.push(panel);
}

export { createHorizontalPlug }
