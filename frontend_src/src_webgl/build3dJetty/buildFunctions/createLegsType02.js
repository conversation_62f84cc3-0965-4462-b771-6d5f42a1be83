function createLegsType02(position) {
    const leg = this.elements.leg_s_plus.clone();
    const legBox = this.boundingBox.setFromObject(this.elements.leg_s_plus);
    leg.position.setX(position.x1);
    leg.position.setY(position.y1);
    leg.position.setZ(position.z1);
    leg.scale.setY(Math.abs(position.y1 - position.y2) / legBox.getSize(this.target).y);
    leg.rotation.y = (-position.rotation_z - 45) * Math.PI / 180;
    leg.name = 'leg';
    this.scene.add(leg);
    this.walls.legs.push(leg);
}

export { createLegsType02 }
