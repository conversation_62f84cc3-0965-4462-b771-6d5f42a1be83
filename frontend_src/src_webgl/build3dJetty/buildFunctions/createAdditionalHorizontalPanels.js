function createAdditionalHorizontalPanels(newPoints) {

    let panel = this.elements["horizontal-plug"].clone();
    panel.rotation.x = -Math.PI / 2;

    panel.position.setY(newPoints.y);
    panel.position.setX(newPoints.x);
    //panel.position.setZ();
    let panelBox = this.boundingBox.setFromObject(this.elements["horizontal-plug"]);

    panel.scale.setY(this.depth / panelBox.getSize(this.target).y);
    panel.name = "additionalHorizontalPanel";

    this.scene.add(panel);
    this.walls.additionalHorizontalElements.push(panel);
}

export { createAdditionalHorizontalPanels }