function createBack(index, oldPoints, newPoints) {
    let dif;

    if (typeof oldPoints !== 'undefined') {
        dif = this.compare_dnas(oldPoints, newPoints);
    } else {
        dif = {
            newitem: true,
            scaled: true,
            only_moved: true,
        };
    }
    if (dif.same === true) {
        return true;
    }

    let back;
    if (dif.newitem) {
        back = this.elements['top-bottom'].clone();
    } else {
        back = this.walls.backs[index];
    }
    const backBox = this.boundingBox.setFromObject(this.elements['top-bottom']);

    back.rotation.x = -Math.PI;

    const scaleY = Math.abs(newPoints.bottom.y - newPoints.top.y - 18) / backBox.getSize(this.target).x;
    const scaleX = Math.abs(newPoints.bottom.x - newPoints.top.x - 18) / backBox.getSize(this.target).x;

    back.position.setX((newPoints.bottom.x + newPoints.top.x) / 2);
    back.position.setY(newPoints.bottom.y - 9);
    back.position.setZ(newPoints.bottom.z);
    back.scale.setY(scaleY);
    back.scale.setX(scaleX);

    if (dif.newitem) {
        back.name = 'backs';
        this.scene.add(back);
        if (this.walls.backs === undefined) {
            this.walls.backs = [];
        }
        this.walls.backs.push(back);
    }
}

export { createBack }
