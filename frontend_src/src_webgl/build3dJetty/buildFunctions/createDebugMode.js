import * as THREE from 'three'

function createPlane(color) {
    const geometry = new THREE.PlaneGeometry(100, 100, 32);
    const material = new THREE.MeshBasicMaterial({ color: color, side: THREE.DoubleSide });
    return new THREE.Mesh( geometry, material );
}

function createDebugMode(width, height, depth) {

    const bottomPlane = createPlane( 0xffff00);
    const bottomPlane2 = createPlane( 0x00ff00);
    const topPlane = createPlane( 0xffff00);
    const topPlane2 = createPlane( 0x00ff00);

    const leftPlane = createPlane( 0xffff00);
    const rightPlane = createPlane( 0xffff00);

    bottomPlane.position.setX(0);
    bottomPlane.position.setY(0);
    bottomPlane.position.setZ(depth / 2);
    bottomPlane.scale.setY(depth / 100);
    bottomPlane.scale.setX(width / 100);
    bottomPlane.name = 'bottomPlane';
    bottomPlane.rotation.x = -Math.PI / 2;
    this.scene.add(bottomPlane);

    bottomPlane2.position.setX(0);
    bottomPlane2.position.setY(18);
    bottomPlane2.position.setZ(depth / 2);
    bottomPlane2.scale.setY(depth / 100);
    bottomPlane2.scale.setX(width / 100);
    bottomPlane2.name = 'bottomPlane2';
    bottomPlane2.rotation.x = -Math.PI / 2;
    this.scene.add(bottomPlane2);

    topPlane.position.setX(0);
    topPlane.position.setY(height);~
    topPlane.position.setZ(depth / 2);
    topPlane.scale.setY(depth / 100);
    topPlane.scale.setX(width / 100);
    topPlane.name = 'bottomPlane';
    topPlane.rotation.x = -Math.PI / 2;
    this.scene.add(topPlane);

    topPlane2.position.setX(0);
    topPlane2.position.setY(height + 18);
    topPlane2.position.setZ(depth / 2);
    topPlane2.scale.setY(depth / 100);
    topPlane2.scale.setX(width / 100);
    topPlane2.name = 'bottomPlane';
    topPlane2.rotation.x = -Math.PI / 2;
    this.scene.add(topPlane2);

}

export { createDebugMode }