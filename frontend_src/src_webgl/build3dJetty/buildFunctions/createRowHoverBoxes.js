import * as THREE from 'three';

const PLANKSIZEMM = 18;
const OVERLAP = 2; // geometry has exact box you need to use 1mm offset to avoid z-fights
const storageHoverBoxHeight = 300;


export class CreateRowHoverBoxes {
    constructor(scene, materialConfig, boxes) {
        this.scene = scene;
        this.boxes = boxes;
        this.materialConfig = materialConfig;
        this.topStorageHoverBox = null;
        this.bottomStorageHoverBox = null;
    }

    createBoxes(box, rowType) {
        const width = box.x1 < box.x2 ? box.x2 - box.x1 + PLANKSIZEMM + OVERLAP : box.x1 - box.x2 + PLANKSIZEMM;
        // const height = Math.abs(box.y1 - box.y2) + PLANKSIZEMM + OVERLAP * 2;
        const height = Math.abs(box.y2) - Math.abs(box.y1);
        const depth = Math.abs(box.z1 - box.z2) + OVERLAP * 2;
        this.maskWidth = 200;


        const geometry = new THREE.BoxGeometry(width, height, depth);
        const material = new THREE.MeshBasicMaterial(this.materialConfig);
        const hoverBox = new THREE.Mesh(geometry, material);
        hoverBox.name = `rowHoverBox:${box.boxIndex}`;
        hoverBox.renderOrder = 10000;
        hoverBox.position.set(
            (box.x1 + box.x2) / 2,
            (box.y1 + box.y2) / 2,
            (box.z1 + box.z2) / 2,
        );

        const maskGeom = new THREE.BoxGeometry(this.maskWidth, height, depth);
        const maskMaterial = new THREE.MeshBasicMaterial({ ...this.materialConfig });
        const maskHoverBox = new THREE.Mesh(maskGeom, maskMaterial);
        maskHoverBox.name = `mask_rowHoverBox:${box.boxIndex}`;
        maskHoverBox.position.set(
            (box.x2 + (box.x2 + this.maskWidth)) / 2,
            (box.y1 + ((box.y2 - box.y1) / 2)),
            (box.z1 + box.z2) / 2,
        );

        const boxes = { hoverBox, maskHoverBox };
        if (rowType !== 'middle') {
            const positionSet = first => ({
                x: (box.x1 + box.x2) / 2,
                y: first ? box.y1 - (storageHoverBoxHeight / 2) : box.y2 + (storageHoverBoxHeight / 2),
                z: (box.z1 + box.z2) / 2,
            });
            const storageMask = new THREE.BoxGeometry(width, storageHoverBoxHeight, depth);
            const storageMaskMaterial = new THREE.MeshBasicMaterial({ ...this.materialConfig });
            const storageMaskHoverBox = new THREE.Mesh(storageMask, storageMaskMaterial);
            if (rowType === 'first' || rowType === 'last') {
                storageMaskHoverBox.name = `storageMask_rowHoverBox:${rowType}`;
                const coords = positionSet(rowType === 'first');
                storageMaskHoverBox.position.set(coords.x, coords.y, coords.z);

                if (rowType === 'first') {
                    this.bottomStorageHoverBox = storageMaskHoverBox;
                } else {
                    this.topStorageHoverBox = storageMaskHoverBox;
                }
                this.scene.add(storageMaskHoverBox);
            } else if (rowType === 'first_and_last') {
                const storageMaskHoverBoxLast = storageMaskHoverBox.clone();
                storageMaskHoverBox.name = 'storageMask_rowHoverBox:first';
                storageMaskHoverBoxLast.name = 'storageMask_rowHoverBox:last';

                const coordsFirst = positionSet(true);
                const coordsLast = positionSet(false);

                storageMaskHoverBox.position.set(coordsFirst.x, coordsFirst.y, coordsFirst.z);
                storageMaskHoverBoxLast.position.set(coordsLast.x, coordsLast.y, coordsLast.z);
                this.bottomStorageHoverBox = storageMaskHoverBox;
                this.topStorageHoverBox = storageMaskHoverBoxLast;
                this.scene.add(storageMaskHoverBox);
                this.scene.add(storageMaskHoverBoxLast);
            }
        }
        this.boxes.push(boxes);
        if (!this.scene) {
            this.scene = new THREE.Scene();
        }
        this.scene.add(hoverBox);
        this.scene.add(maskHoverBox);
    }

    regenerate(geom, width, depth, store) {
        this.removeBoxes();
        this.width = width;
        this.depth = depth;
        geom.forEach(((box, index) => {
            let rowType = 'middle';
            if (index === 0 && index === geom.length - 1) {
                rowType = 'first_and_last';
            } else if (index === 0) {
                rowType = 'first';
            } else if (index === geom.length - 1) {
                rowType = 'last';
            }
            this.createBoxes({ ...box, boxIndex: index }, this.shouldRenderStorageHoverBoxes(rowType, store));
        }));
    }

    shouldRenderStorageHoverBoxes(rowType, store) {
        const { ui: { isMobile }, configuration: { topStorage, bottomStorage } } = store;
        return (isMobile || (rowType === 'last' && topStorage) || (rowType === 'first' && bottomStorage))
            ? 'middle'
            : rowType;
    }

    updateWidth(oldWidth, width) {
        if (oldWidth !== width) {
            const scaleX = width / this.width;
            const maskPosition = ((width / 2) + ((width / 2) + this.maskWidth)) / 2;
            this.boxes.forEach(({ hoverBox, maskHoverBox }) => {
                hoverBox.scale.setX(scaleX);
                maskHoverBox.position.x = maskPosition;
            });
            if (this.topStorageHoverBox) {
                this.topStorageHoverBox.scale.setX(scaleX);
            }
            if (this.bottomStorageHoverBox) {
                this.bottomStorageHoverBox.scale.setX(scaleX);
            }
        }
    }

    updateDepth(oleDepth, depth) {
        if (oleDepth !== depth) {
            const scaleZ = depth / this.depth;
            const positionZ = depth / 2;
            this.boxes.forEach(({ hoverBox, maskHoverBox }) => {
                hoverBox.scale.setZ(scaleZ);
                hoverBox.position.z = (positionZ);
                maskHoverBox.scale.setZ(scaleZ);
                maskHoverBox.position.z = (positionZ);
            });
            if (this.topStorageHoverBox) {
                this.topStorageHoverBox.scale.setZ(scaleZ);
                this.topStorageHoverBox.position.z = (positionZ);
            }
            if (this.bottomStorageHoverBox) {
                this.bottomStorageHoverBox.scale.setZ(scaleZ);
                this.bottomStorageHoverBox.position.z = (positionZ);
            }
        }
    }

    removeBoxes() {
        this.boxes.forEach(({ hoverBox, maskHoverBox }) => {
            this.scene.remove(hoverBox);
            this.scene.remove(maskHoverBox);
        });
        this.scene.remove(this.topStorageHoverBox);
        this.scene.remove(this.bottomStorageHoverBox);
        this.topStorageHoverBox = null;
        this.bottomStorageHoverBox = null;
        this.boxes = [];
    }
}
