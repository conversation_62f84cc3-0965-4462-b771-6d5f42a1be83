import * as THREE from 'three';

let shadowCenter;
let shadowLeft;
let shadowRight;
let wallShadow;

let shadowLegLeft;
let shadowLegRight;
let shadowLegMiddle = [];

function createCastShadows({ width, points, rowConfigurator }) {
    if (rowConfigurator) {
        buildFeetCastShadow.call(this, width);
    } else if (points.cast_shadows.long_legs_shadow) {
        removeShadows.call(this);
        buildLegsCastShadow.call(this, { ...points.cast_shadows.long_legs_shadow, width });
    } else if (points.cast_shadows.plinth_shadow) {
        removeShadows.call(this);
        buildPlinthCastShadow.call(this, { ...points.cast_shadows.plinth_shadow, width });
    } else {
        removeShadows.call(this);
        buildFeetCastShadow.call(this, width);
    }
}

function removeShadows() {
    this.walls.castShadows.feetShadow.forEach(item => this.scene.remove(item));
    this.walls.castShadows.feetShadow.length = 0;
    this.walls.castShadows.legShadow.forEach(item => this.scene.remove(item));
    this.walls.castShadows.legShadow.length = 0;
    this.walls.castShadows.plinthShadow.forEach(item => this.scene.remove(item));
    this.walls.castShadows.plinthShadow.length = 0;
}

function buildWallCastShadow({ width }) {
    wallShadow = this.elements.shadow_wall.clone();
    wallShadow.name = 'wallCastShadow';
    wallShadow.traverse(child => {
        if (child instanceof THREE.Mesh) {
            child.material.map = this.elements.cast_shadow_wall;
            child.material.transparent = true;
            child.material.depthWrite = false;
            child.material.opacity = 0.2;
        }
    });
    wallShadow.scale.setX((width + 30) / 100);
    wallShadow.scale.setY(2);

    this.walls.castShadows.legShadow.push(wallShadow);
    this.scene.add(wallShadow);
}

function buildPlinthCastShadow({
    width, shadow_plinth_L, shadow_plinth_M, shadow_plinth_R, shadow_plinth_M_bar,
}) {
    buildWallCastShadow.call(this, { width });

    const ShadowVariant = {
        WIDE: 'wide',
        MEDIUM: 'medium',
        NARROW: 'narrow',
    };
    
    const variant = (shadow_plinth_L.length > 0) ? shadow_plinth_L[0].type : ShadowVariant.WIDE;

    const shadowLeftPlinth = (variant === ShadowVariant.WIDE) ? 
        this.elements.shadow_plinth_l.clone() : (variant === ShadowVariant.MEDIUM) ? 
        this.elements.shadow_plinth_l_medium.clone() :
        this.elements.shadow_plinth_l_narrow.clone();    
    shadowLeftPlinth.name = 'castShadowLeftPlinth';

    const shadowRightPlinth = (variant === ShadowVariant.WIDE) ? 
        this.elements.shadow_plinth_r.clone() : (variant === ShadowVariant.MEDIUM) ? 
        this.elements.shadow_plinth_r_medium.clone() :
        this.elements.shadow_plinth_r_narrow.clone();    
    shadowRightPlinth.name = 'castShadowRightPlinth';

    const shadowMiddlePlinth = this.elements.shadow_plinth_m.clone();
    shadowMiddlePlinth.name = 'castShadowMiddlePlinth';
    const shadowMiddleBarPlinth = this.elements.shadow_plinth_m_bar.clone();
    shadowMiddleBarPlinth.name = 'castShadowMiddleBarPlinth';

    const shadowLeftEndPlinth = this.elements.shadow_plinth_l_medium_end.clone();
    shadowLeftEndPlinth.name = 'castShadowLeftEndPlinth';

    const shadowRightEndPlinth = this.elements.shadow_plinth_r_medium_end.clone();
    shadowRightEndPlinth.name = 'castShadowRightEndPlinth';


    [shadowLeftPlinth, shadowRightPlinth, shadowMiddlePlinth, shadowMiddleBarPlinth, shadowLeftEndPlinth, shadowRightEndPlinth]
        .forEach(item => item.traverse(child => {
            if (child instanceof THREE.Mesh) {
                child.material.map = this.elements.cast_shadow_plinth;
                child.material.transparent = true;
                child.material.depthWrite = false;
                child.material.opacity = 0.4;
            }
        }));

    const bottomPosition = 3;
    
    const scaleZ = (shadow_plinth_L.length > 0) ? (shadow_plinth_L[0].z2 + shadow_plinth_L[0].z1) / 100 : 0;

    if (shadow_plinth_L.length) {
        shadowLeftPlinth.position.setX((shadow_plinth_L[0].x1 + shadow_plinth_L[0].x2) / 2);
        shadowLeftPlinth.position.setY(bottomPosition);
        shadowLeftPlinth.position.setZ(shadow_plinth_L[0].scale.z / 2);
        shadowLeftPlinth.scale.setZ(scaleZ);
        this.scene.add(shadowLeftPlinth);
        this.walls.castShadows.legShadow.push(shadowLeftPlinth);

        if (variant === ShadowVariant.MEDIUM) {
            shadowLeftEndPlinth.position.setX(shadow_plinth_L[0].x1);
            shadowLeftEndPlinth.position.setY(bottomPosition);
            shadowLeftEndPlinth.position.setZ(shadow_plinth_L[0].scale.z / 2);
            shadowLeftEndPlinth.scale.setX(shadow_plinth_L[0].inset / 100);
            shadowLeftEndPlinth.scale.setZ(scaleZ);
            this.scene.add(shadowLeftEndPlinth);
            this.walls.castShadows.legShadow.push(shadowLeftEndPlinth);
        }
    }

    if (shadow_plinth_R.length) {
        shadowRightPlinth.position.setX((shadow_plinth_R[0].x1 + shadow_plinth_R[0].x2) / 2);
        shadowRightPlinth.position.setY(bottomPosition);
        shadowRightPlinth.position.setZ(shadow_plinth_R[0].scale.z / 2);
        shadowRightPlinth.scale.setZ(scaleZ);
        this.scene.add(shadowRightPlinth);
        this.walls.castShadows.legShadow.push(shadowRightPlinth);

        if (variant === ShadowVariant.MEDIUM) {
            shadowRightEndPlinth.position.setX(shadow_plinth_R[0].x2);
            shadowRightEndPlinth.position.setY(bottomPosition);
            shadowRightEndPlinth.position.setZ(shadow_plinth_R[0].scale.z / 2);
            shadowRightEndPlinth.scale.setX(shadow_plinth_R[0].inset / 100);
            shadowRightEndPlinth.scale.setZ(scaleZ);
            this.scene.add(shadowRightEndPlinth);
            this.walls.castShadows.legShadow.push(shadowRightEndPlinth);
        }        
    }

    shadow_plinth_M.forEach(item => {
        const clone = this.elements.shadow_plinth_m.clone();
        clone.position.setX((item.x1 + item.x2) / 2);
        clone.position.setY(bottomPosition);
        clone.position.setZ(item.scale.z / 2);
        clone.scale.setZ(scaleZ);
        this.scene.add(clone);
        this.walls.castShadows.legShadow.push(clone);
    });

    if (scaleZ > 0) {
        shadow_plinth_M_bar.forEach(item => {
            const clone = this.elements.shadow_plinth_m_bar.clone();
            clone.position.setX((item.x1 + item.x2) / 2);
            clone.position.setY(bottomPosition);
            clone.position.setZ(item.scale.z / 2);
            clone.scale.setX((item.scale.x) / 100);
            clone.scale.setZ(scaleZ);
            this.scene.add(clone);
            this.walls.castShadows.legShadow.push(clone);
        });
    }
}

function buildLegsCastShadow({
    modifier, shadow_leg_L, shadow_leg_R, shadow_between_legs, shadow_leg_M, width,
}) {
    shadowLegLeft = null;
    shadowLegRight = null;
    shadowLegMiddle = [];
    shadowLegLeft = this.elements['cast-shadow-leg-left'].clone();
    shadowLegRight = this.elements['cast-shadow-leg-right'].clone();
    buildWallCastShadow.call(this, { width });

    shadowLegLeft.scale.setZ(modifier);
    shadowLegRight.scale.setZ(modifier);

    shadowLegLeft.position.setX(shadow_leg_L.geom.x);
    shadowLegLeft.position.setY(shadow_leg_L.geom.y);
    shadowLegLeft.position.setZ(shadow_leg_L.geom.z);

    shadowLegRight.position.setX(shadow_leg_R.geom.x);
    shadowLegRight.position.setY(shadow_leg_R.geom.y);
    shadowLegRight.position.setZ(shadow_leg_R.geom.z);
    shadowLegLeft.name = 'shadow_leg_L';
    shadowLegRight.name = 'shadow_leg_R';
    [shadowLegLeft, shadowLegRight].forEach(item => item.traverse(child => {
        if (child instanceof THREE.Mesh) {
            child.material.map = this.elements.cast_shadow_legs;
            child.material.transparent = true;
        }
    }));
    this.scene.add(shadowLegLeft);
    this.scene.add(shadowLegRight);
    this.walls.castShadows.legShadow.push(shadowLegLeft);
    this.walls.castShadows.legShadow.push(shadowLegRight);

    if (shadow_leg_M.length) {
        shadow_leg_M.forEach((shadowM, index) => {
            const clone = this.elements['cast-shadow-leg-middle'].clone();
            clone.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    child.material.map = this.elements.cast_shadow_legs;
                    child.material.transparent = true;
                }
            });
            clone.scale.setZ(modifier);
            clone.position.setX(shadowM.geom.x);
            clone.position.setY(shadowM.geom.y);
            clone.position.setZ(shadowM.geom.z);
            clone.name = `cast-shadow-leg-middle-${index}`;
            this.scene.add(clone);
            this.walls.castShadows.legShadow.push(clone);
        });
    }
    if (shadow_between_legs.length) {
        shadow_between_legs.forEach((shadowB, index) => {
            const clone = this.elements['cast-shadow-leg-between'].clone();
            clone.traverse(child => {
                if (child instanceof THREE.Mesh) {
                    child.material.map = this.elements.cast_shadow_legs;
                    child.material.transparent = true;
                }
            });
            clone.scale.setZ(modifier);
            clone.scale.setX(shadowB.geom.scaleX);
            clone.position.setX(shadowB.geom.x);
            clone.position.setY(shadowB.geom.y);
            clone.position.setZ(shadowB.geom.z);
            clone.name = `cast-shadow-leg-between-${index}`;
            this.scene.add(clone);
            this.walls.castShadows.legShadow.push(clone);
        });
    }
}

function buildFeetCastShadow(width) {
    const { elements } = this;
    const { scene } = this;
    if (width === undefined) {
        width = this.width;
    }
    shadowCenter = elements['cast-shadow-center'].clone();
    shadowCenter.name = 'castShadowCenter';
    shadowRight = elements['cast-shadow-right'].clone();
    shadowRight.name = 'castShadowRight';
    shadowLeft = elements['cast-shadow-left'].clone();
    shadowLeft.name = 'castShadowLeft';

    for (let i = 0; i < this.walls.castShadows.feetShadow.length; i++) {
        scene.remove(this.walls.castShadows.feetShadow[i]);
    }

    this.walls.castShadows.feetShadow.forEach(i => this.scene.remove(i));
    this.walls.castShadows.feetShadow.length = 0;

    const positionBottom = 1;
    const positionZ = this.depth / 2;
    let shadowBox = this.boundingBox.setFromObject(elements['cast-shadow-center']);
    const scaleZ = this.depth * 2 / shadowBox.getSize(this.target).z;
    shadowCenter.scale.setX(width / shadowBox.getSize(this.target).x);
    shadowCenter.scale.setZ(scaleZ);
    shadowCenter.position.setY(positionBottom);
    shadowCenter.position.setZ(positionZ);
    this.walls.castShadows.feetShadow.push(shadowCenter);

    shadowCenter.renderOrder = 10000;

    shadowBox = this.boundingBox.setFromObject(elements['cast-shadow-right']);
    const scaleX = this.depth / 2 / shadowBox.getSize(this.target).x;

    shadowRight.scale.setX(scaleX);
    shadowRight.scale.setZ(scaleZ);
    shadowRight.position.setX(-width / 2 + this.depth / 2 / 2);
    shadowRight.position.setY(positionBottom);
    shadowRight.position.setZ(positionZ);
    this.walls.castShadows.feetShadow.push(shadowRight);

    shadowLeft.scale.setX(scaleX);
    shadowLeft.scale.setZ(scaleZ);
    shadowLeft.position.setX(width / 2 - this.depth / 2 / 2);
    shadowLeft.position.setY(positionBottom);
    shadowLeft.position.setZ(positionZ);
    this.walls.castShadows.feetShadow.push(shadowLeft);

    this.scene.add(shadowCenter);
    this.scene.add(shadowLeft);
    this.scene.add(shadowRight);
}

export { createCastShadows };
