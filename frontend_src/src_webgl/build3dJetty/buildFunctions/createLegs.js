function createLegs(position) {
    const leg = this.elements.leg.clone();
    // const legBox = this.boundingBox.setFromObject(this.elements.leg);
    leg.position.setX(position.x1);
    leg.position.setZ(position.z1);
    leg.position.setY((position.y1 + position.y2) / 2 + Math.abs(position.y1 - position.y2) / 2);

    if (position.width) leg.scale.setX(position.width);
    if (position.depth) leg.scale.setZ(position.depth);

    leg.rotation.x = leg.rotation.x + 90 * Math.PI / 180;
    leg.name = 'leg';

    this.scene.add(leg);
    this.walls.legs.push(leg);
}

export { createLegs }
