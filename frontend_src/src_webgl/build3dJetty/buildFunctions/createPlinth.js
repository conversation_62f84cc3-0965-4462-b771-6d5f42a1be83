import { Vector3 } from 'three'

function createPlinth(newPoints) {
    const plankScaleMM = 18;
    const { subtype } = newPoints.raw;
    let plinth;
    let scaleX;
    let scaleY;
    let scaleZ;
    let posX;
    let posY;
    let posZ;

    if (['z', 'f', 'b'].includes(subtype)) {
        if (subtype === 'b') {
            plinth = (Math.min(newPoints.bottom.x, newPoints.top.x) + 9 < 0) ? 
                this.elements.plinth_l_narrow.clone() : this.elements.plinth_r_narrow.clone()
        }
        else {
            plinth = this.elements.plinth_m.clone();
        }        

        scaleZ = this.depth / 100;
        scaleX = 1;
        scaleY = 1;
        posX = Math.min(newPoints.bottom.x, newPoints.top.x) + 9;
        posY = Math.abs(newPoints.bottom.y - newPoints.top.y) + plankScaleMM / 2;
        posZ = Math.abs(newPoints.bottom.z + newPoints.top.z) / 2;

    } else {
        plinth = this.elements.plinth_mbar.clone();
        scaleZ = 1;
        scaleX = newPoints.raw.width / 100;
        scaleY = 1;
        posX = (newPoints.bottom.x + newPoints.top.x) / 2;
        posY = Math.abs(newPoints.bottom.y - newPoints.top.y) + plankScaleMM / 2;
        posZ = Math.abs(newPoints.bottom.z + newPoints.top.z) / 2;
    }

    plinth.scale.setZ(scaleZ);
    plinth.scale.setX(scaleX);
    plinth.scale.setY(scaleY);

    plinth.position.set(posX, posY, posZ);

    plinth.name = `plinth:${subtype}`;
    this.scene.add(plinth);
    if (this.walls.boxes === undefined) {
        this.walls.boxes = [];
    }
    this.walls.boxes.push(plinth);
}

export { createPlinth };
