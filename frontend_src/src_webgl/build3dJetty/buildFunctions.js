import * as THREE from 'three';

import { createBack } from './buildFunctions/createBack';
import { createSupport } from './buildFunctions/createSupport';
import { createLegs } from './buildFunctions/createLegs';
import { createLegsType02 } from './buildFunctions/createLegsType02';
import { createDrawers } from './buildFunctions/createDrawers';
import { createVerticalPanels } from './buildFunctions/createVerticalPanels';
import { createAdditionalHorizontalPanels } from './buildFunctions/createAdditionalHorizontalPanels';
import { createHorizontalPanels } from './buildFunctions/createHorizontalPanels';
import { createDoor } from './buildFunctions/createDoor';
import { createComponentHoverBox } from './buildFunctions/createComponentHoverBox';
import { CreateRowHoverBoxes } from './buildFunctions/createRowHoverBoxes';
import { createInsert } from './buildFunctions/createInsert';
import { createFacePlaneForRaycasting } from './buildFunctions/createFacePlaneForRaycasting';
import { createPlinth } from './buildFunctions/createPlinth';
import { createVertical } from './buildFunctions/createVertical';
import { createHorizontal } from './buildFunctions/createHorizontal';
import { createHorizontalPlug } from './buildFunctions/createHorizontalPlug';
import { createShadows } from './buildFunctions/createShadows';
import { createCastShadows } from './buildFunctions/createCastShadows';
import { createButton } from './buildFunctions/createButton';
import { createComponentBoundigbox } from './buildFunctions/createComponentBoundigbox';
import { createGrommet } from './buildFunctions/createGrommet';
import { createDebugMode } from './buildFunctions/createDebugMode';
import { createDeskBeam } from './buildFunctions/createDeskBeam';


export class BuildFunctions {
    constructor() {
        this.plankScaleMM = 18;
        this.MAX_DOOR_OPEN = 60;
        this.target = new THREE.Vector3();
        this.boundingBox = new THREE.Box3();

        // methods
        this.createBack = createBack;
        this.createSupport = createSupport;
        this.createLegs = createLegs;
        this.createLegsType02 = createLegsType02;
        this.createDrawers = createDrawers;
        this.createVerticalPanels = createVerticalPanels;
        this.createAdditionalHorizontalPanels = createAdditionalHorizontalPanels;
        this.createHorizontalPanels = createHorizontalPanels;
        this.createDoor = createDoor;
        this.createComponentHoverBox = createComponentHoverBox;
        this.createInsert = createInsert;
        this.createFacePlaneForRaycasting = createFacePlaneForRaycasting;
        this.createPlinth = createPlinth;
        this.createVertical = createVertical;
        this.createHorizontal = createHorizontal;
        this.createHorizontalPlug = createHorizontalPlug;
        this.createShadows = createShadows;
        this.createCastShadows = createCastShadows;
        this.createButton = createButton;
        this.createGrommet = createGrommet;
        this.createDebugMode = createDebugMode;
        this.createComponentBoundigbox = createComponentBoundigbox;
        this.createDeskBeam = createDeskBeam;
        this.CreateRowHoverBoxes = CreateRowHoverBoxes;
    }
}
