/* eslint-disable no-param-reassign */
import * as THREE from 'three';
import _ from 'lodash';


import { getDefinitionForMaterial } from '../presets/materials';
import { BuildFunctions } from './buildFunctions';
import getTotalCapacity from './helpers/getTotalCapacity';

let shadowCastEnabled = true;
let _points;
let reflectionCube;

// here it should use build functions - later
class Build3d extends BuildFunctions {
    constructor({
        elements_in,
        context_in,
        isMobile,
        isOffscreen = false,
        initialShelfType,
        initialShelfMaterial,
    }, rowConfigurator = false) {
        super();

        this.shelfType = initialShelfType;
        this.shelfMaterial = initialShelfMaterial;

        [
            ,
            this.color,
            ,
            this.hex_handler_color,
            this.backs_color,
            this.reflectivityValue,
            ,
        ] = getDefinitionForMaterial(this.shelfMaterial, this.shelfType);

        this.rowConfigurator = rowConfigurator;
        this.context = context_in;
        this.designer_mode = 0; // 0 -disabled, 1 - render everything, 2 - do not render fronts, 3 - drawers and doors open
        this.elements = elements_in;
        window.elements_in = elements_in;
        this.scene = null;
        this.isMobile = isMobile;
        this.backpanel_rows = null;
        this.wallShadowsObjects = [];
        this.row_a = 200;
        this.row_b = 300;
        this.row_c = 400;
        this.isOffscreen = isOffscreen;
        this.componentHoverBoxes = [];

        this.walls = {
            verticals: [],
            horizontals: [],
            supports: [],
            shadows: [[], [], [], [], []],
            castShadows: {
                feetShadow: [],
                legShadow: [],
                plinthShadow: [],
            },
            legs: [],
            doors: [],
            door_groups: [],
            backs: [],
            boxes: [],
            drawers: [],
            topBottomWalls: [],
            leftRightWalls: [],
            additionalHorizontalElements: [],
            wallCompartmentShadow: [],
            plinth: [],
            deskBeams: [],
            componentBoundingBoxes: [],
            cable_management: [],
            hoverBoxes: [],
            cloudIndicators: [],
        };

        this.points = {};
        this.handlers = [];
        this.typek = [];
        this.depth = 320;
        this.depth_previous = 320;
        this.depth_changed = false;
        this.width = 1200;
        this.row_styles_presets = -1;
        this.empty_row_styles = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];

        let boxGeometry = new THREE.BoxGeometry(1, 1, 1);
        let boxMaterial = new THREE.MeshBasicMaterial({
            color: 0xb6b6b7,
            wireframe: false,
        });
        this.elements.backs = new THREE.Mesh(boxGeometry, boxMaterial);
        this.elements.backs.renderOrder = 38;

        // box POF
        boxGeometry = new THREE.BoxGeometry(1, 1, 1);
        boxMaterial = new THREE.MeshBasicMaterial({
            color: 0x67717f,
            wireframe: false,
        });


        this.elements.storage_box = new THREE.Mesh(boxGeometry, boxMaterial);
        // spots POF
        const spotGeometry = new THREE.BoxGeometry(1, 1, 1);
        const spotMaterial = new THREE.MeshBasicMaterial({
            color: 0x550088,
            wireframe: false,
        });
        this.elements.drawers = new THREE.Mesh(spotGeometry, spotMaterial);
    }

    init3d() {
        const { elements } = this;

        reflectionCube = elements.cubemap;
        let shadows = ['shadow', 'shadow-left', 'shadow-right', 'shadow-bottom'];
        for (let i = 0; i < shadows.length; i++) {
            elements[shadows[i]].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.transparent = true;
                    child.material.opacity = 1;
                    child.material.side = THREE.FrontSide;
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = this.reflectivityValue;
                }
            });
            elements[shadows[i]].renderOrder = 50;
        }

        shadows = [
            'cast-shadow-right',
            'cast-shadow-left',
            'cast-shadow-center',
        ];
        // TODO emporary off for desk tests
        for (let i = 0; i < shadows.length; i++) {
            elements[shadows[i]].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.transparent = true;
                    child.material.opacity = 0;
                }
            });
            elements[shadows[i]].renderOrder = 15;
        }

        elements.vertical.traverse(child => {
            if (child.type === 'Mesh') {
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
            }
        });

        elements.support.traverse(child => {
            if (child.type === 'Mesh') {
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
            }
        });

        // elements['insert'].traverse(function (child) {
        //     if (child.type === 'Mesh') {
        //         child.material.envMap = reflectionCube;
        //         child.material.reflectivity = this.reflectivityValue;
        //     }
        // });

        elements.support.renderOrder = 22;

        elements['support-drawer'].traverse(child => {
            if (child.type === 'Mesh') {
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
            }
        });
        elements['support-drawer'].renderOrder = 21;

        elements.vertical.renderOrder = 2;
        elements.horizontal.traverse(child => {
            if (child.type === 'Mesh') {
                child.material.polygonOffset = true;
                child.material.polygonOffsetFactor = 1.0;
                child.material.polygonOffsetUnits = -1.0;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
            }
        });
        elements.horizontal.renderOrder = 20;

        elements.handle_big.traverse(child => {
            if (child.type === 'Mesh') {
                child.rotation.x = -Math.PI / 2;
                child.material.map = elements.doors_open;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
                child.material.color = new THREE.Color(this.hex_handler_color);
            }
        });
        elements.handle_big.renderOrder = 40;

        elements.handle_drawer.traverse(child => {
            if (child.type === 'Mesh') {
                // child.rotation.x = -Math.PI / 2;
                child.material.map = elements.doors_open;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
                child.material.color = new THREE.Color(this.hex_handler_color);
            }
        });
        elements.handle_drawer.renderOrder = 40;

        elements.backs.traverse(child => {
            if (child.type === 'Mesh') {
                child.material.color = new THREE.Color(this.backs_color);
                child.material.reflectivity = this.reflectivityValue;
            }
        });
        elements.backs.renderOrder = 18;


        elements.handle_small.traverse(child => {
            if (child.type === 'Mesh') {
                child.rotation.x = -Math.PI / 2;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
                child.material.map = elements.doors;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
                child.material.color = new THREE.Color(this.hex_handler_color);
            }
        });
        elements.handle_small.renderOrder = 60;


        elements.handle_short_left_shadow.traverse(child => {
            if (child.type === 'Mesh') {
                child.material.map = elements.handle_short_left_texture;
                child.material.transparent = true;
                child.material.depthTest = true;
                child.material.depthWrite = false;
                child.material.polygonOffset = true;
                child.material.polygonOffsetFactor = 1.0;
                child.material.polygonOffsetUnits = -1.0;
            }
        });
        elements.handle_short_left_shadow.renderOrder = 100000;

        elements['top-bottom'].traverse(child => {
            if (child.type === 'Mesh') {
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
            }
        });

        elements['left-right'].traverse(child => {
            if (child.type === 'Mesh') {
                child.material.polygonOffset = true;
                child.material.polygonOffsetFactor = 1.0;
                child.material.polygonOffsetUnits = -1.0;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = this.reflectivityValue;
            }
        });

        if (!this.isOffscreen) {
            elements.typek.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.emissive = new THREE.Color(0xF0F0F0);
                    child.material.opacity = 1;
                    child.material.transparent = true;
                }
            });
        }

        // moved to separate function, as drawers and doors are different depending on shelf_type, so those will be
        // set to new models after each shelf_type change (used in screen_tool)
        this.setProperDoorsAndDrawersModel(this.shelfMaterial);
    }

    setProperDoorsAndDrawersModel(color) {
        let material_id;
        let opacity;
        let hex_color;
        let hex_handler_color;
        let backs_color;
        let reflectivityValue;
        let reflectivityValueDoors;

        [
            material_id,
            color,
            opacity,
            hex_color,
            hex_handler_color,
            backs_color,
            reflectivityValue,
            reflectivityValueDoors,
        ] = getDefinitionForMaterial(color, this.shelfType);
        if (this.shelfType === 1 || this.shelfType === 0) {
            this.elements.door.traverse(child => {
                if (child.type === 'Mesh') {
                    if (parseInt(material_id, 10) === 0) {
                        // eslint-disable-next-line no-param-reassign
                        child.material.map = this.elements.doors_open_white;
                    }
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.color = new THREE.Color(hex_color);
                    // child.material.alphaTest = 0.1;
                }
            });
            this.elements.door.renderOrder = 35;

            this.elements.drawer_front.traverse(child => {
                if (child.type === 'Mesh') {
                    if (parseInt(material_id, 10) === 0) {
                        child.material.map = this.elements.doors_open_white;
                    }
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.color = new THREE.Color(hex_color);
                }
            });
            this.elements.drawer_front.renderOrder = 35;
            this.elements.handle_short_left.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.color = new THREE.Color(hex_handler_color);
                }
            });
        } else if (this.shelfType === 2) {
            this.elements.fdoors.traverse(child => {
                if (child.type === 'Mesh') {
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.map = this.elements[`${color}-fdoors`];
                    child.material.needsUpdate = true;
                }
            });
            this.elements.fdoors.renderOrder = 35;

            this.elements.fdoors_big.traverse(child => {
                if (child.type === 'Mesh') {
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.map = this.elements[`${color}-fdrawer-big`];
                    child.material.needsUpdate = true;
                }
            });
            this.elements.fdoors_big.renderOrder = 35;

            this.elements.fdrawer.traverse(child => {
                if (child.type === 'Mesh') {
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.map = this.elements[`${color}-fdrawer`];
                    child.material.needsUpdate = true;
                }
            });
            this.elements.fdrawer.renderOrder = 35;

            this.elements.fdrawer_big.traverse(child => {
                if (child.type === 'Mesh') {
                    child.rotation.x = Math.PI / 2;
                    child.rotation.z = -Math.PI / 2;
                    child.geometry.center();
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.map = this.elements[`${color}-fdrawer-big`];
                    child.material.needsUpdate = true;
                }
            });
            this.elements.fdrawer_big.renderOrder = 35;

            this.elements.fhandle.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.transparent = false;
                    child.material.map = this.elements[`${color}-fdrawer`];
                    child.material.needsUpdate = true;
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueDoors;
                }
            });
        }
    }

    getScene() {
        return this.scene;
    }

    moveTypek() {
        const leftOffest = 400; // 300 mm
        if (!this.typek.length) this.addTypek();
        this.typek[0].position.z = 205;
        this.typek[0].position.x = -(this.width / 2 + leftOffest);
    }

    addTypek() {
        const typek = this.elements.typek.clone();
        typek.name = 'typek';
        typek.scale.setX(10);
        typek.scale.setY(10);
        this.scene.add(typek);
        this.typek.push(typek);
        this.createSceneBgPlane();
    }

    createSceneBgPlane() {
        const plane = new THREE.Mesh(
            new THREE.PlaneGeometry(20000, 10000, 1),
            new THREE.MeshBasicMaterial({ color: 0xedf0f0, side: THREE.FrontSide }),
        );
        plane.position.set(0, 5000, -3);
        plane.name = 'sceneBackground';
        this.scene.add(plane);
    }

    setScene(scene) {
        this.scene = scene;
        this.scene.background = new THREE.Color(0xf0f0f0);
        this.scene.opacity = 0.5;
    }

    setBackgroundScene(scene) {
        this.scene.background = new THREE.Color(scene);
        this.scene.opacity = 0.5;
    }

    clearScene() {
        this.points = {
            doors: [],
            horizontals: [],
            legs: [],
            verticals: [],
            supports: [],
            additional_elements: {
                shadow_left: [],
                shadow_middle: [],
                shadow_right: [],
                shadow_side: [],
            },
        };
        this.scene.remove.apply(this.scene, this.scene.children);
        this.scene.background = new THREE.Color(0xf0f0f0);
        this.scene.opacity = 0.5;
    }

    rebuildWallsFromJson(new_points, scene, { width, height, depth }) {
        this.width = width;
        this.height = height;
        this.depth = depth;
        new_points.topBottomWalls = this.getTopBottomWallPosition(new_points);
        new_points.leftRightWalls = this.getLeftRightWallPosition(new_points);
        new_points.additionalHorizontalElements = this.getAdditionalHorizontalPanelPosition(new_points);
        this.drawWalls(new_points, {}, scene, true);
        _points = new_points;
        if (!this.isOffscreen) this.moveTypek();
        this.createCastShadows({
            width,
            points: new_points,
            rowConfigurator: this.rowConfigurator,
        });
    }

    getIndicatorBoxesPositions(returnObject) {
        const boxes = this.walls.boxes.filter(box => box.name === 'buttons').filter(Boolean);
        return boxes.map(box => (returnObject ? box : box.position));
    }

    drawWalls(points, ivy, scene, snapping) {
        const { row_heights } = points;
        const rowHeights = row_heights
            || [278, 278, 278, 278, 278, 278, 278, 278, 278, 278, 278, 278, 278];
        // this.createDebugMode(1200, 600, 400);
        const getSizeFromPoint = item => new THREE.Vector3(
            Math.abs(item.x1 - item.x2),
            Math.abs(item.y1 - item.y2),
            Math.abs(item.z1 - item.z2),
        );
        const getPositionFromPoint = item => new THREE.Vector3(
            (item.x1 + item.x2) / 2,
            item.y1,
            (item.z1 + item.z2) / 2,
        );

        if (typeof points.boxes === 'undefined') {
            points.boxes = [];
        }

        // Deleting hover boxes
        this.walls.boxes.forEach(item => scene.remove(item));
        this.getIndicatorBoxesPositions(true).map(box => {
            scene.remove(box);
        });

        const shadow_translator = {
            shadow_middle: 0,
            shadow_left: 1,
            shadow_right: 2,
            shadow_side: 0,
            shadow_bottom: 5
        };


        // this.createFacePlaneForRaycasting();

        if (points.components) {
            this.componentHoverBoxes.map(box => scene.remove(box));
            this.componentHoverBoxes = [];
            points.components.forEach(item => {
                this.createComponentHoverBox.call(this, { points: item, furnitureDepth: 320, THREE });
            });
        }

        _.remove(this.walls.boxes, object => object.name == 'buttons');


        const typical_elements = ['verticals', 'horizontals', 'supports', 'backs', 'topBottomWalls', 'leftRightWalls', 'boxes', 'buttons', 'inserts', 'desk_beams'];
        typical_elements.forEach(element_type => {
            if (this.points[element_type] === undefined) {
                this.points[element_type] = [];
            }
            if (this.points[element_type] && this.points[element_type].length > 0 && points[element_type].length < this.points[element_type].length) {
                const dif = this.points[element_type].length - points[element_type].length;

                if (this.walls[element_type]) {
                    for (let j = this.walls[element_type].length - dif; j < this.walls[element_type].length; j++) {
                        scene.remove(this.walls[element_type][j]);
                    }
                    this.walls[element_type].length = this.walls[element_type].length - dif < 0 ? 0 : this.walls[element_type].length - dif;
                }
            }

            // arrays for holding points
            const newPoints = [];
            const oldPoints = [];
            // build points

            if (points[element_type] === undefined) {
                points[element_type] = [];
            }
            for (let i = 0; i < points[element_type].length; i++) {
                // if there are points push them for comparison
                if (
                    typeof this.points[element_type][i] !== 'undefined'
                ) {
                    newPoints.push({
                        bottom: new THREE.Vector3(
                            points[element_type][i].x1,
                            points[element_type][i].y1,
                            points[element_type][i].z1,
                        ),
                        top: new THREE.Vector3(
                            points[element_type][i].x2,
                            points[element_type][i].y2,
                            points[element_type][i].z2,
                        ),
                        raw: points[element_type][i],
                    });
                    oldPoints.push({
                        bottom: new THREE.Vector3(
                            this.points[element_type][i].x1,
                            this.points[element_type][i].y1,
                            this.points[element_type][i].z1,
                        ),
                        top: new THREE.Vector3(
                            this.points[element_type][i].x2,
                            this.points[element_type][i].y2,
                            this.points[element_type][i].z2,
                        ),
                        raw: points[element_type][i],
                    });
                } else {
                    // if there is more points add new ones
                    newPoints.push({
                        bottom: new THREE.Vector3(
                            points[element_type][i].x1,
                            points[element_type][i].y1,
                            points[element_type][i].z1,
                        ),
                        top: new THREE.Vector3(
                            points[element_type][i].x2,
                            points[element_type][i].y2,
                            points[element_type][i].z2,
                        ),
                        raw: points[element_type][i],
                    });
                }
            }
            // creating points for top and bottom;

            // create verticals from these points aray

            for (let i = 0; i < newPoints.length; i++) {
                if (element_type === 'verticals') {
                    this.createVertical(i, oldPoints[i], newPoints[i]);
                } else if (element_type === 'horizontals') {
                    this.createHorizontal(i, oldPoints[i], newPoints[i]);
                } else if (element_type === 'supports') {
                    const support = points[element_type][i];
                    const sameRowVerticals = points.verticals.filter(vertical => {
                        const verticalCenter = vertical.y2 - (vertical.y2 - vertical.y1) / 2;
                        return verticalCenter < support.y2 && verticalCenter > support.y1;
                    });
                    const nearestLeft = sameRowVerticals.sort((a, b) => b.x1 - a.x1).find(vertical => vertical.x1 < support.x1);
                    const nearestRight = sameRowVerticals.sort((a, b) => a.x1 - b.x1).find(vertical => vertical.x1 > support.x2);
                    const supportCenter = support.x2 - (support.x2 - support.x1) / 2;

                    let supportType = 'left';
                    if (nearestLeft === undefined || nearestRight === undefined) {
                        supportType = nearestLeft === undefined ? 'right' : 'left';
                    } else {
                        supportType = Math.abs(supportCenter - nearestLeft.x1) < Math.abs(supportCenter - nearestRight.x1) ? 'left' : 'right';
                    }

                    this.createSupport(i, oldPoints[i], newPoints[i], supportType);
                } else if (element_type === 'backs') {
                    this.createBack(i, oldPoints[i], newPoints[i]);
                } else if (element_type === 'topBottomWalls') {
                    this.createHorizontalPanels(i, oldPoints[i], newPoints[i]);
                } else if (element_type === 'leftRightWalls') {
                    this.createVerticalPanels(i, oldPoints[i], newPoints[i], ivy.pattern);
                } else if (element_type === 'inserts') {
                    this.createInsert(newPoints[i], element_type.subtype);
                } else if (element_type === 'buttons') {
                    this.createButton(newPoints[i], newPoints);
                }
            }
        });

        const shadows = points.additional_elements;
        this.walls.shadows.forEach(shadow_list => {
            for (let i = 0; i < shadow_list.length; i++) {
                scene.remove(shadow_list[i]);
            }
            shadow_list.length = 0;
        });
        for (const shadow_type in shadows) {
            if (shadow_type === 'styles') {
                continue; // as its style for doors, not shadow
            }
            const shadow_list = shadows[shadow_type];

            const newPoints = [];
            for (let i = 0; i < shadow_list.length; i++) {
                newPoints.push({
                    size: getSizeFromPoint(shadow_list[i]),
                    position: getPositionFromPoint(shadow_list[i]),
                });
            }
            for (let i = 0; i < newPoints.length; i++) {
                this.createShadows(
                    i,
                    undefined,
                    newPoints[i],
                    shadow_translator[shadow_type],
                );
            }
        }

        for (let i = 0; i < this.walls.additionalHorizontalElements.length; i++) {
            scene.remove(this.walls.additionalHorizontalElements[i]);
        }
        this.walls.additionalHorizontalElements.length = 0;
        // create double openings
        for (let i = 0; i < points.additionalHorizontalElements.length; i++) {
            this.createHorizontalPlug(points.additionalHorizontalElements[i]);
        }

        // remove legs because there could be less
        for (let i = 0; i < this.walls.legs.length; i++) {
            scene.remove(this.walls.legs[i]);
        }
        this.walls.legs.length = 0;

        // create new legs
        if (points.legs) {
            for (let i = 0; i < points.legs.length; i++) {
                this.createLegs(points.legs[i]);
            }
        }
        if (points.long_legs) {
            for (let i = 0; i < points.long_legs.length; i++) {
                this.createLegsType02(points.long_legs[i]);
            }
        }

        // cable_management
        for (let i = 0; i < this.walls.cable_management.length; i++) {
            scene.remove(this.walls.cable_management[i]);
        }
        this.walls.cable_management.length = 0;

        if (points.cable_management) {
            for (let i = 0; i < points.cable_management.length; i++) {
                this.createGrommet(points.cable_management[i], this.color);
            }
        }
        // plinths
        for (let i = 0; i < this.walls.plinth.length; i++) {
            scene.remove(this.walls.plinth[i]);
        }
        this.walls.plinth.length = 0;
        if (points.plinth) {
            for (let i = 0; i < points.plinth.length; i++) {
                this.createPlinth({
                    bottom: { x: points.plinth[i].x1, y: points.plinth[i].y1, z: points.plinth[i].z1 },
                    top: { x: points.plinth[i].x2, y: points.plinth[i].y2, z: points.plinth[i].z2 },
                    raw: points.plinth[i],
                });
            }
        }
        // desk beams
        for (let i = 0; i < this.walls.deskBeams.length; i++) {
            scene.remove(this.walls.deskBeams[i]);
        }
        this.walls.deskBeams.length = 0;
        if (points.desk_beams) {
            for (let i = 0; i < points.desk_beams.length; i++) {
                this.createDeskBeam(points.desk_beams[i]);
            }
        }

        //
        // REMOVER DOORS
        //
        this.door_lists = [[], [], [], [], [], [], [], [], [], [], [], []];
        this.door_lists_elements = [[], [], [], [], [], [], [], [], [], [], [], []];
        for (let i = 0; i < this.walls.doors.length; i++) {
            scene.remove(this.walls.doors[i]);
        }
        for (let i = 0; i < this.walls.door_groups.length; i++) {
            scene.remove(this.walls.door_groups[i]);
        }
        this.walls.door_groups.length = 0;
        this.walls.doors.length = 0;

        //
        // CREATE DOORS
        //

        for (let i = 0; i < points.doors.length; i++) {
            this.createDoor(
                getPositionFromPoint(points.doors[i]),
                getSizeFromPoint(points.doors[i]),
                points.doors[i].type,
                points.doors[i].flip,
                points.doors[i].parity,
                rowHeights,
                this.shelfType,
                !!points.doors[i].selected,
                points.doors[i].innerOffset,
                points.doors[i].handleYPos,
                points.doors[i].m_config_id,
            );
        }

        //
        // END DOORS
        //


        //
        // REMOVE DRAWERS
        //
        this.drawers_lists = [[], [], [], [], [], [], [], [], [], [], [], []];
        for (let i = 0; i < this.walls.drawers.length; i++) {
            scene.remove(this.walls.drawers[i]);
        }
        this.walls.drawers.length = 0;
        //
        // CREATE DRAWERS
        //
        for (let i = 0; i < points.drawers.length; i++) {
            this.createDrawers(
                getPositionFromPoint(points.drawers[i]),
                getSizeFromPoint(points.drawers[i]),
                rowHeights,
                this.shelfType,
                points.drawers[i],
            );
        }
        //
        // END CREATE DRAWERS
        //


        const calculatedCapacity = getTotalCapacity(
            points,
            ivy.rows,
            ivy.pattern,
            ivy.width,
        );


        this.capacity = calculatedCapacity[0]; // Shelf total load
        this.compartmentCapacity = [calculatedCapacity[1], calculatedCapacity[2]]; // [ Capacity Min, Capacity Max ]

        this.points = points;
        this.row_styles_availability = points.additional_elements.styles;
        this.depth_previous = this.depth;
    }

    setShelfType(shelfType = 0, material = this.shelfMaterial) {
        this.shelfType = shelfType;
        this.setProperDoorsAndDrawersModel(material);
    }

    setDesignerMode(mode) {
        this.designer_mode = mode;
    }

    setDnaTool(dnaTools) {
        this.dnaTools = dnaTools;
    }


    // the same as verticals, horizontals but also takes what type of shadow it is to know which element to clone

    getLeftRightWallPosition(points) {
        const topLeft = [];

        const [min, max] = points.verticals.reduce((accumulator, vertical) => {
            accumulator[0] = vertical.x1 < accumulator[0] ? vertical.x1 : accumulator[0];
            accumulator[1] = vertical.x1 > accumulator[1] ? vertical.x1 : accumulator[1];
            return accumulator;
        }, [0, 0]);

        points.verticals.forEach(item => {
            if (item.x1 === max || item.x1 === min) topLeft.push(item);
        });

        return topLeft;
    }

    getTopBottomWallPosition(points) {
        let topWallY = 18;
        const bottomWallY = 0;
        points.horizontals.forEach(item => {
            if (topWallY > item.y1) return;
            topWallY = item.y1;
        });
        const topBottom = points.horizontals.filter(item => item.y1 === topWallY || item.y1 === bottomWallY);
        return topBottom;
    }

    getAdditionalHorizontalPanelPosition(points) {
        const minX = _.min(points.horizontals.map(x => _.min([x.x1, x.x2])));
        const maxX = _.max(points.horizontals.map(x => _.max([x.x1, x.x2])));
        const panelPoints = [];

        for (const i of points.horizontals) {
            const localMinX = _.min([i.x1, i.x2]);
            const localMaxX = _.max([i.x1, i.x2]);
            if (localMinX > minX) {
                panelPoints.push({ x: localMinX - 1, y: i.y1 });
            }
            if (localMaxX < maxX) {
                panelPoints.push({ x: localMaxX + 1, y: i.y1 });
            }
        }
        return panelPoints; // topBottom;
    }


    setMaterialColor({
        color,
        shelf_type,
        skipTracking = true,
    }) {
        const localElements = this.elements;
        const { elements } = this;
        const { walls } = this;
        // let magicalElements = this.magical_materials_for_rows;

        let opacity;
        let hex_color;
        let hex_handler_color;
        let backs_color;
        let reflectivityValue;
        let reflectivityValueDoors;
        let reflectivityValueGrommet;
        let material_id;

        [
            material_id,
            color,
            opacity,
            hex_color,
            hex_handler_color,
            backs_color,
            reflectivityValue,
            reflectivityValueDoors,
            reflectivityValueGrommet,
        ] = getDefinitionForMaterial(color, shelf_type);

        // console.log('setColor??', color, shelf_type, skipTracking = true)

        // change base elements based on color
        const reflectionCube = localElements.cubemap;

        if (localElements.vertical) {
            localElements.vertical.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-vert`];
                    child.material.needsUpdate = true;
                    // child.material.color = new THREE.Color(1,1,1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements.horizontal) {
            localElements.horizontal.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-hori`];
                    child.material.needsUpdate = true;
                    // child.material.color = new THREE.Color(1,1,1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements['horizontal-plug']) {
            localElements['horizontal-plug'].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-hori`];
                    child.material.needsUpdate = true;
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements.support) {
            localElements.support.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = (shelf_type === 0) ? 
                        elements[`plain_support`] : 
                        elements[`${color}-support`];
                    child.material.needsUpdate = true;
                    child.material.color = (shelf_type === 0) ? 
                        new THREE.Color(hex_color) : 
                        new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 40;
                }
            });
        }

        if (localElements.plinth_m) {
            localElements.plinth_m.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = elements[`${color}-plinth`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 40;
                }
            });
        }

        if (localElements.plinth_mbar) {
            localElements.plinth_mbar.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = elements[`${color}-plinth`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 40;
                }
            });
        }

        if (localElements.plinth_l_narrow) {
            localElements.plinth_l_narrow.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = elements[`${color}-plinth`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 40;
                }
            });
        }

        if (localElements.plinth_r_narrow) {
            localElements.plinth_r_narrow.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = elements[`${color}-plinth`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 40;
                }
            });
        }


        if (localElements.insert) {
            localElements.insert.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = elements[`${color}-insert`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 15;
                }
            });
        }

        if (localElements['support-drawer']) {
            localElements['support-drawer'].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-support-drawer`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements.shadow) {
            localElements.shadow.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-shadowbox`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 0;
                }
            });
        }

        if (localElements['shadow-bottom']) {
            localElements['shadow-bottom'].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-shadowbox`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                    child.renderOrder = 0;
                }
            });
        }

        if (localElements['shadow-left']) {
            localElements['shadow-left'].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-shadowbox`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements['shadow-right']) {
            localElements['shadow-right'].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-shadowbox`];
                    child.material.needsUpdate = true;
                    child.material.color = new THREE.Color(1, 1, 1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements['left-right']) {
            localElements['left-right'].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-vert`];
                    child.material.needsUpdate = true;
                    // child.material.color = new THREE.Color(1,1,1);
                    child.material.transparent = false;
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements['top-bottom']) {
            localElements['top-bottom'].traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-hori`];
                    child.material.needsUpdate = true;
                    // child.material.color = new THREE.Color(1,1,1);
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements.leg_s_plus) {
            localElements.leg_s_plus.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-leg`];
                    child.material.needsUpdate = true;
                    // child.material.color = new THREE.Color(1,1,1);
                    child.material.reflectivity = reflectivityValue;
                }
            });
        }

        if (localElements.grommet_plug) {
            localElements.grommet_plug.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-hori`];
                    child.material.needsUpdate = true;
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValueGrommet;
                }
            });
        }

        if (localElements.grommet_plug_veneer) {
            localElements.grommet_plug_veneer.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}-grommet`];
                    child.material.needsUpdate = true;
                }
            });
        }

        this.setProperDoorsAndDrawersModel(material_id);

        if (localElements.backs) {
            localElements.backs.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.color = new THREE.Color(backs_color);
                    // child.material.reflectivity = reflectivityValue;
                    child.renderOrder = -2000;
                }
            });
        }

        if (localElements.drawers) {
            localElements.drawers.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.color = new THREE.Color(hex_color);
                    child.material.reflectivity = reflectivityValueDoors;
                    child.material.needsUpdate = true;
                }
            });
        }

        if (localElements.leg_t02) {
            localElements.leg_t02.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material = new THREE.MeshBasicMaterial();
                    child.material.color = new THREE.Color(hex_handler_color);
                    if (color === 'beige' || color === 'mint') child.material.map = localElements[`${color}-hori`];
                    else child.material.color.sub(new THREE.Color(0.01, 0.01, 0.01));
                    child.material.needsUpdate = true;
                }
            });
        }

        if (localElements.plinth_m_bar) {
            localElements.plinth_m_bar.traverse(child => {
                if (child.type === 'Mesh') {
                    child.material.dispose();
                    child.material.map = localElements[`${color}_plinth_m`];
                    child.material.needsUpdate = true;
                }
            });
        }

        if (localElements.handle_big) {
            localElements.handle_big.traverse(child => {
                if (child.type === 'Mesh') {
                    child.rotation.x = -Math.PI / 2;
                    child.material.map = elements.doors;
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValue;
                    child.material.color = new THREE.Color(hex_handler_color);
                }
            });
        }

        if (localElements.handle_small) {
            localElements.handle_small.traverse(child => {
                if (child.type === 'Mesh') {
                    child.rotation.x = -Math.PI / 2;
                    child.material.map = elements.doors;
                    child.material.envMap = reflectionCube;
                    child.material.reflectivity = reflectivityValue;
                    child.material.color = new THREE.Color(hex_handler_color);
                }
            });
        }

        localElements.handle_drawer.traverse(child => {
            if (child.type === 'Mesh') {
                // child.rotation.x = -Math.PI / 2;
                child.material.map = elements.doors_open;
                child.material.envMap = reflectionCube;
                child.material.reflectivity = reflectivityValue;
                child.material.color = new THREE.Color(hex_handler_color);
            }
        });
        localElements.handle_drawer.renderOrder = 40;
    }


    getIndents(shelfSize) {
        return [
            ..._points.additional_elements.shadow_left,
            ..._points.additional_elements.shadow_right,
        ]
            .map(shdw => ({
                x: shdw.x1,
                y: shdw.y1,
                z: shdw.x2,
                w: shdw.y2,
            }))
            .map(indent => ({
                w: indent.z - indent.x,
                h: indent.w - indent.y,
                x: indent.z,
                y: indent.w,
            }))
            .map((size, n) => {
                const half = 0.5;
                const fixWarp = { y: -16, x: 10 };

                const left = -size.x + size.w * half;
                const top = shelfSize[1] - Math.abs(size.y - size.h * half - fixWarp.y) - 30;

                return [left, top,
                    size.w * half + fixWarp.x,
                    size.h * half - fixWarp.y,
                ];
            });
    }

    // create the casted shadows which consists of 3 parts: sides and center, positioned flat on the ground below the shelf

    // compare dna points
    compare_dnas(old_one, new_one) {
        const result = {};

        // check if it is the same
        if (
            old_one.bottom.x === new_one.bottom.x
            && old_one.bottom.y === new_one.bottom.y
            && old_one.top.x === new_one.top.x
            && old_one.top.y === new_one.top.y
        ) {
            result.same = true;
            return result;
        }
        // check if it was only moved on X
        if (
            Math.abs(old_one.bottom.x - old_one.top.x)
            === Math.abs(new_one.bottom.x - new_one.top.x)
            && Math.abs(old_one.bottom.y - old_one.top.y)
            === Math.abs(new_one.bottom.y - new_one.top.y)
        ) {
            result.only_moved = true;
        }
        // check if it was scaled
        if (
            Math.abs(old_one.top.y - old_one.bottom.y)
            !== Math.abs(new_one.top.y - new_one.bottom.y)
            || Math.abs(old_one.top.x - old_one.bottom.x)
            !== Math.abs(new_one.top.x - new_one.bottom.x)
        ) {
            result.scaled = true;
        }

        return result;
    }

    // same as earlier, only the points are bit easier to compare
    compare_shadows(old_one, new_one) {
        const result = {};

        if (
            old_one.position.x === new_one.position.x
            && old_one.position.y === new_one.position.y
            && old_one.size.x === new_one.size.x
            && old_one.size.y === new_one.size.y
        ) {
            result.same = true;
            return result;
        }
        if (
            old_one.position.x !== new_one.position.x
            || old_one.position.y !== new_one.position.y
        ) {
            result.moved = true;
        }
        if (
            old_one.size.x !== new_one.size.x
            || old_one.size.y !== new_one.size.y
        ) {
            result.scaled = true;
        }

        return result;
    }

    get_only_points(ivy, pattern, motion, width, rows, rowHeight, rowStyles) {
        const points = this.dnaTools.get_elements(
            ivy.patterns[pattern].json,
            motion,
            width * 10,
            rows,
            rowHeight,
            rowStyles,
            320,
            9,
            125,
            true,
            [0, 0, 0, 0],
        );
        return points;
    }

    toggleShadowCast() {
        shadowCastEnabled = !shadowCastEnabled;
        // if (!this.rowConfigurator) this.createCastShadows();
    }
}

export { Build3d };
