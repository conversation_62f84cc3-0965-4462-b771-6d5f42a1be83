function getTotalCapacity(shelfGeometry, rowAmount, dnaID, width, shelfType) {
    const capacityTable = {
        300: 50,
        400: 40,
        500: 35,
        600: 30,
        700: 25,
        800: 20,
        900: 15,
        1000: 10,
    };

    const setTableType02 = {
        // Slant

        0: {
            default: 8,
            subset_len: 0.2,
            doors: 0.7,
            param_1: 0.85,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 5,
            p_max: 60,
            o_min: 10,
            o_max: 40,
        },
        // Grid

        1: {
            default: 10,
            subset_len: 0.15,
            doors: 0.95,
            param_1: 1,
            param_2: 0.98,
            param_3: 0.87,
            p_min: 20,
            p_max: 80,
            o_min: 20,
            o_max: 60,
        },

        // Pattern

        2: {
            default: 6,
            subset_len: 0.25,
            doors: 0.8,
            param_1: 1,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 10,
            p_max: 60,
            o_min: 20,
            o_max: 45,
        },


        // Gradient

        3: {
            default: 7,
            subset_len: 0.3,
            doors: 0.9,
            param_1: 1.2,
            param_2: 0.96,
            param_3: 0.88,
            p_min: 15,
            p_max: 65,
            o_min: 20,
            o_max: 50,
        },


    };

    const setTable = {
        // Slant

        0: {
            default: 8,
            subset_len: 0.2,
            doors: 0.7,
            param_1: 0.85,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 5,
            p_max: 60,
            o_min: 10,
            o_max: 40,
        },

        // Gradient

        1: {
            default: 7,
            subset_len: 0.3,
            doors: 0.9,
            param_1: 1.2,
            param_2: 0.96,
            param_3: 0.88,
            p_min: 15,
            p_max: 65,
            o_min: 20,
            o_max: 50,
        },

        // Pattern

        2: {
            default: 6,
            subset_len: 0.25,
            doors: 0.8,
            param_1: 1,
            param_2: 0.95,
            param_3: 0.88,
            p_min: 10,
            p_max: 60,
            o_min: 20,
            o_max: 45,
        },

        // Grid

        3: {
            default: 10,
            subset_len: 0.15,
            doors: 0.95,
            param_1: 1,
            param_2: 0.98,
            param_3: 0.87,
            p_min: 20,
            p_max: 80,
            o_min: 20,
            o_max: 60,
        },
    };

    let parameters = setTable['0'];

    if (shelfType === 1) {
        if (dnaID in setTableType02) {
            parameters = setTableType02[dnaID];
        }
    } else if (dnaID in setTable) {
        parameters = setTable[dnaID];
    }

    if (shelfGeometry.additional_elements.shadow_middle.length === 0) {
        return false;
    }

    const allSorted = shelfGeometry.additional_elements.shadow_middle
        .map(el => el.x2 - el.x1)
        .sort((first, second) => second - first);
    const lengthAll = allSorted.length;
    const shelfAmount = parseInt(Math.floor(lengthAll * parameters.subset_len), 10) || 1;
    const allSum = allSorted
        .slice(0, shelfAmount)
        .reduce((accumulator, currentValue) => accumulator + currentValue);
    const slicedCount = shelfAmount <= allSorted.length ? shelfAmount : allSorted.length;
    const sumAverage = allSum / slicedCount;

    function getCapacity(k) {
        let capacity = parameters.default;

        for (const key in capacityTable) {
            if (parseInt(key) >= k) {
                capacity = capacityTable[key];
                break;
            }
        }
        return capacity;
    }

    const doors = shelfGeometry.doors || [];
    const drawers = shelfGeometry.drawers || [];
    const doorsAndDrawers = doors.concat(drawers);

    let adjustment = doorsAndDrawers.length > 0 ? parameters.doors : 1;
    adjustment *= Math.pow(parameters.param_1 * parameters.param_2, rowAmount);

    const openings = [
        roundTo(getCapacity(allSorted[0]) * adjustment, 5),
        roundTo(getCapacity(allSorted[allSorted.length - 1] * adjustment), 5),
    ];

    // Check for Slant and Grid DNA Cases.
    function checkIfOpeningsAreEqual(array) {
        // For even openings, JS sometimes calculates almost identical values, but not entirely identical ( e.g. 379mm, 381mm ).
        const maxDifferenceBetweenOpenings = 2;
        for (let i = 0; i < array.length; i++) {
            if (Math.abs(array[0] - array[i]) > maxDifferenceBetweenOpenings) {
                return false;
            }
        }
        return true;
    }

    const capMin = checkIfOpeningsAreEqual(allSorted)
        ? null
        : Math.max(parameters.o_min, Math.min.apply(Math, openings));
    const capMax = Math.min(parameters.o_max, Math.max.apply(Math, openings));

    const parameterWidth = rowAmount * width / 1000; // Przejscie na metro rzedy
    const total = getCapacity(sumAverage) * adjustment * lengthAll * parameters.param_3;

    const totalMax = Math.max(
        Math.min(parameters.p_max * parameterWidth, total),
        parameters.p_min * parameterWidth,
        lengthAll * parameters.o_min,
    );

    function roundTo(value, multiplicity) {
        const rounded = parseInt(value / multiplicity, 10) * multiplicity;
        return rounded;
    }

    const totalMaxFlat = roundTo(totalMax, 10); // 264.444 -> 260
    const capacityMultiplier = shelfType === 1 ? 0.66 : 1;

    return {
        totalMaxCapacity: roundTo(totalMaxFlat * capacityMultiplier, 10),
        compartmentCapacityMin: roundTo(capMin * capacityMultiplier, 1),
        compartmentCapacityMax: roundTo(capMax * capacityMultiplier, 1),
    };
}

export default getTotalCapacity;
