export default
    `
        uniform sampler2D foreground;
        uniform sampler2D bg; 
        uniform vec3 blending_color;
        uniform vec3 color;
        uniform float blending_ratio;
        varying vec2 vUv;
        
        float blendOverlay(float base, float blend) {
            return base<0.5?(2.0*base*blend):(1.0-2.0*(1.0-base)*(1.0-blend));
        }
        
        vec3 blendOverlay(vec3 base, vec3 blend) {
            return vec3(blendOverlay(base.r,blend.r),blendOverlay(base.g,blend.g),blendOverlay(base.b,blend.b));
        }
        
        vec3 blendOverlay(vec3 base, vec3 blend, float opacity) {
            return (blendOverlay(base, blend) * opacity + base * (1.0 - opacity));
        }
        
        vec3 blendSoftLight(vec3 base, vec3 blend) {
        return mix(
            sqrt(base) * (2.0 * blend - 1.0) + 2.0 * base * (1.0 - blend),
            2.0 * base * blend + base * base * (1.0 - 2.0 * blend),
            step(base, vec3(0.5))
            );
        }
        
        void main() {
            vec4 bgColor = texture2D(bg, vUv);
            vec4 fgColor = texture2D(foreground, vUv);
            
            //vec3 color = blendSoftLight(blending_color,mix(bgColor.rgb, fgColor.rgb, blending_ratio));
            //vec3 color = blendOverlay(blending_color,mix(bgColor.rgb, fgColor.rgb, blending_ratio),0.8);
            vec3 color = mix(bgColor.rgb, fgColor.rgb, blending_ratio) * blending_color;
            //vec3 color = mix(bgColor.rgb, fgColor.rgb, blending_ratio);
            
            gl_FragColor = vec4(color, 1.0);
        }
    `;
