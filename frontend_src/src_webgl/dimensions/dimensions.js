import { clamp } from 'lodash';
import {
    BoxGeometry,
    DoubleSide,
    Group,
    LineBasicMaterial,
    Mesh,
    MeshBasicMaterial,
    Shape,
    ShapeGeometry,
} from 'three';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader';
import { dimPillsSettings } from './settings';

class Dimensions {
    constructor(scene, mode = 'cplus') {
        this.scene = scene;
        this.mode = mode;
        this.labelGroup = new Group();
        this.labelGroup.name = 'dimensions';
        this.items = [];
        const loader = new FontLoader();
        loader.load('/r_static/fonts_3d/helvetiker_regular.typeface.json', font => {
            this.font = font;
        });
        this.ranges = dimPillsSettings[this.mode].ranges;
        this.dimPillsSettings = dimPillsSettings[this.mode];
    }

    regeneratePills(shelfSize) {
        const { width, height } = shelfSize;
        const pillSize = this.calculatePillSize(width, height);
        this.dimPillsSettings.pillSize = pillSize;
        this.pillWidth = addShape(trackShape(pillSize), 0x484444);
        this.pillHeight = addShape(trackShape(pillSize), 0x7C7D81);
        this.pillDepth = addShape(trackShape(pillSize), 0x484444);
    }

    calculatePillSize(width, height) {
        const {
            shelfSizeThresholdMin, shelfSizeThresholdMax, pillSizeMin, pillSizeMax,
        } = this.dimPillsSettings.pillSizeParams;
        const shelfSizeSpan = shelfSizeThresholdMax - shelfSizeThresholdMin;
        const pillSizeSpan = pillSizeMax - pillSizeMin;

        const shelfSizeMagnitude = Math.max(width, height);
        const pillSizeFactor = clamp((shelfSizeMagnitude - shelfSizeThresholdMin) / shelfSizeSpan, 0, 1);
        return Math.round(pillSizeMin + pillSizeFactor * pillSizeSpan);
    }

    addTextToScene(scene, value, font, color) {
        const matDark = new LineBasicMaterial({
            color,
            side: DoubleSide,
        });
        const shapes = font.generateShapes(value.toString(), this.dimPillsSettings.pillSize);
        const geometry = new ShapeGeometry(shapes);
        geometry.computeBoundingBox();
        const xMid = -0.5 * (geometry.boundingBox.max.x - geometry.boundingBox.min.x);
        geometry.translate(xMid, 0, 0);
        const text = new Mesh(geometry, matDark);
        text.position.x = value < 20 ? -6 : value > 99 && value < 200 ? -8 : -2;
        text.position.y = 2;
        text.position.z = 1;

        return text;
    }

    drawDimensions(dimensions, shelfSize, doorsAreOpened = true) {
        this.removeDimensions();
        this.regeneratePills(shelfSize);

        dimensions
            .filter(({ properties }) => properties.visibility === 'always'
                || (properties.visibility === 'whenOpen' && doorsAreOpened)
                || (properties.visibility === 'whenClosed' && !doorsAreOpened))
            .forEach(dimension => this.drawDimension(dimension));
    }

    drawDimension(dimension) {
        const LINE_LABEL_DISTANCE = 5;

        const { measurement } = dimension.properties;
        const group = this.drawPill(dimension);

        if (dimension.properties.context !== 'interior') {
            const line = addLine({
                range: dimension.range,
                label: group.position,
                measurement,
            });

            if (measurement === 'width') {
                line.position.y += this.dimPillsSettings.pillSize / 2;
                line.position.z -= LINE_LABEL_DISTANCE;
            } else if (measurement === 'height') {
                line.position.x += this.dimPillsSettings.pillSize / 6;
                line.position.z -= LINE_LABEL_DISTANCE;
            } else if (measurement === 'depth') {
                line.position.y += this.dimPillsSettings.pillSize / 2;
                line.position.x += LINE_LABEL_DISTANCE;
            }

            this.scene.add(line);
            this.items.push(line);
        }
    }

    drawPill(dimension) {
        const { measurement } = dimension.properties;
        const textColor = { width: 0xF9F9F9, height: 0xFFFFFF, depth: 0xF9F9F9 }[measurement];
        const text = this.addTextToScene(this.scene, dimension.value, this.font, textColor);
        const pillToClone = { width: this.pillWidth, height: this.pillHeight, depth: this.pillDepth }[measurement];
        const clonePill = pillToClone.clone();
        const group = new Group();
        group.add(clonePill);
        group.add(text);
        const offset = dimension.properties.context !== 'silhouette'
            ? {
                x: this.dimPillsSettings.pillSize * this.dimPillsSettings[measurement].offsetX,
                y: this.dimPillsSettings.pillSize * this.dimPillsSettings[measurement].offsetY,
                z: this.dimPillsSettings.pillSize * this.dimPillsSettings[measurement].offsetZ,
            }
            : { x: 0, y: 0, z: 0 };
        group.position.setX(dimension.labelPoint.x + offset.x);
        group.position.setY(dimension.labelPoint.y + offset.y);
        group.position.setZ(dimension.labelPoint.z + offset.z);
        if (measurement === 'depth') { group.rotation.y = -Math.PI / 2; }
        this.items.push(group);
        this.scene.add(group);
        return group;
    }

    removeDimensions() {
        this.items.forEach(i => this.scene.remove(i));
        this.items = [];
    }
}

const trackShape = size => {
    const shape = new Shape();
    shape.absarc(0, 0, size, -Math.PI / 2, Math.PI / 2, true);
    shape.lineTo(size, size);
    shape.absarc(size, 0, size, Math.PI / 2, -Math.PI / 2, true);
    return { shape, size };
};

const addShape = ({ shape, size }, color) => {
    const geometry = new ShapeGeometry(shape);
    const material = new MeshBasicMaterial({ color, side: DoubleSide });
    const mesh = new Mesh(geometry, material);
    mesh.position.x = -size / 2;
    mesh.position.y = size / 2;
    mesh.opacity = 0.7;
    return mesh;
};

const addLine = ({ range, label, measurement }) => {
    const LINE_THICKNESS = 3;
    const EDGE_THICKNESS = 2;
    const EDGE_BOX_LENGTH = 50;

    const coord = { width: 'x', height: 'y', depth: 'z' }[measurement];
    const lineStart = range.start[coord];
    const lineEnd = range.end[coord];
    const lineLength = lineEnd - lineStart;
    const lineBoxLength = -lineLength;
    const lineBoxPosition = (lineStart + lineEnd) / 2;

    const line = {
        size: [
            measurement === 'width' ? lineBoxLength : LINE_THICKNESS,
            measurement === 'height' ? lineBoxLength : LINE_THICKNESS,
            measurement === 'depth' ? lineBoxLength : LINE_THICKNESS,
        ],
        xPosition: measurement === 'width' ? lineBoxPosition : label.x,
        yPosition: measurement === 'height' ? lineBoxPosition : label.y,
        zPosition: measurement === 'depth' ? lineBoxPosition : label.z,
    };

    const startEdgePosition = lineStart + EDGE_THICKNESS / 2;
    const endEdgePosition = lineEnd - EDGE_THICKNESS / 2;

    const edgeBoxSize = [
        measurement === 'height' ? EDGE_BOX_LENGTH : EDGE_THICKNESS,
        measurement !== 'height' ? EDGE_BOX_LENGTH : EDGE_THICKNESS,
        EDGE_THICKNESS,
    ];

    const startEdge = {
        size: edgeBoxSize,
        xPosition: measurement === 'width' ? startEdgePosition : line.xPosition,
        yPosition: measurement === 'height' ? startEdgePosition : line.yPosition,
        zPosition: measurement === 'depth' ? startEdgePosition : line.zPosition,
    };

    const endEdge = {
        size: edgeBoxSize,
        xPosition: measurement === 'width' ? endEdgePosition : line.xPosition,
        yPosition: measurement === 'height' ? endEdgePosition : line.yPosition,
        zPosition: measurement === 'depth' ? endEdgePosition : line.zPosition,
    };

    const group = new Group();

    [line, startEdge, endEdge].forEach(elem => {
        const lineGeom = new BoxGeometry(...elem.size);
        const lineMat = new MeshBasicMaterial({ color: measurement === 'height' ? 0x7C7D81 : 0x484444 });
        const lineMesh = new Mesh(lineGeom, lineMat);
        lineMesh.position.setX(elem.xPosition);
        lineMesh.position.setY(elem.yPosition);
        lineMesh.position.setZ(elem.zPosition);

        group.add(lineMesh);
    });

    return group;
};

export default Dimensions;
