import * as THREE from 'three';

export const convert3dToPixels = (x, y, z, wrapper, camera) => {
    const width = parseFloat(wrapper.offsetWidth)
    const height = parseFloat(wrapper.offsetHeight)
    const point = new THREE.Vector3()
    point.set(x, y, z)
    point.project(camera);
    return {
        x: Math.round((point.x + 1) / 2 * width),
        y: Math.round(-(point.y - 1) / 2 * height),
    }
}
