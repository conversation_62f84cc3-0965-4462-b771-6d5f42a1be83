
import zip from 'lodash/zip';
import flattenDeep from 'lodash/flattenDeep';


const TYLKO_BLADE_OF_SLAWEK = 'o=}===>';
const url = (a, b) => new URL(b, a).href;

const namesType01 = ['hori', 'vert', 'support', 'support-drawer', 'shadowbox'];
const fileNamesType01 = ['top_bottom', 'left_right', 'support', 'support_drawer', 'shadow_box'];

const namesType02 = ['hori', 'vert', 'support', 'support-drawer', 'shadowbox'];
const fileNamesType02 = ['top_bottom', 'left_right', 'support', 'support_drawer', 'shadow_box'];

const namesType01Veneer = ['hori', 'vert', 'support', 'support-drawer', 'shadowbox', 'fdoors', 'fdrawer'];
const fileNamesType01Veneer = ['top_bottom', 'left_right', 'support', 'support_drawer', 'shadow_box', 'fdoors', 'fdrawer'];

class AsyncLoader {

    constructor({
                    root = 'r_static/src_webgl/ivy/models/',
                    maxFilesPerCycle = 5,
                } = { /* */}) {

        this.elements = [];
        this.cache = [];

        this.base = url(location.origin, root);
    }

    loadColorTextures(colors, shelfType = 0, {fileType = 'jpg'} = { /* */}) {

        // Loads all textures for provided color name
        // Loads colors set with priotity determined by horizontal
        // relation to onboardingColor index in array (neighbours first).

        let baseUrl = url(this.base, "textures/");

        let fix = (name, isName) => name.replace(' ', isName ? '-' : '_');

        let names = [];
        let fileNames = [];

        switch (shelfType) {
        case 0:
            names = namesType01;
            fileNames = fileNamesType01;
            break;
        case 1:
            names = namesType02;
            fileNames = fileNamesType02;
            break;
        case 2:
            names = namesType01Veneer;
            fileNames = fileNamesType01Veneer;
            break;
        default:
            names = namesType01;
            fileNames = fileNamesType01;
            break;
        }
        // let names = ['hori', 'vert', 'support', 'support-drawer', 'shadowbox', 'fdoors', 'fdrawer'];
        // let fileNames = ['top_bottom', 'left_right', 'support', 'support_drawer', 'shadow_box', 'fdoors', 'fdrawer'];


        let loadColor = (color) =>
            zip(
                names.map((name) => `${fix(color, true)}-${name}`),
                fileNames.map((name) => `${fix(color)}_${name}.${fileType}`))
                .map((texture) => this.loadTexture({
                    name: texture[0],
                    url: url(baseUrl, texture[1])
                })
            );

        let jobs = [].concat(colors).map(loadColor);

        return flattenDeep(jobs);
    }

    addTextureToCache(textureDescription) {
        this.cache[textureDescription.name] = textureDescription.texture;
        return textureDescription.name;
    }

    addModelToCache(model, name = null) {
        this.cache[name ? name : model.name] = model;
        return model.name;
    }

    loadTexture(textureDescription, resolveBase = true) {

        return new Promise((resolve) => {
            var finialize = (add, t) => {
                resolve(add ? this.addTextureToCache({
                    name: textureDescription.name,
                    texture: t
                }) : "ERROR");
            };

            var texture = new THREE.TextureLoader().load(
                resolveBase ? url(this.base, textureDescription.url) : textureDescription.url,
                (t) => finialize(true, t),
                undefined,
                (t) => finialize(false)
            );
        });
    }

    loadCubemap(name, {
        fileType = 'jpg',
        filenames = ['px', 'nx', 'py', 'ny', 'pz', 'nz']
    } = { /**/}) {

        let baseUrl = url(this.base, "textures/");

        let urls = filenames.map((face) => url(url(baseUrl, `${name}/`), `${face}.${fileType}`));

        return new Promise((resolve) => {
            let finialize = (add) => (result) =>
                resolve(add ? this.addTextureToCache({
                    name: name,
                    texture
                }) : "ERROR");

            let texture = new THREE.CubeTextureLoader().load(
                urls,
                finialize(true),
                undefined,
                finialize(false)
            );
        });
    }

    loadPlane(planeDescription, resolveBase = true) {

        return new Promise((resolve) => {
            let finialize = (add) => (result) => {
                var geo = new THREE.PlaneGeometry(100, 100, 32);
                var mat = new THREE.MeshBasicMaterial({map: result});
                this.addModelToCache(new THREE.Mesh(geo, mat), planeDescription.name);
                //console.log("SPECIAL", planeDescription.name, this.cache[planeDescription.name]);
                resolve();
            };

            let texture = new THREE.TextureLoader().load(
                resolveBase ? url(this.base, planeDescription.url) : planeDescription.url,
                finialize(true),
                undefined,
                finialize(false)
            );
        });
    }

    loadModels(config) {


        var self = this;

        var manager = new THREE.LoadingManager();
        var objLoader = new THREE.OBJLoader(manager);
        var xhrLoader = new THREE.XHRLoader(objLoader.manager);

        var createModel = (name, texture_name, model) => {


            model.castShadow = false;
            model.receiveShadow = false;
            model.name = name;
            model.traverse((child) => {
                if (child instanceof THREE.Mesh) {

                    if (texture_name) {
                        child.material = new THREE.MeshBasicMaterial({
                            //	map: self.cache["basic_white-hori"].clone(),
                            transparent: true,
                            side: name === 'fdrawer' ? THREE.DobuleSide : THREE.OneSide
                        });

                        if (texture_name == "cast_shadow") {
                            child.material.map = self.cache["cast_shadow"];
                        }

                        if (texture_name == "leg_texture") {
                            child.material.map = self.cache["leg_texture"];
                        }


                    }
                }
            });

            return model;
        };

        // TODO CROSS ORIGIN NOT NEEDED FOR NOW
        // loader.setCrossOrigin(objLoader.crossOrigin);

        return new Promise((resolve) => {

            xhrLoader.load('/r_static/ivy5.objx', (modelsData) => {

                var models = modelsData.split(TYLKO_BLADE_OF_SLAWEK);
                models.splice(0, 1);

                models.map((model) => {
                    if (!model) return;

                    let filename = model.match(/[\w\d-]+\.obj/)[0];
                    let cfg = config[filename];

                    if (cfg) {
                        if (cfg[0] instanceof Array) {
                            for (var j = 0; j < cfg.length; j++) {
                                let currentModel = createModel(cfg[j][0], cfg[j][1], objLoader.parse(model));
                                this.addModelToCache(currentModel);
                            }
                        } else {
                            let currentModel = createModel(cfg[0], cfg[1], objLoader.parse(model));
                            this.addModelToCache(currentModel);
                        }
                    }
                });

                resolve();

            }, null, null);
        });
    }

    loadImage() {
        return new Promise((resolve) => {
            var img = new Image();
            img.addEventListener('load', (e) => {
                resolve();
            });
        });
    }

    getElements() {
        return this.cache;
    }

    addOnLoadedEvent(mainCallback) {
        mainCallback();
    }
}

export {AsyncLoader};
