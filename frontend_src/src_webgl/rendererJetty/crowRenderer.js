import {
  Raycaster,
  Vector3,
} from 'three';
import { Power2 } from 'gsap';

import { customSettings } from '@src_webgl/tylkoCamera/tylko-camera-settings';
import { JettyRenderer } from '@src_webgl/rendererJetty/JettyRenderer';
import { initSceneRotation } from '@src_webgl/rendererJetty/sceneRotation';
import { throttle } from 'lodash';
import { handleHideStorageButtons } from '@src_vue/Configurators/FrontCrow/helpers/storageButtonsDOMInteractions';
import { Build3d } from '../build3dJetty/build3d';
import { findOnScene } from '../utils/heplers/findOnScene';


class CrowRenderer extends JettyRenderer {
  constructor(args) {
    const superArgs = Object.assign(args, { cameraSettings: customSettings.row });
    super(superArgs);
    this.activeRow = null;
    this.storageButtonsFirstExpand = true;

    // TODO: move to build3d
    this.rowHoverBoxes = new this.build3d.CreateRowHoverBoxes(
      this.scene,
      {
        color: 0xFF3C00,
        transparent: true,
        opacity: 0,
        depthWrite: false,
        visible: true,
      },
      this.build3d.walls.hoverBoxes,
    );

    this.easings = {
      openDoors: Power2.easeOut,
      openDrawers: Power2.easeOut,
      closeDoors: Power2.easeOut,
      closeDrawers: Power2.easeOut,
    };
    this.animationTime = 0.9;

    this.setupCanvasInteraction();
    this.setupListeners();
    this.setMobileDefaultSnap();
    this.mouseInStorageButtonsLogic = throttle(this.mouseInStorageButtonsLogic.bind(this), 100);
  }

  setupListeners() {
    if (!this.store.ui.isMobile) {
      this.container.addEventListener('mousedown', () => {
        this.dispatcher('ui/SET_IS_ORBIT_MOVING', true);
      },
      false);
      this.container.parentElement.addEventListener('mouseleave', () => {
        this.dispatcher('UPDATE_ACTIVE_ROW', null);
        this.activeRow = null;
      },
      false);
      document.body.addEventListener('mouseup', () => {
        setTimeout(() => {
          this.dispatcher('ui/SET_IS_ORBIT_MOVING', false);
        }, 450);
      }, false);
    }


    this.container.addEventListener('touchstart', () => {
      this.dispatcher('ui/SET_IS_ORBIT_MOVING', true);
    }, false);

    this.container.addEventListener('touchend', () => {
      this.dispatcher('ui/SET_IS_ORBIT_MOVING', false);
    }, false);

    this.setUpScreenshotShortcut();
  }

  // TODO: Move to build3d
  regenerateHoverBoxes(activeRow) {
    this.rowHoverBoxes.regenerate(this.identifierBoxes, this.width, this.depth, this.store);
    if (activeRow !== null) {
      const identifierBox = this.rowHoverBoxes.boxes.find(box => box.hoverBox.name.split(':')[1] === activeRow);
      if (identifierBox) identifierBox.hoverBox.material.opacity = 0.2;
    }
  }

  calculateStorageButtonPosition(type) {
    const box = type === 'top' ? this.rowHoverBoxes.topStorageHoverBox : this.rowHoverBoxes.bottomStorageHoverBox;
    if (!box) return false;
    const { x, y, z } = box.position;
    return this.convert3dToPixels(x, y, z, this.container, this.tylkoCamera.camera);
  }

  calculateCloudPosition(order) {
    const identifierBox = this.identifierBoxes.find((box, index) => index === Number(order));
    const {
      y1, y2, z1, z2,
    } = identifierBox;
    const x = Math.max(...this.identifierBoxes.map(i => i.x2));
    const y = y1 + ((y2 - y1) / 2);
    const z = (z1 + z2) / 2;
    return this.convert3dToPixels((x + 200), y, z, this.container, this.tylkoCamera.camera);
  }

  selectRow(index) {
    const { bottomStorage, topStorage } = this.store.configuration;
    if (index === null) return;
    this.deselectRow();
    const identifierBox = this.rowHoverBoxes.boxes[!bottomStorage ? index : index + 1];
    if (identifierBox) identifierBox.hoverBox.material.opacity = 0.2;
    if (!this.store.dimensions.show) {
      let indexDoorsForComponent;
      if (index === -1) {
        // bottom storage
        indexDoorsForComponent = 100;
      } else if (
      // top storage
        (topStorage && !bottomStorage && (Number(index) === this.rowHoverBoxes.boxes.length - 1)) || (topStorage && bottomStorage && (Number(index) === this.rowHoverBoxes.boxes.length - 2))
      ) {
        indexDoorsForComponent = 101;
      } else {
        indexDoorsForComponent = index;
      }
      this.openDoorAndDrawersForComponent(indexDoorsForComponent, 1);
    }
  }

  deselectRow() {
    // eslint-disable-next-line no-param-reassign,no-return-assign
    this.rowHoverBoxes.boxes.forEach(({ hoverBox }) => (hoverBox.material.opacity = 0));
    this.closeAllDoorsAndDrawers();
    this.activeRow = null;
  }

  setTabSpecificView(code) {
    if (this.currentView === 'shelf') {
      this.tylkoCamera.setShelfMode();
    } else {
      this.tylkoCamera.setComponentMode();
    }
    switch (code) {
    case 'depth':
      this.tylkoCamera.setViewFinal('deepSide');
      break;
    case 'style':
      this.tylkoCamera.setViewFinal('front');
      break;
    default:
      this.tylkoCamera.setViewFinal('side');
    }
  }

  setMobileDefaultSnap() {
    if (this.store.ui.isMobile) this.tylkoCamera.setViewFinal('side');
  }

  calculateTopStorageButtonPosition() {
    this.dispatcher('ui/UPDATE_COORDS_STORAGE_BUTTON_TOP', this.calculateStorageButtonPosition('top'));
  }

  calculateBottomStorageButtonPosition() {
    this.dispatcher('ui/UPDATE_COORDS_STORAGE_BUTTON_BOTTOM', this.calculateStorageButtonPosition('bottom'));
  }

  mouseInStorageButtonsLogic(res, storageRes, maskRes) {
    const { configuration: { topStorage, bottomStorage }, ui: { isOrbitMoving } } = this.store;
    if (!topStorage && !isOrbitMoving) {
      this.calculateTopStorageButtonPosition();
      this.dispatcher('ui/SHOW_STORAGE_BUTTON_TOP');
    }
    if (!bottomStorage && !isOrbitMoving) {
      this.calculateBottomStorageButtonPosition();
      this.dispatcher('ui/SHOW_STORAGE_BUTTON_BOTTOM');
    }

    if (!res && !storageRes && !maskRes) {
      this.dispatcher('ui/EXPAND_STORAGE_BUTTON_BOTTOM', false);
      this.dispatcher('ui/EXPAND_STORAGE_BUTTON_TOP', false);
    } else if (res || storageRes || maskRes) {
      this.dispatcher('ui/EXPAND_STORAGE_BUTTON_BOTTOM', true);
      this.dispatcher('ui/EXPAND_STORAGE_BUTTON_TOP', true);
    }
  }

  setupCanvasInteraction() {
    let selectedObject = null;
    const raycaster = new Raycaster();
    const mouseVector = new Vector3();
    const getIntersects = (x, y) => {
      const vecX = (x / this.container.offsetWidth) * 2 - 1;
      const vecY = -(y / this.container.offsetHeight) * 2 + 1;
      mouseVector.set(vecX, vecY, 0);
      raycaster.setFromCamera(mouseVector, this.tylkoCamera.camera);
      return raycaster.intersectObject(this.scene, true);
    };
    if (this.store.ui.isMobile) {
      this.container.addEventListener('click', event => {
        event.preventDefault();
        const availableTabs = {
          bottomStorage: 6,
          topStorage: 7,
          rows: 8,
          doors: 9,
          drawers: 10,
        };
        if (!Object.values(availableTabs).includes(this.store.ui.activeTab)) return;

        if (this.activeRow === null) this.deselectRow();
        const intersects = getIntersects(event.layerX, event.layerY);
        if (intersects.length > 0) {
          const res = findOnScene(intersects, 'rowHoverBox');
          const maskRes = findOnScene(intersects, 'mask_rowHoverBox');

          if (res && res.object) {
            if (this.store.ui.adjustSignifier) {
              this.dispatcher('ui/HIDE_ADJUST_SIGNIFIER');
            }
            [, selectedObject] = res.object.name.split(':');
            if (selectedObject !== this.activeRow) {
              this.dispatcher('UPDATE_ACTIVE_ELEMENT', Number(selectedObject));
            }
            this.activeRow = selectedObject;
          } else if (this.activeRow !== null) {
            if (maskRes && maskRes.object) {
              const [, selectedMask] = maskRes.object.name.split(':');
              if (selectedMask !== this.activeRow) {
                this.dispatcher('UPDATE_ACTIVE_ELEMENT', Number(selectedMask));
                this.activeRow = selectedMask;
              }
            }
            this.closeAllDoorsAndDrawers();
          }
        }
      }, false);
    } else {
      this.container.addEventListener('mousemove', event => {
        event.preventDefault();
        if (this.activeRow === null) {
          this.deselectRow();
        }
        const intersects = getIntersects(event.layerX, event.layerY);
        if (intersects.length > 0) {
          const res = findOnScene(intersects, 'rowHoverBox');
          const maskRes = findOnScene(intersects, 'mask_rowHoverBox');
          const storageRes = findOnScene(intersects, 'storageMask_rowHoverBox');
          this.mouseInStorageButtonsLogic(res, storageRes, maskRes);


          if (res && res.object) {
            if (this.store.ui.adjustSignifier) {
              this.dispatcher('ui/HIDE_ADJUST_SIGNIFIER');
            }
            [, selectedObject] = res.object.name.split(':');
            if (selectedObject !== this.activeRow) {
              const cloudPosition = this.calculateCloudPosition(selectedObject);
              this.dispatcher('UPDATE_ACTIVE_ELEMENT', selectedObject);
              this.dispatcher('ui/UPDATE_CLOUD_POSITION', cloudPosition);
            }
            this.activeRow = selectedObject;
          } else if (this.activeRow !== null) {
            if (maskRes && maskRes.object) {
              const [, selectedMask] = maskRes.object.name.split(':');
              if (selectedMask !== this.activeRow) {
                const cloudPosition = this.calculateCloudPosition(selectedMask);
                this.dispatcher('UPDATE_ACTIVE_ELEMENT', selectedMask);
                this.dispatcher('ui/UPDATE_CLOUD_POSITION', cloudPosition);
                this.activeRow = selectedMask;
              }
            } else {
              this.dispatcher('UPDATE_ACTIVE_ROW', null);
              this.activeRow = null;
            }
            this.closeAllDoorsAndDrawers();
          }
        }
      }, false);
      this.container.addEventListener('mouseleave', ({ relatedTarget }) => {
        const { isStorageButton, isCloud } = handleHideStorageButtons(relatedTarget);
        if (isCloud) {
          setTimeout(() => {
            this.dispatcher('ui/EXPAND_STORAGE_BUTTON_BOTTOM', false);
            this.dispatcher('ui/EXPAND_STORAGE_BUTTON_TOP', false);
          }, 100);
          return;
        }

        if (isStorageButton) return;
        setTimeout(() => {
          this.dispatcher('ui/HIDE_STORAGE_BUTTONS');
        }, 100);
      });
    }
    if (window.cro_crow_orbit_signifier) this.sceneRotation = initSceneRotation({ scene: this.scene, isMobile: this.store.ui.isMobile });
  }

  updateGeometry(geom, {
    width, height, depth, cameraUpdate,
  }) {
    if (height !== this.height) {
      this.rowHoverBoxes.regenerate(geom.identifierBoxes, width, depth, this.store);
    }
    this.rowHoverBoxes.updateWidth(this.width, width);
    this.rowHoverBoxes.updateDepth(this.depth, depth);
    this.width = width;
    this.height = height;
    this.depth = depth;
    this.geom = geom;
    const horizontals = [...this.geom.horizontals.map(h => h.y1)];


    this.build3d.setScene(this.scene);
    this.build3d.rebuildWallsFromJson(geom, this.scene, { width, height, depth });
    this.updateItems(geom);
    this.rendererProxy.bake({ width, height, depth });
    this.identifierBoxes = geom.identifierBoxes;
    this.renderer.render(this.scene, this.tylkoCamera.camera);
    if (!cameraUpdate) return;
    this.cameraSettings.geometryMargins.top = this.dynamicCameraMargin(this.cameraSettings, height);
    if (this.currentView === 'shelf') {
      this.tylkoCamera.updateGeometry(
        { x: -width / 2, y: 0, z: 0 },
        { x: width / 2, y: Math.max(...horizontals), z: 410 },
        false,
      );
    }
  }
}

async function initCrowRenderer(args) {
  const {
    shelf_type,
    material,
  } = args.store.commonFurniture.item;

  const build3d = new Build3d({
    elements_in: args.loader.getElements(),
    context_in: {},
    isMobile: window.cstm.is_mobile,
    initialShelfType: shelf_type,
    initialShelfMaterial: material,
  }, true);
  return new CrowRenderer({ ...args, build3d });
}

export { initCrowRenderer as initRenderer };
