import {
  Scene, Vector3, Group, PerspectiveCamera,
} from 'three';

import OrbitControls from 'orbit-controls-es6';
import { Power1, Power3, TweenLite } from 'gsap';
import smoothstep from 'smoothstep';

import { PMREMGenerator } from 'three/src/extras/PMREMGenerator';
import { RGBELoader } from '../@libs/RGBELoader';

import { placeItems, toggleItems } from '../@libs/lib-items';

import { getDefinitionForMaterial } from '../presets/materials';
import { Renderer as ShadowRenderer } from './shadowRenderer';
import { TylkoCamera } from '../tylkoCamera/tylko-camera';
import { convert3dToPixels } from '../utils/heplers/convert3dToPixels';

import { isAbTestSetAsOk } from './helpers/abtest';
import { getCameraTopOffsetForItems } from './helpers/cameraTopOffsetForItems';

// temp and sync
const shouldRenderItems = isAbTestSetAsOk('feature_items');

class JettyRenderer {
  constructor({
    container,
    width,
    height,
    loader,
    geom,
    shelfType = 0,
    shelfColor = 0,
    viewStyle = 'new',
    dispatcher,
    store,
    build3d,
    cameraSettings,
  }) {
    this.cameraSettings = cameraSettings;
    this.container = container;
    this.width = width;
    this.height = height;
    this.depth = null;
    this.loader = loader;
    this.viewStyle = viewStyle;
    this.elements = this.loader.getElements();
    this.geom = geom;
    this.shelfType = shelfType;
    this.shelfColor = shelfColor;
    this.scene = new Scene();
    this.hdrCubeRenderTarget = null;

    window.scene = this.scene; // ? - why? pawel
    this.build3d = build3d;
    this.currentView = 'shelf';
    this.dispatcher = dispatcher;
    this.store = store;
    this.shouldRender = true;
    this.initialRender = true;
    this.isOrbitActive = false;
    this.isAnimating = false;
    this.activeRow = null;
    this.convert3dToPixels = convert3dToPixels;
    this.getDefinitionForMaterial = getDefinitionForMaterial;

    this.createTylkoCamera();
    this.initBuild3d();
    this.createShadowsRenderer();

    this.renderLoop();
    this.easings = {
      openDoors: Power1.easeIn,
      openDrawers: Power1.easeIn,
      closeDoors: Power3.easeOut,
      closeDrawers: Power3.easeOut,
    };
    this.animationTime = 0.5;

    if (shouldRenderItems) {
      let productCategory;
      if (window.cstm) {
        productCategory = window.cstm.item.category;
      }
      const cameraTopOffsetForItems = getCameraTopOffsetForItems(productCategory);
      if (cameraTopOffsetForItems !== null) {
        this.tylkoCamera.setOffsetTop(cameraTopOffsetForItems);
      }

      const pmremGenerator = new PMREMGenerator(this.renderer);
      const envLoader = new RGBELoader();

      envLoader.load('/r_static/ios/light.hdr', hdrEquiRect => {
        this.hdrCubeRenderTarget = pmremGenerator.fromEquirectangular(hdrEquiRect);
        pmremGenerator.compileCubemapShader();

        this.updateItems = jettyGeometry => {
          jettyGeometry.category = productCategory;
          placeItems(
            this.getItemsGroup(),
            jettyGeometry,
            this.hdrCubeRenderTarget ? this.hdrCubeRenderTarget.texture : null,
          );
        };

        this.updateItems(this.lastGeo);
        this.renderer.render(this.scene, this.tylkoCamera.camera);
      });
    }
  }

  updateItems(lastGeo) {
    this.lastGeo = lastGeo;
  }

  getItemsGroup() {
    if (this.itemsGroup) {
      return this.itemsGroup;
    } else {
      const itemsGroup = new Group();
      this.itemsGroup = itemsGroup;
      this.scene.add(itemsGroup);
      return itemsGroup;
    }
  }

  compartmentsToScreen(object) {
    // TODO: trzeba spiąć z zoomami itp
    const canvas = this.container;
    const w = canvas.offsetWidth * 1.65;
    const h = canvas.offsetHeight * 1.6;
    const point = new Vector3();
    point.set(object.x, object.y, object.z);
    point.applyAxisAngle(new Vector3(0, 1, 0), 0);
    // ndc pinhole

    point.project(this.tylkoCamera);
    point.x = Math.round((point.x + 0.6) * w / 2);
    point.y = Math.round((-point.y + 0.882) * h / 2);
    point.z = 0;

    return { value: object.value, point };
  }

  initBuild3d() {
    this.build3d.setDesignerMode(1);
    this.build3d.init3d();
    this.build3d.createFacePlaneForRaycasting();
    this.build3d.setScene(this.scene);
    this.build3d.setShelfType(this.shelfType);
    this.setColor(this.shelfColor, this.shelfType);
    this.rowHoverBoxes = new this.build3d.CreateRowHoverBoxes(
      this.scene,
      {
        color: 0xff3c00,
        transparent: true,
        opacity: 0,
        depthWrite: false,
        visible: true,
      },
      this.build3d.walls.hoverBoxes,
    );
  }

  toggleItemsVisibility(booleanState) {
    toggleItems(booleanState);
  }

  onWindowResize(width, height) {
    this.tylkoCamera.camera.aspect = width / height;
    this.tylkoCamera.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height, false);
  }


  createSimpleCamera() {
    this.simpleCamera = new PerspectiveCamera(this.cameraSettings.fov || 20, this.width / this.height, 1, 10000);
    this.simpleCamera.position.setZ(9000);
    this.simpleCamera.name = 'simpleCamera';
    this.scene.add(this.simpleCamera);
    window.simpleCamera = this.simpleCamera;
  }

  createTylkoCamera() {
    this.createSimpleCamera();
    this.tylkoCamera = new TylkoCamera({
      view: this.container,
      settings: this.cameraSettings,
    });
    // Ustawienia - Animacja + Offset
    this.tylkoCamera.noTransitionAnimation = false;

    this.tylkoCamera.updateAspect(this.width / this.height);

    this.tylkoCamera.controls.addEventListener('render', () => {
      this.shouldRender = true;
      // console.log('render')
      this.dispatcher('interactions/SET_IS_CAMERA_ANIMATING', true);
    });

    this.tylkoCamera.controls.addEventListener('change', () => {
      this.renderer.render(this.scene, this.tylkoCamera.camera);

      // console.log(">> CPLUS - Change")
    });

    this.tylkoCamera.controls.addEventListener('animationEnd', () => {
      // console.log(">>>>>>>>>>>>> ANIMATION EN EVENT >>>>>>>>>>>>>>>>>",)
      setTimeout(() => {
        if (this.updateCoordinatesForButtons) this.updateCoordinatesForButtons();
        this.dispatcher('interactions/SET_IS_CAMERA_ANIMATING', false);
      }, this.timeoutTime);
    });
  }

  getScene() {
    return this.scene;
  }

  getCamera() {
    return this.tylkoCamera;
  }

  getContainer() {
    return this.container;
  }

  createControls() {
    this.controls = new OrbitControls(this.simpleCamera, this.container);
    this.controls.enabled = true;
    this.controls.enableZoom = true;
    this.controls.enablePan = true;
    this.controls.maxDistance = 8000;
    this.controls.minDistance = 0;
  }

  // eslint-disable-next-line class-methods-use-this
  createAndClickOffscreenLink(blob) {
    const a = document.createElement('a');
    const date = new Date();
    const utc = date.toJSON().slice(0, 10).replace(/-/g, '/');
    document.body.appendChild(a);
    a.style.display = 'none';
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = `Shelf_screenshot_(${utc}-${date.getHours()}:${date.getMinutes()})`;
    a.click();
  }

  setUpScreenshotShortcut() {
    let keysPressed = [];
    document.onkeydown = event => {
      if (!keysPressed.includes(event.key)) {
        keysPressed.push(event.key);
      }
      const shortcutIsActive = keysPressed.length === 3 && keysPressed.includes('z') && keysPressed.includes('x') && keysPressed.includes('p');
      if (shortcutIsActive) {
        this.renderer.render(this.scene, this.tylkoCamera.camera);
        this.renderer.domElement.toBlob(blob => this.createAndClickOffscreenLink(blob));
      }
    };
    document.onkeyup = () => {
      keysPressed = [];
    };
  }

  createShadowsRenderer() {
    const backgroundColor = this.cameraSettings && this.cameraSettings.backgroundColor ? this.cameraSettings.backgroundColor : false;
    const rendererProxy = new ShadowRenderer(
      {
        devicePixelRatio: window.devicePixelRatio || 2,
        renderScene: true,
        textures: this.loader.getElements(),
        container: this.container,
        mobileMode: window.is_mobile_loaded,
        backgroundColor,
      },
      this.simpleCamera,
      this.build3d,
    );
    this.rendererProxy = rendererProxy;
    this.renderer = rendererProxy.threeRenderer;
    this.renderer.setClearColor(this.cameraSettings.backgroundColor || 0xedf0f0, 1);
  }

  renderLoop() {
    if (this.shouldRender) {
      // console.log(">> CPLUS - ShouldRender");
      this.shouldRender = false;
      this.tylkoCamera.controls.update();
    }
    this.renderer.render(this.scene, this.tylkoCamera.camera);

    window.requestAnimationFrame(this.renderLoop.bind(this));
  }

  setColor(color, shelfType) {
    this.shelfColor = color;
    this.shelfType = shelfType;
    const [, colorName] = getDefinitionForMaterial(color, shelfType);
    Promise.all(this.loader.loadColorTextures(colorName, this.shelfType)).then(() => {
      this.build3d.setMaterialColor({ color, shelf_type: shelfType, skipTracking: false });
      if (this.initialRender) {
        const event = new CustomEvent('loadCanvas');
        document.getElementById('conf').dispatchEvent(event);
        this.initialRender = false;
      }
    });
  }

  getCoordinatesForButtons() {
    const mobileCoords = [];
    const desktopCoords = [];
    const buttonsArray = this.scene.children.filter(item => item.name.split(':')[0] === 'button');
    const hoversArray = this.scene.children.filter(item => item.name.split(':')[0] === 'hoverBox');
    const tempV = new Vector3();
    const calculateProd = item => {
      item.updateMatrixWorld(true, false);
      item.getWorldPosition(tempV);
      tempV.project(this.tylkoCamera.camera);
      const x = (tempV.x * 0.5 + 0.5) * this.container.clientWidth;
      const y = (tempV.y * -0.5 + 0.5) * this.container.clientHeight;
      return { x, y };
    };
    buttonsArray.forEach(item => {
      desktopCoords.push(calculateProd(item));
    });

    hoversArray.forEach(item => {
      mobileCoords.push(calculateProd(item));
    });
    return {
      mobile: mobileCoords,
      desktop: desktopCoords,
    };
  }

  // eslint-disable-next-line class-methods-use-this
  dynamicCameraMargin(currrentCameraSettings, height) {
    const marginTopDifference = currrentCameraSettings.marginBottomRange.max - currrentCameraSettings.marginBottomRange.min;
    const marginBottomFactor = smoothstep(
      currrentCameraSettings.furnitureHeight.min,
      currrentCameraSettings.furnitureHeight.max,
      height,
    );
    const marginBottomValueToSet = marginTopDifference * marginBottomFactor + currrentCameraSettings.marginBottomRange.min;
    return marginBottomValueToSet;
  }

  setComponentView(geom, activeComponent, isMobile) {
    if (!isMobile) {
      this.tylkoCamera.setViewFinal('frontTripleSnap');
      return;
    }
    this.currentView = 'component';
    const component = geom.components.find(obj => obj.m_config_id == activeComponent);
    // console.log('>> CPLUS - setComponentView', geom, activeComponent, component);
    this.tylkoCamera.setComponentViewFinal(
      { x: component.bounding_box.x1, y: component.bounding_box.y1, z: component.bounding_box.z1 },
      { x: component.bounding_box.x2, y: component.bounding_box.y2, z: component.bounding_box.z2 },
    );
    this.renderer.render(this.scene, this.simpleCamera);
  }

  setShelfView(geom, isMobile, tripleSnap = true) {
    if (!isMobile) return;
    // console.log('>> CPLUS - setShelfView', geom);
    this.currentView = 'shelf';
    this.tylkoCamera.setShelfViewFinal(
      { x: -geom.width / 2, y: 0, z: 0 },
      { x: geom.width / 2, y: geom.height, z: 410 },
      false,
      tripleSnap,
    );
    this.renderer.render(this.scene, this.simpleCamera);
  }

  openDoorAndDrawersForComponent(id, angle) {
    const drawersToOpen = this.scene.children.filter(res => res.name === `drawer_group:${id}`);
    const doorsToOpen = this.scene.children.filter(
      res => res.name === `door_group:${id}:0` || res.name === `door_group:${id}:1`,
    );

    this.openDrawers(drawersToOpen, angle);
    this.openDoors(doorsToOpen, angle);

    const drawersToClose = this.scene.children.filter(
      res => res.name !== `drawer_group:${id}` && res.name.split(':')[0] === 'drawer_group',
    );
    const doorsToClose = this.scene.children.filter(
      res => res.name !== `door_group:${id}:0` && res.name !== `door_group:${id}:1` && res.name.split(':')[0] === 'door_group',
    );

    this.closeDrawers(drawersToClose);
    this.closeDoors(doorsToClose);
  }

  openDoors(doorsToOpen, angle) {
    doorsToOpen.forEach(i => {
      const rotationD = parseInt(i.name.split(':')[2], 10) ? -0.7 : 0.7;
      if (i.rotation.y === 0.7 || i.rotation.y === -0.7 || i.rotation.y >= 0.7 * angle || i.rotation.y <= -0.7 * angle) return;
      const target = { x: i.rotation.x, y: i.rotation.y + rotationD * angle, z: i.rotation.z };
      TweenLite.to(i.rotation, this.animationTime, {
        ease: this.easings.openDoors,
        x: target.x,
        y: target.y,
        z: target.z,
      });
    });
  }

  openDrawers(drawersToOpen, angle) {
    const animationOffset = (this.depth - 60) * angle;
    drawersToOpen.forEach(i => {
      const target = { x: i.position.x, y: i.position.y, z: this.depth - 8 + animationOffset };
      TweenLite.to(i.position, this.animationTime, {
        ease: this.easings.openDrawers,
        x: target.x,
        y: target.y,
        z: target.z,
      });
    });
  }

  closeDoors(doorsToClose) {
    doorsToClose.forEach(i => {
      if (i.rotation.y === 0) return;
      const target = { x: i.rotation.x, y: 0, z: i.rotation.z };
      TweenLite.to(i.rotation, this.animationTime, {
        ease: this.easings.closeDoors,
        x: target.x,
        y: target.y,
        z: target.z,
      });
    });
  }

  closeDrawers(drawersToClose) {
    drawersToClose.forEach(i => {
      if (i.position.z < this.depth || i.position.z <= 0) return;
      const target = { x: i.position.x, y: i.position.y, z: this.depth - 8 };
      TweenLite.to(i.position, this.animationTime, {
        ease: this.easings.closeDoors,
        x: target.x,
        y: target.y,
        z: target.z,
      });
    });
  }

  openAllDoorsAndDrawers() {
    const drawersToOpen = this.scene.children.filter(res => res.name.split(':')[0] === 'drawer_group');
    const doorsToOpen = this.scene.children.filter(res => res.name.split(':')[0] === 'door_group');

    this.openDrawers(drawersToOpen, 1);
    this.openDoors(doorsToOpen, 1);
  }

  closeAllDoorsAndDrawers() {
    const drawersToClose = this.scene.children.filter(res => res.name.split(':')[0] === 'drawer_group');
    const doorsToClose = this.scene.children.filter(res => res.name.split(':')[0] === 'door_group');

    this.closeDrawers(drawersToClose);
    this.closeDoors(doorsToClose);
  }
}

export { JettyRenderer };
