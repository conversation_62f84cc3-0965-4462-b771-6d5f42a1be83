/* eslint-disable no-underscore-dangle */

import * as THREE from 'three';
import { Canvas } from './renderer/utils/factory/canvas';
import { Wall } from './renderer/scene/wall';
import { Bakery } from './renderer/utils/bakery/bakery';
import { softShadowsTexturePass } from './renderer/methods/raytrace/build';
import { ShadowWallA, ShadowWallB, ShadowFloor } from './renderer/cachedMobileShadows';
import {
  settingsScene,
  settingsShadowFloor,
  settingsShadowOne,
  settingsShadowTwo,
  world,
} from './renderer/settings-production';

const DEBUG = false;
const round2power = x => Math.pow(2, Math.ceil(Math.log(x) / Math.log(2)));

class TylkoRenderer {
  constructor({
    antialias = true,
    devicePixelRatio = 2,
    textures,
    renderScene = false,
    mobileMode = false,
    downsamplingFactor = 30,
    container,
    backgroundColor,
  },
  cameraProvider,
  geometryProvider) {
    this.scene = null;
    this.width = 0;
    this.height = 0;
    this.depth = 0;
    this.backgroundColor = backgroundColor;
    if (DEBUG) console.log('Tylko Renderer Initilised', renderScene, mobileMode);

    this.mobileMode = true;

    if (textures) {
      const firstWall = textures.s3d_wall;
      const secondWall = textures.s3d_wall2;
      const floor = textures.s3d_floor;

      if (firstWall && secondWall && floor) {
        this.images = [
          firstWall.image,
          secondWall.image,
          floor.image,
        ];
      } else {
        this.prepareImagesFromBase64();
      }
    } else {
      this.prepareImagesFromBase64();
    }

    this.quality = downsamplingFactor;
    this.camera = cameraProvider;
    this.geo = geometryProvider;

    // real render vs rozciaganie
    this.mobileMode = mobileMode;
    // this.mobileMode = true;

    this.threeRenderer = new THREE.WebGLRenderer({
      antialias,
      devicePixelRatio,
      // preserveDrawingBuffer: true,
      logarithmicDepthBuffer: true,
      alpha: true,
      canvas: container,
      sortObjects: true,
      powerPreference: 'high-performance',
    });

    this.additionalShadowData = null;

    // this.threeRenderer.sortObjects = false;

    switch (renderScene) {
    case true:
      this.compose();
      this.bakePass = true;
      break;
    default:
      this.bakePass = false;
      // this.bakePass = false;
      break;
    }

    return this;
  }

  prepareImagesFromBase64() {
    const imageFromBase64 = src => new Promise(resolve => {
      const image = new Image();
      image.addEventListener('load', () => {
        resolve(image);
      });
      image.crossOrigin = 'Anonymous';
      image.src = src;
    });

    const firstWallImage = imageFromBase64(ShadowWallA);
    const secondWallImage = imageFromBase64(ShadowWallB);
    const floorImage = imageFromBase64(ShadowFloor);

    return Promise.all([
      firstWallImage,
      secondWallImage,
      floorImage,
    ]).then(images => {
      this.images = images;
    });
  }

  isDeskCollectiontype() {
    return this.geo.points.configurator_params.additional_parameters.collection_type === 'Desk';
  }

  compose() {
    // Globals

    const shadowScaleFactor = this.quality;
    const wallWidth = 6000 * world.scale;
    const wallHeight = 4000 * world.scale;
    this.scene = this.geo.getScene();

    // Shadows generators

    const shadowSized = {
      width: wallWidth / shadowScaleFactor,
      height: wallHeight / shadowScaleFactor,
    };

    shadowSized.width = round2power(shadowSized.width);
    shadowSized.height = round2power(shadowSized.height);

    this._shadowSized = shadowSized;


    if (!this.mobileMode) {
      const { pass: firstShadow, canvas: firstShadowCanvas } = this.spwanShadowRenderer(shadowSized);
      const { pass: secondShadow, canvas: secondShadowCanvas } = this.spwanShadowRenderer(shadowSized);
      const { pass: floorShadow, canvas: floorShadowCanvas } = this.spwanShadowRenderer({ top: true, ...shadowSized });

      const { pass: rightFloorShadow, canvas: rightFloorShadowCanvas } = this.spwanShadowRenderer({ top: true, ...shadowSized });
      const { pass: leftFloorShadow, canvas: leftFloorShadowCanvas } = this.spwanShadowRenderer({ top: true, ...shadowSized });

      this.firstShadow = firstShadow;
      this.firstShadowCanvas = firstShadowCanvas;

      this.secondShadow = secondShadow;
      this.secondShadowCanvas = secondShadowCanvas;

      this.floorShadow = floorShadow;
      this.floorShadowCanvas = floorShadowCanvas;

      this.rightFloorShadow = rightFloorShadow;
      this.rightFloorShadowCanvas = rightFloorShadowCanvas;

      this.leftFloorShadow = leftFloorShadow;
      this.leftFloorShadowCanvas = leftFloorShadowCanvas;
    } else {
      const { element: firstShadowCanvasFast } = new Canvas(shadowSized);
      const { element: secondShadowCanvasFast } = new Canvas(shadowSized);
      const { element: floorShadowCanvasFast } = new Canvas(shadowSized);

      this.firstShadowCanvas = firstShadowCanvasFast;
      this.secondShadowCanvas = secondShadowCanvasFast;
      this.floorShadowCanvas = floorShadowCanvasFast;
    }

    const { element: cachedWallTexture } = new Canvas(shadowSized);
    const { element: cachedFloorTexture } = new Canvas(shadowSized);

    this.cashedFloorTextureCanvas = cachedFloorTexture;
    this.cachedWallTextureCanvas = cachedWallTexture;

    // Wall

    const wall = new Wall({
      planeWidth: wallWidth,
      planeHeight: wallHeight,
      scene: this.scene,
      source: this.cachedWallTextureCanvas,
    });

    const floor = new Wall({
      planeWidth: wallWidth,
      planeHeight: wallHeight,
      scene: this.scene,
      source: this.cashedFloorTextureCanvas,
      isFloor: true,
    });

    this.floor = floor;
    this.wall = wall;

    if (this.mobileMode) {
      const resetCanvas = canvas => {
        const el = document.createElement('canvas');
        el.width = canvas.width;
        el.height = canvas.height;
        return el;
      };

      this.firstShadowCanvas = resetCanvas(this.firstShadowCanvas);
      this.secondShadowCanvas = resetCanvas(this.secondShadowCanvas);
      this.floorShadowCanvas = resetCanvas(this.floorShadowCanvas);
    }
  }

  setShelfDim({
    width,
    height,
    depth,
  }) {
    this.width = width;
    this.height = height;
    this.depth = depth;
  }

  getAlternativeDim(shadowType) {
    if (this.additionalShadowData) {
      switch (shadowType) {
      case 'legroom-left':
        return this.getDeskShdowDim('left');
      case 'legroom-right':
        return this.getDeskShdowDim('right');
      default:
        return this.getShelfDim();
      }
    }
  }

  getShelfDim() {
    return { width: this.width, height: this.height, depth: this.depth };
  }

  beforeBake(useRealRenderer, dimensions) {
    if (DEBUG) console.log('BEFORE BAKE', dimensions, useRealRenderer);
    this.setShelfDim(dimensions);

    if (useRealRenderer) {
      this.firstShadow.render({ settings: settingsShadowOne });
      this.secondShadow.render({ settings: settingsShadowTwo });
      this.floorShadow.render({ settings: settingsShadowFloor });

      if (this.isDeskCollectiontype()) {
        this.rightFloorShadow.render({ settings: { ...settingsShadowFloor, partialShadow: 'legroom-right' } });
        this.leftFloorShadow.render({ settings: { ...settingsShadowFloor, partialShadow: 'legroom-left' } });
      }
      return;
    }

    const drawImage = (target, source, x, y, w, h) => {
      const ctx = target.getContext('2d');
      if (ctx) ctx.drawImage(source, x, y, w, h);
    };

    const calculateImagePositionAndSize = (projectedWidth, projectedHeight, heightScalar = 2) => {
      const sx = 755 / (projectedWidth / 2);
      const sy = 362 / (projectedHeight / heightScalar);

      // eslint-disable-next-line no-underscore-dangle
      const width = this._shadowSized.width / sx;
      const height = this._shadowSized.height / sy;

      const x = (this._shadowSized.width - width) / 2;
      const y = -(height - this._shadowSized.height);

      return {
        x, y, width, height,
      };
    };

    if (this.images) {
      const wallProjection = calculateImagePositionAndSize(dimensions.width, dimensions.height);
      const floorProjection = calculateImagePositionAndSize(dimensions.width, dimensions.depth, 1);

      drawImage(this.firstShadowCanvas, this.images[0], wallProjection.x, wallProjection.y, wallProjection.width, wallProjection.height);
      drawImage(this.secondShadowCanvas, this.images[1], wallProjection.x, wallProjection.y, wallProjection.width, wallProjection.height);
      drawImage(this.floorShadowCanvas, this.images[2], floorProjection.x, floorProjection.y, floorProjection.width, floorProjection.height);
    }
  }

  deskLegroomShadowDataPreparation() {
    const geom = this.geo.points;
    const includesDeskBeams = component => geom.desk_beams.some(deskBeam => deskBeam.m_config_id === component.m_config_id);
    const legroomIdx = geom.components.findIndex(includesDeskBeams);
    const shadowLegsSpace = geom.components[legroomIdx];

    const restColumnsCount = geom.components.length - 1;
    const restOfShelf = geom.width - shadowLegsSpace.width;
    const columnWidth = restOfShelf / restColumnsCount;

    const numberColumnsInLeft = geom.components.filter((c, idx) => idx < legroomIdx).length;
    const numberColumnsInRight = geom.components.filter((c, idx) => idx > legroomIdx).length;

    return {
      shadowLegsSpace, columnWidth, numberColumnsInLeft, numberColumnsInRight,
    };
  }

  getDeskShdowDim(shadowType) {
    const { columnWidth, numberColumnsInLeft, numberColumnsInRight } = this.additionalShadowData;
    return {
      width: ((shadowType === 'left' ? numberColumnsInLeft : numberColumnsInRight) * columnWidth) + 18,
      height: this.geo.points.height,
      depth: this.geo.points.depth * 0.95,
    };
  }

  deskShadowProjection() {
    const {
      shadowLegsSpace, columnWidth, numberColumnsInLeft, numberColumnsInRight,
    } = this.additionalShadowData;

    const rightShadowPosition = shadowLegsSpace.width / 2 + (columnWidth * (numberColumnsInLeft / 2));
    const rightShadowProjected = (rightShadowPosition / this.floor.width) * this._shadowSized.width;

    const leftShadowPosition = shadowLegsSpace.width / 2 + (columnWidth * (numberColumnsInRight / 2));
    const leftShadowProjected = (leftShadowPosition / this.floor.width) * this._shadowSized.width;

    return { rightShadowProjected, leftShadowProjected };
  }


  bake(dimensions) {
    if (!this.bakePass) return;
    if (this.isDeskCollectiontype()) this.additionalShadowData = this.deskLegroomShadowDataPreparation();
    this.beforeBake(!this.mobileMode, dimensions);

    if (DEBUG) console.time('bake');

    if (this.isDeskCollectiontype() && !this.mobileMode) {
      const { rightShadowProjected, leftShadowProjected } = this.deskShadowProjection();
      Bakery(this.cashedFloorTextureCanvas, {
        ...settingsScene.floorColor,
        a: settingsScene.floorColorAlpha,
      })
        .mix(this.leftFloorShadowCanvas, {
          opacity: settingsShadowFloor.alpha,
          blendMode: settingsShadowFloor.mixOperation,
        }, { x: -leftShadowProjected, y: 0 })
        .mix(this.rightFloorShadowCanvas, {
          opacity: settingsShadowFloor.alpha,
          blendMode: settingsShadowFloor.mixOperation,
        }, { x: rightShadowProjected, y: 0 })
        .gradientClip();
    } else {
      Bakery(this.cashedFloorTextureCanvas, {
        ...settingsScene.floorColor,
        a: settingsScene.floorColorAlpha,
      })
        .mix(this.floorShadowCanvas, {
          opacity: settingsShadowFloor.alpha,
          blendMode: settingsShadowFloor.mixOperation,
        })
        .gradientClip();
    }

    Bakery(this.cachedWallTextureCanvas, {
      ...settingsScene.backgroundColor,
      a: settingsScene.backgroundColorAlpha,
    })
      .mix(this.firstShadowCanvas, {
        opacity: settingsShadowOne.alpha,
        blendMode: settingsShadowOne.mixOperation,
      })
      .mix(this.secondShadowCanvas, {
        opacity: settingsShadowTwo.alpha,
        blendMode: settingsShadowTwo.mixOperation,
      })
      .gradientClip();

    this.wall.updateMaterial();
    this.wall.wallMaterial.opacity = settingsScene.shadowPlaneOpacity;

    this.floor.updateMaterial();
    this.floor.wallMaterial.opacity = settingsScene.floorPlaneOpacity;

    if (DEBUG) console.timeEnd('bake');
    window.firstShadowCanvas = this.firstShadowCanvas;
    window.secondShadowCanvas = this.secondShadowCanvas;
    window.floorShadowCanvas = this.floorShadowCanvas;
    window.rightFloorShadowCanvas = this.rightFloorShadowCanvas;
    window.leftFloorShadowCanvas = this.leftFloorShadowCanvas;

    if (this.backgroundColor) {
      this.scene.background = new THREE.Color(this.backgroundColor);
    }
  }

  spwanShadowRenderer({ width, height, top }) {
    const { element } = new Canvas({
      width,
      height,
      transparent: true,
    });
    return {
      pass: softShadowsTexturePass(this, element, top),
      canvas: element,
    };
  }

  setBackground({ color, transparency, renderScene }) {
    if (renderScene) this.scene.background = new THREE.Color(color);
    this.threeRenderer.setClearColor(color, transparency);
  }
}

export {
  // eslint-disable-next-line import/prefer-default-export
  TylkoRenderer as Renderer,
};
