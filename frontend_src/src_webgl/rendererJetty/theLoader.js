import flattenDeep from 'lodash/flattenDeep';
import { getDefinitionForMaterial } from '../presets/materials';
import { AsyncLoader } from './asyncLoader';
import { ObjectsConfig } from './modelConfig';

function theLoader({ materialId = 0, shelfType = 0, digitalProductVersion = 0 }) {
    const loader = new AsyncLoader({ digitalProductVersion });

    const textureForSupportOnT01 = (shelfType === 0) ? [['plain_support', 'basic_white_support.jpg']] : [];
    [
        ['cast_shadow', 'cast_shadow3.png'],
        ['cast_shadow_legs', 'shadow_S-plus.png'],
        ['cast_shadow_plinth', 'shadow_plinth.png'],
        ['cast_shadow_wall', 'shadow_wall.png'],
        ['leg_texture', 'leg.jpg'],
        ['doors_open', 'doors_open.jpg'],
        ['doors_open_white', 'doors_open_white.jpg'],
        ['doors_close', 'doors_close.jpg'],
        ['handle_short_left_texture', 'handle_short_left.png'],
        ['s3d_wall', 'shadows/_wall.png'],
        ['s3d_wall2', 'shadows/_wall2.png'],
        ['s3d_floor', 'shadows/_floor.png'],
        ...textureForSupportOnT01,
    ].map(texture => loader.loadTexture({ name: texture[0], url: `textures/${texture[1]}` }, true));

    const [, initialColorName] = getDefinitionForMaterial(materialId, shelfType);
    return Promise.all(flattenDeep([
        loader.loadCubemap('cubemap'),
        loader.loadPlane({
            name: 'wall_compartment_shadow',
            url: 'textures/wall_shadow.jpg',
        }),
        loader.loadColorTextures(initialColorName, shelfType),
    ]))
        .then(() => loader.loadModels(ObjectsConfig(initialColorName))
            .then(() => loader));
}

// eslint-disable-next-line import/prefer-default-export
export { theLoader }
