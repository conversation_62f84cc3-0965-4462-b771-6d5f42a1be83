import { Power1, Power4, TweenLite } from 'gsap';

class SceneRotation {
    constructor({
        isMobile,
        scene,
        isEnable = true,
        settings,
    }) {
        this.isMobile = isMobile;
        this.scene = scene;
        this.isEnable = isEnable;
        this.settings = settings;
        this.init();
    }

    init() {
        if (!this.isMobile) {
            window.addEventListener('mousemove', e => {
                const widthFactor = ((window.innerWidth / 2 - e.clientX) * -1) / window.innerWidth;
                // eslint-disable-next-line no-param-reassign
                TweenLite.to(this.scene.rotation, this.settings.rotationDuration, {
                    ease: Power4.easeOut, y: widthFactor * this.settings.rotationFactor,
                }, true);
            });
        } else {
            try {
                window.addEventListener('deviceorientation', e => {
                    const sign = e.gamma > 0 ? 1 : -1;
                    const valueToRotate = Math.abs(e.gamma) / 180 > this.settings.rotationFactor ? this.settings.rotationFactor : Math.abs(e.gamma) / 180;
                    TweenLite.to(
                        this.scene.rotation,
                        this.settings.rotationDuration,
                        { ease: Power1.easeIn, y: valueToRotate * sign },
                        true,
                    );
                });
            } catch (error) {
                console.error('Detection device orientation is no supporterd', error);
            }
        }
    }

    toggleRotation(val) {
        this.isEnable = val;
        TweenLite.to(this.scene.rotation, this.settings.rotationDuration, {
            ease: Power1.easeIn, y: 0,
        }, true);
    }
}


function initSceneRotation({ isMobile, scene }) {
    const settings = {
        rotationFactor: 0.15,
        rotationDuration: 0.1,
    };
    return new SceneRotation({ isMobile, scene, settings });
}

export { initSceneRotation, SceneRotation };
