import { Canvas } from '../factory/canvas.js';

function bakery(
    productCanvas = null,
    color = { r: 0, g: 0, b: 0, a: 1 }
) {

    var chain;
    var backgorundColor = `rgba(${color.r},${color.g},${color.b},${color.a})`;

    let setup = function setupInitialCanvas(sourceCanvas) {
        if(!productCanvas) {
            let { element } = new Canvas({ 
                width: sourceCanvas.width, 
                height: sourceCanvas.height 
            });
            productCanvas = element;
        } 
    };

    let flush = function clearCanvas(canvas) {
        let ctx = canvas.getContext('2d', { alpha: true });
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = backgorundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
    };

    let merge = function mergeCanvas(canvasSource, settings, projectedPosition = { x: 0, y: 0 }) {
        setup(canvasSource);

        let ctx = productCanvas.getContext('2d');

        ctx.globalAlpha = settings.opacity;
        ctx.globalCompositeOperation = settings.blendMode;
        ctx.drawImage(canvasSource, projectedPosition.x, projectedPosition.y);
        ctx.globalAlpha = 1;
        ctx.globalCompositeOperation = null;

        return chain;
    };

    let clip = function createGradientClip() {
        let ctx = productCanvas.getContext('2d');
        ctx.globalCompositeOperation = 'destination-out';

        let h = productCanvas.height;
        let w = productCanvas.width;

        let spread = 120;

        var leftGradient = ctx.createLinearGradient(0, 0, spread/1.5, 0);

        leftGradient.addColorStop(0,"rgba(240,240,240,1)");
        leftGradient.addColorStop(1,"rgba(240,240,240,0)");

        var rightGradient = ctx.createLinearGradient(w,0,w-spread/1.5,0);
        
        rightGradient.addColorStop(0,"rgba(240,240,240,1)");
        rightGradient.addColorStop(1,"rgba(240,240,240,0)");

        ctx.fillStyle = leftGradient;
        ctx.fillRect(0, 0, spread, h);
        ctx.fillStyle = rightGradient;
        ctx.fillRect(w-spread, 0, spread, h);

        ctx.globalCompositeOperation = null;


        return chain;
    };

    let render = function buildProduct(bakeTo) {
        return bakeTo = productCanvas;
    };

    flush(productCanvas);

    return chain = {
        mix: merge,
        render: render,
        gradientClip: clip
    }
}

export {
    bakery as Bakery
}