THREE = THREE || {};

const DEBUG = false;
const isMobile = $('.webglconfigurator').hasClass('mobile') || window.is_mobile_loaded;
const round2power = (x) => Math.pow(2, Math.ceil(Math.log(x)/Math.log(2)));
// return 1 << 31 - Math.clz32(n);

import { Canvas } from './utils/factory/canvas.js';
import { Wall } from './scene/wall.js';
import { Bakery } from './utils/bakery/bakery.js';

import { softShadowsTexturePass } from './methods/raytrace/build';

import { ShadowWallA, ShadowWallB, ShadowFloor } from './cachedMobileShadows.js';


import {
    settingsScene, 
    settingsShadowFloor, 
    settingsShadowOne, 
    settingsShadowTwo,
    world,
} from './settings-production.js';


class TylkoRenderer {

    constructor ({
        antialias = true,
        devicePixelRatio = 1,

        textures,

        renderScene = false,
        mobileMode = isMobile || window.is_ios_loaded,
        downsamplingFactor = 30,

        width = 'auto',
        height = 'auto',
        ...experimental
    },

    cameraProvider, 
    geometryProvider,
    furnitureModel 
    // performanceStrategy

    ) {


        if(DEBUG) console.log("Tylko Renderer Initilised", renderScene, mobileMode);

        let imageFromBase64 = (src) => {
            return new Promise((resolve) => {
                var image = new Image();
                image.addEventListener("load", () => {
                    resolve(image);
                });
                image.crossOrigin = 'Anonymous';
                image.src = src;
            });
        }

        let firstWallImage, secondWallImage, floorImage;

        if(textures) {
            this.images = [
                textures["s3d_wall"].image,
                textures["s3d_wall2"].image,
                textures["s3d_floor"].image
            ];
        } else {

            firstWallImage = imageFromBase64(ShadowWallA);
            secondWallImage = imageFromBase64(ShadowWallB);
            floorImage = imageFromBase64(ShadowFloor);

            Promise.all([
                firstWallImage,
                secondWallImage,
                floorImage
            ]).then((images) => { 
                this.images = images;
            });
        }
        
        this.quality = downsamplingFactor;
        this.camera = cameraProvider;
        this.geo = geometryProvider;
        this.ivy = furnitureModel;

        this.mobileMode = mobileMode;

        this.threeRenderer = new THREE.WebGLRenderer ({
            antialias,
            devicePixelRatio,
            preserveDrawingBuffer: true,
            alpha: true,
           // sortObjects: false
        });

       // this.threeRenderer.sortObjects = false;

        switch (renderScene) {
            case true:
                this.compose();
                this.bakePass = true;
                break;
            default:
                this.bakePass = false;
                //this.bakePass = false;
                break;
        }

        return this;
    }

    compose() {

        // Globals

        let shadowScaleFactor = this.quality,
            wallWidth = 6000 * world.scale,
            wallHeight = 4000 * world.scale

        let scene = this.geo.getScene();

        // Shadows generators

        let shadowSized = { 
            width: wallWidth / shadowScaleFactor, 
            height: wallHeight / shadowScaleFactor 
        };

        shadowSized.width = round2power(shadowSized.width);
        shadowSized.height = round2power(shadowSized.height);

        this._shadowSized = shadowSized;


        if(!this.mobileMode) {

            let { pass: firstShadow, canvas: firstShadowCanvas } 
                = this.spwanShadowRenderer(shadowSized);

            let { pass : secondShadow, canvas: secondShadowCanvas } 
                = this.spwanShadowRenderer(shadowSized);

            let { pass : floorShadow, canvas: floorShadowCanvas } 
                = this.spwanShadowRenderer({ top: true, ...shadowSized });

            this.firstShadow = firstShadow;
            this.firstShadowCanvas = firstShadowCanvas;

            this.secondShadow = secondShadow;
            this.secondShadowCanvas = secondShadowCanvas;

            this.floorShadow = floorShadow;
            this.floorShadowCanvas = floorShadowCanvas;
    
        } else {

            let { element: firstShadowCanvasFast } = new Canvas(shadowSized);
            let { element: secondShadowCanvasFast } = new Canvas(shadowSized);
            let { element: floorShadowCanvasFast } = new Canvas(shadowSized);

            this.firstShadowCanvas = firstShadowCanvasFast;
            this.secondShadowCanvas = secondShadowCanvasFast;
            this.floorShadowCanvas = floorShadowCanvasFast;
        }

        let { element: cachedWallTexture } = new Canvas(shadowSized);
        let { element: cachedFloorTexture } = new Canvas(shadowSized);
    
        this.cashedFloorTextureCanvas = cachedFloorTexture;
        this.cachedWallTextureCanvas = cachedWallTexture;
             
        // Wall

        let wall = new Wall({
            planeWidth: wallWidth,
            planeHeight: wallHeight,
            scene: scene,
            source: this.cachedWallTextureCanvas
        });

        let floor = new Wall({
            planeWidth: wallWidth,
            planeHeight: wallHeight,
            scene: scene,
            source: this.cashedFloorTextureCanvas,
            isFloor: true
        });

        this.floor = floor;
        this.wall = wall;

        if(this.mobileMode) {
            let resetCanvas = (canvas) => {
                let el = document.createElement('canvas');
                el.width = canvas.width;
                el.height = canvas.height;
                return el;
            }

            this.firstShadowCanvas = resetCanvas(this.firstShadowCanvas);
            this.secondShadowCanvas = resetCanvas(this.secondShadowCanvas);
            this.floorShadowCanvas = resetCanvas(this.floorShadowCanvas);
        }

    }

    beforeBake(useRealRenderer, dimensions) {

        if(DEBUG) console.log("BEFORE BAKE", dimensions);

        if (useRealRenderer) {

            this.firstShadow.render({ settings: settingsShadowOne });
            this.secondShadow.render({ settings: settingsShadowTwo });
            this.floorShadow.render({ settings: settingsShadowFloor });

            return;
        } 

        let drawImage = (target, source, x,y,w,h) => {
            let ctx = target.getContext('2d');
            if(ctx) ctx.drawImage(source, x,y,w,h);
        };

        if (dimensions) {
            var { currentWidth, currentHeight } = dimensions;
        } else {
            var currentWidth = this.ivy.width;
            var currentHeight = this.ivy.getHeight();
        }

        let sx = 755 / (currentWidth / 2);
        let sy = 362 / (currentHeight / 2);

        let ssx = this._shadowSized.width /sx;
        let ssy =  this._shadowSized.height /sy;

        let px = (this._shadowSized.width-ssx)/2;
        let py = -(ssy - this._shadowSized.height);

        if(DEBUG) console.log(px,py,ssx,ssy,sx,sy);

        if (this.images) {

            drawImage(this.firstShadowCanvas, this.images[0], px, py, ssx, ssy);
            drawImage(this.secondShadowCanvas, this.images[1], px, py, ssx, ssy);
            drawImage(this.floorShadowCanvas, this.images[2], px, 0, ssx, this._shadowSized.height);

        } else {

        }

    }

    bake(dimensions) {

        if (!this.bakePass) return;

        this.beforeBake(!this.mobileMode, dimensions);

        if (DEBUG) console.time("bake");

        Bakery(this.cachedWallTextureCanvas, {
            ...settingsScene.backgroundColor,
            a: settingsScene.backgroundColorAlpha
        })
        .mix(this.firstShadowCanvas, {
            opacity: settingsShadowOne.alpha,
            blendMode: settingsShadowOne.mixOperation
        })
        .mix(this.secondShadowCanvas, { 
            opacity: settingsShadowTwo.alpha,
            blendMode: settingsShadowTwo.mixOperation
        })
        .gradientClip();

        Bakery(this.cashedFloorTextureCanvas, {
            ...settingsScene.floorColor,
            a: settingsScene.floorColorAlpha
        })
        .mix(this.floorShadowCanvas, {
            opacity: settingsShadowFloor.alpha,
            blendMode: settingsShadowFloor.mixOperation
        })
        .gradientClip();

        this.wall.updateMaterial();
        this.wall.wallMaterial.opacity = settingsScene.shadowPlaneOpacity;

        this.floor.updateMaterial();
        this.floor.wallMaterial.opacity = settingsScene.floorPlaneOpacity;

        if (DEBUG) console.timeEnd("bake");



        window.firstShadowCanvas = this.firstShadowCanvas;
        window.secondShadowCanvas = this.secondShadowCanvas;
        window.floorShadowCanvas = this.floorShadowCanvas;
       
     
    }

    spwanShadowRenderer({ width, height, top }) {

        let { element } = new Canvas({
             width, 
             height, 
             transparent: true 
        });

        return { 
            pass: softShadowsTexturePass(this, element, top), 
            canvas: element
        }
    }

}

export { 
    TylkoRenderer as Renderer
};