/*

 initilizeMask (wallWidth) {

        let leftCorner = new THREE.Mesh(
            new THREE.PlaneGeometry(1, 1, 1, 1), 
            new THREE.MeshBasicMaterial()
        );

        leftCorner.position.x = -wallWidth/2;
        scene.add(leftCorner);
        this.leftCorner = leftCorner;

        let rightCorner = new THREE.Mesh(
            new THREE.PlaneGeometry(1, 1, 1, 1), 
            new THREE.MeshBasicMaterial()
        );
        
        rightCorner.position.x = wallWidth/2;
        scene.add(rightCorner);
        this.rightCorner = rightCorner;

        this.maskReady = true;
    }
    

clip () {

    return;

    if (this.maskReady != true) return;

    // STYLE!

    var projector = new THREE.Projector();
    let get2dPointForVert = ( vert, camera, canvas ) => {

        var vector = new THREE.Vector3();
        vector.setFromMatrixPosition(vert.matrixWorld);
        vector.project(camera);
        var width = canvas.width, height = canvas.height;
        var widthHalf = width / 2, heightHalf = height / 2;
        vector.x = ( vector.x * widthHalf ) + widthHalf;
        vector.y = -( vector.y * heightHalf ) + heightHalf;
        return vector;
        /*

        vert = new THREE.Vector3( 0, 0,s 0 );
        var vector = vert.project( camera);
        var cameraPosition = new THREE.Vector3();
        cameraPosition.setFromMatrixPosition( camera.matrixWorld );
        vector = vector.applyMatrix3( camera.matrixWorld );
      //  console.log(vector);
        return {
            x: vector.x *= c,
            y: vector.y *= canvas.height
        }
        

    }

    let edgeLeft = get2dPointForVert(
        this.leftCorner,
        this.camera, 
        this.threeRenderer.domElement
    );

    let edgeRight = get2dPointForVert(
        this.leftCorner,
        this.camera, 
        this.threeRenderer.domElement
    );

    if (edgeLeft.x > 0) {
        document.getElementById('leftClip').style.transform = "translateX("+(edgeLeft.x)+"px)";
    }

    if (edgeRight.x < this.threeRenderer.domElement.width) {
        document.getElementById('rightClip').style.transform = "translateX(-"+(edgeRight.x)+"px)";
    }

    //geometry.vertices[ 0 ]
}

*/