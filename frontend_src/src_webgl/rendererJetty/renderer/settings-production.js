const world = {
    scale: 1.7
};

const settingsShadowOne = {
    x: -1661.75,
    y: 7860,
    z: 7860,
    power: 0.9338,
    shelfGap: 257.225,
    iterations :14,
    cameraz: 262.965,
    alpha: 0.1368,
    mixOperation: "multiply",
    warpx: 0.08495 * world.scale,
    warpy: 0.05725 * world.scale,
    partialShadow: 'default',
};

const settingsShadowTwo = {
    x: 3845.5,
    y: 12545.5,
    z: 7860,
    power: 0.6486,
    shelfGap: 0,
    iterations: 14,
    cameraz: 262.965,
    alpha: 0.2865,
    mixOperation: "hard-light",
    warpx:0.0877 * world.scale,
    warpy:0.05725 * world.scale,
    partialShadow: 'default',
};

const settingsShadowFloor = {
    x: 4652.5,
    y: 9404,
    z: 10330.5,
    power: 0.6406,
    shelfGap: 0,
    iterations: 14,
    cameraz: 0,
    alpha: .8,
    mixOperation: "hard-light",
    warpx: 0.0858 * world.scale,
    warpy: 0.0516 * world.scale,
    partialShadow: 'default',
};

const settingsScene = {
    quality: 8,
    background: true,
    mixOperation: "none",
    wallColorAlpha: 0,
    shadowPlaneOpacity: 0.275,
    backgroundColor: {"r": 240,"g": 240,"b": 240},
    backgroundColorAlpha: 0.7,
    floorColor: {"r": 240,"g": 240,"b": 240},
    floorColorAlpha: 0.31,
    floorPlaneOpacity: 0.125
};

export { 
    world,
    settingsShadowOne,
    settingsShadowTwo,
    settingsShadowFloor,
    settingsScene
};