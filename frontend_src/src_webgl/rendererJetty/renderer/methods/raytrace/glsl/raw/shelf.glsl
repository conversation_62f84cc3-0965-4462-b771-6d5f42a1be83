precision mediump float;

uniform float iGlobalTime;
uniform vec2 iResolution;

vec2 doModel(vec3 p);

#pragma glslify: raytrace = require('glsl-raytrace', map = doModel, steps = 290)
#pragma glslify: normal = require('glsl-sdf-normal', map = doModel)
#pragma glslify: camera = require('glsl-turntable-camera')
#pragma glslify: noise = require('glsl-noise/simplex/4d')
#pragma glslify: box = require('glsl-sdf-box') 
#pragma glslify: blinnPhongSpec = require('glsl-specular-phong') 

#pragma glslify: combine = require('glsl-combine-smooth')
#pragma glslify: opU = require('glsl-sdf-ops/union')
#pragma glslify: opS = require('glsl-sdf-ops/subtraction')

#pragma glslify: combine2 = require('glsl-combine-chamfer')

vec2 doModel(vec3 p) {
  
  vec3 scale = vec3(2.0,2.0,2.0);
  float szafka = box(p,vec3(1.2,1.0,0.3)*scale);
  
  float c = box(p-vec3(.0,.65,.0)*scale,vec3(1.15,.3,1.0)*scale);
  float c2 = box(p-vec3(.0,.0,.0)*scale,vec3(1.15,.3,1.0)*scale);
  float c3 = box(p-vec3(.0,-.65,.0)*scale,vec3(1.15,.3,1.0)*scale);

  
  szafka = opS(c, szafka);
  szafka = opS(c2, szafka);
  szafka = opS(c3, szafka);

  
  float podloga = box(p-vec3(0.,-2.1,0.),vec3(10.,.001,10.0));
  float sciana = box(p-vec3(0.,.0,-.3),vec3(10.1,6.0,.01));
  float scena = combine(szafka, podloga , 0.);
  scena = combine(scena, sciana, 0.);
  return vec2(scena, .0);
}

float shadowSoft( vec3 ro, vec3 rd, float mint, float maxt, float k )
{
    float res = 1.5;
    float ph = 0.0;
    float t = 0.1;
    for ( int i = 0; i < 13; ++i )
    {
        float h = doModel(ro + rd*t).x;
        if( h<0.01 )
            return .0;
        float y = h*h/(2.*ph);
        float d = sqrt(h*h-y*y);
        res = min( res, k*d/max(0.0,t-y) );
        ph = h;
        t += h;
    }
    return res;
}

vec3 shade( vec3 pos, vec3 nrm, vec4 light )
{
	vec3 toLight = light.xyz - pos;
	
	float toLightLen = length( toLight );
	toLight = normalize( toLight );

	float comb = 2.0;
	float vis = shadowSoft( pos, toLight, .00625, toLightLen, 1. );
	
	if ( vis > 0.0 )
	{
		float diff = 2.0 * max( 0.0, dot( nrm, toLight ) );
		float attn = 1.0- pow( min( 1.0, toLightLen / light.w ), 1.0 );
		comb += diff * attn * vis;
	}
	
  return vec3(vis);
	//return vec3( comb, comb, comb );
}



/*
float softshadow(vec3 ro, vec3 rd, float mint, float maxt, float k )
{
    float res = 1.0;
    float ph = 1e20;
    for( float t=mint; t < maxt; )
    {
        float h = doModel(ro + rd*t).x;
        if( h<0.001 )
            return 0.0;
        float y = h*h/(2.0*ph);
        float d = sqrt(h*h-y*y)
        res = min( res, k*d/max(0.0,t-y) );
        ph = h;
        t += h;
    }
    return res;
}*/


void main() {
    vec3 color = vec3(0.,0./0.,0.);
    vec3 ro, rd;

    float rotation = sin(time*.8);
    float height   = 0.0;
    float dist     =6.78;
    
    orbitCamera(-.3, height, dist, iResolution.xy, ro, rd);


  vec2 t = raytrace(ro, rd);
  
 // float noiseField = noise(vec4(rd*t.x,ro*t.y)) * 2.0;

  if (t.x > -0.5) {
    vec3 pos = ro + rd * t.x;
    vec3 nor = normal(pos);
    float shininess = 3.4;
    vec3 eyePosition = vec3(10.0,10.0,10.0);
    vec3 surfacePosition = pos;
    vec3 surfaceNormal = nor;

    vec3 lightPosition =   vec3(3.0,5.0,7.5);
    vec3 lightPosition2 =  vec3(2.0,2.5,9.0);

    vec3 eyeDirection = normalize(eyePosition - surfacePosition);
    vec3 lightDirection = normalize(lightPosition - surfacePosition);

    vec3 shaded = shade(pos, nor, vec4(lightPosition,3.2));
    vec3 shaded2 = shade(pos, nor, vec4(lightPosition2,6.2));

    
    float power = blinnPhongSpec(lightDirection, rd, nor, shininess);
    float power2 = blinnPhongSpec(lightDirection, rd, nor, shininess);
    
   color = vec3(dot(nor, vec3(.3, .9, .0) ));
   color += mix(shaded,shaded2,.4);
   color += power*0.3;
  }
  
 
  gl_FragColor.rgb = color;
  gl_FragColor.a   = 1.0;
}