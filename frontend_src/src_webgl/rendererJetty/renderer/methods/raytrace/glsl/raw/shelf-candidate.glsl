precision mediump float;

uniform float time;
uniform vec2 viewport;

uniform vec3 cameraPosition;
uniform vec4 castSourcePosition;
uniform vec3 ivyShelfSize;

uniform float lp;
uniform float gap;
uniform int iterations;

varying vec3 rayPosition;
varying vec3 rayDirection;

//uniform vec3 element[42];
//uniform vec3 elementPosition[42];

float rotation;


#define AO_ITERATIONS 4
#define AO_DELTA .1
#define AO_DECAY .7

#pragma glslify: combine = require('glsl-combine-smooth')
#pragma glslify: opU = require('glsl-sdf-ops/union')
#pragma glslify: opS = require('glsl-sdf-ops/subtraction')
#pragma glslify: box2 = require('glsl-sdf-box') 
#pragma glslify: combine2 = require('glsl-combine-chamfer')


float vmax(vec2 v) {
	return max(v.x, v.y);
}
float box(vec2 p, vec2 b) {
	return vmax(abs(p)-b);
}

float modeler(vec3 p, int mode) {

    float hairplane = 1.0001;
    float shelfHeight = ivyShelfSize.y;
    
   // p.z+= data.z;
  //  p.y+=-cameraPosition.y;

    p.y-=ivyShelfSize.y;
    p.z-=ivyShelfSize.z;

   // p.y -= cameraPosition.y;

    vec2 planeSizes = vec2(16800.0,8400.0);
    
    vec3 scaler = vec3(10.0,10.0,10.0);
    vec3 scaler2 = vec3(10.);

    float scena = 0.;

    if(mode < 1) {
        float podloga = box2(p - vec3(0.0, -ivyShelfSize.y - gap, -gap), vec3(planeSizes.x, hairplane, planeSizes.y));
        float sciana =  box2(p - vec3(0.0,0.0, -(ivyShelfSize.z + gap)), vec3(planeSizes.x, planeSizes.y, hairplane));
        scena = opU(sciana, podloga);
    }

    vec3 SBB = ivyShelfSize;

    float szafka = box2(p + vec3(0.,0.,0.), SBB);

    /*
    for(int i = 0; i < 6; i++) {
        for(int j = 0; j < 6; j++) {
            szafka = max(-box(p.xy - vec2(float(i),float(j))*scaler2.xy - vec2(-2.0,0.0)*scaler2.xy, vec2(0.45)*scaler2.xy), szafka );
        }
    }
    */

    scena = opU(scena, szafka);

    return scena;
}



vec2 shelfSceneSDF(vec3 p) {
   return vec2(modeler(p,0),0.);
}

vec2 shelfSceneSDF2(vec3 p) {
    return vec2(modeler(p,1),0.);
}



#pragma glslify: lookAt = require('glsl-look-at')
#pragma glslify: raytrace = require('glsl-raytrace', map = shelfSceneSDF, steps = 140)
#pragma glslify: raytrace2 = require('glsl-raytrace', map = shelfSceneSDF2, steps = 10)
#pragma glslify: normal = require('glsl-sdf-normal', map = shelfSceneSDF)
#pragma glslify: square = require('glsl-square-frame')
#pragma glslify: blinnPhongSpec = require('glsl-specular-phong') 

float evaluateAmbientOcclusion(vec3 point, vec3 normal)
{
	float ao = 0.0;
	float delta = AO_DELTA;
	float decay = 1.0;

	for(int i = 0; i < AO_ITERATIONS; i++)
	{
		float d = float(i) * delta;
		decay *= AO_DECAY;
		ao += (d - shelfSceneSDF(point + normal * d)).x / decay;
	}

	return clamp(1.0 - ao * .15, 0.0, 1.0);
}



float shadowSoft( vec3 ro, vec3 rd, float mint, float maxt, float k )
{
    float res = 1.5;
    float ph = 0.0;
    float t = 0.1;
    for ( int i = 0; i < 18; ++i )
    {
        float h = shelfSceneSDF(ro + rd*t).x;
        if( h<0.0001 )
            return .0;
        float y = h*h/(2.*ph);
        float d = sqrt(h*h-y*y);
        res = min( res,k*d/max(0.0,t-y) );
        ph = h;
        t += h;
    }
    return res;
}


vec3 shade( vec3 pos, vec3 nrm, vec4 light )
{
	vec3 toLight = light.xyz - pos;
	float toLightLen = length( toLight );
	toLight = normalize( toLight );

	float comb = 2.0;
	float vis = shadowSoft( pos, toLight, .625, toLightLen, lp );
	
	if ( vis > 0.0 )
	{
		float diff = 2.0 * max( 0.0, dot( nrm, toLight ) );
		comb += diff *vis;
	}
	
  return vec3(vis * toLight);
}


vec3 getRay(mat3 camMat, vec2 screenPos, float lensLength) {
    return normalize(camMat * vec3(screenPos, lensLength));
}

vec3 getRay(vec3 origin, vec3 target, vec2 screenPos, float lensLength) {
    mat3 camMat = lookAt(origin, target, 0.0);
    return getRay(camMat, screenPos, lensLength);
}

void orbitCamera(
    in float camAngle,
    in float camHeight,
    in float camDistance,
    in vec2 screenResolution,
    out vec3 rayOrigin,
    out vec3 rayDir
) {

  //  vec2 screenPos = square(screenResolution);
 //   vec3 rayTarget = cameraPosition;

    rayOrigin = cameraPosition;
    rayDir = rayDirection;
}

void main() {

    vec3 color = vec3(0.,0.,0.);
    vec3 rd,  ro;
    rotation = sin(time*.8);

    orbitCamera(0.0, 0.0, 0.0, viewport.xy, ro, rd);

  //  float cameraAngle  = 0.0 * time;
    // vec3  ro    = vec3(0.0);
   // vec3  rayTarget    = vec3(0, 0, 0);
 //   vec2  screenPos    = square(viewport.xy);

    vec2 t = raytrace(rayPosition, rd, 35000., 0.001);
   //  vec2 t2 = raytrace2(rayPosition, rd, 1000., 0.001);


    if (t.x > 0.5) {
        vec3 pos = ro + rd * t.x;
        vec3 nor = normal(pos);

    //    float shininess = 3.4;
      //  vec3 eyePosition = vec3(0.0,0.0,0.0);
     //   vec3 surfacePosition = pos;
     //   vec3 surfaceNormal = nor;

        vec3 lightPosition = vec3(castSourcePosition.x+3.*rotation*rotation, castSourcePosition.y, castSourcePosition.z);
     //   vec3 lightPosition2 = vec3(2.0+rotation,2.5,9.0);
      //  lightPosition *= cos(rotation * rotation);

      // vec3 eyeDirection = normalize(eyePosition - surfacePosition);
       // vec3 lightDirection = normalize(lightPosition - surfacePosition);

        // vec3 shaded2 = shade(pos, nor, vec4(lightPosition2,6.2));
        // float power = blinnPhongSpec(lightDirection, rd, nor, shininess);
        // float power2 = blinnPhongSpec(lightDirection, rd, nor, shininess);
        //color = pow(nor, vec3(.45454));

         vec3 shaded = shade(pos, nor, vec4(lightPosition,lp));
        //color = vec3(.0);
        
          float ao = evaluateAmbientOcclusion(pos, nor);

        color = shaded *  ao;
        color = vec3(dot(color, vec3(.3, .9, .0)));
     //  color = nor * pos;

    }

   // 
//   if(t2.x > 0.5) {
  //     color =vec3(0.);
  // }

    gl_FragColor.rgba = vec4(color,  1.0);
   // gl_FragColor.a   = smoothstep(0.0, 1.0, length(color));
}