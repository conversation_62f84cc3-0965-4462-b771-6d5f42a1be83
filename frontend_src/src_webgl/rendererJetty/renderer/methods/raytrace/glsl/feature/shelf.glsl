precision mediump float;
#define AO_ITERATIONS 4
#define AO_DELTA .1
#define AO_DECAY .7

#pragma glslify: cnoise3 = require('glsl-noise/classic/3d') 

uniform float time;
uniform vec2 viewport;

uniform vec4 idents[40];

uniform mat4 threeProjection;
uniform mat4 viewMatrix;
uniform vec3 cameraPosition;
uniform vec4 data;
uniform vec3 ivybb;

uniform float lp;
uniform float gap;

uniform int iterations;

varying vec3 rayPosition;
varying vec3 rayDirection;

#pragma glslify: unionElements = require('glsl-sdf-ops/union')
#pragma glslify: box = require('glsl-sdf-box') 
#pragma glslify: blinnPhongSpec = require('glsl-specular-phong') 

float vmax(vec2 v) {
	return max(v.x, v.y);
}
float boxFast(vec2 p, vec2 b) {
	return vmax(abs(p)-b);
}

float fOpIntersectionRound(float a, float b, float r) {
	vec2 u = max(vec2(r + a,r + b), vec2(0));
	return min(-r, max (a, b)) + length(u);
}

float modeler(vec3 p, int mode) {

    float hairplane = 1.0001;
    float shelfHeight = ivybb.y;

    p.y-=ivybb.y - 20.;
    p.z-=ivybb.z;

    vec2 planeSizes = vec2(16800.0,28400.0);
    vec3 scaler = vec3(10.0,10.0,10.0);
    
    float scena = 0.;
    vec3 ss = ivybb;

    ss.y-=20.;

    float szafka = box(p + vec3(0.,0.,0.), ss);
    float podloga = dot(p - vec3(0.0, -ivybb.y - gap, -gap), vec3(0.,1.,0.));
    float sciana =  dot(p - vec3(0.0,0.0, -(ivybb.z + gap)), vec3(0.,0.,1.));

    //float podloga = box(p - vec3(0.0, -ivybb.y - gap, -gap), vec3(planeSizes.x, hairplane, planeSizes.y));
    //float sciana =  box(p - vec3(0.0,0.0, -(ivybb.z + gap)), vec3(planeSizes.x, planeSizes.y, hairplane));

    scena = unionElements(sciana, podloga);
  //  } else {
   //     szafka = box(p + vec3(0.,0.,ss.z + hairplane), vec3(ss.x,ss.y,hairplane));
   // }

    float deltax = -50.;
    float deltay = 150.;

    for(int i = 0; i < 20; i++) {
        float renders = dot(idents[i], vec4(1.0));

        if(renders > 0.0) {
            vec2 position = idents[i].xy;
            position.y = ivybb.y - position.y - deltay;
            position.x = - position.x + deltax;

            vec2 size = vec2(idents[i].z-idents[i].x, idents[i].w-idents[i].y);
            size.y = size.y * 0.5;
            size.x = size.x * 0.5;
            size.x += 30.;
            position.x += 10.;

            if(idents[i].x > 0.) {
                position.x-=50.;
            }

          //  szafka = fOpIntersectionRound(-boxFast(p.xy + position, size), szafka, 50.);
           szafka = max(-boxFast(p.xy + position, size), szafka);
        }
     //    szafka = max(-boxFast(p.xy, vec2(0.45)*crpsize.xy), szafka);
    }
    
    scena = unionElements(scena, szafka);
    return scena;
}

vec2 shelfScene(vec3 p) {
   return vec2(modeler(p,0),0.);
}

vec2 shelfSceneSDF2(vec3 p) {
    return vec2(modeler(p,1),0.);
}

float evaluateAmbientOcclusion(vec3 point, vec3 normal)
{
	float ao = 0.0;
	float delta = AO_DELTA;
	float decay = 1.0;

	for(int i = 0; i < AO_ITERATIONS; i++)
	{
		float d = float(i) * delta;
		decay *= AO_DECAY;
		ao += (d - shelfScene(point + normal * d)).x / decay;
	}

	return clamp(1.0 - ao * .15, 0.0, 1.0);
}

#pragma glslify: raytrace = require('glsl-raytrace', map = shelfScene, steps = 140)
#pragma glslify: normal = require('glsl-sdf-normal', map = shelfScene)

float shadowSoft( vec3 ro, vec3 rd, float mint, float maxt, float k )
{
    float res = 2.5;
    float ph = 0.0;
    float t = 0.1;
    for ( int i = 0; i < 26; ++i )
    {
        float h = shelfScene(ro + rd*t).x;
        if( h<0.001 )
            return .0;
        float y = h*h/(2.*ph);
        float d = sqrt(h*h-y*y);
        res = min( res,k*d/max(0.0,t-y) );
        ph = h;
        t += h;
    }
    return res;
}

vec3 shade( vec3 pos, vec3 nrm, vec4 light )
{
	vec3 toLight = light.xyz - pos;
	float toLightLen = length( toLight );
	toLight = normalize( toLight );

	float comb = 2.0;
	float vis = shadowSoft( pos, toLight, .625, toLightLen, lp );
	
  return vec3(vis);
}

void main() {

    vec3 color = vec3(0.,0.,0.);
    vec3 rd,  ro;

    vec3 pos2 = vec3(0.0);

    ro = rayPosition;
    rd = rayDirection;

    vec2 t = raytrace(rayPosition, rd, 35000., 0.001);
   // vec2 tc = raytrace2(rayPosition, rd, 35000., 0.001);

    if (t.x > 0.5) {
        vec3 pos = ro + rd * t.x;
        vec3 nor = normal(pos);
        vec3 lightPosition = data.xyz;
     //   lightPosition.x = cameraPosition.x;
        vec3 shaded = shade(pos, nor, vec4(lightPosition,lp));
     //   float ao = evaluateAmbientOcclusion(pos, nor);
        color = shaded;;// *  ao;
        color = vec3(dot(color, vec3(.3, .9, .0)));

        pos2 = pos;
    }

    gl_FragColor.a = 1.0;
    gl_FragColor.rgb = color;
   // gl_FragColor.a   = smoothstep(0.0, 1.0, length(color));
}