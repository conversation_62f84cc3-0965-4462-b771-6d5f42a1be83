#define clip 35000.0

precision mediump float;

attribute vec2 position;
attribute vec2 uvs;

varying vec4 normalizedDeviceCoords;
varying vec3 rayDirection;
varying vec3 rayPosition;

uniform mat4 viewProjectionMatrix;
uniform vec3 cameraPosition;
// uniform mat4 viewMatrix;
uniform vec3 ivybb;
// uniform mat4 threeProjection;
uniform mat4 sceneMatrix;

void main() {
    vec2 tmp = uvs;
    tmp = (tmp * 2.0) - vec2(1.0);
    normalizedDeviceCoords = vec4(tmp, 1.0, 1.0) * clip;
    rayDirection = normalize(vec3(viewProjectionMatrix * normalizedDeviceCoords) - cameraPosition);
    rayPosition = cameraPosition;
    gl_Position = vec4(position, 0.0, 1.0);
}

/* TODO world to 
float imageAspectRatio = imageWidth / imageHeight; // assuming width > height 
float Px = (2 * ((x + 0.5) / imageWidth) - 1) * tan(fov / 2 * M_PI / 180) * imageAspectRatio; 
float Py = (1 - 2 * ((y + 0.5) / imageHeight) * tan(fov / 2 * M_PI / 180); 
Vec3f rayOrigin = Point3(0, 0, 0); 
Matrix44f cameraToWorld; 
cameraToWorld.set(...); // set matrix 
Vec3f rayOriginWorld, rayPWorld; 
cameraToWorld.multVectMatrix(rayOrigin, rayOriginWorld); 
cameraToWorld.multVectMatrix(Vec3f(Px, Py, -1), rayPWorld); 
Vec3f rayDirection = rayPWorld - rayOriginWorld; 
rayDirection.normalize(); // it's a direction so don't forget to normalize 
*/