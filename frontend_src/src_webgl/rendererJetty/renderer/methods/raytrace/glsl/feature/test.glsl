     float deltax = -50.;
    float deltay = 150.;
    
    for(int i = 0; i < 20; i++) {
        float renders = dot(idents[i], vec4(1.0));

        if(renders > 0.0) {
            vec2 position = idents[i].xy;
            position.y = ivybb.y - position.y - deltay;
            position.x = - position.x + deltax;

            vec2 size = vec2(idents[i].z-idents[i].x, idents[i].w-idents[i].y);
            size.y = size.y * 0.5;
            size.x = size.x * 0.5;
            size.x += 30.;
            position.x += 10.;

            if(idents[i].x > 0.) {
                position.x-=50.;
            }

          //  szafka = fOpIntersectionRound(-boxFast(p.xy + position, size), szafka, 50.);
           szafka = max(-boxFast(p.xy + position, size), szafka);
        }
     //    szafka = max(-boxFast(p.xy, vec2(0.45)*crpsize.xy), szafka);
    }


    var settingsShadowOne = {"x":3845.5,"y":7860,"z":7860,"power":0.5053,"shelfGap":76.815,"iterations":14,"cameraz":262.965,"alpha":0.3541,"mixOperation":"darken","warpx":0.08495,"warpy":0.05725};var settingsShadowTwo = {"x":-4000,"y":12861.25,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.7214,"mixOperation":"darken","warpx":0.088,"warpy":0.05725};var settingsShadowFloor = {"x":2610.25,"y":9095.25,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.6221,"mixOperation":"hard-light","warpx":0.08785,"warpy":0.04895};var settingsScene = {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":22.500000000000004,"g":22.500000000000004,"b":22.500000000000004},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":1,"floorPlaneOpacity":0.34500000000000003}

    