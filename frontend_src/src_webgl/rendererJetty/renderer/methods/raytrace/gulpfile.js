const gulp = require('gulp');
const glslify = require('gulp-glslify');
const browserify = require('gulp-browserify');
const stringify = require('stringify');
const brfs = require('brfs');
const rollup = require('rollup');
const rollupify = require('rollupify');
const b = require('browserify');
const source = require('vinyl-source-stream');
const buffer = require('vinyl-buffer');

const deparser = require('glsl-deparser');
const minify = require('glsl-min-stream');
const parser = require('glsl-parser');
const tokenizer = require('glsl-tokenizer/stream');
const debug = require('gulp-debug');
const flatmap = require('gulp-flatmap');
const stringToStream = require('string-to-stream');
const fs = require('fs');
const Vinyl = require('vinyl');
const transform = require('vinyl-transform');
const tap = require('gulp-tap');
var getRawBody = require('raw-body')
var streamBuffers = require('stream-buffers');
var each = require('gulp-each');

var readfiles = require('node-readfiles');
var through = require('through2').obj;

//var toArray = require('stream-to-array')



gulp.task('glsify-shaders', () => 
    gulp.src('./glsl/flat/*.glsl')
        .pipe(glslify())
        .pipe(gulp.dest('build/shaders/flat'))
);


gulp.task('mangle-shaders', (end) => {

    return new Promise((resolveFinal) => {

        var processes = [];

         readfiles('./build/shaders/flat', {
            filter: '*.glsl',
            readContents: true
          }, function (err, content, filename) {

            processes.push(new Promise((resolve) => {

                var myWritableStreamBuffer = new streamBuffers.WritableStreamBuffer({
                    initialSize: (100 * 1024),   // start at 100 kilobytes.
                    incrementAmount: (10 * 1024) // grow by 10 kilobytes each time buffer overflows.
                });
                
                stringToStream(filename)
                .pipe(tokenizer())
                .pipe(parser())
                .pipe(minify())
                .pipe(deparser(false))
                .pipe(myWritableStreamBuffer)
                .on('finish',function() {     
                    var stringer = myWritableStreamBuffer.getContentsAsString('utf8');
               
                    resolve({ file: content, content: stringer });
                });
             // console.log(filename);

            }));

          }).then((files) => {
              
            
            Promise.all(processes).then((result)=>{


                result.map((r) => fs.writeFileSync('./build/shaders/flat/'+r.file,r.content));

                console.log(result);
                resolveFinal();
            });
           
          });
    });




   
 /*      

    stringToStream(contents)
    .pipe(tokenizer())
    .pipe(parser())
    .pipe(minify())
    .pipe(deparser(false))
    .pipe(myWritableStreamBuffer)
    .on('finish',function() {     
        var stringer = myWritableStreamBuffer.getContentsAsString('utf8');
        console.log(stringer);
    
    });*/

});


gulp.task('build-shaders', gulp.series(
    'glsify-shaders',
    'mangle-shaders'
));


gulp.task('default', gulp.series(
    'build-shaders',
));
