// Settings provider 
// for soft shadows renderer
// 
// https://cocopon.github.io/tweakpane/

import dat from 'dat.gui';

let sheet = document.createElement('style');
sheet.innerHTML = ".dg { z-index: 10000 !important; }";
//document.body.appendChild(sheet);


const mixOpertations = ["none", "source-over", "source-in", "source-out", "source-atop", "destination-over", "destination-in", "destination-out", "destination-atop", "lighter", "copy", "xor", "multiply", "screen", "overlay", "darken", "color-dodge", "color-burn", "hard-light", "soft-light", "difference", "exclusion", "hue", "saturation", "color", "luminosity"];


const gui = new dat.GUI({ autoPlace: false });

/*
var settings = { 
    x: -1404, 
    y: 5698.5, 
    z: 6625, 
    power: 4.425, 
    shelfGap: 31.02, 
    iterations: 14,
    cameraz: 1961.4//3000
};
*/

var settingsShadowOne = { 
    x: -2330.25, 
    y: 7860, 
    z: 7860, 
    power: 0.455,
    shelfGap: 0,
    iterations: 14,
    cameraz: 262.965,
    alpha: 0.2,
    shelfGap: 0,
    cameraz: 262.965,
    mixOperation: null,

    warpx: 0.088,
    warpy: 0.05725
    
};

var settingsShadowTwo = { 
    x: 2330.25, 
    y: 7860, 
    z: 7860, 
    power: 0.455,
    shelfGap: 0,
    iterations: 14,
    cameraz: 262.965,
    alpha: 0.3,
    shelfGap: 0,
    cameraz: 262.965,
    mixOperation: null,
    warpx: 0.088,
    warpy: 0.05725
};


var settingsShadowFloor = { 
    x: 2330.25, 
    y: 7860, 
    z: 7860, 
    power: 0.455,
    shelfGap: 0,
    iterations: 14,
    cameraz: 262.965,
    alpha: 0.3,
    shelfGap: 0,
    cameraz: 0,
    mixOperation: null,
    warpx: 0.088,
    warpy: 0.04725
};

var settingsScene = {
    quality: 8,
    background: true,
    mixOperation: 'none',


    wallColorAlpha: 0,
    shadowPlaneOpacity: 0.3,

    backgroundColor: {r: 245, g: 81, b: 44 },
    backgroundColorAlpha: 0,

    floorColor: {r: 245, g: 81, b: 44 },
    floorColorAlpha: 0,
    floorPlaneOpacity: 1,
    shadowPower: 1
};


const updateObject = (object, json) => {
    for(var key of Object.keys(json)) {
        console.log(key);
        object[key] = json[key];
    }
};

var presets = {
    setup_a: function() {

        updateObject(settingsShadowOne, {"x":3845.5,"y":7860,"z":7860,"power":0.5053,"shelfGap":76.815,"iterations":14,"cameraz":262.965,"alpha":0.3541,"mixOperation":"darken","warpx":0.08495,"warpy":0.05725});

        updateObject(settingsShadowTwo, {"x":548.25,"y":12861.25,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.7214,"mixOperation":"darken","warpx":0.088,"warpy":0.05725});

        updateObject(settingsShadowFloor, {"x":2610.25,"y":9095.25,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.6221,"mixOperation":"hard-light","warpx":0.08785,"warpy":0.04895});

        updateObject(settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":1,"floorPlaneOpacity":0.34500000000000003});

        callForBake();
    },

    setup_b: function() {

        updateObject(settingsShadowOne, {"x":-1977.5,"y":7860,"z":7860,"power":0.5955,"shelfGap":76.815,"iterations":14,"cameraz":262.965,"alpha":0.2414,"mixOperation":"overlay","warpx":0.08495,"warpy":0.05725});

        updateObject(settingsShadowTwo, {"x":-714.5,"y":12861.25,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.22510000000000002,"mixOperation":"hard-light","warpx":0.088,"warpy":0.05725});

        updateObject(settingsShadowFloor, {"x":-2639.25,"y":9404,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.34640000000000004,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.047150000000000004});

        updateObject(settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.28});

        callForBake();
    },

    setup_c: function() {

    
        updateObject(settingsShadowOne, {"x":2758.25,"y":7860,"z":7860,"power":0.5955,"shelfGap":505.285,"iterations":14,"cameraz":262.965,"alpha":0.1173,"mixOperation":"overlay","warpx":0.08495,"warpy":0.05725});

        updateObject(settingsShadowTwo, {"x":-714.5,"y":7494,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.2639,"mixOperation":"hard-light","warpx":0.088,"warpy":0.05725});
        
        updateObject(settingsShadowFloor, {"x":2919.25,"y":9404,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.34640000000000004,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516});

        updateObject(settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.28});

        callForBake();


    },

    setup_d: function() {


        updateObject(settingsShadowOne, {"x":2758.25,"y":7860,"z":7860,"power":0.5955,"shelfGap":505.285,"iterations":14,"cameraz":262.965,"alpha":0.1173,"mixOperation":"overlay","warpx":0.08495,"warpy":0.05725});
        
        updateObject(settingsShadowTwo, {"x":-4000,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.2752,"mixOperation":"hard-light","warpx":0.088,"warpy":0.05725});
        
        updateObject(settingsShadowFloor, {"x":2919.25,"y":9404,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.34640000000000004,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516});
        
        updateObject(settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.28});

        callForBake();

    },

    setup_d2: function() {

        updateObject( settingsShadowOne, {"x":2758.25,"y":7860,"z":7860,"power":0.5955,"shelfGap":505.285,"iterations":14,"cameraz":262.965,"alpha":0.1173,"mixOperation":"overlay","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":-83.25,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.28650000000000003,"mixOperation":"hard-light","warpx":0.088,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":-2924.5,"y":9404,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.2752,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.28});
        callForBake();
    },


    setup_b2: function() {


        updateObject( settingsShadowOne, {"x":2758.25,"y":7860,"z":7860,"power":0.5955,"shelfGap":505.285,"iterations":14,"cameraz":262.965,"alpha":0.1173,"mixOperation":"overlay","warpx":0.08495,"warpy":0.05725}); 
        updateObject( settingsShadowTwo, {"x":-4000,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.2752,"mixOperation":"hard-light","warpx":0.088,"warpy":0.05725}); 
        updateObject( settingsShadowFloor, {"x":-4000,"y":9404,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.34640000000000004,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); 
        updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.28});


        callForBake();

    },

    setup_b2d: function() {


    updateObject( settingsShadowOne, {"x":-2924.5,"y":7860,"z":7860,"power":0.5955,"shelfGap":505.285,"iterations":14,"cameraz":262.965,"alpha":0.1173,"mixOperation":"overlay","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":-2609,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.28650000000000003,"mixOperation":"hard-light","warpx":0.088,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":-2924.5,"y":9404,"z":10330.5,"power":0.3178,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":0.2752,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.28});
    callForBake();

    },


    setup_b2d2Blend: function() {
        updateObject( settingsShadowOne, {"x":-1661.75,"y":7860,"z":7860,"power":0.9338000000000001,"shelfGap":257.225,"iterations":14,"cameraz":262.965,"alpha":0.1368,"mixOperation":"multiply","warpx":0.08495,"warpy":0.05725}); 
        updateObject( settingsShadowTwo, {"x":3845.5,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.28650000000000003,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.6406000000000001,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":1,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":255,"g":255,"b":255},"backgroundColorAlpha":1,"floorColor":{"r":255,"g":255,"b":255},"floorColorAlpha":0.31,"floorPlaneOpacity":0.085});
        callForBake();
    },

    setup_WhiteCandidate: function() {
        updateObject( settingsShadowOne, {"x":-1661.75,"y":7860,"z":7860,"power":0.9338000000000001,"shelfGap":257.225,"iterations":14,"cameraz":262.965,"alpha":0.1368,"mixOperation":"multiply","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":3845.5,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.28650000000000003,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.6406000000000001,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":0.8051,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.056400000000000006}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":255,"g":255,"b":255},"backgroundColorAlpha":1,"floorColor":{"r":255,"g":255,"b":255},"floorColorAlpha":0.75,"floorPlaneOpacity":0.185});


        /*

         updateObject( settingsShadowOne, {"x":-1977.5,"y":7860,"z":7860,"power":1.0465,"shelfGap":144.47,"iterations":14,"cameraz":262.965,"alpha":1,"mixOperation":"copy","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":4463.25,"y":12545.5,"z":7860,"power":1.0016,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.7324,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.0558}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.7082,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":0.7375,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.085,"backgroundColor":{"r":235.00000000000003,"g":235.00000000000003,"b":235.00000000000003},"backgroundColorAlpha":0.735,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.085})

         */

        updateObject( settingsShadowOne, {"x":-1661.75,"y":7860,"z":7860,"power":1.0465,"shelfGap":257.225,"iterations":14,"cameraz":262.965,"alpha":0.1368,"mixOperation":"copy","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":3845.5,"y":12545.5,"z":7860,"power":0.7369,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.9405,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.7082,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":0.7375,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.085,"backgroundColor":{"r":242.5,"g":242.5,"b":242.5},"backgroundColorAlpha":0.445,"floorColor":{"r":255,"g":255,"b":255},"floorColorAlpha":0.31,"floorPlaneOpacity":0.085});

        callForBake();
    },
    setup_WhiteCandidateNew: function() {
        //updateObject( settingsShadowOne, {"x":-1661.75,"y":7860,"z":7860,"power":0.9338000000000001,"shelfGap":257.225,"iterations":14,"cameraz":262.965,"alpha":0.1368,"mixOperation":"multiply","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":3845.5,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.28650000000000003,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.6406000000000001,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":0.8051,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.056400000000000006}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":255,"g":255,"b":255},"backgroundColorAlpha":1,"floorColor":{"r":255,"g":255,"b":255},"floorColorAlpha":0.75,"floorPlaneOpacity":0.185})

        //updateObject( settingsShadowOne, {"x":-1661.75,"y":7860,"z":7860,"power":1.0465,"shelfGap":257.225,"iterations":14,"cameraz":262.965,"alpha":0.1368,"mixOperation":"copy","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":3845.5,"y":12545.5,"z":7860,"power":0.7369,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.9405,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.7082,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":0.7375,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.085,"backgroundColor":{"r":242.5,"g":242.5,"b":242.5},"backgroundColorAlpha":0.445,"floorColor":{"r":255,"g":255,"b":255},"floorColorAlpha":0.31,"floorPlaneOpacity":0.085})
        updateObject( settingsShadowOne, {"x":-1661.75,"y":7860,"z":7860,"power":0.9338000000000001,"shelfGap":257.225,"iterations":14,"cameraz":262.965,"alpha":0.1368,"mixOperation":"multiply","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":3845.5,"y":12545.5,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.28650000000000003,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.6406000000000001,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":1,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.0516}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":220,"g":220,"b":220},"backgroundColorAlpha":1,"floorColor":{"r":220,"g":220,"b":220},"floorColorAlpha":0.31,"floorPlaneOpacity":0.125});
        callForBake();
    }
};
    /*


}
}
/*
var settingsShadowOne = {"x":-2293.25,"y":7860,"z":7860,"power":0.5053,"shelfGap":76.815,"iterations":14,"cameraz":262.965,"alpha":0.3428,"mixOperation":"darken","warpx":0.088,"warpy":0.05725};var settingsShadowTwo = {"x":548.25,"y":12861.25,"z":7860,"power":0.5955,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.6924,"mixOperation":"darken","warpx":0.088,"warpy":0.05725};var settingsShadowFloor = {"x":2610.25,"y":9095.25,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":880.58,"alpha":0.6221,"mixOperation":"hard-light","warpx":0.088,"warpy":0.04725};var settingsScene = {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":1,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":1,"floorPlaneOpacity":0.37}


settingsShadowOne = {"x":-1977.5,"y":7860,"z":7860,"power":0.5955,"shelfGap":76.815,"iterations":14,"cameraz":262.965,"alpha":0.2414,"mixOperation":"overlay","warpx":0.08495,"warpy":0.05725};
settingsShadowTwo = {"x":-714.5,"y":12861.25,"z":7860,"power":0.6486000000000001,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.22510000000000002,"mixOperation":"hard-light","warpx":0.088,"warpy":0.05725};
settingsShadowFloor = {"x":-2639.25,"y":9404,"z":10330.5,"power":0.3178,"shelfGap":207.48000000000002,"iterations":14,"cameraz":0,"alpha":0.34640000000000004,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.047150000000000004};
settingsScene = {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":202.50000000000003,"g":202.50000000000003,"b":202.50000000000003},"backgroundColorAlpha":0.795,"floorColor":{"r":217.5,"g":217.5,"b":217.5},"floorColorAlpha":0.31,"floorPlaneOpacity":0.28}
*/


/*

 updateObject( settingsShadowOne, {"x":1811.25,"y":7860,"z":7860,"power":0.9338000000000001,"shelfGap":212.12,"iterations":14,"cameraz":262.965,"alpha":0.1368,"mixOperation":"multiply","warpx":0.08495,"warpy":0.05725}); updateObject( settingsShadowTwo, {"x":2442.5,"y":12545.5,"z":7860,"power":0.9112,"shelfGap":0,"iterations":14,"cameraz":262.965,"alpha":0.1399,"mixOperation":"hard-light","warpx":0.0877,"warpy":0.05725}); updateObject( settingsShadowFloor, {"x":4652.5,"y":9404,"z":10330.5,"power":0.6406000000000001,"shelfGap":0,"iterations":14,"cameraz":0,"alpha":0.8051,"mixOperation":"hard-light","warpx":0.0858,"warpy":0.056400000000000006}); updateObject( settingsScene, {"quality":8,"background":true,"mixOperation":"none","wallColorAlpha":0,"shadowPlaneOpacity":0.275,"backgroundColor":{"r":237.5,"g":237.5,"b":237.5},"backgroundColorAlpha":0.6900000000000001,"floorColor":{"r":255,"g":255,"b":255},"floorColorAlpha":0.75,"floorPlaneOpacity":0.185})

 */




var copy = { presets: ""};

let shadowSourceOne = gui.addFolder("First Shadow source");
let shadowSourceTwo = gui.addFolder("Second Shadow source");
let shadowSourceFloor = gui.addFolder("Floor Shadow source");

let sceneSettings = gui.addFolder("Scene settings");

let save = gui.add(copy, 'presets').listen();

let values = [];

values.push(shadowSourceOne.add(settingsShadowOne, 'x').min(-4000).max(24000).step(0.25));
values.push(shadowSourceOne.add(settingsShadowOne, 'y').min(-4000).max(24000).step(0.25));
values.push(shadowSourceOne.add(settingsShadowOne, 'z').min(-4000).max(24000).step(0.5));
values.push(shadowSourceOne.add(settingsShadowOne, 'power').min(0).max(2).step(0.0001));
values.push(shadowSourceOne.add(settingsShadowOne, 'alpha').min(0).max(1).step(0.0001));

values.push(shadowSourceTwo.add(settingsShadowTwo, 'x').min(-4000).max(24000).step(0.25));
values.push(shadowSourceTwo.add(settingsShadowTwo, 'y').min(-4000).max(24000).step(0.25));
values.push(shadowSourceTwo.add(settingsShadowTwo, 'z').min(-4000).max(24000).step(0.5));
values.push(shadowSourceTwo.add(settingsShadowTwo, 'power').min(0).max(2).step(0.0001));
values.push(shadowSourceTwo.add(settingsShadowTwo, 'alpha').min(0).max(1).step(0.0001));


values.push(shadowSourceFloor.add(settingsShadowFloor, 'x').min(-4000).max(24000).step(0.25));
values.push(shadowSourceFloor.add(settingsShadowFloor, 'y').min(-4000).max(24000).step(0.25));
values.push(shadowSourceFloor.add(settingsShadowFloor, 'z').min(-4000).max(24000).step(0.5));
values.push(shadowSourceFloor.add(settingsShadowFloor, 'power').min(0).max(2).step(0.0001));
values.push(shadowSourceFloor.add(settingsShadowFloor, 'alpha').min(0).max(1).step(0.0001));


values.push(shadowSourceOne.add(settingsShadowOne, 'shelfGap').min(0).max(2000).step(0.005));
values.push(shadowSourceOne.add(settingsShadowOne, 'cameraz').min(0).max(7000).step(0.005));


values.push(shadowSourceOne.add(settingsShadowOne, 'warpx').min(0).max(0.5).step(0.00005));
values.push(shadowSourceOne.add(settingsShadowOne, 'warpy').min(0).max(0.5).step(0.00005));



values.push(shadowSourceTwo.add(settingsShadowTwo, 'shelfGap').min(0).max(2000).step(0.005));
values.push(shadowSourceTwo.add(settingsShadowTwo, 'cameraz').min(0).max(7000).step(0.005));


values.push(shadowSourceTwo.add(settingsShadowTwo, 'warpx').min(0).max(0.5).step(0.00005));
values.push(shadowSourceTwo.add(settingsShadowTwo, 'warpy').min(0).max(0.5).step(0.00005));


values.push(shadowSourceFloor.add(settingsShadowFloor, 'shelfGap').min(0).max(2000).step(0.005));
values.push(shadowSourceFloor.add(settingsShadowFloor, 'cameraz').min(0).max(7000).step(0.005));

values.push(shadowSourceFloor.add(settingsShadowFloor, 'warpx').min(0).max(0.5).step(0.00005));
values.push(shadowSourceFloor.add(settingsShadowFloor, 'warpy').min(0).max(0.5).step(0.00005));


values.push(shadowSourceOne.add(settingsShadowOne, 'mixOperation', mixOpertations));
values.push(shadowSourceTwo.add(settingsShadowTwo, 'mixOperation', mixOpertations));
values.push(shadowSourceFloor.add(settingsShadowFloor, 'mixOperation', mixOpertations));

values.push(sceneSettings.addColor(settingsScene, 'backgroundColor'));
values.push(sceneSettings.add(settingsScene, 'backgroundColorAlpha').min(0).max(1).step(0.005));
values.push(sceneSettings.add(settingsScene, 'shadowPlaneOpacity').min(0).max(1).step(0.005));


//values.push(sceneSettings.addColor(settingsScene, 'wallColor'));
values.push(sceneSettings.addColor(settingsScene, 'floorColor'));
values.push(sceneSettings.add(settingsScene, 'floorColorAlpha').min(0).max(1).step(0.005));
values.push(sceneSettings.add(settingsScene, 'floorPlaneOpacity').min(0).max(1).step(0.005));


values.push(sceneSettings.add(settingsScene, 'shadowPower').min(0).max(1).step(0.005));


gui.add(presets, 'setup_a').name('Presets A');
gui.add(presets, 'setup_b').name('Presets B');
gui.add(presets, 'setup_c').name('Presets C');
gui.add(presets, 'setup_d').name('Presets D');

gui.add(presets, 'setup_d2').name('Presets D2');
gui.add(presets, 'setup_b2').name('Presets B2');

gui.add(presets, 'setup_b2d').name('Presets B2 / D2');
gui.add(presets, 'setup_b2d2Blend').name('Presets B2 / D2 NEW BLEND');

gui.add(presets, 'setup_WhiteCandidate').name('Presets WhiteCandidate');
gui.add(presets, 'setup_WhiteCandidateNew').name('Presets New');



//values.push(sceneSettings.add(settingsScene, 'wallColorAlpha').min(0).max(1).step(0.005));
//sceneSettings.add(settingsScene, 'color0');

window.datdat = [
    settingsShadowOne,
    settingsShadowTwo,
    settingsShadowFloor,
    settingsScene
];

window.backup = function renderValues() {

    let names = [
        'settingsShadowOne',
        'settingsShadowTwo',
        'settingsShadowFloor',
        'settingsScene'];
       settingsShadowOne, 
     copy.presets = window.datdat.map((v,k)=>" updateObject( "+names[k]+", " +JSON.stringify(v) +")").join(";");
};


function callForBake() {
    PubSub.publish('shelfChanged');
    window.backup();
}

values.map((value) => { 
    value.onChange(callForBake);
    value.listen();
    window.backup();
});

presets.setup_WhiteCandidateNew();

export { 
    settingsShadowOne,
    settingsShadowTwo,
    settingsShadowFloor,
    settingsScene
};