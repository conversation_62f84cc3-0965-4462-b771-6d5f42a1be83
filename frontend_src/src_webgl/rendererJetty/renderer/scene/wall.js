import * as THREE from 'three';

const createPlane = (materialSettings, planeWidth, planeHeight, s) => {

    let material = new THREE.MeshBasicMaterial(materialSettings);
    let geo = new THREE.PlaneGeometry(planeWidth, planeHeight, s, s);
    let plane = new THREE.Mesh(geo, material);

    return { plane };
};

export class Wall {

    constructor({

        scene,
        source,

        planeWidth = 6000,
        planeHeight = 4000,
        background = false,
        backgroundColor =  0xf5512c,
        isFloor = false,
        s = 10 // 's' is for Subdivision
    }) {

        this.width = planeWidth;
        this.height = planeHeight;

        if(isFloor) {
                
            this.shadowTexture = new THREE.Texture(source);
            this.wallMaterial = new THREE.MeshBasicMaterial({ 
                map: this.shadowTexture,
                side: THREE.DoubleSide
            });

        } else {
            this.shadowTexture = new THREE.Texture(source);
            this.wallMaterial = new THREE.MeshBasicMaterial({ 
                map: this.shadowTexture
            });
        }

        let wallPlane = new THREE.PlaneGeometry(planeWidth, planeHeight, s, s);
        let wall = new THREE.Mesh(wallPlane, this.wallMaterial);

        this.wallMaterial.transparent = true;
        this.wallMaterial.opacity = 0.25;

        wall.position.z = 1;
        wall.renderOrder = 1;
        wall.position.y += planeHeight/2
        scene.add(wall);
        this.mesh = wall;
    
        if (background) {
            /*
            let { plane } = createPlane({ color: backgroundColor }, planeWidth, planeHeight, s);

            plane.position.z = -2;
            plane.renderOrder = -1;
            plane.position.y += planeHeight/2;
            scene.add(plane);
            this.background = plane;
            */
        }

        if(isFloor) {
            wall.rotation.x = Math.PI / 2;
            wall.position.y -= planeHeight/2;
            wall.position.z += planeHeight/2 - 6;
            wall.name = 'floor';
        } else {
            wall.position.z -= 3;
            wall.name = 'wall';
        }

        return this;
    }

    changeColor(newColor) {
        this.background.material.color.setHex(newColor);
    }

    updateMaterial() {
        if(this.wallMaterial.map)
            this.wallMaterial.map.needsUpdate = true; 
    }
}