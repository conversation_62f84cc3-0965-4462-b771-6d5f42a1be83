import {
    Ray<PERSON>,
    Vector3,
} from 'three';
import { TweenLite, Power1, Power3 } from 'gsap';

import { <PERSON>yRenderer } from '@src_webgl/rendererJetty/JettyRenderer';
import { customSettings } from '@src_webgl/tylkoCamera/tylko-camera-settings';
import { Build3d } from '../build3dJetty/build3d';

// TODO ABSTRACTION LAYER FOR CAMERA CONFIG BY COLLECTION OR CATEGORY TYPE NOT JUST CONFIGURATOR TYPE
const overrideCameraArg = (args) => Object.assign(args, { 
    cameraSettings: args.geom.configurator_params.additional_parameters.collection_type === 'Desk'
        ? customSettings.desk
        : customSettings.cplus
})
class CplusRenderer extends JettyRenderer {
    constructor(args) {
        super(overrideCameraArg(args));
        this.timeoutTime = 50;
        this.animationTiming = 0.3;

        this.setupCanvasInteraction();
        this.setupListeners();
        this.setMobileDefaultSnap();
    }

    updateCoordinatesForButtons() {
        this.dispatcher('ui/UPDATE_COORDINATES_FOR_BUTTONS', this.getCoordinatesForButtons());
    }

    setupListeners() {
        this.container.addEventListener('mousedown', () => (this.dispatcher('interactions/SET_IS_ORBIT_MOVING', true)), false);
        document.body.addEventListener('mouseup', () => {
            setTimeout(() => {
                this.updateCoordinatesForButtons();
                this.dispatcher('interactions/SET_IS_ORBIT_MOVING', false);
            }, this.timeoutTime);
        }, false);
        this.container.addEventListener('touchstart', () => (this.dispatcher('interactions/SET_IS_ORBIT_MOVING', true)), false);
        document.body.addEventListener('touchend', () => {
            setTimeout(() => {
                this.updateCoordinatesForButtons();
                this.dispatcher('interactions/SET_IS_ORBIT_MOVING', false);
            }, this.timeoutTime);
        }, false);
        this.setUpScreenshotShortcut();
    }

    setTabSpecificView(tab) {
        if (this.currentView === 'shelf') {
            this.tylkoCamera.setShelfMode();
        } else {
            this.tylkoCamera.setComponentMode();
        }

        switch (tab) {
        case 'depth':
            this.tylkoCamera.setViewFinal('deepSide');
            break;
        case 'base':
            this.tylkoCamera.setViewFinal('lowSide');
            break;
        case 'style':
        case 'sections':
            this.tylkoCamera.setViewFinal('front');
            break;
        default:
            this.tylkoCamera.setViewFinal('side');
        }
    }

    setMobileDefaultSnap() {
        if (this.store.ui.isMobile) this.tylkoCamera.setViewFinal('side');
    }

    setupCanvasInteraction() {
        let selectedObject = null;
        const raycaster = new Raycaster();
        const mouseVector = new Vector3();
        const getIntersects = (x, y) => {
            const vecX = (x / this.container.offsetWidth) * 2 - 1;
            const vecY = -(y / this.container.offsetHeight) * 2 + 1;
            mouseVector.set(vecX, vecY, 0);
            raycaster.setFromCamera(mouseVector, this.tylkoCamera.camera);
            return raycaster.intersectObject(this.scene, true);
        };

        this.container.addEventListener('mousemove', event => {
            event.preventDefault();
            if (selectedObject && selectedObject.name.split(':')[0] === 'hoverBox') {
                selectedObject.material.opacity = 0;
                selectedObject = null;
            }
            const intersects = getIntersects(event.layerX, event.layerY);
            if (intersects.length > 0) {
                const res = intersects.filter(res => res && res.object && res.object.name.split(':')[0] === 'hoverBox')[0];
                if (res && res.object) {
                    if (!this.store.ui.isMobile) {
                        selectedObject = res.object;
                        selectedObject.material.opacity = 0.2;
                    }
                    this.dispatcher('UPDATE_HOVER_COMPONENT', res.object.name.split(':')[1]);
                    if (!this.store.activeComponent && !this.store.dimensions.show && !this.store.renderer.isDoorsOpen) {
                        this.openDoorAndDrawersForComponent(res.object.name.split(':')[1], 0.5);
                    }
                } else {
                    this.dispatcher('UPDATE_HOVER_COMPONENT', null);
                    if (!this.store.activeComponent && !this.store.dimensions.show && !this.store.renderer.isDoorsOpen) {
                        this.closeAllDoorsAndDrawers();
                    }
                }
            }
        }, false);

        this.container.addEventListener('click', event => {
            event.preventDefault();
            if (selectedObject && selectedObject.name.split(':')[0] === 'hoverBox') {
                selectedObject.material.opacity = 0;
                selectedObject = null;
            }
            const intersects = getIntersects(event.layerX, event.layerY);
            if (intersects.length > 0) {
                const res = intersects.filter(res => res && res.object && res.object.name.split(':')[0] === 'hoverBox')[0];
                if (res && res.object) {
                    if (!this.store.ui.isMobile) {
                        selectedObject = res.object;
                        selectedObject.material.opacity = 0.2;
                    }
                    this.dispatcher(
                        'ACTIVE_COMPONENT',
                        res.object.name.split(':')[1],
                    );
                    if (this.store.dimensions.show) {
                        this.dispatcher('dimensions/UPDATE_SHOW_DIMENSIONS', false);
                    } else {
                        this.openDoorAndDrawersForComponent(res.object.name.split(':')[1], 1);
                    }
                } else {
                    this.dispatcher('DEACTIVATE_COMPONENT');
                    if (!this.store.ui.isMobile) this.dispatcher('renderer/RENDERER_SET_SHELF_VIEW');
                    this.closeAllDoorsAndDrawers();
                }
            }
        }, false);
    }

    updateGeometry(geom, {
        width, height, depth, cameraUpdate,
    }) {
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.geom = geom;

        this.updateItems(geom);
        this.build3d.setScene(this.scene);
        this.build3d.rebuildWallsFromJson(geom, this.scene, { width, height, depth });
        this.rendererProxy.bake({ width, height, depth });
        if (!cameraUpdate) return;

        const horizontals = [...this.geom.horizontals.map(h => h.y1)];

        this.renderer.render(this.scene, this.tylkoCamera.camera);
        if (this.currentView === 'shelf') {
            this.tylkoCamera.updateGeometry(
                { x: -width / 2, y: 0, z: 0 },
                { x: width / 2, y: Math.max(...horizontals), z: 410 },
                false,
            );
        }
        if (this.store.dimensions.show) this.dispatcher('dimensions/UPDATE_DIMENSIONS', true);
    }

    openDoorAndDrawersForComponent(id, angle) {
        const drawersToOpen = this.scene.children
            .filter(res => res.name === `drawer_group:${id}`);
        const drawersToClose = this.scene.children
            .filter(res => res.name !== `drawer_group:${id}` && res.name.split(':')[0] === 'drawer_group');
        const doorsToOpen = this.scene.children
            .filter(res => res.name === `door_group:${id}:0` || res.name === `door_group:${id}:1`);
        const doorsToClose = this.scene.children
            .filter(res => (res.name !== `door_group:${id}:0` && res.name !== `door_group:${id}:1`) && res.name.split(':')[0] === 'door_group');

        this.openDrawers(drawersToOpen, angle);
        this.openDoors(doorsToOpen, angle);
        this.closeDrawers(drawersToClose);
        this.closeDoors(doorsToClose);
    }

    openDoors(doorsToOpen, angle) {
        doorsToOpen.forEach(i => {
            const rotationD = parseInt(i.name.split(':')[2], 10) ? -0.7 : 0.7;
            if (i.rotation.y === 0.7 || i.rotation.y === -0.7 || i.rotation.y >= 0.7 * angle || i.rotation.y <= -0.7 * angle) return;
            const target = { x: i.rotation.x, y: i.rotation.y + rotationD * angle, z: i.rotation.z };
            TweenLite.to(i.rotation, this.animationTiming, {
                ease: Power1.easeIn, x: target.x, y: target.y, z: target.z,
            });
        });
    }

    openDrawers(drawersToOpen, angle) {
        const animationOffset = (this.depth - 60) * angle;
        drawersToOpen.forEach(i => {
            if (i.position.z > this.depth) return;
            const target = { x: i.position.x, y: i.position.y, z: i.position.z + animationOffset };
            TweenLite.to(i.position, this.animationTiming, {
                ease: Power1.easeIn, x: target.x, y: target.y, z: target.z,
            });
        });
    }

    closeDoors(doorsToClose) {
        doorsToClose.forEach(i => {
            if (i.rotation.y === 0) return;
            const target = { x: i.rotation.x, y: 0, z: i.rotation.z };
            TweenLite.to(i.rotation, this.animationTiming, {
                ease: Power3.easeOut, x: target.x, y: target.y, z: target.z,
            });
        });
    }

    closeDrawers(drawersToClose) {
        drawersToClose.forEach(i => {
            if (i.position.z < this.depth || i.position.z <= 0) return;
            const target = { x: i.position.x, y: i.position.y, z: this.depth - 8 };
            TweenLite.to(i.position, this.animationTiming, {
                ease: Power3.easeOut, x: target.x, y: target.y, z: target.z,
            });
        });
    }

    openAllDoorsAndDrawers() {
        const drawersToOpen = this.scene.children.filter(res => res.name.split(':')[0] === 'drawer_group');
        const doorsToOpen = this.scene.children.filter(res => (res.name.split(':')[0]) === 'door_group');

        this.openDrawers(drawersToOpen, 1);
        this.openDoors(doorsToOpen, 1);
    }

    closeAllDoorsAndDrawers() {
        const drawersToClose = this.scene.children.filter(res => res.name.split(':')[0] === 'drawer_group');
        const doorsToClose = this.scene.children.filter(res => (res.name.split(':')[0]) === 'door_group');

        this.closeDrawers(drawersToClose);
        this.closeDoors(doorsToClose);
    }
}

async function initCplusRenderer(args) {
    const {
        shelf_type,
        material,
    } = args.store.commonFurniture.item;

    const build3d = new Build3d({
        elements_in: args.loader.getElements(),
        context_in: {},
        isMobile: window.cstm.is_mobile,
        initialShelfType: shelf_type,
        initialShelfMaterial: material,
    }, false);
    return new CplusRenderer({ ...args, build3d });
}

export { initCplusRenderer as initRenderer };
