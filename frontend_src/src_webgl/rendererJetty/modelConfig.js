
const preparePreloader = function(colorName) {
    const preloader_config = {};
    preloader_config['2-vertical_edge.obj'] = ['vertical', `${colorName}-vert`];
    preloader_config['2-horizontal_edge.obj'] = ['horizontal', `${colorName}-hori`];
    preloader_config['2-horizontal_edge_plug.obj'] = ['horizontal-plug', `${colorName}-hori`];
    preloader_config['2-support.obj'] = ['support', `${colorName}-support`];
    preloader_config['2-support_drawer.obj'] = ['support-drawer', `${colorName}-support-drawer`];
    preloader_config['2-shadow_box.obj'] = ['shadow', `${colorName}-shadowbox`];
    preloader_config['2-shadow_box_left.obj'] = ['shadow-left', `${colorName}-shadowbox`];
    preloader_config['2-shadow_box_right.obj'] = ['shadow-right', `${colorName}-shadowbox`];
    preloader_config['2-left_right.obj'] = ['left-right', `${colorName}-vert`];
    preloader_config['2-top_bottom.obj'] = ['top-bottom', `${colorName}-hori`];
    preloader_config['shadow_box_desk.obj'] = ['shadow-bottom', `${colorName}-shadowbox`];

    preloader_config['cast_shadow_center2.obj'] = ['cast-shadow-center', 'cast_shadow'];
    preloader_config['cast_shadow_left.obj'] = ['cast-shadow-right', 'cast_shadow'];
    preloader_config['cast_shadow_right.obj'] = ['cast-shadow-left', 'cast_shadow'];

    // S+ LONG_LEGS SHADOW
    preloader_config['shadow_leg_L.obj'] = ['cast-shadow-leg-left', 'cast_shadow_legs'];
    preloader_config['shadow_leg_R.obj'] = ['cast-shadow-leg-right', 'cast_shadow_legs'];
    preloader_config['shadow_leg_M.obj'] = ['cast-shadow-leg-middle', 'cast_shadow_legs'];
    preloader_config['shadow_between_legs.obj'] = ['cast-shadow-leg-between', 'cast_shadow_legs'];

    preloader_config['doors.obj'] = ['door', 'doors_open', 'doors_close'];
    preloader_config['drawers.obj'] = ['drawer_front', 'doors_open'];

    // VENEER SPECIFIC
    preloader_config['doors-fornir.obj'] = ['fdoors', `${colorName}_fdoors`];
    preloader_config['doors-fornir-big.obj'] = ['fdoors_big', `${colorName}_fdoors`];
    preloader_config['drawers-fornir.obj'] = ['fdrawer', `${colorName}_fdrawer`];
    preloader_config['drawers-fornir-big.obj'] = ['fdrawer_big', `${colorName}_fdrawer`];
    preloader_config['fornir_drawer_handle.obj'] = ['fhandle', `${colorName}_fdrawer`];
    preloader_config['grommet_plug_veneer.obj'] = ['grommet_plug_veneer', `${colorName}-grommet`];


    preloader_config['leg.obj'] = ['leg', 'leg_texture'];
    preloader_config['typek.obj'] = ['typek'];

    // SPLUS SPECIFIC
    preloader_config['leg_s_plus.obj'] = ['leg_s_plus', `${colorName}_leg`];
    preloader_config['insert.obj'] = ['insert', `${colorName}-insert`];
    preloader_config['grommet_plug.obj'] = ['grommet_plug', `${colorName}-hori`];


    // HANDLES
    preloader_config['handle_big.obj'] = ['handle_big', 'doors_open', 'doors_close'];
    preloader_config['handle_small.obj'] = ['handle_small', 'doors_open', 'doors_close'];
    preloader_config['handle_drawer.obj'] = ['handle_drawer', 'doors_open', 'doors_close'];
    preloader_config['handle_short_left.obj'] = ['handle_short_left', 'handle_short_left_texture'];
    preloader_config['handle_short_left_shadow.obj'] = ['handle_short_left_shadow', 'handle_short_left_texture'];


    preloader_config['plinth_mbar.obj'] = ['plinth_mbar', `${colorName}_plinth`];
    preloader_config['plinth_m.obj'] = ['plinth_m', `${colorName}_plinth`];
    preloader_config['plinth_l_narrow.obj'] = ['plinth_l_narrow', `${colorName}_plinth`];
    preloader_config['plinth_r_narrow.obj'] = ['plinth_r_narrow', `${colorName}_plinth`];

    preloader_config['shadow_plinth_mbar.obj'] = ['shadow_plinth_m_bar', 'shadow_plinth'];
    preloader_config['shadow_plinth_l.obj'] = ['shadow_plinth_l', 'shadow_plinth'];
    preloader_config['shadow_plinth_l_medium.obj'] = ['shadow_plinth_l_medium', 'shadow_plinth'];
    preloader_config['shadow_plinth_l_medium_end.obj'] = ['shadow_plinth_l_medium_end', 'shadow_plinth'];
    preloader_config['shadow_plinth_l_narrow.obj'] = ['shadow_plinth_l_narrow', 'shadow_plinth'];
    preloader_config['shadow_plinth_m.obj'] = ['shadow_plinth_m', 'shadow_plinth'];
    preloader_config['shadow_plinth_r.obj'] = ['shadow_plinth_r', 'shadow_plinth'];
    preloader_config['shadow_plinth_r_medium.obj'] = ['shadow_plinth_r_medium', 'shadow_plinth'];
    preloader_config['shadow_plinth_r_medium_end.obj'] = ['shadow_plinth_r_medium_end', 'shadow_plinth'];
    preloader_config['shadow_plinth_r_narrow.obj'] = ['shadow_plinth_r_narrow', 'shadow_plinth'];
    
    preloader_config['shadow_wall.obj'] = ['shadow_wall', 'cast_shadow_wall'];

    return preloader_config;
};
// eslint-disable-next-line import/prefer-default-export
export { preparePreloader as ObjectsConfig };
