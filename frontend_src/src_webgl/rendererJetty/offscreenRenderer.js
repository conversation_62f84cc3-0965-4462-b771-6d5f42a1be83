import {
    Scene,
    PerspectiveCamera,
    Color,
    Group,
} from 'three';
import { PMREMGenerator } from 'three/src/extras/PMREMGenerator';
import { RGBELoader } from '../@libs/RGBELoader';
import { placeItems, preloadAllModelsForSet } from '../@libs/lib-items';

import { getDefinitionForMaterial } from '../presets/materials';
import { Build3d } from '../build3dJetty/build3d';
import { Renderer } from './shadowRenderer';
import { TylkoCamera } from '../tylkoCamera/tylko-camera';
import { customSettings } from '../tylkoCamera/tylko-camera-settings';

class OffscreenRenderer {
    constructor({
        domElement = false,
        width,
        height,
        loader,
        geom,
        shelfType = 0,
        shelfColor = 0,
        renderScene = true,
        cameraSettingsKey = 'grid',
        configurator_type,
        adaptCameraSettings = false,
    }) {
        this.cameraSettings = adaptCameraSettings ? this.adaptCameraSettings(customSettings[cameraSettingsKey], geom) : customSettings[cameraSettingsKey];
        this.domElement = domElement;
        this.loader = loader;
        this.canvasWidth = width;
        this.canvasHeight = height;
        this.width = null;
        this.height = null;
        this.depth = null;
        this.elements = this.loader.getElements();
        this.geom = geom;
        this.shelfColor = shelfColor;
        this.shelfType = shelfType;
        this.renderScene = renderScene;
        this.scene = new Scene();

        if (this.domElement) {
            this.container = domElement;
        }


        this.build3d = new Build3d({
            elements_in: this.elements,
            context_in: {},
            isMobile: window.cstm.is_mobile,
            isOffscreen: true,
            initialShelfType: shelfType,
            initialShelfMaterial: shelfColor,

        }, configurator_type === 1);

        this.currentView = 'shelf';
        this.shouldRender = true;
        this.initialRender = true;
        this.isOrbitActive = false;
        this.isAnimating = false;
        this.cameraType = 'tylkoCamera';

        this.createTylkoCamera();
        this.initBuild3d();
        this.createShadowsRenderer();
    }

    loadEnvMap() {
        const pmremGenerator = new PMREMGenerator(this.renderer);
        const envLoader = new RGBELoader();
        if (this.hdrCubeRenderTarget) return;

        return new Promise(done => {
            envLoader.load('/r_static/ios/light.hdr', async hdrEquiRect => {
                this.hdrCubeRenderTarget = pmremGenerator.fromEquirectangular(hdrEquiRect);
                pmremGenerator.compileCubemapShader();
                await preloadAllModelsForSet(this.hdrCubeRenderTarget.texture);

                done();
            });
        });
    }

    getItemsGroup() {
        if (this.itemsGroup) {
            return this.itemsGroup;
        } else {
            const itemsGroup = new Group();
            this.itemsGroup = itemsGroup;
            this.getScene().add(itemsGroup);
            return itemsGroup;
        }
    }

    adaptCameraSettings(settings, geom) {
        const mapValue = (valueRange, referenceRange, referenceValue) => {
            const value = valueRange.min + (valueRange.max - valueRange.min) * (referenceValue - referenceRange.min) / (referenceRange.max - referenceRange.min);
            return Math.max(valueRange.min, Math.min(valueRange.max, value));
        };
        const topMargin = mapValue(settings.marginTopRange, settings.heightRange, geom.height);
        const phiValue = mapValue(settings.phiRange, settings.heightRange, geom.height);
        const adaptedSettings = {
            ...settings,
            geometryMargins: { ...settings.geometryMargins, top: topMargin },
            snapping: [{ ...settings.snapping[0], phi: phiValue }],
        };
        return adaptedSettings;
    }

    createSimpleCamera() {
        this.simpleCamera = new PerspectiveCamera(this.cameraSettings ? this.cameraSettings.fov : 20, this.canvasWidth / this.canvasHeight, 1, 10000);
        this.simpleCamera.position.setZ(9000);
        this.simpleCamera.name = 'simpleCamera';
        this.scene.add(this.simpleCamera);
    }

    getScene() {
        return this.scene;
    }

    setShelfView(geom, isMobile) {
        if (!isMobile) return;
        this.currentView = 'shelf';
        this.tylkoCamera.setShelfViewFinal(
            { x: -geom.width / 2, y: 0, z: 0 },
            { x: geom.width / 2, y: geom.height, z: 410 },
            true,
        );
        this.renderer.render(this.scene, this.simpleCamera);
    }

    getCamera() {
        return this.tylkoCamera;
    }

    updateCanvasSize({ canvasWidth, canvasHeight }) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        const camera = this.tylkoCamera.camera || this.simpleCamera;
        camera.aspect = this.canvasWidth / this.canvasHeight;
        camera.updateProjectionMatrix();
        this.renderer.setSize(this.canvasWidth, this.canvasHeight, false);
    }

    getContainer() {
        return this.container;
    }

    createTylkoCamera() {
        this.createSimpleCamera();
        this.tylkoCamera = new TylkoCamera({
            view: false,
            settings: this.cameraSettings,
        });
        // Settings - Animation + Offset
        this.tylkoCamera.noTransitionAnimation = false;
        this.tylkoCamera.shelfScreenEdgeOffset = {
            left: 80, right: 80, top: 0, bottom: 0,
        };
        this.tylkoCamera.componentScreenEdgeOffset = {
            left: 0, right: 0, top: 0, bottom: 0,
        };

        this.tylkoCamera.updateAspect(this.canvasWidth / this.canvasHeight);

        this.tylkoCamera.controls.addEventListener('render', () => {
            this.shouldRender = true;
        });

        this.tylkoCamera.controls.addEventListener('change', () => {
            this.renderer.render(this.scene, this.tylkoCamera.camera);
        });
    }

    updateCameraSetting(key) {
        this.tylkoCamera.updateSettings(customSettings[key]);
        this.tylkoCamera.controls.snapTo = [...customSettings[key].snapping];
    }

    initBuild3d() {
        // this.build3d.setDesignerMode(1);
        this.build3d.init3d();
        this.build3d.createFacePlaneForRaycasting();
        this.build3d.setScene(this.scene);
        this.build3d.setShelfType(this.shelfType, this.shelfColor);
        this.setColor(this.shelfColor, this.shelfType);
    }

    createShadowsRenderer() {
        const backgroundColor = this.cameraSettings ? this.cameraSettings.backgroundColor : null;

        const rendererProxy = new Renderer({
            devicePixelRatio: 3,
            renderScene: this.renderScene,
            textures: this.loader.getElements(),
            mobileMode: true,
            container: this.container,
        }, this.simpleCamera, this.build3d);
        this.rendereProx = rendererProxy;
        this.renderer = rendererProxy.threeRenderer;
        this.renderer.setClearColor(0xedf0f0, 1);
        this.renderer.setSize(this.canvasWidth, this.canvasHeight, false);
    }

    setColor(color, shelfType) {
        this.shelfColor = color;
        this.shelfType = shelfType;
        const [, colorName] = getDefinitionForMaterial(color, shelfType);
        this.build3d.setMaterialColor({ color, shelf_type: shelfType, skipTracking: true });
    }

    updateGeometry(geom, {
        width, height, depth = 320, cameraUpdate = true,
    }) {
        console.log('camera updateGeometry');
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.geom = geom;
        this.build3d.setScene(this.scene);
        this.build3d.setShelfType(geom.shelf_type);
        this.build3d.rebuildWallsFromJson(geom, this.scene, { width, height, depth });
        this.rendereProx.bake({ width, height, depth });
        const horizontals = [...this.geom.horizontals.map(h => h.y1)];
        if (this.currentView === 'shelf') {
            this.tylkoCamera.updateGeometry(
                { x: -width / 2, y: 0, z: -depth / 2 },
                { x: width / 2, y: Math.max(...horizontals), z: depth / 2 },
                true,
            );
        }

        if (window && window.location) {
             const urlParams = new URLSearchParams(window.location.search);
             const isItemsEnabled = urlParams.get('libitemsEnabled');

             if (isItemsEnabled){
               window.hdr_debug = this.hdrCubeRenderTarget;
               placeItems(this.getItemsGroup(), geom, this.hdrCubeRenderTarget ? this.hdrCubeRenderTarget.texture : null);
             }

        }
        this.renderer.render(this.scene, this.tylkoCamera.camera);
    }

    renderLoop() {
        if (this.shouldRender) {
            this.shouldRender = false;
            this.tylkoCamera.controls.update();
        }
        this.renderer.render(this.scene, this.tylkoCamera.camera);
        window.requestAnimationFrame(this.renderLoop.bind(this));
    }

    getScreenshotForCart() {
        this.updateCameraSetting('row');
        this.cameraSettings.snapping = [{ theta: -0.43, phi: 1.59 }];
        this.updateCanvasSize({ canvasWidth: 1280, canvasHeight: 960 });
        this.renderer.render(this.scene, this.tylkoCamera.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }

    getSreenShotForPralax() {
        this.updateCameraSetting('row');
        this.cameraSettings.snapping = [{ theta: -0.43, phi: 1.59 }];
        this.updateCanvasSize({ canvasWidth: 1280, canvasHeight: 960 });

        this.updateCanvasColor({ backgroundColor: 0x0000000, transparency: 0 });
        this.scene.background = null;

        this.renderer.render(this.scene, this.tylkoCamera.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }

    getScreenshotForMobileDim() {
        this.cameraSettings.snapping = [{ theta: 0, phi: 1.59 }];
        this.updateCameraSetting('rowDim');
        this.updateCanvasSize({ canvasWidth: window.innerWidth * 4, canvasHeight: window.innerHeight * 4 });
        this.renderer.render(this.scene, this.tylkoCamera.camera);
        this.tylkoCamera.camera.updateProjectionMatrix();
        return this.renderer.domElement.toDataURL('image/png');
    }


    updateCanvasColor({ backgroundColor, transparency }) {
        this.scene.background = new Color(backgroundColor);
        this.renderer.setClearColor(backgroundColor, transparency);
        this.rendereProx.setBackground({
            color: backgroundColor,
            transparency,
            renderScene: this.renderScene,
        });
    }

    getScreenshotForSetup({
        backgroundColor = 0xedf0f0,
        transparency = 0,
        cameraSettings = 'left_30',
        canvasWidth = 600,
        canvasHeight = 600,
    }) {
        const cameraPresetName = `feed_${cameraSettings}`;
        this.updateCameraSetting(cameraPresetName);
        this.updateCanvasSize({ canvasWidth, canvasHeight });
        this.updateCanvasColor({ backgroundColor, transparency });
        this.tylkoCamera.camera.updateProjectionMatrix();
        this.renderer.render(this.scene, this.tylkoCamera.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }

    getScene() {
        return this.scene;
    }
}

export { OffscreenRenderer };
