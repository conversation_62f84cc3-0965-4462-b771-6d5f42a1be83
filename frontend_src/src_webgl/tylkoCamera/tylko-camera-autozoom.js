import * as THREE from 'three'

// Obiekt odpowiada za:
// - obliczanie poprawnej wartosci ZOOM gwarantujacej utrzymanie zadanych punktow wewnatrz frustrum
// Obiekt NIE odpowiada za:
// - manipulacje kamera, obliczaniem jej katow i transformacji


export function tylkoCameraAutoZoom(scene) {
    this.points = [  new THREE.Vector3(-1000,-1000,-1000),  new THREE.Vector3(1000, 1000, 1000)];
    this.frustum = new THREE.Frustum();
    this.horizontalPlane = new THREE.Plane()
    this.verticalPlane = new THREE.Plane()
    this.cameraPlanesOffset = 50
    
    var tempPoint1 = new THREE.Vector3()
    var tempPoint2 = new THREE.Vector3()
    
    var deg2rad = Math.PI/180;

    // TEMP
    // let helper_h = new THREE.PlaneHelper( this.horizontalPlane, 1000, 0xffff00 );  // Pionowa os
    // let helper_v = new THREE.PlaneHelper( this.verticalPlane, 1000, 0xaaaa00 );  // Pionowa os

    // this.distTop = 0
    // this.distTarget = 0
    // this.distOffset = 0
    
    // this.pointMin = new THREE.Vector3();
    // this.pointMax = new THREE.Vector3();
    // let mat = new THREE.MeshLambertMaterial({color: 0xCC0000})
    // this.sphereMin = new THREE.Mesh( new THREE.SphereGeometry(4, 4, 4), mat );
    // this.sphereMax = new THREE.Mesh( new THREE.SphereGeometry(4, 4, 4), mat );
    // mat = new THREE.MeshLambertMaterial({color: 0xAAAA00})
    // var sphereTempPoint = new THREE.Mesh( new THREE.SphereGeometry(15, 16, 16), mat );
    // var sphereTempProject = new THREE.Mesh( new THREE.SphereGeometry(15, 16, 16), mat );
    
    // if (scene !== undefined) {
    //     scene.add(helper_h);
    //     scene.add(helper_v);
    //     scene.add( this.sphereMin );
    //     scene.add( this.sphereMax );
    //     scene.add( sphereTempPoint );
    //     scene.add( sphereTempProject );
    // }


    this.calculateNearFarPosition = function(camera, controls) {
        let pointDistances = []
        this.points.forEach(p => {
            pointDistances.push(p.distanceTo(controls.target))
        });
        let camDistance = camera.position.distanceTo(controls.target)
        let maxDistance = Math.max(...pointDistances)
        return [
            Math.max(0, camDistance - maxDistance - this.cameraPlanesOffset),
             camDistance + maxDistance + this.cameraPlanesOffset
            ]
    }

    this.updatePoints = function(points) {
        this.points = points;

        // this.pointMin = points[0]
        // this.sphereMin.position.x = this.pointMin.x
        // this.sphereMin.position.y = this.pointMin.y
        // this.sphereMin.position.z = this.pointMin.z
        // this.pointMax = points[1]
        // this.sphereMax.position.x = this.pointMax.x
        // this.sphereMax.position.y = this.pointMax.y
        // this.sphereMax.position.z = this.pointMax.z
    }

    this.updatePlanes = function(camera, controls) {
        this.frustum.setFromMatrix( new THREE.Matrix4().multiplyMatrices( camera.projectionMatrix, camera.matrixWorldInverse ) );
        this.frustum.planes[1].projectPoint( controls.target, tempPoint1)
        this.frustum.planes[2].projectPoint( controls.target, tempPoint2)
        
        this.horizontalPlane.setFromCoplanarPoints( controls.target, camera.position, tempPoint1)
        this.verticalPlane.setFromCoplanarPoints( controls.target, camera.position, tempPoint2)
    }

    this.calculateDistances = function(point, angle, camPos, targetPos, planeA, planeB) {
        // Map point to vertical Plane
        planeA.projectPoint( point, tempPoint1)
        // Map vertical projection to horizontal Plane
        planeB.projectPoint( tempPoint1, tempPoint2)
        // Calc distances
        // let distTop = this.horizontalPlane.distanceToPoint(tempPoint1)
        // Calc distances
        let distTop = tempPoint1.distanceTo(tempPoint2)
        let distTarget = targetPos.distanceTo(tempPoint2)
        let distPointToCam = camPos.distanceTo(tempPoint2)
        let distTargetToCam = camPos.distanceTo(targetPos)
        distTarget = distPointToCam < distTargetToCam ? distTarget : -distTarget

        let distZoom = distTop / Math.tan(angle) + distTarget

        // this.distOffset = distZoom
        // this.distTarget = distTarget
        // this.distTop = distTop
        // this.distCamera = targetPos.distanceTo(camPos)

        // sphereTempPoint.position.x = tempPoint1.x
        // sphereTempPoint.position.y = tempPoint1.y
        // sphereTempPoint.position.z = tempPoint1.z
        // sphereTempProject.position.x = tempPoint2.x
        // sphereTempProject.position.y = tempPoint2.y
        // sphereTempProject.position.z = tempPoint2.z

        return distZoom
    }

    this.calculateZoom = function(camera, controls) {
        // Calc frustrum Angles
        let zoomDistances = []
        let angle = camera.fov / 2 * deg2rad  // frustrum vertical angle
        this.points.forEach(p => {
            zoomDistances.push(
                this.calculateDistances(
                    p, angle, camera.position, controls.target,
                    this.verticalPlane, this.horizontalPlane
                )
            )
        });
        angle *= camera.aspect
        this.points.forEach(p => {
            zoomDistances.push(
                this.calculateDistances(
                    p, angle, camera.position, controls.target,
                    this.horizontalPlane, this.verticalPlane
                )
            )
        });

        return Math.max(...zoomDistances) // Zoom

    }

}  