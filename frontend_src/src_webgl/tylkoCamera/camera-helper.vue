<template lang="pug">
    section(class="main" style="height: 100%")
        div(style="z-index: 1000;display:block;height: 100vh; width: calc(100vw - 500px);position:fixed;top:0px;left:60px;")
            div(style="width:40%;height: 100vh;display:inline-block;", ref="view1")
            div(style="width:60%;height: 100vh;display:inline-block;", ref="view2")
        div.flex.row(style="height: 100%")
            div.col(style="background: red; width: calc(100vw - 500px);")
                canvas(class="col" ref="c" style="height: calc(100vh); width: calc(100vw - 500px); position: fixed;")
            div.col(style="max-width: 440px; background: white; height: 100%")
                q-scroll-area(style="height:100vh;")
                    t-card(name="Geometry size")
                        .card
                            .card-main
                                .card-content
                                    .card-row
                                        q-field(:label="'X:'+geomX").col-card-6
                                        q-slider(
                                            :value="geomXtemp",
                                            :min="40",
                                            :max="500",
                                            @change="val => { geomXtemp=val; tylkoCam.controls.geometryFixed = true;  tylkoCam.controls.update(); }",
                                            @input="val => { tylkoCam.controls.geometryFixed = false; updateGeometry(val)}",
                                            dark).col-card-17
                                    .card-row
                                        q-field(:label="'Y:'+geomY").col-card-6
                                        q-slider(
                                            :value="geomYtemp",
                                            :min="20",
                                            :max="300",
                                            @change="val => { geomYtemp=val; tylkoCam.controls.geometryFixed = true; tylkoCam.controls.update(); }",
                                            @input="val => { tylkoCam.controls.geometryFixed = false; updateGeometry(undefined, val)}",
                                            dark).col-card-17
                                    .card-row
                                        .col-card-1
                                        q-btn(
                                            label="Shelf",
                                            color="blue",
                                            @click="switchToShelfView").col-card-6
                                        .col-card-1
                                        q-btn(
                                            label="Component",
                                            color="blue",
                                            @click="switchToComponentView").col-card-6
                                        .col-card-1
                                        q-btn(
                                            label="Madness",
                                            color="red",
                                            @click="tylkoCam.setDefaultfView()").col-card-6
                    t-card(name="Camera Automatic Zoom Calculations" v-if="ready")
                        .card
                            .card-main
                                .card-content
                                    .card-row(class="q-pa-sm q-pb-md")
                                        q-toggle(
                                            label="Disable AutoZoom",
                                            v-model="tylkoCam.controls.noAutoZoom",
                                            color="yellow",
                                            dark)
                                        q-toggle(
                                            label="Disable LifeZoom",
                                            v-model="tylkoCam.controls.noLifeZoom",
                                            color="yellow",
                                            dark)
                                    .card-row(class="q-pa-sm q-pb-md")
                                        q-toggle(
                                            label="Disable Animation",
                                            v-model="tylkoCam.controls.noTransitionAnimation",
                                            color="yellow",
                                            dark)
                                        q-toggle(
                                            label="Disable Snapping",
                                            v-model="tylkoCam.controls.noSnap",
                                            color="yellow",
                                            dark)
                                    //.card-row
                                        q-input(
                                            label="Near/Far Offset",
                                            v-model.number="tylkoCam.controls.autoZoomObj.cameraPlanesOffset",
                                            type="number",
                                            dark).col-card-24
                                    .card-row
                                        q-field(label="Geometry to ViewEdge Offsets in object cm:").col-card-24
                                    .card-row
                                        q-input(
                                            label="Left",
                                            v-model.number="tylkoCam.geometryOffset.left",
                                            @input="updateGeometry",
                                            type="number",
                                            dark).col-card-4
                                        q-input(
                                            label="Right",
                                            v-model.number="tylkoCam.geometryOffset.right",
                                            @input="updateGeometry",
                                            type="number",
                                            dark).col-card-4
                                        q-input(
                                            label="Top",
                                            v-model.number="tylkoCam.geometryOffset.top",
                                            @input="updateGeometry",
                                            type="number",
                                            dark).col-card-4
                                        q-input(
                                            label="Bottom",
                                            v-model.number="tylkoCam.geometryOffset.bottom",
                                            @input="updateGeometry",
                                            type="number",
                                            dark).col-card-4
                                        q-input(
                                            label="Front",
                                            v-model.number="tylkoCam.geometryOffset.front",
                                            @input="updateGeometry",
                                            type="number",
                                            dark).col-card-4
                                        q-input(
                                            label="Back",
                                            v-model.number="tylkoCam.geometryOffset.back",
                                            @input="updateGeometry",
                                            type="number",
                                            dark).col-card-4    
                                    .card-row
                                        q-field(label="Geometry to ViewEdge Offsets in screen pixels:").col-card-24
                                    .card-row
                                        q-input(
                                            label="Left",
                                            v-model.number="tylkoCam.controls.screenEdgeOffset.left",
                                            @input="tylkoCam.controls.update()",
                                            type="number",
                                            dark).col-card-6
                                        q-input(
                                            label="Right",
                                            v-model.number="tylkoCam.controls.screenEdgeOffset.right",
                                            @input="tylkoCam.controls.update()",
                                            type="number",
                                            dark).col-card-6
                                        q-input(
                                            label="Top",
                                            v-model.number="tylkoCam.controls.screenEdgeOffset.top",
                                            @input="tylkoCam.controls.update()",
                                            type="number",
                                            dark).col-card-6
                                        q-input(
                                            label="Bottom",
                                            v-model.number="tylkoCam.controls.screenEdgeOffset.bottom",
                                            @input="tylkoCam.controls.update()",
                                            type="number",
                                            dark).col-card-6

                    t-card(name="Camera controls" v-if="ready")
                        .card
                            .card-main
                                .card-content
                                    .card-row(class="q-pa-sm q-pb-md")
                                        .col-card-1
                                        q-btn(
                                            label="Left",
                                            color="blue",
                                            @click="tylkoCam.setView('left')").col-card-3
                                        q-btn(
                                            label="L",
                                            color="red",
                                            @click="tylkoCam.setView('left_straight')").col-card-1    
                                        .col-card-2
                                        q-btn(
                                            label="Front",
                                            color="blue",
                                            @click="tylkoCam.setView('front')").col-card-4
                                        .col-card-2
                                        q-btn(
                                            label="Right",
                                            color="blue",
                                            @click="tylkoCam.setView('right')").col-card-3
                                        q-btn(
                                            label="R",
                                            color="red",
                                            @click="tylkoCam.setView('right_straight')").col-card-1    
                                        .col-card-2
                                        q-btn(
                                            label="Top",
                                            color="blue",
                                            @click="tylkoCam.setView('top')").col-card-3
                                        q-btn(
                                            label="T",
                                            color="red",
                                            @click="tylkoCam.setView('top_straight')").col-card-1
                                    .card-row
                                        q-field(:label="'fov:'+tylkoCam.fov").col-card-6
                                        q-slider(
                                            v-model="tylkoCam.fov",
                                            :min="10",
                                            :max="180",
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-17
                                    .card-row
                                        q-field(label="Range").col-card-6
                                        q-range(
                                            v-model="tylkoCam.range",
                                            :min="1",
                                            :max="10000",
                                            label-always,
                                            drag-range,
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-17
                                    .card-row
                                        q-field(:label="'Position X'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.position.x",
                                            :min="-3000",
                                            :max="3000",
                                            label-always,
                                            :label-value="parseInt(tylkoCam.position.x)",
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'Position Y'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.position.y",
                                            :min="-3000",
                                            :max="3000",
                                            label-always,
                                            :label-value="parseInt(tylkoCam.position.y)",
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'Position Z'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.position.z",
                                            :min="-3000",
                                            :max="3000",
                                            label-always,
                                            :label-value="parseInt(tylkoCam.position.z)",
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'Target X'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.target.x",
                                            :min="-300",
                                            :max="300",
                                            label-always,
                                            :label-value="parseInt(tylkoCam.target.x)",
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'Target Y'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.target.y",
                                            :min="-300",
                                            :max="300",
                                            label-always,
                                            :label-value="parseInt(tylkoCam.target.y)",
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'Target Z'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.target.z",
                                            :min="-300",
                                            :max="300",
                                            label-always,
                                            :label-value="parseInt(tylkoCam.target.z)",
                                            @input="tylkoCam.updateCamera()",
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'Rotate Theta'").col-card-10
                                        q-slider(
                                            v-if="tylkoCam.controls.new_theta !== null",
                                            v-model.number="tylkoCam.controls.new_theta",
                                            type="number",
                                            :min="-Math.PI",
                                            :max="Math.PI",
                                            :step="0.01",
                                            label-always,
                                            :label-value="+(tylkoCam.controls.new_theta).toFixed(3)",
                                            @input="tylkoCam.controls.update()",
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'Rotate Phi'").col-card-10
                                        q-slider(
                                            v-if="tylkoCam.controls.new_phi !== null",
                                            v-model.number="tylkoCam.controls.new_phi",
                                            type="number",
                                            :min="0",
                                            :max="Math.PI",
                                            :step="0.01",
                                            label-always,
                                            :label-value="+(tylkoCam.controls.new_phi).toFixed(3)",
                                            @input="tylkoCam.controls.update()",
                                            dark).col-card-13
                                    .card-row
                                        .col-card-4
                                        q-btn(
                                            label="get matrix",
                                            color="green",
                                            @click="getMatrix").col-card-8
                                        .col-card-2
                                        q-btn(
                                            label="set matrix",
                                            color="red",
                                            @click="setMatrix").col-card-8            
                    t-card(name="Orbit Controls" v-if="ready")
                        .card
                            .card-main
                                .card-content
                                    .card-row(class="q-pa-sm q-pb-md")
                                        q-toggle(
                                            label="Enable",
                                            v-model="tylkoCam.controls.enabled",
                                            color="green",
                                            dark).col-card-7
                                        q-toggle(
                                            label="NoRotate",
                                            v-model="tylkoCam.controls.noRotate",
                                            color="red",
                                            dark).col-card-9
                                        q-toggle(
                                            label="NoPan",
                                            v-model="tylkoCam.controls.noPan",
                                            color="red",
                                            dark)
                                    .card-row
                                        q-field(label="Polar range").col-card-10
                                        q-range(
                                            v-model="tylkoCam.controls.polarAngle",
                                            :min="0",
                                            :max="Math.PI",
                                            :step="0.01",
                                            :left-label-value="tylkoCam.controls.polarAngle ? +(tylkoCam.controls.polarAngle.min).toFixed(3) : 0",
                                            :right-label-value="tylkoCam.controls.polarAngle ? +(tylkoCam.controls.polarAngle.max).toFixed(3) : 0",
                                            label-always,
                                            drag-range,
                                            dark).col-card-13
                                    .card-row
                                        q-field(label="Azimuth range").col-card-10
                                        q-range(
                                            v-model="tylkoCam.controls.azimuthAngle",
                                            :min="-Math.PI",
                                            :max="Math.PI",
                                            :step="0.01",
                                            :left-label-value="tylkoCam.controls.azimuthAngle ? +(tylkoCam.controls.azimuthAngle.min).toFixed(3) : 0",
                                            :right-label-value="tylkoCam.controls.azimuthAngle ? +(tylkoCam.controls.azimuthAngle.max).toFixed(3) : 0",
                                            label-always,
                                            drag-range,
                                            dark).col-card-13        
                                    .card-row
                                        q-field(:label="'ZoomSpeed'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.controls.zoomSpeed",
                                            :min="0.1",
                                            :max="5",
                                            :step="0.1",
                                            label-always,
                                            dark).col-card-13
                                    .card-row
                                        q-field(:label="'RotateSpeed'").col-card-10
                                        q-slider(
                                            v-model="tylkoCam.controls.rotateSpeed",
                                            :min="0.1",
                                            :max="5",
                                            :step="0.1",
                                            label-always,
                                            dark).col-card-13

</template>

<style lang="scss">
@import '~@theme/card.scss';

.ac {
    width: 420px;
    height: calc(100vh - 235px);
    background: #131313;
}
</style>

<script>
import * as THREE from 'three'
import './tylko-camera-orbit-perspective'

import { cape } from '@core/cape'
import { tylkoCamera } from './tylko-camera.js'
import { tylkoCapeComponents } from '@cape-ui'

export default {
    components: {
        ...tylkoCapeComponents,
    },

    data() {
        return {
            ready: false,

            geomX: 240,
            geomXtemp: 240,
            geomY: 120,
            geomYtemp: 120,
            geomZ: 32,
            geom: null,

            view1: null,
            view2: null,
            canvas: null,

            temp1: 0,
            temp2: 0,

            renderer: null,
            scene: null,
            tylkoCam: null,
            camera2: null,
            controls2: null,
        }
    },

    mounted() {
        cape.application.viewport.fixed(true)
        this.setUp()
        this.createGeometry()
        this.render()
        // requestAnimationFrame(this.render)
        this.ready = true

        // LEFT VIEW

        let shouldRender = false;

        this.renderLoop = () => {
            if(shouldRender) {
                shouldRender = false;
                this.render();
                this.tylkoCam.controls.update()
            }
            window.requestAnimationFrame(this.renderLoop)
        };

        this.renderLoop();

        this.tylkoCam.controls.addEventListener('render', () => {
            shouldRender = true;
        });

        this.tylkoCam.controls.addEventListener('change', () => {
            this.render()
        });

        // RIGHT VIEW
        this.controls2.addEventListener('change', () => {
            this.render()
        });

    },

    methods: {
        setUp() {
            this.canvas = this.$refs.c
            this.view1 = this.$refs.view1
            this.view2 = this.$refs.view2
            this.scene = new THREE.Scene()
            this.scene.background = new THREE.Color('black')
            this.renderer = new THREE.WebGLRenderer({ canvas: this.canvas })

            this.tylkoCam = new tylkoCamera(this.view1, this.scene)
            this.cameraHelper = new THREE.CameraHelper(this.tylkoCam.camera)
            this.scene.add(this.cameraHelper)
            this.scene.add(this.tylkoCam.camera)

            this.camera2 = new THREE.PerspectiveCamera(60, 2, 0.1, 50000)
            this.camera2.position.set(800, 500, 700)
            this.camera2.lookAt(0, 5, 0)

            this.controls2 = new THREE.OrbitControls(this.camera2, this.view2)
            this.controls2.target.set(0, 5, 0)
            this.controls2.update()
        },

        createGeometry() {
            const cubeGeo = new THREE.BoxBufferGeometry(1, 1, 1)
            const cubeMat = new THREE.MeshPhongMaterial({ color: '#8AC', opacity: 0.5, transparent: false })
            this.geom = new THREE.Mesh(cubeGeo, cubeMat)
            this.geom.scale.set(this.geomX, this.geomY, this.geomZ)
            this.geom.position.set(0, this.geomY / 2, this.geomZ / 2)
            this.scene.add(this.geom)
            // Grid
            let size = 280,
                steps = 20
            let geometry = new THREE.Geometry()
            let material = new THREE.LineBasicMaterial({
                color: 'gray',
            })
            for (var i = -size; i <= size; i += steps) {
                if (i >= 0) {
                    geometry.vertices.push(new THREE.Vector3(-size, -0.04, i))
                    geometry.vertices.push(new THREE.Vector3(size, -0.04, i))
                }
                if (size - i <= 320) {
                    geometry.vertices.push(
                        new THREE.Vector3(-size, size - i - 0.04, 0)
                    )
                    geometry.vertices.push(
                        new THREE.Vector3(size, size - i - 0.04, 0)
                    )
                }

                geometry.vertices.push(new THREE.Vector3(i, -0.04, 0))
                geometry.vertices.push(new THREE.Vector3(i, -0.04, size))
                geometry.vertices.push(new THREE.Vector3(i, -0.04, 0))
                geometry.vertices.push(new THREE.Vector3(i, 320 - 0.04, 0))
            }
            var line = new THREE.Line(geometry, material, THREE.LinePieces)
            this.scene.add(line)
            this.tylkoCam.updateGeometry(
                {x: -this.geomX/2, y: 0, z: 0},
                {x: this.geomX/2, y: this.geomY, z: this.geomZ}
            )
            // this.tylkoCam.controls.update()
        },

        resizeRendererToDisplaySize(renderer) {
            const canvas = renderer.domElement
            const width = canvas.clientWidth
            const height = canvas.clientHeight
            const needResize =
                canvas.width !== width || canvas.height !== height
            if (needResize) {
                renderer.setSize(width, height, false)
            }
            return needResize
        },

        setScissorForElement(elem) {
            const canvasRect = this.canvas.getBoundingClientRect()
            const elemRect = elem.getBoundingClientRect()

            const right =
                Math.min(elemRect.right, canvasRect.right) - canvasRect.left
            const left = Math.max(0, elemRect.left - canvasRect.left)
            const bottom =
                Math.min(elemRect.bottom, canvasRect.bottom) - canvasRect.top
            const top = Math.max(0, elemRect.top - canvasRect.top)

            const width = Math.min(canvasRect.width, right - left)
            const height = Math.min(canvasRect.height, bottom - top)

            const positiveYUpBottom = canvasRect.height - bottom
            this.renderer.setScissor(left, positiveYUpBottom, width, height)
            this.renderer.setViewport(left, positiveYUpBottom, width, height)

            return width / height
        },

        render () {
            console.log(">>>>> RENDER")
            this.resizeRendererToDisplaySize(this.renderer)
            this.renderer.setScissorTest(true)

            // render from the 1nd camera
            let aspect = this.setScissorForElement(this.view1)
            this.tylkoCam.update(aspect)
            this.cameraHelper.update()
            this.cameraHelper.visible = false
            this.scene.background.set(0x000000)
            this.renderer.render(this.scene, this.tylkoCam.camera)

            // render from the 2nd camera
            aspect = this.setScissorForElement(this.view2)
            this.camera2.aspect = aspect
            this.camera2.updateProjectionMatrix()
            this.cameraHelper.visible = true
            this.scene.background.set(0x000040)
            this.renderer.render(this.scene, this.camera2)
        },

        updateGeometry(geomX, geomY) {
            if (geomX !== undefined) this.geomX = geomX
            if (geomY !== undefined) this.geomY = geomY
            this.geom.scale.set(this.geomX, this.geomY, this.geomZ)
            this.geom.position.y = this.geomY / 2
            this.tylkoCam.updateGeometry(
                {x: -this.geomX/2, y: 0, z: 0},
                {x: this.geomX/2, y: this.geomY, z: this.geomZ}
            )
            this.render()
        },

        switchToShelfView() {
            console.log(">>>> Shelf")
            this.tylkoCam.setShelfView(
                {x: -this.geomX/2, y: 0, z: 0},
                {x: this.geomX/2, y: this.geomY, z: this.geomZ}
            )
        },

        switchToComponentView(geomX, geomY) {
            console.log(">>>> Component")
            this.tylkoCam.setComponentView(
                {x: -20, y: 0, z: 0},
                {x: 40, y: this.geomY, z: this.geomZ}
            )
        },

        getMatrix() {
            this.$q.dialog({
                title: 'Matrix',
                message: JSON.stringify(this.tylkoCam.getCamSettings()),
                ok: true,
            })
        },
        setMatrix() {
            let matrix = prompt('Enter matrix data', '')
            if (matrix) this.tylkoCam.setCamSettings(JSON.parse(matrix))
        },
    },
}
</script>
