module.exports=function(t){var e={};function n(o){if(e[o])return e[o].exports;var i=e[o]={i:o,l:!1,exports:{}};return t[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,o){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(o,i,function(e){return t[e]}.bind(null,i));return o},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){"use strict";function o(){}n.r(e),n.d(e,"default",(function(){return r})),Object.assign(o.prototype,{addEventListener:function(t,e){void 0===this._listeners&&(this._listeners={});var n=this._listeners;void 0===n[t]&&(n[t]=[]),-1===n[t].indexOf(e)&&n[t].push(e)},hasEventListener:function(t,e){if(void 0===this._listeners)return!1;var n=this._listeners;return void 0!==n[t]&&-1!==n[t].indexOf(e)},removeEventListener:function(t,e){if(void 0!==this._listeners){var n=this._listeners[t];if(void 0!==n){var o=n.indexOf(e);-1!==o&&n.splice(o,1)}}},dispatchEvent:function(t){if(void 0!==this._listeners){var e=this._listeners[t.type];if(void 0!==e){t.target=this;for(var n=e.slice(0),o=0,i=n.length;o<i;o++)n[o].call(this,t)}}}});var i=o;const s=function(t,e,n){this.object=t,this.domElement=void 0!==e?e:document,this.points=[new n.Vector3(-1e3,-1e3,-1e3),new n.Vector3(0,0,0),new n.Vector3(0,0,0),new n.Vector3(0,0,0),new n.Vector3(0,0,0),new n.Vector3(0,0,0),new n.Vector3(0,0,0),new n.Vector3(1e3,1e3,1e3)],this.enabled=!0,this.noZoom=!1,this.noAutoZoom=!0,this.noLifeZoom=!0,this.noTransitionAnimation=!0,this.noRotate=!1,this.noPan=!1,this.directionLock=!1,this.noYPan=!1,this.noSnap=!0,this.noSnapReal=this.noSnap,this.target=new n.Vector3,this.snapTo=[{theta:.62,phi:1.1,x:0},{theta:-.4,phi:1.4,x:0},{theta:0,phi:Math.PI/2,x:0}],this.zoomSpeed=1,this.rotateSpeed=2,this.animationStepParameters={distance:40,targetDistance:12,angle:.08},this.zoomRange={min:0,max:1/0},this.polarAngle={min:0,max:Math.PI},this.azimuthAngle={min:-1/0,max:1/0},this.mouseButtons={ORBIT:n.MOUSE.LEFT,ZOOM:n.MOUSE.MIDDLE,PAN:n.MOUSE.RIGHT},this.touchMode={ORBIT:1,ZOOM:2,PAN:3,ZOOMPAN:4},this.new_theta=null,this.new_phi=null,this.new_target=null,this.geometryFixed=!0,this.screenEdgeOffset={left:0,right:0,top:0,bottom:0};const o=this,i={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>t*(2-t),easeInOutQuad:t=>t<.5?2*t*t:(4-2*t)*t-1,easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1- --t*t*t*t,easeInOutQuart:t=>t<.5?8*t*t*t*t:1-8*--t*t*t*t,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1+--t*t*t*t*t,easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t},s=new n.Vector2,a=new n.Vector2;let r=new n.Vector2;const h=new n.Vector2,c=new n.Vector2;let l=new n.Vector2;const u=new n.Vector3,p=new n.Vector3,m=new n.Vector2,f=new n.Vector2,d=new n.Vector2;let g,M,b,y,x,O,S,v,E,P=0,w=0,I=1;const T=new n.Vector3,A=new n.Vector3;let z={counter:0,thetaDiff:0,phiDiff:0,zoomDiff:0,theta:0,phi:0,zoom:0};const Z=-1,_=0,L=1,R=2,V=3,j=4,k=5,D=6,Y=7;let B=Z,X=!1;const F=(new n.Quaternion).setFromUnitVectors(t.up,new n.Vector3(0,1,0)),N=F.clone().inverse(),U={type:"change"},Q={type:"render"},H={type:"animationEnd"};function C(){const t=function(t,e,n,o){let i,s,a;return[i,...o]=o,s=Math.abs(i.theta-e)+(i.x?Math.abs(i.x-n):0),o.forEach(t=>{a=Math.abs(t.theta-e)+(t.x?Math.abs(t.x-n):0),s>a&&(i=t,s=a)}),i}(0,y,o.target.x,o.snapTo);!o.noSnapReal&&W(t.theta,t.phi,void 0,t.x)?(S=t.theta,v=t.phi,t.x&&(E=o.target.clone(),E.x=t.x),g=!0):W(o.new_theta,o.new_phi)&&(S=o.new_theta,v=o.new_phi),null!==o.new_target&&(E=o.new_target)}function G(){const t=2/o.domElement.clientWidth,e=2/o.domElement.clientHeight;let n=[],i=[];o.points.forEach(s=>{A.copy(s).project(o.object),n.push(A.x+(A.x<0?-t*o.screenEdgeOffset.left:t*o.screenEdgeOffset.right)),i.push(A.y+(A.y<0?-e*o.screenEdgeOffset.bottom:e*o.screenEdgeOffset.top))}),n=n.filter(isFinite),i=i.filter(isFinite);let s=o.object.position.distanceTo(o.target);s*=Math.max(...n,...i,Math.abs(Math.min(...n,...i))),O=s||M}function W(t,e,n,i,s){const a=void 0!==t&&Math.abs(y-t)>o.animationStepParameters.angle,r=void 0!==e&&Math.abs(x-e)>o.animationStepParameters.angle,h=void 0!==n&&Math.abs(M-n)>10,c=void 0!==i&&Math.abs(o.target.x-i)>1,l=void 0!==s&&Math.abs(o.target.y-s)>1;return a||r||h||c||l}function q(){if(z.counter===z.total)S=z.theta,v=z.phi,O=z.zoom,E=z.target,B=Z,X=!0;else{let t=z.counter/z.total;t=i.easeInOutCubic(t),S=z.thetaStart+z.thetaDiff*t,v=z.phiStart+z.phiDiff*t,O=z.zoomStart+z.zoomDiff*t,E.x=z.targetStart.x+z.targetDiff.x*t,E.y=z.targetStart.y+z.targetDiff.y*t,z.counter+=1}}function J(){return Math.pow(.95,o.zoomSpeed)}function K(t){if(!1===o.enabled)return;t.preventDefault();const e=o.domElement===document?o.domElement.body:o.domElement;if(B===_){if(!0===o.noRotate)return;a.set(t.clientX,t.clientY),r.subVectors(a,s),P-=2*Math.PI*r.x/e.clientWidth*o.rotateSpeed,w-=2*Math.PI*r.y/e.clientHeight*o.rotateSpeed,s.copy(a)}else if(B===L){if(!0===o.noZoom)return;f.set(t.clientX,t.clientY),d.subVectors(f,m),d.y>0?o.dollyIn():d.y<0&&o.dollyOut(),m.copy(f)}else if(B===R){if(!0===o.noPan)return;c.set(t.clientX,t.clientY),l.subVectors(c,h),o.pan(l.x,l.y),h.copy(c)}B!==Z&&(o.update(),o.noSnapReal=o.noSnap)}function $(){!1!==o.enabled&&(document.removeEventListener("mousemove",K,!1),document.removeEventListener("mouseup",$,!1),B=Z,o.update())}function tt(t){if(!1===o.enabled||!0===o.noZoom||B!==Z)return;t.preventDefault(),t.stopPropagation();let e=0;void 0!==t.wheelDelta?e=t.wheelDelta:void 0!==t.detail&&(e=-t.detail),e>0?o.dollyOut():e<0&&o.dollyIn(),o.noSnapReal=o.noSnap,o.update()}function et(t){switch(o.directionLock){case!1:return t;case"horizontal":return t.setY(0),t;case"vertrical":return t.setX(0),t;default:return o.directionLock=Math.abs(t.x)>Math.abs(t.y)?"horizontal":"vertrical",et(t)}}this.getState=()=>B,this.panLeft=function(t){const e=this.object.matrix.elements;u.set(e[0],e[1],e[2]),u.multiplyScalar(-t),T.add(u)},this.panUp=function(t){const e=this.object.matrix.elements;u.set(e[4],e[5],e[6]),u.multiplyScalar(t),T.add(u)},this.pan=function(t,e){const n=o.domElement===document?o.domElement.body:o.domElement,{position:i}=o.object;let s=i.clone().sub(o.target).length();s*=Math.tan(o.object.fov/2*Math.PI/180),o.panLeft(2*t*s/n.clientHeight),o.noYPan||o.panUp(2*e*s/n.clientHeight)},this.dollyIn=function(t){void 0===t&&(t=J()),I/=t},this.dollyOut=function(t){void 0===t&&(t=J()),I*=t},this.updateCameraRange=function(){let t=0;o.points.forEach(e=>{t=Math.max(o.target.distanceTo(e),t)});o.object.position.distanceTo(o.target);this.object.near=.1,this.object.far=5e4,this.object.updateProjectionMatrix()},this.updateZoom=function(){!function(){function t(t){return Math.max(...t.filter(isFinite))}const e=o.domElement?o.domElement.clientWidth:800,n=o.domElement?o.domElement.clientHeight:600,i=null===o.new_target?o.target:o.new_target,s=o.object.fov*o.object.aspect*(1-o.screenEdgeOffset.left/e),a=o.object.fov*o.object.aspect*(1-o.screenEdgeOffset.right/e),r=o.object.fov*(1-o.screenEdgeOffset.top/n),h=o.object.fov*(1-o.screenEdgeOffset.bottom/n),[c,l,u,p,m]=[[],[],[],[],[]];o.points.forEach(t=>{t.x<i.x?c.push(Math.abs(t.x-i.x)):l.push(Math.abs(t.x-i.x)),t.y-i.y?p.push(Math.abs(t.y-i.y)):u.push(Math.abs(t.y-i.y)),t.z>i.z&&m.push(Math.abs(t.z-i.z))});const f=t(c)/(2*Math.atan(Math.PI*s/360)),d=t(l)/(2*Math.atan(Math.PI*a/360)),g=t(u)/(2*Math.atan(Math.PI*r/360)),M=t(p)/(2*Math.atan(Math.PI*h/360));b=t(m)+2*Math.max(f,d,g,M)}()},this.forceUpdate=function(){B=Z,this.update()},this.update=function(){if(this.geometryFixed||!this.noLifeZoom){switch(p.copy(o.object.position).sub(o.target),p.applyQuaternion(F),y=Math.atan2(p.x,p.z),x=Math.atan2(Math.sqrt(p.x*p.x+p.z*p.z),p.y),g=!1,M=p.length(),O=b||M,S=y,v=x,E=o.new_target||o.target,B){case Z:C(),g||this.noAutoZoom&&(this.noLifeZoom||!0!==this.geometryFixed)&&(this.noLifeZoom||B!==Z)||G(),!this.noTransitionAnimation&&W(S,v,O,E.x,E.y)&&function(){const t=Math.round(Math.min(Math.max(Math.abs(S-y)/o.animationStepParameters.angle,Math.abs(v-x)/o.animationStepParameters.angle,Math.abs(O-M)/o.animationStepParameters.distance,Math.abs(E.x-o.target.x)/o.animationStepParameters.targetDistance,Math.abs(E.y-o.target.y)/o.animationStepParameters.targetDistance,5),35));B=Y,z={counter:1,total:t,thetaDiff:S-y,phiDiff:v-x,zoomDiff:O-M,targetDiff:{x:E.x-o.target.x,y:E.y-o.target.y},theta:S,phi:v,zoom:O,target:E.clone(),thetaStart:y,phiStart:x,zoomStart:M,targetStart:o.target.clone()},q()}();break;case Y:q();break;default:S=Math.atan2(p.x,p.z)+P,v=Math.atan2(Math.sqrt(p.x*p.x+p.z*p.z),p.y)+w,O=M*I,E=o.target.clone(),E.add(T),S=Math.max(o.azimuthAngle.min,Math.min(o.azimuthAngle.max,S)),v=Math.max(o.polarAngle.min,Math.min(o.polarAngle.max,v)),O=Math.max(o.zoomRange.min,Math.min(o.zoomRange.max,O)),this.noAutoZoom||this.noLifeZoom||G()}switch(v=Math.max(1e-6,Math.min(Math.PI-1e-6,v)),p.x=O*Math.sin(v)*Math.sin(S),p.y=O*Math.cos(v),p.z=O*Math.sin(v)*Math.cos(S),p.applyQuaternion(N),o.target=E,o.object.position.copy(o.target).add(p),o.object.lookAt(o.target),P=0,w=0,o.new_theta=S,o.new_phi=v,o.new_target=null,I=1,T.set(0,0,0),B){case Y:o.dispatchEvent(Q);break;default:!0===X&&(X=!1,o.dispatchEvent(H)),o.dispatchEvent(U)}}},this.domElement&&(this.domElement.addEventListener("contextmenu",t=>{t.preventDefault()},!1),this.domElement.addEventListener("mousedown",(function(t){if(!1!==o.enabled){if(t.preventDefault(),t.button===o.mouseButtons.ORBIT){if(!0===o.noRotate)return;B=_,s.set(t.clientX,t.clientY)}else if(t.button===o.mouseButtons.ZOOM){if(!0===o.noZoom)return;B=L,m.set(t.clientX,t.clientY)}else if(t.button===o.mouseButtons.PAN){if(!0===o.noPan)return;B=R,h.set(t.clientX,t.clientY)}B!==Z&&(document.addEventListener("mousemove",K,!1),document.addEventListener("mouseup",$,!1))}}),!1),this.domElement.addEventListener("mousewheel",tt,!1),this.domElement.addEventListener("DOMMouseScroll",tt,!1),this.domElement.addEventListener("touchstart",(function(t){if(!1!==o.enabled)switch(t.touches.length){case o.touchMode.ORBIT:if(!0===o.noRotate)return;B=V,s.set(t.touches[0].pageX,t.touches[0].pageY);break;case o.touchMode.ZOOM:if(!0===o.noZoom)return;B=j;var e=t.touches[0].pageX-t.touches[1].pageX,n=t.touches[0].pageY-t.touches[1].pageY,i=Math.sqrt(e*e+n*n);m.set(0,i);break;case o.touchMode.PAN:if(!0===o.noPan)return;B=k,h.set(t.touches[0].pageX,t.touches[0].pageY);break;case o.touchMode.ZOOMPAN:B=D,o.directionLock=!0,h.set(t.touches[0].pageX,t.touches[0].pageY);break;default:B=Z}}),!1),this.domElement.addEventListener("touchend",(function(){!1!==o.enabled&&(!1!==o.directionLock&&(o.directionLock=!0),B=Z,o.update())}),!1),this.domElement.addEventListener("touchmove",(function(t){if(!1===o.enabled)return;t.preventDefault();const e=o.domElement===document?o.domElement.body:o.domElement;switch(t.touches.length){case o.touchMode.ORBIT:if(!0===o.noRotate)return;if(B!==V)return;a.set(t.touches[0].pageX,t.touches[0].pageY),r.subVectors(a,s),r=et(r),P-=2*Math.PI*r.x/e.clientWidth*o.rotateSpeed,w-=2*Math.PI*r.y/e.clientHeight*o.rotateSpeed,s.copy(a),o.noSnapReal=o.noSnap,o.update();break;case o.touchMode.ZOOM:if(!0===o.noZoom)return;if(B!==j)return;var n=t.touches[0].pageX-t.touches[1].pageX,i=t.touches[0].pageY-t.touches[1].pageY,u=Math.sqrt(n*n+i*i);f.set(0,u),d.subVectors(f,m),d.y>0?o.dollyOut():d.y<0&&o.dollyIn(),m.copy(f),o.noSnapReal=o.noSnap,o.update();break;case o.touchMode.PAN:if(!0===o.noPan)return;if(B!==k)return;c.set(t.touches[0].pageX,t.touches[0].pageY),l.subVectors(c,h),l=et(l),o.pan(l.x,l.y),h.copy(c),o.noSnapReal=o.noSnap,o.update();break;case o.touchMode.ZOOMPAN:if(!0===o.noPan)return;if(B!==D)return;switch(c.set(t.touches[0].pageX,t.touches[0].pageY),l.subVectors(c,h),l=et(l),o.directionLock){case!1:case"horizontal":o.pan(l.x,l.y),h.copy(c),o.noSnapReal=o.noSnap,o.update();break;case"vertrical":P-=2*Math.PI*l.x/e.clientWidth*o.rotateSpeed,w-=2*Math.PI*l.y/e.clientHeight*o.rotateSpeed,h.copy(c),o.noSnapReal=o.noSnap,o.update();break;default:B=Z}break;default:B=Z}}),!1))};(s.prototype=Object.create(i.prototype)).constructor=s;var a=s;function r(t,e={},n){return this.settings=e,this.range={min:1,max:5e4},this.target={x:0,y:5,z:0},this.position={x:0,y:1e3,z:2e3},this.geometryOffset=this.settings.geometryMargins||{left:50,right:0,top:0,bottom:50,front:0,back:0},this.shelfScreenEdgeOffset=this.settings.screenMargins||{left:0,right:0,top:0,bottom:0},this.componentScreenEdgeOffset={left:0,right:0,top:0,bottom:0},this.geometryBox=new n.Box3,this.camera=new n.PerspectiveCamera(this.settings.fov||20,2,this.range.min,this.range.max),this.controls=new a(this.camera,t,n),this.controls.polarAngle={min:Math.PI/4,max:Math.PI/2},this.controls.azimuthAngle={min:-Math.PI,max:Math.PI/2},this.noTransitionAnimation=!0,this.controls.noTransitionAnimation=!0,this.camera.near=this.range.min,this.camera.far=this.range.max,this.camera.updateProjectionMatrix(),this.camera.position.set(1e3,1e3,1e3),this.controls.target.set(0,200,200),this.controls.new_theta=-.52,this.controls.new_phi=1.4,this.controls.update(),this.updatePoints=function(t,e){let n=this.geometryOffset,o=[t.x-n.left,e.x+n.right,t.y-n.bottom,e.y+n.top,t.z-n.back,e.z+n.front];this.controls.points[0].set(o[0],o[2],o[4]),this.controls.points[1].set(o[1],o[3],o[5]),this.controls.points[2].set(o[0],o[2],o[5]),this.controls.points[3].set(o[0],o[3],o[4]),this.controls.points[4].set(o[0],o[3],o[5]),this.controls.points[5].set(o[1],o[2],o[4]),this.controls.points[6].set(o[1],o[2],o[5]),this.controls.points[7].set(o[1],o[3],o[4])},this.updateGeometry=function(t,e,o){this.dynamicTarget||(this.target.y=.5*e.y+55,this.controls.new_target=new n.Vector3(.5*(this.geometryOffset.left-this.geometryOffset.right),.5*(this.geometryOffset.top-this.geometryOffset.bottom),this.target.z),console.log({new_target:this.controls.new_target})),this.updatePoints(t,e),!0===o&&(this.controls.noTransitionAnimation=!0),this.controls.updateZoom(),this.controls.update(),this.controls.noTransitionAnimation=this.noTransitionAnimation},this.updateAspect=function(t){this.camera.aspect=t,this.camera.updateProjectionMatrix()},this.setShelfViewFinal=function(t,e,n){this.controls.noTransitionAnimation=!0===n||this.noTransitionAnimation,this.setShelfMode(),void 0!==t&&this.updateGeometry(t,e),this.controls.noTransitionAnimation=this.noTransitionAnimation},this.setComponentViewFinal=function(t,e,o){this.setComponentMode(),o=void 0===o?t.x+(e.x-t.x)/2:o;let i=Math.max(o-t.x,e.x-o),s=o+this.geometryOffset.right-this.geometryOffset.left,a=(t.y+e.y)/2+(this.geometryOffset.top-this.geometryOffset.bottom);this.controls.new_target=new n.Vector3(s,a,this.controls.target.z),this.controls.screenEdgeOffset=this.componentScreenEdgeOffset,this.controls.snapTo=[{theta:0,phi:1.4,x:s}],this.updatePoints({x:o-i,y:t.y,z:t.z},{x:o+i,y:e.y,z:e.z}),this.controls.updateZoom(),this.controls.update()},this.setShelfMode=function(){this.controls.noZoom=!0,this.controls.noPan=!0,this.controls.noRotate=!1,this.controls.noLifeZoom=!0,this.controls.noAutoZoom=!0,this.controls.animationStepParameters.distance=120,this.controls.rotateSpeed=.5,this.controls.azimuthAngle={min:-Math.PI/6*2.7,max:Math.PI/6*2.7},this.controls.polarAngle={min:Math.PI/6,max:Math.PI/2},this.controls.screenEdgeOffset=this.settings.screenMargins||this.shelfScreenEdgeOffset,this.geometryOffset=this.settings.geometryMargins||{left:0,right:0,top:0,bottom:0,front:0,back:0},this.controls.mouseButtons={ORBIT:n.MOUSE.LEFT,ZOOM:n.MOUSE.MIDDLE,PAN:n.MOUSE.RIGHT},this.controls.touchMode={ORBIT:1,ZOOM:2,PAN:3,ZOOMPAN:4},this.setTripleSnap(),this.controls.noSnap=!1,this.controls.noSnapReal=!1},this.setComponentMode=function(){this.controls.noZoom=!0,this.controls.noPan=!1,this.controls.noRotate=!0,this.controls.noLifeZoom=!0,this.controls.noAutoZoom=!0,this.controls.animationStepParameters.distance=120,this.controls.rotateSpeed=.4,this.controls.azimuthAngle={min:-Math.PI/6*5,max:Math.PI/6*5},this.controls.polarAngle={min:.22*Math.PI,max:.55*Math.PI},this.controls.screenEdgeOffset=this.componentScreenEdgeOffset,this.geometryOffset={left:60,right:60,top:60,bottom:160,front:0,back:0},this.controls.mouseButtons={PAN:n.MOUSE.LEFT,ZOOM:n.MOUSE.MIDDLE,ORBIT:n.MOUSE.RIGHT},this.controls.touchMode={ORBIT:4,ZOOM:2,PAN:3,ZOOMPAN:1},this.controls.noSnap=!1,this.controls.noSnapReal=!1,this.controls.noTransitionAnimation=this.noTransitionAnimation},this.setFxedMode=function(){this.controls.noZoom=!0,this.controls.noPan=!0,this.controls.noRotate=!0,this.controls.noLifeZoom=!0,this.controls.noAutoZoom=!0,this.controls.noSnap=!1,this.controls.noSnapReal=!1,this.controls.noTransitionAnimation=this.noTransitionAnimation},this.setViewFinal=function(t){switch(t){case"frontTripleSnap":this.controls.new_theta=0,this.controls.new_phi=1.4;break;case"leftTripleSnap":this.controls.new_theta=-.52,this.controls.new_phi=1.4;break;case"front":this.controls.snapTo=[{theta:0,phi:1.4}];break;case"side":this.controls.snapTo=[{theta:-.9,phi:1.4}];break;case"lowSide":this.controls.snapTo=[{theta:-.8,phi:1.6}];break;default:return}this.controls.update(),t.includes("TripleSnap")&&this.setTripleSnap()},this.setTripleSnap=function(){this.geometryOffset=this.settings.geometryMargins||{left:250,right:250,top:300,bottom:300,front:0,back:0},this.settings.snapping&&this.settings.snapping.length>0?this.controls.snapTo=[...this.settings.snapping]:this.controls.snapTo=[{theta:.52,phi:1.4,x:0},{theta:-.52,phi:1.4,x:0},{theta:0,phi:1.4,x:0}]},this.setShelfMode(),this.controls.noTransitionAnimation=this.noTransitionAnimation,this}}]);