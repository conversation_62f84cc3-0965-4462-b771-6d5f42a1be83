const defaultSettings = {
    range: { min: 100, max: 30000 },
    fov: 30,
    snapping: [
        { theta: 0.52, phi: 1.52, x: 0 },
        { theta: -0.52, phi: 1.52, x: 0 },
        { theta: 0, phi: 1.52, x: 0 },
    ],
    globalPhi: 1.52,
    geometryMargins: {
        left: 100, right: 100, top: 0, bottom: 50, front: 0, back: 0,
    },
    screenMargins: {
        left: 0, right: 0, top: 0, bottom: 0,
    },
    component: {
        geometryMargins: {
            left: 60, right: 60, top: 60, bottom: 60, front: 500, back: 0,
        },
    },
    animationSpeed: 1,
    shelfPolarAngles: { min: Math.PI / 6, max: Math.PI / 2 },
};

const customSettings = {
    grid: {
        ...defaultSettings,
        fov: 20,
        snapping: [
            { theta: -0.43, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: -300, bottom: 200, front: 0, back: 0,
        },
    },
    cplus: {
        ...defaultSettings,
        geometryMargins: {
            left: 450, right: 450, top: 200, bottom: 200, front: 0, back: 0,
        },
    },
    desk: {
        ...defaultSettings,
        snapping: [
            { theta: 0.52, phi: 1.3, x: 0 },
            { theta: -0.52, phi: 1.3, x: 0 },
            { theta: 0, phi: 1.3, x: 0 },
        ],
        globalPhi: 1.3,
        geometryMargins: {
            left: 450, right: 450, top: 200, bottom: 200, front: 0, back: 0,
        },
    },
    row: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0, phi: 1.59 },
            { theta: -0.43, phi: 1.59 },
            { theta: 0.43, phi: 1.59 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: 150, bottom: 200, front: 0, back: 0,
        },
        animationSpeed: 1.5,
        globalPhi: 1.59,
        furnitureHeight: {
            min: 230,
            max: 4030,
        },
        marginBottomRange: {
            min: -400,
            max: 200,
        },
    },
    rowDim: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0, phi: 1.59 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: 150, bottom: 200, front: 0, back: 0,
        },
        animationSpeed: 1.5,
        globalPhi: 1.59,
        furnitureHeight: {
            min: 230,
            max: 4030,
        },
        marginBottomRange: {
            min: -400,
            max: 200,
        },
    },
    wardrobe: {
        ...defaultSettings,
        geometryMargins: {
            left: 450, right: 450, top: 400, bottom: 400, front: 0, back: 0,
        },
        snapping: [
            { theta: -0.52, phi: 1.57 },
            { theta: 0, phi: 1.57 },
            { theta: 0.52, phi: 1.57 },
        ],
        globalPhi: 1.57,
        shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
    },
    wardrobeCart: {
        ...defaultSettings,
        geometryMargins: {
            left: 450, right: 450, top: 400, bottom: 400, front: 0, back: 0,
        },
        snapping: [
            { theta: -0.52, phi: 1.57 },
        ],
        globalPhi: 1.57,
        shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
    },
    wardrobeSave: {
        ...defaultSettings,
        geometryMargins: {
            left: 450, right: 450, top: 400, bottom: 400, front: 0, back: 0,
        },
        snapping: [
            { theta: 0, phi: 1.57 },
        ],
        globalPhi: 1.57,
        shelfPolarAngles: { min: 1, max: Math.PI / 1.90 },
    },
    chest: {
        ...defaultSettings,
        geometryMargins: {
            left: 450, right: 450, top: 200, bottom: 200, front: 0, back: 0,
        },
    },
    feed_left_30: {
        ...defaultSettings,
        snapping: [
            { theta: -0.43, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: -300, bottom: 200, front: 0, back: 0,
        },
    },
    feed_right_30: {
        ...defaultSettings,
        snapping: [
            { theta: 0.43, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: -300, bottom: 200, front: 0, back: 0,
        },
    },
    feed_front: {
        ...defaultSettings,
        snapping: [
            { theta: 0, phi: 1.48, x: 0 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: -300, bottom: 200, front: 0, back: 0,
        },
    },
    row_feed_left_30: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: -0.43, phi: 1.59 },
        ],
        geometryMargins: {
            left: 500, right: 500, top: 500, bottom: 500, front: 0, back: 0,
        },
        globalPhi: 1.59,
        furnitureHeight: { min: 230, max: 4030 },
        marginBottomRange: { min: -400, max: 200 },
        heightRange: { min: 1000, max: 2200 },
        phiRange: { min: 1.58, max: 1.61 },
        marginTopRange: { min: -300, max: 500 },
    },
    row_feed_right_30: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0.43, phi: 1.59 },
        ],
        geometryMargins: {
            left: 500, right: 500, top: 500, bottom: 500, front: 0, back: 0,
        },
        globalPhi: 1.59,
        furnitureHeight: { min: 230, max: 4030 },
        marginBottomRange: { min: -400, max: 200 },
        heightRange: { min: 1000, max: 2200 },
        phiRange: { min: 1.58, max: 1.61 },
        marginTopRange: { min: -300, max: 500 },
    },
    row_feed_front: {
        ...defaultSettings,
        fov: 30,
        snapping: [
            { theta: 0, phi: 1.59 },
        ],
        geometryMargins: {
            left: 500, right: 500, top: 500, bottom: 500, front: 0, back: 0,
        },
        globalPhi: 1.59,
        furnitureHeight: { min: 230, max: 4030 },
        marginBottomRange: { min: -400, max: 200 },
        heightRange: { min: 1000, max: 2200 },
        phiRange: { min: 1.58, max: 1.61 },
        marginTopRange: { min: -300, max: 500 },
    },
    wardrobe_feed_left_30: {
        ...defaultSettings,
        snapping: [
            { theta: -0.43, phi: 1.57, x: 0 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: 400, bottom: 400, front: 0, back: 0,
        },
    },
    wardrobe_feed_right_30: {
        ...defaultSettings,
        snapping: [
            { theta: 0.43, phi: 1.57, x: 0 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: 400, bottom: 400, front: 0, back: 0,
        },
    },
    wardrobe_feed_front: {
        ...defaultSettings,
        snapping: [
            { theta: 0, phi: 1.57, x: 0 },
        ],
        geometryMargins: {
            left: 450, right: 450, top: 400, bottom: 400, front: 0, back: 0,
        },
    },
};

export { defaultSettings, customSettings };
