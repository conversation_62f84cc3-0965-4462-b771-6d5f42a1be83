import axios from 'axios';
import FurnitureProjectManagerShelf from '../../libs/fpm-original';
import FurnitureProjectManagerT03 from '../../libs/fpm-tone';
import FurnitureProjectManagerT13 from '../../libs/fpm-edge';
import FurnitureProjectManagerExpressions from '../../libs/fpm-expressions.min';
import ConfigurationApi from '../../libs/fpm-sofa';
import { RendererUtilsAPI, ScreenShotRendererAPI } from "../../libs/space-renderer.min";
import { buildSpaceRenderFormat } from "@libs/space-describer.min";
import { shelfTypeConst, cameraAngleConst, imageMode, cameraAngleFeedValue } from "./constants";


let levelReady = false;

export const logToScreen = (text) => {
  $('#content').prepend(`${text}<br/>`);
};

function renderToDom(screenshot, width, height) {
  // function for debugging - show image on the page
  const img = document.createElement('img');
  img.src = `data:image/png;base64,${screenshot}`;
  img.style.margin = '5px 5px';
  img.style.width = width;
  img.style.height = height;
  document.body.append(img);
}

async function setupRenderer(furnitureType) {
  const envSetting = furnitureType === 'jetty' ? 'shelf' : 'wardrobe';
  const cameraSetting = furnitureType === 'jetty' ? 'row' : 'wardrobe';
  RendererUtilsAPI.setAssetsSource('/r_static/src_webgl/t13');
  RendererUtilsAPI.setCanvasWrapperId('conf');
  RendererUtilsAPI.setEnvironmentSettingsPreset(envSetting);
  RendererUtilsAPI.setCameraSettingsPreset(cameraSetting);
  ScreenShotRendererAPI.initialize();
  await ScreenShotRendererAPI.loadAssets(furnitureType !== 'jetty');
}

function initOffScreenRenderer(geom) {
  const spaceRenderFormat = buildSpaceRenderFormat([{ ...geom, dimensions: [] }], {
    name: 'tylko-space',
    category: geom.category,
    system: 'col',
    shelfOffset: { x: 0, y: 0, z: 0 },
  });

  if (!levelReady) {
    ScreenShotRendererAPI.setupLevel(spaceRenderFormat);
    levelReady = true;
  }
  ScreenShotRendererAPI.updateLevel(spaceRenderFormat, true);
}

function createFurnitureGeometry(furniture, dna) {
  let FPM;

  if ([shelfTypeConst.TYPE01, shelfTypeConst.TYPE02, shelfTypeConst.VENEER_TYPE01].includes(furniture.shelf_type)) {
    FPM = new FurnitureProjectManagerShelf().withDNA(dna);
  } else if (furniture.shelf_type === shelfTypeConst.TYPE03) {
    FPM = new FurnitureProjectManagerT03().withDNA(dna);
  } else if (furniture.shelf_type === shelfTypeConst.TYPE13) {
    FPM = new FurnitureProjectManagerT13().withDNA(dna);
  } else if ([shelfTypeConst.TYPE23, shelfTypeConst.TYPE24, shelfTypeConst.TYPE25].includes(furniture.shelf_type)) {
    FPM = new FurnitureProjectManagerExpressions().withDNA(dna);
  } else if (furniture.shelf_type === shelfTypeConst.SOFA_TYPE01) {
    const capi = new ConfigurationApi();
    FPM = capi.withDNA(dna);
  }

  FPM.loadPresetFromJetty(furniture);

  return FPM.getGeometry();
}

async function sendFurnitureData(mode, geom, screenshot, furnitureType, furnitureId = null, configId = null, feedItemID = null) {
  // TODO: Add admin token for auth
  switch (mode) {
    case imageMode.FEEDS: {
      const previewData = {
        magic_preview: screenshot,
        item_id: feedItemID,
        config_id: configId,
      };
      axios.post('/api/v2/feeds/feed_image/', previewData).then(() => logToScreen('Feed image send'));
    }
      break;
    case imageMode.RECALCULATE_GEOMETRY: {
      const previewData = {
        ...geom,
      }
      await axios.patch(`/api/v1/gallery/${furnitureType}/${furnitureId}/`, previewData);
      logToScreen('Gallery image send');
    }
      break;
    case imageMode.PREVIEW: {
      const previewData = {
        magic_preview: screenshot,
      };
      axios.patch(`/api/v1/gallery/${furnitureType}/${furnitureId}/`, previewData).then(() => logToScreen('Gallery image send'));
    }
      break;
  }

}

async function fetchDna(shelfType, collectionType) {
  const data = await axios.get(`/api/v1/furniture_dna/?shelf_type=${shelfType}&collection_type=${collectionType}&dna_version=666`)
  return data.data.json;
}
function generateScreenshot(imageConfig = null) {
  const urlParams = new URLSearchParams(window.location.search);
  const furnitureType = window.furnitureType ? window.furnitureType : urlParams.get('furnitureType');
  const libitemsEnabled = urlParams.get('libitemsEnabled') !== 'false';
  let cameraAngle = cameraAngleConst.LEFT_30;
  if (imageConfig) {
    cameraAngle = cameraAngleFeedValue[imageConfig];
  }

  let cameraSetting = `feed_${cameraAngle}`;
  if (furnitureType === 'watty') {
    cameraSetting = `wardrobe_${cameraSetting}`;
  }

  logToScreen('Creating a screenshot');

  return ScreenShotRendererAPI.drawSpecificView({
    color: 0xffffff,
    transparency: 0,
    interiorVisible: true,
    itemsVisible: libitemsEnabled,
    width: 1000,
    height: 1000,
  }, cameraSetting);
}

async function runScreenTool() {
  const response = await axios.post('/api/v1/render_tasks/webgl/get_task/');
  if (response.status === 204) {
    logToScreen('No tasks, waiting.');
    await new Promise(r => setTimeout(r, 20000));
    return;
  }
  const task = response.data;
  const shelfId = task.furniture_id;
  const furnitureType = task.furniture_type;
  const mode = task.task_type;
  const furniture = task.geometry;
  const imageConfig = task.image_configuration;
  const feedItemID = task.feed_item;
  if (furniture.shelf_type === shelfTypeConst.SOFA_TYPE01) {
    furniture.category = 'sofa';
  }


  const dna = await fetchDna(furniture.shelf_type, furniture.configurator_params.additional_parameters.collection_type);
  logToScreen('Setting up FPM and creating geometry');
  const geom = createFurnitureGeometry(furniture, dna);
  geom.category = furniture.category;
  let screenshot = null;

  if (mode !== imageMode.RECALCULATE_GEOMETRY) {
    logToScreen('Setting the renderer');
    await setupRenderer(furnitureType);
    initOffScreenRenderer(geom);
    screenshot = generateScreenshot(imageConfig).split(',')[1];

    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('debug')) renderToDom(screenshot, '300px', '300px');
  }

  logToScreen('Sending data');
  await sendFurnitureData(mode, geom, screenshot, furnitureType, shelfId, imageConfig, feedItemID);

  logToScreen('Done');
  await axios.put(`/api/v1/render_tasks/webgl/${task.id}/finish_task/ `, {'status': 'completed'});
}

async function generateImgOnly() {
  const furniture = window.geometry;
  const furnitureType = window.furnitureType;

  await setupRenderer(furnitureType);
  initOffScreenRenderer(furniture);
  const screenshot = generateScreenshot().split(',')[1];

  renderToDom(screenshot, '*', '*');
}

async function startScreenTool() {
  try {
    if (window.geometry) {
      await generateImgOnly();
    } else {
      await runScreenTool();
      await new Promise(r => setTimeout(r, 2000));
    }
  } catch (error) {
    logToScreen(`<p style="color: red"> ${error} </p>`);
  }
  if (!window.geometry) location.reload();
}

startScreenTool();
