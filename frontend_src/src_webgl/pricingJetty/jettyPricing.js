/* eslint-disable camelcase */
/* eslint-disable no-shadow */
/* eslint-disable class-methods-use-this */
/* eslint-disable no-param-reassign */
import getTotalCapacity from '../build3dJetty/helpers/getTotalCapacity';

function independentHorizontalJoiner(horizontalsRaw) {
    const horizontals = horizontalsRaw.sort((a, b) => (b.y1 !== a.y1 ? a.y1 - b.y1 : a.x1 - b.x1));
    const joinedHorizontals = [];
    let leftSide = null;
    let currentHeight = null;
    for (let i = 0; i < horizontals.length; i += 1) {
        // if new height
        if (currentHeight === null) {
            currentHeight = horizontals[i].y1;
        }
        // if there is no left side yet (so new level or new horizontal)
        if (leftSide === null) {
            leftSide = horizontals[i].x1;
        }
        // if it is last one
        if (
            (i === horizontals.length - 1)
            || (horizontals[i + 1].y1 !== horizontals[i].y1)
            || (horizontals[i].x2 !== horizontals[i + 1].x1)
        ) {
            joinedHorizontals.push({
                y1: currentHeight,
                y2: currentHeight,
                x1: leftSide,
                x2: horizontals[i].x2,
            });
            currentHeight = null;
            leftSide = null;
        }
    }
    return joinedHorizontals;
}


function independentSupportCleaner(supports, backs) {
    const backsSorted = {};

    backs.forEach(b => {
        const bMin = Math.min(b.y1, b.y2);
        if (Object.keys(backsSorted).indexOf(bMin.toString()) === -1) {
            backsSorted[bMin] = [];
        }
        backsSorted[bMin].push(b);
    });
    const newSupports = [];
    const supportCheck = (sX, b) => Math.min(b.x1, b.x2) <= sX && sX <= Math.max(b.x1, b.x2);
    for (let i = 0; i < supports.length; i += 1) {
        let skipThis = false;
        const s = supports[i];
        const sMin = Math.min(s.y1, s.y2);
        if (Object.keys(backsSorted).indexOf(sMin.toString()) > -1) {
            [s.x1, s.x2].forEach(x => {
                backsSorted[sMin].forEach(b => {
                    if (supportCheck(x, b)) skipThis = true;
                });
            });
        }
        if (!skipThis) {
            newSupports.push(s);
        }
    }
    return newSupports;
}

function getClosestAvailableHeight(height, physicalProductVersion) {
    let availableHeights;
    if (physicalProductVersion === 1) {
        availableHeights = [182, 282, 382];
    } else {
        availableHeights = [182, 282, 382, 482, 582, 682, 782];
    }
    return availableHeights.reduce((a, b) => (Math.abs(height - a) < Math.abs(height - b) ? a : b));
}

class JettyCostCalculator {
    constructor(points, elemType, priceConfig, possiblePhysicalProductVersions = [1, 2]) {
        this.points = points;
        this.productType = Number(points.shelf_type);

        // adjust depth to the available price configurations
        const shelfDepth = Number(points.depth);
        this.depthMM = shelfDepth <= 320 ? 320 : 400;
        this.depthM = this.depthMM * 0.001;

        // adjust material to the available pricing material groups
        const shelfMaterial = Number(points.material);
        if (this.productType === 0) {
            // for T01 all shelves have the same price configs
            this.material = 1;
        } else if (this.productType === 1 && shelfMaterial !== 2) {
            // for T02 all but midnight blue (2) have the same price configs
            this.material = 3;
        } else if (this.productType === 2) {
            // same price configs for all the veneer shelves
            this.material = 0;
        } else {
            this.material = shelfMaterial;
        }
        // adjust physicalProductVersion in case it is missing
        // or not yet implemented for given productType
        const physicalProductVersion = Number(points.physical_product_version);
        this.physicalProductVersion = physicalProductVersion || 1;
        // assign possiblePhysicalProductVersions as an attribute
        // to check if the calculator subclass should be used for the given shelf
        this.possiblePhysicalProductVersions = possiblePhysicalProductVersions;
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            const elemPriceConfiguration = priceConfig[elemType];
            this.COGSPriceConfiguration = elemPriceConfiguration[1];
            this.packagingPriceConfiguration = elemPriceConfiguration[2];
            this.weightPriceConfiguration = elemPriceConfiguration[3];
        }
    }

    getSizeData() {
        return [];
    }

    getCOGS() {
        return 0;
    }

    getPackagingCost() {
        return 0;
    }

    getWeight() {
        return 0;
    }

    getCostsData() {
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            const sizeData = this.getSizeData();
            return [
                this.getCOGS(sizeData),
                this.getPackagingCost(sizeData),
                this.getWeight(sizeData),
            ];
        }
        return [0, 0, 0];
    }
}

class HorizontalsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 1;
        super(points, elemType, priceConfig);
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material];
        }
    }

    getSizeData() {
        const horiList = independentHorizontalJoiner(this.points.horizontals);

        let horiTotalLen = 0.0;
        let horiCount = horiList.length;
        let horiAdditionalFactor = 0.0;
        horiList.forEach(hori => {
            const horiLen = Math.abs(hori.x2 - hori.x1);
            horiTotalLen += horiLen;
            if (horiLen > 2400) {
                horiCount += 1;
            } else if (horiLen > 2300) {
                horiAdditionalFactor += (1.0 - ((2400.0 - horiLen) / (2400.0 - 2300)));
            }
        });
        const horiTotalLenM = horiTotalLen * 0.001;
        const horiTotalPerimM = (horiTotalLenM + (horiCount * this.depthM)) * 2;
        const horiTotalAreaM2 = horiTotalLenM * this.depthM;

        return [horiTotalAreaM2, horiTotalPerimM, [horiCount, horiAdditionalFactor]];
    }

    getCOGS(sizeData) {
        const [horiTotalAreaM2, horiTotalPerimM, [horiCount, horiAdditionalFactor]] = sizeData;
        const costBase = horiTotalAreaM2 * this.COGSPriceConfiguration.area
            + horiTotalPerimM * this.COGSPriceConfiguration.perimeter
            + horiCount * this.COGSPriceConfiguration.unit;
        const costAdditional = horiAdditionalFactor * this.COGSPriceConfiguration.unit;
        return [costBase, costAdditional];
    }

    getPackagingCost(sizeData) {
        const horiTotalAreaM2 = sizeData[0];
        return horiTotalAreaM2 * this.packagingPriceConfiguration.area;
    }

    getWeight(sizeData) {
        const horiTotalAreaM2 = sizeData[0];
        return horiTotalAreaM2 * this.weightPriceConfiguration.area;
    }
}

class JointsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 7;
        super(points, elemType, priceConfig);
    }

    getSizeData() {
        const horiList = independentHorizontalJoiner(this.points.horizontals);
        let jointsCount = 0;
        let jointsAdditionalFactor = 0.0;
        horiList.forEach(hori => {
            const horiLen = Math.abs(hori.x2 - hori.x1);
            if (horiLen > 2400) {
                jointsCount += 1;
            } else if (horiLen > 2300) {
                jointsAdditionalFactor += (1.0 - ((2400.0 - horiLen) / (2400.0 - 2300)));
            }
        });
        return [0, 0, [jointsCount, jointsAdditionalFactor]];
    }

    getCOGS(sizeData) {
        // TODO: przeniesc sizeData do konstruktora i usunac powtorzenia ze wszystkich kalkulatorow
        const [jointsCount, jointsAdditionalFactor] = sizeData[2];
        const jointsCostBase = jointsCount * this.COGSPriceConfiguration.unit;
        const jointsCostAdditional = jointsAdditionalFactor * this.COGSPriceConfiguration.unit;
        return [jointsCostBase, jointsCostAdditional];
    }

    getPackagingCost() {
        return 0;
    }

    getWeight() {
        return 0;
    }
}

class LegsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig, jointsCOGS) {
        const elemType = 6;
        super(points, elemType, priceConfig);
        this.hasJoints = jointsCOGS > 0;
    }

    getSizeData() {
        let legsCount = this.points.legs.length;
        if (legsCount > 0 && this.hasJoints) {
            legsCount += 2;
        }
        return [0, 0, legsCount];
    }

    getCOGS(sizeData) {
        const legsCount = sizeData[2];
        return legsCount * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost() {
        return 0;
    }

    getWeight() {
        return 0;
    }
}

class LongLegsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 10;
        const possiblePhysicalProductVersions = [2];
        super(points, elemType, priceConfig, possiblePhysicalProductVersions);
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            if (this.productType === 1) {
                this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material];
            }
        }
    }

    getSizeData() {
        const longLegsCount = this.points.long_legs.length;
        return [0, 0, longLegsCount];
    }

    getCOGS(sizeData) {
        const longLegsCount = sizeData[2];
        if (longLegsCount > 0) {
            return longLegsCount * this.COGSPriceConfiguration.unit;
        }
        return 0;
    }

    getPackagingCost(sizeData) {
        const llCount = sizeData[2];
        const llOccurrence = llCount > 0;
        if (llOccurrence) {
            return (
                llOccurrence * this.packagingPriceConfiguration.occurrence
                + llCount * this.packagingPriceConfiguration.unit
            );
        }
        return 0;
    }

    getWeight(sizeData) {
        return sizeData[2] * this.weightPriceConfiguration.unit;
    }
}

class VerticalsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 0;
        super(points, elemType, priceConfig);
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material][this.depthMM];
        }
    }

    getSizeData() {
        const vertHeights = this.points.verticals.map(vert => Math.abs(vert.y2 - vert.y1));
        const totalHeight = vertHeights.reduce((a, b) => a + b, 0) * 0.001;
        const totalArea = totalHeight * this.depthM;
        return [totalArea, totalHeight, vertHeights.length];
    }

    getCOGS(sizeData) {
        const [vertTotalArea, vertTotalHeight, vertCount] = sizeData;
        return vertTotalArea * this.COGSPriceConfiguration.area
            + vertTotalHeight * this.COGSPriceConfiguration.perimeter
            + vertCount * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost(sizeData) {
        const [vertTotalArea, , vertCount] = sizeData;
        return vertTotalArea * this.packagingPriceConfiguration.area
            + vertCount * this.packagingPriceConfiguration.unit;
    }

    getWeight(sizeData) {
        return sizeData[0] * this.weightPriceConfiguration.area;
    }
}

class SupportsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 2;
        const possiblePhysicalProductVersions = [1];
        super(points, elemType, priceConfig, possiblePhysicalProductVersions);
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material];
        }
        this.widthM = 0.125;
    }

    getSizeData() {
        const suppList = independentSupportCleaner(this.points.supports, this.points.backs);

        const suppHeights = suppList.map(supp => Math.floor(Math.abs(supp.y2 - supp.y1)));
        const totalHeight = suppHeights.reduce((a, b) => a + b, 0);
        const totalArea = totalHeight * 0.001 * this.widthM;
        return [totalArea, totalHeight, suppHeights.length];
    }

    getCOGS(sizeData) {
        if (this.points.supports.length === 0) {
            return 0;
        }
        const [, vertTotalHeight, vertCount] = sizeData;
        return vertTotalHeight * this.COGSPriceConfiguration.perimeter
            + vertCount * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost() {
        return 0;
    }

    getWeight(sizeData) {
        if (this.points.supports.length === 0) {
            return 0;
        }
        return sizeData[0] * this.weightPriceConfiguration.area;
    }
}

class BacksCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 3;
        super(points, elemType, priceConfig);
        // For backs only, white is the most expensive color
        if (points.shelf_type === 0) this.material = 0;
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material];
        }
    }

    getSizeData() {
        const backLengths = this.points.backs.map(b => Math.abs(b.x2 - b.x1) * 0.001);
        const backHeights = this.points.backs.map(b => Math.abs(b.y2 - b.y1) * 0.001);

        let totalAreaM2 = 0.0;
        let totalPerimM = 0.0;

        backLengths.forEach((value, i) => {
            totalAreaM2 += value * backHeights[i];
            totalPerimM += 2 * (value + backHeights[i]);
        });

        return [totalAreaM2, totalPerimM, backLengths.length];
    }

    getCOGS(sizeData) {
        const [totalAreaM2, totalPerimM, count] = sizeData;
        return totalAreaM2 * this.COGSPriceConfiguration.area
            + totalPerimM * this.COGSPriceConfiguration.perimeter
            + count * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost(sizeData) {
        const totalAreaM2 = sizeData[0];
        const isOccurring = (sizeData[2] > 0);
        return totalAreaM2 * this.packagingPriceConfiguration.area
            + isOccurring * this.packagingPriceConfiguration.occurrence;
    }

    getWeight(sizeData) {
        const totalAreaM2 = sizeData[0];
        return totalAreaM2 * this.weightPriceConfiguration.area;
    }
}

class GrommetsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 12;
        const possiblePhysicalProductVersions = [2];
        super(points, elemType, priceConfig, possiblePhysicalProductVersions);
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material];
        }
    }

    getSizeData() {
        const grommetsCount = this.points.cable_management.length;
        return [0, 0, grommetsCount];
    }

    getCOGS(sizeData) {
        return sizeData[2] * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost(sizeData) {
        return 0;
    }

    getWeight(sizeData) {
        return 0;
    }
}

class DoorsCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 5;
        super(points, elemType, priceConfig);
        this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material][this.depthMM];
    }

    getSizeData() {
        let totalArea = 0.0;
        const hasBacks = this.points.backs.length > 0;
        const doorsCounter = this.points.doors.reduce((accumulator, door) => {
            let height = Math.abs(door.y2 - door.y1);
            height = getClosestAvailableHeight(height);
            const width = Math.abs(door.x2 - door.x1);

            const key = `${height}-${door.type}`;
            if (!accumulator.widths[key]) {
                accumulator.widths[key] = Math.floor(width);
                accumulator.counts[key] = 1;
            } else {
                accumulator.widths[key] += Math.floor(width);
                accumulator.counts[key] += 1;
            }
            totalArea += height * width * 0.001 * 0.001;
            return accumulator;
        }, { widths: {}, counts: {} });
        return [totalArea, doorsCounter.widths, [doorsCounter.counts, hasBacks]];
    }

    getCOGS(sizeData) {
        const [totalAreaM2, widths, [counts, hasBacks]] = sizeData;
        let cogs = 0.0;
        Object.keys(widths).forEach(key => {
            const [height, type] = key.split('-');
            cogs += this.COGSPriceConfiguration[height][type].perimeter * widths[key];
            cogs += this.COGSPriceConfiguration[height][type].unit * counts[key];
        }, this);
        return cogs;
    }

    getPackagingCost(sizeData) {
        const totalAreaM2 = sizeData[0];
        const hasBacks = sizeData[2][1];
        const count = Object.values(sizeData[2][0]).reduce((a, b) => a + b, 0);
        const hasDoors = (count > 0);

        const countDoorsBacksOccurrence = hasDoors && !hasBacks;
        return totalAreaM2 * this.packagingPriceConfiguration.area
            + count * this.packagingPriceConfiguration.unit
            + countDoorsBacksOccurrence * this.packagingPriceConfiguration.occurrence;
    }

    getWeight(sizeData) {
        const totalAreaM2 = sizeData[0];
        return totalAreaM2 * this.weightPriceConfiguration.area;
    }
}

class DrawersCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 4;
        super(points, elemType, priceConfig);
        this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material][this.depthMM];
    }

    getSizeData() {
        let totalFrontArea = 0.0;
        let totalOtherArea = 0.0;

        const drawersCounter = this.points.drawers.reduce((accumulator, drawer) => {
            let height = Math.abs(drawer.y2 - drawer.y1);
            height = getClosestAvailableHeight(height);

            const width = Math.abs(drawer.x2 - drawer.x1);
            const widthM = width * 0.001;
            const heightM = height * 0.001;

            if (!accumulator.widths[height]) {
                accumulator.widths[height] = width;
                accumulator.counts[height] = 1;
            } else {
                accumulator.widths[height] += width;
                accumulator.counts[height] += 1;
            }
            totalFrontArea += heightM * widthM;
            totalOtherArea += widthM * this.depthM
                + 2 * heightM * this.depthM
                + widthM * heightM;

            return accumulator;
        }, { widths: {}, counts: {} });
        return [[totalFrontArea, totalOtherArea], drawersCounter.widths, drawersCounter.counts];
    }

    getCOGS(sizeData) {
        const [, widths, counts] = sizeData;
        let cogs = 0.0;
        Object.keys(widths).forEach(height => {
            cogs += this.COGSPriceConfiguration[height].perimeter * widths[height];
            cogs += this.COGSPriceConfiguration[height].unit * counts[height];
        }, this);
        return cogs;
    }

    getPackagingCost(sizeData) {
        const count = Object.values(sizeData[2]).reduce((a, b) => a + b, 0);
        const hasDrawers = (count > 0);
        return hasDrawers * this.packagingPriceConfiguration.occurrence
            + count * this.packagingPriceConfiguration.unit;
    }

    getWeight(sizeData) {
        const [frontAreaM2, otherAreaM2] = sizeData[0];
        return frontAreaM2 * this.weightPriceConfiguration[0].area
            + otherAreaM2 * this.weightPriceConfiguration[1].area;
    }
}

class InsertsHoriCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 11;
        const possiblePhysicalProductVersions = [2];
        super(points, elemType, priceConfig, possiblePhysicalProductVersions);
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material];
        }
    }

    getSizeData() {
        const inserts = this.points.inserts.filter(ins => ins.subtype === 'h');
        const insertWidthsMM = inserts.map(b => Math.abs(b.x2 - b.x1));
        const insertDepthDifference = 48;
        const insertDepthsMM = inserts.map(b => this.depthMM - insertDepthDifference);

        let totalAreaM2 = 0.0;
        let totalPerimM = 0.0;

        insertWidthsMM.forEach((value, i) => {
            totalAreaM2 += value * insertDepthsMM[i] * 0.001 * 0.001;
            totalPerimM += 2 * (value + insertDepthsMM[i]) * 0.001;
        });
        return [totalAreaM2, totalPerimM, insertWidthsMM.length];
    }

    getCOGS(sizeData) {
        const [totalAreaM2, totalPerimM, count] = sizeData;
        return totalAreaM2 * this.COGSPriceConfiguration.area
            + totalPerimM * this.COGSPriceConfiguration.perimeter
            + count * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost(sizeData) {
        return sizeData[0] * this.packagingPriceConfiguration.area;
    }

    getWeight(sizeData) {
        if (sizeData[2] === 0) {
            return 0;
        }
        const totalAreaM2 = sizeData[0];
        return totalAreaM2 * this.weightPriceConfiguration.area;
    }
}

class WallFittingCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 13;
        const possiblePhysicalProductVersions = [2];
        super(points, elemType, priceConfig, possiblePhysicalProductVersions);
    }

    getSizeData() {
        const wallFittingsThresholds = [0, 550, 1500, 2400, 2800, 3200, 4500];
        let wallFittingsCount = wallFittingsThresholds.findIndex(
            width => width > this.points.width,
        );
        wallFittingsCount = wallFittingsCount === -1 ? 6 : wallFittingsCount;
        return [0, 0, wallFittingsCount];
    }

    getCOGS(sizeData) {
        return sizeData[2] * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost(sizeData) {
        return 0;
    }

    getWeight(sizeData) {
        return 0;
    }
}

class OtherCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 8;
        super(points, elemType, priceConfig);
    }

    getSizeData() {
        return [0, 0, 0];
    }

    getCOGS() {
        return 0;
    }

    getPackagingCost() {
        return this.packagingPriceConfiguration.unit;
    }

    getWeight() {
        if (this.physicalProductVersion === 1) {
            return this.weightPriceConfiguration.unit;
        }
        return 0;
    }
}

class ShelfCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 14;
        const possiblePhysicalProductVersions = [2];
        super(points, elemType, priceConfig, possiblePhysicalProductVersions);
    }

    getSizeData() {
        return [0, 0, 1];
    }

    getCOGS(sizeData) {
        return sizeData[2] * this.COGSPriceConfiguration.unit;
    }

    getPackagingCost(sizeData) {
        return 0;
    }

    getWeight(sizeData) {
        return 0;
    }
}

class PlinthCostCalculator extends JettyCostCalculator {
    constructor(points, priceConfig) {
        const elemType = 15;
        const possiblePhysicalProductVersions = [2];
        super(points, elemType, priceConfig, possiblePhysicalProductVersions);
        if (this.possiblePhysicalProductVersions.includes(this.physicalProductVersion)) {
            this.COGSPriceConfiguration = this.COGSPriceConfiguration[this.material];
        }
    }

    getSizeData() {
        const isPlinthAvailable = this.points.plinth.length > 0 && this.productType !== 1;
        return [this.points.width, this.depthMM, isPlinthAvailable];
    }

    getCOGS(sizeData) {
        const [totalAreaM2, totalPerimM, count] = sizeData;
        if (count > 0) {
            return totalAreaM2 * this.COGSPriceConfiguration.area
                + totalPerimM * this.COGSPriceConfiguration.perimeter
                + count * this.COGSPriceConfiguration.unit;
        }
        return 0;
    }

    getPackagingCost(sizeData) {
        return 0;
    }

    getWeight(sizeData) {
        return 0;
    }
}

export default class JettyPricing {
    constructor(points, serviceURL) {
        this.pointsInitial = points;
        this.serviceURL = serviceURL;
        // shelf type needed here to get the correct priceConfiguration
        this.shelfType = this.pointsInitial.shelf_type;
        // priceConfiguration for a single shelf type + physical product version
        // for all colours and patterns - no need to update
        const physicalProductVersion = this.pointsInitial.physical_product_version;
        this.physicalProductVersion = physicalProductVersion || 1;
        this.fullConfiguration = this.getConfigurationFromDB(
            this.pointsInitial.shelf_type,
            this.physicalProductVersion,
        );
        this.priceConfiguration = this.fullConfiguration.prices;
        this.marginsConfiguration = this.fullConfiguration.margins;
    }

    getConfigurationFromDB(productType, physicalProductVersion) {
        const request = new XMLHttpRequest();
        const endpoint = `${this.serviceURL}/pricing/config/?product_type=${productType}&physical_product_version=${physicalProductVersion}`;
        request.open('GET', endpoint, false);
        request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
        request.send();
        if (request.status === 200) {
            const data = JSON.parse(request.response);
            return data;
        }
        // We reached our target server, but it returned an error
        console.log('error request', request.responseText);
    }

    getJettyCogsDictAndTotalWeight(points) {
        const [horiCOGS, horiPackagingCost, horiWeight] = new HorizontalsCostCalculator(points, this.priceConfiguration).getCostsData();
        const [vertCOGS, vertPackagingCost, vertWeight] = new VerticalsCostCalculator(points, this.priceConfiguration).getCostsData();
        const [backCOGS, backPackagingCost, backWeight] = new BacksCostCalculator(points, this.priceConfiguration).getCostsData();
        const [doorCOGS, doorPackagingCost, doorWeight] = new DoorsCostCalculator(points, this.priceConfiguration).getCostsData();
        const [drawerCOGS, drawerPackagingCost, drawerWeight] = new DrawersCostCalculator(points, this.priceConfiguration).getCostsData();
        const [jointsCOGS, jointsPackagingCost, jointsWeight] = new JointsCostCalculator(points, this.priceConfiguration).getCostsData();
        const [legsCOGS, legsPackagingCost, legsWeight] = new LegsCostCalculator(points, this.priceConfiguration, jointsCOGS[0]).getCostsData();
        const [, otherPackagingCost, otherWeight] = new OtherCostCalculator(points, this.priceConfiguration).getCostsData();

        const [suppCOGS, suppPackagingCost, suppWeight] = new SupportsCostCalculator(points, this.priceConfiguration).getCostsData();
        const [grommetCOGS, grommetPackagingCost, grommetWeight] = new GrommetsCostCalculator(points, this.priceConfiguration).getCostsData();
        let longLegsCOGS;
        let longLegsPackagingCost;
        let longLegsWeight;
        if (points.shelf_type == 1) {
            [longLegsCOGS, longLegsPackagingCost, longLegsWeight] = new LongLegsCostCalculator(points, this.priceConfiguration).getCostsData();
        } else {
            [longLegsCOGS, longLegsPackagingCost, longLegsWeight] = new PlinthCostCalculator(points, this.priceConfiguration).getCostsData();
        }
        const [insertCOGS, insertPackagingCost, insertWeight] = new InsertsHoriCostCalculator(points, this.priceConfiguration).getCostsData();
        const [shelfCOGS, shelfPackagingCost, shelfWeight] = new ShelfCostCalculator(points, this.priceConfiguration).getCostsData();
        const [wallFittingCOGS, wallFittingPackaging, wallFittingWeight] = new WallFittingCostCalculator(points, this.priceConfiguration).getCostsData();

        const weight = horiWeight + vertWeight + suppWeight + backWeight
            + grommetWeight + doorWeight + drawerWeight + jointsWeight
            + legsWeight + longLegsWeight + insertWeight + wallFittingWeight
            + otherWeight + shelfWeight;

        const packaging = horiPackagingCost + vertPackagingCost
            + suppPackagingCost + backPackagingCost + grommetPackagingCost
            + doorPackagingCost + drawerPackagingCost + jointsPackagingCost
            + legsPackagingCost + longLegsPackagingCost + insertPackagingCost
            + wallFittingPackaging + shelfPackagingCost + otherPackagingCost;

        const pricingDict = {
            H: horiCOGS[0],
            V: vertCOGS,
            S: suppCOGS,
            B: backCOGS,
            G: grommetCOGS,
            D: doorCOGS,
            T: drawerCOGS,
            J: jointsCOGS[0],
            J_additional: jointsCOGS[1] + horiCOGS[1],
            L: legsCOGS,
            Ll: longLegsCOGS,
            Ih: insertCOGS,
            Wf: wallFittingCOGS,
            Sh: shelfCOGS,
            other: packaging,
        };
        return [pricingDict, weight];
    }

    getMarginVarBaseFromCOGS(cogsDict) {
        const manufacturingCost = Object.values(cogsDict).reduce((sum, value) => sum + value, 0);
        return Number(this.shelfType) === 0 ? manufacturingCost - cogsDict.T - cogsDict.D - cogsDict.B : manufacturingCost;
    }

    getMarginVarBaseFromFrontArea(points) {
        // FPM is fixed now but we don't want to change the prices yet
        let height = points.height - 30;
        if (points.long_legs.length > 0) {
            height -= 100;
        }
        return points.width * 0.001 * height * 0.001;
    }

    getMarginVarFactorFromCOGS(base) {
        /* function:
        y = param_a / (cogs ^ param_b) - 1

        a parametrized version of y = 1 / x
        const -1 needed as otherwise the function would never reach 0

        param_b calculated in order to include points:
        (x1, y_max)
        (x2, y_min) */
        const log_arg = (this.marginsConfiguration.y_max + 1) / (this.marginsConfiguration.y_min + 1);
        const log_base = this.marginsConfiguration.x2 / this.marginsConfiguration.x1;
        const param_b = Math.log(log_arg) / Math.log(log_base);
        let y = this.marginsConfiguration.param_a / (base ** param_b) - 1;

        if (y < this.marginsConfiguration.y_min) {
            return this.marginsConfiguration.y_min;
        }
        if (y > this.marginsConfiguration.y_max) return this.marginsConfiguration.y_max;

        if (base < 1000) y += this.marginsConfiguration.additional_y * (1000 - base) / 1000.0;

        return y;
    }

    getMarginVarFactorFromFrontArea(base) {
        if (base <= this.marginsConfiguration.x1) {
            return this.marginsConfiguration.y_max;
        }
        if (base >= this.marginsConfiguration.x2) {
            return this.marginsConfiguration.y_min;
        }
        const yDiff = this.marginsConfiguration.y_max - this.marginsConfiguration.y_min;
        const maxDist = this.marginsConfiguration.x2 - this.marginsConfiguration.x1;
        const baseDist = 1 - ((maxDist - (this.marginsConfiguration.x2 - base)) / maxDist);
        return baseDist * yDiff;
    }

    getMarginVarFactor(points, cogsDict) {
        if (this.physicalProductVersion === 1) {
            const base = this.getMarginVarBaseFromCOGS(cogsDict);
            return this.getMarginVarFactorFromCOGS(base);
        }
        const base = this.getMarginVarBaseFromFrontArea(points);
        return this.getMarginVarFactorFromFrontArea(base);
    }

    getMarginValueBasedFactor(points) {
        // needed here to decide if VBP applies
        const shelfType = Number(points.shelf_type);
        const shelfMaterial = Number(points.material);
        const shelfPattern = Number(points.pattern);
        const physicalProductVersion = Number(points.physical_product_version || 1);
        let VBMFactor = 0.0;
        // T02 in Ceramic Red
        if (shelfType === 1 && shelfMaterial === 1) {
            VBMFactor += 0.2;
        }
        // T01 & T02 starting at 295 cm, reaching maximum factor at 310 cm
        if ([0, 1].includes(shelfType) && points.width > 2950) {
            const dist = points.width < 3100 ? (1 - ((3100 - points.width) / (3100 - 2950))) : 1;
            VBMFactor += dist * 0.2;
        }

        // TREX T01 & T02 and height over 250
        if (physicalProductVersion === 1 && [0, 1].includes(shelfType) && points.height >= 2500) {
            VBMFactor += 0.1;
        }

        // TREX T01 & T02 and depth 400
        if (physicalProductVersion === 1 && [0, 1].includes(shelfType) && points.depth === 400) {
            VBMFactor += 0.05;
        }
        return VBMFactor;
    }

    getJettyMarginsDict(points, cogsDict, logisticCost) {
        const marginsDict = {};

        // base for constant cost-based margin
        const manufacturingCost = Object.values(cogsDict).reduce((sum, value) => sum + value, 0);

        // constant cost-based margin
        marginsDict.margin_base = manufacturingCost * this.marginsConfiguration.base_factor;

        // base for variable cost-based margin calculation
        const marginVarFactor = this.getMarginVarFactor(points, cogsDict);

        // variable cost-based margin compensating for higher operational cost of small items
        marginsDict.margin_var = manufacturingCost * marginVarFactor;

        const marginCostBased = Object.values(marginsDict).reduce((sum, value) => sum + value, 0);
        // base for value-based margin calculation
        // value-based margin is based on the total price, hence logistic cost is included here
        const marginVBBase = manufacturingCost + logisticCost + marginCostBased;
        const marginVBFactor = this.getMarginValueBasedFactor(points);

        // value-based margin based on shelf's features
        marginsDict.margin_vb = marginVBBase * marginVBFactor;

        marginsDict.cogs_increase_compensation = (marginVBBase + marginsDict.margin_vb) * this.marginsConfiguration.cogs_increase_compensation_factor;

        return marginsDict;
    }

    getRowAmount(points) {
        const distinctHorizontalsYVales = [];
        points.horizontals.forEach(({ y1 }) => {
            if (distinctHorizontalsYVales.indexOf(y1) === -1) distinctHorizontalsYVales.push(y1);
        });
        return distinctHorizontalsYVales.length - 1;
    }

    getJettyPricingDict(points) {
        let [pricingDict, totalWeight] = this.getJettyCogsDictAndTotalWeight(points);
        const shippingCostKg = 3.66; // TODO extract somewhere
        const polishVatFactor = 1.23;
        const logisticCost = totalWeight * shippingCostKg;
        const factorEuro = 4.3;

        // calculate margins
        const marginsDict = this.getJettyMarginsDict(points, pricingDict, logisticCost);

        // include margins in the pricing dict
        pricingDict = Object.assign({}, pricingDict, marginsDict);

        // having calculated the margins, include the logistic cost into the pricing dict
        pricingDict.logistic = logisticCost;

        const totalNetPln = Object.values(pricingDict).reduce((a, b) => a + b, 0);

        // when calculating final price use the same VAT factor
        // for all countries - net price not needed here,
        // should be calculated later from gross price divided by regional VAT
        const totalGrossPln = totalNetPln * polishVatFactor;
        const totalGrossEur = Math.round(totalGrossPln / factorEuro);

        pricingDict.totalRoundedGrossRegional = Math.round(totalGrossEur * (window.cstm.prices.priceeee || 1));
        pricingDict.weight = totalWeight;
        pricingDict.totalWeight = totalWeight;
        pricingDict.totalGrossEur = totalGrossEur;
        pricingDict.totalCapacity = getTotalCapacity(points, this.getRowAmount(points), points.pattern, points.width, this.shelfType);
        return pricingDict;
    }

    getJettyPrice(points) {
        const pricingDict = this.getJettyPricingDict(points);
        return {
            priceGrossRegional: pricingDict.totalRoundedGrossRegional,
            weight: pricingDict.weight,
            currencySymbol: window.cstm.prices.priceeel,
            priceGrossEur: pricingDict.totalGrossEur,
            capacity: pricingDict.totalCapacity,
        };
    }
}
