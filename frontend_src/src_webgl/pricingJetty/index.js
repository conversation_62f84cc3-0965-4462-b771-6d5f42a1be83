function independentHorizontalJoiner(horizontalsRaw) {
    const horizontals = horizontalsRaw.sort((a, b) => b.y1 !== a.y1 ? a.y1 - b.y1 : a.x1 - b.x1);
    const joinedHorizontals = [];
    let leftSide = null;
    let currentHeight = null;
    for (let i = 0; i < horizontals.length; i += 1) {
        // if new height
        if (currentHeight === null) {
            currentHeight = horizontals[i].y1;
        }
        // if there is no left side yet (so new level or new horizontal)
        if (leftSide === null) {
            leftSide = horizontals[i].x1;
        }
        // if it is last one
        if (
            (i === horizontals.length - 1)
            || (horizontals[i + 1].y1 !== horizontals[i].y1)
            || (horizontals[i].x2 !== horizontals[i + 1].x1)
        ) {
            joinedHorizontals.push({
                y1: currentHeight,
                y2: currentHeight,
                x1: leftSide,
                x2: horizontals[i].x2,
            });
            currentHeight = null;
            leftSide = null;
        }
    }
    return joinedHorizontals
}

function independentSupportCleaner(supports, backs) {
    const backsSorted = {};

    backs.forEach(b => {
        const bMin = Math.min(b.y1, b.y2);
        if (Object.keys(backsSorted).indexOf(bMin.toString()) === -1){
            backsSorted[bMin] = [];
        }
        backsSorted[bMin].push(b);
    });
    const newSupports = [];
    const supportCheck = (sX, b) => Math.min(b.x1, b.x2) <= sX && sX <= Math.max(b.x1, b.x2);
    for (let i = 0; i < supports.length; i += 1) {
        let skipThis = false;
        const s = supports[i];
        const sMin = Math.min(s.y1, s.y2);
        if (Object.keys(backsSorted).indexOf(sMin.toString()) > -1) {
            [s.x1, s.x2].forEach(x => {
                backsSorted[sMin].forEach(b => {
                    if (supportCheck(x, b)) skipThis = true;
                });
            })
        }
        if (!skipThis) {
            newSupports.push(s);
        }
    }
    return newSupports;
}

function getPriceConf(override_color = null) {
    let price_data = {
        // HVS
        'factor_hvs_area': 216.2 * 1.016,
        'factor_verticals_item': 13.85,
        'factor_supports_item': 10.36,
        'factor_horizontals_item': 32.0,
        'factor_horizontals_row': 60 * 0.65,
        // BACKS
        'factor_backs_item': 27,
        'factor_backs_area': 92 * 1.11,
        // DOORS
        'factor_doors_item': 93 * 1.1,
        // DRAWERS
        'factor_drawers_multiplier': 1.25,
        // MARGINS
        'factor_margin_multiplier': 1.53,
        'factor_hvs_mass': 13.5,
        'factor_doors_mass': 12.0,
        'factor_backs_mass': 9.75,
        // OTHER
        'factor_euro': 4.3,
        'factor_material_multiplier': 1.0,
    }
    if (window.ivy.depth === 400){
        price_data['factor_verticals_item'] *= 1.08;
    }
    if (window.ivy.shelf_type === 0 && (override_color || window.ivy.material) === 4) {
        // Oberzyna
        price_data['factor_drawers_multiplier'] -= 0.028;
        price_data['factor_hvs_area'] *= 1.067;
        price_data['factor_backs_area'] = 97;
        price_data['factor_material_multiplier'] = -0.10;
    } else if (window.ivy.shelf_type === 0 && (override_color || window.ivy.material) === 5) {
        // Fornir
        price_data['factor_drawers_multiplier'] += 0.09;  // Cena szuflady ~300
        price_data['factor_hvs_area'] *= 1.525;
        price_data['factor_backs_area'] = 120;
        price_data['factor_material_multiplier'] = -0.10;
    } else if (window.ivy.shelf_type === 1 && ((override_color || window.ivy.material) === 2)) {
        price_data['factor_material_multiplier'] = -0.15;
    } else if (window.ivy.shelf_type == 1 || window.ivy. shelf_type === 2) {
        //NOTE: temp change for veneers secret link
        price_data['factor_material_multiplier'] = -0.2;
    }

    return price_data
}

function calculatePrice(points, material_override, width_override, number_of_rows_override, shelf_type = 0) {
    let prices = getPriceConf();
    let width = width_override === undefined ? window.ivy.width : width_override;
    let rows = number_of_rows_override === undefined ? window.ivy.rows : number_of_rows_override;
    const depth = window.ivy.depth === 240 ? 320 : window.ivy.depth;
    let { horizontals, verticals, supports } = points;

    horizontals = independentHorizontalJoiner(horizontals);
    supports = independentSupportCleaner(points.supports, points.backs);

    let marza_x = function(waga_kg, x = 0.24285, y = 0.0286624, b = 0.8, e = -0.12, min_ = 0.03, max_ = 0.5) {

        let base = Number((x * (1.57 - Math.atan(y * waga_kg - b)) + e).toFixed(2));
        return (1 + Math.min(Math.max(base, min_), max_))
    };

    let get_hvs_area = function(horizontals, verticals, supports, depth) {
        let area = 0;

        for (let i = 0; i < verticals.length; i += 1) {
            area += Math.abs(verticals[i].y2 - verticals[i].y1) * depth;
        }
        for (let i = 0; i < horizontals.length; i += 1) {
            area += Math.abs(horizontals[i].x2 - horizontals[i].x1) * depth;
        }
        for (let i = 0; i < supports.length; i += 1) {
            area += Math.abs(supports[i].y2 - supports[i].y1) * Math.abs(supports[i].x2 - supports[i].x1);
        }
        return area / Math.pow(10, 6);
    };


    let totalPrice = 0;
    let hvs_area = get_hvs_area(horizontals, verticals, supports, depth);

    totalPrice += hvs_area * prices.factor_hvs_area;


    totalPrice += verticals.length * prices.factor_verticals_item;

    totalPrice += supports.length * prices.factor_supports_item;

    // temporary place for new pricing. backs + doors

    if (width > 2400) {
        totalPrice += (rows + 1) * prices.factor_horizontals_row; //#window.ivy.factor_row = 60
        totalPrice += horizontals.length * prices.factor_horizontals_item * 2;
    }
    else {
        totalPrice += horizontals.length * prices.factor_horizontals_item;
    }

    if (points.backs.length > 0){
        totalPrice += points.backs.length * prices.factor_backs_item;
        let wall_material_price = (points.backs.map(b=>Math.abs((b.x2 - b.x1) * (b.y2 - b.y1))).reduce((sum, value)=>sum +value, 0) / 1000 / 1000) * prices.factor_backs_area;
        totalPrice += wall_material_price;
    }

    totalPrice += points.doors.length * prices.factor_doors_item;

    // drawers

    points.drawers.map(d=>{
        let width = Math.abs(d.x2 - d.x1);
        let height = Math.abs(d.y2 - d.y1);
        let drawers_price = ((width > 800 ? 198 : 152) + Math.pow(width,2) / 40000.0 + 0.05*width + 22) * (height < 220 ? 1 : (height < 310 ? 1.08 : 1.13));
        drawers_price *= prices.factor_drawers_multiplier;
        totalPrice += drawers_price;
    });


    totalPrice *= prices.factor_margin_multiplier;

    // weight, plywood + doors + backs
    const doorsWeight = (points.doors.map(b => Math.abs((b.x2 - b.x1) * (b.y2 - b.y1)))
        .reduce((sum, value) => sum + value, 0) / 1000 / 1000) * prices.factor_doors_mass;
    const backsWeight = (points.backs.map(b => Math.abs((b.x2 - b.x1) * (b.y2 - b.y1)))
        .reduce((sum, value) => sum + value, 0) / 1000 / 1000) * prices.factor_backs_mass;
    const weight = (prices.factor_hvs_mass * hvs_area + doorsWeight + backsWeight).toFixed(2); //+((window.cstm.prices.pricew * area + doorsWeight + backsWeight).toFixed(2));
    // place for Jonasz function
    totalPrice *= marza_x(weight);

    if (prices.factor_material_multiplier === 1){
        //lets do nothing
    } else {
        totalPrice *= (1.0 + prices.factor_material_multiplier);
    }

    totalPrice /= prices.factor_euro;
    //NOTE: temp change for veneer secret link
    if (shelf_type === 2) {
        totalPrice *= 1.5
    }

    return [Math.round(Math.ceil(totalPrice * 1.23) * (window.cstm.prices.priceeee || 1)), weight, window.cstm.prices.priceeel || 'â‚¬']

}

export default calculatePrice
