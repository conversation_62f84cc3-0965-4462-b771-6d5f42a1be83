#!/bin/bash
install_node='true'
install_packages='true'
no_watch='false'

if [ "${BASH_SOURCE[0]}" ]
then
  SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
else
  SCRIPT_DIR="$(pwd)/$(dirname "$0")"
fi

(
  while test $# -gt 0; do
    case "$1" in
      -h|--help)
        echo "Old frontend - install & start"
        echo " "
        echo "./install_and_start.sh [options]"
        echo " "
        echo "options:"
        echo "-h, --help                     show brief help"
        echo "-sp, --skip-install-packages   run without npm install"
        echo "-sn, --skip-install-node       run without nvm install"
        echo "--no-watch                     run without watcher"
        exit 0
        ;;
      -sp|--skip-install-packages)
        shift
        install_packages='false'
        ;;
      -sn|--skip-install-node)
        shift
        install_node='false'
        ;;
      --no-watch)
        shift
        no_watch='true'
        ;;
      *)
        break
        ;;
    esac
  done

  cd "$SCRIPT_DIR" || exit 1

  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
  [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

  if ! command -v nvm &> /dev/null
  then
      echo "You must install nvm"
      exit 1
  fi

  if [ "$install_node" = "true" ]; then
    nvm install 16.20.0
  fi
  nvm use 16.20.0

  if [ "$install_packages" = "true" ]; then

    if ! command -v pyenv &> /dev/null
    then
        echo "You must install pyenv"
        exit 1
    fi
    pyenv install --skip-existing 3.10
    pyenv local 3.10
    npm i --legacy-peer-deps
  fi

  if [ "$no_watch" = "true" ]; then
    npm run dev-no-watch
  else
    npm run dev
  fi

)
