const path = require('path');

const IS_PRODUCTION = (process.env.NODE_ENV === 'production');
const MODE = IS_PRODUCTION ? 'production' : 'development';

module.exports = {
  mode: MODE,
  context: __dirname,
  entry: {
    dna_backend: './src_node/dna_backend/node_decoder',
  },
  output: {
    path: path.resolve('../src/'),
    filename: '[name].js',
  },
  target: 'node',
  externals: ['express'],
  module: {
    rules: [
      {
        test: /\.js$/,
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-env'],
          plugins: [
            '@babel/plugin-transform-destructuring',
            '@babel/plugin-proposal-object-rest-spread',
          ],
        },
        exclude: /node_modules/,
      },
    ],
  },
  devServer: {
    historyApiFallback: true,
    noInfo: true,
    overlay: true,
  },
  performance: {
    hints: false,
  },
};
