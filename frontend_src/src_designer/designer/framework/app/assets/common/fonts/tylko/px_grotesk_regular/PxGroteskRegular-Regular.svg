<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="px_grotesk_regularregular" horiz-adv-x="600" >
<font-face units-per-em="1000" ascent="805" descent="-195" />
<missing-glyph horiz-adv-x="254" />
<glyph unicode="fi" horiz-adv-x="608" d="M525 590h-90v90h90v-90zM525 0h-90v433h-226v-433h-89v433h-86v77h86v170h206v-77h-117v-93h316v-510z" />
<glyph unicode="fl" horiz-adv-x="610" d="M209 603v-93h107v-77h-107v-433h-89v433h-86v77h86v170h405v-680h-90v603h-226z" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="254" />
<glyph unicode="&#x09;" horiz-adv-x="254" />
<glyph unicode="&#xa0;" horiz-adv-x="254" />
<glyph unicode="h" horiz-adv-x="570" d="M173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v680h90v-254z" />
<glyph unicode="p" horiz-adv-x="595" d="M83 -170v680h90v-83q24 46 64.5 70.5t98.5 24.5q51 0 89 -21t63 -57t37.5 -85t12.5 -104q0 -56 -12.5 -104.5t-37.5 -84.5t-63 -57t-89 -21q-58 0 -98.5 24.5t-64.5 70.5v-253h-90zM173 212q0 -66 36 -109t104 -43q57 0 93.5 32t36.5 84v158q0 52 -36.5 84t-93.5 32 q-68 0 -104 -43t-36 -109v-86z" />
<glyph unicode="q" horiz-adv-x="595" d="M422 -170v253q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21t-63.5 57t-38 84.5t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v83h90v-680h-90zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5t-41 -23.5t-27.5 -36.5t-10 -47.5v-158 q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="j" horiz-adv-x="268" d="M187 586h-94v94h94v-94zM95 510h90v-680h-208v77h118v603z" />
<glyph unicode="t" horiz-adv-x="368" d="M209 77h117v-77h-206v433h-86v77h86v170h89v-170h117v-77h-117v-356z" />
<glyph unicode="a" horiz-adv-x="551" d="M238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31 t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="o" horiz-adv-x="570" d="M514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5t-42.5 23.5t-51.5 8.5t-51.5 -8.5t-42.5 -23.5t-29 -36.5 t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="u" horiz-adv-x="566" d="M393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="e" horiz-adv-x="548" d="M56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5t-66.5 -34.5t-78 -12q-55 0 -96.5 20t-69 55t-41 84 t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="c" horiz-adv-x="534" d="M57 251q0 61 14.5 111.5t42.5 85.5t69 54.5t93 19.5q40 0 75.5 -10.5t62.5 -32.5t44 -56t21 -80h-90q-3 31 -13 51.5t-26 33t-36.5 17.5t-42.5 5q-23 0 -44 -7.5t-38 -22.5t-27 -37.5t-10 -52.5v-150q0 -27 9.5 -49.5t26 -38t38 -24t46.5 -8.5q50 0 83 27t38 84h90 q-5 -47 -24 -81.5t-47 -57t-63.5 -33.5t-74.5 -11q-56 0 -96.5 19.5t-67.5 55t-40 83.5t-13 105z" />
<glyph unicode="k" horiz-adv-x="522" d="M173 0h-90v680h90v-680zM174 285l200 225h109l-205 -225l219 -285h-103z" />
<glyph unicode="m" horiz-adv-x="848" d="M461 417q16 45 54.5 75t95.5 30q33 0 62 -10t50.5 -32t33.5 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-71.5 32q-24 0 -44.5 -11t-36.5 -31.5t-25 -48.5t-9 -61v-298h-90v330q0 56 -21.5 88t-71.5 32q-24 0 -44.5 -11t-36.5 -31.5t-25 -48.5t-9 -61v-298h-90v510h90 v-84q17 42 52.5 69t89.5 27q51 0 90 -25.5t56 -79.5z" />
<glyph unicode="r" horiz-adv-x="357" d="M83 510h245v-78h-155v-432h-90v510z" />
<glyph unicode="v" horiz-adv-x="508" d="M257 107l130 403h93l-183 -510h-86l-183 510h97l128 -403h4z" />
<glyph unicode="w" horiz-adv-x="749" d="M372 413l-97 -413h-93l-149 510h91l101 -399h4l90 399h109l94 -399h4l102 399h88l-149 -510h-95l-96 413h-4z" />
<glyph unicode="z" horiz-adv-x="486" d="M432 77v-77h-384v77l274 356h-254v77h358v-77l-274 -356h280z" />
<glyph unicode="y" horiz-adv-x="521" d="M264 111l137 399h93l-250 -680h-174v77h117l32 93l-189 510h97l133 -399h4z" />
<glyph unicode="x" horiz-adv-x="508" d="M201 264l-160 246h100l112 -178l114 178h98l-160 -240l177 -270h-101l-128 202l-127 -202h-100z" />
<glyph unicode="." horiz-adv-x="262" d="M186 0h-110v110h110v-110z" />
<glyph unicode="," horiz-adv-x="262" d="M186 0l-62 -110h-48l59 110h-59v110h110v-110z" />
<glyph unicode="A" horiz-adv-x="631" d="M206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="E" horiz-adv-x="602" d="M92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="F" horiz-adv-x="555" d="M92 680h417v-85h-322v-206h298v-85h-298v-304h-95v680z" />
<glyph unicode="I" horiz-adv-x="279" d="M92 680h95v-680h-95v680z" />
<glyph unicode="K" horiz-adv-x="629" d="M189 363l282 317h116l-287 -317l312 -363h-116zM187 0h-95v680h95v-680z" />
<glyph unicode="M" horiz-adv-x="833" d="M420 155l173 525h148v-680h-89v579h-4l-193 -579h-76l-194 579h-4v-579h-89v680h150l174 -525h4z" />
<glyph unicode="N" horiz-adv-x="695" d="M511 115v565h92v-680h-134l-281 568h-4v-568h-92v680h134l281 -565h4z" />
<glyph unicode="O" horiz-adv-x="706" d="M353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340q0 -80 -17 -145.5t-52.5 -112t-90.5 -72t-132 -25.5 t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="R" horiz-adv-x="623" d="M383 368q38 0 58 23t20 55v75q0 35 -21 54.5t-57 19.5h-196v-227h196zM187 287v-287h-95v680h256q53 0 94 -12t68 -37t39 -61.5t12 -84.5q0 -45 -11.5 -81.5t-37.5 -62.5q-20 -20 -46.5 -33t-66.5 -18l168 -290h-109l-159 287h-112z" />
<glyph unicode="S" horiz-adv-x="616" d="M303 612q-65 0 -102.5 -26t-37.5 -71q0 -48 26 -71t83 -39l109 -30q44 -12 78.5 -28t58 -39.5t35.5 -54.5t12 -73q0 -45 -20.5 -81t-55 -61.5t-80.5 -39t-97 -13.5t-97 12.5t-82 38.5t-59 66t-27 96h95q8 -69 54 -99.5t114 -30.5q34 0 63 7t50.5 21t34 34.5t12.5 47.5 q0 44 -24.5 69t-78.5 40l-133 37q-84 23 -124 69.5t-40 121.5q0 38 16.5 71t47.5 57t73.5 38t95.5 14q49 0 91.5 -11.5t75 -36t53 -62.5t25.5 -90h-95q-3 34 -16.5 56.5t-34 36t-46 19t-53.5 5.5z" />
<glyph unicode="T" horiz-adv-x="541" d="M20 680h501v-85h-203v-595h-95v595h-203v85z" />
<glyph unicode="L" horiz-adv-x="513" d="M92 680h95v-595h308v-85h-403v680z" />
<glyph unicode="P" horiz-adv-x="603" d="M386 344q34 0 55 22t21 56v99q0 34 -20 54t-56 20h-199v-251h199zM187 259v-259h-95v680h257q61 0 99.5 -11.5t65.5 -39.5q26 -27 37 -67.5t11 -88.5q0 -47 -13.5 -88.5t-42.5 -70.5q-56 -55 -156 -55h-163z" />
<glyph unicode="H" horiz-adv-x="695" d="M508 307h-321v-307h-95v680h95v-288h321v288h95v-680h-95v307z" />
<glyph unicode="B" horiz-adv-x="648" d="M485 241q0 34 -21 51t-57 17h-220v-224h214q38 0 61 18t23 56v82zM467 354q54 -15 86 -54.5t32 -101.5q0 -99 -52 -147q-29 -26 -71.5 -38.5t-99.5 -12.5h-270v680h265q51 0 88 -7t66 -28q59 -42 59 -130q0 -58 -27.5 -100.5t-75.5 -60.5zM187 595v-201h209q38 0 56 20 t18 52v61q0 68 -74 68h-209z" />
<glyph unicode="Q" horiz-adv-x="712" d="M353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM529 35q-66 -50 -176 -50q-77 0 -132 25t-90.5 71.5 t-52.5 112t-17 146.5t17 146.5t52.5 112t90.5 71.5t132 25t132 -25t90.5 -71.5t52.5 -112t17 -146.5q0 -72 -13.5 -132.5t-43.5 -107.5l103 -100l-62 -62z" />
<glyph unicode="D" horiz-adv-x="667" d="M187 595v-510h125q96 0 141.5 40t45.5 115v200q0 75 -45.5 115t-141.5 40h-125zM312 680q154 0 223 -84.5t69 -255.5t-69 -255.5t-223 -84.5h-220v680h220z" />
<glyph unicode="J" horiz-adv-x="425" d="M333 0h-314v85h219v595h95v-680z" />
<glyph unicode="V" horiz-adv-x="616" d="M310 112l169 568h102l-220 -680h-106l-220 680h102l169 -568h4z" />
<glyph unicode="W" horiz-adv-x="932" d="M652 107h4l133 573h101l-176 -680h-119l-127 547h-4l-127 -547h-119l-176 680h101l133 -573h4l133 573h106z" />
<glyph unicode="Z" horiz-adv-x="593" d="M537 595l-379 -510h378v-85h-487v85l380 510h-364v85h472v-85z" />
<glyph unicode="Y" horiz-adv-x="589" d="M21 680h104l170 -316l173 316h101l-227 -410v-270h-95v271z" />
<glyph unicode="C" horiz-adv-x="642" d="M340 -15q-68 0 -120 24.5t-87 70.5t-53.5 110t-18.5 144q0 83 17 150t52 114t88 72t124 25q49 0 91.5 -14.5t74.5 -41.5t51.5 -65.5t22.5 -86.5h-95q-7 60 -47 92.5t-107 32.5q-36 0 -66.5 -12.5t-53 -34.5t-35 -52.5t-12.5 -67.5v-210q0 -36 13.5 -66.5t37 -53t55 -35 t67.5 -12.5q68 0 110 30.5t49 99.5h95q-3 -48 -23.5 -87.5t-54 -67t-78.5 -43t-97 -15.5z" />
<glyph unicode="U" horiz-adv-x="684" d="M504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="X" horiz-adv-x="586" d="M243 349l-205 331h104l151 -251l154 251h104l-207 -331l215 -349h-106l-160 266l-162 -266h-105z" />
<glyph unicode="G" horiz-adv-x="672" d="M61 334q0 85 17.5 152t52.5 113.5t89 71t127 24.5q51 0 94.5 -14.5t75.5 -41.5t52 -65.5t23 -86.5h-95q-7 60 -47.5 92.5t-111.5 32.5q-38 0 -69.5 -12.5t-54 -34.5t-35.5 -52.5t-13 -67.5v-210q0 -36 13.5 -66.5t37 -53t56 -35t70.5 -12.5q79 0 122 42t43 122v46h-154 v85h249v-363h-91l-4 72q-26 -39 -67 -63t-106 -24t-116 24.5t-86 70t-53.5 110t-18.5 144.5z" />
<glyph unicode="1" d="M106 79h175v443h-175v79h175v79h90v-601h160v-79h-425v79z" />
<glyph unicode="2" d="M190 79h339v-79h-457v79l272 267q20 20 35.5 37.5t26 34.5t16 35.5t5.5 41.5q0 57 -34.5 88.5t-89.5 31.5q-60 0 -98 -33.5t-38 -101.5h-92q0 51 17.5 90.5t48.5 67.5t73 42.5t91 14.5t88.5 -14t67.5 -40.5t43 -63.5t15 -82q0 -33 -9 -62t-23.5 -53.5t-33.5 -46.5 t-40 -41l-223 -211v-2z" />
<glyph unicode="4" d="M144 243h210v308zM354 0v164h-300v79l299 437h91v-437h98v-79h-98v-164h-90z" />
<glyph unicode="3" d="M206 309v79h142q36 0 55.5 20.5t19.5 52.5v62q0 22 -10 39.5t-27 29t-39.5 17.5t-47.5 6q-61 0 -94.5 -31.5t-37.5 -82.5h-92q0 37 15.5 72t44 62t70 43.5t94.5 16.5q51 0 92 -13.5t70 -38t44.5 -58.5t15.5 -74q0 -62 -24.5 -100.5t-61.5 -58.5q47 -21 73 -59.5 t26 -102.5q0 -49 -18 -87t-49.5 -64.5t-75.5 -40t-96 -13.5q-50 0 -93 13t-75 39.5t-50.5 66.5t-18.5 94h92q5 -71 45 -102t100 -31q31 0 57 8.5t44.5 22.5t29 32.5t10.5 38.5v68q0 32 -19.5 53t-55.5 21h-155z" />
<glyph unicode="5" d="M77 298l26 382h393v-79h-310l-16 -211q23 32 62 49.5t83 17.5q51 0 92 -16.5t70 -47t44.5 -73.5t15.5 -95q0 -54 -16.5 -98.5t-47.5 -76t-75 -48.5t-98 -17q-106 0 -166 53t-68 150h92q7 -63 43 -93t96 -30q61 0 100.5 30.5t39.5 85.5v89q0 54 -38 81.5t-100 27.5 q-47 0 -78.5 -21.5t-51.5 -59.5h-92z" />
<glyph unicode="7" d="M251 0h-102l286 601h-356v79h453v-78z" />
<glyph unicode="8" d="M155 162q0 -45 39.5 -71t105.5 -26t105.5 26t39.5 71v52q0 45 -39.5 71.5t-105.5 26.5t-105.5 -26.5t-39.5 -71.5v-52zM427 524q0 22 -10 39.5t-27.5 28.5t-40.5 17t-49 6t-49.5 -6t-40.5 -17t-27 -28.5t-10 -39.5v-43q0 -23 10 -40t27 -28t40.5 -17t49.5 -6t49 6 t40.5 17t27.5 28t10 40v43zM300 -15q-51 0 -95.5 12t-77.5 36t-52.5 61t-19.5 88q0 63 33 107t91 65q-104 45 -104 157q0 43 17.5 77t48 58t71.5 36.5t88 12.5t88 -12.5t71.5 -36.5t48 -58t17.5 -77q0 -112 -104 -157q58 -21 91 -65t33 -107q0 -51 -19.5 -88t-52.5 -61 t-77.5 -36t-95.5 -12z" />
<glyph unicode="0" d="M300 65q62 0 101.5 37t39.5 105v266q0 68 -39.5 105t-101.5 37t-101.5 -37t-39.5 -105v-266q0 -68 39.5 -105t101.5 -37zM541 340q0 -77 -11 -142t-38 -112t-73.5 -74t-118.5 -27t-118.5 27t-73.5 74t-38 112t-11 142q0 76 11 141.5t38 112.5t73.5 74t118.5 27t118.5 -27 t73.5 -74t38 -112.5t11 -141.5z" />
<glyph unicode="&#x26;" horiz-adv-x="617" d="M265 429q39 30 63.5 58.5t24.5 66.5q0 30 -20 48t-57 18q-35 0 -57.5 -20t-22.5 -50q0 -29 20 -58t49 -63zM153 144q0 -21 9 -36t24 -25t34 -15t38 -5q44 0 77.5 11.5t62.5 42.5l-150 194l-58 -45q-16 -12 -26.5 -26t-10.5 -37v-59zM421 225v138h87v-248l90 -115h-107 l-43 55q-38 -33 -82 -50t-107 -17q-94 0 -147.5 46.5t-53.5 133.5q0 59 24.5 99t66.5 72l48 37q-43 52 -67 89.5t-24 78.5q0 32 13 59.5t36 48t54.5 32t68.5 11.5q78 0 121.5 -40t43.5 -107q0 -51 -35 -96.5t-93 -88.5l104 -138h2z" />
<glyph unicode="6" d="M304 65q32 0 58.5 10t45 27.5t29 41t10.5 50.5v56q0 54 -38.5 86.5t-103.5 32.5t-105 -32.5t-40 -86.5v-56q0 -27 10.5 -50.5t29.5 -41t45.5 -27.5t58.5 -10zM304 -15q-68 0 -114.5 21.5t-75 64.5t-41.5 106t-13 146q0 84 13.5 153t43.5 117.5t76.5 75t112.5 26.5 q107 0 160 -48t62 -136h-92q-6 50 -38 77t-92 27q-68 0 -107 -47t-39 -124v-67q28 35 69.5 52.5t89.5 17.5q54 0 96 -16t71.5 -45t45 -70t15.5 -91q0 -43 -15 -86t-45.5 -77t-76 -55.5t-106.5 -21.5z" />
<glyph unicode="9" d="M295 615q-32 0 -58.5 -10t-45.5 -27.5t-29 -41t-10 -50.5v-56q0 -54 38.5 -86.5t103.5 -32.5t105 32.5t40 86.5v56q0 27 -10.5 50.5t-29.5 41t-45.5 27.5t-58.5 10zM295 695q67 0 114 -21.5t75.5 -64.5t41.5 -106t13 -146q0 -85 -13.5 -154t-43.5 -117.5t-76.5 -74.5 t-112.5 -26q-107 0 -160 48t-62 136h92q6 -50 38 -77t92 -27q68 0 107 47t39 124v67q-28 -35 -69.5 -52.5t-89.5 -17.5q-54 0 -96 16t-71.5 45t-45 70t-15.5 91q0 43 15 86t45.5 77t76 55.5t106.5 21.5z" />
<glyph unicode="-" horiz-adv-x="432" d="M76 312h280v-82h-280v82z" />
<glyph unicode="&#xe7;" horiz-adv-x="534" d="M170 -105h70v70h70v-135h-140v65zM57 251q0 61 14.5 111.5t42.5 85.5t69 54.5t93 19.5q40 0 75.5 -10.5t62.5 -32.5t44 -56t21 -80h-90q-3 31 -13 51.5t-26 33t-36.5 17.5t-42.5 5q-23 0 -44 -7.5t-38 -22.5t-27 -37.5t-10 -52.5v-150q0 -27 9.5 -49.5t26 -38t38 -24 t46.5 -8.5q50 0 83 27t38 84h90q-5 -47 -24 -81.5t-47 -57t-63.5 -33.5t-74.5 -11q-56 0 -96.5 19.5t-67.5 55t-40 83.5t-13 105z" />
<glyph unicode="&#x2026;" horiz-adv-x="922" d="M846 0h-110v110h110v-110zM516 0h-110v110h110v-110zM186 0h-110v110h110v-110z" />
<glyph unicode=":" horiz-adv-x="262" d="M186 400h-110v110h110v-110zM186 0h-110v110h110v-110z" />
<glyph unicode="!" horiz-adv-x="278" d="M194 0h-110v110h110v-110zM114 178l-20 149v353h90v-353l-20 -149h-50z" />
<glyph unicode="/" horiz-adv-x="456" d="M118 0h-92l312 680h92z" />
<glyph unicode="[" horiz-adv-x="347" d="M300 716v-74h-125v-738h125v-74h-210v886h210z" />
<glyph unicode="]" horiz-adv-x="347" d="M257 716v-886h-210v74h125v738h-125v74h210z" />
<glyph unicode="\" horiz-adv-x="456" d="M26 680h92l312 -680h-92z" />
<glyph unicode="(" horiz-adv-x="315" d="M147 168q0 -83 37.5 -168t101.5 -170h-80q-34 44 -61.5 91t-47 100.5t-30 115.5t-10.5 136t10.5 136t30 115.5t47 100.5t61.5 91h80q-68 -92 -103.5 -173.5t-35.5 -164.5v-210z" />
<glyph unicode="+" d="M45 219v72h217v219h76v-219h217v-72h-217v-219h-76v219h-217z" />
<glyph unicode="?" horiz-adv-x="503" d="M293 0h-110v110h110v-110zM370 511q0 51 -32.5 79t-82.5 28q-54 0 -88 -30t-39 -87h-90q3 45 21.5 81t48 61t67.5 38.5t81 13.5q44 0 81.5 -13t64.5 -37t42.5 -57.5t15.5 -74.5q0 -33 -6.5 -58.5t-18 -46t-27 -37.5t-32.5 -32q-22 -20 -39 -35t-29 -29t-18.5 -30 t-6.5 -37v-30h-90v30q0 29 6 51t18 41t30.5 36.5t42.5 37.5q42 34 61 64.5t19 72.5z" />
<glyph unicode="%" horiz-adv-x="807" d="M623 48q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM761 175q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58t42.5 38.5t65.5 14t65.5 -14 t42.5 -38.5t23 -58t7 -72.5zM185 378q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM323 505q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58 t42.5 38.5t65.5 14t65.5 -14t42.5 -38.5t23 -58t7 -72.5zM579 680h76l-426 -680h-76z" />
<glyph unicode="&#x2013;" horiz-adv-x="622" d="M76 312h470v-82h-470v82z" />
<glyph unicode="&#x2014;" horiz-adv-x="1132" d="M96 312h940v-82h-940v82z" />
<glyph unicode="&#xb4;" horiz-adv-x="300" d="M188 695h105l-106 -122h-78z" />
<glyph unicode="&#xa8;" horiz-adv-x="332" d="M282 592h-88v88h88v-88zM106 592h-88v88h88v-88z" />
<glyph unicode="`" horiz-adv-x="300" d="M191 573h-78l-106 122h105z" />
<glyph unicode="&#x2c6;" horiz-adv-x="300" d="M92 573h-77l83 122h103l83 -122h-78l-57 73z" />
<glyph unicode="&#xdd;" horiz-adv-x="589" d="M331 849h105l-106 -122h-78zM21 680h104l170 -316l173 316h101l-227 -410v-270h-95v271z" />
<glyph unicode="&#xfd;" horiz-adv-x="521" d="M289 695h105l-106 -122h-78zM264 111l137 399h93l-250 -680h-174v77h117l32 93l-189 510h97l133 -399h4z" />
<glyph unicode="&#xc4;" horiz-adv-x="631" d="M443 766h-88v88h88v-88zM267 766h-88v88h88v-88zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#xc9;" horiz-adv-x="602" d="M323 869h105l-106 -122h-78zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#xdc;" horiz-adv-x="684" d="M474 766h-88v88h88v-88zM298 766h-88v88h88v-88zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#xe1;" horiz-adv-x="551" d="M296 695h105l-106 -122h-78zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41 q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#xe0;" horiz-adv-x="551" d="M298 573h-78l-106 122h105zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41 q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#xe2;" horiz-adv-x="551" d="M204 573h-77l83 122h103l83 -122h-78l-57 73zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103 t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#xe4;" horiz-adv-x="551" d="M392 592h-88v88h88v-88zM216 592h-88v88h88v-88zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58 q0 60 38 103t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#xe9;" horiz-adv-x="548" d="M311 695h105l-106 -122h-78zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5t-66.5 -34.5t-78 -12 q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#xe8;" horiz-adv-x="548" d="M323 573h-78l-106 122h105zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5t-66.5 -34.5t-78 -12 q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#xea;" horiz-adv-x="548" d="M218 573h-77l83 122h103l83 -122h-78l-57 73zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5 t-66.5 -34.5t-78 -12q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#xeb;" horiz-adv-x="548" d="M408 592h-88v88h88v-88zM232 592h-88v88h88v-88zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5 t-66.5 -34.5t-78 -12q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#xed;" horiz-adv-x="256" d="M83 0v510h90v-510h-90zM145 695h105l-106 -122h-78z" />
<glyph unicode="&#xec;" horiz-adv-x="256" d="M83 0v510h90v-510h-90zM190 573h-78l-106 122h105z" />
<glyph unicode="&#xee;" horiz-adv-x="256" d="M83 0v510h90v-510h-90zM71 573h-77l83 122h103l83 -122h-78l-57 73z" />
<glyph unicode="&#xef;" horiz-adv-x="256" d="M83 0v510h90v-510h-90zM260 592h-88v88h88v-88zM84 592h-88v88h88v-88z" />
<glyph unicode="&#xf3;" horiz-adv-x="570" d="M319 695h105l-106 -122h-78zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5t-42.5 23.5t-51.5 8.5 t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#xf2;" horiz-adv-x="570" d="M321 573h-78l-106 122h105zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5t-42.5 23.5t-51.5 8.5 t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#xf4;" horiz-adv-x="570" d="M229 573h-77l83 122h103l83 -122h-78l-57 73zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5t-42.5 23.5 t-51.5 8.5t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#xf6;" horiz-adv-x="570" d="M418 592h-88v88h88v-88zM242 592h-88v88h88v-88zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5 t-42.5 23.5t-51.5 8.5t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#xfa;" horiz-adv-x="566" d="M299 695h105l-106 -122h-78zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#xf9;" horiz-adv-x="566" d="M351 573h-78l-106 122h105zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#xfb;" horiz-adv-x="566" d="M227 573h-77l83 122h103l83 -122h-78l-57 73zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#xfc;" horiz-adv-x="566" d="M415 592h-88v88h88v-88zM239 592h-88v88h88v-88zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#xc0;" horiz-adv-x="631" d="M398 747h-78l-106 122h105zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#xff;" horiz-adv-x="521" d="M396 592h-88v88h88v-88zM220 592h-88v88h88v-88zM264 111l137 399h93l-250 -680h-174v77h117l32 93l-189 510h97l133 -399h4z" />
<glyph unicode="&#x178;" horiz-adv-x="589" d="M428 766h-88v88h88v-88zM252 766h-88v88h88v-88zM21 680h104l170 -316l173 316h101l-227 -410v-270h-95v271z" />
<glyph unicode="&#xc2;" horiz-adv-x="631" d="M259 747h-77l83 122h103l83 -122h-78l-57 73zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#xca;" horiz-adv-x="602" d="M264 747h-77l83 122h103l83 -122h-78l-57 73zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#xc1;" horiz-adv-x="631" d="M306 869h105l-106 -122h-78zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#xcb;" horiz-adv-x="602" d="M454 766h-88v88h88v-88zM278 766h-88v88h88v-88zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#xc8;" horiz-adv-x="602" d="M379 747h-78l-106 122h105zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#xcd;" horiz-adv-x="279" d="M157 869h105l-106 -122h-78zM92 680h95v-680h-95v680z" />
<glyph unicode="&#xce;" horiz-adv-x="279" d="M82 747h-77l83 122h103l83 -122h-78l-57 73zM92 680h95v-680h-95v680z" />
<glyph unicode="&#xcf;" horiz-adv-x="279" d="M271 766h-88v88h88v-88zM95 766h-88v88h88v-88zM92 680h95v-680h-95v680z" />
<glyph unicode="&#xcc;" horiz-adv-x="279" d="M92 680h95v-680h-95v680zM201 747h-78l-106 122h105z" />
<glyph unicode="&#xda;" horiz-adv-x="684" d="M357 869h105l-106 -122h-78zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#xdb;" horiz-adv-x="684" d="M285 747h-77l83 122h103l83 -122h-78l-57 73zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#xd9;" horiz-adv-x="684" d="M409 747h-78l-106 122h105zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#xc7;" horiz-adv-x="642" d="M340 -15q-68 0 -120 24.5t-87 70.5t-53.5 110t-18.5 144q0 83 17 150t52 114t88 72t124 25q49 0 91.5 -14.5t74.5 -41.5t51.5 -65.5t22.5 -86.5h-95q-7 60 -47 92.5t-107 32.5q-36 0 -66.5 -12.5t-53 -34.5t-35 -52.5t-12.5 -67.5v-210q0 -36 13.5 -66.5t37 -53t55 -35 t67.5 -12.5q68 0 110 30.5t49 99.5h95q-3 -48 -23.5 -87.5t-54 -67t-78.5 -43t-97 -15.5zM235 -105h70v70h70v-135h-140v65z" />
<glyph unicode="&#x221e;" horiz-adv-x="746" d="M136 258q0 -35 20 -56t56 -21q19 0 34 5.5t28 15.5t26 24t27 32q-26 35 -51.5 57.5t-61.5 22.5q-38 0 -58 -22.5t-20 -57.5zM373 208q-16 -20 -33 -37t-36.5 -30t-43 -20.5t-51.5 -7.5q-33 0 -59.5 11.5t-45.5 31.5t-29.5 46.5t-10.5 57.5t11 58t30.5 47t46 31t57.5 11 q29 0 53 -8.5t43.5 -22.5t36 -32.5t31.5 -38.5q15 20 31.5 38.5t36 32.5t43.5 22.5t53 8.5q31 0 57.5 -11t46 -31t30.5 -47t11 -58t-10.5 -57.5t-29.5 -46.5t-45.5 -31.5t-59.5 -11.5q-56 0 -94.5 28t-69.5 67zM610 258q0 35 -20 57.5t-58 22.5q-36 0 -61.5 -22.5 t-51.5 -57.5q14 -18 27 -32t26 -24t28 -15.5t34 -5.5q36 0 56 21t20 56z" />
<glyph unicode="&#xa9;" horiz-adv-x="826" d="M123 340q0 -61 22.5 -114t61.5 -92t92 -61.5t114 -22.5t114 22.5t92 61.5t61.5 92t22.5 114t-22.5 114t-61.5 92t-92 61.5t-114 22.5t-114 -22.5t-92 -61.5t-61.5 -92t-22.5 -114zM58 340q0 73 28 138t76 113t112.5 76t138.5 28q73 0 138 -28t113 -76t76 -113t28 -138 q0 -74 -28 -138.5t-76 -112.5t-113 -76t-138 -28q-74 0 -138.5 28t-112.5 76t-76 112.5t-28 138.5zM263 337q0 84 40.5 134.5t112.5 50.5q55 0 94 -30.5t44 -91.5h-66q-4 39 -25.5 53.5t-50.5 14.5q-15 0 -29 -5t-25 -15t-18 -24t-7 -33v-102q0 -35 24 -56t56 -21 q31 0 53 17.5t25 53.5h66q-4 -32 -17.5 -55.5t-33 -39t-44 -23t-49.5 -7.5q-37 0 -65 13.5t-47 37.5t-28.5 56.5t-9.5 71.5z" />
<glyph unicode="#" horiz-adv-x="615" d="M235 240h115l27 199h-115zM537 168h-124l-24 -168h-75l24 168h-115l-24 -168h-75l24 168h-93v72h104l29 199h-111v71h121l25 170h75l-25 -170h115l25 170h75l-25 -170h98v-71h-108l-29 -199h113v-72z" />
<glyph unicode="&#x201a;" horiz-adv-x="262" d="M186 0l-62 -110h-48l59 110h-59v110h110v-110z" />
<glyph unicode="&#x201e;" horiz-adv-x="452" d="M376 0l-62 -110h-48l59 110h-59v110h110v-110zM186 0l-62 -110h-48l59 110h-59v110h110v-110z" />
<glyph unicode="&#x201d;" horiz-adv-x="440" d="M377 570l-62 -110h-48l59 110h-59v110h110v-110zM187 570l-62 -110h-48l59 110h-59v110h110v-110z" />
<glyph unicode="&#x201c;" horiz-adv-x="447" d="M72 570l62 110h48l-59 -110h59v-110h-110v110zM262 570l62 110h48l-59 -110h59v-110h-110v110z" />
<glyph unicode="&#xd7;" d="M99 -1l-55 55l201 200l-201 201l55 55l200 -201l201 201l55 -55l-201 -201l201 -200l-54 -54l-201 201z" />
<glyph unicode="&#x2019;" horiz-adv-x="250" d="M187 570l-62 -110h-48l59 110h-59v110h110v-110z" />
<glyph unicode="&#x2018;" horiz-adv-x="257" d="M72 570l62 110h48l-59 -110h59v-110h-110v110z" />
<glyph unicode="&#xab;" horiz-adv-x="497" d="M165 71l-107 186l107 186h80l-97 -186l97 -186h-80zM352 71l-107 186l107 186h80l-97 -186l97 -186h-80z" />
<glyph unicode="_" horiz-adv-x="620" d="M75 -55h470v-60h-470v60z" />
<glyph unicode="|" horiz-adv-x="263" d="M89 850h85v-1020h-85v1020z" />
<glyph unicode="=" d="M45 116v72h510v-72h-510zM45 322v72h510v-72h-510z" />
<glyph unicode="@" horiz-adv-x="839" d="M284 77v356h270v-279h90v356h-450v-510h360v-77h-450v664h630v-510h-450zM374 154h90v202h-90v-202z" />
<glyph unicode="s" horiz-adv-x="509" d="M309 289q76 -16 113 -52t37 -98q0 -32 -14.5 -59.5t-41.5 -47.5t-64 -32t-82 -12q-43 0 -80.5 11t-65.5 32.5t-44 54t-17 75.5h90q2 -51 36.5 -76t81.5 -25q45 0 78 20t33 58q0 31 -20 49.5t-71 28.5l-84 17q-63 13 -97 48.5t-34 95.5q0 30 13.5 56.5t38.5 46t59 31 t76 11.5q36 0 70.5 -9.5t61.5 -29.5t43.5 -51t17.5 -74h-90q-2 51 -31.5 71.5t-70.5 20.5q-42 0 -71 -20t-29 -52q0 -30 17 -46.5t55 -24.5z" />
<glyph unicode="g" horiz-adv-x="595" d="M512 10q0 -53 -19 -91.5t-50 -64t-70.5 -37.5t-80.5 -12q-40 0 -76.5 8t-66 26.5t-49 49t-25.5 76.5h90q5 -44 37.5 -66t85.5 -22q26 0 50.5 7.5t43 24t29.5 42.5t11 63v69q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21t-63.5 57t-38 84.5t-12.5 104.5q0 55 12.5 104 t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v83h90v-500zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5t-41 -23.5t-27.5 -36.5t-10 -47.5v-158q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="&#xf7;" d="M351 407h-102v103h102v-103zM351 0h-102v103h102v-103zM555 219h-510v72h510v-72z" />
<glyph unicode="&#x2212;" d="M555 219h-510v72h510v-72z" />
<glyph unicode="&#xa6;" horiz-adv-x="267" d="M91 850h85v-417h-85v417zM91 247h85v-417h-85v417z" />
<glyph unicode="&#xac;" d="M477 322h-436v72h510v-274h-74v202z" />
<glyph unicode="&#xb1;" d="M555 0h-510v72h510v-72zM45 279v72h217v159h76v-159h217v-72h-217v-159h-76v159h-217z" />
<glyph unicode="&#x3c;" d="M546 432l-412 -177l412 -177v-78l-510 219v72l510 219v-78z" />
<glyph unicode="&#x3e;" d="M54 510l510 -219v-72l-510 -219v78l412 177l-412 177v78z" />
<glyph unicode="&#x2248;" d="M555 198q0 -55 -37 -90t-95 -35q-18 0 -33 3t-31.5 9t-35.5 15t-44 22q-23 12 -38 20t-26.5 13t-21.5 7t-22 2q-25 0 -38.5 -15.5t-13.5 -41.5h-75q0 55 37 90t95 35q18 0 33 -3t31.5 -9t35.5 -15t44 -22q22 -12 37.5 -20t27 -13t21.5 -7t22 -2q25 0 38.5 15.5t13.5 41.5 h75zM555 404q0 -55 -37 -90t-95 -35q-18 0 -33 3t-31.5 9t-35.5 15t-44 22q-23 12 -38 20t-26.5 13t-21.5 7t-22 2q-25 0 -38.5 -15.5t-13.5 -41.5h-75q0 55 37 90t95 35q18 0 33 -3t31.5 -9t35.5 -15t44 -22q22 -12 37.5 -20t27 -13t21.5 -7t22 -2q25 0 38.5 15.5 t13.5 41.5h75z" />
<glyph unicode="&#x2260;" d="M45 322v72h296l67 116l59 -34l-47 -82h135v-72h-177l-77 -134h254v-72h-295l-67 -116l-59 34l47 82h-136v72h177l77 134h-254z" />
<glyph unicode="&#x2264;" d="M553 432l-392 -117l392 -117v-78l-510 159v72l510 159v-78zM553 0h-510v72h510v-72z" />
<glyph unicode="&#x2265;" d="M47 510l510 -159v-72l-510 -159v78l392 117l-392 117v78zM47 72h510v-72h-510v72z" />
<glyph unicode="&#xae;" horiz-adv-x="826" d="M123 340q0 -61 22.5 -114t61.5 -92t92 -61.5t114 -22.5t114 22.5t92 61.5t61.5 92t22.5 114t-22.5 114t-61.5 92t-92 61.5t-114 22.5t-114 -22.5t-92 -61.5t-61.5 -92t-22.5 -114zM446 347q19 0 28 11.5t9 27.5v34q0 37 -37 37h-95v-110h95zM351 295v-125h-65v340h156 q52 0 80 -28t28 -80q0 -45 -21.5 -70.5t-52.5 -33.5l75 -128h-72l-70 125h-58zM58 340q0 73 28 138t76 113t112.5 76t138.5 28q73 0 138 -28t113 -76t76 -113t28 -138q0 -74 -28 -138.5t-76 -112.5t-113 -76t-138 -28q-74 0 -138.5 28t-112.5 76t-76 112.5t-28 138.5z" />
<glyph unicode="{" horiz-adv-x="386" d="M129 235h-95v76h95v-76zM214 311h-85v405h210v-74h-125v-331zM214 235v-331h125v-74h-210v405h85z" />
<glyph unicode="}" horiz-adv-x="386" d="M257 235v-405h-210v74h125v331h85zM257 311h-85v331h-125v74h210v-405h95v-76h-95v76z" />
<glyph unicode="&#x2030;" horiz-adv-x="1146" d="M964 48q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM1102 175q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58t42.5 38.5t65.5 14t65.5 -14 t42.5 -38.5t23 -58t7 -72.5zM623 48q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM761 175q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58 t42.5 38.5t65.5 14t65.5 -14t42.5 -38.5t23 -58t7 -72.5zM185 378q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM323 505q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5 t-23 58t-7 72.5t7 72.5t23 58t42.5 38.5t65.5 14t65.5 -14t42.5 -38.5t23 -58t7 -72.5zM579 680h76l-426 -680h-76z" />
<glyph unicode="&#xa1;" horiz-adv-x="264" d="M187 400h-110v110h110v-110zM157 332l20 -149v-353h-90v353l20 149h50z" />
<glyph unicode="&#x131;" horiz-adv-x="256" d="M83 0v510h90v-510h-90z" />
<glyph unicode="&#xbf;" horiz-adv-x="495" d="M206 510h110v-110h-110v110zM129 -1q0 -51 32.5 -79t82.5 -28q54 0 88 30t39 87h90q-3 -45 -21.5 -81t-48 -61t-67.5 -38.5t-81 -13.5q-44 0 -81.5 13t-64.5 37t-42.5 57.5t-15.5 74.5q0 33 6.5 58.5t18 46t27 37.5t32.5 32q22 19 39 34.5t29 29.5t18.5 30t6.5 37v30h90 v-30q0 -29 -6 -51t-18 -41t-30.5 -37t-42.5 -37q-42 -34 -61 -64.5t-19 -72.5z" />
<glyph unicode="&#xbd;" horiz-adv-x="775" d="M559 62v-2h166v-60h-257v60l144 130q17 16 27 29.5t10 34.5t-14 34t-38 13q-26 0 -41.5 -14.5t-15.5 -43.5h-70q0 54 36 84.5t93 30.5t88 -29.5t31 -73.5q0 -38 -16.5 -62.5t-41.5 -45.5zM552 680h76l-426 -680h-76zM224 330h-70v234h-111v56h111v60h70v-350z" />
<glyph unicode="&#xbc;" horiz-adv-x="763" d="M589 266l-91 -132h93v132h-2zM591 0v78h-154v56l148 216h74v-216h63v-56h-63v-78h-68zM552 680h76l-426 -680h-76zM224 330h-70v234h-111v56h111v60h70v-350z" />
<glyph unicode="&#x2c7;" horiz-adv-x="300" d="M150 622l57 73h78l-83 -122h-103l-83 122h77z" />
<glyph unicode="&#xd3;" horiz-adv-x="706" d="M375 880h105l-106 -122h-78zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340q0 -80 -17 -145.5 t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="&#xd4;" horiz-adv-x="706" d="M296 758h-77l83 122h103l83 -122h-78l-57 73zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340 q0 -80 -17 -145.5t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="706" d="M406 758h-78l-106 122h105zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340q0 -80 -17 -145.5 t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="706" d="M484 777h-88v88h88v-88zM308 777h-88v88h88v-88zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340 q0 -80 -17 -145.5t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="&#x160;" horiz-adv-x="616" d="M306 807l57 73h78l-83 -122h-103l-83 122h77zM303 612q-65 0 -102.5 -26t-37.5 -71q0 -48 26 -71t83 -39l109 -30q44 -12 78.5 -28t58 -39.5t35.5 -54.5t12 -73q0 -45 -20.5 -81t-55 -61.5t-80.5 -39t-97 -13.5t-97 12.5t-82 38.5t-59 66t-27 96h95q8 -69 54 -99.5 t114 -30.5q34 0 63 7t50.5 21t34 34.5t12.5 47.5q0 44 -24.5 69t-78.5 40l-133 37q-84 23 -124 69.5t-40 121.5q0 38 16.5 71t47.5 57t73.5 38t95.5 14q49 0 91.5 -11.5t75 -36t53 -62.5t25.5 -90h-95q-3 34 -16.5 56.5t-34 36t-46 19t-53.5 5.5z" />
<glyph unicode="&#x161;" horiz-adv-x="509" d="M246 622l57 73h78l-83 -122h-103l-83 122h77zM309 289q76 -16 113 -52t37 -98q0 -32 -14.5 -59.5t-41.5 -47.5t-64 -32t-82 -12q-43 0 -80.5 11t-65.5 32.5t-44 54t-17 75.5h90q2 -51 36.5 -76t81.5 -25q45 0 78 20t33 58q0 31 -20 49.5t-71 28.5l-84 17q-63 13 -97 48.5 t-34 95.5q0 30 13.5 56.5t38.5 46t59 31t76 11.5q36 0 70.5 -9.5t61.5 -29.5t43.5 -51t17.5 -74h-90q-2 51 -31.5 71.5t-70.5 20.5q-42 0 -71 -20t-29 -52q0 -30 17 -46.5t55 -24.5z" />
<glyph unicode="&#x17d;" horiz-adv-x="593" d="M298 807l57 73h78l-83 -122h-103l-83 122h77zM537 595l-379 -510h378v-85h-487v85l380 510h-364v85h472v-85z" />
<glyph unicode="&#x17e;" horiz-adv-x="486" d="M251 622l57 73h78l-83 -122h-103l-83 122h77zM432 77v-77h-384v77l274 356h-254v77h358v-77l-274 -356h280z" />
<glyph unicode="&#xb7;" horiz-adv-x="262" d="M186 201h-110v110h110v-110z" />
<glyph unicode="&#x2044;" horiz-adv-x="100" d="M225 680h76l-426 -680h-76z" />
<glyph unicode="&#x2da;" horiz-adv-x="300" d="M194 651q0 18 -12 31t-32 13t-32.5 -13t-12.5 -31t12.5 -31.5t32.5 -13.5t32 13.5t12 31.5zM239 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="551" d="M304 651q0 18 -12 31t-32 13t-32.5 -13t-12.5 -31t12.5 -31.5t32.5 -13.5t32 13.5t12 31.5zM349 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5 t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90 q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#xc5;" horiz-adv-x="631" d="M359 836q0 18 -12 31t-32 13t-32.5 -13t-12.5 -31t12.5 -31.5t32.5 -13.5t32 13.5t12 31.5zM404 836q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106 l236 -680h-100z" />
<glyph unicode="&#xfe;" horiz-adv-x="595" d="M173 212q0 -66 36 -109t104 -43q57 0 93.5 32t36.5 84v158q0 52 -36.5 84t-93.5 32q-68 0 -104 -43t-36 -109v-86zM173 -170h-90v850h90v-253q24 46 64.5 70.5t98.5 24.5q51 0 89 -21t63 -57t37.5 -85t12.5 -104q0 -56 -12.5 -104.5t-37.5 -84.5t-63 -57t-89 -21 q-58 0 -98.5 24.5t-64.5 70.5v-253z" />
<glyph unicode="&#xd0;" horiz-adv-x="674" d="M350 304h-156v-219h125q96 0 141.5 40t45.5 115v200q0 75 -45.5 115t-141.5 40h-125v-219h156v-72zM319 680q154 0 223 -84.5t69 -255.5t-69 -255.5t-223 -84.5h-220v304h-72v72h72v304h220z" />
<glyph unicode="&#x142;" horiz-adv-x="307" d="M201 351v-351h-90v298l-76 -44v82l76 44v300h90v-247l76 44v-82z" />
<glyph unicode="&#x141;" horiz-adv-x="535" d="M114 255l-96 -56v82l96 56v343h95v-288l152 89v-82l-152 -89v-225h308v-85h-403v255z" />
<glyph unicode="&#xde;" horiz-adv-x="609" d="M386 217q34 0 55 22t21 56v99q0 34 -20 54t-56 20h-199v-251h199zM187 132v-132h-95v680h95v-127h162q61 0 99.5 -11.5t65.5 -39.5q26 -27 37 -67.5t11 -88.5q0 -47 -13.5 -88.5t-42.5 -70.5q-56 -55 -156 -55h-163z" />
<glyph unicode="&#xf0;" horiz-adv-x="571" d="M286 542h-134v62h94l-49 76h97l49 -76h135v-62h-94l53 -79q10 -15 23.5 -37.5t25.5 -51t20.5 -63.5t8.5 -76q0 -55 -15.5 -100.5t-44.5 -78t-72 -50.5t-97 -18t-97 18t-72 50.5t-44.5 78t-15.5 100.5q0 54 14 99t40.5 78t63 51.5t81.5 18.5q47 0 78 -14zM420 294 q0 26 -11 47.5t-29.5 36.5t-43 23.5t-50.5 8.5q-27 0 -51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-118q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v118z" />
<glyph unicode="&#x2020;" horiz-adv-x="499" d="M37 510h170v170h85v-170h170v-72h-170v-528h-85v528h-170v72z" />
<glyph unicode="&#x2021;" horiz-adv-x="571" d="M73 510h170v170h85v-170h170v-72h-170v-286h170v-72h-170v-170h-85v170h-170v72h170v286h-170v72z" />
<glyph unicode="&#x2d9;" horiz-adv-x="290" d="M189 592h-88v88h88v-88z" />
<glyph unicode="&#x2022;" horiz-adv-x="522" d="M92 340q0 35 13 66t36 54t54 36.5t66 13.5t66 -13.5t54 -36.5t36.5 -54t13.5 -66t-13.5 -66t-36.5 -54t-54 -36t-66 -13t-66 13t-54 36t-36 54t-13 66z" />
<glyph unicode="&#xf8;" horiz-adv-x="570" d="M230 69q25 -9 55 -9q27 0 51.5 8.5t42.5 23.5t29 36.5t11 47.5v158q0 20 -7 38t-19 32zM341 440q-28 10 -56 10q-27 0 -51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -21 7 -39t19 -32zM142 33q-44 35 -65 92t-21 130q0 58 14 107.5t42.5 84.5t71.5 55t101 20 q25 0 47 -4t42 -11l36 74l56 -27l-38 -78q44 -35 65 -92t21 -129q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20q-49 0 -88 14l-36 -73l-56 27z" />
<glyph unicode="&#xe6;" horiz-adv-x="847" d="M407 74q-30 -42 -74.5 -64t-101.5 -22q-53 0 -84 11.5t-53 34.5q-20 20 -31 49t-11 67q0 30 10.5 57.5t30 48.5t46.5 33.5t60 12.5h171v47q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12 q54 0 95.5 -16.5t67.5 -51.5q28 32 66 50t86 18q51 0 90.5 -19t66.5 -52t41.5 -77.5t14.5 -95.5v-48h-333v-54q0 -28 10.5 -49.5t28 -36.5t39 -22.5t43.5 -7.5q45 0 79 24.5t39 77.5h90q-3 -41 -21.5 -73t-47 -54.5t-65 -34.5t-75.5 -12q-59 0 -100 22t-71 64h-2zM460 302 h240v28q0 28 -10 50.5t-26.5 38t-38.5 23.5t-45 8t-45 -8t-38.5 -23.5t-26.5 -38t-10 -50.5v-28zM239 60q61 0 96 43.5t35 112.5v14h-181q-20 0 -31 -12.5t-11 -34.5v-55q0 -35 24.5 -51.5t67.5 -16.5z" />
<glyph unicode="&#x153;" horiz-adv-x="906" d="M465 438q29 40 71 62t98 22q52 0 93 -19t68.5 -52t42 -77.5t14.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5t-66.5 -34.5t-78 -12q-58 0 -100.5 21.5t-70.5 60.5q-29 -38 -72.5 -60 t-105.5 -22q-58 0 -101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20q62 0 106.5 -22t73.5 -62zM509 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28zM419 334q0 26 -11 47.5t-29 36.5 t-42.5 23.5t-51.5 8.5t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#x152;" horiz-adv-x="1050" d="M540 48q-35 -31 -81 -47t-106 -16q-77 0 -132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5q60 0 106 -16t81 -47v48h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v48zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210 q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="942" d="M225 247h217v348h-48zM442 0v169h-255l-82 -169h-106l335 680h549v-85h-346v-206h300v-85h-300v-219h346v-85h-441z" />
<glyph unicode="&#x22;" horiz-adv-x="386" d="M307 433h-76v262h76v-262zM155 433h-76v262h76v-262z" />
<glyph unicode="'" horiz-adv-x="234" d="M155 433h-76v262h76v-262z" />
<glyph unicode="&#xd8;" horiz-adv-x="706" d="M353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 64 -41 108l-233 -468q40 -17 87 -17zM166 235q0 -36 12.5 -65t35.5 -51l234 472q-21 10 -45 15.5t-50 5.5q-39 0 -73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210zM645 340q0 -80 -17 -145.5t-52.5 -112 t-90.5 -72t-132 -25.5q-36 0 -67.5 5.5t-58.5 16.5l-37 -75l-56 27l39 79q-58 45 -85 122.5t-27 179.5q0 80 17 145.5t52.5 112t90.5 72t132 25.5q39 0 72 -7t62 -20l40 80l56 -27l-43 -86q54 -46 79.5 -121.5t25.5 -173.5z" />
<glyph unicode="&#xb0;" horiz-adv-x="394" d="M107 555q0 -38 26 -64t64 -26t64 26t26 64t-26 64t-64 26t-64 -26t-26 -64zM57 555q0 29 11 54.5t30 44.5t44.5 30t54.5 11t54.5 -11t44.5 -30t30 -44.5t11 -54.5t-11 -54.5t-30 -44.5t-44.5 -30t-54.5 -11t-54.5 11t-44.5 30t-30 44.5t-11 54.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="624" d="M521 680v-770h-85v698h-127v-698h-85v430h-170v340h467z" />
<glyph unicode="&#xdf;" horiz-adv-x="553" d="M253 63h7q63 0 102 26.5t39 72.5v87q0 31 -16 51t-53 20h-79v77h69q37 0 53 19.5t16 50.5v76q0 20 -8.5 34t-22.5 23t-32.5 13.5t-38.5 4.5q-24 0 -45 -6.5t-36.5 -20t-25 -33.5t-9.5 -48v-510h-90v510q0 35 14.5 68.5t41.5 59.5t65 41.5t85 15.5t83.5 -14t61.5 -38 t38 -55.5t13 -66.5q0 -68 -24.5 -104t-74.5 -59q50 -15 79.5 -54.5t29.5 -113.5q0 -47 -16.5 -85t-47 -64.5t-73.5 -41t-95 -14.5h-10v78z" />
<glyph unicode="&#xa7;" horiz-adv-x="585" d="M195 143h225v225h-225v-225zM105 65v615h404v-78h-314v-156h315v-615h-406v78h316v156h-315z" />
<glyph unicode="$" horiz-adv-x="580" d="M320 384l41 -10q37 -9 68.5 -24.5t55 -39t36.5 -55.5t13 -75t-17.5 -77.5t-46.5 -59t-68 -39.5t-82 -18v-81h-66v83q-41 4 -77 19t-64 41t-45.5 63t-21.5 87h90q5 -59 37.5 -90.5t80.5 -39.5v242l-31 8q-75 20 -116.5 69.5t-41.5 124.5q0 35 15 66.5t40.5 55.5t60 39.5 t73.5 19.5v82h66v-83q37 -5 70 -19t58.5 -38t41.5 -59t19 -81h-90q-5 57 -33.5 82.5t-65.5 32.5v-226zM320 294v-227q51 7 86.5 35t35.5 76q0 87 -105 112zM254 612q-40 -7 -69.5 -31t-29.5 -65q0 -85 99 -115v211z" />
<glyph unicode="&#xa2;" d="M341 -92h-66v82q-47 6 -82 27.5t-57.5 56t-33.5 79.5t-11 98q0 113 47.5 184.5t136.5 83.5v83h66v-82q70 -8 117.5 -50.5t54.5 -126.5h-90q-5 49 -27.5 72.5t-54.5 30.5v-383q36 8 59 34.5t27 73.5h90q-5 -42 -20.5 -74t-39 -54.5t-53.5 -35.5t-63 -17v-82zM275 446 q-37 -8 -63 -37.5t-26 -78.5v-150q0 -46 25.5 -76.5t63.5 -40.5v383z" />
<glyph unicode="&#xa3;" d="M533 79v-79h-479v79h90v237h-90v79h90v108q0 37 12.5 71.5t37.5 61.5t63 43t88 16t88 -16t63 -43t37.5 -62.5t12.5 -73.5h-90q0 57 -27 87t-84 30t-84 -30t-27 -87v-105h163v-79h-163v-237h299z" />
<glyph unicode="&#xa5;" d="M85 232h170v59h-170v59h130l-164 330h98l151 -310l153 310h97l-166 -330h130v-59h-169v-59h169v-59h-169v-173h-90v173h-170v59z" />
<glyph unicode="&#x20ac;" d="M39 431h56q6 58 22.5 106.5t46.5 83.5t75 54.5t108 19.5q99 0 153.5 -52t61.5 -142h-90q-7 62 -39 89t-95 27q-75 0 -111 -48.5t-36 -123.5v-14h228l-20 -59h-208v-61h188l-20 -59h-168v-17q0 -38 10.5 -69.5t30 -54t47 -35.5t60.5 -13q63 0 95 28t39 97h90 q-7 -95 -67 -149t-156 -54q-57 0 -100 19t-73 54t-47.5 84t-24.5 110h-76l20 59h52v61h-72z" />
<glyph unicode="&#x2122;" horiz-adv-x="870" d="M679 680h107v-340h-70v246l-86 -246h-65l-85 246v-246h-70v340h109l80 -238zM69 680h274v-63h-102v-277h-70v277h-102v63z" />
<glyph unicode="*" horiz-adv-x="458" d="M94 403l-25 44l109 63l-109 63l25 44l110 -64v127h50v-127l110 64l25 -44l-110 -63l110 -63l-25 -44l-110 63v-126h-50v126z" />
<glyph unicode="^" d="M262 680h76l207 -388h-84l-162 302l-159 -302h-85z" />
<glyph unicode="~" d="M556 300q0 -55 -37 -90t-95 -35q-18 0 -33 3t-31.5 9t-35.5 15t-44 22q-23 12 -38 20t-26.5 13t-21.5 7t-22 2q-25 0 -38.5 -15.5t-13.5 -41.5h-75q0 55 37 90t95 35q18 0 33 -3t31.5 -9t35.5 -15t44 -22q22 -12 37.5 -20t27 -13t21.5 -7t22 -2q25 0 38.5 15.5t13.5 41.5 h75z" />
<glyph unicode="&#xb5;" horiz-adv-x="598" d="M185 -170h-90v680h90v-341q0 -51 23 -80t77 -29q57 0 90.5 42.5t33.5 109.5v298h90v-510h-90v84q-21 -45 -55.5 -70.5t-88.5 -25.5q-27 0 -47.5 9.5t-32.5 23.5v-191z" />
<glyph unicode="&#x3c0;" horiz-adv-x="606" d="M371 433h-158v-433h-90v433h-90v77h518v-77h-90v-356h90v-77h-180v433z" />
<glyph unicode="&#x220f;" horiz-adv-x="718" d="M528 773h-338v-943h-90v1020h518v-1020h-90v943z" />
<glyph unicode="&#x2211;" horiz-adv-x="572" d="M148 773l307 -430l-307 -436h373v-77h-478v77l307 436l-307 430v77h478v-77h-373z" />
<glyph unicode="&#x221a;" horiz-adv-x="615" d="M190 255l93 -301l227 726h92l-271 -850h-96l-112 348h-90v77h157z" />
<glyph unicode="&#x192;" horiz-adv-x="438" d="M65 433h105l50 247h199v-75h-128l-35 -172h132v-75h-147l-106 -528h-198v75h128l90 453h-90v75z" />
<glyph unicode="&#x2206;" horiz-adv-x="670" d="M149 77h372l-187 474zM291 680h88l267 -680h-622z" />
<glyph unicode="&#x25ca;" horiz-adv-x="650" d="M622 343l-245 -430h-104l-245 430l245 430h104zM520 343l-195 346l-195 -346l195 -346z" />
<glyph unicode="&#x222b;" horiz-adv-x="396" d="M25 -93h128v943h218v-77h-128v-943h-218v77z" />
<glyph unicode="&#x2126;" horiz-adv-x="664" d="M456 77h120v-77h-200v215q59 10 94.5 39.5t35.5 82.5v158q0 30 -14.5 53.5t-38.5 39.5t-55.5 24.5t-65.5 8.5q-35 0 -66.5 -8.5t-55.5 -24.5t-38 -39.5t-14 -53.5v-158q0 -52 35 -81.5t93 -40.5v-215h-200v77h120v83q-72 30 -107.5 95.5t-35.5 160.5q0 133 68.5 206 t200.5 73t200.5 -73t68.5 -206q0 -95 -36 -161t-109 -96v-82z" />
<glyph unicode="&#x2039;" horiz-adv-x="310" d="M165 71l-107 186l107 186h80l-97 -186l97 -186h-80z" />
<glyph unicode="&#x203a;" horiz-adv-x="310" d="M65 71l97 186l-97 186h80l107 -186l-107 -186h-80z" />
<glyph unicode="&#x2202;" horiz-adv-x="598" d="M267 65q83 0 122.5 52t39.5 162q-20 43 -57.5 63t-85.5 20q-64 0 -104 -27.5t-40 -81.5v-79q0 -27 10.5 -47t27.5 -34t40 -21t47 -7zM88 630q29 25 71 45t100 20q72 0 123.5 -24.5t84 -72t47.5 -116t15 -155.5q0 -72 -14 -134t-45 -107.5t-81 -71.5t-121 -26 q-57 0 -99 19.5t-69 51.5t-40 73.5t-13 85.5q0 52 16.5 93t45.5 69.5t70 44t91 15.5q49 0 90 -20t69 -58q0 134 -41.5 194.5t-124.5 60.5q-42 0 -76 -16.5t-59 -38.5z" />
<glyph unicode="f" horiz-adv-x="347" d="M209 510h107v-77h-107v-433h-89v433h-86v77h86v170h206v-77h-117v-93z" />
<glyph unicode="n" horiz-adv-x="570" d="M173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v510h90v-84z" />
<glyph unicode="&#xaf;" horiz-adv-x="300" d="M5 670h289v-59h-289v59z" />
<glyph unicode="&#xbb;" horiz-adv-x="497" d="M252 71l97 186l-97 186h80l107 -186l-107 -186h-80zM65 71l97 186l-97 186h80l107 -186l-107 -186h-80z" />
<glyph unicode="&#x2dd;" horiz-adv-x="300" d="M216 695h105l-101 -122h-78zM53 695h105l-101 -122h-78z" />
<glyph unicode=";" horiz-adv-x="262" d="M186 0l-62 -110h-48l59 110h-59v110h110v-110zM186 400h-110v110h110v-110z" />
<glyph unicode="i" horiz-adv-x="256" d="M175 586h-94v94h94v-94zM83 0v510h90v-510h-90z" />
<glyph unicode="&#x2193;" horiz-adv-x="690" d="M90 333l214 -213v560h82v-560l214 213v-115l-219 -218h-72l-219 218v115z" />
<glyph unicode="&#x2191;" horiz-adv-x="690" d="M90 462l219 218h72l219 -218v-115l-214 213v-560h-82v560l-214 -213v115z" />
<glyph unicode="&#x2dc;" horiz-adv-x="300" d="M304 680q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50z" />
<glyph unicode="&#xd1;" horiz-adv-x="695" d="M501 850q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM511 115v565h92v-680h-134l-281 568h-4v-568h-92v680h134l281 -565h4z" />
<glyph unicode="&#xe3;" horiz-adv-x="551" d="M424 680q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45 q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34 t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#xf1;" horiz-adv-x="570" d="M439 680q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5 v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v510h90v-84z" />
<glyph unicode="&#xf5;" horiz-adv-x="570" d="M439 680q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55 t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5t-42.5 23.5t-51.5 8.5t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5 t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#xc3;" horiz-adv-x="631" d="M469 850q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680 h-100z" />
<glyph unicode="&#xd5;" horiz-adv-x="706" d="M507 850q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5 t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340q0 -80 -17 -145.5t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72 t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="&#x2d8;" horiz-adv-x="300" d="M75 695q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60z" />
<glyph horiz-adv-x="608" d="M304 65q62 0 101.5 37t39.5 105v266q0 68 -39.5 105t-101.5 37t-101.5 -37t-39.5 -105v-266q0 -68 39.5 -105t101.5 -37zM545 340q0 -77 -11 -142t-38 -112t-73.5 -74t-118.5 -27t-118.5 27t-73.5 74t-38 112t-11 142q0 76 11 141.5t38 112.5t73.5 74t118.5 27t118.5 -27 t73.5 -74t38 -112.5t11 -141.5z" />
<glyph horiz-adv-x="399" d="M306 0h-90v522h-175v79h175v79h90v-680z" />
<glyph horiz-adv-x="570" d="M175 79h339v-79h-457v79l272 267q20 20 35.5 37.5t26 34.5t16 35.5t5.5 41.5q0 57 -34.5 88.5t-89.5 31.5q-60 0 -98 -33.5t-38 -101.5h-92q0 51 17.5 90.5t48.5 67.5t73 42.5t91 14.5t88.5 -14t67.5 -40.5t43 -63.5t15 -82q0 -33 -9 -62t-23.5 -53.5t-33.5 -46.5 t-40 -41l-223 -211v-2z" />
<glyph horiz-adv-x="589" d="M200 309v79h142q36 0 55.5 20.5t19.5 52.5v62q0 22 -10 39.5t-27 29t-39.5 17.5t-47.5 6q-61 0 -94.5 -31.5t-37.5 -82.5h-92q0 37 15.5 72t44 62t70 43.5t94.5 16.5q51 0 92 -13.5t70 -38t44.5 -58.5t15.5 -74q0 -62 -24.5 -100.5t-61.5 -58.5q47 -21 73 -59.5 t26 -102.5q0 -49 -18 -87t-49.5 -64.5t-75.5 -40t-96 -13.5q-50 0 -93 13t-75 39.5t-50.5 66.5t-18.5 94h92q5 -71 45 -102t100 -31q31 0 57 8.5t44.5 22.5t29 32.5t10.5 38.5v68q0 32 -19.5 53t-55.5 21h-155z" />
<glyph horiz-adv-x="567" d="M128 243h210v308zM338 0v164h-300v79l299 437h91v-437h98v-79h-98v-164h-90z" />
<glyph horiz-adv-x="577" d="M66 298l26 382h393v-79h-310l-16 -211q23 32 62 49.5t83 17.5q51 0 92 -16.5t70 -47t44.5 -73.5t15.5 -95q0 -54 -16.5 -98.5t-47.5 -76t-75 -48.5t-98 -17q-106 0 -166 53t-68 150h92q7 -63 43 -93t96 -30q61 0 100.5 30.5t39.5 85.5v89q0 54 -38 81.5t-100 27.5 q-47 0 -78.5 -21.5t-51.5 -59.5h-92z" />
<glyph horiz-adv-x="602" d="M306 65q32 0 58.5 10t45 27.5t29 41t10.5 50.5v56q0 54 -38.5 86.5t-103.5 32.5t-105 -32.5t-40 -86.5v-56q0 -27 10.5 -50.5t29.5 -41t45.5 -27.5t58.5 -10zM306 -15q-68 0 -114.5 21.5t-75 64.5t-41.5 106t-13 146q0 84 13.5 153t43.5 117.5t76.5 75t112.5 26.5 q107 0 160 -48t62 -136h-92q-6 50 -38 77t-92 27q-68 0 -107 -47t-39 -124v-67q28 35 69.5 52.5t89.5 17.5q54 0 96 -16t71.5 -45t45 -70t15.5 -91q0 -43 -15 -86t-45.5 -77t-76 -55.5t-106.5 -21.5z" />
<glyph horiz-adv-x="520" d="M209 0h-102l286 601h-356v79h453v-78z" />
<glyph horiz-adv-x="608" d="M159 162q0 -45 39.5 -71t105.5 -26t105.5 26t39.5 71v52q0 45 -39.5 71.5t-105.5 26.5t-105.5 -26.5t-39.5 -71.5v-52zM431 524q0 22 -10 39.5t-27.5 28.5t-40.5 17t-49 6t-49.5 -6t-40.5 -17t-27 -28.5t-10 -39.5v-43q0 -23 10 -40t27 -28t40.5 -17t49.5 -6t49 6 t40.5 17t27.5 28t10 40v43zM304 -15q-51 0 -95.5 12t-77.5 36t-52.5 61t-19.5 88q0 63 33 107t91 65q-104 45 -104 157q0 43 17.5 77t48 58t71.5 36.5t88 12.5t88 -12.5t71.5 -36.5t48 -58t17.5 -77q0 -112 -104 -157q58 -21 91 -65t33 -107q0 -51 -19.5 -88t-52.5 -61 t-77.5 -36t-95.5 -12z" />
<glyph horiz-adv-x="603" d="M296 615q-32 0 -58.5 -10t-45.5 -27.5t-29 -41t-10 -50.5v-56q0 -54 38.5 -86.5t103.5 -32.5t105 32.5t40 86.5v56q0 27 -10.5 50.5t-29.5 41t-45.5 27.5t-58.5 10zM296 695q67 0 114 -21.5t75.5 -64.5t41.5 -106t13 -146q0 -85 -13.5 -154t-43.5 -117.5t-76.5 -74.5 t-112.5 -26q-107 0 -160 48t-62 136h92q6 -50 38 -77t92 -27q68 0 107 47t39 124v67q-28 -35 -69.5 -52.5t-89.5 -17.5q-54 0 -96 16t-71.5 45t-45 70t-15.5 91q0 43 15 86t45.5 77t76 55.5t106.5 21.5z" />
<glyph unicode=")" horiz-adv-x="315" d="M168 378q0 83 -35.5 164.5t-103.5 173.5h80q34 -44 61.5 -91t47 -100.5t30 -115.5t10.5 -136t-10.5 -136t-30 -115.5t-47 -100.5t-61.5 -91h-80q64 85 101.5 170t37.5 168v210z" />
<glyph unicode="&#xa4;" d="M144 292q0 -32 12 -60.5t33 -49.5t49.5 -33t60.5 -12t60.5 12t49.5 33t33 49.5t12 60.5t-12 60.5t-33 49.5t-49.5 33t-60.5 12t-60.5 -12t-49.5 -33t-33 -49.5t-12 -60.5zM111 158q-42 59 -42 134q0 38 11.5 71.5t31.5 61.5l-55 56l54 54l55 -56q29 20 62 31.5t71 11.5 q37 0 70.5 -11.5t62.5 -31.5l56 56l54 -54l-56 -56q20 -29 31.5 -62.5t11.5 -70.5q0 -38 -11.5 -71t-31.5 -62l56 -55l-54 -53l-55 54q-29 -20 -62.5 -31.5t-71.5 -11.5q-75 0 -134 42l-54 -54l-54 54z" />
<glyph unicode="&#xb8;" horiz-adv-x="300" d="M80 -105h70v70h70v-135h-140v65z" />
<glyph horiz-adv-x="368" d="M184 378q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM322 505q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58t42.5 38.5t65.5 14t65.5 -14 t42.5 -38.5t23 -58t7 -72.5z" />
<glyph unicode="&#x2196;" horiz-adv-x="715" d="M90 150v309l51 51h309l81 -81h-302l396 -396l-58 -58l-396 396v-302z" />
<glyph unicode="&#x2197;" horiz-adv-x="715" d="M544 69v302l-396 -396l-58 58l396 396h-302l81 81h309l51 -51v-309z" />
<glyph unicode="&#x2198;" horiz-adv-x="715" d="M184 81h302l-396 396l58 58l396 -396v302l81 -81v-309l-51 -51h-309z" />
<glyph unicode="&#x2199;" horiz-adv-x="715" d="M450 0h-309l-51 51v309l81 81v-302l396 396l58 -58l-396 -396h302z" />
<glyph horiz-adv-x="320" d="M217 330h-70v232h-111v58h111v60h70v-350z" />
<glyph horiz-adv-x="356" d="M188 596l-91 -132h93v132h-2zM190 330v78h-154v56l148 216h74v-216h63v-56h-63v-78h-68z" />
<glyph horiz-adv-x="350" d="M52 478l14 202h219v-53h-157l-6 -94q12 15 30 24t41 9q54 0 84.5 -32.5t30.5 -85.5q0 -59 -34.5 -92.5t-95.5 -33.5q-63 0 -95 28.5t-37 78.5h70q3 -29 19 -41.5t42 -12.5t42 14t16 35v45q0 23 -17 33.5t-40 10.5q-17 0 -33 -9t-23 -26h-70z" />
<glyph horiz-adv-x="314" d="M139 330h-76l148 294h-175v56h250v-56z" />
<glyph horiz-adv-x="364" d="M118 417q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM237 596q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM182 322q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5z" />
<glyph horiz-adv-x="364" d="M181 633q-29 0 -46 -15t-17 -40v-28q0 -24 17 -36.5t46 -12.5t46.5 12.5t17.5 36.5v28q0 25 -17.5 40t-46.5 15zM181 688q137 0 137 -176q0 -41 -8 -76t-25 -60.5t-43 -39.5t-63 -14q-56 0 -88 26.5t-37 71.5h70q6 -43 56 -43q29 0 47 19.5t18 49.5v39q-13 -17 -33 -27 t-47 -10q-54 0 -87 32t-33 83q0 24 9 47t26 40t42.5 27.5t58.5 10.5z" />
<glyph horiz-adv-x="363" d="M183 377q29 0 46 15t17 40v28q0 24 -17 36.5t-46 12.5t-46.5 -12.5t-17.5 -36.5v-28q0 -25 17.5 -40t46.5 -15zM183 322q-137 0 -137 176q0 41 8 76t25 60.5t43 39.5t63 14q56 0 88 -26.5t37 -71.5h-70q-6 43 -56 43q-29 0 -47 -19.5t-18 -49.5v-39q13 17 33 27t47 10 q54 0 87 -32t33 -83q0 -24 -9 -47t-26 -40t-42.5 -27.5t-58.5 -10.5z" />
<glyph horiz-adv-x="333" d="M199 580v17q0 20 -10.5 29t-28.5 9q-19 0 -31 -8.5t-12 -28.5h-67q2 44 33 66t79 22t75.5 -23t27.5 -68v-127h45v-52h-107l-1 39h-2q-25 -45 -75 -45q-38 0 -61 22.5t-23 57.5t21.5 56.5t64.5 26.5zM143 461q26 0 41 16.5t15 47.5v7l-69 -6q-20 -1 -20 -21v-23 q0 -21 33 -21z" />
<glyph horiz-adv-x="349" d="M60 416v350h66v-123q12 20 30.5 31.5t47.5 11.5q27 0 46.5 -11t32 -29.5t18.5 -43.5t6 -53t-6.5 -53.5t-19 -44.5t-32.5 -30t-47 -11q-26 0 -45 12t-31 32v-38h-66zM126 525q0 -30 15.5 -46.5t41.5 -16.5q24 0 38.5 11.5t14.5 35.5v79q0 24 -14.5 35.5t-38.5 11.5 q-26 0 -41.5 -17t-15.5 -47v-46z" />
<glyph horiz-adv-x="314" d="M42 547q0 67 33 103t89 36q41 0 71.5 -22t35.5 -72h-66q-2 26 -13.5 34.5t-31.5 8.5t-33.5 -12t-13.5 -35v-79q0 -21 13 -34t35 -13q44 0 47 44h65q-3 -26 -13 -44t-25 -29.5t-34 -17t-39 -5.5q-58 0 -89 37.5t-31 99.5z" />
<glyph horiz-adv-x="350" d="M224 416v38q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5t31 -31.5v123h66v-350h-66zM224 571q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5 q26 0 41.5 16.5t15.5 46.5v46z" />
<glyph horiz-adv-x="323" d="M42 549q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM112 576h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13z" />
<glyph horiz-adv-x="245" d="M152 680h65v-52h-65v-212h-67v212h-52v52h52v86h138v-52h-71v-34z" />
<glyph horiz-adv-x="350" d="M224 571q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5q26 0 41.5 16.5t15.5 46.5v46zM224 680h66v-259q0 -26 -10 -46.5t-26.5 -34t-37.5 -20.5t-44 -7q-48 0 -79.5 18.5t-39.5 65.5h63q4 -19 17.5 -25.5t35.5 -6.5t38.5 12 t16.5 46v31q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5t31 -31.5v37z" />
<glyph horiz-adv-x="328" d="M127 416h-67v350h67v-350zM128 560l85 118h78l-93 -118l101 -144h-76z" />
<glyph horiz-adv-x="488" d="M274 636q13 26 33 38t45 12q35 0 57 -24t22 -78v-168h-67v172q0 20 -8.5 32.5t-29.5 12.5q-20 0 -33.5 -15t-13.5 -47v-155h-67v172q0 20 -8.5 32.5t-28.5 12.5t-34 -15t-14 -47v-155h-67v264h67v-39q10 22 30 33.5t43 11.5q27 0 46 -12t28 -38z" />
<glyph horiz-adv-x="347" d="M127 641q12 21 31 33t47 12q40 0 62.5 -24t22.5 -73v-173h-67v168q0 23 -9 36t-33 13q-26 0 -40 -18t-14 -46v-153h-67v264h67v-39z" />
<glyph horiz-adv-x="238" d="M60 680h151v-53h-85v-211h-66v264z" />
<glyph horiz-adv-x="302" d="M191 570q29 -5 49.5 -24t20.5 -54t-28 -58.5t-81 -23.5q-52 0 -81.5 23t-30.5 66h67q2 -21 12.5 -30t32.5 -9q19 0 31 8t12 23q0 24 -36 30l-41 7q-35 6 -53.5 26t-18.5 52q0 35 28.5 57.5t73.5 22.5t73.5 -21t30.5 -65h-66q-1 20 -10.5 28t-26.5 8t-27.5 -7.5 t-10.5 -21.5q0 -22 27 -27z" />
<glyph horiz-adv-x="261" d="M151 468h65v-52h-132v212h-51v52h51v86h67v-86h65v-52h-65v-160z" />
<glyph horiz-adv-x="307" d="M153 500h2l56 180h71l-96 -264h-64l-97 264h74z" />
<glyph horiz-adv-x="448" d="M303 508l42 172h71l-82 -264h-70l-40 179h-2l-37 -179h-71l-82 264h75l40 -172h2l34 172h80l38 -172h2z" />
<glyph horiz-adv-x="321" d="M124 553l-85 127h75l48 -80l48 80h73l-85 -123l94 -141h-75l-55 94l-57 -94h-76z" />
<glyph horiz-adv-x="310" d="M157 500l58 180h71l-130 -350h-109v52h64l12 35l-99 263h74l57 -180h2z" />
<glyph horiz-adv-x="303" d="M259 468v-52h-219v40l130 172h-120v52h207v-39l-132 -173h134z" />
<glyph horiz-adv-x="187" d="M132 416l-35 -66h-41l34 66h-35v76h77v-76z" />
<glyph horiz-adv-x="187" d="M132 416h-77v76h77v-76z" />
<glyph horiz-adv-x="333" d="M220 587q0 23 -15.5 36t-38.5 13t-38.5 -13t-15.5 -36v-78q0 -23 15.5 -36t38.5 -13t38.5 13t15.5 36v78zM291 548q0 -63 -32.5 -100.5t-92.5 -37.5q-59 0 -91.5 37.5t-32.5 100.5t32.5 100.5t91.5 37.5q60 0 92.5 -37.5t32.5 -100.5z" />
<glyph horiz-adv-x="187" d="M130 706h-71v60h71v-60zM60 416v264h67v-264h-67z" />
<glyph horiz-adv-x="187" d="M60 680h67v-350h-136v52h69v298zM129 706h-71v60h71v-60z" />
<glyph horiz-adv-x="187" d="M60 416v350h67v-350h-67z" />
<glyph horiz-adv-x="347" d="M127 641q12 21 31 33t47 12q40 0 62.5 -24t22.5 -73v-173h-67v168q0 23 -9 36t-33 13q-26 0 -40 -18t-14 -46v-153h-67v350h67v-125z" />
<glyph horiz-adv-x="345" d="M218 455q-12 -21 -31.5 -33t-45.5 -12q-38 0 -61 24t-23 73v173h67v-168q0 -23 9.5 -36t31.5 -13q26 0 39.5 19t13.5 45v153h67v-264h-67v39z" />
<glyph horiz-adv-x="350" d="M224 571q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5q26 0 41.5 16.5t15.5 46.5v46zM224 330v124q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5 t31 -31.5v37h66v-350h-66z" />
<glyph horiz-adv-x="349" d="M60 330v350h66v-37q12 20 30.5 31.5t47.5 11.5q27 0 46.5 -11t32 -29.5t18.5 -43.5t6 -53t-6.5 -53.5t-19 -44.5t-32.5 -30t-47 -11q-26 0 -45 12t-31 32v-124h-66zM126 525q0 -30 15.5 -46.5t41.5 -16.5q24 0 38.5 11.5t14.5 35.5v79q0 24 -14.5 35.5t-38.5 11.5 q-26 0 -41.5 -17t-15.5 -47v-46z" />
<glyph horiz-adv-x="346" d="M41 525v46h108v109h49v-109h107v-46h-107v-109h-49v109h-108z" />
<glyph horiz-adv-x="374" d="M319 524h-264v46h264v-46z" />
<glyph horiz-adv-x="374" d="M55 470v48h264v-48h-264zM55 576v48h264v-48h-264z" />
<glyph horiz-adv-x="323" d="M42 549q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM112 576h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13zM179 775h68 l-60 -62h-53z" />
<glyph horiz-adv-x="323" d="M42 549q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM112 576h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13zM186 713 h-53l-60 62h68z" />
<glyph horiz-adv-x="285" d="M100 850h85v-1020h-85v1020z" />
<glyph d="M555 291h-510v72h510v-72z" />
<glyph d="M99 71l-55 55l201 200l-201 201l55 55l200 -201l201 201l55 -55l-201 -201l201 -200l-54 -54l-201 201z" />
<glyph d="M45 290v72h217v219h76v-219h217v-72h-217v-219h-76v219h-217z" />
<glyph d="M351 479h-102v103h102v-103zM351 72h-102v103h102v-103zM555 291h-510v72h510v-72z" />
<glyph horiz-adv-x="744" d="M135 330q0 -35 20 -56t56 -21q19 0 34 5.5t28 15.5t26 24t27 32q-26 35 -51.5 57.5t-61.5 22.5q-38 0 -58 -22.5t-20 -57.5zM372 280q-16 -20 -33 -37t-36.5 -30t-43 -20.5t-51.5 -7.5q-33 0 -59.5 11.5t-45.5 31.5t-29.5 46.5t-10.5 57.5t11 58t30.5 47t46 31t57.5 11 q29 0 53 -8.5t43.5 -22.5t36 -32.5t31.5 -38.5q15 20 31.5 38.5t36 32.5t43.5 22.5t53 8.5q31 0 57.5 -11t46 -31t30.5 -47t11 -58t-10.5 -57.5t-29.5 -46.5t-45.5 -31.5t-59.5 -11.5q-56 0 -94.5 28t-69.5 67zM609 330q0 35 -20 57.5t-58 22.5q-36 0 -61.5 -22.5 t-51.5 -57.5q14 -18 27 -32t26 -24t28 -15.5t34 -5.5q36 0 56 21t20 56z" />
<glyph d="M45 188v72h510v-72h-510zM45 394v72h510v-72h-510z" />
<glyph d="M539 504l-412 -177l412 -177v-78l-510 219v72l510 219v-78z" />
<glyph d="M61 582l510 -219v-72l-510 -219v78l412 177l-412 177v78z" />
<glyph horiz-adv-x="806" d="M285 154v372h270v-295h90v372h-450v-526h360v-77h-450v680h630v-526h-450zM375 231h90v218h-90v-218z" />
<glyph d="M45 394v72h296l67 116l59 -34l-47 -82h135v-72h-177l-77 -134h254v-72h-295l-67 -116l-59 34l47 82h-136v72h177l77 134h-254z" />
<glyph d="M555 72h-510v72h510v-72zM45 351v72h217v159h76v-159h217v-72h-217v-159h-76v159h-217z" />
<glyph d="M49 582l510 -159v-72l-510 -159v78l392 117l-392 117v78zM49 144h510v-72h-510v72z" />
<glyph d="M551 504l-392 -117l392 -117v-78l-510 159v72l510 159v-78zM551 72h-510v72h510v-72z" />
<glyph d="M556 270q0 -55 -37 -90t-95 -35q-18 0 -33 3t-31.5 9t-35.5 15t-44 22q-23 12 -38 20t-26.5 13t-21.5 7t-22 2q-25 0 -38.5 -15.5t-13.5 -41.5h-75q0 55 37 90t95 35q18 0 33 -3t31.5 -9t35.5 -15t44 -22q22 -12 37.5 -20t27 -13t21.5 -7t22 -2q25 0 38.5 15.5t13.5 41.5 h75zM556 476q0 -55 -37 -90t-95 -35q-18 0 -33 3t-31.5 9t-35.5 15t-44 22q-23 12 -38 20t-26.5 13t-21.5 7t-22 2q-25 0 -38.5 -15.5t-13.5 -41.5h-75q0 55 37 90t95 35q18 0 33 -3t31.5 -9t35.5 -15t44 -22q22 -12 37.5 -20t27 -13t21.5 -7t22 -2q25 0 38.5 15.5 t13.5 41.5h75z" />
<glyph d="M473 394h-436v72h510v-274h-74v202z" />
<glyph horiz-adv-x="500" d="M253 144l97 186l-97 186h80l107 -186l-107 -186h-80zM66 144l97 186l-97 186h80l107 -186l-107 -186h-80z" />
<glyph horiz-adv-x="313" d="M66 144l97 186l-97 186h80l107 -186l-107 -186h-80z" />
<glyph horiz-adv-x="500" d="M167 144l-107 186l107 186h80l-97 -186l97 -186h-80zM354 144l-107 186l107 186h80l-97 -186l97 -186h-80z" />
<glyph horiz-adv-x="313" d="M167 144l-107 186l107 186h80l-97 -186l97 -186h-80z" />
<glyph horiz-adv-x="360" d="M312 783v-74h-125v-738h125v-74h-210v886h210z" />
<glyph horiz-adv-x="391" d="M133 302h-95v76h95v-76zM218 378h-85v405h210v-74h-125v-331zM218 302v-331h125v-74h-210v405h85z" />
<glyph horiz-adv-x="318" d="M148 235q0 -83 37.5 -168t101.5 -170h-80q-34 44 -61.5 91t-47 100.5t-30 115.5t-10.5 136t10.5 136t30 115.5t47 100.5t61.5 91h80q-68 -92 -103.5 -173.5t-35.5 -164.5v-210z" />
<glyph horiz-adv-x="360" d="M258 783v-886h-210v74h125v738h-125v74h210z" />
<glyph horiz-adv-x="391" d="M258 302v-405h-210v74h125v331h85zM258 378h-85v331h-125v74h210v-405h95v-76h-95v76z" />
<glyph horiz-adv-x="318" d="M170 445q0 83 -35.5 164.5t-103.5 173.5h80q34 -44 61.5 -91t47 -100.5t30 -115.5t10.5 -136t-10.5 -136t-30 -115.5t-47 -100.5t-61.5 -91h-80q64 85 101.5 170t37.5 168v210z" />
<glyph horiz-adv-x="626" d="M78 375h470v-82h-470v82z" />
<glyph horiz-adv-x="1146" d="M103 375h940v-82h-940v82z" />
<glyph horiz-adv-x="436" d="M78 375h280v-82h-280v82z" />
<glyph unicode="&#xad;" horiz-adv-x="432" d="M76 312h280v-82h-280v82z" />
<glyph unicode="&#x120;" horiz-adv-x="672" d="M374 762h-88v88h88v-88zM61 334q0 85 17.5 152t52.5 113.5t89 71t127 24.5q51 0 94.5 -14.5t75.5 -41.5t52 -65.5t23 -86.5h-95q-7 60 -47.5 92.5t-111.5 32.5q-38 0 -69.5 -12.5t-54 -34.5t-35.5 -52.5t-13 -67.5v-210q0 -36 13.5 -66.5t37 -53t56 -35t70.5 -12.5 q79 0 122 42t43 122v46h-154v85h249v-363h-91l-4 72q-26 -39 -67 -63t-106 -24t-116 24.5t-86 70t-53.5 110t-18.5 144.5z" />
<glyph unicode="&#x116;" horiz-adv-x="602" d="M361 762h-88v88h88v-88zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#x100;" horiz-adv-x="631" d="M171 840h289v-59h-289v59zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#x101;" horiz-adv-x="551" d="M116 670h289v-59h-289v59zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41 q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#x102;" horiz-adv-x="631" d="M240 865q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#x103;" horiz-adv-x="551" d="M185 695q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5z M533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5 t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#x106;" horiz-adv-x="642" d="M357 865h105l-106 -122h-78zM340 -15q-68 0 -120 24.5t-87 70.5t-53.5 110t-18.5 144q0 83 17 150t52 114t88 72t124 25q49 0 91.5 -14.5t74.5 -41.5t51.5 -65.5t22.5 -86.5h-95q-7 60 -47 92.5t-107 32.5q-36 0 -66.5 -12.5t-53 -34.5t-35 -52.5t-12.5 -67.5v-210 q0 -36 13.5 -66.5t37 -53t55 -35t67.5 -12.5q68 0 110 30.5t49 99.5h95q-3 -48 -23.5 -87.5t-54 -67t-78.5 -43t-97 -15.5z" />
<glyph unicode="&#x107;" horiz-adv-x="534" d="M296 695h105l-106 -122h-78zM57 251q0 61 14.5 111.5t42.5 85.5t69 54.5t93 19.5q40 0 75.5 -10.5t62.5 -32.5t44 -56t21 -80h-90q-3 31 -13 51.5t-26 33t-36.5 17.5t-42.5 5q-23 0 -44 -7.5t-38 -22.5t-27 -37.5t-10 -52.5v-150q0 -27 9.5 -49.5t26 -38t38 -24 t46.5 -8.5q50 0 83 27t38 84h90q-5 -47 -24 -81.5t-47 -57t-63.5 -33.5t-74.5 -11q-56 0 -96.5 19.5t-67.5 55t-40 83.5t-13 105z" />
<glyph unicode="&#x108;" horiz-adv-x="642" d="M273 743h-77l83 122h103l83 -122h-78l-57 73zM340 -15q-68 0 -120 24.5t-87 70.5t-53.5 110t-18.5 144q0 83 17 150t52 114t88 72t124 25q49 0 91.5 -14.5t74.5 -41.5t51.5 -65.5t22.5 -86.5h-95q-7 60 -47 92.5t-107 32.5q-36 0 -66.5 -12.5t-53 -34.5t-35 -52.5 t-12.5 -67.5v-210q0 -36 13.5 -66.5t37 -53t55 -35t67.5 -12.5q68 0 110 30.5t49 99.5h95q-3 -48 -23.5 -87.5t-54 -67t-78.5 -43t-97 -15.5z" />
<glyph unicode="&#x109;" horiz-adv-x="534" d="M213 573h-77l83 122h103l83 -122h-78l-57 73zM57 251q0 61 14.5 111.5t42.5 85.5t69 54.5t93 19.5q40 0 75.5 -10.5t62.5 -32.5t44 -56t21 -80h-90q-3 31 -13 51.5t-26 33t-36.5 17.5t-42.5 5q-23 0 -44 -7.5t-38 -22.5t-27 -37.5t-10 -52.5v-150q0 -27 9.5 -49.5t26 -38 t38 -24t46.5 -8.5q50 0 83 27t38 84h90q-5 -47 -24 -81.5t-47 -57t-63.5 -33.5t-74.5 -11q-56 0 -96.5 19.5t-67.5 55t-40 83.5t-13 105z" />
<glyph unicode="&#x10a;" horiz-adv-x="642" d="M374 762h-88v88h88v-88zM340 -15q-68 0 -120 24.5t-87 70.5t-53.5 110t-18.5 144q0 83 17 150t52 114t88 72t124 25q49 0 91.5 -14.5t74.5 -41.5t51.5 -65.5t22.5 -86.5h-95q-7 60 -47 92.5t-107 32.5q-36 0 -66.5 -12.5t-53 -34.5t-35 -52.5t-12.5 -67.5v-210 q0 -36 13.5 -66.5t37 -53t55 -35t67.5 -12.5q68 0 110 30.5t49 99.5h95q-3 -48 -23.5 -87.5t-54 -67t-78.5 -43t-97 -15.5z" />
<glyph unicode="&#x10b;" horiz-adv-x="534" d="M312 592h-88v88h88v-88zM57 251q0 61 14.5 111.5t42.5 85.5t69 54.5t93 19.5q40 0 75.5 -10.5t62.5 -32.5t44 -56t21 -80h-90q-3 31 -13 51.5t-26 33t-36.5 17.5t-42.5 5q-23 0 -44 -7.5t-38 -22.5t-27 -37.5t-10 -52.5v-150q0 -27 9.5 -49.5t26 -38t38 -24t46.5 -8.5 q50 0 83 27t38 84h90q-5 -47 -24 -81.5t-47 -57t-63.5 -33.5t-74.5 -11q-56 0 -96.5 19.5t-67.5 55t-40 83.5t-13 105z" />
<glyph unicode="&#x10c;" horiz-adv-x="642" d="M330 792l57 73h78l-83 -122h-103l-83 122h77zM340 -15q-68 0 -120 24.5t-87 70.5t-53.5 110t-18.5 144q0 83 17 150t52 114t88 72t124 25q49 0 91.5 -14.5t74.5 -41.5t51.5 -65.5t22.5 -86.5h-95q-7 60 -47 92.5t-107 32.5q-36 0 -66.5 -12.5t-53 -34.5t-35 -52.5 t-12.5 -67.5v-210q0 -36 13.5 -66.5t37 -53t55 -35t67.5 -12.5q68 0 110 30.5t49 99.5h95q-3 -48 -23.5 -87.5t-54 -67t-78.5 -43t-97 -15.5z" />
<glyph unicode="&#x10d;" horiz-adv-x="534" d="M267 622l57 73h78l-83 -122h-103l-83 122h77zM57 251q0 61 14.5 111.5t42.5 85.5t69 54.5t93 19.5q40 0 75.5 -10.5t62.5 -32.5t44 -56t21 -80h-90q-3 31 -13 51.5t-26 33t-36.5 17.5t-42.5 5q-23 0 -44 -7.5t-38 -22.5t-27 -37.5t-10 -52.5v-150q0 -27 9.5 -49.5t26 -38 t38 -24t46.5 -8.5q50 0 83 27t38 84h90q-5 -47 -24 -81.5t-47 -57t-63.5 -33.5t-74.5 -11q-56 0 -96.5 19.5t-67.5 55t-40 83.5t-13 105z" />
<glyph unicode="&#x10e;" horiz-adv-x="667" d="M315 792l57 73h78l-83 -122h-103l-83 122h77zM187 595v-510h125q96 0 141.5 40t45.5 115v200q0 75 -45.5 115t-141.5 40h-125zM312 680q154 0 223 -84.5t69 -255.5t-69 -255.5t-223 -84.5h-220v680h220z" />
<glyph unicode="&#x10f;" horiz-adv-x="659" d="M682 570l-62 -110h-48l59 110h-59v110h110v-110zM422 0v83q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21t-63.5 57t-38 84.5t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v253h90v-680h-90zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5 t-41 -23.5t-27.5 -36.5t-10 -47.5v-158q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="&#x112;" horiz-adv-x="602" d="M173 840h289v-59h-289v59zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#x113;" horiz-adv-x="548" d="M130 670h289v-59h-289v59zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5t-66.5 -34.5t-78 -12 q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#x12a;" horiz-adv-x="279" d="M-5 840h289v-59h-289v59zM92 680h95v-680h-95v680z" />
<glyph unicode="&#x12b;" horiz-adv-x="256" d="M-16 670h289v-59h-289v59zM83 0v510h90v-510h-90z" />
<glyph unicode="&#x14c;" horiz-adv-x="706" d="M209 840h289v-59h-289v59zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340q0 -80 -17 -145.5 t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="&#x14d;" horiz-adv-x="570" d="M141 670h289v-59h-289v59zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5t-42.5 23.5t-51.5 8.5 t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#x16a;" horiz-adv-x="684" d="M198 840h289v-59h-289v59zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#x16b;" horiz-adv-x="566" d="M140 670h289v-59h-289v59zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#x114;" horiz-adv-x="602" d="M242 865q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#x115;" horiz-adv-x="548" d="M200 695q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54 q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5t-66.5 -34.5t-78 -12q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5 v-28z" />
<glyph unicode="&#x11e;" horiz-adv-x="672" d="M260 865q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM61 334q0 85 17.5 152t52.5 113.5t89 71t127 24.5q51 0 94.5 -14.5t75.5 -41.5t52 -65.5t23 -86.5h-95 q-7 60 -47.5 92.5t-111.5 32.5q-38 0 -69.5 -12.5t-54 -34.5t-35.5 -52.5t-13 -67.5v-210q0 -36 13.5 -66.5t37 -53t56 -35t70.5 -12.5q79 0 122 42t43 122v46h-154v85h249v-363h-91l-4 72q-26 -39 -67 -63t-106 -24t-116 24.5t-86 70t-53.5 110t-18.5 144.5z" />
<glyph unicode="&#x11f;" horiz-adv-x="595" d="M244 695q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM512 10q0 -53 -19 -91.5t-50 -64t-70.5 -37.5t-80.5 -12q-40 0 -76.5 8t-66 26.5t-49 49t-25.5 76.5h90 q5 -44 37.5 -66t85.5 -22q26 0 50.5 7.5t43 24t29.5 42.5t11 63v69q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21t-63.5 57t-38 84.5t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v83h90v-500zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5 t-41 -23.5t-27.5 -36.5t-10 -47.5v-158q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="&#x12c;" horiz-adv-x="279" d="M65 865q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM92 680h95v-680h-95v680z" />
<glyph unicode="&#x12d;" horiz-adv-x="256" d="M53 695q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM83 0v510h90v-510h-90z" />
<glyph unicode="&#x14e;" horiz-adv-x="706" d="M278 865q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5 t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5zM645 340q0 -80 -17 -145.5t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72 t52.5 -112t17 -145.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="570" d="M209 695q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5 t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5t-42.5 23.5t-51.5 8.5t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#x16c;" horiz-adv-x="684" d="M267 865q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438 h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#x16d;" horiz-adv-x="566" d="M210 695q0 -34 21.5 -56.5t53.5 -22.5t53.5 22.5t21.5 56.5h60q0 -28 -10.5 -52.5t-29 -43t-43 -29t-52.5 -10.5t-52.5 10.5t-43 29t-29 43t-10.5 52.5h60zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330 q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#x117;" horiz-adv-x="548" d="M318 592h-88v88h88v-88zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5t-66.5 -34.5t-78 -12 q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#x11a;" horiz-adv-x="602" d="M317 792l57 73h78l-83 -122h-103l-83 122h77zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#x11b;" horiz-adv-x="548" d="M275 622l57 73h78l-83 -122h-103l-83 122h77zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5 t-66.5 -34.5t-78 -12q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#x11c;" horiz-adv-x="672" d="M282 743h-77l83 122h103l83 -122h-78l-57 73zM61 334q0 85 17.5 152t52.5 113.5t89 71t127 24.5q51 0 94.5 -14.5t75.5 -41.5t52 -65.5t23 -86.5h-95q-7 60 -47.5 92.5t-111.5 32.5q-38 0 -69.5 -12.5t-54 -34.5t-35.5 -52.5t-13 -67.5v-210q0 -36 13.5 -66.5t37 -53 t56 -35t70.5 -12.5q79 0 122 42t43 122v46h-154v85h249v-363h-91l-4 72q-26 -39 -67 -63t-106 -24t-116 24.5t-86 70t-53.5 110t-18.5 144.5z" />
<glyph unicode="&#x11d;" horiz-adv-x="595" d="M257 573h-77l83 122h103l83 -122h-78l-57 73zM512 10q0 -53 -19 -91.5t-50 -64t-70.5 -37.5t-80.5 -12q-40 0 -76.5 8t-66 26.5t-49 49t-25.5 76.5h90q5 -44 37.5 -66t85.5 -22q26 0 50.5 7.5t43 24t29.5 42.5t11 63v69q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21 t-63.5 57t-38 84.5t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v83h90v-500zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5t-41 -23.5t-27.5 -36.5t-10 -47.5v-158q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="&#x121;" horiz-adv-x="595" d="M358 592h-88v88h88v-88zM512 10q0 -53 -19 -91.5t-50 -64t-70.5 -37.5t-80.5 -12q-40 0 -76.5 8t-66 26.5t-49 49t-25.5 76.5h90q5 -44 37.5 -66t85.5 -22q26 0 50.5 7.5t43 24t29.5 42.5t11 63v69q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21t-63.5 57t-38 84.5 t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v83h90v-500zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5t-41 -23.5t-27.5 -36.5t-10 -47.5v-158q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="&#x124;" horiz-adv-x="695" d="M291 743h-77l83 122h103l83 -122h-78l-57 73zM508 307h-321v-307h-95v680h95v-288h321v288h95v-680h-95v307z" />
<glyph unicode="&#x125;" horiz-adv-x="570" d="M71 743h-77l83 122h103l83 -122h-78l-57 73zM173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v680h90v-254z" />
<glyph unicode="&#x123;" horiz-adv-x="595" d="M259 740l62 110h48l-59 -110h59v-110h-110v110zM512 10q0 -53 -19 -91.5t-50 -64t-70.5 -37.5t-80.5 -12q-40 0 -76.5 8t-66 26.5t-49 49t-25.5 76.5h90q5 -44 37.5 -66t85.5 -22q26 0 50.5 7.5t43 24t29.5 42.5t11 63v69q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21 t-63.5 57t-38 84.5t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v83h90v-500zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5t-41 -23.5t-27.5 -36.5t-10 -47.5v-158q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="&#x128;" horiz-adv-x="279" d="M293 850q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM92 680h95v-680h-95v680z" />
<glyph unicode="&#x129;" horiz-adv-x="256" d="M282 680q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM83 0v510h90v-510h-90z" />
<glyph unicode="&#x130;" horiz-adv-x="279" d="M184 762h-88v88h88v-88zM92 680h95v-680h-95v680z" />
<glyph unicode="&#x133;" horiz-adv-x="524" d="M443 586h-94v94h94v-94zM351 510h90v-680h-208v77h118v603zM175 586h-94v94h94v-94zM83 0v510h90v-510h-90z" />
<glyph unicode="&#x132;" horiz-adv-x="704" d="M612 0h-314v85h219v595h95v-680zM92 680h95v-680h-95v680z" />
<glyph unicode="&#x134;" horiz-adv-x="425" d="M229 743h-77l83 122h103l83 -122h-78l-57 73zM333 0h-314v85h219v595h95v-680z" />
<glyph unicode="&#x135;" horiz-adv-x="256" d="M71 573h-77l83 122h103l83 -122h-78l-57 73zM83 510h90v-680h-208v77h118v603z" />
<glyph unicode="&#x139;" horiz-adv-x="513" d="M158 865h105l-106 -122h-78zM92 680h95v-595h308v-85h-403v680z" />
<glyph unicode="&#x143;" horiz-adv-x="695" d="M345 865h105l-106 -122h-78zM511 115v565h92v-680h-134l-281 568h-4v-568h-92v680h134l281 -565h4z" />
<glyph unicode="&#x144;" horiz-adv-x="570" d="M293 695h105l-106 -122h-78zM173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v510h90v-84z" />
<glyph unicode="&#x147;" horiz-adv-x="695" d="M347 792l57 73h78l-83 -122h-103l-83 122h77zM511 115v565h92v-680h-134l-281 568h-4v-568h-92v680h134l281 -565h4z" />
<glyph unicode="&#x148;" horiz-adv-x="570" d="M295 622l57 73h78l-83 -122h-103l-83 122h77zM173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v510h90v-84z" />
<glyph unicode="&#x150;" horiz-adv-x="706" d="M459 865h105l-101 -122h-78zM296 865h105l-101 -122h-78zM353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 39 -14.5 69.5t-40 52.5t-59.5 33.5t-73 11.5t-73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210q0 -39 14.5 -69.5t40 -52.5t59.5 -33.5t73 -11.5z M645 340q0 -80 -17 -145.5t-52.5 -112t-90.5 -72t-132 -25.5t-132 25.5t-90.5 72t-52.5 112t-17 145.5t17 145.5t52.5 112t90.5 72t132 25.5t132 -25.5t90.5 -72t52.5 -112t17 -145.5z" />
<glyph unicode="&#x151;" horiz-adv-x="570" d="M391 695h105l-101 -122h-78zM228 695h105l-101 -122h-78zM514 255q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20t-101 20t-71.5 55t-42.5 84t-14 108q0 58 14 107.5t42.5 84.5t71.5 55t101 20t101 -20t71.5 -55t42.5 -84.5t14 -107.5zM419 334q0 26 -11 47.5t-29 36.5 t-42.5 23.5t-51.5 8.5t-51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -26 11 -47.5t29 -36.5t42.5 -23.5t51.5 -8.5t51.5 8.5t42.5 23.5t29 36.5t11 47.5v158z" />
<glyph unicode="&#x155;" horiz-adv-x="357" d="M163 695h105l-106 -122h-78zM83 510h245v-78h-155v-432h-90v510z" />
<glyph unicode="&#x154;" horiz-adv-x="623" d="M335 865h105l-106 -122h-78zM383 368q38 0 58 23t20 55v75q0 35 -21 54.5t-57 19.5h-196v-227h196zM187 287v-287h-95v680h256q53 0 94 -12t68 -37t39 -61.5t12 -84.5q0 -45 -11.5 -81.5t-37.5 -62.5q-20 -20 -46.5 -33t-66.5 -18l168 -290h-109l-159 287h-112z" />
<glyph unicode="&#x158;" horiz-adv-x="623" d="M307 792l57 73h78l-83 -122h-103l-83 122h77zM383 368q38 0 58 23t20 55v75q0 35 -21 54.5t-57 19.5h-196v-227h196zM187 287v-287h-95v680h256q53 0 94 -12t68 -37t39 -61.5t12 -84.5q0 -45 -11.5 -81.5t-37.5 -62.5q-20 -20 -46.5 -33t-66.5 -18l168 -290h-109 l-159 287h-112z" />
<glyph unicode="&#x159;" horiz-adv-x="357" d="M205 622l57 73h78l-83 -122h-103l-83 122h77zM83 510h245v-78h-155v-432h-90v510z" />
<glyph unicode="&#x15a;" horiz-adv-x="616" d="M323 865h105l-106 -122h-78zM303 612q-65 0 -102.5 -26t-37.5 -71q0 -48 26 -71t83 -39l109 -30q44 -12 78.5 -28t58 -39.5t35.5 -54.5t12 -73q0 -45 -20.5 -81t-55 -61.5t-80.5 -39t-97 -13.5t-97 12.5t-82 38.5t-59 66t-27 96h95q8 -69 54 -99.5t114 -30.5q34 0 63 7 t50.5 21t34 34.5t12.5 47.5q0 44 -24.5 69t-78.5 40l-133 37q-84 23 -124 69.5t-40 121.5q0 38 16.5 71t47.5 57t73.5 38t95.5 14q49 0 91.5 -11.5t75 -36t53 -62.5t25.5 -90h-95q-3 34 -16.5 56.5t-34 36t-46 19t-53.5 5.5z" />
<glyph unicode="&#x15b;" horiz-adv-x="509" d="M263 695h105l-106 -122h-78zM309 289q76 -16 113 -52t37 -98q0 -32 -14.5 -59.5t-41.5 -47.5t-64 -32t-82 -12q-43 0 -80.5 11t-65.5 32.5t-44 54t-17 75.5h90q2 -51 36.5 -76t81.5 -25q45 0 78 20t33 58q0 31 -20 49.5t-71 28.5l-84 17q-63 13 -97 48.5t-34 95.5 q0 30 13.5 56.5t38.5 46t59 31t76 11.5q36 0 70.5 -9.5t61.5 -29.5t43.5 -51t17.5 -74h-90q-2 51 -31.5 71.5t-70.5 20.5q-42 0 -71 -20t-29 -52q0 -30 17 -46.5t55 -24.5z" />
<glyph unicode="&#x15c;" horiz-adv-x="616" d="M249 743h-77l83 122h103l83 -122h-78l-57 73zM303 612q-65 0 -102.5 -26t-37.5 -71q0 -48 26 -71t83 -39l109 -30q44 -12 78.5 -28t58 -39.5t35.5 -54.5t12 -73q0 -45 -20.5 -81t-55 -61.5t-80.5 -39t-97 -13.5t-97 12.5t-82 38.5t-59 66t-27 96h95q8 -69 54 -99.5 t114 -30.5q34 0 63 7t50.5 21t34 34.5t12.5 47.5q0 44 -24.5 69t-78.5 40l-133 37q-84 23 -124 69.5t-40 121.5q0 38 16.5 71t47.5 57t73.5 38t95.5 14q49 0 91.5 -11.5t75 -36t53 -62.5t25.5 -90h-95q-3 34 -16.5 56.5t-34 36t-46 19t-53.5 5.5z" />
<glyph unicode="&#x15d;" horiz-adv-x="509" d="M189 573h-77l83 122h103l83 -122h-78l-57 73zM309 289q76 -16 113 -52t37 -98q0 -32 -14.5 -59.5t-41.5 -47.5t-64 -32t-82 -12q-43 0 -80.5 11t-65.5 32.5t-44 54t-17 75.5h90q2 -51 36.5 -76t81.5 -25q45 0 78 20t33 58q0 31 -20 49.5t-71 28.5l-84 17q-63 13 -97 48.5 t-34 95.5q0 30 13.5 56.5t38.5 46t59 31t76 11.5q36 0 70.5 -9.5t61.5 -29.5t43.5 -51t17.5 -74h-90q-2 51 -31.5 71.5t-70.5 20.5q-42 0 -71 -20t-29 -52q0 -30 17 -46.5t55 -24.5z" />
<glyph unicode="&#x164;" horiz-adv-x="541" d="M270 792l57 73h78l-83 -122h-103l-83 122h77zM20 680h501v-85h-203v-595h-95v595h-203v85z" />
<glyph unicode="&#x168;" horiz-adv-x="684" d="M496 850q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18 t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#x169;" horiz-adv-x="566" d="M438 680q0 -45 -23.5 -70t-65.5 -25q-21 0 -39.5 7.5t-35.5 16.5t-32 16.5t-28 7.5q-33 0 -33 -38h-50q0 45 23.5 70t65.5 25q21 0 39.5 -7.5t35.5 -16.5t32 -16.5t28 -7.5q33 0 33 38h50zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5 t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#x16e;" horiz-adv-x="684" d="M386 821q0 18 -12 31t-32 13t-32.5 -13t-12.5 -31t12.5 -31.5t32.5 -13.5t32 13.5t12 31.5zM431 821q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18 t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#x16f;" horiz-adv-x="566" d="M329 651q0 18 -12 31t-32 13t-32.5 -13t-12.5 -31t12.5 -31.5t32.5 -13.5t32 13.5t12 31.5zM374 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32 t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#x170;" horiz-adv-x="684" d="M448 865h105l-101 -122h-78zM285 865h105l-101 -122h-78zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#x171;" horiz-adv-x="566" d="M391 695h105l-101 -122h-78zM228 695h105l-101 -122h-78zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#x174;" horiz-adv-x="932" d="M411 743h-77l83 122h103l83 -122h-78l-57 73zM652 107h4l133 573h101l-176 -680h-119l-127 547h-4l-127 -547h-119l-176 680h101l133 -573h4l133 573h106z" />
<glyph unicode="&#x175;" horiz-adv-x="749" d="M314 573h-77l83 122h103l83 -122h-78l-57 73zM372 413l-97 -413h-93l-149 510h91l101 -399h4l90 399h109l94 -399h4l102 399h88l-149 -510h-95l-96 413h-4z" />
<glyph unicode="&#x176;" horiz-adv-x="589" d="M238 743h-77l83 122h103l83 -122h-78l-57 73zM21 680h104l170 -316l173 316h101l-227 -410v-270h-95v271z" />
<glyph unicode="&#x177;" horiz-adv-x="521" d="M205 573h-77l83 122h103l83 -122h-78l-57 73zM264 111l137 399h93l-250 -680h-174v77h117l32 93l-189 510h97l133 -399h4z" />
<glyph unicode="&#x179;" horiz-adv-x="593" d="M321 865h105l-106 -122h-78zM537 595l-379 -510h378v-85h-487v85l380 510h-364v85h472v-85z" />
<glyph unicode="&#x17a;" horiz-adv-x="486" d="M267 695h105l-106 -122h-78zM432 77v-77h-384v77l274 356h-254v77h358v-77l-274 -356h280z" />
<glyph unicode="&#x17b;" horiz-adv-x="593" d="M338 762h-88v88h88v-88zM537 595l-379 -510h378v-85h-487v85l380 510h-364v85h472v-85z" />
<glyph unicode="&#x17c;" horiz-adv-x="486" d="M284 592h-88v88h88v-88zM432 77v-77h-384v77l274 356h-254v77h358v-77l-274 -356h280z" />
<glyph unicode="&#x1e81;" horiz-adv-x="749" d="M434 573h-78l-106 122h105zM372 413l-97 -413h-93l-149 510h91l101 -399h4l90 399h109l94 -399h4l102 399h88l-149 -510h-95l-96 413h-4z" />
<glyph unicode="&#x1e83;" horiz-adv-x="749" d="M389 695h105l-106 -122h-78zM372 413l-97 -413h-93l-149 510h91l101 -399h4l90 399h109l94 -399h4l102 399h88l-149 -510h-95l-96 413h-4z" />
<glyph unicode="&#x1e85;" horiz-adv-x="749" d="M504 592h-88v88h88v-88zM328 592h-88v88h88v-88zM372 413l-97 -413h-93l-149 510h91l101 -399h4l90 399h109l94 -399h4l102 399h88l-149 -510h-95l-96 413h-4z" />
<glyph unicode="&#x1ef3;" horiz-adv-x="521" d="M316 573h-78l-106 122h105zM264 111l137 399h93l-250 -680h-174v77h117l32 93l-189 510h97l133 -399h4z" />
<glyph unicode="&#x1e80;" horiz-adv-x="932" d="M530 743h-78l-106 122h105zM652 107h4l133 573h101l-176 -680h-119l-127 547h-4l-127 -547h-119l-176 680h101l133 -573h4l133 573h106z" />
<glyph unicode="&#x1e82;" horiz-adv-x="932" d="M485 865h105l-106 -122h-78zM652 107h4l133 573h101l-176 -680h-119l-127 547h-4l-127 -547h-119l-176 680h101l133 -573h4l133 573h106z" />
<glyph unicode="&#x1e84;" horiz-adv-x="932" d="M600 762h-88v88h88v-88zM424 762h-88v88h88v-88zM652 107h4l133 573h101l-176 -680h-119l-127 547h-4l-127 -547h-119l-176 680h101l133 -573h4l133 573h106z" />
<glyph unicode="&#x1ef2;" horiz-adv-x="589" d="M347 743h-78l-106 122h105zM21 680h104l170 -316l173 316h101l-227 -410v-270h-95v271z" />
<glyph unicode="&#x1fc;" horiz-adv-x="942" d="M464 865h105l-106 -122h-78zM225 247h217v348h-48zM442 0v169h-255l-82 -169h-106l335 680h549v-85h-346v-206h300v-85h-300v-219h346v-85h-441z" />
<glyph unicode="&#x13a;" horiz-adv-x="260" d="M146 865h105l-106 -122h-78zM85 0v680h90v-680h-90z" />
<glyph unicode="&#x14a;" horiz-adv-x="695" d="M509 171v509h94v-850h-314v85h219v85l-318 510h-4v-510h-94v680h95l318 -509h4z" />
<glyph unicode="&#x14b;" horiz-adv-x="570" d="M491 -170h-208v77h118v423q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v510h90v-84q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-510z" />
<glyph unicode="&#x165;" horiz-adv-x="449" d="M480 570l-62 -110h-48l59 110h-59v110h110v-110zM209 77h117v-77h-206v433h-86v77h86v170h89v-170h117v-77h-117v-356z" />
<glyph unicode="&#x1fe;" horiz-adv-x="706" d="M353 68q39 0 73 11.5t59.5 33.5t40 52.5t14.5 69.5v210q0 64 -41 108l-233 -468q40 -17 87 -17zM166 235q0 -36 12.5 -65t35.5 -51l234 472q-21 10 -45 15.5t-50 5.5q-39 0 -73 -11.5t-59.5 -33.5t-40 -52.5t-14.5 -69.5v-210zM645 340q0 -80 -17 -145.5t-52.5 -112 t-90.5 -72t-132 -25.5q-36 0 -67.5 5.5t-58.5 16.5l-37 -75l-56 27l39 79q-58 45 -85 122.5t-27 179.5q0 80 17 145.5t52.5 112t90.5 72t132 25.5q39 0 72 -7t62 -20l40 80l56 -27l-43 -86q54 -46 79.5 -121.5t25.5 -173.5zM375 880h105l-106 -122h-78z" />
<glyph unicode="&#x1ff;" horiz-adv-x="570" d="M230 69q25 -9 55 -9q27 0 51.5 8.5t42.5 23.5t29 36.5t11 47.5v158q0 20 -7 38t-19 32zM341 440q-28 10 -56 10q-27 0 -51.5 -8.5t-42.5 -23.5t-29 -36.5t-11 -47.5v-158q0 -21 7 -39t19 -32zM142 33q-44 35 -65 92t-21 130q0 58 14 107.5t42.5 84.5t71.5 55t101 20 q25 0 47 -4t42 -11l36 74l56 -27l-38 -78q44 -35 65 -92t21 -129q0 -59 -14 -108t-42.5 -84t-71.5 -55t-101 -20q-49 0 -88 14l-36 -73l-56 27zM309 695h105l-106 -122h-78z" />
<glyph unicode="&#x1fd;" horiz-adv-x="847" d="M456 695h105l-106 -122h-78zM407 74q-30 -42 -74.5 -64t-101.5 -22q-53 0 -84 11.5t-53 34.5q-20 20 -31 49t-11 67q0 30 10.5 57.5t30 48.5t46.5 33.5t60 12.5h171v47q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66 t42 52t62 34t76.5 12q54 0 95.5 -16.5t67.5 -51.5q28 32 66 50t86 18q51 0 90.5 -19t66.5 -52t41.5 -77.5t14.5 -95.5v-48h-333v-54q0 -28 10.5 -49.5t28 -36.5t39 -22.5t43.5 -7.5q45 0 79 24.5t39 77.5h90q-3 -41 -21.5 -73t-47 -54.5t-65 -34.5t-75.5 -12q-59 0 -100 22 t-71 64h-2zM460 302h240v28q0 28 -10 50.5t-26.5 38t-38.5 23.5t-45 8t-45 -8t-38.5 -23.5t-26.5 -38t-10 -50.5v-28zM239 60q61 0 96 43.5t35 112.5v14h-181q-20 0 -31 -12.5t-11 -34.5v-55q0 -35 24.5 -51.5t67.5 -16.5z" />
<glyph unicode="&#x110;" horiz-adv-x="674" d="M350 304h-156v-219h125q96 0 141.5 40t45.5 115v200q0 75 -45.5 115t-141.5 40h-125v-219h156v-72zM319 680q154 0 223 -84.5t69 -255.5t-69 -255.5t-223 -84.5h-220v304h-72v72h72v304h220z" />
<glyph unicode="&#x111;" horiz-adv-x="595" d="M422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5t-41 -23.5t-27.5 -36.5t-10 -47.5v-158q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86zM272 622h150v58h90v-58h93v-60h-93v-562h-90v83q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21t-63.5 57 t-38 84.5t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v135h-150v60z" />
<glyph unicode="&#x13d;" horiz-adv-x="514" d="M392 570l-62 -110h-48l59 110h-59v110h110v-110zM92 680h95v-595h308v-85h-403v680z" />
<glyph unicode="&#x13e;" horiz-adv-x="322" d="M345 570l-62 -110h-48l59 110h-59v110h110v-110zM85 0v680h90v-680h-90z" />
<glyph unicode="&#x140;" horiz-adv-x="320" d="M345 400h-110v110h110v-110zM85 0v680h90v-680h-90z" />
<glyph unicode="&#x13f;" horiz-adv-x="514" d="M396 400h-110v110h110v-110zM92 680h95v-595h308v-85h-403v680z" />
<glyph unicode="&#x149;" horiz-adv-x="570" d="M173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v510h90v-84zM133 680l-50 -88h-38l47 88h-47v88h88v-88z" />
<glyph unicode="&#xf6c3;" horiz-adv-x="300" d="M194 -150l-50 -88h-38l47 88h-47v88h88v-88z" />
<glyph unicode="&#x122;" horiz-adv-x="672" d="M369 -150l-50 -88h-38l47 88h-47v88h88v-88zM61 334q0 85 17.5 152t52.5 113.5t89 71t127 24.5q51 0 94.5 -14.5t75.5 -41.5t52 -65.5t23 -86.5h-95q-7 60 -47.5 92.5t-111.5 32.5q-38 0 -69.5 -12.5t-54 -34.5t-35.5 -52.5t-13 -67.5v-210q0 -36 13.5 -66.5t37 -53 t56 -35t70.5 -12.5q79 0 122 42t43 122v46h-154v85h249v-363h-91l-4 72q-26 -39 -67 -63t-106 -24t-116 24.5t-86 70t-53.5 110t-18.5 144.5z" />
<glyph unicode="&#x136;" horiz-adv-x="629" d="M344 -150l-50 -88h-38l47 88h-47v88h88v-88zM189 363l282 317h116l-287 -317l312 -363h-116zM187 0h-95v680h95v-680z" />
<glyph unicode="&#x137;" horiz-adv-x="522" d="M299 -150l-50 -88h-38l47 88h-47v88h88v-88zM173 0h-90v680h90v-680zM174 285l200 225h109l-205 -225l219 -285h-103z" />
<glyph unicode="&#x13b;" horiz-adv-x="513" d="M305 -150l-50 -88h-38l47 88h-47v88h88v-88zM92 680h95v-595h308v-85h-403v680z" />
<glyph unicode="&#x13c;" horiz-adv-x="260" d="M174 -150l-50 -88h-38l47 88h-47v88h88v-88zM85 0v680h90v-680h-90z" />
<glyph unicode="&#x145;" horiz-adv-x="695" d="M391 -150l-50 -88h-38l47 88h-47v88h88v-88zM511 115v565h92v-680h-134l-281 568h-4v-568h-92v680h134l281 -565h4z" />
<glyph unicode="&#x146;" horiz-adv-x="570" d="M329 -150l-50 -88h-38l47 88h-47v88h88v-88zM173 426q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v510h90v-84z" />
<glyph unicode="&#x156;" horiz-adv-x="623" d="M351 -150l-50 -88h-38l47 88h-47v88h88v-88zM383 368q38 0 58 23t20 55v75q0 35 -21 54.5t-57 19.5h-196v-227h196zM187 287v-287h-95v680h256q53 0 94 -12t68 -37t39 -61.5t12 -84.5q0 -45 -11.5 -81.5t-37.5 -62.5q-20 -20 -46.5 -33t-66.5 -18l168 -290h-109l-159 287 h-112z" />
<glyph unicode="&#x157;" horiz-adv-x="357" d="M172 -150l-50 -88h-38l47 88h-47v88h88v-88zM83 510h245v-78h-155v-432h-90v510z" />
<glyph unicode="&#x15e;" horiz-adv-x="616" d="M206 -105h70v70h70v-135h-140v65zM303 612q-65 0 -102.5 -26t-37.5 -71q0 -48 26 -71t83 -39l109 -30q44 -12 78.5 -28t58 -39.5t35.5 -54.5t12 -73q0 -45 -20.5 -81t-55 -61.5t-80.5 -39t-97 -13.5t-97 12.5t-82 38.5t-59 66t-27 96h95q8 -69 54 -99.5t114 -30.5 q34 0 63 7t50.5 21t34 34.5t12.5 47.5q0 44 -24.5 69t-78.5 40l-133 37q-84 23 -124 69.5t-40 121.5q0 38 16.5 71t47.5 57t73.5 38t95.5 14q49 0 91.5 -11.5t75 -36t53 -62.5t25.5 -90h-95q-3 34 -16.5 56.5t-34 36t-46 19t-53.5 5.5z" />
<glyph unicode="&#x15f;" horiz-adv-x="509" d="M146 -105h70v70h70v-135h-140v65zM309 289q76 -16 113 -52t37 -98q0 -32 -14.5 -59.5t-41.5 -47.5t-64 -32t-82 -12q-43 0 -80.5 11t-65.5 32.5t-44 54t-17 75.5h90q2 -51 36.5 -76t81.5 -25q45 0 78 20t33 58q0 31 -20 49.5t-71 28.5l-84 17q-63 13 -97 48.5t-34 95.5 q0 30 13.5 56.5t38.5 46t59 31t76 11.5q36 0 70.5 -9.5t61.5 -29.5t43.5 -51t17.5 -74h-90q-2 51 -31.5 71.5t-70.5 20.5q-42 0 -71 -20t-29 -52q0 -30 17 -46.5t55 -24.5z" />
<glyph unicode="&#x163;" horiz-adv-x="368" d="M117 -105h70v70h70v-135h-140v65zM208 77h118v-77h-206v433h-86v77h86v170h88v-170h118v-77h-118v-356z" />
<glyph unicode="&#x162;" horiz-adv-x="541" d="M165 -105h70v70h70v-135h-140v65zM20 680h501v-85h-203v-595h-95v595h-203v85z" />
<glyph unicode="&#x21b;" horiz-adv-x="368" d="M266 -150l-50 -88h-38l47 88h-47v88h88v-88zM209 77h117v-77h-206v433h-86v77h86v170h89v-170h117v-77h-117v-356z" />
<glyph unicode="&#x21a;" horiz-adv-x="541" d="M314 -150l-50 -88h-38l47 88h-47v88h88v-88zM20 680h501v-85h-203v-595h-95v595h-203v85z" />
<glyph unicode="&#x218;" horiz-adv-x="616" d="M303 612q-65 0 -102.5 -26t-37.5 -71q0 -48 26 -71t83 -39l109 -30q44 -12 78.5 -28t58 -39.5t35.5 -54.5t12 -73q0 -45 -20.5 -81t-55 -61.5t-80.5 -39t-97 -13.5t-97 12.5t-82 38.5t-59 66t-27 96h95q8 -69 54 -99.5t114 -30.5q34 0 63 7t50.5 21t34 34.5t12.5 47.5 q0 44 -24.5 69t-78.5 40l-133 37q-84 23 -124 69.5t-40 121.5q0 38 16.5 71t47.5 57t73.5 38t95.5 14q49 0 91.5 -11.5t75 -36t53 -62.5t25.5 -90h-95q-3 34 -16.5 56.5t-34 36t-46 19t-53.5 5.5zM353 -150l-50 -88h-38l47 88h-47v88h88v-88z" />
<glyph unicode="&#x219;" horiz-adv-x="509" d="M309 289q76 -16 113 -52t37 -98q0 -32 -14.5 -59.5t-41.5 -47.5t-64 -32t-82 -12q-43 0 -80.5 11t-65.5 32.5t-44 54t-17 75.5h90q2 -51 36.5 -76t81.5 -25q45 0 78 20t33 58q0 31 -20 49.5t-71 28.5l-84 17q-63 13 -97 48.5t-34 95.5q0 30 13.5 56.5t38.5 46t59 31 t76 11.5q36 0 70.5 -9.5t61.5 -29.5t43.5 -51t17.5 -74h-90q-2 51 -31.5 71.5t-70.5 20.5q-42 0 -71 -20t-29 -52q0 -30 17 -47t55 -24zM299 -150l-50 -88h-38l47 88h-47v88h88v-88z" />
<glyph unicode="&#x2219;" horiz-adv-x="262" d="M186 201h-110v110h110v-110z" />
<glyph unicode="&#x22c5;" d="M355 201h-110v110h110v-110z" />
<glyph horiz-adv-x="436" d="M78 375h280v-82h-280v82z" />
<glyph unicode="&#x126;" horiz-adv-x="717" d="M519 392v85h-321v-85h321zM31 549h72v131h95v-131h321v131h95v-131h72v-72h-72v-477h-95v307h-321v-307h-95v477h-72v72z" />
<glyph unicode="&#x127;" horiz-adv-x="570" d="M173 562v-136q21 45 57.5 70.5t94.5 25.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-90v330q0 56 -21.5 88t-79.5 32q-28 0 -51 -11t-40 -31.5t-26.5 -48.5t-9.5 -61v-298h-90v562h-93v60h93v58h90v-58h150v-60h-150z" />
<glyph unicode="&#x138;" horiz-adv-x="522" d="M173 0h-90v510h90v-510zM174 285l196 225h113l-205 -225l219 -285h-103z" />
<glyph unicode="&#x167;" horiz-adv-x="376" d="M215 77h117v-77h-206v225h-86v60h86v148h-86v77h86v170h89v-170h117v-77h-117v-148h117v-60h-117v-148z" />
<glyph unicode="&#x166;" horiz-adv-x="573" d="M83 376h156v219h-203v85h501v-85h-203v-219h156v-72h-156v-304h-95v304h-156v72z" />
<glyph unicode="&#x1fb;" horiz-adv-x="551" d="M297 914h105l-106 -122h-78zM304 651q0 18 -12 31t-32 13t-32.5 -13t-12.5 -31t12.5 -31.5t32.5 -13.5t32 13.5t12 31.5zM349 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM238 60q61 0 96 43.5t35 112.5v23 l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5 t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph unicode="&#x1fa;" horiz-adv-x="631" d="M354 1064h105l-106 -122h-78zM359 811q0 18 -12 31t-32 13t-32.5 -13t-12.5 -31t12.5 -31.5t32.5 -13.5t32 13.5t12 31.5zM404 811q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM206 246h217l-106 328h-4zM448 168 h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#x2db;" horiz-adv-x="300" d="M220 -170h-140v65l70 70h65l-65 -70h70v-65z" />
<glyph unicode="&#x119;" horiz-adv-x="548" d="M319 -170h-140v65l70 70h65l-65 -70h70v-65zM56 255q0 58 14.5 107.5t43 84.5t69 55t93.5 20q55 0 96 -19t68 -52t40.5 -77.5t13.5 -95.5v-48h-343v-54q0 -28 10.5 -49.5t28.5 -36.5t40.5 -22.5t46.5 -7.5q48 0 83 24.5t40 77.5h90q-3 -41 -21.5 -73t-48 -54.5 t-66.5 -34.5t-78 -12q-55 0 -96.5 20t-69 55t-41 84t-13.5 108zM151 302h250v28q0 28 -10 50.5t-27.5 38t-40 23.5t-47.5 8t-47.5 -8t-40 -23.5t-27.5 -38t-10 -50.5v-28z" />
<glyph unicode="&#x118;" horiz-adv-x="602" d="M362 -170h-140v65l70 70h65l-65 -70h70v-65zM92 680h451v-85h-356v-206h310v-85h-310v-219h356v-85h-451v680z" />
<glyph unicode="&#x12e;" horiz-adv-x="279" d="M177 -170h-140v65l70 70h65l-65 -70h70v-65zM92 680h95v-680h-95v680z" />
<glyph unicode="&#x12f;" horiz-adv-x="256" d="M168 -170h-140v65l70 70h65l-65 -70h70v-65zM175 586h-94v94h94v-94zM83 0v510h90v-510h-90z" />
<glyph unicode="&#x172;" horiz-adv-x="684" d="M380 -170h-140v65l70 70h65l-65 -70h70v-65zM504 680h94v-438q0 -60 -18 -107.5t-51.5 -80.5t-81 -51t-105.5 -18t-105.5 18t-81 51t-51.5 80.5t-18 107.5v438h95v-445q0 -78 41.5 -122.5t119.5 -44.5t120 44.5t42 122.5v445z" />
<glyph unicode="&#x173;" horiz-adv-x="566" d="M475 -170h-140v65l70 70h65l-65 -70h70v-65zM393 84q-21 -45 -56 -70.5t-93 -25.5q-35 0 -65.5 10t-52.5 32t-34.5 56.5t-12.5 83.5v340h90v-330q0 -55 21.5 -87.5t78.5 -32.5t90.5 42.5t33.5 109.5v298h90v-510h-90v84z" />
<glyph unicode="&#x104;" horiz-adv-x="631" d="M573 -170h-140v65l70 70h65l-65 -70h70v-65zM206 246h217l-106 328h-4zM448 168h-267l-56 -168h-98l235 680h106l236 -680h-100z" />
<glyph unicode="&#x105;" horiz-adv-x="551" d="M448 -170h-140v65l70 70h65l-65 -70h70v-65zM238 60q61 0 96 43.5t35 112.5v23l-181 -17q-28 -2 -35 -14.5t-7 -34.5v-45q0 -35 24.5 -51.5t67.5 -16.5zM533 77v-77h-152l-2 94h-4q-21 -54 -62 -80t-98 -26q-38 0 -69 11.5t-53 34.5q-20 20 -31 48t-11 58q0 60 38 103 t114 50l166 15v41q0 30 -7.5 49.5t-21.5 31t-33.5 16t-43.5 4.5q-48 0 -76.5 -21.5t-28.5 -70.5h-90q0 36 15.5 66t42 52t62 34t76.5 12t76.5 -10.5t61.5 -31.5t40.5 -54t14.5 -78v-271h76z" />
<glyph horiz-adv-x="214" d="M112 484q0 -42 20.5 -86t52.5 -87h-63q-35 45 -57 98t-22 130q0 76 22 129t57 98h63q-35 -47 -54 -89t-19 -84v-109z" />
<glyph horiz-adv-x="213" d="M102 593q0 42 -19.5 84t-54.5 89h63q35 -45 57 -98t22 -129q0 -77 -22 -130.5t-57 -97.5h-63q33 43 53.5 87t20.5 86v109z" />
<glyph horiz-adv-x="368" d="M184 464q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM322 591q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58t42.5 38.5t65.5 14t65.5 -14 t42.5 -38.5t23 -58t7 -72.5z" />
<glyph horiz-adv-x="287" d="M216 416h-70v234h-111v56h111v60h70v-350z" />
<glyph horiz-adv-x="350" d="M138 478v-2h166v-60h-257v60l144 130q17 16 27 29.5t10 34.5t-14 34t-38 13q-26 0 -41.5 -14.5t-15.5 -43.5h-70q0 54 36 84.5t93 30.5t88 -29.5t31 -73.5q0 -38 -16.5 -62.5t-41.5 -45.5z" />
<glyph horiz-adv-x="357" d="M188 682l-91 -132h93v132h-2zM190 416v78h-154v56l148 216h74v-216h63v-56h-63v-78h-68z" />
<glyph horiz-adv-x="348" d="M51 564l14 202h219v-53h-157l-6 -94q12 15 30 24t41 9q54 0 84.5 -32.5t30.5 -85.5q0 -59 -34.5 -92.5t-95.5 -33.5q-63 0 -95 28.5t-37 78.5h70q3 -29 19 -41.5t42 -12.5t42 14t16 35v45q0 23 -17 33.5t-40 10.5q-17 0 -33 -9t-23 -26h-70z" />
<glyph horiz-adv-x="364" d="M183 463q29 0 46 15t17 40v28q0 24 -17 36.5t-46 12.5t-46.5 -12.5t-17.5 -36.5v-28q0 -25 17.5 -40t46.5 -15zM183 408q-137 0 -137 176q0 41 8 76t25 60.5t43 39.5t63 14q56 0 88 -26.5t37 -71.5h-70q-6 43 -56 43q-29 0 -47 -19.5t-18 -49.5v-39q13 17 33 27t47 10 q54 0 87 -32t33 -83q0 -24 -9 -47t-26 -40t-42.5 -27.5t-58.5 -10.5z" />
<glyph horiz-adv-x="314" d="M139 416h-76l148 294h-175v56h250v-56z" />
<glyph horiz-adv-x="364" d="M118 503q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM237 682q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM182 408q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5z" />
<glyph horiz-adv-x="363" d="M180 719q-29 0 -46 -15t-17 -40v-28q0 -24 17 -36.5t46 -12.5t46.5 12.5t17.5 36.5v28q0 25 -17.5 40t-46.5 15zM180 774q137 0 137 -176q0 -41 -8 -76t-25 -60.5t-43 -39.5t-63 -14q-56 0 -88 26.5t-37 71.5h70q6 -43 56 -43q29 0 47 19.5t18 49.5v39q-13 -17 -33 -27 t-47 -10q-54 0 -87 32t-33 83q0 24 9 47t26 40t42.5 27.5t58.5 10.5z" />
<glyph horiz-adv-x="368" d="M184 48q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM322 175q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58t42.5 38.5t65.5 14t65.5 -14 t42.5 -38.5t23 -58t7 -72.5z" />
<glyph horiz-adv-x="319" d="M216 0h-70v234h-111v56h111v60h70v-350z" />
<glyph horiz-adv-x="350" d="M138 62v-2h166v-60h-257v60l144 130q17 16 27 29.5t10 34.5t-14 34t-38 13q-26 0 -41.5 -14.5t-15.5 -43.5h-70q0 54 36 84.5t93 30.5t88 -29.5t31 -73.5q0 -38 -16.5 -62.5t-41.5 -45.5z" />
<glyph horiz-adv-x="357" d="M188 266l-91 -132h93v132h-2zM190 0v78h-154v56l148 216h74v-216h63v-56h-63v-78h-68z" />
<glyph horiz-adv-x="348" d="M51 148l14 202h219v-53h-157l-6 -94q12 15 30 24t41 9q54 0 84.5 -32.5t30.5 -85.5q0 -59 -34.5 -92.5t-95.5 -33.5q-63 0 -95 28.5t-37 78.5h70q3 -29 19 -41.5t42 -12.5t42 14t16 35v45q0 23 -17 33.5t-40 10.5q-17 0 -33 -9t-23 -26h-70z" />
<glyph horiz-adv-x="364" d="M183 47q29 0 46 15t17 40v28q0 24 -17 36.5t-46 12.5t-46.5 -12.5t-17.5 -36.5v-28q0 -25 17.5 -40t46.5 -15zM183 -8q-137 0 -137 176q0 41 8 76t25 60.5t43 39.5t63 14q56 0 88 -26.5t37 -71.5h-70q-6 43 -56 43q-29 0 -47 -19.5t-18 -49.5v-39q13 17 33 27t47 10 q54 0 87 -32t33 -83q0 -24 -9 -47t-26 -40t-42.5 -27.5t-58.5 -10.5z" />
<glyph horiz-adv-x="314" d="M139 0h-76l148 294h-175v56h250v-56z" />
<glyph horiz-adv-x="364" d="M118 87q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM237 266q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM182 -8q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5z" />
<glyph horiz-adv-x="363" d="M180 303q-29 0 -46 -15t-17 -40v-28q0 -24 17 -36.5t46 -12.5t46.5 12.5t17.5 36.5v28q0 25 -17.5 40t-46.5 15zM180 358q137 0 137 -176q0 -41 -8 -76t-25 -60.5t-43 -39.5t-63 -14q-56 0 -88 26.5t-37 71.5h70q6 -43 56 -43q29 0 47 19.5t18 49.5v39q-13 -17 -33 -27 t-47 -10q-54 0 -87 32t-33 83q0 24 9 47t26 40t42.5 27.5t58.5 10.5z" />
<glyph horiz-adv-x="368" d="M184 -58q30 0 46.5 15.5t16.5 45.5v132q0 30 -16.5 45.5t-46.5 15.5t-46.5 -15.5t-16.5 -45.5v-132q0 -30 16.5 -45.5t46.5 -15.5zM322 69q0 -39 -7 -72.5t-23 -58t-42.5 -38.5t-65.5 -14t-65.5 14t-42.5 38.5t-23 58t-7 72.5t7 72.5t23 58t42.5 38.5t65.5 14t65.5 -14 t42.5 -38.5t23 -58t7 -72.5z" />
<glyph horiz-adv-x="288" d="M217 -106h-70v234h-111v56h111v60h70v-350z" />
<glyph horiz-adv-x="350" d="M138 -44v-2h166v-60h-257v60l144 130q17 16 27 29.5t10 34.5t-14 34t-38 13q-26 0 -41.5 -14.5t-15.5 -43.5h-70q0 54 36 84.5t93 30.5t88 -29.5t31 -73.5q0 -38 -16.5 -62.5t-41.5 -45.5z" />
<glyph horiz-adv-x="357" d="M188 160l-91 -132h93v132h-2zM190 -106v78h-154v56l148 216h74v-216h63v-56h-63v-78h-68z" />
<glyph horiz-adv-x="350" d="M52 42l14 202h219v-53h-157l-6 -94q12 15 30 24t41 9q54 0 84.5 -32.5t30.5 -85.5q0 -59 -34.5 -92.5t-95.5 -33.5q-63 0 -95 28.5t-37 78.5h70q3 -29 19 -41.5t42 -12.5t42 14t16 35v45q0 23 -17 33.5t-40 10.5q-17 0 -33 -9t-23 -26h-70z" />
<glyph horiz-adv-x="363" d="M183 -59q29 0 46 15t17 40v28q0 24 -17 36.5t-46 12.5t-46.5 -12.5t-17.5 -36.5v-28q0 -25 17.5 -40t46.5 -15zM183 -114q-137 0 -137 176q0 41 8 76t25 60.5t43 39.5t63 14q56 0 88 -26.5t37 -71.5h-70q-6 43 -56 43q-29 0 -47 -19.5t-18 -49.5v-39q13 17 33 27t47 10 q54 0 87 -32t33 -83q0 -24 -9 -47t-26 -40t-42.5 -27.5t-58.5 -10.5z" />
<glyph horiz-adv-x="314" d="M139 -106h-76l148 294h-175v56h250v-56z" />
<glyph horiz-adv-x="364" d="M118 -19q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM237 160q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM182 -114q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5z" />
<glyph horiz-adv-x="364" d="M181 197q-29 0 -46 -15t-17 -40v-28q0 -24 17 -36.5t46 -12.5t46.5 12.5t17.5 36.5v28q0 25 -17.5 40t-46.5 15zM181 252q137 0 137 -176q0 -41 -8 -76t-25 -60.5t-43 -39.5t-63 -14q-56 0 -88 26.5t-37 71.5h70q6 -43 56 -43q29 0 47 19.5t18 49.5v39q-13 -17 -33 -27 t-47 -10q-54 0 -87 32t-33 83q0 24 9 47t26 40t42.5 27.5t58.5 10.5z" />
<glyph horiz-adv-x="346" d="M41 3v46h108v109h49v-109h107v-46h-107v-109h-49v109h-108z" />
<glyph horiz-adv-x="374" d="M319 2h-264v46h264v-46z" />
<glyph horiz-adv-x="374" d="M55 -52v48h264v-48h-264zM55 54v48h264v-48h-264z" />
<glyph horiz-adv-x="215" d="M113 -38q0 -42 20.5 -86t52.5 -87h-63q-35 45 -57 98t-22 130q0 76 22 129t57 98h63q-35 -47 -54 -89t-19 -84v-109z" />
<glyph horiz-adv-x="215" d="M103 71q0 42 -19.5 84t-54.5 89h63q35 -45 57 -98t22 -129q0 -77 -22 -130.5t-57 -97.5h-63q33 43 53.5 87t20.5 86v109z" />
<glyph unicode="&#x215b;" horiz-adv-x="783" d="M535 87q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM654 266q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM599 -8q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5zM224 330h-70v234h-111v56h111v60h70v-350zM552 680 h76l-426 -680h-76z" />
<glyph unicode="&#x215d;" horiz-adv-x="810" d="M562 87q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM681 266q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM626 -8q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5zM54 478l14 202h219v-53h-157l-6 -94q12 15 30 24 t41 9q54 0 84.5 -32.5t30.5 -85.5q0 -59 -34.5 -92.5t-95.5 -33.5q-63 0 -95 28.5t-37 78.5h70q3 -29 19 -41.5t42 -12.5t42 14t16 35v45q0 23 -17 33.5t-40 10.5q-17 0 -33 -9t-23 -26h-70zM579 680h76l-426 -680h-76z" />
<glyph unicode="&#x215e;" horiz-adv-x="748" d="M500 87q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM619 266q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM564 -8q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5zM151 330h-76l148 294h-175v56h250v-56zM517 680h76 l-426 -680h-76z" />
<glyph unicode="&#x2190;" horiz-adv-x="860" d="M423 510l-213 -214h560v-82h-560l213 -214h-115l-218 219v72l218 219h115z" />
<glyph unicode="&#x2192;" horiz-adv-x="860" d="M552 510l218 -219v-72l-218 -219h-115l213 214h-560v82h560l-213 214h115z" />
<glyph unicode="&#x2009;" horiz-adv-x="120" />
<glyph horiz-adv-x="354" d="M128 483v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70 q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74z" />
<glyph horiz-adv-x="355" d="M129 569v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70 q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74z" />
<glyph horiz-adv-x="355" d="M129 153v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70 q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74z" />
<glyph horiz-adv-x="354" d="M128 47v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70 q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74z" />
<glyph unicode="&#xbe;" horiz-adv-x="786" d="M612 266l-91 -132h93v132h-2zM614 0v78h-154v56l148 216h74v-216h63v-56h-63v-78h-68zM134 483v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31 t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74zM584 680h76l-426 -680h-76z" />
<glyph unicode="&#x2153;" horiz-adv-x="778" d="M549 153v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70 q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74zM224 330h-70v234h-111v56h111v60h70v-350zM552 680h76l-426 -680h-76z" />
<glyph unicode="&#x2154;" horiz-adv-x="816" d="M587 153v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70 q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74zM147 392v-2h166v-60h-257v60l144 130q17 16 27 29.5t10 34.5t-14 34t-38 13q-26 0 -41.5 -14.5t-15.5 -43.5h-70q0 54 36 84.5t93 30.5t88 -29.5t31 -73.5q0 -38 -16.5 -62.5t-41.5 -45.5zM590 680h76 l-426 -680h-76z" />
<glyph unicode="&#x215c;" horiz-adv-x="815" d="M567 87q0 -20 19 -30t45 -10t45 10t19 30v26q0 19 -19 29t-45 10t-45 -10t-19 -29v-26zM686 266q0 20 -17.5 28.5t-37.5 8.5t-37.5 -8.5t-17.5 -28.5v-21q0 -19 17.5 -27.5t37.5 -8.5t37.5 8.5t17.5 27.5v21zM631 -8q-28 0 -53 6.5t-43.5 19t-29.5 31.5t-11 45 q0 32 17.5 54.5t47.5 33.5q-54 23 -54 81q0 22 10 40t27.5 30t40 18.5t48.5 6.5q25 0 48 -6.5t40.5 -18.5t27.5 -30t10 -40q0 -58 -54 -81q30 -11 47.5 -33.5t17.5 -54.5q0 -26 -11 -45t-30 -31.5t-43.5 -19t-52.5 -6.5zM134 483v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30 t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74z M584 680h76l-426 -680h-76z" />
<glyph unicode="&#x2592;" horiz-adv-x="935" d="M680 680h85v-85h85v-85h-85v85h-85v85zM170 0h-85v85h85v-85h85v-85h-85v85zM340 0h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h-85v85zM595 595h-85v85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85zM510 510h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85 h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85zM510 0h-85v85h-85v85h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85zM510 340h-85v85h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85 zM510 170h-85v85h-85v85h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85z" />
<glyph unicode="&#x25a4;" horiz-adv-x="935" d="M85 510h765v-85h-765v85zM85 680h765v-85h-765v85zM85 340h765v-85h-765v85zM85 170h765v-85h-765v85zM85 0h765v-85h-765v85z" />
<glyph unicode="&#x25a5;" horiz-adv-x="935" d="M765 680h85v-765h-85v765zM85 680h85v-765h-85v765zM595 680h85v-765h-85v765zM425 680h85v-765h-85v765zM255 680h85v-765h-85v765z" />
<glyph unicode="&#x25a8;" horiz-adv-x="935" d="M510 0h85v-85h-85v85zM170 0h85v-85h-85v85zM765 595h85v-85h-85v85zM680 510h85v-85h-85v85zM595 425h85v-85h-85v85zM765 255h85v-85h-85v85zM510 680h85v-85h-85v85zM510 340h85v-85h-85v85zM680 170h85v-85h-85v85zM170 680h85v-85h-85v85zM425 595h85v-85h-85v85z M85 595h85v-85h-85v85zM340 510h85v-85h-85v85zM255 425h85v-85h-85v85zM170 340h85v-85h-85v85zM425 255h85v-85h-85v85zM85 255h85v-85h-85v85zM340 170h85v-85h-85v85zM595 85h85v-85h-85v85zM255 85h85v-85h-85v85z" />
<glyph unicode="&#x25a7;" horiz-adv-x="935" d="M765 595h85v-85h-85v85h-85v85h85v-85zM255 85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85h85v-85h85v-85zM765 255h85v-85h-85v85h-85v85h-85v85h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85zM595 85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85h-85v85 h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85z" />
<glyph unicode="&#x2b05;" horiz-adv-x="850" d="M340 425h-85v85h85v-85zM425 510h-85v85h85v-85zM765 255h-510v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85h85v85h85v-85h510v-85z" />
<glyph unicode="&#x2b95;" horiz-adv-x="850" d="M510 170h85v-85h-85v85zM425 85h85v-85h-85v85zM510 510h-85v85h85v-85h85v-85h85v-85h85v-85h-85v-85h-85v85h-510v85h510v85h-85v85z" />
<glyph unicode="&#x2b0a;" horiz-adv-x="765" d="M510 170h85v255h85v-425h-425v85h255v85h-85v85h-85v85h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85z" />
<glyph unicode="&#x2b0b;" horiz-adv-x="765" d="M255 255h85v-85h-85v85zM340 340h85v-85h-85v85zM425 425h85v-85h-85v85zM595 425h-85v85h85v-85zM680 510h-85v85h85v-85zM85 0v425h85v-255h85v-85h255v-85h-425z" />
<glyph unicode="&#x2b07;" horiz-adv-x="765" d="M595 255v-85h-85v85h85zM680 340v-85h-85v85h85zM170 340v-85h-85v85h85zM425 680v-510h85v-85h-85v-85h-85v85h-85v85h-85v85h85v-85h85v510h85z" />
<glyph unicode="&#x2b06;" horiz-adv-x="765" d="M170 425v85h85v-85h-85zM85 340v85h85v-85h-85zM680 425v-85h-85v85h-85v85h-85v-510h-85v510h-85v85h85v85h85v-85h85v-85h85v-85h85z" />
<glyph unicode="&#x2717;" horiz-adv-x="765" d="M85 85h85v-85h-85v85zM255 255h85v-85h-85v85zM170 170h85v-85h-85v85zM510 340h-85v85h85v-85zM595 425h-85v85h85v-85zM680 510h-85v85h85v-85zM340 340h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85z" />
<glyph unicode="&#x2bbd;" horiz-adv-x="1104" d="M849 510h-85v85h85v-85zM764 425h-85v85h85v-85zM339 170h85v-85h-85v85zM424 255h85v-85h-85v85zM254 85h85v-85h-85v85zM679 340h-85v85h85v-85zM509 340h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85z M170 -85h764v765h-764v-765zM85 765h934v-935h-934v935z" />
<glyph unicode="&#x25a6;" horiz-adv-x="1104" d="M764 595h85v-85h-85v85zM254 85h85v-85h-85v85zM594 595h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85zM339 170h-85v85h85v-85h85v-85h85v-85h-85v85h-85v85zM509 510h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85zM339 340h-85v85h85v-85h85 v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85zM509 340h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85zM170 -85h764v765h-764v-765zM85 765h934v-935h-934v935z" />
<glyph unicode="&#x29c8;" horiz-adv-x="1104" d="M339 85h425v425h-425v-425zM170 -85h764v765h-764v-765zM85 765h934v-935h-934v935zM254 595h595v-595h-595v595z" />
<glyph unicode="&#x26dd;" horiz-adv-x="1105" d="M935 765h85v-85h-85v85zM425 255h85v-85h-85v85zM85 -85h85v-85h-85v85zM680 340h-85v85h85v-85zM765 425h-85v85h85v-85zM850 510h-85v85h85v-85zM340 170h85v-85h-85v85zM255 85h85v-85h-85v85zM510 340h-85v85h-85v85h-85v85h-85v85h-85v85h85v-85h85v-85h85v-85h85 v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85h-85v85h-85v85zM255 -85h-85v85h-85v595h85v-595h85v-85h595v-85h-595v85zM255 765h595v-85h85v-85h85v-595h-85v595h-85v85h-595v85z" />
<glyph unicode="&#x230c;" horiz-adv-x="425" d="M85 85h85v-170h170v-85h-255v255zM85 765h255v-85h-170v-170h-85v255z" />
<glyph unicode="&#x230d;" horiz-adv-x="425" d="M340 -170h-255v85h170v170h85v-255zM340 510h-85v170h-170v85h255v-255z" />
<glyph unicode="&#x230f;" horiz-adv-x="425" d="M340 -170h-85v170h-170v85h255v-255zM340 510h-255v85h170v170h85v-255z" />
<glyph unicode="&#x230e;" horiz-adv-x="425" d="M85 85h255v-85h-170v-170h-85v255zM85 765h85v-170h170v-85h-255v255z" />
<glyph unicode="&#x274e;" horiz-adv-x="934" d="M764 595h-85v-85h85v85zM339 170h85v85h-85v-85zM254 85h85v85h-85v-85zM169 0h85v85h-85v-85zM594 425h-85v-85h85v85zM679 510h-85v-85h85v85zM169 510h85v-85h85v85h-85v85h-85v-85zM424 425h-85v-85h85v-85h85v-85h85v-85h85v-85h85v85h-85v85h-85v85h-85v85h-85v85z M85 680h764v-765h-764v765z" />
<glyph unicode="&#x2347;" horiz-adv-x="934" d="M425 510h-85v-85h85v85zM340 425h-85v-85h-85v-85h85v-85h85v-85h85v85h-85v85h424v85h-424v85zM85 680h764v-765h-764v765z" />
<glyph unicode="&#x2348;" horiz-adv-x="934" d="M509 85h85v85h-85v-85zM594 510h-85v-85h85v-85h-424v-85h424v-85h85v85h85v85h-85v85h-85v85zM849 -85h-764v765h764v-765z" />
<glyph unicode="&#x2357;" horiz-adv-x="935" d="M595 255v-85h85v85h-85zM340 170v85h-85v-85h85v-85h85v-85h85v85h85v85h-85v425h-85v-425h-85zM85 -85v765h765v-765h-765z" />
<glyph unicode="&#x2350;" horiz-adv-x="935" d="M340 340v85h-85v-85h85zM425 425v-425h85v425h85v-85h85v85h-85v85h-85v85h-85v-85h-85v-85h85zM850 680v-765h-765v765h765z" />
<glyph unicode="&#x25b3;" horiz-adv-x="765" d="M255 510h85v-170h-85v170zM170 340h85v-170h-85v170zM85 170h85v-85h425v85h-85v170h-85v170h-85v170h85v-170h85v-170h85v-170h85v-170h-595v170z" />
<glyph unicode="&#x25bd;" horiz-adv-x="765" d="M595 340h-85v170h85v-170zM510 170h-85v170h85v-170zM255 340h-85v170h-85v170h595v-170h-85v85h-425v-85h85v-170h85v-170h85v-170h-85v170h-85v170z" />
<glyph unicode="&#x25c1;" horiz-adv-x="850" d="M425 425v-85h-170v85h170zM595 510v-85h-170v85h170zM595 170v-85h-170v85h-170v85h-170v85h170v-85h170v-85h170zM595 0v85h85v425h-85v85h170v-595h-170z" />
<glyph unicode="&#x25b7;" horiz-adv-x="850" d="M255 85v85h170v-85h-170zM765 340v-85h-170v85h170zM425 170v85h170v-85h-170zM85 0v595h170v-85h170v-85h170v-85h-170v85h-170v85h-85v-425h85v-85h-170z" />
<glyph unicode="&#x20de;" horiz-adv-x="851" d="M170 85h510v510h-510v-510zM85 680h681v-680h-681v680z" />
<glyph unicode="&#x20dd;" horiz-adv-x="850" d="M170 595h85v-85h-85v85zM595 170h85v-85h-85v85zM255 680h340v-85h85v-85h85v-340h-85v340h-85v85h-340v85zM255 85h340v-85h-340v85h-85v85h-85v340h85v-340h85v-85z" />
<glyph unicode="&#x1f4c4;" horiz-adv-x="680" d="M340 595h-170v-510h340v340h-170v170zM85 680h340v-170h170v-510h-510v680z" />
<glyph unicode="&#xf8ff;" horiz-adv-x="880" d="M100 680h680v-680h-680v680z" />
<glyph unicode="&#x3bc;" horiz-adv-x="570" d="M173 -170h-90v680h90v-341q0 -51 23 -80t77 -29q57 0 90.5 42.5t33.5 109.5v298h90v-510h-90v84q-21 -45 -55.5 -70.5t-88.5 -25.5q-27 0 -47.5 9.5t-32.5 23.5v-191z" />
<glyph unicode="&#x3a9;" horiz-adv-x="661" d="M455 77h120v-77h-200v215q59 10 94.5 39.5t35.5 82.5v158q0 30 -14.5 53.5t-38.5 39.5t-55.5 24.5t-65.5 8.5q-35 0 -66.5 -8.5t-55.5 -24.5t-38 -39.5t-14 -53.5v-158q0 -52 35 -81.5t93 -40.5v-215h-200v77h120v83q-72 30 -107.5 95.5t-35.5 160.5q0 133 68.5 206 t200.5 73t200.5 -73t68.5 -206q0 -95 -36 -161t-109 -96v-82z" />
<glyph unicode="&#x394;" horiz-adv-x="678" d="M153 77h372l-187 474zM295 680h88l267 -680h-622z" />
<glyph horiz-adv-x="256" d="M175 586h-94v94h94v-94zM83 0v510h90v-510h-90z" />
<glyph unicode="b" horiz-adv-x="595" d="M83 0v680h90v-253q24 46 64.5 70.5t98.5 24.5q51 0 89 -21t63 -57t37.5 -85t12.5 -104q0 -56 -12.5 -104.5t-37.5 -84.5t-63 -57t-89 -21q-58 0 -98.5 24.5t-64.5 70.5v-83h-90zM173 212q0 -66 36 -109t104 -43q57 0 93.5 32t36.5 84v158q0 52 -36.5 84t-93.5 32 q-68 0 -104 -43t-36 -109v-86z" />
<glyph unicode="d" horiz-adv-x="595" d="M422 0v83q-24 -46 -64 -70.5t-97 -24.5q-52 0 -90 21t-63.5 57t-38 84.5t-12.5 104.5q0 55 12.5 104t37.5 85t63 57t89 21q57 0 98 -24.5t65 -70.5v253h90v-680h-90zM422 298q0 66 -36 109t-104 43q-28 0 -51.5 -8.5t-41 -23.5t-27.5 -36.5t-10 -47.5v-158 q0 -26 10 -47.5t27.5 -36.5t41 -23.5t51.5 -8.5q68 0 104 43t36 109v86z" />
<glyph unicode="&#x2002;" horiz-adv-x="500" />
<glyph unicode="&#x2003;" horiz-adv-x="1000" />
<glyph unicode="&#x2004;" horiz-adv-x="333" />
<glyph unicode="&#x2005;" horiz-adv-x="250" />
<glyph unicode="&#x200a;" horiz-adv-x="75" />
<glyph unicode="&#x2bc;" horiz-adv-x="306" d="M210 570l-62 -110h-48l59 110h-59v110h110v-110z" />
<glyph horiz-adv-x="664" d="M140 255q0 -30 8.5 -58t24.5 -51l267 267q-23 16 -50.5 25t-57.5 9q-40 0 -75 -15t-61 -41t-41 -61t-15 -75zM332 63q40 0 75 15t61 41t41 61t15 75q0 30 -9 57.5t-25 50.5l-267 -267q23 -16 51 -24.5t58 -8.5zM65 255q0 55 21 103.5t57.5 85t85 57.5t103.5 21 q46 0 87 -14.5t75 -40.5l55 55l50 -50l-55 -55q26 -34 40.5 -75t14.5 -87q0 -55 -21 -103.5t-57.5 -85t-85 -57.5t-103.5 -21q-46 0 -87 14.5t-75 40.5l-55 -55l-50 50l55 55q-26 34 -40.5 75t-14.5 87z" />
<glyph horiz-adv-x="187" d="M132 -106l-35 -66h-41l34 66h-35v76h77v-76z" />
<glyph horiz-adv-x="187" d="M132 -106h-77v76h77v-76z" />
<glyph horiz-adv-x="331" d="M199 494v17q0 20 -10.5 29t-28.5 9q-19 0 -31 -8.5t-12 -28.5h-67q2 44 33 66t79 22t75.5 -23t27.5 -68v-127h45v-52h-107l-1 39h-3q-25 -45 -74 -45q-38 0 -61 22.5t-23 57.5t21.5 56.5t64.5 26.5zM143 375q26 0 41 16.5t15 47.5v7l-69 -6q-20 -1 -20 -21v-23 q0 -21 33 -21z" />
<glyph horiz-adv-x="349" d="M60 330v350h66v-123q12 20 30.5 31.5t47.5 11.5q27 0 46.5 -11t32 -29.5t18.5 -43.5t6 -53t-6.5 -53.5t-19 -44.5t-32.5 -30t-47 -11q-26 0 -45 12t-31 32v-38h-66zM126 439q0 -30 15.5 -46.5t41.5 -16.5q24 0 38.5 11.5t14.5 35.5v79q0 24 -14.5 35.5t-38.5 11.5 q-26 0 -41.5 -17t-15.5 -47v-46z" />
<glyph horiz-adv-x="314" d="M42 461q0 67 33 103t89 36q41 0 71.5 -22t35.5 -72h-66q-2 26 -13.5 34.5t-31.5 8.5t-33.5 -12t-13.5 -35v-79q0 -21 13 -34t35 -13q44 0 47 44h65q-3 -26 -13 -44t-25 -29.5t-34 -17t-39 -5.5q-58 0 -89 37.5t-31 99.5z" />
<glyph horiz-adv-x="349" d="M223 330v38q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5t31 -31.5v123h66v-350h-66zM223 485q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5 q26 0 41.5 16.5t15.5 46.5v46z" />
<glyph horiz-adv-x="320" d="M41 463q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM111 490h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13z" />
<glyph horiz-adv-x="243" d="M151 594h65v-52h-65v-212h-67v212h-52v52h52v86h138v-52h-71v-34z" />
<glyph horiz-adv-x="349" d="M223 485q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5q26 0 41.5 16.5t15.5 46.5v46zM223 594h66v-259q0 -26 -10 -46.5t-26.5 -34t-37.5 -20.5t-44 -7q-49 0 -80 18.5t-39 65.5h63q4 -19 17.5 -25.5t35.5 -6.5t38.5 12 t16.5 46v31q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5t31 -31.5v37z" />
<glyph horiz-adv-x="348" d="M127 555q12 21 31 33t47 12q40 0 62.5 -24t22.5 -73v-173h-67v168q0 23 -9 36t-33 13q-26 0 -40 -18t-14 -46v-153h-67v350h67v-125z" />
<glyph horiz-adv-x="187" d="M130 620h-71v60h71v-60zM60 330v264h67v-264h-67z" />
<glyph horiz-adv-x="187" d="M60 594h67v-350h-136v52h69v298zM129 620h-71v60h71v-60z" />
<glyph horiz-adv-x="326" d="M127 330h-67v350h67v-350zM128 474l85 118h78l-93 -118l101 -144h-76z" />
<glyph horiz-adv-x="187" d="M60 330v350h67v-350h-67z" />
<glyph horiz-adv-x="488" d="M274 550q13 26 33 38t45 12q35 0 57 -24t22 -78v-168h-67v172q0 20 -8.5 32.5t-29.5 12.5q-20 0 -33.5 -15t-13.5 -47v-155h-67v172q0 20 -8.5 32.5t-28.5 12.5t-34 -15t-14 -47v-155h-67v264h67v-39q10 22 30 33.5t43 11.5q27 0 46 -12t28 -38z" />
<glyph horiz-adv-x="348" d="M127 555q12 21 31 33t47 12q40 0 62.5 -24t22.5 -73v-173h-67v168q0 23 -9 36t-33 13q-26 0 -40 -18t-14 -46v-153h-67v264h67v-39z" />
<glyph horiz-adv-x="331" d="M219 501q0 23 -15.5 36t-38.5 13t-38.5 -13t-15.5 -36v-78q0 -23 15.5 -36t38.5 -13t38.5 13t15.5 36v78zM290 462q0 -63 -32.5 -100.5t-92.5 -37.5q-59 0 -91.5 37.5t-32.5 100.5t32.5 100.5t91.5 37.5q60 0 92.5 -37.5t32.5 -100.5z" />
<glyph horiz-adv-x="349" d="M60 244v350h66v-37q12 20 30.5 31.5t47.5 11.5q27 0 46.5 -11t32 -29.5t18.5 -43.5t6 -53t-6.5 -53.5t-19 -44.5t-32.5 -30t-47 -11q-26 0 -45 12t-31 32v-124h-66zM126 439q0 -30 15.5 -46.5t41.5 -16.5q24 0 38.5 11.5t14.5 35.5v79q0 24 -14.5 35.5t-38.5 11.5 q-26 0 -41.5 -17t-15.5 -47v-46z" />
<glyph horiz-adv-x="349" d="M223 485q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5q26 0 41.5 16.5t15.5 46.5v46zM223 244v124q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5 t31 -31.5v37h66v-350h-66z" />
<glyph horiz-adv-x="237" d="M60 594h151v-53h-85v-211h-66v264z" />
<glyph horiz-adv-x="303" d="M192 484q29 -5 49.5 -24t20.5 -54t-28 -58.5t-81 -23.5q-52 0 -81.5 23t-30.5 66h67q2 -21 12.5 -30t32.5 -9q19 0 31 8t12 23q0 24 -36 30l-41 7q-35 6 -53.5 26t-18.5 52q0 35 28.5 57.5t73.5 22.5t73.5 -21t30.5 -65h-66q-1 20 -10.5 28t-26.5 8t-27.5 -7.5 t-10.5 -21.5q0 -22 27 -27z" />
<glyph horiz-adv-x="260" d="M150 382h65v-52h-132v212h-51v52h51v86h67v-86h65v-52h-65v-160z" />
<glyph horiz-adv-x="345" d="M218 369q-12 -21 -31.5 -33t-45.5 -12q-38 0 -61 24t-23 73v173h67v-168q0 -23 9.5 -36t31.5 -13q26 0 39.5 19t13.5 45v153h67v-264h-67v39z" />
<glyph horiz-adv-x="307" d="M152 410h4l55 184h71l-96 -264h-64l-97 264h74z" />
<glyph horiz-adv-x="444" d="M302 418l41 176h71l-81 -264h-71l-39 183h-4l-36 -183h-72l-81 264h75l39 -176h4l33 176h80l37 -176h4z" />
<glyph horiz-adv-x="317" d="M122 467l-85 127h75l48 -80l48 80h73l-85 -123l94 -141h-75l-55 94l-57 -94h-76z" />
<glyph horiz-adv-x="309" d="M157 410l57 184h71l-130 -350h-109v52h64l12 35l-99 263h74l56 -184h4z" />
<glyph horiz-adv-x="301" d="M258 382v-52h-219v40l130 172h-120v52h207v-39l-132 -173h134z" />
<glyph horiz-adv-x="350" d="M138 392v-2h166v-60h-257v60l144 130q17 16 27 29.5t10 34.5t-14 34t-38 13q-26 0 -41.5 -14.5t-15.5 -43.5h-70q0 54 36 84.5t93 30.5t88 -29.5t31 -73.5q0 -38 -16.5 -62.5t-41.5 -45.5z" />
<glyph horiz-adv-x="346" d="M41 439v46h108v109h49v-109h107v-46h-107v-109h-49v109h-108z" />
<glyph horiz-adv-x="374" d="M319 438h-264v46h264v-46z" />
<glyph horiz-adv-x="374" d="M55 384v48h264v-48h-264zM55 490v48h264v-48h-264z" />
<glyph horiz-adv-x="209" d="M109 398q0 -42 20.5 -86t52.5 -87h-63q-35 45 -57 98t-22 130q0 76 22 129t57 98h63q-35 -47 -54 -89t-19 -84v-109z" />
<glyph horiz-adv-x="209" d="M101 507q0 42 -19.5 84t-54.5 89h63q35 -45 57 -98t22 -129q0 -77 -22 -130.5t-57 -97.5h-63q33 43 53.5 87t20.5 86v109z" />
<glyph horiz-adv-x="187" d="M132 330l-35 -66h-41l34 66h-35v76h77v-76z" />
<glyph horiz-adv-x="187" d="M132 330h-77v76h77v-76z" />
<glyph horiz-adv-x="332" d="M199 58v17q0 20 -10.5 29t-28.5 9q-19 0 -31 -8.5t-12 -28.5h-67q2 44 33 66t79 22t75.5 -23t27.5 -68v-127h45v-52h-107l-1 39h-3q-25 -45 -74 -45q-38 0 -61 22.5t-23 57.5t21.5 56.5t64.5 26.5zM143 -61q26 0 41 16.5t15 47.5v7l-69 -6q-20 -1 -20 -21v-23 q0 -21 33 -21z" />
<glyph horiz-adv-x="349" d="M60 -106v350h66v-123q12 20 30.5 31.5t47.5 11.5q27 0 46.5 -11t32 -29.5t18.5 -43.5t6 -53t-6.5 -53.5t-19 -44.5t-32.5 -30t-47 -11q-26 0 -45 12t-31 32v-38h-66zM126 3q0 -30 15.5 -46.5t41.5 -16.5q24 0 38.5 11.5t14.5 35.5v79q0 24 -14.5 35.5t-38.5 11.5 q-26 0 -41.5 -17t-15.5 -47v-46z" />
<glyph horiz-adv-x="314" d="M42 25q0 67 33 103t89 36q41 0 71.5 -22t35.5 -72h-66q-2 26 -13.5 34.5t-31.5 8.5t-33.5 -12t-13.5 -35v-79q0 -21 13 -34t35 -13q44 0 47 44h65q-3 -26 -13 -44t-25 -29.5t-34 -17t-39 -5.5q-58 0 -89 37.5t-31 99.5z" />
<glyph horiz-adv-x="349" d="M223 -106v38q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5t31 -31.5v123h66v-350h-66zM223 49q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5 q26 0 41.5 16.5t15.5 46.5v46z" />
<glyph horiz-adv-x="320" d="M41 27q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM111 54h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13z" />
<glyph horiz-adv-x="243" d="M151 158h65v-52h-65v-212h-67v212h-52v52h52v86h138v-52h-71v-34z" />
<glyph horiz-adv-x="349" d="M223 49q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5q26 0 41.5 16.5t15.5 46.5v46zM223 158h66v-259q0 -26 -10 -46.5t-26.5 -34t-37.5 -20.5t-44 -7q-49 0 -80 18.5t-39 65.5h63q4 -19 17.5 -25.5t35.5 -6.5t38.5 12 t16.5 46v31q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5t31 -31.5v37z" />
<glyph horiz-adv-x="347" d="M127 119q12 21 31 33t47 12q40 0 62.5 -24t22.5 -73v-173h-67v168q0 23 -9 36t-33 13q-26 0 -40 -18t-14 -46v-153h-67v350h67v-125z" />
<glyph horiz-adv-x="187" d="M130 184h-71v60h71v-60zM60 -106v264h67v-264h-67z" />
<glyph horiz-adv-x="187" d="M60 158h67v-350h-136v52h69v298zM129 184h-71v60h71v-60z" />
<glyph horiz-adv-x="326" d="M127 -106h-67v350h67v-350zM128 38l85 118h78l-93 -118l101 -144h-76z" />
<glyph horiz-adv-x="187" d="M60 -106v350h67v-350h-67z" />
<glyph horiz-adv-x="488" d="M274 114q13 26 33 38t45 12q35 0 57 -24t22 -78v-168h-67v172q0 20 -8.5 32.5t-29.5 12.5q-20 0 -33.5 -15t-13.5 -47v-155h-67v172q0 20 -8.5 32.5t-28.5 12.5t-34 -15t-14 -47v-155h-67v264h67v-39q10 22 30 33.5t43 11.5q27 0 46 -12t28 -38z" />
<glyph horiz-adv-x="347" d="M127 119q12 21 31 33t47 12q40 0 62.5 -24t22.5 -73v-173h-67v168q0 23 -9 36t-33 13q-26 0 -40 -18t-14 -46v-153h-67v264h67v-39z" />
<glyph horiz-adv-x="331" d="M219 65q0 23 -15.5 36t-38.5 13t-38.5 -13t-15.5 -36v-78q0 -23 15.5 -36t38.5 -13t38.5 13t15.5 36v78zM290 26q0 -63 -32.5 -100.5t-92.5 -37.5q-59 0 -91.5 37.5t-32.5 100.5t32.5 100.5t91.5 37.5q60 0 92.5 -37.5t32.5 -100.5z" />
<glyph horiz-adv-x="349" d="M60 -192v350h66v-37q12 20 30.5 31.5t47.5 11.5q27 0 46.5 -11t32 -29.5t18.5 -43.5t6 -53t-6.5 -53.5t-19 -44.5t-32.5 -30t-47 -11q-26 0 -45 12t-31 32v-124h-66zM126 3q0 -30 15.5 -46.5t41.5 -16.5q24 0 38.5 11.5t14.5 35.5v79q0 24 -14.5 35.5t-38.5 11.5 q-26 0 -41.5 -17t-15.5 -47v-46z" />
<glyph horiz-adv-x="349" d="M223 49q0 30 -15.5 47t-41.5 17q-24 0 -38.5 -11.5t-14.5 -35.5v-79q0 -24 14.5 -35.5t38.5 -11.5q26 0 41.5 16.5t15.5 46.5v46zM223 -192v124q-12 -20 -30.5 -32t-47.5 -12q-26 0 -45.5 11t-32.5 30t-19 44.5t-6 53.5t6 53t19 43.5t33 29.5t47 11q26 0 45 -11.5 t31 -31.5v37h66v-350h-66z" />
<glyph horiz-adv-x="237" d="M60 158h151v-53h-85v-211h-66v264z" />
<glyph horiz-adv-x="303" d="M192 48q29 -5 49.5 -24t20.5 -54t-28 -58.5t-81 -23.5q-52 0 -81.5 23t-30.5 66h67q2 -21 12.5 -30t32.5 -9q19 0 31 8t12 23q0 24 -36 30l-41 7q-35 6 -53.5 26t-18.5 52q0 35 28.5 57.5t73.5 22.5t73.5 -21t30.5 -65h-66q-1 20 -10.5 28t-26.5 8t-27.5 -7.5 t-10.5 -21.5q0 -22 27 -27z" />
<glyph horiz-adv-x="260" d="M150 -54h65v-52h-132v212h-51v52h51v86h67v-86h65v-52h-65v-160z" />
<glyph horiz-adv-x="345" d="M218 -67q-12 -21 -31.5 -33t-45.5 -12q-38 0 -61 24t-23 73v173h67v-168q0 -23 9.5 -36t31.5 -13q26 0 39.5 19t13.5 45v153h67v-264h-67v39z" />
<glyph horiz-adv-x="307" d="M152 -26h4l55 184h71l-96 -264h-64l-97 264h74z" />
<glyph horiz-adv-x="444" d="M302 -18l41 176h71l-81 -264h-71l-39 183h-4l-36 -183h-72l-81 264h75l39 -176h4l33 176h80l37 -176h4z" />
<glyph horiz-adv-x="317" d="M122 31l-85 127h75l48 -80l48 80h73l-85 -123l94 -141h-75l-55 94l-57 -94h-76z" />
<glyph horiz-adv-x="309" d="M157 -26l57 184h71l-130 -350h-109v52h64l12 35l-99 263h74l56 -184h4z" />
<glyph horiz-adv-x="301" d="M258 -54v-52h-219v40l130 172h-120v52h207v-39l-132 -173h134z" />
<glyph unicode="&#xb2;" horiz-adv-x="397" d="M161 478v-2h166v-60h-257v60l144 130q17 16 27 29.5t10 34.5t-14 34t-38 13q-26 0 -41.5 -14.5t-15.5 -43.5h-70q0 54 36 84.5t93 30.5t88 -29.5t31 -73.5q0 -38 -16.5 -62.5t-41.5 -45.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="386" d="M145 569v54h70q17 0 23.5 8t6.5 21v27q0 20 -14.5 30t-36.5 10q-24 0 -39 -12t-17 -37h-70q0 49 36 76.5t91 27.5q56 0 89.5 -26.5t33.5 -68.5q0 -59 -55 -83q30 -11 45.5 -31t15.5 -54q0 -47 -37 -75t-94 -28q-27 0 -51 7t-42.5 20.5t-29 34.5t-10.5 50h70 q2 -30 19 -43.5t43 -13.5q27 0 43 13t16 32v30q0 31 -32 31h-74z" />
<glyph unicode="&#xb9;" horiz-adv-x="305" d="M226 416h-70v234h-111v56h111v60h70v-350z" />
<glyph unicode="&#xaa;" horiz-adv-x="399" d="M224 494v17q0 20 -10.5 29t-28.5 9q-19 0 -31 -8.5t-12 -28.5h-67q2 44 33 66t79 22t75.5 -23t27.5 -68v-127h45v-52h-107l-1 39h-3q-25 -45 -74 -45q-38 0 -61 22.5t-23 57.5t21.5 56.5t64.5 26.5zM168 375q26 0 41 16.5t15 47.5v7l-69 -6q-20 -1 -20 -21v-23 q0 -21 33 -21z" />
<glyph unicode="&#xba;" horiz-adv-x="373" d="M240 501q0 23 -15.5 36t-38.5 13t-38.5 -13t-15.5 -36v-78q0 -23 15.5 -36t38.5 -13t38.5 13t15.5 36v78zM311 462q0 -63 -32.5 -100.5t-92.5 -37.5q-59 0 -91.5 37.5t-32.5 100.5t32.5 100.5t91.5 37.5q60 0 92.5 -37.5t32.5 -100.5z" />
<glyph horiz-adv-x="320" d="M41 27q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM111 54h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13zM178 253h68 l-60 -62h-53z" />
<glyph horiz-adv-x="320" d="M41 27q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM111 54h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13zM185 191h-53 l-60 62h68z" />
<glyph horiz-adv-x="320" d="M41 463q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM111 490h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13zM178 689h68 l-60 -62h-53z" />
<glyph horiz-adv-x="320" d="M41 463q0 62 33.5 99.5t87.5 37.5q27 0 48.5 -9.5t37 -26.5t23.5 -39.5t8 -49.5v-30h-168v-26q0 -20 15 -32t36 -12t33.5 8.5t15.5 30.5h66q-6 -42 -35 -66t-78 -24q-59 0 -91 38t-32 101zM111 490h100v13q0 23 -13.5 35t-35.5 12q-23 0 -37 -12t-14 -35v-13zM185 627 h-53l-60 62h68z" />
<glyph horiz-adv-x="608" d="M380 597q-33 18 -76 18q-62 0 -101.5 -37t-39.5 -105v-266q0 -41 16 -73zM227 84q31 -19 77 -19q62 0 101.5 37t39.5 105v266q0 42 -16 72zM545 340q0 -77 -11 -142t-38 -112t-73.5 -74t-118.5 -27t-118.5 27t-73.5 74t-38 112t-11 142q0 76 11 141.5t38 112.5t73.5 74 t118.5 27t118.5 -27t73.5 -74t38 -112.5t11 -141.5z" />
<glyph d="M376 597q-33 18 -76 18q-62 0 -101.5 -37t-39.5 -105v-266q0 -41 16 -73zM223 84q31 -19 77 -19q62 0 101.5 37t39.5 105v266q0 42 -16 72zM541 340q0 -77 -11 -142t-38 -112t-73.5 -74t-118.5 -27t-118.5 27t-73.5 74t-38 112t-11 142q0 76 11 141.5t38 112.5t73.5 74 t118.5 27t118.5 -27t73.5 -74t38 -112.5t11 -141.5z" />
<glyph horiz-adv-x="394" d="M231 626q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM161 384q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM333 505q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58t41.5 39t65 14t65 -14 t41.5 -39t22.5 -58t7 -72z" />
<glyph horiz-adv-x="410" d="M239 296q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM169 54q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM341 175q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58t41.5 39t65 14t65 -14 t41.5 -39t22.5 -58t7 -72z" />
<glyph horiz-adv-x="364" d="M216 190q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM146 -52q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM318 69q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58t41.5 39t65 14t65 -14 t41.5 -39t22.5 -58t7 -72z" />
<glyph horiz-adv-x="364" d="M216 712q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM146 470q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM318 591q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58t41.5 39t65 14t65 -14 t41.5 -39t22.5 -58t7 -72z" />
<glyph unicode="&#x2b09;" horiz-adv-x="765" d="M255 425h-85v-255h-85v425h425v-85h-255v-85h85v-85h85v-85h85v-85h85v-85h85v-85h-85v85h-85v85h-85v85h-85v85h-85v85z" />
<glyph unicode="&#x2b08;" horiz-adv-x="765" d="M510 340h-85v85h85v-85zM425 255h-85v85h85v-85zM340 170h-85v85h85v-85zM170 170h85v-85h-85v85zM85 85h85v-85h-85v85zM680 595v-425h-85v255h-85v85h-255v85h425z" />
<glyph horiz-adv-x="803" d="M217 626q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM147 384q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM319 505q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58t41.5 39t65 14t65 -14 t41.5 -39t22.5 -58t7 -72zM655 296q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM585 54q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM757 175q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58 t41.5 39t65 14t65 -14t41.5 -39t22.5 -58t7 -72zM577 680h76l-426 -680h-76z" />
<glyph horiz-adv-x="1141" d="M995 296q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM925 54q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM1097 175q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58t41.5 39t65 14t65 -14 t41.5 -39t22.5 -58t7 -72zM654 296q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM584 54q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM756 175q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58t-7 72t7 72t22.5 58 t41.5 39t65 14t65 -14t41.5 -39t22.5 -58t7 -72zM216 626q-8 3 -16 5t-18 2q-30 0 -46.5 -15.5t-16.5 -45.5v-134q0 -17 4 -26zM146 384q14 -7 36 -7q30 0 46.5 15.5t16.5 45.5v134q0 17 -4 26zM318 505q0 -39 -7 -72t-22.5 -58t-41.5 -39t-65 -14t-65 14t-41.5 39t-22.5 58 t-7 72t7 72t22.5 58t41.5 39t65 14t65 -14t41.5 -39t22.5 -58t7 -72zM576 680h76l-426 -680h-76z" />
<glyph unicode="l" horiz-adv-x="260" d="M85 0v680h90v-680h-90z" />
<hkern u1="&#x20;" u2="&#x104;" k="26" />
<hkern u1="&#x20;" u2="&#x1fa;" k="26" />
<hkern u1="&#x20;" u2="&#x166;" k="26" />
<hkern u1="&#x20;" u2="&#x167;" k="11" />
<hkern u1="&#x20;" u2="&#x21a;" k="26" />
<hkern u1="&#x20;" u2="&#x21b;" k="11" />
<hkern u1="&#x20;" u2="&#x162;" k="26" />
<hkern u1="&#x20;" u2="&#x163;" k="11" />
<hkern u1="&#x20;" u2="&#x165;" k="11" />
<hkern u1="&#x20;" u2="&#x1fc;" k="31" />
<hkern u1="&#x20;" u2="&#x1ef2;" k="31" />
<hkern u1="&#x20;" u2="&#x1e84;" k="24" />
<hkern u1="&#x20;" u2="&#x1e82;" k="24" />
<hkern u1="&#x20;" u2="&#x1e80;" k="24" />
<hkern u1="&#x20;" u2="&#x1ef3;" k="23" />
<hkern u1="&#x20;" u2="&#x1e85;" k="20" />
<hkern u1="&#x20;" u2="&#x1e83;" k="20" />
<hkern u1="&#x20;" u2="&#x1e81;" k="20" />
<hkern u1="&#x20;" u2="&#x177;" k="23" />
<hkern u1="&#x20;" u2="&#x176;" k="31" />
<hkern u1="&#x20;" u2="&#x175;" k="20" />
<hkern u1="&#x20;" u2="&#x174;" k="24" />
<hkern u1="&#x20;" u2="&#x164;" k="26" />
<hkern u1="&#x20;" u2="&#x134;" k="27" />
<hkern u1="&#x20;" u2="&#x102;" k="26" />
<hkern u1="&#x20;" u2="&#x100;" k="26" />
<hkern u1="&#x20;" u2="&#xc3;" k="26" />
<hkern u1="&#x20;" u2="f" k="11" />
<hkern u1="&#x20;" u2="&#x27;" k="10" />
<hkern u1="&#x20;" u2="&#x22;" k="10" />
<hkern u1="&#x20;" u2="&#xc6;" k="31" />
<hkern u1="&#x20;" u2="&#xc5;" k="26" />
<hkern u1="&#x20;" u2="&#x2019;" k="12" />
<hkern u1="&#x20;" u2="&#x201d;" k="12" />
<hkern u1="&#x20;" u2="&#xc1;" k="26" />
<hkern u1="&#x20;" u2="&#xc2;" k="26" />
<hkern u1="&#x20;" u2="&#x178;" k="31" />
<hkern u1="&#x20;" u2="&#xff;" k="23" />
<hkern u1="&#x20;" u2="&#xc0;" k="26" />
<hkern u1="&#x20;" u2="&#xc4;" k="26" />
<hkern u1="&#x20;" u2="&#xfd;" k="23" />
<hkern u1="&#x20;" u2="&#xdd;" k="31" />
<hkern u1="&#x20;" g2="fl" k="11" />
<hkern u1="&#x20;" g2="fi" k="11" />
<hkern u1="&#x20;" u2="Y" k="31" />
<hkern u1="&#x20;" u2="W" k="24" />
<hkern u1="&#x20;" u2="J" k="27" />
<hkern u1="&#x20;" u2="T" k="26" />
<hkern u1="&#x20;" u2="A" k="26" />
<hkern u1="&#x20;" u2="y" k="23" />
<hkern u1="&#x20;" u2="w" k="20" />
<hkern u1="&#x20;" u2="t" k="11" />
<hkern u1="&#x20;" u2="V" k="28" />
<hkern u1="&#x20;" u2="v" k="22" />
<hkern u1="h" u2="&#x29;" k="28" />
<hkern u1="h" u2="&#x2122;" k="21" />
<hkern u1="h" u2="&#x7d;" k="33" />
<hkern u1="h" u2="&#x2019;" k="8" />
<hkern u1="h" u2="\" k="30" />
<hkern u1="h" u2="]" k="32" />
<hkern u1="h" u2="U" k="6" />
<hkern u1="h" u2="Y" k="63" />
<hkern u1="h" u2="Z" k="5" />
<hkern u1="h" u2="W" k="31" />
<hkern u1="h" u2="V" k="43" />
<hkern u1="h" u2="T" k="81" />
<hkern u1="h" u2="y" k="6" />
<hkern u1="h" u2="v" k="6" />
<hkern u1="q" u2="&#x29;" k="26" />
<hkern u1="q" u2="&#x2122;" k="17" />
<hkern u1="q" u2="&#x7d;" k="26" />
<hkern u1="q" u2="\" k="16" />
<hkern u1="q" u2="]" k="26" />
<hkern u1="q" u2="Y" k="54" />
<hkern u1="q" u2="Z" k="5" />
<hkern u1="q" u2="W" k="26" />
<hkern u1="q" u2="V" k="37" />
<hkern u1="q" u2="T" k="87" />
<hkern u1="j" u2="U" k="5" />
<hkern u1="j" u2="Z" k="5" />
<hkern u1="t" u2="&#x2039;" k="12" />
<hkern u1="t" u2="&#xf0;" k="4" />
<hkern u1="t" u2="&#x7d;" k="13" />
<hkern u1="t" u2="]" k="13" />
<hkern u1="t" u2="Y" k="28" />
<hkern u1="t" u2="V" k="8" />
<hkern u1="t" u2="T" k="47" />
<hkern u1="u" u2="&#x29;" k="26" />
<hkern u1="u" u2="&#x2122;" k="17" />
<hkern u1="u" u2="&#x7d;" k="26" />
<hkern u1="u" u2="\" k="16" />
<hkern u1="u" u2="]" k="26" />
<hkern u1="u" u2="Y" k="54" />
<hkern u1="u" u2="Z" k="5" />
<hkern u1="u" u2="W" k="26" />
<hkern u1="u" u2="V" k="37" />
<hkern u1="u" u2="T" k="87" />
<hkern u1="e" u2="&#x29;" k="33" />
<hkern u1="e" u2="f" k="4" />
<hkern u1="e" u2="&#x2122;" k="21" />
<hkern u1="e" u2="&#x7d;" k="33" />
<hkern u1="e" u2="&#x2018;" k="8" />
<hkern u1="e" u2="&#x2019;" k="11" />
<hkern u1="e" u2="\" k="31" />
<hkern u1="e" u2="]" k="33" />
<hkern u1="e" u2="X" k="11" />
<hkern u1="e" u2="Y" k="84" />
<hkern u1="e" u2="Z" k="7" />
<hkern u1="e" u2="W" k="37" />
<hkern u1="e" u2="V" k="44" />
<hkern u1="e" u2="T" k="75" />
<hkern u1="e" u2="A" k="8" />
<hkern u1="e" u2="x" k="15" />
<hkern u1="e" u2="y" k="11" />
<hkern u1="e" u2="w" k="6" />
<hkern u1="e" u2="v" k="9" />
<hkern u1="c" u2="&#x29;" k="33" />
<hkern u1="c" u2="&#x2122;" k="20" />
<hkern u1="c" u2="&#x7d;" k="32" />
<hkern u1="c" u2="\" k="28" />
<hkern u1="c" u2="]" k="32" />
<hkern u1="c" u2="X" k="9" />
<hkern u1="c" u2="Y" k="75" />
<hkern u1="c" u2="Z" k="5" />
<hkern u1="c" u2="W" k="32" />
<hkern u1="c" u2="V" k="45" />
<hkern u1="c" u2="T" k="85" />
<hkern u1="c" u2="S" k="5" />
<hkern u1="c" u2="A" k="5" />
<hkern u1="c" u2="x" k="8" />
<hkern u1="c" u2="y" k="8" />
<hkern u1="c" u2="v" k="6" />
<hkern u1="k" u2="d" k="24" />
<hkern u1="k" u2="&#x2039;" k="32" />
<hkern u1="k" u2="&#x2122;" k="15" />
<hkern u1="k" u2="&#xf0;" k="29" />
<hkern u1="k" u2="&#x7d;" k="17" />
<hkern u1="k" u2="s" k="10" />
<hkern u1="k" u2="]" k="17" />
<hkern u1="k" u2="&#x2d;" k="24" />
<hkern u1="k" u2="Y" k="32" />
<hkern u1="k" u2="V" k="11" />
<hkern u1="k" u2="T" k="60" />
<hkern u1="k" u2="S" k="6" />
<hkern u1="k" u2="O" k="12" />
<hkern u1="k" u2="o" k="25" />
<hkern u1="k" u2="a" k="7" />
<hkern u1="m" u2="&#x29;" k="28" />
<hkern u1="m" u2="&#x2122;" k="21" />
<hkern u1="m" u2="&#x7d;" k="33" />
<hkern u1="m" u2="&#x2019;" k="8" />
<hkern u1="m" u2="\" k="30" />
<hkern u1="m" u2="]" k="32" />
<hkern u1="m" u2="U" k="6" />
<hkern u1="m" u2="Y" k="63" />
<hkern u1="m" u2="Z" k="5" />
<hkern u1="m" u2="W" k="31" />
<hkern u1="m" u2="V" k="43" />
<hkern u1="m" u2="T" k="81" />
<hkern u1="m" u2="y" k="6" />
<hkern u1="m" u2="v" k="6" />
<hkern u1="r" u2="d" k="8" />
<hkern u1="r" u2="&#x29;" k="23" />
<hkern u1="r" u2="&#x2039;" k="22" />
<hkern u1="r" u2="&#xc6;" k="44" />
<hkern u1="r" u2="&#xf0;" k="21" />
<hkern u1="r" u2="&#x7d;" k="15" />
<hkern u1="r" u2="]" k="15" />
<hkern u1="r" u2="&#x2f;" k="28" />
<hkern u1="r" u2="&#x2d;" k="27" />
<hkern u1="r" u2="X" k="28" />
<hkern u1="r" u2="Y" k="21" />
<hkern u1="r" u2="Z" k="11" />
<hkern u1="r" u2="J" k="56" />
<hkern u1="r" u2="T" k="46" />
<hkern u1="r" u2="A" k="39" />
<hkern u1="r" u2="&#x2e;" k="34" />
<hkern u1="r" u2="o" k="9" />
<hkern u1="r" u2="a" k="4" />
<hkern u1="r" u2="&#x20;" k="20" />
<hkern u1="v" u2="d" k="10" />
<hkern u1="v" u2="&#x105;" k="9" />
<hkern u1="v" u2="&#x104;" k="26" />
<hkern u1="v" u2="&#x119;" k="11" />
<hkern u1="v" u2="&#x1fa;" k="26" />
<hkern u1="v" u2="&#x1fb;" k="9" />
<hkern u1="v" u2="&#x166;" k="47" />
<hkern u1="v" u2="&#x219;" k="8" />
<hkern u1="v" u2="&#x21a;" k="47" />
<hkern u1="v" u2="&#x162;" k="47" />
<hkern u1="v" u2="&#x15f;" k="8" />
<hkern u1="v" u2="&#x111;" k="10" />
<hkern u1="v" u2="&#x1fd;" k="9" />
<hkern u1="v" u2="&#x1ff;" k="11" />
<hkern u1="v" u2="&#x1fc;" k="34" />
<hkern u1="v" u2="&#x1ef2;" k="25" />
<hkern u1="v" u2="&#x17b;" k="12" />
<hkern u1="v" u2="&#x179;" k="12" />
<hkern u1="v" u2="&#x176;" k="25" />
<hkern u1="v" u2="&#x164;" k="47" />
<hkern u1="v" u2="&#x15d;" k="8" />
<hkern u1="v" u2="&#x15b;" k="8" />
<hkern u1="v" u2="&#x151;" k="11" />
<hkern u1="v" u2="&#x134;" k="57" />
<hkern u1="v" u2="&#x123;" k="10" />
<hkern u1="v" u2="&#x121;" k="10" />
<hkern u1="v" u2="&#x11d;" k="10" />
<hkern u1="v" u2="&#x11b;" k="11" />
<hkern u1="v" u2="&#x117;" k="11" />
<hkern u1="v" u2="&#x14f;" k="11" />
<hkern u1="v" u2="&#x11f;" k="10" />
<hkern u1="v" u2="&#x115;" k="11" />
<hkern u1="v" u2="&#x14d;" k="11" />
<hkern u1="v" u2="&#x113;" k="11" />
<hkern u1="v" u2="&#x10f;" k="10" />
<hkern u1="v" u2="&#x10d;" k="11" />
<hkern u1="v" u2="&#x10b;" k="11" />
<hkern u1="v" u2="&#x109;" k="11" />
<hkern u1="v" u2="&#x107;" k="11" />
<hkern u1="v" u2="&#x103;" k="9" />
<hkern u1="v" u2="&#x102;" k="26" />
<hkern u1="v" u2="&#x101;" k="9" />
<hkern u1="v" u2="&#x100;" k="26" />
<hkern u1="v" u2="&#xc3;" k="26" />
<hkern u1="v" u2="&#xf5;" k="11" />
<hkern u1="v" u2="&#xe3;" k="9" />
<hkern u1="v" u2="&#x2039;" k="18" />
<hkern u1="v" u2="&#xc6;" k="34" />
<hkern u1="v" u2="&#x153;" k="11" />
<hkern u1="v" u2="&#xe6;" k="9" />
<hkern u1="v" u2="&#xf8;" k="11" />
<hkern u1="v" u2="&#xc5;" k="26" />
<hkern u1="v" u2="&#xe5;" k="9" />
<hkern u1="v" u2="&#x17d;" k="12" />
<hkern u1="v" u2="&#x161;" k="8" />
<hkern u1="v" u2="g" k="10" />
<hkern u1="v" u2="s" k="8" />
<hkern u1="v" u2="&#xab;" k="18" />
<hkern u1="v" u2="&#x201e;" k="33" />
<hkern u1="v" u2="&#x201a;" k="33" />
<hkern u1="v" u2="&#xc1;" k="26" />
<hkern u1="v" u2="&#xc2;" k="26" />
<hkern u1="v" u2="&#x178;" k="25" />
<hkern u1="v" u2="&#xc0;" k="26" />
<hkern u1="v" u2="&#xf6;" k="11" />
<hkern u1="v" u2="&#xf4;" k="11" />
<hkern u1="v" u2="&#xf2;" k="11" />
<hkern u1="v" u2="&#xf3;" k="11" />
<hkern u1="v" u2="&#xeb;" k="11" />
<hkern u1="v" u2="&#xea;" k="11" />
<hkern u1="v" u2="&#xe8;" k="11" />
<hkern u1="v" u2="&#xe9;" k="11" />
<hkern u1="v" u2="&#xe4;" k="9" />
<hkern u1="v" u2="&#xe2;" k="9" />
<hkern u1="v" u2="&#xe0;" k="9" />
<hkern u1="v" u2="&#xe1;" k="9" />
<hkern u1="v" u2="&#xc4;" k="26" />
<hkern u1="v" u2="&#xdd;" k="25" />
<hkern u1="v" u2="&#x2026;" k="33" />
<hkern u1="v" u2="&#xe7;" k="11" />
<hkern u1="v" u2="Y" k="25" />
<hkern u1="v" u2="Z" k="12" />
<hkern u1="v" u2="J" k="57" />
<hkern u1="v" u2="T" k="47" />
<hkern u1="v" u2="A" k="26" />
<hkern u1="v" u2="&#x2c;" k="33" />
<hkern u1="v" u2="&#x2e;" k="33" />
<hkern u1="v" u2="c" k="11" />
<hkern u1="v" u2="e" k="11" />
<hkern u1="v" u2="o" k="11" />
<hkern u1="v" u2="a" k="9" />
<hkern u1="v" u2="q" k="10" />
<hkern u1="v" u2="&#x29;" k="26" />
<hkern u1="v" u2="&#x2122;" k="8" />
<hkern u1="v" u2="&#xf0;" k="15" />
<hkern u1="v" u2="&#x7d;" k="17" />
<hkern u1="v" u2="]" k="17" />
<hkern u1="v" u2="&#x2f;" k="26" />
<hkern u1="v" u2="X" k="27" />
<hkern u1="v" u2="&#x20;" k="22" />
<hkern u1="w" u2="d" k="7" />
<hkern u1="w" u2="&#x29;" k="25" />
<hkern u1="w" u2="&#x2039;" k="13" />
<hkern u1="w" u2="&#xc6;" k="27" />
<hkern u1="w" u2="&#xf0;" k="10" />
<hkern u1="w" u2="&#x7d;" k="17" />
<hkern u1="w" u2="s" k="5" />
<hkern u1="w" u2="]" k="17" />
<hkern u1="w" u2="&#x2f;" k="17" />
<hkern u1="w" u2="X" k="23" />
<hkern u1="w" u2="Y" k="27" />
<hkern u1="w" u2="Z" k="10" />
<hkern u1="w" u2="J" k="34" />
<hkern u1="w" u2="T" k="49" />
<hkern u1="w" u2="A" k="19" />
<hkern u1="w" u2="&#x2e;" k="24" />
<hkern u1="w" u2="o" k="7" />
<hkern u1="w" u2="a" k="6" />
<hkern u1="w" u2="&#x20;" k="20" />
<hkern u1="z" u2="d" k="5" />
<hkern u1="z" u2="&#x29;" k="19" />
<hkern u1="z" u2="&#x2039;" k="20" />
<hkern u1="z" u2="&#x2122;" k="17" />
<hkern u1="z" u2="&#xf0;" k="8" />
<hkern u1="z" u2="&#x7d;" k="22" />
<hkern u1="z" u2="]" k="22" />
<hkern u1="z" u2="&#x2d;" k="13" />
<hkern u1="z" u2="U" k="5" />
<hkern u1="z" u2="Y" k="46" />
<hkern u1="z" u2="W" k="12" />
<hkern u1="z" u2="V" k="22" />
<hkern u1="z" u2="T" k="73" />
<hkern u1="z" u2="o" k="5" />
<hkern u1="y" u2="d" k="11" />
<hkern u1="y" u2="&#x29;" k="25" />
<hkern u1="y" u2="&#x2039;" k="19" />
<hkern u1="y" u2="&#xc6;" k="34" />
<hkern u1="y" u2="&#xf0;" k="15" />
<hkern u1="y" u2="&#x7d;" k="16" />
<hkern u1="y" u2="s" k="8" />
<hkern u1="y" u2="]" k="16" />
<hkern u1="y" u2="&#x2f;" k="27" />
<hkern u1="y" u2="X" k="27" />
<hkern u1="y" u2="Y" k="24" />
<hkern u1="y" u2="Z" k="12" />
<hkern u1="y" u2="J" k="59" />
<hkern u1="y" u2="T" k="46" />
<hkern u1="y" u2="A" k="26" />
<hkern u1="y" u2="&#x2e;" k="34" />
<hkern u1="y" u2="o" k="11" />
<hkern u1="y" u2="a" k="10" />
<hkern u1="y" u2="&#x20;" k="22" />
<hkern u1="x" u2="d" k="18" />
<hkern u1="x" u2="&#x105;" k="4" />
<hkern u1="x" u2="&#x119;" k="19" />
<hkern u1="x" u2="&#x1fb;" k="4" />
<hkern u1="x" u2="&#x166;" k="62" />
<hkern u1="x" u2="&#x219;" k="7" />
<hkern u1="x" u2="&#x218;" k="7" />
<hkern u1="x" u2="&#x21a;" k="62" />
<hkern u1="x" u2="&#x162;" k="62" />
<hkern u1="x" u2="&#x15f;" k="7" />
<hkern u1="x" u2="&#x15e;" k="7" />
<hkern u1="x" u2="&#x122;" k="14" />
<hkern u1="x" u2="&#x111;" k="18" />
<hkern u1="x" u2="&#x1fd;" k="4" />
<hkern u1="x" u2="&#x1ff;" k="19" />
<hkern u1="x" u2="&#x1fe;" k="14" />
<hkern u1="x" u2="&#x1ef2;" k="35" />
<hkern u1="x" u2="&#x176;" k="35" />
<hkern u1="x" u2="&#x164;" k="62" />
<hkern u1="x" u2="&#x15d;" k="7" />
<hkern u1="x" u2="&#x15c;" k="7" />
<hkern u1="x" u2="&#x15b;" k="7" />
<hkern u1="x" u2="&#x15a;" k="7" />
<hkern u1="x" u2="&#x151;" k="19" />
<hkern u1="x" u2="&#x150;" k="14" />
<hkern u1="x" u2="&#x123;" k="18" />
<hkern u1="x" u2="&#x121;" k="18" />
<hkern u1="x" u2="&#x11d;" k="18" />
<hkern u1="x" u2="&#x11c;" k="14" />
<hkern u1="x" u2="&#x11b;" k="19" />
<hkern u1="x" u2="&#x117;" k="19" />
<hkern u1="x" u2="&#x14f;" k="19" />
<hkern u1="x" u2="&#x14e;" k="14" />
<hkern u1="x" u2="&#x11f;" k="18" />
<hkern u1="x" u2="&#x11e;" k="14" />
<hkern u1="x" u2="&#x115;" k="19" />
<hkern u1="x" u2="&#x14d;" k="19" />
<hkern u1="x" u2="&#x14c;" k="14" />
<hkern u1="x" u2="&#x113;" k="19" />
<hkern u1="x" u2="&#x10f;" k="18" />
<hkern u1="x" u2="&#x10d;" k="19" />
<hkern u1="x" u2="&#x10c;" k="14" />
<hkern u1="x" u2="&#x10b;" k="19" />
<hkern u1="x" u2="&#x10a;" k="14" />
<hkern u1="x" u2="&#x109;" k="19" />
<hkern u1="x" u2="&#x108;" k="14" />
<hkern u1="x" u2="&#x107;" k="19" />
<hkern u1="x" u2="&#x106;" k="14" />
<hkern u1="x" u2="&#x103;" k="4" />
<hkern u1="x" u2="&#x101;" k="4" />
<hkern u1="x" u2="&#x120;" k="14" />
<hkern u1="x" u2="&#xad;" k="17" />
<hkern u1="x" u2="&#xd5;" k="14" />
<hkern u1="x" u2="&#xf5;" k="19" />
<hkern u1="x" u2="&#xe3;" k="4" />
<hkern u1="x" u2="&#x2039;" k="28" />
<hkern u1="x" u2="&#xd8;" k="14" />
<hkern u1="x" u2="&#x152;" k="14" />
<hkern u1="x" u2="&#x153;" k="19" />
<hkern u1="x" u2="&#xe6;" k="4" />
<hkern u1="x" u2="&#xf8;" k="19" />
<hkern u1="x" u2="&#xe5;" k="4" />
<hkern u1="x" u2="&#x161;" k="7" />
<hkern u1="x" u2="&#x160;" k="7" />
<hkern u1="x" u2="&#xd6;" k="14" />
<hkern u1="x" u2="&#xd2;" k="14" />
<hkern u1="x" u2="&#xd4;" k="14" />
<hkern u1="x" u2="&#xd3;" k="14" />
<hkern u1="x" u2="g" k="18" />
<hkern u1="x" u2="s" k="7" />
<hkern u1="x" u2="&#xab;" k="28" />
<hkern u1="x" u2="&#xc7;" k="14" />
<hkern u1="x" u2="&#x178;" k="35" />
<hkern u1="x" u2="&#xf6;" k="19" />
<hkern u1="x" u2="&#xf4;" k="19" />
<hkern u1="x" u2="&#xf2;" k="19" />
<hkern u1="x" u2="&#xf3;" k="19" />
<hkern u1="x" u2="&#xeb;" k="19" />
<hkern u1="x" u2="&#xea;" k="19" />
<hkern u1="x" u2="&#xe8;" k="19" />
<hkern u1="x" u2="&#xe9;" k="19" />
<hkern u1="x" u2="&#xe4;" k="4" />
<hkern u1="x" u2="&#xe2;" k="4" />
<hkern u1="x" u2="&#xe0;" k="4" />
<hkern u1="x" u2="&#xe1;" k="4" />
<hkern u1="x" u2="&#xdd;" k="35" />
<hkern u1="x" u2="&#x2014;" k="17" />
<hkern u1="x" u2="&#x2013;" k="17" />
<hkern u1="x" u2="&#xe7;" k="19" />
<hkern u1="x" u2="&#x2d;" k="17" />
<hkern u1="x" u2="G" k="14" />
<hkern u1="x" u2="C" k="14" />
<hkern u1="x" u2="Y" k="35" />
<hkern u1="x" u2="Q" k="14" />
<hkern u1="x" u2="T" k="62" />
<hkern u1="x" u2="S" k="7" />
<hkern u1="x" u2="O" k="14" />
<hkern u1="x" u2="c" k="19" />
<hkern u1="x" u2="e" k="19" />
<hkern u1="x" u2="o" k="19" />
<hkern u1="x" u2="a" k="4" />
<hkern u1="x" u2="q" k="18" />
<hkern u1="x" u2="&#x29;" k="12" />
<hkern u1="x" u2="&#x2122;" k="15" />
<hkern u1="x" u2="&#xf0;" k="22" />
<hkern u1="x" u2="&#x7d;" k="18" />
<hkern u1="x" u2="]" k="18" />
<hkern u1="x" u2="V" k="14" />
<hkern u1="&#x2e;" g2="seven.plf" k="13" />
<hkern u1="&#x2e;" g2="one.plf" k="44" />
<hkern u1="&#x2e;" u2="f" k="10" />
<hkern u1="&#x2e;" u2="&#x27;" k="104" />
<hkern u1="&#x2e;" u2="&#x2018;" k="107" />
<hkern u1="&#x2e;" u2="&#x2019;" k="108" />
<hkern u1="&#x2e;" u2="U" k="8" />
<hkern u1="&#x2e;" u2="Y" k="62" />
<hkern u1="&#x2e;" u2="W" k="40" />
<hkern u1="&#x2e;" u2="V" k="54" />
<hkern u1="&#x2e;" u2="T" k="49" />
<hkern u1="&#x2e;" u2="O" k="9" />
<hkern u1="&#x2e;" u2="y" k="36" />
<hkern u1="&#x2e;" u2="w" k="24" />
<hkern u1="&#x2e;" u2="v" k="33" />
<hkern u1="&#x2c;" g2="seven.plf" k="13" />
<hkern u1="&#x2c;" g2="one.plf" k="44" />
<hkern u1="&#x2c;" u2="f" k="10" />
<hkern u1="&#x2c;" u2="&#x27;" k="104" />
<hkern u1="&#x2c;" u2="&#x2018;" k="107" />
<hkern u1="&#x2c;" u2="&#x2019;" k="108" />
<hkern u1="&#x2c;" u2="U" k="8" />
<hkern u1="&#x2c;" u2="Y" k="62" />
<hkern u1="&#x2c;" u2="W" k="40" />
<hkern u1="&#x2c;" u2="V" k="54" />
<hkern u1="&#x2c;" u2="T" k="49" />
<hkern u1="&#x2c;" u2="O" k="9" />
<hkern u1="&#x2c;" u2="y" k="36" />
<hkern u1="&#x2c;" u2="w" k="24" />
<hkern u1="&#x2c;" u2="v" k="33" />
<hkern u1="A" u2="&#xba;" k="30" />
<hkern u1="A" u2="&#xaa;" k="28" />
<hkern u1="A" u2="d" k="10" />
<hkern u1="A" u2="&#x166;" k="45" />
<hkern u1="A" g2="hyphen.case" k="10" />
<hkern u1="A" g2="guilsinglleft.case" k="24" />
<hkern u1="A" u2="&#x29;" k="16" />
<hkern u1="A" g2="nine.plf" k="10" />
<hkern u1="A" g2="seven.plf" k="14" />
<hkern u1="A" g2="six.plf" k="11" />
<hkern u1="A" g2="one.plf" k="44" />
<hkern u1="A" g2="zero.plf" k="12" />
<hkern u1="A" u2="f" k="17" />
<hkern u1="A" u2="&#x2039;" k="13" />
<hkern u1="A" u2="&#x2a;" k="34" />
<hkern u1="A" u2="&#x2122;" k="43" />
<hkern u1="A" u2="&#x27;" k="38" />
<hkern u1="A" u2="&#xf0;" k="9" />
<hkern u1="A" u2="&#x7d;" k="33" />
<hkern u1="A" u2="&#xae;" k="16" />
<hkern u1="A" u2="s" k="6" />
<hkern u1="A" u2="&#x2018;" k="38" />
<hkern u1="A" u2="&#x2019;" k="40" />
<hkern u1="A" u2="&#xa9;" k="16" />
<hkern u1="A" u2="&#x3f;" k="34" />
<hkern u1="A" u2="\" k="50" />
<hkern u1="A" u2="]" k="33" />
<hkern u1="A" u2="U" k="13" />
<hkern u1="A" u2="Y" k="57" />
<hkern u1="A" u2="W" k="33" />
<hkern u1="A" u2="V" k="42" />
<hkern u1="A" u2="T" k="53" />
<hkern u1="A" u2="S" k="10" />
<hkern u1="A" u2="O" k="17" />
<hkern u1="A" u2="y" k="28" />
<hkern u1="A" u2="w" k="19" />
<hkern u1="A" u2="v" k="26" />
<hkern u1="A" u2="u" k="6" />
<hkern u1="A" u2="o" k="10" />
<hkern u1="A" u2="a" k="5" />
<hkern u1="A" u2="&#x20;" k="26" />
<hkern u1="F" u2="d" k="10" />
<hkern u1="F" u2="&#x105;" k="16" />
<hkern u1="F" u2="&#x104;" k="34" />
<hkern u1="F" u2="&#x173;" k="9" />
<hkern u1="F" u2="&#x119;" k="10" />
<hkern u1="F" u2="&#x1fa;" k="34" />
<hkern u1="F" u2="&#x1fb;" k="16" />
<hkern u1="F" u2="&#x219;" k="13" />
<hkern u1="F" u2="&#x218;" k="8" />
<hkern u1="F" u2="&#x15f;" k="13" />
<hkern u1="F" u2="&#x15e;" k="8" />
<hkern u1="F" u2="&#x157;" k="12" />
<hkern u1="F" u2="&#x146;" k="12" />
<hkern u1="F" u2="&#x149;" k="12" />
<hkern u1="F" u2="&#x111;" k="10" />
<hkern u1="F" u2="&#x1fd;" k="16" />
<hkern u1="F" u2="&#x1ff;" k="10" />
<hkern u1="F" u2="&#x14b;" k="12" />
<hkern u1="F" u2="&#x1fc;" k="51" />
<hkern u1="F" u2="&#x17c;" k="14" />
<hkern u1="F" u2="&#x17a;" k="14" />
<hkern u1="F" u2="&#x171;" k="9" />
<hkern u1="F" u2="&#x16f;" k="9" />
<hkern u1="F" u2="&#x169;" k="9" />
<hkern u1="F" u2="&#x15d;" k="13" />
<hkern u1="F" u2="&#x15c;" k="8" />
<hkern u1="F" u2="&#x15b;" k="13" />
<hkern u1="F" u2="&#x15a;" k="8" />
<hkern u1="F" u2="&#x159;" k="12" />
<hkern u1="F" u2="&#x155;" k="12" />
<hkern u1="F" u2="&#x151;" k="10" />
<hkern u1="F" u2="&#x148;" k="12" />
<hkern u1="F" u2="&#x144;" k="12" />
<hkern u1="F" u2="&#x134;" k="90" />
<hkern u1="F" u2="&#x123;" k="10" />
<hkern u1="F" u2="&#x121;" k="10" />
<hkern u1="F" u2="&#x11d;" k="10" />
<hkern u1="F" u2="&#x11b;" k="10" />
<hkern u1="F" u2="&#x117;" k="10" />
<hkern u1="F" u2="&#x16d;" k="9" />
<hkern u1="F" u2="&#x14f;" k="10" />
<hkern u1="F" u2="&#x11f;" k="10" />
<hkern u1="F" u2="&#x115;" k="10" />
<hkern u1="F" u2="&#x16b;" k="9" />
<hkern u1="F" u2="&#x14d;" k="10" />
<hkern u1="F" u2="&#x113;" k="10" />
<hkern u1="F" u2="&#x10f;" k="10" />
<hkern u1="F" u2="&#x10d;" k="10" />
<hkern u1="F" u2="&#x10b;" k="10" />
<hkern u1="F" u2="&#x109;" k="10" />
<hkern u1="F" u2="&#x107;" k="10" />
<hkern u1="F" u2="&#x103;" k="16" />
<hkern u1="F" u2="&#x102;" k="34" />
<hkern u1="F" u2="&#x101;" k="16" />
<hkern u1="F" u2="&#x100;" k="34" />
<hkern u1="F" u2="&#xc3;" k="34" />
<hkern u1="F" u2="&#xf5;" k="10" />
<hkern u1="F" u2="&#xf1;" k="12" />
<hkern u1="F" u2="&#xe3;" k="16" />
<hkern u1="F" u2="n" k="12" />
<hkern u1="F" u2="&#xc6;" k="51" />
<hkern u1="F" u2="&#x153;" k="10" />
<hkern u1="F" u2="&#xe6;" k="16" />
<hkern u1="F" u2="&#xf8;" k="10" />
<hkern u1="F" u2="&#xc5;" k="34" />
<hkern u1="F" u2="&#xe5;" k="16" />
<hkern u1="F" u2="&#x17e;" k="14" />
<hkern u1="F" u2="&#x161;" k="13" />
<hkern u1="F" u2="&#x160;" k="8" />
<hkern u1="F" u2="g" k="10" />
<hkern u1="F" u2="s" k="13" />
<hkern u1="F" u2="&#x201e;" k="76" />
<hkern u1="F" u2="&#x201a;" k="76" />
<hkern u1="F" u2="&#xc1;" k="34" />
<hkern u1="F" u2="&#xc2;" k="34" />
<hkern u1="F" u2="&#xc0;" k="34" />
<hkern u1="F" u2="&#xfc;" k="9" />
<hkern u1="F" u2="&#xfb;" k="9" />
<hkern u1="F" u2="&#xf9;" k="9" />
<hkern u1="F" u2="&#xfa;" k="9" />
<hkern u1="F" u2="&#xf6;" k="10" />
<hkern u1="F" u2="&#xf4;" k="10" />
<hkern u1="F" u2="&#xf2;" k="10" />
<hkern u1="F" u2="&#xf3;" k="10" />
<hkern u1="F" u2="&#xeb;" k="10" />
<hkern u1="F" u2="&#xea;" k="10" />
<hkern u1="F" u2="&#xe8;" k="10" />
<hkern u1="F" u2="&#xe9;" k="10" />
<hkern u1="F" u2="&#xe4;" k="16" />
<hkern u1="F" u2="&#xe2;" k="16" />
<hkern u1="F" u2="&#xe0;" k="16" />
<hkern u1="F" u2="&#xe1;" k="16" />
<hkern u1="F" u2="&#xc4;" k="34" />
<hkern u1="F" u2="&#x2026;" k="76" />
<hkern u1="F" u2="&#xe7;" k="10" />
<hkern u1="F" u2="J" k="90" />
<hkern u1="F" u2="S" k="8" />
<hkern u1="F" u2="A" k="34" />
<hkern u1="F" u2="&#x2c;" k="76" />
<hkern u1="F" u2="&#x2e;" k="76" />
<hkern u1="F" u2="z" k="14" />
<hkern u1="F" u2="r" k="12" />
<hkern u1="F" u2="m" k="12" />
<hkern u1="F" u2="c" k="10" />
<hkern u1="F" u2="e" k="10" />
<hkern u1="F" u2="u" k="9" />
<hkern u1="F" u2="o" k="10" />
<hkern u1="F" u2="a" k="16" />
<hkern u1="F" u2="q" k="10" />
<hkern u1="F" u2="p" k="12" />
<hkern u1="F" u2="&#xdf;" k="6" />
<hkern u1="F" u2="&#xf0;" k="14" />
<hkern u1="F" u2="&#x131;" k="12" />
<hkern u1="F" u2="&#x2f;" k="33" />
<hkern u1="F" u2="x" k="9" />
<hkern u1="F" u2="&#x20;" k="16" />
<hkern u1="I" u2="d" k="6" />
<hkern u1="I" u2="&#xf0;" k="7" />
<hkern u1="I" u2="o" k="6" />
<hkern u1="M" u2="d" k="6" />
<hkern u1="M" u2="&#xf0;" k="7" />
<hkern u1="M" u2="o" k="6" />
<hkern u1="N" u2="d" k="6" />
<hkern u1="N" u2="&#xf0;" k="7" />
<hkern u1="N" u2="o" k="6" />
<hkern u1="R" u2="d" k="12" />
<hkern u1="R" g2="four.plf" k="11" />
<hkern u1="R" u2="&#x2039;" k="17" />
<hkern u1="R" u2="&#xf0;" k="18" />
<hkern u1="R" u2="&#x7d;" k="11" />
<hkern u1="R" u2="s" k="6" />
<hkern u1="R" u2="]" k="11" />
<hkern u1="R" u2="Y" k="15" />
<hkern u1="R" u2="W" k="10" />
<hkern u1="R" u2="V" k="14" />
<hkern u1="R" u2="A" k="5" />
<hkern u1="R" u2="u" k="6" />
<hkern u1="R" u2="o" k="13" />
<hkern u1="R" u2="a" k="7" />
<hkern u1="S" g2="parenright.case" k="21" />
<hkern u1="S" g2="braceright.case" k="23" />
<hkern u1="S" g2="bracketright.case" k="23" />
<hkern u1="S" u2="&#x29;" k="19" />
<hkern u1="S" u2="&#x7d;" k="17" />
<hkern u1="S" u2="]" k="17" />
<hkern u1="S" u2="X" k="6" />
<hkern u1="S" u2="Y" k="21" />
<hkern u1="S" u2="W" k="12" />
<hkern u1="S" u2="V" k="17" />
<hkern u1="S" u2="T" k="5" />
<hkern u1="S" u2="A" k="9" />
<hkern u1="S" u2="x" k="9" />
<hkern u1="T" u2="d" k="80" />
<hkern u1="T" u2="&#x127;" k="-13" />
<hkern u1="T" u2="&#x159;" k="78" />
<hkern u1="T" u2="&#x129;" k="-28" />
<hkern u1="T" u2="&#x12d;" k="-9" />
<hkern u1="T" u2="&#x12b;" k="-20" />
<hkern u1="T" g2="hyphen.case" k="41" />
<hkern u1="T" g2="guilsinglleft.case" k="58" />
<hkern u1="T" g2="guilsinglright.case" k="36" />
<hkern u1="T" g2="four.plf" k="49" />
<hkern u1="T" u2="n" k="87" />
<hkern u1="T" u2="f" k="20" />
<hkern u1="T" u2="&#x203a;" k="59" />
<hkern u1="T" u2="&#x2039;" k="66" />
<hkern u1="T" u2="&#xdf;" k="16" />
<hkern u1="T" u2="&#xc6;" k="58" />
<hkern u1="T" u2="&#xf0;" k="56" />
<hkern u1="T" u2="&#x131;" k="87" />
<hkern u1="T" u2="&#xae;" k="17" />
<hkern u1="T" u2="s" k="73" />
<hkern u1="T" u2="&#xa9;" k="17" />
<hkern u1="T" u2="&#xef;" k="-7" />
<hkern u1="T" u2="&#x2f;" k="47" />
<hkern u1="T" u2="&#x3a;" k="29" />
<hkern u1="T" u2="&#x2d;" k="42" />
<hkern u1="T" u2="J" k="55" />
<hkern u1="T" u2="O" k="16" />
<hkern u1="T" u2="A" k="53" />
<hkern u1="T" u2="&#x2e;" k="49" />
<hkern u1="T" u2="x" k="60" />
<hkern u1="T" u2="y" k="47" />
<hkern u1="T" u2="z" k="76" />
<hkern u1="T" u2="w" k="49" />
<hkern u1="T" u2="v" k="47" />
<hkern u1="T" u2="u" k="87" />
<hkern u1="T" u2="o" k="81" />
<hkern u1="T" u2="a" k="74" />
<hkern u1="T" u2="&#x20;" k="26" />
<hkern u1="P" u2="d" k="7" />
<hkern u1="P" u2="&#x105;" k="7" />
<hkern u1="P" u2="&#x104;" k="42" />
<hkern u1="P" u2="&#x119;" k="8" />
<hkern u1="P" u2="&#x1fa;" k="42" />
<hkern u1="P" u2="&#x1fb;" k="7" />
<hkern u1="P" u2="&#x111;" k="7" />
<hkern u1="P" u2="&#x1fd;" k="7" />
<hkern u1="P" u2="&#x1ff;" k="8" />
<hkern u1="P" u2="&#x1fc;" k="56" />
<hkern u1="P" u2="&#x1ef2;" k="8" />
<hkern u1="P" u2="&#x17b;" k="6" />
<hkern u1="P" u2="&#x179;" k="6" />
<hkern u1="P" u2="&#x176;" k="8" />
<hkern u1="P" u2="&#x151;" k="8" />
<hkern u1="P" u2="&#x134;" k="71" />
<hkern u1="P" u2="&#x123;" k="7" />
<hkern u1="P" u2="&#x121;" k="7" />
<hkern u1="P" u2="&#x11d;" k="7" />
<hkern u1="P" u2="&#x11b;" k="8" />
<hkern u1="P" u2="&#x117;" k="8" />
<hkern u1="P" u2="&#x14f;" k="8" />
<hkern u1="P" u2="&#x11f;" k="7" />
<hkern u1="P" u2="&#x115;" k="8" />
<hkern u1="P" u2="&#x14d;" k="8" />
<hkern u1="P" u2="&#x113;" k="8" />
<hkern u1="P" u2="&#x10f;" k="7" />
<hkern u1="P" u2="&#x10d;" k="8" />
<hkern u1="P" u2="&#x10b;" k="8" />
<hkern u1="P" u2="&#x109;" k="8" />
<hkern u1="P" u2="&#x107;" k="8" />
<hkern u1="P" u2="&#x103;" k="7" />
<hkern u1="P" u2="&#x102;" k="42" />
<hkern u1="P" u2="&#x101;" k="7" />
<hkern u1="P" u2="&#x100;" k="42" />
<hkern u1="P" u2="&#xc3;" k="42" />
<hkern u1="P" u2="&#xf5;" k="8" />
<hkern u1="P" u2="&#xe3;" k="7" />
<hkern u1="P" u2="&#x2039;" k="10" />
<hkern u1="P" u2="&#xc6;" k="56" />
<hkern u1="P" u2="&#x153;" k="8" />
<hkern u1="P" u2="&#xe6;" k="7" />
<hkern u1="P" u2="&#xf8;" k="8" />
<hkern u1="P" u2="&#xc5;" k="42" />
<hkern u1="P" u2="&#xe5;" k="7" />
<hkern u1="P" u2="&#x17d;" k="6" />
<hkern u1="P" u2="g" k="7" />
<hkern u1="P" u2="&#xab;" k="10" />
<hkern u1="P" u2="&#x201e;" k="81" />
<hkern u1="P" u2="&#x201a;" k="81" />
<hkern u1="P" u2="&#xc1;" k="42" />
<hkern u1="P" u2="&#xc2;" k="42" />
<hkern u1="P" u2="&#x178;" k="8" />
<hkern u1="P" u2="&#xc0;" k="42" />
<hkern u1="P" u2="&#xf6;" k="8" />
<hkern u1="P" u2="&#xf4;" k="8" />
<hkern u1="P" u2="&#xf2;" k="8" />
<hkern u1="P" u2="&#xf3;" k="8" />
<hkern u1="P" u2="&#xeb;" k="8" />
<hkern u1="P" u2="&#xea;" k="8" />
<hkern u1="P" u2="&#xe8;" k="8" />
<hkern u1="P" u2="&#xe9;" k="8" />
<hkern u1="P" u2="&#xe4;" k="7" />
<hkern u1="P" u2="&#xe2;" k="7" />
<hkern u1="P" u2="&#xe0;" k="7" />
<hkern u1="P" u2="&#xe1;" k="7" />
<hkern u1="P" u2="&#xc4;" k="42" />
<hkern u1="P" u2="&#xdd;" k="8" />
<hkern u1="P" u2="&#x2026;" k="81" />
<hkern u1="P" u2="&#xe7;" k="8" />
<hkern u1="P" u2="Y" k="8" />
<hkern u1="P" u2="Z" k="6" />
<hkern u1="P" u2="J" k="71" />
<hkern u1="P" u2="A" k="42" />
<hkern u1="P" u2="&#x2c;" k="81" />
<hkern u1="P" u2="&#x2e;" k="81" />
<hkern u1="P" u2="c" k="8" />
<hkern u1="P" u2="e" k="8" />
<hkern u1="P" u2="o" k="8" />
<hkern u1="P" u2="a" k="7" />
<hkern u1="P" u2="q" k="7" />
<hkern u1="P" g2="parenright.case" k="23" />
<hkern u1="P" g2="braceright.case" k="27" />
<hkern u1="P" g2="bracketright.case" k="27" />
<hkern u1="P" u2="&#x29;" k="11" />
<hkern u1="P" g2="four.plf" k="12" />
<hkern u1="P" u2="&#xf0;" k="13" />
<hkern u1="P" u2="&#x2f;" k="39" />
<hkern u1="P" u2="X" k="20" />
<hkern u1="P" u2="V" k="8" />
<hkern u1="P" u2="&#x20;" k="21" />
<hkern u1="H" u2="d" k="6" />
<hkern u1="H" u2="&#xf0;" k="7" />
<hkern u1="H" u2="o" k="6" />
<hkern u1="B" u2="&#x104;" k="11" />
<hkern u1="B" u2="&#x1fa;" k="11" />
<hkern u1="B" u2="&#x166;" k="7" />
<hkern u1="B" u2="&#x21a;" k="7" />
<hkern u1="B" u2="&#x162;" k="7" />
<hkern u1="B" u2="&#x1ef2;" k="19" />
<hkern u1="B" u2="&#x1e84;" k="12" />
<hkern u1="B" u2="&#x1e82;" k="12" />
<hkern u1="B" u2="&#x1e80;" k="12" />
<hkern u1="B" u2="&#x176;" k="19" />
<hkern u1="B" u2="&#x174;" k="12" />
<hkern u1="B" u2="&#x164;" k="7" />
<hkern u1="B" u2="&#x134;" k="5" />
<hkern u1="B" u2="&#x102;" k="11" />
<hkern u1="B" u2="&#x100;" k="11" />
<hkern u1="B" u2="&#xc3;" k="11" />
<hkern u1="B" u2="&#xc5;" k="11" />
<hkern u1="B" u2="&#xc1;" k="11" />
<hkern u1="B" u2="&#xc2;" k="11" />
<hkern u1="B" u2="&#x178;" k="19" />
<hkern u1="B" u2="&#xc0;" k="11" />
<hkern u1="B" u2="&#xc4;" k="11" />
<hkern u1="B" u2="&#xdd;" k="19" />
<hkern u1="B" u2="Y" k="19" />
<hkern u1="B" u2="W" k="12" />
<hkern u1="B" u2="J" k="5" />
<hkern u1="B" u2="T" k="7" />
<hkern u1="B" u2="A" k="11" />
<hkern u1="B" g2="parenright.case" k="22" />
<hkern u1="B" g2="braceright.case" k="26" />
<hkern u1="B" g2="bracketright.case" k="26" />
<hkern u1="B" u2="&#x29;" k="20" />
<hkern u1="B" u2="&#x7d;" k="18" />
<hkern u1="B" u2="]" k="18" />
<hkern u1="B" u2="X" k="10" />
<hkern u1="B" u2="V" k="16" />
<hkern u1="B" u2="x" k="11" />
<hkern u1="D" u2="&#x166;" k="8" />
<hkern u1="D" g2="parenright.case" k="29" />
<hkern u1="D" g2="braceright.case" k="31" />
<hkern u1="D" g2="bracketright.case" k="31" />
<hkern u1="D" u2="&#x29;" k="27" />
<hkern u1="D" g2="seven.plf" k="12" />
<hkern u1="D" u2="&#xc6;" k="12" />
<hkern u1="D" u2="&#x7d;" k="25" />
<hkern u1="D" u2="]" k="25" />
<hkern u1="D" u2="X" k="26" />
<hkern u1="D" u2="Y" k="30" />
<hkern u1="D" u2="Z" k="8" />
<hkern u1="D" u2="W" k="15" />
<hkern u1="D" u2="V" k="21" />
<hkern u1="D" u2="J" k="17" />
<hkern u1="D" u2="T" k="17" />
<hkern u1="D" u2="A" k="17" />
<hkern u1="D" u2="x" k="13" />
<hkern u1="D" u2="z" k="5" />
<hkern u1="J" u2="d" k="6" />
<hkern u1="J" u2="&#xf0;" k="7" />
<hkern u1="J" u2="o" k="6" />
<hkern u1="V" g2="zero.plfslash" k="16" />
<hkern u1="V" u2="d" k="46" />
<hkern u1="V" u2="&#x105;" k="44" />
<hkern u1="V" u2="&#x104;" k="42" />
<hkern u1="V" u2="&#x173;" k="33" />
<hkern u1="V" u2="&#x119;" k="46" />
<hkern u1="V" u2="&#x1fa;" k="42" />
<hkern u1="V" u2="&#x1fb;" k="44" />
<hkern u1="V" g2="uni00AD.case" k="16" />
<hkern u1="V" u2="&#x219;" k="44" />
<hkern u1="V" u2="&#x218;" k="16" />
<hkern u1="V" u2="&#x15f;" k="44" />
<hkern u1="V" u2="&#x15e;" k="16" />
<hkern u1="V" u2="&#x157;" k="37" />
<hkern u1="V" u2="&#x146;" k="37" />
<hkern u1="V" u2="&#x122;" k="20" />
<hkern u1="V" u2="&#x149;" k="37" />
<hkern u1="V" u2="&#x111;" k="46" />
<hkern u1="V" u2="&#x1fd;" k="44" />
<hkern u1="V" u2="&#x1ff;" k="46" />
<hkern u1="V" u2="&#x1fe;" k="20" />
<hkern u1="V" u2="&#x14b;" k="37" />
<hkern u1="V" u2="&#x1fc;" k="52" />
<hkern u1="V" u2="&#x17c;" k="26" />
<hkern u1="V" u2="&#x17a;" k="26" />
<hkern u1="V" u2="&#x171;" k="33" />
<hkern u1="V" u2="&#x16f;" k="33" />
<hkern u1="V" u2="&#x169;" k="33" />
<hkern u1="V" u2="&#x15d;" k="44" />
<hkern u1="V" u2="&#x15c;" k="16" />
<hkern u1="V" u2="&#x15b;" k="44" />
<hkern u1="V" u2="&#x15a;" k="16" />
<hkern u1="V" u2="&#x155;" k="37" />
<hkern u1="V" u2="&#x151;" k="46" />
<hkern u1="V" u2="&#x150;" k="20" />
<hkern u1="V" u2="&#x148;" k="37" />
<hkern u1="V" u2="&#x144;" k="37" />
<hkern u1="V" u2="&#x134;" k="56" />
<hkern u1="V" u2="&#x123;" k="46" />
<hkern u1="V" u2="&#x121;" k="46" />
<hkern u1="V" u2="&#x11d;" k="46" />
<hkern u1="V" u2="&#x11c;" k="20" />
<hkern u1="V" u2="&#x11b;" k="46" />
<hkern u1="V" u2="&#x117;" k="46" />
<hkern u1="V" u2="&#x16d;" k="33" />
<hkern u1="V" u2="&#x14f;" k="46" />
<hkern u1="V" u2="&#x14e;" k="20" />
<hkern u1="V" u2="&#x11f;" k="46" />
<hkern u1="V" u2="&#x11e;" k="20" />
<hkern u1="V" u2="&#x115;" k="46" />
<hkern u1="V" u2="&#x16b;" k="33" />
<hkern u1="V" u2="&#x14d;" k="46" />
<hkern u1="V" u2="&#x14c;" k="20" />
<hkern u1="V" u2="&#x113;" k="46" />
<hkern u1="V" u2="&#x10f;" k="46" />
<hkern u1="V" u2="&#x10d;" k="46" />
<hkern u1="V" u2="&#x10c;" k="20" />
<hkern u1="V" u2="&#x10b;" k="46" />
<hkern u1="V" u2="&#x10a;" k="20" />
<hkern u1="V" u2="&#x109;" k="46" />
<hkern u1="V" u2="&#x108;" k="20" />
<hkern u1="V" u2="&#x107;" k="46" />
<hkern u1="V" u2="&#x106;" k="20" />
<hkern u1="V" u2="&#x103;" k="44" />
<hkern u1="V" u2="&#x102;" k="42" />
<hkern u1="V" u2="&#x101;" k="44" />
<hkern u1="V" u2="&#x100;" k="42" />
<hkern u1="V" u2="&#x120;" k="20" />
<hkern u1="V" u2="&#xad;" k="24" />
<hkern u1="V" g2="hyphen.case" k="16" />
<hkern u1="V" g2="emdash.case" k="16" />
<hkern u1="V" g2="endash.case" k="16" />
<hkern u1="V" g2="guilsinglleft.case" k="32" />
<hkern u1="V" g2="guillemotleft.case" k="32" />
<hkern u1="V" g2="guilsinglright.case" k="11" />
<hkern u1="V" g2="guillemotright.case" k="11" />
<hkern u1="V" g2="zero.plf" k="16" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xc3;" k="42" />
<hkern u1="V" u2="&#xf5;" k="46" />
<hkern u1="V" u2="&#xf1;" k="37" />
<hkern u1="V" u2="&#xe3;" k="44" />
<hkern u1="V" u2="&#x3b;" k="12" />
<hkern u1="V" u2="&#xbb;" k="22" />
<hkern u1="V" u2="n" k="37" />
<hkern u1="V" u2="&#x203a;" k="22" />
<hkern u1="V" u2="&#x2039;" k="42" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xc6;" k="52" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#x153;" k="46" />
<hkern u1="V" u2="&#xe6;" k="44" />
<hkern u1="V" u2="&#xf8;" k="46" />
<hkern u1="V" u2="&#xc5;" k="42" />
<hkern u1="V" u2="&#xe5;" k="44" />
<hkern u1="V" u2="&#x17e;" k="26" />
<hkern u1="V" u2="&#x161;" k="44" />
<hkern u1="V" u2="&#x160;" k="16" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="g" k="46" />
<hkern u1="V" u2="s" k="44" />
<hkern u1="V" u2="&#xab;" k="42" />
<hkern u1="V" u2="&#x201e;" k="54" />
<hkern u1="V" u2="&#x201a;" k="54" />
<hkern u1="V" u2="&#xc7;" k="20" />
<hkern u1="V" u2="&#xc1;" k="42" />
<hkern u1="V" u2="&#xc2;" k="42" />
<hkern u1="V" u2="&#xc0;" k="42" />
<hkern u1="V" u2="&#xfc;" k="33" />
<hkern u1="V" u2="&#xfb;" k="33" />
<hkern u1="V" u2="&#xf9;" k="33" />
<hkern u1="V" u2="&#xfa;" k="33" />
<hkern u1="V" u2="&#xf6;" k="46" />
<hkern u1="V" u2="&#xf4;" k="46" />
<hkern u1="V" u2="&#xf2;" k="46" />
<hkern u1="V" u2="&#xf3;" k="46" />
<hkern u1="V" u2="&#xeb;" k="46" />
<hkern u1="V" u2="&#xea;" k="46" />
<hkern u1="V" u2="&#xe8;" k="46" />
<hkern u1="V" u2="&#xe9;" k="46" />
<hkern u1="V" u2="&#xe4;" k="44" />
<hkern u1="V" u2="&#xe2;" k="44" />
<hkern u1="V" u2="&#xe0;" k="44" />
<hkern u1="V" u2="&#xe1;" k="44" />
<hkern u1="V" u2="&#xc4;" k="42" />
<hkern u1="V" u2="&#x2014;" k="24" />
<hkern u1="V" u2="&#x2013;" k="24" />
<hkern u1="V" u2="&#x3a;" k="12" />
<hkern u1="V" u2="&#x2026;" k="54" />
<hkern u1="V" u2="&#xe7;" k="46" />
<hkern u1="V" u2="&#x2d;" k="24" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="J" k="56" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="A" k="42" />
<hkern u1="V" u2="&#x2c;" k="54" />
<hkern u1="V" u2="&#x2e;" k="54" />
<hkern u1="V" u2="z" k="26" />
<hkern u1="V" u2="r" k="37" />
<hkern u1="V" u2="m" k="37" />
<hkern u1="V" u2="c" k="46" />
<hkern u1="V" u2="e" k="46" />
<hkern u1="V" u2="u" k="33" />
<hkern u1="V" u2="o" k="46" />
<hkern u1="V" u2="a" k="44" />
<hkern u1="V" u2="q" k="46" />
<hkern u1="V" u2="p" k="37" />
<hkern u1="V" u2="&#x159;" k="28" />
<hkern u1="V" g2="nine.plf" k="13" />
<hkern u1="V" g2="eight.plf" k="15" />
<hkern u1="V" g2="six.plf" k="17" />
<hkern u1="V" g2="four.plf" k="34" />
<hkern u1="V" g2="three.plf" k="14" />
<hkern u1="V" g2="two.plf" k="13" />
<hkern u1="V" u2="&#xdf;" k="19" />
<hkern u1="V" u2="&#xf0;" k="44" />
<hkern u1="V" u2="&#x131;" k="37" />
<hkern u1="V" u2="&#xae;" k="20" />
<hkern u1="V" u2="&#x40;" k="16" />
<hkern u1="V" u2="&#xa9;" k="20" />
<hkern u1="V" u2="&#x2f;" k="48" />
<hkern u1="V" u2="&#x26;" k="19" />
<hkern u1="V" u2="x" k="12" />
<hkern u1="V" u2="&#x20;" k="28" />
<hkern u1="W" u2="d" k="37" />
<hkern u1="W" u2="&#x159;" k="16" />
<hkern u1="W" g2="hyphen.case" k="9" />
<hkern u1="W" g2="guilsinglleft.case" k="22" />
<hkern u1="W" g2="eight.plf" k="11" />
<hkern u1="W" g2="six.plf" k="12" />
<hkern u1="W" g2="four.plf" k="22" />
<hkern u1="W" g2="three.plf" k="10" />
<hkern u1="W" g2="two.plf" k="10" />
<hkern u1="W" g2="zero.plf" k="11" />
<hkern u1="W" u2="n" k="26" />
<hkern u1="W" u2="&#x203a;" k="14" />
<hkern u1="W" u2="&#x2039;" k="32" />
<hkern u1="W" u2="&#xdf;" k="13" />
<hkern u1="W" u2="&#xc6;" k="43" />
<hkern u1="W" u2="&#xf0;" k="40" />
<hkern u1="W" u2="&#x131;" k="26" />
<hkern u1="W" u2="&#xae;" k="14" />
<hkern u1="W" u2="s" k="35" />
<hkern u1="W" u2="&#x40;" k="12" />
<hkern u1="W" u2="&#xa9;" k="14" />
<hkern u1="W" u2="&#x2f;" k="34" />
<hkern u1="W" u2="&#x2d;" k="13" />
<hkern u1="W" u2="&#x26;" k="15" />
<hkern u1="W" u2="J" k="44" />
<hkern u1="W" u2="S" k="11" />
<hkern u1="W" u2="O" k="15" />
<hkern u1="W" u2="A" k="33" />
<hkern u1="W" u2="&#x2e;" k="40" />
<hkern u1="W" u2="z" k="15" />
<hkern u1="W" u2="u" k="22" />
<hkern u1="W" u2="o" k="38" />
<hkern u1="W" u2="a" k="39" />
<hkern u1="W" u2="&#x20;" k="24" />
<hkern u1="C" g2="parenright.case" k="21" />
<hkern u1="C" g2="braceright.case" k="23" />
<hkern u1="C" g2="bracketright.case" k="23" />
<hkern u1="C" u2="&#x29;" k="18" />
<hkern u1="C" u2="&#x7d;" k="17" />
<hkern u1="C" u2="]" k="16" />
<hkern u1="C" u2="Y" k="16" />
<hkern u1="C" u2="W" k="7" />
<hkern u1="C" u2="V" k="12" />
<hkern u1="C" u2="A" k="7" />
<hkern u1="U" u2="d" k="6" />
<hkern u1="U" g2="parenright.case" k="14" />
<hkern u1="U" u2="i" k="5" />
<hkern u1="U" u2="&#xc6;" k="11" />
<hkern u1="U" u2="&#xf0;" k="7" />
<hkern u1="U" u2="s" k="5" />
<hkern u1="U" u2="J" k="14" />
<hkern u1="U" u2="A" k="13" />
<hkern u1="U" u2="&#x2e;" k="8" />
<hkern u1="U" u2="z" k="7" />
<hkern u1="U" u2="u" k="7" />
<hkern u1="U" u2="o" k="5" />
<hkern u1="U" u2="a" k="6" />
<hkern u1="X" u2="d" k="25" />
<hkern u1="X" u2="&#x105;" k="5" />
<hkern u1="X" u2="&#x173;" k="16" />
<hkern u1="X" u2="&#x119;" k="26" />
<hkern u1="X" u2="&#x1fb;" k="5" />
<hkern u1="X" u2="&#x167;" k="9" />
<hkern u1="X" g2="uni00AD.case" k="21" />
<hkern u1="X" u2="&#x219;" k="6" />
<hkern u1="X" u2="&#x218;" k="5" />
<hkern u1="X" u2="&#x21b;" k="9" />
<hkern u1="X" u2="&#x163;" k="9" />
<hkern u1="X" u2="&#x15f;" k="6" />
<hkern u1="X" u2="&#x15e;" k="5" />
<hkern u1="X" u2="&#x122;" k="23" />
<hkern u1="X" u2="&#x111;" k="25" />
<hkern u1="X" u2="&#x1fd;" k="5" />
<hkern u1="X" u2="&#x1ff;" k="26" />
<hkern u1="X" u2="&#x1fe;" k="23" />
<hkern u1="X" u2="&#x165;" k="9" />
<hkern u1="X" u2="&#x1ef3;" k="26" />
<hkern u1="X" u2="&#x1e85;" k="22" />
<hkern u1="X" u2="&#x1e83;" k="22" />
<hkern u1="X" u2="&#x1e81;" k="22" />
<hkern u1="X" u2="&#x177;" k="26" />
<hkern u1="X" u2="&#x175;" k="22" />
<hkern u1="X" u2="&#x171;" k="16" />
<hkern u1="X" u2="&#x16f;" k="16" />
<hkern u1="X" u2="&#x169;" k="16" />
<hkern u1="X" u2="&#x15d;" k="6" />
<hkern u1="X" u2="&#x15c;" k="5" />
<hkern u1="X" u2="&#x15b;" k="6" />
<hkern u1="X" u2="&#x15a;" k="5" />
<hkern u1="X" u2="&#x151;" k="26" />
<hkern u1="X" u2="&#x150;" k="23" />
<hkern u1="X" u2="&#x123;" k="25" />
<hkern u1="X" u2="&#x121;" k="25" />
<hkern u1="X" u2="&#x11d;" k="25" />
<hkern u1="X" u2="&#x11c;" k="23" />
<hkern u1="X" u2="&#x11b;" k="26" />
<hkern u1="X" u2="&#x117;" k="26" />
<hkern u1="X" u2="&#x16d;" k="16" />
<hkern u1="X" u2="&#x14f;" k="26" />
<hkern u1="X" u2="&#x14e;" k="23" />
<hkern u1="X" u2="&#x11f;" k="25" />
<hkern u1="X" u2="&#x11e;" k="23" />
<hkern u1="X" u2="&#x115;" k="26" />
<hkern u1="X" u2="&#x16b;" k="16" />
<hkern u1="X" u2="&#x14d;" k="26" />
<hkern u1="X" u2="&#x14c;" k="23" />
<hkern u1="X" u2="&#x113;" k="26" />
<hkern u1="X" u2="&#x10f;" k="25" />
<hkern u1="X" u2="&#x10d;" k="26" />
<hkern u1="X" u2="&#x10c;" k="23" />
<hkern u1="X" u2="&#x10b;" k="26" />
<hkern u1="X" u2="&#x10a;" k="23" />
<hkern u1="X" u2="&#x109;" k="26" />
<hkern u1="X" u2="&#x108;" k="23" />
<hkern u1="X" u2="&#x107;" k="26" />
<hkern u1="X" u2="&#x106;" k="23" />
<hkern u1="X" u2="&#x103;" k="5" />
<hkern u1="X" u2="&#x101;" k="5" />
<hkern u1="X" u2="&#x120;" k="23" />
<hkern u1="X" u2="&#xad;" k="15" />
<hkern u1="X" g2="hyphen.case" k="21" />
<hkern u1="X" g2="emdash.case" k="21" />
<hkern u1="X" g2="endash.case" k="21" />
<hkern u1="X" g2="guilsinglleft.case" k="25" />
<hkern u1="X" g2="guillemotleft.case" k="25" />
<hkern u1="X" u2="&#xd5;" k="23" />
<hkern u1="X" u2="&#xf5;" k="26" />
<hkern u1="X" u2="&#xe3;" k="5" />
<hkern u1="X" u2="f" k="9" />
<hkern u1="X" u2="&#x2039;" k="19" />
<hkern u1="X" u2="&#xd8;" k="23" />
<hkern u1="X" u2="&#x152;" k="23" />
<hkern u1="X" u2="&#x153;" k="26" />
<hkern u1="X" u2="&#xe6;" k="5" />
<hkern u1="X" u2="&#xf8;" k="26" />
<hkern u1="X" u2="&#xe5;" k="5" />
<hkern u1="X" u2="&#x161;" k="6" />
<hkern u1="X" u2="&#x160;" k="5" />
<hkern u1="X" u2="&#xd6;" k="23" />
<hkern u1="X" u2="&#xd2;" k="23" />
<hkern u1="X" u2="&#xd4;" k="23" />
<hkern u1="X" u2="&#xd3;" k="23" />
<hkern u1="X" u2="g" k="25" />
<hkern u1="X" u2="s" k="6" />
<hkern u1="X" u2="&#xab;" k="19" />
<hkern u1="X" u2="&#xc7;" k="23" />
<hkern u1="X" u2="&#xff;" k="26" />
<hkern u1="X" u2="&#xfc;" k="16" />
<hkern u1="X" u2="&#xfb;" k="16" />
<hkern u1="X" u2="&#xf9;" k="16" />
<hkern u1="X" u2="&#xfa;" k="16" />
<hkern u1="X" u2="&#xf6;" k="26" />
<hkern u1="X" u2="&#xf4;" k="26" />
<hkern u1="X" u2="&#xf2;" k="26" />
<hkern u1="X" u2="&#xf3;" k="26" />
<hkern u1="X" u2="&#xeb;" k="26" />
<hkern u1="X" u2="&#xea;" k="26" />
<hkern u1="X" u2="&#xe8;" k="26" />
<hkern u1="X" u2="&#xe9;" k="26" />
<hkern u1="X" u2="&#xe4;" k="5" />
<hkern u1="X" u2="&#xe2;" k="5" />
<hkern u1="X" u2="&#xe0;" k="5" />
<hkern u1="X" u2="&#xe1;" k="5" />
<hkern u1="X" u2="&#xfd;" k="26" />
<hkern u1="X" u2="&#x2014;" k="15" />
<hkern u1="X" u2="&#x2013;" k="15" />
<hkern u1="X" u2="&#xe7;" k="26" />
<hkern u1="X" u2="&#x2d;" k="15" />
<hkern u1="X" g2="fl" k="9" />
<hkern u1="X" g2="fi" k="9" />
<hkern u1="X" u2="G" k="23" />
<hkern u1="X" u2="C" k="23" />
<hkern u1="X" u2="Q" k="23" />
<hkern u1="X" u2="S" k="5" />
<hkern u1="X" u2="O" k="23" />
<hkern u1="X" u2="y" k="26" />
<hkern u1="X" u2="w" k="22" />
<hkern u1="X" u2="c" k="26" />
<hkern u1="X" u2="e" k="26" />
<hkern u1="X" u2="u" k="16" />
<hkern u1="X" u2="o" k="26" />
<hkern u1="X" u2="a" k="5" />
<hkern u1="X" u2="t" k="9" />
<hkern u1="X" u2="q" k="25" />
<hkern u1="X" u2="&#xba;" k="10" />
<hkern u1="X" u2="&#xaa;" k="10" />
<hkern u1="X" g2="four.plf" k="10" />
<hkern u1="X" u2="&#xf0;" k="25" />
<hkern u1="X" u2="&#xae;" k="20" />
<hkern u1="X" u2="&#xa9;" k="20" />
<hkern u1="X" u2="v" k="25" />
<hkern u1="G" u2="&#x29;" k="14" />
<hkern u1="G" u2="&#x7d;" k="18" />
<hkern u1="G" u2="]" k="17" />
<hkern u1="G" u2="Y" k="25" />
<hkern u1="G" u2="W" k="14" />
<hkern u1="G" u2="V" k="20" />
<hkern u1="G" u2="T" k="10" />
<hkern g1="fi" u2="U" k="5" />
<hkern g1="fi" u2="Z" k="5" />
<hkern g1="fl" u2="&#xb7;" k="57" />
<hkern u1="&#x26;" u2="&#x21a;" k="27" />
<hkern u1="&#x26;" u2="&#x162;" k="27" />
<hkern u1="&#x26;" u2="&#x1fc;" k="-13" />
<hkern u1="&#x26;" u2="&#x1ef2;" k="44" />
<hkern u1="&#x26;" u2="&#x1e84;" k="31" />
<hkern u1="&#x26;" u2="&#x1e82;" k="31" />
<hkern u1="&#x26;" u2="&#x1e80;" k="31" />
<hkern u1="&#x26;" u2="&#x1ef3;" k="20" />
<hkern u1="&#x26;" u2="&#x1e85;" k="12" />
<hkern u1="&#x26;" u2="&#x1e83;" k="12" />
<hkern u1="&#x26;" u2="&#x1e81;" k="12" />
<hkern u1="&#x26;" u2="&#x177;" k="20" />
<hkern u1="&#x26;" u2="&#x176;" k="44" />
<hkern u1="&#x26;" u2="&#x175;" k="12" />
<hkern u1="&#x26;" u2="&#x174;" k="31" />
<hkern u1="&#x26;" u2="&#x164;" k="27" />
<hkern u1="&#x26;" u2="&#x27;" k="23" />
<hkern u1="&#x26;" u2="&#x22;" k="23" />
<hkern u1="&#x26;" u2="&#xc6;" k="-13" />
<hkern u1="&#x26;" u2="&#x2019;" k="27" />
<hkern u1="&#x26;" u2="&#x201d;" k="27" />
<hkern u1="&#x26;" u2="&#x178;" k="44" />
<hkern u1="&#x26;" u2="&#xff;" k="20" />
<hkern u1="&#x26;" u2="&#xfd;" k="20" />
<hkern u1="&#x26;" u2="&#xdd;" k="44" />
<hkern u1="&#x26;" u2="Y" k="44" />
<hkern u1="&#x26;" u2="W" k="31" />
<hkern u1="&#x26;" u2="T" k="27" />
<hkern u1="&#x26;" u2="y" k="20" />
<hkern u1="&#x26;" u2="w" k="12" />
<hkern u1="&#x26;" u2="&#x166;" k="21" />
<hkern u1="&#x26;" u2="V" k="39" />
<hkern u1="&#x26;" u2="v" k="18" />
<hkern u1="&#x2d;" u2="&#x166;" k="12" />
<hkern u1="&#x2d;" g2="seven.plf" k="27" />
<hkern u1="&#x2d;" g2="one.plf" k="17" />
<hkern u1="&#x2d;" u2="X" k="15" />
<hkern u1="&#x2d;" u2="Y" k="44" />
<hkern u1="&#x2d;" u2="W" k="13" />
<hkern u1="&#x2d;" u2="V" k="24" />
<hkern u1="&#x2d;" u2="J" k="42" />
<hkern u1="&#x2d;" u2="T" k="42" />
<hkern u1="&#x2d;" u2="x" k="16" />
<hkern u1="&#x2d;" u2="y" k="8" />
<hkern u1="&#x2d;" u2="z" k="14" />
<hkern u1="&#xe7;" u2="&#x29;" k="33" />
<hkern u1="&#xe7;" u2="&#x2122;" k="20" />
<hkern u1="&#xe7;" u2="&#x7d;" k="32" />
<hkern u1="&#xe7;" u2="\" k="28" />
<hkern u1="&#xe7;" u2="]" k="32" />
<hkern u1="&#xe7;" u2="X" k="9" />
<hkern u1="&#xe7;" u2="Y" k="75" />
<hkern u1="&#xe7;" u2="Z" k="5" />
<hkern u1="&#xe7;" u2="W" k="32" />
<hkern u1="&#xe7;" u2="V" k="45" />
<hkern u1="&#xe7;" u2="T" k="85" />
<hkern u1="&#xe7;" u2="S" k="5" />
<hkern u1="&#xe7;" u2="A" k="5" />
<hkern u1="&#xe7;" u2="x" k="8" />
<hkern u1="&#xe7;" u2="y" k="8" />
<hkern u1="&#xe7;" u2="v" k="6" />
<hkern u1="&#x2f;" u2="d" k="34" />
<hkern u1="&#x2f;" u2="&#x105;" k="31" />
<hkern u1="&#x2f;" u2="&#x104;" k="49" />
<hkern u1="&#x2f;" u2="&#x173;" k="14" />
<hkern u1="&#x2f;" u2="&#x119;" k="34" />
<hkern u1="&#x2f;" u2="&#x1fa;" k="49" />
<hkern u1="&#x2f;" u2="&#x1fb;" k="31" />
<hkern u1="&#x2f;" u2="&#x219;" k="28" />
<hkern u1="&#x2f;" u2="&#x15f;" k="28" />
<hkern u1="&#x2f;" u2="&#x157;" k="16" />
<hkern u1="&#x2f;" u2="&#x146;" k="16" />
<hkern u1="&#x2f;" u2="&#x149;" k="16" />
<hkern u1="&#x2f;" u2="&#x111;" k="34" />
<hkern u1="&#x2f;" u2="&#x1fd;" k="31" />
<hkern u1="&#x2f;" u2="&#x1ff;" k="34" />
<hkern u1="&#x2f;" u2="&#x14b;" k="16" />
<hkern u1="&#x2f;" u2="&#x1fc;" k="61" />
<hkern u1="&#x2f;" u2="&#x17c;" k="12" />
<hkern u1="&#x2f;" u2="&#x17a;" k="12" />
<hkern u1="&#x2f;" u2="&#x171;" k="14" />
<hkern u1="&#x2f;" u2="&#x16f;" k="14" />
<hkern u1="&#x2f;" u2="&#x169;" k="14" />
<hkern u1="&#x2f;" u2="&#x15d;" k="28" />
<hkern u1="&#x2f;" u2="&#x15b;" k="28" />
<hkern u1="&#x2f;" u2="&#x159;" k="16" />
<hkern u1="&#x2f;" u2="&#x155;" k="16" />
<hkern u1="&#x2f;" u2="&#x151;" k="34" />
<hkern u1="&#x2f;" u2="&#x148;" k="16" />
<hkern u1="&#x2f;" u2="&#x144;" k="16" />
<hkern u1="&#x2f;" u2="&#x134;" k="54" />
<hkern u1="&#x2f;" u2="&#x123;" k="34" />
<hkern u1="&#x2f;" u2="&#x121;" k="34" />
<hkern u1="&#x2f;" u2="&#x11d;" k="34" />
<hkern u1="&#x2f;" u2="&#x11b;" k="34" />
<hkern u1="&#x2f;" u2="&#x117;" k="34" />
<hkern u1="&#x2f;" u2="&#x16d;" k="14" />
<hkern u1="&#x2f;" u2="&#x14f;" k="34" />
<hkern u1="&#x2f;" u2="&#x11f;" k="34" />
<hkern u1="&#x2f;" u2="&#x115;" k="34" />
<hkern u1="&#x2f;" u2="&#x16b;" k="14" />
<hkern u1="&#x2f;" u2="&#x14d;" k="34" />
<hkern u1="&#x2f;" u2="&#x113;" k="34" />
<hkern u1="&#x2f;" u2="&#x10f;" k="34" />
<hkern u1="&#x2f;" u2="&#x10d;" k="34" />
<hkern u1="&#x2f;" u2="&#x10b;" k="34" />
<hkern u1="&#x2f;" u2="&#x109;" k="34" />
<hkern u1="&#x2f;" u2="&#x107;" k="34" />
<hkern u1="&#x2f;" u2="&#x103;" k="31" />
<hkern u1="&#x2f;" u2="&#x102;" k="49" />
<hkern u1="&#x2f;" u2="&#x101;" k="31" />
<hkern u1="&#x2f;" u2="&#x100;" k="49" />
<hkern u1="&#x2f;" u2="&#xc3;" k="49" />
<hkern u1="&#x2f;" u2="&#xf5;" k="34" />
<hkern u1="&#x2f;" u2="&#xf1;" k="16" />
<hkern u1="&#x2f;" u2="&#xe3;" k="31" />
<hkern u1="&#x2f;" u2="n" k="16" />
<hkern u1="&#x2f;" u2="&#xc6;" k="61" />
<hkern u1="&#x2f;" u2="&#x153;" k="34" />
<hkern u1="&#x2f;" u2="&#xe6;" k="31" />
<hkern u1="&#x2f;" u2="&#xf8;" k="34" />
<hkern u1="&#x2f;" u2="&#xc5;" k="49" />
<hkern u1="&#x2f;" u2="&#xe5;" k="31" />
<hkern u1="&#x2f;" u2="&#x17e;" k="12" />
<hkern u1="&#x2f;" u2="&#x161;" k="28" />
<hkern u1="&#x2f;" u2="g" k="34" />
<hkern u1="&#x2f;" u2="s" k="28" />
<hkern u1="&#x2f;" u2="&#xc1;" k="49" />
<hkern u1="&#x2f;" u2="&#xc2;" k="49" />
<hkern u1="&#x2f;" u2="&#xc0;" k="49" />
<hkern u1="&#x2f;" u2="&#xfc;" k="14" />
<hkern u1="&#x2f;" u2="&#xfb;" k="14" />
<hkern u1="&#x2f;" u2="&#xf9;" k="14" />
<hkern u1="&#x2f;" u2="&#xfa;" k="14" />
<hkern u1="&#x2f;" u2="&#xf6;" k="34" />
<hkern u1="&#x2f;" u2="&#xf4;" k="34" />
<hkern u1="&#x2f;" u2="&#xf2;" k="34" />
<hkern u1="&#x2f;" u2="&#xf3;" k="34" />
<hkern u1="&#x2f;" u2="&#xeb;" k="34" />
<hkern u1="&#x2f;" u2="&#xea;" k="34" />
<hkern u1="&#x2f;" u2="&#xe8;" k="34" />
<hkern u1="&#x2f;" u2="&#xe9;" k="34" />
<hkern u1="&#x2f;" u2="&#xe4;" k="31" />
<hkern u1="&#x2f;" u2="&#xe2;" k="31" />
<hkern u1="&#x2f;" u2="&#xe0;" k="31" />
<hkern u1="&#x2f;" u2="&#xe1;" k="31" />
<hkern u1="&#x2f;" u2="&#xc4;" k="49" />
<hkern u1="&#x2f;" u2="&#xe7;" k="34" />
<hkern u1="&#x2f;" u2="J" k="54" />
<hkern u1="&#x2f;" u2="A" k="49" />
<hkern u1="&#x2f;" u2="z" k="12" />
<hkern u1="&#x2f;" u2="r" k="16" />
<hkern u1="&#x2f;" u2="m" k="16" />
<hkern u1="&#x2f;" u2="c" k="34" />
<hkern u1="&#x2f;" u2="e" k="34" />
<hkern u1="&#x2f;" u2="u" k="14" />
<hkern u1="&#x2f;" u2="o" k="34" />
<hkern u1="&#x2f;" u2="a" k="31" />
<hkern u1="&#x2f;" u2="q" k="34" />
<hkern u1="&#x2f;" u2="p" k="16" />
<hkern u1="&#x2f;" u2="&#x129;" k="-12" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-19" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-23" />
<hkern u1="&#x2f;" g2="four.plf" k="44" />
<hkern u1="&#x2f;" u2="&#xf0;" k="27" />
<hkern u1="&#x2f;" u2="&#xef;" k="-17" />
<hkern u1="&#x2f;" u2="&#x2f;" k="221" />
<hkern u1="[" g2="zero.plfslash" k="23" />
<hkern u1="[" u2="d" k="36" />
<hkern u1="[" u2="&#x105;" k="32" />
<hkern u1="[" u2="&#x104;" k="33" />
<hkern u1="[" u2="&#x173;" k="25" />
<hkern u1="[" u2="&#x119;" k="36" />
<hkern u1="[" u2="&#x1fa;" k="33" />
<hkern u1="[" u2="&#x1fb;" k="32" />
<hkern u1="[" u2="&#x219;" k="31" />
<hkern u1="[" u2="&#x218;" k="16" />
<hkern u1="[" u2="&#x15f;" k="31" />
<hkern u1="[" u2="&#x15e;" k="16" />
<hkern u1="[" u2="&#x157;" k="26" />
<hkern u1="[" u2="&#x146;" k="26" />
<hkern u1="[" u2="&#x122;" k="25" />
<hkern u1="[" u2="&#x149;" k="26" />
<hkern u1="[" u2="&#x111;" k="36" />
<hkern u1="[" u2="&#x1fd;" k="32" />
<hkern u1="[" u2="&#x1ff;" k="36" />
<hkern u1="[" u2="&#x1fe;" k="25" />
<hkern u1="[" u2="&#x14b;" k="26" />
<hkern u1="[" u2="&#x1fc;" k="19" />
<hkern u1="[" u2="&#x1e85;" k="17" />
<hkern u1="[" u2="&#x1e83;" k="17" />
<hkern u1="[" u2="&#x1e81;" k="17" />
<hkern u1="[" u2="&#x17c;" k="24" />
<hkern u1="[" u2="&#x17a;" k="24" />
<hkern u1="[" u2="&#x175;" k="17" />
<hkern u1="[" u2="&#x171;" k="25" />
<hkern u1="[" u2="&#x16f;" k="25" />
<hkern u1="[" u2="&#x169;" k="25" />
<hkern u1="[" u2="&#x15d;" k="31" />
<hkern u1="[" u2="&#x15c;" k="16" />
<hkern u1="[" u2="&#x15b;" k="31" />
<hkern u1="[" u2="&#x15a;" k="16" />
<hkern u1="[" u2="&#x159;" k="26" />
<hkern u1="[" u2="&#x155;" k="26" />
<hkern u1="[" u2="&#x151;" k="36" />
<hkern u1="[" u2="&#x150;" k="25" />
<hkern u1="[" u2="&#x148;" k="26" />
<hkern u1="[" u2="&#x144;" k="26" />
<hkern u1="[" u2="&#x134;" k="22" />
<hkern u1="[" u2="&#x123;" k="36" />
<hkern u1="[" u2="&#x121;" k="36" />
<hkern u1="[" u2="&#x11d;" k="36" />
<hkern u1="[" u2="&#x11c;" k="25" />
<hkern u1="[" u2="&#x11b;" k="36" />
<hkern u1="[" u2="&#x117;" k="36" />
<hkern u1="[" u2="&#x16d;" k="25" />
<hkern u1="[" u2="&#x14f;" k="36" />
<hkern u1="[" u2="&#x14e;" k="25" />
<hkern u1="[" u2="&#x11f;" k="36" />
<hkern u1="[" u2="&#x11e;" k="25" />
<hkern u1="[" u2="&#x115;" k="36" />
<hkern u1="[" u2="&#x16b;" k="25" />
<hkern u1="[" u2="&#x14d;" k="36" />
<hkern u1="[" u2="&#x14c;" k="25" />
<hkern u1="[" u2="&#x113;" k="36" />
<hkern u1="[" u2="&#x10f;" k="36" />
<hkern u1="[" u2="&#x10d;" k="36" />
<hkern u1="[" u2="&#x10c;" k="25" />
<hkern u1="[" u2="&#x10b;" k="36" />
<hkern u1="[" u2="&#x10a;" k="25" />
<hkern u1="[" u2="&#x109;" k="36" />
<hkern u1="[" u2="&#x108;" k="25" />
<hkern u1="[" u2="&#x107;" k="36" />
<hkern u1="[" u2="&#x106;" k="25" />
<hkern u1="[" u2="&#x103;" k="32" />
<hkern u1="[" u2="&#x102;" k="33" />
<hkern u1="[" u2="&#x101;" k="32" />
<hkern u1="[" u2="&#x100;" k="33" />
<hkern u1="[" u2="&#x120;" k="25" />
<hkern u1="[" g2="zero.plf" k="23" />
<hkern u1="[" u2="&#xd5;" k="25" />
<hkern u1="[" u2="&#xc3;" k="33" />
<hkern u1="[" u2="&#xf5;" k="36" />
<hkern u1="[" u2="&#xf1;" k="26" />
<hkern u1="[" u2="&#xe3;" k="32" />
<hkern u1="[" u2="n" k="26" />
<hkern u1="[" u2="&#xd8;" k="25" />
<hkern u1="[" u2="&#xc6;" k="19" />
<hkern u1="[" u2="&#x152;" k="25" />
<hkern u1="[" u2="&#x153;" k="36" />
<hkern u1="[" u2="&#xe6;" k="32" />
<hkern u1="[" u2="&#xf8;" k="36" />
<hkern u1="[" u2="&#xc5;" k="33" />
<hkern u1="[" u2="&#xe5;" k="32" />
<hkern u1="[" u2="&#x17e;" k="24" />
<hkern u1="[" u2="&#x161;" k="31" />
<hkern u1="[" u2="&#x160;" k="16" />
<hkern u1="[" u2="&#xd6;" k="25" />
<hkern u1="[" u2="&#xd2;" k="25" />
<hkern u1="[" u2="&#xd4;" k="25" />
<hkern u1="[" u2="&#xd3;" k="25" />
<hkern u1="[" u2="g" k="36" />
<hkern u1="[" u2="s" k="31" />
<hkern u1="[" u2="&#xc7;" k="25" />
<hkern u1="[" u2="&#xc1;" k="33" />
<hkern u1="[" u2="&#xc2;" k="33" />
<hkern u1="[" u2="&#xc0;" k="33" />
<hkern u1="[" u2="&#xfc;" k="25" />
<hkern u1="[" u2="&#xfb;" k="25" />
<hkern u1="[" u2="&#xf9;" k="25" />
<hkern u1="[" u2="&#xfa;" k="25" />
<hkern u1="[" u2="&#xf6;" k="36" />
<hkern u1="[" u2="&#xf4;" k="36" />
<hkern u1="[" u2="&#xf2;" k="36" />
<hkern u1="[" u2="&#xf3;" k="36" />
<hkern u1="[" u2="&#xeb;" k="36" />
<hkern u1="[" u2="&#xea;" k="36" />
<hkern u1="[" u2="&#xe8;" k="36" />
<hkern u1="[" u2="&#xe9;" k="36" />
<hkern u1="[" u2="&#xe4;" k="32" />
<hkern u1="[" u2="&#xe2;" k="32" />
<hkern u1="[" u2="&#xe0;" k="32" />
<hkern u1="[" u2="&#xe1;" k="32" />
<hkern u1="[" u2="&#xc4;" k="33" />
<hkern u1="[" u2="&#xe7;" k="36" />
<hkern u1="[" u2="G" k="25" />
<hkern u1="[" u2="C" k="25" />
<hkern u1="[" u2="J" k="22" />
<hkern u1="[" u2="Q" k="25" />
<hkern u1="[" u2="S" k="16" />
<hkern u1="[" u2="O" k="25" />
<hkern u1="[" u2="A" k="33" />
<hkern u1="[" u2="z" k="24" />
<hkern u1="[" u2="w" k="17" />
<hkern u1="[" u2="r" k="26" />
<hkern u1="[" u2="m" k="26" />
<hkern u1="[" u2="c" k="36" />
<hkern u1="[" u2="e" k="36" />
<hkern u1="[" u2="u" k="25" />
<hkern u1="[" u2="o" k="36" />
<hkern u1="[" u2="a" k="32" />
<hkern u1="[" u2="q" k="36" />
<hkern u1="[" u2="p" k="26" />
<hkern u1="[" u2="&#x135;" k="-13" />
<hkern u1="[" u2="&#x129;" k="-7" />
<hkern u1="[" u2="&#x12b;" k="-7" />
<hkern u1="[" g2="nine.plf" k="17" />
<hkern u1="[" g2="eight.plf" k="20" />
<hkern u1="[" g2="six.plf" k="24" />
<hkern u1="[" g2="four.plf" k="36" />
<hkern u1="[" g2="three.plf" k="17" />
<hkern u1="[" g2="two.plf" k="11" />
<hkern u1="[" u2="&#xf0;" k="31" />
<hkern u1="[" u2="&#x7b;" k="10" />
<hkern u1="[" u2="&#x28;" k="19" />
<hkern u1="[" u2="x" k="18" />
<hkern u1="[" u2="v" k="17" />
<hkern u1="\" u2="&#x21a;" k="47" />
<hkern u1="\" u2="&#x162;" k="47" />
<hkern u1="\" u2="&#x1fc;" k="-6" />
<hkern u1="\" u2="&#x1ef2;" k="58" />
<hkern u1="\" u2="&#x1e84;" k="34" />
<hkern u1="\" u2="&#x1e82;" k="34" />
<hkern u1="\" u2="&#x1e80;" k="34" />
<hkern u1="\" u2="&#x1ef3;" k="28" />
<hkern u1="\" u2="&#x1e85;" k="17" />
<hkern u1="\" u2="&#x1e83;" k="17" />
<hkern u1="\" u2="&#x1e81;" k="17" />
<hkern u1="\" u2="&#x177;" k="28" />
<hkern u1="\" u2="&#x176;" k="58" />
<hkern u1="\" u2="&#x175;" k="17" />
<hkern u1="\" u2="&#x174;" k="34" />
<hkern u1="\" u2="&#x164;" k="47" />
<hkern u1="\" u2="&#x27;" k="44" />
<hkern u1="\" u2="&#x22;" k="44" />
<hkern u1="\" u2="&#xc6;" k="-6" />
<hkern u1="\" u2="&#x2019;" k="47" />
<hkern u1="\" u2="&#x201d;" k="47" />
<hkern u1="\" u2="&#x178;" k="58" />
<hkern u1="\" u2="&#xff;" k="28" />
<hkern u1="\" u2="&#xfd;" k="28" />
<hkern u1="\" u2="&#xdd;" k="58" />
<hkern u1="\" u2="Y" k="58" />
<hkern u1="\" u2="W" k="34" />
<hkern u1="\" u2="T" k="47" />
<hkern u1="\" u2="y" k="28" />
<hkern u1="\" u2="w" k="17" />
<hkern u1="\" u2="&#x166;" k="39" />
<hkern u1="\" g2="one.plf" k="43" />
<hkern u1="\" u2="V" k="48" />
<hkern u1="\" u2="v" k="26" />
<hkern u1="&#x28;" g2="zero.plfslash" k="24" />
<hkern u1="&#x28;" u2="d" k="36" />
<hkern u1="&#x28;" u2="&#x105;" k="31" />
<hkern u1="&#x28;" u2="&#x104;" k="17" />
<hkern u1="&#x28;" u2="&#x173;" k="29" />
<hkern u1="&#x28;" u2="&#x119;" k="37" />
<hkern u1="&#x28;" u2="&#x1fa;" k="17" />
<hkern u1="&#x28;" u2="&#x1fb;" k="31" />
<hkern u1="&#x28;" u2="&#x167;" k="16" />
<hkern u1="&#x28;" u2="&#x219;" k="32" />
<hkern u1="&#x28;" u2="&#x218;" k="18" />
<hkern u1="&#x28;" u2="&#x21b;" k="16" />
<hkern u1="&#x28;" u2="&#x163;" k="16" />
<hkern u1="&#x28;" u2="&#x15f;" k="32" />
<hkern u1="&#x28;" u2="&#x15e;" k="18" />
<hkern u1="&#x28;" u2="&#x157;" k="26" />
<hkern u1="&#x28;" u2="&#x146;" k="26" />
<hkern u1="&#x28;" u2="&#x122;" k="26" />
<hkern u1="&#x28;" u2="&#x149;" k="26" />
<hkern u1="&#x28;" u2="&#x111;" k="36" />
<hkern u1="&#x28;" u2="&#x1fd;" k="31" />
<hkern u1="&#x28;" u2="&#x1ff;" k="37" />
<hkern u1="&#x28;" u2="&#x1fe;" k="26" />
<hkern u1="&#x28;" u2="&#x165;" k="16" />
<hkern u1="&#x28;" u2="&#x14b;" k="26" />
<hkern u1="&#x28;" u2="&#x1e85;" k="25" />
<hkern u1="&#x28;" u2="&#x1e83;" k="25" />
<hkern u1="&#x28;" u2="&#x1e81;" k="25" />
<hkern u1="&#x28;" u2="&#x17c;" k="19" />
<hkern u1="&#x28;" u2="&#x17a;" k="19" />
<hkern u1="&#x28;" u2="&#x175;" k="25" />
<hkern u1="&#x28;" u2="&#x171;" k="29" />
<hkern u1="&#x28;" u2="&#x16f;" k="29" />
<hkern u1="&#x28;" u2="&#x169;" k="29" />
<hkern u1="&#x28;" u2="&#x15d;" k="32" />
<hkern u1="&#x28;" u2="&#x15c;" k="18" />
<hkern u1="&#x28;" u2="&#x15b;" k="32" />
<hkern u1="&#x28;" u2="&#x15a;" k="18" />
<hkern u1="&#x28;" u2="&#x159;" k="26" />
<hkern u1="&#x28;" u2="&#x155;" k="26" />
<hkern u1="&#x28;" u2="&#x151;" k="37" />
<hkern u1="&#x28;" u2="&#x150;" k="26" />
<hkern u1="&#x28;" u2="&#x148;" k="26" />
<hkern u1="&#x28;" u2="&#x144;" k="26" />
<hkern u1="&#x28;" u2="&#x123;" k="36" />
<hkern u1="&#x28;" u2="&#x121;" k="36" />
<hkern u1="&#x28;" u2="&#x11d;" k="36" />
<hkern u1="&#x28;" u2="&#x11c;" k="26" />
<hkern u1="&#x28;" u2="&#x11b;" k="37" />
<hkern u1="&#x28;" u2="&#x117;" k="37" />
<hkern u1="&#x28;" u2="&#x16d;" k="29" />
<hkern u1="&#x28;" u2="&#x14f;" k="37" />
<hkern u1="&#x28;" u2="&#x14e;" k="26" />
<hkern u1="&#x28;" u2="&#x11f;" k="36" />
<hkern u1="&#x28;" u2="&#x11e;" k="26" />
<hkern u1="&#x28;" u2="&#x115;" k="37" />
<hkern u1="&#x28;" u2="&#x16b;" k="29" />
<hkern u1="&#x28;" u2="&#x14d;" k="37" />
<hkern u1="&#x28;" u2="&#x14c;" k="26" />
<hkern u1="&#x28;" u2="&#x113;" k="37" />
<hkern u1="&#x28;" u2="&#x10f;" k="36" />
<hkern u1="&#x28;" u2="&#x10d;" k="37" />
<hkern u1="&#x28;" u2="&#x10c;" k="26" />
<hkern u1="&#x28;" u2="&#x10b;" k="37" />
<hkern u1="&#x28;" u2="&#x10a;" k="26" />
<hkern u1="&#x28;" u2="&#x109;" k="37" />
<hkern u1="&#x28;" u2="&#x108;" k="26" />
<hkern u1="&#x28;" u2="&#x107;" k="37" />
<hkern u1="&#x28;" u2="&#x106;" k="26" />
<hkern u1="&#x28;" u2="&#x103;" k="31" />
<hkern u1="&#x28;" u2="&#x102;" k="17" />
<hkern u1="&#x28;" u2="&#x101;" k="31" />
<hkern u1="&#x28;" u2="&#x100;" k="17" />
<hkern u1="&#x28;" u2="&#x120;" k="26" />
<hkern u1="&#x28;" g2="zero.plf" k="24" />
<hkern u1="&#x28;" u2="&#xd5;" k="26" />
<hkern u1="&#x28;" u2="&#xc3;" k="17" />
<hkern u1="&#x28;" u2="&#xf5;" k="37" />
<hkern u1="&#x28;" u2="&#xf1;" k="26" />
<hkern u1="&#x28;" u2="&#xe3;" k="31" />
<hkern u1="&#x28;" u2="n" k="26" />
<hkern u1="&#x28;" u2="f" k="16" />
<hkern u1="&#x28;" u2="&#xd8;" k="26" />
<hkern u1="&#x28;" u2="&#x152;" k="26" />
<hkern u1="&#x28;" u2="&#x153;" k="37" />
<hkern u1="&#x28;" u2="&#xe6;" k="31" />
<hkern u1="&#x28;" u2="&#xf8;" k="37" />
<hkern u1="&#x28;" u2="&#xc5;" k="17" />
<hkern u1="&#x28;" u2="&#xe5;" k="31" />
<hkern u1="&#x28;" u2="&#x17e;" k="19" />
<hkern u1="&#x28;" u2="&#x161;" k="32" />
<hkern u1="&#x28;" u2="&#x160;" k="18" />
<hkern u1="&#x28;" u2="&#xd6;" k="26" />
<hkern u1="&#x28;" u2="&#xd2;" k="26" />
<hkern u1="&#x28;" u2="&#xd4;" k="26" />
<hkern u1="&#x28;" u2="&#xd3;" k="26" />
<hkern u1="&#x28;" u2="g" k="36" />
<hkern u1="&#x28;" u2="s" k="32" />
<hkern u1="&#x28;" u2="&#xc7;" k="26" />
<hkern u1="&#x28;" u2="&#xc1;" k="17" />
<hkern u1="&#x28;" u2="&#xc2;" k="17" />
<hkern u1="&#x28;" u2="&#xc0;" k="17" />
<hkern u1="&#x28;" u2="&#xfc;" k="29" />
<hkern u1="&#x28;" u2="&#xfb;" k="29" />
<hkern u1="&#x28;" u2="&#xf9;" k="29" />
<hkern u1="&#x28;" u2="&#xfa;" k="29" />
<hkern u1="&#x28;" u2="&#xf6;" k="37" />
<hkern u1="&#x28;" u2="&#xf4;" k="37" />
<hkern u1="&#x28;" u2="&#xf2;" k="37" />
<hkern u1="&#x28;" u2="&#xf3;" k="37" />
<hkern u1="&#x28;" u2="&#xeb;" k="37" />
<hkern u1="&#x28;" u2="&#xea;" k="37" />
<hkern u1="&#x28;" u2="&#xe8;" k="37" />
<hkern u1="&#x28;" u2="&#xe9;" k="37" />
<hkern u1="&#x28;" u2="&#xe4;" k="31" />
<hkern u1="&#x28;" u2="&#xe2;" k="31" />
<hkern u1="&#x28;" u2="&#xe0;" k="31" />
<hkern u1="&#x28;" u2="&#xe1;" k="31" />
<hkern u1="&#x28;" u2="&#xc4;" k="17" />
<hkern u1="&#x28;" u2="&#xe7;" k="37" />
<hkern u1="&#x28;" g2="fl" k="16" />
<hkern u1="&#x28;" g2="fi" k="16" />
<hkern u1="&#x28;" u2="G" k="26" />
<hkern u1="&#x28;" u2="C" k="26" />
<hkern u1="&#x28;" u2="Q" k="26" />
<hkern u1="&#x28;" u2="S" k="18" />
<hkern u1="&#x28;" u2="O" k="26" />
<hkern u1="&#x28;" u2="A" k="17" />
<hkern u1="&#x28;" u2="z" k="19" />
<hkern u1="&#x28;" u2="w" k="25" />
<hkern u1="&#x28;" u2="r" k="26" />
<hkern u1="&#x28;" u2="m" k="26" />
<hkern u1="&#x28;" u2="c" k="37" />
<hkern u1="&#x28;" u2="e" k="37" />
<hkern u1="&#x28;" u2="u" k="29" />
<hkern u1="&#x28;" u2="o" k="37" />
<hkern u1="&#x28;" u2="a" k="31" />
<hkern u1="&#x28;" u2="t" k="16" />
<hkern u1="&#x28;" u2="q" k="36" />
<hkern u1="&#x28;" u2="p" k="26" />
<hkern u1="&#x28;" u2="&#x135;" k="-29" />
<hkern u1="&#x28;" g2="nine.plf" k="19" />
<hkern u1="&#x28;" g2="eight.plf" k="22" />
<hkern u1="&#x28;" g2="six.plf" k="25" />
<hkern u1="&#x28;" g2="four.plf" k="34" />
<hkern u1="&#x28;" g2="three.plf" k="19" />
<hkern u1="&#x28;" g2="two.plf" k="10" />
<hkern u1="&#x28;" u2="&#xf0;" k="33" />
<hkern u1="&#x28;" u2="&#x7b;" k="10" />
<hkern u1="&#x28;" u2="&#x28;" k="20" />
<hkern u1="&#x28;" u2="x" k="11" />
<hkern u1="&#x28;" u2="v" k="26" />
<hkern u1="&#x28;" u2="j" k="-17" />
<hkern u1="&#x2013;" u2="&#x166;" k="12" />
<hkern u1="&#x2013;" g2="seven.plf" k="27" />
<hkern u1="&#x2013;" g2="one.plf" k="17" />
<hkern u1="&#x2013;" u2="X" k="15" />
<hkern u1="&#x2013;" u2="Y" k="44" />
<hkern u1="&#x2013;" u2="W" k="13" />
<hkern u1="&#x2013;" u2="V" k="24" />
<hkern u1="&#x2013;" u2="J" k="42" />
<hkern u1="&#x2013;" u2="T" k="42" />
<hkern u1="&#x2013;" u2="x" k="16" />
<hkern u1="&#x2013;" u2="y" k="8" />
<hkern u1="&#x2013;" u2="z" k="14" />
<hkern u1="&#x2014;" u2="&#x166;" k="4" />
<hkern u1="&#x2014;" g2="seven.plf" k="27" />
<hkern u1="&#x2014;" g2="one.plf" k="17" />
<hkern u1="&#x2014;" u2="X" k="15" />
<hkern u1="&#x2014;" u2="Y" k="44" />
<hkern u1="&#x2014;" u2="W" k="13" />
<hkern u1="&#x2014;" u2="V" k="24" />
<hkern u1="&#x2014;" u2="J" k="42" />
<hkern u1="&#x2014;" u2="T" k="42" />
<hkern u1="&#x2014;" u2="x" k="16" />
<hkern u1="&#x2014;" u2="y" k="8" />
<hkern u1="&#x2014;" u2="z" k="14" />
<hkern u1="&#xfd;" u2="d" k="11" />
<hkern u1="&#xfd;" u2="&#x29;" k="25" />
<hkern u1="&#xfd;" u2="&#x2039;" k="19" />
<hkern u1="&#xfd;" u2="&#xc6;" k="34" />
<hkern u1="&#xfd;" u2="&#xf0;" k="15" />
<hkern u1="&#xfd;" u2="&#x7d;" k="16" />
<hkern u1="&#xfd;" u2="s" k="8" />
<hkern u1="&#xfd;" u2="]" k="16" />
<hkern u1="&#xfd;" u2="&#x2f;" k="27" />
<hkern u1="&#xfd;" u2="X" k="27" />
<hkern u1="&#xfd;" u2="Y" k="24" />
<hkern u1="&#xfd;" u2="Z" k="12" />
<hkern u1="&#xfd;" u2="J" k="59" />
<hkern u1="&#xfd;" u2="T" k="46" />
<hkern u1="&#xfd;" u2="A" k="26" />
<hkern u1="&#xfd;" u2="&#x2e;" k="34" />
<hkern u1="&#xfd;" u2="o" k="11" />
<hkern u1="&#xfd;" u2="a" k="10" />
<hkern u1="&#xfd;" u2="&#x20;" k="22" />
<hkern u1="&#xc4;" u2="&#xba;" k="30" />
<hkern u1="&#xc4;" u2="&#xaa;" k="28" />
<hkern u1="&#xc4;" u2="d" k="10" />
<hkern u1="&#xc4;" u2="&#x166;" k="45" />
<hkern u1="&#xc4;" g2="hyphen.case" k="10" />
<hkern u1="&#xc4;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc4;" u2="&#x29;" k="16" />
<hkern u1="&#xc4;" g2="nine.plf" k="10" />
<hkern u1="&#xc4;" g2="seven.plf" k="14" />
<hkern u1="&#xc4;" g2="six.plf" k="11" />
<hkern u1="&#xc4;" g2="one.plf" k="44" />
<hkern u1="&#xc4;" g2="zero.plf" k="12" />
<hkern u1="&#xc4;" u2="f" k="17" />
<hkern u1="&#xc4;" u2="&#x2039;" k="13" />
<hkern u1="&#xc4;" u2="&#x2a;" k="34" />
<hkern u1="&#xc4;" u2="&#x2122;" k="43" />
<hkern u1="&#xc4;" u2="&#x27;" k="38" />
<hkern u1="&#xc4;" u2="&#xf0;" k="9" />
<hkern u1="&#xc4;" u2="&#x7d;" k="33" />
<hkern u1="&#xc4;" u2="&#xae;" k="16" />
<hkern u1="&#xc4;" u2="s" k="6" />
<hkern u1="&#xc4;" u2="&#x2018;" k="38" />
<hkern u1="&#xc4;" u2="&#x2019;" k="40" />
<hkern u1="&#xc4;" u2="&#xa9;" k="16" />
<hkern u1="&#xc4;" u2="&#x3f;" k="34" />
<hkern u1="&#xc4;" u2="\" k="50" />
<hkern u1="&#xc4;" u2="]" k="33" />
<hkern u1="&#xc4;" u2="U" k="13" />
<hkern u1="&#xc4;" u2="Y" k="57" />
<hkern u1="&#xc4;" u2="W" k="33" />
<hkern u1="&#xc4;" u2="V" k="42" />
<hkern u1="&#xc4;" u2="T" k="53" />
<hkern u1="&#xc4;" u2="S" k="10" />
<hkern u1="&#xc4;" u2="O" k="17" />
<hkern u1="&#xc4;" u2="y" k="28" />
<hkern u1="&#xc4;" u2="w" k="19" />
<hkern u1="&#xc4;" u2="v" k="26" />
<hkern u1="&#xc4;" u2="u" k="6" />
<hkern u1="&#xc4;" u2="o" k="10" />
<hkern u1="&#xc4;" u2="a" k="5" />
<hkern u1="&#xc4;" u2="&#x20;" k="26" />
<hkern u1="&#xdc;" u2="d" k="6" />
<hkern u1="&#xdc;" g2="parenright.case" k="14" />
<hkern u1="&#xdc;" u2="i" k="5" />
<hkern u1="&#xdc;" u2="&#xc6;" k="11" />
<hkern u1="&#xdc;" u2="&#xf0;" k="7" />
<hkern u1="&#xdc;" u2="s" k="5" />
<hkern u1="&#xdc;" u2="J" k="14" />
<hkern u1="&#xdc;" u2="A" k="13" />
<hkern u1="&#xdc;" u2="&#x2e;" k="8" />
<hkern u1="&#xdc;" u2="z" k="7" />
<hkern u1="&#xdc;" u2="u" k="7" />
<hkern u1="&#xdc;" u2="o" k="5" />
<hkern u1="&#xdc;" u2="a" k="6" />
<hkern u1="&#xe9;" u2="&#x29;" k="33" />
<hkern u1="&#xe9;" u2="f" k="4" />
<hkern u1="&#xe9;" u2="&#x2122;" k="21" />
<hkern u1="&#xe9;" u2="&#x7d;" k="33" />
<hkern u1="&#xe9;" u2="&#x2018;" k="8" />
<hkern u1="&#xe9;" u2="&#x2019;" k="11" />
<hkern u1="&#xe9;" u2="\" k="31" />
<hkern u1="&#xe9;" u2="]" k="33" />
<hkern u1="&#xe9;" u2="X" k="11" />
<hkern u1="&#xe9;" u2="Y" k="84" />
<hkern u1="&#xe9;" u2="Z" k="7" />
<hkern u1="&#xe9;" u2="W" k="37" />
<hkern u1="&#xe9;" u2="V" k="44" />
<hkern u1="&#xe9;" u2="T" k="75" />
<hkern u1="&#xe9;" u2="A" k="8" />
<hkern u1="&#xe9;" u2="x" k="15" />
<hkern u1="&#xe9;" u2="y" k="11" />
<hkern u1="&#xe9;" u2="w" k="6" />
<hkern u1="&#xe9;" u2="v" k="9" />
<hkern u1="&#xe8;" u2="&#x29;" k="33" />
<hkern u1="&#xe8;" u2="f" k="4" />
<hkern u1="&#xe8;" u2="&#x2122;" k="21" />
<hkern u1="&#xe8;" u2="&#x7d;" k="33" />
<hkern u1="&#xe8;" u2="&#x2018;" k="8" />
<hkern u1="&#xe8;" u2="&#x2019;" k="11" />
<hkern u1="&#xe8;" u2="\" k="31" />
<hkern u1="&#xe8;" u2="]" k="33" />
<hkern u1="&#xe8;" u2="X" k="11" />
<hkern u1="&#xe8;" u2="Y" k="84" />
<hkern u1="&#xe8;" u2="Z" k="7" />
<hkern u1="&#xe8;" u2="W" k="37" />
<hkern u1="&#xe8;" u2="V" k="44" />
<hkern u1="&#xe8;" u2="T" k="75" />
<hkern u1="&#xe8;" u2="A" k="8" />
<hkern u1="&#xe8;" u2="x" k="15" />
<hkern u1="&#xe8;" u2="y" k="11" />
<hkern u1="&#xe8;" u2="w" k="6" />
<hkern u1="&#xe8;" u2="v" k="9" />
<hkern u1="&#xea;" u2="&#x29;" k="33" />
<hkern u1="&#xea;" u2="f" k="4" />
<hkern u1="&#xea;" u2="&#x2122;" k="21" />
<hkern u1="&#xea;" u2="&#x7d;" k="33" />
<hkern u1="&#xea;" u2="&#x2018;" k="8" />
<hkern u1="&#xea;" u2="&#x2019;" k="11" />
<hkern u1="&#xea;" u2="\" k="31" />
<hkern u1="&#xea;" u2="]" k="33" />
<hkern u1="&#xea;" u2="X" k="11" />
<hkern u1="&#xea;" u2="Y" k="84" />
<hkern u1="&#xea;" u2="Z" k="7" />
<hkern u1="&#xea;" u2="W" k="37" />
<hkern u1="&#xea;" u2="V" k="44" />
<hkern u1="&#xea;" u2="T" k="75" />
<hkern u1="&#xea;" u2="A" k="8" />
<hkern u1="&#xea;" u2="x" k="15" />
<hkern u1="&#xea;" u2="y" k="11" />
<hkern u1="&#xea;" u2="w" k="6" />
<hkern u1="&#xea;" u2="v" k="9" />
<hkern u1="&#xeb;" u2="&#x29;" k="33" />
<hkern u1="&#xeb;" u2="f" k="4" />
<hkern u1="&#xeb;" u2="&#x2122;" k="21" />
<hkern u1="&#xeb;" u2="&#x7d;" k="33" />
<hkern u1="&#xeb;" u2="&#x2018;" k="8" />
<hkern u1="&#xeb;" u2="&#x2019;" k="11" />
<hkern u1="&#xeb;" u2="\" k="31" />
<hkern u1="&#xeb;" u2="]" k="33" />
<hkern u1="&#xeb;" u2="X" k="11" />
<hkern u1="&#xeb;" u2="Y" k="84" />
<hkern u1="&#xeb;" u2="Z" k="7" />
<hkern u1="&#xeb;" u2="W" k="37" />
<hkern u1="&#xeb;" u2="V" k="44" />
<hkern u1="&#xeb;" u2="T" k="75" />
<hkern u1="&#xeb;" u2="A" k="8" />
<hkern u1="&#xeb;" u2="x" k="15" />
<hkern u1="&#xeb;" u2="y" k="11" />
<hkern u1="&#xeb;" u2="w" k="6" />
<hkern u1="&#xeb;" u2="v" k="9" />
<hkern u1="&#xed;" u2="U" k="5" />
<hkern u1="&#xed;" u2="Z" k="5" />
<hkern u1="&#xec;" u2="U" k="5" />
<hkern u1="&#xec;" u2="Z" k="5" />
<hkern u1="&#xee;" u2="U" k="5" />
<hkern u1="&#xee;" u2="Z" k="5" />
<hkern u1="&#xef;" u2="\" k="-17" />
<hkern u1="&#xef;" u2="U" k="5" />
<hkern u1="&#xef;" u2="Z" k="5" />
<hkern u1="&#xfa;" u2="&#x29;" k="26" />
<hkern u1="&#xfa;" u2="&#x2122;" k="17" />
<hkern u1="&#xfa;" u2="&#x7d;" k="26" />
<hkern u1="&#xfa;" u2="\" k="16" />
<hkern u1="&#xfa;" u2="]" k="26" />
<hkern u1="&#xfa;" u2="Y" k="54" />
<hkern u1="&#xfa;" u2="Z" k="5" />
<hkern u1="&#xfa;" u2="W" k="26" />
<hkern u1="&#xfa;" u2="V" k="37" />
<hkern u1="&#xfa;" u2="T" k="87" />
<hkern u1="&#xf9;" u2="&#x29;" k="26" />
<hkern u1="&#xf9;" u2="&#x2122;" k="17" />
<hkern u1="&#xf9;" u2="&#x7d;" k="26" />
<hkern u1="&#xf9;" u2="\" k="16" />
<hkern u1="&#xf9;" u2="]" k="26" />
<hkern u1="&#xf9;" u2="Y" k="54" />
<hkern u1="&#xf9;" u2="Z" k="5" />
<hkern u1="&#xf9;" u2="W" k="26" />
<hkern u1="&#xf9;" u2="V" k="37" />
<hkern u1="&#xf9;" u2="T" k="87" />
<hkern u1="&#xfb;" u2="&#x29;" k="26" />
<hkern u1="&#xfb;" u2="&#x2122;" k="17" />
<hkern u1="&#xfb;" u2="&#x7d;" k="26" />
<hkern u1="&#xfb;" u2="\" k="16" />
<hkern u1="&#xfb;" u2="]" k="26" />
<hkern u1="&#xfb;" u2="Y" k="54" />
<hkern u1="&#xfb;" u2="Z" k="5" />
<hkern u1="&#xfb;" u2="W" k="26" />
<hkern u1="&#xfb;" u2="V" k="37" />
<hkern u1="&#xfb;" u2="T" k="87" />
<hkern u1="&#xfc;" u2="&#x29;" k="26" />
<hkern u1="&#xfc;" u2="&#x2122;" k="17" />
<hkern u1="&#xfc;" u2="&#x7d;" k="26" />
<hkern u1="&#xfc;" u2="\" k="16" />
<hkern u1="&#xfc;" u2="]" k="26" />
<hkern u1="&#xfc;" u2="Y" k="54" />
<hkern u1="&#xfc;" u2="Z" k="5" />
<hkern u1="&#xfc;" u2="W" k="26" />
<hkern u1="&#xfc;" u2="V" k="37" />
<hkern u1="&#xfc;" u2="T" k="87" />
<hkern u1="&#xc0;" u2="&#xba;" k="30" />
<hkern u1="&#xc0;" u2="&#xaa;" k="28" />
<hkern u1="&#xc0;" u2="d" k="10" />
<hkern u1="&#xc0;" u2="&#x166;" k="45" />
<hkern u1="&#xc0;" g2="hyphen.case" k="10" />
<hkern u1="&#xc0;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc0;" u2="&#x29;" k="16" />
<hkern u1="&#xc0;" g2="nine.plf" k="10" />
<hkern u1="&#xc0;" g2="seven.plf" k="14" />
<hkern u1="&#xc0;" g2="six.plf" k="11" />
<hkern u1="&#xc0;" g2="one.plf" k="44" />
<hkern u1="&#xc0;" g2="zero.plf" k="12" />
<hkern u1="&#xc0;" u2="f" k="17" />
<hkern u1="&#xc0;" u2="&#x2039;" k="13" />
<hkern u1="&#xc0;" u2="&#x2a;" k="34" />
<hkern u1="&#xc0;" u2="&#x2122;" k="43" />
<hkern u1="&#xc0;" u2="&#x27;" k="38" />
<hkern u1="&#xc0;" u2="&#xf0;" k="9" />
<hkern u1="&#xc0;" u2="&#x7d;" k="33" />
<hkern u1="&#xc0;" u2="&#xae;" k="16" />
<hkern u1="&#xc0;" u2="s" k="6" />
<hkern u1="&#xc0;" u2="&#x2018;" k="38" />
<hkern u1="&#xc0;" u2="&#x2019;" k="40" />
<hkern u1="&#xc0;" u2="&#xa9;" k="16" />
<hkern u1="&#xc0;" u2="&#x3f;" k="34" />
<hkern u1="&#xc0;" u2="\" k="50" />
<hkern u1="&#xc0;" u2="]" k="33" />
<hkern u1="&#xc0;" u2="U" k="13" />
<hkern u1="&#xc0;" u2="Y" k="57" />
<hkern u1="&#xc0;" u2="W" k="33" />
<hkern u1="&#xc0;" u2="V" k="42" />
<hkern u1="&#xc0;" u2="T" k="53" />
<hkern u1="&#xc0;" u2="S" k="10" />
<hkern u1="&#xc0;" u2="O" k="17" />
<hkern u1="&#xc0;" u2="y" k="28" />
<hkern u1="&#xc0;" u2="w" k="19" />
<hkern u1="&#xc0;" u2="v" k="26" />
<hkern u1="&#xc0;" u2="u" k="6" />
<hkern u1="&#xc0;" u2="o" k="10" />
<hkern u1="&#xc0;" u2="a" k="5" />
<hkern u1="&#xc0;" u2="&#x20;" k="26" />
<hkern u1="&#xff;" u2="d" k="11" />
<hkern u1="&#xff;" u2="&#x29;" k="25" />
<hkern u1="&#xff;" u2="&#x2039;" k="19" />
<hkern u1="&#xff;" u2="&#xc6;" k="34" />
<hkern u1="&#xff;" u2="&#xf0;" k="15" />
<hkern u1="&#xff;" u2="&#x7d;" k="16" />
<hkern u1="&#xff;" u2="s" k="8" />
<hkern u1="&#xff;" u2="]" k="16" />
<hkern u1="&#xff;" u2="&#x2f;" k="27" />
<hkern u1="&#xff;" u2="X" k="27" />
<hkern u1="&#xff;" u2="Y" k="24" />
<hkern u1="&#xff;" u2="Z" k="12" />
<hkern u1="&#xff;" u2="J" k="59" />
<hkern u1="&#xff;" u2="T" k="46" />
<hkern u1="&#xff;" u2="A" k="26" />
<hkern u1="&#xff;" u2="&#x2e;" k="34" />
<hkern u1="&#xff;" u2="o" k="11" />
<hkern u1="&#xff;" u2="a" k="10" />
<hkern u1="&#xff;" u2="&#x20;" k="22" />
<hkern u1="&#xc2;" u2="&#xba;" k="30" />
<hkern u1="&#xc2;" u2="&#xaa;" k="28" />
<hkern u1="&#xc2;" u2="d" k="10" />
<hkern u1="&#xc2;" u2="&#x166;" k="45" />
<hkern u1="&#xc2;" g2="hyphen.case" k="10" />
<hkern u1="&#xc2;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc2;" u2="&#x29;" k="16" />
<hkern u1="&#xc2;" g2="nine.plf" k="10" />
<hkern u1="&#xc2;" g2="seven.plf" k="14" />
<hkern u1="&#xc2;" g2="six.plf" k="11" />
<hkern u1="&#xc2;" g2="one.plf" k="44" />
<hkern u1="&#xc2;" g2="zero.plf" k="12" />
<hkern u1="&#xc2;" u2="f" k="17" />
<hkern u1="&#xc2;" u2="&#x2039;" k="13" />
<hkern u1="&#xc2;" u2="&#x2a;" k="34" />
<hkern u1="&#xc2;" u2="&#x2122;" k="43" />
<hkern u1="&#xc2;" u2="&#x27;" k="38" />
<hkern u1="&#xc2;" u2="&#xf0;" k="9" />
<hkern u1="&#xc2;" u2="&#x7d;" k="33" />
<hkern u1="&#xc2;" u2="&#xae;" k="16" />
<hkern u1="&#xc2;" u2="s" k="6" />
<hkern u1="&#xc2;" u2="&#x2018;" k="38" />
<hkern u1="&#xc2;" u2="&#x2019;" k="40" />
<hkern u1="&#xc2;" u2="&#xa9;" k="16" />
<hkern u1="&#xc2;" u2="&#x3f;" k="34" />
<hkern u1="&#xc2;" u2="\" k="50" />
<hkern u1="&#xc2;" u2="]" k="33" />
<hkern u1="&#xc2;" u2="U" k="13" />
<hkern u1="&#xc2;" u2="Y" k="57" />
<hkern u1="&#xc2;" u2="W" k="33" />
<hkern u1="&#xc2;" u2="V" k="42" />
<hkern u1="&#xc2;" u2="T" k="53" />
<hkern u1="&#xc2;" u2="S" k="10" />
<hkern u1="&#xc2;" u2="O" k="17" />
<hkern u1="&#xc2;" u2="y" k="28" />
<hkern u1="&#xc2;" u2="w" k="19" />
<hkern u1="&#xc2;" u2="v" k="26" />
<hkern u1="&#xc2;" u2="u" k="6" />
<hkern u1="&#xc2;" u2="o" k="10" />
<hkern u1="&#xc2;" u2="a" k="5" />
<hkern u1="&#xc2;" u2="&#x20;" k="26" />
<hkern u1="&#xc1;" u2="&#xba;" k="30" />
<hkern u1="&#xc1;" u2="&#xaa;" k="28" />
<hkern u1="&#xc1;" u2="d" k="10" />
<hkern u1="&#xc1;" u2="&#x166;" k="45" />
<hkern u1="&#xc1;" g2="hyphen.case" k="10" />
<hkern u1="&#xc1;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc1;" u2="&#x29;" k="16" />
<hkern u1="&#xc1;" g2="nine.plf" k="10" />
<hkern u1="&#xc1;" g2="seven.plf" k="14" />
<hkern u1="&#xc1;" g2="six.plf" k="11" />
<hkern u1="&#xc1;" g2="one.plf" k="44" />
<hkern u1="&#xc1;" g2="zero.plf" k="12" />
<hkern u1="&#xc1;" u2="f" k="17" />
<hkern u1="&#xc1;" u2="&#x2039;" k="13" />
<hkern u1="&#xc1;" u2="&#x2a;" k="34" />
<hkern u1="&#xc1;" u2="&#x2122;" k="43" />
<hkern u1="&#xc1;" u2="&#x27;" k="38" />
<hkern u1="&#xc1;" u2="&#xf0;" k="9" />
<hkern u1="&#xc1;" u2="&#x7d;" k="33" />
<hkern u1="&#xc1;" u2="&#xae;" k="16" />
<hkern u1="&#xc1;" u2="s" k="6" />
<hkern u1="&#xc1;" u2="&#x2018;" k="38" />
<hkern u1="&#xc1;" u2="&#x2019;" k="40" />
<hkern u1="&#xc1;" u2="&#xa9;" k="16" />
<hkern u1="&#xc1;" u2="&#x3f;" k="34" />
<hkern u1="&#xc1;" u2="\" k="50" />
<hkern u1="&#xc1;" u2="]" k="33" />
<hkern u1="&#xc1;" u2="U" k="13" />
<hkern u1="&#xc1;" u2="Y" k="57" />
<hkern u1="&#xc1;" u2="W" k="33" />
<hkern u1="&#xc1;" u2="V" k="42" />
<hkern u1="&#xc1;" u2="T" k="53" />
<hkern u1="&#xc1;" u2="S" k="10" />
<hkern u1="&#xc1;" u2="O" k="17" />
<hkern u1="&#xc1;" u2="y" k="28" />
<hkern u1="&#xc1;" u2="w" k="19" />
<hkern u1="&#xc1;" u2="v" k="26" />
<hkern u1="&#xc1;" u2="u" k="6" />
<hkern u1="&#xc1;" u2="o" k="10" />
<hkern u1="&#xc1;" u2="a" k="5" />
<hkern u1="&#xc1;" u2="&#x20;" k="26" />
<hkern u1="&#xcd;" u2="d" k="6" />
<hkern u1="&#xcd;" u2="&#xf0;" k="7" />
<hkern u1="&#xcd;" u2="o" k="6" />
<hkern u1="&#xce;" u2="d" k="6" />
<hkern u1="&#xce;" u2="&#xf0;" k="7" />
<hkern u1="&#xce;" u2="o" k="6" />
<hkern u1="&#xcf;" u2="d" k="6" />
<hkern u1="&#xcf;" u2="&#xf0;" k="7" />
<hkern u1="&#xcf;" u2="o" k="6" />
<hkern u1="&#xcc;" u2="d" k="6" />
<hkern u1="&#xcc;" u2="&#xf0;" k="7" />
<hkern u1="&#xcc;" u2="o" k="6" />
<hkern u1="&#xda;" u2="d" k="6" />
<hkern u1="&#xda;" g2="parenright.case" k="14" />
<hkern u1="&#xda;" u2="i" k="5" />
<hkern u1="&#xda;" u2="&#xc6;" k="11" />
<hkern u1="&#xda;" u2="&#xf0;" k="7" />
<hkern u1="&#xda;" u2="s" k="5" />
<hkern u1="&#xda;" u2="J" k="14" />
<hkern u1="&#xda;" u2="A" k="13" />
<hkern u1="&#xda;" u2="&#x2e;" k="8" />
<hkern u1="&#xda;" u2="z" k="7" />
<hkern u1="&#xda;" u2="u" k="7" />
<hkern u1="&#xda;" u2="o" k="5" />
<hkern u1="&#xda;" u2="a" k="6" />
<hkern u1="&#xdb;" u2="d" k="6" />
<hkern u1="&#xdb;" g2="parenright.case" k="14" />
<hkern u1="&#xdb;" u2="i" k="5" />
<hkern u1="&#xdb;" u2="&#xc6;" k="11" />
<hkern u1="&#xdb;" u2="&#xf0;" k="7" />
<hkern u1="&#xdb;" u2="s" k="5" />
<hkern u1="&#xdb;" u2="J" k="14" />
<hkern u1="&#xdb;" u2="A" k="13" />
<hkern u1="&#xdb;" u2="&#x2e;" k="8" />
<hkern u1="&#xdb;" u2="z" k="7" />
<hkern u1="&#xdb;" u2="u" k="7" />
<hkern u1="&#xdb;" u2="o" k="5" />
<hkern u1="&#xdb;" u2="a" k="6" />
<hkern u1="&#xd9;" u2="d" k="6" />
<hkern u1="&#xd9;" g2="parenright.case" k="14" />
<hkern u1="&#xd9;" u2="i" k="5" />
<hkern u1="&#xd9;" u2="&#xc6;" k="11" />
<hkern u1="&#xd9;" u2="&#xf0;" k="7" />
<hkern u1="&#xd9;" u2="s" k="5" />
<hkern u1="&#xd9;" u2="J" k="14" />
<hkern u1="&#xd9;" u2="A" k="13" />
<hkern u1="&#xd9;" u2="&#x2e;" k="8" />
<hkern u1="&#xd9;" u2="z" k="7" />
<hkern u1="&#xd9;" u2="u" k="7" />
<hkern u1="&#xd9;" u2="o" k="5" />
<hkern u1="&#xd9;" u2="a" k="6" />
<hkern u1="&#xc7;" g2="parenright.case" k="21" />
<hkern u1="&#xc7;" g2="braceright.case" k="23" />
<hkern u1="&#xc7;" g2="bracketright.case" k="23" />
<hkern u1="&#xc7;" u2="&#x29;" k="18" />
<hkern u1="&#xc7;" u2="&#x7d;" k="17" />
<hkern u1="&#xc7;" u2="]" k="16" />
<hkern u1="&#xc7;" u2="Y" k="16" />
<hkern u1="&#xc7;" u2="W" k="7" />
<hkern u1="&#xc7;" u2="V" k="12" />
<hkern u1="&#xc7;" u2="A" k="7" />
<hkern u1="&#x201a;" g2="seven.plf" k="13" />
<hkern u1="&#x201a;" g2="one.plf" k="44" />
<hkern u1="&#x201a;" u2="f" k="10" />
<hkern u1="&#x201a;" u2="&#x27;" k="104" />
<hkern u1="&#x201a;" u2="&#x2018;" k="107" />
<hkern u1="&#x201a;" u2="&#x2019;" k="108" />
<hkern u1="&#x201a;" u2="U" k="8" />
<hkern u1="&#x201a;" u2="Y" k="62" />
<hkern u1="&#x201a;" u2="W" k="40" />
<hkern u1="&#x201a;" u2="V" k="54" />
<hkern u1="&#x201a;" u2="T" k="49" />
<hkern u1="&#x201a;" u2="O" k="9" />
<hkern u1="&#x201a;" u2="y" k="36" />
<hkern u1="&#x201a;" u2="w" k="24" />
<hkern u1="&#x201a;" u2="v" k="33" />
<hkern u1="&#x201e;" g2="seven.plf" k="13" />
<hkern u1="&#x201e;" g2="one.plf" k="44" />
<hkern u1="&#x201e;" u2="f" k="10" />
<hkern u1="&#x201e;" u2="&#x27;" k="104" />
<hkern u1="&#x201e;" u2="&#x2018;" k="107" />
<hkern u1="&#x201e;" u2="&#x2019;" k="108" />
<hkern u1="&#x201e;" u2="U" k="8" />
<hkern u1="&#x201e;" u2="Y" k="62" />
<hkern u1="&#x201e;" u2="W" k="40" />
<hkern u1="&#x201e;" u2="V" k="54" />
<hkern u1="&#x201e;" u2="T" k="49" />
<hkern u1="&#x201e;" u2="O" k="9" />
<hkern u1="&#x201e;" u2="y" k="36" />
<hkern u1="&#x201e;" u2="w" k="24" />
<hkern u1="&#x201e;" u2="v" k="33" />
<hkern u1="&#x201d;" u2="d" k="25" />
<hkern u1="&#x201d;" g2="guilsinglleft.case" k="12" />
<hkern u1="&#x201d;" u2="&#x2039;" k="28" />
<hkern u1="&#x201d;" u2="&#xc6;" k="52" />
<hkern u1="&#x201d;" u2="&#xf0;" k="20" />
<hkern u1="&#x201d;" u2="s" k="15" />
<hkern u1="&#x201d;" u2="&#x2f;" k="54" />
<hkern u1="&#x201d;" u2="J" k="50" />
<hkern u1="&#x201d;" u2="A" k="45" />
<hkern u1="&#x201d;" u2="&#x2e;" k="118" />
<hkern u1="&#x201d;" u2="o" k="23" />
<hkern u1="&#x201d;" u2="a" k="18" />
<hkern u1="&#x201d;" u2="&#x20;" k="18" />
<hkern u1="&#x201c;" u2="d" k="14" />
<hkern u1="&#x201c;" u2="&#xc6;" k="45" />
<hkern u1="&#x201c;" u2="&#xf0;" k="17" />
<hkern u1="&#x201c;" u2="J" k="50" />
<hkern u1="&#x201c;" u2="A" k="39" />
<hkern u1="&#x201c;" u2="&#x2e;" k="108" />
<hkern u1="&#x201c;" u2="o" k="12" />
<hkern u1="&#x201c;" u2="a" k="8" />
<hkern u1="&#x2019;" u2="d" k="25" />
<hkern u1="&#x2019;" g2="guilsinglleft.case" k="12" />
<hkern u1="&#x2019;" u2="&#x2039;" k="28" />
<hkern u1="&#x2019;" u2="&#xc6;" k="52" />
<hkern u1="&#x2019;" u2="&#xf0;" k="20" />
<hkern u1="&#x2019;" u2="s" k="15" />
<hkern u1="&#x2019;" u2="&#x2f;" k="54" />
<hkern u1="&#x2019;" u2="J" k="50" />
<hkern u1="&#x2019;" u2="A" k="45" />
<hkern u1="&#x2019;" u2="&#x2e;" k="118" />
<hkern u1="&#x2019;" u2="o" k="23" />
<hkern u1="&#x2019;" u2="a" k="18" />
<hkern u1="&#x2019;" u2="&#x20;" k="18" />
<hkern u1="&#x2018;" u2="d" k="14" />
<hkern u1="&#x2018;" u2="&#xc6;" k="45" />
<hkern u1="&#x2018;" u2="&#xf0;" k="17" />
<hkern u1="&#x2018;" u2="J" k="50" />
<hkern u1="&#x2018;" u2="A" k="39" />
<hkern u1="&#x2018;" u2="&#x2e;" k="108" />
<hkern u1="&#x2018;" u2="o" k="12" />
<hkern u1="&#x2018;" u2="a" k="8" />
<hkern u1="&#xab;" u2="&#x166;" k="26" />
<hkern u1="&#xab;" u2="Y" k="40" />
<hkern u1="&#xab;" u2="W" k="14" />
<hkern u1="&#xab;" u2="V" k="22" />
<hkern u1="&#xab;" u2="T" k="59" />
<hkern u1="&#x40;" u2="&#x104;" k="11" />
<hkern u1="&#x40;" u2="&#x1fa;" k="11" />
<hkern u1="&#x40;" u2="&#x1ef2;" k="19" />
<hkern u1="&#x40;" u2="&#x1e84;" k="12" />
<hkern u1="&#x40;" u2="&#x1e82;" k="12" />
<hkern u1="&#x40;" u2="&#x1e80;" k="12" />
<hkern u1="&#x40;" u2="&#x176;" k="19" />
<hkern u1="&#x40;" u2="&#x174;" k="12" />
<hkern u1="&#x40;" u2="&#x102;" k="11" />
<hkern u1="&#x40;" u2="&#x100;" k="11" />
<hkern u1="&#x40;" u2="&#xc3;" k="11" />
<hkern u1="&#x40;" u2="&#xc5;" k="11" />
<hkern u1="&#x40;" u2="&#xc1;" k="11" />
<hkern u1="&#x40;" u2="&#xc2;" k="11" />
<hkern u1="&#x40;" u2="&#x178;" k="19" />
<hkern u1="&#x40;" u2="&#xc0;" k="11" />
<hkern u1="&#x40;" u2="&#xc4;" k="11" />
<hkern u1="&#x40;" u2="&#xdd;" k="19" />
<hkern u1="&#x40;" u2="Y" k="19" />
<hkern u1="&#x40;" u2="W" k="12" />
<hkern u1="&#x40;" u2="A" k="11" />
<hkern u1="&#x40;" u2="V" k="16" />
<hkern u1="s" u2="&#x29;" k="32" />
<hkern u1="s" u2="&#x2122;" k="23" />
<hkern u1="s" u2="&#x7d;" k="32" />
<hkern u1="s" u2="&#x2019;" k="9" />
<hkern u1="s" u2="\" k="31" />
<hkern u1="s" u2="]" k="32" />
<hkern u1="s" u2="X" k="5" />
<hkern u1="s" u2="U" k="6" />
<hkern u1="s" u2="Y" k="81" />
<hkern u1="s" u2="W" k="39" />
<hkern u1="s" u2="V" k="44" />
<hkern u1="s" u2="T" k="74" />
<hkern u1="s" u2="A" k="6" />
<hkern u1="s" u2="x" k="5" />
<hkern u1="s" u2="y" k="11" />
<hkern u1="s" u2="w" k="6" />
<hkern u1="s" u2="v" k="9" />
<hkern u1="g" u2="&#x29;" k="26" />
<hkern u1="g" u2="&#x2122;" k="17" />
<hkern u1="g" u2="&#x7d;" k="26" />
<hkern u1="g" u2="\" k="16" />
<hkern u1="g" u2="]" k="26" />
<hkern u1="g" u2="Y" k="54" />
<hkern u1="g" u2="Z" k="5" />
<hkern u1="g" u2="W" k="26" />
<hkern u1="g" u2="V" k="37" />
<hkern u1="g" u2="T" k="87" />
<hkern u1="&#x7b;" g2="zero.plfslash" k="23" />
<hkern u1="&#x7b;" u2="d" k="36" />
<hkern u1="&#x7b;" u2="&#x105;" k="32" />
<hkern u1="&#x7b;" u2="&#x104;" k="33" />
<hkern u1="&#x7b;" u2="&#x173;" k="25" />
<hkern u1="&#x7b;" u2="&#x119;" k="36" />
<hkern u1="&#x7b;" u2="&#x1fa;" k="33" />
<hkern u1="&#x7b;" u2="&#x1fb;" k="32" />
<hkern u1="&#x7b;" u2="&#x219;" k="31" />
<hkern u1="&#x7b;" u2="&#x218;" k="16" />
<hkern u1="&#x7b;" u2="&#x15f;" k="31" />
<hkern u1="&#x7b;" u2="&#x15e;" k="16" />
<hkern u1="&#x7b;" u2="&#x157;" k="26" />
<hkern u1="&#x7b;" u2="&#x146;" k="26" />
<hkern u1="&#x7b;" u2="&#x122;" k="25" />
<hkern u1="&#x7b;" u2="&#x149;" k="26" />
<hkern u1="&#x7b;" u2="&#x111;" k="36" />
<hkern u1="&#x7b;" u2="&#x1fd;" k="32" />
<hkern u1="&#x7b;" u2="&#x1ff;" k="36" />
<hkern u1="&#x7b;" u2="&#x1fe;" k="25" />
<hkern u1="&#x7b;" u2="&#x14b;" k="26" />
<hkern u1="&#x7b;" u2="&#x1fc;" k="19" />
<hkern u1="&#x7b;" u2="&#x1e85;" k="17" />
<hkern u1="&#x7b;" u2="&#x1e83;" k="17" />
<hkern u1="&#x7b;" u2="&#x1e81;" k="17" />
<hkern u1="&#x7b;" u2="&#x17c;" k="24" />
<hkern u1="&#x7b;" u2="&#x17a;" k="24" />
<hkern u1="&#x7b;" u2="&#x175;" k="17" />
<hkern u1="&#x7b;" u2="&#x171;" k="25" />
<hkern u1="&#x7b;" u2="&#x16f;" k="25" />
<hkern u1="&#x7b;" u2="&#x169;" k="25" />
<hkern u1="&#x7b;" u2="&#x15d;" k="31" />
<hkern u1="&#x7b;" u2="&#x15c;" k="16" />
<hkern u1="&#x7b;" u2="&#x15b;" k="31" />
<hkern u1="&#x7b;" u2="&#x15a;" k="16" />
<hkern u1="&#x7b;" u2="&#x159;" k="26" />
<hkern u1="&#x7b;" u2="&#x155;" k="26" />
<hkern u1="&#x7b;" u2="&#x151;" k="36" />
<hkern u1="&#x7b;" u2="&#x150;" k="25" />
<hkern u1="&#x7b;" u2="&#x148;" k="26" />
<hkern u1="&#x7b;" u2="&#x144;" k="26" />
<hkern u1="&#x7b;" u2="&#x134;" k="22" />
<hkern u1="&#x7b;" u2="&#x123;" k="36" />
<hkern u1="&#x7b;" u2="&#x121;" k="36" />
<hkern u1="&#x7b;" u2="&#x11d;" k="36" />
<hkern u1="&#x7b;" u2="&#x11c;" k="25" />
<hkern u1="&#x7b;" u2="&#x11b;" k="36" />
<hkern u1="&#x7b;" u2="&#x117;" k="36" />
<hkern u1="&#x7b;" u2="&#x16d;" k="25" />
<hkern u1="&#x7b;" u2="&#x14f;" k="36" />
<hkern u1="&#x7b;" u2="&#x14e;" k="25" />
<hkern u1="&#x7b;" u2="&#x11f;" k="36" />
<hkern u1="&#x7b;" u2="&#x11e;" k="25" />
<hkern u1="&#x7b;" u2="&#x115;" k="36" />
<hkern u1="&#x7b;" u2="&#x16b;" k="25" />
<hkern u1="&#x7b;" u2="&#x14d;" k="36" />
<hkern u1="&#x7b;" u2="&#x14c;" k="25" />
<hkern u1="&#x7b;" u2="&#x113;" k="36" />
<hkern u1="&#x7b;" u2="&#x10f;" k="36" />
<hkern u1="&#x7b;" u2="&#x10d;" k="36" />
<hkern u1="&#x7b;" u2="&#x10c;" k="25" />
<hkern u1="&#x7b;" u2="&#x10b;" k="36" />
<hkern u1="&#x7b;" u2="&#x10a;" k="25" />
<hkern u1="&#x7b;" u2="&#x109;" k="36" />
<hkern u1="&#x7b;" u2="&#x108;" k="25" />
<hkern u1="&#x7b;" u2="&#x107;" k="36" />
<hkern u1="&#x7b;" u2="&#x106;" k="25" />
<hkern u1="&#x7b;" u2="&#x103;" k="32" />
<hkern u1="&#x7b;" u2="&#x102;" k="33" />
<hkern u1="&#x7b;" u2="&#x101;" k="32" />
<hkern u1="&#x7b;" u2="&#x100;" k="33" />
<hkern u1="&#x7b;" u2="&#x120;" k="25" />
<hkern u1="&#x7b;" g2="zero.plf" k="23" />
<hkern u1="&#x7b;" u2="&#xd5;" k="25" />
<hkern u1="&#x7b;" u2="&#xc3;" k="33" />
<hkern u1="&#x7b;" u2="&#xf5;" k="36" />
<hkern u1="&#x7b;" u2="&#xf1;" k="26" />
<hkern u1="&#x7b;" u2="&#xe3;" k="32" />
<hkern u1="&#x7b;" u2="n" k="26" />
<hkern u1="&#x7b;" u2="&#xd8;" k="25" />
<hkern u1="&#x7b;" u2="&#xc6;" k="19" />
<hkern u1="&#x7b;" u2="&#x152;" k="25" />
<hkern u1="&#x7b;" u2="&#x153;" k="36" />
<hkern u1="&#x7b;" u2="&#xe6;" k="32" />
<hkern u1="&#x7b;" u2="&#xf8;" k="36" />
<hkern u1="&#x7b;" u2="&#xc5;" k="33" />
<hkern u1="&#x7b;" u2="&#xe5;" k="32" />
<hkern u1="&#x7b;" u2="&#x17e;" k="24" />
<hkern u1="&#x7b;" u2="&#x161;" k="31" />
<hkern u1="&#x7b;" u2="&#x160;" k="16" />
<hkern u1="&#x7b;" u2="&#xd6;" k="25" />
<hkern u1="&#x7b;" u2="&#xd2;" k="25" />
<hkern u1="&#x7b;" u2="&#xd4;" k="25" />
<hkern u1="&#x7b;" u2="&#xd3;" k="25" />
<hkern u1="&#x7b;" u2="g" k="36" />
<hkern u1="&#x7b;" u2="s" k="31" />
<hkern u1="&#x7b;" u2="&#xc7;" k="25" />
<hkern u1="&#x7b;" u2="&#xc1;" k="33" />
<hkern u1="&#x7b;" u2="&#xc2;" k="33" />
<hkern u1="&#x7b;" u2="&#xc0;" k="33" />
<hkern u1="&#x7b;" u2="&#xfc;" k="25" />
<hkern u1="&#x7b;" u2="&#xfb;" k="25" />
<hkern u1="&#x7b;" u2="&#xf9;" k="25" />
<hkern u1="&#x7b;" u2="&#xfa;" k="25" />
<hkern u1="&#x7b;" u2="&#xf6;" k="36" />
<hkern u1="&#x7b;" u2="&#xf4;" k="36" />
<hkern u1="&#x7b;" u2="&#xf2;" k="36" />
<hkern u1="&#x7b;" u2="&#xf3;" k="36" />
<hkern u1="&#x7b;" u2="&#xeb;" k="36" />
<hkern u1="&#x7b;" u2="&#xea;" k="36" />
<hkern u1="&#x7b;" u2="&#xe8;" k="36" />
<hkern u1="&#x7b;" u2="&#xe9;" k="36" />
<hkern u1="&#x7b;" u2="&#xe4;" k="32" />
<hkern u1="&#x7b;" u2="&#xe2;" k="32" />
<hkern u1="&#x7b;" u2="&#xe0;" k="32" />
<hkern u1="&#x7b;" u2="&#xe1;" k="32" />
<hkern u1="&#x7b;" u2="&#xc4;" k="33" />
<hkern u1="&#x7b;" u2="&#xe7;" k="36" />
<hkern u1="&#x7b;" u2="G" k="25" />
<hkern u1="&#x7b;" u2="C" k="25" />
<hkern u1="&#x7b;" u2="J" k="22" />
<hkern u1="&#x7b;" u2="Q" k="25" />
<hkern u1="&#x7b;" u2="S" k="16" />
<hkern u1="&#x7b;" u2="O" k="25" />
<hkern u1="&#x7b;" u2="A" k="33" />
<hkern u1="&#x7b;" u2="z" k="24" />
<hkern u1="&#x7b;" u2="w" k="17" />
<hkern u1="&#x7b;" u2="r" k="26" />
<hkern u1="&#x7b;" u2="m" k="26" />
<hkern u1="&#x7b;" u2="c" k="36" />
<hkern u1="&#x7b;" u2="e" k="36" />
<hkern u1="&#x7b;" u2="u" k="25" />
<hkern u1="&#x7b;" u2="o" k="36" />
<hkern u1="&#x7b;" u2="a" k="32" />
<hkern u1="&#x7b;" u2="q" k="36" />
<hkern u1="&#x7b;" u2="p" k="26" />
<hkern u1="&#x7b;" u2="&#x135;" k="-13" />
<hkern u1="&#x7b;" u2="&#x129;" k="-7" />
<hkern u1="&#x7b;" u2="&#x12b;" k="-7" />
<hkern u1="&#x7b;" g2="nine.plf" k="17" />
<hkern u1="&#x7b;" g2="eight.plf" k="20" />
<hkern u1="&#x7b;" g2="six.plf" k="25" />
<hkern u1="&#x7b;" g2="four.plf" k="38" />
<hkern u1="&#x7b;" g2="three.plf" k="18" />
<hkern u1="&#x7b;" g2="two.plf" k="11" />
<hkern u1="&#x7b;" u2="&#xf0;" k="31" />
<hkern u1="&#x7b;" u2="&#x7b;" k="11" />
<hkern u1="&#x7b;" u2="&#x28;" k="19" />
<hkern u1="&#x7b;" u2="x" k="18" />
<hkern u1="&#x7b;" u2="v" k="17" />
<hkern u1="&#x7d;" u2="&#x29;" k="10" />
<hkern u1="&#x7d;" u2="&#x7d;" k="11" />
<hkern u1="&#x7d;" u2="]" k="10" />
<hkern u1="&#xa1;" u2="&#x21a;" k="43" />
<hkern u1="&#xa1;" u2="&#x162;" k="43" />
<hkern u1="&#xa1;" u2="&#x1ef2;" k="27" />
<hkern u1="&#xa1;" u2="&#x1e84;" k="11" />
<hkern u1="&#xa1;" u2="&#x1e82;" k="11" />
<hkern u1="&#xa1;" u2="&#x1e80;" k="11" />
<hkern u1="&#xa1;" u2="&#x176;" k="27" />
<hkern u1="&#xa1;" u2="&#x174;" k="11" />
<hkern u1="&#xa1;" u2="&#x164;" k="43" />
<hkern u1="&#xa1;" u2="&#x178;" k="27" />
<hkern u1="&#xa1;" u2="&#xdd;" k="27" />
<hkern u1="&#xa1;" u2="Y" k="27" />
<hkern u1="&#xa1;" u2="W" k="11" />
<hkern u1="&#xa1;" u2="T" k="43" />
<hkern u1="&#xa1;" u2="&#x166;" k="16" />
<hkern u1="&#xa1;" u2="V" k="17" />
<hkern u1="&#x131;" u2="U" k="5" />
<hkern u1="&#x131;" u2="Z" k="5" />
<hkern u1="&#xbf;" u2="d" k="21" />
<hkern u1="&#xbf;" u2="&#x173;" k="15" />
<hkern u1="&#xbf;" u2="&#x172;" k="16" />
<hkern u1="&#xbf;" u2="&#x119;" k="22" />
<hkern u1="&#xbf;" u2="&#x167;" k="14" />
<hkern u1="&#xbf;" u2="&#x219;" k="10" />
<hkern u1="&#xbf;" u2="&#x21a;" k="58" />
<hkern u1="&#xbf;" u2="&#x21b;" k="14" />
<hkern u1="&#xbf;" u2="&#x162;" k="58" />
<hkern u1="&#xbf;" u2="&#x163;" k="14" />
<hkern u1="&#xbf;" u2="&#x15f;" k="10" />
<hkern u1="&#xbf;" u2="&#x122;" k="15" />
<hkern u1="&#xbf;" u2="&#x111;" k="21" />
<hkern u1="&#xbf;" u2="&#x1ff;" k="22" />
<hkern u1="&#xbf;" u2="&#x1fe;" k="15" />
<hkern u1="&#xbf;" u2="&#x165;" k="14" />
<hkern u1="&#xbf;" u2="&#x1ef2;" k="66" />
<hkern u1="&#xbf;" u2="&#x1e84;" k="45" />
<hkern u1="&#xbf;" u2="&#x1e82;" k="45" />
<hkern u1="&#xbf;" u2="&#x1e80;" k="45" />
<hkern u1="&#xbf;" u2="&#x1e85;" k="34" />
<hkern u1="&#xbf;" u2="&#x1e83;" k="34" />
<hkern u1="&#xbf;" u2="&#x1e81;" k="34" />
<hkern u1="&#xbf;" u2="&#x176;" k="66" />
<hkern u1="&#xbf;" u2="&#x175;" k="34" />
<hkern u1="&#xbf;" u2="&#x174;" k="45" />
<hkern u1="&#xbf;" u2="&#x171;" k="15" />
<hkern u1="&#xbf;" u2="&#x170;" k="16" />
<hkern u1="&#xbf;" u2="&#x16f;" k="15" />
<hkern u1="&#xbf;" u2="&#x16e;" k="16" />
<hkern u1="&#xbf;" u2="&#x169;" k="15" />
<hkern u1="&#xbf;" u2="&#x168;" k="16" />
<hkern u1="&#xbf;" u2="&#x164;" k="58" />
<hkern u1="&#xbf;" u2="&#x15d;" k="10" />
<hkern u1="&#xbf;" u2="&#x15b;" k="10" />
<hkern u1="&#xbf;" u2="&#x151;" k="22" />
<hkern u1="&#xbf;" u2="&#x150;" k="15" />
<hkern u1="&#xbf;" u2="&#x123;" k="21" />
<hkern u1="&#xbf;" u2="&#x121;" k="21" />
<hkern u1="&#xbf;" u2="&#x11d;" k="21" />
<hkern u1="&#xbf;" u2="&#x11c;" k="15" />
<hkern u1="&#xbf;" u2="&#x11b;" k="22" />
<hkern u1="&#xbf;" u2="&#x117;" k="22" />
<hkern u1="&#xbf;" u2="&#x16d;" k="15" />
<hkern u1="&#xbf;" u2="&#x16c;" k="16" />
<hkern u1="&#xbf;" u2="&#x14f;" k="22" />
<hkern u1="&#xbf;" u2="&#x14e;" k="15" />
<hkern u1="&#xbf;" u2="&#x11f;" k="21" />
<hkern u1="&#xbf;" u2="&#x11e;" k="15" />
<hkern u1="&#xbf;" u2="&#x115;" k="22" />
<hkern u1="&#xbf;" u2="&#x16b;" k="15" />
<hkern u1="&#xbf;" u2="&#x16a;" k="16" />
<hkern u1="&#xbf;" u2="&#x14d;" k="22" />
<hkern u1="&#xbf;" u2="&#x14c;" k="15" />
<hkern u1="&#xbf;" u2="&#x113;" k="22" />
<hkern u1="&#xbf;" u2="&#x10f;" k="21" />
<hkern u1="&#xbf;" u2="&#x10d;" k="22" />
<hkern u1="&#xbf;" u2="&#x10c;" k="15" />
<hkern u1="&#xbf;" u2="&#x10b;" k="22" />
<hkern u1="&#xbf;" u2="&#x10a;" k="15" />
<hkern u1="&#xbf;" u2="&#x109;" k="22" />
<hkern u1="&#xbf;" u2="&#x108;" k="15" />
<hkern u1="&#xbf;" u2="&#x107;" k="22" />
<hkern u1="&#xbf;" u2="&#x106;" k="15" />
<hkern u1="&#xbf;" u2="&#x120;" k="15" />
<hkern u1="&#xbf;" u2="&#xd5;" k="15" />
<hkern u1="&#xbf;" u2="&#xf5;" k="22" />
<hkern u1="&#xbf;" u2="f" k="14" />
<hkern u1="&#xbf;" u2="&#xd8;" k="15" />
<hkern u1="&#xbf;" u2="&#x152;" k="15" />
<hkern u1="&#xbf;" u2="&#x153;" k="22" />
<hkern u1="&#xbf;" u2="&#xf8;" k="22" />
<hkern u1="&#xbf;" u2="&#x161;" k="10" />
<hkern u1="&#xbf;" u2="&#xd6;" k="15" />
<hkern u1="&#xbf;" u2="&#xd2;" k="15" />
<hkern u1="&#xbf;" u2="&#xd4;" k="15" />
<hkern u1="&#xbf;" u2="&#xd3;" k="15" />
<hkern u1="&#xbf;" u2="g" k="21" />
<hkern u1="&#xbf;" u2="s" k="10" />
<hkern u1="&#xbf;" u2="&#xc7;" k="15" />
<hkern u1="&#xbf;" u2="&#xd9;" k="16" />
<hkern u1="&#xbf;" u2="&#xdb;" k="16" />
<hkern u1="&#xbf;" u2="&#xda;" k="16" />
<hkern u1="&#xbf;" u2="&#x178;" k="66" />
<hkern u1="&#xbf;" u2="&#xfc;" k="15" />
<hkern u1="&#xbf;" u2="&#xfb;" k="15" />
<hkern u1="&#xbf;" u2="&#xf9;" k="15" />
<hkern u1="&#xbf;" u2="&#xfa;" k="15" />
<hkern u1="&#xbf;" u2="&#xf6;" k="22" />
<hkern u1="&#xbf;" u2="&#xf4;" k="22" />
<hkern u1="&#xbf;" u2="&#xf2;" k="22" />
<hkern u1="&#xbf;" u2="&#xf3;" k="22" />
<hkern u1="&#xbf;" u2="&#xeb;" k="22" />
<hkern u1="&#xbf;" u2="&#xea;" k="22" />
<hkern u1="&#xbf;" u2="&#xe8;" k="22" />
<hkern u1="&#xbf;" u2="&#xe9;" k="22" />
<hkern u1="&#xbf;" u2="&#xdc;" k="16" />
<hkern u1="&#xbf;" u2="&#xdd;" k="66" />
<hkern u1="&#xbf;" u2="&#xe7;" k="22" />
<hkern u1="&#xbf;" g2="fl" k="14" />
<hkern u1="&#xbf;" g2="fi" k="14" />
<hkern u1="&#xbf;" u2="G" k="15" />
<hkern u1="&#xbf;" u2="U" k="16" />
<hkern u1="&#xbf;" u2="C" k="15" />
<hkern u1="&#xbf;" u2="Y" k="66" />
<hkern u1="&#xbf;" u2="W" k="45" />
<hkern u1="&#xbf;" u2="Q" k="15" />
<hkern u1="&#xbf;" u2="T" k="58" />
<hkern u1="&#xbf;" u2="O" k="15" />
<hkern u1="&#xbf;" u2="w" k="34" />
<hkern u1="&#xbf;" u2="c" k="22" />
<hkern u1="&#xbf;" u2="e" k="22" />
<hkern u1="&#xbf;" u2="u" k="15" />
<hkern u1="&#xbf;" u2="o" k="22" />
<hkern u1="&#xbf;" u2="t" k="14" />
<hkern u1="&#xbf;" u2="q" k="21" />
<hkern u1="&#xbf;" u2="&#x166;" k="49" />
<hkern u1="&#xbf;" u2="&#xf0;" k="21" />
<hkern u1="&#xbf;" u2="V" k="58" />
<hkern u1="&#xbf;" u2="v" k="41" />
<hkern u1="&#x160;" g2="parenright.case" k="21" />
<hkern u1="&#x160;" g2="braceright.case" k="23" />
<hkern u1="&#x160;" g2="bracketright.case" k="23" />
<hkern u1="&#x160;" u2="&#x29;" k="19" />
<hkern u1="&#x160;" u2="&#x7d;" k="17" />
<hkern u1="&#x160;" u2="]" k="17" />
<hkern u1="&#x160;" u2="X" k="6" />
<hkern u1="&#x160;" u2="Y" k="21" />
<hkern u1="&#x160;" u2="W" k="12" />
<hkern u1="&#x160;" u2="V" k="17" />
<hkern u1="&#x160;" u2="T" k="5" />
<hkern u1="&#x160;" u2="A" k="9" />
<hkern u1="&#x160;" u2="x" k="9" />
<hkern u1="&#x161;" u2="&#x29;" k="32" />
<hkern u1="&#x161;" u2="&#x2122;" k="23" />
<hkern u1="&#x161;" u2="&#x7d;" k="32" />
<hkern u1="&#x161;" u2="&#x2019;" k="9" />
<hkern u1="&#x161;" u2="\" k="31" />
<hkern u1="&#x161;" u2="]" k="32" />
<hkern u1="&#x161;" u2="X" k="5" />
<hkern u1="&#x161;" u2="U" k="6" />
<hkern u1="&#x161;" u2="Y" k="81" />
<hkern u1="&#x161;" u2="W" k="39" />
<hkern u1="&#x161;" u2="V" k="44" />
<hkern u1="&#x161;" u2="T" k="74" />
<hkern u1="&#x161;" u2="A" k="6" />
<hkern u1="&#x161;" u2="x" k="5" />
<hkern u1="&#x161;" u2="y" k="11" />
<hkern u1="&#x161;" u2="w" k="6" />
<hkern u1="&#x161;" u2="v" k="9" />
<hkern u1="&#x17e;" u2="d" k="5" />
<hkern u1="&#x17e;" u2="&#x29;" k="19" />
<hkern u1="&#x17e;" u2="&#x2039;" k="20" />
<hkern u1="&#x17e;" u2="&#x2122;" k="17" />
<hkern u1="&#x17e;" u2="&#xf0;" k="8" />
<hkern u1="&#x17e;" u2="&#x7d;" k="22" />
<hkern u1="&#x17e;" u2="]" k="22" />
<hkern u1="&#x17e;" u2="&#x2d;" k="13" />
<hkern u1="&#x17e;" u2="U" k="5" />
<hkern u1="&#x17e;" u2="Y" k="46" />
<hkern u1="&#x17e;" u2="W" k="12" />
<hkern u1="&#x17e;" u2="V" k="22" />
<hkern u1="&#x17e;" u2="T" k="73" />
<hkern u1="&#x17e;" u2="o" k="5" />
<hkern u1="&#xb7;" u2="l" k="57" />
<hkern u1="&#xb7;" u2="&#x13c;" k="57" />
<hkern u1="&#xb7;" u2="&#x140;" k="57" />
<hkern u1="&#xb7;" u2="&#x13e;" k="57" />
<hkern u1="&#xb7;" u2="&#x13a;" k="57" />
<hkern u1="&#xb7;" u2="&#x142;" k="57" />
<hkern u1="&#xb7;" g2="seven.plf" k="28" />
<hkern u1="&#xb7;" g2="one.plf" k="20" />
<hkern u1="&#x2044;" g2="four.den" k="37" />
<hkern u1="&#xc5;" u2="&#xba;" k="30" />
<hkern u1="&#xc5;" u2="&#xaa;" k="28" />
<hkern u1="&#xc5;" u2="d" k="10" />
<hkern u1="&#xc5;" u2="&#x166;" k="45" />
<hkern u1="&#xc5;" g2="hyphen.case" k="10" />
<hkern u1="&#xc5;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc5;" u2="&#x29;" k="16" />
<hkern u1="&#xc5;" g2="nine.plf" k="10" />
<hkern u1="&#xc5;" g2="seven.plf" k="14" />
<hkern u1="&#xc5;" g2="six.plf" k="11" />
<hkern u1="&#xc5;" g2="one.plf" k="44" />
<hkern u1="&#xc5;" g2="zero.plf" k="12" />
<hkern u1="&#xc5;" u2="f" k="17" />
<hkern u1="&#xc5;" u2="&#x2039;" k="13" />
<hkern u1="&#xc5;" u2="&#x2a;" k="34" />
<hkern u1="&#xc5;" u2="&#x2122;" k="43" />
<hkern u1="&#xc5;" u2="&#x27;" k="38" />
<hkern u1="&#xc5;" u2="&#xf0;" k="9" />
<hkern u1="&#xc5;" u2="&#x7d;" k="33" />
<hkern u1="&#xc5;" u2="&#xae;" k="16" />
<hkern u1="&#xc5;" u2="s" k="6" />
<hkern u1="&#xc5;" u2="&#x2018;" k="38" />
<hkern u1="&#xc5;" u2="&#x2019;" k="40" />
<hkern u1="&#xc5;" u2="&#xa9;" k="16" />
<hkern u1="&#xc5;" u2="&#x3f;" k="34" />
<hkern u1="&#xc5;" u2="\" k="50" />
<hkern u1="&#xc5;" u2="]" k="33" />
<hkern u1="&#xc5;" u2="U" k="13" />
<hkern u1="&#xc5;" u2="Y" k="57" />
<hkern u1="&#xc5;" u2="W" k="33" />
<hkern u1="&#xc5;" u2="V" k="42" />
<hkern u1="&#xc5;" u2="T" k="53" />
<hkern u1="&#xc5;" u2="S" k="10" />
<hkern u1="&#xc5;" u2="O" k="17" />
<hkern u1="&#xc5;" u2="y" k="28" />
<hkern u1="&#xc5;" u2="w" k="19" />
<hkern u1="&#xc5;" u2="v" k="26" />
<hkern u1="&#xc5;" u2="u" k="6" />
<hkern u1="&#xc5;" u2="o" k="10" />
<hkern u1="&#xc5;" u2="a" k="5" />
<hkern u1="&#xc5;" u2="&#x20;" k="26" />
<hkern u1="&#xd0;" u2="&#x166;" k="8" />
<hkern u1="&#xd0;" g2="parenright.case" k="29" />
<hkern u1="&#xd0;" g2="braceright.case" k="31" />
<hkern u1="&#xd0;" g2="bracketright.case" k="31" />
<hkern u1="&#xd0;" u2="&#x29;" k="27" />
<hkern u1="&#xd0;" g2="seven.plf" k="12" />
<hkern u1="&#xd0;" u2="&#xc6;" k="12" />
<hkern u1="&#xd0;" u2="&#x7d;" k="25" />
<hkern u1="&#xd0;" u2="]" k="25" />
<hkern u1="&#xd0;" u2="X" k="26" />
<hkern u1="&#xd0;" u2="Y" k="30" />
<hkern u1="&#xd0;" u2="Z" k="8" />
<hkern u1="&#xd0;" u2="W" k="15" />
<hkern u1="&#xd0;" u2="V" k="21" />
<hkern u1="&#xd0;" u2="J" k="17" />
<hkern u1="&#xd0;" u2="T" k="17" />
<hkern u1="&#xd0;" u2="A" k="17" />
<hkern u1="&#xd0;" u2="x" k="13" />
<hkern u1="&#xd0;" u2="z" k="5" />
<hkern u1="&#x142;" u2="&#xb7;" k="57" />
<hkern u1="&#x141;" u2="&#x166;" k="63" />
<hkern u1="&#xde;" u2="&#x104;" k="21" />
<hkern u1="&#xde;" u2="&#x1fa;" k="21" />
<hkern u1="&#xde;" u2="&#x21a;" k="25" />
<hkern u1="&#xde;" u2="&#x162;" k="25" />
<hkern u1="&#xde;" u2="&#x1fc;" k="18" />
<hkern u1="&#xde;" u2="&#x1ef2;" k="36" />
<hkern u1="&#xde;" u2="&#x1e84;" k="15" />
<hkern u1="&#xde;" u2="&#x1e82;" k="15" />
<hkern u1="&#xde;" u2="&#x1e80;" k="15" />
<hkern u1="&#xde;" u2="&#x17b;" k="16" />
<hkern u1="&#xde;" u2="&#x179;" k="16" />
<hkern u1="&#xde;" u2="&#x176;" k="36" />
<hkern u1="&#xde;" u2="&#x174;" k="15" />
<hkern u1="&#xde;" u2="&#x164;" k="25" />
<hkern u1="&#xde;" u2="&#x134;" k="34" />
<hkern u1="&#xde;" u2="&#x102;" k="21" />
<hkern u1="&#xde;" u2="&#x100;" k="21" />
<hkern u1="&#xde;" u2="&#xc3;" k="21" />
<hkern u1="&#xde;" u2="&#xc6;" k="18" />
<hkern u1="&#xde;" u2="&#xc5;" k="21" />
<hkern u1="&#xde;" u2="&#x17d;" k="16" />
<hkern u1="&#xde;" u2="&#x201e;" k="26" />
<hkern u1="&#xde;" u2="&#x201a;" k="26" />
<hkern u1="&#xde;" u2="&#xc1;" k="21" />
<hkern u1="&#xde;" u2="&#xc2;" k="21" />
<hkern u1="&#xde;" u2="&#x178;" k="36" />
<hkern u1="&#xde;" u2="&#xc0;" k="21" />
<hkern u1="&#xde;" u2="&#xc4;" k="21" />
<hkern u1="&#xde;" u2="&#xdd;" k="36" />
<hkern u1="&#xde;" u2="&#x2026;" k="26" />
<hkern u1="&#xde;" u2="Y" k="36" />
<hkern u1="&#xde;" u2="Z" k="16" />
<hkern u1="&#xde;" u2="W" k="15" />
<hkern u1="&#xde;" u2="J" k="34" />
<hkern u1="&#xde;" u2="T" k="25" />
<hkern u1="&#xde;" u2="A" k="21" />
<hkern u1="&#xde;" u2="&#x2c;" k="26" />
<hkern u1="&#xde;" u2="&#x2e;" k="26" />
<hkern u1="&#xde;" u2="&#x166;" k="11" />
<hkern u1="&#xde;" g2="parenright.case" k="31" />
<hkern u1="&#xde;" g2="braceright.case" k="31" />
<hkern u1="&#xde;" g2="bracketright.case" k="31" />
<hkern u1="&#xde;" u2="&#x29;" k="29" />
<hkern u1="&#xde;" g2="seven.plf" k="20" />
<hkern u1="&#xde;" u2="&#x2122;" k="11" />
<hkern u1="&#xde;" u2="&#x7d;" k="28" />
<hkern u1="&#xde;" u2="\" k="12" />
<hkern u1="&#xde;" u2="]" k="28" />
<hkern u1="&#xde;" u2="&#x2f;" k="16" />
<hkern u1="&#xde;" u2="X" k="36" />
<hkern u1="&#xde;" u2="V" k="22" />
<hkern u1="&#xde;" u2="x" k="6" />
<hkern u1="&#xf0;" u2="&#x104;" k="9" />
<hkern u1="&#xf0;" u2="&#x172;" k="7" />
<hkern u1="&#xf0;" u2="&#x12e;" k="7" />
<hkern u1="&#xf0;" u2="&#x118;" k="7" />
<hkern u1="&#xf0;" u2="&#x1fa;" k="9" />
<hkern u1="&#xf0;" u2="&#x166;" k="14" />
<hkern u1="&#xf0;" u2="&#x167;" k="8" />
<hkern u1="&#xf0;" u2="&#x126;" k="7" />
<hkern u1="&#xf0;" u2="&#x21a;" k="14" />
<hkern u1="&#xf0;" u2="&#x21b;" k="8" />
<hkern u1="&#xf0;" u2="&#x162;" k="14" />
<hkern u1="&#xf0;" u2="&#x163;" k="8" />
<hkern u1="&#xf0;" u2="&#x156;" k="7" />
<hkern u1="&#xf0;" u2="&#x145;" k="7" />
<hkern u1="&#xf0;" u2="&#x13b;" k="7" />
<hkern u1="&#xf0;" u2="&#x136;" k="7" />
<hkern u1="&#xf0;" u2="&#x13d;" k="7" />
<hkern u1="&#xf0;" u2="&#x110;" k="7" />
<hkern u1="&#xf0;" u2="&#x165;" k="8" />
<hkern u1="&#xf0;" u2="&#x14a;" k="7" />
<hkern u1="&#xf0;" u2="&#x1ef2;" k="31" />
<hkern u1="&#xf0;" u2="&#x1e84;" k="16" />
<hkern u1="&#xf0;" u2="&#x1e82;" k="16" />
<hkern u1="&#xf0;" u2="&#x1e80;" k="16" />
<hkern u1="&#xf0;" u2="&#x1ef3;" k="15" />
<hkern u1="&#xf0;" u2="&#x1e85;" k="9" />
<hkern u1="&#xf0;" u2="&#x1e83;" k="9" />
<hkern u1="&#xf0;" u2="&#x1e81;" k="9" />
<hkern u1="&#xf0;" u2="&#x17c;" k="6" />
<hkern u1="&#xf0;" u2="&#x17b;" k="12" />
<hkern u1="&#xf0;" u2="&#x17a;" k="6" />
<hkern u1="&#xf0;" u2="&#x179;" k="12" />
<hkern u1="&#xf0;" u2="&#x177;" k="15" />
<hkern u1="&#xf0;" u2="&#x176;" k="31" />
<hkern u1="&#xf0;" u2="&#x175;" k="9" />
<hkern u1="&#xf0;" u2="&#x174;" k="16" />
<hkern u1="&#xf0;" u2="&#x170;" k="7" />
<hkern u1="&#xf0;" u2="&#x16e;" k="7" />
<hkern u1="&#xf0;" u2="&#x168;" k="7" />
<hkern u1="&#xf0;" u2="&#x164;" k="14" />
<hkern u1="&#xf0;" u2="&#x158;" k="7" />
<hkern u1="&#xf0;" u2="&#x154;" k="7" />
<hkern u1="&#xf0;" u2="&#x147;" k="7" />
<hkern u1="&#xf0;" u2="&#x143;" k="7" />
<hkern u1="&#xf0;" u2="&#x139;" k="7" />
<hkern u1="&#xf0;" u2="&#x134;" k="5" />
<hkern u1="&#xf0;" u2="&#x132;" k="7" />
<hkern u1="&#xf0;" u2="&#x130;" k="7" />
<hkern u1="&#xf0;" u2="&#x128;" k="7" />
<hkern u1="&#xf0;" u2="&#x124;" k="7" />
<hkern u1="&#xf0;" u2="&#x11a;" k="7" />
<hkern u1="&#xf0;" u2="&#x16c;" k="7" />
<hkern u1="&#xf0;" u2="&#x12c;" k="7" />
<hkern u1="&#xf0;" u2="&#x114;" k="7" />
<hkern u1="&#xf0;" u2="&#x16a;" k="7" />
<hkern u1="&#xf0;" u2="&#x12a;" k="7" />
<hkern u1="&#xf0;" u2="&#x112;" k="7" />
<hkern u1="&#xf0;" u2="&#x10e;" k="7" />
<hkern u1="&#xf0;" u2="&#x102;" k="9" />
<hkern u1="&#xf0;" u2="&#x100;" k="9" />
<hkern u1="&#xf0;" u2="&#x116;" k="7" />
<hkern u1="&#xf0;" u2="&#xc3;" k="9" />
<hkern u1="&#xf0;" u2="&#xd1;" k="7" />
<hkern u1="&#xf0;" u2="f" k="8" />
<hkern u1="&#xf0;" u2="&#xde;" k="7" />
<hkern u1="&#xf0;" u2="&#x141;" k="7" />
<hkern u1="&#xf0;" u2="&#xd0;" k="7" />
<hkern u1="&#xf0;" u2="&#xc5;" k="9" />
<hkern u1="&#xf0;" u2="&#x17e;" k="6" />
<hkern u1="&#xf0;" u2="&#x17d;" k="12" />
<hkern u1="&#xf0;" u2="&#xd9;" k="7" />
<hkern u1="&#xf0;" u2="&#xdb;" k="7" />
<hkern u1="&#xf0;" u2="&#xda;" k="7" />
<hkern u1="&#xf0;" u2="&#xcc;" k="7" />
<hkern u1="&#xf0;" u2="&#xcf;" k="7" />
<hkern u1="&#xf0;" u2="&#xce;" k="7" />
<hkern u1="&#xf0;" u2="&#xcd;" k="7" />
<hkern u1="&#xf0;" u2="&#xc8;" k="7" />
<hkern u1="&#xf0;" u2="&#xcb;" k="7" />
<hkern u1="&#xf0;" u2="&#xc1;" k="9" />
<hkern u1="&#xf0;" u2="&#xca;" k="7" />
<hkern u1="&#xf0;" u2="&#xc2;" k="9" />
<hkern u1="&#xf0;" u2="&#x178;" k="31" />
<hkern u1="&#xf0;" u2="&#xff;" k="15" />
<hkern u1="&#xf0;" u2="&#xc0;" k="9" />
<hkern u1="&#xf0;" u2="&#xdc;" k="7" />
<hkern u1="&#xf0;" u2="&#xc9;" k="7" />
<hkern u1="&#xf0;" u2="&#xc4;" k="9" />
<hkern u1="&#xf0;" u2="&#xfd;" k="15" />
<hkern u1="&#xf0;" u2="&#xdd;" k="31" />
<hkern u1="&#xf0;" g2="fl" k="8" />
<hkern u1="&#xf0;" g2="fi" k="8" />
<hkern u1="&#xf0;" u2="U" k="7" />
<hkern u1="&#xf0;" u2="Y" k="31" />
<hkern u1="&#xf0;" u2="Z" k="12" />
<hkern u1="&#xf0;" u2="W" k="16" />
<hkern u1="&#xf0;" u2="J" k="5" />
<hkern u1="&#xf0;" u2="D" k="7" />
<hkern u1="&#xf0;" u2="B" k="7" />
<hkern u1="&#xf0;" u2="H" k="7" />
<hkern u1="&#xf0;" u2="P" k="7" />
<hkern u1="&#xf0;" u2="L" k="7" />
<hkern u1="&#xf0;" u2="T" k="14" />
<hkern u1="&#xf0;" u2="R" k="7" />
<hkern u1="&#xf0;" u2="N" k="7" />
<hkern u1="&#xf0;" u2="M" k="7" />
<hkern u1="&#xf0;" u2="K" k="7" />
<hkern u1="&#xf0;" u2="I" k="7" />
<hkern u1="&#xf0;" u2="F" k="7" />
<hkern u1="&#xf0;" u2="E" k="7" />
<hkern u1="&#xf0;" u2="A" k="9" />
<hkern u1="&#xf0;" u2="y" k="15" />
<hkern u1="&#xf0;" u2="z" k="6" />
<hkern u1="&#xf0;" u2="w" k="9" />
<hkern u1="&#xf0;" u2="t" k="8" />
<hkern u1="&#xf0;" u2="&#x29;" k="23" />
<hkern u1="&#xf0;" u2="&#x7d;" k="18" />
<hkern u1="&#xf0;" u2="]" k="18" />
<hkern u1="&#xf0;" u2="X" k="19" />
<hkern u1="&#xf0;" u2="V" k="22" />
<hkern u1="&#xf0;" u2="x" k="19" />
<hkern u1="&#xf0;" u2="v" k="13" />
<hkern u1="&#xe6;" u2="&#x29;" k="33" />
<hkern u1="&#xe6;" u2="f" k="4" />
<hkern u1="&#xe6;" u2="&#x2122;" k="21" />
<hkern u1="&#xe6;" u2="&#x7d;" k="33" />
<hkern u1="&#xe6;" u2="&#x2018;" k="8" />
<hkern u1="&#xe6;" u2="&#x2019;" k="11" />
<hkern u1="&#xe6;" u2="\" k="31" />
<hkern u1="&#xe6;" u2="]" k="33" />
<hkern u1="&#xe6;" u2="X" k="11" />
<hkern u1="&#xe6;" u2="Y" k="84" />
<hkern u1="&#xe6;" u2="Z" k="7" />
<hkern u1="&#xe6;" u2="W" k="37" />
<hkern u1="&#xe6;" u2="V" k="44" />
<hkern u1="&#xe6;" u2="T" k="75" />
<hkern u1="&#xe6;" u2="A" k="8" />
<hkern u1="&#xe6;" u2="x" k="15" />
<hkern u1="&#xe6;" u2="y" k="11" />
<hkern u1="&#xe6;" u2="w" k="6" />
<hkern u1="&#xe6;" u2="v" k="9" />
<hkern u1="&#x153;" u2="&#x29;" k="33" />
<hkern u1="&#x153;" u2="f" k="4" />
<hkern u1="&#x153;" u2="&#x2122;" k="21" />
<hkern u1="&#x153;" u2="&#x7d;" k="33" />
<hkern u1="&#x153;" u2="&#x2018;" k="8" />
<hkern u1="&#x153;" u2="&#x2019;" k="11" />
<hkern u1="&#x153;" u2="\" k="31" />
<hkern u1="&#x153;" u2="]" k="33" />
<hkern u1="&#x153;" u2="X" k="11" />
<hkern u1="&#x153;" u2="Y" k="84" />
<hkern u1="&#x153;" u2="Z" k="7" />
<hkern u1="&#x153;" u2="W" k="37" />
<hkern u1="&#x153;" u2="V" k="44" />
<hkern u1="&#x153;" u2="T" k="75" />
<hkern u1="&#x153;" u2="A" k="8" />
<hkern u1="&#x153;" u2="x" k="15" />
<hkern u1="&#x153;" u2="y" k="11" />
<hkern u1="&#x153;" u2="w" k="6" />
<hkern u1="&#x153;" u2="v" k="9" />
<hkern u1="&#x22;" u2="d" k="12" />
<hkern u1="&#x22;" g2="four.plf" k="31" />
<hkern u1="&#x22;" u2="&#x2039;" k="14" />
<hkern u1="&#x22;" u2="&#xc6;" k="44" />
<hkern u1="&#x22;" u2="&#xf0;" k="14" />
<hkern u1="&#x22;" u2="&#x2f;" k="44" />
<hkern u1="&#x22;" u2="J" k="50" />
<hkern u1="&#x22;" u2="A" k="38" />
<hkern u1="&#x22;" u2="&#x2e;" k="104" />
<hkern u1="&#x22;" u2="o" k="9" />
<hkern u1="&#x22;" u2="&#x20;" k="10" />
<hkern u1="&#x27;" u2="d" k="12" />
<hkern u1="&#x27;" g2="four.plf" k="31" />
<hkern u1="&#x27;" u2="&#x2039;" k="14" />
<hkern u1="&#x27;" u2="&#xc6;" k="44" />
<hkern u1="&#x27;" u2="&#xf0;" k="14" />
<hkern u1="&#x27;" u2="&#x2f;" k="44" />
<hkern u1="&#x27;" u2="J" k="50" />
<hkern u1="&#x27;" u2="A" k="38" />
<hkern u1="&#x27;" u2="&#x2e;" k="104" />
<hkern u1="&#x27;" u2="o" k="9" />
<hkern u1="&#x27;" u2="&#x20;" k="10" />
<hkern u1="&#xb0;" g2="four.plf" k="37" />
<hkern u1="&#xdf;" u2="&#x104;" k="8" />
<hkern u1="&#xdf;" u2="&#x1fa;" k="8" />
<hkern u1="&#xdf;" u2="&#x166;" k="6" />
<hkern u1="&#xdf;" u2="&#x21a;" k="6" />
<hkern u1="&#xdf;" u2="&#x162;" k="6" />
<hkern u1="&#xdf;" u2="&#x1ef2;" k="17" />
<hkern u1="&#xdf;" u2="&#x1e84;" k="8" />
<hkern u1="&#xdf;" u2="&#x1e82;" k="8" />
<hkern u1="&#xdf;" u2="&#x1e80;" k="8" />
<hkern u1="&#xdf;" u2="&#x1ef3;" k="7" />
<hkern u1="&#xdf;" u2="&#x1e85;" k="4" />
<hkern u1="&#xdf;" u2="&#x1e83;" k="4" />
<hkern u1="&#xdf;" u2="&#x1e81;" k="4" />
<hkern u1="&#xdf;" u2="&#x177;" k="7" />
<hkern u1="&#xdf;" u2="&#x176;" k="17" />
<hkern u1="&#xdf;" u2="&#x175;" k="4" />
<hkern u1="&#xdf;" u2="&#x174;" k="8" />
<hkern u1="&#xdf;" u2="&#x164;" k="6" />
<hkern u1="&#xdf;" u2="&#x102;" k="8" />
<hkern u1="&#xdf;" u2="&#x100;" k="8" />
<hkern u1="&#xdf;" u2="&#xc3;" k="8" />
<hkern u1="&#xdf;" u2="&#xc5;" k="8" />
<hkern u1="&#xdf;" u2="&#xc1;" k="8" />
<hkern u1="&#xdf;" u2="&#xc2;" k="8" />
<hkern u1="&#xdf;" u2="&#x178;" k="17" />
<hkern u1="&#xdf;" u2="&#xff;" k="7" />
<hkern u1="&#xdf;" u2="&#xc0;" k="8" />
<hkern u1="&#xdf;" u2="&#xc4;" k="8" />
<hkern u1="&#xdf;" u2="&#xfd;" k="7" />
<hkern u1="&#xdf;" u2="&#xdd;" k="17" />
<hkern u1="&#xdf;" u2="Y" k="17" />
<hkern u1="&#xdf;" u2="W" k="8" />
<hkern u1="&#xdf;" u2="T" k="6" />
<hkern u1="&#xdf;" u2="A" k="8" />
<hkern u1="&#xdf;" u2="y" k="7" />
<hkern u1="&#xdf;" u2="w" k="4" />
<hkern u1="&#xdf;" u2="&#x29;" k="18" />
<hkern u1="&#xdf;" u2="&#x7d;" k="10" />
<hkern u1="&#xdf;" u2="]" k="10" />
<hkern u1="&#xdf;" u2="X" k="12" />
<hkern u1="&#xdf;" u2="V" k="13" />
<hkern u1="&#xdf;" u2="x" k="4" />
<hkern u1="&#xdf;" u2="v" k="5" />
<hkern u1="&#x2a;" u2="&#x104;" k="34" />
<hkern u1="&#x2a;" u2="&#x1fa;" k="34" />
<hkern u1="&#x2a;" u2="&#x1fc;" k="37" />
<hkern u1="&#x2a;" u2="&#x134;" k="49" />
<hkern u1="&#x2a;" u2="&#x102;" k="34" />
<hkern u1="&#x2a;" u2="&#x100;" k="34" />
<hkern u1="&#x2a;" u2="&#xc3;" k="34" />
<hkern u1="&#x2a;" u2="&#xc6;" k="37" />
<hkern u1="&#x2a;" u2="&#xc5;" k="34" />
<hkern u1="&#x2a;" u2="&#xc1;" k="34" />
<hkern u1="&#x2a;" u2="&#xc2;" k="34" />
<hkern u1="&#x2a;" u2="&#xc0;" k="34" />
<hkern u1="&#x2a;" u2="&#xc4;" k="34" />
<hkern u1="&#x2a;" u2="J" k="49" />
<hkern u1="&#x2a;" u2="A" k="34" />
<hkern u1="&#x2a;" u2="&#xf0;" k="10" />
<hkern u1="&#x2039;" u2="&#x166;" k="26" />
<hkern u1="&#x2039;" u2="Y" k="40" />
<hkern u1="&#x2039;" u2="W" k="14" />
<hkern u1="&#x2039;" u2="V" k="22" />
<hkern u1="&#x2039;" u2="T" k="59" />
<hkern u1="&#x203a;" u2="&#x166;" k="33" />
<hkern u1="&#x203a;" u2="f" k="16" />
<hkern u1="&#x203a;" u2="&#x27;" k="14" />
<hkern u1="&#x203a;" u2="&#x2019;" k="18" />
<hkern u1="&#x203a;" u2="X" k="19" />
<hkern u1="&#x203a;" u2="Y" k="64" />
<hkern u1="&#x203a;" u2="Z" k="11" />
<hkern u1="&#x203a;" u2="W" k="32" />
<hkern u1="&#x203a;" u2="V" k="42" />
<hkern u1="&#x203a;" u2="J" k="15" />
<hkern u1="&#x203a;" u2="T" k="66" />
<hkern u1="&#x203a;" u2="A" k="14" />
<hkern u1="&#x203a;" u2="x" k="27" />
<hkern u1="&#x203a;" u2="y" k="21" />
<hkern u1="&#x203a;" u2="z" k="20" />
<hkern u1="&#x203a;" u2="w" k="13" />
<hkern u1="&#x203a;" u2="v" k="18" />
<hkern u1="f" u2="d" k="7" />
<hkern u1="f" u2="&#x104;" k="27" />
<hkern u1="f" u2="&#x119;" k="8" />
<hkern u1="f" u2="&#x1fa;" k="27" />
<hkern u1="f" u2="&#x111;" k="7" />
<hkern u1="f" u2="&#x1ff;" k="8" />
<hkern u1="f" u2="&#x1fc;" k="22" />
<hkern u1="f" u2="&#x151;" k="8" />
<hkern u1="f" u2="&#x134;" k="28" />
<hkern u1="f" u2="&#x123;" k="7" />
<hkern u1="f" u2="&#x121;" k="7" />
<hkern u1="f" u2="&#x11d;" k="7" />
<hkern u1="f" u2="&#x11b;" k="8" />
<hkern u1="f" u2="&#x117;" k="8" />
<hkern u1="f" u2="&#x14f;" k="8" />
<hkern u1="f" u2="&#x11f;" k="7" />
<hkern u1="f" u2="&#x115;" k="8" />
<hkern u1="f" u2="&#x14d;" k="8" />
<hkern u1="f" u2="&#x113;" k="8" />
<hkern u1="f" u2="&#x10f;" k="7" />
<hkern u1="f" u2="&#x10d;" k="8" />
<hkern u1="f" u2="&#x10b;" k="8" />
<hkern u1="f" u2="&#x109;" k="8" />
<hkern u1="f" u2="&#x107;" k="8" />
<hkern u1="f" u2="&#x102;" k="27" />
<hkern u1="f" u2="&#x100;" k="27" />
<hkern u1="f" u2="&#xad;" k="13" />
<hkern u1="f" u2="&#xc3;" k="27" />
<hkern u1="f" u2="&#xf5;" k="8" />
<hkern u1="f" u2="&#x2039;" k="19" />
<hkern u1="f" u2="&#xc6;" k="22" />
<hkern u1="f" u2="&#x153;" k="8" />
<hkern u1="f" u2="&#xf8;" k="8" />
<hkern u1="f" u2="&#xc5;" k="27" />
<hkern u1="f" u2="g" k="7" />
<hkern u1="f" u2="&#xab;" k="19" />
<hkern u1="f" u2="&#x201e;" k="17" />
<hkern u1="f" u2="&#x201a;" k="17" />
<hkern u1="f" u2="&#xc1;" k="27" />
<hkern u1="f" u2="&#xc2;" k="27" />
<hkern u1="f" u2="&#xc0;" k="27" />
<hkern u1="f" u2="&#xf6;" k="8" />
<hkern u1="f" u2="&#xf4;" k="8" />
<hkern u1="f" u2="&#xf2;" k="8" />
<hkern u1="f" u2="&#xf3;" k="8" />
<hkern u1="f" u2="&#xeb;" k="8" />
<hkern u1="f" u2="&#xea;" k="8" />
<hkern u1="f" u2="&#xe8;" k="8" />
<hkern u1="f" u2="&#xe9;" k="8" />
<hkern u1="f" u2="&#xc4;" k="27" />
<hkern u1="f" u2="&#x2014;" k="13" />
<hkern u1="f" u2="&#x2013;" k="13" />
<hkern u1="f" u2="&#x2026;" k="17" />
<hkern u1="f" u2="&#xe7;" k="8" />
<hkern u1="f" u2="&#x2d;" k="13" />
<hkern u1="f" u2="J" k="28" />
<hkern u1="f" u2="A" k="27" />
<hkern u1="f" u2="&#x2c;" k="17" />
<hkern u1="f" u2="&#x2e;" k="17" />
<hkern u1="f" u2="c" k="8" />
<hkern u1="f" u2="e" k="8" />
<hkern u1="f" u2="o" k="8" />
<hkern u1="f" u2="q" k="7" />
<hkern u1="f" u2="&#x129;" k="-27" />
<hkern u1="f" u2="&#x12d;" k="-8" />
<hkern u1="f" u2="&#x12b;" k="-18" />
<hkern u1="f" u2="&#xf0;" k="16" />
<hkern u1="f" u2="&#xef;" k="-6" />
<hkern u1="f" u2="&#x2f;" k="13" />
<hkern u1="f" u2="&#x20;" k="18" />
<hkern u1="n" u2="&#x29;" k="28" />
<hkern u1="n" u2="&#x2122;" k="21" />
<hkern u1="n" u2="&#x7d;" k="33" />
<hkern u1="n" u2="&#x2019;" k="8" />
<hkern u1="n" u2="\" k="30" />
<hkern u1="n" u2="]" k="32" />
<hkern u1="n" u2="U" k="6" />
<hkern u1="n" u2="Y" k="63" />
<hkern u1="n" u2="Z" k="5" />
<hkern u1="n" u2="W" k="31" />
<hkern u1="n" u2="V" k="43" />
<hkern u1="n" u2="T" k="81" />
<hkern u1="n" u2="y" k="6" />
<hkern u1="n" u2="v" k="6" />
<hkern u1="&#xbb;" u2="&#x166;" k="33" />
<hkern u1="&#xbb;" u2="f" k="16" />
<hkern u1="&#xbb;" u2="&#x27;" k="14" />
<hkern u1="&#xbb;" u2="&#x2019;" k="18" />
<hkern u1="&#xbb;" u2="X" k="19" />
<hkern u1="&#xbb;" u2="Y" k="64" />
<hkern u1="&#xbb;" u2="Z" k="11" />
<hkern u1="&#xbb;" u2="W" k="32" />
<hkern u1="&#xbb;" u2="V" k="42" />
<hkern u1="&#xbb;" u2="J" k="15" />
<hkern u1="&#xbb;" u2="T" k="66" />
<hkern u1="&#xbb;" u2="A" k="14" />
<hkern u1="&#xbb;" u2="x" k="27" />
<hkern u1="&#xbb;" u2="y" k="21" />
<hkern u1="&#xbb;" u2="z" k="20" />
<hkern u1="&#xbb;" u2="w" k="13" />
<hkern u1="&#xbb;" u2="v" k="18" />
<hkern u1="i" u2="U" k="5" />
<hkern u1="i" u2="Z" k="5" />
<hkern u1="&#xd1;" u2="d" k="6" />
<hkern u1="&#xd1;" u2="&#xf0;" k="7" />
<hkern u1="&#xd1;" u2="o" k="6" />
<hkern u1="&#xf1;" u2="&#x29;" k="28" />
<hkern u1="&#xf1;" u2="&#x2122;" k="21" />
<hkern u1="&#xf1;" u2="&#x7d;" k="33" />
<hkern u1="&#xf1;" u2="&#x2019;" k="8" />
<hkern u1="&#xf1;" u2="\" k="30" />
<hkern u1="&#xf1;" u2="]" k="32" />
<hkern u1="&#xf1;" u2="U" k="6" />
<hkern u1="&#xf1;" u2="Y" k="63" />
<hkern u1="&#xf1;" u2="Z" k="5" />
<hkern u1="&#xf1;" u2="W" k="31" />
<hkern u1="&#xf1;" u2="V" k="43" />
<hkern u1="&#xf1;" u2="T" k="81" />
<hkern u1="&#xf1;" u2="y" k="6" />
<hkern u1="&#xf1;" u2="v" k="6" />
<hkern u1="&#xc3;" u2="&#xba;" k="30" />
<hkern u1="&#xc3;" u2="&#xaa;" k="28" />
<hkern u1="&#xc3;" u2="d" k="10" />
<hkern u1="&#xc3;" u2="&#x166;" k="45" />
<hkern u1="&#xc3;" g2="hyphen.case" k="10" />
<hkern u1="&#xc3;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc3;" u2="&#x29;" k="16" />
<hkern u1="&#xc3;" g2="nine.plf" k="10" />
<hkern u1="&#xc3;" g2="seven.plf" k="14" />
<hkern u1="&#xc3;" g2="six.plf" k="11" />
<hkern u1="&#xc3;" g2="one.plf" k="44" />
<hkern u1="&#xc3;" g2="zero.plf" k="12" />
<hkern u1="&#xc3;" u2="f" k="17" />
<hkern u1="&#xc3;" u2="&#x2039;" k="13" />
<hkern u1="&#xc3;" u2="&#x2a;" k="34" />
<hkern u1="&#xc3;" u2="&#x2122;" k="43" />
<hkern u1="&#xc3;" u2="&#x27;" k="38" />
<hkern u1="&#xc3;" u2="&#xf0;" k="9" />
<hkern u1="&#xc3;" u2="&#x7d;" k="33" />
<hkern u1="&#xc3;" u2="&#xae;" k="16" />
<hkern u1="&#xc3;" u2="s" k="6" />
<hkern u1="&#xc3;" u2="&#x2018;" k="38" />
<hkern u1="&#xc3;" u2="&#x2019;" k="40" />
<hkern u1="&#xc3;" u2="&#xa9;" k="16" />
<hkern u1="&#xc3;" u2="&#x3f;" k="34" />
<hkern u1="&#xc3;" u2="\" k="50" />
<hkern u1="&#xc3;" u2="]" k="33" />
<hkern u1="&#xc3;" u2="U" k="13" />
<hkern u1="&#xc3;" u2="Y" k="57" />
<hkern u1="&#xc3;" u2="W" k="33" />
<hkern u1="&#xc3;" u2="V" k="42" />
<hkern u1="&#xc3;" u2="T" k="53" />
<hkern u1="&#xc3;" u2="S" k="10" />
<hkern u1="&#xc3;" u2="O" k="17" />
<hkern u1="&#xc3;" u2="y" k="28" />
<hkern u1="&#xc3;" u2="w" k="19" />
<hkern u1="&#xc3;" u2="v" k="26" />
<hkern u1="&#xc3;" u2="u" k="6" />
<hkern u1="&#xc3;" u2="o" k="10" />
<hkern u1="&#xc3;" u2="a" k="5" />
<hkern u1="&#xc3;" u2="&#x20;" k="26" />
<hkern g1="zero.plf" u2="&#x29;" k="24" />
<hkern g1="zero.plf" u2="&#x7d;" k="23" />
<hkern g1="zero.plf" u2="]" k="23" />
<hkern g1="zero.plf" u2="Y" k="20" />
<hkern g1="zero.plf" u2="W" k="12" />
<hkern g1="zero.plf" u2="V" k="16" />
<hkern g1="zero.plf" u2="A" k="12" />
<hkern g1="two.plf" u2="&#x1ef2;" k="14" />
<hkern g1="two.plf" u2="&#x1e84;" k="10" />
<hkern g1="two.plf" u2="&#x1e82;" k="10" />
<hkern g1="two.plf" u2="&#x1e80;" k="10" />
<hkern g1="two.plf" u2="&#x176;" k="14" />
<hkern g1="two.plf" u2="&#x174;" k="10" />
<hkern g1="two.plf" u2="&#x178;" k="14" />
<hkern g1="two.plf" u2="&#xdd;" k="14" />
<hkern g1="two.plf" u2="Y" k="14" />
<hkern g1="two.plf" u2="W" k="10" />
<hkern g1="two.plf" u2="&#x7d;" k="10" />
<hkern g1="two.plf" u2="]" k="10" />
<hkern g1="two.plf" u2="V" k="14" />
<hkern g1="three.plf" u2="&#x104;" k="10" />
<hkern g1="three.plf" u2="&#x1fa;" k="10" />
<hkern g1="three.plf" u2="&#x1ef2;" k="16" />
<hkern g1="three.plf" u2="&#x1e84;" k="11" />
<hkern g1="three.plf" u2="&#x1e82;" k="11" />
<hkern g1="three.plf" u2="&#x1e80;" k="11" />
<hkern g1="three.plf" u2="&#x176;" k="16" />
<hkern g1="three.plf" u2="&#x174;" k="11" />
<hkern g1="three.plf" u2="&#x102;" k="10" />
<hkern g1="three.plf" u2="&#x100;" k="10" />
<hkern g1="three.plf" u2="&#xc3;" k="10" />
<hkern g1="three.plf" u2="&#xc5;" k="10" />
<hkern g1="three.plf" u2="&#xc1;" k="10" />
<hkern g1="three.plf" u2="&#xc2;" k="10" />
<hkern g1="three.plf" u2="&#x178;" k="16" />
<hkern g1="three.plf" u2="&#xc0;" k="10" />
<hkern g1="three.plf" u2="&#xc4;" k="10" />
<hkern g1="three.plf" u2="&#xdd;" k="16" />
<hkern g1="three.plf" u2="Y" k="16" />
<hkern g1="three.plf" u2="W" k="11" />
<hkern g1="three.plf" u2="A" k="10" />
<hkern g1="three.plf" u2="&#x29;" k="20" />
<hkern g1="three.plf" u2="&#x7d;" k="18" />
<hkern g1="three.plf" u2="]" k="17" />
<hkern g1="three.plf" u2="V" k="15" />
<hkern g1="four.plf" u2="&#x166;" k="10" />
<hkern g1="four.plf" u2="&#x21a;" k="10" />
<hkern g1="four.plf" u2="&#x162;" k="10" />
<hkern g1="four.plf" u2="&#x1ef2;" k="26" />
<hkern g1="four.plf" u2="&#x1e84;" k="19" />
<hkern g1="four.plf" u2="&#x1e82;" k="19" />
<hkern g1="four.plf" u2="&#x1e80;" k="19" />
<hkern g1="four.plf" u2="&#x176;" k="26" />
<hkern g1="four.plf" u2="&#x174;" k="19" />
<hkern g1="four.plf" u2="&#x164;" k="10" />
<hkern g1="four.plf" u2="&#x27;" k="15" />
<hkern g1="four.plf" u2="&#x22;" k="15" />
<hkern g1="four.plf" u2="&#x178;" k="26" />
<hkern g1="four.plf" u2="&#xdd;" k="26" />
<hkern g1="four.plf" u2="Y" k="26" />
<hkern g1="four.plf" u2="W" k="19" />
<hkern g1="four.plf" u2="T" k="10" />
<hkern g1="four.plf" u2="&#x29;" k="23" />
<hkern g1="four.plf" g2="seven.plf" k="14" />
<hkern g1="four.plf" g2="one.plf" k="16" />
<hkern g1="four.plf" u2="&#xb0;" k="15" />
<hkern g1="four.plf" u2="&#x7d;" k="22" />
<hkern g1="four.plf" u2="\" k="13" />
<hkern g1="four.plf" u2="]" k="21" />
<hkern g1="four.plf" u2="V" k="25" />
<hkern g1="five.plf" u2="&#x1ef2;" k="10" />
<hkern g1="five.plf" u2="&#x1e84;" k="10" />
<hkern g1="five.plf" u2="&#x1e82;" k="10" />
<hkern g1="five.plf" u2="&#x1e80;" k="10" />
<hkern g1="five.plf" u2="&#x176;" k="10" />
<hkern g1="five.plf" u2="&#x174;" k="10" />
<hkern g1="five.plf" u2="&#x178;" k="10" />
<hkern g1="five.plf" u2="&#xdd;" k="10" />
<hkern g1="five.plf" u2="Y" k="10" />
<hkern g1="five.plf" u2="W" k="10" />
<hkern g1="five.plf" u2="V" k="13" />
<hkern g1="six.plf" u2="&#x104;" k="10" />
<hkern g1="six.plf" u2="&#x1fa;" k="10" />
<hkern g1="six.plf" u2="&#x1ef2;" k="16" />
<hkern g1="six.plf" u2="&#x1e84;" k="10" />
<hkern g1="six.plf" u2="&#x1e82;" k="10" />
<hkern g1="six.plf" u2="&#x1e80;" k="10" />
<hkern g1="six.plf" u2="&#x176;" k="16" />
<hkern g1="six.plf" u2="&#x174;" k="10" />
<hkern g1="six.plf" u2="&#x102;" k="10" />
<hkern g1="six.plf" u2="&#x100;" k="10" />
<hkern g1="six.plf" u2="&#xc3;" k="10" />
<hkern g1="six.plf" u2="&#xc5;" k="10" />
<hkern g1="six.plf" u2="&#xc1;" k="10" />
<hkern g1="six.plf" u2="&#xc2;" k="10" />
<hkern g1="six.plf" u2="&#x178;" k="16" />
<hkern g1="six.plf" u2="&#xc0;" k="10" />
<hkern g1="six.plf" u2="&#xc4;" k="10" />
<hkern g1="six.plf" u2="&#xdd;" k="16" />
<hkern g1="six.plf" u2="Y" k="16" />
<hkern g1="six.plf" u2="W" k="10" />
<hkern g1="six.plf" u2="A" k="10" />
<hkern g1="six.plf" u2="&#x29;" k="20" />
<hkern g1="six.plf" u2="&#x7d;" k="16" />
<hkern g1="six.plf" u2="]" k="16" />
<hkern g1="six.plf" u2="V" k="14" />
<hkern g1="seven.plf" u2="&#x104;" k="44" />
<hkern g1="seven.plf" u2="&#x1fa;" k="44" />
<hkern g1="seven.plf" u2="&#x1fc;" k="54" />
<hkern g1="seven.plf" u2="&#x134;" k="51" />
<hkern g1="seven.plf" u2="&#x102;" k="44" />
<hkern g1="seven.plf" u2="&#x100;" k="44" />
<hkern g1="seven.plf" u2="&#xad;" k="21" />
<hkern g1="seven.plf" u2="&#xc3;" k="44" />
<hkern g1="seven.plf" u2="&#xc6;" k="54" />
<hkern g1="seven.plf" u2="&#xc5;" k="44" />
<hkern g1="seven.plf" u2="&#x201e;" k="59" />
<hkern g1="seven.plf" u2="&#x201a;" k="59" />
<hkern g1="seven.plf" u2="&#xc1;" k="44" />
<hkern g1="seven.plf" u2="&#xc2;" k="44" />
<hkern g1="seven.plf" u2="&#xc0;" k="44" />
<hkern g1="seven.plf" u2="&#xc4;" k="44" />
<hkern g1="seven.plf" u2="&#x2014;" k="21" />
<hkern g1="seven.plf" u2="&#x2013;" k="21" />
<hkern g1="seven.plf" u2="&#x2026;" k="59" />
<hkern g1="seven.plf" u2="&#x2d;" k="21" />
<hkern g1="seven.plf" u2="J" k="51" />
<hkern g1="seven.plf" u2="A" k="44" />
<hkern g1="seven.plf" u2="&#x2c;" k="59" />
<hkern g1="seven.plf" u2="&#x2e;" k="59" />
<hkern g1="seven.plf" g2="four.plf" k="36" />
<hkern g1="seven.plf" u2="&#xb7;" k="23" />
<hkern g1="seven.plf" u2="&#x2f;" k="54" />
<hkern g1="eight.plf" u2="&#x1ef2;" k="17" />
<hkern g1="eight.plf" u2="&#x1e84;" k="12" />
<hkern g1="eight.plf" u2="&#x1e82;" k="12" />
<hkern g1="eight.plf" u2="&#x1e80;" k="12" />
<hkern g1="eight.plf" u2="&#x176;" k="17" />
<hkern g1="eight.plf" u2="&#x174;" k="12" />
<hkern g1="eight.plf" u2="&#x178;" k="17" />
<hkern g1="eight.plf" u2="&#xdd;" k="17" />
<hkern g1="eight.plf" u2="Y" k="17" />
<hkern g1="eight.plf" u2="W" k="12" />
<hkern g1="eight.plf" u2="&#x29;" k="22" />
<hkern g1="eight.plf" u2="&#x7d;" k="20" />
<hkern g1="eight.plf" u2="]" k="20" />
<hkern g1="eight.plf" u2="V" k="16" />
<hkern g1="nine.plf" u2="&#x104;" k="13" />
<hkern g1="nine.plf" u2="&#x1fa;" k="13" />
<hkern g1="nine.plf" u2="&#x1ef2;" k="18" />
<hkern g1="nine.plf" u2="&#x1e84;" k="11" />
<hkern g1="nine.plf" u2="&#x1e82;" k="11" />
<hkern g1="nine.plf" u2="&#x1e80;" k="11" />
<hkern g1="nine.plf" u2="&#x176;" k="18" />
<hkern g1="nine.plf" u2="&#x174;" k="11" />
<hkern g1="nine.plf" u2="&#x102;" k="13" />
<hkern g1="nine.plf" u2="&#x100;" k="13" />
<hkern g1="nine.plf" u2="&#xc3;" k="13" />
<hkern g1="nine.plf" u2="&#xc5;" k="13" />
<hkern g1="nine.plf" u2="&#xc1;" k="13" />
<hkern g1="nine.plf" u2="&#xc2;" k="13" />
<hkern g1="nine.plf" u2="&#x178;" k="18" />
<hkern g1="nine.plf" u2="&#xc0;" k="13" />
<hkern g1="nine.plf" u2="&#xc4;" k="13" />
<hkern g1="nine.plf" u2="&#xdd;" k="18" />
<hkern g1="nine.plf" u2="Y" k="18" />
<hkern g1="nine.plf" u2="W" k="11" />
<hkern g1="nine.plf" u2="A" k="13" />
<hkern g1="nine.plf" u2="&#x29;" k="24" />
<hkern g1="nine.plf" u2="&#x7d;" k="22" />
<hkern g1="nine.plf" u2="]" k="22" />
<hkern g1="nine.plf" u2="V" k="16" />
<hkern u1="&#x29;" u2="&#x29;" k="20" />
<hkern u1="&#x29;" u2="&#x7d;" k="19" />
<hkern u1="&#x29;" u2="]" k="19" />
<hkern g1="seven.num" u2="&#x2044;" k="33" />
<hkern g1="at.case" u2="&#x104;" k="10" />
<hkern g1="at.case" u2="&#x1fa;" k="10" />
<hkern g1="at.case" u2="&#x134;" k="14" />
<hkern g1="at.case" u2="&#x102;" k="10" />
<hkern g1="at.case" u2="&#x100;" k="10" />
<hkern g1="at.case" u2="&#xc3;" k="10" />
<hkern g1="at.case" u2="&#xc5;" k="10" />
<hkern g1="at.case" u2="&#xc1;" k="10" />
<hkern g1="at.case" u2="&#xc2;" k="10" />
<hkern g1="at.case" u2="&#xc0;" k="10" />
<hkern g1="at.case" u2="&#xc4;" k="10" />
<hkern g1="at.case" u2="J" k="14" />
<hkern g1="at.case" u2="A" k="10" />
<hkern g1="bracketleft.case" u2="&#x172;" k="11" />
<hkern g1="bracketleft.case" u2="&#x218;" k="23" />
<hkern g1="bracketleft.case" u2="&#x15e;" k="23" />
<hkern g1="bracketleft.case" u2="&#x122;" k="30" />
<hkern g1="bracketleft.case" u2="&#x1fe;" k="30" />
<hkern g1="bracketleft.case" u2="&#x170;" k="11" />
<hkern g1="bracketleft.case" u2="&#x16e;" k="11" />
<hkern g1="bracketleft.case" u2="&#x168;" k="11" />
<hkern g1="bracketleft.case" u2="&#x15c;" k="23" />
<hkern g1="bracketleft.case" u2="&#x15a;" k="23" />
<hkern g1="bracketleft.case" u2="&#x150;" k="30" />
<hkern g1="bracketleft.case" u2="&#x11c;" k="30" />
<hkern g1="bracketleft.case" u2="&#x16c;" k="11" />
<hkern g1="bracketleft.case" u2="&#x14e;" k="30" />
<hkern g1="bracketleft.case" u2="&#x11e;" k="30" />
<hkern g1="bracketleft.case" u2="&#x16a;" k="11" />
<hkern g1="bracketleft.case" u2="&#x14c;" k="30" />
<hkern g1="bracketleft.case" u2="&#x10c;" k="30" />
<hkern g1="bracketleft.case" u2="&#x10a;" k="30" />
<hkern g1="bracketleft.case" u2="&#x108;" k="30" />
<hkern g1="bracketleft.case" u2="&#x106;" k="30" />
<hkern g1="bracketleft.case" u2="&#x120;" k="30" />
<hkern g1="bracketleft.case" u2="&#xd5;" k="30" />
<hkern g1="bracketleft.case" u2="&#xd8;" k="30" />
<hkern g1="bracketleft.case" u2="&#x152;" k="30" />
<hkern g1="bracketleft.case" u2="&#x160;" k="23" />
<hkern g1="bracketleft.case" u2="&#xd6;" k="30" />
<hkern g1="bracketleft.case" u2="&#xd2;" k="30" />
<hkern g1="bracketleft.case" u2="&#xd4;" k="30" />
<hkern g1="bracketleft.case" u2="&#xd3;" k="30" />
<hkern g1="bracketleft.case" u2="&#xc7;" k="30" />
<hkern g1="bracketleft.case" u2="&#xd9;" k="11" />
<hkern g1="bracketleft.case" u2="&#xdb;" k="11" />
<hkern g1="bracketleft.case" u2="&#xda;" k="11" />
<hkern g1="bracketleft.case" u2="&#xdc;" k="11" />
<hkern g1="bracketleft.case" u2="G" k="30" />
<hkern g1="bracketleft.case" u2="U" k="11" />
<hkern g1="bracketleft.case" u2="C" k="30" />
<hkern g1="bracketleft.case" u2="Q" k="30" />
<hkern g1="bracketleft.case" u2="S" k="23" />
<hkern g1="bracketleft.case" u2="O" k="30" />
<hkern g1="bracketleft.case" u2="&#x128;" k="-12" />
<hkern g1="bracketleft.case" g2="parenleft.case" k="15" />
<hkern g1="braceleft.case" u2="&#x172;" k="11" />
<hkern g1="braceleft.case" u2="&#x218;" k="23" />
<hkern g1="braceleft.case" u2="&#x15e;" k="23" />
<hkern g1="braceleft.case" u2="&#x122;" k="30" />
<hkern g1="braceleft.case" u2="&#x1fe;" k="30" />
<hkern g1="braceleft.case" u2="&#x170;" k="11" />
<hkern g1="braceleft.case" u2="&#x16e;" k="11" />
<hkern g1="braceleft.case" u2="&#x168;" k="11" />
<hkern g1="braceleft.case" u2="&#x15c;" k="23" />
<hkern g1="braceleft.case" u2="&#x15a;" k="23" />
<hkern g1="braceleft.case" u2="&#x150;" k="30" />
<hkern g1="braceleft.case" u2="&#x11c;" k="30" />
<hkern g1="braceleft.case" u2="&#x16c;" k="11" />
<hkern g1="braceleft.case" u2="&#x14e;" k="30" />
<hkern g1="braceleft.case" u2="&#x11e;" k="30" />
<hkern g1="braceleft.case" u2="&#x16a;" k="11" />
<hkern g1="braceleft.case" u2="&#x14c;" k="30" />
<hkern g1="braceleft.case" u2="&#x10c;" k="30" />
<hkern g1="braceleft.case" u2="&#x10a;" k="30" />
<hkern g1="braceleft.case" u2="&#x108;" k="30" />
<hkern g1="braceleft.case" u2="&#x106;" k="30" />
<hkern g1="braceleft.case" u2="&#x120;" k="30" />
<hkern g1="braceleft.case" u2="&#xd5;" k="30" />
<hkern g1="braceleft.case" u2="&#xd8;" k="30" />
<hkern g1="braceleft.case" u2="&#x152;" k="30" />
<hkern g1="braceleft.case" u2="&#x160;" k="23" />
<hkern g1="braceleft.case" u2="&#xd6;" k="30" />
<hkern g1="braceleft.case" u2="&#xd2;" k="30" />
<hkern g1="braceleft.case" u2="&#xd4;" k="30" />
<hkern g1="braceleft.case" u2="&#xd3;" k="30" />
<hkern g1="braceleft.case" u2="&#xc7;" k="30" />
<hkern g1="braceleft.case" u2="&#xd9;" k="11" />
<hkern g1="braceleft.case" u2="&#xdb;" k="11" />
<hkern g1="braceleft.case" u2="&#xda;" k="11" />
<hkern g1="braceleft.case" u2="&#xdc;" k="11" />
<hkern g1="braceleft.case" u2="G" k="30" />
<hkern g1="braceleft.case" u2="U" k="11" />
<hkern g1="braceleft.case" u2="C" k="30" />
<hkern g1="braceleft.case" u2="Q" k="30" />
<hkern g1="braceleft.case" u2="S" k="23" />
<hkern g1="braceleft.case" u2="O" k="30" />
<hkern g1="braceleft.case" u2="&#x128;" k="-12" />
<hkern g1="braceleft.case" g2="parenleft.case" k="15" />
<hkern g1="parenleft.case" u2="&#x172;" k="14" />
<hkern g1="parenleft.case" u2="&#x122;" k="29" />
<hkern g1="parenleft.case" u2="&#x1fe;" k="29" />
<hkern g1="parenleft.case" u2="&#x170;" k="14" />
<hkern g1="parenleft.case" u2="&#x16e;" k="14" />
<hkern g1="parenleft.case" u2="&#x168;" k="14" />
<hkern g1="parenleft.case" u2="&#x150;" k="29" />
<hkern g1="parenleft.case" u2="&#x11c;" k="29" />
<hkern g1="parenleft.case" u2="&#x16c;" k="14" />
<hkern g1="parenleft.case" u2="&#x14e;" k="29" />
<hkern g1="parenleft.case" u2="&#x11e;" k="29" />
<hkern g1="parenleft.case" u2="&#x16a;" k="14" />
<hkern g1="parenleft.case" u2="&#x14c;" k="29" />
<hkern g1="parenleft.case" u2="&#x10c;" k="29" />
<hkern g1="parenleft.case" u2="&#x10a;" k="29" />
<hkern g1="parenleft.case" u2="&#x108;" k="29" />
<hkern g1="parenleft.case" u2="&#x106;" k="29" />
<hkern g1="parenleft.case" u2="&#x120;" k="29" />
<hkern g1="parenleft.case" u2="&#xd5;" k="29" />
<hkern g1="parenleft.case" u2="&#xd8;" k="29" />
<hkern g1="parenleft.case" u2="&#x152;" k="29" />
<hkern g1="parenleft.case" u2="&#xd6;" k="29" />
<hkern g1="parenleft.case" u2="&#xd2;" k="29" />
<hkern g1="parenleft.case" u2="&#xd4;" k="29" />
<hkern g1="parenleft.case" u2="&#xd3;" k="29" />
<hkern g1="parenleft.case" u2="&#xc7;" k="29" />
<hkern g1="parenleft.case" u2="&#xd9;" k="14" />
<hkern g1="parenleft.case" u2="&#xdb;" k="14" />
<hkern g1="parenleft.case" u2="&#xda;" k="14" />
<hkern g1="parenleft.case" u2="&#xdc;" k="14" />
<hkern g1="parenleft.case" u2="G" k="29" />
<hkern g1="parenleft.case" u2="U" k="14" />
<hkern g1="parenleft.case" u2="C" k="29" />
<hkern g1="parenleft.case" u2="Q" k="29" />
<hkern g1="parenleft.case" u2="O" k="29" />
<hkern g1="parenleft.case" u2="&#x218;" k="21" />
<hkern g1="parenleft.case" u2="&#x15e;" k="21" />
<hkern g1="parenleft.case" u2="&#x15c;" k="21" />
<hkern g1="parenleft.case" u2="&#x15a;" k="21" />
<hkern g1="parenleft.case" u2="&#x160;" k="21" />
<hkern g1="parenleft.case" u2="S" k="21" />
<hkern g1="parenleft.case" u2="&#x128;" k="-26" />
<hkern g1="parenleft.case" g2="parenleft.case" k="16" />
<hkern g1="parenleft.case" u2="&#xcf;" k="-6" />
<hkern g1="parenright.case" g2="parenright.case" k="16" />
<hkern g1="parenright.case" g2="braceright.case" k="15" />
<hkern g1="parenright.case" g2="bracketright.case" k="15" />
<hkern g1="endash.case" u2="&#x166;" k="12" />
<hkern g1="endash.case" u2="X" k="22" />
<hkern g1="endash.case" u2="Y" k="33" />
<hkern g1="endash.case" u2="Z" k="9" />
<hkern g1="endash.case" u2="W" k="9" />
<hkern g1="endash.case" u2="V" k="16" />
<hkern g1="endash.case" u2="J" k="47" />
<hkern g1="endash.case" u2="T" k="41" />
<hkern g1="endash.case" u2="A" k="11" />
<hkern g1="emdash.case" u2="&#x166;" k="4" />
<hkern g1="emdash.case" u2="X" k="22" />
<hkern g1="emdash.case" u2="Y" k="33" />
<hkern g1="emdash.case" u2="Z" k="9" />
<hkern g1="emdash.case" u2="W" k="9" />
<hkern g1="emdash.case" u2="V" k="16" />
<hkern g1="emdash.case" u2="J" k="47" />
<hkern g1="emdash.case" u2="T" k="41" />
<hkern g1="emdash.case" u2="A" k="11" />
<hkern g1="hyphen.case" u2="&#x166;" k="12" />
<hkern g1="hyphen.case" u2="X" k="22" />
<hkern g1="hyphen.case" u2="Y" k="33" />
<hkern g1="hyphen.case" u2="Z" k="9" />
<hkern g1="hyphen.case" u2="W" k="9" />
<hkern g1="hyphen.case" u2="V" k="16" />
<hkern g1="hyphen.case" u2="J" k="47" />
<hkern g1="hyphen.case" u2="T" k="41" />
<hkern g1="hyphen.case" u2="A" k="11" />
<hkern u1="&#xad;" u2="&#x166;" k="12" />
<hkern u1="&#xad;" g2="seven.plf" k="27" />
<hkern u1="&#xad;" g2="one.plf" k="17" />
<hkern u1="&#xad;" u2="X" k="15" />
<hkern u1="&#xad;" u2="Y" k="44" />
<hkern u1="&#xad;" u2="W" k="13" />
<hkern u1="&#xad;" u2="V" k="24" />
<hkern u1="&#xad;" u2="J" k="42" />
<hkern u1="&#xad;" u2="T" k="42" />
<hkern u1="&#xad;" u2="x" k="16" />
<hkern u1="&#xad;" u2="y" k="8" />
<hkern u1="&#xad;" u2="z" k="14" />
<hkern u1="&#x120;" u2="&#x29;" k="14" />
<hkern u1="&#x120;" u2="&#x7d;" k="18" />
<hkern u1="&#x120;" u2="]" k="17" />
<hkern u1="&#x120;" u2="Y" k="25" />
<hkern u1="&#x120;" u2="W" k="14" />
<hkern u1="&#x120;" u2="V" k="20" />
<hkern u1="&#x120;" u2="T" k="10" />
<hkern u1="&#x100;" u2="&#xba;" k="30" />
<hkern u1="&#x100;" u2="&#xaa;" k="28" />
<hkern u1="&#x100;" u2="d" k="10" />
<hkern u1="&#x100;" u2="&#x166;" k="45" />
<hkern u1="&#x100;" g2="hyphen.case" k="10" />
<hkern u1="&#x100;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x100;" u2="&#x29;" k="16" />
<hkern u1="&#x100;" g2="nine.plf" k="10" />
<hkern u1="&#x100;" g2="seven.plf" k="14" />
<hkern u1="&#x100;" g2="six.plf" k="11" />
<hkern u1="&#x100;" g2="one.plf" k="44" />
<hkern u1="&#x100;" g2="zero.plf" k="12" />
<hkern u1="&#x100;" u2="f" k="17" />
<hkern u1="&#x100;" u2="&#x2039;" k="13" />
<hkern u1="&#x100;" u2="&#x2a;" k="34" />
<hkern u1="&#x100;" u2="&#x2122;" k="43" />
<hkern u1="&#x100;" u2="&#x27;" k="38" />
<hkern u1="&#x100;" u2="&#xf0;" k="9" />
<hkern u1="&#x100;" u2="&#x7d;" k="33" />
<hkern u1="&#x100;" u2="&#xae;" k="16" />
<hkern u1="&#x100;" u2="s" k="6" />
<hkern u1="&#x100;" u2="&#x2018;" k="38" />
<hkern u1="&#x100;" u2="&#x2019;" k="40" />
<hkern u1="&#x100;" u2="&#xa9;" k="16" />
<hkern u1="&#x100;" u2="&#x3f;" k="34" />
<hkern u1="&#x100;" u2="\" k="50" />
<hkern u1="&#x100;" u2="]" k="33" />
<hkern u1="&#x100;" u2="U" k="13" />
<hkern u1="&#x100;" u2="Y" k="57" />
<hkern u1="&#x100;" u2="W" k="33" />
<hkern u1="&#x100;" u2="V" k="42" />
<hkern u1="&#x100;" u2="T" k="53" />
<hkern u1="&#x100;" u2="S" k="10" />
<hkern u1="&#x100;" u2="O" k="17" />
<hkern u1="&#x100;" u2="y" k="28" />
<hkern u1="&#x100;" u2="w" k="19" />
<hkern u1="&#x100;" u2="v" k="26" />
<hkern u1="&#x100;" u2="u" k="6" />
<hkern u1="&#x100;" u2="o" k="10" />
<hkern u1="&#x100;" u2="a" k="5" />
<hkern u1="&#x100;" u2="&#x20;" k="26" />
<hkern u1="&#x102;" u2="&#xba;" k="30" />
<hkern u1="&#x102;" u2="&#xaa;" k="28" />
<hkern u1="&#x102;" u2="d" k="10" />
<hkern u1="&#x102;" u2="&#x166;" k="45" />
<hkern u1="&#x102;" g2="hyphen.case" k="10" />
<hkern u1="&#x102;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x102;" u2="&#x29;" k="16" />
<hkern u1="&#x102;" g2="nine.plf" k="10" />
<hkern u1="&#x102;" g2="seven.plf" k="14" />
<hkern u1="&#x102;" g2="six.plf" k="11" />
<hkern u1="&#x102;" g2="one.plf" k="44" />
<hkern u1="&#x102;" g2="zero.plf" k="12" />
<hkern u1="&#x102;" u2="f" k="17" />
<hkern u1="&#x102;" u2="&#x2039;" k="13" />
<hkern u1="&#x102;" u2="&#x2a;" k="34" />
<hkern u1="&#x102;" u2="&#x2122;" k="43" />
<hkern u1="&#x102;" u2="&#x27;" k="38" />
<hkern u1="&#x102;" u2="&#xf0;" k="9" />
<hkern u1="&#x102;" u2="&#x7d;" k="33" />
<hkern u1="&#x102;" u2="&#xae;" k="16" />
<hkern u1="&#x102;" u2="s" k="6" />
<hkern u1="&#x102;" u2="&#x2018;" k="38" />
<hkern u1="&#x102;" u2="&#x2019;" k="40" />
<hkern u1="&#x102;" u2="&#xa9;" k="16" />
<hkern u1="&#x102;" u2="&#x3f;" k="34" />
<hkern u1="&#x102;" u2="\" k="50" />
<hkern u1="&#x102;" u2="]" k="33" />
<hkern u1="&#x102;" u2="U" k="13" />
<hkern u1="&#x102;" u2="Y" k="57" />
<hkern u1="&#x102;" u2="W" k="33" />
<hkern u1="&#x102;" u2="V" k="42" />
<hkern u1="&#x102;" u2="T" k="53" />
<hkern u1="&#x102;" u2="S" k="10" />
<hkern u1="&#x102;" u2="O" k="17" />
<hkern u1="&#x102;" u2="y" k="28" />
<hkern u1="&#x102;" u2="w" k="19" />
<hkern u1="&#x102;" u2="v" k="26" />
<hkern u1="&#x102;" u2="u" k="6" />
<hkern u1="&#x102;" u2="o" k="10" />
<hkern u1="&#x102;" u2="a" k="5" />
<hkern u1="&#x102;" u2="&#x20;" k="26" />
<hkern u1="&#x106;" g2="parenright.case" k="21" />
<hkern u1="&#x106;" g2="braceright.case" k="23" />
<hkern u1="&#x106;" g2="bracketright.case" k="23" />
<hkern u1="&#x106;" u2="&#x29;" k="18" />
<hkern u1="&#x106;" u2="&#x7d;" k="17" />
<hkern u1="&#x106;" u2="]" k="16" />
<hkern u1="&#x106;" u2="Y" k="16" />
<hkern u1="&#x106;" u2="W" k="7" />
<hkern u1="&#x106;" u2="V" k="12" />
<hkern u1="&#x106;" u2="A" k="7" />
<hkern u1="&#x107;" u2="&#x29;" k="33" />
<hkern u1="&#x107;" u2="&#x2122;" k="20" />
<hkern u1="&#x107;" u2="&#x7d;" k="32" />
<hkern u1="&#x107;" u2="\" k="28" />
<hkern u1="&#x107;" u2="]" k="32" />
<hkern u1="&#x107;" u2="X" k="9" />
<hkern u1="&#x107;" u2="Y" k="75" />
<hkern u1="&#x107;" u2="Z" k="5" />
<hkern u1="&#x107;" u2="W" k="32" />
<hkern u1="&#x107;" u2="V" k="45" />
<hkern u1="&#x107;" u2="T" k="85" />
<hkern u1="&#x107;" u2="S" k="5" />
<hkern u1="&#x107;" u2="A" k="5" />
<hkern u1="&#x107;" u2="x" k="8" />
<hkern u1="&#x107;" u2="y" k="8" />
<hkern u1="&#x107;" u2="v" k="6" />
<hkern u1="&#x108;" g2="parenright.case" k="21" />
<hkern u1="&#x108;" g2="braceright.case" k="23" />
<hkern u1="&#x108;" g2="bracketright.case" k="23" />
<hkern u1="&#x108;" u2="&#x29;" k="18" />
<hkern u1="&#x108;" u2="&#x7d;" k="17" />
<hkern u1="&#x108;" u2="]" k="16" />
<hkern u1="&#x108;" u2="Y" k="16" />
<hkern u1="&#x108;" u2="W" k="7" />
<hkern u1="&#x108;" u2="V" k="12" />
<hkern u1="&#x108;" u2="A" k="7" />
<hkern u1="&#x109;" u2="&#x29;" k="33" />
<hkern u1="&#x109;" u2="&#x2122;" k="20" />
<hkern u1="&#x109;" u2="&#x7d;" k="32" />
<hkern u1="&#x109;" u2="\" k="28" />
<hkern u1="&#x109;" u2="]" k="32" />
<hkern u1="&#x109;" u2="X" k="9" />
<hkern u1="&#x109;" u2="Y" k="75" />
<hkern u1="&#x109;" u2="Z" k="5" />
<hkern u1="&#x109;" u2="W" k="32" />
<hkern u1="&#x109;" u2="V" k="45" />
<hkern u1="&#x109;" u2="T" k="85" />
<hkern u1="&#x109;" u2="S" k="5" />
<hkern u1="&#x109;" u2="A" k="5" />
<hkern u1="&#x109;" u2="x" k="8" />
<hkern u1="&#x109;" u2="y" k="8" />
<hkern u1="&#x109;" u2="v" k="6" />
<hkern u1="&#x10a;" g2="parenright.case" k="21" />
<hkern u1="&#x10a;" g2="braceright.case" k="23" />
<hkern u1="&#x10a;" g2="bracketright.case" k="23" />
<hkern u1="&#x10a;" u2="&#x29;" k="18" />
<hkern u1="&#x10a;" u2="&#x7d;" k="17" />
<hkern u1="&#x10a;" u2="]" k="16" />
<hkern u1="&#x10a;" u2="Y" k="16" />
<hkern u1="&#x10a;" u2="W" k="7" />
<hkern u1="&#x10a;" u2="V" k="12" />
<hkern u1="&#x10a;" u2="A" k="7" />
<hkern u1="&#x10b;" u2="&#x29;" k="33" />
<hkern u1="&#x10b;" u2="&#x2122;" k="20" />
<hkern u1="&#x10b;" u2="&#x7d;" k="32" />
<hkern u1="&#x10b;" u2="\" k="28" />
<hkern u1="&#x10b;" u2="]" k="32" />
<hkern u1="&#x10b;" u2="X" k="9" />
<hkern u1="&#x10b;" u2="Y" k="75" />
<hkern u1="&#x10b;" u2="Z" k="5" />
<hkern u1="&#x10b;" u2="W" k="32" />
<hkern u1="&#x10b;" u2="V" k="45" />
<hkern u1="&#x10b;" u2="T" k="85" />
<hkern u1="&#x10b;" u2="S" k="5" />
<hkern u1="&#x10b;" u2="A" k="5" />
<hkern u1="&#x10b;" u2="x" k="8" />
<hkern u1="&#x10b;" u2="y" k="8" />
<hkern u1="&#x10b;" u2="v" k="6" />
<hkern u1="&#x10c;" g2="parenright.case" k="21" />
<hkern u1="&#x10c;" g2="braceright.case" k="23" />
<hkern u1="&#x10c;" g2="bracketright.case" k="23" />
<hkern u1="&#x10c;" u2="&#x29;" k="18" />
<hkern u1="&#x10c;" u2="&#x7d;" k="17" />
<hkern u1="&#x10c;" u2="]" k="16" />
<hkern u1="&#x10c;" u2="Y" k="16" />
<hkern u1="&#x10c;" u2="W" k="7" />
<hkern u1="&#x10c;" u2="V" k="12" />
<hkern u1="&#x10c;" u2="A" k="7" />
<hkern u1="&#x10d;" u2="&#x29;" k="33" />
<hkern u1="&#x10d;" u2="&#x2122;" k="20" />
<hkern u1="&#x10d;" u2="&#x7d;" k="32" />
<hkern u1="&#x10d;" u2="\" k="28" />
<hkern u1="&#x10d;" u2="]" k="32" />
<hkern u1="&#x10d;" u2="X" k="9" />
<hkern u1="&#x10d;" u2="Y" k="75" />
<hkern u1="&#x10d;" u2="Z" k="5" />
<hkern u1="&#x10d;" u2="W" k="32" />
<hkern u1="&#x10d;" u2="V" k="45" />
<hkern u1="&#x10d;" u2="T" k="85" />
<hkern u1="&#x10d;" u2="S" k="5" />
<hkern u1="&#x10d;" u2="A" k="5" />
<hkern u1="&#x10d;" u2="x" k="8" />
<hkern u1="&#x10d;" u2="y" k="8" />
<hkern u1="&#x10d;" u2="v" k="6" />
<hkern u1="&#x10e;" u2="&#x166;" k="8" />
<hkern u1="&#x10e;" g2="parenright.case" k="29" />
<hkern u1="&#x10e;" g2="braceright.case" k="31" />
<hkern u1="&#x10e;" g2="bracketright.case" k="31" />
<hkern u1="&#x10e;" u2="&#x29;" k="27" />
<hkern u1="&#x10e;" g2="seven.plf" k="12" />
<hkern u1="&#x10e;" u2="&#xc6;" k="12" />
<hkern u1="&#x10e;" u2="&#x7d;" k="25" />
<hkern u1="&#x10e;" u2="]" k="25" />
<hkern u1="&#x10e;" u2="X" k="26" />
<hkern u1="&#x10e;" u2="Y" k="30" />
<hkern u1="&#x10e;" u2="Z" k="8" />
<hkern u1="&#x10e;" u2="W" k="15" />
<hkern u1="&#x10e;" u2="V" k="21" />
<hkern u1="&#x10e;" u2="J" k="17" />
<hkern u1="&#x10e;" u2="T" k="17" />
<hkern u1="&#x10e;" u2="A" k="17" />
<hkern u1="&#x10e;" u2="x" k="13" />
<hkern u1="&#x10e;" u2="z" k="5" />
<hkern u1="&#x113;" u2="&#x29;" k="33" />
<hkern u1="&#x113;" u2="f" k="4" />
<hkern u1="&#x113;" u2="&#x2122;" k="21" />
<hkern u1="&#x113;" u2="&#x7d;" k="33" />
<hkern u1="&#x113;" u2="&#x2018;" k="8" />
<hkern u1="&#x113;" u2="&#x2019;" k="11" />
<hkern u1="&#x113;" u2="\" k="31" />
<hkern u1="&#x113;" u2="]" k="33" />
<hkern u1="&#x113;" u2="X" k="11" />
<hkern u1="&#x113;" u2="Y" k="84" />
<hkern u1="&#x113;" u2="Z" k="7" />
<hkern u1="&#x113;" u2="W" k="37" />
<hkern u1="&#x113;" u2="V" k="44" />
<hkern u1="&#x113;" u2="T" k="75" />
<hkern u1="&#x113;" u2="A" k="8" />
<hkern u1="&#x113;" u2="x" k="15" />
<hkern u1="&#x113;" u2="y" k="11" />
<hkern u1="&#x113;" u2="w" k="6" />
<hkern u1="&#x113;" u2="v" k="9" />
<hkern u1="&#x12a;" u2="d" k="6" />
<hkern u1="&#x12a;" u2="&#xf0;" k="7" />
<hkern u1="&#x12a;" u2="o" k="6" />
<hkern u1="&#x12b;" u2="&#x7d;" k="-8" />
<hkern u1="&#x12b;" u2="\" k="-24" />
<hkern u1="&#x12b;" u2="]" k="-8" />
<hkern u1="&#x12b;" u2="U" k="5" />
<hkern u1="&#x12b;" u2="Z" k="5" />
<hkern u1="&#x16a;" u2="d" k="6" />
<hkern u1="&#x16a;" g2="parenright.case" k="14" />
<hkern u1="&#x16a;" u2="i" k="5" />
<hkern u1="&#x16a;" u2="&#xc6;" k="11" />
<hkern u1="&#x16a;" u2="&#xf0;" k="7" />
<hkern u1="&#x16a;" u2="s" k="5" />
<hkern u1="&#x16a;" u2="J" k="14" />
<hkern u1="&#x16a;" u2="A" k="13" />
<hkern u1="&#x16a;" u2="&#x2e;" k="8" />
<hkern u1="&#x16a;" u2="z" k="7" />
<hkern u1="&#x16a;" u2="u" k="7" />
<hkern u1="&#x16a;" u2="o" k="5" />
<hkern u1="&#x16a;" u2="a" k="6" />
<hkern u1="&#x16b;" u2="&#x29;" k="26" />
<hkern u1="&#x16b;" u2="&#x2122;" k="17" />
<hkern u1="&#x16b;" u2="&#x7d;" k="26" />
<hkern u1="&#x16b;" u2="\" k="16" />
<hkern u1="&#x16b;" u2="]" k="26" />
<hkern u1="&#x16b;" u2="Y" k="54" />
<hkern u1="&#x16b;" u2="Z" k="5" />
<hkern u1="&#x16b;" u2="W" k="26" />
<hkern u1="&#x16b;" u2="V" k="37" />
<hkern u1="&#x16b;" u2="T" k="87" />
<hkern u1="&#x115;" u2="&#x29;" k="33" />
<hkern u1="&#x115;" u2="f" k="4" />
<hkern u1="&#x115;" u2="&#x2122;" k="21" />
<hkern u1="&#x115;" u2="&#x7d;" k="33" />
<hkern u1="&#x115;" u2="&#x2018;" k="8" />
<hkern u1="&#x115;" u2="&#x2019;" k="11" />
<hkern u1="&#x115;" u2="\" k="31" />
<hkern u1="&#x115;" u2="]" k="33" />
<hkern u1="&#x115;" u2="X" k="11" />
<hkern u1="&#x115;" u2="Y" k="84" />
<hkern u1="&#x115;" u2="Z" k="7" />
<hkern u1="&#x115;" u2="W" k="37" />
<hkern u1="&#x115;" u2="V" k="44" />
<hkern u1="&#x115;" u2="T" k="75" />
<hkern u1="&#x115;" u2="A" k="8" />
<hkern u1="&#x115;" u2="x" k="15" />
<hkern u1="&#x115;" u2="y" k="11" />
<hkern u1="&#x115;" u2="w" k="6" />
<hkern u1="&#x115;" u2="v" k="9" />
<hkern u1="&#x11e;" u2="&#x29;" k="14" />
<hkern u1="&#x11e;" u2="&#x7d;" k="18" />
<hkern u1="&#x11e;" u2="]" k="17" />
<hkern u1="&#x11e;" u2="Y" k="25" />
<hkern u1="&#x11e;" u2="W" k="14" />
<hkern u1="&#x11e;" u2="V" k="20" />
<hkern u1="&#x11e;" u2="T" k="10" />
<hkern u1="&#x11f;" u2="&#x29;" k="26" />
<hkern u1="&#x11f;" u2="&#x2122;" k="17" />
<hkern u1="&#x11f;" u2="&#x7d;" k="26" />
<hkern u1="&#x11f;" u2="\" k="16" />
<hkern u1="&#x11f;" u2="]" k="26" />
<hkern u1="&#x11f;" u2="Y" k="54" />
<hkern u1="&#x11f;" u2="Z" k="5" />
<hkern u1="&#x11f;" u2="W" k="26" />
<hkern u1="&#x11f;" u2="V" k="37" />
<hkern u1="&#x11f;" u2="T" k="87" />
<hkern u1="&#x12c;" u2="d" k="6" />
<hkern u1="&#x12c;" u2="&#xf0;" k="7" />
<hkern u1="&#x12c;" u2="o" k="6" />
<hkern u1="&#x12d;" u2="\" k="-19" />
<hkern u1="&#x12d;" u2="U" k="5" />
<hkern u1="&#x12d;" u2="Z" k="5" />
<hkern u1="&#x16c;" u2="d" k="6" />
<hkern u1="&#x16c;" g2="parenright.case" k="14" />
<hkern u1="&#x16c;" u2="i" k="5" />
<hkern u1="&#x16c;" u2="&#xc6;" k="11" />
<hkern u1="&#x16c;" u2="&#xf0;" k="7" />
<hkern u1="&#x16c;" u2="s" k="5" />
<hkern u1="&#x16c;" u2="J" k="14" />
<hkern u1="&#x16c;" u2="A" k="13" />
<hkern u1="&#x16c;" u2="&#x2e;" k="8" />
<hkern u1="&#x16c;" u2="z" k="7" />
<hkern u1="&#x16c;" u2="u" k="7" />
<hkern u1="&#x16c;" u2="o" k="5" />
<hkern u1="&#x16c;" u2="a" k="6" />
<hkern u1="&#x16d;" u2="&#x29;" k="26" />
<hkern u1="&#x16d;" u2="&#x2122;" k="17" />
<hkern u1="&#x16d;" u2="&#x7d;" k="26" />
<hkern u1="&#x16d;" u2="\" k="16" />
<hkern u1="&#x16d;" u2="]" k="26" />
<hkern u1="&#x16d;" u2="Y" k="54" />
<hkern u1="&#x16d;" u2="Z" k="5" />
<hkern u1="&#x16d;" u2="W" k="26" />
<hkern u1="&#x16d;" u2="V" k="37" />
<hkern u1="&#x16d;" u2="T" k="87" />
<hkern u1="&#x117;" u2="&#x29;" k="33" />
<hkern u1="&#x117;" u2="f" k="4" />
<hkern u1="&#x117;" u2="&#x2122;" k="21" />
<hkern u1="&#x117;" u2="&#x7d;" k="33" />
<hkern u1="&#x117;" u2="&#x2018;" k="8" />
<hkern u1="&#x117;" u2="&#x2019;" k="11" />
<hkern u1="&#x117;" u2="\" k="31" />
<hkern u1="&#x117;" u2="]" k="33" />
<hkern u1="&#x117;" u2="X" k="11" />
<hkern u1="&#x117;" u2="Y" k="84" />
<hkern u1="&#x117;" u2="Z" k="7" />
<hkern u1="&#x117;" u2="W" k="37" />
<hkern u1="&#x117;" u2="V" k="44" />
<hkern u1="&#x117;" u2="T" k="75" />
<hkern u1="&#x117;" u2="A" k="8" />
<hkern u1="&#x117;" u2="x" k="15" />
<hkern u1="&#x117;" u2="y" k="11" />
<hkern u1="&#x117;" u2="w" k="6" />
<hkern u1="&#x117;" u2="v" k="9" />
<hkern u1="&#x11b;" u2="&#x29;" k="33" />
<hkern u1="&#x11b;" u2="f" k="4" />
<hkern u1="&#x11b;" u2="&#x2122;" k="21" />
<hkern u1="&#x11b;" u2="&#x7d;" k="33" />
<hkern u1="&#x11b;" u2="&#x2018;" k="8" />
<hkern u1="&#x11b;" u2="&#x2019;" k="11" />
<hkern u1="&#x11b;" u2="\" k="31" />
<hkern u1="&#x11b;" u2="]" k="33" />
<hkern u1="&#x11b;" u2="X" k="11" />
<hkern u1="&#x11b;" u2="Y" k="84" />
<hkern u1="&#x11b;" u2="Z" k="7" />
<hkern u1="&#x11b;" u2="W" k="37" />
<hkern u1="&#x11b;" u2="V" k="44" />
<hkern u1="&#x11b;" u2="T" k="75" />
<hkern u1="&#x11b;" u2="A" k="8" />
<hkern u1="&#x11b;" u2="x" k="15" />
<hkern u1="&#x11b;" u2="y" k="11" />
<hkern u1="&#x11b;" u2="w" k="6" />
<hkern u1="&#x11b;" u2="v" k="9" />
<hkern u1="&#x11c;" u2="&#x29;" k="14" />
<hkern u1="&#x11c;" u2="&#x7d;" k="18" />
<hkern u1="&#x11c;" u2="]" k="17" />
<hkern u1="&#x11c;" u2="Y" k="25" />
<hkern u1="&#x11c;" u2="W" k="14" />
<hkern u1="&#x11c;" u2="V" k="20" />
<hkern u1="&#x11c;" u2="T" k="10" />
<hkern u1="&#x11d;" u2="&#x29;" k="26" />
<hkern u1="&#x11d;" u2="&#x2122;" k="17" />
<hkern u1="&#x11d;" u2="&#x7d;" k="26" />
<hkern u1="&#x11d;" u2="\" k="16" />
<hkern u1="&#x11d;" u2="]" k="26" />
<hkern u1="&#x11d;" u2="Y" k="54" />
<hkern u1="&#x11d;" u2="Z" k="5" />
<hkern u1="&#x11d;" u2="W" k="26" />
<hkern u1="&#x11d;" u2="V" k="37" />
<hkern u1="&#x11d;" u2="T" k="87" />
<hkern u1="&#x121;" u2="&#x29;" k="26" />
<hkern u1="&#x121;" u2="&#x2122;" k="17" />
<hkern u1="&#x121;" u2="&#x7d;" k="26" />
<hkern u1="&#x121;" u2="\" k="16" />
<hkern u1="&#x121;" u2="]" k="26" />
<hkern u1="&#x121;" u2="Y" k="54" />
<hkern u1="&#x121;" u2="Z" k="5" />
<hkern u1="&#x121;" u2="W" k="26" />
<hkern u1="&#x121;" u2="V" k="37" />
<hkern u1="&#x121;" u2="T" k="87" />
<hkern u1="&#x124;" u2="d" k="6" />
<hkern u1="&#x124;" u2="&#xf0;" k="7" />
<hkern u1="&#x124;" u2="o" k="6" />
<hkern u1="&#x125;" u2="&#x29;" k="28" />
<hkern u1="&#x125;" u2="&#x2122;" k="21" />
<hkern u1="&#x125;" u2="&#x7d;" k="33" />
<hkern u1="&#x125;" u2="&#x2019;" k="8" />
<hkern u1="&#x125;" u2="\" k="30" />
<hkern u1="&#x125;" u2="]" k="32" />
<hkern u1="&#x125;" u2="U" k="6" />
<hkern u1="&#x125;" u2="Y" k="63" />
<hkern u1="&#x125;" u2="Z" k="5" />
<hkern u1="&#x125;" u2="W" k="31" />
<hkern u1="&#x125;" u2="V" k="43" />
<hkern u1="&#x125;" u2="T" k="81" />
<hkern u1="&#x125;" u2="y" k="6" />
<hkern u1="&#x125;" u2="v" k="6" />
<hkern u1="&#x123;" u2="&#x29;" k="26" />
<hkern u1="&#x123;" u2="&#x2122;" k="17" />
<hkern u1="&#x123;" u2="&#x7d;" k="26" />
<hkern u1="&#x123;" u2="\" k="16" />
<hkern u1="&#x123;" u2="]" k="26" />
<hkern u1="&#x123;" u2="Y" k="54" />
<hkern u1="&#x123;" u2="Z" k="5" />
<hkern u1="&#x123;" u2="W" k="26" />
<hkern u1="&#x123;" u2="V" k="37" />
<hkern u1="&#x123;" u2="T" k="87" />
<hkern u1="&#x128;" u2="d" k="6" />
<hkern u1="&#x128;" u2="&#xf0;" k="7" />
<hkern u1="&#x128;" u2="o" k="6" />
<hkern u1="&#x129;" u2="&#x29;" k="-8" />
<hkern u1="&#x129;" u2="&#x2122;" k="-9" />
<hkern u1="&#x129;" u2="&#x7d;" k="-17" />
<hkern u1="&#x129;" u2="\" k="-39" />
<hkern u1="&#x129;" u2="]" k="-17" />
<hkern u1="&#x129;" u2="U" k="5" />
<hkern u1="&#x129;" u2="Z" k="5" />
<hkern u1="&#x130;" u2="d" k="6" />
<hkern u1="&#x130;" u2="&#xf0;" k="7" />
<hkern u1="&#x130;" u2="o" k="6" />
<hkern u1="&#x133;" u2="U" k="5" />
<hkern u1="&#x133;" u2="Z" k="5" />
<hkern u1="&#x132;" u2="d" k="6" />
<hkern u1="&#x132;" u2="&#xf0;" k="7" />
<hkern u1="&#x132;" u2="o" k="6" />
<hkern u1="&#x134;" u2="d" k="6" />
<hkern u1="&#x134;" u2="&#xf0;" k="7" />
<hkern u1="&#x134;" u2="o" k="6" />
<hkern u1="&#x135;" u2="U" k="5" />
<hkern u1="&#x135;" u2="Z" k="5" />
<hkern u1="&#x143;" u2="d" k="6" />
<hkern u1="&#x143;" u2="&#xf0;" k="7" />
<hkern u1="&#x143;" u2="o" k="6" />
<hkern u1="&#x144;" u2="&#x29;" k="28" />
<hkern u1="&#x144;" u2="&#x2122;" k="21" />
<hkern u1="&#x144;" u2="&#x7d;" k="33" />
<hkern u1="&#x144;" u2="&#x2019;" k="8" />
<hkern u1="&#x144;" u2="\" k="30" />
<hkern u1="&#x144;" u2="]" k="32" />
<hkern u1="&#x144;" u2="U" k="6" />
<hkern u1="&#x144;" u2="Y" k="63" />
<hkern u1="&#x144;" u2="Z" k="5" />
<hkern u1="&#x144;" u2="W" k="31" />
<hkern u1="&#x144;" u2="V" k="43" />
<hkern u1="&#x144;" u2="T" k="81" />
<hkern u1="&#x144;" u2="y" k="6" />
<hkern u1="&#x144;" u2="v" k="6" />
<hkern u1="&#x147;" u2="d" k="6" />
<hkern u1="&#x147;" u2="&#xf0;" k="7" />
<hkern u1="&#x147;" u2="o" k="6" />
<hkern u1="&#x148;" u2="&#x29;" k="28" />
<hkern u1="&#x148;" u2="&#x2122;" k="21" />
<hkern u1="&#x148;" u2="&#x7d;" k="33" />
<hkern u1="&#x148;" u2="&#x2019;" k="8" />
<hkern u1="&#x148;" u2="\" k="30" />
<hkern u1="&#x148;" u2="]" k="32" />
<hkern u1="&#x148;" u2="U" k="6" />
<hkern u1="&#x148;" u2="Y" k="63" />
<hkern u1="&#x148;" u2="Z" k="5" />
<hkern u1="&#x148;" u2="W" k="31" />
<hkern u1="&#x148;" u2="V" k="43" />
<hkern u1="&#x148;" u2="T" k="81" />
<hkern u1="&#x148;" u2="y" k="6" />
<hkern u1="&#x148;" u2="v" k="6" />
<hkern u1="&#x155;" u2="d" k="8" />
<hkern u1="&#x155;" u2="&#x29;" k="23" />
<hkern u1="&#x155;" u2="&#x2039;" k="22" />
<hkern u1="&#x155;" u2="&#xc6;" k="44" />
<hkern u1="&#x155;" u2="&#xf0;" k="21" />
<hkern u1="&#x155;" u2="&#x7d;" k="15" />
<hkern u1="&#x155;" u2="]" k="15" />
<hkern u1="&#x155;" u2="&#x2f;" k="28" />
<hkern u1="&#x155;" u2="&#x2d;" k="27" />
<hkern u1="&#x155;" u2="X" k="28" />
<hkern u1="&#x155;" u2="Y" k="21" />
<hkern u1="&#x155;" u2="Z" k="11" />
<hkern u1="&#x155;" u2="J" k="56" />
<hkern u1="&#x155;" u2="T" k="46" />
<hkern u1="&#x155;" u2="A" k="39" />
<hkern u1="&#x155;" u2="&#x2e;" k="34" />
<hkern u1="&#x155;" u2="o" k="9" />
<hkern u1="&#x155;" u2="a" k="4" />
<hkern u1="&#x155;" u2="&#x20;" k="20" />
<hkern u1="&#x154;" u2="d" k="12" />
<hkern u1="&#x154;" g2="four.plf" k="11" />
<hkern u1="&#x154;" u2="&#x2039;" k="17" />
<hkern u1="&#x154;" u2="&#xf0;" k="18" />
<hkern u1="&#x154;" u2="&#x7d;" k="11" />
<hkern u1="&#x154;" u2="s" k="6" />
<hkern u1="&#x154;" u2="]" k="11" />
<hkern u1="&#x154;" u2="Y" k="15" />
<hkern u1="&#x154;" u2="W" k="10" />
<hkern u1="&#x154;" u2="V" k="14" />
<hkern u1="&#x154;" u2="A" k="5" />
<hkern u1="&#x154;" u2="u" k="6" />
<hkern u1="&#x154;" u2="o" k="13" />
<hkern u1="&#x154;" u2="a" k="7" />
<hkern u1="&#x158;" u2="d" k="12" />
<hkern u1="&#x158;" g2="four.plf" k="11" />
<hkern u1="&#x158;" u2="&#x2039;" k="17" />
<hkern u1="&#x158;" u2="&#xf0;" k="18" />
<hkern u1="&#x158;" u2="&#x7d;" k="11" />
<hkern u1="&#x158;" u2="s" k="6" />
<hkern u1="&#x158;" u2="]" k="11" />
<hkern u1="&#x158;" u2="Y" k="15" />
<hkern u1="&#x158;" u2="W" k="10" />
<hkern u1="&#x158;" u2="V" k="14" />
<hkern u1="&#x158;" u2="A" k="5" />
<hkern u1="&#x158;" u2="u" k="6" />
<hkern u1="&#x158;" u2="o" k="13" />
<hkern u1="&#x158;" u2="a" k="7" />
<hkern u1="&#x159;" u2="d" k="8" />
<hkern u1="&#x159;" u2="&#x29;" k="23" />
<hkern u1="&#x159;" u2="&#x2039;" k="22" />
<hkern u1="&#x159;" u2="&#xc6;" k="44" />
<hkern u1="&#x159;" u2="&#xf0;" k="21" />
<hkern u1="&#x159;" u2="&#x7d;" k="15" />
<hkern u1="&#x159;" u2="]" k="15" />
<hkern u1="&#x159;" u2="&#x2f;" k="28" />
<hkern u1="&#x159;" u2="&#x2d;" k="27" />
<hkern u1="&#x159;" u2="X" k="28" />
<hkern u1="&#x159;" u2="Y" k="21" />
<hkern u1="&#x159;" u2="Z" k="11" />
<hkern u1="&#x159;" u2="J" k="56" />
<hkern u1="&#x159;" u2="T" k="46" />
<hkern u1="&#x159;" u2="A" k="39" />
<hkern u1="&#x159;" u2="&#x2e;" k="34" />
<hkern u1="&#x159;" u2="o" k="9" />
<hkern u1="&#x159;" u2="a" k="4" />
<hkern u1="&#x159;" u2="&#x20;" k="20" />
<hkern u1="&#x15a;" g2="parenright.case" k="21" />
<hkern u1="&#x15a;" g2="braceright.case" k="23" />
<hkern u1="&#x15a;" g2="bracketright.case" k="23" />
<hkern u1="&#x15a;" u2="&#x29;" k="19" />
<hkern u1="&#x15a;" u2="&#x7d;" k="17" />
<hkern u1="&#x15a;" u2="]" k="17" />
<hkern u1="&#x15a;" u2="X" k="6" />
<hkern u1="&#x15a;" u2="Y" k="21" />
<hkern u1="&#x15a;" u2="W" k="12" />
<hkern u1="&#x15a;" u2="V" k="17" />
<hkern u1="&#x15a;" u2="T" k="5" />
<hkern u1="&#x15a;" u2="A" k="9" />
<hkern u1="&#x15a;" u2="x" k="9" />
<hkern u1="&#x15b;" u2="&#x29;" k="32" />
<hkern u1="&#x15b;" u2="&#x2122;" k="23" />
<hkern u1="&#x15b;" u2="&#x7d;" k="32" />
<hkern u1="&#x15b;" u2="&#x2019;" k="9" />
<hkern u1="&#x15b;" u2="\" k="31" />
<hkern u1="&#x15b;" u2="]" k="32" />
<hkern u1="&#x15b;" u2="X" k="5" />
<hkern u1="&#x15b;" u2="U" k="6" />
<hkern u1="&#x15b;" u2="Y" k="81" />
<hkern u1="&#x15b;" u2="W" k="39" />
<hkern u1="&#x15b;" u2="V" k="44" />
<hkern u1="&#x15b;" u2="T" k="74" />
<hkern u1="&#x15b;" u2="A" k="6" />
<hkern u1="&#x15b;" u2="x" k="5" />
<hkern u1="&#x15b;" u2="y" k="11" />
<hkern u1="&#x15b;" u2="w" k="6" />
<hkern u1="&#x15b;" u2="v" k="9" />
<hkern u1="&#x15c;" g2="parenright.case" k="21" />
<hkern u1="&#x15c;" g2="braceright.case" k="23" />
<hkern u1="&#x15c;" g2="bracketright.case" k="23" />
<hkern u1="&#x15c;" u2="&#x29;" k="19" />
<hkern u1="&#x15c;" u2="&#x7d;" k="17" />
<hkern u1="&#x15c;" u2="]" k="17" />
<hkern u1="&#x15c;" u2="X" k="6" />
<hkern u1="&#x15c;" u2="Y" k="21" />
<hkern u1="&#x15c;" u2="W" k="12" />
<hkern u1="&#x15c;" u2="V" k="17" />
<hkern u1="&#x15c;" u2="T" k="5" />
<hkern u1="&#x15c;" u2="A" k="9" />
<hkern u1="&#x15c;" u2="x" k="9" />
<hkern u1="&#x15d;" u2="&#x29;" k="32" />
<hkern u1="&#x15d;" u2="&#x2122;" k="23" />
<hkern u1="&#x15d;" u2="&#x7d;" k="32" />
<hkern u1="&#x15d;" u2="&#x2019;" k="9" />
<hkern u1="&#x15d;" u2="\" k="31" />
<hkern u1="&#x15d;" u2="]" k="32" />
<hkern u1="&#x15d;" u2="X" k="5" />
<hkern u1="&#x15d;" u2="U" k="6" />
<hkern u1="&#x15d;" u2="Y" k="81" />
<hkern u1="&#x15d;" u2="W" k="39" />
<hkern u1="&#x15d;" u2="V" k="44" />
<hkern u1="&#x15d;" u2="T" k="74" />
<hkern u1="&#x15d;" u2="A" k="6" />
<hkern u1="&#x15d;" u2="x" k="5" />
<hkern u1="&#x15d;" u2="y" k="11" />
<hkern u1="&#x15d;" u2="w" k="6" />
<hkern u1="&#x15d;" u2="v" k="9" />
<hkern u1="&#x164;" u2="d" k="80" />
<hkern u1="&#x164;" u2="&#x127;" k="-13" />
<hkern u1="&#x164;" u2="&#x159;" k="78" />
<hkern u1="&#x164;" u2="&#x129;" k="-28" />
<hkern u1="&#x164;" u2="&#x12d;" k="-9" />
<hkern u1="&#x164;" u2="&#x12b;" k="-20" />
<hkern u1="&#x164;" g2="hyphen.case" k="41" />
<hkern u1="&#x164;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x164;" g2="guilsinglright.case" k="36" />
<hkern u1="&#x164;" g2="four.plf" k="49" />
<hkern u1="&#x164;" u2="n" k="87" />
<hkern u1="&#x164;" u2="f" k="20" />
<hkern u1="&#x164;" u2="&#x203a;" k="59" />
<hkern u1="&#x164;" u2="&#x2039;" k="66" />
<hkern u1="&#x164;" u2="&#xdf;" k="16" />
<hkern u1="&#x164;" u2="&#xc6;" k="58" />
<hkern u1="&#x164;" u2="&#xf0;" k="56" />
<hkern u1="&#x164;" u2="&#x131;" k="87" />
<hkern u1="&#x164;" u2="&#xae;" k="17" />
<hkern u1="&#x164;" u2="s" k="73" />
<hkern u1="&#x164;" u2="&#xa9;" k="17" />
<hkern u1="&#x164;" u2="&#xef;" k="-7" />
<hkern u1="&#x164;" u2="&#x2f;" k="47" />
<hkern u1="&#x164;" u2="&#x3a;" k="29" />
<hkern u1="&#x164;" u2="&#x2d;" k="42" />
<hkern u1="&#x164;" u2="J" k="55" />
<hkern u1="&#x164;" u2="O" k="16" />
<hkern u1="&#x164;" u2="A" k="53" />
<hkern u1="&#x164;" u2="&#x2e;" k="49" />
<hkern u1="&#x164;" u2="x" k="60" />
<hkern u1="&#x164;" u2="y" k="47" />
<hkern u1="&#x164;" u2="z" k="76" />
<hkern u1="&#x164;" u2="w" k="49" />
<hkern u1="&#x164;" u2="v" k="47" />
<hkern u1="&#x164;" u2="u" k="87" />
<hkern u1="&#x164;" u2="o" k="81" />
<hkern u1="&#x164;" u2="a" k="74" />
<hkern u1="&#x164;" u2="&#x20;" k="26" />
<hkern u1="&#x168;" u2="d" k="6" />
<hkern u1="&#x168;" g2="parenright.case" k="14" />
<hkern u1="&#x168;" u2="i" k="5" />
<hkern u1="&#x168;" u2="&#xc6;" k="11" />
<hkern u1="&#x168;" u2="&#xf0;" k="7" />
<hkern u1="&#x168;" u2="s" k="5" />
<hkern u1="&#x168;" u2="J" k="14" />
<hkern u1="&#x168;" u2="A" k="13" />
<hkern u1="&#x168;" u2="&#x2e;" k="8" />
<hkern u1="&#x168;" u2="z" k="7" />
<hkern u1="&#x168;" u2="u" k="7" />
<hkern u1="&#x168;" u2="o" k="5" />
<hkern u1="&#x168;" u2="a" k="6" />
<hkern u1="&#x169;" u2="&#x29;" k="26" />
<hkern u1="&#x169;" u2="&#x2122;" k="17" />
<hkern u1="&#x169;" u2="&#x7d;" k="26" />
<hkern u1="&#x169;" u2="\" k="16" />
<hkern u1="&#x169;" u2="]" k="26" />
<hkern u1="&#x169;" u2="Y" k="54" />
<hkern u1="&#x169;" u2="Z" k="5" />
<hkern u1="&#x169;" u2="W" k="26" />
<hkern u1="&#x169;" u2="V" k="37" />
<hkern u1="&#x169;" u2="T" k="87" />
<hkern u1="&#x16e;" u2="d" k="6" />
<hkern u1="&#x16e;" g2="parenright.case" k="14" />
<hkern u1="&#x16e;" u2="i" k="5" />
<hkern u1="&#x16e;" u2="&#xc6;" k="11" />
<hkern u1="&#x16e;" u2="&#xf0;" k="7" />
<hkern u1="&#x16e;" u2="s" k="5" />
<hkern u1="&#x16e;" u2="J" k="14" />
<hkern u1="&#x16e;" u2="A" k="13" />
<hkern u1="&#x16e;" u2="&#x2e;" k="8" />
<hkern u1="&#x16e;" u2="z" k="7" />
<hkern u1="&#x16e;" u2="u" k="7" />
<hkern u1="&#x16e;" u2="o" k="5" />
<hkern u1="&#x16e;" u2="a" k="6" />
<hkern u1="&#x16f;" u2="&#x29;" k="26" />
<hkern u1="&#x16f;" u2="&#x2122;" k="17" />
<hkern u1="&#x16f;" u2="&#x7d;" k="26" />
<hkern u1="&#x16f;" u2="\" k="16" />
<hkern u1="&#x16f;" u2="]" k="26" />
<hkern u1="&#x16f;" u2="Y" k="54" />
<hkern u1="&#x16f;" u2="Z" k="5" />
<hkern u1="&#x16f;" u2="W" k="26" />
<hkern u1="&#x16f;" u2="V" k="37" />
<hkern u1="&#x16f;" u2="T" k="87" />
<hkern u1="&#x170;" u2="d" k="6" />
<hkern u1="&#x170;" g2="parenright.case" k="14" />
<hkern u1="&#x170;" u2="i" k="5" />
<hkern u1="&#x170;" u2="&#xc6;" k="11" />
<hkern u1="&#x170;" u2="&#xf0;" k="7" />
<hkern u1="&#x170;" u2="s" k="5" />
<hkern u1="&#x170;" u2="J" k="14" />
<hkern u1="&#x170;" u2="A" k="13" />
<hkern u1="&#x170;" u2="&#x2e;" k="8" />
<hkern u1="&#x170;" u2="z" k="7" />
<hkern u1="&#x170;" u2="u" k="7" />
<hkern u1="&#x170;" u2="o" k="5" />
<hkern u1="&#x170;" u2="a" k="6" />
<hkern u1="&#x171;" u2="&#x29;" k="26" />
<hkern u1="&#x171;" u2="&#x2122;" k="17" />
<hkern u1="&#x171;" u2="&#x7d;" k="26" />
<hkern u1="&#x171;" u2="\" k="16" />
<hkern u1="&#x171;" u2="]" k="26" />
<hkern u1="&#x171;" u2="Y" k="54" />
<hkern u1="&#x171;" u2="Z" k="5" />
<hkern u1="&#x171;" u2="W" k="26" />
<hkern u1="&#x171;" u2="V" k="37" />
<hkern u1="&#x171;" u2="T" k="87" />
<hkern u1="&#x174;" u2="d" k="37" />
<hkern u1="&#x174;" u2="&#x159;" k="16" />
<hkern u1="&#x174;" g2="hyphen.case" k="9" />
<hkern u1="&#x174;" g2="guilsinglleft.case" k="22" />
<hkern u1="&#x174;" g2="eight.plf" k="11" />
<hkern u1="&#x174;" g2="six.plf" k="12" />
<hkern u1="&#x174;" g2="four.plf" k="22" />
<hkern u1="&#x174;" g2="three.plf" k="10" />
<hkern u1="&#x174;" g2="two.plf" k="10" />
<hkern u1="&#x174;" g2="zero.plf" k="11" />
<hkern u1="&#x174;" u2="n" k="26" />
<hkern u1="&#x174;" u2="&#x203a;" k="14" />
<hkern u1="&#x174;" u2="&#x2039;" k="32" />
<hkern u1="&#x174;" u2="&#xdf;" k="13" />
<hkern u1="&#x174;" u2="&#xc6;" k="43" />
<hkern u1="&#x174;" u2="&#xf0;" k="40" />
<hkern u1="&#x174;" u2="&#x131;" k="26" />
<hkern u1="&#x174;" u2="&#xae;" k="14" />
<hkern u1="&#x174;" u2="s" k="35" />
<hkern u1="&#x174;" u2="&#x40;" k="12" />
<hkern u1="&#x174;" u2="&#xa9;" k="14" />
<hkern u1="&#x174;" u2="&#x2f;" k="34" />
<hkern u1="&#x174;" u2="&#x2d;" k="13" />
<hkern u1="&#x174;" u2="&#x26;" k="15" />
<hkern u1="&#x174;" u2="J" k="44" />
<hkern u1="&#x174;" u2="S" k="11" />
<hkern u1="&#x174;" u2="O" k="15" />
<hkern u1="&#x174;" u2="A" k="33" />
<hkern u1="&#x174;" u2="&#x2e;" k="40" />
<hkern u1="&#x174;" u2="z" k="15" />
<hkern u1="&#x174;" u2="u" k="22" />
<hkern u1="&#x174;" u2="o" k="38" />
<hkern u1="&#x174;" u2="a" k="39" />
<hkern u1="&#x174;" u2="&#x20;" k="24" />
<hkern u1="&#x175;" u2="d" k="7" />
<hkern u1="&#x175;" u2="&#x29;" k="25" />
<hkern u1="&#x175;" u2="&#x2039;" k="13" />
<hkern u1="&#x175;" u2="&#xc6;" k="27" />
<hkern u1="&#x175;" u2="&#xf0;" k="10" />
<hkern u1="&#x175;" u2="&#x7d;" k="17" />
<hkern u1="&#x175;" u2="s" k="5" />
<hkern u1="&#x175;" u2="]" k="17" />
<hkern u1="&#x175;" u2="&#x2f;" k="17" />
<hkern u1="&#x175;" u2="X" k="23" />
<hkern u1="&#x175;" u2="Y" k="27" />
<hkern u1="&#x175;" u2="Z" k="10" />
<hkern u1="&#x175;" u2="J" k="34" />
<hkern u1="&#x175;" u2="T" k="49" />
<hkern u1="&#x175;" u2="A" k="19" />
<hkern u1="&#x175;" u2="&#x2e;" k="24" />
<hkern u1="&#x175;" u2="o" k="7" />
<hkern u1="&#x175;" u2="a" k="6" />
<hkern u1="&#x175;" u2="&#x20;" k="20" />
<hkern u1="&#x177;" u2="d" k="11" />
<hkern u1="&#x177;" u2="&#x29;" k="25" />
<hkern u1="&#x177;" u2="&#x2039;" k="19" />
<hkern u1="&#x177;" u2="&#xc6;" k="34" />
<hkern u1="&#x177;" u2="&#xf0;" k="15" />
<hkern u1="&#x177;" u2="&#x7d;" k="16" />
<hkern u1="&#x177;" u2="s" k="8" />
<hkern u1="&#x177;" u2="]" k="16" />
<hkern u1="&#x177;" u2="&#x2f;" k="27" />
<hkern u1="&#x177;" u2="X" k="27" />
<hkern u1="&#x177;" u2="Y" k="24" />
<hkern u1="&#x177;" u2="Z" k="12" />
<hkern u1="&#x177;" u2="J" k="59" />
<hkern u1="&#x177;" u2="T" k="46" />
<hkern u1="&#x177;" u2="A" k="26" />
<hkern u1="&#x177;" u2="&#x2e;" k="34" />
<hkern u1="&#x177;" u2="o" k="11" />
<hkern u1="&#x177;" u2="a" k="10" />
<hkern u1="&#x177;" u2="&#x20;" k="22" />
<hkern u1="&#x17a;" u2="d" k="5" />
<hkern u1="&#x17a;" u2="&#x29;" k="19" />
<hkern u1="&#x17a;" u2="&#x2039;" k="20" />
<hkern u1="&#x17a;" u2="&#x2122;" k="17" />
<hkern u1="&#x17a;" u2="&#xf0;" k="8" />
<hkern u1="&#x17a;" u2="&#x7d;" k="22" />
<hkern u1="&#x17a;" u2="]" k="22" />
<hkern u1="&#x17a;" u2="&#x2d;" k="13" />
<hkern u1="&#x17a;" u2="U" k="5" />
<hkern u1="&#x17a;" u2="Y" k="46" />
<hkern u1="&#x17a;" u2="W" k="12" />
<hkern u1="&#x17a;" u2="V" k="22" />
<hkern u1="&#x17a;" u2="T" k="73" />
<hkern u1="&#x17a;" u2="o" k="5" />
<hkern u1="&#x17c;" u2="d" k="5" />
<hkern u1="&#x17c;" u2="&#x29;" k="19" />
<hkern u1="&#x17c;" u2="&#x2039;" k="20" />
<hkern u1="&#x17c;" u2="&#x2122;" k="17" />
<hkern u1="&#x17c;" u2="&#xf0;" k="8" />
<hkern u1="&#x17c;" u2="&#x7d;" k="22" />
<hkern u1="&#x17c;" u2="]" k="22" />
<hkern u1="&#x17c;" u2="&#x2d;" k="13" />
<hkern u1="&#x17c;" u2="U" k="5" />
<hkern u1="&#x17c;" u2="Y" k="46" />
<hkern u1="&#x17c;" u2="W" k="12" />
<hkern u1="&#x17c;" u2="V" k="22" />
<hkern u1="&#x17c;" u2="T" k="73" />
<hkern u1="&#x17c;" u2="o" k="5" />
<hkern u1="&#x1e81;" u2="d" k="7" />
<hkern u1="&#x1e81;" u2="&#x29;" k="25" />
<hkern u1="&#x1e81;" u2="&#x2039;" k="13" />
<hkern u1="&#x1e81;" u2="&#xc6;" k="27" />
<hkern u1="&#x1e81;" u2="&#xf0;" k="10" />
<hkern u1="&#x1e81;" u2="&#x7d;" k="17" />
<hkern u1="&#x1e81;" u2="s" k="5" />
<hkern u1="&#x1e81;" u2="]" k="17" />
<hkern u1="&#x1e81;" u2="&#x2f;" k="17" />
<hkern u1="&#x1e81;" u2="X" k="23" />
<hkern u1="&#x1e81;" u2="Y" k="27" />
<hkern u1="&#x1e81;" u2="Z" k="10" />
<hkern u1="&#x1e81;" u2="J" k="34" />
<hkern u1="&#x1e81;" u2="T" k="49" />
<hkern u1="&#x1e81;" u2="A" k="19" />
<hkern u1="&#x1e81;" u2="&#x2e;" k="24" />
<hkern u1="&#x1e81;" u2="o" k="7" />
<hkern u1="&#x1e81;" u2="a" k="6" />
<hkern u1="&#x1e81;" u2="&#x20;" k="20" />
<hkern u1="&#x1e83;" u2="d" k="7" />
<hkern u1="&#x1e83;" u2="&#x29;" k="25" />
<hkern u1="&#x1e83;" u2="&#x2039;" k="13" />
<hkern u1="&#x1e83;" u2="&#xc6;" k="27" />
<hkern u1="&#x1e83;" u2="&#xf0;" k="10" />
<hkern u1="&#x1e83;" u2="&#x7d;" k="17" />
<hkern u1="&#x1e83;" u2="s" k="5" />
<hkern u1="&#x1e83;" u2="]" k="17" />
<hkern u1="&#x1e83;" u2="&#x2f;" k="17" />
<hkern u1="&#x1e83;" u2="X" k="23" />
<hkern u1="&#x1e83;" u2="Y" k="27" />
<hkern u1="&#x1e83;" u2="Z" k="10" />
<hkern u1="&#x1e83;" u2="J" k="34" />
<hkern u1="&#x1e83;" u2="T" k="49" />
<hkern u1="&#x1e83;" u2="A" k="19" />
<hkern u1="&#x1e83;" u2="&#x2e;" k="24" />
<hkern u1="&#x1e83;" u2="o" k="7" />
<hkern u1="&#x1e83;" u2="a" k="6" />
<hkern u1="&#x1e83;" u2="&#x20;" k="20" />
<hkern u1="&#x1e85;" u2="d" k="7" />
<hkern u1="&#x1e85;" u2="&#x29;" k="25" />
<hkern u1="&#x1e85;" u2="&#x2039;" k="13" />
<hkern u1="&#x1e85;" u2="&#xc6;" k="27" />
<hkern u1="&#x1e85;" u2="&#xf0;" k="10" />
<hkern u1="&#x1e85;" u2="&#x7d;" k="17" />
<hkern u1="&#x1e85;" u2="s" k="5" />
<hkern u1="&#x1e85;" u2="]" k="17" />
<hkern u1="&#x1e85;" u2="&#x2f;" k="17" />
<hkern u1="&#x1e85;" u2="X" k="23" />
<hkern u1="&#x1e85;" u2="Y" k="27" />
<hkern u1="&#x1e85;" u2="Z" k="10" />
<hkern u1="&#x1e85;" u2="J" k="34" />
<hkern u1="&#x1e85;" u2="T" k="49" />
<hkern u1="&#x1e85;" u2="A" k="19" />
<hkern u1="&#x1e85;" u2="&#x2e;" k="24" />
<hkern u1="&#x1e85;" u2="o" k="7" />
<hkern u1="&#x1e85;" u2="a" k="6" />
<hkern u1="&#x1e85;" u2="&#x20;" k="20" />
<hkern u1="&#x1ef3;" u2="d" k="11" />
<hkern u1="&#x1ef3;" u2="&#x29;" k="25" />
<hkern u1="&#x1ef3;" u2="&#x2039;" k="19" />
<hkern u1="&#x1ef3;" u2="&#xc6;" k="34" />
<hkern u1="&#x1ef3;" u2="&#xf0;" k="15" />
<hkern u1="&#x1ef3;" u2="&#x7d;" k="16" />
<hkern u1="&#x1ef3;" u2="s" k="8" />
<hkern u1="&#x1ef3;" u2="]" k="16" />
<hkern u1="&#x1ef3;" u2="&#x2f;" k="27" />
<hkern u1="&#x1ef3;" u2="X" k="27" />
<hkern u1="&#x1ef3;" u2="Y" k="24" />
<hkern u1="&#x1ef3;" u2="Z" k="12" />
<hkern u1="&#x1ef3;" u2="J" k="59" />
<hkern u1="&#x1ef3;" u2="T" k="46" />
<hkern u1="&#x1ef3;" u2="A" k="26" />
<hkern u1="&#x1ef3;" u2="&#x2e;" k="34" />
<hkern u1="&#x1ef3;" u2="o" k="11" />
<hkern u1="&#x1ef3;" u2="a" k="10" />
<hkern u1="&#x1ef3;" u2="&#x20;" k="22" />
<hkern u1="&#x1e80;" u2="d" k="37" />
<hkern u1="&#x1e80;" u2="&#x159;" k="16" />
<hkern u1="&#x1e80;" g2="hyphen.case" k="9" />
<hkern u1="&#x1e80;" g2="guilsinglleft.case" k="22" />
<hkern u1="&#x1e80;" g2="eight.plf" k="11" />
<hkern u1="&#x1e80;" g2="six.plf" k="12" />
<hkern u1="&#x1e80;" g2="four.plf" k="22" />
<hkern u1="&#x1e80;" g2="three.plf" k="10" />
<hkern u1="&#x1e80;" g2="two.plf" k="10" />
<hkern u1="&#x1e80;" g2="zero.plf" k="11" />
<hkern u1="&#x1e80;" u2="n" k="26" />
<hkern u1="&#x1e80;" u2="&#x203a;" k="14" />
<hkern u1="&#x1e80;" u2="&#x2039;" k="32" />
<hkern u1="&#x1e80;" u2="&#xdf;" k="13" />
<hkern u1="&#x1e80;" u2="&#xc6;" k="43" />
<hkern u1="&#x1e80;" u2="&#xf0;" k="40" />
<hkern u1="&#x1e80;" u2="&#x131;" k="26" />
<hkern u1="&#x1e80;" u2="&#xae;" k="14" />
<hkern u1="&#x1e80;" u2="s" k="35" />
<hkern u1="&#x1e80;" u2="&#x40;" k="12" />
<hkern u1="&#x1e80;" u2="&#xa9;" k="14" />
<hkern u1="&#x1e80;" u2="&#x2f;" k="34" />
<hkern u1="&#x1e80;" u2="&#x2d;" k="13" />
<hkern u1="&#x1e80;" u2="&#x26;" k="15" />
<hkern u1="&#x1e80;" u2="J" k="44" />
<hkern u1="&#x1e80;" u2="S" k="11" />
<hkern u1="&#x1e80;" u2="O" k="15" />
<hkern u1="&#x1e80;" u2="A" k="33" />
<hkern u1="&#x1e80;" u2="&#x2e;" k="40" />
<hkern u1="&#x1e80;" u2="z" k="15" />
<hkern u1="&#x1e80;" u2="u" k="22" />
<hkern u1="&#x1e80;" u2="o" k="38" />
<hkern u1="&#x1e80;" u2="a" k="39" />
<hkern u1="&#x1e80;" u2="&#x20;" k="24" />
<hkern u1="&#x1e82;" u2="d" k="37" />
<hkern u1="&#x1e82;" u2="&#x159;" k="16" />
<hkern u1="&#x1e82;" g2="hyphen.case" k="9" />
<hkern u1="&#x1e82;" g2="guilsinglleft.case" k="22" />
<hkern u1="&#x1e82;" g2="eight.plf" k="11" />
<hkern u1="&#x1e82;" g2="six.plf" k="12" />
<hkern u1="&#x1e82;" g2="four.plf" k="22" />
<hkern u1="&#x1e82;" g2="three.plf" k="10" />
<hkern u1="&#x1e82;" g2="two.plf" k="10" />
<hkern u1="&#x1e82;" g2="zero.plf" k="11" />
<hkern u1="&#x1e82;" u2="n" k="26" />
<hkern u1="&#x1e82;" u2="&#x203a;" k="14" />
<hkern u1="&#x1e82;" u2="&#x2039;" k="32" />
<hkern u1="&#x1e82;" u2="&#xdf;" k="13" />
<hkern u1="&#x1e82;" u2="&#xc6;" k="43" />
<hkern u1="&#x1e82;" u2="&#xf0;" k="40" />
<hkern u1="&#x1e82;" u2="&#x131;" k="26" />
<hkern u1="&#x1e82;" u2="&#xae;" k="14" />
<hkern u1="&#x1e82;" u2="s" k="35" />
<hkern u1="&#x1e82;" u2="&#x40;" k="12" />
<hkern u1="&#x1e82;" u2="&#xa9;" k="14" />
<hkern u1="&#x1e82;" u2="&#x2f;" k="34" />
<hkern u1="&#x1e82;" u2="&#x2d;" k="13" />
<hkern u1="&#x1e82;" u2="&#x26;" k="15" />
<hkern u1="&#x1e82;" u2="J" k="44" />
<hkern u1="&#x1e82;" u2="S" k="11" />
<hkern u1="&#x1e82;" u2="O" k="15" />
<hkern u1="&#x1e82;" u2="A" k="33" />
<hkern u1="&#x1e82;" u2="&#x2e;" k="40" />
<hkern u1="&#x1e82;" u2="z" k="15" />
<hkern u1="&#x1e82;" u2="u" k="22" />
<hkern u1="&#x1e82;" u2="o" k="38" />
<hkern u1="&#x1e82;" u2="a" k="39" />
<hkern u1="&#x1e82;" u2="&#x20;" k="24" />
<hkern u1="&#x1e84;" u2="d" k="37" />
<hkern u1="&#x1e84;" u2="&#x159;" k="16" />
<hkern u1="&#x1e84;" g2="hyphen.case" k="9" />
<hkern u1="&#x1e84;" g2="guilsinglleft.case" k="22" />
<hkern u1="&#x1e84;" g2="eight.plf" k="11" />
<hkern u1="&#x1e84;" g2="six.plf" k="12" />
<hkern u1="&#x1e84;" g2="four.plf" k="22" />
<hkern u1="&#x1e84;" g2="three.plf" k="10" />
<hkern u1="&#x1e84;" g2="two.plf" k="10" />
<hkern u1="&#x1e84;" g2="zero.plf" k="11" />
<hkern u1="&#x1e84;" u2="n" k="26" />
<hkern u1="&#x1e84;" u2="&#x203a;" k="14" />
<hkern u1="&#x1e84;" u2="&#x2039;" k="32" />
<hkern u1="&#x1e84;" u2="&#xdf;" k="13" />
<hkern u1="&#x1e84;" u2="&#xc6;" k="43" />
<hkern u1="&#x1e84;" u2="&#xf0;" k="40" />
<hkern u1="&#x1e84;" u2="&#x131;" k="26" />
<hkern u1="&#x1e84;" u2="&#xae;" k="14" />
<hkern u1="&#x1e84;" u2="s" k="35" />
<hkern u1="&#x1e84;" u2="&#x40;" k="12" />
<hkern u1="&#x1e84;" u2="&#xa9;" k="14" />
<hkern u1="&#x1e84;" u2="&#x2f;" k="34" />
<hkern u1="&#x1e84;" u2="&#x2d;" k="13" />
<hkern u1="&#x1e84;" u2="&#x26;" k="15" />
<hkern u1="&#x1e84;" u2="J" k="44" />
<hkern u1="&#x1e84;" u2="S" k="11" />
<hkern u1="&#x1e84;" u2="O" k="15" />
<hkern u1="&#x1e84;" u2="A" k="33" />
<hkern u1="&#x1e84;" u2="&#x2e;" k="40" />
<hkern u1="&#x1e84;" u2="z" k="15" />
<hkern u1="&#x1e84;" u2="u" k="22" />
<hkern u1="&#x1e84;" u2="o" k="38" />
<hkern u1="&#x1e84;" u2="a" k="39" />
<hkern u1="&#x1e84;" u2="&#x20;" k="24" />
<hkern u1="&#x13a;" u2="&#xb7;" k="57" />
<hkern u1="&#x14a;" u2="d" k="6" />
<hkern u1="&#x14a;" u2="&#xf0;" k="7" />
<hkern u1="&#x14a;" u2="o" k="6" />
<hkern u1="&#x14b;" u2="&#x29;" k="28" />
<hkern u1="&#x14b;" u2="&#x2122;" k="21" />
<hkern u1="&#x14b;" u2="&#x7d;" k="33" />
<hkern u1="&#x14b;" u2="&#x2019;" k="8" />
<hkern u1="&#x14b;" u2="\" k="30" />
<hkern u1="&#x14b;" u2="]" k="32" />
<hkern u1="&#x14b;" u2="U" k="6" />
<hkern u1="&#x14b;" u2="Y" k="63" />
<hkern u1="&#x14b;" u2="Z" k="5" />
<hkern u1="&#x14b;" u2="W" k="31" />
<hkern u1="&#x14b;" u2="V" k="43" />
<hkern u1="&#x14b;" u2="T" k="81" />
<hkern u1="&#x14b;" u2="y" k="6" />
<hkern u1="&#x14b;" u2="v" k="6" />
<hkern u1="&#x165;" u2="l" k="-6" />
<hkern u1="&#x165;" u2="d" k="17" />
<hkern u1="&#x165;" u2="b" k="-7" />
<hkern u1="&#x165;" g2="idotaccent" k="-9" />
<hkern u1="&#x165;" u2="&#x105;" k="5" />
<hkern u1="&#x165;" u2="&#x12f;" k="-9" />
<hkern u1="&#x165;" u2="&#x119;" k="20" />
<hkern u1="&#x165;" u2="&#x1fb;" k="5" />
<hkern u1="&#x165;" u2="&#x127;" k="-7" />
<hkern u1="&#x165;" u2="&#x13c;" k="-6" />
<hkern u1="&#x165;" u2="&#x137;" k="-7" />
<hkern u1="&#x165;" u2="&#x140;" k="-6" />
<hkern u1="&#x165;" u2="&#x13e;" k="-6" />
<hkern u1="&#x165;" u2="&#x111;" k="17" />
<hkern u1="&#x165;" u2="&#x1fd;" k="5" />
<hkern u1="&#x165;" u2="&#x1ff;" k="20" />
<hkern u1="&#x165;" u2="&#x13a;" k="-6" />
<hkern u1="&#x165;" u2="&#x151;" k="20" />
<hkern u1="&#x165;" u2="&#x135;" k="-9" />
<hkern u1="&#x165;" u2="&#x133;" k="-9" />
<hkern u1="&#x165;" u2="&#x129;" k="-9" />
<hkern u1="&#x165;" u2="&#x123;" k="17" />
<hkern u1="&#x165;" u2="&#x125;" k="-7" />
<hkern u1="&#x165;" u2="&#x121;" k="17" />
<hkern u1="&#x165;" u2="&#x11d;" k="17" />
<hkern u1="&#x165;" u2="&#x11b;" k="20" />
<hkern u1="&#x165;" u2="&#x117;" k="20" />
<hkern u1="&#x165;" u2="&#x14f;" k="20" />
<hkern u1="&#x165;" u2="&#x12d;" k="-9" />
<hkern u1="&#x165;" u2="&#x11f;" k="17" />
<hkern u1="&#x165;" u2="&#x115;" k="20" />
<hkern u1="&#x165;" u2="&#x14d;" k="20" />
<hkern u1="&#x165;" u2="&#x12b;" k="-9" />
<hkern u1="&#x165;" u2="&#x113;" k="20" />
<hkern u1="&#x165;" u2="&#x10f;" k="17" />
<hkern u1="&#x165;" u2="&#x10d;" k="20" />
<hkern u1="&#x165;" u2="&#x10b;" k="20" />
<hkern u1="&#x165;" u2="&#x109;" k="20" />
<hkern u1="&#x165;" u2="&#x107;" k="20" />
<hkern u1="&#x165;" u2="&#x103;" k="5" />
<hkern u1="&#x165;" u2="&#x101;" k="5" />
<hkern u1="&#x165;" u2="&#xad;" k="41" />
<hkern u1="&#x165;" u2="&#xf5;" k="20" />
<hkern u1="&#x165;" u2="&#xe3;" k="5" />
<hkern u1="&#x165;" u2="i" k="-9" />
<hkern u1="&#x165;" u2="&#x2039;" k="43" />
<hkern u1="&#x165;" u2="&#x153;" k="20" />
<hkern u1="&#x165;" u2="&#xe6;" k="5" />
<hkern u1="&#x165;" u2="&#xf8;" k="20" />
<hkern u1="&#x165;" u2="&#x142;" k="-6" />
<hkern u1="&#x165;" u2="&#xfe;" k="-7" />
<hkern u1="&#x165;" u2="&#xe5;" k="5" />
<hkern u1="&#x165;" u2="&#x131;" k="-9" />
<hkern u1="&#x165;" u2="g" k="17" />
<hkern u1="&#x165;" u2="&#xab;" k="43" />
<hkern u1="&#x165;" u2="&#x201e;" k="24" />
<hkern u1="&#x165;" u2="&#x201a;" k="24" />
<hkern u1="&#x165;" u2="&#xf6;" k="20" />
<hkern u1="&#x165;" u2="&#xf4;" k="20" />
<hkern u1="&#x165;" u2="&#xf2;" k="20" />
<hkern u1="&#x165;" u2="&#xf3;" k="20" />
<hkern u1="&#x165;" u2="&#xef;" k="-9" />
<hkern u1="&#x165;" u2="&#xee;" k="-9" />
<hkern u1="&#x165;" u2="&#xec;" k="-9" />
<hkern u1="&#x165;" u2="&#xeb;" k="20" />
<hkern u1="&#x165;" u2="&#xea;" k="20" />
<hkern u1="&#x165;" u2="&#xe8;" k="20" />
<hkern u1="&#x165;" u2="&#xe9;" k="20" />
<hkern u1="&#x165;" u2="&#xe4;" k="5" />
<hkern u1="&#x165;" u2="&#xe2;" k="5" />
<hkern u1="&#x165;" u2="&#xe0;" k="5" />
<hkern u1="&#x165;" u2="&#xe1;" k="5" />
<hkern u1="&#x165;" u2="&#x2014;" k="41" />
<hkern u1="&#x165;" u2="&#x2013;" k="41" />
<hkern u1="&#x165;" u2="&#x2026;" k="24" />
<hkern u1="&#x165;" u2="&#xe7;" k="20" />
<hkern u1="&#x165;" u2="&#x2d;" k="41" />
<hkern u1="&#x165;" u2="&#x2c;" k="24" />
<hkern u1="&#x165;" u2="&#x2e;" k="24" />
<hkern u1="&#x165;" u2="k" k="-7" />
<hkern u1="&#x165;" u2="c" k="20" />
<hkern u1="&#x165;" u2="e" k="20" />
<hkern u1="&#x165;" u2="o" k="20" />
<hkern u1="&#x165;" u2="a" k="5" />
<hkern u1="&#x165;" u2="q" k="17" />
<hkern u1="&#x165;" u2="h" k="-7" />
<hkern u1="&#x165;" u2="&#x2a;" k="-13" />
<hkern u1="&#x165;" u2="&#x2122;" k="-15" />
<hkern u1="&#x165;" u2="&#xdf;" k="-1" />
<hkern u1="&#x165;" u2="&#xf0;" k="39" />
<hkern u1="&#x165;" u2="&#x7d;" k="-10" />
<hkern u1="&#x165;" u2="&#xed;" k="-22" />
<hkern u1="&#x165;" u2="\" k="-32" />
<hkern u1="&#x165;" u2="]" k="-10" />
<hkern u1="&#x165;" u2="&#x2f;" k="17" />
<hkern u1="&#x165;" u2="j" k="-1" />
<hkern u1="&#x165;" u2="&#x20;" k="32" />
<hkern u1="&#x1fd;" u2="&#x29;" k="33" />
<hkern u1="&#x1fd;" u2="f" k="4" />
<hkern u1="&#x1fd;" u2="&#x2122;" k="21" />
<hkern u1="&#x1fd;" u2="&#x7d;" k="33" />
<hkern u1="&#x1fd;" u2="&#x2018;" k="8" />
<hkern u1="&#x1fd;" u2="&#x2019;" k="11" />
<hkern u1="&#x1fd;" u2="\" k="31" />
<hkern u1="&#x1fd;" u2="]" k="33" />
<hkern u1="&#x1fd;" u2="X" k="11" />
<hkern u1="&#x1fd;" u2="Y" k="84" />
<hkern u1="&#x1fd;" u2="Z" k="7" />
<hkern u1="&#x1fd;" u2="W" k="37" />
<hkern u1="&#x1fd;" u2="V" k="44" />
<hkern u1="&#x1fd;" u2="T" k="75" />
<hkern u1="&#x1fd;" u2="A" k="8" />
<hkern u1="&#x1fd;" u2="x" k="15" />
<hkern u1="&#x1fd;" u2="y" k="11" />
<hkern u1="&#x1fd;" u2="w" k="6" />
<hkern u1="&#x1fd;" u2="v" k="9" />
<hkern u1="&#x110;" u2="&#x166;" k="8" />
<hkern u1="&#x110;" g2="parenright.case" k="29" />
<hkern u1="&#x110;" g2="braceright.case" k="31" />
<hkern u1="&#x110;" g2="bracketright.case" k="31" />
<hkern u1="&#x110;" u2="&#x29;" k="27" />
<hkern u1="&#x110;" g2="seven.plf" k="12" />
<hkern u1="&#x110;" u2="&#xc6;" k="12" />
<hkern u1="&#x110;" u2="&#x7d;" k="25" />
<hkern u1="&#x110;" u2="]" k="25" />
<hkern u1="&#x110;" u2="X" k="26" />
<hkern u1="&#x110;" u2="Y" k="30" />
<hkern u1="&#x110;" u2="Z" k="8" />
<hkern u1="&#x110;" u2="W" k="15" />
<hkern u1="&#x110;" u2="V" k="21" />
<hkern u1="&#x110;" u2="J" k="17" />
<hkern u1="&#x110;" u2="T" k="17" />
<hkern u1="&#x110;" u2="A" k="17" />
<hkern u1="&#x110;" u2="x" k="13" />
<hkern u1="&#x110;" u2="z" k="5" />
<hkern u1="&#x13d;" u2="&#x166;" k="41" />
<hkern u1="&#x149;" u2="&#x29;" k="28" />
<hkern u1="&#x149;" u2="&#x2122;" k="21" />
<hkern u1="&#x149;" u2="&#x7d;" k="33" />
<hkern u1="&#x149;" u2="&#x2019;" k="8" />
<hkern u1="&#x149;" u2="\" k="30" />
<hkern u1="&#x149;" u2="]" k="32" />
<hkern u1="&#x149;" u2="U" k="6" />
<hkern u1="&#x149;" u2="Y" k="63" />
<hkern u1="&#x149;" u2="Z" k="5" />
<hkern u1="&#x149;" u2="W" k="31" />
<hkern u1="&#x149;" u2="V" k="43" />
<hkern u1="&#x149;" u2="T" k="81" />
<hkern u1="&#x149;" u2="y" k="6" />
<hkern u1="&#x149;" u2="v" k="6" />
<hkern u1="&#x122;" u2="&#x29;" k="14" />
<hkern u1="&#x122;" u2="&#x7d;" k="18" />
<hkern u1="&#x122;" u2="]" k="17" />
<hkern u1="&#x122;" u2="Y" k="25" />
<hkern u1="&#x122;" u2="W" k="14" />
<hkern u1="&#x122;" u2="V" k="20" />
<hkern u1="&#x122;" u2="T" k="10" />
<hkern u1="&#x137;" u2="d" k="24" />
<hkern u1="&#x137;" u2="&#x2039;" k="32" />
<hkern u1="&#x137;" u2="&#x2122;" k="15" />
<hkern u1="&#x137;" u2="&#xf0;" k="29" />
<hkern u1="&#x137;" u2="&#x7d;" k="17" />
<hkern u1="&#x137;" u2="s" k="10" />
<hkern u1="&#x137;" u2="]" k="17" />
<hkern u1="&#x137;" u2="&#x2d;" k="24" />
<hkern u1="&#x137;" u2="Y" k="32" />
<hkern u1="&#x137;" u2="V" k="11" />
<hkern u1="&#x137;" u2="T" k="60" />
<hkern u1="&#x137;" u2="S" k="6" />
<hkern u1="&#x137;" u2="O" k="12" />
<hkern u1="&#x137;" u2="o" k="25" />
<hkern u1="&#x137;" u2="a" k="7" />
<hkern u1="&#x13c;" u2="&#xb7;" k="57" />
<hkern u1="&#x145;" u2="d" k="6" />
<hkern u1="&#x145;" u2="&#xf0;" k="7" />
<hkern u1="&#x145;" u2="o" k="6" />
<hkern u1="&#x146;" u2="&#x29;" k="28" />
<hkern u1="&#x146;" u2="&#x2122;" k="21" />
<hkern u1="&#x146;" u2="&#x7d;" k="33" />
<hkern u1="&#x146;" u2="&#x2019;" k="8" />
<hkern u1="&#x146;" u2="\" k="30" />
<hkern u1="&#x146;" u2="]" k="32" />
<hkern u1="&#x146;" u2="U" k="6" />
<hkern u1="&#x146;" u2="Y" k="63" />
<hkern u1="&#x146;" u2="Z" k="5" />
<hkern u1="&#x146;" u2="W" k="31" />
<hkern u1="&#x146;" u2="V" k="43" />
<hkern u1="&#x146;" u2="T" k="81" />
<hkern u1="&#x146;" u2="y" k="6" />
<hkern u1="&#x146;" u2="v" k="6" />
<hkern u1="&#x156;" u2="d" k="12" />
<hkern u1="&#x156;" g2="four.plf" k="11" />
<hkern u1="&#x156;" u2="&#x2039;" k="17" />
<hkern u1="&#x156;" u2="&#xf0;" k="18" />
<hkern u1="&#x156;" u2="&#x7d;" k="11" />
<hkern u1="&#x156;" u2="s" k="6" />
<hkern u1="&#x156;" u2="]" k="11" />
<hkern u1="&#x156;" u2="Y" k="15" />
<hkern u1="&#x156;" u2="W" k="10" />
<hkern u1="&#x156;" u2="V" k="14" />
<hkern u1="&#x156;" u2="A" k="5" />
<hkern u1="&#x156;" u2="u" k="6" />
<hkern u1="&#x156;" u2="o" k="13" />
<hkern u1="&#x156;" u2="a" k="7" />
<hkern u1="&#x157;" u2="d" k="8" />
<hkern u1="&#x157;" u2="&#x29;" k="23" />
<hkern u1="&#x157;" u2="&#x2039;" k="22" />
<hkern u1="&#x157;" u2="&#xc6;" k="44" />
<hkern u1="&#x157;" u2="&#xf0;" k="21" />
<hkern u1="&#x157;" u2="&#x7d;" k="15" />
<hkern u1="&#x157;" u2="]" k="15" />
<hkern u1="&#x157;" u2="&#x2f;" k="28" />
<hkern u1="&#x157;" u2="&#x2d;" k="27" />
<hkern u1="&#x157;" u2="X" k="28" />
<hkern u1="&#x157;" u2="Y" k="21" />
<hkern u1="&#x157;" u2="Z" k="11" />
<hkern u1="&#x157;" u2="J" k="56" />
<hkern u1="&#x157;" u2="T" k="46" />
<hkern u1="&#x157;" u2="A" k="39" />
<hkern u1="&#x157;" u2="&#x2e;" k="34" />
<hkern u1="&#x157;" u2="o" k="9" />
<hkern u1="&#x157;" u2="a" k="4" />
<hkern u1="&#x157;" u2="&#x20;" k="20" />
<hkern u1="&#x15e;" g2="parenright.case" k="21" />
<hkern u1="&#x15e;" g2="braceright.case" k="23" />
<hkern u1="&#x15e;" g2="bracketright.case" k="23" />
<hkern u1="&#x15e;" u2="&#x29;" k="19" />
<hkern u1="&#x15e;" u2="&#x7d;" k="17" />
<hkern u1="&#x15e;" u2="]" k="17" />
<hkern u1="&#x15e;" u2="X" k="6" />
<hkern u1="&#x15e;" u2="Y" k="21" />
<hkern u1="&#x15e;" u2="W" k="12" />
<hkern u1="&#x15e;" u2="V" k="17" />
<hkern u1="&#x15e;" u2="T" k="5" />
<hkern u1="&#x15e;" u2="A" k="9" />
<hkern u1="&#x15e;" u2="x" k="9" />
<hkern u1="&#x15f;" u2="&#x29;" k="32" />
<hkern u1="&#x15f;" u2="&#x2122;" k="23" />
<hkern u1="&#x15f;" u2="&#x7d;" k="32" />
<hkern u1="&#x15f;" u2="&#x2019;" k="9" />
<hkern u1="&#x15f;" u2="\" k="31" />
<hkern u1="&#x15f;" u2="]" k="32" />
<hkern u1="&#x15f;" u2="X" k="5" />
<hkern u1="&#x15f;" u2="U" k="6" />
<hkern u1="&#x15f;" u2="Y" k="81" />
<hkern u1="&#x15f;" u2="W" k="39" />
<hkern u1="&#x15f;" u2="V" k="44" />
<hkern u1="&#x15f;" u2="T" k="74" />
<hkern u1="&#x15f;" u2="A" k="6" />
<hkern u1="&#x15f;" u2="x" k="5" />
<hkern u1="&#x15f;" u2="y" k="11" />
<hkern u1="&#x15f;" u2="w" k="6" />
<hkern u1="&#x15f;" u2="v" k="9" />
<hkern u1="&#x163;" u2="&#x2039;" k="12" />
<hkern u1="&#x163;" u2="&#xf0;" k="4" />
<hkern u1="&#x163;" u2="&#x7d;" k="13" />
<hkern u1="&#x163;" u2="]" k="13" />
<hkern u1="&#x163;" u2="Y" k="28" />
<hkern u1="&#x163;" u2="V" k="8" />
<hkern u1="&#x163;" u2="T" k="47" />
<hkern u1="&#x162;" u2="d" k="80" />
<hkern u1="&#x162;" u2="&#x127;" k="-13" />
<hkern u1="&#x162;" u2="&#x159;" k="78" />
<hkern u1="&#x162;" u2="&#x129;" k="-28" />
<hkern u1="&#x162;" u2="&#x12d;" k="-9" />
<hkern u1="&#x162;" u2="&#x12b;" k="-20" />
<hkern u1="&#x162;" g2="hyphen.case" k="41" />
<hkern u1="&#x162;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x162;" g2="guilsinglright.case" k="36" />
<hkern u1="&#x162;" g2="four.plf" k="49" />
<hkern u1="&#x162;" u2="n" k="87" />
<hkern u1="&#x162;" u2="f" k="20" />
<hkern u1="&#x162;" u2="&#x203a;" k="59" />
<hkern u1="&#x162;" u2="&#x2039;" k="66" />
<hkern u1="&#x162;" u2="&#xdf;" k="16" />
<hkern u1="&#x162;" u2="&#xc6;" k="58" />
<hkern u1="&#x162;" u2="&#xf0;" k="56" />
<hkern u1="&#x162;" u2="&#x131;" k="87" />
<hkern u1="&#x162;" u2="&#xae;" k="17" />
<hkern u1="&#x162;" u2="s" k="73" />
<hkern u1="&#x162;" u2="&#xa9;" k="17" />
<hkern u1="&#x162;" u2="&#xef;" k="-7" />
<hkern u1="&#x162;" u2="&#x2f;" k="47" />
<hkern u1="&#x162;" u2="&#x3a;" k="29" />
<hkern u1="&#x162;" u2="&#x2d;" k="42" />
<hkern u1="&#x162;" u2="J" k="55" />
<hkern u1="&#x162;" u2="O" k="16" />
<hkern u1="&#x162;" u2="A" k="53" />
<hkern u1="&#x162;" u2="&#x2e;" k="49" />
<hkern u1="&#x162;" u2="x" k="60" />
<hkern u1="&#x162;" u2="y" k="47" />
<hkern u1="&#x162;" u2="z" k="76" />
<hkern u1="&#x162;" u2="w" k="49" />
<hkern u1="&#x162;" u2="v" k="47" />
<hkern u1="&#x162;" u2="u" k="87" />
<hkern u1="&#x162;" u2="o" k="81" />
<hkern u1="&#x162;" u2="a" k="74" />
<hkern u1="&#x162;" u2="&#x20;" k="26" />
<hkern u1="&#x21b;" u2="&#x2039;" k="12" />
<hkern u1="&#x21b;" u2="&#xf0;" k="4" />
<hkern u1="&#x21b;" u2="&#x7d;" k="13" />
<hkern u1="&#x21b;" u2="]" k="13" />
<hkern u1="&#x21b;" u2="Y" k="28" />
<hkern u1="&#x21b;" u2="V" k="8" />
<hkern u1="&#x21b;" u2="T" k="47" />
<hkern u1="&#x21a;" u2="d" k="80" />
<hkern u1="&#x21a;" u2="&#x127;" k="-13" />
<hkern u1="&#x21a;" u2="&#x159;" k="78" />
<hkern u1="&#x21a;" u2="&#x129;" k="-28" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-9" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-20" />
<hkern u1="&#x21a;" g2="hyphen.case" k="41" />
<hkern u1="&#x21a;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x21a;" g2="guilsinglright.case" k="36" />
<hkern u1="&#x21a;" g2="four.plf" k="49" />
<hkern u1="&#x21a;" u2="n" k="87" />
<hkern u1="&#x21a;" u2="f" k="20" />
<hkern u1="&#x21a;" u2="&#x203a;" k="59" />
<hkern u1="&#x21a;" u2="&#x2039;" k="66" />
<hkern u1="&#x21a;" u2="&#xdf;" k="16" />
<hkern u1="&#x21a;" u2="&#xc6;" k="58" />
<hkern u1="&#x21a;" u2="&#xf0;" k="56" />
<hkern u1="&#x21a;" u2="&#x131;" k="87" />
<hkern u1="&#x21a;" u2="&#xae;" k="17" />
<hkern u1="&#x21a;" u2="s" k="73" />
<hkern u1="&#x21a;" u2="&#xa9;" k="17" />
<hkern u1="&#x21a;" u2="&#xef;" k="-7" />
<hkern u1="&#x21a;" u2="&#x2f;" k="47" />
<hkern u1="&#x21a;" u2="&#x3a;" k="29" />
<hkern u1="&#x21a;" u2="&#x2d;" k="42" />
<hkern u1="&#x21a;" u2="J" k="55" />
<hkern u1="&#x21a;" u2="O" k="16" />
<hkern u1="&#x21a;" u2="A" k="53" />
<hkern u1="&#x21a;" u2="&#x2e;" k="49" />
<hkern u1="&#x21a;" u2="x" k="60" />
<hkern u1="&#x21a;" u2="y" k="47" />
<hkern u1="&#x21a;" u2="z" k="76" />
<hkern u1="&#x21a;" u2="w" k="49" />
<hkern u1="&#x21a;" u2="v" k="47" />
<hkern u1="&#x21a;" u2="u" k="87" />
<hkern u1="&#x21a;" u2="o" k="81" />
<hkern u1="&#x21a;" u2="a" k="74" />
<hkern u1="&#x21a;" u2="&#x20;" k="26" />
<hkern u1="&#x218;" g2="parenright.case" k="21" />
<hkern u1="&#x218;" g2="braceright.case" k="23" />
<hkern u1="&#x218;" g2="bracketright.case" k="23" />
<hkern u1="&#x218;" u2="&#x29;" k="19" />
<hkern u1="&#x218;" u2="&#x7d;" k="17" />
<hkern u1="&#x218;" u2="]" k="17" />
<hkern u1="&#x218;" u2="X" k="6" />
<hkern u1="&#x218;" u2="Y" k="21" />
<hkern u1="&#x218;" u2="W" k="12" />
<hkern u1="&#x218;" u2="V" k="17" />
<hkern u1="&#x218;" u2="T" k="5" />
<hkern u1="&#x218;" u2="A" k="9" />
<hkern u1="&#x218;" u2="x" k="9" />
<hkern u1="&#x219;" u2="&#x29;" k="32" />
<hkern u1="&#x219;" u2="&#x2122;" k="23" />
<hkern u1="&#x219;" u2="&#x7d;" k="32" />
<hkern u1="&#x219;" u2="&#x2019;" k="9" />
<hkern u1="&#x219;" u2="\" k="31" />
<hkern u1="&#x219;" u2="]" k="32" />
<hkern u1="&#x219;" u2="X" k="5" />
<hkern u1="&#x219;" u2="U" k="6" />
<hkern u1="&#x219;" u2="Y" k="81" />
<hkern u1="&#x219;" u2="W" k="39" />
<hkern u1="&#x219;" u2="V" k="44" />
<hkern u1="&#x219;" u2="T" k="74" />
<hkern u1="&#x219;" u2="A" k="6" />
<hkern u1="&#x219;" u2="x" k="5" />
<hkern u1="&#x219;" u2="y" k="11" />
<hkern u1="&#x219;" u2="w" k="6" />
<hkern u1="&#x219;" u2="v" k="9" />
<hkern g1="uni00AD.case" u2="&#x166;" k="12" />
<hkern g1="uni00AD.case" u2="X" k="22" />
<hkern g1="uni00AD.case" u2="Y" k="33" />
<hkern g1="uni00AD.case" u2="Z" k="9" />
<hkern g1="uni00AD.case" u2="W" k="9" />
<hkern g1="uni00AD.case" u2="V" k="16" />
<hkern g1="uni00AD.case" u2="J" k="47" />
<hkern g1="uni00AD.case" u2="T" k="41" />
<hkern g1="uni00AD.case" u2="A" k="11" />
<hkern u1="&#x126;" u2="d" k="6" />
<hkern u1="&#x126;" u2="&#x126;" k="6" />
<hkern u1="&#x126;" u2="&#xf0;" k="7" />
<hkern u1="&#x126;" u2="o" k="6" />
<hkern u1="&#x127;" u2="&#x29;" k="28" />
<hkern u1="&#x127;" u2="&#x2122;" k="21" />
<hkern u1="&#x127;" u2="&#x7d;" k="33" />
<hkern u1="&#x127;" u2="&#x2019;" k="8" />
<hkern u1="&#x127;" u2="\" k="30" />
<hkern u1="&#x127;" u2="]" k="32" />
<hkern u1="&#x127;" u2="U" k="6" />
<hkern u1="&#x127;" u2="Y" k="63" />
<hkern u1="&#x127;" u2="Z" k="5" />
<hkern u1="&#x127;" u2="W" k="31" />
<hkern u1="&#x127;" u2="V" k="43" />
<hkern u1="&#x127;" u2="T" k="81" />
<hkern u1="&#x127;" u2="y" k="6" />
<hkern u1="&#x127;" u2="v" k="6" />
<hkern u1="&#x167;" u2="&#x2039;" k="12" />
<hkern u1="&#x167;" u2="&#xf0;" k="4" />
<hkern u1="&#x167;" u2="&#x7d;" k="13" />
<hkern u1="&#x167;" u2="]" k="13" />
<hkern u1="&#x167;" u2="Y" k="28" />
<hkern u1="&#x167;" u2="V" k="8" />
<hkern u1="&#x167;" u2="T" k="47" />
<hkern u1="&#x166;" u2="&#x105;" k="44" />
<hkern u1="&#x166;" u2="&#x104;" k="45" />
<hkern u1="&#x166;" u2="&#x173;" k="44" />
<hkern u1="&#x166;" u2="&#x119;" k="44" />
<hkern u1="&#x166;" u2="&#x1fa;" k="45" />
<hkern u1="&#x166;" u2="&#x1fb;" k="44" />
<hkern u1="&#x166;" u2="&#x167;" k="8" />
<hkern u1="&#x166;" g2="uni00AD.case" k="12" />
<hkern u1="&#x166;" u2="&#x219;" k="41" />
<hkern u1="&#x166;" u2="&#x21b;" k="8" />
<hkern u1="&#x166;" u2="&#x163;" k="8" />
<hkern u1="&#x166;" u2="&#x15f;" k="41" />
<hkern u1="&#x166;" u2="&#x157;" k="46" />
<hkern u1="&#x166;" u2="&#x146;" k="46" />
<hkern u1="&#x166;" u2="&#x122;" k="8" />
<hkern u1="&#x166;" u2="&#x149;" k="46" />
<hkern u1="&#x166;" u2="&#x111;" k="44" />
<hkern u1="&#x166;" u2="&#x1fd;" k="44" />
<hkern u1="&#x166;" u2="&#x1ff;" k="44" />
<hkern u1="&#x166;" u2="&#x1fe;" k="8" />
<hkern u1="&#x166;" u2="&#x165;" k="8" />
<hkern u1="&#x166;" u2="&#x14b;" k="46" />
<hkern u1="&#x166;" u2="&#x1fc;" k="49" />
<hkern u1="&#x166;" u2="&#x1ef3;" k="21" />
<hkern u1="&#x166;" u2="&#x1e85;" k="21" />
<hkern u1="&#x166;" u2="&#x1e83;" k="21" />
<hkern u1="&#x166;" u2="&#x1e81;" k="21" />
<hkern u1="&#x166;" u2="&#x17c;" k="41" />
<hkern u1="&#x166;" u2="&#x17a;" k="41" />
<hkern u1="&#x166;" u2="&#x177;" k="21" />
<hkern u1="&#x166;" u2="&#x175;" k="21" />
<hkern u1="&#x166;" u2="&#x171;" k="44" />
<hkern u1="&#x166;" u2="&#x16f;" k="44" />
<hkern u1="&#x166;" u2="&#x169;" k="44" />
<hkern u1="&#x166;" u2="&#x15d;" k="41" />
<hkern u1="&#x166;" u2="&#x15b;" k="41" />
<hkern u1="&#x166;" u2="&#x155;" k="46" />
<hkern u1="&#x166;" u2="&#x151;" k="44" />
<hkern u1="&#x166;" u2="&#x150;" k="8" />
<hkern u1="&#x166;" u2="&#x148;" k="46" />
<hkern u1="&#x166;" u2="&#x144;" k="46" />
<hkern u1="&#x166;" u2="&#x123;" k="44" />
<hkern u1="&#x166;" u2="&#x121;" k="44" />
<hkern u1="&#x166;" u2="&#x11d;" k="44" />
<hkern u1="&#x166;" u2="&#x11c;" k="8" />
<hkern u1="&#x166;" u2="&#x11b;" k="44" />
<hkern u1="&#x166;" u2="&#x117;" k="44" />
<hkern u1="&#x166;" u2="&#x16d;" k="44" />
<hkern u1="&#x166;" u2="&#x14f;" k="44" />
<hkern u1="&#x166;" u2="&#x14e;" k="8" />
<hkern u1="&#x166;" u2="&#x11f;" k="44" />
<hkern u1="&#x166;" u2="&#x11e;" k="8" />
<hkern u1="&#x166;" u2="&#x115;" k="44" />
<hkern u1="&#x166;" u2="&#x16b;" k="44" />
<hkern u1="&#x166;" u2="&#x14d;" k="44" />
<hkern u1="&#x166;" u2="&#x14c;" k="8" />
<hkern u1="&#x166;" u2="&#x113;" k="44" />
<hkern u1="&#x166;" u2="&#x10f;" k="44" />
<hkern u1="&#x166;" u2="&#x10d;" k="44" />
<hkern u1="&#x166;" u2="&#x10c;" k="8" />
<hkern u1="&#x166;" u2="&#x10b;" k="44" />
<hkern u1="&#x166;" u2="&#x10a;" k="8" />
<hkern u1="&#x166;" u2="&#x109;" k="44" />
<hkern u1="&#x166;" u2="&#x108;" k="8" />
<hkern u1="&#x166;" u2="&#x107;" k="44" />
<hkern u1="&#x166;" u2="&#x106;" k="8" />
<hkern u1="&#x166;" u2="&#x103;" k="44" />
<hkern u1="&#x166;" u2="&#x102;" k="45" />
<hkern u1="&#x166;" u2="&#x101;" k="44" />
<hkern u1="&#x166;" u2="&#x100;" k="45" />
<hkern u1="&#x166;" u2="&#x120;" k="8" />
<hkern u1="&#x166;" u2="&#xad;" k="12" />
<hkern u1="&#x166;" g2="endash.case" k="12" />
<hkern u1="&#x166;" g2="guillemotleft.case" k="21" />
<hkern u1="&#x166;" g2="guillemotright.case" k="10" />
<hkern u1="&#x166;" u2="&#xd5;" k="8" />
<hkern u1="&#x166;" u2="&#xc3;" k="45" />
<hkern u1="&#x166;" u2="&#xf5;" k="44" />
<hkern u1="&#x166;" u2="&#xf1;" k="46" />
<hkern u1="&#x166;" u2="&#xe3;" k="44" />
<hkern u1="&#x166;" u2="&#x3b;" k="9" />
<hkern u1="&#x166;" u2="&#xbb;" k="26" />
<hkern u1="&#x166;" u2="&#xd8;" k="8" />
<hkern u1="&#x166;" u2="&#x152;" k="8" />
<hkern u1="&#x166;" u2="&#x153;" k="44" />
<hkern u1="&#x166;" u2="&#xe6;" k="44" />
<hkern u1="&#x166;" u2="&#xf8;" k="44" />
<hkern u1="&#x166;" u2="&#xc5;" k="45" />
<hkern u1="&#x166;" u2="&#xe5;" k="44" />
<hkern u1="&#x166;" u2="&#x17e;" k="41" />
<hkern u1="&#x166;" u2="&#x161;" k="41" />
<hkern u1="&#x166;" u2="&#xd6;" k="8" />
<hkern u1="&#x166;" u2="&#xd2;" k="8" />
<hkern u1="&#x166;" u2="&#xd4;" k="8" />
<hkern u1="&#x166;" u2="&#xd3;" k="8" />
<hkern u1="&#x166;" u2="&#xab;" k="33" />
<hkern u1="&#x166;" u2="&#xc7;" k="8" />
<hkern u1="&#x166;" u2="&#xc1;" k="45" />
<hkern u1="&#x166;" u2="&#xc2;" k="45" />
<hkern u1="&#x166;" u2="&#xff;" k="21" />
<hkern u1="&#x166;" u2="&#xc0;" k="45" />
<hkern u1="&#x166;" u2="&#xfc;" k="44" />
<hkern u1="&#x166;" u2="&#xfb;" k="44" />
<hkern u1="&#x166;" u2="&#xf9;" k="44" />
<hkern u1="&#x166;" u2="&#xfa;" k="44" />
<hkern u1="&#x166;" u2="&#xf6;" k="44" />
<hkern u1="&#x166;" u2="&#xf4;" k="44" />
<hkern u1="&#x166;" u2="&#xf2;" k="44" />
<hkern u1="&#x166;" u2="&#xf3;" k="44" />
<hkern u1="&#x166;" u2="&#xeb;" k="44" />
<hkern u1="&#x166;" u2="&#xea;" k="44" />
<hkern u1="&#x166;" u2="&#xe8;" k="44" />
<hkern u1="&#x166;" u2="&#xe9;" k="44" />
<hkern u1="&#x166;" u2="&#xe4;" k="44" />
<hkern u1="&#x166;" u2="&#xe2;" k="44" />
<hkern u1="&#x166;" u2="&#xe0;" k="44" />
<hkern u1="&#x166;" u2="&#xe1;" k="44" />
<hkern u1="&#x166;" u2="&#xc4;" k="45" />
<hkern u1="&#x166;" u2="&#xfd;" k="21" />
<hkern u1="&#x166;" u2="&#x2013;" k="12" />
<hkern u1="&#x166;" u2="&#xe7;" k="44" />
<hkern u1="&#x166;" g2="fl" k="8" />
<hkern u1="&#x166;" g2="fi" k="8" />
<hkern u1="&#x166;" u2="G" k="8" />
<hkern u1="&#x166;" u2="C" k="8" />
<hkern u1="&#x166;" u2="Q" k="8" />
<hkern u1="&#x166;" u2="r" k="46" />
<hkern u1="&#x166;" u2="m" k="46" />
<hkern u1="&#x166;" u2="c" k="44" />
<hkern u1="&#x166;" u2="e" k="44" />
<hkern u1="&#x166;" u2="t" k="8" />
<hkern u1="&#x166;" u2="p" k="46" />
<hkern u1="&#x166;" u2="d" k="80" />
<hkern u1="&#x166;" u2="&#x127;" k="-13" />
<hkern u1="&#x166;" u2="&#x159;" k="78" />
<hkern u1="&#x166;" u2="&#x129;" k="-28" />
<hkern u1="&#x166;" u2="&#x12d;" k="-9" />
<hkern u1="&#x166;" u2="&#x12b;" k="-20" />
<hkern u1="&#x166;" g2="hyphen.case" k="41" />
<hkern u1="&#x166;" g2="emdash.case" k="4" />
<hkern u1="&#x166;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x166;" g2="guilsinglright.case" k="36" />
<hkern u1="&#x166;" g2="four.plf" k="49" />
<hkern u1="&#x166;" u2="n" k="87" />
<hkern u1="&#x166;" u2="f" k="20" />
<hkern u1="&#x166;" u2="&#x203a;" k="59" />
<hkern u1="&#x166;" u2="&#x2039;" k="66" />
<hkern u1="&#x166;" u2="&#xdf;" k="8" />
<hkern u1="&#x166;" u2="&#xc6;" k="58" />
<hkern u1="&#x166;" u2="&#xf0;" k="35" />
<hkern u1="&#x166;" u2="&#x131;" k="87" />
<hkern u1="&#x166;" u2="&#xae;" k="6" />
<hkern u1="&#x166;" u2="g" k="12" />
<hkern u1="&#x166;" u2="s" k="73" />
<hkern u1="&#x166;" u2="&#xa9;" k="6" />
<hkern u1="&#x166;" u2="&#xef;" k="-7" />
<hkern u1="&#x166;" u2="&#x2014;" k="4" />
<hkern u1="&#x166;" u2="&#x2f;" k="39" />
<hkern u1="&#x166;" u2="&#x3a;" k="29" />
<hkern u1="&#x166;" u2="&#x2d;" k="42" />
<hkern u1="&#x166;" u2="J" k="55" />
<hkern u1="&#x166;" u2="O" k="16" />
<hkern u1="&#x166;" u2="A" k="53" />
<hkern u1="&#x166;" u2="&#x2e;" k="49" />
<hkern u1="&#x166;" u2="x" k="29" />
<hkern u1="&#x166;" u2="y" k="47" />
<hkern u1="&#x166;" u2="z" k="76" />
<hkern u1="&#x166;" u2="w" k="49" />
<hkern u1="&#x166;" u2="v" k="20" />
<hkern u1="&#x166;" u2="u" k="87" />
<hkern u1="&#x166;" u2="o" k="81" />
<hkern u1="&#x166;" u2="a" k="74" />
<hkern u1="&#x166;" u2="q" k="12" />
<hkern u1="&#x166;" u2="&#x20;" k="26" />
<hkern u1="&#x1fa;" u2="&#xba;" k="30" />
<hkern u1="&#x1fa;" u2="&#xaa;" k="28" />
<hkern u1="&#x1fa;" u2="d" k="10" />
<hkern u1="&#x1fa;" u2="&#x166;" k="45" />
<hkern u1="&#x1fa;" g2="hyphen.case" k="10" />
<hkern u1="&#x1fa;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x1fa;" u2="&#x29;" k="16" />
<hkern u1="&#x1fa;" g2="nine.plf" k="10" />
<hkern u1="&#x1fa;" g2="seven.plf" k="14" />
<hkern u1="&#x1fa;" g2="six.plf" k="11" />
<hkern u1="&#x1fa;" g2="one.plf" k="44" />
<hkern u1="&#x1fa;" g2="zero.plf" k="12" />
<hkern u1="&#x1fa;" u2="f" k="17" />
<hkern u1="&#x1fa;" u2="&#x2039;" k="13" />
<hkern u1="&#x1fa;" u2="&#x2a;" k="34" />
<hkern u1="&#x1fa;" u2="&#x2122;" k="43" />
<hkern u1="&#x1fa;" u2="&#x27;" k="38" />
<hkern u1="&#x1fa;" u2="&#xf0;" k="9" />
<hkern u1="&#x1fa;" u2="&#x7d;" k="33" />
<hkern u1="&#x1fa;" u2="&#xae;" k="16" />
<hkern u1="&#x1fa;" u2="s" k="6" />
<hkern u1="&#x1fa;" u2="&#x2018;" k="38" />
<hkern u1="&#x1fa;" u2="&#x2019;" k="40" />
<hkern u1="&#x1fa;" u2="&#xa9;" k="16" />
<hkern u1="&#x1fa;" u2="&#x3f;" k="34" />
<hkern u1="&#x1fa;" u2="\" k="50" />
<hkern u1="&#x1fa;" u2="]" k="33" />
<hkern u1="&#x1fa;" u2="U" k="13" />
<hkern u1="&#x1fa;" u2="Y" k="57" />
<hkern u1="&#x1fa;" u2="W" k="33" />
<hkern u1="&#x1fa;" u2="V" k="42" />
<hkern u1="&#x1fa;" u2="T" k="53" />
<hkern u1="&#x1fa;" u2="S" k="10" />
<hkern u1="&#x1fa;" u2="O" k="17" />
<hkern u1="&#x1fa;" u2="y" k="28" />
<hkern u1="&#x1fa;" u2="w" k="19" />
<hkern u1="&#x1fa;" u2="v" k="26" />
<hkern u1="&#x1fa;" u2="u" k="6" />
<hkern u1="&#x1fa;" u2="o" k="10" />
<hkern u1="&#x1fa;" u2="a" k="5" />
<hkern u1="&#x1fa;" u2="&#x20;" k="26" />
<hkern u1="&#x119;" u2="&#x29;" k="33" />
<hkern u1="&#x119;" u2="f" k="4" />
<hkern u1="&#x119;" u2="&#x2122;" k="21" />
<hkern u1="&#x119;" u2="&#x7d;" k="33" />
<hkern u1="&#x119;" u2="&#x2018;" k="8" />
<hkern u1="&#x119;" u2="&#x2019;" k="11" />
<hkern u1="&#x119;" u2="\" k="31" />
<hkern u1="&#x119;" u2="]" k="33" />
<hkern u1="&#x119;" u2="X" k="11" />
<hkern u1="&#x119;" u2="Y" k="84" />
<hkern u1="&#x119;" u2="Z" k="7" />
<hkern u1="&#x119;" u2="W" k="37" />
<hkern u1="&#x119;" u2="V" k="44" />
<hkern u1="&#x119;" u2="T" k="75" />
<hkern u1="&#x119;" u2="A" k="8" />
<hkern u1="&#x119;" u2="x" k="15" />
<hkern u1="&#x119;" u2="y" k="11" />
<hkern u1="&#x119;" u2="w" k="6" />
<hkern u1="&#x119;" u2="v" k="9" />
<hkern u1="&#x12e;" u2="d" k="6" />
<hkern u1="&#x12e;" u2="&#xf0;" k="7" />
<hkern u1="&#x12e;" u2="o" k="6" />
<hkern u1="&#x12f;" u2="U" k="5" />
<hkern u1="&#x12f;" u2="Z" k="5" />
<hkern u1="&#x172;" u2="d" k="6" />
<hkern u1="&#x172;" g2="parenright.case" k="14" />
<hkern u1="&#x172;" u2="i" k="5" />
<hkern u1="&#x172;" u2="&#xc6;" k="11" />
<hkern u1="&#x172;" u2="&#xf0;" k="7" />
<hkern u1="&#x172;" u2="s" k="5" />
<hkern u1="&#x172;" u2="J" k="14" />
<hkern u1="&#x172;" u2="A" k="13" />
<hkern u1="&#x172;" u2="&#x2e;" k="8" />
<hkern u1="&#x172;" u2="z" k="7" />
<hkern u1="&#x172;" u2="u" k="7" />
<hkern u1="&#x172;" u2="o" k="5" />
<hkern u1="&#x172;" u2="a" k="6" />
<hkern u1="&#x173;" u2="&#x29;" k="26" />
<hkern u1="&#x173;" u2="&#x2122;" k="17" />
<hkern u1="&#x173;" u2="&#x7d;" k="26" />
<hkern u1="&#x173;" u2="\" k="16" />
<hkern u1="&#x173;" u2="]" k="26" />
<hkern u1="&#x173;" u2="Y" k="54" />
<hkern u1="&#x173;" u2="Z" k="5" />
<hkern u1="&#x173;" u2="W" k="26" />
<hkern u1="&#x173;" u2="V" k="37" />
<hkern u1="&#x173;" u2="T" k="87" />
<hkern u1="&#x104;" u2="&#xba;" k="30" />
<hkern u1="&#x104;" u2="&#xaa;" k="28" />
<hkern u1="&#x104;" u2="d" k="10" />
<hkern u1="&#x104;" u2="&#x166;" k="45" />
<hkern u1="&#x104;" g2="hyphen.case" k="10" />
<hkern u1="&#x104;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x104;" u2="&#x29;" k="16" />
<hkern u1="&#x104;" g2="nine.plf" k="10" />
<hkern u1="&#x104;" g2="seven.plf" k="14" />
<hkern u1="&#x104;" g2="six.plf" k="11" />
<hkern u1="&#x104;" g2="one.plf" k="44" />
<hkern u1="&#x104;" g2="zero.plf" k="12" />
<hkern u1="&#x104;" u2="f" k="17" />
<hkern u1="&#x104;" u2="&#x2039;" k="13" />
<hkern u1="&#x104;" u2="&#x2a;" k="34" />
<hkern u1="&#x104;" u2="&#x2122;" k="43" />
<hkern u1="&#x104;" u2="&#x27;" k="38" />
<hkern u1="&#x104;" u2="&#xf0;" k="9" />
<hkern u1="&#x104;" u2="&#x7d;" k="33" />
<hkern u1="&#x104;" u2="&#xae;" k="16" />
<hkern u1="&#x104;" u2="s" k="6" />
<hkern u1="&#x104;" u2="&#x2018;" k="38" />
<hkern u1="&#x104;" u2="&#x2019;" k="40" />
<hkern u1="&#x104;" u2="&#xa9;" k="16" />
<hkern u1="&#x104;" u2="&#x3f;" k="34" />
<hkern u1="&#x104;" u2="\" k="50" />
<hkern u1="&#x104;" u2="]" k="33" />
<hkern u1="&#x104;" u2="U" k="13" />
<hkern u1="&#x104;" u2="Y" k="57" />
<hkern u1="&#x104;" u2="W" k="33" />
<hkern u1="&#x104;" u2="V" k="42" />
<hkern u1="&#x104;" u2="T" k="53" />
<hkern u1="&#x104;" u2="S" k="10" />
<hkern u1="&#x104;" u2="O" k="17" />
<hkern u1="&#x104;" u2="y" k="28" />
<hkern u1="&#x104;" u2="w" k="19" />
<hkern u1="&#x104;" u2="v" k="26" />
<hkern u1="&#x104;" u2="u" k="6" />
<hkern u1="&#x104;" u2="o" k="10" />
<hkern u1="&#x104;" u2="a" k="5" />
<hkern u1="&#x104;" u2="&#x20;" k="26" />
<hkern g1="idotaccent" u2="U" k="5" />
<hkern g1="idotaccent" u2="Z" k="5" />
<hkern g1="zero.plfslash" u2="&#x29;" k="24" />
<hkern g1="zero.plfslash" u2="&#x7d;" k="23" />
<hkern g1="zero.plfslash" u2="]" k="23" />
<hkern g1="zero.plfslash" u2="Y" k="20" />
<hkern g1="zero.plfslash" u2="W" k="12" />
<hkern g1="zero.plfslash" u2="V" k="16" />
<hkern g1="zero.plfslash" u2="A" k="12" />
<hkern u1="l" u2="&#xb7;" k="57" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="7" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="7" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="30" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="15" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="17" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="17" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="z,zcaron,zacute,zdotaccent" 	k="5" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="8" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="AE,AEacute" 	k="12" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="J,Jcircumflex" 	k="17" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="9" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="guillemotleft,guilsinglleft" 	k="11" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="10" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="10" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="6" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="guillemotleft.case,guilsinglleft.case" 	k="14" />
<hkern g1="G,Gdotaccent,Gbreve,Gcircumflex,Gcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="25" />
<hkern g1="G,Gdotaccent,Gbreve,Gcircumflex,Gcommaaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="14" />
<hkern g1="G,Gdotaccent,Gbreve,Gcircumflex,Gcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="10" />
<hkern g1="I,M,N,H,J,Iacute,Icircumflex,Idieresis,Igrave,Ntilde,Imacron,Ibreve,Hcircumflex,Itilde,Idotaccent,IJ,Jcircumflex,Nacute,Ncaron,Eng,Ncommaaccent,Hbar,Iogonek" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="6" />
<hkern g1="I,M,N,H,J,Iacute,Icircumflex,Idieresis,Igrave,Ntilde,Imacron,Ibreve,Hcircumflex,Itilde,Idotaccent,IJ,Jcircumflex,Nacute,Ncaron,Eng,Ncommaaccent,Hbar,Iogonek" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="6" />
<hkern g1="K,Kcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="29" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="15" />
<hkern g1="K,Kcommaaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="34" />
<hkern g1="K,Kcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="27" />
<hkern g1="K,Kcommaaccent" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="13" />
<hkern g1="K,Kcommaaccent" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="30" />
<hkern g1="K,Kcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="33" />
<hkern g1="K,Kcommaaccent" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="12" />
<hkern g1="K,Kcommaaccent" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="15" />
<hkern g1="K,Kcommaaccent" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="30" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft.case,guilsinglleft.case" 	k="24" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="20" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="83" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="6" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="13" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="15" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="18" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="5" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="54" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="66" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="67" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="76" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="quotedblleft,quoteleft" 	k="75" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="quotedblright,quoteright" 	k="75" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="14" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="quotedbl,quotesingle" 	k="75" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="40" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="guillemotleft.case,guilsinglleft.case" 	k="54" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="49" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="guillemotright.case,guilsinglright.case" 	k="14" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="29" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="15" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="16" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="17" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="8" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="AE,AEacute" 	k="11" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="J,Jcircumflex" 	k="17" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="9" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="15" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="13" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="17" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="12" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="7" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="6" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="5" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="21" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="12" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="5" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="9" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="81" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="16" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="80" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="74" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="41" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="47" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="20" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="87" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="73" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="49" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guillemotleft.case,guilsinglleft.case" 	k="58" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="53" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="z,zcaron,zacute,zdotaccent" 	k="76" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="AE,AEacute" 	k="58" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="J,Jcircumflex" 	k="55" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="hyphen,endash,emdash,uni00AD" 	k="42" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guillemotright.case,guilsinglright.case" 	k="36" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="49" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="colon,semicolon" 	k="29" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="87" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guilsinglright,guillemotright" 	k="59" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="5" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="6" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="6" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="7" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="5" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="13" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="z,zcaron,zacute,zdotaccent" 	k="7" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="AE,AEacute" 	k="11" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="8" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="j,iacute,igrave,icircumflex,idieresis,dotlessi,i,imacron,ibreve,itilde,ij,jcircumflex,iogonek,idotaccent" 	k="5" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="38" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="zero.plf,zero.plfslash" 	k="11" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="32" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="15" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="37" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="39" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="11" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="9" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="22" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="35" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guillemotleft.case,guilsinglleft.case" 	k="22" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="33" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="z,zcaron,zacute,zdotaccent" 	k="15" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="AE,AEacute" 	k="43" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="J,Jcircumflex" 	k="44" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="hyphen,endash,emdash,uni00AD" 	k="13" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="40" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="26" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guilsinglright,guillemotright" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="71" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="zero.plf,zero.plfslash" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guillemotleft,guilsinglleft" 	k="65" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="28" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="69" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="33" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="26" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="13" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="26" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guillemotleft.case,guilsinglleft.case" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="z,zcaron,zacute,zdotaccent" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="AE,AEacute" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="J,Jcircumflex" 	k="64" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="hyphen,endash,emdash,uni00AD" 	k="44" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guillemotright.case,guilsinglright.case" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="62" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="colon,semicolon" 	k="19" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guilsinglright,guillemotright" 	k="40" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="12" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="guillemotleft,guilsinglleft" 	k="13" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="9" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="12" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="10" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="9" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="6" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="9" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="5" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="7" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="guillemotleft.case,guilsinglleft.case" 	k="19" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="6" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="61" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="guillemotleft,guilsinglleft" 	k="13" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="20" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="67" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="quotedblleft,quoteleft" 	k="20" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="quotedblright,quoteright" 	k="22" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="11" />
<hkern g1="p,thorn,b" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="69" />
<hkern g1="p,thorn,b" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="p,thorn,b" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="p,thorn,b" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="12" />
<hkern g1="p,thorn,b" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="80" />
<hkern g1="p,thorn,b" 	g2="quotedblleft,quoteleft" 	k="13" />
<hkern g1="p,thorn,b" 	g2="quotedblright,quoteright" 	k="15" />
<hkern g1="p,thorn,b" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="6" />
<hkern g1="p,thorn,b" 	g2="quotedbl,quotesingle" 	k="12" />
<hkern g1="p,thorn,b" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="7" />
<hkern g1="p,thorn,b" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="9" />
<hkern g1="p,thorn,b" 	g2="z,zcaron,zacute,zdotaccent" 	k="5" />
<hkern g1="p,thorn,b" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="12" />
<hkern g1="p,thorn,b" 	g2="AE,AEacute" 	k="5" />
<hkern g1="p,thorn,b" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="p,thorn,b" 	g2="E,F,I,K,M,N,R,L,P,H,B,D,Eacute,Ecircumflex,Edieresis,Egrave,Iacute,Icircumflex,Idieresis,Igrave,Eth,Lslash,Thorn,Ntilde,Edotaccent,Dcaron,Emacron,Imacron,Ebreve,Ibreve,Ecaron,Hcircumflex,Itilde,Idotaccent,IJ,Lacute,Nacute,Ncaron,Racute,Rcaron,Eng,Dcroat,Lcaron,Kcommaaccent,Lcommaaccent,Ncommaaccent,Rcommaaccent,Hbar,Eogonek,Iogonek" 	k="6" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="75" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="32" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="5" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="85" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="5" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="19" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="29" />
<hkern g1="dcaron,lcaron" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="24" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="dcaron,lcaron" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="22" />
<hkern g1="dcaron,lcaron" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="15" />
<hkern g1="dcaron,lcaron" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="9" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash,uni00AD" 	k="26" />
<hkern g1="dcaron,lcaron" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="29" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="84" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="11" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="75" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="quotedblleft,quoteleft" 	k="8" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="quotedblright,quoteright" 	k="11" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="4" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="6" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="8" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="7" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="40" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="59" />
<hkern g1="guillemotleft.case,guilsinglleft.case" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="20" />
<hkern g1="guillemotleft.case,guilsinglleft.case" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="36" />
<hkern g1="guilsinglright,guillemotright" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="64" />
<hkern g1="guilsinglright,guillemotright" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="32" />
<hkern g1="guilsinglright,guillemotright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="21" />
<hkern g1="guilsinglright,guillemotright" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="66" />
<hkern g1="guilsinglright,guillemotright" 	g2="quotedblright,quoteright" 	k="18" />
<hkern g1="guilsinglright,guillemotright" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="16" />
<hkern g1="guilsinglright,guillemotright" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="guilsinglright,guillemotright" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="13" />
<hkern g1="guilsinglright,guillemotright" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="14" />
<hkern g1="guilsinglright,guillemotright" 	g2="z,zcaron,zacute,zdotaccent" 	k="20" />
<hkern g1="guilsinglright,guillemotright" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="11" />
<hkern g1="guilsinglright,guillemotright" 	g2="J,Jcircumflex" 	k="15" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="49" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="22" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="58" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="24" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="18" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="AE,AEacute" 	k="23" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="J,Jcircumflex" 	k="46" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="44" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="13" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="8" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="42" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="z,zcaron,zacute,zdotaccent" 	k="14" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="J,Jcircumflex" 	k="42" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="33" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="9" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="41" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="11" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="9" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="J,Jcircumflex" 	k="47" />
<hkern g1="j,fi,iacute,igrave,icircumflex,idieresis,dotlessi,i,imacron,ibreve,itilde,ij,jcircumflex,iogonek,idotaccent" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="5" />
<hkern g1="j,fi,iacute,igrave,icircumflex,idieresis,dotlessi,i,imacron,ibreve,itilde,ij,jcircumflex,iogonek,idotaccent" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="k,kcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="32" />
<hkern g1="k,kcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="25" />
<hkern g1="k,kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="32" />
<hkern g1="k,kcommaaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="12" />
<hkern g1="k,kcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="24" />
<hkern g1="k,kcommaaccent" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="7" />
<hkern g1="k,kcommaaccent" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="6" />
<hkern g1="k,kcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="60" />
<hkern g1="k,kcommaaccent" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="10" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="24" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="63" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="31" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="6" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="81" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="quotedblright,quoteright" 	k="8" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="71" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="5" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="38" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="13" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="81" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedblleft,quoteleft" 	k="10" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedblright,quoteright" 	k="13" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="6" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="9" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="7" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="10" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="z,zcaron,zacute,zdotaccent" 	k="5" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="12" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="AE,AEacute" 	k="5" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="E,F,I,K,M,N,R,L,P,H,B,D,Eacute,Ecircumflex,Edieresis,Egrave,Iacute,Icircumflex,Idieresis,Igrave,Eth,Lslash,Thorn,Ntilde,Edotaccent,Dcaron,Emacron,Imacron,Ebreve,Ibreve,Ecaron,Hcircumflex,Itilde,Idotaccent,IJ,Lacute,Nacute,Ncaron,Racute,Rcaron,Eng,Dcroat,Lcaron,Kcommaaccent,Lcommaaccent,Ncommaaccent,Rcommaaccent,Hbar,Eogonek,Iogonek" 	k="6" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="62" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="9" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="40" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="36" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="49" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="quotedblleft,quoteleft" 	k="107" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="quotedblright,quoteright" 	k="108" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="10" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="104" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="24" />
<hkern g1="quotedblleft,quoteleft" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="12" />
<hkern g1="quotedblleft,quoteleft" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="14" />
<hkern g1="quotedblleft,quoteleft" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="8" />
<hkern g1="quotedblleft,quoteleft" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="39" />
<hkern g1="quotedblleft,quoteleft" 	g2="AE,AEacute" 	k="45" />
<hkern g1="quotedblleft,quoteleft" 	g2="J,Jcircumflex" 	k="50" />
<hkern g1="quotedblleft,quoteleft" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="108" />
<hkern g1="quotedblright,quoteright" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="23" />
<hkern g1="quotedblright,quoteright" 	g2="guillemotleft,guilsinglleft" 	k="28" />
<hkern g1="quotedblright,quoteright" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="25" />
<hkern g1="quotedblright,quoteright" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="18" />
<hkern g1="quotedblright,quoteright" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="15" />
<hkern g1="quotedblright,quoteright" 	g2="guillemotleft.case,guilsinglleft.case" 	k="12" />
<hkern g1="quotedblright,quoteright" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="45" />
<hkern g1="quotedblright,quoteright" 	g2="AE,AEacute" 	k="52" />
<hkern g1="quotedblright,quoteright" 	g2="J,Jcircumflex" 	k="50" />
<hkern g1="quotedblright,quoteright" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="118" />
<hkern g1="quotedbl,quotesingle" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="9" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="12" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="38" />
<hkern g1="quotedbl,quotesingle" 	g2="AE,AEacute" 	k="44" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="50" />
<hkern g1="quotedbl,quotesingle" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="104" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="21" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="9" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="22" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="8" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="4" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="46" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="39" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="11" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="AE,AEacute" 	k="44" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="J,Jcircumflex" 	k="56" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="27" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="34" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="81" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="39" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="11" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="74" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="quotedblright,quoteright" 	k="9" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="6" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="6" />
<hkern g1="t,uni0163,uni021B,tbar" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="28" />
<hkern g1="t,uni0163,uni021B,tbar" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="t,uni0163,uni021B,tbar" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="47" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="54" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="26" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="87" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="27" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="7" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="13" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="7" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="6" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="49" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="5" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="19" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="10" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="AE,AEacute" 	k="27" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="J,Jcircumflex" 	k="34" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="24" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="24" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="11" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="guillemotleft,guilsinglleft" 	k="19" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="11" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="10" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="46" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="8" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="26" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="12" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="AE,AEacute" 	k="34" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="J,Jcircumflex" 	k="59" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="34" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="46" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="5" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="5" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="5" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="12" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="73" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="13" />
<hkern g1="zero.plf,zero.plfslash" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="20" />
<hkern g1="zero.plf,zero.plfslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="12" />
<hkern g1="zero.plf,zero.plfslash" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="12" />
</font>
</defs></svg> 