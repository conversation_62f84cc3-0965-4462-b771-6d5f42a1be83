<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="px_grotesklight" horiz-adv-x="600" >
<font-face units-per-em="1000" ascent="805" descent="-195" />
<missing-glyph horiz-adv-x="262" />
<glyph unicode="fi" horiz-adv-x="555" d="M468 601h-69v69h69v-69zM401 0v453h-216v-453h-65v453h-85v57h85v170h187v-57h-122v-113h281v-510h-65z" />
<glyph unicode="fl" horiz-adv-x="557" d="M185 510h112v-57h-112v-453h-65v453h-85v57h85v170h346v-680h-65v623h-216v-113z" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="262" />
<glyph unicode="&#x09;" horiz-adv-x="262" />
<glyph unicode="&#xa0;" horiz-adv-x="262" />
<glyph unicode="h" horiz-adv-x="561" d="M412 330q0 63 -24.5 99t-89.5 36q-32 0 -58 -12.5t-45.5 -35t-30 -54t-10.5 -68.5v-295h-65v680h65v-259q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v330z" />
<glyph unicode="p" horiz-adv-x="585" d="M89 -170v680h65v-88q50 100 167 100q49 0 87 -19.5t64 -55t39 -84.5t13 -108t-13 -108.5t-38.5 -84.5t-63.5 -54.5t-88 -19.5q-54 0 -98 25t-69 75v-258h-65zM154 213q0 -36 10.5 -67t31 -53.5t49 -35t64.5 -12.5q34 0 60.5 10.5t45.5 28.5t29 42t10 52v154q0 28 -10 52 t-29 42t-45.5 28.5t-60.5 10.5q-36 0 -64.5 -12.5t-49 -35t-31 -53.5t-10.5 -67v-84z" />
<glyph unicode="q" horiz-adv-x="585" d="M431 -170v258q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v88h65v-680h-65zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5t-29 -42t-10 -52v-154 q0 -28 10 -52t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84z" />
<glyph unicode="j" horiz-adv-x="255" d="M168 601h-69v69h69v-69zM101 510h65v-680h-183v57h118v623z" />
<glyph unicode="t" horiz-adv-x="347" d="M185 57h122v-57h-187v453h-85v57h85v170h65v-170h122v-57h-122v-396z" />
<glyph unicode="a" horiz-adv-x="540" d="M167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78 v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="o" horiz-adv-x="560" d="M429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107t40 84.5t68.5 55.5t97.5 20t97.5 -20t68.5 -55.5 t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="u" horiz-adv-x="557" d="M149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="e" horiz-adv-x="537" d="M474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5t45 -27t52.5 -9.5q54 0 92 26.5t45 85.5h65z M409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="c" horiz-adv-x="523" d="M399 348q-3 31 -14.5 53t-29.5 36.5t-41 21t-48 6.5q-26 0 -50 -9.5t-42.5 -27t-30 -43t-11.5 -56.5v-148q0 -29 10.5 -54t29 -43t43 -28.5t52.5 -10.5q27 0 51 8t42.5 23.5t29.5 38t13 51.5h65q-5 -45 -22.5 -78.5t-44.5 -55.5t-61 -33t-71 -11q-54 0 -93 20t-64.5 55 t-37.5 83.5t-12 105.5q0 61 13.5 111t39.5 85t65.5 54.5t90.5 19.5q39 0 72.5 -11t59 -33t42 -54.5t19.5 -75.5h-65z" />
<glyph unicode="k" horiz-adv-x="494" d="M154 0h-65v680h65v-680zM156 285l208 225h85l-215 -226l233 -284h-78z" />
<glyph unicode="m" horiz-adv-x="838" d="M89 510h65v-89q17 46 52.5 73.5t90.5 27.5q52 0 91 -26t56 -84q16 49 54.5 79.5t96.5 30.5q34 0 63 -10t50.5 -32t33.5 -56.5t12 -83.5v-340h-65v331q0 63 -24.5 98.5t-81.5 35.5q-27 0 -50.5 -12.5t-41 -35t-27.5 -54t-10 -68.5v-295h-65v331q0 63 -24.5 98.5 t-81.5 35.5q-27 0 -50.5 -12.5t-41 -35t-27.5 -54t-10 -68.5v-295h-65v510z" />
<glyph unicode="r" horiz-adv-x="342" d="M89 510h225v-57h-160v-453h-65v510z" />
<glyph unicode="v" horiz-adv-x="496" d="M250 95l143 415h71l-183 -510h-66l-183 510h71l143 -415h4z" />
<glyph unicode="w" horiz-adv-x="743" d="M227 95l104 415h83l105 -415h4l114 415h69l-155 -510h-66l-111 429h-4l-111 -429h-67l-155 510h71l115 -415h4z" />
<glyph unicode="z" horiz-adv-x="468" d="M416 57v-57h-366v57l276 396h-261v57h340v-57l-276 -396h287z" />
<glyph unicode="y" horiz-adv-x="510" d="M259 95l147 415h72l-250 -680h-153v57h108l41 113l-189 510h75l145 -415h4z" />
<glyph unicode="x" horiz-adv-x="494" d="M205 264l-160 246h79l122 -194l124 194h79l-162 -241l177 -269h-79l-138 216l-137 -216h-79z" />
<glyph unicode="." horiz-adv-x="245" d="M165 0h-85v85h85v-85z" />
<glyph unicode="," horiz-adv-x="245" d="M165 0l-49 -85h-36l46 85h-46v85h85v-85z" />
<glyph unicode="A" horiz-adv-x="623" d="M310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="E" horiz-adv-x="589" d="M99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="F" horiz-adv-x="542" d="M99 680h392v-65h-322v-236h298v-65h-298v-314h-70v680z" />
<glyph unicode="I" horiz-adv-x="268" d="M99 680h70v-680h-70v680z" />
<glyph unicode="K" horiz-adv-x="603" d="M169 0h-70v680h70v-680zM171 362l293 318h93l-301 -318l323 -362h-88z" />
<glyph unicode="M" horiz-adv-x="829" d="M414 113h4l204 567h108v-680h-67v588h-4l-215 -588h-58l-216 588h-4v-588h-67v680h110z" />
<glyph unicode="N" horiz-adv-x="689" d="M521 79v601h69v-680h-111l-307 602h-4v-602h-69v680h111l307 -601h4z" />
<glyph unicode="O" horiz-adv-x="696" d="M348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73t-129.5 -26t-129.5 26t-87.5 73 t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="R" horiz-adv-x="614" d="M379 358q42 0 68.5 24t26.5 66v83q0 42 -26.5 63t-68.5 21h-210v-257h210zM169 296v-296h-70v680h257q106 0 149.5 -49.5t43.5 -140.5q0 -92 -43 -137t-117 -55l161 -298h-80l-157 296h-144z" />
<glyph unicode="S" horiz-adv-x="607" d="M299 632q-71 0 -113 -29.5t-42 -86.5q0 -31 8.5 -50.5t24.5 -32t38.5 -20.5t50.5 -16l113 -32q89 -25 130.5 -67t41.5 -119q0 -45 -18.5 -81t-50.5 -61t-77 -38.5t-97 -13.5q-53 0 -97 12.5t-77 39t-54 66.5t-27 95h70q8 -77 58 -113.5t125 -36.5q37 0 69.5 8.5t55.5 25 t36.5 40.5t13.5 55q0 29 -8 48.5t-23.5 32.5t-37 21.5t-48.5 16.5l-135 37q-82 23 -118.5 67t-36.5 116q0 38 15.5 71t44.5 57t70.5 37.5t94.5 13.5q49 0 90 -12.5t71.5 -38t49.5 -63t24 -86.5h-70q-3 37 -17.5 63.5t-37 42.5t-51 23.5t-59.5 7.5z" />
<glyph unicode="T" horiz-adv-x="520" d="M22 680h476v-65h-203v-615h-70v615h-203v65z" />
<glyph unicode="L" horiz-adv-x="498" d="M99 680h70v-615h308v-65h-378v680z" />
<glyph unicode="P" horiz-adv-x="597" d="M379 324q42 0 68.5 25t26.5 63v119q0 38 -25 61t-70 23h-210v-291h210zM169 259v-259h-70v680h257q106 0 149.5 -50t43.5 -157q0 -58 -13.5 -99t-39 -66.5t-63 -37t-86.5 -11.5h-178z" />
<glyph unicode="H" horiz-adv-x="689" d="M520 317h-351v-317h-70v680h70v-298h351v298h70v-680h-70v317z" />
<glyph unicode="B" horiz-adv-x="640" d="M497 241q0 38 -27 58t-66 20h-235v-254h229q44 0 71.5 22t27.5 62v92zM454 355q55 -13 86.5 -53.5t31.5 -102.5q0 -105 -52.5 -152t-150.5 -47h-270v680h270q43 0 78 -9t59.5 -28.5t37.5 -51t13 -76.5q0 -58 -27.5 -100t-75.5 -60zM169 615v-231h224q42 0 65.5 22 t23.5 60v71q0 38 -23.5 58t-65.5 20h-224z" />
<glyph unicode="Q" horiz-adv-x="702" d="M348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM529 45q-33 -29 -77.5 -44.5t-103.5 -15.5q-76 0 -129.5 26t-87.5 73t-49.5 112 t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144q0 -75 -14 -137t-44 -108l98 -95l-47 -46z" />
<glyph unicode="D" horiz-adv-x="659" d="M169 615v-550h137q97 0 151 46t54 129v200q0 83 -53.5 129t-150.5 46h-138zM309 680q146 0 214 -82.5t68 -257.5t-68 -257.5t-214 -82.5h-210v680h210z" />
<glyph unicode="J" horiz-adv-x="410" d="M311 0h-289v65h219v615h70v-680z" />
<glyph unicode="V" horiz-adv-x="611" d="M302 78h4l189 602h74l-220 -680h-88l-219 680h75z" />
<glyph unicode="W" horiz-adv-x="915" d="M275 80l137 600h90l139 -600h4l146 600h76l-178 -680h-94l-136 574h-4l-135 -574h-94l-178 680h75l148 -600h4z" />
<glyph unicode="Z" horiz-adv-x="574" d="M517 615l-384 -550h382v-65h-464v65l385 550h-369v65h450v-65z" />
<glyph unicode="Y" horiz-adv-x="574" d="M28 680h78l181 -340l184 340h76l-225 -410v-270h-70v270z" />
<glyph unicode="C" horiz-adv-x="633" d="M336 -15q-132 0 -201 89t-69 260q0 80 16 146.5t49.5 114t84.5 74t120 26.5q47 0 87.5 -14t71.5 -41t50 -65.5t24 -87.5h-70q-4 37 -18.5 64.5t-37 44.5t-51.5 25.5t-63 8.5q-41 0 -74.5 -15t-57.5 -40.5t-37.5 -60.5t-13.5 -74v-200q0 -41 14.5 -76t39 -60.5t57.5 -39.5 t70 -14q78 0 125.5 35t55.5 113h70q-4 -48 -22.5 -87.5t-50 -67t-74.5 -43t-95 -15.5z" />
<glyph unicode="U" horiz-adv-x="678" d="M516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="X" horiz-adv-x="577" d="M248 347l-205 333h81l164 -275l169 275h78l-206 -333l215 -347h-81l-175 290l-175 -290h-82z" />
<glyph unicode="G" horiz-adv-x="662" d="M329 -15q-128 0 -195.5 91.5t-67.5 257.5q0 81 16 147.5t49.5 114t85.5 73.5t123 26q49 0 90.5 -14.5t72.5 -41.5t50 -65.5t24 -86.5h-70q-8 72 -53.5 107.5t-120.5 35.5q-41 0 -75 -14.5t-59 -40t-39 -60t-14 -75.5v-200q0 -42 14.5 -76.5t39 -60t58.5 -39.5t73 -14 q87 0 137 47.5t50 134.5v56h-164v65h234v-353h-66l-4 77q-26 -43 -75 -67.5t-114 -24.5z" />
<glyph unicode="1" d="M122 59h175v482h-175v59h175v80h65v-621h155v-59h-395v59z" />
<glyph unicode="2" d="M154 59h366v-59h-439v73l279 272q18 18 32.5 33.5t25 33.5t16.5 39t6 49q0 62 -36 99t-99 37q-68 0 -109 -40t-43 -111h-67q1 47 17.5 86t45.5 66.5t69 42.5t87 15q95 0 149 -52t54 -143q0 -39 -10 -69.5t-26 -55.5t-35.5 -45t-38.5 -38l-244 -231v-2z" />
<glyph unicode="4" d="M135 233h232v345zM367 0v174h-301v59l300 447h66v-447h98v-59h-98v-174h-65z" />
<glyph unicode="3" d="M206 319v59h149q42 0 62.5 23t20.5 57v65q0 50 -36 82t-102 32q-71 0 -109 -37t-40 -94h-67q1 37 15.5 71.5t42 60.5t67.5 41.5t91 15.5q49 0 88 -14t66.5 -38.5t42 -59t14.5 -74.5q0 -62 -23 -98t-65 -60q50 -23 75.5 -62.5t25.5 -103.5q0 -45 -16.5 -82t-46.5 -63.5 t-72 -40.5t-93 -14q-47 0 -88 13t-72 39t-49 65t-19 91h67q1 -37 13.5 -65t34.5 -47t51 -28.5t62 -9.5q35 0 63.5 10t49 27t31.5 39.5t11 47.5v72q0 34 -23.5 57t-65.5 23h-156z" />
<glyph unicode="5" d="M88 298l26 382h367v-59h-309l-16 -248q28 41 69.5 62.5t90.5 21.5q50 0 89.5 -16.5t66.5 -46.5t41.5 -73t14.5 -95q0 -55 -16 -99.5t-45 -76t-70.5 -48.5t-93.5 -17q-102 0 -158.5 50.5t-67.5 147.5h67q8 -65 47 -102.5t109 -37.5q33 0 61 10.5t48.5 28.5t32 43.5 t11.5 55.5v87q0 58 -40.5 94.5t-110.5 36.5q-48 0 -87 -27.5t-60 -73.5h-67z" />
<glyph unicode="7" d="M234 0h-73l290 621h-361v59h432v-59z" />
<glyph unicode="8" d="M139 151q0 -44 41 -75.5t120 -31.5t120 31.5t41 75.5v66q0 44 -41 75t-120 31t-120 -31t-41 -75v-66zM441 535q0 42 -37.5 71.5t-103.5 29.5t-103.5 -29.5t-37.5 -71.5v-57q0 -43 37.5 -72t103.5 -29t103.5 29t37.5 72v57zM300 -15q-47 0 -89.5 12t-74.5 36t-51 61 t-19 88q0 63 33.5 107t95.5 65q-57 23 -83 62t-26 95q0 43 17.5 77t46.5 58t68 36.5t82 12.5t82 -12.5t68 -36.5t46.5 -58t17.5 -77q0 -56 -26 -95t-83 -62q62 -21 95.5 -65t33.5 -107q0 -51 -19 -88t-51 -61t-74.5 -36t-89.5 -12z" />
<glyph unicode="0" d="M456 473q0 75 -42 119t-114 44t-114 -44t-42 -119v-266q0 -75 42 -119t114 -44t114 44t42 119v266zM531 340q0 -74 -9.5 -139t-35 -113t-70.5 -75.5t-116 -27.5t-116 27.5t-70.5 75.5t-35 113t-9.5 139t9.5 139t35 113t70.5 75.5t116 27.5t116 -27.5t70.5 -75.5t35 -113 t9.5 -139z" />
<glyph unicode="&#x26;" horiz-adv-x="602" d="M255 413q43 34 72.5 67t29.5 78q0 38 -23.5 59.5t-66.5 21.5q-42 0 -67 -22.5t-25 -60.5q0 -33 23.5 -67.5t56.5 -75.5zM132 145q0 -24 10 -43t27 -32t38.5 -19.5t44.5 -6.5q51 0 90 15.5t69 47.5l-168 216l-81 -64q-15 -12 -22.5 -26t-7.5 -34v-54zM423 197v166h64v-248 l90 -115h-82l-47 60q-46 -41 -92.5 -56.5t-102.5 -15.5q-87 0 -139 46.5t-52 133.5q0 29 6.5 52.5t18.5 43t29.5 36.5t38.5 34l49 39q-21 26 -38 48t-29 43t-19 42t-7 44q0 33 12.5 59.5t34 45.5t50.5 29.5t62 10.5q70 0 110.5 -37.5t40.5 -105.5q0 -54 -34.5 -98.5 t-93.5 -90.5l128 -166h2z" />
<glyph unicode="6" d="M304 42q37 0 66.5 12.5t50 33.5t31 48t10.5 56v58q0 29 -10.5 54.5t-31 44t-50 29.5t-66.5 11q-38 0 -67 -11t-49.5 -29.5t-31 -44t-10.5 -54.5v-58q0 -29 10.5 -56t31 -48t49.5 -33.5t67 -12.5zM304 -15q-69 0 -114 24.5t-71.5 68.5t-37 106.5t-10.5 139.5 q0 80 10.5 148t37.5 117.5t74 77.5t119 28q95 0 146 -48t60 -131h-67q-7 52 -40.5 86.5t-103.5 34.5q-39 0 -69.5 -14.5t-50.5 -40t-30.5 -59.5t-10.5 -73v-88q27 42 71 63.5t101 21.5q53 0 94 -16.5t68.5 -46.5t42 -70.5t14.5 -88.5q0 -39 -13 -81.5t-41.5 -78t-72.5 -58 t-106 -22.5z" />
<glyph unicode="9" d="M295 637q-38 0 -67 -12.5t-49.5 -33.5t-31 -48t-10.5 -56v-57q0 -29 10.5 -54.5t31 -44.5t49.5 -29.5t67 -10.5q37 0 66.5 10.5t50 29.5t31 44.5t10.5 54.5v58q0 29 -10.5 56t-31 47.5t-50 33t-66.5 12.5zM293 695q70 0 115.5 -24t72 -68t37 -106.5t10.5 -139.5 q0 -84 -10 -152.5t-36.5 -117.5t-72 -75.5t-115.5 -26.5q-97 0 -151 48.5t-63 131.5h67q7 -53 44 -87.5t101 -34.5q39 0 69.5 14.5t50.5 39t30.5 58.5t10.5 73v90q-28 -43 -73.5 -64t-102.5 -21q-53 0 -93 16.5t-67 46.5t-41 71t-14 90q0 39 13.5 81t41.5 77t71.5 57.5 t104.5 22.5z" />
<glyph unicode="-" horiz-adv-x="440" d="M80 302h280v-62h-280v62z" />
<glyph unicode="&#xe7;" horiz-adv-x="523" d="M180 -90h60v55h60v-110h-120v55zM399 348q-3 31 -14.5 53t-29.5 36.5t-41 21t-48 6.5q-26 0 -50 -9.5t-42.5 -27t-30 -43t-11.5 -56.5v-148q0 -29 10.5 -54t29 -43t43 -28.5t52.5 -10.5q27 0 51 8t42.5 23.5t29.5 38t13 51.5h65q-5 -45 -22.5 -78.5t-44.5 -55.5t-61 -33 t-71 -11q-54 0 -93 20t-64.5 55t-37.5 83.5t-12 105.5q0 61 13.5 111t39.5 85t65.5 54.5t90.5 19.5q39 0 72.5 -11t59 -33t42 -54.5t19.5 -75.5h-65z" />
<glyph unicode="&#x2026;" horiz-adv-x="755" d="M675 0h-85v85h85v-85zM420 0h-85v85h85v-85zM165 0h-85v85h85v-85z" />
<glyph unicode=":" horiz-adv-x="245" d="M165 425h-85v85h85v-85zM165 0h-85v85h85v-85z" />
<glyph unicode="!" horiz-adv-x="261" d="M116 158l-18 149v373h65v-373l-18 -149h-29zM173 0h-85v85h85v-85z" />
<glyph unicode="/" horiz-adv-x="434" d="M95 0h-68l312 680h68z" />
<glyph unicode="[" horiz-adv-x="329" d="M280 716v-54h-125v-778h125v-54h-185v886h185z" />
<glyph unicode="]" horiz-adv-x="329" d="M234 716v-886h-185v54h125v778h-125v54h185z" />
<glyph unicode="\" horiz-adv-x="434" d="M27 680h68l312 -680h-68z" />
<glyph unicode="(" horiz-adv-x="300" d="M127 168q0 -83 37.5 -168t101.5 -170h-55q-34 44 -61.5 91t-47 100.5t-30 115.5t-10.5 136t10.5 136t30 115.5t47 100.5t61.5 91h55q-68 -92 -103.5 -173.5t-35.5 -164.5v-210z" />
<glyph unicode="+" d="M45 229v52h227v229h56v-229h227v-52h-227v-229h-56v229h-227z" />
<glyph unicode="?" horiz-adv-x="496" d="M381 511q0 55 -34.5 91t-95.5 36q-65 0 -101 -38.5t-41 -98.5h-65q3 44 20 80t44 61.5t63.5 39t80.5 13.5q43 0 79 -14t61.5 -38.5t39.5 -57.5t14 -72q0 -62 -26 -103.5t-63 -70.5q-23 -18 -39.5 -33t-27 -29.5t-16 -31t-5.5 -37.5v-30h-65v30q0 52 24 89.5t73 76.5 q42 34 61 64.5t19 72.5zM279 0h-85v85h85v-85z" />
<glyph unicode="%" horiz-adv-x="780" d="M601 33q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM729 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39t21 -58 t6 -71.5zM179 363q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM307 505q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39 t21 -58t6 -71.5zM576 680h54l-426 -680h-54z" />
<glyph unicode="&#x2013;" horiz-adv-x="630" d="M80 302h470v-62h-470v62z" />
<glyph unicode="&#x2014;" horiz-adv-x="1100" d="M80 302h940v-62h-940v62z" />
<glyph unicode="&#xb4;" horiz-adv-x="284" d="M196 695h76l-105 -122h-53z" />
<glyph unicode="&#xa8;" horiz-adv-x="282" d="M227 592h-68v68h68v-68zM91 592h-68v68h68v-68z" />
<glyph unicode="`" horiz-adv-x="284" d="M170 573h-53l-105 122h76z" />
<glyph unicode="&#x2c6;" horiz-adv-x="286" d="M72 573h-52l85 122h75l85 -122h-53l-70 88z" />
<glyph unicode="&#xdd;" horiz-adv-x="574" d="M337 849h76l-105 -122h-53zM28 680h78l181 -340l184 340h76l-225 -410v-270h-70v270z" />
<glyph unicode="&#xfd;" horiz-adv-x="510" d="M299 695h76l-105 -122h-53zM259 95l147 415h72l-250 -680h-153v57h108l41 113l-189 510h75l145 -415h4z" />
<glyph unicode="&#xc4;" horiz-adv-x="623" d="M414 766h-68v68h68v-68zM278 766h-68v68h68v-68zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#xc9;" horiz-adv-x="589" d="M333 869h76l-105 -122h-53zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#xdc;" horiz-adv-x="678" d="M441 766h-68v68h68v-68zM305 766h-68v68h68v-68zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#xe1;" horiz-adv-x="540" d="M306 695h76l-105 -122h-53zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5 t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#xe0;" horiz-adv-x="540" d="M281 573h-53l-105 122h76zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5t75 -10.5 t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#xe2;" horiz-adv-x="540" d="M185 573h-52l85 122h75l85 -122h-53l-70 88zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5 t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#xe4;" horiz-adv-x="540" d="M358 592h-68v68h68v-68zM222 592h-68v68h68v-68zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50 t59.5 32.5t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#xe9;" horiz-adv-x="537" d="M321 695h76l-105 -122h-53zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5t45 -27t52.5 -9.5 q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#xe8;" horiz-adv-x="537" d="M305 573h-53l-105 122h76zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5t45 -27t52.5 -9.5 q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#xea;" horiz-adv-x="537" d="M201 573h-52l85 122h75l85 -122h-53l-70 88zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5 t45 -27t52.5 -9.5q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#xeb;" horiz-adv-x="537" d="M370 592h-68v68h68v-68zM234 592h-68v68h68v-68zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5 t45 -27t52.5 -9.5q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#xed;" horiz-adv-x="243" d="M89 0v510h65v-510h-65zM154 695h76l-105 -122h-53z" />
<glyph unicode="&#xec;" horiz-adv-x="243" d="M89 0v510h65v-510h-65zM187 573h-53l-105 122h76z" />
<glyph unicode="&#xee;" horiz-adv-x="243" d="M89 0v510h65v-510h-65zM51 573h-52l85 122h75l85 -122h-53l-70 88z" />
<glyph unicode="&#xef;" horiz-adv-x="243" d="M89 0v510h65v-510h-65zM224 592h-68v68h68v-68zM88 592h-68v68h68v-68z" />
<glyph unicode="&#xf3;" horiz-adv-x="560" d="M330 695h76l-105 -122h-53zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107t40 84.5t68.5 55.5 t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#xf2;" horiz-adv-x="560" d="M303 573h-53l-105 122h76zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107t40 84.5t68.5 55.5 t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#xf4;" horiz-adv-x="560" d="M210 573h-52l85 122h75l85 -122h-53l-70 88zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107 t40 84.5t68.5 55.5t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#xf6;" horiz-adv-x="560" d="M382 592h-68v68h68v-68zM246 592h-68v68h68v-68zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107 t40 84.5t68.5 55.5t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#xfa;" horiz-adv-x="557" d="M310 695h76l-105 -122h-53zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#xf9;" horiz-adv-x="557" d="M333 573h-53l-105 122h76zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#xfb;" horiz-adv-x="557" d="M206 573h-52l85 122h75l85 -122h-53l-70 88zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#xfc;" horiz-adv-x="557" d="M378 592h-68v68h68v-68zM242 592h-68v68h68v-68zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#xc0;" horiz-adv-x="623" d="M384 747h-53l-105 122h76zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#xff;" horiz-adv-x="510" d="M359 592h-68v68h68v-68zM223 592h-68v68h68v-68zM259 95l147 415h72l-250 -680h-153v57h108l41 113l-189 510h75l145 -415h4z" />
<glyph unicode="&#x178;" horiz-adv-x="574" d="M389 766h-68v68h68v-68zM253 766h-68v68h68v-68zM28 680h78l181 -340l184 340h76l-225 -410v-270h-70v270z" />
<glyph unicode="&#xc2;" horiz-adv-x="623" d="M242 747h-52l85 122h75l85 -122h-53l-70 88zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#xca;" horiz-adv-x="589" d="M246 747h-52l85 122h75l85 -122h-53l-70 88zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#xc1;" horiz-adv-x="623" d="M322 869h76l-105 -122h-53zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#xcb;" horiz-adv-x="589" d="M419 766h-68v68h68v-68zM283 766h-68v68h68v-68zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#xc8;" horiz-adv-x="589" d="M362 747h-53l-105 122h76zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#xcd;" horiz-adv-x="268" d="M168 869h76l-105 -122h-53zM99 680h70v-680h-70v680z" />
<glyph unicode="&#xce;" horiz-adv-x="268" d="M64 747h-52l85 122h75l85 -122h-53l-70 88zM99 680h70v-680h-70v680z" />
<glyph unicode="&#xcf;" horiz-adv-x="268" d="M235 766h-68v68h68v-68zM99 766h-68v68h68v-68zM99 680h70v-680h-70v680z" />
<glyph unicode="&#xcc;" horiz-adv-x="268" d="M193 747h-53l-105 122h76zM99 680h70v-680h-70v680z" />
<glyph unicode="&#xda;" horiz-adv-x="678" d="M369 869h76l-105 -122h-53zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#xdb;" horiz-adv-x="678" d="M269 747h-52l85 122h75l85 -122h-53l-70 88zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#xd9;" horiz-adv-x="678" d="M394 747h-53l-105 122h76zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#xc7;" horiz-adv-x="633" d="M336 -15q-132 0 -201 89t-69 260q0 80 16 146.5t49.5 114t84.5 74t120 26.5q47 0 87.5 -14t71.5 -41t50 -65.5t24 -87.5h-70q-4 37 -18.5 64.5t-37 44.5t-51.5 25.5t-63 8.5q-41 0 -74.5 -15t-57.5 -40.5t-37.5 -60.5t-13.5 -74v-200q0 -41 14.5 -76t39 -60.5t57.5 -39.5 t70 -14q78 0 125.5 35t55.5 113h70q-4 -48 -22.5 -87.5t-50 -67t-74.5 -43t-95 -15.5zM245 -90h60v55h60v-110h-120v55z" />
<glyph unicode="&#x221e;" horiz-adv-x="738" d="M122 258q0 -40 23 -63.5t63 -23.5q39 0 69.5 26.5t59.5 60.5q-13 17 -27.5 33t-30.5 29t-33 20.5t-36 7.5q-42 0 -65 -24.5t-23 -65.5zM369 218q-18 -20 -35 -37t-36.5 -30t-42 -20.5t-50.5 -7.5q-31 0 -56 10.5t-42.5 29t-27 43.5t-9.5 54q0 30 10.5 55t28.5 43t43 28.5 t54 10.5t52 -8.5t42 -22.5t35.5 -32.5t33.5 -38.5q16 20 33 38.5t36 32.5t42 22.5t52 8.5t54 -10.5t43 -28.5t28.5 -43t10.5 -55q0 -29 -9.5 -54t-27.5 -43.5t-43 -29t-56 -10.5q-28 0 -50 7.5t-41.5 20.5t-36.5 30t-35 37zM616 258q0 41 -23 65.5t-65 24.5q-19 0 -36 -7.5 t-33 -20.5t-30.5 -29t-27.5 -33q29 -34 59.5 -60.5t69.5 -26.5q40 0 63 23.5t23 63.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="836" d="M505 403q-3 44 -28.5 62t-59.5 18q-35 0 -62 -24.5t-27 -68.5v-100q0 -42 25.5 -67.5t63.5 -25.5q35 0 61.5 20t29.5 62h46q-7 -62 -45.5 -91.5t-89.5 -29.5q-36 0 -62.5 13.5t-44 37.5t-26 57t-8.5 72q0 84 36.5 134t105.5 50q51 0 88.5 -30t42.5 -89h-46zM107 337 q0 -65 24.5 -121.5t67 -99t99 -67t121.5 -24.5q64 0 121 24.5t100 67t67.5 99t24.5 121.5q0 64 -24.5 121t-67.5 100t-100 67.5t-121 24.5q-65 0 -121.5 -24.5t-99 -67.5t-67 -100t-24.5 -121zM63 340q0 73 28 138t76 113t112.5 76t138.5 28q73 0 138 -28t113 -76t76 -113 t28 -138q0 -74 -28 -138.5t-76 -112.5t-113 -76t-138 -28q-74 0 -138.5 28t-112.5 76t-76 112.5t-28 138.5z" />
<glyph unicode="#" horiz-adv-x="606" d="M219 222h135l33 236h-135zM524 170h-124l-24 -170h-55l24 170h-135l-24 -170h-55l24 170h-97v52h105l35 236h-117v52h124l25 170h55l-25 -170h135l25 170h55l-25 -170h98v-52h-105l-35 -236h116v-52z" />
<glyph unicode="&#x201a;" horiz-adv-x="245" d="M165 0l-49 -85h-36l46 85h-46v85h85v-85z" />
<glyph unicode="&#x201e;" horiz-adv-x="405" d="M325 0l-49 -85h-36l46 85h-46v85h85v-85zM165 0l-49 -85h-36l46 85h-46v85h85v-85z" />
<glyph unicode="&#x201d;" horiz-adv-x="396" d="M326 595l-49 -85h-36l46 85h-46v85h85v-85zM166 595l-49 -85h-36l46 85h-46v85h85v-85z" />
<glyph unicode="&#x201c;" horiz-adv-x="404" d="M78 595l49 85h36l-46 -85h46v-85h-85v85zM238 595l49 85h36l-46 -85h46v-85h-85v85z" />
<glyph unicode="&#xd7;" d="M554 40l-40 -40l-214 214l-215 -215l-40 41l215 215l-215 215l40 40l215 -215l215 215l39 -40l-215 -215z" />
<glyph unicode="&#x2019;" horiz-adv-x="236" d="M166 595l-49 -85h-36l46 85h-46v85h85v-85z" />
<glyph unicode="&#x2018;" horiz-adv-x="244" d="M78 595l49 85h36l-46 -85h46v-85h-85v85z" />
<glyph unicode="&#xab;" horiz-adv-x="447" d="M170 71l-107 186l107 186h55l-97 -186l97 -186h-55zM322 71l-107 186l107 186h55l-97 -186l97 -186h-55z" />
<glyph unicode="_" horiz-adv-x="630" d="M80 -55h470v-40h-470v40z" />
<glyph unicode="|" horiz-adv-x="246" d="M93 850h60v-1020h-60v1020z" />
<glyph unicode="=" d="M45 126v52h510v-52h-510zM45 332v52h510v-52h-510z" />
<glyph unicode="@" horiz-adv-x="828" d="M288 77v356h250v-299h115v396h-480v-550h375v-57h-440v664h610v-510h-430zM353 134h120v242h-120v-242z" />
<glyph unicode="s" horiz-adv-x="495" d="M298 281q72 -15 108 -49.5t36 -96.5q0 -32 -13 -59t-38 -46.5t-60.5 -30.5t-80.5 -11q-41 0 -77 11t-62 32.5t-41.5 52.5t-16.5 72h65q2 -55 41 -83t92 -28q26 0 49 5.5t40 17t27 28t10 38.5q0 38 -23 57.5t-79 31.5l-84 18q-57 12 -91 44.5t-34 93.5q0 31 13 57t36 45 t56 30t72 11q34 0 66.5 -9t58 -28.5t41.5 -49.5t17 -72h-65q-2 54 -36 78t-82 24q-51 0 -82.5 -23.5t-31.5 -61.5t22 -55.5t61 -25.5z" />
<glyph unicode="g" horiz-adv-x="585" d="M431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5t-29 -42t-10 -52v-154q0 -28 10 -52t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84zM496 15q0 -53 -17.5 -92.5t-46.5 -65.5t-67 -39t-79 -13q-39 0 -74.5 7.5t-63 25 t-46 47.5t-23.5 75h65q6 -48 40 -73t98 -25t106.5 37.5t42.5 114.5v74q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v88h65v-495z" />
<glyph unicode="&#xf7;" d="M341 427h-82v83h82v-83zM341 0h-82v83h82v-83zM555 229h-510v52h510v-52z" />
<glyph unicode="&#x2212;" d="M555 229h-510v52h510v-52z" />
<glyph unicode="&#xa6;" horiz-adv-x="228" d="M84 850h60v-417h-60v417zM84 247h60v-417h-60v417z" />
<glyph unicode="&#xac;" d="M497 332h-454v52h510v-264h-56v212z" />
<glyph unicode="&#xb1;" d="M555 0h-510v52h510v-52zM45 281v52h227v177h56v-177h227v-52h-227v-177h-56v177h-227z" />
<glyph unicode="&#x3c;" d="M547 450l-456 -195l456 -195v-60l-510 219v72l510 219v-60z" />
<glyph unicode="&#x3e;" d="M52 510l510 -219v-72l-510 -219v60l456 195l-456 195v60z" />
<glyph unicode="&#x2248;" d="M545 188q0 -49 -35 -77.5t-87 -28.5q-18 0 -33 3t-31.5 9t-35.5 15.5t-44 22.5q-23 12 -38 20t-27 13t-22 7t-22 2q-26 0 -43.5 -15.5t-17.5 -41.5h-55q0 49 35 77.5t87 28.5q18 0 33 -3t31.5 -9t35.5 -15.5t44 -22.5q22 -12 37.5 -20t27.5 -13t22 -7t22 -2 q26 0 43.5 15.5t17.5 41.5h55zM545 394q0 -49 -35 -77.5t-87 -28.5q-18 0 -33 3t-31.5 9t-35.5 15.5t-44 22.5q-23 12 -38 20t-27 13t-22 7t-22 2q-26 0 -43.5 -15.5t-17.5 -41.5h-55q0 49 35 77.5t87 28.5q18 0 33 -3t31.5 -9t35.5 -15.5t44 -22.5q22 -12 37.5 -20 t27.5 -13t22 -7t22 -2q26 0 43.5 15.5t17.5 41.5h55z" />
<glyph unicode="&#x2260;" d="M183 0l-44 25l58 101h-152v52h182l88 154h-270v52h300l73 126l44 -25l-58 -101h151v-52h-181l-89 -154h270v-52h-300z" />
<glyph unicode="&#x2264;" d="M553 451l-451 -144l451 -144v-59l-510 167v72l510 167v-59zM553 0h-510v52h510v-52z" />
<glyph unicode="&#x2265;" d="M47 510l510 -167v-72l-510 -167v59l451 144l-451 144v59zM47 52h510v-52h-510v52z" />
<glyph unicode="&#xae;" horiz-adv-x="836" d="M107 337q0 -65 24.5 -121.5t67 -99t99 -67t121.5 -24.5q64 0 121 24.5t100 67t67.5 99t24.5 121.5q0 64 -24.5 121t-67.5 100t-100 67.5t-121 24.5q-65 0 -121.5 -24.5t-99 -67.5t-67 -100t-24.5 -121zM461 342q19 0 28 11.5t9 27.5v53q0 37 -37 37h-115v-129h115z M346 303v-133h-45v340h146q49 0 75 -26t26 -77q0 -44 -20 -69.5t-56 -31.5l79 -136h-52l-74 133h-79zM63 340q0 73 28 138t76 113t112.5 76t138.5 28q73 0 138 -28t113 -76t76 -113t28 -138q0 -74 -28 -138.5t-76 -112.5t-113 -76t-138 -28q-74 0 -138.5 28t-112.5 76 t-76 112.5t-28 138.5z" />
<glyph unicode="{" horiz-adv-x="366" d="M132 716h185v-54h-125v-361h-60v415zM132 245h-95v56h95v-56h60v-361h125v-54h-185v415z" />
<glyph unicode="}" horiz-adv-x="366" d="M234 -170h-185v54h125v361h60v-415zM234 301h95v-56h-95v56h-60v361h-125v54h185v-415z" />
<glyph unicode="&#x2030;" horiz-adv-x="1108" d="M930 33q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM1058 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39 t21 -58t6 -71.5zM601 33q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM729 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5 t39.5 -39t21 -58t6 -71.5zM179 363q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM307 505q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5 t61.5 -14.5t39.5 -39t21 -58t6 -71.5zM576 680h54l-426 -680h-54z" />
<glyph unicode="&#xa1;" horiz-adv-x="247" d="M138 352l18 -149v-373h-65v373l18 149h29zM166 425h-85v85h85v-85z" />
<glyph unicode="&#x131;" horiz-adv-x="243" d="M89 0v510h65v-510h-65z" />
<glyph unicode="&#xbf;" horiz-adv-x="487" d="M110 -1q0 -55 34.5 -91t95.5 -36q65 0 101 38.5t41 98.5h65q-3 -44 -20 -80t-44 -61.5t-64 -39t-80 -13.5q-44 0 -79.5 14t-61 38.5t-39.5 57.5t-14 72q0 62 26 103.5t63 70.5q22 18 39 33t27.5 29.5t16 31t5.5 37.5v30h65v-30q0 -26 -6 -48t-18 -41.5t-30.5 -38.5 t-42.5 -38q-42 -34 -61 -64.5t-19 -72.5zM212 510h85v-85h-85v85z" />
<glyph unicode="&#xbd;" horiz-adv-x="748" d="M519 42h174v-42h-243v40l145 139q21 20 31.5 37t10.5 40q0 27 -16 44t-48 17q-31 0 -50.5 -18.5t-19.5 -51.5h-50q0 54 34.5 82.5t86.5 28.5q53 0 83 -27.5t30 -75.5q0 -35 -17 -61t-38 -46l-113 -105v-1zM547 680h54l-426 -680h-54zM208 330h-50v258h-111v42h111v50h50 v-350z" />
<glyph unicode="&#xbc;" horiz-adv-x="722" d="M567 280l-108 -154h110v154h-2zM569 0v84h-157v42l159 224h48v-224h56v-42h-56v-84h-50zM547 680h54l-426 -680h-54zM208 330h-50v258h-111v42h111v50h50v-350z" />
<glyph unicode="&#x2c7;" horiz-adv-x="286" d="M143 607l70 88h53l-85 -122h-75l-85 122h52z" />
<glyph unicode="&#xd3;" horiz-adv-x="696" d="M386 880h76l-105 -122h-53zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73t-129.5 -26 t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="&#xd4;" horiz-adv-x="696" d="M278 758h-52l85 122h75l85 -122h-53l-70 88zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112 t-87.5 -73t-129.5 -26t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="&#xd2;" horiz-adv-x="696" d="M388 758h-53l-105 122h76zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73t-129.5 -26 t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="&#xd6;" horiz-adv-x="696" d="M450 777h-68v68h68v-68zM314 777h-68v68h68v-68zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112 t-87.5 -73t-129.5 -26t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="&#x160;" horiz-adv-x="607" d="M300 792l70 88h53l-85 -122h-75l-85 122h52zM299 632q-71 0 -113 -29.5t-42 -86.5q0 -31 8.5 -50.5t24.5 -32t38.5 -20.5t50.5 -16l113 -32q89 -25 130.5 -67t41.5 -119q0 -45 -18.5 -81t-50.5 -61t-77 -38.5t-97 -13.5q-53 0 -97 12.5t-77 39t-54 66.5t-27 95h70 q8 -77 58 -113.5t125 -36.5q37 0 69.5 8.5t55.5 25t36.5 40.5t13.5 55q0 29 -8 48.5t-23.5 32.5t-37 21.5t-48.5 16.5l-135 37q-82 23 -118.5 67t-36.5 116q0 38 15.5 71t44.5 57t70.5 37.5t94.5 13.5q49 0 90 -12.5t71.5 -38t49.5 -63t24 -86.5h-70q-3 37 -17.5 63.5 t-37 42.5t-51 23.5t-59.5 7.5z" />
<glyph unicode="&#x161;" horiz-adv-x="495" d="M245 607l70 88h53l-85 -122h-75l-85 122h52zM298 281q72 -15 108 -49.5t36 -96.5q0 -32 -13 -59t-38 -46.5t-60.5 -30.5t-80.5 -11q-41 0 -77 11t-62 32.5t-41.5 52.5t-16.5 72h65q2 -55 41 -83t92 -28q26 0 49 5.5t40 17t27 28t10 38.5q0 38 -23 57.5t-79 31.5l-84 18 q-57 12 -91 44.5t-34 93.5q0 31 13 57t36 45t56 30t72 11q34 0 66.5 -9t58 -28.5t41.5 -49.5t17 -72h-65q-2 54 -36 78t-82 24q-51 0 -82.5 -23.5t-31.5 -61.5t22 -55.5t61 -25.5z" />
<glyph unicode="&#x17d;" horiz-adv-x="574" d="M283 792l70 88h53l-85 -122h-75l-85 122h52zM517 615l-384 -550h382v-65h-464v65l385 550h-369v65h450v-65z" />
<glyph unicode="&#x17e;" horiz-adv-x="468" d="M233 607l70 88h53l-85 -122h-75l-85 122h52zM416 57v-57h-366v57l276 396h-261v57h340v-57l-276 -396h287z" />
<glyph unicode="&#xb7;" horiz-adv-x="245" d="M165 214h-85v85h85v-85z" />
<glyph unicode="&#x2044;" horiz-adv-x="100" d="M236 680h54l-426 -680h-54z" />
<glyph unicode="&#x2da;" horiz-adv-x="310" d="M207 651q0 21 -14.5 36.5t-37.5 15.5t-38 -15.5t-15 -36.5t15 -37t38 -16t37.5 16t14.5 37zM244 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="540" d="M311 651q0 21 -14.5 36.5t-37.5 15.5t-38 -15.5t-15 -36.5t15 -37t38 -16t37.5 16t14.5 37zM348 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58 t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5 q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#xc5;" horiz-adv-x="623" d="M364 836q0 21 -14.5 36.5t-37.5 15.5t-38 -15.5t-15 -36.5t15 -37t38 -16t37.5 16t14.5 37zM401 836q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74 l235 680h85l235 -680h-75z" />
<glyph unicode="&#xfe;" horiz-adv-x="585" d="M89 -170v850h65v-258q50 100 167 100q49 0 87 -19.5t64 -55t39 -84.5t13 -108t-13 -108.5t-38.5 -84.5t-63.5 -54.5t-88 -19.5q-54 0 -98 25t-69 75v-258h-65zM154 213q0 -36 10.5 -67t31 -53.5t49 -35t64.5 -12.5q34 0 60.5 10.5t45.5 28.5t29 42t10 52v154q0 28 -10 52 t-29 42t-45.5 28.5t-60.5 10.5q-36 0 -64.5 -12.5t-49 -35t-31 -53.5t-10.5 -67v-84z" />
<glyph unicode="&#xd0;" horiz-adv-x="661" d="M101 680h210q146 0 214 -82.5t68 -257.5t-68 -257.5t-214 -82.5h-210v314h-71v52h71v314zM171 65h140q101 0 151.5 46t50.5 129v200q0 83 -50.5 129t-151.5 46h-140v-249h172v-52h-172v-249z" />
<glyph unicode="&#x142;" horiz-adv-x="268" d="M167 360v-360h-65v321l-76 -44v47l76 44v312h65v-273l76 43v-46z" />
<glyph unicode="&#x141;" horiz-adv-x="511" d="M112 272l-96 -55v58l96 56v349h70v-309l152 89v-58l-152 -89v-248h308v-65h-378v272z" />
<glyph unicode="&#xde;" horiz-adv-x="602" d="M379 197q42 0 68.5 25t26.5 63v119q0 38 -25 61t-70 23h-210v-291h210zM99 680h70v-127h187q106 0 149.5 -50t43.5 -157q0 -58 -13.5 -99t-39 -66.5t-63 -37t-86.5 -11.5h-178v-132h-70v680z" />
<glyph unicode="&#xf0;" horiz-adv-x="561" d="M288 553h-126v40h101l-56 87h72l56 -87h128v-40h-102l61 -89q10 -15 23.5 -37.5t25.5 -51t20.5 -64t8.5 -76.5q0 -55 -13 -100.5t-40 -78t-68.5 -50.5t-97.5 -18t-97.5 18t-68.5 50.5t-40 78t-13 100.5q0 54 14.5 99.5t41 78t64 51t84.5 18.5t78 -14zM430 294 q0 30 -11.5 54t-32 41.5t-47.5 26.5t-58 9q-32 0 -59 -9t-47 -26.5t-31.5 -41.5t-11.5 -54v-118q0 -30 11.5 -54t31.5 -41.5t47 -26.5t59 -9t59 9t47 26.5t31.5 41.5t11.5 54v118z" />
<glyph unicode="&#x2020;" horiz-adv-x="482" d="M41 510h170v170h60v-170h170v-53h-170v-547h-60v547h-170v53z" />
<glyph unicode="&#x2021;" horiz-adv-x="556" d="M78 133h170v324h-170v53h170v170h60v-170h170v-53h-170v-324h170v-53h-170v-170h-60v170h-170v53z" />
<glyph unicode="&#x2d9;" horiz-adv-x="280" d="M174 602h-68v68h68v-68z" />
<glyph unicode="&#x2022;" horiz-adv-x="533" d="M97 340q0 35 13 66t36 54t54 36.5t66 13.5t66 -13.5t54 -36.5t36.5 -54t13.5 -66t-13.5 -66t-36.5 -54t-54 -36t-66 -13t-66 13t-54 36t-36 54t-13 66z" />
<glyph unicode="&#xf8;" horiz-adv-x="560" d="M416 477q42 -35 62.5 -92.5t20.5 -129.5q0 -58 -13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20q-27 0 -50.5 5t-43.5 13l-36 -73l-43 21l38 77q-43 35 -63.5 93t-20.5 131q0 58 13 107t40 84.5t68.5 55.5t97.5 20q52 0 95 -19l36 74l43 -21zM211 57q29 -13 69 -13 q32 0 59 9.5t47 27.5t31.5 42.5t11.5 54.5v154q0 57 -39 93zM350 452q-33 14 -70 14q-32 0 -59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -58 39 -95z" />
<glyph unicode="&#xe6;" horiz-adv-x="840" d="M400 86q-34 -56 -78.5 -77t-101.5 -21q-38 0 -70 12t-52 34q-20 20 -31 49t-11 67q0 26 8 51.5t24.5 45.5t42 32.5t61.5 12.5h187v58q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5q57 0 98 -19.5 t63 -60.5q26 38 66 59t91 21q50 0 88 -20t64 -53.5t39 -77.5t13 -93v-43h-338v-55q0 -31 12 -56t31.5 -42.5t43.5 -27t49 -9.5q50 0 87.5 26.5t44.5 85.5h65q-5 -41 -22.5 -72.5t-44.5 -53t-60.5 -32.5t-70.5 -11q-61 0 -104 25t-72 73h-3zM712 330q0 31 -11 56.5 t-29.5 42.5t-43 26.5t-50.5 9.5t-50.5 -9.5t-43 -26.5t-29.5 -42.5t-11 -56.5v-38h268v38zM173 235q-23 0 -35.5 -13.5t-12.5 -36.5v-63q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v13h-206z" />
<glyph unicode="&#x153;" horiz-adv-x="900" d="M772 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30.5 -42.5t-11.5 -56.5v-38h279v38zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5 t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM460 82q-26 -44 -71.5 -69t-108.5 -25q-56 0 -97.5 20t-68.5 55.5t-40 84.5t-13 107t13 107t40 84.5t68.5 55.5t97.5 20q64 0 110 -25t72 -70q26 45 69 70t102 25q52 0 91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-349v-55 q0 -31 12 -56t32 -42.5t45.5 -27t52.5 -9.5q54 0 92 26.5t45 85.5h65q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-62 0 -105.5 25t-68.5 69z" />
<glyph unicode="&#x152;" horiz-adv-x="1040" d="M348 -15q-76 0 -129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26q68 0 117.5 -21.5t84.5 -61.5v68h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v68q-35 -40 -84.5 -61.5t-117.5 -21.5zM348 630q-44 0 -81 -13t-64 -38t-42 -60t-15 -78 v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13q41 0 77.5 13t64.5 36.5t44 57.5t16 76v214q0 42 -16 76t-44 57.5t-64.5 36.5t-77.5 13z" />
<glyph unicode="&#xc6;" horiz-adv-x="928" d="M197 238h251v377h-68zM864 680v-65h-346v-236h300v-65h-300v-249h346v-65h-416v178h-281l-86 -178h-76l330 680h529z" />
<glyph unicode="&#x22;" horiz-adv-x="356" d="M272 433h-56v262h56v-262zM140 433h-56v262h56v-262z" />
<glyph unicode="'" horiz-adv-x="224" d="M140 433h-56v262h56v-262z" />
<glyph unicode="&#xd8;" horiz-adv-x="696" d="M348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 42 -14 75.5t-39 58.5l-251 -503q44 -22 102 -22zM146 239q0 -45 16 -80.5t45 -60.5l251 505q-23 13 -51 20t-59 7q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73 t-129.5 -26q-39 0 -71.5 7t-60.5 20l-37 -76l-43 20l40 81q-58 45 -84 123t-26 180q0 79 15.5 144t49.5 112t87.5 73t129.5 26q81 0 140 -31l40 80l43 -20l-44 -88q54 -46 78.5 -122t24.5 -174z" />
<glyph unicode="&#xb0;" horiz-adv-x="404" d="M94 555q0 -23 8.5 -42.5t23 -34t34 -23t42.5 -8.5q22 0 42 8.5t34.5 23t23 34t8.5 42.5q0 22 -8.5 42t-23 34.5t-34.5 23t-42 8.5q-23 0 -42.5 -8.5t-34 -23t-23 -34.5t-8.5 -42zM62 555q0 29 11 54.5t30 44.5t44.5 30t54.5 11t54.5 -11t44.5 -30t30 -44.5t11 -54.5 t-11 -54.5t-30 -44.5t-44.5 -30t-54.5 -11t-54.5 11t-44.5 30t-30 44.5t-11 54.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="581" d="M473 680v-770h-60v718h-127v-718h-60v450h-170v320h417z" />
<glyph unicode="&#xdf;" horiz-adv-x="544" d="M249 43h7q70 0 113 32.5t43 86.5v92q0 31 -17 53.5t-52 22.5h-94v57h83q35 0 52.5 19.5t17.5 50.5v86q0 24 -9.5 41.5t-25.5 29.5t-37 18t-45 6q-55 0 -93 -32.5t-38 -95.5v-510h-65v510q0 35 13.5 68.5t39 59.5t62 41.5t81.5 15.5t80 -14t58.5 -38t35.5 -57t12 -70 q0 -59 -22 -96.5t-72 -60.5q51 -15 77.5 -56.5t26.5 -112.5q0 -47 -15.5 -85t-44.5 -64.5t-70 -41t-92 -14.5h-10v58z" />
<glyph unicode="&#xa7;" horiz-adv-x="573" d="M174 128h255v255h-255v-255zM109 70v610h384v-58h-319v-181h320v-610h-386v58h321v181h-320z" />
<glyph unicode="$" horiz-adv-x="570" d="M135 517q0 -59 33.5 -87t88.5 -42v245q-25 -3 -47 -12t-38.5 -23.5t-26.5 -35t-10 -45.5zM452 177q0 29 -8.5 49t-24 34.5t-37 23.5t-48.5 16l-27 7v-260q31 3 57.5 13t46 27t30.5 39.5t11 50.5zM257 775h50v-82q38 -4 71 -18t57.5 -38t40 -58t18.5 -79h-65 q-3 34 -13.5 57.5t-27 39t-37.5 24t-44 11.5v-256l47 -12q37 -9 67.5 -24t52 -37t33.5 -52.5t12 -71.5q0 -44 -17 -78.5t-46 -59.5t-67.5 -39t-81.5 -16v-81h-50v82q-42 4 -77.5 17.5t-63 38.5t-44.5 62t-21 88h65q5 -69 44 -104t97 -42v272l-37 10q-75 19 -112.5 67.5 t-37.5 116.5q0 39 14.5 71t39.5 55.5t59 37.5t74 17v81z" />
<glyph unicode="&#xa2;" d="M170 181q0 -26 8.5 -49t23.5 -40.5t35.5 -29.5t44.5 -16v417q-45 -8 -78.5 -42.5t-33.5 -91.5v-148zM282 -10q-48 4 -82 25.5t-56.5 56t-33 80.5t-10.5 100q0 114 46 186t136 82v82h50v-81q69 -6 116 -49.5t54 -123.5h-65q-5 54 -34.5 81t-70.5 34v-416q45 7 75.5 37.5 t33.5 81.5h65q-5 -42 -20 -73.5t-38.5 -53.5t-53 -34.5t-62.5 -15.5v-81h-50v82z" />
<glyph unicode="&#xa3;" d="M63 59h90v267h-90v59h90v118q0 37 11.5 71.5t35.5 61.5t60 43t84 16t84 -15.5t60 -41.5t35.5 -60.5t11.5 -72.5h-65q0 63 -31 97.5t-94 34.5t-95 -37t-32 -100v-115h178v-59h-178v-267h304v-59h-459v59z" />
<glyph unicode="&#xa5;" d="M97 227h170v69h-170v49h132l-166 335h72l164 -335l166 335h72l-168 -335h132v-49h-169v-69h169v-49h-169v-178h-65v178h-170v49z" />
<glyph unicode="&#x20ac;" d="M47 426h57q6 58 21.5 107t44 85t71.5 56.5t104 20.5q90 0 144 -50.5t62 -138.5h-65q-8 63 -44 97t-105 34q-41 0 -71.5 -15t-50.5 -41.5t-30 -61.5t-10 -74v-19h226l-17 -49h-209v-71h186l-17 -49h-169v-22q0 -39 11 -74t31.5 -61t51 -41.5t69.5 -15.5q134 0 149 140h65 q-8 -93 -63.5 -145.5t-149.5 -52.5q-55 0 -96.5 19t-70.5 54.5t-45.5 85.5t-22.5 113h-74l17 49h53v71h-70z" />
<glyph unicode="&#x2122;" horiz-adv-x="836" d="M660 680h87v-340h-50v296l-106 -296h-45l-105 296v-296h-50v340h89l90 -264zM70 680h254v-42h-102v-298h-50v298h-102v42z" />
<glyph unicode="&#xba;" horiz-adv-x="372" d="M304 462q0 -60 -29 -99t-89 -39t-89 39t-29 99t29 99t89 39t89 -39t29 -99zM255 503q0 26 -20.5 42.5t-48.5 16.5t-48.5 -16.5t-20.5 -42.5v-82q0 -26 20.5 -42.5t48.5 -16.5t48.5 16.5t20.5 42.5v82z" />
<glyph unicode="&#xaa;" horiz-adv-x="391" d="M169 361q31 0 49 22t18 59v12l-94 -9q-22 -2 -22 -25v-24q0 -18 13 -26.5t36 -8.5zM322 370v-40h-79l-1 49h-2q-22 -55 -83 -55q-43 0 -65 24q-21 23 -21 54q0 32 20 53.5t60 25.5l85 8v21q0 32 -15 43t-39 11t-39.5 -11t-15.5 -37h-47q0 38 29.5 61t73.5 23q42 0 71 -21 t29 -69v-140h39z" />
<glyph unicode="*" horiz-adv-x="468" d="M251 480v-140h-34v140l-122 -70l-17 30l121 70l-121 70l17 30l122 -70v140h34v-140l122 70l17 -30l-122 -70l122 -70l-17 -30z" />
<glyph unicode="^" d="M272 680h56l207 -388h-64l-172 321l-169 -321h-65z" />
<glyph unicode="~" d="M545 290q0 -49 -35 -77.5t-87 -28.5q-18 0 -33 3t-31.5 9t-35.5 15.5t-44 22.5q-23 12 -38 20t-27 13t-22 7t-22 2q-26 0 -43.5 -15.5t-17.5 -41.5h-55q0 49 35 77.5t87 28.5q18 0 33 -3t31.5 -9t35.5 -15.5t44 -22.5q22 -12 37.5 -20t27.5 -13t22 -7t22 -2 q26 0 43.5 15.5t17.5 41.5h55z" />
<glyph unicode="&#xb5;" horiz-adv-x="588" d="M165 -170h-65v680h65v-341q0 -57 25.5 -90.5t85.5 -33.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-29 0 -54 9t-45 29v-196z" />
<glyph unicode="&#x3c0;" horiz-adv-x="587" d="M377 453h-188v-453h-65v453h-90v57h498v-57h-90v-396h90v-57h-155v453z" />
<glyph unicode="&#x220f;" horiz-adv-x="706" d="M537 791h-368v-961h-65v1020h498v-1020h-65v961z" />
<glyph unicode="&#x2211;" horiz-adv-x="558" d="M126 791l307 -448l-307 -454h378v-59h-458v59l307 454l-307 448v59h458v-59h-378z" />
<glyph unicode="&#x221a;" horiz-adv-x="604" d="M170 255l107 -342l240 767h69l-271 -850h-76l-118 368h-84v57h133z" />
<glyph unicode="&#x192;" horiz-adv-x="420" d="M64 424h103l52 256h179v-57h-124l-41 -199h134v-57h-145l-108 -537h-178v57h124l96 480h-92v57z" />
<glyph unicode="&#x2206;" horiz-adv-x="654" d="M122 59h409l-205 522zM293 680h68l267 -680h-602z" />
<glyph unicode="&#x25ca;" horiz-adv-x="632" d="M603 343l-245 -430h-84l-245 430l245 430h84zM526 343l-210 372l-210 -372l210 -372z" />
<glyph unicode="&#x222b;" horiz-adv-x="375" d="M27 -113h128v963h193v-57h-128v-963h-193v57z" />
<glyph unicode="&#x2126;" horiz-adv-x="652" d="M585 416q0 -95 -36.5 -161.5t-106.5 -96.5v-99h120v-59h-185v199q29 5 54 16.5t44 29t29.5 40.5t10.5 52v158q0 34 -16 60.5t-42 44.5t-60.5 27.5t-70.5 9.5t-70.5 -9.5t-60.5 -27.5t-42 -44.5t-16 -60.5v-158q0 -29 10.5 -52t29.5 -40.5t44 -29t54 -16.5v-199h-185v59 h120v99q-70 30 -106.5 96.5t-36.5 161.5q0 64 17 115.5t49.5 88t81 56t111.5 19.5t111.5 -19.5t81 -56t49.5 -88t17 -115.5z" />
<glyph unicode="&#x2039;" horiz-adv-x="295" d="M170 71l-107 186l107 186h55l-97 -186l97 -186h-55z" />
<glyph unicode="&#x203a;" horiz-adv-x="295" d="M70 71l97 186l-97 186h55l107 -186l-107 -186h-55z" />
<glyph unicode="&#x2202;" horiz-adv-x="586" d="M261 45q91 0 134 60t43 174q-20 43 -59 73t-99 30q-75 0 -114.5 -36t-39.5 -93v-79q0 -57 37.5 -93t97.5 -36zM92 632q31 26 70 44.5t96 18.5q71 0 120 -26t79 -74t43 -116t13 -152q0 -68 -13 -129.5t-42 -108t-77.5 -74t-118.5 -27.5q-57 0 -97 20t-65.5 52.5t-37 74 t-11.5 83.5q0 52 15 93t43 69.5t67.5 44t89.5 15.5q57 0 100.5 -22t71.5 -61q0 142 -42.5 208.5t-133.5 66.5q-45 0 -77.5 -16.5t-58.5 -40.5z" />
<glyph unicode="f" horiz-adv-x="328" d="M185 510h112v-57h-112v-453h-65v453h-85v57h85v170h187v-57h-122v-113z" />
<glyph unicode="n" horiz-adv-x="561" d="M412 328q0 63 -24.5 100t-89.5 37q-32 0 -58 -13t-45.5 -35.5t-30 -54.5t-10.5 -69v-293h-65v510h65v-89q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v328z" />
<glyph unicode="&#xaf;" horiz-adv-x="310" d="M10 662h289v-43h-289v43z" />
<glyph unicode="&#xbb;" horiz-adv-x="447" d="M222 71l97 186l-97 186h55l107 -186l-107 -186h-55zM70 71l97 186l-97 186h55l107 -186l-107 -186h-55z" />
<glyph unicode="&#x2dd;" horiz-adv-x="249" d="M189 695h76l-100 -122h-53zM61 695h76l-100 -122h-53z" />
<glyph unicode=";" horiz-adv-x="245" d="M165 0l-49 -85h-36l46 85h-46v85h85v-85zM165 425h-85v85h85v-85z" />
<glyph unicode="i" horiz-adv-x="243" d="M156 601h-69v69h69v-69zM89 0v510h65v-510h-65z" />
<glyph unicode="&#x2193;" horiz-adv-x="700" d="M95 306l224 -223v597h62v-597l224 223v-88l-219 -218h-72l-219 218v88z" />
<glyph unicode="&#x2191;" horiz-adv-x="700" d="M95 462l219 218h72l219 -218v-88l-224 223v-597h-62v597l-224 -223v88z" />
<glyph unicode="&#x2dc;" horiz-adv-x="296" d="M295 672q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36z" />
<glyph unicode="&#xd1;" horiz-adv-x="689" d="M491 842q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM521 79v601h69v-680h-111l-307 602h-4v-602h-69v680h111l307 -601h4z" />
<glyph unicode="&#xe3;" horiz-adv-x="540" d="M406 672q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13 t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5 q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#xf1;" horiz-adv-x="561" d="M428 672q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM412 328q0 63 -24.5 100t-89.5 37q-32 0 -58 -13t-45.5 -35.5t-30 -54.5t-10.5 -69 v-293h-65v510h65v-89q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v328z" />
<glyph unicode="&#xf5;" horiz-adv-x="560" d="M426 672q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5 t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107t40 84.5t68.5 55.5t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20 t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#xc3;" horiz-adv-x="623" d="M459 842q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85 l235 -680h-75z" />
<glyph unicode="&#xd5;" horiz-adv-x="696" d="M495 842q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38 t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73t-129.5 -26t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112 t15.5 -144z" />
<glyph unicode="&#x2d8;" horiz-adv-x="294" d="M62 695q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42z" />
<glyph horiz-adv-x="598" d="M455 473q0 75 -42 119t-114 44t-114 -44t-42 -119v-266q0 -75 42 -119t114 -44t114 44t42 119v266zM530 340q0 -74 -9.5 -139t-35 -113t-70.5 -75.5t-116 -27.5t-116 27.5t-70.5 75.5t-35 113t-9.5 139t9.5 139t35 113t70.5 75.5t116 27.5t116 -27.5t70.5 -75.5t35 -113 t9.5 -139z" />
<glyph horiz-adv-x="384" d="M221 541h-175v59h175v80h65v-680h-65v541z" />
<glyph horiz-adv-x="560" d="M134 59h366v-59h-439v73l279 272q18 18 32.5 33.5t25 33.5t16.5 39t6 49q0 62 -36 99t-99 37q-68 0 -109 -40t-43 -111h-67q1 47 17.5 86t45.5 66.5t69 42.5t87 15q95 0 149 -52t54 -143q0 -39 -10 -69.5t-26 -55.5t-35.5 -45t-38.5 -38l-244 -231v-2z" />
<glyph horiz-adv-x="580" d="M196 319v59h149q42 0 62.5 23t20.5 57v65q0 50 -36 82t-102 32q-71 0 -109 -37t-40 -94h-67q1 37 15.5 71.5t42 60.5t67.5 41.5t91 15.5q49 0 88 -14t66.5 -38.5t42 -59t14.5 -74.5q0 -62 -23 -98t-65 -60q50 -23 75.5 -62.5t25.5 -103.5q0 -45 -16.5 -82t-46.5 -63.5 t-72 -40.5t-93 -14q-47 0 -88 13t-72 39t-49 65t-19 91h67q1 -37 13.5 -65t34.5 -47t51 -28.5t62 -9.5q35 0 63.5 10t49 27t31.5 39.5t11 47.5v72q0 34 -23.5 57t-65.5 23h-156z" />
<glyph horiz-adv-x="551" d="M111 233h232v345zM343 0v174h-301v59l300 447h66v-447h98v-59h-98v-174h-65z" />
<glyph horiz-adv-x="565" d="M71 298l26 382h367v-59h-309l-16 -248q28 41 69.5 62.5t90.5 21.5q50 0 89.5 -16.5t66.5 -46.5t41.5 -73t14.5 -95q0 -55 -16 -99.5t-45 -76t-70.5 -48.5t-93.5 -17q-102 0 -158.5 50.5t-67.5 147.5h67q8 -65 47 -102.5t109 -37.5q33 0 61 10.5t48.5 28.5t32 43.5 t11.5 55.5v87q0 58 -40.5 94.5t-110.5 36.5q-48 0 -87 -27.5t-60 -73.5h-67z" />
<glyph horiz-adv-x="592" d="M301 42q37 0 66.5 12.5t50 33.5t31 48t10.5 56v58q0 29 -10.5 54.5t-31 44t-50 29.5t-66.5 11q-38 0 -67 -11t-49.5 -29.5t-31 -44t-10.5 -54.5v-58q0 -29 10.5 -56t31 -48t49.5 -33.5t67 -12.5zM301 -15q-69 0 -114 24.5t-71.5 68.5t-37 106.5t-10.5 139.5 q0 80 10.5 148t37.5 117.5t74 77.5t119 28q95 0 146 -48t60 -131h-67q-7 52 -40.5 86.5t-103.5 34.5q-39 0 -69.5 -14.5t-50.5 -40t-30.5 -59.5t-10.5 -73v-88q27 42 71 63.5t101 21.5q53 0 94 -16.5t68.5 -46.5t42 -70.5t14.5 -88.5q0 -39 -13 -81.5t-41.5 -78t-72.5 -58 t-106 -22.5z" />
<glyph horiz-adv-x="505" d="M185 0h-73l290 621h-361v59h432v-59z" />
<glyph horiz-adv-x="596" d="M137 151q0 -44 41 -75.5t120 -31.5t120 31.5t41 75.5v66q0 44 -41 75t-120 31t-120 -31t-41 -75v-66zM439 535q0 42 -37.5 71.5t-103.5 29.5t-103.5 -29.5t-37.5 -71.5v-57q0 -43 37.5 -72t103.5 -29t103.5 29t37.5 72v57zM298 -15q-47 0 -89.5 12t-74.5 36t-51 61 t-19 88q0 63 33.5 107t95.5 65q-57 23 -83 62t-26 95q0 43 17.5 77t46.5 58t68 36.5t82 12.5t82 -12.5t68 -36.5t46.5 -58t17.5 -77q0 -56 -26 -95t-83 -62q62 -21 95.5 -65t33.5 -107q0 -51 -19 -88t-51 -61t-74.5 -36t-89.5 -12z" />
<glyph horiz-adv-x="592" d="M291 637q-38 0 -67 -12.5t-49.5 -33.5t-31 -48t-10.5 -56v-57q0 -29 10.5 -54.5t31 -44.5t49.5 -29.5t67 -10.5q37 0 66.5 10.5t50 29.5t31 44.5t10.5 54.5v58q0 29 -10.5 56t-31 47.5t-50 33t-66.5 12.5zM289 695q70 0 115.5 -24t72 -68t37 -106.5t10.5 -139.5 q0 -84 -10 -152.5t-36.5 -117.5t-72 -75.5t-115.5 -26.5q-97 0 -151 48.5t-63 131.5h67q7 -53 44 -87.5t101 -34.5q39 0 69.5 14.5t50.5 39t30.5 58.5t10.5 73v90q-28 -43 -73.5 -64t-102.5 -21q-53 0 -93 16.5t-67 46.5t-41 71t-14 90q0 39 13.5 81t41.5 77t71.5 57.5 t104.5 22.5z" />
<glyph d="M144 207q0 -61 27 -101l219 505q-38 25 -90 25q-72 0 -114 -44t-42 -119v-266zM456 473q0 60 -28 100l-220 -504q39 -25 92 -25q72 0 114 44t42 119v266zM531 340q0 -74 -9.5 -139t-35 -113t-70.5 -75.5t-116 -27.5t-116 27.5t-70.5 75.5t-35 113t-9.5 139t9.5 139 t35 113t70.5 75.5t116 27.5t116 -27.5t70.5 -75.5t35 -113t9.5 -139z" />
<glyph horiz-adv-x="598" d="M143 207q0 -61 27 -101l219 505q-38 25 -90 25q-72 0 -114 -44t-42 -119v-266zM455 473q0 60 -28 100l-220 -504q39 -25 92 -25q72 0 114 44t42 119v266zM530 340q0 -74 -9.5 -139t-35 -113t-70.5 -75.5t-116 -27.5t-116 27.5t-70.5 75.5t-35 113t-9.5 139t9.5 139 t35 113t70.5 75.5t116 27.5t116 -27.5t70.5 -75.5t35 -113t9.5 -139z" />
<glyph unicode=")" horiz-adv-x="299" d="M173 378q0 83 -35.5 164.5t-103.5 173.5h55q34 -44 61.5 -91t47 -100.5t30 -115.5t10.5 -136t-10.5 -136t-30 -115.5t-47 -100.5t-61.5 -91h-55q64 85 101.5 170t37.5 168v210z" />
<glyph unicode="&#xa4;" d="M124 292q0 -36 14 -68t38 -56t56 -38t68 -14t68 14t56 38t38 56t14 68t-14 68t-38 56t-56 38t-68 14t-68 -14t-56 -38t-38 -56t-14 -68zM442 111q-29 -23 -65.5 -36t-76.5 -13q-41 0 -77 13t-65 36l-54 -54l-39 39l54 54q-23 29 -36 65t-13 77q0 40 13 76.5t36 65.5 l-54 54l39 39l54 -54q29 23 65 36t77 13q40 0 76.5 -13t65.5 -36l54 54l39 -39l-54 -54q23 -29 36 -65.5t13 -76.5q0 -41 -13 -77t-36 -65l54 -54l-39 -39z" />
<glyph unicode="&#xb8;" horiz-adv-x="290" d="M85 -90h60v55h60v-110h-120v55z" />
<glyph horiz-adv-x="358" d="M179 363q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM307 505q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39 t21 -58t6 -71.5z" />
<glyph unicode="&#x2196;" horiz-adv-x="718" d="M95 150v309l51 51h309l62 -62h-316l422 -422l-44 -44l-422 422v-316z" />
<glyph unicode="&#x2197;" horiz-adv-x="718" d="M561 88v316l-422 -422l-44 44l422 422h-316l62 62h309l51 -51v-309z" />
<glyph unicode="&#x2198;" horiz-adv-x="718" d="M201 62h316l-422 422l44 44l422 -422v316l62 -62v-309l-51 -51h-309z" />
<glyph unicode="&#x2199;" horiz-adv-x="718" d="M455 0h-309l-51 51v309l62 62v-316l422 422l44 -44l-422 -422h316z" />
<glyph horiz-adv-x="307" d="M200 330h-50v258h-111v42h111v50h50v-350z" />
<glyph horiz-adv-x="341" d="M118 372h174v-42h-243v40l145 139q21 20 31.5 37t10.5 40q0 27 -16 44t-48 17q-31 0 -50.5 -18.5t-19.5 -51.5h-50q0 54 34.5 82.5t86.5 28.5q53 0 83 -27.5t30 -75.5q0 -35 -17 -61t-38 -46l-113 -105v-1z" />
<glyph horiz-adv-x="341" d="M193 610l-108 -154h110v154h-2zM195 330v84h-157v42l159 224h48v-224h56v-42h-56v-84h-50z" />
<glyph horiz-adv-x="337" d="M53 483l14 197h203v-42h-161l-7 -110q12 17 32.5 26.5t43.5 9.5q52 0 82.5 -32t30.5 -86q0 -57 -33 -90.5t-89 -33.5q-55 0 -86 26.5t-36 77.5h49q7 -63 72 -63q32 0 52.5 16t20.5 44v45q0 27 -19.5 41.5t-52.5 14.5q-25 0 -41 -11t-26 -30h-49z" />
<glyph horiz-adv-x="300" d="M123 330h-55l148 308h-177v42h230v-42z" />
<glyph horiz-adv-x="354" d="M103 413q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM242 600q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM177 322q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6z" />
<glyph horiz-adv-x="351" d="M174 647q-32 0 -52 -19.5t-20 -47.5v-28q0 -28 19.5 -45t52.5 -17q32 0 52.5 17t20.5 45v28q0 28 -20 47.5t-53 19.5zM174 688q70 0 98 -44t28 -130q0 -87 -30.5 -139.5t-97.5 -52.5q-55 0 -82.5 25t-32.5 70h50q3 -26 19 -40t47 -14q35 0 54.5 24.5t19.5 64.5v34 q-14 -19 -35 -28t-46 -9q-55 0 -86 32t-31 83q0 22 7.5 44.5t23.5 40t39 28.5t55 11z" />
<glyph horiz-adv-x="350" d="M177 362q33 0 53 19.5t20 47.5v29q0 28 -20 45t-53 17t-53.5 -17t-20.5 -45v-29q0 -28 20 -47.5t54 -19.5zM176 322q-35 0 -59 12.5t-38.5 36t-21 55.5t-6.5 71q0 41 7 76t22.5 60.5t40.5 40t60 14.5q54 0 81 -26t32 -69h-49q-3 28 -19 41.5t-48 13.5q-35 0 -55 -25 t-20 -60v-44q14 21 35.5 31t46.5 10q54 0 85 -31.5t31 -82.5q0 -21 -7.5 -43t-23 -40t-39 -29.5t-55.5 -11.5z" />
<glyph horiz-adv-x="329" d="M143 447q31 0 49 22t18 59v12l-94 -9q-22 -2 -22 -25v-24q0 -18 13 -26.5t36 -8.5zM296 456v-40h-79l-1 49h-2q-22 -55 -83 -55q-43 0 -65 24q-21 23 -21 54q0 32 20 53.5t60 25.5l85 8v21q0 32 -15 43t-39 11t-39.5 -11t-15.5 -37h-47q0 38 29.5 61t73.5 23q42 0 71 -21 t29 -69v-140h39z" />
<glyph horiz-adv-x="347" d="M65 416v350h47v-129q13 23 33.5 36t50.5 13q26 0 45.5 -10.5t32.5 -29.5t19.5 -44t6.5 -54t-6.5 -54t-19.5 -43.5t-32.5 -29.5t-45.5 -11q-30 0 -50.5 12.5t-33.5 35.5v-42h-47zM112 526q0 -35 18.5 -57t53.5 -22q29 0 47.5 16.5t18.5 43.5v82q0 27 -18.5 43t-47.5 16 q-35 0 -53.5 -21.5t-18.5 -56.5v-44z" />
<glyph horiz-adv-x="313" d="M47 547q0 64 29.5 101.5t85.5 37.5q42 0 71.5 -22t32.5 -71h-47q-2 34 -19.5 45.5t-40.5 11.5t-43 -15.5t-20 -47.5v-77q0 -29 19 -46t45 -17q25 0 41.5 14t19.5 44h47q-5 -50 -35.5 -72.5t-71.5 -22.5q-59 0 -86.5 38t-27.5 99z" />
<glyph horiz-adv-x="347" d="M235 416v42q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5t45.5 11q30 0 50.5 -13.5t33.5 -36.5v130h47v-350h-47zM235 569q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5 t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="320" d="M47 549q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM96 572h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15z" />
<glyph horiz-adv-x="217" d="M130 680h54v-39h-54v-225h-47v225h-43v39h43v86h107v-39h-60v-47z" />
<glyph horiz-adv-x="346" d="M282 420q0 -28 -9.5 -48t-25.5 -33t-36.5 -19.5t-42.5 -6.5q-21 0 -40 4t-34 14t-25 25.5t-13 39.5h47q3 -22 20 -33.5t43 -11.5q27 0 48 16.5t21 54.5v36q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5 t45.5 11q30 0 50.5 -13.5t33.5 -36.5v44h47v-260zM235 569q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="312" d="M112 416h-47v350h47v-350zM113 563l101 117h57l-105 -117l113 -147h-54z" />
<glyph horiz-adv-x="481" d="M260 633q8 23 28 38t49 15q35 0 58.5 -21t23.5 -72v-177h-47v172q0 29 -11 45.5t-37 16.5q-24 0 -41 -22t-17 -57v-155h-47v172q0 29 -11.5 45.5t-37.5 16.5q-23 0 -40.5 -22t-17.5 -57v-155h-47v264h47v-42q9 21 27 34.5t45 13.5t47.5 -13t28.5 -40z" />
<glyph horiz-adv-x="337" d="M112 636q10 23 29 36.5t49 13.5q38 0 61.5 -21.5t23.5 -72.5v-176h-47v171q0 28 -10.5 44.5t-40.5 16.5q-29 0 -47 -21.5t-18 -56.5v-154h-47v264h47v-44z" />
<glyph horiz-adv-x="221" d="M65 680h127v-39h-80v-225h-47v264z" />
<glyph horiz-adv-x="301" d="M179 566q77 -17 77 -77q0 -33 -29 -56t-75 -23q-45 0 -75.5 22.5t-31.5 66.5h47q1 -26 18.5 -39t41.5 -13t40.5 10.5t16.5 29.5q0 16 -10 26t-36 15l-44 9q-32 6 -49.5 24.5t-17.5 48.5q0 32 26.5 54t69.5 22q38 0 68 -20t32 -64h-47q-1 26 -16 37t-36 11 q-22 0 -36.5 -10t-14.5 -27q0 -16 8 -25t28 -13z" />
<glyph horiz-adv-x="237" d="M130 455h60v-39h-107v225h-43v39h43v86h47v-86h60v-39h-60v-186z" />
<glyph horiz-adv-x="295" d="M150 474l68 206h47l-95 -264h-44l-96 264h50l66 -206h4z" />
<glyph horiz-adv-x="431" d="M290 477h4l53 203h48l-81 -264h-47l-51 210h-4l-48 -210h-47l-81 264h51l51 -203h4l44 203h56z" />
<glyph horiz-adv-x="306" d="M126 553l-84 127h51l60 -92l59 92h51l-85 -123l93 -141h-52l-66 105l-67 -105h-51z" />
<glyph horiz-adv-x="296" d="M151 477l69 203h47l-128 -350h-89v39h59l17 49l-97 262h50l68 -203h4z" />
<glyph horiz-adv-x="290" d="M243 456v-40h-199v40l137 185h-127v39h185v-39l-138 -185h142z" />
<glyph horiz-adv-x="175" d="M116 416l-32 -56h-25l31 56h-31v56h57v-56z" />
<glyph horiz-adv-x="175" d="M116 416h-57v56h57v-56z" />
<glyph horiz-adv-x="328" d="M282 548q0 -60 -29 -99t-89 -39t-89 39t-29 99t29 99t89 39t89 -39t29 -99zM233 589q0 26 -20.5 42.5t-48.5 16.5t-48.5 -16.5t-20.5 -42.5v-82q0 -26 20.5 -42.5t48.5 -16.5t48.5 16.5t20.5 42.5v82z" />
<glyph horiz-adv-x="177" d="M113 718h-49v48h49v-48zM65 416v264h47v-264h-47z" />
<glyph horiz-adv-x="177" d="M113 718h-49v48h49v-48zM65 680h47v-350h-108v39h61v311z" />
<glyph horiz-adv-x="177" d="M65 416v350h47v-350h-47z" />
<glyph horiz-adv-x="337" d="M112 636q10 23 29 36.5t49 13.5q38 0 61.5 -21.5t23.5 -72.5v-176h-47v171q0 28 -10.5 44.5t-40.5 16.5q-29 0 -47 -21.5t-18 -56.5v-154h-47v350h47v-130z" />
<glyph horiz-adv-x="337" d="M225 460q-11 -23 -29 -36.5t-48 -13.5q-37 0 -61.5 22t-24.5 73v175h47v-170q0 -29 11 -45.5t41 -16.5q29 0 46.5 22t17.5 57v153h47v-263h-47v43z" />
<glyph horiz-adv-x="347" d="M235 330v128q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5t45.5 11q30 0 50.5 -13.5t33.5 -36.5v44h47v-350h-47zM235 569q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5 t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="347" d="M65 330v350h47v-43q13 23 33.5 36t50.5 13q26 0 45.5 -10.5t32.5 -29.5t19.5 -44t6.5 -54t-6.5 -54t-19.5 -43.5t-32.5 -29.5t-45.5 -11q-30 0 -50.5 12.5t-33.5 35.5v-128h-47zM112 526q0 -35 18.5 -57t53.5 -22q29 0 47.5 16.5t18.5 43.5v82q0 27 -18.5 43t-47.5 16 q-35 0 -53.5 -21.5t-18.5 -56.5v-44z" />
<glyph horiz-adv-x="390" d="M150 375q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM238 636q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM323 505q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5 t61.5 -14.5t39.5 -39t21 -58t6 -71.5z" />
<glyph horiz-adv-x="354" d="M45 530v36h113v114h39v-114h112v-36h-112v-114h-39v114h-113z" />
<glyph horiz-adv-x="382" d="M323 529h-264v36h264v-36z" />
<glyph horiz-adv-x="382" d="M59 480v38h264v-38h-264zM59 576v38h264v-38h-264z" />
<glyph horiz-adv-x="320" d="M47 549q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM96 572h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15zM177 775h55l-59 -62h-40z" />
<glyph horiz-adv-x="320" d="M47 549q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM96 572h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15zM185 713h-40l-59 62h55z" />
<glyph horiz-adv-x="268" d="M104 850h60v-1020h-60v1020z" />
<glyph d="M555 301h-510v52h510v-52z" />
<glyph d="M554 112l-40 -40l-214 214l-215 -215l-40 41l215 215l-215 215l40 40l215 -215l215 215l39 -40l-215 -215z" />
<glyph d="M45 300v52h227v229h56v-229h227v-52h-227v-229h-56v229h-227z" />
<glyph d="M341 499h-82v83h82v-83zM341 72h-82v83h82v-83zM555 301h-510v52h510v-52z" />
<glyph horiz-adv-x="737" d="M122 330q0 -40 23 -63.5t63 -23.5q39 0 69.5 26.5t59.5 60.5q-13 17 -27.5 33t-30.5 29t-33 20.5t-36 7.5q-42 0 -65 -24.5t-23 -65.5zM369 290q-18 -20 -35 -37t-36.5 -30t-42 -20.5t-50.5 -7.5q-31 0 -56 10.5t-42.5 29t-27 43.5t-9.5 54q0 30 10.5 55t28.5 43t43 28.5 t54 10.5t52 -8.5t42 -22.5t35.5 -32.5t33.5 -38.5q16 20 33 38.5t36 32.5t42 22.5t52 8.5t54 -10.5t43 -28.5t28.5 -43t10.5 -55q0 -29 -9.5 -54t-27.5 -43.5t-43 -29t-56 -10.5q-28 0 -50 7.5t-41.5 20.5t-36.5 30t-35 37zM616 330q0 41 -23 65.5t-65 24.5q-19 0 -36 -7.5 t-33 -20.5t-30.5 -29t-27.5 -33q29 -34 59.5 -60.5t69.5 -26.5q40 0 63 23.5t23 63.5z" />
<glyph d="M45 198v52h510v-52h-510zM45 404v52h510v-52h-510z" />
<glyph d="M540 522l-456 -195l456 -195v-60l-510 219v72l510 219v-60z" />
<glyph d="M60 582l510 -219v-72l-510 -219v60l456 195l-456 195v60z" />
<glyph horiz-adv-x="794" d="M289 155v369h250v-311h115v409h-480v-565h375v-57h-440v680h610v-525h-430zM354 213h120v253h-120v-253z" />
<glyph d="M183 72l-44 25l58 101h-152v52h182l88 154h-270v52h300l73 126l44 -25l-58 -101h151v-52h-181l-89 -154h270v-52h-300z" />
<glyph d="M555 72h-510v52h510v-52zM45 353v52h227v177h56v-177h227v-52h-227v-177h-56v177h-227z" />
<glyph d="M49 582l510 -167v-72l-510 -167v59l451 144l-451 144v59zM49 124h510v-52h-510v52z" />
<glyph d="M551 523l-451 -144l451 -144v-59l-510 167v72l510 167v-59zM551 72h-510v52h510v-52z" />
<glyph d="M545 259q0 -49 -35 -77.5t-87 -28.5q-18 0 -33 3t-31.5 9t-35.5 15.5t-44 22.5q-23 12 -38 20t-27 13t-22 7t-22 2q-26 0 -43.5 -15.5t-17.5 -41.5h-55q0 49 35 77.5t87 28.5q18 0 33 -3t31.5 -9t35.5 -15.5t44 -22.5q22 -12 37.5 -20t27.5 -13t22 -7t22 -2 q26 0 43.5 15.5t17.5 41.5h55zM545 465q0 -49 -35 -77.5t-87 -28.5q-18 0 -33 3t-31.5 9t-35.5 15.5t-44 22.5q-23 12 -38 20t-27 13t-22 7t-22 2q-26 0 -43.5 -15.5t-17.5 -41.5h-55q0 49 35 77.5t87 28.5q18 0 33 -3t31.5 -9t35.5 -15.5t44 -22.5q22 -12 37.5 -20 t27.5 -13t22 -7t22 -2q26 0 43.5 15.5t17.5 41.5h55z" />
<glyph d="M493 404h-454v52h510v-264h-56v212z" />
<glyph horiz-adv-x="452" d="M224 144l97 186l-97 186h55l107 -186l-107 -186h-55zM72 144l97 186l-97 186h55l107 -186l-107 -186h-55z" />
<glyph horiz-adv-x="300" d="M72 144l97 186l-97 186h55l107 -186l-107 -186h-55z" />
<glyph horiz-adv-x="452" d="M173 144l-107 186l107 186h55l-97 -186l97 -186h-55zM325 144l-107 186l107 186h55l-97 -186l97 -186h-55z" />
<glyph horiz-adv-x="300" d="M173 144l-107 186l107 186h55l-97 -186l97 -186h-55z" />
<glyph horiz-adv-x="342" d="M291 783v-54h-125v-778h125v-54h-185v886h185z" />
<glyph horiz-adv-x="371" d="M135 783h185v-54h-125v-361h-60v415zM135 312h-95v56h95v-56h60v-361h125v-54h-185v415z" />
<glyph horiz-adv-x="303" d="M128 235q0 -83 37.5 -168t101.5 -170h-55q-34 44 -61.5 91t-47 100.5t-30 115.5t-10.5 136t10.5 136t30 115.5t47 100.5t61.5 91h55q-68 -92 -103.5 -173.5t-35.5 -164.5v-210z" />
<glyph horiz-adv-x="342" d="M236 783v-886h-185v54h125v778h-125v54h185z" />
<glyph horiz-adv-x="371" d="M236 -103h-185v54h125v361h60v-415zM236 368h95v-56h-95v56h-60v361h-125v54h185v-415z" />
<glyph horiz-adv-x="303" d="M175 445q0 83 -35.5 164.5t-103.5 173.5h55q34 -44 61.5 -91t47 -100.5t30 -115.5t10.5 -136t-10.5 -136t-30 -115.5t-47 -100.5t-61.5 -91h-55q64 85 101.5 170t37.5 168v210z" />
<glyph horiz-adv-x="636" d="M83 365h470v-62h-470v62z" />
<glyph horiz-adv-x="1106" d="M83 365h940v-62h-940v62z" />
<glyph horiz-adv-x="446" d="M83 365h280v-62h-280v62z" />
<glyph unicode="&#xad;" horiz-adv-x="440" d="M80 302h280v-62h-280v62z" />
<glyph unicode="&#x120;" horiz-adv-x="662" d="M359 772h-68v68h68v-68zM329 -15q-128 0 -195.5 91.5t-67.5 257.5q0 81 16 147.5t49.5 114t85.5 73.5t123 26q49 0 90.5 -14.5t72.5 -41.5t50 -65.5t24 -86.5h-70q-8 72 -53.5 107.5t-120.5 35.5q-41 0 -75 -14.5t-59 -40t-39 -60t-14 -75.5v-200q0 -42 14.5 -76.5 t39 -60t58.5 -39.5t73 -14q87 0 137 47.5t50 134.5v56h-164v65h234v-353h-66l-4 77q-26 -43 -75 -67.5t-114 -24.5z" />
<glyph unicode="&#x116;" horiz-adv-x="589" d="M346 772h-68v68h68v-68zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#x100;" horiz-adv-x="623" d="M168 832h289v-43h-289v43zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#x101;" horiz-adv-x="540" d="M111 662h289v-43h-289v43zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5t75 -10.5 t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#x102;" horiz-adv-x="623" d="M227 865q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#x103;" horiz-adv-x="540" d="M173 695q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5 t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18 v47z" />
<glyph unicode="&#x106;" horiz-adv-x="633" d="M367 865h76l-105 -122h-53zM336 -15q-132 0 -201 89t-69 260q0 80 16 146.5t49.5 114t84.5 74t120 26.5q47 0 87.5 -14t71.5 -41t50 -65.5t24 -87.5h-70q-4 37 -18.5 64.5t-37 44.5t-51.5 25.5t-63 8.5q-41 0 -74.5 -15t-57.5 -40.5t-37.5 -60.5t-13.5 -74v-200 q0 -41 14.5 -76t39 -60.5t57.5 -39.5t70 -14q78 0 125.5 35t55.5 113h70q-4 -48 -22.5 -87.5t-50 -67t-74.5 -43t-95 -15.5z" />
<glyph unicode="&#x107;" horiz-adv-x="523" d="M319 695h76l-105 -122h-53zM399 348q-3 31 -14.5 53t-29.5 36.5t-41 21t-48 6.5q-26 0 -50 -9.5t-42.5 -27t-30 -43t-11.5 -56.5v-148q0 -29 10.5 -54t29 -43t43 -28.5t52.5 -10.5q27 0 51 8t42.5 23.5t29.5 38t13 51.5h65q-5 -45 -22.5 -78.5t-44.5 -55.5t-61 -33 t-71 -11q-54 0 -93 20t-64.5 55t-37.5 83.5t-12 105.5q0 61 13.5 111t39.5 85t65.5 54.5t90.5 19.5q39 0 72.5 -11t59 -33t42 -54.5t19.5 -75.5h-65z" />
<glyph unicode="&#x108;" horiz-adv-x="633" d="M261 743h-52l85 122h75l85 -122h-53l-70 88zM336 -15q-132 0 -201 89t-69 260q0 80 16 146.5t49.5 114t84.5 74t120 26.5q47 0 87.5 -14t71.5 -41t50 -65.5t24 -87.5h-70q-4 37 -18.5 64.5t-37 44.5t-51.5 25.5t-63 8.5q-41 0 -74.5 -15t-57.5 -40.5t-37.5 -60.5 t-13.5 -74v-200q0 -41 14.5 -76t39 -60.5t57.5 -39.5t70 -14q78 0 125.5 35t55.5 113h70q-4 -48 -22.5 -87.5t-50 -67t-74.5 -43t-95 -15.5z" />
<glyph unicode="&#x109;" horiz-adv-x="523" d="M197 573h-52l85 122h75l85 -122h-53l-70 88zM399 348q-3 31 -14.5 53t-29.5 36.5t-41 21t-48 6.5q-26 0 -50 -9.5t-42.5 -27t-30 -43t-11.5 -56.5v-148q0 -29 10.5 -54t29 -43t43 -28.5t52.5 -10.5q27 0 51 8t42.5 23.5t29.5 38t13 51.5h65q-5 -45 -22.5 -78.5 t-44.5 -55.5t-61 -33t-71 -11q-54 0 -93 20t-64.5 55t-37.5 83.5t-12 105.5q0 61 13.5 111t39.5 85t65.5 54.5t90.5 19.5q39 0 72.5 -11t59 -33t42 -54.5t19.5 -75.5h-65z" />
<glyph unicode="&#x10a;" horiz-adv-x="633" d="M359 772h-68v68h68v-68zM336 -15q-132 0 -201 89t-69 260q0 80 16 146.5t49.5 114t84.5 74t120 26.5q47 0 87.5 -14t71.5 -41t50 -65.5t24 -87.5h-70q-4 37 -18.5 64.5t-37 44.5t-51.5 25.5t-63 8.5q-41 0 -74.5 -15t-57.5 -40.5t-37.5 -60.5t-13.5 -74v-200 q0 -41 14.5 -76t39 -60.5t57.5 -39.5t70 -14q78 0 125.5 35t55.5 113h70q-4 -48 -22.5 -87.5t-50 -67t-74.5 -43t-95 -15.5z" />
<glyph unicode="&#x10b;" horiz-adv-x="523" d="M297 602h-68v68h68v-68zM399 348q-3 31 -14.5 53t-29.5 36.5t-41 21t-48 6.5q-26 0 -50 -9.5t-42.5 -27t-30 -43t-11.5 -56.5v-148q0 -29 10.5 -54t29 -43t43 -28.5t52.5 -10.5q27 0 51 8t42.5 23.5t29.5 38t13 51.5h65q-5 -45 -22.5 -78.5t-44.5 -55.5t-61 -33t-71 -11 q-54 0 -93 20t-64.5 55t-37.5 83.5t-12 105.5q0 61 13.5 111t39.5 85t65.5 54.5t90.5 19.5q39 0 72.5 -11t59 -33t42 -54.5t19.5 -75.5h-65z" />
<glyph unicode="&#x10c;" horiz-adv-x="633" d="M325 777l70 88h53l-85 -122h-75l-85 122h52zM336 -15q-132 0 -201 89t-69 260q0 80 16 146.5t49.5 114t84.5 74t120 26.5q47 0 87.5 -14t71.5 -41t50 -65.5t24 -87.5h-70q-4 37 -18.5 64.5t-37 44.5t-51.5 25.5t-63 8.5q-41 0 -74.5 -15t-57.5 -40.5t-37.5 -60.5 t-13.5 -74v-200q0 -41 14.5 -76t39 -60.5t57.5 -39.5t70 -14q78 0 125.5 35t55.5 113h70q-4 -48 -22.5 -87.5t-50 -67t-74.5 -43t-95 -15.5z" />
<glyph unicode="&#x10d;" horiz-adv-x="523" d="M264 607l70 88h53l-85 -122h-75l-85 122h52zM399 348q-3 31 -14.5 53t-29.5 36.5t-41 21t-48 6.5q-26 0 -50 -9.5t-42.5 -27t-30 -43t-11.5 -56.5v-148q0 -29 10.5 -54t29 -43t43 -28.5t52.5 -10.5q27 0 51 8t42.5 23.5t29.5 38t13 51.5h65q-5 -45 -22.5 -78.5 t-44.5 -55.5t-61 -33t-71 -11q-54 0 -93 20t-64.5 55t-37.5 83.5t-12 105.5q0 61 13.5 111t39.5 85t65.5 54.5t90.5 19.5q39 0 72.5 -11t59 -33t42 -54.5t19.5 -75.5h-65z" />
<glyph unicode="&#x10e;" horiz-adv-x="659" d="M312 777l70 88h53l-85 -122h-75l-85 122h52zM169 615v-550h137q97 0 151 46t54 129v200q0 83 -53.5 129t-150.5 46h-138zM309 680q146 0 214 -82.5t68 -257.5t-68 -257.5t-214 -82.5h-210v680h210z" />
<glyph unicode="&#x10f;" horiz-adv-x="585" d="M641 595l-49 -85h-36l46 85h-46v85h85v-85zM431 0v88q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v258h65v-680h-65zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5 t-46 -28.5t-29 -42t-10 -52v-154q0 -28 10 -52t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84z" />
<glyph unicode="&#x112;" horiz-adv-x="589" d="M168 832h289v-43h-289v43zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#x113;" horiz-adv-x="537" d="M126 662h289v-43h-289v43zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5t45 -27t52.5 -9.5 q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#x12a;" horiz-adv-x="268" d="M-10 832h289v-43h-289v43zM99 680h70v-680h-70v680z" />
<glyph unicode="&#x12b;" horiz-adv-x="243" d="M-23 662h289v-43h-289v43zM89 0v510h65v-510h-65z" />
<glyph unicode="&#x14c;" horiz-adv-x="696" d="M204 832h289v-43h-289v43zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73t-129.5 -26 t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="&#x14d;" horiz-adv-x="560" d="M136 662h289v-43h-289v43zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107t40 84.5t68.5 55.5 t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#x16a;" horiz-adv-x="678" d="M195 832h289v-43h-289v43zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#x16b;" horiz-adv-x="557" d="M132 662h289v-43h-289v43zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#x114;" horiz-adv-x="589" d="M226 865q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#x115;" horiz-adv-x="537" d="M185 695q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5 t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5t45 -27t52.5 -9.5q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#x11e;" horiz-adv-x="662" d="M242 865q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM329 -15q-128 0 -195.5 91.5t-67.5 257.5q0 81 16 147.5t49.5 114t85.5 73.5t123 26q49 0 90.5 -14.5t72.5 -41.5t50 -65.5t24 -86.5h-70q-8 72 -53.5 107.5 t-120.5 35.5q-41 0 -75 -14.5t-59 -40t-39 -60t-14 -75.5v-200q0 -42 14.5 -76.5t39 -60t58.5 -39.5t73 -14q87 0 137 47.5t50 134.5v56h-164v65h234v-353h-66l-4 77q-26 -43 -75 -67.5t-114 -24.5z" />
<glyph unicode="&#x11f;" horiz-adv-x="585" d="M228 695q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5t-29 -42t-10 -52v-154q0 -28 10 -52t29 -42t46 -28.5t60 -10.5 q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84zM496 15q0 -53 -17.5 -92.5t-46.5 -65.5t-67 -39t-79 -13q-39 0 -74.5 7.5t-63 25t-46 47.5t-23.5 75h65q6 -48 40 -73t98 -25t106.5 37.5t42.5 114.5v74q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5 t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v88h65v-495z" />
<glyph unicode="&#x12c;" horiz-adv-x="268" d="M49 865q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM99 680h70v-680h-70v680z" />
<glyph unicode="&#x12d;" horiz-adv-x="243" d="M36 695q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM89 0v510h65v-510h-65z" />
<glyph unicode="&#x14e;" horiz-adv-x="696" d="M263 865q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5 t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73t-129.5 -26t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="&#x14f;" horiz-adv-x="560" d="M195 695q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5 t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255q0 58 13 107t40 84.5t68.5 55.5t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#x16c;" horiz-adv-x="678" d="M254 865q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5 t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#x16d;" horiz-adv-x="557" d="M189 695q0 -41 22 -68t63 -27t63 26.5t22 68.5h42q0 -60 -34.5 -97.5t-92.5 -37.5t-92.5 37.5t-34.5 97.5h42zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32 t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#x117;" horiz-adv-x="537" d="M303 602h-68v68h68v-68zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5t45 -27t52.5 -9.5 q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#x11a;" horiz-adv-x="589" d="M312 777l70 88h53l-85 -122h-75l-85 122h52zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#x11b;" horiz-adv-x="537" d="M270 607l70 88h53l-85 -122h-75l-85 122h52zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5 t45 -27t52.5 -9.5q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#x11c;" horiz-adv-x="662" d="M263 743h-52l85 122h75l85 -122h-53l-70 88zM329 -15q-128 0 -195.5 91.5t-67.5 257.5q0 81 16 147.5t49.5 114t85.5 73.5t123 26q49 0 90.5 -14.5t72.5 -41.5t50 -65.5t24 -86.5h-70q-8 72 -53.5 107.5t-120.5 35.5q-41 0 -75 -14.5t-59 -40t-39 -60t-14 -75.5v-200 q0 -42 14.5 -76.5t39 -60t58.5 -39.5t73 -14q87 0 137 47.5t50 134.5v56h-164v65h234v-353h-66l-4 77q-26 -43 -75 -67.5t-114 -24.5z" />
<glyph unicode="&#x11d;" horiz-adv-x="585" d="M237 573h-52l85 122h75l85 -122h-53l-70 88zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5t-29 -42t-10 -52v-154q0 -28 10 -52t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84zM496 15q0 -53 -17.5 -92.5 t-46.5 -65.5t-67 -39t-79 -13q-39 0 -74.5 7.5t-63 25t-46 47.5t-23.5 75h65q6 -48 40 -73t98 -25t106.5 37.5t42.5 114.5v74q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v88h65v-495z" />
<glyph unicode="&#x121;" horiz-adv-x="585" d="M342 602h-68v68h68v-68zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5t-29 -42t-10 -52v-154q0 -28 10 -52t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84zM496 15q0 -53 -17.5 -92.5t-46.5 -65.5t-67 -39t-79 -13 q-39 0 -74.5 7.5t-63 25t-46 47.5t-23.5 75h65q6 -48 40 -73t98 -25t106.5 37.5t42.5 114.5v74q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v88h65v-495z" />
<glyph unicode="&#x124;" horiz-adv-x="689" d="M274 743h-52l85 122h75l85 -122h-53l-70 88zM520 317h-351v-317h-70v680h70v-298h351v298h70v-680h-70v317z" />
<glyph unicode="&#x125;" horiz-adv-x="561" d="M52 743h-52l85 122h75l85 -122h-53l-70 88zM412 330q0 63 -24.5 99t-89.5 36q-32 0 -58 -12.5t-45.5 -35t-30 -54t-10.5 -68.5v-295h-65v680h65v-259q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v330z" />
<glyph unicode="&#x123;" horiz-adv-x="585" d="M263 765l49 85h36l-46 -85h46v-85h-85v85zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5t-29 -42t-10 -52v-154q0 -28 10 -52t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84zM496 15q0 -53 -17.5 -92.5 t-46.5 -65.5t-67 -39t-79 -13q-39 0 -74.5 7.5t-63 25t-46 47.5t-23.5 75h65q6 -48 40 -73t98 -25t106.5 37.5t42.5 114.5v74q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v88h65v-495z" />
<glyph unicode="&#x128;" horiz-adv-x="268" d="M281 842q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM99 680h70v-680h-70v680z" />
<glyph unicode="&#x129;" horiz-adv-x="243" d="M267 672q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM89 0v510h65v-510h-65z" />
<glyph unicode="&#x130;" horiz-adv-x="268" d="M168 772h-68v68h68v-68zM99 680h70v-680h-70v680z" />
<glyph unicode="&#x133;" horiz-adv-x="498" d="M411 601h-69v69h69v-69zM344 510h65v-680h-183v57h118v623zM156 601h-69v69h69v-69zM89 0v510h65v-510h-65z" />
<glyph unicode="&#x132;" horiz-adv-x="678" d="M579 0h-289v65h219v615h70v-680zM99 680h70v-680h-70v680z" />
<glyph unicode="&#x134;" horiz-adv-x="410" d="M206 743h-52l85 122h75l85 -122h-53l-70 88zM311 0h-289v65h219v615h70v-680z" />
<glyph unicode="&#x135;" horiz-adv-x="243" d="M51 573h-52l85 122h75l85 -122h-53l-70 88zM89 510h65v-680h-183v57h118v623z" />
<glyph unicode="&#x139;" horiz-adv-x="498" d="M168 865h76l-105 -122h-53zM99 680h70v-615h308v-65h-378v680z" />
<glyph unicode="&#x143;" horiz-adv-x="689" d="M357 865h76l-105 -122h-53zM521 79v601h69v-680h-111l-307 602h-4v-602h-69v680h111l307 -601h4z" />
<glyph unicode="&#x144;" horiz-adv-x="561" d="M304 695h76l-105 -122h-53zM412 328q0 63 -24.5 100t-89.5 37q-32 0 -58 -13t-45.5 -35.5t-30 -54.5t-10.5 -69v-293h-65v510h65v-89q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v328z" />
<glyph unicode="&#x147;" horiz-adv-x="689" d="M344 777l70 88h53l-85 -122h-75l-85 122h52zM521 79v601h69v-680h-111l-307 602h-4v-602h-69v680h111l307 -601h4z" />
<glyph unicode="&#x148;" horiz-adv-x="561" d="M291 607l70 88h53l-85 -122h-75l-85 122h52zM412 328q0 63 -24.5 100t-89.5 37q-32 0 -58 -13t-45.5 -35.5t-30 -54.5t-10.5 -69v-293h-65v510h65v-89q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v328z" />
<glyph unicode="&#x150;" horiz-adv-x="696" d="M452 865h76l-100 -122h-53zM324 865h76l-100 -122h-53zM348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 43 -15.5 78t-42.5 60t-64 38t-80 13q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202q0 -44 15 -78.5t42 -59.5t64 -38t81 -13zM630 340q0 -79 -15.5 -144 t-49.5 -112t-87.5 -73t-129.5 -26t-129.5 26t-87.5 73t-49.5 112t-15.5 144t15.5 144t49.5 112t87.5 73t129.5 26t129.5 -26t87.5 -73t49.5 -112t15.5 -144z" />
<glyph unicode="&#x151;" horiz-adv-x="560" d="M384 695h76l-100 -122h-53zM256 695h76l-100 -122h-53zM429 332q0 30 -11.5 54.5t-31.5 42.5t-47 27.5t-59 9.5t-59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -30 11.5 -54.5t31.5 -42.5t47 -27.5t59 -9.5t59 9.5t47 27.5t31.5 42.5t11.5 54.5v154zM61 255 q0 58 13 107t40 84.5t68.5 55.5t97.5 20t97.5 -20t68.5 -55.5t40 -84.5t13 -107t-13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20t-97.5 20t-68.5 55.5t-40 84.5t-13 107z" />
<glyph unicode="&#x155;" horiz-adv-x="342" d="M172 695h76l-105 -122h-53zM89 510h225v-57h-160v-453h-65v510z" />
<glyph unicode="&#x154;" horiz-adv-x="614" d="M347 865h76l-105 -122h-53zM379 358q42 0 68.5 24t26.5 66v83q0 42 -26.5 63t-68.5 21h-210v-257h210zM169 296v-296h-70v680h257q106 0 149.5 -49.5t43.5 -140.5q0 -92 -43 -137t-117 -55l161 -298h-80l-157 296h-144z" />
<glyph unicode="&#x158;" horiz-adv-x="614" d="M304 777l70 88h53l-85 -122h-75l-85 122h52zM379 358q42 0 68.5 24t26.5 66v83q0 42 -26.5 63t-68.5 21h-210v-257h210zM169 296v-296h-70v680h257q106 0 149.5 -49.5t43.5 -140.5q0 -92 -43 -137t-117 -55l161 -298h-80l-157 296h-144z" />
<glyph unicode="&#x159;" horiz-adv-x="342" d="M201 607l70 88h53l-85 -122h-75l-85 122h52zM89 510h225v-57h-160v-453h-65v510z" />
<glyph unicode="&#x15a;" horiz-adv-x="607" d="M333 865h76l-105 -122h-53zM299 632q-71 0 -113 -29.5t-42 -86.5q0 -31 8.5 -50.5t24.5 -32t38.5 -20.5t50.5 -16l113 -32q89 -25 130.5 -67t41.5 -119q0 -45 -18.5 -81t-50.5 -61t-77 -38.5t-97 -13.5q-53 0 -97 12.5t-77 39t-54 66.5t-27 95h70q8 -77 58 -113.5 t125 -36.5q37 0 69.5 8.5t55.5 25t36.5 40.5t13.5 55q0 29 -8 48.5t-23.5 32.5t-37 21.5t-48.5 16.5l-135 37q-82 23 -118.5 67t-36.5 116q0 38 15.5 71t44.5 57t70.5 37.5t94.5 13.5q49 0 90 -12.5t71.5 -38t49.5 -63t24 -86.5h-70q-3 37 -17.5 63.5t-37 42.5t-51 23.5 t-59.5 7.5z" />
<glyph unicode="&#x15b;" horiz-adv-x="495" d="M271 695h76l-105 -122h-53zM298 281q72 -15 108 -49.5t36 -96.5q0 -32 -13 -59t-38 -46.5t-60.5 -30.5t-80.5 -11q-41 0 -77 11t-62 32.5t-41.5 52.5t-16.5 72h65q2 -55 41 -83t92 -28q26 0 49 5.5t40 17t27 28t10 38.5q0 38 -23 57.5t-79 31.5l-84 18q-57 12 -91 44.5 t-34 93.5q0 31 13 57t36 45t56 30t72 11q34 0 66.5 -9t58 -28.5t41.5 -49.5t17 -72h-65q-2 54 -36 78t-82 24q-51 0 -82.5 -23.5t-31.5 -61.5t22 -55.5t61 -25.5z" />
<glyph unicode="&#x15c;" horiz-adv-x="607" d="M231 743h-52l85 122h75l85 -122h-53l-70 88zM299 632q-71 0 -113 -29.5t-42 -86.5q0 -31 8.5 -50.5t24.5 -32t38.5 -20.5t50.5 -16l113 -32q89 -25 130.5 -67t41.5 -119q0 -45 -18.5 -81t-50.5 -61t-77 -38.5t-97 -13.5q-53 0 -97 12.5t-77 39t-54 66.5t-27 95h70 q8 -77 58 -113.5t125 -36.5q37 0 69.5 8.5t55.5 25t36.5 40.5t13.5 55q0 29 -8 48.5t-23.5 32.5t-37 21.5t-48.5 16.5l-135 37q-82 23 -118.5 67t-36.5 116q0 38 15.5 71t44.5 57t70.5 37.5t94.5 13.5q49 0 90 -12.5t71.5 -38t49.5 -63t24 -86.5h-70q-3 37 -17.5 63.5 t-37 42.5t-51 23.5t-59.5 7.5z" />
<glyph unicode="&#x15d;" horiz-adv-x="495" d="M175 573h-52l85 122h75l85 -122h-53l-70 88zM298 281q72 -15 108 -49.5t36 -96.5q0 -32 -13 -59t-38 -46.5t-60.5 -30.5t-80.5 -11q-41 0 -77 11t-62 32.5t-41.5 52.5t-16.5 72h65q2 -55 41 -83t92 -28q26 0 49 5.5t40 17t27 28t10 38.5q0 38 -23 57.5t-79 31.5l-84 18 q-57 12 -91 44.5t-34 93.5q0 31 13 57t36 45t56 30t72 11q34 0 66.5 -9t58 -28.5t41.5 -49.5t17 -72h-65q-2 54 -36 78t-82 24q-51 0 -82.5 -23.5t-31.5 -61.5t22 -55.5t61 -25.5z" />
<glyph unicode="&#x164;" horiz-adv-x="520" d="M260 777l70 88h53l-85 -122h-75l-85 122h52zM22 680h476v-65h-203v-615h-70v615h-203v65z" />
<glyph unicode="&#x168;" horiz-adv-x="678" d="M486 842q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51 t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#x169;" horiz-adv-x="557" d="M423 672q0 -38 -20 -58.5t-56 -20.5q-20 0 -39.5 7.5t-38.5 16.5t-36 16.5t-31 7.5q-36 0 -36 -38h-36q0 38 20 58.5t56 20.5q19 0 39 -7.5t39 -16.5t36 -16.5t31 -7.5q36 0 36 38h36zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65 v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#x16e;" horiz-adv-x="678" d="M391 821q0 21 -14.5 36.5t-37.5 15.5t-38 -15.5t-15 -36.5t15 -37t38 -16t37.5 16t14.5 37zM428 821q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18 t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#x16f;" horiz-adv-x="557" d="M328 651q0 21 -14.5 36.5t-37.5 15.5t-38 -15.5t-15 -36.5t15 -37t38 -16t37.5 16t14.5 37zM365 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5 t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#x170;" horiz-adv-x="678" d="M443 865h76l-100 -122h-53zM315 865h76l-100 -122h-53zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#x171;" horiz-adv-x="557" d="M384 695h76l-100 -122h-53zM256 695h76l-100 -122h-53zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#x174;" horiz-adv-x="915" d="M387 743h-52l85 122h75l85 -122h-53l-70 88zM275 80l137 600h90l139 -600h4l146 600h76l-178 -680h-94l-136 574h-4l-135 -574h-94l-178 680h75l148 -600h4z" />
<glyph unicode="&#x175;" horiz-adv-x="743" d="M303 573h-52l85 122h75l85 -122h-53l-70 88zM227 95l104 415h83l105 -415h4l114 415h69l-155 -510h-66l-111 429h-4l-111 -429h-67l-155 510h71l115 -415h4z" />
<glyph unicode="&#x176;" horiz-adv-x="574" d="M217 743h-52l85 122h75l85 -122h-53l-70 88zM28 680h78l181 -340l184 340h76l-225 -410v-270h-70v270z" />
<glyph unicode="&#x177;" horiz-adv-x="510" d="M187 573h-52l85 122h75l85 -122h-53l-70 88zM259 95l147 415h72l-250 -680h-153v57h108l41 113l-189 510h75l145 -415h4z" />
<glyph unicode="&#x179;" horiz-adv-x="574" d="M327 865h76l-105 -122h-53zM517 615l-384 -550h382v-65h-464v65l385 550h-369v65h450v-65z" />
<glyph unicode="&#x17a;" horiz-adv-x="468" d="M270 695h76l-105 -122h-53zM416 57v-57h-366v57l276 396h-261v57h340v-57l-276 -396h287z" />
<glyph unicode="&#x17b;" horiz-adv-x="574" d="M317 772h-68v68h68v-68zM517 615l-384 -550h382v-65h-464v65l385 550h-369v65h450v-65z" />
<glyph unicode="&#x17c;" horiz-adv-x="468" d="M267 602h-68v68h68v-68zM416 57v-57h-366v57l276 396h-261v57h340v-57l-276 -396h287z" />
<glyph unicode="&#x1e81;" horiz-adv-x="743" d="M419 573h-53l-105 122h76zM227 95l104 415h83l105 -415h4l114 415h69l-155 -510h-66l-111 429h-4l-111 -429h-67l-155 510h71l115 -415h4z" />
<glyph unicode="&#x1e83;" horiz-adv-x="743" d="M402 695h76l-105 -122h-53zM227 95l104 415h83l105 -415h4l114 415h69l-155 -510h-66l-111 429h-4l-111 -429h-67l-155 510h71l115 -415h4z" />
<glyph unicode="&#x1e85;" horiz-adv-x="743" d="M474 592h-68v68h68v-68zM338 592h-68v68h68v-68zM227 95l104 415h83l105 -415h4l114 415h69l-155 -510h-66l-111 429h-4l-111 -429h-67l-155 510h71l115 -415h4z" />
<glyph unicode="&#x1ef3;" horiz-adv-x="510" d="M297 573h-53l-105 122h76zM259 95l147 415h72l-250 -680h-153v57h108l41 113l-189 510h75l145 -415h4z" />
<glyph unicode="&#x1e80;" horiz-adv-x="915" d="M508 743h-53l-105 122h76zM275 80l137 600h90l139 -600h4l146 600h76l-178 -680h-94l-136 574h-4l-135 -574h-94l-178 680h75l148 -600h4z" />
<glyph unicode="&#x1e82;" horiz-adv-x="915" d="M491 865h76l-105 -122h-53zM275 80l137 600h90l139 -600h4l146 600h76l-178 -680h-94l-136 574h-4l-135 -574h-94l-178 680h75l148 -600h4z" />
<glyph unicode="&#x1e84;" horiz-adv-x="915" d="M559 762h-68v68h68v-68zM423 762h-68v68h68v-68zM275 80l137 600h90l139 -600h4l146 600h76l-178 -680h-94l-136 574h-4l-135 -574h-94l-178 680h75l148 -600h4z" />
<glyph unicode="&#x1ef2;" horiz-adv-x="574" d="M328 743h-53l-105 122h76zM28 680h78l181 -340l184 340h76l-225 -410v-270h-70v270z" />
<glyph unicode="&#x1fc;" horiz-adv-x="928" d="M473 865h76l-105 -122h-53zM197 238h251v377h-68zM864 680v-65h-346v-236h300v-65h-300v-249h346v-65h-416v178h-281l-86 -178h-76l330 680h529z" />
<glyph unicode="&#x13a;" horiz-adv-x="247" d="M155 865h76l-105 -122h-53zM91 0v680h65v-680h-65z" />
<glyph unicode="&#x14a;" horiz-adv-x="689" d="M168 0h-69v680h70l348 -549h4l-1 549h70v-850h-289v65h219v105l-348 549h-4v-549z" />
<glyph unicode="&#x14b;" horiz-adv-x="561" d="M477 -170h-183v57h118v441q0 63 -24.5 100t-89.5 37q-32 0 -58 -13t-45.5 -35.5t-30 -54.5t-10.5 -69v-293h-65v510h65v-89q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-510z" />
<glyph unicode="&#x165;" horiz-adv-x="357" d="M434 595l-49 -85h-36l46 85h-46v85h85v-85zM185 57h122v-57h-187v453h-85v57h85v170h65v-170h122v-57h-122v-396z" />
<glyph unicode="&#x1fe;" horiz-adv-x="696" d="M348 50q43 0 80 13t64 37.5t42.5 59.5t15.5 79v202q0 42 -14 75.5t-39 58.5l-251 -503q44 -22 102 -22zM146 239q0 -45 16 -80.5t45 -60.5l251 505q-23 13 -51 20t-59 7q-44 0 -81 -13t-64 -38t-42 -60t-15 -78v-202zM630 340q0 -79 -15.5 -144t-49.5 -112t-87.5 -73 t-129.5 -26q-39 0 -71.5 7t-60.5 20l-37 -76l-43 20l40 81q-58 45 -84 123t-26 180q0 79 15.5 144t49.5 112t87.5 73t129.5 26q81 0 140 -31l40 80l43 -20l-44 -88q54 -46 78.5 -122t24.5 -174zM386 880h76l-105 -122h-53z" />
<glyph unicode="&#x1ff;" horiz-adv-x="560" d="M416 477q42 -35 62.5 -92.5t20.5 -129.5q0 -58 -13 -107t-40 -84.5t-68.5 -55.5t-97.5 -20q-27 0 -50.5 5t-43.5 13l-36 -73l-43 21l38 77q-43 35 -63.5 93t-20.5 131q0 58 13 107t40 84.5t68.5 55.5t97.5 20q52 0 95 -19l36 74l43 -21zM211 57q29 -13 69 -13 q32 0 59 9.5t47 27.5t31.5 42.5t11.5 54.5v154q0 57 -39 93zM350 452q-33 14 -70 14q-32 0 -59 -9.5t-47 -27.5t-31.5 -42.5t-11.5 -54.5v-154q0 -58 39 -95zM330 695h76l-105 -122h-53z" />
<glyph unicode="&#x1fd;" horiz-adv-x="840" d="M467 695h76l-105 -122h-53zM400 86q-34 -56 -78.5 -77t-101.5 -21q-38 0 -70 12t-52 34q-20 20 -31 49t-11 67q0 26 8 51.5t24.5 45.5t42 32.5t61.5 12.5h187v58q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5 t75.5 11.5q57 0 98 -19.5t63 -60.5q26 38 66 59t91 21q50 0 88 -20t64 -53.5t39 -77.5t13 -93v-43h-338v-55q0 -31 12 -56t31.5 -42.5t43.5 -27t49 -9.5q50 0 87.5 26.5t44.5 85.5h65q-5 -41 -22.5 -72.5t-44.5 -53t-60.5 -32.5t-70.5 -11q-61 0 -104 25t-72 73h-3zM712 330 q0 31 -11 56.5t-29.5 42.5t-43 26.5t-50.5 9.5t-50.5 -9.5t-43 -26.5t-29.5 -42.5t-11 -56.5v-38h268v38zM173 235q-23 0 -35.5 -13.5t-12.5 -36.5v-63q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v13h-206z" />
<glyph unicode="&#x110;" horiz-adv-x="661" d="M101 680h210q146 0 214 -82.5t68 -257.5t-68 -257.5t-214 -82.5h-210v314h-71v52h71v314zM171 65h140q101 0 151.5 46t50.5 129v200q0 83 -50.5 129t-151.5 46h-140v-249h172v-52h-172v-249z" />
<glyph unicode="&#x111;" horiz-adv-x="585" d="M496 0h-65v88q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v150h-150v40h150v68h65v-68h93v-40h-93v-572zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5 t-29 -42t-10 -52v-154q0 -28 10 -52t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84z" />
<glyph unicode="&#x13d;" horiz-adv-x="498" d="M324 595l-49 -85h-36l46 85h-46v85h85v-85zM99 680h70v-615h308v-65h-378v680z" />
<glyph unicode="&#x13e;" horiz-adv-x="245" d="M301 595l-49 -85h-36l46 85h-46v85h85v-85zM91 0v680h65v-680h-65z" />
<glyph unicode="&#x140;" horiz-adv-x="270" d="M301 413h-85v85h85v-85zM91 0v680h65v-680h-65z" />
<glyph unicode="&#x13f;" horiz-adv-x="499" d="M367 413h-85v85h85v-85zM99 680h70v-615h308v-65h-378v680z" />
<glyph unicode="&#x149;" horiz-adv-x="561" d="M412 328q0 63 -24.5 100t-89.5 37q-32 0 -58 -13t-45.5 -35.5t-30 -54.5t-10.5 -69v-293h-65v510h65v-89q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v328zM129 681l-40 -68h-28l37 68h-37v68h68v-68z" />
<glyph unicode="&#xf6c3;" horiz-adv-x="290" d="M179 -130l-40 -68h-28l37 68h-37v68h68v-68z" />
<glyph unicode="&#x122;" horiz-adv-x="662" d="M354 -130l-40 -68h-28l37 68h-37v68h68v-68zM329 -15q-128 0 -195.5 91.5t-67.5 257.5q0 81 16 147.5t49.5 114t85.5 73.5t123 26q49 0 90.5 -14.5t72.5 -41.5t50 -65.5t24 -86.5h-70q-8 72 -53.5 107.5t-120.5 35.5q-41 0 -75 -14.5t-59 -40t-39 -60t-14 -75.5v-200 q0 -42 14.5 -76.5t39 -60t58.5 -39.5t73 -14q87 0 137 47.5t50 134.5v56h-164v65h234v-353h-66l-4 77q-26 -43 -75 -67.5t-114 -24.5z" />
<glyph unicode="&#x136;" horiz-adv-x="603" d="M321 -130l-40 -68h-28l37 68h-37v68h68v-68zM169 0h-70v680h70v-680zM171 362l293 318h93l-301 -318l323 -362h-88z" />
<glyph unicode="&#x137;" horiz-adv-x="494" d="M275 -130l-40 -68h-28l37 68h-37v68h68v-68zM154 0h-65v680h65v-680zM156 285l208 225h85l-215 -226l233 -284h-78z" />
<glyph unicode="&#x13b;" horiz-adv-x="498" d="M289 -130l-40 -68h-28l37 68h-37v68h68v-68zM99 680h70v-615h308v-65h-378v680z" />
<glyph unicode="&#x13c;" horiz-adv-x="247" d="M157 -130l-40 -68h-28l37 68h-37v68h68v-68zM91 0v680h65v-680h-65z" />
<glyph unicode="&#x145;" horiz-adv-x="689" d="M378 -130l-40 -68h-28l37 68h-37v68h68v-68zM521 79v601h69v-680h-111l-307 602h-4v-602h-69v680h111l307 -601h4z" />
<glyph unicode="&#x146;" horiz-adv-x="561" d="M317 -130l-40 -68h-28l37 68h-37v68h68v-68zM412 328q0 63 -24.5 100t-89.5 37q-32 0 -58 -13t-45.5 -35.5t-30 -54.5t-10.5 -69v-293h-65v510h65v-89q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v328z" />
<glyph unicode="&#x156;" horiz-adv-x="614" d="M338 -130l-40 -68h-28l37 68h-37v68h68v-68zM379 358q42 0 68.5 24t26.5 66v83q0 42 -26.5 63t-68.5 21h-210v-257h210zM169 296v-296h-70v680h257q106 0 149.5 -49.5t43.5 -140.5q0 -92 -43 -137t-117 -55l161 -298h-80l-157 296h-144z" />
<glyph unicode="&#x157;" horiz-adv-x="342" d="M156 -130l-40 -68h-28l37 68h-37v68h68v-68zM89 510h225v-57h-160v-453h-65v510z" />
<glyph unicode="&#x15e;" horiz-adv-x="607" d="M211 -90h60v55h60v-110h-120v55zM299 632q-71 0 -113 -29.5t-42 -86.5q0 -31 8.5 -50.5t24.5 -32t38.5 -20.5t50.5 -16l113 -32q89 -25 130.5 -67t41.5 -119q0 -45 -18.5 -81t-50.5 -61t-77 -38.5t-97 -13.5q-53 0 -97 12.5t-77 39t-54 66.5t-27 95h70q8 -77 58 -113.5 t125 -36.5q37 0 69.5 8.5t55.5 25t36.5 40.5t13.5 55q0 29 -8 48.5t-23.5 32.5t-37 21.5t-48.5 16.5l-135 37q-82 23 -118.5 67t-36.5 116q0 38 15.5 71t44.5 57t70.5 37.5t94.5 13.5q49 0 90 -12.5t71.5 -38t49.5 -63t24 -86.5h-70q-3 37 -17.5 63.5t-37 42.5t-51 23.5 t-59.5 7.5z" />
<glyph unicode="&#x15f;" horiz-adv-x="495" d="M157 -90h60v55h60v-110h-120v55zM298 281q72 -15 108 -49.5t36 -96.5q0 -32 -13 -59t-38 -46.5t-60.5 -30.5t-80.5 -11q-41 0 -77 11t-62 32.5t-41.5 52.5t-16.5 72h65q2 -55 41 -83t92 -28q26 0 49 5.5t40 17t27 28t10 38.5q0 38 -23 57.5t-79 31.5l-84 18 q-57 12 -91 44.5t-34 93.5q0 31 13 57t36 45t56 30t72 11q34 0 66.5 -9t58 -28.5t41.5 -49.5t17 -72h-65q-2 54 -36 78t-82 24q-51 0 -82.5 -23.5t-31.5 -61.5t22 -55.5t61 -25.5z" />
<glyph unicode="&#x163;" horiz-adv-x="347" d="M185 57h122v-57h-187v453h-85v57h85v170h65v-170h122v-57h-122v-396zM125 -90h60v55h60v-110h-120v55z" />
<glyph unicode="&#x162;" horiz-adv-x="520" d="M22 680h476v-65h-203v-615h-70v615h-203v65zM168 -90h60v55h60v-110h-120v55z" />
<glyph unicode="&#x21b;" horiz-adv-x="347" d="M250 -130l-40 -68h-28l37 68h-37v68h68v-68zM185 57h122v-57h-187v453h-85v57h85v170h65v-170h122v-57h-122v-396z" />
<glyph unicode="&#x21a;" horiz-adv-x="520" d="M294 -130l-40 -68h-28l37 68h-37v68h68v-68zM22 680h476v-65h-203v-615h-70v615h-203v65z" />
<glyph unicode="&#x218;" horiz-adv-x="607" d="M299 632q-71 0 -113 -29.5t-42 -86.5q0 -31 8.5 -50.5t24.5 -32t38.5 -20.5t50.5 -16l113 -32q89 -25 130.5 -67t41.5 -119q0 -45 -18.5 -81t-50.5 -61t-77 -38.5t-97 -13.5q-53 0 -97 12.5t-77 39t-54 66.5t-27 95h70q8 -77 58 -113.5t125 -36.5q37 0 69.5 8.5t55.5 25 t36.5 40.5t13.5 55q0 29 -8 48.5t-23.5 32.5t-37 21.5t-48.5 16.5l-135 37q-82 23 -118.5 67t-36.5 116q0 38 15.5 71t44.5 57t70.5 37.5t94.5 13.5q49 0 90 -12.5t71.5 -38t49.5 -63t24 -86.5h-70q-3 37 -17.5 63.5t-37 42.5t-51 23.5t-59.5 7.5zM341 -130l-40 -68h-28 l37 68h-37v68h68v-68z" />
<glyph unicode="&#x219;" horiz-adv-x="495" d="M298 281q72 -15 108 -49.5t36 -96.5q0 -32 -13 -59t-38 -46.5t-60.5 -30.5t-80.5 -11q-41 0 -77 11t-62 32.5t-41.5 52.5t-16.5 72h65q2 -55 41 -83t92 -28q26 0 49 5.5t40 17t27 28t10 38.5q0 38 -23 57.5t-79 31.5l-84 18q-57 12 -91 44.5t-34 93.5q0 31 13 57t36 45 t56 30t72 11q34 0 66.5 -9t58 -28.5t41.5 -49.5t17 -72h-65q-2 54 -36 78t-82 24q-51 0 -82.5 -23.5t-31.5 -61.5t22 -55.5t61 -25.5zM282 -130l-40 -68h-28l37 68h-37v68h68v-68z" />
<glyph unicode="&#x2219;" horiz-adv-x="245" d="M165 214h-85v85h85v-85z" />
<glyph unicode="&#x22c5;" d="M342 214h-85v85h85v-85z" />
<glyph horiz-adv-x="446" d="M83 365h280v-62h-280v62z" />
<glyph unicode="&#x126;" horiz-adv-x="695" d="M523 382v105h-351v-105h351zM30 539h72v141h70v-141h351v141h70v-141h72v-52h-72v-487h-70v317h-351v-317h-70v487h-72v52z" />
<glyph unicode="&#x127;" horiz-adv-x="561" d="M-4 572v40h93v68h65v-68h150v-40h-150v-151q21 48 60 74.5t97 26.5q37 0 67.5 -10t52.5 -32t34 -56.5t12 -83.5v-340h-65v330q0 63 -24.5 99t-89.5 36q-32 0 -58 -12.5t-45.5 -35t-30 -54t-10.5 -68.5v-295h-65v572h-93z" />
<glyph unicode="&#x138;" horiz-adv-x="494" d="M154 0h-65v510h65v-510zM156 285l208 225h85l-215 -226l233 -284h-78z" />
<glyph unicode="&#x167;" horiz-adv-x="356" d="M42 235v40h85v178h-85v57h85v170h65v-170h122v-57h-122v-178h117v-40h-117v-178h122v-57h-187v235h-85z" />
<glyph unicode="&#x166;" horiz-adv-x="554" d="M86 366h156v249h-203v65h476v-65h-203v-249h156v-52h-156v-314h-70v314h-156v52z" />
<glyph unicode="&#x1fb;" horiz-adv-x="540" d="M307 914h76l-105 -122h-53zM307 651q0 21 -14.5 36.5t-37.5 15.5t-38 -15.5t-15 -36.5t15 -37t38 -16t37.5 16t14.5 37zM344 651q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5 v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3 q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph unicode="&#x1fa;" horiz-adv-x="623" d="M365 1064h76l-105 -122h-53zM363 811q0 21 -14.5 36.5t-37.5 15.5t-38 -15.5t-15 -36.5t15 -37t38 -16t37.5 16t14.5 37zM400 811q0 -34 -23.5 -60t-65.5 -26t-65.5 26t-23.5 60q0 33 23.5 59.5t65.5 26.5t65.5 -26.5t23.5 -59.5zM310 602l-122 -365h246l-120 365h-4z M454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#x2db;" horiz-adv-x="290" d="M205 -145h-120v55l60 55h50l-50 -55h60v-55z" />
<glyph unicode="&#x119;" horiz-adv-x="537" d="M304 -145h-120v55l60 55h50l-50 -55h60v-55zM474 157q-5 -41 -22.5 -72.5t-45 -53t-62 -32.5t-73.5 -11q-54 0 -94 20t-65.5 55t-38 84t-12.5 108q0 58 13 107t39.5 84.5t65.5 55.5t91 20t91 -20t65 -53.5t39.5 -77.5t13.5 -93v-43h-348v-55q0 -31 12 -56t31.5 -42.5 t45 -27t52.5 -9.5q54 0 92 26.5t45 85.5h65zM409 330q0 31 -11 56.5t-30 42.5t-44.5 26.5t-53.5 9.5t-53.5 -9.5t-44.5 -26.5t-30 -42.5t-11 -56.5v-38h278v38z" />
<glyph unicode="&#x118;" horiz-adv-x="589" d="M347 -145h-120v55l60 55h50l-50 -55h60v-55zM99 680h426v-65h-356v-236h310v-65h-310v-249h356v-65h-426v680z" />
<glyph unicode="&#x12e;" horiz-adv-x="268" d="M163 -145h-120v55l60 55h50l-50 -55h60v-55zM99 680h70v-680h-70v680z" />
<glyph unicode="&#x12f;" horiz-adv-x="243" d="M151 -145h-120v55l60 55h50l-50 -55h60v-55zM156 601h-69v69h69v-69zM89 0v510h65v-510h-65z" />
<glyph unicode="&#x172;" horiz-adv-x="678" d="M367 -145h-120v55l60 55h50l-50 -55h60v-55zM516 680h69v-438q0 -60 -18 -107.5t-50.5 -80.5t-78 -51t-99.5 -18t-99.5 18t-78 51t-50.5 80.5t-18 107.5v438h70v-445q0 -42 13 -76.5t36.5 -59t55.5 -37.5t71 -13t71.5 13t56 37t36.5 58.5t13 77.5v445z" />
<glyph unicode="&#x173;" horiz-adv-x="557" d="M461 -145h-120v55l60 55h50l-50 -55h60v-55zM149 181q0 -63 24 -99.5t87 -36.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-36 0 -66 10t-52 32t-34 56.5t-12 83.5v340h65v-329z" />
<glyph unicode="&#x104;" horiz-adv-x="623" d="M573 -145h-120v55l60 55h50l-50 -55h60v-55zM310 602l-122 -365h246l-120 365h-4zM454 177h-286l-60 -177h-74l235 680h85l235 -680h-75z" />
<glyph unicode="&#x105;" horiz-adv-x="540" d="M447 -145h-120v55l60 55h50l-50 -55h60v-55zM167 228q-23 -2 -32.5 -14.5t-9.5 -35.5v-56q0 -39 28.5 -58t77.5 -19q34 0 61.5 13t47 36t29.5 56t10 72v25zM379 350q0 34 -9 56t-24.5 35t-38 18.5t-49.5 5.5q-54 0 -87 -25t-33 -77h-65q0 36 13.5 65t38.5 50t59.5 32.5 t75.5 11.5t75 -10.5t58 -31.5t37 -54t13 -78v-291h75v-57h-131l-2 99h-3q-21 -54 -63 -82.5t-99 -28.5q-38 0 -70.5 11.5t-52.5 33.5q-20 20 -30.5 48.5t-10.5 58.5q0 60 31.5 99.5t98.5 45.5l193 18v47z" />
<glyph horiz-adv-x="203" d="M97 484q0 -42 20.5 -86t52.5 -87h-43q-35 45 -57 98t-22 130q0 76 22 129t57 98h43q-35 -47 -54 -89t-19 -84v-109z" />
<glyph horiz-adv-x="203" d="M107 593q0 42 -19.5 84t-54.5 89h43q35 -45 57 -98t22 -129q0 -77 -22 -130.5t-57 -97.5h-43q33 43 53.5 87t20.5 86v109z" />
<glyph horiz-adv-x="358" d="M179 449q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM307 591q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39 t21 -58t6 -71.5z" />
<glyph horiz-adv-x="274" d="M199 416h-50v258h-111v42h111v50h50v-350z" />
<glyph horiz-adv-x="342" d="M119 458h174v-42h-243v40l145 139q21 20 31.5 37t10.5 40q0 27 -16 44t-48 17q-31 0 -50.5 -18.5t-19.5 -51.5h-50q0 54 34.5 82.5t86.5 28.5q53 0 83 -27.5t30 -75.5q0 -35 -17 -61t-38 -46l-113 -105v-1z" />
<glyph horiz-adv-x="341" d="M193 696l-108 -154h110v154h-2zM195 416v84h-157v42l159 224h48v-224h56v-42h-56v-84h-50z" />
<glyph horiz-adv-x="337" d="M53 569l14 197h203v-42h-161l-7 -110q12 17 32.5 26.5t43.5 9.5q52 0 82.5 -32t30.5 -86q0 -57 -33 -90.5t-89 -33.5q-55 0 -86 26.5t-36 77.5h49q7 -63 72 -63q32 0 52.5 16t20.5 44v45q0 27 -19.5 41.5t-52.5 14.5q-25 0 -41 -11t-26 -30h-49z" />
<glyph horiz-adv-x="350" d="M177 442q33 0 53 19.5t20 47.5v29q0 28 -20 45t-53 17t-53.5 -17t-20.5 -45v-29q0 -28 20 -47.5t54 -19.5zM176 402q-35 0 -59 12.5t-38.5 36t-21 55.5t-6.5 71q0 41 7 76t22.5 60.5t40.5 40t60 14.5q54 0 81 -26t32 -69h-49q-3 28 -19 41.5t-48 13.5q-35 0 -55 -25 t-20 -60v-44q14 21 35.5 31t46.5 10q54 0 85 -31.5t31 -82.5q0 -21 -7.5 -43t-23 -40t-39 -29.5t-55.5 -11.5z" />
<glyph horiz-adv-x="300" d="M123 416h-55l148 308h-177v42h230v-42z" />
<glyph horiz-adv-x="354" d="M103 499q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM242 686q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM177 408q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6z" />
<glyph horiz-adv-x="351" d="M174 733q-32 0 -52 -19.5t-20 -47.5v-28q0 -28 19.5 -45t52.5 -17q32 0 52.5 17t20.5 45v28q0 28 -20 47.5t-53 19.5zM174 774q70 0 98 -44t28 -130q0 -87 -30.5 -139.5t-97.5 -52.5q-55 0 -82.5 25t-32.5 70h50q3 -26 19 -40t47 -14q35 0 54.5 24.5t19.5 64.5v34 q-14 -19 -35 -28t-46 -9q-55 0 -86 32t-31 83q0 22 7.5 44.5t23.5 40t39 28.5t55 11z" />
<glyph horiz-adv-x="358" d="M179 33q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM307 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39t21 -58 t6 -71.5z" />
<glyph horiz-adv-x="306" d="M199 0h-50v258h-111v42h111v50h50v-350z" />
<glyph horiz-adv-x="342" d="M119 42h174v-42h-243v40l145 139q21 20 31.5 37t10.5 40q0 27 -16 44t-48 17q-31 0 -50.5 -18.5t-19.5 -51.5h-50q0 54 34.5 82.5t86.5 28.5q53 0 83 -27.5t30 -75.5q0 -35 -17 -61t-38 -46l-113 -105v-1z" />
<glyph horiz-adv-x="341" d="M193 280l-108 -154h110v154h-2zM195 0v84h-157v42l159 224h48v-224h56v-42h-56v-84h-50z" />
<glyph horiz-adv-x="337" d="M53 153l14 197h203v-42h-161l-7 -110q12 17 32.5 26.5t43.5 9.5q52 0 82.5 -32t30.5 -86q0 -57 -33 -90.5t-89 -33.5q-55 0 -86 26.5t-36 77.5h49q7 -63 72 -63q32 0 52.5 16t20.5 44v45q0 27 -19.5 41.5t-52.5 14.5q-25 0 -41 -11t-26 -30h-49z" />
<glyph horiz-adv-x="350" d="M177 26q33 0 53 19.5t20 47.5v29q0 28 -20 45t-53 17t-53.5 -17t-20.5 -45v-29q0 -28 20 -47.5t54 -19.5zM176 -14q-35 0 -59 12.5t-38.5 36t-21 55.5t-6.5 71q0 41 7 76t22.5 60.5t40.5 40t60 14.5q54 0 81 -26t32 -69h-49q-3 28 -19 41.5t-48 13.5q-35 0 -55 -25 t-20 -60v-44q14 21 35.5 31t46.5 10q54 0 85 -31.5t31 -82.5q0 -21 -7.5 -43t-23 -40t-39 -29.5t-55.5 -11.5z" />
<glyph horiz-adv-x="300" d="M123 0h-55l148 308h-177v42h230v-42z" />
<glyph horiz-adv-x="354" d="M103 83q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM242 270q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM177 -8q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6z" />
<glyph horiz-adv-x="351" d="M174 317q-32 0 -52 -19.5t-20 -47.5v-28q0 -28 19.5 -45t52.5 -17q32 0 52.5 17t20.5 45v28q0 28 -20 47.5t-53 19.5zM174 358q70 0 98 -44t28 -130q0 -87 -30.5 -139.5t-97.5 -52.5q-55 0 -82.5 25t-32.5 70h50q3 -26 19 -40t47 -14q35 0 54.5 24.5t19.5 64.5v34 q-14 -19 -35 -28t-46 -9q-55 0 -86 32t-31 83q0 22 7.5 44.5t23.5 40t39 28.5t55 11z" />
<glyph horiz-adv-x="358" d="M179 -73q35 0 55 19.5t20 54.5v136q0 35 -20 54.5t-55 19.5t-55 -19.5t-20 -54.5v-136q0 -35 20 -54.5t55 -19.5zM307 69q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39t21 -58 t6 -71.5z" />
<glyph horiz-adv-x="275" d="M200 -106h-50v258h-111v42h111v50h50v-350z" />
<glyph horiz-adv-x="341" d="M118 -64h174v-42h-243v40l145 139q21 20 31.5 37t10.5 40q0 27 -16 44t-48 17q-31 0 -50.5 -18.5t-19.5 -51.5h-50q0 54 34.5 82.5t86.5 28.5q53 0 83 -27.5t30 -75.5q0 -35 -17 -61t-38 -46l-113 -105v-1z" />
<glyph horiz-adv-x="341" d="M193 174l-108 -154h110v154h-2zM195 -106v84h-157v42l159 224h48v-224h56v-42h-56v-84h-50z" />
<glyph horiz-adv-x="337" d="M53 47l14 197h203v-42h-161l-7 -110q12 17 32.5 26.5t43.5 9.5q52 0 82.5 -32t30.5 -86q0 -57 -33 -90.5t-89 -33.5q-55 0 -86 26.5t-36 77.5h49q7 -63 72 -63q32 0 52.5 16t20.5 44v45q0 27 -19.5 41.5t-52.5 14.5q-25 0 -41 -11t-26 -30h-49z" />
<glyph horiz-adv-x="350" d="M177 -80q33 0 53 19.5t20 47.5v29q0 28 -20 45t-53 17t-53.5 -17t-20.5 -45v-29q0 -28 20 -47.5t54 -19.5zM176 -120q-35 0 -59 12.5t-38.5 36t-21 55.5t-6.5 71q0 41 7 76t22.5 60.5t40.5 40t60 14.5q54 0 81 -26t32 -69h-49q-3 28 -19 41.5t-48 13.5q-35 0 -55 -25 t-20 -60v-44q14 21 35.5 31t46.5 10q54 0 85 -31.5t31 -82.5q0 -21 -7.5 -43t-23 -40t-39 -29.5t-55.5 -11.5z" />
<glyph horiz-adv-x="300" d="M123 -106h-55l148 308h-177v42h230v-42z" />
<glyph horiz-adv-x="354" d="M103 -23q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM242 164q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM177 -114q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6z" />
<glyph horiz-adv-x="351" d="M174 211q-32 0 -52 -19.5t-20 -47.5v-28q0 -28 19.5 -45t52.5 -17q32 0 52.5 17t20.5 45v28q0 28 -20 47.5t-53 19.5zM174 252q70 0 98 -44t28 -130q0 -87 -30.5 -139.5t-97.5 -52.5q-55 0 -82.5 25t-32.5 70h50q3 -26 19 -40t47 -14q35 0 54.5 24.5t19.5 64.5v34 q-14 -19 -35 -28t-46 -9q-55 0 -86 32t-31 83q0 22 7.5 44.5t23.5 40t39 28.5t55 11z" />
<glyph horiz-adv-x="356" d="M46 8v36h113v114h39v-114h112v-36h-112v-114h-39v114h-113z" />
<glyph horiz-adv-x="382" d="M323 7h-264v36h264v-36z" />
<glyph horiz-adv-x="382" d="M59 -42v38h264v-38h-264zM59 54v38h264v-38h-264z" />
<glyph horiz-adv-x="203" d="M97 -38q0 -42 20.5 -86t52.5 -87h-43q-35 45 -57 98t-22 130q0 76 22 129t57 98h43q-35 -47 -54 -89t-19 -84v-109z" />
<glyph horiz-adv-x="203" d="M107 71q0 42 -19.5 84t-54.5 89h43q35 -45 57 -98t22 -129q0 -77 -22 -130.5t-57 -97.5h-43q33 43 53.5 87t20.5 86v109z" />
<glyph unicode="&#xb9;" horiz-adv-x="302" d="M217 330h-50v258h-111v42h111v50h50v-350z" />
<glyph unicode="&#xb2;" horiz-adv-x="399" d="M147 372h174v-42h-243v40l145 139q21 20 31.5 37t10.5 40q0 27 -16 44t-48 17q-31 0 -50.5 -18.5t-19.5 -51.5h-50q0 54 34.5 82.5t86.5 28.5q53 0 83 -27.5t30 -75.5q0 -35 -17 -61t-38 -46l-113 -105v-1z" />
<glyph unicode="&#x215b;" horiz-adv-x="756" d="M502 83q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM641 270q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM576 -8q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6zM547 680h54l-426 -680h-54zM208 330h-50v258h-111v42h111v50h50v-350z" />
<glyph unicode="&#x215d;" horiz-adv-x="788" d="M534 83q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM673 270q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM608 -8q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6zM60 483l14 197h203v-42h-161l-7 -110q12 17 32.5 26.5t43.5 9.5q52 0 82.5 -32t30.5 -86q0 -57 -33 -90.5t-89 -33.5 q-55 0 -86 26.5t-36 77.5h49q7 -63 72 -63q32 0 52.5 16t20.5 44v45q0 27 -19.5 41.5t-52.5 14.5q-25 0 -41 -11t-26 -30h-49zM579 680h54l-426 -680h-54z" />
<glyph unicode="&#x215e;" horiz-adv-x="719" d="M466 83q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM605 270q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM540 -8q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6zM137 330h-55l148 308h-177v42h230v-42zM512 680h54l-426 -680h-54z" />
<glyph unicode="&#x2190;" horiz-adv-x="870" d="M401 510l-223 -224h597v-62h-597l223 -224h-88l-218 219v72l218 219h88z" />
<glyph unicode="&#x2192;" horiz-adv-x="870" d="M557 510l218 -219v-72l-218 -219h-88l223 224h-597v62h597l-223 224h88z" />
<glyph unicode="&#x2009;" horiz-adv-x="120" />
<glyph horiz-adv-x="344" d="M124 574v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49 q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81z" />
<glyph horiz-adv-x="344" d="M124 158v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49 q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81z" />
<glyph unicode="&#x2153;" horiz-adv-x="747" d="M524 157v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49 q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81zM547 680h54l-426 -680h-54zM208 330h-50v258h-111v42h111v50h50v-350z" />
<glyph horiz-adv-x="344" d="M124 52v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49 q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81z" />
<glyph unicode="&#x2154;" horiz-adv-x="791" d="M568 157v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49 q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81zM130 372h174v-42h-243v40l145 139q21 20 31.5 37t10.5 40q0 27 -16 44t-48 17q-31 0 -50.5 -18.5t-19.5 -51.5h-50q0 54 34.5 82.5t86.5 28.5q53 0 83 -27.5t30 -75.5q0 -35 -17 -61t-38 -46 l-113 -105v-1zM591 680h54l-426 -680h-54z" />
<glyph unicode="&#x215c;" horiz-adv-x="796" d="M542 83q0 -23 20 -36.5t54 -13.5t54 13.5t20 36.5v27q0 23 -20 36.5t-54 13.5t-54 -13.5t-20 -36.5v-27zM681 270q0 23 -19 35t-46 12t-46 -12t-19 -35v-23q0 -23 19 -34.5t46 -11.5t46 11.5t19 34.5v23zM616 -8q-26 0 -49.5 6t-40.5 19t-27 32t-10 45q0 32 17 54.5 t47 33.5q-54 24 -54 81q0 45 34 70t83 25t83 -25t34 -70q0 -57 -54 -81q30 -11 47 -33.5t17 -54.5q0 -26 -10 -45t-27.5 -32t-40.5 -19t-49 -6zM131 488v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22 t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81zM580 680h54l-426 -680h-54z" />
<glyph horiz-adv-x="344" d="M124 488v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49 q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81z" />
<glyph unicode="&#xbe;" horiz-adv-x="755" d="M600 280l-108 -154h110v154h-2zM602 0v84h-157v42l159 224h48v-224h56v-42h-56v-84h-50zM131 488v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5 t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81zM580 680h54l-426 -680h-54z" />
<glyph unicode="&#xb3;" horiz-adv-x="383" d="M145 488v42h74q18 0 28 10.5t10 26.5v32q0 23 -18.5 35.5t-44.5 12.5q-31 0 -48 -16t-19 -43h-50q0 20 8.5 38t23.5 32t37 22t49 8q53 0 82.5 -26.5t29.5 -68.5q0 -32 -13.5 -51.5t-36.5 -30.5q57 -20 57 -83q0 -51 -35 -78.5t-89 -27.5q-50 0 -85 26.5t-37 83.5h49 q3 -36 22.5 -52.5t50.5 -16.5q32 0 53 15.5t21 38.5v33q0 16 -10 27t-28 11h-81z" />
<glyph unicode="&#x2592;" horiz-adv-x="832" d="M704 640h-64v64h64v-64h64v-64h-64v64zM64 128h64v-64h64v-64h-64v64h-64v64zM192 128h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64zM576 640h-64v64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64zM192 256h-64v64h-64v64h64v-64h64v-64h64v-64h64 v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64zM448 640h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64zM320 640h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64 h-64v64zM192 384h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64zM192 640h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64 v64h-64v64h-64v64h-64v64zM192 512h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64z" />
<glyph unicode="&#x25a4;" horiz-adv-x="832" d="M64 704h704v-64h-704v64zM64 576h704v-64h-704v64zM64 448h704v-64h-704v64zM64 320h704v-64h-704v64zM64 192h704v-64h-704v64zM64 64h704v-64h-704v64z" />
<glyph unicode="&#x25a5;" horiz-adv-x="832" d="M64 0v704h64v-704h-64zM192 0v704h64v-704h-64zM320 0v704h64v-704h-64zM448 0v704h64v-704h-64zM576 0v704h64v-704h-64zM704 0v704h64v-704h-64z" />
<glyph unicode="&#x25a8;" horiz-adv-x="832" d="M704 704h64v-64h-64v64zM704 128h64v-64h-64v64zM640 64h64v-64h-64v64zM448 64h64v-64h-64v64zM704 320h64v-64h-64v64zM640 256h64v-64h-64v64zM576 192h64v-64h-64v64zM512 128h64v-64h-64v64zM704 512h64v-64h-64v64zM640 448h64v-64h-64v64zM576 384h64v-64h-64v64z M512 320h64v-64h-64v64zM640 640h64v-64h-64v64zM64 64h64v-64h-64v64zM576 576h64v-64h-64v64zM448 256h64v-64h-64v64zM512 512h64v-64h-64v64zM448 448h64v-64h-64v64zM384 192h64v-64h-64v64zM320 128h64v-64h-64v64zM512 704h64v-64h-64v64zM448 640h64v-64h-64v64z M384 384h64v-64h-64v64zM320 320h64v-64h-64v64zM256 64h64v-64h-64v64zM320 704h64v-64h-64v64zM384 576h64v-64h-64v64zM320 512h64v-64h-64v64zM256 256h64v-64h-64v64zM192 192h64v-64h-64v64zM256 640h64v-64h-64v64zM192 576h64v-64h-64v64zM256 448h64v-64h-64v64z M192 384h64v-64h-64v64zM128 128h64v-64h-64v64zM128 704h64v-64h-64v64zM64 640h64v-64h-64v64zM128 512h64v-64h-64v64zM64 448h64v-64h-64v64zM128 320h64v-64h-64v64zM64 256h64v-64h-64v64z" />
<glyph unicode="&#x25a7;" horiz-adv-x="832" d="M128 64h64v-64h-64v64h-64v64h64v-64zM704 640h64v-64h-64v64h-64v64h64v-64zM256 128h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64zM704 448h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64zM512 64h64v-64h-64 v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64zM704 256h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64zM640 128h64v-64h64v-64h-64v64 h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64z" />
<glyph unicode="&#x2b05;" horiz-adv-x="832" d="M192 448h64v-64h-64v64zM256 512h64v-64h-64v64zM320 576h64v-64h-64v64zM192 192h-64v64h-64v64h64v64h64v-64h576v-64h-576v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64z" />
<glyph unicode="&#x2b95;" horiz-adv-x="832" d="M640 128h-64v64h64v-64zM576 128v-64h-64v64h64zM512 64v-64h-64v64h64zM640 384h64v-64h64v-64h-64v-64h-64v64h-576v64h576v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64z" />
<glyph unicode="&#x2b0a;" horiz-adv-x="640" d="M448 128h64v320h64v-448h-448v64h320v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64z" />
<glyph unicode="&#x2b0b;" horiz-adv-x="640" d="M192 192h64v-64h-64v64zM256 256h64v-64h-64v64zM320 320h64v-64h-64v64zM384 384h64v-64h-64v64zM448 448h64v-64h-64v64zM512 512h64v-64h-64v64zM64 0v448h64v-320h64v-64h320v-64h-448z" />
<glyph unicode="&#x2b07;" horiz-adv-x="704" d="M448 128v64h64v-64h-64zM576 256v64h64v-64h-64zM512 192v64h64v-64h-64zM320 128v576h64v-576h64v-64h-64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64h64z" />
<glyph unicode="&#x2b06;" horiz-adv-x="704" d="M256 576v-64h-64v64h64zM128 448v-64h-64v64h64zM192 512v-64h-64v64h64zM512 576v-64h64v-64h64v-64h-64v64h-64v64h-64v64h64zM384 576v-576h-64v576h-64v64h64v64h64v-64h64v-64h-64z" />
<glyph unicode="&#x2717;" horiz-adv-x="704" d="M576 448h-64v64h64v-64zM192 192h64v-64h-64v64zM512 384h-64v64h64v-64zM128 128h64v-64h-64v64zM640 512h-64v64h64v-64zM64 64h64v-64h-64v64zM448 320h-64v64h64v-64zM256 256h64v-64h-64v64zM512 128h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64 v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64z" />
<glyph unicode="&#x2bbd;" horiz-adv-x="960" d="M704 448h-64v64h64v-64zM320 192h64v-64h-64v64zM640 384h-64v64h64v-64zM256 128h64v-64h-64v64zM768 512h-64v64h64v-64zM192 64h64v-64h-64v64zM576 320h-64v64h64v-64zM384 256h64v-64h-64v64zM640 128h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64 v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64zM128 -64h704v704h-704v-704zM64 704h832v-832h-832v832z" />
<glyph unicode="&#x25a6;" horiz-adv-x="960" d="M704 576h64v-64h-64v64zM192 64h64v-64h-64v64zM640 512h-64v64h64v-64h64v-64h64v-64h-64v64h-64v64zM256 128h-64v64h64v-64h64v-64h64v-64h-64v64h-64v64zM256 256h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64zM512 512h-64v64h64v-64h64 v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64zM256 384h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64zM384 512h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64 h-64v64h-64v64zM256 512h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64zM128 -64h704v704h-704v-704zM64 704h832v-832h-832v832z" />
<glyph unicode="&#x29c8;" horiz-adv-x="960" d="M256 64h448v448h-448v-448zM192 576h576v-576h-576v576zM128 -64h704v704h-704v-704zM64 704h832v-832h-832v832z" />
<glyph unicode="&#x26dd;" horiz-adv-x="960" d="M128 -128h-64v64h64v-64zM704 448h-64v64h64v-64zM384 256h64v-64h-64v64zM256 128h64v-64h-64v64zM320 192h64v-64h-64v64zM640 384h-64v64h64v-64zM576 320h-64v64h64v-64zM768 512h-64v64h64v-64zM192 64h64v-64h-64v64zM896 640h-64v64h64v-64zM640 128h64v-64h64 v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64zM192 0v-64h576v-64h-576v64h-64v64h-64v576h64v-576h64zM192 704h576v-64h64v-64h64 v-576h-64v576h-64v64h-576v64z" />
<glyph unicode="&#x230c;" horiz-adv-x="320" d="M64 64h64v-128h128v-64h-192v192zM64 704h192v-64h-128v-128h-64v192z" />
<glyph unicode="&#x230d;" horiz-adv-x="320" d="M256 -128h-192v64h128v128h64v-192zM256 512h-64v128h-128v64h192v-192z" />
<glyph unicode="&#x230f;" horiz-adv-x="320" d="M256 512h-192v64h128v128h64v-192zM256 -128h-64v128h-128v64h192v-192z" />
<glyph unicode="&#x230e;" horiz-adv-x="320" d="M64 704h64v-128h128v-64h-192v192zM64 64h192v-64h-128v-128h-64v192z" />
<glyph unicode="&#x274e;" horiz-adv-x="832" d="M256 128h64v64h-64v-64zM640 512h-64v-64h64v64zM320 192h64v64h-64v-64zM512 384h-64v-64h64v64zM128 0h64v64h-64v-64zM704 576h-64v-64h64v64zM192 64h64v64h-64v-64zM576 448h-64v-64h64v64zM576 64h64v-64h64v64h-64v64h-64v-64zM576 192h-64v64h-64v64h-64v64h-64 v64h-64v64h-64v64h-64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v64zM64 640h704v-704h-704v704z" />
<glyph unicode="&#x2347;" horiz-adv-x="832" d="M384 512h-64v-64h64v64zM320 448h-64v-64h64v64zM256 128h64v-64h64v64h-64v64h-64v-64zM256 256h448v64h-448v64h-64v-64h-64v-64h64v-64h64v64zM64 640h704v-704h-704v704z" />
<glyph unicode="&#x2348;" horiz-adv-x="832" d="M576 192h-64v-64h64v64zM512 128h-64v-64h64v64zM448 448h64v-64h64v-64h-448v-64h448v-64h64v64h64v64h-64v64h-64v64h-64v64h-64v-64zM768 -64h-704v704h704v-704z" />
<glyph unicode="&#x2357;" horiz-adv-x="832" d="M576 192h64v64h-64v-64zM576 128v64h-64v-64h64zM192 256v-64h64v-64h64v-64h64v-64h64v64h64v64h-64v448h-64v-448h-64v64h-64v64h-64zM64 -64v704h704v-704h-704z" />
<glyph unicode="&#x2350;" horiz-adv-x="832" d="M256 320v64h-64v-64h64zM320 384v64h-64v-64h64zM512 448v64h-64v64h-64v-64h-64v-64h64v-448h64v448h64v-64h64v-64h64v64h-64v64h-64zM768 640v-704h-704v704h704z" />
<glyph unicode="&#x25b3;" horiz-adv-x="704" d="M128 256h64v-128h-64v128zM256 512h64v-128h-64v128zM192 384h64v-128h-64v128zM64 128h64v-64h448v64h-64v128h-64v128h-64v128h-64v128h64v-128h64v-128h64v-128h64v-128h64v-128h-576v128z" />
<glyph unicode="&#x25bd;" horiz-adv-x="704" d="M576 384h-64v128h64v-128zM512 256h-64v128h64v-128zM448 128h-64v128h64v-128zM192 384h-64v128h-64v128h576v-128h-64v64h-448v-64h64v-128h64v-128h64v-128h64v-128h-64v128h-64v128h-64v128z" />
<glyph unicode="&#x25c1;" horiz-adv-x="768" d="M320 384v-64h-128v64h128zM576 512v-64h-128v64h128zM448 448v-64h-128v64h128zM576 128v-64h-128v64h-128v64h-128v64h-128v64h128v-64h128v-64h128v-64h128zM576 0v64h64v448h-64v64h128v-576h-128z" />
<glyph unicode="&#x25b7;" horiz-adv-x="768" d="M192 64v64h128v-64h-128zM448 192v64h128v-64h-128zM320 128v64h128v-64h-128zM64 0v576h128v-64h128v-64h128v-64h128v-64h128v-64h-128v64h-128v64h-128v64h-128v64h-64v-448h64v-64h-128z" />
<glyph unicode="&#x20de;" horiz-adv-x="768" d="M128 64h512v512h-512v-512zM64 640h640v-640h-640v640z" />
<glyph unicode="&#x20dd;" horiz-adv-x="768" d="M128 512h64v-64h-64v64zM192 576h64v-64h-64v64zM640 128h-64v64h64v-64zM576 64h-64v64h64v-64zM192 128h-64v64h-64v256h64v-256h64v-64h64v-64h256v-64h-256v64h-64v64zM576 512h64v-64h64v-256h-64v256h-64v64h-64v64h-256v64h256v-64h64v-64z" />
<glyph unicode="&#x1f4c4;" horiz-adv-x="576" d="M320 448v128h-192v-512h320v384h-128zM384 512h128v-512h-448v640h320v-128z" />
<glyph unicode="b" horiz-adv-x="585" d="M89 0v680h65v-258q50 100 167 100q49 0 87 -19.5t64 -55t39 -84.5t13 -108t-13 -108.5t-38.5 -84.5t-63.5 -54.5t-88 -19.5q-54 0 -98 25t-69 75v-88h-65zM154 213q0 -36 10.5 -67t31 -53.5t49 -35t64.5 -12.5q34 0 60.5 10.5t45.5 28.5t29 42t10 52v154q0 28 -10 52 t-29 42t-45.5 28.5t-60.5 10.5q-36 0 -64.5 -12.5t-49 -35t-31 -53.5t-10.5 -67v-84z" />
<glyph unicode="d" horiz-adv-x="585" d="M431 0v88q-25 -50 -68 -75t-97 -25q-51 0 -89 19.5t-64 54.5t-39 84.5t-13 108.5t13 108t39 84.5t63.5 55t87.5 19.5q117 0 167 -100v258h65v-680h-65zM431 297q0 36 -10.5 67t-31 53.5t-49 35t-64.5 12.5q-33 0 -60 -10.5t-46 -28.5t-29 -42t-10 -52v-154q0 -28 10 -52 t29 -42t46 -28.5t60 -10.5q36 0 64.5 12.5t49 35t31 53.5t10.5 67v84z" />
<glyph horiz-adv-x="674" d="M120 255q0 -38 12 -72.5t34 -62.5l305 305q-28 22 -62 34.5t-72 12.5q-45 0 -84.5 -17t-69 -46.5t-46.5 -69t-17 -84.5zM554 255q0 38 -12.5 72t-34.5 62l-305 -305q28 -22 62.5 -34t72.5 -12q45 0 84.5 17t69 46.5t46.5 69t17 84.5zM70 255q0 55 21 103.5t57.5 85 t85 57.5t103.5 21q48 0 91 -16.5t78 -45.5l62 62l36 -36l-62 -62q29 -35 45.5 -78t16.5 -91q0 -55 -21 -103.5t-57.5 -85t-85 -57.5t-103.5 -21q-49 0 -92 16t-78 45l-61 -61l-36 36l61 61q-29 35 -45 78t-16 92z" />
<glyph unicode="&#xf8ff;" horiz-adv-x="890" d="M105 680h680v-680h-680v680z" />
<glyph horiz-adv-x="408" d="M159 45q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM247 306q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM332 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5 t61.5 -14.5t39.5 -39t21 -58t6 -71.5z" />
<glyph horiz-adv-x="358" d="M134 -61q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM222 200q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM307 69q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5 t61.5 -14.5t39.5 -39t21 -58t6 -71.5z" />
<glyph horiz-adv-x="358" d="M134 461q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM222 722q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM307 591q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5 t61.5 -14.5t39.5 -39t21 -58t6 -71.5z" />
<glyph horiz-adv-x="243" d="M156 601h-69v69h69v-69zM89 0v510h65v-510h-65z" />
<glyph unicode="&#x3bc;" horiz-adv-x="562" d="M154 -170h-65v680h65v-341q0 -57 25.5 -90.5t85.5 -33.5q31 0 57 12.5t45 35.5t30 54.5t11 69.5v293h65v-510h-65v89q-21 -48 -59.5 -74.5t-95.5 -26.5q-29 0 -54 9t-45 29v-196z" />
<glyph unicode="&#x3a9;" horiz-adv-x="650" d="M584 416q0 -95 -36.5 -161.5t-106.5 -96.5v-99h120v-59h-185v199q29 5 54 16.5t44 29t29.5 40.5t10.5 52v158q0 34 -16 60.5t-42 44.5t-60.5 27.5t-70.5 9.5t-70.5 -9.5t-60.5 -27.5t-42 -44.5t-16 -60.5v-158q0 -29 10.5 -52t29.5 -40.5t44 -29t54 -16.5v-199h-185v59 h120v99q-70 30 -106.5 96.5t-36.5 161.5q0 64 17 115.5t49.5 88t81 56t111.5 19.5t111.5 -19.5t81 -56t49.5 -88t17 -115.5z" />
<glyph unicode="&#x394;" horiz-adv-x="660" d="M125 59h409l-205 522zM296 680h68l267 -680h-602z" />
<glyph unicode="&#x2002;" horiz-adv-x="500" />
<glyph unicode="&#x2003;" horiz-adv-x="1000" />
<glyph unicode="&#x2004;" horiz-adv-x="333" />
<glyph unicode="&#x2005;" horiz-adv-x="250" />
<glyph unicode="&#x200a;" horiz-adv-x="75" />
<glyph horiz-adv-x="175" d="M116 -106l-32 -56h-25l31 56h-31v56h57v-56z" />
<glyph horiz-adv-x="175" d="M116 -106h-57v56h57v-56z" />
<glyph horiz-adv-x="326" d="M143 -75q31 0 49 22t18 59v12l-94 -9q-22 -2 -22 -25v-24q0 -18 13 -26.5t36 -8.5zM296 -66v-40h-79l-1 49h-2q-22 -55 -83 -55q-43 0 -65 24q-21 23 -21 54q0 32 20 53.5t60 25.5l85 8v21q0 32 -15 43t-39 11t-39.5 -11t-15.5 -37h-47q0 38 29.5 61t73.5 23q42 0 71 -21 t29 -69v-140h39z" />
<glyph horiz-adv-x="345" d="M64 -106v350h47v-129q13 23 33.5 36t50.5 13q26 0 45.5 -10.5t32.5 -29.5t19.5 -44t6.5 -54t-6.5 -54t-19.5 -43.5t-32.5 -29.5t-45.5 -11q-30 0 -50.5 12.5t-33.5 35.5v-42h-47zM111 4q0 -35 18.5 -57t53.5 -22q29 0 47.5 16.5t18.5 43.5v82q0 27 -18.5 43t-47.5 16 q-35 0 -53.5 -21.5t-18.5 -56.5v-44z" />
<glyph horiz-adv-x="312" d="M46 25q0 64 29.5 101.5t85.5 37.5q42 0 71.5 -22t32.5 -71h-47q-2 34 -19.5 45.5t-40.5 11.5t-43 -15.5t-20 -47.5v-77q0 -29 19 -46t45 -17q25 0 41.5 14t19.5 44h47q-5 -50 -35.5 -72.5t-71.5 -22.5q-59 0 -86.5 38t-27.5 99z" />
<glyph horiz-adv-x="345" d="M234 -106v42q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5t45.5 11q30 0 50.5 -13.5t33.5 -36.5v130h47v-350h-47zM234 47q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5 t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="319" d="M46 27q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM95 50h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15z" />
<glyph horiz-adv-x="215" d="M129 158h54v-39h-54v-225h-47v225h-43v39h43v86h107v-39h-60v-47z" />
<glyph horiz-adv-x="345" d="M281 -102q0 -28 -9.5 -48t-25.5 -33t-36.5 -19.5t-42.5 -6.5q-21 0 -40 4t-34 14t-25 25.5t-13 39.5h47q3 -22 20 -33.5t43 -11.5q27 0 48 16.5t21 54.5v36q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5 t45.5 11q30 0 50.5 -13.5t33.5 -36.5v44h47v-260zM234 47q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="335" d="M111 114q10 23 29 36.5t49 13.5q38 0 61.5 -21.5t23.5 -72.5v-176h-47v171q0 28 -10.5 44.5t-40.5 16.5q-29 0 -47 -21.5t-18 -56.5v-154h-47v350h47v-130z" />
<glyph horiz-adv-x="175" d="M112 196h-49v48h49v-48zM64 -106v264h47v-264h-47z" />
<glyph horiz-adv-x="175" d="M112 196h-49v48h49v-48zM64 158h47v-350h-108v39h61v311z" />
<glyph horiz-adv-x="309" d="M111 -106h-47v350h47v-350zM112 41l101 117h57l-105 -117l113 -147h-54z" />
<glyph horiz-adv-x="175" d="M64 -106v350h47v-350h-47z" />
<glyph horiz-adv-x="480" d="M259 111q8 23 28 38t49 15q35 0 58.5 -21t23.5 -72v-177h-47v172q0 29 -11 45.5t-37 16.5q-24 0 -41 -22t-17 -57v-155h-47v172q0 29 -11.5 45.5t-37.5 16.5q-23 0 -40.5 -22t-17.5 -57v-155h-47v264h47v-42q9 21 27 34.5t45 13.5t47.5 -13t28.5 -40z" />
<glyph horiz-adv-x="335" d="M111 114q10 23 29 36.5t49 13.5q38 0 61.5 -21.5t23.5 -72.5v-176h-47v171q0 28 -10.5 44.5t-40.5 16.5q-29 0 -47 -21.5t-18 -56.5v-154h-47v264h47v-44z" />
<glyph horiz-adv-x="328" d="M282 26q0 -60 -29 -99t-89 -39t-89 39t-29 99t29 99t89 39t89 -39t29 -99zM233 67q0 26 -20.5 42.5t-48.5 16.5t-48.5 -16.5t-20.5 -42.5v-82q0 -26 20.5 -42.5t48.5 -16.5t48.5 16.5t20.5 42.5v82z" />
<glyph horiz-adv-x="345" d="M64 -192v350h47v-43q13 23 33.5 36t50.5 13q26 0 45.5 -10.5t32.5 -29.5t19.5 -44t6.5 -54t-6.5 -54t-19.5 -43.5t-32.5 -29.5t-45.5 -11q-30 0 -50.5 12.5t-33.5 35.5v-128h-47zM111 4q0 -35 18.5 -57t53.5 -22q29 0 47.5 16.5t18.5 43.5v82q0 27 -18.5 43t-47.5 16 q-35 0 -53.5 -21.5t-18.5 -56.5v-44z" />
<glyph horiz-adv-x="345" d="M234 -192v128q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5t45.5 11q30 0 50.5 -13.5t33.5 -36.5v44h47v-350h-47zM234 47q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5 t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="221" d="M64 158h127v-39h-80v-225h-47v264z" />
<glyph horiz-adv-x="301" d="M179 44q77 -17 77 -77q0 -33 -29 -56t-75 -23q-45 0 -75.5 22.5t-31.5 66.5h47q1 -26 18.5 -39t41.5 -13t40.5 10.5t16.5 29.5q0 16 -10 26t-36 15l-44 9q-32 6 -49.5 24.5t-17.5 48.5q0 32 26.5 54t69.5 22q38 0 68 -20t32 -64h-47q-1 26 -16 37t-36 11q-22 0 -36.5 -10 t-14.5 -27q0 -16 8 -25t28 -13z" />
<glyph horiz-adv-x="237" d="M129 -67h60v-39h-107v225h-43v39h43v86h47v-86h60v-39h-60v-186z" />
<glyph horiz-adv-x="335" d="M224 -62q-11 -23 -29 -36.5t-48 -13.5q-37 0 -61.5 22t-24.5 73v175h47v-170q0 -29 11 -45.5t41 -16.5q29 0 46.5 22t17.5 57v153h47v-263h-47v43z" />
<glyph horiz-adv-x="293" d="M149 -48l68 206h47l-95 -264h-44l-96 264h50l66 -206h4z" />
<glyph horiz-adv-x="429" d="M289 -45h4l53 203h48l-81 -264h-47l-51 210h-4l-48 -210h-47l-81 264h51l51 -203h4l44 203h56z" />
<glyph horiz-adv-x="302" d="M124 31l-84 127h51l60 -92l59 92h51l-85 -123l93 -141h-52l-66 105l-67 -105h-51z" />
<glyph horiz-adv-x="295" d="M150 -45l69 203h47l-128 -350h-89v39h59l17 49l-97 262h50l68 -203h4z" />
<glyph horiz-adv-x="289" d="M243 -66v-40h-199v40l137 185h-127v39h185v-39l-138 -185h142z" />
<glyph horiz-adv-x="319" d="M46 27q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM95 50h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15zM176 253h55l-59 -62h-40z" />
<glyph horiz-adv-x="319" d="M46 27q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM95 50h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15zM184 191h-40l-59 62h55z" />
<glyph horiz-adv-x="368" d="M52 531v36h113v114h39v-114h112v-36h-112v-114h-39v114h-113z" />
<glyph horiz-adv-x="382" d="M323 530h-264v36h264v-36z" />
<glyph horiz-adv-x="382" d="M59 481v38h264v-38h-264zM59 577v38h264v-38h-264z" />
<glyph horiz-adv-x="197" d="M93 485q0 -42 20.5 -86t52.5 -87h-43q-35 45 -57 98t-22 130q0 76 22 129t57 98h43q-35 -47 -54 -89t-19 -84v-109z" />
<glyph horiz-adv-x="197" d="M105 594q0 42 -19.5 84t-54.5 89h43q35 -45 57 -98t22 -129q0 -77 -22 -130.5t-57 -97.5h-43q33 43 53.5 87t20.5 86v109z" />
<glyph horiz-adv-x="175" d="M116 331l-32 -56h-25l31 56h-31v56h57v-56z" />
<glyph horiz-adv-x="175" d="M116 331h-57v56h57v-56z" />
<glyph horiz-adv-x="327" d="M143 362q31 0 49 22t18 59v12l-94 -9q-22 -2 -22 -25v-24q0 -18 13 -26.5t36 -8.5zM296 371v-40h-79l-1 49h-2q-22 -55 -83 -55q-43 0 -65 24q-21 23 -21 54q0 32 20 53.5t60 25.5l85 8v21q0 32 -15 43t-39 11t-39.5 -11t-15.5 -37h-47q0 38 29.5 61t73.5 23q42 0 71 -21 t29 -69v-140h39z" />
<glyph horiz-adv-x="346" d="M64 331v350h47v-129q13 23 33.5 36t50.5 13q26 0 45.5 -10.5t32.5 -29.5t19.5 -44t6.5 -54t-6.5 -54t-19.5 -43.5t-32.5 -29.5t-45.5 -11q-30 0 -50.5 12.5t-33.5 35.5v-42h-47zM111 441q0 -35 18.5 -57t53.5 -22q29 0 47.5 16.5t18.5 43.5v82q0 27 -18.5 43t-47.5 16 q-35 0 -53.5 -21.5t-18.5 -56.5v-44z" />
<glyph horiz-adv-x="313" d="M47 462q0 64 29.5 101.5t85.5 37.5q42 0 71.5 -22t32.5 -71h-47q-2 34 -19.5 45.5t-40.5 11.5t-43 -15.5t-20 -47.5v-77q0 -29 19 -46t45 -17q25 0 41.5 14t19.5 44h47q-5 -50 -35.5 -72.5t-71.5 -22.5q-59 0 -86.5 38t-27.5 99z" />
<glyph horiz-adv-x="345" d="M234 331v42q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5t45.5 11q30 0 50.5 -13.5t33.5 -36.5v130h47v-350h-47zM234 484q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5 t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="319" d="M46 464q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM95 487h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15z" />
<glyph horiz-adv-x="215" d="M129 595h54v-39h-54v-225h-47v225h-43v39h43v86h107v-39h-60v-47z" />
<glyph horiz-adv-x="345" d="M281 335q0 -28 -9.5 -48t-25.5 -33t-36.5 -19.5t-42.5 -6.5q-21 0 -40 4t-34 14t-25 25.5t-13 39.5h47q3 -22 20 -33.5t43 -11.5q27 0 48 16.5t21 54.5v36q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5 t45.5 11q30 0 50.5 -13.5t33.5 -36.5v44h47v-260zM234 484q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="335" d="M111 551q10 23 29 36.5t49 13.5q38 0 61.5 -21.5t23.5 -72.5v-176h-47v171q0 28 -10.5 44.5t-40.5 16.5q-29 0 -47 -21.5t-18 -56.5v-154h-47v350h47v-130z" />
<glyph horiz-adv-x="175" d="M112 633h-49v48h49v-48zM64 331v264h47v-264h-47z" />
<glyph horiz-adv-x="175" d="M112 633h-49v48h49v-48zM64 595h47v-350h-108v39h61v311z" />
<glyph horiz-adv-x="310" d="M111 331h-47v350h47v-350zM112 478l101 117h57l-105 -117l113 -147h-54z" />
<glyph horiz-adv-x="175" d="M64 331v350h47v-350h-47z" />
<glyph horiz-adv-x="479" d="M259 548q8 23 28 38t49 15q35 0 58.5 -21t23.5 -72v-177h-47v172q0 29 -11 45.5t-37 16.5q-24 0 -41 -22t-17 -57v-155h-47v172q0 29 -11.5 45.5t-37.5 16.5q-23 0 -40.5 -22t-17.5 -57v-155h-47v264h47v-42q9 21 27 34.5t45 13.5t47.5 -13t28.5 -40z" />
<glyph horiz-adv-x="335" d="M111 551q10 23 29 36.5t49 13.5q38 0 61.5 -21.5t23.5 -72.5v-176h-47v171q0 28 -10.5 44.5t-40.5 16.5q-29 0 -47 -21.5t-18 -56.5v-154h-47v264h47v-44z" />
<glyph horiz-adv-x="328" d="M282 463q0 -60 -29 -99t-89 -39t-89 39t-29 99t29 99t89 39t89 -39t29 -99zM233 504q0 26 -20.5 42.5t-48.5 16.5t-48.5 -16.5t-20.5 -42.5v-82q0 -26 20.5 -42.5t48.5 -16.5t48.5 16.5t20.5 42.5v82z" />
<glyph horiz-adv-x="346" d="M64 245v350h47v-43q13 23 33.5 36t50.5 13q26 0 45.5 -10.5t32.5 -29.5t19.5 -44t6.5 -54t-6.5 -54t-19.5 -43.5t-32.5 -29.5t-45.5 -11q-30 0 -50.5 12.5t-33.5 35.5v-128h-47zM111 441q0 -35 18.5 -57t53.5 -22q29 0 47.5 16.5t18.5 43.5v82q0 27 -18.5 43t-47.5 16 q-35 0 -53.5 -21.5t-18.5 -56.5v-44z" />
<glyph horiz-adv-x="345" d="M234 245v128q-13 -23 -33 -35.5t-50 -12.5q-26 0 -46 10.5t-33 29.5t-19.5 43.5t-6.5 53.5t6.5 54.5t19.5 44t32.5 29.5t45.5 11q30 0 50.5 -13.5t33.5 -36.5v44h47v-350h-47zM234 484q0 35 -18.5 56.5t-53.5 21.5q-29 0 -47.5 -16t-18.5 -43v-81q0 -27 18.5 -43.5 t47.5 -16.5q35 0 53.5 22t18.5 57v43z" />
<glyph horiz-adv-x="221" d="M64 595h127v-39h-80v-225h-47v264z" />
<glyph horiz-adv-x="301" d="M179 481q77 -17 77 -77q0 -33 -29 -56t-75 -23q-45 0 -75.5 22.5t-31.5 66.5h47q1 -26 18.5 -39t41.5 -13t40.5 10.5t16.5 29.5q0 16 -10 26t-36 15l-44 9q-32 6 -49.5 24.5t-17.5 48.5q0 32 26.5 54t69.5 22q38 0 68 -20t32 -64h-47q-1 26 -16 37t-36 11 q-22 0 -36.5 -10t-14.5 -27q0 -16 8 -25t28 -13z" />
<glyph horiz-adv-x="237" d="M129 370h60v-39h-107v225h-43v39h43v86h47v-86h60v-39h-60v-186z" />
<glyph horiz-adv-x="335" d="M224 375q-11 -23 -29 -36.5t-48 -13.5q-37 0 -61.5 22t-24.5 73v175h47v-170q0 -29 11 -45.5t41 -16.5q29 0 46.5 22t17.5 57v153h47v-263h-47v43z" />
<glyph horiz-adv-x="292" d="M148 389l68 206h47l-95 -264h-44l-96 264h50l66 -206h4z" />
<glyph horiz-adv-x="427" d="M288 392h4l53 203h48l-81 -264h-47l-51 210h-4l-48 -210h-47l-81 264h51l51 -203h4l44 203h56z" />
<glyph horiz-adv-x="303" d="M125 468l-84 127h51l60 -92l59 92h51l-85 -123l93 -141h-52l-66 105l-67 -105h-51z" />
<glyph horiz-adv-x="294" d="M150 392l69 203h47l-128 -350h-89v39h59l17 49l-97 262h50l68 -203h4z" />
<glyph horiz-adv-x="289" d="M243 371v-40h-199v40l137 185h-127v39h185v-39l-138 -185h142z" />
<glyph horiz-adv-x="319" d="M46 464q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM95 487h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15zM176 690h55l-59 -62h-40z" />
<glyph horiz-adv-x="319" d="M46 464q0 61 29.5 99t84.5 38t84 -36t29 -89v-25h-178v-28q0 -30 20 -45.5t45 -15.5t44 13t21 40h46q-4 -42 -36 -66t-75 -24q-58 0 -86 38t-28 101zM95 487h130v15q0 30 -19.5 46.5t-45.5 16.5t-45.5 -16.5t-19.5 -46.5v-15zM184 628h-40l-59 62h55z" />
<glyph unicode="&#x2bc;" horiz-adv-x="291" d="M190 595l-49 -85h-36l46 85h-46v85h85v-85z" />
<glyph unicode="&#x2b09;" horiz-adv-x="640" d="M192 384h-64v-320h-64v448h448v-64h-320v-64h64v-64h64v-64h64v-64h64v-64h64v-64h64v-64h-64v64h-64v64h-64v64h-64v64h-64v64h-64v64z" />
<glyph unicode="&#x2b08;" horiz-adv-x="640" d="M448 320h-64v64h64v-64zM384 256h-64v64h64v-64zM320 192h-64v64h64v-64zM256 128h-64v64h64v-64zM192 64h-64v64h64v-64zM128 0h-64v64h64v-64zM576 512v-448h-64v320h-64v64h-320v64h448z" />
<glyph horiz-adv-x="780" d="M556 45q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM644 306q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM729 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5 t61.5 -14.5t39.5 -39t21 -58t6 -71.5zM134 375q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM222 636q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM307 505q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5 t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39t21 -58t6 -71.5zM576 680h54l-426 -680h-54z" />
<glyph horiz-adv-x="1108" d="M885 45q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM973 306q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM1058 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5 t61.5 -14.5t39.5 -39t21 -58t6 -71.5zM556 45q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM644 306q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM729 175q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14t-61.5 14t-39.5 39t-21 58.5t-6 71.5 t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39t21 -58t6 -71.5zM134 375q18 -12 45 -12q35 0 55 19.5t20 54.5v136q0 26 -13 46zM222 636q-19 11 -43 11q-35 0 -55 -19.5t-20 -54.5v-136q0 -27 11 -44zM307 505q0 -38 -6 -71.5t-21 -58.5t-39.5 -39t-61.5 -14 t-61.5 14t-39.5 39t-21 58.5t-6 71.5t6 71.5t21 58t39.5 39t61.5 14.5t61.5 -14.5t39.5 -39t21 -58t6 -71.5zM576 680h54l-426 -680h-54z" />
<glyph unicode="l" horiz-adv-x="247" d="M91 0v680h65v-680h-65z" />
<hkern u1="&#x20;" u2="&#x104;" k="26" />
<hkern u1="&#x20;" u2="&#x1fa;" k="26" />
<hkern u1="&#x20;" u2="&#x166;" k="27" />
<hkern u1="&#x20;" u2="&#x167;" k="10" />
<hkern u1="&#x20;" u2="&#x21a;" k="27" />
<hkern u1="&#x20;" u2="&#x21b;" k="10" />
<hkern u1="&#x20;" u2="&#x162;" k="27" />
<hkern u1="&#x20;" u2="&#x163;" k="10" />
<hkern u1="&#x20;" u2="&#x165;" k="10" />
<hkern u1="&#x20;" u2="&#x1fc;" k="31" />
<hkern u1="&#x20;" u2="&#x1ef2;" k="31" />
<hkern u1="&#x20;" u2="&#x1e84;" k="23" />
<hkern u1="&#x20;" u2="&#x1e82;" k="23" />
<hkern u1="&#x20;" u2="&#x1e80;" k="23" />
<hkern u1="&#x20;" u2="&#x1ef3;" k="23" />
<hkern u1="&#x20;" u2="&#x1e85;" k="20" />
<hkern u1="&#x20;" u2="&#x1e83;" k="20" />
<hkern u1="&#x20;" u2="&#x1e81;" k="20" />
<hkern u1="&#x20;" u2="&#x177;" k="23" />
<hkern u1="&#x20;" u2="&#x176;" k="31" />
<hkern u1="&#x20;" u2="&#x175;" k="20" />
<hkern u1="&#x20;" u2="&#x174;" k="23" />
<hkern u1="&#x20;" u2="&#x164;" k="27" />
<hkern u1="&#x20;" u2="&#x134;" k="27" />
<hkern u1="&#x20;" u2="&#x102;" k="26" />
<hkern u1="&#x20;" u2="&#x100;" k="26" />
<hkern u1="&#x20;" u2="&#xc3;" k="26" />
<hkern u1="&#x20;" u2="f" k="10" />
<hkern u1="&#x20;" u2="&#x27;" k="10" />
<hkern u1="&#x20;" u2="&#x22;" k="10" />
<hkern u1="&#x20;" u2="&#xc6;" k="31" />
<hkern u1="&#x20;" u2="&#xc5;" k="26" />
<hkern u1="&#x20;" u2="&#x2019;" k="19" />
<hkern u1="&#x20;" u2="&#x201d;" k="19" />
<hkern u1="&#x20;" u2="&#xc1;" k="26" />
<hkern u1="&#x20;" u2="&#xc2;" k="26" />
<hkern u1="&#x20;" u2="&#x178;" k="31" />
<hkern u1="&#x20;" u2="&#xff;" k="23" />
<hkern u1="&#x20;" u2="&#xc0;" k="26" />
<hkern u1="&#x20;" u2="&#xc4;" k="26" />
<hkern u1="&#x20;" u2="&#xfd;" k="23" />
<hkern u1="&#x20;" u2="&#xdd;" k="31" />
<hkern u1="&#x20;" g2="fl" k="10" />
<hkern u1="&#x20;" g2="fi" k="10" />
<hkern u1="&#x20;" u2="Y" k="31" />
<hkern u1="&#x20;" u2="W" k="23" />
<hkern u1="&#x20;" u2="J" k="27" />
<hkern u1="&#x20;" u2="T" k="27" />
<hkern u1="&#x20;" u2="A" k="26" />
<hkern u1="&#x20;" u2="y" k="23" />
<hkern u1="&#x20;" u2="w" k="20" />
<hkern u1="&#x20;" u2="t" k="10" />
<hkern u1="&#x20;" u2="V" k="28" />
<hkern u1="&#x20;" u2="v" k="22" />
<hkern u1="h" u2="&#x29;" k="26" />
<hkern u1="h" u2="&#x2122;" k="20" />
<hkern u1="h" u2="&#x7d;" k="31" />
<hkern u1="h" u2="&#x2018;" k="13" />
<hkern u1="h" u2="&#x2019;" k="14" />
<hkern u1="h" u2="\" k="27" />
<hkern u1="h" u2="]" k="31" />
<hkern u1="h" u2="U" k="5" />
<hkern u1="h" u2="Y" k="61" />
<hkern u1="h" u2="Z" k="5" />
<hkern u1="h" u2="W" k="29" />
<hkern u1="h" u2="V" k="41" />
<hkern u1="h" u2="T" k="80" />
<hkern u1="h" u2="y" k="6" />
<hkern u1="h" u2="v" k="5" />
<hkern u1="q" u2="&#x29;" k="24" />
<hkern u1="q" u2="&#x2122;" k="16" />
<hkern u1="q" u2="&#x7d;" k="25" />
<hkern u1="q" u2="\" k="15" />
<hkern u1="q" u2="]" k="25" />
<hkern u1="q" u2="Y" k="53" />
<hkern u1="q" u2="Z" k="5" />
<hkern u1="q" u2="W" k="26" />
<hkern u1="q" u2="V" k="35" />
<hkern u1="q" u2="T" k="59" />
<hkern u1="j" u2="Z" k="6" />
<hkern u1="t" u2="&#x2039;" k="14" />
<hkern u1="t" u2="&#x7d;" k="10" />
<hkern u1="t" u2="]" k="10" />
<hkern u1="t" u2="Y" k="21" />
<hkern u1="t" u2="T" k="44" />
<hkern u1="u" u2="&#x29;" k="24" />
<hkern u1="u" u2="&#x2122;" k="16" />
<hkern u1="u" u2="&#x7d;" k="25" />
<hkern u1="u" u2="\" k="15" />
<hkern u1="u" u2="]" k="25" />
<hkern u1="u" u2="Y" k="53" />
<hkern u1="u" u2="Z" k="5" />
<hkern u1="u" u2="W" k="26" />
<hkern u1="u" u2="V" k="35" />
<hkern u1="u" u2="T" k="59" />
<hkern u1="e" u2="&#x29;" k="31" />
<hkern u1="e" u2="f" k="4" />
<hkern u1="e" u2="&#x2122;" k="20" />
<hkern u1="e" u2="&#x7d;" k="32" />
<hkern u1="e" u2="&#x2018;" k="16" />
<hkern u1="e" u2="&#x2019;" k="18" />
<hkern u1="e" u2="\" k="28" />
<hkern u1="e" u2="]" k="31" />
<hkern u1="e" u2="X" k="10" />
<hkern u1="e" u2="U" k="5" />
<hkern u1="e" u2="Y" k="83" />
<hkern u1="e" u2="Z" k="8" />
<hkern u1="e" u2="W" k="37" />
<hkern u1="e" u2="V" k="42" />
<hkern u1="e" u2="T" k="74" />
<hkern u1="e" u2="A" k="7" />
<hkern u1="e" u2="x" k="14" />
<hkern u1="e" u2="y" k="10" />
<hkern u1="e" u2="w" k="5" />
<hkern u1="e" u2="v" k="8" />
<hkern u1="c" u2="&#x29;" k="30" />
<hkern u1="c" u2="&#x2122;" k="20" />
<hkern u1="c" u2="&#x7d;" k="30" />
<hkern u1="c" u2="&#x2018;" k="12" />
<hkern u1="c" u2="&#x2019;" k="14" />
<hkern u1="c" u2="\" k="24" />
<hkern u1="c" u2="]" k="30" />
<hkern u1="c" u2="X" k="6" />
<hkern u1="c" u2="Y" k="71" />
<hkern u1="c" u2="W" k="30" />
<hkern u1="c" u2="V" k="44" />
<hkern u1="c" u2="T" k="84" />
<hkern u1="c" u2="S" k="5" />
<hkern u1="c" u2="A" k="5" />
<hkern u1="c" u2="x" k="8" />
<hkern u1="c" u2="y" k="7" />
<hkern u1="c" u2="v" k="5" />
<hkern u1="k" u2="d" k="22" />
<hkern u1="k" u2="&#x2039;" k="31" />
<hkern u1="k" u2="&#x2122;" k="14" />
<hkern u1="k" u2="&#xf0;" k="25" />
<hkern u1="k" u2="&#x7d;" k="16" />
<hkern u1="k" u2="s" k="7" />
<hkern u1="k" u2="]" k="16" />
<hkern u1="k" u2="&#x2d;" k="25" />
<hkern u1="k" u2="Y" k="30" />
<hkern u1="k" u2="V" k="10" />
<hkern u1="k" u2="T" k="66" />
<hkern u1="k" u2="S" k="6" />
<hkern u1="k" u2="O" k="11" />
<hkern u1="k" u2="o" k="23" />
<hkern u1="k" u2="a" k="5" />
<hkern u1="m" u2="&#x29;" k="26" />
<hkern u1="m" u2="&#x2122;" k="20" />
<hkern u1="m" u2="&#x7d;" k="31" />
<hkern u1="m" u2="&#x2018;" k="13" />
<hkern u1="m" u2="&#x2019;" k="14" />
<hkern u1="m" u2="\" k="27" />
<hkern u1="m" u2="]" k="31" />
<hkern u1="m" u2="U" k="5" />
<hkern u1="m" u2="Y" k="61" />
<hkern u1="m" u2="Z" k="5" />
<hkern u1="m" u2="W" k="29" />
<hkern u1="m" u2="V" k="41" />
<hkern u1="m" u2="T" k="80" />
<hkern u1="m" u2="y" k="6" />
<hkern u1="m" u2="v" k="5" />
<hkern u1="r" u2="d" k="11" />
<hkern u1="r" u2="&#x29;" k="19" />
<hkern u1="r" u2="&#x2039;" k="24" />
<hkern u1="r" u2="&#xc6;" k="44" />
<hkern u1="r" u2="&#xf0;" k="27" />
<hkern u1="r" u2="&#x7d;" k="13" />
<hkern u1="r" u2="]" k="13" />
<hkern u1="r" u2="&#x2f;" k="26" />
<hkern u1="r" u2="&#x2d;" k="27" />
<hkern u1="r" u2="X" k="21" />
<hkern u1="r" u2="Y" k="15" />
<hkern u1="r" u2="Z" k="11" />
<hkern u1="r" u2="J" k="55" />
<hkern u1="r" u2="T" k="45" />
<hkern u1="r" u2="A" k="39" />
<hkern u1="r" u2="&#x2e;" k="33" />
<hkern u1="r" u2="o" k="11" />
<hkern u1="r" u2="a" k="4" />
<hkern u1="r" u2="&#x20;" k="21" />
<hkern u1="v" u2="d" k="9" />
<hkern u1="v" u2="&#x105;" k="8" />
<hkern u1="v" u2="&#x104;" k="25" />
<hkern u1="v" u2="&#x119;" k="10" />
<hkern u1="v" u2="&#x1fa;" k="25" />
<hkern u1="v" u2="&#x1fb;" k="8" />
<hkern u1="v" u2="&#x166;" k="50" />
<hkern u1="v" u2="&#x219;" k="7" />
<hkern u1="v" u2="&#x21a;" k="50" />
<hkern u1="v" u2="&#x162;" k="50" />
<hkern u1="v" u2="&#x15f;" k="7" />
<hkern u1="v" u2="&#x111;" k="9" />
<hkern u1="v" u2="&#x1fd;" k="8" />
<hkern u1="v" u2="&#x1ff;" k="10" />
<hkern u1="v" u2="&#x1fc;" k="32" />
<hkern u1="v" u2="&#x1ef2;" k="23" />
<hkern u1="v" u2="&#x17b;" k="14" />
<hkern u1="v" u2="&#x179;" k="14" />
<hkern u1="v" u2="&#x176;" k="23" />
<hkern u1="v" u2="&#x164;" k="50" />
<hkern u1="v" u2="&#x15d;" k="7" />
<hkern u1="v" u2="&#x15b;" k="7" />
<hkern u1="v" u2="&#x151;" k="10" />
<hkern u1="v" u2="&#x134;" k="58" />
<hkern u1="v" u2="&#x123;" k="9" />
<hkern u1="v" u2="&#x121;" k="9" />
<hkern u1="v" u2="&#x11d;" k="9" />
<hkern u1="v" u2="&#x11b;" k="10" />
<hkern u1="v" u2="&#x117;" k="10" />
<hkern u1="v" u2="&#x14f;" k="10" />
<hkern u1="v" u2="&#x11f;" k="9" />
<hkern u1="v" u2="&#x115;" k="10" />
<hkern u1="v" u2="&#x14d;" k="10" />
<hkern u1="v" u2="&#x113;" k="10" />
<hkern u1="v" u2="&#x10f;" k="9" />
<hkern u1="v" u2="&#x10d;" k="10" />
<hkern u1="v" u2="&#x10b;" k="10" />
<hkern u1="v" u2="&#x109;" k="10" />
<hkern u1="v" u2="&#x107;" k="10" />
<hkern u1="v" u2="&#x103;" k="8" />
<hkern u1="v" u2="&#x102;" k="25" />
<hkern u1="v" u2="&#x101;" k="8" />
<hkern u1="v" u2="&#x100;" k="25" />
<hkern u1="v" u2="&#xc3;" k="25" />
<hkern u1="v" u2="&#xf5;" k="10" />
<hkern u1="v" u2="&#xe3;" k="8" />
<hkern u1="v" u2="&#x2039;" k="18" />
<hkern u1="v" u2="&#xc6;" k="32" />
<hkern u1="v" u2="&#x153;" k="10" />
<hkern u1="v" u2="&#xe6;" k="8" />
<hkern u1="v" u2="&#xf8;" k="10" />
<hkern u1="v" u2="&#xc5;" k="25" />
<hkern u1="v" u2="&#xe5;" k="8" />
<hkern u1="v" u2="&#x17d;" k="14" />
<hkern u1="v" u2="&#x161;" k="7" />
<hkern u1="v" u2="g" k="9" />
<hkern u1="v" u2="s" k="7" />
<hkern u1="v" u2="&#xab;" k="18" />
<hkern u1="v" u2="&#x201e;" k="35" />
<hkern u1="v" u2="&#x201a;" k="35" />
<hkern u1="v" u2="&#xc1;" k="25" />
<hkern u1="v" u2="&#xc2;" k="25" />
<hkern u1="v" u2="&#x178;" k="23" />
<hkern u1="v" u2="&#xc0;" k="25" />
<hkern u1="v" u2="&#xf6;" k="10" />
<hkern u1="v" u2="&#xf4;" k="10" />
<hkern u1="v" u2="&#xf2;" k="10" />
<hkern u1="v" u2="&#xf3;" k="10" />
<hkern u1="v" u2="&#xeb;" k="10" />
<hkern u1="v" u2="&#xea;" k="10" />
<hkern u1="v" u2="&#xe8;" k="10" />
<hkern u1="v" u2="&#xe9;" k="10" />
<hkern u1="v" u2="&#xe4;" k="8" />
<hkern u1="v" u2="&#xe2;" k="8" />
<hkern u1="v" u2="&#xe0;" k="8" />
<hkern u1="v" u2="&#xe1;" k="8" />
<hkern u1="v" u2="&#xc4;" k="25" />
<hkern u1="v" u2="&#xdd;" k="23" />
<hkern u1="v" u2="&#x2026;" k="35" />
<hkern u1="v" u2="&#xe7;" k="10" />
<hkern u1="v" u2="Y" k="23" />
<hkern u1="v" u2="Z" k="14" />
<hkern u1="v" u2="J" k="58" />
<hkern u1="v" u2="T" k="50" />
<hkern u1="v" u2="A" k="25" />
<hkern u1="v" u2="&#x2c;" k="35" />
<hkern u1="v" u2="&#x2e;" k="35" />
<hkern u1="v" u2="c" k="10" />
<hkern u1="v" u2="e" k="10" />
<hkern u1="v" u2="o" k="10" />
<hkern u1="v" u2="a" k="8" />
<hkern u1="v" u2="q" k="9" />
<hkern u1="v" u2="&#x29;" k="24" />
<hkern u1="v" u2="&#xf0;" k="14" />
<hkern u1="v" u2="&#x7d;" k="18" />
<hkern u1="v" u2="]" k="18" />
<hkern u1="v" u2="&#x2f;" k="25" />
<hkern u1="v" u2="X" k="24" />
<hkern u1="v" u2="&#x20;" k="22" />
<hkern u1="w" u2="d" k="7" />
<hkern u1="w" u2="&#x29;" k="23" />
<hkern u1="w" u2="&#x2039;" k="13" />
<hkern u1="w" u2="&#xc6;" k="26" />
<hkern u1="w" u2="&#xf0;" k="10" />
<hkern u1="w" u2="&#x7d;" k="17" />
<hkern u1="w" u2="s" k="4" />
<hkern u1="w" u2="]" k="17" />
<hkern u1="w" u2="&#x2f;" k="18" />
<hkern u1="w" u2="X" k="20" />
<hkern u1="w" u2="Y" k="24" />
<hkern u1="w" u2="Z" k="12" />
<hkern u1="w" u2="J" k="40" />
<hkern u1="w" u2="T" k="52" />
<hkern u1="w" u2="A" k="20" />
<hkern u1="w" u2="&#x2e;" k="26" />
<hkern u1="w" u2="o" k="6" />
<hkern u1="w" u2="a" k="5" />
<hkern u1="w" u2="&#x20;" k="20" />
<hkern u1="z" u2="d" k="5" />
<hkern u1="z" u2="&#x29;" k="16" />
<hkern u1="z" u2="&#x2039;" k="21" />
<hkern u1="z" u2="&#x2122;" k="16" />
<hkern u1="z" u2="&#xf0;" k="7" />
<hkern u1="z" u2="&#x7d;" k="21" />
<hkern u1="z" u2="]" k="21" />
<hkern u1="z" u2="&#x2d;" k="14" />
<hkern u1="z" u2="U" k="6" />
<hkern u1="z" u2="Y" k="43" />
<hkern u1="z" u2="W" k="10" />
<hkern u1="z" u2="V" k="20" />
<hkern u1="z" u2="T" k="76" />
<hkern u1="z" u2="o" k="6" />
<hkern u1="y" u2="d" k="9" />
<hkern u1="y" u2="&#x29;" k="23" />
<hkern u1="y" u2="&#x2039;" k="19" />
<hkern u1="y" u2="&#xc6;" k="33" />
<hkern u1="y" u2="&#xf0;" k="15" />
<hkern u1="y" u2="&#x7d;" k="17" />
<hkern u1="y" u2="s" k="6" />
<hkern u1="y" u2="]" k="17" />
<hkern u1="y" u2="&#x2f;" k="26" />
<hkern u1="y" u2="X" k="24" />
<hkern u1="y" u2="Y" k="22" />
<hkern u1="y" u2="Z" k="14" />
<hkern u1="y" u2="J" k="61" />
<hkern u1="y" u2="T" k="49" />
<hkern u1="y" u2="A" k="26" />
<hkern u1="y" u2="&#x2e;" k="35" />
<hkern u1="y" u2="o" k="9" />
<hkern u1="y" u2="a" k="8" />
<hkern u1="y" u2="&#x20;" k="22" />
<hkern u1="x" u2="d" k="16" />
<hkern u1="x" u2="&#x105;" k="6" />
<hkern u1="x" u2="&#x119;" k="17" />
<hkern u1="x" u2="&#x1fb;" k="6" />
<hkern u1="x" u2="&#x166;" k="64" />
<hkern u1="x" u2="&#x219;" k="7" />
<hkern u1="x" u2="&#x218;" k="6" />
<hkern u1="x" u2="&#x21a;" k="64" />
<hkern u1="x" u2="&#x162;" k="64" />
<hkern u1="x" u2="&#x15f;" k="7" />
<hkern u1="x" u2="&#x15e;" k="6" />
<hkern u1="x" u2="&#x122;" k="11" />
<hkern u1="x" u2="&#x111;" k="16" />
<hkern u1="x" u2="&#x1fd;" k="6" />
<hkern u1="x" u2="&#x1ff;" k="17" />
<hkern u1="x" u2="&#x1fe;" k="11" />
<hkern u1="x" u2="&#x1ef2;" k="31" />
<hkern u1="x" u2="&#x176;" k="31" />
<hkern u1="x" u2="&#x164;" k="64" />
<hkern u1="x" u2="&#x15d;" k="7" />
<hkern u1="x" u2="&#x15c;" k="6" />
<hkern u1="x" u2="&#x15b;" k="7" />
<hkern u1="x" u2="&#x15a;" k="6" />
<hkern u1="x" u2="&#x151;" k="17" />
<hkern u1="x" u2="&#x150;" k="11" />
<hkern u1="x" u2="&#x123;" k="16" />
<hkern u1="x" u2="&#x121;" k="16" />
<hkern u1="x" u2="&#x11d;" k="16" />
<hkern u1="x" u2="&#x11c;" k="11" />
<hkern u1="x" u2="&#x11b;" k="17" />
<hkern u1="x" u2="&#x117;" k="17" />
<hkern u1="x" u2="&#x14f;" k="17" />
<hkern u1="x" u2="&#x14e;" k="11" />
<hkern u1="x" u2="&#x11f;" k="16" />
<hkern u1="x" u2="&#x11e;" k="11" />
<hkern u1="x" u2="&#x115;" k="17" />
<hkern u1="x" u2="&#x14d;" k="17" />
<hkern u1="x" u2="&#x14c;" k="11" />
<hkern u1="x" u2="&#x113;" k="17" />
<hkern u1="x" u2="&#x10f;" k="16" />
<hkern u1="x" u2="&#x10d;" k="17" />
<hkern u1="x" u2="&#x10c;" k="11" />
<hkern u1="x" u2="&#x10b;" k="17" />
<hkern u1="x" u2="&#x10a;" k="11" />
<hkern u1="x" u2="&#x109;" k="17" />
<hkern u1="x" u2="&#x108;" k="11" />
<hkern u1="x" u2="&#x107;" k="17" />
<hkern u1="x" u2="&#x106;" k="11" />
<hkern u1="x" u2="&#x103;" k="6" />
<hkern u1="x" u2="&#x101;" k="6" />
<hkern u1="x" u2="&#x120;" k="11" />
<hkern u1="x" u2="&#xad;" k="15" />
<hkern u1="x" u2="&#xd5;" k="11" />
<hkern u1="x" u2="&#xf5;" k="17" />
<hkern u1="x" u2="&#xe3;" k="6" />
<hkern u1="x" u2="&#x2039;" k="25" />
<hkern u1="x" u2="&#xd8;" k="11" />
<hkern u1="x" u2="&#x152;" k="11" />
<hkern u1="x" u2="&#x153;" k="17" />
<hkern u1="x" u2="&#xe6;" k="6" />
<hkern u1="x" u2="&#xf8;" k="17" />
<hkern u1="x" u2="&#xe5;" k="6" />
<hkern u1="x" u2="&#x161;" k="7" />
<hkern u1="x" u2="&#x160;" k="6" />
<hkern u1="x" u2="&#xd6;" k="11" />
<hkern u1="x" u2="&#xd2;" k="11" />
<hkern u1="x" u2="&#xd4;" k="11" />
<hkern u1="x" u2="&#xd3;" k="11" />
<hkern u1="x" u2="g" k="16" />
<hkern u1="x" u2="s" k="7" />
<hkern u1="x" u2="&#xab;" k="25" />
<hkern u1="x" u2="&#xc7;" k="11" />
<hkern u1="x" u2="&#x178;" k="31" />
<hkern u1="x" u2="&#xf6;" k="17" />
<hkern u1="x" u2="&#xf4;" k="17" />
<hkern u1="x" u2="&#xf2;" k="17" />
<hkern u1="x" u2="&#xf3;" k="17" />
<hkern u1="x" u2="&#xeb;" k="17" />
<hkern u1="x" u2="&#xea;" k="17" />
<hkern u1="x" u2="&#xe8;" k="17" />
<hkern u1="x" u2="&#xe9;" k="17" />
<hkern u1="x" u2="&#xe4;" k="6" />
<hkern u1="x" u2="&#xe2;" k="6" />
<hkern u1="x" u2="&#xe0;" k="6" />
<hkern u1="x" u2="&#xe1;" k="6" />
<hkern u1="x" u2="&#xdd;" k="31" />
<hkern u1="x" u2="&#x2014;" k="15" />
<hkern u1="x" u2="&#x2013;" k="15" />
<hkern u1="x" u2="&#xe7;" k="17" />
<hkern u1="x" u2="&#x2d;" k="15" />
<hkern u1="x" u2="G" k="11" />
<hkern u1="x" u2="C" k="11" />
<hkern u1="x" u2="Y" k="31" />
<hkern u1="x" u2="Q" k="11" />
<hkern u1="x" u2="T" k="64" />
<hkern u1="x" u2="S" k="6" />
<hkern u1="x" u2="O" k="11" />
<hkern u1="x" u2="c" k="17" />
<hkern u1="x" u2="e" k="17" />
<hkern u1="x" u2="o" k="17" />
<hkern u1="x" u2="a" k="6" />
<hkern u1="x" u2="q" k="16" />
<hkern u1="x" u2="&#x29;" k="10" />
<hkern u1="x" u2="&#x2122;" k="13" />
<hkern u1="x" u2="&#xf0;" k="20" />
<hkern u1="x" u2="&#x7d;" k="17" />
<hkern u1="x" u2="]" k="17" />
<hkern u1="x" u2="V" k="10" />
<hkern u1="&#x2e;" g2="seven.plf" k="11" />
<hkern u1="&#x2e;" g2="one.plf" k="44" />
<hkern u1="&#x2e;" u2="f" k="8" />
<hkern u1="&#x2e;" u2="&#x27;" k="105" />
<hkern u1="&#x2e;" u2="&#x2018;" k="116" />
<hkern u1="&#x2e;" u2="&#x2019;" k="118" />
<hkern u1="&#x2e;" u2="U" k="10" />
<hkern u1="&#x2e;" u2="Y" k="61" />
<hkern u1="&#x2e;" u2="W" k="41" />
<hkern u1="&#x2e;" u2="V" k="55" />
<hkern u1="&#x2e;" u2="T" k="47" />
<hkern u1="&#x2e;" u2="O" k="10" />
<hkern u1="&#x2e;" u2="y" k="38" />
<hkern u1="&#x2e;" u2="w" k="26" />
<hkern u1="&#x2e;" u2="v" k="35" />
<hkern u1="&#x2c;" g2="seven.plf" k="11" />
<hkern u1="&#x2c;" g2="one.plf" k="44" />
<hkern u1="&#x2c;" u2="f" k="8" />
<hkern u1="&#x2c;" u2="&#x27;" k="105" />
<hkern u1="&#x2c;" u2="&#x2018;" k="116" />
<hkern u1="&#x2c;" u2="&#x2019;" k="118" />
<hkern u1="&#x2c;" u2="U" k="10" />
<hkern u1="&#x2c;" u2="Y" k="61" />
<hkern u1="&#x2c;" u2="W" k="41" />
<hkern u1="&#x2c;" u2="V" k="55" />
<hkern u1="&#x2c;" u2="T" k="47" />
<hkern u1="&#x2c;" u2="O" k="10" />
<hkern u1="&#x2c;" u2="y" k="38" />
<hkern u1="&#x2c;" u2="w" k="26" />
<hkern u1="&#x2c;" u2="v" k="35" />
<hkern u1="A" u2="d" k="10" />
<hkern u1="A" u2="&#x166;" k="44" />
<hkern u1="A" g2="hyphen.case" k="9" />
<hkern u1="A" g2="braceright.case" k="11" />
<hkern u1="A" g2="bracketright.case" k="11" />
<hkern u1="A" g2="guilsinglleft.case" k="24" />
<hkern u1="A" u2="&#x29;" k="16" />
<hkern u1="A" g2="nine.plf" k="10" />
<hkern u1="A" g2="seven.plf" k="15" />
<hkern u1="A" g2="six.plf" k="11" />
<hkern u1="A" g2="one.plf" k="44" />
<hkern u1="A" g2="zero.plf" k="12" />
<hkern u1="A" u2="f" k="16" />
<hkern u1="A" u2="&#x2039;" k="14" />
<hkern u1="A" u2="&#x2a;" k="35" />
<hkern u1="A" u2="&#xaa;" k="28" />
<hkern u1="A" u2="&#xba;" k="31" />
<hkern u1="A" u2="&#x2122;" k="43" />
<hkern u1="A" u2="&#x27;" k="38" />
<hkern u1="A" u2="&#xf0;" k="9" />
<hkern u1="A" u2="&#x7d;" k="33" />
<hkern u1="A" u2="&#xae;" k="16" />
<hkern u1="A" u2="s" k="6" />
<hkern u1="A" u2="&#x2018;" k="44" />
<hkern u1="A" u2="&#x2019;" k="45" />
<hkern u1="A" u2="&#xa9;" k="16" />
<hkern u1="A" u2="&#x3f;" k="35" />
<hkern u1="A" u2="\" k="48" />
<hkern u1="A" u2="]" k="33" />
<hkern u1="A" u2="U" k="13" />
<hkern u1="A" u2="Y" k="57" />
<hkern u1="A" u2="W" k="32" />
<hkern u1="A" u2="V" k="41" />
<hkern u1="A" u2="T" k="52" />
<hkern u1="A" u2="S" k="9" />
<hkern u1="A" u2="O" k="15" />
<hkern u1="A" u2="y" k="27" />
<hkern u1="A" u2="w" k="20" />
<hkern u1="A" u2="v" k="25" />
<hkern u1="A" u2="u" k="8" />
<hkern u1="A" u2="o" k="10" />
<hkern u1="A" u2="a" k="5" />
<hkern u1="A" u2="&#x20;" k="26" />
<hkern u1="F" u2="d" k="11" />
<hkern u1="F" u2="&#x105;" k="18" />
<hkern u1="F" u2="&#x104;" k="34" />
<hkern u1="F" u2="&#x173;" k="12" />
<hkern u1="F" u2="&#x119;" k="10" />
<hkern u1="F" u2="&#x1fa;" k="34" />
<hkern u1="F" u2="&#x1fb;" k="18" />
<hkern u1="F" u2="&#x219;" k="13" />
<hkern u1="F" u2="&#x218;" k="9" />
<hkern u1="F" u2="&#x15f;" k="13" />
<hkern u1="F" u2="&#x15e;" k="9" />
<hkern u1="F" u2="&#x157;" k="15" />
<hkern u1="F" u2="&#x146;" k="15" />
<hkern u1="F" u2="&#x149;" k="15" />
<hkern u1="F" u2="&#x111;" k="11" />
<hkern u1="F" u2="&#x1fd;" k="18" />
<hkern u1="F" u2="&#x1ff;" k="10" />
<hkern u1="F" u2="&#x14b;" k="15" />
<hkern u1="F" u2="&#x1fc;" k="53" />
<hkern u1="F" u2="&#x17c;" k="15" />
<hkern u1="F" u2="&#x17a;" k="15" />
<hkern u1="F" u2="&#x171;" k="12" />
<hkern u1="F" u2="&#x16f;" k="12" />
<hkern u1="F" u2="&#x169;" k="12" />
<hkern u1="F" u2="&#x15d;" k="13" />
<hkern u1="F" u2="&#x15c;" k="9" />
<hkern u1="F" u2="&#x15b;" k="13" />
<hkern u1="F" u2="&#x15a;" k="9" />
<hkern u1="F" u2="&#x159;" k="15" />
<hkern u1="F" u2="&#x155;" k="15" />
<hkern u1="F" u2="&#x151;" k="10" />
<hkern u1="F" u2="&#x148;" k="15" />
<hkern u1="F" u2="&#x144;" k="15" />
<hkern u1="F" u2="&#x134;" k="87" />
<hkern u1="F" u2="&#x123;" k="11" />
<hkern u1="F" u2="&#x121;" k="11" />
<hkern u1="F" u2="&#x11d;" k="11" />
<hkern u1="F" u2="&#x11b;" k="10" />
<hkern u1="F" u2="&#x117;" k="10" />
<hkern u1="F" u2="&#x16d;" k="12" />
<hkern u1="F" u2="&#x14f;" k="10" />
<hkern u1="F" u2="&#x11f;" k="11" />
<hkern u1="F" u2="&#x115;" k="10" />
<hkern u1="F" u2="&#x16b;" k="12" />
<hkern u1="F" u2="&#x14d;" k="10" />
<hkern u1="F" u2="&#x113;" k="10" />
<hkern u1="F" u2="&#x10f;" k="11" />
<hkern u1="F" u2="&#x10d;" k="10" />
<hkern u1="F" u2="&#x10b;" k="10" />
<hkern u1="F" u2="&#x109;" k="10" />
<hkern u1="F" u2="&#x107;" k="10" />
<hkern u1="F" u2="&#x103;" k="18" />
<hkern u1="F" u2="&#x102;" k="34" />
<hkern u1="F" u2="&#x101;" k="18" />
<hkern u1="F" u2="&#x100;" k="34" />
<hkern u1="F" u2="&#xc3;" k="34" />
<hkern u1="F" u2="&#xf5;" k="10" />
<hkern u1="F" u2="&#xf1;" k="15" />
<hkern u1="F" u2="&#xe3;" k="18" />
<hkern u1="F" u2="n" k="15" />
<hkern u1="F" u2="&#xc6;" k="53" />
<hkern u1="F" u2="&#x153;" k="10" />
<hkern u1="F" u2="&#xe6;" k="18" />
<hkern u1="F" u2="&#xf8;" k="10" />
<hkern u1="F" u2="&#xc5;" k="34" />
<hkern u1="F" u2="&#xe5;" k="18" />
<hkern u1="F" u2="&#x17e;" k="15" />
<hkern u1="F" u2="&#x161;" k="13" />
<hkern u1="F" u2="&#x160;" k="9" />
<hkern u1="F" u2="g" k="11" />
<hkern u1="F" u2="s" k="13" />
<hkern u1="F" u2="&#x201e;" k="80" />
<hkern u1="F" u2="&#x201a;" k="80" />
<hkern u1="F" u2="&#xc1;" k="34" />
<hkern u1="F" u2="&#xc2;" k="34" />
<hkern u1="F" u2="&#xc0;" k="34" />
<hkern u1="F" u2="&#xfc;" k="12" />
<hkern u1="F" u2="&#xfb;" k="12" />
<hkern u1="F" u2="&#xf9;" k="12" />
<hkern u1="F" u2="&#xfa;" k="12" />
<hkern u1="F" u2="&#xf6;" k="10" />
<hkern u1="F" u2="&#xf4;" k="10" />
<hkern u1="F" u2="&#xf2;" k="10" />
<hkern u1="F" u2="&#xf3;" k="10" />
<hkern u1="F" u2="&#xeb;" k="10" />
<hkern u1="F" u2="&#xea;" k="10" />
<hkern u1="F" u2="&#xe8;" k="10" />
<hkern u1="F" u2="&#xe9;" k="10" />
<hkern u1="F" u2="&#xe4;" k="18" />
<hkern u1="F" u2="&#xe2;" k="18" />
<hkern u1="F" u2="&#xe0;" k="18" />
<hkern u1="F" u2="&#xe1;" k="18" />
<hkern u1="F" u2="&#xc4;" k="34" />
<hkern u1="F" u2="&#x2026;" k="80" />
<hkern u1="F" u2="&#xe7;" k="10" />
<hkern u1="F" u2="J" k="87" />
<hkern u1="F" u2="S" k="9" />
<hkern u1="F" u2="A" k="34" />
<hkern u1="F" u2="&#x2c;" k="80" />
<hkern u1="F" u2="&#x2e;" k="80" />
<hkern u1="F" u2="z" k="15" />
<hkern u1="F" u2="r" k="15" />
<hkern u1="F" u2="m" k="15" />
<hkern u1="F" u2="c" k="10" />
<hkern u1="F" u2="e" k="10" />
<hkern u1="F" u2="u" k="12" />
<hkern u1="F" u2="o" k="10" />
<hkern u1="F" u2="a" k="18" />
<hkern u1="F" u2="q" k="11" />
<hkern u1="F" u2="p" k="15" />
<hkern u1="F" u2="&#xdf;" k="7" />
<hkern u1="F" u2="&#xf0;" k="16" />
<hkern u1="F" u2="&#x131;" k="15" />
<hkern u1="F" u2="&#x2f;" k="33" />
<hkern u1="F" u2="x" k="13" />
<hkern u1="F" u2="&#x20;" k="16" />
<hkern u1="I" u2="d" k="6" />
<hkern u1="I" u2="&#xf0;" k="7" />
<hkern u1="I" u2="o" k="6" />
<hkern u1="M" u2="d" k="6" />
<hkern u1="M" u2="&#xf0;" k="7" />
<hkern u1="M" u2="o" k="6" />
<hkern u1="N" u2="d" k="6" />
<hkern u1="N" u2="&#xf0;" k="7" />
<hkern u1="N" u2="o" k="6" />
<hkern u1="R" u2="d" k="11" />
<hkern u1="R" g2="four.plf" k="12" />
<hkern u1="R" u2="&#x2039;" k="15" />
<hkern u1="R" u2="&#xf0;" k="17" />
<hkern u1="R" u2="&#x7d;" k="12" />
<hkern u1="R" u2="s" k="5" />
<hkern u1="R" u2="]" k="12" />
<hkern u1="R" u2="Y" k="12" />
<hkern u1="R" u2="W" k="8" />
<hkern u1="R" u2="V" k="12" />
<hkern u1="R" u2="A" k="6" />
<hkern u1="R" u2="u" k="5" />
<hkern u1="R" u2="o" k="12" />
<hkern u1="R" u2="a" k="7" />
<hkern u1="S" g2="parenright.case" k="19" />
<hkern u1="S" g2="braceright.case" k="23" />
<hkern u1="S" g2="bracketright.case" k="23" />
<hkern u1="S" u2="&#x29;" k="18" />
<hkern u1="S" u2="&#x7d;" k="18" />
<hkern u1="S" u2="]" k="18" />
<hkern u1="S" u2="X" k="6" />
<hkern u1="S" u2="Y" k="18" />
<hkern u1="S" u2="W" k="10" />
<hkern u1="S" u2="V" k="15" />
<hkern u1="S" u2="T" k="5" />
<hkern u1="S" u2="A" k="9" />
<hkern u1="S" u2="x" k="8" />
<hkern u1="T" u2="d" k="79" />
<hkern u1="T" u2="&#x144;" k="72" />
<hkern u1="T" u2="&#x129;" k="-27" />
<hkern u1="T" u2="&#x12d;" k="-7" />
<hkern u1="T" u2="&#x12b;" k="-25" />
<hkern u1="T" g2="hyphen.case" k="38" />
<hkern u1="T" g2="guilsinglleft.case" k="58" />
<hkern u1="T" g2="guilsinglright.case" k="39" />
<hkern u1="T" g2="four.plf" k="46" />
<hkern u1="T" u2="n" k="59" />
<hkern u1="T" u2="f" k="16" />
<hkern u1="T" u2="&#x203a;" k="55" />
<hkern u1="T" u2="&#x2039;" k="61" />
<hkern u1="T" u2="&#xdf;" k="19" />
<hkern u1="T" u2="&#xc6;" k="56" />
<hkern u1="T" u2="&#xf0;" k="58" />
<hkern u1="T" u2="&#x131;" k="59" />
<hkern u1="T" u2="&#xae;" k="19" />
<hkern u1="T" u2="s" k="71" />
<hkern u1="T" u2="&#xa9;" k="19" />
<hkern u1="T" u2="&#xfa;" k="75" />
<hkern u1="T" u2="&#x2f;" k="43" />
<hkern u1="T" u2="&#x3a;" k="30" />
<hkern u1="T" u2="&#x2d;" k="39" />
<hkern u1="T" u2="J" k="51" />
<hkern u1="T" u2="O" k="15" />
<hkern u1="T" u2="A" k="52" />
<hkern u1="T" u2="&#x2e;" k="47" />
<hkern u1="T" u2="x" k="64" />
<hkern u1="T" u2="y" k="50" />
<hkern u1="T" u2="z" k="75" />
<hkern u1="T" u2="w" k="52" />
<hkern u1="T" u2="v" k="50" />
<hkern u1="T" u2="u" k="53" />
<hkern u1="T" u2="o" k="78" />
<hkern u1="T" u2="a" k="73" />
<hkern u1="T" u2="&#x20;" k="27" />
<hkern u1="P" u2="d" k="6" />
<hkern u1="P" u2="&#x105;" k="6" />
<hkern u1="P" u2="&#x104;" k="38" />
<hkern u1="P" u2="&#x119;" k="6" />
<hkern u1="P" u2="&#x1fa;" k="38" />
<hkern u1="P" u2="&#x1fb;" k="6" />
<hkern u1="P" u2="&#x111;" k="6" />
<hkern u1="P" u2="&#x1fd;" k="6" />
<hkern u1="P" u2="&#x1ff;" k="6" />
<hkern u1="P" u2="&#x1fc;" k="48" />
<hkern u1="P" u2="&#x1ef2;" k="8" />
<hkern u1="P" u2="&#x17b;" k="6" />
<hkern u1="P" u2="&#x179;" k="6" />
<hkern u1="P" u2="&#x176;" k="8" />
<hkern u1="P" u2="&#x151;" k="6" />
<hkern u1="P" u2="&#x134;" k="70" />
<hkern u1="P" u2="&#x123;" k="6" />
<hkern u1="P" u2="&#x121;" k="6" />
<hkern u1="P" u2="&#x11d;" k="6" />
<hkern u1="P" u2="&#x11b;" k="6" />
<hkern u1="P" u2="&#x117;" k="6" />
<hkern u1="P" u2="&#x14f;" k="6" />
<hkern u1="P" u2="&#x11f;" k="6" />
<hkern u1="P" u2="&#x115;" k="6" />
<hkern u1="P" u2="&#x14d;" k="6" />
<hkern u1="P" u2="&#x113;" k="6" />
<hkern u1="P" u2="&#x10f;" k="6" />
<hkern u1="P" u2="&#x10d;" k="6" />
<hkern u1="P" u2="&#x10b;" k="6" />
<hkern u1="P" u2="&#x109;" k="6" />
<hkern u1="P" u2="&#x107;" k="6" />
<hkern u1="P" u2="&#x103;" k="6" />
<hkern u1="P" u2="&#x102;" k="38" />
<hkern u1="P" u2="&#x101;" k="6" />
<hkern u1="P" u2="&#x100;" k="38" />
<hkern u1="P" u2="&#xc3;" k="38" />
<hkern u1="P" u2="&#xf5;" k="6" />
<hkern u1="P" u2="&#xe3;" k="6" />
<hkern u1="P" u2="&#xc6;" k="48" />
<hkern u1="P" u2="&#x153;" k="6" />
<hkern u1="P" u2="&#xe6;" k="6" />
<hkern u1="P" u2="&#xf8;" k="6" />
<hkern u1="P" u2="&#xc5;" k="38" />
<hkern u1="P" u2="&#xe5;" k="6" />
<hkern u1="P" u2="&#x17d;" k="6" />
<hkern u1="P" u2="g" k="6" />
<hkern u1="P" u2="&#x201e;" k="82" />
<hkern u1="P" u2="&#x201a;" k="82" />
<hkern u1="P" u2="&#xc1;" k="38" />
<hkern u1="P" u2="&#xc2;" k="38" />
<hkern u1="P" u2="&#x178;" k="8" />
<hkern u1="P" u2="&#xc0;" k="38" />
<hkern u1="P" u2="&#xf6;" k="6" />
<hkern u1="P" u2="&#xf4;" k="6" />
<hkern u1="P" u2="&#xf2;" k="6" />
<hkern u1="P" u2="&#xf3;" k="6" />
<hkern u1="P" u2="&#xeb;" k="6" />
<hkern u1="P" u2="&#xea;" k="6" />
<hkern u1="P" u2="&#xe8;" k="6" />
<hkern u1="P" u2="&#xe9;" k="6" />
<hkern u1="P" u2="&#xe4;" k="6" />
<hkern u1="P" u2="&#xe2;" k="6" />
<hkern u1="P" u2="&#xe0;" k="6" />
<hkern u1="P" u2="&#xe1;" k="6" />
<hkern u1="P" u2="&#xc4;" k="38" />
<hkern u1="P" u2="&#xdd;" k="8" />
<hkern u1="P" u2="&#x2026;" k="82" />
<hkern u1="P" u2="&#xe7;" k="6" />
<hkern u1="P" u2="Y" k="8" />
<hkern u1="P" u2="Z" k="6" />
<hkern u1="P" u2="J" k="70" />
<hkern u1="P" u2="A" k="38" />
<hkern u1="P" u2="&#x2c;" k="82" />
<hkern u1="P" u2="&#x2e;" k="82" />
<hkern u1="P" u2="c" k="6" />
<hkern u1="P" u2="e" k="6" />
<hkern u1="P" u2="o" k="6" />
<hkern u1="P" u2="a" k="6" />
<hkern u1="P" u2="q" k="6" />
<hkern u1="P" g2="parenright.case" k="22" />
<hkern u1="P" g2="braceright.case" k="27" />
<hkern u1="P" g2="bracketright.case" k="26" />
<hkern u1="P" u2="&#x29;" k="10" />
<hkern u1="P" g2="four.plf" k="12" />
<hkern u1="P" u2="&#xf0;" k="12" />
<hkern u1="P" u2="&#x2f;" k="36" />
<hkern u1="P" u2="X" k="16" />
<hkern u1="P" u2="V" k="8" />
<hkern u1="P" u2="&#x20;" k="21" />
<hkern u1="H" u2="d" k="6" />
<hkern u1="H" u2="&#xf0;" k="7" />
<hkern u1="H" u2="o" k="6" />
<hkern u1="B" u2="&#x104;" k="10" />
<hkern u1="B" u2="&#x1fa;" k="10" />
<hkern u1="B" u2="&#x166;" k="6" />
<hkern u1="B" u2="&#x21a;" k="6" />
<hkern u1="B" u2="&#x162;" k="6" />
<hkern u1="B" u2="&#x1ef2;" k="16" />
<hkern u1="B" u2="&#x1e84;" k="10" />
<hkern u1="B" u2="&#x1e82;" k="10" />
<hkern u1="B" u2="&#x1e80;" k="10" />
<hkern u1="B" u2="&#x176;" k="16" />
<hkern u1="B" u2="&#x174;" k="10" />
<hkern u1="B" u2="&#x164;" k="6" />
<hkern u1="B" u2="&#x134;" k="5" />
<hkern u1="B" u2="&#x102;" k="10" />
<hkern u1="B" u2="&#x100;" k="10" />
<hkern u1="B" u2="&#xc3;" k="10" />
<hkern u1="B" u2="&#xc5;" k="10" />
<hkern u1="B" u2="&#xc1;" k="10" />
<hkern u1="B" u2="&#xc2;" k="10" />
<hkern u1="B" u2="&#x178;" k="16" />
<hkern u1="B" u2="&#xc0;" k="10" />
<hkern u1="B" u2="&#xc4;" k="10" />
<hkern u1="B" u2="&#xdd;" k="16" />
<hkern u1="B" u2="Y" k="16" />
<hkern u1="B" u2="W" k="10" />
<hkern u1="B" u2="J" k="5" />
<hkern u1="B" u2="T" k="6" />
<hkern u1="B" u2="A" k="10" />
<hkern u1="B" g2="parenright.case" k="20" />
<hkern u1="B" g2="braceright.case" k="26" />
<hkern u1="B" g2="bracketright.case" k="25" />
<hkern u1="B" u2="&#x29;" k="18" />
<hkern u1="B" u2="&#x7d;" k="19" />
<hkern u1="B" u2="]" k="19" />
<hkern u1="B" u2="X" k="10" />
<hkern u1="B" u2="V" k="15" />
<hkern u1="B" u2="x" k="9" />
<hkern u1="D" u2="&#x166;" k="7" />
<hkern u1="D" g2="parenright.case" k="27" />
<hkern u1="D" g2="braceright.case" k="29" />
<hkern u1="D" g2="bracketright.case" k="29" />
<hkern u1="D" u2="&#x29;" k="25" />
<hkern u1="D" g2="seven.plf" k="13" />
<hkern u1="D" u2="&#xc6;" k="8" />
<hkern u1="D" u2="&#x7d;" k="25" />
<hkern u1="D" u2="]" k="25" />
<hkern u1="D" u2="X" k="22" />
<hkern u1="D" u2="Y" k="25" />
<hkern u1="D" u2="Z" k="8" />
<hkern u1="D" u2="W" k="13" />
<hkern u1="D" u2="V" k="18" />
<hkern u1="D" u2="J" k="17" />
<hkern u1="D" u2="T" k="16" />
<hkern u1="D" u2="A" k="15" />
<hkern u1="D" u2="&#x2e;" k="9" />
<hkern u1="D" u2="x" k="12" />
<hkern u1="J" u2="d" k="6" />
<hkern u1="J" u2="&#xf0;" k="7" />
<hkern u1="J" u2="o" k="6" />
<hkern u1="V" u2="d" k="46" />
<hkern u1="V" u2="&#x105;" k="42" />
<hkern u1="V" u2="&#x104;" k="42" />
<hkern u1="V" u2="&#x173;" k="32" />
<hkern u1="V" u2="&#x119;" k="46" />
<hkern u1="V" u2="&#x1fa;" k="42" />
<hkern u1="V" u2="&#x1fb;" k="42" />
<hkern u1="V" g2="uni00AD.case" k="15" />
<hkern u1="V" u2="&#x219;" k="41" />
<hkern u1="V" u2="&#x218;" k="14" />
<hkern u1="V" u2="&#x15f;" k="41" />
<hkern u1="V" u2="&#x15e;" k="14" />
<hkern u1="V" u2="&#x157;" k="35" />
<hkern u1="V" u2="&#x146;" k="35" />
<hkern u1="V" u2="&#x122;" k="18" />
<hkern u1="V" u2="&#x149;" k="35" />
<hkern u1="V" u2="&#x111;" k="46" />
<hkern u1="V" u2="&#x1fd;" k="42" />
<hkern u1="V" u2="&#x1ff;" k="46" />
<hkern u1="V" u2="&#x1fe;" k="18" />
<hkern u1="V" u2="&#x14b;" k="35" />
<hkern u1="V" u2="&#x1fc;" k="50" />
<hkern u1="V" u2="&#x17c;" k="18" />
<hkern u1="V" u2="&#x17a;" k="18" />
<hkern u1="V" u2="&#x171;" k="32" />
<hkern u1="V" u2="&#x16f;" k="32" />
<hkern u1="V" u2="&#x169;" k="32" />
<hkern u1="V" u2="&#x15d;" k="41" />
<hkern u1="V" u2="&#x15c;" k="14" />
<hkern u1="V" u2="&#x15b;" k="41" />
<hkern u1="V" u2="&#x15a;" k="14" />
<hkern u1="V" u2="&#x155;" k="35" />
<hkern u1="V" u2="&#x151;" k="46" />
<hkern u1="V" u2="&#x150;" k="18" />
<hkern u1="V" u2="&#x148;" k="35" />
<hkern u1="V" u2="&#x144;" k="35" />
<hkern u1="V" u2="&#x134;" k="55" />
<hkern u1="V" u2="&#x123;" k="46" />
<hkern u1="V" u2="&#x121;" k="46" />
<hkern u1="V" u2="&#x11d;" k="46" />
<hkern u1="V" u2="&#x11c;" k="18" />
<hkern u1="V" u2="&#x11b;" k="46" />
<hkern u1="V" u2="&#x117;" k="46" />
<hkern u1="V" u2="&#x16d;" k="32" />
<hkern u1="V" u2="&#x14f;" k="46" />
<hkern u1="V" u2="&#x14e;" k="18" />
<hkern u1="V" u2="&#x11f;" k="46" />
<hkern u1="V" u2="&#x11e;" k="18" />
<hkern u1="V" u2="&#x115;" k="46" />
<hkern u1="V" u2="&#x16b;" k="32" />
<hkern u1="V" u2="&#x14d;" k="46" />
<hkern u1="V" u2="&#x14c;" k="18" />
<hkern u1="V" u2="&#x113;" k="46" />
<hkern u1="V" u2="&#x10f;" k="46" />
<hkern u1="V" u2="&#x10d;" k="46" />
<hkern u1="V" u2="&#x10c;" k="18" />
<hkern u1="V" u2="&#x10b;" k="46" />
<hkern u1="V" u2="&#x10a;" k="18" />
<hkern u1="V" u2="&#x109;" k="46" />
<hkern u1="V" u2="&#x108;" k="18" />
<hkern u1="V" u2="&#x107;" k="46" />
<hkern u1="V" u2="&#x106;" k="18" />
<hkern u1="V" u2="&#x103;" k="42" />
<hkern u1="V" u2="&#x102;" k="42" />
<hkern u1="V" u2="&#x101;" k="42" />
<hkern u1="V" u2="&#x100;" k="42" />
<hkern u1="V" u2="&#x120;" k="18" />
<hkern u1="V" u2="&#xad;" k="23" />
<hkern u1="V" g2="hyphen.case" k="15" />
<hkern u1="V" g2="emdash.case" k="15" />
<hkern u1="V" g2="endash.case" k="15" />
<hkern u1="V" g2="guilsinglleft.case" k="31" />
<hkern u1="V" g2="guillemotleft.case" k="31" />
<hkern u1="V" g2="guilsinglright.case" k="12" />
<hkern u1="V" g2="guillemotright.case" k="12" />
<hkern u1="V" g2="zero.plfslash" k="15" />
<hkern u1="V" g2="zero.plf" k="15" />
<hkern u1="V" u2="&#xd5;" k="18" />
<hkern u1="V" u2="&#xc3;" k="42" />
<hkern u1="V" u2="&#xf5;" k="46" />
<hkern u1="V" u2="&#xf1;" k="35" />
<hkern u1="V" u2="&#xe3;" k="42" />
<hkern u1="V" u2="&#x3b;" k="11" />
<hkern u1="V" u2="&#xbb;" k="22" />
<hkern u1="V" u2="n" k="35" />
<hkern u1="V" u2="&#x203a;" k="22" />
<hkern u1="V" u2="&#x2039;" k="40" />
<hkern u1="V" u2="&#xd8;" k="18" />
<hkern u1="V" u2="&#xc6;" k="50" />
<hkern u1="V" u2="&#x152;" k="18" />
<hkern u1="V" u2="&#x153;" k="46" />
<hkern u1="V" u2="&#xe6;" k="42" />
<hkern u1="V" u2="&#xf8;" k="46" />
<hkern u1="V" u2="&#xc5;" k="42" />
<hkern u1="V" u2="&#xe5;" k="42" />
<hkern u1="V" u2="&#x17e;" k="18" />
<hkern u1="V" u2="&#x161;" k="41" />
<hkern u1="V" u2="&#x160;" k="14" />
<hkern u1="V" u2="&#xd6;" k="18" />
<hkern u1="V" u2="&#xd2;" k="18" />
<hkern u1="V" u2="&#xd4;" k="18" />
<hkern u1="V" u2="&#xd3;" k="18" />
<hkern u1="V" u2="g" k="46" />
<hkern u1="V" u2="s" k="41" />
<hkern u1="V" u2="&#xab;" k="40" />
<hkern u1="V" u2="&#x201e;" k="56" />
<hkern u1="V" u2="&#x201a;" k="56" />
<hkern u1="V" u2="&#xc7;" k="18" />
<hkern u1="V" u2="&#xc1;" k="42" />
<hkern u1="V" u2="&#xc2;" k="42" />
<hkern u1="V" u2="&#xc0;" k="42" />
<hkern u1="V" u2="&#xfc;" k="32" />
<hkern u1="V" u2="&#xfb;" k="32" />
<hkern u1="V" u2="&#xf9;" k="32" />
<hkern u1="V" u2="&#xfa;" k="32" />
<hkern u1="V" u2="&#xf6;" k="46" />
<hkern u1="V" u2="&#xf4;" k="46" />
<hkern u1="V" u2="&#xf2;" k="46" />
<hkern u1="V" u2="&#xf3;" k="46" />
<hkern u1="V" u2="&#xeb;" k="46" />
<hkern u1="V" u2="&#xea;" k="46" />
<hkern u1="V" u2="&#xe8;" k="46" />
<hkern u1="V" u2="&#xe9;" k="46" />
<hkern u1="V" u2="&#xe4;" k="42" />
<hkern u1="V" u2="&#xe2;" k="42" />
<hkern u1="V" u2="&#xe0;" k="42" />
<hkern u1="V" u2="&#xe1;" k="42" />
<hkern u1="V" u2="&#xc4;" k="42" />
<hkern u1="V" u2="&#x2014;" k="23" />
<hkern u1="V" u2="&#x2013;" k="23" />
<hkern u1="V" u2="&#x3a;" k="11" />
<hkern u1="V" u2="&#x2026;" k="56" />
<hkern u1="V" u2="&#xe7;" k="46" />
<hkern u1="V" u2="&#x2d;" k="23" />
<hkern u1="V" u2="G" k="18" />
<hkern u1="V" u2="C" k="18" />
<hkern u1="V" u2="J" k="55" />
<hkern u1="V" u2="Q" k="18" />
<hkern u1="V" u2="S" k="14" />
<hkern u1="V" u2="O" k="18" />
<hkern u1="V" u2="A" k="42" />
<hkern u1="V" u2="&#x2c;" k="56" />
<hkern u1="V" u2="&#x2e;" k="56" />
<hkern u1="V" u2="z" k="18" />
<hkern u1="V" u2="r" k="35" />
<hkern u1="V" u2="m" k="35" />
<hkern u1="V" u2="c" k="46" />
<hkern u1="V" u2="e" k="46" />
<hkern u1="V" u2="u" k="32" />
<hkern u1="V" u2="o" k="46" />
<hkern u1="V" u2="a" k="42" />
<hkern u1="V" u2="q" k="46" />
<hkern u1="V" u2="p" k="35" />
<hkern u1="V" u2="&#x159;" k="26" />
<hkern u1="V" g2="braceright.case" k="11" />
<hkern u1="V" g2="bracketright.case" k="11" />
<hkern u1="V" g2="nine.plf" k="13" />
<hkern u1="V" g2="eight.plf" k="15" />
<hkern u1="V" g2="six.plf" k="16" />
<hkern u1="V" g2="four.plf" k="33" />
<hkern u1="V" g2="three.plf" k="14" />
<hkern u1="V" g2="two.plf" k="13" />
<hkern u1="V" u2="&#xdf;" k="19" />
<hkern u1="V" u2="&#xf0;" k="48" />
<hkern u1="V" u2="&#x131;" k="35" />
<hkern u1="V" u2="&#xae;" k="19" />
<hkern u1="V" u2="&#x40;" k="15" />
<hkern u1="V" u2="&#xa9;" k="19" />
<hkern u1="V" u2="&#x2f;" k="47" />
<hkern u1="V" u2="&#x26;" k="19" />
<hkern u1="V" u2="x" k="10" />
<hkern u1="V" u2="&#x20;" k="28" />
<hkern u1="W" u2="d" k="35" />
<hkern u1="W" u2="&#x159;" k="17" />
<hkern u1="W" g2="guilsinglleft.case" k="23" />
<hkern u1="W" g2="eight.plf" k="11" />
<hkern u1="W" g2="six.plf" k="11" />
<hkern u1="W" g2="four.plf" k="22" />
<hkern u1="W" g2="zero.plf" k="11" />
<hkern u1="W" u2="n" k="26" />
<hkern u1="W" u2="&#x203a;" k="13" />
<hkern u1="W" u2="&#x2039;" k="30" />
<hkern u1="W" u2="&#xdf;" k="13" />
<hkern u1="W" u2="&#xc6;" k="41" />
<hkern u1="W" u2="&#xf0;" k="38" />
<hkern u1="W" u2="&#x131;" k="26" />
<hkern u1="W" u2="&#xae;" k="13" />
<hkern u1="W" u2="s" k="33" />
<hkern u1="W" u2="&#x40;" k="11" />
<hkern u1="W" u2="&#xa9;" k="13" />
<hkern u1="W" u2="&#x2f;" k="33" />
<hkern u1="W" u2="&#x2d;" k="12" />
<hkern u1="W" u2="&#x26;" k="14" />
<hkern u1="W" u2="J" k="47" />
<hkern u1="W" u2="S" k="10" />
<hkern u1="W" u2="O" k="12" />
<hkern u1="W" u2="A" k="32" />
<hkern u1="W" u2="&#x2e;" k="41" />
<hkern u1="W" u2="z" k="9" />
<hkern u1="W" u2="u" k="21" />
<hkern u1="W" u2="o" k="36" />
<hkern u1="W" u2="a" k="37" />
<hkern u1="W" u2="&#x20;" k="23" />
<hkern u1="C" g2="parenright.case" k="19" />
<hkern u1="C" g2="braceright.case" k="23" />
<hkern u1="C" g2="bracketright.case" k="23" />
<hkern u1="C" u2="&#x29;" k="17" />
<hkern u1="C" u2="&#x7d;" k="18" />
<hkern u1="C" u2="]" k="18" />
<hkern u1="C" u2="X" k="5" />
<hkern u1="C" u2="Y" k="13" />
<hkern u1="C" u2="W" k="6" />
<hkern u1="C" u2="V" k="10" />
<hkern u1="C" u2="A" k="6" />
<hkern u1="U" u2="d" k="6" />
<hkern u1="U" g2="parenright.case" k="13" />
<hkern u1="U" g2="braceright.case" k="14" />
<hkern u1="U" g2="bracketright.case" k="14" />
<hkern u1="U" u2="&#xc6;" k="9" />
<hkern u1="U" u2="&#xf0;" k="8" />
<hkern u1="U" u2="s" k="6" />
<hkern u1="U" u2="J" k="16" />
<hkern u1="U" u2="A" k="13" />
<hkern u1="U" u2="&#x2e;" k="10" />
<hkern u1="U" u2="z" k="7" />
<hkern u1="U" u2="u" k="7" />
<hkern u1="U" u2="o" k="6" />
<hkern u1="U" u2="a" k="6" />
<hkern u1="X" u2="d" k="25" />
<hkern u1="X" u2="&#x173;" k="15" />
<hkern u1="X" u2="&#x119;" k="25" />
<hkern u1="X" u2="&#x167;" k="7" />
<hkern u1="X" g2="uni00AD.case" k="18" />
<hkern u1="X" u2="&#x219;" k="5" />
<hkern u1="X" u2="&#x218;" k="6" />
<hkern u1="X" u2="&#x21b;" k="7" />
<hkern u1="X" u2="&#x163;" k="7" />
<hkern u1="X" u2="&#x15f;" k="5" />
<hkern u1="X" u2="&#x15e;" k="6" />
<hkern u1="X" u2="&#x122;" k="21" />
<hkern u1="X" u2="&#x111;" k="25" />
<hkern u1="X" u2="&#x1ff;" k="25" />
<hkern u1="X" u2="&#x1fe;" k="21" />
<hkern u1="X" u2="&#x165;" k="7" />
<hkern u1="X" u2="&#x1ef3;" k="24" />
<hkern u1="X" u2="&#x1e85;" k="21" />
<hkern u1="X" u2="&#x1e83;" k="21" />
<hkern u1="X" u2="&#x1e81;" k="21" />
<hkern u1="X" u2="&#x177;" k="24" />
<hkern u1="X" u2="&#x175;" k="21" />
<hkern u1="X" u2="&#x171;" k="15" />
<hkern u1="X" u2="&#x16f;" k="15" />
<hkern u1="X" u2="&#x169;" k="15" />
<hkern u1="X" u2="&#x15d;" k="5" />
<hkern u1="X" u2="&#x15c;" k="6" />
<hkern u1="X" u2="&#x15b;" k="5" />
<hkern u1="X" u2="&#x15a;" k="6" />
<hkern u1="X" u2="&#x151;" k="25" />
<hkern u1="X" u2="&#x150;" k="21" />
<hkern u1="X" u2="&#x123;" k="25" />
<hkern u1="X" u2="&#x121;" k="25" />
<hkern u1="X" u2="&#x11d;" k="25" />
<hkern u1="X" u2="&#x11c;" k="21" />
<hkern u1="X" u2="&#x11b;" k="25" />
<hkern u1="X" u2="&#x117;" k="25" />
<hkern u1="X" u2="&#x16d;" k="15" />
<hkern u1="X" u2="&#x14f;" k="25" />
<hkern u1="X" u2="&#x14e;" k="21" />
<hkern u1="X" u2="&#x11f;" k="25" />
<hkern u1="X" u2="&#x11e;" k="21" />
<hkern u1="X" u2="&#x115;" k="25" />
<hkern u1="X" u2="&#x16b;" k="15" />
<hkern u1="X" u2="&#x14d;" k="25" />
<hkern u1="X" u2="&#x14c;" k="21" />
<hkern u1="X" u2="&#x113;" k="25" />
<hkern u1="X" u2="&#x10f;" k="25" />
<hkern u1="X" u2="&#x10d;" k="25" />
<hkern u1="X" u2="&#x10c;" k="21" />
<hkern u1="X" u2="&#x10b;" k="25" />
<hkern u1="X" u2="&#x10a;" k="21" />
<hkern u1="X" u2="&#x109;" k="25" />
<hkern u1="X" u2="&#x108;" k="21" />
<hkern u1="X" u2="&#x107;" k="25" />
<hkern u1="X" u2="&#x106;" k="21" />
<hkern u1="X" u2="&#x120;" k="21" />
<hkern u1="X" u2="&#xad;" k="13" />
<hkern u1="X" g2="hyphen.case" k="18" />
<hkern u1="X" g2="emdash.case" k="18" />
<hkern u1="X" g2="endash.case" k="18" />
<hkern u1="X" g2="guilsinglleft.case" k="23" />
<hkern u1="X" g2="guillemotleft.case" k="23" />
<hkern u1="X" u2="&#xd5;" k="21" />
<hkern u1="X" u2="&#xf5;" k="25" />
<hkern u1="X" u2="f" k="7" />
<hkern u1="X" u2="&#x2039;" k="18" />
<hkern u1="X" u2="&#xd8;" k="21" />
<hkern u1="X" u2="&#x152;" k="21" />
<hkern u1="X" u2="&#x153;" k="25" />
<hkern u1="X" u2="&#xf8;" k="25" />
<hkern u1="X" u2="&#x161;" k="5" />
<hkern u1="X" u2="&#x160;" k="6" />
<hkern u1="X" u2="&#xd6;" k="21" />
<hkern u1="X" u2="&#xd2;" k="21" />
<hkern u1="X" u2="&#xd4;" k="21" />
<hkern u1="X" u2="&#xd3;" k="21" />
<hkern u1="X" u2="g" k="25" />
<hkern u1="X" u2="s" k="5" />
<hkern u1="X" u2="&#xab;" k="18" />
<hkern u1="X" u2="&#xc7;" k="21" />
<hkern u1="X" u2="&#xff;" k="24" />
<hkern u1="X" u2="&#xfc;" k="15" />
<hkern u1="X" u2="&#xfb;" k="15" />
<hkern u1="X" u2="&#xf9;" k="15" />
<hkern u1="X" u2="&#xfa;" k="15" />
<hkern u1="X" u2="&#xf6;" k="25" />
<hkern u1="X" u2="&#xf4;" k="25" />
<hkern u1="X" u2="&#xf2;" k="25" />
<hkern u1="X" u2="&#xf3;" k="25" />
<hkern u1="X" u2="&#xeb;" k="25" />
<hkern u1="X" u2="&#xea;" k="25" />
<hkern u1="X" u2="&#xe8;" k="25" />
<hkern u1="X" u2="&#xe9;" k="25" />
<hkern u1="X" u2="&#xfd;" k="24" />
<hkern u1="X" u2="&#x2014;" k="13" />
<hkern u1="X" u2="&#x2013;" k="13" />
<hkern u1="X" u2="&#xe7;" k="25" />
<hkern u1="X" u2="&#x2d;" k="13" />
<hkern u1="X" g2="fl" k="7" />
<hkern u1="X" g2="fi" k="7" />
<hkern u1="X" u2="G" k="21" />
<hkern u1="X" u2="C" k="21" />
<hkern u1="X" u2="Q" k="21" />
<hkern u1="X" u2="S" k="6" />
<hkern u1="X" u2="O" k="21" />
<hkern u1="X" u2="y" k="24" />
<hkern u1="X" u2="w" k="21" />
<hkern u1="X" u2="c" k="25" />
<hkern u1="X" u2="e" k="25" />
<hkern u1="X" u2="u" k="15" />
<hkern u1="X" u2="o" k="25" />
<hkern u1="X" u2="t" k="7" />
<hkern u1="X" u2="q" k="25" />
<hkern u1="X" g2="four.plf" k="12" />
<hkern u1="X" u2="&#xaa;" k="10" />
<hkern u1="X" u2="&#xba;" k="10" />
<hkern u1="X" u2="&#xf0;" k="22" />
<hkern u1="X" u2="&#xae;" k="19" />
<hkern u1="X" u2="&#xa9;" k="19" />
<hkern u1="X" u2="v" k="23" />
<hkern u1="G" u2="&#x29;" k="14" />
<hkern u1="G" u2="&#x7d;" k="19" />
<hkern u1="G" u2="]" k="19" />
<hkern u1="G" u2="Y" k="23" />
<hkern u1="G" u2="W" k="12" />
<hkern u1="G" u2="V" k="18" />
<hkern u1="G" u2="T" k="10" />
<hkern g1="fi" u2="Z" k="6" />
<hkern g1="fl" u2="&#xb7;" k="61" />
<hkern g1="fl" u2="Z" k="5" />
<hkern u1="&#x26;" u2="&#x166;" k="25" />
<hkern u1="&#x26;" u2="&#x21a;" k="25" />
<hkern u1="&#x26;" u2="&#x162;" k="25" />
<hkern u1="&#x26;" u2="&#x1ef2;" k="42" />
<hkern u1="&#x26;" u2="&#x1e84;" k="31" />
<hkern u1="&#x26;" u2="&#x1e82;" k="31" />
<hkern u1="&#x26;" u2="&#x1e80;" k="31" />
<hkern u1="&#x26;" u2="&#x1ef3;" k="20" />
<hkern u1="&#x26;" u2="&#x1e85;" k="13" />
<hkern u1="&#x26;" u2="&#x1e83;" k="13" />
<hkern u1="&#x26;" u2="&#x1e81;" k="13" />
<hkern u1="&#x26;" u2="&#x177;" k="20" />
<hkern u1="&#x26;" u2="&#x176;" k="42" />
<hkern u1="&#x26;" u2="&#x175;" k="13" />
<hkern u1="&#x26;" u2="&#x174;" k="31" />
<hkern u1="&#x26;" u2="&#x164;" k="25" />
<hkern u1="&#x26;" u2="&#x27;" k="24" />
<hkern u1="&#x26;" u2="&#x22;" k="24" />
<hkern u1="&#x26;" u2="&#x2019;" k="29" />
<hkern u1="&#x26;" u2="&#x201d;" k="29" />
<hkern u1="&#x26;" u2="&#x178;" k="42" />
<hkern u1="&#x26;" u2="&#xff;" k="20" />
<hkern u1="&#x26;" u2="&#xfd;" k="20" />
<hkern u1="&#x26;" u2="&#xdd;" k="42" />
<hkern u1="&#x26;" u2="Y" k="42" />
<hkern u1="&#x26;" u2="W" k="31" />
<hkern u1="&#x26;" u2="T" k="25" />
<hkern u1="&#x26;" u2="y" k="20" />
<hkern u1="&#x26;" u2="w" k="13" />
<hkern u1="&#x26;" u2="V" k="38" />
<hkern u1="&#x26;" u2="v" k="18" />
<hkern u1="&#x2d;" u2="&#x166;" k="11" />
<hkern u1="&#x2d;" g2="seven.plf" k="27" />
<hkern u1="&#x2d;" g2="one.plf" k="17" />
<hkern u1="&#x2d;" u2="X" k="12" />
<hkern u1="&#x2d;" u2="Y" k="42" />
<hkern u1="&#x2d;" u2="W" k="12" />
<hkern u1="&#x2d;" u2="V" k="23" />
<hkern u1="&#x2d;" u2="J" k="43" />
<hkern u1="&#x2d;" u2="T" k="39" />
<hkern u1="&#x2d;" u2="x" k="15" />
<hkern u1="&#x2d;" u2="z" k="14" />
<hkern u1="&#xe7;" u2="&#x29;" k="30" />
<hkern u1="&#xe7;" u2="&#x2122;" k="20" />
<hkern u1="&#xe7;" u2="&#x7d;" k="30" />
<hkern u1="&#xe7;" u2="&#x2018;" k="12" />
<hkern u1="&#xe7;" u2="&#x2019;" k="14" />
<hkern u1="&#xe7;" u2="\" k="24" />
<hkern u1="&#xe7;" u2="]" k="30" />
<hkern u1="&#xe7;" u2="X" k="6" />
<hkern u1="&#xe7;" u2="Y" k="71" />
<hkern u1="&#xe7;" u2="W" k="30" />
<hkern u1="&#xe7;" u2="V" k="44" />
<hkern u1="&#xe7;" u2="T" k="84" />
<hkern u1="&#xe7;" u2="S" k="5" />
<hkern u1="&#xe7;" u2="A" k="5" />
<hkern u1="&#xe7;" u2="x" k="8" />
<hkern u1="&#xe7;" u2="y" k="7" />
<hkern u1="&#xe7;" u2="v" k="5" />
<hkern u1="&#x2f;" u2="d" k="31" />
<hkern u1="&#x2f;" u2="&#x105;" k="27" />
<hkern u1="&#x2f;" u2="&#x104;" k="48" />
<hkern u1="&#x2f;" u2="&#x173;" k="13" />
<hkern u1="&#x2f;" u2="&#x119;" k="30" />
<hkern u1="&#x2f;" u2="&#x1fa;" k="48" />
<hkern u1="&#x2f;" u2="&#x1fb;" k="27" />
<hkern u1="&#x2f;" u2="&#x219;" k="24" />
<hkern u1="&#x2f;" u2="&#x15f;" k="24" />
<hkern u1="&#x2f;" u2="&#x157;" k="15" />
<hkern u1="&#x2f;" u2="&#x146;" k="15" />
<hkern u1="&#x2f;" u2="&#x149;" k="15" />
<hkern u1="&#x2f;" u2="&#x111;" k="31" />
<hkern u1="&#x2f;" u2="&#x1fd;" k="27" />
<hkern u1="&#x2f;" u2="&#x1ff;" k="30" />
<hkern u1="&#x2f;" u2="&#x14b;" k="15" />
<hkern u1="&#x2f;" u2="&#x1fc;" k="56" />
<hkern u1="&#x2f;" u2="&#x171;" k="13" />
<hkern u1="&#x2f;" u2="&#x16f;" k="13" />
<hkern u1="&#x2f;" u2="&#x169;" k="13" />
<hkern u1="&#x2f;" u2="&#x15d;" k="24" />
<hkern u1="&#x2f;" u2="&#x15b;" k="24" />
<hkern u1="&#x2f;" u2="&#x159;" k="15" />
<hkern u1="&#x2f;" u2="&#x155;" k="15" />
<hkern u1="&#x2f;" u2="&#x151;" k="30" />
<hkern u1="&#x2f;" u2="&#x148;" k="15" />
<hkern u1="&#x2f;" u2="&#x144;" k="15" />
<hkern u1="&#x2f;" u2="&#x134;" k="50" />
<hkern u1="&#x2f;" u2="&#x123;" k="31" />
<hkern u1="&#x2f;" u2="&#x121;" k="31" />
<hkern u1="&#x2f;" u2="&#x11d;" k="31" />
<hkern u1="&#x2f;" u2="&#x11b;" k="30" />
<hkern u1="&#x2f;" u2="&#x117;" k="30" />
<hkern u1="&#x2f;" u2="&#x16d;" k="13" />
<hkern u1="&#x2f;" u2="&#x14f;" k="30" />
<hkern u1="&#x2f;" u2="&#x11f;" k="31" />
<hkern u1="&#x2f;" u2="&#x115;" k="30" />
<hkern u1="&#x2f;" u2="&#x16b;" k="13" />
<hkern u1="&#x2f;" u2="&#x14d;" k="30" />
<hkern u1="&#x2f;" u2="&#x113;" k="30" />
<hkern u1="&#x2f;" u2="&#x10f;" k="31" />
<hkern u1="&#x2f;" u2="&#x10d;" k="30" />
<hkern u1="&#x2f;" u2="&#x10b;" k="30" />
<hkern u1="&#x2f;" u2="&#x109;" k="30" />
<hkern u1="&#x2f;" u2="&#x107;" k="30" />
<hkern u1="&#x2f;" u2="&#x103;" k="27" />
<hkern u1="&#x2f;" u2="&#x102;" k="48" />
<hkern u1="&#x2f;" u2="&#x101;" k="27" />
<hkern u1="&#x2f;" u2="&#x100;" k="48" />
<hkern u1="&#x2f;" u2="&#xc3;" k="48" />
<hkern u1="&#x2f;" u2="&#xf5;" k="30" />
<hkern u1="&#x2f;" u2="&#xf1;" k="15" />
<hkern u1="&#x2f;" u2="&#xe3;" k="27" />
<hkern u1="&#x2f;" u2="n" k="15" />
<hkern u1="&#x2f;" u2="&#xc6;" k="56" />
<hkern u1="&#x2f;" u2="&#x153;" k="30" />
<hkern u1="&#x2f;" u2="&#xe6;" k="27" />
<hkern u1="&#x2f;" u2="&#xf8;" k="30" />
<hkern u1="&#x2f;" u2="&#xc5;" k="48" />
<hkern u1="&#x2f;" u2="&#xe5;" k="27" />
<hkern u1="&#x2f;" u2="&#x161;" k="24" />
<hkern u1="&#x2f;" u2="g" k="31" />
<hkern u1="&#x2f;" u2="s" k="24" />
<hkern u1="&#x2f;" u2="&#xc1;" k="48" />
<hkern u1="&#x2f;" u2="&#xc2;" k="48" />
<hkern u1="&#x2f;" u2="&#xc0;" k="48" />
<hkern u1="&#x2f;" u2="&#xfc;" k="13" />
<hkern u1="&#x2f;" u2="&#xfb;" k="13" />
<hkern u1="&#x2f;" u2="&#xf9;" k="13" />
<hkern u1="&#x2f;" u2="&#xfa;" k="13" />
<hkern u1="&#x2f;" u2="&#xf6;" k="30" />
<hkern u1="&#x2f;" u2="&#xf4;" k="30" />
<hkern u1="&#x2f;" u2="&#xf2;" k="30" />
<hkern u1="&#x2f;" u2="&#xf3;" k="30" />
<hkern u1="&#x2f;" u2="&#xeb;" k="30" />
<hkern u1="&#x2f;" u2="&#xea;" k="30" />
<hkern u1="&#x2f;" u2="&#xe8;" k="30" />
<hkern u1="&#x2f;" u2="&#xe9;" k="30" />
<hkern u1="&#x2f;" u2="&#xe4;" k="27" />
<hkern u1="&#x2f;" u2="&#xe2;" k="27" />
<hkern u1="&#x2f;" u2="&#xe0;" k="27" />
<hkern u1="&#x2f;" u2="&#xe1;" k="27" />
<hkern u1="&#x2f;" u2="&#xc4;" k="48" />
<hkern u1="&#x2f;" u2="&#xe7;" k="30" />
<hkern u1="&#x2f;" u2="J" k="50" />
<hkern u1="&#x2f;" u2="A" k="48" />
<hkern u1="&#x2f;" u2="r" k="15" />
<hkern u1="&#x2f;" u2="m" k="15" />
<hkern u1="&#x2f;" u2="c" k="30" />
<hkern u1="&#x2f;" u2="e" k="30" />
<hkern u1="&#x2f;" u2="u" k="13" />
<hkern u1="&#x2f;" u2="o" k="30" />
<hkern u1="&#x2f;" u2="a" k="27" />
<hkern u1="&#x2f;" u2="q" k="31" />
<hkern u1="&#x2f;" u2="p" k="15" />
<hkern u1="&#x2f;" u2="&#x129;" k="-15" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-18" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-27" />
<hkern u1="&#x2f;" g2="four.plf" k="42" />
<hkern u1="&#x2f;" u2="&#xf0;" k="27" />
<hkern u1="&#x2f;" u2="&#x2f;" k="208" />
<hkern u1="[" u2="d" k="34" />
<hkern u1="[" u2="&#x105;" k="30" />
<hkern u1="[" u2="&#x104;" k="33" />
<hkern u1="[" u2="&#x173;" k="24" />
<hkern u1="[" u2="&#x119;" k="34" />
<hkern u1="[" u2="&#x1fa;" k="33" />
<hkern u1="[" u2="&#x1fb;" k="30" />
<hkern u1="[" u2="&#x219;" k="29" />
<hkern u1="[" u2="&#x218;" k="16" />
<hkern u1="[" u2="&#x15f;" k="29" />
<hkern u1="[" u2="&#x15e;" k="16" />
<hkern u1="[" u2="&#x157;" k="25" />
<hkern u1="[" u2="&#x146;" k="25" />
<hkern u1="[" u2="&#x122;" k="24" />
<hkern u1="[" u2="&#x149;" k="25" />
<hkern u1="[" u2="&#x111;" k="34" />
<hkern u1="[" u2="&#x1fd;" k="30" />
<hkern u1="[" u2="&#x1ff;" k="34" />
<hkern u1="[" u2="&#x1fe;" k="24" />
<hkern u1="[" u2="&#x14b;" k="25" />
<hkern u1="[" u2="&#x1fc;" k="19" />
<hkern u1="[" u2="&#x1e85;" k="17" />
<hkern u1="[" u2="&#x1e83;" k="17" />
<hkern u1="[" u2="&#x1e81;" k="17" />
<hkern u1="[" u2="&#x17c;" k="21" />
<hkern u1="[" u2="&#x17a;" k="21" />
<hkern u1="[" u2="&#x175;" k="17" />
<hkern u1="[" u2="&#x171;" k="24" />
<hkern u1="[" u2="&#x16f;" k="24" />
<hkern u1="[" u2="&#x169;" k="24" />
<hkern u1="[" u2="&#x15d;" k="29" />
<hkern u1="[" u2="&#x15c;" k="16" />
<hkern u1="[" u2="&#x15b;" k="29" />
<hkern u1="[" u2="&#x15a;" k="16" />
<hkern u1="[" u2="&#x159;" k="25" />
<hkern u1="[" u2="&#x155;" k="25" />
<hkern u1="[" u2="&#x151;" k="34" />
<hkern u1="[" u2="&#x150;" k="24" />
<hkern u1="[" u2="&#x148;" k="25" />
<hkern u1="[" u2="&#x144;" k="25" />
<hkern u1="[" u2="&#x134;" k="20" />
<hkern u1="[" u2="&#x123;" k="34" />
<hkern u1="[" u2="&#x121;" k="34" />
<hkern u1="[" u2="&#x11d;" k="34" />
<hkern u1="[" u2="&#x11c;" k="24" />
<hkern u1="[" u2="&#x11b;" k="34" />
<hkern u1="[" u2="&#x117;" k="34" />
<hkern u1="[" u2="&#x16d;" k="24" />
<hkern u1="[" u2="&#x14f;" k="34" />
<hkern u1="[" u2="&#x14e;" k="24" />
<hkern u1="[" u2="&#x11f;" k="34" />
<hkern u1="[" u2="&#x11e;" k="24" />
<hkern u1="[" u2="&#x115;" k="34" />
<hkern u1="[" u2="&#x16b;" k="24" />
<hkern u1="[" u2="&#x14d;" k="34" />
<hkern u1="[" u2="&#x14c;" k="24" />
<hkern u1="[" u2="&#x113;" k="34" />
<hkern u1="[" u2="&#x10f;" k="34" />
<hkern u1="[" u2="&#x10d;" k="34" />
<hkern u1="[" u2="&#x10c;" k="24" />
<hkern u1="[" u2="&#x10b;" k="34" />
<hkern u1="[" u2="&#x10a;" k="24" />
<hkern u1="[" u2="&#x109;" k="34" />
<hkern u1="[" u2="&#x108;" k="24" />
<hkern u1="[" u2="&#x107;" k="34" />
<hkern u1="[" u2="&#x106;" k="24" />
<hkern u1="[" u2="&#x103;" k="30" />
<hkern u1="[" u2="&#x102;" k="33" />
<hkern u1="[" u2="&#x101;" k="30" />
<hkern u1="[" u2="&#x100;" k="33" />
<hkern u1="[" u2="&#x120;" k="24" />
<hkern u1="[" g2="zero.plfslash" k="22" />
<hkern u1="[" g2="zero.plf" k="22" />
<hkern u1="[" u2="&#xd5;" k="24" />
<hkern u1="[" u2="&#xc3;" k="33" />
<hkern u1="[" u2="&#xf5;" k="34" />
<hkern u1="[" u2="&#xf1;" k="25" />
<hkern u1="[" u2="&#xe3;" k="30" />
<hkern u1="[" u2="n" k="25" />
<hkern u1="[" u2="&#xd8;" k="24" />
<hkern u1="[" u2="&#xc6;" k="19" />
<hkern u1="[" u2="&#x152;" k="24" />
<hkern u1="[" u2="&#x153;" k="34" />
<hkern u1="[" u2="&#xe6;" k="30" />
<hkern u1="[" u2="&#xf8;" k="34" />
<hkern u1="[" u2="&#xc5;" k="33" />
<hkern u1="[" u2="&#xe5;" k="30" />
<hkern u1="[" u2="&#x17e;" k="21" />
<hkern u1="[" u2="&#x161;" k="29" />
<hkern u1="[" u2="&#x160;" k="16" />
<hkern u1="[" u2="&#xd6;" k="24" />
<hkern u1="[" u2="&#xd2;" k="24" />
<hkern u1="[" u2="&#xd4;" k="24" />
<hkern u1="[" u2="&#xd3;" k="24" />
<hkern u1="[" u2="g" k="34" />
<hkern u1="[" u2="s" k="29" />
<hkern u1="[" u2="&#xc7;" k="24" />
<hkern u1="[" u2="&#xc1;" k="33" />
<hkern u1="[" u2="&#xc2;" k="33" />
<hkern u1="[" u2="&#xc0;" k="33" />
<hkern u1="[" u2="&#xfc;" k="24" />
<hkern u1="[" u2="&#xfb;" k="24" />
<hkern u1="[" u2="&#xf9;" k="24" />
<hkern u1="[" u2="&#xfa;" k="24" />
<hkern u1="[" u2="&#xf6;" k="34" />
<hkern u1="[" u2="&#xf4;" k="34" />
<hkern u1="[" u2="&#xf2;" k="34" />
<hkern u1="[" u2="&#xf3;" k="34" />
<hkern u1="[" u2="&#xeb;" k="34" />
<hkern u1="[" u2="&#xea;" k="34" />
<hkern u1="[" u2="&#xe8;" k="34" />
<hkern u1="[" u2="&#xe9;" k="34" />
<hkern u1="[" u2="&#xe4;" k="30" />
<hkern u1="[" u2="&#xe2;" k="30" />
<hkern u1="[" u2="&#xe0;" k="30" />
<hkern u1="[" u2="&#xe1;" k="30" />
<hkern u1="[" u2="&#xc4;" k="33" />
<hkern u1="[" u2="&#xe7;" k="34" />
<hkern u1="[" u2="G" k="24" />
<hkern u1="[" u2="C" k="24" />
<hkern u1="[" u2="J" k="20" />
<hkern u1="[" u2="Q" k="24" />
<hkern u1="[" u2="S" k="16" />
<hkern u1="[" u2="O" k="24" />
<hkern u1="[" u2="A" k="33" />
<hkern u1="[" u2="z" k="21" />
<hkern u1="[" u2="w" k="17" />
<hkern u1="[" u2="r" k="25" />
<hkern u1="[" u2="m" k="25" />
<hkern u1="[" u2="c" k="34" />
<hkern u1="[" u2="e" k="34" />
<hkern u1="[" u2="u" k="24" />
<hkern u1="[" u2="o" k="34" />
<hkern u1="[" u2="a" k="30" />
<hkern u1="[" u2="q" k="34" />
<hkern u1="[" u2="p" k="25" />
<hkern u1="[" u2="&#x12b;" k="-8" />
<hkern u1="[" g2="nine.plf" k="18" />
<hkern u1="[" g2="eight.plf" k="20" />
<hkern u1="[" g2="six.plf" k="23" />
<hkern u1="[" g2="four.plf" k="33" />
<hkern u1="[" g2="three.plf" k="18" />
<hkern u1="[" g2="two.plf" k="15" />
<hkern u1="[" g2="one.plf" k="11" />
<hkern u1="[" u2="&#xf0;" k="31" />
<hkern u1="[" u2="&#x28;" k="19" />
<hkern u1="[" u2="x" k="18" />
<hkern u1="[" u2="v" k="18" />
<hkern u1="\" u2="&#x21a;" k="43" />
<hkern u1="\" u2="&#x162;" k="43" />
<hkern u1="\" u2="&#x1ef2;" k="55" />
<hkern u1="\" u2="&#x1e84;" k="33" />
<hkern u1="\" u2="&#x1e82;" k="33" />
<hkern u1="\" u2="&#x1e80;" k="33" />
<hkern u1="\" u2="&#x1ef3;" k="27" />
<hkern u1="\" u2="&#x1e85;" k="18" />
<hkern u1="\" u2="&#x1e83;" k="18" />
<hkern u1="\" u2="&#x1e81;" k="18" />
<hkern u1="\" u2="&#x177;" k="27" />
<hkern u1="\" u2="&#x176;" k="55" />
<hkern u1="\" u2="&#x175;" k="18" />
<hkern u1="\" u2="&#x174;" k="33" />
<hkern u1="\" u2="&#x164;" k="43" />
<hkern u1="\" u2="&#x27;" k="44" />
<hkern u1="\" u2="&#x22;" k="44" />
<hkern u1="\" u2="&#x2019;" k="53" />
<hkern u1="\" u2="&#x201d;" k="53" />
<hkern u1="\" u2="&#x178;" k="55" />
<hkern u1="\" u2="&#xff;" k="27" />
<hkern u1="\" u2="&#xfd;" k="27" />
<hkern u1="\" u2="&#xdd;" k="55" />
<hkern u1="\" u2="Y" k="55" />
<hkern u1="\" u2="W" k="33" />
<hkern u1="\" u2="T" k="43" />
<hkern u1="\" u2="y" k="27" />
<hkern u1="\" u2="w" k="18" />
<hkern u1="\" u2="&#x166;" k="37" />
<hkern u1="\" g2="one.plf" k="42" />
<hkern u1="\" u2="V" k="47" />
<hkern u1="\" u2="v" k="25" />
<hkern u1="&#x28;" u2="d" k="33" />
<hkern u1="&#x28;" u2="&#x105;" k="29" />
<hkern u1="&#x28;" u2="&#x104;" k="17" />
<hkern u1="&#x28;" u2="&#x173;" k="27" />
<hkern u1="&#x28;" u2="&#x119;" k="33" />
<hkern u1="&#x28;" u2="&#x1fa;" k="17" />
<hkern u1="&#x28;" u2="&#x1fb;" k="29" />
<hkern u1="&#x28;" u2="&#x167;" k="13" />
<hkern u1="&#x28;" u2="&#x219;" k="28" />
<hkern u1="&#x28;" u2="&#x218;" k="16" />
<hkern u1="&#x28;" u2="&#x21b;" k="13" />
<hkern u1="&#x28;" u2="&#x163;" k="13" />
<hkern u1="&#x28;" u2="&#x15f;" k="28" />
<hkern u1="&#x28;" u2="&#x15e;" k="16" />
<hkern u1="&#x28;" u2="&#x157;" k="24" />
<hkern u1="&#x28;" u2="&#x146;" k="24" />
<hkern u1="&#x28;" u2="&#x122;" k="24" />
<hkern u1="&#x28;" u2="&#x149;" k="24" />
<hkern u1="&#x28;" u2="&#x111;" k="33" />
<hkern u1="&#x28;" u2="&#x1fd;" k="29" />
<hkern u1="&#x28;" u2="&#x1ff;" k="33" />
<hkern u1="&#x28;" u2="&#x1fe;" k="24" />
<hkern u1="&#x28;" u2="&#x165;" k="13" />
<hkern u1="&#x28;" u2="&#x14b;" k="24" />
<hkern u1="&#x28;" u2="&#x1e85;" k="23" />
<hkern u1="&#x28;" u2="&#x1e83;" k="23" />
<hkern u1="&#x28;" u2="&#x1e81;" k="23" />
<hkern u1="&#x28;" u2="&#x17c;" k="16" />
<hkern u1="&#x28;" u2="&#x17a;" k="16" />
<hkern u1="&#x28;" u2="&#x175;" k="23" />
<hkern u1="&#x28;" u2="&#x171;" k="27" />
<hkern u1="&#x28;" u2="&#x16f;" k="27" />
<hkern u1="&#x28;" u2="&#x169;" k="27" />
<hkern u1="&#x28;" u2="&#x15d;" k="28" />
<hkern u1="&#x28;" u2="&#x15c;" k="16" />
<hkern u1="&#x28;" u2="&#x15b;" k="28" />
<hkern u1="&#x28;" u2="&#x15a;" k="16" />
<hkern u1="&#x28;" u2="&#x159;" k="24" />
<hkern u1="&#x28;" u2="&#x155;" k="24" />
<hkern u1="&#x28;" u2="&#x151;" k="33" />
<hkern u1="&#x28;" u2="&#x150;" k="24" />
<hkern u1="&#x28;" u2="&#x148;" k="24" />
<hkern u1="&#x28;" u2="&#x144;" k="24" />
<hkern u1="&#x28;" u2="&#x123;" k="33" />
<hkern u1="&#x28;" u2="&#x121;" k="33" />
<hkern u1="&#x28;" u2="&#x11d;" k="33" />
<hkern u1="&#x28;" u2="&#x11c;" k="24" />
<hkern u1="&#x28;" u2="&#x11b;" k="33" />
<hkern u1="&#x28;" u2="&#x117;" k="33" />
<hkern u1="&#x28;" u2="&#x16d;" k="27" />
<hkern u1="&#x28;" u2="&#x14f;" k="33" />
<hkern u1="&#x28;" u2="&#x14e;" k="24" />
<hkern u1="&#x28;" u2="&#x11f;" k="33" />
<hkern u1="&#x28;" u2="&#x11e;" k="24" />
<hkern u1="&#x28;" u2="&#x115;" k="33" />
<hkern u1="&#x28;" u2="&#x16b;" k="27" />
<hkern u1="&#x28;" u2="&#x14d;" k="33" />
<hkern u1="&#x28;" u2="&#x14c;" k="24" />
<hkern u1="&#x28;" u2="&#x113;" k="33" />
<hkern u1="&#x28;" u2="&#x10f;" k="33" />
<hkern u1="&#x28;" u2="&#x10d;" k="33" />
<hkern u1="&#x28;" u2="&#x10c;" k="24" />
<hkern u1="&#x28;" u2="&#x10b;" k="33" />
<hkern u1="&#x28;" u2="&#x10a;" k="24" />
<hkern u1="&#x28;" u2="&#x109;" k="33" />
<hkern u1="&#x28;" u2="&#x108;" k="24" />
<hkern u1="&#x28;" u2="&#x107;" k="33" />
<hkern u1="&#x28;" u2="&#x106;" k="24" />
<hkern u1="&#x28;" u2="&#x103;" k="29" />
<hkern u1="&#x28;" u2="&#x102;" k="17" />
<hkern u1="&#x28;" u2="&#x101;" k="29" />
<hkern u1="&#x28;" u2="&#x100;" k="17" />
<hkern u1="&#x28;" u2="&#x120;" k="24" />
<hkern u1="&#x28;" g2="zero.plfslash" k="22" />
<hkern u1="&#x28;" g2="zero.plf" k="22" />
<hkern u1="&#x28;" u2="&#xd5;" k="24" />
<hkern u1="&#x28;" u2="&#xc3;" k="17" />
<hkern u1="&#x28;" u2="&#xf5;" k="33" />
<hkern u1="&#x28;" u2="&#xf1;" k="24" />
<hkern u1="&#x28;" u2="&#xe3;" k="29" />
<hkern u1="&#x28;" u2="n" k="24" />
<hkern u1="&#x28;" u2="f" k="13" />
<hkern u1="&#x28;" u2="&#xd8;" k="24" />
<hkern u1="&#x28;" u2="&#x152;" k="24" />
<hkern u1="&#x28;" u2="&#x153;" k="33" />
<hkern u1="&#x28;" u2="&#xe6;" k="29" />
<hkern u1="&#x28;" u2="&#xf8;" k="33" />
<hkern u1="&#x28;" u2="&#xc5;" k="17" />
<hkern u1="&#x28;" u2="&#xe5;" k="29" />
<hkern u1="&#x28;" u2="&#x17e;" k="16" />
<hkern u1="&#x28;" u2="&#x161;" k="28" />
<hkern u1="&#x28;" u2="&#x160;" k="16" />
<hkern u1="&#x28;" u2="&#xd6;" k="24" />
<hkern u1="&#x28;" u2="&#xd2;" k="24" />
<hkern u1="&#x28;" u2="&#xd4;" k="24" />
<hkern u1="&#x28;" u2="&#xd3;" k="24" />
<hkern u1="&#x28;" u2="g" k="33" />
<hkern u1="&#x28;" u2="s" k="28" />
<hkern u1="&#x28;" u2="&#xc7;" k="24" />
<hkern u1="&#x28;" u2="&#xc1;" k="17" />
<hkern u1="&#x28;" u2="&#xc2;" k="17" />
<hkern u1="&#x28;" u2="&#xc0;" k="17" />
<hkern u1="&#x28;" u2="&#xfc;" k="27" />
<hkern u1="&#x28;" u2="&#xfb;" k="27" />
<hkern u1="&#x28;" u2="&#xf9;" k="27" />
<hkern u1="&#x28;" u2="&#xfa;" k="27" />
<hkern u1="&#x28;" u2="&#xf6;" k="33" />
<hkern u1="&#x28;" u2="&#xf4;" k="33" />
<hkern u1="&#x28;" u2="&#xf2;" k="33" />
<hkern u1="&#x28;" u2="&#xf3;" k="33" />
<hkern u1="&#x28;" u2="&#xeb;" k="33" />
<hkern u1="&#x28;" u2="&#xea;" k="33" />
<hkern u1="&#x28;" u2="&#xe8;" k="33" />
<hkern u1="&#x28;" u2="&#xe9;" k="33" />
<hkern u1="&#x28;" u2="&#xe4;" k="29" />
<hkern u1="&#x28;" u2="&#xe2;" k="29" />
<hkern u1="&#x28;" u2="&#xe0;" k="29" />
<hkern u1="&#x28;" u2="&#xe1;" k="29" />
<hkern u1="&#x28;" u2="&#xc4;" k="17" />
<hkern u1="&#x28;" u2="&#xe7;" k="33" />
<hkern u1="&#x28;" g2="fl" k="13" />
<hkern u1="&#x28;" g2="fi" k="13" />
<hkern u1="&#x28;" u2="G" k="24" />
<hkern u1="&#x28;" u2="C" k="24" />
<hkern u1="&#x28;" u2="Q" k="24" />
<hkern u1="&#x28;" u2="S" k="16" />
<hkern u1="&#x28;" u2="O" k="24" />
<hkern u1="&#x28;" u2="A" k="17" />
<hkern u1="&#x28;" u2="z" k="16" />
<hkern u1="&#x28;" u2="w" k="23" />
<hkern u1="&#x28;" u2="r" k="24" />
<hkern u1="&#x28;" u2="m" k="24" />
<hkern u1="&#x28;" u2="c" k="33" />
<hkern u1="&#x28;" u2="e" k="33" />
<hkern u1="&#x28;" u2="u" k="27" />
<hkern u1="&#x28;" u2="o" k="33" />
<hkern u1="&#x28;" u2="a" k="29" />
<hkern u1="&#x28;" u2="t" k="13" />
<hkern u1="&#x28;" u2="q" k="33" />
<hkern u1="&#x28;" u2="p" k="24" />
<hkern u1="&#x28;" u2="&#x135;" k="-18" />
<hkern u1="&#x28;" g2="nine.plf" k="18" />
<hkern u1="&#x28;" g2="eight.plf" k="20" />
<hkern u1="&#x28;" g2="six.plf" k="23" />
<hkern u1="&#x28;" g2="four.plf" k="32" />
<hkern u1="&#x28;" g2="three.plf" k="17" />
<hkern u1="&#x28;" u2="&#xf0;" k="31" />
<hkern u1="&#x28;" u2="&#x28;" k="19" />
<hkern u1="&#x28;" u2="x" k="10" />
<hkern u1="&#x28;" u2="v" k="24" />
<hkern u1="&#x28;" u2="j" k="-6" />
<hkern u1="&#x2013;" u2="&#x166;" k="11" />
<hkern u1="&#x2013;" g2="seven.plf" k="27" />
<hkern u1="&#x2013;" g2="one.plf" k="17" />
<hkern u1="&#x2013;" u2="X" k="12" />
<hkern u1="&#x2013;" u2="Y" k="42" />
<hkern u1="&#x2013;" u2="W" k="12" />
<hkern u1="&#x2013;" u2="V" k="23" />
<hkern u1="&#x2013;" u2="J" k="43" />
<hkern u1="&#x2013;" u2="T" k="39" />
<hkern u1="&#x2013;" u2="x" k="15" />
<hkern u1="&#x2013;" u2="z" k="14" />
<hkern u1="&#x2014;" u2="&#x166;" k="11" />
<hkern u1="&#x2014;" g2="seven.plf" k="27" />
<hkern u1="&#x2014;" g2="one.plf" k="17" />
<hkern u1="&#x2014;" u2="X" k="12" />
<hkern u1="&#x2014;" u2="Y" k="42" />
<hkern u1="&#x2014;" u2="W" k="12" />
<hkern u1="&#x2014;" u2="V" k="23" />
<hkern u1="&#x2014;" u2="J" k="43" />
<hkern u1="&#x2014;" u2="T" k="39" />
<hkern u1="&#x2014;" u2="x" k="15" />
<hkern u1="&#x2014;" u2="z" k="14" />
<hkern u1="&#xfd;" u2="d" k="9" />
<hkern u1="&#xfd;" u2="&#x29;" k="23" />
<hkern u1="&#xfd;" u2="&#x2039;" k="19" />
<hkern u1="&#xfd;" u2="&#xc6;" k="33" />
<hkern u1="&#xfd;" u2="&#xf0;" k="15" />
<hkern u1="&#xfd;" u2="&#x7d;" k="17" />
<hkern u1="&#xfd;" u2="s" k="6" />
<hkern u1="&#xfd;" u2="]" k="17" />
<hkern u1="&#xfd;" u2="&#x2f;" k="26" />
<hkern u1="&#xfd;" u2="X" k="24" />
<hkern u1="&#xfd;" u2="Y" k="22" />
<hkern u1="&#xfd;" u2="Z" k="14" />
<hkern u1="&#xfd;" u2="J" k="61" />
<hkern u1="&#xfd;" u2="T" k="49" />
<hkern u1="&#xfd;" u2="A" k="26" />
<hkern u1="&#xfd;" u2="&#x2e;" k="35" />
<hkern u1="&#xfd;" u2="o" k="9" />
<hkern u1="&#xfd;" u2="a" k="8" />
<hkern u1="&#xfd;" u2="&#x20;" k="22" />
<hkern u1="&#xc4;" u2="d" k="10" />
<hkern u1="&#xc4;" u2="&#x166;" k="44" />
<hkern u1="&#xc4;" g2="hyphen.case" k="9" />
<hkern u1="&#xc4;" g2="braceright.case" k="11" />
<hkern u1="&#xc4;" g2="bracketright.case" k="11" />
<hkern u1="&#xc4;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc4;" u2="&#x29;" k="16" />
<hkern u1="&#xc4;" g2="nine.plf" k="10" />
<hkern u1="&#xc4;" g2="seven.plf" k="15" />
<hkern u1="&#xc4;" g2="six.plf" k="11" />
<hkern u1="&#xc4;" g2="one.plf" k="44" />
<hkern u1="&#xc4;" g2="zero.plf" k="12" />
<hkern u1="&#xc4;" u2="f" k="16" />
<hkern u1="&#xc4;" u2="&#x2039;" k="14" />
<hkern u1="&#xc4;" u2="&#x2a;" k="35" />
<hkern u1="&#xc4;" u2="&#xaa;" k="28" />
<hkern u1="&#xc4;" u2="&#xba;" k="31" />
<hkern u1="&#xc4;" u2="&#x2122;" k="43" />
<hkern u1="&#xc4;" u2="&#x27;" k="38" />
<hkern u1="&#xc4;" u2="&#xf0;" k="9" />
<hkern u1="&#xc4;" u2="&#x7d;" k="33" />
<hkern u1="&#xc4;" u2="&#xae;" k="16" />
<hkern u1="&#xc4;" u2="s" k="6" />
<hkern u1="&#xc4;" u2="&#x2018;" k="44" />
<hkern u1="&#xc4;" u2="&#x2019;" k="45" />
<hkern u1="&#xc4;" u2="&#xa9;" k="16" />
<hkern u1="&#xc4;" u2="&#x3f;" k="35" />
<hkern u1="&#xc4;" u2="\" k="48" />
<hkern u1="&#xc4;" u2="]" k="33" />
<hkern u1="&#xc4;" u2="U" k="13" />
<hkern u1="&#xc4;" u2="Y" k="57" />
<hkern u1="&#xc4;" u2="W" k="32" />
<hkern u1="&#xc4;" u2="V" k="41" />
<hkern u1="&#xc4;" u2="T" k="52" />
<hkern u1="&#xc4;" u2="S" k="9" />
<hkern u1="&#xc4;" u2="O" k="15" />
<hkern u1="&#xc4;" u2="y" k="27" />
<hkern u1="&#xc4;" u2="w" k="20" />
<hkern u1="&#xc4;" u2="v" k="25" />
<hkern u1="&#xc4;" u2="u" k="8" />
<hkern u1="&#xc4;" u2="o" k="10" />
<hkern u1="&#xc4;" u2="a" k="5" />
<hkern u1="&#xc4;" u2="&#x20;" k="26" />
<hkern u1="&#xdc;" u2="d" k="6" />
<hkern u1="&#xdc;" g2="parenright.case" k="13" />
<hkern u1="&#xdc;" g2="braceright.case" k="14" />
<hkern u1="&#xdc;" g2="bracketright.case" k="14" />
<hkern u1="&#xdc;" u2="&#xc6;" k="9" />
<hkern u1="&#xdc;" u2="&#xf0;" k="8" />
<hkern u1="&#xdc;" u2="s" k="6" />
<hkern u1="&#xdc;" u2="J" k="16" />
<hkern u1="&#xdc;" u2="A" k="13" />
<hkern u1="&#xdc;" u2="&#x2e;" k="10" />
<hkern u1="&#xdc;" u2="z" k="7" />
<hkern u1="&#xdc;" u2="u" k="7" />
<hkern u1="&#xdc;" u2="o" k="6" />
<hkern u1="&#xdc;" u2="a" k="6" />
<hkern u1="&#xe9;" u2="&#x29;" k="31" />
<hkern u1="&#xe9;" u2="f" k="4" />
<hkern u1="&#xe9;" u2="&#x2122;" k="20" />
<hkern u1="&#xe9;" u2="&#x7d;" k="32" />
<hkern u1="&#xe9;" u2="&#x2018;" k="16" />
<hkern u1="&#xe9;" u2="&#x2019;" k="18" />
<hkern u1="&#xe9;" u2="\" k="28" />
<hkern u1="&#xe9;" u2="]" k="31" />
<hkern u1="&#xe9;" u2="X" k="10" />
<hkern u1="&#xe9;" u2="U" k="5" />
<hkern u1="&#xe9;" u2="Y" k="83" />
<hkern u1="&#xe9;" u2="Z" k="8" />
<hkern u1="&#xe9;" u2="W" k="37" />
<hkern u1="&#xe9;" u2="V" k="42" />
<hkern u1="&#xe9;" u2="T" k="74" />
<hkern u1="&#xe9;" u2="A" k="7" />
<hkern u1="&#xe9;" u2="x" k="14" />
<hkern u1="&#xe9;" u2="y" k="10" />
<hkern u1="&#xe9;" u2="w" k="5" />
<hkern u1="&#xe9;" u2="v" k="8" />
<hkern u1="&#xe8;" u2="&#x29;" k="31" />
<hkern u1="&#xe8;" u2="f" k="4" />
<hkern u1="&#xe8;" u2="&#x2122;" k="20" />
<hkern u1="&#xe8;" u2="&#x7d;" k="32" />
<hkern u1="&#xe8;" u2="&#x2018;" k="16" />
<hkern u1="&#xe8;" u2="&#x2019;" k="18" />
<hkern u1="&#xe8;" u2="\" k="28" />
<hkern u1="&#xe8;" u2="]" k="31" />
<hkern u1="&#xe8;" u2="X" k="10" />
<hkern u1="&#xe8;" u2="U" k="5" />
<hkern u1="&#xe8;" u2="Y" k="83" />
<hkern u1="&#xe8;" u2="Z" k="8" />
<hkern u1="&#xe8;" u2="W" k="37" />
<hkern u1="&#xe8;" u2="V" k="42" />
<hkern u1="&#xe8;" u2="T" k="74" />
<hkern u1="&#xe8;" u2="A" k="7" />
<hkern u1="&#xe8;" u2="x" k="14" />
<hkern u1="&#xe8;" u2="y" k="10" />
<hkern u1="&#xe8;" u2="w" k="5" />
<hkern u1="&#xe8;" u2="v" k="8" />
<hkern u1="&#xea;" u2="&#x29;" k="31" />
<hkern u1="&#xea;" u2="f" k="4" />
<hkern u1="&#xea;" u2="&#x2122;" k="20" />
<hkern u1="&#xea;" u2="&#x7d;" k="32" />
<hkern u1="&#xea;" u2="&#x2018;" k="16" />
<hkern u1="&#xea;" u2="&#x2019;" k="18" />
<hkern u1="&#xea;" u2="\" k="28" />
<hkern u1="&#xea;" u2="]" k="31" />
<hkern u1="&#xea;" u2="X" k="10" />
<hkern u1="&#xea;" u2="U" k="5" />
<hkern u1="&#xea;" u2="Y" k="83" />
<hkern u1="&#xea;" u2="Z" k="8" />
<hkern u1="&#xea;" u2="W" k="37" />
<hkern u1="&#xea;" u2="V" k="42" />
<hkern u1="&#xea;" u2="T" k="74" />
<hkern u1="&#xea;" u2="A" k="7" />
<hkern u1="&#xea;" u2="x" k="14" />
<hkern u1="&#xea;" u2="y" k="10" />
<hkern u1="&#xea;" u2="w" k="5" />
<hkern u1="&#xea;" u2="v" k="8" />
<hkern u1="&#xeb;" u2="&#x29;" k="31" />
<hkern u1="&#xeb;" u2="f" k="4" />
<hkern u1="&#xeb;" u2="&#x2122;" k="20" />
<hkern u1="&#xeb;" u2="&#x7d;" k="32" />
<hkern u1="&#xeb;" u2="&#x2018;" k="16" />
<hkern u1="&#xeb;" u2="&#x2019;" k="18" />
<hkern u1="&#xeb;" u2="\" k="28" />
<hkern u1="&#xeb;" u2="]" k="31" />
<hkern u1="&#xeb;" u2="X" k="10" />
<hkern u1="&#xeb;" u2="U" k="5" />
<hkern u1="&#xeb;" u2="Y" k="83" />
<hkern u1="&#xeb;" u2="Z" k="8" />
<hkern u1="&#xeb;" u2="W" k="37" />
<hkern u1="&#xeb;" u2="V" k="42" />
<hkern u1="&#xeb;" u2="T" k="74" />
<hkern u1="&#xeb;" u2="A" k="7" />
<hkern u1="&#xeb;" u2="x" k="14" />
<hkern u1="&#xeb;" u2="y" k="10" />
<hkern u1="&#xeb;" u2="w" k="5" />
<hkern u1="&#xeb;" u2="v" k="8" />
<hkern u1="&#xed;" u2="Z" k="6" />
<hkern u1="&#xec;" u2="Z" k="6" />
<hkern u1="&#xee;" u2="Z" k="6" />
<hkern u1="&#xef;" u2="Z" k="6" />
<hkern u1="&#xfa;" u2="&#x29;" k="24" />
<hkern u1="&#xfa;" u2="&#x2122;" k="16" />
<hkern u1="&#xfa;" u2="&#x7d;" k="25" />
<hkern u1="&#xfa;" u2="\" k="15" />
<hkern u1="&#xfa;" u2="]" k="25" />
<hkern u1="&#xfa;" u2="Y" k="53" />
<hkern u1="&#xfa;" u2="Z" k="5" />
<hkern u1="&#xfa;" u2="W" k="26" />
<hkern u1="&#xfa;" u2="V" k="35" />
<hkern u1="&#xfa;" u2="T" k="59" />
<hkern u1="&#xf9;" u2="&#x29;" k="24" />
<hkern u1="&#xf9;" u2="&#x2122;" k="16" />
<hkern u1="&#xf9;" u2="&#x7d;" k="25" />
<hkern u1="&#xf9;" u2="\" k="15" />
<hkern u1="&#xf9;" u2="]" k="25" />
<hkern u1="&#xf9;" u2="Y" k="53" />
<hkern u1="&#xf9;" u2="Z" k="5" />
<hkern u1="&#xf9;" u2="W" k="26" />
<hkern u1="&#xf9;" u2="V" k="35" />
<hkern u1="&#xf9;" u2="T" k="59" />
<hkern u1="&#xfb;" u2="&#x29;" k="24" />
<hkern u1="&#xfb;" u2="&#x2122;" k="16" />
<hkern u1="&#xfb;" u2="&#x7d;" k="25" />
<hkern u1="&#xfb;" u2="\" k="15" />
<hkern u1="&#xfb;" u2="]" k="25" />
<hkern u1="&#xfb;" u2="Y" k="53" />
<hkern u1="&#xfb;" u2="Z" k="5" />
<hkern u1="&#xfb;" u2="W" k="26" />
<hkern u1="&#xfb;" u2="V" k="35" />
<hkern u1="&#xfb;" u2="T" k="59" />
<hkern u1="&#xfc;" u2="&#x29;" k="24" />
<hkern u1="&#xfc;" u2="&#x2122;" k="16" />
<hkern u1="&#xfc;" u2="&#x7d;" k="25" />
<hkern u1="&#xfc;" u2="\" k="15" />
<hkern u1="&#xfc;" u2="]" k="25" />
<hkern u1="&#xfc;" u2="Y" k="53" />
<hkern u1="&#xfc;" u2="Z" k="5" />
<hkern u1="&#xfc;" u2="W" k="26" />
<hkern u1="&#xfc;" u2="V" k="35" />
<hkern u1="&#xfc;" u2="T" k="59" />
<hkern u1="&#xc0;" u2="d" k="10" />
<hkern u1="&#xc0;" u2="&#x166;" k="44" />
<hkern u1="&#xc0;" g2="hyphen.case" k="9" />
<hkern u1="&#xc0;" g2="braceright.case" k="11" />
<hkern u1="&#xc0;" g2="bracketright.case" k="11" />
<hkern u1="&#xc0;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc0;" u2="&#x29;" k="16" />
<hkern u1="&#xc0;" g2="nine.plf" k="10" />
<hkern u1="&#xc0;" g2="seven.plf" k="15" />
<hkern u1="&#xc0;" g2="six.plf" k="11" />
<hkern u1="&#xc0;" g2="one.plf" k="44" />
<hkern u1="&#xc0;" g2="zero.plf" k="12" />
<hkern u1="&#xc0;" u2="f" k="16" />
<hkern u1="&#xc0;" u2="&#x2039;" k="14" />
<hkern u1="&#xc0;" u2="&#x2a;" k="35" />
<hkern u1="&#xc0;" u2="&#xaa;" k="28" />
<hkern u1="&#xc0;" u2="&#xba;" k="31" />
<hkern u1="&#xc0;" u2="&#x2122;" k="43" />
<hkern u1="&#xc0;" u2="&#x27;" k="38" />
<hkern u1="&#xc0;" u2="&#xf0;" k="9" />
<hkern u1="&#xc0;" u2="&#x7d;" k="33" />
<hkern u1="&#xc0;" u2="&#xae;" k="16" />
<hkern u1="&#xc0;" u2="s" k="6" />
<hkern u1="&#xc0;" u2="&#x2018;" k="44" />
<hkern u1="&#xc0;" u2="&#x2019;" k="45" />
<hkern u1="&#xc0;" u2="&#xa9;" k="16" />
<hkern u1="&#xc0;" u2="&#x3f;" k="35" />
<hkern u1="&#xc0;" u2="\" k="48" />
<hkern u1="&#xc0;" u2="]" k="33" />
<hkern u1="&#xc0;" u2="U" k="13" />
<hkern u1="&#xc0;" u2="Y" k="57" />
<hkern u1="&#xc0;" u2="W" k="32" />
<hkern u1="&#xc0;" u2="V" k="41" />
<hkern u1="&#xc0;" u2="T" k="52" />
<hkern u1="&#xc0;" u2="S" k="9" />
<hkern u1="&#xc0;" u2="O" k="15" />
<hkern u1="&#xc0;" u2="y" k="27" />
<hkern u1="&#xc0;" u2="w" k="20" />
<hkern u1="&#xc0;" u2="v" k="25" />
<hkern u1="&#xc0;" u2="u" k="8" />
<hkern u1="&#xc0;" u2="o" k="10" />
<hkern u1="&#xc0;" u2="a" k="5" />
<hkern u1="&#xc0;" u2="&#x20;" k="26" />
<hkern u1="&#xff;" u2="d" k="9" />
<hkern u1="&#xff;" u2="&#x29;" k="23" />
<hkern u1="&#xff;" u2="&#x2039;" k="19" />
<hkern u1="&#xff;" u2="&#xc6;" k="33" />
<hkern u1="&#xff;" u2="&#xf0;" k="15" />
<hkern u1="&#xff;" u2="&#x7d;" k="17" />
<hkern u1="&#xff;" u2="s" k="6" />
<hkern u1="&#xff;" u2="]" k="17" />
<hkern u1="&#xff;" u2="&#x2f;" k="26" />
<hkern u1="&#xff;" u2="X" k="24" />
<hkern u1="&#xff;" u2="Y" k="22" />
<hkern u1="&#xff;" u2="Z" k="14" />
<hkern u1="&#xff;" u2="J" k="61" />
<hkern u1="&#xff;" u2="T" k="49" />
<hkern u1="&#xff;" u2="A" k="26" />
<hkern u1="&#xff;" u2="&#x2e;" k="35" />
<hkern u1="&#xff;" u2="o" k="9" />
<hkern u1="&#xff;" u2="a" k="8" />
<hkern u1="&#xff;" u2="&#x20;" k="22" />
<hkern u1="&#xc2;" u2="d" k="10" />
<hkern u1="&#xc2;" u2="&#x166;" k="44" />
<hkern u1="&#xc2;" g2="hyphen.case" k="9" />
<hkern u1="&#xc2;" g2="braceright.case" k="11" />
<hkern u1="&#xc2;" g2="bracketright.case" k="11" />
<hkern u1="&#xc2;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc2;" u2="&#x29;" k="16" />
<hkern u1="&#xc2;" g2="nine.plf" k="10" />
<hkern u1="&#xc2;" g2="seven.plf" k="15" />
<hkern u1="&#xc2;" g2="six.plf" k="11" />
<hkern u1="&#xc2;" g2="one.plf" k="44" />
<hkern u1="&#xc2;" g2="zero.plf" k="12" />
<hkern u1="&#xc2;" u2="f" k="16" />
<hkern u1="&#xc2;" u2="&#x2039;" k="14" />
<hkern u1="&#xc2;" u2="&#x2a;" k="35" />
<hkern u1="&#xc2;" u2="&#xaa;" k="28" />
<hkern u1="&#xc2;" u2="&#xba;" k="31" />
<hkern u1="&#xc2;" u2="&#x2122;" k="43" />
<hkern u1="&#xc2;" u2="&#x27;" k="38" />
<hkern u1="&#xc2;" u2="&#xf0;" k="9" />
<hkern u1="&#xc2;" u2="&#x7d;" k="33" />
<hkern u1="&#xc2;" u2="&#xae;" k="16" />
<hkern u1="&#xc2;" u2="s" k="6" />
<hkern u1="&#xc2;" u2="&#x2018;" k="44" />
<hkern u1="&#xc2;" u2="&#x2019;" k="45" />
<hkern u1="&#xc2;" u2="&#xa9;" k="16" />
<hkern u1="&#xc2;" u2="&#x3f;" k="35" />
<hkern u1="&#xc2;" u2="\" k="48" />
<hkern u1="&#xc2;" u2="]" k="33" />
<hkern u1="&#xc2;" u2="U" k="13" />
<hkern u1="&#xc2;" u2="Y" k="57" />
<hkern u1="&#xc2;" u2="W" k="32" />
<hkern u1="&#xc2;" u2="V" k="41" />
<hkern u1="&#xc2;" u2="T" k="52" />
<hkern u1="&#xc2;" u2="S" k="9" />
<hkern u1="&#xc2;" u2="O" k="15" />
<hkern u1="&#xc2;" u2="y" k="27" />
<hkern u1="&#xc2;" u2="w" k="20" />
<hkern u1="&#xc2;" u2="v" k="25" />
<hkern u1="&#xc2;" u2="u" k="8" />
<hkern u1="&#xc2;" u2="o" k="10" />
<hkern u1="&#xc2;" u2="a" k="5" />
<hkern u1="&#xc2;" u2="&#x20;" k="26" />
<hkern u1="&#xc1;" u2="d" k="10" />
<hkern u1="&#xc1;" u2="&#x166;" k="44" />
<hkern u1="&#xc1;" g2="hyphen.case" k="9" />
<hkern u1="&#xc1;" g2="braceright.case" k="11" />
<hkern u1="&#xc1;" g2="bracketright.case" k="11" />
<hkern u1="&#xc1;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc1;" u2="&#x29;" k="16" />
<hkern u1="&#xc1;" g2="nine.plf" k="10" />
<hkern u1="&#xc1;" g2="seven.plf" k="15" />
<hkern u1="&#xc1;" g2="six.plf" k="11" />
<hkern u1="&#xc1;" g2="one.plf" k="44" />
<hkern u1="&#xc1;" g2="zero.plf" k="12" />
<hkern u1="&#xc1;" u2="f" k="16" />
<hkern u1="&#xc1;" u2="&#x2039;" k="14" />
<hkern u1="&#xc1;" u2="&#x2a;" k="35" />
<hkern u1="&#xc1;" u2="&#xaa;" k="28" />
<hkern u1="&#xc1;" u2="&#xba;" k="31" />
<hkern u1="&#xc1;" u2="&#x2122;" k="43" />
<hkern u1="&#xc1;" u2="&#x27;" k="38" />
<hkern u1="&#xc1;" u2="&#xf0;" k="9" />
<hkern u1="&#xc1;" u2="&#x7d;" k="33" />
<hkern u1="&#xc1;" u2="&#xae;" k="16" />
<hkern u1="&#xc1;" u2="s" k="6" />
<hkern u1="&#xc1;" u2="&#x2018;" k="44" />
<hkern u1="&#xc1;" u2="&#x2019;" k="45" />
<hkern u1="&#xc1;" u2="&#xa9;" k="16" />
<hkern u1="&#xc1;" u2="&#x3f;" k="35" />
<hkern u1="&#xc1;" u2="\" k="48" />
<hkern u1="&#xc1;" u2="]" k="33" />
<hkern u1="&#xc1;" u2="U" k="13" />
<hkern u1="&#xc1;" u2="Y" k="57" />
<hkern u1="&#xc1;" u2="W" k="32" />
<hkern u1="&#xc1;" u2="V" k="41" />
<hkern u1="&#xc1;" u2="T" k="52" />
<hkern u1="&#xc1;" u2="S" k="9" />
<hkern u1="&#xc1;" u2="O" k="15" />
<hkern u1="&#xc1;" u2="y" k="27" />
<hkern u1="&#xc1;" u2="w" k="20" />
<hkern u1="&#xc1;" u2="v" k="25" />
<hkern u1="&#xc1;" u2="u" k="8" />
<hkern u1="&#xc1;" u2="o" k="10" />
<hkern u1="&#xc1;" u2="a" k="5" />
<hkern u1="&#xc1;" u2="&#x20;" k="26" />
<hkern u1="&#xcd;" u2="d" k="6" />
<hkern u1="&#xcd;" u2="&#xf0;" k="7" />
<hkern u1="&#xcd;" u2="o" k="6" />
<hkern u1="&#xce;" u2="d" k="6" />
<hkern u1="&#xce;" u2="&#xf0;" k="7" />
<hkern u1="&#xce;" u2="o" k="6" />
<hkern u1="&#xcf;" u2="d" k="6" />
<hkern u1="&#xcf;" u2="&#xf0;" k="7" />
<hkern u1="&#xcf;" u2="o" k="6" />
<hkern u1="&#xcc;" u2="d" k="6" />
<hkern u1="&#xcc;" u2="&#xf0;" k="7" />
<hkern u1="&#xcc;" u2="o" k="6" />
<hkern u1="&#xda;" u2="d" k="6" />
<hkern u1="&#xda;" g2="parenright.case" k="13" />
<hkern u1="&#xda;" g2="braceright.case" k="14" />
<hkern u1="&#xda;" g2="bracketright.case" k="14" />
<hkern u1="&#xda;" u2="&#xc6;" k="9" />
<hkern u1="&#xda;" u2="&#xf0;" k="8" />
<hkern u1="&#xda;" u2="s" k="6" />
<hkern u1="&#xda;" u2="J" k="16" />
<hkern u1="&#xda;" u2="A" k="13" />
<hkern u1="&#xda;" u2="&#x2e;" k="10" />
<hkern u1="&#xda;" u2="z" k="7" />
<hkern u1="&#xda;" u2="u" k="7" />
<hkern u1="&#xda;" u2="o" k="6" />
<hkern u1="&#xda;" u2="a" k="6" />
<hkern u1="&#xdb;" u2="d" k="6" />
<hkern u1="&#xdb;" g2="parenright.case" k="13" />
<hkern u1="&#xdb;" g2="braceright.case" k="14" />
<hkern u1="&#xdb;" g2="bracketright.case" k="14" />
<hkern u1="&#xdb;" u2="&#xc6;" k="9" />
<hkern u1="&#xdb;" u2="&#xf0;" k="8" />
<hkern u1="&#xdb;" u2="s" k="6" />
<hkern u1="&#xdb;" u2="J" k="16" />
<hkern u1="&#xdb;" u2="A" k="13" />
<hkern u1="&#xdb;" u2="&#x2e;" k="10" />
<hkern u1="&#xdb;" u2="z" k="7" />
<hkern u1="&#xdb;" u2="u" k="7" />
<hkern u1="&#xdb;" u2="o" k="6" />
<hkern u1="&#xdb;" u2="a" k="6" />
<hkern u1="&#xd9;" u2="d" k="6" />
<hkern u1="&#xd9;" g2="parenright.case" k="13" />
<hkern u1="&#xd9;" g2="braceright.case" k="14" />
<hkern u1="&#xd9;" g2="bracketright.case" k="14" />
<hkern u1="&#xd9;" u2="&#xc6;" k="9" />
<hkern u1="&#xd9;" u2="&#xf0;" k="8" />
<hkern u1="&#xd9;" u2="s" k="6" />
<hkern u1="&#xd9;" u2="J" k="16" />
<hkern u1="&#xd9;" u2="A" k="13" />
<hkern u1="&#xd9;" u2="&#x2e;" k="10" />
<hkern u1="&#xd9;" u2="z" k="7" />
<hkern u1="&#xd9;" u2="u" k="7" />
<hkern u1="&#xd9;" u2="o" k="6" />
<hkern u1="&#xd9;" u2="a" k="6" />
<hkern u1="&#xc7;" g2="parenright.case" k="19" />
<hkern u1="&#xc7;" g2="braceright.case" k="23" />
<hkern u1="&#xc7;" g2="bracketright.case" k="23" />
<hkern u1="&#xc7;" u2="&#x29;" k="17" />
<hkern u1="&#xc7;" u2="&#x7d;" k="18" />
<hkern u1="&#xc7;" u2="]" k="18" />
<hkern u1="&#xc7;" u2="X" k="5" />
<hkern u1="&#xc7;" u2="Y" k="13" />
<hkern u1="&#xc7;" u2="W" k="6" />
<hkern u1="&#xc7;" u2="V" k="10" />
<hkern u1="&#xc7;" u2="A" k="6" />
<hkern u1="&#x201a;" g2="seven.plf" k="11" />
<hkern u1="&#x201a;" g2="one.plf" k="44" />
<hkern u1="&#x201a;" u2="f" k="8" />
<hkern u1="&#x201a;" u2="&#x27;" k="105" />
<hkern u1="&#x201a;" u2="&#x2018;" k="116" />
<hkern u1="&#x201a;" u2="&#x2019;" k="118" />
<hkern u1="&#x201a;" u2="U" k="10" />
<hkern u1="&#x201a;" u2="Y" k="61" />
<hkern u1="&#x201a;" u2="W" k="41" />
<hkern u1="&#x201a;" u2="V" k="55" />
<hkern u1="&#x201a;" u2="T" k="47" />
<hkern u1="&#x201a;" u2="O" k="10" />
<hkern u1="&#x201a;" u2="y" k="38" />
<hkern u1="&#x201a;" u2="w" k="26" />
<hkern u1="&#x201a;" u2="v" k="35" />
<hkern u1="&#x201e;" g2="seven.plf" k="11" />
<hkern u1="&#x201e;" g2="one.plf" k="44" />
<hkern u1="&#x201e;" u2="f" k="8" />
<hkern u1="&#x201e;" u2="&#x27;" k="105" />
<hkern u1="&#x201e;" u2="&#x2018;" k="116" />
<hkern u1="&#x201e;" u2="&#x2019;" k="118" />
<hkern u1="&#x201e;" u2="U" k="10" />
<hkern u1="&#x201e;" u2="Y" k="61" />
<hkern u1="&#x201e;" u2="W" k="41" />
<hkern u1="&#x201e;" u2="V" k="55" />
<hkern u1="&#x201e;" u2="T" k="47" />
<hkern u1="&#x201e;" u2="O" k="10" />
<hkern u1="&#x201e;" u2="y" k="38" />
<hkern u1="&#x201e;" u2="w" k="26" />
<hkern u1="&#x201e;" u2="v" k="35" />
<hkern u1="&#x201d;" u2="d" k="32" />
<hkern u1="&#x201d;" g2="guilsinglleft.case" k="14" />
<hkern u1="&#x201d;" u2="&#x2039;" k="35" />
<hkern u1="&#x201d;" u2="&#xc6;" k="54" />
<hkern u1="&#x201d;" u2="&#xf0;" k="24" />
<hkern u1="&#x201d;" u2="s" k="19" />
<hkern u1="&#x201d;" u2="&#x2f;" k="58" />
<hkern u1="&#x201d;" u2="J" k="49" />
<hkern u1="&#x201d;" u2="A" k="49" />
<hkern u1="&#x201d;" u2="&#x2e;" k="118" />
<hkern u1="&#x201d;" u2="o" k="28" />
<hkern u1="&#x201d;" u2="a" k="21" />
<hkern u1="&#x201d;" u2="&#x20;" k="23" />
<hkern u1="&#x201c;" u2="d" k="24" />
<hkern u1="&#x201c;" u2="&#xc6;" k="49" />
<hkern u1="&#x201c;" u2="&#xf0;" k="25" />
<hkern u1="&#x201c;" u2="s" k="12" />
<hkern u1="&#x201c;" u2="J" k="49" />
<hkern u1="&#x201c;" u2="A" k="44" />
<hkern u1="&#x201c;" u2="&#x2e;" k="117" />
<hkern u1="&#x201c;" u2="o" k="20" />
<hkern u1="&#x201c;" u2="a" k="15" />
<hkern u1="&#x2019;" u2="d" k="32" />
<hkern u1="&#x2019;" g2="guilsinglleft.case" k="14" />
<hkern u1="&#x2019;" u2="&#x2039;" k="35" />
<hkern u1="&#x2019;" u2="&#xc6;" k="54" />
<hkern u1="&#x2019;" u2="&#xf0;" k="24" />
<hkern u1="&#x2019;" u2="s" k="19" />
<hkern u1="&#x2019;" u2="&#x2f;" k="58" />
<hkern u1="&#x2019;" u2="J" k="49" />
<hkern u1="&#x2019;" u2="A" k="49" />
<hkern u1="&#x2019;" u2="&#x2e;" k="118" />
<hkern u1="&#x2019;" u2="o" k="28" />
<hkern u1="&#x2019;" u2="a" k="21" />
<hkern u1="&#x2019;" u2="&#x20;" k="23" />
<hkern u1="&#x2018;" u2="d" k="24" />
<hkern u1="&#x2018;" u2="&#xc6;" k="49" />
<hkern u1="&#x2018;" u2="&#xf0;" k="25" />
<hkern u1="&#x2018;" u2="s" k="12" />
<hkern u1="&#x2018;" u2="J" k="49" />
<hkern u1="&#x2018;" u2="A" k="44" />
<hkern u1="&#x2018;" u2="&#x2e;" k="117" />
<hkern u1="&#x2018;" u2="o" k="20" />
<hkern u1="&#x2018;" u2="a" k="15" />
<hkern u1="&#xab;" u2="&#x166;" k="24" />
<hkern u1="&#xab;" u2="Y" k="38" />
<hkern u1="&#xab;" u2="W" k="13" />
<hkern u1="&#xab;" u2="V" k="22" />
<hkern u1="&#xab;" u2="T" k="55" />
<hkern u1="&#x40;" u2="&#x104;" k="11" />
<hkern u1="&#x40;" u2="&#x1fa;" k="11" />
<hkern u1="&#x40;" u2="&#x1ef2;" k="18" />
<hkern u1="&#x40;" u2="&#x1e84;" k="11" />
<hkern u1="&#x40;" u2="&#x1e82;" k="11" />
<hkern u1="&#x40;" u2="&#x1e80;" k="11" />
<hkern u1="&#x40;" u2="&#x176;" k="18" />
<hkern u1="&#x40;" u2="&#x174;" k="11" />
<hkern u1="&#x40;" u2="&#x102;" k="11" />
<hkern u1="&#x40;" u2="&#x100;" k="11" />
<hkern u1="&#x40;" u2="&#xc3;" k="11" />
<hkern u1="&#x40;" u2="&#xc5;" k="11" />
<hkern u1="&#x40;" u2="&#xc1;" k="11" />
<hkern u1="&#x40;" u2="&#xc2;" k="11" />
<hkern u1="&#x40;" u2="&#x178;" k="18" />
<hkern u1="&#x40;" u2="&#xc0;" k="11" />
<hkern u1="&#x40;" u2="&#xc4;" k="11" />
<hkern u1="&#x40;" u2="&#xdd;" k="18" />
<hkern u1="&#x40;" u2="Y" k="18" />
<hkern u1="&#x40;" u2="W" k="11" />
<hkern u1="&#x40;" u2="A" k="11" />
<hkern u1="&#x40;" u2="V" k="16" />
<hkern u1="s" u2="&#x29;" k="28" />
<hkern u1="s" u2="&#x2122;" k="22" />
<hkern u1="s" u2="&#x7d;" k="30" />
<hkern u1="s" u2="&#x2018;" k="13" />
<hkern u1="s" u2="&#x2019;" k="15" />
<hkern u1="s" u2="\" k="27" />
<hkern u1="s" u2="]" k="30" />
<hkern u1="s" u2="U" k="6" />
<hkern u1="s" u2="Y" k="77" />
<hkern u1="s" u2="W" k="37" />
<hkern u1="s" u2="V" k="42" />
<hkern u1="s" u2="T" k="72" />
<hkern u1="s" u2="A" k="5" />
<hkern u1="s" u2="x" k="6" />
<hkern u1="s" u2="y" k="9" />
<hkern u1="s" u2="w" k="5" />
<hkern u1="s" u2="v" k="8" />
<hkern u1="g" u2="&#x29;" k="24" />
<hkern u1="g" u2="&#x2122;" k="16" />
<hkern u1="g" u2="&#x7d;" k="25" />
<hkern u1="g" u2="\" k="15" />
<hkern u1="g" u2="]" k="25" />
<hkern u1="g" u2="Y" k="53" />
<hkern u1="g" u2="Z" k="5" />
<hkern u1="g" u2="W" k="26" />
<hkern u1="g" u2="V" k="35" />
<hkern u1="g" u2="T" k="59" />
<hkern u1="&#x7b;" u2="d" k="34" />
<hkern u1="&#x7b;" u2="&#x105;" k="30" />
<hkern u1="&#x7b;" u2="&#x104;" k="33" />
<hkern u1="&#x7b;" u2="&#x173;" k="24" />
<hkern u1="&#x7b;" u2="&#x119;" k="34" />
<hkern u1="&#x7b;" u2="&#x1fa;" k="33" />
<hkern u1="&#x7b;" u2="&#x1fb;" k="30" />
<hkern u1="&#x7b;" u2="&#x219;" k="29" />
<hkern u1="&#x7b;" u2="&#x218;" k="17" />
<hkern u1="&#x7b;" u2="&#x15f;" k="29" />
<hkern u1="&#x7b;" u2="&#x15e;" k="17" />
<hkern u1="&#x7b;" u2="&#x157;" k="25" />
<hkern u1="&#x7b;" u2="&#x146;" k="25" />
<hkern u1="&#x7b;" u2="&#x122;" k="24" />
<hkern u1="&#x7b;" u2="&#x149;" k="25" />
<hkern u1="&#x7b;" u2="&#x111;" k="34" />
<hkern u1="&#x7b;" u2="&#x1fd;" k="30" />
<hkern u1="&#x7b;" u2="&#x1ff;" k="34" />
<hkern u1="&#x7b;" u2="&#x1fe;" k="24" />
<hkern u1="&#x7b;" u2="&#x14b;" k="25" />
<hkern u1="&#x7b;" u2="&#x1fc;" k="19" />
<hkern u1="&#x7b;" u2="&#x1e85;" k="17" />
<hkern u1="&#x7b;" u2="&#x1e83;" k="17" />
<hkern u1="&#x7b;" u2="&#x1e81;" k="17" />
<hkern u1="&#x7b;" u2="&#x17c;" k="21" />
<hkern u1="&#x7b;" u2="&#x17a;" k="21" />
<hkern u1="&#x7b;" u2="&#x175;" k="17" />
<hkern u1="&#x7b;" u2="&#x171;" k="24" />
<hkern u1="&#x7b;" u2="&#x16f;" k="24" />
<hkern u1="&#x7b;" u2="&#x169;" k="24" />
<hkern u1="&#x7b;" u2="&#x15d;" k="29" />
<hkern u1="&#x7b;" u2="&#x15c;" k="17" />
<hkern u1="&#x7b;" u2="&#x15b;" k="29" />
<hkern u1="&#x7b;" u2="&#x15a;" k="17" />
<hkern u1="&#x7b;" u2="&#x159;" k="25" />
<hkern u1="&#x7b;" u2="&#x155;" k="25" />
<hkern u1="&#x7b;" u2="&#x151;" k="34" />
<hkern u1="&#x7b;" u2="&#x150;" k="24" />
<hkern u1="&#x7b;" u2="&#x148;" k="25" />
<hkern u1="&#x7b;" u2="&#x144;" k="25" />
<hkern u1="&#x7b;" u2="&#x134;" k="20" />
<hkern u1="&#x7b;" u2="&#x123;" k="34" />
<hkern u1="&#x7b;" u2="&#x121;" k="34" />
<hkern u1="&#x7b;" u2="&#x11d;" k="34" />
<hkern u1="&#x7b;" u2="&#x11c;" k="24" />
<hkern u1="&#x7b;" u2="&#x11b;" k="34" />
<hkern u1="&#x7b;" u2="&#x117;" k="34" />
<hkern u1="&#x7b;" u2="&#x16d;" k="24" />
<hkern u1="&#x7b;" u2="&#x14f;" k="34" />
<hkern u1="&#x7b;" u2="&#x14e;" k="24" />
<hkern u1="&#x7b;" u2="&#x11f;" k="34" />
<hkern u1="&#x7b;" u2="&#x11e;" k="24" />
<hkern u1="&#x7b;" u2="&#x115;" k="34" />
<hkern u1="&#x7b;" u2="&#x16b;" k="24" />
<hkern u1="&#x7b;" u2="&#x14d;" k="34" />
<hkern u1="&#x7b;" u2="&#x14c;" k="24" />
<hkern u1="&#x7b;" u2="&#x113;" k="34" />
<hkern u1="&#x7b;" u2="&#x10f;" k="34" />
<hkern u1="&#x7b;" u2="&#x10d;" k="34" />
<hkern u1="&#x7b;" u2="&#x10c;" k="24" />
<hkern u1="&#x7b;" u2="&#x10b;" k="34" />
<hkern u1="&#x7b;" u2="&#x10a;" k="24" />
<hkern u1="&#x7b;" u2="&#x109;" k="34" />
<hkern u1="&#x7b;" u2="&#x108;" k="24" />
<hkern u1="&#x7b;" u2="&#x107;" k="34" />
<hkern u1="&#x7b;" u2="&#x106;" k="24" />
<hkern u1="&#x7b;" u2="&#x103;" k="30" />
<hkern u1="&#x7b;" u2="&#x102;" k="33" />
<hkern u1="&#x7b;" u2="&#x101;" k="30" />
<hkern u1="&#x7b;" u2="&#x100;" k="33" />
<hkern u1="&#x7b;" u2="&#x120;" k="24" />
<hkern u1="&#x7b;" g2="zero.plfslash" k="22" />
<hkern u1="&#x7b;" g2="zero.plf" k="22" />
<hkern u1="&#x7b;" u2="&#xd5;" k="24" />
<hkern u1="&#x7b;" u2="&#xc3;" k="33" />
<hkern u1="&#x7b;" u2="&#xf5;" k="34" />
<hkern u1="&#x7b;" u2="&#xf1;" k="25" />
<hkern u1="&#x7b;" u2="&#xe3;" k="30" />
<hkern u1="&#x7b;" u2="n" k="25" />
<hkern u1="&#x7b;" u2="&#xd8;" k="24" />
<hkern u1="&#x7b;" u2="&#xc6;" k="19" />
<hkern u1="&#x7b;" u2="&#x152;" k="24" />
<hkern u1="&#x7b;" u2="&#x153;" k="34" />
<hkern u1="&#x7b;" u2="&#xe6;" k="30" />
<hkern u1="&#x7b;" u2="&#xf8;" k="34" />
<hkern u1="&#x7b;" u2="&#xc5;" k="33" />
<hkern u1="&#x7b;" u2="&#xe5;" k="30" />
<hkern u1="&#x7b;" u2="&#x17e;" k="21" />
<hkern u1="&#x7b;" u2="&#x161;" k="29" />
<hkern u1="&#x7b;" u2="&#x160;" k="17" />
<hkern u1="&#x7b;" u2="&#xd6;" k="24" />
<hkern u1="&#x7b;" u2="&#xd2;" k="24" />
<hkern u1="&#x7b;" u2="&#xd4;" k="24" />
<hkern u1="&#x7b;" u2="&#xd3;" k="24" />
<hkern u1="&#x7b;" u2="g" k="34" />
<hkern u1="&#x7b;" u2="s" k="29" />
<hkern u1="&#x7b;" u2="&#xc7;" k="24" />
<hkern u1="&#x7b;" u2="&#xc1;" k="33" />
<hkern u1="&#x7b;" u2="&#xc2;" k="33" />
<hkern u1="&#x7b;" u2="&#xc0;" k="33" />
<hkern u1="&#x7b;" u2="&#xfc;" k="24" />
<hkern u1="&#x7b;" u2="&#xfb;" k="24" />
<hkern u1="&#x7b;" u2="&#xf9;" k="24" />
<hkern u1="&#x7b;" u2="&#xfa;" k="24" />
<hkern u1="&#x7b;" u2="&#xf6;" k="34" />
<hkern u1="&#x7b;" u2="&#xf4;" k="34" />
<hkern u1="&#x7b;" u2="&#xf2;" k="34" />
<hkern u1="&#x7b;" u2="&#xf3;" k="34" />
<hkern u1="&#x7b;" u2="&#xeb;" k="34" />
<hkern u1="&#x7b;" u2="&#xea;" k="34" />
<hkern u1="&#x7b;" u2="&#xe8;" k="34" />
<hkern u1="&#x7b;" u2="&#xe9;" k="34" />
<hkern u1="&#x7b;" u2="&#xe4;" k="30" />
<hkern u1="&#x7b;" u2="&#xe2;" k="30" />
<hkern u1="&#x7b;" u2="&#xe0;" k="30" />
<hkern u1="&#x7b;" u2="&#xe1;" k="30" />
<hkern u1="&#x7b;" u2="&#xc4;" k="33" />
<hkern u1="&#x7b;" u2="&#xe7;" k="34" />
<hkern u1="&#x7b;" u2="G" k="24" />
<hkern u1="&#x7b;" u2="C" k="24" />
<hkern u1="&#x7b;" u2="J" k="20" />
<hkern u1="&#x7b;" u2="Q" k="24" />
<hkern u1="&#x7b;" u2="S" k="17" />
<hkern u1="&#x7b;" u2="O" k="24" />
<hkern u1="&#x7b;" u2="A" k="33" />
<hkern u1="&#x7b;" u2="z" k="21" />
<hkern u1="&#x7b;" u2="w" k="17" />
<hkern u1="&#x7b;" u2="r" k="25" />
<hkern u1="&#x7b;" u2="m" k="25" />
<hkern u1="&#x7b;" u2="c" k="34" />
<hkern u1="&#x7b;" u2="e" k="34" />
<hkern u1="&#x7b;" u2="u" k="24" />
<hkern u1="&#x7b;" u2="o" k="34" />
<hkern u1="&#x7b;" u2="a" k="30" />
<hkern u1="&#x7b;" u2="q" k="34" />
<hkern u1="&#x7b;" u2="p" k="25" />
<hkern u1="&#x7b;" u2="&#x12b;" k="-8" />
<hkern u1="&#x7b;" g2="nine.plf" k="18" />
<hkern u1="&#x7b;" g2="eight.plf" k="20" />
<hkern u1="&#x7b;" g2="six.plf" k="23" />
<hkern u1="&#x7b;" g2="four.plf" k="34" />
<hkern u1="&#x7b;" g2="three.plf" k="18" />
<hkern u1="&#x7b;" g2="two.plf" k="15" />
<hkern u1="&#x7b;" g2="one.plf" k="11" />
<hkern u1="&#x7b;" u2="&#xf0;" k="31" />
<hkern u1="&#x7b;" u2="&#x28;" k="19" />
<hkern u1="&#x7b;" u2="x" k="18" />
<hkern u1="&#x7b;" u2="v" k="18" />
<hkern u1="&#xa1;" u2="&#x21a;" k="42" />
<hkern u1="&#xa1;" u2="&#x162;" k="42" />
<hkern u1="&#xa1;" u2="&#x1ef2;" k="25" />
<hkern u1="&#xa1;" u2="&#x176;" k="25" />
<hkern u1="&#xa1;" u2="&#x164;" k="42" />
<hkern u1="&#xa1;" u2="&#x178;" k="25" />
<hkern u1="&#xa1;" u2="&#xdd;" k="25" />
<hkern u1="&#xa1;" u2="Y" k="25" />
<hkern u1="&#xa1;" u2="T" k="42" />
<hkern u1="&#xa1;" u2="&#x166;" k="17" />
<hkern u1="&#xa1;" u2="V" k="16" />
<hkern u1="&#x131;" u2="Z" k="6" />
<hkern u1="&#xbf;" u2="d" k="19" />
<hkern u1="&#xbf;" u2="&#x173;" k="13" />
<hkern u1="&#xbf;" u2="&#x172;" k="15" />
<hkern u1="&#xbf;" u2="&#x119;" k="19" />
<hkern u1="&#xbf;" u2="&#x167;" k="12" />
<hkern u1="&#xbf;" u2="&#x21a;" k="56" />
<hkern u1="&#xbf;" u2="&#x21b;" k="12" />
<hkern u1="&#xbf;" u2="&#x162;" k="56" />
<hkern u1="&#xbf;" u2="&#x163;" k="12" />
<hkern u1="&#xbf;" u2="&#x122;" k="12" />
<hkern u1="&#xbf;" u2="&#x111;" k="19" />
<hkern u1="&#xbf;" u2="&#x1ff;" k="19" />
<hkern u1="&#xbf;" u2="&#x1fe;" k="12" />
<hkern u1="&#xbf;" u2="&#x165;" k="12" />
<hkern u1="&#xbf;" u2="&#x1ef2;" k="63" />
<hkern u1="&#xbf;" u2="&#x1e84;" k="43" />
<hkern u1="&#xbf;" u2="&#x1e82;" k="43" />
<hkern u1="&#xbf;" u2="&#x1e80;" k="43" />
<hkern u1="&#xbf;" u2="&#x1ef3;" k="11" />
<hkern u1="&#xbf;" u2="&#x1e85;" k="34" />
<hkern u1="&#xbf;" u2="&#x1e83;" k="34" />
<hkern u1="&#xbf;" u2="&#x1e81;" k="34" />
<hkern u1="&#xbf;" u2="&#x177;" k="11" />
<hkern u1="&#xbf;" u2="&#x176;" k="63" />
<hkern u1="&#xbf;" u2="&#x175;" k="34" />
<hkern u1="&#xbf;" u2="&#x174;" k="43" />
<hkern u1="&#xbf;" u2="&#x171;" k="13" />
<hkern u1="&#xbf;" u2="&#x170;" k="15" />
<hkern u1="&#xbf;" u2="&#x16f;" k="13" />
<hkern u1="&#xbf;" u2="&#x16e;" k="15" />
<hkern u1="&#xbf;" u2="&#x169;" k="13" />
<hkern u1="&#xbf;" u2="&#x168;" k="15" />
<hkern u1="&#xbf;" u2="&#x164;" k="56" />
<hkern u1="&#xbf;" u2="&#x151;" k="19" />
<hkern u1="&#xbf;" u2="&#x150;" k="12" />
<hkern u1="&#xbf;" u2="&#x123;" k="19" />
<hkern u1="&#xbf;" u2="&#x121;" k="19" />
<hkern u1="&#xbf;" u2="&#x11d;" k="19" />
<hkern u1="&#xbf;" u2="&#x11c;" k="12" />
<hkern u1="&#xbf;" u2="&#x11b;" k="19" />
<hkern u1="&#xbf;" u2="&#x117;" k="19" />
<hkern u1="&#xbf;" u2="&#x16d;" k="13" />
<hkern u1="&#xbf;" u2="&#x16c;" k="15" />
<hkern u1="&#xbf;" u2="&#x14f;" k="19" />
<hkern u1="&#xbf;" u2="&#x14e;" k="12" />
<hkern u1="&#xbf;" u2="&#x11f;" k="19" />
<hkern u1="&#xbf;" u2="&#x11e;" k="12" />
<hkern u1="&#xbf;" u2="&#x115;" k="19" />
<hkern u1="&#xbf;" u2="&#x16b;" k="13" />
<hkern u1="&#xbf;" u2="&#x16a;" k="15" />
<hkern u1="&#xbf;" u2="&#x14d;" k="19" />
<hkern u1="&#xbf;" u2="&#x14c;" k="12" />
<hkern u1="&#xbf;" u2="&#x113;" k="19" />
<hkern u1="&#xbf;" u2="&#x10f;" k="19" />
<hkern u1="&#xbf;" u2="&#x10d;" k="19" />
<hkern u1="&#xbf;" u2="&#x10c;" k="12" />
<hkern u1="&#xbf;" u2="&#x10b;" k="19" />
<hkern u1="&#xbf;" u2="&#x10a;" k="12" />
<hkern u1="&#xbf;" u2="&#x109;" k="19" />
<hkern u1="&#xbf;" u2="&#x108;" k="12" />
<hkern u1="&#xbf;" u2="&#x107;" k="19" />
<hkern u1="&#xbf;" u2="&#x106;" k="12" />
<hkern u1="&#xbf;" u2="&#x120;" k="12" />
<hkern u1="&#xbf;" u2="&#xd5;" k="12" />
<hkern u1="&#xbf;" u2="&#xf5;" k="19" />
<hkern u1="&#xbf;" u2="f" k="12" />
<hkern u1="&#xbf;" u2="&#xd8;" k="12" />
<hkern u1="&#xbf;" u2="&#x152;" k="12" />
<hkern u1="&#xbf;" u2="&#x153;" k="19" />
<hkern u1="&#xbf;" u2="&#xf8;" k="19" />
<hkern u1="&#xbf;" u2="&#xd6;" k="12" />
<hkern u1="&#xbf;" u2="&#xd2;" k="12" />
<hkern u1="&#xbf;" u2="&#xd4;" k="12" />
<hkern u1="&#xbf;" u2="&#xd3;" k="12" />
<hkern u1="&#xbf;" u2="g" k="19" />
<hkern u1="&#xbf;" u2="&#xc7;" k="12" />
<hkern u1="&#xbf;" u2="&#xd9;" k="15" />
<hkern u1="&#xbf;" u2="&#xdb;" k="15" />
<hkern u1="&#xbf;" u2="&#xda;" k="15" />
<hkern u1="&#xbf;" u2="&#x178;" k="63" />
<hkern u1="&#xbf;" u2="&#xff;" k="11" />
<hkern u1="&#xbf;" u2="&#xfc;" k="13" />
<hkern u1="&#xbf;" u2="&#xfb;" k="13" />
<hkern u1="&#xbf;" u2="&#xf9;" k="13" />
<hkern u1="&#xbf;" u2="&#xfa;" k="13" />
<hkern u1="&#xbf;" u2="&#xf6;" k="19" />
<hkern u1="&#xbf;" u2="&#xf4;" k="19" />
<hkern u1="&#xbf;" u2="&#xf2;" k="19" />
<hkern u1="&#xbf;" u2="&#xf3;" k="19" />
<hkern u1="&#xbf;" u2="&#xeb;" k="19" />
<hkern u1="&#xbf;" u2="&#xea;" k="19" />
<hkern u1="&#xbf;" u2="&#xe8;" k="19" />
<hkern u1="&#xbf;" u2="&#xe9;" k="19" />
<hkern u1="&#xbf;" u2="&#xdc;" k="15" />
<hkern u1="&#xbf;" u2="&#xfd;" k="11" />
<hkern u1="&#xbf;" u2="&#xdd;" k="63" />
<hkern u1="&#xbf;" u2="&#xe7;" k="19" />
<hkern u1="&#xbf;" g2="fl" k="12" />
<hkern u1="&#xbf;" g2="fi" k="12" />
<hkern u1="&#xbf;" u2="G" k="12" />
<hkern u1="&#xbf;" u2="U" k="15" />
<hkern u1="&#xbf;" u2="C" k="12" />
<hkern u1="&#xbf;" u2="Y" k="63" />
<hkern u1="&#xbf;" u2="W" k="43" />
<hkern u1="&#xbf;" u2="Q" k="12" />
<hkern u1="&#xbf;" u2="T" k="56" />
<hkern u1="&#xbf;" u2="O" k="12" />
<hkern u1="&#xbf;" u2="y" k="11" />
<hkern u1="&#xbf;" u2="w" k="34" />
<hkern u1="&#xbf;" u2="c" k="19" />
<hkern u1="&#xbf;" u2="e" k="19" />
<hkern u1="&#xbf;" u2="u" k="13" />
<hkern u1="&#xbf;" u2="o" k="19" />
<hkern u1="&#xbf;" u2="t" k="12" />
<hkern u1="&#xbf;" u2="q" k="19" />
<hkern u1="&#xbf;" u2="&#x166;" k="46" />
<hkern u1="&#xbf;" u2="&#xf0;" k="18" />
<hkern u1="&#xbf;" u2="V" k="55" />
<hkern u1="&#xbf;" u2="v" k="40" />
<hkern u1="&#x160;" g2="parenright.case" k="19" />
<hkern u1="&#x160;" g2="braceright.case" k="23" />
<hkern u1="&#x160;" g2="bracketright.case" k="23" />
<hkern u1="&#x160;" u2="&#x29;" k="18" />
<hkern u1="&#x160;" u2="&#x7d;" k="18" />
<hkern u1="&#x160;" u2="]" k="18" />
<hkern u1="&#x160;" u2="X" k="6" />
<hkern u1="&#x160;" u2="Y" k="18" />
<hkern u1="&#x160;" u2="W" k="10" />
<hkern u1="&#x160;" u2="V" k="15" />
<hkern u1="&#x160;" u2="T" k="5" />
<hkern u1="&#x160;" u2="A" k="9" />
<hkern u1="&#x160;" u2="x" k="8" />
<hkern u1="&#x161;" u2="&#x29;" k="28" />
<hkern u1="&#x161;" u2="&#x2122;" k="22" />
<hkern u1="&#x161;" u2="&#x7d;" k="30" />
<hkern u1="&#x161;" u2="&#x2018;" k="13" />
<hkern u1="&#x161;" u2="&#x2019;" k="15" />
<hkern u1="&#x161;" u2="\" k="27" />
<hkern u1="&#x161;" u2="]" k="30" />
<hkern u1="&#x161;" u2="U" k="6" />
<hkern u1="&#x161;" u2="Y" k="77" />
<hkern u1="&#x161;" u2="W" k="37" />
<hkern u1="&#x161;" u2="V" k="42" />
<hkern u1="&#x161;" u2="T" k="72" />
<hkern u1="&#x161;" u2="A" k="5" />
<hkern u1="&#x161;" u2="x" k="6" />
<hkern u1="&#x161;" u2="y" k="9" />
<hkern u1="&#x161;" u2="w" k="5" />
<hkern u1="&#x161;" u2="v" k="8" />
<hkern u1="&#x17e;" u2="d" k="5" />
<hkern u1="&#x17e;" u2="&#x29;" k="16" />
<hkern u1="&#x17e;" u2="&#x2039;" k="21" />
<hkern u1="&#x17e;" u2="&#x2122;" k="16" />
<hkern u1="&#x17e;" u2="&#xf0;" k="7" />
<hkern u1="&#x17e;" u2="&#x7d;" k="21" />
<hkern u1="&#x17e;" u2="]" k="21" />
<hkern u1="&#x17e;" u2="&#x2d;" k="14" />
<hkern u1="&#x17e;" u2="U" k="6" />
<hkern u1="&#x17e;" u2="Y" k="43" />
<hkern u1="&#x17e;" u2="W" k="10" />
<hkern u1="&#x17e;" u2="V" k="20" />
<hkern u1="&#x17e;" u2="T" k="76" />
<hkern u1="&#x17e;" u2="o" k="6" />
<hkern u1="&#xb7;" u2="l" k="61" />
<hkern u1="&#xb7;" u2="&#x13c;" k="61" />
<hkern u1="&#xb7;" u2="&#x140;" k="61" />
<hkern u1="&#xb7;" u2="&#x13e;" k="61" />
<hkern u1="&#xb7;" u2="&#x13a;" k="61" />
<hkern u1="&#xb7;" u2="&#x142;" k="61" />
<hkern u1="&#xb7;" g2="seven.plf" k="28" />
<hkern u1="&#xb7;" g2="one.plf" k="18" />
<hkern u1="&#x2044;" g2="four.den" k="39" />
<hkern u1="&#xc5;" u2="d" k="10" />
<hkern u1="&#xc5;" u2="&#x166;" k="44" />
<hkern u1="&#xc5;" g2="hyphen.case" k="9" />
<hkern u1="&#xc5;" g2="braceright.case" k="11" />
<hkern u1="&#xc5;" g2="bracketright.case" k="11" />
<hkern u1="&#xc5;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc5;" u2="&#x29;" k="16" />
<hkern u1="&#xc5;" g2="nine.plf" k="10" />
<hkern u1="&#xc5;" g2="seven.plf" k="15" />
<hkern u1="&#xc5;" g2="six.plf" k="11" />
<hkern u1="&#xc5;" g2="one.plf" k="44" />
<hkern u1="&#xc5;" g2="zero.plf" k="12" />
<hkern u1="&#xc5;" u2="f" k="16" />
<hkern u1="&#xc5;" u2="&#x2039;" k="14" />
<hkern u1="&#xc5;" u2="&#x2a;" k="35" />
<hkern u1="&#xc5;" u2="&#xaa;" k="28" />
<hkern u1="&#xc5;" u2="&#xba;" k="31" />
<hkern u1="&#xc5;" u2="&#x2122;" k="43" />
<hkern u1="&#xc5;" u2="&#x27;" k="38" />
<hkern u1="&#xc5;" u2="&#xf0;" k="9" />
<hkern u1="&#xc5;" u2="&#x7d;" k="33" />
<hkern u1="&#xc5;" u2="&#xae;" k="16" />
<hkern u1="&#xc5;" u2="s" k="6" />
<hkern u1="&#xc5;" u2="&#x2018;" k="44" />
<hkern u1="&#xc5;" u2="&#x2019;" k="45" />
<hkern u1="&#xc5;" u2="&#xa9;" k="16" />
<hkern u1="&#xc5;" u2="&#x3f;" k="35" />
<hkern u1="&#xc5;" u2="\" k="48" />
<hkern u1="&#xc5;" u2="]" k="33" />
<hkern u1="&#xc5;" u2="U" k="13" />
<hkern u1="&#xc5;" u2="Y" k="57" />
<hkern u1="&#xc5;" u2="W" k="32" />
<hkern u1="&#xc5;" u2="V" k="41" />
<hkern u1="&#xc5;" u2="T" k="52" />
<hkern u1="&#xc5;" u2="S" k="9" />
<hkern u1="&#xc5;" u2="O" k="15" />
<hkern u1="&#xc5;" u2="y" k="27" />
<hkern u1="&#xc5;" u2="w" k="20" />
<hkern u1="&#xc5;" u2="v" k="25" />
<hkern u1="&#xc5;" u2="u" k="8" />
<hkern u1="&#xc5;" u2="o" k="10" />
<hkern u1="&#xc5;" u2="a" k="5" />
<hkern u1="&#xc5;" u2="&#x20;" k="26" />
<hkern u1="&#xd0;" u2="&#x166;" k="7" />
<hkern u1="&#xd0;" g2="parenright.case" k="27" />
<hkern u1="&#xd0;" g2="braceright.case" k="29" />
<hkern u1="&#xd0;" g2="bracketright.case" k="29" />
<hkern u1="&#xd0;" u2="&#x29;" k="25" />
<hkern u1="&#xd0;" g2="seven.plf" k="13" />
<hkern u1="&#xd0;" u2="&#xc6;" k="8" />
<hkern u1="&#xd0;" u2="&#x7d;" k="25" />
<hkern u1="&#xd0;" u2="]" k="25" />
<hkern u1="&#xd0;" u2="X" k="22" />
<hkern u1="&#xd0;" u2="Y" k="25" />
<hkern u1="&#xd0;" u2="Z" k="8" />
<hkern u1="&#xd0;" u2="W" k="13" />
<hkern u1="&#xd0;" u2="V" k="18" />
<hkern u1="&#xd0;" u2="J" k="17" />
<hkern u1="&#xd0;" u2="T" k="16" />
<hkern u1="&#xd0;" u2="A" k="15" />
<hkern u1="&#xd0;" u2="&#x2e;" k="9" />
<hkern u1="&#xd0;" u2="x" k="12" />
<hkern u1="&#x142;" u2="&#xb7;" k="61" />
<hkern u1="&#x142;" u2="Z" k="5" />
<hkern u1="&#x141;" u2="&#x166;" k="61" />
<hkern u1="&#xde;" u2="&#x104;" k="18" />
<hkern u1="&#xde;" u2="&#x1fa;" k="18" />
<hkern u1="&#xde;" u2="&#x21a;" k="22" />
<hkern u1="&#xde;" u2="&#x162;" k="22" />
<hkern u1="&#xde;" u2="&#x1fc;" k="13" />
<hkern u1="&#xde;" u2="&#x1ef2;" k="31" />
<hkern u1="&#xde;" u2="&#x1e84;" k="13" />
<hkern u1="&#xde;" u2="&#x1e82;" k="13" />
<hkern u1="&#xde;" u2="&#x1e80;" k="13" />
<hkern u1="&#xde;" u2="&#x17b;" k="13" />
<hkern u1="&#xde;" u2="&#x179;" k="13" />
<hkern u1="&#xde;" u2="&#x176;" k="31" />
<hkern u1="&#xde;" u2="&#x174;" k="13" />
<hkern u1="&#xde;" u2="&#x164;" k="22" />
<hkern u1="&#xde;" u2="&#x134;" k="29" />
<hkern u1="&#xde;" u2="&#x102;" k="18" />
<hkern u1="&#xde;" u2="&#x100;" k="18" />
<hkern u1="&#xde;" u2="&#xc3;" k="18" />
<hkern u1="&#xde;" u2="&#xc6;" k="13" />
<hkern u1="&#xde;" u2="&#xc5;" k="18" />
<hkern u1="&#xde;" u2="&#x17d;" k="13" />
<hkern u1="&#xde;" u2="&#x201e;" k="29" />
<hkern u1="&#xde;" u2="&#x201a;" k="29" />
<hkern u1="&#xde;" u2="&#xc1;" k="18" />
<hkern u1="&#xde;" u2="&#xc2;" k="18" />
<hkern u1="&#xde;" u2="&#x178;" k="31" />
<hkern u1="&#xde;" u2="&#xc0;" k="18" />
<hkern u1="&#xde;" u2="&#xc4;" k="18" />
<hkern u1="&#xde;" u2="&#xdd;" k="31" />
<hkern u1="&#xde;" u2="&#x2026;" k="29" />
<hkern u1="&#xde;" u2="Y" k="31" />
<hkern u1="&#xde;" u2="Z" k="13" />
<hkern u1="&#xde;" u2="W" k="13" />
<hkern u1="&#xde;" u2="J" k="29" />
<hkern u1="&#xde;" u2="T" k="22" />
<hkern u1="&#xde;" u2="A" k="18" />
<hkern u1="&#xde;" u2="&#x2c;" k="29" />
<hkern u1="&#xde;" u2="&#x2e;" k="29" />
<hkern u1="&#xde;" u2="&#x166;" k="8" />
<hkern u1="&#xde;" g2="parenright.case" k="28" />
<hkern u1="&#xde;" g2="braceright.case" k="28" />
<hkern u1="&#xde;" g2="bracketright.case" k="28" />
<hkern u1="&#xde;" u2="&#x29;" k="27" />
<hkern u1="&#xde;" g2="seven.plf" k="20" />
<hkern u1="&#xde;" u2="&#x2122;" k="10" />
<hkern u1="&#xde;" u2="&#x7d;" k="26" />
<hkern u1="&#xde;" u2="\" k="11" />
<hkern u1="&#xde;" u2="]" k="26" />
<hkern u1="&#xde;" u2="&#x2f;" k="13" />
<hkern u1="&#xde;" u2="X" k="29" />
<hkern u1="&#xde;" u2="V" k="20" />
<hkern u1="&#xde;" u2="x" k="5" />
<hkern u1="&#xf0;" u2="&#x104;" k="9" />
<hkern u1="&#xf0;" u2="&#x172;" k="7" />
<hkern u1="&#xf0;" u2="&#x12e;" k="6" />
<hkern u1="&#xf0;" u2="&#x118;" k="6" />
<hkern u1="&#xf0;" u2="&#x1fa;" k="9" />
<hkern u1="&#xf0;" u2="&#x166;" k="34" />
<hkern u1="&#xf0;" u2="&#x167;" k="7" />
<hkern u1="&#xf0;" u2="&#x126;" k="6" />
<hkern u1="&#xf0;" u2="&#x21a;" k="34" />
<hkern u1="&#xf0;" u2="&#x21b;" k="7" />
<hkern u1="&#xf0;" u2="&#x162;" k="34" />
<hkern u1="&#xf0;" u2="&#x163;" k="7" />
<hkern u1="&#xf0;" u2="&#x156;" k="6" />
<hkern u1="&#xf0;" u2="&#x145;" k="6" />
<hkern u1="&#xf0;" u2="&#x13b;" k="6" />
<hkern u1="&#xf0;" u2="&#x136;" k="6" />
<hkern u1="&#xf0;" u2="&#x13d;" k="6" />
<hkern u1="&#xf0;" u2="&#x110;" k="6" />
<hkern u1="&#xf0;" u2="&#x165;" k="7" />
<hkern u1="&#xf0;" u2="&#x14a;" k="6" />
<hkern u1="&#xf0;" u2="&#x1ef2;" k="33" />
<hkern u1="&#xf0;" u2="&#x1e84;" k="16" />
<hkern u1="&#xf0;" u2="&#x1e82;" k="16" />
<hkern u1="&#xf0;" u2="&#x1e80;" k="16" />
<hkern u1="&#xf0;" u2="&#x1ef3;" k="14" />
<hkern u1="&#xf0;" u2="&#x1e85;" k="9" />
<hkern u1="&#xf0;" u2="&#x1e83;" k="9" />
<hkern u1="&#xf0;" u2="&#x1e81;" k="9" />
<hkern u1="&#xf0;" u2="&#x17c;" k="5" />
<hkern u1="&#xf0;" u2="&#x17b;" k="13" />
<hkern u1="&#xf0;" u2="&#x17a;" k="5" />
<hkern u1="&#xf0;" u2="&#x179;" k="13" />
<hkern u1="&#xf0;" u2="&#x177;" k="14" />
<hkern u1="&#xf0;" u2="&#x176;" k="33" />
<hkern u1="&#xf0;" u2="&#x175;" k="9" />
<hkern u1="&#xf0;" u2="&#x174;" k="16" />
<hkern u1="&#xf0;" u2="&#x170;" k="7" />
<hkern u1="&#xf0;" u2="&#x16e;" k="7" />
<hkern u1="&#xf0;" u2="&#x168;" k="7" />
<hkern u1="&#xf0;" u2="&#x164;" k="34" />
<hkern u1="&#xf0;" u2="&#x158;" k="6" />
<hkern u1="&#xf0;" u2="&#x154;" k="6" />
<hkern u1="&#xf0;" u2="&#x147;" k="6" />
<hkern u1="&#xf0;" u2="&#x143;" k="6" />
<hkern u1="&#xf0;" u2="&#x139;" k="6" />
<hkern u1="&#xf0;" u2="&#x134;" k="6" />
<hkern u1="&#xf0;" u2="&#x132;" k="6" />
<hkern u1="&#xf0;" u2="&#x130;" k="6" />
<hkern u1="&#xf0;" u2="&#x128;" k="6" />
<hkern u1="&#xf0;" u2="&#x124;" k="6" />
<hkern u1="&#xf0;" u2="&#x11a;" k="6" />
<hkern u1="&#xf0;" u2="&#x16c;" k="7" />
<hkern u1="&#xf0;" u2="&#x12c;" k="6" />
<hkern u1="&#xf0;" u2="&#x114;" k="6" />
<hkern u1="&#xf0;" u2="&#x16a;" k="7" />
<hkern u1="&#xf0;" u2="&#x12a;" k="6" />
<hkern u1="&#xf0;" u2="&#x112;" k="6" />
<hkern u1="&#xf0;" u2="&#x10e;" k="6" />
<hkern u1="&#xf0;" u2="&#x102;" k="9" />
<hkern u1="&#xf0;" u2="&#x100;" k="9" />
<hkern u1="&#xf0;" u2="&#x116;" k="6" />
<hkern u1="&#xf0;" u2="&#xc3;" k="9" />
<hkern u1="&#xf0;" u2="&#xd1;" k="6" />
<hkern u1="&#xf0;" u2="f" k="7" />
<hkern u1="&#xf0;" u2="&#xde;" k="6" />
<hkern u1="&#xf0;" u2="&#x141;" k="6" />
<hkern u1="&#xf0;" u2="&#xd0;" k="6" />
<hkern u1="&#xf0;" u2="&#xc5;" k="9" />
<hkern u1="&#xf0;" u2="&#x17e;" k="5" />
<hkern u1="&#xf0;" u2="&#x17d;" k="13" />
<hkern u1="&#xf0;" u2="&#xd9;" k="7" />
<hkern u1="&#xf0;" u2="&#xdb;" k="7" />
<hkern u1="&#xf0;" u2="&#xda;" k="7" />
<hkern u1="&#xf0;" u2="&#xcc;" k="6" />
<hkern u1="&#xf0;" u2="&#xcf;" k="6" />
<hkern u1="&#xf0;" u2="&#xce;" k="6" />
<hkern u1="&#xf0;" u2="&#xcd;" k="6" />
<hkern u1="&#xf0;" u2="&#xc8;" k="6" />
<hkern u1="&#xf0;" u2="&#xcb;" k="6" />
<hkern u1="&#xf0;" u2="&#xc1;" k="9" />
<hkern u1="&#xf0;" u2="&#xca;" k="6" />
<hkern u1="&#xf0;" u2="&#xc2;" k="9" />
<hkern u1="&#xf0;" u2="&#x178;" k="33" />
<hkern u1="&#xf0;" u2="&#xff;" k="14" />
<hkern u1="&#xf0;" u2="&#xc0;" k="9" />
<hkern u1="&#xf0;" u2="&#xdc;" k="7" />
<hkern u1="&#xf0;" u2="&#xc9;" k="6" />
<hkern u1="&#xf0;" u2="&#xc4;" k="9" />
<hkern u1="&#xf0;" u2="&#xfd;" k="14" />
<hkern u1="&#xf0;" u2="&#xdd;" k="33" />
<hkern u1="&#xf0;" g2="fl" k="7" />
<hkern u1="&#xf0;" g2="fi" k="7" />
<hkern u1="&#xf0;" u2="U" k="7" />
<hkern u1="&#xf0;" u2="Y" k="33" />
<hkern u1="&#xf0;" u2="Z" k="13" />
<hkern u1="&#xf0;" u2="W" k="16" />
<hkern u1="&#xf0;" u2="J" k="6" />
<hkern u1="&#xf0;" u2="D" k="6" />
<hkern u1="&#xf0;" u2="B" k="6" />
<hkern u1="&#xf0;" u2="H" k="6" />
<hkern u1="&#xf0;" u2="P" k="6" />
<hkern u1="&#xf0;" u2="L" k="6" />
<hkern u1="&#xf0;" u2="T" k="34" />
<hkern u1="&#xf0;" u2="R" k="6" />
<hkern u1="&#xf0;" u2="N" k="6" />
<hkern u1="&#xf0;" u2="M" k="6" />
<hkern u1="&#xf0;" u2="K" k="6" />
<hkern u1="&#xf0;" u2="I" k="6" />
<hkern u1="&#xf0;" u2="F" k="6" />
<hkern u1="&#xf0;" u2="E" k="6" />
<hkern u1="&#xf0;" u2="A" k="9" />
<hkern u1="&#xf0;" u2="y" k="14" />
<hkern u1="&#xf0;" u2="z" k="5" />
<hkern u1="&#xf0;" u2="w" k="9" />
<hkern u1="&#xf0;" u2="t" k="7" />
<hkern u1="&#xf0;" u2="&#x29;" k="23" />
<hkern u1="&#xf0;" u2="&#x2122;" k="8" />
<hkern u1="&#xf0;" u2="&#x7d;" k="20" />
<hkern u1="&#xf0;" u2="]" k="20" />
<hkern u1="&#xf0;" u2="X" k="16" />
<hkern u1="&#xf0;" u2="V" k="23" />
<hkern u1="&#xf0;" u2="x" k="17" />
<hkern u1="&#xf0;" u2="v" k="12" />
<hkern u1="&#xe6;" u2="&#x29;" k="31" />
<hkern u1="&#xe6;" u2="f" k="4" />
<hkern u1="&#xe6;" u2="&#x2122;" k="20" />
<hkern u1="&#xe6;" u2="&#x7d;" k="32" />
<hkern u1="&#xe6;" u2="&#x2018;" k="16" />
<hkern u1="&#xe6;" u2="&#x2019;" k="18" />
<hkern u1="&#xe6;" u2="\" k="28" />
<hkern u1="&#xe6;" u2="]" k="31" />
<hkern u1="&#xe6;" u2="X" k="10" />
<hkern u1="&#xe6;" u2="U" k="5" />
<hkern u1="&#xe6;" u2="Y" k="83" />
<hkern u1="&#xe6;" u2="Z" k="8" />
<hkern u1="&#xe6;" u2="W" k="37" />
<hkern u1="&#xe6;" u2="V" k="42" />
<hkern u1="&#xe6;" u2="T" k="74" />
<hkern u1="&#xe6;" u2="A" k="7" />
<hkern u1="&#xe6;" u2="x" k="14" />
<hkern u1="&#xe6;" u2="y" k="10" />
<hkern u1="&#xe6;" u2="w" k="5" />
<hkern u1="&#xe6;" u2="v" k="8" />
<hkern u1="&#x153;" u2="&#x29;" k="31" />
<hkern u1="&#x153;" u2="f" k="4" />
<hkern u1="&#x153;" u2="&#x2122;" k="20" />
<hkern u1="&#x153;" u2="&#x7d;" k="32" />
<hkern u1="&#x153;" u2="&#x2018;" k="16" />
<hkern u1="&#x153;" u2="&#x2019;" k="18" />
<hkern u1="&#x153;" u2="\" k="28" />
<hkern u1="&#x153;" u2="]" k="31" />
<hkern u1="&#x153;" u2="X" k="10" />
<hkern u1="&#x153;" u2="U" k="5" />
<hkern u1="&#x153;" u2="Y" k="83" />
<hkern u1="&#x153;" u2="Z" k="8" />
<hkern u1="&#x153;" u2="W" k="37" />
<hkern u1="&#x153;" u2="V" k="42" />
<hkern u1="&#x153;" u2="T" k="74" />
<hkern u1="&#x153;" u2="A" k="7" />
<hkern u1="&#x153;" u2="x" k="14" />
<hkern u1="&#x153;" u2="y" k="10" />
<hkern u1="&#x153;" u2="w" k="5" />
<hkern u1="&#x153;" u2="v" k="8" />
<hkern u1="&#x22;" u2="d" k="11" />
<hkern u1="&#x22;" g2="four.plf" k="32" />
<hkern u1="&#x22;" u2="&#x2039;" k="13" />
<hkern u1="&#x22;" u2="&#xc6;" k="43" />
<hkern u1="&#x22;" u2="&#xf0;" k="15" />
<hkern u1="&#x22;" u2="&#x2f;" k="44" />
<hkern u1="&#x22;" u2="J" k="49" />
<hkern u1="&#x22;" u2="A" k="38" />
<hkern u1="&#x22;" u2="&#x2e;" k="105" />
<hkern u1="&#x22;" u2="o" k="8" />
<hkern u1="&#x22;" u2="&#x20;" k="10" />
<hkern u1="&#x27;" u2="d" k="11" />
<hkern u1="&#x27;" g2="four.plf" k="32" />
<hkern u1="&#x27;" u2="&#x2039;" k="13" />
<hkern u1="&#x27;" u2="&#xc6;" k="43" />
<hkern u1="&#x27;" u2="&#xf0;" k="15" />
<hkern u1="&#x27;" u2="&#x2f;" k="44" />
<hkern u1="&#x27;" u2="J" k="49" />
<hkern u1="&#x27;" u2="A" k="38" />
<hkern u1="&#x27;" u2="&#x2e;" k="105" />
<hkern u1="&#x27;" u2="o" k="8" />
<hkern u1="&#x27;" u2="&#x20;" k="10" />
<hkern u1="&#xb0;" g2="four.plf" k="38" />
<hkern u1="&#xdf;" u2="&#x104;" k="8" />
<hkern u1="&#xdf;" u2="&#x1fa;" k="8" />
<hkern u1="&#xdf;" u2="&#x166;" k="7" />
<hkern u1="&#xdf;" u2="&#x21a;" k="7" />
<hkern u1="&#xdf;" u2="&#x162;" k="7" />
<hkern u1="&#xdf;" u2="&#x1ef2;" k="16" />
<hkern u1="&#xdf;" u2="&#x1e84;" k="7" />
<hkern u1="&#xdf;" u2="&#x1e82;" k="7" />
<hkern u1="&#xdf;" u2="&#x1e80;" k="7" />
<hkern u1="&#xdf;" u2="&#x1ef3;" k="6" />
<hkern u1="&#xdf;" u2="&#x17b;" k="5" />
<hkern u1="&#xdf;" u2="&#x179;" k="5" />
<hkern u1="&#xdf;" u2="&#x177;" k="6" />
<hkern u1="&#xdf;" u2="&#x176;" k="16" />
<hkern u1="&#xdf;" u2="&#x174;" k="7" />
<hkern u1="&#xdf;" u2="&#x164;" k="7" />
<hkern u1="&#xdf;" u2="&#x102;" k="8" />
<hkern u1="&#xdf;" u2="&#x100;" k="8" />
<hkern u1="&#xdf;" u2="&#xc3;" k="8" />
<hkern u1="&#xdf;" u2="&#xc5;" k="8" />
<hkern u1="&#xdf;" u2="&#x17d;" k="5" />
<hkern u1="&#xdf;" u2="&#xc1;" k="8" />
<hkern u1="&#xdf;" u2="&#xc2;" k="8" />
<hkern u1="&#xdf;" u2="&#x178;" k="16" />
<hkern u1="&#xdf;" u2="&#xff;" k="6" />
<hkern u1="&#xdf;" u2="&#xc0;" k="8" />
<hkern u1="&#xdf;" u2="&#xc4;" k="8" />
<hkern u1="&#xdf;" u2="&#xfd;" k="6" />
<hkern u1="&#xdf;" u2="&#xdd;" k="16" />
<hkern u1="&#xdf;" u2="Y" k="16" />
<hkern u1="&#xdf;" u2="Z" k="5" />
<hkern u1="&#xdf;" u2="W" k="7" />
<hkern u1="&#xdf;" u2="T" k="7" />
<hkern u1="&#xdf;" u2="A" k="8" />
<hkern u1="&#xdf;" u2="y" k="6" />
<hkern u1="&#xdf;" u2="&#x29;" k="16" />
<hkern u1="&#xdf;" u2="&#x7d;" k="11" />
<hkern u1="&#xdf;" u2="]" k="11" />
<hkern u1="&#xdf;" u2="X" k="11" />
<hkern u1="&#xdf;" u2="V" k="12" />
<hkern u1="&#xdf;" u2="x" k="5" />
<hkern u1="&#xdf;" u2="v" k="5" />
<hkern u1="&#x2a;" u2="&#x104;" k="34" />
<hkern u1="&#x2a;" u2="&#x1fa;" k="34" />
<hkern u1="&#x2a;" u2="&#x1fc;" k="37" />
<hkern u1="&#x2a;" u2="&#x134;" k="49" />
<hkern u1="&#x2a;" u2="&#x102;" k="34" />
<hkern u1="&#x2a;" u2="&#x100;" k="34" />
<hkern u1="&#x2a;" u2="&#xc3;" k="34" />
<hkern u1="&#x2a;" u2="&#xc6;" k="37" />
<hkern u1="&#x2a;" u2="&#xc5;" k="34" />
<hkern u1="&#x2a;" u2="&#xc1;" k="34" />
<hkern u1="&#x2a;" u2="&#xc2;" k="34" />
<hkern u1="&#x2a;" u2="&#xc0;" k="34" />
<hkern u1="&#x2a;" u2="&#xc4;" k="34" />
<hkern u1="&#x2a;" u2="J" k="49" />
<hkern u1="&#x2a;" u2="A" k="34" />
<hkern u1="&#x2a;" u2="&#xf0;" k="12" />
<hkern u1="&#x2039;" u2="&#x166;" k="24" />
<hkern u1="&#x2039;" u2="Y" k="38" />
<hkern u1="&#x2039;" u2="W" k="13" />
<hkern u1="&#x2039;" u2="V" k="22" />
<hkern u1="&#x2039;" u2="T" k="55" />
<hkern u1="&#x203a;" u2="&#x166;" k="31" />
<hkern u1="&#x203a;" u2="f" k="15" />
<hkern u1="&#x203a;" u2="&#x27;" k="13" />
<hkern u1="&#x203a;" u2="&#x2019;" k="28" />
<hkern u1="&#x203a;" u2="X" k="17" />
<hkern u1="&#x203a;" u2="Y" k="61" />
<hkern u1="&#x203a;" u2="Z" k="11" />
<hkern u1="&#x203a;" u2="W" k="30" />
<hkern u1="&#x203a;" u2="V" k="40" />
<hkern u1="&#x203a;" u2="J" k="18" />
<hkern u1="&#x203a;" u2="T" k="61" />
<hkern u1="&#x203a;" u2="A" k="14" />
<hkern u1="&#x203a;" u2="x" k="25" />
<hkern u1="&#x203a;" u2="y" k="20" />
<hkern u1="&#x203a;" u2="z" k="20" />
<hkern u1="&#x203a;" u2="w" k="13" />
<hkern u1="&#x203a;" u2="v" k="18" />
<hkern u1="f" u2="d" k="8" />
<hkern u1="f" u2="&#x104;" k="29" />
<hkern u1="f" u2="&#x119;" k="8" />
<hkern u1="f" u2="&#x1fa;" k="29" />
<hkern u1="f" u2="&#x111;" k="8" />
<hkern u1="f" u2="&#x1ff;" k="8" />
<hkern u1="f" u2="&#x1fc;" k="23" />
<hkern u1="f" u2="&#x151;" k="8" />
<hkern u1="f" u2="&#x134;" k="28" />
<hkern u1="f" u2="&#x123;" k="8" />
<hkern u1="f" u2="&#x121;" k="8" />
<hkern u1="f" u2="&#x11d;" k="8" />
<hkern u1="f" u2="&#x11b;" k="8" />
<hkern u1="f" u2="&#x117;" k="8" />
<hkern u1="f" u2="&#x14f;" k="8" />
<hkern u1="f" u2="&#x11f;" k="8" />
<hkern u1="f" u2="&#x115;" k="8" />
<hkern u1="f" u2="&#x14d;" k="8" />
<hkern u1="f" u2="&#x113;" k="8" />
<hkern u1="f" u2="&#x10f;" k="8" />
<hkern u1="f" u2="&#x10d;" k="8" />
<hkern u1="f" u2="&#x10b;" k="8" />
<hkern u1="f" u2="&#x109;" k="8" />
<hkern u1="f" u2="&#x107;" k="8" />
<hkern u1="f" u2="&#x102;" k="29" />
<hkern u1="f" u2="&#x100;" k="29" />
<hkern u1="f" u2="&#xad;" k="12" />
<hkern u1="f" u2="&#xc3;" k="29" />
<hkern u1="f" u2="&#xf5;" k="8" />
<hkern u1="f" u2="&#x2039;" k="19" />
<hkern u1="f" u2="&#xc6;" k="23" />
<hkern u1="f" u2="&#x153;" k="8" />
<hkern u1="f" u2="&#xf8;" k="8" />
<hkern u1="f" u2="&#xc5;" k="29" />
<hkern u1="f" u2="g" k="8" />
<hkern u1="f" u2="&#xab;" k="19" />
<hkern u1="f" u2="&#x201e;" k="17" />
<hkern u1="f" u2="&#x201a;" k="17" />
<hkern u1="f" u2="&#xc1;" k="29" />
<hkern u1="f" u2="&#xc2;" k="29" />
<hkern u1="f" u2="&#xc0;" k="29" />
<hkern u1="f" u2="&#xf6;" k="8" />
<hkern u1="f" u2="&#xf4;" k="8" />
<hkern u1="f" u2="&#xf2;" k="8" />
<hkern u1="f" u2="&#xf3;" k="8" />
<hkern u1="f" u2="&#xeb;" k="8" />
<hkern u1="f" u2="&#xea;" k="8" />
<hkern u1="f" u2="&#xe8;" k="8" />
<hkern u1="f" u2="&#xe9;" k="8" />
<hkern u1="f" u2="&#xc4;" k="29" />
<hkern u1="f" u2="&#x2014;" k="12" />
<hkern u1="f" u2="&#x2013;" k="12" />
<hkern u1="f" u2="&#x2026;" k="17" />
<hkern u1="f" u2="&#xe7;" k="8" />
<hkern u1="f" u2="&#x2d;" k="12" />
<hkern u1="f" u2="J" k="28" />
<hkern u1="f" u2="A" k="29" />
<hkern u1="f" u2="&#x2c;" k="17" />
<hkern u1="f" u2="&#x2e;" k="17" />
<hkern u1="f" u2="c" k="8" />
<hkern u1="f" u2="e" k="8" />
<hkern u1="f" u2="o" k="8" />
<hkern u1="f" u2="q" k="8" />
<hkern u1="f" u2="&#x129;" k="-26" />
<hkern u1="f" u2="&#x12d;" k="-7" />
<hkern u1="f" u2="&#x12b;" k="-25" />
<hkern u1="f" u2="&#xf0;" k="17" />
<hkern u1="f" u2="&#x2f;" k="14" />
<hkern u1="f" u2="&#x20;" k="19" />
<hkern u1="n" u2="&#x29;" k="26" />
<hkern u1="n" u2="&#x2122;" k="20" />
<hkern u1="n" u2="&#x7d;" k="31" />
<hkern u1="n" u2="&#x2018;" k="13" />
<hkern u1="n" u2="&#x2019;" k="14" />
<hkern u1="n" u2="\" k="27" />
<hkern u1="n" u2="]" k="31" />
<hkern u1="n" u2="U" k="5" />
<hkern u1="n" u2="Y" k="61" />
<hkern u1="n" u2="Z" k="5" />
<hkern u1="n" u2="W" k="29" />
<hkern u1="n" u2="V" k="41" />
<hkern u1="n" u2="T" k="80" />
<hkern u1="n" u2="y" k="6" />
<hkern u1="n" u2="v" k="5" />
<hkern u1="&#xbb;" u2="&#x166;" k="31" />
<hkern u1="&#xbb;" u2="f" k="15" />
<hkern u1="&#xbb;" u2="&#x27;" k="13" />
<hkern u1="&#xbb;" u2="&#x2019;" k="28" />
<hkern u1="&#xbb;" u2="X" k="17" />
<hkern u1="&#xbb;" u2="Y" k="61" />
<hkern u1="&#xbb;" u2="Z" k="11" />
<hkern u1="&#xbb;" u2="W" k="30" />
<hkern u1="&#xbb;" u2="V" k="40" />
<hkern u1="&#xbb;" u2="J" k="18" />
<hkern u1="&#xbb;" u2="T" k="61" />
<hkern u1="&#xbb;" u2="A" k="14" />
<hkern u1="&#xbb;" u2="x" k="25" />
<hkern u1="&#xbb;" u2="y" k="20" />
<hkern u1="&#xbb;" u2="z" k="20" />
<hkern u1="&#xbb;" u2="w" k="13" />
<hkern u1="&#xbb;" u2="v" k="18" />
<hkern u1="i" u2="Z" k="6" />
<hkern u1="&#xd1;" u2="d" k="6" />
<hkern u1="&#xd1;" u2="&#xf0;" k="7" />
<hkern u1="&#xd1;" u2="o" k="6" />
<hkern u1="&#xf1;" u2="&#x29;" k="26" />
<hkern u1="&#xf1;" u2="&#x2122;" k="20" />
<hkern u1="&#xf1;" u2="&#x7d;" k="31" />
<hkern u1="&#xf1;" u2="&#x2018;" k="13" />
<hkern u1="&#xf1;" u2="&#x2019;" k="14" />
<hkern u1="&#xf1;" u2="\" k="27" />
<hkern u1="&#xf1;" u2="]" k="31" />
<hkern u1="&#xf1;" u2="U" k="5" />
<hkern u1="&#xf1;" u2="Y" k="61" />
<hkern u1="&#xf1;" u2="Z" k="5" />
<hkern u1="&#xf1;" u2="W" k="29" />
<hkern u1="&#xf1;" u2="V" k="41" />
<hkern u1="&#xf1;" u2="T" k="80" />
<hkern u1="&#xf1;" u2="y" k="6" />
<hkern u1="&#xf1;" u2="v" k="5" />
<hkern u1="&#xc3;" u2="d" k="10" />
<hkern u1="&#xc3;" u2="&#x166;" k="44" />
<hkern u1="&#xc3;" g2="hyphen.case" k="9" />
<hkern u1="&#xc3;" g2="braceright.case" k="11" />
<hkern u1="&#xc3;" g2="bracketright.case" k="11" />
<hkern u1="&#xc3;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#xc3;" u2="&#x29;" k="16" />
<hkern u1="&#xc3;" g2="nine.plf" k="10" />
<hkern u1="&#xc3;" g2="seven.plf" k="15" />
<hkern u1="&#xc3;" g2="six.plf" k="11" />
<hkern u1="&#xc3;" g2="one.plf" k="44" />
<hkern u1="&#xc3;" g2="zero.plf" k="12" />
<hkern u1="&#xc3;" u2="f" k="16" />
<hkern u1="&#xc3;" u2="&#x2039;" k="14" />
<hkern u1="&#xc3;" u2="&#x2a;" k="35" />
<hkern u1="&#xc3;" u2="&#xaa;" k="28" />
<hkern u1="&#xc3;" u2="&#xba;" k="31" />
<hkern u1="&#xc3;" u2="&#x2122;" k="43" />
<hkern u1="&#xc3;" u2="&#x27;" k="38" />
<hkern u1="&#xc3;" u2="&#xf0;" k="9" />
<hkern u1="&#xc3;" u2="&#x7d;" k="33" />
<hkern u1="&#xc3;" u2="&#xae;" k="16" />
<hkern u1="&#xc3;" u2="s" k="6" />
<hkern u1="&#xc3;" u2="&#x2018;" k="44" />
<hkern u1="&#xc3;" u2="&#x2019;" k="45" />
<hkern u1="&#xc3;" u2="&#xa9;" k="16" />
<hkern u1="&#xc3;" u2="&#x3f;" k="35" />
<hkern u1="&#xc3;" u2="\" k="48" />
<hkern u1="&#xc3;" u2="]" k="33" />
<hkern u1="&#xc3;" u2="U" k="13" />
<hkern u1="&#xc3;" u2="Y" k="57" />
<hkern u1="&#xc3;" u2="W" k="32" />
<hkern u1="&#xc3;" u2="V" k="41" />
<hkern u1="&#xc3;" u2="T" k="52" />
<hkern u1="&#xc3;" u2="S" k="9" />
<hkern u1="&#xc3;" u2="O" k="15" />
<hkern u1="&#xc3;" u2="y" k="27" />
<hkern u1="&#xc3;" u2="w" k="20" />
<hkern u1="&#xc3;" u2="v" k="25" />
<hkern u1="&#xc3;" u2="u" k="8" />
<hkern u1="&#xc3;" u2="o" k="10" />
<hkern u1="&#xc3;" u2="a" k="5" />
<hkern u1="&#xc3;" u2="&#x20;" k="26" />
<hkern g1="zero.plf" u2="&#x29;" k="22" />
<hkern g1="zero.plf" u2="&#x7d;" k="22" />
<hkern g1="zero.plf" u2="]" k="22" />
<hkern g1="zero.plf" u2="Y" k="19" />
<hkern g1="zero.plf" u2="W" k="11" />
<hkern g1="zero.plf" u2="V" k="16" />
<hkern g1="zero.plf" u2="A" k="12" />
<hkern g1="two.plf" u2="&#x1ef2;" k="13" />
<hkern g1="two.plf" u2="&#x176;" k="13" />
<hkern g1="two.plf" u2="&#x178;" k="13" />
<hkern g1="two.plf" u2="&#xdd;" k="13" />
<hkern g1="two.plf" u2="Y" k="13" />
<hkern g1="two.plf" g2="four.plf" k="11" />
<hkern g1="two.plf" u2="&#x7d;" k="12" />
<hkern g1="two.plf" u2="]" k="12" />
<hkern g1="two.plf" u2="V" k="13" />
<hkern g1="three.plf" u2="&#x1ef2;" k="15" />
<hkern g1="three.plf" u2="&#x1e84;" k="10" />
<hkern g1="three.plf" u2="&#x1e82;" k="10" />
<hkern g1="three.plf" u2="&#x1e80;" k="10" />
<hkern g1="three.plf" u2="&#x176;" k="15" />
<hkern g1="three.plf" u2="&#x174;" k="10" />
<hkern g1="three.plf" u2="&#x178;" k="15" />
<hkern g1="three.plf" u2="&#xdd;" k="15" />
<hkern g1="three.plf" u2="Y" k="15" />
<hkern g1="three.plf" u2="W" k="10" />
<hkern g1="three.plf" u2="&#x29;" k="18" />
<hkern g1="three.plf" u2="&#x7d;" k="18" />
<hkern g1="three.plf" u2="]" k="18" />
<hkern g1="three.plf" u2="V" k="14" />
<hkern g1="four.plf" u2="&#x1ef2;" k="25" />
<hkern g1="four.plf" u2="&#x1e84;" k="19" />
<hkern g1="four.plf" u2="&#x1e82;" k="19" />
<hkern g1="four.plf" u2="&#x1e80;" k="19" />
<hkern g1="four.plf" u2="&#x176;" k="25" />
<hkern g1="four.plf" u2="&#x174;" k="19" />
<hkern g1="four.plf" u2="&#x27;" k="15" />
<hkern g1="four.plf" u2="&#x22;" k="15" />
<hkern g1="four.plf" u2="&#x178;" k="25" />
<hkern g1="four.plf" u2="&#xdd;" k="25" />
<hkern g1="four.plf" u2="Y" k="25" />
<hkern g1="four.plf" u2="W" k="19" />
<hkern g1="four.plf" u2="&#x29;" k="21" />
<hkern g1="four.plf" g2="seven.plf" k="13" />
<hkern g1="four.plf" g2="one.plf" k="15" />
<hkern g1="four.plf" u2="&#xb0;" k="15" />
<hkern g1="four.plf" u2="&#x7d;" k="21" />
<hkern g1="four.plf" u2="\" k="12" />
<hkern g1="four.plf" u2="]" k="20" />
<hkern g1="four.plf" u2="V" k="25" />
<hkern g1="five.plf" u2="&#x1ef2;" k="12" />
<hkern g1="five.plf" u2="&#x1e84;" k="10" />
<hkern g1="five.plf" u2="&#x1e82;" k="10" />
<hkern g1="five.plf" u2="&#x1e80;" k="10" />
<hkern g1="five.plf" u2="&#x176;" k="12" />
<hkern g1="five.plf" u2="&#x174;" k="10" />
<hkern g1="five.plf" u2="&#x178;" k="12" />
<hkern g1="five.plf" u2="&#xdd;" k="12" />
<hkern g1="five.plf" u2="Y" k="12" />
<hkern g1="five.plf" u2="W" k="10" />
<hkern g1="five.plf" g2="one.plf" k="12" />
<hkern g1="five.plf" u2="V" k="14" />
<hkern g1="six.plf" u2="&#x1ef2;" k="15" />
<hkern g1="six.plf" u2="&#x1e84;" k="10" />
<hkern g1="six.plf" u2="&#x1e82;" k="10" />
<hkern g1="six.plf" u2="&#x1e80;" k="10" />
<hkern g1="six.plf" u2="&#x176;" k="15" />
<hkern g1="six.plf" u2="&#x174;" k="10" />
<hkern g1="six.plf" u2="&#x178;" k="15" />
<hkern g1="six.plf" u2="&#xdd;" k="15" />
<hkern g1="six.plf" u2="Y" k="15" />
<hkern g1="six.plf" u2="W" k="10" />
<hkern g1="six.plf" u2="&#x29;" k="18" />
<hkern g1="six.plf" u2="&#x7d;" k="18" />
<hkern g1="six.plf" u2="]" k="17" />
<hkern g1="six.plf" u2="V" k="14" />
<hkern g1="seven.plf" u2="&#x104;" k="45" />
<hkern g1="seven.plf" u2="&#x1fa;" k="45" />
<hkern g1="seven.plf" u2="&#x1fc;" k="52" />
<hkern g1="seven.plf" u2="&#x134;" k="49" />
<hkern g1="seven.plf" u2="&#x102;" k="45" />
<hkern g1="seven.plf" u2="&#x100;" k="45" />
<hkern g1="seven.plf" u2="&#xad;" k="23" />
<hkern g1="seven.plf" u2="&#xc3;" k="45" />
<hkern g1="seven.plf" u2="&#xc6;" k="52" />
<hkern g1="seven.plf" u2="&#xc5;" k="45" />
<hkern g1="seven.plf" u2="&#x201e;" k="62" />
<hkern g1="seven.plf" u2="&#x201a;" k="62" />
<hkern g1="seven.plf" u2="&#xc1;" k="45" />
<hkern g1="seven.plf" u2="&#xc2;" k="45" />
<hkern g1="seven.plf" u2="&#xc0;" k="45" />
<hkern g1="seven.plf" u2="&#xc4;" k="45" />
<hkern g1="seven.plf" u2="&#x2014;" k="23" />
<hkern g1="seven.plf" u2="&#x2013;" k="23" />
<hkern g1="seven.plf" u2="&#x2026;" k="62" />
<hkern g1="seven.plf" u2="&#x2d;" k="23" />
<hkern g1="seven.plf" u2="J" k="49" />
<hkern g1="seven.plf" u2="A" k="45" />
<hkern g1="seven.plf" u2="&#x2c;" k="62" />
<hkern g1="seven.plf" u2="&#x2e;" k="62" />
<hkern g1="seven.plf" g2="four.plf" k="37" />
<hkern g1="seven.plf" u2="&#xb7;" k="25" />
<hkern g1="seven.plf" u2="&#x2f;" k="53" />
<hkern g1="eight.plf" u2="&#x1ef2;" k="17" />
<hkern g1="eight.plf" u2="&#x1e84;" k="11" />
<hkern g1="eight.plf" u2="&#x1e82;" k="11" />
<hkern g1="eight.plf" u2="&#x1e80;" k="11" />
<hkern g1="eight.plf" u2="&#x176;" k="17" />
<hkern g1="eight.plf" u2="&#x174;" k="11" />
<hkern g1="eight.plf" u2="&#x178;" k="17" />
<hkern g1="eight.plf" u2="&#xdd;" k="17" />
<hkern g1="eight.plf" u2="Y" k="17" />
<hkern g1="eight.plf" u2="W" k="11" />
<hkern g1="eight.plf" u2="&#x29;" k="20" />
<hkern g1="eight.plf" u2="&#x7d;" k="20" />
<hkern g1="eight.plf" u2="]" k="20" />
<hkern g1="eight.plf" u2="V" k="16" />
<hkern g1="nine.plf" u2="&#x104;" k="12" />
<hkern g1="nine.plf" u2="&#x1fa;" k="12" />
<hkern g1="nine.plf" u2="&#x1ef2;" k="17" />
<hkern g1="nine.plf" u2="&#x1e84;" k="11" />
<hkern g1="nine.plf" u2="&#x1e82;" k="11" />
<hkern g1="nine.plf" u2="&#x1e80;" k="11" />
<hkern g1="nine.plf" u2="&#x176;" k="17" />
<hkern g1="nine.plf" u2="&#x174;" k="11" />
<hkern g1="nine.plf" u2="&#x102;" k="12" />
<hkern g1="nine.plf" u2="&#x100;" k="12" />
<hkern g1="nine.plf" u2="&#xc3;" k="12" />
<hkern g1="nine.plf" u2="&#xc5;" k="12" />
<hkern g1="nine.plf" u2="&#xc1;" k="12" />
<hkern g1="nine.plf" u2="&#xc2;" k="12" />
<hkern g1="nine.plf" u2="&#x178;" k="17" />
<hkern g1="nine.plf" u2="&#xc0;" k="12" />
<hkern g1="nine.plf" u2="&#xc4;" k="12" />
<hkern g1="nine.plf" u2="&#xdd;" k="17" />
<hkern g1="nine.plf" u2="Y" k="17" />
<hkern g1="nine.plf" u2="W" k="11" />
<hkern g1="nine.plf" u2="A" k="12" />
<hkern g1="nine.plf" u2="&#x29;" k="21" />
<hkern g1="nine.plf" u2="&#x7d;" k="21" />
<hkern g1="nine.plf" u2="]" k="21" />
<hkern g1="nine.plf" u2="V" k="15" />
<hkern g1="zero.plfslash" u2="&#x29;" k="22" />
<hkern g1="zero.plfslash" u2="&#x7d;" k="22" />
<hkern g1="zero.plfslash" u2="]" k="22" />
<hkern g1="zero.plfslash" u2="Y" k="19" />
<hkern g1="zero.plfslash" u2="W" k="11" />
<hkern g1="zero.plfslash" u2="V" k="16" />
<hkern g1="zero.plfslash" u2="A" k="12" />
<hkern u1="&#x29;" u2="&#x29;" k="19" />
<hkern u1="&#x29;" u2="&#x7d;" k="19" />
<hkern u1="&#x29;" u2="]" k="19" />
<hkern g1="seven.num" u2="&#x2044;" k="33" />
<hkern g1="at.case" u2="&#x104;" k="10" />
<hkern g1="at.case" u2="&#x1fa;" k="10" />
<hkern g1="at.case" u2="&#x134;" k="15" />
<hkern g1="at.case" u2="&#x102;" k="10" />
<hkern g1="at.case" u2="&#x100;" k="10" />
<hkern g1="at.case" u2="&#xc3;" k="10" />
<hkern g1="at.case" u2="&#xc5;" k="10" />
<hkern g1="at.case" u2="&#xc1;" k="10" />
<hkern g1="at.case" u2="&#xc2;" k="10" />
<hkern g1="at.case" u2="&#xc0;" k="10" />
<hkern g1="at.case" u2="&#xc4;" k="10" />
<hkern g1="at.case" u2="J" k="15" />
<hkern g1="at.case" u2="A" k="10" />
<hkern g1="bracketleft.case" u2="&#x104;" k="12" />
<hkern g1="bracketleft.case" u2="&#x172;" k="14" />
<hkern g1="bracketleft.case" u2="&#x1fa;" k="12" />
<hkern g1="bracketleft.case" u2="&#x218;" k="23" />
<hkern g1="bracketleft.case" u2="&#x15e;" k="23" />
<hkern g1="bracketleft.case" u2="&#x122;" k="28" />
<hkern g1="bracketleft.case" u2="&#x1fe;" k="28" />
<hkern g1="bracketleft.case" u2="&#x1ef2;" k="13" />
<hkern g1="bracketleft.case" u2="&#x176;" k="13" />
<hkern g1="bracketleft.case" u2="&#x170;" k="14" />
<hkern g1="bracketleft.case" u2="&#x16e;" k="14" />
<hkern g1="bracketleft.case" u2="&#x168;" k="14" />
<hkern g1="bracketleft.case" u2="&#x15c;" k="23" />
<hkern g1="bracketleft.case" u2="&#x15a;" k="23" />
<hkern g1="bracketleft.case" u2="&#x150;" k="28" />
<hkern g1="bracketleft.case" u2="&#x11c;" k="28" />
<hkern g1="bracketleft.case" u2="&#x16c;" k="14" />
<hkern g1="bracketleft.case" u2="&#x14e;" k="28" />
<hkern g1="bracketleft.case" u2="&#x11e;" k="28" />
<hkern g1="bracketleft.case" u2="&#x16a;" k="14" />
<hkern g1="bracketleft.case" u2="&#x14c;" k="28" />
<hkern g1="bracketleft.case" u2="&#x10c;" k="28" />
<hkern g1="bracketleft.case" u2="&#x10a;" k="28" />
<hkern g1="bracketleft.case" u2="&#x108;" k="28" />
<hkern g1="bracketleft.case" u2="&#x106;" k="28" />
<hkern g1="bracketleft.case" u2="&#x102;" k="12" />
<hkern g1="bracketleft.case" u2="&#x100;" k="12" />
<hkern g1="bracketleft.case" u2="&#x120;" k="28" />
<hkern g1="bracketleft.case" u2="&#xd5;" k="28" />
<hkern g1="bracketleft.case" u2="&#xc3;" k="12" />
<hkern g1="bracketleft.case" u2="&#xd8;" k="28" />
<hkern g1="bracketleft.case" u2="&#x152;" k="28" />
<hkern g1="bracketleft.case" u2="&#xc5;" k="12" />
<hkern g1="bracketleft.case" u2="&#x160;" k="23" />
<hkern g1="bracketleft.case" u2="&#xd6;" k="28" />
<hkern g1="bracketleft.case" u2="&#xd2;" k="28" />
<hkern g1="bracketleft.case" u2="&#xd4;" k="28" />
<hkern g1="bracketleft.case" u2="&#xd3;" k="28" />
<hkern g1="bracketleft.case" u2="&#xc7;" k="28" />
<hkern g1="bracketleft.case" u2="&#xd9;" k="14" />
<hkern g1="bracketleft.case" u2="&#xdb;" k="14" />
<hkern g1="bracketleft.case" u2="&#xda;" k="14" />
<hkern g1="bracketleft.case" u2="&#xc1;" k="12" />
<hkern g1="bracketleft.case" u2="&#xc2;" k="12" />
<hkern g1="bracketleft.case" u2="&#x178;" k="13" />
<hkern g1="bracketleft.case" u2="&#xc0;" k="12" />
<hkern g1="bracketleft.case" u2="&#xdc;" k="14" />
<hkern g1="bracketleft.case" u2="&#xc4;" k="12" />
<hkern g1="bracketleft.case" u2="&#xdd;" k="13" />
<hkern g1="bracketleft.case" u2="G" k="28" />
<hkern g1="bracketleft.case" u2="U" k="14" />
<hkern g1="bracketleft.case" u2="C" k="28" />
<hkern g1="bracketleft.case" u2="Y" k="13" />
<hkern g1="bracketleft.case" u2="Q" k="28" />
<hkern g1="bracketleft.case" u2="S" k="23" />
<hkern g1="bracketleft.case" u2="O" k="28" />
<hkern g1="bracketleft.case" u2="A" k="12" />
<hkern g1="bracketleft.case" u2="&#x128;" k="-6" />
<hkern g1="bracketleft.case" g2="parenleft.case" k="16" />
<hkern g1="bracketleft.case" u2="V" k="13" />
<hkern g1="braceleft.case" u2="&#x104;" k="12" />
<hkern g1="braceleft.case" u2="&#x172;" k="14" />
<hkern g1="braceleft.case" u2="&#x1fa;" k="12" />
<hkern g1="braceleft.case" u2="&#x218;" k="23" />
<hkern g1="braceleft.case" u2="&#x15e;" k="23" />
<hkern g1="braceleft.case" u2="&#x122;" k="29" />
<hkern g1="braceleft.case" u2="&#x1fe;" k="29" />
<hkern g1="braceleft.case" u2="&#x1ef2;" k="13" />
<hkern g1="braceleft.case" u2="&#x176;" k="13" />
<hkern g1="braceleft.case" u2="&#x170;" k="14" />
<hkern g1="braceleft.case" u2="&#x16e;" k="14" />
<hkern g1="braceleft.case" u2="&#x168;" k="14" />
<hkern g1="braceleft.case" u2="&#x15c;" k="23" />
<hkern g1="braceleft.case" u2="&#x15a;" k="23" />
<hkern g1="braceleft.case" u2="&#x150;" k="29" />
<hkern g1="braceleft.case" u2="&#x11c;" k="29" />
<hkern g1="braceleft.case" u2="&#x16c;" k="14" />
<hkern g1="braceleft.case" u2="&#x14e;" k="29" />
<hkern g1="braceleft.case" u2="&#x11e;" k="29" />
<hkern g1="braceleft.case" u2="&#x16a;" k="14" />
<hkern g1="braceleft.case" u2="&#x14c;" k="29" />
<hkern g1="braceleft.case" u2="&#x10c;" k="29" />
<hkern g1="braceleft.case" u2="&#x10a;" k="29" />
<hkern g1="braceleft.case" u2="&#x108;" k="29" />
<hkern g1="braceleft.case" u2="&#x106;" k="29" />
<hkern g1="braceleft.case" u2="&#x102;" k="12" />
<hkern g1="braceleft.case" u2="&#x100;" k="12" />
<hkern g1="braceleft.case" u2="&#x120;" k="29" />
<hkern g1="braceleft.case" u2="&#xd5;" k="29" />
<hkern g1="braceleft.case" u2="&#xc3;" k="12" />
<hkern g1="braceleft.case" u2="&#xd8;" k="29" />
<hkern g1="braceleft.case" u2="&#x152;" k="29" />
<hkern g1="braceleft.case" u2="&#xc5;" k="12" />
<hkern g1="braceleft.case" u2="&#x160;" k="23" />
<hkern g1="braceleft.case" u2="&#xd6;" k="29" />
<hkern g1="braceleft.case" u2="&#xd2;" k="29" />
<hkern g1="braceleft.case" u2="&#xd4;" k="29" />
<hkern g1="braceleft.case" u2="&#xd3;" k="29" />
<hkern g1="braceleft.case" u2="&#xc7;" k="29" />
<hkern g1="braceleft.case" u2="&#xd9;" k="14" />
<hkern g1="braceleft.case" u2="&#xdb;" k="14" />
<hkern g1="braceleft.case" u2="&#xda;" k="14" />
<hkern g1="braceleft.case" u2="&#xc1;" k="12" />
<hkern g1="braceleft.case" u2="&#xc2;" k="12" />
<hkern g1="braceleft.case" u2="&#x178;" k="13" />
<hkern g1="braceleft.case" u2="&#xc0;" k="12" />
<hkern g1="braceleft.case" u2="&#xdc;" k="14" />
<hkern g1="braceleft.case" u2="&#xc4;" k="12" />
<hkern g1="braceleft.case" u2="&#xdd;" k="13" />
<hkern g1="braceleft.case" u2="G" k="29" />
<hkern g1="braceleft.case" u2="U" k="14" />
<hkern g1="braceleft.case" u2="C" k="29" />
<hkern g1="braceleft.case" u2="Y" k="13" />
<hkern g1="braceleft.case" u2="Q" k="29" />
<hkern g1="braceleft.case" u2="S" k="23" />
<hkern g1="braceleft.case" u2="O" k="29" />
<hkern g1="braceleft.case" u2="A" k="12" />
<hkern g1="braceleft.case" u2="&#x128;" k="-6" />
<hkern g1="braceleft.case" g2="parenleft.case" k="16" />
<hkern g1="braceleft.case" u2="V" k="13" />
<hkern g1="parenleft.case" u2="&#x172;" k="13" />
<hkern g1="parenleft.case" u2="&#x122;" k="26" />
<hkern g1="parenleft.case" u2="&#x1fe;" k="26" />
<hkern g1="parenleft.case" u2="&#x170;" k="13" />
<hkern g1="parenleft.case" u2="&#x16e;" k="13" />
<hkern g1="parenleft.case" u2="&#x168;" k="13" />
<hkern g1="parenleft.case" u2="&#x150;" k="26" />
<hkern g1="parenleft.case" u2="&#x11c;" k="26" />
<hkern g1="parenleft.case" u2="&#x16c;" k="13" />
<hkern g1="parenleft.case" u2="&#x14e;" k="26" />
<hkern g1="parenleft.case" u2="&#x11e;" k="26" />
<hkern g1="parenleft.case" u2="&#x16a;" k="13" />
<hkern g1="parenleft.case" u2="&#x14c;" k="26" />
<hkern g1="parenleft.case" u2="&#x10c;" k="26" />
<hkern g1="parenleft.case" u2="&#x10a;" k="26" />
<hkern g1="parenleft.case" u2="&#x108;" k="26" />
<hkern g1="parenleft.case" u2="&#x106;" k="26" />
<hkern g1="parenleft.case" u2="&#x120;" k="26" />
<hkern g1="parenleft.case" u2="&#xd5;" k="26" />
<hkern g1="parenleft.case" u2="&#xd8;" k="26" />
<hkern g1="parenleft.case" u2="&#x152;" k="26" />
<hkern g1="parenleft.case" u2="&#xd6;" k="26" />
<hkern g1="parenleft.case" u2="&#xd2;" k="26" />
<hkern g1="parenleft.case" u2="&#xd4;" k="26" />
<hkern g1="parenleft.case" u2="&#xd3;" k="26" />
<hkern g1="parenleft.case" u2="&#xc7;" k="26" />
<hkern g1="parenleft.case" u2="&#xd9;" k="13" />
<hkern g1="parenleft.case" u2="&#xdb;" k="13" />
<hkern g1="parenleft.case" u2="&#xda;" k="13" />
<hkern g1="parenleft.case" u2="&#xdc;" k="13" />
<hkern g1="parenleft.case" u2="G" k="26" />
<hkern g1="parenleft.case" u2="U" k="13" />
<hkern g1="parenleft.case" u2="C" k="26" />
<hkern g1="parenleft.case" u2="Q" k="26" />
<hkern g1="parenleft.case" u2="O" k="26" />
<hkern g1="parenleft.case" u2="&#x218;" k="19" />
<hkern g1="parenleft.case" u2="&#x15e;" k="19" />
<hkern g1="parenleft.case" u2="&#x15c;" k="19" />
<hkern g1="parenleft.case" u2="&#x15a;" k="19" />
<hkern g1="parenleft.case" u2="&#x160;" k="19" />
<hkern g1="parenleft.case" u2="S" k="19" />
<hkern g1="parenleft.case" u2="&#x128;" k="-19" />
<hkern g1="parenleft.case" g2="parenleft.case" k="16" />
<hkern g1="parenright.case" g2="parenright.case" k="15" />
<hkern g1="parenright.case" g2="braceright.case" k="15" />
<hkern g1="parenright.case" g2="bracketright.case" k="15" />
<hkern g1="endash.case" u2="&#x166;" k="10" />
<hkern g1="endash.case" u2="X" k="18" />
<hkern g1="endash.case" u2="Y" k="30" />
<hkern g1="endash.case" u2="Z" k="8" />
<hkern g1="endash.case" u2="V" k="14" />
<hkern g1="endash.case" u2="J" k="44" />
<hkern g1="endash.case" u2="T" k="38" />
<hkern g1="endash.case" u2="A" k="9" />
<hkern g1="emdash.case" u2="&#x166;" k="10" />
<hkern g1="emdash.case" u2="X" k="18" />
<hkern g1="emdash.case" u2="Y" k="30" />
<hkern g1="emdash.case" u2="Z" k="8" />
<hkern g1="emdash.case" u2="V" k="14" />
<hkern g1="emdash.case" u2="J" k="44" />
<hkern g1="emdash.case" u2="T" k="38" />
<hkern g1="emdash.case" u2="A" k="9" />
<hkern g1="hyphen.case" u2="&#x166;" k="10" />
<hkern g1="hyphen.case" u2="X" k="18" />
<hkern g1="hyphen.case" u2="Y" k="30" />
<hkern g1="hyphen.case" u2="Z" k="8" />
<hkern g1="hyphen.case" u2="V" k="14" />
<hkern g1="hyphen.case" u2="J" k="44" />
<hkern g1="hyphen.case" u2="T" k="38" />
<hkern g1="hyphen.case" u2="A" k="9" />
<hkern u1="&#xad;" u2="&#x166;" k="11" />
<hkern u1="&#xad;" g2="seven.plf" k="27" />
<hkern u1="&#xad;" g2="one.plf" k="17" />
<hkern u1="&#xad;" u2="X" k="12" />
<hkern u1="&#xad;" u2="Y" k="42" />
<hkern u1="&#xad;" u2="W" k="12" />
<hkern u1="&#xad;" u2="V" k="23" />
<hkern u1="&#xad;" u2="J" k="43" />
<hkern u1="&#xad;" u2="T" k="39" />
<hkern u1="&#xad;" u2="x" k="15" />
<hkern u1="&#xad;" u2="z" k="14" />
<hkern u1="&#x120;" u2="&#x29;" k="14" />
<hkern u1="&#x120;" u2="&#x7d;" k="19" />
<hkern u1="&#x120;" u2="]" k="19" />
<hkern u1="&#x120;" u2="Y" k="23" />
<hkern u1="&#x120;" u2="W" k="12" />
<hkern u1="&#x120;" u2="V" k="18" />
<hkern u1="&#x120;" u2="T" k="10" />
<hkern u1="&#x100;" u2="d" k="10" />
<hkern u1="&#x100;" u2="&#x166;" k="44" />
<hkern u1="&#x100;" g2="hyphen.case" k="9" />
<hkern u1="&#x100;" g2="braceright.case" k="11" />
<hkern u1="&#x100;" g2="bracketright.case" k="11" />
<hkern u1="&#x100;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x100;" u2="&#x29;" k="16" />
<hkern u1="&#x100;" g2="nine.plf" k="10" />
<hkern u1="&#x100;" g2="seven.plf" k="15" />
<hkern u1="&#x100;" g2="six.plf" k="11" />
<hkern u1="&#x100;" g2="one.plf" k="44" />
<hkern u1="&#x100;" g2="zero.plf" k="12" />
<hkern u1="&#x100;" u2="f" k="16" />
<hkern u1="&#x100;" u2="&#x2039;" k="14" />
<hkern u1="&#x100;" u2="&#x2a;" k="35" />
<hkern u1="&#x100;" u2="&#xaa;" k="28" />
<hkern u1="&#x100;" u2="&#xba;" k="31" />
<hkern u1="&#x100;" u2="&#x2122;" k="43" />
<hkern u1="&#x100;" u2="&#x27;" k="38" />
<hkern u1="&#x100;" u2="&#xf0;" k="9" />
<hkern u1="&#x100;" u2="&#x7d;" k="33" />
<hkern u1="&#x100;" u2="&#xae;" k="16" />
<hkern u1="&#x100;" u2="s" k="6" />
<hkern u1="&#x100;" u2="&#x2018;" k="44" />
<hkern u1="&#x100;" u2="&#x2019;" k="45" />
<hkern u1="&#x100;" u2="&#xa9;" k="16" />
<hkern u1="&#x100;" u2="&#x3f;" k="35" />
<hkern u1="&#x100;" u2="\" k="48" />
<hkern u1="&#x100;" u2="]" k="33" />
<hkern u1="&#x100;" u2="U" k="13" />
<hkern u1="&#x100;" u2="Y" k="57" />
<hkern u1="&#x100;" u2="W" k="32" />
<hkern u1="&#x100;" u2="V" k="41" />
<hkern u1="&#x100;" u2="T" k="52" />
<hkern u1="&#x100;" u2="S" k="9" />
<hkern u1="&#x100;" u2="O" k="15" />
<hkern u1="&#x100;" u2="y" k="27" />
<hkern u1="&#x100;" u2="w" k="20" />
<hkern u1="&#x100;" u2="v" k="25" />
<hkern u1="&#x100;" u2="u" k="8" />
<hkern u1="&#x100;" u2="o" k="10" />
<hkern u1="&#x100;" u2="a" k="5" />
<hkern u1="&#x100;" u2="&#x20;" k="26" />
<hkern u1="&#x102;" u2="d" k="10" />
<hkern u1="&#x102;" u2="&#x166;" k="44" />
<hkern u1="&#x102;" g2="hyphen.case" k="9" />
<hkern u1="&#x102;" g2="braceright.case" k="11" />
<hkern u1="&#x102;" g2="bracketright.case" k="11" />
<hkern u1="&#x102;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x102;" u2="&#x29;" k="16" />
<hkern u1="&#x102;" g2="nine.plf" k="10" />
<hkern u1="&#x102;" g2="seven.plf" k="15" />
<hkern u1="&#x102;" g2="six.plf" k="11" />
<hkern u1="&#x102;" g2="one.plf" k="44" />
<hkern u1="&#x102;" g2="zero.plf" k="12" />
<hkern u1="&#x102;" u2="f" k="16" />
<hkern u1="&#x102;" u2="&#x2039;" k="14" />
<hkern u1="&#x102;" u2="&#x2a;" k="35" />
<hkern u1="&#x102;" u2="&#xaa;" k="28" />
<hkern u1="&#x102;" u2="&#xba;" k="31" />
<hkern u1="&#x102;" u2="&#x2122;" k="43" />
<hkern u1="&#x102;" u2="&#x27;" k="38" />
<hkern u1="&#x102;" u2="&#xf0;" k="9" />
<hkern u1="&#x102;" u2="&#x7d;" k="33" />
<hkern u1="&#x102;" u2="&#xae;" k="16" />
<hkern u1="&#x102;" u2="s" k="6" />
<hkern u1="&#x102;" u2="&#x2018;" k="44" />
<hkern u1="&#x102;" u2="&#x2019;" k="45" />
<hkern u1="&#x102;" u2="&#xa9;" k="16" />
<hkern u1="&#x102;" u2="&#x3f;" k="35" />
<hkern u1="&#x102;" u2="\" k="48" />
<hkern u1="&#x102;" u2="]" k="33" />
<hkern u1="&#x102;" u2="U" k="13" />
<hkern u1="&#x102;" u2="Y" k="57" />
<hkern u1="&#x102;" u2="W" k="32" />
<hkern u1="&#x102;" u2="V" k="41" />
<hkern u1="&#x102;" u2="T" k="52" />
<hkern u1="&#x102;" u2="S" k="9" />
<hkern u1="&#x102;" u2="O" k="15" />
<hkern u1="&#x102;" u2="y" k="27" />
<hkern u1="&#x102;" u2="w" k="20" />
<hkern u1="&#x102;" u2="v" k="25" />
<hkern u1="&#x102;" u2="u" k="8" />
<hkern u1="&#x102;" u2="o" k="10" />
<hkern u1="&#x102;" u2="a" k="5" />
<hkern u1="&#x102;" u2="&#x20;" k="26" />
<hkern u1="&#x106;" g2="parenright.case" k="19" />
<hkern u1="&#x106;" g2="braceright.case" k="23" />
<hkern u1="&#x106;" g2="bracketright.case" k="23" />
<hkern u1="&#x106;" u2="&#x29;" k="17" />
<hkern u1="&#x106;" u2="&#x7d;" k="18" />
<hkern u1="&#x106;" u2="]" k="18" />
<hkern u1="&#x106;" u2="X" k="5" />
<hkern u1="&#x106;" u2="Y" k="13" />
<hkern u1="&#x106;" u2="W" k="6" />
<hkern u1="&#x106;" u2="V" k="10" />
<hkern u1="&#x106;" u2="A" k="6" />
<hkern u1="&#x107;" u2="&#x29;" k="30" />
<hkern u1="&#x107;" u2="&#x2122;" k="20" />
<hkern u1="&#x107;" u2="&#x7d;" k="30" />
<hkern u1="&#x107;" u2="&#x2018;" k="12" />
<hkern u1="&#x107;" u2="&#x2019;" k="14" />
<hkern u1="&#x107;" u2="\" k="24" />
<hkern u1="&#x107;" u2="]" k="30" />
<hkern u1="&#x107;" u2="X" k="6" />
<hkern u1="&#x107;" u2="Y" k="71" />
<hkern u1="&#x107;" u2="W" k="30" />
<hkern u1="&#x107;" u2="V" k="44" />
<hkern u1="&#x107;" u2="T" k="84" />
<hkern u1="&#x107;" u2="S" k="5" />
<hkern u1="&#x107;" u2="A" k="5" />
<hkern u1="&#x107;" u2="x" k="8" />
<hkern u1="&#x107;" u2="y" k="7" />
<hkern u1="&#x107;" u2="v" k="5" />
<hkern u1="&#x108;" g2="parenright.case" k="19" />
<hkern u1="&#x108;" g2="braceright.case" k="23" />
<hkern u1="&#x108;" g2="bracketright.case" k="23" />
<hkern u1="&#x108;" u2="&#x29;" k="17" />
<hkern u1="&#x108;" u2="&#x7d;" k="18" />
<hkern u1="&#x108;" u2="]" k="18" />
<hkern u1="&#x108;" u2="X" k="5" />
<hkern u1="&#x108;" u2="Y" k="13" />
<hkern u1="&#x108;" u2="W" k="6" />
<hkern u1="&#x108;" u2="V" k="10" />
<hkern u1="&#x108;" u2="A" k="6" />
<hkern u1="&#x109;" u2="&#x29;" k="30" />
<hkern u1="&#x109;" u2="&#x2122;" k="20" />
<hkern u1="&#x109;" u2="&#x7d;" k="30" />
<hkern u1="&#x109;" u2="&#x2018;" k="12" />
<hkern u1="&#x109;" u2="&#x2019;" k="14" />
<hkern u1="&#x109;" u2="\" k="24" />
<hkern u1="&#x109;" u2="]" k="30" />
<hkern u1="&#x109;" u2="X" k="6" />
<hkern u1="&#x109;" u2="Y" k="71" />
<hkern u1="&#x109;" u2="W" k="30" />
<hkern u1="&#x109;" u2="V" k="44" />
<hkern u1="&#x109;" u2="T" k="84" />
<hkern u1="&#x109;" u2="S" k="5" />
<hkern u1="&#x109;" u2="A" k="5" />
<hkern u1="&#x109;" u2="x" k="8" />
<hkern u1="&#x109;" u2="y" k="7" />
<hkern u1="&#x109;" u2="v" k="5" />
<hkern u1="&#x10a;" g2="parenright.case" k="19" />
<hkern u1="&#x10a;" g2="braceright.case" k="23" />
<hkern u1="&#x10a;" g2="bracketright.case" k="23" />
<hkern u1="&#x10a;" u2="&#x29;" k="17" />
<hkern u1="&#x10a;" u2="&#x7d;" k="18" />
<hkern u1="&#x10a;" u2="]" k="18" />
<hkern u1="&#x10a;" u2="X" k="5" />
<hkern u1="&#x10a;" u2="Y" k="13" />
<hkern u1="&#x10a;" u2="W" k="6" />
<hkern u1="&#x10a;" u2="V" k="10" />
<hkern u1="&#x10a;" u2="A" k="6" />
<hkern u1="&#x10b;" u2="&#x29;" k="30" />
<hkern u1="&#x10b;" u2="&#x2122;" k="20" />
<hkern u1="&#x10b;" u2="&#x7d;" k="30" />
<hkern u1="&#x10b;" u2="&#x2018;" k="12" />
<hkern u1="&#x10b;" u2="&#x2019;" k="14" />
<hkern u1="&#x10b;" u2="\" k="24" />
<hkern u1="&#x10b;" u2="]" k="30" />
<hkern u1="&#x10b;" u2="X" k="6" />
<hkern u1="&#x10b;" u2="Y" k="71" />
<hkern u1="&#x10b;" u2="W" k="30" />
<hkern u1="&#x10b;" u2="V" k="44" />
<hkern u1="&#x10b;" u2="T" k="84" />
<hkern u1="&#x10b;" u2="S" k="5" />
<hkern u1="&#x10b;" u2="A" k="5" />
<hkern u1="&#x10b;" u2="x" k="8" />
<hkern u1="&#x10b;" u2="y" k="7" />
<hkern u1="&#x10b;" u2="v" k="5" />
<hkern u1="&#x10c;" g2="parenright.case" k="19" />
<hkern u1="&#x10c;" g2="braceright.case" k="23" />
<hkern u1="&#x10c;" g2="bracketright.case" k="23" />
<hkern u1="&#x10c;" u2="&#x29;" k="17" />
<hkern u1="&#x10c;" u2="&#x7d;" k="18" />
<hkern u1="&#x10c;" u2="]" k="18" />
<hkern u1="&#x10c;" u2="X" k="5" />
<hkern u1="&#x10c;" u2="Y" k="13" />
<hkern u1="&#x10c;" u2="W" k="6" />
<hkern u1="&#x10c;" u2="V" k="10" />
<hkern u1="&#x10c;" u2="A" k="6" />
<hkern u1="&#x10d;" u2="&#x29;" k="30" />
<hkern u1="&#x10d;" u2="&#x2122;" k="20" />
<hkern u1="&#x10d;" u2="&#x7d;" k="30" />
<hkern u1="&#x10d;" u2="&#x2018;" k="12" />
<hkern u1="&#x10d;" u2="&#x2019;" k="14" />
<hkern u1="&#x10d;" u2="\" k="24" />
<hkern u1="&#x10d;" u2="]" k="30" />
<hkern u1="&#x10d;" u2="X" k="6" />
<hkern u1="&#x10d;" u2="Y" k="71" />
<hkern u1="&#x10d;" u2="W" k="30" />
<hkern u1="&#x10d;" u2="V" k="44" />
<hkern u1="&#x10d;" u2="T" k="84" />
<hkern u1="&#x10d;" u2="S" k="5" />
<hkern u1="&#x10d;" u2="A" k="5" />
<hkern u1="&#x10d;" u2="x" k="8" />
<hkern u1="&#x10d;" u2="y" k="7" />
<hkern u1="&#x10d;" u2="v" k="5" />
<hkern u1="&#x10e;" u2="&#x166;" k="7" />
<hkern u1="&#x10e;" g2="parenright.case" k="27" />
<hkern u1="&#x10e;" g2="braceright.case" k="29" />
<hkern u1="&#x10e;" g2="bracketright.case" k="29" />
<hkern u1="&#x10e;" u2="&#x29;" k="25" />
<hkern u1="&#x10e;" g2="seven.plf" k="13" />
<hkern u1="&#x10e;" u2="&#xc6;" k="8" />
<hkern u1="&#x10e;" u2="&#x7d;" k="25" />
<hkern u1="&#x10e;" u2="]" k="25" />
<hkern u1="&#x10e;" u2="X" k="22" />
<hkern u1="&#x10e;" u2="Y" k="25" />
<hkern u1="&#x10e;" u2="Z" k="8" />
<hkern u1="&#x10e;" u2="W" k="13" />
<hkern u1="&#x10e;" u2="V" k="18" />
<hkern u1="&#x10e;" u2="J" k="17" />
<hkern u1="&#x10e;" u2="T" k="16" />
<hkern u1="&#x10e;" u2="A" k="15" />
<hkern u1="&#x10e;" u2="&#x2e;" k="9" />
<hkern u1="&#x10e;" u2="x" k="12" />
<hkern u1="&#x113;" u2="&#x29;" k="31" />
<hkern u1="&#x113;" u2="f" k="4" />
<hkern u1="&#x113;" u2="&#x2122;" k="20" />
<hkern u1="&#x113;" u2="&#x7d;" k="32" />
<hkern u1="&#x113;" u2="&#x2018;" k="16" />
<hkern u1="&#x113;" u2="&#x2019;" k="18" />
<hkern u1="&#x113;" u2="\" k="28" />
<hkern u1="&#x113;" u2="]" k="31" />
<hkern u1="&#x113;" u2="X" k="10" />
<hkern u1="&#x113;" u2="U" k="5" />
<hkern u1="&#x113;" u2="Y" k="83" />
<hkern u1="&#x113;" u2="Z" k="8" />
<hkern u1="&#x113;" u2="W" k="37" />
<hkern u1="&#x113;" u2="V" k="42" />
<hkern u1="&#x113;" u2="T" k="74" />
<hkern u1="&#x113;" u2="A" k="7" />
<hkern u1="&#x113;" u2="x" k="14" />
<hkern u1="&#x113;" u2="y" k="10" />
<hkern u1="&#x113;" u2="w" k="5" />
<hkern u1="&#x113;" u2="v" k="8" />
<hkern u1="&#x12a;" u2="d" k="6" />
<hkern u1="&#x12a;" u2="&#xf0;" k="7" />
<hkern u1="&#x12a;" u2="o" k="6" />
<hkern u1="&#x12b;" u2="&#x2122;" k="-6" />
<hkern u1="&#x12b;" u2="&#x7d;" k="-8" />
<hkern u1="&#x12b;" u2="\" k="-27" />
<hkern u1="&#x12b;" u2="]" k="-8" />
<hkern u1="&#x12b;" u2="Z" k="6" />
<hkern u1="&#x16a;" u2="d" k="6" />
<hkern u1="&#x16a;" g2="parenright.case" k="13" />
<hkern u1="&#x16a;" g2="braceright.case" k="14" />
<hkern u1="&#x16a;" g2="bracketright.case" k="14" />
<hkern u1="&#x16a;" u2="&#xc6;" k="9" />
<hkern u1="&#x16a;" u2="&#xf0;" k="8" />
<hkern u1="&#x16a;" u2="s" k="6" />
<hkern u1="&#x16a;" u2="J" k="16" />
<hkern u1="&#x16a;" u2="A" k="13" />
<hkern u1="&#x16a;" u2="&#x2e;" k="10" />
<hkern u1="&#x16a;" u2="z" k="7" />
<hkern u1="&#x16a;" u2="u" k="7" />
<hkern u1="&#x16a;" u2="o" k="6" />
<hkern u1="&#x16a;" u2="a" k="6" />
<hkern u1="&#x16b;" u2="&#x29;" k="24" />
<hkern u1="&#x16b;" u2="&#x2122;" k="16" />
<hkern u1="&#x16b;" u2="&#x7d;" k="25" />
<hkern u1="&#x16b;" u2="\" k="15" />
<hkern u1="&#x16b;" u2="]" k="25" />
<hkern u1="&#x16b;" u2="Y" k="53" />
<hkern u1="&#x16b;" u2="Z" k="5" />
<hkern u1="&#x16b;" u2="W" k="26" />
<hkern u1="&#x16b;" u2="V" k="35" />
<hkern u1="&#x16b;" u2="T" k="59" />
<hkern u1="&#x115;" u2="&#x29;" k="31" />
<hkern u1="&#x115;" u2="f" k="4" />
<hkern u1="&#x115;" u2="&#x2122;" k="20" />
<hkern u1="&#x115;" u2="&#x7d;" k="32" />
<hkern u1="&#x115;" u2="&#x2018;" k="16" />
<hkern u1="&#x115;" u2="&#x2019;" k="18" />
<hkern u1="&#x115;" u2="\" k="28" />
<hkern u1="&#x115;" u2="]" k="31" />
<hkern u1="&#x115;" u2="X" k="10" />
<hkern u1="&#x115;" u2="U" k="5" />
<hkern u1="&#x115;" u2="Y" k="83" />
<hkern u1="&#x115;" u2="Z" k="8" />
<hkern u1="&#x115;" u2="W" k="37" />
<hkern u1="&#x115;" u2="V" k="42" />
<hkern u1="&#x115;" u2="T" k="74" />
<hkern u1="&#x115;" u2="A" k="7" />
<hkern u1="&#x115;" u2="x" k="14" />
<hkern u1="&#x115;" u2="y" k="10" />
<hkern u1="&#x115;" u2="w" k="5" />
<hkern u1="&#x115;" u2="v" k="8" />
<hkern u1="&#x11e;" u2="&#x29;" k="14" />
<hkern u1="&#x11e;" u2="&#x7d;" k="19" />
<hkern u1="&#x11e;" u2="]" k="19" />
<hkern u1="&#x11e;" u2="Y" k="23" />
<hkern u1="&#x11e;" u2="W" k="12" />
<hkern u1="&#x11e;" u2="V" k="18" />
<hkern u1="&#x11e;" u2="T" k="10" />
<hkern u1="&#x11f;" u2="&#x29;" k="24" />
<hkern u1="&#x11f;" u2="&#x2122;" k="16" />
<hkern u1="&#x11f;" u2="&#x7d;" k="25" />
<hkern u1="&#x11f;" u2="\" k="15" />
<hkern u1="&#x11f;" u2="]" k="25" />
<hkern u1="&#x11f;" u2="Y" k="53" />
<hkern u1="&#x11f;" u2="Z" k="5" />
<hkern u1="&#x11f;" u2="W" k="26" />
<hkern u1="&#x11f;" u2="V" k="35" />
<hkern u1="&#x11f;" u2="T" k="59" />
<hkern u1="&#x12c;" u2="d" k="6" />
<hkern u1="&#x12c;" u2="&#xf0;" k="7" />
<hkern u1="&#x12c;" u2="o" k="6" />
<hkern u1="&#x12d;" u2="\" k="-17" />
<hkern u1="&#x12d;" u2="Z" k="6" />
<hkern u1="&#x16c;" u2="d" k="6" />
<hkern u1="&#x16c;" g2="parenright.case" k="13" />
<hkern u1="&#x16c;" g2="braceright.case" k="14" />
<hkern u1="&#x16c;" g2="bracketright.case" k="14" />
<hkern u1="&#x16c;" u2="&#xc6;" k="9" />
<hkern u1="&#x16c;" u2="&#xf0;" k="8" />
<hkern u1="&#x16c;" u2="s" k="6" />
<hkern u1="&#x16c;" u2="J" k="16" />
<hkern u1="&#x16c;" u2="A" k="13" />
<hkern u1="&#x16c;" u2="&#x2e;" k="10" />
<hkern u1="&#x16c;" u2="z" k="7" />
<hkern u1="&#x16c;" u2="u" k="7" />
<hkern u1="&#x16c;" u2="o" k="6" />
<hkern u1="&#x16c;" u2="a" k="6" />
<hkern u1="&#x16d;" u2="&#x29;" k="24" />
<hkern u1="&#x16d;" u2="&#x2122;" k="16" />
<hkern u1="&#x16d;" u2="&#x7d;" k="25" />
<hkern u1="&#x16d;" u2="\" k="15" />
<hkern u1="&#x16d;" u2="]" k="25" />
<hkern u1="&#x16d;" u2="Y" k="53" />
<hkern u1="&#x16d;" u2="Z" k="5" />
<hkern u1="&#x16d;" u2="W" k="26" />
<hkern u1="&#x16d;" u2="V" k="35" />
<hkern u1="&#x16d;" u2="T" k="59" />
<hkern u1="&#x117;" u2="&#x29;" k="31" />
<hkern u1="&#x117;" u2="f" k="4" />
<hkern u1="&#x117;" u2="&#x2122;" k="20" />
<hkern u1="&#x117;" u2="&#x7d;" k="32" />
<hkern u1="&#x117;" u2="&#x2018;" k="16" />
<hkern u1="&#x117;" u2="&#x2019;" k="18" />
<hkern u1="&#x117;" u2="\" k="28" />
<hkern u1="&#x117;" u2="]" k="31" />
<hkern u1="&#x117;" u2="X" k="10" />
<hkern u1="&#x117;" u2="U" k="5" />
<hkern u1="&#x117;" u2="Y" k="83" />
<hkern u1="&#x117;" u2="Z" k="8" />
<hkern u1="&#x117;" u2="W" k="37" />
<hkern u1="&#x117;" u2="V" k="42" />
<hkern u1="&#x117;" u2="T" k="74" />
<hkern u1="&#x117;" u2="A" k="7" />
<hkern u1="&#x117;" u2="x" k="14" />
<hkern u1="&#x117;" u2="y" k="10" />
<hkern u1="&#x117;" u2="w" k="5" />
<hkern u1="&#x117;" u2="v" k="8" />
<hkern u1="&#x11b;" u2="&#x29;" k="31" />
<hkern u1="&#x11b;" u2="f" k="4" />
<hkern u1="&#x11b;" u2="&#x2122;" k="20" />
<hkern u1="&#x11b;" u2="&#x7d;" k="32" />
<hkern u1="&#x11b;" u2="&#x2018;" k="16" />
<hkern u1="&#x11b;" u2="&#x2019;" k="18" />
<hkern u1="&#x11b;" u2="\" k="28" />
<hkern u1="&#x11b;" u2="]" k="31" />
<hkern u1="&#x11b;" u2="X" k="10" />
<hkern u1="&#x11b;" u2="U" k="5" />
<hkern u1="&#x11b;" u2="Y" k="83" />
<hkern u1="&#x11b;" u2="Z" k="8" />
<hkern u1="&#x11b;" u2="W" k="37" />
<hkern u1="&#x11b;" u2="V" k="42" />
<hkern u1="&#x11b;" u2="T" k="74" />
<hkern u1="&#x11b;" u2="A" k="7" />
<hkern u1="&#x11b;" u2="x" k="14" />
<hkern u1="&#x11b;" u2="y" k="10" />
<hkern u1="&#x11b;" u2="w" k="5" />
<hkern u1="&#x11b;" u2="v" k="8" />
<hkern u1="&#x11c;" u2="&#x29;" k="14" />
<hkern u1="&#x11c;" u2="&#x7d;" k="19" />
<hkern u1="&#x11c;" u2="]" k="19" />
<hkern u1="&#x11c;" u2="Y" k="23" />
<hkern u1="&#x11c;" u2="W" k="12" />
<hkern u1="&#x11c;" u2="V" k="18" />
<hkern u1="&#x11c;" u2="T" k="10" />
<hkern u1="&#x11d;" u2="&#x29;" k="24" />
<hkern u1="&#x11d;" u2="&#x2122;" k="16" />
<hkern u1="&#x11d;" u2="&#x7d;" k="25" />
<hkern u1="&#x11d;" u2="\" k="15" />
<hkern u1="&#x11d;" u2="]" k="25" />
<hkern u1="&#x11d;" u2="Y" k="53" />
<hkern u1="&#x11d;" u2="Z" k="5" />
<hkern u1="&#x11d;" u2="W" k="26" />
<hkern u1="&#x11d;" u2="V" k="35" />
<hkern u1="&#x11d;" u2="T" k="59" />
<hkern u1="&#x121;" u2="&#x29;" k="24" />
<hkern u1="&#x121;" u2="&#x2122;" k="16" />
<hkern u1="&#x121;" u2="&#x7d;" k="25" />
<hkern u1="&#x121;" u2="\" k="15" />
<hkern u1="&#x121;" u2="]" k="25" />
<hkern u1="&#x121;" u2="Y" k="53" />
<hkern u1="&#x121;" u2="Z" k="5" />
<hkern u1="&#x121;" u2="W" k="26" />
<hkern u1="&#x121;" u2="V" k="35" />
<hkern u1="&#x121;" u2="T" k="59" />
<hkern u1="&#x124;" u2="d" k="6" />
<hkern u1="&#x124;" u2="&#xf0;" k="7" />
<hkern u1="&#x124;" u2="o" k="6" />
<hkern u1="&#x125;" u2="&#x29;" k="26" />
<hkern u1="&#x125;" u2="&#x2122;" k="20" />
<hkern u1="&#x125;" u2="&#x7d;" k="31" />
<hkern u1="&#x125;" u2="&#x2018;" k="13" />
<hkern u1="&#x125;" u2="&#x2019;" k="14" />
<hkern u1="&#x125;" u2="\" k="27" />
<hkern u1="&#x125;" u2="]" k="31" />
<hkern u1="&#x125;" u2="U" k="5" />
<hkern u1="&#x125;" u2="Y" k="61" />
<hkern u1="&#x125;" u2="Z" k="5" />
<hkern u1="&#x125;" u2="W" k="29" />
<hkern u1="&#x125;" u2="V" k="41" />
<hkern u1="&#x125;" u2="T" k="80" />
<hkern u1="&#x125;" u2="y" k="6" />
<hkern u1="&#x125;" u2="v" k="5" />
<hkern u1="&#x123;" u2="&#x29;" k="24" />
<hkern u1="&#x123;" u2="&#x2122;" k="16" />
<hkern u1="&#x123;" u2="&#x7d;" k="25" />
<hkern u1="&#x123;" u2="\" k="15" />
<hkern u1="&#x123;" u2="]" k="25" />
<hkern u1="&#x123;" u2="Y" k="53" />
<hkern u1="&#x123;" u2="Z" k="5" />
<hkern u1="&#x123;" u2="W" k="26" />
<hkern u1="&#x123;" u2="V" k="35" />
<hkern u1="&#x123;" u2="T" k="59" />
<hkern u1="&#x128;" u2="d" k="6" />
<hkern u1="&#x128;" u2="&#xf0;" k="7" />
<hkern u1="&#x128;" u2="o" k="6" />
<hkern u1="&#x129;" u2="&#x2122;" k="-7" />
<hkern u1="&#x129;" u2="&#x7d;" k="-12" />
<hkern u1="&#x129;" u2="\" k="-33" />
<hkern u1="&#x129;" u2="]" k="-12" />
<hkern u1="&#x129;" u2="Z" k="6" />
<hkern u1="&#x130;" u2="d" k="6" />
<hkern u1="&#x130;" u2="&#xf0;" k="7" />
<hkern u1="&#x130;" u2="o" k="6" />
<hkern u1="&#x133;" u2="Z" k="6" />
<hkern u1="&#x132;" u2="d" k="6" />
<hkern u1="&#x132;" u2="&#xf0;" k="7" />
<hkern u1="&#x132;" u2="o" k="6" />
<hkern u1="&#x134;" u2="d" k="6" />
<hkern u1="&#x134;" u2="&#xf0;" k="7" />
<hkern u1="&#x134;" u2="o" k="6" />
<hkern u1="&#x135;" u2="Z" k="6" />
<hkern u1="&#x143;" u2="d" k="6" />
<hkern u1="&#x143;" u2="&#xf0;" k="7" />
<hkern u1="&#x143;" u2="o" k="6" />
<hkern u1="&#x144;" u2="&#x29;" k="26" />
<hkern u1="&#x144;" u2="&#x2122;" k="20" />
<hkern u1="&#x144;" u2="&#x7d;" k="31" />
<hkern u1="&#x144;" u2="&#x2018;" k="13" />
<hkern u1="&#x144;" u2="&#x2019;" k="14" />
<hkern u1="&#x144;" u2="\" k="27" />
<hkern u1="&#x144;" u2="]" k="31" />
<hkern u1="&#x144;" u2="U" k="5" />
<hkern u1="&#x144;" u2="Y" k="61" />
<hkern u1="&#x144;" u2="Z" k="5" />
<hkern u1="&#x144;" u2="W" k="29" />
<hkern u1="&#x144;" u2="V" k="41" />
<hkern u1="&#x144;" u2="T" k="80" />
<hkern u1="&#x144;" u2="y" k="6" />
<hkern u1="&#x144;" u2="v" k="5" />
<hkern u1="&#x147;" u2="d" k="6" />
<hkern u1="&#x147;" u2="&#xf0;" k="7" />
<hkern u1="&#x147;" u2="o" k="6" />
<hkern u1="&#x148;" u2="&#x29;" k="26" />
<hkern u1="&#x148;" u2="&#x2122;" k="20" />
<hkern u1="&#x148;" u2="&#x7d;" k="31" />
<hkern u1="&#x148;" u2="&#x2018;" k="13" />
<hkern u1="&#x148;" u2="&#x2019;" k="14" />
<hkern u1="&#x148;" u2="\" k="27" />
<hkern u1="&#x148;" u2="]" k="31" />
<hkern u1="&#x148;" u2="U" k="5" />
<hkern u1="&#x148;" u2="Y" k="61" />
<hkern u1="&#x148;" u2="Z" k="5" />
<hkern u1="&#x148;" u2="W" k="29" />
<hkern u1="&#x148;" u2="V" k="41" />
<hkern u1="&#x148;" u2="T" k="80" />
<hkern u1="&#x148;" u2="y" k="6" />
<hkern u1="&#x148;" u2="v" k="5" />
<hkern u1="&#x155;" u2="d" k="11" />
<hkern u1="&#x155;" u2="&#x29;" k="19" />
<hkern u1="&#x155;" u2="&#x2039;" k="24" />
<hkern u1="&#x155;" u2="&#xc6;" k="44" />
<hkern u1="&#x155;" u2="&#xf0;" k="27" />
<hkern u1="&#x155;" u2="&#x7d;" k="13" />
<hkern u1="&#x155;" u2="]" k="13" />
<hkern u1="&#x155;" u2="&#x2f;" k="26" />
<hkern u1="&#x155;" u2="&#x2d;" k="27" />
<hkern u1="&#x155;" u2="X" k="21" />
<hkern u1="&#x155;" u2="Y" k="15" />
<hkern u1="&#x155;" u2="Z" k="11" />
<hkern u1="&#x155;" u2="J" k="55" />
<hkern u1="&#x155;" u2="T" k="45" />
<hkern u1="&#x155;" u2="A" k="39" />
<hkern u1="&#x155;" u2="&#x2e;" k="33" />
<hkern u1="&#x155;" u2="o" k="11" />
<hkern u1="&#x155;" u2="a" k="4" />
<hkern u1="&#x155;" u2="&#x20;" k="21" />
<hkern u1="&#x154;" u2="d" k="11" />
<hkern u1="&#x154;" g2="four.plf" k="12" />
<hkern u1="&#x154;" u2="&#x2039;" k="15" />
<hkern u1="&#x154;" u2="&#xf0;" k="17" />
<hkern u1="&#x154;" u2="&#x7d;" k="12" />
<hkern u1="&#x154;" u2="s" k="5" />
<hkern u1="&#x154;" u2="]" k="12" />
<hkern u1="&#x154;" u2="Y" k="12" />
<hkern u1="&#x154;" u2="W" k="8" />
<hkern u1="&#x154;" u2="V" k="12" />
<hkern u1="&#x154;" u2="A" k="6" />
<hkern u1="&#x154;" u2="u" k="5" />
<hkern u1="&#x154;" u2="o" k="12" />
<hkern u1="&#x154;" u2="a" k="7" />
<hkern u1="&#x158;" u2="d" k="11" />
<hkern u1="&#x158;" g2="four.plf" k="12" />
<hkern u1="&#x158;" u2="&#x2039;" k="15" />
<hkern u1="&#x158;" u2="&#xf0;" k="17" />
<hkern u1="&#x158;" u2="&#x7d;" k="12" />
<hkern u1="&#x158;" u2="s" k="5" />
<hkern u1="&#x158;" u2="]" k="12" />
<hkern u1="&#x158;" u2="Y" k="12" />
<hkern u1="&#x158;" u2="W" k="8" />
<hkern u1="&#x158;" u2="V" k="12" />
<hkern u1="&#x158;" u2="A" k="6" />
<hkern u1="&#x158;" u2="u" k="5" />
<hkern u1="&#x158;" u2="o" k="12" />
<hkern u1="&#x158;" u2="a" k="7" />
<hkern u1="&#x159;" u2="d" k="11" />
<hkern u1="&#x159;" u2="&#x29;" k="19" />
<hkern u1="&#x159;" u2="&#x2039;" k="24" />
<hkern u1="&#x159;" u2="&#xc6;" k="44" />
<hkern u1="&#x159;" u2="&#xf0;" k="27" />
<hkern u1="&#x159;" u2="&#x7d;" k="13" />
<hkern u1="&#x159;" u2="]" k="13" />
<hkern u1="&#x159;" u2="&#x2f;" k="26" />
<hkern u1="&#x159;" u2="&#x2d;" k="27" />
<hkern u1="&#x159;" u2="X" k="21" />
<hkern u1="&#x159;" u2="Y" k="15" />
<hkern u1="&#x159;" u2="Z" k="11" />
<hkern u1="&#x159;" u2="J" k="55" />
<hkern u1="&#x159;" u2="T" k="45" />
<hkern u1="&#x159;" u2="A" k="39" />
<hkern u1="&#x159;" u2="&#x2e;" k="33" />
<hkern u1="&#x159;" u2="o" k="11" />
<hkern u1="&#x159;" u2="a" k="4" />
<hkern u1="&#x159;" u2="&#x20;" k="21" />
<hkern u1="&#x15a;" g2="parenright.case" k="19" />
<hkern u1="&#x15a;" g2="braceright.case" k="23" />
<hkern u1="&#x15a;" g2="bracketright.case" k="23" />
<hkern u1="&#x15a;" u2="&#x29;" k="18" />
<hkern u1="&#x15a;" u2="&#x7d;" k="18" />
<hkern u1="&#x15a;" u2="]" k="18" />
<hkern u1="&#x15a;" u2="X" k="6" />
<hkern u1="&#x15a;" u2="Y" k="18" />
<hkern u1="&#x15a;" u2="W" k="10" />
<hkern u1="&#x15a;" u2="V" k="15" />
<hkern u1="&#x15a;" u2="T" k="5" />
<hkern u1="&#x15a;" u2="A" k="9" />
<hkern u1="&#x15a;" u2="x" k="8" />
<hkern u1="&#x15b;" u2="&#x29;" k="28" />
<hkern u1="&#x15b;" u2="&#x2122;" k="22" />
<hkern u1="&#x15b;" u2="&#x7d;" k="30" />
<hkern u1="&#x15b;" u2="&#x2018;" k="13" />
<hkern u1="&#x15b;" u2="&#x2019;" k="15" />
<hkern u1="&#x15b;" u2="\" k="27" />
<hkern u1="&#x15b;" u2="]" k="30" />
<hkern u1="&#x15b;" u2="U" k="6" />
<hkern u1="&#x15b;" u2="Y" k="77" />
<hkern u1="&#x15b;" u2="W" k="37" />
<hkern u1="&#x15b;" u2="V" k="42" />
<hkern u1="&#x15b;" u2="T" k="72" />
<hkern u1="&#x15b;" u2="A" k="5" />
<hkern u1="&#x15b;" u2="x" k="6" />
<hkern u1="&#x15b;" u2="y" k="9" />
<hkern u1="&#x15b;" u2="w" k="5" />
<hkern u1="&#x15b;" u2="v" k="8" />
<hkern u1="&#x15c;" g2="parenright.case" k="19" />
<hkern u1="&#x15c;" g2="braceright.case" k="23" />
<hkern u1="&#x15c;" g2="bracketright.case" k="23" />
<hkern u1="&#x15c;" u2="&#x29;" k="18" />
<hkern u1="&#x15c;" u2="&#x7d;" k="18" />
<hkern u1="&#x15c;" u2="]" k="18" />
<hkern u1="&#x15c;" u2="X" k="6" />
<hkern u1="&#x15c;" u2="Y" k="18" />
<hkern u1="&#x15c;" u2="W" k="10" />
<hkern u1="&#x15c;" u2="V" k="15" />
<hkern u1="&#x15c;" u2="T" k="5" />
<hkern u1="&#x15c;" u2="A" k="9" />
<hkern u1="&#x15c;" u2="x" k="8" />
<hkern u1="&#x15d;" u2="&#x29;" k="28" />
<hkern u1="&#x15d;" u2="&#x2122;" k="22" />
<hkern u1="&#x15d;" u2="&#x7d;" k="30" />
<hkern u1="&#x15d;" u2="&#x2018;" k="13" />
<hkern u1="&#x15d;" u2="&#x2019;" k="15" />
<hkern u1="&#x15d;" u2="\" k="27" />
<hkern u1="&#x15d;" u2="]" k="30" />
<hkern u1="&#x15d;" u2="U" k="6" />
<hkern u1="&#x15d;" u2="Y" k="77" />
<hkern u1="&#x15d;" u2="W" k="37" />
<hkern u1="&#x15d;" u2="V" k="42" />
<hkern u1="&#x15d;" u2="T" k="72" />
<hkern u1="&#x15d;" u2="A" k="5" />
<hkern u1="&#x15d;" u2="x" k="6" />
<hkern u1="&#x15d;" u2="y" k="9" />
<hkern u1="&#x15d;" u2="w" k="5" />
<hkern u1="&#x15d;" u2="v" k="8" />
<hkern u1="&#x164;" u2="d" k="79" />
<hkern u1="&#x164;" u2="&#x144;" k="72" />
<hkern u1="&#x164;" u2="&#x129;" k="-27" />
<hkern u1="&#x164;" u2="&#x12d;" k="-7" />
<hkern u1="&#x164;" u2="&#x12b;" k="-25" />
<hkern u1="&#x164;" g2="hyphen.case" k="38" />
<hkern u1="&#x164;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x164;" g2="guilsinglright.case" k="39" />
<hkern u1="&#x164;" g2="four.plf" k="46" />
<hkern u1="&#x164;" u2="n" k="59" />
<hkern u1="&#x164;" u2="f" k="16" />
<hkern u1="&#x164;" u2="&#x203a;" k="55" />
<hkern u1="&#x164;" u2="&#x2039;" k="61" />
<hkern u1="&#x164;" u2="&#xdf;" k="19" />
<hkern u1="&#x164;" u2="&#xc6;" k="56" />
<hkern u1="&#x164;" u2="&#xf0;" k="58" />
<hkern u1="&#x164;" u2="&#x131;" k="59" />
<hkern u1="&#x164;" u2="&#xae;" k="19" />
<hkern u1="&#x164;" u2="s" k="71" />
<hkern u1="&#x164;" u2="&#xa9;" k="19" />
<hkern u1="&#x164;" u2="&#xfa;" k="75" />
<hkern u1="&#x164;" u2="&#x2f;" k="43" />
<hkern u1="&#x164;" u2="&#x3a;" k="30" />
<hkern u1="&#x164;" u2="&#x2d;" k="39" />
<hkern u1="&#x164;" u2="J" k="51" />
<hkern u1="&#x164;" u2="O" k="15" />
<hkern u1="&#x164;" u2="A" k="52" />
<hkern u1="&#x164;" u2="&#x2e;" k="47" />
<hkern u1="&#x164;" u2="x" k="64" />
<hkern u1="&#x164;" u2="y" k="50" />
<hkern u1="&#x164;" u2="z" k="75" />
<hkern u1="&#x164;" u2="w" k="52" />
<hkern u1="&#x164;" u2="v" k="50" />
<hkern u1="&#x164;" u2="u" k="53" />
<hkern u1="&#x164;" u2="o" k="78" />
<hkern u1="&#x164;" u2="a" k="73" />
<hkern u1="&#x164;" u2="&#x20;" k="27" />
<hkern u1="&#x168;" u2="d" k="6" />
<hkern u1="&#x168;" g2="parenright.case" k="13" />
<hkern u1="&#x168;" g2="braceright.case" k="14" />
<hkern u1="&#x168;" g2="bracketright.case" k="14" />
<hkern u1="&#x168;" u2="&#xc6;" k="9" />
<hkern u1="&#x168;" u2="&#xf0;" k="8" />
<hkern u1="&#x168;" u2="s" k="6" />
<hkern u1="&#x168;" u2="J" k="16" />
<hkern u1="&#x168;" u2="A" k="13" />
<hkern u1="&#x168;" u2="&#x2e;" k="10" />
<hkern u1="&#x168;" u2="z" k="7" />
<hkern u1="&#x168;" u2="u" k="7" />
<hkern u1="&#x168;" u2="o" k="6" />
<hkern u1="&#x168;" u2="a" k="6" />
<hkern u1="&#x169;" u2="&#x29;" k="24" />
<hkern u1="&#x169;" u2="&#x2122;" k="16" />
<hkern u1="&#x169;" u2="&#x7d;" k="25" />
<hkern u1="&#x169;" u2="\" k="15" />
<hkern u1="&#x169;" u2="]" k="25" />
<hkern u1="&#x169;" u2="Y" k="53" />
<hkern u1="&#x169;" u2="Z" k="5" />
<hkern u1="&#x169;" u2="W" k="26" />
<hkern u1="&#x169;" u2="V" k="35" />
<hkern u1="&#x169;" u2="T" k="59" />
<hkern u1="&#x16e;" u2="d" k="6" />
<hkern u1="&#x16e;" g2="parenright.case" k="13" />
<hkern u1="&#x16e;" g2="braceright.case" k="14" />
<hkern u1="&#x16e;" g2="bracketright.case" k="14" />
<hkern u1="&#x16e;" u2="&#xc6;" k="9" />
<hkern u1="&#x16e;" u2="&#xf0;" k="8" />
<hkern u1="&#x16e;" u2="s" k="6" />
<hkern u1="&#x16e;" u2="J" k="16" />
<hkern u1="&#x16e;" u2="A" k="13" />
<hkern u1="&#x16e;" u2="&#x2e;" k="10" />
<hkern u1="&#x16e;" u2="z" k="7" />
<hkern u1="&#x16e;" u2="u" k="7" />
<hkern u1="&#x16e;" u2="o" k="6" />
<hkern u1="&#x16e;" u2="a" k="6" />
<hkern u1="&#x16f;" u2="&#x29;" k="24" />
<hkern u1="&#x16f;" u2="&#x2122;" k="16" />
<hkern u1="&#x16f;" u2="&#x7d;" k="25" />
<hkern u1="&#x16f;" u2="\" k="15" />
<hkern u1="&#x16f;" u2="]" k="25" />
<hkern u1="&#x16f;" u2="Y" k="53" />
<hkern u1="&#x16f;" u2="Z" k="5" />
<hkern u1="&#x16f;" u2="W" k="26" />
<hkern u1="&#x16f;" u2="V" k="35" />
<hkern u1="&#x16f;" u2="T" k="59" />
<hkern u1="&#x170;" u2="d" k="6" />
<hkern u1="&#x170;" g2="parenright.case" k="13" />
<hkern u1="&#x170;" g2="braceright.case" k="14" />
<hkern u1="&#x170;" g2="bracketright.case" k="14" />
<hkern u1="&#x170;" u2="&#xc6;" k="9" />
<hkern u1="&#x170;" u2="&#xf0;" k="8" />
<hkern u1="&#x170;" u2="s" k="6" />
<hkern u1="&#x170;" u2="J" k="16" />
<hkern u1="&#x170;" u2="A" k="13" />
<hkern u1="&#x170;" u2="&#x2e;" k="10" />
<hkern u1="&#x170;" u2="z" k="7" />
<hkern u1="&#x170;" u2="u" k="7" />
<hkern u1="&#x170;" u2="o" k="6" />
<hkern u1="&#x170;" u2="a" k="6" />
<hkern u1="&#x171;" u2="&#x29;" k="24" />
<hkern u1="&#x171;" u2="&#x2122;" k="16" />
<hkern u1="&#x171;" u2="&#x7d;" k="25" />
<hkern u1="&#x171;" u2="\" k="15" />
<hkern u1="&#x171;" u2="]" k="25" />
<hkern u1="&#x171;" u2="Y" k="53" />
<hkern u1="&#x171;" u2="Z" k="5" />
<hkern u1="&#x171;" u2="W" k="26" />
<hkern u1="&#x171;" u2="V" k="35" />
<hkern u1="&#x171;" u2="T" k="59" />
<hkern u1="&#x174;" u2="d" k="35" />
<hkern u1="&#x174;" u2="&#x159;" k="17" />
<hkern u1="&#x174;" g2="guilsinglleft.case" k="23" />
<hkern u1="&#x174;" g2="eight.plf" k="11" />
<hkern u1="&#x174;" g2="six.plf" k="11" />
<hkern u1="&#x174;" g2="four.plf" k="22" />
<hkern u1="&#x174;" g2="zero.plf" k="11" />
<hkern u1="&#x174;" u2="n" k="26" />
<hkern u1="&#x174;" u2="&#x203a;" k="13" />
<hkern u1="&#x174;" u2="&#x2039;" k="30" />
<hkern u1="&#x174;" u2="&#xdf;" k="13" />
<hkern u1="&#x174;" u2="&#xc6;" k="41" />
<hkern u1="&#x174;" u2="&#xf0;" k="38" />
<hkern u1="&#x174;" u2="&#x131;" k="26" />
<hkern u1="&#x174;" u2="&#xae;" k="13" />
<hkern u1="&#x174;" u2="s" k="33" />
<hkern u1="&#x174;" u2="&#x40;" k="11" />
<hkern u1="&#x174;" u2="&#xa9;" k="13" />
<hkern u1="&#x174;" u2="&#x2f;" k="33" />
<hkern u1="&#x174;" u2="&#x2d;" k="12" />
<hkern u1="&#x174;" u2="&#x26;" k="14" />
<hkern u1="&#x174;" u2="J" k="47" />
<hkern u1="&#x174;" u2="S" k="10" />
<hkern u1="&#x174;" u2="O" k="12" />
<hkern u1="&#x174;" u2="A" k="32" />
<hkern u1="&#x174;" u2="&#x2e;" k="41" />
<hkern u1="&#x174;" u2="z" k="9" />
<hkern u1="&#x174;" u2="u" k="21" />
<hkern u1="&#x174;" u2="o" k="36" />
<hkern u1="&#x174;" u2="a" k="37" />
<hkern u1="&#x174;" u2="&#x20;" k="23" />
<hkern u1="&#x175;" u2="d" k="7" />
<hkern u1="&#x175;" u2="&#x29;" k="23" />
<hkern u1="&#x175;" u2="&#x2039;" k="13" />
<hkern u1="&#x175;" u2="&#xc6;" k="26" />
<hkern u1="&#x175;" u2="&#xf0;" k="10" />
<hkern u1="&#x175;" u2="&#x7d;" k="17" />
<hkern u1="&#x175;" u2="s" k="4" />
<hkern u1="&#x175;" u2="]" k="17" />
<hkern u1="&#x175;" u2="&#x2f;" k="18" />
<hkern u1="&#x175;" u2="X" k="20" />
<hkern u1="&#x175;" u2="Y" k="24" />
<hkern u1="&#x175;" u2="Z" k="12" />
<hkern u1="&#x175;" u2="J" k="40" />
<hkern u1="&#x175;" u2="T" k="52" />
<hkern u1="&#x175;" u2="A" k="20" />
<hkern u1="&#x175;" u2="&#x2e;" k="26" />
<hkern u1="&#x175;" u2="o" k="6" />
<hkern u1="&#x175;" u2="a" k="5" />
<hkern u1="&#x175;" u2="&#x20;" k="20" />
<hkern u1="&#x177;" u2="d" k="9" />
<hkern u1="&#x177;" u2="&#x29;" k="23" />
<hkern u1="&#x177;" u2="&#x2039;" k="19" />
<hkern u1="&#x177;" u2="&#xc6;" k="33" />
<hkern u1="&#x177;" u2="&#xf0;" k="15" />
<hkern u1="&#x177;" u2="&#x7d;" k="17" />
<hkern u1="&#x177;" u2="s" k="6" />
<hkern u1="&#x177;" u2="]" k="17" />
<hkern u1="&#x177;" u2="&#x2f;" k="26" />
<hkern u1="&#x177;" u2="X" k="24" />
<hkern u1="&#x177;" u2="Y" k="22" />
<hkern u1="&#x177;" u2="Z" k="14" />
<hkern u1="&#x177;" u2="J" k="61" />
<hkern u1="&#x177;" u2="T" k="49" />
<hkern u1="&#x177;" u2="A" k="26" />
<hkern u1="&#x177;" u2="&#x2e;" k="35" />
<hkern u1="&#x177;" u2="o" k="9" />
<hkern u1="&#x177;" u2="a" k="8" />
<hkern u1="&#x177;" u2="&#x20;" k="22" />
<hkern u1="&#x17a;" u2="d" k="5" />
<hkern u1="&#x17a;" u2="&#x29;" k="16" />
<hkern u1="&#x17a;" u2="&#x2039;" k="21" />
<hkern u1="&#x17a;" u2="&#x2122;" k="16" />
<hkern u1="&#x17a;" u2="&#xf0;" k="7" />
<hkern u1="&#x17a;" u2="&#x7d;" k="21" />
<hkern u1="&#x17a;" u2="]" k="21" />
<hkern u1="&#x17a;" u2="&#x2d;" k="14" />
<hkern u1="&#x17a;" u2="U" k="6" />
<hkern u1="&#x17a;" u2="Y" k="43" />
<hkern u1="&#x17a;" u2="W" k="10" />
<hkern u1="&#x17a;" u2="V" k="20" />
<hkern u1="&#x17a;" u2="T" k="76" />
<hkern u1="&#x17a;" u2="o" k="6" />
<hkern u1="&#x17c;" u2="d" k="5" />
<hkern u1="&#x17c;" u2="&#x29;" k="16" />
<hkern u1="&#x17c;" u2="&#x2039;" k="21" />
<hkern u1="&#x17c;" u2="&#x2122;" k="16" />
<hkern u1="&#x17c;" u2="&#xf0;" k="7" />
<hkern u1="&#x17c;" u2="&#x7d;" k="21" />
<hkern u1="&#x17c;" u2="]" k="21" />
<hkern u1="&#x17c;" u2="&#x2d;" k="14" />
<hkern u1="&#x17c;" u2="U" k="6" />
<hkern u1="&#x17c;" u2="Y" k="43" />
<hkern u1="&#x17c;" u2="W" k="10" />
<hkern u1="&#x17c;" u2="V" k="20" />
<hkern u1="&#x17c;" u2="T" k="76" />
<hkern u1="&#x17c;" u2="o" k="6" />
<hkern u1="&#x1e81;" u2="d" k="7" />
<hkern u1="&#x1e81;" u2="&#x29;" k="23" />
<hkern u1="&#x1e81;" u2="&#x2039;" k="13" />
<hkern u1="&#x1e81;" u2="&#xc6;" k="26" />
<hkern u1="&#x1e81;" u2="&#xf0;" k="10" />
<hkern u1="&#x1e81;" u2="&#x7d;" k="17" />
<hkern u1="&#x1e81;" u2="s" k="4" />
<hkern u1="&#x1e81;" u2="]" k="17" />
<hkern u1="&#x1e81;" u2="&#x2f;" k="18" />
<hkern u1="&#x1e81;" u2="X" k="20" />
<hkern u1="&#x1e81;" u2="Y" k="24" />
<hkern u1="&#x1e81;" u2="Z" k="12" />
<hkern u1="&#x1e81;" u2="J" k="40" />
<hkern u1="&#x1e81;" u2="T" k="52" />
<hkern u1="&#x1e81;" u2="A" k="20" />
<hkern u1="&#x1e81;" u2="&#x2e;" k="26" />
<hkern u1="&#x1e81;" u2="o" k="6" />
<hkern u1="&#x1e81;" u2="a" k="5" />
<hkern u1="&#x1e81;" u2="&#x20;" k="20" />
<hkern u1="&#x1e83;" u2="d" k="7" />
<hkern u1="&#x1e83;" u2="&#x29;" k="23" />
<hkern u1="&#x1e83;" u2="&#x2039;" k="13" />
<hkern u1="&#x1e83;" u2="&#xc6;" k="26" />
<hkern u1="&#x1e83;" u2="&#xf0;" k="10" />
<hkern u1="&#x1e83;" u2="&#x7d;" k="17" />
<hkern u1="&#x1e83;" u2="s" k="4" />
<hkern u1="&#x1e83;" u2="]" k="17" />
<hkern u1="&#x1e83;" u2="&#x2f;" k="18" />
<hkern u1="&#x1e83;" u2="X" k="20" />
<hkern u1="&#x1e83;" u2="Y" k="24" />
<hkern u1="&#x1e83;" u2="Z" k="12" />
<hkern u1="&#x1e83;" u2="J" k="40" />
<hkern u1="&#x1e83;" u2="T" k="52" />
<hkern u1="&#x1e83;" u2="A" k="20" />
<hkern u1="&#x1e83;" u2="&#x2e;" k="26" />
<hkern u1="&#x1e83;" u2="o" k="6" />
<hkern u1="&#x1e83;" u2="a" k="5" />
<hkern u1="&#x1e83;" u2="&#x20;" k="20" />
<hkern u1="&#x1e85;" u2="d" k="7" />
<hkern u1="&#x1e85;" u2="&#x29;" k="23" />
<hkern u1="&#x1e85;" u2="&#x2039;" k="13" />
<hkern u1="&#x1e85;" u2="&#xc6;" k="26" />
<hkern u1="&#x1e85;" u2="&#xf0;" k="10" />
<hkern u1="&#x1e85;" u2="&#x7d;" k="17" />
<hkern u1="&#x1e85;" u2="s" k="4" />
<hkern u1="&#x1e85;" u2="]" k="17" />
<hkern u1="&#x1e85;" u2="&#x2f;" k="18" />
<hkern u1="&#x1e85;" u2="X" k="20" />
<hkern u1="&#x1e85;" u2="Y" k="24" />
<hkern u1="&#x1e85;" u2="Z" k="12" />
<hkern u1="&#x1e85;" u2="J" k="40" />
<hkern u1="&#x1e85;" u2="T" k="52" />
<hkern u1="&#x1e85;" u2="A" k="20" />
<hkern u1="&#x1e85;" u2="&#x2e;" k="26" />
<hkern u1="&#x1e85;" u2="o" k="6" />
<hkern u1="&#x1e85;" u2="a" k="5" />
<hkern u1="&#x1e85;" u2="&#x20;" k="20" />
<hkern u1="&#x1ef3;" u2="d" k="9" />
<hkern u1="&#x1ef3;" u2="&#x29;" k="23" />
<hkern u1="&#x1ef3;" u2="&#x2039;" k="19" />
<hkern u1="&#x1ef3;" u2="&#xc6;" k="33" />
<hkern u1="&#x1ef3;" u2="&#xf0;" k="15" />
<hkern u1="&#x1ef3;" u2="&#x7d;" k="17" />
<hkern u1="&#x1ef3;" u2="s" k="6" />
<hkern u1="&#x1ef3;" u2="]" k="17" />
<hkern u1="&#x1ef3;" u2="&#x2f;" k="26" />
<hkern u1="&#x1ef3;" u2="X" k="24" />
<hkern u1="&#x1ef3;" u2="Y" k="22" />
<hkern u1="&#x1ef3;" u2="Z" k="14" />
<hkern u1="&#x1ef3;" u2="J" k="61" />
<hkern u1="&#x1ef3;" u2="T" k="49" />
<hkern u1="&#x1ef3;" u2="A" k="26" />
<hkern u1="&#x1ef3;" u2="&#x2e;" k="35" />
<hkern u1="&#x1ef3;" u2="o" k="9" />
<hkern u1="&#x1ef3;" u2="a" k="8" />
<hkern u1="&#x1ef3;" u2="&#x20;" k="22" />
<hkern u1="&#x1e80;" u2="d" k="35" />
<hkern u1="&#x1e80;" u2="&#x159;" k="17" />
<hkern u1="&#x1e80;" g2="guilsinglleft.case" k="23" />
<hkern u1="&#x1e80;" g2="eight.plf" k="11" />
<hkern u1="&#x1e80;" g2="six.plf" k="11" />
<hkern u1="&#x1e80;" g2="four.plf" k="22" />
<hkern u1="&#x1e80;" g2="zero.plf" k="11" />
<hkern u1="&#x1e80;" u2="n" k="26" />
<hkern u1="&#x1e80;" u2="&#x203a;" k="13" />
<hkern u1="&#x1e80;" u2="&#x2039;" k="30" />
<hkern u1="&#x1e80;" u2="&#xdf;" k="13" />
<hkern u1="&#x1e80;" u2="&#xc6;" k="41" />
<hkern u1="&#x1e80;" u2="&#xf0;" k="38" />
<hkern u1="&#x1e80;" u2="&#x131;" k="26" />
<hkern u1="&#x1e80;" u2="&#xae;" k="13" />
<hkern u1="&#x1e80;" u2="s" k="33" />
<hkern u1="&#x1e80;" u2="&#x40;" k="11" />
<hkern u1="&#x1e80;" u2="&#xa9;" k="13" />
<hkern u1="&#x1e80;" u2="&#x2f;" k="33" />
<hkern u1="&#x1e80;" u2="&#x2d;" k="12" />
<hkern u1="&#x1e80;" u2="&#x26;" k="14" />
<hkern u1="&#x1e80;" u2="J" k="47" />
<hkern u1="&#x1e80;" u2="S" k="10" />
<hkern u1="&#x1e80;" u2="O" k="12" />
<hkern u1="&#x1e80;" u2="A" k="32" />
<hkern u1="&#x1e80;" u2="&#x2e;" k="41" />
<hkern u1="&#x1e80;" u2="z" k="9" />
<hkern u1="&#x1e80;" u2="u" k="21" />
<hkern u1="&#x1e80;" u2="o" k="36" />
<hkern u1="&#x1e80;" u2="a" k="37" />
<hkern u1="&#x1e80;" u2="&#x20;" k="23" />
<hkern u1="&#x1e82;" u2="d" k="35" />
<hkern u1="&#x1e82;" u2="&#x159;" k="17" />
<hkern u1="&#x1e82;" g2="guilsinglleft.case" k="23" />
<hkern u1="&#x1e82;" g2="eight.plf" k="11" />
<hkern u1="&#x1e82;" g2="six.plf" k="11" />
<hkern u1="&#x1e82;" g2="four.plf" k="22" />
<hkern u1="&#x1e82;" g2="zero.plf" k="11" />
<hkern u1="&#x1e82;" u2="n" k="26" />
<hkern u1="&#x1e82;" u2="&#x203a;" k="13" />
<hkern u1="&#x1e82;" u2="&#x2039;" k="30" />
<hkern u1="&#x1e82;" u2="&#xdf;" k="13" />
<hkern u1="&#x1e82;" u2="&#xc6;" k="41" />
<hkern u1="&#x1e82;" u2="&#xf0;" k="38" />
<hkern u1="&#x1e82;" u2="&#x131;" k="26" />
<hkern u1="&#x1e82;" u2="&#xae;" k="13" />
<hkern u1="&#x1e82;" u2="s" k="33" />
<hkern u1="&#x1e82;" u2="&#x40;" k="11" />
<hkern u1="&#x1e82;" u2="&#xa9;" k="13" />
<hkern u1="&#x1e82;" u2="&#x2f;" k="33" />
<hkern u1="&#x1e82;" u2="&#x2d;" k="12" />
<hkern u1="&#x1e82;" u2="&#x26;" k="14" />
<hkern u1="&#x1e82;" u2="J" k="47" />
<hkern u1="&#x1e82;" u2="S" k="10" />
<hkern u1="&#x1e82;" u2="O" k="12" />
<hkern u1="&#x1e82;" u2="A" k="32" />
<hkern u1="&#x1e82;" u2="&#x2e;" k="41" />
<hkern u1="&#x1e82;" u2="z" k="9" />
<hkern u1="&#x1e82;" u2="u" k="21" />
<hkern u1="&#x1e82;" u2="o" k="36" />
<hkern u1="&#x1e82;" u2="a" k="37" />
<hkern u1="&#x1e82;" u2="&#x20;" k="23" />
<hkern u1="&#x1e84;" u2="d" k="35" />
<hkern u1="&#x1e84;" u2="&#x159;" k="17" />
<hkern u1="&#x1e84;" g2="guilsinglleft.case" k="23" />
<hkern u1="&#x1e84;" g2="eight.plf" k="11" />
<hkern u1="&#x1e84;" g2="six.plf" k="11" />
<hkern u1="&#x1e84;" g2="four.plf" k="22" />
<hkern u1="&#x1e84;" g2="zero.plf" k="11" />
<hkern u1="&#x1e84;" u2="n" k="26" />
<hkern u1="&#x1e84;" u2="&#x203a;" k="13" />
<hkern u1="&#x1e84;" u2="&#x2039;" k="30" />
<hkern u1="&#x1e84;" u2="&#xdf;" k="13" />
<hkern u1="&#x1e84;" u2="&#xc6;" k="41" />
<hkern u1="&#x1e84;" u2="&#xf0;" k="38" />
<hkern u1="&#x1e84;" u2="&#x131;" k="26" />
<hkern u1="&#x1e84;" u2="&#xae;" k="13" />
<hkern u1="&#x1e84;" u2="s" k="33" />
<hkern u1="&#x1e84;" u2="&#x40;" k="11" />
<hkern u1="&#x1e84;" u2="&#xa9;" k="13" />
<hkern u1="&#x1e84;" u2="&#x2f;" k="33" />
<hkern u1="&#x1e84;" u2="&#x2d;" k="12" />
<hkern u1="&#x1e84;" u2="&#x26;" k="14" />
<hkern u1="&#x1e84;" u2="J" k="47" />
<hkern u1="&#x1e84;" u2="S" k="10" />
<hkern u1="&#x1e84;" u2="O" k="12" />
<hkern u1="&#x1e84;" u2="A" k="32" />
<hkern u1="&#x1e84;" u2="&#x2e;" k="41" />
<hkern u1="&#x1e84;" u2="z" k="9" />
<hkern u1="&#x1e84;" u2="u" k="21" />
<hkern u1="&#x1e84;" u2="o" k="36" />
<hkern u1="&#x1e84;" u2="a" k="37" />
<hkern u1="&#x1e84;" u2="&#x20;" k="23" />
<hkern u1="&#x13a;" u2="&#xb7;" k="61" />
<hkern u1="&#x13a;" u2="Z" k="5" />
<hkern u1="&#x14a;" u2="d" k="6" />
<hkern u1="&#x14a;" u2="&#xf0;" k="7" />
<hkern u1="&#x14a;" u2="o" k="6" />
<hkern u1="&#x14b;" u2="&#x29;" k="26" />
<hkern u1="&#x14b;" u2="&#x2122;" k="20" />
<hkern u1="&#x14b;" u2="&#x7d;" k="31" />
<hkern u1="&#x14b;" u2="&#x2018;" k="13" />
<hkern u1="&#x14b;" u2="&#x2019;" k="14" />
<hkern u1="&#x14b;" u2="\" k="27" />
<hkern u1="&#x14b;" u2="]" k="31" />
<hkern u1="&#x14b;" u2="U" k="5" />
<hkern u1="&#x14b;" u2="Y" k="61" />
<hkern u1="&#x14b;" u2="Z" k="5" />
<hkern u1="&#x14b;" u2="W" k="29" />
<hkern u1="&#x14b;" u2="V" k="41" />
<hkern u1="&#x14b;" u2="T" k="80" />
<hkern u1="&#x14b;" u2="y" k="6" />
<hkern u1="&#x14b;" u2="v" k="5" />
<hkern u1="&#x165;" u2="l" k="-46" />
<hkern u1="&#x165;" g2="idotaccent" k="-50" />
<hkern u1="&#x165;" u2="b" k="-48" />
<hkern u1="&#x165;" u2="&#x12f;" k="-50" />
<hkern u1="&#x165;" u2="&#x167;" k="-16" />
<hkern u1="&#x165;" u2="&#x127;" k="-48" />
<hkern u1="&#x165;" u2="&#x21b;" k="-16" />
<hkern u1="&#x165;" u2="&#x163;" k="-16" />
<hkern u1="&#x165;" u2="&#x13c;" k="-46" />
<hkern u1="&#x165;" u2="&#x137;" k="-48" />
<hkern u1="&#x165;" u2="&#x140;" k="-46" />
<hkern u1="&#x165;" u2="&#x13e;" k="-46" />
<hkern u1="&#x165;" u2="&#x165;" k="-16" />
<hkern u1="&#x165;" u2="&#x13a;" k="-46" />
<hkern u1="&#x165;" u2="&#x1ef3;" k="-12" />
<hkern u1="&#x165;" u2="&#x1e85;" k="-11" />
<hkern u1="&#x165;" u2="&#x1e83;" k="-11" />
<hkern u1="&#x165;" u2="&#x1e81;" k="-11" />
<hkern u1="&#x165;" u2="&#x177;" k="-12" />
<hkern u1="&#x165;" u2="&#x175;" k="-11" />
<hkern u1="&#x165;" u2="&#x135;" k="-50" />
<hkern u1="&#x165;" u2="&#x133;" k="-50" />
<hkern u1="&#x165;" u2="&#x129;" k="-50" />
<hkern u1="&#x165;" u2="&#x125;" k="-48" />
<hkern u1="&#x165;" u2="&#x12d;" k="-50" />
<hkern u1="&#x165;" u2="&#x12b;" k="-50" />
<hkern u1="&#x165;" u2="i" k="-50" />
<hkern u1="&#x165;" u2="f" k="-16" />
<hkern u1="&#x165;" u2="&#x2039;" k="34" />
<hkern u1="&#x165;" u2="&#x27;" k="-18" />
<hkern u1="&#x165;" u2="&#x22;" k="-18" />
<hkern u1="&#x165;" u2="&#x142;" k="-46" />
<hkern u1="&#x165;" u2="&#xfe;" k="-48" />
<hkern u1="&#x165;" u2="&#x131;" k="-50" />
<hkern u1="&#x165;" u2="&#xab;" k="34" />
<hkern u1="&#x165;" u2="&#x2018;" k="-23" />
<hkern u1="&#x165;" u2="&#x2019;" k="-21" />
<hkern u1="&#x165;" u2="&#x201c;" k="-23" />
<hkern u1="&#x165;" u2="&#x201d;" k="-21" />
<hkern u1="&#x165;" u2="&#xff;" k="-12" />
<hkern u1="&#x165;" u2="&#xef;" k="-50" />
<hkern u1="&#x165;" u2="&#xee;" k="-50" />
<hkern u1="&#x165;" u2="&#xec;" k="-50" />
<hkern u1="&#x165;" u2="&#xed;" k="-50" />
<hkern u1="&#x165;" u2="&#xfd;" k="-12" />
<hkern u1="&#x165;" g2="fl" k="-16" />
<hkern u1="&#x165;" g2="fi" k="-16" />
<hkern u1="&#x165;" u2="y" k="-12" />
<hkern u1="&#x165;" u2="w" k="-11" />
<hkern u1="&#x165;" u2="k" k="-48" />
<hkern u1="&#x165;" u2="t" k="-16" />
<hkern u1="&#x165;" u2="h" k="-48" />
<hkern u1="&#x165;" u2="&#x29;" k="-42" />
<hkern u1="&#x165;" u2="&#x2a;" k="-48" />
<hkern u1="&#x165;" u2="&#x2122;" k="-59" />
<hkern u1="&#x165;" u2="&#xdf;" k="-27" />
<hkern u1="&#x165;" u2="&#xf0;" k="10" />
<hkern u1="&#x165;" u2="&#x7d;" k="-53" />
<hkern u1="&#x165;" u2="&#xa6;" k="-17" />
<hkern u1="&#x165;" u2="&#x7c;" k="-9" />
<hkern u1="&#x165;" u2="&#x3f;" k="-32" />
<hkern u1="&#x165;" u2="\" k="-76" />
<hkern u1="&#x165;" u2="]" k="-53" />
<hkern u1="&#x165;" u2="v" k="-16" />
<hkern u1="&#x165;" u2="j" k="-38" />
<hkern u1="&#x165;" u2="&#x20;" k="10" />
<hkern u1="&#x1fd;" u2="&#x29;" k="31" />
<hkern u1="&#x1fd;" u2="f" k="4" />
<hkern u1="&#x1fd;" u2="&#x2122;" k="20" />
<hkern u1="&#x1fd;" u2="&#x7d;" k="32" />
<hkern u1="&#x1fd;" u2="&#x2018;" k="16" />
<hkern u1="&#x1fd;" u2="&#x2019;" k="18" />
<hkern u1="&#x1fd;" u2="\" k="28" />
<hkern u1="&#x1fd;" u2="]" k="31" />
<hkern u1="&#x1fd;" u2="X" k="10" />
<hkern u1="&#x1fd;" u2="U" k="5" />
<hkern u1="&#x1fd;" u2="Y" k="83" />
<hkern u1="&#x1fd;" u2="Z" k="8" />
<hkern u1="&#x1fd;" u2="W" k="37" />
<hkern u1="&#x1fd;" u2="V" k="42" />
<hkern u1="&#x1fd;" u2="T" k="74" />
<hkern u1="&#x1fd;" u2="A" k="7" />
<hkern u1="&#x1fd;" u2="x" k="14" />
<hkern u1="&#x1fd;" u2="y" k="10" />
<hkern u1="&#x1fd;" u2="w" k="5" />
<hkern u1="&#x1fd;" u2="v" k="8" />
<hkern u1="&#x110;" u2="&#x166;" k="7" />
<hkern u1="&#x110;" g2="parenright.case" k="27" />
<hkern u1="&#x110;" g2="braceright.case" k="29" />
<hkern u1="&#x110;" g2="bracketright.case" k="29" />
<hkern u1="&#x110;" u2="&#x29;" k="25" />
<hkern u1="&#x110;" g2="seven.plf" k="13" />
<hkern u1="&#x110;" u2="&#xc6;" k="8" />
<hkern u1="&#x110;" u2="&#x7d;" k="25" />
<hkern u1="&#x110;" u2="]" k="25" />
<hkern u1="&#x110;" u2="X" k="22" />
<hkern u1="&#x110;" u2="Y" k="25" />
<hkern u1="&#x110;" u2="Z" k="8" />
<hkern u1="&#x110;" u2="W" k="13" />
<hkern u1="&#x110;" u2="V" k="18" />
<hkern u1="&#x110;" u2="J" k="17" />
<hkern u1="&#x110;" u2="T" k="16" />
<hkern u1="&#x110;" u2="A" k="15" />
<hkern u1="&#x110;" u2="&#x2e;" k="9" />
<hkern u1="&#x110;" u2="x" k="12" />
<hkern u1="&#x111;" u2="Z" k="5" />
<hkern u1="&#x13d;" u2="&#x166;" k="54" />
<hkern u1="&#x140;" g2="fl" k="-7" />
<hkern u1="&#x140;" g2="fi" k="-7" />
<hkern u1="&#x149;" u2="&#x29;" k="26" />
<hkern u1="&#x149;" u2="&#x2122;" k="20" />
<hkern u1="&#x149;" u2="&#x7d;" k="31" />
<hkern u1="&#x149;" u2="&#x2018;" k="13" />
<hkern u1="&#x149;" u2="&#x2019;" k="14" />
<hkern u1="&#x149;" u2="\" k="27" />
<hkern u1="&#x149;" u2="]" k="31" />
<hkern u1="&#x149;" u2="U" k="5" />
<hkern u1="&#x149;" u2="Y" k="61" />
<hkern u1="&#x149;" u2="Z" k="5" />
<hkern u1="&#x149;" u2="W" k="29" />
<hkern u1="&#x149;" u2="V" k="41" />
<hkern u1="&#x149;" u2="T" k="80" />
<hkern u1="&#x149;" u2="y" k="6" />
<hkern u1="&#x149;" u2="v" k="5" />
<hkern u1="&#x122;" u2="&#x29;" k="14" />
<hkern u1="&#x122;" u2="&#x7d;" k="19" />
<hkern u1="&#x122;" u2="]" k="19" />
<hkern u1="&#x122;" u2="Y" k="23" />
<hkern u1="&#x122;" u2="W" k="12" />
<hkern u1="&#x122;" u2="V" k="18" />
<hkern u1="&#x122;" u2="T" k="10" />
<hkern u1="&#x137;" u2="d" k="22" />
<hkern u1="&#x137;" u2="&#x2039;" k="31" />
<hkern u1="&#x137;" u2="&#x2122;" k="14" />
<hkern u1="&#x137;" u2="&#xf0;" k="25" />
<hkern u1="&#x137;" u2="&#x7d;" k="16" />
<hkern u1="&#x137;" u2="s" k="7" />
<hkern u1="&#x137;" u2="]" k="16" />
<hkern u1="&#x137;" u2="&#x2d;" k="25" />
<hkern u1="&#x137;" u2="Y" k="30" />
<hkern u1="&#x137;" u2="V" k="10" />
<hkern u1="&#x137;" u2="T" k="66" />
<hkern u1="&#x137;" u2="S" k="6" />
<hkern u1="&#x137;" u2="O" k="11" />
<hkern u1="&#x137;" u2="o" k="23" />
<hkern u1="&#x137;" u2="a" k="5" />
<hkern u1="&#x13c;" u2="&#xb7;" k="61" />
<hkern u1="&#x13c;" u2="Z" k="5" />
<hkern u1="&#x145;" u2="d" k="6" />
<hkern u1="&#x145;" u2="&#xf0;" k="7" />
<hkern u1="&#x145;" u2="o" k="6" />
<hkern u1="&#x146;" u2="&#x29;" k="26" />
<hkern u1="&#x146;" u2="&#x2122;" k="20" />
<hkern u1="&#x146;" u2="&#x7d;" k="31" />
<hkern u1="&#x146;" u2="&#x2018;" k="13" />
<hkern u1="&#x146;" u2="&#x2019;" k="14" />
<hkern u1="&#x146;" u2="\" k="27" />
<hkern u1="&#x146;" u2="]" k="31" />
<hkern u1="&#x146;" u2="U" k="5" />
<hkern u1="&#x146;" u2="Y" k="61" />
<hkern u1="&#x146;" u2="Z" k="5" />
<hkern u1="&#x146;" u2="W" k="29" />
<hkern u1="&#x146;" u2="V" k="41" />
<hkern u1="&#x146;" u2="T" k="80" />
<hkern u1="&#x146;" u2="y" k="6" />
<hkern u1="&#x146;" u2="v" k="5" />
<hkern u1="&#x156;" u2="d" k="11" />
<hkern u1="&#x156;" g2="four.plf" k="12" />
<hkern u1="&#x156;" u2="&#x2039;" k="15" />
<hkern u1="&#x156;" u2="&#xf0;" k="17" />
<hkern u1="&#x156;" u2="&#x7d;" k="12" />
<hkern u1="&#x156;" u2="s" k="5" />
<hkern u1="&#x156;" u2="]" k="12" />
<hkern u1="&#x156;" u2="Y" k="12" />
<hkern u1="&#x156;" u2="W" k="8" />
<hkern u1="&#x156;" u2="V" k="12" />
<hkern u1="&#x156;" u2="A" k="6" />
<hkern u1="&#x156;" u2="u" k="5" />
<hkern u1="&#x156;" u2="o" k="12" />
<hkern u1="&#x156;" u2="a" k="7" />
<hkern u1="&#x157;" u2="d" k="11" />
<hkern u1="&#x157;" u2="&#x29;" k="19" />
<hkern u1="&#x157;" u2="&#x2039;" k="24" />
<hkern u1="&#x157;" u2="&#xc6;" k="44" />
<hkern u1="&#x157;" u2="&#xf0;" k="27" />
<hkern u1="&#x157;" u2="&#x7d;" k="13" />
<hkern u1="&#x157;" u2="]" k="13" />
<hkern u1="&#x157;" u2="&#x2f;" k="26" />
<hkern u1="&#x157;" u2="&#x2d;" k="27" />
<hkern u1="&#x157;" u2="X" k="21" />
<hkern u1="&#x157;" u2="Y" k="15" />
<hkern u1="&#x157;" u2="Z" k="11" />
<hkern u1="&#x157;" u2="J" k="55" />
<hkern u1="&#x157;" u2="T" k="45" />
<hkern u1="&#x157;" u2="A" k="39" />
<hkern u1="&#x157;" u2="&#x2e;" k="33" />
<hkern u1="&#x157;" u2="o" k="11" />
<hkern u1="&#x157;" u2="a" k="4" />
<hkern u1="&#x157;" u2="&#x20;" k="21" />
<hkern u1="&#x15e;" g2="parenright.case" k="19" />
<hkern u1="&#x15e;" g2="braceright.case" k="23" />
<hkern u1="&#x15e;" g2="bracketright.case" k="23" />
<hkern u1="&#x15e;" u2="&#x29;" k="18" />
<hkern u1="&#x15e;" u2="&#x7d;" k="18" />
<hkern u1="&#x15e;" u2="]" k="18" />
<hkern u1="&#x15e;" u2="X" k="6" />
<hkern u1="&#x15e;" u2="Y" k="18" />
<hkern u1="&#x15e;" u2="W" k="10" />
<hkern u1="&#x15e;" u2="V" k="15" />
<hkern u1="&#x15e;" u2="T" k="5" />
<hkern u1="&#x15e;" u2="A" k="9" />
<hkern u1="&#x15e;" u2="x" k="8" />
<hkern u1="&#x15f;" u2="&#x29;" k="28" />
<hkern u1="&#x15f;" u2="&#x2122;" k="22" />
<hkern u1="&#x15f;" u2="&#x7d;" k="30" />
<hkern u1="&#x15f;" u2="&#x2018;" k="13" />
<hkern u1="&#x15f;" u2="&#x2019;" k="15" />
<hkern u1="&#x15f;" u2="\" k="27" />
<hkern u1="&#x15f;" u2="]" k="30" />
<hkern u1="&#x15f;" u2="U" k="6" />
<hkern u1="&#x15f;" u2="Y" k="77" />
<hkern u1="&#x15f;" u2="W" k="37" />
<hkern u1="&#x15f;" u2="V" k="42" />
<hkern u1="&#x15f;" u2="T" k="72" />
<hkern u1="&#x15f;" u2="A" k="5" />
<hkern u1="&#x15f;" u2="x" k="6" />
<hkern u1="&#x15f;" u2="y" k="9" />
<hkern u1="&#x15f;" u2="w" k="5" />
<hkern u1="&#x15f;" u2="v" k="8" />
<hkern u1="&#x163;" u2="&#x2039;" k="14" />
<hkern u1="&#x163;" u2="&#x7d;" k="10" />
<hkern u1="&#x163;" u2="]" k="10" />
<hkern u1="&#x163;" u2="Y" k="21" />
<hkern u1="&#x163;" u2="T" k="44" />
<hkern u1="&#x162;" u2="d" k="79" />
<hkern u1="&#x162;" u2="&#x144;" k="72" />
<hkern u1="&#x162;" u2="&#x129;" k="-27" />
<hkern u1="&#x162;" u2="&#x12d;" k="-7" />
<hkern u1="&#x162;" u2="&#x12b;" k="-25" />
<hkern u1="&#x162;" g2="hyphen.case" k="38" />
<hkern u1="&#x162;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x162;" g2="guilsinglright.case" k="39" />
<hkern u1="&#x162;" g2="four.plf" k="46" />
<hkern u1="&#x162;" u2="n" k="59" />
<hkern u1="&#x162;" u2="f" k="16" />
<hkern u1="&#x162;" u2="&#x203a;" k="55" />
<hkern u1="&#x162;" u2="&#x2039;" k="61" />
<hkern u1="&#x162;" u2="&#xdf;" k="19" />
<hkern u1="&#x162;" u2="&#xc6;" k="56" />
<hkern u1="&#x162;" u2="&#xf0;" k="58" />
<hkern u1="&#x162;" u2="&#x131;" k="59" />
<hkern u1="&#x162;" u2="&#xae;" k="19" />
<hkern u1="&#x162;" u2="s" k="71" />
<hkern u1="&#x162;" u2="&#xa9;" k="19" />
<hkern u1="&#x162;" u2="&#xfa;" k="75" />
<hkern u1="&#x162;" u2="&#x2f;" k="43" />
<hkern u1="&#x162;" u2="&#x3a;" k="30" />
<hkern u1="&#x162;" u2="&#x2d;" k="39" />
<hkern u1="&#x162;" u2="J" k="51" />
<hkern u1="&#x162;" u2="O" k="15" />
<hkern u1="&#x162;" u2="A" k="52" />
<hkern u1="&#x162;" u2="&#x2e;" k="47" />
<hkern u1="&#x162;" u2="x" k="64" />
<hkern u1="&#x162;" u2="y" k="50" />
<hkern u1="&#x162;" u2="z" k="75" />
<hkern u1="&#x162;" u2="w" k="52" />
<hkern u1="&#x162;" u2="v" k="50" />
<hkern u1="&#x162;" u2="u" k="53" />
<hkern u1="&#x162;" u2="o" k="78" />
<hkern u1="&#x162;" u2="a" k="73" />
<hkern u1="&#x162;" u2="&#x20;" k="27" />
<hkern u1="&#x21b;" u2="&#x2039;" k="14" />
<hkern u1="&#x21b;" u2="&#x7d;" k="10" />
<hkern u1="&#x21b;" u2="]" k="10" />
<hkern u1="&#x21b;" u2="Y" k="21" />
<hkern u1="&#x21b;" u2="T" k="44" />
<hkern u1="&#x21a;" u2="d" k="79" />
<hkern u1="&#x21a;" u2="&#x144;" k="72" />
<hkern u1="&#x21a;" u2="&#x129;" k="-27" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-7" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-25" />
<hkern u1="&#x21a;" g2="hyphen.case" k="38" />
<hkern u1="&#x21a;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x21a;" g2="guilsinglright.case" k="39" />
<hkern u1="&#x21a;" g2="four.plf" k="46" />
<hkern u1="&#x21a;" u2="n" k="59" />
<hkern u1="&#x21a;" u2="f" k="16" />
<hkern u1="&#x21a;" u2="&#x203a;" k="55" />
<hkern u1="&#x21a;" u2="&#x2039;" k="61" />
<hkern u1="&#x21a;" u2="&#xdf;" k="19" />
<hkern u1="&#x21a;" u2="&#xc6;" k="56" />
<hkern u1="&#x21a;" u2="&#xf0;" k="58" />
<hkern u1="&#x21a;" u2="&#x131;" k="59" />
<hkern u1="&#x21a;" u2="&#xae;" k="19" />
<hkern u1="&#x21a;" u2="s" k="71" />
<hkern u1="&#x21a;" u2="&#xa9;" k="19" />
<hkern u1="&#x21a;" u2="&#xfa;" k="75" />
<hkern u1="&#x21a;" u2="&#x2f;" k="43" />
<hkern u1="&#x21a;" u2="&#x3a;" k="30" />
<hkern u1="&#x21a;" u2="&#x2d;" k="39" />
<hkern u1="&#x21a;" u2="J" k="51" />
<hkern u1="&#x21a;" u2="O" k="15" />
<hkern u1="&#x21a;" u2="A" k="52" />
<hkern u1="&#x21a;" u2="&#x2e;" k="47" />
<hkern u1="&#x21a;" u2="x" k="64" />
<hkern u1="&#x21a;" u2="y" k="50" />
<hkern u1="&#x21a;" u2="z" k="75" />
<hkern u1="&#x21a;" u2="w" k="52" />
<hkern u1="&#x21a;" u2="v" k="50" />
<hkern u1="&#x21a;" u2="u" k="53" />
<hkern u1="&#x21a;" u2="o" k="78" />
<hkern u1="&#x21a;" u2="a" k="73" />
<hkern u1="&#x21a;" u2="&#x20;" k="27" />
<hkern u1="&#x218;" g2="parenright.case" k="19" />
<hkern u1="&#x218;" g2="braceright.case" k="23" />
<hkern u1="&#x218;" g2="bracketright.case" k="23" />
<hkern u1="&#x218;" u2="&#x29;" k="18" />
<hkern u1="&#x218;" u2="&#x7d;" k="18" />
<hkern u1="&#x218;" u2="]" k="18" />
<hkern u1="&#x218;" u2="X" k="6" />
<hkern u1="&#x218;" u2="Y" k="18" />
<hkern u1="&#x218;" u2="W" k="10" />
<hkern u1="&#x218;" u2="V" k="15" />
<hkern u1="&#x218;" u2="T" k="5" />
<hkern u1="&#x218;" u2="A" k="9" />
<hkern u1="&#x218;" u2="x" k="8" />
<hkern u1="&#x219;" u2="&#x29;" k="28" />
<hkern u1="&#x219;" u2="&#x2122;" k="22" />
<hkern u1="&#x219;" u2="&#x7d;" k="30" />
<hkern u1="&#x219;" u2="&#x2018;" k="13" />
<hkern u1="&#x219;" u2="&#x2019;" k="15" />
<hkern u1="&#x219;" u2="\" k="27" />
<hkern u1="&#x219;" u2="]" k="30" />
<hkern u1="&#x219;" u2="U" k="6" />
<hkern u1="&#x219;" u2="Y" k="77" />
<hkern u1="&#x219;" u2="W" k="37" />
<hkern u1="&#x219;" u2="V" k="42" />
<hkern u1="&#x219;" u2="T" k="72" />
<hkern u1="&#x219;" u2="A" k="5" />
<hkern u1="&#x219;" u2="x" k="6" />
<hkern u1="&#x219;" u2="y" k="9" />
<hkern u1="&#x219;" u2="w" k="5" />
<hkern u1="&#x219;" u2="v" k="8" />
<hkern g1="uni00AD.case" u2="&#x166;" k="10" />
<hkern g1="uni00AD.case" u2="X" k="18" />
<hkern g1="uni00AD.case" u2="Y" k="30" />
<hkern g1="uni00AD.case" u2="Z" k="8" />
<hkern g1="uni00AD.case" u2="V" k="14" />
<hkern g1="uni00AD.case" u2="J" k="44" />
<hkern g1="uni00AD.case" u2="T" k="38" />
<hkern g1="uni00AD.case" u2="A" k="9" />
<hkern u1="&#x126;" u2="d" k="6" />
<hkern u1="&#x126;" u2="&#xf0;" k="7" />
<hkern u1="&#x126;" u2="o" k="6" />
<hkern u1="&#x127;" u2="&#x29;" k="26" />
<hkern u1="&#x127;" u2="&#x2122;" k="20" />
<hkern u1="&#x127;" u2="&#x7d;" k="31" />
<hkern u1="&#x127;" u2="&#x2018;" k="13" />
<hkern u1="&#x127;" u2="&#x2019;" k="14" />
<hkern u1="&#x127;" u2="\" k="27" />
<hkern u1="&#x127;" u2="]" k="31" />
<hkern u1="&#x127;" u2="U" k="5" />
<hkern u1="&#x127;" u2="Y" k="61" />
<hkern u1="&#x127;" u2="Z" k="5" />
<hkern u1="&#x127;" u2="W" k="29" />
<hkern u1="&#x127;" u2="V" k="41" />
<hkern u1="&#x127;" u2="T" k="80" />
<hkern u1="&#x127;" u2="y" k="6" />
<hkern u1="&#x127;" u2="v" k="5" />
<hkern u1="&#x167;" u2="&#x2039;" k="14" />
<hkern u1="&#x167;" u2="&#x7d;" k="10" />
<hkern u1="&#x167;" u2="]" k="10" />
<hkern u1="&#x167;" u2="Y" k="21" />
<hkern u1="&#x167;" u2="T" k="44" />
<hkern u1="&#x166;" u2="&#x105;" k="43" />
<hkern u1="&#x166;" u2="&#x104;" k="44" />
<hkern u1="&#x166;" u2="&#x173;" k="30" />
<hkern u1="&#x166;" u2="&#x119;" k="42" />
<hkern u1="&#x166;" u2="&#x1fa;" k="44" />
<hkern u1="&#x166;" u2="&#x1fb;" k="43" />
<hkern u1="&#x166;" u2="&#x167;" k="6" />
<hkern u1="&#x166;" g2="uni00AD.case" k="10" />
<hkern u1="&#x166;" u2="&#x219;" k="39" />
<hkern u1="&#x166;" u2="&#x21b;" k="6" />
<hkern u1="&#x166;" u2="&#x163;" k="6" />
<hkern u1="&#x166;" u2="&#x15f;" k="39" />
<hkern u1="&#x166;" u2="&#x157;" k="34" />
<hkern u1="&#x166;" u2="&#x146;" k="34" />
<hkern u1="&#x166;" u2="&#x122;" k="7" />
<hkern u1="&#x166;" u2="&#x149;" k="34" />
<hkern u1="&#x166;" u2="&#x111;" k="43" />
<hkern u1="&#x166;" u2="&#x1fd;" k="43" />
<hkern u1="&#x166;" u2="&#x1ff;" k="42" />
<hkern u1="&#x166;" u2="&#x1fe;" k="7" />
<hkern u1="&#x166;" u2="&#x165;" k="6" />
<hkern u1="&#x166;" u2="&#x14b;" k="34" />
<hkern u1="&#x166;" u2="&#x1fc;" k="46" />
<hkern u1="&#x166;" u2="&#x1ef3;" k="23" />
<hkern u1="&#x166;" u2="&#x1e85;" k="22" />
<hkern u1="&#x166;" u2="&#x1e83;" k="22" />
<hkern u1="&#x166;" u2="&#x1e81;" k="22" />
<hkern u1="&#x166;" u2="&#x17c;" k="39" />
<hkern u1="&#x166;" u2="&#x17a;" k="39" />
<hkern u1="&#x166;" u2="&#x177;" k="23" />
<hkern u1="&#x166;" u2="&#x175;" k="22" />
<hkern u1="&#x166;" u2="&#x171;" k="30" />
<hkern u1="&#x166;" u2="&#x16f;" k="30" />
<hkern u1="&#x166;" u2="&#x169;" k="30" />
<hkern u1="&#x166;" u2="&#x15d;" k="39" />
<hkern u1="&#x166;" u2="&#x15b;" k="39" />
<hkern u1="&#x166;" u2="&#x159;" k="34" />
<hkern u1="&#x166;" u2="&#x155;" k="34" />
<hkern u1="&#x166;" u2="&#x151;" k="42" />
<hkern u1="&#x166;" u2="&#x150;" k="7" />
<hkern u1="&#x166;" u2="&#x148;" k="34" />
<hkern u1="&#x166;" u2="&#x123;" k="43" />
<hkern u1="&#x166;" u2="&#x121;" k="43" />
<hkern u1="&#x166;" u2="&#x11d;" k="43" />
<hkern u1="&#x166;" u2="&#x11c;" k="7" />
<hkern u1="&#x166;" u2="&#x11b;" k="42" />
<hkern u1="&#x166;" u2="&#x117;" k="42" />
<hkern u1="&#x166;" u2="&#x16d;" k="30" />
<hkern u1="&#x166;" u2="&#x14f;" k="42" />
<hkern u1="&#x166;" u2="&#x14e;" k="7" />
<hkern u1="&#x166;" u2="&#x11f;" k="43" />
<hkern u1="&#x166;" u2="&#x11e;" k="7" />
<hkern u1="&#x166;" u2="&#x115;" k="42" />
<hkern u1="&#x166;" u2="&#x16b;" k="30" />
<hkern u1="&#x166;" u2="&#x14d;" k="42" />
<hkern u1="&#x166;" u2="&#x14c;" k="7" />
<hkern u1="&#x166;" u2="&#x113;" k="42" />
<hkern u1="&#x166;" u2="&#x10f;" k="43" />
<hkern u1="&#x166;" u2="&#x10d;" k="42" />
<hkern u1="&#x166;" u2="&#x10c;" k="7" />
<hkern u1="&#x166;" u2="&#x10b;" k="42" />
<hkern u1="&#x166;" u2="&#x10a;" k="7" />
<hkern u1="&#x166;" u2="&#x109;" k="42" />
<hkern u1="&#x166;" u2="&#x108;" k="7" />
<hkern u1="&#x166;" u2="&#x107;" k="42" />
<hkern u1="&#x166;" u2="&#x106;" k="7" />
<hkern u1="&#x166;" u2="&#x103;" k="43" />
<hkern u1="&#x166;" u2="&#x102;" k="44" />
<hkern u1="&#x166;" u2="&#x101;" k="43" />
<hkern u1="&#x166;" u2="&#x100;" k="44" />
<hkern u1="&#x166;" u2="&#x120;" k="7" />
<hkern u1="&#x166;" u2="&#xad;" k="11" />
<hkern u1="&#x166;" g2="emdash.case" k="10" />
<hkern u1="&#x166;" g2="endash.case" k="10" />
<hkern u1="&#x166;" g2="guillemotleft.case" k="21" />
<hkern u1="&#x166;" g2="guillemotright.case" k="12" />
<hkern u1="&#x166;" u2="&#xd5;" k="7" />
<hkern u1="&#x166;" u2="&#xc3;" k="44" />
<hkern u1="&#x166;" u2="&#xf5;" k="42" />
<hkern u1="&#x166;" u2="&#xf1;" k="34" />
<hkern u1="&#x166;" u2="&#xe3;" k="43" />
<hkern u1="&#x166;" u2="&#x3b;" k="10" />
<hkern u1="&#x166;" u2="&#xbb;" k="24" />
<hkern u1="&#x166;" u2="&#xd8;" k="7" />
<hkern u1="&#x166;" u2="&#x152;" k="7" />
<hkern u1="&#x166;" u2="&#x153;" k="42" />
<hkern u1="&#x166;" u2="&#xe6;" k="43" />
<hkern u1="&#x166;" u2="&#xf8;" k="42" />
<hkern u1="&#x166;" u2="&#xc5;" k="44" />
<hkern u1="&#x166;" u2="&#xe5;" k="43" />
<hkern u1="&#x166;" u2="&#x17e;" k="39" />
<hkern u1="&#x166;" u2="&#x161;" k="39" />
<hkern u1="&#x166;" u2="&#xd6;" k="7" />
<hkern u1="&#x166;" u2="&#xd2;" k="7" />
<hkern u1="&#x166;" u2="&#xd4;" k="7" />
<hkern u1="&#x166;" u2="&#xd3;" k="7" />
<hkern u1="&#x166;" u2="&#xab;" k="31" />
<hkern u1="&#x166;" u2="&#xc7;" k="7" />
<hkern u1="&#x166;" u2="&#xc1;" k="44" />
<hkern u1="&#x166;" u2="&#xc2;" k="44" />
<hkern u1="&#x166;" u2="&#xff;" k="23" />
<hkern u1="&#x166;" u2="&#xc0;" k="44" />
<hkern u1="&#x166;" u2="&#xfc;" k="30" />
<hkern u1="&#x166;" u2="&#xfb;" k="30" />
<hkern u1="&#x166;" u2="&#xf9;" k="30" />
<hkern u1="&#x166;" u2="&#xf6;" k="42" />
<hkern u1="&#x166;" u2="&#xf4;" k="42" />
<hkern u1="&#x166;" u2="&#xf2;" k="42" />
<hkern u1="&#x166;" u2="&#xf3;" k="42" />
<hkern u1="&#x166;" u2="&#xeb;" k="42" />
<hkern u1="&#x166;" u2="&#xea;" k="42" />
<hkern u1="&#x166;" u2="&#xe8;" k="42" />
<hkern u1="&#x166;" u2="&#xe9;" k="42" />
<hkern u1="&#x166;" u2="&#xe4;" k="43" />
<hkern u1="&#x166;" u2="&#xe2;" k="43" />
<hkern u1="&#x166;" u2="&#xe0;" k="43" />
<hkern u1="&#x166;" u2="&#xe1;" k="43" />
<hkern u1="&#x166;" u2="&#xc4;" k="44" />
<hkern u1="&#x166;" u2="&#xfd;" k="23" />
<hkern u1="&#x166;" u2="&#x2014;" k="11" />
<hkern u1="&#x166;" u2="&#x2013;" k="11" />
<hkern u1="&#x166;" u2="&#xe7;" k="42" />
<hkern u1="&#x166;" g2="fl" k="6" />
<hkern u1="&#x166;" g2="fi" k="6" />
<hkern u1="&#x166;" u2="G" k="7" />
<hkern u1="&#x166;" u2="C" k="7" />
<hkern u1="&#x166;" u2="Q" k="7" />
<hkern u1="&#x166;" u2="r" k="34" />
<hkern u1="&#x166;" u2="m" k="34" />
<hkern u1="&#x166;" u2="c" k="42" />
<hkern u1="&#x166;" u2="e" k="42" />
<hkern u1="&#x166;" u2="t" k="6" />
<hkern u1="&#x166;" u2="p" k="34" />
<hkern u1="&#x166;" u2="d" k="79" />
<hkern u1="&#x166;" u2="&#x144;" k="72" />
<hkern u1="&#x166;" u2="&#x129;" k="-27" />
<hkern u1="&#x166;" u2="&#x12d;" k="-7" />
<hkern u1="&#x166;" u2="&#x12b;" k="-25" />
<hkern u1="&#x166;" g2="hyphen.case" k="38" />
<hkern u1="&#x166;" g2="guilsinglleft.case" k="58" />
<hkern u1="&#x166;" g2="guilsinglright.case" k="39" />
<hkern u1="&#x166;" g2="four.plf" k="46" />
<hkern u1="&#x166;" u2="n" k="59" />
<hkern u1="&#x166;" u2="f" k="16" />
<hkern u1="&#x166;" u2="&#x203a;" k="55" />
<hkern u1="&#x166;" u2="&#x2039;" k="61" />
<hkern u1="&#x166;" u2="&#xdf;" k="11" />
<hkern u1="&#x166;" u2="&#xc6;" k="56" />
<hkern u1="&#x166;" u2="&#xf0;" k="37" />
<hkern u1="&#x166;" u2="&#x131;" k="59" />
<hkern u1="&#x166;" u2="&#xae;" k="6" />
<hkern u1="&#x166;" u2="g" k="13" />
<hkern u1="&#x166;" u2="s" k="71" />
<hkern u1="&#x166;" u2="&#xa9;" k="6" />
<hkern u1="&#x166;" u2="&#xfa;" k="75" />
<hkern u1="&#x166;" u2="&#x2f;" k="37" />
<hkern u1="&#x166;" u2="&#x3a;" k="30" />
<hkern u1="&#x166;" u2="&#x2d;" k="39" />
<hkern u1="&#x166;" u2="J" k="51" />
<hkern u1="&#x166;" u2="O" k="15" />
<hkern u1="&#x166;" u2="A" k="52" />
<hkern u1="&#x166;" u2="&#x2e;" k="47" />
<hkern u1="&#x166;" u2="x" k="31" />
<hkern u1="&#x166;" u2="y" k="50" />
<hkern u1="&#x166;" u2="z" k="75" />
<hkern u1="&#x166;" u2="w" k="52" />
<hkern u1="&#x166;" u2="v" k="22" />
<hkern u1="&#x166;" u2="u" k="53" />
<hkern u1="&#x166;" u2="o" k="78" />
<hkern u1="&#x166;" u2="a" k="73" />
<hkern u1="&#x166;" u2="q" k="13" />
<hkern u1="&#x166;" u2="&#x20;" k="27" />
<hkern u1="&#x1fa;" u2="d" k="10" />
<hkern u1="&#x1fa;" u2="&#x166;" k="44" />
<hkern u1="&#x1fa;" g2="hyphen.case" k="9" />
<hkern u1="&#x1fa;" g2="braceright.case" k="11" />
<hkern u1="&#x1fa;" g2="bracketright.case" k="11" />
<hkern u1="&#x1fa;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x1fa;" u2="&#x29;" k="16" />
<hkern u1="&#x1fa;" g2="nine.plf" k="10" />
<hkern u1="&#x1fa;" g2="seven.plf" k="15" />
<hkern u1="&#x1fa;" g2="six.plf" k="11" />
<hkern u1="&#x1fa;" g2="one.plf" k="44" />
<hkern u1="&#x1fa;" g2="zero.plf" k="12" />
<hkern u1="&#x1fa;" u2="f" k="16" />
<hkern u1="&#x1fa;" u2="&#x2039;" k="14" />
<hkern u1="&#x1fa;" u2="&#x2a;" k="35" />
<hkern u1="&#x1fa;" u2="&#xaa;" k="28" />
<hkern u1="&#x1fa;" u2="&#xba;" k="31" />
<hkern u1="&#x1fa;" u2="&#x2122;" k="43" />
<hkern u1="&#x1fa;" u2="&#x27;" k="38" />
<hkern u1="&#x1fa;" u2="&#xf0;" k="9" />
<hkern u1="&#x1fa;" u2="&#x7d;" k="33" />
<hkern u1="&#x1fa;" u2="&#xae;" k="16" />
<hkern u1="&#x1fa;" u2="s" k="6" />
<hkern u1="&#x1fa;" u2="&#x2018;" k="44" />
<hkern u1="&#x1fa;" u2="&#x2019;" k="45" />
<hkern u1="&#x1fa;" u2="&#xa9;" k="16" />
<hkern u1="&#x1fa;" u2="&#x3f;" k="35" />
<hkern u1="&#x1fa;" u2="\" k="48" />
<hkern u1="&#x1fa;" u2="]" k="33" />
<hkern u1="&#x1fa;" u2="U" k="13" />
<hkern u1="&#x1fa;" u2="Y" k="57" />
<hkern u1="&#x1fa;" u2="W" k="32" />
<hkern u1="&#x1fa;" u2="V" k="41" />
<hkern u1="&#x1fa;" u2="T" k="52" />
<hkern u1="&#x1fa;" u2="S" k="9" />
<hkern u1="&#x1fa;" u2="O" k="15" />
<hkern u1="&#x1fa;" u2="y" k="27" />
<hkern u1="&#x1fa;" u2="w" k="20" />
<hkern u1="&#x1fa;" u2="v" k="25" />
<hkern u1="&#x1fa;" u2="u" k="8" />
<hkern u1="&#x1fa;" u2="o" k="10" />
<hkern u1="&#x1fa;" u2="a" k="5" />
<hkern u1="&#x1fa;" u2="&#x20;" k="26" />
<hkern u1="&#x119;" u2="&#x29;" k="31" />
<hkern u1="&#x119;" u2="f" k="4" />
<hkern u1="&#x119;" u2="&#x2122;" k="20" />
<hkern u1="&#x119;" u2="&#x7d;" k="32" />
<hkern u1="&#x119;" u2="&#x2018;" k="16" />
<hkern u1="&#x119;" u2="&#x2019;" k="18" />
<hkern u1="&#x119;" u2="\" k="28" />
<hkern u1="&#x119;" u2="]" k="31" />
<hkern u1="&#x119;" u2="X" k="10" />
<hkern u1="&#x119;" u2="U" k="5" />
<hkern u1="&#x119;" u2="Y" k="83" />
<hkern u1="&#x119;" u2="Z" k="8" />
<hkern u1="&#x119;" u2="W" k="37" />
<hkern u1="&#x119;" u2="V" k="42" />
<hkern u1="&#x119;" u2="T" k="74" />
<hkern u1="&#x119;" u2="A" k="7" />
<hkern u1="&#x119;" u2="x" k="14" />
<hkern u1="&#x119;" u2="y" k="10" />
<hkern u1="&#x119;" u2="w" k="5" />
<hkern u1="&#x119;" u2="v" k="8" />
<hkern u1="&#x12e;" u2="d" k="6" />
<hkern u1="&#x12e;" u2="&#xf0;" k="7" />
<hkern u1="&#x12e;" u2="o" k="6" />
<hkern u1="&#x12f;" u2="Z" k="6" />
<hkern u1="&#x172;" u2="d" k="6" />
<hkern u1="&#x172;" g2="parenright.case" k="13" />
<hkern u1="&#x172;" g2="braceright.case" k="14" />
<hkern u1="&#x172;" g2="bracketright.case" k="14" />
<hkern u1="&#x172;" u2="&#xc6;" k="9" />
<hkern u1="&#x172;" u2="&#xf0;" k="8" />
<hkern u1="&#x172;" u2="s" k="6" />
<hkern u1="&#x172;" u2="J" k="16" />
<hkern u1="&#x172;" u2="A" k="13" />
<hkern u1="&#x172;" u2="&#x2e;" k="10" />
<hkern u1="&#x172;" u2="z" k="7" />
<hkern u1="&#x172;" u2="u" k="7" />
<hkern u1="&#x172;" u2="o" k="6" />
<hkern u1="&#x172;" u2="a" k="6" />
<hkern u1="&#x173;" u2="&#x29;" k="24" />
<hkern u1="&#x173;" u2="&#x2122;" k="16" />
<hkern u1="&#x173;" u2="&#x7d;" k="25" />
<hkern u1="&#x173;" u2="\" k="15" />
<hkern u1="&#x173;" u2="]" k="25" />
<hkern u1="&#x173;" u2="Y" k="53" />
<hkern u1="&#x173;" u2="Z" k="5" />
<hkern u1="&#x173;" u2="W" k="26" />
<hkern u1="&#x173;" u2="V" k="35" />
<hkern u1="&#x173;" u2="T" k="59" />
<hkern u1="&#x104;" u2="d" k="10" />
<hkern u1="&#x104;" u2="&#x166;" k="44" />
<hkern u1="&#x104;" g2="hyphen.case" k="9" />
<hkern u1="&#x104;" g2="braceright.case" k="11" />
<hkern u1="&#x104;" g2="bracketright.case" k="11" />
<hkern u1="&#x104;" g2="guilsinglleft.case" k="24" />
<hkern u1="&#x104;" u2="&#x29;" k="16" />
<hkern u1="&#x104;" g2="nine.plf" k="10" />
<hkern u1="&#x104;" g2="seven.plf" k="15" />
<hkern u1="&#x104;" g2="six.plf" k="11" />
<hkern u1="&#x104;" g2="one.plf" k="44" />
<hkern u1="&#x104;" g2="zero.plf" k="12" />
<hkern u1="&#x104;" u2="f" k="16" />
<hkern u1="&#x104;" u2="&#x2039;" k="14" />
<hkern u1="&#x104;" u2="&#x2a;" k="35" />
<hkern u1="&#x104;" u2="&#xaa;" k="28" />
<hkern u1="&#x104;" u2="&#xba;" k="31" />
<hkern u1="&#x104;" u2="&#x2122;" k="43" />
<hkern u1="&#x104;" u2="&#x27;" k="38" />
<hkern u1="&#x104;" u2="&#xf0;" k="9" />
<hkern u1="&#x104;" u2="&#x7d;" k="33" />
<hkern u1="&#x104;" u2="&#xae;" k="16" />
<hkern u1="&#x104;" u2="s" k="6" />
<hkern u1="&#x104;" u2="&#x2018;" k="44" />
<hkern u1="&#x104;" u2="&#x2019;" k="45" />
<hkern u1="&#x104;" u2="&#xa9;" k="16" />
<hkern u1="&#x104;" u2="&#x3f;" k="35" />
<hkern u1="&#x104;" u2="\" k="48" />
<hkern u1="&#x104;" u2="]" k="33" />
<hkern u1="&#x104;" u2="U" k="13" />
<hkern u1="&#x104;" u2="Y" k="57" />
<hkern u1="&#x104;" u2="W" k="32" />
<hkern u1="&#x104;" u2="V" k="41" />
<hkern u1="&#x104;" u2="T" k="52" />
<hkern u1="&#x104;" u2="S" k="9" />
<hkern u1="&#x104;" u2="O" k="15" />
<hkern u1="&#x104;" u2="y" k="27" />
<hkern u1="&#x104;" u2="w" k="20" />
<hkern u1="&#x104;" u2="v" k="25" />
<hkern u1="&#x104;" u2="u" k="8" />
<hkern u1="&#x104;" u2="o" k="10" />
<hkern u1="&#x104;" u2="a" k="5" />
<hkern u1="&#x104;" u2="&#x20;" k="26" />
<hkern u1="d" u2="Z" k="5" />
<hkern g1="idotaccent" u2="Z" k="6" />
<hkern u1="l" u2="&#xb7;" k="61" />
<hkern u1="l" u2="Z" k="5" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="13" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="6" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="6" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="25" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="13" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="16" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="15" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="9" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="8" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="AE,AEacute" 	k="8" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="J,Jcircumflex" 	k="17" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="11" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="9" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="guillemotleft.case,guilsinglleft.case" 	k="13" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="9" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="5" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="9" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="11" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="7" />
<hkern g1="E,Eacute,Ecircumflex,Edieresis,Egrave,OE,AE,Edotaccent,Emacron,Ebreve,Ecaron,AEacute,Eogonek" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="6" />
<hkern g1="G,Gdotaccent,Gbreve,Gcircumflex,Gcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="23" />
<hkern g1="G,Gdotaccent,Gbreve,Gcircumflex,Gcommaaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="12" />
<hkern g1="G,Gdotaccent,Gbreve,Gcircumflex,Gcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="10" />
<hkern g1="I,M,N,H,J,Iacute,Icircumflex,Idieresis,Igrave,Ntilde,Imacron,Ibreve,Hcircumflex,Itilde,Idotaccent,IJ,Jcircumflex,Nacute,Ncaron,Eng,Ncommaaccent,Hbar,Iogonek" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="6" />
<hkern g1="I,M,N,H,J,Iacute,Icircumflex,Idieresis,Igrave,Ntilde,Imacron,Ibreve,Hcircumflex,Itilde,Idotaccent,IJ,Jcircumflex,Nacute,Ncaron,Eng,Ncommaaccent,Hbar,Iogonek" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="6" />
<hkern g1="K,Kcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="28" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="13" />
<hkern g1="K,Kcommaaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="30" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft.case,guilsinglleft.case" 	k="20" />
<hkern g1="K,Kcommaaccent" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="28" />
<hkern g1="K,Kcommaaccent" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="13" />
<hkern g1="K,Kcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="31" />
<hkern g1="K,Kcommaaccent" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="9" />
<hkern g1="K,Kcommaaccent" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="K,Kcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="27" />
<hkern g1="K,Kcommaaccent" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="28" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="20" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="83" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="7" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="18" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="18" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="guillemotleft.case,guilsinglleft.case" 	k="64" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="66" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="58" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="70" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="72" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="quotedblleft,quoteleft" 	k="74" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="quotedblright,quoteright" 	k="74" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="12" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="quotedbl,quotesingle" 	k="74" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="7" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="43" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="52" />
<hkern g1="L,Lslash,Lacute,Lcaron,Ldot,Lcommaaccent" 	g2="guillemotright.case,guilsinglright.case" 	k="29" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="24" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="12" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="15" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="15" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="7" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="AE,AEacute" 	k="8" />
<hkern g1="O,Q,Oacute,Ocircumflex,Ograve,Odieresis,Oslash,Otilde,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="12" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="12" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="15" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="7" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="8" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="5" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="5" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="11" />
<hkern g1="R,Racute,Rcaron,Rcommaaccent" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="6" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="18" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="5" />
<hkern g1="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="9" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="78" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="15" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guillemotleft.case,guilsinglleft.case" 	k="58" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="38" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="73" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="50" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="16" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="53" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="71" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="79" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="52" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="52" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="47" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="AE,AEacute" 	k="56" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="J,Jcircumflex" 	k="51" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="59" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="hyphen,endash,emdash,uni00AD" 	k="39" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guillemotright.case,guilsinglright.case" 	k="39" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="colon,semicolon" 	k="30" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="z,zcaron,zacute,zdotaccent" 	k="75" />
<hkern g1="T,Tcaron,uni0162,uni021A,Tbar" 	g2="guilsinglright,guillemotright" 	k="55" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="6" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="6" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="7" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="6" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="6" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="13" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="AE,AEacute" 	k="9" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	g2="z,zcaron,zacute,zdotaccent" 	k="7" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="36" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="zero.plf,zero.plfslash" 	k="11" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="30" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="12" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guillemotleft.case,guilsinglleft.case" 	k="23" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="37" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="10" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="21" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="33" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="35" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="32" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="AE,AEacute" 	k="41" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="J,Jcircumflex" 	k="47" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="26" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="hyphen,endash,emdash,uni00AD" 	k="12" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="z,zcaron,zacute,zdotaccent" 	k="9" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guilsinglright,guillemotright" 	k="13" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="zero.plf,zero.plfslash" 	k="17" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="24" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guillemotleft.case,guilsinglleft.case" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="30" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="79" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="15" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="23" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="71" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="24" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="AE,AEacute" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="J,Jcircumflex" 	k="63" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="53" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="hyphen,endash,emdash,uni00AD" 	k="42" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guillemotright.case,guilsinglright.case" 	k="19" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="colon,semicolon" 	k="17" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="z,zcaron,zacute,zdotaccent" 	k="42" />
<hkern g1="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	g2="guilsinglright,guillemotright" 	k="37" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="13" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="7" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="guillemotleft.case,guilsinglleft.case" 	k="18" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="endash.case,emdash.case,hyphen.case,uni00AD.case" 	k="8" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="10" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="5" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="u,uacute,ugrave,ucircumflex,udieresis,umacron,ubreve,utilde,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="12" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="8" />
<hkern g1="Z,Zcaron,Zacute,Zdotaccent" 	g2="p,m,r,n,ntilde,nacute,ncaron,racute,rcaron,eng,napostrophe,ncommaaccent,rcommaaccent" 	k="7" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="61" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="9" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="27" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="17" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="67" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="quotedblleft,quoteleft" 	k="27" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="quotedblright,quoteright" 	k="28" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="a,aacute,agrave,acircumflex,adieresis,aring,atilde,amacron,abreve,aringacute,aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="10" />
<hkern g1="p,thorn,b" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="67" />
<hkern g1="p,thorn,b" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="p,thorn,b" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="p,thorn,b" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="11" />
<hkern g1="p,thorn,b" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="79" />
<hkern g1="p,thorn,b" 	g2="quotedblleft,quoteleft" 	k="23" />
<hkern g1="p,thorn,b" 	g2="quotedblright,quoteright" 	k="25" />
<hkern g1="p,thorn,b" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="5" />
<hkern g1="p,thorn,b" 	g2="quotedbl,quotesingle" 	k="11" />
<hkern g1="p,thorn,b" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="7" />
<hkern g1="p,thorn,b" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="10" />
<hkern g1="p,thorn,b" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="12" />
<hkern g1="p,thorn,b" 	g2="J,Jcircumflex" 	k="7" />
<hkern g1="p,thorn,b" 	g2="z,zcaron,zacute,zdotaccent" 	k="5" />
<hkern g1="p,thorn,b" 	g2="E,F,I,K,M,N,R,L,P,H,B,D,Eacute,Ecircumflex,Edieresis,Egrave,Iacute,Icircumflex,Idieresis,Igrave,Eth,Lslash,Thorn,Ntilde,Edotaccent,Dcaron,Emacron,Imacron,Ebreve,Ibreve,Ecaron,Hcircumflex,Itilde,Idotaccent,IJ,Lacute,Nacute,Ncaron,Racute,Rcaron,Eng,Dcroat,Lcaron,Kcommaaccent,Lcommaaccent,Ncommaaccent,Rcommaaccent,Hbar,Eogonek,Iogonek" 	k="6" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="71" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="30" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="5" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="7" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="84" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quotedblleft,quoteleft" 	k="12" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quotedblright,quoteright" 	k="14" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="5" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="17" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="30" />
<hkern g1="dcroat,d" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="dcaron,lcaron" 	g2="j,iacute,igrave,icircumflex,idieresis,dotlessi,i,imacron,ibreve,itilde,ij,jcircumflex,iogonek,idotaccent" 	k="-29" />
<hkern g1="dcaron,lcaron" 	g2="lslash,lacute,lcaron,ldot,lcommaaccent,l" 	k="-25" />
<hkern g1="dcaron,lcaron" 	g2="h,k,thorn,germandbls,hcircumflex,kcommaaccent,hbar,b" 	k="-27" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="83" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="5" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="10" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="74" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="quotedblleft,quoteleft" 	k="16" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="quotedblright,quoteright" 	k="18" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="4" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="5" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="7" />
<hkern g1="e,eacute,egrave,ecircumflex,edieresis,ae,oe,emacron,ebreve,edotaccent,ecaron,aeacute,eogonek" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="38" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="13" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="55" />
<hkern g1="guillemotleft.case,guilsinglleft.case" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="20" />
<hkern g1="guillemotleft.case,guilsinglleft.case" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="39" />
<hkern g1="guillemotleft.case,guilsinglleft.case" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="guilsinglright,guillemotright" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="61" />
<hkern g1="guilsinglright,guillemotright" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="30" />
<hkern g1="guilsinglright,guillemotright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="20" />
<hkern g1="guilsinglright,guillemotright" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="61" />
<hkern g1="guilsinglright,guillemotright" 	g2="quotedblright,quoteright" 	k="28" />
<hkern g1="guilsinglright,guillemotright" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="15" />
<hkern g1="guilsinglright,guillemotright" 	g2="quotedbl,quotesingle" 	k="13" />
<hkern g1="guilsinglright,guillemotright" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="13" />
<hkern g1="guilsinglright,guillemotright" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="14" />
<hkern g1="guilsinglright,guillemotright" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="11" />
<hkern g1="guilsinglright,guillemotright" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="guilsinglright,guillemotright" 	g2="z,zcaron,zacute,zdotaccent" 	k="20" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="46" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="23" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="58" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="24" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="18" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="AE,AEacute" 	k="21" />
<hkern g1="guillemotright.case,guilsinglright.case" 	g2="J,Jcircumflex" 	k="54" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="42" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="12" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="39" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="J,Jcircumflex" 	k="43" />
<hkern g1="hyphen,endash,emdash,uni00AD" 	g2="z,zcaron,zacute,zdotaccent" 	k="14" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="30" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="38" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="9" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="8" />
<hkern g1="endash.case,emdash.case,hyphen.case,uni00AD.case" 	g2="J,Jcircumflex" 	k="44" />
<hkern g1="j,fi,iacute,igrave,icircumflex,idieresis,dotlessi,i,imacron,ibreve,itilde,ij,jcircumflex,iogonek,idotaccent" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="6" />
<hkern g1="k,kcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="30" />
<hkern g1="k,kcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="23" />
<hkern g1="k,kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="k,kcommaaccent" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="11" />
<hkern g1="k,kcommaaccent" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="5" />
<hkern g1="k,kcommaaccent" 	g2="S,Scaron,Sacute,Scircumflex,Scedilla,uni0218" 	k="6" />
<hkern g1="k,kcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="66" />
<hkern g1="k,kcommaaccent" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="7" />
<hkern g1="k,kcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="22" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="25" />
<hkern g1="fl,lslash,lacute,lcommaaccent,l" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="61" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="5" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="29" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="6" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="80" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="quotedblleft,quoteleft" 	k="13" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="quotedblright,quoteright" 	k="14" />
<hkern g1="h,m,n,ntilde,hcircumflex,nacute,ncaron,eng,napostrophe,ncommaaccent,hbar" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="68" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="36" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="11" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="78" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedblleft,quoteleft" 	k="19" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedblright,quoteright" 	k="21" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="5" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="6" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="10" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="12" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="z,zcaron,zacute,zdotaccent" 	k="5" />
<hkern g1="o,oacute,ograve,ocircumflex,odieresis,oslash,otilde,omacron,obreve,ohungarumlaut,oslashacute" 	g2="E,F,I,K,M,N,R,L,P,H,B,D,Eacute,Ecircumflex,Edieresis,Egrave,Iacute,Icircumflex,Idieresis,Igrave,Eth,Lslash,Thorn,Ntilde,Edotaccent,Dcaron,Emacron,Imacron,Ebreve,Ibreve,Ecaron,Hcircumflex,Itilde,Idotaccent,IJ,Lacute,Nacute,Ncaron,Racute,Rcaron,Eng,Dcroat,Lcaron,Kcommaaccent,Lcommaaccent,Ncommaaccent,Rcommaaccent,Hbar,Eogonek,Iogonek" 	k="6" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="61" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="O,Q,C,G,Ccedilla,Oacute,Ocircumflex,Ograve,Odieresis,OE,Oslash,Otilde,Gdotaccent,Cacute,Ccircumflex,Cdotaccent,Ccaron,Omacron,Gbreve,Obreve,Gcircumflex,Ohungarumlaut,Oslashacute,Gcommaaccent" 	k="10" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="41" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="38" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="47" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="quotedblleft,quoteleft" 	k="116" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="quotedblright,quoteright" 	k="118" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="t,fi,fl,f,tcaron,uni0163,uni021B,tbar" 	k="8" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="105" />
<hkern g1="period,comma,quotesinglbase,quotedblbase" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="26" />
<hkern g1="quotedblleft,quoteleft" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="20" />
<hkern g1="quotedblleft,quoteleft" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="15" />
<hkern g1="quotedblleft,quoteleft" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="12" />
<hkern g1="quotedblleft,quoteleft" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="24" />
<hkern g1="quotedblleft,quoteleft" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="44" />
<hkern g1="quotedblleft,quoteleft" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="117" />
<hkern g1="quotedblleft,quoteleft" 	g2="AE,AEacute" 	k="49" />
<hkern g1="quotedblleft,quoteleft" 	g2="J,Jcircumflex" 	k="49" />
<hkern g1="quotedblright,quoteright" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="28" />
<hkern g1="quotedblright,quoteright" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="quotedblright,quoteright" 	g2="guillemotleft.case,guilsinglleft.case" 	k="14" />
<hkern g1="quotedblright,quoteright" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="21" />
<hkern g1="quotedblright,quoteright" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="19" />
<hkern g1="quotedblright,quoteright" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="32" />
<hkern g1="quotedblright,quoteright" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="49" />
<hkern g1="quotedblright,quoteright" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="118" />
<hkern g1="quotedblright,quoteright" 	g2="AE,AEacute" 	k="54" />
<hkern g1="quotedblright,quoteright" 	g2="J,Jcircumflex" 	k="49" />
<hkern g1="quotedbl,quotesingle" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="8" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="13" />
<hkern g1="quotedbl,quotesingle" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="11" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="38" />
<hkern g1="quotedbl,quotesingle" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="105" />
<hkern g1="quotedbl,quotesingle" 	g2="AE,AEacute" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="49" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="15" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="11" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="24" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="4" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="45" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="11" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="39" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="33" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="11" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="AE,AEacute" 	k="44" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="J,Jcircumflex" 	k="55" />
<hkern g1="r,racute,rcaron,rcommaaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="27" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="77" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="9" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="72" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="quotedblleft,quoteleft" 	k="13" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="quotedblright,quoteright" 	k="15" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="5" />
<hkern g1="s,scaron,sacute,scircumflex,scedilla,uni0219" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="5" />
<hkern g1="t,uni0163,uni021B,tbar" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="21" />
<hkern g1="t,uni0163,uni021B,tbar" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="t,uni0163,uni021B,tbar" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="44" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="53" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="26" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="59" />
<hkern g1="q,u,uacute,ugrave,ucircumflex,udieresis,g,umacron,gbreve,ubreve,gcircumflex,gdotaccent,gcommaaccent,utilde,uring,uhungarumlaut,uogonek" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="5" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="24" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="6" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="13" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="5" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="52" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="4" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="7" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="20" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="26" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="12" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="AE,AEacute" 	k="26" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="J,Jcircumflex" 	k="40" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="22" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="9" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="guillemotleft,guilsinglleft" 	k="19" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="a,aacute,agrave,acircumflex,adieresis,aring,ae,atilde,amacron,abreve,aeacute,aringacute,aogonek" 	k="8" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="49" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="s,scaron,sacute,scircumflex,scedilla,uni0219" 	k="6" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="9" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="26" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="period,comma,ellipsis,quotesinglbase,quotedblbase" 	k="35" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Z,Zcaron,Zacute,Zdotaccent" 	k="14" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="AE,AEacute" 	k="33" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="J,Jcircumflex" 	k="61" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="43" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="o,e,c,ccedilla,eacute,egrave,ecircumflex,edieresis,oacute,ograve,ocircumflex,odieresis,oslash,oe,otilde,cacute,ccircumflex,cdotaccent,ccaron,emacron,omacron,ebreve,obreve,edotaccent,ecaron,ohungarumlaut,oslashacute,eogonek" 	k="6" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="guillemotleft,guilsinglleft" 	k="21" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave,Umacron,Ubreve,Utilde,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="T,Tcaron,uni0162,uni021A,Tbar" 	k="76" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="q,g,dcaron,gbreve,gcircumflex,gdotaccent,gcommaaccent,dcroat,d" 	k="5" />
<hkern g1="z,zcaron,zacute,zdotaccent" 	g2="hyphen,endash,emdash,uni00AD" 	k="14" />
<hkern g1="zero.plf,zero.plfslash" 	g2="Y,Yacute,Ydieresis,Ycircumflex,Ygrave" 	k="19" />
<hkern g1="zero.plf,zero.plfslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="11" />
<hkern g1="zero.plf,zero.plfslash" 	g2="A,Adieresis,Agrave,Acircumflex,Aacute,Aring,Atilde,Amacron,Abreve,Aringacute,Aogonek" 	k="12" />
</font>
</defs></svg> 