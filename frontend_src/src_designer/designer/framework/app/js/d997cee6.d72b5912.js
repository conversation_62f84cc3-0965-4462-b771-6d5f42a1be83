(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["d997cee6"],{1331:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=(0,n.regex)("integer",/^-?[0-9]*$/);t.default=r},2385:function(e,t,a){},"2a12":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"maxLength",max:t},function(e){return!(0,n.req)(e)||(0,n.len)(e)<=t})};t.default=r},"2d08":function(e,t,a){"use strict";var n=a("0e26");var r=a("22fe");var i=a("a911");var o=a("6ac8");var l=a("b1cb");var s=a("d7d0");var c=a("e3c1");var u=a("2b52");r(r.S+r.F*!a("5db0")(function(e){Array.from(e)}),"Array",{from:function e(t){var a=i(t);var r=typeof this=="function"?this:Array;var d=arguments.length;var f=d>1?arguments[1]:undefined;var v=f!==undefined;var m=0;var p=u(a);var h,b,y,C;if(v)f=n(f,d>2?arguments[2]:undefined,2);if(p!=undefined&&!(r==Array&&l(p))){for(C=p.call(a),b=new r;!(y=C.next()).done;m++){c(b,m,v?o(C,f,[y.value,m],true):y.value)}}else{h=s(a.length);for(b=new r(h);h>m;m++){c(b,m,v?f(a[m],m):a[m])}}b.length=m;return b}})},"32b7":function(e,t,a){"use strict";var n=a("72b6");var r=a.n(n);var i=r.a},3360:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(){for(var t=arguments.length,a=new Array(t),r=0;r<t;r++){a[r]=arguments[r]}return(0,n.withParams)({type:"and"},function(){var e=this;for(var t=arguments.length,n=new Array(t),r=0;r<t;r++){n[r]=arguments[r]}return a.length>0&&a.reduce(function(t,a){return t&&a.apply(e,n)},true)})};t.default=r},"3a54":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=(0,n.regex)("alphaNum",/^[a-zA-Z0-9]*$/);t.default=r},"3d04":function(e,t,a){"use strict";var n=a("6db0");var r=a.n(n);var i=a("448a");var o=a.n(i);var l=a("970b");var s=a.n(l);var c=a("5bc3");var u=a.n(c);var d=a("9ec3");var f=a.n(d);var v=function(){function e(t){s()(this,e);this.sources=f.a.merge.apply(f.a,o()(t));this.state=""}u()(e,[{key:"filter",value:function e(t){var a=this;new Promise(function(e){console.log(a.sources);e(a)});return this}},{key:"pickFew",value:function e(t){var a=this;new Promise(function(e){console.log(a.sources);e(a)});return this}},{key:"pickOne",value:function e(t){var a=this;new Promise(function(e){console.log(a.sources);e(a)});return this}},{key:"result",value:function e(){var t=this;return new Promise(function(e){console.log(t.sources);e(t)})}}]);return e}();var m=function(){function e(){s()(this,e)}u()(e,[{key:"construct",value:function e(){}},{key:"add",value:function e(t){return new v(t)}}]);return e}();var p=new m},"45b8":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=(0,n.regex)("numeric",/^[0-9]*$/);t.default=r},"46bc":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"maxValue",max:t},function(e){return!(0,n.req)(e)||(!/\s/.test(e)||e instanceof Date)&&+e<=+t})};t.default=r},4757:function(e,t,a){"use strict";var n=a("f0e1");var r=a.n(n);var i=r.a},"4f9c":function(e,t,a){"use strict";var n=a("a702");var r=a.n(n);var i=r.a},"5c3d":function(e,t,a){"use strict";var n=a("c333");var r=a.n(n);var i=r.a},"5d75":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=/(^$|^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$)/;var i=(0,n.regex)("email",r);t.default=i},"5db3":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"minLength",min:t},function(e){return!(0,n.req)(e)||(0,n.len)(e)>=t})};t.default=r},"5dca":function(e,t,a){a("d2c4")("search",1,function(e,t,a){return[function a(n){"use strict";var r=e(this);var i=n==undefined?undefined:n[t];return i!==undefined?i.call(n,r):new RegExp(n)[t](String(r))},a]})},6235:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=(0,n.regex)("alpha",/^[a-zA-Z]*$/);t.default=r},6417:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"not"},function(e,a){return!(0,n.req)(e)||!t.call(this,e,a)})};t.default=r},"6e531":function(e,t,a){"use strict";var n=a("2385");var r=a.n(n);var i=r.a},"72b6":function(e,t,a){},"772d":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=/^(?:(?:https?|ftp):\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[\/?#]\S*)?$/i;var i=(0,n.regex)("url",r);t.default=i},"78ef":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"withParams",{enumerable:true,get:function e(){return n.default}});t.regex=t.ref=t.len=t.req=void 0;var n=r(a("8750"));function r(e){return e&&e.__esModule?e:{default:e}}function i(e){if(typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"){i=function e(t){return typeof t}}else{i=function e(t){return t&&typeof Symbol==="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t}}return i(e)}var o=function e(t){if(Array.isArray(t))return!!t.length;if(t===undefined||t===null){return false}if(t===false){return true}if(t instanceof Date){return!isNaN(t.getTime())}if(i(t)==="object"){for(var a in t){return true}return false}return!!String(t).length};t.req=o;var l=function e(t){if(Array.isArray(t))return t.length;if(i(t)==="object"){return Object.keys(t).length}return String(t).length};t.len=l;var s=function e(t,a,n){return typeof t==="function"?t.call(a,n):n[t]};t.ref=s;var c=function e(t,a){return(0,n.default)({type:t},function(e){return!o(e)||a.test(e)})};t.regex=c},"7b3e":function(e,t,a){"use strict";var n=a("a3de");var r;if(n.canUseDOM){r=document.implementation&&document.implementation.hasFeature&&document.implementation.hasFeature("","")!==true}
/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @param {?boolean} capture Check if the capture phase is supported.
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */function i(e,t){if(!n.canUseDOM||t&&!("addEventListener"in document)){return false}var a="on"+e;var i=a in document;if(!i){var o=document.createElement("div");o.setAttribute(a,"return;");i=typeof o[a]==="function"}if(!i&&r&&e==="wheel"){i=document.implementation.hasFeature("Events.wheel","3.0")}return i}e.exports=i},8750:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=Object({NODE_ENV:"production",CLIENT:true,SERVER:false,DEV:false,PROD:true,MODE:"spa",VUE_ROUTER_MODE:"history",VUE_ROUTER_BASE:"/admin/webdesigner_app/",APP_URL:"undefined"}).BUILD==="web"?a("cb69").withParams:a("0234").withParams;var r=n;t.default=r},"8eb7":function(e,t){var a=false;var n,r,i,o,l;var s;var c,u,d,f;var v;var m,p,h;var b;function y(){if(a){return}a=true;var e=navigator.userAgent;var t=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e);var y=/(Mac OS X)|(Windows)|(Linux)/.exec(e);m=/\b(iPhone|iP[ao]d)/.exec(e);p=/\b(iP[ao]d)/.exec(e);f=/Android/i.exec(e);h=/FBAN\/\w+;/i.exec(e);b=/Mobile/i.exec(e);v=!!/Win64/.exec(e);if(t){n=t[1]?parseFloat(t[1]):t[5]?parseFloat(t[5]):NaN;if(n&&document&&document.documentMode){n=document.documentMode}var C=/(?:Trident\/(\d+.\d+))/.exec(e);s=C?parseFloat(C[1])+4:n;r=t[2]?parseFloat(t[2]):NaN;i=t[3]?parseFloat(t[3]):NaN;o=t[4]?parseFloat(t[4]):NaN;if(o){t=/(?:Chrome\/(\d+\.\d+))/.exec(e);l=t&&t[1]?parseFloat(t[1]):NaN}else{l=NaN}}else{n=r=i=l=o=NaN}if(y){if(y[1]){var w=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);c=w?parseFloat(w[1].replace("_",".")):true}else{c=false}u=!!y[2];d=!!y[3]}else{c=u=d=false}}var C={ie:function(){return y()||n},ieCompatibilityMode:function(){return y()||s>n},ie64:function(){return C.ie()&&v},firefox:function(){return y()||r},opera:function(){return y()||i},webkit:function(){return y()||o},safari:function(){return C.webkit()},chrome:function(){return y()||l},windows:function(){return y()||u},osx:function(){return y()||c},linux:function(){return y()||d},iphone:function(){return y()||m},mobile:function(){return y()||(m||p||f||b)},nativeApp:function(){return y()||h},android:function(){return y()||f},ipad:function(){return y()||p}};e.exports=C},"91d3":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:":";return(0,n.withParams)({type:"macAddress"},function(e){if(!(0,n.req)(e)){return true}if(typeof e!=="string"){return false}var a=typeof t==="string"&&t!==""?e.split(t):e.length===12||e.length===16?e.match(/.{2}/g):null;return a!==null&&(a.length===6||a.length===8)&&a.every(i)})};t.default=r;var i=function e(t){return t.toLowerCase().match(/^[0-9a-f]{2}$/)}},a3de:function(e,t,a){"use strict";var n=!!(typeof window!=="undefined"&&window.document&&window.document.createElement);var r={canUseDOM:n,canUseWorkers:typeof Worker!=="undefined",canUseEventListeners:n&&!!(window.addEventListener||window.attachEvent),canUseViewport:n&&!!window.screen,isInWorker:!n};e.exports=r},a702:function(e,t,a){},aa82:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"requiredIf",prop:t},function(e,a){return(0,n.ref)(t,this,a)?(0,n.req)(e):true})};t.default=r},b356:function(e,t,a){"use strict";var n=a("ce6c");var r=a.n(n);var i=r.a},b5ae:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"alpha",{enumerable:true,get:function e(){return n.default}});Object.defineProperty(t,"alphaNum",{enumerable:true,get:function e(){return r.default}});Object.defineProperty(t,"numeric",{enumerable:true,get:function e(){return i.default}});Object.defineProperty(t,"between",{enumerable:true,get:function e(){return o.default}});Object.defineProperty(t,"email",{enumerable:true,get:function e(){return l.default}});Object.defineProperty(t,"ipAddress",{enumerable:true,get:function e(){return s.default}});Object.defineProperty(t,"macAddress",{enumerable:true,get:function e(){return c.default}});Object.defineProperty(t,"maxLength",{enumerable:true,get:function e(){return u.default}});Object.defineProperty(t,"minLength",{enumerable:true,get:function e(){return d.default}});Object.defineProperty(t,"required",{enumerable:true,get:function e(){return f.default}});Object.defineProperty(t,"requiredIf",{enumerable:true,get:function e(){return v.default}});Object.defineProperty(t,"requiredUnless",{enumerable:true,get:function e(){return m.default}});Object.defineProperty(t,"sameAs",{enumerable:true,get:function e(){return p.default}});Object.defineProperty(t,"url",{enumerable:true,get:function e(){return h.default}});Object.defineProperty(t,"or",{enumerable:true,get:function e(){return b.default}});Object.defineProperty(t,"and",{enumerable:true,get:function e(){return y.default}});Object.defineProperty(t,"not",{enumerable:true,get:function e(){return C.default}});Object.defineProperty(t,"minValue",{enumerable:true,get:function e(){return w.default}});Object.defineProperty(t,"maxValue",{enumerable:true,get:function e(){return g.default}});Object.defineProperty(t,"integer",{enumerable:true,get:function e(){return _.default}});Object.defineProperty(t,"decimal",{enumerable:true,get:function e(){return k.default}});t.helpers=void 0;var n=P(a("6235"));var r=P(a("3a54"));var i=P(a("45b8"));var o=P(a("ec11"));var l=P(a("5d75"));var s=P(a("c99d"));var c=P(a("91d3"));var u=P(a("2a12"));var d=P(a("5db3"));var f=P(a("d4f4"));var v=P(a("aa82"));var m=P(a("e652"));var p=P(a("b6cb"));var h=P(a("772d"));var b=P(a("d294"));var y=P(a("3360"));var C=P(a("6417"));var w=P(a("eb66"));var g=P(a("46bc"));var _=P(a("1331"));var k=P(a("c301"));var x=O(a("78ef"));t.helpers=x;function O(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var a in e){if(Object.prototype.hasOwnProperty.call(e,a)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,a):{};if(n.get||n.set){Object.defineProperty(t,a,n)}else{t[a]=e[a]}}}}t.default=e;return t}}function P(e){return e&&e.__esModule?e:{default:e}}},b666:function(e,t,a){"use strict";var n=a("e886");var r=a.n(n);var i=r.a},b6cb:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"sameAs",eq:t},function(e,a){return e===(0,n.ref)(t,this,a)})};t.default=r},c098:function(e,t,a){e.exports=a("d4af")},c301:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=(0,n.regex)("decimal",/^[-]?\d*(\.\d+)?$/);t.default=r},c333:function(e,t,a){},c99d:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=(0,n.withParams)({type:"ipAddress"},function(e){if(!(0,n.req)(e)){return true}if(typeof e!=="string"){return false}var t=e.split(".");return t.length===4&&t.every(i)});t.default=r;var i=function e(t){if(t.length>3||t.length===0){return false}if(t[0]==="0"&&t!=="0"){return false}if(!t.match(/^\d+$/)){return false}var a=+t|0;return a>=0&&a<=255}},cb69:function(e,t,a){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:true});t.withParams=void 0;function a(e){if(typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"){a=function e(t){return typeof t}}else{a=function e(t){return t&&typeof Symbol==="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t}}return a(e)}var n=typeof window!=="undefined"?window:typeof e!=="undefined"?e:{};var r=function e(t,n){if(a(t)==="object"&&n!==undefined){return n}return t(function(){})};var i=n.vuelidate?n.vuelidate.withParams:r;t.withParams=i}).call(this,a("c8ba"))},ce6c:function(e,t,a){},d294:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(){for(var t=arguments.length,a=new Array(t),r=0;r<t;r++){a[r]=arguments[r]}return(0,n.withParams)({type:"or"},function(){var e=this;for(var t=arguments.length,n=new Array(t),r=0;r<t;r++){n[r]=arguments[r]}return a.length>0&&a.reduce(function(t,a){return t||a.apply(e,n)},false)})};t.default=r},d4af:function(e,t,a){"use strict";var n=a("8eb7");var r=a("7b3e");var i=10;var o=40;var l=800;function s(e){var t=0,a=0,n=0,r=0;if("detail"in e){a=e.detail}if("wheelDelta"in e){a=-e.wheelDelta/120}if("wheelDeltaY"in e){a=-e.wheelDeltaY/120}if("wheelDeltaX"in e){t=-e.wheelDeltaX/120}if("axis"in e&&e.axis===e.HORIZONTAL_AXIS){t=a;a=0}n=t*i;r=a*i;if("deltaY"in e){r=e.deltaY}if("deltaX"in e){n=e.deltaX}if((n||r)&&e.deltaMode){if(e.deltaMode==1){n*=o;r*=o}else{n*=l;r*=l}}if(n&&!t){t=n<1?-1:1}if(r&&!a){a=r<1?-1:1}return{spinX:t,spinY:a,pixelX:n,pixelY:r}}s.getEventType=function(){return n.firefox()?"DOMMouseScroll":r("wheel")?"wheel":"mousewheel"};e.exports=s},d4f4:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=(0,n.withParams)({type:"required"},n.req);t.default=r},d6e2:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("section",{staticClass:"tylko-designer",attrs:{id:"tylkoDesignerLayout"}},[a("div",{staticClass:"flex row"},[a("div",{staticClass:"col menu-column"},[a("t-navigation")],1),a("div",{staticClass:"col"},[a("router-view")],1)])])};var r=[];var i=a("0967");var o=a("2b0e");var l=function(e,t){var a=window.open;if(i["a"].is.cordova===true){if(cordova!==void 0&&cordova.InAppBrowser!==void 0&&cordova.InAppBrowser.open!==void 0){a=cordova.InAppBrowser.open}else if(navigator!==void 0&&navigator.app!==void 0){return navigator.app.loadUrl(e,{openExternal:true})}}else if(o["a"].prototype.$q.electron!==void 0){return o["a"].prototype.$q.electron.shell.openExternal(e)}var n=a(e,"_blank");if(n){n.focus();return n}else{t&&t()}};var s=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.hide,expression:"hide"}],staticClass:"navigation-panel"},[a("div",{ref:"collectionsBar",staticClass:"collections-quick",class:e.paneState},[a("div",{staticClass:"icon-home"},[a("router-link",{staticClass:"collection-link",attrs:{to:"/collection"},nativeOn:{click:function(t){return e.showCollectionsQuick(t)}}},[a("q-icon",{attrs:{name:"home",color:"white"}})],1)],1),e.collections&&e.collections.length>0?a("div",{ref:"collectionPane",staticClass:"content"},e._l(e.collections,function(t){return a("div",{key:t.id,staticClass:"collection-pan"},[a("router-link",{staticClass:"collection-link",attrs:{to:"/collection/"+t.id}},[t.id==e.$route.params.selectedCollection?a("div",[a("div",{staticClass:"selected-collection"},[a("strong",[e._v(e._s(t.name))])])]):a("div",[a("div",[a("p",[e._v(e._s(t.name))])])])])],1)}),0):e._e()]),a("t-menu",{attrs:{activeCollection:e.$route.params.selectedCollection},on:{ShowCollectionsQuick:e.showCollectionsQuick}}),a("t-menu-search")],1)};var c=[];var u=a("a34a");var d=a.n(u);var f=a("192a");var v=a("c973");var m=a.n(v);var p=a("e67d");var h=a.n(p);var b=a("3d04");var y=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("div",{staticClass:"menu-bar"},[a("div",{staticClass:"flex row"},[a("div",{staticClass:"col menu-col"},[a("div",{staticClass:"tool-entry",class:e.currPos=="col"?"selected":""},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{name:"collections_bookmark",color:"white"},nativeOn:{click:function(t){return e.castShowCollectionsQuick(t)}}})],1)]),a("div",{staticClass:"tool-entry",class:e.currPos=="rel"?"selected":""},[a("router-link",{staticClass:"collection-link",attrs:{disabled:!e.selectedCollection,to:e.selectedCollection?"/relations/"+e.selectedCollection:""}},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{name:"device_hub",color:"white"}})],1)])],1),a("div",{staticClass:"tool-entry",class:e.currPos=="edit"?"selected":""},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{name:"edit",disabled:e.currPos!="edit",color:"white"}})],1)]),a("div",{staticClass:"tool-entry",class:e.currPos=="hack"?"selected":""},[a("router-link",{staticClass:"collection-link",attrs:{to:"/hackday"}},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{name:"public",color:"white"}})],1)])],1),a("div",{staticClass:"tool-entry",class:e.currPos=="camera"?"selected":""},[a("router-link",{staticClass:"collection-link",attrs:{to:"/camera"}},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{name:"traffic",color:"white"}})],1)])],1),a("div",{staticClass:"tool-entry"},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{name:"add",disabled:!e.selectedCollection,color:"white"}}),e.selectedCollection?a("div",{staticClass:"action"},[a("q-btn",{attrs:{color:"grey-10",label:"Component Set"},on:{click:e.addNewComponent}}),a("q-btn",{attrs:{color:"grey-10",label:"Mesh"},on:{click:e.addNewMesh}})],1):e._e()],1)]),a("div",{staticClass:"tool-entry settings"},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{name:"more_horiz",color:"white"}})],1)])]),a("t-modal-new-component",{ref:"componentCreator",attrs:{activeCollection:e.activeCollection}}),a("t-modal-new-mesh",{ref:"meshCreator",attrs:{activeCollection:e.activeCollection}})],1)])};var C=[];var w=a("9af0");var g=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("q-dialog",{ref:"modal",attrs:{position:"top",minimized:"minimized"}},[a("div",{staticClass:"modal-main global flex"},[a("div",{staticClass:"row full"},[a("p",{staticClass:"q-headline"},[e._v("Create a new component")])]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"Component name"}},[a("q-input",{attrs:{dark:"dark"},model:{value:e.payload.name,callback:function(t){e.$set(e.payload,"name",t)},expression:"payload.name"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"dim_x"}},[a("q-input",{attrs:{dark:"dark",inverted:"inverted",disable:!e.payload.useInitialDims},model:{value:e.payload.initialDimX,callback:function(t){e.$set(e.payload,"initialDimX",t)},expression:"payload.initialDimX"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"dim_y"}},[a("q-input",{attrs:{dark:"dark",inverted:"inverted",disable:!e.payload.useInitialDims},model:{value:e.payload.initialDimY,callback:function(t){e.$set(e.payload,"initialDimY",t)},expression:"payload.initialDimY"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"dim_z"}},[a("q-input",{attrs:{dark:"dark",inverted:"inverted",disable:!e.payload.useInitialDims},model:{value:e.payload.initialDimZ,callback:function(t){e.$set(e.payload,"initialDimZ",t)},expression:"payload.initialDimZ"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:""}},[a("q-checkbox",{attrs:{dark:"dark",label:"Edit initial setups"},model:{value:e.payload.useInitialDims,callback:function(t){e.$set(e.payload,"useInitialDims",t)},expression:"payload.useInitialDims"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup",value:e.v-e.close-e.popup,expression:"v-close-popup"}],attrs:{color:"dark",inverted:"inverted",label:"Close"}}),a("q-btn",{staticClass:"float-right",attrs:{color:"primary"},nativeOn:{click:function(t){return e.submit(t)}}},[e._v("Submit")])],1)])])])};var _=[];var k=a("3156");var x=a.n(k);var O=a("d6b6");var P=a("b5ae");var q=a("18a5");var $={name:"TylkoNewComponent",props:{configuration:[String,Number],activeCollection:null},validations:{payload:{name:{required:P["required"],minLength:Object(P["minLength"])(4)}}},methods:{submit:function e(){this.$v.payload.$touch();if(this.$v.payload.$error){return}this.addComponent()},addComponent:function e(){var t=this;var a={};a.dim_x=this.payload.initialDimX;a.dim_y=this.payload.initialDimY;a.dim_z=this.payload.initialDimZ;a.collection=+this.$route.params.selectedCollection;q["a"].api.createComponent(x()({name:this.payload.name},a)).then(function(e){q["a"].api.fetchComponents=true;t.payload.name="";t.payload.initialDimX="100-1000";t.payload.initialDimY="200";t.payload.initialDimZ="320,400";t.$router.push("/components/".concat(t.$route.params.selectedCollection,"/")+e.id);t.$emit("reloadAfterCreation")})}},data:function e(){return{payload:{name:"",initialDimX:"100-1000",initialDimY:"200",initialDimZ:"320,400",useInitialDims:false},newHeight:0,newWidthValue:0,newWidthRatio:0,setupName:"Loading..."}}};var D=$;var j=a("5c3d");var S=a("2877");var M=Object(S["a"])(D,g,_,false,null,"37880399",null);var A=M.exports;var N=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("q-dialog",{ref:"modal",attrs:{position:"top",minimized:"minimized"}},[a("div",{staticClass:"modal-main global flex"},[a("div",{staticClass:"row full"},[a("p",{staticClass:"q-headline"},[e._v("Create a new mesh")])]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"Mesh name"}},[a("q-input",{attrs:{dark:"dark"},model:{value:e.payload.name,callback:function(t){e.$set(e.payload,"name",t)},expression:"payload.name"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"dim_x"}},[a("q-input",{attrs:{dark:"dark",disable:!e.payload.useInitialDims},model:{value:e.payload.initialDimX,callback:function(t){e.$set(e.payload,"initialDimX",t)},expression:"payload.initialDimX"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"dim_y",color:"white"}},[a("q-input",{attrs:{dark:"dark",disable:!e.payload.useInitialDims},model:{value:e.payload.initialDimY,callback:function(t){e.$set(e.payload,"initialDimY",t)},expression:"payload.initialDimY"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:"dim_z",color:"white"}},[a("q-input",{attrs:{dark:"dark",disable:!e.payload.useInitialDims},model:{value:e.payload.initialDimZ,callback:function(t){e.$set(e.payload,"initialDimZ",t)},expression:"payload.initialDimZ"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-field",{attrs:{label:""}},[a("q-checkbox",{attrs:{dark:"dark",label:"Edit initial setups"},model:{value:e.payload.useInitialDims,callback:function(t){e.$set(e.payload,"useInitialDims",t)},expression:"payload.useInitialDims"}})],1)],1)]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup",value:e.v-e.close-e.popup,expression:"v-close-popup"}],attrs:{color:"dark",inverted:"inverted",label:"Close"}}),a("q-btn",{staticClass:"float-right",attrs:{color:"primary"},nativeOn:{click:function(t){return e.submit(t)}}},[e._v("Submit")])],1)])])])};var E=[];var I={name:"TylkoNewComponent",props:{configuration:[String,Number],activeCollection:null},validations:{payload:{name:{required:P["required"],minLength:Object(P["minLength"])(4)}}},methods:{submit:function e(){this.$v.payload.$touch();if(this.$v.payload.$error){return}this.addComponent()},addComponent:function e(){var t=this;var a={};a.dim_x=this.payload.initialDimX;a.dim_y=this.payload.initialDimY;a.dim_z=this.payload.initialDimZ;a.collection=this.$route.params.selectedCollection;q["a"].api.createMesh(x()({name:this.payload.name},a)).then(function(e){console.log("!!!!!",e);q["a"].api.fetchMeshes=true;t.payload.name="";t.payload.initialDimX="1200-4500";t.payload.initialDimY="300, 400, 500, 600, 700, 800";t.payload.initialDimZ="400";t.$router.push("/meshes/".concat(t.$route.params.selectedCollection,"/")+e.id);t.$emit("reloadAfterMeshCreation")})}},data:function e(){return{payload:{name:"",initialDimX:"1200-4500",initialDimY:"300, 400, 500, 600, 700, 800",initialDimZ:"400",useInitialDims:false},newHeight:0,newWidthValue:0,newWidthRatio:0,setupName:"Loading..."}}};var L=I;var F=a("32b7");var X=Object(S["a"])(L,N,E,false,null,"6a76d99b",null);var z=X.exports;var U=a("202d");var T={name:"t-menu",props:["mode","behaviour","selected","compact","activeCollection"],data:function e(){return{checked:false,elements:[],components:[],meshes:[],restocking:false,showNotifications:false,currPos:""}},computed:{selectedCollection:function e(){return this.$route.params.selectedCollection?this.$route.params.selectedCollection:false}},components:{"t-modal-new-component":A,"t-modal-new-mesh":z},watch:{$route:function e(t,a){this.selectItem()}},methods:{selectItem:function e(){if(this.$route.name.indexOf("relations")==0)this.currPos="rel";if(this.$route.name.indexOf("meshes")==0)this.currPos="edit";if(this.$route.name.indexOf("components")==0)this.currPos="edit";if(this.$route.name.indexOf("collection")==0)this.currPos="col";if(this.$route.name.indexOf("hackday")==0)this.currPos="hack";if(this.$route.name.indexOf("camera")==0)this.currPos="camera"},castShowCollectionsQuick:function e(){this.$emit("ShowCollectionsQuick");console.log(this.$route)},uuid:function e(){},gotoCollections:function e(){this.$router.push("/collection")},addNewComponent:function e(){this.$refs.componentCreator.$refs.modal.show()},addNewMesh:function e(){this.$refs.meshCreator.$refs.modal.show()},navigate:function e(t){console.log(t,this)}},updated:function e(){},mounted:function e(){this.selectItem()}};var Y=T;var R=a("4f9c");var Z=Object(S["a"])(Y,y,C,false,null,"1afa78f5",null);var V=Z.exports;var B=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("div",{staticClass:"menu-bar"},[a("div",{staticClass:"flex row"},[a("div",{staticClass:"col menu-col"},[a("div",{staticClass:"tool-entry"},[a("div",{staticClass:"icon"},[a("q-icon",{attrs:{disabled:!e.$route.params.selectedCollection,name:"search",color:"white"},nativeOn:{click:function(t){return e.toggleSearch(t)}}})],1)])])]),a("t-menu-fullscreen-search",{attrs:{isOpened:e.isOpenedSearch,activeCollection:e.$route.params.selectedCollection,toggleSearch:e.toggleSearch,dataFromApi:e.dataFromApi}})],1)};var W=[];var Q=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("div",{staticClass:"menu-ite"},[e.item.child?a("div",[a("q-icon",{attrs:{name:e.item.icon,color:"white"}}),a("div",{staticClass:"menu-item-wrapper"},[a("div",{staticClass:"menu-item-title"},[e._v(e._s(e.item.title))]),e._l(e.item.children,function(t){return a("div",[t.type==="multipleSearch"?a("t-menu-multiple-search",{attrs:{childIten:t}}):e._e()],1)})],2)],1):a("div",[a("q-icon",{attrs:{name:e.item.icon,color:"white"}})],1)])};var H=[];var J=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("q-tabs",{attrs:{animated:"animated",position:"top","text-color":"white","underline-color":"white",align:"justify"}},[a("q-tab",{attrs:{slot:"title",default:"default",name:"components",label:"Components"},slot:"title"}),a("q-tab",{attrs:{slot:"title",name:"componentsSets",label:"Components Sets"},slot:"title"}),a("q-tab",{attrs:{slot:"title",name:"componentsTables",label:"Components Tables"},slot:"title"}),a("q-tab",{attrs:{slot:"title",name:"meshes",label:"Meshes"},slot:"title"}),a("q-tab",{attrs:{slot:"title",name:"containers",label:"Containers"},slot:"title"}),a("q-tab-pane",{attrs:{name:"components"}},[a("t-select")],1),a("q-tab-pane",{attrs:{name:"componentsSets"}},[e._v("Components Sets search")]),a("q-tab-pane",{attrs:{name:"componentsTables"}},[e._v("Components Tables search")]),a("q-tab-pane",{attrs:{name:"meshes"}},[e._v("Meshes search")]),a("q-tab-pane",{attrs:{name:"containers"}},[e._v("Containers search")]),e._v(e._s(e.childItem))],1)};var G=[];var K={name:"TylkoMenuItemMultipleSearch",props:["childItem"]};var ee=K;var te=Object(S["a"])(ee,J,G,false,null,"f2930124",null);var ae=te.exports;var ne={components:{"t-menu-multiple-search":ae},name:"TylkoMenuItem",props:["item","activeCollection"]};var re=ne;var ie=a("b666");var oe=Object(S["a"])(re,Q,H,false,null,"ac861fb6",null);var le=oe.exports;var se=function(){var e=this;var t=e.$createElement;var a=e._self._c||t;return a("div",{staticClass:"fullscreen-search",class:{opened:e.isOpened}},[a("q-layout",[a("q-page-container",[a("div",{staticClass:"row"},[a("div",{staticClass:"col-11"},[a("q-search",{staticClass:"q-mb-md",attrs:{dark:true,placeholder:"Start typing name",color:"white"},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}}),a("span",{staticClass:"q-mr-xl"},[e._v("Search in:")]),e._l(e.fields,function(t,n){return a("q-checkbox",{key:n,staticClass:"q-mr-md",attrs:{label:t.label},model:{value:t.active,callback:function(a){e.$set(t,"active",a)},expression:"field.active"}})})],2),a("div",{staticClass:"col-1 fullscreen-search--close"},[a("span",{on:{click:e.toggleSearch}},[e._v("Close")])])]),a("div",{staticClass:"row q-pt-xl"},[a("div",{staticClass:"col-12 fullscreen-search--header"},[a("h3",[e._v("Results for collection: "+e._s(e.activeCollection)+" - for "+e._s(e.search))])]),a("div",{staticClass:"col-12 fullscreen-search--results"},e._l(e.activeFileds,function(t,n){return a("div",{key:n,staticClass:"fullscreen-search--column"},[a("h6",[e._v(e._s(t.label))]),a("transition-group",{attrs:{name:"list",tag:"div"}},e._l(e.filterName(e.dataFromApi[t.name]),function(n,r){return a("div",{key:r,staticClass:"fullscreen-search--column-item"},[a("div",{staticClass:"q-mb-sm"},[e._v(e._s(n.name))]),t.name==="mesh"?a("router-link",{attrs:{to:"/preview/mesh/"+n.id}},[a("q-btn",{attrs:{icon:"remove_red_eye",size:"xs"},on:{click:e.toggleSearch}})],1):e._e(),a("router-link",{attrs:{to:"/"+(t.linkName||t.name)+"/"+e.currentColelction+"/"+n.id},on:{click:e.toggleSearch}},[a("q-btn",{attrs:{icon:"edit",size:"xs"},on:{click:e.toggleSearch}})],1)],1)}),0)],1)}),0)])])],1)],1)};var ce=[];var ue=a("5dca");var de=a("f5ae");var fe=a("2f26");var ve=a("2d08");var me=a("359c");var pe=a("7341");var he=a("fb2b");var be={data:function e(){return{search:"",fields:[{name:"component-set",label:"Component Set",active:true,model:"",linkName:"components"},{name:"component-table",label:"Component Table",active:true,model:""},{name:"mesh",label:"Meshes",active:true,model:"",linkName:"meshes"},{name:"containers",label:"Container",active:true,model:""}],currentColelction:this.$route.params.selectedCollection}},watch:{$route:function e(){this.currentColelction=this.$route.params.selectedCollection}},computed:{activeFileds:function e(){return this.fields.filter(function(e){return e.active===true})},mergedData:function e(){var t=this;return;var a=Object.keys(this.dataFromApi);var e=[];a.map(function(a){t.dataFromApi[a].each(function(t,n){t.type=a;t.index=n;e.push(t)})});return e}},methods:{searchMethod:function e(t,a){var n=filter(t,{field:"name",list:this.mergedData})},filterName:function e(t){var a=this;if(typeof t==="undefined")return[];var n=Array.from(t);return n.filter(function(e){return e.name.toLowerCase().match(a.search.toLowerCase())})}},name:"TylkoMenuItemMultipleSearch",props:["isOpened","activeCollection","toggleSearch","dataFromApi"]};var ye=be;var Ce=a("6e531");var we=Object(S["a"])(ye,se,ce,false,null,"3413cd34",null);var ge=we.exports;var _e=a("202d");var ke={name:"t-menu",props:["mode","behaviour","selected","compact"],data:function e(){return{lastCollection:-1,checked:false,isOpenedSearch:false,elements:[],components:[],meshes:[],restocking:false,showNotifications:false,dataFromApi:{},menu:[{title:"Home",icon:"home",child:true,opened:false,route:"/"},{title:"Search",icon:"search",child:true,opened:false,route:"/",children:[{type:"multipleSearch",searchList:[{model:"",label:"Components"},{model:"",label:"Components Sets"},{model:"",label:"Components Tables"},{model:"",label:"Meshes"},{model:"",label:"Containers"},{model:"",label:"Relations"}]}]},{title:"Preview",icon:"remove_red_eye",child:false,opened:false,route:"/"}]}},watch:{$route:function e(t,a){}},components:{"t-menu-item":le,"t-menu-fullscreen-search":ge},methods:{toggleSearch:function e(){if(!this.$route.params.selectedCollection)return;if(this.lastCollection!=this.$route.selectedCollection){this.dataFromApi={};this.$forceUpdate()}if(this.isOpenedSearch===false)this.getAllFromAPI();this.isOpenedSearch=!this.isOpenedSearch},getAllFromAPI:function(){var e=m()(d.a.mark(function e(){return d.a.wrap(function e(t){while(1){switch(t.prev=t.next){case 0:console.log("getAllFromAPI");t.next=3;return q["a"].api.palette.collection(this.$route.params.selectedCollection).componentSet.components.meshes.fetch();case 3:this.dataFromApi=t.sent;this.lastCollection=this.$route.selectedCollection;case 5:case"end":return t.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),navigate:function e(t){console.log(t,this)}}};var xe=ke;var Oe=a("df8f");var Pe=Object(S["a"])(xe,B,W,false,null,"378754fc",null);var qe=Pe.exports;var $e=a("0fe3");var De=a("7b01");var je=a("c098");var Se={props:[],data:function e(){return{collections:[],pane:{delta:0,max:-1,show:false,created:false}}},components:{"t-menu":V,"t-menu-search":qe},created:function e(){this.loadAllCollections()},computed:{paneState:function e(){var t=document.getElementById("tylkoDesignerLayout");if(t)t.classList.toggle("pane");return{show:this.pane.show}}},updated:function e(){var t=this;this.paneUpdate();if(!this.pane.created){this.$refs.collectionsBar.addEventListener("mousewheel",function(e){t.scroll(e)});this.pane.created=true}},watch:{collections:function e(){}},methods:{scroll:function e(t){if(t){var a=je(t);this.pane.delta+=a.pixelY}if(this.pane.delta<0)this.pane.delta=0;if(this.pane.delta>this.pane.max)this.pane.delta=this.pane.max;this.$refs.collectionPane.style.transform="translateX(".concat(-this.pane.delta,"px)")},loadAllCollections:function(){var e=m()(d.a.mark(function e(){var t,a;return d.a.wrap(function e(n){while(1){switch(n.prev=n.next){case 0:n.next=2;return q["a"].api.palette.collection().fetch();case 2:t=n.sent;a=t.collection;this.collections=a;case 5:case"end":return n.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),showCollectionsQuick:function e(t){if(t!=undefined){this.pane.show=false}else{this.pane.show=this.pane.show==true?false:true}var a=document.querySelector(".selected-collection");if(a){var n=a.getBoundingClientRect();this.pane.delta=n.left;this.scroll()}},hide:function e(){if(this.pane.show==false)return;this.showCollectionsQuick(false)},paneUpdate:function e(){if(this.collections.length>0){this.pane.max=this.$refs.collectionPane.getBoundingClientRect().width-window.innerWidth}}},directives:{ClickOutside:h.a},mounted:function e(){var t=this;window.addEventListener("resize",function(){return t.paneUpdate()})}};var Me=Se;var Ae=a("4757");var Ne=Object(S["a"])(Me,s,c,false,null,"a9b65fae",null);var Ee=Ne.exports;var Ie={name:"LayoutDefault",components:{"t-navigation":Ee},data:function e(){return{drawer:true}},methods:{openURL:l,created:function e(){}}};var Le=Ie;var Fe=a("b356");var Xe=Object(S["a"])(Le,n,r,false,null,null,null);var ze=t["default"]=Xe.exports},d9cd:function(e,t,a){},df8f:function(e,t,a){"use strict";var n=a("d9cd");var r=a.n(n);var i=r.a},e3c1:function(e,t,a){"use strict";var n=a("98ab");var r=a("5a3a");e.exports=function(e,t,a){if(t in e)n.f(e,t,r(0,a));else e[t]=a}},e652:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"requiredUnless",prop:t},function(e,a){return!(0,n.ref)(t,this,a)?(0,n.req)(e):true})};t.default=r},e67d:function(e,t){function a(e){if(typeof e.value!=="function"){console.warn("[Vue-click-outside:] provided expression",e.expression,"is not a function.");return false}return true}function n(e,t){if(!e||!t)return false;for(var a=0,n=t.length;a<n;a++){try{if(e.contains(t[a])){return true}if(t[a].contains(e)){return false}}catch(r){return false}}return false}function r(e){return typeof e.componentInstance!=="undefined"&&e.componentInstance.$isServer}t=e.exports={bind:function(e,t,i){if(!a(t))return;function o(t){if(!i.context)return;var a=t.path||t.composedPath&&t.composedPath();a&&a.length>0&&a.unshift(t.target);if(e.contains(t.target)||n(i.context.popupItem,a))return;e.__vueClickOutside__.callback(t)}e.__vueClickOutside__={handler:o,callback:t.value};!r(i)&&document.addEventListener("click",o)},update:function(e,t){if(a(t))e.__vueClickOutside__.callback=t.value},unbind:function(e,t,a){!r(a)&&document.removeEventListener("click",e.__vueClickOutside__.handler);delete e.__vueClickOutside__}}},e886:function(e,t,a){},eb66:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t){return(0,n.withParams)({type:"minValue",min:t},function(e){return!(0,n.req)(e)||(!/\s/.test(e)||e instanceof Date)&&+e>=+t})};t.default=r},ec11:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.default=void 0;var n=a("78ef");var r=function e(t,a){return(0,n.withParams)({type:"between",min:t,max:a},function(e){return!(0,n.req)(e)||(!/\s/.test(e)||e instanceof Date)&&+t<=+e&&+a>=+e})};t.default=r},f0e1:function(e,t,a){},f5ae:function(e,t,a){a("d2c4")("match",1,function(e,t,a){return[function a(n){"use strict";var r=e(this);var i=n==undefined?undefined:n[t];return i!==undefined?i.call(n,r):new RegExp(n)[t](String(r))},a]})}}]);
//# sourceMappingURL=d997cee6.d72b5912.js.map