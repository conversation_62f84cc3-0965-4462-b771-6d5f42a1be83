(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["fe80db62"],{"00f4":function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(t,r,n,a,o,s,u,l,h){i(this,e);this.lineWidth=t;this.lineAlignment=h;this.nativeLines=u;this.lineColor=r;this.lineAlpha=n;this._lineTint=r;this.fillColor=a;this.fillAlpha=o;this._fillTint=a;this.fill=s;this.holes=[];this.shape=l;this.type=l.type}e.prototype.clone=function t(){return new e(this.lineWidth,this.lineColor,this.lineAlpha,this.fillColor,this.fillAlpha,this.fill,this.nativeLines,this.shape,this.lineAlignment)};e.prototype.addHole=function e(t){this.holes.push(t)};e.prototype.destroy=function e(){this.shape=null;this.holes=null};return e}();t.default=n},"0120":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=d(n);var o=r("73f5");var s=c(o);var u=r("a7a2");var l=c(u);var h=r("14b7");var f=c(h);function c(e){return e&&e.__esModule?e:{default:e}}function d(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function p(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function v(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function y(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var g=function(e){y(t,e);function t(r,i,n,o){p(this,t);o=o||5;var u=(0,s.default)(o,false);var h=(0,l.default)(o);var f=v(this,e.call(this,u,h));f.resolution=n||a.settings.RESOLUTION;f._quality=0;f.quality=i||4;f.strength=r||8;f.firstRun=true;return f}t.prototype.apply=function e(t,r,i,n){if(this.firstRun){var a=t.renderer.gl;var o=(0,f.default)(a);this.vertexSrc=(0,s.default)(o,false);this.fragmentSrc=(0,l.default)(o);this.firstRun=false}this.uniforms.strength=1/i.size.height*(i.size.height/r.size.height);this.uniforms.strength*=this.strength;this.uniforms.strength/=this.passes;if(this.passes===1){t.applyFilter(this,r,i,n)}else{var u=t.getRenderTarget(true);var h=r;var c=u;for(var d=0;d<this.passes-1;d++){t.applyFilter(this,h,c,true);var p=c;c=h;h=p}t.applyFilter(this,h,i,n);t.returnRenderTarget(u)}};i(t,[{key:"blur",get:function e(){return this.strength},set:function e(t){this.padding=Math.abs(t)*2;this.strength=t}},{key:"quality",get:function e(){return this._quality},set:function e(t){this._quality=t;this.passes=t}}]);return t}(a.Filter);t.default=g},"063c":function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(t){i(this,e);this.renderer=t;this.renderer.on("context",this.onContextChange,this)}e.prototype.onContextChange=function e(){};e.prototype.destroy=function e(){this.renderer.off("context",this.onContextChange,this);this.renderer=null};return e}();t.default=n},"073b":function(e,t,r){"use strict";t.__esModule=true;var i=r("c88f");var n=r("a506");var a=r("f16a");var o=u(a);var s=r("aa9d");function u(e){return e&&e.__esModule?e:{default:e}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var h=function(){function e(t){l(this,e);this.renderer=t;this.gl=t.gl;this._managedTextures=[]}e.prototype.bindTexture=function e(){};e.prototype.getTexture=function e(){};e.prototype.updateTexture=function e(t,r){var a=this.gl;var s=!!t._glRenderTargets;if(!t.hasLoaded){return null}var u=this.renderer.boundTextures;if(r===undefined){r=0;for(var l=0;l<u.length;++l){if(u[l]===t){r=l;break}}}u[r]=t;a.activeTexture(a.TEXTURE0+r);var h=t._glTextures[this.renderer.CONTEXT_UID];if(!h){if(s){var f=new o.default(this.gl,t.width,t.height,t.scaleMode,t.resolution);f.resize(t.width,t.height);t._glRenderTargets[this.renderer.CONTEXT_UID]=f;h=f.texture;if(!this.renderer._activeRenderTarget.root){this.renderer._activeRenderTarget.frameBuffer.bind()}}else{h=new i.GLTexture(this.gl,null,null,null,null);h.bind(r);h.premultiplyAlpha=true;h.upload(t.source)}t._glTextures[this.renderer.CONTEXT_UID]=h;t.on("update",this.updateTexture,this);t.on("dispose",this.destroyTexture,this);this._managedTextures.push(t);if(t.isPowerOfTwo){if(t.mipmap){h.enableMipmap()}if(t.wrapMode===n.WRAP_MODES.CLAMP){h.enableWrapClamp()}else if(t.wrapMode===n.WRAP_MODES.REPEAT){h.enableWrapRepeat()}else{h.enableWrapMirrorRepeat()}}else{h.enableWrapClamp()}if(t.scaleMode===n.SCALE_MODES.NEAREST){h.enableNearestScaling()}else{h.enableLinearScaling()}}else if(s){t._glRenderTargets[this.renderer.CONTEXT_UID].resize(t.width,t.height)}else{h.upload(t.source)}return h};e.prototype.destroyTexture=function e(t,r){t=t.baseTexture||t;if(!t.hasLoaded){return}var i=this.renderer;var n=i.CONTEXT_UID;var a=t._glTextures;var o=t._glRenderTargets;if(a[n]){i.unbindTexture(t);a[n].destroy();t.off("update",this.updateTexture,this);t.off("dispose",this.destroyTexture,this);delete a[n];if(!r){var u=this._managedTextures.indexOf(t);if(u!==-1){(0,s.removeItems)(this._managedTextures,u,1)}}}if(o&&o[n]){if(i._activeRenderTarget===o[n]){i.bindRenderTarget(i.rootRenderTarget)}o[n].destroy();delete o[n]}};e.prototype.removeAll=function e(){for(var t=0;t<this._managedTextures.length;++t){var r=this._managedTextures[t];if(r._glTextures[this.renderer.CONTEXT_UID]){delete r._glTextures[this.renderer.CONTEXT_UID]}}};e.prototype.destroy=function e(){for(var t=0;t<this._managedTextures.length;++t){var r=this._managedTextures[t];this.destroyTexture(r,true);r.off("update",this.updateTexture,this);r.off("dispose",this.destroyTexture,this)}this._managedTextures=null};return e}();t.default=h},"07b7":function(e,t){var r=function(e,t,r,i,n){this.gl=e;this.texture=e.createTexture();this.mipmap=false;this.premultiplyAlpha=false;this.width=t||-1;this.height=r||-1;this.format=i||e.RGBA;this.type=n||e.UNSIGNED_BYTE};r.prototype.upload=function(e){this.bind();var t=this.gl;t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,this.premultiplyAlpha);var r=e.videoWidth||e.width;var i=e.videoHeight||e.height;if(i!==this.height||r!==this.width){t.texImage2D(t.TEXTURE_2D,0,this.format,this.format,this.type,e)}else{t.texSubImage2D(t.TEXTURE_2D,0,0,0,this.format,this.type,e)}this.width=r;this.height=i};var i=false;r.prototype.uploadData=function(e,t,r){this.bind();var n=this.gl;if(e instanceof Float32Array){if(!i){var a=n.getExtension("OES_texture_float");if(a){i=true}else{throw new Error("floating point textures not available")}}this.type=n.FLOAT}else{this.type=this.type||n.UNSIGNED_BYTE}n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,this.premultiplyAlpha);if(t!==this.width||r!==this.height){n.texImage2D(n.TEXTURE_2D,0,this.format,t,r,0,this.format,this.type,e||null)}else{n.texSubImage2D(n.TEXTURE_2D,0,0,0,t,r,this.format,this.type,e||null)}this.width=t;this.height=r};r.prototype.bind=function(e){var t=this.gl;if(e!==undefined){t.activeTexture(t.TEXTURE0+e)}t.bindTexture(t.TEXTURE_2D,this.texture)};r.prototype.unbind=function(){var e=this.gl;e.bindTexture(e.TEXTURE_2D,null)};r.prototype.minFilter=function(e){var t=this.gl;this.bind();if(this.mipmap){t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,e?t.LINEAR_MIPMAP_LINEAR:t.NEAREST_MIPMAP_NEAREST)}else{t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,e?t.LINEAR:t.NEAREST)}};r.prototype.magFilter=function(e){var t=this.gl;this.bind();t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,e?t.LINEAR:t.NEAREST)};r.prototype.enableMipmap=function(){var e=this.gl;this.bind();this.mipmap=true;e.generateMipmap(e.TEXTURE_2D)};r.prototype.enableLinearScaling=function(){this.minFilter(true);this.magFilter(true)};r.prototype.enableNearestScaling=function(){this.minFilter(false);this.magFilter(false)};r.prototype.enableWrapClamp=function(){var e=this.gl;this.bind();e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE)};r.prototype.enableWrapRepeat=function(){var e=this.gl;this.bind();e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.REPEAT);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.REPEAT)};r.prototype.enableWrapMirrorRepeat=function(){var e=this.gl;this.bind();e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.MIRRORED_REPEAT);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.MIRRORED_REPEAT)};r.prototype.destroy=function(){var e=this.gl;e.deleteTexture(this.texture)};r.fromSource=function(e,t,i){var n=new r(e);n.premultiplyAlpha=i||false;n.upload(t);return n};r.fromData=function(e,t,i,n){var a=new r(e);a.uploadData(t,i,n);return a};e.exports=r},"0b1a":function(e,t){var r=function(e,t,r,n){var a=i(e,e.VERTEX_SHADER,t);var o=i(e,e.FRAGMENT_SHADER,r);var s=e.createProgram();e.attachShader(s,a);e.attachShader(s,o);if(n){for(var u in n){e.bindAttribLocation(s,n[u],u)}}e.linkProgram(s);if(!e.getProgramParameter(s,e.LINK_STATUS)){console.error("Pixi.js Error: Could not initialize shader.");console.error("gl.VALIDATE_STATUS",e.getProgramParameter(s,e.VALIDATE_STATUS));console.error("gl.getError()",e.getError());if(e.getProgramInfoLog(s)!==""){console.warn("Pixi.js Warning: gl.getProgramInfoLog()",e.getProgramInfoLog(s))}e.deleteProgram(s);s=null}e.deleteShader(a);e.deleteShader(o);return s};var i=function(e,t,r){var i=e.createShader(t);e.shaderSource(i,r);e.compileShader(i);if(!e.getShaderParameter(i,e.COMPILE_STATUS)){console.log(e.getShaderInfoLog(i));return null}return i};e.exports=r},"0e7f":function(e,t,r){"use strict";t.__esModule=true;var i=r("b018");Object.defineProperty(t,"Mesh",{enumerable:true,get:function e(){return l(i).default}});var n=r("a7ce");Object.defineProperty(t,"MeshRenderer",{enumerable:true,get:function e(){return l(n).default}});var a=r("e08d");Object.defineProperty(t,"CanvasMeshRenderer",{enumerable:true,get:function e(){return l(a).default}});var o=r("12ee");Object.defineProperty(t,"Plane",{enumerable:true,get:function e(){return l(o).default}});var s=r("4891");Object.defineProperty(t,"NineSlicePlane",{enumerable:true,get:function e(){return l(s).default}});var u=r("d427");Object.defineProperty(t,"Rope",{enumerable:true,get:function e(){return l(u).default}});function l(e){return e&&e.__esModule?e:{default:e}}},"0ee1":function(e,t,r){"use strict";t.__esModule=true;var i=r("3745");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function u(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var l=function(e){u(t,e);function t(r){o(this,t);return s(this,e.call(this,r,["attribute vec2 aVertexPosition;","attribute vec4 aColor;","uniform mat3 translationMatrix;","uniform mat3 projectionMatrix;","uniform float alpha;","uniform vec3 tint;","varying vec4 vColor;","void main(void){","   gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);","   vColor = aColor * vec4(tint * alpha, alpha);","}"].join("\n"),["varying vec4 vColor;","void main(void){","   gl_FragColor = vColor;","}"].join("\n")))}return t}(n.default);t.default=l},"0fe2":function(e,t,r){"use strict";t.__esModule=true;var i=r("063c");var n=g(i);var a=r("f16a");var o=g(a);var s=r("3ee0");var u=g(s);var l=r("774e");var h=r("3745");var f=g(h);var c=r("ad41");var d=y(c);var p=r("a48a");var v=g(p);function y(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function g(e){return e&&e.__esModule?e:{default:e}}function _(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function m(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function b(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var x=function(){function e(){b(this,e);this.renderTarget=null;this.target=null;this.resolution=1;this.sourceFrame=new l.Rectangle;this.destinationFrame=new l.Rectangle;this.filters=[]}e.prototype.clear=function e(){this.filters=null;this.target=null;this.renderTarget=null};return e}();var T="screen";var E=function(e){m(t,e);function t(r){b(this,t);var i=_(this,e.call(this,r));i.gl=i.renderer.gl;i.quad=new u.default(i.gl,r.state.attribState);i.shaderCache={};i.pool={};i.filterData=null;i.managedFilters=[];i.renderer.on("prerender",i.onPrerender,i);i._screenWidth=r.view.width;i._screenHeight=r.view.height;return i}t.prototype.pushFilter=function e(t,r){var i=this.renderer;var n=this.filterData;if(!n){n=this.renderer._activeRenderTarget.filterStack;var a=new x;a.sourceFrame=a.destinationFrame=this.renderer._activeRenderTarget.size;a.renderTarget=i._activeRenderTarget;this.renderer._activeRenderTarget.filterData=n={index:0,stack:[a]};this.filterData=n}var o=n.stack[++n.index];var s=n.stack[0].destinationFrame;if(!o){o=n.stack[n.index]=new x}var u=t.filterArea&&t.filterArea.x===0&&t.filterArea.y===0&&t.filterArea.width===i.screen.width&&t.filterArea.height===i.screen.height;var l=r[0].resolution;var h=r[0].padding|0;var f=u?i.screen:t.filterArea||t.getBounds(true);var c=o.sourceFrame;var d=o.destinationFrame;c.x=(f.x*l|0)/l;c.y=(f.y*l|0)/l;c.width=(f.width*l|0)/l;c.height=(f.height*l|0)/l;if(!u){if(n.stack[0].renderTarget.transform){}else if(r[0].autoFit){c.fit(s)}c.pad(h)}d.width=c.width;d.height=c.height;var p=this.getPotRenderTarget(i.gl,c.width,c.height,l);o.target=t;o.filters=r;o.resolution=l;o.renderTarget=p;p.setFrame(d,c);i.bindRenderTarget(p);p.clear()};t.prototype.popFilter=function e(){var t=this.filterData;var r=t.stack[t.index-1];var i=t.stack[t.index];this.quad.map(i.renderTarget.size,i.sourceFrame).upload();var n=i.filters;if(n.length===1){n[0].apply(this,i.renderTarget,r.renderTarget,false,i);this.freePotRenderTarget(i.renderTarget)}else{var a=i.renderTarget;var o=this.getPotRenderTarget(this.renderer.gl,i.sourceFrame.width,i.sourceFrame.height,i.resolution);o.setFrame(i.destinationFrame,i.sourceFrame);o.clear();var s=0;for(s=0;s<n.length-1;++s){n[s].apply(this,a,o,true,i);var u=a;a=o;o=u}n[s].apply(this,a,r.renderTarget,false,i);this.freePotRenderTarget(a);this.freePotRenderTarget(o)}i.clear();t.index--;if(t.index===0){this.filterData=null}};t.prototype.applyFilter=function e(t,r,i,n){var a=this.renderer;var o=a.gl;var s=t.glShaders[a.CONTEXT_UID];if(!s){if(t.glShaderKey){s=this.shaderCache[t.glShaderKey];if(!s){s=new f.default(this.gl,t.vertexSrc,t.fragmentSrc);t.glShaders[a.CONTEXT_UID]=this.shaderCache[t.glShaderKey]=s;this.managedFilters.push(t)}}else{s=t.glShaders[a.CONTEXT_UID]=new f.default(this.gl,t.vertexSrc,t.fragmentSrc);this.managedFilters.push(t)}a.bindVao(null);this.quad.initVao(s)}a.bindVao(this.quad.vao);a.bindRenderTarget(i);if(n){o.disable(o.SCISSOR_TEST);a.clear();o.enable(o.SCISSOR_TEST)}if(i===a.maskManager.scissorRenderTarget){a.maskManager.pushScissorMask(null,a.maskManager.scissorData)}a.bindShader(s);var u=this.renderer.emptyTextures[0];this.renderer.boundTextures[0]=u;this.syncUniforms(s,t);a.state.setBlendMode(t.blendMode);o.activeTexture(o.TEXTURE0);o.bindTexture(o.TEXTURE_2D,r.texture.texture);this.quad.vao.draw(this.renderer.gl.TRIANGLES,6,0);o.bindTexture(o.TEXTURE_2D,u._glTextures[this.renderer.CONTEXT_UID].texture)};t.prototype.syncUniforms=function e(t,r){var i=r.uniformData;var n=r.uniforms;var a=1;var o=void 0;if(t.uniforms.filterArea){o=this.filterData.stack[this.filterData.index];var s=t.uniforms.filterArea;s[0]=o.renderTarget.size.width;s[1]=o.renderTarget.size.height;s[2]=o.sourceFrame.x;s[3]=o.sourceFrame.y;t.uniforms.filterArea=s}if(t.uniforms.filterClamp){o=o||this.filterData.stack[this.filterData.index];var u=t.uniforms.filterClamp;u[0]=0;u[1]=0;u[2]=(o.sourceFrame.width-1)/o.renderTarget.size.width;u[3]=(o.sourceFrame.height-1)/o.renderTarget.size.height;t.uniforms.filterClamp=u}for(var l in i){if(!t.uniforms.data[l]){continue}var h=i[l].type;if(h==="sampler2d"&&n[l]!==0){if(n[l].baseTexture){t.uniforms[l]=this.renderer.bindTexture(n[l].baseTexture,a)}else{t.uniforms[l]=a;var f=this.renderer.gl;this.renderer.boundTextures[a]=this.renderer.emptyTextures[a];f.activeTexture(f.TEXTURE0+a);n[l].texture.bind()}a++}else if(h==="mat3"){if(n[l].a!==undefined){t.uniforms[l]=n[l].toArray(true)}else{t.uniforms[l]=n[l]}}else if(h==="vec2"){if(n[l].x!==undefined){var c=t.uniforms[l]||new Float32Array(2);c[0]=n[l].x;c[1]=n[l].y;t.uniforms[l]=c}else{t.uniforms[l]=n[l]}}else if(h==="float"){if(t.uniforms.data[l].value!==i[l]){t.uniforms[l]=n[l]}}else{t.uniforms[l]=n[l]}}};t.prototype.getRenderTarget=function e(t,r){var i=this.filterData.stack[this.filterData.index];var n=this.getPotRenderTarget(this.renderer.gl,i.sourceFrame.width,i.sourceFrame.height,r||i.resolution);n.setFrame(i.destinationFrame,i.sourceFrame);return n};t.prototype.returnRenderTarget=function e(t){this.freePotRenderTarget(t)};t.prototype.calculateScreenSpaceMatrix=function e(t){var r=this.filterData.stack[this.filterData.index];return d.calculateScreenSpaceMatrix(t,r.sourceFrame,r.renderTarget.size)};t.prototype.calculateNormalizedScreenSpaceMatrix=function e(t){var r=this.filterData.stack[this.filterData.index];return d.calculateNormalizedScreenSpaceMatrix(t,r.sourceFrame,r.renderTarget.size,r.destinationFrame)};t.prototype.calculateSpriteMatrix=function e(t,r){var i=this.filterData.stack[this.filterData.index];return d.calculateSpriteMatrix(t,i.sourceFrame,i.renderTarget.size,r)};t.prototype.destroy=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var r=this.renderer;var i=this.managedFilters;r.off("prerender",this.onPrerender,this);for(var n=0;n<i.length;n++){if(!t){i[n].glShaders[r.CONTEXT_UID].destroy()}delete i[n].glShaders[r.CONTEXT_UID]}this.shaderCache={};if(!t){this.emptyPool()}else{this.pool={}}};t.prototype.getPotRenderTarget=function e(t,r,i,n){var a=T;r*=n;i*=n;if(r!==this._screenWidth||i!==this._screenHeight){r=v.default.nextPow2(r);i=v.default.nextPow2(i);a=(r&65535)<<16|i&65535}if(!this.pool[a]){this.pool[a]=[]}var s=this.pool[a].pop();if(!s){var u=this.renderer.boundTextures[0];t.activeTexture(t.TEXTURE0);s=new o.default(t,r,i,null,1);t.bindTexture(t.TEXTURE_2D,u._glTextures[this.renderer.CONTEXT_UID].texture)}s.resolution=n;s.defaultFrame.width=s.size.width=r/n;s.defaultFrame.height=s.size.height=i/n;s.filterPoolKey=a;return s};t.prototype.emptyPool=function e(){for(var t in this.pool){var r=this.pool[t];if(r){for(var i=0;i<r.length;i++){r[i].destroy(true)}}}this.pool={}};t.prototype.freePotRenderTarget=function e(t){this.pool[t.filterPoolKey].push(t)};t.prototype.onPrerender=function e(){if(this._screenWidth!==this.renderer.view.width||this._screenHeight!==this.renderer.view.height){this._screenWidth=this.renderer.view.width;this._screenHeight=this.renderer.view.height;var t=this.pool[T];if(t){for(var r=0;r<t.length;r++){t[r].destroy(true)}}this.pool[T]=[]}};return t}(n.default);t.default=E},"12ee":function(e,t,r){"use strict";t.__esModule=true;var i=r("b018");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function u(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var l=function(e){u(t,e);function t(r,i,a){o(this,t);var u=s(this,e.call(this,r));u._ready=true;u.verticesX=i||10;u.verticesY=a||10;u.drawMode=n.default.DRAW_MODES.TRIANGLES;u.refresh();return u}t.prototype._refresh=function e(){var t=this._texture;var r=this.verticesX*this.verticesY;var i=[];var n=[];var a=[];var o=[];var s=this.verticesX-1;var u=this.verticesY-1;var l=t.width/s;var h=t.height/u;for(var f=0;f<r;f++){var c=f%this.verticesX;var d=f/this.verticesX|0;i.push(c*l,d*h);a.push(c/s,d/u)}var p=s*u;for(var v=0;v<p;v++){var y=v%s;var g=v/s|0;var _=g*this.verticesX+y;var m=g*this.verticesX+y+1;var b=(g+1)*this.verticesX+y;var x=(g+1)*this.verticesX+y+1;o.push(_,m,b);o.push(m,x,b)}this.vertices=new Float32Array(i);this.uvs=new Float32Array(a);this.colors=new Float32Array(n);this.indices=new Uint16Array(o);this.dirty++;this.indexDirty++;this.multiplyUvs()};t.prototype._onTextureUpdate=function e(){n.default.prototype._onTextureUpdate.call(this);if(this._ready){this.refresh()}};return t}(n.default);t.default=l},"148f":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("aa9d");var a=r("774e");var o=r("a506");var s=r("61df");var u=v(s);var l=r("6bf3");var h=v(l);var f=r("7881");var c=v(f);var d=r("ba10");var p=v(d);function v(e){return e&&e.__esModule?e:{default:e}}function y(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function g(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function _(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var m=new a.Matrix;var b=function(e){_(t,e);function t(r,i,s,l){y(this,t);var f=g(this,e.call(this));(0,n.sayHello)(r);if(typeof i==="number"){i=Object.assign({width:i,height:s||u.default.RENDER_OPTIONS.height},l)}i=Object.assign({},u.default.RENDER_OPTIONS,i);f.options=i;f.type=o.RENDERER_TYPE.UNKNOWN;f.screen=new a.Rectangle(0,0,i.width,i.height);f.view=i.view||document.createElement("canvas");f.resolution=i.resolution||u.default.RESOLUTION;f.transparent=i.transparent;f.autoResize=i.autoResize||false;f.blendModes=null;f.preserveDrawingBuffer=i.preserveDrawingBuffer;f.clearBeforeRender=i.clearBeforeRender;f.roundPixels=i.roundPixels;f._backgroundColor=0;f._backgroundColorRgba=[0,0,0,0];f._backgroundColorString="#000000";f.backgroundColor=i.backgroundColor||f._backgroundColor;f._tempDisplayObjectParent=new h.default;f._lastObjectRendered=f._tempDisplayObjectParent;return f}t.prototype.resize=function e(t,r){this.screen.width=t;this.screen.height=r;this.view.width=t*this.resolution;this.view.height=r*this.resolution;if(this.autoResize){this.view.style.width=t+"px";this.view.style.height=r+"px"}};t.prototype.generateTexture=function e(t,r,i,n){n=n||t.getLocalBounds();var a=c.default.create(n.width|0,n.height|0,r,i);m.tx=-n.x;m.ty=-n.y;this.render(t,a,false,m,!!t.parent);return a};t.prototype.destroy=function e(t){if(t&&this.view.parentNode){this.view.parentNode.removeChild(this.view)}this.type=o.RENDERER_TYPE.UNKNOWN;this.view=null;this.screen=null;this.resolution=0;this.transparent=false;this.autoResize=false;this.blendModes=null;this.options=null;this.preserveDrawingBuffer=false;this.clearBeforeRender=false;this.roundPixels=false;this._backgroundColor=0;this._backgroundColorRgba=null;this._backgroundColorString=null;this._tempDisplayObjectParent=null;this._lastObjectRendered=null};i(t,[{key:"width",get:function e(){return this.view.width}},{key:"height",get:function e(){return this.view.height}},{key:"backgroundColor",get:function e(){return this._backgroundColor},set:function e(t){this._backgroundColor=t;this._backgroundColorString=(0,n.hex2string)(t);(0,n.hex2rgb)(t,this._backgroundColorRgba)}}]);return t}(p.default);t.default=b},"14b7":function(e,t,r){"use strict";t.__esModule=true;t.default=i;function i(e){var t=e.getParameter(e.MAX_VARYING_VECTORS);var r=15;while(r>t){r-=2}return r}},"14d6":function(e,t,r){var i=r("07b7");var n=function(e,t,r){this.gl=e;this.framebuffer=e.createFramebuffer();this.stencil=null;this.texture=null;this.width=t||100;this.height=r||100};n.prototype.enableTexture=function(e){var t=this.gl;this.texture=e||new i(t);this.texture.bind();this.bind();t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,this.texture.texture,0)};n.prototype.enableStencil=function(){if(this.stencil)return;var e=this.gl;this.stencil=e.createRenderbuffer();e.bindRenderbuffer(e.RENDERBUFFER,this.stencil);e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_STENCIL_ATTACHMENT,e.RENDERBUFFER,this.stencil);e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_STENCIL,this.width,this.height)};n.prototype.clear=function(e,t,r,i){this.bind();var n=this.gl;n.clearColor(e,t,r,i);n.clear(n.COLOR_BUFFER_BIT|n.DEPTH_BUFFER_BIT)};n.prototype.bind=function(){var e=this.gl;e.bindFramebuffer(e.FRAMEBUFFER,this.framebuffer)};n.prototype.unbind=function(){var e=this.gl;e.bindFramebuffer(e.FRAMEBUFFER,null)};n.prototype.resize=function(e,t){var r=this.gl;this.width=e;this.height=t;if(this.texture){this.texture.uploadData(null,e,t)}if(this.stencil){r.bindRenderbuffer(r.RENDERBUFFER,this.stencil);r.renderbufferStorage(r.RENDERBUFFER,r.DEPTH_STENCIL,e,t)}};n.prototype.destroy=function(){var e=this.gl;if(this.texture){this.texture.destroy()}e.deleteFramebuffer(this.framebuffer);this.gl=null;this.stencil=null;this.texture=null};n.createRGBA=function(e,t,r,a){var o=i.fromData(e,null,t,r);o.enableNearestScaling();o.enableWrapClamp();var s=new n(e,t,r);s.enableTexture(o);s.unbind();return s};n.createFloat32=function(e,t,r,a){var o=new i.fromData(e,a,t,r);o.enableNearestScaling();o.enableWrapClamp();var s=new n(e,t,r);s.enableTexture(o);s.unbind();return s};e.exports=n},"1cdc":function(e,t){var r=new ArrayBuffer(0);var i=function(e,t,i,n){this.gl=e;this.buffer=e.createBuffer();this.type=t||e.ARRAY_BUFFER;this.drawType=n||e.STATIC_DRAW;this.data=r;if(i){this.upload(i)}this._updateID=0};i.prototype.upload=function(e,t,r){if(!r)this.bind();var i=this.gl;e=e||this.data;t=t||0;if(this.data.byteLength>=e.byteLength){i.bufferSubData(this.type,t,e)}else{i.bufferData(this.type,e,this.drawType)}this.data=e};i.prototype.bind=function(){var e=this.gl;e.bindBuffer(this.type,this.buffer)};i.createVertexBuffer=function(e,t,r){return new i(e,e.ARRAY_BUFFER,t,r)};i.createIndexBuffer=function(e,t,r){return new i(e,e.ELEMENT_ARRAY_BUFFER,t,r)};i.create=function(e,t,r,n){return new i(e,t,r,n)};i.prototype.destroy=function(){this.gl.deleteBuffer(this.buffer)};e.exports=i},"1cfe":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();function n(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(t,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;n(this,e);this._x=i;this._y=a;this.cb=t;this.scope=r}e.prototype.clone=function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var n=r||this.cb;var a=i||this.scope;return new e(n,a,this._x,this._y)};e.prototype.set=function e(t,r){var i=t||0;var n=r||(r!==0?i:0);if(this._x!==i||this._y!==n){this._x=i;this._y=n;this.cb.call(this.scope)}};e.prototype.copy=function e(t){if(this._x!==t.x||this._y!==t.y){this._x=t.x;this._y=t.y;this.cb.call(this.scope)}};e.prototype.equals=function e(t){return t.x===this._x&&t.y===this._y};i(e,[{key:"x",get:function e(){return this._x},set:function e(t){if(this._x!==t){this._x=t;this.cb.call(this.scope)}}},{key:"y",get:function e(){return this._y},set:function e(t){if(this._y!==t){this._y=t;this.cb.call(this.scope)}}}]);return e}();t.default=a},"1fdb":function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=u(i);var a=r("99e3");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function h(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function f(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var c=16;var d=function(e){f(t,e);function t(r){l(this,t);var i=h(this,e.call(this,r));i.uploadHookHelper=i;i.canvas=document.createElement("canvas");i.canvas.width=c;i.canvas.height=c;i.ctx=i.canvas.getContext("2d");i.registerUploadHook(p);return i}t.prototype.destroy=function t(){e.prototype.destroy.call(this);this.ctx=null;this.canvas=null};return t}(o.default);t.default=d;function p(e,t){if(t instanceof n.BaseTexture){var r=t.source;var i=r.width===0?e.canvas.width:Math.min(e.canvas.width,r.width);var a=r.height===0?e.canvas.height:Math.min(e.canvas.height,r.height);e.ctx.drawImage(r,0,0,i,a,0,0,e.canvas.width,e.canvas.height);return true}return false}n.CanvasRenderer.registerPlugin("prepare",d)},"22ce":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=s(n);var o=r("df7c");function s(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:.5;var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:Math.random();u(this,t);var n=l(this,e.call(this,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n    vTextureCoord = aTextureCoord;\n}","precision highp float;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\n\nuniform float uNoise;\nuniform float uSeed;\nuniform sampler2D uSampler;\n\nfloat rand(vec2 co)\n{\n    return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);\n}\n\nvoid main()\n{\n    vec4 color = texture2D(uSampler, vTextureCoord);\n    float randomValue = rand(gl_FragCoord.xy * uSeed);\n    float diff = (randomValue - 0.5) * uNoise;\n\n    // Un-premultiply alpha before applying the color matrix. See issue #3539.\n    if (color.a > 0.0) {\n        color.rgb /= color.a;\n    }\n\n    color.r += diff;\n    color.g += diff;\n    color.b += diff;\n\n    // Premultiply alpha again.\n    color.rgb *= color.a;\n\n    gl_FragColor = color;\n}\n"));n.noise=r;n.seed=i;return n}i(t,[{key:"noise",get:function e(){return this.uniforms.uNoise},set:function e(t){this.uniforms.uNoise=t}},{key:"seed",get:function e(){return this.uniforms.uSeed},set:function e(t){this.uniforms.uSeed=t}}]);return t}(a.Filter);t.default=f},"238d":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=d(n);var o=r("73f5");var s=c(o);var u=r("a7a2");var l=c(u);var h=r("14b7");var f=c(h);function c(e){return e&&e.__esModule?e:{default:e}}function d(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function p(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function v(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function y(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var g=function(e){y(t,e);function t(r,i,n,o){p(this,t);o=o||5;var u=(0,s.default)(o,true);var h=(0,l.default)(o);var f=v(this,e.call(this,u,h));f.resolution=n||a.settings.RESOLUTION;f._quality=0;f.quality=i||4;f.strength=r||8;f.firstRun=true;return f}t.prototype.apply=function e(t,r,i,n){if(this.firstRun){var a=t.renderer.gl;var o=(0,f.default)(a);this.vertexSrc=(0,s.default)(o,true);this.fragmentSrc=(0,l.default)(o);this.firstRun=false}this.uniforms.strength=1/i.size.width*(i.size.width/r.size.width);this.uniforms.strength*=this.strength;this.uniforms.strength/=this.passes;if(this.passes===1){t.applyFilter(this,r,i,n)}else{var u=t.getRenderTarget(true);var h=r;var c=u;for(var d=0;d<this.passes-1;d++){t.applyFilter(this,h,c,true);var p=c;c=h;h=p}t.applyFilter(this,h,i,n);t.returnRenderTarget(u)}};i(t,[{key:"blur",get:function e(){return this.strength},set:function e(t){this.padding=Math.abs(t)*2;this.strength=t}},{key:"quality",get:function e(){return this._quality},set:function e(t){this._quality=t;this.passes=t}}]);return t}(a.Filter);t.default=g},"24b2":function(e,t,r){"use strict";t.__esModule=true;t.default=i;function i(){var e=!!navigator.platform&&/iPad|iPhone|iPod/.test(navigator.platform);return!e}},2545:function(e,t){var r=function(e,t,r){var i;if(r){var n=r.tempAttribState,a=r.attribState;for(i=0;i<n.length;i++){n[i]=false}for(i=0;i<t.length;i++){n[t[i].attribute.location]=true}for(i=0;i<a.length;i++){if(a[i]!==n[i]){a[i]=n[i];if(r.attribState[i]){e.enableVertexAttribArray(i)}else{e.disableVertexAttribArray(i)}}}}else{for(i=0;i<t.length;i++){var o=t[i];e.enableVertexAttribArray(o.attribute.location)}}};e.exports=r},"25e8":function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=o(i);var a=r("df7c");function o(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function u(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function l(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var h=function(e){l(t,e);function t(){s(this,t);return u(this,e.call(this,"\nattribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 v_rgbNW;\nvarying vec2 v_rgbNE;\nvarying vec2 v_rgbSW;\nvarying vec2 v_rgbSE;\nvarying vec2 v_rgbM;\n\nuniform vec4 filterArea;\n\nvarying vec2 vTextureCoord;\n\nvec2 mapCoord( vec2 coord )\n{\n    coord *= filterArea.xy;\n    coord += filterArea.zw;\n\n    return coord;\n}\n\nvec2 unmapCoord( vec2 coord )\n{\n    coord -= filterArea.zw;\n    coord /= filterArea.xy;\n\n    return coord;\n}\n\nvoid texcoords(vec2 fragCoord, vec2 resolution,\n               out vec2 v_rgbNW, out vec2 v_rgbNE,\n               out vec2 v_rgbSW, out vec2 v_rgbSE,\n               out vec2 v_rgbM) {\n    vec2 inverseVP = 1.0 / resolution.xy;\n    v_rgbNW = (fragCoord + vec2(-1.0, -1.0)) * inverseVP;\n    v_rgbNE = (fragCoord + vec2(1.0, -1.0)) * inverseVP;\n    v_rgbSW = (fragCoord + vec2(-1.0, 1.0)) * inverseVP;\n    v_rgbSE = (fragCoord + vec2(1.0, 1.0)) * inverseVP;\n    v_rgbM = vec2(fragCoord * inverseVP);\n}\n\nvoid main(void) {\n\n   gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n   vTextureCoord = aTextureCoord;\n\n   vec2 fragCoord = vTextureCoord * filterArea.xy;\n\n   texcoords(fragCoord, filterArea.xy, v_rgbNW, v_rgbNE, v_rgbSW, v_rgbSE, v_rgbM);\n}",'varying vec2 v_rgbNW;\nvarying vec2 v_rgbNE;\nvarying vec2 v_rgbSW;\nvarying vec2 v_rgbSE;\nvarying vec2 v_rgbM;\n\nvarying vec2 vTextureCoord;\nuniform sampler2D uSampler;\nuniform vec4 filterArea;\n\n/**\n Basic FXAA implementation based on the code on geeks3d.com with the\n modification that the texture2DLod stuff was removed since it\'s\n unsupported by WebGL.\n \n --\n \n From:\n https://github.com/mitsuhiko/webgl-meincraft\n \n Copyright (c) 2011 by Armin Ronacher.\n \n Some rights reserved.\n \n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions are\n met:\n \n * Redistributions of source code must retain the above copyright\n notice, this list of conditions and the following disclaimer.\n \n * Redistributions in binary form must reproduce the above\n copyright notice, this list of conditions and the following\n disclaimer in the documentation and/or other materials provided\n with the distribution.\n \n * The names of the contributors may not be used to endorse or\n promote products derived from this software without specific\n prior written permission.\n \n THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n#ifndef FXAA_REDUCE_MIN\n#define FXAA_REDUCE_MIN   (1.0/ 128.0)\n#endif\n#ifndef FXAA_REDUCE_MUL\n#define FXAA_REDUCE_MUL   (1.0 / 8.0)\n#endif\n#ifndef FXAA_SPAN_MAX\n#define FXAA_SPAN_MAX     8.0\n#endif\n\n//optimized version for mobile, where dependent\n//texture reads can be a bottleneck\nvec4 fxaa(sampler2D tex, vec2 fragCoord, vec2 resolution,\n          vec2 v_rgbNW, vec2 v_rgbNE,\n          vec2 v_rgbSW, vec2 v_rgbSE,\n          vec2 v_rgbM) {\n    vec4 color;\n    mediump vec2 inverseVP = vec2(1.0 / resolution.x, 1.0 / resolution.y);\n    vec3 rgbNW = texture2D(tex, v_rgbNW).xyz;\n    vec3 rgbNE = texture2D(tex, v_rgbNE).xyz;\n    vec3 rgbSW = texture2D(tex, v_rgbSW).xyz;\n    vec3 rgbSE = texture2D(tex, v_rgbSE).xyz;\n    vec4 texColor = texture2D(tex, v_rgbM);\n    vec3 rgbM  = texColor.xyz;\n    vec3 luma = vec3(0.299, 0.587, 0.114);\n    float lumaNW = dot(rgbNW, luma);\n    float lumaNE = dot(rgbNE, luma);\n    float lumaSW = dot(rgbSW, luma);\n    float lumaSE = dot(rgbSE, luma);\n    float lumaM  = dot(rgbM,  luma);\n    float lumaMin = min(lumaM, min(min(lumaNW, lumaNE), min(lumaSW, lumaSE)));\n    float lumaMax = max(lumaM, max(max(lumaNW, lumaNE), max(lumaSW, lumaSE)));\n    \n    mediump vec2 dir;\n    dir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));\n    dir.y =  ((lumaNW + lumaSW) - (lumaNE + lumaSE));\n    \n    float dirReduce = max((lumaNW + lumaNE + lumaSW + lumaSE) *\n                          (0.25 * FXAA_REDUCE_MUL), FXAA_REDUCE_MIN);\n    \n    float rcpDirMin = 1.0 / (min(abs(dir.x), abs(dir.y)) + dirReduce);\n    dir = min(vec2(FXAA_SPAN_MAX, FXAA_SPAN_MAX),\n              max(vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX),\n                  dir * rcpDirMin)) * inverseVP;\n    \n    vec3 rgbA = 0.5 * (\n                       texture2D(tex, fragCoord * inverseVP + dir * (1.0 / 3.0 - 0.5)).xyz +\n                       texture2D(tex, fragCoord * inverseVP + dir * (2.0 / 3.0 - 0.5)).xyz);\n    vec3 rgbB = rgbA * 0.5 + 0.25 * (\n                                     texture2D(tex, fragCoord * inverseVP + dir * -0.5).xyz +\n                                     texture2D(tex, fragCoord * inverseVP + dir * 0.5).xyz);\n    \n    float lumaB = dot(rgbB, luma);\n    if ((lumaB < lumaMin) || (lumaB > lumaMax))\n        color = vec4(rgbA, texColor.a);\n    else\n        color = vec4(rgbB, texColor.a);\n    return color;\n}\n\nvoid main() {\n\n      vec2 fragCoord = vTextureCoord * filterArea.xy;\n\n      vec4 color;\n\n    color = fxaa(uSampler, fragCoord, filterArea.xy, v_rgbNW, v_rgbNE, v_rgbSW, v_rgbSE, v_rgbM);\n\n      gl_FragColor = color;\n}\n'))}return t}(n.Filter);t.default=h},2641:function(e,t,r){"use strict";t.__esModule=true;t.Loader=undefined;var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var a=r("d5e4");var o=d(a);var s=r("bbad");var u=d(s);var l=r("51d3");var h=c(l);var f=r("5f08");function c(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function d(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var v=100;var y=/(#[\w-]+)?$/;var g=t.Loader=function(){function e(){var t=this;var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"";var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:10;p(this,e);this.baseUrl=r;this.progress=0;this.loading=false;this.defaultQueryString="";this._beforeMiddleware=[];this._afterMiddleware=[];this._resourcesParsing=[];this._boundLoadResource=function(e,r){return t._loadResource(e,r)};this._queue=h.queue(this._boundLoadResource,i);this._queue.pause();this.resources={};this.onProgress=new o.default;this.onError=new o.default;this.onLoad=new o.default;this.onStart=new o.default;this.onComplete=new o.default;for(var n=0;n<e._defaultBeforeMiddleware.length;++n){this.pre(e._defaultBeforeMiddleware[n])}for(var a=0;a<e._defaultAfterMiddleware.length;++a){this.use(e._defaultAfterMiddleware[a])}}e.prototype.add=function e(t,r,n,a){if(Array.isArray(t)){for(var o=0;o<t.length;++o){this.add(t[o])}return this}if((typeof t==="undefined"?"undefined":i(t))==="object"){a=r||t.callback||t.onComplete;n=t;r=t.url;t=t.name||t.key||t.url}if(typeof r!=="string"){a=n;n=r;r=t}if(typeof r!=="string"){throw new Error("No url passed to add resource to loader.")}if(typeof n==="function"){a=n;n=null}if(this.loading&&(!n||!n.parentResource)){throw new Error("Cannot add resources while the loader is running.")}if(this.resources[t]){throw new Error('Resource named "'+t+'" already exists.')}r=this._prepareUrl(r);this.resources[t]=new f.Resource(t,r,n);if(typeof a==="function"){this.resources[t].onAfterMiddleware.once(a)}if(this.loading){var s=n.parentResource;var u=[];for(var l=0;l<s.children.length;++l){if(!s.children[l].isComplete){u.push(s.children[l])}}var h=s.progressChunk*(u.length+1);var c=h/(u.length+2);s.children.push(this.resources[t]);s.progressChunk=c;for(var d=0;d<u.length;++d){u[d].progressChunk=c}this.resources[t].progressChunk=c}this._queue.push(this.resources[t]);return this};e.prototype.pre=function e(t){this._beforeMiddleware.push(t);return this};e.prototype.use=function e(t){this._afterMiddleware.push(t);return this};e.prototype.reset=function e(){this.progress=0;this.loading=false;this._queue.kill();this._queue.pause();for(var t in this.resources){var r=this.resources[t];if(r._onLoadBinding){r._onLoadBinding.detach()}if(r.isLoading){r.abort()}}this.resources={};return this};e.prototype.load=function e(t){if(typeof t==="function"){this.onComplete.once(t)}if(this.loading){return this}if(this._queue.idle()){this._onStart();this._onComplete()}else{var r=this._queue._tasks.length;var i=v/r;for(var n=0;n<this._queue._tasks.length;++n){this._queue._tasks[n].data.progressChunk=i}this._onStart();this._queue.resume()}return this};e.prototype._prepareUrl=function e(t){var r=(0,u.default)(t,{strictMode:true});var i=void 0;if(r.protocol||!r.path||t.indexOf("//")===0){i=t}else if(this.baseUrl.length&&this.baseUrl.lastIndexOf("/")!==this.baseUrl.length-1&&t.charAt(0)!=="/"){i=this.baseUrl+"/"+t}else{i=this.baseUrl+t}if(this.defaultQueryString){var n=y.exec(i)[0];i=i.substr(0,i.length-n.length);if(i.indexOf("?")!==-1){i+="&"+this.defaultQueryString}else{i+="?"+this.defaultQueryString}i+=n}return i};e.prototype._loadResource=function e(t,r){var i=this;t._dequeue=r;h.eachSeries(this._beforeMiddleware,function(e,r){e.call(i,t,function(){r(t.isComplete?{}:null)})},function(){if(t.isComplete){i._onLoad(t)}else{t._onLoadBinding=t.onComplete.once(i._onLoad,i);t.load()}},true)};e.prototype._onStart=function e(){this.progress=0;this.loading=true;this.onStart.dispatch(this)};e.prototype._onComplete=function e(){this.progress=v;this.loading=false;this.onComplete.dispatch(this,this.resources)};e.prototype._onLoad=function e(t){var r=this;t._onLoadBinding=null;this._resourcesParsing.push(t);t._dequeue();h.eachSeries(this._afterMiddleware,function(e,i){e.call(r,t,i)},function(){t.onAfterMiddleware.dispatch(t);r.progress=Math.min(v,r.progress+t.progressChunk);r.onProgress.dispatch(r,t);if(t.error){r.onError.dispatch(t.error,r,t)}else{r.onLoad.dispatch(r,t)}r._resourcesParsing.splice(r._resourcesParsing.indexOf(t),1);if(r._queue.idle()&&r._resourcesParsing.length===0){r._onComplete()}},true)};n(e,[{key:"concurrency",get:function e(){return this._queue.concurrency},set:function e(t){this._queue.concurrency=t}}]);return e}();g._defaultBeforeMiddleware=[];g._defaultAfterMiddleware=[];g.pre=function e(t){g._defaultBeforeMiddleware.push(t);return g};g.use=function e(t){g._defaultAfterMiddleware.push(t);return g}},"281c":function(e,t,r){"use strict";t.__esModule=true;var i=r("774e");function n(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(){n(this,e);this.worldTransform=new i.Matrix;this.localTransform=new i.Matrix;this._worldID=0;this._parentID=0}e.prototype.updateLocalTransform=function e(){};e.prototype.updateTransform=function e(t){var r=t.worldTransform;var i=this.worldTransform;var n=this.localTransform;i.a=n.a*r.a+n.b*r.c;i.b=n.a*r.b+n.b*r.d;i.c=n.c*r.a+n.d*r.c;i.d=n.c*r.b+n.d*r.d;i.tx=n.tx*r.a+n.ty*r.c+r.tx;i.ty=n.tx*r.b+n.ty*r.d+r.ty;this._worldID++};return e}();t.default=a;a.prototype.updateWorldTransform=a.prototype.updateTransform;a.IDENTITY=new a},"2af6":function(e,t,r){"use strict";t.__esModule=true;t.default=l;var i=r("e4ec");var n=u(i);var a=r("aa9d");var o=r("5664");var s=u(o);function u(e){return e&&e.__esModule?e:{default:e}}function l(e,t,r){e.points=e.shape.points.slice();var i=e.points;if(e.fill&&i.length>=6){var o=[];var u=e.holes;for(var l=0;l<u.length;l++){var h=u[l];o.push(i.length/2);i=i.concat(h.points)}var f=t.points;var c=t.indices;var d=i.length/2;var p=(0,a.hex2rgb)(e.fillColor);var v=e.fillAlpha;var y=p[0]*v;var g=p[1]*v;var _=p[2]*v;var m=(0,s.default)(i,o,2);if(!m){return}var b=f.length/6;for(var x=0;x<m.length;x+=3){c.push(m[x]+b);c.push(m[x]+b);c.push(m[x+1]+b);c.push(m[x+2]+b);c.push(m[x+2]+b)}for(var T=0;T<d;T++){f.push(i[T*2],i[T*2+1],y,g,_,v)}}if(e.lineWidth>0){(0,n.default)(e,t,r)}}},"2bd4":function(e,t){var r=function(e,t){var r=e.getContext("webgl",t)||e.getContext("experimental-webgl",t);if(!r){throw new Error("This browser does not support webGL. Try using the canvas renderer")}return r};e.exports=r},"2cd2":function(e,t,r){"use strict";t.__esModule=true;var i=r("a557");var n=l(i);var a=r("a506");var o=r("774e");var s=r("fd43");var u=l(s);function l(e){return e&&e.__esModule?e:{default:e}}function h(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=new o.Matrix;var c=function(){function e(t){h(this,e);this.renderer=t}e.prototype.render=function e(t){var r=t._texture;var i=this.renderer;var n=r._frame.width;var s=r._frame.height;var l=t.transform.worldTransform;var h=0;var c=0;if(r.orig.width<=0||r.orig.height<=0||!r.baseTexture.source){return}i.setBlendMode(t.blendMode);if(r.valid){i.context.globalAlpha=t.worldAlpha;var d=r.baseTexture.scaleMode===a.SCALE_MODES.LINEAR;if(i.smoothProperty&&i.context[i.smoothProperty]!==d){i.context[i.smoothProperty]=d}if(r.trim){h=r.trim.width/2+r.trim.x-t.anchor.x*r.orig.width;c=r.trim.height/2+r.trim.y-t.anchor.y*r.orig.height}else{h=(.5-t.anchor.x)*r.orig.width;c=(.5-t.anchor.y)*r.orig.height}if(r.rotate){l.copy(f);l=f;o.GroupD8.matrixAppendRotationInv(l,r.rotate,h,c);h=0;c=0}h-=n/2;c-=s/2;if(i.roundPixels){i.context.setTransform(l.a,l.b,l.c,l.d,l.tx*i.resolution|0,l.ty*i.resolution|0);h=h|0;c=c|0}else{i.context.setTransform(l.a,l.b,l.c,l.d,l.tx*i.resolution,l.ty*i.resolution)}var p=r.baseTexture.resolution;if(t.tint!==16777215){if(t.cachedTint!==t.tint||t.tintedTexture.tintId!==t._texture._updateID){t.cachedTint=t.tint;t.tintedTexture=u.default.getTintedTexture(t,t.tint)}i.context.drawImage(t.tintedTexture,0,0,n*p,s*p,h*i.resolution,c*i.resolution,n*i.resolution,s*i.resolution)}else{i.context.drawImage(r.baseTexture.source,r._frame.x*p,r._frame.y*p,n*p,s*p,h*i.resolution,c*i.resolution,n*i.resolution,s*i.resolution)}}};e.prototype.destroy=function e(){this.renderer=null};return e}();t.default=c;n.default.registerPlugin("sprite",c)},"320c":function(e,t,r){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var i=Object.getOwnPropertySymbols;var n=Object.prototype.hasOwnProperty;var a=Object.prototype.propertyIsEnumerable;function o(e){if(e===null||e===undefined){throw new TypeError("Object.assign cannot be called with null or undefined")}return Object(e)}function s(){try{if(!Object.assign){return false}var e=new String("abc");e[5]="de";if(Object.getOwnPropertyNames(e)[0]==="5"){return false}var t={};for(var r=0;r<10;r++){t["_"+String.fromCharCode(r)]=r}var i=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if(i.join("")!=="**********"){return false}var n={};"abcdefghijklmnopqrst".split("").forEach(function(e){n[e]=e});if(Object.keys(Object.assign({},n)).join("")!=="abcdefghijklmnopqrst"){return false}return true}catch(a){return false}}e.exports=s()?Object.assign:function(e,t){var r;var s=o(e);var u;for(var l=1;l<arguments.length;l++){r=Object(arguments[l]);for(var h in r){if(n.call(r,h)){s[h]=r[h]}}if(i){u=i(r);for(var f=0;f<u.length;f++){if(a.call(r,u[f])){s[u[f]]=r[u[f]]}}}}return s}},3232:function(e,t,r){"use strict";t.__esModule=true;t.default=s;var i=r("e4ec");var n=o(i);var a=r("aa9d");function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t,r){var i=e.shape;var o=i.x;var s=i.y;var u=i.width;var l=i.height;if(e.fill){var h=(0,a.hex2rgb)(e.fillColor);var f=e.fillAlpha;var c=h[0]*f;var d=h[1]*f;var p=h[2]*f;var v=t.points;var y=t.indices;var g=v.length/6;v.push(o,s);v.push(c,d,p,f);v.push(o+u,s);v.push(c,d,p,f);v.push(o,s+l);v.push(c,d,p,f);v.push(o+u,s+l);v.push(c,d,p,f);y.push(g,g,g+1,g+2,g+3,g+3)}if(e.lineWidth){var _=e.points;e.points=[o,s,o+u,s,o+u,s+l,o,s+l,o,s];(0,n.default)(e,t,r);e.points=_}}},3342:function(e,t,r){"use strict";var i=r("320c");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}if(!Object.assign){Object.assign=n.default}},"33a2":function(e,t,r){"use strict";t.__esModule=true;var i=r("fe6b");Object.defineProperty(t,"webgl",{enumerable:true,get:function e(){return u(i).default}});var n=r("1fdb");Object.defineProperty(t,"canvas",{enumerable:true,get:function e(){return u(n).default}});var a=r("99e3");Object.defineProperty(t,"BasePrepare",{enumerable:true,get:function e(){return u(a).default}});var o=r("40d5");Object.defineProperty(t,"CountLimiter",{enumerable:true,get:function e(){return u(o).default}});var s=r("7ec4");Object.defineProperty(t,"TimeLimiter",{enumerable:true,get:function e(){return u(s).default}});function u(e){return e&&e.__esModule?e:{default:e}}},"344f":function(e,t,r){"use strict";t.__esModule=true;var i=r("063c");var n=s(i);var a=r("85ef");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(r){u(this,t);var i=l(this,e.call(this,r));i.scissor=false;i.scissorData=null;i.scissorRenderTarget=null;i.enableScissor=true;i.alphaMaskPool=[];i.alphaMaskIndex=0;return i}t.prototype.pushMask=function e(t,r){if(r.texture){this.pushSpriteMask(t,r)}else if(this.enableScissor&&!this.scissor&&this.renderer._activeRenderTarget.root&&!this.renderer.stencilManager.stencilMaskStack.length&&r.isFastRect()){var i=r.worldTransform;var n=Math.atan2(i.b,i.a);n=Math.round(n*(180/Math.PI));if(n%90){this.pushStencilMask(r)}else{this.pushScissorMask(t,r)}}else{this.pushStencilMask(r)}};t.prototype.popMask=function e(t,r){if(r.texture){this.popSpriteMask(t,r)}else if(this.enableScissor&&!this.renderer.stencilManager.stencilMaskStack.length){this.popScissorMask(t,r)}else{this.popStencilMask(t,r)}};t.prototype.pushSpriteMask=function e(t,r){var i=this.alphaMaskPool[this.alphaMaskIndex];if(!i){i=this.alphaMaskPool[this.alphaMaskIndex]=[new o.default(r)]}i[0].resolution=this.renderer.resolution;i[0].maskSprite=r;var n=t.filterArea;t.filterArea=r.getBounds(true);this.renderer.filterManager.pushFilter(t,i);t.filterArea=n;this.alphaMaskIndex++};t.prototype.popSpriteMask=function e(){this.renderer.filterManager.popFilter();this.alphaMaskIndex--};t.prototype.pushStencilMask=function e(t){this.renderer.currentRenderer.stop();this.renderer.stencilManager.pushStencil(t)};t.prototype.popStencilMask=function e(){this.renderer.currentRenderer.stop();this.renderer.stencilManager.popStencil()};t.prototype.pushScissorMask=function e(t,r){r.renderable=true;var i=this.renderer._activeRenderTarget;var n=r.getBounds();n.fit(i.size);r.renderable=false;this.renderer.gl.enable(this.renderer.gl.SCISSOR_TEST);var a=this.renderer.resolution;this.renderer.gl.scissor(n.x*a,(i.root?i.size.height-n.y-n.height:n.y)*a,n.width*a,n.height*a);this.scissorRenderTarget=i;this.scissorData=r;this.scissor=true};t.prototype.popScissorMask=function e(){this.scissorRenderTarget=null;this.scissorData=null;this.scissor=false;var t=this.renderer.gl;t.disable(t.SCISSOR_TEST)};return t}(n.default);t.default=f},"34ee":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("61df");var a=l(n);var o=r("a506");var s=r("c576");var u=l(s);function l(e){return e&&e.__esModule?e:{default:e}}function h(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=function(){function e(){var t=this;h(this,e);this._head=new u.default(null,null,Infinity);this._requestId=null;this._maxElapsedMS=100;this.autoStart=false;this.deltaTime=1;this.elapsedMS=1/a.default.TARGET_FPMS;this.lastTime=-1;this.speed=1;this.started=false;this._tick=function(e){t._requestId=null;if(t.started){t.update(e);if(t.started&&t._requestId===null&&t._head.next){t._requestId=requestAnimationFrame(t._tick)}}}}e.prototype._requestIfNeeded=function e(){if(this._requestId===null&&this._head.next){this.lastTime=performance.now();this._requestId=requestAnimationFrame(this._tick)}};e.prototype._cancelIfNeeded=function e(){if(this._requestId!==null){cancelAnimationFrame(this._requestId);this._requestId=null}};e.prototype._startIfPossible=function e(){if(this.started){this._requestIfNeeded()}else if(this.autoStart){this.start()}};e.prototype.add=function e(t,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:o.UPDATE_PRIORITY.NORMAL;return this._addListener(new u.default(t,r,i))};e.prototype.addOnce=function e(t,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:o.UPDATE_PRIORITY.NORMAL;return this._addListener(new u.default(t,r,i,true))};e.prototype._addListener=function e(t){var r=this._head.next;var i=this._head;if(!r){t.connect(i)}else{while(r){if(t.priority>r.priority){t.connect(i);break}i=r;r=r.next}if(!t.previous){t.connect(i)}}this._startIfPossible();return this};e.prototype.remove=function e(t,r){var i=this._head.next;while(i){if(i.match(t,r)){i=i.destroy()}else{i=i.next}}if(!this._head.next){this._cancelIfNeeded()}return this};e.prototype.start=function e(){if(!this.started){this.started=true;this._requestIfNeeded()}};e.prototype.stop=function e(){if(this.started){this.started=false;this._cancelIfNeeded()}};e.prototype.destroy=function e(){this.stop();var t=this._head.next;while(t){t=t.destroy(true)}this._head.destroy();this._head=null};e.prototype.update=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:performance.now();var r=void 0;if(t>this.lastTime){r=this.elapsedMS=t-this.lastTime;if(r>this._maxElapsedMS){r=this._maxElapsedMS}this.deltaTime=r*a.default.TARGET_FPMS*this.speed;var i=this._head;var n=i.next;while(n){n=n.emit(this.deltaTime)}if(!i.next){this._cancelIfNeeded()}}else{this.deltaTime=this.elapsedMS=0}this.lastTime=t};i(e,[{key:"FPS",get:function e(){return 1e3/this.elapsedMS}},{key:"minFPS",get:function e(){return 1e3/this._maxElapsedMS},set:function e(t){var r=Math.min(Math.max(0,t)/1e3,a.default.TARGET_FPMS);this._maxElapsedMS=1/r}}]);return e}();t.default=f},"36fe":function(e,t,r){"use strict";t.__esModule=true;var i=r("a506");var n=r("61df");var a=o(n);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(t){s(this,e);this.renderer=t;this.count=0;this.checkCount=0;this.maxIdle=a.default.GC_MAX_IDLE;this.checkCountMax=a.default.GC_MAX_CHECK_COUNT;this.mode=a.default.GC_MODE}e.prototype.update=function e(){this.count++;if(this.mode===i.GC_MODES.MANUAL){return}this.checkCount++;if(this.checkCount>this.checkCountMax){this.checkCount=0;this.run()}};e.prototype.run=function e(){var t=this.renderer.textureManager;var r=t._managedTextures;var i=false;for(var n=0;n<r.length;n++){var a=r[n];if(!a._glRenderTargets&&this.count-a.touched>this.maxIdle){t.destroyTexture(a,true);r[n]=null;i=true}}if(i){var o=0;for(var s=0;s<r.length;s++){if(r[s]!==null){r[o++]=r[s]}}r.length=o}};e.prototype.unload=function e(t){var r=this.renderer.textureManager;if(t._texture&&t._texture._glRenderTargets){r.destroyTexture(t._texture,true)}for(var i=t.children.length-1;i>=0;i--){this.unload(t.children[i])}};return e}();t.default=u},"371d":function(e,t,r){"use strict";t.__esModule=true;t.default=function(){return function e(t,r){var n=t.name+"_image";if(!t.data||t.type!==i.Resource.TYPE.JSON||!t.data.frames||this.resources[n]){r();return}var a={crossOrigin:t.crossOrigin,metadata:t.metadata.imageMetadata,parentResource:t};var s=u(t,this.baseUrl);this.add(n,s,a,function e(i){if(i.error){r(i.error);return}var n=new o.Spritesheet(i.texture.baseTexture,t.data,t.url);n.parse(function(){t.spritesheet=n;t.textures=n.textures;r()})})}};t.getResourcePath=u;var i=r("cfe7");var n=r("0b16");var a=s(n);var o=r("dc07");function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(e.isDataUrl){return e.data.meta.image}return a.default.resolve(e.url.replace(t,""),e.data.meta.image)}},3745:function(e,t,r){"use strict";t.__esModule=true;var i=r("c88f");var n=r("61df");var a=o(n);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function u(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function l(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function h(e,t){if(e instanceof Array){if(e[0].substring(0,9)!=="precision"){var r=e.slice(0);r.unshift("precision "+t+" float;");return r}}else if(e.trim().substring(0,9)!=="precision"){return"precision "+t+" float;\n"+e}return e}var f=function(e){l(t,e);function t(r,i,n,o,l){s(this,t);return u(this,e.call(this,r,h(i,l||a.default.PRECISION_VERTEX),h(n,l||a.default.PRECISION_FRAGMENT),undefined,o))}return t}(i.GLShader);t.default=f},"3ee0":function(e,t,r){"use strict";t.__esModule=true;var i=r("c88f");var n=s(i);var a=r("4f24");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l=function(){function e(t,r){u(this,e);this.gl=t;this.vertices=new Float32Array([-1,-1,1,-1,1,1,-1,1]);this.uvs=new Float32Array([0,0,1,0,1,1,0,1]);this.interleaved=new Float32Array(8*2);for(var i=0;i<4;i++){this.interleaved[i*4]=this.vertices[i*2];this.interleaved[i*4+1]=this.vertices[i*2+1];this.interleaved[i*4+2]=this.uvs[i*2];this.interleaved[i*4+3]=this.uvs[i*2+1]}this.indices=(0,o.default)(1);this.vertexBuffer=n.default.GLBuffer.createVertexBuffer(t,this.interleaved,t.STATIC_DRAW);this.indexBuffer=n.default.GLBuffer.createIndexBuffer(t,this.indices,t.STATIC_DRAW);this.vao=new n.default.VertexArrayObject(t,r)}e.prototype.initVao=function e(t){this.vao.clear().addIndex(this.indexBuffer).addAttribute(this.vertexBuffer,t.attributes.aVertexPosition,this.gl.FLOAT,false,4*4,0).addAttribute(this.vertexBuffer,t.attributes.aTextureCoord,this.gl.FLOAT,false,4*4,2*4)};e.prototype.map=function e(t,r){var i=0;var n=0;this.uvs[0]=i;this.uvs[1]=n;this.uvs[2]=i+r.width/t.width;this.uvs[3]=n;this.uvs[4]=i+r.width/t.width;this.uvs[5]=n+r.height/t.height;this.uvs[6]=i;this.uvs[7]=n+r.height/t.height;i=r.x;n=r.y;this.vertices[0]=i;this.vertices[1]=n;this.vertices[2]=i+r.width;this.vertices[3]=n;this.vertices[4]=i+r.width;this.vertices[5]=n+r.height;this.vertices[6]=i;this.vertices[7]=n+r.height;return this};e.prototype.upload=function e(){for(var t=0;t<4;t++){this.interleaved[t*4]=this.vertices[t*2];this.interleaved[t*4+1]=this.vertices[t*2+1];this.interleaved[t*4+2]=this.uvs[t*2];this.interleaved[t*4+3]=this.uvs[t*2+1]}this.vertexBuffer.upload(this.interleaved);return this};e.prototype.destroy=function e(){var t=this.gl;t.deleteBuffer(this.vertexBuffer);t.deleteBuffer(this.indexBuffer)};return e}();t.default=l},"3f62":function(e,t,r){var i=r("0b1a"),n=r("ec06"),a=r("f7ce"),o=r("d035"),s=r("872d");var u=function(e,t,r,u,l){this.gl=e;if(u){t=o(t,u);r=o(r,u)}this.program=i(e,t,r,l);this.attributes=n(e,this.program);this.uniformData=a(e,this.program);this.uniforms=s(e,this.uniformData)};u.prototype.bind=function(){this.gl.useProgram(this.program);return this};u.prototype.destroy=function(){this.attributes=null;this.uniformData=null;this.uniforms=null;var e=this.gl;e.deleteProgram(this.program)};e.exports=u},"40b6":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=c(n);var o=r("1cfe");var s=f(o);var u=r("aa9d");var l=r("61df");var h=f(l);function f(e){return e&&e.__esModule?e:{default:e}}function c(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function d(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function p(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function v(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var y=function(e){v(t,e);function t(r){var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};d(this,t);var n=p(this,e.call(this));n._textWidth=0;n._textHeight=0;n._glyphs=[];n._font={tint:i.tint!==undefined?i.tint:16777215,align:i.align||"left",name:null,size:0};n.font=i.font;n._text=r;n._maxWidth=0;n._maxLineHeight=0;n._letterSpacing=0;n._anchor=new s.default(function(){n.dirty=true},n,0,0);n.dirty=false;n.updateText();return n}t.prototype.updateText=function e(){var r=t.fonts[this._font.name];var i=this._font.size/r.size;var n=new a.Point;var o=[];var s=[];var u=this.text.replace(/(?:\r\n|\r)/g,"\n");var l=u.length;var h=this._maxWidth*r.size/this._font.size;var f=null;var c=0;var d=0;var p=0;var v=-1;var y=0;var g=0;var _=0;for(var m=0;m<l;m++){var b=u.charCodeAt(m);var x=u.charAt(m);if(/(?:\s)/.test(x)){v=m;y=c}if(x==="\r"||x==="\n"){s.push(c);d=Math.max(d,c);++p;++g;n.x=0;n.y+=r.lineHeight;f=null;continue}var T=r.chars[b];if(!T){continue}if(f&&T.kerning[f]){n.x+=T.kerning[f]}o.push({texture:T.texture,line:p,charCode:b,position:new a.Point(n.x+T.xOffset+this._letterSpacing/2,n.y+T.yOffset)});n.x+=T.xAdvance+this._letterSpacing;c=n.x;_=Math.max(_,T.yOffset+T.texture.height);f=b;if(v!==-1&&h>0&&n.x>h){++g;a.utils.removeItems(o,1+v-g,1+m-v);m=v;v=-1;s.push(y);d=Math.max(d,y);p++;n.x=0;n.y+=r.lineHeight;f=null}}var E=u.charAt(u.length-1);if(E!=="\r"&&E!=="\n"){if(/(?:\s)/.test(E)){c=y}s.push(c);d=Math.max(d,c)}var w=[];for(var S=0;S<=p;S++){var O=0;if(this._font.align==="right"){O=d-s[S]}else if(this._font.align==="center"){O=(d-s[S])/2}w.push(O)}var M=o.length;var P=this.tint;for(var C=0;C<M;C++){var A=this._glyphs[C];if(A){A.texture=o[C].texture}else{A=new a.Sprite(o[C].texture);this._glyphs.push(A)}A.position.x=(o[C].position.x+w[o[C].line])*i;A.position.y=o[C].position.y*i;A.scale.x=A.scale.y=i;A.tint=P;if(!A.parent){this.addChild(A)}}for(var R=M;R<this._glyphs.length;++R){this.removeChild(this._glyphs[R])}this._textWidth=d*i;this._textHeight=(n.y+r.lineHeight)*i;if(this.anchor.x!==0||this.anchor.y!==0){for(var I=0;I<M;I++){this._glyphs[I].x-=this._textWidth*this.anchor.x;this._glyphs[I].y-=this._textHeight*this.anchor.y}}this._maxLineHeight=_*i};t.prototype.updateTransform=function e(){this.validate();this.containerUpdateTransform()};t.prototype.getLocalBounds=function t(){this.validate();return e.prototype.getLocalBounds.call(this)};t.prototype.validate=function e(){if(this.dirty){this.updateText();this.dirty=false}};t.registerFont=function e(r,i){var n={};var o=r.getElementsByTagName("info")[0];var s=r.getElementsByTagName("common")[0];var l=r.getElementsByTagName("page");var f=(0,u.getResolutionOfUrl)(l[0].getAttribute("file"),h.default.RESOLUTION);var c={};n.font=o.getAttribute("face");n.size=parseInt(o.getAttribute("size"),10);n.lineHeight=parseInt(s.getAttribute("lineHeight"),10)/f;n.chars={};if(i instanceof a.Texture){i=[i]}for(var d=0;d<l.length;d++){var p=l[d].getAttribute("id");var v=l[d].getAttribute("file");c[p]=i instanceof Array?i[d]:i[v]}var y=r.getElementsByTagName("char");for(var g=0;g<y.length;g++){var _=y[g];var m=parseInt(_.getAttribute("id"),10);var b=_.getAttribute("page")||0;var x=new a.Rectangle(parseInt(_.getAttribute("x"),10)/f+c[b].frame.x/f,parseInt(_.getAttribute("y"),10)/f+c[b].frame.y/f,parseInt(_.getAttribute("width"),10)/f,parseInt(_.getAttribute("height"),10)/f);n.chars[m]={xOffset:parseInt(_.getAttribute("xoffset"),10)/f,yOffset:parseInt(_.getAttribute("yoffset"),10)/f,xAdvance:parseInt(_.getAttribute("xadvance"),10)/f,kerning:{},texture:new a.Texture(c[b].baseTexture,x),page:b}}var T=r.getElementsByTagName("kerning");for(var E=0;E<T.length;E++){var w=T[E];var S=parseInt(w.getAttribute("first"),10)/f;var O=parseInt(w.getAttribute("second"),10)/f;var M=parseInt(w.getAttribute("amount"),10)/f;if(n.chars[O]){n.chars[O].kerning[S]=M}}t.fonts[n.font]=n;return n};i(t,[{key:"tint",get:function e(){return this._font.tint},set:function e(t){this._font.tint=typeof t==="number"&&t>=0?t:16777215;this.dirty=true}},{key:"align",get:function e(){return this._font.align},set:function e(t){this._font.align=t||"left";this.dirty=true}},{key:"anchor",get:function e(){return this._anchor},set:function e(t){if(typeof t==="number"){this._anchor.set(t)}else{this._anchor.copy(t)}}},{key:"font",get:function e(){return this._font},set:function e(r){if(!r){return}if(typeof r==="string"){r=r.split(" ");this._font.name=r.length===1?r[0]:r.slice(1).join(" ");this._font.size=r.length>=2?parseInt(r[0],10):t.fonts[this._font.name].size}else{this._font.name=r.name;this._font.size=typeof r.size==="number"?r.size:parseInt(r.size,10)}this.dirty=true}},{key:"text",get:function e(){return this._text},set:function e(t){t=t.toString()||" ";if(this._text===t){return}this._text=t;this.dirty=true}},{key:"maxWidth",get:function e(){return this._maxWidth},set:function e(t){if(this._maxWidth===t){return}this._maxWidth=t;this.dirty=true}},{key:"maxLineHeight",get:function e(){this.validate();return this._maxLineHeight}},{key:"textWidth",get:function e(){this.validate();return this._textWidth}},{key:"letterSpacing",get:function e(){return this._letterSpacing},set:function e(t){if(this._letterSpacing!==t){this._letterSpacing=t;this.dirty=true}}},{key:"textHeight",get:function e(){this.validate();return this._textHeight}}]);return t}(a.Container);t.default=y;y.fonts={}},"40d5":function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(t){i(this,e);this.maxItemsPerFrame=t;this.itemsLeft=0}e.prototype.beginFrame=function e(){this.itemsLeft=this.maxItemsPerFrame};e.prototype.allowedToUpload=function e(){return this.itemsLeft-- >0};return e}();t.default=n},"476a":function(e,t,r){"use strict";var i=r("dc07");var n=f(i);var a=r("c49b");var o=h(a);var s=r("dc99");var u=h(s);var l=r("aa9d");function h(e){return e&&e.__esModule?e:{default:e}}function f(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var d=n.DisplayObject;var p=new n.Matrix;d.prototype._cacheAsBitmap=false;d.prototype._cacheData=false;var v=function e(){c(this,e);this.textureCacheId=null;this.originalRenderWebGL=null;this.originalRenderCanvas=null;this.originalCalculateBounds=null;this.originalGetLocalBounds=null;this.originalUpdateTransform=null;this.originalHitTest=null;this.originalDestroy=null;this.originalMask=null;this.originalFilterArea=null;this.sprite=null};Object.defineProperties(d.prototype,{cacheAsBitmap:{get:function e(){return this._cacheAsBitmap},set:function e(t){if(this._cacheAsBitmap===t){return}this._cacheAsBitmap=t;var r=void 0;if(t){if(!this._cacheData){this._cacheData=new v}r=this._cacheData;r.originalRenderWebGL=this.renderWebGL;r.originalRenderCanvas=this.renderCanvas;r.originalUpdateTransform=this.updateTransform;r.originalCalculateBounds=this.calculateBounds;r.originalGetLocalBounds=this.getLocalBounds;r.originalDestroy=this.destroy;r.originalContainsPoint=this.containsPoint;r.originalMask=this._mask;r.originalFilterArea=this.filterArea;this.renderWebGL=this._renderCachedWebGL;this.renderCanvas=this._renderCachedCanvas;this.destroy=this._cacheAsBitmapDestroy}else{r=this._cacheData;if(r.sprite){this._destroyCachedDisplayObject()}this.renderWebGL=r.originalRenderWebGL;this.renderCanvas=r.originalRenderCanvas;this.calculateBounds=r.originalCalculateBounds;this.getLocalBounds=r.originalGetLocalBounds;this.destroy=r.originalDestroy;this.updateTransform=r.originalUpdateTransform;this.containsPoint=r.originalContainsPoint;this._mask=r.originalMask;this.filterArea=r.originalFilterArea}}}});d.prototype._renderCachedWebGL=function e(t){if(!this.visible||this.worldAlpha<=0||!this.renderable){return}this._initCachedDisplayObject(t);this._cacheData.sprite.transform._worldID=this.transform._worldID;this._cacheData.sprite.worldAlpha=this.worldAlpha;this._cacheData.sprite._renderWebGL(t)};d.prototype._initCachedDisplayObject=function e(t){if(this._cacheData&&this._cacheData.sprite){return}var r=this.alpha;this.alpha=1;t.currentRenderer.flush();var i=this.getLocalBounds().clone();if(this._filters&&this._filters.length){var a=this._filters[0].padding;i.pad(a)}i.ceil(n.settings.RESOLUTION);var s=t._activeRenderTarget;var h=t.filterManager.filterStack;var f=n.RenderTexture.create(i.width,i.height);var c="cacheAsBitmap_"+(0,l.uid)();this._cacheData.textureCacheId=c;u.default.addToCache(f.baseTexture,c);o.default.addToCache(f,c);var d=p;d.tx=-i.x;d.ty=-i.y;this.transform.worldTransform.identity();this.renderWebGL=this._cacheData.originalRenderWebGL;t.render(this,f,true,d,true);t.bindRenderTarget(s);t.filterManager.filterStack=h;this.renderWebGL=this._renderCachedWebGL;this.updateTransform=this.displayObjectUpdateTransform;this.calculateBounds=this._calculateCachedBounds;this.getLocalBounds=this._getCachedLocalBounds;this._mask=null;this.filterArea=null;var v=new n.Sprite(f);v.transform.worldTransform=this.transform.worldTransform;v.anchor.x=-(i.x/i.width);v.anchor.y=-(i.y/i.height);v.alpha=r;v._bounds=this._bounds;this._cacheData.sprite=v;this.transform._parentID=-1;if(!this.parent){this.parent=t._tempDisplayObjectParent;this.updateTransform();this.parent=null}else{this.updateTransform()}this.containsPoint=v.containsPoint.bind(v)};d.prototype._renderCachedCanvas=function e(t){if(!this.visible||this.worldAlpha<=0||!this.renderable){return}this._initCachedDisplayObjectCanvas(t);this._cacheData.sprite.worldAlpha=this.worldAlpha;this._cacheData.sprite._renderCanvas(t)};d.prototype._initCachedDisplayObjectCanvas=function e(t){if(this._cacheData&&this._cacheData.sprite){return}var r=this.getLocalBounds();var i=this.alpha;this.alpha=1;var a=t.context;r.ceil(n.settings.RESOLUTION);var s=n.RenderTexture.create(r.width,r.height);var h="cacheAsBitmap_"+(0,l.uid)();this._cacheData.textureCacheId=h;u.default.addToCache(s.baseTexture,h);o.default.addToCache(s,h);var f=p;this.transform.localTransform.copy(f);f.invert();f.tx-=r.x;f.ty-=r.y;this.renderCanvas=this._cacheData.originalRenderCanvas;t.render(this,s,true,f,false);t.context=a;this.renderCanvas=this._renderCachedCanvas;this.updateTransform=this.displayObjectUpdateTransform;this.calculateBounds=this._calculateCachedBounds;this.getLocalBounds=this._getCachedLocalBounds;this._mask=null;this.filterArea=null;var c=new n.Sprite(s);c.transform.worldTransform=this.transform.worldTransform;c.anchor.x=-(r.x/r.width);c.anchor.y=-(r.y/r.height);c.alpha=i;c._bounds=this._bounds;this._cacheData.sprite=c;this.transform._parentID=-1;if(!this.parent){this.parent=t._tempDisplayObjectParent;this.updateTransform();this.parent=null}else{this.updateTransform()}this.containsPoint=c.containsPoint.bind(c)};d.prototype._calculateCachedBounds=function e(){this._bounds.clear();this._cacheData.sprite.transform._worldID=this.transform._worldID;this._cacheData.sprite._calculateBounds();this._lastBoundsID=this._boundsID};d.prototype._getCachedLocalBounds=function e(){return this._cacheData.sprite.getLocalBounds()};d.prototype._destroyCachedDisplayObject=function e(){this._cacheData.sprite._texture.destroy(true);this._cacheData.sprite=null;u.default.removeFromCache(this._cacheData.textureCacheId);o.default.removeFromCache(this._cacheData.textureCacheId);this._cacheData.textureCacheId=null};d.prototype._cacheAsBitmapDestroy=function e(t){this.cacheAsBitmap=false;this.destroy(t)}},4891:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("12ee");var a=u(n);var o=r("fd43");var s=u(o);function u(e){return e&&e.__esModule?e:{default:e}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function h(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function f(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var c=10;var d=function(e){f(t,e);function t(r,i,n,a,o){l(this,t);var s=h(this,e.call(this,r,4,4));s._origWidth=r.orig.width;s._origHeight=r.orig.height;s._width=s._origWidth;s._height=s._origHeight;s._leftWidth=typeof i!=="undefined"?i:c;s._rightWidth=typeof a!=="undefined"?a:c;s._topHeight=typeof n!=="undefined"?n:c;s._bottomHeight=typeof o!=="undefined"?o:c;s._cachedTint=16777215;s._tintedTexture=null;s._canvasUvs=null;s.refresh(true);return s}t.prototype.updateHorizontalVertices=function e(){var t=this.vertices;var r=this._topHeight+this._bottomHeight;var i=this._height>r?1:this._height/r;t[9]=t[11]=t[13]=t[15]=this._topHeight*i;t[17]=t[19]=t[21]=t[23]=this._height-this._bottomHeight*i;t[25]=t[27]=t[29]=t[31]=this._height};t.prototype.updateVerticalVertices=function e(){var t=this.vertices;var r=this._leftWidth+this._rightWidth;var i=this._width>r?1:this._width/r;t[2]=t[10]=t[18]=t[26]=this._leftWidth*i;t[4]=t[12]=t[20]=t[28]=this._width-this._rightWidth*i;t[6]=t[14]=t[22]=t[30]=this._width};t.prototype._renderCanvas=function e(t){var r=t.context;var i=this.worldTransform;var n=t.resolution;var a=this.tint!==16777215;var o=this._texture;if(a){if(this._cachedTint!==this.tint){this._cachedTint=this.tint;this._tintedTexture=s.default.getTintedTexture(this,this.tint)}}var u=!a?o.baseTexture.source:this._tintedTexture;if(!this._canvasUvs){this._canvasUvs=[0,0,0,0,0,0,0,0]}var l=this.vertices;var h=this._canvasUvs;var f=a?0:o.frame.x;var c=a?0:o.frame.y;var d=f+o.frame.width;var p=c+o.frame.height;h[0]=f;h[1]=f+this._leftWidth;h[2]=d-this._rightWidth;h[3]=d;h[4]=c;h[5]=c+this._topHeight;h[6]=p-this._bottomHeight;h[7]=p;for(var v=0;v<8;v++){h[v]*=o.baseTexture.resolution}r.globalAlpha=this.worldAlpha;t.setBlendMode(this.blendMode);if(t.roundPixels){r.setTransform(i.a*n,i.b*n,i.c*n,i.d*n,i.tx*n|0,i.ty*n|0)}else{r.setTransform(i.a*n,i.b*n,i.c*n,i.d*n,i.tx*n,i.ty*n)}for(var y=0;y<3;y++){for(var g=0;g<3;g++){var _=g*2+y*8;var m=Math.max(1,h[g+1]-h[g]);var b=Math.max(1,h[y+5]-h[y+4]);var x=Math.max(1,l[_+10]-l[_]);var T=Math.max(1,l[_+11]-l[_+1]);r.drawImage(u,h[g],h[y+4],m,b,l[_],l[_+1],x,T)}}};t.prototype._refresh=function t(){e.prototype._refresh.call(this);var r=this.uvs;var i=this._texture;this._origWidth=i.orig.width;this._origHeight=i.orig.height;var n=1/this._origWidth;var a=1/this._origHeight;r[0]=r[8]=r[16]=r[24]=0;r[1]=r[3]=r[5]=r[7]=0;r[6]=r[14]=r[22]=r[30]=1;r[25]=r[27]=r[29]=r[31]=1;r[2]=r[10]=r[18]=r[26]=n*this._leftWidth;r[4]=r[12]=r[20]=r[28]=1-n*this._rightWidth;r[9]=r[11]=r[13]=r[15]=a*this._topHeight;r[17]=r[19]=r[21]=r[23]=1-a*this._bottomHeight;this.updateHorizontalVertices();this.updateVerticalVertices();this.dirty++;this.multiplyUvs()};i(t,[{key:"width",get:function e(){return this._width},set:function e(t){this._width=t;this._refresh()}},{key:"height",get:function e(){return this._height},set:function e(t){this._height=t;this._refresh()}},{key:"leftWidth",get:function e(){return this._leftWidth},set:function e(t){this._leftWidth=t;this._refresh()}},{key:"rightWidth",get:function e(){return this._rightWidth},set:function e(t){this._rightWidth=t;this._refresh()}},{key:"topHeight",get:function e(){return this._topHeight},set:function e(t){this._topHeight=t;this._refresh()}},{key:"bottomHeight",get:function e(){return this._bottomHeight},set:function e(t){this._bottomHeight=t;this._refresh()}}]);return t}(a.default);t.default=d},"48b2":function(e,t,r){"use strict";t.__esModule=true;t.default=function(){return function e(t,r){if(t.data&&t.type===i.Resource.TYPE.IMAGE){t.texture=a.default.fromLoader(t.data,t.url,t.name)}r()}};var i=r("cfe7");var n=r("c49b");var a=o(n);function o(e){return e&&e.__esModule?e:{default:e}}},"4c5f":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=r("aa9d");function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s=function(){i(e,null,[{key:"BATCH_SIZE",get:function e(){return 1e3}}]);function e(t,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;o(this,e);this.baseTexture=t;this.textures={};this.animations={};this.data=r;this.resolution=this._updateResolution(i||this.baseTexture.imageUrl);this._frames=this.data.frames;this._frameKeys=Object.keys(this._frames);this._batchIndex=0;this._callback=null}e.prototype._updateResolution=function e(t){var r=this.data.meta.scale;var i=(0,a.getResolutionOfUrl)(t,null);if(i===null){i=r!==undefined?parseFloat(r):1}if(i!==1){this.baseTexture.resolution=i;this.baseTexture.update()}return i};e.prototype.parse=function t(r){this._batchIndex=0;this._callback=r;if(this._frameKeys.length<=e.BATCH_SIZE){this._processFrames(0);this._processAnimations();this._parseComplete()}else{this._nextBatch()}};e.prototype._processFrames=function t(r){var i=r;var a=e.BATCH_SIZE;var o=this.baseTexture.sourceScale;while(i-r<a&&i<this._frameKeys.length){var s=this._frameKeys[i];var u=this._frames[s];var l=u.frame;if(l){var h=null;var f=null;var c=u.trimmed!==false&&u.sourceSize?u.sourceSize:u.frame;var d=new n.Rectangle(0,0,Math.floor(c.w*o)/this.resolution,Math.floor(c.h*o)/this.resolution);if(u.rotated){h=new n.Rectangle(Math.floor(l.x*o)/this.resolution,Math.floor(l.y*o)/this.resolution,Math.floor(l.h*o)/this.resolution,Math.floor(l.w*o)/this.resolution)}else{h=new n.Rectangle(Math.floor(l.x*o)/this.resolution,Math.floor(l.y*o)/this.resolution,Math.floor(l.w*o)/this.resolution,Math.floor(l.h*o)/this.resolution)}if(u.trimmed!==false&&u.spriteSourceSize){f=new n.Rectangle(Math.floor(u.spriteSourceSize.x*o)/this.resolution,Math.floor(u.spriteSourceSize.y*o)/this.resolution,Math.floor(l.w*o)/this.resolution,Math.floor(l.h*o)/this.resolution)}this.textures[s]=new n.Texture(this.baseTexture,h,d,f,u.rotated?2:0,u.anchor);n.Texture.addToCache(this.textures[s],s)}i++}};e.prototype._processAnimations=function e(){var t=this.data.animations||{};for(var r in t){this.animations[r]=[];for(var i=t[r],n=Array.isArray(i),a=0,i=n?i:i[Symbol.iterator]();;){var o;if(n){if(a>=i.length)break;o=i[a++]}else{a=i.next();if(a.done)break;o=a.value}var s=o;this.animations[r].push(this.textures[s])}}};e.prototype._parseComplete=function e(){var t=this._callback;this._callback=null;this._batchIndex=0;t.call(this,this.textures)};e.prototype._nextBatch=function t(){var r=this;this._processFrames(this._batchIndex*e.BATCH_SIZE);this._batchIndex++;setTimeout(function(){if(r._batchIndex*e.BATCH_SIZE<r._frameKeys.length){r._nextBatch()}else{r._processAnimations();r._parseComplete()}},0)};e.prototype.destroy=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;for(var r in this.textures){this.textures[r].destroy()}this._frames=null;this._frameKeys=null;this.data=null;this.textures=null;if(t){this.baseTexture.destroy()}this.baseTexture=null};return e}();t.default=s},"4d16":function(e,t,r){"use strict";t.__esModule=true;t.default=s;var i=r("c88f");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}var o=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join("\n");function s(e,t){var r=!t;if(e===0){throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`")}if(r){var i=document.createElement("canvas");i.width=1;i.height=1;t=n.default.createContext(i)}var a=t.createShader(t.FRAGMENT_SHADER);while(true){var s=o.replace(/%forloop%/gi,u(e));t.shaderSource(a,s);t.compileShader(a);if(!t.getShaderParameter(a,t.COMPILE_STATUS)){e=e/2|0}else{break}}if(r){if(t.getExtension("WEBGL_lose_context")){t.getExtension("WEBGL_lose_context").loseContext()}}return e}function u(e){var t="";for(var r=0;r<e;++r){if(r>0){t+="\nelse "}if(r<e-1){t+="if(test == "+r+".0){}"}}return t}},"4d1d":function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(t,r,n,a,o,s,u,l,h){i(this,e);this.text=t;this.style=r;this.width=n;this.height=a;this.lines=o;this.lineWidths=s;this.lineHeight=u;this.maxLineWidth=l;this.fontProperties=h}e.measureText=function t(r,i,n){var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:e._canvas;n=n===undefined||n===null?i.wordWrap:n;var o=i.toFontString();var s=e.measureFont(o);var u=a.getContext("2d");u.font=o;var l=n?e.wordWrap(r,i,a):r;var h=l.split(/(?:\r\n|\r|\n)/);var f=new Array(h.length);var c=0;for(var d=0;d<h.length;d++){var p=u.measureText(h[d]).width+(h[d].length-1)*i.letterSpacing;f[d]=p;c=Math.max(c,p)}var v=c+i.strokeThickness;if(i.dropShadow){v+=i.dropShadowDistance}var y=i.lineHeight||s.fontSize+i.strokeThickness;var g=Math.max(y,s.fontSize+i.strokeThickness)+(h.length-1)*(y+i.leading);if(i.dropShadow){g+=i.dropShadowDistance}return new e(r,i,v,g,h,f,y+i.leading,c,s)};e.wordWrap=function t(r,i){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:e._canvas;var a=n.getContext("2d");var o=0;var s="";var u="";var l={};var h=i.letterSpacing,f=i.whiteSpace;var c=e.collapseSpaces(f);var d=e.collapseNewlines(f);var p=!c;var v=i.wordWrapWidth+h;var y=e.tokenize(r);for(var g=0;g<y.length;g++){var _=y[g];if(e.isNewline(_)){if(!d){u+=e.addLine(s);p=!c;s="";o=0;continue}_=" "}if(c){var m=e.isBreakingSpace(_);var b=e.isBreakingSpace(s[s.length-1]);if(m&&b){continue}}var x=e.getFromCache(_,h,l,a);if(x>v){if(s!==""){u+=e.addLine(s);s="";o=0}if(e.canBreakWords(_,i.breakWords)){var T=_.split("");for(var E=0;E<T.length;E++){var w=T[E];var S=1;while(T[E+S]){var O=T[E+S];var M=w[w.length-1];if(!e.canBreakChars(M,O,_,E,i.breakWords)){w+=O}else{break}S++}E+=w.length-1;var P=e.getFromCache(w,h,l,a);if(P+o>v){u+=e.addLine(s);p=false;s="";o=0}s+=w;o+=P}}else{if(s.length>0){u+=e.addLine(s);s="";o=0}var C=g===y.length-1;u+=e.addLine(_,!C);p=false;s="";o=0}}else{if(x+o>v){p=false;u+=e.addLine(s);s="";o=0}if(s.length>0||!e.isBreakingSpace(_)||p){s+=_;o+=x}}}u+=e.addLine(s,false);return u};e.addLine=function t(r){var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;r=e.trimRight(r);r=i?r+"\n":r;return r};e.getFromCache=function e(t,r,i,n){var a=i[t];if(a===undefined){var o=t.length*r;a=n.measureText(t).width+o;i[t]=a}return a};e.collapseSpaces=function e(t){return t==="normal"||t==="pre-line"};e.collapseNewlines=function e(t){return t==="normal"};e.trimRight=function t(r){if(typeof r!=="string"){return""}for(var i=r.length-1;i>=0;i--){var n=r[i];if(!e.isBreakingSpace(n)){break}r=r.slice(0,-1)}return r};e.isNewline=function t(r){if(typeof r!=="string"){return false}return e._newlines.indexOf(r.charCodeAt(0))>=0};e.isBreakingSpace=function t(r){if(typeof r!=="string"){return false}return e._breakingSpaces.indexOf(r.charCodeAt(0))>=0};e.tokenize=function t(r){var i=[];var n="";if(typeof r!=="string"){return i}for(var a=0;a<r.length;a++){var o=r[a];if(e.isBreakingSpace(o)||e.isNewline(o)){if(n!==""){i.push(n);n=""}i.push(o);continue}n+=o}if(n!==""){i.push(n)}return i};e.canBreakWords=function e(t,r){return r};e.canBreakChars=function e(t,r,i,n,a){return true};e.measureFont=function t(r){if(e._fonts[r]){return e._fonts[r]}var i={};var n=e._canvas;var a=e._context;a.font=r;var o=e.METRICS_STRING+e.BASELINE_SYMBOL;var s=Math.ceil(a.measureText(o).width);var u=Math.ceil(a.measureText(e.BASELINE_SYMBOL).width);var l=2*u;u=u*e.BASELINE_MULTIPLIER|0;n.width=s;n.height=l;a.fillStyle="#f00";a.fillRect(0,0,s,l);a.font=r;a.textBaseline="alphabetic";a.fillStyle="#000";a.fillText(o,0,u);var h=a.getImageData(0,0,s,l).data;var f=h.length;var c=s*4;var d=0;var p=0;var v=false;for(d=0;d<u;++d){for(var y=0;y<c;y+=4){if(h[p+y]!==255){v=true;break}}if(!v){p+=c}else{break}}i.ascent=u-d;p=f-c;v=false;for(d=l;d>u;--d){for(var g=0;g<c;g+=4){if(h[p+g]!==255){v=true;break}}if(!v){p-=c}else{break}}i.descent=d-u;i.fontSize=i.ascent+i.descent;e._fonts[r]=i;return i};e.clearMetrics=function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"";if(r){delete e._fonts[r]}else{e._fonts={}}};return e}();t.default=n;var a=document.createElement("canvas");a.width=a.height=10;n._canvas=a;n._context=a.getContext("2d");n._fonts={};n.METRICS_STRING="|Éq";n.BASELINE_SYMBOL="M";n.BASELINE_MULTIPLIER=1.4;n._newlines=[10,13];n._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288]},"4d71":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("a506");function a(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var o=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;a(this,e);this.x=Number(t);this.y=Number(r);this.width=Number(i);this.height=Number(o);this.type=n.SHAPES.RECT}e.prototype.clone=function t(){return new e(this.x,this.y,this.width,this.height)};e.prototype.copy=function e(t){this.x=t.x;this.y=t.y;this.width=t.width;this.height=t.height;return this};e.prototype.contains=function e(t,r){if(this.width<=0||this.height<=0){return false}if(t>=this.x&&t<this.x+this.width){if(r>=this.y&&r<this.y+this.height){return true}}return false};e.prototype.pad=function e(t,r){t=t||0;r=r||(r!==0?t:0);this.x-=t;this.y-=r;this.width+=t*2;this.height+=r*2};e.prototype.fit=function e(t){var r=Math.max(this.x,t.x);var i=Math.min(this.x+this.width,t.x+t.width);var n=Math.max(this.y,t.y);var a=Math.min(this.y+this.height,t.y+t.height);this.x=r;this.width=Math.max(i-r,0);this.y=n;this.height=Math.max(a-n,0)};e.prototype.enlarge=function e(t){var r=Math.min(this.x,t.x);var i=Math.max(this.x+this.width,t.x+t.width);var n=Math.min(this.y,t.y);var a=Math.max(this.y+this.height,t.y+t.height);this.x=r;this.width=i-r;this.y=n;this.height=a-n};e.prototype.ceil=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:.001;var i=Math.ceil((this.x+this.width-r)*t)/t;var n=Math.ceil((this.y+this.height-r)*t)/t;this.x=Math.floor((this.x+r)*t)/t;this.y=Math.floor((this.y+r)*t)/t;this.width=i-this.x;this.height=n-this.y};i(e,[{key:"left",get:function e(){return this.x}},{key:"right",get:function e(){return this.x+this.width}},{key:"top",get:function e(){return this.y}},{key:"bottom",get:function e(){return this.y+this.height}}],[{key:"EMPTY",get:function t(){return new e(0,0,0,0)}}]);return e}();t.default=o},"4edd":function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=a(i);function a(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s=new n.Rectangle;var u=4;var l=function(){function e(t){o(this,e);this.renderer=t;t.extract=this}e.prototype.image=function e(t){var e=new Image;e.src=this.base64(t);return e};e.prototype.base64=function e(t){return this.canvas(t).toDataURL()};e.prototype.canvas=function e(t){var r=this.renderer;var i=void 0;var a=void 0;var o=void 0;var l=false;var h=void 0;var f=false;if(t){if(t instanceof n.RenderTexture){h=t}else{h=this.renderer.generateTexture(t);f=true}}if(h){i=h.baseTexture._glRenderTargets[this.renderer.CONTEXT_UID];a=i.resolution;o=h.frame;l=false}else{i=this.renderer.rootRenderTarget;a=i.resolution;l=true;o=s;o.width=i.size.width;o.height=i.size.height}var c=o.width*a;var d=o.height*a;var p=new n.CanvasRenderTarget(c,d,1);if(i){r.bindRenderTarget(i);var v=new Uint8Array(u*c*d);var y=r.gl;y.readPixels(o.x*a,o.y*a,c,d,y.RGBA,y.UNSIGNED_BYTE,v);var g=p.context.getImageData(0,0,c,d);g.data.set(v);p.context.putImageData(g,0,0);if(l){p.context.scale(1,-1);p.context.drawImage(p.canvas,0,-d)}}if(f){h.destroy(true)}return p.canvas};e.prototype.pixels=function e(t){var r=this.renderer;var i=void 0;var a=void 0;var o=void 0;var l=void 0;var h=false;if(t){if(t instanceof n.RenderTexture){l=t}else{l=this.renderer.generateTexture(t);h=true}}if(l){i=l.baseTexture._glRenderTargets[this.renderer.CONTEXT_UID];a=i.resolution;o=l.frame}else{i=this.renderer.rootRenderTarget;a=i.resolution;o=s;o.width=i.size.width;o.height=i.size.height}var f=o.width*a;var c=o.height*a;var d=new Uint8Array(u*f*c);if(i){r.bindRenderTarget(i);var p=r.gl;p.readPixels(o.x*a,o.y*a,f,c,p.RGBA,p.UNSIGNED_BYTE,d)}if(h){l.destroy(true)}return d};e.prototype.destroy=function e(){this.renderer.extract=null;this.renderer=null};return e}();t.default=l;n.WebGLRenderer.registerPlugin("extract",l)},"4f24":function(e,t,r){"use strict";t.__esModule=true;t.default=i;function i(e){var t=e*6;var r=new Uint16Array(t);for(var i=0,n=0;i<t;i+=6,n+=4){r[i+0]=n+0;r[i+1]=n+1;r[i+2]=n+2;r[i+3]=n+0;r[i+4]=n+2;r[i+5]=n+3}return r}},"51d3":function(e,t,r){"use strict";t.__esModule=true;t.eachSeries=n;t.queue=o;function i(){}function n(e,t,r,i){var n=0;var a=e.length;(function o(s){if(s||n===a){if(r){r(s)}return}if(i){setTimeout(function(){t(e[n++],o)},1)}else{t(e[n++],o)}})()}function a(e){return function t(){if(e===null){throw new Error("Callback was already called.")}var r=e;e=null;r.apply(this,arguments)}}function o(e,t){if(t==null){t=1}else if(t===0){throw new Error("Concurrency must not be zero")}var r=0;var n={_tasks:[],concurrency:t,saturated:i,unsaturated:i,buffer:t/4,empty:i,drain:i,error:i,started:false,paused:false,push:function e(t,r){o(t,false,r)},kill:function e(){r=0;n.drain=i;n.started=false;n._tasks=[]},unshift:function e(t,r){o(t,true,r)},process:function t(){while(!n.paused&&r<n.concurrency&&n._tasks.length){var i=n._tasks.shift();if(n._tasks.length===0){n.empty()}r+=1;if(r===n.concurrency){n.saturated()}e(i.data,a(s(i)))}},length:function e(){return n._tasks.length},running:function e(){return r},idle:function e(){return n._tasks.length+r===0},pause:function e(){if(n.paused===true){return}n.paused=true},resume:function e(){if(n.paused===false){return}n.paused=false;for(var t=1;t<=n.concurrency;t++){n.process()}}};function o(e,t,r){if(r!=null&&typeof r!=="function"){throw new Error("task callback must be a function")}n.started=true;if(e==null&&n.idle()){setTimeout(function(){return n.drain()},1);return}var a={data:e,callback:typeof r==="function"?r:i};if(t){n._tasks.unshift(a)}else{n._tasks.push(a)}setTimeout(function(){return n.process()},1)}function s(e){return function t(){r-=1;e.callback.apply(e,arguments);if(arguments[0]!=null){n.error(arguments[0],e.data)}if(r<=n.concurrency-n.buffer){n.unsaturated()}if(n.idle()){n.drain()}n.process()}}return n}},5401:function(e,t,r){"use strict";t.__esModule=true;var i=r("96c8");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s=0;var u=1;var l=2;var h=3;var f=4;var c=function(){function e(t){o(this,e);this.activeState=new Uint8Array(16);this.defaultState=new Uint8Array(16);this.defaultState[0]=1;this.stackIndex=0;this.stack=[];this.gl=t;this.maxAttribs=t.getParameter(t.MAX_VERTEX_ATTRIBS);this.attribState={tempAttribState:new Array(this.maxAttribs),attribState:new Array(this.maxAttribs)};this.blendModes=(0,n.default)(t);this.nativeVaoExtension=t.getExtension("OES_vertex_array_object")||t.getExtension("MOZ_OES_vertex_array_object")||t.getExtension("WEBKIT_OES_vertex_array_object")}e.prototype.push=function e(){var t=this.stack[this.stackIndex];if(!t){t=this.stack[this.stackIndex]=new Uint8Array(16)}++this.stackIndex;for(var r=0;r<this.activeState.length;r++){t[r]=this.activeState[r]}};e.prototype.pop=function e(){var t=this.stack[--this.stackIndex];this.setState(t)};e.prototype.setState=function e(t){this.setBlend(t[s]);this.setDepthTest(t[u]);this.setFrontFace(t[l]);this.setCullFace(t[h]);this.setBlendMode(t[f])};e.prototype.setBlend=function e(t){t=t?1:0;if(this.activeState[s]===t){return}this.activeState[s]=t;this.gl[t?"enable":"disable"](this.gl.BLEND)};e.prototype.setBlendMode=function e(t){if(t===this.activeState[f]){return}this.activeState[f]=t;var r=this.blendModes[t];if(r.length===2){this.gl.blendFunc(r[0],r[1])}else{this.gl.blendFuncSeparate(r[0],r[1],r[2],r[3])}};e.prototype.setDepthTest=function e(t){t=t?1:0;if(this.activeState[u]===t){return}this.activeState[u]=t;this.gl[t?"enable":"disable"](this.gl.DEPTH_TEST)};e.prototype.setCullFace=function e(t){t=t?1:0;if(this.activeState[h]===t){return}this.activeState[h]=t;this.gl[t?"enable":"disable"](this.gl.CULL_FACE)};e.prototype.setFrontFace=function e(t){t=t?1:0;if(this.activeState[l]===t){return}this.activeState[l]=t;this.gl.frontFace(this.gl[t?"CW":"CCW"])};e.prototype.resetAttributes=function e(){for(var t=0;t<this.attribState.tempAttribState.length;t++){this.attribState.tempAttribState[t]=0}for(var r=0;r<this.attribState.attribState.length;r++){this.attribState.attribState[r]=0}for(var i=1;i<this.maxAttribs;i++){this.gl.disableVertexAttribArray(i)}};e.prototype.resetToDefault=function e(){if(this.nativeVaoExtension){this.nativeVaoExtension.bindVertexArrayOES(null)}this.resetAttributes();for(var t=0;t<this.activeState.length;++t){this.activeState[t]=32}this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL,false);this.setState(this.defaultState)};return e}();t.default=c},5664:function(e,t,r){"use strict";e.exports=i;e.exports.default=i;function i(e,t,r){r=r||2;var i=t&&t.length,a=i?t[0]*r:e.length,s=n(e,0,a,r,true),u=[];if(!s||s.next===s.prev)return u;var l,h,c,d,p,v,y;if(i)s=f(e,t,s,r);if(e.length>80*r){l=c=e[0];h=d=e[1];for(var g=r;g<a;g+=r){p=e[g];v=e[g+1];if(p<l)l=p;if(v<h)h=v;if(p>c)c=p;if(v>d)d=v}y=Math.max(c-l,d-h);y=y!==0?1/y:0}o(s,u,r,l,h,y);return u}function n(e,t,r,i,n){var a,o;if(n===R(e,t,r,i)>0){for(a=t;a<r;a+=i)o=P(a,e[a],e[a+1],o)}else{for(a=r-i;a>=t;a-=i)o=P(a,e[a],e[a+1],o)}if(o&&T(o,o.next)){C(o);o=o.next}return o}function a(e,t){if(!e)return e;if(!t)t=e;var r=e,i;do{i=false;if(!r.steiner&&(T(r,r.next)||x(r.prev,r,r.next)===0)){C(r);r=t=r.prev;if(r===r.next)break;i=true}else{r=r.next}}while(i||r!==t);return t}function o(e,t,r,i,n,f,c){if(!e)return;if(!c&&f)v(e,i,n,f);var d=e,p,y;while(e.prev!==e.next){p=e.prev;y=e.next;if(f?u(e,i,n,f):s(e)){t.push(p.i/r);t.push(e.i/r);t.push(y.i/r);C(e);e=y.next;d=y.next;continue}e=y;if(e===d){if(!c){o(a(e),t,r,i,n,f,1)}else if(c===1){e=l(e,t,r);o(e,t,r,i,n,f,2)}else if(c===2){h(e,t,r,i,n,f)}break}}}function s(e){var t=e.prev,r=e,i=e.next;if(x(t,r,i)>=0)return false;var n=e.next.next;while(n!==e.prev){if(m(t.x,t.y,r.x,r.y,i.x,i.y,n.x,n.y)&&x(n.prev,n,n.next)>=0)return false;n=n.next}return true}function u(e,t,r,i){var n=e.prev,a=e,o=e.next;if(x(n,a,o)>=0)return false;var s=n.x<a.x?n.x<o.x?n.x:o.x:a.x<o.x?a.x:o.x,u=n.y<a.y?n.y<o.y?n.y:o.y:a.y<o.y?a.y:o.y,l=n.x>a.x?n.x>o.x?n.x:o.x:a.x>o.x?a.x:o.x,h=n.y>a.y?n.y>o.y?n.y:o.y:a.y>o.y?a.y:o.y;var f=g(s,u,t,r,i),c=g(l,h,t,r,i);var d=e.prevZ,p=e.nextZ;while(d&&d.z>=f&&p&&p.z<=c){if(d!==e.prev&&d!==e.next&&m(n.x,n.y,a.x,a.y,o.x,o.y,d.x,d.y)&&x(d.prev,d,d.next)>=0)return false;d=d.prevZ;if(p!==e.prev&&p!==e.next&&m(n.x,n.y,a.x,a.y,o.x,o.y,p.x,p.y)&&x(p.prev,p,p.next)>=0)return false;p=p.nextZ}while(d&&d.z>=f){if(d!==e.prev&&d!==e.next&&m(n.x,n.y,a.x,a.y,o.x,o.y,d.x,d.y)&&x(d.prev,d,d.next)>=0)return false;d=d.prevZ}while(p&&p.z<=c){if(p!==e.prev&&p!==e.next&&m(n.x,n.y,a.x,a.y,o.x,o.y,p.x,p.y)&&x(p.prev,p,p.next)>=0)return false;p=p.nextZ}return true}function l(e,t,r){var i=e;do{var n=i.prev,a=i.next.next;if(!T(n,a)&&E(n,i,i.next,a)&&S(n,a)&&S(a,n)){t.push(n.i/r);t.push(i.i/r);t.push(a.i/r);C(i);C(i.next);i=e=a}i=i.next}while(i!==e);return i}function h(e,t,r,i,n,s){var u=e;do{var l=u.next.next;while(l!==u.prev){if(u.i!==l.i&&b(u,l)){var h=M(u,l);u=a(u,u.next);h=a(h,h.next);o(u,t,r,i,n,s);o(h,t,r,i,n,s);return}l=l.next}u=u.next}while(u!==e)}function f(e,t,r,i){var o=[],s,u,l,h,f;for(s=0,u=t.length;s<u;s++){l=t[s]*i;h=s<u-1?t[s+1]*i:e.length;f=n(e,l,h,i,false);if(f===f.next)f.steiner=true;o.push(_(f))}o.sort(c);for(s=0;s<o.length;s++){d(o[s],r);r=a(r,r.next)}return r}function c(e,t){return e.x-t.x}function d(e,t){t=p(e,t);if(t){var r=M(t,e);a(r,r.next)}}function p(e,t){var r=t,i=e.x,n=e.y,a=-Infinity,o;do{if(n<=r.y&&n>=r.next.y&&r.next.y!==r.y){var s=r.x+(n-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(s<=i&&s>a){a=s;if(s===i){if(n===r.y)return r;if(n===r.next.y)return r.next}o=r.x<r.next.x?r:r.next}}r=r.next}while(r!==t);if(!o)return null;if(i===a)return o.prev;var u=o,l=o.x,h=o.y,f=Infinity,c;r=o.next;while(r!==u){if(i>=r.x&&r.x>=l&&i!==r.x&&m(n<h?i:a,n,l,h,n<h?a:i,n,r.x,r.y)){c=Math.abs(n-r.y)/(i-r.x);if((c<f||c===f&&r.x>o.x)&&S(r,e)){o=r;f=c}}r=r.next}return o}function v(e,t,r,i){var n=e;do{if(n.z===null)n.z=g(n.x,n.y,t,r,i);n.prevZ=n.prev;n.nextZ=n.next;n=n.next}while(n!==e);n.prevZ.nextZ=null;n.prevZ=null;y(n)}function y(e){var t,r,i,n,a,o,s,u,l=1;do{r=e;e=null;a=null;o=0;while(r){o++;i=r;s=0;for(t=0;t<l;t++){s++;i=i.nextZ;if(!i)break}u=l;while(s>0||u>0&&i){if(s!==0&&(u===0||!i||r.z<=i.z)){n=r;r=r.nextZ;s--}else{n=i;i=i.nextZ;u--}if(a)a.nextZ=n;else e=n;n.prevZ=a;a=n}r=i}a.nextZ=null;l*=2}while(o>1);return e}function g(e,t,r,i,n){e=32767*(e-r)*n;t=32767*(t-i)*n;e=(e|e<<8)&16711935;e=(e|e<<4)&252645135;e=(e|e<<2)&858993459;e=(e|e<<1)&1431655765;t=(t|t<<8)&16711935;t=(t|t<<4)&252645135;t=(t|t<<2)&858993459;t=(t|t<<1)&1431655765;return e|t<<1}function _(e){var t=e,r=e;do{if(t.x<r.x||t.x===r.x&&t.y<r.y)r=t;t=t.next}while(t!==e);return r}function m(e,t,r,i,n,a,o,s){return(n-o)*(t-s)-(e-o)*(a-s)>=0&&(e-o)*(i-s)-(r-o)*(t-s)>=0&&(r-o)*(a-s)-(n-o)*(i-s)>=0}function b(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!w(e,t)&&S(e,t)&&S(t,e)&&O(e,t)}function x(e,t,r){return(t.y-e.y)*(r.x-t.x)-(t.x-e.x)*(r.y-t.y)}function T(e,t){return e.x===t.x&&e.y===t.y}function E(e,t,r,i){if(T(e,t)&&T(r,i)||T(e,i)&&T(r,t))return true;return x(e,t,r)>0!==x(e,t,i)>0&&x(r,i,e)>0!==x(r,i,t)>0}function w(e,t){var r=e;do{if(r.i!==e.i&&r.next.i!==e.i&&r.i!==t.i&&r.next.i!==t.i&&E(r,r.next,e,t))return true;r=r.next}while(r!==e);return false}function S(e,t){return x(e.prev,e,e.next)<0?x(e,t,e.next)>=0&&x(e,e.prev,t)>=0:x(e,t,e.prev)<0||x(e,e.next,t)<0}function O(e,t){var r=e,i=false,n=(e.x+t.x)/2,a=(e.y+t.y)/2;do{if(r.y>a!==r.next.y>a&&r.next.y!==r.y&&n<(r.next.x-r.x)*(a-r.y)/(r.next.y-r.y)+r.x)i=!i;r=r.next}while(r!==e);return i}function M(e,t){var r=new A(e.i,e.x,e.y),i=new A(t.i,t.x,t.y),n=e.next,a=t.prev;e.next=t;t.prev=e;r.next=n;n.prev=r;i.next=r;r.prev=i;a.next=i;i.prev=a;return i}function P(e,t,r,i){var n=new A(e,t,r);if(!i){n.prev=n;n.next=n}else{n.next=i.next;n.prev=i;i.next.prev=n;i.next=n}return n}function C(e){e.next.prev=e.prev;e.prev.next=e.next;if(e.prevZ)e.prevZ.nextZ=e.nextZ;if(e.nextZ)e.nextZ.prevZ=e.prevZ}function A(e,t,r){this.i=e;this.x=t;this.y=r;this.prev=null;this.next=null;this.z=null;this.prevZ=null;this.nextZ=null;this.steiner=false}i.deviation=function(e,t,r,i){var n=t&&t.length;var a=n?t[0]*r:e.length;var o=Math.abs(R(e,0,a,r));if(n){for(var s=0,u=t.length;s<u;s++){var l=t[s]*r;var h=s<u-1?t[s+1]*r:e.length;o-=Math.abs(R(e,l,h,r))}}var f=0;for(s=0;s<i.length;s+=3){var c=i[s]*r;var d=i[s+1]*r;var p=i[s+2]*r;f+=Math.abs((e[c]-e[p])*(e[d+1]-e[c+1])-(e[c]-e[d])*(e[p+1]-e[c+1]))}return o===0&&f===0?0:Math.abs((f-o)/o)};function R(e,t,r,i){var n=0;for(var a=t,o=r-i;a<r;a+=i){n+=(e[o]-e[a])*(e[a+1]+e[o+1]);o=a}return n}i.flatten=function(e){var t=e[0][0].length,r={vertices:[],holes:[],dimensions:t},i=0;for(var n=0;n<e.length;n++){for(var a=0;a<e[n].length;a++){for(var o=0;o<t;o++)r.vertices.push(e[n][a][o])}if(n>0){i+=e[n-1].length;r.holes.push(i)}}return r}},"589c":function(e,t,r){"use strict";t.__esModule=true;var i=r("dc99");var n=s(i);var a=r("61df");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:100;var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:100;var n=arguments[2];var a=arguments[3];u(this,t);var s=l(this,e.call(this,null,n));s.resolution=a||o.default.RESOLUTION;s.width=Math.ceil(r);s.height=Math.ceil(i);s.realWidth=s.width*s.resolution;s.realHeight=s.height*s.resolution;s.scaleMode=n!==undefined?n:o.default.SCALE_MODE;s.hasLoaded=true;s._glRenderTargets={};s._canvasRenderTarget=null;s.valid=false;return s}t.prototype.resize=function e(t,r){t=Math.ceil(t);r=Math.ceil(r);if(t===this.width&&r===this.height){return}this.valid=t>0&&r>0;this.width=t;this.height=r;this.realWidth=this.width*this.resolution;this.realHeight=this.height*this.resolution;if(!this.valid){return}this.emit("update",this)};t.prototype.destroy=function t(){e.prototype.destroy.call(this,true);this.renderer=null};return t}(n.default);t.default=f},"5abd":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("62ee");var a=h(n);var o=r("aa9d");var s=r("a506");var u=r("61df");var l=h(u);function h(e){return e&&e.__esModule?e:{default:e}}function f(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var c={};var d=function(){function e(t,r,i){f(this,e);this.vertexSrc=t||e.defaultVertexSrc;this.fragmentSrc=r||e.defaultFragmentSrc;this._blendMode=s.BLEND_MODES.NORMAL;this.uniformData=i||(0,a.default)(this.vertexSrc,this.fragmentSrc,"projectionMatrix|uSampler");this.uniforms={};for(var n in this.uniformData){this.uniforms[n]=this.uniformData[n].value;if(this.uniformData[n].type){this.uniformData[n].type=this.uniformData[n].type.toLowerCase()}}this.glShaders={};if(!c[this.vertexSrc+this.fragmentSrc]){c[this.vertexSrc+this.fragmentSrc]=(0,o.uid)()}this.glShaderKey=c[this.vertexSrc+this.fragmentSrc];this.padding=4;this.resolution=l.default.FILTER_RESOLUTION;this.enabled=true;this.autoFit=true}e.prototype.apply=function e(t,r,i,n,a){t.applyFilter(this,r,i,n)};i(e,[{key:"blendMode",get:function e(){return this._blendMode},set:function e(t){this._blendMode=t}}],[{key:"defaultVertexSrc",get:function e(){return["attribute vec2 aVertexPosition;","attribute vec2 aTextureCoord;","uniform mat3 projectionMatrix;","uniform mat3 filterMatrix;","varying vec2 vTextureCoord;","varying vec2 vFilterCoord;","void main(void){","   gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);","   vFilterCoord = ( filterMatrix * vec3( aTextureCoord, 1.0)  ).xy;","   vTextureCoord = aTextureCoord ;","}"].join("\n")}},{key:"defaultFragmentSrc",get:function e(){return["varying vec2 vTextureCoord;","varying vec2 vFilterCoord;","uniform sampler2D uSampler;","uniform sampler2D filterSampler;","void main(void){","   vec4 masky = texture2D(filterSampler, vFilterCoord);","   vec4 sample = texture2D(uSampler, vTextureCoord);","   vec4 color;","   if(mod(vFilterCoord.x, 1.0) > 0.5)","   {","     color = vec4(1.0, 0.0, 0.0, 1.0);","   }","   else","   {","     color = vec4(0.0, 1.0, 0.0, 1.0);","   }","   gl_FragColor = mix(sample, masky, 0.5);","   gl_FragColor *= sample.a;","}"].join("\n")}}]);return e}();t.default=d},"5b76":function(e,t,r){"use strict";t.__esModule=true;var i=r("4d71");var n=o(i);var a=r("a506");function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;s(this,e);this.x=t;this.y=r;this.radius=i;this.type=a.SHAPES.CIRC}e.prototype.clone=function t(){return new e(this.x,this.y,this.radius)};e.prototype.contains=function e(t,r){if(this.radius<=0){return false}var i=this.radius*this.radius;var n=this.x-t;var a=this.y-r;n*=n;a*=a;return n+a<=i};e.prototype.getBounds=function e(){return new n.default(this.x-this.radius,this.y-this.radius,this.radius*2,this.radius*2)};return e}();t.default=u},"5f08":function(e,t,r){"use strict";t.__esModule=true;t.Resource=undefined;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("bbad");var a=u(n);var o=r("d5e4");var s=u(o);function u(e){return e&&e.__esModule?e:{default:e}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var h=!!(window.XDomainRequest&&!("withCredentials"in new XMLHttpRequest));var f=null;var c=0;var d=200;var p=204;var v=1223;var y=2;function g(){}var _=t.Resource=function(){e.setExtensionLoadType=function t(r,i){m(e._loadTypeMap,r,i)};e.setExtensionXhrType=function t(r,i){m(e._xhrTypeMap,r,i)};function e(t,r,i){l(this,e);if(typeof t!=="string"||typeof r!=="string"){throw new Error("Both name and url are required for constructing a resource.")}i=i||{};this._flags=0;this._setFlag(e.STATUS_FLAGS.DATA_URL,r.indexOf("data:")===0);this.name=t;this.url=r;this.extension=this._getExtension();this.data=null;this.crossOrigin=i.crossOrigin===true?"anonymous":i.crossOrigin;this.timeout=i.timeout||0;this.loadType=i.loadType||this._determineLoadType();this.xhrType=i.xhrType;this.metadata=i.metadata||{};this.error=null;this.xhr=null;this.children=[];this.type=e.TYPE.UNKNOWN;this.progressChunk=0;this._dequeue=g;this._onLoadBinding=null;this._elementTimer=0;this._boundComplete=this.complete.bind(this);this._boundOnError=this._onError.bind(this);this._boundOnProgress=this._onProgress.bind(this);this._boundOnTimeout=this._onTimeout.bind(this);this._boundXhrOnError=this._xhrOnError.bind(this);this._boundXhrOnTimeout=this._xhrOnTimeout.bind(this);this._boundXhrOnAbort=this._xhrOnAbort.bind(this);this._boundXhrOnLoad=this._xhrOnLoad.bind(this);this.onStart=new s.default;this.onProgress=new s.default;this.onComplete=new s.default;this.onAfterMiddleware=new s.default}e.prototype.complete=function e(){this._clearEvents();this._finish()};e.prototype.abort=function t(r){if(this.error){return}this.error=new Error(r);this._clearEvents();if(this.xhr){this.xhr.abort()}else if(this.xdr){this.xdr.abort()}else if(this.data){if(this.data.src){this.data.src=e.EMPTY_GIF}else{while(this.data.firstChild){this.data.removeChild(this.data.firstChild)}}}this._finish()};e.prototype.load=function t(r){var i=this;if(this.isLoading){return}if(this.isComplete){if(r){setTimeout(function(){return r(i)},1)}return}else if(r){this.onComplete.once(r)}this._setFlag(e.STATUS_FLAGS.LOADING,true);this.onStart.dispatch(this);if(this.crossOrigin===false||typeof this.crossOrigin!=="string"){this.crossOrigin=this._determineCrossOrigin(this.url)}switch(this.loadType){case e.LOAD_TYPE.IMAGE:this.type=e.TYPE.IMAGE;this._loadElement("image");break;case e.LOAD_TYPE.AUDIO:this.type=e.TYPE.AUDIO;this._loadSourceElement("audio");break;case e.LOAD_TYPE.VIDEO:this.type=e.TYPE.VIDEO;this._loadSourceElement("video");break;case e.LOAD_TYPE.XHR:default:if(h&&this.crossOrigin){this._loadXdr()}else{this._loadXhr()}break}};e.prototype._hasFlag=function e(t){return(this._flags&t)!==0};e.prototype._setFlag=function e(t,r){this._flags=r?this._flags|t:this._flags&~t};e.prototype._clearEvents=function e(){clearTimeout(this._elementTimer);if(this.data&&this.data.removeEventListener){this.data.removeEventListener("error",this._boundOnError,false);this.data.removeEventListener("load",this._boundComplete,false);this.data.removeEventListener("progress",this._boundOnProgress,false);this.data.removeEventListener("canplaythrough",this._boundComplete,false)}if(this.xhr){if(this.xhr.removeEventListener){this.xhr.removeEventListener("error",this._boundXhrOnError,false);this.xhr.removeEventListener("timeout",this._boundXhrOnTimeout,false);this.xhr.removeEventListener("abort",this._boundXhrOnAbort,false);this.xhr.removeEventListener("progress",this._boundOnProgress,false);this.xhr.removeEventListener("load",this._boundXhrOnLoad,false)}else{this.xhr.onerror=null;this.xhr.ontimeout=null;this.xhr.onprogress=null;this.xhr.onload=null}}};e.prototype._finish=function t(){if(this.isComplete){throw new Error("Complete called again for an already completed resource.")}this._setFlag(e.STATUS_FLAGS.COMPLETE,true);this._setFlag(e.STATUS_FLAGS.LOADING,false);this.onComplete.dispatch(this)};e.prototype._loadElement=function e(t){if(this.metadata.loadElement){this.data=this.metadata.loadElement}else if(t==="image"&&typeof window.Image!=="undefined"){this.data=new Image}else{this.data=document.createElement(t)}if(this.crossOrigin){this.data.crossOrigin=this.crossOrigin}if(!this.metadata.skipSource){this.data.src=this.url}this.data.addEventListener("error",this._boundOnError,false);this.data.addEventListener("load",this._boundComplete,false);this.data.addEventListener("progress",this._boundOnProgress,false);if(this.timeout){this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout)}};e.prototype._loadSourceElement=function e(t){if(this.metadata.loadElement){this.data=this.metadata.loadElement}else if(t==="audio"&&typeof window.Audio!=="undefined"){this.data=new Audio}else{this.data=document.createElement(t)}if(this.data===null){this.abort("Unsupported element: "+t);return}if(this.crossOrigin){this.data.crossOrigin=this.crossOrigin}if(!this.metadata.skipSource){if(navigator.isCocoonJS){this.data.src=Array.isArray(this.url)?this.url[0]:this.url}else if(Array.isArray(this.url)){var r=this.metadata.mimeType;for(var i=0;i<this.url.length;++i){this.data.appendChild(this._createSource(t,this.url[i],Array.isArray(r)?r[i]:r))}}else{var n=this.metadata.mimeType;this.data.appendChild(this._createSource(t,this.url,Array.isArray(n)?n[0]:n))}}this.data.addEventListener("error",this._boundOnError,false);this.data.addEventListener("load",this._boundComplete,false);this.data.addEventListener("progress",this._boundOnProgress,false);this.data.addEventListener("canplaythrough",this._boundComplete,false);this.data.load();if(this.timeout){this._elementTimer=setTimeout(this._boundOnTimeout,this.timeout)}};e.prototype._loadXhr=function t(){if(typeof this.xhrType!=="string"){this.xhrType=this._determineXhrType()}var r=this.xhr=new XMLHttpRequest;r.open("GET",this.url,true);r.timeout=this.timeout;if(this.xhrType===e.XHR_RESPONSE_TYPE.JSON||this.xhrType===e.XHR_RESPONSE_TYPE.DOCUMENT){r.responseType=e.XHR_RESPONSE_TYPE.TEXT}else{r.responseType=this.xhrType}r.addEventListener("error",this._boundXhrOnError,false);r.addEventListener("timeout",this._boundXhrOnTimeout,false);r.addEventListener("abort",this._boundXhrOnAbort,false);r.addEventListener("progress",this._boundOnProgress,false);r.addEventListener("load",this._boundXhrOnLoad,false);r.send()};e.prototype._loadXdr=function e(){if(typeof this.xhrType!=="string"){this.xhrType=this._determineXhrType()}var t=this.xhr=new XDomainRequest;t.timeout=this.timeout||5e3;t.onerror=this._boundXhrOnError;t.ontimeout=this._boundXhrOnTimeout;t.onprogress=this._boundOnProgress;t.onload=this._boundXhrOnLoad;t.open("GET",this.url,true);setTimeout(function(){return t.send()},1)};e.prototype._createSource=function e(t,r,i){if(!i){i=t+"/"+this._getExtension(r)}var n=document.createElement("source");n.src=r;n.type=i;return n};e.prototype._onError=function e(t){this.abort("Failed to load element using: "+t.target.nodeName)};e.prototype._onProgress=function e(t){if(t&&t.lengthComputable){this.onProgress.dispatch(this,t.loaded/t.total)}};e.prototype._onTimeout=function e(){this.abort("Load timed out.")};e.prototype._xhrOnError=function e(){var t=this.xhr;this.abort(b(t)+" Request failed. Status: "+t.status+', text: "'+t.statusText+'"')};e.prototype._xhrOnTimeout=function e(){var t=this.xhr;this.abort(b(t)+" Request timed out.")};e.prototype._xhrOnAbort=function e(){var t=this.xhr;this.abort(b(t)+" Request was aborted by the user.")};e.prototype._xhrOnLoad=function t(){var r=this.xhr;var i="";var n=typeof r.status==="undefined"?d:r.status;if(r.responseType===""||r.responseType==="text"||typeof r.responseType==="undefined"){i=r.responseText}if(n===c&&(i.length>0||r.responseType===e.XHR_RESPONSE_TYPE.BUFFER)){n=d}else if(n===v){n=p}var a=n/100|0;if(a===y){if(this.xhrType===e.XHR_RESPONSE_TYPE.TEXT){this.data=i;this.type=e.TYPE.TEXT}else if(this.xhrType===e.XHR_RESPONSE_TYPE.JSON){try{this.data=JSON.parse(i);this.type=e.TYPE.JSON}catch(u){this.abort("Error trying to parse loaded json: "+u);return}}else if(this.xhrType===e.XHR_RESPONSE_TYPE.DOCUMENT){try{if(window.DOMParser){var o=new DOMParser;this.data=o.parseFromString(i,"text/xml")}else{var s=document.createElement("div");s.innerHTML=i;this.data=s}this.type=e.TYPE.XML}catch(u){this.abort("Error trying to parse loaded xml: "+u);return}}else{this.data=r.response||i}}else{this.abort("["+r.status+"] "+r.statusText+": "+r.responseURL);return}this.complete()};e.prototype._determineCrossOrigin=function e(t,r){if(t.indexOf("data:")===0){return""}if(window.origin!==window.location.origin){return"anonymous"}r=r||window.location;if(!f){f=document.createElement("a")}f.href=t;t=(0,a.default)(f.href,{strictMode:true});var i=!t.port&&r.port===""||t.port===r.port;var n=t.protocol?t.protocol+":":"";if(t.host!==r.hostname||!i||n!==r.protocol){return"anonymous"}return""};e.prototype._determineXhrType=function t(){return e._xhrTypeMap[this.extension]||e.XHR_RESPONSE_TYPE.TEXT};e.prototype._determineLoadType=function t(){return e._loadTypeMap[this.extension]||e.LOAD_TYPE.XHR};e.prototype._getExtension=function e(){var t=this.url;var r="";if(this.isDataUrl){var i=t.indexOf("/");r=t.substring(i+1,t.indexOf(";",i))}else{var n=t.indexOf("?");var a=t.indexOf("#");var o=Math.min(n>-1?n:t.length,a>-1?a:t.length);t=t.substring(0,o);r=t.substring(t.lastIndexOf(".")+1)}return r.toLowerCase()};e.prototype._getMimeFromXhrType=function t(r){switch(r){case e.XHR_RESPONSE_TYPE.BUFFER:return"application/octet-binary";case e.XHR_RESPONSE_TYPE.BLOB:return"application/blob";case e.XHR_RESPONSE_TYPE.DOCUMENT:return"application/xml";case e.XHR_RESPONSE_TYPE.JSON:return"application/json";case e.XHR_RESPONSE_TYPE.DEFAULT:case e.XHR_RESPONSE_TYPE.TEXT:default:return"text/plain"}};i(e,[{key:"isDataUrl",get:function t(){return this._hasFlag(e.STATUS_FLAGS.DATA_URL)}},{key:"isComplete",get:function t(){return this._hasFlag(e.STATUS_FLAGS.COMPLETE)}},{key:"isLoading",get:function t(){return this._hasFlag(e.STATUS_FLAGS.LOADING)}}]);return e}();_.STATUS_FLAGS={NONE:0,DATA_URL:1<<0,COMPLETE:1<<1,LOADING:1<<2};_.TYPE={UNKNOWN:0,JSON:1,XML:2,IMAGE:3,AUDIO:4,VIDEO:5,TEXT:6};_.LOAD_TYPE={XHR:1,IMAGE:2,AUDIO:3,VIDEO:4};_.XHR_RESPONSE_TYPE={DEFAULT:"text",BUFFER:"arraybuffer",BLOB:"blob",DOCUMENT:"document",JSON:"json",TEXT:"text"};_._loadTypeMap={gif:_.LOAD_TYPE.IMAGE,png:_.LOAD_TYPE.IMAGE,bmp:_.LOAD_TYPE.IMAGE,jpg:_.LOAD_TYPE.IMAGE,jpeg:_.LOAD_TYPE.IMAGE,tif:_.LOAD_TYPE.IMAGE,tiff:_.LOAD_TYPE.IMAGE,webp:_.LOAD_TYPE.IMAGE,tga:_.LOAD_TYPE.IMAGE,svg:_.LOAD_TYPE.IMAGE,"svg+xml":_.LOAD_TYPE.IMAGE,mp3:_.LOAD_TYPE.AUDIO,ogg:_.LOAD_TYPE.AUDIO,wav:_.LOAD_TYPE.AUDIO,mp4:_.LOAD_TYPE.VIDEO,webm:_.LOAD_TYPE.VIDEO};_._xhrTypeMap={xhtml:_.XHR_RESPONSE_TYPE.DOCUMENT,html:_.XHR_RESPONSE_TYPE.DOCUMENT,htm:_.XHR_RESPONSE_TYPE.DOCUMENT,xml:_.XHR_RESPONSE_TYPE.DOCUMENT,tmx:_.XHR_RESPONSE_TYPE.DOCUMENT,svg:_.XHR_RESPONSE_TYPE.DOCUMENT,tsx:_.XHR_RESPONSE_TYPE.DOCUMENT,gif:_.XHR_RESPONSE_TYPE.BLOB,png:_.XHR_RESPONSE_TYPE.BLOB,bmp:_.XHR_RESPONSE_TYPE.BLOB,jpg:_.XHR_RESPONSE_TYPE.BLOB,jpeg:_.XHR_RESPONSE_TYPE.BLOB,tif:_.XHR_RESPONSE_TYPE.BLOB,tiff:_.XHR_RESPONSE_TYPE.BLOB,webp:_.XHR_RESPONSE_TYPE.BLOB,tga:_.XHR_RESPONSE_TYPE.BLOB,json:_.XHR_RESPONSE_TYPE.JSON,text:_.XHR_RESPONSE_TYPE.TEXT,txt:_.XHR_RESPONSE_TYPE.TEXT,ttf:_.XHR_RESPONSE_TYPE.BUFFER,otf:_.XHR_RESPONSE_TYPE.BUFFER};_.EMPTY_GIF="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==";function m(e,t,r){if(t&&t.indexOf(".")===0){t=t.substring(1)}if(!t){return}e[t]=r}function b(e){return e.toString().replace("object ","")}if(true){e.exports.default=_}},"61df":function(e,t,r){"use strict";t.__esModule=true;var i=r("c6a9");var n=s(i);var a=r("24b2");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}t.default={TARGET_FPMS:.06,MIPMAP_TEXTURES:true,RESOLUTION:1,FILTER_RESOLUTION:1,SPRITE_MAX_TEXTURES:(0,n.default)(32),SPRITE_BATCH_SIZE:4096,RETINA_PREFIX:/@([0-9\.]+)x/,RENDER_OPTIONS:{view:null,antialias:false,forceFXAA:false,autoResize:false,transparent:false,backgroundColor:0,clearBeforeRender:true,preserveDrawingBuffer:false,roundPixels:false,width:800,height:600,legacy:false},TRANSFORM_MODE:0,GC_MODE:0,GC_MAX_IDLE:60*60,GC_MAX_CHECK_COUNT:60*10,WRAP_MODE:0,SCALE_MODE:0,PRECISION_VERTEX:"highp",PRECISION_FRAGMENT:"mediump",CAN_UPLOAD_SAME_BUFFER:(0,o.default)(),MESH_CANVAS_PADDING:0}},"622c":function(e,t,r){"use strict";t.__esModule=true;var i=r("6f1d");Object.defineProperty(t,"accessibleTarget",{enumerable:true,get:function e(){return a(i).default}});var n=r("d801");Object.defineProperty(t,"AccessibilityManager",{enumerable:true,get:function e(){return a(n).default}});function a(e){return e&&e.__esModule?e:{default:e}}},6274:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("774e");var a=r("281c");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){u(this,t);var r=l(this,e.call(this));r.position=new n.Point(0,0);r.scale=new n.Point(1,1);r.skew=new n.ObservablePoint(r.updateSkew,r,0,0);r.pivot=new n.Point(0,0);r._rotation=0;r._cx=1;r._sx=0;r._cy=0;r._sy=1;return r}t.prototype.updateSkew=function e(){this._cx=Math.cos(this._rotation+this.skew._y);this._sx=Math.sin(this._rotation+this.skew._y);this._cy=-Math.sin(this._rotation-this.skew._x);this._sy=Math.cos(this._rotation-this.skew._x)};t.prototype.updateLocalTransform=function e(){var t=this.localTransform;t.a=this._cx*this.scale.x;t.b=this._sx*this.scale.x;t.c=this._cy*this.scale.y;t.d=this._sy*this.scale.y;t.tx=this.position.x-(this.pivot.x*t.a+this.pivot.y*t.c);t.ty=this.position.y-(this.pivot.x*t.b+this.pivot.y*t.d)};t.prototype.updateTransform=function e(t){var r=this.localTransform;r.a=this._cx*this.scale.x;r.b=this._sx*this.scale.x;r.c=this._cy*this.scale.y;r.d=this._sy*this.scale.y;r.tx=this.position.x-(this.pivot.x*r.a+this.pivot.y*r.c);r.ty=this.position.y-(this.pivot.x*r.b+this.pivot.y*r.d);var i=t.worldTransform;var n=this.worldTransform;n.a=r.a*i.a+r.b*i.c;n.b=r.a*i.b+r.b*i.d;n.c=r.c*i.a+r.d*i.c;n.d=r.c*i.b+r.d*i.d;n.tx=r.tx*i.a+r.ty*i.c+i.tx;n.ty=r.tx*i.b+r.ty*i.d+i.ty;this._worldID++};t.prototype.setFromMatrix=function e(t){t.decompose(this)};i(t,[{key:"rotation",get:function e(){return this._rotation},set:function e(t){this._rotation=t;this.updateSkew()}}]);return t}(o.default);t.default=f},"62ee":function(e,t,r){"use strict";t.__esModule=true;t.default=s;var i=r("c88f");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}var o=n.default.shader.defaultValue;function s(e,t,r){var i=u(e,r);var n=u(t,r);return Object.assign(i,n)}function u(e){var t=new RegExp("^(projectionMatrix|uSampler|filterArea|filterClamp)$");var r={};var i=void 0;var n=e.replace(/\s+/g," ").split(/\s*;\s*/);for(var a=0;a<n.length;a++){var s=n[a].trim();if(s.indexOf("uniform")>-1){var u=s.split(" ");var l=u[1];var h=u[2];var f=1;if(h.indexOf("[")>-1){i=h.split(/\[|]/);h=i[0];f*=Number(i[1])}if(!h.match(t)){r[h]={value:o(l,f),name:h,type:l}}}}return r}},"635d":function(e,t,r){"use strict";t.__esModule=true;t.BitmapText=t.TilingSpriteRenderer=t.TilingSprite=t.AnimatedSprite=undefined;var i=r("debf");Object.defineProperty(t,"AnimatedSprite",{enumerable:true,get:function e(){return s(i).default}});var n=r("8786");Object.defineProperty(t,"TilingSprite",{enumerable:true,get:function e(){return s(n).default}});var a=r("a446");Object.defineProperty(t,"TilingSpriteRenderer",{enumerable:true,get:function e(){return s(a).default}});var o=r("40b6");Object.defineProperty(t,"BitmapText",{enumerable:true,get:function e(){return s(o).default}});r("476a");r("aef4");r("f1e4");function s(e){return e&&e.__esModule?e:{default:e}}},"63c9":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=s(n);var o=r("df7c");function s(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){u(this,t);var r=l(this,e.call(this,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n    vTextureCoord = aTextureCoord;\n}","varying vec2 vTextureCoord;\nuniform sampler2D uSampler;\nuniform float m[20];\nuniform float uAlpha;\n\nvoid main(void)\n{\n    vec4 c = texture2D(uSampler, vTextureCoord);\n\n    if (uAlpha == 0.0) {\n        gl_FragColor = c;\n        return;\n    }\n\n    // Un-premultiply alpha before applying the color matrix. See issue #3539.\n    if (c.a > 0.0) {\n      c.rgb /= c.a;\n    }\n\n    vec4 result;\n\n    result.r = (m[0] * c.r);\n        result.r += (m[1] * c.g);\n        result.r += (m[2] * c.b);\n        result.r += (m[3] * c.a);\n        result.r += m[4];\n\n    result.g = (m[5] * c.r);\n        result.g += (m[6] * c.g);\n        result.g += (m[7] * c.b);\n        result.g += (m[8] * c.a);\n        result.g += m[9];\n\n    result.b = (m[10] * c.r);\n       result.b += (m[11] * c.g);\n       result.b += (m[12] * c.b);\n       result.b += (m[13] * c.a);\n       result.b += m[14];\n\n    result.a = (m[15] * c.r);\n       result.a += (m[16] * c.g);\n       result.a += (m[17] * c.b);\n       result.a += (m[18] * c.a);\n       result.a += m[19];\n\n    vec3 rgb = mix(c.rgb, result.rgb, uAlpha);\n\n    // Premultiply alpha again.\n    rgb *= result.a;\n\n    gl_FragColor = vec4(rgb, result.a);\n}\n"));r.uniforms.m=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0];r.alpha=1;return r}t.prototype._loadMatrix=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var i=t;if(r){this._multiply(i,this.uniforms.m,t);i=this._colorMatrix(i)}this.uniforms.m=i};t.prototype._multiply=function e(t,r,i){t[0]=r[0]*i[0]+r[1]*i[5]+r[2]*i[10]+r[3]*i[15];t[1]=r[0]*i[1]+r[1]*i[6]+r[2]*i[11]+r[3]*i[16];t[2]=r[0]*i[2]+r[1]*i[7]+r[2]*i[12]+r[3]*i[17];t[3]=r[0]*i[3]+r[1]*i[8]+r[2]*i[13]+r[3]*i[18];t[4]=r[0]*i[4]+r[1]*i[9]+r[2]*i[14]+r[3]*i[19]+r[4];t[5]=r[5]*i[0]+r[6]*i[5]+r[7]*i[10]+r[8]*i[15];t[6]=r[5]*i[1]+r[6]*i[6]+r[7]*i[11]+r[8]*i[16];t[7]=r[5]*i[2]+r[6]*i[7]+r[7]*i[12]+r[8]*i[17];t[8]=r[5]*i[3]+r[6]*i[8]+r[7]*i[13]+r[8]*i[18];t[9]=r[5]*i[4]+r[6]*i[9]+r[7]*i[14]+r[8]*i[19]+r[9];t[10]=r[10]*i[0]+r[11]*i[5]+r[12]*i[10]+r[13]*i[15];t[11]=r[10]*i[1]+r[11]*i[6]+r[12]*i[11]+r[13]*i[16];t[12]=r[10]*i[2]+r[11]*i[7]+r[12]*i[12]+r[13]*i[17];t[13]=r[10]*i[3]+r[11]*i[8]+r[12]*i[13]+r[13]*i[18];t[14]=r[10]*i[4]+r[11]*i[9]+r[12]*i[14]+r[13]*i[19]+r[14];t[15]=r[15]*i[0]+r[16]*i[5]+r[17]*i[10]+r[18]*i[15];t[16]=r[15]*i[1]+r[16]*i[6]+r[17]*i[11]+r[18]*i[16];t[17]=r[15]*i[2]+r[16]*i[7]+r[17]*i[12]+r[18]*i[17];t[18]=r[15]*i[3]+r[16]*i[8]+r[17]*i[13]+r[18]*i[18];t[19]=r[15]*i[4]+r[16]*i[9]+r[17]*i[14]+r[18]*i[19]+r[19];return t};t.prototype._colorMatrix=function e(t){var r=new Float32Array(t);r[4]/=255;r[9]/=255;r[14]/=255;r[19]/=255;return r};t.prototype.brightness=function e(t,r){var i=[t,0,0,0,0,0,t,0,0,0,0,0,t,0,0,0,0,0,1,0];this._loadMatrix(i,r)};t.prototype.greyscale=function e(t,r){var i=[t,t,t,0,0,t,t,t,0,0,t,t,t,0,0,0,0,0,1,0];this._loadMatrix(i,r)};t.prototype.blackAndWhite=function e(t){var r=[.3,.6,.1,0,0,.3,.6,.1,0,0,.3,.6,.1,0,0,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.hue=function e(t,r){t=(t||0)/180*Math.PI;var i=Math.cos(t);var n=Math.sin(t);var a=Math.sqrt;var o=1/3;var s=a(o);var u=i+(1-i)*o;var l=o*(1-i)-s*n;var h=o*(1-i)+s*n;var f=o*(1-i)+s*n;var c=i+o*(1-i);var d=o*(1-i)-s*n;var p=o*(1-i)-s*n;var v=o*(1-i)+s*n;var y=i+o*(1-i);var g=[u,l,h,0,0,f,c,d,0,0,p,v,y,0,0,0,0,0,1,0];this._loadMatrix(g,r)};t.prototype.contrast=function e(t,r){var i=(t||0)+1;var n=-.5*(i-1);var a=[i,0,0,0,n,0,i,0,0,n,0,0,i,0,n,0,0,0,1,0];this._loadMatrix(a,r)};t.prototype.saturate=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments[1];var i=t*2/3+1;var n=(i-1)*-.5;var a=[i,n,n,0,0,n,i,n,0,0,n,n,i,0,0,0,0,0,1,0];this._loadMatrix(a,r)};t.prototype.desaturate=function e(){this.saturate(-1)};t.prototype.negative=function e(t){var r=[-1,0,0,1,0,0,-1,0,1,0,0,0,-1,1,0,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.sepia=function e(t){var r=[.393,.7689999,.18899999,0,0,.349,.6859999,.16799999,0,0,.272,.5339999,.13099999,0,0,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.technicolor=function e(t){var r=[1.9125277891456083,-.8545344976951645,-.09155508482755585,0,11.793603434377337,-.3087833385928097,1.7658908555458428,-.10601743074722245,0,-70.35205161461398,-.231103377548616,-.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.polaroid=function e(t){var r=[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.toBGR=function e(t){var r=[0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.kodachrome=function e(t){var r=[1.1285582396593525,-.3967382283601348,-.03992559172921793,0,63.72958762196502,-.16404339962244616,1.0835251566291304,-.05498805115633132,0,24.732407896706203,-.16786010706155763,-.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.browni=function e(t){var r=[.5997023498159715,.34553243048391263,-.2708298674538042,0,47.43192855600873,-.037703249837783157,.8609577587992641,.15059552388459913,0,-36.96841498319127,.24113635128153335,-.07441037908422492,.44972182064877153,0,-7.562075277591283,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.vintage=function e(t){var r=[.6279345635605994,.3202183420819367,-.03965408211312453,0,9.651285835294123,.02578397704808868,.6441188644374771,.03259127616149294,0,7.462829176470591,.0466055556782719,-.0851232987247891,.5241648018700465,0,5.159190588235296,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.colorTone=function e(t,r,i,n,a){t=t||.2;r=r||.15;i=i||16770432;n=n||3375104;var o=(i>>16&255)/255;var s=(i>>8&255)/255;var u=(i&255)/255;var l=(n>>16&255)/255;var h=(n>>8&255)/255;var f=(n&255)/255;var c=[.3,.59,.11,0,0,o,s,u,t,0,l,h,f,r,0,o-l,s-h,u-f,0,0];this._loadMatrix(c,a)};t.prototype.night=function e(t,r){t=t||.1;var i=[t*-2,-t,0,0,0,-t,0,t,0,0,0,t,t*2,0,0,0,0,0,1,0];this._loadMatrix(i,r)};t.prototype.predator=function e(t,r){var i=[11.224130630493164*t,-4.794486999511719*t,-2.8746118545532227*t,0*t,.40342438220977783*t,-3.6330697536468506*t,9.193157196044922*t,-2.951810836791992*t,0*t,-1.316135048866272*t,-3.2184197902679443*t,-4.2375030517578125*t,7.476448059082031*t,0*t,.8044459223747253*t,0,0,0,1,0];this._loadMatrix(i,r)};t.prototype.lsd=function e(t){var r=[2,-.4,.5,0,0,-.5,2,-.4,0,0,-.4,-.5,3,0,0,0,0,0,1,0];this._loadMatrix(r,t)};t.prototype.reset=function e(){var t=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0];this._loadMatrix(t,false)};i(t,[{key:"matrix",get:function e(){return this.uniforms.m},set:function e(t){this.uniforms.m=t}},{key:"alpha",get:function e(){return this.uniforms.uAlpha},set:function e(t){this.uniforms.uAlpha=t}}]);return t}(a.Filter);t.default=f;f.prototype.grayscale=f.prototype.greyscale},6743:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("8d00");var a=m(n);var o=r("c49b");var s=m(o);var u=r("774e");var l=r("aa9d");var h=r("a506");var f=r("61df");var c=m(f);var d=r("bd43");var p=m(d);var v=r("4d1d");var y=m(v);var g=r("fc76");var _=m(g);function m(e){return e&&e.__esModule?e:{default:e}}function b(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function x(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function T(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var E={texture:true,children:false,baseTexture:true};var w=function(e){T(t,e);function t(r,i,n){b(this,t);n=n||document.createElement("canvas");n.width=3;n.height=3;var a=s.default.fromCanvas(n,c.default.SCALE_MODE,"text");a.orig=new u.Rectangle;a.trim=new u.Rectangle;var o=x(this,e.call(this,a));s.default.addToCache(o._texture,o._texture.baseTexture.textureCacheIds[0]);o.canvas=n;o.context=o.canvas.getContext("2d");o.resolution=c.default.RESOLUTION;o._text=null;o._style=null;o._styleListener=null;o._font="";o.text=r;o.style=i;o.localStyleID=-1;return o}t.prototype.updateText=function e(t){var r=this._style;if(this.localStyleID!==r.styleID){this.dirty=true;this.localStyleID=r.styleID}if(!this.dirty&&t){return}this._font=this._style.toFontString();var i=this.context;var n=y.default.measureText(this._text,this._style,this._style.wordWrap,this.canvas);var a=n.width;var o=n.height;var s=n.lines;var u=n.lineHeight;var l=n.lineWidths;var h=n.maxLineWidth;var f=n.fontProperties;this.canvas.width=Math.ceil((Math.max(1,a)+r.padding*2)*this.resolution);this.canvas.height=Math.ceil((Math.max(1,o)+r.padding*2)*this.resolution);i.scale(this.resolution,this.resolution);i.clearRect(0,0,this.canvas.width,this.canvas.height);i.font=this._font;i.strokeStyle=r.stroke;i.lineWidth=r.strokeThickness;i.textBaseline=r.textBaseline;i.lineJoin=r.lineJoin;i.miterLimit=r.miterLimit;var c=void 0;var d=void 0;if(r.dropShadow){i.fillStyle=r.dropShadowColor;i.globalAlpha=r.dropShadowAlpha;i.shadowBlur=r.dropShadowBlur;if(r.dropShadowBlur>0){i.shadowColor=r.dropShadowColor}var p=Math.cos(r.dropShadowAngle)*r.dropShadowDistance;var v=Math.sin(r.dropShadowAngle)*r.dropShadowDistance;for(var g=0;g<s.length;g++){c=r.strokeThickness/2;d=r.strokeThickness/2+g*u+f.ascent;if(r.align==="right"){c+=h-l[g]}else if(r.align==="center"){c+=(h-l[g])/2}if(r.fill){this.drawLetterSpacing(s[g],c+p+r.padding,d+v+r.padding);if(r.stroke&&r.strokeThickness){i.strokeStyle=r.dropShadowColor;this.drawLetterSpacing(s[g],c+p+r.padding,d+v+r.padding,true);i.strokeStyle=r.stroke}}}}i.shadowBlur=0;i.globalAlpha=1;i.fillStyle=this._generateFillStyle(r,s);for(var _=0;_<s.length;_++){c=r.strokeThickness/2;d=r.strokeThickness/2+_*u+f.ascent;if(r.align==="right"){c+=h-l[_]}else if(r.align==="center"){c+=(h-l[_])/2}if(r.stroke&&r.strokeThickness){this.drawLetterSpacing(s[_],c+r.padding,d+r.padding,true)}if(r.fill){this.drawLetterSpacing(s[_],c+r.padding,d+r.padding)}}this.updateTexture()};t.prototype.drawLetterSpacing=function e(t,r,i){var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;var a=this._style;var o=a.letterSpacing;if(o===0){if(n){this.context.strokeText(t,r,i)}else{this.context.fillText(t,r,i)}return}var s=String.prototype.split.call(t,"");var u=r;var l=0;var h="";while(l<t.length){h=s[l++];if(n){this.context.strokeText(h,u,i)}else{this.context.fillText(h,u,i)}u+=this.context.measureText(h).width+o}};t.prototype.updateTexture=function e(){var t=this.canvas;if(this._style.trim){var r=(0,_.default)(t);if(r.data){t.width=r.width;t.height=r.height;this.context.putImageData(r.data,0,0)}}var i=this._texture;var n=this._style;var a=n.trim?0:n.padding;var o=i.baseTexture;o.hasLoaded=true;o.resolution=this.resolution;o.realWidth=t.width;o.realHeight=t.height;o.width=t.width/this.resolution;o.height=t.height/this.resolution;i.trim.width=i._frame.width=t.width/this.resolution;i.trim.height=i._frame.height=t.height/this.resolution;i.trim.x=-a;i.trim.y=-a;i.orig.width=i._frame.width-a*2;i.orig.height=i._frame.height-a*2;this._onTextureUpdate();o.emit("update",o);this.dirty=false};t.prototype.renderWebGL=function t(r){if(this.resolution!==r.resolution){this.resolution=r.resolution;this.dirty=true}this.updateText(true);e.prototype.renderWebGL.call(this,r)};t.prototype._renderCanvas=function t(r){if(this.resolution!==r.resolution){this.resolution=r.resolution;this.dirty=true}this.updateText(true);e.prototype._renderCanvas.call(this,r)};t.prototype.getLocalBounds=function t(r){this.updateText(true);return e.prototype.getLocalBounds.call(this,r)};t.prototype._calculateBounds=function e(){this.updateText(true);this.calculateVertices();this._bounds.addQuad(this.vertexData)};t.prototype._onStyleChange=function e(){this.dirty=true};t.prototype._generateFillStyle=function e(t,r){if(!Array.isArray(t.fill)){return t.fill}if(navigator.isCocoonJS){return t.fill[0]}var i=void 0;var n=void 0;var a=void 0;var o=void 0;var s=this.canvas.width/this.resolution;var u=this.canvas.height/this.resolution;var l=t.fill.slice();var f=t.fillGradientStops.slice();if(!f.length){var c=l.length+1;for(var d=1;d<c;++d){f.push(d/c)}}l.unshift(t.fill[0]);f.unshift(0);l.push(t.fill[t.fill.length-1]);f.push(1);if(t.fillGradientType===h.TEXT_GRADIENT.LINEAR_VERTICAL){i=this.context.createLinearGradient(s/2,0,s/2,u);n=(l.length+1)*r.length;a=0;for(var p=0;p<r.length;p++){a+=1;for(var v=0;v<l.length;v++){if(typeof f[v]==="number"){o=f[v]/r.length+p/r.length}else{o=a/n}i.addColorStop(o,l[v]);a++}}}else{i=this.context.createLinearGradient(0,u/2,s,u/2);n=l.length+1;a=1;for(var y=0;y<l.length;y++){if(typeof f[y]==="number"){o=f[y]}else{o=a/n}i.addColorStop(o,l[y]);a++}}return i};t.prototype.destroy=function t(r){if(typeof r==="boolean"){r={children:r}}r=Object.assign({},E,r);e.prototype.destroy.call(this,r);this.context=null;this.canvas=null;this._style=null};i(t,[{key:"width",get:function e(){this.updateText(true);return Math.abs(this.scale.x)*this._texture.orig.width},set:function e(t){this.updateText(true);var r=(0,l.sign)(this.scale.x)||1;this.scale.x=r*t/this._texture.orig.width;this._width=t}},{key:"height",get:function e(){this.updateText(true);return Math.abs(this.scale.y)*this._texture.orig.height},set:function e(t){this.updateText(true);var r=(0,l.sign)(this.scale.y)||1;this.scale.y=r*t/this._texture.orig.height;this._height=t}},{key:"style",get:function e(){return this._style},set:function e(t){t=t||{};if(t instanceof p.default){this._style=t}else{this._style=new p.default(t)}this.localStyleID=-1;this.dirty=true}},{key:"text",get:function e(){return this._text},set:function e(t){t=String(t===""||t===null||t===undefined?" ":t);if(this._text===t){return}this._text=t;this.dirty=true}}]);return t}(a.default);t.default=w},"69df":function(e,t,r){"use strict";t.__esModule=true;t.default=s;var i=r("0b16");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}var o=void 0;function s(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:window.location;if(e.indexOf("data:")===0){return""}t=t||window.location;if(!o){o=document.createElement("a")}o.href=e;e=n.default.parse(o.href);var r=!e.port&&t.port===""||e.port===t.port;if(e.hostname!==t.hostname||!r||e.protocol!==t.protocol){return"anonymous"}return""}},"6aaf":function(e,t,r){"use strict";t.__esModule=true;t.default=u;var i=r("e4ec");var n=s(i);var a=r("a506");var o=r("aa9d");function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t,r){var i=e.shape;var s=i.x;var u=i.y;var l=void 0;var h=void 0;if(e.type===a.SHAPES.CIRC){l=i.radius;h=i.radius}else{l=i.width;h=i.height}if(l===0||h===0){return}var f=Math.floor(30*Math.sqrt(i.radius))||Math.floor(15*Math.sqrt(i.width+i.height));var c=Math.PI*2/f;if(e.fill){var d=(0,o.hex2rgb)(e.fillColor);var p=e.fillAlpha;var v=d[0]*p;var y=d[1]*p;var g=d[2]*p;var _=t.points;var m=t.indices;var b=_.length/6;m.push(b);for(var x=0;x<f+1;x++){_.push(s,u,v,y,g,p);_.push(s+Math.sin(c*x)*l,u+Math.cos(c*x)*h,v,y,g,p);m.push(b++,b++)}m.push(b-1)}if(e.lineWidth){var T=e.points;e.points=[];for(var E=0;E<f;E++){e.points.push(s+Math.sin(c*-E)*l,u+Math.cos(c*-E)*h)}e.points.push(e.points[0],e.points[1]);(0,n.default)(e,t,r);e.points=T}}},"6bb5":function(e,t,r){"use strict";if(!Number.isInteger){Number.isInteger=function e(t){return typeof t==="number"&&isFinite(t)&&Math.floor(t)===t}}},"6bf3":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("aa9d");var a=r("d886");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){u(this,t);var r=l(this,e.call(this));r.children=[];return r}t.prototype.onChildrenChange=function e(){};t.prototype.addChild=function e(t){var r=arguments.length;if(r>1){for(var i=0;i<r;i++){this.addChild(arguments[i])}}else{if(t.parent){t.parent.removeChild(t)}t.parent=this;t.transform._parentID=-1;this.children.push(t);this._boundsID++;this.onChildrenChange(this.children.length-1);t.emit("added",this)}return t};t.prototype.addChildAt=function e(t,r){if(r<0||r>this.children.length){throw new Error(t+"addChildAt: The index "+r+" supplied is out of bounds "+this.children.length)}if(t.parent){t.parent.removeChild(t)}t.parent=this;t.transform._parentID=-1;this.children.splice(r,0,t);this._boundsID++;this.onChildrenChange(r);t.emit("added",this);return t};t.prototype.swapChildren=function e(t,r){if(t===r){return}var i=this.getChildIndex(t);var n=this.getChildIndex(r);this.children[i]=r;this.children[n]=t;this.onChildrenChange(i<n?i:n)};t.prototype.getChildIndex=function e(t){var r=this.children.indexOf(t);if(r===-1){throw new Error("The supplied DisplayObject must be a child of the caller")}return r};t.prototype.setChildIndex=function e(t,r){if(r<0||r>=this.children.length){throw new Error("The index "+r+" supplied is out of bounds "+this.children.length)}var i=this.getChildIndex(t);(0,n.removeItems)(this.children,i,1);this.children.splice(r,0,t);this.onChildrenChange(r)};t.prototype.getChildAt=function e(t){if(t<0||t>=this.children.length){throw new Error("getChildAt: Index ("+t+") does not exist.")}return this.children[t]};t.prototype.removeChild=function e(t){var r=arguments.length;if(r>1){for(var i=0;i<r;i++){this.removeChild(arguments[i])}}else{var a=this.children.indexOf(t);if(a===-1)return null;t.parent=null;t.transform._parentID=-1;(0,n.removeItems)(this.children,a,1);this._boundsID++;this.onChildrenChange(a);t.emit("removed",this)}return t};t.prototype.removeChildAt=function e(t){var r=this.getChildAt(t);r.parent=null;r.transform._parentID=-1;(0,n.removeItems)(this.children,t,1);this._boundsID++;this.onChildrenChange(t);r.emit("removed",this);return r};t.prototype.removeChildren=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments[1];var i=t;var n=typeof r==="number"?r:this.children.length;var a=n-i;var o=void 0;if(a>0&&a<=n){o=this.children.splice(i,a);for(var s=0;s<o.length;++s){o[s].parent=null;if(o[s].transform){o[s].transform._parentID=-1}}this._boundsID++;this.onChildrenChange(t);for(var u=0;u<o.length;++u){o[u].emit("removed",this)}return o}else if(a===0&&this.children.length===0){return[]}throw new RangeError("removeChildren: numeric values are outside the acceptable range.")};t.prototype.updateTransform=function e(){this._boundsID++;this.transform.updateTransform(this.parent.transform);this.worldAlpha=this.alpha*this.parent.worldAlpha;for(var t=0,r=this.children.length;t<r;++t){var i=this.children[t];if(i.visible){i.updateTransform()}}};t.prototype.calculateBounds=function e(){this._bounds.clear();this._calculateBounds();for(var t=0;t<this.children.length;t++){var r=this.children[t];if(!r.visible||!r.renderable){continue}r.calculateBounds();if(r._mask){r._mask.calculateBounds();this._bounds.addBoundsMask(r._bounds,r._mask._bounds)}else if(r.filterArea){this._bounds.addBoundsArea(r._bounds,r.filterArea)}else{this._bounds.addBounds(r._bounds)}}this._lastBoundsID=this._boundsID};t.prototype._calculateBounds=function e(){};t.prototype.renderWebGL=function e(t){if(!this.visible||this.worldAlpha<=0||!this.renderable){return}if(this._mask||this._filters){this.renderAdvancedWebGL(t)}else{this._renderWebGL(t);for(var r=0,i=this.children.length;r<i;++r){this.children[r].renderWebGL(t)}}};t.prototype.renderAdvancedWebGL=function e(t){t.flush();var r=this._filters;var i=this._mask;if(r){if(!this._enabledFilters){this._enabledFilters=[]}this._enabledFilters.length=0;for(var n=0;n<r.length;n++){if(r[n].enabled){this._enabledFilters.push(r[n])}}if(this._enabledFilters.length){t.filterManager.pushFilter(this,this._enabledFilters)}}if(i){t.maskManager.pushMask(this,this._mask)}this._renderWebGL(t);for(var a=0,o=this.children.length;a<o;a++){this.children[a].renderWebGL(t)}t.flush();if(i){t.maskManager.popMask(this,this._mask)}if(r&&this._enabledFilters&&this._enabledFilters.length){t.filterManager.popFilter()}};t.prototype._renderWebGL=function e(t){};t.prototype._renderCanvas=function e(t){};t.prototype.renderCanvas=function e(t){if(!this.visible||this.worldAlpha<=0||!this.renderable){return}if(this._mask){t.maskManager.pushMask(this._mask)}this._renderCanvas(t);for(var r=0,i=this.children.length;r<i;++r){this.children[r].renderCanvas(t)}if(this._mask){t.maskManager.popMask(t)}};t.prototype.destroy=function t(r){e.prototype.destroy.call(this);var i=typeof r==="boolean"?r:r&&r.children;var n=this.removeChildren(0,this.children.length);if(i){for(var a=0;a<n.length;++a){n[a].destroy(r)}}};i(t,[{key:"width",get:function e(){return this.scale.x*this.getLocalBounds().width},set:function e(t){var r=this.getLocalBounds().width;if(r!==0){this.scale.x=t/r}else{this.scale.x=1}this._width=t}},{key:"height",get:function e(){return this.scale.y*this.getLocalBounds().height},set:function e(t){var r=this.getLocalBounds().height;if(r!==0){this.scale.y=t/r}else{this.scale.y=1}this._height=t}}]);return t}(o.default);t.default=f;f.prototype.containerUpdateTransform=f.prototype.updateTransform},"6d8f":function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;i(this,e);this.x=t;this.y=r}e.prototype.clone=function t(){return new e(this.x,this.y)};e.prototype.copy=function e(t){this.set(t.x,t.y)};e.prototype.equals=function e(t){return t.x===this.x&&t.y===this.y};e.prototype.set=function e(t,r){this.x=t||0;this.y=r||(r!==0?this.x:0)};return e}();t.default=n},"6f0a":function(e,t,r){"use strict";t.__esModule=true;var i=r("6d8f");var n=o(i);var a=r("a506");function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++){r[i]=arguments[i]}s(this,e);if(Array.isArray(r[0])){r=r[0]}if(r[0]instanceof n.default){var o=[];for(var u=0,l=r.length;u<l;u++){o.push(r[u].x,r[u].y)}r=o}this.closed=true;this.points=r;this.type=a.SHAPES.POLY}e.prototype.clone=function t(){return new e(this.points.slice())};e.prototype.close=function e(){var t=this.points;if(t[0]!==t[t.length-2]||t[1]!==t[t.length-1]){t.push(t[0],t[1])}};e.prototype.contains=function e(t,r){var i=false;var n=this.points.length/2;for(var a=0,o=n-1;a<n;o=a++){var s=this.points[a*2];var u=this.points[a*2+1];var l=this.points[o*2];var h=this.points[o*2+1];var f=u>r!==h>r&&t<(l-s)*((r-u)/(h-u))+s;if(f){i=!i}}return i};return e}();t.default=u},"6f1d":function(e,t,r){"use strict";t.__esModule=true;t.default={accessible:false,accessibleTitle:null,accessibleHint:null,tabIndex:0,_accessibleActive:false,_accessibleDiv:false}},"72f6":function(e,t,r){"use strict";t.__esModule=true;var i=r("a506");function n(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(t){n(this,e);this.renderer=t}e.prototype.pushMask=function e(t){var r=this.renderer;r.context.save();var i=t.alpha;var n=t.transform.worldTransform;var a=r.resolution;r.context.setTransform(n.a*a,n.b*a,n.c*a,n.d*a,n.tx*a,n.ty*a);if(!t._texture){this.renderGraphicsShape(t);r.context.clip()}t.worldAlpha=i};e.prototype.renderGraphicsShape=function e(t){var r=this.renderer.context;var n=t.graphicsData.length;if(n===0){return}r.beginPath();for(var a=0;a<n;a++){var o=t.graphicsData[a];var s=o.shape;if(o.type===i.SHAPES.POLY){var u=s.points;var l=o.holes;var h=void 0;var f=void 0;r.moveTo(u[0],u[1]);for(var c=2;c<u.length;c+=2){r.lineTo(u[c],u[c+1])}if(u[0]===u[u.length-2]&&u[1]===u[u.length-1]){r.closePath()}if(l.length>0){h=0;for(var d=0;d<u.length;d+=2){h+=u[d]*u[d+3]-u[d+1]*u[d+2]}for(var p=0;p<l.length;p++){u=l[p].points;f=0;for(var v=0;v<u.length;v+=2){f+=u[v]*u[v+3]-u[v+1]*u[v+2]}r.moveTo(u[0],u[1]);if(f*h<0){for(var y=2;y<u.length;y+=2){r.lineTo(u[y],u[y+1])}}else{for(var g=u.length-2;g>=2;g-=2){r.lineTo(u[g],u[g+1])}}}}}else if(o.type===i.SHAPES.RECT){r.rect(s.x,s.y,s.width,s.height);r.closePath()}else if(o.type===i.SHAPES.CIRC){r.arc(s.x,s.y,s.radius,0,2*Math.PI);r.closePath()}else if(o.type===i.SHAPES.ELIP){var _=s.width*2;var m=s.height*2;var b=s.x-_/2;var x=s.y-m/2;var T=.5522848;var E=_/2*T;var w=m/2*T;var S=b+_;var O=x+m;var M=b+_/2;var P=x+m/2;r.moveTo(b,P);r.bezierCurveTo(b,P-w,M-E,x,M,x);r.bezierCurveTo(M+E,x,S,P-w,S,P);r.bezierCurveTo(S,P+w,M+E,O,M,O);r.bezierCurveTo(M-E,O,b,P+w,b,P);r.closePath()}else if(o.type===i.SHAPES.RREC){var C=s.x;var A=s.y;var R=s.width;var I=s.height;var D=s.radius;var L=Math.min(R,I)/2|0;D=D>L?L:D;r.moveTo(C,A+D);r.lineTo(C,A+I-D);r.quadraticCurveTo(C,A+I,C+D,A+I);r.lineTo(C+R-D,A+I);r.quadraticCurveTo(C+R,A+I,C+R,A+I-D);r.lineTo(C+R,A+D);r.quadraticCurveTo(C+R,A,C+R-D,A);r.lineTo(C+D,A);r.quadraticCurveTo(C,A,C,A+D);r.closePath()}}};e.prototype.popMask=function e(t){t.context.restore();t.invalidateBlendMode()};e.prototype.destroy=function e(){};return e}();t.default=a},"73f5":function(e,t,r){"use strict";t.__esModule=true;t.default=n;var i=["attribute vec2 aVertexPosition;","attribute vec2 aTextureCoord;","uniform float strength;","uniform mat3 projectionMatrix;","varying vec2 vBlurTexCoords[%size%];","void main(void)","{","gl_Position = vec4((projectionMatrix * vec3((aVertexPosition), 1.0)).xy, 0.0, 1.0);","%blur%","}"].join("\n");function n(e,t){var r=Math.ceil(e/2);var n=i;var a="";var o=void 0;if(t){o="vBlurTexCoords[%index%] = aTextureCoord + vec2(%sampleIndex% * strength, 0.0);"}else{o="vBlurTexCoords[%index%] = aTextureCoord + vec2(0.0, %sampleIndex% * strength);"}for(var s=0;s<e;s++){var u=o.replace("%index%",s);u=u.replace("%sampleIndex%",s-(r-1)+".0");a+=u;a+="\n"}n=n.replace("%blur%",a);n=n.replace("%size%",e);return n}},7467:function(e,t,r){"use strict";t.__esModule=true;t.default=n;var i=r("a506");function n(){var e=[];var t=[];for(var r=0;r<32;r++){e[r]=r;t[r]=r}e[i.BLEND_MODES.NORMAL_NPM]=i.BLEND_MODES.NORMAL;e[i.BLEND_MODES.ADD_NPM]=i.BLEND_MODES.ADD;e[i.BLEND_MODES.SCREEN_NPM]=i.BLEND_MODES.SCREEN;t[i.BLEND_MODES.NORMAL]=i.BLEND_MODES.NORMAL_NPM;t[i.BLEND_MODES.ADD]=i.BLEND_MODES.ADD_NPM;t[i.BLEND_MODES.SCREEN]=i.BLEND_MODES.SCREEN_NPM;var n=[];n.push(t);n.push(e);return n}},"7726a":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=f(n);var o=r("238d");var s=h(o);var u=r("0120");var l=h(u);function h(e){return e&&e.__esModule?e:{default:e}}function f(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function d(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function p(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var v=function(e){p(t,e);function t(r,i,n,o){c(this,t);var u=d(this,e.call(this));u.blurXFilter=new s.default(r,i,n,o);u.blurYFilter=new l.default(r,i,n,o);u.padding=0;u.resolution=n||a.settings.RESOLUTION;u.quality=i||4;u.blur=r||8;return u}t.prototype.apply=function e(t,r,i){var n=t.getRenderTarget(true);this.blurXFilter.apply(t,r,n,true);this.blurYFilter.apply(t,n,i,false);t.returnRenderTarget(n)};i(t,[{key:"blur",get:function e(){return this.blurXFilter.blur},set:function e(t){this.blurXFilter.blur=this.blurYFilter.blur=t;this.padding=Math.max(Math.abs(this.blurXFilter.strength),Math.abs(this.blurYFilter.strength))*2}},{key:"quality",get:function e(){return this.blurXFilter.quality},set:function e(t){this.blurXFilter.quality=this.blurYFilter.quality=t}},{key:"blurX",get:function e(){return this.blurXFilter.blur},set:function e(t){this.blurXFilter.blur=t;this.padding=Math.max(Math.abs(this.blurXFilter.strength),Math.abs(this.blurYFilter.strength))*2}},{key:"blurY",get:function e(){return this.blurYFilter.blur},set:function e(t){this.blurYFilter.blur=t;this.padding=Math.max(Math.abs(this.blurXFilter.strength),Math.abs(this.blurYFilter.strength))*2}},{key:"blendMode",get:function e(){return this.blurYFilter._blendMode},set:function e(t){this.blurYFilter._blendMode=t}}]);return t}(a.Filter);t.default=v},"774e":function(e,t,r){"use strict";t.__esModule=true;var i=r("6d8f");Object.defineProperty(t,"Point",{enumerable:true,get:function e(){return c(i).default}});var n=r("1cfe");Object.defineProperty(t,"ObservablePoint",{enumerable:true,get:function e(){return c(n).default}});var a=r("f87a");Object.defineProperty(t,"Matrix",{enumerable:true,get:function e(){return c(a).default}});var o=r("ff33");Object.defineProperty(t,"GroupD8",{enumerable:true,get:function e(){return c(o).default}});var s=r("5b76");Object.defineProperty(t,"Circle",{enumerable:true,get:function e(){return c(s).default}});var u=r("a843");Object.defineProperty(t,"Ellipse",{enumerable:true,get:function e(){return c(u).default}});var l=r("6f0a");Object.defineProperty(t,"Polygon",{enumerable:true,get:function e(){return c(l).default}});var h=r("4d71");Object.defineProperty(t,"Rectangle",{enumerable:true,get:function e(){return c(h).default}});var f=r("8845");Object.defineProperty(t,"RoundedRectangle",{enumerable:true,get:function e(){return c(f).default}});function c(e){return e&&e.__esModule?e:{default:e}}},"77a3":function(e,t,r){"use strict";t.__esModule=true;t.blobMiddlewareFactory=o;var i=r("5f08");var n=r("b358");var a=window.URL||window.webkitURL;function o(){return function e(t,r){if(!t.data){r();return}if(t.xhr&&t.xhrType===i.Resource.XHR_RESPONSE_TYPE.BLOB){if(!window.Blob||typeof t.data==="string"){var o=t.xhr.getResponseHeader("content-type");if(o&&o.indexOf("image")===0){t.data=new Image;t.data.src="data:"+o+";base64,"+(0,n.encodeBinary)(t.xhr.responseText);t.type=i.Resource.TYPE.IMAGE;t.data.onload=function(){t.data.onload=null;r()};return}}else if(t.data.type.indexOf("image")===0){var s=a.createObjectURL(t.data);t.blob=t.data;t.data=new Image;t.data.src=s;t.type=i.Resource.TYPE.IMAGE;t.data.onload=function(){a.revokeObjectURL(s);t.data.onload=null;r()};return}}r()}}},7881:function(e,t,r){"use strict";t.__esModule=true;var i=r("589c");var n=s(i);var a=r("c49b");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(r,i){u(this,t);var a=null;if(!(r instanceof n.default)){var o=arguments[1];var s=arguments[2];var h=arguments[3];var f=arguments[4];console.warn("Please use RenderTexture.create("+o+", "+s+") instead of the ctor directly.");a=arguments[0];i=null;r=new n.default(o,s,h,f)}var c=l(this,e.call(this,r,i));c.legacyRenderer=a;c.valid=true;c._updateUvs();return c}t.prototype.resize=function e(t,r,i){t=Math.ceil(t);r=Math.ceil(r);this.valid=t>0&&r>0;this._frame.width=this.orig.width=t;this._frame.height=this.orig.height=r;if(!i){this.baseTexture.resize(t,r)}this._updateUvs()};t.create=function e(r,i,a,o){return new t(new n.default(r,i,a,o))};return t}(o.default);t.default=f},"7b0f":function(e,t,r){"use strict";t.__esModule=true;t.default=n;var i=r("a506");function n(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};t[i.DRAW_MODES.POINTS]=e.POINTS;t[i.DRAW_MODES.LINES]=e.LINES;t[i.DRAW_MODES.LINE_LOOP]=e.LINE_LOOP;t[i.DRAW_MODES.LINE_STRIP]=e.LINE_STRIP;t[i.DRAW_MODES.TRIANGLES]=e.TRIANGLES;t[i.DRAW_MODES.TRIANGLE_STRIP]=e.TRIANGLE_STRIP;t[i.DRAW_MODES.TRIANGLE_FAN]=e.TRIANGLE_FAN;return t}},"7bc8":function(e,t,r){"use strict";t.__esModule=true;var i=r("cfe7");var n=p(i);var a=r("77a3");var o=r("ba10");var s=p(o);var u=r("48b2");var l=p(u);var h=r("371d");var f=p(h);var c=r("a172");var d=p(c);function p(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function y(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function g(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var _=function(e){g(t,e);function t(r,i){v(this,t);var n=y(this,e.call(this,r,i));s.default.call(n);for(var a=0;a<t._pixiMiddleware.length;++a){n.use(t._pixiMiddleware[a]())}n.onStart.add(function(e){return n.emit("start",e)});n.onProgress.add(function(e,t){return n.emit("progress",e,t)});n.onError.add(function(e,t,r){return n.emit("error",e,t,r)});n.onLoad.add(function(e,t){return n.emit("load",e,t)});n.onComplete.add(function(e,t){return n.emit("complete",e,t)});return n}t.addPixiMiddleware=function e(r){t._pixiMiddleware.push(r)};t.prototype.destroy=function e(){this.removeAllListeners();this.reset()};return t}(n.default);t.default=_;for(var m in s.default.prototype){_.prototype[m]=s.default.prototype[m]}_._pixiMiddleware=[a.blobMiddlewareFactory,l.default,f.default,d.default];var b=n.default.Resource;b.setExtensionXhrType("fnt",b.XHR_RESPONSE_TYPE.DOCUMENT)},"7ce1":function(e,t,r){"use strict";t.__esModule=true;var i=r("c88f");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s=function(){function e(t,r,i){o(this,e);this.gl=t;this.color=[0,0,0];this.points=[];this.indices=[];this.buffer=n.default.GLBuffer.createVertexBuffer(t);this.indexBuffer=n.default.GLBuffer.createIndexBuffer(t);this.dirty=true;this.nativeLines=false;this.glPoints=null;this.glIndices=null;this.shader=r;this.vao=new n.default.VertexArrayObject(t,i).addIndex(this.indexBuffer).addAttribute(this.buffer,r.attributes.aVertexPosition,t.FLOAT,false,4*6,0).addAttribute(this.buffer,r.attributes.aColor,t.FLOAT,false,4*6,2*4)}e.prototype.reset=function e(){this.points.length=0;this.indices.length=0};e.prototype.upload=function e(){this.glPoints=new Float32Array(this.points);this.buffer.upload(this.glPoints);this.glIndices=new Uint16Array(this.indices);this.indexBuffer.upload(this.glIndices);this.dirty=false};e.prototype.destroy=function e(){this.color=null;this.points=null;this.indices=null;this.vao.destroy();this.buffer.destroy();this.indexBuffer.destroy();this.gl=null;this.buffer=null;this.indexBuffer=null;this.glPoints=null;this.glIndices=null};return e}();t.default=s},"7d2e":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=s(n);var o=r("df7c");function s(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(r,i){u(this,t);var n=new a.Matrix;r.renderable=false;var o=l(this,e.call(this,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 filterMatrix;\n\nvarying vec2 vTextureCoord;\nvarying vec2 vFilterCoord;\n\nvoid main(void)\n{\n   gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n   vFilterCoord = ( filterMatrix * vec3( aTextureCoord, 1.0)  ).xy;\n   vTextureCoord = aTextureCoord;\n}","varying vec2 vFilterCoord;\nvarying vec2 vTextureCoord;\n\nuniform vec2 scale;\n\nuniform sampler2D uSampler;\nuniform sampler2D mapSampler;\n\nuniform vec4 filterArea;\nuniform vec4 filterClamp;\n\nvoid main(void)\n{\n  vec4 map =  texture2D(mapSampler, vFilterCoord);\n\n  map -= 0.5;\n  map.xy *= scale / filterArea.xy;\n\n  gl_FragColor = texture2D(uSampler, clamp(vec2(vTextureCoord.x + map.x, vTextureCoord.y + map.y), filterClamp.xy, filterClamp.zw));\n}\n"));o.maskSprite=r;o.maskMatrix=n;o.uniforms.mapSampler=r._texture;o.uniforms.filterMatrix=n;o.uniforms.scale={x:1,y:1};if(i===null||i===undefined){i=20}o.scale=new a.Point(i,i);return o}t.prototype.apply=function e(t,r,i){this.uniforms.filterMatrix=t.calculateSpriteMatrix(this.maskMatrix,this.maskSprite);this.uniforms.scale.x=this.scale.x;this.uniforms.scale.y=this.scale.y;t.applyFilter(this,r,i)};i(t,[{key:"map",get:function e(){return this.uniforms.mapSampler},set:function e(t){this.uniforms.mapSampler=t}}]);return t}(a.Filter);t.default=f},"7ec4":function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(t){i(this,e);this.maxMilliseconds=t;this.frameStart=0}e.prototype.beginFrame=function e(){this.frameStart=Date.now()};e.prototype.allowedToUpload=function e(){return Date.now()-this.frameStart<this.maxMilliseconds};return e}();t.default=n},"7ef9":function(e,t,r){"use strict";t.__esModule=true;var i=r("e397");Object.defineProperty(t,"InteractionData",{enumerable:true,get:function e(){return u(i).default}});var n=r("95eb");Object.defineProperty(t,"InteractionManager",{enumerable:true,get:function e(){return u(n).default}});var a=r("e268");Object.defineProperty(t,"interactiveTarget",{enumerable:true,get:function e(){return u(a).default}});var o=r("8a73");Object.defineProperty(t,"InteractionTrackingData",{enumerable:true,get:function e(){return u(o).default}});var s=r("d6d7");Object.defineProperty(t,"InteractionEvent",{enumerable:true,get:function e(){return u(s).default}});function u(e){return e&&e.__esModule?e:{default:e}}},8114:function(e,t,r){var i,n,a;!function(r){var o=/iPhone/i,s=/iPod/i,u=/iPad/i,l=/\bAndroid(?:.+)Mobile\b/i,h=/Android/i,f=/\bAndroid(?:.+)SD4930UR\b/i,c=/\bAndroid(?:.+)(?:KF[A-Z]{2,4})\b/i,d=/Windows Phone/i,p=/\bWindows(?:.+)ARM\b/i,v=/BlackBerry/i,y=/BB10/i,g=/Opera Mini/i,_=/\b(CriOS|Chrome)(?:.+)Mobile/i,m=/\Mobile(?:.+)Firefox\b/i;function b(e,t){return e.test(t)}function x(e){var t=e||("undefined"!=typeof navigator?navigator.userAgent:""),r=t.split("[FBAN");void 0!==r[1]&&(t=r[0]),void 0!==(r=t.split("Twitter"))[1]&&(t=r[0]);var i={apple:{phone:b(o,t)&&!b(d,t),ipod:b(s,t),tablet:!b(o,t)&&b(u,t)&&!b(d,t),device:(b(o,t)||b(s,t)||b(u,t))&&!b(d,t)},amazon:{phone:b(f,t),tablet:!b(f,t)&&b(c,t),device:b(f,t)||b(c,t)},android:{phone:!b(d,t)&&b(f,t)||!b(d,t)&&b(l,t),tablet:!b(d,t)&&!b(f,t)&&!b(l,t)&&(b(c,t)||b(h,t)),device:!b(d,t)&&(b(f,t)||b(c,t)||b(l,t)||b(h,t))},windows:{phone:b(d,t),tablet:b(p,t),device:b(d,t)||b(p,t)},other:{blackberry:b(v,t),blackberry10:b(y,t),opera:b(g,t),firefox:b(m,t),chrome:b(_,t),device:b(v,t)||b(y,t)||b(g,t)||b(m,t)||b(_,t)}};return i.any=i.apple.device||i.android.device||i.windows.device||i.other.device,i.phone=i.apple.phone||i.android.phone||i.windows.phone,i.tablet=i.apple.tablet||i.android.tablet||i.windows.tablet,i}true&&e.exports&&"undefined"==typeof window?e.exports=x:true&&e.exports&&"undefined"!=typeof window?e.exports=x():true?!(n=[],i=r.isMobile=x(),a=typeof i==="function"?i.apply(t,n):i,a!==undefined&&(e.exports=a)):undefined}(this)},"825e":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("774e");var a=r("281c");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){u(this,t);var r=l(this,e.call(this));r.position=new n.ObservablePoint(r.onChange,r,0,0);r.scale=new n.ObservablePoint(r.onChange,r,1,1);r.pivot=new n.ObservablePoint(r.onChange,r,0,0);r.skew=new n.ObservablePoint(r.updateSkew,r,0,0);r._rotation=0;r._cx=1;r._sx=0;r._cy=0;r._sy=1;r._localID=0;r._currentLocalID=0;return r}t.prototype.onChange=function e(){this._localID++};t.prototype.updateSkew=function e(){this._cx=Math.cos(this._rotation+this.skew._y);this._sx=Math.sin(this._rotation+this.skew._y);this._cy=-Math.sin(this._rotation-this.skew._x);this._sy=Math.cos(this._rotation-this.skew._x);this._localID++};t.prototype.updateLocalTransform=function e(){var t=this.localTransform;if(this._localID!==this._currentLocalID){t.a=this._cx*this.scale._x;t.b=this._sx*this.scale._x;t.c=this._cy*this.scale._y;t.d=this._sy*this.scale._y;t.tx=this.position._x-(this.pivot._x*t.a+this.pivot._y*t.c);t.ty=this.position._y-(this.pivot._x*t.b+this.pivot._y*t.d);this._currentLocalID=this._localID;this._parentID=-1}};t.prototype.updateTransform=function e(t){var r=this.localTransform;if(this._localID!==this._currentLocalID){r.a=this._cx*this.scale._x;r.b=this._sx*this.scale._x;r.c=this._cy*this.scale._y;r.d=this._sy*this.scale._y;r.tx=this.position._x-(this.pivot._x*r.a+this.pivot._y*r.c);r.ty=this.position._y-(this.pivot._x*r.b+this.pivot._y*r.d);this._currentLocalID=this._localID;this._parentID=-1}if(this._parentID!==t._worldID){var i=t.worldTransform;var n=this.worldTransform;n.a=r.a*i.a+r.b*i.c;n.b=r.a*i.b+r.b*i.d;n.c=r.c*i.a+r.d*i.c;n.d=r.c*i.b+r.d*i.d;n.tx=r.tx*i.a+r.ty*i.c+i.tx;n.ty=r.tx*i.b+r.ty*i.d+i.ty;this._parentID=t._worldID;this._worldID++}};t.prototype.setFromMatrix=function e(t){t.decompose(this);this._localID++};i(t,[{key:"rotation",get:function e(){return this._rotation},set:function e(t){if(this._rotation!==t){this._rotation=t;this.updateSkew()}}}]);return t}(o.default);t.default=f},"839e":function(e,t,r){"use strict";t.__esModule=true;t.default=n;function i(e){var t=document.createElement("canvas");t.width=6;t.height=1;var r=t.getContext("2d");r.fillStyle=e;r.fillRect(0,0,6,1);return t}function n(){if(typeof document==="undefined"){return false}var e=i("#ff00ff");var t=i("#ffff00");var r=document.createElement("canvas");r.width=6;r.height=1;var n=r.getContext("2d");n.globalCompositeOperation="multiply";n.drawImage(e,0,0);n.drawImage(t,2,0);var a=n.getImageData(2,0,1,1);if(!a){return false}var o=a.data;return o[0]===255&&o[1]===0&&o[2]===0}},"84df":function(e,t,r){"use strict";t.__esModule=true;t.default=s;var i=r("a506");var n=r("839e");var a=o(n);function o(e){return e&&e.__esModule?e:{default:e}}function s(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];if((0,a.default)()){e[i.BLEND_MODES.NORMAL]="source-over";e[i.BLEND_MODES.ADD]="lighter";e[i.BLEND_MODES.MULTIPLY]="multiply";e[i.BLEND_MODES.SCREEN]="screen";e[i.BLEND_MODES.OVERLAY]="overlay";e[i.BLEND_MODES.DARKEN]="darken";e[i.BLEND_MODES.LIGHTEN]="lighten";e[i.BLEND_MODES.COLOR_DODGE]="color-dodge";e[i.BLEND_MODES.COLOR_BURN]="color-burn";e[i.BLEND_MODES.HARD_LIGHT]="hard-light";e[i.BLEND_MODES.SOFT_LIGHT]="soft-light";e[i.BLEND_MODES.DIFFERENCE]="difference";e[i.BLEND_MODES.EXCLUSION]="exclusion";e[i.BLEND_MODES.HUE]="hue";e[i.BLEND_MODES.SATURATION]="saturate";e[i.BLEND_MODES.COLOR]="color";e[i.BLEND_MODES.LUMINOSITY]="luminosity"}else{e[i.BLEND_MODES.NORMAL]="source-over";e[i.BLEND_MODES.ADD]="lighter";e[i.BLEND_MODES.MULTIPLY]="source-over";e[i.BLEND_MODES.SCREEN]="source-over";e[i.BLEND_MODES.OVERLAY]="source-over";e[i.BLEND_MODES.DARKEN]="source-over";e[i.BLEND_MODES.LIGHTEN]="source-over";e[i.BLEND_MODES.COLOR_DODGE]="source-over";e[i.BLEND_MODES.COLOR_BURN]="source-over";e[i.BLEND_MODES.HARD_LIGHT]="source-over";e[i.BLEND_MODES.SOFT_LIGHT]="source-over";e[i.BLEND_MODES.DIFFERENCE]="source-over";e[i.BLEND_MODES.EXCLUSION]="source-over";e[i.BLEND_MODES.HUE]="source-over";e[i.BLEND_MODES.SATURATION]="source-over";e[i.BLEND_MODES.COLOR]="source-over";e[i.BLEND_MODES.LUMINOSITY]="source-over"}e[i.BLEND_MODES.NORMAL_NPM]=e[i.BLEND_MODES.NORMAL];e[i.BLEND_MODES.ADD_NPM]=e[i.BLEND_MODES.ADD];e[i.BLEND_MODES.SCREEN_NPM]=e[i.BLEND_MODES.SCREEN];return e}},"85ef":function(e,t,r){"use strict";t.__esModule=true;var i=r("5abd");var n=l(i);var a=r("774e");var o=r("df7c");var s=r("e1f8");var u=l(s);function l(e){return e&&e.__esModule?e:{default:e}}function h(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function f(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function c(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var d=function(e){c(t,e);function t(r){h(this,t);var i=new a.Matrix;var n=f(this,e.call(this,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 otherMatrix;\n\nvarying vec2 vMaskCoord;\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = aTextureCoord;\n    vMaskCoord = ( otherMatrix * vec3( aTextureCoord, 1.0)  ).xy;\n}\n","varying vec2 vMaskCoord;\nvarying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform sampler2D mask;\nuniform float alpha;\nuniform vec4 maskClamp;\n\nvoid main(void)\n{\n    float clip = step(3.5,\n        step(maskClamp.x, vMaskCoord.x) +\n        step(maskClamp.y, vMaskCoord.y) +\n        step(vMaskCoord.x, maskClamp.z) +\n        step(vMaskCoord.y, maskClamp.w));\n\n    vec4 original = texture2D(uSampler, vTextureCoord);\n    vec4 masky = texture2D(mask, vMaskCoord);\n\n    original *= (masky.r * masky.a * alpha * clip);\n\n    gl_FragColor = original;\n}\n"));r.renderable=false;n.maskSprite=r;n.maskMatrix=i;return n}t.prototype.apply=function e(t,r,i,n){var a=this.maskSprite;var o=this.maskSprite.texture;if(!o.valid){return}if(!o.transform){o.transform=new u.default(o,0)}o.transform.update();this.uniforms.mask=o;this.uniforms.otherMatrix=t.calculateSpriteMatrix(this.maskMatrix,a).prepend(o.transform.mapCoord);this.uniforms.alpha=a.worldAlpha;this.uniforms.maskClamp=o.transform.uClampFrame;t.applyFilter(this,r,i,n)};return t}(n.default);t.default=d},"872d":function(e,t){var r=function(e,t){var r={data:{}};r.gl=e;var n=Object.keys(t);for(var a=0;a<n.length;a++){var u=n[a];var l=u.split(".");var h=l[l.length-1];var f=s(l,r);var c=t[u];f.data[h]=c;f.gl=e;Object.defineProperty(f,h,{get:i(h),set:o(h,c)})}return r};var i=function(e){return function(){return this.data[e].value}};var n={float:function e(t,r,i){t.uniform1f(r,i)},vec2:function e(t,r,i){t.uniform2f(r,i[0],i[1])},vec3:function e(t,r,i){t.uniform3f(r,i[0],i[1],i[2])},vec4:function e(t,r,i){t.uniform4f(r,i[0],i[1],i[2],i[3])},int:function e(t,r,i){t.uniform1i(r,i)},ivec2:function e(t,r,i){t.uniform2i(r,i[0],i[1])},ivec3:function e(t,r,i){t.uniform3i(r,i[0],i[1],i[2])},ivec4:function e(t,r,i){t.uniform4i(r,i[0],i[1],i[2],i[3])},bool:function e(t,r,i){t.uniform1i(r,i)},bvec2:function e(t,r,i){t.uniform2i(r,i[0],i[1])},bvec3:function e(t,r,i){t.uniform3i(r,i[0],i[1],i[2])},bvec4:function e(t,r,i){t.uniform4i(r,i[0],i[1],i[2],i[3])},mat2:function e(t,r,i){t.uniformMatrix2fv(r,false,i)},mat3:function e(t,r,i){t.uniformMatrix3fv(r,false,i)},mat4:function e(t,r,i){t.uniformMatrix4fv(r,false,i)},sampler2D:function e(t,r,i){t.uniform1i(r,i)}};var a={float:function e(t,r,i){t.uniform1fv(r,i)},vec2:function e(t,r,i){t.uniform2fv(r,i)},vec3:function e(t,r,i){t.uniform3fv(r,i)},vec4:function e(t,r,i){t.uniform4fv(r,i)},int:function e(t,r,i){t.uniform1iv(r,i)},ivec2:function e(t,r,i){t.uniform2iv(r,i)},ivec3:function e(t,r,i){t.uniform3iv(r,i)},ivec4:function e(t,r,i){t.uniform4iv(r,i)},bool:function e(t,r,i){t.uniform1iv(r,i)},bvec2:function e(t,r,i){t.uniform2iv(r,i)},bvec3:function e(t,r,i){t.uniform3iv(r,i)},bvec4:function e(t,r,i){t.uniform4iv(r,i)},sampler2D:function e(t,r,i){t.uniform1iv(r,i)}};function o(e,t){return function(r){this.data[e].value=r;var i=this.data[e].location;if(t.size===1){n[t.type](this.gl,i,r)}else{a[t.type](this.gl,i,r)}}}function s(e,t){var r=t;for(var i=0;i<e.length-1;i++){var n=r[e[i]]||{data:{}};r[e[i]]=n;r=n}return r}e.exports=r},8786:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=l(n);var o=r("fd43");var s=u(o);function u(e){return e&&e.__esModule?e:{default:e}}function l(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function h(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function f(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function c(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var d=new a.Point;var p=function(e){c(t,e);function t(r){var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:100;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:100;h(this,t);var o=f(this,e.call(this,r));o.tileTransform=new a.TransformStatic;o._width=i;o._height=n;o._canvasPattern=null;o.uvTransform=r.transform||new a.TextureMatrix(r);o.pluginName="tilingSprite";o.uvRespectAnchor=false;return o}t.prototype._onTextureUpdate=function e(){if(this.uvTransform){this.uvTransform.texture=this._texture}this.cachedTint=16777215};t.prototype._renderWebGL=function e(t){var r=this._texture;if(!r||!r.valid){return}this.tileTransform.updateLocalTransform();this.uvTransform.update();t.setObjectRenderer(t.plugins[this.pluginName]);t.plugins[this.pluginName].render(this)};t.prototype._renderCanvas=function e(t){var r=this._texture;if(!r.baseTexture.hasLoaded){return}var i=t.context;var n=this.worldTransform;var o=t.resolution;var u=r.rotate===2;var l=r.baseTexture;var h=l.resolution;var f=this.tilePosition.x/this.tileScale.x%r.orig.width*h;var c=this.tilePosition.y/this.tileScale.y%r.orig.height*h;if(this._textureID!==this._texture._updateID||this.cachedTint!==this.tint){this._textureID=this._texture._updateID;var d=new a.CanvasRenderTarget(r.orig.width,r.orig.height,h);if(this.tint!==16777215){this.tintedTexture=s.default.getTintedTexture(this,this.tint);d.context.drawImage(this.tintedTexture,0,0)}else{var p=r._frame.x*h;var v=r._frame.y*h;var y=r._frame.width*h;var g=r._frame.height*h;var _=(r.trim?r.trim.width:r.orig.width)*h;var m=(r.trim?r.trim.height:r.orig.height)*h;var b=(r.trim?r.trim.x:0)*h;var x=(r.trim?r.trim.y:0)*h;if(u){d.context.rotate(-Math.PI/2);d.context.translate(-m,0);d.context.drawImage(l.source,p,v,y,g,-x,b,m,_)}else{d.context.drawImage(l.source,p,v,y,g,b,x,_,m)}}this.cachedTint=this.tint;this._canvasPattern=d.context.createPattern(d.canvas,"repeat")}i.globalAlpha=this.worldAlpha;i.setTransform(n.a*o,n.b*o,n.c*o,n.d*o,n.tx*o,n.ty*o);t.setBlendMode(this.blendMode);i.fillStyle=this._canvasPattern;i.scale(this.tileScale.x/h,this.tileScale.y/h);var T=this.anchor.x*-this._width*h;var E=this.anchor.y*-this._height*h;if(this.uvRespectAnchor){i.translate(f,c);i.fillRect(-f+T,-c+E,this._width/this.tileScale.x*h,this._height/this.tileScale.y*h)}else{i.translate(f+T,c+E);i.fillRect(-f,-c,this._width/this.tileScale.x*h,this._height/this.tileScale.y*h)}};t.prototype._calculateBounds=function e(){var t=this._width*-this._anchor._x;var r=this._height*-this._anchor._y;var i=this._width*(1-this._anchor._x);var n=this._height*(1-this._anchor._y);this._bounds.addFrame(this.transform,t,r,i,n)};t.prototype.getLocalBounds=function t(r){if(this.children.length===0){this._bounds.minX=this._width*-this._anchor._x;this._bounds.minY=this._height*-this._anchor._y;this._bounds.maxX=this._width*(1-this._anchor._x);this._bounds.maxY=this._height*(1-this._anchor._y);if(!r){if(!this._localBoundsRect){this._localBoundsRect=new a.Rectangle}r=this._localBoundsRect}return this._bounds.getRectangle(r)}return e.prototype.getLocalBounds.call(this,r)};t.prototype.containsPoint=function e(t){this.worldTransform.applyInverse(t,d);var r=this._width;var i=this._height;var n=-r*this.anchor._x;if(d.x>=n&&d.x<n+r){var a=-i*this.anchor._y;if(d.y>=a&&d.y<a+i){return true}}return false};t.prototype.destroy=function t(r){e.prototype.destroy.call(this,r);this.tileTransform=null;this.uvTransform=null};t.from=function e(r,i,n){return new t(a.Texture.from(r),i,n)};t.fromFrame=function e(r,i,n){var o=a.utils.TextureCache[r];if(!o){throw new Error('The frameId "'+r+'" does not exist in the texture cache '+this)}return new t(o,i,n)};t.fromImage=function e(r,i,n,o,s){return new t(a.Texture.fromImage(r,o,s),i,n)};i(t,[{key:"clampMargin",get:function e(){return this.uvTransform.clampMargin},set:function e(t){this.uvTransform.clampMargin=t;this.uvTransform.update(true)}},{key:"tileScale",get:function e(){return this.tileTransform.scale},set:function e(t){this.tileTransform.scale.copy(t)}},{key:"tilePosition",get:function e(){return this.tileTransform.position},set:function e(t){this.tileTransform.position.copy(t)}},{key:"width",get:function e(){return this._width},set:function e(t){this._width=t}},{key:"height",get:function e(){return this._height},set:function e(t){this._height=t}}]);return t}(a.Sprite);t.default=p},8845:function(e,t,r){"use strict";t.__esModule=true;var i=r("a506");function n(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var o=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;var s=arguments.length>4&&arguments[4]!==undefined?arguments[4]:20;n(this,e);this.x=t;this.y=r;this.width=a;this.height=o;this.radius=s;this.type=i.SHAPES.RREC}e.prototype.clone=function t(){return new e(this.x,this.y,this.width,this.height,this.radius)};e.prototype.contains=function e(t,r){if(this.width<=0||this.height<=0){return false}if(t>=this.x&&t<=this.x+this.width){if(r>=this.y&&r<=this.y+this.height){if(r>=this.y+this.radius&&r<=this.y+this.height-this.radius||t>=this.x+this.radius&&t<=this.x+this.width-this.radius){return true}var i=t-(this.x+this.radius);var n=r-(this.y+this.radius);var a=this.radius*this.radius;if(i*i+n*n<=a){return true}i=t-(this.x+this.width-this.radius);if(i*i+n*n<=a){return true}n=r-(this.y+this.height-this.radius);if(i*i+n*n<=a){return true}i=t-(this.x+this.radius);if(i*i+n*n<=a){return true}}}return false};return e}();t.default=a},"8a73":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();function n(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(t){n(this,e);this._pointerId=t;this._flags=e.FLAGS.NONE}e.prototype._doSet=function e(t,r){if(r){this._flags=this._flags|t}else{this._flags=this._flags&~t}};i(e,[{key:"pointerId",get:function e(){return this._pointerId}},{key:"flags",get:function e(){return this._flags},set:function e(t){this._flags=t}},{key:"none",get:function e(){return this._flags===this.constructor.FLAGS.NONE}},{key:"over",get:function e(){return(this._flags&this.constructor.FLAGS.OVER)!==0},set:function e(t){this._doSet(this.constructor.FLAGS.OVER,t)}},{key:"rightDown",get:function e(){return(this._flags&this.constructor.FLAGS.RIGHT_DOWN)!==0},set:function e(t){this._doSet(this.constructor.FLAGS.RIGHT_DOWN,t)}},{key:"leftDown",get:function e(){return(this._flags&this.constructor.FLAGS.LEFT_DOWN)!==0},set:function e(t){this._doSet(this.constructor.FLAGS.LEFT_DOWN,t)}}]);return e}();t.default=a;a.FLAGS=Object.freeze({NONE:0,OVER:1<<0,LEFT_DOWN:1<<1,RIGHT_DOWN:1<<2})},"8c4d":function(e,t,r){"use strict";t.__esModule=true;t.default=i;function i(e,t,r,i,n,a,o,s,u){var l=arguments.length>9&&arguments[9]!==undefined?arguments[9]:[];var h=0;var f=0;var c=0;var d=0;var p=0;l.push(e,t);for(var v=1,y=0;v<=u;++v){y=v/u;h=1-y;f=h*h;c=f*h;d=y*y;p=d*y;l.push(c*e+3*f*y*r+3*h*d*n+p*o,c*t+3*f*y*i+3*h*d*a+p*s)}return l}},"8caf":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=s(n);var o=r("df7c");function s(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;u(this,t);var i=l(this,e.call(this,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n    vTextureCoord = aTextureCoord;\n}","varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform float uAlpha;\n\nvoid main(void)\n{\n   gl_FragColor = texture2D(uSampler, vTextureCoord) * uAlpha;\n}\n"));i.alpha=r;i.glShaderKey="alpha";return i}i(t,[{key:"alpha",get:function e(){return this.uniforms.uAlpha},set:function e(t){this.uniforms.uAlpha=t}}]);return t}(a.Filter);t.default=f},"8d00":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("774e");var a=r("aa9d");var o=r("a506");var s=r("c49b");var u=f(s);var l=r("6bf3");var h=f(l);function f(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function d(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function p(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var v=new n.Point;var y=function(e){p(t,e);function t(r){c(this,t);var i=d(this,e.call(this));i._anchor=new n.ObservablePoint(i._onAnchorUpdate,i,r?r.defaultAnchor.x:0,r?r.defaultAnchor.y:0);i._texture=null;i._width=0;i._height=0;i._tint=null;i._tintRGB=null;i.tint=16777215;i.blendMode=o.BLEND_MODES.NORMAL;i.shader=null;i.cachedTint=16777215;i.texture=r||u.default.EMPTY;i.vertexData=new Float32Array(8);i.vertexTrimmedData=null;i._transformID=-1;i._textureID=-1;i._transformTrimmedID=-1;i._textureTrimmedID=-1;i.pluginName="sprite";return i}t.prototype._onTextureUpdate=function e(){this._textureID=-1;this._textureTrimmedID=-1;this.cachedTint=16777215;if(this._width){this.scale.x=(0,a.sign)(this.scale.x)*this._width/this._texture.orig.width}if(this._height){this.scale.y=(0,a.sign)(this.scale.y)*this._height/this._texture.orig.height}};t.prototype._onAnchorUpdate=function e(){this._transformID=-1;this._transformTrimmedID=-1};t.prototype.calculateVertices=function e(){if(this._transformID===this.transform._worldID&&this._textureID===this._texture._updateID){return}this._transformID=this.transform._worldID;this._textureID=this._texture._updateID;var t=this._texture;var r=this.transform.worldTransform;var i=r.a;var n=r.b;var a=r.c;var o=r.d;var s=r.tx;var u=r.ty;var l=this.vertexData;var h=t.trim;var f=t.orig;var c=this._anchor;var d=0;var p=0;var v=0;var y=0;if(h){p=h.x-c._x*f.width;d=p+h.width;y=h.y-c._y*f.height;v=y+h.height}else{p=-c._x*f.width;d=p+f.width;y=-c._y*f.height;v=y+f.height}l[0]=i*p+a*y+s;l[1]=o*y+n*p+u;l[2]=i*d+a*y+s;l[3]=o*y+n*d+u;l[4]=i*d+a*v+s;l[5]=o*v+n*d+u;l[6]=i*p+a*v+s;l[7]=o*v+n*p+u};t.prototype.calculateTrimmedVertices=function e(){if(!this.vertexTrimmedData){this.vertexTrimmedData=new Float32Array(8)}else if(this._transformTrimmedID===this.transform._worldID&&this._textureTrimmedID===this._texture._updateID){return}this._transformTrimmedID=this.transform._worldID;this._textureTrimmedID=this._texture._updateID;var t=this._texture;var r=this.vertexTrimmedData;var i=t.orig;var n=this._anchor;var a=this.transform.worldTransform;var o=a.a;var s=a.b;var u=a.c;var l=a.d;var h=a.tx;var f=a.ty;var c=-n._x*i.width;var d=c+i.width;var p=-n._y*i.height;var v=p+i.height;r[0]=o*c+u*p+h;r[1]=l*p+s*c+f;r[2]=o*d+u*p+h;r[3]=l*p+s*d+f;r[4]=o*d+u*v+h;r[5]=l*v+s*d+f;r[6]=o*c+u*v+h;r[7]=l*v+s*c+f};t.prototype._renderWebGL=function e(t){this.calculateVertices();t.setObjectRenderer(t.plugins[this.pluginName]);t.plugins[this.pluginName].render(this)};t.prototype._renderCanvas=function e(t){t.plugins[this.pluginName].render(this)};t.prototype._calculateBounds=function e(){var t=this._texture.trim;var r=this._texture.orig;if(!t||t.width===r.width&&t.height===r.height){this.calculateVertices();this._bounds.addQuad(this.vertexData)}else{this.calculateTrimmedVertices();this._bounds.addQuad(this.vertexTrimmedData)}};t.prototype.getLocalBounds=function t(r){if(this.children.length===0){this._bounds.minX=this._texture.orig.width*-this._anchor._x;this._bounds.minY=this._texture.orig.height*-this._anchor._y;this._bounds.maxX=this._texture.orig.width*(1-this._anchor._x);this._bounds.maxY=this._texture.orig.height*(1-this._anchor._y);if(!r){if(!this._localBoundsRect){this._localBoundsRect=new n.Rectangle}r=this._localBoundsRect}return this._bounds.getRectangle(r)}return e.prototype.getLocalBounds.call(this,r)};t.prototype.containsPoint=function e(t){this.worldTransform.applyInverse(t,v);var r=this._texture.orig.width;var i=this._texture.orig.height;var n=-r*this.anchor.x;var a=0;if(v.x>=n&&v.x<n+r){a=-i*this.anchor.y;if(v.y>=a&&v.y<a+i){return true}}return false};t.prototype.destroy=function t(r){e.prototype.destroy.call(this,r);this._texture.off("update",this._onTextureUpdate,this);this._anchor=null;var i=typeof r==="boolean"?r:r&&r.texture;if(i){var n=typeof r==="boolean"?r:r&&r.baseTexture;this._texture.destroy(!!n)}this._texture=null;this.shader=null};t.from=function e(r){return new t(u.default.from(r))};t.fromFrame=function e(r){var i=a.TextureCache[r];if(!i){throw new Error('The frameId "'+r+'" does not exist in the texture cache')}return new t(i)};t.fromImage=function e(r,i,n){return new t(u.default.fromImage(r,i,n))};i(t,[{key:"width",get:function e(){return Math.abs(this.scale.x)*this._texture.orig.width},set:function e(t){var r=(0,a.sign)(this.scale.x)||1;this.scale.x=r*t/this._texture.orig.width;this._width=t}},{key:"height",get:function e(){return Math.abs(this.scale.y)*this._texture.orig.height},set:function e(t){var r=(0,a.sign)(this.scale.y)||1;this.scale.y=r*t/this._texture.orig.height;this._height=t}},{key:"anchor",get:function e(){return this._anchor},set:function e(t){this._anchor.copy(t)}},{key:"tint",get:function e(){return this._tint},set:function e(t){this._tint=t;this._tintRGB=(t>>16)+(t&65280)+((t&255)<<16)}},{key:"texture",get:function e(){return this._texture},set:function e(t){if(this._texture===t){return}this._texture=t||u.default.EMPTY;this.cachedTint=16777215;this._textureID=-1;this._textureTrimmedID=-1;if(t){if(t.baseTexture.hasLoaded){this._onTextureUpdate()}else{t.once("update",this._onTextureUpdate,this)}}}}]);return t}(h.default);t.default=y},"912c":function(e,t,r){"use strict";(function(e){t.__esModule=true;t.loader=t.prepare=t.particles=t.mesh=t.loaders=t.interaction=t.filters=t.extras=t.extract=t.accessibility=undefined;var i=r("dd89");Object.keys(i).forEach(function(e){if(e==="default"||e==="__esModule")return;Object.defineProperty(t,e,{enumerable:true,get:function t(){return i[e]}})});var n=r("dc07");Object.keys(n).forEach(function(e){if(e==="default"||e==="__esModule")return;Object.defineProperty(t,e,{enumerable:true,get:function t(){return n[e]}})});var a=r("9938");var o=O(a);var s=r("622c");var u=S(s);var l=r("e5e9");var h=S(l);var f=r("635d");var c=S(f);var d=r("cbfa");var p=S(d);var v=r("7ef9");var y=S(v);var g=r("aa59");var _=S(g);var m=r("0e7f");var b=S(m);var x=r("dc6c");var T=S(x);var E=r("33a2");var w=S(E);function S(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function O(e){return e&&e.__esModule?e:{default:e}}n.utils.mixins.performMixins();var M=_.shared||null;t.accessibility=u;t.extract=h;t.extras=c;t.filters=p;t.interaction=y;t.loaders=_;t.mesh=b;t.particles=T;t.prepare=w;t.loader=M;if(typeof o.default==="function"){(0,o.default)(t)}e.PIXI=t}).call(this,r("c8ba"))},9280:function(e,t){var r=function(e,t){if(!i){var r=Object.keys(n);i={};for(var a=0;a<r.length;++a){var o=r[a];i[e[o]]=n[o]}}return i[t]};var i=null;var n={FLOAT:"float",FLOAT_VEC2:"vec2",FLOAT_VEC3:"vec3",FLOAT_VEC4:"vec4",INT:"int",INT_VEC2:"ivec2",INT_VEC3:"ivec3",INT_VEC4:"ivec4",BOOL:"bool",BOOL_VEC2:"bvec2",BOOL_VEC3:"bvec3",BOOL_VEC4:"bvec4",FLOAT_MAT2:"mat2",FLOAT_MAT3:"mat3",FLOAT_MAT4:"mat4",SAMPLER_2D:"sampler2D"};e.exports=r},"95eb":function(e,t,r){"use strict";t.__esModule=true;var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var n=r("dc07");var a=g(n);var o=r("e397");var s=y(o);var u=r("d6d7");var l=y(u);var h=r("8a73");var f=y(h);var c=r("ba10");var d=y(c);var p=r("e268");var v=y(p);function y(e){return e&&e.__esModule?e:{default:e}}function g(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function _(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function m(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function b(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}a.utils.mixins.delayMixin(a.DisplayObject.prototype,v.default);var x=1;var T={target:null,data:{global:null}};var E=function(e){b(t,e);function t(r,i){_(this,t);var n=m(this,e.call(this));i=i||{};n.renderer=r;n.autoPreventDefault=i.autoPreventDefault!==undefined?i.autoPreventDefault:true;n.interactionFrequency=i.interactionFrequency||10;n.mouse=new s.default;n.mouse.identifier=x;n.mouse.global.set(-999999);n.activeInteractionData={};n.activeInteractionData[x]=n.mouse;n.interactionDataPool=[];n.eventData=new l.default;n.interactionDOMElement=null;n.moveWhenInside=false;n.eventsAdded=false;n.mouseOverRenderer=false;n.supportsTouchEvents="ontouchstart"in window;n.supportsPointerEvents=!!window.PointerEvent;n.onPointerUp=n.onPointerUp.bind(n);n.processPointerUp=n.processPointerUp.bind(n);n.onPointerCancel=n.onPointerCancel.bind(n);n.processPointerCancel=n.processPointerCancel.bind(n);n.onPointerDown=n.onPointerDown.bind(n);n.processPointerDown=n.processPointerDown.bind(n);n.onPointerMove=n.onPointerMove.bind(n);n.processPointerMove=n.processPointerMove.bind(n);n.onPointerOut=n.onPointerOut.bind(n);n.processPointerOverOut=n.processPointerOverOut.bind(n);n.onPointerOver=n.onPointerOver.bind(n);n.cursorStyles={default:"inherit",pointer:"pointer"};n.currentCursorMode=null;n.cursor=null;n._tempPoint=new a.Point;n.resolution=1;n.setTargetElement(n.renderer.view,n.renderer.resolution);return n}t.prototype.hitTest=function e(t,r){T.target=null;T.data.global=t;if(!r){r=this.renderer._lastObjectRendered}this.processInteractive(T,r,null,true);return T.target};t.prototype.setTargetElement=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;this.removeEvents();this.interactionDOMElement=t;this.resolution=r;this.addEvents()};t.prototype.addEvents=function e(){if(!this.interactionDOMElement){return}a.ticker.shared.add(this.update,this,a.UPDATE_PRIORITY.INTERACTION);if(window.navigator.msPointerEnabled){this.interactionDOMElement.style["-ms-content-zooming"]="none";this.interactionDOMElement.style["-ms-touch-action"]="none"}else if(this.supportsPointerEvents){this.interactionDOMElement.style["touch-action"]="none"}if(this.supportsPointerEvents){window.document.addEventListener("pointermove",this.onPointerMove,true);this.interactionDOMElement.addEventListener("pointerdown",this.onPointerDown,true);this.interactionDOMElement.addEventListener("pointerleave",this.onPointerOut,true);this.interactionDOMElement.addEventListener("pointerover",this.onPointerOver,true);window.addEventListener("pointercancel",this.onPointerCancel,true);window.addEventListener("pointerup",this.onPointerUp,true)}else{window.document.addEventListener("mousemove",this.onPointerMove,true);this.interactionDOMElement.addEventListener("mousedown",this.onPointerDown,true);this.interactionDOMElement.addEventListener("mouseout",this.onPointerOut,true);this.interactionDOMElement.addEventListener("mouseover",this.onPointerOver,true);window.addEventListener("mouseup",this.onPointerUp,true)}if(this.supportsTouchEvents){this.interactionDOMElement.addEventListener("touchstart",this.onPointerDown,true);this.interactionDOMElement.addEventListener("touchcancel",this.onPointerCancel,true);this.interactionDOMElement.addEventListener("touchend",this.onPointerUp,true);this.interactionDOMElement.addEventListener("touchmove",this.onPointerMove,true)}this.eventsAdded=true};t.prototype.removeEvents=function e(){if(!this.interactionDOMElement){return}a.ticker.shared.remove(this.update,this);if(window.navigator.msPointerEnabled){this.interactionDOMElement.style["-ms-content-zooming"]="";this.interactionDOMElement.style["-ms-touch-action"]=""}else if(this.supportsPointerEvents){this.interactionDOMElement.style["touch-action"]=""}if(this.supportsPointerEvents){window.document.removeEventListener("pointermove",this.onPointerMove,true);this.interactionDOMElement.removeEventListener("pointerdown",this.onPointerDown,true);this.interactionDOMElement.removeEventListener("pointerleave",this.onPointerOut,true);this.interactionDOMElement.removeEventListener("pointerover",this.onPointerOver,true);window.removeEventListener("pointercancel",this.onPointerCancel,true);window.removeEventListener("pointerup",this.onPointerUp,true)}else{window.document.removeEventListener("mousemove",this.onPointerMove,true);this.interactionDOMElement.removeEventListener("mousedown",this.onPointerDown,true);this.interactionDOMElement.removeEventListener("mouseout",this.onPointerOut,true);this.interactionDOMElement.removeEventListener("mouseover",this.onPointerOver,true);window.removeEventListener("mouseup",this.onPointerUp,true)}if(this.supportsTouchEvents){this.interactionDOMElement.removeEventListener("touchstart",this.onPointerDown,true);this.interactionDOMElement.removeEventListener("touchcancel",this.onPointerCancel,true);this.interactionDOMElement.removeEventListener("touchend",this.onPointerUp,true);this.interactionDOMElement.removeEventListener("touchmove",this.onPointerMove,true)}this.interactionDOMElement=null;this.eventsAdded=false};t.prototype.update=function e(t){this._deltaTime+=t;if(this._deltaTime<this.interactionFrequency){return}this._deltaTime=0;if(!this.interactionDOMElement){return}if(this.didMove){this.didMove=false;return}this.cursor=null;for(var r in this.activeInteractionData){if(this.activeInteractionData.hasOwnProperty(r)){var i=this.activeInteractionData[r];if(i.originalEvent&&i.pointerType!=="touch"){var n=this.configureInteractionEventForDOMEvent(this.eventData,i.originalEvent,i);this.processInteractive(n,this.renderer._lastObjectRendered,this.processPointerOverOut,true)}}}this.setCursorMode(this.cursor)};t.prototype.setCursorMode=function e(t){t=t||"default";if(this.currentCursorMode===t){return}this.currentCursorMode=t;var r=this.cursorStyles[t];if(r){switch(typeof r==="undefined"?"undefined":i(r)){case"string":this.interactionDOMElement.style.cursor=r;break;case"function":r(t);break;case"object":Object.assign(this.interactionDOMElement.style,r);break}}else if(typeof t==="string"&&!Object.prototype.hasOwnProperty.call(this.cursorStyles,t)){this.interactionDOMElement.style.cursor=t}};t.prototype.dispatchEvent=function e(t,r,i){if(!i.stopped){i.currentTarget=t;i.type=r;t.emit(r,i);if(t[r]){t[r](i)}}};t.prototype.mapPositionToPoint=function e(t,r,i){var n=void 0;if(!this.interactionDOMElement.parentElement){n={x:0,y:0,width:0,height:0}}else{n=this.interactionDOMElement.getBoundingClientRect()}var a=navigator.isCocoonJS?this.resolution:1/this.resolution;t.x=(r-n.left)*(this.interactionDOMElement.width/n.width)*a;t.y=(i-n.top)*(this.interactionDOMElement.height/n.height)*a};t.prototype.processInteractive=function e(t,r,i,n,a){if(!r||!r.visible){return false}var o=t.data.global;a=r.interactive||a;var s=false;var u=a;var l=true;if(r.hitArea){if(n){r.worldTransform.applyInverse(o,this._tempPoint);if(!r.hitArea.contains(this._tempPoint.x,this._tempPoint.y)){n=false;l=false}else{s=true}}u=false}else if(r._mask){if(n){if(!r._mask.containsPoint(o)){n=false;l=false}}}if(l&&r.interactiveChildren&&r.children){var h=r.children;for(var f=h.length-1;f>=0;f--){var c=h[f];var d=this.processInteractive(t,c,i,n,u);if(d){if(!c.parent){continue}u=false;if(d){if(t.target){n=false}s=true}}}}if(a){if(n&&!t.target){if(!r.hitArea&&r.containsPoint){if(r.containsPoint(o)){s=true}}}if(r.interactive){if(s&&!t.target){t.target=r}if(i){i(t,r,!!s)}}}return s};t.prototype.onPointerDown=function e(t){if(this.supportsTouchEvents&&t.pointerType==="touch")return;var r=this.normalizeToPointerData(t);if(this.autoPreventDefault&&r[0].isNormalized){t.preventDefault()}var i=r.length;for(var n=0;n<i;n++){var a=r[n];var o=this.getInteractionDataForPointerId(a);var s=this.configureInteractionEventForDOMEvent(this.eventData,a,o);s.data.originalEvent=t;this.processInteractive(s,this.renderer._lastObjectRendered,this.processPointerDown,true);this.emit("pointerdown",s);if(a.pointerType==="touch"){this.emit("touchstart",s)}else if(a.pointerType==="mouse"||a.pointerType==="pen"){var u=a.button===2;this.emit(u?"rightdown":"mousedown",this.eventData)}}};t.prototype.processPointerDown=function e(t,r,i){var n=t.data;var a=t.data.identifier;if(i){if(!r.trackedPointers[a]){r.trackedPointers[a]=new f.default(a)}this.dispatchEvent(r,"pointerdown",t);if(n.pointerType==="touch"){this.dispatchEvent(r,"touchstart",t)}else if(n.pointerType==="mouse"||n.pointerType==="pen"){var o=n.button===2;if(o){r.trackedPointers[a].rightDown=true}else{r.trackedPointers[a].leftDown=true}this.dispatchEvent(r,o?"rightdown":"mousedown",t)}}};t.prototype.onPointerComplete=function e(t,r,i){var n=this.normalizeToPointerData(t);var a=n.length;var o=t.target!==this.interactionDOMElement?"outside":"";for(var s=0;s<a;s++){var u=n[s];var l=this.getInteractionDataForPointerId(u);var h=this.configureInteractionEventForDOMEvent(this.eventData,u,l);h.data.originalEvent=t;this.processInteractive(h,this.renderer._lastObjectRendered,i,r||!o);this.emit(r?"pointercancel":"pointerup"+o,h);if(u.pointerType==="mouse"||u.pointerType==="pen"){var f=u.button===2;this.emit(f?"rightup"+o:"mouseup"+o,h)}else if(u.pointerType==="touch"){this.emit(r?"touchcancel":"touchend"+o,h);this.releaseInteractionDataForPointerId(u.pointerId,l)}}};t.prototype.onPointerCancel=function e(t){if(this.supportsTouchEvents&&t.pointerType==="touch")return;this.onPointerComplete(t,true,this.processPointerCancel)};t.prototype.processPointerCancel=function e(t,r){var i=t.data;var n=t.data.identifier;if(r.trackedPointers[n]!==undefined){delete r.trackedPointers[n];this.dispatchEvent(r,"pointercancel",t);if(i.pointerType==="touch"){this.dispatchEvent(r,"touchcancel",t)}}};t.prototype.onPointerUp=function e(t){if(this.supportsTouchEvents&&t.pointerType==="touch")return;this.onPointerComplete(t,false,this.processPointerUp)};t.prototype.processPointerUp=function e(t,r,i){var n=t.data;var a=t.data.identifier;var o=r.trackedPointers[a];var s=n.pointerType==="touch";var u=n.pointerType==="mouse"||n.pointerType==="pen";var l=false;if(u){var h=n.button===2;var c=f.default.FLAGS;var d=h?c.RIGHT_DOWN:c.LEFT_DOWN;var p=o!==undefined&&o.flags&d;if(i){this.dispatchEvent(r,h?"rightup":"mouseup",t);if(p){this.dispatchEvent(r,h?"rightclick":"click",t);l=true}}else if(p){this.dispatchEvent(r,h?"rightupoutside":"mouseupoutside",t)}if(o){if(h){o.rightDown=false}else{o.leftDown=false}}}if(i){this.dispatchEvent(r,"pointerup",t);if(s)this.dispatchEvent(r,"touchend",t);if(o){if(!u||l){this.dispatchEvent(r,"pointertap",t)}if(s){this.dispatchEvent(r,"tap",t);o.over=false}}}else if(o){this.dispatchEvent(r,"pointerupoutside",t);if(s)this.dispatchEvent(r,"touchendoutside",t)}if(o&&o.none){delete r.trackedPointers[a]}};t.prototype.onPointerMove=function e(t){if(this.supportsTouchEvents&&t.pointerType==="touch")return;var r=this.normalizeToPointerData(t);if(r[0].pointerType==="mouse"||r[0].pointerType==="pen"){this.didMove=true;this.cursor=null}var i=r.length;for(var n=0;n<i;n++){var a=r[n];var o=this.getInteractionDataForPointerId(a);var s=this.configureInteractionEventForDOMEvent(this.eventData,a,o);s.data.originalEvent=t;var u=a.pointerType==="touch"?this.moveWhenInside:true;this.processInteractive(s,this.renderer._lastObjectRendered,this.processPointerMove,u);this.emit("pointermove",s);if(a.pointerType==="touch")this.emit("touchmove",s);if(a.pointerType==="mouse"||a.pointerType==="pen")this.emit("mousemove",s)}if(r[0].pointerType==="mouse"){this.setCursorMode(this.cursor)}};t.prototype.processPointerMove=function e(t,r,i){var n=t.data;var a=n.pointerType==="touch";var o=n.pointerType==="mouse"||n.pointerType==="pen";if(o){this.processPointerOverOut(t,r,i)}if(!this.moveWhenInside||i){this.dispatchEvent(r,"pointermove",t);if(a)this.dispatchEvent(r,"touchmove",t);if(o)this.dispatchEvent(r,"mousemove",t)}};t.prototype.onPointerOut=function e(t){if(this.supportsTouchEvents&&t.pointerType==="touch")return;var r=this.normalizeToPointerData(t);var i=r[0];if(i.pointerType==="mouse"){this.mouseOverRenderer=false;this.setCursorMode(null)}var n=this.getInteractionDataForPointerId(i);var a=this.configureInteractionEventForDOMEvent(this.eventData,i,n);a.data.originalEvent=i;this.processInteractive(a,this.renderer._lastObjectRendered,this.processPointerOverOut,false);this.emit("pointerout",a);if(i.pointerType==="mouse"||i.pointerType==="pen"){this.emit("mouseout",a)}else{this.releaseInteractionDataForPointerId(n.identifier)}};t.prototype.processPointerOverOut=function e(t,r,i){var n=t.data;var a=t.data.identifier;var o=n.pointerType==="mouse"||n.pointerType==="pen";var s=r.trackedPointers[a];if(i&&!s){s=r.trackedPointers[a]=new f.default(a)}if(s===undefined)return;if(i&&this.mouseOverRenderer){if(!s.over){s.over=true;this.dispatchEvent(r,"pointerover",t);if(o){this.dispatchEvent(r,"mouseover",t)}}if(o&&this.cursor===null){this.cursor=r.cursor}}else if(s.over){s.over=false;this.dispatchEvent(r,"pointerout",this.eventData);if(o){this.dispatchEvent(r,"mouseout",t)}if(s.none){delete r.trackedPointers[a]}}};t.prototype.onPointerOver=function e(t){var r=this.normalizeToPointerData(t);var i=r[0];var n=this.getInteractionDataForPointerId(i);var a=this.configureInteractionEventForDOMEvent(this.eventData,i,n);a.data.originalEvent=i;if(i.pointerType==="mouse"){this.mouseOverRenderer=true}this.emit("pointerover",a);if(i.pointerType==="mouse"||i.pointerType==="pen"){this.emit("mouseover",a)}};t.prototype.getInteractionDataForPointerId=function e(t){var r=t.pointerId;var i=void 0;if(r===x||t.pointerType==="mouse"){i=this.mouse}else if(this.activeInteractionData[r]){i=this.activeInteractionData[r]}else{i=this.interactionDataPool.pop()||new s.default;i.identifier=r;this.activeInteractionData[r]=i}i.copyEvent(t);return i};t.prototype.releaseInteractionDataForPointerId=function e(t){var r=this.activeInteractionData[t];if(r){delete this.activeInteractionData[t];r.reset();this.interactionDataPool.push(r)}};t.prototype.configureInteractionEventForDOMEvent=function e(t,r,i){t.data=i;this.mapPositionToPoint(i.global,r.clientX,r.clientY);if(navigator.isCocoonJS&&r.pointerType==="touch"){i.global.x=i.global.x/this.resolution;i.global.y=i.global.y/this.resolution}if(r.pointerType==="touch"){r.globalX=i.global.x;r.globalY=i.global.y}i.originalEvent=r;t.reset();return t};t.prototype.normalizeToPointerData=function e(t){var r=[];if(this.supportsTouchEvents&&t instanceof TouchEvent){for(var i=0,n=t.changedTouches.length;i<n;i++){var a=t.changedTouches[i];if(typeof a.button==="undefined")a.button=t.touches.length?1:0;if(typeof a.buttons==="undefined")a.buttons=t.touches.length?1:0;if(typeof a.isPrimary==="undefined"){a.isPrimary=t.touches.length===1&&t.type==="touchstart"}if(typeof a.width==="undefined")a.width=a.radiusX||1;if(typeof a.height==="undefined")a.height=a.radiusY||1;if(typeof a.tiltX==="undefined")a.tiltX=0;if(typeof a.tiltY==="undefined")a.tiltY=0;if(typeof a.pointerType==="undefined")a.pointerType="touch";if(typeof a.pointerId==="undefined")a.pointerId=a.identifier||0;if(typeof a.pressure==="undefined")a.pressure=a.force||.5;if(typeof a.twist==="undefined")a.twist=0;if(typeof a.tangentialPressure==="undefined")a.tangentialPressure=0;if(typeof a.layerX==="undefined")a.layerX=a.offsetX=a.clientX;if(typeof a.layerY==="undefined")a.layerY=a.offsetY=a.clientY;a.isNormalized=true;r.push(a)}}else if(t instanceof MouseEvent&&(!this.supportsPointerEvents||!(t instanceof window.PointerEvent))){if(typeof t.isPrimary==="undefined")t.isPrimary=true;if(typeof t.width==="undefined")t.width=1;if(typeof t.height==="undefined")t.height=1;if(typeof t.tiltX==="undefined")t.tiltX=0;if(typeof t.tiltY==="undefined")t.tiltY=0;if(typeof t.pointerType==="undefined")t.pointerType="mouse";if(typeof t.pointerId==="undefined")t.pointerId=x;if(typeof t.pressure==="undefined")t.pressure=.5;if(typeof t.twist==="undefined")t.twist=0;if(typeof t.tangentialPressure==="undefined")t.tangentialPressure=0;t.isNormalized=true;r.push(t)}else{r.push(t)}return r};t.prototype.destroy=function e(){this.removeEvents();this.removeAllListeners();this.renderer=null;this.mouse=null;this.eventData=null;this.interactionDOMElement=null;this.onPointerDown=null;this.processPointerDown=null;this.onPointerUp=null;this.processPointerUp=null;this.onPointerCancel=null;this.processPointerCancel=null;this.onPointerMove=null;this.processPointerMove=null;this.onPointerOut=null;this.processPointerOverOut=null;this.onPointerOver=null;this._tempPoint=null};return t}(d.default);t.default=E;a.WebGLRenderer.registerPlugin("interaction",E);a.CanvasRenderer.registerPlugin("interaction",E)},"96c8":function(e,t,r){"use strict";t.__esModule=true;t.default=n;var i=r("a506");function n(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[];t[i.BLEND_MODES.NORMAL]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.ADD]=[e.ONE,e.DST_ALPHA];t[i.BLEND_MODES.MULTIPLY]=[e.DST_COLOR,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.SCREEN]=[e.ONE,e.ONE_MINUS_SRC_COLOR];t[i.BLEND_MODES.OVERLAY]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.DARKEN]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.LIGHTEN]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.COLOR_DODGE]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.COLOR_BURN]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.HARD_LIGHT]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.SOFT_LIGHT]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.DIFFERENCE]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.EXCLUSION]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.HUE]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.SATURATION]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.COLOR]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.LUMINOSITY]=[e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.NORMAL_NPM]=[e.SRC_ALPHA,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA];t[i.BLEND_MODES.ADD_NPM]=[e.SRC_ALPHA,e.DST_ALPHA,e.ONE,e.DST_ALPHA];t[i.BLEND_MODES.SCREEN_NPM]=[e.SRC_ALPHA,e.ONE_MINUS_SRC_COLOR,e.ONE,e.ONE_MINUS_SRC_COLOR];return t}},"974c":function(e,t,r){e.exports={compileProgram:r("0b1a"),defaultValue:r("ac8f"),extractAttributes:r("ec06"),extractUniforms:r("f7ce"),generateUniformAccessObject:r("872d"),setPrecision:r("d035"),mapSize:r("ba33"),mapType:r("9280")}},"97cb":function(e,t,r){"use strict";t.__esModule=true;var i=r("c88f");var n=s(i);var a=r("4f24");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l=function(){function e(t,r,i,n){u(this,e);this.gl=t;this.size=n;this.dynamicProperties=[];this.staticProperties=[];for(var a=0;a<r.length;++a){var o=r[a];o={attribute:o.attribute,size:o.size,uploadFunction:o.uploadFunction,unsignedByte:o.unsignedByte,offset:o.offset};if(i[a]){this.dynamicProperties.push(o)}else{this.staticProperties.push(o)}}this.staticStride=0;this.staticBuffer=null;this.staticData=null;this.staticDataUint32=null;this.dynamicStride=0;this.dynamicBuffer=null;this.dynamicData=null;this.dynamicDataUint32=null;this._updateID=0;this.initBuffers()}e.prototype.initBuffers=function e(){var t=this.gl;var r=0;this.indices=(0,o.default)(this.size);this.indexBuffer=n.default.GLBuffer.createIndexBuffer(t,this.indices,t.STATIC_DRAW);this.dynamicStride=0;for(var i=0;i<this.dynamicProperties.length;++i){var a=this.dynamicProperties[i];a.offset=r;r+=a.size;this.dynamicStride+=a.size}var s=new ArrayBuffer(this.size*this.dynamicStride*4*4);this.dynamicData=new Float32Array(s);this.dynamicDataUint32=new Uint32Array(s);this.dynamicBuffer=n.default.GLBuffer.createVertexBuffer(t,s,t.STREAM_DRAW);var u=0;this.staticStride=0;for(var l=0;l<this.staticProperties.length;++l){var h=this.staticProperties[l];h.offset=u;u+=h.size;this.staticStride+=h.size}var f=new ArrayBuffer(this.size*this.staticStride*4*4);this.staticData=new Float32Array(f);this.staticDataUint32=new Uint32Array(f);this.staticBuffer=n.default.GLBuffer.createVertexBuffer(t,f,t.STATIC_DRAW);this.vao=new n.default.VertexArrayObject(t).addIndex(this.indexBuffer);for(var c=0;c<this.dynamicProperties.length;++c){var d=this.dynamicProperties[c];if(d.unsignedByte){this.vao.addAttribute(this.dynamicBuffer,d.attribute,t.UNSIGNED_BYTE,true,this.dynamicStride*4,d.offset*4)}else{this.vao.addAttribute(this.dynamicBuffer,d.attribute,t.FLOAT,false,this.dynamicStride*4,d.offset*4)}}for(var p=0;p<this.staticProperties.length;++p){var v=this.staticProperties[p];if(v.unsignedByte){this.vao.addAttribute(this.staticBuffer,v.attribute,t.UNSIGNED_BYTE,true,this.staticStride*4,v.offset*4)}else{this.vao.addAttribute(this.staticBuffer,v.attribute,t.FLOAT,false,this.staticStride*4,v.offset*4)}}};e.prototype.uploadDynamic=function e(t,r,i){for(var n=0;n<this.dynamicProperties.length;n++){var a=this.dynamicProperties[n];a.uploadFunction(t,r,i,a.unsignedByte?this.dynamicDataUint32:this.dynamicData,this.dynamicStride,a.offset)}this.dynamicBuffer.upload()};e.prototype.uploadStatic=function e(t,r,i){for(var n=0;n<this.staticProperties.length;n++){var a=this.staticProperties[n];a.uploadFunction(t,r,i,a.unsignedByte?this.staticDataUint32:this.staticData,this.staticStride,a.offset)}this.staticBuffer.upload()};e.prototype.destroy=function e(){this.dynamicProperties=null;this.dynamicBuffer.destroy();this.dynamicBuffer=null;this.dynamicData=null;this.dynamicDataUint32=null;this.staticProperties=null;this.staticBuffer.destroy();this.staticBuffer=null;this.staticData=null;this.staticDataUint32=null};return e}();t.default=l},9938:function(e,t,r){"use strict";t.__esModule=true;t.default=a;var i={};function n(e){if(i[e]){return}var t=(new Error).stack;if(typeof t==="undefined"){console.warn("Deprecation Warning: ",e)}else{t=t.split("\n").splice(3).join("\n");if(console.groupCollapsed){console.groupCollapsed("%cDeprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",e);console.warn(t);console.groupEnd()}else{console.warn("Deprecation Warning: ",e);console.warn(t)}}i[e]=true}function a(e){var t=e.mesh,r=e.particles,i=e.extras,a=e.filters,o=e.prepare,s=e.loaders,u=e.interaction;Object.defineProperties(e,{SpriteBatch:{get:function e(){throw new ReferenceError("SpriteBatch does not exist any more, "+"please use the new ParticleContainer instead.")}},AssetLoader:{get:function e(){throw new ReferenceError("The loader system was overhauled in PixiJS v3, "+"please see the new PIXI.loaders.Loader class.")}},Stage:{get:function t(){n("You do not need to use a PIXI Stage any more, you can simply render any container.");return e.Container}},DisplayObjectContainer:{get:function t(){n("DisplayObjectContainer has been shortened to Container, please use Container from now on.");return e.Container}},Strip:{get:function e(){n("The Strip class has been renamed to Mesh and moved to mesh.Mesh, please use mesh.Mesh from now on.");return t.Mesh}},Rope:{get:function e(){n("The Rope class has been moved to mesh.Rope, please use mesh.Rope from now on.");return t.Rope}},ParticleContainer:{get:function e(){n("The ParticleContainer class has been moved to particles.ParticleContainer, "+"please use particles.ParticleContainer from now on.");return r.ParticleContainer}},MovieClip:{get:function e(){n("The MovieClip class has been moved to extras.AnimatedSprite, please use extras.AnimatedSprite.");return i.AnimatedSprite}},TilingSprite:{get:function e(){n("The TilingSprite class has been moved to extras.TilingSprite, "+"please use extras.TilingSprite from now on.");return i.TilingSprite}},BitmapText:{get:function e(){n("The BitmapText class has been moved to extras.BitmapText, "+"please use extras.BitmapText from now on.");return i.BitmapText}},blendModes:{get:function t(){n("The blendModes has been moved to BLEND_MODES, please use BLEND_MODES from now on.");return e.BLEND_MODES}},scaleModes:{get:function t(){n("The scaleModes has been moved to SCALE_MODES, please use SCALE_MODES from now on.");return e.SCALE_MODES}},BaseTextureCache:{get:function t(){n("The BaseTextureCache class has been moved to utils.BaseTextureCache, "+"please use utils.BaseTextureCache from now on.");return e.utils.BaseTextureCache}},TextureCache:{get:function t(){n("The TextureCache class has been moved to utils.TextureCache, "+"please use utils.TextureCache from now on.");return e.utils.TextureCache}},math:{get:function t(){n("The math namespace is deprecated, please access members already accessible on PIXI.");return e}},AbstractFilter:{get:function t(){n("AstractFilter has been renamed to Filter, please use PIXI.Filter");return e.Filter}},TransformManual:{get:function t(){n("TransformManual has been renamed to TransformBase, please update your pixi-spine");return e.TransformBase}},TARGET_FPMS:{get:function t(){n("PIXI.TARGET_FPMS has been deprecated, please use PIXI.settings.TARGET_FPMS");return e.settings.TARGET_FPMS},set:function t(r){n("PIXI.TARGET_FPMS has been deprecated, please use PIXI.settings.TARGET_FPMS");e.settings.TARGET_FPMS=r}},FILTER_RESOLUTION:{get:function t(){n("PIXI.FILTER_RESOLUTION has been deprecated, please use PIXI.settings.FILTER_RESOLUTION");return e.settings.FILTER_RESOLUTION},set:function t(r){n("PIXI.FILTER_RESOLUTION has been deprecated, please use PIXI.settings.FILTER_RESOLUTION");e.settings.FILTER_RESOLUTION=r}},RESOLUTION:{get:function t(){n("PIXI.RESOLUTION has been deprecated, please use PIXI.settings.RESOLUTION");return e.settings.RESOLUTION},set:function t(r){n("PIXI.RESOLUTION has been deprecated, please use PIXI.settings.RESOLUTION");e.settings.RESOLUTION=r}},MIPMAP_TEXTURES:{get:function t(){n("PIXI.MIPMAP_TEXTURES has been deprecated, please use PIXI.settings.MIPMAP_TEXTURES");return e.settings.MIPMAP_TEXTURES},set:function t(r){n("PIXI.MIPMAP_TEXTURES has been deprecated, please use PIXI.settings.MIPMAP_TEXTURES");e.settings.MIPMAP_TEXTURES=r}},SPRITE_BATCH_SIZE:{get:function t(){n("PIXI.SPRITE_BATCH_SIZE has been deprecated, please use PIXI.settings.SPRITE_BATCH_SIZE");return e.settings.SPRITE_BATCH_SIZE},set:function t(r){n("PIXI.SPRITE_BATCH_SIZE has been deprecated, please use PIXI.settings.SPRITE_BATCH_SIZE");e.settings.SPRITE_BATCH_SIZE=r}},SPRITE_MAX_TEXTURES:{get:function t(){n("PIXI.SPRITE_MAX_TEXTURES has been deprecated, please use PIXI.settings.SPRITE_MAX_TEXTURES");return e.settings.SPRITE_MAX_TEXTURES},set:function t(r){n("PIXI.SPRITE_MAX_TEXTURES has been deprecated, please use PIXI.settings.SPRITE_MAX_TEXTURES");e.settings.SPRITE_MAX_TEXTURES=r}},RETINA_PREFIX:{get:function t(){n("PIXI.RETINA_PREFIX has been deprecated, please use PIXI.settings.RETINA_PREFIX");return e.settings.RETINA_PREFIX},set:function t(r){n("PIXI.RETINA_PREFIX has been deprecated, please use PIXI.settings.RETINA_PREFIX");e.settings.RETINA_PREFIX=r}},DEFAULT_RENDER_OPTIONS:{get:function t(){n("PIXI.DEFAULT_RENDER_OPTIONS has been deprecated, please use PIXI.settings.DEFAULT_RENDER_OPTIONS");return e.settings.RENDER_OPTIONS}}});var l=[{parent:"TRANSFORM_MODE",target:"TRANSFORM_MODE"},{parent:"GC_MODES",target:"GC_MODE"},{parent:"WRAP_MODES",target:"WRAP_MODE"},{parent:"SCALE_MODES",target:"SCALE_MODE"},{parent:"PRECISION",target:"PRECISION_FRAGMENT"}];var h=function t(r){var i=l[r];Object.defineProperty(e[i.parent],"DEFAULT",{get:function t(){n("PIXI."+i.parent+".DEFAULT has been deprecated, "+("please use PIXI.settings."+i.target));return e.settings[i.target]},set:function t(r){n("PIXI."+i.parent+".DEFAULT has been deprecated, "+("please use PIXI.settings."+i.target));e.settings[i.target]=r}})};for(var f=0;f<l.length;f++){h(f)}Object.defineProperties(e.settings,{PRECISION:{get:function t(){n("PIXI.settings.PRECISION has been deprecated, please use PIXI.settings.PRECISION_FRAGMENT");return e.settings.PRECISION_FRAGMENT},set:function t(r){n("PIXI.settings.PRECISION has been deprecated, please use PIXI.settings.PRECISION_FRAGMENT");e.settings.PRECISION_FRAGMENT=r}}});if(i.AnimatedSprite){Object.defineProperties(i,{MovieClip:{get:function e(){n("The MovieClip class has been renamed to AnimatedSprite, please use AnimatedSprite from now on.");return i.AnimatedSprite}}})}if(i){Object.defineProperties(i,{TextureTransform:{get:function t(){n("The TextureTransform class has been renamed to TextureMatrix, "+"please use PIXI.TextureMatrix from now on.");return e.TextureMatrix}}})}e.DisplayObject.prototype.generateTexture=function e(t,r,i){n("generateTexture has moved to the renderer, please use renderer.generateTexture(displayObject)");return t.generateTexture(this,r,i)};e.Graphics.prototype.generateTexture=function e(t,r){n("graphics generate texture has moved to the renderer. "+"Or to render a graphics to a texture using canvas please use generateCanvasTexture");return this.generateCanvasTexture(t,r)};e.GroupD8.isSwapWidthHeight=function t(r){n("GroupD8.isSwapWidthHeight was renamed to GroupD8.isVertical");return e.GroupD8.isVertical(r)};e.RenderTexture.prototype.render=function e(t,r,i,a){this.legacyRenderer.render(t,this,i,r,!a);n("RenderTexture.render is now deprecated, please use renderer.render(displayObject, renderTexture)")};e.RenderTexture.prototype.getImage=function e(t){n("RenderTexture.getImage is now deprecated, please use renderer.extract.image(target)");return this.legacyRenderer.extract.image(t)};e.RenderTexture.prototype.getBase64=function e(t){n("RenderTexture.getBase64 is now deprecated, please use renderer.extract.base64(target)");return this.legacyRenderer.extract.base64(t)};e.RenderTexture.prototype.getCanvas=function e(t){n("RenderTexture.getCanvas is now deprecated, please use renderer.extract.canvas(target)");return this.legacyRenderer.extract.canvas(t)};e.RenderTexture.prototype.getPixels=function e(t){n("RenderTexture.getPixels is now deprecated, please use renderer.extract.pixels(target)");return this.legacyRenderer.pixels(t)};e.Sprite.prototype.setTexture=function e(t){this.texture=t;n("setTexture is now deprecated, please use the texture property, e.g : sprite.texture = texture;")};if(i.BitmapText){i.BitmapText.prototype.setText=function e(t){this.text=t;n("setText is now deprecated, please use the text property, e.g : myBitmapText.text = 'my text';")}}e.Text.prototype.setText=function e(t){this.text=t;n("setText is now deprecated, please use the text property, e.g : myText.text = 'my text';")};e.Text.calculateFontProperties=function t(r){n("Text.calculateFontProperties is now deprecated, please use the TextMetrics.measureFont");return e.TextMetrics.measureFont(r)};Object.defineProperties(e.Text,{fontPropertiesCache:{get:function t(){n("Text.fontPropertiesCache is deprecated");return e.TextMetrics._fonts}},fontPropertiesCanvas:{get:function t(){n("Text.fontPropertiesCanvas is deprecated");return e.TextMetrics._canvas}},fontPropertiesContext:{get:function t(){n("Text.fontPropertiesContext is deprecated");return e.TextMetrics._context}}});e.Text.prototype.setStyle=function e(t){this.style=t;n("setStyle is now deprecated, please use the style property, e.g : myText.style = style;")};e.Text.prototype.determineFontProperties=function t(r){n("determineFontProperties is now deprecated, please use TextMetrics.measureFont method");return e.TextMetrics.measureFont(r)};e.Text.getFontStyle=function t(r){n("getFontStyle is now deprecated, please use TextStyle.toFontString() instead");r=r||{};if(!(r instanceof e.TextStyle)){r=new e.TextStyle(r)}return r.toFontString()};Object.defineProperties(e.TextStyle.prototype,{font:{get:function e(){n("text style property 'font' is now deprecated, please use the "+"'fontFamily', 'fontSize', 'fontStyle', 'fontVariant' and 'fontWeight' properties from now on");var t=typeof this._fontSize==="number"?this._fontSize+"px":this._fontSize;return this._fontStyle+" "+this._fontVariant+" "+this._fontWeight+" "+t+" "+this._fontFamily},set:function e(t){n("text style property 'font' is now deprecated, please use the "+"'fontFamily','fontSize',fontStyle','fontVariant' and 'fontWeight' properties from now on");if(t.indexOf("italic")>1){this._fontStyle="italic"}else if(t.indexOf("oblique")>-1){this._fontStyle="oblique"}else{this._fontStyle="normal"}if(t.indexOf("small-caps")>-1){this._fontVariant="small-caps"}else{this._fontVariant="normal"}var r=t.split(" ");var i=-1;this._fontSize=26;for(var a=0;a<r.length;++a){if(r[a].match(/(px|pt|em|%)/)){i=a;this._fontSize=r[a];break}}this._fontWeight="normal";for(var o=0;o<i;++o){if(r[o].match(/(bold|bolder|lighter|100|200|300|400|500|600|700|800|900)/)){this._fontWeight=r[o];break}}if(i>-1&&i<r.length-1){this._fontFamily="";for(var s=i+1;s<r.length;++s){this._fontFamily+=r[s]+" "}this._fontFamily=this._fontFamily.slice(0,-1)}else{this._fontFamily="Arial"}this.styleID++}}});e.Texture.prototype.setFrame=function e(t){this.frame=t;n("setFrame is now deprecated, please use the frame property, e.g: myTexture.frame = frame;")};e.Texture.addTextureToCache=function t(r,i){e.Texture.addToCache(r,i);n("Texture.addTextureToCache is deprecated, please use Texture.addToCache from now on.")};e.Texture.removeTextureFromCache=function t(r){n("Texture.removeTextureFromCache is deprecated, please use Texture.removeFromCache from now on. "+"Be aware that Texture.removeFromCache does not automatically its BaseTexture from the BaseTextureCache. "+"For that, use BaseTexture.removeFromCache");e.BaseTexture.removeFromCache(r);return e.Texture.removeFromCache(r)};Object.defineProperties(a,{AbstractFilter:{get:function t(){n("AstractFilter has been renamed to Filter, please use PIXI.Filter");return e.AbstractFilter}},SpriteMaskFilter:{get:function t(){n("filters.SpriteMaskFilter is an undocumented alias, please use SpriteMaskFilter from now on.");return e.SpriteMaskFilter}},VoidFilter:{get:function e(){n("VoidFilter has been renamed to AlphaFilter, please use PIXI.filters.AlphaFilter");return a.AlphaFilter}}});e.utils.uuid=function(){n("utils.uuid() is deprecated, please use utils.uid() from now on.");return e.utils.uid()};e.utils.canUseNewCanvasBlendModes=function(){n("utils.canUseNewCanvasBlendModes() is deprecated, please use CanvasTinter.canUseMultiply from now on");return e.CanvasTinter.canUseMultiply};var c=true;Object.defineProperty(e.utils,"_saidHello",{set:function e(t){if(t){n("PIXI.utils._saidHello is deprecated, please use PIXI.utils.skipHello()");this.skipHello()}c=t},get:function e(){return c}});if(o.BasePrepare){o.BasePrepare.prototype.register=function e(t,r){n("renderer.plugins.prepare.register is now deprecated, "+"please use renderer.plugins.prepare.registerFindHook & renderer.plugins.prepare.registerUploadHook");if(t){this.registerFindHook(t)}if(r){this.registerUploadHook(r)}return this}}if(o.canvas){Object.defineProperty(o.canvas,"UPLOADS_PER_FRAME",{set:function e(){n("PIXI.CanvasPrepare.UPLOADS_PER_FRAME has been removed. Please set "+"renderer.plugins.prepare.limiter.maxItemsPerFrame on your renderer")},get:function e(){n("PIXI.CanvasPrepare.UPLOADS_PER_FRAME has been removed. Please use "+"renderer.plugins.prepare.limiter");return NaN}})}if(o.webgl){Object.defineProperty(o.webgl,"UPLOADS_PER_FRAME",{set:function e(){n("PIXI.WebGLPrepare.UPLOADS_PER_FRAME has been removed. Please set "+"renderer.plugins.prepare.limiter.maxItemsPerFrame on your renderer")},get:function e(){n("PIXI.WebGLPrepare.UPLOADS_PER_FRAME has been removed. Please use "+"renderer.plugins.prepare.limiter");return NaN}})}if(s.Loader){var d=s.Resource;var p=s.Loader;Object.defineProperties(d.prototype,{isJson:{get:function e(){n("The isJson property is deprecated, please use `resource.type === Resource.TYPE.JSON`.");return this.type===d.TYPE.JSON}},isXml:{get:function e(){n("The isXml property is deprecated, please use `resource.type === Resource.TYPE.XML`.");return this.type===d.TYPE.XML}},isImage:{get:function e(){n("The isImage property is deprecated, please use `resource.type === Resource.TYPE.IMAGE`.");return this.type===d.TYPE.IMAGE}},isAudio:{get:function e(){n("The isAudio property is deprecated, please use `resource.type === Resource.TYPE.AUDIO`.");return this.type===d.TYPE.AUDIO}},isVideo:{get:function e(){n("The isVideo property is deprecated, please use `resource.type === Resource.TYPE.VIDEO`.");return this.type===d.TYPE.VIDEO}}});Object.defineProperties(p.prototype,{before:{get:function e(){n("The before() method is deprecated, please use pre().");return this.pre}},after:{get:function e(){n("The after() method is deprecated, please use use().");return this.use}}})}if(u.interactiveTarget){Object.defineProperty(u.interactiveTarget,"defaultCursor",{set:function e(t){n("Property defaultCursor has been replaced with 'cursor'. ");this.cursor=t},get:function e(){n("Property defaultCursor has been replaced with 'cursor'. ");return this.cursor}})}if(u.InteractionManager){Object.defineProperty(u.InteractionManager,"defaultCursorStyle",{set:function e(t){n("Property defaultCursorStyle has been replaced with 'cursorStyles.default'. ");this.cursorStyles.default=t},get:function e(){n("Property defaultCursorStyle has been replaced with 'cursorStyles.default'. ");return this.cursorStyles.default}});Object.defineProperty(u.InteractionManager,"currentCursorStyle",{set:function e(t){n("Property currentCursorStyle has been removed."+"See the currentCursorMode property, which works differently.");this.currentCursorMode=t},get:function e(){n("Property currentCursorStyle has been removed."+"See the currentCursorMode property, which works differently.");return this.currentCursorMode}})}}},"99e3":function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=u(i);var a=r("40d5");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var h=n.ticker.shared;n.settings.UPLOADS_PER_FRAME=4;var f=function(){function e(t){var r=this;l(this,e);this.limiter=new o.default(n.settings.UPLOADS_PER_FRAME);this.renderer=t;this.uploadHookHelper=null;this.queue=[];this.addHooks=[];this.uploadHooks=[];this.completes=[];this.ticking=false;this.delayedTick=function(){if(!r.queue){return}r.prepareItems()};this.registerFindHook(g);this.registerFindHook(_);this.registerFindHook(c);this.registerFindHook(d);this.registerFindHook(p);this.registerUploadHook(v);this.registerUploadHook(y)}e.prototype.upload=function e(t,r){if(typeof t==="function"){r=t;t=null}if(t){this.add(t)}if(this.queue.length){if(r){this.completes.push(r)}if(!this.ticking){this.ticking=true;h.addOnce(this.tick,this,n.UPDATE_PRIORITY.UTILITY)}}else if(r){r()}};e.prototype.tick=function e(){setTimeout(this.delayedTick,0)};e.prototype.prepareItems=function e(){this.limiter.beginFrame();while(this.queue.length&&this.limiter.allowedToUpload()){var t=this.queue[0];var r=false;if(t&&!t._destroyed){for(var i=0,a=this.uploadHooks.length;i<a;i++){if(this.uploadHooks[i](this.uploadHookHelper,t)){this.queue.shift();r=true;break}}}if(!r){this.queue.shift()}}if(!this.queue.length){this.ticking=false;var o=this.completes.slice(0);this.completes.length=0;for(var s=0,u=o.length;s<u;s++){o[s]()}}else{h.addOnce(this.tick,this,n.UPDATE_PRIORITY.UTILITY)}};e.prototype.registerFindHook=function e(t){if(t){this.addHooks.push(t)}return this};e.prototype.registerUploadHook=function e(t){if(t){this.uploadHooks.push(t)}return this};e.prototype.add=function e(t){for(var r=0,i=this.addHooks.length;r<i;r++){if(this.addHooks[r](t,this.queue)){break}}if(t instanceof n.Container){for(var a=t.children.length-1;a>=0;a--){this.add(t.children[a])}}return this};e.prototype.destroy=function e(){if(this.ticking){h.remove(this.tick,this)}this.ticking=false;this.addHooks=null;this.uploadHooks=null;this.renderer=null;this.completes=null;this.queue=null;this.limiter=null;this.uploadHookHelper=null};return e}();t.default=f;function c(e,t){var r=false;if(e&&e._textures&&e._textures.length){for(var i=0;i<e._textures.length;i++){if(e._textures[i]instanceof n.Texture){var a=e._textures[i].baseTexture;if(t.indexOf(a)===-1){t.push(a);r=true}}}}return r}function d(e,t){if(e instanceof n.BaseTexture){if(t.indexOf(e)===-1){t.push(e)}return true}return false}function p(e,t){if(e._texture&&e._texture instanceof n.Texture){var r=e._texture.baseTexture;if(t.indexOf(r)===-1){t.push(r)}return true}return false}function v(e,t){if(t instanceof n.Text){t.updateText(true);return true}return false}function y(e,t){if(t instanceof n.TextStyle){var r=t.toFontString();n.TextMetrics.measureFont(r);return true}return false}function g(e,t){if(e instanceof n.Text){if(t.indexOf(e.style)===-1){t.push(e.style)}if(t.indexOf(e)===-1){t.push(e)}var r=e._texture.baseTexture;if(t.indexOf(r)===-1){t.push(r)}return true}return false}function _(e,t){if(e instanceof n.TextStyle){if(t.indexOf(e)===-1){t.push(e)}return true}return false}},"9acd":function(e,t,r){"use strict";t.__esModule=true;function i(e){e.__plugins={};e.registerPlugin=function t(r,i){e.__plugins[r]=i};e.prototype.initPlugins=function t(){this.plugins=this.plugins||{};for(var r in e.__plugins){this.plugins[r]=new e.__plugins[r](this)}};e.prototype.destroyPlugins=function e(){for(var t in this.plugins){this.plugins[t].destroy();this.plugins[t]=null}this.plugins=null}}t.default={mixin:function e(t){i(t)}}},"9d62":function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=s(n);var o=r("aa9d");function s(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=function(e){h(t,e);function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1500;var i=arguments[1];var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:16384;var o=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;u(this,t);var s=l(this,e.call(this));var h=16384;if(n>h){n=h}if(n>r){n=r}s._properties=[false,true,false,false,false];s._maxSize=r;s._batchSize=n;s._glBuffers={};s._bufferUpdateIDs=[];s._updateID=0;s.interactiveChildren=false;s.blendMode=a.BLEND_MODES.NORMAL;s.autoResize=o;s.roundPixels=true;s.baseTexture=null;s.setProperties(i);s._tint=0;s.tintRgb=new Float32Array(4);s.tint=16777215;return s}t.prototype.setProperties=function e(t){if(t){this._properties[0]="vertices"in t||"scale"in t?!!t.vertices||!!t.scale:this._properties[0];this._properties[1]="position"in t?!!t.position:this._properties[1];this._properties[2]="rotation"in t?!!t.rotation:this._properties[2];this._properties[3]="uvs"in t?!!t.uvs:this._properties[3];this._properties[4]="tint"in t||"alpha"in t?!!t.tint||!!t.alpha:this._properties[4]}};t.prototype.updateTransform=function e(){this.displayObjectUpdateTransform()};t.prototype.renderWebGL=function e(t){var r=this;if(!this.visible||this.worldAlpha<=0||!this.children.length||!this.renderable){return}if(!this.baseTexture){this.baseTexture=this.children[0]._texture.baseTexture;if(!this.baseTexture.hasLoaded){this.baseTexture.once("update",function(){return r.onChildrenChange(0)})}}t.setObjectRenderer(t.plugins.particle);t.plugins.particle.render(this)};t.prototype.onChildrenChange=function e(t){var r=Math.floor(t/this._batchSize);while(this._bufferUpdateIDs.length<r){this._bufferUpdateIDs.push(0)}this._bufferUpdateIDs[r]=++this._updateID};t.prototype.renderCanvas=function e(t){if(!this.visible||this.worldAlpha<=0||!this.children.length||!this.renderable){return}var r=t.context;var i=this.worldTransform;var n=true;var a=0;var o=0;var s=0;var u=0;t.setBlendMode(this.blendMode);r.globalAlpha=this.worldAlpha;this.displayObjectUpdateTransform();for(var l=0;l<this.children.length;++l){var h=this.children[l];if(!h.visible){continue}var f=h._texture.frame;r.globalAlpha=this.worldAlpha*h.alpha;if(h.rotation%(Math.PI*2)===0){if(n){r.setTransform(i.a,i.b,i.c,i.d,i.tx*t.resolution,i.ty*t.resolution);n=false}a=h.anchor.x*(-f.width*h.scale.x)+h.position.x+.5;o=h.anchor.y*(-f.height*h.scale.y)+h.position.y+.5;s=f.width*h.scale.x;u=f.height*h.scale.y}else{if(!n){n=true}h.displayObjectUpdateTransform();var c=h.worldTransform;if(t.roundPixels){r.setTransform(c.a,c.b,c.c,c.d,c.tx*t.resolution|0,c.ty*t.resolution|0)}else{r.setTransform(c.a,c.b,c.c,c.d,c.tx*t.resolution,c.ty*t.resolution)}a=h.anchor.x*-f.width+.5;o=h.anchor.y*-f.height+.5;s=f.width;u=f.height}var d=h._texture.baseTexture.resolution;r.drawImage(h._texture.baseTexture.source,f.x*d,f.y*d,f.width*d,f.height*d,a*t.resolution,o*t.resolution,s*t.resolution,u*t.resolution)}};t.prototype.destroy=function t(r){e.prototype.destroy.call(this,r);if(this._buffers){for(var i=0;i<this._buffers.length;++i){this._buffers[i].destroy()}}this._properties=null;this._buffers=null;this._bufferUpdateIDs=null};i(t,[{key:"tint",get:function e(){return this._tint},set:function e(t){this._tint=t;(0,o.hex2rgb)(t,this.tintRgb)}}]);return t}(a.Container);t.default=f},"9ddd":function(e,t,r){var i=r("2545");function n(e,t){this.nativeVaoExtension=null;if(!n.FORCE_NATIVE){this.nativeVaoExtension=e.getExtension("OES_vertex_array_object")||e.getExtension("MOZ_OES_vertex_array_object")||e.getExtension("WEBKIT_OES_vertex_array_object")}this.nativeState=t;if(this.nativeVaoExtension){this.nativeVao=this.nativeVaoExtension.createVertexArrayOES();var r=e.getParameter(e.MAX_VERTEX_ATTRIBS);this.nativeState={tempAttribState:new Array(r),attribState:new Array(r)}}this.gl=e;this.attributes=[];this.indexBuffer=null;this.dirty=false}n.prototype.constructor=n;e.exports=n;n.FORCE_NATIVE=false;n.prototype.bind=function(){if(this.nativeVao){this.nativeVaoExtension.bindVertexArrayOES(this.nativeVao);if(this.dirty){this.dirty=false;this.activate();return this}if(this.indexBuffer){this.indexBuffer.bind()}}else{this.activate()}return this};n.prototype.unbind=function(){if(this.nativeVao){this.nativeVaoExtension.bindVertexArrayOES(null)}return this};n.prototype.activate=function(){var e=this.gl;var t=null;for(var r=0;r<this.attributes.length;r++){var n=this.attributes[r];if(t!==n.buffer){n.buffer.bind();t=n.buffer}e.vertexAttribPointer(n.attribute.location,n.attribute.size,n.type||e.FLOAT,n.normalized||false,n.stride||0,n.start||0)}i(e,this.attributes,this.nativeState);if(this.indexBuffer){this.indexBuffer.bind()}return this};n.prototype.addAttribute=function(e,t,r,i,n,a){this.attributes.push({buffer:e,attribute:t,location:t.location,type:r||this.gl.FLOAT,normalized:i||false,stride:n||0,start:a||0});this.dirty=true;return this};n.prototype.addIndex=function(e){this.indexBuffer=e;this.dirty=true;return this};n.prototype.clear=function(){if(this.nativeVao){this.nativeVaoExtension.bindVertexArrayOES(this.nativeVao)}this.attributes.length=0;this.indexBuffer=null;return this};n.prototype.draw=function(e,t,r){var i=this.gl;if(this.indexBuffer){i.drawElements(e,t||this.indexBuffer.data.length,i.UNSIGNED_SHORT,(r||0)*2)}else{i.drawArrays(e,r,t||this.getSize())}return this};n.prototype.destroy=function(){this.gl=null;this.indexBuffer=null;this.attributes=null;this.nativeState=null;if(this.nativeVao){this.nativeVaoExtension.deleteVertexArrayOES(this.nativeVao)}this.nativeVaoExtension=null;this.nativeVao=null};n.prototype.getSize=function(){var e=this.attributes[0];return e.buffer.data.length/(e.stride/4||e.attribute.size)}},a172:function(e,t,r){"use strict";t.__esModule=true;t.parse=u;t.default=function(){return function e(t,r){if(!t.data||t.type!==a.Resource.TYPE.XML){r();return}if(t.data.getElementsByTagName("page").length===0||t.data.getElementsByTagName("info").length===0||t.data.getElementsByTagName("info")[0].getAttribute("face")===null){r();return}var i=!t.isDataUrl?n.dirname(t.url):"";if(t.isDataUrl){if(i==="."){i=""}if(this.baseUrl&&i){if(this.baseUrl.charAt(this.baseUrl.length-1)==="/"){i+="/"}}}i=i.replace(this.baseUrl,"");if(i&&i.charAt(i.length-1)!=="/"){i+="/"}var o=t.data.getElementsByTagName("page");var s={};var l=function e(i){s[i.metadata.pageFile]=i.texture;if(Object.keys(s).length===o.length){u(t,s);r()}};for(var h=0;h<o.length;++h){var f=o[h].getAttribute("file");var c=i+f;var d=false;for(var p in this.resources){var v=this.resources[p];if(v.url===c){v.metadata.pageFile=f;if(v.texture){l(v)}else{v.onAfterMiddleware.add(l)}d=true;break}}if(!d){var y={crossOrigin:t.crossOrigin,loadType:a.Resource.LOAD_TYPE.IMAGE,metadata:Object.assign({pageFile:f},t.metadata.imageMetadata),parentResource:t};this.add(c,y,l)}}}};var i=r("df7c");var n=s(i);var a=r("cfe7");var o=r("635d");function s(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function u(e,t){e.bitmapFont=o.BitmapText.registerFont(e.data,t)}},a433:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("61df");var a=o(n);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(t,r,i){s(this,e);this.canvas=document.createElement("canvas");this.context=this.canvas.getContext("2d");this.resolution=i||a.default.RESOLUTION;this.resize(t,r)}e.prototype.clear=function e(){this.context.setTransform(1,0,0,1,0,0);this.context.clearRect(0,0,this.canvas.width,this.canvas.height)};e.prototype.resize=function e(t,r){this.canvas.width=t*this.resolution;this.canvas.height=r*this.resolution};e.prototype.destroy=function e(){this.context=null;this.canvas=null};i(e,[{key:"width",get:function e(){return this.canvas.width},set:function e(t){this.canvas.width=t}},{key:"height",get:function e(){return this.canvas.height},set:function e(t){this.canvas.height=t}}]);return e}();t.default=u},a446:function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=s(i);var a=r("a506");var o=r("df7c");function s(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function l(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var f=new n.Matrix;var c=function(e){h(t,e);function t(r){u(this,t);var i=l(this,e.call(this,r));i.shader=null;i.simpleShader=null;i.quad=null;return i}t.prototype.onContextChange=function e(){var t=this.renderer.gl;this.shader=new n.Shader(t,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTransform;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;\n}\n","varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\nuniform mat3 uMapCoord;\nuniform vec4 uClampFrame;\nuniform vec2 uClampOffset;\n\nvoid main(void)\n{\n    vec2 coord = mod(vTextureCoord - uClampOffset, vec2(1.0, 1.0)) + uClampOffset;\n    coord = (uMapCoord * vec3(coord, 1.0)).xy;\n    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);\n\n    vec4 sample = texture2D(uSampler, coord);\n    gl_FragColor = sample * uColor;\n}\n");this.simpleShader=new n.Shader(t,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTransform;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;\n}\n","varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\nuniform vec4 uColor;\n\nvoid main(void)\n{\n    vec4 sample = texture2D(uSampler, vTextureCoord);\n    gl_FragColor = sample * uColor;\n}\n");this.renderer.bindVao(null);this.quad=new n.Quad(t,this.renderer.state.attribState);this.quad.initVao(this.shader)};t.prototype.render=function e(t){var r=this.renderer;var i=this.quad;r.bindVao(i.vao);var o=i.vertices;o[0]=o[6]=t._width*-t.anchor.x;o[1]=o[3]=t._height*-t.anchor.y;o[2]=o[4]=t._width*(1-t.anchor.x);o[5]=o[7]=t._height*(1-t.anchor.y);if(t.uvRespectAnchor){o=i.uvs;o[0]=o[6]=-t.anchor.x;o[1]=o[3]=-t.anchor.y;o[2]=o[4]=1-t.anchor.x;o[5]=o[7]=1-t.anchor.y}i.upload();var s=t._texture;var u=s.baseTexture;var l=t.tileTransform.localTransform;var h=t.uvTransform;var c=u.isPowerOfTwo&&s.frame.width===u.width&&s.frame.height===u.height;if(c){if(!u._glTextures[r.CONTEXT_UID]){if(u.wrapMode===a.WRAP_MODES.CLAMP){u.wrapMode=a.WRAP_MODES.REPEAT}}else{c=u.wrapMode!==a.WRAP_MODES.CLAMP}}var d=c?this.simpleShader:this.shader;r.bindShader(d);var p=s.width;var v=s.height;var y=t._width;var g=t._height;f.set(l.a*p/y,l.b*p/g,l.c*v/y,l.d*v/g,l.tx/y,l.ty/g);f.invert();if(c){f.prepend(h.mapCoord)}else{d.uniforms.uMapCoord=h.mapCoord.toArray(true);d.uniforms.uClampFrame=h.uClampFrame;d.uniforms.uClampOffset=h.uClampOffset}d.uniforms.uTransform=f.toArray(true);d.uniforms.uColor=n.utils.premultiplyTintToRgba(t.tint,t.worldAlpha,d.uniforms.uColor,u.premultipliedAlpha);d.uniforms.translationMatrix=t.transform.worldTransform.toArray(true);d.uniforms.uSampler=r.bindTexture(s);r.setBlendMode(n.utils.correctBlendMode(t.blendMode,u.premultipliedAlpha));i.vao.draw(this.renderer.gl.TRIANGLES,6,0)};return t}(n.ObjectRenderer);t.default=c;n.WebGLRenderer.registerPlugin("tilingSprite",c)},a48a:function(e,t,r){"use strict";"use restrict";var i=32;t.INT_BITS=i;t.INT_MAX=2147483647;t.INT_MIN=-1<<i-1;t.sign=function(e){return(e>0)-(e<0)};t.abs=function(e){var t=e>>i-1;return(e^t)-t};t.min=function(e,t){return t^(e^t)&-(e<t)};t.max=function(e,t){return e^(e^t)&-(e<t)};t.isPow2=function(e){return!(e&e-1)&&!!e};t.log2=function(e){var t,r;t=(e>65535)<<4;e>>>=t;r=(e>255)<<3;e>>>=r;t|=r;r=(e>15)<<2;e>>>=r;t|=r;r=(e>3)<<1;e>>>=r;t|=r;return t|e>>1};t.log10=function(e){return e>=1e9?9:e>=1e8?8:e>=1e7?7:e>=1e6?6:e>=1e5?5:e>=1e4?4:e>=1e3?3:e>=100?2:e>=10?1:0};t.popCount=function(e){e=e-(e>>>1&1431655765);e=(e&858993459)+(e>>>2&858993459);return(e+(e>>>4)&252645135)*16843009>>>24};function n(e){var t=32;e&=-e;if(e)t--;if(e&65535)t-=16;if(e&16711935)t-=8;if(e&252645135)t-=4;if(e&858993459)t-=2;if(e&1431655765)t-=1;return t}t.countTrailingZeros=n;t.nextPow2=function(e){e+=e===0;--e;e|=e>>>1;e|=e>>>2;e|=e>>>4;e|=e>>>8;e|=e>>>16;return e+1};t.prevPow2=function(e){e|=e>>>1;e|=e>>>2;e|=e>>>4;e|=e>>>8;e|=e>>>16;return e-(e>>>1)};t.parity=function(e){e^=e>>>16;e^=e>>>8;e^=e>>>4;e&=15;return 27030>>>e&1};var a=new Array(256);(function(e){for(var t=0;t<256;++t){var r=t,i=t,n=7;for(r>>>=1;r;r>>>=1){i<<=1;i|=r&1;--n}e[t]=i<<n&255}})(a);t.reverse=function(e){return a[e&255]<<24|a[e>>>8&255]<<16|a[e>>>16&255]<<8|a[e>>>24&255]};t.interleave2=function(e,t){e&=65535;e=(e|e<<8)&16711935;e=(e|e<<4)&252645135;e=(e|e<<2)&858993459;e=(e|e<<1)&1431655765;t&=65535;t=(t|t<<8)&16711935;t=(t|t<<4)&252645135;t=(t|t<<2)&858993459;t=(t|t<<1)&1431655765;return e|t<<1};t.deinterleave2=function(e,t){e=e>>>t&1431655765;e=(e|e>>>1)&858993459;e=(e|e>>>2)&252645135;e=(e|e>>>4)&16711935;e=(e|e>>>16)&65535;return e<<16>>16};t.interleave3=function(e,t,r){e&=1023;e=(e|e<<16)&4278190335;e=(e|e<<8)&251719695;e=(e|e<<4)&3272356035;e=(e|e<<2)&1227133513;t&=1023;t=(t|t<<16)&4278190335;t=(t|t<<8)&251719695;t=(t|t<<4)&3272356035;t=(t|t<<2)&1227133513;e|=t<<1;r&=1023;r=(r|r<<16)&4278190335;r=(r|r<<8)&251719695;r=(r|r<<4)&3272356035;r=(r|r<<2)&1227133513;return e|r<<2};t.deinterleave3=function(e,t){e=e>>>t&1227133513;e=(e|e>>>2)&3272356035;e=(e|e>>>4)&251719695;e=(e|e>>>8)&4278190335;e=(e|e>>>16)&1023;return e<<22>>22};t.nextCombination=function(e){var t=e|e-1;return t+1|(~t&-~t)-1>>>n(e)+1}},a506:function(e,t,r){"use strict";t.__esModule=true;var i=t.VERSION="4.8.6";var n=t.PI_2=Math.PI*2;var a=t.RAD_TO_DEG=180/Math.PI;var o=t.DEG_TO_RAD=Math.PI/180;var s=t.RENDERER_TYPE={UNKNOWN:0,WEBGL:1,CANVAS:2};var u=t.BLEND_MODES={NORMAL:0,ADD:1,MULTIPLY:2,SCREEN:3,OVERLAY:4,DARKEN:5,LIGHTEN:6,COLOR_DODGE:7,COLOR_BURN:8,HARD_LIGHT:9,SOFT_LIGHT:10,DIFFERENCE:11,EXCLUSION:12,HUE:13,SATURATION:14,COLOR:15,LUMINOSITY:16,NORMAL_NPM:17,ADD_NPM:18,SCREEN_NPM:19};var l=t.DRAW_MODES={POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6};var h=t.SCALE_MODES={LINEAR:0,NEAREST:1};var f=t.WRAP_MODES={CLAMP:0,REPEAT:1,MIRRORED_REPEAT:2};var c=t.GC_MODES={AUTO:0,MANUAL:1};var d=t.URL_FILE_EXTENSION=/\.(\w{3,4})(?:$|\?|#)/i;var p=t.DATA_URI=/^\s*data:(?:([\w-]+)\/([\w+.-]+))?(?:;charset=([\w-]+))?(?:;(base64))?,(.*)/i;var v=t.SVG_SIZE=/<svg[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*>/i;var y=t.SHAPES={POLY:0,RECT:1,CIRC:2,ELIP:3,RREC:4};var g=t.PRECISION={LOW:"lowp",MEDIUM:"mediump",HIGH:"highp"};var _=t.TRANSFORM_MODE={STATIC:0,DYNAMIC:1};var m=t.TEXT_GRADIENT={LINEAR_VERTICAL:0,LINEAR_HORIZONTAL:1};var b=t.UPDATE_PRIORITY={INTERACTION:50,HIGH:25,NORMAL:0,LOW:-25,UTILITY:-50}},a557:function(e,t,r){"use strict";t.__esModule=true;var i=r("148f");var n=v(i);var a=r("72f6");var o=v(a);var s=r("a433");var u=v(s);var l=r("84df");var h=v(l);var f=r("aa9d");var c=r("a506");var d=r("61df");var p=v(d);function v(e){return e&&e.__esModule?e:{default:e}}function y(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function g(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function _(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var m=function(e){_(t,e);function t(r,i,n){y(this,t);var a=g(this,e.call(this,"Canvas",r,i,n));a.type=c.RENDERER_TYPE.CANVAS;a.rootContext=a.view.getContext("2d",{alpha:a.transparent});a.context=a.rootContext;a.refresh=true;a.maskManager=new o.default(a);a.smoothProperty="imageSmoothingEnabled";if(!a.rootContext.imageSmoothingEnabled){if(a.rootContext.webkitImageSmoothingEnabled){a.smoothProperty="webkitImageSmoothingEnabled"}else if(a.rootContext.mozImageSmoothingEnabled){a.smoothProperty="mozImageSmoothingEnabled"}else if(a.rootContext.oImageSmoothingEnabled){a.smoothProperty="oImageSmoothingEnabled"}else if(a.rootContext.msImageSmoothingEnabled){a.smoothProperty="msImageSmoothingEnabled"}}a.initPlugins();a.blendModes=(0,h.default)();a._activeBlendMode=null;a.renderingToScreen=false;a.resize(a.options.width,a.options.height);return a}t.prototype.render=function e(t,r,i,n,a){if(!this.view){return}this.renderingToScreen=!r;this.emit("prerender");var o=this.resolution;if(r){r=r.baseTexture||r;if(!r._canvasRenderTarget){r._canvasRenderTarget=new u.default(r.width,r.height,r.resolution);r.source=r._canvasRenderTarget.canvas;r.valid=true}this.context=r._canvasRenderTarget.context;this.resolution=r._canvasRenderTarget.resolution}else{this.context=this.rootContext}var s=this.context;if(!r){this._lastObjectRendered=t}if(!a){var l=t.parent;var h=this._tempDisplayObjectParent.transform.worldTransform;if(n){n.copy(h);this._tempDisplayObjectParent.transform._worldID=-1}else{h.identity()}t.parent=this._tempDisplayObjectParent;t.updateTransform();t.parent=l}s.save();s.setTransform(1,0,0,1,0,0);s.globalAlpha=1;this._activeBlendMode=c.BLEND_MODES.NORMAL;s.globalCompositeOperation=this.blendModes[c.BLEND_MODES.NORMAL];if(navigator.isCocoonJS&&this.view.screencanvas){s.fillStyle="black";s.clear()}if(i!==undefined?i:this.clearBeforeRender){if(this.renderingToScreen){if(this.transparent){s.clearRect(0,0,this.width,this.height)}else{s.fillStyle=this._backgroundColorString;s.fillRect(0,0,this.width,this.height)}}}var f=this.context;this.context=s;t.renderCanvas(this);this.context=f;s.restore();this.resolution=o;this.emit("postrender")};t.prototype.clear=function e(t){var r=this.context;t=t||this._backgroundColorString;if(!this.transparent&&t){r.fillStyle=t;r.fillRect(0,0,this.width,this.height)}else{r.clearRect(0,0,this.width,this.height)}};t.prototype.setBlendMode=function e(t){if(this._activeBlendMode===t){return}this._activeBlendMode=t;this.context.globalCompositeOperation=this.blendModes[t]};t.prototype.destroy=function t(r){this.destroyPlugins();e.prototype.destroy.call(this,r);this.context=null;this.refresh=true;this.maskManager.destroy();this.maskManager=null;this.smoothProperty=null};t.prototype.resize=function t(r,i){e.prototype.resize.call(this,r,i);if(this.smoothProperty){this.rootContext[this.smoothProperty]=p.default.SCALE_MODE===c.SCALE_MODES.LINEAR}};t.prototype.invalidateBlendMode=function e(){this._activeBlendMode=this.blendModes.indexOf(this.context.globalCompositeOperation)};return t}(n.default);t.default=m;f.pluginTarget.mixin(m)},a55e:function(e,t,r){"use strict";t.__esModule=true;var i=r("148f");var n=R(i);var a=r("344f");var o=R(a);var s=r("f4f6");var u=R(s);var l=r("0fe2");var h=R(l);var f=r("f16a");var c=R(f);var d=r("e056");var p=R(d);var v=r("073b");var y=R(v);var g=r("dc99");var _=R(g);var m=r("36fe");var b=R(m);var x=r("5401");var T=R(x);var E=r("7b0f");var w=R(E);var S=r("f48e");var O=R(S);var M=r("aa9d");var P=r("c88f");var C=R(P);var A=r("a506");function R(e){return e&&e.__esModule?e:{default:e}}function I(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function D(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function L(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var N=0;var k=function(e){L(t,e);function t(r,i,n){I(this,t);var a=D(this,e.call(this,"WebGL",r,i,n));a.legacy=a.options.legacy;if(a.legacy){C.default.VertexArrayObject.FORCE_NATIVE=true}a.type=A.RENDERER_TYPE.WEBGL;a.handleContextLost=a.handleContextLost.bind(a);a.handleContextRestored=a.handleContextRestored.bind(a);a.view.addEventListener("webglcontextlost",a.handleContextLost,false);a.view.addEventListener("webglcontextrestored",a.handleContextRestored,false);a._contextOptions={alpha:a.transparent,antialias:a.options.antialias,premultipliedAlpha:a.transparent&&a.transparent!=="notMultiplied",stencil:true,preserveDrawingBuffer:a.options.preserveDrawingBuffer,powerPreference:a.options.powerPreference};a._backgroundColorRgba[3]=a.transparent?0:1;a.maskManager=new o.default(a);a.stencilManager=new u.default(a);a.emptyRenderer=new p.default(a);a.currentRenderer=a.emptyRenderer;a.textureManager=null;a.filterManager=null;a.initPlugins();if(a.options.context){(0,O.default)(a.options.context)}a.gl=a.options.context||C.default.createContext(a.view,a._contextOptions);a.CONTEXT_UID=N++;a.state=new T.default(a.gl);a.renderingToScreen=true;a.boundTextures=null;a._activeShader=null;a._activeVao=null;a._activeRenderTarget=null;a._initContext();a.drawModes=(0,w.default)(a.gl);a._nextTextureLocation=0;a.setBlendMode(0);return a}t.prototype._initContext=function e(){var t=this.gl;if(t.isContextLost()&&t.getExtension("WEBGL_lose_context")){t.getExtension("WEBGL_lose_context").restoreContext()}var r=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS);this._activeShader=null;this._activeVao=null;this.boundTextures=new Array(r);this.emptyTextures=new Array(r);this._unknownBoundTextures=false;this.textureManager=new y.default(this);this.filterManager=new h.default(this);this.textureGC=new b.default(this);this.state.resetToDefault();this.rootRenderTarget=new c.default(t,this.width,this.height,null,this.resolution,true);this.rootRenderTarget.clearColor=this._backgroundColorRgba;this.bindRenderTarget(this.rootRenderTarget);var i=new C.default.GLTexture.fromData(t,null,1,1);var n={_glTextures:{}};n._glTextures[this.CONTEXT_UID]={};for(var a=0;a<r;a++){var o=new _.default;o._glTextures[this.CONTEXT_UID]=i;this.boundTextures[a]=n;this.emptyTextures[a]=o;this.bindTexture(null,a)}this.emit("context",t);this.resize(this.screen.width,this.screen.height)};t.prototype.render=function e(t,r,i,n,a){this.renderingToScreen=!r;this.emit("prerender");if(!this.gl||this.gl.isContextLost()){return}this._nextTextureLocation=0;if(!r){this._lastObjectRendered=t}if(!a){var o=t.parent;t.parent=this._tempDisplayObjectParent;t.updateTransform();t.parent=o}this.bindRenderTexture(r,n);this.currentRenderer.start();if(i!==undefined?i:this.clearBeforeRender){this._activeRenderTarget.clear()}t.renderWebGL(this);this.currentRenderer.flush();this.textureGC.update();this.emit("postrender")};t.prototype.setObjectRenderer=function e(t){if(this.currentRenderer===t){return}this.currentRenderer.stop();this.currentRenderer=t;this.currentRenderer.start()};t.prototype.flush=function e(){this.setObjectRenderer(this.emptyRenderer)};t.prototype.resize=function e(t,r){n.default.prototype.resize.call(this,t,r);this.rootRenderTarget.resize(t,r);if(this._activeRenderTarget===this.rootRenderTarget){this.rootRenderTarget.activate();if(this._activeShader){this._activeShader.uniforms.projectionMatrix=this.rootRenderTarget.projectionMatrix.toArray(true)}}};t.prototype.setBlendMode=function e(t){this.state.setBlendMode(t)};t.prototype.clear=function e(t){this._activeRenderTarget.clear(t)};t.prototype.setTransform=function e(t){this._activeRenderTarget.transform=t};t.prototype.clearRenderTexture=function e(t,r){var i=t.baseTexture;var n=i._glRenderTargets[this.CONTEXT_UID];if(n){n.clear(r)}return this};t.prototype.bindRenderTexture=function e(t,r){var i=void 0;if(t){var n=t.baseTexture;if(!n._glRenderTargets[this.CONTEXT_UID]){this.textureManager.updateTexture(n,0)}this.unbindTexture(n);i=n._glRenderTargets[this.CONTEXT_UID];i.setFrame(t.frame)}else{i=this.rootRenderTarget}i.transform=r;this.bindRenderTarget(i);return this};t.prototype.bindRenderTarget=function e(t){if(t!==this._activeRenderTarget){this._activeRenderTarget=t;t.activate();if(this._activeShader){this._activeShader.uniforms.projectionMatrix=t.projectionMatrix.toArray(true)}this.stencilManager.setMaskStack(t.stencilMaskStack)}return this};t.prototype.bindShader=function e(t,r){if(this._activeShader!==t){this._activeShader=t;t.bind();if(r!==false){t.uniforms.projectionMatrix=this._activeRenderTarget.projectionMatrix.toArray(true)}}return this};t.prototype.bindTexture=function e(t,r,i){t=t||this.emptyTextures[r];t=t.baseTexture||t;t.touched=this.textureGC.count;if(!i){for(var n=0;n<this.boundTextures.length;n++){if(this.boundTextures[n]===t){return n}}if(r===undefined){this._nextTextureLocation++;this._nextTextureLocation%=this.boundTextures.length;r=this.boundTextures.length-this._nextTextureLocation-1}}else{r=r||0}var a=this.gl;var o=t._glTextures[this.CONTEXT_UID];if(!o){this.textureManager.updateTexture(t,r)}else{this.boundTextures[r]=t;a.activeTexture(a.TEXTURE0+r);a.bindTexture(a.TEXTURE_2D,o.texture)}return r};t.prototype.unbindTexture=function e(t){var r=this.gl;t=t.baseTexture||t;if(this._unknownBoundTextures){this._unknownBoundTextures=false;for(var i=0;i<this.boundTextures.length;i++){if(this.boundTextures[i]===this.emptyTextures[i]){r.activeTexture(r.TEXTURE0+i);r.bindTexture(r.TEXTURE_2D,this.emptyTextures[i]._glTextures[this.CONTEXT_UID].texture)}}}for(var n=0;n<this.boundTextures.length;n++){if(this.boundTextures[n]===t){this.boundTextures[n]=this.emptyTextures[n];r.activeTexture(r.TEXTURE0+n);r.bindTexture(r.TEXTURE_2D,this.emptyTextures[n]._glTextures[this.CONTEXT_UID].texture)}}return this};t.prototype.createVao=function e(){return new C.default.VertexArrayObject(this.gl,this.state.attribState)};t.prototype.bindVao=function e(t){if(this._activeVao===t){return this}if(t){t.bind()}else if(this._activeVao){this._activeVao.unbind()}this._activeVao=t;return this};t.prototype.reset=function e(){this.setObjectRenderer(this.emptyRenderer);this.bindVao(null);this._activeShader=null;this._activeRenderTarget=this.rootRenderTarget;this._unknownBoundTextures=true;for(var t=0;t<this.boundTextures.length;t++){this.boundTextures[t]=this.emptyTextures[t]}this.rootRenderTarget.activate();this.state.resetToDefault();return this};t.prototype.handleContextLost=function e(t){t.preventDefault()};t.prototype.handleContextRestored=function e(){this.textureManager.removeAll();this.filterManager.destroy(true);this._initContext()};t.prototype.destroy=function t(r){this.destroyPlugins();this.view.removeEventListener("webglcontextlost",this.handleContextLost);this.view.removeEventListener("webglcontextrestored",this.handleContextRestored);this.textureManager.destroy();e.prototype.destroy.call(this,r);this.uid=0;this.maskManager.destroy();this.stencilManager.destroy();this.filterManager.destroy();this.maskManager=null;this.filterManager=null;this.textureManager=null;this.currentRenderer=null;this.handleContextLost=null;this.handleContextRestored=null;this._contextOptions=null;this.gl.useProgram(null);if(this.gl.getExtension("WEBGL_lose_context")){this.gl.getExtension("WEBGL_lose_context").loseContext()}this.gl=null};return t}(n.default);t.default=k;M.pluginTarget.mixin(k)},a7a2:function(e,t,r){"use strict";t.__esModule=true;t.default=a;var i={5:[.153388,.221461,.250301],7:[.071303,.131514,.189879,.214607],9:[.028532,.067234,.124009,.179044,.20236],11:[.0093,.028002,.065984,.121703,.175713,.198596],13:[.002406,.009255,.027867,.065666,.121117,.174868,.197641],15:[489e-6,.002403,.009246,.02784,.065602,.120999,.174697,.197448]};var n=["varying vec2 vBlurTexCoords[%size%];","uniform sampler2D uSampler;","void main(void)","{","    gl_FragColor = vec4(0.0);","    %blur%","}"].join("\n");function a(e){var t=i[e];var r=t.length;var a=n;var o="";var s="gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;";var u=void 0;for(var l=0;l<e;l++){var h=s.replace("%index%",l);u=l;if(l>=r){u=e-l-1}h=h.replace("%value%",t[u]);o+=h;o+="\n"}a=a.replace("%blur%",o);a=a.replace("%size%",e);return a}},a7ce:function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=f(i);var a=r("c88f");var o=h(a);var s=r("b018");var u=h(s);var l=r("df7c");function h(e){return e&&e.__esModule?e:{default:e}}function f(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function d(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function p(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var v=n.Matrix.IDENTITY;var y=function(e){p(t,e);function t(r){c(this,t);var i=d(this,e.call(this,r));i.shader=null;return i}t.prototype.onContextChange=function e(){var t=this.renderer.gl;this.shader=new n.Shader(t,"attribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 projectionMatrix;\nuniform mat3 translationMatrix;\nuniform mat3 uTransform;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;\n}\n","varying vec2 vTextureCoord;\nuniform vec4 uColor;\n\nuniform sampler2D uSampler;\n\nvoid main(void)\n{\n    gl_FragColor = texture2D(uSampler, vTextureCoord) * uColor;\n}\n")};t.prototype.render=function e(t){var r=this.renderer;var i=r.gl;var a=t._texture;if(!a.valid){return}var s=t._glDatas[r.CONTEXT_UID];if(!s){r.bindVao(null);s={shader:this.shader,vertexBuffer:o.default.GLBuffer.createVertexBuffer(i,t.vertices,i.STREAM_DRAW),uvBuffer:o.default.GLBuffer.createVertexBuffer(i,t.uvs,i.STREAM_DRAW),indexBuffer:o.default.GLBuffer.createIndexBuffer(i,t.indices,i.STATIC_DRAW),vao:null,dirty:t.dirty,indexDirty:t.indexDirty,vertexDirty:t.vertexDirty};s.vao=new o.default.VertexArrayObject(i).addIndex(s.indexBuffer).addAttribute(s.vertexBuffer,s.shader.attributes.aVertexPosition,i.FLOAT,false,2*4,0).addAttribute(s.uvBuffer,s.shader.attributes.aTextureCoord,i.FLOAT,false,2*4,0);t._glDatas[r.CONTEXT_UID]=s}r.bindVao(s.vao);if(t.dirty!==s.dirty){s.dirty=t.dirty;s.uvBuffer.upload(t.uvs)}if(t.indexDirty!==s.indexDirty){s.indexDirty=t.indexDirty;s.indexBuffer.upload(t.indices)}if(t.vertexDirty!==s.vertexDirty){s.vertexDirty=t.vertexDirty;s.vertexBuffer.upload(t.vertices)}r.bindShader(s.shader);s.shader.uniforms.uSampler=r.bindTexture(a);r.state.setBlendMode(n.utils.correctBlendMode(t.blendMode,a.baseTexture.premultipliedAlpha));if(s.shader.uniforms.uTransform){if(t.uploadUvTransform){s.shader.uniforms.uTransform=t._uvTransform.mapCoord.toArray(true)}else{s.shader.uniforms.uTransform=v.toArray(true)}}s.shader.uniforms.translationMatrix=t.worldTransform.toArray(true);s.shader.uniforms.uColor=n.utils.premultiplyRgba(t.tintRgb,t.worldAlpha,s.shader.uniforms.uColor,a.baseTexture.premultipliedAlpha);var l=t.drawMode===u.default.DRAW_MODES.TRIANGLE_MESH?i.TRIANGLE_STRIP:i.TRIANGLES;s.vao.draw(l,t.indices.length,0)};return t}(n.ObjectRenderer);t.default=y;n.WebGLRenderer.registerPlugin("mesh",y)},a843:function(e,t,r){"use strict";t.__esModule=true;var i=r("4d71");var n=o(i);var a=r("a506");function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;s(this,e);this.x=t;this.y=r;this.width=i;this.height=n;this.type=a.SHAPES.ELIP}e.prototype.clone=function t(){return new e(this.x,this.y,this.width,this.height)};e.prototype.contains=function e(t,r){if(this.width<=0||this.height<=0){return false}var i=(t-this.x)/this.width;var n=(r-this.y)/this.height;i*=i;n*=n;return i+n<=1};e.prototype.getBounds=function e(){return new n.default(this.x-this.width,this.y-this.height,this.width,this.height)};return e}();t.default=u},aa59:function(e,t,r){"use strict";t.__esModule=true;t.shared=t.Resource=t.textureParser=t.getResourcePath=t.spritesheetParser=t.parseBitmapFontData=t.bitmapFontParser=t.Loader=undefined;var i=r("a172");Object.defineProperty(t,"bitmapFontParser",{enumerable:true,get:function e(){return f(i).default}});Object.defineProperty(t,"parseBitmapFontData",{enumerable:true,get:function e(){return i.parse}});var n=r("371d");Object.defineProperty(t,"spritesheetParser",{enumerable:true,get:function e(){return f(n).default}});Object.defineProperty(t,"getResourcePath",{enumerable:true,get:function e(){return n.getResourcePath}});var a=r("48b2");Object.defineProperty(t,"textureParser",{enumerable:true,get:function e(){return f(a).default}});var o=r("cfe7");Object.defineProperty(t,"Resource",{enumerable:true,get:function e(){return o.Resource}});var s=r("ccb2");var u=f(s);var l=r("7bc8");var h=f(l);function f(e){return e&&e.__esModule?e:{default:e}}t.Loader=h.default;var c=new h.default;c.destroy=function(){};t.shared=c;var d=u.default.prototype;d._loader=null;Object.defineProperty(d,"loader",{get:function e(){if(!this._loader){var t=this._options.sharedLoader;this._loader=t?c:new h.default}return this._loader}});d._parentDestroy=d.destroy;d.destroy=function e(t,r){if(this._loader){this._loader.destroy();this._loader=null}this._parentDestroy(t,r)}},aa9d:function(e,t,r){"use strict";t.__esModule=true;t.premultiplyBlendMode=t.BaseTextureCache=t.TextureCache=t.earcut=t.mixins=t.pluginTarget=t.EventEmitter=t.removeItems=t.isMobile=undefined;t.uid=w;t.hex2rgb=S;t.hex2string=O;t.rgb2hex=M;t.getResolutionOfUrl=P;t.decomposeDataUri=C;t.getUrlFileExtension=A;t.getSvgSize=R;t.skipHello=I;t.sayHello=D;t.isWebGLSupported=L;t.sign=N;t.destroyTextureCache=F;t.clearTextureCache=U;t.correctBlendMode=X;t.premultiplyTint=H;t.premultiplyRgba=G;t.premultiplyTintToRgba=W;var i=r("a506");var n=r("61df");var a=x(n);var o=r("ba10");var s=x(o);var u=r("9acd");var l=x(u);var h=r("b90d");var f=b(h);var c=r("8114");var d=b(c);var p=r("d109");var v=x(p);var y=r("7467");var g=x(y);var _=r("5664");var m=x(_);function b(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function x(e){return e&&e.__esModule?e:{default:e}}var T=0;var E=false;t.isMobile=d;t.removeItems=v.default;t.EventEmitter=s.default;t.pluginTarget=l.default;t.mixins=f;t.earcut=m.default;function w(){return++T}function S(e,t){t=t||[];t[0]=(e>>16&255)/255;t[1]=(e>>8&255)/255;t[2]=(e&255)/255;return t}function O(e){e=e.toString(16);e="000000".substr(0,6-e.length)+e;return"#"+e}function M(e){return(e[0]*255<<16)+(e[1]*255<<8)+(e[2]*255|0)}function P(e,t){var r=a.default.RETINA_PREFIX.exec(e);if(r){return parseFloat(r[1])}return t!==undefined?t:1}function C(e){var t=i.DATA_URI.exec(e);if(t){return{mediaType:t[1]?t[1].toLowerCase():undefined,subType:t[2]?t[2].toLowerCase():undefined,charset:t[3]?t[3].toLowerCase():undefined,encoding:t[4]?t[4].toLowerCase():undefined,data:t[5]}}return undefined}function A(e){var t=i.URL_FILE_EXTENSION.exec(e);if(t){return t[1].toLowerCase()}return undefined}function R(e){var t=i.SVG_SIZE.exec(e);var r={};if(t){r[t[1]]=Math.round(parseFloat(t[3]));r[t[5]]=Math.round(parseFloat(t[7]))}return r}function I(){E=true}function D(e){if(E){return}if(navigator.userAgent.toLowerCase().indexOf("chrome")>-1){var t=["\n %c %c %c PixiJS "+i.VERSION+" - ✰ "+e+" ✰  %c  %c  http://www.pixijs.com/  %c %c ♥%c♥%c♥ \n\n","background: #ff66a5; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff66a5; background: #030307; padding:5px 0;","background: #ff66a5; padding:5px 0;","background: #ffc3dc; padding:5px 0;","background: #ff66a5; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;","color: #ff2424; background: #fff; padding:5px 0;"];window.console.log.apply(console,t)}else if(window.console){window.console.log("PixiJS "+i.VERSION+" - "+e+" - http://www.pixijs.com/")}E=true}function L(){var e={stencil:true,failIfMajorPerformanceCaveat:true};try{if(!window.WebGLRenderingContext){return false}var t=document.createElement("canvas");var r=t.getContext("webgl",e)||t.getContext("experimental-webgl",e);var i=!!(r&&r.getContextAttributes().stencil);if(r){var n=r.getExtension("WEBGL_lose_context");if(n){n.loseContext()}}r=null;return i}catch(a){return false}}function N(e){if(e===0)return 0;return e<0?-1:1}var k=t.TextureCache=Object.create(null);var B=t.BaseTextureCache=Object.create(null);function F(){var e=void 0;for(e in k){k[e].destroy()}for(e in B){B[e].destroy()}}function U(){var e=void 0;for(e in k){delete k[e]}for(e in B){delete B[e]}}var j=t.premultiplyBlendMode=(0,g.default)();function X(e,t){return j[t?1:0][e]}function H(e,t){if(t===1){return(t*255<<24)+e}if(t===0){return 0}var r=e>>16&255;var i=e>>8&255;var n=e&255;r=r*t+.5|0;i=i*t+.5|0;n=n*t+.5|0;return(t*255<<24)+(r<<16)+(i<<8)+n}function G(e,t,r,i){r=r||new Float32Array(4);if(i||i===undefined){r[0]=e[0]*t;r[1]=e[1]*t;r[2]=e[2]*t}else{r[0]=e[0];r[1]=e[1];r[2]=e[2]}r[3]=t;return r}function W(e,t,r,i){r=r||new Float32Array(4);r[0]=(e>>16&255)/255;r[1]=(e>>8&255)/255;r[2]=(e&255)/255;if(i||i===undefined){r[0]*=t;r[1]*=t;r[2]*=t}r[3]=t;return r}},ac8f:function(e,t){var r=function(e,t){switch(e){case"float":return 0;case"vec2":return new Float32Array(2*t);case"vec3":return new Float32Array(3*t);case"vec4":return new Float32Array(4*t);case"int":case"sampler2D":return 0;case"ivec2":return new Int32Array(2*t);case"ivec3":return new Int32Array(3*t);case"ivec4":return new Int32Array(4*t);case"bool":return false;case"bvec2":return i(2*t);case"bvec3":return i(3*t);case"bvec4":return i(4*t);case"mat2":return new Float32Array([1,0,0,1]);case"mat3":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}};var i=function(e){var t=new Array(e);for(var r=0;r<t.length;r++){t[r]=false}return t};e.exports=r},ad41:function(e,t,r){"use strict";t.__esModule=true;t.calculateScreenSpaceMatrix=n;t.calculateNormalizedScreenSpaceMatrix=a;t.calculateSpriteMatrix=o;var i=r("774e");function n(e,t,r){var i=e.identity();i.translate(t.x/r.width,t.y/r.height);i.scale(r.width,r.height);return i}function a(e,t,r){var i=e.identity();i.translate(t.x/r.width,t.y/r.height);var n=r.width/t.width;var a=r.height/t.height;i.scale(n,a);return i}function o(e,t,r,n){var a=n._texture.orig;var o=e.set(r.width,0,0,r.height,t.x,t.y);var s=n.worldTransform.copy(i.Matrix.TEMP_MATRIX);s.invert();o.prepend(s);o.scale(1/a.width,1/a.height);o.translate(n.anchor.x,n.anchor.y);return o}},aef4:function(e,t,r){"use strict";var i=r("dc07");var n=a(i);function a(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}n.DisplayObject.prototype.name=null;n.Container.prototype.getChildByName=function e(t){for(var r=0;r<this.children.length;r++){if(this.children[r].name===t){return this.children[r]}}return null}},b008:function(e,t,r){"use strict";t.__esModule=true;var i=r("774e");function n(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(){n(this,e);this.minX=Infinity;this.minY=Infinity;this.maxX=-Infinity;this.maxY=-Infinity;this.rect=null}e.prototype.isEmpty=function e(){return this.minX>this.maxX||this.minY>this.maxY};e.prototype.clear=function e(){this.updateID++;this.minX=Infinity;this.minY=Infinity;this.maxX=-Infinity;this.maxY=-Infinity};e.prototype.getRectangle=function e(t){if(this.minX>this.maxX||this.minY>this.maxY){return i.Rectangle.EMPTY}t=t||new i.Rectangle(0,0,1,1);t.x=this.minX;t.y=this.minY;t.width=this.maxX-this.minX;t.height=this.maxY-this.minY;return t};e.prototype.addPoint=function e(t){this.minX=Math.min(this.minX,t.x);this.maxX=Math.max(this.maxX,t.x);this.minY=Math.min(this.minY,t.y);this.maxY=Math.max(this.maxY,t.y)};e.prototype.addQuad=function e(t){var r=this.minX;var i=this.minY;var n=this.maxX;var a=this.maxY;var o=t[0];var s=t[1];r=o<r?o:r;i=s<i?s:i;n=o>n?o:n;a=s>a?s:a;o=t[2];s=t[3];r=o<r?o:r;i=s<i?s:i;n=o>n?o:n;a=s>a?s:a;o=t[4];s=t[5];r=o<r?o:r;i=s<i?s:i;n=o>n?o:n;a=s>a?s:a;o=t[6];s=t[7];r=o<r?o:r;i=s<i?s:i;n=o>n?o:n;a=s>a?s:a;this.minX=r;this.minY=i;this.maxX=n;this.maxY=a};e.prototype.addFrame=function e(t,r,i,n,a){var o=t.worldTransform;var s=o.a;var u=o.b;var l=o.c;var h=o.d;var f=o.tx;var c=o.ty;var d=this.minX;var p=this.minY;var v=this.maxX;var y=this.maxY;var g=s*r+l*i+f;var _=u*r+h*i+c;d=g<d?g:d;p=_<p?_:p;v=g>v?g:v;y=_>y?_:y;g=s*n+l*i+f;_=u*n+h*i+c;d=g<d?g:d;p=_<p?_:p;v=g>v?g:v;y=_>y?_:y;g=s*r+l*a+f;_=u*r+h*a+c;d=g<d?g:d;p=_<p?_:p;v=g>v?g:v;y=_>y?_:y;g=s*n+l*a+f;_=u*n+h*a+c;d=g<d?g:d;p=_<p?_:p;v=g>v?g:v;y=_>y?_:y;this.minX=d;this.minY=p;this.maxX=v;this.maxY=y};e.prototype.addVertices=function e(t,r,i,n){var a=t.worldTransform;var o=a.a;var s=a.b;var u=a.c;var l=a.d;var h=a.tx;var f=a.ty;var c=this.minX;var d=this.minY;var p=this.maxX;var v=this.maxY;for(var y=i;y<n;y+=2){var g=r[y];var _=r[y+1];var m=o*g+u*_+h;var b=l*_+s*g+f;c=m<c?m:c;d=b<d?b:d;p=m>p?m:p;v=b>v?b:v}this.minX=c;this.minY=d;this.maxX=p;this.maxY=v};e.prototype.addBounds=function e(t){var r=this.minX;var i=this.minY;var n=this.maxX;var a=this.maxY;this.minX=t.minX<r?t.minX:r;this.minY=t.minY<i?t.minY:i;this.maxX=t.maxX>n?t.maxX:n;this.maxY=t.maxY>a?t.maxY:a};e.prototype.addBoundsMask=function e(t,r){var i=t.minX>r.minX?t.minX:r.minX;var n=t.minY>r.minY?t.minY:r.minY;var a=t.maxX<r.maxX?t.maxX:r.maxX;var o=t.maxY<r.maxY?t.maxY:r.maxY;if(i<=a&&n<=o){var s=this.minX;var u=this.minY;var l=this.maxX;var h=this.maxY;this.minX=i<s?i:s;this.minY=n<u?n:u;this.maxX=a>l?a:l;this.maxY=o>h?o:h}};e.prototype.addBoundsArea=function e(t,r){var i=t.minX>r.x?t.minX:r.x;var n=t.minY>r.y?t.minY:r.y;var a=t.maxX<r.x+r.width?t.maxX:r.x+r.width;var o=t.maxY<r.y+r.height?t.maxY:r.y+r.height;if(i<=a&&n<=o){var s=this.minX;var u=this.minY;var l=this.maxX;var h=this.maxY;this.minX=i<s?i:s;this.minY=n<u?n:u;this.maxX=a>l?a:l;this.maxY=o>h?o:h}};return e}();t.default=a},b018:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=l(n);var o=r("c49b");var s=u(o);function u(e){return e&&e.__esModule?e:{default:e}}function l(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function h(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function f(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function c(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var d=new a.Point;var p=new a.Polygon;var v=function(e){c(t,e);function t(r,i,n,o,u){h(this,t);var l=f(this,e.call(this));l._texture=r||s.default.EMPTY;l.uvs=n||new Float32Array([0,0,1,0,1,1,0,1]);l.vertices=i||new Float32Array([0,0,100,0,100,100,0,100]);l.indices=o||new Uint16Array([0,1,3,2]);l.dirty=0;l.indexDirty=0;l.vertexDirty=0;l.autoUpdate=true;l.blendMode=a.BLEND_MODES.NORMAL;l.canvasPadding=a.settings.MESH_CANVAS_PADDING;l.drawMode=u||t.DRAW_MODES.TRIANGLE_MESH;l.shader=null;l.tintRgb=new Float32Array([1,1,1]);l._glDatas={};l._uvTransform=new a.TextureMatrix(l._texture);l.uploadUvTransform=false;l.pluginName="mesh";return l}t.prototype._renderWebGL=function e(t){this.refresh();t.setObjectRenderer(t.plugins[this.pluginName]);t.plugins[this.pluginName].render(this)};t.prototype._renderCanvas=function e(t){this.refresh();t.plugins[this.pluginName].render(this)};t.prototype._onTextureUpdate=function e(){this._uvTransform.texture=this._texture;this.refresh()};t.prototype.multiplyUvs=function e(){if(!this.uploadUvTransform){this._uvTransform.multiplyUvs(this.uvs)}};t.prototype.refresh=function e(t){if(this.autoUpdate){this.vertexDirty++}if(this._uvTransform.update(t)){this._refresh()}};t.prototype._refresh=function e(){};t.prototype._calculateBounds=function e(){this._bounds.addVertices(this.transform,this.vertices,0,this.vertices.length)};t.prototype.containsPoint=function e(r){if(!this.getBounds().contains(r.x,r.y)){return false}this.worldTransform.applyInverse(r,d);var i=this.vertices;var n=p.points;var a=this.indices;var o=this.indices.length;var s=this.drawMode===t.DRAW_MODES.TRIANGLES?3:1;for(var u=0;u+2<o;u+=s){var l=a[u]*2;var h=a[u+1]*2;var f=a[u+2]*2;n[0]=i[l];n[1]=i[l+1];n[2]=i[h];n[3]=i[h+1];n[4]=i[f];n[5]=i[f+1];if(p.contains(d.x,d.y)){return true}}return false};t.prototype.destroy=function t(r){for(var i in this._glDatas){var n=this._glDatas[i];if(n.destroy){n.destroy()}else{if(n.vertexBuffer){n.vertexBuffer.destroy();n.vertexBuffer=null}if(n.indexBuffer){n.indexBuffer.destroy();n.indexBuffer=null}if(n.uvBuffer){n.uvBuffer.destroy();n.uvBuffer=null}if(n.vao){n.vao.destroy();n.vao=null}}}this._glDatas=null;e.prototype.destroy.call(this,r)};i(t,[{key:"texture",get:function e(){return this._texture},set:function e(t){if(this._texture===t){return}this._texture=t;if(t){if(t.baseTexture.hasLoaded){this._onTextureUpdate()}else{t.once("update",this._onTextureUpdate,this)}}}},{key:"tint",get:function e(){return a.utils.rgb2hex(this.tintRgb)},set:function e(t){this.tintRgb=a.utils.hex2rgb(t,this.tintRgb)}}]);return t}(a.Container);t.default=v;v.DRAW_MODES={TRIANGLE_MESH:0,TRIANGLES:1}},b042:function(e,t,r){"use strict";(function(e){var t=16;if(!(Date.now&&Date.prototype.getTime)){Date.now=function e(){return(new Date).getTime()}}if(!(e.performance&&e.performance.now)){var r=Date.now();if(!e.performance){e.performance={}}e.performance.now=function(){return Date.now()-r}}var i=Date.now();var n=["ms","moz","webkit","o"];for(var a=0;a<n.length&&!e.requestAnimationFrame;++a){var o=n[a];e.requestAnimationFrame=e[o+"RequestAnimationFrame"];e.cancelAnimationFrame=e[o+"CancelAnimationFrame"]||e[o+"CancelRequestAnimationFrame"]}if(!e.requestAnimationFrame){e.requestAnimationFrame=function(e){if(typeof e!=="function"){throw new TypeError(e+"is not a function")}var r=Date.now();var n=t+i-r;if(n<0){n=0}i=r;return setTimeout(function(){i=Date.now();e(performance.now())},n)}}if(!e.cancelAnimationFrame){e.cancelAnimationFrame=function(e){return clearTimeout(e)}}}).call(this,r("c8ba"))},b353:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc99");var a=f(n);var o=r("aa9d");var s=r("f20d");var u=r("a506");var l=r("69df");var h=f(l);function f(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function d(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function p(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var v=function(e){p(t,e);function t(r,i){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:true;c(this,t);if(!r){throw new Error("No video source element specified.")}if((r.readyState===r.HAVE_ENOUGH_DATA||r.readyState===r.HAVE_FUTURE_DATA)&&r.width&&r.height){r.complete=true}var a=d(this,e.call(this,r,i));a.width=r.videoWidth;a.height=r.videoHeight;a._autoUpdate=true;a._isAutoUpdating=false;a.autoPlay=n;a.update=a.update.bind(a);a._onCanPlay=a._onCanPlay.bind(a);r.addEventListener("play",a._onPlayStart.bind(a));r.addEventListener("pause",a._onPlayStop.bind(a));a.hasLoaded=false;a.__loaded=false;if(!a._isSourceReady()){r.addEventListener("canplay",a._onCanPlay);r.addEventListener("canplaythrough",a._onCanPlay)}else{a._onCanPlay()}return a}t.prototype._isSourcePlaying=function e(){var t=this.source;return t.currentTime>0&&t.paused===false&&t.ended===false&&t.readyState>2};t.prototype._isSourceReady=function e(){return this.source.readyState===3||this.source.readyState===4};t.prototype._onPlayStart=function e(){if(!this.hasLoaded){this._onCanPlay()}if(!this._isAutoUpdating&&this.autoUpdate){s.shared.add(this.update,this,u.UPDATE_PRIORITY.HIGH);this._isAutoUpdating=true}};t.prototype._onPlayStop=function e(){if(this._isAutoUpdating){s.shared.remove(this.update,this);this._isAutoUpdating=false}};t.prototype._onCanPlay=function e(){this.hasLoaded=true;if(this.source){this.source.removeEventListener("canplay",this._onCanPlay);this.source.removeEventListener("canplaythrough",this._onCanPlay);this.width=this.source.videoWidth;this.height=this.source.videoHeight;if(!this.__loaded){this.__loaded=true;this.emit("loaded",this)}if(this._isSourcePlaying()){this._onPlayStart()}else if(this.autoPlay){this.source.play()}}};t.prototype.destroy=function t(){if(this._isAutoUpdating){s.shared.remove(this.update,this)}if(this.source&&this.source._pixiId){a.default.removeFromCache(this.source._pixiId);delete this.source._pixiId;this.source.pause();this.source.src="";this.source.load()}e.prototype.destroy.call(this)};t.fromVideo=function e(r,i,n){if(!r._pixiId){r._pixiId="video_"+(0,o.uid)()}var s=o.BaseTextureCache[r._pixiId];if(!s){s=new t(r,i,n);a.default.addToCache(s,r._pixiId)}return s};t.fromUrl=function e(r,i,n,a){var o=document.createElement("video");o.setAttribute("webkit-playsinline","");o.setAttribute("playsinline","");var s=Array.isArray(r)?r[0].src||r[0]:r.src||r;if(n===undefined&&s.indexOf("data:")!==0){o.crossOrigin=(0,h.default)(s)}else if(n){o.crossOrigin=typeof n==="string"?n:"anonymous"}if(Array.isArray(r)){for(var u=0;u<r.length;++u){o.appendChild(y(r[u].src||r[u],r[u].mime))}}else{o.appendChild(y(s,r.mime))}o.load();return t.fromVideo(o,i,a)};i(t,[{key:"autoUpdate",get:function e(){return this._autoUpdate},set:function e(t){if(t!==this._autoUpdate){this._autoUpdate=t;if(!this._autoUpdate&&this._isAutoUpdating){s.shared.remove(this.update,this);this._isAutoUpdating=false}else if(this._autoUpdate&&!this._isAutoUpdating){s.shared.add(this.update,this,u.UPDATE_PRIORITY.HIGH);this._isAutoUpdating=true}}}}]);return t}(a.default);t.default=v;v.fromUrls=v.fromUrl;function y(e,t){if(!t){var r=e.split("?").shift().toLowerCase();t="video/"+r.substr(r.lastIndexOf(".")+1)}var i=document.createElement("source");i.src=e;i.type=t;return i}},b358:function(e,t,r){"use strict";t.__esModule=true;t.encodeBinary=n;var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/=";function n(e){var t="";var r=0;while(r<e.length){var n=[0,0,0];var a=[0,0,0,0];for(var o=0;o<n.length;++o){if(r<e.length){n[o]=e.charCodeAt(r++)&255}else{n[o]=0}}a[0]=n[0]>>2;a[1]=(n[0]&3)<<4|n[1]>>4;a[2]=(n[1]&15)<<2|n[2]>>6;a[3]=n[2]&63;var s=r-(e.length-1);switch(s){case 2:a[3]=64;a[2]=64;break;case 1:a[3]=64;break;default:break}for(var u=0;u<a.length;++u){t+=i.charAt(a[u])}}return t}if(true){e.exports.default=n}},b7fe:function(e,t,r){"use strict";t.__esModule=true;var i=r("a557");var n=o(i);var a=r("a506");function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(t){s(this,e);this.renderer=t}e.prototype.render=function e(t){var r=this.renderer;var i=r.context;var n=t.worldAlpha;var o=t.transform.worldTransform;var s=r.resolution;i.setTransform(o.a*s,o.b*s,o.c*s,o.d*s,o.tx*s,o.ty*s);if(t.canvasTintDirty!==t.dirty||t._prevTint!==t.tint){this.updateGraphicsTint(t)}r.setBlendMode(t.blendMode);for(var u=0;u<t.graphicsData.length;u++){var l=t.graphicsData[u];var h=l.shape;var f=l._fillTint;var c=l._lineTint;i.lineWidth=l.lineWidth;if(l.type===a.SHAPES.POLY){i.beginPath();var d=h.points;var p=l.holes;var v=void 0;var y=void 0;i.moveTo(d[0],d[1]);for(var g=2;g<d.length;g+=2){i.lineTo(d[g],d[g+1])}if(h.closed){i.closePath()}if(p.length>0){v=0;for(var _=0;_<d.length;_+=2){v+=d[_]*d[_+3]-d[_+1]*d[_+2]}for(var m=0;m<p.length;m++){d=p[m].points;y=0;for(var b=0;b<d.length;b+=2){y+=d[b]*d[b+3]-d[b+1]*d[b+2]}i.moveTo(d[0],d[1]);if(y*v<0){for(var x=2;x<d.length;x+=2){i.lineTo(d[x],d[x+1])}}else{for(var T=d.length-2;T>=2;T-=2){i.lineTo(d[T],d[T+1])}}if(p[m].closed){i.closePath()}}}if(l.fill){i.globalAlpha=l.fillAlpha*n;i.fillStyle="#"+("00000"+(f|0).toString(16)).substr(-6);i.fill()}if(l.lineWidth){i.globalAlpha=l.lineAlpha*n;i.strokeStyle="#"+("00000"+(c|0).toString(16)).substr(-6);i.stroke()}}else if(l.type===a.SHAPES.RECT){if(l.fillColor||l.fillColor===0){i.globalAlpha=l.fillAlpha*n;i.fillStyle="#"+("00000"+(f|0).toString(16)).substr(-6);i.fillRect(h.x,h.y,h.width,h.height)}if(l.lineWidth){i.globalAlpha=l.lineAlpha*n;i.strokeStyle="#"+("00000"+(c|0).toString(16)).substr(-6);i.strokeRect(h.x,h.y,h.width,h.height)}}else if(l.type===a.SHAPES.CIRC){i.beginPath();i.arc(h.x,h.y,h.radius,0,2*Math.PI);i.closePath();if(l.fill){i.globalAlpha=l.fillAlpha*n;i.fillStyle="#"+("00000"+(f|0).toString(16)).substr(-6);i.fill()}if(l.lineWidth){i.globalAlpha=l.lineAlpha*n;i.strokeStyle="#"+("00000"+(c|0).toString(16)).substr(-6);i.stroke()}}else if(l.type===a.SHAPES.ELIP){var E=h.width*2;var w=h.height*2;var S=h.x-E/2;var O=h.y-w/2;i.beginPath();var M=.5522848;var P=E/2*M;var C=w/2*M;var A=S+E;var R=O+w;var I=S+E/2;var D=O+w/2;i.moveTo(S,D);i.bezierCurveTo(S,D-C,I-P,O,I,O);i.bezierCurveTo(I+P,O,A,D-C,A,D);i.bezierCurveTo(A,D+C,I+P,R,I,R);i.bezierCurveTo(I-P,R,S,D+C,S,D);i.closePath();if(l.fill){i.globalAlpha=l.fillAlpha*n;i.fillStyle="#"+("00000"+(f|0).toString(16)).substr(-6);i.fill()}if(l.lineWidth){i.globalAlpha=l.lineAlpha*n;i.strokeStyle="#"+("00000"+(c|0).toString(16)).substr(-6);i.stroke()}}else if(l.type===a.SHAPES.RREC){var L=h.x;var N=h.y;var k=h.width;var B=h.height;var F=h.radius;var U=Math.min(k,B)/2|0;F=F>U?U:F;i.beginPath();i.moveTo(L,N+F);i.lineTo(L,N+B-F);i.quadraticCurveTo(L,N+B,L+F,N+B);i.lineTo(L+k-F,N+B);i.quadraticCurveTo(L+k,N+B,L+k,N+B-F);i.lineTo(L+k,N+F);i.quadraticCurveTo(L+k,N,L+k-F,N);i.lineTo(L+F,N);i.quadraticCurveTo(L,N,L,N+F);i.closePath();if(l.fillColor||l.fillColor===0){i.globalAlpha=l.fillAlpha*n;i.fillStyle="#"+("00000"+(f|0).toString(16)).substr(-6);i.fill()}if(l.lineWidth){i.globalAlpha=l.lineAlpha*n;i.strokeStyle="#"+("00000"+(c|0).toString(16)).substr(-6);i.stroke()}}}};e.prototype.updateGraphicsTint=function e(t){t._prevTint=t.tint;t.canvasTintDirty=t.dirty;var r=(t.tint>>16&255)/255;var i=(t.tint>>8&255)/255;var n=(t.tint&255)/255;for(var a=0;a<t.graphicsData.length;++a){var o=t.graphicsData[a];var s=o.fillColor|0;var u=o.lineColor|0;o._fillTint=((s>>16&255)/255*r*255<<16)+((s>>8&255)/255*i*255<<8)+(s&255)/255*n*255;o._lineTint=((u>>16&255)/255*r*255<<16)+((u>>8&255)/255*i*255<<8)+(u&255)/255*n*255}};e.prototype.renderPolygon=function e(t,r,i){i.moveTo(t[0],t[1]);for(var n=1;n<t.length/2;++n){i.lineTo(t[n*2],t[n*2+1])}if(r){i.closePath()}};e.prototype.destroy=function e(){this.renderer=null};return e}();t.default=u;n.default.registerPlugin("graphics",u)},b90d:function(e,t,r){"use strict";t.__esModule=true;t.mixin=i;t.delayMixin=a;t.performMixins=o;function i(e,t){if(!e||!t)return;var r=Object.keys(t);for(var i=0;i<r.length;++i){var n=r[i];Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}}var n=[];function a(e,t){n.push(e,t)}function o(){for(var e=0;e<n.length;e+=2){i(n[e],n[e+1])}n.length=0}},b9cf:function(e,t,r){"use strict";t.__esModule=true;t.autoDetectRenderer=f;var i=r("aa9d");var n=h(i);var a=r("a557");var o=l(a);var s=r("a55e");var u=l(s);function l(e){return e&&e.__esModule?e:{default:e}}function h(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function f(e,t,r,i){var a=e&&e.forceCanvas;if(i!==undefined){a=i}if(!a&&n.isWebGLSupported()){return new u.default(e,t,r)}return new o.default(e,t,r)}},ba10:function(e,t,r){"use strict";var i=Object.prototype.hasOwnProperty,n="~";function a(){}if(Object.create){a.prototype=Object.create(null);if(!(new a).__proto__)n=false}function o(e,t,r){this.fn=e;this.context=t;this.once=r||false}function s(){this._events=new a;this._eventsCount=0}s.prototype.eventNames=function e(){var t=[],r,a;if(this._eventsCount===0)return t;for(a in r=this._events){if(i.call(r,a))t.push(n?a.slice(1):a)}if(Object.getOwnPropertySymbols){return t.concat(Object.getOwnPropertySymbols(r))}return t};s.prototype.listeners=function e(t,r){var i=n?n+t:t,a=this._events[i];if(r)return!!a;if(!a)return[];if(a.fn)return[a.fn];for(var o=0,s=a.length,u=new Array(s);o<s;o++){u[o]=a[o].fn}return u};s.prototype.emit=function e(t,r,i,a,o,s){var u=n?n+t:t;if(!this._events[u])return false;var l=this._events[u],h=arguments.length,f,c;if(l.fn){if(l.once)this.removeListener(t,l.fn,undefined,true);switch(h){case 1:return l.fn.call(l.context),true;case 2:return l.fn.call(l.context,r),true;case 3:return l.fn.call(l.context,r,i),true;case 4:return l.fn.call(l.context,r,i,a),true;case 5:return l.fn.call(l.context,r,i,a,o),true;case 6:return l.fn.call(l.context,r,i,a,o,s),true}for(c=1,f=new Array(h-1);c<h;c++){f[c-1]=arguments[c]}l.fn.apply(l.context,f)}else{var d=l.length,p;for(c=0;c<d;c++){if(l[c].once)this.removeListener(t,l[c].fn,undefined,true);switch(h){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,r);break;case 3:l[c].fn.call(l[c].context,r,i);break;case 4:l[c].fn.call(l[c].context,r,i,a);break;default:if(!f)for(p=1,f=new Array(h-1);p<h;p++){f[p-1]=arguments[p]}l[c].fn.apply(l[c].context,f)}}}return true};s.prototype.on=function e(t,r,i){var a=new o(r,i||this),s=n?n+t:t;if(!this._events[s])this._events[s]=a,this._eventsCount++;else if(!this._events[s].fn)this._events[s].push(a);else this._events[s]=[this._events[s],a];return this};s.prototype.once=function e(t,r,i){var a=new o(r,i||this,true),s=n?n+t:t;if(!this._events[s])this._events[s]=a,this._eventsCount++;else if(!this._events[s].fn)this._events[s].push(a);else this._events[s]=[this._events[s],a];return this};s.prototype.removeListener=function e(t,r,i,o){var s=n?n+t:t;if(!this._events[s])return this;if(!r){if(--this._eventsCount===0)this._events=new a;else delete this._events[s];return this}var u=this._events[s];if(u.fn){if(u.fn===r&&(!o||u.once)&&(!i||u.context===i)){if(--this._eventsCount===0)this._events=new a;else delete this._events[s]}}else{for(var l=0,h=[],f=u.length;l<f;l++){if(u[l].fn!==r||o&&!u[l].once||i&&u[l].context!==i){h.push(u[l])}}if(h.length)this._events[s]=h.length===1?h[0]:h;else if(--this._eventsCount===0)this._events=new a;else delete this._events[s]}return this};s.prototype.removeAllListeners=function e(t){var r;if(t){r=n?n+t:t;if(this._events[r]){if(--this._eventsCount===0)this._events=new a;else delete this._events[r]}}else{this._events=new a;this._eventsCount=0}return this};s.prototype.off=s.prototype.removeListener;s.prototype.addListener=s.prototype.on;s.prototype.setMaxListeners=function e(){return this};s.prefixed=n;s.EventEmitter=s;if(true){e.exports=s}},ba33:function(e,t){var r=function(e){return i[e]};var i={float:1,vec2:2,vec3:3,vec4:4,int:1,ivec2:2,ivec3:3,ivec4:4,bool:1,bvec2:2,bvec3:3,bvec4:4,mat2:4,mat3:9,mat4:16,sampler2D:1};e.exports=r},bbad:function(e,t,r){"use strict";e.exports=function e(t,r){r=r||{};var i={key:["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}};var n=i.parser[r.strictMode?"strict":"loose"].exec(t);var a={};var o=14;while(o--)a[i.key[o]]=n[o]||"";a[i.q.name]={};a[i.key[12]].replace(i.q.parser,function(e,t,r){if(t)a[i.q.name][t]=r});return a}},bd43:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("a506");var a=r("aa9d");function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s={align:"left",breakWords:false,dropShadow:false,dropShadowAlpha:1,dropShadowAngle:Math.PI/6,dropShadowBlur:0,dropShadowColor:"black",dropShadowDistance:5,fill:"black",fillGradientType:n.TEXT_GRADIENT.LINEAR_VERTICAL,fillGradientStops:[],fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",letterSpacing:0,lineHeight:0,lineJoin:"miter",miterLimit:10,padding:0,stroke:"black",strokeThickness:0,textBaseline:"alphabetic",trim:false,whiteSpace:"pre",wordWrap:false,wordWrapWidth:100,leading:0};var u=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];var l=function(){function e(t){o(this,e);this.styleID=0;this.reset();d(this,t,t)}e.prototype.clone=function t(){var r={};d(r,this,s);return new e(r)};e.prototype.reset=function e(){d(this,s,s)};e.prototype.toFontString=function e(){var t=typeof this.fontSize==="number"?this.fontSize+"px":this.fontSize;var r=this.fontFamily;if(!Array.isArray(this.fontFamily)){r=this.fontFamily.split(",")}for(var i=r.length-1;i>=0;i--){var n=r[i].trim();if(!/([\"\'])[^\'\"]+\1/.test(n)&&u.indexOf(n)<0){n='"'+n+'"'}r[i]=n}return this.fontStyle+" "+this.fontVariant+" "+this.fontWeight+" "+t+" "+r.join(",")};i(e,[{key:"align",get:function e(){return this._align},set:function e(t){if(this._align!==t){this._align=t;this.styleID++}}},{key:"breakWords",get:function e(){return this._breakWords},set:function e(t){if(this._breakWords!==t){this._breakWords=t;this.styleID++}}},{key:"dropShadow",get:function e(){return this._dropShadow},set:function e(t){if(this._dropShadow!==t){this._dropShadow=t;this.styleID++}}},{key:"dropShadowAlpha",get:function e(){return this._dropShadowAlpha},set:function e(t){if(this._dropShadowAlpha!==t){this._dropShadowAlpha=t;this.styleID++}}},{key:"dropShadowAngle",get:function e(){return this._dropShadowAngle},set:function e(t){if(this._dropShadowAngle!==t){this._dropShadowAngle=t;this.styleID++}}},{key:"dropShadowBlur",get:function e(){return this._dropShadowBlur},set:function e(t){if(this._dropShadowBlur!==t){this._dropShadowBlur=t;this.styleID++}}},{key:"dropShadowColor",get:function e(){return this._dropShadowColor},set:function e(t){var r=f(t);if(this._dropShadowColor!==r){this._dropShadowColor=r;this.styleID++}}},{key:"dropShadowDistance",get:function e(){return this._dropShadowDistance},set:function e(t){if(this._dropShadowDistance!==t){this._dropShadowDistance=t;this.styleID++}}},{key:"fill",get:function e(){return this._fill},set:function e(t){var r=f(t);if(this._fill!==r){this._fill=r;this.styleID++}}},{key:"fillGradientType",get:function e(){return this._fillGradientType},set:function e(t){if(this._fillGradientType!==t){this._fillGradientType=t;this.styleID++}}},{key:"fillGradientStops",get:function e(){return this._fillGradientStops},set:function e(t){if(!c(this._fillGradientStops,t)){this._fillGradientStops=t;this.styleID++}}},{key:"fontFamily",get:function e(){return this._fontFamily},set:function e(t){if(this.fontFamily!==t){this._fontFamily=t;this.styleID++}}},{key:"fontSize",get:function e(){return this._fontSize},set:function e(t){if(this._fontSize!==t){this._fontSize=t;this.styleID++}}},{key:"fontStyle",get:function e(){return this._fontStyle},set:function e(t){if(this._fontStyle!==t){this._fontStyle=t;this.styleID++}}},{key:"fontVariant",get:function e(){return this._fontVariant},set:function e(t){if(this._fontVariant!==t){this._fontVariant=t;this.styleID++}}},{key:"fontWeight",get:function e(){return this._fontWeight},set:function e(t){if(this._fontWeight!==t){this._fontWeight=t;this.styleID++}}},{key:"letterSpacing",get:function e(){return this._letterSpacing},set:function e(t){if(this._letterSpacing!==t){this._letterSpacing=t;this.styleID++}}},{key:"lineHeight",get:function e(){return this._lineHeight},set:function e(t){if(this._lineHeight!==t){this._lineHeight=t;this.styleID++}}},{key:"leading",get:function e(){return this._leading},set:function e(t){if(this._leading!==t){this._leading=t;this.styleID++}}},{key:"lineJoin",get:function e(){return this._lineJoin},set:function e(t){if(this._lineJoin!==t){this._lineJoin=t;this.styleID++}}},{key:"miterLimit",get:function e(){return this._miterLimit},set:function e(t){if(this._miterLimit!==t){this._miterLimit=t;this.styleID++}}},{key:"padding",get:function e(){return this._padding},set:function e(t){if(this._padding!==t){this._padding=t;this.styleID++}}},{key:"stroke",get:function e(){return this._stroke},set:function e(t){var r=f(t);if(this._stroke!==r){this._stroke=r;this.styleID++}}},{key:"strokeThickness",get:function e(){return this._strokeThickness},set:function e(t){if(this._strokeThickness!==t){this._strokeThickness=t;this.styleID++}}},{key:"textBaseline",get:function e(){return this._textBaseline},set:function e(t){if(this._textBaseline!==t){this._textBaseline=t;this.styleID++}}},{key:"trim",get:function e(){return this._trim},set:function e(t){if(this._trim!==t){this._trim=t;this.styleID++}}},{key:"whiteSpace",get:function e(){return this._whiteSpace},set:function e(t){if(this._whiteSpace!==t){this._whiteSpace=t;this.styleID++}}},{key:"wordWrap",get:function e(){return this._wordWrap},set:function e(t){if(this._wordWrap!==t){this._wordWrap=t;this.styleID++}}},{key:"wordWrapWidth",get:function e(){return this._wordWrapWidth},set:function e(t){if(this._wordWrapWidth!==t){this._wordWrapWidth=t;this.styleID++}}}]);return e}();t.default=l;function h(e){if(typeof e==="number"){return(0,a.hex2string)(e)}else if(typeof e==="string"){if(e.indexOf("0x")===0){e=e.replace("0x","#")}}return e}function f(e){if(!Array.isArray(e)){return h(e)}else{for(var t=0;t<e.length;++t){e[t]=h(e[t])}return e}}function c(e,t){if(!Array.isArray(e)||!Array.isArray(t)){return false}if(e.length!==t.length){return false}for(var r=0;r<e.length;++r){if(e[r]!==t[r]){return false}}return true}function d(e,t,r){for(var i in r){if(Array.isArray(t[i])){e[i]=t[i].slice()}else{e[i]=t[i]}}}},c43e:function(e,t,r){"use strict";t.__esModule=true;t.default=u;var i=r("3745");var n=o(i);var a=r("df7c");function o(e){return e&&e.__esModule?e:{default:e}}var s=["varying vec2 vTextureCoord;","varying vec4 vColor;","varying float vTextureId;","uniform sampler2D uSamplers[%count%];","void main(void){","vec4 color;","float textureId = floor(vTextureId+0.5);","%forloop%","gl_FragColor = color * vColor;","}"].join("\n");function u(e,t){var r="precision highp float;\nattribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\nattribute vec4 aColor;\nattribute float aTextureId;\n\nuniform mat3 projectionMatrix;\n\nvarying vec2 vTextureCoord;\nvarying vec4 vColor;\nvarying float vTextureId;\n\nvoid main(void){\n    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\n\n    vTextureCoord = aTextureCoord;\n    vTextureId = aTextureId;\n    vColor = aColor;\n}\n";var i=s;i=i.replace(/%count%/gi,t);i=i.replace(/%forloop%/gi,l(t));var a=new n.default(e,r,i);var o=[];for(var u=0;u<t;u++){o[u]=u}a.bind();a.uniforms.uSamplers=o;return a}function l(e){var t="";t+="\n";t+="\n";for(var r=0;r<e;r++){if(r>0){t+="\nelse "}if(r<e-1){t+="if(textureId == "+r+".0)"}t+="\n{";t+="\n\tcolor = texture2D(uSamplers["+r+"], vTextureCoord);";t+="\n}"}t+="\n";t+="\n";return t}},c49b:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc99");var a=y(n);var o=r("b353");var s=y(o);var u=r("f7ad");var l=y(u);var h=r("ba10");var f=y(h);var c=r("774e");var d=r("aa9d");var p=r("61df");var v=y(p);function y(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function _(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function m(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var b=function(e){m(t,e);function t(r,i,n,a,o,s){g(this,t);var u=_(this,e.call(this));u.noFrame=false;if(!i){u.noFrame=true;i=new c.Rectangle(0,0,1,1)}if(r instanceof t){r=r.baseTexture}u.baseTexture=r;u._frame=i;u.trim=a;u.valid=false;u.requiresUpdate=false;u._uvs=null;u.orig=n||i;u._rotate=Number(o||0);if(o===true){u._rotate=2}else if(u._rotate%2!==0){throw new Error("attempt to use diamond-shaped UVs. If you are sure, set rotation manually")}if(r.hasLoaded){if(u.noFrame){i=new c.Rectangle(0,0,r.width,r.height);r.on("update",u.onBaseTextureUpdated,u)}u.frame=i}else{r.once("loaded",u.onBaseTextureLoaded,u)}u.defaultAnchor=s?new c.Point(s.x,s.y):new c.Point(0,0);u._updateID=0;u.transform=null;u.textureCacheIds=[];return u}t.prototype.update=function e(){this.baseTexture.update()};t.prototype.onBaseTextureLoaded=function e(t){this._updateID++;if(this.noFrame){this.frame=new c.Rectangle(0,0,t.width,t.height)}else{this.frame=this._frame}this.baseTexture.on("update",this.onBaseTextureUpdated,this);this.emit("update",this)};t.prototype.onBaseTextureUpdated=function e(t){this._updateID++;this._frame.width=t.width;this._frame.height=t.height;this.emit("update",this)};t.prototype.destroy=function e(r){if(this.baseTexture){if(r){if(d.TextureCache[this.baseTexture.imageUrl]){t.removeFromCache(this.baseTexture.imageUrl)}this.baseTexture.destroy()}this.baseTexture.off("update",this.onBaseTextureUpdated,this);this.baseTexture.off("loaded",this.onBaseTextureLoaded,this);this.baseTexture=null}this._frame=null;this._uvs=null;this.trim=null;this.orig=null;this.valid=false;t.removeFromCache(this);this.textureCacheIds=null};t.prototype.clone=function e(){return new t(this.baseTexture,this.frame,this.orig,this.trim,this.rotate,this.defaultAnchor)};t.prototype._updateUvs=function e(){if(!this._uvs){this._uvs=new l.default}this._uvs.set(this._frame,this.baseTexture,this.rotate);this._updateID++};t.fromImage=function e(r,i,n,o){var s=d.TextureCache[r];if(!s){s=new t(a.default.fromImage(r,i,n,o));t.addToCache(s,r)}return s};t.fromFrame=function e(t){var r=d.TextureCache[t];if(!r){throw new Error('The frameId "'+t+'" does not exist in the texture cache')}return r};t.fromCanvas=function e(r,i){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"canvas";return new t(a.default.fromCanvas(r,i,n))};t.fromVideo=function e(r,i,n,a){if(typeof r==="string"){return t.fromVideoUrl(r,i,n,a)}return new t(s.default.fromVideo(r,i,a))};t.fromVideoUrl=function e(r,i,n,a){return new t(s.default.fromUrl(r,i,n,a))};t.from=function e(r){if(typeof r==="string"){var i=d.TextureCache[r];if(!i){var n=r.match(/\.(mp4|webm|ogg|h264|avi|mov)$/)!==null;if(n){return t.fromVideoUrl(r)}return t.fromImage(r)}return i}else if(r instanceof HTMLImageElement){return new t(a.default.from(r))}else if(r instanceof HTMLCanvasElement){return t.fromCanvas(r,v.default.SCALE_MODE,"HTMLCanvasElement")}else if(r instanceof HTMLVideoElement){return t.fromVideo(r)}else if(r instanceof a.default){return new t(r)}return r};t.fromLoader=function e(r,i,n){var o=new a.default(r,undefined,(0,d.getResolutionOfUrl)(i));var s=new t(o);o.imageUrl=i;if(!n){n=i}a.default.addToCache(s.baseTexture,n);t.addToCache(s,n);if(n!==i){a.default.addToCache(s.baseTexture,i);t.addToCache(s,i)}return s};t.addToCache=function e(t,r){if(r){if(t.textureCacheIds.indexOf(r)===-1){t.textureCacheIds.push(r)}if(d.TextureCache[r]){console.warn("Texture added to the cache with an id ["+r+"] that already had an entry")}d.TextureCache[r]=t}};t.removeFromCache=function e(t){if(typeof t==="string"){var r=d.TextureCache[t];if(r){var i=r.textureCacheIds.indexOf(t);if(i>-1){r.textureCacheIds.splice(i,1)}delete d.TextureCache[t];return r}}else if(t&&t.textureCacheIds){for(var n=0;n<t.textureCacheIds.length;++n){if(d.TextureCache[t.textureCacheIds[n]]===t){delete d.TextureCache[t.textureCacheIds[n]]}}t.textureCacheIds.length=0;return t}return null};i(t,[{key:"frame",get:function e(){return this._frame},set:function e(t){this._frame=t;this.noFrame=false;var r=t.x,i=t.y,n=t.width,a=t.height;var o=r+n>this.baseTexture.width;var s=i+a>this.baseTexture.height;if(o||s){var u=o&&s?"and":"or";var l="X: "+r+" + "+n+" = "+(r+n)+" > "+this.baseTexture.width;var h="Y: "+i+" + "+a+" = "+(i+a)+" > "+this.baseTexture.height;throw new Error("Texture Error: frame does not fit inside the base Texture dimensions: "+(l+" "+u+" "+h))}this.valid=n&&a&&this.baseTexture.hasLoaded;if(!this.trim&&!this.rotate){this.orig=t}if(this.valid){this._updateUvs()}}},{key:"rotate",get:function e(){return this._rotate},set:function e(t){this._rotate=t;if(this.valid){this._updateUvs()}}},{key:"width",get:function e(){return this.orig.width}},{key:"height",get:function e(){return this.orig.height}}]);return t}(f.default);t.default=b;function x(){var e=document.createElement("canvas");e.width=10;e.height=10;var t=e.getContext("2d");t.fillStyle="white";t.fillRect(0,0,10,10);return new b(new a.default(e))}function T(e){e.destroy=function e(){};e.on=function e(){};e.once=function e(){};e.emit=function e(){}}b.EMPTY=new b(new a.default);T(b.EMPTY);T(b.EMPTY.baseTexture);b.WHITE=x();T(b.WHITE);T(b.WHITE.baseTexture)},c576:function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;i(this,e);this.fn=t;this.context=r;this.priority=n;this.once=a;this.next=null;this.previous=null;this._destroyed=false}e.prototype.match=function e(t,r){r=r||null;return this.fn===t&&this.context===r};e.prototype.emit=function e(t){if(this.fn){if(this.context){this.fn.call(this.context,t)}else{this.fn(t)}}var r=this.next;if(this.once){this.destroy(true)}if(this._destroyed){this.next=null}return r};e.prototype.connect=function e(t){this.previous=t;if(t.next){t.next.previous=this}this.next=t.next;t.next=this};e.prototype.destroy=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;this._destroyed=true;this.fn=null;this.context=null;if(this.previous){this.previous.next=this.next}if(this.next){this.next.previous=this.previous}var r=this.next;this.next=t?null:r;this.previous=null;return r};return e}();t.default=n},c676:function(e,t,r){"use strict";if(!Math.sign){Math.sign=function e(t){t=Number(t);if(t===0||isNaN(t)){return t}return t>0?1:-1}}},c6a9:function(e,t,r){"use strict";t.__esModule=true;t.default=o;var i=r("8114");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e){if(n.default.tablet||n.default.phone){return 4}return e}},c88f:function(e,t,r){var i={createContext:r("2bd4"),setVertexAttribArrays:r("2545"),GLBuffer:r("1cdc"),GLFramebuffer:r("14d6"),GLShader:r("3f62"),GLTexture:r("07b7"),VertexArrayObject:r("9ddd"),shader:r("974c")};if(true&&e.exports){e.exports=i}if(typeof window!=="undefined"){window.PIXI=window.PIXI||{};window.PIXI.glCore=i}},c98e:function(e,t,r){"use strict";t.__esModule=true;t.default=l;var i=r("5664");var n=u(i);var a=r("e4ec");var o=u(a);var s=r("aa9d");function u(e){return e&&e.__esModule?e:{default:e}}function l(e,t,r){var i=e.shape;var a=i.x;var u=i.y;var l=i.width;var h=i.height;var c=i.radius;var d=[];d.push(a+c,u);f(a+l-c,u,a+l,u,a+l,u+c,d);f(a+l,u+h-c,a+l,u+h,a+l-c,u+h,d);f(a+c,u+h,a,u+h,a,u+h-c,d);f(a,u+c,a,u,a+c+1e-10,u,d);if(e.fill){var p=(0,s.hex2rgb)(e.fillColor);var v=e.fillAlpha;var y=p[0]*v;var g=p[1]*v;var _=p[2]*v;var m=t.points;var b=t.indices;var x=m.length/6;var T=(0,n.default)(d,null,2);for(var E=0,w=T.length;E<w;E+=3){b.push(T[E]+x);b.push(T[E]+x);b.push(T[E+1]+x);b.push(T[E+2]+x);b.push(T[E+2]+x)}for(var S=0,O=d.length;S<O;S++){m.push(d[S],d[++S],y,g,_,v)}}if(e.lineWidth){var M=e.points;e.points=d;(0,o.default)(e,t,r);e.points=M}}function h(e,t,r){var i=t-e;return e+i*r}function f(e,t,r,i,n,a){var o=arguments.length>6&&arguments[6]!==undefined?arguments[6]:[];var s=20;var u=o;var l=0;var f=0;var c=0;var d=0;var p=0;var v=0;for(var y=0,g=0;y<=s;++y){g=y/s;l=h(e,r,g);f=h(t,i,g);c=h(r,n,g);d=h(i,a,g);p=h(l,c,g);v=h(f,d,g);u.push(p,v)}return u}},cbfa:function(e,t,r){"use strict";t.__esModule=true;var i=r("25e8");Object.defineProperty(t,"FXAAFilter",{enumerable:true,get:function e(){return f(i).default}});var n=r("22ce");Object.defineProperty(t,"NoiseFilter",{enumerable:true,get:function e(){return f(n).default}});var a=r("7d2e");Object.defineProperty(t,"DisplacementFilter",{enumerable:true,get:function e(){return f(a).default}});var o=r("7726a");Object.defineProperty(t,"BlurFilter",{enumerable:true,get:function e(){return f(o).default}});var s=r("238d");Object.defineProperty(t,"BlurXFilter",{enumerable:true,get:function e(){return f(s).default}});var u=r("0120");Object.defineProperty(t,"BlurYFilter",{enumerable:true,get:function e(){return f(u).default}});var l=r("63c9");Object.defineProperty(t,"ColorMatrixFilter",{enumerable:true,get:function e(){return f(l).default}});var h=r("8caf");Object.defineProperty(t,"AlphaFilter",{enumerable:true,get:function e(){return f(h).default}});function f(e){return e&&e.__esModule?e:{default:e}}},ccb2:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("b9cf");var a=r("6bf3");var o=f(a);var s=r("f20d");var u=r("61df");var l=f(u);var h=r("a506");function f(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var d=function(){function e(t,r,i,a,u){c(this,e);if(typeof t==="number"){t=Object.assign({width:t,height:r||l.default.RENDER_OPTIONS.height,forceCanvas:!!a,sharedTicker:!!u},i)}this._options=t=Object.assign({autoStart:true,sharedTicker:false,forceCanvas:false,sharedLoader:false},t);this.renderer=(0,n.autoDetectRenderer)(t);this.stage=new o.default;this._ticker=null;this.ticker=t.sharedTicker?s.shared:new s.Ticker;if(t.autoStart){this.start()}}e.prototype.render=function e(){this.renderer.render(this.stage)};e.prototype.stop=function e(){this._ticker.stop()};e.prototype.start=function e(){this._ticker.start()};e.prototype.destroy=function e(t,r){if(this._ticker){var i=this._ticker;this.ticker=null;i.destroy()}this.stage.destroy(r);this.stage=null;this.renderer.destroy(t);this.renderer=null;this._options=null};i(e,[{key:"ticker",set:function e(t){if(this._ticker){this._ticker.remove(this.render,this)}this._ticker=t;if(t){t.add(this.render,this,h.UPDATE_PRIORITY.LOW)}},get:function e(){return this._ticker}},{key:"view",get:function e(){return this.renderer.view}},{key:"screen",get:function e(){return this.renderer.screen}}]);return e}();t.default=d},cfe7:function(e,t,r){"use strict";var i=r("2641").Loader;var n=r("5f08").Resource;var a=r("51d3");var o=r("b358");i.Resource=n;i.async=a;i.encodeBinary=o;i.base64=o;e.exports=i;e.exports.Loader=i;e.exports.default=i},d035:function(e,t){var r=function(e,t){if(e.substring(0,9)!=="precision"){return"precision "+t+" float;\n"+e}return e};e.exports=r},d075:function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=f(i);var a=r("f5af");var o=h(a);var s=r("97cb");var u=h(s);var l=r("aa9d");function h(e){return e&&e.__esModule?e:{default:e}}function f(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function d(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function p(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var v=function(e){p(t,e);function t(r){c(this,t);var i=d(this,e.call(this,r));i.shader=null;i.indexBuffer=null;i.properties=null;i.tempMatrix=new n.Matrix;i.CONTEXT_UID=0;return i}t.prototype.onContextChange=function e(){var t=this.renderer.gl;this.CONTEXT_UID=this.renderer.CONTEXT_UID;this.shader=new o.default(t);this.properties=[{attribute:this.shader.attributes.aVertexPosition,size:2,uploadFunction:this.uploadVertices,offset:0},{attribute:this.shader.attributes.aPositionCoord,size:2,uploadFunction:this.uploadPosition,offset:0},{attribute:this.shader.attributes.aRotation,size:1,uploadFunction:this.uploadRotation,offset:0},{attribute:this.shader.attributes.aTextureCoord,size:2,uploadFunction:this.uploadUvs,offset:0},{attribute:this.shader.attributes.aColor,size:1,unsignedByte:true,uploadFunction:this.uploadTint,offset:0}]};t.prototype.start=function e(){this.renderer.bindShader(this.shader)};t.prototype.render=function e(t){var r=t.children;var i=t._maxSize;var a=t._batchSize;var o=this.renderer;var s=r.length;if(s===0){return}else if(s>i){s=i}var u=t._glBuffers[o.CONTEXT_UID];if(!u){u=t._glBuffers[o.CONTEXT_UID]=this.generateBuffers(t)}var l=r[0]._texture.baseTexture;this.renderer.setBlendMode(n.utils.correctBlendMode(t.blendMode,l.premultipliedAlpha));var h=o.gl;var f=t.worldTransform.copy(this.tempMatrix);f.prepend(o._activeRenderTarget.projectionMatrix);this.shader.uniforms.projectionMatrix=f.toArray(true);this.shader.uniforms.uColor=n.utils.premultiplyRgba(t.tintRgb,t.worldAlpha,this.shader.uniforms.uColor,l.premultipliedAlpha);this.shader.uniforms.uSampler=o.bindTexture(l);var c=false;for(var d=0,p=0;d<s;d+=a,p+=1){var v=s-d;if(v>a){v=a}if(p>=u.length){if(!t.autoResize){break}u.push(this._generateOneMoreBuffer(t))}var y=u[p];y.uploadDynamic(r,d,v);var g=t._bufferUpdateIDs[p]||0;c=c||y._updateID<g;if(c){y._updateID=t._updateID;y.uploadStatic(r,d,v)}o.bindVao(y.vao);y.vao.draw(h.TRIANGLES,v*6)}};t.prototype.generateBuffers=function e(t){var r=this.renderer.gl;var i=[];var n=t._maxSize;var a=t._batchSize;var o=t._properties;for(var s=0;s<n;s+=a){i.push(new u.default(r,this.properties,o,a))}return i};t.prototype._generateOneMoreBuffer=function e(t){var r=this.renderer.gl;var i=t._batchSize;var n=t._properties;return new u.default(r,this.properties,n,i)};t.prototype.uploadVertices=function e(t,r,i,n,a,o){var s=0;var u=0;var l=0;var h=0;for(var f=0;f<i;++f){var c=t[r+f];var d=c._texture;var p=c.scale.x;var v=c.scale.y;var y=d.trim;var g=d.orig;if(y){u=y.x-c.anchor.x*g.width;s=u+y.width;h=y.y-c.anchor.y*g.height;l=h+y.height}else{s=g.width*(1-c.anchor.x);u=g.width*-c.anchor.x;l=g.height*(1-c.anchor.y);h=g.height*-c.anchor.y}n[o]=u*p;n[o+1]=h*v;n[o+a]=s*p;n[o+a+1]=h*v;n[o+a*2]=s*p;n[o+a*2+1]=l*v;n[o+a*3]=u*p;n[o+a*3+1]=l*v;o+=a*4}};t.prototype.uploadPosition=function e(t,r,i,n,a,o){for(var s=0;s<i;s++){var u=t[r+s].position;n[o]=u.x;n[o+1]=u.y;n[o+a]=u.x;n[o+a+1]=u.y;n[o+a*2]=u.x;n[o+a*2+1]=u.y;n[o+a*3]=u.x;n[o+a*3+1]=u.y;o+=a*4}};t.prototype.uploadRotation=function e(t,r,i,n,a,o){for(var s=0;s<i;s++){var u=t[r+s].rotation;n[o]=u;n[o+a]=u;n[o+a*2]=u;n[o+a*3]=u;o+=a*4}};t.prototype.uploadUvs=function e(t,r,i,n,a,o){for(var s=0;s<i;++s){var u=t[r+s]._texture._uvs;if(u){n[o]=u.x0;n[o+1]=u.y0;n[o+a]=u.x1;n[o+a+1]=u.y1;n[o+a*2]=u.x2;n[o+a*2+1]=u.y2;n[o+a*3]=u.x3;n[o+a*3+1]=u.y3;o+=a*4}else{n[o]=0;n[o+1]=0;n[o+a]=0;n[o+a+1]=0;n[o+a*2]=0;n[o+a*2+1]=0;n[o+a*3]=0;n[o+a*3+1]=0;o+=a*4}}};t.prototype.uploadTint=function e(t,r,i,n,a,o){for(var s=0;s<i;++s){var u=t[r+s];var h=u._texture.baseTexture.premultipliedAlpha;var f=u.alpha;var c=f<1&&h?(0,l.premultiplyTint)(u._tintRGB,f):u._tintRGB+(f*255<<24);n[o]=c;n[o+a]=c;n[o+a*2]=c;n[o+a*3]=c;o+=a*4}};t.prototype.destroy=function t(){if(this.renderer.gl){this.renderer.gl.deleteBuffer(this.indexBuffer)}e.prototype.destroy.call(this);this.shader.destroy();this.indices=null;this.tempMatrix=null};return t}(n.ObjectRenderer);t.default=v;n.WebGLRenderer.registerPlugin("particle",v)},d109:function(e,t,r){"use strict";e.exports=function e(t,r,i){var n,a=t.length;if(r>=a||i===0){return}i=r+i>a?a-r:i;var o=a-i;for(n=r;n<o;++n){t[n]=t[n+i]}t.length=o}},d427:function(e,t,r){"use strict";t.__esModule=true;var i=r("b018");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function u(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var l=function(e){u(t,e);function t(r,i){o(this,t);var n=s(this,e.call(this,r));n.points=i;n.vertices=new Float32Array(i.length*4);n.uvs=new Float32Array(i.length*4);n.colors=new Float32Array(i.length*2);n.indices=new Uint16Array(i.length*2);n.autoUpdate=true;n.refresh();return n}t.prototype._refresh=function e(){var t=this.points;if(t.length<1||!this._texture._uvs){return}if(this.vertices.length/4!==t.length){this.vertices=new Float32Array(t.length*4);this.uvs=new Float32Array(t.length*4);this.colors=new Float32Array(t.length*2);this.indices=new Uint16Array(t.length*2)}var r=this.uvs;var i=this.indices;var n=this.colors;r[0]=0;r[1]=0;r[2]=0;r[3]=1;n[0]=1;n[1]=1;i[0]=0;i[1]=1;var a=t.length;for(var o=1;o<a;o++){var s=o*4;var u=o/(a-1);r[s]=u;r[s+1]=0;r[s+2]=u;r[s+3]=1;s=o*2;n[s]=1;n[s+1]=1;s=o*2;i[s]=s;i[s+1]=s+1}this.dirty++;this.indexDirty++;this.multiplyUvs();this.refreshVertices()};t.prototype.refreshVertices=function e(){var t=this.points;if(t.length<1){return}var r=t[0];var i=void 0;var n=0;var a=0;var o=this.vertices;var s=t.length;for(var u=0;u<s;u++){var l=t[u];var h=u*4;if(u<t.length-1){i=t[u+1]}else{i=l}a=-(i.x-r.x);n=i.y-r.y;var f=(1-u/(s-1))*10;if(f>1){f=1}var c=Math.sqrt(n*n+a*a);var d=this._texture.height/2;n/=c;a/=c;n*=d;a*=d;o[h]=l.x+n;o[h+1]=l.y+a;o[h+2]=l.x-n;o[h+3]=l.y-a;r=l}};t.prototype.updateTransform=function e(){if(this.autoUpdate){this.refreshVertices()}this.containerUpdateTransform()};return t}(n.default);t.default=l},d5e4:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();function n(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(t,r,i){if(r===undefined)r=false;n(this,e);this._fn=t;this._once=r;this._thisArg=i;this._next=this._prev=this._owner=null}i(e,[{key:"detach",value:function e(){if(this._owner===null)return false;this._owner.detach(this);return true}}]);return e}();function o(e,t){if(!e._head){e._head=t;e._tail=t}else{e._tail._next=t;t._prev=e._tail;e._tail=t}t._owner=e;return t}var s=function(){function e(){n(this,e);this._head=this._tail=undefined}i(e,[{key:"handlers",value:function e(){var t=arguments.length<=0||arguments[0]===undefined?false:arguments[0];var r=this._head;if(t)return!!r;var i=[];while(r){i.push(r);r=r._next}return i}},{key:"has",value:function e(t){if(!(t instanceof a)){throw new Error("MiniSignal#has(): First arg must be a MiniSignalBinding object.")}return t._owner===this}},{key:"dispatch",value:function e(){var t=this._head;if(!t)return false;while(t){if(t._once)this.detach(t);t._fn.apply(t._thisArg,arguments);t=t._next}return true}},{key:"add",value:function e(t){var r=arguments.length<=1||arguments[1]===undefined?null:arguments[1];if(typeof t!=="function"){throw new Error("MiniSignal#add(): First arg must be a Function.")}return o(this,new a(t,false,r))}},{key:"once",value:function e(t){var r=arguments.length<=1||arguments[1]===undefined?null:arguments[1];if(typeof t!=="function"){throw new Error("MiniSignal#once(): First arg must be a Function.")}return o(this,new a(t,true,r))}},{key:"detach",value:function e(t){if(!(t instanceof a)){throw new Error("MiniSignal#detach(): First arg must be a MiniSignalBinding object.")}if(t._owner!==this)return this;if(t._prev)t._prev._next=t._next;if(t._next)t._next._prev=t._prev;if(t===this._head){this._head=t._next;if(t._next===null){this._tail=null}}else if(t===this._tail){this._tail=t._prev;this._tail._next=null}t._owner=null;return this}},{key:"detachAll",value:function e(){var t=this._head;if(!t)return this;this._head=this._tail=null;while(t){t._owner=null;t=t._next}return this}}]);return e}();s.MiniSignalBinding=a;t["default"]=s;e.exports=t["default"]},d6d7:function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(){i(this,e);this.stopped=false;this.target=null;this.currentTarget=null;this.type=null;this.data=null}e.prototype.stopPropagation=function e(){this.stopped=true};e.prototype.reset=function e(){this.stopped=false;this.currentTarget=null;this.target=null};return e}();t.default=n},d801:function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=h(i);var a=r("8114");var o=l(a);var s=r("6f1d");var u=l(s);function l(e){return e&&e.__esModule?e:{default:e}}function h(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function f(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}n.utils.mixins.delayMixin(n.DisplayObject.prototype,u.default);var c=9;var d=100;var p=0;var v=0;var y=2;var g=1;var _=-1e3;var m=-1e3;var b=2;var x=function(){function e(t){f(this,e);if((o.default.tablet||o.default.phone)&&!navigator.isCocoonJS){this.createTouchHook()}var r=document.createElement("div");r.style.width=d+"px";r.style.height=d+"px";r.style.position="absolute";r.style.top=p+"px";r.style.left=v+"px";r.style.zIndex=y;this.div=r;this.pool=[];this.renderId=0;this.debug=false;this.renderer=t;this.children=[];this._onKeyDown=this._onKeyDown.bind(this);this._onMouseMove=this._onMouseMove.bind(this);this.isActive=false;this.isMobileAccessabillity=false;window.addEventListener("keydown",this._onKeyDown,false)}e.prototype.createTouchHook=function e(){var t=this;var r=document.createElement("button");r.style.width=g+"px";r.style.height=g+"px";r.style.position="absolute";r.style.top=_+"px";r.style.left=m+"px";r.style.zIndex=b;r.style.backgroundColor="#FF0000";r.title="HOOK DIV";r.addEventListener("focus",function(){t.isMobileAccessabillity=true;t.activate();document.body.removeChild(r)});document.body.appendChild(r)};e.prototype.activate=function e(){if(this.isActive){return}this.isActive=true;window.document.addEventListener("mousemove",this._onMouseMove,true);window.removeEventListener("keydown",this._onKeyDown,false);this.renderer.on("postrender",this.update,this);if(this.renderer.view.parentNode){this.renderer.view.parentNode.appendChild(this.div)}};e.prototype.deactivate=function e(){if(!this.isActive||this.isMobileAccessabillity){return}this.isActive=false;window.document.removeEventListener("mousemove",this._onMouseMove,true);window.addEventListener("keydown",this._onKeyDown,false);this.renderer.off("postrender",this.update);if(this.div.parentNode){this.div.parentNode.removeChild(this.div)}};e.prototype.updateAccessibleObjects=function e(t){if(!t.visible){return}if(t.accessible&&t.interactive){if(!t._accessibleActive){this.addChild(t)}t.renderId=this.renderId}var r=t.children;for(var i=0;i<r.length;i++){this.updateAccessibleObjects(r[i])}};e.prototype.update=function e(){if(!this.renderer.renderingToScreen){return}this.updateAccessibleObjects(this.renderer._lastObjectRendered);var t=this.renderer.view.getBoundingClientRect();var r=t.width/this.renderer.width;var i=t.height/this.renderer.height;var a=this.div;a.style.left=t.left+"px";a.style.top=t.top+"px";a.style.width=this.renderer.width+"px";a.style.height=this.renderer.height+"px";for(var o=0;o<this.children.length;o++){var s=this.children[o];if(s.renderId!==this.renderId){s._accessibleActive=false;n.utils.removeItems(this.children,o,1);this.div.removeChild(s._accessibleDiv);this.pool.push(s._accessibleDiv);s._accessibleDiv=null;o--;if(this.children.length===0){this.deactivate()}}else{a=s._accessibleDiv;var u=s.hitArea;var l=s.worldTransform;if(s.hitArea){a.style.left=(l.tx+u.x*l.a)*r+"px";a.style.top=(l.ty+u.y*l.d)*i+"px";a.style.width=u.width*l.a*r+"px";a.style.height=u.height*l.d*i+"px"}else{u=s.getBounds();this.capHitArea(u);a.style.left=u.x*r+"px";a.style.top=u.y*i+"px";a.style.width=u.width*r+"px";a.style.height=u.height*i+"px";if(a.title!==s.accessibleTitle&&s.accessibleTitle!==null){a.title=s.accessibleTitle}if(a.getAttribute("aria-label")!==s.accessibleHint&&s.accessibleHint!==null){a.setAttribute("aria-label",s.accessibleHint)}}}}this.renderId++};e.prototype.capHitArea=function e(t){if(t.x<0){t.width+=t.x;t.x=0}if(t.y<0){t.height+=t.y;t.y=0}if(t.x+t.width>this.renderer.width){t.width=this.renderer.width-t.x}if(t.y+t.height>this.renderer.height){t.height=this.renderer.height-t.y}};e.prototype.addChild=function e(t){var r=this.pool.pop();if(!r){r=document.createElement("button");r.style.width=d+"px";r.style.height=d+"px";r.style.backgroundColor=this.debug?"rgba(255,0,0,0.5)":"transparent";r.style.position="absolute";r.style.zIndex=y;r.style.borderStyle="none";if(navigator.userAgent.toLowerCase().indexOf("chrome")>-1){r.setAttribute("aria-live","off")}else{r.setAttribute("aria-live","polite")}if(navigator.userAgent.match(/rv:.*Gecko\//)){r.setAttribute("aria-relevant","additions")}else{r.setAttribute("aria-relevant","text")}r.addEventListener("click",this._onClick.bind(this));r.addEventListener("focus",this._onFocus.bind(this));r.addEventListener("focusout",this._onFocusOut.bind(this))}if(t.accessibleTitle&&t.accessibleTitle!==null){r.title=t.accessibleTitle}else if(!t.accessibleHint||t.accessibleHint===null){r.title="displayObject "+t.tabIndex}if(t.accessibleHint&&t.accessibleHint!==null){r.setAttribute("aria-label",t.accessibleHint)}t._accessibleActive=true;t._accessibleDiv=r;r.displayObject=t;this.children.push(t);this.div.appendChild(t._accessibleDiv);t._accessibleDiv.tabIndex=t.tabIndex};e.prototype._onClick=function e(t){var r=this.renderer.plugins.interaction;r.dispatchEvent(t.target.displayObject,"click",r.eventData)};e.prototype._onFocus=function e(t){if(!t.target.getAttribute("aria-live","off")){t.target.setAttribute("aria-live","assertive")}var r=this.renderer.plugins.interaction;r.dispatchEvent(t.target.displayObject,"mouseover",r.eventData)};e.prototype._onFocusOut=function e(t){if(!t.target.getAttribute("aria-live","off")){t.target.setAttribute("aria-live","polite")}var r=this.renderer.plugins.interaction;r.dispatchEvent(t.target.displayObject,"mouseout",r.eventData)};e.prototype._onKeyDown=function e(t){if(t.keyCode!==c){return}this.activate()};e.prototype._onMouseMove=function e(t){if(t.movementX===0&&t.movementY===0){return}this.deactivate()};e.prototype.destroy=function e(){this.div=null;for(var t=0;t<this.children.length;t++){this.children[t].div=null}window.document.removeEventListener("mousemove",this._onMouseMove,true);window.removeEventListener("keydown",this._onKeyDown);this.pool=null;this.children=null;this.renderer=null};return e}();t.default=x;n.WebGLRenderer.registerPlugin("accessibility",x);n.CanvasRenderer.registerPlugin("accessibility",x)},d886:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("ba10");var a=y(n);var o=r("a506");var s=r("61df");var u=y(s);var l=r("825e");var h=y(l);var f=r("6274");var c=y(f);var d=r("b008");var p=y(d);var v=r("774e");function y(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function _(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function m(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var b=function(e){m(t,e);function t(){g(this,t);var r=_(this,e.call(this));var i=u.default.TRANSFORM_MODE===o.TRANSFORM_MODE.STATIC?h.default:c.default;r.tempDisplayObjectParent=null;r.transform=new i;r.alpha=1;r.visible=true;r.renderable=true;r.parent=null;r.worldAlpha=1;r.filterArea=null;r._filters=null;r._enabledFilters=null;r._bounds=new p.default;r._boundsID=0;r._lastBoundsID=-1;r._boundsRect=null;r._localBoundsRect=null;r._mask=null;r._destroyed=false;return r}t.prototype.updateTransform=function e(){this.transform.updateTransform(this.parent.transform);this.worldAlpha=this.alpha*this.parent.worldAlpha;this._bounds.updateID++};t.prototype._recursivePostUpdateTransform=function e(){if(this.parent){this.parent._recursivePostUpdateTransform();this.transform.updateTransform(this.parent.transform)}else{this.transform.updateTransform(this._tempDisplayObjectParent.transform)}};t.prototype.getBounds=function e(t,r){if(!t){if(!this.parent){this.parent=this._tempDisplayObjectParent;this.updateTransform();this.parent=null}else{this._recursivePostUpdateTransform();this.updateTransform()}}if(this._boundsID!==this._lastBoundsID){this.calculateBounds()}if(!r){if(!this._boundsRect){this._boundsRect=new v.Rectangle}r=this._boundsRect}return this._bounds.getRectangle(r)};t.prototype.getLocalBounds=function e(t){var r=this.transform;var i=this.parent;this.parent=null;this.transform=this._tempDisplayObjectParent.transform;if(!t){if(!this._localBoundsRect){this._localBoundsRect=new v.Rectangle}t=this._localBoundsRect}var n=this.getBounds(false,t);this.parent=i;this.transform=r;return n};t.prototype.toGlobal=function e(t,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;if(!i){this._recursivePostUpdateTransform();if(!this.parent){this.parent=this._tempDisplayObjectParent;this.displayObjectUpdateTransform();this.parent=null}else{this.displayObjectUpdateTransform()}}return this.worldTransform.apply(t,r)};t.prototype.toLocal=function e(t,r,i,n){if(r){t=r.toGlobal(t,i,n)}if(!n){this._recursivePostUpdateTransform();if(!this.parent){this.parent=this._tempDisplayObjectParent;this.displayObjectUpdateTransform();this.parent=null}else{this.displayObjectUpdateTransform()}}return this.worldTransform.applyInverse(t,i)};t.prototype.renderWebGL=function e(t){};t.prototype.renderCanvas=function e(t){};t.prototype.setParent=function e(t){if(!t||!t.addChild){throw new Error("setParent: Argument must be a Container")}t.addChild(this);return t};t.prototype.setTransform=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:1;var a=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;var s=arguments.length>6&&arguments[6]!==undefined?arguments[6]:0;var u=arguments.length>7&&arguments[7]!==undefined?arguments[7]:0;var l=arguments.length>8&&arguments[8]!==undefined?arguments[8]:0;this.position.x=t;this.position.y=r;this.scale.x=!i?1:i;this.scale.y=!n?1:n;this.rotation=a;this.skew.x=o;this.skew.y=s;this.pivot.x=u;this.pivot.y=l;return this};t.prototype.destroy=function e(){this.removeAllListeners();if(this.parent){this.parent.removeChild(this)}this.transform=null;this.parent=null;this._bounds=null;this._currentBounds=null;this._mask=null;this.filterArea=null;this.interactive=false;this.interactiveChildren=false;this._destroyed=true};i(t,[{key:"_tempDisplayObjectParent",get:function e(){if(this.tempDisplayObjectParent===null){this.tempDisplayObjectParent=new t}return this.tempDisplayObjectParent}},{key:"x",get:function e(){return this.position.x},set:function e(t){this.transform.position.x=t}},{key:"y",get:function e(){return this.position.y},set:function e(t){this.transform.position.y=t}},{key:"worldTransform",get:function e(){return this.transform.worldTransform}},{key:"localTransform",get:function e(){return this.transform.localTransform}},{key:"position",get:function e(){return this.transform.position},set:function e(t){this.transform.position.copy(t)}},{key:"scale",get:function e(){return this.transform.scale},set:function e(t){this.transform.scale.copy(t)}},{key:"pivot",get:function e(){return this.transform.pivot},set:function e(t){this.transform.pivot.copy(t)}},{key:"skew",get:function e(){return this.transform.skew},set:function e(t){this.transform.skew.copy(t)}},{key:"rotation",get:function e(){return this.transform.rotation},set:function e(t){this.transform.rotation=t}},{key:"worldVisible",get:function e(){var t=this;do{if(!t.visible){return false}t=t.parent}while(t);return true}},{key:"mask",get:function e(){return this._mask},set:function e(t){if(this._mask){this._mask.renderable=true;this._mask.isMask=false}this._mask=t;if(this._mask){this._mask.renderable=false;this._mask.isMask=true}}},{key:"filters",get:function e(){return this._filters&&this._filters.slice()},set:function e(t){this._filters=t&&t.slice()}}]);return t}(a.default);t.default=b;b.prototype.displayObjectUpdateTransform=b.prototype.updateTransform},dbac:function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=a(i);function a(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s=new n.Rectangle;var u=function(){function e(t){o(this,e);this.renderer=t;t.extract=this}e.prototype.image=function e(t){var e=new Image;e.src=this.base64(t);return e};e.prototype.base64=function e(t){return this.canvas(t).toDataURL()};e.prototype.canvas=function e(t){var r=this.renderer;var i=void 0;var a=void 0;var o=void 0;var u=void 0;if(t){if(t instanceof n.RenderTexture){u=t}else{u=r.generateTexture(t)}}if(u){i=u.baseTexture._canvasRenderTarget.context;a=u.baseTexture._canvasRenderTarget.resolution;o=u.frame}else{i=r.rootContext;a=r.resolution;o=s;o.width=this.renderer.width;o.height=this.renderer.height}var l=o.width*a;var h=o.height*a;var f=new n.CanvasRenderTarget(l,h,1);var c=i.getImageData(o.x*a,o.y*a,l,h);f.context.putImageData(c,0,0);return f.canvas};e.prototype.pixels=function e(t){var r=this.renderer;var i=void 0;var a=void 0;var o=void 0;var u=void 0;if(t){if(t instanceof n.RenderTexture){u=t}else{u=r.generateTexture(t)}}if(u){i=u.baseTexture._canvasRenderTarget.context;a=u.baseTexture._canvasRenderTarget.resolution;o=u.frame}else{i=r.rootContext;o=s;o.width=r.width;o.height=r.height}return i.getImageData(0,0,o.width*a,o.height*a).data};e.prototype.destroy=function e(){this.renderer.extract=null;this.renderer=null};return e}();t.default=u;n.CanvasRenderer.registerPlugin("extract",u)},dc07:function(e,t,r){"use strict";t.__esModule=true;t.autoDetectRenderer=t.Application=t.Filter=t.SpriteMaskFilter=t.Quad=t.RenderTarget=t.ObjectRenderer=t.WebGLManager=t.Shader=t.CanvasRenderTarget=t.TextureUvs=t.VideoBaseTexture=t.BaseRenderTexture=t.RenderTexture=t.BaseTexture=t.TextureMatrix=t.Texture=t.Spritesheet=t.CanvasGraphicsRenderer=t.GraphicsRenderer=t.GraphicsData=t.Graphics=t.TextMetrics=t.TextStyle=t.Text=t.SpriteRenderer=t.CanvasTinter=t.CanvasSpriteRenderer=t.Sprite=t.TransformBase=t.TransformStatic=t.Transform=t.Container=t.DisplayObject=t.Bounds=t.glCore=t.WebGLRenderer=t.CanvasRenderer=t.ticker=t.utils=t.settings=undefined;var i=r("a506");Object.keys(i).forEach(function(e){if(e==="default"||e==="__esModule")return;Object.defineProperty(t,e,{enumerable:true,get:function t(){return i[e]}})});var n=r("774e");Object.keys(n).forEach(function(e){if(e==="default"||e==="__esModule")return;Object.defineProperty(t,e,{enumerable:true,get:function t(){return n[e]}})});var a=r("c88f");Object.defineProperty(t,"glCore",{enumerable:true,get:function e(){return Q(a).default}});var o=r("b008");Object.defineProperty(t,"Bounds",{enumerable:true,get:function e(){return Q(o).default}});var s=r("d886");Object.defineProperty(t,"DisplayObject",{enumerable:true,get:function e(){return Q(s).default}});var u=r("6bf3");Object.defineProperty(t,"Container",{enumerable:true,get:function e(){return Q(u).default}});var l=r("6274");Object.defineProperty(t,"Transform",{enumerable:true,get:function e(){return Q(l).default}});var h=r("825e");Object.defineProperty(t,"TransformStatic",{enumerable:true,get:function e(){return Q(h).default}});var f=r("281c");Object.defineProperty(t,"TransformBase",{enumerable:true,get:function e(){return Q(f).default}});var c=r("8d00");Object.defineProperty(t,"Sprite",{enumerable:true,get:function e(){return Q(c).default}});var d=r("2cd2");Object.defineProperty(t,"CanvasSpriteRenderer",{enumerable:true,get:function e(){return Q(d).default}});var p=r("fd43");Object.defineProperty(t,"CanvasTinter",{enumerable:true,get:function e(){return Q(p).default}});var v=r("e28d");Object.defineProperty(t,"SpriteRenderer",{enumerable:true,get:function e(){return Q(v).default}});var y=r("6743");Object.defineProperty(t,"Text",{enumerable:true,get:function e(){return Q(y).default}});var g=r("bd43");Object.defineProperty(t,"TextStyle",{enumerable:true,get:function e(){return Q(g).default}});var _=r("4d1d");Object.defineProperty(t,"TextMetrics",{enumerable:true,get:function e(){return Q(_).default}});var m=r("e5d3");Object.defineProperty(t,"Graphics",{enumerable:true,get:function e(){return Q(m).default}});var b=r("00f4");Object.defineProperty(t,"GraphicsData",{enumerable:true,get:function e(){return Q(b).default}});var x=r("ec94");Object.defineProperty(t,"GraphicsRenderer",{enumerable:true,get:function e(){return Q(x).default}});var T=r("b7fe");Object.defineProperty(t,"CanvasGraphicsRenderer",{enumerable:true,get:function e(){return Q(T).default}});var E=r("4c5f");Object.defineProperty(t,"Spritesheet",{enumerable:true,get:function e(){return Q(E).default}});var w=r("c49b");Object.defineProperty(t,"Texture",{enumerable:true,get:function e(){return Q(w).default}});var S=r("e1f8");Object.defineProperty(t,"TextureMatrix",{enumerable:true,get:function e(){return Q(S).default}});var O=r("dc99");Object.defineProperty(t,"BaseTexture",{enumerable:true,get:function e(){return Q(O).default}});var M=r("7881");Object.defineProperty(t,"RenderTexture",{enumerable:true,get:function e(){return Q(M).default}});var P=r("589c");Object.defineProperty(t,"BaseRenderTexture",{enumerable:true,get:function e(){return Q(P).default}});var C=r("b353");Object.defineProperty(t,"VideoBaseTexture",{enumerable:true,get:function e(){return Q(C).default}});var A=r("f7ad");Object.defineProperty(t,"TextureUvs",{enumerable:true,get:function e(){return Q(A).default}});var R=r("a433");Object.defineProperty(t,"CanvasRenderTarget",{enumerable:true,get:function e(){return Q(R).default}});var I=r("3745");Object.defineProperty(t,"Shader",{enumerable:true,get:function e(){return Q(I).default}});var D=r("063c");Object.defineProperty(t,"WebGLManager",{enumerable:true,get:function e(){return Q(D).default}});var L=r("e056");Object.defineProperty(t,"ObjectRenderer",{enumerable:true,get:function e(){return Q(L).default}});var N=r("f16a");Object.defineProperty(t,"RenderTarget",{enumerable:true,get:function e(){return Q(N).default}});var k=r("3ee0");Object.defineProperty(t,"Quad",{enumerable:true,get:function e(){return Q(k).default}});var B=r("85ef");Object.defineProperty(t,"SpriteMaskFilter",{enumerable:true,get:function e(){return Q(B).default}});var F=r("5abd");Object.defineProperty(t,"Filter",{enumerable:true,get:function e(){return Q(F).default}});var U=r("ccb2");Object.defineProperty(t,"Application",{enumerable:true,get:function e(){return Q(U).default}});var j=r("b9cf");Object.defineProperty(t,"autoDetectRenderer",{enumerable:true,get:function e(){return j.autoDetectRenderer}});var X=r("aa9d");var H=J(X);var G=r("f20d");var W=J(G);var Y=r("61df");var V=Q(Y);var z=r("a557");var q=Q(z);var Z=r("a55e");var K=Q(Z);function J(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function Q(e){return e&&e.__esModule?e:{default:e}}t.settings=V.default;t.utils=H;t.ticker=W;t.CanvasRenderer=q.default;t.WebGLRenderer=K.default},dc6c:function(e,t,r){"use strict";t.__esModule=true;var i=r("9d62");Object.defineProperty(t,"ParticleContainer",{enumerable:true,get:function e(){return a(i).default}});var n=r("d075");Object.defineProperty(t,"ParticleRenderer",{enumerable:true,get:function e(){return a(n).default}});function a(e){return e&&e.__esModule?e:{default:e}}},dc99:function(e,t,r){"use strict";t.__esModule=true;var i=r("aa9d");var n=r("61df");var a=c(n);var o=r("ba10");var s=c(o);var u=r("69df");var l=c(u);var h=r("a48a");var f=c(h);function c(e){return e&&e.__esModule?e:{default:e}}function d(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function p(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function v(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var y=function(e){v(t,e);function t(r,n,o){d(this,t);var s=p(this,e.call(this));s.uid=(0,i.uid)();s.touched=0;s.resolution=o||a.default.RESOLUTION;s.width=100;s.height=100;s.realWidth=100;s.realHeight=100;s.scaleMode=n!==undefined?n:a.default.SCALE_MODE;s.hasLoaded=false;s.isLoading=false;s.source=null;s.origSource=null;s.imageType=null;s.sourceScale=1;s.premultipliedAlpha=true;s.imageUrl=null;s.isPowerOfTwo=false;s.mipmap=a.default.MIPMAP_TEXTURES;s.wrapMode=a.default.WRAP_MODE;s._glTextures={};s._enabled=0;s._virtalBoundId=-1;s._destroyed=false;s.textureCacheIds=[];if(r){s.loadSource(r)}return s}t.prototype.update=function e(){if(this.imageType!=="svg"){this.realWidth=this.source.naturalWidth||this.source.videoWidth||this.source.width;this.realHeight=this.source.naturalHeight||this.source.videoHeight||this.source.height;this._updateDimensions()}this.emit("update",this)};t.prototype._updateDimensions=function e(){this.width=this.realWidth/this.resolution;this.height=this.realHeight/this.resolution;this.isPowerOfTwo=f.default.isPow2(this.realWidth)&&f.default.isPow2(this.realHeight)};t.prototype.loadSource=function e(t){var r=this.isLoading;this.hasLoaded=false;this.isLoading=false;if(r&&this.source){this.source.onload=null;this.source.onerror=null}var i=!this.source;this.source=t;if((t.src&&t.complete||t.getContext)&&t.width&&t.height){this._updateImageType();if(this.imageType==="svg"){this._loadSvgSource()}else{this._sourceLoaded()}if(i){this.emit("loaded",this)}}else if(!t.getContext){this.isLoading=true;var n=this;t.onload=function(){n._updateImageType();t.onload=null;t.onerror=null;if(!n.isLoading){return}n.isLoading=false;n._sourceLoaded();if(n.imageType==="svg"){n._loadSvgSource();return}n.emit("loaded",n)};t.onerror=function(){t.onload=null;t.onerror=null;if(!n.isLoading){return}n.isLoading=false;n.emit("error",n)};if(t.complete&&t.src){t.onload=null;t.onerror=null;if(n.imageType==="svg"){n._loadSvgSource();return}this.isLoading=false;if(t.width&&t.height){this._sourceLoaded();if(r){this.emit("loaded",this)}}else if(r){this.emit("error",this)}}}};t.prototype._updateImageType=function e(){if(!this.imageUrl){return}var t=(0,i.decomposeDataUri)(this.imageUrl);var r=void 0;if(t&&t.mediaType==="image"){var n=t.subType.split("+")[0];r=(0,i.getUrlFileExtension)("."+n);if(!r){throw new Error("Invalid image type in data URI.")}}else{r=(0,i.getUrlFileExtension)(this.imageUrl);if(!r){r="png"}}this.imageType=r};t.prototype._loadSvgSource=function e(){if(this.imageType!=="svg"){return}var t=(0,i.decomposeDataUri)(this.imageUrl);if(t){this._loadSvgSourceUsingDataUri(t)}else{this._loadSvgSourceUsingXhr()}};t.prototype._loadSvgSourceUsingDataUri=function e(t){var r=void 0;if(t.encoding==="base64"){if(!atob){throw new Error("Your browser doesn't support base64 conversions.")}r=atob(t.data)}else{r=t.data}this._loadSvgSourceUsingString(r)};t.prototype._loadSvgSourceUsingXhr=function e(){var t=this;var r=new XMLHttpRequest;r.onload=function(){if(r.readyState!==r.DONE||r.status!==200){throw new Error("Failed to load SVG using XHR.")}t._loadSvgSourceUsingString(r.response)};r.onerror=function(){return t.emit("error",t)};r.open("GET",this.imageUrl,true);r.send()};t.prototype._loadSvgSourceUsingString=function e(r){var n=(0,i.getSvgSize)(r);var a=n.width;var o=n.height;if(!a||!o){throw new Error("The SVG image must have width and height defined (in pixels), canvas API needs them.")}this.realWidth=Math.round(a*this.sourceScale);this.realHeight=Math.round(o*this.sourceScale);this._updateDimensions();var s=document.createElement("canvas");s.width=this.realWidth;s.height=this.realHeight;s._pixiId="canvas_"+(0,i.uid)();s.getContext("2d").drawImage(this.source,0,0,a,o,0,0,this.realWidth,this.realHeight);this.origSource=this.source;this.source=s;t.addToCache(this,s._pixiId);this.isLoading=false;this._sourceLoaded();this.emit("loaded",this)};t.prototype._sourceLoaded=function e(){this.hasLoaded=true;this.update()};t.prototype.destroy=function e(){if(this.imageUrl){delete i.TextureCache[this.imageUrl];this.imageUrl=null;if(!navigator.isCocoonJS){this.source.src=""}}this.source=null;this.dispose();t.removeFromCache(this);this.textureCacheIds=null;this._destroyed=true};t.prototype.dispose=function e(){this.emit("dispose",this)};t.prototype.updateSourceImage=function e(t){this.source.src=t;this.loadSource(this.source)};t.fromImage=function e(r,n,a,o){var s=i.BaseTextureCache[r];if(!s){var u=new Image;if(n===undefined&&r.indexOf("data:")!==0){u.crossOrigin=(0,l.default)(r)}else if(n){u.crossOrigin=typeof n==="string"?n:"anonymous"}s=new t(u,a);s.imageUrl=r;if(o){s.sourceScale=o}s.resolution=(0,i.getResolutionOfUrl)(r);u.src=r;t.addToCache(s,r)}return s};t.fromCanvas=function e(r,n){var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"canvas";if(!r._pixiId){r._pixiId=a+"_"+(0,i.uid)()}var o=i.BaseTextureCache[r._pixiId];if(!o){o=new t(r,n);t.addToCache(o,r._pixiId)}return o};t.from=function e(r,n,a){if(typeof r==="string"){return t.fromImage(r,undefined,n,a)}else if(r instanceof HTMLImageElement){var o=r.src;var s=i.BaseTextureCache[o];if(!s){s=new t(r,n);s.imageUrl=o;if(a){s.sourceScale=a}s.resolution=(0,i.getResolutionOfUrl)(o);t.addToCache(s,o)}return s}else if(r instanceof HTMLCanvasElement){return t.fromCanvas(r,n)}return r};t.addToCache=function e(t,r){if(r){if(t.textureCacheIds.indexOf(r)===-1){t.textureCacheIds.push(r)}if(i.BaseTextureCache[r]){console.warn("BaseTexture added to the cache with an id ["+r+"] that already had an entry")}i.BaseTextureCache[r]=t}};t.removeFromCache=function e(t){if(typeof t==="string"){var r=i.BaseTextureCache[t];if(r){var n=r.textureCacheIds.indexOf(t);if(n>-1){r.textureCacheIds.splice(n,1)}delete i.BaseTextureCache[t];return r}}else if(t&&t.textureCacheIds){for(var a=0;a<t.textureCacheIds.length;++a){delete i.BaseTextureCache[t.textureCacheIds[a]]}t.textureCacheIds.length=0;return t}return null};return t}(s.default);t.default=y},dcb4:function(e,t,r){"use strict";t.__esModule=true;function i(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var n=function(){function e(t){i(this,e);this.vertices=new ArrayBuffer(t);this.float32View=new Float32Array(this.vertices);this.uint32View=new Uint32Array(this.vertices)}e.prototype.destroy=function e(){this.vertices=null;this.positions=null;this.uvs=null;this.colors=null};return e}();t.default=n},dd89:function(e,t,r){"use strict";r("3342");r("b042");r("c676");r("6bb5");if(!window.ArrayBuffer){window.ArrayBuffer=Array}if(!window.Float32Array){window.Float32Array=Array}if(!window.Uint32Array){window.Uint32Array=Array}if(!window.Uint16Array){window.Uint16Array=Array}},debf:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=o(n);function o(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function u(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function l(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var h=function(e){l(t,e);function t(r,i){s(this,t);var n=u(this,e.call(this,r[0]instanceof a.Texture?r[0]:r[0].texture));n._textures=null;n._durations=null;n.textures=r;n._autoUpdate=i!==false;n.animationSpeed=1;n.loop=true;n.updateAnchor=false;n.onComplete=null;n.onFrameChange=null;n.onLoop=null;n._currentTime=0;n.playing=false;return n}t.prototype.stop=function e(){if(!this.playing){return}this.playing=false;if(this._autoUpdate){a.ticker.shared.remove(this.update,this)}};t.prototype.play=function e(){if(this.playing){return}this.playing=true;if(this._autoUpdate){a.ticker.shared.add(this.update,this,a.UPDATE_PRIORITY.HIGH)}};t.prototype.gotoAndStop=function e(t){this.stop();var r=this.currentFrame;this._currentTime=t;if(r!==this.currentFrame){this.updateTexture()}};t.prototype.gotoAndPlay=function e(t){var r=this.currentFrame;this._currentTime=t;if(r!==this.currentFrame){this.updateTexture()}this.play()};t.prototype.update=function e(t){var r=this.animationSpeed*t;var i=this.currentFrame;if(this._durations!==null){var n=this._currentTime%1*this._durations[this.currentFrame];n+=r/60*1e3;while(n<0){this._currentTime--;n+=this._durations[this.currentFrame]}var a=Math.sign(this.animationSpeed*t);this._currentTime=Math.floor(this._currentTime);while(n>=this._durations[this.currentFrame]){n-=this._durations[this.currentFrame]*a;this._currentTime+=a}this._currentTime+=n/this._durations[this.currentFrame]}else{this._currentTime+=r}if(this._currentTime<0&&!this.loop){this.gotoAndStop(0);if(this.onComplete){this.onComplete()}}else if(this._currentTime>=this._textures.length&&!this.loop){this.gotoAndStop(this._textures.length-1);if(this.onComplete){this.onComplete()}}else if(i!==this.currentFrame){if(this.loop&&this.onLoop){if(this.animationSpeed>0&&this.currentFrame<i){this.onLoop()}else if(this.animationSpeed<0&&this.currentFrame>i){this.onLoop()}}this.updateTexture()}};t.prototype.updateTexture=function e(){this._texture=this._textures[this.currentFrame];this._textureID=-1;this.cachedTint=16777215;if(this.updateAnchor){this._anchor.copy(this._texture.defaultAnchor)}if(this.onFrameChange){this.onFrameChange(this.currentFrame)}};t.prototype.destroy=function t(r){this.stop();e.prototype.destroy.call(this,r)};t.fromFrames=function e(r){var i=[];for(var n=0;n<r.length;++n){i.push(a.Texture.fromFrame(r[n]))}return new t(i)};t.fromImages=function e(r){var i=[];for(var n=0;n<r.length;++n){i.push(a.Texture.fromImage(r[n]))}return new t(i)};i(t,[{key:"totalFrames",get:function e(){return this._textures.length}},{key:"textures",get:function e(){return this._textures},set:function e(t){if(t[0]instanceof a.Texture){this._textures=t;this._durations=null}else{this._textures=[];this._durations=[];for(var r=0;r<t.length;r++){this._textures.push(t[r].texture);this._durations.push(t[r].time)}}this.gotoAndStop(0);this.updateTexture()}},{key:"currentFrame",get:function e(){var t=Math.floor(this._currentTime)%this._textures.length;if(t<0){t+=this._textures.length}return t}}]);return t}(a.Sprite);t.default=h},e056:function(e,t,r){"use strict";t.__esModule=true;var i=r("063c");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function u(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var l=function(e){u(t,e);function t(){o(this,t);return s(this,e.apply(this,arguments))}t.prototype.start=function e(){};t.prototype.stop=function e(){this.flush()};t.prototype.flush=function e(){};t.prototype.render=function e(t){};return t}(n.default);t.default=l},e08d:function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=u(i);var a=r("b018");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var h=function(){function e(t){l(this,e);this.renderer=t}e.prototype.render=function e(t){var r=this.renderer;var i=r.context;var n=t.worldTransform;var a=r.resolution;if(r.roundPixels){i.setTransform(n.a*a,n.b*a,n.c*a,n.d*a,n.tx*a|0,n.ty*a|0)}else{i.setTransform(n.a*a,n.b*a,n.c*a,n.d*a,n.tx*a,n.ty*a)}r.context.globalAlpha=t.worldAlpha;r.setBlendMode(t.blendMode);if(t.drawMode===o.default.DRAW_MODES.TRIANGLE_MESH){this._renderTriangleMesh(t)}else{this._renderTriangles(t)}};e.prototype._renderTriangleMesh=function e(t){var r=t.vertices.length/2;for(var i=0;i<r-2;i++){var n=i*2;this._renderDrawTriangle(t,n,n+2,n+4)}};e.prototype._renderTriangles=function e(t){var r=t.indices;var i=r.length;for(var n=0;n<i;n+=3){var a=r[n]*2;var o=r[n+1]*2;var s=r[n+2]*2;this._renderDrawTriangle(t,a,o,s)}};e.prototype._renderDrawTriangle=function e(t,r,i,n){var a=this.renderer.context;var o=t.uvs;var s=t.vertices;var u=t._texture;if(!u.valid){return}var l=u.baseTexture;var h=l.source;var f=l.width;var c=l.height;var d=void 0;var p=void 0;var v=void 0;var y=void 0;var g=void 0;var _=void 0;if(t.uploadUvTransform){var m=t._uvTransform.mapCoord;d=(o[r]*m.a+o[r+1]*m.c+m.tx)*l.width;p=(o[i]*m.a+o[i+1]*m.c+m.tx)*l.width;v=(o[n]*m.a+o[n+1]*m.c+m.tx)*l.width;y=(o[r]*m.b+o[r+1]*m.d+m.ty)*l.height;g=(o[i]*m.b+o[i+1]*m.d+m.ty)*l.height;_=(o[n]*m.b+o[n+1]*m.d+m.ty)*l.height}else{d=o[r]*l.width;p=o[i]*l.width;v=o[n]*l.width;y=o[r+1]*l.height;g=o[i+1]*l.height;_=o[n+1]*l.height}var b=s[r];var x=s[i];var T=s[n];var E=s[r+1];var w=s[i+1];var S=s[n+1];var O=t.canvasPadding/this.renderer.resolution;if(O>0){var M=O/Math.abs(t.worldTransform.a);var P=O/Math.abs(t.worldTransform.d);var C=(b+x+T)/3;var A=(E+w+S)/3;var R=b-C;var I=E-A;var D=Math.sqrt(R*R+I*I);b=C+R/D*(D+M);E=A+I/D*(D+P);R=x-C;I=w-A;D=Math.sqrt(R*R+I*I);x=C+R/D*(D+M);w=A+I/D*(D+P);R=T-C;I=S-A;D=Math.sqrt(R*R+I*I);T=C+R/D*(D+M);S=A+I/D*(D+P)}a.save();a.beginPath();a.moveTo(b,E);a.lineTo(x,w);a.lineTo(T,S);a.closePath();a.clip();var L=d*g+y*v+p*_-g*v-y*p-d*_;var N=b*g+y*T+x*_-g*T-y*x-b*_;var k=d*x+b*v+p*T-x*v-b*p-d*T;var B=d*g*T+y*x*v+b*p*_-b*g*v-y*p*T-d*x*_;var F=E*g+y*S+w*_-g*S-y*w-E*_;var U=d*w+E*v+p*S-w*v-E*p-d*S;var j=d*g*S+y*w*v+E*p*_-E*g*v-y*p*S-d*w*_;a.transform(N/L,F/L,k/L,U/L,B/L,j/L);a.drawImage(h,0,0,f*l.resolution,c*l.resolution,0,0,f,c);a.restore();this.renderer.invalidateBlendMode()};e.prototype.renderMeshFlat=function e(t){var r=this.renderer.context;var i=t.vertices;var n=i.length/2;r.beginPath();for(var a=1;a<n-2;++a){var o=a*2;var s=i[o];var u=i[o+1];var l=i[o+2];var h=i[o+3];var f=i[o+4];var c=i[o+5];r.moveTo(s,u);r.lineTo(l,h);r.lineTo(f,c)}r.fillStyle="#FF0000";r.fill();r.closePath()};e.prototype.destroy=function e(){this.renderer=null};return e}();t.default=h;n.CanvasRenderer.registerPlugin("mesh",h)},e1f8:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("f87a");var a=o(n);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=new a.default;var l=function(){function e(t,r){s(this,e);this._texture=t;this.mapCoord=new a.default;this.uClampFrame=new Float32Array(4);this.uClampOffset=new Float32Array(2);this._lastTextureID=-1;this.clampOffset=0;this.clampMargin=typeof r==="undefined"?.5:r}e.prototype.multiplyUvs=function e(t,r){if(r===undefined){r=t}var i=this.mapCoord;for(var n=0;n<t.length;n+=2){var a=t[n];var o=t[n+1];r[n]=a*i.a+o*i.c+i.tx;r[n+1]=a*i.b+o*i.d+i.ty}return r};e.prototype.update=function e(t){var r=this._texture;if(!r||!r.valid){return false}if(!t&&this._lastTextureID===r._updateID){return false}this._lastTextureID=r._updateID;var i=r._uvs;this.mapCoord.set(i.x1-i.x0,i.y1-i.y0,i.x3-i.x0,i.y3-i.y0,i.x0,i.y0);var n=r.orig;var a=r.trim;if(a){u.set(n.width/a.width,0,0,n.height/a.height,-a.x/a.width,-a.y/a.height);this.mapCoord.append(u)}var o=r.baseTexture;var s=this.uClampFrame;var l=this.clampMargin/o.resolution;var h=this.clampOffset;s[0]=(r._frame.x+l+h)/o.width;s[1]=(r._frame.y+l+h)/o.height;s[2]=(r._frame.x+r._frame.width-l+h)/o.width;s[3]=(r._frame.y+r._frame.height-l+h)/o.height;this.uClampOffset[0]=h/o.realWidth;this.uClampOffset[1]=h/o.realHeight;return true};i(e,[{key:"texture",get:function e(){return this._texture},set:function e(t){this._texture=t;this._lastTextureID=-1}}]);return e}();t.default=l},e268:function(e,t,r){"use strict";t.__esModule=true;t.default={interactive:false,interactiveChildren:true,hitArea:null,get buttonMode(){return this.cursor==="pointer"},set buttonMode(e){if(e){this.cursor="pointer"}else if(this.cursor==="pointer"){this.cursor=null}},cursor:null,get trackedPointers(){if(this._trackedPointers===undefined)this._trackedPointers={};return this._trackedPointers},_trackedPointers:undefined}},e28d:function(e,t,r){"use strict";t.__esModule=true;var i=r("e056");var n=T(i);var a=r("a55e");var o=T(a);var s=r("4f24");var u=T(s);var l=r("c43e");var h=T(l);var f=r("4d16");var c=T(f);var d=r("dcb4");var p=T(d);var v=r("61df");var y=T(v);var g=r("aa9d");var _=r("c88f");var m=T(_);var b=r("a48a");var x=T(b);function T(e){return e&&e.__esModule?e:{default:e}}function E(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function w(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function S(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var O=0;var M=0;var P=function(e){S(t,e);function t(r){E(this,t);var i=w(this,e.call(this,r));i.vertSize=5;i.vertByteSize=i.vertSize*4;i.size=y.default.SPRITE_BATCH_SIZE;i.buffers=[];for(var n=1;n<=x.default.nextPow2(i.size);n*=2){i.buffers.push(new p.default(n*4*i.vertByteSize))}i.indices=(0,u.default)(i.size);i.shader=null;i.currentIndex=0;i.groups=[];for(var a=0;a<i.size;a++){i.groups[a]={textures:[],textureCount:0,ids:[],size:0,start:0,blend:0}}i.sprites=[];i.vertexBuffers=[];i.vaos=[];i.vaoMax=2;i.vertexCount=0;i.renderer.on("prerender",i.onPrerender,i);return i}t.prototype.onContextChange=function e(){var t=this.renderer.gl;if(this.renderer.legacy){this.MAX_TEXTURES=1}else{this.MAX_TEXTURES=Math.min(t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS),y.default.SPRITE_MAX_TEXTURES);this.MAX_TEXTURES=(0,c.default)(this.MAX_TEXTURES,t)}this.shader=(0,h.default)(t,this.MAX_TEXTURES);this.indexBuffer=m.default.GLBuffer.createIndexBuffer(t,this.indices,t.STATIC_DRAW);this.renderer.bindVao(null);var r=this.shader.attributes;for(var i=0;i<this.vaoMax;i++){var n=this.vertexBuffers[i]=m.default.GLBuffer.createVertexBuffer(t,null,t.STREAM_DRAW);var a=this.renderer.createVao().addIndex(this.indexBuffer).addAttribute(n,r.aVertexPosition,t.FLOAT,false,this.vertByteSize,0).addAttribute(n,r.aTextureCoord,t.UNSIGNED_SHORT,true,this.vertByteSize,2*4).addAttribute(n,r.aColor,t.UNSIGNED_BYTE,true,this.vertByteSize,3*4);if(r.aTextureId){a.addAttribute(n,r.aTextureId,t.FLOAT,false,this.vertByteSize,4*4)}this.vaos[i]=a}this.vao=this.vaos[0];this.currentBlendMode=99999;this.boundTextures=new Array(this.MAX_TEXTURES)};t.prototype.onPrerender=function e(){this.vertexCount=0};t.prototype.render=function e(t){if(this.currentIndex>=this.size){this.flush()}if(!t._texture._uvs){return}this.sprites[this.currentIndex++]=t};t.prototype.flush=function e(){if(this.currentIndex===0){return}var t=this.renderer.gl;var r=this.MAX_TEXTURES;var i=x.default.nextPow2(this.currentIndex);var n=x.default.log2(i);var a=this.buffers[n];var o=this.sprites;var s=this.groups;var u=a.float32View;var l=a.uint32View;var h=this.boundTextures;var f=this.renderer.boundTextures;var c=this.renderer.textureGC.count;var d=0;var p=void 0;var v=void 0;var _=1;var b=0;var T=s[0];var E=void 0;var w=void 0;var S=g.premultiplyBlendMode[o[0]._texture.baseTexture.premultipliedAlpha?1:0][o[0].blendMode];T.textureCount=0;T.start=0;T.blend=S;O++;var P=void 0;for(P=0;P<r;++P){var C=f[P];if(C._enabled===O){h[P]=this.renderer.emptyTextures[P];continue}h[P]=C;C._virtalBoundId=P;C._enabled=O}O++;for(P=0;P<this.currentIndex;++P){var A=o[P];o[P]=null;p=A._texture.baseTexture;var R=g.premultiplyBlendMode[Number(p.premultipliedAlpha)][A.blendMode];if(S!==R){S=R;v=null;b=r;O++}if(v!==p){v=p;if(p._enabled!==O){if(b===r){O++;T.size=P-T.start;b=0;T=s[_++];T.blend=S;T.textureCount=0;T.start=P}p.touched=c;if(p._virtalBoundId===-1){for(var I=0;I<r;++I){var D=(I+M)%r;var L=h[D];if(L._enabled!==O){M++;L._virtalBoundId=-1;p._virtalBoundId=D;h[D]=p;break}}}p._enabled=O;T.textureCount++;T.ids[b]=p._virtalBoundId;T.textures[b++]=p}}E=A.vertexData;w=A._texture._uvs.uvsUint32;if(this.renderer.roundPixels){var N=this.renderer.resolution;u[d]=(E[0]*N|0)/N;u[d+1]=(E[1]*N|0)/N;u[d+5]=(E[2]*N|0)/N;u[d+6]=(E[3]*N|0)/N;u[d+10]=(E[4]*N|0)/N;u[d+11]=(E[5]*N|0)/N;u[d+15]=(E[6]*N|0)/N;u[d+16]=(E[7]*N|0)/N}else{u[d]=E[0];u[d+1]=E[1];u[d+5]=E[2];u[d+6]=E[3];u[d+10]=E[4];u[d+11]=E[5];u[d+15]=E[6];u[d+16]=E[7]}l[d+2]=w[0];l[d+7]=w[1];l[d+12]=w[2];l[d+17]=w[3];var k=Math.min(A.worldAlpha,1);var B=k<1&&p.premultipliedAlpha?(0,g.premultiplyTint)(A._tintRGB,k):A._tintRGB+(k*255<<24);l[d+3]=l[d+8]=l[d+13]=l[d+18]=B;u[d+4]=u[d+9]=u[d+14]=u[d+19]=p._virtalBoundId;d+=20}T.size=P-T.start;if(!y.default.CAN_UPLOAD_SAME_BUFFER){if(this.vaoMax<=this.vertexCount){this.vaoMax++;var F=this.shader.attributes;var U=this.vertexBuffers[this.vertexCount]=m.default.GLBuffer.createVertexBuffer(t,null,t.STREAM_DRAW);var j=this.renderer.createVao().addIndex(this.indexBuffer).addAttribute(U,F.aVertexPosition,t.FLOAT,false,this.vertByteSize,0).addAttribute(U,F.aTextureCoord,t.UNSIGNED_SHORT,true,this.vertByteSize,2*4).addAttribute(U,F.aColor,t.UNSIGNED_BYTE,true,this.vertByteSize,3*4);if(F.aTextureId){j.addAttribute(U,F.aTextureId,t.FLOAT,false,this.vertByteSize,4*4)}this.vaos[this.vertexCount]=j}this.renderer.bindVao(this.vaos[this.vertexCount]);this.vertexBuffers[this.vertexCount].upload(a.vertices,0,false);this.vertexCount++}else{this.vertexBuffers[this.vertexCount].upload(a.vertices,0,true)}for(P=0;P<r;++P){f[P]._virtalBoundId=-1}for(P=0;P<_;++P){var X=s[P];var H=X.textureCount;for(var G=0;G<H;G++){v=X.textures[G];if(f[X.ids[G]]!==v){this.renderer.bindTexture(v,X.ids[G],true)}v._virtalBoundId=-1}this.renderer.state.setBlendMode(X.blend);t.drawElements(t.TRIANGLES,X.size*6,t.UNSIGNED_SHORT,X.start*6*2)}this.currentIndex=0};t.prototype.start=function e(){this.renderer.bindShader(this.shader);if(y.default.CAN_UPLOAD_SAME_BUFFER){this.renderer.bindVao(this.vaos[this.vertexCount]);this.vertexBuffers[this.vertexCount].bind()}};t.prototype.stop=function e(){this.flush()};t.prototype.destroy=function t(){for(var r=0;r<this.vaoMax;r++){if(this.vertexBuffers[r]){this.vertexBuffers[r].destroy()}if(this.vaos[r]){this.vaos[r].destroy()}}if(this.indexBuffer){this.indexBuffer.destroy()}this.renderer.off("prerender",this.onPrerender,this);e.prototype.destroy.call(this);if(this.shader){this.shader.destroy();this.shader=null}this.vertexBuffers=null;this.vaos=null;this.indexBuffer=null;this.indices=null;this.sprites=null;for(var i=0;i<this.buffers.length;++i){this.buffers[i].destroy()}};return t}(n.default);t.default=P;o.default.registerPlugin("sprite",P)},e397:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("dc07");var a=o(n);function o(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){function e(){s(this,e);this.global=new a.Point;this.target=null;this.originalEvent=null;this.identifier=null;this.isPrimary=false;this.button=0;this.buttons=0;this.width=0;this.height=0;this.tiltX=0;this.tiltY=0;this.pointerType=null;this.pressure=0;this.rotationAngle=0;this.twist=0;this.tangentialPressure=0}e.prototype.getLocalPosition=function e(t,r,i){return t.worldTransform.applyInverse(i||this.global,r)};e.prototype.copyEvent=function e(t){if(t.isPrimary){this.isPrimary=true}this.button=t.button;this.buttons=Number.isInteger(t.buttons)?t.buttons:t.which;this.width=t.width;this.height=t.height;this.tiltX=t.tiltX;this.tiltY=t.tiltY;this.pointerType=t.pointerType;this.pressure=t.pressure;this.rotationAngle=t.rotationAngle;this.twist=t.twist||0;this.tangentialPressure=t.tangentialPressure||0};e.prototype.reset=function e(){this.isPrimary=false};i(e,[{key:"pointerId",get:function e(){return this.identifier}}]);return e}();t.default=u},e4ec:function(e,t,r){"use strict";t.__esModule=true;t.default=function(e,t,r){if(e.nativeLines){o(e,r)}else{a(e,t)}};var i=r("774e");var n=r("aa9d");function a(e,t){var r=e.points;if(r.length===0){return}var a=new i.Point(r[0],r[1]);var o=new i.Point(r[r.length-2],r[r.length-1]);if(a.x===o.x&&a.y===o.y){r=r.slice();r.pop();r.pop();o=new i.Point(r[r.length-2],r[r.length-1]);var s=o.x+(a.x-o.x)*.5;var u=o.y+(a.y-o.y)*.5;r.unshift(s,u);r.push(s,u)}var l=t.points;var h=t.indices;var f=r.length/2;var c=r.length;var d=l.length/6;var p=e.lineWidth/2;var v=(0,n.hex2rgb)(e.lineColor);var y=e.lineAlpha;var g=v[0]*y;var _=v[1]*y;var m=v[2]*y;var b=r[0];var x=r[1];var T=r[2];var E=r[3];var w=0;var S=0;var O=-(x-E);var M=b-T;var P=0;var C=0;var A=0;var R=0;var I=Math.sqrt(O*O+M*M);O/=I;M/=I;O*=p;M*=p;var D=e.lineAlignment;var L=(1-D)*2;var N=D*2;l.push(b-O*L,x-M*L,g,_,m,y);l.push(b+O*N,x+M*N,g,_,m,y);for(var k=1;k<f-1;++k){b=r[(k-1)*2];x=r[(k-1)*2+1];T=r[k*2];E=r[k*2+1];w=r[(k+1)*2];S=r[(k+1)*2+1];O=-(x-E);M=b-T;I=Math.sqrt(O*O+M*M);O/=I;M/=I;O*=p;M*=p;P=-(E-S);C=T-w;I=Math.sqrt(P*P+C*C);P/=I;C/=I;P*=p;C*=p;var B=-M+x-(-M+E);var F=-O+T-(-O+b);var U=(-O+b)*(-M+E)-(-O+T)*(-M+x);var j=-C+S-(-C+E);var X=-P+T-(-P+w);var H=(-P+w)*(-C+E)-(-P+T)*(-C+S);var G=B*X-j*F;if(Math.abs(G)<.1){G+=10.1;l.push(T-O*L,E-M*L,g,_,m,y);l.push(T+O*N,E+M*N,g,_,m,y);continue}var W=(F*H-X*U)/G;var Y=(j*U-B*H)/G;var V=(W-T)*(W-T)+(Y-E)*(Y-E);if(V>196*p*p){A=O-P;R=M-C;I=Math.sqrt(A*A+R*R);A/=I;R/=I;A*=p;R*=p;l.push(T-A*L,E-R*L);l.push(g,_,m,y);l.push(T+A*N,E+R*N);l.push(g,_,m,y);l.push(T-A*N*L,E-R*L);l.push(g,_,m,y);c++}else{l.push(T+(W-T)*L,E+(Y-E)*L);l.push(g,_,m,y);l.push(T-(W-T)*N,E-(Y-E)*N);l.push(g,_,m,y)}}b=r[(f-2)*2];x=r[(f-2)*2+1];T=r[(f-1)*2];E=r[(f-1)*2+1];O=-(x-E);M=b-T;I=Math.sqrt(O*O+M*M);O/=I;M/=I;O*=p;M*=p;l.push(T-O*L,E-M*L);l.push(g,_,m,y);l.push(T+O*N,E+M*N);l.push(g,_,m,y);h.push(d);for(var z=0;z<c;++z){h.push(d++)}h.push(d-1)}function o(e,t){var r=0;var i=e.points;if(i.length===0)return;var a=t.points;var o=i.length/2;var s=(0,n.hex2rgb)(e.lineColor);var u=e.lineAlpha;var l=s[0]*u;var h=s[1]*u;var f=s[2]*u;for(r=1;r<o;r++){var c=i[(r-1)*2];var d=i[(r-1)*2+1];var p=i[r*2];var v=i[r*2+1];a.push(c,d);a.push(l,h,f,u);a.push(p,v);a.push(l,h,f,u)}}},e5d3:function(e,t,r){"use strict";t.__esModule=true;var i=r("6bf3");var n=T(i);var a=r("7881");var o=T(a);var s=r("c49b");var u=T(s);var l=r("00f4");var h=T(l);var f=r("8d00");var c=T(f);var d=r("774e");var p=r("aa9d");var v=r("a506");var y=r("b008");var g=T(y);var _=r("8c4d");var m=T(_);var b=r("a557");var x=T(b);function T(e){return e&&e.__esModule?e:{default:e}}function E(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function w(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function S(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var O=void 0;var M=new d.Matrix;var P=new d.Point;var C=new Float32Array(4);var A=new Float32Array(4);var R=function(e){S(t,e);function t(){var r=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;E(this,t);var i=w(this,e.call(this));i.fillAlpha=1;i.lineWidth=0;i.nativeLines=r;i.lineColor=0;i.lineAlignment=.5;i.graphicsData=[];i.tint=16777215;i._prevTint=16777215;i.blendMode=v.BLEND_MODES.NORMAL;i.currentPath=null;i._webGL={};i.isMask=false;i.boundsPadding=0;i._localBounds=new g.default;i.dirty=0;i.fastRectDirty=-1;i.clearDirty=0;i.boundsDirty=-1;i.cachedSpriteDirty=false;i._spriteRect=null;i._fastRect=false;i._prevRectTint=null;i._prevRectFillColor=null;return i}t.prototype.clone=function e(){var e=new t;e.renderable=this.renderable;e.fillAlpha=this.fillAlpha;e.lineWidth=this.lineWidth;e.lineColor=this.lineColor;e.lineAlignment=this.lineAlignment;e.tint=this.tint;e.blendMode=this.blendMode;e.isMask=this.isMask;e.boundsPadding=this.boundsPadding;e.dirty=0;e.cachedSpriteDirty=this.cachedSpriteDirty;for(var r=0;r<this.graphicsData.length;++r){e.graphicsData.push(this.graphicsData[r].clone())}e.currentPath=e.graphicsData[e.graphicsData.length-1];e.updateLocalBounds();return e};t.prototype._quadraticCurveLength=function e(t,r,i,n,a,o){var s=t-2*i+a;var u=r-2*n+o;var l=2*i-2*t;var h=2*n-2*r;var f=4*(s*s+u*u);var c=4*(s*l+u*h);var d=l*l+h*h;var p=2*Math.sqrt(f+c+d);var v=Math.sqrt(f);var y=2*f*v;var g=2*Math.sqrt(d);var _=c/v;return(y*p+v*c*(p-g)+(4*d*f-c*c)*Math.log((2*v+_+p)/(_+g)))/(4*y)};t.prototype._bezierCurveLength=function e(t,r,i,n,a,o,s,u){var l=10;var h=0;var f=0;var c=0;var d=0;var p=0;var v=0;var y=0;var g=0;var _=0;var m=0;var b=0;var x=t;var T=r;for(var E=1;E<=l;++E){f=E/l;c=f*f;d=c*f;p=1-f;v=p*p;y=v*p;g=y*t+3*v*f*i+3*p*c*a+d*s;_=y*r+3*v*f*n+3*p*c*o+d*u;m=x-g;b=T-_;x=g;T=_;h+=Math.sqrt(m*m+b*b)}return h};t.prototype._segmentsCount=function e(r){var i=Math.ceil(r/t.CURVES.maxLength);if(i<t.CURVES.minSegments){i=t.CURVES.minSegments}else if(i>t.CURVES.maxSegments){i=t.CURVES.maxSegments}return i};t.prototype.lineStyle=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:.5;this.lineWidth=t;this.lineColor=r;this.lineAlpha=i;this.lineAlignment=n;if(this.currentPath){if(this.currentPath.shape.points.length){var a=new d.Polygon(this.currentPath.shape.points.slice(-2));a.closed=false;this.drawShape(a)}else{this.currentPath.lineWidth=this.lineWidth;this.currentPath.lineColor=this.lineColor;this.currentPath.lineAlpha=this.lineAlpha;this.currentPath.lineAlignment=this.lineAlignment}}return this};t.prototype.moveTo=function e(t,r){var i=new d.Polygon([t,r]);i.closed=false;this.drawShape(i);return this};t.prototype.lineTo=function e(t,r){var i=this.currentPath.shape.points;var n=i[i.length-2];var a=i[i.length-1];if(n!==t||a!==r){i.push(t,r);this.dirty++}return this};t.prototype.quadraticCurveTo=function e(r,i,n,a){if(this.currentPath){if(this.currentPath.shape.points.length===0){this.currentPath.shape.points=[0,0]}}else{this.moveTo(0,0)}var o=this.currentPath.shape.points;var s=0;var u=0;if(o.length===0){this.moveTo(0,0)}var l=o[o.length-2];var h=o[o.length-1];var f=t.CURVES.adaptive?this._segmentsCount(this._quadraticCurveLength(l,h,r,i,n,a)):20;for(var c=1;c<=f;++c){var d=c/f;s=l+(r-l)*d;u=h+(i-h)*d;o.push(s+(r+(n-r)*d-s)*d,u+(i+(a-i)*d-u)*d)}this.dirty++;return this};t.prototype.bezierCurveTo=function e(r,i,n,a,o,s){if(this.currentPath){if(this.currentPath.shape.points.length===0){this.currentPath.shape.points=[0,0]}}else{this.moveTo(0,0)}var u=this.currentPath.shape.points;var l=u[u.length-2];var h=u[u.length-1];u.length-=2;var f=t.CURVES.adaptive?this._segmentsCount(this._bezierCurveLength(l,h,r,i,n,a,o,s)):20;(0,m.default)(l,h,r,i,n,a,o,s,f,u);this.dirty++;return this};t.prototype.arcTo=function e(t,r,i,n,a){if(this.currentPath){if(this.currentPath.shape.points.length===0){this.currentPath.shape.points.push(t,r)}}else{this.moveTo(t,r)}var o=this.currentPath.shape.points;var s=o[o.length-2];var u=o[o.length-1];var l=u-r;var h=s-t;var f=n-r;var c=i-t;var d=Math.abs(l*c-h*f);if(d<1e-8||a===0){if(o[o.length-2]!==t||o[o.length-1]!==r){o.push(t,r)}}else{var p=l*l+h*h;var v=f*f+c*c;var y=l*f+h*c;var g=a*Math.sqrt(p)/d;var _=a*Math.sqrt(v)/d;var m=g*y/p;var b=_*y/v;var x=g*c+_*h;var T=g*f+_*l;var E=h*(_+m);var w=l*(_+m);var S=c*(g+b);var O=f*(g+b);var M=Math.atan2(w-T,E-x);var P=Math.atan2(O-T,S-x);this.arc(x+t,T+r,a,M,P,h*f>c*l)}this.dirty++;return this};t.prototype.arc=function e(r,i,n,a,o){var s=arguments.length>5&&arguments[5]!==undefined?arguments[5]:false;if(a===o){return this}if(!s&&o<=a){o+=v.PI_2}else if(s&&a<=o){a+=v.PI_2}var u=o-a;var l=t.CURVES.adaptive?this._segmentsCount(Math.abs(u)*n):Math.ceil(Math.abs(u)/v.PI_2)*40;if(u===0){return this}var h=r+Math.cos(a)*n;var f=i+Math.sin(a)*n;var c=this.currentPath?this.currentPath.shape.points:null;if(c){var d=Math.abs(c[c.length-2]-h);var p=Math.abs(c[c.length-1]-f);if(d<.001&&p<.001){}else{c.push(h,f)}}else{this.moveTo(h,f);c=this.currentPath.shape.points}var y=u/(l*2);var g=y*2;var _=Math.cos(y);var m=Math.sin(y);var b=l-1;var x=b%1/b;for(var T=0;T<=b;++T){var E=T+x*T;var w=y+a+g*E;var S=Math.cos(w);var O=-Math.sin(w);c.push((_*S+m*O)*n+r,(_*-O+m*S)*n+i)}this.dirty++;return this};t.prototype.beginFill=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;this.filling=true;this.fillColor=t;this.fillAlpha=r;if(this.currentPath){if(this.currentPath.shape.points.length<=2){this.currentPath.fill=this.filling;this.currentPath.fillColor=this.fillColor;this.currentPath.fillAlpha=this.fillAlpha}}return this};t.prototype.endFill=function e(){this.filling=false;this.fillColor=null;this.fillAlpha=1;return this};t.prototype.drawRect=function e(t,r,i,n){this.drawShape(new d.Rectangle(t,r,i,n));return this};t.prototype.drawRoundedRect=function e(t,r,i,n,a){this.drawShape(new d.RoundedRectangle(t,r,i,n,a));return this};t.prototype.drawCircle=function e(t,r,i){this.drawShape(new d.Circle(t,r,i));return this};t.prototype.drawEllipse=function e(t,r,i,n){this.drawShape(new d.Ellipse(t,r,i,n));return this};t.prototype.drawPolygon=function e(t){var r=t;var i=true;if(r instanceof d.Polygon){i=r.closed;r=r.points}if(!Array.isArray(r)){r=new Array(arguments.length);for(var n=0;n<r.length;++n){r[n]=arguments[n]}}var a=new d.Polygon(r);a.closed=i;this.drawShape(a);return this};t.prototype.drawStar=function e(t,r,i,n,a){var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;a=a||n/2;var s=-1*Math.PI/2+o;var u=i*2;var l=v.PI_2/u;var h=[];for(var f=0;f<u;f++){var c=f%2?a:n;var d=f*l+s;h.push(t+c*Math.cos(d),r+c*Math.sin(d))}return this.drawPolygon(h)};t.prototype.clear=function e(){if(this.lineWidth||this.filling||this.graphicsData.length>0){this.lineWidth=0;this.lineAlignment=.5;this.filling=false;this.boundsDirty=-1;this.canvasTintDirty=-1;this.dirty++;this.clearDirty++;this.graphicsData.length=0}this.currentPath=null;this._spriteRect=null;return this};t.prototype.isFastRect=function e(){return this.graphicsData.length===1&&this.graphicsData[0].shape.type===v.SHAPES.RECT&&!this.graphicsData[0].lineWidth};t.prototype._renderWebGL=function e(t){if(this.dirty!==this.fastRectDirty){this.fastRectDirty=this.dirty;this._fastRect=this.isFastRect()}if(this._fastRect){this._renderSpriteRect(t)}else{t.setObjectRenderer(t.plugins.graphics);t.plugins.graphics.render(this)}};t.prototype._renderSpriteRect=function e(t){var r=this.graphicsData[0].shape;if(!this._spriteRect){this._spriteRect=new c.default(new u.default(u.default.WHITE))}var i=this._spriteRect;var n=this.graphicsData[0].fillColor;if(this.tint===16777215){i.tint=n}else if(this.tint!==this._prevRectTint||n!==this._prevRectFillColor){var a=C;var o=A;(0,p.hex2rgb)(n,a);(0,p.hex2rgb)(this.tint,o);a[0]*=o[0];a[1]*=o[1];a[2]*=o[2];i.tint=(0,p.rgb2hex)(a);this._prevRectTint=this.tint;this._prevRectFillColor=n}i.alpha=this.graphicsData[0].fillAlpha;i.worldAlpha=this.worldAlpha*i.alpha;i.blendMode=this.blendMode;i._texture._frame.width=r.width;i._texture._frame.height=r.height;i.transform.worldTransform=this.transform.worldTransform;i.anchor.set(-r.x/r.width,-r.y/r.height);i._onAnchorUpdate();i._renderWebGL(t)};t.prototype._renderCanvas=function e(t){if(this.isMask===true){return}t.plugins.graphics.render(this)};t.prototype._calculateBounds=function e(){if(this.boundsDirty!==this.dirty){this.boundsDirty=this.dirty;this.updateLocalBounds();this.cachedSpriteDirty=true}var t=this._localBounds;this._bounds.addFrame(this.transform,t.minX,t.minY,t.maxX,t.maxY)};t.prototype.containsPoint=function e(t){this.worldTransform.applyInverse(t,P);var r=this.graphicsData;for(var i=0;i<r.length;++i){var n=r[i];if(!n.fill){continue}if(n.shape){if(n.shape.contains(P.x,P.y)){if(n.holes){for(var a=0;a<n.holes.length;a++){var o=n.holes[a];if(o.contains(P.x,P.y)){return false}}}return true}}}return false};t.prototype.updateLocalBounds=function e(){var t=Infinity;var r=-Infinity;var i=Infinity;var n=-Infinity;if(this.graphicsData.length){var a=0;var o=0;var s=0;var u=0;var l=0;for(var h=0;h<this.graphicsData.length;h++){var f=this.graphicsData[h];var c=f.type;var d=f.lineWidth;var p=f.lineAlignment;var y=d*p;a=f.shape;if(c===v.SHAPES.RECT||c===v.SHAPES.RREC){o=a.x-y;s=a.y-y;u=a.width+y*2;l=a.height+y*2;t=o<t?o:t;r=o+u>r?o+u:r;i=s<i?s:i;n=s+l>n?s+l:n}else if(c===v.SHAPES.CIRC){o=a.x;s=a.y;u=a.radius+y;l=a.radius+y;t=o-u<t?o-u:t;r=o+u>r?o+u:r;i=s-l<i?s-l:i;n=s+l>n?s+l:n}else if(c===v.SHAPES.ELIP){o=a.x;s=a.y;u=a.width+y;l=a.height+y;t=o-u<t?o-u:t;r=o+u>r?o+u:r;i=s-l<i?s-l:i;n=s+l>n?s+l:n}else{var g=a.points;var _=0;var m=0;var b=0;var x=0;var T=0;var E=0;var w=0;var S=0;for(var O=0;O+2<g.length;O+=2){o=g[O];s=g[O+1];_=g[O+2];m=g[O+3];b=Math.abs(_-o);x=Math.abs(m-s);l=y*2;u=Math.sqrt(b*b+x*x);if(u<1e-9){continue}T=(l/u*x+b)/2;E=(l/u*b+x)/2;w=(_+o)/2;S=(m+s)/2;t=w-T<t?w-T:t;r=w+T>r?w+T:r;i=S-E<i?S-E:i;n=S+E>n?S+E:n}}}}else{t=0;r=0;i=0;n=0}var M=this.boundsPadding;this._localBounds.minX=t-M;this._localBounds.maxX=r+M;this._localBounds.minY=i-M;this._localBounds.maxY=n+M};t.prototype.drawShape=function e(t){if(this.currentPath){if(this.currentPath.shape.points.length<=2){this.graphicsData.pop()}}this.currentPath=null;var r=new h.default(this.lineWidth,this.lineColor,this.lineAlpha,this.fillColor,this.fillAlpha,this.filling,this.nativeLines,t,this.lineAlignment);this.graphicsData.push(r);if(r.type===v.SHAPES.POLY){r.shape.closed=r.shape.closed;this.currentPath=r}this.dirty++;return r};t.prototype.generateCanvasTexture=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;var i=this.getLocalBounds();var n=o.default.create(i.width,i.height,t,r);if(!O){O=new x.default}this.transform.updateLocalTransform();this.transform.localTransform.copy(M);M.invert();M.tx-=i.x;M.ty-=i.y;O.render(this,n,true,M);var a=u.default.fromCanvas(n.baseTexture._canvasRenderTarget.canvas,t,"graphics");a.baseTexture.resolution=r;a.baseTexture.update();return a};t.prototype.closePath=function e(){var t=this.currentPath;if(t&&t.shape){t.shape.close()}return this};t.prototype.addHole=function e(){var t=this.graphicsData.pop();this.currentPath=this.graphicsData[this.graphicsData.length-1];this.currentPath.addHole(t.shape);this.currentPath=null;return this};t.prototype.destroy=function t(r){e.prototype.destroy.call(this,r);for(var i=0;i<this.graphicsData.length;++i){this.graphicsData[i].destroy()}for(var n in this._webGL){for(var a=0;a<this._webGL[n].data.length;++a){this._webGL[n].data[a].destroy()}}if(this._spriteRect){this._spriteRect.destroy()}this.graphicsData=null;this.currentPath=null;this._webGL=null;this._localBounds=null};return t}(n.default);t.default=R;R._SPRITE_TEXTURE=null;R.CURVES={adaptive:false,maxLength:10,minSegments:8,maxSegments:2048}},e5e9:function(e,t,r){"use strict";t.__esModule=true;var i=r("4edd");Object.defineProperty(t,"webgl",{enumerable:true,get:function e(){return a(i).default}});var n=r("dbac");Object.defineProperty(t,"canvas",{enumerable:true,get:function e(){return a(n).default}});function a(e){return e&&e.__esModule?e:{default:e}}},ec06:function(e,t,r){var i=r("9280");var n=r("ba33");var a=function(e,t){var r={};var a=e.getProgramParameter(t,e.ACTIVE_ATTRIBUTES);for(var s=0;s<a;s++){var u=e.getActiveAttrib(t,s);var l=i(e,u.type);r[u.name]={type:l,size:n(l),location:e.getAttribLocation(t,u.name),pointer:o}}return r};var o=function(e,t,r,i){gl.vertexAttribPointer(this.location,this.size,e||gl.FLOAT,t||false,r||0,i||0)};e.exports=a},ec94:function(e,t,r){"use strict";t.__esModule=true;var i=r("aa9d");var n=r("a506");var a=r("e056");var o=x(a);var s=r("a55e");var u=x(s);var l=r("7ce1");var h=x(l);var f=r("0ee1");var c=x(f);var d=r("2af6");var p=x(d);var v=r("3232");var y=x(v);var g=r("c98e");var _=x(g);var m=r("6aaf");var b=x(m);function x(e){return e&&e.__esModule?e:{default:e}}function T(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function E(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function w(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var S=function(e){w(t,e);function t(r){T(this,t);var i=E(this,e.call(this,r));i.graphicsDataPool=[];i.primitiveShader=null;i.gl=r.gl;i.CONTEXT_UID=0;return i}t.prototype.onContextChange=function e(){this.gl=this.renderer.gl;this.CONTEXT_UID=this.renderer.CONTEXT_UID;this.primitiveShader=new c.default(this.gl)};t.prototype.destroy=function e(){o.default.prototype.destroy.call(this);for(var t=0;t<this.graphicsDataPool.length;++t){this.graphicsDataPool[t].destroy()}this.graphicsDataPool=null};t.prototype.render=function e(t){var r=this.renderer;var n=r.gl;var a=void 0;var o=t._webGL[this.CONTEXT_UID];if(!o||t.dirty!==o.dirty){this.updateGraphics(t);o=t._webGL[this.CONTEXT_UID]}var s=this.primitiveShader;r.bindShader(s);r.state.setBlendMode(t.blendMode);for(var u=0,l=o.data.length;u<l;u++){a=o.data[u];var h=a.shader;r.bindShader(h);h.uniforms.translationMatrix=t.transform.worldTransform.toArray(true);h.uniforms.tint=(0,i.hex2rgb)(t.tint);h.uniforms.alpha=t.worldAlpha;r.bindVao(a.vao);if(a.nativeLines){n.drawArrays(n.LINES,0,a.points.length/6)}else{a.vao.draw(n.TRIANGLE_STRIP,a.indices.length)}}};t.prototype.updateGraphics=function e(t){var r=this.renderer.gl;var i=t._webGL[this.CONTEXT_UID];if(!i){i=t._webGL[this.CONTEXT_UID]={lastIndex:0,data:[],gl:r,clearDirty:-1,dirty:-1}}i.dirty=t.dirty;if(t.clearDirty!==i.clearDirty){i.clearDirty=t.clearDirty;for(var a=0;a<i.data.length;a++){this.graphicsDataPool.push(i.data[a])}i.data.length=0;i.lastIndex=0}var o=void 0;var s=void 0;for(var u=i.lastIndex;u<t.graphicsData.length;u++){var l=t.graphicsData[u];o=this.getWebGLData(i,0);if(l.nativeLines&&l.lineWidth){s=this.getWebGLData(i,0,true);i.lastIndex++}if(l.type===n.SHAPES.POLY){(0,p.default)(l,o,s)}if(l.type===n.SHAPES.RECT){(0,y.default)(l,o,s)}else if(l.type===n.SHAPES.CIRC||l.type===n.SHAPES.ELIP){(0,b.default)(l,o,s)}else if(l.type===n.SHAPES.RREC){(0,_.default)(l,o,s)}i.lastIndex++}this.renderer.bindVao(null);for(var h=0;h<i.data.length;h++){o=i.data[h];if(o.dirty){o.upload()}}};t.prototype.getWebGLData=function e(t,r,i){var n=t.data[t.data.length-1];if(!n||n.nativeLines!==i||n.points.length>32e4){n=this.graphicsDataPool.pop()||new h.default(this.renderer.gl,this.primitiveShader,this.renderer.state.attribsState);n.nativeLines=i;n.reset(r);t.data.push(n)}n.dirty=true;return n};return t}(o.default);t.default=S;u.default.registerPlugin("graphics",S)},f16a:function(e,t,r){"use strict";t.__esModule=true;var i=r("774e");var n=r("a506");var a=r("61df");var o=u(a);var s=r("c88f");function u(e){return e&&e.__esModule?e:{default:e}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var h=function(){function e(t,r,a,u,h,f){l(this,e);this.gl=t;this.frameBuffer=null;this.texture=null;this.clearColor=[0,0,0,0];this.size=new i.Rectangle(0,0,1,1);this.resolution=h||o.default.RESOLUTION;this.projectionMatrix=new i.Matrix;this.transform=null;this.frame=null;this.defaultFrame=new i.Rectangle;this.destinationFrame=null;this.sourceFrame=null;this.stencilBuffer=null;this.stencilMaskStack=[];this.filterData=null;this.filterPoolKey="";this.scaleMode=u!==undefined?u:o.default.SCALE_MODE;this.root=f||false;if(!this.root){this.frameBuffer=s.GLFramebuffer.createRGBA(t,100,100);if(this.scaleMode===n.SCALE_MODES.NEAREST){this.frameBuffer.texture.enableNearestScaling()}else{this.frameBuffer.texture.enableLinearScaling()}this.texture=this.frameBuffer.texture}else{this.frameBuffer=new s.GLFramebuffer(t,100,100);this.frameBuffer.framebuffer=null}this.setFrame();this.resize(r,a)}e.prototype.clear=function e(t){var r=t||this.clearColor;this.frameBuffer.clear(r[0],r[1],r[2],r[3])};e.prototype.attachStencilBuffer=function e(){if(!this.root){this.frameBuffer.enableStencil()}};e.prototype.setFrame=function e(t,r){this.destinationFrame=t||this.destinationFrame||this.defaultFrame;this.sourceFrame=r||this.sourceFrame||this.destinationFrame};e.prototype.activate=function e(){var t=this.gl;this.frameBuffer.bind();this.calculateProjection(this.destinationFrame,this.sourceFrame);if(this.transform){this.projectionMatrix.append(this.transform)}if(this.destinationFrame!==this.sourceFrame){t.enable(t.SCISSOR_TEST);t.scissor(this.destinationFrame.x|0,this.destinationFrame.y|0,this.destinationFrame.width*this.resolution|0,this.destinationFrame.height*this.resolution|0)}else{t.disable(t.SCISSOR_TEST)}t.viewport(this.destinationFrame.x|0,this.destinationFrame.y|0,this.destinationFrame.width*this.resolution|0,this.destinationFrame.height*this.resolution|0)};e.prototype.calculateProjection=function e(t,r){var i=this.projectionMatrix;r=r||t;i.identity();if(!this.root){i.a=1/t.width*2;i.d=1/t.height*2;i.tx=-1-r.x*i.a;i.ty=-1-r.y*i.d}else{i.a=1/t.width*2;i.d=-1/t.height*2;i.tx=-1-r.x*i.a;i.ty=1-r.y*i.d}};e.prototype.resize=function e(t,r){t=t|0;r=r|0;if(this.size.width===t&&this.size.height===r){return}this.size.width=t;this.size.height=r;this.defaultFrame.width=t;this.defaultFrame.height=r;this.frameBuffer.resize(t*this.resolution,r*this.resolution);var i=this.frame||this.size;this.calculateProjection(i)};e.prototype.destroy=function e(){if(this.frameBuffer.stencil){this.gl.deleteRenderbuffer(this.frameBuffer.stencil)}this.frameBuffer.destroy();this.frameBuffer=null;this.texture=null};return e}();t.default=h},f1e4:function(e,t,r){"use strict";var i=r("dc07");var n=a(i);function a(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}n.DisplayObject.prototype.getGlobalPosition=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:new n.Point;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;if(this.parent){this.parent.toGlobal(this.position,t,r)}else{t.x=this.position.x;t.y=this.position.y}return t}},f20d:function(e,t,r){"use strict";t.__esModule=true;t.Ticker=t.shared=undefined;var i=r("34ee");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}var o=new n.default;o.autoStart=true;o.destroy=function(){};t.shared=o;t.Ticker=n.default},f48e:function(e,t,r){"use strict";t.__esModule=true;t.default=i;function i(e){var t=e.getContextAttributes();if(!t.stencil){console.warn("Provided WebGL context does not have a stencil buffer, masks may not render correctly")}}},f4f6:function(e,t,r){"use strict";t.__esModule=true;var i=r("063c");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function u(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var l=function(e){u(t,e);function t(r){o(this,t);var i=s(this,e.call(this,r));i.stencilMaskStack=null;return i}t.prototype.setMaskStack=function e(t){this.stencilMaskStack=t;var r=this.renderer.gl;if(t.length===0){r.disable(r.STENCIL_TEST)}else{r.enable(r.STENCIL_TEST)}};t.prototype.pushStencil=function e(t){this.renderer.setObjectRenderer(this.renderer.plugins.graphics);this.renderer._activeRenderTarget.attachStencilBuffer();var r=this.renderer.gl;var i=this.stencilMaskStack.length;if(i===0){r.enable(r.STENCIL_TEST)}this.stencilMaskStack.push(t);r.colorMask(false,false,false,false);r.stencilFunc(r.EQUAL,i,this._getBitwiseMask());r.stencilOp(r.KEEP,r.KEEP,r.INCR);this.renderer.plugins.graphics.render(t);this._useCurrent()};t.prototype.popStencil=function e(){this.renderer.setObjectRenderer(this.renderer.plugins.graphics);var t=this.renderer.gl;var r=this.stencilMaskStack.pop();if(this.stencilMaskStack.length===0){t.disable(t.STENCIL_TEST);t.clear(t.STENCIL_BUFFER_BIT);t.clearStencil(0)}else{t.colorMask(false,false,false,false);t.stencilOp(t.KEEP,t.KEEP,t.DECR);this.renderer.plugins.graphics.render(r);this._useCurrent()}};t.prototype._useCurrent=function e(){var t=this.renderer.gl;t.colorMask(true,true,true,true);t.stencilFunc(t.EQUAL,this.stencilMaskStack.length,this._getBitwiseMask());t.stencilOp(t.KEEP,t.KEEP,t.KEEP)};t.prototype._getBitwiseMask=function e(){return(1<<this.stencilMaskStack.length)-1};t.prototype.destroy=function e(){n.default.prototype.destroy.call(this);this.stencilMaskStack.stencilStack=null};return t}(n.default);t.default=l},f5af:function(e,t,r){"use strict";t.__esModule=true;var i=r("3745");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function u(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var l=function(e){u(t,e);function t(r){o(this,t);return s(this,e.call(this,r,["attribute vec2 aVertexPosition;","attribute vec2 aTextureCoord;","attribute vec4 aColor;","attribute vec2 aPositionCoord;","attribute float aRotation;","uniform mat3 projectionMatrix;","uniform vec4 uColor;","varying vec2 vTextureCoord;","varying vec4 vColor;","void main(void){","   float x = (aVertexPosition.x) * cos(aRotation) - (aVertexPosition.y) * sin(aRotation);","   float y = (aVertexPosition.x) * sin(aRotation) + (aVertexPosition.y) * cos(aRotation);","   vec2 v = vec2(x, y);","   v = v + aPositionCoord;","   gl_Position = vec4((projectionMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);","   vTextureCoord = aTextureCoord;","   vColor = aColor * uColor;","}"].join("\n"),["varying vec2 vTextureCoord;","varying vec4 vColor;","uniform sampler2D uSampler;","void main(void){","  vec4 color = texture2D(uSampler, vTextureCoord) * vColor;","  gl_FragColor = color;","}"].join("\n")))}return t}(n.default);t.default=l},f7ad:function(e,t,r){"use strict";t.__esModule=true;var i=r("ff33");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s=function(){function e(){o(this,e);this.x0=0;this.y0=0;this.x1=1;this.y1=0;this.x2=1;this.y2=1;this.x3=0;this.y3=1;this.uvsUint32=new Uint32Array(4)}e.prototype.set=function e(t,r,i){var a=r.width;var o=r.height;if(i){var s=t.width/2/a;var u=t.height/2/o;var l=t.x/a+s;var h=t.y/o+u;i=n.default.add(i,n.default.NW);this.x0=l+s*n.default.uX(i);this.y0=h+u*n.default.uY(i);i=n.default.add(i,2);this.x1=l+s*n.default.uX(i);this.y1=h+u*n.default.uY(i);i=n.default.add(i,2);this.x2=l+s*n.default.uX(i);this.y2=h+u*n.default.uY(i);i=n.default.add(i,2);this.x3=l+s*n.default.uX(i);this.y3=h+u*n.default.uY(i)}else{this.x0=t.x/a;this.y0=t.y/o;this.x1=(t.x+t.width)/a;this.y1=t.y/o;this.x2=(t.x+t.width)/a;this.y2=(t.y+t.height)/o;this.x3=t.x/a;this.y3=(t.y+t.height)/o}this.uvsUint32[0]=(Math.round(this.y0*65535)&65535)<<16|Math.round(this.x0*65535)&65535;this.uvsUint32[1]=(Math.round(this.y1*65535)&65535)<<16|Math.round(this.x1*65535)&65535;this.uvsUint32[2]=(Math.round(this.y2*65535)&65535)<<16|Math.round(this.x2*65535)&65535;this.uvsUint32[3]=(Math.round(this.y3*65535)&65535)<<16|Math.round(this.x3*65535)&65535};return e}();t.default=s},f7ce:function(e,t,r){var i=r("9280");var n=r("ac8f");var a=function(e,t){var r={};var a=e.getProgramParameter(t,e.ACTIVE_UNIFORMS);for(var o=0;o<a;o++){var s=e.getActiveUniform(t,o);var u=s.name.replace(/\[.*?\]/,"");var l=i(e,s.type);r[u]={type:l,size:s.size,location:e.getUniformLocation(t,u),value:n(l,s.size)}}return r};e.exports=a},f87a:function(e,t,r){"use strict";t.__esModule=true;var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(t,r,i){if(r)e(t.prototype,r);if(i)e(t,i);return t}}();var n=r("6d8f");var a=s(n);var o=r("a506");function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:1;var a=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;u(this,e);this.a=t;this.b=r;this.c=i;this.d=n;this.tx=a;this.ty=o;this.array=null}e.prototype.fromArray=function e(t){this.a=t[0];this.b=t[1];this.c=t[3];this.d=t[4];this.tx=t[2];this.ty=t[5]};e.prototype.set=function e(t,r,i,n,a,o){this.a=t;this.b=r;this.c=i;this.d=n;this.tx=a;this.ty=o;return this};e.prototype.toArray=function e(t,r){if(!this.array){this.array=new Float32Array(9)}var i=r||this.array;if(t){i[0]=this.a;i[1]=this.b;i[2]=0;i[3]=this.c;i[4]=this.d;i[5]=0;i[6]=this.tx;i[7]=this.ty;i[8]=1}else{i[0]=this.a;i[1]=this.c;i[2]=this.tx;i[3]=this.b;i[4]=this.d;i[5]=this.ty;i[6]=0;i[7]=0;i[8]=1}return i};e.prototype.apply=function e(t,r){r=r||new a.default;var i=t.x;var n=t.y;r.x=this.a*i+this.c*n+this.tx;r.y=this.b*i+this.d*n+this.ty;return r};e.prototype.applyInverse=function e(t,r){r=r||new a.default;var i=1/(this.a*this.d+this.c*-this.b);var n=t.x;var o=t.y;r.x=this.d*i*n+-this.c*i*o+(this.ty*this.c-this.tx*this.d)*i;r.y=this.a*i*o+-this.b*i*n+(-this.ty*this.a+this.tx*this.b)*i;return r};e.prototype.translate=function e(t,r){this.tx+=t;this.ty+=r;return this};e.prototype.scale=function e(t,r){this.a*=t;this.d*=r;this.c*=t;this.b*=r;this.tx*=t;this.ty*=r;return this};e.prototype.rotate=function e(t){var r=Math.cos(t);var i=Math.sin(t);var n=this.a;var a=this.c;var o=this.tx;this.a=n*r-this.b*i;this.b=n*i+this.b*r;this.c=a*r-this.d*i;this.d=a*i+this.d*r;this.tx=o*r-this.ty*i;this.ty=o*i+this.ty*r;return this};e.prototype.append=function e(t){var r=this.a;var i=this.b;var n=this.c;var a=this.d;this.a=t.a*r+t.b*n;this.b=t.a*i+t.b*a;this.c=t.c*r+t.d*n;this.d=t.c*i+t.d*a;this.tx=t.tx*r+t.ty*n+this.tx;this.ty=t.tx*i+t.ty*a+this.ty;return this};e.prototype.setTransform=function e(t,r,i,n,a,o,s,u,l){this.a=Math.cos(s+l)*a;this.b=Math.sin(s+l)*a;this.c=-Math.sin(s-u)*o;this.d=Math.cos(s-u)*o;this.tx=t-(i*this.a+n*this.c);this.ty=r-(i*this.b+n*this.d);return this};e.prototype.prepend=function e(t){var r=this.tx;if(t.a!==1||t.b!==0||t.c!==0||t.d!==1){var i=this.a;var n=this.c;this.a=i*t.a+this.b*t.c;this.b=i*t.b+this.b*t.d;this.c=n*t.a+this.d*t.c;this.d=n*t.b+this.d*t.d}this.tx=r*t.a+this.ty*t.c+t.tx;this.ty=r*t.b+this.ty*t.d+t.ty;return this};e.prototype.decompose=function e(t){var r=this.a;var i=this.b;var n=this.c;var a=this.d;var s=-Math.atan2(-n,a);var u=Math.atan2(i,r);var l=Math.abs(s+u);if(l<1e-5||Math.abs(o.PI_2-l)<1e-5){t.rotation=u;if(r<0&&a>=0){t.rotation+=t.rotation<=0?Math.PI:-Math.PI}t.skew.x=t.skew.y=0}else{t.rotation=0;t.skew.x=s;t.skew.y=u}t.scale.x=Math.sqrt(r*r+i*i);t.scale.y=Math.sqrt(n*n+a*a);t.position.x=this.tx;t.position.y=this.ty;return t};e.prototype.invert=function e(){var t=this.a;var r=this.b;var i=this.c;var n=this.d;var a=this.tx;var o=t*n-r*i;this.a=n/o;this.b=-r/o;this.c=-i/o;this.d=t/o;this.tx=(i*this.ty-n*a)/o;this.ty=-(t*this.ty-r*a)/o;return this};e.prototype.identity=function e(){this.a=1;this.b=0;this.c=0;this.d=1;this.tx=0;this.ty=0;return this};e.prototype.clone=function t(){var r=new e;r.a=this.a;r.b=this.b;r.c=this.c;r.d=this.d;r.tx=this.tx;r.ty=this.ty;return r};e.prototype.copy=function e(t){t.a=this.a;t.b=this.b;t.c=this.c;t.d=this.d;t.tx=this.tx;t.ty=this.ty;return t};i(e,null,[{key:"IDENTITY",get:function t(){return new e}},{key:"TEMP_MATRIX",get:function t(){return new e}}]);return e}();t.default=l},fc76:function(e,t,r){"use strict";t.__esModule=true;t.default=i;function i(e){var t=e.width;var r=e.height;var i=e.getContext("2d");var n=i.getImageData(0,0,t,r);var a=n.data;var o=a.length;var s={top:null,left:null,right:null,bottom:null};var u=null;var l=void 0;var h=void 0;var f=void 0;for(l=0;l<o;l+=4){if(a[l+3]!==0){h=l/4%t;f=~~(l/4/t);if(s.top===null){s.top=f}if(s.left===null){s.left=h}else if(h<s.left){s.left=h}if(s.right===null){s.right=h+1}else if(s.right<h){s.right=h+1}if(s.bottom===null){s.bottom=f}else if(s.bottom<f){s.bottom=f}}}if(s.top!==null){t=s.right-s.left;r=s.bottom-s.top+1;u=i.getImageData(s.left,s.top,t,r)}return{height:r,width:t,data:u}}},fd43:function(e,t,r){"use strict";t.__esModule=true;var i=r("aa9d");var n=r("839e");var a=o(n);function o(e){return e&&e.__esModule?e:{default:e}}var s={getTintedTexture:function e(t,r){var i=t._texture;r=s.roundColor(r);var n="#"+("00000"+(r|0).toString(16)).substr(-6);i.tintCache=i.tintCache||{};var a=i.tintCache[n];var o=void 0;if(a){if(a.tintId===i._updateID){return i.tintCache[n]}o=i.tintCache[n]}else{o=s.canvas||document.createElement("canvas")}s.tintMethod(i,r,o);o.tintId=i._updateID;if(s.convertTintToImage){var u=new Image;u.src=o.toDataURL();i.tintCache[n]=u}else{i.tintCache[n]=o;s.canvas=null}return o},tintWithMultiply:function e(t,r,i){var n=i.getContext("2d");var a=t._frame.clone();var o=t.baseTexture.resolution;a.x*=o;a.y*=o;a.width*=o;a.height*=o;i.width=Math.ceil(a.width);i.height=Math.ceil(a.height);n.save();n.fillStyle="#"+("00000"+(r|0).toString(16)).substr(-6);n.fillRect(0,0,a.width,a.height);n.globalCompositeOperation="multiply";n.drawImage(t.baseTexture.source,a.x,a.y,a.width,a.height,0,0,a.width,a.height);n.globalCompositeOperation="destination-atop";n.drawImage(t.baseTexture.source,a.x,a.y,a.width,a.height,0,0,a.width,a.height);n.restore()},tintWithOverlay:function e(t,r,i){var n=i.getContext("2d");var a=t._frame.clone();var o=t.baseTexture.resolution;a.x*=o;a.y*=o;a.width*=o;a.height*=o;i.width=Math.ceil(a.width);i.height=Math.ceil(a.height);n.save();n.globalCompositeOperation="copy";n.fillStyle="#"+("00000"+(r|0).toString(16)).substr(-6);n.fillRect(0,0,a.width,a.height);n.globalCompositeOperation="destination-atop";n.drawImage(t.baseTexture.source,a.x,a.y,a.width,a.height,0,0,a.width,a.height);n.restore()},tintWithPerPixel:function e(t,r,n){var a=n.getContext("2d");var o=t._frame.clone();var s=t.baseTexture.resolution;o.x*=s;o.y*=s;o.width*=s;o.height*=s;n.width=Math.ceil(o.width);n.height=Math.ceil(o.height);a.save();a.globalCompositeOperation="copy";a.drawImage(t.baseTexture.source,o.x,o.y,o.width,o.height,0,0,o.width,o.height);a.restore();var u=(0,i.hex2rgb)(r);var l=u[0];var h=u[1];var f=u[2];var c=a.getImageData(0,0,o.width,o.height);var d=c.data;for(var p=0;p<d.length;p+=4){d[p+0]*=l;d[p+1]*=h;d[p+2]*=f}a.putImageData(c,0,0)},roundColor:function e(t){var r=s.cacheStepsPerColorChannel;var n=(0,i.hex2rgb)(t);n[0]=Math.min(255,n[0]/r*r);n[1]=Math.min(255,n[1]/r*r);n[2]=Math.min(255,n[2]/r*r);return(0,i.rgb2hex)(n)},cacheStepsPerColorChannel:8,convertTintToImage:false,canUseMultiply:(0,a.default)(),tintMethod:0};s.tintMethod=s.canUseMultiply?s.tintWithMultiply:s.tintWithPerPixel;t.default=s},fe6b:function(e,t,r){"use strict";t.__esModule=true;var i=r("dc07");var n=u(i);var a=r("99e3");var o=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function h(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function f(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var c=function(e){f(t,e);function t(r){l(this,t);var i=h(this,e.call(this,r));i.uploadHookHelper=i.renderer;i.registerFindHook(v);i.registerUploadHook(d);i.registerUploadHook(p);return i}return t}(o.default);t.default=c;function d(e,t){if(t instanceof n.BaseTexture){if(!t._glTextures[e.CONTEXT_UID]){e.textureManager.updateTexture(t)}return true}return false}function p(e,t){if(t instanceof n.Graphics){if(t.dirty||t.clearDirty||!t._webGL[e.plugins.graphics.CONTEXT_UID]){e.plugins.graphics.updateGraphics(t)}return true}return false}function v(e,t){if(e instanceof n.Graphics){t.push(e);return true}return false}n.WebGLRenderer.registerPlugin("prepare",c)},ff33:function(e,t,r){"use strict";t.__esModule=true;var i=r("f87a");var n=a(i);function a(e){return e&&e.__esModule?e:{default:e}}var o=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1];var s=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1];var u=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1];var l=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1];var h=[];var f=[];function c(e){if(e<0){return-1}if(e>0){return 1}return 0}function d(){for(var e=0;e<16;e++){var t=[];f.push(t);for(var r=0;r<16;r++){var i=c(o[e]*o[r]+u[e]*s[r]);var a=c(s[e]*o[r]+l[e]*s[r]);var d=c(o[e]*u[r]+u[e]*l[r]);var p=c(s[e]*u[r]+l[e]*l[r]);for(var v=0;v<16;v++){if(o[v]===i&&s[v]===a&&u[v]===d&&l[v]===p){t.push(v);break}}}}for(var y=0;y<16;y++){var g=new n.default;g.set(o[y],s[y],u[y],l[y],0,0);h.push(g)}}d();var p={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MIRROR_HORIZONTAL:12,uX:function e(t){return o[t]},uY:function e(t){return s[t]},vX:function e(t){return u[t]},vY:function e(t){return l[t]},inv:function e(t){if(t&8){return t&15}return-t&7},add:function e(t,r){return f[t][r]},sub:function e(t,r){return f[t][p.inv(r)]},rotate180:function e(t){return t^4},isVertical:function e(t){return(t&3)===2},byDirection:function e(t,r){if(Math.abs(t)*2<=Math.abs(r)){if(r>=0){return p.S}return p.N}else if(Math.abs(r)*2<=Math.abs(t)){if(t>0){return p.E}return p.W}else if(r>0){if(t>0){return p.SE}return p.SW}else if(t>0){return p.NE}return p.NW},matrixAppendRotationInv:function e(t,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;var a=h[p.inv(r)];a.tx=i;a.ty=n;t.append(a)}};t.default=p}}]);
//# sourceMappingURL=fe80db62.9c73268c.js.map