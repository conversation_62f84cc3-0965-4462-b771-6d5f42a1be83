{"version": 3, "sources": ["webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations-editor.vue?f21c", "webpack:///../app/@tylko/cards/relations/series-card.vue?70e6", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.regexp.search.js", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations-editor.vue?416f", "webpack:///./node_modules/@babel/runtime/helpers/asyncIterator.js", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations.vue?67b3", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations-editor.vue?f93f", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations-renderer-new.js", "webpack:///../app/@tylko/cards/relations/series-card.vue?dbeb", "webpack:///../app/@tylko/cards/relations/series-card.vue", "webpack:///../app/@tylko/cards/relations/series-card.vue?7b81", "webpack:///../app/@tylko/cards/relations/series-card.vue?886f", "webpack:///../app/@tylko/cards/relations/table-card.vue?fc80", "webpack:///../app/@tylko/cards/relations/table-card.vue", "webpack:///../app/@tylko/cards/relations/table-card.vue?7b7b", "webpack:///../app/@tylko/cards/relations/table-card.vue?c7b3", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations-editor.vue", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations-editor.vue?ec49", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations-editor.vue?5794", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations.vue", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations.vue?f2cf", "webpack:///../app/@tylko/cape-entrypoints/editors/cape-relations-editor/relations.vue?18c5", "webpack:///../app/@tylko/cards/relations/table-card.vue?90cf"], "names": ["_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_relations_editor_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "__webpack_require__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_relations_editor_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "n", "_unused_webpack_default_export", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_series_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_series_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "defined", "SEARCH", "$search", "search", "regexp", "O", "this", "fn", "undefined", "call", "RegExp", "String", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_relations_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_relations_editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "_asyncIterator", "iterable", "method", "Symbol", "asyncIterator", "iterator", "TypeError", "module", "exports", "relationsvue_type_template_id_5fbf5109_lang_pug_render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "ref", "staticRenderFns", "relations_editorvue_type_template_id_1c30add9_lang_pug_render", "staticStyle", "width", "overflow", "position", "_m", "_l", "serie", "class", "selectedCollectioName", "id", "$route", "params", "selectedSerie", "attrs", "to", "_v", "_s", "name", "color", "dark", "dense", "disabled", "$refs", "tableEditor", "syncing", "label", "val", "on", "input", "updateTable", "model", "value", "callback", "$$v", "choosenSeriesForTable", "expression", "_e", "height", "300", "400", "500", "600", "700", "800", "900", "1000", "max-width", "options", "target-model", "dropMode", "big", "section-label", "editMode", "inverted-light", "separator", "miniops", "selectMiniMode", "mini", "loading", "batchRunning", "percentage", "miniaturesDone", "nativeOn", "click", "$event", "prepareMiniaturesAll", "prepareMiniaturesCollection", "series", "query<PERSON><PERSON>ult", "tablesList", "tableConfigurations", "colletionID", "projectFilter", "selectedSeries", "loadTables", "loaded", "dimSeries", "currentComponentSeries", "render", "rerrender", "relations_editorvue_type_template_id_1c30add9_lang_pug_staticRenderFns", "menu", "box", "cols", "rows", "gutter", "actAsNavigation", "typesToDisplay", "searchMatch", "lightup", "zTotal", "Ruler", "ORIENTATION_VERTICAL", "ORIENTATION_HORIZONTAL", "DEBUG", "Director", "_this", "classCallCheck_default", "checked", "bgDrawn", "items", "labels", "sprites", "orderHittest", "getSortArray", "currentState", "cache", "delta", "x", "y", "selectedSeriesNo", "fetching", "throttledDraw", "_", "throttle", "__draw", "heights", "window", "addEventListener", "e", "onResize", "draw", "arguments", "length", "pixiApp", "vue", "_this$getSize", "getSize", "currentWidth", "renderer", "resize", "view", "style", "concat", "<PERSON><PERSON><PERSON><PERSON>", "clear", "vueComponentInstance", "_this2", "initializePixi", "append<PERSON><PERSON><PERSON>", "event", "normalized", "normalizeWheel", "pixelY", "Math", "abs", "pixiHitests", "selected", "size", "$el", "getBoundingClientRect", "top", "left", "orientation", "Editor", "_this$getSize2", "PIXI", "backgroundColor", "resolution", "pixiRoot", "annotationsOverlay", "drawingContainer", "<PERSON><PERSON><PERSON><PERSON>", "stage", "stop", "seriesToDim", "clientX", "clientY", "_this$vue$$refs$rende", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON>ght", "map", "a", "an", "h", "ceil", "no", "item_width", "item_height", "max_blocks", "cols_in_block", "rows_in_block", "elements_in_block", "getPosition", "block", "floor", "index", "col", "row", "block2", "round", "objectSpread_default", "w", "type", "item", "order", "unshift", "addConfiguration", "superSelect", "_this3", "_this$getElementPosit", "getElementPosition", "push", "parseInt", "selectedConfiguration", "drawingSprite", "fake", "lineStyle", "beginFill", "drawRoundedRect", "endFill", "inconsequent", "drawRect", "square", "addChildAt", "alpha", "interactive", "buttonMode", "hitArea", "cursor", "data", "serieId", "componentID", "hash", "XXH", "h32", "toString", "sprite", "cape", "services", "miniatures", "add", "find", "component", "thumbnails_data", "fetched", "then", "result", "bt", "painterState", "_sourceLoaded", "texture", "defaultAnchor", "console", "log", "specialMenuOpened", "originalEvent", "preventDefault", "lightupByID", "application", "contextMenu", "show", "compName", "setID", "setId", "configID2", "configID", "selectedCollection", "onDragStart", "onDragEnd", "onDragMove", "dnd", "isDraggingHappening", "isMatch", "hit", "children", "indexOf", "inconsequentArray", "drawSelectedSeries", "_this4", "no2Id", "findIndex", "rawSeries", "Array", "isArray", "noList", "idList", "seriesToDimId", "root", "totalHeight", "seriesCount", "_this$getElementPosit2", "bar", "bar2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img", "generateTexture", "mapper", "fill", "pass", "difference", "drawInconsequent", "heightNo", "serieID", "tableConfiguration", "inconsequents", "_this$getElementPosit3", "dragging", "startPosition", "getLocalPosition", "draggedId", "selectedIndex", "draggedSprite", "targetSprite", "sorted", "filter", "s", "directionReverse", "splice", "draggedInitialNo", "draggedTargetNo", "d", "no2", "_this5", "changedSerie", "inconsequentToggle", "_ref", "asyncToGenerator_default", "regenerator_default", "mark", "_callee", "state", "_this5$getElementPosi", "wrap", "_callee$", "_context", "prev", "next", "api", "updateSeriesConfiguration", "tag", "<PERSON><PERSON><PERSON><PERSON>", "updateAfterChange", "_x", "_x2", "apply", "seriesNoLocal", "seriesIdLocal", "cloneMode", "selectedSpriteNoData", "targetSpriteNoData", "config", "isNew", "draggedItem", "draggedItemType", "newPosition", "p", "liveSortExecute", "oldPosition", "r", "sqrt", "pow", "seriesNames", "transform", "ss", "_this6", "i", "removeAllListeners", "_this$getSize3", "edgeStep", "j", "moveX", "ordered", "orderBy", "drawItem", "text", "c", "sampleText", "gltext", "create", "set", "series_cardvue_type_template_id_3bb2da30_lang_pug_render", "neverEmpty", "seriesList", "update-model", "save", "v", "selectSerie", "title", "buttons", "saveSerie", "selectedSerieName", "remove", "nodes", "tree", "node-key", "default-expand-all", "series_cardvue_type_template_id_3bb2da30_lang_pug_staticRenderFns", "series_cardvue_type_script_lang_js_", "props", "components", "_cape_ui", "computed", "watch", "from", "mounted", "methods", "_saveSerie", "query", "updateSerie", "sent", "$emit", "$q", "dialog", "message", "ok", "cancel", "onOk", "_callee2", "_callee2$", "_context2", "removeSeries", "$router", "selectedTable", "broadcastNewParameters", "source", "updateComponent", "comp", "additional_params", "subscribe", "relations_series_cardvue_type_script_lang_js_", "Object", "componentNormalizer", "series_card", "table_cardvue_type_template_id_b7994140_lang_pug_render", "selectedTableName", "newModel", "selectTable", "saveTable", "createTable", "newTableName", "table_cardvue_type_template_id_b7994140_lang_pug_staticRenderFns", "table_cardvue_type_script_lang_js_", "collectionTables", "defineProperty_default", "collectionID", "Number", "table", "tableId", "isSeriesChoosen", "diffSeriesConfigurations", "_diffSeriesConfigurations", "_saveTable", "_callee3", "_callee3$", "_context3", "removeTable", "_createTable", "_callee4", "_callee4$", "_context4", "collection", "getCurrentTableConfigs", "currentTableConfigations", "configurations", "_updateTable", "_callee5", "createTableConfg", "removeTableConfg", "currentSeries", "savedConigurations", "diff", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "_iterator", "_step", "_value", "newConfig", "_iteratorNormalCompletion2", "_didIteratorError2", "_iteratorError2", "_iterator2", "_step2", "_value2", "removedConfig", "_callee5$", "_context5", "createTableConfiguration", "removeTableConfiguration", "conf", "differenceWith", "isEqual", "asyncIterator_default", "done", "t0", "return", "finish", "t1", "_x3", "relations_table_cardvue_type_script_lang_js_", "table_card_component", "table_card", "relations_editorvue_type_script_lang_js_Editor", "uuidv4", "relations_editorvue_type_script_lang_js_DEBUG", "relations_editorvue_type_script_lang_js_", "t-series", "t-table", "t-bar", "palette_bar", "miniApperance", "colors", "settings", "get", "ids", "mode", "settingss", "_dimSeries", "id2no", "loadSeries", "_loadSeries", "palette", "relations", "componentSeries", "thumbs", "fetch", "_loadTables", "componentTable", "currentTable", "selectSeries", "selectInconsequent", "uuid", "selectConfiguration", "prepareMiniatures", "_prepareMiniatures", "specific", "allComponents", "total", "allCollections", "componentsOfCollections", "collectionComponents", "lodash_default", "flatten", "geo", "componentSet", "queryForMiniature", "forceFetch", "pipe", "_ref2", "updateComponentThumb", "error", "catch", "getHeightFromSetup", "computeItems", "_computeItems", "_callee6", "drawAfter", "needsUpdate", "first", "seriesObject", "emptyItem", "seriesConfigMatchHeight", "_i2", "renderable", "_args6", "_callee6$", "_context6", "serializeState", "_draw", "abrupt", "rawSeriesObject", "cell", "component_set", "component_name", "configurationID", "seria", "seriaNo", "cellNo", "emptyFillNo", "_i", "idserii", "itemNo", "seriesID", "t", "reload", "$forceUpdate", "saveOrder", "_saveOrder", "_callee7", "_callee7$", "_context7", "_addConfiguration", "_callee8", "meshMode", "_args8", "_callee8$", "_context8", "_x4", "build", "executeChanges", "actionsToPerform", "findDifferences", "remove<PERSON>mpty<PERSON><PERSON><PERSON>", "diffArray", "serieNo", "seriesConfigurationID", "action", "configHeight", "targetSerieNo", "saveDiff", "_ref3", "_callee9", "waitFor", "newCollection", "newCollectionOnDrop", "_i3", "targetSeriesID", "_ref4", "apicall", "isSpwapping", "_callee9$", "_context9", "createSeries", "removeSeriesConfiguration", "parent", "createSeriesConfiguration", "configs", "currentInq", "setInconsequent", "_inconsequentToggle", "_callee10", "tableInq", "inqState", "_callee10$", "_context10", "updateTableConfiguration", "_x5", "_x6", "addItemDrop", "target", "newState", "stateBySeries", "stateBySeriesSource", "emptySeries", "groupBy", "created", "_this7", "apper", "miniStyleData", "userSettings", "key", "keys", "mId", "addDestinationComponent", "instance", "<PERSON><PERSON><PERSON><PERSON>", "drop", "traceDropCoordinates", "setContext", "listenAction", "context", "dispatchEvent", "Event", "handler", "ob", "slot", "setObjectItem", "deep", "currentSetup", "currentHeight", "cape_relations_editor_relations_editorvue_type_script_lang_js_", "relations_editor_component", "relations_editor", "relationsvue_type_script_lang_js_", "t-relations", "viewport", "fixed", "rendererHeight", "innerHeight", "activeCollection", "cape_relations_editor_relationsvue_type_script_lang_js_", "relations_component", "__webpack_exports__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_table_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_table_card_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default"], "mappings": "wGAAA,IAAAA,EAAAC,EAAA,YAAAC,EAAAD,EAAAE,EAAAH,GAAmlB,IAAAI,EAAAF,EAAG,8DCAtlB,IAAAG,EAAAJ,EAAA,YAAAK,EAAAL,EAAAE,EAAAE,GAA4jB,IAAAD,EAAAE,EAAG,0BCC/jBL,EAAQ,OAARA,CAAuB,oBAAAM,EAAAC,EAAAC,GAEvB,gBAAAC,EAAAC,GACA,aACA,IAAAC,EAAAL,EAAAM,MACA,IAAAC,EAAAH,GAAAI,oBAAAJ,EAAAH,GACA,OAAAM,IAAAC,UAAAD,EAAAE,KAAAL,EAAAC,GAAA,IAAAK,OAAAN,GAAAH,GAAAU,OAAAN,KACGH,wFCRH,IAAAU,EAAAlB,EAAA,YAAAmB,EAAAnB,EAAAE,EAAAgB,GAAmlB,IAAAf,EAAAgB,EAAG,wBCAtlB,SAAAC,EAAAC,GACA,IAAAC,EAEA,UAAAC,SAAA,YACA,GAAAA,OAAAC,cAAA,CACAF,EAAAD,EAAAE,OAAAC,eACA,GAAAF,GAAA,YAAAA,EAAAP,KAAAM,GAGA,GAAAE,OAAAE,SAAA,CACAH,EAAAD,EAAAE,OAAAE,UACA,GAAAH,GAAA,YAAAA,EAAAP,KAAAM,IAIA,UAAAK,UAAA,gCAGAC,EAAAC,QAAAR,8CClBA,IAAIS,EAAM,WAAgB,IAAAC,EAAAlB,KAAa,IAAAmB,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,YAAA,QAAmB,CAAAF,EAAA,eAAoBG,IAAA,YAAa,IAClK,IAAAC,EAAA,mBCDA,IAAIC,EAAM,WAAgB,IAAAR,EAAAlB,KAAa,IAAAmB,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,YAAA,8BAAyC,CAAAF,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,OAAkB,CAAAF,EAAA,OAAYE,YAAA,MAAAI,YAAA,CAA+BC,MAAA,cAAAC,SAAA,SAAAC,SAAA,aAAiE,CAAAT,EAAA,OAAYE,YAAA,KAAgB,CAAAL,EAAAa,GAAA,WAAAV,EAAA,OAA8BE,YAAA,OAAkB,CAAAF,EAAA,OAAYE,YAAA,MAAAI,YAAA,CAA+BC,MAAA,+BAAsC,CAAAP,EAAA,OAAYE,YAAA,yBAAoC,CAAAL,EAAA,YAAAG,EAAA,OAA8BG,IAAA,cAAAD,YAAA,mBAAgDL,EAAAc,GAAAd,EAAA,gCAAAe,GAAqD,OAAAZ,EAAA,OAAiBE,YAAA,kBAAAW,MAAA,CAAqCC,sBAAAF,EAAAG,IAAAlB,EAAAmB,OAAAC,OAAAC,gBAAsE,CAAAlB,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,eAAoBE,YAAA,UAAAiB,MAAA,CAA6BC,GAAA,cAAAvB,EAAAmB,OAAAC,OAAA,uBAAApB,EAAAmB,OAAAC,OAAA,kBAAAL,EAAA,KAA4H,CAAAf,EAAAwB,GAAAxB,EAAAyB,GAAAV,EAAAW,UAAA,GAAAvB,EAAA,OAA6CE,YAAA,UAAqB,CAAAF,EAAA,cAAmBmB,MAAA,CAAOK,MAAA,OAAAC,KAAA,OAAAC,MAAA,QAAAC,SAAA9B,EAAA+B,MAAAC,YAAAC,QAAAC,MAAA,MAAAC,IAAApB,EAAAG,IAAmHkB,GAAA,CAAKC,MAAArC,EAAAsC,aAAwBC,MAAA,CAAQC,MAAAxC,EAAA,sBAAAyC,SAAA,SAAAC,GAA2D1C,EAAA2C,sBAAAD,GAA8BE,WAAA,4BAAqC,OAAQ,GAAA5C,EAAA6C,OAAA1C,EAAA,OAA0BE,YAAA,gCAA2C,CAAAF,EAAA,OAAYE,YAAA,eAA0BL,EAAAc,GAAAd,EAAA,iBAAA8C,GAAuC,OAAA3C,EAAA,OAAiBE,YAAA,iBAA4B,CAAAL,EAAAwB,GAAAxB,EAAAyB,GAAA,CAAiBsB,IAAA,IAAAC,IAAA,IAAAC,IAAA,IAAAC,IAAA,IAAAC,IAAA,IAAAC,IAAA,IAAAC,IAAA,IAAAC,KAAA,KAAgGR,IAAA,IAAA9C,EAAAyB,GAAAqB,QAAgC,GAAA3C,EAAA,OAAeG,IAAA,YAAAD,YAAA,yCAAoE,CAAAF,EAAA,OAAYG,IAAA,WAAAD,YAAA,6BAAgDF,EAAA,OAAkBE,YAAA,MAAAI,YAAA,CAA+B8C,YAAA,UAAqB,CAAApD,EAAA,iBAAsBE,YAAA,aAAwB,CAAAF,EAAA,UAAemB,MAAA,CAAOI,KAAA,oBAA0B,CAAAvB,EAAA,OAAYE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,aAAkBmB,MAAA,CAAOkC,QAAA,EAAYtB,MAAA,UAAAM,MAAA,WAAqC,CAAGN,MAAA,SAAAM,MAAA,WAAmCiB,eAAAzD,EAAA0D,SAAAC,IAAA,MAAAC,gBAAA,eAAwEzD,EAAA,aAAkBmB,MAAA,CAAOkC,QAAA,EAAYtB,MAAA,UAAAM,MAAA,WAAqC,CAAGN,MAAA,QAAAM,MAAA,UAAiCiB,eAAAzD,EAAA6D,SAAAF,IAAA,MAAAC,gBAAA,gBAAwE,OAAAzD,EAAA,OAAoBE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,aAAkBmB,MAAA,CAAOI,KAAA,mBAAyB,CAAAvB,EAAA,UAAAA,EAAA,UAAAA,EAAA,YAA2CmB,MAAA,CAAOwC,iBAAA,iBAAAnC,MAAA,SAAAoC,UAAA,YAAAP,QAAAxD,EAAAgE,SAAiG5B,GAAA,CAAKC,MAAArC,EAAAiE,gBAA2B1B,MAAA,CAAQC,MAAAxC,EAAA,KAAAyC,SAAA,SAAAC,GAA0C1C,EAAAkE,KAAAxB,GAAaE,WAAA,WAAoB,GAAAzC,EAAA,UAAAA,EAAA,SAA+BmB,MAAA,CAAO6C,QAAAnE,EAAAoE,cAAA,EAAAlC,MAAA,wBAAAmC,WAAArE,EAAAsE,gBAA8FC,SAAA,CAAWC,MAAA,SAAAC,GAAyB,OAAAzE,EAAA0E,qBAAAD,QAA0C,GAAAtE,EAAA,UAAAA,EAAA,SAA+BmB,MAAA,CAAO6C,QAAAnE,EAAAoE,cAAA,EAAAlC,MAAA,mCAAAmC,WAAArE,EAAAsE,gBAAyGC,SAAA,CAAWC,MAAA,SAAAC,GAAyB,OAAAzE,EAAA2E,4BAAAF,QAAiD,qBAAAtE,EAAA,WAAsCG,IAAA,cAAAgB,MAAA,CAAyBsD,OAAA5E,EAAA6E,YAAAC,WAAA9E,EAAA8E,WAAAC,oBAAA/E,EAAA+E,oBAAAC,YAAAhF,EAAAiF,cAAAC,eAAAlF,EAAA2C,uBAA8KP,GAAA,CAAK+C,WAAAnF,EAAAmF,WAAAC,OAAApF,EAAAqF,aAAoDlF,EAAA,YAAiBmB,MAAA,CAAOsD,OAAA5E,EAAAsF,uBAAAN,YAAAhF,EAAAiF,eAAoE7C,GAAA,CAAK+C,WAAAnF,EAAAmF,WAAAI,OAAAvF,EAAAwF,cAAoD,cAC9rH,IAAIC,EAAe,YAAiB,IAAAzF,EAAAlB,KAAa,IAAAmB,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,SAAmBG,IAAA,MAAAgB,MAAA,CAAiBoE,KAAA,OAAAC,IAAA,CAAqBjF,MAAA,IAAAoC,OAAA,GAAA8C,KAAA,EAAAC,KAAA,EAAAC,OAAA,GAAsDC,gBAAA,MAAAC,eAAA,sBAA+D5D,GAAA,CAAK6D,YAAAjG,EAAAkG,+eCMtR,IAAIC,EAAS,IAEb,IAAIC,EAAQ,CACRC,qBAAsB,WACtBC,uBAAwB,cAG5B,IAAMC,EAAQ,SAERC,aAEF,SAAAA,IAAc,IAAAC,EAAA3H,KAAA4H,IAAA5H,KAAA0H,GAEV1H,KAAK6H,QAAU,MACf7H,KAAK8H,QAAU,MACf9H,KAAK+H,MAAQ,GACb/H,KAAKgI,OAAS,GACdhI,KAAKiI,QAAU,GACfjI,KAAKkI,aAAe,GACpBlI,KAAKmI,aAAe,GACpBnI,KAAK8F,OAAS,GACd9F,KAAKoI,aAAe,KACpBpI,KAAKqI,MAAQ,GACbrI,KAAKsI,MAAQ,CAAEC,EAAG,EAAGC,EAAG,GACxBxI,KAAK+E,SAAW,SAChB/E,KAAKyI,iBAAmB,GACxBzI,KAAK0I,SAAW,MAEhB1I,KAAK2I,cAAgBC,IAAEC,SAAS7I,KAAK8I,OAAQ,GAE7C,IAAIC,EAAU/I,KAAK+I,QAAU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAEjEC,OAAOC,iBAAiB,SAAU,SAACC,GAC/BvB,EAAKwB,uDAKS,IAAbC,EAAaC,UAAAC,OAAA,GAAAD,UAAA,KAAAnJ,UAAAmJ,UAAA,GAAN,KAEZ,IAAKrJ,KAAKuJ,UAAYvJ,KAAKwJ,IAAK,OAFd,IAAAC,EAGMzJ,KAAK0J,UAAvB9H,EAHY6H,EAGZ7H,MAAOoC,EAHKyF,EAGLzF,OACbhE,KAAK2J,aAAe/H,EACpB5B,KAAKuJ,QAAQK,SAASC,OAAOjI,EAAOoC,GACpChE,KAAKuJ,QAAQO,KAAKC,MAAM,aAAxB,GAAAC,OAA0CpI,EAA1C,MACA5B,KAAKiK,oBAAoBC,QACzBlK,KAAK8H,QAAU,MACf,GAAIsB,EAAMpJ,KAAKwJ,IAAIJ,4CAGZe,GAAsB,IAAAC,EAAApK,KAC7BA,KAAKwJ,IAAMW,EACXnK,KAAKqK,iBACLrK,KAAKwJ,IAAIvG,MAAM2G,SAASU,YAAYtK,KAAKuJ,QAAQO,MACjD9J,KAAKsI,MAAQ,CAAEC,EAAG,EAAGC,EAAG,GAExBxI,KAAKwJ,IAAIvG,MAAM2G,SAASX,iBAAiB,aAAc,SAACsB,GACpD,IAAMC,EAAaC,IAAeF,GAElCH,EAAK9B,MAAMC,GAAKiC,EAAWE,OAE3B,GAAIN,EAAK9B,MAAMC,EAAI,EAAG6B,EAAK9B,MAAMC,EAAI,EACrC,GAAIoC,KAAKC,IAAIR,EAAK9B,MAAMC,GAAKqB,EAASiB,YAAYjJ,MAAQwI,EAAKT,aAC3DS,EAAK9B,MAAMC,IAAMqB,EAASiB,YAAYjJ,MAAQwI,EAAKT,cACvD,GAAIC,EAASiB,YAAYjJ,MAAQwI,EAAKT,aAAcS,EAAK9B,MAAMC,EAAI,EAEnE6B,EAAKzB,kBAGT3I,KAAK8K,SAAW,CAAE1I,GAAIpC,KAAKwJ,IAAInH,OAAOC,OAAOF,IAE7CpC,KAAKwJ,IAAIJ,OACTpJ,KAAKmJ,6CAKL,IAAI4B,EAAO/K,KAAKwJ,IAAIwB,IAAIC,wBACxB,MAAO,CAAE1C,EAAGwC,EAAKG,IAAK1C,EAAGuC,EAAKI,0CAM9B,IAAIJ,EAAO/K,KAAKwJ,IAAIwB,IAAIC,wBACxB,MAAO,CAAErJ,MAAOmJ,EAAKnJ,MAAQ,IAAKoC,OAAQ+G,EAAK/G,QAE/C,OAAQhE,KAAKoL,aACT,KAAKC,OAAO9D,qBACR,MACJ,KAAK8D,OAAO7D,uBACR,MACJ,QACI,kDAMR,GAAIxH,KAAKuJ,QAAS,OAFL,IAAA+B,EAIWtL,KAAK0J,UAAvB9H,EAJO0J,EAIP1J,MAAOoC,EAJAsH,EAIAtH,OAEbhE,KAAKuJ,QAAU,IAAIgC,iBAAiB3J,EAAOoC,EAAQ,CAC/CwH,gBAAiB,EACjBC,WAAY,IAGhB,IAAIlC,EAAUvJ,KAAKuJ,QAEnBvJ,KAAKqI,MAAQ,GAEbrI,KAAK0L,SAAW,IAAIH,eACpBvL,KAAK6K,YAAc,IAAIU,eAEvBvL,KAAK2L,mBAAqB,IAAIJ,eAE9BvL,KAAK4L,iBAAmB,IAAIL,cAC5BvL,KAAKiK,oBAAsB,IAAIsB,cAE/BvL,KAAK0L,SAASG,SAAS7L,KAAKiK,qBAC5BjK,KAAK0L,SAASG,SAAS7L,KAAK4L,kBAE5B5L,KAAKuJ,QAAQuC,MAAMD,SAAS7L,KAAK0L,UACjC1L,KAAKuJ,QAAQuC,MAAMD,SAAS7L,KAAK6K,aAEjC7K,KAAKuJ,QAAQuC,MAAMD,SAAS7L,KAAK2L,oBAEjC3L,KAAKwJ,IAAIvG,MAAM2G,SAASU,YAAYf,EAAQO,MAC5C9J,KAAKuJ,QAAQO,KAAKC,MAAM,aAAxB,GAAAC,OAA0CpI,EAA1C,MAEA5B,KAAKuJ,QAAQwC,OAEb/L,KAAKgM,YAAc,GAGnBhM,KAAKwJ,IAAIJ,sDAIQmB,GAAO,IAEThC,EAAkBgC,EAA3B0B,QAAqBzD,EAAM+B,EAAf2B,QAFM,IAAAC,EAGJnM,KAAKwJ,IAAIvG,MAAM2G,SAASwC,WAAWnB,wBAAjDC,EAHkBiB,EAGlBjB,IAAKC,EAHagB,EAGbhB,KAEX5C,GAAK4C,EAAOnL,KAAKsI,MAAMC,EACvBC,GAAK0C,EAEL,IAAI9E,EAAgBiG,EAEpBrM,KAAKkI,aAAaoE,IAAI,SAACC,EAAGC,GAEtBD,EAAED,IAAI,SAACzF,EAAKvH,GACR,GAAIiJ,GAAK1B,EAAI2B,GAAKD,GAAK1B,EAAI2B,EAAI3B,EAAI4F,EAAG,CAClCrG,EAAiB9G,EACjB+M,EAAiB1B,KAAK+B,KAAKlE,EAAI3B,EAAI4F,GAAK,OAKpD,MAAO,CAAErG,iBAAgBiG,+DAGVM,GACf,IAAI3F,EAAS,EAEb,IAAI4F,EAAa,IACjB,IAAIC,EAAc,IAElB,IAAIC,EAAa,GACjB,IAAIC,EAAgB,EACpB,IAAIC,EAAgB,EACpB,IAAIC,EAAoBF,EAAgBC,EAExC,IAAME,EAAc,SAAdA,EAAcP,GAChB,IAAIQ,EAAQxC,KAAKyC,MAAMT,EAAKM,GAE5B,IAAII,EAAQV,EAAKQ,EAAQF,EACzB,IAAIK,EAAM3C,KAAKyC,MAAMC,EAAQL,GAC7B,IAAIO,EAAMF,EAAQC,EAAMN,EACxB,IAAIQ,EAAS7C,KAAK8C,MAAMd,EAAKM,GAE7B,MAAO,CACH1E,EAAG+E,EAAMV,IAAc5F,EAAS,EAChCwB,EAAG+E,EAAMV,EAAc7F,EAAS,EAAImG,EAAQN,EAAcG,IAIlE,OAAAU,IAAA,GACOR,EAAYP,GADnB,CAEIgB,EAAGf,EAAa5F,EAChByF,EAAGI,EAAc7F,EACjBA,6CAIA5E,EAAIwL,GACR,IAAIC,EAAO,CAAEzL,KAAIwL,OAAME,MAAO9N,KAAK+H,MAAMuB,OAAS,GAClDtJ,KAAK+H,MAAMgG,QAAQF,GACnB7N,KAAKgO,iBAAiBH,sCAGjB5L,EAAO0K,EAAIkB,EAAMD,EAAMK,GAAa,IAAAC,EAAAlO,KAAA,IAAAmO,EAEZnO,KAAKoO,mBAAmBzB,GAA/CpE,EAFmC4F,EAEnC5F,EAAGC,EAFgC2F,EAEhC3F,EAAGmF,EAF6BQ,EAE7BR,EAAGlB,EAF0B0B,EAE1B1B,EAAGzF,EAFuBmH,EAEvBnH,OAElBhH,KAAKkI,aAAajG,EAAMG,IAAMpC,KAAKkI,aAAajG,EAAMG,KAAO,GAC7DpC,KAAKkI,aAAajG,EAAMG,IAAIiM,KAAK,CAAE7F,IAAGiE,MAEtC,IAAIrK,EAAK6L,EACHA,EACAK,SAAStO,KAAKwJ,IAAInH,OAAOC,OAAOiM,uBAEtC,IAAIC,EAAgB,IAAIjD,cAExBiD,EAAcxK,OAASyI,EACvB+B,EAAc5M,MAAQ+L,EACtBa,EAAc1I,OAAS7D,EAAMG,GAE7B,GAAIyL,EAAKY,KAAM,CACXD,EACKE,UAAU,EAAG,SAAU,IACvBC,UACG,EACA,IAEHC,gBAAgB5H,EAAQA,EAAQ2G,EAAI3G,EAAS,EAAGyF,EAAIzF,EAAS,EAAG,GAChE6H,cACF,CACHL,EACKE,UAAU,EAAG,SAAU,IACvBC,UACGd,EAAKiB,aAAe,IAAW,IAC/BjB,EAAKY,KAAO,GAAM,GAErBM,SAAS,EAAG,EAAGpB,EAAGlB,GAClBoC,UAMT,IAAIG,EAAS,IAAIzD,cACjByD,EACKN,UAAU,EAAG,MAAU,IACvBK,SAAS/H,EAAS,EAAI,EAAGA,EAAS,EAAI,EAAG2G,EAAI3G,EAAS,EAAI,GAAIyF,EAAIzF,EAAS,EAAI,IAC/E6H,UAELL,EAAcS,WAAWD,EAAQ,GACjCA,EAAOE,MAAQ,EACfF,EAAOG,YAAc,MACrBH,EAAOI,WAAa,MAEpBZ,EAAca,QAAU,IAAI9D,eAAe,EAAG,EAAGoC,EAAGlB,GACpD+B,EAAc1M,SAAS0G,EAAIvG,EAAM6L,OAASH,EAAI3G,GAAUuB,EACxDiG,EAAc1M,SAASyG,EAAIC,EAC3BgG,EAAcW,YAAc,KAC5BX,EAAcY,WAAa,KAC3BZ,EAAcc,OAAS,UACvBd,EAAce,KAAO,CAAEhF,MAAO,KAAMiF,QAASvN,EAAMG,GAAI0L,MAAOnB,EAAI8C,YAAa5B,EAAK4B,aAEpF,IAAK5B,EAAKY,KAAM,CAEZ,IAAIiB,EAAOC,IAAIC,IAAJ,GAAA5F,OAAW6D,EAAK4B,aAAe,OAAQI,SAAS,IAE3D,GAAI7P,KAAKqI,MAAMqH,GAAO,CAClB,IAAII,EAAS,IAAIvE,YAAYvL,KAAKqI,MAAMqH,IACxClB,EAAcS,WAAWa,EAAQ,OAC9B,CACH,GAAI9P,KAAK+F,aAAe8H,EAAK4B,YAAa,CAEtCM,OAAKC,SAASC,WAAWC,IAAI,CACzBX,KAAM3G,IAAEuH,KAAKnQ,KAAK+F,YAAYqK,UAAW,CAAEhO,GAAIyL,EAAK4B,cAAeY,gBACnEjO,GAAIyL,EAAK4B,YACTa,QAAS,OACVC,KAAK,SAACC,GAGL,IAAIC,EAAK,IAAIlF,iBAAiBiF,EAAOE,cACrCD,EAAGE,gBACH,IAAIC,EAAU,IAAIrF,aAAakF,GAC/BG,EAAQC,cAAgB,CAAEtI,EAAG,EAAGC,EAAG,GACnC0F,EAAK7F,MAAMqH,GAAQkB,EACnB,IAAId,EAAS,IAAIvE,YAAYqF,GAC7BpC,EAAcS,WAAWa,EAAQ,GAEjC5B,EAAK3E,QAAQ9C,YAOzBqK,QAAQC,IAAI9O,EAAMG,IAElBoM,EACKlL,GAAG,YAAa,SAAC4F,GACd,GAAIgF,EAAKxF,SAAU,OACnB8F,EAAcwC,kBAAoB,KAClC9H,EAAEqG,KAAK0B,cAAcC,iBACrBJ,QAAQC,IAAI,OAAQlD,GACpBK,EAAKiD,YAAYtD,EAAK4B,aACtBM,OAAKqB,YAAYC,YAAYC,KAAKpI,EAAG,CAAEtG,KAAMiL,EAAK0D,SAAUC,MAAO3D,EAAK4D,MAAOC,UAAW7D,EAAKzL,GAAIuP,SAAU9D,EAAK4B,YAAamC,mBAAoB1D,EAAK1E,IAAInH,OAAOC,OAAOsP,oBAAsB,eAEnMtO,GAAG,YAAa,SAAC4F,GACd,GAAIgF,EAAKxF,SAAU,OAEnBwF,EAAK2D,YAAYhE,EAAMlB,EAAI6B,EAAetF,EAAGjH,KAEhDqB,GAAG,UAAW,SAAC4F,GACZ,GAAIgF,EAAKxF,SAAU,OAEnBwF,EAAK4D,UAAUjE,EAAKzL,GAAIoM,EAAeX,EAAMlB,EAAI1K,KAEpDqB,GAAG,mBAAoB,SAAC4F,GACrB,GAAIgF,EAAKxF,SAAU,OAEnBwF,EAAK4D,UAAUjE,EAAKzL,GAAIoM,EAAeX,EAAMlB,EAAI1K,KAEpDqB,GAAG,YAAa,SAAC4F,GACd,GAAIgF,EAAKxF,SAAU,OACnBwF,EAAK6D,WAAWlE,EAAMlB,EAAI6B,EAAetF,EAAGjH,KAE/CqB,GAAG,YAAa,SAAC4F,GACd,GAAIgF,EAAKxF,SAAU,OACnB,GAAIqH,OAAKqB,YAAYY,IAAIC,oBAAqB,CAC1CzD,EAAcU,MAAQ,GACtBhB,EAAK3E,QAAQ9C,YAGpBnD,GAAG,WAAY,SAAC4F,GACb,GAAIgF,EAAKxF,SAAU,OACnB,GAAIqH,OAAKqB,YAAYY,IAAIC,oBAAqB,CAC1CzD,EAAcU,MAAQ,EACtBhB,EAAK3E,QAAQ9C,gBAItB,CACH+H,EAAclL,GAAG,YAAa,SAAA4F,GAC1B,GAAI6G,OAAKqB,YAAYY,IAAIC,oBAAqB,CAC1CzD,EAActE,QACdsE,EACKE,UAAU,EAAG,SAAU,IACvBC,UACG,MACA,IAEHC,gBAAgB5H,EAAQA,EAAQ2G,EAAI3G,EAAS,EAAGyF,EAAIzF,EAAS,EAAG,GAChE6H,UACLX,EAAK3E,QAAQ9C,YAElBnD,GAAG,WAAY,SAAA4F,GACdsF,EAActE,QACdsE,EACKE,UAAU,EAAG,SAAU,IACvBC,UACG,EACA,IAEHC,gBAAgB5H,EAAQA,EAAQ2G,EAAI3G,EAAS,EAAGyF,EAAIzF,EAAS,EAAG,GAChE6H,UACLX,EAAK3E,QAAQ9C,WAKrBzG,KAAK6K,YAAYgB,SAAS2C,GAE1BxO,KAAKiI,QAAQoG,KAAKG,yCAGVpM,GAAqB,IAAjB8P,EAAiB7I,UAAAC,OAAA,GAAAD,UAAA,KAAAnJ,UAAAmJ,UAAA,GAAP,MACtByH,QAAQC,IAAI3O,EAAI8P,EAAS,eACzB,IAAIC,EAAM,MACV,IAAKnS,KAAK6K,YAAa,OACvB7K,KAAK6K,YAAYuH,SAAS9F,IAAI,SAACuB,GAC3BiD,QAAQC,IAAI,MAAO3O,EAAIyL,EAAK0B,KAAKE,aAAc5B,EAAK0B,KAAKE,cAAgBrN,GACzE,IAAKyL,IAASA,EAAKuE,SAAU,OACtB,GAAIvE,EAAKuE,SAAS9I,OAAS,EAAG,CACjC,IAAKlH,EAAI,CACLyL,EAAKuE,SAAS,GAAGlD,MAAQ,OACtB,GAAIgD,EAAU9P,EAAGiQ,SAASxE,EAAK0B,KAAKE,cAAgB,GAAK5B,EAAK0B,KAAKE,cAAgBrN,EAAI,CAC1FyL,EAAKuE,SAAS,GAAGlD,MAAQ,EACzBrB,EAAKuE,SAAS,GAAGlD,MAAQ,EACzBiD,EAAM,SACH,CACHtE,EAAKuE,SAAS,GAAGlD,MAAQ,MAIrC,IAAKiD,GAAO/P,EAAIpC,KAAKmR,cACrBnR,KAAKuJ,QAAQ9C,mDAGD8I,GACZvP,KAAKsS,kBAAoB/C,EACzBvP,KAAKuS,qBACLvS,KAAKuJ,QAAQ9C,gDAGJlD,GAAO,IAAAiP,EAAAxS,KAChB,IAAKuD,GAASA,EAAQ,EAAG,OACzB,IAAIkP,EAAQ,SAARA,EAAS9F,GAAD,OAAQ/D,IAAE8J,UAAUF,EAAKG,UAAW,CAAEvQ,GAAIuK,KACtD3M,KAAKyI,iBAAmBmK,MAAMC,QAAQtP,GAASA,EAAM+I,IAAImG,GAAS,CAACA,EAAMlP,IACzEvD,KAAKuS,qBACLvS,KAAKuJ,QAAQ9C,6CAGPqM,EAAQC,GACd,GAAItL,EAAOqJ,QAAQC,IAAI,MAAO+B,GAC9B,IAAKA,GAAUA,EAAS,EAAG,OAC3B9S,KAAKgM,YAAc8G,EACnB9S,KAAKgT,cAAgBD,EACrB/S,KAAKuS,qBACLvS,KAAKuJ,QAAQ9C,wDAKb,IAAIwM,EAAOjT,KAAK2L,mBAEhB,IAAIlD,EAAmBzI,KAAKyI,iBAC5B,IAAIyK,EAAc,IAClB,IAAIC,EAAcnT,KAAK8F,OAAOwD,OANb,IAAA8J,EAQYpT,KAAKoO,mBAAmB,GAA/C7F,EARW6K,EAQX7K,EAAGC,EARQ4K,EAQR5K,EAAGmF,EARKyF,EAQLzF,EAAGlB,EARE2G,EAQF3G,EAAGzF,EARDoM,EAQCpM,OAElB,IAAIqM,EAAM,IAAI9H,cACd8H,EACK3E,UAAU,EAAG,SAAU,IACvBK,SAAS,EAAG,EAAGpB,EAAI3G,EAAS,EAAGkM,GAC/BrE,UAEL,IAAIyE,EAAO,IAAI/H,cACf+H,EACK3E,UAAU,EAAG,IACbI,SAAS,EAAG,EAAGpB,EAAGuF,GAClBrE,UAELoE,EAAKM,iBACL9K,EAAiB6D,IAAI,SAAAK,GACjB,IAAI6G,EAAM,IAAIjI,YAAY8H,EAAII,mBAC9BD,EAAI1R,SAASyG,GAAKoF,EAAI3G,GAAU2F,EAAK3F,EACrCiM,EAAKpH,SAAS2H,KAGlB,IAAIE,EAAS,IAAId,MAAM,IAAIe,KAAK,GAChCD,EAASA,EAAOpH,IAAI,SAACsH,EAAMjH,GAAP,OAAcA,IAClC+G,EAAS9K,IAAEiL,WAAWH,EAAQ1T,KAAKgM,YAAYhC,OAAOvB,IAGtDiL,EAAOpH,IAAI,SAACK,GACR,IAAI6G,EAAM,IAAIjI,YAAY+H,EAAKG,mBAC/BD,EAAI1R,SAASyG,EAAIoE,GAAM,EAAI,GAAKgB,EAAI3G,GAAU2F,EAC9CsG,EAAKpH,SAAS2H,KAGlBxT,KAAK8T,gEAML,IAAIb,EAAOjT,KAAK2L,mBAEhB,IAAK,IAAI1J,EAAQ,EAAGA,EAAQjC,KAAKgM,YAAY1C,OAAQrH,IAAS,CAC1D,IAAK,IAAI8R,EAAW,EAAGA,EAAW/T,KAAK+I,QAAQO,OAAQyK,IAAY,CAE/D,IAAIC,EAAUhU,KAAKgT,cAAc/Q,GACjC,IAAIgS,EAAqBrL,IAAEuH,KAAKnQ,KAAKsS,kBAAmB,CAAExM,OAAQkO,IAClE,IAAIE,EAAgBD,EAAqBA,EAAmBnF,aAAe,KAE3EgC,QAAQC,IAAI,OAAQkD,EAAoBC,GAExC,GAAIA,GAAiBA,EAAc7B,QAAQrS,KAAK+I,QAAQgL,KAAc,EAAG,KAAAI,EACxCnU,KAAKoO,mBAAmB2F,GAA/CxL,EAD+D4L,EAC/D5L,EAAGC,EAD4D2L,EAC5D3L,EAAGmF,EADyDwG,EACzDxG,EAAGlB,EADsD0H,EACtD1H,EAAGzF,EADmDmN,EACnDnN,OAClB,IAAIgI,EAAS,IAAIzD,cACjByD,EACKN,UAAU,EAAG,SAAU,IACvBC,UACG,SACA,IAEHI,SAAS,EAAG,EAAGpB,EAAI3G,EAAS,EAAGyF,GAC/BoC,UACLG,EAAOlN,SAASyG,EAAIvI,KAAKgM,YAAY/J,IAAU0L,EAAI3G,GAAUA,EAC7DgI,EAAOlN,SAAS0G,EAAIA,EACpByK,EAAKpH,SAASmD,kDAQ1BhP,KAAKoU,SAAW,4CAIRvG,EAAMlB,EAAImD,EAAQvF,GAC1BuF,EAAOP,KAAKhF,MAAQA,EAAMgF,KAE1BO,EAAOsE,SAAW,KAClBtE,EAAOxH,MAAQ,MACfwH,EAAOuE,cAAgBvE,EAAOP,KAAKhF,MAAM+J,iBAAiBtU,KAAKuJ,QAAQuC,iDAG3DyI,EAAW/L,EAAGvG,EAAOgG,GAEjC,IAAIuM,EAAgB,KAEpBxU,KAAKkI,aAAajG,EAAMG,IAAIkK,IAAI,SAACzF,EAAKvH,GAClC,GAAIkJ,GAAK3B,EAAI2B,GAAKA,GAAK3B,EAAI2B,EAAI3B,EAAI4F,EAAI,GAAI,CACvC+H,EAAgBlV,KAIxB,GAAIkV,IAAkB,KAAM,CACxBA,EAAgBxU,KAAKkI,aAAajG,EAAMG,IAAIkH,OAGhD,IAAImL,EAAgB,KACpB,IAAIC,EAAe,KAEnB,IAAIC,EAAS3U,KAAKiI,QACbqE,IAAI,SAACwD,GAEF,GAAIA,EAAOP,KAAKC,SAAWvN,EAAMG,GAAI,OAAO0N,IAE/C8E,OAAO,SAAAC,GAAC,OAAKA,EAAI,KAAO,QACxBvI,IAAI,SAACwD,EAAQxQ,GACV,GAAIA,GAAKiV,EAAW,CAChB,GAAIA,GAAaC,EAAe,CAC5B,GAAIlV,GAAKkV,EAAe,CACpBE,EAAe5E,MACZ,CACH,OAAOA,OAER,CACH,OAAOA,OAER,CACH2E,EAAgB3E,KAGvB8E,OAAO,SAAAC,GAAC,OAAKA,EAAI,KAAO,QAE7B7U,KAAK8U,iBAAmBP,EAAYC,EAAgB,EAAI,EAExD,GAAIxU,KAAK+E,UAAY,SAAU,CAC3B,GAAI2P,EAAcC,EAAOI,OAAOR,EAAYvU,KAAK8U,iBAAkB,EAAGJ,GACtEC,EAAOI,OAAOP,EAAe,EAAGC,QAC7B,GAAIzU,KAAK+E,UAAY,UAAW,OAKhC,GAAI/E,KAAK+E,UAAY,QAAS,CACjC,GAAI2P,EAAcC,EAAOI,OAAOR,EAAYvU,KAAK8U,iBAAkB,EAAGJ,GACtEC,EAAOI,OAAOP,EAAe,EAAGC,GAGpC,GAAIhN,EAAOqJ,QAAQC,IAAI,cAAewD,EAAWA,EAAYvU,KAAK8U,iBAAkBN,EAAeC,EAAeC,GAElH1U,KAAKgV,iBAAmBT,EAAYvU,KAAK8U,iBACzC9U,KAAKiV,gBAAkBT,EAEvB,GAAIxU,KAAKiV,iBAAmBjV,KAAKgV,iBAAkB,CAC/ChV,KAAKgV,iBAAmBT,EAG5B,GAAIvU,KAAK+E,UAAY,QAAS,MAEvB,uCAeDmQ,EAAGL,EAAGhH,EAAMsH,EAAKlT,GAAO,IAAAmT,EAAApV,KAC9B6U,EAAE3F,MAAQ,EACV2F,EAAET,SAAW,MACb,IAAIzH,EAAK,EACT3M,KAAKqV,aAAe,GAEpB,GAAI5N,EAAOqJ,QAAQC,IAAI,QAAS8D,EAAEvM,OAClC,GAAIuM,EAAEvM,OAAS,MAAO,CAGlB,IAAIgN,EAAkB,eAAAC,EAAAC,IAAAC,EAAAlJ,EAAAmJ,KAAG,SAAAC,EAAOvT,EAAIwT,GAAX,IAAAC,EAAAtN,EAAAC,EAAAmF,EAAAlB,EAAAzF,EAAA,OAAAyO,EAAAlJ,EAAAuJ,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAAAF,EAAAE,KAAA,SACfnG,OAAKoG,IAAIC,0BAA0BhU,EAAI,CAAE0M,aAAc8G,IADxC,OAAAC,EAEQT,EAAKhH,mBAAmBzB,GAA/CpE,EAFesN,EAEftN,EAAGC,EAFYqN,EAEZrN,EAAGmF,EAFSkI,EAETlI,EAAGlB,EAFMoJ,EAENpJ,EAAGzF,EAFG6O,EAEH7O,OAClB,GAAI4O,GAAS,MAAO,CAChB,GAAIf,EAAEwB,IAAKxB,EAAEyB,YAAYzB,EAAEwB,SACxB,EAGPxI,EAAKiB,aAAe8G,EACpBR,EAAK7L,QAAQ9C,SACb2O,EAAK5L,IAAI+M,kBAAkB,OAVN,wBAAAP,EAAAjK,UAAA4J,MAAH,gBAAlBL,EAAkBkB,EAAAC,GAAA,OAAAlB,EAAAmB,MAAA1W,KAAAqJ,YAAA,GAYtB,GAAI5B,EAAOqJ,QAAQC,IAAIlD,EAAKiB,cAG5B,IAAI6H,EAAgB3W,KAAKgM,YAAYqG,QAAQ8C,GAC7C,IAAIyB,EAAgB5W,KAAKgT,cAAc2D,GAEvC,GAAIC,EAAe,CACf,IAAI3C,EAAqBrL,IAAEuH,KAAKnQ,KAAKsS,kBAAmB,CAAExM,OAAQ8Q,IAClE5W,KAAKwJ,IAAI8L,mBAAmBzH,EAAK7J,OAAQiQ,GACzCY,EAAEvM,MAAQ,MACVtI,KAAKqV,aAAahH,KAAKpM,EAAMG,UAO9B,GAAIyS,EAAEvM,QAAU,KAAM,CAEzB,GAAIb,EAAOqJ,QAAQC,IAAI,MAAOmE,EAAGL,EAAGhH,EAAMsH,EAAKlT,GAC/C,IAAIoG,EAAQpG,EAAM8F,MAAMuE,IAAI,SAAAuB,GAAI,MAAK,CAAEC,MAAOD,EAAKC,SAInD,IAAI+I,EAAY7W,KAAK+E,UAAY,QAEjC,GAAI8R,EAAW,CACX,IAAIC,EAAuBlO,IAAE8J,UAAUrK,EAAO,CAAEyF,MAAO9N,KAAKgV,mBAC5D,IAAI+B,EAAqBnO,IAAE8J,UAAUrK,EAAO,CAAEyF,MAAO9N,KAAKiV,kBAE1D,IAAI1F,EAAOtN,EAAM8F,MAAM+O,GACvB,GAAIrP,EAAOqJ,QAAQC,IAAI,cAAe,MAAOxB,GAC7C,GAAIA,EAAKyH,SAAW,EAAG,CACnBzH,EAAOtN,EAAM8F,MAAMgP,GACnB,GAAItP,EAAOqJ,QAAQC,IAAI,cAAe,OAAQxB,GAGlDtN,EAAM8F,MAAMgN,OAAO/U,KAAKiV,gBAAiB,EACrC,CACI7S,GAAImN,EAAKE,YAAa7B,KAAM,YAAaqJ,MAAO,KAChDjT,OAAQhE,KAAK+I,QAAQsJ,QAAQ9C,EAAKvL,QAClC8J,MAAO9N,KAAKiV,gBAAiB+B,OAAQzH,EAAKE,cAGlDzP,KAAKqV,aAAahH,KAAKpM,EAAMG,QAG1B,CAEH,IAAI0U,EAAuBlO,IAAE8J,UAAUrK,EAAO,CAAEyF,MAAO9N,KAAKgV,mBAC5D,IAAI+B,EAAqBnO,IAAE8J,UAAUrK,EAAO,CAAEyF,MAAO9N,KAAKiV,kBAE1D,IAAI1F,EAAOtN,EAAM8F,MAAM+O,GACvB,GAAIrP,EAAOqJ,QAAQC,IAAI,cAAe+F,EAAsBC,EAAoB9U,EAAM8F,MAAO,MAAOwH,GACpG,GAAIA,EAAKyH,SAAW,EAAG,CACnBzH,EAAOtN,EAAM8F,MAAMgP,GACnB,GAAItP,EAAOqJ,QAAQC,IAAI,cAAe,OAAQxB,GAIlDtN,EAAM8F,MAAMgN,OAAO/U,KAAKgV,iBAAkB,EAAG,CAAE5S,IAAK,EAAG0L,MAAO9N,KAAKgV,iBAAkBvG,KAAM,OAE3FxM,EAAM8F,MAAMgN,OAAO/U,KAAKiV,gBAAiB,EACrC,CACI7S,GAAImN,EAAKE,YAAa7B,KAAM,YAAaqJ,MAAO,KAChDjT,OAAQhE,KAAK+I,QAAQsJ,QAAQ9C,EAAKvL,QAClC8J,MAAO9N,KAAKiV,gBAAiB+B,OAAQzH,EAAKE,cAGlDzP,KAAKqV,aAAahH,KAAKpM,EAAMG,IAQjCpC,KAAKwJ,IAAI+M,kBAAkB,4CAIxBW,EAAaC,EAAiBrH,EAAQ5G,EAAGpD,GAChD,GAAIgK,EAAOsE,SAAU,CACjBpU,KAAKuJ,QAAQ9C,SACb,IAAIO,EAAS,EACb,GAAI8I,EAAOxH,MAAO,CACd,IAAI8O,EAActH,EAAOP,KAAKhF,MAAM+J,iBAAiBtU,KAAKuJ,QAAQuC,OAClE,IAAIuL,EAAI1M,KAAK+B,MAAM0K,EAAY7O,EAAIvI,KAAKsI,MAAMC,GAAKvB,GAAUA,EAC7D8I,EAAOhO,SAASyG,EAAI8O,EAAI,GACxBrX,KAAKsX,gBAAgBH,EAAiBE,EAAGvR,EAAQgK,OAE9C,CACH,IAAIsH,EAActH,EAAOP,KAAKhF,MAAM+J,iBAAiBtU,KAAKuJ,QAAQuC,OAClE,IAAIyL,EAAczH,EAAOuE,cAEzB,IAAImD,EAAI7M,KAAK8M,KACT9M,KAAK+M,IAAIN,EAAY5O,EAAI+O,EAAY/O,EAAG,GACxCmC,KAAK+M,IAAIN,EAAY7O,EAAIgP,EAAYhP,EAAG,IAE5CuH,EAAOxH,MAAQ,MACf,GAAIkP,EAAIxQ,EAAQ,CACZ8I,EAAOxH,MAAQ,KACftI,KAAK6K,YAAYyL,YAAYxG,GAC7B9P,KAAK6K,YAAYoE,WAAWa,EAAQ9P,KAAK6K,YAAYuH,SAAS9I,8CAO1EtJ,KAAK6K,YAAY/I,SAASyG,EAAIvI,KAAKsI,MAAMC,EACzCvI,KAAK2L,mBAAmB7J,SAASyG,EAAIvI,KAAKsI,MAAMC,EAChDvI,KAAKwJ,IAAIvG,MAAM0U,YAAY5N,MAAM6N,UAAjC,cAAA5N,OAA2DhK,KAAKsI,MAAMC,EAAtE,OAEAvI,KAAKuJ,QAAQ9C,yCAGXoR,GAAI,IAAAC,EAAA9X,KAENA,KAAKgI,OAAOsE,IAAI,SAAClJ,EAAO2U,GACpB,GAAI3U,EAAO,CACP0U,EAAKvO,QAAQuC,MAAMwK,YAAYlT,GAC/B0U,EAAK9P,OAAO+P,GAAK,KACjB3U,EAAQ,QAIhBpD,KAAKiI,QAAQqE,IAAI,SAACwD,EAAQiI,GACtB,GAAIjI,EAAQ,CACRA,EAAOkI,qBACPF,EAAKjN,YAAYyL,YAAYxG,GAC7BgI,EAAK7P,QAAQ8P,GAAK,KAClBjI,EAAS,QAIjB9P,KAAKgI,OAAS,GACdhI,KAAKiI,QAAU,GACfjI,KAAKkI,aAAe,GAEpB,IAAKlI,KAAK8H,QAAS,KAAAmQ,EACSjY,KAAK0J,UAAvB9H,EADSqW,EACTrW,MAAOoC,EADEiU,EACFjU,OACb,IAAIkU,EAAW,GACf,IAAK,IAAIH,EAAI,EAAGA,EAAInW,EAAQsW,EAAUH,IAAK,CACvC,IAAK,IAAII,EAAI,EAAGA,EAAInU,EAASkU,EAAUC,IAAK,CACxCnY,KAAKiK,oBACAyE,UAAU,EAAG,SAAW,GACxBK,SAASgJ,EAAIG,EAAUC,EAAID,EAAU,EAAG,IAGrDlY,KAAK8H,QAAU,KAGnB,IAAI6E,EAAK,EAET,GAAI3M,KAAK8F,OAAOwD,OAAS,EAAG,CACxB,IAAI8O,EAAQzN,KAAK8C,MAAMzN,KAAKsI,MAAMC,EAAI,IACtCvI,KAAK8F,OAAOwG,IAAI,SAACrK,EAAO0K,GACpBmE,QAAQC,IAAI,KAAM9O,GAClB,IAAIoW,EAAUzP,IAAE0P,QAAQrW,EAAM8F,MAAO,CAAC,SAAU,CAAC,QAAQuE,IAAI,SAACuB,EAAMvO,GAChEwR,QAAQC,IAAI,OAAQlD,GACpBiK,EAAKS,SAAStW,EAAO3C,EAAGuO,EAAMA,EAAKD,KAAMiK,OAKrD7X,KAAKuS,qBAELvS,KAAKuJ,QAAQ9C,4CAGR+R,EAAMjQ,EAAGC,EAAGiQ,GACjB,OACA,IAAIlP,EAAUvJ,KAAKuJ,QACnB,IAAImP,EAAaC,OAAOC,OAAOJ,GAC/BxY,KAAKgI,OAAOqG,KAAKqK,GACjBD,EAAE5M,SAAS6M,GACXA,EAAW5W,SAAS+W,IAAI,GAAI,oBAKpC,IAAMjP,EAAW,IAAIlC,EChyBrB,IAAIoR,EAAM,WAAgB,IAAA5X,EAAAlB,KAAa,IAAAmB,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,UAAoBmB,MAAA,CAAOI,KAAA,oBAA0B,CAAAvB,EAAA,OAAYE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,OAAYE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,YAAiBmB,MAAA,CAAOmC,eAAAzD,EAAAqB,cAAAwW,WAAA,aAAArU,QAAAxD,EAAA8X,WAAAlU,gBAAA,mBAAAmU,eAAA,gBAAqJ3V,GAAA,CAAK4V,KAAA,SAAAC,GAAoB,OAAAjY,EAAAkY,YAAA,GAAAD,EAAAzV,YAAwC,KAAAxC,EAAA,cAAAG,EAAA,OAAsCE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,SAAcmB,MAAA,CAAOY,MAAA,gBAAuB,CAAA/B,EAAA,gBAAqBmB,MAAA,CAAO6W,MAAA,cAAAC,QAAA,WAA0ChW,GAAA,CAAK4V,KAAAhY,EAAAqY,WAAqB9V,MAAA,CAAQC,MAAAxC,EAAA,kBAAAyC,SAAA,SAAAC,GAAuD1C,EAAAsY,kBAAA5V,GAA0BE,WAAA,sBAAiC,CAAAzC,EAAA,WAAgBmB,MAAA,CAAOoL,KAAA,QAAcnK,MAAA,CAAQC,MAAAxC,EAAA,kBAAAyC,SAAA,SAAAC,GAAuD1C,EAAAsY,kBAAA5V,GAA0BE,WAAA,wBAAiC,OAAAzC,EAAA,SAAsBmB,MAAA,CAAOY,MAAA,UAAiBE,GAAA,CAAKoC,MAAAxE,EAAAuY,WAAoB,KAAAvY,EAAA6C,KAAA7C,EAAA,cAAAG,EAAA,OAA+CE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAL,EAAA,cAAAG,EAAA,UAAmCmB,MAAA,CAAOkX,MAAAxY,EAAAyY,KAAA7W,KAAA,OAAAkC,iBAAA,iBAAA4U,WAAA,QAAAC,qBAAA,wBAA+H3Y,EAAA6C,MAAA,KAAA7C,EAAA6C,YAC91C,IAAI+V,EAAe,mBC4BnB,IAAAC,EAAA,CACAnX,KAAA,eAEAoX,MAAA,CACAlU,OAAA8M,OAGAqH,WAAAvM,IAAA,GACAwM,EAAA,MAGA3K,KAXA,SAAAA,IAYA,OACAoK,KAAA,GACApX,eAAA,EACAiX,kBAAA,KAIAW,SAAA,CACAnB,WADA,SAAAA,IAEA,OAAAhZ,KAAA8F,OAAAwG,IAAA,SAAArK,GAAA,OACAmB,MAAAnB,EAAAW,KACAc,MAAA4K,SAAArM,EAAAG,SAKAgY,MAAA,CACApB,WADA,SAAAA,MAKAlT,OALA,SAAAA,IAMA,GAAA9F,KAAA8F,OAAA,CACA,IAAA7D,EAAA2G,EAAAuH,KAAAnQ,KAAA8F,OAAA,CAAA1D,GAAApC,KAAAuC,gBACA,GAAAN,EAAAjC,KAAAwZ,kBAAAvX,EAAAW,KACA5C,KAAA2Z,KAAA1X,EAAA0X,OAIAtX,OAbA,SAAAA,EAaAI,EAAA4X,GACAra,KAAAuC,eAAAvC,KAAAqC,OAAAC,OAAAC,cACA,GAAAvC,KAAA8F,OAAA,CACA,IAAA7D,EAAA2G,EAAAuH,KAAAnQ,KAAA8F,OAAA,CAAA1D,GAAApC,KAAAuC,gBACA,GAAAN,EAAAjC,KAAAwZ,kBAAAvX,EAAAW,KACA5C,KAAA2Z,KAAA1X,EAAA0X,QAKAW,QAnDA,SAAAA,IAoDAta,KAAAuC,eAAAvC,KAAAqC,OAAAC,OAAAC,cACA,GAAAvC,KAAA8F,OAAA,CACA,IAAA7D,EAAA2G,EAAAuH,KAAAnQ,KAAA8F,OAAA,CAAA1D,GAAApC,KAAAuC,gBACA,GAAAN,EAAAjC,KAAAwZ,kBAAAvX,EAAAW,OAIA2X,QAAA,CACAhB,UADA,eAAAiB,EAAAhF,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAC,EACA/S,GADA,IAAA6X,EAAA1U,EAAA,OAAA0P,EAAAlJ,EAAAuJ,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAEAuE,EAAA1K,EAAA,KAAAoG,IAAAuE,aAAA1a,KAAAqC,OAAAC,OAAAC,cAAA,CACAK,SAHAoT,EAAAE,KAAA,SAKAuE,EALA,OAKA1U,EALAiQ,EAAA2E,KAMA3a,KAAA4a,MAAA,cACA5a,KAAA4a,MAAA,UAPA,wBAAA5E,EAAAjK,UAAA4J,EAAA3V,SAAA,SAAAuZ,EAAA/C,GAAA,OAAAgE,EAAA9D,MAAA1W,KAAAqJ,WAAA,OAAAkQ,EAAA,GAUAE,OAVA,SAAAA,IAUA,IAAA9R,EAAA3H,KACAA,KAAA6a,GACAC,OAAA,CACAzB,MAAA,SACA0B,QAAA,gCACAC,GAAA,KACAC,OAAA,WAEAC,KAPM1F,IAAAC,EAAAlJ,EAAAmJ,KAON,SAAAyF,IAAA,OAAA1F,EAAAlJ,EAAAuJ,KAAA,SAAAsF,EAAAC,GAAA,gBAAAA,EAAApF,KAAAoF,EAAAnF,MAAA,OAAAmF,EAAAnF,KAAA,SACAnG,EAAA,KAAAoG,IAAAmF,aAAA3T,EAAApF,eADA,OAEAoF,EAAAiT,MAAA,cACAjT,EAAAiT,MAAA,UAHA,wBAAAS,EAAAtP,UAAAoP,QAOA/B,YAzBA,SAAAA,EAyBA5J,GACAxP,KAAAub,QAAAlN,KAAA,cAAArE,OACAhK,KAAAqC,OAAAC,OAAAsP,mBADA,KAAA5H,OAEAhK,KAAAqC,OAAAC,OAAAkZ,cAFA,KAAAxR,OAGAwF,KAIAiM,uBAjCA,SAAAA,EAiCAC,GAAA,IAAAtR,EAAApK,KACA+P,EAAA,KAAAoG,IAAAwF,gBAAA3b,KAAA4b,KAAAxZ,GAAA,CACAyZ,kBAAAH,IACAI,UAAA,WACA1R,EAAAwQ,MAAA,sBC7HqO,IAAAmB,EAAA,kCCQrO,IAAA3L,EAAgB4L,OAAAC,EAAA,KAAAD,CACdD,EACAjD,EACAgB,EACF,MACA,KACA,KACA,MAIe,IAAAoC,GAAA9L,UCnBf,IAAI+L,GAAM,WAAgB,IAAAjb,EAAAlB,KAAa,IAAAmB,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,UAAoBmB,MAAA,CAAOI,KAAA,kBAAA1B,EAAAkb,oBAAoD,CAAA/a,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAL,EAAA8E,WAAAsD,OAAA,EAAAjI,EAAA,OAAwCE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,YAAiBmB,MAAA,CAAOmC,eAAAzD,EAAAmb,SAAAtD,WAAA,aAAArU,QAAAxD,EAAA8E,WAAAlB,gBAAA,kBAAAmU,eAAA,gBAA+I3V,GAAA,CAAK4V,KAAA,SAAAC,GAAoB,OAAAjY,EAAAob,YAAAnD,EAAAzV,YAAqC,KAAAxC,EAAA6C,KAAA1C,EAAA,OAA2BE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAL,EAAAsa,gBAAA,EAAAna,EAAA,OAAsCE,YAAA,MAAiB,CAAAF,EAAA,SAAcmB,MAAA,CAAOY,MAAA,YAAkB/B,EAAA,gBAAqBmB,MAAA,CAAO6W,MAAA,cAAAC,QAAA,WAA0ChW,GAAA,CAAK4V,KAAAhY,EAAAqb,WAAqB9Y,MAAA,CAAQC,MAAAxC,EAAA,kBAAAyC,SAAA,SAAAC,GAAuD1C,EAAAkb,kBAAAxY,GAA0BE,WAAA,sBAAiC,CAAAzC,EAAA,WAAgBmB,MAAA,CAAOoL,KAAA,QAAcnK,MAAA,CAAQC,MAAAxC,EAAA,kBAAAyC,SAAA,SAAAC,GAAuD1C,EAAAkb,kBAAAxY,GAA0BE,WAAA,wBAAiC,OAAA5C,EAAA6C,KAAA1C,EAAA,OAA6BE,YAAA,MAAiB,CAAAF,EAAA,SAAcmB,MAAA,CAAOY,MAAA,aAAmB/B,EAAA,gBAAqBmB,MAAA,CAAO6W,MAAA,gBAAAC,QAAA,WAA4ChW,GAAA,CAAK4V,KAAAhY,EAAAsb,aAAuB/Y,MAAA,CAAQC,MAAAxC,EAAA,aAAAyC,SAAA,SAAAC,GAAkD1C,EAAAub,aAAA7Y,GAAqBE,WAAA,iBAA4B,CAAAzC,EAAA,WAAgBmB,MAAA,CAAOoL,KAAA,QAAcnK,MAAA,CAAQC,MAAAxC,EAAA,aAAAyC,SAAA,SAAAC,GAAkD1C,EAAAub,aAAA7Y,GAAqBE,WAAA,mBAA4B,GAAA5C,EAAA,SAAAG,EAAA,SAAiCmB,MAAA,CAAOY,MAAA,UAAiBE,GAAA,CAAKoC,MAAAxE,EAAAuY,UAAoBvY,EAAA6C,MAAA,OAAA7C,EAAA8E,WAAAsD,OAAA,EAAAjI,EAAA,OAAyDE,YAAA,KAAgB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAL,EAAA,SAAAG,EAAA,UAA8BmB,MAAA,CAAOkX,MAAAxY,EAAAyY,KAAA5W,MAAA,QAAAD,KAAA,OAAAkC,iBAAA,iBAAA4U,WAAA,QAAAC,qBAAA,wBAA+I3Y,EAAA6C,MAAA,KAAA7C,EAAA6C,OAAA7C,EAAA8E,WAAAsD,OAAA,GAAAjI,EAAA,OAAmEE,YAAA,KAAgB,CAAAF,EAAA,OAAAA,EAAA,KAAAH,EAAAwB,GAAA,qDAAAxB,EAAA6C,YACn9D,IAAI2Y,GAAe,oBCqCnB,IAAAC,GAAA,CACA/Z,KAAA,cAEA2M,KAHA,SAAAA,IAGA,IAAAgG,EACA,OAAAA,EAAA,CACA6G,kBAAA,GACAK,aAAA,GACAG,iBAAA,GACA5W,WAAA,IAJA6W,IAAAtH,EAAA,eAKA,IALAsH,IAAAtH,EAAA,iBAMA,GANAsH,IAAAtH,EAAA,YAOA,GAPAsH,IAAAtH,EAAA,UAQA,OARAsH,IAAAtH,EAAA,OASA,IATAA,GAaAyE,MAAA,CACA8C,aAAA,CAAAzc,OAAA0c,QACA3W,eAAAwM,MACA3M,oBAAA2M,MACA5M,WAAA4M,OAGAqH,WAAAvM,IAAA,GACAwM,EAAA,MAGAE,MAAA,CACAhU,eADA,SAAAA,IAEApG,KAAAwb,cAAAxb,KAAAqC,OAAAC,OAAAkZ,eAIAY,kBANA,SAAAA,MAQApW,WARA,SAAAA,IASA,IACAhG,KAAAwb,eACAxb,KAAAgG,WAAAsD,OAAA,IACAtJ,KAAAqC,OAAAC,OAAAkZ,cACA,CACAxb,KAAAwb,cAAAxb,KAAAgG,WAAA,GAAAtC,MACA1D,KAAAsc,YAAAtc,KAAAgG,WAAA,GAAAtC,OAEA1D,KAAAqc,SAAA/N,SAAAtO,KAAAqC,OAAAC,OAAAkZ,gBAGAa,SApBA,SAAAA,EAoBAja,GAAA,IAAAuF,EAAA3H,KACAA,KAAAgG,WAAAsG,IAAA,SAAA0Q,GACA,GAAAA,QAAA5a,MAAA,CAEAuF,EAAAyU,kBAAAY,EAAA5Z,MACAuE,EAAAgS,KAAAqD,QAAArD,KACA7I,QAAAC,IAAA,OAAAiM,MAGAhd,KAAA4a,MAAA,gBAIAN,QA7DA,SAAAA,MAiEAC,QAAA,CACA+B,YADA,SAAAA,EACAW,GACAjd,KAAAwb,cAAAyB,EACA,IAAAC,EAAAld,KAAAqC,OAAAC,OAAAC,cACAvC,KAAAub,QAAAlN,KAAA,cAAArE,OAEAhK,KAAAqC,OAAAC,OAAAsP,mBAFA,KAAA5H,OAGAiT,GAHAjT,OAGAkT,EAAA,IAAAA,EAAA,KAEAld,KAAAqc,SAAA/N,SAAAtO,KAAAqC,OAAAC,OAAAkZ,gBAGA2B,yBAZA,eAAAC,EAAA5H,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAC,IAAA,OAAAF,EAAAlJ,EAAAuJ,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAaAlW,KAAAoG,eAbA,wBAAA4P,EAAAjK,UAAA4J,EAAA3V,SAAA,SAAAmd,IAAA,OAAAC,EAAA1G,MAAA1W,KAAAqJ,WAAA,OAAA8T,EAAA,GAgBAZ,UAhBA,eAAAc,EAAA7H,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAyF,EAgBAvY,GAhBA,IAAA6X,EAAA1U,EAAA,OAAA0P,EAAAlJ,EAAAuJ,KAAA,SAAAsF,EAAAC,GAAA,gBAAAA,EAAApF,KAAAoF,EAAAnF,MAAA,OAiBAuE,EAAA1K,EAAA,KAAAoG,IAAA3S,YAAAxD,KAAAqc,SAAA,CACAzZ,SAlBAyY,EAAAnF,KAAA,SAoBAuE,EApBA,OAoBA1U,EApBAsV,EAAAV,KAqBA3a,KAAA4a,MAAA,cArBA,wBAAAS,EAAAtP,UAAAoP,EAAAnb,SAAA,SAAAuc,EAAA/F,GAAA,OAAA6G,EAAA3G,MAAA1W,KAAAqJ,WAAA,OAAAkT,EAAA,GAwBA9C,OAxBA,SAAAA,IAwBA,IAAArP,EAAApK,KACAA,KAAA6a,GACAC,OAAA,CACAzB,MAAA,SACA0B,QAAA,gCACAC,GAAA,KACAC,OAAA,WAEAC,KAPM1F,IAAAC,EAAAlJ,EAAAmJ,KAON,SAAA4H,IAAA,OAAA7H,EAAAlJ,EAAAuJ,KAAA,SAAAyH,EAAAC,GAAA,gBAAAA,EAAAvH,KAAAuH,EAAAtH,MAAA,OAAAsH,EAAAtH,KAAA,SACAnG,EAAA,KAAAoG,IAAAsH,YAAArT,EAAAiS,UADA,OAEAjS,EAAAwQ,MAAA,cACAxQ,EAAAwQ,MAAA,UACAxQ,EAAAiS,UAAA,EAJA,wBAAAmB,EAAAzR,UAAAuR,QAQAd,YAxCA,eAAAkB,EAAAlI,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAiI,EAwCA/a,GAxCA,IAAA6X,EAAA1U,EAAA,OAAA0P,EAAAlJ,EAAAuJ,KAAA,SAAA8H,EAAAC,GAAA,gBAAAA,EAAA5H,KAAA4H,EAAA3H,MAAA,OAyCAuE,EAAA1K,EAAA,KAAAoG,IAAAqG,YAAA,CACAsB,WAAA9d,KAAAqC,OAAAC,OAAAsP,mBACAhP,SA3CAib,EAAA3H,KAAA,SA6CAuE,EA7CA,OA6CA1U,EA7CA8X,EAAAlD,KA8CA3a,KAAAsc,YAAAvW,EAAA3D,IACApC,KAAA4a,MAAA,cA/CA,wBAAAiD,EAAA9R,UAAA4R,EAAA3d,SAAA,SAAAwc,EAAA/F,GAAA,OAAAiH,EAAAhH,MAAA1W,KAAAqJ,WAAA,OAAAmT,EAAA,GAkDAuB,uBAlDA,SAAAA,EAkDAd,GACA,IAAAe,EAAA,GACAhe,KAAAgG,WAAAsG,IAAA,SAAA0Q,GACA,GAAAA,QAAA5a,IAAA6a,EAAA,CACAe,EAAAhB,QAAAiB,kBAGA,OAAAD,GAGAxa,YA5DA,eAAA0a,EAAA1I,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAyI,EA4DA/X,GA5DA,IAAA6W,EAAAa,EAAAE,EAAAI,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA7J,EAAAlJ,EAAAuJ,KAAA,SAAAyJ,EAAAC,GAAA,gBAAAA,EAAAvJ,KAAAuJ,EAAAtJ,MAAA,OA6DAlW,KAAAmD,QAAA,KAIA8Z,EAAAjd,KAAAqc,SACAyB,EAAA9d,KAAAqC,OAAAC,OAAAsP,mBAEAoM,EAAAhe,KAAA+d,uBAAAd,GAEAmB,EAAA,SAAAA,EAAAnc,GAAA,OACA8N,EAAA,KAAAoG,IAAAsJ,yBAAA,CACAzC,MAAAC,EACAhb,QACA6b,gBAEAO,EAAA,SAAAA,EAAAjc,GAAA,OAAA2N,EAAA,KAAAoG,IAAAuJ,yBAAAtd,IAMAkc,EAAAlY,EACAmY,EAAAP,EAAA1R,IACA,SAAAqT,GAAA,OAAAA,EAAA7Z,SAQA0Y,EAAA,CACA/E,OAAA7Q,EAAAgX,eACArB,EACAD,EACA1V,EAAAiX,SAEA3P,IAAAtH,EAAAgX,eACAtB,EACAC,EACA3V,EAAAiX,UAMArB,EAAA/E,OAAA+E,EAAA/E,OAAAnN,IACA,SAAArK,GAAA,OAAA2G,EAAAuH,KAAA6N,EAAA,CAAAlY,OAAA7D,IAAAG,KA5GAqc,EAAA,KAAAC,EAAA,MAAAc,EAAAvJ,KAAA,GAAA2I,EAAAkB,IAkHAtB,EAAAtO,IAAA5D,IAAA8R,IAlHA,QAAAoB,EAAAtJ,KAAA,UAAA0I,EAAA1I,OAAA,QAAA2I,EAAAW,EAAA7E,KAAA8D,EAAAI,EAAAkB,KAAAP,EAAAtJ,KAAA,UAAA2I,EAAAnb,MAAA,QAAAob,EAAAU,EAAA7E,KAAA,GAAA8D,EAAA,CAAAe,EAAAtJ,KAAA,SAkHA6I,EAlHAD,EAAA,QAAAL,EAAA,KAAAe,EAAAtJ,KAAA,iBAAAsJ,EAAAtJ,KAAA,iBAAAsJ,EAAAvJ,KAAA,GAAAuJ,EAAAQ,GAAAR,EAAA,aAAAd,EAAA,KAAAC,EAAAa,EAAAQ,GAAA,QAAAR,EAAAvJ,KAAA,GAAAuJ,EAAAvJ,KAAA,SAAAwI,GAAAG,EAAAqB,QAAA,OAAAT,EAAAtJ,KAAA,SAAAsJ,EAAAtJ,KAAA,UAAA0I,EAAAqB,SAAA,QAAAT,EAAAvJ,KAAA,OAAAyI,EAAA,CAAAc,EAAAtJ,KAAA,eAAAyI,EAAA,eAAAa,EAAAU,OAAA,mBAAAV,EAAAU,OAAA,gBAoHA7B,EApHA,CAAAmB,EAAAtJ,KAAA,SAAA8I,EAAA,KAAAC,EAAA,MAAAO,EAAAvJ,KAAA,GAAAkJ,EAAAW,IAqHAtB,EAAA/E,OAAAnN,IACA+R,IAtHA,QAAAmB,EAAAtJ,KAAA,UAAAiJ,EAAAjJ,OAAA,QAAAkJ,EAAAI,EAAA7E,KAAAqE,EAAAI,EAAAW,KAAAP,EAAAtJ,KAAA,UAAAkJ,EAAA1b,MAAA,QAAA2b,EAAAG,EAAA7E,KAAA,GAAAqE,EAAA,CAAAQ,EAAAtJ,KAAA,SAqHAoJ,EArHAD,EAAA,QAAAL,EAAA,KAAAQ,EAAAtJ,KAAA,iBAAAsJ,EAAAtJ,KAAA,iBAAAsJ,EAAAvJ,KAAA,GAAAuJ,EAAAW,GAAAX,EAAA,aAAAP,EAAA,KAAAC,EAAAM,EAAAW,GAAA,QAAAX,EAAAvJ,KAAA,GAAAuJ,EAAAvJ,KAAA,SAAA+I,GAAAG,EAAAc,QAAA,OAAAT,EAAAtJ,KAAA,SAAAsJ,EAAAtJ,KAAA,UAAAiJ,EAAAc,SAAA,QAAAT,EAAAvJ,KAAA,OAAAgJ,EAAA,CAAAO,EAAAtJ,KAAA,eAAAgJ,EAAA,eAAAM,EAAAU,OAAA,mBAAAV,EAAAU,OAAA,YA2HAlgB,KAAA4a,MAAA,cACA5a,KAAA4a,MAAA,SAAAxU,GACA0K,QAAAC,IAAA,UAAA3K,GA7HA,yBAAAoZ,EAAAzT,UAAAoS,EAAAne,KAAA,mEAAAwD,EAAA4c,GAAA,OAAAlC,EAAAxH,MAAA1W,KAAAqJ,WAAA,OAAA7F,EAAA,KCvGoO,IAAA6c,GAAA,oBCQpO,IAAIC,GAAYtE,OAAAC,EAAA,KAAAD,CACdqE,GACAlE,GACAO,GACF,MACA,KACA,KACA,MAIe,IAAA6D,GAAAD,4BC0Jf,IAAAE,GAAA,CACAjZ,qBAAA,WACAC,uBAAA,cAGA,IAAAiZ,GAAArhB,EAAA,QAaA,IAAAshB,GAAA,MAEA,IAAAC,GAAA,CACA/d,KAAA,cAEAqX,WAAAvM,IAAA,CACAkT,WAAA1E,GACA2E,UAAAN,GACAO,QAAAC,GAAA,MACA7G,EAAA,MAGAF,MAAA,0CAEAzK,KAZA,SAAAA,IAaA,OACAyR,cAAA,KACAC,OAAA,UACAlc,SAAA,UACAH,SAAA,UACAiD,QAAA,MACAC,QAAA,MACAC,MAAA,GACAC,OAAA,GACAC,QAAA,GACAS,SAAA,MACAR,aAAA,GACAnC,YAAA,KACAgD,QAAA,GACAlF,sBAAA,GACAmC,WAAA,GACA4W,iBAAA,GACA3W,oBAAA,GACAO,uBAAA,GACAhB,eAAA,EACAF,aAAA,MACAJ,QAAA,CACA,CACA9B,MAAA,WACAM,MAAA,aAEA,CACAN,MAAA,mBACAM,MAAA,cAGA0B,KAAA2K,EAAA,KAAAqB,YAAA8P,SAAAC,IAAA,4BAIA5G,QAAA,CACA/W,YADA,SAAAA,IAEAxD,KAAAiD,MAAAC,YAAAM,YAAAxD,KAAA6D,wBAGAuD,QALA,SAAAA,EAKAga,GACAxX,EAAAuH,YAAAiQ,EAAA,OAGAjc,eATA,SAAAA,EASAkc,GACAtR,EAAA,KAAAqB,YAAAkQ,UAAAzI,IAAA,YAAAwI,IAGA9a,UAbA,eAAAgb,EAAA/L,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAC,EAaApS,GAbA,IAAAoE,EAAA3H,KAAA,IAAAohB,EAAAI,EAAA,OAAA/L,EAAAlJ,EAAAuJ,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAcA,GAAAwK,GAAA5P,QAAAC,IAAA,WAAAxN,GACA6d,EAAA7d,EAEAie,EAAAJ,EACA9U,IAAA,SAAA0Q,GACA,IAAA7K,EAAA,MACAxK,EAAAnB,uBAAA8F,IAAA,SAAAxG,EAAA6G,GACA,GAAAqQ,GAAAlX,EAAA1D,GAAA,CACA+P,EAAAxF,KAGA,OAAAwF,IAEAyC,OAAA,SAAAuE,GAAA,OAAAA,IAAA,QAEAvP,EAAArD,UAAAib,EAAAje,GA7BA,wBAAAyS,EAAAjK,UAAA4J,MAAA,SAAApP,EAAAiQ,GAAA,OAAA+K,EAAA7K,MAAA1W,KAAAqJ,WAAA,OAAA9C,EAAA,GAgCAkb,WAhCA,eAAAC,EAAAlM,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAyF,IAAA,IAAAV,EAAA,OAAAhF,EAAAlJ,EAAAuJ,KAAA,SAAAsF,EAAAC,GAAA,gBAAAA,EAAApF,KAAAoF,EAAAnF,MAAA,OAiCAuE,EAAA1K,EAAA,KAAAoG,IAAAwL,QAAAC,UACA5hB,KAAAqC,OAAAC,OAAAsP,oBACAiQ,gBAAAC,OAnCAzG,EAAAnF,KAAA,SAqCAuE,EAAAsH,QArCA,OAqCA/hB,KAAA4c,iBArCAvB,EAAAV,KAsCA3a,KAAAwG,uBAAAxG,KAAA4c,iBACA,oBAvCA,wBAAAvB,EAAAtP,UAAAoP,EAAAnb,SAAA,SAAAyhB,IAAA,OAAAC,EAAAhL,MAAA1W,KAAAqJ,WAAA,OAAAoY,EAAA,GA8CApb,WA9CA,eAAA2b,EAAAxM,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAA4H,IAAA,IAAAlT,EAAApK,KAAA,IAAAya,EAAAuD,EAAA,OAAAvI,EAAAlJ,EAAAuJ,KAAA,SAAAyH,EAAAC,GAAA,gBAAAA,EAAAvH,KAAAuH,EAAAtH,MAAA,OA+CAuE,EAAA1K,EAAA,KAAAoG,IAAAwL,QAAAC,UACA5hB,KAAAqC,OAAAC,OAAAsP,oBACAqQ,eAAAhc,oBAAA6b,OAjDAtE,EAAAtH,KAAA,SAmDAuE,EAAAsH,QAnDA,OAmDA/hB,KAAA4c,iBAnDAY,EAAA7C,KAoDA3a,KAAAgG,WAAAhG,KAAA4c,iBAAA,mBAAAtQ,IACA,SAAA0Q,GAAA,OACA5Z,MAAA4Z,EAAApa,KACAc,MAAAsZ,EAAA5a,GACA4a,WAIAgB,EAAA,GACAhe,KAAAgG,WAAAsG,IAAA,SAAA0Q,GACA,GAAAA,QAAA5a,IAAAgI,EAAA/H,OAAAC,OAAAkZ,cAAA,CACApR,EAAA8X,aAAAlF,QACAgB,EAAAhB,QAAAiB,kBAIAje,KAAA6D,sBAAAma,EAAA1R,IACA,SAAAqT,GAAA,OAAAA,EAAA7Z,SAGA9F,KAAAmiB,eACAniB,KAAAoiB,mBAAApE,GAoBAhe,KAAAiD,MAAAC,YAAAC,QAAA,MA7FA,yBAAAqa,EAAAzR,UAAAuR,EAAAtd,SAAA,SAAAqG,IAAA,OAAA2b,EAAAtL,MAAA1W,KAAAqJ,WAAA,OAAAhD,EAAA,GAiGAgc,KAjGA,SAAAA,IAkGA,OAAA5B,MAGA6B,oBArGA,SAAAA,EAqGAzU,EAAAlB,KAEA/G,qBAvGA,SAAAA,IAwGA5F,KAAAuiB,qBAGA1c,4BA3GA,SAAAA,IA4GA7F,KAAAuiB,kBAAAviB,KAAAqC,OAAAC,OAAAsP,qBAGA2Q,kBA/GA,eAAAC,EAAAhN,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAyI,EA+GAsE,GA/GA,IAAAvU,EAAAlO,KAAA,IAAA0iB,EAAAC,EAAA5C,EAAA6C,EAAAC,EAAAtN,EAAAkJ,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAgE,EAAArc,EAAA,OAAAgP,EAAAlJ,EAAAuJ,KAAA,SAAAyJ,EAAAC,GAAA,gBAAAA,EAAAvJ,KAAAuJ,EAAAtJ,MAAA,OAgHAwM,EAAA,GACAC,EAAA,EACA5C,EAAA,EAlHA,GAsHA0C,EAtHA,CAAAjD,EAAAtJ,KAAA,QAAAsJ,EAAAtJ,KAAA,SAyHAnG,EAAA,KAAAoG,IAAAwL,QAAA7D,aAAAiE,QAzHA,OAAAxM,EAAAiK,EAAA7E,KAwHAiI,EAxHArN,EAwHAuI,WAEA+E,EAAAD,EAAAtW,IAAA,SAAAwR,GACA,OAAA/N,EAAA,KAAAoG,IAAAwL,QACAC,UAAA9D,EAAA1b,IACAgO,UAAA0R,OAAAC,UA7HAvC,EAAAtJ,KAAA,gBAAAsJ,EAAAtJ,KAAA,UAgIAnG,EAAA,KAAAoG,IAAAwL,QACAC,UAAAa,GACArS,UAAA0R,OAAAC,QAlIA,QAgIAc,EAhIArD,EAAA7E,KAmIAkI,EAAA,CAAAA,GAnIA,QAAApE,EAAA,KAAAC,EAAA,MAAAc,EAAAvJ,KAAA,GAAA2I,EAAAkB,IAsIA+C,GAtIA,QAAArD,EAAAtJ,KAAA,UAAA0I,EAAA1I,OAAA,QAAA2I,EAAAW,EAAA7E,KAAA8D,EAAAI,EAAAkB,KAAAP,EAAAtJ,KAAA,UAAA2I,EAAAnb,MAAA,QAAAob,EAAAU,EAAA7E,KAAA,GAAA8D,EAAA,CAAAe,EAAAtJ,KAAA,SAsIA4M,EAtIAhE,EAuIA4D,EAAArU,KAAAyU,EAAA1S,WACAuS,GAAAG,EAAA1S,UAAA9G,OAxIA,QAAAmV,EAAA,KAAAe,EAAAtJ,KAAA,iBAAAsJ,EAAAtJ,KAAA,iBAAAsJ,EAAAvJ,KAAA,GAAAuJ,EAAAQ,GAAAR,EAAA,aAAAd,EAAA,KAAAC,EAAAa,EAAAQ,GAAA,QAAAR,EAAAvJ,KAAA,GAAAuJ,EAAAvJ,KAAA,SAAAwI,GAAAG,EAAAqB,QAAA,OAAAT,EAAAtJ,KAAA,SAAAsJ,EAAAtJ,KAAA,UAAA0I,EAAAqB,SAAA,QAAAT,EAAAvJ,KAAA,OAAAyI,EAAA,CAAAc,EAAAtJ,KAAA,eAAAyI,EAAA,eAAAa,EAAAU,OAAA,mBAAAV,EAAAU,OAAA,YA2IAwC,EAAAK,EAAAxW,EAAAyW,QAAAN,GAEAjc,EAAA,SAAAA,IACA,IAAA4G,EAAA,EACA,IAAA6I,EAAA,SAAAA,IACA,GAAA7I,GAAAqV,EAAApZ,OAAA,CACA,IAAAsS,EAAA8G,EAAArV,GACA0C,EAAA,KAAAoG,IAAA8M,IACAC,aAAA,CACAtV,KAAA,qBACAxL,GAAAwZ,EAAAxZ,GACA+gB,kBAAA,OAEAC,WAAA,MACAC,KACA,SAAAJ,GAEA,GAAAvC,GAAA5P,QAAAC,IAAA,MAAAkS,GACAlT,EAAA,KAAAC,SAAAC,WACAC,IAAA,CACAX,KAAA0T,EACA7gB,GAAAwZ,EAAAxZ,GACAkO,QAAA,QAEAC,KANA,eAAA+S,EAAA9N,IAA0BC,EAAAlJ,EAAAmJ,KAM1B,SAAAiI,EAAAnN,GAAA,OAAAiF,EAAAlJ,EAAAuJ,KAAA,SAAA8H,EAAAC,GAAA,gBAAAA,EAAA5H,KAAA4H,EAAA3H,MAAA,OAAA2H,EAAA3H,KAAA,SACAnG,EAAA,KAAAoG,IAAAoN,qBACA3H,EAAAxZ,GACA,CACAiO,gBAAAG,EAAAgT,MACA,KACAhT,EAAAyS,MANA,OASA/M,IACA6J,IACA1S,IACAa,EAAA1I,eACAua,EAAA4C,EAAA,IAbA,wBAAA9E,EAAA9R,UAAA4R,MANA,gBAAAyC,GAAA,OAAAkD,EAAA5M,MAAA1W,KAAAqJ,YAAA,IAqBAoa,MAAA,eAEA,sBAIAvN,KAGAlW,KAAA6a,GACAC,OAAA,CACAzB,MAAA,UACA0B,QAAA,oBAAA/Q,OAAA2Y,EAAA,gBACA3H,GAAA,WACAC,OAAA,OAEAC,KAAA,WACAhN,EAAA5I,aAAAmd,EAAA,IACAhc,MApMA,yBAAA+Y,EAAAzT,UAAAoS,EAAAne,KAAA,yCAAAuiB,EAAA9L,GAAA,OAAA+L,EAAA9L,MAAA1W,KAAAqJ,WAAA,OAAAkZ,EAAA,GAwMAmB,mBAxMA,SAAAA,IAyMA,YAGAhd,UA5MA,SAAAA,IA6MA1G,KAAA2jB,aAAA,OAGAA,aAhNA,eAAAC,EAAApO,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAmO,IAAA,IAAAC,EAAAC,EAAAC,EAAAvJ,EAAA1U,EAAAke,EAAAlb,EAAAjD,EAAA+H,EAAAqW,EAAAC,EAAApc,EAAA4E,EAAAyX,EAAApgB,EAAAqgB,EAAAC,EAAAjb,UAAA,OAAAoM,EAAAlJ,EAAAuJ,KAAA,SAAAyO,EAAAC,GAAA,gBAAAA,EAAAvO,KAAAuO,EAAAtO,MAAA,OAiNA4N,EAjNAQ,EAAAhb,OAAA,GAAAgb,EAAA,KAAApkB,UAAAokB,EAAA,GAiNA,MACAP,EAlNAO,EAAAhb,OAAA,GAAAgb,EAAA,KAAApkB,UAAAokB,EAAA,GAkNA,KACAN,EAnNAM,EAAAhb,OAAA,GAAAgb,EAAA,KAAApkB,UAAAokB,EAAA,GAmNA,KAEA1a,EAAAlB,SAAA,KArNA,GAuNAqb,EAvNA,CAAAS,EAAAtO,KAAA,QAwNA,GAAA4N,EAAA,CACAla,EAAAxB,aAAApI,KAAAykB,iBACA7a,EAAA8a,QA1NA,OAAAF,EAAAG,OAAA,iBAgOA3kB,KAAAmG,cAAAnG,KAAAqC,OAAAC,OAAAsP,mBAEA6I,EAAA1K,EAAA,KAAAoG,IAAAwL,QAAAC,UAAA5hB,KAAAmG,eACA0b,gBAAAzR,UAAA0R,OAnOA0C,EAAAtO,KAAA,UAoOAuE,EAAAsH,QApOA,QAoOAhc,EApOAye,EAAA7J,KAqOA3a,KAAA+F,cACA/F,KAAAwG,uBAAAT,EAAA,oBACA6D,EAAA7D,cACAke,EAAAle,EAAA,oBAEA,GAAA2a,GAAA5P,QAAAC,IAAA,SAAAkT,GAEAjkB,KAAA4kB,gBAAAX,EAEAlb,EAAA/I,KAAA+I,QAAA,CACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAGAjD,EAAAme,EAEAra,EAAA+I,UAAA7M,EAEA+H,EAAA,SAAAA,EAAAlB,EAAAkY,GACA,OACA/W,MAAAnB,EACA3I,OAAA6gB,EAAA7gB,OACA8K,aAAA+V,EAAA/V,aACA2C,MAAAoT,EAAAC,cACAvT,SAAAsT,EAAAE,eACA3iB,GAAAyiB,EAAAziB,GACA4iB,gBAAAH,EAAAziB,GACA4U,OAAA6N,EAAAzU,UACAX,YAAAoV,EAAAzU,UACAxC,KAAA,YACAa,KAAA,QAIAyV,EAAA,SAAAA,EAAAvX,EAAAF,GAAA,OACAqB,MAAAnB,EACA3I,OAAAyI,EACA7J,KAAA,QACAR,GAAAuK,EACAqK,QAAA,EACApJ,KAAA,QACAa,KAAA,OAGA0V,EAAA,SAAAA,EAAAU,EAAApY,GAAA,OAAAoY,EAAA7gB,QAAAyI,GAEA3G,IAAAwG,IAAA,SAAA2Y,EAAAC,GACApU,QAAAC,IAAA,QAAAkU,GACAA,EAAAhH,eAAA8E,EAAAxW,EAAA+L,QACA2M,EAAAhH,eACA,WACA,SAEA,IAAAlW,EAAA,GACAod,EAAA,EACAC,EAAA,EACAzY,EAAA,EACA,QAAA0Y,EAAA,EAAAA,EAAAtc,EAAAO,OAAA+b,IAAA,KAAArhB,EAAA+E,EAAAsc,GACA,IAAAR,EAAA9B,EAAAxW,EAAA4D,KAAA8U,EAAAhH,eAAA,CACAja,WAEA,GAAA0c,GAAA5P,QAAAC,IAAAoU,EAAAN,EAAA7gB,GAAA6gB,EAAA7gB,IACA8M,QAAAC,IAAA,OAAA8T,GACA,GAAAA,EAAA,CACA9c,EAAAsG,KAAAR,EAAAlB,IAAAkY,QACA,CACA9c,EAAAsG,KAAA6V,EAAAvX,IAAA3I,KAGA,OAAA0J,IAAA,GAAAuX,EAAA,CAAAK,QAAAL,EAAA7iB,GAAA2F,QAAA+F,MAAAoX,MAGAnd,EAAA,GACA4E,EAAA,EACA,IAAAyX,EAAA,EAAAA,EAAArb,EAAAO,OAAA8a,IAAA,CAAApgB,EAAA+E,EAAAqb,GAAArc,EAAAsG,KAAA6V,EAAAvX,MACA7G,EAAAuI,KAAA,CACAjM,GAAA,MACA0L,MAAA,EACAkJ,QAAA,EACAjP,UAGAsc,EAAA,GACAve,EAAAwG,IAAA,SAAA2Y,EAAAC,GACAD,EAAAld,MAAAuE,IAAA,SAAAuB,EAAA0X,GACA1X,EAAAC,MAAAoX,EACArX,EAAAiB,aAAAjB,EAAAiB,aACA,GAAA4R,GAAA5P,QAAAC,IAAA,KAAAlD,GAEA,GAAAwW,EAAAkB,GAAA,CACAlB,EAAAkB,GAAAxd,MAAAsG,KAAAR,OACA,CACAwW,EAAAkB,GAAA,CACAxd,MAAA,CAAA8F,GACAzL,GAAAmjB,EACAD,QAAAL,EAAAK,QACAE,SAAAP,EAAA7iB,GACAwL,KAAA,QACAE,MAAAyX,QAMA3b,EAAA9D,OAAAue,EACA,GAAAP,EAAA,CACAla,EAAAxB,aAAApI,KAAAykB,iBACA7a,EAAA8a,QAGA,GAAAV,EAAA,CACAhkB,KAAAuG,UACAvG,KAAAiD,MAAAC,YACA6a,uBACA/d,KAAAqC,OAAAC,OAAAkZ,eAEAlP,IAAA,SAAAmZ,GAAA,OAAAA,EAAA3f,UAIA9F,KAAAmiB,eACAvY,EAAAlB,SAAA,MApWA,yBAAA8b,EAAAzY,UAAA8X,EAAA7jB,SAAA,SAAA2jB,IAAA,OAAAC,EAAAlN,MAAA1W,KAAAqJ,WAAA,OAAAsa,EAAA,GAuWA+B,OAvWA,SAAAA,IAwWA1lB,KAAA2lB,gBAGAC,UA3WA,eAAAC,EAAArQ,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAoQ,IAAA,OAAArQ,EAAAlJ,EAAAuJ,KAAA,SAAAiQ,EAAAC,GAAA,gBAAAA,EAAA/P,KAAA+P,EAAA9P,MAAA,wBAAA8P,EAAAja,UAAA+Z,MAAA,SAAAF,IAAA,OAAAC,EAAAnP,MAAA1W,KAAAqJ,WAAA,OAAAuc,EAAA,GA6WA5X,iBA7WA,eAAAiY,EAAAzQ,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAAwQ,EA6WArY,GA7WA,IAAAsY,EAAAC,EAAA/c,UAAA,OAAAoM,EAAAlJ,EAAAuJ,KAAA,SAAAuQ,EAAAC,GAAA,gBAAAA,EAAArQ,KAAAqQ,EAAApQ,MAAA,OA6WAiQ,EA7WAC,EAAA9c,OAAA,GAAA8c,EAAA,KAAAlmB,UAAAkmB,EAAA,GA6WA,MA7WA,wBAAAE,EAAAva,UAAAma,MAAA,SAAAlY,EAAAuY,GAAA,OAAAN,EAAAvP,MAAA1W,KAAAqJ,WAAA,OAAA2E,EAAA,GA+WAwY,MA/WA,SAAAA,IAgXAxmB,KAAAoJ,QAGAqd,eAnXA,SAAAA,EAmXAvQ,GAAA,IAAA1D,EAAAxS,KACA,GAAA0gB,GACA5P,QAAAC,IAAA,kDAEA,IAAA2V,EAAA,GACA,IAAAlI,EAAAxe,KAAA2mB,gBAAA3mB,KAAAykB,kBAEA,GAAA/D,GACA5P,QAAAC,IACA,OACA,OACAyN,EACAA,EACA5U,EAAAxB,aACApI,KAAAykB,kBAGA,IAAAmC,EAAA,SAAAA,EAAAC,GAAA,OACAA,EAAAjS,OAAA,SAAAiQ,GAAA,OAAAA,EAAApW,QAEA+P,EAAAtO,IAAAsO,EAAAtO,IAAA5D,IAAAsa,GACApI,EAAA/E,OAAA+E,EAAA/E,OAAAnN,IAAAsa,GAEA,GAAAlG,GAAA5P,QAAAC,IAAA,OAAAyN,GAEAA,EAAA/E,OAAAnN,IAAA,SAAArK,EAAA6kB,GACA7kB,EAAAqK,IAAA,SAAAuY,GACA,IAAAkC,EAAAlC,EAAAziB,GACA,IAAA4kB,EAAA,CACApZ,KAAA,SACAoJ,OAAA+P,GAGA,GAAArG,GACA5P,QAAAC,IACA,OADA,uBAAA/G,OAEA+c,GACAlC,GAEA6B,EAAArY,KAAA2Y,OAIAxI,EAAAtO,IAAA5D,IAAA,SAAArK,EAAA6kB,GACA7kB,EAAAqK,IAAA,SAAAuY,GACA,IAAAkC,EAAAlC,EAAAziB,GACA,IAAA6kB,EAAApC,EAAA7gB,OACA,IAAAgQ,EAAA6Q,EAAA5iB,MACA,IAAAgV,EAAA4N,EAAA5N,MAEA,IAAA+P,EAAA,CACApZ,KAAA,MACAoJ,OAAA+P,EACAG,cAAAlT,GAGA,GAAAiD,EAAA,CACA+P,EAAAtZ,IAAA,GACAsZ,EADA,CAEAhjB,OAAAijB,GAAA,MAAAA,IAEAD,EAAApZ,KAAA,SAGA,GAAA8S,GACA5P,QAAAC,IACA,OADA,OAAA/G,OAGAiN,EAAA,UAHA,iBAAAjN,OAIA+c,EAJA,eAAA/c,OAIAgK,GACAgT,EACAnC,GAGA6B,EAAArY,KAAA2Y,OAIA,IAAAG,EAAA,eAAAC,EAAA5R,IAAQC,EAAAlJ,EAAAmJ,KAAR,SAAA2R,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAT,EAAAU,EAAAC,EAAAvlB,EAAAwlB,EAAAC,EAAA,OAAApS,EAAAlJ,EAAAuJ,KAAA,SAAAgS,EAAAC,GAAA,gBAAAA,EAAA9R,KAAA8R,EAAA7R,MAAA,OACAoR,EAAA,GACAC,EAAA,MACAC,EAAA,MAHAC,EAAA,cAAAA,EAKAf,EALApd,QAAA,CAAAye,EAAA7R,KAAA,SAKA8Q,EAAAN,EALAe,GAAA,KAOAT,EAAApZ,MAAA,OAAAoZ,EAAApZ,MAAA,UAPA,CAAAma,EAAA7R,KAAA,SAQAwR,EAAAlV,EAAAoS,gBACAoC,EAAAE,eATA,GAWAQ,EAXA,CAAAK,EAAA7R,KAAA,SAAA6R,EAAA7R,KAAA,UAYAnG,EAAA,KAAAoG,IAAA6R,aAAA,CACAlK,WAAAtL,EAAArM,cACAvD,KAAA,QAdA,QAAA+kB,EAAAI,EAAApN,KAYAvY,EAZAulB,EAYAvlB,GAIAslB,EAAAtlB,EACAolB,EAAA,KAjBAO,EAAA7R,KAAA,iBAmBAwR,IAAAtlB,GAnBA,QAuBAwlB,OAvBA,EAAAG,EAAA/H,GAwBAgH,EAAApZ,KAxBAma,EAAA7R,KAAA6R,EAAA/H,KAyBA,SAzBA,GAAA+H,EAAA/H,KAqCA,MArCA,GAAA+H,EAAA/H,KA6CA,SA7CA,oBA0BA6H,EACA9E,EAAAxW,EAAAmG,UAAAgU,EAAA,CACA9Y,KAAA,MACAoJ,OAAAgQ,EAAAhQ,UACA,EA9BA,GA+BA6Q,EA/BA,CAAAE,EAAA7R,KAAA,SAAA6R,EAAA7R,KAAA,UAgCAnG,EAAA,KAAAoG,IAAA8R,0BACAjB,EAAAhQ,QAjCA,eAAA+Q,EAAApD,OAAA,oBAAAoD,EAAA7R,KAAA,UAsCAnG,EAAA,KAAAoG,IAAAC,0BACA4Q,EAAAhQ,OACA,CACAkR,OAAAR,IAzCA,eAAAK,EAAApD,OAAA,oBAAAoD,EAAA7R,KAAA,UA8CAnG,EAAA,KAAAoG,IAAAgS,0BACAT,EACAV,EAAAhQ,OACAxE,EAAAzJ,QAAAie,EAAAhjB,SAjDA,eAAA+jB,EAAApD,OAAA,oBA6DAzO,IA7DA,QAAAuR,IAAAM,EAAA7R,KAAA,iCAAA6R,EAAAhc,UAAAsb,MAAA,gBAAAF,IAAA,OAAAC,EAAA1Q,MAAA1W,KAAAqJ,YAAA,GAiEA8d,KAGA/N,YArgBA,SAAAA,EAqgBA5J,GACA,IAAAxP,KAAAqC,OAAAC,OAAAsP,mBAAA,OACA5R,KAAAub,QAAAlN,KAAA,cAAArE,OACAhK,KAAAqC,OAAAC,OAAAsP,mBADA,KAAA5H,OAEAhK,KAAAqC,OAAAC,OAAAkZ,cAFA,KAAAxR,OAGAwF,KAIA2S,aA9gBA,SAAAA,IA+gBA,IAAA/f,GAAApC,KAAAqC,OAAAC,OAAAC,cACA,GAAAH,EAAAwH,EAAAuY,aAAA/f,IAGAggB,mBAnhBA,SAAAA,EAmhBAgG,GACAtX,QAAAC,IAAA,OAAAqX,GACApoB,KAAAqoB,WAAAD,EACAxe,EAAA0e,gBAAAF,IAGA9S,mBAzhBA,eAAAiT,EAAA/S,IAAAC,EAAAlJ,EAAAmJ,KAAA,SAAA8S,EAyhBAxkB,EAAAgT,GAzhBA,IAAAyR,EAAAC,EAAAjO,EAAA1U,EAAA,OAAA0P,EAAAlJ,EAAAuJ,KAAA,SAAA6S,EAAAC,GAAA,gBAAAA,EAAA3S,KAAA2S,EAAA1S,MAAA,OA0hBAuS,EAAA1F,EAAAxW,EAAA4D,KAAAnQ,KAAAqoB,WAAA,CACAjmB,GAAA4U,EAAA5U,KAGAsmB,EAAAD,EAAA3Z,aAAA2Z,EAAA3Z,aAAA,GAEA,GAAA4Z,EAAArW,QAAArO,IAAA,GACA0kB,EAAA3T,OAAA2T,EAAArW,QAAArO,GAAA,OACA,CACA0kB,EAAAra,KAAArK,GAGAhE,KAAAqoB,WAAAK,EACAjO,EAAA1K,EAAA,KAAAoG,IAAA0S,yBAAA7R,EAAA5U,GAAA,CACA0M,aAAA4Z,IAxiBAE,EAAA1S,KAAA,SA0iBAuE,EA1iBA,OA0iBA1U,EA1iBA6iB,EAAAjO,KA2iBA3a,KAAAiD,MAAAC,YAAA0X,MAAA,cA3iBA,wBAAAgO,EAAA7c,UAAAyc,EAAAxoB,SAAA,SAAAsV,EAAAwT,EAAAC,GAAA,OAAAR,EAAA7R,MAAA1W,KAAAqJ,WAAA,OAAAiM,EAAA,GA8iBAlM,KA9iBA,SAAAA,IA+iBA,GAAAQ,EAAA9D,OAAA,CACA8D,EAAAxB,aAAApI,KAAAykB,iBACA7a,EAAA8a,UAIAnO,kBArjBA,SAAAA,EAqjBAwN,GAAA,IAAA3O,EAAApV,KACA4J,EAAA8a,QACA,GAAA9a,EAAAlB,SAAA,OACA1I,KAAAymB,eAAA,WACArR,EAAAuO,aAAA,KAAAI,MAIAiF,YA7jBA,SAAAA,EA6jBAnb,EAAAob,GAAA,IACA7mB,EAAAyL,EAAAzL,GAAAwL,EAAAC,EAAAD,KADA,IAEA3L,EAAAgnB,EAAA7iB,eAAApC,EAAAilB,EAAA5c,eAEA,IAAAtE,EAAAgb,EAAAxW,EAAA+L,QACA1O,EAAA9D,OAAA9B,GAAA+D,MACA,UACA,SAEA,GAAAA,EAAA9F,GAAAwM,KAAA,CACA1G,EAAAgN,OAAA9S,EAAA,GACAG,KACAwL,OACAqJ,MAAA,KACAjT,SACA8J,MAAA7L,EACA+U,OAAA5U,IAEAwH,EAAA9D,OAAA9B,GAAA+D,QACA/H,KAAAuW,kBAAA,WACA,GAAAvW,KAAA4E,UAAA,WACAmD,EAAAgN,OAAA9S,EAAA,GACAG,KACAwL,OACAqJ,MAAA,KACAjT,SACA8J,MAAA7L,EACA+U,OAAA5U,IAEAwH,EAAA9D,OAAA9B,GAAA+D,QACA/H,KAAAuW,kBAAA,QAIAoQ,gBA/lBA,SAAAA,EA+lBAuC,EAAAzD,GACA,IAAAjH,EAAA,CACAtO,IAAA,GACAuJ,OAAA,IAEA,QAAAqN,KAAAld,EAAAxB,aAAA,CACAoW,EAAAtO,IAAA7B,KACA0U,EAAAxW,EAAAqT,eACAsJ,EAAApC,GACAld,EAAAxB,aAAA0e,GACA/D,EAAAxW,EAAAsT,UAGArB,EAAA/E,OAAApL,KACA0U,EAAAxW,EAAAqT,eACAhW,EAAAxB,aAAA0e,GACAoC,EAAApC,GACA/D,EAAAxW,EAAAsT,UAIA,OAAArB,GAGAiG,eAvnBA,SAAAA,IAunBA,IAAA3M,EAAA9X,KACA,IAAAmpB,EAAA,GACAC,EAAA,GACA,IAAApM,EAAA,GACA,IAAAqM,EAAA,GAEAzf,EAAA9D,OAAAwG,IAAA,SAAArK,EAAA6kB,GACA7kB,EAAA8F,MAAAuE,IAAA,SAAAuB,GACAub,EAAA/a,KAAA,CACAjM,GAAAyL,EAAAzL,GACA4B,OAAA6J,EAAA7J,QAAA8T,EAAA/O,QAAA+d,GACA7kB,MAAA4L,EAAAC,MACAkJ,OAAAnJ,EAAAmJ,OACAvI,KAAAZ,EAAAY,KACAwI,MAAApJ,EAAAoJ,YAKAkS,EAAApG,EAAAxW,EAAA+c,QAAAF,EAAA,SAAArR,GAAA,OAAAA,EAAA9V,QAOA,GAAAye,GAAA5P,QAAAC,IAAA,OAAAoY,GAEA,OAAAA,IAIAI,QAvsBA,SAAAA,MAysBAjP,QAzsBA,SAAAA,IAysBA,IAAAkP,EAAAxpB,KACA,IAAAypB,EAAA,SAAAA,IACA,IAAAxI,EAAA,CACA,CACArT,KAAA,OACAxL,GAAA,EACAS,MAAA,WAEA,CACA+K,KAAA,QACAxL,GAAA,EACAS,MAAA,WAEA,CACA+K,KAAA,WACAxL,GAAA,EACAS,MAAA,WAEA,CACA+K,KAAA,UACAxL,GAAA,EACAS,MAAA,WAEA,CACA+K,KAAA,aACAxL,GAAA,EACAS,MAAA,WAEA,CACA+K,KAAA,YACAxL,GAAA,EACAS,MAAA,WAEA,CACA+K,KAAA,SACAxL,GAAA,EACAS,MAAA,WAEA,CACA+K,KAAA,OACAxL,GAAA,EACAS,MAAA,YAIA,IAAA6mB,EAAAC,aAAAxI,IAAA,iBACA,GAAAuI,EAAAna,KAAA,CACAma,EAAAna,KAAAjD,IAAA,SAAAC,GACA,IAAAqd,EAAA5N,OAAA6N,KAAAtd,GAAA,GACA,IAAAud,EAAA/G,EAAAxW,EAAAmG,UAAAuO,EAAA,CACA7e,IAAAwnB,IAEA3I,EAAA6I,GAAAjnB,MAAA0J,EAAAqd,KAIA,OAAA3I,GAEAlR,EAAA,KAAAqB,YAAAY,IAAA+X,wBAAA,CACAnc,KAAA,iBACAoc,SAAAhqB,KACAiqB,gBAAA,SAAAA,EAAA1a,EAAAhF,GACA,IAAA2f,EAAAtgB,EAAAugB,qBAAA5f,GACAif,EAAAR,YAAAzZ,EAAA2a,MAIAlqB,KAAAuJ,QAAA,KACAvJ,KAAA0L,SAAA,KACA1L,KAAA4L,iBAAA,KACA5L,KAAAiK,oBAAA,KAEAjK,KAAAmG,cAAAnG,KAAAqC,OAAAC,OAAAsP,mBACAhI,EAAAwgB,WAAApqB,MACAA,KAAA2jB,aAAA,gBACA3jB,KAAAqG,aAEA0J,EAAA,KAAAqB,YAAAC,YACAgZ,aAAA,CACArD,OAAA,SACAsD,QAAA,cAEAxO,UAAA,WACA0N,EAAA7F,aAAA,kBAGA3jB,KAAAghB,cAAAyI,IACAzgB,OAAAuhB,cAAA,IAAAC,MAAA,YAGApQ,MAAA,CACA4G,cAAA,CACAyJ,QADA,SAAAA,IAEA,IAAAC,EAAA1qB,KAAAghB,cAAA1U,IAAA,SAAAqe,GAAA,OAAA9N,IAAA,GACA8N,EAAAvoB,GAAAuoB,EAAA9nB,SAEA8mB,aAAAiB,cAAA,uBAAAF,IAEAG,KAAA,MAGArkB,uBAXA,SAAAA,MAaAnE,OAbA,SAAAA,EAaAI,EAAA4X,GACAra,KAAAmiB,eACAniB,KAAAuG,UACAvG,KAAAiD,MAAAC,YACA6a,uBAAA/d,KAAAqC,OAAAC,OAAAkZ,eACAlP,IAAA,SAAAmZ,GAAA,OAAAA,EAAA3f,WAIAlB,SAtBA,SAAAA,IAuBAgF,EAAAhF,SAAA5E,KAAA4E,UAGAG,SA1BA,SAAAA,IA2BA6E,EAAA7E,SAAA/E,KAAA+E,UAGAgD,MA9BA,SAAAA,MAkCA6T,KAlCA,SAAAA,MAsCAkP,aAtCA,SAAAA,MA0CAC,cA1CA,SAAAA,QCp+BgP,IAAAC,GAAA,qCCShP,IAAIC,GAAYjP,OAAAC,EAAA,KAAAD,CACdgP,GACAtpB,EACAiF,EACF,MACA,KACA,KACA,MAIe,IAAAukB,GAAAD,WCXf,IAAAE,GAAA,CACAlR,WAAA,CACAmR,cAAAF,IAGA5Q,QALA,SAAAA,IAMA+Q,SAAAC,MAAA,OAGA/Q,QAAA,CACApR,SADA,SAAAA,EACA4B,GACA/K,KAAAurB,eAAAxgB,EAAA/G,OAAA,KAGAulB,QALA,SAAAA,IAMAvpB,KAAAmJ,SAAA,CAAAnF,OAAAgF,OAAAwiB,gBAIAjc,KAnBA,SAAAA,IAoBA,OACAkc,iBAAA,QC9ByO,IAAAC,GAAA,GCOzO,IAAIC,GAAY3P,OAAAC,EAAA,KAAAD,CACd0P,GACAzqB,EACAQ,EACF,MACA,KACA,KACA,MAIe,IAAAmgB,GAAAgK,EAAA,WAAAD,gDClBf,IAAAE,EAAAzsB,EAAA,YAAA0sB,EAAA1sB,EAAAE,EAAAusB,GAA2jB,IAAAtsB,EAAAusB,EAAG", "file": "js/1c8ddbb3.a1086893.js", "sourcesContent": ["import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations-editor.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations-editor.vue?vue&type=style&index=1&lang=scss&\"", "import mod from \"-!../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./series-card.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./series-card.vue?vue&type=style&index=0&lang=scss&\"", "// @@search logic\nrequire('./_fix-re-wks')('search', 1, function (defined, SEARCH, $search) {\n  // 21.1.3.15 String.prototype.search(regexp)\n  return [function search(regexp) {\n    'use strict';\n    var O = defined(this);\n    var fn = regexp == undefined ? undefined : regexp[SEARCH];\n    return fn !== undefined ? fn.call(regexp, O) : new RegExp(regexp)[SEARCH](String(O));\n  }, $search];\n});\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations-editor.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations-editor.vue?vue&type=style&index=0&lang=scss&\"", "function _asyncIterator(iterable) {\n  var method;\n\n  if (typeof Symbol === \"function\") {\n    if (Symbol.asyncIterator) {\n      method = iterable[Symbol.asyncIterator];\n      if (method != null) return method.call(iterable);\n    }\n\n    if (Symbol.iterator) {\n      method = iterable[Symbol.iterator];\n      if (method != null) return method.call(iterable);\n    }\n  }\n\n  throw new TypeError(\"Object is not async iterable\");\n}\n\nmodule.exports = _asyncIterator;", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"main\"},[_c('t-relations',{ref:\"editor\"})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"relations-editor-container\"},[_c('div',{staticClass:\"flex\"},[_c('div',{staticClass:\"row\"},[_c('div',{staticClass:\"col\",staticStyle:{\"width\":\"calc(100vw)\",\"overflow\":\"hidden\",\"position\":\"relative\"}},[_c('div',{staticClass:\"_\"},[_vm._m(0)],1)])]),_c('div',{staticClass:\"row\"},[_c('div',{staticClass:\"col\",staticStyle:{\"width\":\"calc(100vw - 420px - 40px)\"}},[_c('div',{staticClass:\"row firstRowRelations\"},[(_vm.queryResult)?_c('div',{ref:\"seriesNames\",staticClass:\"col seriesNames\"},_vm._l((_vm.currentComponentSeries),function(serie){return _c('div',{staticClass:\"collection-name\",class:{ 'selectedCollectioName': serie.id==_vm.$route.params.selectedSerie }},[_c('div',{staticClass:\"name\"},[_c('router-link',{staticClass:\"relLink\",attrs:{\"to\":(\"/relations/\" + (_vm.$route.params.selectedCollection) + \"/\" + (_vm.$route.params.selectedTable) + \"/\" + (serie.id))}},[_vm._v(_vm._s(serie.name))])],1),_c('div',{staticClass:\"select\"},[_c('q-checkbox',{attrs:{\"color\":\"pink\",\"dark\":\"dark\",\"dense\":\"dense\",\"disabled\":_vm.$refs.tableEditor.syncing,\"label\":\"Use\",\"val\":serie.id},on:{\"input\":_vm.updateTable},model:{value:(_vm.choosenSeriesForTable),callback:function ($$v) {_vm.choosenSeriesForTable=$$v},expression:\"choosenSeriesForTable\"}})],1)])}),0):_vm._e()]),_c('div',{staticClass:\"flex row secondRowRelatcions\"},[_c('div',{staticClass:\"col heights\"},_vm._l((_vm.heights),function(height){return _c('div',{staticClass:\"series-height\"},[_vm._v(_vm._s({\"300\": \"B\", \"400\": \"C\", \"500\": \"D\", \"600\": \"E\", \"700\": \"F\", \"800\": \"G\", \"900\": \"H\", \"1000\": \"I\"}[height])+\" \"+_vm._s(height))])}),0),_c('div',{ref:\"container\",staticClass:\"col relations-editor-container-canvas\"},[_c('div',{ref:\"renderer\",staticClass:\"renderer-relations\"})])])]),_c('div',{staticClass:\"col\",staticStyle:{\"max-width\":\"420px\"}},[_c('q-scroll-area',{staticClass:\"scrollRel\"},[_c('t-card',{attrs:{\"name\":\"Editor settings\"}},[_c('div',{staticClass:\"card mid\"},[_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-main\"},[_c('div',{staticClass:\"card-content\"},[_c('t-presets',{attrs:{\"options\":[{ label: 'Replace', value: 'replace' }, { label: 'Normal', value: 'normal' } ],\"target-model\":_vm.dropMode,\"big\":\"big\",\"section-label\":\"Drop mode\"}}),_c('t-presets',{attrs:{\"options\":[{ label: 'Replace', value: 'replace' }, { label: 'Clone', value: 'clone' } ],\"target-model\":_vm.editMode,\"big\":\"big\",\"section-label\":\"Swap mode\"}})],1)])]),_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-main\"},[_c('t-section',{attrs:{\"name\":\"Extra settings\"}},[_c('q-list',[_c('q-item',[_c('q-select',{attrs:{\"inverted-light\":\"inverted-light\",\"color\":\"grey-7\",\"separator\":\"separator\",\"options\":_vm.miniops},on:{\"input\":_vm.selectMiniMode},model:{value:(_vm.mini),callback:function ($$v) {_vm.mini=$$v},expression:\"mini\"}})],1),_c('q-item',[_c('q-btn',{attrs:{\"loading\":_vm.batchRunning==2,\"label\":\"Render all miniatures\",\"percentage\":_vm.miniaturesDone},nativeOn:{\"click\":function($event){return _vm.prepareMiniaturesAll($event)}}})],1),_c('q-item',[_c('q-btn',{attrs:{\"loading\":_vm.batchRunning==1,\"label\":\"Render miniatures for collection\",\"percentage\":_vm.miniaturesDone},nativeOn:{\"click\":function($event){return _vm.prepareMiniaturesCollection($event)}}})],1)],1)],1)],1)])])]),_c('t-table',{ref:\"tableEditor\",attrs:{\"series\":_vm.queryResult,\"tablesList\":_vm.tablesList,\"tableConfigurations\":_vm.tableConfigurations,\"colletionID\":_vm.projectFilter,\"selectedSeries\":_vm.choosenSeriesForTable},on:{\"loadTables\":_vm.loadTables,\"loaded\":_vm.dimSeries}}),_c('t-series',{attrs:{\"series\":_vm.currentComponentSeries,\"colletionID\":_vm.projectFilter},on:{\"loadTables\":_vm.loadTables,\"render\":_vm.rerrender}})],1)],1)])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('t-bar',{ref:\"bar\",attrs:{\"menu\":\"true\",\"box\":{ width: 120, height: 90, cols: 2, rows: 2, gutter: 8 },\"actAsNavigation\":false,\"typesToDisplay\":\"componentRelations\"},on:{\"searchMatch\":_vm.lightup}})}]\n\nexport { render, staticRenderFns }", "import * as PIXI from 'pixi.js';\nimport { gltext } from '@core/gl/msdf-text';\nimport normalizeWheel from 'normalize-wheel';\nimport _ from 'lodash';\nimport XXH from 'xxhashjs';\nimport { cape } from '@core/cape';\n\nlet zTotal = 1000;\n\nlet Ruler = {\n    ORIENTATION_VERTICAL: 'vertical',\n    ORIENTATION_HORIZONTAL: 'horizontal'\n};\n\nconst DEBUG = true;\n\nclass Director {\n\n    constructor() {\n\n        this.checked = false;\n        this.bgDrawn = false;\n        this.items = [];\n        this.labels = [];\n        this.sprites = [];\n        this.orderHittest = [];\n        this.getSortArray = [];\n        this.series = [];\n        this.currentState = null;\n        this.cache = [];\n        this.delta = { x: 0, y: 0 };\n        this.editMode = 'normal';\n        this.selectedSeriesNo = [];\n        this.fetching = false;\n\n        this.throttledDraw = _.throttle(this.__draw, 1);\n\n        let heights = this.heights = [300, 400, 500, 600, 700, 800, 900, 1000]; // rows\n\n        window.addEventListener(\"resize\", (e) => {\n            this.onResize();\n        });\n\n    }\n\n    onResize(draw = true) {\n\n        if (!this.pixiApp || !this.vue) return;\n        let { width, height } = this.getSize();\n        this.currentWidth = width;\n        this.pixiApp.renderer.resize(width, height);\n        this.pixiApp.view.style[\"max-width\"] = `${width}px`;\n        this.backgroundContainer.clear();\n        this.bgDrawn = false;\n        if (draw) this.vue.draw();\n    }\n\n    setContext(vueComponentInstance) {\n        this.vue = vueComponentInstance;\n        this.initializePixi();\n        this.vue.$refs.renderer.appendChild(this.pixiApp.view);\n        this.delta = { x: 0, y: 0 };\n\n        this.vue.$refs.renderer.addEventListener('mousewheel', (event) => {\n            const normalized = normalizeWheel(event);\n\n            this.delta.x += normalized.pixelY;\n\n            if (this.delta.x > 0) this.delta.x = 0;\n            if (Math.abs(this.delta.x) > renderer.pixiHitests.width - this.currentWidth)\n                this.delta.x = -(renderer.pixiHitests.width - this.currentWidth);\n            if (renderer.pixiHitests.width < this.currentWidth) this.delta.x = 0;\n\n            this.throttledDraw();\n        });\n\n        this.selected = { id: this.vue.$route.params.id };\n\n        this.vue.draw();\n        this.onResize();\n    }\n\n\n    getXY() {\n        let size = this.vue.$el.getBoundingClientRect();\n        return { x: size.top, y: size.left };\n    }\n\n    getSize() {\n\n        // let size = this.vue.$refs.container.getBoundingClientRect();\n        let size = this.vue.$el.getBoundingClientRect();\n        return { width: size.width - 390, height: size.height };\n\n        switch (this.orientation) {\n            case Editor.ORIENTATION_VERTICAL:\n                break;\n            case Editor.ORIENTATION_HORIZONTAL:\n                break;\n            default:\n                break;\n        }\n    }\n\n    initializePixi() {\n\n        if (this.pixiApp) return;\n\n        let { width, height } = this.getSize();\n\n        this.pixiApp = new PIXI.Application(width, height, {\n            backgroundColor: 0,\n            resolution: 1\n        });\n\n        let pixiApp = this.pixiApp;\n\n        this.cache = [];\n\n        this.pixiRoot = new PIXI.Container();\n        this.pixiHitests = new PIXI.Container();\n\n        this.annotationsOverlay = new PIXI.Container();\n\n        this.drawingContainer = new PIXI.Graphics();\n        this.backgroundContainer = new PIXI.Graphics();\n\n        this.pixiRoot.addChild(this.backgroundContainer);\n        this.pixiRoot.addChild(this.drawingContainer);\n\n        this.pixiApp.stage.addChild(this.pixiRoot);\n        this.pixiApp.stage.addChild(this.pixiHitests);\n\n        this.pixiApp.stage.addChild(this.annotationsOverlay);\n\n        this.vue.$refs.renderer.appendChild(pixiApp.view);\n        this.pixiApp.view.style[\"max-width\"] = `${width}px`;\n\n        this.pixiApp.stop();\n\n        this.seriesToDim = [];\n\n        //  gltext.init(this.pixiApp).then(() => {\n        this.vue.draw();\n        //    });\n    }\n\n    traceDropCoordinates(event) {\n\n        let { clientX: x, clientY: y } = event;\n        let { top, left } = this.vue.$refs.renderer.firstChild.getBoundingClientRect();\n\n        x -= left + this.delta.x;\n        y -= top;\n\n        let selectedSeries, selectedHeight;\n\n        this.orderHittest.map((a, an) => {\n            //   if(DEBUG) console.log(\"hits\",a,an, x);\n            a.map((box, n) => {\n                if (x >= box.y && x <= box.y + box.h) {\n                    selectedSeries = n;\n                    selectedHeight = Math.ceil(y / box.h) - 1;\n                }\n            });\n        });\n\n        return { selectedSeries, selectedHeight };\n    }\n\n    getElementPosition(no) {\n        let gutter = 1;\n\n        let item_width = 100;\n        let item_height = 100;\n\n        let max_blocks = 10;\n        let cols_in_block = 1;\n        let rows_in_block = 1;\n        let elements_in_block = cols_in_block * rows_in_block;\n\n        const getPosition = no => {\n            let block = Math.floor(no / elements_in_block);\n\n            let index = no - block * elements_in_block;\n            let col = Math.floor(index / rows_in_block);\n            let row = index - col * rows_in_block;\n            let block2 = Math.round(no / elements_in_block);\n\n            return {\n                x: col * item_width + +gutter / 2,\n                y: row * item_height + gutter / 2 + block * item_height * rows_in_block\n            };\n        };\n\n        return {\n            ...getPosition(no),\n            w: item_width - gutter,\n            h: item_height - gutter,\n            gutter\n        };\n    }\n\n    addItem(id, type) {\n        let item = { id, type, order: this.items.length + 2 };\n        this.items.unshift(item);\n        this.addConfiguration(item);\n    }\n\n    drawItem(serie, no, item, type, superSelect) {\n\n        let { x, y, w, h, gutter } = this.getElementPosition(no);\n\n        this.orderHittest[serie.id] = this.orderHittest[serie.id] || [];\n        this.orderHittest[serie.id].push({ y, h });\n\n        let id = superSelect\n            ? superSelect\n            : parseInt(this.vue.$route.params.selectedConfiguration);\n\n        let drawingSprite = new PIXI.Graphics();\n\n        drawingSprite.height = h;\n        drawingSprite.width = w;\n        drawingSprite.series = serie.id;\n\n        if (item.fake) {\n            drawingSprite\n                .lineStyle(1, 0xffffff, 0.2)\n                .beginFill(\n                    0,\n                    0.8\n                )\n                .drawRoundedRect(gutter, gutter, w - gutter * 2, h - gutter * 2, 5)\n                .endFill();\n        } else {\n            drawingSprite\n                .lineStyle(2, 0xffffff, 0.2)\n                .beginFill(\n                    item.inconsequent ? 0x0000ff : 0x333,\n                    item.fake ? 0.1 : 1\n                )\n                .drawRect(0, 0, w, h)\n                .endFill();\n\n\n        }\n\n\n        let square = new PIXI.Graphics();\n        square\n            .lineStyle(5, 0x00ff00, 0.9)\n            .drawRect(gutter * 2 + 5, gutter * 2 + 5, w - gutter * 4 - 10, h - gutter * 2 - 10)\n            .endFill();\n\n        drawingSprite.addChildAt(square, 0);\n        square.alpha = 0;\n        square.interactive = false;\n        square.buttonMode = false;\n\n        drawingSprite.hitArea = new PIXI.Rectangle(0, 0, w, h);\n        drawingSprite.position.y = serie.order * (w + gutter) + x;\n        drawingSprite.position.x = y;\n        drawingSprite.interactive = true;\n        drawingSprite.buttonMode = true;\n        drawingSprite.cursor = \"pointer\";\n        drawingSprite.data = { event: null, serieId: serie.id, order: no, componentID: item.componentID };\n\n        if (!item.fake) {\n\n            let hash = XXH.h32(`${item.componentID}`, 0xABCD).toString(16);\n\n            if (this.cache[hash]) {\n                var sprite = new PIXI.Sprite(this.cache[hash]);\n                drawingSprite.addChildAt(sprite, 0);\n            } else {\n                if (this.queryResult && item.componentID) {\n                    //    if(DEBUG) console.log(132, this.queryResult.component, item);\n                    cape.services.miniatures.add({\n                        data: _.find(this.queryResult.component, { id: item.componentID }).thumbnails_data,\n                        id: item.componentID,\n                        fetched: true\n                    }).then((result) => {\n                        //      if(DEBUG) console.log(\"parsedGeo\",result);\n                        // result: ImageBitmap from worker (OffscreenCanvas)\n                        var bt = new PIXI.BaseTexture(result.painterState);\n                        bt._sourceLoaded();\n                        var texture = new PIXI.Texture(bt);\n                        texture.defaultAnchor = { x: 0, y: 0 };\n                        this.cache[hash] = texture;\n                        var sprite = new PIXI.Sprite(texture);\n                        drawingSprite.addChildAt(sprite, 0);\n                        //   drawingSprite.cacheAsBitmap = true;\n                        this.pixiApp.render();\n                    });\n                }\n\n            }\n\n\n            console.log(serie.id);\n\n            drawingSprite\n                .on(\"rightdown\", (e) => {\n                    if (this.fetching) return;\n                    drawingSprite.specialMenuOpened = true;\n                    e.data.originalEvent.preventDefault();\n                    console.log(\"wwww\", item);\n                    this.lightupByID(item.componentID);\n                    cape.application.contextMenu.show(e, { name: item.compName, setID: item.setId, configID2: item.id, configID: item.componentID, selectedCollection: this.vue.$route.params.selectedCollection }, \"relations\");\n                })\n                .on(\"mousedown\", (e) => {\n                    if (this.fetching) return;\n                    //  if(drawingSprite.specialMenuOpened) return; \n                    this.onDragStart(item, no, drawingSprite, e, serie);\n                })\n                .on(\"mouseup\", (e) => {\n                    if (this.fetching) return;\n                    //   if(drawingSprite.specialMenuOpened) return; \n                    this.onDragEnd(item.id, drawingSprite, item, no, serie);\n                })\n                .on(\"pointerupoutside\", (e) => {\n                    if (this.fetching) return;\n                    //   if(drawingSprite.specialMenuOpened) return; \n                    this.onDragEnd(item.id, drawingSprite, item, no, serie);\n                })\n                .on(\"mousemove\", (e) => {\n                    if (this.fetching) return;\n                    this.onDragMove(item, no, drawingSprite, e, serie)\n                })\n                .on(\"mouseover\", (e) => {\n                    if (this.fetching) return;\n                    if (cape.application.dnd.isDraggingHappening) {\n                        drawingSprite.alpha = 0.5;;\n                        this.pixiApp.render();\n                    }\n                })\n                .on(\"mouseout\", (e) => {\n                    if (this.fetching) return;\n                    if (cape.application.dnd.isDraggingHappening) {\n                        drawingSprite.alpha = 1;\n                        this.pixiApp.render();\n                    }\n                });\n\n        } else {\n            drawingSprite.on(\"mouseover\", e => {\n                if (cape.application.dnd.isDraggingHappening) {\n                    drawingSprite.clear();\n                    drawingSprite\n                        .lineStyle(1, 0xffffff, 0.2)\n                        .beginFill(\n                            0x00ff00,\n                            0.2\n                        )\n                        .drawRoundedRect(gutter, gutter, w - gutter * 2, h - gutter * 2, 5)\n                        .endFill();\n                    this.pixiApp.render();\n                }\n            }).on(\"mouseout\", e => {\n                drawingSprite.clear();\n                drawingSprite\n                    .lineStyle(1, 0xffffff, 0.2)\n                    .beginFill(\n                        0,\n                        0.8\n                    )\n                    .drawRoundedRect(gutter, gutter, w - gutter * 2, h - gutter * 2, 5)\n                    .endFill();\n                this.pixiApp.render();\n            });\n        }\n\n\n        this.pixiHitests.addChild(drawingSprite);\n        // drawingSprite.cacheAsBitmap = true;\n        this.sprites.push(drawingSprite);\n    }\n\n    lightupByID(id, isMatch = false) {\n        console.log(id, isMatch, \"lightupByID\");\n        let hit = false;\n        if (!this.pixiHitests) return;\n        this.pixiHitests.children.map((item) => {\n            console.log(\"idi\", id, item.data.componentID, +item.data.componentID == +id);\n            if (!item || !item.children) {\n            } else if (item.children.length > 1) {\n                if (!id) {\n                    item.children[1].alpha = 0\n                } else if (isMatch ? id.indexOf(+item.data.componentID) > -1 : +item.data.componentID == +id) {\n                    item.children[0].alpha = 1;\n                    item.children[1].alpha = 1;\n                    hit = true;\n                } else {\n                    item.children[1].alpha = 0;\n                }\n            }\n        });\n        if (!hit && id) this.lightupByID();\n        this.pixiApp.render();\n    }\n\n    setInconsequent(data) {\n        this.inconsequentArray = data;\n        this.drawSelectedSeries();\n        this.pixiApp.render();\n    }\n\n    selectSeries(input) {\n        if (!input || input < 0) return;\n        let no2Id = (no) => _.findIndex(this.rawSeries, { id: no });\n        this.selectedSeriesNo = Array.isArray(input) ? input.map(no2Id) : [no2Id(input)];\n        this.drawSelectedSeries();\n        this.pixiApp.render();\n    }\n\n    dimSeries(noList, idList) {\n        if (DEBUG) console.log(\"dim\", noList);\n        if (!noList || noList < 0) return;\n        this.seriesToDim = noList;\n        this.seriesToDimId = idList;\n        this.drawSelectedSeries();\n        this.pixiApp.render();\n    }\n\n    drawSelectedSeries() {\n\n        let root = this.annotationsOverlay;\n\n        let selectedSeriesNo = this.selectedSeriesNo;\n        let totalHeight = 1000;\n        let seriesCount = this.series.length;\n\n        let { x, y, w, h, gutter } = this.getElementPosition(1);\n\n        let bar = new PIXI.Graphics();\n        bar\n            .lineStyle(5, 0xffffff, 0.9)\n            .drawRect(0, 0, w - gutter * 2, totalHeight)\n            .endFill();\n\n        let bar2 = new PIXI.Graphics();\n        bar2\n            .beginFill(0, 0.6)\n            .drawRect(0, 0, w, totalHeight)\n            .endFill();\n\n        root.removeChildren();\n        selectedSeriesNo.map(no => {\n            let img = new PIXI.Sprite(bar.generateTexture());\n            img.position.x = (w + gutter) * no + gutter;\n            root.addChild(img);\n        });\n\n        let mapper = new Array(40).fill(0);\n        mapper = mapper.map((pass, no) => no);\n        mapper = _.difference(mapper, this.seriesToDim.concat(selectedSeriesNo));\n        //  if(DEBUG) console.log(\"ddd\", mapper, this.seriesToDim);\n\n        mapper.map((no) => {\n            let img = new PIXI.Sprite(bar2.generateTexture());\n            img.position.x = no == 0 ? 1 : (w + gutter) * no;\n            root.addChild(img);\n        });\n\n        this.drawInconsequent();\n\n    }\n\n    drawInconsequent() {\n\n        let root = this.annotationsOverlay;\n\n        for (let serie = 0; serie < this.seriesToDim.length; serie++) {\n            for (let heightNo = 0; heightNo < this.heights.length; heightNo++) {\n\n                let serieID = this.seriesToDimId[serie];\n                let tableConfiguration = _.find(this.inconsequentArray, { series: serieID });\n                let inconsequents = tableConfiguration ? tableConfiguration.inconsequent : null;\n\n                console.log(\"im22\", tableConfiguration, inconsequents);\n\n                if (inconsequents && inconsequents.indexOf(this.heights[heightNo]) > -1) {\n                    let { x, y, w, h, gutter } = this.getElementPosition(heightNo);\n                    let square = new PIXI.Graphics();\n                    square\n                        .lineStyle(5, 0xff0000, 0.9)\n                        .beginFill(\n                            0xff0000,\n                            0.2\n                        )\n                        .drawRect(0, 0, w - gutter * 2, h)\n                        .endFill();\n                    square.position.x = this.seriesToDim[serie] * (w + gutter) + gutter;\n                    square.position.y = y;\n                    root.addChild(square);\n                }\n                // alert();\n            }\n        }\n    }\n\n    transferControl() {\n        this.dragging = false;\n        // if(DEBUG) console.log(\"trasnfer\");\n    }\n\n    onDragStart(item, no, sprite, event) {\n        sprite.data.event = event.data;\n        // sprite.alpha = 0.5;\n        sprite.dragging = true;\n        sprite.delta = false;\n        sprite.startPosition = sprite.data.event.getLocalPosition(this.pixiApp.stage);\n    }\n\n    liveSortExecute(draggedId, y, serie, sprites) {\n\n        let selectedIndex = null;\n\n        this.orderHittest[serie.id].map((box, n) => {\n            if (y >= box.y && y <= box.y + box.h + 16) {\n                selectedIndex = n;\n            }\n        });\n\n        if (selectedIndex === null) {\n            selectedIndex = this.orderHittest[serie.id].length;\n        }\n\n        let draggedSprite = null;\n        let targetSprite = null;\n\n        let sorted = this.sprites\n            .map((sprite) => {\n                // Narrow result to serie\n                if (sprite.data.serieId == serie.id) return sprite;\n            })\n            .filter(s => (s ? true : false))\n            .map((sprite, n) => {\n                if (n != draggedId) {\n                    if (draggedId != selectedIndex) {\n                        if (n == selectedIndex) {\n                            targetSprite = sprite;\n                        } else {\n                            return sprite;\n                        }\n                    } else {\n                        return sprite;\n                    }\n                } else {\n                    draggedSprite = sprite;\n                }\n            })\n            .filter(s => (s ? true : false));\n\n        this.directionReverse = draggedId > selectedIndex ? 1 : 0;\n\n        if (this.editMode == \"normal\") {\n            if (targetSprite) sorted.splice(draggedId - this.directionReverse, 0, targetSprite);\n            sorted.splice(selectedIndex, 0, draggedSprite);\n        } else if (this.editMode == \"replace\") {\n\n            //    if(targetSprite) sorted.splice(draggedId - this.directionReverse, 0, targetSprite);\n            //   sorted.splice(selectedIndex, 0, draggedSprite);\n\n        } else if (this.editMode == \"clone\") {\n            if (targetSprite) sorted.splice(draggedId - this.directionReverse, 0, targetSprite);\n            sorted.splice(selectedIndex, 0, draggedSprite);\n        }\n\n        if (DEBUG) console.log(\"before diff\", draggedId, draggedId - this.directionReverse, selectedIndex, draggedSprite, targetSprite);\n\n        this.draggedInitialNo = draggedId - this.directionReverse;\n        this.draggedTargetNo = selectedIndex;\n\n        if (this.draggedTargetNo == this.draggedInitialNo) {\n            this.draggedInitialNo = draggedId;\n        }\n\n        if (this.editMode == \"clone\") {\n\n        } else {\n            /* sorted.map((sprite, n) => {\n                 if(sprite) {\n                     sprite.order = n;\n                     if (n == selectedIndex) return;\n                     let { y: newY } = this.getElementPosition(n);\n                     sprite.position.x = newY;\n                 }\n             });\n             if(DEBUG) console.log(\"xxx\",sorted);\n             this.getSortArray = sorted;*/\n        }\n\n    }\n\n    onDragEnd(d, s, item, no2, serie) {\n        s.alpha = 1;\n        s.dragging = false;\n        let no = 0;\n        this.changedSerie = [];\n\n        if (DEBUG) console.log(\"DELTA\", s.delta);\n        if (s.delta == false) {\n\n            // item.height\n            let inconsequentToggle = async (id, state) => {\n                await cape.api.updateSeriesConfiguration(id, { inconsequent: state });\n                let { x, y, w, h, gutter } = this.getElementPosition(no);\n                if (state == false) {\n                    if (s.tag) s.removeChild(s.tag);\n                } else {\n\n                }\n                item.inconsequent = state;\n                this.pixiApp.render();\n                this.vue.updateAfterChange(false);\n            };\n            if (DEBUG) console.log(item.inconsequent);\n            //inconsequentToggle(item.id, !item.inconsequent);\n\n            let seriesNoLocal = this.seriesToDim.indexOf(no2);\n            let seriesIdLocal = this.seriesToDimId[seriesNoLocal];\n\n            if (seriesIdLocal) {\n                let tableConfiguration = _.find(this.inconsequentArray, { series: seriesIdLocal });\n                this.vue.inconsequentToggle(item.height, tableConfiguration);\n                s.delta = false;\n                this.changedSerie.push(serie.id);\n            }\n\n\n            // this.vue.selectConfiguration(item, no2);\n            //  this.vue.draw(item.id);\n            // s.delta = false;\n        } else if (s.delta === true) {\n\n            if (DEBUG) console.log(\"wtf\", d, s, item, no2, serie);\n            let cache = serie.items.map(item => ({ order: item.order }));\n\n            // Apply order change\n\n            let cloneMode = this.editMode == \"clone\";\n\n            if (cloneMode) {\n                let selectedSpriteNoData = _.findIndex(cache, { order: this.draggedInitialNo });\n                let targetSpriteNoData = _.findIndex(cache, { order: this.draggedTargetNo });\n\n                let data = serie.items[selectedSpriteNoData];\n                if (DEBUG) console.log(\"before diff\", \"fin\", data);\n                if (data.config == -1) {\n                    data = serie.items[targetSpriteNoData];\n                    if (DEBUG) console.log(\"before diff\", \"fin2\", data);\n                }\n\n                serie.items.splice(this.draggedTargetNo, 1,\n                    {\n                        id: data.componentID, type: \"component\", isNew: true,\n                        height: this.heights.indexOf(data.height),\n                        order: this.draggedTargetNo, config: data.componentID\n                    })\n\n                this.changedSerie.push(serie.id);\n\n                // if(DEBUG) console.log(\"before diff\", \"fin\", data);\n            } else {\n\n                let selectedSpriteNoData = _.findIndex(cache, { order: this.draggedInitialNo });\n                let targetSpriteNoData = _.findIndex(cache, { order: this.draggedTargetNo });\n\n                let data = serie.items[selectedSpriteNoData];\n                if (DEBUG) console.log(\"before diff\", selectedSpriteNoData, targetSpriteNoData, serie.items, \"fin\", data);\n                if (data.config == -1) {\n                    data = serie.items[targetSpriteNoData];\n                    if (DEBUG) console.log(\"before diff\", \"fin2\", data);\n                }\n\n\n                serie.items.splice(this.draggedInitialNo, 1, { id: -1, order: this.draggedInitialNo, fake: true });\n\n                serie.items.splice(this.draggedTargetNo, 1,\n                    {\n                        id: data.componentID, type: \"component\", isNew: true,\n                        height: this.heights.indexOf(data.height),\n                        order: this.draggedTargetNo, config: data.componentID\n                    })\n\n                this.changedSerie.push(serie.id);\n\n                /* this.getSortArray.map((item, i) => {\n                     let n = item.data.order;\n                     let id = _.findIndex(cache, { order: n });\n                     serie.items[id].order = i;\n                 });*/\n            }\n            this.vue.updateAfterChange(true);\n        }\n    }\n\n    onDragMove(draggedItem, draggedItemType, sprite, e, series) {\n        if (sprite.dragging) {\n            this.pixiApp.render();\n            let gutter = 1;\n            if (sprite.delta) {\n                var newPosition = sprite.data.event.getLocalPosition(this.pixiApp.stage);\n                let p = Math.ceil((newPosition.x - this.delta.x) / gutter) * gutter;\n                sprite.position.x = p - 50;\n                this.liveSortExecute(draggedItemType, p, series, sprite);\n\n            } else {\n                var newPosition = sprite.data.event.getLocalPosition(this.pixiApp.stage);\n                var oldPosition = sprite.startPosition;\n\n                let r = Math.sqrt(\n                    Math.pow(newPosition.y - oldPosition.y, 2) +\n                    Math.pow(newPosition.x - oldPosition.x, 2)\n                );\n                sprite.delta = false;\n                if (r > gutter) {\n                    sprite.delta = true;\n                    this.pixiHitests.removeChild(sprite);\n                    this.pixiHitests.addChildAt(sprite, this.pixiHitests.children.length);\n                }\n            }\n        }\n    }\n\n    __draw() {\n        this.pixiHitests.position.x = this.delta.x;\n        this.annotationsOverlay.position.x = this.delta.x;\n        this.vue.$refs.seriesNames.style.transform = `translateX(${this.delta.x}px)`;\n        //  if(DEBUG) console.log(`translateX(-${this.delta.x}px)`);\n        this.pixiApp.render();\n    }\n\n    _draw(ss) {\n\n        this.labels.map((label, i) => {\n            if (label) {\n                this.pixiApp.stage.removeChild(label);\n                this.labels[i] = null;\n                label = null;\n            }\n        });\n\n        this.sprites.map((sprite, i) => {\n            if (sprite) {\n                sprite.removeAllListeners();\n                this.pixiHitests.removeChild(sprite);\n                this.sprites[i] = null;\n                sprite = null;\n            }\n        });\n\n        this.labels = [];\n        this.sprites = [];\n        this.orderHittest = [];\n\n        if (!this.bgDrawn) {\n            let { width, height } = this.getSize();\n            let edgeStep = 80;\n            for (var i = 0; i < width / edgeStep; i++) {\n                for (var j = 0; j < height / edgeStep; j++) {\n                    this.backgroundContainer\n                        .lineStyle(1, 0x2222229, 1)\n                        .drawRect(i * edgeStep, j * edgeStep, 1, 1);\n                }\n            }\n            this.bgDrawn = true;\n        }\n\n        var no = 0;\n\n        if (this.series.length > 0) {\n            let moveX = Math.round(this.delta.x / 20);\n            this.series.map((serie, no) => {\n                console.log(1234, serie);\n                let ordered = _.orderBy(serie.items, [\"order\"], [\"asc\"]).map((item, n) => {\n                    console.log(\"item\", item);\n                    this.drawItem(serie, n, item, item.type, ss);\n                });\n            })\n        }\n\n        this.drawSelectedSeries();\n\n        this.pixiApp.render();\n    }\n\n    drawText(text, x, y, c) {\n        return;\n        let pixiApp = this.pixiApp;\n        let sampleText = gltext.create(text);\n        this.labels.push(sampleText);\n        c.addChild(sampleText);\n        sampleText.position.set(10, 10);\n    }\n\n}\n\nconst renderer = new Director();\n\nexport {\n    renderer\n}", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('t-card',{attrs:{\"name\":\"Series settings\"}},[_c('div',{staticClass:\"card mid\"},[_c('div',{staticClass:\"card-main\"},[_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-content\"},[_c('t-select',{attrs:{\"target-model\":_vm.selectedSerie,\"neverEmpty\":\"neverEmpty\",\"options\":_vm.seriesList,\"section-label\":\"Selected series:\",\"update-model\":\"update-model\"},on:{\"save\":function (v){ return _vm.selectSerie(''+v.value); }}})],1)]),(_vm.selectedSerie)?_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-content\"},[_c('q-btn',{attrs:{\"label\":\"Change name\"}},[_c('q-popup-edit',{attrs:{\"title\":\"Change name\",\"buttons\":\"buttons\"},on:{\"save\":_vm.saveSerie},model:{value:(_vm.selectedSerieName),callback:function ($$v) {_vm.selectedSerieName=$$v},expression:\"selectedSerieName\"}},[_c('q-input',{attrs:{\"type\":\"text\"},model:{value:(_vm.selectedSerieName),callback:function ($$v) {_vm.selectedSerieName=$$v},expression:\"selectedSerieName\"}})],1)],1),_c('q-btn',{attrs:{\"label\":\"Remove\"},on:{\"click\":_vm.remove}})],1)]):_vm._e(),(_vm.selectedSerie)?_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-content\"},[(_vm.selectedSerie)?_c('q-tree',{attrs:{\"nodes\":_vm.tree,\"dark\":\"dark\",\"inverted-light\":\"inverted-light\",\"node-key\":\"label\",\"default-expand-all\":\"default-expand-all\"}}):_vm._e()],1)]):_vm._e()])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n   t-card(name=\"Series settings\")\n    .card.mid\n      .card-main\n        ._\n          .card-content\n            t-select(:target-model=\"selectedSerie\",\n                neverEmpty,\n                :options=\"seriesList\",\n                section-label=\"Selected series:\",\n                update-model,\n                @save=\"v=>selectSerie(''+v.value)\")\n        ._(v-if=\"selectedSerie\")\n          .card-content\n            q-btn(label=\"Change name\")\n              q-popup-edit(v-model=\"selectedSerieName\", title=\"Change name\", buttons, @save=\"saveSerie\")\n                q-input(type=\"text\", v-model=\"selectedSerieName\")\n            q-btn(label=\"Remove\", @click=\"remove\")\n        ._(v-if=\"selectedSerie\")\n          .card-content\n            q-tree(:nodes=\"tree\", dark, inverted-light, node-key=\"label\", default-expand-all, v-if=\"selectedSerie\")\n</template>\n<style lang=\"scss\">\n@import '~@theme/card.scss';\n</style>\n<script>\nimport { cape } from '@core/cape'\nimport { tylkoCapeComponents } from '@cape-ui'\n\nexport default {\n    name: 'Tylko-Series',\n\n    props: {\n        series: Array,\n    },\n\n    components: {\n        ...tylkoCapeComponents,\n    },\n\n    data() {\n        return {\n            tree: [],\n            selectedSerie: -1,\n            selectedSerieName: '',\n        }\n    },\n\n    computed: {\n        seriesList() {\n            return this.series.map(serie => ({\n                label: serie.name,\n                value: parseInt(serie.id),\n            }))\n        },\n    },\n\n    watch: {\n        seriesList() {\n            //   this.selectedSerie = this.$route.params.selectedSerie;\n        },\n\n        series() {\n            if (this.series) {\n                let serie = _.find(this.series, { id: this.selectedSerie })\n                if (serie) this.selectedSerieName = serie.name\n                this.tree = serie.tree\n            }\n        },\n\n        $route(to, from) {\n            this.selectedSerie = +this.$route.params.selectedSerie\n            if (this.series) {\n                let serie = _.find(this.series, { id: this.selectedSerie })\n                if (serie) this.selectedSerieName = serie.name\n                this.tree = serie.tree\n            }\n        },\n    },\n\n    mounted() {\n        this.selectedSerie = +this.$route.params.selectedSerie\n        if (this.series) {\n            let serie = _.find(this.series, { id: this.selectedSerie })\n            if (serie) this.selectedSerieName = serie.name\n        }\n    },\n\n    methods: {\n        async saveSerie(name) {\n            let query = cape.api.updateSerie(+this.$route.params.selectedSerie, {\n                name,\n            })\n            let queryResult = await query\n            this.$emit('loadTables')\n            this.$emit('render')\n        },\n\n        remove() {\n            this.$q\n                .dialog({\n                    title: 'Delete',\n                    message: 'This action will delete serie',\n                    ok: 'Ok',\n                    cancel: 'Cancel',\n                })\n                .onOk(async () => {\n                    await cape.api.removeSeries(this.selectedSerie)\n                    this.$emit('loadTables')\n                    this.$emit('render')\n                })\n        },\n\n        selectSerie(serieId) {\n            this.$router.push(\n                `/relations/${this.$route.params.selectedCollection}/${\n                    this.$route.params.selectedTable\n                }/${serieId}`\n            )\n        },\n\n        broadcastNewParameters(source) {\n            cape.api.updateComponent(this.comp.id, {\n                additional_params: source,\n            }).subscribe(() => {\n                this.$emit('newParameters')\n            })\n        },\n    },\n}\n</script>\n", "import mod from \"-!../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./series-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./series-card.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./series-card.vue?vue&type=template&id=3bb2da30&lang=pug&\"\nimport script from \"./series-card.vue?vue&type=script&lang=js&\"\nexport * from \"./series-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./series-card.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('t-card',{attrs:{\"name\":(\"Table Settings \" + _vm.selectedTableName)}},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-main\"},[(_vm.tablesList.length > 0)?_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-content\"},[_c('t-select',{attrs:{\"target-model\":_vm.newModel,\"neverEmpty\":\"neverEmpty\",\"options\":_vm.tablesList,\"section-label\":\"Selected table:\",\"update-model\":\"update-model\"},on:{\"save\":function (v){ return _vm.selectTable(v.value); }}})],1)]):_vm._e(),_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-content\"},[(_vm.selectedTable != -1)?_c('div',{staticClass:\"__\"},[_c('q-btn',{attrs:{\"label\":\"Rename\"}}),_c('q-popup-edit',{attrs:{\"title\":\"Change name\",\"buttons\":\"buttons\"},on:{\"save\":_vm.saveTable},model:{value:(_vm.selectedTableName),callback:function ($$v) {_vm.selectedTableName=$$v},expression:\"selectedTableName\"}},[_c('q-input',{attrs:{\"type\":\"text\"},model:{value:(_vm.selectedTableName),callback:function ($$v) {_vm.selectedTableName=$$v},expression:\"selectedTableName\"}})],1)],1):_vm._e(),_c('div',{staticClass:\"__\"},[_c('q-btn',{attrs:{\"label\":\"Add new\"}}),_c('q-popup-edit',{attrs:{\"title\":\"Add new table\",\"buttons\":\"buttons\"},on:{\"save\":_vm.createTable},model:{value:(_vm.newTableName),callback:function ($$v) {_vm.newTableName=$$v},expression:\"newTableName\"}},[_c('q-input',{attrs:{\"type\":\"text\"},model:{value:(_vm.newTableName),callback:function ($$v) {_vm.newTableName=$$v},expression:\"newTableName\"}})],1),(_vm.newModel)?_c('q-btn',{attrs:{\"label\":\"Remove\"},on:{\"click\":_vm.remove}}):_vm._e()],1)])]),(_vm.tablesList.length > 0)?_c('div',{staticClass:\"_\"},[_c('div',{staticClass:\"card-main\"},[(_vm.newModel)?_c('q-tree',{attrs:{\"nodes\":_vm.tree,\"dense\":\"dense\",\"dark\":\"dark\",\"inverted-light\":\"inverted-light\",\"node-key\":\"label\",\"default-expand-all\":\"default-expand-all\"}}):_vm._e()],1)]):_vm._e(),(!(_vm.tablesList.length > 0))?_c('div',{staticClass:\"_\"},[_c('div',[_c('p',[_vm._v(\"No tables exist for the selected colelction\")])])]):_vm._e()])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n  t-card(:name=\"`Table Settings ${selectedTableName}`\")\n   .card\n    .card-main\n      ._(v-if=\"tablesList.length > 0\")\n        .card-content\n          t-select(:target-model=\"newModel\",\n            neverEmpty,\n            :options=\"tablesList\",\n            section-label=\"Selected table:\",\n            update-model,\n            @save=\"v=>selectTable(v.value)\")\n      ._\n        .card-content\n          .__(v-if=\"selectedTable != -1\")\n            q-btn(label=\"Rename\")\n            q-popup-edit(v-model=\"selectedTableName\", title=\"Change name\", buttons, @save=\"saveTable\")\n              q-input(type=\"text\", v-model=\"selectedTableName\")\n          .__\n            q-btn(label=\"Add new\")\n            q-popup-edit(v-model=\"newTableName\", title=\"Add new table\", buttons, @save=\"createTable\")\n              q-input(type=\"text\", v-model=\"newTableName\")\n            q-btn(label=\"Remove\", @click=\"remove\", v-if=\"newModel\")\n      ._(v-if=\"tablesList.length > 0\")\n        .card-main\n          q-tree(:nodes=\"tree\", dense, dark, inverted-light, node-key=\"label\", default-expand-all, v-if=\"newModel\")\n      ._(v-if=\"!(tablesList.length > 0)\")\n        div\n          p No tables exist for the selected colelction\n</template>\n<style lang=\"scss\">\n@import '~@theme/card.scss';\n</style>\n<script>\nimport { cape } from '@core/cape';\n\nimport { tylkoCapeComponents } from '@cape-ui'\n\nexport default {\n    name: 'Tylko-Table',\n\n    data() {\n        return {\n            selectedTableName: '',\n            newTableName: '',\n            collectionTables: [],\n            tablesList: [],\n            newTableName: '',\n            selectedTable: -1,\n            newModel: -1,\n            syncing: false,\n            tree: [],\n        }\n    },\n\n    props: {\n        collectionID: [String, Number],\n        selectedSeries: Array,\n        tableConfigurations: Array,\n        tablesList: Array,\n    },\n\n    components: {\n        ...tylkoCapeComponents,\n    },\n\n    watch: {\n        selectedSeries() {\n            this.selectedTable = this.$route.params.selectedTable\n            //  this.updateTable();\n        },\n\n        selectedTableName() {},\n\n        tablesList() {\n            if (\n                !this.selectedTable &&\n                this.tablesList.length > 0 &&\n                !this.$route.params.selectedTable\n            ) {\n                this.selectedTable = this.tablesList[0].value\n                this.selectTable(this.tablesList[0].value)\n            }\n            this.newModel = parseInt(this.$route.params.selectedTable)\n        },\n\n        newModel(id) {\n            this.tablesList.map(table => {\n                if (table.table.id == id) {\n                    // console.log(\"ye\",table);\n                    this.selectedTableName = table.label\n                    this.tree = table.table.tree\n                    console.log('tree', table)\n                }\n            })\n            this.$emit('loadTables')\n        },\n    },\n\n    mounted() {\n        //  this.selectedTable = this.$route.params.selectedTable;\n    },\n\n    methods: {\n        selectTable(tableId) {\n            this.selectedTable = tableId\n            let isSeriesChoosen = this.$route.params.selectedSerie\n            this.$router.push(\n                `/relations/${\n                    this.$route.params.selectedCollection\n                }/${tableId}${isSeriesChoosen ? '/' + isSeriesChoosen : ''}`\n            )\n            this.newModel = parseInt(this.$route.params.selectedTable)\n        },\n\n        async diffSeriesConfigurations() {\n            this.selectedSeries\n        },\n\n        async saveTable(name) {\n            let query = cape.api.updateTable(this.newModel, {\n                name,\n            })\n            let queryResult = await query\n            this.$emit('loadTables')\n        },\n\n        remove() {\n            this.$q\n                .dialog({\n                    title: 'Delete',\n                    message: 'This action will delete table',\n                    ok: 'Ok',\n                    cancel: 'Cancel',\n                })\n                .onOk(async () => {\n                    await cape.api.removeTable(this.newModel)\n                    this.$emit('loadTables')\n                    this.$emit('render')\n                    this.newModel = -1\n                })\n        },\n\n        async createTable(name) {\n            let query = cape.api.createTable({\n                collection: this.$route.params.selectedCollection,\n                name,\n            })\n            let queryResult = await query\n            this.selectTable(queryResult.id)\n            this.$emit('loadTables')\n        },\n\n        getCurrentTableConfigs(tableId) {\n            let currentTableConfigations = []\n            this.tablesList.map(table => {\n                if (table.table.id == tableId) {\n                    currentTableConfigations = table.table.configurations\n                }\n            })\n            return currentTableConfigations\n        },\n\n        async updateTable(selectedSeries) {\n            this.syncing = true\n\n            // Helpers\n\n            let tableId = this.newModel\n            let collection = this.$route.params.selectedCollection\n\n            let currentTableConfigations = this.getCurrentTableConfigs(tableId)\n\n            let createTableConfg = serie =>\n                cape.api.createTableConfiguration({\n                    table: tableId,\n                    serie,\n                    collection,\n                })\n            let removeTableConfg = id => cape.api.removeTableConfiguration(id)\n\n            // Datasets\n\n            //  console.log(1234, \"currentTableConfigations\", currentTableConfigations);\n\n            let currentSeries = selectedSeries\n            let savedConigurations = currentTableConfigations.map(\n                conf => conf.series\n            )\n\n            //   console.log(1234,\"a\",currentSeries);\n            //   console.log(1234,\"b\",savedConigurations);\n\n            // Cross matched existion configurations\n\n            let diff = {\n                remove: _.differenceWith(\n                    savedConigurations,\n                    currentSeries,\n                    _.isEqual\n                ),\n                add: _.differenceWith(\n                    currentSeries,\n                    savedConigurations,\n                    _.isEqual\n                ),\n            }\n\n            // Execute\n\n            diff.remove = diff.remove.map(\n                serie => _.find(currentTableConfigations, { series: serie }).id\n            )\n\n            //   console.log(1234,\"a2\",diff.add);\n            //  console.log(1234,\"b2\",diff.remove);\n\n            for await (let newConfig of diff.add.map(createTableConfg)) {\n            }\n            if (removeTableConfg) {\n                for await (let removedConfig of diff.remove.map(\n                    removeTableConfg\n                )) {\n                }\n            }\n\n            this.$emit('loadTables')\n            this.$emit('loaded', selectedSeries)\n            console.log('dddddim', selectedSeries)\n        },\n    },\n}\n</script>\n", "import mod from \"-!../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./table-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./table-card.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./table-card.vue?vue&type=template&id=b7994140&lang=pug&\"\nimport script from \"./table-card.vue?vue&type=script&lang=js&\"\nexport * from \"./table-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./table-card.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template lang=\"pug\">\n    section.relations-editor-container\n        .flex\n            .row\n                .col(style=\"width: calc(100vw);overflow:hidden;position: relative;\")\n                    ._\n                        t-bar(v-once, ref=\"bar\", menu=\"true\", \n                            :box=\"{ width: 120, height: 90, cols: 2, rows: 2, gutter: 8 }\",\n                            :actAsNavigation=\"false\",\n                            @searchMatch=\"lightup\",\n                            typesToDisplay=\"componentRelations\")\n            .row\n                .col(style=\"width: calc(100vw - 420px - 40px)\")\n                    .row.firstRowRelations\n                        div.col.seriesNames(ref=\"seriesNames\", v-if=\"queryResult\")\n                            div.collection-name(v-for=\"serie in currentComponentSeries\", \n                                :class=\"{ 'selectedCollectioName': serie.id==$route.params.selectedSerie }\") \n                                div.name \n                                    router-link(class=\"relLink\", :to=\"`/relations/${$route.params.selectedCollection}/${$route.params.selectedTable}/${serie.id}`\") {{ serie.name }}\n                                div.select\n                                    q-checkbox(color=\"pink\", dark, dense,v-model=\"choosenSeriesForTable\",:disabled=\"$refs.tableEditor.syncing\" label=\"Use\", @input=\"updateTable\",:val=\"serie.id\")\n                    div.flex.row.secondRowRelatcions\n                        div.col.heights\n                            div.series-height(v-for=\"height in heights\") {{{\"300\": \"B\", \"400\": \"C\", \"500\": \"D\", \"600\": \"E\", \"700\": \"F\", \"800\": \"G\", \"900\": \"H\", \"1000\": \"I\"}[height]}} {{height}}\n                        div.col.relations-editor-container-canvas(ref=\"container\")\n                            div.renderer-relations(ref=\"renderer\")\n                \n                .col(style=\"max-width: 420px;\")\n                    q-scroll-area.scrollRel\n                        t-card(name=\"Editor settings\")\n                            .card.mid\n                                ._\n                                    .card-main\n                                        .card-content\n                                            t-presets(:options=\"[{ label: 'Replace', value: 'replace' }, { label: 'Normal', value: 'normal' } ]\", \n                                                :target-model=\"dropMode\",\n                                                big,\n                                                section-label=\"Drop mode\")\n                                            t-presets(:options=\"[{ label: 'Replace', value: 'replace' }, { label: 'Clone', value: 'clone' } ]\", \n                                                :target-model=\"editMode\",\n                                                big,\n                                                section-label=\"Swap mode\")\n                                ._\n                                    .card-main\n                                        t-section(name=\"Extra settings\")\n                                            q-list\n                                                q-item\n                                                    q-select(v-model=\"mini\", inverted-light, color=\"grey-7\", separator, :options=\"miniops\", @input=\"selectMiniMode\")\n                                                q-item\n                                                    q-btn(@click.native=\"prepareMiniaturesAll\",:loading=\"batchRunning==2\", label=\"Render all miniatures\",  :percentage=\"miniaturesDone\")\n                                                q-item\n                                                    q-btn(@click.native=\"prepareMiniaturesCollection\", :loading=\"batchRunning==1\", :label=\"`Render miniatures for collection`\", :percentage=\"miniaturesDone\")\n                                             \n                                //._\n                                    .card-main\n                                        t-section(name=\"Apperance\")\n                                            q-list\n                                                q-item \n                                                    p Reflesh page to see changes\n                                                q-item(v-for=\"element in miniApperance\")\n                                                    p {{element.type}}\n                                                    q-color-picker(v-model=\"element.color\")\n                        t-table(ref=\"tableEditor\",\n                            :series=\"queryResult\", \n                            :tablesList=\"tablesList\",\n                            :tableConfigurations=\"tableConfigurations\",\n                            :colletionID=\"projectFilter\", \n                            :selectedSeries=\"choosenSeriesForTable\",\n                            @loadTables=\"loadTables\",\n                            @loaded=\"dimSeries\")\n                        t-series(:series=\"currentComponentSeries\", \n                            :colletionID=\"projectFilter\",\n                            @loadTables=\"loadTables\",\n                            @render=\"rerrender\")\n</template>\n\n<style lang=\"scss\">\n@import '~@theme/card.scss';\n\n.scrollRel {\n    width: 420px;\n    height: calc(100vh - 235px);\n    background: #131313;\n}\n\n.selectedCollectioName {\n    font-weight: bold;\n\n    //background-image: linear-gradient(rgba(255, 0, 0, 0), rgba(255, 255, 255, 0.281));\n    &:after {\n        display: block;\n        content: '';\n        width: 100%;\n        height: 60px;\n        padding: 10px;\n        box-sizing: border-box;\n    }\n}\n\n.relLink {\n    text-align: center;\n    text-decoration: none;\n    color: white;\n    height: 38px;\n    display: inline-block;\n}\n\n.smallTabsFont {\n    .q-tab-label {\n        font-size: 10px;\n    }\n}\n\n.relations-editor-container-canvas {\n    height: calc(100vh - 294px);\n    width: 100%;\n}\n\n.relations-editor-container {\n    color: white;\n}\n\n.secondRowRelatcions {\n    margin-top: 85px;\n}\n\n.firstRowRelations {\n    height: 70px;\n    width: 100%;\n    overflow: hidden;\n    position: absolute;\n    top: 255px;\n}\n\n.heights {\n    max-width: 60px !important;\n}\n\n.dark {\n    background: rgb(36, 36, 36);\n}\n\n.seriesNames {\n    position: absolute;\n    margin-left: 60px;\n    display: flex;\n    height: 80px;\n\n    .collection-name {\n        overflow: hidden;\n        width: 100px;\n        height: 80px;\n        position: relative;\n        min-width: 100px;\n\n        .name {\n        }\n\n        .select {\n        }\n    }\n}\n\n.series-height {\n    display: block;\n    max-width: 100px;\n    overflow: hidden;\n    width: 50px;\n    height: 100px;\n}\n</style>\n\n<script>\nlet Editor = {\n    ORIENTATION_VERTICAL: 'vertical',\n    ORIENTATION_HORIZONTAL: 'horizontal',\n}\n\nconst uuidv4 = require('uuid/v4')\n\nimport _ from 'lodash'\n\nimport { cape } from '@core/cape'\n\nimport { renderer } from '@tylko/cape-entrypoints/editors/cape-relations-editor/relations-renderer-new'\nimport seriesCard from '@tylko/cards/relations/series-card'\nimport tableCard from '@tylko/cards/relations/table-card'\nimport TylkoBar from '@tylko/cape-palette/palette-bar'\n\nimport { tylkoCapeComponents } from '@cape-ui'\n\nconst DEBUG = false\n\nexport default {\n    name: 'TylkoEditor',\n\n    components: {\n        't-series': seriesCard,\n        't-table': tableCard,\n        't-bar': TylkoBar,\n        ...tylkoCapeComponents,\n    },\n\n    props: ['comp', 'mesh', 'meshMode', 'currentSetup'],\n\n    data() {\n        return {\n            miniApperance: null,\n            colors: '#ddffaa',\n            editMode: 'replace',\n            dropMode: 'replace',\n            checked: false,\n            bgDrawn: false,\n            items: [],\n            labels: [],\n            sprites: [],\n            fetching: false,\n            orderHittest: [],\n            queryResult: null,\n            heights: [],\n            choosenSeriesForTable: [],\n            tablesList: [],\n            collectionTables: [],\n            tableConfigurations: [],\n            currentComponentSeries: [],\n            miniaturesDone: 0,\n            batchRunning: false,\n            miniops: [\n                {\n                    label: 'standard',\n                    value: 'triangles',\n                },\n                {\n                    label: 'the wire \"frame\"',\n                    value: 'line loop',\n                },\n            ],\n            mini: cape.application.settings.get('ministyle') || 'triangles',\n        }\n    },\n\n    methods: {\n        updateTable() {\n            this.$refs.tableEditor.updateTable(this.choosenSeriesForTable)\n        },\n\n        lightup(ids) {\n            renderer.lightupByID(ids, true)\n        },\n\n        selectMiniMode(mode) {\n            cape.application.settingss.set('ministyle', mode)\n        },\n\n        async dimSeries(input) {\n            if (DEBUG) console.log(123, 'dodo', input)\n            let ids = input // || this.$refs.tableEditor.getCurrentTableConfigs(this.$route.params.selectedTable);\n\n            let id2no = ids\n                .map(table => {\n                    let hit = false\n                    this.currentComponentSeries.map((series, no) => {\n                        if (table == series.id) {\n                            hit = no\n                        }\n                    })\n                    return hit\n                })\n                .filter(v => v !== false)\n\n            renderer.dimSeries(id2no, input)\n        },\n\n        async loadSeries() {\n            let query = cape.api.palette.relations(\n                this.$route.params.selectedCollection\n            ).componentSeries.thumbs\n\n            this.collectionTables = await query.fetch()\n            this.currentComponentSeries = this.collectionTables[\n                'component-series'\n            ]\n\n            // choosenSeriesForTable\n            // SORT\n        },\n\n        async loadTables() {\n            let query = cape.api.palette.relations(\n                this.$route.params.selectedCollection\n            ).componentTable.tableConfigurations.thumbs\n\n            this.collectionTables = await query.fetch()\n            this.tablesList = this.collectionTables['component-table'].map(\n                table => ({\n                    label: table.name,\n                    value: table.id,\n                    table,\n                })\n            )\n\n            let currentTableConfigations = []\n            this.tablesList.map(table => {\n                if (table.table.id == this.$route.params.selectedTable) {\n                    this.currentTable = table.table\n                    currentTableConfigations = table.table.configurations\n                }\n            })\n\n            this.choosenSeriesForTable = currentTableConfigations.map(\n                conf => conf.series\n            )\n\n            this.selectSeries()\n            this.selectInconsequent(currentTableConfigations)\n\n            // this.tableConfigurations = this.collectionTables['component-table-configuration']\n            //   .map((table) => ({ label: table.name, value: table.id }));\n            //  this.dimSeries();\n\n            // SORT\n            /*\n      //this.currentComponentSeries = this.collectionTables['component-series'];\n      console.log(\"SORT\", this.choosenSeriesForTable, this.currentComponentSeries);\n\n      let selectedSeries = _.filter(this.currentComponentSeries, \n        (serie) => this.choosenSeriesForTable.indexOf(serie.id) > -1);\n      let unselectedSeries = _.difference(this.currentComponentSeries, selectedSeries);\n\n      console.log(\"SORT\", selectedSeries, unselectedSeries);\n\n      this.currentComponentSeries = selectedSeries.concat(_.orderBy(unselectedSeries, \n        ['name'], ['asc']));\n*/\n            this.$refs.tableEditor.syncing = false\n        },\n\n        // new {\n        uuid() {\n            return uuidv4()\n        },\n\n        selectConfiguration(item, no) {},\n\n        prepareMiniaturesAll() {\n            this.prepareMiniatures()\n        },\n\n        prepareMiniaturesCollection() {\n            this.prepareMiniatures(this.$route.params.selectedCollection)\n        },\n\n        async prepareMiniatures(specific) {\n            let allComponents = [],\n                total = 0,\n                done = 0\n            var allCollections\n            let componentsOfCollections\n\n            if (!specific) {\n                var {\n                    collection: allCollections,\n                } = await cape.api.palette.collection().fetch()\n                componentsOfCollections = allCollections.map(collection => {\n                    return cape.api.palette\n                        .relations(collection.id)\n                        .component.thumbs.fetch()\n                })\n            } else {\n                componentsOfCollections = await cape.api.palette\n                    .relations(specific)\n                    .component.thumbs.fetch()\n                componentsOfCollections = [componentsOfCollections]\n            }\n\n            for await (let collectionComponents of componentsOfCollections) {\n                allComponents.push(collectionComponents.component)\n                total += collectionComponents.component.length\n            }\n\n            allComponents = _.flatten(allComponents)\n\n            let render = () => {\n                let index = 0\n                let next = () => {\n                    if (index <= allComponents.length) {\n                        let comp = allComponents[index]\n                        cape.api.geo\n                            .componentSet({\n                                type: 'componentRelations',\n                                id: comp.id, // component-set id\n                                queryForMiniature: true,\n                            })\n                            .forceFetch(true)\n                            .pipe(\n                                geo => {\n                                    //add(geo, item.id);\n                                    if (DEBUG) console.log('123', geo)\n                                    cape.services.miniatures\n                                        .add({\n                                            data: geo,\n                                            id: comp.id,\n                                            fetched: false,\n                                        })\n                                        .then(async result => {\n                                            await cape.api.updateComponentThumb(\n                                                comp.id,\n                                                {\n                                                    thumbnails_data: result.error\n                                                        ? [{}]\n                                                        : result.geo,\n                                                }\n                                            )\n                                            next()\n                                            done++\n                                            index++\n                                            this.miniaturesDone =\n                                                (done / total) * 100\n                                        })\n                                        .catch(() => {})\n                                },\n                                'paletteRelations'\n                            )\n                    }\n                }\n                next()\n            }\n\n            this.$q\n                .dialog({\n                    title: 'Confirm',\n                    message: `Batch render all ${total} mianitures?`,\n                    ok: 'Well ok.',\n                    cancel: 'No',\n                })\n                .onOk(() => {\n                    this.batchRunning = specific ? 1 : 2\n                    render()\n                })\n        },\n\n        getHeightFromSetup() {\n            return 200 // parseInt(this.currentSetup.label);\n        },\n\n        rerrender() {\n            this.computeItems(true)\n        },\n\n        async computeItems(\n            drawAfter = false,\n            needsUpdate = true,\n            first = true\n        ) {\n            renderer.fetching = true\n\n            if (!needsUpdate) {\n                if (drawAfter) {\n                    renderer.currentState = this.serializeState()\n                    renderer._draw()\n                }\n                return\n            }\n            //api.removeSeriesConfiguration(9).subscribe(()=>{});\n\n            this.projectFilter = this.$route.params.selectedCollection\n\n            let query = cape.api.palette.relations(this.projectFilter)\n                .componentSeries.component.thumbs\n            let queryResult = await query.fetch()\n            this.queryResult = queryResult\n            this.currentComponentSeries = queryResult['component-series']\n            renderer.queryResult = queryResult\n            let { ['component-series']: seriesObject } = queryResult\n\n            if (DEBUG) console.log('SERIES', seriesObject)\n\n            this.rawSeriesObject = seriesObject\n\n            let heights = (this.heights = [\n                300,\n                400,\n                500,\n                600,\n                700,\n                800,\n                900,\n                1000,\n            ]) // rows\n\n            let series = seriesObject\n\n            renderer.rawSeries = series\n\n            let item = (no, cell) => {\n                return {\n                    order: no,\n                    height: cell.height,\n                    inconsequent: cell.inconsequent,\n                    setId: cell.component_set,\n                    compName: cell.component_name,\n                    id: cell.id,\n                    configurationID: cell.id,\n                    config: cell.component,\n                    componentID: cell.component,\n                    type: 'component',\n                    fake: false,\n                }\n            }\n\n            let emptyItem = (no, h) => ({\n                order: no,\n                height: h,\n                name: 'space',\n                id: no,\n                config: -1,\n                type: 'empty',\n                fake: true,\n            })\n\n            let seriesConfigMatchHeight = (cell, h) => cell.height == h\n\n            series = series.map((seria, seriaNo) => {\n                console.log('seria', seria)\n                seria.configurations = _.orderBy(\n                    seria.configurations,\n                    ['height'],\n                    ['asc']\n                )\n                let items = [],\n                    cellNo = 0,\n                    emptyFillNo = 0,\n                    no = 0\n                for (let height of heights) {\n                    let cell = _.find(seria.configurations, {\n                        height: height,\n                    })\n                    if (DEBUG) console.log(cellNo, cell, height, (cell, height))\n                    console.log('cell', cell)\n                    if (cell) {\n                        items.push(item(no++, cell))\n                    } else {\n                        items.push(emptyItem(no++, height))\n                    }\n                }\n                return { ...seria, idserii: seria.id, items, order: seriaNo }\n            })\n\n            let items = [],\n                no = 0\n            for (let height of heights) items.push(emptyItem(no++))\n            series.push({\n                id: 'new',\n                order: 2,\n                config: -1,\n                items,\n            })\n\n            let renderable = []\n            series.map((seria, seriaNo) => {\n                seria.items.map((item, itemNo) => {\n                    item.order = seriaNo\n                    item.inconsequent = item.inconsequent\n                    if (DEBUG) console.log('qq', item)\n                    // Organizacja serii w format a la tablica\n                    if (renderable[itemNo]) {\n                        renderable[itemNo].items.push(item)\n                    } else {\n                        renderable[itemNo] = {\n                            items: [item],\n                            id: itemNo,\n                            idserii: seria.idserii,\n                            seriesID: seria.id,\n                            type: 'serie',\n                            order: itemNo,\n                        }\n                    }\n                })\n            })\n\n            renderer.series = renderable\n            if (drawAfter) {\n                renderer.currentState = this.serializeState()\n                renderer._draw()\n            }\n\n            if (first) {\n                this.dimSeries(\n                    this.$refs.tableEditor\n                        .getCurrentTableConfigs(\n                            this.$route.params.selectedTable\n                        )\n                        .map(t => t.series)\n                )\n            }\n\n            this.selectSeries()\n            renderer.fetching = false\n        },\n\n        reload() {\n            this.$forceUpdate()\n        },\n\n        async saveOrder() {},\n\n        async addConfiguration(item, meshMode = false) {},\n\n        build() {\n            this.draw()\n        },\n\n        executeChanges(next) {\n            if (DEBUG)\n                console.log('diff', 'Looking for differences after action...')\n\n            let actionsToPerform = []\n            let diff = this.findDifferences(this.serializeState())\n\n            if (DEBUG)\n                console.log(\n                    'diff',\n                    'full',\n                    diff,\n                    diff,\n                    renderer.currentState,\n                    this.serializeState()\n                )\n\n            let removeEmptyCells = diffArray =>\n                diffArray.filter(cell => !cell.fake)\n\n            diff.add = diff.add.map(removeEmptyCells)\n            diff.remove = diff.remove.map(removeEmptyCells)\n\n            if (DEBUG) console.log('diff', diff)\n\n            diff.remove.map((serie, serieNo) => {\n                serie.map(cell => {\n                    let seriesConfigurationID = cell.id\n                    let action = {\n                        type: 'REMOVE',\n                        config: seriesConfigurationID,\n                    }\n\n                    if (DEBUG)\n                        console.log(\n                            'diff',\n                            `Remove cell with ID ${seriesConfigurationID}`,\n                            cell\n                        )\n                    actionsToPerform.push(action)\n                })\n            })\n\n            diff.add.map((serie, serieNo) => {\n                serie.map(cell => {\n                    let seriesConfigurationID = cell.id\n                    let configHeight = cell.height\n                    let serieID = cell.serie\n                    let isNew = cell.isNew\n\n                    let action = {\n                        type: 'ADD',\n                        config: seriesConfigurationID,\n                        targetSerieNo: serieID,\n                    }\n\n                    if (isNew) {\n                        action = {\n                            ...action,\n                            height: configHeight == 300 ? 0 : configHeight,\n                        }\n                        action.type = 'ADDNEW'\n                    }\n\n                    if (DEBUG)\n                        console.log(\n                            'diff',\n                            `Add ${\n                                isNew ? 'NEW ' : ''\n                            }cell with ID ${seriesConfigurationID} to series ${serieID}`,\n                            action,\n                            cell\n                        )\n\n                    actionsToPerform.push(action)\n                })\n            })\n\n            let saveDiff = async () => {\n                let waitFor = []\n                let newCollection = false\n                let newCollectionOnDrop = false\n\n                for (let action of actionsToPerform) {\n                    var targetSeriesID\n                    if (action.type == 'ADD' || action.type == 'ADDNEW') {\n                        targetSeriesID = this.rawSeriesObject[\n                            action.targetSerieNo\n                        ]\n                        if (!targetSeriesID) {\n                            let { id } = await cape.api.createSeries({\n                                collection: this.projectFilter,\n                                name: `zzz`,\n                            })\n                            targetSeriesID = id\n                            newCollectionOnDrop = true\n                        } else {\n                            targetSeriesID = targetSeriesID.id\n                        }\n                    }\n\n                    let apicall\n                    switch (action.type) {\n                        case 'REMOVE':\n                            let isSpwapping =\n                                _.findIndex(actionsToPerform, {\n                                    type: 'ADD',\n                                    config: action.config,\n                                }) > -1\n                            if (!isSpwapping) {\n                                await cape.api.removeSeriesConfiguration(\n                                    action.config\n                                )\n                            }\n                            break\n                        case 'ADD':\n                            await cape.api.updateSeriesConfiguration(\n                                action.config,\n                                {\n                                    parent: targetSeriesID,\n                                }\n                            )\n                            break\n                        case 'ADDNEW':\n                            await cape.api.createSeriesConfiguration(\n                                targetSeriesID,\n                                action.config,\n                                this.heights[action.height]\n                            )\n                            break\n                    }\n\n                    /* if(newCollectionOnDrop) {\n                         setTimeout(() => {\n                           this.computeItems(true,true);\n                           this.selectSerie(targetSeriesID);\n                         }, 10);\n                     }*/\n\n                    next()\n                }\n            }\n\n            saveDiff()\n        },\n\n        selectSerie(serieId) {\n            if (!this.$route.params.selectedCollection) return\n            this.$router.push(\n                `/relations/${this.$route.params.selectedCollection}/${\n                    this.$route.params.selectedTable\n                }/${serieId}`\n            )\n        },\n\n        selectSeries() {\n            let id = +this.$route.params.selectedSerie\n            if (id) renderer.selectSeries(id)\n        },\n\n        selectInconsequent(configs) {\n            console.log('cfgs', configs)\n            this.currentInq = configs\n            renderer.setInconsequent(configs)\n        },\n\n        async inconsequentToggle(height, config) {\n            let tableInq = _.find(this.currentInq, {\n                id: config.id,\n            })\n\n            let inqState = tableInq.inconsequent ? tableInq.inconsequent : []\n\n            if (inqState.indexOf(height) > -1) {\n                inqState.splice(inqState.indexOf(height), 1)\n            } else {\n                inqState.push(height)\n            }\n\n            this.currentInq = inqState\n            let query = cape.api.updateTableConfiguration(config.id, {\n                inconsequent: inqState,\n            })\n            let queryResult = await query\n            this.$refs.tableEditor.$emit('loadTables')\n        },\n\n        draw() {\n            if (renderer.series) {\n                renderer.currentState = this.serializeState()\n                renderer._draw()\n            }\n        },\n\n        updateAfterChange(needsUpdate) {\n            renderer._draw()\n            if (renderer.fetching) return\n            this.executeChanges(() => {\n                this.computeItems(true, needsUpdate)\n            })\n        },\n\n        addItemDrop(item, target) {\n            let { id, type } = item\n            let { selectedSeries: serie, selectedHeight: height } = target\n            //    console.log(this.series);\n            let items = _.orderBy(\n                renderer.series[height].items,\n                ['order'],\n                ['asc']\n            )\n            if (items[serie].fake) {\n                items.splice(serie, 1, {\n                    id,\n                    type,\n                    isNew: true,\n                    height: height,\n                    order: serie,\n                    config: id,\n                })\n                renderer.series[height].items = items\n                this.updateAfterChange(true)\n            } else if (this.dropMode == 'replace') {\n                items.splice(serie, 1, {\n                    id,\n                    type,\n                    isNew: true,\n                    height: height,\n                    order: serie,\n                    config: id,\n                })\n                renderer.series[height].items = items\n                this.updateAfterChange(true)\n            }\n        },\n\n        findDifferences(newState, t) {\n            let diff = {\n                add: [],\n                remove: [],\n            }\n            for (let serieNo in renderer.currentState) {\n                diff.add.push(\n                    _.differenceWith(\n                        newState[serieNo],\n                        renderer.currentState[serieNo],\n                        _.isEqual\n                    )\n                )\n                diff.remove.push(\n                    _.differenceWith(\n                        renderer.currentState[serieNo],\n                        newState[serieNo],\n                        _.isEqual\n                    )\n                )\n            }\n            return diff\n        },\n\n        serializeState() {\n            let stateBySeries = [],\n                stateBySeriesSource = []\n            let table = []\n            let emptySeries = []\n\n            renderer.series.map((serie, serieNo) => {\n                serie.items.map(item => {\n                    stateBySeriesSource.push({\n                        id: item.id,\n                        height: item.height || this.heights[serieNo],\n                        serie: item.order,\n                        config: item.config,\n                        fake: item.fake,\n                        isNew: item.isNew,\n                    })\n                })\n            })\n\n            stateBySeries = _.groupBy(stateBySeriesSource, i => i.serie)\n\n            /*  emptySeries.map((no) => { \n        stateBySeries[no]=[];\n      });\n*/\n\n            if (DEBUG) console.log('####', stateBySeries)\n\n            return stateBySeries\n        },\n    },\n\n    created() {},\n\n    mounted() {\n        let apper = () => {\n            let colors = [\n                {\n                    type: 'back',\n                    id: 1,\n                    color: '#252120',\n                },\n                {\n                    type: 'doors',\n                    id: 2,\n                    color: '#32453A',\n                },\n                {\n                    type: 'supports',\n                    id: 3,\n                    color: '#252120',\n                },\n                {\n                    type: 'drawers',\n                    id: 4,\n                    color: '#802431',\n                },\n                {\n                    type: 'horizontal',\n                    id: 5,\n                    color: '#A19C8A',\n                },\n                {\n                    type: 'verticals',\n                    id: 6,\n                    color: '#A19C8A',\n                },\n                {\n                    type: 'insert',\n                    id: 7,\n                    color: '#FCFF69',\n                },\n                {\n                    type: '----',\n                    id: 8,\n                    color: '#00ff00',\n                },\n            ]\n\n            let miniStyleData = userSettings.get('miniApperance')\n            if (miniStyleData.data) {\n                miniStyleData.data.map(a => {\n                    let key = Object.keys(a)[0]\n                    let mId = _.findIndex(colors, {\n                        id: +key,\n                    })\n                    colors[mId].color = a[key]\n                })\n            }\n\n            return colors\n        }\n        cape.application.dnd.addDestinationComponent({\n            type: 'compset-editor',\n            instance: this,\n            incomingHandler: (data, event) => {\n                let drop = renderer.traceDropCoordinates(event)\n                this.addItemDrop(data, drop)\n            },\n        })\n\n        this.pixiApp = null\n        this.pixiRoot = null\n        this.drawingContainer = null\n        this.backgroundContainer = null\n\n        this.projectFilter = this.$route.params.selectedCollection\n        renderer.setContext(this)\n        this.computeItems(true, true, true)\n        this.loadTables()\n\n        cape.application.contextMenu\n            .listenAction({\n                action: 'reload',\n                context: 'relations',\n            })\n            .subscribe(() => {\n                this.computeItems(true, true, true)\n            })\n\n        this.miniApperance = apper()\n        window.dispatchEvent(new Event('resize'))\n    },\n\n    watch: {\n        miniApperance: {\n            handler() {\n                let ob = this.miniApperance.map(slot => ({\n                    [slot.id]: slot.color,\n                }))\n                userSettings.setObjectItem('miniApperance', 'data', ob)\n            },\n            deep: true,\n        },\n\n        currentComponentSeries() {},\n\n        $route(to, from) {\n            this.selectSeries()\n            this.dimSeries(\n                this.$refs.tableEditor\n                    .getCurrentTableConfigs(this.$route.params.selectedTable)\n                    .map(t => t.series)\n            )\n        },\n\n        dropMode() {\n            renderer.dropMode = this.dropMode\n        },\n\n        editMode() {\n            renderer.editMode = this.editMode\n        },\n\n        items() {\n            //this.build(1);\n        },\n\n        comp() {\n            //   this.computeItems();\n        },\n\n        currentSetup() {\n            //   this.computeItems();\n        },\n\n        currentHeight() {},\n    },\n}\n</script>\n<style lang=\"scss\">\n.relations-editor-container {\n    position: fixed;\n    min-height: 100vh;\n    width: calc(100vw - 60px);\n    background: black;\n}\n</style>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations-editor.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations-editor.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./relations-editor.vue?vue&type=template&id=1c30add9&lang=pug&\"\nimport script from \"./relations-editor.vue?vue&type=script&lang=js&\"\nexport * from \"./relations-editor.vue?vue&type=script&lang=js&\"\nimport style0 from \"./relations-editor.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./relations-editor.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template lang=\"pug\">\n    section(class=\"main\")\n        t-relations(ref=\"editor\")\n</template>\n<script>\nimport TylkoRelationsEditor from '@tylko/cape-entrypoints/editors/cape-relations-editor/relations-editor'\n\nimport { cape } from '@core/cape'\n\nexport default {\n    components: {\n        't-relations': TylkoRelationsEditor,\n    },\n\n    mounted() {\n        viewport.fixed(true)\n    },\n\n    methods: {\n        onResize(size) {\n            this.rendererHeight = size.height - 230\n        },\n\n        created() {\n            this.onResize({ height: window.innerHeight })\n        },\n    },\n\n    data() {\n        return {\n            activeCollection: null,\n        }\n    },\n}\n</script>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./relations.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./relations.vue?vue&type=template&id=5fbf5109&lang=pug&\"\nimport script from \"./relations.vue?vue&type=script&lang=js&\"\nexport * from \"./relations.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./table-card.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./table-card.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}