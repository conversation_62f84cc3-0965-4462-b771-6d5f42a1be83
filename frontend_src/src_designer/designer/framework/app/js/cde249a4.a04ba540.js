(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["cde249a4"],{"044b":function(e,t){
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return e!=null&&(r(e)||n(e)||!!e._isBuffer)};function r(e){return!!e.constructor&&typeof e.constructor.isBuffer==="function"&&e.constructor.isBuffer(e)}function n(e){return typeof e.readFloatLE==="function"&&typeof e.slice==="function"&&r(e.slice(0,0))}},"04a2":function(e,t,r){(function(t){var n=r("7a87");var a=new t([66,77,70,3]);e.exports=function(e){if(typeof e==="string")return e.substring(0,3)==="BMF";return e.length>4&&n(e.slice(0,4),a)}}).call(this,r("b639").Buffer)},"07c3":function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"container"},[r("div",{ref:"mini",staticClass:"mini"})])};var a=[];var i=r("a34a");var o=r.n(i);var s=r("192a");var c=r("c973");var l=r.n(c);var u=r("d6b6");var f=r("18a5");var p={props:{id:[String,Number],geo:[String,Object,Array],bgColor:[String],update:[String]},methods:{load:function e(t){var r=this;var n=f["a"].api.geo.componentSet({type:"componentRelations",id:t,queryForMiniature:true});n.pipe(function(e){console.log(e);r.build(e,t,false)},"paletteRelations")},build:function e(t,r,n){var a=this;f["a"].services.miniatures.add({data:t,id:r,fetched:n,base64:true,colorSet:"production",bgColor:this.bgColor}).then(function(){var e=l()(o.a.mark(function e(t){var r;return o.a.wrap(function e(n){while(1){switch(n.prev=n.next){case 0:while(a.$refs.mini&&a.$refs.mini.hasChildNodes()){a.$refs.mini.removeChild(a.$refs.mini.firstChild)}r=new Image;r.src=t.painterState;a.$refs.mini.appendChild(r);case 4:case"end":return n.stop()}}},e)}));return function(t){return e.apply(this,arguments)}}())}},created:function e(){},watch:{geo:function e(){this.build(this.geo,this.id,true)}},mounted:function e(){if(this.geo){this.build(this.geo,this.id,true);return}if(this.id){this.load(this.id)}}};var d=p;var h=r("f699");var v=r("2877");var m=Object(v["a"])(d,n,a,false,null,"360dcb88",null);var g=t["a"]=m.exports},"0f01":function(e,t,r){"use strict";var n=r("e9ac");var a=n("%Object%");var i=n("%TypeError%");var o=n("%String%");var s=r("c46d");var c=r("2057");var l=r("c612");var u=r("5975");var f=r("bb53");var p=r("21d0");var d=r("2f17");var h=r("a0d3");var v={ToPrimitive:d,ToBoolean:function e(t){return!!t},ToNumber:function e(t){return+t},ToInteger:function e(t){var r=this.ToNumber(t);if(c(r)){return 0}if(r===0||!l(r)){return r}return u(r)*Math.floor(Math.abs(r))},ToInt32:function e(t){return this.ToNumber(t)>>0},ToUint32:function e(t){return this.ToNumber(t)>>>0},ToUint16:function e(t){var r=this.ToNumber(t);if(c(r)||r===0||!l(r)){return 0}var n=u(r)*Math.floor(Math.abs(r));return f(n,65536)},ToString:function e(t){return o(t)},ToObject:function e(t){this.CheckObjectCoercible(t);return a(t)},CheckObjectCoercible:function e(t,r){if(t==null){throw new i(r||"Cannot call method on "+t)}return t},IsCallable:p,SameValue:function e(t,r){if(t===r){if(t===0){return 1/t===1/r}return true}return c(t)&&c(r)},Type:function e(t){if(t===null){return"Null"}if(typeof t==="undefined"){return"Undefined"}if(typeof t==="function"||typeof t==="object"){return"Object"}if(typeof t==="number"){return"Number"}if(typeof t==="boolean"){return"Boolean"}if(typeof t==="string"){return"String"}},IsPropertyDescriptor:function e(t){if(this.Type(t)!=="Object"){return false}var r={"[[Configurable]]":true,"[[Enumerable]]":true,"[[Get]]":true,"[[Set]]":true,"[[Value]]":true,"[[Writable]]":true};for(var n in t){if(h(t,n)&&!r[n]){return false}}var a=h(t,"[[Value]]");var o=h(t,"[[Get]]")||h(t,"[[Set]]");if(a&&o){throw new i("Property Descriptors may not be both accessor and data descriptors")}return true},IsAccessorDescriptor:function e(t){if(typeof t==="undefined"){return false}s(this,"Property Descriptor","Desc",t);if(!h(t,"[[Get]]")&&!h(t,"[[Set]]")){return false}return true},IsDataDescriptor:function e(t){if(typeof t==="undefined"){return false}s(this,"Property Descriptor","Desc",t);if(!h(t,"[[Value]]")&&!h(t,"[[Writable]]")){return false}return true},IsGenericDescriptor:function e(t){if(typeof t==="undefined"){return false}s(this,"Property Descriptor","Desc",t);if(!this.IsAccessorDescriptor(t)&&!this.IsDataDescriptor(t)){return true}return false},FromPropertyDescriptor:function e(t){if(typeof t==="undefined"){return t}s(this,"Property Descriptor","Desc",t);if(this.IsDataDescriptor(t)){return{value:t["[[Value]]"],writable:!!t["[[Writable]]"],enumerable:!!t["[[Enumerable]]"],configurable:!!t["[[Configurable]]"]}}else if(this.IsAccessorDescriptor(t)){return{get:t["[[Get]]"],set:t["[[Set]]"],enumerable:!!t["[[Enumerable]]"],configurable:!!t["[[Configurable]]"]}}else{throw new i("FromPropertyDescriptor must be called with a fully populated Property Descriptor")}},ToPropertyDescriptor:function e(t){if(this.Type(t)!=="Object"){throw new i("ToPropertyDescriptor requires an object")}var r={};if(h(t,"enumerable")){r["[[Enumerable]]"]=this.ToBoolean(t.enumerable)}if(h(t,"configurable")){r["[[Configurable]]"]=this.ToBoolean(t.configurable)}if(h(t,"value")){r["[[Value]]"]=t.value}if(h(t,"writable")){r["[[Writable]]"]=this.ToBoolean(t.writable)}if(h(t,"get")){var n=t.get;if(typeof n!=="undefined"&&!this.IsCallable(n)){throw new TypeError("getter must be a function")}r["[[Get]]"]=n}if(h(t,"set")){var a=t.set;if(typeof a!=="undefined"&&!this.IsCallable(a)){throw new i("setter must be a function")}r["[[Set]]"]=a}if((h(r,"[[Get]]")||h(r,"[[Set]]"))&&(h(r,"[[Value]]")||h(r,"[[Writable]]"))){throw new i("Invalid property descriptor. Cannot both specify accessors and a value or writable attribute")}return r}};e.exports=v},"0f7c":function(e,t,r){"use strict";var n=r("688e");e.exports=Function.prototype.bind||n},"1b7f":function(e,t,r){"use strict";var n=r("562e");var a="​";e.exports=function e(){if(String.prototype.trim&&a.trim()===a){return String.prototype.trim}return n}},"1bf1":function(e,t,r){var n=r("320c");e.exports=function e(t){t=t||{};var r=typeof t.opacity==="number"?t.opacity:1;var a=typeof t.alphaTest==="number"?t.alphaTest:1e-4;var i=t.precision||"highp";var o=t.color;var s=t.map;var c=typeof t.negate==="boolean"?t.negate:true;delete t.map;delete t.color;delete t.precision;delete t.opacity;delete t.negate;return n({uniforms:{opacity:{type:"f",value:r},map:{type:"t",value:s||new THREE.Texture},color:{type:"c",value:new THREE.Color(o)}},vertexShader:["attribute vec2 uv;","attribute vec4 position;","uniform mat4 projectionMatrix;","uniform mat4 modelViewMatrix;","varying vec2 vUv;","void main() {","vUv = uv;","gl_Position = projectionMatrix * modelViewMatrix * position;","}"].join("\n"),fragmentShader:["#ifdef GL_OES_standard_derivatives","#extension GL_OES_standard_derivatives : enable","#endif","precision "+i+" float;","uniform float opacity;","uniform vec3 color;","uniform sampler2D map;","varying vec2 vUv;","float median(float r, float g, float b) {","  return max(min(r, g), min(max(r, g), b));","}","void main() {","  vec3 sample = "+(c?"1.0 - ":"")+"texture2D(map, vUv).rgb;","  float sigDist = median(sample.r, sample.g, sample.b) - 0.5;","  float alpha = clamp(sigDist/fwidth(sigDist) + 0.5, 0.0, 1.0);","  gl_FragColor = vec4(color.xyz, alpha * opacity);",a===0?"":"  if (gl_FragColor.a < "+a+") discard;","}"].join("\n")},t)}},"1c33":function(e,t){const r=[[-1,-1],[+1,-1],[-1,+1],[-1,+1],[+1,-1],[+1,+1]];const n=[[0,0],[1,0],[0,1],[0,1],[1,0],[1,1]];const a=[[0,1,2],[3,4,5]];const i=`\n  precision mediump float;\n  attribute vec2 a_position;\n  attribute vec2 a_uv;\n\n  uniform float u_clip_y;\n\n  varying vec2 v_uv;\n  \n  void main() {\n    v_uv = a_uv;\n    gl_Position = vec4(a_position * vec2(1,u_clip_y), 0, 1);\n  }\n`;const o=`\n  precision mediump float;\n  varying vec2 v_uv;\n  uniform sampler2D u_tex;\n  void main () {\n    gl_FragColor = texture2D(u_tex,v_uv);\n  }\n`;const s=`\n  precision mediump float;\n  varying vec2 v_uv;\n  void main () {\n    gl_FragColor = vec4(v_uv,0,1);\n  }\n`;const c=`\n  precision mediump float;\n  attribute vec2 a_position;\n\n  uniform float u_clip_y;\n\n  varying vec2 v_uv;\n  \n  void main() {\n    gl_Position = vec4(a_position * vec2(1,u_clip_y), 0, 1);\n    v_uv = gl_Position.xy;\n  }\n`;const l=`\n  precision mediump float;\n  varying vec2 v_uv;\n  void main () {\n    gl_FragColor = vec4(v_uv,0,1);\n  }\n`;const u=`\ndata:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAA\nBACAIAAAAlC+aJAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQ\nUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAEbSURBVGhD7dhRDsIgEI\nRhjubNPHqlHUTAdjfRWRKa+UIirQnd376Z0vZZG1vQsfvB76WAa3\nEn3yug3GHD0HX6gIZCAaYaEGdSQM2g9yjApADfpIBhTzQvIIgCTA\nrwKcCkAJ8CTArwKcCkAN/56Y/8XAZCwH7AsS6sEDBseisEYF1YIW\nDY9Lq7eW6Mjk29/Bk/YD+vO7Bc/D/rKULAqSbj80tHrOehPC9mjY\n/krhkBeBF4HvZE6CgXRJgeW3wAPYMf0IwO1NO/RL2BhgJMCvApwK\nQAnwJMCvApwKQAnwJMCvApwNQGYE/vmRowbCgUYLpbQHvJMi8gSN\nTpmLsGxGWsH9Aq90gwfW1gwv9zx+qUr0mWD8hCps/uE5DSC/pgVD\nkvIARVAAAAAElFTkSuQmCC`.replace(/\s*/g,"");const f={directions:{uri:u}};e.exports={verts:r,indices:a,uvs:n,shader:{vert:i,frag:o},show:{uvs:{frag:s,vert:i},positions:{frag:l,vert:c}},bitmaps:f}},2057:function(e,t){e.exports=Number.isNaN||function e(t){return t!==t}},"21d0":function(e,t,r){"use strict";var n=Function.prototype.toString;var a=/^\s*class\b/;var i=function e(t){try{var r=n.call(t);return a.test(r)}catch(i){return false}};var o=function e(t){try{if(i(t)){return false}n.call(t);return true}catch(r){return false}};var s=Object.prototype.toString;var c="[object Function]";var l="[object GeneratorFunction]";var u=typeof Symbol==="function"&&typeof Symbol.toStringTag==="symbol";e.exports=function e(t){if(!t){return false}if(typeof t!=="function"&&typeof t!=="object"){return false}if(typeof t==="function"&&!t.prototype){return true}if(u){return o(t)}if(i(t)){return false}var r=s.call(t);return r===c||r===l}},"247b":function(e,t,r){"use strict";r("80dc")("fontsize",function(e){return function t(r){return e(this,"font","size",r)}})},"28e2":function(e,t,r){},"2e69":function(e,t,r){},"2f17":function(e,t,r){"use strict";var n=Object.prototype.toString;var a=r("4de8");var i=r("21d0");var o={"[[DefaultValue]]":function(e){var t;if(arguments.length>1){t=arguments[1]}else{t=n.call(e)==="[object Date]"?String:Number}if(t===String||t===Number){var r=t===String?["toString","valueOf"]:["valueOf","toString"];var o,s;for(s=0;s<r.length;++s){if(i(e[r[s]])){o=e[r[s]]();if(a(o)){return o}}}throw new TypeError("No default value")}throw new TypeError("invalid [[DefaultValue]] hint supplied")}};e.exports=function e(t){if(a(t)){return t}if(arguments.length>1){return o["[[DefaultValue]]"](t,arguments[1])}return o["[[DefaultValue]]"](t)}},"320c":function(e,t,r){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var n=Object.getOwnPropertySymbols;var a=Object.prototype.hasOwnProperty;var i=Object.prototype.propertyIsEnumerable;function o(e){if(e===null||e===undefined){throw new TypeError("Object.assign cannot be called with null or undefined")}return Object(e)}function s(){try{if(!Object.assign){return false}var e=new String("abc");e[5]="de";if(Object.getOwnPropertyNames(e)[0]==="5"){return false}var t={};for(var r=0;r<10;r++){t["_"+String.fromCharCode(r)]=r}var n=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if(n.join("")!=="**********"){return false}var a={};"abcdefghijklmnopqrst".split("").forEach(function(e){a[e]=e});if(Object.keys(Object.assign({},a)).join("")!=="abcdefghijklmnopqrst"){return false}return true}catch(i){return false}}e.exports=s()?Object.assign:function(e,t){var r;var s=o(e);var c;for(var l=1;l<arguments.length;l++){r=Object(arguments[l]);for(var u in r){if(a.call(r,u)){s[u]=r[u]}}if(n){c=n(r);for(var f=0;f<c.length;f++){if(i.call(r,c[f])){s[c[f]]=r[c[f]]}}}}return s}},"37cd":function(e,t,r){var n=r("7831");e.exports=a;function a(e,t,r){if(!e)throw new TypeError("must specify data as first parameter");r=+(r||0)|0;if(Array.isArray(e)&&(e[0]&&typeof e[0][0]==="number")){var a=e[0].length;var i=e.length*a;var o,s,c,l;if(!t||typeof t==="string"){t=new(n(t||"float32"))(i+r)}var u=t.length-r;if(i!==u){throw new Error("source length "+i+" ("+a+"x"+e.length+")"+" does not match destination length "+u)}for(o=0,c=r;o<e.length;o++){for(s=0;s<a;s++){t[c++]=e[o][s]===null?NaN:e[o][s]}}}else{if(!t||typeof t==="string"){var f=n(t||"float32");if(Array.isArray(e)||t==="array"){t=new f(e.length+r);for(o=0,c=r,l=t.length;c<l;c++,o++){t[c]=e[o]===null?NaN:e[o]}}else{if(r===0){t=new f(e)}else{t=new f(e.length+r);t.set(e,r)}}}else{t.set(e,r)}}return t}},"42b6":function(e,t){var r=/\n/;var n="\n";var a=/\s/;e.exports=function(t,r){var n=e.exports.lines(t,r);return n.map(function(e){return t.substring(e.start,e.end)}).join("\n")};e.exports.lines=function e(t,r){r=r||{};if(r.width===0&&r.mode!=="nowrap")return[];t=t||"";var n=typeof r.width==="number"?r.width:Number.MAX_VALUE;var a=Math.max(0,r.start||0);var i=typeof r.end==="number"?r.end:t.length;var o=r.mode;var u=r.measure||l;if(o==="pre")return s(u,t,a,i,n);else return c(u,t,a,i,n,o)};function i(e,t,r,n){var a=e.indexOf(t,r);if(a===-1||a>n)return n;return a}function o(e){return a.test(e)}function s(e,t,n,a,i){var o=[];var s=n;for(var c=n;c<a&&c<t.length;c++){var l=t.charAt(c);var u=r.test(l);if(u||c===a-1){var f=u?c:c+1;var p=e(t,s,f,i);o.push(p);s=c+1}}return o}function c(e,t,r,a,s,c){var l=[];var u=s;if(c==="nowrap")u=Number.MAX_VALUE;while(r<a&&r<t.length){var f=i(t,n,r,a);while(r<f){if(!o(t.charAt(r)))break;r++}var p=e(t,r,f,u);var d=r+(p.end-p.start);var h=d+n.length;if(d<f){while(d>r){if(o(t.charAt(d)))break;d--}if(d===r){if(h>r+n.length)h--;d=h}else{h=d;while(d>r){if(!o(t.charAt(d-n.length)))break;d--}}}if(d>=r){var v=e(t,r,d,u);l.push(v)}r=h}return l}function l(e,t,r,n){var a=Math.min(n,r-t);return{start:t,end:t+a}}},"486c":function(e,t){e.exports=function e(){if(typeof self.DOMParser!=="undefined"){return function(e){var t=new self.DOMParser;return t.parseFromString(e,"application/xml")}}if(typeof self.ActiveXObject!=="undefined"&&new self.ActiveXObject("Microsoft.XMLDOM")){return function(e){var t=new self.ActiveXObject("Microsoft.XMLDOM");t.async="false";t.loadXML(e);return t}}return function(e){var t=document.createElement("div");t.innerHTML=e;return t}}()},"4de8":function(e,t){e.exports=function e(t){return t===null||typeof t!=="function"&&typeof t!=="object"}},"562e":function(e,t,r){"use strict";var n=r("0f7c");var a=r("0f01");var i=n.call(Function.call,String.prototype.replace);var o=/^[\x09\x0A\x0B\x0C\x0D\x20\xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF]+/;var s=/[\x09\x0A\x0B\x0C\x0D\x20\xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF]+$/;e.exports=function e(){var t=a.ToString(a.CheckObjectCoercible(this));return i(i(t,o,""),s,"")}},5882:function(e,t){var r=Object.prototype.toString;e.exports=n;function n(e){return e.BYTES_PER_ELEMENT&&r.call(e.buffer)==="[object ArrayBuffer]"||Array.isArray(e)}},5975:function(e,t){e.exports=function e(t){return t>=0?1:-1}},"5b25":function(e,t,r){var n=r("37cd");var a=false;e.exports.attr=o;e.exports.index=i;function i(e,t,r,n){if(typeof r!=="number")r=1;if(typeof n!=="string")n="uint16";var a=!e.index&&typeof e.setIndex!=="function";var i=a?e.getAttribute("index"):e.index;var o=s(i,t,r,n);if(o){if(a)e.addAttribute("index",o);else e.index=o}}function o(e,t,r,n,a){if(typeof n!=="number")n=3;if(typeof a!=="string")a="float32";if(Array.isArray(r)&&Array.isArray(r[0])&&r[0].length!==n){throw new Error("Nested vertex array has unexpected size; expected "+n+" but found "+r[0].length)}var i=e.getAttribute(t);var o=s(i,r,n,a);if(o){e.addAttribute(t,o)}}function s(e,t,r,i){t=t||[];if(!e||c(e,t,r)){t=n(t,i);var o=e&&typeof e.setArray!=="function";if(!e||o){if(o&&!a){a=true;console.warn(["A WebGL buffer is being updated with a new size or itemSize, ","however this version of ThreeJS only supports fixed-size buffers.","\nThe old buffer may still be kept in memory.\n","To avoid memory leaks, it is recommended that you dispose ","your geometries and create new ones, or update to ThreeJS r82 or newer.\n","See here for discussion:\n","https://github.com/mrdoob/three.js/pull/9631"].join(""))}e=new THREE.BufferAttribute(t,r)}e.itemSize=r;e.needsUpdate=true;if(typeof e.setArray==="function"){e.setArray(t)}return e}else{n(t,e.array);e.needsUpdate=true;return null}}function c(e,t,r){if(e.itemSize!==r)return true;if(!e.array)return true;var n=e.array.length;if(Array.isArray(t)&&Array.isArray(t[0])){return n!==t.length*r}else{return n!==t.length}return false}},6418:function(e,t){var r=2;var n={min:[0,0],max:[0,0]};function a(e){var t=e.length/r;n.min[0]=e[0];n.min[1]=e[1];n.max[0]=e[0];n.max[1]=e[1];for(var a=0;a<t;a++){var i=e[a*r+0];var o=e[a*r+1];n.min[0]=Math.min(i,n.min[0]);n.min[1]=Math.min(o,n.min[1]);n.max[0]=Math.max(i,n.max[0]);n.max[1]=Math.max(o,n.max[1])}}e.exports.computeBox=function(e,t){a(e);t.min.set(n.min[0],n.min[1],0);t.max.set(n.max[0],n.max[1],0)};e.exports.computeSphere=function(e,t){a(e);var r=n.min[0];var i=n.min[1];var o=n.max[0];var s=n.max[1];var c=o-r;var l=s-i;var u=Math.sqrt(c*c+l*l);t.center.set(r+c/2,i+l/2,0);t.radius=u/2}},6444:function(e,t,r){var n=r("ca9f"),a=r("d024"),i=function(e){return Object.prototype.toString.call(e)==="[object Array]"};e.exports=function(e){if(!e)return{};var t={};a(n(e).split("\n"),function(e){var r=e.indexOf(":"),a=n(e.slice(0,r)).toLowerCase(),o=n(e.slice(r+1));if(typeof t[a]==="undefined"){t[a]=o}else if(i(t[a])){t[a].push(o)}else{t[a]=[t[a],o]}});return t}},"688e":function(e,t,r){"use strict";var n="Function.prototype.bind called on incompatible ";var a=Array.prototype.slice;var i=Object.prototype.toString;var o="[object Function]";e.exports=function e(t){var r=this;if(typeof r!=="function"||i.call(r)!==o){throw new TypeError(n+r)}var s=a.call(arguments,1);var c;var l=function(){if(this instanceof c){var e=r.apply(this,s.concat(a.call(arguments)));if(Object(e)===e){return e}return this}else{return r.apply(t,s.concat(a.call(arguments)))}};var u=Math.max(0,r.length-s.length);var f=[];for(var p=0;p<u;p++){f.push("$"+p)}c=Function("binder","return function ("+f.join(",")+"){ return binder.apply(this,arguments); }")(l);if(r.prototype){var d=function e(){};d.prototype=r.prototype;c.prototype=new d;d.prototype=null}return c}},"68ee":function(e,t,r){"use strict";var n=r("8895");var a=r.n(n);var i=a.a},"778a":function(e,t,r){},7831:function(e,t){e.exports=function(e){switch(e){case"int8":return Int8Array;case"int16":return Int16Array;case"int32":return Int32Array;case"uint8":return Uint8Array;case"uint16":return Uint16Array;case"uint32":return Uint32Array;case"float32":return Float32Array;case"float64":return Float64Array;case"array":return Array;case"uint8_clamped":return Uint8ClampedArray}}},"78ab":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{ref:"rendererContainer",staticClass:"flex row preview-wrapper"},[this.$route.name==="configurator"?r("TylkoUsps",{attrs:{isVisible:e.visibleUspsText}}):e._e(),this.$route.name==="configurator"?r("TylkoMenu"):e._e(),r("div",{staticClass:"renderer-wrapper",on:{mouseover:e.rendererMouseOver,mouseleave:e.rendererMouseLeave}},[!e.loaderHidden?r("q-spinner",{staticClass:"spinner",attrs:{color:"blue",size:"50px"}}):e._e(),r("div",{ref:"rendererWapper",staticClass:"production-renderer-canvas load-hidden"}),r("div",{staticClass:"mapped"},e._l(e.mappedObjects,function(t,n){return r("div",{staticClass:"mapped-container"},[r("div",{staticClass:"cont",style:"position: absolute; top: "+t.y+"px; left: "+t.x+"px;}"},[r("div",{class:[n+1==e.$refs.configuration.$refs.previewSelect.selectedTab?"comp-selected":""],staticStyle:{"z-index":"99"}},[r("q-btn",{class:[e.hoverElement==n?"hoverState":"","tylko-button"],style:e.$refs.configuration.$refs.previewSelect.selectedTab!=0&&n+1!=e.$refs.configuration.$refs.previewSelect.selectedTab?"opacity: 0":"",attrs:{round:"round",dense:"dense",color:"white",size:"md",icon:"tune"},nativeOn:{mouseleave:function(t){return e.selectOnLeave(t)},mouseover:function(t){return e.selectOnEnter(n)},mousedown:function(t){return e.select(n)}}},[r("span",{staticClass:"adjust"},[e._v(e._s(n+1==e.$refs.configuration.$refs.previewSelect.selectedTab||e.hoverElement==n?"Adjust":""))])])],1)]),e._l(e.compartmentsHeights,function(t){return r("div",{staticClass:"compartments-heights-container"},[r("div",{staticClass:"compartment compartment-height",style:"position: absolute; transform: translate("+t.point.x+"px, "+t.point.y+"px)"},[e._v(e._s(Math.floor(t.value/10)))])])}),e._l(e.compartmentsWidths,function(t){return r("div",{staticClass:"compartments-widths-container"},[r("div",{staticClass:"compartment compartment-width",style:"position: absolute; transform: translate("+t.point.x+"px, "+t.point.y+"px)"},[e._v(e._s(Math.floor(t.value/10)))])])})],2)}),0)],1),r("div",{staticStyle:{position:"relative",height:"100vh",width:"30%"}},[r("TylkoPreviewControls",{attrs:{showAllCompartmentsButton:e.showAllCompartmentsButton}}),r("TylkoPreviewConfiguration",{ref:"configuration",attrs:{type:e.decoderType,element:e.element,grabButtons:e.grabButtons,objectLine:e.objectLine,choosenColor:e.choosenColor,decoderWidth:e.decoderWidth,decoderHeight:e.decoderHeight,decoderOutput:e.decoderOutput,distortion:e.distortion,density:e.density,depth:e.depth,plinth:e.plinth,mappedObjects:e.mappedObjects,price:e.price,locked:e.locked,previousStateAvailble:e.previousStateAvailble,nextStateAvailble:e.nextStateAvailble,parametersStatesStatus:e.parametersStatesStatus}})],1)],1)};var a=[];var i=r("448a");var o=r.n(i);var s=r("5e86");var c=r("b544");var l=r("1df5");var u=r("9523");var f=r.n(u);var p=r("3156");var d=r.n(p);var h=r("e1c2");var v=r("723b");var m=r("9af0");var g=r("d6b6");var y=r("278c");var b=r.n(y);var w=r("5a51");var S=r("359c");var x=r("9ec3");var _=r.n(x);var C=r("18a5");var P=r("ce62");var E=r("85a6");var A=r("1197");var T=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("q-scroll-area",{staticClass:"configuration-wrapper",class:{badania:e.isConfigurator},staticStyle:{}},[e.isConfigurator?r("span",{staticClass:"title"},[e._v("Type01 Sideboard")]):e._e(),r("q-card",{staticClass:"no-shadow bradius"},[r("q-card-title",{staticClass:"card-title"},[!e.isConfigurator?r("span",[e._v(e._s(e.type)+" - "+e._s(e.element?e.element.name:"")+" | "+e._s(e.objectLine))]):e._e(),r("span",{staticClass:"text-weight-bold",staticStyle:{color:"black","font-size":"24px","line-height":"24px"}},[e._v(e._s(e.price)+"€")]),!e.isConfigurator?r("q-btn-group",[e.$route.params.type==="mesh"&&e.isConfigurator?r("q-btn",{staticClass:"q-pl-md",attrs:{label:"RESET",size:"sm"},on:{click:e.resetToInitialState}}):e._e(),e.$route.params.type==="mesh"&&!e.isConfigurator?r("q-btn",{staticClass:"q-pl-md",attrs:{label:"SAVE STATE",color:"positive",size:"sm"},on:{click:e.saveState}}):e._e(),e.$route.params.type==="mesh"?r("q-btn",{staticClass:"q-pl-md arrow-btn",attrs:{label:this.isConfigurator?"":"PREVIOUS STATE",size:"md",disable:!this.previousStateAvailble,icon:this.isConfigurator?"undo":""},on:{click:e.undo}}):e._e(),e.$route.params.type==="mesh"?r("q-btn",{staticClass:"q-pl-md arrow-btn",attrs:{label:this.isConfigurator?"":"NEXT STATE",size:"md",disable:!this.nextStateAvailble,icon:this.isConfigurator?"redo":""},on:{click:e.redo}}):e._e()],1):e._e(),e.$route.params.type==="mesh"&&!e.isConfigurator?r("div",{staticClass:"row"},[r("q-slider",{attrs:{markers:"markers",snap:"snap",step:1,min:0,max:this.parametersStatesStatus[1]},on:{input:function(t){return e.setState(t)}},model:{value:e.currentParametersHistoryState,callback:function(t){e.currentParametersHistoryState=t},expression:"currentParametersHistoryState"}})],1):e._e()],1),r("div",{staticClass:"separator"}),!e.isConfigurator?r("q-card-main",{staticClass:"row justify-between"},[r("q-toggle",{staticClass:"col-8",attrs:{label:"Camera lock"},on:{input:function(t){return e.updateParam("locked",t)}},model:{value:e.choosenLocked,callback:function(t){e.choosenLocked=t},expression:"choosenLocked"}}),!e.isConfigurator&&e.$route.params.type==="mesh"&&e.element?r("router-link",{attrs:{tag:"a",to:"/meshes/"+e.element.collection+"/"+e.element.id}},[r("q-btn",{staticClass:"link-button q-pl-md",attrs:{rounded:"rounded","icon-right":"settings",size:"sm",label:"EDIT"}})],1):e._e(),!e.isConfigurator&&e.$route.params.type==="component"&&e.element?r("router-link",{attrs:{tag:"a",to:"/components/"+e.element.collection+"/"+e.element.parent+"/"+e.element.id}},[r("q-btn",{attrs:{rounded:"rounded","icon-right":"settings",size:"sm",label:"EDIT",color:"blue"}})],1):e._e()],1):e._e(),r("q-card-main",{staticClass:"settings-wrapper"},[r("div",{staticClass:"row items-center"},[r("q-field",{staticClass:"col-md-2",attrs:{label:"Color"}}),r("TylkoPreviewColors",{staticClass:"col-md-10 justify-end",attrs:{objectLine:e.objectLine,choosenColor:e.choosenColor}})],1),r("div",{staticClass:"row items-center"},[e.widthRange.length?r("q-field",{staticClass:"col-md-2",attrs:{label:"Width"}}):e._e(),r("q-slider",{staticClass:"col-md-10",attrs:{min:e.widthRange[0],max:e.widthRange[1],label:"label","label-always":true,"label-value":+e.choosenWidth+" cm"},on:{input:function(t){return e.updateConfigurator("decoderWidth",t,true)}},model:{value:e.choosenWidth,callback:function(t){e.choosenWidth=t},expression:"choosenWidth"}})],1),r("div",{staticClass:"row items-center"},[e.heightRange.length&&e.type==="mesh"?r("q-field",{staticClass:"col-md-2",attrs:{label:"Height"}}):e._e(),r("q-slider",{staticClass:"col-md-10",attrs:{min:0,max:e.heightRange.length-1,step:1,label:"label",markers:"markers","label-always":true,"label-value":Math.round((+e.choosenHeight+e.additional_height[0]+e.additional_height[1])/10)+" cm"},on:{input:function(t){e.computeHeight(t);e.lazyAutoSaveState();e.showRendererElements()}},model:{value:e.heightIndex,callback:function(t){e.heightIndex=t},expression:"heightIndex"}})],1),e.type==="mesh"&&["gradient","ratio"].includes(e.distortionMode)?r("div",{staticClass:"row items-center"},[r("q-field",{staticClass:"col-md-2",attrs:{label:"Motion"}}),r("q-slider",{staticClass:"col-md-10",attrs:{min:0,max:100,"label-always":true,"label-value":e.choosenDistortion+" %"},on:{input:function(t){return e.updateConfigurator("distortion",t,true)}},model:{value:e.choosenDistortion,callback:function(t){e.choosenDistortion=t},expression:"choosenDistortion"}})],1):e._e(),e.densityMode==="setup_range_slider"?r("div",{staticClass:"row items-center"},[r("q-field",{staticClass:"col-md-2",attrs:{label:"Density"}}),r("q-slider",{staticClass:"col-md-10",attrs:{min:0,max:100,"label-always":true,"label-value":e.choosenDensity+" %"},on:{input:function(t){return e.updateConfigurator("density",t,true)}},model:{value:e.choosenDensity,callback:function(t){e.choosenDensity=t},expression:"choosenDensity"}})],1):e._e(),e.densityMode==="setup_range_stepper"&&e.densityOptions.length!==0?r("q-field",{staticClass:"col-md-10",attrs:{label:"Columns"}},[r("div",{staticClass:"row items-center",staticStyle:{"padding-top":"14px"}},[r("q-btn",{staticClass:"button-badania small",class:{disable:e.densityOptions[0][0]===e.choosenDensity},attrs:{label:"–"},on:{click:function(t){return e.updateConfigurator("density",e.densityOptions[e.densityOptions.findIndex(function(t){return t[0]===e.choosenDensity})-1][0],false)}}}),e._v(e._s(e.densityOptions.find(function(t){return t[0]===e.choosenDensity})[1])+"    "),r("q-btn",{staticClass:"button-badania small",class:{disable:e.densityOptions[e.densityOptions.length-1][0]===e.choosenDensity},attrs:{label:"+"},on:{click:function(t){return e.updateConfigurator("density",e.densityOptions[e.densityOptions.findIndex(function(t){return t[0]===e.choosenDensity})+1][0],false)}}})],1)]):e._e(),r("div",{staticClass:"row items-center"},[r("q-field",{staticClass:"component-select col-md-10",staticStyle:{float:"left"},attrs:{label:"Depth"}},[r("q-btn",{staticClass:"button-badania",class:{active:e.choosenDepth==320},attrs:{label:"32cm","no-ripple":"no-ripple"},on:{click:function(t){return e.updateConfigurator("depth",320,false)}}}),r("q-btn",{staticClass:"button-badania",class:{active:e.choosenDepth==400},attrs:{label:"40cm","no-ripple":"no-ripple"},on:{click:function(t){return e.updateConfigurator("depth",400,false)}}})],1)],1),r("div",{staticClass:"row items-center"},[r("q-field",{staticClass:"component-select col-md-10",attrs:{label:"Base"}},[r("q-btn",{staticClass:"button-badania",class:{active:!e.choosenPlinth},attrs:{label:"Feet","no-ripple":"no-ripple"},on:{click:function(t){return e.updateConfigurator("plinth",false,false)}}}),r("q-btn",{staticClass:"button-badania",class:{active:e.choosenPlinth},attrs:{label:"Plinth","no-ripple":"no-ripple"},on:{click:function(t){return e.updateConfigurator("plinth",true,false)}}})],1)],1)],1),r("div",{staticClass:"separator"}),e.isConfigurator?r("q-card-main",[r("TylkoAddToCart")],1):e._e(),e.type==="mesh"?r("q-card-main",{class:[e.isConfigurator?"configurator-mode-hidden":""]},[r("TylkoPreviewComponentSelect",{ref:"previewSelect",attrs:{grabButtons:e.grabButtons,setupID:e.setupID,components:e.components,height:e.choosenHeight,preview_mode:e.$route.name,thumbnails:e.thumbnails,distortionMode:e.distortionMode,mappedObjects:e.mappedObjects,lines:e.lines}})],1):e._e()],1)],1)};var k=[];var O=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"colors-wrapper"},e._l(e.colors,function(t){return t.type===e.objectLine?r("button",{class:"color-btn "+(t.value===e.choosenColor?"active":""),on:{click:function(){return e.changeColor(t.value)}}},[r("img",{attrs:{src:t.imgPath,alt:t.alt}})]):e._e()}),0)};var $=[];var j=window.location.href.indexOf("localhost")>-1?"":"/r_static/webdesigner";var I={name:"TylkoPreviewColors",props:["objectLine","choosenColor","updateRenderer"],methods:{changeColor:function e(t){C["a"].application.bus.$emit("updateRenderer","choosenColor",t)}},data:function e(){return{colors:[{type:"type_01",imgPath:j+"/statics/T01-1.svg",value:"0:0",alt:"white"},{type:"type_01",imgPath:j+"/statics/T01-2.svg",value:"0:3",alt:"grey"},{type:"type_01",imgPath:j+"/statics/T01-3.svg",value:"0:1",alt:"black"},{type:"type_01",imgPath:j+"/statics/T01-4.png",value:"0:5",alt:"fornir"},{type:"type_01",imgPath:j+"/statics/T01-5.svg",value:"0:4",alt:"abuergine"},{type:"type_02",imgPath:j+"/statics/T02-1.svg",value:"1:0",alt:"white"},{type:"type_02",imgPath:j+"/statics/T02-2.svg",value:"1:2",alt:"color3"},{type:"type_02",imgPath:j+"/statics/T02-3.svg",value:"1:1",alt:"color2"},{type:"type_02",imgPath:j+"/statics/T02-4.svg",value:"1:3",alt:"color4"},{type:"type_02",imgPath:j+"/statics/T02-5.svg",value:"1:4",alt:"color5"}]}}};var M=I;var R=r("895e");var D=r("2877");var L=Object(D["a"])(M,O,$,false,null,null,null);var z=L.exports;var F=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"component-select"},[e._l(e.configuration,function(t,n){return!e.isConfigurator?r("q-field",[e._v(e._s(n)+" - Chn:  "+e._s(t.channel_id)+" - Tabl: "+e._s(t.table_id)+" - Seri "+e._s(t.series_id)+" - Comp "+e._s(t.component_id))]):e._e()}),r("q-card-separator"),r("q-tabs",{attrs:{color:"tertiary",inverted:"inverted"},model:{value:e.selectedTab,callback:function(t){e.selectedTab=t},expression:"selectedTab"}},[r("q-tab",{key:"0",style:e.isConfigurator?"display: none":"",attrs:{slot:"title",name:"0",label:"0"},slot:"title"}),e._l(e.configuration,function(t,n){return r("q-tab",{key:""+(n+1),style:e.isConfigurator?"display: none":"",attrs:{slot:"title",name:""+(n+1),label:""+(n+1)},slot:"title"})})],2),r("q-tab-panels",{staticClass:"tab-style tab-style",model:{value:e.selectedTab,callback:function(t){e.selectedTab=t},expression:"selectedTab"}},[r("q-tab-panel",{style:e.isConfigurator?"display: none":"",attrs:{name:"0"}},[!e.isConfigurator?r("q-tree",{attrs:{nodes:e.setupsTreeNodes,"node-key":"label","default-expand-all":"default-expand-all"}}):e._e()],1),e._l(e.configuration,function(t,n){return r("q-tab-panel",{class:["component-select-wrapper",e.isConfigurator?"configurator-style":""],style:e.isConfigurator?"position: fixed; top: "+(e.mappedObjects[n]?e.mappedObjects[n].y:0)+"px; left: "+(e.mappedObjects[n]?e.mappedObjects[n].x:0)+"px;}":"",attrs:{name:""+(n+1)},nativeOn:{mouseleave:function(t){return function(){return e.triggerPrevSelected()}(t)},mouseover:function(t){return function(){return e.clearPrevSelectedTimeout()}(t)}}},[t.options&&e.optionsError(t)?r("div",{staticClass:"div tab-style"},[t.options[0]?r("div",{staticClass:"series"},e._l(t.options,function(n,a){return r("div",{key:e.hash(""+n.value),class:["miniature-wrapper",t.series_id===n.value?" active":""],on:{click:function(){t.series_id=n.value;e.dispatchToConfiguration();e.autoSaveState()}}},[n.thumbnail?r("TylkoMiniature",{staticClass:"mini",attrs:{"bg-color":"#ffffff",geo:n.thumbnail}}):e._e()],1)}),0):e._e(),!t.options[0].value?r("q-field",{staticClass:"select",attrs:{label:"m_config_id ("+t.m_config_id+")"}},[r("p",[e._v(e._s(t.options[0].label)+"                    ")])]):e._e(),e.distortionMode==="local_x"||e.distortionMode==="local_all"?r("q-field",{attrs:{label:"Width"}},[r("q-slider",{attrs:{min:-20,max:20,"label-always":true,"label-value":t.width+" cm"},on:{input:function(){e.dispatchToConfiguration();e.lazyAutoSaveState()}},nativeOn:{mouseup:function(t){return function(){e.dispatchToConfiguration(true)}(t)}},model:{value:t.distortion,callback:function(r){e.$set(t,"distortion",r)},expression:"config.distortion"}})],1):e._e(),r("div",{staticStyle:{display:"flex"}},[t.door_flippable&&t.options[0].value?r("div",{staticClass:"t-field"},[r("q-checkbox",{staticStyle:{"margin-top":"6px"},attrs:{"true-value":"right","false-value":"left"},on:{input:function(){e.dispatchToConfiguration(true);e.autoSaveState()}},model:{value:t.door_flip,callback:function(r){e.$set(t,"door_flip",r)},expression:"config.door_flip"}}),r("p",[e._v("Door flip")])],1):e._e(),t.cables_available&&t.options[0].value?r("div",{staticClass:"t-field",class:{push:t.door_flippable&&t.options[0].value}},[r("q-checkbox",{staticStyle:{"margin-top":"5px"},on:{input:function(){e.dispatchToConfiguration(true);e.autoSaveState()}},model:{value:t.cables,callback:function(r){e.$set(t,"cables",r)},expression:"config.cables"}}),r("p",[e._v("Opening for cables")])],1):e._e()])],1):e._e(),e.preview_mode!=="configurator"?r("div",{staticClass:"div"},[r("div",{staticClass:"row"},[e._v(e._s(t.component_id)+" - komponent")]),r("div",{staticClass:"row"},[e._v(e._s(t.series_id)+" - seria")]),r("div",{staticClass:"row"},[e._v(e._s(t.table_id)+" - tablica")]),r("div",{staticClass:"row"},[e._v(e._s(t.channel_id)+" - kanał")]),r("div",{staticClass:"row"},[e._v(e._s(t.m_config_id)+" - m_config_id")]),r("div",{staticClass:"row"},[e._v(e._s(t.cables)+" - cables")]),r("div",{staticClass:"row"},[e._v(e._s(t.door_flip)+" - door flip")]),e.distortionMode!="OFF"?r("div",{staticClass:"row"},[e._v(e._s(t.distortion)+" - distortion")]):e._e()]):e._e()])})],2),e.configuration[e.selectedTab-1]&&e.distortionMode==="edge"?r("div",[e.configuration[e.selectedTab-1].line_left?r("div",{style:"transform: translate("+e.mapEdges(e.lines[e.configuration[e.selectedTab-1].line_left]).x+"px, "+(e.mapEdges(e.lines[e.configuration[e.selectedTab-1].line_left]).y-70)+"px)",attrs:{id:"button-grab-left"}},[r("q-btn",{staticClass:"tylko-button",attrs:{round:"round",dense:"dense",color:"white",size:"md",icon:e.lines[e.configuration[e.selectedTab-1].line_left].value===e.lines[e.configuration[e.selectedTab-1].line_left].v_min?"keyboard_arrow_right":e.lines[e.configuration[e.selectedTab-1].line_left].value===e.lines[e.configuration[e.selectedTab-1].line_left].v_max?"keyboard_arrow_left":"code"},nativeOn:{mousedown:function(t){return function(t){return e.startDrag(e.configuration[e.selectedTab-1].line_left,t)}(t)}}})],1):e._e(),e.configuration[e.selectedTab-1].line_right?r("div",{style:"transform: translate("+e.mapEdges(e.lines[e.configuration[e.selectedTab-1].line_right]).x+"px, "+(e.mapEdges(e.lines[e.configuration[e.selectedTab-1].line_right]).y-70)+"px)",attrs:{id:"button-grab-right"}},[r("q-btn",{staticClass:"tylko-button",attrs:{round:"round",dense:"dense",color:"white",size:"md",icon:e.lines[e.configuration[e.selectedTab-1].line_right].value===e.lines[e.configuration[e.selectedTab-1].line_right].v_min?"keyboard_arrow_right":e.lines[e.configuration[e.selectedTab-1].line_right].value===e.lines[e.configuration[e.selectedTab-1].line_right].v_max?"keyboard_arrow_left":"code"},nativeOn:{mousedown:function(t){return function(t){return e.startDrag(e.configuration[e.selectedTab-1].line_right,t)}(t)}}})],1):e._e()]):e._e()],2)};var B=[];var H=r("7813");var q=r("4082");var U=r.n(q);var N=r("0de4");var V=r("9b8e");var G=r("7341");var W=r("fb2b");var X=r("7037");var Y=r.n(X);var J=r("0fe3");var Z=r.n(J);var K=r("e67d");var Q=r("07c3");var ee=r("202d");function te(e){return e&&Y()(e)==="object"&&e.constructor===Object&&Object.keys(e).length!==0}var re={name:"TylkoPreviewComponentSelect",props:["setupID","components","height","preview_mode","thumbnails","distortionMode","mappedObjects","lines","grabButtons"],components:{TylkoMiniature:Q["a"]},methods:{startDrag:function e(t,r){if(!this.dragging){this.dragging={id:t,startX:r.clientX,startY:r.clientY,state:this.lines[t].value,startOffset:this.mapEdges({x:r.clientX,y:r.clientY,z:0},true)}}},drag:function e(t){if(this.dragging){var r=this.lines[this.dragging.id];var n=this.lines[this.dragging.id];var a=this.mapEdges({x:t.clientX,y:t.clientY,z:0},true);var i=-(this.dragging.startOffset.x-a.x);i=this.dragging.state+i;i=Math.max(r.v_min,Math.min(i,r.v_max));console.log("OFFFSET",this.dragging.startOffset.x-a.x,this.dragging);this.lines[this.dragging.id].value=i;this.dispatchToConfiguration();this.lazyAutoSaveState()}},stopDrag:function e(){this.dragging=null},triggerPrevSelected:function e(){C["a"].application.bus.$emit("triggerPrevSelected")},clearPrevSelectedTimeout:function e(){C["a"].application.bus.$emit("clearPrevSelectedTimeout")},hash:function e(t){if(!this.H)this.H=Z.a.h32(43981);return this.H.update(t).digest().toString(16)},uuiid:function e(){return ee()},autoSaveState:function e(){if(this.isConfigurator)C["a"].application.bus.$emit("savePreviewParametersState",true)},lazyAutoSaveState:_.a.debounce(function(){this.autoSaveState()},300),setupsTree:function e(){this.setupsTreeNodes=this.mapObject(this.objToDispatch)},mapObject:function e(t){var r=this;if(te(t)){var n=[];Object.keys(t).forEach(function(e){if(te(t[e])){n=[].concat(o()(n),[{label:e,children:r.mapObject(t[e])}])}else{n=[].concat(o()(n),[{label:e+": "+t[e]}])}});return n}return[]},optionsError:function e(t){if(_.a.has(t,"options")){if(t.options.some(function(e){return!!e===false}))return false;return true}else{return false}},dispatchToConfiguration:function e(t){var r=this;var n={};var a={};var i={};this.configuration.forEach(function(e){var t=e.tab,i=e.m_config_id,o=e.door_flip,s=e.series_id,c=e.channel_id,l=e.distortion,u=e.cables;l=l?l:{};n=d()({},n,f()({},i,{door_flip:o,series_id:s,cables:u,distortion:l}));if(r.selectedTab===t){a=d()({},a,f()({},c,{door_flip:o,series_id:s,cables:u,distortion:l}))}});var o=Object.entries(this.lines);for(var s=0;s<o.length;s++){var c=b()(o[s],2),l=c[0],u=c[1];i[l]=u.value}this.objToDispatch={setups:d()({},this.objToDispatch.setups,f()({},this.setupID,n)),channels:d()({},this.objToDispatch.channels,a),lines:d()({},this.objToDispatch.lines,i)};if(t){this.generateThumbnails(this.selectedTab)}else{this.thumbsForChannel(null);C["a"].application.bus.$emit("disableThumbnails")}C["a"].application.bus.$emit("dispatchConfiguratorCustomParams",this.objToDispatch);this.setupsTree()},thumbsForChannel:function e(t){C["a"].application.bus.$emit("thumbsForChannel",t)},lazyGenerateThumbanils:_.a.debounce(function(){this.generateThumbnails(this.selectedTab)},200),generateThumbnails:function e(t){if(t!=="0"){C["a"].application.bus.$emit("generateThumbnails")}C["a"].application.bus.$emit("changeElements",t!=="0"?"all_opened":"all");C["a"].application.bus.$emit("changeSetupTabIndex",t)},processGeometry:function e(t){var r=this;if(this.thumbnails){this.thumbnailsStore=this.thumbnails}this.configuration=this.configuration.map(function(e,t){var n=e.m_config_id,a=e.table_id,i=U()(e,["m_config_id","table_id"]);return d()({},i,{m_config_id:n,table_id:a,tab:""+(t+1),options:r.thumbnailsStore&&r.thumbnailsStore.hasOwnProperty(n)?r.thumbnailsStore[n].map(function(e){var t=e.series_name,r=e.series_id,n=U()(e,["series_name","series_id"]);return d()({label:t,value:r},n)}):null})})}},watch:{components:function e(t){this.configuration=t.map(function(e){var t=e.m_config_id,r=e.door_flip,n=e.table_id,a=e.series_id,i=e.door_flippable,o=e.channel_id,s=e.component_id,c=e.distortion,l=e.cables_available,u=e.cables,f=e.x1,p=e.x2,d=e.line_left,h=e.line_right;return{m_config_id:t,door_flip:r,table_id:n,series_id:a,door_flippable:i,channel_id:o,component_id:s,distortion:c,cables_available:l,cables:u,line_left:d,line_right:h,width:Math.round((p-f)/10)}});this.height=this.height.replace(/\s/g,"");C["a"].api.geo.mesh({type:"mesh",id:this.$route.params.id}).pipe(this.processGeometry,"preview")},selectedTab:function e(t){this.thumbsForChannel(null);this.generateThumbnails(t)}},mounted:function e(){var t=this;C["a"].application.bus.$emit("changeElemnets","all");C["a"].application.bus.$on("resetTabs",function(){t.selectedTab="0"});C["a"].application.bus.$on("loadConfiguratorCustomParams",function(e){t.objToDispatch=e;t.setupsTree()});window.addEventListener("mouseup",this.stopDrag);window.addEventListener("mousemove",this.drag)},data:function e(){return{configuration:[],selectedTab:"0",distortion:false,thumbnailsStore:null,objToDispatch:{},setupsTreeNodes:[],isConfigurator:this.$route.name==="configurator"}}};var ne=re;var ae=r("f4c8");var ie=Object(D["a"])(ne,F,B,false,null,null,null);var oe=ie.exports;var se=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return e._m(0)};var ce=[function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"row"},[r("div",{staticClass:"col-xs-3"},[r("div",{staticClass:"shipping"},[e._v("\n      Ship in\n      "),r("br"),e._v("4-6 weeks\n    ")])]),r("div",{staticClass:"col-xs-9"},[r("button",{staticClass:"button button-red"},[e._v("Add to cart")])])])}];var le={name:"TylkoAddToCart"};var ue=le;var fe=r("ac6a9");var pe=Object(D["a"])(ue,se,ce,false,null,"faeb3fda",null);var de=pe.exports;var he={name:"TylkoPreviewConfiguration",props:["grabButtons","updateRenderer","element","type","decoderWidth","decoderHeight","decoderOutput","objectLine","choosenColor","distortion","density","depth","plinth","price","locked","mappedObjects","previousStateAvailble","nextStateAvailble","parametersStatesStatus"],components:{TylkoPreviewComponentSelect:oe,TylkoPreviewColors:z,TylkoAddToCart:de},methods:{showRendererElements:function e(){window.clearTimeout(this.rendererTimeout);C["a"].application.bus.$emit("showCompartments");this.rendererTimeout=window.setTimeout(function(){C["a"].application.bus.$emit("hideCompartments")},2e3)},updateConfigurator:function e(t,r,n){this.updateParam(t,r);if(n){this.lazyAutoSaveState()}else{this.autoSaveState()}this.showRendererElements()},resetToInitialState:function e(){C["a"].application.bus.$emit("loadConfiguratorCustomParams",{});C["a"].application.bus.$emit("resetTabs");C["a"].application.bus.$emit("resetPreviewParametersToInitialState",this.isConfigurator);this.autoSaveState()},saveState:function e(){C["a"].application.bus.$emit("savePreviewParametersState",this.isConfigurator)},autoSaveState:function e(){if(this.isConfigurator)this.saveState()},lazyAutoSaveState:_.a.debounce(function(){this.autoSaveState()},300),setState:function e(t){C["a"].application.bus.$emit("setPreviewParametersState",t)},undo:function e(){C["a"].application.bus.$emit("previousPreviewParametersState")},redo:function e(){C["a"].application.bus.$emit("nextPreviewParametersState")},computeHeight:function e(t){this.updateParam("decoderHeight",this.heightRange[t])},generateThumbnails:function e(){C["a"].application.bus.$emit("disableThumbnails")},updateParam:_.a.throttle(function(e,t){if(e==="decoderWidth"){t=t*10}C["a"].application.bus.$emit("updateRenderer",e,t);C["a"].application.bus.$emit("resetTabs");C["a"].application.bus.$emit("resetPrevSelected");this.generateThumbnails()},100)},watch:{decoderOutput:function e(t){console.log(123123,t);this.thumbnails=t.thumbnails?t.thumbnails:null;this.additional_height=[0,1].map(function(e){return t.additional_height[e]||0});this.setupID=t.setup_id;this.components=t.components;this.lines=t.lines;this.densityOptions=t.density_options},distortion:function e(t){this.choosenDistortion=t},depth:function e(t){this.choosenDepth=t},plinth:function e(t){this.choosenPlinth=t},locked:function e(t){this.choosenLocked=t},density:function e(t){this.choosenDensity=t},decoderWidth:function e(t){this.choosenWidth=t/10},decoderHeight:function e(t){var r=this;if(this.type==="mesh"){this.heightRange.forEach(function(e,n){if(e===+t){r.heightIndex=n}});this.choosenHeight=this.heightRange[this.heightIndex]+""}},heightIndex:function e(t){this.choosenHeight=this.heightRange[t]+""},element:function e(){var t=this;if(this.type==="component"){this.widthRange=[10,100]}else if(this.type==="mesh"){this.widthRange=this.element.dim_x.split("-").map(function(e){return+e/10});this.heightRange=this.element.dim_y.split(",").map(function(e){return+e});this.densityMode=this.element.density_mode;this.distortionMode=this.element.distortion_mode;this.heightRange.forEach(function(e,r){if(e===+t.decoderHeight){t.heightIndex=r}});this.choosenHeight=this.heightRange[this.heightIndex]+""}},parametersStatesStatus:function e(t){this.currentParametersHistoryState=t[0]}},mounted:function e(){this.choosenWidth=+this.decoderWidth/10;this.choosenHeight=+this.decoderHeight;this.choosenDistortion=this.distortion;this.choosenDensity=this.density;this.choosenDepth=this.depth;this.choosenPlinth=this.plinth;this.choosenLocked=this.locked},data:function e(){return{isConfigurator:this.$route.name==="configurator",widthRange:[1,2],choosenWidth:null,choosenHeight:null,heightIndex:null,heightRange:[1,2],densityMode:null,distortionMode:null,setupID:null,components:[],lines:{},choosenDensity:null,choosenDistortion:null,choosenDepth:null,choosenPlinth:null,additional_height:[0,0],choosenLocked:null,thumbnails:null,densityOptions:[],currentParametersHistoryState:0,rendererTimeout:null}}};var ve=he;var me=r("f111");var ge=Object(D["a"])(ve,T,k,false,null,null,null);var ye=ge.exports;var be=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"controls"},[r("q-btn",{attrs:{round:"round",icon:"straighten"},on:{click:e.showHideCompartments}},[r("q-tooltip",{attrs:{anchor:"center left",self:"center right",offset:[10,10]}},[e._v("Open/close doors & drawers")])],1),r("q-btn",{attrs:{round:"round",icon:"restore"},on:{click:e.resetChanges}},[r("q-tooltip",{attrs:{anchor:"center left",self:"center right",offset:[10,10]}},[e._v("Reset changes")])],1),r("q-btn",{attrs:{round:"round",icon:"undo"},on:{click:e.undo}},[r("q-tooltip",{attrs:{anchor:"center left",self:"center right",offset:[10,10]}},[e._v("Undo")])],1),r("q-btn",{attrs:{round:"round",icon:"redo"},on:{click:e.redo}},[r("q-tooltip",{attrs:{anchor:"center left",self:"center right",offset:[10,10]}},[e._v("Redo")])],1)],1)};var we=[];var Se={name:"TylkoPreviewControls",props:["showAllCompartmentsButton"],methods:{showHideCompartments:function e(){C["a"].application.bus.$emit("showAllCompartmentsButton",!this.showAllCompartmentsButton)},resetChanges:function e(){C["a"].application.bus.$emit("loadConfiguratorCustomParams",{});C["a"].application.bus.$emit("resetTabs");C["a"].application.bus.$emit("resetPreviewParametersToInitialState",true);C["a"].application.bus.$emit("savePreviewParametersState",true)},undo:function e(){C["a"].application.bus.$emit("previousPreviewParametersState")},redo:function e(){C["a"].application.bus.$emit("nextPreviewParametersState")}},watch:{},mounted:function e(){},data:function e(){return{empty:true}}};var xe=Se;var _e=r("e797");var Ce=Object(D["a"])(xe,be,we,false,null,null,null);var Pe=Ce.exports;var Ee=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("section",{staticClass:"pdp-2018 usps is-past-bottom is-in-view",attrs:{id:"type02-usps","data-scroll":"virtual"}},[r("div",{staticClass:"dimensions-info",class:{visible:e.isVisible}},[r("i",{staticClass:"material-icons"},[e._v("transform")]),r("span",[e._v("All dimensions are in centimeters (cm)")])]),r("div",{staticClass:"grid no-bottom-gutter"},[e._m(0),r("div",{staticClass:"grid-4 no-bottom-gutter rating text-center"},[r("div",{staticClass:"grade-list"},[r("svg",{attrs:{width:"24px",height:"21px",viewBox:"0 0 24 21",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}},[r("g",{attrs:{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[r("polygon",{attrs:{id:"Star",fill:"#FFB414",points:"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954"}})])]),r("svg",{attrs:{width:"24px",height:"21px",viewBox:"0 0 24 21",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}},[r("g",{attrs:{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[r("polygon",{attrs:{id:"Star",fill:"#FFB414",points:"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954"}})])]),r("svg",{attrs:{width:"24px",height:"21px",viewBox:"0 0 24 21",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}},[r("g",{attrs:{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[r("polygon",{attrs:{id:"Star",fill:"#FFB414",points:"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954"}})])]),r("svg",{attrs:{width:"24px",height:"21px",viewBox:"0 0 24 21",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}},[r("g",{attrs:{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[r("polygon",{attrs:{id:"Star",fill:"#FFB414",points:"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954"}})])]),r("svg",{attrs:{width:"24px",height:"21px",viewBox:"0 0 24 21",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}},[r("g",{attrs:{id:"Page-1",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[r("polygon",{attrs:{id:"Star",fill:"#FFB414",points:"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954"}})])]),r("span",{staticClass:"score"},[e._v("4.7")])]),e._m(1)]),e._m(2)])])};var Ae=[function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"grid-4 no-bottom-gutter"},[r("img",{attrs:{src:"https://tylko.com/r_static/basic-pdp/ic_b_free_delivery.710019919939897397.svg",width:"48",height:"48",alt:""}}),r("div",{staticClass:"text-slot text-center"},[r("h4",{staticClass:"th-5 text-center"},[e._v("Free delivery")]),r("p",{staticClass:"pdp-2018-tooltip-wrapper tp-small tfc-gray text-center"},[e._v("\n          anywhere in the EU\n          "),r("span",{staticClass:"question-mark pdp-2018-tooltip-trigger",attrs:{"data-tooltip-trigger":"freeDelivery"}},[r("span",{staticClass:"pdp-2018-tooltip tp-small tfc-gray",attrs:{id:"freeDelivery"}},[e._v("Enjoy free professional delivery – right to your doorstep. Your shelf will arrive in labelled flat pack boxes for quick, easy assembly. We ship to all EU countries, including Switzerland and Norway.")])])])])])},function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"text-slot rating"},[r("h4",{staticClass:"th-5 text-center"},[e._v("Customer rating")]),r("p",{staticClass:"tp-small tfc-gray text-center"},[e._v("based on 1804 reviews")])])},function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"grid-4 no-bottom-gutter"},[r("img",{attrs:{src:"https://tylko.com/r_static/basic-pdp/ic_b_free_returns.292403674054.svg",width:"48",height:"48",alt:""}}),r("div",{staticClass:"text-slot"},[r("h4",{staticClass:"th-5 text-center"},[e._v("100 days to fall in love")]),r("p",{staticClass:"pdp-2018-tooltip-wrapper tp-small tfc-gray text-center"},[e._v("\n          or return it for free\n          "),r("span",{staticClass:"question-mark pdp-2018-tooltip-trigger",attrs:{"data-tooltip-trigger":"freeReturns"}},[r("span",{staticClass:"pdp-2018-tooltip tp-small tfc-gray",attrs:{id:"freeReturns"}},[e._v("If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days and give you a full refund. No questions asked.")])])])])])}];var Te={props:["isVisible"],data:function e(){return{isVisible:false}},name:"TylkoUsps"};var ke=Te;var Oe=r("68ee");var $e=Object(D["a"])(ke,Ee,Ae,false,null,"96c8332e",null);var je=$e.exports;var Ie=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return e._m(0)};var Me=[function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("a",{staticClass:"nav",attrs:{href:"https://preview.uxpin.com/c9a95a25baed4bea4958c4942cdff85af8a8b0db#/pages/106584156/simulate/no-panels?mode=cvhdmf"}},[r("div",{staticClass:"logo"},[e._v("Tylko")]),r("ul",[r("li",[e._v("Shelves")]),r("li",[e._v("Reviews")]),r("li",[e._v("Our Mission")]),r("li",[e._v("App")]),r("li",{staticClass:"wishlist"},[r("img",{staticClass:"menu-icon",attrs:{width:"25",height:"21",src:"https://tylko.com/r_static/icons/icon-heart-3.3621101997981029856.svg",alt:"Wishlist"}})]),r("li",{staticClass:"cart"},[r("img",{staticClass:"menu-icon",attrs:{width:"25",height:"21",src:"https://tylko.com/r_static/icons/icon-cart.100724389979834.svg",alt:"Cart"}})])])])}];var Re={name:"TylkoMenu"};var De=Re;var Le=r("888e");var ze=Object(D["a"])(De,Ie,Me,false,null,"6ac1de8d",null);var Fe=ze.exports;var Be={name:"PagePreview",components:{TylkoPreviewConfiguration:ye,TylkoPreviewControls:Pe,TylkoUsps:je,TylkoMenu:Fe},methods:{innitialize:function e(){var t=this;var r={};if(this.$route.params.startingParams){this.$route.params.startingParams.split("&").forEach(function(e){var t=e.split("="),n=b()(t,2),a=n[0],i=n[1];i=typeof i==="undefined"||i==="on"?true:i==="off"?false:isNaN(i)?i:Number(i);r[a]=i})}if(this.decoderType==="component"){this.decoderWidth=_.a.get(r,"width",500)}else if(this.decoderType==="mesh"){this.decoderWidth=_.a.get(r,"width",this.element.dim_x.split("-").map(function(e){return+e})[0]);this.decoderHeight=_.a.get(r,"height",this.element.dim_y.split(",").map(function(e){return+e})[0])}this.choosenColor=_.a.get(r,"color","0:0");this.density=_.a.get(r,"density",0);this.depth=_.a.get(r,"depth",320);this.plinth=_.a.get(r,"plinth",true);this.distortion=_.a.get(r,"distortion",0);this.saveCurrentPreviewParametersState(false,0);if(!this.isConfigurator){this.loadFromStorage();this.saveCurrentPreviewParametersState(true)}if(this.isConfigurator)this.locked=true;this.update();this.initRenderer();if(this.isConfigurator){document.getElementsByClassName("menu-column")[0].style.display="none"}C["a"].application.bus.$on("showCompartments",function(){t.showCompartments()});C["a"].application.bus.$on("hideCompartments",function(){t.hideCompartments()});C["a"].application.bus.$on("showRendererElements",function(){t.$refs.rendererContainer.classList.add("show-elements")});C["a"].application.bus.$on("hideRendererElements",function(){t.$refs.rendererContainer.classList.remove("show-elements")});C["a"].application.bus.$on("clearPrevSelectedTimeout",function(){window.clearTimeout(t.timeoutHandle)});C["a"].application.bus.$on("resetPrevSelected",function(){t.prevSelected=null});C["a"].application.bus.$on("triggerPrevSelected",function(){if(t.$route.name==="configurator"){t.selectOnLeave()}});C["a"].application.bus.$on("dispatchConfiguratorCustomParams",function(e){t.configuratorCustomParams=e;t.update()});C["a"].application.bus.$on("changeSetupTabIndex",function(e){t.selectedSetupTab=e;t.update()});C["a"].application.bus.$on("thumbsForChannel",function(e){t.thumbsForChannel=e});C["a"].application.bus.$on("changeElemnets",function(e){t.elements=e;t.update()});C["a"].application.bus.$on("generateThumbnails",function(){t.generateThumbnails=true});C["a"].application.bus.$on("disableThumbnails",function(){t.generateThumbnails=false});C["a"].application.bus.$on("updateRenderer",function(e,r){if(!t.hasOwnProperty(e))return;t[e]=r;if(e==="choosenColor"&&t.isConfigurator)t.saveCurrentPreviewParametersState(true);t.updatePreviewStore(e,r);if(e==="locked"){t.initRenderer();t.switchElements()}else{t.update()}});C["a"].application.bus.$on("resetPreviewParametersToInitialState",function(e){if(!t.previewParametersStates){console.log("---- Brak stanu początkowego!");t.updateStatesAvailbility();return}t.loadPreviewParametersState(t.previewParametersStates[0]);if(e)t.saveCurrentPreviewParametersState(true);t.update()});C["a"].application.bus.$on("savePreviewParametersState",function(e){t.saveCurrentPreviewParametersState(e)});C["a"].application.bus.$on("previousPreviewParametersState",function(){t.loadPreviousPreviewParametersState()});C["a"].application.bus.$on("nextPreviewParametersState",function(){t.loadNextPreviewParametersState()});C["a"].application.bus.$on("setPreviewParametersState",function(e){if(!t.previewParametersStates||t.previewParametersStates.length===0||e<0||e>t.previewParametersStates.length-1){console.log("---- Niepoprawny index!");t.updateStatesAvailbility();return}t.currentPreviewParametersState=e;t.loadPreviewParametersState(t.previewParametersStates[e])});C["a"].application.bus.$on("setConfiguratorType",function(e){t.isConfigurator=e});C["a"].application.bus.$on("showAllCompartmentsButton",function(e){t.showAllCompartmentsButton=e});this.$refs.configuration.$refs.previewSelect.mapEdges=this.edgesToScreen;this.mapCompartments(this.decoderOutput);if(!this.showAllCompartmentsButton){this.showAllCompartments=false;this.$refs.rendererContainer.classList.remove("show-compartment")}else{this.showAllCompartments=true;this.$refs.rendererContainer.classList.add("show-compartment")}this.visibleUsps()},visibleUsps:function e(){console.log(123,this.$refs.configuration.$refs.previewSelect.selectedTab,this.showAllCompartments,this.$refs.configuration.$refs.previewSelect.selectedTab!=0||this.showAllCompartments);this.visibleUspsText=this.$refs.configuration.$refs.previewSelect.selectedTab!=0||this.showAllCompartments},showCompartments:function e(){this.showAllCompartments=true;this.$refs.rendererContainer.classList.add("show-compartment")},hideCompartments:function e(){if(!this.showAllCompartmentsButton){this.showAllCompartments=false;this.$refs.rendererContainer.classList.remove("show-compartment")}},arrageComponentsPopovers:function e(){console.log("box-list",this.boxList.map(this.objectToScreen));this.mappedObjects=this.boxList.map(this.objectToScreen)},selectOnLeave:function e(){var t=this;this.timeoutHandle=window.setTimeout(function(){console.log(123,"selectOnLeave");t.hoverElement=null;t.currentRenderer.renderer.domElement.style.cursor="auto"},500)},selectOnEnter:function e(t){window.clearTimeout(this.timeoutHandle);this.hoverElement=t;this.currentRenderer.renderer.domElement.style.cursor="pointer";if(this.$refs.configuration.$refs.previewSelect.selectedTab=="0"){}else{}},select:function e(t){if(""+(t+1)===this.$refs.configuration.$refs.previewSelect.selectedTab){this.$refs.configuration.$refs.previewSelect.selectedTab="0";return}this.$refs.configuration.$refs.previewSelect.selectedTab=""+(t+1);this.mapCompartments(this.decoderOutput);this.buildResizers(this.decoderOutput)},rendererMouseOver:function e(){this.$refs.rendererContainer.classList.add("show-elements")},rendererMouseLeave:function e(){var t=this;this.rendererLeaveTimeout=window.setTimeout(function(){t.$refs.rendererContainer.classList.remove("show-elements")},100)},previewSetComponent:function e(t){this.select(t)},mouseMoveSetComponent:function e(t){this.selectOnEnter(t)},mouseMoveOnLeave:function e(){this.selectOnLeave()},deselectComponent:function e(){this.deselect()},deselect:function e(){this.select(-1)},compartmentsToScreen:function e(t){var r=this.currentRenderer.renderer.domElement;var n=parseFloat(r.style.width),a=parseFloat(r.style.height);var i=new THREE.Vector3;i.set(t.x,t.y,t.z);i.applyAxisAngle(new THREE.Vector3(0,1,0),.5);i.project(this.currentRenderer.camera);i.x=Math.round((i.x+1)*n/2);i.y=Math.round((-i.y+1)*a/2);i.z=0;return{value:t.value,point:i}},edgesToScreen:function e(t,r,n){var a=.5;var i=this.currentRenderer.renderer.domElement;var o=parseFloat(i.style.width),s=parseFloat(i.style.height);var n=new THREE.Vector3;n.set(t.x,t.y,320);n.applyAxisAngle(new THREE.Vector3(0,1,0),a);console.log("e2s",n,o,s);if(r){var c=new THREE.Vector3;c.set(t.x/o*2-1,-(t.y/s)*2+1,.5);c.unproject(this.currentRenderer.camera);c.sub(this.currentRenderer.camera.position).normalize();var l=new THREE.Ray(this.currentRenderer.camera.position,c);var u=new THREE.Vector3;l.closestPointToPoint(new THREE.Vector3(0,0,0),u);return u}else{n.project(this.currentRenderer.camera);n.x=Math.round((n.x+1)*o/2);n.y=Math.round((-n.y+1)*s/2);n.z=0}return n},objectToScreen:function e(t){t.y=0;var r=this.currentRenderer.renderer.domElement;var n=parseFloat(r.style.width),a=parseFloat(r.style.height);var i=new THREE.Vector3;i.set(t.x,t.y,t.z);i.applyAxisAngle(new THREE.Vector3(0,1,0),.5);i.project(this.currentRenderer.camera);i.x=Math.round((i.x+1)*n/2);i.y=Math.round((-i.y+1)*a/2);i.z=0;return i},update:function e(){var t={type:this.decoderType,id:this.decoderId,width:this.decoderWidth,height:this.decoderHeight,mode:this.mode,elements:this.elements};if(this.decoderType==="mesh"){C["a"].api.geo.mesh(t).pipe(this.processGeometry,"preview")}else if(this.decoderType==="component"){C["a"].api.geo.component(t).pipe(this.processGeometry,"preview")}},updatePreviewStore:function e(t,r){var n={};if(C["a"].application.settings.get("previewStore")[this.decoderType]&&C["a"].application.settings.get("previewStore")[this.decoderType][this.decoderId]){n=C["a"].application.settings.get("previewStore")[this.decoderType][this.decoderId]}C["a"].application.settings.setObjectItem("previewStore",this.decoderType,Object.assign({},f()({},this.decoderId,d()({},n,f()({},t,r)))))},processGeometry:function e(t){var r=this;console.log("PROCESSS");if(!t)return;this.objectLine=t.superior_object_line;if(!this.currentGeometry){if(!this.choosenColor||this.objectLine==="type_02"&&!this.choosenColor.startsWith("1")){this.choosenColor="1:0"}else if(!this.choosenColor||this.objectLine==="type_01"&&!this.choosenColor.startsWith("0")){this.choosenColor="0:0"}}this.currentGeometry=t;switch(parseInt(this.mode)){case 8:this.designerRenderer.then(function(e){var n=Object(P["getGeometry"])({serialization:t},{width:r.decoderWidth,height:r.meshMode?r.decoderHeight:null,depth:r.depth,mesh_setup:null,geom_id:r.decoderId,geom_type:r.decoderType,generate_thumbnails:r.generateThumbnails,thumbsForChannel:r.thumbsForChannel,distortion:r.meshMode?r.distortion:null,density:r.meshMode?r.density:null,plinth:r.plinth,configurator_custom_params:r.configuratorCustomParams?r.configuratorCustomParams:null},{format:"gallery"});if(r.selectedSetupTab&&r.selectedSetupTab>0){var a=r.decoderOutput.components[r.selectedSetupTab-1].m_config_id;var i=["drawers","doors","buttons"];for(var o=0;o<i.length;o++){var s=i[o];var c=true;var l=false;var u=undefined;try{for(var f=n[s][Symbol.iterator](),p;!(c=(p=f.next()).done);c=true){var d=p.value;if(d.m_config_id==a){d.selected=true}}}catch(v){l=true;u=v}finally{try{if(!c&&f.return!=null){f.return()}}finally{if(l){throw u}}}}}var h=console.warn;console.warn=function(){};console.log("PROCESSS DISPLAY");e.displayShelf(n,r.choosenColor,r.previewRenderer.scene,r.previewRenderer.camera,r.previewRenderer.renderer);e.previewSetComponent=r.previewSetComponent;e.mouseMoveSetComponent=r.mouseMoveSetComponent;e.mouseMoveOnLeave=r.mouseMoveOnLeave;e.deselectComponent=r.deselectComponent;console.warn=h;r.faceBox=e.facePlane;r.currentRenderer=r.previewRenderer;r.price=e.getPrice(n);r.boxList=e.getIndicatorBoxesPositions();r.currentGeometry=n;r.decoderOutput=n;r.arrageComponentsPopovers()});break}this.loaderHidden=true},loadFromStorage:function e(){if(C["a"].application.settings.get("previewStore")[this.decoderType]&&C["a"].application.settings.get("previewStore")[this.decoderType][this.decoderId]){var t=C["a"].application.settings.get("previewStore")[this.decoderType][this.decoderId];for(var r in t){if(t.hasOwnProperty(r)){this[r]=t[r]}}}},initialPreviewParametersState:function e(){},loadPreviousPreviewParametersState:function e(){if(this.currentPreviewParametersState<=0||this.previewParametersStates.length<=1){console.log("---- Tylko jeden zapisany stan lub aktualny stan to stan początkowy!");this.updateStatesAvailbility();return}var t=this.currentPreviewParametersState-1;var r={};if(t<=this.previewParametersStates.length){r=this.previewParametersStates[t];this.currentPreviewParametersState=t}else{this.currentPreviewParametersState=this.previewParametersStates.length-1;r=this.previewParametersStates[this.currentPreviewParametersState]}this.loadPreviewParametersState(r)},loadNextPreviewParametersState:function e(){if(this.currentPreviewParametersState>this.previewParametersStates.length){console.log("---- Aktualny stan parametrow jest ostatnim zapisanym!");this.updateStatesAvailbility();return}this.currentPreviewParametersState+=1;var t=this.previewParametersStates[this.currentPreviewParametersState];this.loadPreviewParametersState(t)},loadPreviewParametersState:function e(t){Object.assign(this,t);this.updateStatesAvailbility();this.update()},updateStatesAvailbility:function e(){this.previousStateAvailble=this.currentPreviewParametersState>0&&this.previewParametersStates&&this.previewParametersStates.length!==0;this.nextStateAvailble=this.currentPreviewParametersState<this.previewParametersStates.length-1;this.parametersStatesStatus=[this.currentPreviewParametersState,this.previewParametersStates.length-1]},saveCurrentPreviewParametersState:function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;if(t&&this.currentPreviewParametersState!==this.previewParametersStates.length-1){this.previewParametersStates.length=this.currentPreviewParametersState+1}var n={choosenColor:this.choosenColor,decoderWidth:this.decoderWidth,decoderHeight:this.decoderHeight,density:this.density,depth:this.depth,plinth:this.plinth,distortion:this.distortion,configuratorCustomParams:this.configuratorCustomParams};if(r===null)this.previewParametersStates.push(n);else this.previewParametersStates.splice(r,1,n);this.currentPreviewParametersState=this.previewParametersStates.length-1;this.updateStatesAvailbility()},initRenderer:function e(){var t=this;if(this.$el.querySelector(".production-renderer-canvas").firstChild){this.$el.querySelector(".production-renderer-canvas").firstChild.remove()}this.designerRenderer=Object(A["a"])();var r=new E["a"]({});this.previewRenderer=r.set({container:this.$el.querySelector(".production-renderer-canvas"),width:this.vwidth,height:this.vheight,cameraLock:this.locked,updatedCameraCallback:function e(){t.arrageComponentsPopovers();t.mapCompartments(t.decoderOutput);t.updateResizers()}});var n=console.warn;console.warn=function(){};this.previewRenderer.resetScene();this.designerRenderer.then(function(e){e.setScene(t.previewRenderer.scene);e.clearScene(t.previewRenderer.scene);e.setBackgroundScene(t.previewRenderer._scene)});console.warn=n;window.addEventListener("resize",function(){var e=t.$el.getBoundingClientRect();t.previewRenderer.resize({width:e.width*.7,height:e.height})});this.update()},switchElements:function e(){if(this.elements==="all"){this.designerRenderer.then(function(e){return e.setDesignerMode(1)})}else if(this.elements==="all_opened"){this.designerRenderer.then(function(e){return e.setDesignerMode(1)})}},buildResizers:function e(t){},updateResizers:function e(){},mapCompartments:function e(t){var r=[],n=[];var a=this.$refs.configuration.$refs.previewSelect.selectedTab;if(this.showAllCompartments){t.components.forEach(function(e){var t=e.compartments;t.forEach(function(e){var t=e.height,a=e.width;r=[].concat(o()(r),[a]);n=[].concat(o()(n),[t])})})}else{if(a&&a!=="0"){t.components[Number(a)-1].compartments.forEach(function(e){var t=e.height,a=e.width;r=[].concat(o()(r),[a]);n=[].concat(o()(n),[t])})}}this.visibleUsps();this.compartmentsWidths=r.map(this.compartmentsToScreen);this.compartmentsHeights=n.map(this.compartmentsToScreen)}},watch:{showAllCompartmentsButton:function e(t){this.mapCompartments(this.decoderOutput);if(!t){this.showAllCompartments=false;this.$refs.rendererContainer.classList.remove("show-compartment")}else{this.showAllCompartments=true;this.$refs.rendererContainer.classList.add("show-compartment")}},loaderHidden:function e(t){if(t){this.$refs.rendererWapper.classList.remove("load-hidden")}},elements:function e(){this.switchElements()},showAllCompartments:function e(){this.update()},decoderOutput:function e(t){this.mapCompartments(t);this.buildResizers(t)}},created:function e(){if(this.$route.params.type!=="mesh"&&this.$route.params.type!=="component"){this.$router.push("/")}this.isConfigurator=this.$route.name==="configurator"},mounted:function e(){var t=this;var r=this.$route.params,n=r.type,a=r.id;this.decoderType=n;this.meshMode=n==="mesh";this.decoderId=a;this.$refs.configuration.$refs.previewSelect.$el.addEventListener("mouseover",function(e){window.clearTimeout(t.rendererLeaveTimeout)});if(this.decoderType==="component"){C["a"].api.componentSetup(this.decoderId).subscribe(function(e){document.title="Component - ".concat(e.name);t.element=e;t.innitialize()})}else if(this.decoderType==="mesh"){C["a"].api.mesh(this.decoderId).subscribe(function(e){document.title="Mesh - ".concat(e.name);t.element=e;t.innitialize()})}},data:function e(){return{grabButtons:{left:{x:0,y:0},right:{x:0,y:0}},previewParametersStates:[],currentPreviewParametersState:0,previousStateAvailble:false,nextStateAvailble:false,parametersStatesStatus:[0,0],mappedObjects:[],decoderType:"mesh",vwidth:window.innerWidth*.7-42,vheight:window.innerHeight,decoderWidth:null,decoderHeight:null,decoderId:null,meshMode:null,mode:8,elements:"all",choosenColor:"0:0",currentGeometry:null,previewRenderer:null,decoderOutput:null,solidMode:false,designerRenderer:null,loaderHidden:false,element:null,configuratorCustomParams:null,selectedSetupTab:0,objectLine:null,distortion:100,density:100,depth:320,plinth:true,price:"-",locked:true,generateThumbnails:false,thumbsForChannel:null,prevSelected:null,timeoutHandle:null,isConfigurator:null,compartmentsWidths:[],compartmentsHeights:[],showAllCompartments:false,showAllCompartmentsButton:false,rendererLeaveTimeout:null,hoverElement:null,visibleUspsText:false}}};var He=Be;var qe=r("c498");var Ue=Object(D["a"])(He,n,a,false,null,null,null);var Ne=t["default"]=Ue.exports},"7a69":function(e,t,r){var n=r("f202");var a=r("3fb5");var i=r("88a0");var o=r("5b25");var s=r("320c");var c=r("a3a2");var l=r("6418");var u=THREE.BufferGeometry;e.exports=function e(t){return new f(t)};function f(e){u.call(this);if(typeof e==="string"){e={text:e}}this._opt=s({},e);if(e)this.update(e)}a(f,u);f.prototype.update=function(e){if(typeof e==="string"){e={text:e}}e=s({},this._opt,e);if(!e.font){throw new TypeError("must specify a { font } in options")}this.layout=n(e);var t=e.flipY!==false;var r=e.font;var a=r.common.scaleW;var l=r.common.scaleH;var u=this.layout.glyphs.filter(function(e){var t=e.data;return t.width*t.height>0});this.visibleGlyphs=u;var f=c.positions(u);var p=c.uvs(u,a,l,t);var d=i({clockwise:true,type:"uint16",count:u.length});o.index(this,d,1,"uint16");o.attr(this,"position",f,2);o.attr(this,"uv",p,2);if(!e.multipage&&"page"in this.attributes){this.removeAttribute("page")}else if(e.multipage){var h=c.pages(u);o.attr(this,"page",h,1)}};f.prototype.computeBoundingSphere=function(){if(this.boundingSphere===null){this.boundingSphere=new THREE.Sphere}var e=this.attributes.position.array;var t=this.attributes.position.itemSize;if(!e||!t||e.length<2){this.boundingSphere.radius=0;this.boundingSphere.center.set(0,0,0);return}l.computeSphere(e,this.boundingSphere);if(isNaN(this.boundingSphere.radius)){console.error("THREE.BufferGeometry.computeBoundingSphere(): "+"Computed radius is NaN. The "+'"position" attribute is likely to have NaN values.')}};f.prototype.computeBoundingBox=function(){if(this.boundingBox===null){this.boundingBox=new THREE.Box3}var e=this.boundingBox;var t=this.attributes.position.array;var r=this.attributes.position.itemSize;if(!t||!r||t.length<2){e.makeEmpty();return}l.computeBox(t,e)}},"7a87":function(e,t,r){var n=r("b639").Buffer;e.exports=function(e,t){if(!n.isBuffer(e))return undefined;if(!n.isBuffer(t))return undefined;if(typeof e.equals==="function")return e.equals(t);if(e.length!==t.length)return false;for(var r=0;r<e.length;r++){if(e[r]!==t[r])return false}return true}},"7e4b":function(e,t,r){},"7ece":function(e,t){e.exports=function e(t){if(!t)throw new Error("no data provided");t=t.toString().trim();var n={pages:[],chars:[],kernings:[]};var a=t.split(/\r\n?|\n/g);if(a.length===0)throw new Error("no data in BMFont file");for(var i=0;i<a.length;i++){var o=r(a[i],i);if(!o)continue;if(o.key==="page"){if(typeof o.data.id!=="number")throw new Error("malformed file at line "+i+" -- needs page id=N");if(typeof o.data.file!=="string")throw new Error("malformed file at line "+i+' -- needs page file="path"');n.pages[o.data.id]=o.data.file}else if(o.key==="chars"||o.key==="kernings"){}else if(o.key==="char"){n.chars.push(o.data)}else if(o.key==="kerning"){n.kernings.push(o.data)}else{n[o.key]=o.data}}return n};function r(e,t){e=e.replace(/\t+/g," ").trim();if(!e)return null;var r=e.indexOf(" ");if(r===-1)throw new Error("no named row at line "+t);var a=e.substring(0,r);e=e.substring(r+1);e=e.replace(/letter=[\'\"]\S+[\'\"]/gi,"");e=e.split("=");e=e.map(function(e){return e.trim().match(/(".*?"|[^"\s]+)+(?=\s*|\s*$)/g)});var i=[];for(var o=0;o<e.length;o++){var s=e[o];if(o===0){i.push({key:s[0],data:""})}else if(o===e.length-1){i[i.length-1].data=n(s[0])}else{i[i.length-1].data=n(s[0]);i.push({key:s[1],data:""})}}var c={key:a,data:{}};i.forEach(function(e){c.data[e.key]=e.data});return c}function n(e){if(!e||e.length===0)return"";if(e.indexOf('"')===0||e.indexOf("'")===0)return e.substring(1,e.length-1);if(e.indexOf(",")!==-1)return a(e);return parseInt(e,10)}function a(e){return e.split(",").map(function(e){return parseInt(e,10)})}},8362:function(e,t){e.exports=n;var r=Object.prototype.toString;function n(e){var t=r.call(e);return t==="[object Function]"||typeof e==="function"&&t!=="[object RegExp]"||typeof window!=="undefined"&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)}},"85a6":function(e,t,r){"use strict";var n=r("247b");var a=r("9b8e");var i=r("359c");var o=r("7341");var s=r("1adf");var c=r("6db0");var l=r("970b");var u=r.n(l);var f=r("5bc3");var p=r.n(f);var d=r("e411");var h=r("e1c2");var v=r("498f");var m=r.n(v);var g=r("1c33");var y=r.n(g);var b="precision mediump float;\n#define GLSLIFY 1\nvarying vec3 rayPosition;varying vec3 rayDirection;uniform vec4 shadowSource;uniform float shadowPower;uniform vec3 shelfSize;uniform vec4 idents[20];uniform float shelfGap;float a(float b,float c){return min(b,c);}vec2 a(vec2 b,vec2 c){return (b.x<c.x)?b:c;}float d(vec3 e,vec3 f){vec3 g=abs(e)-f;return min(max(g.x,max(g.y,g.z)),0.0)+length(max(g,0.0));}float h(vec2 i){return max(i.x,i.y);}float j(vec2 k,vec2 l){return h(abs(k)-l);}float m(float n,float l,float o){vec2 p=max(vec2(o+n,o+l),vec2(0));return min(-o,max(n,l))+length(p);}vec2 q(vec3 k){k.y-=shelfSize.y-20.;k.z-=shelfSize.z;float r=0.;vec3 s=shelfSize;s.y-=20.;float t=d(k+vec3(0,0,0),s);float u=dot(k-vec3(0,-shelfSize.y-shelfGap,-shelfGap),vec3(0,1.,0));float v=dot(k-vec3(0,0,-(shelfSize.z+shelfGap)),vec3(0,0,1.));r=a(v,u);for(int w=0;w<20;w++){float x=dot(idents[w],vec4(1.0));if(x!=0.0){vec2 e=idents[w].xy;vec2 y=idents[w].zw;t=max(-j(k.xy+e,y),t);}}r=a(r,t);return vec2(r,0);}vec2 z(vec3 A,vec3 B,float C,float D){float E=D*2.0;float F=+0.0;float G=-1.0;vec2 H=vec2(-1.0,-1.0);for(int w=0;w<140;w++){if(E<D||F>C) break;vec2 I=q(A+B*F);E=I.x;G=I.y;F+=E;}if(F<C){H=vec2(F,G);}return H;}vec2 z(vec3 A,vec3 B){return z(A,B,20.0,0.001);}vec3 J(vec3 K,float L){const vec3 M=vec3(1.0,-1.0,-1.0);const vec3 N=vec3(-1.0,-1.0,1.0);const vec3 O=vec3(-1.0,1.0,-1.0);const vec3 P=vec3(1.0,1.0,1.0);return normalize(M*q(K+M*L).x+N*q(K+N*L).x+O*q(K+O*L).x+P*q(K+P*L).x);}vec3 J(vec3 K){return J(K,0.002);}float Q(vec3 R,vec3 S,float T,float U,float V){float H=2.5;float W=0.0;float X=0.1;for(int w=0;w<26;++w){float Y=q(R+S*X).x;if(Y<0.001) return .0;float Z=Y*Y/(2.*W);float g=sqrt(Y*Y-Z*Z);H=min(H,V*g/max(0.0,X-Z));W=Y;X+=Y;}return H;}vec3 ba(vec3 K,vec3 bb,vec4 bc){vec3 bd=bc.xyz-K;float be=length(bd);bd=normalize(bd);float bf=2.0;float bg=Q(K,bd,.625,be,shadowPower);return vec3(bg);}void main(){vec3 bh=vec3(0.,0.,0.);vec3 S,R;R=rayPosition;S=rayDirection;vec2 X=z(rayPosition,S,35000.,0.001);if(X.x>0.5){vec3 K=R+S*X.x;vec3 bi=J(K);vec3 bj=ba(K,bi,vec4(shadowSource.xyz,shadowPower));bh=bj;bh=vec3(dot(bh,vec3(.3,.9,.0)));}gl_FragColor.rgb=bh;gl_FragColor.a=1.0-smoothstep(1.0,0.0,length(bh));}";var w="precision mediump float;\n#define GLSLIFY 1\nvarying vec3 rayPosition;varying vec3 rayDirection;uniform vec4 shadowSource;uniform float shadowPower;uniform vec3 shelfSize;uniform vec4 idents[20];uniform float shelfGap;float a(float b,float c){return min(b,c);}vec2 a(vec2 b,vec2 c){return (b.x<c.x)?b:c;}float d(vec3 e,vec3 f){vec3 g=abs(e)-f;return min(max(g.x,max(g.y,g.z)),0.0)+length(max(g,0.0));}float h(vec2 i){return max(i.x,i.y);}float j(vec2 k,vec2 l){return h(abs(k)-l);}float m(float n,float l,float o){vec2 p=max(vec2(o+n,o+l),vec2(0));return min(-o,max(n,l))+length(p);}vec2 q(vec3 k){k.y-=shelfSize.y-20.;k.z-=shelfSize.z;float r=0.;vec3 s=shelfSize;s.y-=20.;float t=d(k+vec3(0,0,0),s);float u=dot(k-vec3(0,-shelfSize.y-shelfGap,-shelfGap),vec3(0,1.,0));float v=dot(k-vec3(0,0,-(shelfSize.z+shelfGap)),vec3(0,0,1.));r=a(v,u);r=a(r,t);return vec2(r,0);}vec2 w(vec3 x,vec3 y,float z,float A){float B=A*2.0;float C=+0.0;float D=-1.0;vec2 E=vec2(-1.0,-1.0);for(int F=0;F<10;F++){if(B<A||C>z) break;vec2 G=q(x+y*C);B=G.x;D=G.y;C+=B;}if(C<z){E=vec2(C,D);}return E;}vec2 w(vec3 x,vec3 y){return w(x,y,20.0,0.001);}vec3 H(vec3 I,float J){const vec3 K=vec3(1.0,-1.0,-1.0);const vec3 L=vec3(-1.0,-1.0,1.0);const vec3 M=vec3(-1.0,1.0,-1.0);const vec3 N=vec3(1.0,1.0,1.0);return normalize(K*q(I+K*J).x+L*q(I+L*J).x+M*q(I+M*J).x+N*q(I+N*J).x);}vec3 H(vec3 I){return H(I,0.002);}float O(vec3 P,vec3 Q,float R,float S,float T){float E=2.5;float U=0.0;float V=0.1;for(int F=0;F<26;++F){float W=q(P+Q*V).x;if(W<0.001) return .0;float X=W*W/(2.*U);float g=sqrt(W*W-X*X);E=min(E,T*g/max(0.0,V-X));U=W;V+=W;}return E;}vec3 Y(vec3 I,vec3 Z,vec4 ba){vec3 bb=ba.xyz-I;float bc=length(bb);bb=normalize(bb);float bd=2.0;float be=O(I,bb,.625,bc,shadowPower);return vec3(be);}void main(){vec3 bf=vec3(0.,0.,0.);vec3 Q,P;P=rayPosition;Q=rayDirection;vec2 V=w(rayPosition,Q,35000.,0.001);if(V.x>0.5){vec3 I=P+Q*V.x;vec3 bg=H(I);vec3 bh=shadowSource.xyz;vec3 bi=Y(I,bg,vec4(bh,shadowPower));bf=bi;bf=vec3(dot(bf,vec3(.3,.9,.0)));}gl_FragColor.rgb=bf;gl_FragColor.a=1.0-smoothstep(1.0,0.0,length(bf));}";var S="precision mediump float;\n#define GLSLIFY 1\n\n#define clip 35000.0\nattribute vec2 position;attribute vec2 uvs;varying vec4 ndc;varying vec3 rayDirection;varying vec3 rayPosition;uniform vec3 cameraPosition;uniform vec2 warp;void main(){vec2 a=uvs;a=(a*2.0)-vec2(1.0,0.0);ndc=vec4(a,1.0,1.0)*clip;rayDirection=vec3(0,0,-1);rayPosition=vec3(0);rayPosition.z+=cameraPosition.z;rayPosition.xy+=ndc.xy*warp;gl_Position=vec4(position,0.0,1.0);}";var x="precision mediump float;\n#define GLSLIFY 1\n\n#define clip 35000.0\nattribute vec2 position;attribute vec2 uvs;varying vec4 ndc;varying vec3 rayDirection;varying vec3 rayPosition;uniform vec3 cameraPosition;uniform vec2 warp;void main(){vec2 a=uvs;a=(a*2.0)-vec2(1.0,0.0);ndc=vec4(a,1.0,1.0)*clip;rayDirection=vec3(0,-1,0);rayPosition=vec3(0);rayPosition.y+=cameraPosition.z;rayPosition.xz+=ndc.xy*warp;gl_Position=vec4(position,0.0,1.0);}";var _=42;var C=new d["Matrix4"];var P=new d["Matrix4"];var E=new d["Matrix4"];var A=new d["Matrix4"];function T(e,t,r){var n=e.camera;var a=e.geo;var i={blend:{enable:true,func:{srcRGB:"src alpha",srcAlpha:0,dstRGB:"one minus src alpha",dstAlpha:1},equation:{rgb:"add",alpha:"add"},color:[0,0,0,0]}};var o=function t(){return[e.ivy.width/2,e.ivy.getHeight()/2,320/2]};var s=m()(t);var c={};for(var l=0;l<_;l++){c["idents["+l+"]"]=s.prop("idents"+l)}var u=Object.assign({cameraPosition:s.prop("cameraPosition"),shelfGap:s.prop("shelfGap"),shelfSize:s.prop("shelfSize")},c,{shadowSource:s.prop("shadowSource"),shadowPower:s.prop("shadowPower"),warp:s.prop("warp")});var f=s(Object.assign({frag:r?w:b,vert:r?x:S,attributes:{position:s.buffer(y.a.verts),uvs:s.buffer(y.a.uvs)},uniforms:u,count:6},i));var p=function e(t){var r=t.settings;s.clear({depth:1,color:[0,0,0,1]});var n=a.getIndents(o());var i={};for(var c=0;c<_;c++){if(n[c]){i["idents"+c]=n[c]}else{i["idents"+c]=[0,0,0,0]}}f(Object.assign({shelfSize:o(),shadowPower:r.power,shelfGap:r.shelfGap,cameraPosition:[0,0,r.cameraz*3],shadowSource:[r.x,r.y,r.z,0],warp:[r.warpx,r.warpy]},i))};var d=function e(){return false};var h=function e(){return false};return{render:p,updateIndents:d,updateSize:h}}var k=r("723b");var O=window.THREE||r("e411");var $=function e(t,r){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var a=this;var i={NONE:-1,ROTATE:0,ZOOM:1,PAN:2,TOUCH_ROTATE:3,TOUCH_ZOOM_PAN:4};this.object=t;this.domElement=r!==undefined?r:document;this.locked=n;this.enabled=true;this.screen={left:0,top:0,width:0,height:0};this.rotateSpeed=1;this.zoomSpeed=1.2;this.panSpeed=.3;this.noRotate=false;this.noZoom=false;this.noPan=false;this.staticMoving=false;this.dynamicDampingFactor=.2;this.minDistance=0;this.maxDistance=Infinity;this.keys=[65,83,68];this.target=new O.Vector3;var o=1e-6;var s=new O.Vector3;var c=i.NONE,l=i.NONE,u=new O.Vector3,f=new O.Vector2,p=new O.Vector2,d=new O.Vector3,h=0,v=new O.Vector2,m=new O.Vector2,g=0,y=0,b=new O.Vector2,w=new O.Vector2;this.target0=this.target.clone();this.position0=this.object.position.clone();this.up0=this.object.up.clone();var S={type:"change"};var x={type:"start"};var _={type:"end"};this.handleResize=function(){if(this.domElement===document){this.screen.left=0;this.screen.top=0;this.screen.width=window.innerWidth;this.screen.height=window.innerHeight}else{var e=this.domElement.getBoundingClientRect();var t=this.domElement.ownerDocument.documentElement;this.screen.left=e.left+window.pageXOffset-t.clientLeft;this.screen.top=e.top+window.pageYOffset-t.clientTop;this.screen.width=e.width;this.screen.height=e.height}};this.handleEvent=function(e){if(typeof this[e.type]=="function"){this[e.type](e)}};var C=function(){var e=new O.Vector2;return function t(r,n){e.set((r-a.screen.left)/a.screen.width,(n-a.screen.top)/a.screen.height);return e}}();var P=function(){var e=new O.Vector2;return function t(r,n){e.set((r-a.screen.width*.5-a.screen.left)/(a.screen.width*.5),(a.screen.height+2*(a.screen.top-n))/a.screen.width);return e}}();this.rotateCamera=function(){var e=new O.Vector3,t=new O.Quaternion,r=new O.Vector3,n=new O.Vector3,i=new O.Vector3,o=new O.Vector3,s;var c=0;function l(){var l=o.clone();o.set(p.x-f.x,p.y-f.y,0);s=o.length();var v=30;var m=false;c+=s*(e.y>0?1:-1);var g=c*(180/Math.PI);g=Math.max(-v,Math.min(g,v));if(s){u.copy(a.object.position).sub(a.target);r.copy(u).normalize();n.copy(a.object.up).normalize();i.crossVectors(n,r).normalize();n.setLength(p.y-f.y);i.setLength(p.x-f.x);if(this.locked){o.copy(i)}else{o.copy(n.add(i))}e.crossVectors(o,u).normalize();t.setFromAxisAngle(e,s);u.applyQuaternion(t);if(!this.locked)a.object.up.applyQuaternion(t);d.copy(e);h=s}else if(!a.staticMoving&&h){h*=Math.sqrt(1-a.dynamicDampingFactor);u.copy(a.object.position).sub(a.target);t.setFromAxisAngle(d,h);u.applyQuaternion(t);a.object.up.applyQuaternion(t)}f.copy(p)}return l}();this.zoomCamera=function(){var e;if(c===i.TOUCH_ZOOM_PAN){e=g/y;g=y;u.multiplyScalar(e)}else{e=1+(m.y-v.y)*a.zoomSpeed;if(e!==1&&e>0){u.multiplyScalar(e)}if(a.staticMoving){v.copy(m)}else{v.y+=(m.y-v.y)*this.dynamicDampingFactor}}};this.panCamera=function(){var e=new O.Vector2,t=new O.Vector3,r=new O.Vector3;return function n(){e.copy(w).sub(b);if(e.lengthSq()){e.multiplyScalar(u.length()*a.panSpeed);r.copy(u).cross(a.object.up).setLength(e.x);r.add(t.copy(a.object.up).setLength(e.y));a.object.position.add(r);a.target.add(r);if(a.staticMoving){b.copy(w)}else{b.add(e.subVectors(w,b).multiplyScalar(a.dynamicDampingFactor))}}}}();this.checkDistances=function(){if(!a.noZoom||!a.noPan){if(u.lengthSq()>a.maxDistance*a.maxDistance){a.object.position.addVectors(a.target,u.setLength(a.maxDistance));v.copy(m)}if(u.lengthSq()<a.minDistance*a.minDistance){a.object.position.addVectors(a.target,u.setLength(a.minDistance));v.copy(m)}}};this.update=function(){u.subVectors(a.object.position,a.target);if(!a.noRotate){a.rotateCamera()}if(!a.noZoom){a.zoomCamera()}if(!a.noPan){a.panCamera()}a.object.position.addVectors(a.target,u);a.checkDistances();a.object.lookAt(a.target);if(s.distanceToSquared(a.object.position)>o){a.dispatchEvent(S);s.copy(a.object.position)}};this.reset=function(){c=i.NONE;l=i.NONE;a.target.copy(a.target0);a.object.position.copy(a.position0);a.object.up.copy(a.up0);u.subVectors(a.object.position,a.target);a.object.lookAt(a.target);a.dispatchEvent(S);s.copy(a.object.position)};function E(e,t){if(Array.isArray(e)){return e.indexOf(t)!==-1}else{return e===t}}function A(e){if(a.enabled===false)return;window.removeEventListener("keydown",A);l=c;if(c!==i.NONE){}else if(E(a.keys[i.ROTATE],e.keyCode)&&!a.noRotate){c=i.ROTATE}else if(E(a.keys[i.ZOOM],e.keyCode)&&!a.noZoom){c=i.ZOOM}else if(E(a.keys[i.PAN],e.keyCode)&&!a.noPan){c=i.PAN}}function T(e){if(a.enabled===false)return;c=l;window.addEventListener("keydown",A,false)}function k(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();if(c===i.NONE){c=e.button}if(c===i.ROTATE&&!a.noRotate){p.copy(P(e.pageX,e.pageY));f.copy(p)}else if(c===i.ZOOM&&!a.noZoom){v.copy(C(e.pageX,e.pageY));m.copy(v)}else if(c===i.PAN&&!a.noPan){b.copy(C(e.pageX,e.pageY));w.copy(b)}document.addEventListener("mousemove",$,false);document.addEventListener("mouseup",j,false);a.dispatchEvent(x)}function $(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();if(c===i.ROTATE&&!a.noRotate){f.copy(p);p.copy(P(e.pageX,e.pageY))}else if(c===i.ZOOM&&!a.noZoom){m.copy(C(e.pageX,e.pageY))}else if(c===i.PAN&&!a.noPan){w.copy(C(e.pageX,e.pageY))}}function j(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();c=i.NONE;document.removeEventListener("mousemove",$);document.removeEventListener("mouseup",j);a.dispatchEvent(_)}function I(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();switch(e.deltaMode){case 2:v.y-=e.deltaY*.025;break;case 1:v.y-=e.deltaY*.01;break;default:v.y-=e.deltaY*25e-5;break}a.dispatchEvent(x);a.dispatchEvent(_)}function M(e){if(a.enabled===false)return;switch(e.touches.length){case 1:c=i.TOUCH_ROTATE;p.copy(P(e.touches[0].pageX,e.touches[0].pageY));f.copy(p);break;default:c=i.TOUCH_ZOOM_PAN;var t=e.touches[0].pageX-e.touches[1].pageX;var r=e.touches[0].pageY-e.touches[1].pageY;y=g=Math.sqrt(t*t+r*r);var n=(e.touches[0].pageX+e.touches[1].pageX)/2;var o=(e.touches[0].pageY+e.touches[1].pageY)/2;b.copy(C(n,o));w.copy(b);break}a.dispatchEvent(x)}function R(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();switch(e.touches.length){case 1:f.copy(p);p.copy(P(e.touches[0].pageX,e.touches[0].pageY));break;default:var t=e.touches[0].pageX-e.touches[1].pageX;var r=e.touches[0].pageY-e.touches[1].pageY;y=Math.sqrt(t*t+r*r);var n=(e.touches[0].pageX+e.touches[1].pageX)/2;var i=(e.touches[0].pageY+e.touches[1].pageY)/2;w.copy(C(n,i));break}}function D(e){if(a.enabled===false)return;switch(e.touches.length){case 0:c=i.NONE;break;case 1:c=i.TOUCH_ROTATE;p.copy(P(e.touches[0].pageX,e.touches[0].pageY));f.copy(p);break}a.dispatchEvent(_)}function L(e){if(a.enabled===false)return;e.preventDefault()}this.dispose=function(){this.domElement.removeEventListener("contextmenu",L,false);this.domElement.removeEventListener("mousedown",k,false);this.domElement.removeEventListener("wheel",I,false);this.domElement.removeEventListener("touchstart",M,false);this.domElement.removeEventListener("touchend",D,false);this.domElement.removeEventListener("touchmove",R,false);document.removeEventListener("mousemove",$,false);document.removeEventListener("mouseup",j,false);window.removeEventListener("keydown",A,false);window.removeEventListener("keyup",T,false)};this.domElement.addEventListener("contextmenu",L,false);this.domElement.addEventListener("mousedown",k,false);this.domElement.addEventListener("wheel",I,false);this.domElement.addEventListener("touchstart",M,false);this.domElement.addEventListener("touchend",D,false);this.domElement.addEventListener("touchmove",R,false);window.addEventListener("keydown",A,false);window.addEventListener("keyup",T,false);this.handleResize();this.update()};function j(e){e.preventDefault()}$.prototype=Object.create(O.EventDispatcher.prototype);r.d(t,"a",function(){return F});window.THREE=d;var I=window.location.href.indexOf("localhost")>-1?"":"/r_static/webdesigner/";var M=r("7a69");var R=r("c9c8");var D=r("1bf1");var L=function e(){this.horizontals=true;this.verticals=true;this.supports=true;this.backs=true;this.doors=true;this.drawers=true;this.fills=true;this.legs=true;this.accessories=true;this.spacer=true;this.colorMode=0;this.filterMaterialKey="";this.filterMaterial="";this.filterEnable=false};var z=new L;var F=function(){function e(t){var r=t.container,n=t.width,a=t.height,i=t.cameraLock,o=t.updatedCameraCallback;u()(this,e);this.controls=null;this.cameraLock=i;this.container=r;if(r)this.init(n,a)}p()(e,[{key:"updatedCamera",value:function e(){console.log("camera-updates");if(this.updatedCameraCallbackFunction)this.updatedCameraCallbackFunction()}},{key:"set",value:function e(t){var r=t.container,n=t.width,a=t.height,i=t.cameraLock,o=t.updatedCameraCallback;this.cameraLock=i;this.container=r;this.init(n,a);this.resize({width:n,height:a});this.updatedCameraCallbackFunction=o;return this}},{key:"resize",value:function e(t){var r=t.width,n=t.height;this.camera.aspect=r/n;this.camera.updateProjectionMatrix();this.renderer.setSize(r,n);this.renderer.domElement.style.width="".concat(r,"px");this.renderer.domElement.style.height="".concat(n,"px")}},{key:"rotateScene",value:function e(t){var r=t.angle;console.log(r)}},{key:"create",value:function e(t,r,n){if(t==null)return this.flush();var a=this.filterElements(t.item.elements,z);this.drawElements(a,r,n);this.render()}},{key:"flush",value:function e(){this.resetItems()}},{key:"addInfo",value:function e(t){var r=this;var n=new d["TextureLoader"];return new Promise(function(e){R(I+"fonts/inter-msdf/font.json",function(a,i){var o=M({width:300,align:"left",font:i,multipage:true});o.update(t);n.load(I+"fonts/inter-msdf/sheet0.png",function(t){var n=new d["RawShaderMaterial"](D({map:t,precision:"highp",alphaTest:1e-8,side:d["DoubleSide"],transparent:true,color:"rgb(230, 230, 230)"}));var a=new d["Mesh"](o,n);r.scene.add(a);a.scale.set(1,-1,1);e(a)})})})}},{key:"fitCameraToObject",value:function e(t,r,n,a){n=n||1.25;var i=new d["Box3"];i.setFromObject(r);var o=i.getCenter();var s=i.getSize();var c=Math.max(s.x,s.y,s.z);var l=t.fov*(Math.PI/180);var u=Math.abs(c/4*Math.tan(l*2));u*=n;t.position.z=u;var f=i.min.z;var p=f<0?-f+u:u-f;t.far=p*3;t.updateProjectionMatrix();if(a){a.target=o;a.maxDistance=p*2;a.saveState()}else{t.lookAt(o)}}},{key:"fitOrtho",value:function e(){var t=(new d["Box3"]).setFromObject(this.scene);var r=this.canvasAbsoluteHeight;var n=this.canvasAbsoluteWidth;var a=new d["OrthographicCamera"](n/-2,n/2,r/2,r/-2,0,100);var t=(new d["Box3"]).setFromObject(this.scene);this.scene.position.multiplyScalar(-1);a.zoom=Math.min(n/(t.max.x-t.min.x),r/(t.max.y-t.min.y))*.9;a.updateProjectionMatrix();a.updateMatrix();return a}},{key:"init",value:function e(t,r){var n=this;var a=this.container;if(this.renderer){a.appendChild(this.renderer.domElement);return}var i,o;var s,c;this.canvasAbsoluteHeight=c=r;this.canvasAbsoluteWidth=s=t;var l=new d["WebGLRenderer"]({antialias:true,preserveDrawingBuffer:true,alpha:true});this.renderer=l;l.setPixelRatio(window.devicePixelRatio);l.setSize(s,c);a.appendChild(l.domElement);this.camera=i=new d["PerspectiveCamera"](20,t/r,1,2e4);i.position.z=12e3;i.position.x=400;i.position.y=800;var u=this.controls=new $(this.camera,l.domElement,this.cameraLock);u.rotateSpeed=1;u.zoomSpeed=1.2;u.panSpeed=.8;u.noZoom=false;u.noPan=false;u.staticMoving=true;u.dynamicDampingFactor=.3;u.target=new d["Vector3"](200,250,0);u.addEventListener("change",function(){n.updatedCamera()});this.scene=o=new d["Object3D"];this._scene=new d["Scene"];this._scene.add(this.scene);o.background=new d["Color"](1052688);var f=new d["GridHelper"](4400,100,7829367,4473924);f.position.y=0;f.position.x=0;o.add(f);o.rotation.y=.5;this.items=[];this.labels=[];var p=function e(){n.controls.update();n.render();requestAnimationFrame(e)};p()}},{key:"filterElements",value:function e(t,r){return t.filter(function(e){return e["elem_type"]==="H"&&r["horizontals"]||e["elem_type"]==="V"&&r["verticals"]||e["elem_type"]==="S"&&r["supports"]||e["elem_type"]==="B"&&r["backs"]||e["elem_type"]==="D"&&r["doors"]||e["elem_type"]==="O"||e["elem_type"]==="M"||e["elem_type"]==="L"&&r["legs"]||e["elem_type"]==="T"&&r["drawers"]||e["elem_type"]==="FILL"&&r["fills"]||e["elem_type"]==="ACC"&&r["accessories"]||e["elem_type"]==="SPACER"&&r["spacer"]})}},{key:"render",value:function e(){this.renderer.render(this._scene,this.camera)}},{key:"getCompoundBoundingBox",value:function e(t){var r=null;t.traverse(function(e){var t=e.geometry;if(t===undefined)return;t.computeBoundingBox();if(r===null){r=t.boundingBox}else{r.union(t.boundingBox)}});return r}},{key:"selectElement",value:function e(t){this.groups.map(function(e,r){e.element.visible=r==t})}},{key:"drawElements",value:function e(t,r,n){var a=this;this.resetItems();var i=this.scene;var o=this.items;var s=this.labels;this.groups=[];var c=function e(c){var l=t[c];if(l.components==null){return"continue"}var y=[(l.x_domain[1]+l.x_domain[0])/200,(l.y_domain[1]+l.y_domain[0])/200,(l.z_domain[1]+l.z_domain[0])/200];var b=[(l.x_domain[1]-l.x_domain[0])/100,(l.y_domain[1]-l.y_domain[0])/100,(l.z_domain[1]-l.z_domain[0])/100];for(var w=0;w<Object.values(l.components).length;w++){var S=Object.values(l.components)[w];if(z.filterEnable==true){if(!((S[z.filterMaterialKey]||"missing").toString().indexOf(z.filterMaterial)>-1)){continue}}var x=[(S.x_domain[1]-S.x_domain[0])/100,(S.y_domain[1]-S.y_domain[0])/100,(S.z_domain[1]-S.z_domain[0])/100];var _=[(S.x_domain[1]+S.x_domain[0])/200,(S.y_domain[1]+S.y_domain[0])/200,(S.z_domain[1]+S.z_domain[0])/200];u=l.elem_type=="L"?new d["CylinderGeometry"](x[0]/2,x[0]/2,x[1],12,2):new d["BoxGeometry"](x[0],x[1],x[2]);f=new d["MeshBasicMaterial"]({color:10066329,wireframe:false,transparent:true,polygonOffset:true,polygonOffsetFactor:1,polygonOffsetUnits:1,opacity:n?.95:.5});p=new d["Mesh"](u,f);p.position.x=_[0];p.position.y=_[1];p.position.z=_[2];p.item_data=l;h=new d["EdgesGeometry"](p.geometry);v=new d["LineBasicMaterial"]({color:r?11184810:16777215,linewidth:1});m=new d["LineSegments"](h,n?f:v);p.add(m);i.add(p);o.push(p);if(!a.groups[l.c_config_id]){a.groups[l.c_config_id]={group:new d["Group"],pos:new d["Vector3"]}}a.groups[l.c_config_id].group.add(p.clone());a.groups[l.c_config_id].pos.x=_[0];a.groups[l.c_config_id].pos.y=_[1];a.groups[l.c_config_id].pos.z=_[2]}if(l.surname!=""){g=a.makeTextSprite("".concat(l.surname),{fontsize:70,borderColor:{r:255,g:0,b:0,a:1},backgroundColor:{r:255,g:100,b:100,a:.2}});a.addInfo("".concat(l.surname)).then(function(e){e.position.set(y[0]-150,y[1],y[2]+b[2]/2);i.add(e);s.push(e)})}};for(var l=0;l<t.length;l++){var u;var f;var p;var h;var v;var m;var g;var y=c(l);if(y==="continue")continue}this.groups.map(function(e,t){var r=new d["MeshBasicMaterial"]({color:20991,wireframe:false,transparent:true,polygonOffset:true,polygonOffsetFactor:1,polygonOffsetUnits:1,opacity:.65});var n=a.getCompoundBoundingBox(e.group);var s=a.getCompoundBoundingBox(e.group);var c=new d["Vector3"];var l=new d["Vector3"];var u=new d["Vector3"];s.expandByScalar(25);s.getSize(l);n.getSize(c);var f=new d["BoxGeometry"](l.x,l.y,l.z);var p=new d["Mesh"](f,r);p.visible=false;p.position.x=e.pos.x;p.position.y=e.pos.y-c.y/2+15;p.position.z=e.pos.z;i.add(p);o.push(p);e.element=p})}},{key:"resetScene",value:function e(t){if(t){this._scene.background=new d["Color"](15790320);this._scene.opacity=1}else{this._scene.background=new d["Color"](1052688);this._scene.opacity=.5}var r=new d["GridHelper"](4400,100,7829367,4473924);r.position.y=0;r.position.x=0;this.scene.add(r)}},{key:"resetItems",value:function e(){var t=this.scene;var r=this.labels;var n=this.groups?this.groups:[];for(var a=0;a<this.items.length;a++){t.remove(this.items[a])}this.items=[];for(var i=0;i<this.labels.length;i++){t.remove(this.labels[i])}this.labels=[];for(var o=0;o<n.length;o++){if(n[o])t.remove(n[o].group)}this.groups=[]}},{key:"resetCamera",value:function e(){this.controls.reset()}},{key:"makeTextSprite",value:function e(t,r){if(r===undefined)r={};var n=r.hasOwnProperty("fontface")?r["fontface"]:"Arial";var a=r.hasOwnProperty("fontsize")?r["fontsize"]:18;var i=r.hasOwnProperty("borderThickness")?r["borderThickness"]:4;var o=r.hasOwnProperty("borderColor")?r["borderColor"]:{r:0,g:0,b:0,a:1};var s=r.hasOwnProperty("backgroundColor")?r["backgroundColor"]:{r:255,g:255,b:255,a:1};var c=document.createElement("canvas");if(t.length>10){c.width=t.length*35}var l=c.getContext("2d");l.font="Bold "+a+"px "+n;var u=l.measureText(t);var f=u.width;l.fillStyle="rgba("+s.r+","+s.g+","+s.b+","+s.a+")";l.strokeStyle="rgba("+o.r+","+o.g+","+o.b+","+o.a+")";l.lineWidth=i;l.fillStyle="rgba(255, 255, 2555, 1.0)";l.fillText(t,i,a+i);var p=new d["Texture"](c);p.needsUpdate=true;var h=new d["SpriteMaterial"]({map:p});var v=new d["Sprite"](h);v.scale.set(c.width==300?200:450,c.width==300?100:.016*c.width,1);return v}}]);return e}();var B=new F({})},"888e":function(e,t,r){"use strict";var n=r("de6d");var a=r.n(n);var i=a.a},8895:function(e,t,r){},"88a0":function(e,t,r){var n=r("7831");var a=r("5882");var i=r("044b");var o=[0,2,3];var s=[2,1,3];e.exports=function e(t,r){if(!t||!(a(t)||i(t))){r=t||{};t=null}if(typeof r==="number")r={count:r};else r=r||{};var c=typeof r.type==="string"?r.type:"uint16";var l=typeof r.count==="number"?r.count:1;var u=r.start||0;var f=r.clockwise!==false?o:s,p=f[0],d=f[1],h=f[2];var v=l*6;var m=t||new(n(c))(v);for(var g=0,y=0;g<v;g+=6,y+=4){var b=g+u;m[b+0]=y+0;m[b+1]=y+1;m[b+2]=y+2;m[b+3]=y+p;m[b+4]=y+d;m[b+5]=y+h}return m}},"895e":function(e,t,r){"use strict";var n=r("d8ab");var a=r.n(n);var i=a.a},"8f6d":function(e,t){e.exports=function e(t,r){return typeof t==="number"?t:typeof r==="number"?r:0}},a0d3:function(e,t,r){"use strict";var n=r("0f7c");e.exports=n.call(Function.call,Object.prototype.hasOwnProperty)},a3a2:function(e,t){e.exports.pages=function e(t){var e=new Float32Array(t.length*4*1);var r=0;t.forEach(function(t){var n=t.data.page||0;e[r++]=n;e[r++]=n;e[r++]=n;e[r++]=n});return e};e.exports.uvs=function e(t,r,n,a){var e=new Float32Array(t.length*4*2);var i=0;t.forEach(function(t){var o=t.data;var s=o.x+o.width;var c=o.y+o.height;var l=o.x/r;var u=o.y/n;var f=s/r;var p=c/n;if(a){u=(n-o.y)/n;p=(n-c)/n}e[i++]=l;e[i++]=u;e[i++]=l;e[i++]=p;e[i++]=f;e[i++]=p;e[i++]=f;e[i++]=u});return e};e.exports.positions=function e(t){var e=new Float32Array(t.length*4*2);var r=0;t.forEach(function(t){var n=t.data;var a=t.position[0]+n.xoffset;var i=t.position[1]+n.yoffset;var o=n.width;var s=n.height;e[r++]=a;e[r++]=i;e[r++]=a;e[r++]=i+s;e[r++]=a+o;e[r++]=i+s;e[r++]=a+o;e[r++]=i});return e}},ac1b:function(e,t,r){},ac6a9:function(e,t,r){"use strict";var n=r("fce8");var a=r.n(n);var i=a.a},b189:function(e,t,r){"use strict";var n;if(!Object.keys){var a=Object.prototype.hasOwnProperty;var i=Object.prototype.toString;var o=r("d4ab");var s=Object.prototype.propertyIsEnumerable;var c=!s.call({toString:null},"toString");var l=s.call(function(){},"prototype");var u=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"];var f=function(e){var t=e.constructor;return t&&t.prototype===e};var p={$applicationCache:true,$console:true,$external:true,$frame:true,$frameElement:true,$frames:true,$innerHeight:true,$innerWidth:true,$outerHeight:true,$outerWidth:true,$pageXOffset:true,$pageYOffset:true,$parent:true,$scrollLeft:true,$scrollTop:true,$scrollX:true,$scrollY:true,$self:true,$webkitIndexedDB:true,$webkitStorageInfo:true,$window:true};var d=function(){if(typeof window==="undefined"){return false}for(var e in window){try{if(!p["$"+e]&&a.call(window,e)&&window[e]!==null&&typeof window[e]==="object"){try{f(window[e])}catch(t){return true}}}catch(t){return true}}return false}();var h=function(e){if(typeof window==="undefined"||!d){return f(e)}try{return f(e)}catch(t){return false}};n=function e(t){var r=t!==null&&typeof t==="object";var n=i.call(t)==="[object Function]";var s=o(t);var f=r&&i.call(t)==="[object String]";var p=[];if(!r&&!n&&!s){throw new TypeError("Object.keys called on a non-object")}var d=l&&n;if(f&&t.length>0&&!a.call(t,0)){for(var v=0;v<t.length;++v){p.push(String(v))}}if(s&&t.length>0){for(var m=0;m<t.length;++m){p.push(String(m))}}else{for(var g in t){if(!(d&&g==="prototype")&&a.call(t,g)){p.push(String(g))}}}if(c){var y=h(t);for(var b=0;b<u.length;++b){if(!(y&&u[b]==="constructor")&&a.call(t,u[b])){p.push(u[b])}}}return p}}e.exports=n},b3fd:function(e,t,r){"use strict";var n=r("f367");var a=r("1b7f");e.exports=function e(){var t=a();n(String.prototype,{trim:t},{trim:function(){return String.prototype.trim!==t}});return t}},bb53:function(e,t){e.exports=function e(t,r){var n=t%r;return Math.floor(n>=0?n:n+r)}},be09:function(e,t,r){(function(t){var r;if(typeof window!=="undefined"){r=window}else if(typeof t!=="undefined"){r=t}else if(typeof self!=="undefined"){r=self}else{r={}}e.exports=r}).call(this,r("c8ba"))},c46d:function(e,t,r){"use strict";var n=r("e9ac");var a=n("%TypeError%");var i=n("%SyntaxError%");var o=r("a0d3");var s={"Property Descriptor":function e(t,r){if(t.Type(r)!=="Object"){return false}var n={"[[Configurable]]":true,"[[Enumerable]]":true,"[[Get]]":true,"[[Set]]":true,"[[Value]]":true,"[[Writable]]":true};for(var i in r){if(o(r,i)&&!n[i]){return false}}var s=o(r,"[[Value]]");var c=o(r,"[[Get]]")||o(r,"[[Set]]");if(s&&c){throw new a("Property Descriptors may not be both accessor and data descriptors")}return true}};e.exports=function e(t,r,n,o){var c=s[r];if(typeof c!=="function"){throw new i("unknown record type: "+r)}if(!c(t,o)){throw new a(n+" must be a "+r)}console.log(c(t,o),o)}},c498:function(e,t,r){"use strict";var n=r("2e69");var a=r.n(n);var i=a.a},c612:function(e,t){var r=Number.isNaN||function(e){return e!==e};e.exports=Number.isFinite||function(e){return typeof e==="number"&&!r(e)&&e!==Infinity&&e!==-Infinity}},c69a6:function(e,t){var r=[66,77,70];e.exports=function e(t){if(t.length<6)throw new Error("invalid buffer length for BMFont");var a=r.every(function(e,r){return t.readUInt8(r)===e});if(!a)throw new Error("BMFont missing BMF byte header");var i=3;var o=t.readUInt8(i++);if(o>3)throw new Error("Only supports BMFont Binary v3 (BMFont App v1.10)");var s={kernings:[],chars:[]};for(var c=0;c<5;c++)i+=n(s,t,i);return s};function n(e,t,r){if(r>t.length-1)return 0;var n=t.readUInt8(r++);var l=t.readInt32LE(r);r+=4;switch(n){case 1:e.info=a(t,r);break;case 2:e.common=i(t,r);break;case 3:e.pages=o(t,r,l);break;case 4:e.chars=s(t,r,l);break;case 5:e.kernings=c(t,r,l);break}return 5+l}function a(e,t){var r={};r.size=e.readInt16LE(t);var n=e.readUInt8(t+2);r.smooth=n>>7&1;r.unicode=n>>6&1;r.italic=n>>5&1;r.bold=n>>4&1;if(n>>3&1)r.fixedHeight=1;r.charset=e.readUInt8(t+3)||"";r.stretchH=e.readUInt16LE(t+4);r.aa=e.readUInt8(t+6);r.padding=[e.readInt8(t+7),e.readInt8(t+8),e.readInt8(t+9),e.readInt8(t+10)];r.spacing=[e.readInt8(t+11),e.readInt8(t+12)];r.outline=e.readUInt8(t+13);r.face=u(e,t+14);return r}function i(e,t){var r={};r.lineHeight=e.readUInt16LE(t);r.base=e.readUInt16LE(t+2);r.scaleW=e.readUInt16LE(t+4);r.scaleH=e.readUInt16LE(t+6);r.pages=e.readUInt16LE(t+8);var n=e.readUInt8(t+10);r.packed=0;r.alphaChnl=e.readUInt8(t+11);r.redChnl=e.readUInt8(t+12);r.greenChnl=e.readUInt8(t+13);r.blueChnl=e.readUInt8(t+14);return r}function o(e,t,r){var n=[];var a=l(e,t);var i=a.length+1;var o=r/i;for(var s=0;s<o;s++){n[s]=e.slice(t,t+a.length).toString("utf8");t+=i}return n}function s(e,t,r){var n=[];var a=r/20;for(var i=0;i<a;i++){var o={};var s=i*20;o.id=e.readUInt32LE(t+0+s);o.x=e.readUInt16LE(t+4+s);o.y=e.readUInt16LE(t+6+s);o.width=e.readUInt16LE(t+8+s);o.height=e.readUInt16LE(t+10+s);o.xoffset=e.readInt16LE(t+12+s);o.yoffset=e.readInt16LE(t+14+s);o.xadvance=e.readInt16LE(t+16+s);o.page=e.readUInt8(t+18+s);o.chnl=e.readUInt8(t+19+s);n[i]=o}return n}function c(e,t,r){var n=[];var a=r/10;for(var i=0;i<a;i++){var o={};var s=i*10;o.first=e.readUInt32LE(t+0+s);o.second=e.readUInt32LE(t+4+s);o.amount=e.readInt16LE(t+8+s);n[i]=o}return n}function l(e,t){var r=t;for(;r<e.length;r++){if(e[r]===0)break}return e.slice(t,r)}function u(e,t){return l(e,t).toString("utf8")}},c9c8:function(e,t,r){(function(t){var n=r("eec7");var a=function(){};var i=r("7ece");var o=r("fad7");var s=r("c69a6");var c=r("04a2");var l=r("53a8");var u=function e(){return self.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}();e.exports=function(e,r){r=typeof r==="function"?r:a;if(typeof e==="string")e={uri:e};else if(!e)e={};var l=e.binary;if(l)e=p(e);n(e,function(n,l,u){if(n)return r(n);if(!/^2/.test(l.statusCode))return r(new Error("http status code: "+l.statusCode));if(!u)return r(new Error("no body result"));var p=false;if(f(u)){var d=new Uint8Array(u);u=new t(d,"binary")}if(c(u)){p=true;if(typeof u==="string")u=new t(u,"binary")}if(!p){if(t.isBuffer(u))u=u.toString(e.encoding);u=u.trim()}var h;try{var v=l.headers["content-type"];if(p)h=s(u);else if(/json/.test(v)||u.charAt(0)==="{")h=JSON.parse(u);else if(/xml/.test(v)||u.charAt(0)==="<")h=o(u);else h=i(u)}catch(m){r(new Error("error parsing font "+m.message));r=a}r(null,h)})};function f(e){var t=Object.prototype.toString;return t.call(e)==="[object ArrayBuffer]"}function p(e){if(u)return l(e,{responseType:"arraybuffer"});if(typeof self.XMLHttpRequest==="undefined")throw new Error("your browser does not support XHR loading");var t=new self.XMLHttpRequest;t.overrideMimeType("text/plain; charset=x-user-defined");return l({xhr:t},e)}}).call(this,r("b639").Buffer)},ca9f:function(e,t,r){"use strict";var n=r("0f7c");var a=r("f367");var i=r("562e");var o=r("1b7f");var s=r("b3fd");var c=n.call(Function.call,o());a(c,{getPolyfill:o,implementation:i,shim:s});e.exports=c},d024:function(e,t,r){"use strict";var n=r("21d0");var a=Object.prototype.toString;var i=Object.prototype.hasOwnProperty;var o=function e(t,r,n){for(var a=0,o=t.length;a<o;a++){if(i.call(t,a)){if(n==null){r(t[a],a,t)}else{r.call(n,t[a],a,t)}}}};var s=function e(t,r,n){for(var a=0,i=t.length;a<i;a++){if(n==null){r(t.charAt(a),a,t)}else{r.call(n,t.charAt(a),a,t)}}};var c=function e(t,r,n){for(var a in t){if(i.call(t,a)){if(n==null){r(t[a],a,t)}else{r.call(n,t[a],a,t)}}}};var l=function e(t,r,i){if(!n(r)){throw new TypeError("iterator must be a function")}var l;if(arguments.length>=3){l=i}if(a.call(t)==="[object Array]"){o(t,r,l)}else if(typeof t==="string"){s(t,r,l)}else{c(t,r,l)}};e.exports=l},d4ab:function(e,t,r){"use strict";var n=Object.prototype.toString;e.exports=function e(t){var r=n.call(t);var a=r==="[object Arguments]";if(!a){a=r!=="[object Array]"&&t!==null&&typeof t==="object"&&typeof t.length==="number"&&t.length>=0&&n.call(t.callee)==="[object Function]"}return a}},d6c7:function(e,t,r){"use strict";var n=Array.prototype.slice;var a=r("d4ab");var i=Object.keys;var o=i?function e(t){return i(t)}:r("b189");var s=Object.keys;o.shim=function e(){if(Object.keys){var t=function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2);if(!t){Object.keys=function e(t){if(a(t)){return s(n.call(t))}return s(t)}}}else{Object.keys=o}return Object.keys||o};e.exports=o},d8ab:function(e,t,r){},de6d:function(e,t,r){},e67d:function(e,t){function r(e){if(typeof e.value!=="function"){console.warn("[Vue-click-outside:] provided expression",e.expression,"is not a function.");return false}return true}function n(e,t){if(!e||!t)return false;for(var r=0,n=t.length;r<n;r++){try{if(e.contains(t[r])){return true}if(t[r].contains(e)){return false}}catch(a){return false}}return false}function a(e){return typeof e.componentInstance!=="undefined"&&e.componentInstance.$isServer}t=e.exports={bind:function(e,t,i){if(!r(t))return;function o(t){if(!i.context)return;var r=t.path||t.composedPath&&t.composedPath();r&&r.length>0&&r.unshift(t.target);if(e.contains(t.target)||n(i.context.popupItem,r))return;e.__vueClickOutside__.callback(t)}e.__vueClickOutside__={handler:o,callback:t.value};!a(i)&&document.addEventListener("click",o)},update:function(e,t){if(r(t))e.__vueClickOutside__.callback=t.value},unbind:function(e,t,r){!a(r)&&document.removeEventListener("click",e.__vueClickOutside__.handler);delete e.__vueClickOutside__}}},e797:function(e,t,r){"use strict";var n=r("778a");var a=r.n(n);var i=a.a},e9ac:function(e,t,r){"use strict";var n;var a=Object.getOwnPropertyDescriptor?function(){return Object.getOwnPropertyDescriptor(arguments,"callee").get}():function(){throw new TypeError};var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol";var o=Object.getPrototypeOf||function(e){return e.__proto__};var s;var c=s?o(s):n;var l;var u=l?l.constructor:n;var f;var p=f?o(f):n;var d=f?f():n;var h=typeof Uint8Array==="undefined"?n:o(Uint8Array);var v={"$ %Array%":Array,"$ %ArrayBuffer%":typeof ArrayBuffer==="undefined"?n:ArrayBuffer,"$ %ArrayBufferPrototype%":typeof ArrayBuffer==="undefined"?n:ArrayBuffer.prototype,"$ %ArrayIteratorPrototype%":i?o([][Symbol.iterator]()):n,"$ %ArrayPrototype%":Array.prototype,"$ %ArrayProto_entries%":Array.prototype.entries,"$ %ArrayProto_forEach%":Array.prototype.forEach,"$ %ArrayProto_keys%":Array.prototype.keys,"$ %ArrayProto_values%":Array.prototype.values,"$ %AsyncFromSyncIteratorPrototype%":n,"$ %AsyncFunction%":u,"$ %AsyncFunctionPrototype%":u?u.prototype:n,"$ %AsyncGenerator%":f?o(d):n,"$ %AsyncGeneratorFunction%":p,"$ %AsyncGeneratorPrototype%":p?p.prototype:n,"$ %AsyncIteratorPrototype%":d&&i&&Symbol.asyncIterator?d[Symbol.asyncIterator]():n,"$ %Atomics%":typeof Atomics==="undefined"?n:Atomics,"$ %Boolean%":Boolean,"$ %BooleanPrototype%":Boolean.prototype,"$ %DataView%":typeof DataView==="undefined"?n:DataView,"$ %DataViewPrototype%":typeof DataView==="undefined"?n:DataView.prototype,"$ %Date%":Date,"$ %DatePrototype%":Date.prototype,"$ %decodeURI%":decodeURI,"$ %decodeURIComponent%":decodeURIComponent,"$ %encodeURI%":encodeURI,"$ %encodeURIComponent%":encodeURIComponent,"$ %Error%":Error,"$ %ErrorPrototype%":Error.prototype,"$ %eval%":eval,"$ %EvalError%":EvalError,"$ %EvalErrorPrototype%":EvalError.prototype,"$ %Float32Array%":typeof Float32Array==="undefined"?n:Float32Array,"$ %Float32ArrayPrototype%":typeof Float32Array==="undefined"?n:Float32Array.prototype,"$ %Float64Array%":typeof Float64Array==="undefined"?n:Float64Array,"$ %Float64ArrayPrototype%":typeof Float64Array==="undefined"?n:Float64Array.prototype,"$ %Function%":Function,"$ %FunctionPrototype%":Function.prototype,"$ %Generator%":s?o(s()):n,"$ %GeneratorFunction%":c,"$ %GeneratorPrototype%":c?c.prototype:n,"$ %Int8Array%":typeof Int8Array==="undefined"?n:Int8Array,"$ %Int8ArrayPrototype%":typeof Int8Array==="undefined"?n:Int8Array.prototype,"$ %Int16Array%":typeof Int16Array==="undefined"?n:Int16Array,"$ %Int16ArrayPrototype%":typeof Int16Array==="undefined"?n:Int8Array.prototype,"$ %Int32Array%":typeof Int32Array==="undefined"?n:Int32Array,"$ %Int32ArrayPrototype%":typeof Int32Array==="undefined"?n:Int32Array.prototype,"$ %isFinite%":isFinite,"$ %isNaN%":isNaN,"$ %IteratorPrototype%":i?o(o([][Symbol.iterator]())):n,"$ %JSON%":JSON,"$ %JSONParse%":JSON.parse,"$ %Map%":typeof Map==="undefined"?n:Map,"$ %MapIteratorPrototype%":typeof Map==="undefined"||!i?n:o((new Map)[Symbol.iterator]()),"$ %MapPrototype%":typeof Map==="undefined"?n:Map.prototype,"$ %Math%":Math,"$ %Number%":Number,"$ %NumberPrototype%":Number.prototype,"$ %Object%":Object,"$ %ObjectPrototype%":Object.prototype,"$ %ObjProto_toString%":Object.prototype.toString,"$ %ObjProto_valueOf%":Object.prototype.valueOf,"$ %parseFloat%":parseFloat,"$ %parseInt%":parseInt,"$ %Promise%":typeof Promise==="undefined"?n:Promise,"$ %PromisePrototype%":typeof Promise==="undefined"?n:Promise.prototype,"$ %PromiseProto_then%":typeof Promise==="undefined"?n:Promise.prototype.then,"$ %Promise_all%":typeof Promise==="undefined"?n:Promise.all,"$ %Promise_reject%":typeof Promise==="undefined"?n:Promise.reject,"$ %Promise_resolve%":typeof Promise==="undefined"?n:Promise.resolve,"$ %Proxy%":typeof Proxy==="undefined"?n:Proxy,"$ %RangeError%":RangeError,"$ %RangeErrorPrototype%":RangeError.prototype,"$ %ReferenceError%":ReferenceError,"$ %ReferenceErrorPrototype%":ReferenceError.prototype,"$ %Reflect%":typeof Reflect==="undefined"?n:Reflect,"$ %RegExp%":RegExp,"$ %RegExpPrototype%":RegExp.prototype,"$ %Set%":typeof Set==="undefined"?n:Set,"$ %SetIteratorPrototype%":typeof Set==="undefined"||!i?n:o((new Set)[Symbol.iterator]()),"$ %SetPrototype%":typeof Set==="undefined"?n:Set.prototype,"$ %SharedArrayBuffer%":typeof SharedArrayBuffer==="undefined"?n:SharedArrayBuffer,"$ %SharedArrayBufferPrototype%":typeof SharedArrayBuffer==="undefined"?n:SharedArrayBuffer.prototype,"$ %String%":String,"$ %StringIteratorPrototype%":i?o(""[Symbol.iterator]()):n,"$ %StringPrototype%":String.prototype,"$ %Symbol%":i?Symbol:n,"$ %SymbolPrototype%":i?Symbol.prototype:n,"$ %SyntaxError%":SyntaxError,"$ %SyntaxErrorPrototype%":SyntaxError.prototype,"$ %ThrowTypeError%":a,"$ %TypedArray%":h,"$ %TypedArrayPrototype%":h?h.prototype:n,"$ %TypeError%":TypeError,"$ %TypeErrorPrototype%":TypeError.prototype,"$ %Uint8Array%":typeof Uint8Array==="undefined"?n:Uint8Array,"$ %Uint8ArrayPrototype%":typeof Uint8Array==="undefined"?n:Uint8Array.prototype,"$ %Uint8ClampedArray%":typeof Uint8ClampedArray==="undefined"?n:Uint8ClampedArray,"$ %Uint8ClampedArrayPrototype%":typeof Uint8ClampedArray==="undefined"?n:Uint8ClampedArray.prototype,"$ %Uint16Array%":typeof Uint16Array==="undefined"?n:Uint16Array,"$ %Uint16ArrayPrototype%":typeof Uint16Array==="undefined"?n:Uint16Array.prototype,"$ %Uint32Array%":typeof Uint32Array==="undefined"?n:Uint32Array,"$ %Uint32ArrayPrototype%":typeof Uint32Array==="undefined"?n:Uint32Array.prototype,"$ %URIError%":URIError,"$ %URIErrorPrototype%":URIError.prototype,"$ %WeakMap%":typeof WeakMap==="undefined"?n:WeakMap,"$ %WeakMapPrototype%":typeof WeakMap==="undefined"?n:WeakMap.prototype,"$ %WeakSet%":typeof WeakSet==="undefined"?n:WeakSet,"$ %WeakSetPrototype%":typeof WeakSet==="undefined"?n:WeakSet.prototype};e.exports=function e(t,r){if(arguments.length>1&&typeof r!=="boolean"){throw new TypeError('"allowMissing" argument must be a boolean')}var n="$ "+t;if(!(n in v)){throw new SyntaxError("intrinsic "+t+" does not exist!")}if(typeof v[n]==="undefined"&&!r){throw new TypeError("intrinsic "+t+" exists, but is not available. Please file an issue!")}return v[n]}},eec7:function(e,t,r){"use strict";var n=r("be09");var a=r("8362");var i=r("6444");var o=r("53a8");e.exports=u;e.exports.default=u;u.XMLHttpRequest=n.XMLHttpRequest||d;u.XDomainRequest="withCredentials"in new u.XMLHttpRequest?u.XMLHttpRequest:n.XDomainRequest;s(["get","put","post","patch","head","delete"],function(e){u[e==="delete"?"del":e]=function(t,r,n){r=l(t,r,n);r.method=e.toUpperCase();return f(r)}});function s(e,t){for(var r=0;r<e.length;r++){t(e[r])}}function c(e){for(var t in e){if(e.hasOwnProperty(t))return false}return true}function l(e,t,r){var n=e;if(a(t)){r=t;if(typeof e==="string"){n={uri:e}}}else{n=o(t,{uri:e})}n.callback=r;return n}function u(e,t,r){t=l(e,t,r);return f(t)}function f(e){if(typeof e.callback==="undefined"){throw new Error("callback argument missing")}var t=false;var r=function r(n,a,i){if(!t){t=true;e.callback(n,a,i)}};function n(){if(l.readyState===4){setTimeout(s,0)}}function a(){var e=undefined;if(l.response){e=l.response}else{e=l.responseText||p(l)}if(b){try{e=JSON.parse(e)}catch(t){}}return e}function o(e){clearTimeout(w);if(!(e instanceof Error)){e=new Error(""+(e||"Unknown XMLHttpRequest Error"))}e.statusCode=0;return r(e,S)}function s(){if(d)return;var t;clearTimeout(w);if(e.useXDR&&l.status===undefined){t=200}else{t=l.status===1223?204:l.status}var n=S;var o=null;if(t!==0){n={body:a(),statusCode:t,method:v,headers:{},url:h,rawRequest:l};if(l.getAllResponseHeaders){n.headers=i(l.getAllResponseHeaders())}}else{o=new Error("Internal XMLHttpRequest Error")}return r(o,n,n.body)}var l=e.xhr||null;if(!l){if(e.cors||e.useXDR){l=new u.XDomainRequest}else{l=new u.XMLHttpRequest}}var f;var d;var h=l.url=e.uri||e.url;var v=l.method=e.method||"GET";var m=e.body||e.data;var g=l.headers=e.headers||{};var y=!!e.sync;var b=false;var w;var S={body:undefined,headers:{},statusCode:0,method:v,url:h,rawRequest:l};if("json"in e&&e.json!==false){b=true;g["accept"]||g["Accept"]||(g["Accept"]="application/json");if(v!=="GET"&&v!=="HEAD"){g["content-type"]||g["Content-Type"]||(g["Content-Type"]="application/json");m=JSON.stringify(e.json===true?m:e.json)}}l.onreadystatechange=n;l.onload=s;l.onerror=o;l.onprogress=function(){};l.onabort=function(){d=true};l.ontimeout=o;l.open(v,h,!y,e.username,e.password);if(!y){l.withCredentials=!!e.withCredentials}if(!y&&e.timeout>0){w=setTimeout(function(){if(d)return;d=true;l.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT";o(e)},e.timeout)}if(l.setRequestHeader){for(f in g){if(g.hasOwnProperty(f)){l.setRequestHeader(f,g[f])}}}else if(e.headers&&!c(e.headers)){throw new Error("Headers cannot be set on an XDomainRequest object")}if("responseType"in e){l.responseType=e.responseType}if("beforeSend"in e&&typeof e.beforeSend==="function"){e.beforeSend(l)}l.send(m||null);return l}function p(e){try{if(e.responseType==="document"){return e.responseXML}var t=e.responseXML&&e.responseXML.documentElement.nodeName==="parsererror";if(e.responseType===""&&!t){return e.responseXML}}catch(r){}return null}function d(){}},f111:function(e,t,r){"use strict";var n=r("7e4b");var a=r.n(n);var i=a.a},f1f5:function(e,t){var r="chasrset";e.exports=function e(t){if(r in t){t["charset"]=t[r];delete t[r]}for(var a in t){if(a==="face"||a==="charset")continue;else if(a==="padding"||a==="spacing")t[a]=n(t[a]);else t[a]=parseInt(t[a],10)}return t};function n(e){return e.split(",").map(function(e){return parseInt(e,10)})}},f202:function(e,t,r){var n=r("42b6");var a=r("53a8");var i=r("8f6d");var o=["x","e","a","o","n","s","r","c","u","m","v","w","z"];var s=["m","w"];var c=["H","I","N","E","F","K","L","T","U","V","W","X","Y","Z"];var l="\t".charCodeAt(0);var u=" ".charCodeAt(0);var f=0,p=1,d=2;e.exports=function e(t){return new h(t)};function h(e){this.glyphs=[];this._measure=this.computeMetrics.bind(this);this.update(e)}h.prototype.update=function(e){e=a({measure:this._measure},e);this._opt=e;this._opt.tabSize=i(this._opt.tabSize,4);if(!e.font)throw new Error("must provide a valid bitmap font");var t=this.glyphs;var r=e.text||"";var o=e.font;this._setupSpaceGlyphs(o);var s=n.lines(r,e);var c=e.width||0;t.length=0;var l=s.reduce(function(e,t){return Math.max(e,t.width,c)},0);var u=0;var f=0;var h=i(e.lineHeight,o.common.lineHeight);var v=o.common.base;var m=h-v;var g=e.letterSpacing||0;var b=h*s.length-m;var _=x(this._opt.align);f-=b;this._width=l;this._height=b;this._descender=h-v;this._baseline=v;this._xHeight=y(o);this._capHeight=w(o);this._lineHeight=h;this._ascender=h-m-this._xHeight;var C=this;s.forEach(function(e,n){var a=e.start;var i=e.end;var s=e.width;var c;for(var v=a;v<i;v++){var m=r.charCodeAt(v);var y=C.getGlyph(o,m);if(y){if(c)u+=S(o,c.id,y.id);var b=u;if(_===p)b+=(l-s)/2;else if(_===d)b+=l-s;t.push({position:[b,f],data:y,index:v,line:n});u+=y.xadvance+g;c=y}}f+=h;u=0});this._linesTotal=s.length};h.prototype._setupSpaceGlyphs=function(e){this._fallbackSpaceGlyph=null;this._fallbackTabGlyph=null;if(!e.chars||e.chars.length===0)return;var t=g(e,u)||b(e)||e.chars[0];var r=this._opt.tabSize*t.xadvance;this._fallbackSpaceGlyph=t;this._fallbackTabGlyph=a(t,{x:0,y:0,xadvance:r,id:l,xoffset:0,yoffset:0,width:0,height:0})};h.prototype.getGlyph=function(e,t){var r=g(e,t);if(r)return r;else if(t===l)return this._fallbackTabGlyph;else if(t===u)return this._fallbackSpaceGlyph;return null};h.prototype.computeMetrics=function(e,t,r,n){var a=this._opt.letterSpacing||0;var i=this._opt.font;var o=0;var s=0;var c=0;var l;var u;if(!i.chars||i.chars.length===0){return{start:t,end:t,width:0}}r=Math.min(e.length,r);for(var f=t;f<r;f++){var p=e.charCodeAt(f);var l=this.getGlyph(i,p);if(l){var d=l.xoffset;var h=u?S(i,u.id,l.id):0;o+=h;var v=o+l.xadvance+a;var m=o+l.width;if(m>=n||v>=n)break;o=v;s=m;u=l}c++}if(u)s+=u.xoffset;return{start:t,end:t+c,width:s}};["width","height","descender","ascender","xHeight","baseline","capHeight","lineHeight"].forEach(v);function v(e){Object.defineProperty(h.prototype,e,{get:m(e),configurable:true})}function m(e){return new Function(["return function "+e+"() {","  return this._"+e,"}"].join("\n"))()}function g(e,t){if(!e.chars||e.chars.length===0)return null;var r=_(e.chars,t);if(r>=0)return e.chars[r];return null}function y(e){for(var t=0;t<o.length;t++){var r=o[t].charCodeAt(0);var n=_(e.chars,r);if(n>=0)return e.chars[n].height}return 0}function b(e){for(var t=0;t<s.length;t++){var r=s[t].charCodeAt(0);var n=_(e.chars,r);if(n>=0)return e.chars[n]}return 0}function w(e){for(var t=0;t<c.length;t++){var r=c[t].charCodeAt(0);var n=_(e.chars,r);if(n>=0)return e.chars[n].height}return 0}function S(e,t,r){if(!e.kernings||e.kernings.length===0)return 0;var n=e.kernings;for(var a=0;a<n.length;a++){var i=n[a];if(i.first===t&&i.second===r)return i.amount}return 0}function x(e){if(e==="center")return p;else if(e==="right")return d;return f}function _(e,t,r){r=r||0;for(var n=r;n<e.length;n++){if(e[n].id===t){return n}}return-1}},f367:function(e,t,r){"use strict";var n=r("d6c7");var a=typeof Symbol==="function"&&typeof Symbol("foo")==="symbol";var i=Object.prototype.toString;var o=Array.prototype.concat;var s=Object.defineProperty;var c=function(e){return typeof e==="function"&&i.call(e)==="[object Function]"};var l=function(){var e={};try{s(e,"x",{enumerable:false,value:e});for(var t in e){return false}return e.x===e}catch(r){return false}};var u=s&&l();var f=function(e,t,r,n){if(t in e&&(!c(n)||!n())){return}if(u){s(e,t,{configurable:true,enumerable:false,value:r,writable:true})}else{e[t]=r}};var p=function(e,t){var r=arguments.length>2?arguments[2]:{};var i=n(t);if(a){i=o.call(i,Object.getOwnPropertySymbols(t))}for(var s=0;s<i.length;s+=1){f(e,i[s],t[i[s]],r[i[s]])}};p.supportsDescriptors=!!u;e.exports=p},f4c8:function(e,t,r){"use strict";var n=r("28e2");var a=r.n(n);var i=a.a},f699:function(e,t,r){"use strict";var n=r("ac1b");var a=r.n(n);var i=a.a},fad7:function(e,t,r){var n=r("f1f5");var a=r("486c");var i={scaleh:"scaleH",scalew:"scaleW",stretchh:"stretchH",lineheight:"lineHeight",alphachnl:"alphaChnl",redchnl:"redChnl",greenchnl:"greenChnl",bluechnl:"blueChnl"};e.exports=function e(t){t=t.toString();var r=a(t);var i={pages:[],chars:[],kernings:[]};["info","common"].forEach(function(e){var t=r.getElementsByTagName(e)[0];if(t)i[e]=n(o(t))});var s=r.getElementsByTagName("pages")[0];if(!s)throw new Error("malformed file -- no <pages> element");var c=s.getElementsByTagName("page");for(var l=0;l<c.length;l++){var u=c[l];var f=parseInt(u.getAttribute("id"),10);var p=u.getAttribute("file");if(isNaN(f))throw new Error('malformed file -- page "id" attribute is NaN');if(!p)throw new Error('malformed file -- needs page "file" attribute');i.pages[parseInt(f,10)]=p}["chars","kernings"].forEach(function(e){var t=r.getElementsByTagName(e)[0];if(!t)return;var a=e.substring(0,e.length-1);var s=t.getElementsByTagName(a);for(var c=0;c<s.length;c++){var l=s[c];i[e].push(n(o(l)))}});return i};function o(e){var t=s(e);return t.reduce(function(e,t){var r=c(t.nodeName);e[r]=t.nodeValue;return e},{})}function s(e){var t=[];for(var r=0;r<e.attributes.length;r++)t.push(e.attributes[r]);return t}function c(e){return i[e.toLowerCase()]||e}},fce8:function(e,t,r){}}]);
//# sourceMappingURL=cde249a4.a04ba540.js.map