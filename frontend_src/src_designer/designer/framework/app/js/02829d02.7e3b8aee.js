(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["02829d02"],{1344:function(e,n){e.exports=t;function t(e,n,t,r,o){this.from=e;this.to=n;this.length=t;this.coeff=r;this.weight=typeof o==="number"?o:1}},"21c4":function(e,n,t){e.exports.main=function(e){var n=t("7a7c").balancedBinTree(9),o=r(n);var i=t("adbe");var a=i(e,n,o);var f=t("3134");f(a,o);u();function u(){a.renderFrame();requestAnimationFrame(u)}};function r(e){var n=t("2364");return n(e,{springLength:30,springCoeff:8e-4,dragCoeff:.01,gravity:-1.2,theta:1})}},"235c":function(e,n,t){},2364:function(e,n,t){e.exports=o;e.exports.simulator=t("4a85");var r=t("bb8c");function o(e,n){if(!e){throw new Error("Graph structure cannot be undefined")}var o=t("4a85");var a=o(n);var f=M;if(n&&typeof n.nodeMass==="function"){f=n.nodeMass}var u=Object.create(null);var s={};var c=0;var d=a.settings.springTransform||i;b();g();var l=false;var v={step:function(){if(c===0)return true;var e=a.step();v.lastMove=e;v.fire("step");var n=e/c;var t=n<=.01;if(l!==t){l=t;m(t)}return t},getNodePosition:function(e){return P(e).pos},setNodePosition:function(e){var n=P(e);n.setPosition.apply(n,Array.prototype.slice.call(arguments,1));a.invalidateBBox()},getLinkPosition:function(e){var n=s[e];if(n){return{from:n.from.pos,to:n.to.pos}}},getGraphRect:function(){return a.getBBox()},forEachBody:p,pinNode:function(e,n){var t=P(e.id);t.isPinned=!!n},isNodePinned:function(e){return P(e.id).isPinned},dispose:function(){e.off("changed",x);v.fire("disposed")},getBody:y,getSpring:h,simulator:a,graph:e,lastMove:0};r(v);return v;function p(e){Object.keys(u).forEach(function(n){e(u[n],n)})}function h(n,t){var r;if(t===undefined){if(typeof n!=="object"){r=n}else{r=n.id}}else{var o=e.hasLink(n,t);if(!o)return;r=o.id}return s[r]}function y(e){return u[e]}function g(){e.on("changed",x)}function m(e){v.fire("stable",e)}function x(n){for(var t=0;t<n.length;++t){var r=n[t];if(r.changeType==="add"){if(r.node){w(r.node.id)}if(r.link){L(r.link)}}else if(r.changeType==="remove"){if(r.node){k(r.node)}if(r.link){E(r.link)}}}c=e.getNodesCount()}function b(){c=0;e.forEachNode(function(e){w(e.id);c+=1});e.forEachLink(L)}function w(n){var t=u[n];if(!t){var r=e.getNode(n);if(!r){throw new Error("initBody() was called with unknown node id")}var o=r.position;if(!o){var i=N(r);o=a.getBestNewBodyPosition(i)}t=a.addBodyAt(o);t.id=n;u[n]=t;I(n);if(q(r)){t.isPinned=true}}}function k(e){var n=e.id;var t=u[n];if(t){u[n]=null;delete u[n];a.removeBody(t)}}function L(e){I(e.fromId);I(e.toId);var n=u[e.fromId],t=u[e.toId],r=a.addSpring(n,t,e.length);d(e,r);s[e.id]=r}function E(n){var t=s[n.id];if(t){var r=e.getNode(n.fromId),o=e.getNode(n.toId);if(r)I(r.id);if(o)I(o.id);delete s[n.id];a.removeSpring(t)}}function N(e){var n=[];if(!e.links){return n}var t=Math.min(e.links.length,2);for(var r=0;r<t;++r){var o=e.links[r];var i=o.fromId!==e.id?u[o.fromId]:u[o.toId];if(i&&i.pos){n.push(i)}}return n}function I(e){var n=u[e];n.mass=f(e);if(Number.isNaN(n.mass)){throw new Error("Node mass should be a number")}}function q(e){return e&&(e.isPinned||e.data&&e.data.isPinned)}function P(e){var n=u[e];if(!n){w(e);n=u[e]}return n}function M(n){var t=e.getLinks(n);if(!t)return 1;return 1+t.length/3}}function i(){}},3134:function(e,n,t){e.exports=function(e,n){var r=t("f73a");var o=e.graphGraphics;r(e.domContainer,function(e){a(e.clientX,e.clientY,e.deltaY<0)});f();var i=function(){var e={global:{x:0,y:0}};return function(n,t){e.global.x=n;e.global.y=t;return PIXI.InteractionData.prototype.getLocalPosition.call(e,o)}}();function a(e,n,t){direction=t?1:-1;var r=1+direction*.1;o.scale.x*=r;o.scale.y*=r;var a=i(e,n);o.updateTransform();var f=i(e,n);o.position.x+=(f.x-a.x)*o.scale.x;o.position.y+=(f.y-a.y)*o.scale.y;o.updateTransform()}function f(){var t=e.stage;t,interactive=true;var r=false,a,f,u;t.on("mousedown",function(t){var o=t.global;var s=i(o.x,o.y);a=e.getNodeAt(s.x,s.y);if(a){n.pinNode(a,true)}f=o.x;u=o.y;r=true});t.mousemove=function(e){if(!r){return}var t=e.global;if(a){var s=i(t.x,t.y);n.setNodePosition(a.id,s.x,s.y)}else{var c=t.x-f;var d=t.y-u;f=t.x;u=t.y;o.position.x+=c;o.position.y+=d}};t.mouseup=function(e){r=false;if(a){draggingNode=null;n.pinNode(a,false)}}}}},"48ba":function(e,n,t){e.exports=o;var r=t("7cb7");function o(e){e=e||{};if("uniqueLinkId"in e){console.warn("ngraph.graph: Starting from version 0.14 `uniqueLinkId` is deprecated.\n"+"Use `multigraph` option instead\n","\n","Note: there is also change in default behavior: From now on each graph\n"+"is considered to be not a multigraph by default (each edge is unique).");e.multigraph=e.uniqueLinkId}if(e.multigraph===undefined)e.multigraph=false;var n=typeof Object.create==="function"?Object.create(null):{},t=[],o={},s=0,d=0,l=z(),v=e.multigraph?P:q,p=[],h=X,y=X,g=X,m=X;var x={addNode:L,addLink:I,removeLink:B,removeNode:N,getNode:E,getNodesCount:function(){return s},getLinksCount:function(){return t.length},getLinks:M,forEachNode:l,forEachLinkedNode:O,forEachLink:A,beginUpdate:g,endUpdate:m,clear:D,hasLink:C,hasNode:E,getLink:C};r(x);b();return x;function b(){var e=x.on;x.on=n;function n(){x.beginUpdate=g=T;x.endUpdate=m=U;h=w;y=k;x.on=e;return e.apply(x,arguments)}}function w(e,n){p.push({link:e,changeType:n})}function k(e,n){p.push({node:e,changeType:n})}function L(e,t){if(e===undefined){throw new Error("Invalid node identifier")}g();var r=E(e);if(!r){r=new a(e,t);s++;y(r,"add")}else{r.data=t;y(r,"update")}n[e]=r;m();return r}function E(e){return n[e]}function N(e){var t=E(e);if(!t){return false}g();var r=t.links;if(r){t.links=null;for(var o=0;o<r.length;++o){B(r[o])}}delete n[e];s--;y(t,"remove");m();return true}function I(e,n,r){g();var o=E(e)||L(e);var i=E(n)||L(n);var a=v(e,n,r);t.push(a);f(o,a);if(e!==n){f(i,a)}h(a,"add");m();return a}function q(e,n,t){var r=c(e,n);return new u(e,n,t,r)}function P(e,n,t){var r=c(e,n);var i=o.hasOwnProperty(r);if(i||C(e,n)){if(!i){o[r]=0}var a="@"+ ++o[r];r=c(e+a,n+a)}return new u(e,n,t,r)}function M(e){var n=E(e);return n?n.links:null}function B(e){if(!e){return false}var n=i(e,t);if(n<0){return false}g();t.splice(n,1);var r=E(e.fromId);var o=E(e.toId);if(r){n=i(e,r.links);if(n>=0){r.links.splice(n,1)}}if(o){n=i(e,o.links);if(n>=0){o.links.splice(n,1)}}h(e,"remove");m();return true}function C(e,n){var t=E(e),r;if(!t||!t.links){return null}for(r=0;r<t.links.length;++r){var o=t.links[r];if(o.fromId===e&&o.toId===n){return o}}return null}function D(){g();l(function(e){N(e.id)});m()}function A(e){var n,r;if(typeof e==="function"){for(n=0,r=t.length;n<r;++n){e(t[n])}}}function O(e,n,t){var r=E(e);if(r&&r.links&&typeof n==="function"){if(t){return j(r.links,e,n)}else{return S(r.links,e,n)}}}function S(e,t,r){var o;for(var i=0;i<e.length;++i){var a=e[i];var f=a.fromId===t?a.toId:a.fromId;o=r(n[f],a);if(o){return true}}}function j(e,t,r){var o;for(var i=0;i<e.length;++i){var a=e[i];if(a.fromId===t){o=r(n[a.toId],a);if(o){return true}}}}function X(){}function T(){d+=1}function U(){d-=1;if(d===0&&p.length>0){x.fire("changed",p);p.length=0}}function z(){return Object.keys?V:_}function V(e){if(typeof e!=="function"){return}var t=Object.keys(n);for(var r=0;r<t.length;++r){if(e(n[t[r]])){return true}}}function _(e){if(typeof e!=="function"){return}var t;for(t in n){if(e(n[t])){return true}}}}function i(e,n){if(!n)return-1;if(n.indexOf){return n.indexOf(e)}var t=n.length,r;for(r=0;r<t;r+=1){if(n[r]===e){return r}}return-1}function a(e,n){this.id=e;this.links=null;this.data=n}function f(e,n){if(e.links){e.links.push(n)}else{e.links=[n]}}function u(e,n,t,r){this.fromId=e;this.toId=n;this.data=t;this.id=r}function s(e){var n=0,t,r,o;if(e.length==0)return n;for(t=0,o=e.length;t<o;t++){r=e.charCodeAt(t);n=(n<<5)-n+r;n|=0}return n}function c(e,n){return e.toString()+"👉 "+n.toString()}},"4a85":function(e,n,t){e.exports=r;function r(e){var n=t("1344");var r=t("ae53");var o=t("4dac");var i=t("7cb7");e=o(e,{springLength:30,springCoeff:8e-4,gravity:-1.2,theta:.8,dragCoeff:.02,timeStep:20});var a=e.createQuadTree||t("8e62");var f=e.createBounds||t("d5a0");var u=e.createDragForce||t("7177");var s=e.createSpringForce||t("5b38");var c=e.integrator||t("4cd1");var d=e.createBody||t("ef9b");var l=[],v=[],p=a(e),h=f(l,e),y=s(e),g=u(e);var m=true;var x=0;var b={bodies:l,quadTree:p,springs:v,settings:e,step:function(){w();var n=c(l,e.timeStep);h.update();return n},addBody:function(e){if(!e){throw new Error("Body is required")}l.push(e);return e},addBodyAt:function(e){if(!e){throw new Error("Body position is required")}var n=d(e);l.push(n);return n},removeBody:function(e){if(!e){return}var n=l.indexOf(e);if(n<0){return}l.splice(n,1);if(l.length===0){h.reset()}return true},addSpring:function(e,t,r,o,i){if(!e||!t){throw new Error("Cannot add null spring to force simulator")}if(typeof r!=="number"){r=-1}var a=new n(e,t,r,i>=0?i:-1,o);v.push(a);return a},getTotalMovement:function(){return x},removeSpring:function(e){if(!e){return}var n=v.indexOf(e);if(n>-1){v.splice(n,1);return true}},getBestNewBodyPosition:function(e){return h.getBestNewPosition(e)},getBBox:function(){if(m){h.update();m=false}return h.box},invalidateBBox:function(){m=true},gravity:function(n){if(n!==undefined){e.gravity=n;p.options({gravity:n});return this}else{return e.gravity}},theta:function(n){if(n!==undefined){e.theta=n;p.options({theta:n});return this}else{return e.theta}}};r(e,b);i(b);return b;function w(){var e,n=l.length;if(n){p.insertBodies(l);while(n--){e=l[n];if(!e.isPinned){e.force.reset();p.updateBodyForce(e);g.update(e)}}}n=v.length;while(n--){y.update(v[n])}}}},"4cd1":function(e,n){e.exports=t;function t(e,n){var t=0,r=0,o=0,i=0,a,f=e.length;if(f===0){return 0}for(a=0;a<f;++a){var u=e[a],s=n/u.mass;u.velocity.x+=s*u.force.x;u.velocity.y+=s*u.force.y;var c=u.velocity.x,d=u.velocity.y,l=Math.sqrt(c*c+d*d);if(l>1){u.velocity.x=c/l;u.velocity.y=d/l}t=n*u.velocity.x;o=n*u.velocity.y;u.pos.x+=t;u.pos.y+=o;r+=Math.abs(t);i+=Math.abs(o)}return(r*r+i*i)/f}},"4dac":function(e,n){e.exports=t;function t(e,n){var r;if(!e){e={}}if(n){for(r in n){if(n.hasOwnProperty(r)){var o=e.hasOwnProperty(r),i=typeof n[r],a=!o||typeof e[r]!==i;if(a){e[r]=n[r]}else if(i==="object"){e[r]=t(e[r],n[r])}}}}return e}},"5b38":function(e,n,t){e.exports=function(e){var n=t("4dac");var r=t("989e").random(42);var o=t("ae53");e=n(e,{springCoeff:2e-4,springLength:80});var i={update:function(n){var t=n.from,o=n.to,i=n.length<0?e.springLength:n.length,a=o.pos.x-t.pos.x,f=o.pos.y-t.pos.y,u=Math.sqrt(a*a+f*f);if(u===0){a=(r.nextDouble()-.5)/50;f=(r.nextDouble()-.5)/50;u=Math.sqrt(a*a+f*f)}var s=u-i;var c=(!n.coeff||n.coeff<0?e.springCoeff:n.coeff)*s/u*n.weight;t.force.x+=c*a;t.force.y+=c*f;o.force.x-=c*a;o.force.y-=c*f}};o(e,i,["springCoeff","springLength"]);return i}},"63b6":function(e,n,t){"use strict";var r=t("235c");var o=t.n(r);var i=o.a},7177:function(e,n,t){e.exports=function(e){var n=t("4dac"),r=t("ae53");e=n(e,{dragCoeff:.02});var o={update:function(n){n.force.x-=e.dragCoeff*n.velocity.x;n.force.y-=e.dragCoeff*n.velocity.y}};r(e,o,["dragCoeff"]);return o}},"7a7c":function(e,n,t){var r=t("48ba");e.exports=o(r);e.exports.factory=o;function o(e){return{ladder:n,complete:o,completeBipartite:i,balancedBinTree:s,path:a,circularLadder:r,grid:f,grid3:u,noLinks:c,wattsStrogatz:l,cliqueCircle:d};function n(n){if(!n||n<0){throw new Error("Invalid number of nodes")}var t=e(),r;for(r=0;r<n-1;++r){t.addLink(r,r+1);t.addLink(n+r,n+r+1);t.addLink(r,n+r)}t.addLink(n-1,2*n-1);return t}function r(e){if(!e||e<0){throw new Error("Invalid number of nodes")}var t=n(e);t.addLink(0,e-1);t.addLink(e,2*e-1);return t}function o(n){if(!n||n<1){throw new Error("At least two nodes are expected for complete graph")}var t=e(),r,o;for(r=0;r<n;++r){for(o=r+1;o<n;++o){if(r!==o){t.addLink(r,o)}}}return t}function i(n,t){if(!n||!t||n<0||t<0){throw new Error("Graph dimensions are invalid. Number of nodes in each partition should be greater than 0")}var r=e(),o,i;for(o=0;o<n;++o){for(i=n;i<n+t;++i){r.addLink(o,i)}}return r}function a(n){if(!n||n<0){throw new Error("Invalid number of nodes")}var t=e(),r;t.addNode(0);for(r=1;r<n;++r){t.addLink(r-1,r)}return t}function f(n,t){if(n<1||t<1){throw new Error("Invalid number of nodes in grid graph")}var r=e(),o,i;if(n===1&&t===1){r.addNode(0);return r}for(o=0;o<n;++o){for(i=0;i<t;++i){var a=o+i*n;if(o>0){r.addLink(a,o-1+i*n)}if(i>0){r.addLink(a,o+(i-1)*n)}}}return r}function u(n,t,r){if(n<1||t<1||r<1){throw new Error("Invalid number of nodes in grid3 graph")}var o=e(),i,a,f;if(n===1&&t===1&&r===1){o.addNode(0);return o}for(f=0;f<r;++f){for(i=0;i<n;++i){for(a=0;a<t;++a){var u=f*n*t;var s=i+a*n+u;if(i>0){o.addLink(s,i-1+a*n+u)}if(a>0){o.addLink(s,i+(a-1)*n+u)}if(f>0){o.addLink(s,i+a*n+(f-1)*n*t)}}}}return o}function s(n){if(n<0){throw new Error("Invalid number of nodes in balanced tree")}var t=e(),r=Math.pow(2,n),o;if(n===0){t.addNode(1)}for(o=1;o<r;++o){var i=o,a=i*2,f=i*2+1;t.addLink(i,a);t.addLink(i,f)}return t}function c(n){if(n<0){throw new Error("Number of nodes should be >= 0")}var t=e(),r;for(r=0;r<n;++r){t.addNode(r)}return t}function d(n,t){if(n<1)throw new Error("Invalid number of cliqueCount in cliqueCircle");if(t<1)throw new Error("Invalid number of cliqueSize in cliqueCircle");var r=e();for(var o=0;o<n;++o){i(t,o*t);if(o>0){r.addLink(o*t,o*t-1)}}r.addLink(0,r.getNodesCount()-1);return r;function i(e,n){for(var t=0;t<e;++t){r.addNode(t+n)}for(var t=0;t<e;++t){for(var o=t+1;o<e;++o){r.addLink(t+n,o+n)}}}}function l(n,r,o,i){if(r>=n)throw new Error("Choose smaller `k`. It cannot be larger than number of nodes `n`");var a=t("90db").random(i||42);var f=e(),u,s;for(u=0;u<n;++u){f.addNode(u)}var c=Math.floor(r/2+1);for(var d=1;d<c;++d){for(u=0;u<n;++u){s=(d+u)%n;f.addLink(u,s)}}for(d=1;d<c;++d){for(u=0;u<n;++u){if(a.nextDouble()<o){var l=u;s=(d+u)%n;var v=a.next(n);var p=v===l||f.hasLink(l,v);if(p&&f.getLinks(l).length===n-1){continue}while(p){v=a.next(n);p=v===l||f.hasLink(l,v)}var h=f.hasLink(l,s);f.removeLink(h);f.addLink(l,v)}}}return f}}},"7cb7":function(e,n){e.exports=function(e){r(e);var n=t(e);e.on=n.on;e.off=n.off;e.fire=n.fire;return e};function t(e){var n=Object.create(null);return{on:function(t,r,o){if(typeof r!=="function"){throw new Error("callback is expected to be a function")}var i=n[t];if(!i){i=n[t]=[]}i.push({callback:r,ctx:o});return e},off:function(t,r){var o=typeof t==="undefined";if(o){n=Object.create(null);return e}if(n[t]){var i=typeof r!=="function";if(i){delete n[t]}else{var a=n[t];for(var f=0;f<a.length;++f){if(a[f].callback===r){a.splice(f,1)}}}}return e},fire:function(t){var r=n[t];if(!r){return e}var o;if(arguments.length>1){o=Array.prototype.splice.call(arguments,1)}for(var i=0;i<r.length;++i){var a=r[i];a.callback.apply(a.ctx,o)}return e}}}function r(e){if(!e){throw new Error("Eventify cannot use falsy object as events subject")}var n=["on","fire","off"];for(var t=0;t<n.length;++t){if(e.hasOwnProperty(n[t])){throw new Error("Subject cannot be eventified, since it already has property '"+n[t]+"'")}}}},"7e93":function(e,n){e.exports=t;function t(){this.stack=[];this.popIdx=0}t.prototype={isEmpty:function(){return this.popIdx===0},push:function(e,n){var t=this.stack[this.popIdx];if(!t){this.stack[this.popIdx]=new r(e,n)}else{t.node=e;t.body=n}++this.popIdx},pop:function(){if(this.popIdx>0){return this.stack[--this.popIdx]}},reset:function(){this.popIdx=0}};function r(e,n){this.node=e;this.body=n}},"8e62":function(e,n,t){e.exports=function(e){e=e||{};e.gravity=typeof e.gravity==="number"?e.gravity:-1;e.theta=typeof e.theta==="number"?e.theta:.8;var n=t("989e").random(1984),i=t("ec64"),a=t("7e93"),f=t("8f06");var u=e.gravity,s=[],c=new a,d=e.theta,l=[],v=0,p=h();return{insertBodies:g,getRoot:function(){return p},updateBodyForce:y,options:function(e){if(e){if(typeof e.gravity==="number"){u=e.gravity}if(typeof e.theta==="number"){d=e.theta}return this}return{gravity:u,theta:d}}};function h(){var e=l[v];if(e){e.quad0=null;e.quad1=null;e.quad2=null;e.quad3=null;e.body=null;e.mass=e.massX=e.massY=0;e.left=e.right=e.top=e.bottom=0}else{e=new i;l[v]=e}++v;return e}function y(e){var t=s,r,o,i,a,f=0,c=0,l=1,v=0,h=1;t[0]=p;while(l){var y=t[v],g=y.body;l-=1;v+=1;var m=g!==e;if(g&&m){o=g.pos.x-e.pos.x;i=g.pos.y-e.pos.y;a=Math.sqrt(o*o+i*i);if(a===0){o=(n.nextDouble()-.5)/50;i=(n.nextDouble()-.5)/50;a=Math.sqrt(o*o+i*i)}r=u*g.mass*e.mass/(a*a*a);f+=r*o;c+=r*i}else if(m){o=y.massX/y.mass-e.pos.x;i=y.massY/y.mass-e.pos.y;a=Math.sqrt(o*o+i*i);if(a===0){o=(n.nextDouble()-.5)/50;i=(n.nextDouble()-.5)/50;a=Math.sqrt(o*o+i*i)}if((y.right-y.left)/a<d){r=u*y.mass*e.mass/(a*a*a);f+=r*o;c+=r*i}else{if(y.quad0){t[h]=y.quad0;l+=1;h+=1}if(y.quad1){t[h]=y.quad1;l+=1;h+=1}if(y.quad2){t[h]=y.quad2;l+=1;h+=1}if(y.quad3){t[h]=y.quad3;l+=1;h+=1}}}}e.force.x+=f;e.force.y+=c}function g(e){var n=Number.MAX_VALUE,t=Number.MAX_VALUE,r=Number.MIN_VALUE,o=Number.MIN_VALUE,i,a=e.length;i=a;while(i--){var f=e[i].pos.x;var u=e[i].pos.y;if(f<n){n=f}if(f>r){r=f}if(u<t){t=u}if(u>o){o=u}}var s=r-n,c=o-t;if(s>c){o=t+s}else{r=n+c}v=0;p=h();p.left=n;p.right=r;p.top=t;p.bottom=o;i=a-1;if(i>=0){p.body=e[i]}while(i--){m(e[i],p)}}function m(e){c.reset();c.push(p,e);while(!c.isEmpty()){var t=c.pop(),i=t.node,a=t.body;if(!i.body){var u=a.pos.x;var s=a.pos.y;i.mass=i.mass+a.mass;i.massX=i.massX+a.mass*u;i.massY=i.massY+a.mass*s;var d=0,l=i.left,v=(i.right+l)/2,y=i.top,g=(i.bottom+y)/2;if(u>v){d=d+1;l=v;v=i.right}if(s>g){d=d+2;y=g;g=i.bottom}var m=r(i,d);if(!m){m=h();m.left=l;m.top=y;m.right=v;m.bottom=g;m.body=a;o(i,d,m)}else{c.push(m,a)}}else{var x=i.body;i.body=null;if(f(x.pos,a.pos)){var b=3;do{var w=n.nextDouble();var k=(i.right-i.left)*w;var L=(i.bottom-i.top)*w;x.pos.x=i.left+k;x.pos.y=i.top+L;b-=1}while(b>0&&f(x.pos,a.pos));if(b===0&&f(x.pos,a.pos)){return}}c.push(i,x);c.push(i,a)}}}};function r(e,n){if(n===0)return e.quad0;if(n===1)return e.quad1;if(n===2)return e.quad2;if(n===3)return e.quad3;return null}function o(e,n,t){if(n===0)e.quad0=t;else if(n===1)e.quad1=t;else if(n===2)e.quad2=t;else if(n===3)e.quad3=t}},"8f06":function(e,n){e.exports=function e(n,t){var r=Math.abs(n.x-t.x);var o=Math.abs(n.y-t.y);return r<1e-8&&o<1e-8}},"90db":function(e,n){e.exports=t;e.exports.random=t,e.exports.randomIterator=f;function t(e){var n=typeof e==="number"?e:+new Date;return new r(n)}function r(e){this.seed=e}r.prototype.next=a;r.prototype.nextDouble=i;r.prototype.uniform=i;r.prototype.gaussian=o;function o(){var e,n,t;do{n=this.nextDouble()*2-1;t=this.nextDouble()*2-1;e=n*n+t*t}while(e>=1||e===0);return n*Math.sqrt(-2*Math.log(e)/e)}function i(){var e=this.seed;e=e+2127912214+(e<<12)&4294967295;e=(e^3345072700^e>>>19)&4294967295;e=e+374761393+(e<<5)&4294967295;e=(e+3550635116^e<<9)&4294967295;e=e+4251993797+(e<<3)&4294967295;e=(e^3042594569^e>>>16)&4294967295;this.seed=e;return(e&268435455)/268435456}function a(e){return Math.floor(this.nextDouble()*e)}function f(e,n){var r=n||t();if(typeof r.next!=="function"){throw new Error("customRandom does not match expected API: next() function is missing")}return{forEach:i,shuffle:o};function o(){var n,t,o;for(n=e.length-1;n>0;--n){t=r.next(n+1);o=e[t];e[t]=e[n];e[n]=o}return e}function i(n){var t,o,i;for(t=e.length-1;t>0;--t){o=r.next(t+1);i=e[o];e[o]=e[t];e[t]=i;n(i)}if(e.length){n(e[0])}}}},"989e":function(e,n){e.exports={random:t,randomIterator:r};function t(e){var n=typeof e==="number"?e:+new Date;var t=function(){n=n+2127912214+(n<<12)&4294967295;n=(n^3345072700^n>>>19)&4294967295;n=n+374761393+(n<<5)&4294967295;n=(n+3550635116^n<<9)&4294967295;n=n+4251993797+(n<<3)&4294967295;n=(n^3042594569^n>>>16)&4294967295;return(n&268435455)/268435456};return{next:function(e){return Math.floor(t()*e)},nextDouble:function(){return t()}}}function r(e,n){var r=n||t();if(typeof r.next!=="function"){throw new Error("customRandom does not match expected API: next() function is missing")}return{forEach:function(n){var t,o,i;for(t=e.length-1;t>0;--t){o=r.next(t+1);i=e[o];e[o]=e[t];e[t]=i;n(i)}if(e.length){n(e[0])}},shuffle:function(){var n,t,o;for(n=e.length-1;n>0;--n){t=r.next(n+1);o=e[t];e[t]=e[n];e[n]=o}return e}}}},adbe:function(e,n,t){var r=20;var o=t("912c");e.exports=function(e,n,t){var a=window.innerWidth,f=window.innerHeight;var u=new o.Stage(16777215,true);var s=o.autoDetectRenderer(a,f,null,false,true);s.view.style.display="block";e.appendChild(s.view);var c=new o.Graphics;c.position.x=a/2;c.position.y=f/2;c.scale.x=1;c.scale.y=1;u.addChild(c);var d=[],l={},v=[];n.forEachNode(function(e){d.push(t.getNodePosition(e.id));l[d.length-1]=e});n.forEachLink(function(e){v.push(t.getLinkPosition(e.id))});return{renderFrame:function e(){t.step();i(c,d,v);s.render(u)},domContainer:s.view,graphGraphics:c,stage:u,getNodeAt:function e(n,t){var o=r/2;for(var i=0;i<d.length;++i){var a=d[i];var f=a.x-o<n&&n<a.x+o&&a.y-o<t&&t<a.y+o;if(f){return l[i]}}}}};function i(e,n,t){e.clear();e.beginFill(16777215);var o,i,a,f,u;e.lineStyle(1,16777215,1);for(o=0;o<t.length;++o){var s=t[o];e.moveTo(s.from.x,s.from.y);e.lineTo(s.to.x,s.to.y)}e.lineStyle(0);var c=r/2;for(o=0;o<n.length;++o){i=n[o].x-c;a=n[o].y-c;e.drawRect(i,a,r,r)}}},ae53:function(e,n){e.exports=t;function t(e,n,t){var o=Object.prototype.toString.call(t)==="[object Array]";if(o){for(var i=0;i<t.length;++i){r(e,n,t[i])}}else{for(var a in e){r(e,n,a)}}}function r(e,n,t){if(e.hasOwnProperty(t)){if(typeof n[t]==="function"){return}n[t]=function(r){if(r!==undefined){e[t]=r;return n}return e[t]}}}},bb8c:function(e,n){e.exports=function(e){r(e);var n=t(e);e.on=n.on;e.off=n.off;e.fire=n.fire;return e};function t(e){var n=Object.create(null);return{on:function(t,r,o){if(typeof r!=="function"){throw new Error("callback is expected to be a function")}var i=n[t];if(!i){i=n[t]=[]}i.push({callback:r,ctx:o});return e},off:function(t,r){var o=typeof t==="undefined";if(o){n=Object.create(null);return e}if(n[t]){var i=typeof r!=="function";if(i){delete n[t]}else{var a=n[t];for(var f=0;f<a.length;++f){if(a[f].callback===r){a.splice(f,1)}}}}return e},fire:function(t){var r=n[t];if(!r){return e}var o;if(arguments.length>1){o=Array.prototype.splice.call(arguments,1)}for(var i=0;i<r.length;++i){var a=r[i];a.callback.apply(a.ctx,o)}return e}}}function r(e){if(!e){throw new Error("Eventify cannot use falsy object as events subject")}var n=["on","fire","off"];for(var t=0;t<n.length;++t){if(e.hasOwnProperty(n[t])){throw new Error("Subject cannot be eventified, since it already has property '"+n[t]+"'")}}}},c538:function(e,n){e.exports={Body:t,Vector2d:r,Body3d:o,Vector3d:i};function t(e,n){this.pos=new r(e,n);this.prevPos=new r(e,n);this.force=new r;this.velocity=new r;this.mass=1}t.prototype.setPosition=function(e,n){this.prevPos.x=this.pos.x=e;this.prevPos.y=this.pos.y=n};function r(e,n){if(e&&typeof e!=="number"){this.x=typeof e.x==="number"?e.x:0;this.y=typeof e.y==="number"?e.y:0}else{this.x=typeof e==="number"?e:0;this.y=typeof n==="number"?n:0}}r.prototype.reset=function(){this.x=this.y=0};function o(e,n,t){this.pos=new i(e,n,t);this.prevPos=new i(e,n,t);this.force=new i;this.velocity=new i;this.mass=1}o.prototype.setPosition=function(e,n,t){this.prevPos.x=this.pos.x=e;this.prevPos.y=this.pos.y=n;this.prevPos.z=this.pos.z=t};function i(e,n,t){if(e&&typeof e!=="number"){this.x=typeof e.x==="number"?e.x:0;this.y=typeof e.y==="number"?e.y:0;this.z=typeof e.z==="number"?e.z:0}else{this.x=typeof e==="number"?e:0;this.y=typeof n==="number"?n:0;this.z=typeof t==="number"?t:0}}i.prototype.reset=function(){this.x=this.y=this.z=0}},c95c:function(e,n,t){"use strict";t.r(n);var r=function(){var e=this;var n=e.$createElement;var t=e._self._c||n;return t("section",{staticClass:"cape-bg"},[t("div",{ref:"here",staticClass:"graph"})])};var o=[];var i=t("18a5");var a=t("ce62");var f=t("39fc");var u=t("21c4");var s=t.n(u);var c={components:{},mounted:function e(){s.a.main(this.$refs.here)},methods:{},data:function e(){return{meshes:[]}}};var d=c;var l=t("63b6");var v=t("2877");var p=Object(v["a"])(d,r,o,false,null,null,null);var h=n["default"]=p.exports},d5a0:function(e,n,t){e.exports=function(e,n){var r=t("989e").random(42);var o={x1:0,y1:0,x2:0,y2:0};return{box:o,update:i,reset:function(){o.x1=o.y1=0;o.x2=o.y2=0},getBestNewPosition:function(e){var t=o;var i=0,a=0;if(e.length){for(var f=0;f<e.length;++f){i+=e[f].pos.x;a+=e[f].pos.y}i/=e.length;a/=e.length}else{i=(t.x1+t.x2)/2;a=(t.y1+t.y2)/2}var u=n.springLength;return{x:i+r.next(u)-u/2,y:a+r.next(u)-u/2}}};function i(){var n=e.length;if(n===0){return}var t=Number.MAX_VALUE,r=Number.MAX_VALUE,i=Number.MIN_VALUE,a=Number.MIN_VALUE;while(n--){var f=e[n];if(f.isPinned){f.pos.x=f.prevPos.x;f.pos.y=f.prevPos.y}else{f.prevPos.x=f.pos.x;f.prevPos.y=f.pos.y}if(f.pos.x<t){t=f.pos.x}if(f.pos.x>i){i=f.pos.x}if(f.pos.y<r){r=f.pos.y}if(f.pos.y>a){a=f.pos.y}}o.x1=t;o.x2=i;o.y1=r;o.y2=a}}},ec64:function(e,n){e.exports=function e(){this.body=null;this.quad0=null;this.quad1=null;this.quad2=null;this.quad3=null;this.mass=0;this.massX=0;this.massY=0;this.left=0;this.top=0;this.bottom=0;this.right=0}},ef9b:function(e,n,t){var r=t("c538");e.exports=function(e){return new r.Body(e)}},f73a:function(e,n){e.exports=a;var t="",r,o,i;if(window.addEventListener){r="addEventListener"}else{r="attachEvent";t="on"}i="onwheel"in document.createElement("div")?"wheel":document.onmousewheel!==undefined?"mousewheel":"DOMMouseScroll";function a(e,n,t){f(e,i,n,t);if(i=="DOMMouseScroll"){f(e,"MozMousePixelScroll",n,t)}}function f(e,n,o,a){e[r](t+n,i=="wheel"?o:function(e){!e&&(e=window.event);var n={originalEvent:e,target:e.target||e.srcElement,type:"wheel",deltaMode:e.type=="MozMousePixelScroll"?0:1,deltaX:0,delatZ:0,preventDefault:function n(){e.preventDefault?e.preventDefault():e.returnValue=false}};if(i=="mousewheel"){n.deltaY=-1/40*e.wheelDelta;e.wheelDeltaX&&(n.deltaX=-1/40*e.wheelDeltaX)}else{n.deltaY=e.detail}return o(n)},a||false)}}}]);
//# sourceMappingURL=02829d02.7e3b8aee.js.map