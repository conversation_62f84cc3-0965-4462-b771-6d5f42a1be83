{"version": 3, "sources": ["webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/localforage/dist/localforage.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/collection-visuallisation.vue?68fc", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/collection-visuallisation.vue?bdb2", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/cape-exploded-view-mesh.vue?f3b7", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/cape-exploded-view-mesh.vue", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/cape-exploded-view-mesh.vue?c8b4", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/cape-exploded-view-mesh.vue?f04f", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/collection-visuallisation.vue", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/collection-visuallisation.vue?1d46", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/collection-visuallisation.vue?b419", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/cape-exploded-view-mesh.vue?cae6", "webpack:///../app/renderers/wireframe-multiscene/camera.js", "webpack:///../app/renderers/wireframe-multiscene/multi.js"], "names": ["global", "require", "f", "module", "exports", "g", "define", "e", "t", "n", "r", "s", "o", "u", "a", "i", "Error", "code", "l", "call", "length", "1", "_dereq_", "Mutation", "MutationObserver", "WebKitMutationObserver", "scheduleDrain", "called", "observer", "nextTick", "element", "document", "createTextNode", "observe", "characterData", "data", "setImmediate", "MessageChannel", "channel", "port1", "onmessage", "port2", "postMessage", "createElement", "scriptEl", "onreadystatechange", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "documentElement", "append<PERSON><PERSON><PERSON>", "setTimeout", "draining", "queue", "oldQueue", "len", "immediate", "task", "push", "this", "self", "window", "2", "INTERNAL", "handlers", "REJECTED", "FULFILLED", "PENDING", "Promise", "resolver", "TypeError", "state", "outcome", "safelyResolveThenable", "prototype", "onRejected", "then", "onFulfilled", "promise", "constructor", "unwrap", "QueueItem", "callFulfilled", "otherCallFulfilled", "callRejected", "otherCallRejected", "value", "resolve", "reject", "func", "returnValue", "result", "tryCatch", "getThen", "status", "thenable", "error", "obj", "appy<PERSON><PERSON>", "apply", "arguments", "onError", "onSuccess", "tryToUnwrap", "out", "reason", "all", "iterable", "Object", "toString", "values", "Array", "resolved", "allResolver", "resolveFromAll", "outValue", "race", "response", "3", "4", "_typeof", "Symbol", "iterator", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "getIDB", "indexedDB", "webkitIndexedDB", "mozIndexedDB", "OIndexedDB", "msIndexedDB", "idb", "isIndexedDBValid", "<PERSON><PERSON><PERSON><PERSON>", "openDatabase", "test", "navigator", "userAgent", "platform", "hasFetch", "fetch", "indexOf", "IDBKeyRange", "createBlob", "parts", "properties", "Blob", "name", "Builder", "BlobBuilder", "MSBlobBuilder", "MozBlobBuilder", "WebKitBlobBuilder", "builder", "append", "getBlob", "type", "Promise$1", "executeCallback", "callback", "executeTwoCallbacks", "<PERSON><PERSON><PERSON><PERSON>", "normalizeKey", "key", "console", "warn", "String", "get<PERSON>allback", "DETECT_BLOB_SUPPORT_STORE", "supportsBlobs", "db<PERSON><PERSON><PERSON><PERSON>", "READ_ONLY", "READ_WRITE", "_binStringToArrayBuffer", "bin", "buf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arr", "Uint8Array", "charCodeAt", "_checkBlobSupportWithoutCaching", "txn", "transaction", "blob", "objectStore", "put", "<PERSON>ab<PERSON>", "preventDefault", "stopPropagation", "oncomplete", "matchedChrome", "match", "matchedEdge", "parseInt", "_checkBlobSupport", "_deferReadiness", "dbInfo", "dbContext", "deferredOperation", "deferredOperations", "db<PERSON><PERSON><PERSON>", "_advanceReadiness", "pop", "_rejectReadiness", "err", "_getConnection", "upgradeNeeded", "createDbContext", "db", "close", "db<PERSON><PERSON>s", "version", "openreq", "open", "onupgradeneeded", "createObjectStore", "storeName", "oldVersion", "ex", "newVersion", "onerror", "onsuccess", "_getOriginalConnection", "_getUpgradedConnection", "_isUpgradeNeeded", "defaultVersion", "isNewStore", "objectStoreNames", "contains", "isDowngrade", "isUpgrade", "incVersion", "_encodeBlob", "reader", "FileReader", "onloadend", "base64", "btoa", "target", "__local_forage_encoded_blob", "readAsBinaryString", "_decodeBlob", "encodedBlob", "arrayBuff", "atob", "_isEncodedBlob", "_fullyReady", "_initReady", "_dbInfo", "_tryReconnect", "forages", "forage", "createTransaction", "mode", "retries", "undefined", "tx", "_initStorage", "options", "ready", "initPromises", "ignoreErrors", "j", "slice", "_defaultConfig", "k", "getItem", "store", "req", "get", "iterate", "openCursor", "iterationNumber", "cursor", "setItem", "blobSupport", "removeItem", "clear", "count", "advanced", "advance", "keys", "dropInstance", "currentConfig", "config", "isCurrentDb", "db<PERSON><PERSON><PERSON>", "dropDBPromise", "deleteDatabase", "onblocked", "_forage", "dropObjectPromise", "deleteObjectStore", "_forage2", "asyncStorage", "_driver", "_support", "isWebSQLValid", "BASE_CHARS", "BLOB_TYPE_PREFIX", "BLOB_TYPE_PREFIX_REGEX", "SERIALIZED_MARKER", "SERIALIZED_MARKER_LENGTH", "TYPE_ARRAYBUFFER", "TYPE_BLOB", "TYPE_INT8ARRAY", "TYPE_UINT8ARRAY", "TYPE_UINT8CLAMPEDARRAY", "TYPE_INT16ARRAY", "TYPE_INT32ARRAY", "TYPE_UINT16ARRAY", "TYPE_UINT32ARRAY", "TYPE_FLOAT32ARRAY", "TYPE_FLOAT64ARRAY", "TYPE_SERIALIZED_MARKER_LENGTH", "toString$1", "string<PERSON>o<PERSON>uffer", "serializedString", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "buffer", "bytes", "bufferToString", "base64String", "substring", "serialize", "valueType", "marker", "fileReader", "onload", "str", "readAsA<PERSON>y<PERSON><PERSON>er", "JSON", "stringify", "deserialize", "parse", "blobType", "matcher", "Int8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "localforageSerializer", "createDbTable", "executeSql", "_initStorage$1", "dbInfoPromise", "description", "size", "serializer", "tryExecuteSql", "sqlStatement", "args", "SYNTAX_ERR", "results", "rows", "getItem$1", "item", "iterate$1", "_setItem", "retriesLeft", "originalValue", "sqlError", "QUOTA_ERR", "setItem$1", "removeItem$1", "clear$1", "length$1", "c", "key$1", "keys$1", "getAllStoreNames", "storeNames", "dropInstance$1", "operationInfo", "dropTable", "operations", "webSQLStorage", "isLocalStorageValid", "localStorage", "_getKeyPrefix", "defaultConfig", "keyPrefix", "checkIfLocalStorageThrows", "localStorageTestKey", "_isLocalStorageUsable", "_initStorage$2", "clear$2", "getItem$2", "iterate$2", "keyPrefix<PERSON><PERSON><PERSON>", "key$2", "keys$2", "itemKey", "length$2", "removeItem$2", "setItem$2", "dropInstance$2", "localStorageWrapper", "sameValue", "x", "y", "isNaN", "includes", "array", "searchElement", "isArray", "arg", "DefinedDrivers", "DriverSupport", "DefaultDrivers", "INDEXEDDB", "WEBSQL", "LOCALSTORAGE", "DefaultDriverOrder", "OptionalDriverMethods", "LibraryMethods", "concat", "DefaultConfig", "driver", "callWhenReady", "localForageInstance", "libraryMethod", "_args", "extend", "_key", "hasOwnProperty", "LocalForage", "driverT<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "defineDriver", "_config", "_driverSet", "_initDriver", "_ready", "_wrapLibraryMethodsWithReady", "setDriver", "replace", "driverObject", "complianceError", "driverMethods", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isRequired", "configureMissingMethods", "methodNotImplementedFactory", "methodName", "_i", "_len", "optionalDriverMethod", "setDriverSupport", "support", "info", "getDriver", "getDriverPromise", "getSerializer", "serializerPromise", "drivers", "supportedDrivers", "_getSupportedDrivers", "setDriverToConfig", "extendSelfWithDriver", "_extend", "initDriver", "currentDriverIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldDriverSetDone", "supports", "libraryMethodsAndProperties", "createInstance", "localforage_js", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_visuallisation_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "__webpack_require__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_visuallisation_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "_unused_webpack_default_export", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "attrs", "min", "max", "on", "input", "updateWidth", "model", "$$v", "width", "expression", "_l", "mesh", "mesh-id", "id", "mesh-width", "widthThrottled", "staticRenderFns", "cape_exploded_view_meshvue_type_template_id_3396ca48_lang_pug_render", "ref", "cape_exploded_view_meshvue_type_template_id_3396ca48_lang_pug_staticRenderFns", "cape_exploded_view_meshvue_type_script_lang_js_", "props", "meshId", "Number", "meshWidth", "mounted", "proxy<PERSON><PERSON><PERSON>", "multi", "createProxy", "container", "$refs", "root", "height", "fetchSerialization", "watch", "draw", "methods", "_fetchSerialization", "asyncToGenerator_default", "regenerator_default", "mark", "_callee", "cache", "wrap", "_callee$", "_context", "prev", "next", "localforage_default", "sent", "processGeometry", "cape", "api", "geo", "componentSet", "forceFetch", "pipe", "stop", "json", "serialization", "_draw", "_callee2", "settings", "wireframeGeo", "_callee2$", "_context2", "depth", "mesh_setup", "geom_id", "geom_type", "distortion", "density", "configurator_custom_params", "decoder", "getGeometry", "format", "updateGeometry", "cape_collection_visualisation_cape_exploded_view_meshvue_type_script_lang_js_", "component", "componentNormalizer", "cape_exploded_view_mesh", "collection_visuallisationvue_type_script_lang_js_", "components", "t-exploded-view", "_this", "getMeshesForCollection", "meshes", "lodash_default", "throttle", "_updateWidth", "newWidth", "log", "_getMeshesForCollection", "palette", "collection", "abrupt", "cape_collection_visualisation_collection_visuallisationvue_type_script_lang_js_", "collection_visuallisation_component", "collection_visuallisation", "__webpack_exports__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_cape_exploded_view_mesh_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_cape_exploded_view_mesh_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "THREE", "TrackballControls", "object", "dom<PERSON>lement", "lock", "STATE", "NONE", "ROTATE", "ZOOM", "PAN", "TOUCH_ROTATE", "TOUCH_ZOOM_PAN", "locked", "enabled", "screen", "left", "top", "rotateSpeed", "zoomSpeed", "panSpeed", "noRotate", "noZoom", "noPan", "staticMoving", "dynamicDampingFactor", "minDistance", "maxDistance", "Infinity", "Vector3", "EPS", "lastPosition", "_state", "_prevState", "_eye", "_movePrev", "Vector2", "_move<PERSON>urr", "_lastAxis", "_lastAngle", "_zoomStart", "_zoomEnd", "_touchZoomDistanceStart", "_touchZoomDistanceEnd", "_panStart", "_panEnd", "target0", "clone", "position0", "position", "up0", "up", "changeEvent", "startEvent", "endEvent", "handleResize", "innerWidth", "innerHeight", "box", "getBoundingClientRect", "d", "ownerDocument", "pageXOffset", "clientLeft", "pageYOffset", "clientTop", "handleEvent", "event", "getMouseOnScreen", "vector", "pageX", "pageY", "set", "getMouseOnCircle", "rotateCamera", "axis", "quaternion", "Quaternion", "eyeDirection", "objectUpDirection", "objectSidewaysDirection", "moveDirection", "angle", "deltaAxis", "moveDirectionCopy", "maxA", "breakIt", "angleInDeg", "Math", "PI", "copy", "sub", "normalize", "crossVectors", "<PERSON><PERSON><PERSON><PERSON>", "add", "setFromAxisAngle", "applyQuaternion", "sqrt", "zoomCamera", "factor", "multiplyScalar", "panCamera", "mouseChange", "objectUp", "pan", "lengthSq", "cross", "subVectors", "checkDistances", "addVectors", "update", "lookAt", "distanceToSquared", "dispatchEvent", "reset", "<PERSON><PERSON><PERSON>", "keydown", "removeEventListener", "keyCode", "keyup", "addEventListener", "mousedown", "button", "mousemove", "mouseup", "mousewheel", "deltaMode", "deltaY", "touchstart", "touches", "dx", "dy", "touchmove", "touchend", "contextmenu", "dispose", "create", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HD_RENDERER", "renderer", "Webdesigner<PERSON><PERSON><PERSON>", "conf", "verticals", "backs", "doors", "drawers", "fills", "legs", "accessories", "spacer", "colorMode", "filterMaterialKey", "filterMaterial", "filterEnable", "horizontals", "mat", "color", "linewidth", "material", "wireframe", "transparent", "polygonOffset", "polygonOffsetFactor", "polygonOffsetUnits", "opacity", "PROXY_ID", "MultiSceneR<PERSON><PERSON>", "classCallCheck_default", "scenes", "proxies", "init", "_ref", "setSize", "style", "canvas", "camera", "z", "controls", "_ref2", "cameraMode", "_ref2$type", "cameraInstance", "proxyNo", "CapeCamera", "tylkoCamera", "updateAspect", "noTransitionAnimation", "shouldRender", "renderLoop", "renderCamera", "requestAnimationFrame", "delay", "createCamera", "aspect", "updateProjectionMatrix", "proxy", "createCameraListener", "getPrice", "scene", "setColor", "geometryId", "fixed", "renderScene", "setComponentScene", "comp", "thumbs", "renderComponentScene", "getScene", "flush", "re<PERSON>ze", "proxyInstance", "proxyId", "no", "_", "find", "getProxyByNo", "setComponentViewFinal", "boundingBox", "pMin", "pMax", "x1", "x2", "displayShelf", "bbcam", "boundingBoxForCamera", "geometryMin", "geometryMax", "initedCam", "setShelfViewFinal", "setPipViewFinal", "geometryFixed", "filtered_elements", "filterElements", "elements", "drawElements", "resetItems", "canvasAbsoluteWidth", "canvasAbsoluteHeight", "antialias", "preserveDrawingBuffer", "alpha", "filter_conf", "filter", "resize", "currentScene", "getContext", "clearRect", "drawImage", "object3D", "traverse", "obj3D", "geometry", "computeBoundingBox", "union", "main_item", "sizes", "x_domain", "y_domain", "z_domain", "centers", "elem_type", "cube", "setDesignerMode", "children", "remove", "multiScene<PERSON><PERSON><PERSON>"], "mappings": "4FAAA,SAAAA,GAAA,IAAAC,EAAA,IAAAA;;;;;;;;;;;;;CAMA,SAAAC,GAAa,GAAG,KAAsD,CAAEC,EAAAC,QAAAF,QAAwB,KAAAG,IAAhG,CAAqU,WAAa,IAAAC,EAAAH,EAAAC,EAA0B,gBAAAG,EAAAC,EAAAC,EAAAC,GAA0B,SAAAC,EAAAC,EAAAC,GAAgB,IAAAJ,EAAAG,GAAA,CAAU,IAAAJ,EAAAI,GAAA,CAAU,IAAAE,SAAAb,GAAA,YAAAA,EAA0C,IAAAY,GAAAC,EAAA,OAAgBb,EAACW,GAAA,GAAO,GAAAG,EAAA,OAAAA,EAAAH,GAAA,GAAoB,IAAAV,EAAA,IAAAc,MAAA,uBAAAJ,EAAA,KAA8C,MAAAV,EAAAe,KAAA,mBAAAf,EAAqC,IAAAgB,EAAAT,EAAAG,GAAA,CAAYR,QAAA,IAAYI,EAAAI,GAAA,GAAAO,KAAAD,EAAAd,QAAA,SAAAG,GAAmC,IAAAE,EAAAD,EAAAI,GAAA,GAAAL,GAAiB,OAAAI,EAAAF,IAAAF,IAAgBW,IAAAd,QAAAG,EAAAC,EAAAC,EAAAC,GAAsB,OAAAD,EAAAG,GAAAR,QAAoB,IAAAW,SAAAd,GAAA,YAAAA,EAA0C,QAAAW,EAAA,EAAYA,EAAAF,EAAAU,OAAWR,IAAAD,EAAAD,EAAAE,IAAY,OAAAD,EAA1b,CAAmc,CAAGU,EAAA,UAAAC,EAAAnB,EAAAC,IAClzB,SAAAJ,GACA,aACA,IAAAuB,EAAAvB,EAAAwB,kBAAAxB,EAAAyB,uBAEA,IAAAC,EAEA,CACA,GAAAH,EAAA,CACA,IAAAI,EAAA,EACA,IAAAC,EAAA,IAAAL,EAAAM,GACA,IAAAC,EAAA9B,EAAA+B,SAAAC,eAAA,IACAJ,EAAAK,QAAAH,EAAA,CACAI,cAAA,OAEAR,EAAA,WACAI,EAAAK,KAAAR,MAAA,QAEG,IAAA3B,EAAAoC,qBAAApC,EAAAqC,iBAAA,aACH,IAAAC,EAAA,IAAAtC,EAAAqC,eACAC,EAAAC,MAAAC,UAAAX,EACAH,EAAA,WACAY,EAAAG,MAAAC,YAAA,SAEG,gBAAA1C,GAAA,uBAAAA,EAAA+B,SAAAY,cAAA,WACHjB,EAAA,WAIA,IAAAkB,EAAA5C,EAAA+B,SAAAY,cAAA,UACAC,EAAAC,mBAAA,WACAhB,IAEAe,EAAAC,mBAAA,KACAD,EAAAE,WAAAC,YAAAH,GACAA,EAAA,MAEA5C,EAAA+B,SAAAiB,gBAAAC,YAAAL,QAEG,CACHlB,EAAA,WACAwB,WAAArB,EAAA,KAKA,IAAAsB,EACA,IAAAC,EAAA,GAEA,SAAAvB,IACAsB,EAAA,KACA,IAAApC,EAAAsC,EACA,IAAAC,EAAAF,EAAAhC,OACA,MAAAkC,EAAA,CACAD,EAAAD,EACAA,EAAA,GACArC,GAAA,EACA,QAAAA,EAAAuC,EAAA,CACAD,EAAAtC,KAEAuC,EAAAF,EAAAhC,OAEA+B,EAAA,MAGAhD,EAAAC,QAAAmD,EACA,SAAAA,EAAAC,GACA,GAAAJ,EAAAK,KAAAD,KAAA,IAAAL,EAAA,CACAzB,QAICP,KAAAuC,YAAA1D,IAAA,YAAAA,SAAA2D,OAAA,YAAAA,YAAAC,SAAA,YAAAA,OAAA,KACA,IAAGC,EAAA,UAAAvC,EAAAnB,EAAAC,GACJ,aACA,IAAAmD,EAAAjC,EAAA,GAGA,SAAAwC,KAEA,IAAAC,EAAA,GAEA,IAAAC,EAAA,aACA,IAAAC,EAAA,cACA,IAAAC,EAAA,YAEA/D,EAAAC,QAAA+D,EAEA,SAAAA,EAAAC,GACA,UAAAA,IAAA,YACA,UAAAC,UAAA,+BAEAX,KAAAY,MAAAJ,EACAR,KAAAN,MAAA,GACAM,KAAAa,aAAA,EACA,GAAAH,IAAAN,EAAA,CACAU,EAAAd,KAAAU,IAIAD,EAAAM,UAAA,kBAAAC,GACA,OAAAhB,KAAAiB,KAAA,KAAAD,IAEAP,EAAAM,UAAAE,KAAA,SAAAC,EAAAF,GACA,UAAAE,IAAA,YAAAlB,KAAAY,QAAAL,UACAS,IAAA,YAAAhB,KAAAY,QAAAN,EAAA,CACA,OAAAN,KAEA,IAAAmB,EAAA,IAAAnB,KAAAoB,YAAAhB,GACA,GAAAJ,KAAAY,QAAAJ,EAAA,CACA,IAAAE,EAAAV,KAAAY,QAAAL,EAAAW,EAAAF,EACAK,EAAAF,EAAAT,EAAAV,KAAAa,aACG,CACHb,KAAAN,MAAAK,KAAA,IAAAuB,EAAAH,EAAAD,EAAAF,IAGA,OAAAG,GAEA,SAAAG,EAAAH,EAAAD,EAAAF,GACAhB,KAAAmB,UACA,UAAAD,IAAA,YACAlB,KAAAkB,cACAlB,KAAAuB,cAAAvB,KAAAwB,mBAEA,UAAAR,IAAA,YACAhB,KAAAgB,aACAhB,KAAAyB,aAAAzB,KAAA0B,mBAGAJ,EAAAP,UAAAQ,cAAA,SAAAI,GACAtB,EAAAuB,QAAA5B,KAAAmB,QAAAQ,IAEAL,EAAAP,UAAAS,mBAAA,SAAAG,GACAN,EAAArB,KAAAmB,QAAAnB,KAAAkB,YAAAS,IAEAL,EAAAP,UAAAU,aAAA,SAAAE,GACAtB,EAAAwB,OAAA7B,KAAAmB,QAAAQ,IAEAL,EAAAP,UAAAW,kBAAA,SAAAC,GACAN,EAAArB,KAAAmB,QAAAnB,KAAAgB,WAAAW,IAGA,SAAAN,EAAAF,EAAAW,EAAAH,GACA9B,EAAA,WACA,IAAAkC,EACA,IACAA,EAAAD,EAAAH,GACK,MAAA9E,GACL,OAAAwD,EAAAwB,OAAAV,EAAAtE,GAEA,GAAAkF,IAAAZ,EAAA,CACAd,EAAAwB,OAAAV,EAAA,IAAAR,UAAA,2CACK,CACLN,EAAAuB,QAAAT,EAAAY,MAKA1B,EAAAuB,QAAA,SAAA3B,EAAA0B,GACA,IAAAK,EAAAC,EAAAC,EAAAP,GACA,GAAAK,EAAAG,SAAA,SACA,OAAA9B,EAAAwB,OAAA5B,EAAA+B,EAAAL,OAEA,IAAAS,EAAAJ,EAAAL,MAEA,GAAAS,EAAA,CACAtB,EAAAb,EAAAmC,OACG,CACHnC,EAAAW,MAAAL,EACAN,EAAAY,QAAAc,EACA,IAAAtE,GAAA,EACA,IAAAuC,EAAAK,EAAAP,MAAAhC,OACA,QAAAL,EAAAuC,EAAA,CACAK,EAAAP,MAAArC,GAAAkE,cAAAI,IAGA,OAAA1B,GAEAI,EAAAwB,OAAA,SAAA5B,EAAAoC,GACApC,EAAAW,MAAAN,EACAL,EAAAY,QAAAwB,EACA,IAAAhF,GAAA,EACA,IAAAuC,EAAAK,EAAAP,MAAAhC,OACA,QAAAL,EAAAuC,EAAA,CACAK,EAAAP,MAAArC,GAAAoE,aAAAY,GAEA,OAAApC,GAGA,SAAAiC,EAAAI,GAEA,IAAArB,EAAAqB,KAAArB,KACA,GAAAqB,eAAA,iBAAAA,IAAA,oBAAArB,IAAA,YACA,gBAAAsB,IACAtB,EAAAuB,MAAAF,EAAAG,aAKA,SAAA3B,EAAAb,EAAAmC,GAEA,IAAAnE,EAAA,MACA,SAAAyE,EAAAf,GACA,GAAA1D,EAAA,CACA,OAEAA,EAAA,KACAoC,EAAAwB,OAAA5B,EAAA0B,GAGA,SAAAgB,EAAAhB,GACA,GAAA1D,EAAA,CACA,OAEAA,EAAA,KACAoC,EAAAuB,QAAA3B,EAAA0B,GAGA,SAAAiB,IACAR,EAAAO,EAAAD,GAGA,IAAAV,EAAAC,EAAAW,GACA,GAAAZ,EAAAG,SAAA,SACAO,EAAAV,EAAAL,QAIA,SAAAM,EAAAH,EAAAH,GACA,IAAAkB,EAAA,GACA,IACAA,EAAAlB,MAAAG,EAAAH,GACAkB,EAAAV,OAAA,UACG,MAAAtF,GACHgG,EAAAV,OAAA,QACAU,EAAAlB,MAAA9E,EAEA,OAAAgG,EAGApC,EAAAmB,UACA,SAAAA,EAAAD,GACA,GAAAA,aAAA3B,KAAA,CACA,OAAA2B,EAEA,OAAAtB,EAAAuB,QAAA,IAAA5B,KAAAI,GAAAuB,GAGAlB,EAAAoB,SACA,SAAAA,EAAAiB,GACA,IAAA3B,EAAA,IAAAnB,KAAAI,GACA,OAAAC,EAAAwB,OAAAV,EAAA2B,GAGArC,EAAAsC,MACA,SAAAA,EAAAC,GACA,IAAA/C,EAAAD,KACA,GAAAiD,OAAAlC,UAAAmC,SAAAzF,KAAAuF,KAAA,kBACA,OAAAhD,KAAA6B,OAAA,IAAAlB,UAAA,qBAGA,IAAAf,EAAAoD,EAAAtF,OACA,IAAAO,EAAA,MACA,IAAA2B,EAAA,CACA,OAAAI,KAAA4B,QAAA,IAGA,IAAAuB,EAAA,IAAAC,MAAAxD,GACA,IAAAyD,EAAA,EACA,IAAAhG,GAAA,EACA,IAAA8D,EAAA,IAAAnB,KAAAI,GAEA,QAAA/C,EAAAuC,EAAA,CACA0D,EAAAN,EAAA3F,MAEA,OAAA8D,EACA,SAAAmC,EAAA3B,EAAAtE,GACA4C,EAAA2B,QAAAD,GAAAV,KAAAsC,EAAA,SAAAlB,GACA,IAAApE,EAAA,CACAA,EAAA,KACAoC,EAAAwB,OAAAV,EAAAkB,MAGA,SAAAkB,EAAAC,GACAL,EAAA9F,GAAAmG,EACA,KAAAH,IAAAzD,IAAA3B,EAAA,CACAA,EAAA,KACAoC,EAAAuB,QAAAT,EAAAgC,MAMA1C,EAAAgD,OACA,SAAAA,EAAAT,GACA,IAAA/C,EAAAD,KACA,GAAAiD,OAAAlC,UAAAmC,SAAAzF,KAAAuF,KAAA,kBACA,OAAAhD,KAAA6B,OAAA,IAAAlB,UAAA,qBAGA,IAAAf,EAAAoD,EAAAtF,OACA,IAAAO,EAAA,MACA,IAAA2B,EAAA,CACA,OAAAI,KAAA4B,QAAA,IAGA,IAAAvE,GAAA,EACA,IAAA8D,EAAA,IAAAnB,KAAAI,GAEA,QAAA/C,EAAAuC,EAAA,CACAc,EAAAsC,EAAA3F,IAEA,OAAA8D,EACA,SAAAT,EAAAiB,GACA1B,EAAA2B,QAAAD,GAAAV,KAAA,SAAAyC,GACA,IAAAzF,EAAA,CACAA,EAAA,KACAoC,EAAAuB,QAAAT,EAAAuC,KAEK,SAAArB,GACL,IAAApE,EAAA,CACAA,EAAA,KACAoC,EAAAwB,OAAAV,EAAAkB,SAMC,CAAE1E,EAAA,IAAMgG,EAAA,UAAA/F,EAAAnB,EAAAC,IACT,SAAAJ,GACA,aACA,UAAAA,EAAAmE,UAAA,YACAnE,EAAAmE,QAAA7C,EAAA,MAGCH,KAAAuC,YAAA1D,IAAA,YAAAA,SAAA2D,OAAA,YAAAA,YAAAC,SAAA,YAAAA,OAAA,KACA,CAAEC,EAAA,IAAMyD,EAAA,UAAAhG,EAAAnB,EAAAC,GACT,aAEA,IAAAmH,SAAAC,SAAA,mBAAAA,OAAAC,WAAA,kBAAAzB,GAAoG,cAAAA,GAAqB,SAAAA,GAAmB,OAAAA,UAAAwB,SAAA,YAAAxB,EAAAlB,cAAA0C,QAAAxB,IAAAwB,OAAA/C,UAAA,gBAAAuB,GAE5I,SAAA0B,EAAAC,EAAAC,GAAiD,KAAAD,aAAAC,GAAA,CAA0C,UAAAvD,UAAA,sCAE3F,SAAAwD,IAEA,IACA,UAAAC,YAAA,aACA,OAAAA,UAEA,UAAAC,kBAAA,aACA,OAAAA,gBAEA,UAAAC,eAAA,aACA,OAAAA,aAEA,UAAAC,aAAA,aACA,OAAAA,WAEA,UAAAC,cAAA,aACA,OAAAA,aAEK,MAAA3H,GACL,QAIA,IAAA4H,EAAAN,IAEA,SAAAO,IACA,IAGA,IAAAD,EAAA,CACA,aAMA,IAAAE,SAAAC,eAAA,yCAAAC,KAAAC,UAAAC,aAAA,SAAAF,KAAAC,UAAAC,aAAA,aAAAF,KAAAC,UAAAE,UAEA,IAAAC,SAAAC,QAAA,YAAAA,MAAAhC,WAAAiC,QAAA,qBAIA,QAAAR,GAAAM,WAAAb,YAAA,oBAKAgB,cAAA,YACK,MAAAvI,GACL,cAUA,SAAAwI,EAAAC,EAAAC,GAEAD,KAAA,GACAC,KAAA,GACA,IACA,WAAAC,KAAAF,EAAAC,GACK,MAAA1I,GACL,GAAAA,EAAA4I,OAAA,aACA,MAAA5I,EAEA,IAAA6I,SAAAC,cAAA,YAAAA,mBAAAC,gBAAA,YAAAA,qBAAAC,iBAAA,YAAAA,eAAAC,kBACA,IAAAC,EAAA,IAAAL,EACA,QAAArI,EAAA,EAAuBA,EAAAiI,EAAA5H,OAAkBL,GAAA,GACzC0I,EAAAC,OAAAV,EAAAjI,IAEA,OAAA0I,EAAAE,QAAAV,EAAAW,OAMA,UAAAzF,UAAA,aAGA7C,EAAA,GAEA,IAAAuI,EAAA1F,QAEA,SAAA2F,EAAAjF,EAAAkF,GACA,GAAAA,EAAA,CACAlF,EAAAF,KAAA,SAAAe,GACAqE,EAAA,KAAArE,IACS,SAAAK,GACTgE,EAAAhE,MAKA,SAAAiE,EAAAnF,EAAAkF,EAAAE,GACA,UAAAF,IAAA,YACAlF,EAAAF,KAAAoF,GAGA,UAAAE,IAAA,YACApF,EAAA,SAAAoF,IAIA,SAAAC,EAAAC,GAEA,UAAAA,IAAA,UACAC,QAAAC,KAAAF,EAAA,2CACAA,EAAAG,OAAAH,GAGA,OAAAA,EAGA,SAAAI,IACA,GAAApE,UAAA/E,eAAA+E,oBAAA/E,OAAA,iBACA,OAAA+E,oBAAA/E,OAAA,IAOA,IAAAoJ,EAAA,mCACA,IAAAC,OAAA,EACA,IAAAC,EAAA,GACA,IAAA9D,EAAAD,OAAAlC,UAAAmC,SAGA,IAAA+D,EAAA,WACA,IAAAC,EAAA,YAOA,SAAAC,EAAAC,GACA,IAAA1J,EAAA0J,EAAA1J,OACA,IAAA2J,EAAA,IAAAC,YAAA5J,GACA,IAAA6J,EAAA,IAAAC,WAAAH,GACA,QAAAhK,EAAA,EAAmBA,EAAAK,EAAYL,IAAA,CAC/BkK,EAAAlK,GAAA+J,EAAAK,WAAApK,GAEA,OAAAgK,EAkBA,SAAAK,EAAAjD,GACA,WAAA0B,EAAA,SAAAvE,GACA,IAAA+F,EAAAlD,EAAAmD,YAAAd,EAAAI,GACA,IAAAW,EAAAxC,EAAA,MACAsC,EAAAG,YAAAhB,GAAAiB,IAAAF,EAAA,OAEAF,EAAAK,QAAA,SAAAnL,GAGAA,EAAAoL,iBACApL,EAAAqL,kBACAtG,EAAA,QAGA+F,EAAAQ,WAAA,WACA,IAAAC,EAAAtD,UAAAC,UAAAsD,MAAA,iBACA,IAAAC,EAAAxD,UAAAC,UAAAsD,MAAA,UAGAzG,EAAA0G,IAAAF,GAAAG,SAAAH,EAAA,eAEK,oBACL,eAIA,SAAAI,EAAA/D,GACA,UAAAsC,IAAA,WACA,OAAAZ,EAAAvE,QAAAmF,GAEA,OAAAW,EAAAjD,GAAAxD,KAAA,SAAAU,GACAoF,EAAApF,EACA,OAAAoF,IAIA,SAAA0B,EAAAC,GACA,IAAAC,EAAA3B,EAAA0B,EAAAjD,MAGA,IAAAmD,EAAA,GAEAA,EAAAzH,QAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA+G,EAAAhH,UACAgH,EAAA/G,WAIA8G,EAAAE,mBAAA9I,KAAA6I,GAGA,IAAAD,EAAAG,QAAA,CACAH,EAAAG,QAAAF,EAAAzH,YACK,CACLwH,EAAAG,QAAAH,EAAAG,QAAA7H,KAAA,WACA,OAAA2H,EAAAzH,WAKA,SAAA4H,EAAAL,GACA,IAAAC,EAAA3B,EAAA0B,EAAAjD,MAGA,IAAAmD,EAAAD,EAAAE,mBAAAG,MAIA,GAAAJ,EAAA,CACAA,EAAAhH,UACA,OAAAgH,EAAAzH,SAIA,SAAA8H,EAAAP,EAAAQ,GACA,IAAAP,EAAA3B,EAAA0B,EAAAjD,MAGA,IAAAmD,EAAAD,EAAAE,mBAAAG,MAIA,GAAAJ,EAAA,CACAA,EAAA/G,OAAAqH,GACA,OAAAN,EAAAzH,SAIA,SAAAgI,EAAAT,EAAAU,GACA,WAAAjD,EAAA,SAAAvE,EAAAC,GACAmF,EAAA0B,EAAAjD,MAAAuB,EAAA0B,EAAAjD,OAAA4D,IAEA,GAAAX,EAAAY,GAAA,CACA,GAAAF,EAAA,CACAX,EAAAC,GACAA,EAAAY,GAAAC,YACa,CACb,OAAA3H,EAAA8G,EAAAY,KAIA,IAAAE,EAAA,CAAAd,EAAAjD,MAEA,GAAA2D,EAAA,CACAI,EAAAzJ,KAAA2I,EAAAe,SAGA,IAAAC,EAAAjF,EAAAkF,KAAAnH,MAAAiC,EAAA+E,GAEA,GAAAJ,EAAA,CACAM,EAAAE,gBAAA,SAAA/M,GACA,IAAAyM,EAAAI,EAAA1H,OACA,IACAsH,EAAAO,kBAAAnB,EAAAoB,WACA,GAAAjN,EAAAkN,YAAA,GAEAT,EAAAO,kBAAA/C,IAEiB,MAAAkD,GACjB,GAAAA,EAAAvE,OAAA,mBACAiB,QAAAC,KAAA,iBAAA+B,EAAAjD,KAAA,uCAAA5I,EAAAkN,WAAA,eAAAlN,EAAAoN,WAAA,sBAAAvB,EAAAoB,UAAA,yBACqB,CACrB,MAAAE,KAMAN,EAAAQ,QAAA,SAAArN,GACAA,EAAAoL,iBACApG,EAAA6H,EAAArH,QAGAqH,EAAAS,UAAA,WACAvI,EAAA8H,EAAA1H,QACA+G,EAAAL,MAKA,SAAA0B,EAAA1B,GACA,OAAAS,EAAAT,EAAA,OAGA,SAAA2B,EAAA3B,GACA,OAAAS,EAAAT,EAAA,MAGA,SAAA4B,EAAA5B,EAAA6B,GACA,IAAA7B,EAAAY,GAAA,CACA,YAGA,IAAAkB,GAAA9B,EAAAY,GAAAmB,iBAAAC,SAAAhC,EAAAoB,WACA,IAAAa,EAAAjC,EAAAe,QAAAf,EAAAY,GAAAG,QACA,IAAAmB,EAAAlC,EAAAe,QAAAf,EAAAY,GAAAG,QAEA,GAAAkB,EAAA,CAGA,GAAAjC,EAAAe,UAAAc,EAAA,CACA7D,QAAAC,KAAA,iBAAA+B,EAAAjD,KAAA,yCAAAiD,EAAAY,GAAAG,QAAA,eAAAf,EAAAe,QAAA,KAGAf,EAAAe,QAAAf,EAAAY,GAAAG,QAGA,GAAAmB,GAAAJ,EAAA,CAIA,GAAAA,EAAA,CACA,IAAAK,EAAAnC,EAAAY,GAAAG,QAAA,EACA,GAAAoB,EAAAnC,EAAAe,QAAA,CACAf,EAAAe,QAAAoB,GAIA,YAGA,aAIA,SAAAC,EAAAjD,GACA,WAAA1B,EAAA,SAAAvE,EAAAC,GACA,IAAAkJ,EAAA,IAAAC,WACAD,EAAAb,QAAArI,EACAkJ,EAAAE,UAAA,SAAApO,GACA,IAAAqO,EAAAC,KAAAtO,EAAAuO,OAAApJ,QAAA,IACAJ,EAAA,CACAyJ,4BAAA,KACA5M,KAAAyM,EACAhF,KAAA2B,EAAA3B,QAGA6E,EAAAO,mBAAAzD,KAKA,SAAA0D,EAAAC,GACA,IAAAC,EAAAtE,EAAAuE,KAAAF,EAAA/M,OACA,OAAA4G,EAAA,CAAAoG,GAAA,CAAoCvF,KAAAsF,EAAAtF,OAIpC,SAAAyF,EAAAhK,GACA,OAAAA,KAAA0J,4BAOA,SAAAO,EAAAvF,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAAlB,EAAA4L,aAAA5K,KAAA,WACA,IAAA0H,EAAA3B,EAAA/G,EAAA6L,QAAArG,MAEA,GAAAkD,KAAAG,QAAA,CACA,OAAAH,EAAAG,WAIAxC,EAAAnF,EAAAkF,KACA,OAAAlF,EAMA,SAAA4K,EAAArD,GACAD,EAAAC,GAEA,IAAAC,EAAA3B,EAAA0B,EAAAjD,MACA,IAAAuG,EAAArD,EAAAqD,QAEA,QAAA3O,EAAA,EAAmBA,EAAA2O,EAAAtO,OAAoBL,IAAA,CACvC,IAAA4O,EAAAD,EAAA3O,GACA,GAAA4O,EAAAH,QAAAxC,GAAA,CACA2C,EAAAH,QAAAxC,GAAAC,QACA0C,EAAAH,QAAAxC,GAAA,MAGAZ,EAAAY,GAAA,KAEA,OAAAc,EAAA1B,GAAAzH,KAAA,SAAAqI,GACAZ,EAAAY,KACA,GAAAgB,EAAA5B,GAAA,CAEA,OAAA2B,EAAA3B,GAEA,OAAAY,IACKrI,KAAA,SAAAqI,GAGLZ,EAAAY,GAAAX,EAAAW,KACA,QAAAjM,EAAA,EAAuBA,EAAA2O,EAAAtO,OAAoBL,IAAA,CAC3C2O,EAAA3O,GAAAyO,QAAAxC,QAEK,kBAAAJ,GACLD,EAAAP,EAAAQ,GACA,MAAAA,IAMA,SAAAgD,EAAAxD,EAAAyD,EAAA9F,EAAA+F,GACA,GAAAA,IAAAC,UAAA,CACAD,EAAA,EAGA,IACA,IAAAE,EAAA5D,EAAAY,GAAA1B,YAAAc,EAAAoB,UAAAqC,GACA9F,EAAA,KAAAiG,GACK,MAAApD,GACL,GAAAkD,EAAA,KAAA1D,EAAAY,IAAAJ,EAAAzD,OAAA,qBAAAyD,EAAAzD,OAAA,kBACA,OAAAU,EAAAvE,UAAAX,KAAA,WACA,IAAAyH,EAAAY,IAAAJ,EAAAzD,OAAA,kBAAAiD,EAAAY,GAAAmB,iBAAAC,SAAAhC,EAAAoB,YAAApB,EAAAe,SAAAf,EAAAY,GAAAG,QAAA,CAEA,GAAAf,EAAAY,GAAA,CACAZ,EAAAe,QAAAf,EAAAY,GAAAG,QAAA,EAGA,OAAAY,EAAA3B,MAEazH,KAAA,WACb,OAAA8K,EAAArD,GAAAzH,KAAA,WACAiL,EAAAxD,EAAAyD,EAAA9F,EAAA+F,EAAA,OAEa,SAAA/F,GAGbA,EAAA6C,IAIA,SAAAG,IACA,OAEA2C,QAAA,GAEA1C,GAAA,KAEAR,QAAA,KAEAD,mBAAA,IAMA,SAAA0D,EAAAC,GACA,IAAAvM,EAAAD,KACA,IAAA0I,EAAA,CACAY,GAAA,MAGA,GAAAkD,EAAA,CACA,QAAAnP,KAAAmP,EAAA,CACA9D,EAAArL,GAAAmP,EAAAnP,IAKA,IAAAsL,EAAA3B,EAAA0B,EAAAjD,MAGA,IAAAkD,EAAA,CACAA,EAAAU,IAEArC,EAAA0B,EAAAjD,MAAAkD,EAIAA,EAAAqD,QAAAjM,KAAAE,GAGA,IAAAA,EAAA4L,WAAA,CACA5L,EAAA4L,WAAA5L,EAAAwM,MACAxM,EAAAwM,MAAAb,EAIA,IAAAc,EAAA,GAEA,SAAAC,IAGA,OAAAxG,EAAAvE,UAGA,QAAAgL,EAAA,EAAmBA,EAAAjE,EAAAqD,QAAAtO,OAA8BkP,IAAA,CACjD,IAAAX,EAAAtD,EAAAqD,QAAAY,GACA,GAAAX,IAAAhM,EAAA,CAEAyM,EAAA3M,KAAAkM,EAAAJ,aAAA,SAAAc,KAKA,IAAAX,EAAArD,EAAAqD,QAAAa,MAAA,GAIA,OAAA1G,EAAApD,IAAA2J,GAAAzL,KAAA,WACAyH,EAAAY,GAAAX,EAAAW,GAEA,OAAAc,EAAA1B,KACKzH,KAAA,SAAAqI,GACLZ,EAAAY,KACA,GAAAgB,EAAA5B,EAAAzI,EAAA6M,eAAArD,SAAA,CAEA,OAAAY,EAAA3B,GAEA,OAAAY,IACKrI,KAAA,SAAAqI,GACLZ,EAAAY,GAAAX,EAAAW,KACArJ,EAAA6L,QAAApD,EAEA,QAAAqE,EAAA,EAAuBA,EAAAf,EAAAtO,OAAoBqP,IAAA,CAC3C,IAAAd,EAAAD,EAAAe,GACA,GAAAd,IAAAhM,EAAA,CAEAgM,EAAAH,QAAAxC,GAAAZ,EAAAY,GACA2C,EAAAH,QAAArC,QAAAf,EAAAe,YAMA,SAAAuD,EAAAvG,EAAAJ,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACAiL,EAAAjM,EAAA6L,QAAA7E,EAAA,SAAAiC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WACA,IAAAoD,EAAAD,EAAAE,IAAA1G,GAEAyG,EAAA/C,UAAA,WACA,IAAAxI,EAAAuL,EAAAlL,OACA,GAAAL,IAAA0K,UAAA,CACA1K,EAAA,KAEA,GAAAgK,EAAAhK,GAAA,CACAA,EAAA4J,EAAA5J,GAEAC,EAAAD,IAGAuL,EAAAhD,QAAA,WACArI,EAAAqL,EAAA7K,QAEiB,MAAAxF,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAIA,SAAAiM,EAAArJ,EAAAsC,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACAiL,EAAAjM,EAAA6L,QAAA7E,EAAA,SAAAiC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WACA,IAAAoD,EAAAD,EAAAI,aACA,IAAAC,EAAA,EAEAJ,EAAA/C,UAAA,WACA,IAAAoD,EAAAL,EAAAlL,OAEA,GAAAuL,EAAA,CACA,IAAA5L,EAAA4L,EAAA5L,MACA,GAAAgK,EAAAhK,GAAA,CACAA,EAAA4J,EAAA5J,GAEA,IAAAK,EAAA+B,EAAApC,EAAA4L,EAAA9G,IAAA6G,KAKA,GAAAtL,SAAA,GACAJ,EAAAI,OAC6B,CAC7BuL,EAAA,mBAEyB,CACzB3L,MAIAsL,EAAAhD,QAAA,WACArI,EAAAqL,EAAA7K,QAEiB,MAAAxF,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GAEA,OAAAlF,EAGA,SAAAqM,EAAA/G,EAAA9E,EAAA0E,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA,IAAA6G,EACAzI,EAAAwM,QAAAxL,KAAA,WACAyH,EAAAzI,EAAA6L,QACA,GAAA5I,EAAAzF,KAAAkE,KAAA,iBACA,OAAA6G,EAAAE,EAAAY,IAAArI,KAAA,SAAAwM,GACA,GAAAA,EAAA,CACA,OAAA9L,EAEA,OAAAmJ,EAAAnJ,KAGA,OAAAA,IACSV,KAAA,SAAAU,GACTuK,EAAAjM,EAAA6L,QAAA5E,EAAA,SAAAgC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WAMA,GAAAnI,IAAA,MACAA,EAAA0K,UAGA,IAAAa,EAAAD,EAAAlF,IAAApG,EAAA8E,GAEAmB,EAAAO,WAAA,WAOA,GAAAxG,IAAA0K,UAAA,CACA1K,EAAA,KAGAC,EAAAD,IAEAiG,EAAAI,QAAAJ,EAAAsC,QAAA,WACA,IAAAhB,EAAAgE,EAAA7K,MAAA6K,EAAA7K,MAAA6K,EAAAtF,YAAAvF,MACAR,EAAAqH,IAEiB,MAAArM,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAuM,EAAAjH,EAAAJ,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACAiL,EAAAjM,EAAA6L,QAAA5E,EAAA,SAAAgC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WAMA,IAAAoD,EAAAD,EAAA,UAAAxG,GACAmB,EAAAO,WAAA,WACAvG,KAGAgG,EAAAsC,QAAA,WACArI,EAAAqL,EAAA7K,QAKAuF,EAAAI,QAAA,WACA,IAAAkB,EAAAgE,EAAA7K,MAAA6K,EAAA7K,MAAA6K,EAAAtF,YAAAvF,MACAR,EAAAqH,IAEiB,MAAArM,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAwM,EAAAtH,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACAiL,EAAAjM,EAAA6L,QAAA5E,EAAA,SAAAgC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WACA,IAAAoD,EAAAD,EAAAU,QAEA/F,EAAAO,WAAA,WACAvG,KAGAgG,EAAAI,QAAAJ,EAAAsC,QAAA,WACA,IAAAhB,EAAAgE,EAAA7K,MAAA6K,EAAA7K,MAAA6K,EAAAtF,YAAAvF,MACAR,EAAAqH,IAEiB,MAAArM,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAzD,EAAA2I,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACAiL,EAAAjM,EAAA6L,QAAA7E,EAAA,SAAAiC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WACA,IAAAoD,EAAAD,EAAAW,QAEAV,EAAA/C,UAAA,WACAvI,EAAAsL,EAAAlL,SAGAkL,EAAAhD,QAAA,WACArI,EAAAqL,EAAA7K,QAEiB,MAAAxF,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAsF,EAAA1J,EAAAsJ,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA,GAAA9E,EAAA,GACA6E,EAAA,MAEA,OAGA3B,EAAAwM,QAAAxL,KAAA,WACAiL,EAAAjM,EAAA6L,QAAA7E,EAAA,SAAAiC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WACA,IAAA+D,EAAA,MACA,IAAAX,EAAAD,EAAAI,aAEAH,EAAA/C,UAAA,WACA,IAAAoD,EAAAL,EAAAlL,OACA,IAAAuL,EAAA,CAEA3L,EAAA,MAEA,OAGA,GAAA7E,IAAA,GAGA6E,EAAA2L,EAAA9G,SACyB,CACzB,IAAAoH,EAAA,CAGAA,EAAA,KACAN,EAAAO,QAAA/Q,OAC6B,CAE7B6E,EAAA2L,EAAA9G,QAKAyG,EAAAhD,QAAA,WACArI,EAAAqL,EAAA7K,QAEiB,MAAAxF,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAA4M,EAAA1H,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACAiL,EAAAjM,EAAA6L,QAAA7E,EAAA,SAAAiC,EAAAtB,GACA,GAAAsB,EAAA,CACA,OAAArH,EAAAqH,GAGA,IACA,IAAA+D,EAAArF,EAAAE,YAAA7H,EAAA6L,QAAAhC,WACA,IAAAoD,EAAAD,EAAAI,aACA,IAAAU,EAAA,GAEAb,EAAA/C,UAAA,WACA,IAAAoD,EAAAL,EAAAlL,OAEA,IAAAuL,EAAA,CACA3L,EAAAmM,GACA,OAGAA,EAAAhO,KAAAwN,EAAA9G,KACA8G,EAAA,eAGAL,EAAAhD,QAAA,WACArI,EAAAqL,EAAA7K,QAEiB,MAAAxF,GACjBgF,EAAAhF,QAGS,SAAAgF,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAA6M,EAAAxB,EAAAnG,GACAA,EAAAQ,EAAArE,MAAAxC,KAAAyC,WAEA,IAAAwL,EAAAjO,KAAAkO,SACA1B,aAAA,YAAAA,GAAA,GACA,IAAAA,EAAA/G,KAAA,CACA+G,EAAA/G,KAAA+G,EAAA/G,MAAAwI,EAAAxI,KACA+G,EAAA1C,UAAA0C,EAAA1C,WAAAmE,EAAAnE,UAGA,IAAA7J,EAAAD,KACA,IAAAmB,EACA,IAAAqL,EAAA/G,KAAA,CACAtE,EAAAgF,EAAAtE,OAAA,yBACK,CACL,IAAAsM,EAAA3B,EAAA/G,OAAAwI,EAAAxI,MAAAxF,EAAA6L,QAAAxC,GAEA,IAAA8E,EAAAD,EAAAhI,EAAAvE,QAAA3B,EAAA6L,QAAAxC,IAAAc,EAAAoC,GAAAvL,KAAA,SAAAqI,GACA,IAAAX,EAAA3B,EAAAwF,EAAA/G,MACA,IAAAuG,EAAArD,EAAAqD,QACArD,EAAAW,KACA,QAAAjM,EAAA,EAA2BA,EAAA2O,EAAAtO,OAAoBL,IAAA,CAC/C2O,EAAA3O,GAAAyO,QAAAxC,KAEA,OAAAA,IAGA,IAAAkD,EAAA1C,UAAA,CACA3I,EAAAiN,EAAAnN,KAAA,SAAAqI,GACAb,EAAA+D,GAEA,IAAA7D,EAAA3B,EAAAwF,EAAA/G,MACA,IAAAuG,EAAArD,EAAAqD,QAEA1C,EAAAC,QACA,QAAAlM,EAAA,EAA+BA,EAAA2O,EAAAtO,OAAoBL,IAAA,CACnD,IAAA4O,EAAAD,EAAA3O,GACA4O,EAAAH,QAAAxC,GAAA,KAGA,IAAA+E,EAAA,IAAAlI,EAAA,SAAAvE,EAAAC,GACA,IAAAqL,EAAAzI,EAAA6J,eAAA9B,EAAA/G,MAEAyH,EAAAhD,QAAAgD,EAAAqB,UAAA,SAAArF,GACA,IAAAI,EAAA4D,EAAAlL,OACA,GAAAsH,EAAA,CACAA,EAAAC,QAEA1H,EAAAqH,IAGAgE,EAAA/C,UAAA,WACA,IAAAb,EAAA4D,EAAAlL,OACA,GAAAsH,EAAA,CACAA,EAAAC,QAEA3H,EAAA0H,MAIA,OAAA+E,EAAApN,KAAA,SAAAqI,GACAX,EAAAW,KACA,QAAAjM,EAAA,EAAmCA,EAAA2O,EAAAtO,OAAoBL,IAAA,CACvD,IAAAmR,EAAAxC,EAAA3O,GACA0L,EAAAyF,EAAA1C,YAEiB,kBAAA5C,IACjBD,EAAAuD,EAAAtD,IAAA/C,EAAAvE,WAAA,uBACA,MAAAsH,UAGS,CACT/H,EAAAiN,EAAAnN,KAAA,SAAAqI,GACA,IAAAA,EAAAmB,iBAAAC,SAAA8B,EAAA1C,WAAA,CACA,OAGA,IAAAG,EAAAX,EAAAG,QAAA,EAEAhB,EAAA+D,GAEA,IAAA7D,EAAA3B,EAAAwF,EAAA/G,MACA,IAAAuG,EAAArD,EAAAqD,QAEA1C,EAAAC,QACA,QAAAlM,EAAA,EAA+BA,EAAA2O,EAAAtO,OAAoBL,IAAA,CACnD,IAAA4O,EAAAD,EAAA3O,GACA4O,EAAAH,QAAAxC,GAAA,KACA2C,EAAAH,QAAArC,QAAAQ,EAGA,IAAAwE,EAAA,IAAAtI,EAAA,SAAAvE,EAAAC,GACA,IAAAqL,EAAAzI,EAAAkF,KAAA6C,EAAA/G,KAAAwE,GAEAiD,EAAAhD,QAAA,SAAAhB,GACA,IAAAI,EAAA4D,EAAAlL,OACAsH,EAAAC,QACA1H,EAAAqH,IAGAgE,EAAAtD,gBAAA,WACA,IAAAN,EAAA4D,EAAAlL,OACAsH,EAAAoF,kBAAAlC,EAAA1C,YAGAoD,EAAA/C,UAAA,WACA,IAAAb,EAAA4D,EAAAlL,OACAsH,EAAAC,QACA3H,EAAA0H,MAIA,OAAAmF,EAAAxN,KAAA,SAAAqI,GACAX,EAAAW,KACA,QAAAsD,EAAA,EAAmCA,EAAAZ,EAAAtO,OAAoBkP,IAAA,CACvD,IAAA+B,EAAA3C,EAAAY,GACA+B,EAAA7C,QAAAxC,KACAP,EAAA4F,EAAA7C,YAEiB,kBAAA5C,IACjBD,EAAAuD,EAAAtD,IAAA/C,EAAAvE,WAAA,uBACA,MAAAsH,OAMA9C,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,IAAAyN,EAAA,CACAC,QAAA,eACAtC,eACAuC,SAAApK,IACA0I,UACAJ,UACAQ,UACAE,aACAC,QACAjQ,SACA+I,MACAsH,OACAC,gBAGA,SAAAe,IACA,cAAAnK,eAAA,WAMA,IAAAoK,EAAA,mEAEA,IAAAC,EAAA,uBACA,IAAAC,EAAA,gCAEA,IAAAC,EAAA,YACA,IAAAC,EAAAD,EAAAzR,OAGA,IAAA2R,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAA,OACA,IAAAC,GAAAZ,EAAAC,GAAA3R,OAEA,IAAAuS,GAAAhN,OAAAlC,UAAAmC,SAEA,SAAAgN,GAAAC,GAEA,IAAAC,EAAAD,EAAAzS,OAAA,IACA,IAAAkC,EAAAuQ,EAAAzS,OACA,IAAAL,EACA,IAAAgT,EAAA,EACA,IAAAC,EAAAC,EAAAC,EAAAC,EAEA,GAAAN,IAAAzS,OAAA,UACA0S,IACA,GAAAD,IAAAzS,OAAA,UACA0S,KAIA,IAAAM,EAAA,IAAApJ,YAAA8I,GACA,IAAAO,EAAA,IAAAnJ,WAAAkJ,GAEA,IAAArT,EAAA,EAAeA,EAAAuC,EAASvC,GAAA,GACxBiT,EAAAtB,EAAA7J,QAAAgL,EAAA9S,IACAkT,EAAAvB,EAAA7J,QAAAgL,EAAA9S,EAAA,IACAmT,EAAAxB,EAAA7J,QAAAgL,EAAA9S,EAAA,IACAoT,EAAAzB,EAAA7J,QAAAgL,EAAA9S,EAAA,IAGAsT,EAAAN,KAAAC,GAAA,EAAAC,GAAA,EACAI,EAAAN,MAAAE,EAAA,OAAAC,GAAA,EACAG,EAAAN,MAAAG,EAAA,MAAAC,EAAA,GAEA,OAAAC,EAKA,SAAAE,GAAAF,GAEA,IAAAC,EAAA,IAAAnJ,WAAAkJ,GACA,IAAAG,EAAA,GACA,IAAAxT,EAEA,IAAAA,EAAA,EAAeA,EAAAsT,EAAAjT,OAAkBL,GAAA,GAEjCwT,GAAA7B,EAAA2B,EAAAtT,IAAA,GACAwT,GAAA7B,GAAA2B,EAAAtT,GAAA,MAAAsT,EAAAtT,EAAA,OACAwT,GAAA7B,GAAA2B,EAAAtT,EAAA,UAAAsT,EAAAtT,EAAA,OACAwT,GAAA7B,EAAA2B,EAAAtT,EAAA,OAGA,GAAAsT,EAAAjT,OAAA,OACAmT,IAAAC,UAAA,EAAAD,EAAAnT,OAAA,YACK,GAAAiT,EAAAjT,OAAA,OACLmT,IAAAC,UAAA,EAAAD,EAAAnT,OAAA,QAGA,OAAAmT,EAMA,SAAAE,GAAApP,EAAA0E,GACA,IAAA2K,EAAA,GACA,GAAArP,EAAA,CACAqP,EAAAf,GAAAxS,KAAAkE,GAOA,GAAAA,IAAAqP,IAAA,wBAAArP,EAAA+O,QAAAT,GAAAxS,KAAAkE,EAAA+O,UAAA,yBAGA,IAAAA,EACA,IAAAO,EAAA9B,EAEA,GAAAxN,aAAA2F,YAAA,CACAoJ,EAAA/O,EACAsP,GAAA5B,OACS,CACTqB,EAAA/O,EAAA+O,OAEA,GAAAM,IAAA,sBACAC,GAAA1B,QACa,GAAAyB,IAAA,uBACbC,GAAAzB,QACa,GAAAwB,IAAA,8BACbC,GAAAxB,QACa,GAAAuB,IAAA,uBACbC,GAAAvB,QACa,GAAAsB,IAAA,wBACbC,GAAArB,QACa,GAAAoB,IAAA,uBACbC,GAAAtB,QACa,GAAAqB,IAAA,wBACbC,GAAApB,QACa,GAAAmB,IAAA,yBACbC,GAAAnB,QACa,GAAAkB,IAAA,yBACbC,GAAAlB,OACa,CACb1J,EAAA,IAAA/I,MAAA,wCAIA+I,EAAA4K,EAAAL,GAAAF,SACK,GAAAM,IAAA,iBAEL,IAAAE,EAAA,IAAAlG,WAEAkG,EAAAC,OAAA,WAEA,IAAAC,EAAAnC,EAAAtN,EAAAuE,KAAA,IAAA0K,GAAA5Q,KAAAgC,QAEAqE,EAAA8I,EAAAG,GAAA8B,IAGAF,EAAAG,kBAAA1P,OACK,CACL,IACA0E,EAAAiL,KAAAC,UAAA5P,IACS,MAAA9E,GACT6J,QAAArE,MAAA,8CAAAV,GAEA0E,EAAA,KAAAxJ,KAaA,SAAA2U,GAAA7P,GAIA,GAAAA,EAAAmP,UAAA,EAAA1B,KAAAD,EAAA,CACA,OAAAmC,KAAAG,MAAA9P,GAMA,IAAAwO,EAAAxO,EAAAmP,UAAAd,IACA,IAAA9J,EAAAvE,EAAAmP,UAAA1B,EAAAY,IAEA,IAAA0B,EAGA,GAAAxL,IAAAoJ,IAAAJ,EAAArK,KAAAsL,GAAA,CACA,IAAAwB,EAAAxB,EAAA9H,MAAA6G,GACAwC,EAAAC,EAAA,GACAxB,IAAAW,UAAAa,EAAA,GAAAjU,QAEA,IAAAgT,EAAAR,GAAAC,GAIA,OAAAjK,GACA,KAAAmJ,GACA,OAAAqB,EACA,KAAApB,GACA,OAAAjK,EAAA,CAAAqL,GAAA,CAAyCxK,KAAAwL,IACzC,KAAAnC,GACA,WAAAqC,UAAAlB,GACA,KAAAlB,GACA,WAAAhI,WAAAkJ,GACA,KAAAjB,GACA,WAAAoC,kBAAAnB,GACA,KAAAhB,GACA,WAAAoC,WAAApB,GACA,KAAAd,GACA,WAAAmC,YAAArB,GACA,KAAAf,GACA,WAAAqC,WAAAtB,GACA,KAAAb,GACA,WAAAoC,YAAAvB,GACA,KAAAZ,GACA,WAAAoC,aAAAxB,GACA,KAAAX,GACA,WAAAoC,aAAAzB,GACA,QACA,UAAApT,MAAA,gBAAA4I,IAIA,IAAAkM,GAAA,CACArB,aACAS,eACAtB,kBACAU,mBAaA,SAAAyB,GAAAvV,EAAA4L,EAAArC,EAAAE,GACAzJ,EAAAwV,WAAA,8BAAA5J,EAAAoB,UAAA,qDAAAzD,EAAAE,GAKA,SAAAgM,GAAA/F,GACA,IAAAvM,EAAAD,KACA,IAAA0I,EAAA,CACAY,GAAA,MAGA,GAAAkD,EAAA,CACA,QAAAnP,KAAAmP,EAAA,CACA9D,EAAArL,UAAAmP,EAAAnP,KAAA,SAAAmP,EAAAnP,GAAA6F,WAAAsJ,EAAAnP,IAIA,IAAAmV,EAAA,IAAArM,EAAA,SAAAvE,EAAAC,GAGA,IACA6G,EAAAY,GAAA1E,aAAA8D,EAAAjD,KAAAmB,OAAA8B,EAAAe,SAAAf,EAAA+J,YAAA/J,EAAAgK,MACS,MAAA7V,GACT,OAAAgF,EAAAhF,GAIA6L,EAAAY,GAAA1B,YAAA,SAAA9K,GACAuV,GAAAvV,EAAA4L,EAAA,WACAzI,EAAA6L,QAAApD,EACA9G,KACa,SAAA9E,EAAAuF,GACbR,EAAAQ,MAESR,KAGT6G,EAAAiK,WAAAP,GACA,OAAAI,EAGA,SAAAI,GAAA9V,EAAA4L,EAAAmK,EAAAC,EAAAzM,EAAAE,GACAzJ,EAAAwV,WAAAO,EAAAC,EAAAzM,EAAA,SAAAvJ,EAAAuF,GACA,GAAAA,EAAA9E,OAAA8E,EAAA0Q,WAAA,CACAjW,EAAAwV,WAAA,qEAAA5J,EAAAoB,WAAA,SAAAhN,EAAAkW,GACA,IAAAA,EAAAC,KAAAvV,OAAA,CAGA2U,GAAAvV,EAAA4L,EAAA,WACA5L,EAAAwV,WAAAO,EAAAC,EAAAzM,EAAAE,IACqBA,OACJ,CACjBA,EAAAzJ,EAAAuF,KAEakE,OACJ,CACTA,EAAAzJ,EAAAuF,KAEKkE,GAGL,SAAA2M,GAAAzM,EAAAJ,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACApD,EAAAY,GAAA1B,YAAA,SAAA9K,GACA8V,GAAA9V,EAAA4L,EAAA,iBAAAA,EAAAoB,UAAA,0BAAArD,GAAA,SAAA3J,EAAAkW,GACA,IAAAhR,EAAAgR,EAAAC,KAAAvV,OAAAsV,EAAAC,KAAAE,KAAA,GAAAxR,MAAA,KAIA,GAAAK,EAAA,CACAA,EAAA0G,EAAAiK,WAAAnB,YAAAxP,GAGAJ,EAAAI,IACiB,SAAAlF,EAAAuF,GACjBR,EAAAQ,SAGS,SAAAR,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAiS,GAAArP,EAAAsC,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QAEApD,EAAAY,GAAA1B,YAAA,SAAA9K,GACA8V,GAAA9V,EAAA4L,EAAA,iBAAAA,EAAAoB,UAAA,YAAAhN,EAAAkW,GACA,IAAAC,EAAAD,EAAAC,KACA,IAAAvV,EAAAuV,EAAAvV,OAEA,QAAAL,EAAA,EAAmCA,EAAAK,EAAYL,IAAA,CAC/C,IAAA8V,EAAAF,EAAAE,KAAA9V,GACA,IAAA2E,EAAAmR,EAAAxR,MAIA,GAAAK,EAAA,CACAA,EAAA0G,EAAAiK,WAAAnB,YAAAxP,GAGAA,EAAA+B,EAAA/B,EAAAmR,EAAA1M,IAAApJ,EAAA,GAIA,GAAA2E,SAAA,GACAJ,EAAAI,GACA,QAIAJ,KACiB,SAAA9E,EAAAuF,GACjBR,EAAAQ,SAGS,SAAAR,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAkS,GAAA5M,EAAA9E,EAAA0E,EAAAiN,GACA,IAAArT,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WAIA,GAAAU,IAAA0K,UAAA,CACA1K,EAAA,KAIA,IAAA4R,EAAA5R,EAEA,IAAA+G,EAAAzI,EAAA6L,QACApD,EAAAiK,WAAA5B,UAAApP,EAAA,SAAAA,EAAAU,GACA,GAAAA,EAAA,CACAR,EAAAQ,OACiB,CACjBqG,EAAAY,GAAA1B,YAAA,SAAA9K,GACA8V,GAAA9V,EAAA4L,EAAA,0BAAAA,EAAAoB,UAAA,kCAAArD,EAAA9E,GAAA,WACAC,EAAA2R,IACyB,SAAAzW,EAAAuF,GACzBR,EAAAQ,MAEqB,SAAAmR,GAGrB,GAAAA,EAAAjW,OAAAiW,EAAAC,UAAA,CAQA,GAAAH,EAAA,GACA1R,EAAAyR,GAAA7Q,MAAAvC,EAAA,CAAAwG,EAAA8M,EAAAlN,EAAAiN,EAAA,KACA,OAEAzR,EAAA2R,WAKS,SAAA3R,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAuS,GAAAjN,EAAA9E,EAAA0E,GACA,OAAAgN,GAAA7Q,MAAAxC,KAAA,CAAAyG,EAAA9E,EAAA0E,EAAA,IAGA,SAAAsN,GAAAlN,EAAAJ,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACApD,EAAAY,GAAA1B,YAAA,SAAA9K,GACA8V,GAAA9V,EAAA4L,EAAA,eAAAA,EAAAoB,UAAA,kBAAArD,GAAA,WACA7E,KACiB,SAAA9E,EAAAuF,GACjBR,EAAAQ,SAGS,SAAAR,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAKA,SAAAyS,GAAAvN,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACApD,EAAAY,GAAA1B,YAAA,SAAA9K,GACA8V,GAAA9V,EAAA4L,EAAA,eAAAA,EAAAoB,UAAA,cACAlI,KACiB,SAAA9E,EAAAuF,GACjBR,EAAAQ,SAGS,SAAAR,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAKA,SAAA0S,GAAAxN,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACApD,EAAAY,GAAA1B,YAAA,SAAA9K,GAEA8V,GAAA9V,EAAA4L,EAAA,+BAAAA,EAAAoB,UAAA,YAAAhN,EAAAkW,GACA,IAAAhR,EAAAgR,EAAAC,KAAAE,KAAA,GAAAW,EACAlS,EAAAI,IACiB,SAAAlF,EAAAuF,GACjBR,EAAAQ,SAGS,SAAAR,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAUA,SAAA4S,GAAAhX,EAAAsJ,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACApD,EAAAY,GAAA1B,YAAA,SAAA9K,GACA8V,GAAA9V,EAAA4L,EAAA,mBAAAA,EAAAoB,UAAA,yBAAA/M,EAAA,YAAAD,EAAAkW,GACA,IAAAhR,EAAAgR,EAAAC,KAAAvV,OAAAsV,EAAAC,KAAAE,KAAA,GAAA1M,IAAA,KACA7E,EAAAI,IACiB,SAAAlF,EAAAuF,GACjBR,EAAAQ,SAGS,SAAAR,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAA6S,GAAA3N,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA5B,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACApD,EAAAY,GAAA1B,YAAA,SAAA9K,GACA8V,GAAA9V,EAAA4L,EAAA,mBAAAA,EAAAoB,UAAA,YAAAhN,EAAAkW,GACA,IAAAjF,EAAA,GAEA,QAAA1Q,EAAA,EAAmCA,EAAA2V,EAAAC,KAAAvV,OAAyBL,IAAA,CAC5D0Q,EAAAhO,KAAAiT,EAAAC,KAAAE,KAAA9V,GAAAoJ,KAGA7E,EAAAmM,IACiB,SAAAjR,EAAAuF,GACjBR,EAAAQ,SAGS,SAAAR,KAGTuE,EAAAjF,EAAAkF,GACA,OAAAlF,EAKA,SAAA8S,GAAA3K,GACA,WAAAnD,EAAA,SAAAvE,EAAAC,GACAyH,EAAA1B,YAAA,SAAA9K,GACAA,EAAAwV,WAAA,6GAAAxV,EAAAkW,GACA,IAAAkB,EAAA,GAEA,QAAA7W,EAAA,EAA+BA,EAAA2V,EAAAC,KAAAvV,OAAyBL,IAAA,CACxD6W,EAAAnU,KAAAiT,EAAAC,KAAAE,KAAA9V,GAAAoI,MAGA7D,EAAA,CACA0H,KACA4K,gBAEa,SAAApX,EAAAuF,GACbR,EAAAQ,MAES,SAAAmR,GACT3R,EAAA2R,OAKA,SAAAW,GAAA3H,EAAAnG,GACAA,EAAAQ,EAAArE,MAAAxC,KAAAyC,WAEA,IAAAwL,EAAAjO,KAAAkO,SACA1B,aAAA,YAAAA,GAAA,GACA,IAAAA,EAAA/G,KAAA,CACA+G,EAAA/G,KAAA+G,EAAA/G,MAAAwI,EAAAxI,KACA+G,EAAA1C,UAAA0C,EAAA1C,WAAAmE,EAAAnE,UAGA,IAAA7J,EAAAD,KACA,IAAAmB,EACA,IAAAqL,EAAA/G,KAAA,CACAtE,EAAAgF,EAAAtE,OAAA,yBACK,CACLV,EAAA,IAAAgF,EAAA,SAAAvE,GACA,IAAA0H,EACA,GAAAkD,EAAA/G,OAAAwI,EAAAxI,KAAA,CAEA6D,EAAArJ,EAAA6L,QAAAxC,OACa,CACbA,EAAA1E,aAAA4H,EAAA/G,KAAA,SAGA,IAAA+G,EAAA1C,UAAA,CAEAlI,EAAAqS,GAAA3K,QACa,CACb1H,EAAA,CACA0H,KACA4K,WAAA,CAAA1H,EAAA1C,gBAGS7I,KAAA,SAAAmT,GACT,WAAAjO,EAAA,SAAAvE,EAAAC,GACAuS,EAAA9K,GAAA1B,YAAA,SAAA9K,GACA,SAAAuX,EAAAvK,GACA,WAAA3D,EAAA,SAAAvE,EAAAC,GACA/E,EAAAwV,WAAA,wBAAAxI,EAAA,cACAlI,KAC6B,SAAA9E,EAAAuF,GAC7BR,EAAAQ,OAKA,IAAAiS,EAAA,GACA,QAAAjX,EAAA,EAAAuC,EAAAwU,EAAAF,WAAAxW,OAA0EL,EAAAuC,EAASvC,IAAA,CACnFiX,EAAAvU,KAAAsU,EAAAD,EAAAF,WAAA7W,KAGA8I,EAAApD,IAAAuR,GAAArT,KAAA,WACAW,MACqB,kBAAA/E,GACrBgF,EAAAhF,MAEiB,SAAA2W,GACjB3R,EAAA2R,SAMApN,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,IAAAoT,GAAA,CACA1F,QAAA,gBACAtC,aAAAgG,GACAzD,SAAAC,IACA3B,QAAAgG,GACApG,QAAAkG,GACA1F,QAAAkG,GACAhG,WAAAiG,GACAhG,MAAAiG,GACAlW,OAAAmW,GACApN,IAAAsN,GACAhG,KAAAiG,GACAhG,aAAAmG,IAGA,SAAAK,KACA,IACA,cAAAC,eAAA,yBAAAA,gBAEAA,aAAAjH,QACK,MAAA3Q,GACL,cAIA,SAAA6X,GAAAlI,EAAAmI,GACA,IAAAC,EAAApI,EAAA/G,KAAA,IAEA,GAAA+G,EAAA1C,YAAA6K,EAAA7K,UAAA,CACA8K,GAAApI,EAAA1C,UAAA,IAEA,OAAA8K,EAIA,SAAAC,KACA,IAAAC,EAAA,4BAEA,IACAL,aAAAjH,QAAAsH,EAAA,MACAL,aAAA/G,WAAAoH,GAEA,aACK,MAAAjY,GACL,aAQA,SAAAkY,KACA,OAAAF,MAAAJ,aAAA/W,OAAA,EAIA,SAAAsX,GAAAxI,GACA,IAAAvM,EAAAD,KACA,IAAA0I,EAAA,GACA,GAAA8D,EAAA,CACA,QAAAnP,KAAAmP,EAAA,CACA9D,EAAArL,GAAAmP,EAAAnP,IAIAqL,EAAAkM,UAAAF,GAAAlI,EAAAvM,EAAA6M,gBAEA,IAAAiI,KAAA,CACA,OAAA5O,EAAAtE,SAGA5B,EAAA6L,QAAApD,EACAA,EAAAiK,WAAAP,GAEA,OAAAjM,EAAAvE,UAKA,SAAAqT,GAAA5O,GACA,IAAApG,EAAAD,KACA,IAAAmB,EAAAlB,EAAAwM,QAAAxL,KAAA,WACA,IAAA2T,EAAA3U,EAAA6L,QAAA8I,UAEA,QAAAvX,EAAAoX,aAAA/W,OAAA,EAA6CL,GAAA,EAAQA,IAAA,CACrD,IAAAoJ,EAAAgO,aAAAhO,IAAApJ,GAEA,GAAAoJ,EAAAtB,QAAAyP,KAAA,GACAH,aAAA/G,WAAAjH,OAKAL,EAAAjF,EAAAkF,GACA,OAAAlF,EAMA,SAAA+T,GAAAzO,EAAAJ,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAAlB,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACA,IAAA9J,EAAAyS,aAAAzH,QAAAtE,EAAAkM,UAAAnO,GAMA,GAAAzE,EAAA,CACAA,EAAA0G,EAAAiK,WAAAnB,YAAAxP,GAGA,OAAAA,IAGAoE,EAAAjF,EAAAkF,GACA,OAAAlF,EAIA,SAAAgU,GAAApR,EAAAsC,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAAlB,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACA,IAAA8I,EAAAlM,EAAAkM,UACA,IAAAQ,EAAAR,EAAAlX,OACA,IAAAA,EAAA+W,aAAA/W,OAQA,IAAA4P,EAAA,EAEA,QAAAjQ,EAAA,EAAuBA,EAAAK,EAAYL,IAAA,CACnC,IAAAoJ,EAAAgO,aAAAhO,IAAApJ,GACA,GAAAoJ,EAAAtB,QAAAyP,KAAA,GACA,SAEA,IAAAjT,EAAA8S,aAAAzH,QAAAvG,GAMA,GAAA9E,EAAA,CACAA,EAAA+G,EAAAiK,WAAAnB,YAAA7P,GAGAA,EAAAoC,EAAApC,EAAA8E,EAAAqK,UAAAsE,GAAA9H,KAEA,GAAA3L,SAAA,GACA,OAAAA,MAKAyE,EAAAjF,EAAAkF,GACA,OAAAlF,EAIA,SAAAkU,GAAAtY,EAAAsJ,GACA,IAAApG,EAAAD,KACA,IAAAmB,EAAAlB,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACA,IAAA9J,EACA,IACAA,EAAAyS,aAAAhO,IAAA1J,GACS,MAAAsF,GACTL,EAAA,KAIA,GAAAA,EAAA,CACAA,IAAA8O,UAAApI,EAAAkM,UAAAlX,QAGA,OAAAsE,IAGAoE,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAmU,GAAAjP,GACA,IAAApG,EAAAD,KACA,IAAAmB,EAAAlB,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACA,IAAApO,EAAA+W,aAAA/W,OACA,IAAAqQ,EAAA,GAEA,QAAA1Q,EAAA,EAAuBA,EAAAK,EAAYL,IAAA,CACnC,IAAAkY,EAAAd,aAAAhO,IAAApJ,GACA,GAAAkY,EAAApQ,QAAAuD,EAAAkM,aAAA,GACA7G,EAAAhO,KAAAwV,EAAAzE,UAAApI,EAAAkM,UAAAlX,UAIA,OAAAqQ,IAGA3H,EAAAjF,EAAAkF,GACA,OAAAlF,EAIA,SAAAqU,GAAAnP,GACA,IAAApG,EAAAD,KACA,IAAAmB,EAAAlB,EAAA8N,OAAA9M,KAAA,SAAA8M,GACA,OAAAA,EAAArQ,SAGA0I,EAAAjF,EAAAkF,GACA,OAAAlF,EAIA,SAAAsU,GAAAhP,EAAAJ,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAAlB,EAAAwM,QAAAxL,KAAA,WACA,IAAAyH,EAAAzI,EAAA6L,QACA2I,aAAA/G,WAAAhF,EAAAkM,UAAAnO,KAGAL,EAAAjF,EAAAkF,GACA,OAAAlF,EAOA,SAAAuU,GAAAjP,EAAA9E,EAAA0E,GACA,IAAApG,EAAAD,KAEAyG,EAAAD,EAAAC,GAEA,IAAAtF,EAAAlB,EAAAwM,QAAAxL,KAAA,WAGA,GAAAU,IAAA0K,UAAA,CACA1K,EAAA,KAIA,IAAA4R,EAAA5R,EAEA,WAAAwE,EAAA,SAAAvE,EAAAC,GACA,IAAA6G,EAAAzI,EAAA6L,QACApD,EAAAiK,WAAA5B,UAAApP,EAAA,SAAAA,EAAAU,GACA,GAAAA,EAAA,CACAR,EAAAQ,OACiB,CACjB,IACAoS,aAAAjH,QAAA9E,EAAAkM,UAAAnO,EAAA9E,GACAC,EAAA2R,GACqB,MAAA1W,GAGrB,GAAAA,EAAA4I,OAAA,sBAAA5I,EAAA4I,OAAA,8BACA5D,EAAAhF,GAEAgF,EAAAhF,WAOAuJ,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,SAAAwU,GAAAnJ,EAAAnG,GACAA,EAAAQ,EAAArE,MAAAxC,KAAAyC,WAEA+J,aAAA,YAAAA,GAAA,GACA,IAAAA,EAAA/G,KAAA,CACA,IAAAwI,EAAAjO,KAAAkO,SACA1B,EAAA/G,KAAA+G,EAAA/G,MAAAwI,EAAAxI,KACA+G,EAAA1C,UAAA0C,EAAA1C,WAAAmE,EAAAnE,UAGA,IAAA7J,EAAAD,KACA,IAAAmB,EACA,IAAAqL,EAAA/G,KAAA,CACAtE,EAAAgF,EAAAtE,OAAA,yBACK,CACLV,EAAA,IAAAgF,EAAA,SAAAvE,GACA,IAAA4K,EAAA1C,UAAA,CACAlI,EAAA4K,EAAA/G,KAAA,SACa,CACb7D,EAAA8S,GAAAlI,EAAAvM,EAAA6M,oBAES7L,KAAA,SAAA2T,GACT,QAAAvX,EAAAoX,aAAA/W,OAAA,EAAiDL,GAAA,EAAQA,IAAA,CACzD,IAAAoJ,EAAAgO,aAAAhO,IAAApJ,GAEA,GAAAoJ,EAAAtB,QAAAyP,KAAA,GACAH,aAAA/G,WAAAjH,OAMAL,EAAAjF,EAAAkF,GACA,OAAAlF,EAGA,IAAAyU,GAAA,CACA/G,QAAA,sBACAtC,aAAAyI,GACAlG,SAAA0F,KACApH,QAAA+H,GACAnI,QAAAkI,GACA1H,QAAAkI,GACAhI,WAAA+H,GACA9H,MAAAsH,GACAvX,OAAA8X,GACA/O,IAAA4O,GACAtH,KAAAuH,GACAtH,aAAA2H,IAGA,IAAAE,GAAA,SAAAA,EAAAC,EAAAC,GACA,OAAAD,IAAAC,UAAAD,IAAA,iBAAAC,IAAA,UAAAC,MAAAF,IAAAE,MAAAD,IAGA,IAAAE,GAAA,SAAAA,EAAAC,EAAAC,GACA,IAAAvW,EAAAsW,EAAAxY,OACA,IAAAL,EAAA,EACA,MAAAA,EAAAuC,EAAA,CACA,GAAAiW,GAAAK,EAAA7Y,GAAA8Y,GAAA,CACA,YAEA9Y,IAGA,cAGA,IAAA+Y,GAAAhT,MAAAgT,SAAA,SAAAC,GACA,OAAApT,OAAAlC,UAAAmC,SAAAzF,KAAA4Y,KAAA,kBAKA,IAAAC,GAAA,GAEA,IAAAC,GAAA,GAEA,IAAAC,GAAA,CACAC,UAAA7H,EACA8H,OAAAnC,GACAoC,aAAAf,IAGA,IAAAgB,GAAA,CAAAJ,GAAAC,UAAA5H,QAAA2H,GAAAE,OAAA7H,QAAA2H,GAAAG,aAAA9H,SAEA,IAAAgI,GAAA,iBAEA,IAAAC,GAAA,2EAAAC,OAAAF,IAEA,IAAAG,GAAA,CACAvE,YAAA,GACAwE,OAAAL,GAAA/J,QACApH,KAAA,cAGAiN,KAAA,QACA5I,UAAA,gBACAL,QAAA,GAGA,SAAAyN,GAAAC,EAAAC,GACAD,EAAAC,GAAA,WACA,IAAAC,EAAA5U,UACA,OAAA0U,EAAA1K,QAAAxL,KAAA,WACA,OAAAkW,EAAAC,GAAA5U,MAAA2U,EAAAE,MAKA,SAAAC,KACA,QAAAja,EAAA,EAAmBA,EAAAoF,UAAA/E,OAAsBL,IAAA,CACzC,IAAAgZ,EAAA5T,UAAApF,GAEA,GAAAgZ,EAAA,CACA,QAAAkB,KAAAlB,EAAA,CACA,GAAAA,EAAAmB,eAAAD,GAAA,CACA,GAAAnB,GAAAC,EAAAkB,IAAA,CACA9U,UAAA,GAAA8U,GAAAlB,EAAAkB,GAAA1K,YACqB,CACrBpK,UAAA,GAAA8U,GAAAlB,EAAAkB,OAOA,OAAA9U,UAAA,GAGA,IAAAgV,GAAA,WACA,SAAAA,EAAAjL,GACAxI,EAAAhE,KAAAyX,GAEA,QAAAC,KAAAlB,GAAA,CACA,GAAAA,GAAAgB,eAAAE,GAAA,CACA,IAAAT,EAAAT,GAAAkB,GACA,IAAAC,EAAAV,EAAApI,QACA7O,KAAA0X,GAAAC,EAEA,IAAArB,GAAAqB,GAAA,CAIA3X,KAAA4X,aAAAX,KAKAjX,KAAA8M,eAAAwK,GAAA,GAAuCN,IACvChX,KAAA6X,QAAAP,GAAA,GAAgCtX,KAAA8M,eAAAN,GAChCxM,KAAA8X,WAAA,KACA9X,KAAA+X,YAAA,KACA/X,KAAAgY,OAAA,MACAhY,KAAA8L,QAAA,KAEA9L,KAAAiY,+BACAjY,KAAAkY,UAAAlY,KAAA6X,QAAAZ,QAAA,uBASAQ,EAAA1W,UAAAmN,OAAA,SAAAA,EAAA1B,GAIA,WAAAA,IAAA,wBAAA3I,EAAA2I,MAAA,UAGA,GAAAxM,KAAAgY,OAAA,CACA,WAAA1a,MAAA,2DAGA,QAAAD,KAAAmP,EAAA,CACA,GAAAnP,IAAA,aACAmP,EAAAnP,GAAAmP,EAAAnP,GAAA8a,QAAA,WAGA,GAAA9a,IAAA,kBAAAmP,EAAAnP,KAAA,UACA,WAAAC,MAAA,sCAGA0C,KAAA6X,QAAAxa,GAAAmP,EAAAnP,GAKA,cAAAmP,KAAAyK,OAAA,CACA,OAAAjX,KAAAkY,UAAAlY,KAAA6X,QAAAZ,QAGA,iBACS,UAAAzK,IAAA,UACT,OAAAxM,KAAA6X,QAAArL,OACS,CACT,OAAAxM,KAAA6X,UAQAJ,EAAA1W,UAAA6W,aAAA,SAAAA,EAAAQ,EAAA/R,EAAAE,GACA,IAAApF,EAAA,IAAAgF,EAAA,SAAAvE,EAAAC,GACA,IACA,IAAA8V,EAAAS,EAAAvJ,QACA,IAAAwJ,EAAA,IAAA/a,MAAA,oCAA6E,uDAI7E,IAAA8a,EAAAvJ,QAAA,CACAhN,EAAAwW,GACA,OAGA,IAAAC,EAAAxB,GAAAC,OAAA,gBACA,QAAA1Z,EAAA,EAAAuC,EAAA0Y,EAAA5a,OAA2DL,EAAAuC,EAASvC,IAAA,CACpE,IAAAkb,EAAAD,EAAAjb,GAIA,IAAAmb,GAAAvC,GAAAY,GAAA0B,GACA,IAAAC,GAAAJ,EAAAG,YAAAH,EAAAG,KAAA,YACA1W,EAAAwW,GACA,QAIA,IAAAI,EAAA,SAAAA,IACA,IAAAC,EAAA,SAAAA,EAAAC,GACA,kBACA,IAAAtW,EAAA,IAAA/E,MAAA,UAAAqb,EAAA,6CACA,IAAAxX,EAAAgF,EAAAtE,OAAAQ,GACA+D,EAAAjF,EAAAsB,oBAAA/E,OAAA,IACA,OAAAyD,IAIA,QAAAyX,EAAA,EAAAC,EAAAhC,GAAAnZ,OAAyEkb,EAAAC,EAAWD,IAAA,CACpF,IAAAE,EAAAjC,GAAA+B,GACA,IAAAR,EAAAU,GAAA,CACAV,EAAAU,GAAAJ,EAAAI,MAKAL,IAEA,IAAAM,EAAA,SAAAA,EAAAC,GACA,GAAA1C,GAAAqB,GAAA,CACAjR,QAAAuS,KAAA,kCAAAtB,GAEArB,GAAAqB,GAAAS,EACA7B,GAAAoB,GAAAqB,EAIApX,KAGA,gBAAAwW,EAAA,CACA,GAAAA,EAAAtJ,iBAAAsJ,EAAAtJ,WAAA,YACAsJ,EAAAtJ,WAAA7N,KAAA8X,EAAAlX,OACqB,CACrBkX,IAAAX,EAAAtJ,eAEiB,CACjBiK,EAAA,OAEa,MAAAlc,GACbgF,EAAAhF,MAIAyJ,EAAAnF,EAAAkF,EAAAE,GACA,OAAApF,GAGAsW,EAAA1W,UAAAkW,OAAA,SAAAA,IACA,OAAAjX,KAAA6O,SAAA,MAGA4I,EAAA1W,UAAAmY,UAAA,SAAAA,EAAAvB,EAAAtR,EAAAE,GACA,IAAA4S,EAAA7C,GAAAqB,GAAAxR,EAAAvE,QAAA0U,GAAAqB,IAAAxR,EAAAtE,OAAA,IAAAvE,MAAA,sBAEAgJ,EAAA6S,EAAA9S,EAAAE,GACA,OAAA4S,GAGA1B,EAAA1W,UAAAqY,cAAA,SAAAA,EAAA/S,GACA,IAAAgT,EAAAlT,EAAAvE,QAAAwQ,IACA9L,EAAA+S,EAAAhT,GACA,OAAAgT,GAGA5B,EAAA1W,UAAA0L,MAAA,SAAAA,EAAApG,GACA,IAAApG,EAAAD,KAEA,IAAAmB,EAAAlB,EAAA6X,WAAA7W,KAAA,WACA,GAAAhB,EAAA+X,SAAA,MACA/X,EAAA+X,OAAA/X,EAAA8X,cAGA,OAAA9X,EAAA+X,SAGA1R,EAAAnF,EAAAkF,KACA,OAAAlF,GAGAsW,EAAA1W,UAAAmX,UAAA,SAAAA,EAAAoB,EAAAjT,EAAAE,GACA,IAAAtG,EAAAD,KAEA,IAAAoW,GAAAkD,GAAA,CACAA,EAAA,CAAAA,GAGA,IAAAC,EAAAvZ,KAAAwZ,qBAAAF,GAEA,SAAAG,IACAxZ,EAAA4X,QAAAZ,OAAAhX,EAAAgX,SAGA,SAAAyC,EAAAzC,GACAhX,EAAA0Z,QAAA1C,GACAwC,IAEAxZ,EAAA+X,OAAA/X,EAAAsM,aAAAtM,EAAA4X,SACA,OAAA5X,EAAA+X,OAGA,SAAA4B,EAAAL,GACA,kBACA,IAAAM,EAAA,EAEA,SAAAC,IACA,MAAAD,EAAAN,EAAA7b,OAAA,CACA,IAAAia,EAAA4B,EAAAM,GACAA,IAEA5Z,EAAA6L,QAAA,KACA7L,EAAA+X,OAAA,KAEA,OAAA/X,EAAAiZ,UAAAvB,GAAA1W,KAAAyY,GAAA,SAAAI,GAGAL,IACA,IAAApX,EAAA,IAAA/E,MAAA,sCACA2C,EAAA6X,WAAA3R,EAAAtE,OAAAQ,GACA,OAAApC,EAAA6X,WAGA,OAAAgC,KAOA,IAAAC,EAAA/Z,KAAA8X,aAAA,KAAA9X,KAAA8X,WAAA,oBACA,OAAA3R,EAAAvE,YACSuE,EAAAvE,UAET5B,KAAA8X,WAAAiC,EAAA9Y,KAAA,WACA,IAAA0W,EAAA4B,EAAA,GACAtZ,EAAA6L,QAAA,KACA7L,EAAA+X,OAAA,KAEA,OAAA/X,EAAAiZ,UAAAvB,GAAA1W,KAAA,SAAAgW,GACAhX,EAAA4O,QAAAoI,EAAApI,QACA4K,IACAxZ,EAAAgY,+BACAhY,EAAA8X,YAAA6B,EAAAL,OAES,oBACTE,IACA,IAAApX,EAAA,IAAA/E,MAAA,sCACA2C,EAAA6X,WAAA3R,EAAAtE,OAAAQ,GACA,OAAApC,EAAA6X,aAGAxR,EAAAtG,KAAA8X,WAAAzR,EAAAE,GACA,OAAAvG,KAAA8X,YAGAL,EAAA1W,UAAAiZ,SAAA,SAAAA,EAAArC,GACA,QAAApB,GAAAoB,IAGAF,EAAA1W,UAAA4Y,QAAA,SAAAA,EAAAM,GACA3C,GAAAtX,KAAAia,IAGAxC,EAAA1W,UAAAyY,qBAAA,SAAAA,EAAAF,GACA,IAAAC,EAAA,GACA,QAAAlc,EAAA,EAAAuC,EAAA0Z,EAAA5b,OAA6CL,EAAAuC,EAASvC,IAAA,CACtD,IAAAsa,EAAA2B,EAAAjc,GACA,GAAA2C,KAAAga,SAAArC,GAAA,CACA4B,EAAAxZ,KAAA4X,IAGA,OAAA4B,GAGA9B,EAAA1W,UAAAkX,6BAAA,SAAAA,IAKA,QAAA5a,EAAA,EAAAuC,EAAAkX,GAAApZ,OAAoDL,EAAAuC,EAASvC,IAAA,CAC7D6Z,GAAAlX,KAAA8W,GAAAzZ,MAIAoa,EAAA1W,UAAAmZ,eAAA,SAAAA,EAAA1N,GACA,WAAAiL,EAAAjL,IAGA,OAAAiL,EArSA,GA4SA,IAAA0C,GAAA,IAAA1C,GAEAhb,EAAAC,QAAAyd,IAEC,CAAExW,EAAA,KAAQ,GAAG,IAruF8V,CAquF9V,wFC3uFd,IAAAyW,EAAAC,EAAA,YAAAC,EAAAD,EAAAtd,EAAAqd,GAA4lB,IAAAG,EAAAD,EAAG,8CCA/lB,IAAAE,EAAA,WAA0B,IAAAC,EAAAza,KAAa,IAAA0a,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,YAAA,WAAsB,CAAAF,EAAA,YAAiBG,MAAA,CAAOC,IAAA,IAAAC,IAAA,KAAsBC,GAAA,CAAKC,MAAAV,EAAAW,aAAwBC,MAAA,CAAQ1Z,MAAA8Y,EAAA,MAAApU,SAAA,SAAAiV,GAA2Cb,EAAAc,MAAAD,GAAcE,WAAA,WAAqBf,EAAAgB,GAAAhB,EAAA,gBAAAiB,GAAqC,OAAAd,EAAA,OAAiBE,YAAA,UAAqB,CAAAF,EAAA,mBAAwBG,MAAA,CAAOY,UAAAD,EAAAE,GAAAC,aAAApB,EAAAqB,mBAAmD,MAAM,IACxc,IAAAC,EAAA,0JCDA,IAAIC,EAAM,WAAgB,IAAAvB,EAAAza,KAAa,IAAA0a,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,eAA0B,CAAAF,EAAA,cAAAA,EAAA,UAAgCqB,IAAA,YAAW,IAC/K,IAAIC,EAAe,gECanB,IAAAC,EAAA,CACAC,MAAA,CACAC,OAAA,CAAAC,QACAC,UAAA,CAAAD,SAGAE,QANA,SAAAA,IAOAxc,KAAAyc,cAAAC,EAAA,KAAAC,YAAA,CACAC,UAAA5c,KAAA6c,MAAAC,KACAvB,MAAA,IACAwB,OAAA,MAGA/c,KAAAgd,sBAGAC,MAAA,CACAV,UADA,SAAAA,IAEAvc,KAAAkd,SAIAC,QAAA,CACAH,mBADA,eAAAI,EAAAC,IAAAC,EAAAlgB,EAAAmgB,KAAA,SAAAC,IAAA,IAAAC,EAAA,OAAAH,EAAAlgB,EAAAsgB,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAAAF,EAAAE,KAAA,SAEAC,EAAA3gB,EAAA4P,QAAA,OAAA+J,OAAA/W,KAAAqc,SAFA,OAEAoB,EAFAG,EAAAI,KAGA,GAAAP,EAAA,CACAzd,KAAAie,gBAAAR,EAAA,UACA,CACAS,EAAA,KAAAC,IAAAC,IACAC,aAAA,CACAnY,KAAA,OACA0V,GAAA5b,KAAAqc,SAEAiC,WAAA,MACAC,KACAve,KAAAie,gBACA,WAdA,wBAAAL,EAAAY,UAAAhB,EAAAxd,SAAA,SAAAgd,IAAA,OAAAI,EAAA5a,MAAAxC,KAAAyC,WAAA,OAAAua,EAAA,GAmBAiB,gBAnBA,SAAAA,EAmBAQ,EAAAhB,GACAzd,KAAA0e,cAAAD,EACAze,KAAAkd,OAEA,IAAAO,EAAA,CACAM,EAAA3gB,EAAAoQ,QAAA,OAAAuJ,OAAA/W,KAAAqc,QAAAoC,KAIAvB,KA5BA,eAAAyB,EAAAtB,IAAAC,EAAAlgB,EAAAmgB,KAAA,SAAAqB,IAAA,IAAAC,EAAAC,EAAA,OAAAxB,EAAAlgB,EAAAsgB,KAAA,SAAAqB,EAAAC,GAAA,gBAAAA,EAAAnB,KAAAmB,EAAAlB,MAAA,OA6BAe,EAAA,CACAtD,MAAAvb,KAAAuc,UACAQ,OAAA,IACAkC,MAAA,IACAC,WAAA,KACAC,QAAAnf,KAAAqc,OACA+C,UAAA,OACAC,WAAA,KACAC,QAAA,KACAC,2BAAA,MAtCAP,EAAAlB,KAAA,SAyCAI,EAAA,KAAAsB,QAAArB,IAAAsB,YAAA,CACAf,cAAA1e,KAAA0e,cACA9d,MAAAie,EACAa,OAAA,cA5CA,OAyCAZ,EAzCAE,EAAAhB,KAgDAhe,KAAAyc,cAAAkD,eAAA3f,KAAAqc,OAAAyC,GAhDA,wBAAAE,EAAAR,UAAAI,EAAA5e,SAAA,SAAAkd,IAAA,OAAAyB,EAAAnc,MAAAxC,KAAAyC,WAAA,OAAAya,EAAA,IAqDAze,KA3EA,SAAAA,IA4EA,WC1FuP,IAAAmhB,EAAA,kCCQvP,IAAAC,EAAgB5c,OAAA6c,EAAA,KAAA7c,CACd2c,EACA5D,EACAE,EACF,MACA,KACA,KACA,MAIe,IAAA6D,EAAAF,UCJf,IAAAG,EAAA,CACAC,WAAA,CACAC,kBAAAH,GAGAvD,QALA,SAAAA,IAKA,IAAA2D,EAAAngB,KACAA,KAAAogB,yBAAAnf,KAAA,SAAAxC,GACA0hB,EAAAE,OAAA5hB,EAAAid,KAAA7O,MAAA,OAEA7M,KAAAob,YAAAkF,EAAAljB,EAAAmjB,SAAAvgB,KAAAwgB,aAAA,KAGArD,QAAA,CACAqD,aADA,SAAAA,EACAC,GACA/Z,QAAAga,IAAAD,GACAzgB,KAAA8b,eAAA2E,GAEAL,uBALA,eAAAO,EAAAtD,IAAAC,EAAAlgB,EAAAmgB,KAAA,SAAAC,IAAA,OAAAF,EAAAlgB,EAAAsgB,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAAAF,EAAAE,KAAA,SAMAI,EAAA,KAAAC,IAAAyC,QAAAC,WAAA,IAAAR,OAAAnb,QANA,cAAA0Y,EAAAkD,OAAA,SAAAlD,EAAAI,MAAA,wBAAAJ,EAAAY,UAAAhB,MAAA,SAAA4C,IAAA,OAAAO,EAAAne,MAAAxC,KAAAyC,WAAA,OAAA2d,EAAA,IAUA3hB,KAtBA,SAAAA,IAuBA,OACAqd,eAAA,KACAuE,OAAA,GACA9E,MAAA,QCzCyP,IAAAwF,EAAA,kBCQzP,IAAIC,EAAY/d,OAAA6c,EAAA,KAAA7c,CACd8d,EACAvG,EACAuB,EACF,MACA,KACA,KACA,MAIe,IAAAkF,EAAAC,EAAA,WAAAF,6CCnBf,IAAAG,EAAA9G,EAAA,YAAA+G,EAAA/G,EAAAtd,EAAAokB,GAA0lB,IAAA5G,EAAA6G,EAAG,4PCU7lB,IAAIC,EAAQnhB,OAAOmhB,OAAS9kB,EAAQ,QAEpC,IAAI+kB,EAAoB,SAApBA,EAA8BC,EAAQC,GAA0B,IAAdC,EAAchf,UAAA/E,OAAA,GAAA+E,UAAA,KAAA4J,UAAA5J,UAAA,GAAP,MAE5D,IAAI0d,EAAQngB,KACZ,IAAI0hB,EAAQ,CAAEC,MAAQ,EAAGC,OAAQ,EAAGC,KAAM,EAAGC,IAAK,EAAGC,aAAc,EAAGC,eAAgB,GAEtFhiB,KAAKuhB,OAASA,EACdvhB,KAAKwhB,WAAcA,IAAenV,UAAamV,EAAanjB,SAG5D2B,KAAKiiB,OAAS,MACdjiB,KAAKkiB,QAAU,KAEfliB,KAAKmiB,OAAS,CAAEC,KAAM,EAAGC,IAAK,EAAG9G,MAAO,EAAGwB,OAAQ,GAEnD/c,KAAKsiB,YAAc,EACnBtiB,KAAKuiB,UAAY,IACjBviB,KAAKwiB,SAAW,GAEhBxiB,KAAKyiB,SAAW,MAChBziB,KAAK0iB,OAAS,MACd1iB,KAAK2iB,MAAQ,MAEb3iB,KAAK4iB,aAAe,MACpB5iB,KAAK6iB,qBAAuB,GAE5B7iB,KAAK8iB,YAAc,EACnB9iB,KAAK+iB,YAAcC,SAOnBhjB,KAAK+N,KAAO,CAAC,GAAU,GAAU,IAIjC/N,KAAKoL,OAAS,IAAIiW,EAAM4B,QAExB,IAAIC,EAAM,KAEV,IAAIC,EAAe,IAAI9B,EAAM4B,QAE7B,IAAIG,EAAS1B,EAAMC,KAClB0B,EAAa3B,EAAMC,KAEnB2B,EAAO,IAAIjC,EAAM4B,QAEjBM,EAAY,IAAIlC,EAAMmC,QACtBC,EAAY,IAAIpC,EAAMmC,QAEtBE,EAAY,IAAIrC,EAAM4B,QACtBU,EAAa,EAEbC,EAAa,IAAIvC,EAAMmC,QACvBK,EAAW,IAAIxC,EAAMmC,QAErBM,EAA0B,EAC1BC,EAAwB,EAExBC,EAAY,IAAI3C,EAAMmC,QACtBS,EAAU,IAAI5C,EAAMmC,QAIrBxjB,KAAKkkB,QAAUlkB,KAAKoL,OAAO+Y,QAC3BnkB,KAAKokB,UAAYpkB,KAAKuhB,OAAO8C,SAASF,QACtCnkB,KAAKskB,IAAMtkB,KAAKuhB,OAAOgD,GAAGJ,QAI1B,IAAIK,EAAc,CAAEte,KAAM,UAC1B,IAAIue,EAAa,CAAEve,KAAM,SACzB,IAAIwe,EAAW,CAAExe,KAAM,OAGvBQ,QAAQga,IAAI,UAIZ1gB,KAAK2kB,aAAe,WAEnB,GAAI3kB,KAAKwhB,aAAenjB,SAAU,CAEjC2B,KAAKmiB,OAAOC,KAAO,EACnBpiB,KAAKmiB,OAAOE,IAAM,EAClBriB,KAAKmiB,OAAO5G,MAAQrb,OAAO0kB,WAC3B5kB,KAAKmiB,OAAOpF,OAAS7c,OAAO2kB,gBAEtB,CAEN,IAAIC,EAAM9kB,KAAKwhB,WAAWuD,wBAE1B,IAAIC,EAAIhlB,KAAKwhB,WAAWyD,cAAc3lB,gBACtCU,KAAKmiB,OAAOC,KAAO0C,EAAI1C,KAAOliB,OAAOglB,YAAcF,EAAEG,WACrDnlB,KAAKmiB,OAAOE,IAAMyC,EAAIzC,IAAMniB,OAAOklB,YAAcJ,EAAEK,UACnDrlB,KAAKmiB,OAAO5G,MAAQuJ,EAAIvJ,MACxBvb,KAAKmiB,OAAOpF,OAAS+H,EAAI/H,SAM3B/c,KAAKslB,YAAc,SAAUC,GAE5B,UAAWvlB,KAAKulB,EAAMrf,OAAS,WAAY,CAE1ClG,KAAKulB,EAAMrf,MAAMqf,KAMnB,IAAIC,EAAoB,WAEvB,IAAIC,EAAS,IAAIpE,EAAMmC,QAEvB,OAAO,SAASgC,EAAiBE,EAAOC,GAEvCF,EAAOG,KACLF,EAAQvF,EAAMgC,OAAOC,MAAQjC,EAAMgC,OAAO5G,OAC1CoK,EAAQxF,EAAMgC,OAAOE,KAAOlC,EAAMgC,OAAOpF,QAG3C,OAAO0I,GAXe,GAiBxB,IAAII,EAAoB,WAEvB,IAAIJ,EAAS,IAAIpE,EAAMmC,QAEvB,OAAO,SAASqC,EAAiBH,EAAOC,GAEvCF,EAAOG,KACJF,EAAQvF,EAAMgC,OAAO5G,MAAQ,GAAM4E,EAAMgC,OAAOC,OAASjC,EAAMgC,OAAO5G,MAAQ,KAC9E4E,EAAMgC,OAAOpF,OAAS,GAAKoD,EAAMgC,OAAOE,IAAMsD,IAAUxF,EAAMgC,OAAO5G,OAGxE,OAAOkK,GAXe,GAiBxBzlB,KAAK8lB,aAAgB,WAEpB,IAAIC,EAAO,IAAI1E,EAAM4B,QACpB+C,EAAa,IAAI3E,EAAM4E,WACvBC,EAAe,IAAI7E,EAAM4B,QACzBkD,EAAoB,IAAI9E,EAAM4B,QAC9BmD,EAA0B,IAAI/E,EAAM4B,QACpCoD,EAAgB,IAAIhF,EAAM4B,QAC1BqD,EAED,IAAIC,EAAY,EAEhB,SAAST,IAER,IAAIU,EAAoBH,EAAclC,QACtCkC,EAAcT,IAAInC,EAAU3N,EAAIyN,EAAUzN,EAAG2N,EAAU1N,EAAIwN,EAAUxN,EAAG,GACxEuQ,EAAQD,EAAc3oB,SAEtB,IAAI+oB,EAAO,GACX,IAAIC,EAAU,MACdH,GAAaD,GAASP,EAAKhQ,EAAI,EAAI,GAAK,GACxC,IAAI4Q,EAAaJ,GAAa,IAAMK,KAAKC,IACzCF,EAAaC,KAAK3L,KAAKwL,EAAMG,KAAK5L,IAAI2L,EAAYF,IAYlD,GAAIH,EAAO,CAEVhD,EAAKwD,KAAK3G,EAAMoB,OAAO8C,UAAU0C,IAAI5G,EAAM/U,QAE3C8a,EAAaY,KAAKxD,GAAM0D,YACxBb,EAAkBW,KAAK3G,EAAMoB,OAAOgD,IAAIyC,YACxCZ,EAAwBa,aAAad,EAAmBD,GAAcc,YAEtEb,EAAkBe,UAAUzD,EAAU1N,EAAIwN,EAAUxN,GACpDqQ,EAAwBc,UAAUzD,EAAU3N,EAAIyN,EAAUzN,GAE1D,GAAI9V,KAAKiiB,OAAQ,CAChBoE,EAAcS,KAAKV,OACb,CACNC,EAAcS,KAAKX,EAAkBgB,IAAIf,IAG1CL,EAAKkB,aAAaZ,EAAe/C,GAAM0D,YAEvChB,EAAWoB,iBAAiBrB,EAAMO,GAElChD,EAAK+D,gBAAgBrB,GACrB,IAAKhmB,KAAKiiB,OAAQ9B,EAAMoB,OAAOgD,GAAG8C,gBAAgBrB,GAElDtC,EAAUoD,KAAKf,GACfpC,EAAa2C,OAEP,IAAKnG,EAAMyC,cAAgBe,EAAY,CAE7CA,GAAciD,KAAKU,KAAK,EAAMnH,EAAM0C,sBACpCS,EAAKwD,KAAK3G,EAAMoB,OAAO8C,UAAU0C,IAAI5G,EAAM/U,QAC3C4a,EAAWoB,iBAAiB1D,EAAWC,GACvCL,EAAK+D,gBAAgBrB,GACrB7F,EAAMoB,OAAOgD,GAAG8C,gBAAgBrB,GAIjCzC,EAAUuD,KAAKrD,GAGhB,OAAOqC,EA1Ea,GAiFrB9lB,KAAKunB,WAAa,WAEjB,IAAIC,EAEJ,GAAIpE,IAAW1B,EAAMM,eAAgB,CAEpCwF,EAAS1D,EAA0BC,EACnCD,EAA0BC,EAC1BT,EAAKmE,eAAeD,OAEd,CAENA,EAAS,GAAO3D,EAAS9N,EAAI6N,EAAW7N,GAAKoK,EAAMoC,UAEnD,GAAIiF,IAAW,GAAOA,EAAS,EAAK,CAEnClE,EAAKmE,eAAeD,GAIrB,GAAIrH,EAAMyC,aAAc,CAEvBgB,EAAWkD,KAAKjD,OAEV,CAEND,EAAW7N,IAAM8N,EAAS9N,EAAI6N,EAAW7N,GAAK/V,KAAK6iB,wBAQtD7iB,KAAK0nB,UAAa,WAEjB,IAAIC,EAAc,IAAItG,EAAMmC,QAC3BoE,EAAW,IAAIvG,EAAM4B,QACrB4E,EAAM,IAAIxG,EAAM4B,QAEjB,OAAO,SAASyE,IAEfC,EAAYb,KAAK7C,GAAS8C,IAAI/C,GAE9B,GAAI2D,EAAYG,WAAY,CAE3BH,EAAYF,eAAenE,EAAK5lB,SAAWyiB,EAAMqC,UAEjDqF,EAAIf,KAAKxD,GAAMyE,MAAM5H,EAAMoB,OAAOgD,IAAI2C,UAAUS,EAAY7R,GAC5D+R,EAAIV,IAAIS,EAASd,KAAK3G,EAAMoB,OAAOgD,IAAI2C,UAAUS,EAAY5R,IAE7DoK,EAAMoB,OAAO8C,SAAS8C,IAAIU,GAC1B1H,EAAM/U,OAAO+b,IAAIU,GAEjB,GAAI1H,EAAMyC,aAAc,CAEvBoB,EAAU8C,KAAK7C,OAET,CAEND,EAAUmD,IAAIQ,EAAYK,WAAW/D,EAASD,GAAWyD,eAAetH,EAAM0C,0BA1BhE,GAoClB7iB,KAAKioB,eAAiB,WAErB,IAAK9H,EAAMuC,SAAWvC,EAAMwC,MAAO,CAElC,GAAIW,EAAKwE,WAAa3H,EAAM4C,YAAc5C,EAAM4C,YAAa,CAE5D5C,EAAMoB,OAAO8C,SAAS6D,WAAW/H,EAAM/U,OAAQkY,EAAK4D,UAAU/G,EAAM4C,cACpEa,EAAWkD,KAAKjD,GAIjB,GAAIP,EAAKwE,WAAa3H,EAAM2C,YAAc3C,EAAM2C,YAAa,CAE5D3C,EAAMoB,OAAO8C,SAAS6D,WAAW/H,EAAM/U,OAAQkY,EAAK4D,UAAU/G,EAAM2C,cACpEc,EAAWkD,KAAKjD,MAQnB7jB,KAAKmoB,OAAS,WAIb7E,EAAK0E,WAAW7H,EAAMoB,OAAO8C,SAAUlE,EAAM/U,QAE7C,IAAK+U,EAAMsC,SAAU,CAEpBtC,EAAM2F,eAIP,IAAK3F,EAAMuC,OAAQ,CAElBvC,EAAMoH,aAIP,IAAKpH,EAAMwC,MAAO,CAEjBxC,EAAMuH,YAIPvH,EAAMoB,OAAO8C,SAAS6D,WAAW/H,EAAM/U,OAAQkY,GAE/CnD,EAAM8H,iBAEN9H,EAAMoB,OAAO6G,OAAOjI,EAAM/U,QAE1B,GAAI+X,EAAakF,kBAAkBlI,EAAMoB,OAAO8C,UAAYnB,EAAK,CAEhE/C,EAAMmI,cAAc9D,GAEpBrB,EAAa2D,KAAK3G,EAAMoB,OAAO8C,YAMjCrkB,KAAKuoB,MAAQ,WAEZnF,EAAS1B,EAAMC,KACf0B,EAAa3B,EAAMC,KAEnBxB,EAAM/U,OAAO0b,KAAK3G,EAAM+D,SACxB/D,EAAMoB,OAAO8C,SAASyC,KAAK3G,EAAMiE,WACjCjE,EAAMoB,OAAOgD,GAAGuC,KAAK3G,EAAMmE,KAE3BhB,EAAK0E,WAAW7H,EAAMoB,OAAO8C,SAAUlE,EAAM/U,QAE7C+U,EAAMoB,OAAO6G,OAAOjI,EAAM/U,QAE1B+U,EAAMmI,cAAc9D,GAEpBrB,EAAa2D,KAAK3G,EAAMoB,OAAO8C,WAehC,SAASmE,EAAYza,EAAMtH,GAC1B,GAAIrD,MAAMgT,QAAQrI,GAAO,CACxB,OAAOA,EAAK5I,QAAQsB,MAAU,MACxB,CACN,OAAOsH,IAAStH,GAMlB,SAASgiB,EAAQlD,GAEhB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BhiB,OAAOwoB,oBAAoB,UAAWD,GAEtCpF,EAAaD,EAEb,GAAIA,IAAW1B,EAAMC,KAAM,OAIpB,GAAI6G,EAAYrI,EAAMpS,KAAK2T,EAAME,QAAS2D,EAAMoD,WAAaxI,EAAMsC,SAAU,CAEnFW,EAAS1B,EAAME,YAET,GAAI4G,EAAYrI,EAAMpS,KAAK2T,EAAMG,MAAO0D,EAAMoD,WAAaxI,EAAMuC,OAAQ,CAE/EU,EAAS1B,EAAMG,UAET,GAAI2G,EAAYrI,EAAMpS,KAAK2T,EAAMI,KAAMyD,EAAMoD,WAAaxI,EAAMwC,MAAO,CAE7ES,EAAS1B,EAAMI,KAMjB,SAAS8G,EAAMrD,GAEd,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BkB,EAASC,EAETnjB,OAAO2oB,iBAAiB,UAAWJ,EAAS,OAI7C,SAASK,EAAUvD,GAElB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BqD,EAAMtd,iBACNsd,EAAMrd,kBAEN,GAAIkb,IAAW1B,EAAMC,KAAM,CAE1ByB,EAASmC,EAAMwD,OAIhB,GAAI3F,IAAW1B,EAAME,SAAWzB,EAAMsC,SAAU,CAE/CgB,EAAUqD,KAAKjB,EAAiBN,EAAMG,MAAOH,EAAMI,QACnDpC,EAAUuD,KAAKrD,QAET,GAAIL,IAAW1B,EAAMG,OAAS1B,EAAMuC,OAAQ,CAElDkB,EAAWkD,KAAKtB,EAAiBD,EAAMG,MAAOH,EAAMI,QACpD9B,EAASiD,KAAKlD,QAER,GAAIR,IAAW1B,EAAMI,MAAQ3B,EAAMwC,MAAO,CAEhDqB,EAAU8C,KAAKtB,EAAiBD,EAAMG,MAAOH,EAAMI,QACnD1B,EAAQ6C,KAAK9C,GAId3lB,SAASwqB,iBAAiB,YAAaG,EAAW,OAClD3qB,SAASwqB,iBAAiB,UAAWI,EAAS,OAE9C9I,EAAMmI,cAAc7D,GAIrB,SAASuE,EAAUzD,GAElB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BqD,EAAMtd,iBACNsd,EAAMrd,kBAEN,GAAIkb,IAAW1B,EAAME,SAAWzB,EAAMsC,SAAU,CAE/Cc,EAAUuD,KAAKrD,GACfA,EAAUqD,KAAKjB,EAAiBN,EAAMG,MAAOH,EAAMI,aAE7C,GAAIvC,IAAW1B,EAAMG,OAAS1B,EAAMuC,OAAQ,CAElDmB,EAASiD,KAAKtB,EAAiBD,EAAMG,MAAOH,EAAMI,aAE5C,GAAIvC,IAAW1B,EAAMI,MAAQ3B,EAAMwC,MAAO,CAEhDsB,EAAQ6C,KAAKtB,EAAiBD,EAAMG,MAAOH,EAAMI,SAMnD,SAASsD,EAAQ1D,GAEhB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BqD,EAAMtd,iBACNsd,EAAMrd,kBAENkb,EAAS1B,EAAMC,KAEftjB,SAASqqB,oBAAoB,YAAaM,GAC1C3qB,SAASqqB,oBAAoB,UAAWO,GACxC9I,EAAMmI,cAAc5D,GAIrB,SAASwE,EAAW3D,GAEnB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BqD,EAAMtd,iBACNsd,EAAMrd,kBAEN,OAAQqd,EAAM4D,WAEb,KAAK,EAEJvF,EAAW7N,GAAKwP,EAAM6D,OAAS,KAC/B,MAED,KAAK,EAEJxF,EAAW7N,GAAKwP,EAAM6D,OAAS,IAC/B,MAED,QAECxF,EAAW7N,GAAKwP,EAAM6D,OAAS,MAC/B,MAIFjJ,EAAMmI,cAAc7D,GACpBtE,EAAMmI,cAAc5D,GAIrB,SAAS2E,EAAW9D,GAEnB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7B,OAAQqD,EAAM+D,QAAQ5rB,QAErB,KAAK,EACJ0lB,EAAS1B,EAAMK,aACf0B,EAAUqD,KAAKjB,EAAiBN,EAAM+D,QAAQ,GAAG5D,MAAOH,EAAM+D,QAAQ,GAAG3D,QACzEpC,EAAUuD,KAAKrD,GACf,MAED,QACCL,EAAS1B,EAAMM,eACf,IAAIuH,EAAKhE,EAAM+D,QAAQ,GAAG5D,MAAQH,EAAM+D,QAAQ,GAAG5D,MACnD,IAAI8D,EAAKjE,EAAM+D,QAAQ,GAAG3D,MAAQJ,EAAM+D,QAAQ,GAAG3D,MACnD5B,EAAwBD,EAA0B8C,KAAKU,KAAKiC,EAAKA,EAAKC,EAAKA,GAE3E,IAAI1T,GAAKyP,EAAM+D,QAAQ,GAAG5D,MAAQH,EAAM+D,QAAQ,GAAG5D,OAAS,EAC5D,IAAI3P,GAAKwP,EAAM+D,QAAQ,GAAG3D,MAAQJ,EAAM+D,QAAQ,GAAG3D,OAAS,EAC5D3B,EAAU8C,KAAKtB,EAAiB1P,EAAGC,IACnCkO,EAAQ6C,KAAK9C,GACb,MAIF7D,EAAMmI,cAAc7D,GAIrB,SAASgF,EAAUlE,GAElB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BqD,EAAMtd,iBACNsd,EAAMrd,kBAEN,OAAQqd,EAAM+D,QAAQ5rB,QAErB,KAAK,EACJ6lB,EAAUuD,KAAKrD,GACfA,EAAUqD,KAAKjB,EAAiBN,EAAM+D,QAAQ,GAAG5D,MAAOH,EAAM+D,QAAQ,GAAG3D,QACzE,MAED,QACC,IAAI4D,EAAKhE,EAAM+D,QAAQ,GAAG5D,MAAQH,EAAM+D,QAAQ,GAAG5D,MACnD,IAAI8D,EAAKjE,EAAM+D,QAAQ,GAAG3D,MAAQJ,EAAM+D,QAAQ,GAAG3D,MACnD5B,EAAwB6C,KAAKU,KAAKiC,EAAKA,EAAKC,EAAKA,GAEjD,IAAI1T,GAAKyP,EAAM+D,QAAQ,GAAG5D,MAAQH,EAAM+D,QAAQ,GAAG5D,OAAS,EAC5D,IAAI3P,GAAKwP,EAAM+D,QAAQ,GAAG3D,MAAQJ,EAAM+D,QAAQ,GAAG3D,OAAS,EAC5D1B,EAAQ6C,KAAKtB,EAAiB1P,EAAGC,IACjC,MAIFrP,QAAQga,IAAI,OAIb,SAASgJ,EAASnE,GAEjB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7B,OAAQqD,EAAM+D,QAAQ5rB,QAErB,KAAK,EACJ0lB,EAAS1B,EAAMC,KACf,MAED,KAAK,EACJyB,EAAS1B,EAAMK,aACf0B,EAAUqD,KAAKjB,EAAiBN,EAAM+D,QAAQ,GAAG5D,MAAOH,EAAM+D,QAAQ,GAAG3D,QACzEpC,EAAUuD,KAAKrD,GACf,MAIFtD,EAAMmI,cAAc5D,GAIrB,SAASiF,EAAYpE,GAEpB,GAAIpF,EAAM+B,UAAY,MAAO,OAE7BqD,EAAMtd,iBAIPjI,KAAK4pB,QAAU,aAkBf5pB,KAAKwhB,WAAWqH,iBAAiB,cAAec,EAAa,OAC7D3pB,KAAKwhB,WAAWqH,iBAAiB,YAAaC,EAAW,OACzD9oB,KAAKwhB,WAAWqH,iBAAiB,QAASK,EAAY,OAEtDlpB,KAAKwhB,WAAWqH,iBAAiB,aAAcQ,EAAY,OAC3DrpB,KAAKwhB,WAAWqH,iBAAiB,WAAYa,EAAU,OACvD1pB,KAAKwhB,WAAWqH,iBAAiB,YAAaY,EAAW,OAEzDvpB,OAAO2oB,iBAAiB,UAAWJ,EAAS,OAC5CvoB,OAAO2oB,iBAAiB,QAASD,EAAO,OAExC5oB,KAAK2kB,eAGL3kB,KAAKmoB,UAQN7G,EAAkBvgB,UAAYkC,OAAO4mB,OAAOxI,EAAMyI,gBAAgB/oB,2DCvqBlEb,OAAOmhB,MAAQA,EAEf,IAAI0I,EAAc,KAElB,IAAIC,EAAUC,iBACdD,EAAS/oB,KAAK,SAAA+oB,GACVD,EAAcC,IAIlB,IAAME,EAAO,CACTC,UAAW,KACXnQ,SAAU,KACVoQ,MAAO,KACPC,MAAO,KACPC,QAAS,KACTC,MAAO,KACPC,KAAM,KACNC,YAAa,KACbC,OAAQ,KACRC,UAAW,EACXC,kBAAmB,GACnBC,eAAgB,GAChBC,aAAc,MACdC,YAAa,MAGjB,IAAMC,EAAM,IAAI3J,uBAAwB,CACpC4J,MAAO,SACPC,UAAW,IAGf,IAAMC,EAAW,IAAI9J,uBAAwB,CACzC4J,MAAO,SAAUG,UAAW,MAAOC,YAAa,KAAMC,cAAe,KACrEC,oBAAqB,EAAGC,mBAAoB,EAAGC,QAAS,KAG5D,IAAIC,EAAW,MAETC,aACF,SAAAA,IAAcC,IAAA5rB,KAAA2rB,GACV3rB,KAAK6rB,OAAS,GACd7rB,KAAK8rB,QAAU,GACf9rB,KAAK+rB,KAAK,IAAK,8CAGO,IAAjBxQ,EAAiByQ,EAAjBzQ,MAAOwB,EAAUiP,EAAVjP,OACZ/c,KAAKgqB,SAASiC,QAAQ1Q,EAAOwB,GAC7B/c,KAAKgqB,SAASxI,WAAW0K,MAAM3Q,MAA/B,GAAAxE,OAA0CwE,EAA1C,MACAvb,KAAKgqB,SAASxI,WAAW0K,MAAMnP,OAA/B,GAAAhG,OAA2CgG,EAA3C,6CAGSoP,GACT,IAAIC,EAAS,IAAI/K,uBAAwB,GAAI,EAAG,EAAG,KAEnD+K,EAAO/H,SAASgI,EAAI,IACpBD,EAAO/H,SAASvO,EAAI,IACpBsW,EAAO/H,SAAStO,EAAI,IAEpB,IAAIuW,EAAW,IAAIhL,EAAkB8K,EAAQD,GAG7CG,EAAShK,YAAc,EACvBgK,EAAS/J,UAAY,IACrB+J,EAAS9J,SAAW,GACpB8J,EAAS5J,OAAS,MAClB4J,EAAS3J,MAAQ,MACjB2J,EAAS1J,aAAe,KACxB0J,EAASzJ,qBAAuB,GAChCyJ,EAASlhB,OAAS,IAAIiW,aAAc,IAAK,IAAK,GAG9C,MAAO,CAAE+K,SAAQE,qDAGqE,IAAAnM,EAAAngB,KAAA,IAA5E4c,EAA4E2P,EAA5E3P,UAAWrB,EAAiEgR,EAAjEhR,MAAOwB,EAA0DwP,EAA1DxP,OAAQyP,EAAkDD,EAAlDC,WAAkDC,EAAAF,EAAtCrmB,OAAsCumB,SAAA,EAA/B,YAA+BA,EAAlBC,EAAkBH,EAAlBG,eAEpE9P,EAAUrB,MAAQA,EAClBqB,EAAUG,OAASA,EAEnB,IAAI4P,EAAUjB,IAEd,IAAK1rB,KAAK6rB,OAAOc,GAAU3sB,KAAK6rB,OAAOc,GAAW,IAAItL,WACtD,IAAI+K,EAAS,KAEb,IAAIQ,EAAa,KAEjB,OAAO1mB,GACH,IAAK,UACD0mB,EAAa,IAAIC,OAAYjQ,EAAW5c,KAAK6rB,OAAOc,IACpDP,EAAS,CAAEA,OAAQQ,EAAWR,OAAQE,SAAUM,EAAWN,UAC3DM,EAAWE,aAAavR,EAAMwB,GAC9BqP,EAAOE,SAASS,sBAAwB,KAExC,IAAIC,EAAe,MACnB,IAAIC,EAAa,SAAbA,IACA,GAAGD,EAAc,CACbA,EAAe,MACf7M,EAAK+M,aAAaP,GAClBP,EAAOE,SAASnE,SAEpBjoB,OAAOitB,sBAAsBF,IAGjCA,IAEAb,EAAOE,SAASzD,iBAAiB,SAAU,SAACuE,GAExCJ,EAAe,OAGnBZ,EAAOE,SAASzD,iBAAiB,SAAU,WACvC1I,EAAK+M,aAAaP,KAG1B,MACA,IAAK,YACDP,EAASpsB,KAAKqtB,aAAazQ,GAC3BwP,EAAOA,OAAOkB,OAAS/R,EAAQwB,EAC/BqP,EAAOA,OAAOmB,yBAClB,MACA,QACInB,EAAS,CAAEA,OAAQ,KAAME,SAAU,MACvC,MAGJ,IAAIkB,EAAQxtB,KAAK8rB,QAAQ/rB,KAAK,CAC1BytB,MAAOb,EACPR,OAAQvP,EACRrB,QACAwB,SACA7W,OACAsmB,aACAK,YAAaD,EACbR,OAAQA,EAAOA,OACfE,SAAUF,EAAOE,SACjBmB,qBAAsB,SAAAA,EAACpnB,GACnB+lB,EAAOE,SAASzD,iBAAiB,SAAU,WACvCxiB,OAGRqnB,SAAU,SAAAA,EAACjP,GACP,OAAOsL,EAAY2D,SAASjP,IAEhCjE,OAAQ,SAAAA,EAACmT,GACLxN,EAAK+M,aAAaP,EAASgB,EAAO,OAEtCC,SAAU,SAAAA,EAACC,EAAYpP,EAAMwM,EAAO/kB,GAChC6jB,EAAY6D,SAAS3C,EAAO/kB,IAGhCyZ,eAAgB,SAAAA,EAACkO,EAAYpP,GAA6B,IAAvBwM,EAAuBxoB,UAAA/E,OAAA,GAAA+E,UAAA,KAAA4J,UAAA5J,UAAA,GAAjB,MAAiB,IAAVqrB,EAAUrrB,UAAA/E,OAAA,EAAA+E,UAAA,GAAA4J,UACtD8T,EAAK4N,YAAYpB,EAAjB,GAAA5V,OAA6B7Q,EAA7B,KAAA6Q,OAAqC8W,GAAcpP,EAAMwM,EAAO6C,IAGpEE,kBAAmB,SAAAA,EAACH,EAAYI,EAAMC,GAClC/N,EAAKgO,qBAAqBxB,EAA1B,GAAA5V,OAAsC7Q,EAAtC,KAAA6Q,OAA8C8W,GAAcI,EAAMC,IAGtEE,SAAU,SAAAA,IACN,OAAOjO,EAAK0L,OAAOc,IAEvB0B,MAAO,SAAAA,EAACR,GACJ1N,EAAKkO,MAAMR,IAEfS,OAAQ,SAAAA,EAAC5b,OAKb,IAAI6b,EAAgBvuB,KAAK8rB,QAAQ0B,EAAQ,GACzCe,EAAcC,QAAUhB,EAGxB,OAAOxtB,KAAK8rB,QAAQ0B,EAAQ,0CAGnBiB,GACT,OAAOC,IAAEC,KAAK3uB,KAAK8rB,QAAS,CAAE0B,MAAOiB,mDAGpB9B,EAAS/Q,EAAIqS,EAAMC,GACpC,IAAIV,EAAQxtB,KAAK4uB,aAAajC,GAC9Ba,EAAMX,YAAYgC,sBACdX,EAAO,GAAGY,YAAYC,KACtBb,EAAO,GAAGY,YAAYE,KACtBf,EAAKgB,IAAMhB,EAAKiB,GAAKjB,EAAKgB,IAAM,GACpCjvB,KAAKwa,OAAOmS,EAAS/Q,yCAGb+Q,EAAS/Q,EAAI6C,EAAMwM,EAAO6C,GAClC,GAAIrP,GAAQ,KAAM,OAAOze,KAAKquB,MAAMzS,GACpC,IAAI4R,EAAQxtB,KAAK4uB,aAAajC,GAG9B,OAAOa,EAAMtnB,MACT,IAAK,UACD,IAAKlG,KAAK6rB,OAAOjQ,GAAK5b,KAAK6rB,OAAOjQ,GAAM,IAAIyF,WAC5C0I,EAAYoF,aAAa1Q,EAAMwM,EAAOjrB,KAAK6rB,OAAOjQ,GAAK4R,EAAMpB,OAAQpsB,KAAKgqB,UAC1E,GAAI,CAAC,QAAS,OAAO7kB,QAAQqoB,EAAMhB,aAAe,EAAG,CACjD,IAAI4C,EAAQ3Q,EAAK4Q,qBADgC,IAE5CC,EACD,CAACxZ,EAAGsZ,EAAML,KAAK,GAAIhZ,EAAGqZ,EAAML,KAAK,GAAI1C,EAAG+C,EAAML,KAAK,IADrCQ,EAEd,CAACzZ,EAAGsZ,EAAMJ,KAAK,GAAIjZ,EAAGqZ,EAAMJ,KAAK,GAAI3C,EAAG+C,EAAMJ,KAAK,IAGvD,IAAKxB,EAAMgC,UAAW,CAClB,OAAQhC,EAAMhB,YACV,IAAK,QACDgB,EAAMX,YAAY4C,kBAAkBH,EAAaC,GACjD/B,EAAMgC,UAAY,KAClB,MACJ,IAAK,MACDhC,EAAMX,YAAY6C,gBAAgBJ,EAAaC,GAC/C/B,EAAMgC,UAAY,KAClB,WAGT,CACH1B,EAAQA,IAAUzhB,UAAY,KAAOyhB,EACrC,OAAQN,EAAMhB,YACV,IAAK,QACDgB,EAAMX,YAAYP,SAASqD,cAAgB7B,EAC3C,MACJ,IAAK,YACD,MACJ,IAAK,MACD,MAERN,EAAMX,YAAYlN,eAAgB2P,EAAaC,IAGvD,MAEA,IAAK,UACD,IAAKvvB,KAAK6rB,OAAOjQ,GAAK5b,KAAK6rB,OAAOjQ,GAAM,IAAIyF,WAChD,MACA,QACI,IAAIuO,EAAoB5vB,KAAK6vB,eAAepR,EAAKtL,KAAK2c,SAAU5F,GAChElqB,KAAK+vB,aAAanU,EAAIgU,EAAmBvL,UAC7C,MAGJrkB,KAAKwa,OAAOmS,EAAS/Q,mCAGnBA,GACF5b,KAAKgwB,WAAWpU,kCAGfL,EAAOwB,GACR,IAAIqP,EACJ,IAAI6D,EAAqBC,EAEzBlwB,KAAKkwB,qBAAuBA,EAAuBnT,EACnD/c,KAAKiwB,oBAAsBA,EAAsB1U,EAEjD,IAAIyO,EAAW,IAAI3I,mBAAoB,CAAE8O,UAAW,KAAMC,sBAAuB,MAAOC,MAAO,OAC/FrwB,KAAKgqB,SAAWA,EAGhBA,EAASiC,QAAQgE,EAAqBC,4CAY3BJ,EAAUQ,GACrB,OAAOR,EAASS,OAAO,SAAAza,GACnB,OACKA,EAAE,eAAiB,KAAOwa,EAAY,gBACtCxa,EAAE,eAAiB,KAAOwa,EAAY,cACtCxa,EAAE,eAAiB,KAAOwa,EAAY,aACtCxa,EAAE,eAAiB,KAAOwa,EAAY,UACtCxa,EAAE,eAAiB,KAAOwa,EAAY,UACtCxa,EAAE,eAAiB,KACnBA,EAAE,eAAiB,KACnBA,EAAE,eAAiB,KAAOwa,EAAY,SACtCxa,EAAE,eAAiB,KAAOwa,EAAY,YACtCxa,EAAE,eAAiB,QAAUwa,EAAY,UACzCxa,EAAE,eAAiB,OAASwa,EAAY,gBACxCxa,EAAE,eAAiB,UAAYwa,EAAY,6CAIjD9B,EAAS5S,GACZ,IAAI4R,EAAQkB,IAAEC,KAAK3uB,KAAK8rB,QAAS,CAAE0B,MAAOgB,IAC1CxuB,KAAKwwB,OAAO,CAAEjV,MAAOiS,EAAMjS,MAAOwB,OAAQyQ,EAAMzQ,SAChD/c,KAAKgqB,SAASxP,OAAOxa,KAAK6rB,OAAOjQ,GAAK4R,EAAMpB,QAC5CoB,EAAMiD,aAAe7U,EACrB4R,EAAMrB,OAAOuE,WAAW,MAAMC,UAAU,EAAG,EAAGnD,EAAMjS,MAAOiS,EAAMzQ,QACjEyQ,EAAMrB,OAAOuE,WAAW,MAAME,UAAU5wB,KAAKgqB,SAASxI,WAAY,EAAG,GACrEgM,EAAMlB,SAASnE,gDAGNqG,GAAoC,IAA3Bb,EAA2BlrB,UAAA/E,OAAA,GAAA+E,UAAA,KAAA4J,UAAA5J,UAAA,GAAnB,KAAmB,IAAbkL,EAAalL,UAAA/E,OAAA,GAAA+E,UAAA,KAAA4J,UAAA5J,UAAA,GAAP,MACtC,IAAI+qB,EAAQkB,IAAEC,KAAK3uB,KAAK8rB,QAAS,CAAE0B,MAAOgB,IAC1Cb,EAAQA,GAAS3tB,KAAK6rB,OAAO2B,EAAMiD,eAAiBzwB,KAAK6rB,OAAO2C,GAChExuB,KAAKgqB,SAASxP,OAAOmT,EAAOH,EAAMpB,QAElC,GAAGze,EAAO6f,EAAMrB,OAAOuE,WAAW,MAAMC,UAAU,EAAG,EAAGnD,EAAMjS,MAAOiS,EAAMzQ,QAC3EyQ,EAAMrB,OAAOuE,WAAW,MAAME,UAAU5wB,KAAKgqB,SAASxI,WAAY,EAAG,oDAGlDqP,GACnB,IAAI/L,EAAM,KACV+L,EAASC,SAAS,SAAUC,GACxB,IAAIC,EAAWD,EAAMC,SACrB,GAAIA,IAAa3kB,UAAW,OAC5B2kB,EAASC,qBACT,GAAInM,IAAQ,KAAM,CACdA,EAAMkM,EAASlC,gBACZ,CACHhK,EAAIoM,MAAMF,EAASlC,gBAG3B,OAAOhK,yCAGElJ,EAAIkU,EAAUzL,GACvB,IAAKrkB,KAAK6rB,OAAOjQ,GAAK5b,KAAK6rB,OAAOjQ,GAAM,IAAIyF,WAE5C,IAAIgD,EAAUrkB,KAAKgwB,WAAWpU,GAC9B,IAAI+R,EAAQ3tB,KAAK6rB,OAAOjQ,GAExB,IAAIgB,EAAY,IAAIyE,cAEpB,IAAK,IAAIhkB,EAAI,EAAGA,EAAIyyB,EAASpyB,OAAQL,IAAK,CACtC,IAAI8zB,EAAYrB,EAASzyB,GACzB,GAAI8zB,EAAUlR,YAAc,KAAM,SAElC,IAAK,IAAIrT,EAAI,EAAGA,EAAI3J,OAAOE,OAAOguB,EAAUlR,YAAYviB,OAAQkP,IAAK,CAEjE,IAAIuG,EAAOlQ,OAAOE,OAAOguB,EAAUlR,YAAYrT,GAC/C,GAAIsd,EAAKY,cAAgB,KAAM,CAC3B,MAAO3X,EAAK+W,EAAKU,oBAAsB,WAClC1nB,WACAiC,QAAQ+kB,EAAKW,iBAAmB,GACnC,CACE,UAIR,IAAIuG,EAAQ,EACPje,EAAKke,SAAS,GAAKle,EAAKke,SAAS,IAAM,KACvCle,EAAKme,SAAS,GAAKne,EAAKme,SAAS,IAAM,KACvCne,EAAKoe,SAAS,GAAKpe,EAAKoe,SAAS,IAAM,KAG5C,IAAIC,EAAU,EACTre,EAAKke,SAAS,GAAKle,EAAKke,SAAS,IAAM,KACvCle,EAAKme,SAAS,GAAKne,EAAKme,SAAS,IAAM,KACvCne,EAAKoe,SAAS,GAAKpe,EAAKoe,SAAS,IAAM,KAG5C,IAAIP,EAAYG,EAAUM,WAAa,IAAO,IAAIpQ,sBAAuB+P,EAAM,GAAK,EAAGA,EAAM,GAAK,EAAGA,EAAM,GAAI,GAAI,GAAK,IAAI/P,iBAAkB+P,EAAM,GAAIA,EAAM,GAAIA,EAAM,IAExK,IAAIM,EAAO,IAAIrQ,UAAW2P,EAAU7F,GACpCuG,EAAKrN,SAASvO,EAAI0b,EAAQ,GAC1BE,EAAKrN,SAAStO,EAAIyb,EAAQ,GAC1BE,EAAKrN,SAASgI,EAAImF,EAAQ,GAE1B,IAAIpT,EAAM,IAAIiD,mBAAoBqQ,EAAKV,UACvC,IAAI5F,EAAY,IAAI/J,kBAAmBjD,EAAK4M,GAE5C0G,EAAKvK,IAAIiE,GACTxO,EAAUuK,IAAIuK,IAGtB/D,EAAMxG,IAAIvK,GACV,GAAGyH,EAAU,CACTzH,EAAUyH,SAASvO,EAAIuO,EAASvO,EAChC8G,EAAUyH,SAAStO,EAAIsO,EAAStO,EAChC6G,EAAUyH,SAASgI,EAAIhI,EAASgI,uCAKpCtC,EAAY4H,gBAAgB,wCAI5B5H,EAAY4H,gBAAgB,wCAGrB/V,GACP,IAAI+R,EAAQ3tB,KAAK6rB,OAAOjQ,GACxB,GAAG+R,EAAO,MAAOA,EAAMiE,SAASl0B,OAAtB,CAA8BiwB,EAAMkE,OAAOlE,EAAMiE,SAAS,qBAI5E,IAAME,EAAqB,IAAInG", "file": "js/6b051dda.34fb8e96.js", "sourcesContent": ["/*!\n    localForage -- Offline Storage, Improved\n    Version 1.7.3\n    https://localforage.github.io/localForage\n    (c) 2013-2017 Mozilla, Apache License 2.0\n*/\n(function(f){if(typeof exports===\"object\"&&typeof module!==\"undefined\"){module.exports=f()}else if(typeof define===\"function\"&&define.amd){define([],f)}else{var g;if(typeof window!==\"undefined\"){g=window}else if(typeof global!==\"undefined\"){g=global}else if(typeof self!==\"undefined\"){g=self}else{g=this}g.localforage = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw (f.code=\"MODULE_NOT_FOUND\", f)}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(_dereq_,module,exports){\n(function (global){\n'use strict';\nvar Mutation = global.MutationObserver || global.WebKitMutationObserver;\n\nvar scheduleDrain;\n\n{\n  if (Mutation) {\n    var called = 0;\n    var observer = new Mutation(nextTick);\n    var element = global.document.createTextNode('');\n    observer.observe(element, {\n      characterData: true\n    });\n    scheduleDrain = function () {\n      element.data = (called = ++called % 2);\n    };\n  } else if (!global.setImmediate && typeof global.MessageChannel !== 'undefined') {\n    var channel = new global.MessageChannel();\n    channel.port1.onmessage = nextTick;\n    scheduleDrain = function () {\n      channel.port2.postMessage(0);\n    };\n  } else if ('document' in global && 'onreadystatechange' in global.document.createElement('script')) {\n    scheduleDrain = function () {\n\n      // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n      // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n      var scriptEl = global.document.createElement('script');\n      scriptEl.onreadystatechange = function () {\n        nextTick();\n\n        scriptEl.onreadystatechange = null;\n        scriptEl.parentNode.removeChild(scriptEl);\n        scriptEl = null;\n      };\n      global.document.documentElement.appendChild(scriptEl);\n    };\n  } else {\n    scheduleDrain = function () {\n      setTimeout(nextTick, 0);\n    };\n  }\n}\n\nvar draining;\nvar queue = [];\n//named nextTick for less confusing stack traces\nfunction nextTick() {\n  draining = true;\n  var i, oldQueue;\n  var len = queue.length;\n  while (len) {\n    oldQueue = queue;\n    queue = [];\n    i = -1;\n    while (++i < len) {\n      oldQueue[i]();\n    }\n    len = queue.length;\n  }\n  draining = false;\n}\n\nmodule.exports = immediate;\nfunction immediate(task) {\n  if (queue.push(task) === 1 && !draining) {\n    scheduleDrain();\n  }\n}\n\n}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{}],2:[function(_dereq_,module,exports){\n'use strict';\nvar immediate = _dereq_(1);\n\n/* istanbul ignore next */\nfunction INTERNAL() {}\n\nvar handlers = {};\n\nvar REJECTED = ['REJECTED'];\nvar FULFILLED = ['FULFILLED'];\nvar PENDING = ['PENDING'];\n\nmodule.exports = Promise;\n\nfunction Promise(resolver) {\n  if (typeof resolver !== 'function') {\n    throw new TypeError('resolver must be a function');\n  }\n  this.state = PENDING;\n  this.queue = [];\n  this.outcome = void 0;\n  if (resolver !== INTERNAL) {\n    safelyResolveThenable(this, resolver);\n  }\n}\n\nPromise.prototype[\"catch\"] = function (onRejected) {\n  return this.then(null, onRejected);\n};\nPromise.prototype.then = function (onFulfilled, onRejected) {\n  if (typeof onFulfilled !== 'function' && this.state === FULFILLED ||\n    typeof onRejected !== 'function' && this.state === REJECTED) {\n    return this;\n  }\n  var promise = new this.constructor(INTERNAL);\n  if (this.state !== PENDING) {\n    var resolver = this.state === FULFILLED ? onFulfilled : onRejected;\n    unwrap(promise, resolver, this.outcome);\n  } else {\n    this.queue.push(new QueueItem(promise, onFulfilled, onRejected));\n  }\n\n  return promise;\n};\nfunction QueueItem(promise, onFulfilled, onRejected) {\n  this.promise = promise;\n  if (typeof onFulfilled === 'function') {\n    this.onFulfilled = onFulfilled;\n    this.callFulfilled = this.otherCallFulfilled;\n  }\n  if (typeof onRejected === 'function') {\n    this.onRejected = onRejected;\n    this.callRejected = this.otherCallRejected;\n  }\n}\nQueueItem.prototype.callFulfilled = function (value) {\n  handlers.resolve(this.promise, value);\n};\nQueueItem.prototype.otherCallFulfilled = function (value) {\n  unwrap(this.promise, this.onFulfilled, value);\n};\nQueueItem.prototype.callRejected = function (value) {\n  handlers.reject(this.promise, value);\n};\nQueueItem.prototype.otherCallRejected = function (value) {\n  unwrap(this.promise, this.onRejected, value);\n};\n\nfunction unwrap(promise, func, value) {\n  immediate(function () {\n    var returnValue;\n    try {\n      returnValue = func(value);\n    } catch (e) {\n      return handlers.reject(promise, e);\n    }\n    if (returnValue === promise) {\n      handlers.reject(promise, new TypeError('Cannot resolve promise with itself'));\n    } else {\n      handlers.resolve(promise, returnValue);\n    }\n  });\n}\n\nhandlers.resolve = function (self, value) {\n  var result = tryCatch(getThen, value);\n  if (result.status === 'error') {\n    return handlers.reject(self, result.value);\n  }\n  var thenable = result.value;\n\n  if (thenable) {\n    safelyResolveThenable(self, thenable);\n  } else {\n    self.state = FULFILLED;\n    self.outcome = value;\n    var i = -1;\n    var len = self.queue.length;\n    while (++i < len) {\n      self.queue[i].callFulfilled(value);\n    }\n  }\n  return self;\n};\nhandlers.reject = function (self, error) {\n  self.state = REJECTED;\n  self.outcome = error;\n  var i = -1;\n  var len = self.queue.length;\n  while (++i < len) {\n    self.queue[i].callRejected(error);\n  }\n  return self;\n};\n\nfunction getThen(obj) {\n  // Make sure we only access the accessor once as required by the spec\n  var then = obj && obj.then;\n  if (obj && (typeof obj === 'object' || typeof obj === 'function') && typeof then === 'function') {\n    return function appyThen() {\n      then.apply(obj, arguments);\n    };\n  }\n}\n\nfunction safelyResolveThenable(self, thenable) {\n  // Either fulfill, reject or reject with error\n  var called = false;\n  function onError(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.reject(self, value);\n  }\n\n  function onSuccess(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.resolve(self, value);\n  }\n\n  function tryToUnwrap() {\n    thenable(onSuccess, onError);\n  }\n\n  var result = tryCatch(tryToUnwrap);\n  if (result.status === 'error') {\n    onError(result.value);\n  }\n}\n\nfunction tryCatch(func, value) {\n  var out = {};\n  try {\n    out.value = func(value);\n    out.status = 'success';\n  } catch (e) {\n    out.status = 'error';\n    out.value = e;\n  }\n  return out;\n}\n\nPromise.resolve = resolve;\nfunction resolve(value) {\n  if (value instanceof this) {\n    return value;\n  }\n  return handlers.resolve(new this(INTERNAL), value);\n}\n\nPromise.reject = reject;\nfunction reject(reason) {\n  var promise = new this(INTERNAL);\n  return handlers.reject(promise, reason);\n}\n\nPromise.all = all;\nfunction all(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var values = new Array(len);\n  var resolved = 0;\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    allResolver(iterable[i], i);\n  }\n  return promise;\n  function allResolver(value, i) {\n    self.resolve(value).then(resolveFromAll, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n    function resolveFromAll(outValue) {\n      values[i] = outValue;\n      if (++resolved === len && !called) {\n        called = true;\n        handlers.resolve(promise, values);\n      }\n    }\n  }\n}\n\nPromise.race = race;\nfunction race(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    resolver(iterable[i]);\n  }\n  return promise;\n  function resolver(value) {\n    self.resolve(value).then(function (response) {\n      if (!called) {\n        called = true;\n        handlers.resolve(promise, response);\n      }\n    }, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n  }\n}\n\n},{\"1\":1}],3:[function(_dereq_,module,exports){\n(function (global){\n'use strict';\nif (typeof global.Promise !== 'function') {\n  global.Promise = _dereq_(2);\n}\n\n}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{\"2\":2}],4:[function(_dereq_,module,exports){\n'use strict';\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction getIDB() {\n    /* global indexedDB,webkitIndexedDB,mozIndexedDB,OIndexedDB,msIndexedDB */\n    try {\n        if (typeof indexedDB !== 'undefined') {\n            return indexedDB;\n        }\n        if (typeof webkitIndexedDB !== 'undefined') {\n            return webkitIndexedDB;\n        }\n        if (typeof mozIndexedDB !== 'undefined') {\n            return mozIndexedDB;\n        }\n        if (typeof OIndexedDB !== 'undefined') {\n            return OIndexedDB;\n        }\n        if (typeof msIndexedDB !== 'undefined') {\n            return msIndexedDB;\n        }\n    } catch (e) {\n        return;\n    }\n}\n\nvar idb = getIDB();\n\nfunction isIndexedDBValid() {\n    try {\n        // Initialize IndexedDB; fall back to vendor-prefixed versions\n        // if needed.\n        if (!idb) {\n            return false;\n        }\n        // We mimic PouchDB here;\n        //\n        // We test for openDatabase because IE Mobile identifies itself\n        // as Safari. Oh the lulz...\n        var isSafari = typeof openDatabase !== 'undefined' && /(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent) && !/BlackBerry/.test(navigator.platform);\n\n        var hasFetch = typeof fetch === 'function' && fetch.toString().indexOf('[native code') !== -1;\n\n        // Safari <10.1 does not meet our requirements for IDB support (#5572)\n        // since Safari 10.1 shipped with fetch, we can use that to detect it\n        return (!isSafari || hasFetch) && typeof indexedDB !== 'undefined' &&\n        // some outdated implementations of IDB that appear on Samsung\n        // and HTC Android devices <4.4 are missing IDBKeyRange\n        // See: https://github.com/mozilla/localForage/issues/128\n        // See: https://github.com/mozilla/localForage/issues/272\n        typeof IDBKeyRange !== 'undefined';\n    } catch (e) {\n        return false;\n    }\n}\n\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor. (i.e.\n// old QtWebKit versions, at least).\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor. (i.e.\n// old QtWebKit versions, at least).\nfunction createBlob(parts, properties) {\n    /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n    parts = parts || [];\n    properties = properties || {};\n    try {\n        return new Blob(parts, properties);\n    } catch (e) {\n        if (e.name !== 'TypeError') {\n            throw e;\n        }\n        var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder : typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder : typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : WebKitBlobBuilder;\n        var builder = new Builder();\n        for (var i = 0; i < parts.length; i += 1) {\n            builder.append(parts[i]);\n        }\n        return builder.getBlob(properties.type);\n    }\n}\n\n// This is CommonJS because lie is an external dependency, so Rollup\n// can just ignore it.\nif (typeof Promise === 'undefined') {\n    // In the \"nopromises\" build this will just throw if you don't have\n    // a global promise object, but it would throw anyway later.\n    _dereq_(3);\n}\nvar Promise$1 = Promise;\n\nfunction executeCallback(promise, callback) {\n    if (callback) {\n        promise.then(function (result) {\n            callback(null, result);\n        }, function (error) {\n            callback(error);\n        });\n    }\n}\n\nfunction executeTwoCallbacks(promise, callback, errorCallback) {\n    if (typeof callback === 'function') {\n        promise.then(callback);\n    }\n\n    if (typeof errorCallback === 'function') {\n        promise[\"catch\"](errorCallback);\n    }\n}\n\nfunction normalizeKey(key) {\n    // Cast the key to a string, as that's all we can set as a key.\n    if (typeof key !== 'string') {\n        console.warn(key + ' used as a key, but it is not a string.');\n        key = String(key);\n    }\n\n    return key;\n}\n\nfunction getCallback() {\n    if (arguments.length && typeof arguments[arguments.length - 1] === 'function') {\n        return arguments[arguments.length - 1];\n    }\n}\n\n// Some code originally from async_storage.js in\n// [Gaia](https://github.com/mozilla-b2g/gaia).\n\nvar DETECT_BLOB_SUPPORT_STORE = 'local-forage-detect-blob-support';\nvar supportsBlobs = void 0;\nvar dbContexts = {};\nvar toString = Object.prototype.toString;\n\n// Transaction Modes\nvar READ_ONLY = 'readonly';\nvar READ_WRITE = 'readwrite';\n\n// Transform a binary string to an array buffer, because otherwise\n// weird stuff happens when you try to work with the binary string directly.\n// It is known.\n// From http://stackoverflow.com/questions/14967647/ (continues on next line)\n// encode-decode-image-with-base64-breaks-image (2013-04-21)\nfunction _binStringToArrayBuffer(bin) {\n    var length = bin.length;\n    var buf = new ArrayBuffer(length);\n    var arr = new Uint8Array(buf);\n    for (var i = 0; i < length; i++) {\n        arr[i] = bin.charCodeAt(i);\n    }\n    return buf;\n}\n\n//\n// Blobs are not supported in all versions of IndexedDB, notably\n// Chrome <37 and Android <5. In those versions, storing a blob will throw.\n//\n// Various other blob bugs exist in Chrome v37-42 (inclusive).\n// Detecting them is expensive and confusing to users, and Chrome 37-42\n// is at very low usage worldwide, so we do a hacky userAgent check instead.\n//\n// content-type bug: https://code.google.com/p/chromium/issues/detail?id=408120\n// 404 bug: https://code.google.com/p/chromium/issues/detail?id=447916\n// FileReader bug: https://code.google.com/p/chromium/issues/detail?id=447836\n//\n// Code borrowed from PouchDB. See:\n// https://github.com/pouchdb/pouchdb/blob/master/packages/node_modules/pouchdb-adapter-idb/src/blobSupport.js\n//\nfunction _checkBlobSupportWithoutCaching(idb) {\n    return new Promise$1(function (resolve) {\n        var txn = idb.transaction(DETECT_BLOB_SUPPORT_STORE, READ_WRITE);\n        var blob = createBlob(['']);\n        txn.objectStore(DETECT_BLOB_SUPPORT_STORE).put(blob, 'key');\n\n        txn.onabort = function (e) {\n            // If the transaction aborts now its due to not being able to\n            // write to the database, likely due to the disk being full\n            e.preventDefault();\n            e.stopPropagation();\n            resolve(false);\n        };\n\n        txn.oncomplete = function () {\n            var matchedChrome = navigator.userAgent.match(/Chrome\\/(\\d+)/);\n            var matchedEdge = navigator.userAgent.match(/Edge\\//);\n            // MS Edge pretends to be Chrome 42:\n            // https://msdn.microsoft.com/en-us/library/hh869301%28v=vs.85%29.aspx\n            resolve(matchedEdge || !matchedChrome || parseInt(matchedChrome[1], 10) >= 43);\n        };\n    })[\"catch\"](function () {\n        return false; // error, so assume unsupported\n    });\n}\n\nfunction _checkBlobSupport(idb) {\n    if (typeof supportsBlobs === 'boolean') {\n        return Promise$1.resolve(supportsBlobs);\n    }\n    return _checkBlobSupportWithoutCaching(idb).then(function (value) {\n        supportsBlobs = value;\n        return supportsBlobs;\n    });\n}\n\nfunction _deferReadiness(dbInfo) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Create a deferred object representing the current database operation.\n    var deferredOperation = {};\n\n    deferredOperation.promise = new Promise$1(function (resolve, reject) {\n        deferredOperation.resolve = resolve;\n        deferredOperation.reject = reject;\n    });\n\n    // Enqueue the deferred operation.\n    dbContext.deferredOperations.push(deferredOperation);\n\n    // Chain its promise to the database readiness.\n    if (!dbContext.dbReady) {\n        dbContext.dbReady = deferredOperation.promise;\n    } else {\n        dbContext.dbReady = dbContext.dbReady.then(function () {\n            return deferredOperation.promise;\n        });\n    }\n}\n\nfunction _advanceReadiness(dbInfo) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Dequeue a deferred operation.\n    var deferredOperation = dbContext.deferredOperations.pop();\n\n    // Resolve its promise (which is part of the database readiness\n    // chain of promises).\n    if (deferredOperation) {\n        deferredOperation.resolve();\n        return deferredOperation.promise;\n    }\n}\n\nfunction _rejectReadiness(dbInfo, err) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Dequeue a deferred operation.\n    var deferredOperation = dbContext.deferredOperations.pop();\n\n    // Reject its promise (which is part of the database readiness\n    // chain of promises).\n    if (deferredOperation) {\n        deferredOperation.reject(err);\n        return deferredOperation.promise;\n    }\n}\n\nfunction _getConnection(dbInfo, upgradeNeeded) {\n    return new Promise$1(function (resolve, reject) {\n        dbContexts[dbInfo.name] = dbContexts[dbInfo.name] || createDbContext();\n\n        if (dbInfo.db) {\n            if (upgradeNeeded) {\n                _deferReadiness(dbInfo);\n                dbInfo.db.close();\n            } else {\n                return resolve(dbInfo.db);\n            }\n        }\n\n        var dbArgs = [dbInfo.name];\n\n        if (upgradeNeeded) {\n            dbArgs.push(dbInfo.version);\n        }\n\n        var openreq = idb.open.apply(idb, dbArgs);\n\n        if (upgradeNeeded) {\n            openreq.onupgradeneeded = function (e) {\n                var db = openreq.result;\n                try {\n                    db.createObjectStore(dbInfo.storeName);\n                    if (e.oldVersion <= 1) {\n                        // Added when support for blob shims was added\n                        db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);\n                    }\n                } catch (ex) {\n                    if (ex.name === 'ConstraintError') {\n                        console.warn('The database \"' + dbInfo.name + '\"' + ' has been upgraded from version ' + e.oldVersion + ' to version ' + e.newVersion + ', but the storage \"' + dbInfo.storeName + '\" already exists.');\n                    } else {\n                        throw ex;\n                    }\n                }\n            };\n        }\n\n        openreq.onerror = function (e) {\n            e.preventDefault();\n            reject(openreq.error);\n        };\n\n        openreq.onsuccess = function () {\n            resolve(openreq.result);\n            _advanceReadiness(dbInfo);\n        };\n    });\n}\n\nfunction _getOriginalConnection(dbInfo) {\n    return _getConnection(dbInfo, false);\n}\n\nfunction _getUpgradedConnection(dbInfo) {\n    return _getConnection(dbInfo, true);\n}\n\nfunction _isUpgradeNeeded(dbInfo, defaultVersion) {\n    if (!dbInfo.db) {\n        return true;\n    }\n\n    var isNewStore = !dbInfo.db.objectStoreNames.contains(dbInfo.storeName);\n    var isDowngrade = dbInfo.version < dbInfo.db.version;\n    var isUpgrade = dbInfo.version > dbInfo.db.version;\n\n    if (isDowngrade) {\n        // If the version is not the default one\n        // then warn for impossible downgrade.\n        if (dbInfo.version !== defaultVersion) {\n            console.warn('The database \"' + dbInfo.name + '\"' + \" can't be downgraded from version \" + dbInfo.db.version + ' to version ' + dbInfo.version + '.');\n        }\n        // Align the versions to prevent errors.\n        dbInfo.version = dbInfo.db.version;\n    }\n\n    if (isUpgrade || isNewStore) {\n        // If the store is new then increment the version (if needed).\n        // This will trigger an \"upgradeneeded\" event which is required\n        // for creating a store.\n        if (isNewStore) {\n            var incVersion = dbInfo.db.version + 1;\n            if (incVersion > dbInfo.version) {\n                dbInfo.version = incVersion;\n            }\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n// encode a blob for indexeddb engines that don't support blobs\nfunction _encodeBlob(blob) {\n    return new Promise$1(function (resolve, reject) {\n        var reader = new FileReader();\n        reader.onerror = reject;\n        reader.onloadend = function (e) {\n            var base64 = btoa(e.target.result || '');\n            resolve({\n                __local_forage_encoded_blob: true,\n                data: base64,\n                type: blob.type\n            });\n        };\n        reader.readAsBinaryString(blob);\n    });\n}\n\n// decode an encoded blob\nfunction _decodeBlob(encodedBlob) {\n    var arrayBuff = _binStringToArrayBuffer(atob(encodedBlob.data));\n    return createBlob([arrayBuff], { type: encodedBlob.type });\n}\n\n// is this one of our fancy encoded blobs?\nfunction _isEncodedBlob(value) {\n    return value && value.__local_forage_encoded_blob;\n}\n\n// Specialize the default `ready()` function by making it dependent\n// on the current database operations. Thus, the driver will be actually\n// ready when it's been initialized (default) *and* there are no pending\n// operations on the database (initiated by some other instances).\nfunction _fullyReady(callback) {\n    var self = this;\n\n    var promise = self._initReady().then(function () {\n        var dbContext = dbContexts[self._dbInfo.name];\n\n        if (dbContext && dbContext.dbReady) {\n            return dbContext.dbReady;\n        }\n    });\n\n    executeTwoCallbacks(promise, callback, callback);\n    return promise;\n}\n\n// Try to establish a new db connection to replace the\n// current one which is broken (i.e. experiencing\n// InvalidStateError while creating a transaction).\nfunction _tryReconnect(dbInfo) {\n    _deferReadiness(dbInfo);\n\n    var dbContext = dbContexts[dbInfo.name];\n    var forages = dbContext.forages;\n\n    for (var i = 0; i < forages.length; i++) {\n        var forage = forages[i];\n        if (forage._dbInfo.db) {\n            forage._dbInfo.db.close();\n            forage._dbInfo.db = null;\n        }\n    }\n    dbInfo.db = null;\n\n    return _getOriginalConnection(dbInfo).then(function (db) {\n        dbInfo.db = db;\n        if (_isUpgradeNeeded(dbInfo)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n        }\n        return db;\n    }).then(function (db) {\n        // store the latest db reference\n        // in case the db was upgraded\n        dbInfo.db = dbContext.db = db;\n        for (var i = 0; i < forages.length; i++) {\n            forages[i]._dbInfo.db = db;\n        }\n    })[\"catch\"](function (err) {\n        _rejectReadiness(dbInfo, err);\n        throw err;\n    });\n}\n\n// FF doesn't like Promises (micro-tasks) and IDDB store operations,\n// so we have to do it with callbacks\nfunction createTransaction(dbInfo, mode, callback, retries) {\n    if (retries === undefined) {\n        retries = 1;\n    }\n\n    try {\n        var tx = dbInfo.db.transaction(dbInfo.storeName, mode);\n        callback(null, tx);\n    } catch (err) {\n        if (retries > 0 && (!dbInfo.db || err.name === 'InvalidStateError' || err.name === 'NotFoundError')) {\n            return Promise$1.resolve().then(function () {\n                if (!dbInfo.db || err.name === 'NotFoundError' && !dbInfo.db.objectStoreNames.contains(dbInfo.storeName) && dbInfo.version <= dbInfo.db.version) {\n                    // increase the db version, to create the new ObjectStore\n                    if (dbInfo.db) {\n                        dbInfo.version = dbInfo.db.version + 1;\n                    }\n                    // Reopen the database for upgrading.\n                    return _getUpgradedConnection(dbInfo);\n                }\n            }).then(function () {\n                return _tryReconnect(dbInfo).then(function () {\n                    createTransaction(dbInfo, mode, callback, retries - 1);\n                });\n            })[\"catch\"](callback);\n        }\n\n        callback(err);\n    }\n}\n\nfunction createDbContext() {\n    return {\n        // Running localForages sharing a database.\n        forages: [],\n        // Shared database.\n        db: null,\n        // Database readiness (promise).\n        dbReady: null,\n        // Deferred operations on the database.\n        deferredOperations: []\n    };\n}\n\n// Open the IndexedDB database (automatically creates one if one didn't\n// previously exist), using any options set in the config.\nfunction _initStorage(options) {\n    var self = this;\n    var dbInfo = {\n        db: null\n    };\n\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = options[i];\n        }\n    }\n\n    // Get the current context of the database;\n    var dbContext = dbContexts[dbInfo.name];\n\n    // ...or create a new context.\n    if (!dbContext) {\n        dbContext = createDbContext();\n        // Register the new context in the global container.\n        dbContexts[dbInfo.name] = dbContext;\n    }\n\n    // Register itself as a running localForage in the current context.\n    dbContext.forages.push(self);\n\n    // Replace the default `ready()` function with the specialized one.\n    if (!self._initReady) {\n        self._initReady = self.ready;\n        self.ready = _fullyReady;\n    }\n\n    // Create an array of initialization states of the related localForages.\n    var initPromises = [];\n\n    function ignoreErrors() {\n        // Don't handle errors here,\n        // just makes sure related localForages aren't pending.\n        return Promise$1.resolve();\n    }\n\n    for (var j = 0; j < dbContext.forages.length; j++) {\n        var forage = dbContext.forages[j];\n        if (forage !== self) {\n            // Don't wait for itself...\n            initPromises.push(forage._initReady()[\"catch\"](ignoreErrors));\n        }\n    }\n\n    // Take a snapshot of the related localForages.\n    var forages = dbContext.forages.slice(0);\n\n    // Initialize the connection process only when\n    // all the related localForages aren't pending.\n    return Promise$1.all(initPromises).then(function () {\n        dbInfo.db = dbContext.db;\n        // Get the connection or open a new one without upgrade.\n        return _getOriginalConnection(dbInfo);\n    }).then(function (db) {\n        dbInfo.db = db;\n        if (_isUpgradeNeeded(dbInfo, self._defaultConfig.version)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n        }\n        return db;\n    }).then(function (db) {\n        dbInfo.db = dbContext.db = db;\n        self._dbInfo = dbInfo;\n        // Share the final connection amongst related localForages.\n        for (var k = 0; k < forages.length; k++) {\n            var forage = forages[k];\n            if (forage !== self) {\n                // Self is already up-to-date.\n                forage._dbInfo.db = dbInfo.db;\n                forage._dbInfo.version = dbInfo.version;\n            }\n        }\n    });\n}\n\nfunction getItem(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.get(key);\n\n                    req.onsuccess = function () {\n                        var value = req.result;\n                        if (value === undefined) {\n                            value = null;\n                        }\n                        if (_isEncodedBlob(value)) {\n                            value = _decodeBlob(value);\n                        }\n                        resolve(value);\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Iterate over all items stored in database.\nfunction iterate(iterator, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.openCursor();\n                    var iterationNumber = 1;\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n\n                        if (cursor) {\n                            var value = cursor.value;\n                            if (_isEncodedBlob(value)) {\n                                value = _decodeBlob(value);\n                            }\n                            var result = iterator(value, cursor.key, iterationNumber++);\n\n                            // when the iterator callback retuns any\n                            // (non-`undefined`) value, then we stop\n                            // the iteration immediately\n                            if (result !== void 0) {\n                                resolve(result);\n                            } else {\n                                cursor[\"continue\"]();\n                            }\n                        } else {\n                            resolve();\n                        }\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n\n    return promise;\n}\n\nfunction setItem(key, value, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        var dbInfo;\n        self.ready().then(function () {\n            dbInfo = self._dbInfo;\n            if (toString.call(value) === '[object Blob]') {\n                return _checkBlobSupport(dbInfo.db).then(function (blobSupport) {\n                    if (blobSupport) {\n                        return value;\n                    }\n                    return _encodeBlob(value);\n                });\n            }\n            return value;\n        }).then(function (value) {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n\n                    // The reason we don't _save_ null is because IE 10 does\n                    // not support saving the `null` type in IndexedDB. How\n                    // ironic, given the bug below!\n                    // See: https://github.com/mozilla/localForage/issues/161\n                    if (value === null) {\n                        value = undefined;\n                    }\n\n                    var req = store.put(value, key);\n\n                    transaction.oncomplete = function () {\n                        // Cast to undefined so the value passed to\n                        // callback/promise is the same as what one would get out\n                        // of `getItem()` later. This leads to some weirdness\n                        // (setItem('foo', undefined) will return `null`), but\n                        // it's not my fault localStorage is our baseline and that\n                        // it's weird.\n                        if (value === undefined) {\n                            value = null;\n                        }\n\n                        resolve(value);\n                    };\n                    transaction.onabort = transaction.onerror = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction removeItem(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    // We use a Grunt task to make this safe for IE and some\n                    // versions of Android (including those used by Cordova).\n                    // Normally IE won't like `.delete()` and will insist on\n                    // using `['delete']()`, but we have a build step that\n                    // fixes this for us now.\n                    var req = store[\"delete\"](key);\n                    transaction.oncomplete = function () {\n                        resolve();\n                    };\n\n                    transaction.onerror = function () {\n                        reject(req.error);\n                    };\n\n                    // The request will be also be aborted if we've exceeded our storage\n                    // space.\n                    transaction.onabort = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction clear(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.clear();\n\n                    transaction.oncomplete = function () {\n                        resolve();\n                    };\n\n                    transaction.onabort = transaction.onerror = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction length(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.count();\n\n                    req.onsuccess = function () {\n                        resolve(req.result);\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction key(n, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        if (n < 0) {\n            resolve(null);\n\n            return;\n        }\n\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var advanced = false;\n                    var req = store.openCursor();\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n                        if (!cursor) {\n                            // this means there weren't enough keys\n                            resolve(null);\n\n                            return;\n                        }\n\n                        if (n === 0) {\n                            // We have the first key, return it if that's what they\n                            // wanted.\n                            resolve(cursor.key);\n                        } else {\n                            if (!advanced) {\n                                // Otherwise, ask the cursor to skip ahead n\n                                // records.\n                                advanced = true;\n                                cursor.advance(n);\n                            } else {\n                                // When we get here, we've got the nth key.\n                                resolve(cursor.key);\n                            }\n                        }\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.openCursor();\n                    var keys = [];\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n\n                        if (!cursor) {\n                            resolve(keys);\n                            return;\n                        }\n\n                        keys.push(cursor.key);\n                        cursor[\"continue\"]();\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction dropInstance(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    var currentConfig = this.config();\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        var isCurrentDb = options.name === currentConfig.name && self._dbInfo.db;\n\n        var dbPromise = isCurrentDb ? Promise$1.resolve(self._dbInfo.db) : _getOriginalConnection(options).then(function (db) {\n            var dbContext = dbContexts[options.name];\n            var forages = dbContext.forages;\n            dbContext.db = db;\n            for (var i = 0; i < forages.length; i++) {\n                forages[i]._dbInfo.db = db;\n            }\n            return db;\n        });\n\n        if (!options.storeName) {\n            promise = dbPromise.then(function (db) {\n                _deferReadiness(options);\n\n                var dbContext = dbContexts[options.name];\n                var forages = dbContext.forages;\n\n                db.close();\n                for (var i = 0; i < forages.length; i++) {\n                    var forage = forages[i];\n                    forage._dbInfo.db = null;\n                }\n\n                var dropDBPromise = new Promise$1(function (resolve, reject) {\n                    var req = idb.deleteDatabase(options.name);\n\n                    req.onerror = req.onblocked = function (err) {\n                        var db = req.result;\n                        if (db) {\n                            db.close();\n                        }\n                        reject(err);\n                    };\n\n                    req.onsuccess = function () {\n                        var db = req.result;\n                        if (db) {\n                            db.close();\n                        }\n                        resolve(db);\n                    };\n                });\n\n                return dropDBPromise.then(function (db) {\n                    dbContext.db = db;\n                    for (var i = 0; i < forages.length; i++) {\n                        var _forage = forages[i];\n                        _advanceReadiness(_forage._dbInfo);\n                    }\n                })[\"catch\"](function (err) {\n                    (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                    throw err;\n                });\n            });\n        } else {\n            promise = dbPromise.then(function (db) {\n                if (!db.objectStoreNames.contains(options.storeName)) {\n                    return;\n                }\n\n                var newVersion = db.version + 1;\n\n                _deferReadiness(options);\n\n                var dbContext = dbContexts[options.name];\n                var forages = dbContext.forages;\n\n                db.close();\n                for (var i = 0; i < forages.length; i++) {\n                    var forage = forages[i];\n                    forage._dbInfo.db = null;\n                    forage._dbInfo.version = newVersion;\n                }\n\n                var dropObjectPromise = new Promise$1(function (resolve, reject) {\n                    var req = idb.open(options.name, newVersion);\n\n                    req.onerror = function (err) {\n                        var db = req.result;\n                        db.close();\n                        reject(err);\n                    };\n\n                    req.onupgradeneeded = function () {\n                        var db = req.result;\n                        db.deleteObjectStore(options.storeName);\n                    };\n\n                    req.onsuccess = function () {\n                        var db = req.result;\n                        db.close();\n                        resolve(db);\n                    };\n                });\n\n                return dropObjectPromise.then(function (db) {\n                    dbContext.db = db;\n                    for (var j = 0; j < forages.length; j++) {\n                        var _forage2 = forages[j];\n                        _forage2._dbInfo.db = db;\n                        _advanceReadiness(_forage2._dbInfo);\n                    }\n                })[\"catch\"](function (err) {\n                    (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                    throw err;\n                });\n            });\n        }\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar asyncStorage = {\n    _driver: 'asyncStorage',\n    _initStorage: _initStorage,\n    _support: isIndexedDBValid(),\n    iterate: iterate,\n    getItem: getItem,\n    setItem: setItem,\n    removeItem: removeItem,\n    clear: clear,\n    length: length,\n    key: key,\n    keys: keys,\n    dropInstance: dropInstance\n};\n\nfunction isWebSQLValid() {\n    return typeof openDatabase === 'function';\n}\n\n// Sadly, the best way to save binary data in WebSQL/localStorage is serializing\n// it to Base64, so this is how we store it to prevent very strange errors with less\n// verbose ways of binary <-> string data storage.\nvar BASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nvar BLOB_TYPE_PREFIX = '~~local_forage_type~';\nvar BLOB_TYPE_PREFIX_REGEX = /^~~local_forage_type~([^~]+)~/;\n\nvar SERIALIZED_MARKER = '__lfsc__:';\nvar SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER.length;\n\n// OMG the serializations!\nvar TYPE_ARRAYBUFFER = 'arbf';\nvar TYPE_BLOB = 'blob';\nvar TYPE_INT8ARRAY = 'si08';\nvar TYPE_UINT8ARRAY = 'ui08';\nvar TYPE_UINT8CLAMPEDARRAY = 'uic8';\nvar TYPE_INT16ARRAY = 'si16';\nvar TYPE_INT32ARRAY = 'si32';\nvar TYPE_UINT16ARRAY = 'ur16';\nvar TYPE_UINT32ARRAY = 'ui32';\nvar TYPE_FLOAT32ARRAY = 'fl32';\nvar TYPE_FLOAT64ARRAY = 'fl64';\nvar TYPE_SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER_LENGTH + TYPE_ARRAYBUFFER.length;\n\nvar toString$1 = Object.prototype.toString;\n\nfunction stringToBuffer(serializedString) {\n    // Fill the string into a ArrayBuffer.\n    var bufferLength = serializedString.length * 0.75;\n    var len = serializedString.length;\n    var i;\n    var p = 0;\n    var encoded1, encoded2, encoded3, encoded4;\n\n    if (serializedString[serializedString.length - 1] === '=') {\n        bufferLength--;\n        if (serializedString[serializedString.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    var buffer = new ArrayBuffer(bufferLength);\n    var bytes = new Uint8Array(buffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = BASE_CHARS.indexOf(serializedString[i]);\n        encoded2 = BASE_CHARS.indexOf(serializedString[i + 1]);\n        encoded3 = BASE_CHARS.indexOf(serializedString[i + 2]);\n        encoded4 = BASE_CHARS.indexOf(serializedString[i + 3]);\n\n        /*jslint bitwise: true */\n        bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n        bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n        bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n    }\n    return buffer;\n}\n\n// Converts a buffer to a string to store, serialized, in the backend\n// storage library.\nfunction bufferToString(buffer) {\n    // base64-arraybuffer\n    var bytes = new Uint8Array(buffer);\n    var base64String = '';\n    var i;\n\n    for (i = 0; i < bytes.length; i += 3) {\n        /*jslint bitwise: true */\n        base64String += BASE_CHARS[bytes[i] >> 2];\n        base64String += BASE_CHARS[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n        base64String += BASE_CHARS[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n        base64String += BASE_CHARS[bytes[i + 2] & 63];\n    }\n\n    if (bytes.length % 3 === 2) {\n        base64String = base64String.substring(0, base64String.length - 1) + '=';\n    } else if (bytes.length % 3 === 1) {\n        base64String = base64String.substring(0, base64String.length - 2) + '==';\n    }\n\n    return base64String;\n}\n\n// Serialize a value, afterwards executing a callback (which usually\n// instructs the `setItem()` callback/promise to be executed). This is how\n// we store binary data with localStorage.\nfunction serialize(value, callback) {\n    var valueType = '';\n    if (value) {\n        valueType = toString$1.call(value);\n    }\n\n    // Cannot use `value instanceof ArrayBuffer` or such here, as these\n    // checks fail when running the tests using casper.js...\n    //\n    // TODO: See why those tests fail and use a better solution.\n    if (value && (valueType === '[object ArrayBuffer]' || value.buffer && toString$1.call(value.buffer) === '[object ArrayBuffer]')) {\n        // Convert binary arrays to a string and prefix the string with\n        // a special marker.\n        var buffer;\n        var marker = SERIALIZED_MARKER;\n\n        if (value instanceof ArrayBuffer) {\n            buffer = value;\n            marker += TYPE_ARRAYBUFFER;\n        } else {\n            buffer = value.buffer;\n\n            if (valueType === '[object Int8Array]') {\n                marker += TYPE_INT8ARRAY;\n            } else if (valueType === '[object Uint8Array]') {\n                marker += TYPE_UINT8ARRAY;\n            } else if (valueType === '[object Uint8ClampedArray]') {\n                marker += TYPE_UINT8CLAMPEDARRAY;\n            } else if (valueType === '[object Int16Array]') {\n                marker += TYPE_INT16ARRAY;\n            } else if (valueType === '[object Uint16Array]') {\n                marker += TYPE_UINT16ARRAY;\n            } else if (valueType === '[object Int32Array]') {\n                marker += TYPE_INT32ARRAY;\n            } else if (valueType === '[object Uint32Array]') {\n                marker += TYPE_UINT32ARRAY;\n            } else if (valueType === '[object Float32Array]') {\n                marker += TYPE_FLOAT32ARRAY;\n            } else if (valueType === '[object Float64Array]') {\n                marker += TYPE_FLOAT64ARRAY;\n            } else {\n                callback(new Error('Failed to get type for BinaryArray'));\n            }\n        }\n\n        callback(marker + bufferToString(buffer));\n    } else if (valueType === '[object Blob]') {\n        // Conver the blob to a binaryArray and then to a string.\n        var fileReader = new FileReader();\n\n        fileReader.onload = function () {\n            // Backwards-compatible prefix for the blob type.\n            var str = BLOB_TYPE_PREFIX + value.type + '~' + bufferToString(this.result);\n\n            callback(SERIALIZED_MARKER + TYPE_BLOB + str);\n        };\n\n        fileReader.readAsArrayBuffer(value);\n    } else {\n        try {\n            callback(JSON.stringify(value));\n        } catch (e) {\n            console.error(\"Couldn't convert value into a JSON string: \", value);\n\n            callback(null, e);\n        }\n    }\n}\n\n// Deserialize data we've inserted into a value column/field. We place\n// special markers into our strings to mark them as encoded; this isn't\n// as nice as a meta field, but it's the only sane thing we can do whilst\n// keeping localStorage support intact.\n//\n// Oftentimes this will just deserialize JSON content, but if we have a\n// special marker (SERIALIZED_MARKER, defined above), we will extract\n// some kind of arraybuffer/binary data/typed array out of the string.\nfunction deserialize(value) {\n    // If we haven't marked this string as being specially serialized (i.e.\n    // something other than serialized JSON), we can just return it and be\n    // done with it.\n    if (value.substring(0, SERIALIZED_MARKER_LENGTH) !== SERIALIZED_MARKER) {\n        return JSON.parse(value);\n    }\n\n    // The following code deals with deserializing some kind of Blob or\n    // TypedArray. First we separate out the type of data we're dealing\n    // with from the data itself.\n    var serializedString = value.substring(TYPE_SERIALIZED_MARKER_LENGTH);\n    var type = value.substring(SERIALIZED_MARKER_LENGTH, TYPE_SERIALIZED_MARKER_LENGTH);\n\n    var blobType;\n    // Backwards-compatible blob type serialization strategy.\n    // DBs created with older versions of localForage will simply not have the blob type.\n    if (type === TYPE_BLOB && BLOB_TYPE_PREFIX_REGEX.test(serializedString)) {\n        var matcher = serializedString.match(BLOB_TYPE_PREFIX_REGEX);\n        blobType = matcher[1];\n        serializedString = serializedString.substring(matcher[0].length);\n    }\n    var buffer = stringToBuffer(serializedString);\n\n    // Return the right type based on the code/type set during\n    // serialization.\n    switch (type) {\n        case TYPE_ARRAYBUFFER:\n            return buffer;\n        case TYPE_BLOB:\n            return createBlob([buffer], { type: blobType });\n        case TYPE_INT8ARRAY:\n            return new Int8Array(buffer);\n        case TYPE_UINT8ARRAY:\n            return new Uint8Array(buffer);\n        case TYPE_UINT8CLAMPEDARRAY:\n            return new Uint8ClampedArray(buffer);\n        case TYPE_INT16ARRAY:\n            return new Int16Array(buffer);\n        case TYPE_UINT16ARRAY:\n            return new Uint16Array(buffer);\n        case TYPE_INT32ARRAY:\n            return new Int32Array(buffer);\n        case TYPE_UINT32ARRAY:\n            return new Uint32Array(buffer);\n        case TYPE_FLOAT32ARRAY:\n            return new Float32Array(buffer);\n        case TYPE_FLOAT64ARRAY:\n            return new Float64Array(buffer);\n        default:\n            throw new Error('Unkown type: ' + type);\n    }\n}\n\nvar localforageSerializer = {\n    serialize: serialize,\n    deserialize: deserialize,\n    stringToBuffer: stringToBuffer,\n    bufferToString: bufferToString\n};\n\n/*\n * Includes code from:\n *\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 Niklas von Hertzen\n * Licensed under the MIT license.\n */\n\nfunction createDbTable(t, dbInfo, callback, errorCallback) {\n    t.executeSql('CREATE TABLE IF NOT EXISTS ' + dbInfo.storeName + ' ' + '(id INTEGER PRIMARY KEY, key unique, value)', [], callback, errorCallback);\n}\n\n// Open the WebSQL database (automatically creates one if one didn't\n// previously exist), using any options set in the config.\nfunction _initStorage$1(options) {\n    var self = this;\n    var dbInfo = {\n        db: null\n    };\n\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = typeof options[i] !== 'string' ? options[i].toString() : options[i];\n        }\n    }\n\n    var dbInfoPromise = new Promise$1(function (resolve, reject) {\n        // Open the database; the openDatabase API will automatically\n        // create it for us if it doesn't exist.\n        try {\n            dbInfo.db = openDatabase(dbInfo.name, String(dbInfo.version), dbInfo.description, dbInfo.size);\n        } catch (e) {\n            return reject(e);\n        }\n\n        // Create our key/value table if it doesn't exist.\n        dbInfo.db.transaction(function (t) {\n            createDbTable(t, dbInfo, function () {\n                self._dbInfo = dbInfo;\n                resolve();\n            }, function (t, error) {\n                reject(error);\n            });\n        }, reject);\n    });\n\n    dbInfo.serializer = localforageSerializer;\n    return dbInfoPromise;\n}\n\nfunction tryExecuteSql(t, dbInfo, sqlStatement, args, callback, errorCallback) {\n    t.executeSql(sqlStatement, args, callback, function (t, error) {\n        if (error.code === error.SYNTAX_ERR) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name = ?\", [dbInfo.storeName], function (t, results) {\n                if (!results.rows.length) {\n                    // if the table is missing (was deleted)\n                    // re-create it table and retry\n                    createDbTable(t, dbInfo, function () {\n                        t.executeSql(sqlStatement, args, callback, errorCallback);\n                    }, errorCallback);\n                } else {\n                    errorCallback(t, error);\n                }\n            }, errorCallback);\n        } else {\n            errorCallback(t, error);\n        }\n    }, errorCallback);\n}\n\nfunction getItem$1(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName + ' WHERE key = ? LIMIT 1', [key], function (t, results) {\n                    var result = results.rows.length ? results.rows.item(0).value : null;\n\n                    // Check to see if this is serialized content we need to\n                    // unpack.\n                    if (result) {\n                        result = dbInfo.serializer.deserialize(result);\n                    }\n\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction iterate$1(iterator, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var rows = results.rows;\n                    var length = rows.length;\n\n                    for (var i = 0; i < length; i++) {\n                        var item = rows.item(i);\n                        var result = item.value;\n\n                        // Check to see if this is serialized content\n                        // we need to unpack.\n                        if (result) {\n                            result = dbInfo.serializer.deserialize(result);\n                        }\n\n                        result = iterator(result, item.key, i + 1);\n\n                        // void(0) prevents problems with redefinition\n                        // of `undefined`.\n                        if (result !== void 0) {\n                            resolve(result);\n                            return;\n                        }\n                    }\n\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction _setItem(key, value, callback, retriesLeft) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            // The localStorage API doesn't return undefined values in an\n            // \"expected\" way, so undefined is always cast to null in all\n            // drivers. See: https://github.com/mozilla/localForage/pull/42\n            if (value === undefined) {\n                value = null;\n            }\n\n            // Save the original value to pass to the callback.\n            var originalValue = value;\n\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n                if (error) {\n                    reject(error);\n                } else {\n                    dbInfo.db.transaction(function (t) {\n                        tryExecuteSql(t, dbInfo, 'INSERT OR REPLACE INTO ' + dbInfo.storeName + ' ' + '(key, value) VALUES (?, ?)', [key, value], function () {\n                            resolve(originalValue);\n                        }, function (t, error) {\n                            reject(error);\n                        });\n                    }, function (sqlError) {\n                        // The transaction failed; check\n                        // to see if it's a quota error.\n                        if (sqlError.code === sqlError.QUOTA_ERR) {\n                            // We reject the callback outright for now, but\n                            // it's worth trying to re-run the transaction.\n                            // Even if the user accepts the prompt to use\n                            // more storage on Safari, this error will\n                            // be called.\n                            //\n                            // Try to re-run the transaction.\n                            if (retriesLeft > 0) {\n                                resolve(_setItem.apply(self, [key, originalValue, callback, retriesLeft - 1]));\n                                return;\n                            }\n                            reject(sqlError);\n                        }\n                    });\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction setItem$1(key, value, callback) {\n    return _setItem.apply(this, [key, value, callback, 1]);\n}\n\nfunction removeItem$1(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName + ' WHERE key = ?', [key], function () {\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Deletes every item in the table.\n// TODO: Find out if this resets the AUTO_INCREMENT number.\nfunction clear$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName, [], function () {\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Does a simple `COUNT(key)` to get the number of items stored in\n// localForage.\nfunction length$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                // Ahhh, SQL makes this one soooooo easy.\n                tryExecuteSql(t, dbInfo, 'SELECT COUNT(key) as c FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var result = results.rows.item(0).c;\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Return the key located at key index X; essentially gets the key from a\n// `WHERE id = ?`. This is the most efficient way I can think to implement\n// this rarely-used (in my experience) part of the API, but it can seem\n// inconsistent, because we do `INSERT OR REPLACE INTO` on `setItem()`, so\n// the ID of each key will change every time it's updated. Perhaps a stored\n// procedure for the `setItem()` SQL would solve this problem?\n// TODO: Don't change ID on `setItem()`.\nfunction key$1(n, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName + ' WHERE id = ? LIMIT 1', [n + 1], function (t, results) {\n                    var result = results.rows.length ? results.rows.item(0).key : null;\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var keys = [];\n\n                    for (var i = 0; i < results.rows.length; i++) {\n                        keys.push(results.rows.item(i).key);\n                    }\n\n                    resolve(keys);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// https://www.w3.org/TR/webdatabase/#databases\n// > There is no way to enumerate or delete the databases available for an origin from this API.\nfunction getAllStoreNames(db) {\n    return new Promise$1(function (resolve, reject) {\n        db.transaction(function (t) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'\", [], function (t, results) {\n                var storeNames = [];\n\n                for (var i = 0; i < results.rows.length; i++) {\n                    storeNames.push(results.rows.item(i).name);\n                }\n\n                resolve({\n                    db: db,\n                    storeNames: storeNames\n                });\n            }, function (t, error) {\n                reject(error);\n            });\n        }, function (sqlError) {\n            reject(sqlError);\n        });\n    });\n}\n\nfunction dropInstance$1(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    var currentConfig = this.config();\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        promise = new Promise$1(function (resolve) {\n            var db;\n            if (options.name === currentConfig.name) {\n                // use the db reference of the current instance\n                db = self._dbInfo.db;\n            } else {\n                db = openDatabase(options.name, '', '', 0);\n            }\n\n            if (!options.storeName) {\n                // drop all database tables\n                resolve(getAllStoreNames(db));\n            } else {\n                resolve({\n                    db: db,\n                    storeNames: [options.storeName]\n                });\n            }\n        }).then(function (operationInfo) {\n            return new Promise$1(function (resolve, reject) {\n                operationInfo.db.transaction(function (t) {\n                    function dropTable(storeName) {\n                        return new Promise$1(function (resolve, reject) {\n                            t.executeSql('DROP TABLE IF EXISTS ' + storeName, [], function () {\n                                resolve();\n                            }, function (t, error) {\n                                reject(error);\n                            });\n                        });\n                    }\n\n                    var operations = [];\n                    for (var i = 0, len = operationInfo.storeNames.length; i < len; i++) {\n                        operations.push(dropTable(operationInfo.storeNames[i]));\n                    }\n\n                    Promise$1.all(operations).then(function () {\n                        resolve();\n                    })[\"catch\"](function (e) {\n                        reject(e);\n                    });\n                }, function (sqlError) {\n                    reject(sqlError);\n                });\n            });\n        });\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar webSQLStorage = {\n    _driver: 'webSQLStorage',\n    _initStorage: _initStorage$1,\n    _support: isWebSQLValid(),\n    iterate: iterate$1,\n    getItem: getItem$1,\n    setItem: setItem$1,\n    removeItem: removeItem$1,\n    clear: clear$1,\n    length: length$1,\n    key: key$1,\n    keys: keys$1,\n    dropInstance: dropInstance$1\n};\n\nfunction isLocalStorageValid() {\n    try {\n        return typeof localStorage !== 'undefined' && 'setItem' in localStorage &&\n        // in IE8 typeof localStorage.setItem === 'object'\n        !!localStorage.setItem;\n    } catch (e) {\n        return false;\n    }\n}\n\nfunction _getKeyPrefix(options, defaultConfig) {\n    var keyPrefix = options.name + '/';\n\n    if (options.storeName !== defaultConfig.storeName) {\n        keyPrefix += options.storeName + '/';\n    }\n    return keyPrefix;\n}\n\n// Check if localStorage throws when saving an item\nfunction checkIfLocalStorageThrows() {\n    var localStorageTestKey = '_localforage_support_test';\n\n    try {\n        localStorage.setItem(localStorageTestKey, true);\n        localStorage.removeItem(localStorageTestKey);\n\n        return false;\n    } catch (e) {\n        return true;\n    }\n}\n\n// Check if localStorage is usable and allows to save an item\n// This method checks if localStorage is usable in Safari Private Browsing\n// mode, or in any other case where the available quota for localStorage\n// is 0 and there wasn't any saved items yet.\nfunction _isLocalStorageUsable() {\n    return !checkIfLocalStorageThrows() || localStorage.length > 0;\n}\n\n// Config the localStorage backend, using options set in the config.\nfunction _initStorage$2(options) {\n    var self = this;\n    var dbInfo = {};\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = options[i];\n        }\n    }\n\n    dbInfo.keyPrefix = _getKeyPrefix(options, self._defaultConfig);\n\n    if (!_isLocalStorageUsable()) {\n        return Promise$1.reject();\n    }\n\n    self._dbInfo = dbInfo;\n    dbInfo.serializer = localforageSerializer;\n\n    return Promise$1.resolve();\n}\n\n// Remove all keys from the datastore, effectively destroying all data in\n// the app's key/value store!\nfunction clear$2(callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var keyPrefix = self._dbInfo.keyPrefix;\n\n        for (var i = localStorage.length - 1; i >= 0; i--) {\n            var key = localStorage.key(i);\n\n            if (key.indexOf(keyPrefix) === 0) {\n                localStorage.removeItem(key);\n            }\n        }\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Retrieve an item from the store. Unlike the original async_storage\n// library in Gaia, we don't modify return values at all. If a key's value\n// is `undefined`, we pass that value to the callback function.\nfunction getItem$2(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var result = localStorage.getItem(dbInfo.keyPrefix + key);\n\n        // If a result was found, parse it from the serialized\n        // string into a JS object. If result isn't truthy, the key\n        // is likely undefined and we'll pass it straight to the\n        // callback.\n        if (result) {\n            result = dbInfo.serializer.deserialize(result);\n        }\n\n        return result;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Iterate over all items in the store.\nfunction iterate$2(iterator, callback) {\n    var self = this;\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var keyPrefix = dbInfo.keyPrefix;\n        var keyPrefixLength = keyPrefix.length;\n        var length = localStorage.length;\n\n        // We use a dedicated iterator instead of the `i` variable below\n        // so other keys we fetch in localStorage aren't counted in\n        // the `iterationNumber` argument passed to the `iterate()`\n        // callback.\n        //\n        // See: github.com/mozilla/localForage/pull/435#discussion_r38061530\n        var iterationNumber = 1;\n\n        for (var i = 0; i < length; i++) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) !== 0) {\n                continue;\n            }\n            var value = localStorage.getItem(key);\n\n            // If a result was found, parse it from the serialized\n            // string into a JS object. If result isn't truthy, the\n            // key is likely undefined and we'll pass it straight\n            // to the iterator.\n            if (value) {\n                value = dbInfo.serializer.deserialize(value);\n            }\n\n            value = iterator(value, key.substring(keyPrefixLength), iterationNumber++);\n\n            if (value !== void 0) {\n                return value;\n            }\n        }\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Same as localStorage's key() method, except takes a callback.\nfunction key$2(n, callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var result;\n        try {\n            result = localStorage.key(n);\n        } catch (error) {\n            result = null;\n        }\n\n        // Remove the prefix from the key, if a key is found.\n        if (result) {\n            result = result.substring(dbInfo.keyPrefix.length);\n        }\n\n        return result;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys$2(callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var length = localStorage.length;\n        var keys = [];\n\n        for (var i = 0; i < length; i++) {\n            var itemKey = localStorage.key(i);\n            if (itemKey.indexOf(dbInfo.keyPrefix) === 0) {\n                keys.push(itemKey.substring(dbInfo.keyPrefix.length));\n            }\n        }\n\n        return keys;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Supply the number of keys in the datastore to the callback function.\nfunction length$2(callback) {\n    var self = this;\n    var promise = self.keys().then(function (keys) {\n        return keys.length;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Remove an item from the store, nice and simple.\nfunction removeItem$2(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        localStorage.removeItem(dbInfo.keyPrefix + key);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Set a key's value and run an optional callback once the value is set.\n// Unlike Gaia's implementation, the callback function is passed the value,\n// in case you want to operate on that value only after you're sure it\n// saved, or something like that.\nfunction setItem$2(key, value, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        // Convert undefined values to null.\n        // https://github.com/mozilla/localForage/pull/42\n        if (value === undefined) {\n            value = null;\n        }\n\n        // Save the original value to pass to the callback.\n        var originalValue = value;\n\n        return new Promise$1(function (resolve, reject) {\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n                if (error) {\n                    reject(error);\n                } else {\n                    try {\n                        localStorage.setItem(dbInfo.keyPrefix + key, value);\n                        resolve(originalValue);\n                    } catch (e) {\n                        // localStorage capacity exceeded.\n                        // TODO: Make this a specific error/event.\n                        if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {\n                            reject(e);\n                        }\n                        reject(e);\n                    }\n                }\n            });\n        });\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction dropInstance$2(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        var currentConfig = this.config();\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        promise = new Promise$1(function (resolve) {\n            if (!options.storeName) {\n                resolve(options.name + '/');\n            } else {\n                resolve(_getKeyPrefix(options, self._defaultConfig));\n            }\n        }).then(function (keyPrefix) {\n            for (var i = localStorage.length - 1; i >= 0; i--) {\n                var key = localStorage.key(i);\n\n                if (key.indexOf(keyPrefix) === 0) {\n                    localStorage.removeItem(key);\n                }\n            }\n        });\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar localStorageWrapper = {\n    _driver: 'localStorageWrapper',\n    _initStorage: _initStorage$2,\n    _support: isLocalStorageValid(),\n    iterate: iterate$2,\n    getItem: getItem$2,\n    setItem: setItem$2,\n    removeItem: removeItem$2,\n    clear: clear$2,\n    length: length$2,\n    key: key$2,\n    keys: keys$2,\n    dropInstance: dropInstance$2\n};\n\nvar sameValue = function sameValue(x, y) {\n    return x === y || typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y);\n};\n\nvar includes = function includes(array, searchElement) {\n    var len = array.length;\n    var i = 0;\n    while (i < len) {\n        if (sameValue(array[i], searchElement)) {\n            return true;\n        }\n        i++;\n    }\n\n    return false;\n};\n\nvar isArray = Array.isArray || function (arg) {\n    return Object.prototype.toString.call(arg) === '[object Array]';\n};\n\n// Drivers are stored here when `defineDriver()` is called.\n// They are shared across all instances of localForage.\nvar DefinedDrivers = {};\n\nvar DriverSupport = {};\n\nvar DefaultDrivers = {\n    INDEXEDDB: asyncStorage,\n    WEBSQL: webSQLStorage,\n    LOCALSTORAGE: localStorageWrapper\n};\n\nvar DefaultDriverOrder = [DefaultDrivers.INDEXEDDB._driver, DefaultDrivers.WEBSQL._driver, DefaultDrivers.LOCALSTORAGE._driver];\n\nvar OptionalDriverMethods = ['dropInstance'];\n\nvar LibraryMethods = ['clear', 'getItem', 'iterate', 'key', 'keys', 'length', 'removeItem', 'setItem'].concat(OptionalDriverMethods);\n\nvar DefaultConfig = {\n    description: '',\n    driver: DefaultDriverOrder.slice(),\n    name: 'localforage',\n    // Default DB size is _JUST UNDER_ 5MB, as it's the highest size\n    // we can use without a prompt.\n    size: 4980736,\n    storeName: 'keyvaluepairs',\n    version: 1.0\n};\n\nfunction callWhenReady(localForageInstance, libraryMethod) {\n    localForageInstance[libraryMethod] = function () {\n        var _args = arguments;\n        return localForageInstance.ready().then(function () {\n            return localForageInstance[libraryMethod].apply(localForageInstance, _args);\n        });\n    };\n}\n\nfunction extend() {\n    for (var i = 1; i < arguments.length; i++) {\n        var arg = arguments[i];\n\n        if (arg) {\n            for (var _key in arg) {\n                if (arg.hasOwnProperty(_key)) {\n                    if (isArray(arg[_key])) {\n                        arguments[0][_key] = arg[_key].slice();\n                    } else {\n                        arguments[0][_key] = arg[_key];\n                    }\n                }\n            }\n        }\n    }\n\n    return arguments[0];\n}\n\nvar LocalForage = function () {\n    function LocalForage(options) {\n        _classCallCheck(this, LocalForage);\n\n        for (var driverTypeKey in DefaultDrivers) {\n            if (DefaultDrivers.hasOwnProperty(driverTypeKey)) {\n                var driver = DefaultDrivers[driverTypeKey];\n                var driverName = driver._driver;\n                this[driverTypeKey] = driverName;\n\n                if (!DefinedDrivers[driverName]) {\n                    // we don't need to wait for the promise,\n                    // since the default drivers can be defined\n                    // in a blocking manner\n                    this.defineDriver(driver);\n                }\n            }\n        }\n\n        this._defaultConfig = extend({}, DefaultConfig);\n        this._config = extend({}, this._defaultConfig, options);\n        this._driverSet = null;\n        this._initDriver = null;\n        this._ready = false;\n        this._dbInfo = null;\n\n        this._wrapLibraryMethodsWithReady();\n        this.setDriver(this._config.driver)[\"catch\"](function () {});\n    }\n\n    // Set any config values for localForage; can be called anytime before\n    // the first API call (e.g. `getItem`, `setItem`).\n    // We loop through options so we don't overwrite existing config\n    // values.\n\n\n    LocalForage.prototype.config = function config(options) {\n        // If the options argument is an object, we use it to set values.\n        // Otherwise, we return either a specified config value or all\n        // config values.\n        if ((typeof options === 'undefined' ? 'undefined' : _typeof(options)) === 'object') {\n            // If localforage is ready and fully initialized, we can't set\n            // any new configuration values. Instead, we return an error.\n            if (this._ready) {\n                return new Error(\"Can't call config() after localforage \" + 'has been used.');\n            }\n\n            for (var i in options) {\n                if (i === 'storeName') {\n                    options[i] = options[i].replace(/\\W/g, '_');\n                }\n\n                if (i === 'version' && typeof options[i] !== 'number') {\n                    return new Error('Database version must be a number.');\n                }\n\n                this._config[i] = options[i];\n            }\n\n            // after all config options are set and\n            // the driver option is used, try setting it\n            if ('driver' in options && options.driver) {\n                return this.setDriver(this._config.driver);\n            }\n\n            return true;\n        } else if (typeof options === 'string') {\n            return this._config[options];\n        } else {\n            return this._config;\n        }\n    };\n\n    // Used to define a custom driver, shared across all instances of\n    // localForage.\n\n\n    LocalForage.prototype.defineDriver = function defineDriver(driverObject, callback, errorCallback) {\n        var promise = new Promise$1(function (resolve, reject) {\n            try {\n                var driverName = driverObject._driver;\n                var complianceError = new Error('Custom driver not compliant; see ' + 'https://mozilla.github.io/localForage/#definedriver');\n\n                // A driver name should be defined and not overlap with the\n                // library-defined, default drivers.\n                if (!driverObject._driver) {\n                    reject(complianceError);\n                    return;\n                }\n\n                var driverMethods = LibraryMethods.concat('_initStorage');\n                for (var i = 0, len = driverMethods.length; i < len; i++) {\n                    var driverMethodName = driverMethods[i];\n\n                    // when the property is there,\n                    // it should be a method even when optional\n                    var isRequired = !includes(OptionalDriverMethods, driverMethodName);\n                    if ((isRequired || driverObject[driverMethodName]) && typeof driverObject[driverMethodName] !== 'function') {\n                        reject(complianceError);\n                        return;\n                    }\n                }\n\n                var configureMissingMethods = function configureMissingMethods() {\n                    var methodNotImplementedFactory = function methodNotImplementedFactory(methodName) {\n                        return function () {\n                            var error = new Error('Method ' + methodName + ' is not implemented by the current driver');\n                            var promise = Promise$1.reject(error);\n                            executeCallback(promise, arguments[arguments.length - 1]);\n                            return promise;\n                        };\n                    };\n\n                    for (var _i = 0, _len = OptionalDriverMethods.length; _i < _len; _i++) {\n                        var optionalDriverMethod = OptionalDriverMethods[_i];\n                        if (!driverObject[optionalDriverMethod]) {\n                            driverObject[optionalDriverMethod] = methodNotImplementedFactory(optionalDriverMethod);\n                        }\n                    }\n                };\n\n                configureMissingMethods();\n\n                var setDriverSupport = function setDriverSupport(support) {\n                    if (DefinedDrivers[driverName]) {\n                        console.info('Redefining LocalForage driver: ' + driverName);\n                    }\n                    DefinedDrivers[driverName] = driverObject;\n                    DriverSupport[driverName] = support;\n                    // don't use a then, so that we can define\n                    // drivers that have simple _support methods\n                    // in a blocking manner\n                    resolve();\n                };\n\n                if ('_support' in driverObject) {\n                    if (driverObject._support && typeof driverObject._support === 'function') {\n                        driverObject._support().then(setDriverSupport, reject);\n                    } else {\n                        setDriverSupport(!!driverObject._support);\n                    }\n                } else {\n                    setDriverSupport(true);\n                }\n            } catch (e) {\n                reject(e);\n            }\n        });\n\n        executeTwoCallbacks(promise, callback, errorCallback);\n        return promise;\n    };\n\n    LocalForage.prototype.driver = function driver() {\n        return this._driver || null;\n    };\n\n    LocalForage.prototype.getDriver = function getDriver(driverName, callback, errorCallback) {\n        var getDriverPromise = DefinedDrivers[driverName] ? Promise$1.resolve(DefinedDrivers[driverName]) : Promise$1.reject(new Error('Driver not found.'));\n\n        executeTwoCallbacks(getDriverPromise, callback, errorCallback);\n        return getDriverPromise;\n    };\n\n    LocalForage.prototype.getSerializer = function getSerializer(callback) {\n        var serializerPromise = Promise$1.resolve(localforageSerializer);\n        executeTwoCallbacks(serializerPromise, callback);\n        return serializerPromise;\n    };\n\n    LocalForage.prototype.ready = function ready(callback) {\n        var self = this;\n\n        var promise = self._driverSet.then(function () {\n            if (self._ready === null) {\n                self._ready = self._initDriver();\n            }\n\n            return self._ready;\n        });\n\n        executeTwoCallbacks(promise, callback, callback);\n        return promise;\n    };\n\n    LocalForage.prototype.setDriver = function setDriver(drivers, callback, errorCallback) {\n        var self = this;\n\n        if (!isArray(drivers)) {\n            drivers = [drivers];\n        }\n\n        var supportedDrivers = this._getSupportedDrivers(drivers);\n\n        function setDriverToConfig() {\n            self._config.driver = self.driver();\n        }\n\n        function extendSelfWithDriver(driver) {\n            self._extend(driver);\n            setDriverToConfig();\n\n            self._ready = self._initStorage(self._config);\n            return self._ready;\n        }\n\n        function initDriver(supportedDrivers) {\n            return function () {\n                var currentDriverIndex = 0;\n\n                function driverPromiseLoop() {\n                    while (currentDriverIndex < supportedDrivers.length) {\n                        var driverName = supportedDrivers[currentDriverIndex];\n                        currentDriverIndex++;\n\n                        self._dbInfo = null;\n                        self._ready = null;\n\n                        return self.getDriver(driverName).then(extendSelfWithDriver)[\"catch\"](driverPromiseLoop);\n                    }\n\n                    setDriverToConfig();\n                    var error = new Error('No available storage method found.');\n                    self._driverSet = Promise$1.reject(error);\n                    return self._driverSet;\n                }\n\n                return driverPromiseLoop();\n            };\n        }\n\n        // There might be a driver initialization in progress\n        // so wait for it to finish in order to avoid a possible\n        // race condition to set _dbInfo\n        var oldDriverSetDone = this._driverSet !== null ? this._driverSet[\"catch\"](function () {\n            return Promise$1.resolve();\n        }) : Promise$1.resolve();\n\n        this._driverSet = oldDriverSetDone.then(function () {\n            var driverName = supportedDrivers[0];\n            self._dbInfo = null;\n            self._ready = null;\n\n            return self.getDriver(driverName).then(function (driver) {\n                self._driver = driver._driver;\n                setDriverToConfig();\n                self._wrapLibraryMethodsWithReady();\n                self._initDriver = initDriver(supportedDrivers);\n            });\n        })[\"catch\"](function () {\n            setDriverToConfig();\n            var error = new Error('No available storage method found.');\n            self._driverSet = Promise$1.reject(error);\n            return self._driverSet;\n        });\n\n        executeTwoCallbacks(this._driverSet, callback, errorCallback);\n        return this._driverSet;\n    };\n\n    LocalForage.prototype.supports = function supports(driverName) {\n        return !!DriverSupport[driverName];\n    };\n\n    LocalForage.prototype._extend = function _extend(libraryMethodsAndProperties) {\n        extend(this, libraryMethodsAndProperties);\n    };\n\n    LocalForage.prototype._getSupportedDrivers = function _getSupportedDrivers(drivers) {\n        var supportedDrivers = [];\n        for (var i = 0, len = drivers.length; i < len; i++) {\n            var driverName = drivers[i];\n            if (this.supports(driverName)) {\n                supportedDrivers.push(driverName);\n            }\n        }\n        return supportedDrivers;\n    };\n\n    LocalForage.prototype._wrapLibraryMethodsWithReady = function _wrapLibraryMethodsWithReady() {\n        // Add a stub for each driver API method that delays the call to the\n        // corresponding driver method until localForage is ready. These stubs\n        // will be replaced by the driver methods as soon as the driver is\n        // loaded, so there is no performance impact.\n        for (var i = 0, len = LibraryMethods.length; i < len; i++) {\n            callWhenReady(this, LibraryMethods[i]);\n        }\n    };\n\n    LocalForage.prototype.createInstance = function createInstance(options) {\n        return new LocalForage(options);\n    };\n\n    return LocalForage;\n}();\n\n// The actual localForage object that we expose as a module or via a\n// global. It's extended by pulling in one of our other libraries.\n\n\nvar localforage_js = new LocalForage();\n\nmodule.exports = localforage_js;\n\n},{\"3\":3}]},{},[4])(4)\n});\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-visuallisation.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-visuallisation.vue?vue&type=style&index=0&lang=scss&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"cape-bg\"},[_c('q-slider',{attrs:{\"min\":1000,\"max\":3000},on:{\"input\":_vm.updateWidth},model:{value:(_vm.width),callback:function ($$v) {_vm.width=$$v},expression:\"width\"}}),_vm._l((_vm.meshes),function(mesh){return _c('div',{staticClass:\"inline\"},[_c('t-exploded-view',{attrs:{\"mesh-id\":mesh.id,\"mesh-width\":_vm.widthThrottled}})],1)})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"mesh-widget\"},[_c('keep-alive',[_c('canvas',{ref:\"root\"})])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n    div.mesh-widget\n        keep-alive\n            canvas(ref=\"root\")\n</template>\n<script>\nimport { cape } from '@core/cape'\n\nimport { multiSceneRenderer } from 'renderers/wireframe-multiscene/multi.js'\nimport { getGeometry, getGeometryForPostPayload } from 'dna/dnaToolsWD.js'\nimport { tylkoCapeComponents } from '@cape-ui'\n\nimport localForage from 'localforage';\n\nexport default {\n    props: {\n        meshId: [Number],\n        meshWidth: [Number],\n    },\n\n    mounted() {\n        this.proxyRenderer = multiSceneRenderer.createProxy({\n            container: this.$refs.root,\n            width: 600,\n            height: 400,\n        })\n\n        this.fetchSerialization()\n    },\n\n    watch: {\n        meshWidth() {\n            this.draw()\n        },\n    },\n\n    methods: {\n        async fetchSerialization() {\n            let cache = await localForage.getItem(`mesh${this.meshId}`);\n            if(cache) {\n                this.processGeometry(cache, true);\n            } else {\n                 cape.api.geo\n                    .componentSet({\n                        type: 'mesh',\n                        id: this.meshId,\n                    })\n                    .forceFetch(true)\n                    .pipe(\n                        this.processGeometry,\n                        'preview'\n                    )\n            }\n        },\n\n        processGeometry(json, cache) {\n            this.serialization = json\n            this.draw();\n\n            if(!cache) {\n                localForage.setItem(`mesh${this.meshId}`, json);\n            }\n        },\n\n        async draw() {\n            let settings = {\n                width: this.meshWidth,\n                height: 600,\n                depth: 320,\n                mesh_setup: null,\n                geom_id: this.meshId,\n                geom_type: 'mesh',\n                distortion: null,\n                density: null,\n                configurator_custom_params: null,\n            }\n\n            let wireframeGeo = await cape.decoder.api.getGeometry({\n                serialization: this.serialization,\n                state: settings,\n                format: 'wireframe'\n            });\n            //this.proxyRenderer.flush(this.meshId);\n          //  for(var n=0, t=10; n<=t; n++) {\n            this.proxyRenderer.updateGeometry(this.meshId, wireframeGeo);\n          //  }\n        },\n    },\n\n    data() {\n        return {}\n    },\n}\n</script>\n<style lang=\"scss\">\n.mesh-widget {\n    width: 600px;\n    height: 400px;\n}\n</style>", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-exploded-view-mesh.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-exploded-view-mesh.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./cape-exploded-view-mesh.vue?vue&type=template&id=3396ca48&lang=pug&\"\nimport script from \"./cape-exploded-view-mesh.vue?vue&type=script&lang=js&\"\nexport * from \"./cape-exploded-view-mesh.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cape-exploded-view-mesh.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template lang=\"pug\">\n    section.cape-bg\n        q-slider(:min=\"1000\",:max=\"3000\", v-model=\"width\", @input=\"updateWidth\")\n        div.inline(v-for=\"mesh in meshes\") \n            t-exploded-view(:mesh-id=\"mesh.id\", :mesh-width=\"widthThrottled\")\n</template>\n\n<script>\nimport _ from 'lodash';\nimport { cape } from '@core/cape'\nimport { getGeometry, getGeometryForPostPayload } from 'dna/dnaToolsWD.js'\nimport { tylkoCapeComponents } from '@cape-ui'\n\nimport explodedView from './cape-exploded-view-mesh'\n\nexport default {\n    components: {\n        't-exploded-view': explodedView,\n    },\n\n    mounted() {\n        this.getMeshesForCollection().then(data => {\n            this.meshes = data.mesh.slice(0, 9);\n        })\n        this.updateWidth = _.throttle(this._updateWidth, 60);\n    },\n\n    methods: {\n        _updateWidth(newWidth) {\n            console.log(newWidth);\n            this.widthThrottled = newWidth;\n        },\n        async getMeshesForCollection() {\n            return await cape.api.palette.collection(66).meshes.fetch()\n        },\n    },\n\n    data() {\n        return {\n            widthThrottled: 1500,\n            meshes: [],\n            width: 1500,\n        }\n    },\n}\n</script>\n<style lang=\"scss\">\n.inline {\n    display: inline-block;\n}\n.cape-bg {\n    position: fixed;\n    height: 102vh;\n    overflow: scroll;\n    width: 100%;\n    min-height: 100vh;\n    overflow: scroll;\n\n    background-color: rgb(19, 19, 19);\n    $color: rgba(220, 220, 220, 0.04);\n    $size: 40px;\n\n    background-image: -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px);\n    background-size: $size * 4 $size * 4, $size * 4 $size * 4, $size $size,\n        $size $size, $size/2 $size/2, $size/2 $size/2;\n    background-position-x: -6px;\n}\n</style>", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-visuallisation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-visuallisation.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./collection-visuallisation.vue?vue&type=template&id=735ac1d8&lang=pug&\"\nimport script from \"./collection-visuallisation.vue?vue&type=script&lang=js&\"\nexport * from \"./collection-visuallisation.vue?vue&type=script&lang=js&\"\nimport style0 from \"./collection-visuallisation.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-exploded-view-mesh.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-exploded-view-mesh.vue?vue&type=style&index=0&lang=scss&\"", "/**\n * <AUTHOR> / http://egraether.com/\n * <AUTHOR> \t/ http://mark-lundin.com\n * <AUTHOR> / http://daron1337.github.io\n * <AUTHOR> \t/ http://lantiga.github.io\n\n ** three-trackballcontrols module\n ** <AUTHOR> / http://jonlim.ca\n */\n\nvar THREE = window.THREE || require('three');\n\nvar TrackballControls = function (object, domElement, lock = false) {\n\n\tvar _this = this;\n\tvar STATE = { NONE: - 1, ROTATE: 0, ZOOM: 1, PAN: 2, TOUCH_ROTATE: 3, TOUCH_ZOOM_PAN: 4 };\n\n\tthis.object = object;\n\tthis.domElement = (domElement !== undefined) ? domElement : document;\n\n\t// API\n\tthis.locked = false;\n\tthis.enabled = true;\n\n\tthis.screen = { left: 0, top: 0, width: 0, height: 0 };\n\n\tthis.rotateSpeed = 1.0;\n\tthis.zoomSpeed = 1.2;\n\tthis.panSpeed = 0.3;\n\n\tthis.noRotate = false;\n\tthis.noZoom = false;\n\tthis.noPan = false;\n\n\tthis.staticMoving = false;\n\tthis.dynamicDampingFactor = 0.2;\n\n\tthis.minDistance = 0;\n\tthis.maxDistance = Infinity;\n\n\t/**\n\t * `KeyboardEvent.keyCode` values which should trigger the different \n\t * interaction states. Each element can be a single code or an array\n\t * of codes. All elements are required.\n\t */\n\tthis.keys = [65 /*A*/, 83 /*S*/, 68 /*D*/];\n\n\t// internals\n\n\tthis.target = new THREE.Vector3();\n\n\tvar EPS = 0.000001;\n\n\tvar lastPosition = new THREE.Vector3();\n\n\tvar _state = STATE.NONE,\n\t\t_prevState = STATE.NONE,\n\n\t\t_eye = new THREE.Vector3(),\n\n\t\t_movePrev = new THREE.Vector2(),\n\t\t_moveCurr = new THREE.Vector2(),\n\n\t\t_lastAxis = new THREE.Vector3(),\n\t\t_lastAngle = 0,\n\n\t\t_zoomStart = new THREE.Vector2(),\n\t\t_zoomEnd = new THREE.Vector2(),\n\n\t\t_touchZoomDistanceStart = 0,\n\t\t_touchZoomDistanceEnd = 0,\n\n\t\t_panStart = new THREE.Vector2(),\n\t\t_panEnd = new THREE.Vector2();\n\n\t// for reset\n\n\tthis.target0 = this.target.clone();\n\tthis.position0 = this.object.position.clone();\n\tthis.up0 = this.object.up.clone();\n\n\t// events\n\n\tvar changeEvent = { type: 'change' };\n\tvar startEvent = { type: 'start' };\n\tvar endEvent = { type: 'end' };\n\n\n\tconsole.log(\"CAMERA\");\n\t// methods\n\t// methods\n\n\tthis.handleResize = function () {\n\n\t\tif (this.domElement === document) {\n\n\t\t\tthis.screen.left = 0;\n\t\t\tthis.screen.top = 0;\n\t\t\tthis.screen.width = window.innerWidth;\n\t\t\tthis.screen.height = window.innerHeight;\n\n\t\t} else {\n\n\t\t\tvar box = this.domElement.getBoundingClientRect();\n\t\t\t// adjustments come from similar code in the jquery offset() function\n\t\t\tvar d = this.domElement.ownerDocument.documentElement;\n\t\t\tthis.screen.left = box.left + window.pageXOffset - d.clientLeft;\n\t\t\tthis.screen.top = box.top + window.pageYOffset - d.clientTop;\n\t\t\tthis.screen.width = box.width;\n\t\t\tthis.screen.height = box.height;\n\n\t\t}\n\n\t};\n\n\tthis.handleEvent = function (event) {\n\n\t\tif (typeof this[event.type] == 'function') {\n\n\t\t\tthis[event.type](event);\n\n\t\t}\n\n\t};\n\n\tvar getMouseOnScreen = (function () {\n\n\t\tvar vector = new THREE.Vector2();\n\n\t\treturn function getMouseOnScreen(pageX, pageY) {\n\n\t\t\tvector.set(\n\t\t\t\t(pageX - _this.screen.left) / _this.screen.width,\n\t\t\t\t(pageY - _this.screen.top) / _this.screen.height\n\t\t\t);\n\n\t\t\treturn vector;\n\n\t\t};\n\n\t}());\n\n\tvar getMouseOnCircle = (function () {\n\n\t\tvar vector = new THREE.Vector2();\n\n\t\treturn function getMouseOnCircle(pageX, pageY) {\n\n\t\t\tvector.set(\n\t\t\t\t((pageX - _this.screen.width * 0.5 - _this.screen.left) / (_this.screen.width * 0.5)),\n\t\t\t\t((_this.screen.height + 2 * (_this.screen.top - pageY)) / _this.screen.width) // screen.width intentional\n\t\t\t);\n\n\t\t\treturn vector;\n\n\t\t};\n\n\t}());\n\n\tthis.rotateCamera = (function () {\n\n\t\tvar axis = new THREE.Vector3(),\n\t\t\tquaternion = new THREE.Quaternion(),\n\t\t\teyeDirection = new THREE.Vector3(),\n\t\t\tobjectUpDirection = new THREE.Vector3(),\n\t\t\tobjectSidewaysDirection = new THREE.Vector3(),\n\t\t\tmoveDirection = new THREE.Vector3(),\n\t\t\tangle;\n\n\t\tvar deltaAxis = 0;\n\n\t\tfunction rotateCamera() {\n\n\t\t\tlet moveDirectionCopy = moveDirection.clone();\n\t\t\tmoveDirection.set(_moveCurr.x - _movePrev.x, _moveCurr.y - _movePrev.y, 0);\n\t\t\tangle = moveDirection.length();\n\n\t\t\tlet maxA = 30;\n\t\t\tlet breakIt = false;\n\t\t\tdeltaAxis += angle * (axis.y > 0 ? 1 : -1);\n\t\t\tlet angleInDeg = deltaAxis * (180 / Math.PI);\n\t\t\tangleInDeg = Math.max(-maxA, Math.min(angleInDeg, maxA));\n\n\t\t\t/*\n\t\t\tif(Math.abs(angleInDeg) == maxA) {\n\n\t\t\t\tmoveDirection = moveDirectionCopy;\n\t\t\t\tconsole.log(12345,_moveCurr, _movePrev);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t*/\n\n\n\t\t\tif (angle) {\n\n\t\t\t\t_eye.copy(_this.object.position).sub(_this.target);\n\n\t\t\t\teyeDirection.copy(_eye).normalize();\n\t\t\t\tobjectUpDirection.copy(_this.object.up).normalize();\n\t\t\t\tobjectSidewaysDirection.crossVectors(objectUpDirection, eyeDirection).normalize();\n\n\t\t\t\tobjectUpDirection.setLength(_moveCurr.y - _movePrev.y);\n\t\t\t\tobjectSidewaysDirection.setLength(_moveCurr.x - _movePrev.x);\n\n\t\t\t\tif (this.locked) {\n\t\t\t\t\tmoveDirection.copy(objectSidewaysDirection);\n\t\t\t\t} else {\n\t\t\t\t\tmoveDirection.copy(objectUpDirection.add(objectSidewaysDirection));\n\t\t\t\t}\n\n\t\t\t\taxis.crossVectors(moveDirection, _eye).normalize();\n\n\t\t\t\tquaternion.setFromAxisAngle(axis, angle);\n\n\t\t\t\t_eye.applyQuaternion(quaternion);\n\t\t\t\tif (!this.locked) _this.object.up.applyQuaternion(quaternion);\n\n\t\t\t\t_lastAxis.copy(axis);\n\t\t\t\t_lastAngle = angle;\n\n\t\t\t} else if (!_this.staticMoving && _lastAngle) {\n\n\t\t\t\t_lastAngle *= Math.sqrt(1.0 - _this.dynamicDampingFactor);\n\t\t\t\t_eye.copy(_this.object.position).sub(_this.target);\n\t\t\t\tquaternion.setFromAxisAngle(_lastAxis, _lastAngle);\n\t\t\t\t_eye.applyQuaternion(quaternion);\n\t\t\t\t_this.object.up.applyQuaternion(quaternion);\n\n\t\t\t}\n\n\t\t\t_movePrev.copy(_moveCurr);\n\n\t\t}\n\t\treturn rotateCamera;\n\n\n\n\t}());\n\n\n\tthis.zoomCamera = function () {\n\n\t\tvar factor;\n\n\t\tif (_state === STATE.TOUCH_ZOOM_PAN) {\n\n\t\t\tfactor = _touchZoomDistanceStart / _touchZoomDistanceEnd;\n\t\t\t_touchZoomDistanceStart = _touchZoomDistanceEnd;\n\t\t\t_eye.multiplyScalar(factor);\n\n\t\t} else {\n\n\t\t\tfactor = 1.0 + (_zoomEnd.y - _zoomStart.y) * _this.zoomSpeed;\n\n\t\t\tif (factor !== 1.0 && factor > 0.0) {\n\n\t\t\t\t_eye.multiplyScalar(factor);\n\n\t\t\t}\n\n\t\t\tif (_this.staticMoving) {\n\n\t\t\t\t_zoomStart.copy(_zoomEnd);\n\n\t\t\t} else {\n\n\t\t\t\t_zoomStart.y += (_zoomEnd.y - _zoomStart.y) * this.dynamicDampingFactor;\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n\tthis.panCamera = (function () {\n\n\t\tvar mouseChange = new THREE.Vector2(),\n\t\t\tobjectUp = new THREE.Vector3(),\n\t\t\tpan = new THREE.Vector3();\n\n\t\treturn function panCamera() {\n\n\t\t\tmouseChange.copy(_panEnd).sub(_panStart);\n\n\t\t\tif (mouseChange.lengthSq()) {\n\n\t\t\t\tmouseChange.multiplyScalar(_eye.length() * _this.panSpeed);\n\n\t\t\t\tpan.copy(_eye).cross(_this.object.up).setLength(mouseChange.x);\n\t\t\t\tpan.add(objectUp.copy(_this.object.up).setLength(mouseChange.y));\n\n\t\t\t\t_this.object.position.add(pan);\n\t\t\t\t_this.target.add(pan);\n\n\t\t\t\tif (_this.staticMoving) {\n\n\t\t\t\t\t_panStart.copy(_panEnd);\n\n\t\t\t\t} else {\n\n\t\t\t\t\t_panStart.add(mouseChange.subVectors(_panEnd, _panStart).multiplyScalar(_this.dynamicDampingFactor));\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t};\n\n\t}());\n\n\tthis.checkDistances = function () {\n\n\t\tif (!_this.noZoom || !_this.noPan) {\n\n\t\t\tif (_eye.lengthSq() > _this.maxDistance * _this.maxDistance) {\n\n\t\t\t\t_this.object.position.addVectors(_this.target, _eye.setLength(_this.maxDistance));\n\t\t\t\t_zoomStart.copy(_zoomEnd);\n\n\t\t\t}\n\n\t\t\tif (_eye.lengthSq() < _this.minDistance * _this.minDistance) {\n\n\t\t\t\t_this.object.position.addVectors(_this.target, _eye.setLength(_this.minDistance));\n\t\t\t\t_zoomStart.copy(_zoomEnd);\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n\tthis.update = function () {\n\n\n\n\t\t_eye.subVectors(_this.object.position, _this.target);\n\n\t\tif (!_this.noRotate) {\n\n\t\t\t_this.rotateCamera();\n\n\t\t}\n\n\t\tif (!_this.noZoom) {\n\n\t\t\t_this.zoomCamera();\n\n\t\t}\n\n\t\tif (!_this.noPan) {\n\n\t\t\t_this.panCamera();\n\n\t\t}\n\n\t\t_this.object.position.addVectors(_this.target, _eye);\n\n\t\t_this.checkDistances();\n\n\t\t_this.object.lookAt(_this.target);\n\n\t\tif (lastPosition.distanceToSquared(_this.object.position) > EPS) {\n\n\t\t\t_this.dispatchEvent(changeEvent);\n\n\t\t\tlastPosition.copy(_this.object.position);\n\n\t\t}\n\n\t};\n\n\tthis.reset = function () {\n\n\t\t_state = STATE.NONE;\n\t\t_prevState = STATE.NONE;\n\n\t\t_this.target.copy(_this.target0);\n\t\t_this.object.position.copy(_this.position0);\n\t\t_this.object.up.copy(_this.up0);\n\n\t\t_eye.subVectors(_this.object.position, _this.target);\n\n\t\t_this.object.lookAt(_this.target);\n\n\t\t_this.dispatchEvent(changeEvent);\n\n\t\tlastPosition.copy(_this.object.position);\n\n\t};\n\n\t// helpers\n\n\t/**\n\t * Checks if the pressed key is any of the configured modifier keys for\n\t * a specified behavior.\n\t * \n\t * @param {number | number[]} keys \n\t * @param {number} key \n\t * \n\t * @returns {boolean} `true` if `keys` contains or equals `key`\n\t */\n\tfunction containsKey(keys, key) {\n\t\tif (Array.isArray(keys)) {\n\t\t\treturn keys.indexOf(key) !== -1;\n\t\t} else {\n\t\t\treturn keys === key;\n\t\t}\n\t}\n\n\t// listeners\n\n\tfunction keydown(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\twindow.removeEventListener('keydown', keydown);\n\n\t\t_prevState = _state;\n\n\t\tif (_state !== STATE.NONE) {\n\n\n\n\t\t} else if (containsKey(_this.keys[STATE.ROTATE], event.keyCode) && !_this.noRotate) {\n\n\t\t\t_state = STATE.ROTATE;\n\n\t\t} else if (containsKey(_this.keys[STATE.ZOOM], event.keyCode) && !_this.noZoom) {\n\n\t\t\t_state = STATE.ZOOM;\n\n\t\t} else if (containsKey(_this.keys[STATE.PAN], event.keyCode) && !_this.noPan) {\n\n\t\t\t_state = STATE.PAN;\n\n\t\t}\n\n\t}\n\n\tfunction keyup(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\t_state = _prevState;\n\n\t\twindow.addEventListener('keydown', keydown, false);\n\n\t}\n\n\tfunction mousedown(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tif (_state === STATE.NONE) {\n\n\t\t\t_state = event.button;\n\n\t\t}\n\n\t\tif (_state === STATE.ROTATE && !_this.noRotate) {\n\n\t\t\t_moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));\n\t\t\t_movePrev.copy(_moveCurr);\n\n\t\t} else if (_state === STATE.ZOOM && !_this.noZoom) {\n\n\t\t\t_zoomStart.copy(getMouseOnScreen(event.pageX, event.pageY));\n\t\t\t_zoomEnd.copy(_zoomStart);\n\n\t\t} else if (_state === STATE.PAN && !_this.noPan) {\n\n\t\t\t_panStart.copy(getMouseOnScreen(event.pageX, event.pageY));\n\t\t\t_panEnd.copy(_panStart);\n\n\t\t}\n\n\t\tdocument.addEventListener('mousemove', mousemove, false);\n\t\tdocument.addEventListener('mouseup', mouseup, false);\n\n\t\t_this.dispatchEvent(startEvent);\n\n\t}\n\n\tfunction mousemove(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tif (_state === STATE.ROTATE && !_this.noRotate) {\n\n\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t_moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));\n\n\t\t} else if (_state === STATE.ZOOM && !_this.noZoom) {\n\n\t\t\t_zoomEnd.copy(getMouseOnScreen(event.pageX, event.pageY));\n\n\t\t} else if (_state === STATE.PAN && !_this.noPan) {\n\n\t\t\t_panEnd.copy(getMouseOnScreen(event.pageX, event.pageY));\n\n\t\t}\n\n\t}\n\n\tfunction mouseup(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\t_state = STATE.NONE;\n\n\t\tdocument.removeEventListener('mousemove', mousemove);\n\t\tdocument.removeEventListener('mouseup', mouseup);\n\t\t_this.dispatchEvent(endEvent);\n\n\t}\n\n\tfunction mousewheel(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tswitch (event.deltaMode) {\n\n\t\t\tcase 2:\n\t\t\t\t// Zoom in pages\n\t\t\t\t_zoomStart.y -= event.deltaY * 0.025;\n\t\t\t\tbreak;\n\n\t\t\tcase 1:\n\t\t\t\t// Zoom in lines\n\t\t\t\t_zoomStart.y -= event.deltaY * 0.01;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\t// undefined, 0, assume pixels\n\t\t\t\t_zoomStart.y -= event.deltaY * 0.00025;\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t_this.dispatchEvent(startEvent);\n\t\t_this.dispatchEvent(endEvent);\n\n\t}\n\n\tfunction touchstart(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase 1:\n\t\t\t\t_state = STATE.TOUCH_ROTATE;\n\t\t\t\t_moveCurr.copy(getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n\t\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t\tbreak;\n\n\t\t\tdefault: // 2 or more\n\t\t\t\t_state = STATE.TOUCH_ZOOM_PAN;\n\t\t\t\tvar dx = event.touches[0].pageX - event.touches[1].pageX;\n\t\t\t\tvar dy = event.touches[0].pageY - event.touches[1].pageY;\n\t\t\t\t_touchZoomDistanceEnd = _touchZoomDistanceStart = Math.sqrt(dx * dx + dy * dy);\n\n\t\t\t\tvar x = (event.touches[0].pageX + event.touches[1].pageX) / 2;\n\t\t\t\tvar y = (event.touches[0].pageY + event.touches[1].pageY) / 2;\n\t\t\t\t_panStart.copy(getMouseOnScreen(x, y));\n\t\t\t\t_panEnd.copy(_panStart);\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t_this.dispatchEvent(startEvent);\n\n\t}\n\n\tfunction touchmove(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase 1:\n\t\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t\t_moveCurr.copy(getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n\t\t\t\tbreak;\n\n\t\t\tdefault: // 2 or more\n\t\t\t\tvar dx = event.touches[0].pageX - event.touches[1].pageX;\n\t\t\t\tvar dy = event.touches[0].pageY - event.touches[1].pageY;\n\t\t\t\t_touchZoomDistanceEnd = Math.sqrt(dx * dx + dy * dy);\n\n\t\t\t\tvar x = (event.touches[0].pageX + event.touches[1].pageX) / 2;\n\t\t\t\tvar y = (event.touches[0].pageY + event.touches[1].pageY) / 2;\n\t\t\t\t_panEnd.copy(getMouseOnScreen(x, y));\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\tconsole.log(\"lol\");\n\n\t}\n\n\tfunction touchend(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase 0:\n\t\t\t\t_state = STATE.NONE;\n\t\t\t\tbreak;\n\n\t\t\tcase 1:\n\t\t\t\t_state = STATE.TOUCH_ROTATE;\n\t\t\t\t_moveCurr.copy(getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n\t\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t_this.dispatchEvent(endEvent);\n\n\t}\n\n\tfunction contextmenu(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\n\t}\n\n\tthis.dispose = function () {\n\t\t/*\n\t\t\t\tthis.domElement.removeEventListener( 'contextmenu', contextmenu, false );\n\t\t\t\tthis.domElement.removeEventListener( 'mousedown', mousedown, false );\n\t\t\t\tthis.domElement.removeEventListener( 'wheel', mousewheel, false );\n\t\t\n\t\t\t\tthis.domElement.removeEventListener( 'touchstart', touchstart, false );\n\t\t\t\tthis.domElement.removeEventListener( 'touchend', touchend, false );\n\t\t\t\tthis.domElement.removeEventListener( 'touchmove', touchmove, false );\n\t\t\n\t\t\t\tdocument.removeEventListener( 'mousemove', mousemove, false );\n\t\t\t\tdocument.removeEventListener( 'mouseup', mouseup, false );\n\t\t\n\t\t\t\twindow.removeEventListener( 'keydown', keydown, false );\n\t\t\t\twindow.removeEventListener( 'keyup', keyup, false );\n\t\t*/\n\t};\n\n\tthis.domElement.addEventListener('contextmenu', contextmenu, false);\n\tthis.domElement.addEventListener('mousedown', mousedown, false);\n\tthis.domElement.addEventListener('wheel', mousewheel, false);\n\n\tthis.domElement.addEventListener('touchstart', touchstart, false);\n\tthis.domElement.addEventListener('touchend', touchend, false);\n\tthis.domElement.addEventListener('touchmove', touchmove, false);\n\n\twindow.addEventListener('keydown', keydown, false);\n\twindow.addEventListener('keyup', keyup, false);\n\n\tthis.handleResize();\n\n\t// force an update at start\n\tthis.update();\n\n\n\n};\n\n//function preventEvent( event ) { event.preventDefault(); }\n\nTrackballControls.prototype = Object.create(THREE.EventDispatcher.prototype);\n\n\nexport { TrackballControls };", "import * as THREE from 'three';\nimport _ from 'lodash';\n\nimport { WebdesignerRenderer } from 'configurator/ivy/webdesigner.js'\nimport { TrackballControls } from './camera.js';\n\nimport { tylkoCamera } from '@tylko/cape-entrypoints/experimental/cape-camera/tylko-camera.js'\n\nwindow.THREE = THREE;\n\nvar HD_RENDERER = null;\n\nlet renderer= WebdesignerRenderer();\nrenderer.then(renderer => {\n    HD_RENDERER = renderer;\n});\n\n\nconst conf = {\n    verticals: true,\n    supports: true,\n    backs: true,\n    doors: true,\n    drawers: true,\n    fills: true,\n    legs: true,\n    accessories: true,\n    spacer: true,\n    colorMode: 0,\n    filterMaterialKey: '',\n    filterMaterial: '',\n    filterEnable: false,\n    horizontals: true,\n};\n\nconst mat = new THREE.LineBasicMaterial({\n    color: 0xaaaaaa,\n    linewidth: 1\n});\n\nconst material = new THREE.MeshBasicMaterial({\n    color: 0x999999, wireframe: false, transparent: true, polygonOffset: true,\n    polygonOffsetFactor: 1, polygonOffsetUnits: 1, opacity: 0.5\n});\n\nvar PROXY_ID = 0;\n\nclass MultiSceneRenderer {\n    constructor() {\n        this.scenes = [];\n        this.proxies = [];\n        this.init(100, 100);\n    }\n\n    resize({ width, height }) {\n        this.renderer.setSize(width, height);\n        this.renderer.domElement.style.width = `${width}px`;\n        this.renderer.domElement.style.height = `${height}px`;\n    }\n\n    createCamera(canvas) {\n        let camera = new THREE.PerspectiveCamera(20, 1, 1, 20000);\n\n        camera.position.z = 7000;\n        camera.position.x = 400;\n        camera.position.y = 800;\n\n        let controls = new TrackballControls(camera, canvas);\n\n\n        controls.rotateSpeed = 1.0;\n        controls.zoomSpeed = 1.2;\n        controls.panSpeed = 0.8;\n        controls.noZoom = false;\n        controls.noPan = false;\n        controls.staticMoving = true;\n        controls.dynamicDampingFactor = 0.3;\n        controls.target = new THREE.Vector3(200, 250, 0);\n       // animate();\n\n        return { camera, controls };\n    }\n\n    createProxy({ container, width, height, cameraMode, type = \"wireframe\", cameraInstance }) {\n\n        container.width = width;\n        container.height = height;\n\n        let proxyNo = PROXY_ID++;\n\n        if (!this.scenes[proxyNo]) this.scenes[proxyNo] = new THREE.Scene();\n        let camera = null;\n\n        let CapeCamera = null;\n\n        switch(type) {\n            case \"gallery\":\n                CapeCamera = new tylkoCamera(container, this.scenes[proxyNo]);\n                camera = { camera: CapeCamera.camera, controls: CapeCamera.controls }; \n                CapeCamera.updateAspect(width/height);\n                camera.controls.noTransitionAnimation = true;\n\n                let shouldRender = false;\n                let renderLoop = () => {\n                    if(shouldRender) {\n                        shouldRender = false;\n                        this.renderCamera(proxyNo);\n                        camera.controls.update()\n                    }\n                    window.requestAnimationFrame(renderLoop)\n                };\n\n                renderLoop();\n\n                camera.controls.addEventListener('render', (delay) => {\n                    // setTimeout(() => { shouldRender = true; }, dealy);\n                    shouldRender = true;\n                });\n\n                camera.controls.addEventListener('change', () => {\n                    this.renderCamera(proxyNo);\n                });\n\n            break;\n            case \"wireframe\":\n                camera = this.createCamera(container);\n                camera.camera.aspect = width / height;\n                camera.camera.updateProjectionMatrix();\n            break;\n            default: \n                camera = { camera: null, controls: null };\n            break;\n        }\n\n        let proxy = this.proxies.push({\n            proxy: proxyNo,\n            canvas: container,\n            width,\n            height,\n            type,\n            cameraMode,\n            tylkoCamera: CapeCamera,\n            camera: camera.camera,\n            controls: camera.controls,\n            createCameraListener: (callback) => {\n                camera.controls.addEventListener('change', () => {\n                    callback();\n                });\n            },\n            getPrice: (json) => {\n                return HD_RENDERER.getPrice(json);\n            },\n            render: (scene) => {\n                this.renderCamera(proxyNo, scene, true);\n            },\n            setColor: (geometryId, json, color, type) => {\n                HD_RENDERER.setColor(color, type);\n             //   this.renderScene(proxyNo, `${type}-${geometryId}`, json, `${type}:${color}`);\n            },\n            updateGeometry: (geometryId, json, color=\"0:0\", fixed) => {\n                this.renderScene(proxyNo, `${type}-${geometryId}`, json, color, fixed);\n            },\n\n            setComponentScene: (geometryId, comp, thumbs) => {\n                this.renderComponentScene(proxyNo, `${type}-${geometryId}`, comp, thumbs);\n            },\n         \n            getScene: () => {\n                return this.scenes[proxyNo];\n            },\n            flush: (geometryId) => {\n                this.flush(geometryId);\n            },\n            reisze: (size) => {\n\n            }\n        });\n\n        let proxyInstance = this.proxies[proxy - 1];\n        proxyInstance.proxyId = proxy;\n\n\n        return this.proxies[proxy - 1];\n    }\n\n    getProxyByNo(no) {\n        return _.find(this.proxies, { proxy: no });\n    }\n\n    renderComponentScene(proxyNo, id, comp, thumbs) {\n        let proxy = this.getProxyByNo(proxyNo);\n        proxy.tylkoCamera.setComponentViewFinal(\n            thumbs[0].boundingBox.pMin,\n            thumbs[0].boundingBox.pMax,\n            comp.x1 + (comp.x2 - comp.x1) / 2)\n        this.render(proxyNo, id);\n    }\n\n    renderScene(proxyNo, id, json, color, fixed) {\n        if (json == null) return this.flush(id);\n        let proxy = this.getProxyByNo(proxyNo);\n\n\n        switch(proxy.type) {\n            case \"gallery\":\n                if (!this.scenes[id]) this.scenes[id] = new THREE.Scene();\n                HD_RENDERER.displayShelf(json, color, this.scenes[id], proxy.camera, this.renderer);\n                if (['shelf', 'pip'].indexOf(proxy.cameraMode) > -1) {\n                    let bbcam = json.boundingBoxForCamera;\n                    let [geometryMin, geometryMax] = [\n                        {x: bbcam.pMin[0], y: bbcam.pMin[1], z: bbcam.pMin[2]},\n                        {x: bbcam.pMax[0], y: bbcam.pMax[1], z: bbcam.pMax[2]},\n                    ]\n\n                    if (!proxy.initedCam) {\n                        switch (proxy.cameraMode) {\n                            case 'shelf':\n                                proxy.tylkoCamera.setShelfViewFinal(geometryMin, geometryMax)\n                                proxy.initedCam = true;\n                                break\n                            case 'pip':\n                                proxy.tylkoCamera.setPipViewFinal(geometryMin, geometryMax)\n                                proxy.initedCam = true;\n                                break\n                    }\n\n                } else {\n                    fixed = fixed === undefined ? true : fixed\n                    switch (proxy.cameraMode) {\n                        case 'shelf':\n                            proxy.tylkoCamera.controls.geometryFixed = fixed;\n                            break\n                        case 'component':\n                            break\n                        case 'pip':\n                            break\n                    }\n                    proxy.tylkoCamera.updateGeometry( geometryMin, geometryMax )\n                }\n            }\n            break;\n\n            case \"virtual\":\n                if (!this.scenes[id]) this.scenes[id] = new THREE.Scene();\n            break;\n            default:\n                let filtered_elements = this.filterElements(json.item.elements, conf);\n                this.drawElements(id, filtered_elements, position);\n            break;\n        }\n\n        this.render(proxyNo, id);\n    }\n\n    flush(id) {\n        this.resetItems(id);\n    }\n\n    init(width, height) {\n        let camera;\n        let canvasAbsoluteWidth, canvasAbsoluteHeight;\n\n        this.canvasAbsoluteHeight = canvasAbsoluteHeight = height;\n        this.canvasAbsoluteWidth = canvasAbsoluteWidth = width;\n\n        var renderer = new THREE.WebGLRenderer({ antialias: true, preserveDrawingBuffer: false, alpha: true });\n        this.renderer = renderer;\n\n        //renderer.setPixelRatio(window.devicePixelRatio);\n        renderer.setSize(canvasAbsoluteWidth, canvasAbsoluteHeight);\n\n        /*\n        const animate = () => {\n            this.controls.update();\n            this.render();\n            requestAnimationFrame(animate);\n        };\n        animate();\n        */\n    }\n\n    filterElements(elements, filter_conf) {\n        return elements.filter(x => {\n            return (\n                (x['elem_type'] === 'H' && filter_conf['horizontals']) ||\n                (x['elem_type'] === 'V' && filter_conf['verticals']) ||\n                (x['elem_type'] === 'S' && filter_conf['supports']) ||\n                (x['elem_type'] === 'B' && filter_conf['backs']) ||\n                (x['elem_type'] === 'D' && filter_conf['doors']) ||\n                (x['elem_type'] === 'O') ||\n                (x['elem_type'] === 'M') ||\n                (x['elem_type'] === 'L' && filter_conf['legs']) ||\n                (x['elem_type'] === 'T' && filter_conf['drawers']) ||\n                (x['elem_type'] === 'FILL' && filter_conf['fills']) ||\n                (x['elem_type'] === 'ACC' && filter_conf['accessories']) ||\n                (x['elem_type'] === 'SPACER' && filter_conf['spacer']))\n        });\n    }\n\n    render(proxyId, id) {\n        let proxy = _.find(this.proxies, { proxy: proxyId });\n        this.resize({ width: proxy.width, height: proxy.height });\n        this.renderer.render(this.scenes[id], proxy.camera);\n        proxy.currentScene = id;\n        proxy.canvas.getContext(\"2d\").clearRect(0, 0, proxy.width, proxy.height);\n        proxy.canvas.getContext(\"2d\").drawImage(this.renderer.domElement, 0, 0);\n        proxy.controls.update();\n    }\n\n    renderCamera(proxyId, scene = null, clear=false) {\n        let proxy = _.find(this.proxies, { proxy: proxyId });\n        scene = scene || this.scenes[proxy.currentScene] || this.scenes[proxyId];\n        this.renderer.render(scene, proxy.camera);\n        //proxy.canvas.getContext(\"2d\").clearRect(0, 0, proxy.width, proxy.height);\n        if(clear) proxy.canvas.getContext(\"2d\").clearRect(0, 0, proxy.width, proxy.height);\n        proxy.canvas.getContext(\"2d\").drawImage(this.renderer.domElement, 0, 0);\n    }\n\n    getCompoundBoundingBox(object3D) {\n        var box = null;\n        object3D.traverse(function (obj3D) {\n            var geometry = obj3D.geometry;\n            if (geometry === undefined) return;\n            geometry.computeBoundingBox();\n            if (box === null) {\n                box = geometry.boundingBox;\n            } else {\n                box.union(geometry.boundingBox);\n            }\n        });\n        return box;\n    }\n\n    drawElements(id, elements, position) {\n        if (!this.scenes[id]) this.scenes[id] = new THREE.Scene();\n\n        if(!position) this.resetItems(id);\n        let scene = this.scenes[id];\n\n        let container = new THREE.Object3D();\n\n        for (let i = 0; i < elements.length; i++) {\n            let main_item = elements[i];\n            if (main_item.components == null) continue;\n\n            for (let j = 0; j < Object.values(main_item.components).length; j++) {\n\n                let item = Object.values(main_item.components)[j];\n                if (conf.filterEnable == true) {\n                    if (!((item[conf.filterMaterialKey] || 'missing')\n                        .toString()\n                        .indexOf(conf.filterMaterial) > -1)\n                    ) {\n                        continue;\n                    }\n                }\n\n                let sizes = [\n                    (item.x_domain[1] - item.x_domain[0]) / 100,\n                    (item.y_domain[1] - item.y_domain[0]) / 100,\n                    (item.z_domain[1] - item.z_domain[0]) / 100,\n                ];\n\n                let centers = [\n                    (item.x_domain[1] + item.x_domain[0]) / 200,\n                    (item.y_domain[1] + item.y_domain[0]) / 200,\n                    (item.z_domain[1] + item.z_domain[0]) / 200,\n                ];\n\n                let geometry = (main_item.elem_type == 'L') ? new THREE.CylinderGeometry(sizes[0] / 2, sizes[0] / 2, sizes[1], 12, 2) : new THREE.BoxGeometry(sizes[0], sizes[1], sizes[2]);\n\n                let cube = new THREE.Mesh(geometry, material);\n                cube.position.x = centers[0];\n                cube.position.y = centers[1];\n                cube.position.z = centers[2];\n\n                let geo = new THREE.EdgesGeometry(cube.geometry);\n                let wireframe = new THREE.LineSegments(geo, mat);\n\n                cube.add(wireframe);\n                container.add(cube);\n            }\n        }\n        scene.add(container);\n        if(position) {\n            container.position.x = position.x;\n            container.position.y = position.y;\n            container.position.z = position.z;\n        }\n    }\n\n    openAll() {\n        HD_RENDERER.setDesignerMode(3);\n    }\n\n    closeAll() {\n        HD_RENDERER.setDesignerMode(1);\n    }\n\n    resetItems(id) {\n        let scene = this.scenes[id];\n        if(scene) while (scene.children.length) scene.remove(scene.children[0]);\n    }\n}\n\nconst multiSceneRenderer = new MultiSceneRenderer()\n\nexport { multiSceneRenderer }"], "sourceRoot": ""}