(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["f69ea88c"],{"0419":function(t,a,e){},7718:function(t,a,e){"use strict";e.r(a);var o=function(){var t=this;var a=t.$createElement;var e=t._self._c||a;return e("section",{staticClass:"main",staticStyle:{height:"100%"}},[e("div",{staticStyle:{"z-index":"1000",display:"block",height:"100vh",width:"calc(100vw - 500px)",position:"fixed",top:"0px",left:"60px"}},[e("div",{ref:"view1",staticStyle:{width:"40%",height:"100vh",display:"inline-block"}}),e("div",{ref:"view2",staticStyle:{width:"60%",height:"100vh",display:"inline-block"}})]),e("div",{staticClass:"flex row",staticStyle:{height:"100%"}},[e("div",{staticClass:"col",staticStyle:{background:"red",width:"calc(100vw - 500px)"}},[e("canvas",{ref:"c",staticClass:"col",staticStyle:{height:"calc(100vh)",width:"calc(100vw - 500px)",position:"fixed"}})]),e("div",{staticClass:"col",staticStyle:{"max-width":"440px",background:"white",height:"100%"}},[e("q-scroll-area",{staticStyle:{height:"100vh"}},[e("t-card",{attrs:{name:"Geometry size"}},[e("div",{staticClass:"card"},[e("div",{staticClass:"card-main"},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-6",attrs:{label:"X:"+t.geomX}}),e("q-slider",{staticClass:"col-card-17",attrs:{value:t.geomXtemp,min:40,max:500,dark:"dark"},on:{change:function(a){t.geomXtemp=a;t.tylkoCam.controls.geometryFixed=true;t.tylkoCam.controls.update()},input:function(a){t.tylkoCam.controls.geometryFixed=false;t.updateGeometry(a)}}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-6",attrs:{label:"Y:"+t.geomY}}),e("q-slider",{staticClass:"col-card-17",attrs:{value:t.geomYtemp,min:20,max:300,dark:"dark"},on:{change:function(a){t.geomYtemp=a;t.tylkoCam.controls.geometryFixed=true;t.tylkoCam.controls.update()},input:function(a){t.tylkoCam.controls.geometryFixed=false;t.updateGeometry(undefined,a)}}})],1),e("div",{staticClass:"card-row"},[e("div",{staticClass:"col-card-1"}),e("q-btn",{staticClass:"col-card-6",attrs:{label:"Shelf",color:"blue"},on:{click:t.switchToShelfView}}),e("div",{staticClass:"col-card-1"}),e("q-btn",{staticClass:"col-card-6",attrs:{label:"Component",color:"blue"},on:{click:t.switchToComponentView}}),e("div",{staticClass:"col-card-1"}),e("q-btn",{staticClass:"col-card-6",attrs:{label:"Madness",color:"red"},on:{click:function(a){return t.tylkoCam.setDefaultfView()}}})],1)])])])]),t.ready?e("t-card",{attrs:{name:"Camera Automatic Zoom Calculations"}},[e("div",{staticClass:"card"},[e("div",{staticClass:"card-main"},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-row q-pa-sm q-pb-md"},[e("q-toggle",{attrs:{label:"Disable AutoZoom",color:"yellow",dark:"dark"},model:{value:t.tylkoCam.controls.noAutoZoom,callback:function(a){t.$set(t.tylkoCam.controls,"noAutoZoom",a)},expression:"tylkoCam.controls.noAutoZoom"}}),e("q-toggle",{attrs:{label:"Disable LifeZoom",color:"yellow",dark:"dark"},model:{value:t.tylkoCam.controls.noLifeZoom,callback:function(a){t.$set(t.tylkoCam.controls,"noLifeZoom",a)},expression:"tylkoCam.controls.noLifeZoom"}})],1),e("div",{staticClass:"card-row q-pa-sm q-pb-md"},[e("q-toggle",{attrs:{label:"Disable Animation",color:"yellow",dark:"dark"},model:{value:t.tylkoCam.controls.noTransitionAnimation,callback:function(a){t.$set(t.tylkoCam.controls,"noTransitionAnimation",a)},expression:"tylkoCam.controls.noTransitionAnimation"}}),e("q-toggle",{attrs:{label:"Disable Snapping",color:"yellow",dark:"dark"},model:{value:t.tylkoCam.controls.noSnap,callback:function(a){t.$set(t.tylkoCam.controls,"noSnap",a)},expression:"tylkoCam.controls.noSnap"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-24",attrs:{label:"Geometry to ViewEdge Offsets in object cm:"}})],1),e("div",{staticClass:"card-row"},[e("q-input",{staticClass:"col-card-4",attrs:{label:"Left",type:"number",dark:"dark"},on:{input:t.updateGeometry},model:{value:t.tylkoCam.geometryOffset.left,callback:function(a){t.$set(t.tylkoCam.geometryOffset,"left",t._n(a))},expression:"tylkoCam.geometryOffset.left"}}),e("q-input",{staticClass:"col-card-4",attrs:{label:"Right",type:"number",dark:"dark"},on:{input:t.updateGeometry},model:{value:t.tylkoCam.geometryOffset.right,callback:function(a){t.$set(t.tylkoCam.geometryOffset,"right",t._n(a))},expression:"tylkoCam.geometryOffset.right"}}),e("q-input",{staticClass:"col-card-4",attrs:{label:"Top",type:"number",dark:"dark"},on:{input:t.updateGeometry},model:{value:t.tylkoCam.geometryOffset.top,callback:function(a){t.$set(t.tylkoCam.geometryOffset,"top",t._n(a))},expression:"tylkoCam.geometryOffset.top"}}),e("q-input",{staticClass:"col-card-4",attrs:{label:"Bottom",type:"number",dark:"dark"},on:{input:t.updateGeometry},model:{value:t.tylkoCam.geometryOffset.bottom,callback:function(a){t.$set(t.tylkoCam.geometryOffset,"bottom",t._n(a))},expression:"tylkoCam.geometryOffset.bottom"}}),e("q-input",{staticClass:"col-card-4",attrs:{label:"Front",type:"number",dark:"dark"},on:{input:t.updateGeometry},model:{value:t.tylkoCam.geometryOffset.front,callback:function(a){t.$set(t.tylkoCam.geometryOffset,"front",t._n(a))},expression:"tylkoCam.geometryOffset.front"}}),e("q-input",{staticClass:"col-card-4",attrs:{label:"Back",type:"number",dark:"dark"},on:{input:t.updateGeometry},model:{value:t.tylkoCam.geometryOffset.back,callback:function(a){t.$set(t.tylkoCam.geometryOffset,"back",t._n(a))},expression:"tylkoCam.geometryOffset.back"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-24",attrs:{label:"Geometry to ViewEdge Offsets in screen pixels:"}})],1),e("div",{staticClass:"card-row"},[e("q-input",{staticClass:"col-card-6",attrs:{label:"Left",type:"number",dark:"dark"},on:{input:function(a){return t.tylkoCam.controls.update()}},model:{value:t.tylkoCam.controls.screenEdgeOffset.left,callback:function(a){t.$set(t.tylkoCam.controls.screenEdgeOffset,"left",t._n(a))},expression:"tylkoCam.controls.screenEdgeOffset.left"}}),e("q-input",{staticClass:"col-card-6",attrs:{label:"Right",type:"number",dark:"dark"},on:{input:function(a){return t.tylkoCam.controls.update()}},model:{value:t.tylkoCam.controls.screenEdgeOffset.right,callback:function(a){t.$set(t.tylkoCam.controls.screenEdgeOffset,"right",t._n(a))},expression:"tylkoCam.controls.screenEdgeOffset.right"}}),e("q-input",{staticClass:"col-card-6",attrs:{label:"Top",type:"number",dark:"dark"},on:{input:function(a){return t.tylkoCam.controls.update()}},model:{value:t.tylkoCam.controls.screenEdgeOffset.top,callback:function(a){t.$set(t.tylkoCam.controls.screenEdgeOffset,"top",t._n(a))},expression:"tylkoCam.controls.screenEdgeOffset.top"}}),e("q-input",{staticClass:"col-card-6",attrs:{label:"Bottom",type:"number",dark:"dark"},on:{input:function(a){return t.tylkoCam.controls.update()}},model:{value:t.tylkoCam.controls.screenEdgeOffset.bottom,callback:function(a){t.$set(t.tylkoCam.controls.screenEdgeOffset,"bottom",t._n(a))},expression:"tylkoCam.controls.screenEdgeOffset.bottom"}})],1)])])])]):t._e(),t.ready?e("t-card",{attrs:{name:"Camera controls"}},[e("div",{staticClass:"card"},[e("div",{staticClass:"card-main"},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-row q-pa-sm q-pb-md"},[e("div",{staticClass:"col-card-1"}),e("q-btn",{staticClass:"col-card-3",attrs:{label:"Left",color:"blue"},on:{click:function(a){return t.tylkoCam.setView("left")}}}),e("q-btn",{staticClass:"col-card-1",attrs:{label:"L",color:"red"},on:{click:function(a){return t.tylkoCam.setView("left_straight")}}}),e("div",{staticClass:"col-card-2"}),e("q-btn",{staticClass:"col-card-4",attrs:{label:"Front",color:"blue"},on:{click:function(a){return t.tylkoCam.setView("front")}}}),e("div",{staticClass:"col-card-2"}),e("q-btn",{staticClass:"col-card-3",attrs:{label:"Right",color:"blue"},on:{click:function(a){return t.tylkoCam.setView("right")}}}),e("q-btn",{staticClass:"col-card-1",attrs:{label:"R",color:"red"},on:{click:function(a){return t.tylkoCam.setView("right_straight")}}}),e("div",{staticClass:"col-card-2"}),e("q-btn",{staticClass:"col-card-3",attrs:{label:"Top",color:"blue"},on:{click:function(a){return t.tylkoCam.setView("top")}}}),e("q-btn",{staticClass:"col-card-1",attrs:{label:"T",color:"red"},on:{click:function(a){return t.tylkoCam.setView("top_straight")}}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-6",attrs:{label:"fov:"+t.tylkoCam.fov}}),e("q-slider",{staticClass:"col-card-17",attrs:{min:10,max:180,dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.fov,callback:function(a){t.$set(t.tylkoCam,"fov",a)},expression:"tylkoCam.fov"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-6",attrs:{label:"Range"}}),e("q-range",{staticClass:"col-card-17",attrs:{min:1,max:1e4,"label-always":"label-always","drag-range":"drag-range",dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.range,callback:function(a){t.$set(t.tylkoCam,"range",a)},expression:"tylkoCam.range"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Position X"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:-3e3,max:3e3,"label-always":"label-always","label-value":parseInt(t.tylkoCam.position.x),dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.position.x,callback:function(a){t.$set(t.tylkoCam.position,"x",a)},expression:"tylkoCam.position.x"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Position Y"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:-3e3,max:3e3,"label-always":"label-always","label-value":parseInt(t.tylkoCam.position.y),dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.position.y,callback:function(a){t.$set(t.tylkoCam.position,"y",a)},expression:"tylkoCam.position.y"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Position Z"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:-3e3,max:3e3,"label-always":"label-always","label-value":parseInt(t.tylkoCam.position.z),dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.position.z,callback:function(a){t.$set(t.tylkoCam.position,"z",a)},expression:"tylkoCam.position.z"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Target X"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:-300,max:300,"label-always":"label-always","label-value":parseInt(t.tylkoCam.target.x),dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.target.x,callback:function(a){t.$set(t.tylkoCam.target,"x",a)},expression:"tylkoCam.target.x"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Target Y"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:-300,max:300,"label-always":"label-always","label-value":parseInt(t.tylkoCam.target.y),dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.target.y,callback:function(a){t.$set(t.tylkoCam.target,"y",a)},expression:"tylkoCam.target.y"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Target Z"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:-300,max:300,"label-always":"label-always","label-value":parseInt(t.tylkoCam.target.z),dark:"dark"},on:{input:function(a){return t.tylkoCam.updateCamera()}},model:{value:t.tylkoCam.target.z,callback:function(a){t.$set(t.tylkoCam.target,"z",a)},expression:"tylkoCam.target.z"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Rotate Theta"}}),t.tylkoCam.controls.new_theta!==null?e("q-slider",{staticClass:"col-card-13",attrs:{type:"number",min:-Math.PI,max:Math.PI,step:.01,"label-always":"label-always","label-value":+t.tylkoCam.controls.new_theta.toFixed(3),dark:"dark"},on:{input:function(a){return t.tylkoCam.controls.update()}},model:{value:t.tylkoCam.controls.new_theta,callback:function(a){t.$set(t.tylkoCam.controls,"new_theta",t._n(a))},expression:"tylkoCam.controls.new_theta"}}):t._e()],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Rotate Phi"}}),t.tylkoCam.controls.new_phi!==null?e("q-slider",{staticClass:"col-card-13",attrs:{type:"number",min:0,max:Math.PI,step:.01,"label-always":"label-always","label-value":+t.tylkoCam.controls.new_phi.toFixed(3),dark:"dark"},on:{input:function(a){return t.tylkoCam.controls.update()}},model:{value:t.tylkoCam.controls.new_phi,callback:function(a){t.$set(t.tylkoCam.controls,"new_phi",t._n(a))},expression:"tylkoCam.controls.new_phi"}}):t._e()],1),e("div",{staticClass:"card-row"},[e("div",{staticClass:"col-card-4"}),e("q-btn",{staticClass:"col-card-8",attrs:{label:"get matrix",color:"green"},on:{click:t.getMatrix}}),e("div",{staticClass:"col-card-2"}),e("q-btn",{staticClass:"col-card-8",attrs:{label:"set matrix",color:"red"},on:{click:t.setMatrix}})],1)])])])]):t._e(),t.ready?e("t-card",{attrs:{name:"Orbit Controls"}},[e("div",{staticClass:"card"},[e("div",{staticClass:"card-main"},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-row q-pa-sm q-pb-md"},[e("q-toggle",{staticClass:"col-card-7",attrs:{label:"Enable",color:"green",dark:"dark"},model:{value:t.tylkoCam.controls.enabled,callback:function(a){t.$set(t.tylkoCam.controls,"enabled",a)},expression:"tylkoCam.controls.enabled"}}),e("q-toggle",{staticClass:"col-card-9",attrs:{label:"NoRotate",color:"red",dark:"dark"},model:{value:t.tylkoCam.controls.noRotate,callback:function(a){t.$set(t.tylkoCam.controls,"noRotate",a)},expression:"tylkoCam.controls.noRotate"}}),e("q-toggle",{attrs:{label:"NoPan",color:"red",dark:"dark"},model:{value:t.tylkoCam.controls.noPan,callback:function(a){t.$set(t.tylkoCam.controls,"noPan",a)},expression:"tylkoCam.controls.noPan"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Polar range"}}),e("q-range",{staticClass:"col-card-13",attrs:{min:0,max:Math.PI,step:.01,"left-label-value":t.tylkoCam.controls.polarAngle?+t.tylkoCam.controls.polarAngle.min.toFixed(3):0,"right-label-value":t.tylkoCam.controls.polarAngle?+t.tylkoCam.controls.polarAngle.max.toFixed(3):0,"label-always":"label-always","drag-range":"drag-range",dark:"dark"},model:{value:t.tylkoCam.controls.polarAngle,callback:function(a){t.$set(t.tylkoCam.controls,"polarAngle",a)},expression:"tylkoCam.controls.polarAngle"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"Azimuth range"}}),e("q-range",{staticClass:"col-card-13",attrs:{min:-Math.PI,max:Math.PI,step:.01,"left-label-value":t.tylkoCam.controls.azimuthAngle?+t.tylkoCam.controls.azimuthAngle.min.toFixed(3):0,"right-label-value":t.tylkoCam.controls.azimuthAngle?+t.tylkoCam.controls.azimuthAngle.max.toFixed(3):0,"label-always":"label-always","drag-range":"drag-range",dark:"dark"},model:{value:t.tylkoCam.controls.azimuthAngle,callback:function(a){t.$set(t.tylkoCam.controls,"azimuthAngle",a)},expression:"tylkoCam.controls.azimuthAngle"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"ZoomSpeed"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:.1,max:5,step:.1,"label-always":"label-always",dark:"dark"},model:{value:t.tylkoCam.controls.zoomSpeed,callback:function(a){t.$set(t.tylkoCam.controls,"zoomSpeed",a)},expression:"tylkoCam.controls.zoomSpeed"}})],1),e("div",{staticClass:"card-row"},[e("q-field",{staticClass:"col-card-10",attrs:{label:"RotateSpeed"}}),e("q-slider",{staticClass:"col-card-13",attrs:{min:.1,max:5,step:.1,"label-always":"label-always",dark:"dark"},model:{value:t.tylkoCam.controls.rotateSpeed,callback:function(a){t.$set(t.tylkoCam.controls,"rotateSpeed",a)},expression:"tylkoCam.controls.rotateSpeed"}})],1)])])])]):t._e()],1)],1)])])};var l=[];var s=e("b263");var r=e("3156");var i=e.n(r);var n=e("e411");var c=e("5926");var d=e("18a5");var m=e("480d");var u=e("39fc");var C={components:i()({},u["j"]),data:function t(){return{ready:false,geomX:240,geomXtemp:240,geomY:120,geomYtemp:120,geomZ:32,geom:null,view1:null,view2:null,canvas:null,temp1:0,temp2:0,renderer:null,scene:null,tylkoCam:null,camera2:null,controls2:null}},mounted:function t(){var a=this;d["a"].application.viewport.fixed(true);this.setUp();this.createGeometry();this.render();this.ready=true;var e=false;this.renderLoop=function(){if(e){e=false;a.render();a.tylkoCam.controls.update()}window.requestAnimationFrame(a.renderLoop)};this.renderLoop();this.tylkoCam.controls.addEventListener("render",function(){e=true});this.tylkoCam.controls.addEventListener("change",function(){a.render()});this.controls2.addEventListener("change",function(){a.render()})},methods:{setUp:function t(){this.canvas=this.$refs.c;this.view1=this.$refs.view1;this.view2=this.$refs.view2;this.scene=new n["Scene"];this.scene.background=new n["Color"]("black");this.renderer=new n["WebGLRenderer"]({canvas:this.canvas});this.tylkoCam=new m["a"](this.view1,this.scene);this.cameraHelper=new n["CameraHelper"](this.tylkoCam.camera);this.scene.add(this.cameraHelper);this.scene.add(this.tylkoCam.camera);this.camera2=new n["PerspectiveCamera"](60,2,.1,5e4);this.camera2.position.set(800,500,700);this.camera2.lookAt(0,5,0);this.controls2=new n["OrbitControls"](this.camera2,this.view2);this.controls2.target.set(0,5,0);this.controls2.update()},createGeometry:function t(){var a=new n["BoxBufferGeometry"](1,1,1);var e=new n["MeshPhongMaterial"]({color:"#8AC",opacity:.5,transparent:false});this.geom=new n["Mesh"](a,e);this.geom.scale.set(this.geomX,this.geomY,this.geomZ);this.geom.position.set(0,this.geomY/2,this.geomZ/2);this.scene.add(this.geom);var o=280,l=20;var s=new n["Geometry"];var r=new n["LineBasicMaterial"]({color:"gray"});for(var i=-o;i<=o;i+=l){if(i>=0){s.vertices.push(new n["Vector3"](-o,-.04,i));s.vertices.push(new n["Vector3"](o,-.04,i))}if(o-i<=320){s.vertices.push(new n["Vector3"](-o,o-i-.04,0));s.vertices.push(new n["Vector3"](o,o-i-.04,0))}s.vertices.push(new n["Vector3"](i,-.04,0));s.vertices.push(new n["Vector3"](i,-.04,o));s.vertices.push(new n["Vector3"](i,-.04,0));s.vertices.push(new n["Vector3"](i,320-.04,0))}var c=new n["Line"](s,r,n["LinePieces"]);this.scene.add(c);this.tylkoCam.updateGeometry({x:-this.geomX/2,y:0,z:0},{x:this.geomX/2,y:this.geomY,z:this.geomZ})},resizeRendererToDisplaySize:function t(a){var e=a.domElement;var o=e.clientWidth;var l=e.clientHeight;var s=e.width!==o||e.height!==l;if(s){a.setSize(o,l,false)}return s},setScissorForElement:function t(a){var e=this.canvas.getBoundingClientRect();var o=a.getBoundingClientRect();var l=Math.min(o.right,e.right)-e.left;var s=Math.max(0,o.left-e.left);var r=Math.min(o.bottom,e.bottom)-e.top;var i=Math.max(0,o.top-e.top);var n=Math.min(e.width,l-s);var c=Math.min(e.height,r-i);var d=e.height-r;this.renderer.setScissor(s,d,n,c);this.renderer.setViewport(s,d,n,c);return n/c},render:function t(){console.log(">>>>> RENDER");this.resizeRendererToDisplaySize(this.renderer);this.renderer.setScissorTest(true);var a=this.setScissorForElement(this.view1);this.tylkoCam.update(a);this.cameraHelper.update();this.cameraHelper.visible=false;this.scene.background.set(0);this.renderer.render(this.scene,this.tylkoCam.camera);a=this.setScissorForElement(this.view2);this.camera2.aspect=a;this.camera2.updateProjectionMatrix();this.cameraHelper.visible=true;this.scene.background.set(64);this.renderer.render(this.scene,this.camera2)},updateGeometry:function t(a,e){if(a!==undefined)this.geomX=a;if(e!==undefined)this.geomY=e;this.geom.scale.set(this.geomX,this.geomY,this.geomZ);this.geom.position.y=this.geomY/2;this.tylkoCam.updateGeometry({x:-this.geomX/2,y:0,z:0},{x:this.geomX/2,y:this.geomY,z:this.geomZ});this.render()},switchToShelfView:function t(){console.log(">>>> Shelf");this.tylkoCam.setShelfView({x:-this.geomX/2,y:0,z:0},{x:this.geomX/2,y:this.geomY,z:this.geomZ})},switchToComponentView:function t(a,e){console.log(">>>> Component");this.tylkoCam.setComponentView({x:-20,y:0,z:0},{x:40,y:this.geomY,z:this.geomZ})},getMatrix:function t(){this.$q.dialog({title:"Matrix",message:JSON.stringify(this.tylkoCam.getCamSettings()),ok:true})},setMatrix:function t(){var a=prompt("Enter matrix data","");if(a)this.tylkoCam.setCamSettings(JSON.parse(a))}}};var k=C;var y=e("f077");var p=e("2877");var f=Object(p["a"])(k,o,l,false,null,null,null);var h=a["default"]=f.exports},f077:function(t,a,e){"use strict";var o=e("0419");var l=e.n(o);var s=l.a}}]);
//# sourceMappingURL=f69ea88c.89908a52.js.map