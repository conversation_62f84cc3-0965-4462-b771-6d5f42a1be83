(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["52ec88eb"],{"02b5":function(t,e,a){},"07c3":function(t,e,a){"use strict";var i=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("div",{staticClass:"container"},[a("div",{ref:"mini",staticClass:"mini"})])};var s=[];var n=a("a34a");var l=a.n(n);var r=a("192a");var o=a("c973");var c=a.n(o);var d=a("d6b6");var u=a("18a5");var p={props:{id:[String,Number],geo:[String,Object,Array],bgColor:[String],update:[String]},methods:{load:function t(e){var a=this;var i=u["a"].api.geo.componentSet({type:"componentRelations",id:e,queryForMiniature:true});i.pipe(function(t){console.log(t);a.build(t,e,false)},"paletteRelations")},build:function t(e,a,i){var s=this;u["a"].services.miniatures.add({data:e,id:a,fetched:i,base64:true,colorSet:"production",bgColor:this.bgColor}).then(function(){var t=c()(l.a.mark(function t(e){var a;return l.a.wrap(function t(i){while(1){switch(i.prev=i.next){case 0:while(s.$refs.mini&&s.$refs.mini.hasChildNodes()){s.$refs.mini.removeChild(s.$refs.mini.firstChild)}a=new Image;a.src=e.painterState;s.$refs.mini.appendChild(a);case 4:case"end":return i.stop()}}},t)}));return function(e){return t.apply(this,arguments)}}())}},created:function t(){},watch:{geo:function t(){this.build(this.geo,this.id,true)}},mounted:function t(){if(this.geo){this.build(this.geo,this.id,true);return}if(this.id){this.load(this.id)}}};var v=p;var h=a("f699");var f=a("2877");var g=Object(f["a"])(v,i,s,false,null,"360dcb88",null);var m=e["a"]=g.exports},"0c79":function(t,e,a){"use strict";var i=a("7989");var s=a.n(i);var n=s.a},"24f6":function(t,e,a){"use strict";var i=a("8fe4");var s=a.n(i);var n=s.a},"259b":function(t,e,a){},"38f3":function(t,e,a){},"39fc":function(t,e,a){"use strict";var i=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("section",{staticClass:"tylko-preset-picker t-grid card-wrapper"},[a("div",{staticClass:"card-row"},[a("div",{staticClass:"col-card-6"},[a("p",[t._v(t._s(t.sectionLabel))])]),a("div",{staticClass:"col-card-18"},[!t.isConstant(t.targetField?t.targetModel[t.targetField]:t.targetModel)?a("div",{staticClass:"_"},[t.noLabel?a("div",{staticClass:"_"},[a("q-option-group",{attrs:{inline:"inline",dense:"dense",dark:"dark","toggle-color":"black",options:t.options},on:{input:function(e){return t.toggleButtonChanged(t.targetField,t.targetModel[t.targetField])}},model:{value:t.targetModel[t.targetField],callback:function(e){t.$set(t.targetModel,t.targetField,e)},expression:"targetModel[targetField]"}})],1):a("div",{staticClass:"_"},[t.targetField?a("div",{staticClass:"_"},[a("q-btn-toggle",{attrs:{dark:"dark",dense:"dense",size:t.big?"md":"sm","toggle-color":"black",options:t.options},on:{input:function(e){return t.toggleButtonChanged(t.targetField,t.targetModel[t.targetField])}},model:{value:t.targetModel[t.targetField],callback:function(e){t.$set(t.targetModel,t.targetField,e)},expression:"targetModel[targetField]"}})],1):a("div",{staticClass:"_"},[a("q-btn-toggle",{attrs:{dark:"dark",dense:"dense",size:t.big?"md":"sm","toggle-color":"black",options:t.options},model:{value:t.targetModel,callback:function(e){t.targetModel=e},expression:"targetModel"}})],1)])]):t._e(),t.isConstant(t.targetField?t.targetModel[t.targetField]:t.targetModel)?a("div",{staticClass:"_"},[a("q-input",{staticClass:"q-pa-a-xs",attrs:{dark:"dark",filled:"filled",debounce:"300",dense:"dense","hide-bottom-space":"hide-bottom-space",hint:"Constant value (preset field)"},on:{input:function(e){return t.toggleButtonChanged(t.targetField,t.targetModel[t.targetField])}},model:{value:t.targetModel[t.targetField],callback:function(e){t.$set(t.targetModel,t.targetField,e)},expression:"targetModel[targetField]"}})],1):t._e()])])])};var s=[];var n=a("9b8e");var l=a("1df5");var r={props:{sectionLabel:[String],allowArbitratyConstant:[String,Boolean],options:[Array],targetModel:[Object],targetField:[String],noLabel:[Boolean],big:[Boolean,String]},data:function t(){return{}},computed:{},watch:{},mounted:function t(){},methods:{toggleButtonChanged:function t(){this.$emit("toggleButtonChanged",this.targetField,this.targetModel[this.targetField])},isConstant:function t(e){if(e===undefined){return false}else{return e.toString().startsWith("#")}}}};var o=r;var c=a("afc6");var d=a("2877");var u=Object(d["a"])(o,i,s,false,null,null,null);var p=u.exports;var v=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("section",{staticClass:"tylko-field-box tylko-input t-grid card-wrapper"},[a("div",{staticClass:"card-row"},[a("div",{staticClass:"col-card-6"},[a("p",[t._v(t._s(t.sectionLabel))])]),a("div",{staticClass:"col-card-18"},[a("div",{staticClass:"_"},[a("q-input",{attrs:{dark:"dark",filled:"filled",dense:"dense","hide-bottom-space":"hide-bottom-space",hint:t.sectionTip?t.sectionTip:t.sectionLabel},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")){return null}return t.setValue(e)},focus:t.enterMode,blur:t.setValue},model:{value:t.newValue,callback:function(e){t.newValue=e},expression:"newValue"}},[a("q-icon",{directives:[{name:"show",rawName:"v-show",value:!t.neverEmpty&&t.newValue.toString().length>0,expression:"!neverEmpty && newValue.toString().length>0"}],staticClass:"cursor-pointer",attrs:{slot:"append",name:"close"},on:{click:function(e){return t.updateValue(t.newValue="")}},slot:"append"})],1)],1)])])])};var h=[];var f=a("d6b6");var g={props:{sectionTip:[String],sectionLabel:[String],allowArbitratyConstant:[String,Boolean],options:[Array],targetModel:[Object],targetField:[String],updateModel:[Boolean],allowMultipleStreaks:[Boolean],neverEmpty:{type:[Boolean,String],default:false},deep:[Boolean]},data:function t(){return{value:[Number,String],newValue:[Number,String]}},computed:{},watch:{targetModel:{handler:function t(){if(!this.targetField){this.value=this.targetModel}else{this.value=this.targetModel[this.targetField]}this.newValue=this.value},deep:true}},mounted:function t(){if(!this.targetField){this.value=this.targetModel}else{this.value=this.targetModel[this.targetField]}this.newValue=this.value},methods:{enterMode:function t(){this.enterModeFlag=true},setValue:function t(){if(this.allowMultipleStreaks!=true&&!this.enterModeFlag)return;if(this.value==this.newValue)return;this.value=this.newValue;this.updateValue(this.value)},updateValue:function t(e){this.enterModeFlag=false;if(this.updateModel){if(!this.targetField){this.targetModel=e}else{this.targetModel[this.targetField]=e}}this.$emit("save",e);if(this.targetField)this.$emit("updateField",{field:this.targetField,value:e})},toggleButtonChanged:function t(){this.$emit("toggleButtonChanged",this.targetField,this.targetModel[this.targetField])}}};var m=g;var b=a("83c3");var C=Object(d["a"])(m,v,h,false,null,"8c8b157c",null);var M=C.exports;var w=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("section",{staticClass:"tylko-field-box tylko-input t-grid card-wrapper"},[a("div",{staticClass:"card-row"},[a("div",{staticClass:"col-card-6"},[a("p",[t._v(t._s(t.sectionLabel))])]),a("div",{staticClass:"col-card-18"},[a("div",{staticClass:"_"},[a("q-slider",{attrs:{dark:"dark",filled:"filled",min:t.min,max:t.max,dense:"dense",hint:t.sectionTip?t.sectionTip:t.sectionLabel},on:{input:function(){t.setValueSafe()}},model:{value:t.newValue,callback:function(e){t.newValue=e},expression:"newValue"}})],1)])])])};var S=[];var k=a("9ec3");var y=a.n(k);var F={props:{sectionTip:[String],sectionLabel:[String],targetModel:[Object],targetField:[String],updateModel:[Boolean],przepustnica:[Number],min:[Number],max:[Number],neverEmpty:{type:[Boolean,String],default:false},deep:[Boolean]},data:function t(){return{value:[Number,String],newValue:[Number,String],setValueSafe:[Function]}},computed:{},watch:{targetModel:{handler:function t(){if(!this.targetField){this.value=this.targetModel}else{this.value=this.targetModel[this.targetField]}this.newValue=this.value},deep:true}},mounted:function t(){var e=this;this.setValueSafe=y.a.throttle(function(){return e.setValue()},this.przepustnica?+this.przepustnica:60);if(!this.targetField){this.value=this.targetModel}else{this.value=this.targetModel[this.targetField]}this.newValue=this.value},methods:{enterMode:function t(){this.enterModeFlag=true},setValue:function t(){if(this.value==this.newValue)return;this.value=this.newValue;this.updateValue(this.value)},updateValue:function t(e){this.enterModeFlag=false;if(this.updateModel){if(!this.targetField){this.targetModel=e}else{this.targetModel[this.targetField]=e}}this.$emit("save",e);if(this.targetField)this.$emit("updateField",{field:this.targetField,value:e})}}};var x=F;var $=a("24f6");var q=Object(d["a"])(x,w,S,false,null,"3a36560d",null);var V=q.exports;var B=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("section",{staticClass:"tylko-card t-grid",class:{"card-disabled":t.active===false}},[a("div",{staticClass:"card"},[a("div",{staticClass:"card-main"},[a("div",{staticClass:"card-title"},[a("div",{staticClass:"card-row"},[a("div",{staticClass:"_",class:t.$slots.links?"col-card-14":"col-card-20"},[t.active?a("div",{staticClass:"card-title-label"},[t.nameSwitch?a("div",{staticClass:"_"},[a("q-btn-dropdown",{staticClass:"force-title-size",attrs:{"no-caps":"no-caps",dense:"dense",unelevated:"unelevated",flat:"flat","content-class":"card-action",icon:t.nameModel.icon,label:t.nameModel.name}},[t._t("nameSwitch")],2)],1):a("div",{staticClass:"_"},[a("p",{staticClass:"lead"},[t._v(t._s(t.name))])])]):a("div",{staticClass:"card-title-label"},[a("p",{staticClass:"lead"},[t._v(t._s(t.name)+" not selected.")])])]),t.$slots.links&&t.active?a("div",{staticClass:"_ col-card-6"},[t._t("links")],2):t._e(),a("div",{staticClass:"_ col-card-4"},[t.$slots.options&&t.active?a("div",{staticClass:"_"},[a("q-btn-dropdown",{attrs:{dense:"dense","text-color":"grey",icon:"playlist_play",unelevated:"unelevated","content-class":"card-action"}},[t._t("options")],2)],1):t._e()])])])])]),t._t("default")],2)};var L=[];var O={props:{name:[String],nameSwitch:[Boolean],nameModel:[Object],active:{type:Boolean,default:true}},data:function t(){return{empty:true}},mounted:function t(){}};var N=O;var j=a("9cd6");var E=Object(d["a"])(N,B,L,false,null,null,null);var A=E.exports;var I=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("section",{staticClass:"tylko-card t-grid"},[t._t("default")],2)};var T=[];var D={props:{name:[String],accordion:[Boolean]},data:function t(){return{}},mounted:function t(){}};var z=D;var W=Object(d["a"])(z,I,T,false,null,"421e5462",null);var R=W.exports;var P=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("section",{staticClass:"tylko-field-box tylko-preset-picker t-grid card-wrapper"},[a("div",{staticClass:"card-row"},[a("div",{staticClass:"col-card-6"},[a("p",[t._v(t._s(t.sectionLabel))])]),a("div",{staticClass:"col-card-18"},[a("div",{staticClass:"_"},[a("q-select",{attrs:{"options-cover":t.up,dark:"dark",dense:"dense",filled:"filled",options:t.options,hint:t.sectionLabel},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[a("q-icon",{directives:[{name:"show",rawName:"v-show",value:!t.neverEmpty,expression:"!neverEmpty"}],attrs:{slot:"append",name:"delete"},on:{click:function(e){return t.$emit("save",undefined)}},slot:"append"})],1)],1)])])])};var H=[];var J=a("1a9d");var X={props:{sectionLabel:[String],allowArbitratyConstant:[String,Boolean],options:[Array],targetModel:[Object],targetField:[String],updateModel:[Boolean],up:[Boolean],useLabelValuePair:[Boolean],emitValue:[Boolean],neverEmpty:{type:[Boolean,String],default:false}},data:function t(){return{value:[String]}},computed:{},watch:{value:function t(e){if(this.updateModel){if(!this.targetField){this.targetModel=e}else{this.targetModel[this.targetField]=e}}this.$emit("save",this.emitValue?e.value:e)},targetModel:{handler:function t(){this.updateValue()},deep:true}},mounted:function t(){this.updateValue()},methods:{updateValue:function t(){if(!this.targetField){this.value=y.a.find(this.options,{value:this.targetModel})}else{this.value=y.a.find(this.options,{value:this.targetModel[this.targetField]})}},toggleButtonChanged:function t(){this.$emit("toggleButtonChanged",this.targetField,this.targetModel[this.targetField])}}};var G=X;var K=a("c61f");var Q=Object(d["a"])(G,P,H,false,null,null,null);var U=Q.exports;var Y=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("div",[a("q-expansion-item",{staticClass:"bottom-line",attrs:{"expand-separator":"expand-separator",group:t.$parent.accordion?"accordion":false,"dense-toggle":"dense-toggle",dark:"dark",label:t.name}},[a("div",{staticClass:"expansion"},[t._t("default")],2)])],1)};var Z=[];var tt={props:{name:[String]},data:function t(){return{}},mounted:function t(){}};var et=tt;var at=a("b115");var it=Object(d["a"])(et,Y,Z,false,null,null,null);var st=it.exports;var nt=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("section",{staticClass:"tylko-field-box t-grid"},[a("div",{staticClass:"card-row"},[a("div",{staticClass:"col-card-6"},[a("p",[t._v(t._s(t.sectionLabel))])]),a("div",{staticClass:"col-card-18"},[a("div",{staticClass:"_"},[a("q-select",{attrs:{hint:"Additional parameters",filled:"filled",dark:"dark",dense:"dense","use-input":"use-input","use-chips":"use-chips",multiple:"multiple","hide-dropdown-icon":"hide-dropdown-icon","new-value-mode":"add-unique"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)])])])};var lt=[];var rt={props:{sectionLabel:[String],allowArbitratyConstant:[String,Boolean],options:[Array],targetModel:[Object],targetField:[String],updateModel:[Boolean]},data:function t(){return{value:[String]}},computed:{},watch:{value:function t(e){if(this.updateModel){if(!this.targetField){this.targetModel=e}else{this.targetModel[this.targetField]=e}}this.$emit("save",e)},targetModel:{handler:function t(e,a){if(!y.a.isEqual(e.additional_params,a.additional_params))this.updateValue()},deep:true}},mounted:function t(){if(!this.targetField){this.value=this.targetModel}else{this.value=this.targetModel[this.targetField]}},methods:{updateValue:function t(){if(!this.targetField){this.value=this.targetModel}else{this.value=this.targetModel[this.targetField]}},toggleButtonChanged:function t(){this.$emit("toggleButtonChanged",this.targetField,this.targetModel[this.targetField])}}};var ot=rt;var ct=a("e9cd");var dt=Object(d["a"])(ot,nt,lt,false,null,null,null);var ut=dt.exports;var pt=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("div",{staticClass:"t-capeid-rpc_actions_entry"},[a("q-btn",{attrs:{color:"black",label:t.buttonText},nativeOn:{click:function(e){return t.evaluate(e)}}}),a("q-dialog",{attrs:{position:"top",minimized:"minimized"},model:{value:t.showDialog,callback:function(e){t.showDialog=e},expression:"showDialog"}},[a("div",{staticClass:"modal-main global flex"},[a("div",{staticClass:"row full"},[a("p",{staticClass:"q-headline"},[t._v(t._s(t.promptTitle))])]),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-select",{staticStyle:{width:"250px"},attrs:{label:t.promptMessage,filled:"filled",dark:"dark","use-input":"use-input","use-chips":"use-chips",multiple:"multiple","hide-dropdown-icon":"hide-dropdown-icon","input-debounce":"0","new-value-mode":"add-unique"},model:{value:t.parametersChips,callback:function(e){t.parametersChips=e},expression:"parametersChips"}})],1)]),t.desc!==undefined?a("div",{staticClass:"row full"},[a("p",[a("i",[t._v('"'+t._s(t.desc)+'"')])])]):t._e(),a("div",{staticClass:"row full"},[a("div",{staticClass:"col"},[a("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup",value:t.v-t.close-t.popup,expression:"v-close-popup"}],attrs:{color:"dark",inverted:"inverted",label:"Cancel"}}),a("q-btn",{staticClass:"float-right",attrs:{color:"primary",label:t.buttonText},nativeOn:{click:function(e){return t.callActionInput(e)}}})],1)])])])],1)};var vt=[];var ht=a("18a5");var ft=a("965a");var gt={name:"t-rpc-action",props:{desc:[String],source:[String,Number],buttonText:[String,Number],methodName:[String,Number],promptTitle:[String,Number],promptMessage:[String,Number],argumentPayload:[String,Number,Array,Object],withInput:{type:[Boolean,String],default:false},showDialog:false,action:Function},data:function t(){return{parametersChips:[]}},components:{"t-modal-action":ft["a"]},methods:{evaluate:function t(){if(this.withInput){this.showDialog=true}else{ht["a"].api.callFunction(this.source,this.methodName,this.argumentPayload).then(this.evaluateResponse)}},callActionInput:function t(){var e=this;ht["a"].api.callFunction(this.source,this.methodName,this.parametersChips).then(function(t){if(t.status=="ok"){var a=e.$refs.rpcModal;e.showDialog=false}return t}).then(this.evaluateResponse)},evaluateResponse:function t(e){ht["a"].application.bus.$emit("actions",e.actions);if(e.reload_components){}this.$q.notify({type:e.status=="ok"?"positive":"negative",message:e.message,position:"center"})}}};var mt=gt;var bt=a("4955");var Ct=Object(d["a"])(mt,pt,vt,false,null,"ae70fe90",null);var Mt=Ct.exports;var wt=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("div",{staticClass:"setup-list-container col",staticStyle:{width:"100%"}},[a("div",{staticClass:"row",staticStyle:{width:"100%"}},[a("div",{staticClass:"col"},[a("q-tabs",{staticClass:"setup-tabs",attrs:{"active-color":"white",dense:"dense",align:"left"},model:{value:t.selectedSetupModel,callback:function(e){t.selectedSetupModel=e},expression:"selectedSetupModel"}},t._l(t.setupList,function(e,i){return a("q-tab",{key:e.id,staticClass:"q-px-sm",attrs:{color:"white",name:e.id,label:t.getLabel(e.id)}},[t.selectMode.value==4?a("div",{staticClass:"_"},[a("div",[t._v("Height: "+t._s(e.height))]),a("div",[t._v("Types: "+t._s(e.types))]),a("t-mini",{key:e.id,staticClass:"inline",attrs:{id:e.id,"bg-color":"#aaaaaa"}})],1):t._e()])}),1),t.setupList.length<1?a("div",{staticClass:"_"},[a("p",[t._v("No setups")])]):t._e()],1),a("div",{staticClass:"col",staticStyle:{"max-width":"200px"}},[a("q-select",{attrs:{outlined:"outlined","options-dark":"options-dark","items-aligned":"false","options-cover":"options-cover","options-dense":"options-dense",dark:"dark","option-label":"label",dense:"dense",options:t.selectModeOptions},model:{value:t.selectMode,callback:function(e){t.selectMode=e},expression:"selectMode"}},[a("q-icon",{attrs:{slot:"prepend",name:"assignment"},slot:"prepend"})],1)],1),a("div",{staticClass:"col",staticStyle:{"max-width":"200px"}},[a("q-select",{attrs:{flat:"flat",outlined:"outlined",borderless:"borderless","items-aligned":"false","options-cover":"options-cover","options-dense":"options-dense",dark:"dark","option-value":"id","option-label":"label",dense:"dense","emit-value":"emit-value",options:t.setupList},model:{value:t.choosenSetup,callback:function(e){t.choosenSetup=e},expression:"choosenSetup"}})],1),a("div",{staticClass:"col",staticStyle:{"max-width":"60px"}},[a("q-btn",{staticStyle:{width:"100%",height:"100%"},attrs:{icon:"add",color:"white",flat:"flat",dark:"dark"},on:{click:t.addSetup}})],1)])])};var St=[];var kt=a("a34a");var _t=a.n(kt);var yt=a("192a");var Ft=a("c973");var xt=a.n(Ft);var $t=a("9af0");var qt=a("07c3");var Vt={props:{name:[String],selectedSetupModel:[Object],setupList:[Array],thumbsData:[Array]},components:{"t-mini":qt["a"]},watch:{selectedSetupModel:function t(e,a){this.choosenSetup=e},choosenSetup:function t(e){this.$emit("change",e)}},methods:{isNormalInteger:function t(e){var a=Math.floor(Number(e));return a!==Infinity&&String(a)===e&&a>=0},addSetup:function t(){this.$emit("add")},getLabel:function t(e){var a=_.find(this.setupList,{id:e});switch(this.selectMode.value){case 0:return a.height;case 1:return a.name;case 2:return a.types;case 3:return a.label;case 4:return a.height}},getAllMiniData:function(){var t=xt()(_t.a.mark(function t(){var e;return _t.a.wrap(function t(a){while(1){switch(a.prev=a.next){case 0:a.next=2;return ht["a"].api.palette.collection(this.$route.params.selectedCollection).componentSet.components.thumbs.fetch();case 2:e=a.sent;case 3:case"end":return a.stop()}}},t,this)}));function e(){return t.apply(this,arguments)}return e}()},data:function t(){this.selectModeOptions=[{value:0,label:"Heights"},{value:1,label:"Names"},{value:2,label:"Types"},{value:3,label:"All inline"},{value:4,label:"Full description"}];return{choosenSetup:-1,selectMode:this.selectModeOptions[0]}},mounted:function t(){}};var Bt=Vt;var Lt=a("5efb");var Ot=Object(d["a"])(Bt,wt,St,false,null,null,null);var Nt=Ot.exports;var jt=function(){var t=this;var e=t.$createElement;var a=t._self._c||e;return a("div",{staticClass:"setup-list-container col",staticStyle:{width:"100%"}},[a("div",{staticClass:"row",staticStyle:{width:"100%"}},[a("div",{staticClass:"col"},[a("q-tabs",{staticClass:"setup-tabs",attrs:{"active-color":"white",dense:"dense",align:"left"},model:{value:t.choosenSetup,callback:function(e){t.choosenSetup=e},expression:"choosenSetup"}},[t._l(t.setupListSorted,function(e,i){return a("q-tab",{key:e.id,staticClass:"q-px-sm",attrs:{color:"white",name:e.id,label:e.label},on:{click:function(e){t.changeManual=true}}})}),t.setupList.length<1?a("q-tab",{attrs:{color:"white",disable:"disable",label:"No setups"}}):t._e()],2)],1),a("div",{staticClass:"col",staticStyle:{"max-width":"150px"}},[a("q-select",{attrs:{outlined:"outlined","options-dark":"options-dark","items-aligned":"false","options-cover":"options-cover","options-dense":"options-dense",dark:"dark","option-label":"label",dense:"dense",options:t.selectModeOptions},model:{value:t.selectMode,callback:function(e){t.selectMode=e},expression:"selectMode"}},[a("q-icon",{attrs:{slot:"prepend",name:"settings_ethernet"},slot:"prepend"})],1)],1),a("div",{staticClass:"col",staticStyle:{"max-width":"100px"}},[a("q-select",{attrs:{flat:"flat",outlined:"outlined","items-aligned":"false","options-cover":"options-cover","options-dense":"options-dense",dark:"dark","option-value":"id","option-label":"label",dense:"dense","emit-value":"emit-value",options:t.setupList},model:{value:t.choosenSetup,callback:function(e){t.choosenSetup=e},expression:"choosenSetup"}})],1),a("div",{staticClass:"col",staticStyle:{"max-width":"60px"}},[a("q-btn",{staticStyle:{width:"100%",height:"100%"},attrs:{icon:"add",outlined:"outlined",color:"white",flat:"flat",dark:"dark"},on:{click:t.addSetup}})],1)])])};var Et=[];var At=a("5a51");var It={props:{name:[String],selectedSetupModel:[Object],setupList:[Array]},computed:{setupListSorted:function t(){return y.a.sortBy(this.setupList,function(t){return parseInt(t.label,10)})}},watch:{selectedSetupModel:function t(e,a){this.choosenSetup=e},choosenSetup:function t(e){var a=y.a.find(this.setupList,{id:e});if(a){switch(this.selectMode.value){case 0:if(a.dim){if(a.dim.indexOf("-")>-1){this.$emit("changeWidth",+a.dim.split("-")[0])}else{this.$emit("changeWidth",+a.dim)}}else if(this.isNormalInteger(a.label)){this.$emit("changeWidth",+a.label)}break;case 1:if(this.isNormalInteger(a.label)){this.$emit("changeWidth",+a.label)}break;case 2:if(a.dim){if(a.dim.indexOf("-")>-1){this.$emit("changeWidth",+a.dim.split("-")[0])}else{this.$emit("changeWidth",+a.dim)}}break}}this.$emit("changeSetup",e)},setupList:function t(){if(!this.selectedSetupModel){var e=this.$route.params.setWidth;if(e){this.choosenSetup=e}else{if(this.setupList.length>0){this.choosenSetup=this.setupList[0].id}}}}},methods:{selectSetupById:function t(e){this.choosenSetup=""+e},isNormalInteger:function t(e){var a=Math.floor(Number(e));return a!==Infinity&&String(a)===e&&a>=0},addSetup:function t(){this.$emit("add")}},data:function t(){this.changeManual=false;this.selectModeOptions=[{value:0,label:"Autoselect"},{value:1,label:"Label"},{value:2,label:"DimX"}];return{choosenSetup:-1,currentDimX:-1,selectMode:this.selectModeOptions[0]}},mounted:function t(){}};var Tt=It;var Dt=a("0c79");var zt=Object(d["a"])(Tt,jt,Et,false,null,null,null);var Wt=zt.exports;a.d(e,"j",function(){return Rt});a.d(e,"c",function(){return p});a.d(e,"b",function(){return M});a.d(e,"a",function(){return A});a.d(e,"f",function(){return R});a.d(e,"e",function(){return st});a.d(e,"i",function(){return ut});a.d(e,"d",function(){return Mt});a.d(e,"g",function(){return Nt});a.d(e,"h",function(){return Wt});var Rt={"t-presets":p,"t-input":M,"t-slider":V,"t-card":A,"t-sections":R,"t-section":st,"t-tags":ut,"t-rpc-action":Mt,"t-select":U}},4470:function(t,e,a){},4955:function(t,e,a){"use strict";var i=a("7bdf");var s=a.n(i);var n=s.a},"5efb":function(t,e,a){"use strict";var i=a("f12c");var s=a.n(i);var n=s.a},7989:function(t,e,a){},"7bdf":function(t,e,a){},"83c3":function(t,e,a){"use strict";var i=a("02b5");var s=a.n(i);var n=s.a},"86de":function(t,e,a){},"8fe4":function(t,e,a){},"9cd6":function(t,e,a){"use strict";var i=a("4470");var s=a.n(i);var n=s.a},ac1b:function(t,e,a){},afc6:function(t,e,a){"use strict";var i=a("86de");var s=a.n(i);var n=s.a},b115:function(t,e,a){"use strict";var i=a("38f3");var s=a.n(i);var n=s.a},c61f:function(t,e,a){"use strict";var i=a("259b");var s=a.n(i);var n=s.a},d881:function(t,e,a){},e9cd:function(t,e,a){"use strict";var i=a("d881");var s=a.n(i);var n=s.a},f12c:function(t,e,a){},f699:function(t,e,a){"use strict";var i=a("ac1b");var s=a.n(i);var n=s.a}}]);
//# sourceMappingURL=52ec88eb.1655c511.js.map