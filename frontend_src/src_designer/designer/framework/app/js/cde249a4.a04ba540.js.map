{"version": 3, "sources": ["webpack:///./node_modules/is-buffer/index.js", "webpack:///./node_modules/load-bmfont/lib/is-binary.js", "webpack:///../app/@cape-ui/TylkoMiniature.vue?ae86", "webpack:///../app/@cape-ui/TylkoMiniature.vue", "webpack:///../app/@cape-ui/TylkoMiniature.vue?fd3c", "webpack:///../app/@cape-ui/TylkoMiniature.vue?74e7", "webpack:///./node_modules/es-abstract/es5.js", "webpack:///./node_modules/function-bind/index.js", "webpack:///./node_modules/string.prototype.trim/polyfill.js", "webpack:///./node_modules/three-bmfont-text/shaders/msdf.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/glsl-quad/glsl-quad.js", "webpack:///./node_modules/es-abstract/helpers/isNaN.js", "webpack:///./node_modules/is-callable/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.string.fontsize.js", "webpack:///./node_modules/es-to-primitive/es5.js", "webpack:///./node_modules/object-assign/index.js", "webpack:///./node_modules/flatten-vertex-data/index.js", "webpack:///./node_modules/word-wrapper/index.js", "webpack:///./node_modules/xml-parse-from-string/index.js", "webpack:///./node_modules/es-to-primitive/helpers/isPrimitive.js", "webpack:///./node_modules/string.prototype.trim/implementation.js", "webpack:///./node_modules/an-array/index.js", "webpack:///./node_modules/es-abstract/helpers/sign.js", "webpack:///./node_modules/three-buffer-vertex-data/index.js", "webpack:///./node_modules/three-bmfont-text/lib/utils.js", "webpack:///./node_modules/parse-headers/parse-headers.js", "webpack:///./node_modules/function-bind/implementation.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoUsps.vue?7b7c", "webpack:///./node_modules/dtype/index.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/preview-page.vue?f842", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewConfiguration.vue?f70d", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewColors.vue?fb9c", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewColors.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewColors.vue?4966", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewColors.vue?0dc7", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewComponentSelect.vue?e29c", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewComponentSelect.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewComponentSelect.vue?dba2", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewComponentSelect.vue?0e38", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoAddToCart.vue?d44f", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoAddToCart.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoAddToCart.vue?1dc2", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoAddToCart.vue?0628", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewConfiguration.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewConfiguration.vue?c9ca", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewConfiguration.vue?05ce", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewControls.vue?5ee2", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewControls.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewControls.vue?37f3", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewControls.vue?54d9", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoUsps.vue?90c5", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoUsps.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoUsps.vue?8147", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoUsps.vue?f474", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoMenu.vue?450a", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoMenu.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoMenu.vue?d362", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoMenu.vue?3086", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/preview-page.vue", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/preview-page.vue?33c7", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/preview-page.vue?0b1a", "webpack:///./node_modules/three-bmfont-text/index.js", "webpack:///./node_modules/buffer-equal/index.js", "webpack:///./node_modules/parse-bmfont-ascii/index.js", "webpack:///./node_modules/is-function/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_webgl/ivy/renderer/methods/raytrace/build/index.js", "webpack:///../app/renderers/wireframe-3d/camera.js", "webpack:///../app/renderers/wireframe-3d/simple.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoMenu.vue?eab7", "webpack:///./node_modules/quad-indices/index.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewColors.vue?dba5", "webpack:///./node_modules/as-number/index.js", "webpack:///./node_modules/has/src/index.js", "webpack:///./node_modules/three-bmfont-text/lib/vertices.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/fakeTylkoUi/TylkoAddToCart.vue?2683", "webpack:///./node_modules/object-keys/implementation.js", "webpack:///./node_modules/string.prototype.trim/shim.js", "webpack:///./node_modules/es-abstract/helpers/mod.js", "webpack:///./node_modules/global/window.js", "webpack:///./node_modules/es-abstract/helpers/assertRecord.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/preview-page.vue?72d2", "webpack:///./node_modules/es-abstract/helpers/isFinite.js", "webpack:///./node_modules/parse-bmfont-binary/index.js", "webpack:///./node_modules/load-bmfont/browser.js", "webpack:///./node_modules/string.prototype.trim/index.js", "webpack:///./node_modules/for-each/index.js", "webpack:///./node_modules/object-keys/isArguments.js", "webpack:///./node_modules/object-keys/index.js", "webpack:///./node_modules/vue-click-outside/index.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewControls.vue?a80c", "webpack:///./node_modules/es-abstract/GetIntrinsic.js", "webpack:///./node_modules/xhr/index.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewConfiguration.vue?7c43", "webpack:///./node_modules/parse-bmfont-xml/lib/parse-attribs.js", "webpack:///./node_modules/layout-bmfont-text/index.js", "webpack:///./node_modules/define-properties/index.js", "webpack:///../app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/TylkoPreviewComponentSelect.vue?ea2a", "webpack:///../app/@cape-ui/TylkoMiniature.vue?44bc", "webpack:///./node_modules/parse-bmfont-xml/lib/browser.js"], "names": ["module", "exports", "obj", "<PERSON><PERSON><PERSON><PERSON>", "is<PERSON><PERSON><PERSON><PERSON>er", "_isBuffer", "constructor", "readFloatLE", "slice", "<PERSON><PERSON><PERSON>", "equal", "__webpack_require__", "HEADER", "buf", "substring", "length", "render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "ref", "staticRenderFns", "TylkoMiniaturevue_type_script_lang_js_", "props", "id", "String", "Number", "geo", "Object", "Array", "bgColor", "update", "methods", "load", "_this", "geo<PERSON><PERSON><PERSON>", "cape", "api", "componentSet", "type", "queryForMiniature", "pipe", "console", "log", "build", "fetched", "_this2", "services", "miniatures", "add", "data", "base64", "colorSet", "then", "_ref", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "result", "image", "wrap", "_callee$", "_context", "prev", "next", "$refs", "mini", "hasChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Image", "src", "painterState", "append<PERSON><PERSON><PERSON>", "stop", "_x", "apply", "arguments", "created", "watch", "mounted", "_cape_ui_TylkoMiniaturevue_type_script_lang_js_", "component", "componentNormalizer", "TylkoMiniature", "__webpack_exports__", "GetIntrinsic", "$Object", "$TypeError", "$String", "assertRecord", "$isNaN", "$isFinite", "sign", "mod", "IsCallable", "toPrimitive", "has", "ES5", "ToPrimitive", "ToBoolean", "value", "ToNumber", "ToInteger", "number", "Math", "floor", "abs", "ToInt32", "x", "ToUint32", "ToUint16", "posInt", "ToString", "ToObject", "CheckObjectCoercible", "optMessage", "SameValue", "y", "Type", "IsPropertyDescriptor", "Desc", "allowed", "[[Configurable]]", "[[Enumerable]]", "[[Get]]", "[[Set]]", "[[Value]]", "[[Writable]]", "key", "isData", "IsAccessor", "IsAccessorDescriptor", "IsDataDescriptor", "IsGenericDescriptor", "FromPropertyDescriptor", "writable", "enumerable", "configurable", "get", "set", "ToPropertyDescriptor", "<PERSON>b<PERSON>", "desc", "getter", "TypeError", "setter", "implementation", "Function", "prototype", "bind", "zeroWidthSpace", "getPolyfill", "trim", "assign", "createMSDFShader", "opt", "opacity", "alphaTest", "precision", "color", "map", "negate", "uniforms", "THREE", "Texture", "Color", "vertexShader", "join", "fragmentShader", "verts", "uvs", "indices", "vs<PERSON>er", "fshader", "showUVsFshader", "showPositions<PERSON><PERSON><PERSON>", "showPositionsFshader", "directionsDataUri", "replace", "bitmaps", "directions", "uri", "shader", "vert", "frag", "show", "positions", "isNaN", "fnToStr", "toString", "constructorRegex", "isES6ClassFn", "isES6ClassFunction", "fnStr", "call", "test", "e", "tryFunctionObject", "tryFunctionToStr", "toStr", "fnClass", "genClass", "hasToStringTag", "Symbol", "toStringTag", "isCallable", "strClass", "createHTML", "fontsize", "size", "isPrimitive", "ES5internalSlots", "[[De<PERSON><PERSON><PERSON><PERSON><PERSON>]]", "O", "actualHint", "i", "input", "getOwnPropertySymbols", "hasOwnProperty", "propIsEnumerable", "propertyIsEnumerable", "toObject", "val", "undefined", "shouldUseNative", "test1", "getOwnPropertyNames", "test2", "fromCharCode", "order2", "n", "test3", "split", "for<PERSON>ach", "letter", "keys", "err", "target", "source", "from", "to", "symbols", "s", "dtype", "flattenVertexData", "output", "offset", "isArray", "dim", "j", "k", "l", "dst<PERSON><PERSON><PERSON>", "Error", "NaN", "Ctor", "newline", "newlineChar", "whitespace", "text", "lines", "line", "start", "end", "wordwrap", "width", "mode", "MAX_VALUE", "max", "measure", "monospace", "pre", "greedy", "idxOf", "chr", "idx", "indexOf", "isWhitespace", "lineStart", "char<PERSON>t", "isNewline", "lineEnd", "measured", "push", "testWidth", "newLine", "nextStart", "glyphs", "min", "xmlparser", "self", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "str", "parser", "parseFromString", "ActiveXObject", "xmlDoc", "async", "loadXML", "div", "document", "createElement", "innerHTML", "ES", "leftWhitespace", "rightWhitespace", "S", "anArray", "arr", "BYTES_PER_ELEMENT", "buffer", "flatten", "warned", "attr", "setAttribute", "index", "setIndex", "geometry", "itemSize", "isR69", "attrib", "getAttribute", "newAttrib", "updateAttribute", "addAttribute", "rebuildAttribute", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON>y", "warn", "BufferAttribute", "needsUpdate", "array", "attribLength", "box", "bounds", "count", "computeBox", "computeSphere", "minX", "minY", "maxX", "maxY", "height", "sqrt", "center", "radius", "arg", "headers", "row", "toLowerCase", "ERROR_MESSAGE", "funcType", "that", "args", "bound", "binder", "concat", "<PERSON><PERSON><PERSON><PERSON>", "boundArgs", "Empty", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoUsps_vue_vue_type_style_index_0_id_96c8332e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoUsps_vue_vue_type_style_index_0_id_96c8332e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default", "_unused_webpack_default_export", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "Uint8ClampedArray", "$route", "name", "attrs", "isVisible", "visibleUspsText", "_e", "on", "mouseover", "rendererMouseOver", "mouseleave", "rendererMouseLeave", "loaderHidden", "_l", "position", "no", "style", "class", "configuration", "previewSelect", "selectedTab", "staticStyle", "z-index", "hoverElement", "round", "dense", "icon", "nativeOn", "$event", "selectOnLeave", "selectOnEnter", "mousedown", "select", "_v", "_s", "comp", "point", "showAllCompartmentsButton", "decoderType", "element", "grabButtons", "objectLine", "choosenColor", "decoder<PERSON><PERSON><PERSON>", "decoderHeight", "decoderOutput", "distortion", "density", "depth", "plinth", "mappedObjects", "price", "locked", "previousStateAvailble", "nextStateAvailble", "parametersStatesStatus", "TylkoPreviewConfigurationvue_type_template_id_eb219b22_lang_pug_render", "badania", "isConfigurator", "font-size", "line-height", "params", "label", "click", "resetToInitialState", "saveState", "disable", "undo", "redo", "markers", "snap", "step", "setState", "model", "callback", "$$v", "currentParametersHistoryState", "expression", "updateParam", "choosenLocked", "tag", "rounded", "icon-right", "widthRange", "label-always", "label-value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateConfigurator", "heightRange", "choosenHeight", "additional_height", "computeHeight", "lazyAutoSaveState", "showRendererElements", "heightIndex", "includes", "distortionMode", "choosenDistortion", "densityMode", "choosenDensity", "densityOptions", "padding-top", "findIndex", "find", "float", "active", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "no-ripple", "choosenPlinth", "setupID", "components", "preview_mode", "thumbnails", "TylkoPreviewConfigurationvue_type_template_id_eb219b22_lang_pug_staticRenderFns", "TylkoPreviewColorsvue_type_template_id_12588e0a_lang_pug_render", "changeColor", "imgPath", "alt", "TylkoPreviewColorsvue_type_template_id_12588e0a_lang_pug_staticRenderFns", "ASSETS_PATH", "window", "location", "href", "TylkoPreviewColorsvue_type_script_lang_js_", "application", "bus", "$emit", "colors", "cape_preview_configurator_TylkoPreviewColorsvue_type_script_lang_js_", "TylkoPreviewColors", "TylkoPreviewComponentSelectvue_type_template_id_41b06fba_lang_pug_render", "config", "channel_id", "table_id", "series_id", "component_id", "inverted", "slot", "nodes", "setupsTreeNodes", "node-key", "default-expand-all", "triggerPrevSelected", "clearPrevSelectedTimeout", "options", "optionsError", "option", "hash", "dispatchToConfiguration", "autoSaveState", "bg-color", "thumbnail", "m_config_id", "mouseup", "$set", "display", "door_flippable", "margin-top", "true-value", "false-value", "cables_available", "cables", "door_flip", "mapEdges", "line_left", "v_min", "v_max", "startDrag", "line_right", "TylkoPreviewComponentSelectvue_type_template_id_41b06fba_lang_pug_staticRenderFns", "uuidv4", "isObject", "typeof_default", "TylkoPreviewComponentSelectvue_type_script_lang_js_", "edge", "event", "dragging", "startX", "clientX", "startY", "clientY", "state", "startOffset", "z", "drag", "dragPoint", "currentOffserProjectionReverse", "stopDrag", "H", "lib_default", "h32", "digest", "<PERSON><PERSON><PERSON>", "lodash_default", "debounce", "setupsTree", "mapObject", "objToDispatch", "toConsumableArray_default", "children", "some", "generateThumbs", "setups", "channels", "lines_data", "tab", "objectSpread_default", "defineProperty_default", "_arr", "entries", "_i", "_arr$_i", "slicedToArray_default", "line_name", "line_data", "generateThumbnails", "thumbsForChannel", "channel", "lazyGenerateThumbanils", "processGeometry", "_this3", "thumbnailsStore", "_ref2", "rest", "objectWithoutProperties_default", "_ref3", "series_name", "_components", "_ref4", "x1", "x2", "mesh", "_this4", "$on", "payload", "addEventListener", "cape_preview_configurator_TylkoPreviewComponentSelectvue_type_script_lang_js_", "TylkoPreviewComponentSelect_component", "TylkoPreviewComponentSelect", "TylkoAddToCartvue_type_template_id_faeb3fda_scoped_true_render", "_m", "TylkoAddToCartvue_type_template_id_faeb3fda_scoped_true_staticRenderFns", "TylkoAddToCartvue_type_script_lang_js_", "fakeTylkoUi_TylkoAddToCartvue_type_script_lang_js_", "TylkoAddToCart_component", "TylkoAddToCart", "TylkoPreviewConfigurationvue_type_script_lang_js_", "clearTimeout", "rendererTimeout", "setTimeout", "param", "lazy", "throttle", "v", "setup_id", "density_options", "dim_x", "dim_y", "density_mode", "distortion_mode", "cape_preview_configurator_TylkoPreviewConfigurationvue_type_script_lang_js_", "TylkoPreviewConfiguration_component", "TylkoPreviewConfiguration", "TylkoPreviewControlsvue_type_template_id_e255c32e_lang_pug_render", "showHideCompartments", "anchor", "resetChanges", "TylkoPreviewControlsvue_type_template_id_e255c32e_lang_pug_staticRenderFns", "TylkoPreviewControlsvue_type_script_lang_js_", "empty", "cape_preview_configurator_TylkoPreviewControlsvue_type_script_lang_js_", "TylkoPreviewControls_component", "TylkoPreviewControls", "TylkoUspsvue_type_template_id_96c8332e_scoped_true_render", "data-scroll", "visible", "viewBox", "version", "xmlns", "xmlns:xlink", "stroke", "stroke-width", "fill", "fill-rule", "points", "TylkoUspsvue_type_template_id_96c8332e_scoped_true_staticRenderFns", "data-tooltip-trigger", "TylkoUspsvue_type_script_lang_js_", "fakeTylkoUi_TylkoUspsvue_type_script_lang_js_", "TylkoUsps_component", "TylkoUsps", "TylkoMenuvue_type_template_id_6ac1de8d_scoped_true_render", "TylkoMenuvue_type_template_id_6ac1de8d_scoped_true_staticRenderFns", "TylkoMenuvue_type_script_lang_js_", "fakeTylkoUi_TylkoMenuvue_type_script_lang_js_", "TylkoMenu_component", "TylkoMenu", "preview_pagevue_type_script_lang_js_", "innitialize", "startingParams", "prop", "_prop$split", "_prop$split2", "saveCurrentPreviewParametersState", "loadFromStorage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getElementsByClassName", "showCompartments", "hideCompartments", "rendererContainer", "classList", "remove", "timeoutH<PERSON>le", "prevSelected", "configuratorCustomParams", "selectedSetupTab", "elements", "updatePreviewStore", "switchElements", "save", "previewParametersStates", "updateStatesAvailbility", "loadPreviewParametersState", "linearHistory", "loadPreviousPreviewParametersState", "loadNextPreviewParametersState", "stateIndex", "currentPreviewParametersState", "edgesToScreen", "mapCompartments", "showAllCompartments", "visibleUsps", "arrageComponentsPopovers", "boxList", "objectToScreen", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "dom<PERSON>lement", "cursor", "buildResizers", "rendererLeaveTimeout", "previewSetComponent", "mouseMoveSetComponent", "mouseMoveOnLeave", "deselectComponent", "deselect", "compartmentsToScreen", "object", "canvas", "w", "parseFloat", "h", "Vector3", "applyAxisAngle", "project", "camera", "reverseForRaycasting", "initialRotationRad", "direction", "unproject", "sub", "normalize", "ray", "<PERSON>", "closestPointToPoint", "decoderId", "settings", "setObjectItem", "json", "superior_object_line", "currentGeometry", "startsWith", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "tmpData", "dnaToolsWD", "serialization", "meshMode", "mesh_setup", "geom_id", "geom_type", "generate_thumbnails", "configurator_custom_params", "format", "selected_config_id", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "_iterator", "iterator", "_step", "done", "item", "selected", "return", "oldWarnFunction", "displayShelf", "preview<PERSON><PERSON><PERSON>", "scene", "faceBox", "facePlane", "getPrice", "getIndicatorBoxesPositions", "initialPreviewParametersState", "stateToLoad", "replace_index", "paramSet", "splice", "_this5", "$el", "querySelector", "webdesigner", "productionRenderer", "simple", "container", "vwidth", "vheight", "cameraLock", "updatedCameraCallback", "updateResizers", "resetScene", "setScene", "clearScene", "setBackgroundScene", "_scene", "getBoundingClientRect", "resize", "setDesignerMode", "widths", "heights", "compartments", "compartmentsWidths", "compartmentsHeights", "bool", "rendererWapper", "$router", "_this6", "_this$$route$params", "componentSetup", "subscribe", "resp", "title", "left", "right", "innerWidth", "innerHeight", "solidMode", "cape_preview_configurator_preview_pagevue_type_script_lang_js_", "preview_page_component", "preview_page", "createLayout", "inherits", "createIndices", "vertices", "utils", "Base", "BufferGeometry", "createTextGeometry", "TextGeometry", "_opt", "font", "layout", "flipY", "tex<PERSON><PERSON><PERSON>", "common", "scaleW", "texHeight", "scaleH", "filter", "glyph", "bitmap", "visibleGlyphs", "clockwise", "multipage", "attributes", "removeAttribute", "pages", "computeBoundingSphere", "boundingSphere", "Sphere", "error", "computeBoundingBox", "boundingBox", "Box3", "bbox", "makeEmpty", "b", "equals", "parseBMFontAscii", "chars", "kernings", "lineData", "splitLine", "file", "space", "match", "dt", "parseData", "out", "parseIntList", "isFunction", "fn", "string", "alert", "confirm", "prompt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsRaymarcherFast", "shadowsRaymarcherCasterOtho", "shadowsRaymarcherCasterOthoTop", "MAX_IDENTS", "inverseProjectionMatrix", "Matrix4", "worldToLocal", "emptyM4", "mappedProjection", "softShadowsTexturePass", "canvasElement", "isTopProjection", "cameraObject", "build3d", "blend", "enable", "func", "srcRGB", "srcAlpha", "dstRGB", "dstAlpha", "equation", "rgb", "alpha", "selfSize", "ivy", "getHeight", "rl", "regl", "uniformsIdents", "cameraPosition", "shelfGap", "shelfSize", "shadowSource", "<PERSON><PERSON><PERSON>er", "warp", "castFlatShadows", "quad", "clear", "idents", "getIndents", "parsedIdents", "power", "cameraz", "warpx", "warpy", "updateIndents", "updateSize", "require", "TrackballControls", "lock", "STATE", "NONE", "ROTATE", "ZOOM", "PAN", "TOUCH_ROTATE", "TOUCH_ZOOM_PAN", "enabled", "screen", "top", "rotateSpeed", "zoomSpeed", "panSpeed", "noRotate", "noZoom", "noPan", "staticMoving", "dynamicDampingFactor", "minDistance", "maxDistance", "Infinity", "EPS", "lastPosition", "_state", "_prevState", "_eye", "_movePrev", "Vector2", "_move<PERSON>urr", "_lastAxis", "_lastAngle", "_zoomStart", "_zoomEnd", "_touchZoomDistanceStart", "_touchZoomDistanceEnd", "_panStart", "_panEnd", "target0", "clone", "position0", "up0", "up", "changeEvent", "startEvent", "endEvent", "handleResize", "d", "ownerDocument", "documentElement", "pageXOffset", "clientLeft", "pageYOffset", "clientTop", "handleEvent", "getMouseOnScreen", "vector", "pageX", "pageY", "getMouseOnCircle", "rotateCamera", "axis", "quaternion", "Quaternion", "eyeDirection", "objectUpDirection", "objectSidewaysDirection", "moveDirection", "angle", "deltaAxis", "moveDirectionCopy", "maxA", "breakIt", "angleInDeg", "PI", "copy", "crossVectors", "<PERSON><PERSON><PERSON><PERSON>", "setFromAxisAngle", "applyQuaternion", "zoomCamera", "factor", "multiplyScalar", "panCamera", "mouseChange", "objectUp", "pan", "lengthSq", "cross", "subVectors", "checkDistances", "addVectors", "lookAt", "distanceToSquared", "dispatchEvent", "reset", "<PERSON><PERSON><PERSON>", "keydown", "removeEventListener", "keyCode", "keyup", "preventDefault", "stopPropagation", "button", "mousemove", "mousewheel", "deltaMode", "deltaY", "touchstart", "touches", "dx", "dy", "touchmove", "touchend", "contextmenu", "dispose", "preventEvent", "create", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createGeometry", "loadFont", "MSDFShader", "ConfiguratorData", "horizontals", "verticals", "supports", "backs", "doors", "drawers", "fills", "legs", "accessories", "spacer", "colorMode", "filterMaterialKey", "filterMaterial", "filterEnable", "conf", "ProductionRenderer", "classCallCheck_default", "controls", "init", "updatedCameraCallbackFunction", "aspect", "updateProjectionMatrix", "setSize", "opaque", "solid", "flush", "filtered_elements", "filterElements", "drawElements", "resetItems", "textureLoader", "Promise", "resolve", "align", "texture", "material", "side", "transparent", "scale", "setFromObject", "getCenter", "getSize", "maxDim", "fov", "cameraZ", "tan", "minZ", "cameraToFarEdge", "far", "offsetHeight", "canvasAbsoluteHeight", "offsetWidth", "canvasAbsoluteWidth", "zoom", "updateMatrix", "antialias", "preserveDrawingBuffer", "setPixelRatio", "devicePixelRatio", "updatedCamera", "background", "gridHelper", "rotation", "items", "labels", "animate", "requestAnimationFrame", "filter_conf", "object3D", "traverse", "obj3D", "union", "configurationId", "groups", "g", "c", "_loop", "main_item", "centers", "x_domain", "y_domain", "z_domain", "sizes", "values", "elem_type", "wireframe", "polygonOffset", "polygonOffsetFactor", "polygonOffsetUnits", "cube", "item_data", "mat", "linewidth", "c_config_id", "group", "pos", "surname", "spritey", "makeTextSprite", "borderColor", "r", "backgroundColor", "addInfo", "_ret", "materialSelect", "bb", "getCompoundBoundingBox", "bbExpanded", "sizeExpanded", "expandByScalar", "geometrySelect", "cubeSelect", "whiteBackground", "message", "parameters", "fontface", "borderThickness", "context", "getContext", "metrics", "measureText", "textWidth", "fillStyle", "strokeStyle", "lineWidth", "fillText", "spriteMaterial", "sprite", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoMenu_vue_vue_type_style_index_0_id_6ac1de8d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoMenu_vue_vue_type_style_index_0_id_6ac1de8d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default", "CW", "CCW", "createQuadElements", "dir", "numIndices", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewColors_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewColors_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "numtype", "num", "def", "page", "bw", "bh", "u0", "v1", "u1", "v0", "xoffset", "yoffset", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoAddToCart_vue_vue_type_style_index_0_id_faeb3fda_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoAddToCart_vue_vue_type_style_index_0_id_faeb3fda_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default", "<PERSON><PERSON><PERSON>", "is<PERSON><PERSON><PERSON>", "isEnumerable", "hasDontEnumBug", "hasProtoEnumBug", "dontEnums", "equalsConstructorPrototype", "o", "ctor", "<PERSON><PERSON><PERSON><PERSON>", "$applicationCache", "$console", "$external", "$frame", "$frameElement", "$frames", "$innerHeight", "$innerWidth", "$outerHeight", "$outerWidth", "$pageXOffset", "$pageYOffset", "$parent", "$scrollLeft", "$scrollTop", "$scrollX", "$scrollY", "$self", "$webkitIndexedDB", "$webkitStorageInfo", "$window", "hasAutomationEqualityBug", "equalsConstructorPrototypeIfNotBuggy", "isArguments", "isString", "theKeys", "<PERSON><PERSON><PERSON><PERSON>", "skipConstructor", "define", "shimStringTrim", "polyfill", "modulo", "remain", "global", "win", "$SyntaxError", "predicates", "Property Descriptor", "isPropertyDescriptor", "recordType", "argumentName", "predicate", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_preview_page_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_preview_page_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "isFinite", "readBMFontBinary", "header", "every", "byte", "readUInt8", "vers", "readBlock", "blockID", "blockSize", "readInt32LE", "info", "readInfo", "readCommon", "readPages", "readChars", "readK<PERSON><PERSON>", "readInt16LE", "bitField", "smooth", "unicode", "italic", "bold", "fixedHeight", "charset", "stretchH", "readUInt16LE", "aa", "padding", "readInt8", "spacing", "outline", "face", "readStringNT", "lineHeight", "base", "packed", "alphaChnl", "redChnl", "greenChnl", "blueChnl", "readNameNT", "len", "char", "off", "readUInt32LE", "xadvance", "chnl", "kern", "first", "second", "amount", "xhr", "noop", "parseASCII", "parseXML", "readBinary", "isBinaryFormat", "xtend", "xml2", "hasXML2", "XMLHttpRequest", "cb", "expectBinary", "binary", "getBinaryOpts", "res", "body", "statusCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encoding", "JSON", "parse", "responseType", "req", "overrideMimeType", "shim", "boundTrim", "forEachArray", "receiver", "forEachString", "forEachObject", "list", "thisArg", "callee", "orig<PERSON>eys", "originalKeys", "shimObjectKeys", "keysWorksWithArguments", "validate", "binding", "isPopup", "popupItem", "contains", "isServer", "vNode", "componentInstance", "$isServer", "el", "handler", "path", "<PERSON><PERSON><PERSON>", "unshift", "__vueClickOutside__", "unbind", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewControls_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewControls_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "ThrowTypeError", "getOwnPropertyDescriptor", "hasSymbols", "getProto", "getPrototypeOf", "__proto__", "generator", "generatorFunction", "asyncFn", "asyncFunction", "asyncGen", "asyncGenFunction", "asyncGenIterator", "TypedArray", "INTRINSICS", "$ %Array%", "$ %ArrayBuffer%", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$ %ArrayBufferPrototype%", "$ %ArrayIteratorPrototype%", "$ %ArrayPrototype%", "$ %ArrayProto_entries%", "$ %ArrayProto_forEach%", "$ %ArrayProto_keys%", "$ %ArrayProto_values%", "$ %AsyncFromSyncIteratorPrototype%", "$ %AsyncFunction%", "$ %AsyncFunctionPrototype%", "$ %AsyncGenerator%", "$ %AsyncGeneratorFunction%", "$ %AsyncGeneratorPrototype%", "$ %AsyncIteratorPrototype%", "asyncIterator", "$ %Atomics%", "Atomics", "$ %Boolean%", "Boolean", "$ %BooleanPrototype%", "$ %DataView%", "DataView", "$ %DataViewPrototype%", "$ %Date%", "Date", "$ %DatePrototype%", "$ %decodeURI%", "decodeURI", "$ %decodeURIComponent%", "decodeURIComponent", "$ %encodeURI%", "encodeURI", "$ %encodeURIComponent%", "encodeURIComponent", "$ %Error%", "$ %ErrorPrototype%", "$ %eval%", "eval", "$ %EvalError%", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$ %EvalErrorPrototype%", "$ %Float32Array%", "$ %Float32ArrayPrototype%", "$ %Float64Array%", "$ %Float64ArrayPrototype%", "$ %Function%", "$ %FunctionPrototype%", "$ %Generator%", "$ %GeneratorFunction%", "$ %GeneratorPrototype%", "$ %Int8Array%", "$ %Int8ArrayPrototype%", "$ %Int16Array%", "$ %Int16ArrayPrototype%", "$ %Int32Array%", "$ %Int32ArrayPrototype%", "$ %isFinite%", "$ %isNaN%", "$ %IteratorPrototype%", "$ %JSON%", "$ %JSONParse%", "$ %Map%", "Map", "$ %MapIteratorPrototype%", "$ %MapPrototype%", "$ %Math%", "$ %Number%", "$ %NumberPrototype%", "$ %Object%", "$ %ObjectPrototype%", "$ %ObjProto_toString%", "$ %ObjProto_valueOf%", "valueOf", "$ %parseFloat%", "$ %parseInt%", "$ %Promise%", "$ %PromisePrototype%", "$ %PromiseProto_then%", "$ %Promise_all%", "all", "$ %Promise_reject%", "reject", "$ %Promise_resolve%", "$ %Proxy%", "Proxy", "$ %RangeError%", "RangeError", "$ %RangeErrorPrototype%", "$ %ReferenceError%", "ReferenceError", "$ %ReferenceErrorPrototype%", "$ %Reflect%", "Reflect", "$ %RegExp%", "RegExp", "$ %RegExpPrototype%", "$ %Set%", "Set", "$ %SetIteratorPrototype%", "$ %SetPrototype%", "$ %SharedArrayBuffer%", "SharedArrayBuffer", "$ %SharedArrayBufferPrototype%", "$ %String%", "$ %StringIteratorPrototype%", "$ %StringPrototype%", "$ %Symbol%", "$ %SymbolPrototype%", "$ %SyntaxError%", "SyntaxError", "$ %SyntaxErrorPrototype%", "$ %ThrowTypeError%", "$ %TypedArray%", "$ %TypedArrayPrototype%", "$ %TypeError%", "$ %TypeErrorPrototype%", "$ %Uint8Array%", "$ %Uint8ArrayPrototype%", "$ %Uint8ClampedArray%", "$ %Uint8ClampedArrayPrototype%", "$ %Uint16Array%", "$ %Uint16ArrayPrototype%", "$ %Uint32Array%", "$ %Uint32ArrayPrototype%", "$ %URIError%", "URIError", "$ %URIErrorPrototype%", "$ %WeakMap%", "WeakMap", "$ %WeakMapPrototype%", "$ %WeakSet%", "WeakSet", "$ %WeakSetPrototype%", "allowMissing", "parseHeaders", "createXHR", "default", "XDomainRequest", "method", "initParams", "toUpperCase", "_createXHR", "isEmpty", "called", "cbOnce", "response", "readystatechange", "readyState", "loadFunc", "getBody", "responseText", "getXml", "isJson", "errorFunc", "evt", "timeoutTimer", "failureResponse", "aborted", "status", "useXDR", "url", "rawRequest", "getAllResponseHeaders", "cors", "sync", "stringify", "onreadystatechange", "onload", "onerror", "onprogress", "<PERSON>ab<PERSON>", "ontimeout", "open", "username", "password", "withCredentials", "timeout", "abort", "code", "setRequestHeader", "beforeSend", "send", "responseXML", "firefoxBugTakenEffect", "nodeName", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewConfiguration_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewConfiguration_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "GLYPH_DESIGNER_ERROR", "parseAttributes", "wordWrap", "X_HEIGHTS", "M_WIDTHS", "CAP_HEIGHTS", "TAB_ID", "charCodeAt", "SPACE_ID", "ALIGN_LEFT", "ALIGN_CENTER", "ALIGN_RIGHT", "TextLayout", "_measure", "computeMetrics", "tabSize", "_setupSpaceGlyphs", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "reduce", "baseline", "descender", "letterSpacing", "getAlignType", "_width", "_height", "_descender", "_baseline", "_xHeight", "getXHeight", "_capHeight", "getCapHeight", "_lineHeight", "_ascender", "lineIndex", "lastGlyph", "getGlyph", "getKerning", "tx", "_linesTotal", "_fallbackSpaceGlyph", "_fallbackTabGlyph", "getGlyphById", "getMGlyph", "tabWidth", "cur<PERSON>en", "cur<PERSON><PERSON>th", "xoff", "nextPen", "nextWidth", "addGetter", "defineProperty", "wrapper", "glyphIdx", "findChar", "table", "origDefineProperty", "arePropertyDescriptorsSupported", "_", "supportsDescriptors", "defineProperties", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewComponentSelect_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoPreviewComponentSelect_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoMiniature_vue_vue_type_style_index_0_id_360dcb88_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoMiniature_vue_vue_type_style_index_0_id_360dcb88_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default", "NAME_MAP", "scaleh", "scalew", "stretchh", "lineheight", "alphachnl", "redchnl", "greenchnl", "bluechnl", "xmlRoot", "getElementsByTagName", "getAttribs", "pageRoot", "p", "childTag", "child", "attribs", "getAttribList", "dict", "mapName", "nodeValue"], "mappings": ";;;;;;;AASAA,EAAAC,QAAA,SAAAC,GACA,OAAAA,GAAA,OAAAC,EAAAD,IAAAE,EAAAF,QAAAG,YAGA,SAAAF,EAAAD,GACA,QAAAA,EAAAI,oBAAAJ,EAAAI,YAAAH,WAAA,YAAAD,EAAAI,YAAAH,SAAAD,GAIA,SAAAE,EAAAF,GACA,cAAAA,EAAAK,cAAA,mBAAAL,EAAAM,QAAA,YAAAL,EAAAD,EAAAM,MAAA,gCCnBA,SAAAC,GAAA,IAAAC,EAAYC,EAAQ,QACpB,IAAAC,EAAA,IAAAH,EAAA,cAEAT,EAAAC,QAAA,SAAAY,GACA,UAAAA,IAAA,SACA,OAAAA,EAAAC,UAAA,aACA,OAAAD,EAAAE,OAAA,GAAAL,EAAAG,EAAAL,MAAA,KAAAI,uECNA,IAAAI,EAAA,WAA0B,IAAAC,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,aAAwB,CAAAF,EAAA,OAAYG,IAAA,OAAAD,YAAA,YAC9I,IAAAE,EAAA,6GCMA,IAAAC,EAAA,CACAC,MAAA,CACAC,GAAA,CAAAC,OAAAC,QACAC,IAAA,CAAAF,OAAAG,OAAAC,OACAC,QAAA,CAAAL,QACAM,OAAA,CAAAN,SAGAO,QAAA,CACAC,KADA,SAAAA,EACAT,GAAA,IAAAU,EAAApB,KACA,IAAAqB,EAAAC,EAAA,KAAAC,IAAAV,IAAAW,aAAA,CACAC,KAAA,qBACAf,KAEAgB,kBAAA,OAEAL,EAAAM,KACA,SAAAd,GACAe,QAAAC,IAAAhB,GACAO,EAAAU,MAAAjB,EAAAH,EAAA,QAEA,qBAIAoB,MAjBA,SAAAA,EAiBAjB,EAAAH,EAAAqB,GAAA,IAAAC,EAAAhC,KACAsB,EAAA,KAAAW,SAAAC,WACAC,IAAA,CACAC,KAAAvB,EACAH,KACAqB,UACAM,OAAA,KACAC,SAAA,aACAtB,QAAAhB,KAAAgB,UAEAuB,KATA,eAAAC,EAAAC,IAAQC,EAAAC,EAAAC,KASR,SAAAC,EAAAC,GAAA,IAAAC,EAAA,OAAAL,EAAAC,EAAAK,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OACA,MAAApB,EAAAqB,MAAAC,MAAAtB,EAAAqB,MAAAC,KAAAC,gBAAA,CACAvB,EAAAqB,MAAAC,KAAAE,YAAAxB,EAAAqB,MAAAC,KAAAG,YACAV,EAAA,IAAAW,MACAX,EAAAY,IAAAb,EAAAc,aACA5B,EAAAqB,MAAAC,KAAAO,YAAAd,GALA,wBAAAG,EAAAY,UAAAjB,MATA,gBAAAkB,GAAA,OAAAvB,EAAAwB,MAAAhE,KAAAiE,YAAA,MAmBAC,QA7CA,SAAAA,MA+CAC,MAAA,CACAtD,IADA,SAAAA,IAEAb,KAAA8B,MAAA9B,KAAAa,IAAAb,KAAAU,GAAA,QAIA0D,QArDA,SAAAA,IAsDA,GAAApE,KAAAa,IAAA,CACAb,KAAA8B,MAAA9B,KAAAa,IAAAb,KAAAU,GAAA,MACA,OAEA,GAAAV,KAAAU,GAAA,CACAV,KAAAmB,KAAAnB,KAAAU,OClE4N,IAAA2D,EAAA,kCCQ5N,IAAAC,EAAgBxD,OAAAyD,EAAA,KAAAzD,CACduD,EACAvE,EACAS,EACF,MACA,KACA,WACA,MAIe,IAAAiE,EAAAC,EAAA,KAAAH,+CCjBf,IAAAI,EAAmBjF,EAAQ,QAE3B,IAAAkF,EAAAD,EAAA,YACA,IAAAE,EAAAF,EAAA,eACA,IAAAG,EAAAH,EAAA,YAEA,IAAAI,EAAmBrF,EAAQ,QAC3B,IAAAsF,EAAatF,EAAQ,QACrB,IAAAuF,EAAgBvF,EAAQ,QAExB,IAAAwF,EAAWxF,EAAQ,QACnB,IAAAyF,EAAUzF,EAAQ,QAElB,IAAA0F,EAAiB1F,EAAQ,QACzB,IAAA2F,EAAkB3F,EAAQ,QAE1B,IAAA4F,EAAU5F,EAAQ,QAGlB,IAAA6F,EAAA,CACAC,YAAAH,EAEAI,UAAA,SAAAA,EAAAC,GACA,QAAAA,GAEAC,SAAA,SAAAA,EAAAD,GACA,OAAAA,GAEAE,UAAA,SAAAA,EAAAF,GACA,IAAAG,EAAA5F,KAAA0F,SAAAD,GACA,GAAAV,EAAAa,GAAA,CAAuB,SACvB,GAAAA,IAAA,IAAAZ,EAAAY,GAAA,CAA2C,OAAAA,EAC3C,OAAAX,EAAAW,GAAAC,KAAAC,MAAAD,KAAAE,IAAAH,KAEAI,QAAA,SAAAA,EAAAC,GACA,OAAAjG,KAAA0F,SAAAO,IAAA,GAEAC,SAAA,SAAAA,EAAAD,GACA,OAAAjG,KAAA0F,SAAAO,KAAA,GAEAE,SAAA,SAAAA,EAAAV,GACA,IAAAG,EAAA5F,KAAA0F,SAAAD,GACA,GAAAV,EAAAa,QAAA,IAAAZ,EAAAY,GAAA,CAA6D,SAC7D,IAAAQ,EAAAnB,EAAAW,GAAAC,KAAAC,MAAAD,KAAAE,IAAAH,IACA,OAAAV,EAAAkB,EAAA,QAEAC,SAAA,SAAAA,EAAAZ,GACA,OAAAZ,EAAAY,IAEAa,SAAA,SAAAA,EAAAb,GACAzF,KAAAuG,qBAAAd,GACA,OAAAd,EAAAc,IAEAc,qBAAA,SAAAA,EAAAd,EAAAe,GAEA,GAAAf,GAAA,MACA,UAAAb,EAAA4B,GAAA,yBAAAf,GAEA,OAAAA,GAEAN,aACAsB,UAAA,SAAAA,EAAAR,EAAAS,GACA,GAAAT,IAAAS,EAAA,CACA,GAAAT,IAAA,GAAiB,SAAAA,IAAA,EAAAS,EACjB,YAEA,OAAA3B,EAAAkB,IAAAlB,EAAA2B,IAIAC,KAAA,SAAAA,EAAAV,GACA,GAAAA,IAAA,MACA,aAEA,UAAAA,IAAA,aACA,kBAEA,UAAAA,IAAA,mBAAAA,IAAA,UACA,eAEA,UAAAA,IAAA,UACA,eAEA,UAAAA,IAAA,WACA,gBAEA,UAAAA,IAAA,UACA,iBAKAW,qBAAA,SAAAA,EAAAC,GACA,GAAA7G,KAAA2G,KAAAE,KAAA,UACA,aAEA,IAAAC,EAAA,CACAC,mBAAA,KACAC,iBAAA,KACAC,UAAA,KACAC,UAAA,KACAC,YAAA,KACAC,eAAA,MAGA,QAAAC,KAAAR,EAAA,CACA,GAAAxB,EAAAwB,EAAAQ,KAAAP,EAAAO,GAAA,CACA,cAIA,IAAAC,EAAAjC,EAAAwB,EAAA,aACA,IAAAU,EAAAlC,EAAAwB,EAAA,YAAAxB,EAAAwB,EAAA,WACA,GAAAS,GAAAC,EAAA,CACA,UAAA3C,EAAA,sEAEA,aAIA4C,qBAAA,SAAAA,EAAAX,GACA,UAAAA,IAAA,aACA,aAGA/B,EAAA9E,KAAA,6BAAA6G,GAEA,IAAAxB,EAAAwB,EAAA,aAAAxB,EAAAwB,EAAA,YACA,aAGA,aAIAY,iBAAA,SAAAA,EAAAZ,GACA,UAAAA,IAAA,aACA,aAGA/B,EAAA9E,KAAA,6BAAA6G,GAEA,IAAAxB,EAAAwB,EAAA,eAAAxB,EAAAwB,EAAA,iBACA,aAGA,aAIAa,oBAAA,SAAAA,EAAAb,GACA,UAAAA,IAAA,aACA,aAGA/B,EAAA9E,KAAA,6BAAA6G,GAEA,IAAA7G,KAAAwH,qBAAAX,KAAA7G,KAAAyH,iBAAAZ,GAAA,CACA,YAGA,cAIAc,uBAAA,SAAAA,EAAAd,GACA,UAAAA,IAAA,aACA,OAAAA,EAGA/B,EAAA9E,KAAA,6BAAA6G,GAEA,GAAA7G,KAAAyH,iBAAAZ,GAAA,CACA,OACApB,MAAAoB,EAAA,aACAe,WAAAf,EAAA,gBACAgB,aAAAhB,EAAA,kBACAiB,eAAAjB,EAAA,0BAEG,GAAA7G,KAAAwH,qBAAAX,GAAA,CACH,OACAkB,IAAAlB,EAAA,WACAmB,IAAAnB,EAAA,WACAgB,aAAAhB,EAAA,kBACAiB,eAAAjB,EAAA,yBAEG,CACH,UAAAjC,EAAA,sFAKAqD,qBAAA,SAAAA,EAAAC,GACA,GAAAlI,KAAA2G,KAAAuB,KAAA,UACA,UAAAtD,EAAA,2CAGA,IAAAuD,EAAA,GACA,GAAA9C,EAAA6C,EAAA,eACAC,EAAA,kBAAAnI,KAAAwF,UAAA0C,EAAAL,YAEA,GAAAxC,EAAA6C,EAAA,iBACAC,EAAA,oBAAAnI,KAAAwF,UAAA0C,EAAAJ,cAEA,GAAAzC,EAAA6C,EAAA,UACAC,EAAA,aAAAD,EAAAzC,MAEA,GAAAJ,EAAA6C,EAAA,aACAC,EAAA,gBAAAnI,KAAAwF,UAAA0C,EAAAN,UAEA,GAAAvC,EAAA6C,EAAA,QACA,IAAAE,EAAAF,EAAAH,IACA,UAAAK,IAAA,cAAApI,KAAAmF,WAAAiD,GAAA,CACA,UAAAC,UAAA,6BAEAF,EAAA,WAAAC,EAEA,GAAA/C,EAAA6C,EAAA,QACA,IAAAI,EAAAJ,EAAAF,IACA,UAAAM,IAAA,cAAAtI,KAAAmF,WAAAmD,GAAA,CACA,UAAA1D,EAAA,6BAEAuD,EAAA,WAAAG,EAGA,IAAAjD,EAAA8C,EAAA,YAAA9C,EAAA8C,EAAA,cAAA9C,EAAA8C,EAAA,cAAA9C,EAAA8C,EAAA,kBACA,UAAAvD,EAAA,gGAEA,OAAAuD,IAIArJ,EAAAC,QAAAuG,uCCxOA,IAAAiD,EAAqB9I,EAAQ,QAE7BX,EAAAC,QAAAyJ,SAAAC,UAAAC,MAAAH,uCCFA,IAAAA,EAAqB9I,EAAQ,QAE7B,IAAAkJ,EAAA,IAEA7J,EAAAC,QAAA,SAAA6J,IACA,GAAAjI,OAAA8H,UAAAI,MAAAF,EAAAE,SAAAF,EAAA,CACA,OAAAhI,OAAA8H,UAAAI,KAEA,OAAAN,2BCVA,IAAAO,EAAarJ,EAAQ,QAErBX,EAAAC,QAAA,SAAAgK,EAAAC,GACAA,KAAA,GACA,IAAAC,SAAAD,EAAAC,UAAA,SAAAD,EAAAC,QAAA,EACA,IAAAC,SAAAF,EAAAE,YAAA,SAAAF,EAAAE,UAAA,KACA,IAAAC,EAAAH,EAAAG,WAAA,QACA,IAAAC,EAAAJ,EAAAI,MACA,IAAAC,EAAAL,EAAAK,IACA,IAAAC,SAAAN,EAAAM,SAAA,UAAAN,EAAAM,OAAA,YAGAN,EAAAK,WACAL,EAAAI,aACAJ,EAAAG,iBACAH,EAAAC,eACAD,EAAAM,OAEA,OAAAR,EAAA,CACAS,SAAA,CACAN,QAAA,CAAgBxH,KAAA,IAAAgE,MAAAwD,GAChBI,IAAA,CAAY5H,KAAA,IAAAgE,MAAA4D,GAAA,IAAAG,MAAAC,SACZL,MAAA,CAAc3H,KAAA,IAAAgE,MAAA,IAAA+D,MAAAE,MAAAN,KAEdO,aAAA,CACA,qBACA,2BACA,iCACA,gCACA,oBACA,gBACA,YACA,+DACA,KACAC,KAAA,MACAC,eAAA,CACA,qCACA,kDACA,SACA,aAAAV,EAAA,UACA,yBACA,sBACA,yBACA,oBAEA,4CACA,8CACA,IAEA,gBACA,oBAAAG,EAAA,wCACA,gEACA,kEACA,qDACAJ,IAAA,EACA,GACA,0BAAAA,EAAA,aACA,KACAU,KAAA,OACGZ,0BC1DH,MAAAc,EAAA,CACA,QACA,QACA,QACA,QACA,QACA,SAGA,MAAAC,EAAA,CACA,MACA,MACA,MACA,MACA,MACA,OAGA,MAAAC,EAAA,CACA,QACA,SAGA,MAAAC,wPAeA,MAAAC,uJASA,MAAAC,oHASA,MAAAC,0OAcA,MAAAC,oHAQA,MAAAC,yjBAWAC,QAAA,WAEA,MAAAC,EAAA,CACAC,WAAA,CAAeC,IAAAJ,IAGfxL,EAAAC,QAAA,CAAkB+K,QAAAE,UAAAD,MAAAY,OAAA,CAA8BC,KAAAX,EAAAY,KAAAX,GAChDY,KAAA,CACAf,IAAA,CAA0Bc,KAAAV,EAAAS,KAAAX,GAC1Bc,UAAA,CAAgCF,KAAAR,EAAAO,KAAAR,IAEhCI,+BCrGA1L,EAAAC,QAAA6B,OAAAoK,OAAA,SAAAA,EAAArI,GACA,OAAAA,4CCCA,IAAAsI,EAAAzC,SAAAC,UAAAyC,SAEA,IAAAC,EAAA,cACA,IAAAC,EAAA,SAAAC,EAAA5F,GACA,IACA,IAAA6F,EAAAL,EAAAM,KAAA9F,GACA,OAAA0F,EAAAK,KAAAF,GACE,MAAAG,GACF,eAIA,IAAAC,EAAA,SAAAC,EAAAlG,GACA,IACA,GAAA2F,EAAA3F,GAAA,CAA4B,aAC5BwF,EAAAM,KAAA9F,GACA,YACE,MAAAgG,GACF,eAGA,IAAAG,EAAA9K,OAAA2H,UAAAyC,SACA,IAAAW,EAAA,oBACA,IAAAC,EAAA,6BACA,IAAAC,SAAAC,SAAA,mBAAAA,OAAAC,cAAA,SAEAnN,EAAAC,QAAA,SAAAmN,EAAAzG,GACA,IAAAA,EAAA,CAAc,aACd,UAAAA,IAAA,mBAAAA,IAAA,UAAgE,aAChE,UAAAA,IAAA,aAAAA,EAAAgD,UAAA,CAAuD,YACvD,GAAAsD,EAAA,CAAsB,OAAAL,EAAAjG,GACtB,GAAA2F,EAAA3F,GAAA,CAA2B,aAC3B,IAAA0G,EAAAP,EAAAL,KAAA9F,GACA,OAAA0G,IAAAN,GAAAM,IAAAL,wCCjCArM,EAAQ,OAARA,CAAwB,oBAAA2M,GACxB,gBAAAC,EAAAC,GACA,OAAAF,EAAApM,KAAA,cAAAsM,6FCFA,IAAAV,EAAA9K,OAAA2H,UAAAyC,SAEA,IAAAqB,EAAkB9M,EAAQ,QAE1B,IAAAyM,EAAiBzM,EAAQ,QAGzB,IAAA+M,EAAA,CACAC,mBAAA,SAAAC,GACA,IAAAC,EACA,GAAA1I,UAAApE,OAAA,GACA8M,EAAA1I,UAAA,OACG,CACH0I,EAAAf,EAAAL,KAAAmB,KAAA,gBAAA/L,OAAAC,OAGA,GAAA+L,IAAAhM,QAAAgM,IAAA/L,OAAA,CACA,IAAAM,EAAAyL,IAAAhM,OAAA,8CACA,IAAA8E,EAAAmH,EACA,IAAAA,EAAA,EAAcA,EAAA1L,EAAArB,SAAoB+M,EAAA,CAClC,GAAAV,EAAAQ,EAAAxL,EAAA0L,KAAA,CACAnH,EAAAiH,EAAAxL,EAAA0L,MACA,GAAAL,EAAA9G,GAAA,CACA,OAAAA,IAIA,UAAA4C,UAAA,oBAEA,UAAAA,UAAA,4CAKAvJ,EAAAC,QAAA,SAAAwG,EAAAsH,GACA,GAAAN,EAAAM,GAAA,CACA,OAAAA,EAEA,GAAA5I,UAAApE,OAAA,GACA,OAAA2M,EAAA,oBAAAK,EAAA5I,UAAA,IAEA,OAAAuI,EAAA,oBAAAK;;;;;ECnCA,IAAAC,EAAAhM,OAAAgM,sBACA,IAAAC,EAAAjM,OAAA2H,UAAAsE,eACA,IAAAC,EAAAlM,OAAA2H,UAAAwE,qBAEA,SAAAC,EAAAC,GACA,GAAAA,IAAA,MAAAA,IAAAC,UAAA,CACA,UAAA/E,UAAA,yDAGA,OAAAvH,OAAAqM,GAGA,SAAAE,IACA,IACA,IAAAvM,OAAAgI,OAAA,CACA,aAMA,IAAAwE,EAAA,IAAA3M,OAAA,OACA2M,EAAA,QACA,GAAAxM,OAAAyM,oBAAAD,GAAA,UACA,aAIA,IAAAE,EAAA,GACA,QAAAZ,EAAA,EAAiBA,EAAA,GAAQA,IAAA,CACzBY,EAAA,IAAA7M,OAAA8M,aAAAb,MAEA,IAAAc,EAAA5M,OAAAyM,oBAAAC,GAAAnE,IAAA,SAAAsE,GACA,OAAAH,EAAAG,KAEA,GAAAD,EAAA9D,KAAA,oBACA,aAIA,IAAAgE,EAAA,GACA,uBAAAC,MAAA,IAAAC,QAAA,SAAAC,GACAH,EAAAG,OAEA,GAAAjN,OAAAkN,KAAAlN,OAAAgI,OAAA,GAAkC8E,IAAAhE,KAAA,MAClC,wBACA,aAGA,YACE,MAAAqE,GAEF,cAIAnP,EAAAC,QAAAsO,IAAAvM,OAAAgI,OAAA,SAAAoF,EAAAC,GACA,IAAAC,EACA,IAAAC,EAAAnB,EAAAgB,GACA,IAAAI,EAEA,QAAAC,EAAA,EAAgBA,EAAAtK,UAAApE,OAAsB0O,IAAA,CACtCH,EAAAtN,OAAAmD,UAAAsK,IAEA,QAAAlH,KAAA+G,EAAA,CACA,GAAArB,EAAAxB,KAAA6C,EAAA/G,GAAA,CACAgH,EAAAhH,GAAA+G,EAAA/G,IAIA,GAAAyF,EAAA,CACAwB,EAAAxB,EAAAsB,GACA,QAAAxB,EAAA,EAAkBA,EAAA0B,EAAAzO,OAAoB+M,IAAA,CACtC,GAAAI,EAAAzB,KAAA6C,EAAAE,EAAA1B,IAAA,CACAyB,EAAAC,EAAA1B,IAAAwB,EAAAE,EAAA1B,OAMA,OAAAyB,2BCvFA,IAAAG,EAAY/O,EAAQ,QAEpBX,EAAAC,QAAA0P,EAEA,SAAAA,EAAArM,EAAAsM,EAAAC,GACA,IAAAvM,EAAA,UAAAiG,UAAA,wCACAsG,OAAA,KAEA,GAAA5N,MAAA6N,QAAAxM,OAAA,WAAAA,EAAA,mBACA,IAAAyM,EAAAzM,EAAA,GAAAvC,OACA,IAAAA,EAAAuC,EAAAvC,OAAAgP,EACA,IAAAjC,EAAAkC,EAAAC,EAAAC,EAGA,IAAAN,cAAA,UACAA,EAAA,IAAAF,EAAAE,GAAA,YAAA7O,EAAA8O,GAGA,IAAAM,EAAAP,EAAA7O,OAAA8O,EACA,GAAA9O,IAAAoP,EAAA,CACA,UAAAC,MAAA,iBAAArP,EAAA,KAAAgP,EAAA,IAAAzM,EAAAvC,OAAA,IACA,sCAAAoP,GAGA,IAAArC,EAAA,EAAAmC,EAAAJ,EAA2B/B,EAAAxK,EAAAvC,OAAiB+M,IAAA,CAC5C,IAAAkC,EAAA,EAAiBA,EAAAD,EAASC,IAAA,CAC1BJ,EAAAK,KAAA3M,EAAAwK,GAAAkC,KAAA,KAAAK,IAAA/M,EAAAwK,GAAAkC,SAGG,CACH,IAAAJ,cAAA,UAEA,IAAAU,EAAAZ,EAAAE,GAAA,WAGA,GAAA3N,MAAA6N,QAAAxM,IAAAsM,IAAA,SACAA,EAAA,IAAAU,EAAAhN,EAAAvC,OAAA8O,GACA,IAAA/B,EAAA,EAAAmC,EAAAJ,EAAAK,EAAAN,EAAA7O,OAAkDkP,EAAAC,EAAOD,IAAAnC,IAAA,CACzD8B,EAAAK,GAAA3M,EAAAwK,KAAA,KAAAuC,IAAA/M,EAAAwK,QAEO,CACP,GAAA+B,IAAA,GACAD,EAAA,IAAAU,EAAAhN,OACS,CACTsM,EAAA,IAAAU,EAAAhN,EAAAvC,OAAA8O,GAEAD,EAAA1G,IAAA5F,EAAAuM,SAGK,CAELD,EAAA1G,IAAA5F,EAAAuM,IAIA,OAAAD,yBCxDA,IAAAW,EAAA,KACA,IAAAC,EAAA,KACA,IAAAC,EAAA,KAEAzQ,EAAAC,QAAA,SAAAyQ,EAAAxG,GACA,IAAAyG,EAAA3Q,EAAAC,QAAA0Q,MAAAD,EAAAxG,GACA,OAAAyG,EAAApG,IAAA,SAAAqG,GACA,OAAAF,EAAA5P,UAAA8P,EAAAC,MAAAD,EAAAE,OACKhG,KAAA,OAGL9K,EAAAC,QAAA0Q,MAAA,SAAAI,EAAAL,EAAAxG,GACAA,KAAA,GAGA,GAAAA,EAAA8G,QAAA,GAAA9G,EAAA+G,OAAA,SACA,SAEAP,KAAA,GACA,IAAAM,SAAA9G,EAAA8G,QAAA,SAAA9G,EAAA8G,MAAAlP,OAAAoP,UACA,IAAAL,EAAA9J,KAAAoK,IAAA,EAAAjH,EAAA2G,OAAA,GACA,IAAAC,SAAA5G,EAAA4G,MAAA,SAAA5G,EAAA4G,IAAAJ,EAAA3P,OACA,IAAAkQ,EAAA/G,EAAA+G,KAEA,IAAAG,EAAAlH,EAAAkH,SAAAC,EACA,GAAAJ,IAAA,MACA,OAAAK,EAAAF,EAAAV,EAAAG,EAAAC,EAAAE,QAEA,OAAAO,EAAAH,EAAAV,EAAAG,EAAAC,EAAAE,EAAAC,IAGA,SAAAO,EAAAd,EAAAe,EAAAZ,EAAAC,GACA,IAAAY,EAAAhB,EAAAiB,QAAAF,EAAAZ,GACA,GAAAa,KAAA,GAAAA,EAAAZ,EACA,OAAAA,EACA,OAAAY,EAGA,SAAAE,EAAAH,GACA,OAAAhB,EAAA/D,KAAA+E,GAGA,SAAAH,EAAAF,EAAAV,EAAAG,EAAAC,EAAAE,GACA,IAAAL,EAAA,GACA,IAAAkB,EAAAhB,EACA,QAAA/C,EAAA+C,EAAqB/C,EAAAgD,GAAAhD,EAAA4C,EAAA3P,OAAwB+M,IAAA,CAC7C,IAAA2D,EAAAf,EAAAoB,OAAAhE,GACA,IAAAiE,EAAAxB,EAAA7D,KAAA+E,GAIA,GAAAM,GAAAjE,IAAAgD,EAAA,GACA,IAAAkB,EAAAD,EAAAjE,IAAA,EACA,IAAAmE,EAAAb,EAAAV,EAAAmB,EAAAG,EAAAhB,GACAL,EAAAuB,KAAAD,GAEAJ,EAAA/D,EAAA,GAGA,OAAA6C,EAGA,SAAAY,EAAAH,EAAAV,EAAAG,EAAAC,EAAAE,EAAAC,GAGA,IAAAN,EAAA,GAEA,IAAAwB,EAAAnB,EAEA,GAAAC,IAAA,SACAkB,EAAArQ,OAAAoP,UAEA,MAAAL,EAAAC,GAAAD,EAAAH,EAAA3P,OAAA,CAEA,IAAAqR,EAAAZ,EAAAd,EAAAF,EAAAK,EAAAC,GAGA,MAAAD,EAAAuB,EAAA,CACA,IAAAR,EAAAlB,EAAAoB,OAAAjB,IACA,MACAA,IAIA,IAAAoB,EAAAb,EAAAV,EAAAG,EAAAuB,EAAAD,GAEA,IAAAH,EAAAnB,GAAAoB,EAAAnB,IAAAmB,EAAApB,OACA,IAAAwB,EAAAL,EAAAxB,EAAAzP,OAGA,GAAAiR,EAAAI,EAAA,CAEA,MAAAJ,EAAAnB,EAAA,CACA,GAAAe,EAAAlB,EAAAoB,OAAAE,IACA,MACAA,IAEA,GAAAA,IAAAnB,EAAA,CACA,GAAAwB,EAAAxB,EAAAL,EAAAzP,OAAAsR,IACAL,EAAAK,MACa,CACbA,EAAAL,EAEA,MAAAA,EAAAnB,EAAA,CACA,IAAAe,EAAAlB,EAAAoB,OAAAE,EAAAxB,EAAAzP,SACA,MACAiR,MAIA,GAAAA,GAAAnB,EAAA,CACA,IAAA7M,EAAAoN,EAAAV,EAAAG,EAAAmB,EAAAG,GACAxB,EAAAuB,KAAAlO,GAEA6M,EAAAwB,EAEA,OAAA1B,EAIA,SAAAU,EAAAX,EAAAG,EAAAC,EAAAE,GACA,IAAAsB,EAAAvL,KAAAwL,IAAAvB,EAAAF,EAAAD,GACA,OACAA,QACAC,IAAAD,EAAAyB,0BC5HAtS,EAAAC,QAAA,SAAAuS,IAEA,UAAAC,KAAAC,YAAA,aACA,gBAAAC,GACA,IAAAC,EAAA,IAAAH,KAAAC,UACA,OAAAE,EAAAC,gBAAAF,EAAA,oBAKA,UAAAF,KAAAK,gBAAA,aACA,IAAAL,KAAAK,cAAA,qBACA,gBAAAH,GACA,IAAAI,EAAA,IAAAN,KAAAK,cAAA,oBACAC,EAAAC,MAAA,QACAD,EAAAE,QAAAN,GACA,OAAAI,GAKA,gBAAAJ,GACA,IAAAO,EAAAC,SAAAC,cAAA,OACAF,EAAAG,UAAAV,EACA,OAAAO,GAxBA,yBCAAlT,EAAAC,QAAA,SAAAwN,EAAA9G,GACA,OAAAA,IAAA,aAAAA,IAAA,mBAAAA,IAAA,+CCCA,IAAAiD,EAAWjJ,EAAQ,QACnB,IAAA2S,EAAS3S,EAAQ,QACjB,IAAA8K,EAAA7B,EAAA6C,KAAA/C,SAAA+C,KAAA5K,OAAA8H,UAAA8B,SAEA,IAAA8H,EAAA,qJACA,IAAAC,EAAA,qJAEAxT,EAAAC,QAAA,SAAA8J,IACA,IAAA0J,EAAAH,EAAA/L,SAAA+L,EAAA7L,qBAAAvG,OACA,OAAAuK,IAAAgI,EAAAF,EAAA,IAAAC,EAAA,yBCXA,IAAAb,EAAA3Q,OAAA2H,UAAAyC,SAEApM,EAAAC,QAAAyT,EAEA,SAAAA,EAAAC,GACA,OACAA,EAAAC,mBACAjB,EAAAlG,KAAAkH,EAAAE,UAAA,wBACA5R,MAAA6N,QAAA6D,wBCRA3T,EAAAC,QAAA,SAAAkG,EAAAW,GACA,OAAAA,GAAA,gCCDA,IAAAgN,EAAcnT,EAAQ,QACtB,IAAAoT,EAAA,MAEA/T,EAAAC,QAAA+T,KAAAC,EACAjU,EAAAC,QAAAiU,MAAAC,EAEA,SAAAA,EAAAC,EAAA9Q,EAAA+Q,EAAA3E,GACA,UAAA2E,IAAA,SAAAA,EAAA,EACA,UAAA3E,IAAA,SAAAA,EAAA,SAEA,IAAA4E,GAAAF,EAAAF,cAAAE,EAAAD,WAAA,WACA,IAAAI,EAAAD,EAAAF,EAAAI,aAAA,SAAAJ,EAAAF,MACA,IAAAO,EAAAC,EAAAH,EAAAjR,EAAA+Q,EAAA3E,GACA,GAAA+E,EAAA,CACA,GAAAH,EAAAF,EAAAO,aAAA,QAAAF,QACAL,EAAAF,MAAAO,GAIA,SAAAR,EAAAG,EAAA7L,EAAAjF,EAAA+Q,EAAA3E,GACA,UAAA2E,IAAA,SAAAA,EAAA,EACA,UAAA3E,IAAA,SAAAA,EAAA,UACA,GAAAzN,MAAA6N,QAAAxM,IACArB,MAAA6N,QAAAxM,EAAA,KACAA,EAAA,GAAAvC,SAAAsT,EAAA,CACA,UAAAjE,MAAA,qDACAiE,EAAA,cAAA/Q,EAAA,GAAAvC,QAGA,IAAAwT,EAAAH,EAAAI,aAAAjM,GACA,IAAAkM,EAAAC,EAAAH,EAAAjR,EAAA+Q,EAAA3E,GACA,GAAA+E,EAAA,CACAL,EAAAO,aAAApM,EAAAkM,IAIA,SAAAC,EAAAH,EAAAjR,EAAA+Q,EAAA3E,GACApM,KAAA,GACA,IAAAiR,GAAAK,EAAAL,EAAAjR,EAAA+Q,GAAA,CAEA/Q,EAAAwQ,EAAAxQ,EAAAoM,GAEA,IAAAmF,EAAAN,YAAAO,WAAA,WACA,IAAAP,GAAAM,EAAA,CAIA,GAAAA,IAAAd,EAAA,CACAA,EAAA,KACAjR,QAAAiS,KAAA,CACA,gEACA,oEACA,kDACA,6DACA,4EACA,6BACA,gDACAjK,KAAA,KAIAyJ,EAAA,IAAA7J,MAAAsK,gBAAA1R,EAAA+Q,GAGAE,EAAAF,WACAE,EAAAU,YAAA,KAKA,UAAAV,EAAAO,WAAA,YACAP,EAAAO,SAAAxR,GAGA,OAAAiR,MACG,CAEHT,EAAAxQ,EAAAiR,EAAAW,OACAX,EAAAU,YAAA,KACA,aAMA,SAAAL,EAAAL,EAAAjR,EAAA+Q,GACA,GAAAE,EAAAF,aAAA,YACA,IAAAE,EAAAW,MAAA,YACA,IAAAC,EAAAZ,EAAAW,MAAAnU,OACA,GAAAkB,MAAA6N,QAAAxM,IAAArB,MAAA6N,QAAAxM,EAAA,KAEA,OAAA6R,IAAA7R,EAAAvC,OAAAsT,MACG,CAEH,OAAAc,IAAA7R,EAAAvC,OAEA,kCChGA,IAAAsT,EAAA,EACA,IAAAe,EAAA,CAAW7C,IAAA,MAAApB,IAAA,OAEX,SAAAkE,EAAApJ,GACA,IAAAqJ,EAAArJ,EAAAlL,OAAAsT,EACAe,EAAA7C,IAAA,GAAAtG,EAAA,GACAmJ,EAAA7C,IAAA,GAAAtG,EAAA,GACAmJ,EAAAjE,IAAA,GAAAlF,EAAA,GACAmJ,EAAAjE,IAAA,GAAAlF,EAAA,GAEA,QAAA6B,EAAA,EAAiBA,EAAAwH,EAAWxH,IAAA,CAC5B,IAAA3G,EAAA8E,EAAA6B,EAAAuG,EAAA,GACA,IAAAzM,EAAAqE,EAAA6B,EAAAuG,EAAA,GACAe,EAAA7C,IAAA,GAAAxL,KAAAwL,IAAApL,EAAAiO,EAAA7C,IAAA,IACA6C,EAAA7C,IAAA,GAAAxL,KAAAwL,IAAA3K,EAAAwN,EAAA7C,IAAA,IACA6C,EAAAjE,IAAA,GAAApK,KAAAoK,IAAAhK,EAAAiO,EAAAjE,IAAA,IACAiE,EAAAjE,IAAA,GAAApK,KAAAoK,IAAAvJ,EAAAwN,EAAAjE,IAAA,KAIAnR,EAAAC,QAAAsV,WAAA,SAAAtJ,EAAA2D,GACAyF,EAAApJ,GACA2D,EAAA2C,IAAArJ,IAAAkM,EAAA7C,IAAA,GAAA6C,EAAA7C,IAAA,MACA3C,EAAAuB,IAAAjI,IAAAkM,EAAAjE,IAAA,GAAAiE,EAAAjE,IAAA,OAGAnR,EAAAC,QAAAuV,cAAA,SAAAvJ,EAAA2D,GACAyF,EAAApJ,GACA,IAAAwJ,EAAAL,EAAA7C,IAAA,GACA,IAAAmD,EAAAN,EAAA7C,IAAA,GACA,IAAAoD,EAAAP,EAAAjE,IAAA,GACA,IAAAyE,EAAAR,EAAAjE,IAAA,GACA,IAAAH,EAAA2E,EAAAF,EACA,IAAAI,EAAAD,EAAAF,EACA,IAAA3U,EAAAgG,KAAA+O,KAAA9E,IAAA6E,KACAjG,EAAAmG,OAAA7M,IAAAuM,EAAAzE,EAAA,EAAA0E,EAAAG,EAAA,KACAjG,EAAAoG,OAAAjV,EAAA,yBCpCA,IAAAgJ,EAAWpJ,EAAQ,QACnBqO,EAAcrO,EAAQ,QACtBmP,EAAA,SAAAmG,GACA,OAAAjU,OAAA2H,UAAAyC,SAAAK,KAAAwJ,KAAA,kBAGAjW,EAAAC,QAAA,SAAAiW,GACA,IAAAA,EACA,SAEA,IAAAlS,EAAA,GAEAgL,EACAjF,EAAAmM,GAAAnH,MAAA,MACA,SAAAoH,GACA,IAAAjC,EAAAiC,EAAAxE,QAAA,KACApJ,EAAAwB,EAAAoM,EAAA3V,MAAA,EAAA0T,IAAAkC,cACAzP,EAAAoD,EAAAoM,EAAA3V,MAAA0T,EAAA,IAEA,UAAAlQ,EAAAuE,KAAA,aACAvE,EAAAuE,GAAA5B,OACS,GAAAmJ,EAAA9L,EAAAuE,IAAA,CACTvE,EAAAuE,GAAA2J,KAAAvL,OACS,CACT3C,EAAAuE,GAAA,CAAAvE,EAAAuE,GAAA5B,MAKA,OAAA3C,wCCzBA,IAAAqS,EAAA,kDACA,IAAA7V,EAAAyB,MAAA0H,UAAAnJ,MACA,IAAAsM,EAAA9K,OAAA2H,UAAAyC,SACA,IAAAkK,EAAA,oBAEAtW,EAAAC,QAAA,SAAA2J,EAAA2M,GACA,IAAAnH,EAAAlO,KACA,UAAAkO,IAAA,YAAAtC,EAAAL,KAAA2C,KAAAkH,EAAA,CACA,UAAA/M,UAAA8M,EAAAjH,GAEA,IAAAoH,EAAAhW,EAAAiM,KAAAtH,UAAA,GAEA,IAAAsR,EACA,IAAAC,EAAA,WACA,GAAAxV,gBAAAuV,EAAA,CACA,IAAAzS,EAAAoL,EAAAlK,MACAhE,KACAsV,EAAAG,OAAAnW,EAAAiM,KAAAtH,aAEA,GAAAnD,OAAAgC,OAAA,CACA,OAAAA,EAEA,OAAA9C,SACS,CACT,OAAAkO,EAAAlK,MACAqR,EACAC,EAAAG,OAAAnW,EAAAiM,KAAAtH,eAKA,IAAAyR,EAAA7P,KAAAoK,IAAA,EAAA/B,EAAArO,OAAAyV,EAAAzV,QACA,IAAA8V,EAAA,GACA,QAAA/I,EAAA,EAAmBA,EAAA8I,EAAiB9I,IAAA,CACpC+I,EAAA3E,KAAA,IAAApE,GAGA2I,EAAA/M,SAAA,6BAAAmN,EAAA/L,KAAA,iDAAApB,CAAqHgN,GAErH,GAAAtH,EAAAzF,UAAA,CACA,IAAAmN,EAAA,SAAAA,MACAA,EAAAnN,UAAAyF,EAAAzF,UACA8M,EAAA9M,UAAA,IAAAmN,EACAA,EAAAnN,UAAA,KAGA,OAAA8M,wCClDA,IAAAM,EAAApW,EAAA,YAAAqW,EAAArW,EAAAkO,EAAAkI,GAAsnB,IAAAE,EAAAD,EAAG,+CCAznBhX,EAAAC,QAAA,SAAAyP,GACA,OAAAA,GACA,WACA,OAAAwH,UACA,YACA,OAAAC,WACA,YACA,OAAAC,WACA,YACA,OAAAC,WACA,aACA,OAAAC,YACA,aACA,OAAAC,YACA,cACA,OAAAC,aACA,cACA,OAAAC,aACA,YACA,OAAAxV,MACA,oBACA,OAAAyV,gECrBA,IAAA1W,EAAA,WAA0B,IAAAC,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBG,IAAA,oBAAAD,YAAA,4BAA+D,CAAAL,KAAAyW,OAAAC,OAAA,eAAAvW,EAAA,aAAwDwW,MAAA,CAAOC,UAAA7W,EAAA8W,mBAAiC9W,EAAA+W,KAAA9W,KAAAyW,OAAAC,OAAA,eAAAvW,EAAA,aAAAJ,EAAA+W,KAAA3W,EAAA,OAAoFE,YAAA,mBAAA0W,GAAA,CAAmCC,UAAAjX,EAAAkX,kBAAAC,WAAAnX,EAAAoX,qBAAuE,EAAApX,EAAAqX,aAAAjX,EAAA,aAAsCE,YAAA,UAAAsW,MAAA,CAA6BvN,MAAA,OAAAkD,KAAA,UAA8BvM,EAAA+W,KAAA3W,EAAA,OAAqBG,IAAA,iBAAAD,YAAA,2CAA0EF,EAAA,OAAYE,YAAA,UAAqBN,EAAAsX,GAAAtX,EAAA,uBAAAuX,EAAAC,GAAkD,OAAApX,EAAA,OAAiBE,YAAA,oBAA+B,CAAAF,EAAA,OAAYE,YAAA,OAAAmX,MAAA,4BAA+CF,EAAA,eAA6BA,EAAA,UAAmC,CAAAnX,EAAA,OAAYsX,MAAA,CAAAF,EAAA,GAAAxX,EAAAsD,MAAAqU,cAAArU,MAAAsU,cAAAC,YAAA,oBAAAC,YAAA,CAA6GC,UAAA,OAAgB,CAAA3X,EAAA,SAAcsX,MAAA,CAAA1X,EAAAgY,cAAAR,EAAA,gCAAAC,MAAAzX,EAAAsD,MAAAqU,cAAArU,MAAAsU,cAAAC,aAAA,GAAAL,EAAA,GAAAxX,EAAAsD,MAAAqU,cAAArU,MAAAsU,cAAAC,YAAA,gBAAAjB,MAAA,CAAwOqB,MAAA,QAAAC,MAAA,QAAA7O,MAAA,QAAAkD,KAAA,KAAA4L,KAAA,QAA0EC,SAAA,CAAWjB,WAAA,SAAAkB,GAA8B,OAAArY,EAAAsY,cAAAD,IAAiCpB,UAAA,SAAAoB,GAA8B,OAAArY,EAAAuY,cAAAf,IAA6BgB,UAAA,SAAAH,GAA8B,OAAArY,EAAAyY,OAAAjB,MAAwB,CAAApX,EAAA,QAAaE,YAAA,UAAqB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAnB,EAAA,GAAAxX,EAAAsD,MAAAqU,cAAArU,MAAAsU,cAAAC,aAAA7X,EAAAgY,cAAAR,EAAA,wBAAAxX,EAAAsX,GAAAtX,EAAA,6BAAA4Y,GAAsL,OAAAxY,EAAA,OAAiBE,YAAA,kCAA6C,CAAAF,EAAA,OAAYE,YAAA,iCAAAmX,MAAA,4CAAyEmB,EAAAC,MAAA,SAAAD,EAAAC,MAAA,SAA6E,CAAA7Y,EAAA0Y,GAAA1Y,EAAA2Y,GAAA7S,KAAAC,MAAA6S,EAAAlT,MAAA,YAAgD1F,EAAAsX,GAAAtX,EAAA,4BAAA4Y,GAAiD,OAAAxY,EAAA,OAAiBE,YAAA,iCAA4C,CAAAF,EAAA,OAAYE,YAAA,gCAAAmX,MAAA,4CAAwEmB,EAAAC,MAAA,SAAAD,EAAAC,MAAA,SAA6E,CAAA7Y,EAAA0Y,GAAA1Y,EAAA2Y,GAAA7S,KAAAC,MAAA6S,EAAAlT,MAAA,aAAgD,KAAM,OAAAtF,EAAA,OAAmB0X,YAAA,CAAaP,SAAA,WAAA3C,OAAA,QAAA7E,MAAA,QAAsD,CAAA3P,EAAA,wBAA6BwW,MAAA,CAAOkC,0BAAA9Y,EAAA8Y,6BAA2D1Y,EAAA,6BAAkCG,IAAA,gBAAAqW,MAAA,CAA2BlV,KAAA1B,EAAA+Y,YAAAC,QAAAhZ,EAAAgZ,QAAAC,YAAAjZ,EAAAiZ,YAAAC,WAAAlZ,EAAAkZ,WAAAC,aAAAnZ,EAAAmZ,aAAAC,aAAApZ,EAAAoZ,aAAAC,cAAArZ,EAAAqZ,cAAAC,cAAAtZ,EAAAsZ,cAAAC,WAAAvZ,EAAAuZ,WAAAC,QAAAxZ,EAAAwZ,QAAAC,MAAAzZ,EAAAyZ,MAAAC,OAAA1Z,EAAA0Z,OAAAC,cAAA3Z,EAAA2Z,cAAAC,MAAA5Z,EAAA4Z,MAAAC,OAAA7Z,EAAA6Z,OAAAC,sBAAA9Z,EAAA8Z,sBAAAC,kBAAA/Z,EAAA+Z,kBAAAC,uBAAAha,EAAAga,2BAA4hB,QACpkG,IAAAxZ,EAAA,oWCDA,IAAIyZ,EAAM,WAAgB,IAAAja,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,iBAA2BE,YAAA,wBAAAoX,MAAA,CAA2CwC,QAAAla,EAAAma,gBAAiCrC,YAAA,IAAgB,CAAA9X,EAAA,eAAAI,EAAA,QAAkCE,YAAA,SAAoB,CAAAN,EAAA0Y,GAAA,sBAAA1Y,EAAA+W,KAAA3W,EAAA,UAAqDE,YAAA,qBAAgC,CAAAF,EAAA,gBAAqBE,YAAA,cAAyB,EAAAN,EAAAma,eAAA/Z,EAAA,QAAAJ,EAAA0Y,GAAA1Y,EAAA2Y,GAAA3Y,EAAA0B,MAAA,MAAA1B,EAAA2Y,GAAA3Y,EAAAgZ,QAAAhZ,EAAAgZ,QAAArC,KAAA,UAAA3W,EAAA2Y,GAAA3Y,EAAAkZ,eAAAlZ,EAAA+W,KAAA3W,EAAA,QAAiKE,YAAA,mBAAAwX,YAAA,CAA4CzO,MAAA,QAAA+Q,YAAA,OAAAC,cAAA,SAAyD,CAAAra,EAAA0Y,GAAA1Y,EAAA2Y,GAAA3Y,EAAA4Z,OAAA,QAAA5Z,EAAAma,eAAA/Z,EAAA,eAAAJ,EAAA0W,OAAA4D,OAAA5Y,OAAA,QAAA1B,EAAAma,eAAA/Z,EAAA,SAAgJE,YAAA,UAAAsW,MAAA,CAA6B2D,MAAA,QAAAhO,KAAA,MAA4ByK,GAAA,CAAKwD,MAAAxa,EAAAya,uBAAiCza,EAAA+W,KAAA/W,EAAA0W,OAAA4D,OAAA5Y,OAAA,SAAA1B,EAAAma,eAAA/Z,EAAA,SAAkFE,YAAA,UAAAsW,MAAA,CAA6B2D,MAAA,aAAAlR,MAAA,WAAAkD,KAAA,MAAoDyK,GAAA,CAAKwD,MAAAxa,EAAA0a,aAAuB1a,EAAA+W,KAAA/W,EAAA0W,OAAA4D,OAAA5Y,OAAA,OAAAtB,EAAA,SAA2DE,YAAA,oBAAAsW,MAAA,CAAuC2D,MAAAta,KAAAka,eAAA,oBAAA5N,KAAA,KAAAoO,SAAA1a,KAAA6Z,sBAAA3B,KAAAlY,KAAAka,eAAA,WAA+InD,GAAA,CAAKwD,MAAAxa,EAAA4a,QAAkB5a,EAAA+W,KAAA/W,EAAA0W,OAAA4D,OAAA5Y,OAAA,OAAAtB,EAAA,SAA2DE,YAAA,oBAAAsW,MAAA,CAAuC2D,MAAAta,KAAAka,eAAA,gBAAA5N,KAAA,KAAAoO,SAAA1a,KAAA8Z,kBAAA5B,KAAAlY,KAAAka,eAAA,WAAuInD,GAAA,CAAKwD,MAAAxa,EAAA6a,QAAkB7a,EAAA+W,MAAA,GAAA/W,EAAA+W,KAAA/W,EAAA0W,OAAA4D,OAAA5Y,OAAA,SAAA1B,EAAAma,eAAA/Z,EAAA,OAA6FE,YAAA,OAAkB,CAAAF,EAAA,YAAiBwW,MAAA,CAAOkE,QAAA,UAAAC,KAAA,OAAAC,KAAA,EAAA1J,IAAA,EAAApB,IAAAjQ,KAAA+Z,uBAAA,IAAwFhD,GAAA,CAAKlK,MAAA,SAAAM,GAAwB,OAAApN,EAAAib,SAAA7N,KAA6B8N,MAAA,CAAQxV,MAAA1F,EAAA,8BAAAmb,SAAA,SAAAC,GAAmEpb,EAAAqb,8BAAAD,GAAsCE,WAAA,oCAA6C,GAAAtb,EAAA+W,MAAA,GAAA3W,EAAA,OAA6BE,YAAA,eAAwBN,EAAAma,eAAA/Z,EAAA,eAA0CE,YAAA,uBAAkC,CAAAF,EAAA,YAAiBE,YAAA,QAAAsW,MAAA,CAA2B2D,MAAA,eAAsBvD,GAAA,CAAKlK,MAAA,SAAAM,GAAwB,OAAApN,EAAAub,YAAA,SAAAnO,KAA0C8N,MAAA,CAAQxV,MAAA1F,EAAA,cAAAmb,SAAA,SAAAC,GAAmDpb,EAAAwb,cAAAJ,GAAsBE,WAAA,oBAA6Btb,EAAAma,gBAAAna,EAAA0W,OAAA4D,OAAA5Y,OAAA,QAAA1B,EAAAgZ,QAAA5Y,EAAA,eAA8FwW,MAAA,CAAO6E,IAAA,IAAAnN,GAAA,WAAAtO,EAAAgZ,QAAA,eAAAhZ,EAAAgZ,QAAA,KAAiF,CAAA5Y,EAAA,SAAcE,YAAA,sBAAAsW,MAAA,CAAyC8E,QAAA,UAAAC,aAAA,WAAApP,KAAA,KAAAgO,MAAA,WAAwE,GAAAva,EAAA+W,MAAA/W,EAAAma,gBAAAna,EAAA0W,OAAA4D,OAAA5Y,OAAA,aAAA1B,EAAAgZ,QAAA5Y,EAAA,eAAgHwW,MAAA,CAAO6E,IAAA,IAAAnN,GAAA,eAAAtO,EAAAgZ,QAAA,eAAAhZ,EAAAgZ,QAAA,WAAAhZ,EAAAgZ,QAAA,KAAkH,CAAA5Y,EAAA,SAAcwW,MAAA,CAAO8E,QAAA,UAAAC,aAAA,WAAApP,KAAA,KAAAgO,MAAA,OAAAlR,MAAA,WAAuF,GAAArJ,EAAA+W,MAAA,GAAA/W,EAAA+W,KAAA3W,EAAA,eAA8CE,YAAA,oBAA+B,CAAAF,EAAA,OAAYE,YAAA,oBAA+B,CAAAF,EAAA,WAAgBE,YAAA,WAAAsW,MAAA,CAA8B2D,MAAA,WAAiBna,EAAA,sBAA2BE,YAAA,wBAAAsW,MAAA,CAA2CsC,WAAAlZ,EAAAkZ,WAAAC,aAAAnZ,EAAAmZ,iBAA6D,GAAA/Y,EAAA,OAAgBE,YAAA,oBAA+B,CAAAN,EAAA4b,WAAA,OAAAxb,EAAA,WAAwCE,YAAA,WAAAsW,MAAA,CAA8B2D,MAAA,WAAiBva,EAAA+W,KAAA3W,EAAA,YAA0BE,YAAA,YAAAsW,MAAA,CAA+BtF,IAAAtR,EAAA4b,WAAA,GAAA1L,IAAAlQ,EAAA4b,WAAA,GAAArB,MAAA,QAAAsB,eAAA,KAAAC,eAAA9b,EAAA+b,aAAA,OAA4H/E,GAAA,CAAKlK,MAAA,SAAAM,GAAwB,OAAApN,EAAAgc,mBAAA,eAAA5O,EAAA,QAA6D8N,MAAA,CAAQxV,MAAA1F,EAAA,aAAAmb,SAAA,SAAAC,GAAkDpb,EAAA+b,aAAAX,GAAqBE,WAAA,mBAA4B,GAAAlb,EAAA,OAAgBE,YAAA,oBAA+B,CAAAN,EAAAic,YAAAnc,QAAAE,EAAA0B,OAAA,OAAAtB,EAAA,WAAgEE,YAAA,WAAAsW,MAAA,CAA8B2D,MAAA,YAAkBva,EAAA+W,KAAA3W,EAAA,YAA0BE,YAAA,YAAAsW,MAAA,CAA+BtF,IAAA,EAAApB,IAAAlQ,EAAAic,YAAAnc,OAAA,EAAAkb,KAAA,EAAAT,MAAA,QAAAO,QAAA,UAAAe,eAAA,KAAAC,cAAAhW,KAAAmS,QAAAjY,EAAAkc,cAAAlc,EAAAmc,kBAAA,GAAAnc,EAAAmc,kBAAA,eAAyNnF,GAAA,CAAKlK,MAAA,SAAAM,GAAwBpN,EAAAoc,cAAAhP,GAAuBpN,EAAAqc,oBAAyBrc,EAAAsc,yBAA6BpB,MAAA,CAAQxV,MAAA1F,EAAA,YAAAmb,SAAA,SAAAC,GAAiDpb,EAAAuc,YAAAnB,GAAoBE,WAAA,kBAA2B,GAAAtb,EAAA0B,OAAA,6BAAA8a,SAAAxc,EAAAyc,gBAAArc,EAAA,OAA4FE,YAAA,oBAA+B,CAAAF,EAAA,WAAgBE,YAAA,WAAAsW,MAAA,CAA8B2D,MAAA,YAAkBna,EAAA,YAAiBE,YAAA,YAAAsW,MAAA,CAA+BtF,IAAA,EAAApB,IAAA,IAAA2L,eAAA,KAAAC,cAAA9b,EAAA0c,kBAAA,MAAgF1F,GAAA,CAAKlK,MAAA,SAAAM,GAAwB,OAAApN,EAAAgc,mBAAA,aAAA5O,EAAA,QAA2D8N,MAAA,CAAQxV,MAAA1F,EAAA,kBAAAmb,SAAA,SAAAC,GAAuDpb,EAAA0c,kBAAAtB,GAA0BE,WAAA,wBAAiC,GAAAtb,EAAA+W,KAAA/W,EAAA2c,cAAA,qBAAAvc,EAAA,OAAoEE,YAAA,oBAA+B,CAAAF,EAAA,WAAgBE,YAAA,WAAAsW,MAAA,CAA8B2D,MAAA,aAAmBna,EAAA,YAAiBE,YAAA,YAAAsW,MAAA,CAA+BtF,IAAA,EAAApB,IAAA,IAAA2L,eAAA,KAAAC,cAAA9b,EAAA4c,eAAA,MAA6E5F,GAAA,CAAKlK,MAAA,SAAAM,GAAwB,OAAApN,EAAAgc,mBAAA,UAAA5O,EAAA,QAAwD8N,MAAA,CAAQxV,MAAA1F,EAAA,eAAAmb,SAAA,SAAAC,GAAoDpb,EAAA4c,eAAAxB,GAAuBE,WAAA,qBAA8B,GAAAtb,EAAA+W,KAAA/W,EAAA2c,cAAA,uBAAA3c,EAAA6c,eAAA/c,SAAA,EAAAM,EAAA,WAA4GE,YAAA,YAAAsW,MAAA,CAA+B2D,MAAA,YAAmB,CAAAna,EAAA,OAAYE,YAAA,mBAAAwX,YAAA,CAA4CgF,cAAA,SAAsB,CAAA1c,EAAA,SAAcE,YAAA,uBAAAoX,MAAA,CAA0CiD,QAAA3a,EAAA6c,eAAA,QAAA7c,EAAA4c,gBAA6DhG,MAAA,CAAQ2D,MAAA,KAAYvD,GAAA,CAAKwD,MAAA,SAAApN,GAAwB,OAAApN,EAAAgc,mBAAA,UAAAhc,EAAA6c,eAAA7c,EAAA6c,eAAAE,UAAA,SAAArR,GAAyG,OAAAA,EAAA,KAAA1L,EAAA4c,iBAAsC,iBAAuB5c,EAAA0Y,GAAA1Y,EAAA2Y,GAAA3Y,EAAA6c,eAAAG,KAAA,SAAAtR,GAAsD,OAAAA,EAAA,KAAA1L,EAAA4c,iBAAsC,YAAAxc,EAAA,SAA0BE,YAAA,uBAAAoX,MAAA,CAA0CiD,QAAA3a,EAAA6c,eAAA7c,EAAA6c,eAAA/c,OAAA,QAAAE,EAAA4c,gBAAyFhG,MAAA,CAAQ2D,MAAA,KAAYvD,GAAA,CAAKwD,MAAA,SAAApN,GAAwB,OAAApN,EAAAgc,mBAAA,UAAAhc,EAAA6c,eAAA7c,EAAA6c,eAAAE,UAAA,SAAArR,GAAyG,OAAAA,EAAA,KAAA1L,EAAA4c,iBAAsC,kBAAuB,KAAA5c,EAAA+W,KAAA3W,EAAA,OAA2BE,YAAA,oBAA+B,CAAAF,EAAA,WAAgBE,YAAA,6BAAAwX,YAAA,CAAsDmF,MAAA,QAAerG,MAAA,CAAQ2D,MAAA,UAAiB,CAAAna,EAAA,SAAcE,YAAA,iBAAAoX,MAAA,CAAoCwF,OAAAld,EAAAmd,cAAA,KAAkCvG,MAAA,CAAQ2D,MAAA,OAAA6C,YAAA,aAAuCpG,GAAA,CAAKwD,MAAA,SAAAnC,GAAyB,OAAArY,EAAAgc,mBAAA,uBAAqD5b,EAAA,SAAcE,YAAA,iBAAAoX,MAAA,CAAoCwF,OAAAld,EAAAmd,cAAA,KAAkCvG,MAAA,CAAQ2D,MAAA,OAAA6C,YAAA,aAAuCpG,GAAA,CAAKwD,MAAA,SAAAnC,GAAyB,OAAArY,EAAAgc,mBAAA,wBAAqD,OAAA5b,EAAA,OAAoBE,YAAA,oBAA+B,CAAAF,EAAA,WAAgBE,YAAA,6BAAAsW,MAAA,CAAgD2D,MAAA,SAAgB,CAAAna,EAAA,SAAcE,YAAA,iBAAAoX,MAAA,CAAoCwF,QAAAld,EAAAqd,eAA+BzG,MAAA,CAAQ2D,MAAA,OAAA6C,YAAA,aAAuCpG,GAAA,CAAKwD,MAAA,SAAAnC,GAAyB,OAAArY,EAAAgc,mBAAA,0BAAwD5b,EAAA,SAAcE,YAAA,iBAAAoX,MAAA,CAAoCwF,OAAAld,EAAAqd,eAA8BzG,MAAA,CAAQ2D,MAAA,SAAA6C,YAAA,aAAyCpG,GAAA,CAAKwD,MAAA,SAAAnC,GAAyB,OAAArY,EAAAgc,mBAAA,0BAAuD,WAAA5b,EAAA,OAAwBE,YAAA,cAAwBN,EAAA,eAAAI,EAAA,eAAAA,EAAA,sBAAAJ,EAAA+W,KAAA/W,EAAA0B,OAAA,OAAAtB,EAAA,eAAmHsX,MAAA,CAAA1X,EAAAma,eAAA,gCAA6D,CAAA/Z,EAAA,+BAAoCG,IAAA,gBAAAqW,MAAA,CAA2BqC,YAAAjZ,EAAAiZ,YAAAqE,QAAAtd,EAAAsd,QAAAC,WAAAvd,EAAAud,WAAA3I,OAAA5U,EAAAkc,cAAAsB,aAAAxd,EAAA0W,OAAAC,KAAA8G,WAAAzd,EAAAyd,WAAAhB,eAAAzc,EAAAyc,eAAA9C,cAAA3Z,EAAA2Z,cAAAjK,MAAA1P,EAAA0P,UAA+P,GAAA1P,EAAA+W,MAAA,QACxlQ,IAAI2G,EAAe,GCDnB,IAAIC,EAAM,WAAgB,IAAA3d,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,kBAA6BN,EAAAsX,GAAAtX,EAAA,gBAAAqJ,GAAqC,OAAAA,EAAA3H,OAAA1B,EAAAkZ,WAAA9Y,EAAA,UAAoDsX,MAAA,cAAArO,EAAA3D,QAAA1F,EAAAmZ,aAAA,aAAAnC,GAAA,CAA2EwD,MAAA,WAAqB,OAAAxa,EAAA4d,YAAAvU,EAAA3D,UAAyC,CAAAtF,EAAA,OAAYwW,MAAA,CAAOhT,IAAAyF,EAAAwU,QAAAC,IAAAzU,EAAAyU,SAAqC9d,EAAA+W,OAAa,IAC9a,IAAIgH,EAAe,GCqDnB,IAAAC,EACAC,OAAAC,SAAAC,KAAAzN,QAAA,gBACA,GACA,wBAGA,IAAA0N,EAAA,CACAzH,KAAA,qBACAjW,MAAA,+CACAS,QAAA,CACAyc,YADA,SAAAA,EACAlY,GACAnE,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,gCAAA7Y,KAGArD,KARA,SAAAA,IASA,OACAmc,OAAA,CACA,CACA9c,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,SAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,QAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,SAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,UAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,aAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,SAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,UAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,UAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,UAEA,CACApc,KAAA,UACAmc,QAAAG,EAAA,qBACAtY,MAAA,MACAoY,IAAA,cCjIkP,IAAAW,EAAA,kCCQlP,IAAAla,EAAgBxD,OAAAyD,EAAA,KAAAzD,CACd0d,EACAd,EACAI,EACF,MACA,KACA,KACA,MAIe,IAAAW,EAAAna,UCnBf,IAAIoa,EAAM,WAAgB,IAAA3e,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,oBAA+B,CAAAN,EAAAsX,GAAAtX,EAAA,uBAAA4e,EAAAtX,GAAkD,OAAAtH,EAAAma,eAAA/Z,EAAA,WAAAJ,EAAA0Y,GAAA1Y,EAAA2Y,GAAArR,GAAA,YAAAtH,EAAA2Y,GAAAiG,EAAAC,YAAA,YAAA7e,EAAA2Y,GAAAiG,EAAAE,UAAA,WAAA9e,EAAA2Y,GAAAiG,EAAAG,WAAA,WAAA/e,EAAA2Y,GAAAiG,EAAAI,iBAAAhf,EAAA+W,OAA+N3W,EAAA,oBAAAA,EAAA,UAAsCwW,MAAA,CAAOvN,MAAA,WAAA4V,SAAA,YAAyC/D,MAAA,CAAQxV,MAAA1F,EAAA,YAAAmb,SAAA,SAAAC,GAAiDpb,EAAA6X,YAAAuD,GAAoBE,WAAA,gBAA2B,CAAAlb,EAAA,SAAckH,IAAA,IAAAmQ,MAAAzX,EAAAma,eAAA,mBAAAvD,MAAA,CAAiEsI,KAAA,QAAAvI,KAAA,IAAA4D,MAAA,KAAsC2E,KAAA,UAAclf,EAAAsX,GAAAtX,EAAA,uBAAA4e,EAAAtX,GAAkD,OAAAlH,EAAA,SAAmBkH,IAAA,IAAAA,EAAA,GAAAmQ,MAAAzX,EAAAma,eAAA,mBAAAvD,MAAA,CAA2EsI,KAAA,QAAAvI,KAAA,IAAArP,EAAA,GAAAiT,MAAA,IAAAjT,EAAA,IAA0D4X,KAAA,aAAgB,GAAA9e,EAAA,gBAAyBE,YAAA,sBAAA4a,MAAA,CAAyCxV,MAAA1F,EAAA,YAAAmb,SAAA,SAAAC,GAAiDpb,EAAA6X,YAAAuD,GAAoBE,WAAA,gBAA2B,CAAAlb,EAAA,eAAoBqX,MAAAzX,EAAAma,eAAA,mBAAAvD,MAAA,CAAyDD,KAAA,MAAY,EAAA3W,EAAAma,eAAA/Z,EAAA,UAAqCwW,MAAA,CAAOuI,MAAAnf,EAAAof,gBAAAC,WAAA,QAAAC,qBAAA,wBAA0Ftf,EAAA+W,MAAA,GAAA/W,EAAAsX,GAAAtX,EAAA,uBAAA4e,EAAAtX,GAA+D,OAAAlH,EAAA,eAAyBsX,MAAA,4BAAA1X,EAAAma,eAAA,yBAAA1C,MAAAzX,EAAAma,eAAA,0BAAiIna,EAAA2Z,cAAArS,GAAAtH,EAAA2Z,cAAArS,GAAAX,EAAA,iBAAwE3G,EAAA2Z,cAAArS,GAAAtH,EAAA2Z,cAAArS,GAAApB,EAAA,UAA0E,GAAA0Q,MAAA,CAAcD,KAAA,IAAArP,EAAA,IAAqB8Q,SAAA,CAAWjB,WAAA,SAAAkB,GAA8B,kBAAqB,OAAArY,EAAAuf,sBAArB,CAAyDlH,IAAUpB,UAAA,SAAAoB,GAA8B,kBAAqB,OAAArY,EAAAwf,2BAArB,CAA8DnH,MAAY,CAAAuG,EAAAa,SAAAzf,EAAA0f,aAAAd,GAAAxe,EAAA,OAAyDE,YAAA,iBAA4B,CAAAse,EAAAa,QAAA,GAAArf,EAAA,OAAgCE,YAAA,UAAqBN,EAAAsX,GAAAsH,EAAA,iBAAAe,EAAA3Q,GAA4C,OAAA5O,EAAA,OAAiBkH,IAAAtH,EAAA4f,KAAA,GAAAD,EAAA,OAAAjI,MAAA,qBAAAkH,EAAAG,YAAAY,EAAAja,MAAA,cAAAsR,GAAA,CAA0HwD,MAAA,WAAqBoE,EAAAG,UAAAY,EAAAja,MAAgC1F,EAAA6f,0BAA+B7f,EAAA8f,mBAAuB,CAAAH,EAAA,UAAAvf,EAAA,kBAA0CE,YAAA,OAAAsW,MAAA,CAA0BmJ,WAAA,UAAAjf,IAAA6e,EAAAK,aAA6ChgB,EAAA+W,MAAA,KAAe,GAAA/W,EAAA+W,MAAA6H,EAAAa,QAAA,GAAA/Z,MAAAtF,EAAA,WAAuDE,YAAA,SAAAsW,MAAA,CAA4B2D,MAAA,gBAAAqE,EAAAqB,YAAA,MAAoD,CAAA7f,EAAA,KAAAJ,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAAa,QAAA,GAAAlF,OAAA,4BAAAva,EAAA+W,KAAA/W,EAAAyc,iBAAA,WAAAzc,EAAAyc,iBAAA,YAAArc,EAAA,WAA6KwW,MAAA,CAAO2D,MAAA,UAAiB,CAAAna,EAAA,YAAiBwW,MAAA,CAAOtF,KAAA,GAAApB,IAAA,GAAA2L,eAAA,KAAAC,cAAA8C,EAAA7O,MAAA,OAA0EiH,GAAA,CAAKlK,MAAA,WAAqB9M,EAAA6f,0BAA+B7f,EAAAqc,sBAA2BjE,SAAA,CAAW8H,QAAA,SAAA7H,GAA2B,kBAAqBrY,EAAA6f,wBAAA,MAArB,CAA0DxH,KAAW6C,MAAA,CAAQxV,MAAAkZ,EAAA,WAAAzD,SAAA,SAAAC,GAAmDpb,EAAAmgB,KAAAvB,EAAA,aAAAxD,IAAoCE,WAAA,wBAAiC,GAAAtb,EAAA+W,KAAA3W,EAAA,OAAyB0X,YAAA,CAAasI,QAAA,SAAkB,CAAAxB,EAAAyB,gBAAAzB,EAAAa,QAAA,GAAA/Z,MAAAtF,EAAA,OAA+DE,YAAA,WAAsB,CAAAF,EAAA,cAAmB0X,YAAA,CAAawI,aAAA,OAAmB1J,MAAA,CAAQ2J,aAAA,QAAAC,cAAA,QAA0CxJ,GAAA,CAAKlK,MAAA,WAAqB9M,EAAA6f,wBAAA,MAAkC7f,EAAA8f,kBAAsB5E,MAAA,CAAQxV,MAAAkZ,EAAA,UAAAzD,SAAA,SAAAC,GAAkDpb,EAAAmgB,KAAAvB,EAAA,YAAAxD,IAAmCE,WAAA,sBAAgClb,EAAA,KAAAJ,EAAA0Y,GAAA,mBAAA1Y,EAAA+W,KAAA6H,EAAA6B,kBAAA7B,EAAAa,QAAA,GAAA/Z,MAAAtF,EAAA,OAA4GE,YAAA,UAAAoX,MAAA,CAA6BzG,KAAA2N,EAAAyB,gBAAAzB,EAAAa,QAAA,GAAA/Z,QAA4D,CAAAtF,EAAA,cAAmB0X,YAAA,CAAawI,aAAA,OAAmBtJ,GAAA,CAAKlK,MAAA,WAAqB9M,EAAA6f,wBAAA,MAAkC7f,EAAA8f,kBAAsB5E,MAAA,CAAQxV,MAAAkZ,EAAA,OAAAzD,SAAA,SAAAC,GAA+Cpb,EAAAmgB,KAAAvB,EAAA,SAAAxD,IAAgCE,WAAA,mBAA6Blb,EAAA,KAAAJ,EAAA0Y,GAAA,4BAAA1Y,EAAA+W,QAAA,GAAA/W,EAAA+W,KAAA/W,EAAAwd,eAAA,eAAApd,EAAA,OAAqHE,YAAA,OAAkB,CAAAF,EAAA,OAAYE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAAI,cAAA,kBAAA5e,EAAA,OAAiEE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAAG,WAAA,cAAA3e,EAAA,OAA0DE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAAE,UAAA,gBAAA1e,EAAA,OAA2DE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAAC,YAAA,cAAAze,EAAA,OAA2DE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAAqB,aAAA,oBAAA7f,EAAA,OAAkEE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAA8B,QAAA,eAAAtgB,EAAA,OAAwDE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAA+B,WAAA,kBAAA3gB,EAAAyc,gBAAA,MAAArc,EAAA,OAA4FE,YAAA,OAAkB,CAAAN,EAAA0Y,GAAA1Y,EAAA2Y,GAAAiG,EAAArF,YAAA,mBAAAvZ,EAAA+W,OAAA/W,EAAA+W,UAA4E,GAAA/W,EAAA2X,cAAA3X,EAAA6X,YAAA,IAAA7X,EAAAyc,iBAAA,OAAArc,EAAA,OAAAJ,EAAA2X,cAAA3X,EAAA6X,YAAA,aAAAzX,EAAA,OAAmJqX,MAAA,wBAAAzX,EAAA4gB,SAAA5gB,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAgJ,YAAA,UAAA7gB,EAAA4gB,SAAA5gB,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAgJ,YAAAla,EAAA,UAAAiQ,MAAA,CAAyNjW,GAAA,qBAAyB,CAAAP,EAAA,SAAcE,YAAA,eAAAsW,MAAA,CAAkCqB,MAAA,QAAAC,MAAA,QAAA7O,MAAA,QAAAkD,KAAA,KAAA4L,KAAAnY,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAgJ,WAAAnb,QAAA1F,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAgJ,WAAAC,MAAA,uBAAA9gB,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAgJ,WAAAnb,QAAA1F,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAgJ,WAAAE,MAAA,8BAAuY3I,SAAA,CAAWI,UAAA,SAAAH,GAA6B,gBAAA3M,GAAsB,OAAA1L,EAAAghB,UAAAhhB,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAgJ,UAAAnV,GAAtB,CAAiG2M,QAAY,GAAArY,EAAA+W,KAAA/W,EAAA2X,cAAA3X,EAAA6X,YAAA,cAAAzX,EAAA,OAA2EqX,MAAA,wBAAAzX,EAAA4gB,SAAA5gB,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAoJ,aAAA,UAAAjhB,EAAA4gB,SAAA5gB,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAoJ,aAAAta,EAAA,UAAAiQ,MAAA,CAA2NjW,GAAA,sBAA0B,CAAAP,EAAA,SAAcE,YAAA,eAAAsW,MAAA,CAAkCqB,MAAA,QAAAC,MAAA,QAAA7O,MAAA,QAAAkD,KAAA,KAAA4L,KAAAnY,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAoJ,YAAAvb,QAAA1F,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAoJ,YAAAH,MAAA,uBAAA9gB,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAoJ,YAAAvb,QAAA1F,EAAA0P,MAAA1P,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAoJ,YAAAF,MAAA,8BAA2Y3I,SAAA,CAAWI,UAAA,SAAAH,GAA6B,gBAAA3M,GAAsB,OAAA1L,EAAAghB,UAAAhhB,EAAA2X,cAAA3X,EAAA6X,YAAA,GAAAoJ,WAAAvV,GAAtB,CAAkG2M,QAAY,GAAArY,EAAA+W,OAAA/W,EAAA+W,MAAA,IACxsN,IAAImK,EAAe,0MC0HnB,IAAAC,GAAAzhB,EAAA,QAEA,SAAA0hB,GAAA1b,GACA,OACAA,GACA2b,IAAA3b,KAAA,UACAA,EAAArG,cAAA0B,QACAA,OAAAkN,KAAAvI,GAAA5F,SAAA,EAIA,IAAAwhB,GAAA,CACA3K,KAAA,8BAEAjW,MAAA,CACA,UACA,aACA,SACA,eACA,aACA,iBACA,gBACA,QACA,eAEA6c,WAAA,CACA9Y,iBAAA,MAEAtD,QAAA,CACA6f,UADA,SAAAA,EACAO,EAAAC,GACA,IAAAvhB,KAAAwhB,SAAA,CACAxhB,KAAAwhB,SAAA,CACA9gB,GAAA4gB,EACAG,OAAAF,EAAAG,QACAC,OAAAJ,EAAAK,QACAC,MAAA7hB,KAAAyP,MAAA6R,GAAA7b,MACAqc,YAAA9hB,KAAA2gB,SACA,CAAA1a,EAAAsb,EAAAG,QAAAhb,EAAA6a,EAAAK,QAAAG,EAAA,GACA,SAMAC,KAhBA,SAAAA,EAgBAT,GACA,GAAAvhB,KAAAwhB,SAAA,CACA,IAAA9R,EAAA1P,KAAAyP,MAAAzP,KAAAwhB,SAAA9gB,IACA,IAAAuhB,EAAAjiB,KAAAyP,MAAAzP,KAAAwhB,SAAA9gB,IAEA,IAAAwhB,EAAAliB,KAAA2gB,SACA,CAAA1a,EAAAsb,EAAAG,QAAAhb,EAAA6a,EAAAK,QAAAG,EAAA,GACA,MAGA,IAAApT,IACA3O,KAAAwhB,SAAAM,YAAA7b,EACAic,EAAAjc,GAEA0I,EAAA3O,KAAAwhB,SAAAK,MAAAlT,EACAA,EAAA9I,KAAAoK,IAAAP,EAAAmR,MAAAhb,KAAAwL,IAAA1C,EAAAe,EAAAoR,QACAlf,QAAAC,IACA,UACA7B,KAAAwhB,SAAAM,YAAA7b,EACAic,EAAAjc,EACAjG,KAAAwhB,UAEAxhB,KAAAyP,MAAAzP,KAAAwhB,SAAA9gB,IAAA+E,MAAAkJ,EACA3O,KAAA4f,0BACA5f,KAAAoc,sBAIA+F,SA5CA,SAAAA,IA6CAniB,KAAAwhB,SAAA,MAGAlC,oBAhDA,SAAAA,IAiDAhe,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,wBAEAiB,yBAnDA,SAAAA,IAoDAje,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,6BAEAqB,KAtDA,SAAAA,EAsDA9S,GACA,IAAA7M,KAAAoiB,EAAApiB,KAAAoiB,EAAAC,EAAA1f,EAAA2f,IAAA,OACA,OAAAtiB,KAAAoiB,EAAAnhB,OAAA4L,GACA0V,SACArX,SAAA,KAGAsX,MA7DA,SAAAA,IA8DA,OAAAtB,MAEArB,cAhEA,SAAAA,IAiEA,GAAA7f,KAAAka,eACA5Y,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,oCAEAlC,kBAAAqG,EAAA9f,EAAA+f,SAAA,WACA1iB,KAAA6f,iBACA,KACA8C,WAvEA,SAAAA,IAwEA3iB,KAAAmf,gBAAAnf,KAAA4iB,UAAA5iB,KAAA6iB,gBAEAD,UA1EA,SAAAA,EA0EA5jB,GAAA,IAAAoC,EAAApB,KACA,GAAAmhB,GAAAniB,GAAA,CACA,IAAAyT,EAAA,GACA3R,OAAAkN,KAAAhP,GAAA8O,QAAA,SAAAzG,GACA,GAAA8Z,GAAAniB,EAAAqI,IAAA,CACAoL,EAAA,GAAAgD,OAAAqN,IACArQ,GADA,CAEA,CACA6H,MAAAjT,EACA0b,SAAA3hB,EAAAwhB,UAAA5jB,EAAAqI,WAGA,CACAoL,EAAA,GAAAgD,OAAAqN,IAAArQ,GAAA,EAAA6H,MAAAjT,EAAA,KAAArI,EAAAqI,SAGA,OAAAoL,EAEA,UAEAgN,aA9FA,SAAAA,EA8FAd,GACA,GAAA8D,EAAA9f,EAAA0C,IAAAsZ,EAAA,YACA,GAAAA,EAAAa,QAAAwD,KAAA,SAAAvd,GAAA,QAAAA,IAAA,QACA,aACA,gBACA,CACA,eAGAma,wBAvGA,SAAAA,EAuGAqD,GAAA,IAAAjhB,EAAAhC,KACA,IAAAkjB,EAAA,GACA,IAAAC,EAAA,GACA,IAAAC,EAAA,GAEApjB,KAAA0X,cAAA5J,QACA,SAAAtL,GAQA,IAPA6gB,EAOA7gB,EAPA6gB,IACArD,EAMAxd,EANAwd,YACAU,EAKAle,EALAke,UACA5B,EAIAtc,EAJAsc,UACAF,EAGApc,EAHAoc,WACAtF,EAEA9W,EAFA8W,WACAmH,EACAje,EADAie,OAEAnH,MAAA,GAEA4J,EAAAI,IAAA,GACAJ,EADAK,IAAA,GAEAvD,EAAA,CACAU,YACA5B,YACA2B,SACAnH,gBAGA,GAAAtX,EAAA4V,cAAAyL,EAAA,CACAF,EAAAG,IAAA,GACAH,EADAI,IAAA,GAEA3E,EAAA,CACA8B,YACA5B,YACA2B,SACAnH,mBAjCA,IAAAkK,EAwCA1iB,OAAA2iB,QAAAzjB,KAAAyP,OAAA,QAAAiU,EAAA,EAAAA,EAAAF,EAAA3jB,OAAA6jB,IAAA,KAAAC,EAAAC,IAAAJ,EAAAE,GAAA,GAAAG,EAAAF,EAAA,GAAAG,EAAAH,EAAA,GACAP,EAAAS,GAAAC,EAAAre,MAGAzF,KAAA6iB,cAAA,CACAK,OAAAI,IAAA,GACAtjB,KAAA6iB,cAAAK,OADAK,IAAA,GAEAvjB,KAAAqd,QAAA6F,IAEAC,SAAAG,IAAA,GAAAtjB,KAAA6iB,cAAAM,YACA1T,MAAA6T,IAAA,GAAAtjB,KAAA6iB,cAAApT,MAAA2T,IAGA,GAAAH,EAAA,CACAjjB,KAAA+jB,mBAAA/jB,KAAA4X,iBACA,CACA5X,KAAAgkB,iBAAA,MACA1iB,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,qBAGAhd,EAAA,KAAA8c,YAAAC,IAAAC,MACA,mCACAte,KAAA6iB,eAEA7iB,KAAA2iB,cAEAqB,iBAzKA,SAAAA,EAyKAC,GACA3iB,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,mBAAA2F,IAEAC,uBAAAzB,EAAA9f,EAAA+f,SAAA,WACA1iB,KAAA+jB,mBAAA/jB,KAAA4X,cACA,KACAmM,mBA/KA,SAAAA,EA+KA/Q,GACA,GAAAA,IAAA,KACA1R,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,sBAEAhd,EAAA,KAAA8c,YAAAC,IAAAC,MACA,iBACAtL,IAAA,wBAEA1R,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,sBAAAtL,IAEAmR,gBAzLA,SAAAA,EAyLAtjB,GAAA,IAAAujB,EAAApkB,KACA,GAAAA,KAAAwd,WAAA,CACAxd,KAAAqkB,gBAAArkB,KAAAwd,WAEAxd,KAAA0X,cAAA1X,KAAA0X,cAAArO,IACA,SAAAib,EAAAjd,GAAA,IAAA2Y,EAAAsE,EAAAtE,YAAAnB,EAAAyF,EAAAzF,SAAA0F,EAAAC,IAAAF,EAAA,mCAAAhB,IAAA,GACAiB,EADA,CAEAvE,cACAnB,WACAwE,IAAA,IAAAhc,EAAA,GACAmY,QACA4E,EAAAC,iBACAD,EAAAC,gBAAAtX,eAAAiT,GACAoE,EAAAC,gBAAArE,GAAA3W,IACA,SAAAob,GAAA,IAAAC,EAAAD,EAAAC,YAAA5F,EAAA2F,EAAA3F,UAAAyF,EAAAC,IAAAC,EAAA,oCAAAnB,IAAA,CACAhJ,MAAAoK,EACAjf,MAAAqZ,GACAyF,KAGA,WAMApgB,MAAA,CACAmZ,WADA,SAAAA,EACAqH,GACA3kB,KAAA0X,cAAAiN,EAAAtb,IACA,SAAAub,GAeA,IAdA5E,EAcA4E,EAdA5E,YACAU,EAaAkE,EAbAlE,UACA7B,EAYA+F,EAZA/F,SACAC,EAWA8F,EAXA9F,UACAsB,EAUAwE,EAVAxE,eACAxB,EASAgG,EATAhG,WACAG,EAQA6F,EARA7F,aACAzF,EAOAsL,EAPAtL,WACAkH,EAMAoE,EANApE,iBACAC,EAKAmE,EALAnE,OACAoE,EAIAD,EAJAC,GACAC,EAGAF,EAHAE,GACAlE,EAEAgE,EAFAhE,UACAI,EACA4D,EADA5D,WAEA,OACAhB,cACAU,YACA7B,WACAC,YACAsB,iBACAxB,aACAG,eACAzF,aACAkH,mBACAC,SACAG,YACAI,aACAlR,MAAAjK,KAAAmS,OAAA8M,EAAAD,GAAA,OAIA7kB,KAAA2U,OAAA3U,KAAA2U,OAAApK,QAAA,UACAjJ,EAAA,KAAAC,IAAAV,IAAAkkB,KAAA,CAAAtjB,KAAA,OAAAf,GAAAV,KAAAyW,OAAA4D,OAAA3Z,KAAAiB,KACA3B,KAAAmkB,gBACA,YAGAvM,YA1CA,SAAAA,EA0CA5E,GACAhT,KAAAgkB,iBAAA,MACAhkB,KAAA+jB,mBAAA/Q,KAGA5O,QAnRA,SAAAA,IAmRA,IAAA4gB,EAAAhlB,KACAsB,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,wBACAhd,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,uBACAD,EAAApN,YAAA,MAEAtW,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,wCAAAC,GACAF,EAAAnC,cAAAqC,EACAF,EAAArC,eAEA3E,OAAAmH,iBAAA,UAAAnlB,KAAAmiB,UACAnE,OAAAmH,iBAAA,YAAAnlB,KAAAgiB,OAEA5f,KA/RA,SAAAA,IAgSA,OACAsV,cAAA,GACAE,YAAA,IACA0B,WAAA,MACA+K,gBAAA,KACAxB,cAAA,GACA1D,gBAAA,GACAjF,eAAAla,KAAAyW,OAAAC,OAAA,kBC7a2P,IAAA0O,GAAA,oBCQ3P,IAAIC,GAAYvkB,OAAAyD,EAAA,KAAAzD,CACdskB,GACA1G,EACAuC,EACF,MACA,KACA,KACA,MAIe,IAAAqE,GAAAD,WCnBf,IAAIE,GAAM,WAAgB,IAAAxlB,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAF,EAAAylB,GAAA,IACzF,IAAIC,GAAe,YAAiB,IAAA1lB,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,OAAkB,CAAAF,EAAA,OAAYE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,YAAuB,CAAAN,EAAA0Y,GAAA,2BAAAtY,EAAA,MAAAJ,EAAA0Y,GAAA,uBAAAtY,EAAA,OAAqFE,YAAA,YAAuB,CAAAF,EAAA,UAAeE,YAAA,qBAAgC,CAAAN,EAAA0Y,GAAA,uBCcvW,IAAAiN,GAAA,CACAhP,KAAA,kBChBoP,IAAAiP,GAAA,qBCQpP,IAAIC,GAAY9kB,OAAAyD,EAAA,KAAAzD,CACd6kB,GACAJ,GACAE,GACF,MACA,KACA,WACA,MAIe,IAAAI,GAAAD,WC+Jf,IAAAE,GAAA,CACApP,KAAA,4BACAjW,MAAA,CACA,cACA,iBACA,UACA,OACA,eACA,gBACA,gBACA,aACA,eACA,aACA,UACA,QACA,SACA,QACA,SACA,gBACA,wBACA,oBACA,0BAEA6c,WAAA,CACAgI,+BACA7G,qBACAoH,mBAGA3kB,QAAA,CACAmb,qBADA,SAAAA,IAEA2B,OAAA+H,aAAA/lB,KAAAgmB,iBAEA1kB,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,oBACAte,KAAAgmB,gBAAAhI,OAAAiI,WAAA,WAEA3kB,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,qBACA,MAEAvC,mBAVA,SAAAA,EAUAmK,EAAA/Y,EAAAgZ,GACAnmB,KAAAsb,YAAA4K,EAAA/Y,GACA,GAAAgZ,EAAA,CACAnmB,KAAAoc,wBACA,CACApc,KAAA6f,gBAEA7f,KAAAqc,wBAEA7B,oBAnBA,SAAAA,IAoBAlZ,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,mCACAhd,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,aACAhd,EAAA,KAAA8c,YAAAC,IAAAC,MACA,uCACAte,KAAAka,gBAEAla,KAAA6f,iBAEApF,UA5BA,SAAAA,IA6BAnZ,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,6BAAAte,KAAAka,iBAEA2F,cA/BA,SAAAA,IAgCA,GAAA7f,KAAAka,eAAAla,KAAAya,aAEA2B,kBAAAqG,EAAA9f,EAAA+f,SAAA,WACA1iB,KAAA6f,iBACA,KACA7E,SArCA,SAAAA,EAqCAhI,GACA1R,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,4BAAAtL,IAEA2H,KAxCA,SAAAA,IAyCArZ,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,mCAEA1D,KA3CA,SAAAA,IA4CAtZ,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,+BAEAnC,cA9CA,SAAAA,EA8CA1Q,GACAzL,KAAAsb,YAAA,gBAAAtb,KAAAgc,YAAAvQ,KAEAsY,mBAjDA,SAAAA,IAkDAziB,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,sBAEAhD,YAAAmH,EAAA9f,EAAAyjB,SAAA,SAAA3kB,EAAAgE,GACA,GAAAhE,IAAA,gBACAgE,IAAA,GAEAnE,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,iBAAA7c,EAAAgE,GACAnE,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,aACAhd,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,qBACAte,KAAA+jB,sBACA,MAGA5f,MAAA,CACAkV,cADA,SAAAA,EACA5N,GACA7J,QAAAC,IAAA,OAAA4J,GACAzL,KAAAwd,WAAA/R,EAAA+R,WAAA/R,EAAA+R,WAAA,KACAxd,KAAAkc,kBAAA,MAAA7S,IACA,SAAAgd,GAAA,OAAA5a,EAAAyQ,kBAAAmK,IAAA,IAEArmB,KAAAqd,QAAA5R,EAAA6a,SACAtmB,KAAAsd,WAAA7R,EAAA6R,WACAtd,KAAAyP,MAAAhE,EAAAgE,MACAzP,KAAA4c,eAAAnR,EAAA8a,iBAEAjN,WAZA,SAAAA,EAYA7T,GACAzF,KAAAyc,kBAAAhX,GAEA+T,MAfA,SAAAA,EAeA/T,GACAzF,KAAAkd,aAAAzX,GAEAgU,OAlBA,SAAAA,EAkBAhU,GACAzF,KAAAod,cAAA3X,GAEAmU,OArBA,SAAAA,EAqBAnU,GACAzF,KAAAub,cAAA9V,GAEA8T,QAxBA,SAAAA,EAwBA9T,GACAzF,KAAA2c,eAAAlX,GAEA0T,aA3BA,SAAAA,EA2BA1T,GACAzF,KAAA8b,aAAArW,EAAA,IAEA2T,cA9BA,SAAAA,EA8BA3T,GAAA,IAAArE,EAAApB,KACA,GAAAA,KAAAyB,OAAA,QACAzB,KAAAgc,YAAAlO,QAAA,SAAAX,EAAAP,GACA,GAAAO,KAAA1H,EAAA,CACArE,EAAAkb,YAAA1P,KAGA5M,KAAAic,cAAAjc,KAAAgc,YAAAhc,KAAAsc,aAAA,KAGAA,YAxCA,SAAAA,EAwCAtJ,GACAhT,KAAAic,cAAAjc,KAAAgc,YAAAhJ,GAAA,IAEA+F,QA3CA,SAAAA,IA2CA,IAAA/W,EAAAhC,KACA,GAAAA,KAAAyB,OAAA,aACAzB,KAAA2b,WAAA,cACA,GAAA3b,KAAAyB,OAAA,QACAzB,KAAA2b,WAAA3b,KAAA+Y,QAAAyN,MACA3Y,MAAA,KACAxE,IAAA,SAAA8D,GAAA,OAAAA,EAAA,KACAnN,KAAAgc,YAAAhc,KAAA+Y,QAAA0N,MACA5Y,MAAA,KACAxE,IAAA,SAAA8D,GAAA,OAAAA,IACAnN,KAAA0c,YAAA1c,KAAA+Y,QAAA2N,aACA1mB,KAAAwc,eAAAxc,KAAA+Y,QAAA4N,gBACA3mB,KAAAgc,YAAAlO,QAAA,SAAAX,EAAAP,GACA,GAAAO,KAAAnL,EAAAoX,cAAA,CACApX,EAAAsa,YAAA1P,KAGA5M,KAAAic,cAAAjc,KAAAgc,YAAAhc,KAAAsc,aAAA,KAGAvC,uBA/DA,SAAAA,EA+DAtU,GACAzF,KAAAob,8BAAA3V,EAAA,KAIArB,QAhKA,SAAAA,IAiKApE,KAAA8b,cAAA9b,KAAAmZ,aAAA,GACAnZ,KAAAic,eAAAjc,KAAAoZ,cACApZ,KAAAyc,kBAAAzc,KAAAsZ,WACAtZ,KAAA2c,eAAA3c,KAAAuZ,QACAvZ,KAAAkd,aAAAld,KAAAwZ,MACAxZ,KAAAod,cAAApd,KAAAyZ,OACAzZ,KAAAub,cAAAvb,KAAA4Z,QAGAxX,KA1KA,SAAAA,IA2KA,OACA8X,eAAAla,KAAAyW,OAAAC,OAAA,eACAiF,WAAA,MACAG,aAAA,KACAG,cAAA,KACAK,YAAA,KACAN,YAAA,MACAU,YAAA,KACAF,eAAA,KACAa,QAAA,KACAC,WAAA,GACA7N,MAAA,GACAkN,eAAA,KACAF,kBAAA,KACAS,aAAA,KACAE,cAAA,KACAlB,kBAAA,MACAX,cAAA,KACAiC,WAAA,KACAZ,eAAA,GACAxB,8BAAA,EACA4K,gBAAA,QClXyP,IAAAY,GAAA,oBCQzP,IAAIC,GAAY/lB,OAAAyD,EAAA,KAAAzD,CACd8lB,GACA5M,EACAyD,EACF,MACA,KACA,KACA,MAIe,IAAAqJ,GAAAD,WCnBf,IAAIE,GAAM,WAAgB,IAAAhnB,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAuB,CAAAF,EAAA,SAAcwW,MAAA,CAAOqB,MAAA,QAAAE,KAAA,cAAoCnB,GAAA,CAAKwD,MAAAxa,EAAAinB,uBAAkC,CAAA7mB,EAAA,aAAkBwW,MAAA,CAAOsQ,OAAA,cAAA1V,KAAA,eAAA5C,OAAA,UAAgE,CAAA5O,EAAA0Y,GAAA,oCAAAtY,EAAA,SAAyDwW,MAAA,CAAOqB,MAAA,QAAAE,KAAA,WAAiCnB,GAAA,CAAKwD,MAAAxa,EAAAmnB,eAA0B,CAAA/mB,EAAA,aAAkBwW,MAAA,CAAOsQ,OAAA,cAAA1V,KAAA,eAAA5C,OAAA,UAAgE,CAAA5O,EAAA0Y,GAAA,uBAAAtY,EAAA,SAA4CwW,MAAA,CAAOqB,MAAA,QAAAE,KAAA,QAA8BnB,GAAA,CAAKwD,MAAAxa,EAAA4a,OAAkB,CAAAxa,EAAA,aAAkBwW,MAAA,CAAOsQ,OAAA,cAAA1V,KAAA,eAAA5C,OAAA,UAAgE,CAAA5O,EAAA0Y,GAAA,cAAAtY,EAAA,SAAmCwW,MAAA,CAAOqB,MAAA,QAAAE,KAAA,QAA8BnB,GAAA,CAAKwD,MAAAxa,EAAA6a,OAAkB,CAAAza,EAAA,aAAkBwW,MAAA,CAAOsQ,OAAA,cAAA1V,KAAA,eAAA5C,OAAA,UAAgE,CAAA5O,EAAA0Y,GAAA,mBAC54B,IAAI0O,GAAe,GCenB,IAAAC,GAAA,CACA1Q,KAAA,uBACAjW,MAAA,8BAEAS,QAAA,CACA8lB,qBADA,SAAAA,IAEA1lB,EAAA,KAAA8c,YAAAC,IAAAC,MACA,6BACAte,KAAA6Y,4BAGAqO,aAPA,SAAAA,IAQA5lB,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,mCACAhd,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,aACAhd,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,6CACAhd,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,oCAEA3D,KAbA,SAAAA,IAcArZ,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,mCAEA1D,KAhBA,SAAAA,IAiBAtZ,EAAA,KAAA8c,YAAAC,IAAAC,MAAA,gCAIAna,MAAA,GAEAC,QA3BA,SAAAA,MA6BAhC,KA7BA,SAAAA,IA8BA,OACAilB,MAAA,QC/CoP,IAAAC,GAAA,oBCQpP,IAAIC,GAAYzmB,OAAAyD,EAAA,KAAAzD,CACdwmB,GACAP,GACAI,GACF,MACA,KACA,KACA,MAIe,IAAAK,GAAAD,WCnBf,IAAIE,GAAM,WAAgB,IAAA1nB,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,YAAA,0CAAAsW,MAAA,CAA6DjW,GAAA,cAAAgnB,cAAA,YAA4C,CAAAvnB,EAAA,OAAYE,YAAA,kBAAAoX,MAAA,CAAqCkQ,QAAA5nB,EAAA6W,YAA0B,CAAAzW,EAAA,KAAUE,YAAA,kBAA6B,CAAAN,EAAA0Y,GAAA,eAAAtY,EAAA,QAAAJ,EAAA0Y,GAAA,8CAAAtY,EAAA,OAAkGE,YAAA,yBAAoC,CAAAN,EAAAylB,GAAA,GAAArlB,EAAA,OAAsBE,YAAA,8CAAyD,CAAAF,EAAA,OAAYE,YAAA,cAAyB,CAAAF,EAAA,OAAYwW,MAAA,CAAO7G,MAAA,OAAA6E,OAAA,OAAAiT,QAAA,YAAAC,QAAA,MAAAC,MAAA,6BAAAC,cAAA,iCAAwJ,CAAA5nB,EAAA,KAAUwW,MAAA,CAAOjW,GAAA,SAAAsnB,OAAA,OAAAC,eAAA,IAAAC,KAAA,OAAAC,YAAA,YAAsF,CAAAhoB,EAAA,WAAgBwW,MAAA,CAAOjW,GAAA,OAAAwnB,KAAA,UAAAE,OAAA,qMAAqOjoB,EAAA,OAAgBwW,MAAA,CAAO7G,MAAA,OAAA6E,OAAA,OAAAiT,QAAA,YAAAC,QAAA,MAAAC,MAAA,6BAAAC,cAAA,iCAAwJ,CAAA5nB,EAAA,KAAUwW,MAAA,CAAOjW,GAAA,SAAAsnB,OAAA,OAAAC,eAAA,IAAAC,KAAA,OAAAC,YAAA,YAAsF,CAAAhoB,EAAA,WAAgBwW,MAAA,CAAOjW,GAAA,OAAAwnB,KAAA,UAAAE,OAAA,qMAAqOjoB,EAAA,OAAgBwW,MAAA,CAAO7G,MAAA,OAAA6E,OAAA,OAAAiT,QAAA,YAAAC,QAAA,MAAAC,MAAA,6BAAAC,cAAA,iCAAwJ,CAAA5nB,EAAA,KAAUwW,MAAA,CAAOjW,GAAA,SAAAsnB,OAAA,OAAAC,eAAA,IAAAC,KAAA,OAAAC,YAAA,YAAsF,CAAAhoB,EAAA,WAAgBwW,MAAA,CAAOjW,GAAA,OAAAwnB,KAAA,UAAAE,OAAA,qMAAqOjoB,EAAA,OAAgBwW,MAAA,CAAO7G,MAAA,OAAA6E,OAAA,OAAAiT,QAAA,YAAAC,QAAA,MAAAC,MAAA,6BAAAC,cAAA,iCAAwJ,CAAA5nB,EAAA,KAAUwW,MAAA,CAAOjW,GAAA,SAAAsnB,OAAA,OAAAC,eAAA,IAAAC,KAAA,OAAAC,YAAA,YAAsF,CAAAhoB,EAAA,WAAgBwW,MAAA,CAAOjW,GAAA,OAAAwnB,KAAA,UAAAE,OAAA,qMAAqOjoB,EAAA,OAAgBwW,MAAA,CAAO7G,MAAA,OAAA6E,OAAA,OAAAiT,QAAA,YAAAC,QAAA,MAAAC,MAAA,6BAAAC,cAAA,iCAAwJ,CAAA5nB,EAAA,KAAUwW,MAAA,CAAOjW,GAAA,SAAAsnB,OAAA,OAAAC,eAAA,IAAAC,KAAA,OAAAC,YAAA,YAAsF,CAAAhoB,EAAA,WAAgBwW,MAAA,CAAOjW,GAAA,OAAAwnB,KAAA,UAAAE,OAAA,qMAAqOjoB,EAAA,QAAiBE,YAAA,SAAoB,CAAAN,EAAA0Y,GAAA,WAAA1Y,EAAAylB,GAAA,KAAAzlB,EAAAylB,GAAA,QAC9rG,IAAI6C,GAAe,YAAiB,IAAAtoB,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,2BAAsC,CAAAF,EAAA,OAAYwW,MAAA,CAAOhT,IAAA,iFAAAmM,MAAA,KAAA6E,OAAA,KAAAkJ,IAAA,MAA4H1d,EAAA,OAAYE,YAAA,yBAAoC,CAAAF,EAAA,MAAWE,YAAA,oBAA+B,CAAAN,EAAA0Y,GAAA,mBAAAtY,EAAA,KAAoCE,YAAA,0DAAqE,CAAAN,EAAA0Y,GAAA,8CAAAtY,EAAA,QAAkEE,YAAA,yCAAAsW,MAAA,CAA4D2R,uBAAA,iBAAuC,CAAAnoB,EAAA,QAAaE,YAAA,qCAAAsW,MAAA,CAAwDjW,GAAA,iBAAqB,CAAAX,EAAA0Y,GAAA,qNAA6N,WAAc,IAAA1Y,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,oBAA+B,CAAAF,EAAA,MAAWE,YAAA,oBAA+B,CAAAN,EAAA0Y,GAAA,qBAAAtY,EAAA,KAAsCE,YAAA,iCAA4C,CAAAN,EAAA0Y,GAAA,8BAAsC,WAAc,IAAA1Y,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,2BAAsC,CAAAF,EAAA,OAAYwW,MAAA,CAAOhT,IAAA,0EAAAmM,MAAA,KAAA6E,OAAA,KAAAkJ,IAAA,MAAqH1d,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,MAAWE,YAAA,oBAA+B,CAAAN,EAAA0Y,GAAA,8BAAAtY,EAAA,KAA+CE,YAAA,0DAAqE,CAAAN,EAAA0Y,GAAA,iDAAAtY,EAAA,QAAqEE,YAAA,yCAAAsW,MAAA,CAA4D2R,uBAAA,gBAAsC,CAAAnoB,EAAA,QAAaE,YAAA,qCAAAsW,MAAA,CAAwDjW,GAAA,gBAAoB,CAAAX,EAAA0Y,GAAA,kKCkJr7D,IAAA8P,GAAA,CACA9nB,MAAA,cACA2B,KAFA,SAAAA,IAGA,OACAwU,UAAA,QAGAF,KAAA,aC1J+O,IAAA8R,GAAA,oBCQ/O,IAAIC,GAAY3nB,OAAAyD,EAAA,KAAAzD,CACd0nB,GACAf,GACAY,GACF,MACA,KACA,WACA,MAIe,IAAAK,GAAAD,WCnBf,IAAIE,GAAM,WAAgB,IAAA5oB,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAF,EAAAylB,GAAA,IACzF,IAAIoD,GAAe,YAAiB,IAAA7oB,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,KAAeE,YAAA,MAAAsW,MAAA,CAAyBuH,KAAA,uHAA6H,CAAA/d,EAAA,OAAYE,YAAA,QAAmB,CAAAN,EAAA0Y,GAAA,WAAAtY,EAAA,MAAAA,EAAA,MAAAJ,EAAA0Y,GAAA,aAAAtY,EAAA,MAAAJ,EAAA0Y,GAAA,aAAAtY,EAAA,MAAAJ,EAAA0Y,GAAA,iBAAAtY,EAAA,MAAAJ,EAAA0Y,GAAA,SAAAtY,EAAA,MAA0JE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,YAAAsW,MAAA,CAA+B7G,MAAA,KAAA6E,OAAA,KAAAhR,IAAA,wEAAAka,IAAA,gBAA2H1d,EAAA,MAAaE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,YAAAsW,MAAA,CAA+B7G,MAAA,KAAA6E,OAAA,KAAAhR,IAAA,iEAAAka,IAAA,kBCiCzsB,IAAAgL,GAAA,CACAnS,KAAA,aCnC+O,IAAAoS,GAAA,oBCQ/O,IAAIC,GAAYjoB,OAAAyD,EAAA,KAAAzD,CACdgoB,GACAH,GACAC,GACF,MACA,KACA,WACA,MAIe,IAAAI,GAAAD,WC2Nf,IAAAE,GAAA,CACAvS,KAAA,cACA4G,WAAA,CACAwJ,6BACAU,wBACAkB,aACAM,cAGA9nB,QAAA,CACAgoB,YADA,SAAAA,IACA,IAAA9nB,EAAApB,KACA,IAAAmpB,EAAA,GACA,GAAAnpB,KAAAyW,OAAA4D,OAAA8O,eAAA,CACAnpB,KAAAyW,OAAA4D,OAAA8O,eAAAtb,MAAA,KAAAC,QAAA,SAAAsb,GAAA,IAAAC,EACAD,EAAAvb,MAAA,KADAyb,EAAA1F,IAAAyF,EAAA,GACAta,EADAua,EAAA,GACAjD,EADAiD,EAAA,GAEAjD,SACAA,IAAA,aAAAA,IAAA,KACA,KACAA,IAAA,MACA,MACArb,MAAAqb,GACAA,EACAzlB,OAAAylB,GACA8C,EAAApa,GAAAsX,IAGA,GAAArmB,KAAA8Y,cAAA,aACA9Y,KAAAmZ,aAAAsJ,EAAA9f,EAAAoF,IAAAohB,EAAA,kBACA,GAAAnpB,KAAA8Y,cAAA,QACA9Y,KAAAmZ,aAAAsJ,EAAA9f,EAAAoF,IACAohB,EACA,QACAnpB,KAAA+Y,QAAAyN,MAAA3Y,MAAA,KAAAxE,IAAA,SAAA8D,GAAA,OAAAA,IAAA,IAEAnN,KAAAoZ,cAAAqJ,EAAA9f,EAAAoF,IACAohB,EACA,SACAnpB,KAAA+Y,QAAA0N,MAAA5Y,MAAA,KAAAxE,IAAA,SAAA8D,GAAA,OAAAA,IAAA,IAGAnN,KAAAkZ,aAAAuJ,EAAA9f,EAAAoF,IAAAohB,EAAA,eACAnpB,KAAAuZ,QAAAkJ,EAAA9f,EAAAoF,IAAAohB,EAAA,aACAnpB,KAAAwZ,MAAAiJ,EAAA9f,EAAAoF,IAAAohB,EAAA,aACAnpB,KAAAyZ,OAAAgJ,EAAA9f,EAAAoF,IAAAohB,EAAA,eACAnpB,KAAAsZ,WAAAmJ,EAAA9f,EAAAoF,IAAAohB,EAAA,gBAEAnpB,KAAAupB,kCAAA,SACA,IAAAvpB,KAAAka,eAAA,CACAla,KAAAwpB,kBACAxpB,KAAAupB,kCAAA,MAEA,GAAAvpB,KAAAka,eAAAla,KAAA4Z,OAAA,KACA5Z,KAAAiB,SACAjB,KAAAypB,eACA,GAAAzpB,KAAAka,eAAA,CACAjI,SAAAyX,uBACA,eACA,GAAAlS,MAAA2I,QAAA,OAEA7e,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,8BACA7jB,EAAAuoB,qBAEAroB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,8BACA7jB,EAAAwoB,qBAEAtoB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,kCACA7jB,EAAAiC,MAAAwmB,kBAAAC,UAAA3nB,IAAA,mBAEAb,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,kCACA7jB,EAAAiC,MAAAwmB,kBAAAC,UAAAC,OAAA,mBAEAzoB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,sCACAjH,OAAA+H,aAAA3kB,EAAA4oB,iBAEA1oB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,+BACA7jB,EAAA6oB,aAAA,OAEA3oB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,iCACA,GAAA7jB,EAAAqV,OAAAC,OAAA,gBACAtV,EAAAiX,mBAGA/W,EAAA,KAAA8c,YAAAC,IAAA4G,IACA,mCACA,SAAAC,GACA9jB,EAAA8oB,yBAAAhF,EACA9jB,EAAAH,WAGAK,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,+BAAAC,GACA9jB,EAAA+oB,iBAAAjF,EACA9jB,EAAAH,WAEAK,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,4BAAAC,GACA9jB,EAAA4iB,iBAAAkB,IAEA5jB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,0BAAAC,GACA9jB,EAAAgpB,SAAAlF,EACA9jB,EAAAH,WAEAK,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,gCACA7jB,EAAA2iB,mBAAA,OAEAziB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,+BACA7jB,EAAA2iB,mBAAA,QAEAziB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,0BAAAxjB,EAAAgE,GACA,IAAArE,EAAA2L,eAAAtL,GAAA,OACAL,EAAAK,GAAAgE,EACA,GAAAhE,IAAA,gBAAAL,EAAA8Y,eACA9Y,EAAAmoB,kCAAA,MACAnoB,EAAAipB,mBAAA5oB,EAAAgE,GACA,GAAAhE,IAAA,UACAL,EAAAqoB,eACAroB,EAAAkpB,qBACA,CACAlpB,EAAAH,YAGAK,EAAA,KAAA8c,YAAAC,IAAA4G,IACA,uCACA,SAAAsF,GACA,IAAAnpB,EAAAopB,wBAAA,CACA5oB,QAAAC,IAAA,iCACAT,EAAAqpB,0BACA,OAEArpB,EAAAspB,2BACAtpB,EAAAopB,wBAAA,IAUA,GAAAD,EAAAnpB,EAAAmoB,kCAAA,MACAnoB,EAAAH,WAGAK,EAAA,KAAA8c,YAAAC,IAAA4G,IACA,6BACA,SAAA0F,GACAvpB,EAAAmoB,kCAAAoB,KAGArpB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,4CACA7jB,EAAAwpB,uCAEAtpB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,wCACA7jB,EAAAypB,mCAEAvpB,EAAA,KAAA8c,YAAAC,IAAA4G,IACA,4BACA,SAAA6F,GACA,IACA1pB,EAAAopB,yBACAppB,EAAAopB,wBAAA3qB,SAAA,GACAirB,EAAA,GACAA,EAAA1pB,EAAAopB,wBAAA3qB,OAAA,EACA,CACA+B,QAAAC,IAAA,2BACAT,EAAAqpB,0BACA,OAEArpB,EAAA2pB,8BAAAD,EACA1pB,EAAAspB,2BACAtpB,EAAAopB,wBAAAM,MAIAxpB,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,+BAAAxf,GACArE,EAAA8Y,eAAAzU,IAGAnE,EAAA,KAAA8c,YAAAC,IAAA4G,IAAA,qCAAAxf,GACArE,EAAAyX,0BAAApT,IAGAzF,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAgJ,SAAA3gB,KAAAgrB,cAEAhrB,KAAAirB,gBAAAjrB,KAAAqZ,eACA,IAAArZ,KAAA6Y,0BAAA,CACA7Y,KAAAkrB,oBAAA,MACAlrB,KAAAqD,MAAAwmB,kBAAAC,UAAAC,OACA,wBAEA,CACA/pB,KAAAkrB,oBAAA,KACAlrB,KAAAqD,MAAAwmB,kBAAAC,UAAA3nB,IAAA,oBAEAnC,KAAAmrB,eAEAA,YA1LA,SAAAA,IA2LAvpB,QAAAC,IACA,IACA7B,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAC,YACA5X,KAAAkrB,oBACAlrB,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAC,aAAA,GACA5X,KAAAkrB,qBAEAlrB,KAAA6W,gBACA7W,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAC,aAAA,GACA5X,KAAAkrB,qBAEAvB,iBAtMA,SAAAA,IAuMA3pB,KAAAkrB,oBAAA,KACAlrB,KAAAqD,MAAAwmB,kBAAAC,UAAA3nB,IAAA,qBAEAynB,iBA1MA,SAAAA,IA2MA,IAAA5pB,KAAA6Y,0BAAA,CACA7Y,KAAAkrB,oBAAA,MACAlrB,KAAAqD,MAAAwmB,kBAAAC,UAAAC,OACA,sBAIAqB,yBAlNA,SAAAA,IAmNAxpB,QAAAC,IAAA,WAAA7B,KAAAqrB,QAAAhiB,IAAArJ,KAAAsrB,iBACAtrB,KAAA0Z,cAAA1Z,KAAAqrB,QAAAhiB,IAAArJ,KAAAsrB,iBAEAjT,cAtNA,SAAAA,IAsNA,IAAArW,EAAAhC,KACAA,KAAAgqB,cAAAhM,OAAAiI,WAAA,WACArkB,QAAAC,IAAA,qBAEAG,EAAA+V,aAAA,KACA/V,EAAAupB,gBAAAC,SAAAC,WAAAjU,MAAAkU,OAAA,QACA,MAEApT,cA9NA,SAAAA,EA8NAf,GACAyG,OAAA+H,aAAA/lB,KAAAgqB,eACAhqB,KAAA+X,aAAAR,EACAvX,KAAAurB,gBAAAC,SAAAC,WAAAjU,MAAAkU,OAAA,UACA,GACA1rB,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAC,aAAA,IACA,MAEA,IAGAY,OAzOA,SAAAA,EAyOAjB,GACA,GACA,IAAAA,EAAA,KACAvX,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAC,YACA,CACA5X,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAC,YAAA,IACA,OAEA5X,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAC,YACA,IAAAL,EAAA,GAEAvX,KAAAirB,gBAAAjrB,KAAAqZ,eACArZ,KAAA2rB,cAAA3rB,KAAAqZ,gBAEApC,kBAvPA,SAAAA,IAwPAjX,KAAAqD,MAAAwmB,kBAAAC,UAAA3nB,IAAA,kBAEAgV,mBA1PA,SAAAA,IA0PA,IAAAiN,EAAApkB,KACAA,KAAA4rB,qBAAA5N,OAAAiI,WAAA,WACA7B,EAAA/gB,MAAAwmB,kBAAAC,UAAAC,OAAA,kBACA,MAEA8B,oBA/PA,SAAAA,EA+PAtU,GACAvX,KAAAwY,OAAAjB,IAEAuU,sBAlQA,SAAAA,EAkQAvU,GACAvX,KAAAsY,cAAAf,IAEAwU,iBArQA,SAAAA,IAsQA/rB,KAAAqY,iBAEA2T,kBAxQA,SAAAA,IAyQAhsB,KAAAisB,YAEAA,SA3QA,SAAAA,IA4QAjsB,KAAAwY,QAAA,IAEA0T,qBA9QA,SAAAA,EA8QAC,GACA,IAAAC,EAAApsB,KAAAurB,gBAAAC,SAAAC,WACA,IAAAY,EAAAC,WAAAF,EAAA5U,MAAA1H,OACAyc,EAAAD,WAAAF,EAAA5U,MAAA7C,QAEA,IAAAiE,EAAA,IAAApP,MAAAgjB,QACA5T,EAAA5Q,IAAAmkB,EAAAlmB,EAAAkmB,EAAAzlB,EAAAylB,EAAApK,GACAnJ,EAAA6T,eAAA,IAAAjjB,MAAAgjB,QAAA,WAEA5T,EAAA8T,QAAA1sB,KAAAurB,gBAAAoB,QACA/T,EAAA3S,EAAAJ,KAAAmS,OAAAY,EAAA3S,EAAA,GAAAomB,EAAA,GACAzT,EAAAlS,EAAAb,KAAAmS,QAAAY,EAAAlS,EAAA,GAAA6lB,EAAA,GACA3T,EAAAmJ,EAAA,EAEA,OAAAtc,MAAA0mB,EAAA1mB,MAAAmT,UAGAoS,cA/RA,SAAAA,EA+RAmB,EAAAS,EAAAhU,GACA,IAAAiU,EAAA,GACA,IAAAT,EAAApsB,KAAAurB,gBAAAC,SAAAC,WACA,IAAAY,EAAAC,WAAAF,EAAA5U,MAAA1H,OACAyc,EAAAD,WAAAF,EAAA5U,MAAA7C,QACA,IAAAiE,EAAA,IAAApP,MAAAgjB,QACA5T,EAAA5Q,IAAAmkB,EAAAlmB,EAAAkmB,EAAAzlB,EAAA,KACAkS,EAAA6T,eAAA,IAAAjjB,MAAAgjB,QAAA,OAAAK,GAGAjrB,QAAAC,IAAA,MAAA+W,EAAAyT,EAAAE,GACA,GAAAK,EAAA,CACA,IAAAE,EAAA,IAAAtjB,MAAAgjB,QACAM,EAAA9kB,IACAmkB,EAAAlmB,EAAAomB,EAAA,MACAF,EAAAzlB,EAAA6lB,GAAA,IACA,IAEAO,EAAAC,UAAA/sB,KAAAurB,gBAAAoB,QAGAG,EAAAE,IAAAhtB,KAAAurB,gBAAAoB,OAAArV,UAAA2V,YACA,IAAAC,EAAA,IAAA1jB,MAAA2jB,IACAntB,KAAAurB,gBAAAoB,OAAArV,SACAwV,GAEA,IAAAhqB,EAAA,IAAA0G,MAAAgjB,QACAU,EAAAE,oBAAA,IAAA5jB,MAAAgjB,QAAA,OAAA1pB,GACA,OAAAA,MACA,CACA8V,EAAA8T,QAAA1sB,KAAAurB,gBAAAoB,QAEA/T,EAAA3S,EAAAJ,KAAAmS,OAAAY,EAAA3S,EAAA,GAAAomB,EAAA,GACAzT,EAAAlS,EAAAb,KAAAmS,QAAAY,EAAAlS,EAAA,GAAA6lB,EAAA,GACA3T,EAAAmJ,EAAA,EAGA,OAAAnJ,GAGA0S,eAvUA,SAAAA,EAuUAa,GAEAA,EAAAzlB,EAAA,EACA,IAAA0lB,EAAApsB,KAAAurB,gBAAAC,SAAAC,WACA,IAAAY,EAAAC,WAAAF,EAAA5U,MAAA1H,OACAyc,EAAAD,WAAAF,EAAA5U,MAAA7C,QAEA,IAAAiE,EAAA,IAAApP,MAAAgjB,QACA5T,EAAA5Q,IAAAmkB,EAAAlmB,EAAAkmB,EAAAzlB,EAAAylB,EAAApK,GACAnJ,EAAA6T,eAAA,IAAAjjB,MAAAgjB,QAAA,WAEA5T,EAAA8T,QAAA1sB,KAAAurB,gBAAAoB,QACA/T,EAAA3S,EAAAJ,KAAAmS,OAAAY,EAAA3S,EAAA,GAAAomB,EAAA,GACAzT,EAAAlS,EAAAb,KAAAmS,QAAAY,EAAAlS,EAAA,GAAA6lB,EAAA,GACA3T,EAAAmJ,EAAA,EACA,OAAAnJ,GAGA3X,OAzVA,SAAAA,IA0VA,IAAAjC,EAAA,CACAyC,KAAAzB,KAAA8Y,YACApY,GAAAV,KAAAqtB,UACAvd,MAAA9P,KAAAmZ,aACAxE,OAAA3U,KAAAoZ,cACArJ,KAAA/P,KAAA+P,KACAqa,SAAApqB,KAAAoqB,UAEA,GAAApqB,KAAA8Y,cAAA,QACAxX,EAAA,KAAAC,IAAAV,IAAAkkB,KAAA/lB,GAAA2C,KACA3B,KAAAmkB,gBACA,gBAEA,GAAAnkB,KAAA8Y,cAAA,aACAxX,EAAA,KAAAC,IAAAV,IAAAyD,UAAAtF,GAAA2C,KACA3B,KAAAmkB,gBACA,aAIAkG,mBA9WA,SAAAA,EA8WA5oB,EAAAgE,GACA,IAAA6nB,EAAA,GACA,GACAhsB,EAAA,KAAA8c,YAAAkP,SAAAvlB,IAAA,gBACA/H,KAAA8Y,cAEAxX,EAAA,KAAA8c,YAAAkP,SAAAvlB,IAAA,gBAAA/H,KAAA8Y,aACA9Y,KAAAqtB,WAEA,CACAC,EAAAhsB,EAAA,KAAA8c,YAAAkP,SAAAvlB,IAAA,gBACA/H,KAAA8Y,aACA9Y,KAAAqtB,WAEA/rB,EAAA,KAAA8c,YAAAkP,SAAAC,cACA,eACAvtB,KAAA8Y,YACAhY,OAAAgI,OACA,GADAya,IAAA,GAGAvjB,KAAAqtB,UAHA/J,IAAA,GAIAgK,EAJA/J,IAAA,GAKA9hB,EAAAgE,QAMA0e,gBA1YA,SAAAA,EA0YAqJ,GAAA,IAAAxI,EAAAhlB,KACA4B,QAAAC,IAAA,YACA,IAAA2rB,EAAA,OACAxtB,KAAAiZ,WAAAuU,EAAAC,qBACA,IAAAztB,KAAA0tB,gBAAA,CACA,IACA1tB,KAAAkZ,cACAlZ,KAAAiZ,aAAA,YACAjZ,KAAAkZ,aAAAyU,WAAA,KACA,CACA3tB,KAAAkZ,aAAA,WACA,IACAlZ,KAAAkZ,cACAlZ,KAAAiZ,aAAA,YACAjZ,KAAAkZ,aAAAyU,WAAA,KACA,CACA3tB,KAAAkZ,aAAA,OAGAlZ,KAAA0tB,gBAAAF,EAEA,OAAAI,SAAA5tB,KAAA+P,OACA,OACA/P,KAAA6tB,iBAAAtrB,KAAA,SAAAipB,GACA,IAAAsC,EAAAhtB,OAAAitB,EAAA,eAAAjtB,CACA,CACAktB,cAAAR,GAEA,CACA1d,MAAAkV,EAAA7L,aACAxE,OAAAqQ,EAAAiJ,SACAjJ,EAAA5L,cACA,KACAI,MAAAwL,EAAAxL,MACA0U,WAAA,KACAC,QAAAnJ,EAAAqI,UACAe,UAAApJ,EAAAlM,YACAuV,oBAAArJ,EAAAjB,mBACAC,iBAAAgB,EAAAhB,iBACA1K,WAAA0L,EAAAiJ,SACAjJ,EAAA1L,WACA,KACAC,QAAAyL,EAAAiJ,SAAAjJ,EAAAzL,QAAA,KACAE,OAAAuL,EAAAvL,OACA6U,2BAAAtJ,EACAkF,yBACAlF,EAAAkF,yBACA,MAEA,CACAqE,OAAA,YAMA,GACAvJ,EAAAmF,kBACAnF,EAAAmF,iBAAA,EACA,CACA,IAAAqE,EAAAxJ,EAAA3L,cACAiE,WAAA0H,EAAAmF,iBAAA,GACAnK,YAHA,IAAAwD,EAIA,sCAAAE,EAAA,EAAAA,EAAAF,EAAA3jB,OAAA6jB,IAAA,KAAAzd,EAAAud,EAAAE,GAAA,IAAA+K,EAAA,SAAAC,EAAA,UAAAC,EAAAvhB,UAAA,IACA,QAAAwhB,EAAAd,EAAA7nB,GAAA+F,OAAA6iB,YAAAC,IAAAL,GAAAK,EAAAF,EAAAxrB,QAAA2rB,MAAAN,EAAA,UAAAO,EAAAF,EAAArpB,MACA,GACAupB,EAAAhP,aAAAwO,EACA,CACAQ,EAAAC,SAAA,OALA,MAAAhhB,GAAAygB,EAAA,KAAAC,EAAA1gB,EAAA,gBAAAwgB,GAAAG,EAAAM,QAAA,MAAAN,EAAAM,UAAA,WAAAR,EAAA,OAAAC,MAUA,IAAAQ,EAAAvtB,QAAAiS,KACAjS,QAAAiS,KAAA,aACAjS,QAAAC,IAAA,oBACA2pB,EAAA4D,aACAtB,EACA9I,EAAA9L,aACA8L,EAAAqK,gBAAAC,MACAtK,EAAAqK,gBAAA1C,OACA3H,EAAAqK,gBAAA7D,UAGAA,EAAAK,oBAAA7G,EAAA6G,oBACAL,EAAAM,sBAAA9G,EAAA8G,sBACAN,EAAAO,iBAAA/G,EAAA+G,iBACAP,EAAAQ,kBAAAhH,EAAAgH,kBAEApqB,QAAAiS,KAAAsb,EACAnK,EAAAuK,QAAA/D,EAAAgE,UAEAxK,EAAAuG,gBAAAvG,EAAAqK,gBACArK,EAAArL,MAAA6R,EAAAiE,SAAA3B,GACA9I,EAAAqG,QAAAG,EAAAkE,6BAEA1K,EAAA0I,gBAAAI,EACA9I,EAAA3L,cAAAyU,EACA9I,EAAAoG,6BAEA,MAEAprB,KAAAoX,aAAA,MAEAoS,gBAlfA,SAAAA,IAmfA,GACAloB,EAAA,KAAA8c,YAAAkP,SAAAvlB,IAAA,gBACA/H,KAAA8Y,cAEAxX,EAAA,KAAA8c,YAAAkP,SAAAvlB,IAAA,gBAAA/H,KAAA8Y,aACA9Y,KAAAqtB,WAEA,CACA,IAAAC,EAAAhsB,EAAA,KAAA8c,YAAAkP,SAAAvlB,IAAA,gBACA/H,KAAA8Y,aACA9Y,KAAAqtB,WACA,QAAAhmB,KAAAimB,EAAA,CACA,GAAAA,EAAAvgB,eAAA1F,GAAA,CACArH,KAAAqH,GAAAimB,EAAAjmB,OAMAsoB,8BAtgBA,SAAAA,MAwgBA/E,mCAxgBA,SAAAA,IAygBA,GACA5qB,KAAA+qB,+BAAA,GACA/qB,KAAAwqB,wBAAA3qB,QAAA,EACA,CACA+B,QAAAC,IACA,wEAEA7B,KAAAyqB,0BACA,OAEA,IAAAmF,EAAA5vB,KAAA+qB,8BAAA,EACA,IAAAlJ,EAAA,GACA,GAAA+N,GAAA5vB,KAAAwqB,wBAAA3qB,OAAA,CACAgiB,EAAA7hB,KAAAwqB,wBAAAoF,GACA5vB,KAAA+qB,8BAAA6E,MACA,CACA5vB,KAAA+qB,8BACA/qB,KAAAwqB,wBAAA3qB,OAAA,EACAgiB,EAAA7hB,KAAAwqB,wBACAxqB,KAAA+qB,+BAGA/qB,KAAA0qB,2BAAA7I,IAGAgJ,+BAliBA,SAAAA,IAmiBA,GACA7qB,KAAA+qB,8BACA/qB,KAAAwqB,wBAAA3qB,OACA,CACA+B,QAAAC,IACA,0DAEA7B,KAAAyqB,0BACA,OAEAzqB,KAAA+qB,+BAAA,EACA,IAAAlJ,EAAA7hB,KAAAwqB,wBACAxqB,KAAA+qB,+BAEA/qB,KAAA0qB,2BAAA7I,IAGA6I,2BApjBA,SAAAA,EAojBA7I,GACA/gB,OAAAgI,OAAA9I,KAAA6hB,GACA7hB,KAAAyqB,0BACAzqB,KAAAiB,UAGAwpB,wBA1jBA,SAAAA,IA2jBAzqB,KAAA6Z,sBACA7Z,KAAA+qB,8BAAA,GACA/qB,KAAAwqB,yBACAxqB,KAAAwqB,wBAAA3qB,SAAA,EACAG,KAAA8Z,kBACA9Z,KAAA+qB,8BACA/qB,KAAAwqB,wBAAA3qB,OAAA,EACAG,KAAA+Z,uBAAA,CACA/Z,KAAA+qB,8BACA/qB,KAAAwqB,wBAAA3qB,OAAA,IAIA0pB,kCAxkBA,SAAAA,EAwkBAoB,GAAA,IAAAkF,EAAA5rB,UAAApE,OAAA,GAAAoE,UAAA,KAAAmJ,UAAAnJ,UAAA,QACA,GACA0mB,GACA3qB,KAAA+qB,gCACA/qB,KAAAwqB,wBAAA3qB,OAAA,EACA,CACAG,KAAAwqB,wBAAA3qB,OACAG,KAAA+qB,8BAAA,EAEA,IAAA+E,EAAA,CACA5W,aAAAlZ,KAAAkZ,aACAC,aAAAnZ,KAAAmZ,aACAC,cAAApZ,KAAAoZ,cACAG,QAAAvZ,KAAAuZ,QACAC,MAAAxZ,KAAAwZ,MACAC,OAAAzZ,KAAAyZ,OACAH,WAAAtZ,KAAAsZ,WACA4Q,yBAAAlqB,KAAAkqB,0BAEA,GAAA2F,IAAA,KACA7vB,KAAAwqB,wBAAAxZ,KAAA8e,QACA9vB,KAAAwqB,wBAAAuF,OAAAF,EAAA,EAAAC,GAEA9vB,KAAA+qB,8BACA/qB,KAAAwqB,wBAAA3qB,OAAA,EACAG,KAAAyqB,2BAGAhB,aApmBA,SAAAA,IAomBA,IAAAuG,EAAAhwB,KACA,GACAA,KAAAiwB,IAAAC,cAAA,+BAAAzsB,WACA,CACAzD,KAAAiwB,IACAC,cAAA,+BACAzsB,WAAAsmB,SAEA/pB,KAAA6tB,iBAAA/sB,OAAAqvB,EAAA,KAAArvB,GACA,IAAAsvB,EAAA,IAAAC,EAAA,SACArwB,KAAAqvB,gBAAAe,EAAApoB,IAAA,CACAsoB,UAAAtwB,KAAAiwB,IAAAC,cACA,+BAEApgB,MAAA9P,KAAAuwB,OACA5b,OAAA3U,KAAAwwB,QACAC,WAAAzwB,KAAA4Z,OACA8W,sBAAA,SAAAA,IACAV,EAAA5E,2BACA4E,EAAA/E,gBAAA+E,EAAA3W,eACA2W,EAAAW,oBAGA,IAAAxB,EAAAvtB,QAAAiS,KACAjS,QAAAiS,KAAA,aAEA7T,KAAAqvB,gBAAAuB,aACA5wB,KAAA6tB,iBAAAtrB,KAAA,SAAAipB,GACAA,EAAAqF,SAAAb,EAAAX,gBAAAC,OACA9D,EAAAsF,WAAAd,EAAAX,gBAAAC,OACA9D,EAAAuF,mBAAAf,EAAAX,gBAAA2B,UAEApvB,QAAAiS,KAAAsb,EACAnR,OAAAmH,iBAAA,oBACA,IAAA7Y,EAAA0jB,EAAAC,IAAAgB,wBACAjB,EAAAX,gBAAA6B,OAAA,CACAphB,MAAAxD,EAAAwD,MAAA,GACA6E,OAAArI,EAAAqI,WAGA3U,KAAAiB,UAEAqpB,eA9oBA,SAAAA,IA+oBA,GAAAtqB,KAAAoqB,WAAA,OACApqB,KAAA6tB,iBAAAtrB,KAAA,SAAAipB,GAAA,OACAA,EAAA2F,gBAAA,UAEA,GAAAnxB,KAAAoqB,WAAA,cACApqB,KAAA6tB,iBAAAtrB,KAAA,SAAAipB,GAAA,OACAA,EAAA2F,gBAAA,OAKAxF,cA1pBA,SAAAA,EA0pBAjd,KAEAiiB,eA5pBA,SAAAA,MA8pBA1F,gBA9pBA,SAAAA,EA8pBAvc,GACA,IAAA0iB,EAAA,GACAC,EAAA,GACA,IAAApC,EAAAjvB,KAAAqD,MAAAqU,cAAArU,MAAAsU,cACAC,YACA,GAAA5X,KAAAkrB,oBAAA,CACAxc,EAAA4O,WAAAxP,QAAA,SAAAtL,GAAA,IAAA8uB,EAAA9uB,EAAA8uB,aACAA,EAAAxjB,QAAA,SAAAwW,GAAA,IAAA3P,EAAA2P,EAAA3P,OAAA7E,EAAAwU,EAAAxU,MACAshB,EAAA,GAAA3b,OAAAqN,IAAAsO,GAAA,CAAAthB,IACAuhB,EAAA,GAAA5b,OAAAqN,IAAAuO,GAAA,CAAA1c,YAGA,CACA,GAAAsa,OAAA,KACAvgB,EAAA4O,WACA1c,OAAAquB,GAAA,GACAqC,aAAAxjB,QAAA,SAAA2W,GAAA,IAAA9P,EAAA8P,EAAA9P,OAAA7E,EAAA2U,EAAA3U,MACAshB,EAAA,GAAA3b,OAAAqN,IAAAsO,GAAA,CAAAthB,IACAuhB,EAAA,GAAA5b,OAAAqN,IAAAuO,GAAA,CAAA1c,OAIA3U,KAAAmrB,cACAnrB,KAAAuxB,mBAAAH,EAAA/nB,IAAArJ,KAAAksB,sBACAlsB,KAAAwxB,oBAAAH,EAAAhoB,IAAArJ,KAAAksB,wBAIA/nB,MAAA,CACA0U,0BADA,SAAAA,EACA4Y,GACAzxB,KAAAirB,gBAAAjrB,KAAAqZ,eACA,IAAAoY,EAAA,CACAzxB,KAAAkrB,oBAAA,MACAlrB,KAAAqD,MAAAwmB,kBAAAC,UAAAC,OACA,wBAEA,CACA/pB,KAAAkrB,oBAAA,KACAlrB,KAAAqD,MAAAwmB,kBAAAC,UAAA3nB,IAAA,sBAGAiV,aAbA,SAAAA,EAaAqa,GACA,GAAAA,EAAA,CACAzxB,KAAAqD,MAAAquB,eAAA5H,UAAAC,OAAA,iBAGAK,SAlBA,SAAAA,IAmBApqB,KAAAsqB,kBAEAY,oBArBA,SAAAA,IAsBAlrB,KAAAiB,UAEAoY,cAxBA,SAAAA,EAwBA3K,GACA1O,KAAAirB,gBAAAvc,GACA1O,KAAA2rB,cAAAjd,KAIAxK,QAjuBA,SAAAA,IAkuBA,GACAlE,KAAAyW,OAAA4D,OAAA5Y,OAAA,QACAzB,KAAAyW,OAAA4D,OAAA5Y,OAAA,YACA,CACAzB,KAAA2xB,QAAA3gB,KAAA,KAEAhR,KAAAka,eAAAla,KAAAyW,OAAAC,OAAA,gBAGAtS,QA3uBA,SAAAA,IA2uBA,IAAAwtB,EAAA5xB,KAAA,IAAA6xB,EACA7xB,KAAAyW,OAAA4D,OAAA5Y,EADAowB,EACApwB,KAAAf,EADAmxB,EACAnxB,GACAV,KAAA8Y,YAAArX,EACAzB,KAAAiuB,SAAAxsB,IAAA,OACAzB,KAAAqtB,UAAA3sB,EACAV,KAAAqD,MAAAqU,cAAArU,MAAAsU,cAAAsY,IAAA9K,iBACA,YACA,SAAA1Z,GACAuS,OAAA+H,aAAA6L,EAAAhG,wBAGA,GAAA5rB,KAAA8Y,cAAA,aACAxX,EAAA,KAAAC,IAAAuwB,eAAA9xB,KAAAqtB,WAAA0E,UAAA,SAAAC,GACA/f,SAAAggB,MAAA,eAAAxc,OAAAuc,EAAAtb,MACAkb,EAAA7Y,QAAAiZ,EACAJ,EAAA1I,qBAEA,GAAAlpB,KAAA8Y,cAAA,QACAxX,EAAA,KAAAC,IAAAwjB,KAAA/kB,KAAAqtB,WAAA0E,UAAA,SAAAC,GACA/f,SAAAggB,MAAA,UAAAxc,OAAAuc,EAAAtb,MACAkb,EAAA7Y,QAAAiZ,EACAJ,EAAA1I,kBAKA9mB,KArwBA,SAAAA,IAswBA,OACA4W,YAAA,CACAkZ,KAAA,CAAAjsB,EAAA,EAAAS,EAAA,GACAyrB,MAAA,CAAAlsB,EAAA,EAAAS,EAAA,IAGA8jB,wBAAA,GACAO,8BAAA,EACAlR,sBAAA,MACAC,kBAAA,MACAC,uBAAA,MACAL,cAAA,GACAZ,YAAA,OACAyX,OAAAvS,OAAAoU,WAAA,MACA5B,QAAAxS,OAAAqU,YACAlZ,aAAA,KACAC,cAAA,KACAiU,UAAA,KACAY,SAAA,KACAle,KAAA,EACAqa,SAAA,MACAlR,aAAA,MACAwU,gBAAA,KACA2B,gBAAA,KACAhW,cAAA,KACAiZ,UAAA,MACAzE,iBAAA,KACAzW,aAAA,MACA2B,QAAA,KACAmR,yBAAA,KACAC,iBAAA,EACAlR,WAAA,KACAK,WAAA,IACAC,QAAA,IACAC,MAAA,IACAC,OAAA,KACAE,MAAA,IACAC,OAAA,KACAmK,mBAAA,MACAC,iBAAA,KACAiG,aAAA,KACAD,cAAA,KACA9P,eAAA,KACAqX,mBAAA,GACAC,oBAAA,GACAtG,oBAAA,MACArS,0BAAA,MACA+S,qBAAA,KACA7T,aAAA,KACAlB,gBAAA,SCriC4O,IAAA0b,GAAA,oBCQ5O,IAAIC,GAAY1xB,OAAAyD,EAAA,KAAAzD,CACdyxB,GACAzyB,EACAS,EACF,MACA,KACA,KACA,MAIe,IAAAkyB,GAAAhuB,EAAA,WAAA+tB,mCCnBf,IAAAE,EAAmBjzB,EAAQ,QAC3B,IAAAkzB,EAAelzB,EAAQ,QACvB,IAAAmzB,EAAoBnzB,EAAQ,QAC5B,IAAAkT,EAAalT,EAAQ,QACrB,IAAAqJ,EAAarJ,EAAQ,QAErB,IAAAozB,EAAepzB,EAAQ,QACvB,IAAAqzB,EAAYrzB,EAAQ,QAEpB,IAAAszB,EAAAvpB,MAAAwpB,eAEAl0B,EAAAC,QAAA,SAAAk0B,EAAAjqB,GACA,WAAAkqB,EAAAlqB,IAGA,SAAAkqB,EAAAlqB,GACA+pB,EAAAxnB,KAAAvL,MAEA,UAAAgJ,IAAA,UACAA,EAAA,CAAWwG,KAAAxG,GAKXhJ,KAAAmzB,KAAArqB,EAAA,GAAuBE,GAGvB,GAAAA,EAAAhJ,KAAAiB,OAAA+H,GAGA2pB,EAAAO,EAAAH,GAEAG,EAAAzqB,UAAAxH,OAAA,SAAA+H,GACA,UAAAA,IAAA,UACAA,EAAA,CAAWwG,KAAAxG,GAIXA,EAAAF,EAAA,GAAiB9I,KAAAmzB,KAAAnqB,GAEjB,IAAAA,EAAAoqB,KAAA,CACA,UAAA/qB,UAAA,sCAGArI,KAAAqzB,OAAAX,EAAA1pB,GAGA,IAAAsqB,EAAAtqB,EAAAsqB,QAAA,MAGA,IAAAF,EAAApqB,EAAAoqB,KAGA,IAAAG,EAAAH,EAAAI,OAAAC,OACA,IAAAC,EAAAN,EAAAI,OAAAG,OAGA,IAAAviB,EAAApR,KAAAqzB,OAAAjiB,OAAAwiB,OAAA,SAAAC,GACA,IAAAC,EAAAD,EAAAzxB,KACA,OAAA0xB,EAAAhkB,MAAAgkB,EAAAnf,OAAA,IAIA3U,KAAA+zB,cAAA3iB,EAGA,IAAArG,EAAA8nB,EAAA9nB,UAAAqG,GACA,IAAArH,EAAA8oB,EAAA9oB,IAAAqH,EAAAmiB,EAAAG,EAAAJ,GACA,IAAAtpB,EAAA4oB,EAAA,CACAoB,UAAA,KACAvyB,KAAA,SACA2S,MAAAhD,EAAAvR,SAIA8S,EAAAK,MAAAhT,KAAAgK,EAAA,YACA2I,EAAAG,KAAA9S,KAAA,WAAA+K,EAAA,GACA4H,EAAAG,KAAA9S,KAAA,KAAA+J,EAAA,GAGA,IAAAf,EAAAirB,WAAA,SAAAj0B,KAAAk0B,WAAA,CAEAl0B,KAAAm0B,gBAAA,aACG,GAAAnrB,EAAAirB,UAAA,CACH,IAAAG,EAAAvB,EAAAuB,MAAAhjB,GAEAuB,EAAAG,KAAA9S,KAAA,OAAAo0B,EAAA,KAIAlB,EAAAzqB,UAAA4rB,sBAAA,WACA,GAAAr0B,KAAAs0B,iBAAA,MACAt0B,KAAAs0B,eAAA,IAAA9qB,MAAA+qB,OAGA,IAAAxpB,EAAA/K,KAAAk0B,WAAA5c,SAAAtD,MACA,IAAAb,EAAAnT,KAAAk0B,WAAA5c,SAAAnE,SACA,IAAApI,IAAAoI,GAAApI,EAAAlL,OAAA,GACAG,KAAAs0B,eAAAxf,OAAA,EACA9U,KAAAs0B,eAAAzf,OAAA7M,IAAA,OACA,OAEA8qB,EAAAxe,cAAAvJ,EAAA/K,KAAAs0B,gBACA,GAAAtpB,MAAAhL,KAAAs0B,eAAAxf,QAAA,CACAlT,QAAA4yB,MAAA,iDACA,+BACA,wDAIAtB,EAAAzqB,UAAAgsB,mBAAA,WACA,GAAAz0B,KAAA00B,cAAA,MACA10B,KAAA00B,YAAA,IAAAlrB,MAAAmrB,KAGA,IAAAC,EAAA50B,KAAA00B,YACA,IAAA3pB,EAAA/K,KAAAk0B,WAAA5c,SAAAtD,MACA,IAAAb,EAAAnT,KAAAk0B,WAAA5c,SAAAnE,SACA,IAAApI,IAAAoI,GAAApI,EAAAlL,OAAA,GACA+0B,EAAAC,YACA,OAEA/B,EAAAze,WAAAtJ,EAAA6pB,4BC1HA,IAAAr1B,EAAaE,EAAQ,QAAQF,OAE7BT,EAAAC,QAAA,SAAA4D,EAAAmyB,GACA,IAAAv1B,EAAAN,SAAA0D,GAAA,OAAAyK,UACA,IAAA7N,EAAAN,SAAA61B,GAAA,OAAA1nB,UACA,UAAAzK,EAAAoyB,SAAA,kBAAApyB,EAAAoyB,OAAAD,GACA,GAAAnyB,EAAA9C,SAAAi1B,EAAAj1B,OAAA,aAEA,QAAA+M,EAAA,EAAmBA,EAAAjK,EAAA9C,OAAc+M,IAAA,CACjC,GAAAjK,EAAAiK,KAAAkoB,EAAAloB,GAAA,aAGA,4DCZA9N,EAAAC,QAAA,SAAAi2B,EAAA5yB,GACA,IAAAA,EACA,UAAA8M,MAAA,oBACA9M,IAAA8I,WAAArC,OAEA,IAAA6F,EAAA,CACA0lB,MAAA,GACAa,MAAA,GACAC,SAAA,IAGA,IAAAzlB,EAAArN,EAAAyL,MAAA,aAEA,GAAA4B,EAAA5P,SAAA,EACA,UAAAqP,MAAA,0BAEA,QAAAtC,EAAA,EAAiBA,EAAA6C,EAAA5P,OAAkB+M,IAAA,CACnC,IAAAuoB,EAAAC,EAAA3lB,EAAA7C,MACA,IAAAuoB,EACA,SAEA,GAAAA,EAAA9tB,MAAA,QACA,UAAA8tB,EAAA/yB,KAAA1B,KAAA,SACA,UAAAwO,MAAA,0BAAAtC,EAAA,uBACA,UAAAuoB,EAAA/yB,KAAAizB,OAAA,SACA,UAAAnmB,MAAA,0BAAAtC,EAAA,8BACA8B,EAAA0lB,MAAAe,EAAA/yB,KAAA1B,IAAAy0B,EAAA/yB,KAAAizB,UACK,GAAAF,EAAA9tB,MAAA,SAAA8tB,EAAA9tB,MAAA,kBAEA,GAAA8tB,EAAA9tB,MAAA,QACLqH,EAAAumB,MAAAjkB,KAAAmkB,EAAA/yB,WACK,GAAA+yB,EAAA9tB,MAAA,WACLqH,EAAAwmB,SAAAlkB,KAAAmkB,EAAA/yB,UACK,CACLsM,EAAAymB,EAAA9tB,KAAA8tB,EAAA/yB,MAIA,OAAAsM,GAGA,SAAA0mB,EAAA1lB,EAAAc,GACAd,IAAAnF,QAAA,YAAA1B,OACA,IAAA6G,EACA,YAEA,IAAA4lB,EAAA5lB,EAAAe,QAAA,KACA,GAAA6kB,KAAA,EACA,UAAApmB,MAAA,wBAAAsB,GAEA,IAAAnJ,EAAAqI,EAAA9P,UAAA,EAAA01B,GAEA5lB,IAAA9P,UAAA01B,EAAA,GAGA5lB,IAAAnF,QAAA,+BACAmF,IAAA7B,MAAA,KACA6B,IAAArG,IAAA,SAAAoI,GACA,OAAAA,EAAA5I,OAAA0sB,MAAA,mCAGA,IAAAnzB,EAAA,GACA,QAAAwK,EAAA,EAAiBA,EAAA8C,EAAA7P,OAAiB+M,IAAA,CAClC,IAAA4oB,EAAA9lB,EAAA9C,GACA,GAAAA,IAAA,GACAxK,EAAA4O,KAAA,CACA3J,IAAAmuB,EAAA,GACApzB,KAAA,UAEK,GAAAwK,IAAA8C,EAAA7P,OAAA,GACLuC,IAAAvC,OAAA,GAAAuC,KAAAqzB,EAAAD,EAAA,QACK,CACLpzB,IAAAvC,OAAA,GAAAuC,KAAAqzB,EAAAD,EAAA,IACApzB,EAAA4O,KAAA,CACA3J,IAAAmuB,EAAA,GACApzB,KAAA,MAKA,IAAAszB,EAAA,CACAruB,MACAjF,KAAA,IAGAA,EAAA0L,QAAA,SAAAuY,GACAqP,EAAAtzB,KAAAikB,EAAAhf,KAAAgf,EAAAjkB,OAGA,OAAAszB,EAGA,SAAAD,EAAArzB,GACA,IAAAA,KAAAvC,SAAA,EACA,SAEA,GAAAuC,EAAAqO,QAAA,UAAArO,EAAAqO,QAAA,SACA,OAAArO,EAAAxC,UAAA,EAAAwC,EAAAvC,OAAA,GACA,GAAAuC,EAAAqO,QAAA,UACA,OAAAklB,EAAAvzB,GACA,OAAAwrB,SAAAxrB,EAAA,IAGA,SAAAuzB,EAAAvzB,GACA,OAAAA,EAAAyL,MAAA,KAAAxE,IAAA,SAAA8D,GACA,OAAAygB,SAAAzgB,EAAA,2BCzGArO,EAAAC,QAAA62B,EAEA,IAAA1qB,EAAApK,OAAA2H,UAAAyC,SAEA,SAAA0qB,EAAAC,GACA,IAAAC,EAAA5qB,EAAAK,KAAAsqB,GACA,OAAAC,IAAA,4BACAD,IAAA,YAAAC,IAAA,0BACA9X,SAAA,cAEA6X,IAAA7X,OAAAiI,YACA4P,IAAA7X,OAAA+X,OACAF,IAAA7X,OAAAgY,SACAH,IAAA7X,OAAAiY,kSCTA,IAAIC,EAAoB,mqEAExB,IAAIC,EAAwB,wiEAE5B,IAAIC,EAA8B,2bAElC,IAAIC,EAAiC,2bAErC,IAAIC,EAAa,GAEjB,IAAIC,EAA0B,IAAIC,aAClC,IAAIC,EAAe,IAAID,aAEvB,IAAIE,EAAU,IAAIF,aAClB,IAAIG,EAAmB,IAAIH,aAM3B,SAASI,EAAuBpL,EAAUqL,EAAeC,GAErD,IAAIC,EAAevL,EAASmB,OAC5B,IAAIqK,EAAUxL,EAAS3qB,IAEvB,IAAIo2B,EAAQ,CACRA,MAAO,CAQHC,OAAQ,KACRC,KAAM,CACFC,OAAQ,YACRC,SAAU,EACVC,OAAQ,sBACRC,SAAU,GAEdC,SAAU,CACNC,IAAK,MACLC,MAAO,OAEXtuB,MAAO,CAAC,EAAG,EAAG,EAAG,KAIzB,IAAIuuB,EAAW,SAASA,IACpB,MAAO,CAACnM,EAASoM,IAAI9nB,MAAQ,EAAG0b,EAASoM,IAAIC,YAAc,EAAG,IAAM,IAGxE,IAAIC,EAAKC,IAAKlB,GAEd,IAAImB,EAAiB,GAErB,IAAK,IAAIlpB,EAAI,EAAGA,EAAIwnB,EAAYxnB,IAAK,CACjCkpB,EAAe,UAAYlpB,EAAI,KAAOgpB,EAAG1O,KAAK,SAAWta,GAG7D,IAAIvF,EAAWzI,OAAOgI,OAAO,CAIzBmvB,eAAgBH,EAAG1O,KAAK,kBAExB8O,SAAUJ,EAAG1O,KAAK,YAClB+O,UAAWL,EAAG1O,KAAK,cACpB4O,EAAgB,CAEfI,aAAcN,EAAG1O,KAAK,gBACtBiP,YAAaP,EAAG1O,KAAK,eAErBkP,KAAMR,EAAG1O,KAAK,UAGlB,IAAImP,EAAkBT,EAAGh3B,OAAOgI,OAAO,CAEnC+B,KAAMisB,EAAkBX,EAAwBD,EAEhDtrB,KAAMksB,EAAkBT,EAAiCD,EAEzDlC,WAAY,CACR5c,SAAUwgB,EAAGnlB,OAAO6lB,IAAK1uB,OACzBC,IAAK+tB,EAAGnlB,OAAO6lB,IAAKzuB,MAGxBR,SAAUA,EACV6K,MAAO,GAER6iB,IAEH,IAAIn3B,EAAS,SAASA,EAAO0C,GACzB,IAAI8qB,EAAW9qB,EAAK8qB,SAGpBwK,EAAGW,MAAM,CACLjf,MAAO,EACPpQ,MAAO,CAAC,EAAG,EAAG,EAAG,KAGrB,IAAIsvB,EAAS1B,EAAQ2B,WAAWhB,KAChC,IAAIiB,EAAe,GAEnB,IAAK,IAAIvS,EAAI,EAAGA,EAAIiQ,EAAYjQ,IAAK,CACjC,GAAIqS,EAAOrS,GAAI,CACXuS,EAAa,SAAWvS,GAAKqS,EAAOrS,OACjC,CACHuS,EAAa,SAAWvS,GAAK,CAAC,EAAG,EAAG,EAAG,IAI/CkS,EAAgBz3B,OAAOgI,OAAO,CAE1BqvB,UAAWR,IAEXU,YAAa/K,EAASuL,MACtBX,SAAU5K,EAAS4K,SAEnBD,eAAgB,CAAC,EAAG,EAAG3K,EAASwL,QAAU,GAE1CV,aAAc,CAAC9K,EAASrnB,EAAGqnB,EAAS5mB,EAAG4mB,EAASvL,EAAG,GACnDuW,KAAM,CAAChL,EAASyL,MAAOzL,EAAS0L,QAEjCJ,KAGP,IAAIK,EAAgB,SAASA,IACzB,OAAO,OAEX,IAAIC,EAAa,SAASA,IACtB,OAAO,OAGX,MAAO,CAAEp5B,OAAQA,EAAQm5B,cAAeA,EAAeC,WAAYA,mBCjIvE,IAAI1vB,EAAQwU,OAAOxU,OAAS2vB,EAAQ,QAEpC,IAAIC,EAAoB,SAApBA,EAA8BjN,EAAQV,GAA0B,IAAd4N,EAAcp1B,UAAApE,OAAA,GAAAoE,UAAA,KAAAmJ,UAAAnJ,UAAA,GAAP,MAE5D,IAAI7C,EAAQpB,KACZ,IAAIs5B,EAAQ,CAAEC,MAAQ,EAAGC,OAAQ,EAAGC,KAAM,EAAGC,IAAK,EAAGC,aAAc,EAAGC,eAAgB,GAEtF55B,KAAKmsB,OAASA,EACdnsB,KAAKyrB,WAAcA,IAAere,UAAaqe,EAAaxZ,SAG5DjS,KAAK4Z,OAASyf,EACdr5B,KAAK65B,QAAU,KAEf75B,KAAK85B,OAAS,CAAE5H,KAAM,EAAG6H,IAAK,EAAGjqB,MAAO,EAAG6E,OAAQ,GAEnD3U,KAAKg6B,YAAc,EACnBh6B,KAAKi6B,UAAY,IACjBj6B,KAAKk6B,SAAW,GAEhBl6B,KAAKm6B,SAAW,MAChBn6B,KAAKo6B,OAAS,MACdp6B,KAAKq6B,MAAQ,MAEbr6B,KAAKs6B,aAAe,MACpBt6B,KAAKu6B,qBAAuB,GAE5Bv6B,KAAKw6B,YAAc,EACnBx6B,KAAKy6B,YAAcC,SAOnB16B,KAAKgO,KAAO,CAAC,GAAU,GAAU,IAIjChO,KAAKkO,OAAS,IAAI1E,EAAMgjB,QAExB,IAAImO,EAAM,KAEV,IAAIC,EAAe,IAAIpxB,EAAMgjB,QAE7B,IAAIqO,EAASvB,EAAMC,KAClBuB,EAAaxB,EAAMC,KAEnBwB,EAAO,IAAIvxB,EAAMgjB,QAEjBwO,EAAY,IAAIxxB,EAAMyxB,QACtBC,EAAY,IAAI1xB,EAAMyxB,QAEtBE,EAAY,IAAI3xB,EAAMgjB,QACtB4O,EAAa,EAEbC,EAAa,IAAI7xB,EAAMyxB,QACvBK,EAAW,IAAI9xB,EAAMyxB,QAErBM,EAA0B,EAC1BC,EAAwB,EAExBC,EAAY,IAAIjyB,EAAMyxB,QACtBS,EAAU,IAAIlyB,EAAMyxB,QAIrBj7B,KAAK27B,QAAU37B,KAAKkO,OAAO0tB,QAC3B57B,KAAK67B,UAAY77B,KAAKmsB,OAAO7U,SAASskB,QACtC57B,KAAK87B,IAAM97B,KAAKmsB,OAAO4P,GAAGH,QAI1B,IAAII,EAAc,CAAEv6B,KAAM,UAC1B,IAAIw6B,EAAa,CAAEx6B,KAAM,SACzB,IAAIy6B,EAAW,CAAEz6B,KAAM,OAMvBzB,KAAKm8B,aAAe,WAEnB,GAAIn8B,KAAKyrB,aAAexZ,SAAU,CAEjCjS,KAAK85B,OAAO5H,KAAO,EACnBlyB,KAAK85B,OAAOC,IAAM,EAClB/5B,KAAK85B,OAAOhqB,MAAQkO,OAAOoU,WAC3BpyB,KAAK85B,OAAOnlB,OAASqJ,OAAOqU,gBAEtB,CAEN,IAAIne,EAAMlU,KAAKyrB,WAAWwF,wBAE1B,IAAImL,EAAIp8B,KAAKyrB,WAAW4Q,cAAcC,gBACtCt8B,KAAK85B,OAAO5H,KAAOhe,EAAIge,KAAOlU,OAAOue,YAAcH,EAAEI,WACrDx8B,KAAK85B,OAAOC,IAAM7lB,EAAI6lB,IAAM/b,OAAOye,YAAcL,EAAEM,UACnD18B,KAAK85B,OAAOhqB,MAAQoE,EAAIpE,MACxB9P,KAAK85B,OAAOnlB,OAAST,EAAIS,SAM3B3U,KAAK28B,YAAc,SAAUpb,GAE5B,UAAWvhB,KAAKuhB,EAAM9f,OAAS,WAAY,CAE1CzB,KAAKuhB,EAAM9f,MAAM8f,KAMnB,IAAIqb,EAAoB,WAEvB,IAAIC,EAAS,IAAIrzB,EAAMyxB,QAEvB,OAAO,SAAS2B,EAAiBE,EAAOC,GAEvCF,EAAO70B,KACL80B,EAAQ17B,EAAM04B,OAAO5H,MAAQ9wB,EAAM04B,OAAOhqB,OAC1CitB,EAAQ37B,EAAM04B,OAAOC,KAAO34B,EAAM04B,OAAOnlB,QAG3C,OAAOkoB,GAXe,GAiBxB,IAAIG,EAAoB,WAEvB,IAAIH,EAAS,IAAIrzB,EAAMyxB,QAEvB,OAAO,SAAS+B,EAAiBF,EAAOC,GAEvCF,EAAO70B,KACJ80B,EAAQ17B,EAAM04B,OAAOhqB,MAAQ,GAAM1O,EAAM04B,OAAO5H,OAAS9wB,EAAM04B,OAAOhqB,MAAQ,KAC9E1O,EAAM04B,OAAOnlB,OAAS,GAAKvT,EAAM04B,OAAOC,IAAMgD,IAAU37B,EAAM04B,OAAOhqB,OAGxE,OAAO+sB,GAXe,GAiBxB78B,KAAKi9B,aAAgB,WAEpB,IAAIC,EAAO,IAAI1zB,EAAMgjB,QACpB2Q,EAAa,IAAI3zB,EAAM4zB,WACvBC,EAAe,IAAI7zB,EAAMgjB,QACzB8Q,EAAoB,IAAI9zB,EAAMgjB,QAC9B+Q,EAA0B,IAAI/zB,EAAMgjB,QACpCgR,EAAgB,IAAIh0B,EAAMgjB,QAC1BiR,EAED,IAAIC,EAAY,EAEhB,SAAST,IAER,IAAIU,EAAoBH,EAAc5B,QACtC4B,EAAcx1B,IAAIkzB,EAAUj1B,EAAI+0B,EAAU/0B,EAAGi1B,EAAUx0B,EAAIs0B,EAAUt0B,EAAG,GACxE+2B,EAAQD,EAAc39B,SAEtB,IAAI+9B,EAAO,GACX,IAAIC,EAAU,MACdH,GAAaD,GAASP,EAAKx2B,EAAI,EAAI,GAAK,GACxC,IAAIo3B,EAAaJ,GAAa,IAAM73B,KAAKk4B,IACzCD,EAAaj4B,KAAKoK,KAAK2tB,EAAM/3B,KAAKwL,IAAIysB,EAAYF,IAYlD,GAAIH,EAAO,CAEV1C,EAAKiD,KAAK58B,EAAM+qB,OAAO7U,UAAU0V,IAAI5rB,EAAM8M,QAE3CmvB,EAAaW,KAAKjD,GAAM9N,YACxBqQ,EAAkBU,KAAK58B,EAAM+qB,OAAO4P,IAAI9O,YACxCsQ,EAAwBU,aAAaX,EAAmBD,GAAcpQ,YAEtEqQ,EAAkBY,UAAUhD,EAAUx0B,EAAIs0B,EAAUt0B,GACpD62B,EAAwBW,UAAUhD,EAAUj1B,EAAI+0B,EAAU/0B,GAE1D,GAAIjG,KAAK4Z,OAAQ,CAChB4jB,EAAcQ,KAAKT,OACb,CACNC,EAAcQ,KAAKV,EAAkBn7B,IAAIo7B,IAG1CL,EAAKe,aAAaT,EAAezC,GAAM9N,YAEvCkQ,EAAWgB,iBAAiBjB,EAAMO,GAElC1C,EAAKqD,gBAAgBjB,GACrB,IAAKn9B,KAAK4Z,OAAQxY,EAAM+qB,OAAO4P,GAAGqC,gBAAgBjB,GAElDhC,EAAU6C,KAAKd,GACf9B,EAAaqC,OAEP,IAAKr8B,EAAMk5B,cAAgBc,EAAY,CAE7CA,GAAcv1B,KAAK+O,KAAK,EAAMxT,EAAMm5B,sBACpCQ,EAAKiD,KAAK58B,EAAM+qB,OAAO7U,UAAU0V,IAAI5rB,EAAM8M,QAC3CivB,EAAWgB,iBAAiBhD,EAAWC,GACvCL,EAAKqD,gBAAgBjB,GACrB/7B,EAAM+qB,OAAO4P,GAAGqC,gBAAgBjB,GAIjCnC,EAAUgD,KAAK9C,GAGhB,OAAO+B,EA1Ea,GAiFrBj9B,KAAKq+B,WAAa,WAEjB,IAAIC,EAEJ,GAAIzD,IAAWvB,EAAMM,eAAgB,CAEpC0E,EAAS/C,EAA0BC,EACnCD,EAA0BC,EAC1BT,EAAKwD,eAAeD,OAEd,CAENA,EAAS,GAAOhD,EAAS50B,EAAI20B,EAAW30B,GAAKtF,EAAM64B,UAEnD,GAAIqE,IAAW,GAAOA,EAAS,EAAK,CAEnCvD,EAAKwD,eAAeD,GAIrB,GAAIl9B,EAAMk5B,aAAc,CAEvBe,EAAW2C,KAAK1C,OAEV,CAEND,EAAW30B,IAAM40B,EAAS50B,EAAI20B,EAAW30B,GAAK1G,KAAKu6B,wBAQtDv6B,KAAKw+B,UAAa,WAEjB,IAAIC,EAAc,IAAIj1B,EAAMyxB,QAC3ByD,EAAW,IAAIl1B,EAAMgjB,QACrBmS,EAAM,IAAIn1B,EAAMgjB,QAEjB,OAAO,SAASgS,IAEfC,EAAYT,KAAKtC,GAAS1O,IAAIyO,GAE9B,GAAIgD,EAAYG,WAAY,CAE3BH,EAAYF,eAAexD,EAAKl7B,SAAWuB,EAAM84B,UAEjDyE,EAAIX,KAAKjD,GAAM8D,MAAMz9B,EAAM+qB,OAAO4P,IAAImC,UAAUO,EAAYx4B,GAC5D04B,EAAIx8B,IAAIu8B,EAASV,KAAK58B,EAAM+qB,OAAO4P,IAAImC,UAAUO,EAAY/3B,IAE7DtF,EAAM+qB,OAAO7U,SAASnV,IAAIw8B,GAC1Bv9B,EAAM8M,OAAO/L,IAAIw8B,GAEjB,GAAIv9B,EAAMk5B,aAAc,CAEvBmB,EAAUuC,KAAKtC,OAET,CAEND,EAAUt5B,IAAIs8B,EAAYK,WAAWpD,EAASD,GAAW8C,eAAen9B,EAAMm5B,0BA1BhE,GAoClBv6B,KAAK++B,eAAiB,WAErB,IAAK39B,EAAMg5B,SAAWh5B,EAAMi5B,MAAO,CAElC,GAAIU,EAAK6D,WAAax9B,EAAMq5B,YAAcr5B,EAAMq5B,YAAa,CAE5Dr5B,EAAM+qB,OAAO7U,SAAS0nB,WAAW59B,EAAM8M,OAAQ6sB,EAAKmD,UAAU98B,EAAMq5B,cACpEY,EAAW2C,KAAK1C,GAIjB,GAAIP,EAAK6D,WAAax9B,EAAMo5B,YAAcp5B,EAAMo5B,YAAa,CAE5Dp5B,EAAM+qB,OAAO7U,SAAS0nB,WAAW59B,EAAM8M,OAAQ6sB,EAAKmD,UAAU98B,EAAMo5B,cACpEa,EAAW2C,KAAK1C,MAQnBt7B,KAAKiB,OAAS,WAEb85B,EAAK+D,WAAW19B,EAAM+qB,OAAO7U,SAAUlW,EAAM8M,QAE7C,IAAK9M,EAAM+4B,SAAU,CAEpB/4B,EAAM67B,eAIP,IAAK77B,EAAMg5B,OAAQ,CAElBh5B,EAAMi9B,aAIP,IAAKj9B,EAAMi5B,MAAO,CAEjBj5B,EAAMo9B,YAIPp9B,EAAM+qB,OAAO7U,SAAS0nB,WAAW59B,EAAM8M,OAAQ6sB,GAE/C35B,EAAM29B,iBAEN39B,EAAM+qB,OAAO8S,OAAO79B,EAAM8M,QAE1B,GAAI0sB,EAAasE,kBAAkB99B,EAAM+qB,OAAO7U,UAAYqjB,EAAK,CAEhEv5B,EAAM+9B,cAAcnD,GAEpBpB,EAAaoD,KAAK58B,EAAM+qB,OAAO7U,YAMjCtX,KAAKo/B,MAAQ,WAEZvE,EAASvB,EAAMC,KACfuB,EAAaxB,EAAMC,KAEnBn4B,EAAM8M,OAAO8vB,KAAK58B,EAAMu6B,SACxBv6B,EAAM+qB,OAAO7U,SAAS0mB,KAAK58B,EAAMy6B,WACjCz6B,EAAM+qB,OAAO4P,GAAGiC,KAAK58B,EAAM06B,KAE3Bf,EAAK+D,WAAW19B,EAAM+qB,OAAO7U,SAAUlW,EAAM8M,QAE7C9M,EAAM+qB,OAAO8S,OAAO79B,EAAM8M,QAE1B9M,EAAM+9B,cAAcnD,GAEpBpB,EAAaoD,KAAK58B,EAAM+qB,OAAO7U,WAehC,SAAS+nB,EAAYrxB,EAAM3G,GAC1B,GAAItG,MAAM6N,QAAQZ,GAAO,CACxB,OAAOA,EAAKyC,QAAQpJ,MAAU,MACxB,CACN,OAAO2G,IAAS3G,GAMlB,SAASi4B,EAAQ/d,GAEhB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7B7b,OAAOuhB,oBAAoB,UAAWD,GAEtCxE,EAAaD,EAEb,GAAIA,IAAWvB,EAAMC,KAAM,OAIpB,GAAI8F,EAAYj+B,EAAM4M,KAAKsrB,EAAME,QAASjY,EAAMie,WAAap+B,EAAM+4B,SAAU,CAEnFU,EAASvB,EAAME,YAET,GAAI6F,EAAYj+B,EAAM4M,KAAKsrB,EAAMG,MAAOlY,EAAMie,WAAap+B,EAAMg5B,OAAQ,CAE/ES,EAASvB,EAAMG,UAET,GAAI4F,EAAYj+B,EAAM4M,KAAKsrB,EAAMI,KAAMnY,EAAMie,WAAap+B,EAAMi5B,MAAO,CAE7EQ,EAASvB,EAAMI,KAMjB,SAAS+F,EAAMle,GAEd,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7BgB,EAASC,EAET9c,OAAOmH,iBAAiB,UAAWma,EAAS,OAI7C,SAAS/mB,EAAUgJ,GAElB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7BtY,EAAMme,iBACNne,EAAMoe,kBAEN,GAAI9E,IAAWvB,EAAMC,KAAM,CAE1BsB,EAAStZ,EAAMqe,OAIhB,GAAI/E,IAAWvB,EAAME,SAAWp4B,EAAM+4B,SAAU,CAE/Ce,EAAU8C,KAAKhB,EAAiBzb,EAAMub,MAAOvb,EAAMwb,QACnD/B,EAAUgD,KAAK9C,QAET,GAAIL,IAAWvB,EAAMG,OAASr4B,EAAMg5B,OAAQ,CAElDiB,EAAW2C,KAAKpB,EAAiBrb,EAAMub,MAAOvb,EAAMwb,QACpDzB,EAAS0C,KAAK3C,QAER,GAAIR,IAAWvB,EAAMI,MAAQt4B,EAAMi5B,MAAO,CAEhDoB,EAAUuC,KAAKpB,EAAiBrb,EAAMub,MAAOvb,EAAMwb,QACnDrB,EAAQsC,KAAKvC,GAIdxpB,SAASkT,iBAAiB,YAAa0a,EAAW,OAClD5tB,SAASkT,iBAAiB,UAAWlF,EAAS,OAE9C7e,EAAM+9B,cAAclD,GAIrB,SAAS4D,EAAUte,GAElB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7BtY,EAAMme,iBACNne,EAAMoe,kBAEN,GAAI9E,IAAWvB,EAAME,SAAWp4B,EAAM+4B,SAAU,CAE/Ca,EAAUgD,KAAK9C,GACfA,EAAU8C,KAAKhB,EAAiBzb,EAAMub,MAAOvb,EAAMwb,aAE7C,GAAIlC,IAAWvB,EAAMG,OAASr4B,EAAMg5B,OAAQ,CAElDkB,EAAS0C,KAAKpB,EAAiBrb,EAAMub,MAAOvb,EAAMwb,aAE5C,GAAIlC,IAAWvB,EAAMI,MAAQt4B,EAAMi5B,MAAO,CAEhDqB,EAAQsC,KAAKpB,EAAiBrb,EAAMub,MAAOvb,EAAMwb,SAMnD,SAAS9c,EAAQsB,GAEhB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7BtY,EAAMme,iBACNne,EAAMoe,kBAEN9E,EAASvB,EAAMC,KAEftnB,SAASstB,oBAAoB,YAAaM,GAC1C5tB,SAASstB,oBAAoB,UAAWtf,GACxC7e,EAAM+9B,cAAcjD,GAIrB,SAAS4D,EAAWve,GAEnB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7BtY,EAAMme,iBACNne,EAAMoe,kBAEN,OAAQpe,EAAMwe,WAEb,KAAK,EAEJ1E,EAAW30B,GAAK6a,EAAMye,OAAS,KAC/B,MAED,KAAK,EAEJ3E,EAAW30B,GAAK6a,EAAMye,OAAS,IAC/B,MAED,QAEC3E,EAAW30B,GAAK6a,EAAMye,OAAS,MAC/B,MAIF5+B,EAAM+9B,cAAclD,GACpB76B,EAAM+9B,cAAcjD,GAIrB,SAAS+D,EAAW1e,GAEnB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7B,OAAQtY,EAAM2e,QAAQrgC,QAErB,KAAK,EACJg7B,EAASvB,EAAMK,aACfuB,EAAU8C,KAAKhB,EAAiBzb,EAAM2e,QAAQ,GAAGpD,MAAOvb,EAAM2e,QAAQ,GAAGnD,QACzE/B,EAAUgD,KAAK9C,GACf,MAED,QACCL,EAASvB,EAAMM,eACf,IAAIuG,EAAK5e,EAAM2e,QAAQ,GAAGpD,MAAQvb,EAAM2e,QAAQ,GAAGpD,MACnD,IAAIsD,EAAK7e,EAAM2e,QAAQ,GAAGnD,MAAQxb,EAAM2e,QAAQ,GAAGnD,MACnDvB,EAAwBD,EAA0B11B,KAAK+O,KAAKurB,EAAKA,EAAKC,EAAKA,GAE3E,IAAIn6B,GAAKsb,EAAM2e,QAAQ,GAAGpD,MAAQvb,EAAM2e,QAAQ,GAAGpD,OAAS,EAC5D,IAAIp2B,GAAK6a,EAAM2e,QAAQ,GAAGnD,MAAQxb,EAAM2e,QAAQ,GAAGnD,OAAS,EAC5DtB,EAAUuC,KAAKpB,EAAiB32B,EAAGS,IACnCg1B,EAAQsC,KAAKvC,GACb,MAIFr6B,EAAM+9B,cAAclD,GAIrB,SAASoE,EAAU9e,GAElB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7BtY,EAAMme,iBACNne,EAAMoe,kBAEN,OAAQpe,EAAM2e,QAAQrgC,QAErB,KAAK,EACJm7B,EAAUgD,KAAK9C,GACfA,EAAU8C,KAAKhB,EAAiBzb,EAAM2e,QAAQ,GAAGpD,MAAOvb,EAAM2e,QAAQ,GAAGnD,QACzE,MAED,QACC,IAAIoD,EAAK5e,EAAM2e,QAAQ,GAAGpD,MAAQvb,EAAM2e,QAAQ,GAAGpD,MACnD,IAAIsD,EAAK7e,EAAM2e,QAAQ,GAAGnD,MAAQxb,EAAM2e,QAAQ,GAAGnD,MACnDvB,EAAwB31B,KAAK+O,KAAKurB,EAAKA,EAAKC,EAAKA,GAEjD,IAAIn6B,GAAKsb,EAAM2e,QAAQ,GAAGpD,MAAQvb,EAAM2e,QAAQ,GAAGpD,OAAS,EAC5D,IAAIp2B,GAAK6a,EAAM2e,QAAQ,GAAGnD,MAAQxb,EAAM2e,QAAQ,GAAGnD,OAAS,EAC5DrB,EAAQsC,KAAKpB,EAAiB32B,EAAGS,IACjC,OAMH,SAAS45B,EAAS/e,GAEjB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7B,OAAQtY,EAAM2e,QAAQrgC,QAErB,KAAK,EACJg7B,EAASvB,EAAMC,KACf,MAED,KAAK,EACJsB,EAASvB,EAAMK,aACfuB,EAAU8C,KAAKhB,EAAiBzb,EAAM2e,QAAQ,GAAGpD,MAAOvb,EAAM2e,QAAQ,GAAGnD,QACzE/B,EAAUgD,KAAK9C,GACf,MAIF95B,EAAM+9B,cAAcjD,GAIrB,SAASqE,EAAYhf,GAEpB,GAAIngB,EAAMy4B,UAAY,MAAO,OAE7BtY,EAAMme,iBAIP1/B,KAAKwgC,QAAU,WAEdxgC,KAAKyrB,WAAW8T,oBAAoB,cAAegB,EAAa,OAChEvgC,KAAKyrB,WAAW8T,oBAAoB,YAAahnB,EAAW,OAC5DvY,KAAKyrB,WAAW8T,oBAAoB,QAASO,EAAY,OAEzD9/B,KAAKyrB,WAAW8T,oBAAoB,aAAcU,EAAY,OAC9DjgC,KAAKyrB,WAAW8T,oBAAoB,WAAYe,EAAU,OAC1DtgC,KAAKyrB,WAAW8T,oBAAoB,YAAac,EAAW,OAE5DpuB,SAASstB,oBAAoB,YAAaM,EAAW,OACrD5tB,SAASstB,oBAAoB,UAAWtf,EAAS,OAEjDjC,OAAOuhB,oBAAoB,UAAWD,EAAS,OAC/CthB,OAAOuhB,oBAAoB,QAASE,EAAO,QAI5Cz/B,KAAKyrB,WAAWtG,iBAAiB,cAAeob,EAAa,OAC7DvgC,KAAKyrB,WAAWtG,iBAAiB,YAAa5M,EAAW,OACzDvY,KAAKyrB,WAAWtG,iBAAiB,QAAS2a,EAAY,OAEtD9/B,KAAKyrB,WAAWtG,iBAAiB,aAAc8a,EAAY,OAC3DjgC,KAAKyrB,WAAWtG,iBAAiB,WAAYmb,EAAU,OACvDtgC,KAAKyrB,WAAWtG,iBAAiB,YAAakb,EAAW,OAEzDriB,OAAOmH,iBAAiB,UAAWma,EAAS,OAC5CthB,OAAOmH,iBAAiB,QAASsa,EAAO,OAExCz/B,KAAKm8B,eAGLn8B,KAAKiB,UAIN,SAASw/B,EAAalf,GAASA,EAAMme,iBAErCtG,EAAkB3wB,UAAY3H,OAAO4/B,OAAOl3B,EAAMm3B,gBAAgBl4B,2CChqBlEuV,OAAOxU,MAAQA,EAEf,IAAIuU,EAAcC,OAAOC,SAASC,KAAKzN,QAAQ,cAAgB,EAAI,GAAK,yBAExE,IAAImwB,EAAiBzH,EAAQ,QAC7B,IAAI0H,EAAW1H,EAAQ,QACvB,IAAI2H,EAAa3H,EAAQ,QAGzB,IAAI4H,EAAmB,SAAnBA,IACA/gC,KAAKghC,YAAc,KACnBhhC,KAAKihC,UAAY,KACjBjhC,KAAKkhC,SAAW,KAChBlhC,KAAKmhC,MAAQ,KACbnhC,KAAKohC,MAAQ,KACbphC,KAAKqhC,QAAU,KACfrhC,KAAKshC,MAAQ,KACbthC,KAAKuhC,KAAO,KACZvhC,KAAKwhC,YAAc,KACnBxhC,KAAKyhC,OAAS,KACdzhC,KAAK0hC,UAAY,EACjB1hC,KAAK2hC,kBAAoB,GACzB3hC,KAAK4hC,eAAiB,GACtB5hC,KAAK6hC,aAAe,OAGxB,IAAIC,EAAO,IAAIf,MAETgB,aAEF,SAAAA,EAAAv/B,GAA6E,IAA/D8tB,EAA+D9tB,EAA/D8tB,UAAWxgB,EAAoDtN,EAApDsN,MAAO6E,EAA6CnS,EAA7CmS,OAAQ8b,EAAqCjuB,EAArCiuB,WAAYC,EAAyBluB,EAAzBkuB,sBAAyBsR,IAAAhiC,KAAA+hC,GACzE/hC,KAAKiiC,SAAW,KAChBjiC,KAAKywB,WAAaA,EAClBzwB,KAAKswB,UAAYA,EACjB,GAAIA,EAAWtwB,KAAKkiC,KAAKpyB,EAAO6E,kDAIhC/S,QAAQC,IAAI,kBACZ,GAAI7B,KAAKmiC,8BAA+BniC,KAAKmiC,iEAGoB,IAA/D7R,EAA+DhM,EAA/DgM,UAAWxgB,EAAoDwU,EAApDxU,MAAO6E,EAA6C2P,EAA7C3P,OAAQ8b,EAAqCnM,EAArCmM,WAAYC,EAAyBpM,EAAzBoM,sBAGxC1wB,KAAKywB,WAAaA,EAClBzwB,KAAKswB,UAAYA,EACjBtwB,KAAKkiC,KAAKpyB,EAAO6E,GACjB3U,KAAKkxB,OAAO,CAAEphB,QAAO6E,WACrB3U,KAAKmiC,8BAAgCzR,EAErC,OAAO1wB,yCAIe,IAAjB8P,EAAiB2U,EAAjB3U,MAAO6E,EAAU8P,EAAV9P,OACZ3U,KAAK2sB,OAAOyV,OAAStyB,EAAQ6E,EAC7B3U,KAAK2sB,OAAO0V,yBACZriC,KAAKwrB,SAAS8W,QAAQxyB,EAAO6E,GAC7B3U,KAAKwrB,SAASC,WAAWjU,MAAM1H,MAA/B,GAAA2F,OAA0C3F,EAA1C,MACA9P,KAAKwrB,SAASC,WAAWjU,MAAM7C,OAA/B,GAAAc,OAA2Cd,EAA3C,+CAGmB,IAAT8oB,EAAS7Y,EAAT6Y,MACV77B,QAAQC,IAAI47B,oCAGTjQ,EAAM+U,EAAQC,GACjB,GAAIhV,GAAQ,KAAM,OAAOxtB,KAAKyiC,QAC9B,IAAIC,EAAoB1iC,KAAK2iC,eAAenV,EAAKwB,KAAK5E,SAAU0X,GAEhE9hC,KAAK4iC,aAAaF,EAAmBH,EAAQC,GAC7CxiC,KAAKF,2CAILE,KAAK6iC,+CAGDrzB,GAEJ,IAAI+B,EAAOvR,KACX,IAAI8iC,EAAgB,IAAIt5B,mBAExB,OAAO,IAAIu5B,QAAQ,SAACC,GAEhBnC,EAAS9iB,EAAc,6BAA8B,SAAU9P,EAAKmlB,GAEhE,IAAIlgB,EAAW0tB,EAAe,CAC1B9wB,MAAO,IACPmzB,MAAO,OACP7P,KAAMA,EACNa,UAAW,OAGf/gB,EAASjS,OAAOuO,GAEhBszB,EAAc3hC,KAAK4c,EAAc,8BAA+B,SAAUmlB,GAEtE,IAAIC,EAAW,IAAI35B,uBAAwBs3B,EAAW,CAClDz3B,IAAK65B,EACL/5B,UAAW,QACXD,UAAW,KACXk6B,KAAM55B,gBACN65B,YAAa,KACbj6B,MAAO,wBAIX,IAAI2b,EAAO,IAAIvb,UAAW0J,EAAUiwB,GACpC5xB,EAAK+d,MAAMntB,IAAI4iB,GACfA,EAAKue,MAAMt7B,IAAI,GAAI,EAAG,GAEtBg7B,EAAQje,qDAMN4H,EAAQR,EAAQxd,EAAQszB,GAEtCtzB,EAASA,GAAU,KAEnB,IAAM+lB,EAAc,IAAIlrB,UAGxBkrB,EAAY6O,cAAcpX,GAE1B,IAAMtX,EAAS6f,EAAY8O,YAE3B,IAAMl3B,EAAOooB,EAAY+O,UAGzB,IAAMC,EAAS79B,KAAKoK,IAAI3D,EAAKrG,EAAGqG,EAAK5F,EAAG4F,EAAKyV,GAC7C,IAAM4hB,EAAMhX,EAAOgX,KAAO99B,KAAKk4B,GAAK,KACpC,IAAI6F,EAAU/9B,KAAKE,IAAI29B,EAAS,EAAI79B,KAAKg+B,IAAIF,EAAM,IAEnDC,GAAWj1B,EAEXge,EAAOrV,SAASyK,EAAI6hB,EAEpB,IAAME,EAAOpP,EAAYrjB,IAAI0Q,EAC7B,IAAMgiB,EAAmBD,EAAO,GAAMA,EAAOF,EAAUA,EAAUE,EAEjEnX,EAAOqX,IAAMD,EAAkB,EAC/BpX,EAAO0V,yBAEP,GAAIJ,EAAU,CAGVA,EAAS/zB,OAAS2G,EAGlBotB,EAASxH,YAAcsJ,EAAkB,EAEzC9B,EAASxnB,gBAEN,CAEHkS,EAAOsS,OAAOpqB,yCAQlB,IAAIX,GAAM,IAAI1K,WAAa+5B,cAAcvjC,KAAKsvB,OAI9C,IAAI2U,EAAejkC,KAAKkkC,qBACxB,IAAIC,EAAcnkC,KAAKokC,oBAEvB,IAAIzX,EAAS,IAAInjB,wBAAyB26B,GAAe,EAAGA,EAAc,EAAGF,EAAe,EAAGA,GAAgB,EAAG,EAAG,KAErH,IAAI/vB,GAAM,IAAI1K,WAAa+5B,cAAcvjC,KAAKsvB,OAG9CtvB,KAAKsvB,MAAMhY,SAASinB,gBAAgB,GAEpC5R,EAAO0X,KAAOx+B,KAAKwL,IAAI8yB,GAAejwB,EAAIjE,IAAIhK,EAAIiO,EAAI7C,IAAIpL,GACtDg+B,GAAgB/vB,EAAIjE,IAAIvJ,EAAIwN,EAAI7C,IAAI3K,IAAM,GAC9CimB,EAAO0V,yBACP1V,EAAO2X,eAMP,OAAO3X,iCAGN7c,EAAO6E,GAAQ,IAAAvT,EAAApB,KAChB,IAAIswB,EAAYtwB,KAAKswB,UAErB,GAAItwB,KAAKwrB,SAAU,CACf8E,EAAUzsB,YAAY7D,KAAKwrB,SAASC,YAIpC,OAGJ,IAAIkB,EAAQ2C,EAEZ,IAAI8U,EAAqBF,EAGzBlkC,KAAKkkC,qBAAuBA,EAAuBvvB,EACnD3U,KAAKokC,oBAAsBA,EAAsBt0B,EAGjD,IAAI0b,EAAW,IAAIhiB,mBAAoB,CACnC+6B,UAAW,KACXC,sBAAuB,KACvB9M,MAAO,OAGX13B,KAAKwrB,SAAWA,EAChBA,EAASiZ,cAAczmB,OAAO0mB,kBAC9BlZ,EAAS8W,QAAQ8B,EAAqBF,GACtC5T,EAAUzsB,YAAY2nB,EAASC,YAE/BzrB,KAAK2sB,OAASA,EAAS,IAAInjB,uBAAwB,GAAIsG,EAAQ6E,EAAQ,EAAG,KAG1EgY,EAAOrV,SAASyK,EAAI,KACpB4K,EAAOrV,SAASrR,EAAI,IACpB0mB,EAAOrV,SAAS5Q,EAAI,IAEpB,IAAIu7B,EAAWjiC,KAAKiiC,SAAW,IAAI7I,EAAkBp5B,KAAK2sB,OAAQnB,EAASC,WAAYzrB,KAAKywB,YAC5FwR,EAASjI,YAAc,EACvBiI,EAAShI,UAAY,IACrBgI,EAAS/H,SAAW,GACpB+H,EAAS7H,OAAS,MAClB6H,EAAS5H,MAAQ,MACjB4H,EAAS3H,aAAe,KACxB2H,EAAS1H,qBAAuB,GAChC0H,EAAS/zB,OAAS,IAAI1E,aAAc,IAAK,IAAK,GAG9Cy4B,EAAS9c,iBAAiB,SAAU,WAChC/jB,EAAKujC,kBAOT3kC,KAAKsvB,MAAQA,EAAQ,IAAI9lB,cACzBxJ,KAAKgxB,OAAS,IAAIxnB,WAClBxJ,KAAKgxB,OAAO7uB,IAAInC,KAAKsvB,OAErBA,EAAMsV,WAAa,IAAIp7B,WAAY,SAEnC,IAAIq7B,EAAa,IAAIr7B,gBAAiB,KAAM,IAAK,QAAU,SAC3Dq7B,EAAWvtB,SAAS5Q,EAAI,EACxBm+B,EAAWvtB,SAASrR,EAAI,EACxBqpB,EAAMntB,IAAI0iC,GAEVvV,EAAMwV,SAASp+B,EAAI,GAGnB1G,KAAK+kC,MAAQ,GACb/kC,KAAKglC,OAAS,GAEd,IAAMC,EAAU,SAAVA,IACF7jC,EAAK6gC,SAAShhC,SAEdG,EAAKtB,SACLolC,sBAAsBD,IAI1BA,6CAKW7a,EAAU+a,GACrB,OAAO/a,EAASwJ,OAAO,SAAA3tB,GACnB,OACKA,EAAE,eAAiB,KAAOk/B,EAAY,gBACtCl/B,EAAE,eAAiB,KAAOk/B,EAAY,cACtCl/B,EAAE,eAAiB,KAAOk/B,EAAY,aACtCl/B,EAAE,eAAiB,KAAOk/B,EAAY,UACtCl/B,EAAE,eAAiB,KAAOk/B,EAAY,UACtCl/B,EAAE,eAAiB,KACnBA,EAAE,eAAiB,KACnBA,EAAE,eAAiB,KAAOk/B,EAAY,SACtCl/B,EAAE,eAAiB,KAAOk/B,EAAY,YACtCl/B,EAAE,eAAiB,QAAUk/B,EAAY,UACzCl/B,EAAE,eAAiB,OAASk/B,EAAY,gBACxCl/B,EAAE,eAAiB,UAAYk/B,EAAY,+CAOpDnlC,KAAKwrB,SAAS1rB,OAAOE,KAAKgxB,OAAQhxB,KAAK2sB,yDAKpByY,GACnB,IAAIlxB,EAAM,KACVkxB,EAASC,SAAS,SAAUC,GACxB,IAAIpyB,EAAWoyB,EAAMpyB,SACrB,GAAIA,IAAa9F,UAAW,OAC5B8F,EAASuhB,qBACT,GAAIvgB,IAAQ,KAAM,CACdA,EAAMhB,EAASwhB,gBACZ,CACHxgB,EAAIqxB,MAAMryB,EAASwhB,gBAG3B,OAAOxgB,0CAGGsxB,GACVxlC,KAAKylC,OAAOp8B,IAAI,SAACq8B,EAAGC,GAChBD,EAAE3sB,QAAQ4O,QAAUge,GAAKH,2CAIpBpb,EAAUmY,EAAQC,GAAO,IAAAxgC,EAAAhC,KAElCA,KAAK6iC,aAEL,IAAIvT,EAAQtvB,KAAKsvB,MACjB,IAAIyV,EAAQ/kC,KAAK+kC,MACjB,IAAIC,EAAShlC,KAAKglC,OAElBhlC,KAAKylC,OAAS,GARoB,IAAAG,EAAA,SAAAA,EAUzBh5B,GACL,IAAIi5B,EAAYzb,EAASxd,GAEzB,GAAIi5B,EAAUvoB,YAAc,KAAM,CAC9B,iBAGJ,IAAIwoB,EAAU,EACTD,EAAUE,SAAS,GAAKF,EAAUE,SAAS,IAAM,KACjDF,EAAUG,SAAS,GAAKH,EAAUG,SAAS,IAAM,KACjDH,EAAUI,SAAS,GAAKJ,EAAUI,SAAS,IAAM,KAGtD,IAAIC,EAAQ,EACPL,EAAUE,SAAS,GAAKF,EAAUE,SAAS,IAAM,KACjDF,EAAUG,SAAS,GAAKH,EAAUG,SAAS,IAAM,KACjDH,EAAUI,SAAS,GAAKJ,EAAUI,SAAS,IAAM,KAGtD,IACI,IAAIn3B,EAAI,EACRA,EAAIhO,OAAOqlC,OAAON,EAAUvoB,YAAYzd,OACxCiP,IACF,CAEE,IAAIkgB,EAAOluB,OAAOqlC,OAAON,EAAUvoB,YAAYxO,GAC/C,GAAIgzB,EAAKD,cAAgB,KAAM,CAC3B,MAAO7S,EAAK8S,EAAKH,oBAAsB,WAClCz2B,WACAuF,QAAQqxB,EAAKF,iBAAmB,GACnC,CACE,UAIR,IAAIsE,EAAQ,EACPlX,EAAK+W,SAAS,GAAK/W,EAAK+W,SAAS,IAAM,KACvC/W,EAAKgX,SAAS,GAAKhX,EAAKgX,SAAS,IAAM,KACvChX,EAAKiX,SAAS,GAAKjX,EAAKiX,SAAS,IAAM,KAG5C,IAAIH,EAAU,EACT9W,EAAK+W,SAAS,GAAK/W,EAAK+W,SAAS,IAAM,KACvC/W,EAAKgX,SAAS,GAAKhX,EAAKgX,SAAS,IAAM,KACvChX,EAAKiX,SAAS,GAAKjX,EAAKiX,SAAS,IAAM,KAGxC/yB,EAAY2yB,EAAUO,WAAa,IAAO,IAAI58B,sBAAuB08B,EAAM,GAAK,EAAGA,EAAM,GAAK,EAAGA,EAAM,GAAI,GAAI,GAAK,IAAI18B,iBAAkB08B,EAAM,GAAIA,EAAM,GAAIA,EAAM,IACpK/C,EAAW,IAAI35B,uBAAwB,CACvCJ,MAAO,SAAUi9B,UAAW,MAAOhD,YAAa,KAChDiD,cAAe,KACfC,oBAAqB,EACrBC,mBAAoB,EACpBv9B,QAASu5B,EAAQ,IAAO,KAIxBiE,EAAO,IAAIj9B,UAAW0J,EAAUiwB,GACpCsD,EAAKnvB,SAASrR,EAAI6/B,EAAQ,GAC1BW,EAAKnvB,SAAS5Q,EAAIo/B,EAAQ,GAC1BW,EAAKnvB,SAASyK,EAAI+jB,EAAQ,GAC1BW,EAAKC,UAAYb,EAEbhlC,EAAM,IAAI2I,mBAAoBi9B,EAAKvzB,UACnCyzB,EAAM,IAAIn9B,uBAAwB,CAClCJ,MAAOm5B,EAAS,SAAW,SAC3BqE,UAAW,IAEXP,EAAY,IAAI78B,kBAAmB3I,EAAK2hC,EAAQW,EAAWwD,GAE/DF,EAAKtkC,IAAIkkC,GACT/W,EAAMntB,IAAIskC,GACV1B,EAAM/zB,KAAKy1B,GAEX,IAAKzkC,EAAKyjC,OAAOI,EAAUgB,aAAc,CACrC7kC,EAAKyjC,OAAOI,EAAUgB,aAAe,CAAEC,MAAO,IAAIt9B,WAAeu9B,IAAK,IAAIv9B,cAG9ExH,EAAKyjC,OAAOI,EAAUgB,aAAaC,MAAM3kC,IAAIskC,EAAK7K,SAClD55B,EAAKyjC,OAAOI,EAAUgB,aAAaE,IAAI9gC,EAAI6/B,EAAQ,GACnD9jC,EAAKyjC,OAAOI,EAAUgB,aAAaE,IAAIrgC,EAAIo/B,EAAQ,GACnD9jC,EAAKyjC,OAAOI,EAAUgB,aAAaE,IAAIhlB,EAAI+jB,EAAQ,GAIvD,GAAID,EAAUmB,SAAW,GAAI,CACrBC,EAAUjlC,EAAKklC,eAAL,GAAAzxB,OAAuBowB,EAAUmB,SAC3C,CACI36B,SAAU,GACV86B,YAAa,CAAEC,EAAG,IAAK1B,EAAG,EAAG5Q,EAAG,EAAGnyB,EAAG,GACtC0kC,gBAAiB,CAAED,EAAG,IAAK1B,EAAG,IAAK5Q,EAAG,IAAKnyB,EAAG,MAGtDX,EAAKslC,QAAL,GAAA7xB,OAAgBowB,EAAUmB,UAAWzkC,KAAK,SAACwiB,GACvCA,EAAKzN,SAAStP,IACV89B,EAAQ,GAAK,IACbA,EAAQ,GACRA,EAAQ,GAAKI,EAAM,GAAK,GAC5B5W,EAAMntB,IAAI4iB,GACVigB,EAAOh0B,KAAK+T,OAnGxB,IAAK,IAAInY,EAAI,EAAGA,EAAIwd,EAASvqB,OAAQ+M,IAAK,KA+C9BsG,EA/C8B,IAgD9BiwB,EAhD8B,IAyD9BsD,EAzD8B,IA+D9B5lC,EA/D8B,IAgE9B8lC,EAhE8B,IAoE9BN,EApE8B,IAsF9BY,EAtF8B,IAAAM,EAAA3B,EAAjCh5B,GAAiC,GAAA26B,IAAA,WAIlC,SAqGRvnC,KAAKylC,OAAOp8B,IAAI,SAACy9B,EAAOpmC,GACpB,IAAI8mC,EAAiB,IAAIh+B,uBAAwB,CAC7CJ,MAAO,MAAUi9B,UAAW,MAAOhD,YAAa,KAChDiD,cAAe,KACfC,oBAAqB,EACrBC,mBAAoB,EACpBv9B,QAAS,MAGb,IAAIw+B,EAAKzlC,EAAK0lC,uBAAuBZ,EAAMA,OAC3C,IAAIa,EAAa3lC,EAAK0lC,uBAAuBZ,EAAMA,OACnD,IAAIx6B,EAAO,IAAI9C,aACf,IAAIo+B,EAAe,IAAIp+B,aACvB,IAAIqL,EAAS,IAAIrL,aAEjBm+B,EAAWE,eAAe,IAC1BF,EAAWlE,QAAQmE,GACnBH,EAAGhE,QAAQn3B,GAGX,IAAIw7B,EAAiB,IAAIt+B,iBAAkBo+B,EAAa3hC,EAAG2hC,EAAalhC,EAAGkhC,EAAa7lB,GACxF,IAAIgmB,EAAa,IAAIv+B,UAAWs+B,EAAgBN,GAEhDO,EAAWpgB,QAAU,MACrBogB,EAAWzwB,SAASrR,EAAI6gC,EAAMC,IAAI9gC,EAClC8hC,EAAWzwB,SAAS5Q,EAAIogC,EAAMC,IAAIrgC,EAAI4F,EAAK5F,EAAI,EAAI,GACnDqhC,EAAWzwB,SAASyK,EAAI+kB,EAAMC,IAAIhlB,EAClCuN,EAAMntB,IAAI4lC,GACVhD,EAAM/zB,KAAK+2B,GACXjB,EAAM/tB,QAAUgvB,yCAKbC,GACP,GAAIA,EAAiB,CACjBhoC,KAAKgxB,OAAO4T,WAAa,IAAIp7B,WAAY,UACzCxJ,KAAKgxB,OAAO/nB,QAAU,MACnB,CACHjJ,KAAKgxB,OAAO4T,WAAa,IAAIp7B,WAAY,SACzCxJ,KAAKgxB,OAAO/nB,QAAU,GAK1B,IAAI47B,EAAa,IAAIr7B,gBAAiB,KAAM,IAAK,QAAU,SAC3Dq7B,EAAWvtB,SAAS5Q,EAAI,EACxBm+B,EAAWvtB,SAASrR,EAAI,EACxBjG,KAAKsvB,MAAMntB,IAAI0iC,0CAIf,IAAIvV,EAAQtvB,KAAKsvB,MACjB,IAAI0V,EAAShlC,KAAKglC,OAClB,IAAIS,EAASzlC,KAAKylC,OAASzlC,KAAKylC,OAAS,GACzC,IAAK,IAAI74B,EAAI,EAAGA,EAAI5M,KAAK+kC,MAAMllC,OAAQ+M,IAAK,CACxC0iB,EAAMvF,OAAO/pB,KAAK+kC,MAAMn4B,IAE5B5M,KAAK+kC,MAAQ,GACb,IAAK,IAAIn4B,EAAI,EAAGA,EAAI5M,KAAKglC,OAAOnlC,OAAQ+M,IAAK,CACzC0iB,EAAMvF,OAAO/pB,KAAKglC,OAAOp4B,IAE7B5M,KAAKglC,OAAS,GACd,IAAK,IAAIp4B,EAAI,EAAGA,EAAI64B,EAAO5lC,OAAQ+M,IAAK,CACpC,GAAI64B,EAAO74B,GAAI0iB,EAAMvF,OAAO0b,EAAO74B,GAAGk6B,OAE1C9mC,KAAKylC,OAAS,2CAIdzlC,KAAKiiC,SAAS7C,iDAGH6I,EAASC,GACpB,GAAIA,IAAe96B,UAAW86B,EAAa,GAE3C,IAAIC,EAAWD,EAAWn7B,eAAe,YAAcm7B,EAAW,YAAc,QAChF,IAAI77B,EAAW67B,EAAWn7B,eAAe,YAAcm7B,EAAW,YAAc,GAChF,IAAIE,EAAkBF,EAAWn7B,eAAe,mBAAqBm7B,EAAW,mBAAqB,EACrG,IAAIf,EAAce,EAAWn7B,eAAe,eAAiBm7B,EAAW,eAAiB,CAAEd,EAAG,EAAG1B,EAAG,EAAG5Q,EAAG,EAAGnyB,EAAG,GAChH,IAAI0kC,EAAkBa,EAAWn7B,eAAe,mBAAqBm7B,EAAW,mBAAqB,CAAEd,EAAG,IAAK1B,EAAG,IAAK5Q,EAAG,IAAKnyB,EAAG,GAElI,IAAIypB,EAASna,SAASC,cAAc,UAEpC,GAAI+1B,EAAQpoC,OAAS,GAAI,CACrBusB,EAAOtc,MAAQm4B,EAAQpoC,OAAS,GAGpC,IAAIwoC,EAAUjc,EAAOkc,WAAW,MAChCD,EAAQjV,KAAO,QAAU/mB,EAAW,MAAQ87B,EAE5C,IAAII,EAAUF,EAAQG,YAAYP,GAElC,IAAIQ,EAAYF,EAAQz4B,MAGxBu4B,EAAQK,UAAY,QAAUrB,EAAgBD,EAAI,IAAMC,EAAgB3B,EAAI,IACtE2B,EAAgBvS,EAAI,IAAMuS,EAAgB1kC,EAAI,IAEpD0lC,EAAQM,YAAc,QAAUxB,EAAYC,EAAI,IAAMD,EAAYzB,EAAI,IAChEyB,EAAYrS,EAAI,IAAMqS,EAAYxkC,EAAI,IAE5C0lC,EAAQO,UAAYR,EAEpBC,EAAQK,UAAY,4BACpBL,EAAQQ,SAASZ,EAASG,EAAiB/7B,EAAW+7B,GAEtD,IAAIlF,EAAU,IAAI15B,aAAc4iB,GAChC8W,EAAQnvB,YAAc,KAEtB,IAAI+0B,EAAiB,IAAIt/B,oBAAqB,CAAEH,IAAK65B,IACrD,IAAI6F,EAAS,IAAIv/B,YAAas/B,GAC9BC,EAAOzF,MAAMt7B,IAAIokB,EAAOtc,OAAS,IAAM,IAAM,IAAKsc,EAAOtc,OAAS,IAAM,IAAM,KAAQsc,EAAOtc,MAAO,GACpG,OAAOi5B,kBAKf,IAAI3Y,EAAqB,IAAI2R,EAAmB,yCCxjBhD,IAAAiH,EAAAvpC,EAAA,YAAAwpC,EAAAxpC,EAAAkO,EAAAq7B,GAAsnB,IAAAjzB,EAAAkzB,EAAG,iDCAznB,IAAAz6B,EAAY/O,EAAQ,QACpB,IAAA+S,EAAc/S,EAAQ,QACtB,IAAAR,EAAeQ,EAAQ,QAEvB,IAAAypC,EAAA,QACA,IAAAC,EAAA,QAEArqC,EAAAC,QAAA,SAAAqqC,EAAAp1B,EAAAhL,GAEA,IAAAgL,KAAAxB,EAAAwB,IAAA/U,EAAA+U,IAAA,CACAhL,EAAAgL,GAAA,GACAA,EAAA,KAGA,UAAAhL,IAAA,SACAA,EAAA,CAAeoL,MAAApL,QAEfA,KAAA,GAEA,IAAAvH,SAAAuH,EAAAvH,OAAA,SAAAuH,EAAAvH,KAAA,SACA,IAAA2S,SAAApL,EAAAoL,QAAA,SAAApL,EAAAoL,MAAA,EACA,IAAAzE,EAAA3G,EAAA2G,OAAA,EAEA,IAAA05B,EAAArgC,EAAAgrB,YAAA,MAAAkV,EAAAC,EACAxmC,EAAA0mC,EAAA,GACAvU,EAAAuU,EAAA,GACA1D,EAAA0D,EAAA,GAEA,IAAAC,EAAAl1B,EAAA,EAEA,IAAApK,EAAAgK,GAAA,IAAAxF,EAAA/M,GAAA,CAAA6nC,GACA,QAAA18B,EAAA,EAAAkC,EAAA,EAA0BlC,EAAA08B,EAAgB18B,GAAA,EAAAkC,GAAA,GAC1C,IAAA7I,EAAA2G,EAAA+C,EACA3F,EAAA/D,EAAA,GAAA6I,EAAA,EACA9E,EAAA/D,EAAA,GAAA6I,EAAA,EACA9E,EAAA/D,EAAA,GAAA6I,EAAA,EACA9E,EAAA/D,EAAA,GAAA6I,EAAAnM,EACAqH,EAAA/D,EAAA,GAAA6I,EAAAgmB,EACA9qB,EAAA/D,EAAA,GAAA6I,EAAA62B,EAEA,OAAA37B,wCCxCA,IAAAu/B,EAAA9pC,EAAA,YAAA+pC,EAAA/pC,EAAAkO,EAAA47B,GAAqlB,IAAAxzB,EAAAyzB,EAAG,wBCAxlB1qC,EAAAC,QAAA,SAAA0qC,EAAAC,EAAAC,GACA,cAAAD,IAAA,SACAA,SACAC,IAAA,SAAAA,EAAA,sCCDA,IAAAjhC,EAAWjJ,EAAQ,QAEnBX,EAAAC,QAAA2J,EAAA6C,KAAA/C,SAAA+C,KAAAzK,OAAA2H,UAAAsE,oCCJAjO,EAAAC,QAAAq1B,MAAA,SAAAA,EAAAhjB,GACA,IAAAgjB,EAAA,IAAA9d,aAAAlF,EAAAvR,OAAA,KACA,IAAA+M,EAAA,EACAwE,EAAAtD,QAAA,SAAA+lB,GACA,IAAAnzB,EAAAmzB,EAAAzxB,KAAAwnC,MAAA,EACAxV,EAAAxnB,KAAAlM,EACA0zB,EAAAxnB,KAAAlM,EACA0zB,EAAAxnB,KAAAlM,EACA0zB,EAAAxnB,KAAAlM,IAEA,OAAA0zB,GAGAt1B,EAAAC,QAAAgL,IAAA,SAAAA,EAAAqH,EAAAmiB,EAAAG,EAAAJ,GACA,IAAAvpB,EAAA,IAAAuM,aAAAlF,EAAAvR,OAAA,KACA,IAAA+M,EAAA,EACAwE,EAAAtD,QAAA,SAAA+lB,GACA,IAAAC,EAAAD,EAAAzxB,KACA,IAAAynC,EAAA/V,EAAA7tB,EAAA6tB,EAAAhkB,MACA,IAAAg6B,EAAAhW,EAAAptB,EAAAotB,EAAAnf,OAGA,IAAAo1B,EAAAjW,EAAA7tB,EAAAstB,EACA,IAAAyW,EAAAlW,EAAAptB,EAAAgtB,EACA,IAAAuW,EAAAJ,EAAAtW,EACA,IAAA2W,EAAAJ,EAAApW,EAEA,GAAAJ,EAAA,CACA0W,GAAAtW,EAAAI,EAAAptB,GAAAgtB,EACAwW,GAAAxW,EAAAoW,GAAApW,EAIA3pB,EAAA6C,KAAAm9B,EACAhgC,EAAA6C,KAAAo9B,EAEAjgC,EAAA6C,KAAAm9B,EACAhgC,EAAA6C,KAAAs9B,EAEAngC,EAAA6C,KAAAq9B,EACAlgC,EAAA6C,KAAAs9B,EAEAngC,EAAA6C,KAAAq9B,EACAlgC,EAAA6C,KAAAo9B,IAEA,OAAAjgC,GAGAjL,EAAAC,QAAAgM,UAAA,SAAAA,EAAAqG,GACA,IAAArG,EAAA,IAAAuL,aAAAlF,EAAAvR,OAAA,KACA,IAAA+M,EAAA,EACAwE,EAAAtD,QAAA,SAAA+lB,GACA,IAAAC,EAAAD,EAAAzxB,KAGA,IAAA6D,EAAA4tB,EAAAvc,SAAA,GAAAwc,EAAAqW,QACA,IAAAzjC,EAAAmtB,EAAAvc,SAAA,GAAAwc,EAAAsW,QAGA,IAAA/d,EAAAyH,EAAAhkB,MACA,IAAAyc,EAAAuH,EAAAnf,OAGA5J,EAAA6B,KAAA3G,EACA8E,EAAA6B,KAAAlG,EAEAqE,EAAA6B,KAAA3G,EACA8E,EAAA6B,KAAAlG,EAAA6lB,EAEAxhB,EAAA6B,KAAA3G,EAAAomB,EACAthB,EAAA6B,KAAAlG,EAAA6lB,EAEAxhB,EAAA6B,KAAA3G,EAAAomB,EACAthB,EAAA6B,KAAAlG,IAEA,OAAAqE,8DC3EA,IAAAs/B,EAAA5qC,EAAA,YAAA6qC,EAAA7qC,EAAAkO,EAAA08B,GAA2nB,IAAAt0B,EAAAu0B,EAAG,qCCE9nB,IAAAC,EACA,IAAAzpC,OAAAkN,KAAA,CAEA,IAAA3I,EAAAvE,OAAA2H,UAAAsE,eACA,IAAAnB,EAAA9K,OAAA2H,UAAAyC,SACA,IAAAs/B,EAAc/qC,EAAQ,QACtB,IAAAgrC,EAAA3pC,OAAA2H,UAAAwE,qBACA,IAAAy9B,GAAAD,EAAAl/B,KAAA,CAA0CL,SAAA,MAAiB,YAC3D,IAAAy/B,EAAAF,EAAAl/B,KAAA,aAAuD,aACvD,IAAAq/B,EAAA,CACA,WACA,iBACA,UACA,iBACA,gBACA,uBACA,eAEA,IAAAC,EAAA,SAAAC,GACA,IAAAC,EAAAD,EAAA1rC,YACA,OAAA2rC,KAAAtiC,YAAAqiC,GAEA,IAAAE,EAAA,CACAC,kBAAA,KACAC,SAAA,KACAC,UAAA,KACAC,OAAA,KACAC,cAAA,KACAC,QAAA,KACAC,aAAA,KACAC,YAAA,KACAC,aAAA,KACAC,YAAA,KACAC,aAAA,KACAC,aAAA,KACAC,QAAA,KACAC,YAAA,KACAC,WAAA,KACAC,SAAA,KACAC,SAAA,KACAC,MAAA,KACAC,iBAAA,KACAC,mBAAA,KACAC,QAAA,MAEA,IAAAC,EAAA,WAEA,UAAAtuB,SAAA,aAAsC,aACtC,QAAAjP,KAAAiP,OAAA,CACA,IACA,IAAAgtB,EAAA,IAAAj8B,IAAA1J,EAAAkG,KAAAyS,OAAAjP,IAAAiP,OAAAjP,KAAA,aAAAiP,OAAAjP,KAAA,UACA,IACA87B,EAAA7sB,OAAAjP,IACM,MAAAtD,GACN,cAGI,MAAAA,GACJ,aAGA,aAhBA,GAkBA,IAAA8gC,EAAA,SAAAzB,GAEA,UAAA9sB,SAAA,cAAAsuB,EAAA,CACA,OAAAzB,EAAAC,GAEA,IACA,OAAAD,EAAAC,GACG,MAAAr/B,GACH,eAIA8+B,EAAA,SAAAv8B,EAAAme,GACA,IAAAhL,EAAAgL,IAAA,aAAAA,IAAA,SACA,IAAAyJ,EAAAhqB,EAAAL,KAAA4gB,KAAA,oBACA,IAAAqgB,EAAAhC,EAAAre,GACA,IAAAsgB,EAAAtrB,GAAAvV,EAAAL,KAAA4gB,KAAA,kBACA,IAAAugB,EAAA,GAEA,IAAAvrB,IAAAyU,IAAA4W,EAAA,CACA,UAAAnkC,UAAA,sCAGA,IAAAskC,EAAAhC,GAAA/U,EACA,GAAA6W,GAAAtgB,EAAAtsB,OAAA,IAAAwF,EAAAkG,KAAA4gB,EAAA,IACA,QAAAvf,EAAA,EAAkBA,EAAAuf,EAAAtsB,SAAmB+M,EAAA,CACrC8/B,EAAA17B,KAAArQ,OAAAiM,KAIA,GAAA4/B,GAAArgB,EAAAtsB,OAAA,GACA,QAAAiP,EAAA,EAAkBA,EAAAqd,EAAAtsB,SAAmBiP,EAAA,CACrC49B,EAAA17B,KAAArQ,OAAAmO,SAEG,CACH,QAAA4H,KAAAyV,EAAA,CACA,KAAAwgB,GAAAj2B,IAAA,cAAArR,EAAAkG,KAAA4gB,EAAAzV,GAAA,CACAg2B,EAAA17B,KAAArQ,OAAA+V,MAKA,GAAAg0B,EAAA,CACA,IAAAkC,EAAAL,EAAApgB,GAEA,QAAApd,EAAA,EAAkBA,EAAA67B,EAAA/qC,SAAsBkP,EAAA,CACxC,KAAA69B,GAAAhC,EAAA77B,KAAA,gBAAA1J,EAAAkG,KAAA4gB,EAAAye,EAAA77B,IAAA,CACA29B,EAAA17B,KAAA45B,EAAA77B,MAIA,OAAA29B,GAGA5tC,EAAAC,QAAAwrC,qCCrHA,IAAAsC,EAAaptC,EAAQ,QACrB,IAAAmJ,EAAkBnJ,EAAQ,QAE1BX,EAAAC,QAAA,SAAA+tC,IACA,IAAAC,EAAAnkC,IACAikC,EAAAlsC,OAAA8H,UAAA,CAA2BI,KAAAkkC,GAAiB,CAAGlkC,KAAA,WAAoB,OAAAlI,OAAA8H,UAAAI,OAAAkkC,KACnE,OAAAA,uBCRAjuC,EAAAC,QAAA,SAAAmG,EAAAU,EAAAonC,GACA,IAAAC,EAAArnC,EAAAonC,EACA,OAAAnnC,KAAAC,MAAAmnC,GAAA,EAAAA,IAAAD,2BCFA,SAAAE,GAAA,IAAAC,EAEA,UAAAnvB,SAAA,aACAmvB,EAAAnvB,YACC,UAAAkvB,IAAA,aACDC,EAAAD,OACC,UAAA37B,OAAA,aACD47B,EAAA57B,SACC,CACD47B,EAAA,GAGAruC,EAAAC,QAAAouC,4DCVA,IAAAzoC,EAAmBjF,EAAQ,QAE3B,IAAAmF,EAAAF,EAAA,eACA,IAAA0oC,EAAA1oC,EAAA,iBAEA,IAAAW,EAAU5F,EAAQ,QAElB,IAAA4tC,EAAA,CAEAC,sBAAA,SAAAC,EAAAn7B,EAAAvL,GACA,GAAAuL,EAAAzL,KAAAE,KAAA,UACA,aAEA,IAAAC,EAAA,CACAC,mBAAA,KACAC,iBAAA,KACAC,UAAA,KACAC,UAAA,KACAC,YAAA,KACAC,eAAA,MAGA,QAAAC,KAAAR,EAAA,CACA,GAAAxB,EAAAwB,EAAAQ,KAAAP,EAAAO,GAAA,CACA,cAIA,IAAAC,EAAAjC,EAAAwB,EAAA,aACA,IAAAU,EAAAlC,EAAAwB,EAAA,YAAAxB,EAAAwB,EAAA,WACA,GAAAS,GAAAC,EAAA,CACA,UAAA3C,EAAA,sEAEA,cAIA9F,EAAAC,QAAA,SAAA+F,EAAAsN,EAAAo7B,EAAAC,EAAAhoC,GACA,IAAAioC,EAAAL,EAAAG,GACA,UAAAE,IAAA,YACA,UAAAN,EAAA,wBAAAI,GAEA,IAAAE,EAAAt7B,EAAA3M,GAAA,CACA,UAAAb,EAAA6oC,EAAA,cAAAD,GAEA5rC,QAAAC,IAAA6rC,EAAAt7B,EAAA3M,0CC/CA,IAAAkoC,EAAAluC,EAAA,YAAAmuC,EAAAnuC,EAAAkO,EAAAggC,GAA+kB,IAAA53B,EAAA63B,EAAG,sBCAllB,IAAA7oC,EAAAnE,OAAAoK,OAAA,SAAArI,GAA2C,OAAAA,OAE3C7D,EAAAC,QAAA6B,OAAAitC,UAAA,SAAA5nC,GAAkD,cAAAA,IAAA,WAAAlB,EAAAkB,QAAAy0B,UAAAz0B,KAAAy0B,+BCFlD,IAAAh7B,EAAA,WAEAZ,EAAAC,QAAA,SAAA+uC,EAAAnuC,GACA,GAAAA,EAAAE,OAAA,EACA,UAAAqP,MAAA,oCAEA,IAAA6+B,EAAAruC,EAAAsuC,MAAA,SAAAC,EAAArhC,GACA,OAAAjN,EAAAuuC,UAAAthC,KAAAqhC,IAGA,IAAAF,EACA,UAAA7+B,MAAA,kCAEA,IAAAtC,EAAA,EACA,IAAAuhC,EAAAxuC,EAAAuuC,UAAAthC,KACA,GAAAuhC,EAAA,EACA,UAAAj/B,MAAA,qDAEA,IAAAhB,EAAA,CAAgBgnB,SAAA,GAAAD,MAAA,IAChB,QAAAH,EAAA,EAAeA,EAAA,EAAKA,IACpBloB,GAAAwhC,EAAAlgC,EAAAvO,EAAAiN,GACA,OAAAsB,GAGA,SAAAkgC,EAAAlgC,EAAAvO,EAAAiN,GACA,GAAAA,EAAAjN,EAAAE,OAAA,EACA,SAEA,IAAAwuC,EAAA1uC,EAAAuuC,UAAAthC,KACA,IAAA0hC,EAAA3uC,EAAA4uC,YAAA3hC,GACAA,GAAA,EAEA,OAAAyhC,GACA,OACAngC,EAAAsgC,KAAAC,EAAA9uC,EAAAiN,GACA,MACA,OACAsB,EAAAslB,OAAAkb,EAAA/uC,EAAAiN,GACA,MACA,OACAsB,EAAAkmB,MAAAua,EAAAhvC,EAAAiN,EAAA0hC,GACA,MACA,OACApgC,EAAA+mB,MAAA2Z,EAAAjvC,EAAAiN,EAAA0hC,GACA,MACA,OACApgC,EAAAgnB,SAAA2Z,EAAAlvC,EAAAiN,EAAA0hC,GACA,MAEA,SAAAA,EAGA,SAAAG,EAAA9uC,EAAAiN,GACA,IAAA4hC,EAAA,GACAA,EAAAliC,KAAA3M,EAAAmvC,YAAAliC,GAEA,IAAAmiC,EAAApvC,EAAAuuC,UAAAthC,EAAA,GACA4hC,EAAAQ,OAAAD,GAAA,IACAP,EAAAS,QAAAF,GAAA,IACAP,EAAAU,OAAAH,GAAA,IACAP,EAAAW,KAAAJ,GAAA,IAGA,GAAAA,GAAA,IACAP,EAAAY,YAAA,EAEAZ,EAAAa,QAAA1vC,EAAAuuC,UAAAthC,EAAA,OACA4hC,EAAAc,SAAA3vC,EAAA4vC,aAAA3iC,EAAA,GACA4hC,EAAAgB,GAAA7vC,EAAAuuC,UAAAthC,EAAA,GACA4hC,EAAAiB,QAAA,CACA9vC,EAAA+vC,SAAA9iC,EAAA,GACAjN,EAAA+vC,SAAA9iC,EAAA,GACAjN,EAAA+vC,SAAA9iC,EAAA,GACAjN,EAAA+vC,SAAA9iC,EAAA,KAEA4hC,EAAAmB,QAAA,CACAhwC,EAAA+vC,SAAA9iC,EAAA,IACAjN,EAAA+vC,SAAA9iC,EAAA,KAEA4hC,EAAAoB,QAAAjwC,EAAAuuC,UAAAthC,EAAA,IACA4hC,EAAAqB,KAAAC,EAAAnwC,EAAAiN,EAAA,IACA,OAAA4hC,EAGA,SAAAE,EAAA/uC,EAAAiN,GACA,IAAA4mB,EAAA,GACAA,EAAAuc,WAAApwC,EAAA4vC,aAAA3iC,GACA4mB,EAAAwc,KAAArwC,EAAA4vC,aAAA3iC,EAAA,GACA4mB,EAAAC,OAAA9zB,EAAA4vC,aAAA3iC,EAAA,GACA4mB,EAAAG,OAAAh0B,EAAA4vC,aAAA3iC,EAAA,GACA4mB,EAAAY,MAAAz0B,EAAA4vC,aAAA3iC,EAAA,GACA,IAAAmiC,EAAApvC,EAAAuuC,UAAAthC,EAAA,IACA4mB,EAAAyc,OAAA,EACAzc,EAAA0c,UAAAvwC,EAAAuuC,UAAAthC,EAAA,IACA4mB,EAAA2c,QAAAxwC,EAAAuuC,UAAAthC,EAAA,IACA4mB,EAAA4c,UAAAzwC,EAAAuuC,UAAAthC,EAAA,IACA4mB,EAAA6c,SAAA1wC,EAAAuuC,UAAAthC,EAAA,IACA,OAAA4mB,EAGA,SAAAmb,EAAAhvC,EAAAiN,EAAAN,GACA,IAAA8nB,EAAA,GACA,IAAA5kB,EAAA8gC,EAAA3wC,EAAAiN,GACA,IAAA2jC,EAAA/gC,EAAA3P,OAAA,EACA,IAAAuU,EAAA9H,EAAAikC,EACA,QAAA5K,EAAA,EAAeA,EAAAvxB,EAASuxB,IAAA,CACxBvR,EAAAuR,GAAAhmC,EAAAL,MAAAsN,IAAA4C,EAAA3P,QAAAqL,SAAA,QACA0B,GAAA2jC,EAEA,OAAAnc,EAGA,SAAAwa,EAAAjvC,EAAAiN,EAAA0hC,GACA,IAAArZ,EAAA,GAEA,IAAA7gB,EAAAk6B,EAAA,GACA,QAAA3I,EAAA,EAAeA,EAAAvxB,EAASuxB,IAAA,CACxB,IAAA6K,EAAA,GACA,IAAAC,EAAA9K,EAAA,GACA6K,EAAA9vC,GAAAf,EAAA+wC,aAAA9jC,EAAA,EAAA6jC,GACAD,EAAAvqC,EAAAtG,EAAA4vC,aAAA3iC,EAAA,EAAA6jC,GACAD,EAAA9pC,EAAA/G,EAAA4vC,aAAA3iC,EAAA,EAAA6jC,GACAD,EAAA1gC,MAAAnQ,EAAA4vC,aAAA3iC,EAAA,EAAA6jC,GACAD,EAAA77B,OAAAhV,EAAA4vC,aAAA3iC,EAAA,GAAA6jC,GACAD,EAAArG,QAAAxqC,EAAAmvC,YAAAliC,EAAA,GAAA6jC,GACAD,EAAApG,QAAAzqC,EAAAmvC,YAAAliC,EAAA,GAAA6jC,GACAD,EAAAG,SAAAhxC,EAAAmvC,YAAAliC,EAAA,GAAA6jC,GACAD,EAAA5G,KAAAjqC,EAAAuuC,UAAAthC,EAAA,GAAA6jC,GACAD,EAAAI,KAAAjxC,EAAAuuC,UAAAthC,EAAA,GAAA6jC,GACAxb,EAAA0Q,GAAA6K,EAEA,OAAAvb,EAGA,SAAA4Z,EAAAlvC,EAAAiN,EAAA0hC,GACA,IAAApZ,EAAA,GACA,IAAA9gB,EAAAk6B,EAAA,GACA,QAAA3I,EAAA,EAAeA,EAAAvxB,EAASuxB,IAAA,CACxB,IAAAkL,EAAA,GACA,IAAAJ,EAAA9K,EAAA,GACAkL,EAAAC,MAAAnxC,EAAA+wC,aAAA9jC,EAAA,EAAA6jC,GACAI,EAAAE,OAAApxC,EAAA+wC,aAAA9jC,EAAA,EAAA6jC,GACAI,EAAAG,OAAArxC,EAAAmvC,YAAAliC,EAAA,EAAA6jC,GACAvb,EAAAyQ,GAAAkL,EAEA,OAAA3b,EAGA,SAAAob,EAAA3wC,EAAAgP,GACA,IAAAo4B,EAAAp4B,EACA,KAAQo4B,EAAApnC,EAAAE,OAAgBknC,IAAA,CACxB,GAAApnC,EAAAonC,KAAA,EACA,MAEA,OAAApnC,EAAAL,MAAAqP,EAAAo4B,GAGA,SAAA+I,EAAAnwC,EAAAgP,GACA,OAAA2hC,EAAA3wC,EAAAgP,GAAAzD,SAAA,gCC9JA,SAAA3L,GAAA,IAAA0xC,EAAUxxC,EAAQ,QAClB,IAAAyxC,EAAA,aACA,IAAAC,EAAiB1xC,EAAQ,QACzB,IAAA2xC,EAAe3xC,EAAQ,QACvB,IAAA4xC,EAAiB5xC,EAAQ,SACzB,IAAA6xC,EAAqB7xC,EAAQ,QAC7B,IAAA8xC,EAAY9xC,EAAQ,QAEpB,IAAA+xC,EAAA,SAAAC,IACA,OAAAlgC,KAAAmgC,gBAAA,wBAAAA,eADA,GAIA5yC,EAAAC,QAAA,SAAAiK,EAAA2oC,GACAA,aAAA,WAAAA,EAAAT,EAEA,UAAAloC,IAAA,SACAA,EAAA,CAAW0B,IAAA1B,QACX,IAAAA,EACAA,EAAA,GAEA,IAAA4oC,EAAA5oC,EAAA6oC,OACA,GAAAD,EACA5oC,EAAA8oC,EAAA9oC,GAEAioC,EAAAjoC,EAAA,SAAAiF,EAAA8jC,EAAAC,GACA,GAAA/jC,EACA,OAAA0jC,EAAA1jC,GACA,SAAAzC,KAAAumC,EAAAE,YACA,OAAAN,EAAA,IAAAziC,MAAA,qBAAA6iC,EAAAE,aACA,IAAAD,EACA,OAAAL,EAAA,IAAAziC,MAAA,mBAEA,IAAA2iC,EAAA,MAIA,GAAAK,EAAAF,GAAA,CACA,IAAAh+B,EAAA,IAAAmC,WAAA67B,GACAA,EAAA,IAAAzyC,EAAAyU,EAAA,UAKA,GAAAs9B,EAAAU,GAAA,CACAH,EAAA,KAEA,UAAAG,IAAA,SACAA,EAAA,IAAAzyC,EAAAyyC,EAAA,UAIA,IAAAH,EAAA,CAEA,GAAAtyC,EAAAN,SAAA+yC,GACAA,IAAA9mC,SAAAlC,EAAAmpC,UACAH,IAAAnpC,OAGA,IAAA/F,EACA,IACA,IAAArB,EAAAswC,EAAA/8B,QAAA,gBACA,GAAA68B,EACA/uC,EAAAuuC,EAAAW,QACA,UAAAxmC,KAAA/J,IAAAuwC,EAAAphC,OAAA,SACA9N,EAAAsvC,KAAAC,MAAAL,QACA,SAAAxmC,KAAA/J,IAAAuwC,EAAAphC,OAAA,SACA9N,EAAAsuC,EAAAY,QAEAlvC,EAAAquC,EAAAa,GACK,MAAAvmC,GACLkmC,EAAA,IAAAziC,MAAA,sBAAAzD,EAAAw8B,UACA0J,EAAAT,EAEAS,EAAA,KAAA7uC,MAIA,SAAAovC,EAAAz/B,GACA,IAAAhB,EAAA3Q,OAAA2H,UAAAyC,SACA,OAAAuG,EAAAlG,KAAAkH,KAAA,uBAGA,SAAAq/B,EAAA9oC,GAEA,GAAAwoC,EACA,OAAAD,EAAAvoC,EAAA,CAAuBspC,aAAA,gBAEvB,UAAA/gC,KAAAmgC,iBAAA,YACA,UAAAxiC,MAAA,6CAGA,IAAAqjC,EAAA,IAAAhhC,KAAAmgC,eACAa,EAAAC,iBAAA,sCACA,OAAAjB,EAAA,CACAN,IAAAsB,GACGvpC,qEC7FH,IAAAN,EAAWjJ,EAAQ,QACnB,IAAAotC,EAAaptC,EAAQ,QAErB,IAAA8I,EAAqB9I,EAAQ,QAC7B,IAAAmJ,EAAkBnJ,EAAQ,QAC1B,IAAAgzC,EAAWhzC,EAAQ,QAEnB,IAAAizC,EAAAhqC,EAAA6C,KAAA/C,SAAA+C,KAAA3C,KAEAikC,EAAA6F,EAAA,CACA9pC,cACAL,iBACAkqC,SAGA3zC,EAAAC,QAAA2zC,qCCfA,IAAAxmC,EAAiBzM,EAAQ,QAEzB,IAAAmM,EAAA9K,OAAA2H,UAAAyC,SACA,IAAA6B,EAAAjM,OAAA2H,UAAAsE,eAEA,IAAA4lC,EAAA,SAAAA,EAAA3+B,EAAA6a,EAAA+jB,GACA,QAAAhmC,EAAA,EAAA2jC,EAAAv8B,EAAAnU,OAAuC+M,EAAA2jC,EAAS3jC,IAAA,CAChD,GAAAG,EAAAxB,KAAAyI,EAAApH,GAAA,CACA,GAAAgmC,GAAA,MACA/jB,EAAA7a,EAAApH,KAAAoH,OACa,CACb6a,EAAAtjB,KAAAqnC,EAAA5+B,EAAApH,KAAAoH,OAMA,IAAA6+B,EAAA,SAAAA,EAAA/c,EAAAjH,EAAA+jB,GACA,QAAAhmC,EAAA,EAAA2jC,EAAAza,EAAAj2B,OAAwC+M,EAAA2jC,EAAS3jC,IAAA,CAEjD,GAAAgmC,GAAA,MACA/jB,EAAAiH,EAAAllB,OAAAhE,KAAAkpB,OACS,CACTjH,EAAAtjB,KAAAqnC,EAAA9c,EAAAllB,OAAAhE,KAAAkpB,MAKA,IAAAgd,EAAA,SAAAA,EAAA3mB,EAAA0C,EAAA+jB,GACA,QAAA7jC,KAAAod,EAAA,CACA,GAAApf,EAAAxB,KAAA4gB,EAAApd,GAAA,CACA,GAAA6jC,GAAA,MACA/jB,EAAA1C,EAAApd,KAAAod,OACa,CACb0C,EAAAtjB,KAAAqnC,EAAAzmB,EAAApd,KAAAod,OAMA,IAAAre,EAAA,SAAAA,EAAAilC,EAAAlkB,EAAAmkB,GACA,IAAA9mC,EAAA2iB,GAAA,CACA,UAAAxmB,UAAA,+BAGA,IAAAuqC,EACA,GAAA3uC,UAAApE,QAAA,GACA+yC,EAAAI,EAGA,GAAApnC,EAAAL,KAAAwnC,KAAA,kBACAJ,EAAAI,EAAAlkB,EAAA+jB,QACK,UAAAG,IAAA,UACLF,EAAAE,EAAAlkB,EAAA+jB,OACK,CACLE,EAAAC,EAAAlkB,EAAA+jB,KAIA9zC,EAAAC,QAAA+O,qCC3DA,IAAAlC,EAAA9K,OAAA2H,UAAAyC,SAEApM,EAAAC,QAAA,SAAAytC,EAAA/mC,GACA,IAAAgM,EAAA7F,EAAAL,KAAA9F,GACA,IAAA+kC,EAAA/4B,IAAA,qBACA,IAAA+4B,EAAA,CACAA,EAAA/4B,IAAA,kBACAhM,IAAA,aACAA,IAAA,iBACAA,EAAA5F,SAAA,UACA4F,EAAA5F,QAAA,GACA+L,EAAAL,KAAA9F,EAAAwtC,UAAA,oBAEA,OAAAzI,sCCbA,IAAAlrC,EAAAyB,MAAA0H,UAAAnJ,MACA,IAAAkrC,EAAa/qC,EAAQ,QAErB,IAAAyzC,EAAApyC,OAAAkN,KACA,IAAAu8B,EAAA2I,EAAA,SAAAllC,EAAA88B,GAA4C,OAAAoI,EAAApI,IAAyBrrC,EAAQ,QAE7E,IAAA0zC,EAAAryC,OAAAkN,KAEAu8B,EAAAkI,KAAA,SAAAW,IACA,GAAAtyC,OAAAkN,KAAA,CACA,IAAAqlC,EAAA,WAEA,IAAA/9B,EAAAxU,OAAAkN,KAAA/J,WACA,OAAAqR,KAAAzV,SAAAoE,UAAApE,OAHA,CAIG,KACH,IAAAwzC,EAAA,CACAvyC,OAAAkN,KAAA,SAAAA,EAAAme,GACA,GAAAqe,EAAAre,GAAA,CACA,OAAAgnB,EAAA7zC,EAAAiM,KAAA4gB,IAEA,OAAAgnB,EAAAhnB,SAGE,CACFrrB,OAAAkN,KAAAu8B,EAEA,OAAAzpC,OAAAkN,MAAAu8B,GAGAzrC,EAAAC,QAAAwrC,oEC/BA,SAAA+I,EAAAC,GACA,UAAAA,EAAA9tC,QAAA,YACA7D,QAAAiS,KAAA,2CAAA0/B,EAAAl4B,WAAA,sBACA,aAGA,YAGA,SAAAm4B,EAAAC,EAAArpB,GACA,IAAAqpB,IAAArpB,EACA,aAEA,QAAAxd,EAAA,EAAA2jC,EAAAnmB,EAAAvqB,OAAwC+M,EAAA2jC,EAAS3jC,IAAA,CACjD,IACA,GAAA6mC,EAAAC,SAAAtpB,EAAAxd,IAAA,CACA,YAEA,GAAAwd,EAAAxd,GAAA8mC,SAAAD,GAAA,CACA,cAEK,MAAAhoC,GACL,cAIA,aAGA,SAAAkoC,EAAAC,GACA,cAAAA,EAAAC,oBAAA,aAAAD,EAAAC,kBAAAC,UAGA/0C,EAAAD,EAAAC,QAAA,CACA2J,KAAA,SAAAqrC,EAAAR,EAAAK,GACA,IAAAN,EAAAC,GAAA,OAGA,SAAAS,EAAAvoC,GACA,IAAAmoC,EAAAvL,QAAA,OAGA,IAAAje,EAAA3e,EAAAwoC,MAAAxoC,EAAAyoC,cAAAzoC,EAAAyoC,eACA9pB,KAAAvqB,OAAA,GAAAuqB,EAAA+pB,QAAA1oC,EAAAyC,QAEA,GAAA6lC,EAAAL,SAAAjoC,EAAAyC,SAAAslC,EAAAI,EAAAvL,QAAAoL,UAAArpB,GAAA,OAEA2pB,EAAAK,oBAAAl5B,SAAAzP,GAIAsoC,EAAAK,oBAAA,CACAJ,UACA94B,SAAAq4B,EAAA9tC,QAEAkuC,EAAAC,IAAA3hC,SAAAkT,iBAAA,QAAA6uB,IAGA/yC,OAAA,SAAA8yC,EAAAR,GACA,GAAAD,EAAAC,GAAAQ,EAAAK,oBAAAl5B,SAAAq4B,EAAA9tC,OAGA4uC,OAAA,SAAAN,EAAAR,EAAAK,IAEAD,EAAAC,IAAA3hC,SAAAstB,oBAAA,QAAAwU,EAAAK,oBAAAJ,gBACAD,EAAAK,yDCjEA,IAAAE,EAAA70C,EAAA,YAAA80C,EAAA90C,EAAAkO,EAAA2mC,GAAulB,IAAAv+B,EAAAw+B,EAAG,qCC6B1lB,IAAAnnC,EAEA,IAAAonC,EAAA1zC,OAAA2zC,yBACA,WAAiB,OAAA3zC,OAAA2zC,yBAAAxwC,UAAA,UAAA8D,IAAjB,GACA,WAAgB,UAAAM,WAEhB,IAAAqsC,SAAA1oC,SAAA,mBAAAA,OAAA6iB,WAAA,SAEA,IAAA8lB,EAAA7zC,OAAA8zC,gBAAA,SAAA3uC,GAAsD,OAAAA,EAAA4uC,WAEtD,IAAAC,EACA,IAAAC,EAAAD,EAAAH,EAAAG,GAAA1nC,EACA,IAAA4nC,EACA,IAAAC,EAAAD,IAAA51C,YAAAgO,EACA,IAAA8nC,EACA,IAAAC,EAAAD,EAAAP,EAAAO,GAAA9nC,EACA,IAAAgoC,EAAAF,MAAA9nC,EAEA,IAAAioC,SAAAl/B,aAAA,YAAA/I,EAAAunC,EAAAx+B,YAEA,IAAAm/B,EAAA,CACAC,YAAAx0C,MACAy0C,yBAAAC,cAAA,YAAAroC,EAAAqoC,YACAC,kCAAAD,cAAA,YAAAroC,EAAAqoC,YAAAhtC,UACAktC,6BAAAjB,EAAAC,EAAA,GAAA3oC,OAAA6iB,aAAAzhB,EACAwoC,qBAAA70C,MAAA0H,UACAotC,yBAAA90C,MAAA0H,UAAAgb,QACAqyB,yBAAA/0C,MAAA0H,UAAAqF,QACAioC,sBAAAh1C,MAAA0H,UAAAuF,KACAgoC,wBAAAj1C,MAAA0H,UAAA09B,OACA8P,qCAAA7oC,EACA8oC,oBAAAjB,EACAkB,6BAAAlB,IAAAxsC,UAAA2E,EACAgpC,qBAAAlB,EAAAP,EAAAS,GAAAhoC,EACAipC,6BAAAlB,EACAmB,8BAAAnB,IAAA1sC,UAAA2E,EACAmpC,6BAAAnB,GAAAV,GAAA1oC,OAAAwqC,cAAApB,EAAAppC,OAAAwqC,iBAAAppC,EACAqpC,qBAAAC,UAAA,YAAAtpC,EAAAspC,QACAC,cAAAC,QACAC,uBAAAD,QAAAnuC,UACAquC,sBAAAC,WAAA,YAAA3pC,EAAA2pC,SACAC,+BAAAD,WAAA,YAAA3pC,EAAA2pC,SAAAtuC,UACAwuC,WAAAC,KACAC,oBAAAD,KAAAzuC,UACA2uC,gBAAAC,UACAC,yBAAAC,mBACAC,gBAAAC,UACAC,yBAAAC,mBACAC,YAAA1oC,MACA2oC,qBAAA3oC,MAAAzG,UACAqvC,WAAAC,KACAC,gBAAAC,UACAC,yBAAAD,UAAAxvC,UACA0vC,0BAAA7hC,eAAA,YAAAlJ,EAAAkJ,aACA8hC,mCAAA9hC,eAAA,YAAAlJ,EAAAkJ,aAAA7N,UACA4vC,0BAAA9hC,eAAA,YAAAnJ,EAAAmJ,aACA+hC,mCAAA/hC,eAAA,YAAAnJ,EAAAmJ,aAAA9N,UACA8vC,eAAA/vC,SACAgwC,wBAAAhwC,SAAAC,UACAgwC,gBAAA3D,EAAAH,EAAAG,KAAA1nC,EACAsrC,wBAAA3D,EACA4D,yBAAA5D,IAAAtsC,UAAA2E,EACAwrC,uBAAA5iC,YAAA,YAAA5I,EAAA4I,UACA6iC,gCAAA7iC,YAAA,YAAA5I,EAAA4I,UAAAvN,UACAqwC,wBAAA7iC,aAAA,YAAA7I,EAAA6I,WACA8iC,iCAAA9iC,aAAA,YAAA7I,EAAA4I,UAAAvN,UACAuwC,wBAAA9iC,aAAA,YAAA9I,EAAA8I,WACA+iC,iCAAA/iC,aAAA,YAAA9I,EAAA8I,WAAAzN,UACAywC,eAAArL,SACAsL,YAAAnuC,MACAouC,wBAAA1E,EAAAC,IAAA,GAAA3oC,OAAA6iB,cAAAzhB,EACAisC,WAAAjH,KACAkH,gBAAAlH,KAAAC,MACAkH,iBAAAC,MAAA,YAAApsC,EAAAosC,IACAC,kCAAAD,MAAA,cAAA9E,EAAAtnC,EAAAunC,GAAA,IAAA6E,KAAAxtC,OAAA6iB,aACA6qB,0BAAAF,MAAA,YAAApsC,EAAAosC,IAAA/wC,UACAkxC,WAAA9zC,KACA+zC,aAAAh5C,OACAi5C,sBAAAj5C,OAAA6H,UACAqxC,aAAAh5C,OACAi5C,sBAAAj5C,OAAA2H,UACAuxC,wBAAAl5C,OAAA2H,UAAAyC,SACA+uC,uBAAAn5C,OAAA2H,UAAAyxC,QACAC,iBAAA7tB,WACA8tB,eAAAxsB,SACAysB,qBAAAtX,UAAA,YAAA31B,EAAA21B,QACAuX,8BAAAvX,UAAA,YAAA31B,EAAA21B,QAAAt6B,UACA8xC,+BAAAxX,UAAA,YAAA31B,EAAA21B,QAAAt6B,UAAAlG,KACAi4C,yBAAAzX,UAAA,YAAA31B,EAAA21B,QAAA0X,IACAC,4BAAA3X,UAAA,YAAA31B,EAAA21B,QAAA4X,OACAC,6BAAA7X,UAAA,YAAA31B,EAAA21B,QAAAC,QACA6X,mBAAAC,QAAA,YAAA1tC,EAAA0tC,MACAC,iBAAAC,WACAC,0BAAAD,WAAAvyC,UACAyyC,qBAAAC,eACAC,8BAAAD,eAAA1yC,UACA4yC,qBAAAC,UAAA,YAAAluC,EAAAkuC,QACAC,aAAAC,OACAC,sBAAAD,OAAA/yC,UACAizC,iBAAAC,MAAA,YAAAvuC,EAAAuuC,IACAC,kCAAAD,MAAA,cAAAjH,EAAAtnC,EAAAunC,GAAA,IAAAgH,KAAA3vC,OAAA6iB,aACAgtB,0BAAAF,MAAA,YAAAvuC,EAAAuuC,IAAAlzC,UACAqzC,+BAAAC,oBAAA,YAAA3uC,EAAA2uC,kBACAC,wCAAAD,oBAAA,YAAA3uC,EAAA2uC,kBAAAtzC,UACAwzC,aAAAt7C,OACAu7C,8BAAAxH,EAAAC,EAAA,GAAA3oC,OAAA6iB,aAAAzhB,EACA+uC,sBAAAx7C,OAAA8H,UACA2zC,aAAA1H,EAAA1oC,OAAAoB,EACAivC,sBAAA3H,EAAA1oC,OAAAvD,UAAA2E,EACAkvC,kBAAAC,YACAC,2BAAAD,YAAA9zC,UACAg0C,qBAAAjI,EACAkI,iBAAArH,EACAsH,0BAAAtH,IAAA5sC,UAAA2E,EACAwvC,gBAAAv0C,UACAw0C,yBAAAx0C,UAAAI,UACAq0C,wBAAA3mC,aAAA,YAAA/I,EAAA+I,WACA4mC,iCAAA5mC,aAAA,YAAA/I,EAAA+I,WAAA1N,UACAu0C,+BAAAxmC,oBAAA,YAAApJ,EAAAoJ,kBACAymC,wCAAAzmC,oBAAA,YAAApJ,EAAAoJ,kBAAA/N,UACAy0C,yBAAA9mC,cAAA,YAAAhJ,EAAAgJ,YACA+mC,kCAAA/mC,cAAA,YAAAhJ,EAAAgJ,YAAA3N,UACA20C,yBAAA/mC,cAAA,YAAAjJ,EAAAiJ,YACAgnC,kCAAAhnC,cAAA,YAAAjJ,EAAAiJ,YAAA5N,UACA60C,eAAAC,SACAC,wBAAAD,SAAA90C,UACAg1C,qBAAAC,UAAA,YAAAtwC,EAAAswC,QACAC,8BAAAD,UAAA,YAAAtwC,EAAAswC,QAAAj1C,UACAm1C,qBAAAC,UAAA,YAAAzwC,EAAAywC,QACAC,8BAAAD,UAAA,YAAAzwC,EAAAywC,QAAAp1C,WAGA3J,EAAAC,QAAA,SAAA2F,EAAAgS,EAAAqnC,GACA,GAAA95C,UAAApE,OAAA,UAAAk+C,IAAA,WACA,UAAA11C,UAAA,6CAGA,IAAAhB,EAAA,KAAAqP,EACA,KAAArP,KAAAiuC,GAAA,CACA,UAAAiH,YAAA,aAAA7lC,EAAA,oBAIA,UAAA4+B,EAAAjuC,KAAA,cAAA02C,EAAA,CACA,UAAA11C,UAAA,aAAAqO,EAAA,wDAEA,OAAA4+B,EAAAjuC,uCC9KA,IAAA2W,EAAave,EAAQ,QACrB,IAAAm2B,EAAiBn2B,EAAQ,QACzB,IAAAu+C,EAAmBv+C,EAAQ,QAC3B,IAAA8xC,EAAY9xC,EAAQ,QAEpBX,EAAAC,QAAAk/C,EAEAn/C,EAAAC,QAAAm/C,QAAAD,EACAA,EAAAvM,eAAA1zB,EAAA0zB,gBAAAR,EACA+M,EAAAE,eAAA,wBAAAF,EAAAvM,eAAAuM,EAAAvM,eAAA1zB,EAAAmgC,eAEAxL,EAAA,sDAAAyL,GACAH,EAAAG,IAAA,eAAAA,GAAA,SAAA1zC,EAAA8U,EAAAtE,GACAsE,EAAA6+B,EAAA3zC,EAAA8U,EAAAtE,GACAsE,EAAA4+B,SAAAE,cACA,OAAAC,EAAA/+B,MAIA,SAAAmzB,EAAA3+B,EAAA6a,GACA,QAAAjiB,EAAA,EAAmBA,EAAAoH,EAAAnU,OAAkB+M,IAAA,CACrCiiB,EAAA7a,EAAApH,KAIA,SAAA4xC,EAAAx/C,GACA,QAAA4N,KAAA5N,EAAA,CACA,GAAAA,EAAA+N,eAAAH,GAAA,aAEA,YAGA,SAAAyxC,EAAA3zC,EAAA8U,EAAAtE,GACA,IAAAb,EAAA3P,EAEA,GAAAkrB,EAAApW,GAAA,CACAtE,EAAAsE,EACA,UAAA9U,IAAA,UACA2P,EAAA,CAAsB3P,YAEjB,CACL2P,EAAAk3B,EAAA/xB,EAAA,CAAiC9U,QAGjC2P,EAAAa,WACA,OAAAb,EAGA,SAAA4jC,EAAAvzC,EAAA8U,EAAAtE,GACAsE,EAAA6+B,EAAA3zC,EAAA8U,EAAAtE,GACA,OAAAqjC,EAAA/+B,GAGA,SAAA++B,EAAA/+B,GACA,UAAAA,EAAAtE,WAAA,aACA,UAAAhM,MAAA,6BAGA,IAAAuvC,EAAA,MACA,IAAAvjC,EAAA,SAAAwjC,EAAAzwC,EAAA0wC,EAAA3M,GACA,IAAAyM,EAAA,CACAA,EAAA,KACAj/B,EAAAtE,SAAAjN,EAAA0wC,EAAA3M,KAIA,SAAA4M,IACA,GAAA3N,EAAA4N,aAAA,GACA54B,WAAA64B,EAAA,IAIA,SAAAC,IAEA,IAAA/M,EAAA5kC,UAEA,GAAA6jC,EAAA0N,SAAA,CACA3M,EAAAf,EAAA0N,aACS,CACT3M,EAAAf,EAAA+N,cAAAC,EAAAhO,GAGA,GAAAiO,EAAA,CACA,IACAlN,EAAAI,KAAAC,MAAAL,GACa,MAAAvmC,KAGb,OAAAumC,EAGA,SAAAmN,EAAAC,GACAr5B,aAAAs5B,GACA,KAAAD,aAAAlwC,OAAA,CACAkwC,EAAA,IAAAlwC,MAAA,IAAAkwC,GAAA,iCAEAA,EAAAnN,WAAA,EACA,OAAA/2B,EAAAkkC,EAAAE,GAIA,SAAAR,IACA,GAAAS,EAAA,OACA,IAAAC,EACAz5B,aAAAs5B,GACA,GAAA7/B,EAAAigC,QAAAxO,EAAAuO,SAAApyC,UAAA,CAEAoyC,EAAA,QACS,CACTA,EAAAvO,EAAAuO,SAAA,SAAAvO,EAAAuO,OAEA,IAAAb,EAAAW,EACA,IAAArxC,EAAA,KAEA,GAAAuxC,IAAA,GACAb,EAAA,CACA3M,KAAA+M,IACA9M,WAAAuN,EACApB,SACAppC,QAAA,GACA0qC,IAAAh1C,EACAi1C,WAAA1O,GAEA,GAAAA,EAAA2O,sBAAA,CACAjB,EAAA3pC,QAAAgpC,EAAA/M,EAAA2O,8BAES,CACT3xC,EAAA,IAAAiB,MAAA,iCAEA,OAAAgM,EAAAjN,EAAA0wC,IAAA3M,MAGA,IAAAf,EAAAzxB,EAAAyxB,KAAA,KAEA,IAAAA,EAAA,CACA,GAAAzxB,EAAAqgC,MAAArgC,EAAAigC,OAAA,CACAxO,EAAA,IAAAgN,EAAAE,mBACS,CACTlN,EAAA,IAAAgN,EAAAvM,gBAIA,IAAArqC,EACA,IAAAk4C,EACA,IAAA70C,EAAAumC,EAAAyO,IAAAlgC,EAAA9U,KAAA8U,EAAAkgC,IACA,IAAAtB,EAAAnN,EAAAmN,OAAA5+B,EAAA4+B,QAAA,MACA,IAAApM,EAAAxyB,EAAAwyB,MAAAxyB,EAAApd,KACA,IAAA4S,EAAAi8B,EAAAj8B,QAAAwK,EAAAxK,SAAA,GACA,IAAA8qC,IAAAtgC,EAAAsgC,KACA,IAAAZ,EAAA,MACA,IAAAG,EACA,IAAAC,EAAA,CACAtN,KAAA5kC,UACA4H,QAAA,GACAi9B,WAAA,EACAmM,SACAsB,IAAAh1C,EACAi1C,WAAA1O,GAGA,YAAAzxB,KAAAgO,OAAA,OACA0xB,EAAA,KACAlqC,EAAA,WAAAA,EAAA,YAAAA,EAAA,8BACA,GAAAopC,IAAA,OAAAA,IAAA,QACAppC,EAAA,iBAAAA,EAAA,kBAAAA,EAAA,oCACAg9B,EAAAI,KAAA2N,UAAAvgC,EAAAgO,OAAA,KAAAwkB,EAAAxyB,EAAAgO,OAIAyjB,EAAA+O,mBAAApB,EACA3N,EAAAgP,OAAAnB,EACA7N,EAAAiP,QAAAf,EAEAlO,EAAAkP,WAAA,aAGAlP,EAAAmP,QAAA,WACAb,EAAA,MAEAtO,EAAAoP,UAAAlB,EACAlO,EAAAqP,KAAAlC,EAAA1zC,GAAAo1C,EAAAtgC,EAAA+gC,SAAA/gC,EAAAghC,UAEA,IAAAV,EAAA,CACA7O,EAAAwP,kBAAAjhC,EAAAihC,gBAKA,IAAAX,GAAAtgC,EAAAkhC,QAAA,GACArB,EAAAp5B,WAAA,WACA,GAAAs5B,EAAA,OACAA,EAAA,KACAtO,EAAA0P,MAAA,WACA,IAAAl1C,EAAA,IAAAyD,MAAA,0BACAzD,EAAAm1C,KAAA,YACAzB,EAAA1zC,IACS+T,EAAAkhC,SAGT,GAAAzP,EAAA4P,iBAAA,CACA,IAAAx5C,KAAA2N,EAAA,CACA,GAAAA,EAAAjI,eAAA1F,GAAA,CACA4pC,EAAA4P,iBAAAx5C,EAAA2N,EAAA3N,WAGK,GAAAmY,EAAAxK,UAAAwpC,EAAAh/B,EAAAxK,SAAA,CACL,UAAA9F,MAAA,qDAGA,oBAAAsQ,EAAA,CACAyxB,EAAAqB,aAAA9yB,EAAA8yB,aAGA,kBAAA9yB,UACAA,EAAAshC,aAAA,WACA,CACAthC,EAAAshC,WAAA7P,GAMAA,EAAA8P,KAAA/O,GAAA,MAEA,OAAAf,EAKA,SAAAgO,EAAAhO,GAGA,IACA,GAAAA,EAAAqB,eAAA,YACA,OAAArB,EAAA+P,YAEA,IAAAC,EAAAhQ,EAAA+P,aAAA/P,EAAA+P,YAAA1kB,gBAAA4kB,WAAA,cACA,GAAAjQ,EAAAqB,eAAA,KAAA2O,EAAA,CACA,OAAAhQ,EAAA+P,aAEK,MAAAv1C,IAEL,YAGA,SAAAylC,yCCtPA,IAAAiQ,EAAA1hD,EAAA,YAAA2hD,EAAA3hD,EAAAkO,EAAAwzC,GAA4lB,IAAAprC,EAAAqrC,EAAG,sBCI/lB,IAAAC,EAAA,WAEAviD,EAAAC,QAAA,SAAAuiD,EAAAtiD,GACA,GAAAqiD,KAAAriD,EAAA,CACAA,EAAA,WAAAA,EAAAqiD,UACAriD,EAAAqiD,GAGA,QAAAtyC,KAAA/P,EAAA,CACA,GAAA+P,IAAA,QAAAA,IAAA,UACA,cACA,GAAAA,IAAA,WAAAA,IAAA,UACA/P,EAAA+P,GAAA4mB,EAAA32B,EAAA+P,SAEA/P,EAAA+P,GAAA6e,SAAA5uB,EAAA+P,GAAA,IAEA,OAAA/P,GAGA,SAAA22B,EAAAvzB,GACA,OAAAA,EAAAyL,MAAA,KAAAxE,IAAA,SAAA8D,GACA,OAAAygB,SAAAzgB,EAAA,6BCzBA,IAAAo0C,EAAe9hD,EAAQ,QACvB,IAAA8xC,EAAY9xC,EAAQ,QACpB,IAAAmG,EAAanG,EAAQ,QAErB,IAAA+hD,EAAA,sDACA,IAAAC,EAAA,UACA,IAAAC,EAAA,0DAGA,IAAAC,EAAA,KAAAC,WAAA,GACA,IAAAC,EAAA,IAAAD,WAAA,GACA,IAAAE,EAAA,EACAC,EAAA,EACAC,EAAA,EAEAljD,EAAAC,QAAA,SAAA2zB,EAAA1pB,GACA,WAAAi5C,EAAAj5C,IAGA,SAAAi5C,EAAAj5C,GACAhJ,KAAAoR,OAAA,GACApR,KAAAkiD,SAAAliD,KAAAmiD,eAAAz5C,KAAA1I,MACAA,KAAAiB,OAAA+H,GAGAi5C,EAAAx5C,UAAAxH,OAAA,SAAA+H,GACAA,EAAAuoC,EAAA,CACArhC,QAAAlQ,KAAAkiD,UACGl5C,GACHhJ,KAAAmzB,KAAAnqB,EACAhJ,KAAAmzB,KAAAivB,QAAAx8C,EAAA5F,KAAAmzB,KAAAivB,QAAA,GAEA,IAAAp5C,EAAAoqB,KACA,UAAAlkB,MAAA,oCAEA,IAAAkC,EAAApR,KAAAoR,OACA,IAAA5B,EAAAxG,EAAAwG,MAAA,GACA,IAAA4jB,EAAApqB,EAAAoqB,KACApzB,KAAAqiD,kBAAAjvB,GAEA,IAAA3jB,EAAA8xC,EAAA9xC,MAAAD,EAAAxG,GACA,IAAAs5C,EAAAt5C,EAAA8G,OAAA,EAGAsB,EAAAvR,OAAA,EAGA,IAAA0iD,EAAA9yC,EAAA+yC,OAAA,SAAAr/C,EAAAuM,GACA,OAAA7J,KAAAoK,IAAA9M,EAAAuM,EAAAI,MAAAwyC,IACG,GAGH,IAAAr8C,EAAA,EACA,IAAAS,EAAA,EACA,IAAAqpC,EAAAnqC,EAAAoD,EAAA+mC,WAAA3c,EAAAI,OAAAuc,YACA,IAAA0S,EAAArvB,EAAAI,OAAAwc,KACA,IAAA0S,EAAA3S,EAAA0S,EACA,IAAAE,EAAA35C,EAAA25C,eAAA,EACA,IAAAhuC,EAAAo7B,EAAAtgC,EAAA5P,OAAA6iD,EACA,IAAAzf,EAAA2f,EAAA5iD,KAAAmzB,KAAA8P,OAGAv8B,GAAAiO,EAGA3U,KAAA6iD,OAAAN,EACAviD,KAAA8iD,QAAAnuC,EACA3U,KAAA+iD,WAAAhT,EAAA0S,EACAziD,KAAAgjD,UAAAP,EACAziD,KAAAijD,SAAAC,EAAA9vB,GACApzB,KAAAmjD,WAAAC,EAAAhwB,GACApzB,KAAAqjD,YAAAtT,EACA/vC,KAAAsjD,UAAAvT,EAAA2S,EAAA1iD,KAAAijD,SAGA,IAAA1xC,EAAAvR,KACAyP,EAAA3B,QAAA,SAAA4B,EAAA6zC,GACA,IAAA5zC,EAAAD,EAAAC,MACA,IAAAC,EAAAF,EAAAE,IACA,IAAAg5B,EAAAl5B,EAAAI,MACA,IAAA0zC,EAGA,QAAA52C,EAAA+C,EAAqB/C,EAAAgD,EAAOhD,IAAA,CAC5B,IAAAlM,EAAA8O,EAAAoyC,WAAAh1C,GACA,IAAAinB,EAAAtiB,EAAAkyC,SAAArwB,EAAA1yB,GACA,GAAAmzB,EAAA,CACA,GAAA2vB,EACAv9C,GAAAy9C,EAAAtwB,EAAAowB,EAAA9iD,GAAAmzB,EAAAnzB,IAEA,IAAAijD,EAAA19C,EACA,GAAAg9B,IAAA8e,EACA4B,IAAApB,EAAA3Z,GAAA,OACA,GAAA3F,IAAA+e,EACA2B,GAAApB,EAAA3Z,EAEAx3B,EAAAJ,KAAA,CACAsG,SAAA,CAAAqsC,EAAAj9C,GACAtE,KAAAyxB,EACA7gB,MAAApG,EACA8C,KAAA6zC,IAIAt9C,GAAA4tB,EAAA8c,SAAAgS,EACAa,EAAA3vB,GAKAntB,GAAAqpC,EACA9pC,EAAA,IAEAjG,KAAA4jD,YAAAn0C,EAAA5P,QAGAoiD,EAAAx5C,UAAA45C,kBAAA,SAAAjvB,GAGApzB,KAAA6jD,oBAAA,KACA7jD,KAAA8jD,kBAAA,KAEA,IAAA1wB,EAAA6B,OAAA7B,EAAA6B,MAAAp1B,SAAA,EACA,OAKA,IAAAy1B,EAAAyuB,EAAA3wB,EAAAyuB,IACAmC,EAAA5wB,IACAA,EAAA6B,MAAA,GAGA,IAAAgvB,EAAAjkD,KAAAmzB,KAAAivB,QAAA9sB,EAAAqb,SACA3wC,KAAA6jD,oBAAAvuB,EACAt1B,KAAA8jD,kBAAAvS,EAAAjc,EAAA,CACArvB,EAAA,EAAAS,EAAA,EAAAiqC,SAAAsT,EAAAvjD,GAAAihD,EACAxX,QAAA,EAAAC,QAAA,EAAAt6B,MAAA,EAAA6E,OAAA,KAIAstC,EAAAx5C,UAAAg7C,SAAA,SAAArwB,EAAA1yB,GACA,IAAAmzB,EAAAkwB,EAAA3wB,EAAA1yB,GACA,GAAAmzB,EACA,OAAAA,OACA,GAAAnzB,IAAAihD,EACA,OAAA3hD,KAAA8jD,uBACA,GAAApjD,IAAAmhD,EACA,OAAA7hD,KAAA6jD,oBACA,aAGA5B,EAAAx5C,UAAA05C,eAAA,SAAA3yC,EAAAG,EAAAC,EAAAE,GACA,IAAA6yC,EAAA3iD,KAAAmzB,KAAAwvB,eAAA,EACA,IAAAvvB,EAAApzB,KAAAmzB,KAAAC,KACA,IAAA8wB,EAAA,EACA,IAAAC,EAAA,EACA,IAAA/vC,EAAA,EACA,IAAAyf,EACA,IAAA2vB,EAEA,IAAApwB,EAAA6B,OAAA7B,EAAA6B,MAAAp1B,SAAA,GACA,OACA8P,QACAC,IAAAD,EACAG,MAAA,GAIAF,EAAA/J,KAAAwL,IAAA7B,EAAA3P,OAAA+P,GACA,QAAAhD,EAAA+C,EAAmB/C,EAAAgD,EAAShD,IAAA,CAC5B,IAAAlM,EAAA8O,EAAAoyC,WAAAh1C,GACA,IAAAinB,EAAA7zB,KAAAyjD,SAAArwB,EAAA1yB,GAEA,GAAAmzB,EAAA,CAEA,IAAAuwB,EAAAvwB,EAAAsW,QACA,IAAA0G,EAAA2S,EAAAE,EAAAtwB,EAAAowB,EAAA9iD,GAAAmzB,EAAAnzB,IAAA,EACAwjD,GAAArT,EAEA,IAAAwT,EAAAH,EAAArwB,EAAA8c,SAAAgS,EACA,IAAA2B,EAAAJ,EAAArwB,EAAA/jB,MAGA,GAAAw0C,GAAAx0C,GAAAu0C,GAAAv0C,EACA,MAGAo0C,EAAAG,EACAF,EAAAG,EACAd,EAAA3vB,EAEAzf,IAIA,GAAAovC,EACAW,GAAAX,EAAArZ,QAEA,OACAx6B,QACAC,IAAAD,EAAAyE,EACAtE,MAAAq0C,IAKC,kBACD,uBACA,qBACA,YACA,cAAAr2C,QAAAy2C,GAEA,SAAAA,EAAA7tC,GACA5V,OAAA0jD,eAAAvC,EAAAx5C,UAAAiO,EAAA,CACA3O,IAAA08C,EAAA/tC,GACA5O,aAAA,OAKA,SAAA28C,EAAA/tC,GACA,WAAAlO,SAAA,CACA,mBAAAkO,EAAA,OACA,kBAAAA,EACA,KACA9M,KAAA,MAJA,GAOA,SAAAm6C,EAAA3wB,EAAA1yB,GACA,IAAA0yB,EAAA6B,OAAA7B,EAAA6B,MAAAp1B,SAAA,EACA,YAEA,IAAA6kD,EAAAC,EAAAvxB,EAAA6B,MAAAv0B,GACA,GAAAgkD,GAAA,EACA,OAAAtxB,EAAA6B,MAAAyvB,GACA,YAGA,SAAAxB,EAAA9vB,GACA,QAAAxmB,EAAA,EAAeA,EAAA40C,EAAA3hD,OAAoB+M,IAAA,CACnC,IAAAlM,EAAA8gD,EAAA50C,GAAAg1C,WAAA,GACA,IAAApxC,EAAAm0C,EAAAvxB,EAAA6B,MAAAv0B,GACA,GAAA8P,GAAA,EACA,OAAA4iB,EAAA6B,MAAAzkB,GAAAmE,OAEA,SAGA,SAAAqvC,EAAA5wB,GACA,QAAAxmB,EAAA,EAAeA,EAAA60C,EAAA5hD,OAAmB+M,IAAA,CAClC,IAAAlM,EAAA+gD,EAAA70C,GAAAg1C,WAAA,GACA,IAAApxC,EAAAm0C,EAAAvxB,EAAA6B,MAAAv0B,GACA,GAAA8P,GAAA,EACA,OAAA4iB,EAAA6B,MAAAzkB,GAEA,SAGA,SAAA4yC,EAAAhwB,GACA,QAAAxmB,EAAA,EAAeA,EAAA80C,EAAA7hD,OAAsB+M,IAAA,CACrC,IAAAlM,EAAAghD,EAAA90C,GAAAg1C,WAAA,GACA,IAAApxC,EAAAm0C,EAAAvxB,EAAA6B,MAAAv0B,GACA,GAAA8P,GAAA,EACA,OAAA4iB,EAAA6B,MAAAzkB,GAAAmE,OAEA,SAGA,SAAA+uC,EAAAtwB,EAAAlB,EAAAC,GACA,IAAAiB,EAAA8B,UAAA9B,EAAA8B,SAAAr1B,SAAA,EACA,SAEA,IAAA+kD,EAAAxxB,EAAA8B,SACA,QAAAtoB,EAAA,EAAeA,EAAAg4C,EAAA/kD,OAAgB+M,IAAA,CAC/B,IAAAikC,EAAA+T,EAAAh4C,GACA,GAAAikC,EAAAC,QAAA5e,GAAA2e,EAAAE,SAAA5e,EACA,OAAA0e,EAAAG,OAEA,SAGA,SAAA4R,EAAA3f,GACA,GAAAA,IAAA,SACA,OAAA8e,OACA,GAAA9e,IAAA,QACA,OAAA+e,EACA,OAAAF,EAGA,SAAA6C,EAAA3wC,EAAAvO,EAAAkK,GACAA,KAAA,EACA,QAAA/C,EAAA+C,EAAqB/C,EAAAoH,EAAAnU,OAAkB+M,IAAA,CACvC,GAAAoH,EAAApH,GAAAlM,KAAA+E,EAAA,CACA,OAAAmH,GAGA,6CCvSA,IAAAoB,EAAWvO,EAAQ,QACnB,IAAAi1C,SAAA1oC,SAAA,mBAAAA,OAAA,kBAEA,IAAAJ,EAAA9K,OAAA2H,UAAAyC,SACA,IAAAuK,EAAA1U,MAAA0H,UAAAgN,OACA,IAAAovC,EAAA/jD,OAAA0jD,eAEA,IAAA5uB,EAAA,SAAAC,GACA,cAAAA,IAAA,YAAAjqB,EAAAL,KAAAsqB,KAAA,qBAGA,IAAAivB,EAAA,WACA,IAAA9lD,EAAA,GACA,IACA6lD,EAAA7lD,EAAA,KAAgC6I,WAAA,MAAApC,MAAAzG,IAEhC,QAAA+lD,KAAA/lD,EAAA,CACA,aAEA,OAAAA,EAAAiH,IAAAjH,EACE,MAAAyM,GACF,eAGA,IAAAu5C,EAAAH,GAAAC,IAEA,IAAAN,EAAA,SAAAr4B,EAAAzV,EAAAjR,EAAAioC,GACA,GAAAh3B,KAAAyV,KAAAyJ,EAAA8X,UAAA,CACA,OAEA,GAAAsX,EAAA,CACAH,EAAA14B,EAAAzV,EAAA,CACA5O,aAAA,KACAD,WAAA,MACApC,QACAmC,SAAA,WAEE,CACFukB,EAAAzV,GAAAjR,IAIA,IAAAw/C,EAAA,SAAA94B,EAAA9iB,GACA,IAAAgkC,EAAAppC,UAAApE,OAAA,EAAAoE,UAAA,MACA,IAAAxD,EAAAuN,EAAA3E,GACA,GAAAqrC,EAAA,CACAj0C,EAAAgV,EAAAlK,KAAA9K,EAAAK,OAAAgM,sBAAAzD,IAEA,QAAAuD,EAAA,EAAgBA,EAAAnM,EAAAZ,OAAkB+M,GAAA,GAClC43C,EAAAr4B,EAAA1rB,EAAAmM,GAAAvD,EAAA5I,EAAAmM,IAAAygC,EAAA5sC,EAAAmM,OAIAq4C,EAAAD,wBAEAlmD,EAAAC,QAAAkmD,qCCzDA,IAAAC,EAAAzlD,EAAA,YAAA0lD,EAAA1lD,EAAAkO,EAAAu3C,GAA8lB,IAAAnvC,EAAAovC,EAAG,qCCAjmB,IAAAC,EAAA3lD,EAAA,YAAA4lD,EAAA5lD,EAAAkO,EAAAy3C,GAAmjB,IAAArvC,EAAAsvC,EAAG,wBCAtjB,IAAA/D,EAAsB7hD,EAAQ,QAC9B,IAAAkS,EAAsBlS,EAAQ,QAK9B,IAAA6lD,EAAA,CACAC,OAAA,SACAC,OAAA,SACAC,SAAA,WACAC,WAAA,aACAC,UAAA,YACAC,QAAA,UACAC,UAAA,YACAC,SAAA,YAGAhnD,EAAAC,QAAA,SAAAszC,EAAAjwC,GACAA,IAAA8I,WAEA,IAAA66C,EAAAp0C,EAAAvP,GACA,IAAAsM,EAAA,CACA0lB,MAAA,GACAa,MAAA,GACAC,SAAA,IAIG,kBAAApnB,QAAA,SAAAzG,GACH,IAAA0R,EAAAgtC,EAAAC,qBAAA3+C,GAAA,GACA,GAAA0R,EACArK,EAAArH,GAAAi6C,EAAA2E,EAAAltC,MAIA,IAAAmtC,EAAAH,EAAAC,qBAAA,YACA,IAAAE,EACA,UAAAh3C,MAAA,wCACA,IAAAklB,EAAA8xB,EAAAF,qBAAA,QACA,QAAAp5C,EAAA,EAAeA,EAAAwnB,EAAAv0B,OAAgB+M,IAAA,CAC/B,IAAAu5C,EAAA/xB,EAAAxnB,GACA,IAAAlM,EAAAktB,SAAAu4B,EAAA7yC,aAAA,UACA,IAAA+hB,EAAA8wB,EAAA7yC,aAAA,QACA,GAAAtI,MAAAtK,GACA,UAAAwO,MAAA,gDACA,IAAAmmB,EACA,UAAAnmB,MAAA,iDACAR,EAAA0lB,MAAAxG,SAAAltB,EAAA,KAAA20B,EAIG,qBAAAvnB,QAAA,SAAAzG,GACH,IAAA0R,EAAAgtC,EAAAC,qBAAA3+C,GAAA,GACA,IAAA0R,EACA,OACA,IAAAqtC,EAAA/+C,EAAAzH,UAAA,EAAAyH,EAAAxH,OAAA,GACA,IAAAkjB,EAAAhK,EAAAitC,qBAAAI,GACA,QAAAx5C,EAAA,EAAiBA,EAAAmW,EAAAljB,OAAmB+M,IAAA,CACpC,IAAAy5C,EAAAtjC,EAAAnW,GACA8B,EAAArH,GAAA2J,KAAAswC,EAAA2E,EAAAI,QAGA,OAAA33C,GAGA,SAAAu3C,EAAAltC,GACA,IAAAutC,EAAAC,EAAAxtC,GACA,OAAAutC,EAAA9D,OAAA,SAAAgE,EAAAnzC,GACA,IAAAhM,EAAAo/C,EAAApzC,EAAA6tC,UACAsF,EAAAn/C,GAAAgM,EAAAqzC,UACA,OAAAF,GACG,IAGH,SAAAD,EAAAxtC,GAEA,IAAAutC,EAAA,GACA,QAAA15C,EAAA,EAAeA,EAAAmM,EAAAmb,WAAAr0B,OAA6B+M,IAC5C05C,EAAAt1C,KAAA+H,EAAAmb,WAAAtnB,IACA,OAAA05C,EAGA,SAAAG,EAAAvF,GACA,OAAAoE,EAAApE,EAAAhsC,gBAAAgsC", "file": "js/cde249a4.a04ba540.js", "sourcesContent": ["/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n\n// The _isBuffer check is for Safari 5-7 support, because it's missing\n// Object.prototype.constructor. Remove this eventually\nmodule.exports = function (obj) {\n  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer)\n}\n\nfunction isBuffer (obj) {\n  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n\n// For Node v0.10 support. Remove this eventually.\nfunction isSlowBuffer (obj) {\n  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0))\n}\n", "var equal = require('buffer-equal')\nvar HEADER = new Buffer([66, 77, 70, 3])\n\nmodule.exports = function(buf) {\n  if (typeof buf === 'string')\n    return buf.substring(0, 3) === 'BMF'\n  return buf.length > 4 && equal(buf.slice(0, 4), HEADER)\n}", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"container\"},[_c('div',{ref:\"mini\",staticClass:\"mini\"})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n    div.container()\n        div.mini(ref=\"mini\")\n</template>\n<script>\nimport { cape } from '@core/cape'\n\nexport default {\n    props: {\n        id: [String, Number],\n        geo: [String, Object, Array],\n        bgColor: [String],\n        update: [String],\n    },\n\n    methods: {\n        load(id) {\n            let geoQuery = cape.api.geo.componentSet({\n                type: 'componentRelations',\n                id: id, // component id\n\n                queryForMiniature: true,\n            })\n            geoQuery.pipe(\n                geo => {\n                    console.log(geo)\n                    this.build(geo, id, false)\n                },\n                'paletteRelations'\n            )\n        },\n\n        build(geo, id, fetched) {\n            cape.services.miniatures\n                .add({\n                    data: geo,\n                    id,\n                    fetched,\n                    base64: true,\n                    colorSet: 'production',\n                    bgColor: this.bgColor,\n                })\n                .then(async result => {\n                    while (this.$refs.mini && this.$refs.mini.hasChildNodes())\n                        this.$refs.mini.removeChild(this.$refs.mini.firstChild)\n                    var image = new Image()\n                    image.src = result.painterState\n                    this.$refs.mini.appendChild(image)\n                })\n        },\n    },\n\n    created() {},\n\n    watch: {\n        geo() {\n            this.build(this.geo, this.id, true)\n        },\n    },\n\n    mounted() {\n        if (this.geo) {\n            this.build(this.geo, this.id, true)\n            return\n        }\n        if (this.id) {\n            this.load(this.id)\n        }\n    },\n}\n</script>\n<style lang=\"scss\" scoped>\n.mini {\n    width: 100px;\n    height: 100px;\n}\n</style>\n\n", "import mod from \"-!../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoMiniature.vue?vue&type=template&id=360dcb88&scoped=true&lang=pug&\"\nimport script from \"./TylkoMiniature.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoMiniature.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoMiniature.vue?vue&type=style&index=0&id=360dcb88&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"360dcb88\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\n\nvar GetIntrinsic = require('./GetIntrinsic');\n\nvar $Object = GetIntrinsic('%Object%');\nvar $TypeError = GetIntrinsic('%TypeError%');\nvar $String = GetIntrinsic('%String%');\n\nvar assertRecord = require('./helpers/assertRecord');\nvar $isNaN = require('./helpers/isNaN');\nvar $isFinite = require('./helpers/isFinite');\n\nvar sign = require('./helpers/sign');\nvar mod = require('./helpers/mod');\n\nvar IsCallable = require('is-callable');\nvar toPrimitive = require('es-to-primitive/es5');\n\nvar has = require('has');\n\n// https://es5.github.io/#x9\nvar ES5 = {\n\tToPrimitive: toPrimitive,\n\n\tToBoolean: function ToBoolean(value) {\n\t\treturn !!value;\n\t},\n\tToNumber: function ToNumber(value) {\n\t\treturn +value; // eslint-disable-line no-implicit-coercion\n\t},\n\tToInteger: function ToInteger(value) {\n\t\tvar number = this.ToNumber(value);\n\t\tif ($isNaN(number)) { return 0; }\n\t\tif (number === 0 || !$isFinite(number)) { return number; }\n\t\treturn sign(number) * Math.floor(Math.abs(number));\n\t},\n\tToInt32: function ToInt32(x) {\n\t\treturn this.ToNumber(x) >> 0;\n\t},\n\tToUint32: function ToUint32(x) {\n\t\treturn this.ToNumber(x) >>> 0;\n\t},\n\tToUint16: function ToUint16(value) {\n\t\tvar number = this.ToNumber(value);\n\t\tif ($isNaN(number) || number === 0 || !$isFinite(number)) { return 0; }\n\t\tvar posInt = sign(number) * Math.floor(Math.abs(number));\n\t\treturn mod(posInt, 0x10000);\n\t},\n\tToString: function ToString(value) {\n\t\treturn $String(value);\n\t},\n\tToObject: function ToObject(value) {\n\t\tthis.CheckObjectCoercible(value);\n\t\treturn $Object(value);\n\t},\n\tCheckObjectCoercible: function CheckObjectCoercible(value, optMessage) {\n\t\t/* jshint eqnull:true */\n\t\tif (value == null) {\n\t\t\tthrow new $TypeError(optMessage || 'Cannot call method on ' + value);\n\t\t}\n\t\treturn value;\n\t},\n\tIsCallable: IsCallable,\n\tSameValue: function SameValue(x, y) {\n\t\tif (x === y) { // 0 === -0, but they are not identical.\n\t\t\tif (x === 0) { return 1 / x === 1 / y; }\n\t\t\treturn true;\n\t\t}\n\t\treturn $isNaN(x) && $isNaN(y);\n\t},\n\n\t// https://www.ecma-international.org/ecma-262/5.1/#sec-8\n\tType: function Type(x) {\n\t\tif (x === null) {\n\t\t\treturn 'Null';\n\t\t}\n\t\tif (typeof x === 'undefined') {\n\t\t\treturn 'Undefined';\n\t\t}\n\t\tif (typeof x === 'function' || typeof x === 'object') {\n\t\t\treturn 'Object';\n\t\t}\n\t\tif (typeof x === 'number') {\n\t\t\treturn 'Number';\n\t\t}\n\t\tif (typeof x === 'boolean') {\n\t\t\treturn 'Boolean';\n\t\t}\n\t\tif (typeof x === 'string') {\n\t\t\treturn 'String';\n\t\t}\n\t},\n\n\t// https://ecma-international.org/ecma-262/6.0/#sec-property-descriptor-specification-type\n\tIsPropertyDescriptor: function IsPropertyDescriptor(Desc) {\n\t\tif (this.Type(Desc) !== 'Object') {\n\t\t\treturn false;\n\t\t}\n\t\tvar allowed = {\n\t\t\t'[[Configurable]]': true,\n\t\t\t'[[Enumerable]]': true,\n\t\t\t'[[Get]]': true,\n\t\t\t'[[Set]]': true,\n\t\t\t'[[Value]]': true,\n\t\t\t'[[Writable]]': true\n\t\t};\n\n\t\tfor (var key in Desc) { // eslint-disable-line\n\t\t\tif (has(Desc, key) && !allowed[key]) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tvar isData = has(Desc, '[[Value]]');\n\t\tvar IsAccessor = has(Desc, '[[Get]]') || has(Desc, '[[Set]]');\n\t\tif (isData && IsAccessor) {\n\t\t\tthrow new $TypeError('Property Descriptors may not be both accessor and data descriptors');\n\t\t}\n\t\treturn true;\n\t},\n\n\t// https://ecma-international.org/ecma-262/5.1/#sec-8.10.1\n\tIsAccessorDescriptor: function IsAccessorDescriptor(Desc) {\n\t\tif (typeof Desc === 'undefined') {\n\t\t\treturn false;\n\t\t}\n\n\t\tassertRecord(this, 'Property Descriptor', 'Desc', Desc);\n\n\t\tif (!has(Desc, '[[Get]]') && !has(Desc, '[[Set]]')) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t},\n\n\t// https://ecma-international.org/ecma-262/5.1/#sec-8.10.2\n\tIsDataDescriptor: function IsDataDescriptor(Desc) {\n\t\tif (typeof Desc === 'undefined') {\n\t\t\treturn false;\n\t\t}\n\n\t\tassertRecord(this, 'Property Descriptor', 'Desc', Desc);\n\n\t\tif (!has(Desc, '[[Value]]') && !has(Desc, '[[Writable]]')) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t},\n\n\t// https://ecma-international.org/ecma-262/5.1/#sec-8.10.3\n\tIsGenericDescriptor: function IsGenericDescriptor(Desc) {\n\t\tif (typeof Desc === 'undefined') {\n\t\t\treturn false;\n\t\t}\n\n\t\tassertRecord(this, 'Property Descriptor', 'Desc', Desc);\n\n\t\tif (!this.IsAccessorDescriptor(Desc) && !this.IsDataDescriptor(Desc)) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t},\n\n\t// https://ecma-international.org/ecma-262/5.1/#sec-8.10.4\n\tFromPropertyDescriptor: function FromPropertyDescriptor(Desc) {\n\t\tif (typeof Desc === 'undefined') {\n\t\t\treturn Desc;\n\t\t}\n\n\t\tassertRecord(this, 'Property Descriptor', 'Desc', Desc);\n\n\t\tif (this.IsDataDescriptor(Desc)) {\n\t\t\treturn {\n\t\t\t\tvalue: Desc['[[Value]]'],\n\t\t\t\twritable: !!Desc['[[Writable]]'],\n\t\t\t\tenumerable: !!Desc['[[Enumerable]]'],\n\t\t\t\tconfigurable: !!Desc['[[Configurable]]']\n\t\t\t};\n\t\t} else if (this.IsAccessorDescriptor(Desc)) {\n\t\t\treturn {\n\t\t\t\tget: Desc['[[Get]]'],\n\t\t\t\tset: Desc['[[Set]]'],\n\t\t\t\tenumerable: !!Desc['[[Enumerable]]'],\n\t\t\t\tconfigurable: !!Desc['[[Configurable]]']\n\t\t\t};\n\t\t} else {\n\t\t\tthrow new $TypeError('FromPropertyDescriptor must be called with a fully populated Property Descriptor');\n\t\t}\n\t},\n\n\t// https://ecma-international.org/ecma-262/5.1/#sec-8.10.5\n\tToPropertyDescriptor: function ToPropertyDescriptor(Obj) {\n\t\tif (this.Type(Obj) !== 'Object') {\n\t\t\tthrow new $TypeError('ToPropertyDescriptor requires an object');\n\t\t}\n\n\t\tvar desc = {};\n\t\tif (has(Obj, 'enumerable')) {\n\t\t\tdesc['[[Enumerable]]'] = this.ToBoolean(Obj.enumerable);\n\t\t}\n\t\tif (has(Obj, 'configurable')) {\n\t\t\tdesc['[[Configurable]]'] = this.ToBoolean(Obj.configurable);\n\t\t}\n\t\tif (has(Obj, 'value')) {\n\t\t\tdesc['[[Value]]'] = Obj.value;\n\t\t}\n\t\tif (has(Obj, 'writable')) {\n\t\t\tdesc['[[Writable]]'] = this.ToBoolean(Obj.writable);\n\t\t}\n\t\tif (has(Obj, 'get')) {\n\t\t\tvar getter = Obj.get;\n\t\t\tif (typeof getter !== 'undefined' && !this.IsCallable(getter)) {\n\t\t\t\tthrow new TypeError('getter must be a function');\n\t\t\t}\n\t\t\tdesc['[[Get]]'] = getter;\n\t\t}\n\t\tif (has(Obj, 'set')) {\n\t\t\tvar setter = Obj.set;\n\t\t\tif (typeof setter !== 'undefined' && !this.IsCallable(setter)) {\n\t\t\t\tthrow new $TypeError('setter must be a function');\n\t\t\t}\n\t\t\tdesc['[[Set]]'] = setter;\n\t\t}\n\n\t\tif ((has(desc, '[[Get]]') || has(desc, '[[Set]]')) && (has(desc, '[[Value]]') || has(desc, '[[Writable]]'))) {\n\t\t\tthrow new $TypeError('Invalid property descriptor. Cannot both specify accessors and a value or writable attribute');\n\t\t}\n\t\treturn desc;\n\t}\n};\n\nmodule.exports = ES5;\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nvar zeroWidthSpace = '\\u200b';\n\nmodule.exports = function getPolyfill() {\n\tif (String.prototype.trim && zeroWidthSpace.trim() === zeroWidthSpace) {\n\t\treturn String.prototype.trim;\n\t}\n\treturn implementation;\n};\n", "var assign = require('object-assign');\n\nmodule.exports = function createMSDFShader (opt) {\n  opt = opt || {};\n  var opacity = typeof opt.opacity === 'number' ? opt.opacity : 1;\n  var alphaTest = typeof opt.alphaTest === 'number' ? opt.alphaTest : 0.0001;\n  var precision = opt.precision || 'highp';\n  var color = opt.color;\n  var map = opt.map;\n  var negate = typeof opt.negate === 'boolean' ? opt.negate : true;\n\n  // remove to satisfy r73\n  delete opt.map;\n  delete opt.color;\n  delete opt.precision;\n  delete opt.opacity;\n  delete opt.negate;\n\n  return assign({\n    uniforms: {\n      opacity: { type: 'f', value: opacity },\n      map: { type: 't', value: map || new THREE.Texture() },\n      color: { type: 'c', value: new THREE.Color(color) }\n    },\n    vertexShader: [\n      'attribute vec2 uv;',\n      'attribute vec4 position;',\n      'uniform mat4 projectionMatrix;',\n      'uniform mat4 modelViewMatrix;',\n      'varying vec2 vUv;',\n      'void main() {',\n      'vUv = uv;',\n      'gl_Position = projectionMatrix * modelViewMatrix * position;',\n      '}'\n    ].join('\\n'),\n    fragmentShader: [\n      '#ifdef GL_OES_standard_derivatives',\n      '#extension GL_OES_standard_derivatives : enable',\n      '#endif',\n      'precision ' + precision + ' float;',\n      'uniform float opacity;',\n      'uniform vec3 color;',\n      'uniform sampler2D map;',\n      'varying vec2 vUv;',\n\n      'float median(float r, float g, float b) {',\n      '  return max(min(r, g), min(max(r, g), b));',\n      '}',\n\n      'void main() {',\n      '  vec3 sample = ' + (negate ? '1.0 - ' : '') + 'texture2D(map, vUv).rgb;',\n      '  float sigDist = median(sample.r, sample.g, sample.b) - 0.5;',\n      '  float alpha = clamp(sigDist/fwidth(sigDist) + 0.5, 0.0, 1.0);',\n      '  gl_FragColor = vec4(color.xyz, alpha * opacity);',\n      alphaTest === 0\n        ? ''\n        : '  if (gl_FragColor.a < ' + alphaTest + ') discard;',\n      '}'\n    ].join('\\n')\n  }, opt);\n};\n", "\nconst verts = [\n  [-1.0, -1.0],\n  [+1.0, -1.0],\n  [-1.0, +1.0],\n  [-1.0, +1.0],\n  [+1.0, -1.0],\n  [+1.0, +1.0]\n];\n\nconst uvs = [\n  [0.0, 0.0],\n  [1.0, 0.0],\n  [0.0, 1.0],\n  [0.0, 1.0],\n  [1.0, 0.0],\n  [1.0, 1.0]\n];\n\nconst indices = [\n  [0, 1, 2],\n  [3, 4, 5]\n];\n\nconst vshader = `\n  precision mediump float;\n  attribute vec2 a_position;\n  attribute vec2 a_uv;\n\n  uniform float u_clip_y;\n\n  varying vec2 v_uv;\n  \n  void main() {\n    v_uv = a_uv;\n    gl_Position = vec4(a_position * vec2(1,u_clip_y), 0, 1);\n  }\n`;\n\nconst fshader = `\n  precision mediump float;\n  varying vec2 v_uv;\n  uniform sampler2D u_tex;\n  void main () {\n    gl_FragColor = texture2D(u_tex,v_uv);\n  }\n`;\n\nconst showUVsFshader = `\n  precision mediump float;\n  varying vec2 v_uv;\n  void main () {\n    gl_FragColor = vec4(v_uv,0,1);\n  }\n`;\n\n\nconst showPositionsVshader = `\n  precision mediump float;\n  attribute vec2 a_position;\n\n  uniform float u_clip_y;\n\n  varying vec2 v_uv;\n  \n  void main() {\n    gl_Position = vec4(a_position * vec2(1,u_clip_y), 0, 1);\n    v_uv = gl_Position.xy;\n  }\n`;\n\nconst showPositionsFshader = `\n  precision mediump float;\n  varying vec2 v_uv;\n  void main () {\n    gl_FragColor = vec4(v_uv,0,1);\n  }\n`;\n\nconst directionsDataUri = `\ndata:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAA\nBACAIAAAAlC+aJAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQ\nUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAEbSURBVGhD7dhRDsIgEI\nRhjubNPHqlHUTAdjfRWRKa+UIirQnd376Z0vZZG1vQsfvB76WAa3\nEn3yug3GHD0HX6gIZCAaYaEGdSQM2g9yjApADfpIBhTzQvIIgCTA\nrwKcCkAJ8CTArwKcCkAN/56Y/8XAZCwH7AsS6sEDBseisEYF1YIW\nDY9Lq7eW6Mjk29/Bk/YD+vO7Bc/D/rKULAqSbj80tHrOehPC9mjY\n/krhkBeBF4HvZE6CgXRJgeW3wAPYMf0IwO1NO/RL2BhgJMCvApwK\nQAnwJMCvApwKQAnwJMCvApwNQGYE/vmRowbCgUYLpbQHvJMi8gSN\nTpmLsGxGWsH9Aq90gwfW1gwv9zx+qUr0mWD8hCps/uE5DSC/pgVD\nkvIARVAAAAAElFTkSuQmCC`.replace(/\\s*/g, '');\n\nconst bitmaps = {\n  directions: {uri: directionsDataUri}\n};\n\nmodule.exports = {verts, indices, uvs, shader: {vert: vshader, frag: fshader},\n                  show: {\n                    uvs: {frag: showUVsFshader, vert: vshader},\n                    positions: {frag: showPositionsFshader, vert: showPositionsVshader}\n                  },\n                  bitmaps};\n", "module.exports = Number.isNaN || function isNaN(a) {\n\treturn a !== a;\n};\n", "'use strict';\n\nvar fnToStr = Function.prototype.toString;\n\nvar constructorRegex = /^\\s*class\\b/;\nvar isES6ClassFn = function isES6ClassFunction(value) {\n\ttry {\n\t\tvar fnStr = fnToStr.call(value);\n\t\treturn constructorRegex.test(fnStr);\n\t} catch (e) {\n\t\treturn false; // not a function\n\t}\n};\n\nvar tryFunctionObject = function tryFunctionToStr(value) {\n\ttry {\n\t\tif (isES6ClassFn(value)) { return false; }\n\t\tfnToStr.call(value);\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\nvar toStr = Object.prototype.toString;\nvar fnClass = '[object Function]';\nvar genClass = '[object GeneratorFunction]';\nvar hasToStringTag = typeof Symbol === 'function' && typeof Symbol.toStringTag === 'symbol';\n\nmodule.exports = function isCallable(value) {\n\tif (!value) { return false; }\n\tif (typeof value !== 'function' && typeof value !== 'object') { return false; }\n\tif (typeof value === 'function' && !value.prototype) { return true; }\n\tif (hasToStringTag) { return tryFunctionObject(value); }\n\tif (isES6ClassFn(value)) { return false; }\n\tvar strClass = toStr.call(value);\n\treturn strClass === fnClass || strClass === genClass;\n};\n", "'use strict';\n// B.2.3.8 String.prototype.fontsize(size)\nrequire('./_string-html')('fontsize', function (createHTML) {\n  return function fontsize(size) {\n    return createHTML(this, 'font', 'size', size);\n  };\n});\n", "'use strict';\n\nvar toStr = Object.prototype.toString;\n\nvar isPrimitive = require('./helpers/isPrimitive');\n\nvar isCallable = require('is-callable');\n\n// http://ecma-international.org/ecma-262/5.1/#sec-8.12.8\nvar ES5internalSlots = {\n\t'[[DefaultValue]]': function (O) {\n\t\tvar actualHint;\n\t\tif (arguments.length > 1) {\n\t\t\tactualHint = arguments[1];\n\t\t} else {\n\t\t\tactualHint = toStr.call(O) === '[object Date]' ? String : Number;\n\t\t}\n\n\t\tif (actualHint === String || actualHint === Number) {\n\t\t\tvar methods = actualHint === String ? ['toString', 'valueOf'] : ['valueOf', 'toString'];\n\t\t\tvar value, i;\n\t\t\tfor (i = 0; i < methods.length; ++i) {\n\t\t\t\tif (isCallable(O[methods[i]])) {\n\t\t\t\t\tvalue = O[methods[i]]();\n\t\t\t\t\tif (isPrimitive(value)) {\n\t\t\t\t\t\treturn value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tthrow new TypeError('No default value');\n\t\t}\n\t\tthrow new TypeError('invalid [[DefaultValue]] hint supplied');\n\t}\n};\n\n// http://ecma-international.org/ecma-262/5.1/#sec-9.1\nmodule.exports = function ToPrimitive(input) {\n\tif (isPrimitive(input)) {\n\t\treturn input;\n\t}\n\tif (arguments.length > 1) {\n\t\treturn ES5internalSlots['[[DefaultValue]]'](input, arguments[1]);\n\t}\n\treturn ES5internalSlots['[[DefaultValue]]'](input);\n};\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/*eslint new-cap:0*/\nvar dtype = require('dtype')\n\nmodule.exports = flattenVertexData\n\nfunction flattenVertexData (data, output, offset) {\n  if (!data) throw new TypeError('must specify data as first parameter')\n  offset = +(offset || 0) | 0\n\n  if (Array.isArray(data) && (data[0] && typeof data[0][0] === 'number')) {\n    var dim = data[0].length\n    var length = data.length * dim\n    var i, j, k, l\n\n    // no output specified, create a new typed array\n    if (!output || typeof output === 'string') {\n      output = new (dtype(output || 'float32'))(length + offset)\n    }\n\n    var dstLength = output.length - offset\n    if (length !== dstLength) {\n      throw new Error('source length ' + length + ' (' + dim + 'x' + data.length + ')' +\n        ' does not match destination length ' + dstLength)\n    }\n\n    for (i = 0, k = offset; i < data.length; i++) {\n      for (j = 0; j < dim; j++) {\n        output[k++] = data[i][j] === null ? NaN : data[i][j]\n      }\n    }\n  } else {\n    if (!output || typeof output === 'string') {\n      // no output, create a new one\n      var Ctor = dtype(output || 'float32')\n\n      // handle arrays separately due to possible nulls\n      if (Array.isArray(data) || output === 'array') {\n        output = new Ctor(data.length + offset)\n        for (i = 0, k = offset, l = output.length; k < l; k++, i++) {\n          output[k] = data[i] === null ? NaN : data[i]\n        }\n      } else {\n        if (offset === 0) {\n          output = new Ctor(data)\n        } else {\n          output = new Ctor(data.length + offset)\n\n          output.set(data, offset)\n        }\n      }\n    } else {\n      // store output in existing array\n      output.set(data, offset)\n    }\n  }\n\n  return output\n}\n", "var newline = /\\n/\nvar newlineChar = '\\n'\nvar whitespace = /\\s/\n\nmodule.exports = function(text, opt) {\n    var lines = module.exports.lines(text, opt)\n    return lines.map(function(line) {\n        return text.substring(line.start, line.end)\n    }).join('\\n')\n}\n\nmodule.exports.lines = function wordwrap(text, opt) {\n    opt = opt||{}\n\n    //zero width results in nothing visible\n    if (opt.width === 0 && opt.mode !== 'nowrap') \n        return []\n\n    text = text||''\n    var width = typeof opt.width === 'number' ? opt.width : Number.MAX_VALUE\n    var start = Math.max(0, opt.start||0)\n    var end = typeof opt.end === 'number' ? opt.end : text.length\n    var mode = opt.mode\n\n    var measure = opt.measure || monospace\n    if (mode === 'pre')\n        return pre(measure, text, start, end, width)\n    else\n        return greedy(measure, text, start, end, width, mode)\n}\n\nfunction idxOf(text, chr, start, end) {\n    var idx = text.indexOf(chr, start)\n    if (idx === -1 || idx > end)\n        return end\n    return idx\n}\n\nfunction isWhitespace(chr) {\n    return whitespace.test(chr)\n}\n\nfunction pre(measure, text, start, end, width) {\n    var lines = []\n    var lineStart = start\n    for (var i=start; i<end && i<text.length; i++) {\n        var chr = text.charAt(i)\n        var isNewline = newline.test(chr)\n\n        //If we've reached a newline, then step down a line\n        //Or if we've reached the EOF\n        if (isNewline || i===end-1) {\n            var lineEnd = isNewline ? i : i+1\n            var measured = measure(text, lineStart, lineEnd, width)\n            lines.push(measured)\n            \n            lineStart = i+1\n        }\n    }\n    return lines\n}\n\nfunction greedy(measure, text, start, end, width, mode) {\n    //A greedy word wrapper based on LibGDX algorithm\n    //https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/BitmapFontCache.java\n    var lines = []\n\n    var testWidth = width\n    //if 'nowrap' is specified, we only wrap on newline chars\n    if (mode === 'nowrap')\n        testWidth = Number.MAX_VALUE\n\n    while (start < end && start < text.length) {\n        //get next newline position\n        var newLine = idxOf(text, newlineChar, start, end)\n\n        //eat whitespace at start of line\n        while (start < newLine) {\n            if (!isWhitespace( text.charAt(start) ))\n                break\n            start++\n        }\n\n        //determine visible # of glyphs for the available width\n        var measured = measure(text, start, newLine, testWidth)\n\n        var lineEnd = start + (measured.end-measured.start)\n        var nextStart = lineEnd + newlineChar.length\n\n        //if we had to cut the line before the next newline...\n        if (lineEnd < newLine) {\n            //find char to break on\n            while (lineEnd > start) {\n                if (isWhitespace(text.charAt(lineEnd)))\n                    break\n                lineEnd--\n            }\n            if (lineEnd === start) {\n                if (nextStart > start + newlineChar.length) nextStart--\n                lineEnd = nextStart // If no characters to break, show all.\n            } else {\n                nextStart = lineEnd\n                //eat whitespace at end of line\n                while (lineEnd > start) {\n                    if (!isWhitespace(text.charAt(lineEnd - newlineChar.length)))\n                        break\n                    lineEnd--\n                }\n            }\n        }\n        if (lineEnd >= start) {\n            var result = measure(text, start, lineEnd, testWidth)\n            lines.push(result)\n        }\n        start = nextStart\n    }\n    return lines\n}\n\n//determines the visible number of glyphs within a given width\nfunction monospace(text, start, end, width) {\n    var glyphs = Math.min(width, end-start)\n    return {\n        start: start,\n        end: start+glyphs\n    }\n}", "module.exports = (function xmlparser() {\n  //common browsers\n  if (typeof self.DOMParser !== 'undefined') {\n    return function(str) {\n      var parser = new self.DOMParser()\n      return parser.parseFromString(str, 'application/xml')\n    }\n  } \n\n  //IE8 fallback\n  if (typeof self.ActiveXObject !== 'undefined'\n      && new self.ActiveXObject('Microsoft.XMLDOM')) {\n    return function(str) {\n      var xmlDoc = new self.ActiveXObject(\"Microsoft.XMLDOM\")\n      xmlDoc.async = \"false\"\n      xmlDoc.loadXML(str)\n      return xmlDoc\n    }\n  }\n\n  //last resort fallback\n  return function(str) {\n    var div = document.createElement('div')\n    div.innerHTML = str\n    return div\n  }\n})()\n", "module.exports = function isPrimitive(value) {\n\treturn value === null || (typeof value !== 'function' && typeof value !== 'object');\n};\n", "'use strict';\n\nvar bind = require('function-bind');\nvar ES = require('es-abstract/es5');\nvar replace = bind.call(Function.call, String.prototype.replace);\n\nvar leftWhitespace = /^[\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u180E\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF]+/;\nvar rightWhitespace = /[\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u180E\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF]+$/;\n\nmodule.exports = function trim() {\n\tvar S = ES.ToString(ES.CheckObjectCoercible(this));\n\treturn replace(replace(S, leftWhitespace, ''), rightWhitespace, '');\n};\n", "var str = Object.prototype.toString\n\nmodule.exports = anArray\n\nfunction anArray(arr) {\n  return (\n       arr.BYTES_PER_ELEMENT\n    && str.call(arr.buffer) === '[object ArrayBuffer]'\n    || Array.isArray(arr)\n  )\n}\n", "module.exports = function sign(number) {\n\treturn number >= 0 ? 1 : -1;\n};\n", "var flatten = require('flatten-vertex-data')\nvar warned = false;\n\nmodule.exports.attr = setAttribute\nmodule.exports.index = setIndex\n\nfunction setIndex (geometry, data, itemSize, dtype) {\n  if (typeof itemSize !== 'number') itemSize = 1\n  if (typeof dtype !== 'string') dtype = 'uint16'\n\n  var isR69 = !geometry.index && typeof geometry.setIndex !== 'function'\n  var attrib = isR69 ? geometry.getAttribute('index') : geometry.index\n  var newAttrib = updateAttribute(attrib, data, itemSize, dtype)\n  if (newAttrib) {\n    if (isR69) geometry.addAttribute('index', newAttrib)\n    else geometry.index = newAttrib\n  }\n}\n\nfunction setAttribute (geometry, key, data, itemSize, dtype) {\n  if (typeof itemSize !== 'number') itemSize = 3\n  if (typeof dtype !== 'string') dtype = 'float32'\n  if (Array.isArray(data) &&\n    Array.isArray(data[0]) &&\n    data[0].length !== itemSize) {\n    throw new Error('Nested vertex array has unexpected size; expected ' +\n      itemSize + ' but found ' + data[0].length)\n  }\n\n  var attrib = geometry.getAttribute(key)\n  var newAttrib = updateAttribute(attrib, data, itemSize, dtype)\n  if (newAttrib) {\n    geometry.addAttribute(key, newAttrib)\n  }\n}\n\nfunction updateAttribute (attrib, data, itemSize, dtype) {\n  data = data || []\n  if (!attrib || rebuildAttribute(attrib, data, itemSize)) {\n    // create a new array with desired type\n    data = flatten(data, dtype)\n\n    var needsNewBuffer = attrib && typeof attrib.setArray !== 'function'\n    if (!attrib || needsNewBuffer) {\n      // We are on an old version of ThreeJS which can't\n      // support growing / shrinking buffers, so we need\n      // to build a new buffer\n      if (needsNewBuffer && !warned) {\n        warned = true\n        console.warn([\n          'A WebGL buffer is being updated with a new size or itemSize, ',\n          'however this version of ThreeJS only supports fixed-size buffers.',\n          '\\nThe old buffer may still be kept in memory.\\n',\n          'To avoid memory leaks, it is recommended that you dispose ',\n          'your geometries and create new ones, or update to ThreeJS r82 or newer.\\n',\n          'See here for discussion:\\n',\n          'https://github.com/mrdoob/three.js/pull/9631'\n        ].join(''))\n      }\n\n      // Build a new attribute\n      attrib = new THREE.BufferAttribute(data, itemSize);\n    }\n\n    attrib.itemSize = itemSize\n    attrib.needsUpdate = true\n\n    // New versions of ThreeJS suggest using setArray\n    // to change the data. It will use bufferData internally,\n    // so you can change the array size without any issues\n    if (typeof attrib.setArray === 'function') {\n      attrib.setArray(data)\n    }\n\n    return attrib\n  } else {\n    // copy data into the existing array\n    flatten(data, attrib.array)\n    attrib.needsUpdate = true\n    return null\n  }\n}\n\n// Test whether the attribute needs to be re-created,\n// returns false if we can re-use it as-is.\nfunction rebuildAttribute (attrib, data, itemSize) {\n  if (attrib.itemSize !== itemSize) return true\n  if (!attrib.array) return true\n  var attribLength = attrib.array.length\n  if (Array.isArray(data) && Array.isArray(data[0])) {\n    // [ [ x, y, z ] ]\n    return attribLength !== data.length * itemSize\n  } else {\n    // [ x, y, z ]\n    return attribLength !== data.length\n  }\n  return false\n}\n", "var itemSize = 2\nvar box = { min: [0, 0], max: [0, 0] }\n\nfunction bounds (positions) {\n  var count = positions.length / itemSize\n  box.min[0] = positions[0]\n  box.min[1] = positions[1]\n  box.max[0] = positions[0]\n  box.max[1] = positions[1]\n\n  for (var i = 0; i < count; i++) {\n    var x = positions[i * itemSize + 0]\n    var y = positions[i * itemSize + 1]\n    box.min[0] = Math.min(x, box.min[0])\n    box.min[1] = Math.min(y, box.min[1])\n    box.max[0] = Math.max(x, box.max[0])\n    box.max[1] = Math.max(y, box.max[1])\n  }\n}\n\nmodule.exports.computeBox = function (positions, output) {\n  bounds(positions)\n  output.min.set(box.min[0], box.min[1], 0)\n  output.max.set(box.max[0], box.max[1], 0)\n}\n\nmodule.exports.computeSphere = function (positions, output) {\n  bounds(positions)\n  var minX = box.min[0]\n  var minY = box.min[1]\n  var maxX = box.max[0]\n  var maxY = box.max[1]\n  var width = maxX - minX\n  var height = maxY - minY\n  var length = Math.sqrt(width * width + height * height)\n  output.center.set(minX + width / 2, minY + height / 2, 0)\n  output.radius = length / 2\n}\n", "var trim = require('string.prototype.trim')\n  , forEach = require('for-each')\n  , isArray = function(arg) {\n      return Object.prototype.toString.call(arg) === '[object Array]';\n    }\n\nmodule.exports = function (headers) {\n  if (!headers)\n    return {}\n\n  var result = {}\n\n  forEach(\n      trim(headers).split('\\n')\n    , function (row) {\n        var index = row.indexOf(':')\n          , key = trim(row.slice(0, index)).toLowerCase()\n          , value = trim(row.slice(index + 1))\n\n        if (typeof(result[key]) === 'undefined') {\n          result[key] = value\n        } else if (isArray(result[key])) {\n          result[key].push(value)\n        } else {\n          result[key] = [ result[key], value ]\n        }\n      }\n  )\n\n  return result\n}\n", "'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar slice = Array.prototype.slice;\nvar toStr = Object.prototype.toString;\nvar funcType = '[object Function]';\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.call(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slice.call(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                args.concat(slice.call(arguments))\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        } else {\n            return target.apply(\n                that,\n                args.concat(slice.call(arguments))\n            );\n        }\n    };\n\n    var boundLength = Math.max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs.push('$' + i);\n    }\n\n    bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n", "import mod from \"-!../../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoUsps.vue?vue&type=style&index=0&id=96c8332e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoUsps.vue?vue&type=style&index=0&id=96c8332e&lang=scss&scoped=true&\"", "module.exports = function(dtype) {\n  switch (dtype) {\n    case 'int8':\n      return Int8Array\n    case 'int16':\n      return Int16Array\n    case 'int32':\n      return Int32Array\n    case 'uint8':\n      return Uint8Array\n    case 'uint16':\n      return Uint16Array\n    case 'uint32':\n      return Uint32Array\n    case 'float32':\n      return Float32Array\n    case 'float64':\n      return Float64Array\n    case 'array':\n      return Array\n    case 'uint8_clamped':\n      return Uint8ClampedArray\n  }\n}\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{ref:\"rendererContainer\",staticClass:\"flex row preview-wrapper\"},[(this.$route.name === 'configurator')?_c('TylkoUsps',{attrs:{\"isVisible\":_vm.visibleUspsText}}):_vm._e(),(this.$route.name === 'configurator')?_c('TylkoMenu'):_vm._e(),_c('div',{staticClass:\"renderer-wrapper\",on:{\"mouseover\":_vm.rendererMouseOver,\"mouseleave\":_vm.rendererMouseLeave}},[(!_vm.loaderHidden)?_c('q-spinner',{staticClass:\"spinner\",attrs:{\"color\":\"blue\",\"size\":\"50px\"}}):_vm._e(),_c('div',{ref:\"rendererWapper\",staticClass:\"production-renderer-canvas load-hidden\"}),_c('div',{staticClass:\"mapped\"},_vm._l((_vm.mappedObjects),function(position,no){return _c('div',{staticClass:\"mapped-container\"},[_c('div',{staticClass:\"cont\",style:((\"position: absolute; top: \" + (position.y) + \"px; left: \" + (position.x) + \"px;}\"))},[_c('div',{class:[no+1 == _vm.$refs.configuration.$refs.previewSelect.selectedTab ? 'comp-selected' : '' ],staticStyle:{\"z-index\":\"99\"}},[_c('q-btn',{class:[ _vm.hoverElement == no ? 'hoverState' : '', 'tylko-button' ],style:(_vm.$refs.configuration.$refs.previewSelect.selectedTab != 0 && no+1 != _vm.$refs.configuration.$refs.previewSelect.selectedTab ? 'opacity: 0' : ''),attrs:{\"round\":\"round\",\"dense\":\"dense\",\"color\":\"white\",\"size\":\"md\",\"icon\":\"tune\"},nativeOn:{\"mouseleave\":function($event){return _vm.selectOnLeave($event)},\"mouseover\":function($event){return _vm.selectOnEnter(no)},\"mousedown\":function($event){return _vm.select(no)}}},[_c('span',{staticClass:\"adjust\"},[_vm._v(_vm._s(no+1 == _vm.$refs.configuration.$refs.previewSelect.selectedTab || _vm.hoverElement == no ? 'Adjust' : ''))])])],1)]),_vm._l((_vm.compartmentsHeights),function(comp){return _c('div',{staticClass:\"compartments-heights-container\"},[_c('div',{staticClass:\"compartment compartment-height\",style:((\"position: absolute; transform: translate(\" + (comp.point.x) + \"px, \" + (comp.point.y) + \"px)\"))},[_vm._v(_vm._s(Math.floor(comp.value/10)))])])}),_vm._l((_vm.compartmentsWidths),function(comp){return _c('div',{staticClass:\"compartments-widths-container\"},[_c('div',{staticClass:\"compartment compartment-width\",style:((\"position: absolute; transform: translate(\" + (comp.point.x) + \"px, \" + (comp.point.y) + \"px)\"))},[_vm._v(_vm._s(Math.floor(comp.value/10)))])])})],2)}),0)],1),_c('div',{staticStyle:{\"position\":\"relative\",\"height\":\"100vh\",\"width\":\"30%\"}},[_c('TylkoPreviewControls',{attrs:{\"showAllCompartmentsButton\":_vm.showAllCompartmentsButton}}),_c('TylkoPreviewConfiguration',{ref:\"configuration\",attrs:{\"type\":_vm.decoderType,\"element\":_vm.element,\"grabButtons\":_vm.grabButtons,\"objectLine\":_vm.objectLine,\"choosenColor\":_vm.choosenColor,\"decoderWidth\":_vm.decoderWidth,\"decoderHeight\":_vm.decoderHeight,\"decoderOutput\":_vm.decoderOutput,\"distortion\":_vm.distortion,\"density\":_vm.density,\"depth\":_vm.depth,\"plinth\":_vm.plinth,\"mappedObjects\":_vm.mappedObjects,\"price\":_vm.price,\"locked\":_vm.locked,\"previousStateAvailble\":_vm.previousStateAvailble,\"nextStateAvailble\":_vm.nextStateAvailble,\"parametersStatesStatus\":_vm.parametersStatesStatus}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('q-scroll-area',{staticClass:\"configuration-wrapper\",class:{ 'badania' : _vm.isConfigurator },staticStyle:{}},[(_vm.isConfigurator)?_c('span',{staticClass:\"title\"},[_vm._v(\"Type01 Sideboard\")]):_vm._e(),_c('q-card',{staticClass:\"no-shadow bradius\"},[_c('q-card-title',{staticClass:\"card-title\"},[(!_vm.isConfigurator)?_c('span',[_vm._v(_vm._s(_vm.type)+\" - \"+_vm._s(_vm.element ? _vm.element.name : '')+\" | \"+_vm._s(_vm.objectLine))]):_vm._e(),_c('span',{staticClass:\"text-weight-bold\",staticStyle:{\"color\":\"black\",\"font-size\":\"24px\",\"line-height\":\"24px\"}},[_vm._v(_vm._s(_vm.price)+\"€\")]),(!_vm.isConfigurator)?_c('q-btn-group',[(_vm.$route.params.type === 'mesh' && _vm.isConfigurator)?_c('q-btn',{staticClass:\"q-pl-md\",attrs:{\"label\":\"RESET\",\"size\":\"sm\"},on:{\"click\":_vm.resetToInitialState}}):_vm._e(),(_vm.$route.params.type === 'mesh' && !_vm.isConfigurator)?_c('q-btn',{staticClass:\"q-pl-md\",attrs:{\"label\":\"SAVE STATE\",\"color\":\"positive\",\"size\":\"sm\"},on:{\"click\":_vm.saveState}}):_vm._e(),(_vm.$route.params.type === 'mesh')?_c('q-btn',{staticClass:\"q-pl-md arrow-btn\",attrs:{\"label\":this.isConfigurator ? '' : 'PREVIOUS STATE',\"size\":\"md\",\"disable\":!this.previousStateAvailble,\"icon\":this.isConfigurator ? 'undo' : ''},on:{\"click\":_vm.undo}}):_vm._e(),(_vm.$route.params.type === 'mesh')?_c('q-btn',{staticClass:\"q-pl-md arrow-btn\",attrs:{\"label\":this.isConfigurator ? '' : 'NEXT STATE',\"size\":\"md\",\"disable\":!this.nextStateAvailble,\"icon\":this.isConfigurator ? 'redo' : ''},on:{\"click\":_vm.redo}}):_vm._e()],1):_vm._e(),(_vm.$route.params.type === 'mesh' && !_vm.isConfigurator)?_c('div',{staticClass:\"row\"},[_c('q-slider',{attrs:{\"markers\":\"markers\",\"snap\":\"snap\",\"step\":1,\"min\":0,\"max\":this.parametersStatesStatus[1]},on:{\"input\":function (val) { return _vm.setState(val); }},model:{value:(_vm.currentParametersHistoryState),callback:function ($$v) {_vm.currentParametersHistoryState=$$v},expression:\"currentParametersHistoryState\"}})],1):_vm._e()],1),_c('div',{staticClass:\"separator\"}),(!_vm.isConfigurator)?_c('q-card-main',{staticClass:\"row justify-between\"},[_c('q-toggle',{staticClass:\"col-8\",attrs:{\"label\":\"Camera lock\"},on:{\"input\":function (val) { return _vm.updateParam('locked', val); }},model:{value:(_vm.choosenLocked),callback:function ($$v) {_vm.choosenLocked=$$v},expression:\"choosenLocked\"}}),(!_vm.isConfigurator && _vm.$route.params.type === 'mesh' && _vm.element)?_c('router-link',{attrs:{\"tag\":\"a\",\"to\":(\"/meshes/\" + (_vm.element.collection) + \"/\" + (_vm.element.id))}},[_c('q-btn',{staticClass:\"link-button q-pl-md\",attrs:{\"rounded\":\"rounded\",\"icon-right\":\"settings\",\"size\":\"sm\",\"label\":\"EDIT\"}})],1):_vm._e(),(!_vm.isConfigurator && _vm.$route.params.type === 'component' && _vm.element)?_c('router-link',{attrs:{\"tag\":\"a\",\"to\":(\"/components/\" + (_vm.element.collection) + \"/\" + (_vm.element.parent) + \"/\" + (_vm.element.id))}},[_c('q-btn',{attrs:{\"rounded\":\"rounded\",\"icon-right\":\"settings\",\"size\":\"sm\",\"label\":\"EDIT\",\"color\":\"blue\"}})],1):_vm._e()],1):_vm._e(),_c('q-card-main',{staticClass:\"settings-wrapper\"},[_c('div',{staticClass:\"row items-center\"},[_c('q-field',{staticClass:\"col-md-2\",attrs:{\"label\":\"Color\"}}),_c('TylkoPreviewColors',{staticClass:\"col-md-10 justify-end\",attrs:{\"objectLine\":_vm.objectLine,\"choosenColor\":_vm.choosenColor}})],1),_c('div',{staticClass:\"row items-center\"},[(_vm.widthRange.length)?_c('q-field',{staticClass:\"col-md-2\",attrs:{\"label\":\"Width\"}}):_vm._e(),_c('q-slider',{staticClass:\"col-md-10\",attrs:{\"min\":_vm.widthRange[0],\"max\":_vm.widthRange[1],\"label\":\"label\",\"label-always\":true,\"label-value\":+ _vm.choosenWidth +' cm'},on:{\"input\":function (val) { return _vm.updateConfigurator('decoderWidth', val, true); }},model:{value:(_vm.choosenWidth),callback:function ($$v) {_vm.choosenWidth=$$v},expression:\"choosenWidth\"}})],1),_c('div',{staticClass:\"row items-center\"},[(_vm.heightRange.length && _vm.type === 'mesh')?_c('q-field',{staticClass:\"col-md-2\",attrs:{\"label\":\"Height\"}}):_vm._e(),_c('q-slider',{staticClass:\"col-md-10\",attrs:{\"min\":0,\"max\":_vm.heightRange.length - 1,\"step\":1,\"label\":\"label\",\"markers\":\"markers\",\"label-always\":true,\"label-value\":Math.round((+_vm.choosenHeight + _vm.additional_height[0] + _vm.additional_height[1])/10) +' cm'},on:{\"input\":function (val) {_vm.computeHeight(val); _vm.lazyAutoSaveState(); _vm.showRendererElements()}},model:{value:(_vm.heightIndex),callback:function ($$v) {_vm.heightIndex=$$v},expression:\"heightIndex\"}})],1),(_vm.type === 'mesh' && ['gradient', 'ratio'].includes(_vm.distortionMode))?_c('div',{staticClass:\"row items-center\"},[_c('q-field',{staticClass:\"col-md-2\",attrs:{\"label\":\"Motion\"}}),_c('q-slider',{staticClass:\"col-md-10\",attrs:{\"min\":0,\"max\":100,\"label-always\":true,\"label-value\":_vm.choosenDistortion +' %'},on:{\"input\":function (val) { return _vm.updateConfigurator('distortion', val, true); }},model:{value:(_vm.choosenDistortion),callback:function ($$v) {_vm.choosenDistortion=$$v},expression:\"choosenDistortion\"}})],1):_vm._e(),(_vm.densityMode === 'setup_range_slider')?_c('div',{staticClass:\"row items-center\"},[_c('q-field',{staticClass:\"col-md-2\",attrs:{\"label\":\"Density\"}}),_c('q-slider',{staticClass:\"col-md-10\",attrs:{\"min\":0,\"max\":100,\"label-always\":true,\"label-value\":_vm.choosenDensity +' %'},on:{\"input\":function (val) { return _vm.updateConfigurator('density', val, true); }},model:{value:(_vm.choosenDensity),callback:function ($$v) {_vm.choosenDensity=$$v},expression:\"choosenDensity\"}})],1):_vm._e(),(_vm.densityMode === 'setup_range_stepper' && _vm.densityOptions.length !== 0)?_c('q-field',{staticClass:\"col-md-10\",attrs:{\"label\":\"Columns\"}},[_c('div',{staticClass:\"row items-center\",staticStyle:{\"padding-top\":\"14px\"}},[_c('q-btn',{staticClass:\"button-badania small\",class:{ 'disable': _vm.densityOptions[0][0] === _vm.choosenDensity },attrs:{\"label\":\"–\"},on:{\"click\":function (val) { return _vm.updateConfigurator('density', _vm.densityOptions[ _vm.densityOptions.findIndex(function (e) { return e[0] === _vm.choosenDensity; }) - 1 ][0], false); }}}),_vm._v(_vm._s(_vm.densityOptions.find(function (e) { return e[0] === _vm.choosenDensity; })[1])+\"    \"),_c('q-btn',{staticClass:\"button-badania small\",class:{ 'disable': _vm.densityOptions[_vm.densityOptions.length - 1][0] === _vm.choosenDensity },attrs:{\"label\":\"+\"},on:{\"click\":function (val) { return _vm.updateConfigurator('density', _vm.densityOptions[ _vm.densityOptions.findIndex(function (e) { return e[0] === _vm.choosenDensity; }) + 1 ][0], false); }}})],1)]):_vm._e(),_c('div',{staticClass:\"row items-center\"},[_c('q-field',{staticClass:\"component-select col-md-10\",staticStyle:{\"float\":\"left\"},attrs:{\"label\":\"Depth\"}},[_c('q-btn',{staticClass:\"button-badania\",class:{ 'active': _vm.choosenDepth==320 },attrs:{\"label\":\"32cm\",\"no-ripple\":\"no-ripple\"},on:{\"click\":function($event){return _vm.updateConfigurator('depth', 320, false)}}}),_c('q-btn',{staticClass:\"button-badania\",class:{ 'active': _vm.choosenDepth==400 },attrs:{\"label\":\"40cm\",\"no-ripple\":\"no-ripple\"},on:{\"click\":function($event){return _vm.updateConfigurator('depth', 400, false)}}})],1)],1),_c('div',{staticClass:\"row items-center\"},[_c('q-field',{staticClass:\"component-select col-md-10\",attrs:{\"label\":\"Base\"}},[_c('q-btn',{staticClass:\"button-badania\",class:{ 'active': !_vm.choosenPlinth },attrs:{\"label\":\"Feet\",\"no-ripple\":\"no-ripple\"},on:{\"click\":function($event){return _vm.updateConfigurator('plinth', false, false)}}}),_c('q-btn',{staticClass:\"button-badania\",class:{ 'active': _vm.choosenPlinth },attrs:{\"label\":\"Plinth\",\"no-ripple\":\"no-ripple\"},on:{\"click\":function($event){return _vm.updateConfigurator('plinth', true, false)}}})],1)],1)],1),_c('div',{staticClass:\"separator\"}),(_vm.isConfigurator)?_c('q-card-main',[_c('TylkoAddToCart')],1):_vm._e(),(_vm.type === 'mesh')?_c('q-card-main',{class:[_vm.isConfigurator ? 'configurator-mode-hidden' : '']},[_c('TylkoPreviewComponentSelect',{ref:\"previewSelect\",attrs:{\"grabButtons\":_vm.grabButtons,\"setupID\":_vm.setupID,\"components\":_vm.components,\"height\":_vm.choosenHeight,\"preview_mode\":_vm.$route.name,\"thumbnails\":_vm.thumbnails,\"distortionMode\":_vm.distortionMode,\"mappedObjects\":_vm.mappedObjects,\"lines\":_vm.lines}})],1):_vm._e()],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"colors-wrapper\"},_vm._l((_vm.colors),function(color){return (color.type === _vm.objectLine)?_c('button',{class:'color-btn ' + (color.value === _vm.choosenColor ? 'active': ''),on:{\"click\":function () { return _vm.changeColor(color.value); }}},[_c('img',{attrs:{\"src\":color.imgPath,\"alt\":color.alt}})]):_vm._e()}),0)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n        .colors-wrapper\n            button(\n            v-for=\"color in colors\"\n            v-if=\"color.type === objectLine\"\n            :class=\"'color-btn ' + (color.value === choosenColor ? 'active': '') \"\n            @click=\"() => changeColor(color.value)\"\n            )\n                img(\n                :src=\"color.imgPath\"\n                :alt=\"color.alt\")\n</template>\n\n<style lang=\"scss\">\n.color-btn {\n    width: 40px;\n    padding: 0;\n    height: 40px;\n    overflow: hidden;\n    border-radius: 50%;\n    margin-right: 8px;\n    outline: none;\n    border: 2px solid #fff;\n    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);\n    img {\n        display: block;\n        border-radius: 50%;\n        width: 100%;\n        height: auto;\n        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);\n    }\n    &:hover {\n        border-color: #cad0d0;\n        img {\n            transform: scale(0.9);\n        }\n    }\n    &.active {\n        border-color: #ff3c00;\n        img {\n            transform: scale(0.9);\n        }\n    }\n}\n\n.type-heading {\n    margin: 0;\n    line-height: 1;\n    margin-bottom: 8px;\n    font-size: 14px;\n}\n</style>\n\n<script>\nvar ASSETS_PATH =\n    window.location.href.indexOf('localhost') > -1\n        ? ''\n        : '/r_static/webdesigner'\n\nimport { cape } from '@core/cape'\nexport default {\n    name: 'TylkoPreviewColors',\n    props: ['objectLine', 'choosenColor', 'updateRenderer'],\n    methods: {\n        changeColor(value) {\n            cape.application.bus.$emit('updateRenderer', 'choosenColor', value)\n        },\n    },\n    data() {\n        return {\n            colors: [\n                {\n                    type: 'type_01',\n                    imgPath: ASSETS_PATH + '/statics/T01-1.svg',\n                    value: '0:0',\n                    alt: 'white',\n                },\n                {\n                    type: 'type_01',\n                    imgPath: ASSETS_PATH + '/statics/T01-2.svg',\n                    value: '0:3',\n                    alt: 'grey',\n                },\n                {\n                    type: 'type_01',\n                    imgPath: ASSETS_PATH + '/statics/T01-3.svg',\n                    value: '0:1',\n                    alt: 'black',\n                },\n                {\n                    type: 'type_01',\n                    imgPath: ASSETS_PATH + '/statics/T01-4.png',\n                    value: '0:5',\n                    alt: 'fornir',\n                },\n                {\n                    type: 'type_01',\n                    imgPath: ASSETS_PATH + '/statics/T01-5.svg',\n                    value: '0:4',\n                    alt: 'abuergine',\n                },\n                {\n                    type: 'type_02',\n                    imgPath: ASSETS_PATH + '/statics/T02-1.svg',\n                    value: '1:0',\n                    alt: 'white',\n                },\n                {\n                    type: 'type_02',\n                    imgPath: ASSETS_PATH + '/statics/T02-2.svg',\n                    value: '1:2',\n                    alt: 'color3',\n                },\n                {\n                    type: 'type_02',\n                    imgPath: ASSETS_PATH + '/statics/T02-3.svg',\n                    value: '1:1',\n                    alt: 'color2',\n                },\n                {\n                    type: 'type_02',\n                    imgPath: ASSETS_PATH + '/statics/T02-4.svg',\n                    value: '1:3',\n                    alt: 'color4',\n                },\n                {\n                    type: 'type_02',\n                    imgPath: ASSETS_PATH + '/statics/T02-5.svg',\n                    value: '1:4',\n                    alt: 'color5',\n                },\n            ],\n        }\n    },\n}\n</script>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewColors.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewColors.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoPreviewColors.vue?vue&type=template&id=12588e0a&lang=pug&\"\nimport script from \"./TylkoPreviewColors.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoPreviewColors.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoPreviewColors.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"component-select\"},[_vm._l((_vm.configuration),function(config,key){return (!_vm.isConfigurator)?_c('q-field',[_vm._v(_vm._s(key)+\" - Chn:  \"+_vm._s(config.channel_id)+\" - Tabl: \"+_vm._s(config.table_id)+\" - Seri \"+_vm._s(config.series_id)+\" - Comp \"+_vm._s(config.component_id))]):_vm._e()}),_c('q-card-separator'),_c('q-tabs',{attrs:{\"color\":\"tertiary\",\"inverted\":\"inverted\"},model:{value:(_vm.selectedTab),callback:function ($$v) {_vm.selectedTab=$$v},expression:\"selectedTab\"}},[_c('q-tab',{key:\"0\",style:(_vm.isConfigurator ? 'display: none' : ''),attrs:{\"slot\":\"title\",\"name\":\"0\",\"label\":\"0\"},slot:\"title\"}),_vm._l((_vm.configuration),function(config,key){return _c('q-tab',{key:'' + (key +1),style:(_vm.isConfigurator ? 'display: none' : ''),attrs:{\"slot\":\"title\",\"name\":'' + (key +1),\"label\":'' + (key +1)},slot:\"title\"})})],2),_c('q-tab-panels',{staticClass:\"tab-style tab-style\",model:{value:(_vm.selectedTab),callback:function ($$v) {_vm.selectedTab=$$v},expression:\"selectedTab\"}},[_c('q-tab-panel',{style:(_vm.isConfigurator ? 'display: none' : ''),attrs:{\"name\":\"0\"}},[(!_vm.isConfigurator)?_c('q-tree',{attrs:{\"nodes\":_vm.setupsTreeNodes,\"node-key\":\"label\",\"default-expand-all\":\"default-expand-all\"}}):_vm._e()],1),_vm._l((_vm.configuration),function(config,key){return _c('q-tab-panel',{class:['component-select-wrapper', _vm.isConfigurator ? 'configurator-style' : ''],style:(_vm.isConfigurator ? (\"position: fixed; top: \" + (_vm.mappedObjects[key] ? _vm.mappedObjects[key].y : 0) + \"px; left: \" + (_vm.mappedObjects[key] ? _vm.mappedObjects[key].x : 0) + \"px;}\"):''),attrs:{\"name\":'' + (key +1)},nativeOn:{\"mouseleave\":function($event){return (function () { return _vm.triggerPrevSelected(); })($event)},\"mouseover\":function($event){return (function () { return _vm.clearPrevSelectedTimeout(); })($event)}}},[(config.options && _vm.optionsError(config))?_c('div',{staticClass:\"div tab-style\"},[(config.options[0])?_c('div',{staticClass:\"series\"},_vm._l((config.options),function(option,k){return _c('div',{key:_vm.hash((\"\" + (option.value))),class:['miniature-wrapper', (config.series_id === option.value ? ' active' : '')],on:{\"click\":function () {config.series_id = option.value; _vm.dispatchToConfiguration(); _vm.autoSaveState()}}},[(option.thumbnail)?_c('TylkoMiniature',{staticClass:\"mini\",attrs:{\"bg-color\":\"#ffffff\",\"geo\":option.thumbnail}}):_vm._e()],1)}),0):_vm._e(),(!config.options[0].value)?_c('q-field',{staticClass:\"select\",attrs:{\"label\":'m_config_id (' + config.m_config_id + ')'}},[_c('p',[_vm._v(_vm._s(config.options[0].label)+\"                    \")])]):_vm._e(),(_vm.distortionMode === 'local_x' || _vm.distortionMode === 'local_all')?_c('q-field',{attrs:{\"label\":\"Width\"}},[_c('q-slider',{attrs:{\"min\":-20,\"max\":20,\"label-always\":true,\"label-value\":config.width + ' cm'},on:{\"input\":function () { _vm.dispatchToConfiguration(); _vm.lazyAutoSaveState();}},nativeOn:{\"mouseup\":function($event){return (function () { _vm.dispatchToConfiguration(true); })($event)}},model:{value:(config.distortion),callback:function ($$v) {_vm.$set(config, \"distortion\", $$v)},expression:\"config.distortion\"}})],1):_vm._e(),_c('div',{staticStyle:{\"display\":\"flex\"}},[(config.door_flippable && config.options[0].value)?_c('div',{staticClass:\"t-field\"},[_c('q-checkbox',{staticStyle:{\"margin-top\":\"6px\"},attrs:{\"true-value\":\"right\",\"false-value\":\"left\"},on:{\"input\":function () {_vm.dispatchToConfiguration(true); _vm.autoSaveState()}},model:{value:(config.door_flip),callback:function ($$v) {_vm.$set(config, \"door_flip\", $$v)},expression:\"config.door_flip\"}}),_c('p',[_vm._v(\"Door flip\")])],1):_vm._e(),(config.cables_available && config.options[0].value)?_c('div',{staticClass:\"t-field\",class:{ 'push': config.door_flippable && config.options[0].value }},[_c('q-checkbox',{staticStyle:{\"margin-top\":\"5px\"},on:{\"input\":function () {_vm.dispatchToConfiguration(true); _vm.autoSaveState()}},model:{value:(config.cables),callback:function ($$v) {_vm.$set(config, \"cables\", $$v)},expression:\"config.cables\"}}),_c('p',[_vm._v(\"Opening for cables\")])],1):_vm._e()])],1):_vm._e(),(_vm.preview_mode !== 'configurator')?_c('div',{staticClass:\"div\"},[_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.component_id)+\" - komponent\")]),_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.series_id)+\" - seria\")]),_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.table_id)+\" - tablica\")]),_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.channel_id)+\" - kanał\")]),_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.m_config_id)+\" - m_config_id\")]),_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.cables)+\" - cables\")]),_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.door_flip)+\" - door flip\")]),(_vm.distortionMode != 'OFF')?_c('div',{staticClass:\"row\"},[_vm._v(_vm._s(config.distortion)+\" - distortion\")]):_vm._e()]):_vm._e()])})],2),(_vm.configuration[_vm.selectedTab-1] && _vm.distortionMode === 'edge')?_c('div',[(_vm.configuration[_vm.selectedTab-1].line_left)?_c('div',{style:((\"transform: translate(\" + (_vm.mapEdges(_vm.lines[_vm.configuration[_vm.selectedTab-1].line_left]).x) + \"px, \" + (_vm.mapEdges(_vm.lines[_vm.configuration[_vm.selectedTab-1].line_left]).y-70) + \"px)\")),attrs:{\"id\":\"button-grab-left\"}},[_c('q-btn',{staticClass:\"tylko-button\",attrs:{\"round\":\"round\",\"dense\":\"dense\",\"color\":\"white\",\"size\":\"md\",\"icon\":_vm.lines[_vm.configuration[_vm.selectedTab-1].line_left].value === _vm.lines[_vm.configuration[_vm.selectedTab-1].line_left].v_min ? 'keyboard_arrow_right' : _vm.lines[_vm.configuration[_vm.selectedTab-1].line_left].value === _vm.lines[_vm.configuration[_vm.selectedTab-1].line_left].v_max ? 'keyboard_arrow_left' : 'code'},nativeOn:{\"mousedown\":function($event){return (function (e) { return _vm.startDrag(_vm.configuration[_vm.selectedTab-1].line_left, e); })($event)}}})],1):_vm._e(),(_vm.configuration[_vm.selectedTab-1].line_right)?_c('div',{style:((\"transform: translate(\" + (_vm.mapEdges(_vm.lines[_vm.configuration[_vm.selectedTab-1].line_right]).x) + \"px, \" + (_vm.mapEdges(_vm.lines[_vm.configuration[_vm.selectedTab-1].line_right]).y-70) + \"px)\")),attrs:{\"id\":\"button-grab-right\"}},[_c('q-btn',{staticClass:\"tylko-button\",attrs:{\"round\":\"round\",\"dense\":\"dense\",\"color\":\"white\",\"size\":\"md\",\"icon\":_vm.lines[_vm.configuration[_vm.selectedTab-1].line_right].value === _vm.lines[_vm.configuration[_vm.selectedTab-1].line_right].v_min ? 'keyboard_arrow_right' : _vm.lines[_vm.configuration[_vm.selectedTab-1].line_right].value === _vm.lines[_vm.configuration[_vm.selectedTab-1].line_right].v_max ? 'keyboard_arrow_left' : 'code'},nativeOn:{\"mousedown\":function($event){return (function (e) { return _vm.startDrag(_vm.configuration[_vm.selectedTab-1].line_right, e); })($event)}}})],1):_vm._e()]):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n    div.component-select\n        q-field(v-if=\"!isConfigurator\" v-for=\"(config, key) in configuration\")\n            | {{key}} - Chn:  {{ config.channel_id}} - Tabl: {{ config.table_id }} - Seri {{config.series_id}} - Comp {{config.component_id }}\n        q-card-separator\n        q-tabs(v-model=\"selectedTab\", color=\"tertiary\", inverted)\n            q-tab(name=\"0\" slot=\"title\" key=\"0\" label=\"0\" :style=\"isConfigurator ? 'display: none' : ''\")\n            q-tab(v-for=\"(config, key) in configuration\" :name=\"'' + (key +1)\" slot=\"title\" :key=\"'' + (key +1)\" :label=\"'' + (key +1)\" :style=\"isConfigurator ? 'display: none' : ''\" )\n        q-tab-panels.tab-style(v-model=\"selectedTab\", class=\"tab-style\")\n            q-tab-panel(name=\"0\" :style=\"isConfigurator ? 'display: none' : ''\")\n                q-tree(\n                v-if=\"!isConfigurator\"\n                :nodes=\"setupsTreeNodes\"\n                node-key=\"label\"\n                default-expand-all\n                )\n            q-tab-panel(\n                v-for=\"(config, key) in configuration\",\n                :name=\"'' + (key +1) \",\n                @mouseleave.native=\"() => triggerPrevSelected()\",\n                @mouseover.native=\"() => clearPrevSelectedTimeout()\",\n                :style=\"isConfigurator ? `position: fixed; top: ${mappedObjects[key] ? mappedObjects[key].y : 0}px; left: ${mappedObjects[key] ? mappedObjects[key].x : 0}px;}`:''\"\n                :class=\"['component-select-wrapper', isConfigurator ? 'configurator-style' : '']\")\n                .div.tab-style(v-if=\"config.options && optionsError(config)\")\n                    div(class=\"series\" v-if=\"config.options[0]\")\n                        div(\n                            v-for=\"(option, k) in config.options\"\n                            :key=\"hash(`${option.value}`)\",\n                            :class=\"['miniature-wrapper', (config.series_id === option.value ? ' active' : '')]\"\n                            @click=\"() => {config.series_id = option.value; dispatchToConfiguration(); autoSaveState()}\"\n                        )\n                            TylkoMiniature.mini(\n                                v-if=\"option.thumbnail\",\n                                bg-color=\"#ffffff\",\n                                :geo=\"option.thumbnail\" )\n\n                    q-field(:label=\"'m_config_id (' + config.m_config_id + ')'\" v-if=\"!config.options[0].value\" class=\"select\")\n                        p {{ config.options[0].label }}                    \n                    q-field(label=\"Width\" v-if=\"distortionMode === 'local_x' || distortionMode === 'local_all'\")\n                        q-slider(\n                        v-model=\"config.distortion\"\n                        :min=\"-20\",\n                        :max=\"20\",\n                        :label-always=\"true\"\n                        :label-value=\"config.width + ' cm'\"\n                        @input=\"() => { dispatchToConfiguration(); lazyAutoSaveState();}\",\n                        @mouseup.native=\"() => { dispatchToConfiguration(true); }\"\n                        )\n\n                    //q-field(:label=\"'EdgeL:' + (config.line_left || '-')\", v-if=\"distortionMode === 'edge'\")\n                        q-slider(\n                            v-if=\"config.line_left && lines.hasOwnProperty(config.line_left)\",\n                            v-model=\"lines[config.line_left].value\",\n                            :min=\"lines[config.line_left].v_min\",\n                            :max=\"lines[config.line_left].v_max\",\n                            snap=\"true\",\n                            :label-always=\"true\",\n                            :label-value=\"config.width + ' cm'\",\n                            @input=\"() => { dispatchToConfiguration(); lazyAutoSaveState();}\",\n                            @mouseup.native=\"() => { dispatchToConfiguration(true); }\"\n                        )\n                    //q-field(:label=\"'EdgeR:' + (config.line_right || '-')\", v-if=\"distortionMode === 'edge'\")\n                        q-slider(\n                            v-if=\"config.line_right && lines.hasOwnProperty(config.line_right)\",\n                            v-model=\"lines[config.line_right].value\",\n                            :min=\"lines[config.line_right].v_min\",\n                            :max=\"lines[config.line_right].v_max\",\n                            snap=\"true\",\n                            :label-always=\"true\",\n                            :label-value=\"config.width + ' cm'\",\n                            @input=\"() => { dispatchToConfiguration(); lazyAutoSaveState();}\",\n                            @mouseup.native=\"() => { dispatchToConfiguration(true); }\"\n                        )\n                    div(style=\"display: flex\")\n                        div.t-field(v-if=\"config.door_flippable && config.options[0].value\")\n                            q-checkbox(\n                                v-model=\"config.door_flip\"\n                                style=\"margin-top: 6px\"\n                                true-value=\"right\"\n                                false-value=\"left\"\n                                @input=\"() => {dispatchToConfiguration(true); autoSaveState()}\"\n                            )\n                            p Door flip\n                        div.t-field(v-if=\"config.cables_available && config.options[0].value\", \n                            :class=\"{ 'push': config.door_flippable && config.options[0].value }\")\n                            q-checkbox(\n                                v-model=\"config.cables\"\n                                style=\"margin-top: 5px\"\n                                @input=\"() => {dispatchToConfiguration(true); autoSaveState()}\"\n                            )\n                            p Opening for cables\n                .div(v-if=\"preview_mode !== 'configurator'\")\n                    .row {{config.component_id}} - komponent\n                    .row {{config.series_id}} - seria\n                    .row {{config.table_id}} - tablica\n                    .row {{config.channel_id}} - kanał\n                    .row {{config.m_config_id}} - m_config_id\n                    .row {{config.cables}} - cables\n                    .row {{config.door_flip}} - door flip\n                    .row(v-if=\"distortionMode != 'OFF'\") {{config.distortion}} - distortion\n\n        div(v-if=\"configuration[selectedTab-1] && distortionMode === 'edge'\")\n            div#button-grab-left(v-if=\"configuration[selectedTab-1].line_left\", :style=\"`transform: translate(${mapEdges(lines[configuration[selectedTab-1].line_left]).x}px, ${mapEdges(lines[configuration[selectedTab-1].line_left]).y-70}px)`\")\n                q-btn.tylko-button(@mousedown.native=\"e => startDrag(configuration[selectedTab-1].line_left, e)\", \n                    round, dense, color=\"white\", size=\"md\",\n                    :icon=\"lines[configuration[selectedTab-1].line_left].value === lines[configuration[selectedTab-1].line_left].v_min ? 'keyboard_arrow_right' : lines[configuration[selectedTab-1].line_left].value === lines[configuration[selectedTab-1].line_left].v_max ? 'keyboard_arrow_left' : 'code' \"\n                    )\n            div#button-grab-right(v-if=\"configuration[selectedTab-1].line_right\", :style=\"`transform: translate(${mapEdges(lines[configuration[selectedTab-1].line_right]).x}px, ${mapEdges(lines[configuration[selectedTab-1].line_right]).y-70}px)`\")\n                q-btn.tylko-button(@mousedown.native=\"e => startDrag(configuration[selectedTab-1].line_right, e)\", \n                    round, dense, color=\"white\", size=\"md\",\n                    :icon=\"lines[configuration[selectedTab-1].line_right].value === lines[configuration[selectedTab-1].line_right].v_min ? 'keyboard_arrow_right' : lines[configuration[selectedTab-1].line_right].value === lines[configuration[selectedTab-1].line_right].v_max ? 'keyboard_arrow_left' : 'code' \"\n                    )\n\n\n</template>\n\n<script>\nimport XXH from 'xxhashjs'\nimport ClickOutside from 'vue-click-outside'\n\nimport { cape } from '@core/cape'\nimport TylkoMiniature from '@cape-ui/TylkoMiniature'\nimport _ from 'lodash'\nconst uuidv4 = require('uuid/v4')\n\nfunction isObject(value) {\n    return (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object &&\n        Object.keys(value).length !== 0\n    )\n}\n\nexport default {\n    name: 'TylkoPreviewComponentSelect',\n\n    props: [\n        'setupID',\n        'components',\n        'height',\n        'preview_mode',\n        'thumbnails',\n        'distortionMode',\n        'mappedObjects',\n        'lines',\n        'grabButtons',\n    ],\n    components: {\n        TylkoMiniature,\n    },\n    methods: {\n        startDrag(edge, event) {\n            if (!this.dragging) {\n                this.dragging = {\n                    id: edge,\n                    startX: event.clientX,\n                    startY: event.clientY,\n                    state: this.lines[edge].value,\n                    startOffset: this.mapEdges(\n                        { x: event.clientX, y: event.clientY, z: 0 },\n                        true\n                    ),\n                }\n            }\n        },\n\n        drag(event) {\n            if (this.dragging) {\n                let line = this.lines[this.dragging.id]\n                let dragPoint = this.lines[this.dragging.id]\n\n                let currentOffserProjectionReverse = this.mapEdges(\n                    { x: event.clientX, y: event.clientY, z: 0 },\n                    true\n                )\n\n                let offset = -(\n                    this.dragging.startOffset.x -\n                    currentOffserProjectionReverse.x\n                )\n                offset = this.dragging.state + offset\n                offset = Math.max(line.v_min, Math.min(offset, line.v_max))\n                console.log(\n                    'OFFFSET',\n                    this.dragging.startOffset.x -\n                        currentOffserProjectionReverse.x,\n                    this.dragging\n                )\n                this.lines[this.dragging.id].value = offset\n                this.dispatchToConfiguration()\n                this.lazyAutoSaveState()\n            }\n        },\n\n        stopDrag() {\n            this.dragging = null\n        },\n\n        triggerPrevSelected() {\n            cape.application.bus.$emit('triggerPrevSelected')\n        },\n        clearPrevSelectedTimeout() {\n            cape.application.bus.$emit('clearPrevSelectedTimeout')\n        },\n        hash(input) {\n            if (!this.H) this.H = XXH.h32(0xabcd)\n            return this.H.update(input)\n                .digest()\n                .toString(16)\n        },\n\n        uuiid() {\n            return uuidv4()\n        },\n        autoSaveState() {\n            if (this.isConfigurator)\n                cape.application.bus.$emit('savePreviewParametersState', true)\n        },\n        lazyAutoSaveState: _.debounce(function() {\n            this.autoSaveState()\n        }, 300),\n        setupsTree() {\n            this.setupsTreeNodes = this.mapObject(this.objToDispatch)\n        },\n        mapObject(obj) {\n            if (isObject(obj)) {\n                let arr = []\n                Object.keys(obj).forEach(key => {\n                    if (isObject(obj[key])) {\n                        arr = [\n                            ...arr,\n                            {\n                                label: key,\n                                children: this.mapObject(obj[key]),\n                            },\n                        ]\n                    } else {\n                        arr = [...arr, { label: key + ': ' + obj[key] }]\n                    }\n                })\n                return arr\n            }\n            return []\n        },\n        optionsError(config) {\n            if (_.has(config, 'options')) {\n                if (config.options.some(value => !!value === false))\n                    return false\n                return true\n            } else {\n                return false\n            }\n        },\n        dispatchToConfiguration(generateThumbs) {\n            let setups = {}\n            let channels = {}\n            let lines_data = {}\n\n            this.configuration.forEach(\n                ({\n                    tab,\n                    m_config_id,\n                    door_flip,\n                    series_id,\n                    channel_id,\n                    distortion,\n                    cables,\n                }) => {\n                    distortion = distortion ? distortion : {}\n                    // ^^ Co tu sie dzieje?\n                    setups = {\n                        ...setups,\n                        [m_config_id]: {\n                            door_flip,\n                            series_id,\n                            cables,\n                            distortion,\n                        },\n                    }\n                    if (this.selectedTab === tab) {\n                        channels = {\n                            ...channels,\n                            [channel_id]: {\n                                door_flip,\n                                series_id,\n                                cables,\n                                distortion,\n                            },\n                        }\n                    }\n                }\n            )\n\n            for (let [line_name, line_data] of Object.entries(this.lines)) {\n                lines_data[line_name] = line_data.value\n            }\n\n            this.objToDispatch = {\n                setups: {\n                    ...this.objToDispatch.setups,\n                    [this.setupID]: setups,\n                },\n                channels: { ...this.objToDispatch.channels, ...channels },\n                lines: { ...this.objToDispatch.lines, ...lines_data },\n            }\n\n            if (generateThumbs) {\n                this.generateThumbnails(this.selectedTab)\n            } else {\n                this.thumbsForChannel(null)\n                cape.application.bus.$emit('disableThumbnails')\n            }\n\n            cape.application.bus.$emit(\n                'dispatchConfiguratorCustomParams',\n                this.objToDispatch\n            )\n            this.setupsTree()\n        },\n        thumbsForChannel(channel) {\n            cape.application.bus.$emit('thumbsForChannel', channel)\n        },\n        lazyGenerateThumbanils: _.debounce(function() {\n            this.generateThumbnails(this.selectedTab)\n        }, 200),\n        generateThumbnails(index) {\n            if (index !== '0') {\n                cape.application.bus.$emit('generateThumbnails')\n            }\n            cape.application.bus.$emit(\n                'changeElements',\n                index !== '0' ? 'all_opened' : 'all'\n            )\n            cape.application.bus.$emit('changeSetupTabIndex', index)\n        },\n        processGeometry(geo) {\n            if (this.thumbnails) {\n                this.thumbnailsStore = this.thumbnails\n            }\n            this.configuration = this.configuration.map(\n                ({ m_config_id, table_id, ...rest }, key) => ({\n                    ...rest,\n                    m_config_id,\n                    table_id,\n                    tab: '' + (key + 1),\n                    options:\n                        this.thumbnailsStore &&\n                        this.thumbnailsStore.hasOwnProperty(m_config_id)\n                            ? this.thumbnailsStore[m_config_id].map(\n                                  ({ series_name, series_id, ...rest }) => ({\n                                      label: series_name,\n                                      value: series_id,\n                                      ...rest,\n                                  })\n                              )\n                            : null,\n                })\n            )\n        },\n    },\n\n    watch: {\n        components(components) {\n            this.configuration = components.map(\n                ({\n                    m_config_id,\n                    door_flip,\n                    table_id,\n                    series_id,\n                    door_flippable,\n                    channel_id,\n                    component_id,\n                    distortion,\n                    cables_available,\n                    cables,\n                    x1,\n                    x2,\n                    line_left,\n                    line_right,\n                }) => {\n                    return {\n                        m_config_id,\n                        door_flip,\n                        table_id,\n                        series_id,\n                        door_flippable,\n                        channel_id,\n                        component_id,\n                        distortion,\n                        cables_available,\n                        cables,\n                        line_left,\n                        line_right,\n                        width: Math.round((x2 - x1) / 10),\n                    }\n                }\n            )\n            this.height = this.height.replace(/\\s/g, '')\n            cape.api.geo.mesh({ type: 'mesh', id: this.$route.params.id }).pipe(\n                this.processGeometry,\n                'preview'\n            )\n        },\n        selectedTab(index) {\n            this.thumbsForChannel(null)\n            this.generateThumbnails(index)\n        },\n    },\n    mounted() {\n        cape.application.bus.$emit('changeElemnets', 'all')\n        cape.application.bus.$on('resetTabs', () => {\n            this.selectedTab = '0'\n        })\n        cape.application.bus.$on('loadConfiguratorCustomParams', payload => {\n            this.objToDispatch = payload\n            this.setupsTree()\n        })\n        window.addEventListener('mouseup', this.stopDrag)\n        window.addEventListener('mousemove', this.drag)\n    },\n    data() {\n        return {\n            configuration: [],\n            selectedTab: '0',\n            distortion: false,\n            thumbnailsStore: null,\n            objToDispatch: {},\n            setupsTreeNodes: [],\n            isConfigurator: this.$route.name === 'configurator',\n        }\n    },\n}\n</script>\n\n<style lang=\"scss\">\n.tab-style {\n    max-width: 500px;\n}\n\n.t-field {\n    * {\n        display: inline-block;\n        line-height: 16px;\n    }\n    &.push {\n        margin-left: 16px;\n    }\n}\n\n.configurator-style {\n    background: white;\n    border-radius: 10px;\n    transform: translateX(-50%);\n    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05), 0 6px 20px 0 rgba(0, 0, 0, 0.1);\n    margin-top: 43px;\n    z-index: 100;\n    max-width: 500px;\n    max-height: 300px;\n}\n\n.component-select-wrapper {\n    .q-field-label {\n        width: 70px;\n        color: black;\n    }\n}\n\n.series {\n    display: flex;\n    flex-wrap: wrap;\n    margin-bottom: 5px;\n    .miniature-wrapper {\n        margin-right: 5px;\n        margin-bottom: 9px;\n        cursor: pointer;\n        box-sizing: border-box;\n        border-radius: 7px;\n\n        &.active {\n            .container {\n                border-width: 2px;\n                border-color: #ff3c00;\n                &:hover {\n                    border-color: #ff3c00;\n                }\n            }\n        }\n\n        .container {\n            /*transition: border-width 0.1s ease, border-color 0.1s ease;*/\n            box-sizing: border-box;\n            width: 100px;\n            height: 100px;\n            border: 2px white solid;\n\n            border-radius: 7px;\n            overflow: hidden;\n            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05),\n                0 6px 20px 0 rgba(0, 0, 0, 0.1);\n\n            &:hover {\n                border-width: 2px;\n                border-color: #cad0d0;\n            }\n            .mini,\n            .mini > img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n    }\n}\n\n.component-select {\n    margin-top: 15px;\n    .select {\n        border-top: 1px solid rgba(#fff, 0.3);\n        margin-top: 5px;\n    }\n    .q-option {\n        margin-right: 10px;\n    }\n    .q-input-target,\n    .q-input-shadow {\n        color: white;\n    }\n}\n\n.configuration-wrapper {\n    margin-top: 0;\n}\n\n#button-grab-left,\n#button-grab-right {\n    position: fixed;\n    top: 70px;\n    left: 0px;\n    button {\n        transform: translate(-50%, -50%);\n    }\n}\n</style>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewComponentSelect.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewComponentSelect.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoPreviewComponentSelect.vue?vue&type=template&id=41b06fba&lang=pug&\"\nimport script from \"./TylkoPreviewComponentSelect.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoPreviewComponentSelect.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoPreviewComponentSelect.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _vm._m(0)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"row\"},[_c('div',{staticClass:\"col-xs-3\"},[_c('div',{staticClass:\"shipping\"},[_vm._v(\"\\n      Ship in\\n      \"),_c('br'),_vm._v(\"4-6 weeks\\n    \")])]),_c('div',{staticClass:\"col-xs-9\"},[_c('button',{staticClass:\"button button-red\"},[_vm._v(\"Add to cart\")])])])}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"row\">\n    <div class=\"col-xs-3\">\n      <div class=\"shipping\">\n        Ship in\n        <br>4-6 weeks\n      </div>\n    </div>\n    <div class=\"col-xs-9\">\n      <button class=\"button button-red\">Add to cart</button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n    name: 'TylkoAddToCart',\n}\n</script>\n<style lang=\"scss\" scoped>\n@import '~@theme/tylko/styles-from-tylko-com.scss';\nbutton {\n    width: 100%;\n}\n.shipping {\n    color: black;\n    font-size: 16px;\n    position: relative;\n    top: 3px;\n}\nbutton.button.button-red:hover {\n    border-color: #ff3c00;\n}\n</style>\n", "import mod from \"-!../../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoAddToCart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoAddToCart.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoAddToCart.vue?vue&type=template&id=faeb3fda&scoped=true&\"\nimport script from \"./TylkoAddToCart.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoAddToCart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoAddToCart.vue?vue&type=style&index=0&id=faeb3fda&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"faeb3fda\",\n  null\n  \n)\n\nexport default component.exports", "<template lang=\"pug\">\n    q-scroll-area.configuration-wrapper(style=\"height=100%\", :class=\"{ 'badania' : isConfigurator }\")\n        span.title(v-if=\"isConfigurator\") Type01 Sideboard\n        q-card.no-shadow.bradius\n            q-card-title(class=\"card-title\")\n                span(v-if=\"!isConfigurator\") {{type}} - {{element ? element.name : ''}} | {{objectLine}}\n                span(class=\"text-weight-bold\" style=\"color: black; font-size: 24px; line-height: 24px\") {{price}}€\n                q-btn-group(v-if=\"!isConfigurator\")\n                    q-btn(\n                    label=\"RESET\",\n                    v-if=\"$route.params.type === 'mesh' && isConfigurator\", size=\"sm\", class=\"q-pl-md\",\n                    @click=\"resetToInitialState\"\n                    )\n                    q-btn(\n                    label=\"SAVE STATE\", color=\"positive\",\n                    v-if=\"$route.params.type === 'mesh' && !isConfigurator\", size=\"sm\", class=\"q-pl-md\",\n                    @click=\"saveState\"\n                    )\n                    q-btn(\n                    :label=\"this.isConfigurator ? '' : 'PREVIOUS STATE'\", v-if=\"$route.params.type === 'mesh'\", size=\"md\", class=\"q-pl-md arrow-btn\",\n                    @click=\"undo\", :disable=\"!this.previousStateAvailble\", :icon=\"this.isConfigurator ? 'undo' : ''\"\n                    )\n                    q-btn(\n                    :label=\"this.isConfigurator ? '' : 'NEXT STATE'\", v-if=\"$route.params.type === 'mesh'\", size=\"md\", class=\"q-pl-md arrow-btn\",\n                    @click=\"redo\", :disable=\"!this.nextStateAvailble\", :icon=\"this.isConfigurator ? 'redo' : ''\"\n                    )\n                .row(v-if=\"$route.params.type === 'mesh' && !isConfigurator\")\n                    q-slider(\n                    v-model=\"currentParametersHistoryState\",\n                    markers, snap,\n                    :step=1, :min=0, :max=\"this.parametersStatesStatus[1]\",\n                    @input=\"val => setState(val)\",\n                    )\n            .separator\n            q-card-main(class='row justify-between' v-if=\"!isConfigurator\")\n                q-toggle.col-8(\n                v-model=\"choosenLocked\"\n                label=\"Camera lock\"\n                @input=\"val => updateParam('locked', val)\"\n                )\n\n                router-link(\n                v-if=\"!isConfigurator && $route.params.type === 'mesh' && element\",\n                tag='a', :to=\"`/meshes/${element.collection}/${element.id}`\"\n                )\n                    q-btn(rounded, icon-right=\"settings\", size=\"sm\", label=\"EDIT\", class=\"link-button q-pl-md\")\n\n                router-link(\n                v-if=\"!isConfigurator && $route.params.type === 'component' && element\",\n                tag='a', :to=\"`/components/${element.collection}/${element.parent}/${element.id}`\"\n                )\n                    q-btn(rounded, icon-right=\"settings\", size=\"sm\", label=\"EDIT\", color=\"blue\")\n            q-card-main(class=\"settings-wrapper\")\n                .row.items-center\n                    q-field(label=\"Color\" class=\"col-md-2\")\n                    TylkoPreviewColors(\n                        :objectLine=\"objectLine\"\n                        :choosenColor=\"choosenColor\"\n                        class=\"col-md-10 justify-end\"\n                    )\n                .row.items-center\n                    q-field(label=\"Width\" v-if=\"widthRange.length\" class=\"col-md-2\")\n                    q-slider(\n                        class=\"col-md-10\"\n                        v-model=\"choosenWidth\"\n                        :min=\"widthRange[0]\",\n                        :max=\"widthRange[1]\",\n                        label\n                        :label-always=\"true\"\n                        :label-value=\"+ choosenWidth +' cm'\"\n                        @input=\"val => updateConfigurator('decoderWidth', val, true)\"\n                            )\n                .row.items-center\n                    q-field(label=\"Height\" v-if=\"heightRange.length && type === 'mesh'\" class=\"col-md-2\")\n                    q-slider(\n                        class=\"col-md-10\"\n                        v-model=\"heightIndex\"\n                        :min=0,\n                        :max=\"heightRange.length - 1\",\n                        :step=1,\n                        label\n                        markers\n                        :label-always=\"true\"\n                        :label-value=\"Math.round((+choosenHeight + additional_height[0] + additional_height[1])/10) +' cm'\"\n                        @input=\"val => {computeHeight(val); lazyAutoSaveState(); showRendererElements()}\"\n                    )\n                .row.items-center(v-if=\"type === 'mesh' && ['gradient', 'ratio'].includes(distortionMode)\")\n                    q-field(label=\"Motion\" class=\"col-md-2\")\n                    q-slider(\n                        v-model=\"choosenDistortion\"\n                        class=\"col-md-10\"\n                        :min=\"0\",\n                        :max=\"100\",\n                        :label-always=\"true\"\n                        :label-value=\"choosenDistortion +' %'\"\n                        @input=\"val => updateConfigurator('distortion', val, true)\"\n                    )\n                .row.items-center(v-if=\"densityMode === 'setup_range_slider'\")\n                    q-field(label=\"Density\" class=\"col-md-2\")\n                    q-slider(\n                        v-model=\"choosenDensity\"\n                        class=\"col-md-10\"\n                        :min=\"0\",\n                        :max=\"100\",\n                        :label-always=\"true\"\n                        :label-value=\"choosenDensity +' %'\"\n                        @input=\"val => updateConfigurator('density', val, true)\"\n                    )\n                q-field(label=\"Columns\" class=\"col-md-10\" v-if=\"densityMode === 'setup_range_stepper' && densityOptions.length !== 0\")\n                    .row.items-center(style=\"padding-top: 14px;\")\n                        q-btn.button-badania.small(\n                            label=\"–\",\n                            :class=\"{ 'disable': densityOptions[0][0] === choosenDensity }\",\n                            @click=\"val => updateConfigurator('density', densityOptions[ densityOptions.findIndex(e => e[0] === choosenDensity) - 1 ][0], false)\"\n                        ) \n                        | {{ densityOptions.find(e => e[0] === choosenDensity)[1] }} &nbsp; &nbsp;\n                        q-btn.button-badania.small(\n                            label=\"+\",\n                            :class=\"{ 'disable': densityOptions[densityOptions.length - 1][0] === choosenDensity }\",\n                            @click=\"val => updateConfigurator('density', densityOptions[ densityOptions.findIndex(e => e[0] === choosenDensity) + 1 ][0], false)\"\n                        )\n                .row.items-center\n                    q-field(label=\"Depth\" class=\"component-select\" class=\"col-md-10\", style=\"float: left;\")\n                        q-btn.button-badania(\n                            :class=\"{ 'active': choosenDepth==320 }\",\n                            label=\"32cm\",\n                            no-ripple,\n                            @click=\"updateConfigurator('depth', 320, false)\"\n                        )\n                        q-btn.button-badania(\n                            :class=\"{ 'active': choosenDepth==400 }\",\n                            label=\"40cm\",\n                            no-ripple,\n                            @click=\"updateConfigurator('depth', 400, false)\"\n                        )\n                .row.items-center\n                    q-field(label=\"Base\" class=\"component-select\" class=\"col-md-10\")\n                        q-btn.button-badania(\n                            :class=\"{ 'active': !choosenPlinth }\",\n                            label=\"Feet\",\n                            no-ripple,\n                            @click=\"updateConfigurator('plinth', false, false)\"\n                        )\n                        q-btn.button-badania(\n                            :class=\"{ 'active': choosenPlinth }\",\n                            label=\"Plinth\",\n                            no-ripple,\n                            @click=\"updateConfigurator('plinth', true, false)\"\n                        )\n            .separator\n            q-card-main(v-if=\"isConfigurator\")\n                TylkoAddToCart\n            q-card-main(v-if=\"type === 'mesh'\" :class=\"[isConfigurator ? 'configurator-mode-hidden' : '']\")\n                TylkoPreviewComponentSelect(\n                    :grabButtons=\"grabButtons\",\n                    :setupID=\"setupID\"\n                    :components=\"components\"\n                    :height=\"choosenHeight\"\n                    :preview_mode=\"$route.name\"\n                    :thumbnails=\"thumbnails\"\n                    :distortionMode=\"distortionMode\"\n                    :mappedObjects=\"mappedObjects\"\n                    :lines=\"lines\"\n                    ref=\"previewSelect\"\n                )\n\n\n</template>\n\n\n<script>\nimport { cape } from '@core/cape'\nimport TylkoPreviewColors from './TylkoPreviewColors'\nimport TylkoPreviewComponentSelect from './TylkoPreviewComponentSelect'\nimport TylkoAddToCart from './fakeTylkoUi/TylkoAddToCart'\n\nimport _ from 'lodash'\n\nexport default {\n    name: 'TylkoPreviewConfiguration',\n    props: [\n        'grabButtons',\n        'updateRenderer',\n        'element',\n        'type',\n        'decoderWidth',\n        'decoderHeight',\n        'decoderOutput',\n        'objectLine',\n        'choosenColor',\n        'distortion',\n        'density',\n        'depth',\n        'plinth',\n        'price',\n        'locked',\n        'mappedObjects',\n        'previousStateAvailble',\n        'nextStateAvailble',\n        'parametersStatesStatus',\n    ],\n    components: {\n        TylkoPreviewComponentSelect,\n        TylkoPreviewColors,\n        TylkoAddToCart,\n    },\n\n    methods: {\n        showRendererElements() {\n            window.clearTimeout(this.rendererTimeout)\n            // cape.application.bus.$emit('showRendererElements');\n            cape.application.bus.$emit('showCompartments')\n            this.rendererTimeout = window.setTimeout(() => {\n                // cape.application.bus.$emit('hideRendererElements');\n                cape.application.bus.$emit('hideCompartments')\n            }, 2000)\n        },\n        updateConfigurator(param, val, lazy) {\n            this.updateParam(param, val)\n            if (lazy) {\n                this.lazyAutoSaveState()\n            } else {\n                this.autoSaveState()\n            }\n            this.showRendererElements()\n        },\n        resetToInitialState() {\n            cape.application.bus.$emit('loadConfiguratorCustomParams', {})\n            cape.application.bus.$emit('resetTabs')\n            cape.application.bus.$emit(\n                'resetPreviewParametersToInitialState',\n                this.isConfigurator\n            )\n            this.autoSaveState()\n        },\n        saveState() {\n            cape.application.bus.$emit('savePreviewParametersState', this.isConfigurator)\n        },\n        autoSaveState() {\n            if (this.isConfigurator) this.saveState()\n        },\n        lazyAutoSaveState: _.debounce(function() {\n            this.autoSaveState()\n        }, 300),\n        setState(index) {\n            cape.application.bus.$emit('setPreviewParametersState', index)\n        },\n        undo() {\n            cape.application.bus.$emit('previousPreviewParametersState')\n        },\n        redo() {\n            cape.application.bus.$emit('nextPreviewParametersState')\n        },\n        computeHeight(e) {\n            this.updateParam('decoderHeight', this.heightRange[e])\n        },\n        generateThumbnails() {\n            cape.application.bus.$emit('disableThumbnails')\n        },\n        updateParam: _.throttle(function(type, value) {\n            if (type === 'decoderWidth') {\n                value = value * 10\n            }\n            cape.application.bus.$emit('updateRenderer', type, value)\n            cape.application.bus.$emit('resetTabs')\n            cape.application.bus.$emit('resetPrevSelected')\n            this.generateThumbnails()\n        }, 100),\n    },\n\n    watch: {\n        decoderOutput(e) {\n            console.log(123123, e);\n            this.thumbnails = e.thumbnails ? e.thumbnails : null\n            this.additional_height = [0, 1].map(\n                v => e.additional_height[v] || 0\n            )\n            this.setupID = e.setup_id\n            this.components = e.components\n            this.lines = e.lines\n            this.densityOptions = e.density_options\n        },\n        distortion(value) {\n            this.choosenDistortion = value\n        },\n        depth(value) {\n            this.choosenDepth = value\n        },\n        plinth(value) {\n            this.choosenPlinth = value\n        },\n        locked(value) {\n            this.choosenLocked = value\n        },\n        density(value) {\n            this.choosenDensity = value\n        },\n        decoderWidth(value) {\n            this.choosenWidth = value / 10\n        },\n        decoderHeight(value) {\n            if (this.type === 'mesh') {\n                this.heightRange.forEach((val, i) => {\n                    if (val === +value) {\n                        this.heightIndex = i\n                    }\n                })\n                this.choosenHeight = this.heightRange[this.heightIndex] + ''\n            }\n        },\n        heightIndex(index) {\n            this.choosenHeight = this.heightRange[index] + ''\n        },\n        element() {\n            if (this.type === 'component') {\n                this.widthRange = [10, 100]\n            } else if (this.type === 'mesh') {\n                this.widthRange = this.element.dim_x\n                    .split('-')\n                    .map(val => +val / 10)\n                this.heightRange = this.element.dim_y\n                    .split(',')\n                    .map(val => +val)\n                this.densityMode = this.element.density_mode\n                this.distortionMode = this.element.distortion_mode\n                this.heightRange.forEach((val, i) => {\n                    if (val === +this.decoderHeight) {\n                        this.heightIndex = i\n                    }\n                })\n                this.choosenHeight = this.heightRange[this.heightIndex] + ''\n            }\n        },\n        parametersStatesStatus(value) {\n            this.currentParametersHistoryState = value[0]\n        },\n    },\n\n    mounted() {\n        this.choosenWidth = +this.decoderWidth / 10\n        this.choosenHeight = +this.decoderHeight\n        this.choosenDistortion = this.distortion\n        this.choosenDensity = this.density\n        this.choosenDepth = this.depth\n        this.choosenPlinth = this.plinth\n        this.choosenLocked = this.locked\n    },\n\n    data() {\n        return {\n            isConfigurator: this.$route.name === 'configurator',\n            widthRange: [1, 2], // Wartosc randomowa - zapobiega bledom podczas ladowania strony\n            choosenWidth: null,\n            choosenHeight: null,\n            heightIndex: null,\n            heightRange: [1, 2], // Wartosc randomowa - zapobiega bledom podczas ladowania strony\n            densityMode: null,\n            distortionMode: null,\n            setupID: null,\n            components: [],\n            lines: {},\n            choosenDensity: null,\n            choosenDistortion: null,\n            choosenDepth: null,\n            choosenPlinth: null,\n            additional_height: [0, 0], // Wartosc randomowa - zapobiega bledom podczas ladowania strony\n            choosenLocked: null,\n            thumbnails: null,\n            densityOptions: [],\n            currentParametersHistoryState: 0,\n            rendererTimeout: null,\n        }\n    },\n}\n</script>\n<style lang=\"scss\">\n.badania {\n    .q-field-label {\n        width: 81px;\n    }\n\n    .button-badania {\n        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05),\n            0 6px 20px 0 rgba(0, 0, 0, 0.1);\n        display: inline-block;\n        float: left;\n        margin-right: 15px;\n        border-radius: 7px;\n        overflow: hidden;\n        border: 2px solid transparent;\n        opacity: 0.7;\n        width: 90px;\n        &.small {\n            width: 40px;\n            .q-btn-inner {\n                font-size: 21px;\n            }\n            &.disable {\n                opacity: 0.4;\n                pointer-events: none;\n            }\n        }\n        &.active {\n            border: 2px solid #ff3c00;\n            opacity: 1;\n            .q-focus-helper {\n                background: transparent !important;\n            }\n        }\n        .q-focus-helper {\n            background: transparent !important;\n        }\n    }\n}\n\n.configuration-wrapper {\n    height: 100vh;\n    .text-primary {\n        color: #ff3c00 !important;\n    }\n    .card-title {\n        .q-card-title {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            line-height: 1.5rem;\n            .arrow-btn {\n                padding-left: 0;\n                padding-right: 0;\n                min-width: 10px;\n                i {\n                    margin-right: 0;\n                    margin: 0 6px;\n                }\n            }\n        }\n    }\n    .title {\n        display: block;\n        padding-bottom: 20px;\n        padding-top: 15vh;\n        font-size: 24px;\n        line-height: 28px;\n    }\n    .q-card {\n        //margin: 10px;\n        background: #ffffff;\n        position: relative;\n        margin-right: 76px;\n        //margin-top: 23%;\n    }\n    .link-button {\n        background-color: #ff3c00;\n        color: white;\n    }\n    .q-field-content {\n        padding-top: 0;\n    }\n    .q-btn-group {\n        .q-btn-item {\n            text-transform: capitalize;\n            min-width: 67px;\n        }\n    }\n    .q-btn-group-dense {\n        .q-btn-item {\n            text-transform: capitalize;\n            min-width: 27px;\n        }\n    }\n    .component-select {\n        margin-top: 22px;\n        .q-field-label-inner {\n            margin-top: 0 !important;\n        }\n    }\n    .separator {\n        border-bottom: 1px solid #d9dcdc;\n        width: calc(100% - 48px);\n        margin-left: 24px;\n    }\n    .toggle-button {\n        border-radius: 7px;\n        background: white !important;\n        box-shadow: none;\n        .bg-primary {\n            background: white !important;\n            color: #0c0c0c !important;\n            &:hover {\n                background: white !important;\n                .q-focus-helper {\n                    background: white;\n                }\n                color: #ff3c00 !important;\n            }\n            box-shadow: rgba(0, 0, 0, 0.1) 0px 0.5px 6px 1px;\n        }\n        .q-btn {\n            &:not(.bg-primary) {\n                background: #eff2f3;\n            }\n            &:first-child {\n                border-top-left-radius: 7px;\n                border-bottom-left-radius: 7px;\n            }\n            &:last-child {\n                border-top-right-radius: 7px;\n                border-bottom-right-radius: 7px;\n            }\n        }\n    }\n    .q-slider {\n        color: #ff3c00;\n        &:hover {\n            .q-slider-handle {\n                box-shadow: rgba(0, 0, 0, 0.4) 0px 0.5px 6px 1px;\n            }\n        }\n    }\n    .bg-primary {\n        background: #ff3c00 !important;\n    }\n    .q-field-label {\n        display: flex;\n        align-items: center;\n        color: #555;\n        .q-field-label-inner {\n            margin-top: 18px;\n        }\n    }\n    .q-slider-handle {\n        color: white;\n        transition: all 0.2s ease;\n        box-shadow: rgba(0, 0, 0, 0.28) 0px 0.5px 3px 1px;\n        .q-slider-ring {\n            display: none;\n        }\n    }\n    .settings-wrapper {\n        .q-field {\n            &:first-child {\n                //margin-top: 5px;\n                .q-field-label {\n                    display: flex;\n                }\n            }\n        }\n    }\n}\n\n.q-card-container {\n    padding: 24px;\n}\n\n.bradius {\n    border-radius: 6px;\n}\n\n.configurator-mode-hidden {\n    height: 0px;\n    overflow: hidden;\n    padding: 0;\n}\n\n.q-slider-handle.dragging {\n    //transform: translate3d(-50%, -50%, 0) scale(1);\n}\n</style>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewConfiguration.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewConfiguration.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoPreviewConfiguration.vue?vue&type=template&id=eb219b22&lang=pug&\"\nimport script from \"./TylkoPreviewConfiguration.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoPreviewConfiguration.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoPreviewConfiguration.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"controls\"},[_c('q-btn',{attrs:{\"round\":\"round\",\"icon\":\"straighten\"},on:{\"click\":_vm.showHideCompartments}},[_c('q-tooltip',{attrs:{\"anchor\":\"center left\",\"self\":\"center right\",\"offset\":[10, 10]}},[_vm._v(\"Open/close doors & drawers\")])],1),_c('q-btn',{attrs:{\"round\":\"round\",\"icon\":\"restore\"},on:{\"click\":_vm.resetChanges}},[_c('q-tooltip',{attrs:{\"anchor\":\"center left\",\"self\":\"center right\",\"offset\":[10, 10]}},[_vm._v(\"Reset changes\")])],1),_c('q-btn',{attrs:{\"round\":\"round\",\"icon\":\"undo\"},on:{\"click\":_vm.undo}},[_c('q-tooltip',{attrs:{\"anchor\":\"center left\",\"self\":\"center right\",\"offset\":[10, 10]}},[_vm._v(\"Undo\")])],1),_c('q-btn',{attrs:{\"round\":\"round\",\"icon\":\"redo\"},on:{\"click\":_vm.redo}},[_c('q-tooltip',{attrs:{\"anchor\":\"center left\",\"self\":\"center right\",\"offset\":[10, 10]}},[_vm._v(\"Redo\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n    div.controls\n        q-btn(round icon=\"straighten\" @click=\"showHideCompartments\")\n            q-tooltip(anchor=\"center left\" self=\"center right\" :offset=\"[10, 10]\") Open/close doors & drawers\n        q-btn(round icon=\"restore\" @click=\"resetChanges\")\n            q-tooltip(anchor=\"center left\" self=\"center right\" :offset=\"[10, 10]\") Reset changes\n        q-btn(round icon=\"undo\" @click=\"undo\")\n            q-tooltip(anchor=\"center left\" self=\"center right\" :offset=\"[10, 10]\") Undo\n        q-btn(round icon=\"redo\" @click=\"redo\")\n            q-tooltip(anchor=\"center left\" self=\"center right\" :offset=\"[10, 10]\") Redo\n</template>\n\n\n<script>\nimport { cape } from '@core/cape'\n\nexport default {\n    name: 'TylkoPreviewControls',\n    props: ['showAllCompartmentsButton'],\n\n    methods: {\n        showHideCompartments() {\n            cape.application.bus.$emit(\n                'showAllCompartmentsButton',\n                !this.showAllCompartmentsButton\n            )\n        },\n        resetChanges() {\n            cape.application.bus.$emit('loadConfiguratorCustomParams', {})\n            cape.application.bus.$emit('resetTabs')\n            cape.application.bus.$emit('resetPreviewParametersToInitialState', true)\n            cape.application.bus.$emit('savePreviewParametersState', true)\n        },\n        undo() {\n            cape.application.bus.$emit('previousPreviewParametersState')\n        },\n        redo() {\n            cape.application.bus.$emit('nextPreviewParametersState')\n        },\n    },\n\n    watch: {},\n\n    mounted() {},\n\n    data() {\n        return {\n            empty: true\n        }\n    },\n}\n</script>\n<style lang=\"scss\">\n.controls {\n    position: absolute;\n    width: 50px;\n    height: 400px;\n    top: 204px;\n    right: 100%;\n    .q-btn {\n        background: white;\n        margin-bottom: 12px;\n        &:focus {\n            .q-focus-helper {\n                background: white !important;\n            }\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewControls.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewControls.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoPreviewControls.vue?vue&type=template&id=e255c32e&lang=pug&\"\nimport script from \"./TylkoPreviewControls.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoPreviewControls.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoPreviewControls.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"pdp-2018 usps is-past-bottom is-in-view\",attrs:{\"id\":\"type02-usps\",\"data-scroll\":\"virtual\"}},[_c('div',{staticClass:\"dimensions-info\",class:{ visible: _vm.isVisible }},[_c('i',{staticClass:\"material-icons\"},[_vm._v(\"transform\")]),_c('span',[_vm._v(\"All dimensions are in centimeters (cm)\")])]),_c('div',{staticClass:\"grid no-bottom-gutter\"},[_vm._m(0),_c('div',{staticClass:\"grid-4 no-bottom-gutter rating text-center\"},[_c('div',{staticClass:\"grade-list\"},[_c('svg',{attrs:{\"width\":\"24px\",\"height\":\"21px\",\"viewBox\":\"0 0 24 21\",\"version\":\"1.1\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\"}},[_c('g',{attrs:{\"id\":\"Page-1\",\"stroke\":\"none\",\"stroke-width\":\"1\",\"fill\":\"none\",\"fill-rule\":\"evenodd\"}},[_c('polygon',{attrs:{\"id\":\"Star\",\"fill\":\"#FFB414\",\"points\":\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"}})])]),_c('svg',{attrs:{\"width\":\"24px\",\"height\":\"21px\",\"viewBox\":\"0 0 24 21\",\"version\":\"1.1\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\"}},[_c('g',{attrs:{\"id\":\"Page-1\",\"stroke\":\"none\",\"stroke-width\":\"1\",\"fill\":\"none\",\"fill-rule\":\"evenodd\"}},[_c('polygon',{attrs:{\"id\":\"Star\",\"fill\":\"#FFB414\",\"points\":\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"}})])]),_c('svg',{attrs:{\"width\":\"24px\",\"height\":\"21px\",\"viewBox\":\"0 0 24 21\",\"version\":\"1.1\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\"}},[_c('g',{attrs:{\"id\":\"Page-1\",\"stroke\":\"none\",\"stroke-width\":\"1\",\"fill\":\"none\",\"fill-rule\":\"evenodd\"}},[_c('polygon',{attrs:{\"id\":\"Star\",\"fill\":\"#FFB414\",\"points\":\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"}})])]),_c('svg',{attrs:{\"width\":\"24px\",\"height\":\"21px\",\"viewBox\":\"0 0 24 21\",\"version\":\"1.1\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\"}},[_c('g',{attrs:{\"id\":\"Page-1\",\"stroke\":\"none\",\"stroke-width\":\"1\",\"fill\":\"none\",\"fill-rule\":\"evenodd\"}},[_c('polygon',{attrs:{\"id\":\"Star\",\"fill\":\"#FFB414\",\"points\":\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"}})])]),_c('svg',{attrs:{\"width\":\"24px\",\"height\":\"21px\",\"viewBox\":\"0 0 24 21\",\"version\":\"1.1\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\"}},[_c('g',{attrs:{\"id\":\"Page-1\",\"stroke\":\"none\",\"stroke-width\":\"1\",\"fill\":\"none\",\"fill-rule\":\"evenodd\"}},[_c('polygon',{attrs:{\"id\":\"Star\",\"fill\":\"#FFB414\",\"points\":\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"}})])]),_c('span',{staticClass:\"score\"},[_vm._v(\"4.7\")])]),_vm._m(1)]),_vm._m(2)])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"grid-4 no-bottom-gutter\"},[_c('img',{attrs:{\"src\":\"https://tylko.com/r_static/basic-pdp/ic_b_free_delivery.710019919939897397.svg\",\"width\":\"48\",\"height\":\"48\",\"alt\":\"\"}}),_c('div',{staticClass:\"text-slot text-center\"},[_c('h4',{staticClass:\"th-5 text-center\"},[_vm._v(\"Free delivery\")]),_c('p',{staticClass:\"pdp-2018-tooltip-wrapper tp-small tfc-gray text-center\"},[_vm._v(\"\\n          anywhere in the EU\\n          \"),_c('span',{staticClass:\"question-mark pdp-2018-tooltip-trigger\",attrs:{\"data-tooltip-trigger\":\"freeDelivery\"}},[_c('span',{staticClass:\"pdp-2018-tooltip tp-small tfc-gray\",attrs:{\"id\":\"freeDelivery\"}},[_vm._v(\"Enjoy free professional delivery – right to your doorstep. Your shelf will arrive in labelled flat pack boxes for quick, easy assembly. We ship to all EU countries, including Switzerland and Norway.\")])])])])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"text-slot rating\"},[_c('h4',{staticClass:\"th-5 text-center\"},[_vm._v(\"Customer rating\")]),_c('p',{staticClass:\"tp-small tfc-gray text-center\"},[_vm._v(\"based on 1804 reviews\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"grid-4 no-bottom-gutter\"},[_c('img',{attrs:{\"src\":\"https://tylko.com/r_static/basic-pdp/ic_b_free_returns.292403674054.svg\",\"width\":\"48\",\"height\":\"48\",\"alt\":\"\"}}),_c('div',{staticClass:\"text-slot\"},[_c('h4',{staticClass:\"th-5 text-center\"},[_vm._v(\"100 days to fall in love\")]),_c('p',{staticClass:\"pdp-2018-tooltip-wrapper tp-small tfc-gray text-center\"},[_vm._v(\"\\n          or return it for free\\n          \"),_c('span',{staticClass:\"question-mark pdp-2018-tooltip-trigger\",attrs:{\"data-tooltip-trigger\":\"freeReturns\"}},[_c('span',{staticClass:\"pdp-2018-tooltip tp-small tfc-gray\",attrs:{\"id\":\"freeReturns\"}},[_vm._v(\"If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days and give you a full refund. No questions asked.\")])])])])])}]\n\nexport { render, staticRenderFns }", "<template>\n  <section class=\"pdp-2018 usps is-past-bottom is-in-view\" id=\"type02-usps\" data-scroll=\"virtual\">\n    <div class=\"dimensions-info\" :class=\"{ visible: isVisible }\">\n      <i class=\"material-icons\">transform</i>\n      <span>All dimensions are in centimeters (cm)</span>\n    </div>\n    <div class=\"grid no-bottom-gutter\">\n      <div class=\"grid-4 no-bottom-gutter\">\n        <img\n          src=\"https://tylko.com/r_static/basic-pdp/ic_b_free_delivery.710019919939897397.svg\"\n          width=\"48\"\n          height=\"48\"\n          alt\n        >\n        <div class=\"text-slot text-center\">\n          <h4 class=\"th-5 text-center\">Free delivery</h4>\n          <p class=\"pdp-2018-tooltip-wrapper tp-small tfc-gray text-center\">\n            anywhere in the EU\n            <span\n              class=\"question-mark pdp-2018-tooltip-trigger\"\n              data-tooltip-trigger=\"freeDelivery\"\n            >\n              <span\n                class=\"pdp-2018-tooltip tp-small tfc-gray\"\n                id=\"freeDelivery\"\n              >Enjoy free professional delivery – right to your doorstep. Your shelf will arrive in labelled flat pack boxes for quick, easy assembly. We ship to all EU countries, including Switzerland and Norway.</span>\n            </span>\n          </p>\n        </div>\n      </div>\n      <div class=\"grid-4 no-bottom-gutter rating text-center\">\n        <div class=\"grade-list\">\n          <svg\n            width=\"24px\"\n            height=\"21px\"\n            viewBox=\"0 0 24 21\"\n            version=\"1.1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n          >\n            <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n              <polygon\n                id=\"Star\"\n                fill=\"#FFB414\"\n                points=\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"\n              ></polygon>\n            </g>\n          </svg>\n          <svg\n            width=\"24px\"\n            height=\"21px\"\n            viewBox=\"0 0 24 21\"\n            version=\"1.1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n          >\n            <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n              <polygon\n                id=\"Star\"\n                fill=\"#FFB414\"\n                points=\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"\n              ></polygon>\n            </g>\n          </svg>\n          <svg\n            width=\"24px\"\n            height=\"21px\"\n            viewBox=\"0 0 24 21\"\n            version=\"1.1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n          >\n            <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n              <polygon\n                id=\"Star\"\n                fill=\"#FFB414\"\n                points=\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"\n              ></polygon>\n            </g>\n          </svg>\n          <svg\n            width=\"24px\"\n            height=\"21px\"\n            viewBox=\"0 0 24 21\"\n            version=\"1.1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n          >\n            <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n              <polygon\n                id=\"Star\"\n                fill=\"#FFB414\"\n                points=\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"\n              ></polygon>\n            </g>\n          </svg>\n          <svg\n            width=\"24px\"\n            height=\"21px\"\n            viewBox=\"0 0 24 21\"\n            version=\"1.1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n          >\n            <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n              <polygon\n                id=\"Star\"\n                fill=\"#FFB414\"\n                points=\"12 16.445 4.94657697 20.8036954 7.09254838 13.028089 0.587321804 7.94630456 8.9670281 7.49941096 12 0 15.0329719 7.49941096 23.4126782 7.94630456 16.9074516 13.028089 19.053423 20.8036954\"\n              ></polygon>\n            </g>\n          </svg>\n          <span class=\"score\">4.7</span>\n        </div>\n        <div class=\"text-slot rating\">\n          <h4 class=\"th-5 text-center\">Customer rating</h4>\n          <p class=\"tp-small tfc-gray text-center\">based on 1804 reviews</p>\n        </div>\n      </div>\n      <div class=\"grid-4 no-bottom-gutter\">\n        <img\n          src=\"https://tylko.com/r_static/basic-pdp/ic_b_free_returns.292403674054.svg\"\n          width=\"48\"\n          height=\"48\"\n          alt\n        >\n        <div class=\"text-slot\">\n          <h4 class=\"th-5 text-center\">100 days to fall in love</h4>\n          <p class=\"pdp-2018-tooltip-wrapper tp-small tfc-gray text-center\">\n            or return it for free\n            <span\n              class=\"question-mark pdp-2018-tooltip-trigger\"\n              data-tooltip-trigger=\"freeReturns\"\n            >\n              <span\n                class=\"pdp-2018-tooltip tp-small tfc-gray\"\n                id=\"freeReturns\"\n              >If for any reason you’re not happy with your shelf, we’ll pick it back up for free within 100 days and give you a full refund. No questions asked.</span>\n            </span>\n          </p>\n        </div>\n      </div>\n    </div>\n  </section>\n</template>\n\n<script>\nexport default {\n    props: ['isVisible'],\n    data() {\n        return {\n            isVisible: false\n        }\n    },\n    name: 'TylkoUsps',\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dimensions-info {\n    position: absolute;\n    top: -40px;\n    left: 50%;\n    transform: translateX(-50%);\n    color: #7c7d81;\n    opacity: 0;\n    visibility: hidden;\n    transition: opacity 0.3s ease-in;\n    &.visible {\n        opacity: 1;\n        visibility: visible;\n    }\n    span {\n        margin-left: 8px;\n        color: #7c7d81;\n        font-size: 16px;\n    }\n    i {\n        position: relative;\n        top: -2px;\n    }\n}\n\n.usps {\n    position: absolute !important;\n    bottom: -50px;\n    left: 0;\n    width: 100%;\n    z-index: 1;\n}\n\n@import '~@theme/tylko/styles-from-tylko-com.scss';\n\n.usps {\n    position: relative;\n    z-index: 2;\n    @media screen and (max-width: $size-desktop-min - 1px) {\n        background-color: #fff;\n    }\n    @media screen and (min-width: $size-desktop-min) {\n        padding: $ts-l 0;\n        h4 {\n            line-height: 22px;\n            margin: 0;\n        }\n    }\n    p {\n        padding-top: 0;\n    }\n    ul {\n        padding-top: 8px;\n        li {\n            //@include pdp-2018-h2($color-gray);\n            position: relative;\n            padding: 12px 0;\n            line-height: 24px;\n            padding-left: 40px;\n            border-top: 1px solid $color-gray-background;\n            &:last-child {\n                border-bottom: 1px solid $color-gray-background;\n            }\n            .arrow-right {\n                position: absolute;\n                display: block;\n                top: 18px;\n                right: 0;\n                transform: rotate(-45deg);\n                width: 6px;\n                height: 6px;\n                text-indent: -9999px;\n                border-color: $color-gray;\n                border-bottom: 1px solid;\n                border-right: 1px solid;\n            }\n            &:not(:first-child) {\n                &:after {\n                    background-image: url('https://tylko.com/r_static/basic-pdp/right-black.svg');\n                    width: 5px;\n                    height: 9px;\n                    background-size: 100%;\n                    content: '';\n                    position: absolute;\n                    top: 19px;\n                    right: 0;\n                    fill: black;\n                }\n            }\n\n            &:before {\n                position: absolute;\n                width: 32px;\n                left: 0;\n                height: 32px;\n                content: '';\n                top: 8px;\n                background-size: 100%;\n                background-repeat: no-repeat;\n            }\n            &.item {\n                &_1 {\n                    &:before {\n                        background-image: url('https://tylko.com/r_static/basic-pdp/usps-icons/1.svg');\n                    }\n                }\n                &_2 {\n                    &:before {\n                        background-image: url('https://tylko.com/r_static/basic-pdp/usps-icons/2.svg');\n                    }\n                }\n                &_3 {\n                    &:before {\n                        background-image: url('https://tylko.com/r_static/basic-pdp/ic_free_delivery.svg');\n                    }\n                }\n                &_4 {\n                    &:before {\n                        background-image: url('https://tylko.com/r_static/basic-pdp/ic_free_returns.svg');\n                    }\n                }\n            }\n        }\n    }\n    // should have media query\n    background: $color-gray-background;\n    @media screen and (min-width: $size-desktop-min) {\n        background: $color-bg;\n    }\n    img {\n        margin: 0 auto;\n    }\n    .grade-list {\n        margin-top: 18px;\n        height: 20px;\n        svg {\n            margin: 0px 5px;\n        }\n        .score {\n            margin-left: 5px;\n            color: $color-black;\n            font-size: 20px;\n            font-family: $font-heading;\n            line-height: 28px;\n            position: relative;\n            top: -1px;\n        }\n    }\n    .text-slot {\n        text-align: center;\n        margin-top: $ts-s;\n        &.rating {\n            margin-top: $ts-m;\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoUsps.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoUsps.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoUsps.vue?vue&type=template&id=96c8332e&scoped=true&\"\nimport script from \"./TylkoUsps.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoUsps.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoUsps.vue?vue&type=style&index=0&id=96c8332e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"96c8332e\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _vm._m(0)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('a',{staticClass:\"nav\",attrs:{\"href\":\"https://preview.uxpin.com/c9a95a25baed4bea4958c4942cdff85af8a8b0db#/pages/106584156/simulate/no-panels?mode=cvhdmf\"}},[_c('div',{staticClass:\"logo\"},[_vm._v(\"Tylko\")]),_c('ul',[_c('li',[_vm._v(\"Shelves\")]),_c('li',[_vm._v(\"Reviews\")]),_c('li',[_vm._v(\"Our Mission\")]),_c('li',[_vm._v(\"App\")]),_c('li',{staticClass:\"wishlist\"},[_c('img',{staticClass:\"menu-icon\",attrs:{\"width\":\"25\",\"height\":\"21\",\"src\":\"https://tylko.com/r_static/icons/icon-heart-3.3621101997981029856.svg\",\"alt\":\"Wishlist\"}})]),_c('li',{staticClass:\"cart\"},[_c('img',{staticClass:\"menu-icon\",attrs:{\"width\":\"25\",\"height\":\"21\",\"src\":\"https://tylko.com/r_static/icons/icon-cart.100724389979834.svg\",\"alt\":\"Cart\"}})])])])}]\n\nexport { render, staticRenderFns }", "<template>\n  <a\n    class=\"nav\"\n    href=\"https://preview.uxpin.com/c9a95a25baed4bea4958c4942cdff85af8a8b0db#/pages/106584156/simulate/no-panels?mode=cvhdmf\"\n  >\n    <div class=\"logo\">Tylko</div>\n    <ul>\n      <li>Shelves</li>\n      <li>Reviews</li>\n      <li>Our Mission</li>\n      <li>App</li>\n      <li class=\"wishlist\">\n        <img\n          class=\"menu-icon\"\n          width=\"25\"\n          height=\"21\"\n          src=\"https://tylko.com/r_static/icons/icon-heart-3.3621101997981029856.svg\"\n          alt=\"Wishlist\"\n        >\n      </li>\n      <li class=\"cart\">\n        <img\n          class=\"menu-icon\"\n          width=\"25\"\n          height=\"21\"\n          src=\"https://tylko.com/r_static/icons/icon-cart.100724389979834.svg\"\n          alt=\"Cart\"\n        >\n      </li>\n    </ul>\n  </a>\n</template>\n\n<script>\nexport default {\n    name: 'TylkoMenu',\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.nav {\n    display: flex;\n    position: fixed;\n    width: 0;\n    top: 0;\n    left: 0;\n    height: 68px;\n    padding: 0 76px;\n    background: #ffffff;\n    z-index: 1;\n    line-height: 68px;\n    width: 100%;\n    color: #7c7d81;\n    .logo {\n        width: 81px;\n        overflow: hidden;\n        height: 43px;\n        margin: 13px 0 0 0;\n        text-indent: -9999px;\n        display: block;\n        background: url('https://tylko.com/r_static/dist/images/tylko-logo.svg')\n            center center no-repeat;\n        background-size: contain;\n    }\n    ul {\n        margin: 0 0 0 48px;\n        width: 100%;\n        position: relative;\n        li {\n            display: inline-block;\n            margin-right: 20px;\n            &.wishlist,\n            &.cart {\n                align-self: flex-end;\n                position: absolute;\n                right: 0;\n                img {\n                    position: relative;\n                    top: 3px;\n                }\n            }\n            &.wishlist {\n                right: 35px;\n            }\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMenu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMenu.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoMenu.vue?vue&type=template&id=6ac1de8d&scoped=true&\"\nimport script from \"./TylkoMenu.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoMenu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoMenu.vue?vue&type=style&index=0&id=6ac1de8d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6ac1de8d\",\n  null\n  \n)\n\nexport default component.exports", "<template lang=\"pug\">\n    .flex.row.preview-wrapper(ref=\"rendererContainer\")\n        TylkoUsps(\n        v-if=\"this.$route.name === 'configurator'\"\n        :isVisible=\"visibleUspsText\"\n        )\n        TylkoMenu(v-if=\"this.$route.name === 'configurator'\")\n        div.renderer-wrapper(@mouseover=\"rendererMouseOver\" @mouseleave=\"rendererMouseLeave\")\n            q-spinner(color=\"blue\" size=\"50px\" class=\"spinner\" v-if=\"!loaderHidden\")\n            div.production-renderer-canvas.load-hidden(ref=\"rendererWapper\")\n            div.mapped\n                div.mapped-container(v-for=\"(position, no) in mappedObjects\")\n                    div.cont(:style=\"`position: absolute; top: ${position.y}px; left: ${position.x}px;}`\")\n                        div(style=\"z-index: 99;\", :class=\"[no+1 == $refs.configuration.$refs.previewSelect.selectedTab ? 'comp-selected' : '' ]\")\n                            q-btn(\n                            :style=\"$refs.configuration.$refs.previewSelect.selectedTab != 0 && no+1 != $refs.configuration.$refs.previewSelect.selectedTab ? 'opacity: 0' : '' \"\n                            :class=\"[ hoverElement == no ? 'hoverState' : '', 'tylko-button' ]\"\n                            @mouseleave.native=\"selectOnLeave\"\n                            @mouseover.native=\"selectOnEnter(no)\"\n                            @mousedown.native=\"select(no)\", round, dense, color=\"white\", size=\"md\", icon=\"tune\"\n                            )\n                                span(class=\"adjust\") {{ no+1 == $refs.configuration.$refs.previewSelect.selectedTab || hoverElement == no ? 'Adjust' : ''   }}\n                    div.compartments-heights-container(v-for=\"comp in compartmentsHeights\")\n                        div.compartment.compartment-height(:style=\"`position: absolute; transform: translate(${comp.point.x}px, ${comp.point.y}px)`\") {{Math.floor(comp.value/10)}}\n                    div.compartments-widths-container(v-for=\"comp in compartmentsWidths\")\n                        div.compartment.compartment-width(:style=\"`position: absolute; transform: translate(${comp.point.x}px, ${comp.point.y}px)`\") {{Math.floor(comp.value/10)}}\n        div(style=\"position: relative; height: 100vh; width: 30%\")\n            TylkoPreviewControls(\n            :showAllCompartmentsButton=\"showAllCompartmentsButton\"\n            )\n            TylkoPreviewConfiguration(\n            :type=\"decoderType\"\n            :element=\"element\"\n            :grabButtons=\"grabButtons\",\n            :objectLine=\"objectLine\",\n            :choosenColor=\"choosenColor\"\n            :decoderWidth=\"decoderWidth\"\n            :decoderHeight=\"decoderHeight\"\n            :decoderOutput=\"decoderOutput\"\n            :distortion=\"distortion\",\n            :density=\"density\",\n            :depth=\"depth\",\n            :plinth=\"plinth\",\n            :mappedObjects=\"mappedObjects\",\n            :price=\"price\",\n            :locked=\"locked\",\n            :previousStateAvailble=\"previousStateAvailble\",\n            :nextStateAvailble=\"nextStateAvailble\",\n            :parametersStatesStatus=\"parametersStatesStatus\",\n            ref=\"configuration\"\n            )\n\n</template>\n\n<style lang=\"scss\">\n.show-elements {\n    .compartment,\n    .component-select-wrapper,\n    .cont,\n    #button-grab-left,\n    #button-grab-right {\n        display: block;\n    }\n}\n\n.show-compartment {\n    .compartment {\n        display: block;\n    }\n}\n\n.component-select-wrapper,\n.compartment,\n.cont,\n#button-grab-left,\n#button-grab-right {\n    display: none;\n}\n\n.compartment {\n    background-color: white;\n    font-size: 14px;\n    padding: 3px 4px;\n    border-radius: 15px;\n    line-height: 1;\n    text-align: center;\n    min-width: 32px;\n    /*box-shadow: rgba(0, 0, 0, 0.03) 0px 0.5px 3px 1px;*/\n}\n\n.compartment-width {\n    background-color: rgba(#edf0f0, 0.4);\n    color: #4a4a4a;\n    margin-top: -30px;\n    margin-left: -16px;\n}\n\n.compartment-height {\n    background-color: rgba(#4a4a4a, 0.4);\n    color: #ffffff;\n    margin-top: -15px;\n    margin-left: 8px;\n}\n\n.q-btn-dense {\n    transition: background 0.3s ease;\n    i {\n        transition: color 0.3s ease;\n    }\n}\n\n.hoverState.tylko-button {\n    width: 93px !important;\n    background-color: white !important;\n    border-radius: 24px;\n    i {\n        margin-right: 4px;\n        color: black;\n    }\n    .adjust {\n        opacity: 1 !important;\n        color: black !important;\n    }\n}\n\n.cont {\n    .comp-selected {\n        .tylko-button {\n            width: 93px !important;\n            border-radius: 24px;\n            background: #ff3c00 !important;\n            &:hover {\n                background: #ffe6df !important;\n                .adjust {\n                    color: #ff3c00 !important;\n                }\n                i {\n                    color: #ff3c00;\n                }\n            }\n            i {\n                color: white;\n                margin-right: 4px;\n            }\n            .adjust {\n                opacity: 1 !important;\n                color: white !important;\n            }\n        }\n\n        z-index: 1000 !important;\n    }\n}\n\n.tylko-button {\n    transition: all 0.3s ease;\n    width: auto;\n    min-width: 2.4em;\n    &:hover {\n        background: #ffe6df !important;\n        .adjust {\n            color: #ff3c00 !important;\n        }\n        i {\n            color: #ff3c00;\n        }\n        .bg-white {\n            background: #ff3c00 !important;\n        }\n    }\n    .adjust {\n        transition: opacity 0.3s ease 0.2s !important;\n        color: white !important;\n        opacity: 0;\n    }\n    .q-btn-inner {\n        flex-wrap: nowrap;\n        justify-content: flex-start;\n    }\n    i {\n        color: black;\n        margin-left: 7px;\n    }\n}\n\ndiv.mapped {\n    position: absolute;\n    top: 0px;\n    .mapped-container {\n        .cont {\n            transform: translateX(-50%);\n        }\n    }\n}\n\n.renderer-wrapper {\n    position: relative;\n    width: 70%;\n    .production-renderer-canvas {\n        transition: opacity 0.2s ease;\n        opacity: 1;\n        &.load-hidden {\n            opacity: 0;\n        }\n    }\n\n    .spinner {\n        position: absolute;\n        top: 50%;\n        left: 50%;\n    }\n}\n\n.component-select {\n    margin-top: 0;\n}\n\n.preview-wrapper {\n    background: #f0f0f0;\n    .component-select .q-input-target,\n    .component-select .q-input-shadow {\n        color: black;\n    }\n}\n</style>\n\n<script>\nimport _ from 'lodash'\nimport { cape } from '@core/cape'\nimport { getGeometry } from 'dna/dnaToolsWD.js'\nimport { ProductionRenderer } from 'renderers/wireframe-3d/simple.js'\nimport { WebdesignerRenderer } from 'configurator/ivy/webdesigner.js'\nimport TylkoPreviewConfiguration from './TylkoPreviewConfiguration'\nimport TylkoPreviewControls from './TylkoPreviewControls'\nimport TylkoUsps from './fakeTylkoUi/TylkoUsps'\nimport TylkoMenu from './fakeTylkoUi/TylkoMenu'\n\n// import TylkoBar from 'components/tylko/menu/TylkoElementsBar';\nexport default {\n    name: 'PagePreview',\n    components: {\n        TylkoPreviewConfiguration,\n        TylkoPreviewControls,\n        TylkoUsps,\n        TylkoMenu,\n    },\n\n    methods: {\n        innitialize() {\n            let startingParams = {}\n            if (this.$route.params.startingParams) {\n                this.$route.params.startingParams.split('&').forEach(prop => {\n                    let [k, v] = prop.split('=')\n                    v =\n                        typeof v === 'undefined' || v === 'on'\n                            ? true\n                            : v === 'off'\n                            ? false\n                            : isNaN(v)\n                            ? v\n                            : Number(v)\n                    startingParams[k] = v\n                })\n            }\n            if (this.decoderType === 'component') {\n                this.decoderWidth = _.get(startingParams, 'width', 500)\n            } else if (this.decoderType === 'mesh') {\n                this.decoderWidth = _.get(\n                    startingParams,\n                    'width',\n                    this.element.dim_x.split('-').map(val => +val)[0]\n                )\n                this.decoderHeight = _.get(\n                    startingParams,\n                    'height',\n                    this.element.dim_y.split(',').map(val => +val)[0]\n                )\n            }\n            this.choosenColor = _.get(startingParams, 'color', '0:0')\n            this.density = _.get(startingParams, 'density', 0)\n            this.depth = _.get(startingParams, 'depth', 320)\n            this.plinth = _.get(startingParams, 'plinth', true)\n            this.distortion = _.get(startingParams, 'distortion', 0)\n\n            this.saveCurrentPreviewParametersState(false, 0)\n            if (!this.isConfigurator) {\n                this.loadFromStorage()\n                this.saveCurrentPreviewParametersState(true)\n            }\n            if (this.isConfigurator) this.locked = true\n            this.update()\n            this.initRenderer()\n            if (this.isConfigurator) {\n                document.getElementsByClassName(\n                    'menu-column'\n                )[0].style.display = 'none'\n            }\n            cape.application.bus.$on('showCompartments', () => {\n                this.showCompartments()\n            })\n            cape.application.bus.$on('hideCompartments', () => {\n                this.hideCompartments()\n            })\n            cape.application.bus.$on('showRendererElements', () => {\n                this.$refs.rendererContainer.classList.add('show-elements')\n            })\n            cape.application.bus.$on('hideRendererElements', () => {\n                this.$refs.rendererContainer.classList.remove('show-elements')\n            })\n            cape.application.bus.$on('clearPrevSelectedTimeout', () => {\n                window.clearTimeout(this.timeoutHandle)\n            })\n            cape.application.bus.$on('resetPrevSelected', () => {\n                this.prevSelected = null\n            })\n            cape.application.bus.$on('triggerPrevSelected', () => {\n                if (this.$route.name === 'configurator') {\n                    this.selectOnLeave()\n                }\n            })\n            cape.application.bus.$on(\n                'dispatchConfiguratorCustomParams',\n                payload => {\n                    this.configuratorCustomParams = payload\n                    this.update()\n                }\n            )\n            cape.application.bus.$on('changeSetupTabIndex', payload => {\n                this.selectedSetupTab = payload\n                this.update()\n            })\n            cape.application.bus.$on('thumbsForChannel', payload => {\n                this.thumbsForChannel = payload\n            })\n            cape.application.bus.$on('changeElemnets', payload => {\n                this.elements = payload\n                this.update()\n            })\n            cape.application.bus.$on('generateThumbnails', () => {\n                this.generateThumbnails = true\n            })\n            cape.application.bus.$on('disableThumbnails', () => {\n                this.generateThumbnails = false\n            })\n            cape.application.bus.$on('updateRenderer', (type, value) => {\n                if (!this.hasOwnProperty(type)) return\n                this[type] = value\n                if (type === 'choosenColor' && this.isConfigurator)\n                    this.saveCurrentPreviewParametersState(true)\n                this.updatePreviewStore(type, value)\n                if (type === 'locked') {\n                    this.initRenderer()\n                    this.switchElements()\n                } else {\n                    this.update()\n                }\n            })\n            cape.application.bus.$on(\n                'resetPreviewParametersToInitialState',\n                save => {\n                    if (!this.previewParametersStates) {\n                        console.log('---- Brak stanu początkowego!')\n                        this.updateStatesAvailbility()\n                        return\n                    }\n                    this.loadPreviewParametersState(\n                        this.previewParametersStates[0]\n                    )\n                    // this.configuratorCustomParams = null;\n                    // this.decoderWidth = this.element.dim_x.split('-').map(val => +val)[0];\n                    // this.decoderHeight = this.element.dim_y.split(',').map(val => +val)[0];\n                    // this.distortion = 0;\n                    // this.density = 0;\n                    // this.depth = 320;\n                    // this.plinth = true;\n                    // this.choosenColor = '0:0';\n                    if (save) this.saveCurrentPreviewParametersState(true)\n                    this.update()\n                }\n            )\n            cape.application.bus.$on(\n                'savePreviewParametersState',\n                linearHistory => {\n                    this.saveCurrentPreviewParametersState(linearHistory)\n                }\n            )\n            cape.application.bus.$on('previousPreviewParametersState', () => {\n                this.loadPreviousPreviewParametersState()\n            })\n            cape.application.bus.$on('nextPreviewParametersState', () => {\n                this.loadNextPreviewParametersState()\n            })\n            cape.application.bus.$on(\n                'setPreviewParametersState',\n                stateIndex => {\n                    if (\n                        !this.previewParametersStates ||\n                        this.previewParametersStates.length === 0 ||\n                        stateIndex < 0 ||\n                        stateIndex > this.previewParametersStates.length - 1\n                    ) {\n                        console.log('---- Niepoprawny index!')\n                        this.updateStatesAvailbility()\n                        return\n                    }\n                    this.currentPreviewParametersState = stateIndex\n                    this.loadPreviewParametersState(\n                        this.previewParametersStates[stateIndex]\n                    )\n                }\n            )\n            cape.application.bus.$on('setConfiguratorType', value => {\n                this.isConfigurator = value\n            })\n\n            cape.application.bus.$on('showAllCompartmentsButton', value => {\n                this.showAllCompartmentsButton = value\n            })\n\n            this.$refs.configuration.$refs.previewSelect.mapEdges = this.edgesToScreen\n\n            this.mapCompartments(this.decoderOutput)\n            if (!this.showAllCompartmentsButton) {\n                this.showAllCompartments = false\n                this.$refs.rendererContainer.classList.remove(\n                    'show-compartment'\n                )\n            } else {\n                this.showAllCompartments = true\n                this.$refs.rendererContainer.classList.add('show-compartment')\n            }\n            this.visibleUsps()\n        },\n        visibleUsps() {\n            console.log(\n                123,\n                this.$refs.configuration.$refs.previewSelect.selectedTab,\n                this.showAllCompartments,\n                this.$refs.configuration.$refs.previewSelect.selectedTab != 0 ||\n                    this.showAllCompartments\n            )\n            this.visibleUspsText =\n                this.$refs.configuration.$refs.previewSelect.selectedTab != 0 ||\n                this.showAllCompartments\n        },\n        showCompartments() {\n            this.showAllCompartments = true\n            this.$refs.rendererContainer.classList.add('show-compartment')\n        },\n        hideCompartments() {\n            if (!this.showAllCompartmentsButton) {\n                this.showAllCompartments = false\n                this.$refs.rendererContainer.classList.remove(\n                    'show-compartment'\n                )\n            }\n        },\n        arrageComponentsPopovers() {\n            console.log('box-list', this.boxList.map(this.objectToScreen))\n            this.mappedObjects = this.boxList.map(this.objectToScreen)\n        },\n        selectOnLeave() {\n            this.timeoutHandle = window.setTimeout(() => {\n                console.log(123, 'selectOnLeave')\n                // this.$refs.configuration.$refs.previewSelect.selectedTab = this.prevSelected;\n                this.hoverElement = null\n                this.currentRenderer.renderer.domElement.style.cursor = 'auto'\n            }, 500)\n        },\n        selectOnEnter(no) {\n            window.clearTimeout(this.timeoutHandle)\n            this.hoverElement = no\n            this.currentRenderer.renderer.domElement.style.cursor = 'pointer'\n            if (\n                this.$refs.configuration.$refs.previewSelect.selectedTab == '0'\n            ) {\n                // this.$refs.configuration.$refs.previewSelect.selectedTab = \"\" + (no + 1);\n            } else {\n            }\n        },\n        select(no) {\n            if (\n                '' + (no + 1) ===\n                this.$refs.configuration.$refs.previewSelect.selectedTab\n            ) {\n                this.$refs.configuration.$refs.previewSelect.selectedTab = '0'\n                return\n            }\n            this.$refs.configuration.$refs.previewSelect.selectedTab =\n                '' + (no + 1)\n            // this.prevSelected = \"\" + (no + 1);\n            this.mapCompartments(this.decoderOutput)\n            this.buildResizers(this.decoderOutput)\n        },\n        rendererMouseOver() {\n            this.$refs.rendererContainer.classList.add('show-elements')\n        },\n        rendererMouseLeave() {\n            this.rendererLeaveTimeout = window.setTimeout(() => {\n                this.$refs.rendererContainer.classList.remove('show-elements')\n            }, 100)\n        },\n        previewSetComponent(no) {\n            this.select(no)\n        },\n        mouseMoveSetComponent(no) {\n            this.selectOnEnter(no)\n        },\n        mouseMoveOnLeave() {\n            this.selectOnLeave()\n        },\n        deselectComponent() {\n            this.deselect()\n        },\n        deselect() {\n            this.select(-1)\n        },\n        compartmentsToScreen(object) {\n            var canvas = this.currentRenderer.renderer.domElement\n            let w = parseFloat(canvas.style.width),\n                h = parseFloat(canvas.style.height)\n\n            var point = new THREE.Vector3()\n            point.set(object.x, object.y, object.z)\n            point.applyAxisAngle(new THREE.Vector3(0, 1, 0), 0.5)\n            // ndc pinhole\n            point.project(this.currentRenderer.camera)\n            point.x = Math.round(((point.x + 1) * w) / 2)\n            point.y = Math.round(((-point.y + 1) * h) / 2)\n            point.z = 0\n\n            return { value: object.value, point }\n        },\n\n        edgesToScreen(object, reverseForRaycasting, point) {\n            let initialRotationRad = 0.5\n            var canvas = this.currentRenderer.renderer.domElement\n            let w = parseFloat(canvas.style.width),\n                h = parseFloat(canvas.style.height)\n            var point = new THREE.Vector3()\n            point.set(object.x, object.y, 320)\n            point.applyAxisAngle(new THREE.Vector3(0, 1, 0), initialRotationRad)\n\n            // ndc pinhole\n            console.log('e2s', point, w, h)\n            if (reverseForRaycasting) {\n                let direction = new THREE.Vector3()\n                direction.set(\n                    (object.x / w) * 2 - 1,\n                    -(object.y / h) * 2 + 1,\n                    0.5\n                )\n                direction.unproject(this.currentRenderer.camera)\n                //   point.applyAxisAngle(new THREE.Vector3(0, 1, 0), -initialRotationRad/2);\n\n                direction.sub(this.currentRenderer.camera.position).normalize()\n                let ray = new THREE.Ray(\n                    this.currentRenderer.camera.position,\n                    direction\n                )\n                let result = new THREE.Vector3()\n                ray.closestPointToPoint(new THREE.Vector3(0, 0, 0), result)\n                return result\n            } else {\n                point.project(this.currentRenderer.camera)\n\n                point.x = Math.round(((point.x + 1) * w) / 2)\n                point.y = Math.round(((-point.y + 1) * h) / 2)\n                point.z = 0\n            }\n\n            return point\n        },\n\n        objectToScreen(object) {\n            // move object up\n            object.y = 0\n            var canvas = this.currentRenderer.renderer.domElement\n            let w = parseFloat(canvas.style.width),\n                h = parseFloat(canvas.style.height)\n\n            var point = new THREE.Vector3()\n            point.set(object.x, object.y, object.z)\n            point.applyAxisAngle(new THREE.Vector3(0, 1, 0), 0.5)\n            // ndc pinhole\n            point.project(this.currentRenderer.camera)\n            point.x = Math.round(((point.x + 1) * w) / 2)\n            point.y = Math.round(((-point.y + 1) * h) / 2)\n            point.z = 0\n            return point\n        },\n\n        update() {\n            let obj = {\n                type: this.decoderType,\n                id: this.decoderId,\n                width: this.decoderWidth,\n                height: this.decoderHeight,\n                mode: this.mode,\n                elements: this.elements,\n            }\n            if (this.decoderType === 'mesh') {\n                cape.api.geo.mesh(obj).pipe(\n                    this.processGeometry,\n                    'preview'\n                )\n            } else if (this.decoderType === 'component') {\n                cape.api.geo.component(obj).pipe(\n                    this.processGeometry,\n                    'preview'\n                )\n            }\n        },\n        updatePreviewStore(type, value) {\n            let settings = {}\n            if (\n                cape.application.settings.get('previewStore')[\n                    this.decoderType\n                ] &&\n                cape.application.settings.get('previewStore')[this.decoderType][\n                    this.decoderId\n                ]\n            ) {\n                settings = cape.application.settings.get('previewStore')[\n                    this.decoderType\n                ][this.decoderId]\n            }\n            cape.application.settings.setObjectItem(\n                'previewStore',\n                this.decoderType,\n                Object.assign(\n                    {},\n                    {\n                        [this.decoderId]: {\n                            ...settings,\n                            [type]: value,\n                        },\n                    }\n                )\n            )\n        },\n        processGeometry(json) {\n            console.log('PROCESSS')\n            if (!json) return\n            this.objectLine = json.superior_object_line\n            if (!this.currentGeometry) {\n                if (\n                    !this.choosenColor ||\n                    (this.objectLine === 'type_02' &&\n                        !this.choosenColor.startsWith('1'))\n                ) {\n                    this.choosenColor = '1:0'\n                } else if (\n                    !this.choosenColor ||\n                    (this.objectLine === 'type_01' &&\n                        !this.choosenColor.startsWith('0'))\n                ) {\n                    this.choosenColor = '0:0'\n                }\n            }\n            this.currentGeometry = json\n\n            switch (parseInt(this.mode)) {\n                case 8:\n                    this.designerRenderer.then(renderer => {\n                        let tmpData = getGeometry(\n                            {\n                                serialization: json,\n                            },\n                            {\n                                width: this.decoderWidth,\n                                height: this.meshMode\n                                    ? this.decoderHeight\n                                    : null,\n                                depth: this.depth,\n                                mesh_setup: null,\n                                geom_id: this.decoderId,\n                                geom_type: this.decoderType,\n                                generate_thumbnails: this.generateThumbnails,\n                                thumbsForChannel: this.thumbsForChannel,\n                                distortion: this.meshMode\n                                    ? this.distortion\n                                    : null,\n                                density: this.meshMode ? this.density : null,\n                                plinth: this.plinth,\n                                configurator_custom_params: this\n                                    .configuratorCustomParams\n                                    ? this.configuratorCustomParams\n                                    : null,\n                            },\n                            {\n                                format: 'gallery',\n                            }\n                        )\n\n                        // renderer.setScene(this.previewRenderer.scene);\n                        // renderer.clearScene(this.previewRenderer.scene);\n                        if (\n                            this.selectedSetupTab &&\n                            this.selectedSetupTab > 0\n                        ) {\n                            let selected_config_id = this.decoderOutput\n                                .components[this.selectedSetupTab - 1]\n                                .m_config_id\n                            for (let x of ['drawers', 'doors', 'buttons']) {\n                                for (let item of tmpData[x]) {\n                                    if (\n                                        item.m_config_id == selected_config_id\n                                    ) {\n                                        item.selected = true\n                                    }\n                                }\n                            }\n                        }\n                        let oldWarnFunction = console.warn\n                        console.warn = function() {}\n                        console.log('PROCESSS DISPLAY')\n                        renderer.displayShelf(\n                            tmpData,\n                            this.choosenColor,\n                            this.previewRenderer.scene,\n                            this.previewRenderer.camera,\n                            this.previewRenderer.renderer\n                        )\n\n                        renderer.previewSetComponent = this.previewSetComponent\n                        renderer.mouseMoveSetComponent = this.mouseMoveSetComponent\n                        renderer.mouseMoveOnLeave = this.mouseMoveOnLeave\n                        renderer.deselectComponent = this.deselectComponent\n\n                        console.warn = oldWarnFunction\n                        this.faceBox = renderer.facePlane\n\n                        this.currentRenderer = this.previewRenderer\n                        this.price = renderer.getPrice(tmpData)\n                        this.boxList = renderer.getIndicatorBoxesPositions()\n\n                        this.currentGeometry = tmpData\n                        this.decoderOutput = tmpData\n                        this.arrageComponentsPopovers()\n                    })\n                    break\n            }\n            this.loaderHidden = true\n        },\n        loadFromStorage() {\n            if (\n                cape.application.settings.get('previewStore')[\n                    this.decoderType\n                ] &&\n                cape.application.settings.get('previewStore')[this.decoderType][\n                    this.decoderId\n                ]\n            ) {\n                let settings = cape.application.settings.get('previewStore')[\n                    this.decoderType\n                ][this.decoderId]\n                for (let key in settings) {\n                    if (settings.hasOwnProperty(key)) {\n                        this[key] = settings[key]\n                    }\n                }\n            }\n        },\n\n        initialPreviewParametersState() {},\n\n        loadPreviousPreviewParametersState() {\n            if (\n                this.currentPreviewParametersState <= 0 ||\n                this.previewParametersStates.length <= 1\n            ) {\n                console.log(\n                    '---- Tylko jeden zapisany stan lub aktualny stan to stan początkowy!'\n                )\n                this.updateStatesAvailbility()\n                return\n            }\n            let stateToLoad = this.currentPreviewParametersState - 1\n            let state = {}\n            if (stateToLoad <= this.previewParametersStates.length) {\n                state = this.previewParametersStates[stateToLoad]\n                this.currentPreviewParametersState = stateToLoad\n            } else {\n                this.currentPreviewParametersState =\n                    this.previewParametersStates.length - 1\n                state = this.previewParametersStates[\n                    this.currentPreviewParametersState\n                ]\n            }\n            this.loadPreviewParametersState(state)\n        },\n\n        loadNextPreviewParametersState() {\n            if (\n                this.currentPreviewParametersState >\n                this.previewParametersStates.length\n            ) {\n                console.log(\n                    '---- Aktualny stan parametrow jest ostatnim zapisanym!'\n                )\n                this.updateStatesAvailbility()\n                return\n            }\n            this.currentPreviewParametersState += 1\n            let state = this.previewParametersStates[\n                this.currentPreviewParametersState\n            ]\n            this.loadPreviewParametersState(state)\n        },\n\n        loadPreviewParametersState(state) {\n            Object.assign(this, state)\n            this.updateStatesAvailbility()\n            this.update()\n        },\n\n        updateStatesAvailbility() {\n            this.previousStateAvailble =\n                this.currentPreviewParametersState > 0 &&\n                this.previewParametersStates &&\n                this.previewParametersStates.length !== 0\n            this.nextStateAvailble =\n                this.currentPreviewParametersState <\n                this.previewParametersStates.length - 1\n            this.parametersStatesStatus = [\n                this.currentPreviewParametersState,\n                this.previewParametersStates.length - 1,\n            ]\n        },\n\n        saveCurrentPreviewParametersState(linearHistory, replace_index = null) {\n            if (\n                linearHistory &&\n                this.currentPreviewParametersState !==\n                    this.previewParametersStates.length - 1\n            ) {\n                this.previewParametersStates.length =\n                    this.currentPreviewParametersState + 1\n            }\n            let paramSet = {\n                choosenColor: this.choosenColor,\n                decoderWidth: this.decoderWidth,\n                decoderHeight: this.decoderHeight,\n                density: this.density,\n                depth: this.depth,\n                plinth: this.plinth,\n                distortion: this.distortion,\n                configuratorCustomParams: this.configuratorCustomParams,\n            }\n            if (replace_index === null)\n                this.previewParametersStates.push(paramSet)\n            else this.previewParametersStates.splice(replace_index, 1, paramSet)\n\n            this.currentPreviewParametersState =\n                this.previewParametersStates.length - 1\n            this.updateStatesAvailbility()\n        },\n\n        initRenderer() {\n            if (\n                this.$el.querySelector('.production-renderer-canvas').firstChild\n            ) {\n                this.$el\n                    .querySelector('.production-renderer-canvas')\n                    .firstChild.remove()\n            }\n            this.designerRenderer = WebdesignerRenderer()\n            let productionRenderer = new ProductionRenderer({})\n            this.previewRenderer = productionRenderer.set({\n                container: this.$el.querySelector(\n                    '.production-renderer-canvas'\n                ),\n                width: this.vwidth,\n                height: this.vheight,\n                cameraLock: this.locked,\n                updatedCameraCallback: () => {\n                    this.arrageComponentsPopovers()\n                    this.mapCompartments(this.decoderOutput)\n                    this.updateResizers()\n                },\n            })\n            let oldWarnFunction = console.warn\n            console.warn = function() {}\n\n            this.previewRenderer.resetScene()\n            this.designerRenderer.then(renderer => {\n                renderer.setScene(this.previewRenderer.scene)\n                renderer.clearScene(this.previewRenderer.scene)\n                renderer.setBackgroundScene(this.previewRenderer._scene)\n            })\n            console.warn = oldWarnFunction\n            window.addEventListener('resize', () => {\n                let size = this.$el.getBoundingClientRect()\n                this.previewRenderer.resize({\n                    width: size.width * 0.7,\n                    height: size.height,\n                })\n            })\n            this.update()\n        },\n        switchElements() {\n            if (this.elements === 'all') {\n                this.designerRenderer.then(renderer =>\n                    renderer.setDesignerMode(1)\n                )\n            } else if (this.elements === 'all_opened') {\n                this.designerRenderer.then(renderer =>\n                    renderer.setDesignerMode(1)\n                )\n            }\n        },\n\n        buildResizers(output) {},\n\n        updateResizers() {},\n\n        mapCompartments(output) {\n            let widths = [],\n                heights = []\n            let selected = this.$refs.configuration.$refs.previewSelect\n                .selectedTab\n            if (this.showAllCompartments) {\n                output.components.forEach(({ compartments }) => {\n                    compartments.forEach(({ height, width }) => {\n                        widths = [...widths, width]\n                        heights = [...heights, height]\n                    })\n                })\n            } else {\n                if (selected && selected !== '0') {\n                    output.components[\n                        Number(selected) - 1\n                    ].compartments.forEach(({ height, width }) => {\n                        widths = [...widths, width]\n                        heights = [...heights, height]\n                    })\n                }\n            }\n            this.visibleUsps()\n            this.compartmentsWidths = widths.map(this.compartmentsToScreen)\n            this.compartmentsHeights = heights.map(this.compartmentsToScreen)\n        },\n    },\n\n    watch: {\n        showAllCompartmentsButton(bool) {\n            this.mapCompartments(this.decoderOutput)\n            if (!bool) {\n                this.showAllCompartments = false\n                this.$refs.rendererContainer.classList.remove(\n                    'show-compartment'\n                )\n            } else {\n                this.showAllCompartments = true\n                this.$refs.rendererContainer.classList.add('show-compartment')\n            }\n        },\n        loaderHidden(bool) {\n            if (bool) {\n                this.$refs.rendererWapper.classList.remove('load-hidden')\n            }\n        },\n        elements() {\n            this.switchElements()\n        },\n        showAllCompartments() {\n            this.update()\n        },\n        decoderOutput(output) {\n            this.mapCompartments(output)\n            this.buildResizers(output)\n        },\n    },\n\n    created() {\n        if (\n            this.$route.params.type !== 'mesh' &&\n            this.$route.params.type !== 'component'\n        ) {\n            this.$router.push('/')\n        }\n        this.isConfigurator = this.$route.name === 'configurator'\n    },\n\n    mounted() {\n        let { type, id } = this.$route.params\n        this.decoderType = type\n        this.meshMode = type === 'mesh'\n        this.decoderId = id\n        this.$refs.configuration.$refs.previewSelect.$el.addEventListener(\n            'mouseover',\n            e => {\n                window.clearTimeout(this.rendererLeaveTimeout)\n            }\n        )\n        if (this.decoderType === 'component') {\n            cape.api.componentSetup(this.decoderId).subscribe(resp => {\n                document.title = `Component - ${resp.name}`\n                this.element = resp\n                this.innitialize()\n            })\n        } else if (this.decoderType === 'mesh') {\n            cape.api.mesh(this.decoderId).subscribe(resp => {\n                document.title = `Mesh - ${resp.name}`\n                this.element = resp\n                this.innitialize()\n            })\n        }\n    },\n\n    data() {\n        return {\n            grabButtons: {\n                left: { x: 0, y: 0 },\n                right: { x: 0, y: 0 },\n            },\n\n            previewParametersStates: [],\n            currentPreviewParametersState: 0,\n            previousStateAvailble: false,\n            nextStateAvailble: false,\n            parametersStatesStatus: [0, 0],\n            mappedObjects: [],\n            decoderType: 'mesh',\n            vwidth: window.innerWidth * 0.7 - 42,\n            vheight: window.innerHeight,\n            decoderWidth: null,\n            decoderHeight: null,\n            decoderId: null,\n            meshMode: null,\n            mode: 8,\n            elements: 'all',\n            choosenColor: '0:0',\n            currentGeometry: null,\n            previewRenderer: null,\n            decoderOutput: null,\n            solidMode: false,\n            designerRenderer: null,\n            loaderHidden: false,\n            element: null,\n            configuratorCustomParams: null,\n            selectedSetupTab: 0,\n            objectLine: null,\n            distortion: 100,\n            density: 100,\n            depth: 320,\n            plinth: true,\n            price: '-',\n            locked: true,\n            generateThumbnails: false,\n            thumbsForChannel: null,\n            prevSelected: null,\n            timeoutHandle: null,\n            isConfigurator: null,\n            compartmentsWidths: [],\n            compartmentsHeights: [],\n            showAllCompartments: false,\n            showAllCompartmentsButton: false,\n            rendererLeaveTimeout: null,\n            hoverElement: null,\n            visibleUspsText: false,\n        }\n    },\n}\n</script>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./preview-page.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./preview-page.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./preview-page.vue?vue&type=template&id=12b833e8&lang=pug&\"\nimport script from \"./preview-page.vue?vue&type=script&lang=js&\"\nexport * from \"./preview-page.vue?vue&type=script&lang=js&\"\nimport style0 from \"./preview-page.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var createLayout = require('layout-bmfont-text')\nvar inherits = require('inherits')\nvar createIndices = require('quad-indices')\nvar buffer = require('three-buffer-vertex-data')\nvar assign = require('object-assign')\n\nvar vertices = require('./lib/vertices')\nvar utils = require('./lib/utils')\n\nvar Base = THREE.BufferGeometry\n\nmodule.exports = function createTextGeometry (opt) {\n  return new TextGeometry(opt)\n}\n\nfunction TextGeometry (opt) {\n  Base.call(this)\n\n  if (typeof opt === 'string') {\n    opt = { text: opt }\n  }\n\n  // use these as default values for any subsequent\n  // calls to update()\n  this._opt = assign({}, opt)\n\n  // also do an initial setup...\n  if (opt) this.update(opt)\n}\n\ninherits(TextGeometry, Base)\n\nTextGeometry.prototype.update = function (opt) {\n  if (typeof opt === 'string') {\n    opt = { text: opt }\n  }\n\n  // use constructor defaults\n  opt = assign({}, this._opt, opt)\n\n  if (!opt.font) {\n    throw new TypeError('must specify a { font } in options')\n  }\n\n  this.layout = createLayout(opt)\n\n  // get vec2 texcoords\n  var flipY = opt.flipY !== false\n\n  // the desired BMFont data\n  var font = opt.font\n\n  // determine texture size from font file\n  var texWidth = font.common.scaleW\n  var texHeight = font.common.scaleH\n\n  // get visible glyphs\n  var glyphs = this.layout.glyphs.filter(function (glyph) {\n    var bitmap = glyph.data\n    return bitmap.width * bitmap.height > 0\n  })\n\n  // provide visible glyphs for convenience\n  this.visibleGlyphs = glyphs\n\n  // get common vertex data\n  var positions = vertices.positions(glyphs)\n  var uvs = vertices.uvs(glyphs, texWidth, texHeight, flipY)\n  var indices = createIndices({\n    clockwise: true,\n    type: 'uint16',\n    count: glyphs.length\n  })\n\n  // update vertex data\n  buffer.index(this, indices, 1, 'uint16')\n  buffer.attr(this, 'position', positions, 2)\n  buffer.attr(this, 'uv', uvs, 2)\n\n  // update multipage data\n  if (!opt.multipage && 'page' in this.attributes) {\n    // disable multipage rendering\n    this.removeAttribute('page')\n  } else if (opt.multipage) {\n    var pages = vertices.pages(glyphs)\n    // enable multipage rendering\n    buffer.attr(this, 'page', pages, 1)\n  }\n}\n\nTextGeometry.prototype.computeBoundingSphere = function () {\n  if (this.boundingSphere === null) {\n    this.boundingSphere = new THREE.Sphere()\n  }\n\n  var positions = this.attributes.position.array\n  var itemSize = this.attributes.position.itemSize\n  if (!positions || !itemSize || positions.length < 2) {\n    this.boundingSphere.radius = 0\n    this.boundingSphere.center.set(0, 0, 0)\n    return\n  }\n  utils.computeSphere(positions, this.boundingSphere)\n  if (isNaN(this.boundingSphere.radius)) {\n    console.error('THREE.BufferGeometry.computeBoundingSphere(): ' +\n      'Computed radius is NaN. The ' +\n      '\"position\" attribute is likely to have NaN values.')\n  }\n}\n\nTextGeometry.prototype.computeBoundingBox = function () {\n  if (this.boundingBox === null) {\n    this.boundingBox = new THREE.Box3()\n  }\n\n  var bbox = this.boundingBox\n  var positions = this.attributes.position.array\n  var itemSize = this.attributes.position.itemSize\n  if (!positions || !itemSize || positions.length < 2) {\n    bbox.makeEmpty()\n    return\n  }\n  utils.computeBox(positions, bbox)\n}\n", "var Buffer = require('buffer').Buffer; // for use with browserify\n\nmodule.exports = function (a, b) {\n    if (!Buffer.isBuffer(a)) return undefined;\n    if (!Buffer.isBuffer(b)) return undefined;\n    if (typeof a.equals === 'function') return a.equals(b);\n    if (a.length !== b.length) return false;\n    \n    for (var i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) return false;\n    }\n    \n    return true;\n};\n", "module.exports = function parseBMFontAscii(data) {\n  if (!data)\n    throw new Error('no data provided')\n  data = data.toString().trim()\n\n  var output = {\n    pages: [],\n    chars: [],\n    kernings: []\n  }\n\n  var lines = data.split(/\\r\\n?|\\n/g)\n\n  if (lines.length === 0)\n    throw new Error('no data in BMFont file')\n\n  for (var i = 0; i < lines.length; i++) {\n    var lineData = splitLine(lines[i], i)\n    if (!lineData) //skip empty lines\n      continue\n\n    if (lineData.key === 'page') {\n      if (typeof lineData.data.id !== 'number')\n        throw new Error('malformed file at line ' + i + ' -- needs page id=N')\n      if (typeof lineData.data.file !== 'string')\n        throw new Error('malformed file at line ' + i + ' -- needs page file=\"path\"')\n      output.pages[lineData.data.id] = lineData.data.file\n    } else if (lineData.key === 'chars' || lineData.key === 'kernings') {\n      //... do nothing for these two ...\n    } else if (lineData.key === 'char') {\n      output.chars.push(lineData.data)\n    } else if (lineData.key === 'kerning') {\n      output.kernings.push(lineData.data)\n    } else {\n      output[lineData.key] = lineData.data\n    }\n  }\n\n  return output\n}\n\nfunction splitLine(line, idx) {\n  line = line.replace(/\\t+/g, ' ').trim()\n  if (!line)\n    return null\n\n  var space = line.indexOf(' ')\n  if (space === -1) \n    throw new Error(\"no named row at line \" + idx)\n\n  var key = line.substring(0, space)\n\n  line = line.substring(space + 1)\n  //clear \"letter\" field as it is non-standard and\n  //requires additional complexity to parse \" / = symbols\n  line = line.replace(/letter=[\\'\\\"]\\S+[\\'\\\"]/gi, '')  \n  line = line.split(\"=\")\n  line = line.map(function(str) {\n    return str.trim().match((/(\".*?\"|[^\"\\s]+)+(?=\\s*|\\s*$)/g))\n  })\n\n  var data = []\n  for (var i = 0; i < line.length; i++) {\n    var dt = line[i]\n    if (i === 0) {\n      data.push({\n        key: dt[0],\n        data: \"\"\n      })\n    } else if (i === line.length - 1) {\n      data[data.length - 1].data = parseData(dt[0])\n    } else {\n      data[data.length - 1].data = parseData(dt[0])\n      data.push({\n        key: dt[1],\n        data: \"\"\n      })\n    }\n  }\n\n  var out = {\n    key: key,\n    data: {}\n  }\n\n  data.forEach(function(v) {\n    out.data[v.key] = v.data;\n  })\n\n  return out\n}\n\nfunction parseData(data) {\n  if (!data || data.length === 0)\n    return \"\"\n\n  if (data.indexOf('\"') === 0 || data.indexOf(\"'\") === 0)\n    return data.substring(1, data.length - 1)\n  if (data.indexOf(',') !== -1)\n    return parseIntList(data)\n  return parseInt(data, 10)\n}\n\nfunction parseIntList(data) {\n  return data.split(',').map(function(val) {\n    return parseInt(val, 10)\n  })\n}", "module.exports = isFunction\n\nvar toString = Object.prototype.toString\n\nfunction isFunction (fn) {\n  var string = toString.call(fn)\n  return string === '[object Function]' ||\n    (typeof fn === 'function' && string !== '[object RegExp]') ||\n    (typeof window !== 'undefined' &&\n     // IE8 and below\n     (fn === window.setTimeout ||\n      fn === window.alert ||\n      fn === window.confirm ||\n      fn === window.prompt))\n};\n", "import regl from 'regl';\nimport quad from 'glsl-quad';\nimport { Matrix4 } from 'three';\n\nvar shadowsRaymarcher = \"precision mediump float;\\n#define GLSLIFY 1\\nvarying vec3 rayPosition;varying vec3 rayDirection;uniform vec4 shadowSource;uniform float shadowPower;uniform vec3 shelfSize;uniform vec4 idents[20];uniform float shelfGap;float a(float b,float c){return min(b,c);}vec2 a(vec2 b,vec2 c){return (b.x<c.x)?b:c;}float d(vec3 e,vec3 f){vec3 g=abs(e)-f;return min(max(g.x,max(g.y,g.z)),0.0)+length(max(g,0.0));}float h(vec2 i){return max(i.x,i.y);}float j(vec2 k,vec2 l){return h(abs(k)-l);}float m(float n,float l,float o){vec2 p=max(vec2(o+n,o+l),vec2(0));return min(-o,max(n,l))+length(p);}vec2 q(vec3 k){k.y-=shelfSize.y-20.;k.z-=shelfSize.z;float r=0.;vec3 s=shelfSize;s.y-=20.;float t=d(k+vec3(0,0,0),s);float u=dot(k-vec3(0,-shelfSize.y-shelfGap,-shelfGap),vec3(0,1.,0));float v=dot(k-vec3(0,0,-(shelfSize.z+shelfGap)),vec3(0,0,1.));r=a(v,u);for(int w=0;w<20;w++){float x=dot(idents[w],vec4(1.0));if(x!=0.0){vec2 e=idents[w].xy;vec2 y=idents[w].zw;t=max(-j(k.xy+e,y),t);}}r=a(r,t);return vec2(r,0);}vec2 z(vec3 A,vec3 B,float C,float D){float E=D*2.0;float F=+0.0;float G=-1.0;vec2 H=vec2(-1.0,-1.0);for(int w=0;w<140;w++){if(E<D||F>C) break;vec2 I=q(A+B*F);E=I.x;G=I.y;F+=E;}if(F<C){H=vec2(F,G);}return H;}vec2 z(vec3 A,vec3 B){return z(A,B,20.0,0.001);}vec3 J(vec3 K,float L){const vec3 M=vec3(1.0,-1.0,-1.0);const vec3 N=vec3(-1.0,-1.0,1.0);const vec3 O=vec3(-1.0,1.0,-1.0);const vec3 P=vec3(1.0,1.0,1.0);return normalize(M*q(K+M*L).x+N*q(K+N*L).x+O*q(K+O*L).x+P*q(K+P*L).x);}vec3 J(vec3 K){return J(K,0.002);}float Q(vec3 R,vec3 S,float T,float U,float V){float H=2.5;float W=0.0;float X=0.1;for(int w=0;w<26;++w){float Y=q(R+S*X).x;if(Y<0.001) return .0;float Z=Y*Y/(2.*W);float g=sqrt(Y*Y-Z*Z);H=min(H,V*g/max(0.0,X-Z));W=Y;X+=Y;}return H;}vec3 ba(vec3 K,vec3 bb,vec4 bc){vec3 bd=bc.xyz-K;float be=length(bd);bd=normalize(bd);float bf=2.0;float bg=Q(K,bd,.625,be,shadowPower);return vec3(bg);}void main(){vec3 bh=vec3(0.,0.,0.);vec3 S,R;R=rayPosition;S=rayDirection;vec2 X=z(rayPosition,S,35000.,0.001);if(X.x>0.5){vec3 K=R+S*X.x;vec3 bi=J(K);vec3 bj=ba(K,bi,vec4(shadowSource.xyz,shadowPower));bh=bj;bh=vec3(dot(bh,vec3(.3,.9,.0)));}gl_FragColor.rgb=bh;gl_FragColor.a=1.0-smoothstep(1.0,0.0,length(bh));}\";\n\nvar shadowsRaymarcherFast = \"precision mediump float;\\n#define GLSLIFY 1\\nvarying vec3 rayPosition;varying vec3 rayDirection;uniform vec4 shadowSource;uniform float shadowPower;uniform vec3 shelfSize;uniform vec4 idents[20];uniform float shelfGap;float a(float b,float c){return min(b,c);}vec2 a(vec2 b,vec2 c){return (b.x<c.x)?b:c;}float d(vec3 e,vec3 f){vec3 g=abs(e)-f;return min(max(g.x,max(g.y,g.z)),0.0)+length(max(g,0.0));}float h(vec2 i){return max(i.x,i.y);}float j(vec2 k,vec2 l){return h(abs(k)-l);}float m(float n,float l,float o){vec2 p=max(vec2(o+n,o+l),vec2(0));return min(-o,max(n,l))+length(p);}vec2 q(vec3 k){k.y-=shelfSize.y-20.;k.z-=shelfSize.z;float r=0.;vec3 s=shelfSize;s.y-=20.;float t=d(k+vec3(0,0,0),s);float u=dot(k-vec3(0,-shelfSize.y-shelfGap,-shelfGap),vec3(0,1.,0));float v=dot(k-vec3(0,0,-(shelfSize.z+shelfGap)),vec3(0,0,1.));r=a(v,u);r=a(r,t);return vec2(r,0);}vec2 w(vec3 x,vec3 y,float z,float A){float B=A*2.0;float C=+0.0;float D=-1.0;vec2 E=vec2(-1.0,-1.0);for(int F=0;F<10;F++){if(B<A||C>z) break;vec2 G=q(x+y*C);B=G.x;D=G.y;C+=B;}if(C<z){E=vec2(C,D);}return E;}vec2 w(vec3 x,vec3 y){return w(x,y,20.0,0.001);}vec3 H(vec3 I,float J){const vec3 K=vec3(1.0,-1.0,-1.0);const vec3 L=vec3(-1.0,-1.0,1.0);const vec3 M=vec3(-1.0,1.0,-1.0);const vec3 N=vec3(1.0,1.0,1.0);return normalize(K*q(I+K*J).x+L*q(I+L*J).x+M*q(I+M*J).x+N*q(I+N*J).x);}vec3 H(vec3 I){return H(I,0.002);}float O(vec3 P,vec3 Q,float R,float S,float T){float E=2.5;float U=0.0;float V=0.1;for(int F=0;F<26;++F){float W=q(P+Q*V).x;if(W<0.001) return .0;float X=W*W/(2.*U);float g=sqrt(W*W-X*X);E=min(E,T*g/max(0.0,V-X));U=W;V+=W;}return E;}vec3 Y(vec3 I,vec3 Z,vec4 ba){vec3 bb=ba.xyz-I;float bc=length(bb);bb=normalize(bb);float bd=2.0;float be=O(I,bb,.625,bc,shadowPower);return vec3(be);}void main(){vec3 bf=vec3(0.,0.,0.);vec3 Q,P;P=rayPosition;Q=rayDirection;vec2 V=w(rayPosition,Q,35000.,0.001);if(V.x>0.5){vec3 I=P+Q*V.x;vec3 bg=H(I);vec3 bh=shadowSource.xyz;vec3 bi=Y(I,bg,vec4(bh,shadowPower));bf=bi;bf=vec3(dot(bf,vec3(.3,.9,.0)));}gl_FragColor.rgb=bf;gl_FragColor.a=1.0-smoothstep(1.0,0.0,length(bf));}\";\n\nvar shadowsRaymarcherCasterOtho = \"precision mediump float;\\n#define GLSLIFY 1\\n\\n#define clip 35000.0\\nattribute vec2 position;attribute vec2 uvs;varying vec4 ndc;varying vec3 rayDirection;varying vec3 rayPosition;uniform vec3 cameraPosition;uniform vec2 warp;void main(){vec2 a=uvs;a=(a*2.0)-vec2(1.0,0.0);ndc=vec4(a,1.0,1.0)*clip;rayDirection=vec3(0,0,-1);rayPosition=vec3(0);rayPosition.z+=cameraPosition.z;rayPosition.xy+=ndc.xy*warp;gl_Position=vec4(position,0.0,1.0);}\";\n\nvar shadowsRaymarcherCasterOthoTop = \"precision mediump float;\\n#define GLSLIFY 1\\n\\n#define clip 35000.0\\nattribute vec2 position;attribute vec2 uvs;varying vec4 ndc;varying vec3 rayDirection;varying vec3 rayPosition;uniform vec3 cameraPosition;uniform vec2 warp;void main(){vec2 a=uvs;a=(a*2.0)-vec2(1.0,0.0);ndc=vec4(a,1.0,1.0)*clip;rayDirection=vec3(0,-1,0);rayPosition=vec3(0);rayPosition.y+=cameraPosition.z;rayPosition.xz+=ndc.xy*warp;gl_Position=vec4(position,0.0,1.0);}\";\n\nvar MAX_IDENTS = 42;\n\nvar inverseProjectionMatrix = new Matrix4();\nvar worldToLocal = new Matrix4();\n\nvar emptyM4 = new Matrix4();\nvar mappedProjection = new Matrix4();\n\n// Implement multiple commands for one render pass i.e. fast-blur  https://github.com/jwerle/regl-combine\n// Add \"shadow-right\" and other elements from Ivy render -> proxy gemoetry\n\n\nfunction softShadowsTexturePass(renderer, canvasElement, isTopProjection) {\n\n    var cameraObject = renderer.camera;\n    var build3d = renderer.geo;\n\n    var blend = {\n        blend: {\n            /*  enable: false,\n              func: {\n                  srcRGB: 'one',\n                  srcAlpha: 'zero',\n                  dstRGB: 'one minus src alpha',\n                  dstAlpha: 'one minus src alpha'\n              }*/\n            enable: true,\n            func: {\n                srcRGB: 'src alpha',\n                srcAlpha: 0,\n                dstRGB: 'one minus src alpha',\n                dstAlpha: 1\n            },\n            equation: {\n                rgb: 'add',\n                alpha: 'add'\n            },\n            color: [0, 0, 0, 0]\n        }\n    };\n\n    var selfSize = function selfSize() {\n        return [renderer.ivy.width / 2, renderer.ivy.getHeight() / 2, 320 / 2];\n    };\n\n    var rl = regl(canvasElement);\n\n    var uniformsIdents = {};\n\n    for (var j = 0; j < MAX_IDENTS; j++) {\n        uniformsIdents['idents[' + j + ']'] = rl.prop('idents' + j);\n    }\n\n    var uniforms = Object.assign({\n\n        // Tylko Renderer\n\n        cameraPosition: rl.prop('cameraPosition'),\n\n        shelfGap: rl.prop('shelfGap'),\n        shelfSize: rl.prop('shelfSize')\n    }, uniformsIdents, {\n\n        shadowSource: rl.prop('shadowSource'),\n        shadowPower: rl.prop('shadowPower'),\n\n        warp: rl.prop('warp')\n    });\n\n    var castFlatShadows = rl(Object.assign({\n\n        frag: isTopProjection ? shadowsRaymarcherFast : shadowsRaymarcher,\n\n        vert: isTopProjection ? shadowsRaymarcherCasterOthoTop : shadowsRaymarcherCasterOtho,\n\n        attributes: {\n            position: rl.buffer(quad.verts),\n            uvs: rl.buffer(quad.uvs)\n        },\n\n        uniforms: uniforms,\n        count: 6\n\n    }, blend));\n\n    var render = function render(_ref) {\n        var settings = _ref.settings;\n\n\n        rl.clear({\n            depth: 1,\n            color: [0, 0, 0, 1]\n        });\n\n        var idents = build3d.getIndents(selfSize());\n        var parsedIdents = {};\n\n        for (var v = 0; v < MAX_IDENTS; v++) {\n            if (idents[v]) {\n                parsedIdents['idents' + v] = idents[v];\n            } else {\n                parsedIdents['idents' + v] = [0, 0, 0, 0];\n            }\n        }\n\n        castFlatShadows(Object.assign({\n\n            shelfSize: selfSize(),\n\n            shadowPower: settings.power,\n            shelfGap: settings.shelfGap,\n\n            cameraPosition: [0, 0, settings.cameraz * 3],\n\n            shadowSource: [settings.x, settings.y, settings.z, 0],\n            warp: [settings.warpx, settings.warpy]\n\n        }, parsedIdents));\n    };\n\n    var updateIndents = function updateIndents() {\n        return false;\n    };\n    var updateSize = function updateSize() {\n        return false;\n    };\n\n    return { render: render, updateIndents: updateIndents, updateSize: updateSize };\n}\n\nexport { softShadowsTexturePass };\n", "/**\n * <AUTHOR> / http://egraether.com/\n * <AUTHOR> \t/ http://mark-lundin.com\n * <AUTHOR> / http://daron1337.github.io\n * <AUTHOR> \t/ http://lantiga.github.io\n\n ** three-trackballcontrols module\n ** <AUTHOR> / http://jonlim.ca\n */\n\nvar THREE = window.THREE || require('three');\n\nvar TrackballControls = function (object, domElement, lock = false) {\n\n\tvar _this = this;\n\tvar STATE = { NONE: - 1, ROTATE: 0, ZOOM: 1, PAN: 2, TOUCH_ROTATE: 3, TOUCH_ZOOM_PAN: 4 };\n\n\tthis.object = object;\n\tthis.domElement = (domElement !== undefined) ? domElement : document;\n\n\t// API\n\tthis.locked = lock;\n\tthis.enabled = true;\n\n\tthis.screen = { left: 0, top: 0, width: 0, height: 0 };\n\n\tthis.rotateSpeed = 1.0;\n\tthis.zoomSpeed = 1.2;\n\tthis.panSpeed = 0.3;\n\n\tthis.noRotate = false;\n\tthis.noZoom = false;\n\tthis.noPan = false;\n\n\tthis.staticMoving = false;\n\tthis.dynamicDampingFactor = 0.2;\n\n\tthis.minDistance = 0;\n\tthis.maxDistance = Infinity;\n\n\t/**\n\t * `KeyboardEvent.keyCode` values which should trigger the different \n\t * interaction states. Each element can be a single code or an array\n\t * of codes. All elements are required.\n\t */\n\tthis.keys = [65 /*A*/, 83 /*S*/, 68 /*D*/];\n\n\t// internals\n\n\tthis.target = new THREE.Vector3();\n\n\tvar EPS = 0.000001;\n\n\tvar lastPosition = new THREE.Vector3();\n\n\tvar _state = STATE.NONE,\n\t\t_prevState = STATE.NONE,\n\n\t\t_eye = new THREE.Vector3(),\n\n\t\t_movePrev = new THREE.Vector2(),\n\t\t_moveCurr = new THREE.Vector2(),\n\n\t\t_lastAxis = new THREE.Vector3(),\n\t\t_lastAngle = 0,\n\n\t\t_zoomStart = new THREE.Vector2(),\n\t\t_zoomEnd = new THREE.Vector2(),\n\n\t\t_touchZoomDistanceStart = 0,\n\t\t_touchZoomDistanceEnd = 0,\n\n\t\t_panStart = new THREE.Vector2(),\n\t\t_panEnd = new THREE.Vector2();\n\n\t// for reset\n\n\tthis.target0 = this.target.clone();\n\tthis.position0 = this.object.position.clone();\n\tthis.up0 = this.object.up.clone();\n\n\t// events\n\n\tvar changeEvent = { type: 'change' };\n\tvar startEvent = { type: 'start' };\n\tvar endEvent = { type: 'end' };\n\n\n\t// methods\n\t// methods\n\n\tthis.handleResize = function () {\n\n\t\tif (this.domElement === document) {\n\n\t\t\tthis.screen.left = 0;\n\t\t\tthis.screen.top = 0;\n\t\t\tthis.screen.width = window.innerWidth;\n\t\t\tthis.screen.height = window.innerHeight;\n\n\t\t} else {\n\n\t\t\tvar box = this.domElement.getBoundingClientRect();\n\t\t\t// adjustments come from similar code in the jquery offset() function\n\t\t\tvar d = this.domElement.ownerDocument.documentElement;\n\t\t\tthis.screen.left = box.left + window.pageXOffset - d.clientLeft;\n\t\t\tthis.screen.top = box.top + window.pageYOffset - d.clientTop;\n\t\t\tthis.screen.width = box.width;\n\t\t\tthis.screen.height = box.height;\n\n\t\t}\n\n\t};\n\n\tthis.handleEvent = function (event) {\n\n\t\tif (typeof this[event.type] == 'function') {\n\n\t\t\tthis[event.type](event);\n\n\t\t}\n\n\t};\n\n\tvar getMouseOnScreen = (function () {\n\n\t\tvar vector = new THREE.Vector2();\n\n\t\treturn function getMouseOnScreen(pageX, pageY) {\n\n\t\t\tvector.set(\n\t\t\t\t(pageX - _this.screen.left) / _this.screen.width,\n\t\t\t\t(pageY - _this.screen.top) / _this.screen.height\n\t\t\t);\n\n\t\t\treturn vector;\n\n\t\t};\n\n\t}());\n\n\tvar getMouseOnCircle = (function () {\n\n\t\tvar vector = new THREE.Vector2();\n\n\t\treturn function getMouseOnCircle(pageX, pageY) {\n\n\t\t\tvector.set(\n\t\t\t\t((pageX - _this.screen.width * 0.5 - _this.screen.left) / (_this.screen.width * 0.5)),\n\t\t\t\t((_this.screen.height + 2 * (_this.screen.top - pageY)) / _this.screen.width) // screen.width intentional\n\t\t\t);\n\n\t\t\treturn vector;\n\n\t\t};\n\n\t}());\n\n\tthis.rotateCamera = (function () {\n\n\t\tvar axis = new THREE.Vector3(),\n\t\t\tquaternion = new THREE.Quaternion(),\n\t\t\teyeDirection = new THREE.Vector3(),\n\t\t\tobjectUpDirection = new THREE.Vector3(),\n\t\t\tobjectSidewaysDirection = new THREE.Vector3(),\n\t\t\tmoveDirection = new THREE.Vector3(),\n\t\t\tangle;\n\n\t\tvar deltaAxis = 0;\n\n\t\tfunction rotateCamera() {\n\n\t\t\tlet moveDirectionCopy = moveDirection.clone();\n\t\t\tmoveDirection.set(_moveCurr.x - _movePrev.x, _moveCurr.y - _movePrev.y, 0);\n\t\t\tangle = moveDirection.length();\n\n\t\t\tlet maxA = 30;\n\t\t\tlet breakIt = false;\n\t\t\tdeltaAxis += angle * (axis.y > 0 ? 1 : -1);\n\t\t\tlet angleInDeg = deltaAxis * (180 / Math.PI);\n\t\t\tangleInDeg = Math.max(-maxA, Math.min(angleInDeg, maxA));\n\n\t\t\t/*\n\t\t\tif(Math.abs(angleInDeg) == maxA) {\n\n\t\t\t\tmoveDirection = moveDirectionCopy;\n\t\t\t\tconsole.log(12345,_moveCurr, _movePrev);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t*/\n\n\n\t\t\tif (angle) {\n\n\t\t\t\t_eye.copy(_this.object.position).sub(_this.target);\n\n\t\t\t\teyeDirection.copy(_eye).normalize();\n\t\t\t\tobjectUpDirection.copy(_this.object.up).normalize();\n\t\t\t\tobjectSidewaysDirection.crossVectors(objectUpDirection, eyeDirection).normalize();\n\n\t\t\t\tobjectUpDirection.setLength(_moveCurr.y - _movePrev.y);\n\t\t\t\tobjectSidewaysDirection.setLength(_moveCurr.x - _movePrev.x);\n\n\t\t\t\tif (this.locked) {\n\t\t\t\t\tmoveDirection.copy(objectSidewaysDirection);\n\t\t\t\t} else {\n\t\t\t\t\tmoveDirection.copy(objectUpDirection.add(objectSidewaysDirection));\n\t\t\t\t}\n\n\t\t\t\taxis.crossVectors(moveDirection, _eye).normalize();\n\n\t\t\t\tquaternion.setFromAxisAngle(axis, angle);\n\n\t\t\t\t_eye.applyQuaternion(quaternion);\n\t\t\t\tif (!this.locked) _this.object.up.applyQuaternion(quaternion);\n\n\t\t\t\t_lastAxis.copy(axis);\n\t\t\t\t_lastAngle = angle;\n\n\t\t\t} else if (!_this.staticMoving && _lastAngle) {\n\n\t\t\t\t_lastAngle *= Math.sqrt(1.0 - _this.dynamicDampingFactor);\n\t\t\t\t_eye.copy(_this.object.position).sub(_this.target);\n\t\t\t\tquaternion.setFromAxisAngle(_lastAxis, _lastAngle);\n\t\t\t\t_eye.applyQuaternion(quaternion);\n\t\t\t\t_this.object.up.applyQuaternion(quaternion);\n\n\t\t\t}\n\n\t\t\t_movePrev.copy(_moveCurr);\n\n\t\t}\n\t\treturn rotateCamera;\n\n\n\n\t}());\n\n\n\tthis.zoomCamera = function () {\n\n\t\tvar factor;\n\n\t\tif (_state === STATE.TOUCH_ZOOM_PAN) {\n\n\t\t\tfactor = _touchZoomDistanceStart / _touchZoomDistanceEnd;\n\t\t\t_touchZoomDistanceStart = _touchZoomDistanceEnd;\n\t\t\t_eye.multiplyScalar(factor);\n\n\t\t} else {\n\n\t\t\tfactor = 1.0 + (_zoomEnd.y - _zoomStart.y) * _this.zoomSpeed;\n\n\t\t\tif (factor !== 1.0 && factor > 0.0) {\n\n\t\t\t\t_eye.multiplyScalar(factor);\n\n\t\t\t}\n\n\t\t\tif (_this.staticMoving) {\n\n\t\t\t\t_zoomStart.copy(_zoomEnd);\n\n\t\t\t} else {\n\n\t\t\t\t_zoomStart.y += (_zoomEnd.y - _zoomStart.y) * this.dynamicDampingFactor;\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n\tthis.panCamera = (function () {\n\n\t\tvar mouseChange = new THREE.Vector2(),\n\t\t\tobjectUp = new THREE.Vector3(),\n\t\t\tpan = new THREE.Vector3();\n\n\t\treturn function panCamera() {\n\n\t\t\tmouseChange.copy(_panEnd).sub(_panStart);\n\n\t\t\tif (mouseChange.lengthSq()) {\n\n\t\t\t\tmouseChange.multiplyScalar(_eye.length() * _this.panSpeed);\n\n\t\t\t\tpan.copy(_eye).cross(_this.object.up).setLength(mouseChange.x);\n\t\t\t\tpan.add(objectUp.copy(_this.object.up).setLength(mouseChange.y));\n\n\t\t\t\t_this.object.position.add(pan);\n\t\t\t\t_this.target.add(pan);\n\n\t\t\t\tif (_this.staticMoving) {\n\n\t\t\t\t\t_panStart.copy(_panEnd);\n\n\t\t\t\t} else {\n\n\t\t\t\t\t_panStart.add(mouseChange.subVectors(_panEnd, _panStart).multiplyScalar(_this.dynamicDampingFactor));\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t};\n\n\t}());\n\n\tthis.checkDistances = function () {\n\n\t\tif (!_this.noZoom || !_this.noPan) {\n\n\t\t\tif (_eye.lengthSq() > _this.maxDistance * _this.maxDistance) {\n\n\t\t\t\t_this.object.position.addVectors(_this.target, _eye.setLength(_this.maxDistance));\n\t\t\t\t_zoomStart.copy(_zoomEnd);\n\n\t\t\t}\n\n\t\t\tif (_eye.lengthSq() < _this.minDistance * _this.minDistance) {\n\n\t\t\t\t_this.object.position.addVectors(_this.target, _eye.setLength(_this.minDistance));\n\t\t\t\t_zoomStart.copy(_zoomEnd);\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n\tthis.update = function () {\n\n\t\t_eye.subVectors(_this.object.position, _this.target);\n\n\t\tif (!_this.noRotate) {\n\n\t\t\t_this.rotateCamera();\n\n\t\t}\n\n\t\tif (!_this.noZoom) {\n\n\t\t\t_this.zoomCamera();\n\n\t\t}\n\n\t\tif (!_this.noPan) {\n\n\t\t\t_this.panCamera();\n\n\t\t}\n\n\t\t_this.object.position.addVectors(_this.target, _eye);\n\n\t\t_this.checkDistances();\n\n\t\t_this.object.lookAt(_this.target);\n\n\t\tif (lastPosition.distanceToSquared(_this.object.position) > EPS) {\n\n\t\t\t_this.dispatchEvent(changeEvent);\n\n\t\t\tlastPosition.copy(_this.object.position);\n\n\t\t}\n\n\t};\n\n\tthis.reset = function () {\n\n\t\t_state = STATE.NONE;\n\t\t_prevState = STATE.NONE;\n\n\t\t_this.target.copy(_this.target0);\n\t\t_this.object.position.copy(_this.position0);\n\t\t_this.object.up.copy(_this.up0);\n\n\t\t_eye.subVectors(_this.object.position, _this.target);\n\n\t\t_this.object.lookAt(_this.target);\n\n\t\t_this.dispatchEvent(changeEvent);\n\n\t\tlastPosition.copy(_this.object.position);\n\n\t};\n\n\t// helpers\n\n\t/**\n\t * Checks if the pressed key is any of the configured modifier keys for\n\t * a specified behavior.\n\t * \n\t * @param {number | number[]} keys \n\t * @param {number} key \n\t * \n\t * @returns {boolean} `true` if `keys` contains or equals `key`\n\t */\n\tfunction containsKey(keys, key) {\n\t\tif (Array.isArray(keys)) {\n\t\t\treturn keys.indexOf(key) !== -1;\n\t\t} else {\n\t\t\treturn keys === key;\n\t\t}\n\t}\n\n\t// listeners\n\n\tfunction keydown(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\twindow.removeEventListener('keydown', keydown);\n\n\t\t_prevState = _state;\n\n\t\tif (_state !== STATE.NONE) {\n\n\n\n\t\t} else if (containsKey(_this.keys[STATE.ROTATE], event.keyCode) && !_this.noRotate) {\n\n\t\t\t_state = STATE.ROTATE;\n\n\t\t} else if (containsKey(_this.keys[STATE.ZOOM], event.keyCode) && !_this.noZoom) {\n\n\t\t\t_state = STATE.ZOOM;\n\n\t\t} else if (containsKey(_this.keys[STATE.PAN], event.keyCode) && !_this.noPan) {\n\n\t\t\t_state = STATE.PAN;\n\n\t\t}\n\n\t}\n\n\tfunction keyup(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\t_state = _prevState;\n\n\t\twindow.addEventListener('keydown', keydown, false);\n\n\t}\n\n\tfunction mousedown(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tif (_state === STATE.NONE) {\n\n\t\t\t_state = event.button;\n\n\t\t}\n\n\t\tif (_state === STATE.ROTATE && !_this.noRotate) {\n\n\t\t\t_moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));\n\t\t\t_movePrev.copy(_moveCurr);\n\n\t\t} else if (_state === STATE.ZOOM && !_this.noZoom) {\n\n\t\t\t_zoomStart.copy(getMouseOnScreen(event.pageX, event.pageY));\n\t\t\t_zoomEnd.copy(_zoomStart);\n\n\t\t} else if (_state === STATE.PAN && !_this.noPan) {\n\n\t\t\t_panStart.copy(getMouseOnScreen(event.pageX, event.pageY));\n\t\t\t_panEnd.copy(_panStart);\n\n\t\t}\n\n\t\tdocument.addEventListener('mousemove', mousemove, false);\n\t\tdocument.addEventListener('mouseup', mouseup, false);\n\n\t\t_this.dispatchEvent(startEvent);\n\n\t}\n\n\tfunction mousemove(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tif (_state === STATE.ROTATE && !_this.noRotate) {\n\n\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t_moveCurr.copy(getMouseOnCircle(event.pageX, event.pageY));\n\n\t\t} else if (_state === STATE.ZOOM && !_this.noZoom) {\n\n\t\t\t_zoomEnd.copy(getMouseOnScreen(event.pageX, event.pageY));\n\n\t\t} else if (_state === STATE.PAN && !_this.noPan) {\n\n\t\t\t_panEnd.copy(getMouseOnScreen(event.pageX, event.pageY));\n\n\t\t}\n\n\t}\n\n\tfunction mouseup(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\t_state = STATE.NONE;\n\n\t\tdocument.removeEventListener('mousemove', mousemove);\n\t\tdocument.removeEventListener('mouseup', mouseup);\n\t\t_this.dispatchEvent(endEvent);\n\n\t}\n\n\tfunction mousewheel(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tswitch (event.deltaMode) {\n\n\t\t\tcase 2:\n\t\t\t\t// Zoom in pages\n\t\t\t\t_zoomStart.y -= event.deltaY * 0.025;\n\t\t\t\tbreak;\n\n\t\t\tcase 1:\n\t\t\t\t// Zoom in lines\n\t\t\t\t_zoomStart.y -= event.deltaY * 0.01;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\t// undefined, 0, assume pixels\n\t\t\t\t_zoomStart.y -= event.deltaY * 0.00025;\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t_this.dispatchEvent(startEvent);\n\t\t_this.dispatchEvent(endEvent);\n\n\t}\n\n\tfunction touchstart(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase 1:\n\t\t\t\t_state = STATE.TOUCH_ROTATE;\n\t\t\t\t_moveCurr.copy(getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n\t\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t\tbreak;\n\n\t\t\tdefault: // 2 or more\n\t\t\t\t_state = STATE.TOUCH_ZOOM_PAN;\n\t\t\t\tvar dx = event.touches[0].pageX - event.touches[1].pageX;\n\t\t\t\tvar dy = event.touches[0].pageY - event.touches[1].pageY;\n\t\t\t\t_touchZoomDistanceEnd = _touchZoomDistanceStart = Math.sqrt(dx * dx + dy * dy);\n\n\t\t\t\tvar x = (event.touches[0].pageX + event.touches[1].pageX) / 2;\n\t\t\t\tvar y = (event.touches[0].pageY + event.touches[1].pageY) / 2;\n\t\t\t\t_panStart.copy(getMouseOnScreen(x, y));\n\t\t\t\t_panEnd.copy(_panStart);\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t_this.dispatchEvent(startEvent);\n\n\t}\n\n\tfunction touchmove(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase 1:\n\t\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t\t_moveCurr.copy(getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n\t\t\t\tbreak;\n\n\t\t\tdefault: // 2 or more\n\t\t\t\tvar dx = event.touches[0].pageX - event.touches[1].pageX;\n\t\t\t\tvar dy = event.touches[0].pageY - event.touches[1].pageY;\n\t\t\t\t_touchZoomDistanceEnd = Math.sqrt(dx * dx + dy * dy);\n\n\t\t\t\tvar x = (event.touches[0].pageX + event.touches[1].pageX) / 2;\n\t\t\t\tvar y = (event.touches[0].pageY + event.touches[1].pageY) / 2;\n\t\t\t\t_panEnd.copy(getMouseOnScreen(x, y));\n\t\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\tfunction touchend(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase 0:\n\t\t\t\t_state = STATE.NONE;\n\t\t\t\tbreak;\n\n\t\t\tcase 1:\n\t\t\t\t_state = STATE.TOUCH_ROTATE;\n\t\t\t\t_moveCurr.copy(getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY));\n\t\t\t\t_movePrev.copy(_moveCurr);\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t_this.dispatchEvent(endEvent);\n\n\t}\n\n\tfunction contextmenu(event) {\n\n\t\tif (_this.enabled === false) return;\n\n\t\tevent.preventDefault();\n\n\t}\n\n\tthis.dispose = function () {\n\n\t\tthis.domElement.removeEventListener('contextmenu', contextmenu, false);\n\t\tthis.domElement.removeEventListener('mousedown', mousedown, false);\n\t\tthis.domElement.removeEventListener('wheel', mousewheel, false);\n\n\t\tthis.domElement.removeEventListener('touchstart', touchstart, false);\n\t\tthis.domElement.removeEventListener('touchend', touchend, false);\n\t\tthis.domElement.removeEventListener('touchmove', touchmove, false);\n\n\t\tdocument.removeEventListener('mousemove', mousemove, false);\n\t\tdocument.removeEventListener('mouseup', mouseup, false);\n\n\t\twindow.removeEventListener('keydown', keydown, false);\n\t\twindow.removeEventListener('keyup', keyup, false);\n\n\t};\n\n\tthis.domElement.addEventListener('contextmenu', contextmenu, false);\n\tthis.domElement.addEventListener('mousedown', mousedown, false);\n\tthis.domElement.addEventListener('wheel', mousewheel, false);\n\n\tthis.domElement.addEventListener('touchstart', touchstart, false);\n\tthis.domElement.addEventListener('touchend', touchend, false);\n\tthis.domElement.addEventListener('touchmove', touchmove, false);\n\n\twindow.addEventListener('keydown', keydown, false);\n\twindow.addEventListener('keyup', keyup, false);\n\n\tthis.handleResize();\n\n\t// force an update at start\n\tthis.update();\n\n};\n\nfunction preventEvent(event) { event.preventDefault(); }\n\nTrackballControls.prototype = Object.create(THREE.EventDispatcher.prototype);\n\n\nexport { TrackballControls };", "import * as THREE from 'three';\n\nimport { softShadowsTexturePass } from 'raytracer/build';\n\n\nimport { TrackballControls } from './camera.js';\n\n\nwindow.THREE = THREE;\n\nvar ASSETS_PATH = window.location.href.indexOf('localhost') > -1 ? '' : '/r_static/webdesigner/';\n\nvar createGeometry = require('three-bmfont-text');\nvar loadFont = require('load-bmfont');\nvar MSDFShader = require('three-bmfont-text/shaders/msdf');\n\n\nlet ConfiguratorData = function () {\n    this.horizontals = true;\n    this.verticals = true;\n    this.supports = true;\n    this.backs = true;\n    this.doors = true;\n    this.drawers = true;\n    this.fills = true;\n    this.legs = true;\n    this.accessories = true;\n    this.spacer = true;\n    this.colorMode = 0;\n    this.filterMaterialKey = '';\n    this.filterMaterial = '';\n    this.filterEnable = false\n};\n\nvar conf = new ConfiguratorData();\n\nclass ProductionRenderer {\n\n    constructor({ container, width, height, cameraLock, updatedCameraCallback }) {\n        this.controls = null;\n        this.cameraLock = cameraLock;\n        this.container = container;\n        if (container) this.init(width, height)\n    }\n\n    updatedCamera() {\n        console.log(\"camera-updates\");\n        if (this.updatedCameraCallbackFunction) this.updatedCameraCallbackFunction();\n    }\n\n    set({ container, width, height, cameraLock, updatedCameraCallback }) {\n\n        //  this.controls = null;\n        this.cameraLock = cameraLock;\n        this.container = container;\n        this.init(width, height);\n        this.resize({ width, height });\n        this.updatedCameraCallbackFunction = updatedCameraCallback;\n\n        return this;\n\n    }\n\n    resize({ width, height }) {\n        this.camera.aspect = width / height;\n        this.camera.updateProjectionMatrix();\n        this.renderer.setSize(width, height);\n        this.renderer.domElement.style.width = `${width}px`;\n        this.renderer.domElement.style.height = `${height}px`;\n    }\n\n    rotateScene({ angle }) {\n        console.log(angle);\n    }\n\n    create(json, opaque, solid) {\n        if (json == null) return this.flush();\n        let filtered_elements = this.filterElements(json.item.elements, conf);\n\n        this.drawElements(filtered_elements, opaque, solid);\n        this.render();\n    }\n\n    flush() {\n        this.resetItems();\n    }\n\n    addInfo(text) {\n\n        let self = this;\n        var textureLoader = new THREE.TextureLoader();\n\n        return new Promise((resolve) => {\n\n            loadFont(ASSETS_PATH + 'fonts/inter-msdf/font.json', function (err, font) {\n\n                var geometry = createGeometry({\n                    width: 300,\n                    align: 'left',\n                    font: font,\n                    multipage: true\n                });\n\n                geometry.update(text);\n\n                textureLoader.load(ASSETS_PATH + 'fonts/inter-msdf/sheet0.png', function (texture) {\n\n                    var material = new THREE.RawShaderMaterial(MSDFShader({\n                        map: texture,\n                        precision: 'highp',\n                        alphaTest: 0.00000001,\n                        side: THREE.DoubleSide,\n                        transparent: true,\n                        color: 'rgb(230, 230, 230)'\n                    }));\n\n                    // now do something with our mesh!\n                    var mesh = new THREE.Mesh(geometry, material);\n                    self.scene.add(mesh);\n                    mesh.scale.set(1, -1, 1);\n\n                    resolve(mesh);\n                });\n            });\n        });\n    }\n\n    fitCameraToObject(camera, object, offset, controls) {\n\n        offset = offset || 1.25;\n\n        const boundingBox = new THREE.Box3();\n\n        // get bounding box of object - this will be used to setup controls and camera\n        boundingBox.setFromObject(object);\n\n        const center = boundingBox.getCenter();\n\n        const size = boundingBox.getSize();\n\n        // get the max side of the bounding box (fits to width OR height as needed )\n        const maxDim = Math.max(size.x, size.y, size.z);\n        const fov = camera.fov * (Math.PI / 180);\n        let cameraZ = Math.abs(maxDim / 4 * Math.tan(fov * 2));\n\n        cameraZ *= offset; // zoom out a little so that objects don't fill the screen\n\n        camera.position.z = cameraZ;\n\n        const minZ = boundingBox.min.z;\n        const cameraToFarEdge = (minZ < 0) ? -minZ + cameraZ : cameraZ - minZ;\n\n        camera.far = cameraToFarEdge * 3;\n        camera.updateProjectionMatrix();\n\n        if (controls) {\n\n            // set camera to rotate around center of loaded object\n            controls.target = center;\n\n            // prevent camera from zooming out far enough to create far plane cutoff\n            controls.maxDistance = cameraToFarEdge * 2;\n\n            controls.saveState();\n\n        } else {\n\n            camera.lookAt(center)\n\n        }\n    }\n\n    fitOrtho() {\n\n\n        var box = new THREE.Box3().setFromObject(this.scene);\n        //  .. var s =  new THREE\n        // this.scene.getSize(s);\n\n        let offsetHeight = this.canvasAbsoluteHeight;\n        let offsetWidth = this.canvasAbsoluteWidth;\n\n        var camera = new THREE.OrthographicCamera(offsetWidth / -2, offsetWidth / 2, offsetHeight / 2, offsetHeight / -2, 0, 100);\n\n        var box = new THREE.Box3().setFromObject(this.scene);\n        // box.center(this.scene.position);\n        //  this.scene.localToWorld(box);\n        this.scene.position.multiplyScalar(-1);\n\n        camera.zoom = Math.min(offsetWidth / (box.max.x - box.min.x),\n            offsetHeight / (box.max.y - box.min.y)) * 0.9;\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n\n        //  console.log(box);\n\n        //    this.fitCameraToObject()\n\n        return camera;\n    }\n\n    init(width, height) {\n        let container = this.container;\n\n        if (this.renderer) {\n            container.appendChild(this.renderer.domElement);\n\n            //animate();\n\n            return;\n        }\n\n        let camera, scene;\n\n        let canvasAbsoluteWidth, canvasAbsoluteHeight;\n\n        //renderjson.set_show_to_level(2);\n        this.canvasAbsoluteHeight = canvasAbsoluteHeight = height;\n        this.canvasAbsoluteWidth = canvasAbsoluteWidth = width;\n\n        //scontainer = document.getElementById('webgl');\n        var renderer = new THREE.WebGLRenderer({\n            antialias: true,\n            preserveDrawingBuffer: true,\n            alpha: true\n        });\n        // sortObjects: false});\n        this.renderer = renderer;\n        renderer.setPixelRatio(window.devicePixelRatio);\n        renderer.setSize(canvasAbsoluteWidth, canvasAbsoluteHeight);\n        container.appendChild(renderer.domElement);\n\n        this.camera = camera = new THREE.PerspectiveCamera(20, width / height, 1, 20000);\n\n\n        camera.position.z = 12000;\n        camera.position.x = 400;\n        camera.position.y = 800;\n\n        let controls = this.controls = new TrackballControls(this.camera, renderer.domElement, this.cameraLock);\n        controls.rotateSpeed = 1.0;\n        controls.zoomSpeed = 1.2;\n        controls.panSpeed = 0.8;\n        controls.noZoom = false;\n        controls.noPan = false;\n        controls.staticMoving = true;\n        controls.dynamicDampingFactor = 0.3;\n        controls.target = new THREE.Vector3(200, 250, 0);\n\n\n        controls.addEventListener('change', () => {\n            this.updatedCamera();\n\n\n        });\n\n\n        // world\n        this.scene = scene = new THREE.Object3D();\n        this._scene = new THREE.Scene();\n        this._scene.add(this.scene);\n\n        scene.background = new THREE.Color(0x101010);\n\n        var gridHelper = new THREE.GridHelper(4400, 100, 0x777777, 0x444444);\n        gridHelper.position.y = 0;\n        gridHelper.position.x = 0;\n        scene.add(gridHelper);\n\n        scene.rotation.y = 0.5;\n\n\n        this.items = [];\n        this.labels = [];\n\n        const animate = () => {\n            this.controls.update();\n\n            this.render();\n            requestAnimationFrame(animate);\n\n        };\n\n        animate();\n\n\n    }\n\n    filterElements(elements, filter_conf) {\n        return elements.filter(x => {\n            return (\n                (x['elem_type'] === 'H' && filter_conf['horizontals']) ||\n                (x['elem_type'] === 'V' && filter_conf['verticals']) ||\n                (x['elem_type'] === 'S' && filter_conf['supports']) ||\n                (x['elem_type'] === 'B' && filter_conf['backs']) ||\n                (x['elem_type'] === 'D' && filter_conf['doors']) ||\n                (x['elem_type'] === 'O') ||\n                (x['elem_type'] === 'M') ||\n                (x['elem_type'] === 'L' && filter_conf['legs']) ||\n                (x['elem_type'] === 'T' && filter_conf['drawers']) ||\n                (x['elem_type'] === 'FILL' && filter_conf['fills']) ||\n                (x['elem_type'] === 'ACC' && filter_conf['accessories']) ||\n                (x['elem_type'] === 'SPACER' && filter_conf['spacer']))\n        });\n    }\n\n    render() {\n\n\n        this.renderer.render(this._scene, this.camera);\n\n\n    }\n\n    getCompoundBoundingBox(object3D) {\n        var box = null;\n        object3D.traverse(function (obj3D) {\n            var geometry = obj3D.geometry;\n            if (geometry === undefined) return;\n            geometry.computeBoundingBox();\n            if (box === null) {\n                box = geometry.boundingBox;\n            } else {\n                box.union(geometry.boundingBox);\n            }\n        });\n        return box;\n    }\n\n    selectElement(configurationId) {\n        this.groups.map((g, c) => {\n            g.element.visible = c == configurationId;\n        });\n    }\n\n    drawElements(elements, opaque, solid) {\n\n        this.resetItems();\n\n        let scene = this.scene;\n        let items = this.items;\n        let labels = this.labels;\n\n        this.groups = [];\n\n        for (let i = 0; i < elements.length; i++) {\n            let main_item = elements[i];\n\n            if (main_item.components == null) {\n                continue;\n            }\n\n            let centers = [\n                (main_item.x_domain[1] + main_item.x_domain[0]) / 200,\n                (main_item.y_domain[1] + main_item.y_domain[0]) / 200,\n                (main_item.z_domain[1] + main_item.z_domain[0]) / 200,\n            ];\n\n            let sizes = [\n                (main_item.x_domain[1] - main_item.x_domain[0]) / 100,\n                (main_item.y_domain[1] - main_item.y_domain[0]) / 100,\n                (main_item.z_domain[1] - main_item.z_domain[0]) / 100,\n            ];\n\n            for (\n                let j = 0;\n                j < Object.values(main_item.components).length;\n                j++\n            ) {\n\n                let item = Object.values(main_item.components)[j];\n                if (conf.filterEnable == true) {\n                    if (!((item[conf.filterMaterialKey] || 'missing')\n                        .toString()\n                        .indexOf(conf.filterMaterial) > -1)\n                    ) {\n                        continue;\n                    }\n                }\n\n                let sizes = [\n                    (item.x_domain[1] - item.x_domain[0]) / 100,\n                    (item.y_domain[1] - item.y_domain[0]) / 100,\n                    (item.z_domain[1] - item.z_domain[0]) / 100,\n                ];\n\n                let centers = [\n                    (item.x_domain[1] + item.x_domain[0]) / 200,\n                    (item.y_domain[1] + item.y_domain[0]) / 200,\n                    (item.z_domain[1] + item.z_domain[0]) / 200,\n                ];\n\n                var geometry = (main_item.elem_type == 'L') ? new THREE.CylinderGeometry(sizes[0] / 2, sizes[0] / 2, sizes[1], 12, 2) : new THREE.BoxGeometry(sizes[0], sizes[1], sizes[2]);\n                var material = new THREE.MeshBasicMaterial({\n                    color: 0x999999, wireframe: false, transparent: true,\n                    polygonOffset: true,\n                    polygonOffsetFactor: 1,\n                    polygonOffsetUnits: 1,\n                    opacity: solid ? 0.95 : 0.5\n                });\n\n\n                var cube = new THREE.Mesh(geometry, material);\n                cube.position.x = centers[0];\n                cube.position.y = centers[1];\n                cube.position.z = centers[2];\n                cube.item_data = main_item;\n\n                var geo = new THREE.EdgesGeometry(cube.geometry);\n                var mat = new THREE.LineBasicMaterial({\n                    color: opaque ? 0xaaaaaa : 0xffffff,\n                    linewidth: 1\n                });\n                var wireframe = new THREE.LineSegments(geo, solid ? material : mat);\n\n                cube.add(wireframe);\n                scene.add(cube);\n                items.push(cube);\n\n                if (!this.groups[main_item.c_config_id]) {\n                    this.groups[main_item.c_config_id] = { group: new THREE.Group(), pos: new THREE.Vector3() };\n                }\n\n                this.groups[main_item.c_config_id].group.add(cube.clone());\n                this.groups[main_item.c_config_id].pos.x = centers[0];\n                this.groups[main_item.c_config_id].pos.y = centers[1];\n                this.groups[main_item.c_config_id].pos.z = centers[2];\n\n            }\n\n            if (main_item.surname != '') {\n                var spritey = this.makeTextSprite(`${main_item.surname}`,\n                    {\n                        fontsize: 70,\n                        borderColor: { r: 255, g: 0, b: 0, a: 1.0 },\n                        backgroundColor: { r: 255, g: 100, b: 100, a: 0.2 },\n                    });\n\n                this.addInfo(`${main_item.surname}`).then((mesh) => {\n                    mesh.position.set(\n                        centers[0] - 150,\n                        centers[1],\n                        centers[2] + sizes[2] / 2);\n                    scene.add(mesh);\n                    labels.push(mesh);\n                });\n            }\n        }\n\n\n        this.groups.map((group, id) => {\n            var materialSelect = new THREE.MeshBasicMaterial({\n                color: 0x0051ff, wireframe: false, transparent: true,\n                polygonOffset: true,\n                polygonOffsetFactor: 1,\n                polygonOffsetUnits: 1,\n                opacity: 0.65\n            });\n\n            let bb = this.getCompoundBoundingBox(group.group);\n            let bbExpanded = this.getCompoundBoundingBox(group.group);\n            var size = new THREE.Vector3();\n            var sizeExpanded = new THREE.Vector3();\n            var center = new THREE.Vector3();\n\n            bbExpanded.expandByScalar(25);\n            bbExpanded.getSize(sizeExpanded);\n            bb.getSize(size);\n\n\n            var geometrySelect = new THREE.BoxGeometry(sizeExpanded.x, sizeExpanded.y, sizeExpanded.z);\n            var cubeSelect = new THREE.Mesh(geometrySelect, materialSelect);\n\n            cubeSelect.visible = false;\n            cubeSelect.position.x = group.pos.x;\n            cubeSelect.position.y = group.pos.y - size.y / 2 + 15;\n            cubeSelect.position.z = group.pos.z;\n            scene.add(cubeSelect);\n            items.push(cubeSelect);\n            group.element = cubeSelect;\n\n        });\n    }\n\n    resetScene(whiteBackground) {\n        if (whiteBackground) {\n            this._scene.background = new THREE.Color(0xf0f0f0);\n            this._scene.opacity = 1;\n        } else {\n            this._scene.background = new THREE.Color(0x101010);\n            this._scene.opacity = 0.5;\n\n        }\n\n\n        var gridHelper = new THREE.GridHelper(4400, 100, 0x777777, 0x444444);\n        gridHelper.position.y = 0;\n        gridHelper.position.x = 0;\n        this.scene.add(gridHelper);\n    }\n\n    resetItems() {\n        let scene = this.scene;\n        let labels = this.labels;\n        let groups = this.groups ? this.groups : [];\n        for (let i = 0; i < this.items.length; i++) {\n            scene.remove(this.items[i]);\n        }\n        this.items = [];\n        for (let i = 0; i < this.labels.length; i++) {\n            scene.remove(this.labels[i]);\n        }\n        this.labels = [];\n        for (let i = 0; i < groups.length; i++) {\n            if (groups[i]) scene.remove(groups[i].group);\n        }\n        this.groups = [];\n    }\n\n    resetCamera() {\n        this.controls.reset();\n    }\n\n    makeTextSprite(message, parameters) {\n        if (parameters === undefined) parameters = {};\n\n        var fontface = parameters.hasOwnProperty(\"fontface\") ? parameters[\"fontface\"] : \"Arial\";\n        var fontsize = parameters.hasOwnProperty(\"fontsize\") ? parameters[\"fontsize\"] : 18;\n        var borderThickness = parameters.hasOwnProperty(\"borderThickness\") ? parameters[\"borderThickness\"] : 4;\n        var borderColor = parameters.hasOwnProperty(\"borderColor\") ? parameters[\"borderColor\"] : { r: 0, g: 0, b: 0, a: 1.0 };\n        var backgroundColor = parameters.hasOwnProperty(\"backgroundColor\") ? parameters[\"backgroundColor\"] : { r: 255, g: 255, b: 255, a: 1.0 };\n\n        var canvas = document.createElement('canvas');\n\n        if (message.length > 10) {\n            canvas.width = message.length * 35;\n        }\n\n        var context = canvas.getContext('2d');\n        context.font = \"Bold \" + fontsize + \"px \" + fontface;\n\n        var metrics = context.measureText(message);\n        // console.log('this text has ', message, metrics, canvas.width);\n        var textWidth = metrics.width;\n\n        // background color\n        context.fillStyle = \"rgba(\" + backgroundColor.r + \",\" + backgroundColor.g + \",\"\n            + backgroundColor.b + \",\" + backgroundColor.a + \")\";\n        // border color\n        context.strokeStyle = \"rgba(\" + borderColor.r + \",\" + borderColor.g + \",\"\n            + borderColor.b + \",\" + borderColor.a + \")\";\n\n        context.lineWidth = borderThickness;\n\n        context.fillStyle = \"rgba(255, 255, 2555, 1.0)\";\n        context.fillText(message, borderThickness, fontsize + borderThickness);\n\n        var texture = new THREE.Texture(canvas);\n        texture.needsUpdate = true;\n\n        var spriteMaterial = new THREE.SpriteMaterial({ map: texture });\n        var sprite = new THREE.Sprite(spriteMaterial);\n        sprite.scale.set(canvas.width == 300 ? 200 : 450, canvas.width == 300 ? 100 : 0.016 * canvas.width, 1.0);\n        return sprite;\n    }\n}\n\n\nvar productionRenderer = new ProductionRenderer({});\n\nexport { ProductionRenderer, productionRenderer }", "import mod from \"-!../../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMenu.vue?vue&type=style&index=0&id=6ac1de8d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMenu.vue?vue&type=style&index=0&id=6ac1de8d&lang=scss&scoped=true&\"", "var dtype = require('dtype')\nvar anArray = require('an-array')\nvar isBuffer = require('is-buffer')\n\nvar CW = [0, 2, 3]\nvar CCW = [2, 1, 3]\n\nmodule.exports = function createQuadElements(array, opt) {\n    //if user didn't specify an output array\n    if (!array || !(anArray(array) || isBuffer(array))) {\n        opt = array || {}\n        array = null\n    }\n\n    if (typeof opt === 'number') //backwards-compatible\n        opt = { count: opt }\n    else\n        opt = opt || {}\n\n    var type = typeof opt.type === 'string' ? opt.type : 'uint16'\n    var count = typeof opt.count === 'number' ? opt.count : 1\n    var start = (opt.start || 0) \n\n    var dir = opt.clockwise !== false ? CW : CCW,\n        a = dir[0], \n        b = dir[1],\n        c = dir[2]\n\n    var numIndices = count * 6\n\n    var indices = array || new (dtype(type))(numIndices)\n    for (var i = 0, j = 0; i < numIndices; i += 6, j += 4) {\n        var x = i + start\n        indices[x + 0] = j + 0\n        indices[x + 1] = j + 1\n        indices[x + 2] = j + 2\n        indices[x + 3] = j + a\n        indices[x + 4] = j + b\n        indices[x + 5] = j + c\n    }\n    return indices\n}", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewColors.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewColors.vue?vue&type=style&index=0&lang=scss&\"", "module.exports = function numtype(num, def) {\n\treturn typeof num === 'number'\n\t\t? num \n\t\t: (typeof def === 'number' ? def : 0)\n}", "'use strict';\n\nvar bind = require('function-bind');\n\nmodule.exports = bind.call(Function.call, Object.prototype.hasOwnProperty);\n", "module.exports.pages = function pages (glyphs) {\n  var pages = new Float32Array(glyphs.length * 4 * 1)\n  var i = 0\n  glyphs.forEach(function (glyph) {\n    var id = glyph.data.page || 0\n    pages[i++] = id\n    pages[i++] = id\n    pages[i++] = id\n    pages[i++] = id\n  })\n  return pages\n}\n\nmodule.exports.uvs = function uvs (glyphs, texWidth, texHeight, flipY) {\n  var uvs = new Float32Array(glyphs.length * 4 * 2)\n  var i = 0\n  glyphs.forEach(function (glyph) {\n    var bitmap = glyph.data\n    var bw = (bitmap.x + bitmap.width)\n    var bh = (bitmap.y + bitmap.height)\n\n    // top left position\n    var u0 = bitmap.x / texWidth\n    var v1 = bitmap.y / texHeight\n    var u1 = bw / texWidth\n    var v0 = bh / texHeight\n\n    if (flipY) {\n      v1 = (texHeight - bitmap.y) / texHeight\n      v0 = (texHeight - bh) / texHeight\n    }\n\n    // BL\n    uvs[i++] = u0\n    uvs[i++] = v1\n    // TL\n    uvs[i++] = u0\n    uvs[i++] = v0\n    // TR\n    uvs[i++] = u1\n    uvs[i++] = v0\n    // BR\n    uvs[i++] = u1\n    uvs[i++] = v1\n  })\n  return uvs\n}\n\nmodule.exports.positions = function positions (glyphs) {\n  var positions = new Float32Array(glyphs.length * 4 * 2)\n  var i = 0\n  glyphs.forEach(function (glyph) {\n    var bitmap = glyph.data\n\n    // bottom left position\n    var x = glyph.position[0] + bitmap.xoffset\n    var y = glyph.position[1] + bitmap.yoffset\n\n    // quad size\n    var w = bitmap.width\n    var h = bitmap.height\n\n    // BL\n    positions[i++] = x\n    positions[i++] = y\n    // TL\n    positions[i++] = x\n    positions[i++] = y + h\n    // TR\n    positions[i++] = x + w\n    positions[i++] = y + h\n    // BR\n    positions[i++] = x + w\n    positions[i++] = y\n  })\n  return positions\n}\n", "import mod from \"-!../../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoAddToCart.vue?vue&type=style&index=0&id=faeb3fda&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoAddToCart.vue?vue&type=style&index=0&id=faeb3fda&lang=scss&scoped=true&\"", "'use strict';\n\nvar keysShim;\nif (!Object.keys) {\n\t// modified from https://github.com/es-shims/es5-shim\n\tvar has = Object.prototype.hasOwnProperty;\n\tvar toStr = Object.prototype.toString;\n\tvar isArgs = require('./isArguments'); // eslint-disable-line global-require\n\tvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\tvar hasDontEnumBug = !isEnumerable.call({ toString: null }, 'toString');\n\tvar hasProtoEnumBug = isEnumerable.call(function () {}, 'prototype');\n\tvar dontEnums = [\n\t\t'toString',\n\t\t'toLocaleString',\n\t\t'valueOf',\n\t\t'hasOwnProperty',\n\t\t'isPrototypeOf',\n\t\t'propertyIsEnumerable',\n\t\t'constructor'\n\t];\n\tvar equalsConstructorPrototype = function (o) {\n\t\tvar ctor = o.constructor;\n\t\treturn ctor && ctor.prototype === o;\n\t};\n\tvar excludedKeys = {\n\t\t$applicationCache: true,\n\t\t$console: true,\n\t\t$external: true,\n\t\t$frame: true,\n\t\t$frameElement: true,\n\t\t$frames: true,\n\t\t$innerHeight: true,\n\t\t$innerWidth: true,\n\t\t$outerHeight: true,\n\t\t$outerWidth: true,\n\t\t$pageXOffset: true,\n\t\t$pageYOffset: true,\n\t\t$parent: true,\n\t\t$scrollLeft: true,\n\t\t$scrollTop: true,\n\t\t$scrollX: true,\n\t\t$scrollY: true,\n\t\t$self: true,\n\t\t$webkitIndexedDB: true,\n\t\t$webkitStorageInfo: true,\n\t\t$window: true\n\t};\n\tvar hasAutomationEqualityBug = (function () {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined') { return false; }\n\t\tfor (var k in window) {\n\t\t\ttry {\n\t\t\t\tif (!excludedKeys['$' + k] && has.call(window, k) && window[k] !== null && typeof window[k] === 'object') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tequalsConstructorPrototype(window[k]);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}());\n\tvar equalsConstructorPrototypeIfNotBuggy = function (o) {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined' || !hasAutomationEqualityBug) {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t}\n\t\ttry {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t};\n\n\tkeysShim = function keys(object) {\n\t\tvar isObject = object !== null && typeof object === 'object';\n\t\tvar isFunction = toStr.call(object) === '[object Function]';\n\t\tvar isArguments = isArgs(object);\n\t\tvar isString = isObject && toStr.call(object) === '[object String]';\n\t\tvar theKeys = [];\n\n\t\tif (!isObject && !isFunction && !isArguments) {\n\t\t\tthrow new TypeError('Object.keys called on a non-object');\n\t\t}\n\n\t\tvar skipProto = hasProtoEnumBug && isFunction;\n\t\tif (isString && object.length > 0 && !has.call(object, 0)) {\n\t\t\tfor (var i = 0; i < object.length; ++i) {\n\t\t\t\ttheKeys.push(String(i));\n\t\t\t}\n\t\t}\n\n\t\tif (isArguments && object.length > 0) {\n\t\t\tfor (var j = 0; j < object.length; ++j) {\n\t\t\t\ttheKeys.push(String(j));\n\t\t\t}\n\t\t} else {\n\t\t\tfor (var name in object) {\n\t\t\t\tif (!(skipProto && name === 'prototype') && has.call(object, name)) {\n\t\t\t\t\ttheKeys.push(String(name));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (hasDontEnumBug) {\n\t\t\tvar skipConstructor = equalsConstructorPrototypeIfNotBuggy(object);\n\n\t\t\tfor (var k = 0; k < dontEnums.length; ++k) {\n\t\t\t\tif (!(skipConstructor && dontEnums[k] === 'constructor') && has.call(object, dontEnums[k])) {\n\t\t\t\t\ttheKeys.push(dontEnums[k]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn theKeys;\n\t};\n}\nmodule.exports = keysShim;\n", "'use strict';\n\nvar define = require('define-properties');\nvar getPolyfill = require('./polyfill');\n\nmodule.exports = function shimStringTrim() {\n\tvar polyfill = getPolyfill();\n\tdefine(String.prototype, { trim: polyfill }, { trim: function () { return String.prototype.trim !== polyfill; } });\n\treturn polyfill;\n};\n", "module.exports = function mod(number, modulo) {\n\tvar remain = number % modulo;\n\treturn Math.floor(remain >= 0 ? remain : remain + modulo);\n};\n", "var win;\n\nif (typeof window !== \"undefined\") {\n    win = window;\n} else if (typeof global !== \"undefined\") {\n    win = global;\n} else if (typeof self !== \"undefined\"){\n    win = self;\n} else {\n    win = {};\n}\n\nmodule.exports = win;\n", "'use strict';\n\nvar GetIntrinsic = require('../GetIntrinsic');\n\nvar $TypeError = GetIntrinsic('%TypeError%');\nvar $SyntaxError = GetIntrinsic('%SyntaxError%');\n\nvar has = require('has');\n\nvar predicates = {\n  // https://ecma-international.org/ecma-262/6.0/#sec-property-descriptor-specification-type\n  'Property Descriptor': function isPropertyDescriptor(ES, Desc) {\n    if (ES.Type(Desc) !== 'Object') {\n      return false;\n    }\n    var allowed = {\n      '[[Configurable]]': true,\n      '[[Enumerable]]': true,\n      '[[Get]]': true,\n      '[[Set]]': true,\n      '[[Value]]': true,\n      '[[Writable]]': true\n    };\n\n    for (var key in Desc) { // eslint-disable-line\n      if (has(Desc, key) && !allowed[key]) {\n        return false;\n      }\n    }\n\n    var isData = has(Desc, '[[Value]]');\n    var IsAccessor = has(Desc, '[[Get]]') || has(Desc, '[[Set]]');\n    if (isData && IsAccessor) {\n      throw new $TypeError('Property Descriptors may not be both accessor and data descriptors');\n    }\n    return true;\n  }\n};\n\nmodule.exports = function assertRecord(ES, recordType, argumentName, value) {\n  var predicate = predicates[recordType];\n  if (typeof predicate !== 'function') {\n    throw new $SyntaxError('unknown record type: ' + recordType);\n  }\n  if (!predicate(ES, value)) {\n    throw new $TypeError(argumentName + ' must be a ' + recordType);\n  }\n  console.log(predicate(ES, value), value);\n};\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./preview-page.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./preview-page.vue?vue&type=style&index=0&lang=scss&\"", "var $isNaN = Number.isNaN || function (a) { return a !== a; };\n\nmodule.exports = Number.isFinite || function (x) { return typeof x === 'number' && !$isNaN(x) && x !== Infinity && x !== -Infinity; };\n", "var HEADER = [66, 77, 70]\n\nmodule.exports = function readBMFontBinary(buf) {\n  if (buf.length < 6)\n    throw new Error('invalid buffer length for BMFont')\n\n  var header = HEADER.every(function(byte, i) {\n    return buf.readUInt8(i) === byte\n  })\n\n  if (!header)\n    throw new Error('<PERSON><PERSON><PERSON> missing BMF byte header')\n\n  var i = 3\n  var vers = buf.readUInt8(i++)\n  if (vers > 3)\n    throw new Error('Only supports BMFont Binary v3 (BMFont App v1.10)')\n  \n  var target = { kernings: [], chars: [] }\n  for (var b=0; b<5; b++)\n    i += readBlock(target, buf, i)\n  return target\n}\n\nfunction readBlock(target, buf, i) {\n  if (i > buf.length-1)\n    return 0\n\n  var blockID = buf.readUInt8(i++)\n  var blockSize = buf.readInt32LE(i)\n  i += 4\n\n  switch(blockID) {\n    case 1: \n      target.info = readInfo(buf, i)\n      break\n    case 2:\n      target.common = readCommon(buf, i)\n      break\n    case 3:\n      target.pages = readPages(buf, i, blockSize)\n      break\n    case 4:\n      target.chars = readChars(buf, i, blockSize)\n      break\n    case 5:\n      target.kernings = readKernings(buf, i, blockSize)\n      break\n  }\n  return 5 + blockSize\n}\n\nfunction readInfo(buf, i) {\n  var info = {}\n  info.size = buf.readInt16LE(i)\n\n  var bitField = buf.readUInt8(i+2)\n  info.smooth = (bitField >> 7) & 1\n  info.unicode = (bitField >> 6) & 1\n  info.italic = (bitField >> 5) & 1\n  info.bold = (bitField >> 4) & 1\n  \n  //fixedHeight is only mentioned in binary spec \n  if ((bitField >> 3) & 1)\n    info.fixedHeight = 1\n  \n  info.charset = buf.readUInt8(i+3) || ''\n  info.stretchH = buf.readUInt16LE(i+4)\n  info.aa = buf.readUInt8(i+6)\n  info.padding = [\n    buf.readInt8(i+7),\n    buf.readInt8(i+8),\n    buf.readInt8(i+9),\n    buf.readInt8(i+10)\n  ]\n  info.spacing = [\n    buf.readInt8(i+11),\n    buf.readInt8(i+12)\n  ]\n  info.outline = buf.readUInt8(i+13)\n  info.face = readStringNT(buf, i+14)\n  return info\n}\n\nfunction readCommon(buf, i) {\n  var common = {}\n  common.lineHeight = buf.readUInt16LE(i)\n  common.base = buf.readUInt16LE(i+2)\n  common.scaleW = buf.readUInt16LE(i+4)\n  common.scaleH = buf.readUInt16LE(i+6)\n  common.pages = buf.readUInt16LE(i+8)\n  var bitField = buf.readUInt8(i+10)\n  common.packed = 0\n  common.alphaChnl = buf.readUInt8(i+11)\n  common.redChnl = buf.readUInt8(i+12)\n  common.greenChnl = buf.readUInt8(i+13)\n  common.blueChnl = buf.readUInt8(i+14)\n  return common\n}\n\nfunction readPages(buf, i, size) {\n  var pages = []\n  var text = readNameNT(buf, i)\n  var len = text.length+1\n  var count = size / len\n  for (var c=0; c<count; c++) {\n    pages[c] = buf.slice(i, i+text.length).toString('utf8')\n    i += len\n  }\n  return pages\n}\n\nfunction readChars(buf, i, blockSize) {\n  var chars = []\n\n  var count = blockSize / 20\n  for (var c=0; c<count; c++) {\n    var char = {}\n    var off = c*20\n    char.id = buf.readUInt32LE(i + 0 + off)\n    char.x = buf.readUInt16LE(i + 4 + off)\n    char.y = buf.readUInt16LE(i + 6 + off)\n    char.width = buf.readUInt16LE(i + 8 + off)\n    char.height = buf.readUInt16LE(i + 10 + off)\n    char.xoffset = buf.readInt16LE(i + 12 + off)\n    char.yoffset = buf.readInt16LE(i + 14 + off)\n    char.xadvance = buf.readInt16LE(i + 16 + off)\n    char.page = buf.readUInt8(i + 18 + off)\n    char.chnl = buf.readUInt8(i + 19 + off)\n    chars[c] = char\n  }\n  return chars\n}\n\nfunction readKernings(buf, i, blockSize) {\n  var kernings = []\n  var count = blockSize / 10\n  for (var c=0; c<count; c++) {\n    var kern = {}\n    var off = c*10\n    kern.first = buf.readUInt32LE(i + 0 + off)\n    kern.second = buf.readUInt32LE(i + 4 + off)\n    kern.amount = buf.readInt16LE(i + 8 + off)\n    kernings[c] = kern\n  }\n  return kernings\n}\n\nfunction readNameNT(buf, offset) {\n  var pos=offset\n  for (; pos<buf.length; pos++) {\n    if (buf[pos] === 0x00) \n      break\n  }\n  return buf.slice(offset, pos)\n}\n\nfunction readStringNT(buf, offset) {\n  return readNameNT(buf, offset).toString('utf8')\n}", "var xhr = require('xhr')\nvar noop = function(){}\nvar parseASCII = require('parse-bmfont-ascii')\nvar parseXML = require('parse-bmfont-xml')\nvar readBinary = require('parse-bmfont-binary')\nvar isBinaryFormat = require('./lib/is-binary')\nvar xtend = require('xtend')\n\nvar xml2 = (function hasXML2() {\n  return self.XMLHttpRequest && \"withCredentials\" in new XMLHttpRequest\n})()\n\nmodule.exports = function(opt, cb) {\n  cb = typeof cb === 'function' ? cb : noop\n\n  if (typeof opt === 'string')\n    opt = { uri: opt }\n  else if (!opt)\n    opt = {}\n\n  var expectBinary = opt.binary\n  if (expectBinary)\n    opt = getBinaryOpts(opt)\n\n  xhr(opt, function(err, res, body) {\n    if (err)\n      return cb(err)\n    if (!/^2/.test(res.statusCode))\n      return cb(new Error('http status code: '+res.statusCode))\n    if (!body)\n      return cb(new Error('no body result'))\n\n    var binary = false \n\n    //if the response type is an array buffer,\n    //we need to convert it into a regular Buffer object\n    if (isArrayBuffer(body)) {\n      var array = new Uint8Array(body)\n      body = new Buffer(array, 'binary')\n    }\n\n    //now check the string/Buffer response\n    //and see if it has a binary BMF header\n    if (isBinaryFormat(body)) {\n      binary = true\n      //if we have a string, turn it into a Buffer\n      if (typeof body === 'string') \n        body = new Buffer(body, 'binary')\n    } \n\n    //we are not parsing a binary format, just ASCII/XML/etc\n    if (!binary) {\n      //might still be a buffer if responseType is 'arraybuffer'\n      if (Buffer.isBuffer(body))\n        body = body.toString(opt.encoding)\n      body = body.trim()\n    }\n\n    var result\n    try {\n      var type = res.headers['content-type']\n      if (binary)\n        result = readBinary(body)\n      else if (/json/.test(type) || body.charAt(0) === '{')\n        result = JSON.parse(body)\n      else if (/xml/.test(type)  || body.charAt(0) === '<')\n        result = parseXML(body)\n      else\n        result = parseASCII(body)\n    } catch (e) {\n      cb(new Error('error parsing font '+e.message))\n      cb = noop\n    }\n    cb(null, result)\n  })\n}\n\nfunction isArrayBuffer(arr) {\n  var str = Object.prototype.toString\n  return str.call(arr) === '[object ArrayBuffer]'\n}\n\nfunction getBinaryOpts(opt) {\n  //IE10+ and other modern browsers support array buffers\n  if (xml2)\n    return xtend(opt, { responseType: 'arraybuffer' })\n  \n  if (typeof self.XMLHttpRequest === 'undefined')\n    throw new Error('your browser does not support XHR loading')\n\n  //IE9 and XML1 browsers could still use an override\n  var req = new self.XMLHttpRequest()\n  req.overrideMimeType('text/plain; charset=x-user-defined')\n  return xtend({\n    xhr: req\n  }, opt)\n}\n", "'use strict';\n\nvar bind = require('function-bind');\nvar define = require('define-properties');\n\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\n\nvar boundTrim = bind.call(Function.call, getPolyfill());\n\ndefine(boundTrim, {\n\tgetPolyfill: getPolyfill,\n\timplementation: implementation,\n\tshim: shim\n});\n\nmodule.exports = boundTrim;\n", "'use strict';\n\nvar isCallable = require('is-callable');\n\nvar toStr = Object.prototype.toString;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar forEachArray = function forEachArray(array, iterator, receiver) {\n    for (var i = 0, len = array.length; i < len; i++) {\n        if (hasOwnProperty.call(array, i)) {\n            if (receiver == null) {\n                iterator(array[i], i, array);\n            } else {\n                iterator.call(receiver, array[i], i, array);\n            }\n        }\n    }\n};\n\nvar forEachString = function forEachString(string, iterator, receiver) {\n    for (var i = 0, len = string.length; i < len; i++) {\n        // no such thing as a sparse string.\n        if (receiver == null) {\n            iterator(string.charAt(i), i, string);\n        } else {\n            iterator.call(receiver, string.charAt(i), i, string);\n        }\n    }\n};\n\nvar forEachObject = function forEachObject(object, iterator, receiver) {\n    for (var k in object) {\n        if (hasOwnProperty.call(object, k)) {\n            if (receiver == null) {\n                iterator(object[k], k, object);\n            } else {\n                iterator.call(receiver, object[k], k, object);\n            }\n        }\n    }\n};\n\nvar forEach = function forEach(list, iterator, thisArg) {\n    if (!isCallable(iterator)) {\n        throw new TypeError('iterator must be a function');\n    }\n\n    var receiver;\n    if (arguments.length >= 3) {\n        receiver = thisArg;\n    }\n\n    if (toStr.call(list) === '[object Array]') {\n        forEachArray(list, iterator, receiver);\n    } else if (typeof list === 'string') {\n        forEachString(list, iterator, receiver);\n    } else {\n        forEachObject(list, iterator, receiver);\n    }\n};\n\nmodule.exports = forEach;\n", "'use strict';\n\nvar toStr = Object.prototype.toString;\n\nmodule.exports = function isArguments(value) {\n\tvar str = toStr.call(value);\n\tvar isArgs = str === '[object Arguments]';\n\tif (!isArgs) {\n\t\tisArgs = str !== '[object Array]' &&\n\t\t\tvalue !== null &&\n\t\t\ttypeof value === 'object' &&\n\t\t\ttypeof value.length === 'number' &&\n\t\t\tvalue.length >= 0 &&\n\t\t\ttoStr.call(value.callee) === '[object Function]';\n\t}\n\treturn isArgs;\n};\n", "'use strict';\n\nvar slice = Array.prototype.slice;\nvar isArgs = require('./isArguments');\n\nvar origKeys = Object.keys;\nvar keysShim = origKeys ? function keys(o) { return origKeys(o); } : require('./implementation');\n\nvar originalKeys = Object.keys;\n\nkeysShim.shim = function shimObjectKeys() {\n\tif (Object.keys) {\n\t\tvar keysWorksWithArguments = (function () {\n\t\t\t// Safari 5.0 bug\n\t\t\tvar args = Object.keys(arguments);\n\t\t\treturn args && args.length === arguments.length;\n\t\t}(1, 2));\n\t\tif (!keysWorksWithArguments) {\n\t\t\tObject.keys = function keys(object) { // eslint-disable-line func-name-matching\n\t\t\t\tif (isArgs(object)) {\n\t\t\t\t\treturn originalKeys(slice.call(object));\n\t\t\t\t}\n\t\t\t\treturn originalKeys(object);\n\t\t\t};\n\t\t}\n\t} else {\n\t\tObject.keys = keysShim;\n\t}\n\treturn Object.keys || keysShim;\n};\n\nmodule.exports = keysShim;\n", "function validate(binding) {\r\n  if (typeof binding.value !== 'function') {\r\n    console.warn('[Vue-click-outside:] provided expression', binding.expression, 'is not a function.')\r\n    return false\r\n  }\r\n\r\n  return true\r\n}\r\n\r\nfunction isPopup(popupItem, elements) {\r\n  if (!popupItem || !elements)\r\n    return false\r\n\r\n  for (var i = 0, len = elements.length; i < len; i++) {\r\n    try {\r\n      if (popupItem.contains(elements[i])) {\r\n        return true\r\n      }\r\n      if (elements[i].contains(popupItem)) {\r\n        return false\r\n      }\r\n    } catch(e) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  return false\r\n}\r\n\r\nfunction isServer(vNode) {\r\n  return typeof vNode.componentInstance !== 'undefined' && vNode.componentInstance.$isServer\r\n}\r\n\r\nexports = module.exports = {\r\n  bind: function (el, binding, vNode) {\r\n    if (!validate(binding)) return\r\n\r\n    // Define Handler and cache it on the element\r\n    function handler(e) {\r\n      if (!vNode.context) return\r\n\r\n      // some components may have related popup item, on which we shall prevent the click outside event handler.\r\n      var elements = e.path || (e.composedPath && e.composedPath())\r\n      elements && elements.length > 0 && elements.unshift(e.target)\r\n      \r\n      if (el.contains(e.target) || isPopup(vNode.context.popupItem, elements)) return\r\n\r\n      el.__vueClickOutside__.callback(e)\r\n    }\r\n\r\n    // add Event Listeners\r\n    el.__vueClickOutside__ = {\r\n      handler: handler,\r\n      callback: binding.value\r\n    }\r\n    !isServer(vNode) && document.addEventListener('click', handler)\r\n  },\r\n\r\n  update: function (el, binding) {\r\n    if (validate(binding)) el.__vueClickOutside__.callback = binding.value\r\n  },\r\n  \r\n  unbind: function (el, binding, vNode) {\r\n    // Remove Event Listeners\r\n    !isServer(vNode) && document.removeEventListener('click', el.__vueClickOutside__.handler)\r\n    delete el.__vueClickOutside__\r\n  }\r\n}\r\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewControls.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewControls.vue?vue&type=style&index=0&lang=scss&\"", "'use strict';\n\n/* globals\n\tSet,\n\tMap,\n\tWeakSet,\n\tWeakMap,\n\n\tPromise,\n\n\tSymbol,\n\tProxy,\n\n\tAtomics,\n\tSharedArrayBuffer,\n\n\tArrayBuffer,\n\tDataView,\n\tUint8Array,\n\tFloat32Array,\n\tFloat64Array,\n\tInt8Array,\n\tInt16Array,\n\tInt32Array,\n\tUint8ClampedArray,\n\tUint16Array,\n\tUint32Array,\n*/\n\nvar undefined; // eslint-disable-line no-shadow-restricted-names\n\nvar ThrowTypeError = Object.getOwnPropertyDescriptor\n\t? (function () { return Object.getOwnPropertyDescriptor(arguments, 'callee').get; }())\n\t: function () { throw new TypeError(); };\n\nvar hasSymbols = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol';\n\nvar getProto = Object.getPrototypeOf || function (x) { return x.__proto__; }; // eslint-disable-line no-proto\n\nvar generator; // = function * () {};\nvar generatorFunction = generator ? getProto(generator) : undefined;\nvar asyncFn; // async function() {};\nvar asyncFunction = asyncFn ? asyncFn.constructor : undefined;\nvar asyncGen; // async function * () {};\nvar asyncGenFunction = asyncGen ? getProto(asyncGen) : undefined;\nvar asyncGenIterator = asyncGen ? asyncGen() : undefined;\n\nvar TypedArray = typeof Uint8Array === 'undefined' ? undefined : getProto(Uint8Array);\n\nvar INTRINSICS = {\n\t'$ %Array%': Array,\n\t'$ %ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer,\n\t'$ %ArrayBufferPrototype%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer.prototype,\n\t'$ %ArrayIteratorPrototype%': hasSymbols ? getProto([][Symbol.iterator]()) : undefined,\n\t'$ %ArrayPrototype%': Array.prototype,\n\t'$ %ArrayProto_entries%': Array.prototype.entries,\n\t'$ %ArrayProto_forEach%': Array.prototype.forEach,\n\t'$ %ArrayProto_keys%': Array.prototype.keys,\n\t'$ %ArrayProto_values%': Array.prototype.values,\n\t'$ %AsyncFromSyncIteratorPrototype%': undefined,\n\t'$ %AsyncFunction%': asyncFunction,\n\t'$ %AsyncFunctionPrototype%': asyncFunction ? asyncFunction.prototype : undefined,\n\t'$ %AsyncGenerator%': asyncGen ? getProto(asyncGenIterator) : undefined,\n\t'$ %AsyncGeneratorFunction%': asyncGenFunction,\n\t'$ %AsyncGeneratorPrototype%': asyncGenFunction ? asyncGenFunction.prototype : undefined,\n\t'$ %AsyncIteratorPrototype%': asyncGenIterator && hasSymbols && Symbol.asyncIterator ? asyncGenIterator[Symbol.asyncIterator]() : undefined,\n\t'$ %Atomics%': typeof Atomics === 'undefined' ? undefined : Atomics,\n\t'$ %Boolean%': Boolean,\n\t'$ %BooleanPrototype%': Boolean.prototype,\n\t'$ %DataView%': typeof DataView === 'undefined' ? undefined : DataView,\n\t'$ %DataViewPrototype%': typeof DataView === 'undefined' ? undefined : DataView.prototype,\n\t'$ %Date%': Date,\n\t'$ %DatePrototype%': Date.prototype,\n\t'$ %decodeURI%': decodeURI,\n\t'$ %decodeURIComponent%': decodeURIComponent,\n\t'$ %encodeURI%': encodeURI,\n\t'$ %encodeURIComponent%': encodeURIComponent,\n\t'$ %Error%': Error,\n\t'$ %ErrorPrototype%': Error.prototype,\n\t'$ %eval%': eval, // eslint-disable-line no-eval\n\t'$ %EvalError%': EvalError,\n\t'$ %EvalErrorPrototype%': EvalError.prototype,\n\t'$ %Float32Array%': typeof Float32Array === 'undefined' ? undefined : Float32Array,\n\t'$ %Float32ArrayPrototype%': typeof Float32Array === 'undefined' ? undefined : Float32Array.prototype,\n\t'$ %Float64Array%': typeof Float64Array === 'undefined' ? undefined : Float64Array,\n\t'$ %Float64ArrayPrototype%': typeof Float64Array === 'undefined' ? undefined : Float64Array.prototype,\n\t'$ %Function%': Function,\n\t'$ %FunctionPrototype%': Function.prototype,\n\t'$ %Generator%': generator ? getProto(generator()) : undefined,\n\t'$ %GeneratorFunction%': generatorFunction,\n\t'$ %GeneratorPrototype%': generatorFunction ? generatorFunction.prototype : undefined,\n\t'$ %Int8Array%': typeof Int8Array === 'undefined' ? undefined : Int8Array,\n\t'$ %Int8ArrayPrototype%': typeof Int8Array === 'undefined' ? undefined : Int8Array.prototype,\n\t'$ %Int16Array%': typeof Int16Array === 'undefined' ? undefined : Int16Array,\n\t'$ %Int16ArrayPrototype%': typeof Int16Array === 'undefined' ? undefined : Int8Array.prototype,\n\t'$ %Int32Array%': typeof Int32Array === 'undefined' ? undefined : Int32Array,\n\t'$ %Int32ArrayPrototype%': typeof Int32Array === 'undefined' ? undefined : Int32Array.prototype,\n\t'$ %isFinite%': isFinite,\n\t'$ %isNaN%': isNaN,\n\t'$ %IteratorPrototype%': hasSymbols ? getProto(getProto([][Symbol.iterator]())) : undefined,\n\t'$ %JSON%': JSON,\n\t'$ %JSONParse%': JSON.parse,\n\t'$ %Map%': typeof Map === 'undefined' ? undefined : Map,\n\t'$ %MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols ? undefined : getProto(new Map()[Symbol.iterator]()),\n\t'$ %MapPrototype%': typeof Map === 'undefined' ? undefined : Map.prototype,\n\t'$ %Math%': Math,\n\t'$ %Number%': Number,\n\t'$ %NumberPrototype%': Number.prototype,\n\t'$ %Object%': Object,\n\t'$ %ObjectPrototype%': Object.prototype,\n\t'$ %ObjProto_toString%': Object.prototype.toString,\n\t'$ %ObjProto_valueOf%': Object.prototype.valueOf,\n\t'$ %parseFloat%': parseFloat,\n\t'$ %parseInt%': parseInt,\n\t'$ %Promise%': typeof Promise === 'undefined' ? undefined : Promise,\n\t'$ %PromisePrototype%': typeof Promise === 'undefined' ? undefined : Promise.prototype,\n\t'$ %PromiseProto_then%': typeof Promise === 'undefined' ? undefined : Promise.prototype.then,\n\t'$ %Promise_all%': typeof Promise === 'undefined' ? undefined : Promise.all,\n\t'$ %Promise_reject%': typeof Promise === 'undefined' ? undefined : Promise.reject,\n\t'$ %Promise_resolve%': typeof Promise === 'undefined' ? undefined : Promise.resolve,\n\t'$ %Proxy%': typeof Proxy === 'undefined' ? undefined : Proxy,\n\t'$ %RangeError%': RangeError,\n\t'$ %RangeErrorPrototype%': RangeError.prototype,\n\t'$ %ReferenceError%': ReferenceError,\n\t'$ %ReferenceErrorPrototype%': ReferenceError.prototype,\n\t'$ %Reflect%': typeof Reflect === 'undefined' ? undefined : Reflect,\n\t'$ %RegExp%': RegExp,\n\t'$ %RegExpPrototype%': RegExp.prototype,\n\t'$ %Set%': typeof Set === 'undefined' ? undefined : Set,\n\t'$ %SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols ? undefined : getProto(new Set()[Symbol.iterator]()),\n\t'$ %SetPrototype%': typeof Set === 'undefined' ? undefined : Set.prototype,\n\t'$ %SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer,\n\t'$ %SharedArrayBufferPrototype%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer.prototype,\n\t'$ %String%': String,\n\t'$ %StringIteratorPrototype%': hasSymbols ? getProto(''[Symbol.iterator]()) : undefined,\n\t'$ %StringPrototype%': String.prototype,\n\t'$ %Symbol%': hasSymbols ? Symbol : undefined,\n\t'$ %SymbolPrototype%': hasSymbols ? Symbol.prototype : undefined,\n\t'$ %SyntaxError%': SyntaxError,\n\t'$ %SyntaxErrorPrototype%': SyntaxError.prototype,\n\t'$ %ThrowTypeError%': ThrowTypeError,\n\t'$ %TypedArray%': TypedArray,\n\t'$ %TypedArrayPrototype%': TypedArray ? TypedArray.prototype : undefined,\n\t'$ %TypeError%': TypeError,\n\t'$ %TypeErrorPrototype%': TypeError.prototype,\n\t'$ %Uint8Array%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array,\n\t'$ %Uint8ArrayPrototype%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array.prototype,\n\t'$ %Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray,\n\t'$ %Uint8ClampedArrayPrototype%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray.prototype,\n\t'$ %Uint16Array%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array,\n\t'$ %Uint16ArrayPrototype%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array.prototype,\n\t'$ %Uint32Array%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array,\n\t'$ %Uint32ArrayPrototype%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array.prototype,\n\t'$ %URIError%': URIError,\n\t'$ %URIErrorPrototype%': URIError.prototype,\n\t'$ %WeakMap%': typeof WeakMap === 'undefined' ? undefined : WeakMap,\n\t'$ %WeakMapPrototype%': typeof WeakMap === 'undefined' ? undefined : WeakMap.prototype,\n\t'$ %WeakSet%': typeof WeakSet === 'undefined' ? undefined : WeakSet,\n\t'$ %WeakSetPrototype%': typeof WeakSet === 'undefined' ? undefined : WeakSet.prototype\n};\n\nmodule.exports = function GetIntrinsic(name, allowMissing) {\n\tif (arguments.length > 1 && typeof allowMissing !== 'boolean') {\n\t\tthrow new TypeError('\"allowMissing\" argument must be a boolean');\n\t}\n\n\tvar key = '$ ' + name;\n\tif (!(key in INTRINSICS)) {\n\t\tthrow new SyntaxError('intrinsic ' + name + ' does not exist!');\n\t}\n\n\t// istanbul ignore if // hopefully this is impossible to test :-)\n\tif (typeof INTRINSICS[key] === 'undefined' && !allowMissing) {\n\t\tthrow new TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');\n\t}\n\treturn INTRINSICS[key];\n};\n", "\"use strict\";\nvar window = require(\"global/window\")\nvar isFunction = require(\"is-function\")\nvar parseHeaders = require(\"parse-headers\")\nvar xtend = require(\"xtend\")\n\nmodule.exports = createXHR\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = createXHR;\ncreateXHR.XMLHttpRequest = window.XMLHttpRequest || noop\ncreateXHR.XDomainRequest = \"withCredentials\" in (new createXHR.XMLHttpRequest()) ? createXHR.XMLHttpRequest : window.XDomainRequest\n\nforEachArray([\"get\", \"put\", \"post\", \"patch\", \"head\", \"delete\"], function(method) {\n    createXHR[method === \"delete\" ? \"del\" : method] = function(uri, options, callback) {\n        options = initParams(uri, options, callback)\n        options.method = method.toUpperCase()\n        return _createXHR(options)\n    }\n})\n\nfunction forEachArray(array, iterator) {\n    for (var i = 0; i < array.length; i++) {\n        iterator(array[i])\n    }\n}\n\nfunction isEmpty(obj){\n    for(var i in obj){\n        if(obj.hasOwnProperty(i)) return false\n    }\n    return true\n}\n\nfunction initParams(uri, options, callback) {\n    var params = uri\n\n    if (isFunction(options)) {\n        callback = options\n        if (typeof uri === \"string\") {\n            params = {uri:uri}\n        }\n    } else {\n        params = xtend(options, {uri: uri})\n    }\n\n    params.callback = callback\n    return params\n}\n\nfunction createXHR(uri, options, callback) {\n    options = initParams(uri, options, callback)\n    return _createXHR(options)\n}\n\nfunction _createXHR(options) {\n    if(typeof options.callback === \"undefined\"){\n        throw new Error(\"callback argument missing\")\n    }\n\n    var called = false\n    var callback = function cbOnce(err, response, body){\n        if(!called){\n            called = true\n            options.callback(err, response, body)\n        }\n    }\n\n    function readystatechange() {\n        if (xhr.readyState === 4) {\n            setTimeout(loadFunc, 0)\n        }\n    }\n\n    function getBody() {\n        // Chrome with requestType=blob throws errors arround when even testing access to responseText\n        var body = undefined\n\n        if (xhr.response) {\n            body = xhr.response\n        } else {\n            body = xhr.responseText || getXml(xhr)\n        }\n\n        if (isJson) {\n            try {\n                body = JSON.parse(body)\n            } catch (e) {}\n        }\n\n        return body\n    }\n\n    function errorFunc(evt) {\n        clearTimeout(timeoutTimer)\n        if(!(evt instanceof Error)){\n            evt = new Error(\"\" + (evt || \"Unknown XMLHttpRequest Error\") )\n        }\n        evt.statusCode = 0\n        return callback(evt, failureResponse)\n    }\n\n    // will load the data & process the response in a special response object\n    function loadFunc() {\n        if (aborted) return\n        var status\n        clearTimeout(timeoutTimer)\n        if(options.useXDR && xhr.status===undefined) {\n            //IE8 CORS GET successful response doesn't have a status field, but body is fine\n            status = 200\n        } else {\n            status = (xhr.status === 1223 ? 204 : xhr.status)\n        }\n        var response = failureResponse\n        var err = null\n\n        if (status !== 0){\n            response = {\n                body: getBody(),\n                statusCode: status,\n                method: method,\n                headers: {},\n                url: uri,\n                rawRequest: xhr\n            }\n            if(xhr.getAllResponseHeaders){ //remember xhr can in fact be XDR for CORS in IE\n                response.headers = parseHeaders(xhr.getAllResponseHeaders())\n            }\n        } else {\n            err = new Error(\"Internal XMLHttpRequest Error\")\n        }\n        return callback(err, response, response.body)\n    }\n\n    var xhr = options.xhr || null\n\n    if (!xhr) {\n        if (options.cors || options.useXDR) {\n            xhr = new createXHR.XDomainRequest()\n        }else{\n            xhr = new createXHR.XMLHttpRequest()\n        }\n    }\n\n    var key\n    var aborted\n    var uri = xhr.url = options.uri || options.url\n    var method = xhr.method = options.method || \"GET\"\n    var body = options.body || options.data\n    var headers = xhr.headers = options.headers || {}\n    var sync = !!options.sync\n    var isJson = false\n    var timeoutTimer\n    var failureResponse = {\n        body: undefined,\n        headers: {},\n        statusCode: 0,\n        method: method,\n        url: uri,\n        rawRequest: xhr\n    }\n\n    if (\"json\" in options && options.json !== false) {\n        isJson = true\n        headers[\"accept\"] || headers[\"Accept\"] || (headers[\"Accept\"] = \"application/json\") //Don't override existing accept header declared by user\n        if (method !== \"GET\" && method !== \"HEAD\") {\n            headers[\"content-type\"] || headers[\"Content-Type\"] || (headers[\"Content-Type\"] = \"application/json\") //Don't override existing accept header declared by user\n            body = JSON.stringify(options.json === true ? body : options.json)\n        }\n    }\n\n    xhr.onreadystatechange = readystatechange\n    xhr.onload = loadFunc\n    xhr.onerror = errorFunc\n    // IE9 must have onprogress be set to a unique function.\n    xhr.onprogress = function () {\n        // IE must die\n    }\n    xhr.onabort = function(){\n        aborted = true;\n    }\n    xhr.ontimeout = errorFunc\n    xhr.open(method, uri, !sync, options.username, options.password)\n    //has to be after open\n    if(!sync) {\n        xhr.withCredentials = !!options.withCredentials\n    }\n    // Cannot set timeout with sync request\n    // not setting timeout on the xhr object, because of old webkits etc. not handling that correctly\n    // both npm's request and jquery 1.x use this kind of timeout, so this is being consistent\n    if (!sync && options.timeout > 0 ) {\n        timeoutTimer = setTimeout(function(){\n            if (aborted) return\n            aborted = true//IE9 may still call readystatechange\n            xhr.abort(\"timeout\")\n            var e = new Error(\"XMLHttpRequest timeout\")\n            e.code = \"ETIMEDOUT\"\n            errorFunc(e)\n        }, options.timeout )\n    }\n\n    if (xhr.setRequestHeader) {\n        for(key in headers){\n            if(headers.hasOwnProperty(key)){\n                xhr.setRequestHeader(key, headers[key])\n            }\n        }\n    } else if (options.headers && !isEmpty(options.headers)) {\n        throw new Error(\"Headers cannot be set on an XDomainRequest object\")\n    }\n\n    if (\"responseType\" in options) {\n        xhr.responseType = options.responseType\n    }\n\n    if (\"beforeSend\" in options &&\n        typeof options.beforeSend === \"function\"\n    ) {\n        options.beforeSend(xhr)\n    }\n\n    // Microsoft Edge browser sends \"undefined\" when send is called with undefined value.\n    // XMLHttpRequest spec says to pass null as body to indicate no body\n    // See https://github.com/naugtur/xhr/issues/100.\n    xhr.send(body || null)\n\n    return xhr\n\n\n}\n\nfunction getXml(xhr) {\n    // xhr.responseXML will throw Exception \"InvalidStateError\" or \"DOMException\"\n    // See https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/responseXML.\n    try {\n        if (xhr.responseType === \"document\") {\n            return xhr.responseXML\n        }\n        var firefoxBugTakenEffect = xhr.responseXML && xhr.responseXML.documentElement.nodeName === \"parsererror\"\n        if (xhr.responseType === \"\" && !firefoxBugTakenEffect) {\n            return xhr.responseXML\n        }\n    } catch (e) {}\n\n    return null\n}\n\nfunction noop() {}\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewConfiguration.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewConfiguration.vue?vue&type=style&index=0&lang=scss&\"", "//Some versions of GlyphDesigner have a typo\n//that causes some bugs with parsing. \n//Need to confirm with recent version of the software\n//to see whether this is still an issue or not.\nvar GLYPH_DESIGNER_ERROR = 'chasrset'\n\nmodule.exports = function parseAttributes(obj) {\n  if (GLYPH_DESIGNER_ERROR in obj) {\n    obj['charset'] = obj[GLYPH_DESIGNER_ERROR]\n    delete obj[GLYPH_DESIGNER_ERROR]\n  }\n\n  for (var k in obj) {\n    if (k === 'face' || k === 'charset') \n      continue\n    else if (k === 'padding' || k === 'spacing')\n      obj[k] = parseIntList(obj[k])\n    else\n      obj[k] = parseInt(obj[k], 10) \n  }\n  return obj\n}\n\nfunction parseIntList(data) {\n  return data.split(',').map(function(val) {\n    return parseInt(val, 10)\n  })\n}", "var wordWrap = require('word-wrapper')\nvar xtend = require('xtend')\nvar number = require('as-number')\n\nvar X_HEIGHTS = ['x', 'e', 'a', 'o', 'n', 's', 'r', 'c', 'u', 'm', 'v', 'w', 'z']\nvar M_WIDTHS = ['m', 'w']\nvar CAP_HEIGHTS = ['H', 'I', 'N', 'E', 'F', 'K', 'L', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']\n\n\nvar TAB_ID = '\\t'.charCodeAt(0)\nvar SPACE_ID = ' '.charCodeAt(0)\nvar ALIGN_LEFT = 0, \n    ALIGN_CENTER = 1, \n    ALIGN_RIGHT = 2\n\nmodule.exports = function createLayout(opt) {\n  return new TextLayout(opt)\n}\n\nfunction TextLayout(opt) {\n  this.glyphs = []\n  this._measure = this.computeMetrics.bind(this)\n  this.update(opt)\n}\n\nTextLayout.prototype.update = function(opt) {\n  opt = xtend({\n    measure: this._measure\n  }, opt)\n  this._opt = opt\n  this._opt.tabSize = number(this._opt.tabSize, 4)\n\n  if (!opt.font)\n    throw new Error('must provide a valid bitmap font')\n\n  var glyphs = this.glyphs\n  var text = opt.text||'' \n  var font = opt.font\n  this._setupSpaceGlyphs(font)\n  \n  var lines = wordWrap.lines(text, opt)\n  var minWidth = opt.width || 0\n\n  //clear glyphs\n  glyphs.length = 0\n\n  //get max line width\n  var maxLineWidth = lines.reduce(function(prev, line) {\n    return Math.max(prev, line.width, minWidth)\n  }, 0)\n\n  //the pen position\n  var x = 0\n  var y = 0\n  var lineHeight = number(opt.lineHeight, font.common.lineHeight)\n  var baseline = font.common.base\n  var descender = lineHeight-baseline\n  var letterSpacing = opt.letterSpacing || 0\n  var height = lineHeight * lines.length - descender\n  var align = getAlignType(this._opt.align)\n\n  //draw text along baseline\n  y -= height\n  \n  //the metrics for this text layout\n  this._width = maxLineWidth\n  this._height = height\n  this._descender = lineHeight - baseline\n  this._baseline = baseline\n  this._xHeight = getXHeight(font)\n  this._capHeight = getCapHeight(font)\n  this._lineHeight = lineHeight\n  this._ascender = lineHeight - descender - this._xHeight\n    \n  //layout each glyph\n  var self = this\n  lines.forEach(function(line, lineIndex) {\n    var start = line.start\n    var end = line.end\n    var lineWidth = line.width\n    var lastGlyph\n    \n    //for each glyph in that line...\n    for (var i=start; i<end; i++) {\n      var id = text.charCodeAt(i)\n      var glyph = self.getGlyph(font, id)\n      if (glyph) {\n        if (lastGlyph) \n          x += getKerning(font, lastGlyph.id, glyph.id)\n\n        var tx = x\n        if (align === ALIGN_CENTER) \n          tx += (maxLineWidth-lineWidth)/2\n        else if (align === ALIGN_RIGHT)\n          tx += (maxLineWidth-lineWidth)\n\n        glyphs.push({\n          position: [tx, y],\n          data: glyph,\n          index: i,\n          line: lineIndex\n        })  \n\n        //move pen forward\n        x += glyph.xadvance + letterSpacing\n        lastGlyph = glyph\n      }\n    }\n\n    //next line down\n    y += lineHeight\n    x = 0\n  })\n  this._linesTotal = lines.length;\n}\n\nTextLayout.prototype._setupSpaceGlyphs = function(font) {\n  //These are fallbacks, when the font doesn't include\n  //' ' or '\\t' glyphs\n  this._fallbackSpaceGlyph = null\n  this._fallbackTabGlyph = null\n\n  if (!font.chars || font.chars.length === 0)\n    return\n\n  //try to get space glyph\n  //then fall back to the 'm' or 'w' glyphs\n  //then fall back to the first glyph available\n  var space = getGlyphById(font, SPACE_ID) \n          || getMGlyph(font) \n          || font.chars[0]\n\n  //and create a fallback for tab\n  var tabWidth = this._opt.tabSize * space.xadvance\n  this._fallbackSpaceGlyph = space\n  this._fallbackTabGlyph = xtend(space, {\n    x: 0, y: 0, xadvance: tabWidth, id: TAB_ID, \n    xoffset: 0, yoffset: 0, width: 0, height: 0\n  })\n}\n\nTextLayout.prototype.getGlyph = function(font, id) {\n  var glyph = getGlyphById(font, id)\n  if (glyph)\n    return glyph\n  else if (id === TAB_ID) \n    return this._fallbackTabGlyph\n  else if (id === SPACE_ID) \n    return this._fallbackSpaceGlyph\n  return null\n}\n\nTextLayout.prototype.computeMetrics = function(text, start, end, width) {\n  var letterSpacing = this._opt.letterSpacing || 0\n  var font = this._opt.font\n  var curPen = 0\n  var curWidth = 0\n  var count = 0\n  var glyph\n  var lastGlyph\n\n  if (!font.chars || font.chars.length === 0) {\n    return {\n      start: start,\n      end: start,\n      width: 0\n    }\n  }\n\n  end = Math.min(text.length, end)\n  for (var i=start; i < end; i++) {\n    var id = text.charCodeAt(i)\n    var glyph = this.getGlyph(font, id)\n\n    if (glyph) {\n      //move pen forward\n      var xoff = glyph.xoffset\n      var kern = lastGlyph ? getKerning(font, lastGlyph.id, glyph.id) : 0\n      curPen += kern\n\n      var nextPen = curPen + glyph.xadvance + letterSpacing\n      var nextWidth = curPen + glyph.width\n\n      //we've hit our limit; we can't move onto the next glyph\n      if (nextWidth >= width || nextPen >= width)\n        break\n\n      //otherwise continue along our line\n      curPen = nextPen\n      curWidth = nextWidth\n      lastGlyph = glyph\n    }\n    count++\n  }\n  \n  //make sure rightmost edge lines up with rendered glyphs\n  if (lastGlyph)\n    curWidth += lastGlyph.xoffset\n\n  return {\n    start: start,\n    end: start + count,\n    width: curWidth\n  }\n}\n\n//getters for the private vars\n;['width', 'height', \n  'descender', 'ascender',\n  'xHeight', 'baseline',\n  'capHeight',\n  'lineHeight' ].forEach(addGetter)\n\nfunction addGetter(name) {\n  Object.defineProperty(TextLayout.prototype, name, {\n    get: wrapper(name),\n    configurable: true\n  })\n}\n\n//create lookups for private vars\nfunction wrapper(name) {\n  return (new Function([\n    'return function '+name+'() {',\n    '  return this._'+name,\n    '}'\n  ].join('\\n')))()\n}\n\nfunction getGlyphById(font, id) {\n  if (!font.chars || font.chars.length === 0)\n    return null\n\n  var glyphIdx = findChar(font.chars, id)\n  if (glyphIdx >= 0)\n    return font.chars[glyphIdx]\n  return null\n}\n\nfunction getXHeight(font) {\n  for (var i=0; i<X_HEIGHTS.length; i++) {\n    var id = X_HEIGHTS[i].charCodeAt(0)\n    var idx = findChar(font.chars, id)\n    if (idx >= 0) \n      return font.chars[idx].height\n  }\n  return 0\n}\n\nfunction getMGlyph(font) {\n  for (var i=0; i<M_WIDTHS.length; i++) {\n    var id = M_WIDTHS[i].charCodeAt(0)\n    var idx = findChar(font.chars, id)\n    if (idx >= 0) \n      return font.chars[idx]\n  }\n  return 0\n}\n\nfunction getCapHeight(font) {\n  for (var i=0; i<CAP_HEIGHTS.length; i++) {\n    var id = CAP_HEIGHTS[i].charCodeAt(0)\n    var idx = findChar(font.chars, id)\n    if (idx >= 0) \n      return font.chars[idx].height\n  }\n  return 0\n}\n\nfunction getKerning(font, left, right) {\n  if (!font.kernings || font.kernings.length === 0)\n    return 0\n\n  var table = font.kernings\n  for (var i=0; i<table.length; i++) {\n    var kern = table[i]\n    if (kern.first === left && kern.second === right)\n      return kern.amount\n  }\n  return 0\n}\n\nfunction getAlignType(align) {\n  if (align === 'center')\n    return ALIGN_CENTER\n  else if (align === 'right')\n    return ALIGN_RIGHT\n  return ALIGN_LEFT\n}\n\nfunction findChar (array, value, start) {\n  start = start || 0\n  for (var i = start; i < array.length; i++) {\n    if (array[i].id === value) {\n      return i\n    }\n  }\n  return -1\n}", "'use strict';\n\nvar keys = require('object-keys');\nvar hasSymbols = typeof Symbol === 'function' && typeof Symbol('foo') === 'symbol';\n\nvar toStr = Object.prototype.toString;\nvar concat = Array.prototype.concat;\nvar origDefineProperty = Object.defineProperty;\n\nvar isFunction = function (fn) {\n\treturn typeof fn === 'function' && toStr.call(fn) === '[object Function]';\n};\n\nvar arePropertyDescriptorsSupported = function () {\n\tvar obj = {};\n\ttry {\n\t\torigDefineProperty(obj, 'x', { enumerable: false, value: obj });\n\t\t// eslint-disable-next-line no-unused-vars, no-restricted-syntax\n\t\tfor (var _ in obj) { // jscs:ignore disallowUnusedVariables\n\t\t\treturn false;\n\t\t}\n\t\treturn obj.x === obj;\n\t} catch (e) { /* this is IE 8. */\n\t\treturn false;\n\t}\n};\nvar supportsDescriptors = origDefineProperty && arePropertyDescriptorsSupported();\n\nvar defineProperty = function (object, name, value, predicate) {\n\tif (name in object && (!isFunction(predicate) || !predicate())) {\n\t\treturn;\n\t}\n\tif (supportsDescriptors) {\n\t\torigDefineProperty(object, name, {\n\t\t\tconfigurable: true,\n\t\t\tenumerable: false,\n\t\t\tvalue: value,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\tobject[name] = value;\n\t}\n};\n\nvar defineProperties = function (object, map) {\n\tvar predicates = arguments.length > 2 ? arguments[2] : {};\n\tvar props = keys(map);\n\tif (hasSymbols) {\n\t\tprops = concat.call(props, Object.getOwnPropertySymbols(map));\n\t}\n\tfor (var i = 0; i < props.length; i += 1) {\n\t\tdefineProperty(object, props[i], map[props[i]], predicates[props[i]]);\n\t}\n};\n\ndefineProperties.supportsDescriptors = !!supportsDescriptors;\n\nmodule.exports = defineProperties;\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewComponentSelect.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoPreviewComponentSelect.vue?vue&type=style&index=0&lang=scss&\"", "import mod from \"-!../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=style&index=0&id=360dcb88&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=style&index=0&id=360dcb88&lang=scss&scoped=true&\"", "var parseAttributes = require('./parse-attribs')\nvar parseFromString = require('xml-parse-from-string')\n\n//In some cases element.attribute.nodeName can return\n//all lowercase values.. so we need to map them to the correct \n//case\nvar NAME_MAP = {\n  scaleh: 'scaleH',\n  scalew: 'scaleW',\n  stretchh: 'stretchH',\n  lineheight: 'lineHeight',\n  alphachnl: 'alphaChnl',\n  redchnl: 'redChnl',\n  greenchnl: 'greenChnl',\n  bluechnl: 'blueChnl'\n}\n\nmodule.exports = function parse(data) {\n  data = data.toString()\n  \n  var xmlRoot = parseFromString(data)\n  var output = {\n    pages: [],\n    chars: [],\n    kernings: []\n  }\n\n  //get config settings\n  ;['info', 'common'].forEach(function(key) {\n    var element = xmlRoot.getElementsByTagName(key)[0]\n    if (element)\n      output[key] = parseAttributes(getAttribs(element))\n  })\n\n  //get page info\n  var pageRoot = xmlRoot.getElementsByTagName('pages')[0]\n  if (!pageRoot)\n    throw new Error('malformed file -- no <pages> element')\n  var pages = pageRoot.getElementsByTagName('page')\n  for (var i=0; i<pages.length; i++) {\n    var p = pages[i]\n    var id = parseInt(p.getAttribute('id'), 10)\n    var file = p.getAttribute('file')\n    if (isNaN(id))\n      throw new Error('malformed file -- page \"id\" attribute is NaN')\n    if (!file)\n      throw new Error('malformed file -- needs page \"file\" attribute')\n    output.pages[parseInt(id, 10)] = file\n  }\n\n  //get kernings / chars\n  ;['chars', 'kernings'].forEach(function(key) {\n    var element = xmlRoot.getElementsByTagName(key)[0]\n    if (!element)\n      return\n    var childTag = key.substring(0, key.length-1)\n    var children = element.getElementsByTagName(childTag)\n    for (var i=0; i<children.length; i++) {      \n      var child = children[i]\n      output[key].push(parseAttributes(getAttribs(child)))\n    }\n  })\n  return output\n}\n\nfunction getAttribs(element) {\n  var attribs = getAttribList(element)\n  return attribs.reduce(function(dict, attrib) {\n    var key = mapName(attrib.nodeName)\n    dict[key] = attrib.nodeValue\n    return dict\n  }, {})\n}\n\nfunction getAttribList(element) {\n  //IE8+ and modern browsers\n  var attribs = []\n  for (var i=0; i<element.attributes.length; i++)\n    attribs.push(element.attributes[i])\n  return attribs\n}\n\nfunction mapName(nodeName) {\n  return NAME_MAP[nodeName.toLowerCase()] || nodeName\n}"], "sourceRoot": ""}