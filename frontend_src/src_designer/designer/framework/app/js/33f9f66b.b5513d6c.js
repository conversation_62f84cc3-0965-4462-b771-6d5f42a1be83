(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["33f9f66b"],{"4b71":function(t,a,f){},"8c3f":function(t,a,f){"use strict";var s=f("4b71");var e=f.n(s);var n=e.a},f273:function(t,a,f){"use strict";f.r(a);var s=function(){var t=this;var a=t.$createElement;var f=t._self._c||a;return f("section",{staticClass:"main"},[t._v("404"),f("svg",{staticClass:"absolute-center",attrs:{version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",x:"0px",y:"0px",width:"281px",height:"190px",viewBox:"0 0 81 29","enable-background":"new 0 0 81 29","xml:space":"preserve"}},[f("polygon",{attrs:{fill:"#ffffff",points:"26.5,5.7 22,18.5 17.7,5.7 13.8,5.7 20.2,23 19.3,25.8 15.3,25.8 15.3,29 21.7,29 30.3,5.7"}}),f("path",{attrs:{fill:"#ffffff",d:"M71.6,20.4c-3.2,0-5.9-2.6-6-5.8c-0.1-3.3,2.5-6,5.8-6.1l0.2,0c3.2,0,5.9,2.6,6,5.8c0.1,3.3-2.5,6-5.8,6.1 L71.6,20.4z M71.4,5.2c-5.2,0.2-9.2,4.4-9.1,9.6c0.2,5.1,4.3,9,9.3,9h0c0.1,0,0.2,0,0.3,0c2.5-0.1,4.8-1.1,6.5-2.9 c1.7-1.8,2.6-4.2,2.5-6.7C80.8,9.1,76.6,5.1,71.4,5.2"}}),f("polygon",{attrs:{fill:"#ffffff",points:"45.9,0 45.9,23.4 49.5,23.4 49.5,13.6 56.7,23.3 56.8,23.4 61.3,23.4 54,13.6 61.3,5.7 56.7,5.7 49.5,13.5 49.5,0 "}}),f("polygon",{attrs:{fill:"#ffffff",points:"32.5,0 32.5,3.3 35.6,3.3 35.6,20.2 32.5,20.2 32.5,23.4 42.5,23.4 42.5,20.2 39.3,20.2 39.3,0 "}}),f("polygon",{attrs:{fill:"#ffffff",points:"2.8,0 2.8,5.8 0,5.8 0,9 2.8,9 2.8,23.4 10.4,23.4 10.4,20.2 6.4,20.2 6.4,9 10.4,9 10.4,5.8 6.4,5.8 6.4,0 "}})]),f("q-page",{staticClass:"q-pa-xl"},[f("div",{staticClass:"flex row"},[f("div",{staticClass:"col"},[t._v("werew")])])])],1)};var e=[];var n={name:"PageIndex",components:{},data:function t(){return{checked:false}}};var l=n;var r=f("8c3f");var c=f("2877");var i=Object(c["a"])(l,s,e,false,null,"5ee1b8a0",null);var o=a["default"]=i.exports}}]);
//# sourceMappingURL=33f9f66b.b5513d6c.js.map