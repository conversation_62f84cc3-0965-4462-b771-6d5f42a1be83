(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["6b051dda"],{"1ae5":function(e,t,n){(function(t){var n;var n;
/*!
    localForage -- Offline Storage, Improved
    Version 1.7.3
    https://localforage.github.io/localForage
    (c) 2013-2017 Mozilla, Apache License 2.0
*/
/*!
    localForage -- Offline Storage, Improved
    Version 1.7.3
    https://localforage.github.io/localForage
    (c) 2013-2017 Mozilla, Apache License 2.0
*/
(function(t){if(true){e.exports=t()}else{var n}})(function(){var e,r,o;return function e(t,r,o){function a(s,c){if(!r[s]){if(!t[s]){var u=typeof n=="function"&&n;if(!c&&u)return n(s,!0);if(i)return i(s,!0);var f=new Error("Cannot find module '"+s+"'");throw f.code="MODULE_NOT_FOUND",f}var l=r[s]={exports:{}};t[s][0].call(l.exports,function(e){var n=t[s][1][e];return a(n?n:e)},l,l.exports,e,t,r,o)}return r[s].exports}var i=typeof n=="function"&&n;for(var s=0;s<o.length;s++)a(o[s]);return a}({1:[function(e,n,r){(function(e){"use strict";var t=e.MutationObserver||e.WebKitMutationObserver;var r;{if(t){var o=0;var a=new t(f);var i=e.document.createTextNode("");a.observe(i,{characterData:true});r=function(){i.data=o=++o%2}}else if(!e.setImmediate&&typeof e.MessageChannel!=="undefined"){var s=new e.MessageChannel;s.port1.onmessage=f;r=function(){s.port2.postMessage(0)}}else if("document"in e&&"onreadystatechange"in e.document.createElement("script")){r=function(){var t=e.document.createElement("script");t.onreadystatechange=function(){f();t.onreadystatechange=null;t.parentNode.removeChild(t);t=null};e.document.documentElement.appendChild(t)}}else{r=function(){setTimeout(f,0)}}}var c;var u=[];function f(){c=true;var e,t;var n=u.length;while(n){t=u;u=[];e=-1;while(++e<n){t[e]()}n=u.length}c=false}n.exports=l;function l(e){if(u.push(e)===1&&!c){r()}}}).call(this,typeof t!=="undefined"?t:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],2:[function(e,t,n){"use strict";var r=e(1);function o(){}var a={};var i=["REJECTED"];var s=["FULFILLED"];var c=["PENDING"];t.exports=u;function u(e){if(typeof e!=="function"){throw new TypeError("resolver must be a function")}this.state=c;this.queue=[];this.outcome=void 0;if(e!==o){v(this,e)}}u.prototype["catch"]=function(e){return this.then(null,e)};u.prototype.then=function(e,t){if(typeof e!=="function"&&this.state===s||typeof t!=="function"&&this.state===i){return this}var n=new this.constructor(o);if(this.state!==c){var r=this.state===s?e:t;l(n,r,this.outcome)}else{this.queue.push(new f(n,e,t))}return n};function f(e,t,n){this.promise=e;if(typeof t==="function"){this.onFulfilled=t;this.callFulfilled=this.otherCallFulfilled}if(typeof n==="function"){this.onRejected=n;this.callRejected=this.otherCallRejected}}f.prototype.callFulfilled=function(e){a.resolve(this.promise,e)};f.prototype.otherCallFulfilled=function(e){l(this.promise,this.onFulfilled,e)};f.prototype.callRejected=function(e){a.reject(this.promise,e)};f.prototype.otherCallRejected=function(e){l(this.promise,this.onRejected,e)};function l(e,t,n){r(function(){var r;try{r=t(n)}catch(o){return a.reject(e,o)}if(r===e){a.reject(e,new TypeError("Cannot resolve promise with itself"))}else{a.resolve(e,r)}})}a.resolve=function(e,t){var n=h(d,t);if(n.status==="error"){return a.reject(e,n.value)}var r=n.value;if(r){v(e,r)}else{e.state=s;e.outcome=t;var o=-1;var i=e.queue.length;while(++o<i){e.queue[o].callFulfilled(t)}}return e};a.reject=function(e,t){e.state=i;e.outcome=t;var n=-1;var r=e.queue.length;while(++n<r){e.queue[n].callRejected(t)}return e};function d(e){var t=e&&e.then;if(e&&(typeof e==="object"||typeof e==="function")&&typeof t==="function"){return function n(){t.apply(e,arguments)}}}function v(e,t){var n=false;function r(t){if(n){return}n=true;a.reject(e,t)}function o(t){if(n){return}n=true;a.resolve(e,t)}function i(){t(o,r)}var s=h(i);if(s.status==="error"){r(s.value)}}function h(e,t){var n={};try{n.value=e(t);n.status="success"}catch(r){n.status="error";n.value=r}return n}u.resolve=p;function p(e){if(e instanceof this){return e}return a.resolve(new this(o),e)}u.reject=m;function m(e){var t=new this(o);return a.reject(t,e)}u.all=y;function y(e){var t=this;if(Object.prototype.toString.call(e)!=="[object Array]"){return this.reject(new TypeError("must be an array"))}var n=e.length;var r=false;if(!n){return this.resolve([])}var i=new Array(n);var s=0;var c=-1;var u=new this(o);while(++c<n){f(e[c],c)}return u;function f(e,o){t.resolve(e).then(c,function(e){if(!r){r=true;a.reject(u,e)}});function c(e){i[o]=e;if(++s===n&&!r){r=true;a.resolve(u,i)}}}}u.race=g;function g(e){var t=this;if(Object.prototype.toString.call(e)!=="[object Array]"){return this.reject(new TypeError("must be an array"))}var n=e.length;var r=false;if(!n){return this.resolve([])}var i=-1;var s=new this(o);while(++i<n){c(e[i])}return s;function c(e){t.resolve(e).then(function(e){if(!r){r=true;a.resolve(s,e)}},function(e){if(!r){r=true;a.reject(s,e)}})}}},{1:1}],3:[function(e,n,r){(function(t){"use strict";if(typeof t.Promise!=="function"){t.Promise=e(2)}}).call(this,typeof t!=="undefined"?t:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{2:2}],4:[function(e,t,n){"use strict";var r=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function o(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function a(){try{if(typeof indexedDB!=="undefined"){return indexedDB}if(typeof webkitIndexedDB!=="undefined"){return webkitIndexedDB}if(typeof mozIndexedDB!=="undefined"){return mozIndexedDB}if(typeof OIndexedDB!=="undefined"){return OIndexedDB}if(typeof msIndexedDB!=="undefined"){return msIndexedDB}}catch(e){return}}var i=a();function s(){try{if(!i){return false}var e=typeof openDatabase!=="undefined"&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform);var t=typeof fetch==="function"&&fetch.toString().indexOf("[native code")!==-1;return(!e||t)&&typeof indexedDB!=="undefined"&&typeof IDBKeyRange!=="undefined"}catch(n){return false}}function c(e,t){e=e||[];t=t||{};try{return new Blob(e,t)}catch(a){if(a.name!=="TypeError"){throw a}var n=typeof BlobBuilder!=="undefined"?BlobBuilder:typeof MSBlobBuilder!=="undefined"?MSBlobBuilder:typeof MozBlobBuilder!=="undefined"?MozBlobBuilder:WebKitBlobBuilder;var r=new n;for(var o=0;o<e.length;o+=1){r.append(e[o])}return r.getBlob(t.type)}}if(typeof Promise==="undefined"){e(3)}var u=Promise;function f(e,t){if(t){e.then(function(e){t(null,e)},function(e){t(e)})}}function l(e,t,n){if(typeof t==="function"){e.then(t)}if(typeof n==="function"){e["catch"](n)}}function d(e){if(typeof e!=="string"){console.warn(e+" used as a key, but it is not a string.");e=String(e)}return e}function v(){if(arguments.length&&typeof arguments[arguments.length-1]==="function"){return arguments[arguments.length-1]}}var h="local-forage-detect-blob-support";var p=void 0;var m={};var y=Object.prototype.toString;var g="readonly";var b="readwrite";function w(e){var t=e.length;var n=new ArrayBuffer(t);var r=new Uint8Array(n);for(var o=0;o<t;o++){r[o]=e.charCodeAt(o)}return n}function _(e){return new u(function(t){var n=e.transaction(h,b);var r=c([""]);n.objectStore(h).put(r,"key");n.onabort=function(e){e.preventDefault();e.stopPropagation();t(false)};n.oncomplete=function(){var e=navigator.userAgent.match(/Chrome\/(\d+)/);var n=navigator.userAgent.match(/Edge\//);t(n||!e||parseInt(e[1],10)>=43)}})["catch"](function(){return false})}function E(e){if(typeof p==="boolean"){return u.resolve(p)}return _(e).then(function(e){p=e;return p})}function S(e){var t=m[e.name];var n={};n.promise=new u(function(e,t){n.resolve=e;n.reject=t});t.deferredOperations.push(n);if(!t.dbReady){t.dbReady=n.promise}else{t.dbReady=t.dbReady.then(function(){return n.promise})}}function I(e){var t=m[e.name];var n=t.deferredOperations.pop();if(n){n.resolve();return n.promise}}function x(e,t){var n=m[e.name];var r=n.deferredOperations.pop();if(r){r.reject(t);return r.promise}}function k(e,t){return new u(function(n,r){m[e.name]=m[e.name]||B();if(e.db){if(t){S(e);e.db.close()}else{return n(e.db)}}var o=[e.name];if(t){o.push(e.version)}var a=i.open.apply(i,o);if(t){a.onupgradeneeded=function(t){var n=a.result;try{n.createObjectStore(e.storeName);if(t.oldVersion<=1){n.createObjectStore(h)}}catch(r){if(r.name==="ConstraintError"){console.warn('The database "'+e.name+'"'+" has been upgraded from version "+t.oldVersion+" to version "+t.newVersion+', but the storage "'+e.storeName+'" already exists.')}else{throw r}}}}a.onerror=function(e){e.preventDefault();r(a.error)};a.onsuccess=function(){n(a.result);I(e)}})}function O(e){return k(e,false)}function N(e){return k(e,true)}function j(e,t){if(!e.db){return true}var n=!e.db.objectStoreNames.contains(e.storeName);var r=e.version<e.db.version;var o=e.version>e.db.version;if(r){if(e.version!==t){console.warn('The database "'+e.name+'"'+" can't be downgraded from version "+e.db.version+" to version "+e.version+".")}e.version=e.db.version}if(o||n){if(n){var a=e.db.version+1;if(a>e.version){e.version=a}}return true}return false}function C(e){return new u(function(t,n){var r=new FileReader;r.onerror=n;r.onloadend=function(n){var r=btoa(n.target.result||"");t({__local_forage_encoded_blob:true,data:r,type:e.type})};r.readAsBinaryString(e)})}function A(e){var t=w(atob(e.data));return c([t],{type:e.type})}function R(e){return e&&e.__local_forage_encoded_blob}function D(e){var t=this;var n=t._initReady().then(function(){var e=m[t._dbInfo.name];if(e&&e.dbReady){return e.dbReady}});l(n,e,e);return n}function M(e){S(e);var t=m[e.name];var n=t.forages;for(var r=0;r<n.length;r++){var o=n[r];if(o._dbInfo.db){o._dbInfo.db.close();o._dbInfo.db=null}}e.db=null;return O(e).then(function(t){e.db=t;if(j(e)){return N(e)}return t}).then(function(r){e.db=t.db=r;for(var o=0;o<n.length;o++){n[o]._dbInfo.db=r}})["catch"](function(t){x(e,t);throw t})}function T(e,t,n,r){if(r===undefined){r=1}try{var o=e.db.transaction(e.storeName,t);n(null,o)}catch(a){if(r>0&&(!e.db||a.name==="InvalidStateError"||a.name==="NotFoundError")){return u.resolve().then(function(){if(!e.db||a.name==="NotFoundError"&&!e.db.objectStoreNames.contains(e.storeName)&&e.version<=e.db.version){if(e.db){e.version=e.db.version+1}return N(e)}}).then(function(){return M(e).then(function(){T(e,t,n,r-1)})})["catch"](n)}n(a)}}function B(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function L(e){var t=this;var n={db:null};if(e){for(var r in e){n[r]=e[r]}}var o=m[n.name];if(!o){o=B();m[n.name]=o}o.forages.push(t);if(!t._initReady){t._initReady=t.ready;t.ready=D}var a=[];function i(){return u.resolve()}for(var s=0;s<o.forages.length;s++){var c=o.forages[s];if(c!==t){a.push(c._initReady()["catch"](i))}}var f=o.forages.slice(0);return u.all(a).then(function(){n.db=o.db;return O(n)}).then(function(e){n.db=e;if(j(n,t._defaultConfig.version)){return N(n)}return e}).then(function(e){n.db=o.db=e;t._dbInfo=n;for(var r=0;r<f.length;r++){var a=f[r];if(a!==t){a._dbInfo.db=n.db;a._dbInfo.version=n.version}}})}function z(e,t){var n=this;e=d(e);var r=new u(function(t,r){n.ready().then(function(){T(n._dbInfo,g,function(o,a){if(o){return r(o)}try{var i=a.objectStore(n._dbInfo.storeName);var s=i.get(e);s.onsuccess=function(){var e=s.result;if(e===undefined){e=null}if(R(e)){e=A(e)}t(e)};s.onerror=function(){r(s.error)}}catch(c){r(c)}})})["catch"](r)});f(r,t);return r}function P(e,t){var n=this;var r=new u(function(t,r){n.ready().then(function(){T(n._dbInfo,g,function(o,a){if(o){return r(o)}try{var i=a.objectStore(n._dbInfo.storeName);var s=i.openCursor();var c=1;s.onsuccess=function(){var n=s.result;if(n){var r=n.value;if(R(r)){r=A(r)}var o=e(r,n.key,c++);if(o!==void 0){t(o)}else{n["continue"]()}}else{t()}};s.onerror=function(){r(s.error)}}catch(u){r(u)}})})["catch"](r)});f(r,t);return r}function F(e,t,n){var r=this;e=d(e);var o=new u(function(n,o){var a;r.ready().then(function(){a=r._dbInfo;if(y.call(t)==="[object Blob]"){return E(a.db).then(function(e){if(e){return t}return C(t)})}return t}).then(function(t){T(r._dbInfo,b,function(a,i){if(a){return o(a)}try{var s=i.objectStore(r._dbInfo.storeName);if(t===null){t=undefined}var c=s.put(t,e);i.oncomplete=function(){if(t===undefined){t=null}n(t)};i.onabort=i.onerror=function(){var e=c.error?c.error:c.transaction.error;o(e)}}catch(u){o(u)}})})["catch"](o)});f(o,n);return o}function V(e,t){var n=this;e=d(e);var r=new u(function(t,r){n.ready().then(function(){T(n._dbInfo,b,function(o,a){if(o){return r(o)}try{var i=a.objectStore(n._dbInfo.storeName);var s=i["delete"](e);a.oncomplete=function(){t()};a.onerror=function(){r(s.error)};a.onabort=function(){var e=s.error?s.error:s.transaction.error;r(e)}}catch(c){r(c)}})})["catch"](r)});f(r,t);return r}function U(e){var t=this;var n=new u(function(e,n){t.ready().then(function(){T(t._dbInfo,b,function(r,o){if(r){return n(r)}try{var a=o.objectStore(t._dbInfo.storeName);var i=a.clear();o.oncomplete=function(){e()};o.onabort=o.onerror=function(){var e=i.error?i.error:i.transaction.error;n(e)}}catch(s){n(s)}})})["catch"](n)});f(n,e);return n}function W(e){var t=this;var n=new u(function(e,n){t.ready().then(function(){T(t._dbInfo,g,function(r,o){if(r){return n(r)}try{var a=o.objectStore(t._dbInfo.storeName);var i=a.count();i.onsuccess=function(){e(i.result)};i.onerror=function(){n(i.error)}}catch(s){n(s)}})})["catch"](n)});f(n,e);return n}function q(e,t){var n=this;var r=new u(function(t,r){if(e<0){t(null);return}n.ready().then(function(){T(n._dbInfo,g,function(o,a){if(o){return r(o)}try{var i=a.objectStore(n._dbInfo.storeName);var s=false;var c=i.openCursor();c.onsuccess=function(){var n=c.result;if(!n){t(null);return}if(e===0){t(n.key)}else{if(!s){s=true;n.advance(e)}else{t(n.key)}}};c.onerror=function(){r(c.error)}}catch(u){r(u)}})})["catch"](r)});f(r,t);return r}function Y(e){var t=this;var n=new u(function(e,n){t.ready().then(function(){T(t._dbInfo,g,function(r,o){if(r){return n(r)}try{var a=o.objectStore(t._dbInfo.storeName);var i=a.openCursor();var s=[];i.onsuccess=function(){var t=i.result;if(!t){e(s);return}s.push(t.key);t["continue"]()};i.onerror=function(){n(i.error)}}catch(c){n(c)}})})["catch"](n)});f(n,e);return n}function X(e,t){t=v.apply(this,arguments);var n=this.config();e=typeof e!=="function"&&e||{};if(!e.name){e.name=e.name||n.name;e.storeName=e.storeName||n.storeName}var r=this;var o;if(!e.name){o=u.reject("Invalid arguments")}else{var a=e.name===n.name&&r._dbInfo.db;var s=a?u.resolve(r._dbInfo.db):O(e).then(function(t){var n=m[e.name];var r=n.forages;n.db=t;for(var o=0;o<r.length;o++){r[o]._dbInfo.db=t}return t});if(!e.storeName){o=s.then(function(t){S(e);var n=m[e.name];var r=n.forages;t.close();for(var o=0;o<r.length;o++){var a=r[o];a._dbInfo.db=null}var s=new u(function(t,n){var r=i.deleteDatabase(e.name);r.onerror=r.onblocked=function(e){var t=r.result;if(t){t.close()}n(e)};r.onsuccess=function(){var e=r.result;if(e){e.close()}t(e)}});return s.then(function(e){n.db=e;for(var t=0;t<r.length;t++){var o=r[t];I(o._dbInfo)}})["catch"](function(t){(x(e,t)||u.resolve())["catch"](function(){});throw t})})}else{o=s.then(function(t){if(!t.objectStoreNames.contains(e.storeName)){return}var n=t.version+1;S(e);var r=m[e.name];var o=r.forages;t.close();for(var a=0;a<o.length;a++){var s=o[a];s._dbInfo.db=null;s._dbInfo.version=n}var c=new u(function(t,r){var o=i.open(e.name,n);o.onerror=function(e){var t=o.result;t.close();r(e)};o.onupgradeneeded=function(){var t=o.result;t.deleteObjectStore(e.storeName)};o.onsuccess=function(){var e=o.result;e.close();t(e)}});return c.then(function(e){r.db=e;for(var t=0;t<o.length;t++){var n=o[t];n._dbInfo.db=e;I(n._dbInfo)}})["catch"](function(t){(x(e,t)||u.resolve())["catch"](function(){});throw t})})}}f(o,t);return o}var H={_driver:"asyncStorage",_initStorage:L,_support:s(),iterate:P,getItem:z,setItem:F,removeItem:V,clear:U,length:W,key:q,keys:Y,dropInstance:X};function G(){return typeof openDatabase==="function"}var Z="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";var Q="~~local_forage_type~";var K=/^~~local_forage_type~([^~]+)~/;var J="__lfsc__:";var $=J.length;var ee="arbf";var te="blob";var ne="si08";var re="ui08";var oe="uic8";var ae="si16";var ie="si32";var se="ur16";var ce="ui32";var ue="fl32";var fe="fl64";var le=$+ee.length;var de=Object.prototype.toString;function ve(e){var t=e.length*.75;var n=e.length;var r;var o=0;var a,i,s,c;if(e[e.length-1]==="="){t--;if(e[e.length-2]==="="){t--}}var u=new ArrayBuffer(t);var f=new Uint8Array(u);for(r=0;r<n;r+=4){a=Z.indexOf(e[r]);i=Z.indexOf(e[r+1]);s=Z.indexOf(e[r+2]);c=Z.indexOf(e[r+3]);f[o++]=a<<2|i>>4;f[o++]=(i&15)<<4|s>>2;f[o++]=(s&3)<<6|c&63}return u}function he(e){var t=new Uint8Array(e);var n="";var r;for(r=0;r<t.length;r+=3){n+=Z[t[r]>>2];n+=Z[(t[r]&3)<<4|t[r+1]>>4];n+=Z[(t[r+1]&15)<<2|t[r+2]>>6];n+=Z[t[r+2]&63]}if(t.length%3===2){n=n.substring(0,n.length-1)+"="}else if(t.length%3===1){n=n.substring(0,n.length-2)+"=="}return n}function pe(e,t){var n="";if(e){n=de.call(e)}if(e&&(n==="[object ArrayBuffer]"||e.buffer&&de.call(e.buffer)==="[object ArrayBuffer]")){var r;var o=J;if(e instanceof ArrayBuffer){r=e;o+=ee}else{r=e.buffer;if(n==="[object Int8Array]"){o+=ne}else if(n==="[object Uint8Array]"){o+=re}else if(n==="[object Uint8ClampedArray]"){o+=oe}else if(n==="[object Int16Array]"){o+=ae}else if(n==="[object Uint16Array]"){o+=se}else if(n==="[object Int32Array]"){o+=ie}else if(n==="[object Uint32Array]"){o+=ce}else if(n==="[object Float32Array]"){o+=ue}else if(n==="[object Float64Array]"){o+=fe}else{t(new Error("Failed to get type for BinaryArray"))}}t(o+he(r))}else if(n==="[object Blob]"){var a=new FileReader;a.onload=function(){var n=Q+e.type+"~"+he(this.result);t(J+te+n)};a.readAsArrayBuffer(e)}else{try{t(JSON.stringify(e))}catch(i){console.error("Couldn't convert value into a JSON string: ",e);t(null,i)}}}function me(e){if(e.substring(0,$)!==J){return JSON.parse(e)}var t=e.substring(le);var n=e.substring($,le);var r;if(n===te&&K.test(t)){var o=t.match(K);r=o[1];t=t.substring(o[0].length)}var a=ve(t);switch(n){case ee:return a;case te:return c([a],{type:r});case ne:return new Int8Array(a);case re:return new Uint8Array(a);case oe:return new Uint8ClampedArray(a);case ae:return new Int16Array(a);case se:return new Uint16Array(a);case ie:return new Int32Array(a);case ce:return new Uint32Array(a);case ue:return new Float32Array(a);case fe:return new Float64Array(a);default:throw new Error("Unkown type: "+n)}}var ye={serialize:pe,deserialize:me,stringToBuffer:ve,bufferToString:he};function ge(e,t,n,r){e.executeSql("CREATE TABLE IF NOT EXISTS "+t.storeName+" "+"(id INTEGER PRIMARY KEY, key unique, value)",[],n,r)}function be(e){var t=this;var n={db:null};if(e){for(var r in e){n[r]=typeof e[r]!=="string"?e[r].toString():e[r]}}var o=new u(function(e,r){try{n.db=openDatabase(n.name,String(n.version),n.description,n.size)}catch(o){return r(o)}n.db.transaction(function(o){ge(o,n,function(){t._dbInfo=n;e()},function(e,t){r(t)})},r)});n.serializer=ye;return o}function we(e,t,n,r,o,a){e.executeSql(n,r,o,function(e,i){if(i.code===i.SYNTAX_ERR){e.executeSql("SELECT name FROM sqlite_master "+"WHERE type='table' AND name = ?",[t.storeName],function(e,s){if(!s.rows.length){ge(e,t,function(){e.executeSql(n,r,o,a)},a)}else{a(e,i)}},a)}else{a(e,i)}},a)}function _e(e,t){var n=this;e=d(e);var r=new u(function(t,r){n.ready().then(function(){var o=n._dbInfo;o.db.transaction(function(n){we(n,o,"SELECT * FROM "+o.storeName+" WHERE key = ? LIMIT 1",[e],function(e,n){var r=n.rows.length?n.rows.item(0).value:null;if(r){r=o.serializer.deserialize(r)}t(r)},function(e,t){r(t)})})})["catch"](r)});f(r,t);return r}function Ee(e,t){var n=this;var r=new u(function(t,r){n.ready().then(function(){var o=n._dbInfo;o.db.transaction(function(n){we(n,o,"SELECT * FROM "+o.storeName,[],function(n,r){var a=r.rows;var i=a.length;for(var s=0;s<i;s++){var c=a.item(s);var u=c.value;if(u){u=o.serializer.deserialize(u)}u=e(u,c.key,s+1);if(u!==void 0){t(u);return}}t()},function(e,t){r(t)})})})["catch"](r)});f(r,t);return r}function Se(e,t,n,r){var o=this;e=d(e);var a=new u(function(a,i){o.ready().then(function(){if(t===undefined){t=null}var s=t;var c=o._dbInfo;c.serializer.serialize(t,function(t,u){if(u){i(u)}else{c.db.transaction(function(n){we(n,c,"INSERT OR REPLACE INTO "+c.storeName+" "+"(key, value) VALUES (?, ?)",[e,t],function(){a(s)},function(e,t){i(t)})},function(t){if(t.code===t.QUOTA_ERR){if(r>0){a(Se.apply(o,[e,s,n,r-1]));return}i(t)}})}})})["catch"](i)});f(a,n);return a}function Ie(e,t,n){return Se.apply(this,[e,t,n,1])}function xe(e,t){var n=this;e=d(e);var r=new u(function(t,r){n.ready().then(function(){var o=n._dbInfo;o.db.transaction(function(n){we(n,o,"DELETE FROM "+o.storeName+" WHERE key = ?",[e],function(){t()},function(e,t){r(t)})})})["catch"](r)});f(r,t);return r}function ke(e){var t=this;var n=new u(function(e,n){t.ready().then(function(){var r=t._dbInfo;r.db.transaction(function(t){we(t,r,"DELETE FROM "+r.storeName,[],function(){e()},function(e,t){n(t)})})})["catch"](n)});f(n,e);return n}function Oe(e){var t=this;var n=new u(function(e,n){t.ready().then(function(){var r=t._dbInfo;r.db.transaction(function(t){we(t,r,"SELECT COUNT(key) as c FROM "+r.storeName,[],function(t,n){var r=n.rows.item(0).c;e(r)},function(e,t){n(t)})})})["catch"](n)});f(n,e);return n}function Ne(e,t){var n=this;var r=new u(function(t,r){n.ready().then(function(){var o=n._dbInfo;o.db.transaction(function(n){we(n,o,"SELECT key FROM "+o.storeName+" WHERE id = ? LIMIT 1",[e+1],function(e,n){var r=n.rows.length?n.rows.item(0).key:null;t(r)},function(e,t){r(t)})})})["catch"](r)});f(r,t);return r}function je(e){var t=this;var n=new u(function(e,n){t.ready().then(function(){var r=t._dbInfo;r.db.transaction(function(t){we(t,r,"SELECT key FROM "+r.storeName,[],function(t,n){var r=[];for(var o=0;o<n.rows.length;o++){r.push(n.rows.item(o).key)}e(r)},function(e,t){n(t)})})})["catch"](n)});f(n,e);return n}function Ce(e){return new u(function(t,n){e.transaction(function(r){r.executeSql("SELECT name FROM sqlite_master "+"WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(n,r){var o=[];for(var a=0;a<r.rows.length;a++){o.push(r.rows.item(a).name)}t({db:e,storeNames:o})},function(e,t){n(t)})},function(e){n(e)})})}function Ae(e,t){t=v.apply(this,arguments);var n=this.config();e=typeof e!=="function"&&e||{};if(!e.name){e.name=e.name||n.name;e.storeName=e.storeName||n.storeName}var r=this;var o;if(!e.name){o=u.reject("Invalid arguments")}else{o=new u(function(t){var o;if(e.name===n.name){o=r._dbInfo.db}else{o=openDatabase(e.name,"","",0)}if(!e.storeName){t(Ce(o))}else{t({db:o,storeNames:[e.storeName]})}}).then(function(e){return new u(function(t,n){e.db.transaction(function(r){function o(e){return new u(function(t,n){r.executeSql("DROP TABLE IF EXISTS "+e,[],function(){t()},function(e,t){n(t)})})}var a=[];for(var i=0,s=e.storeNames.length;i<s;i++){a.push(o(e.storeNames[i]))}u.all(a).then(function(){t()})["catch"](function(e){n(e)})},function(e){n(e)})})})}f(o,t);return o}var Re={_driver:"webSQLStorage",_initStorage:be,_support:G(),iterate:Ee,getItem:_e,setItem:Ie,removeItem:xe,clear:ke,length:Oe,key:Ne,keys:je,dropInstance:Ae};function De(){try{return typeof localStorage!=="undefined"&&"setItem"in localStorage&&!!localStorage.setItem}catch(e){return false}}function Me(e,t){var n=e.name+"/";if(e.storeName!==t.storeName){n+=e.storeName+"/"}return n}function Te(){var e="_localforage_support_test";try{localStorage.setItem(e,true);localStorage.removeItem(e);return false}catch(t){return true}}function Be(){return!Te()||localStorage.length>0}function Le(e){var t=this;var n={};if(e){for(var r in e){n[r]=e[r]}}n.keyPrefix=Me(e,t._defaultConfig);if(!Be()){return u.reject()}t._dbInfo=n;n.serializer=ye;return u.resolve()}function ze(e){var t=this;var n=t.ready().then(function(){var e=t._dbInfo.keyPrefix;for(var n=localStorage.length-1;n>=0;n--){var r=localStorage.key(n);if(r.indexOf(e)===0){localStorage.removeItem(r)}}});f(n,e);return n}function Pe(e,t){var n=this;e=d(e);var r=n.ready().then(function(){var t=n._dbInfo;var r=localStorage.getItem(t.keyPrefix+e);if(r){r=t.serializer.deserialize(r)}return r});f(r,t);return r}function Fe(e,t){var n=this;var r=n.ready().then(function(){var t=n._dbInfo;var r=t.keyPrefix;var o=r.length;var a=localStorage.length;var i=1;for(var s=0;s<a;s++){var c=localStorage.key(s);if(c.indexOf(r)!==0){continue}var u=localStorage.getItem(c);if(u){u=t.serializer.deserialize(u)}u=e(u,c.substring(o),i++);if(u!==void 0){return u}}});f(r,t);return r}function Ve(e,t){var n=this;var r=n.ready().then(function(){var t=n._dbInfo;var r;try{r=localStorage.key(e)}catch(o){r=null}if(r){r=r.substring(t.keyPrefix.length)}return r});f(r,t);return r}function Ue(e){var t=this;var n=t.ready().then(function(){var e=t._dbInfo;var n=localStorage.length;var r=[];for(var o=0;o<n;o++){var a=localStorage.key(o);if(a.indexOf(e.keyPrefix)===0){r.push(a.substring(e.keyPrefix.length))}}return r});f(n,e);return n}function We(e){var t=this;var n=t.keys().then(function(e){return e.length});f(n,e);return n}function qe(e,t){var n=this;e=d(e);var r=n.ready().then(function(){var t=n._dbInfo;localStorage.removeItem(t.keyPrefix+e)});f(r,t);return r}function Ye(e,t,n){var r=this;e=d(e);var o=r.ready().then(function(){if(t===undefined){t=null}var n=t;return new u(function(o,a){var i=r._dbInfo;i.serializer.serialize(t,function(t,r){if(r){a(r)}else{try{localStorage.setItem(i.keyPrefix+e,t);o(n)}catch(s){if(s.name==="QuotaExceededError"||s.name==="NS_ERROR_DOM_QUOTA_REACHED"){a(s)}a(s)}}})})});f(o,n);return o}function Xe(e,t){t=v.apply(this,arguments);e=typeof e!=="function"&&e||{};if(!e.name){var n=this.config();e.name=e.name||n.name;e.storeName=e.storeName||n.storeName}var r=this;var o;if(!e.name){o=u.reject("Invalid arguments")}else{o=new u(function(t){if(!e.storeName){t(e.name+"/")}else{t(Me(e,r._defaultConfig))}}).then(function(e){for(var t=localStorage.length-1;t>=0;t--){var n=localStorage.key(t);if(n.indexOf(e)===0){localStorage.removeItem(n)}}})}f(o,t);return o}var He={_driver:"localStorageWrapper",_initStorage:Le,_support:De(),iterate:Fe,getItem:Pe,setItem:Ye,removeItem:qe,clear:ze,length:We,key:Ve,keys:Ue,dropInstance:Xe};var Ge=function e(t,n){return t===n||typeof t==="number"&&typeof n==="number"&&isNaN(t)&&isNaN(n)};var Ze=function e(t,n){var r=t.length;var o=0;while(o<r){if(Ge(t[o],n)){return true}o++}return false};var Qe=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"};var Ke={};var Je={};var $e={INDEXEDDB:H,WEBSQL:Re,LOCALSTORAGE:He};var et=[$e.INDEXEDDB._driver,$e.WEBSQL._driver,$e.LOCALSTORAGE._driver];var tt=["dropInstance"];var nt=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(tt);var rt={description:"",driver:et.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function ot(e,t){e[t]=function(){var n=arguments;return e.ready().then(function(){return e[t].apply(e,n)})}}function at(){for(var e=1;e<arguments.length;e++){var t=arguments[e];if(t){for(var n in t){if(t.hasOwnProperty(n)){if(Qe(t[n])){arguments[0][n]=t[n].slice()}else{arguments[0][n]=t[n]}}}}}return arguments[0]}var it=function(){function e(t){o(this,e);for(var n in $e){if($e.hasOwnProperty(n)){var r=$e[n];var a=r._driver;this[n]=a;if(!Ke[a]){this.defineDriver(r)}}}this._defaultConfig=at({},rt);this._config=at({},this._defaultConfig,t);this._driverSet=null;this._initDriver=null;this._ready=false;this._dbInfo=null;this._wrapLibraryMethodsWithReady();this.setDriver(this._config.driver)["catch"](function(){})}e.prototype.config=function e(t){if((typeof t==="undefined"?"undefined":r(t))==="object"){if(this._ready){return new Error("Can't call config() after localforage "+"has been used.")}for(var n in t){if(n==="storeName"){t[n]=t[n].replace(/\W/g,"_")}if(n==="version"&&typeof t[n]!=="number"){return new Error("Database version must be a number.")}this._config[n]=t[n]}if("driver"in t&&t.driver){return this.setDriver(this._config.driver)}return true}else if(typeof t==="string"){return this._config[t]}else{return this._config}};e.prototype.defineDriver=function e(t,n,r){var o=new u(function(e,n){try{var r=t._driver;var o=new Error("Custom driver not compliant; see "+"https://mozilla.github.io/localForage/#definedriver");if(!t._driver){n(o);return}var a=nt.concat("_initStorage");for(var i=0,s=a.length;i<s;i++){var c=a[i];var l=!Ze(tt,c);if((l||t[c])&&typeof t[c]!=="function"){n(o);return}}var d=function e(){var n=function e(t){return function(){var e=new Error("Method "+t+" is not implemented by the current driver");var n=u.reject(e);f(n,arguments[arguments.length-1]);return n}};for(var r=0,o=tt.length;r<o;r++){var a=tt[r];if(!t[a]){t[a]=n(a)}}};d();var v=function n(o){if(Ke[r]){console.info("Redefining LocalForage driver: "+r)}Ke[r]=t;Je[r]=o;e()};if("_support"in t){if(t._support&&typeof t._support==="function"){t._support().then(v,n)}else{v(!!t._support)}}else{v(true)}}catch(h){n(h)}});l(o,n,r);return o};e.prototype.driver=function e(){return this._driver||null};e.prototype.getDriver=function e(t,n,r){var o=Ke[t]?u.resolve(Ke[t]):u.reject(new Error("Driver not found."));l(o,n,r);return o};e.prototype.getSerializer=function e(t){var n=u.resolve(ye);l(n,t);return n};e.prototype.ready=function e(t){var n=this;var r=n._driverSet.then(function(){if(n._ready===null){n._ready=n._initDriver()}return n._ready});l(r,t,t);return r};e.prototype.setDriver=function e(t,n,r){var o=this;if(!Qe(t)){t=[t]}var a=this._getSupportedDrivers(t);function i(){o._config.driver=o.driver()}function s(e){o._extend(e);i();o._ready=o._initStorage(o._config);return o._ready}function c(e){return function(){var t=0;function n(){while(t<e.length){var r=e[t];t++;o._dbInfo=null;o._ready=null;return o.getDriver(r).then(s)["catch"](n)}i();var a=new Error("No available storage method found.");o._driverSet=u.reject(a);return o._driverSet}return n()}}var f=this._driverSet!==null?this._driverSet["catch"](function(){return u.resolve()}):u.resolve();this._driverSet=f.then(function(){var e=a[0];o._dbInfo=null;o._ready=null;return o.getDriver(e).then(function(e){o._driver=e._driver;i();o._wrapLibraryMethodsWithReady();o._initDriver=c(a)})})["catch"](function(){i();var e=new Error("No available storage method found.");o._driverSet=u.reject(e);return o._driverSet});l(this._driverSet,n,r);return this._driverSet};e.prototype.supports=function e(t){return!!Je[t]};e.prototype._extend=function e(t){at(this,t)};e.prototype._getSupportedDrivers=function e(t){var n=[];for(var r=0,o=t.length;r<o;r++){var a=t[r];if(this.supports(a)){n.push(a)}}return n};e.prototype._wrapLibraryMethodsWithReady=function e(){for(var t=0,n=nt.length;t<n;t++){ot(this,nt[t])}};e.prototype.createInstance=function t(n){return new e(n)};return e}();var st=new it;t.exports=st},{3:3}]},{},[4])(4)})}).call(this,n("c8ba"))},2370:function(e,t,n){},"4c0d":function(e,t,n){"use strict";var r=n("2370");var o=n.n(r);var a=o.a},"6cf9":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this;var t=e.$createElement;var n=e._self._c||t;return n("section",{staticClass:"cape-bg"},[n("q-slider",{attrs:{min:1e3,max:3e3},on:{input:e.updateWidth},model:{value:e.width,callback:function(t){e.width=t},expression:"width"}}),e._l(e.meshes,function(t){return n("div",{staticClass:"inline"},[n("t-exploded-view",{attrs:{"mesh-id":t.id,"mesh-width":e.widthThrottled}})],1)})],2)};var o=[];var a=n("a34a");var i=n.n(a);var s=n("192a");var c=n("c973");var u=n.n(c);var f=n("9ec3");var l=n.n(f);var d=n("18a5");var v=n("ce62");var h=n("39fc");var p=function(){var e=this;var t=e.$createElement;var n=e._self._c||t;return n("div",{staticClass:"mesh-widget"},[n("keep-alive",[n("canvas",{ref:"root"})])],1)};var m=[];var y=n("d6b6");var g=n("ab50");var b=n("1ae5");var w=n.n(b);var _={props:{meshId:[Number],meshWidth:[Number]},mounted:function e(){this.proxyRenderer=g["a"].createProxy({container:this.$refs.root,width:600,height:400});this.fetchSerialization()},watch:{meshWidth:function e(){this.draw()}},methods:{fetchSerialization:function(){var e=u()(i.a.mark(function e(){var t;return i.a.wrap(function e(n){while(1){switch(n.prev=n.next){case 0:n.next=2;return w.a.getItem("mesh".concat(this.meshId));case 2:t=n.sent;if(t){this.processGeometry(t,true)}else{d["a"].api.geo.componentSet({type:"mesh",id:this.meshId}).forceFetch(true).pipe(this.processGeometry,"preview")}case 4:case"end":return n.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),processGeometry:function e(t,n){this.serialization=t;this.draw();if(!n){w.a.setItem("mesh".concat(this.meshId),t)}},draw:function(){var e=u()(i.a.mark(function e(){var t,n;return i.a.wrap(function e(r){while(1){switch(r.prev=r.next){case 0:t={width:this.meshWidth,height:600,depth:320,mesh_setup:null,geom_id:this.meshId,geom_type:"mesh",distortion:null,density:null,configurator_custom_params:null};r.next=3;return d["a"].decoder.api.getGeometry({serialization:this.serialization,state:t,format:"wireframe"});case 3:n=r.sent;this.proxyRenderer.updateGeometry(this.meshId,n);case 5:case"end":return r.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}()},data:function e(){return{}}};var E=_;var S=n("a027");var I=n("2877");var x=Object(I["a"])(E,p,m,false,null,null,null);var k=x.exports;var O={components:{"t-exploded-view":k},mounted:function e(){var t=this;this.getMeshesForCollection().then(function(e){t.meshes=e.mesh.slice(0,9)});this.updateWidth=l.a.throttle(this._updateWidth,60)},methods:{_updateWidth:function e(t){console.log(t);this.widthThrottled=t},getMeshesForCollection:function(){var e=u()(i.a.mark(function e(){return i.a.wrap(function e(t){while(1){switch(t.prev=t.next){case 0:t.next=2;return d["a"].api.palette.collection(66).meshes.fetch();case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}},e)}));function t(){return e.apply(this,arguments)}return t}()},data:function e(){return{widthThrottled:1500,meshes:[],width:1500}}};var N=O;var j=n("4c0d");var C=Object(I["a"])(N,r,o,false,null,null,null);var A=t["default"]=C.exports},a027:function(e,t,n){"use strict";var r=n("fd27");var o=n.n(r);var a=o.a},ab50:function(e,t,n){"use strict";var r=n("9b8e");var o=n("359c");var a=n("7341");var i=n("1adf");var s=n("1a9d");var c=n("970b");var u=n.n(c);var f=n("5bc3");var l=n.n(f);var d=n("e411");var v=n("9ec3");var h=n.n(v);var p=n("1197");var m=n("723b");var y=window.THREE||n("e411");var g=function e(t,n){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var o=this;var a={NONE:-1,ROTATE:0,ZOOM:1,PAN:2,TOUCH_ROTATE:3,TOUCH_ZOOM_PAN:4};this.object=t;this.domElement=n!==undefined?n:document;this.locked=false;this.enabled=true;this.screen={left:0,top:0,width:0,height:0};this.rotateSpeed=1;this.zoomSpeed=1.2;this.panSpeed=.3;this.noRotate=false;this.noZoom=false;this.noPan=false;this.staticMoving=false;this.dynamicDampingFactor=.2;this.minDistance=0;this.maxDistance=Infinity;this.keys=[65,83,68];this.target=new y.Vector3;var i=1e-6;var s=new y.Vector3;var c=a.NONE,u=a.NONE,f=new y.Vector3,l=new y.Vector2,d=new y.Vector2,v=new y.Vector3,h=0,p=new y.Vector2,m=new y.Vector2,g=0,b=0,w=new y.Vector2,_=new y.Vector2;this.target0=this.target.clone();this.position0=this.object.position.clone();this.up0=this.object.up.clone();var E={type:"change"};var S={type:"start"};var I={type:"end"};console.log("CAMERA");this.handleResize=function(){if(this.domElement===document){this.screen.left=0;this.screen.top=0;this.screen.width=window.innerWidth;this.screen.height=window.innerHeight}else{var e=this.domElement.getBoundingClientRect();var t=this.domElement.ownerDocument.documentElement;this.screen.left=e.left+window.pageXOffset-t.clientLeft;this.screen.top=e.top+window.pageYOffset-t.clientTop;this.screen.width=e.width;this.screen.height=e.height}};this.handleEvent=function(e){if(typeof this[e.type]=="function"){this[e.type](e)}};var x=function(){var e=new y.Vector2;return function t(n,r){e.set((n-o.screen.left)/o.screen.width,(r-o.screen.top)/o.screen.height);return e}}();var k=function(){var e=new y.Vector2;return function t(n,r){e.set((n-o.screen.width*.5-o.screen.left)/(o.screen.width*.5),(o.screen.height+2*(o.screen.top-r))/o.screen.width);return e}}();this.rotateCamera=function(){var e=new y.Vector3,t=new y.Quaternion,n=new y.Vector3,r=new y.Vector3,a=new y.Vector3,i=new y.Vector3,s;var c=0;function u(){var u=i.clone();i.set(d.x-l.x,d.y-l.y,0);s=i.length();var p=30;var m=false;c+=s*(e.y>0?1:-1);var y=c*(180/Math.PI);y=Math.max(-p,Math.min(y,p));if(s){f.copy(o.object.position).sub(o.target);n.copy(f).normalize();r.copy(o.object.up).normalize();a.crossVectors(r,n).normalize();r.setLength(d.y-l.y);a.setLength(d.x-l.x);if(this.locked){i.copy(a)}else{i.copy(r.add(a))}e.crossVectors(i,f).normalize();t.setFromAxisAngle(e,s);f.applyQuaternion(t);if(!this.locked)o.object.up.applyQuaternion(t);v.copy(e);h=s}else if(!o.staticMoving&&h){h*=Math.sqrt(1-o.dynamicDampingFactor);f.copy(o.object.position).sub(o.target);t.setFromAxisAngle(v,h);f.applyQuaternion(t);o.object.up.applyQuaternion(t)}l.copy(d)}return u}();this.zoomCamera=function(){var e;if(c===a.TOUCH_ZOOM_PAN){e=g/b;g=b;f.multiplyScalar(e)}else{e=1+(m.y-p.y)*o.zoomSpeed;if(e!==1&&e>0){f.multiplyScalar(e)}if(o.staticMoving){p.copy(m)}else{p.y+=(m.y-p.y)*this.dynamicDampingFactor}}};this.panCamera=function(){var e=new y.Vector2,t=new y.Vector3,n=new y.Vector3;return function r(){e.copy(_).sub(w);if(e.lengthSq()){e.multiplyScalar(f.length()*o.panSpeed);n.copy(f).cross(o.object.up).setLength(e.x);n.add(t.copy(o.object.up).setLength(e.y));o.object.position.add(n);o.target.add(n);if(o.staticMoving){w.copy(_)}else{w.add(e.subVectors(_,w).multiplyScalar(o.dynamicDampingFactor))}}}}();this.checkDistances=function(){if(!o.noZoom||!o.noPan){if(f.lengthSq()>o.maxDistance*o.maxDistance){o.object.position.addVectors(o.target,f.setLength(o.maxDistance));p.copy(m)}if(f.lengthSq()<o.minDistance*o.minDistance){o.object.position.addVectors(o.target,f.setLength(o.minDistance));p.copy(m)}}};this.update=function(){f.subVectors(o.object.position,o.target);if(!o.noRotate){o.rotateCamera()}if(!o.noZoom){o.zoomCamera()}if(!o.noPan){o.panCamera()}o.object.position.addVectors(o.target,f);o.checkDistances();o.object.lookAt(o.target);if(s.distanceToSquared(o.object.position)>i){o.dispatchEvent(E);s.copy(o.object.position)}};this.reset=function(){c=a.NONE;u=a.NONE;o.target.copy(o.target0);o.object.position.copy(o.position0);o.object.up.copy(o.up0);f.subVectors(o.object.position,o.target);o.object.lookAt(o.target);o.dispatchEvent(E);s.copy(o.object.position)};function O(e,t){if(Array.isArray(e)){return e.indexOf(t)!==-1}else{return e===t}}function N(e){if(o.enabled===false)return;window.removeEventListener("keydown",N);u=c;if(c!==a.NONE){}else if(O(o.keys[a.ROTATE],e.keyCode)&&!o.noRotate){c=a.ROTATE}else if(O(o.keys[a.ZOOM],e.keyCode)&&!o.noZoom){c=a.ZOOM}else if(O(o.keys[a.PAN],e.keyCode)&&!o.noPan){c=a.PAN}}function j(e){if(o.enabled===false)return;c=u;window.addEventListener("keydown",N,false)}function C(e){if(o.enabled===false)return;e.preventDefault();e.stopPropagation();if(c===a.NONE){c=e.button}if(c===a.ROTATE&&!o.noRotate){d.copy(k(e.pageX,e.pageY));l.copy(d)}else if(c===a.ZOOM&&!o.noZoom){p.copy(x(e.pageX,e.pageY));m.copy(p)}else if(c===a.PAN&&!o.noPan){w.copy(x(e.pageX,e.pageY));_.copy(w)}document.addEventListener("mousemove",A,false);document.addEventListener("mouseup",R,false);o.dispatchEvent(S)}function A(e){if(o.enabled===false)return;e.preventDefault();e.stopPropagation();if(c===a.ROTATE&&!o.noRotate){l.copy(d);d.copy(k(e.pageX,e.pageY))}else if(c===a.ZOOM&&!o.noZoom){m.copy(x(e.pageX,e.pageY))}else if(c===a.PAN&&!o.noPan){_.copy(x(e.pageX,e.pageY))}}function R(e){if(o.enabled===false)return;e.preventDefault();e.stopPropagation();c=a.NONE;document.removeEventListener("mousemove",A);document.removeEventListener("mouseup",R);o.dispatchEvent(I)}function D(e){if(o.enabled===false)return;e.preventDefault();e.stopPropagation();switch(e.deltaMode){case 2:p.y-=e.deltaY*.025;break;case 1:p.y-=e.deltaY*.01;break;default:p.y-=e.deltaY*25e-5;break}o.dispatchEvent(S);o.dispatchEvent(I)}function M(e){if(o.enabled===false)return;switch(e.touches.length){case 1:c=a.TOUCH_ROTATE;d.copy(k(e.touches[0].pageX,e.touches[0].pageY));l.copy(d);break;default:c=a.TOUCH_ZOOM_PAN;var t=e.touches[0].pageX-e.touches[1].pageX;var n=e.touches[0].pageY-e.touches[1].pageY;b=g=Math.sqrt(t*t+n*n);var r=(e.touches[0].pageX+e.touches[1].pageX)/2;var i=(e.touches[0].pageY+e.touches[1].pageY)/2;w.copy(x(r,i));_.copy(w);break}o.dispatchEvent(S)}function T(e){if(o.enabled===false)return;e.preventDefault();e.stopPropagation();switch(e.touches.length){case 1:l.copy(d);d.copy(k(e.touches[0].pageX,e.touches[0].pageY));break;default:var t=e.touches[0].pageX-e.touches[1].pageX;var n=e.touches[0].pageY-e.touches[1].pageY;b=Math.sqrt(t*t+n*n);var r=(e.touches[0].pageX+e.touches[1].pageX)/2;var a=(e.touches[0].pageY+e.touches[1].pageY)/2;_.copy(x(r,a));break}console.log("lol")}function B(e){if(o.enabled===false)return;switch(e.touches.length){case 0:c=a.NONE;break;case 1:c=a.TOUCH_ROTATE;d.copy(k(e.touches[0].pageX,e.touches[0].pageY));l.copy(d);break}o.dispatchEvent(I)}function L(e){if(o.enabled===false)return;e.preventDefault()}this.dispose=function(){};this.domElement.addEventListener("contextmenu",L,false);this.domElement.addEventListener("mousedown",C,false);this.domElement.addEventListener("wheel",D,false);this.domElement.addEventListener("touchstart",M,false);this.domElement.addEventListener("touchend",B,false);this.domElement.addEventListener("touchmove",T,false);window.addEventListener("keydown",N,false);window.addEventListener("keyup",j,false);this.handleResize();this.update()};g.prototype=Object.create(y.EventDispatcher.prototype);var b=n("480d");n.d(t,"a",function(){return O});window.THREE=d;var w=null;var _=Object(p["a"])();_.then(function(e){w=e});var E={verticals:true,supports:true,backs:true,doors:true,drawers:true,fills:true,legs:true,accessories:true,spacer:true,colorMode:0,filterMaterialKey:"",filterMaterial:"",filterEnable:false,horizontals:true};var S=new d["LineBasicMaterial"]({color:11184810,linewidth:1});var I=new d["MeshBasicMaterial"]({color:10066329,wireframe:false,transparent:true,polygonOffset:true,polygonOffsetFactor:1,polygonOffsetUnits:1,opacity:.5});var x=0;var k=function(){function e(){u()(this,e);this.scenes=[];this.proxies=[];this.init(100,100)}l()(e,[{key:"resize",value:function e(t){var n=t.width,r=t.height;this.renderer.setSize(n,r);this.renderer.domElement.style.width="".concat(n,"px");this.renderer.domElement.style.height="".concat(r,"px")}},{key:"createCamera",value:function e(t){var n=new d["PerspectiveCamera"](20,1,1,2e4);n.position.z=7e3;n.position.x=400;n.position.y=800;var r=new g(n,t);r.rotateSpeed=1;r.zoomSpeed=1.2;r.panSpeed=.8;r.noZoom=false;r.noPan=false;r.staticMoving=true;r.dynamicDampingFactor=.3;r.target=new d["Vector3"](200,250,0);return{camera:n,controls:r}}},{key:"createProxy",value:function e(t){var n=this;var r=t.container,o=t.width,a=t.height,i=t.cameraMode,s=t.type,c=s===void 0?"wireframe":s,u=t.cameraInstance;r.width=o;r.height=a;var f=x++;if(!this.scenes[f])this.scenes[f]=new d["Scene"];var l=null;var v=null;switch(c){case"gallery":v=new b["a"](r,this.scenes[f]);l={camera:v.camera,controls:v.controls};v.updateAspect(o/a);l.controls.noTransitionAnimation=true;var h=false;var p=function e(){if(h){h=false;n.renderCamera(f);l.controls.update()}window.requestAnimationFrame(e)};p();l.controls.addEventListener("render",function(e){h=true});l.controls.addEventListener("change",function(){n.renderCamera(f)});break;case"wireframe":l=this.createCamera(r);l.camera.aspect=o/a;l.camera.updateProjectionMatrix();break;default:l={camera:null,controls:null};break}var m=this.proxies.push({proxy:f,canvas:r,width:o,height:a,type:c,cameraMode:i,tylkoCamera:v,camera:l.camera,controls:l.controls,createCameraListener:function e(t){l.controls.addEventListener("change",function(){t()})},getPrice:function e(t){return w.getPrice(t)},render:function e(t){n.renderCamera(f,t,true)},setColor:function e(t,n,r,o){w.setColor(r,o)},updateGeometry:function e(t,r){var o=arguments.length>2&&arguments[2]!==undefined?arguments[2]:"0:0";var a=arguments.length>3?arguments[3]:undefined;n.renderScene(f,"".concat(c,"-").concat(t),r,o,a)},setComponentScene:function e(t,r,o){n.renderComponentScene(f,"".concat(c,"-").concat(t),r,o)},getScene:function e(){return n.scenes[f]},flush:function e(t){n.flush(t)},reisze:function e(t){}});var y=this.proxies[m-1];y.proxyId=m;return this.proxies[m-1]}},{key:"getProxyByNo",value:function e(t){return h.a.find(this.proxies,{proxy:t})}},{key:"renderComponentScene",value:function e(t,n,r,o){var a=this.getProxyByNo(t);a.tylkoCamera.setComponentViewFinal(o[0].boundingBox.pMin,o[0].boundingBox.pMax,r.x1+(r.x2-r.x1)/2);this.render(t,n)}},{key:"renderScene",value:function e(t,n,r,o,a){if(r==null)return this.flush(n);var i=this.getProxyByNo(t);switch(i.type){case"gallery":if(!this.scenes[n])this.scenes[n]=new d["Scene"];w.displayShelf(r,o,this.scenes[n],i.camera,this.renderer);if(["shelf","pip"].indexOf(i.cameraMode)>-1){var s=r.boundingBoxForCamera;var c={x:s.pMin[0],y:s.pMin[1],z:s.pMin[2]},u={x:s.pMax[0],y:s.pMax[1],z:s.pMax[2]};if(!i.initedCam){switch(i.cameraMode){case"shelf":i.tylkoCamera.setShelfViewFinal(c,u);i.initedCam=true;break;case"pip":i.tylkoCamera.setPipViewFinal(c,u);i.initedCam=true;break}}else{a=a===undefined?true:a;switch(i.cameraMode){case"shelf":i.tylkoCamera.controls.geometryFixed=a;break;case"component":break;case"pip":break}i.tylkoCamera.updateGeometry(c,u)}}break;case"virtual":if(!this.scenes[n])this.scenes[n]=new d["Scene"];break;default:var f=this.filterElements(r.item.elements,E);this.drawElements(n,f,position);break}this.render(t,n)}},{key:"flush",value:function e(t){this.resetItems(t)}},{key:"init",value:function e(t,n){var r;var o,a;this.canvasAbsoluteHeight=a=n;this.canvasAbsoluteWidth=o=t;var i=new d["WebGLRenderer"]({antialias:true,preserveDrawingBuffer:false,alpha:true});this.renderer=i;i.setSize(o,a)}},{key:"filterElements",value:function e(t,n){return t.filter(function(e){return e["elem_type"]==="H"&&n["horizontals"]||e["elem_type"]==="V"&&n["verticals"]||e["elem_type"]==="S"&&n["supports"]||e["elem_type"]==="B"&&n["backs"]||e["elem_type"]==="D"&&n["doors"]||e["elem_type"]==="O"||e["elem_type"]==="M"||e["elem_type"]==="L"&&n["legs"]||e["elem_type"]==="T"&&n["drawers"]||e["elem_type"]==="FILL"&&n["fills"]||e["elem_type"]==="ACC"&&n["accessories"]||e["elem_type"]==="SPACER"&&n["spacer"]})}},{key:"render",value:function e(t,n){var r=h.a.find(this.proxies,{proxy:t});this.resize({width:r.width,height:r.height});this.renderer.render(this.scenes[n],r.camera);r.currentScene=n;r.canvas.getContext("2d").clearRect(0,0,r.width,r.height);r.canvas.getContext("2d").drawImage(this.renderer.domElement,0,0);r.controls.update()}},{key:"renderCamera",value:function e(t){var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var o=h.a.find(this.proxies,{proxy:t});n=n||this.scenes[o.currentScene]||this.scenes[t];this.renderer.render(n,o.camera);if(r)o.canvas.getContext("2d").clearRect(0,0,o.width,o.height);o.canvas.getContext("2d").drawImage(this.renderer.domElement,0,0)}},{key:"getCompoundBoundingBox",value:function e(t){var n=null;t.traverse(function(e){var t=e.geometry;if(t===undefined)return;t.computeBoundingBox();if(n===null){n=t.boundingBox}else{n.union(t.boundingBox)}});return n}},{key:"drawElements",value:function e(t,n,r){if(!this.scenes[t])this.scenes[t]=new d["Scene"];if(!r)this.resetItems(t);var o=this.scenes[t];var a=new d["Object3D"];for(var i=0;i<n.length;i++){var s=n[i];if(s.components==null)continue;for(var c=0;c<Object.values(s.components).length;c++){var u=Object.values(s.components)[c];if(E.filterEnable==true){if(!((u[E.filterMaterialKey]||"missing").toString().indexOf(E.filterMaterial)>-1)){continue}}var f=[(u.x_domain[1]-u.x_domain[0])/100,(u.y_domain[1]-u.y_domain[0])/100,(u.z_domain[1]-u.z_domain[0])/100];var l=[(u.x_domain[1]+u.x_domain[0])/200,(u.y_domain[1]+u.y_domain[0])/200,(u.z_domain[1]+u.z_domain[0])/200];var v=s.elem_type=="L"?new d["CylinderGeometry"](f[0]/2,f[0]/2,f[1],12,2):new d["BoxGeometry"](f[0],f[1],f[2]);var h=new d["Mesh"](v,I);h.position.x=l[0];h.position.y=l[1];h.position.z=l[2];var p=new d["EdgesGeometry"](h.geometry);var m=new d["LineSegments"](p,S);h.add(m);a.add(h)}}o.add(a);if(r){a.position.x=r.x;a.position.y=r.y;a.position.z=r.z}}},{key:"openAll",value:function e(){w.setDesignerMode(3)}},{key:"closeAll",value:function e(){w.setDesignerMode(1)}},{key:"resetItems",value:function e(t){var n=this.scenes[t];if(n)while(n.children.length){n.remove(n.children[0])}}}]);return e}();var O=new k},fd27:function(e,t,n){}}]);
//# sourceMappingURL=6b051dda.34fb8e96.js.map