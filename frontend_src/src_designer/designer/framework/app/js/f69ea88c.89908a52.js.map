{"version": 3, "sources": ["webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/camera-helper.vue?de95", "webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/camera-helper.vue", "webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/camera-helper.vue?9b8d", "webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/camera-helper.vue?0153", "webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/camera-helper.vue?1215"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "staticStyle", "height", "z-index", "display", "width", "position", "top", "left", "ref", "background", "max-width", "attrs", "name", "label", "geomX", "value", "geomXtemp", "min", "max", "dark", "on", "change", "val", "tylkoCam", "controls", "geometryFixed", "update", "input", "updateGeometry", "geomY", "geomYtemp", "undefined", "color", "click", "switchToShelfView", "switchToComponentView", "$event", "setDefaultfView", "model", "callback", "$$v", "$set", "expression", "type", "geometryOffset", "_n", "screenEdgeOffset", "_e", "<PERSON><PERSON><PERSON><PERSON>", "fov", "updateCamera", "label-always", "drag-range", "label-value", "parseInt", "x", "y", "z", "target", "new_theta", "Math", "PI", "step", "toFixed", "new_phi", "getMatrix", "setMatrix", "left-label-value", "polarAngle", "right-label-value", "azimuthAngle", "staticRenderFns", "camera_helpervue_type_script_lang_js_", "components", "objectSpread_default", "_cape_ui", "data", "ready", "geomZ", "geom", "view1", "view2", "canvas", "temp1", "temp2", "renderer", "scene", "camera2", "controls2", "mounted", "_this", "cape", "application", "viewport", "fixed", "setUp", "createGeometry", "shouldRender", "renderLoop", "window", "requestAnimationFrame", "addEventListener", "methods", "$refs", "c", "three_module", "tylko_camera", "cameraHelper", "camera", "add", "set", "lookAt", "cubeGeo", "cubeMat", "opacity", "transparent", "scale", "size", "steps", "geometry", "material", "i", "vertices", "push", "line", "resizeRendererToDisplaySize", "dom<PERSON>lement", "clientWidth", "clientHeight", "needResize", "setSize", "setScissorForElement", "elem", "canvasRect", "getBoundingClientRect", "elemRect", "right", "bottom", "positiveYUpBottom", "set<PERSON><PERSON>sor", "setViewport", "console", "log", "setScissorTest", "aspect", "visible", "updateProjectionMatrix", "setShelfView", "setComponent<PERSON>iew", "$q", "dialog", "title", "message", "JSON", "stringify", "getCamSettings", "ok", "matrix", "prompt", "setCamSettings", "parse", "cape_camera_camera_helpervue_type_script_lang_js_", "component", "Object", "componentNormalizer", "camera_helper", "__webpack_exports__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_camera_helper_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "__webpack_require__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_camera_helper_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "n", "_unused_webpack_default_export"], "mappings": "sIAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,YAAA,OAAAC,YAAA,CAAgCC,OAAA,SAAiB,CAAAJ,EAAA,OAAYG,YAAA,CAAaE,UAAA,OAAAC,QAAA,QAAAF,OAAA,QAAAG,MAAA,sBAAAC,SAAA,QAAAC,IAAA,MAAAC,KAAA,SAAgI,CAAAV,EAAA,OAAYW,IAAA,QAAAR,YAAA,CAAyBI,MAAA,MAAAH,OAAA,QAAAE,QAAA,kBAAyDN,EAAA,OAAYW,IAAA,QAAAR,YAAA,CAAyBI,MAAA,MAAAH,OAAA,QAAAE,QAAA,oBAAyDN,EAAA,OAAcE,YAAA,WAAAC,YAAA,CAAoCC,OAAA,SAAiB,CAAAJ,EAAA,OAAYE,YAAA,MAAAC,YAAA,CAA+BS,WAAA,MAAAL,MAAA,wBAAkD,CAAAP,EAAA,UAAeW,IAAA,IAAAT,YAAA,MAAAC,YAAA,CAAuCC,OAAA,cAAAG,MAAA,sBAAAC,SAAA,aAAyER,EAAA,OAAcE,YAAA,MAAAC,YAAA,CAA+BU,YAAA,QAAAD,WAAA,QAAAR,OAAA,SAA0D,CAAAJ,EAAA,iBAAsBG,YAAA,CAAaC,OAAA,UAAkB,CAAAJ,EAAA,UAAec,MAAA,CAAOC,KAAA,kBAAwB,CAAAf,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,OAAYE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,KAAApB,EAAAqB,SAAwBjB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCI,MAAAtB,EAAAuB,UAAAC,IAAA,GAAAC,IAAA,IAAAC,KAAA,QAAuDC,GAAA,CAAKC,OAAA,SAAAC,GAAyB7B,EAAAuB,UAAAM,EAAmB7B,EAAA8B,SAAAC,SAAAC,cAAA,KAA4ChC,EAAA8B,SAAAC,SAAAE,UAAmCC,MAAA,SAAAL,GAAyB7B,EAAA8B,SAAAC,SAAAC,cAAA,MAA6ChC,EAAAmC,eAAAN,QAA2B,GAAAzB,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,KAAApB,EAAAoC,SAAwBhC,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCI,MAAAtB,EAAAqC,UAAAb,IAAA,GAAAC,IAAA,IAAAC,KAAA,QAAuDC,GAAA,CAAKC,OAAA,SAAAC,GAAyB7B,EAAAqC,UAAAR,EAAmB7B,EAAA8B,SAAAC,SAAAC,cAAA,KAA4ChC,EAAA8B,SAAAC,SAAAE,UAAkCC,MAAA,SAAAL,GAAyB7B,EAAA8B,SAAAC,SAAAC,cAAA,MAA6ChC,EAAAmC,eAAAG,UAAAT,QAAsC,GAAAzB,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,QAAAmB,MAAA,QAA+BZ,GAAA,CAAKa,MAAAxC,EAAAyC,qBAA+BrC,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,YAAAmB,MAAA,QAAmCZ,GAAA,CAAKa,MAAAxC,EAAA0C,yBAAmCtC,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,UAAAmB,MAAA,OAAgCZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAc,uBAAwC,WAAA5C,EAAA,MAAAI,EAAA,UAAuCc,MAAA,CAAOC,KAAA,uCAA6C,CAAAf,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,OAAYE,YAAA,4BAAuC,CAAAF,EAAA,YAAiBc,MAAA,CAAOE,MAAA,mBAAAmB,MAAA,SAAAb,KAAA,QAA0DmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,WAAAe,SAAA,SAAAC,GAAkE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,aAAAgB,IAAmDE,WAAA,kCAA4C7C,EAAA,YAAiBc,MAAA,CAAOE,MAAA,mBAAAmB,MAAA,SAAAb,KAAA,QAA0DmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,WAAAe,SAAA,SAAAC,GAAkE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,aAAAgB,IAAmDE,WAAA,mCAA4C,GAAA7C,EAAA,OAAgBE,YAAA,4BAAuC,CAAAF,EAAA,YAAiBc,MAAA,CAAOE,MAAA,oBAAAmB,MAAA,SAAAb,KAAA,QAA2DmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,sBAAAe,SAAA,SAAAC,GAA6E/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,wBAAAgB,IAA8DE,WAAA,6CAAuD7C,EAAA,YAAiBc,MAAA,CAAOE,MAAA,mBAAAmB,MAAA,SAAAb,KAAA,QAA0DmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,OAAAe,SAAA,SAAAC,GAA8D/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,SAAAgB,IAA+CE,WAAA,+BAAwC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,iDAAsD,GAAAhB,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,OAAA8B,KAAA,SAAAxB,KAAA,QAA6CC,GAAA,CAAKO,MAAAlC,EAAAmC,gBAA2BU,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAqB,eAAA,KAAAL,SAAA,SAAAC,GAAkE/C,EAAAgD,KAAAhD,EAAA8B,SAAAqB,eAAA,OAAAnD,EAAAoD,GAAAL,KAA2DE,WAAA,kCAA4C7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,QAAA8B,KAAA,SAAAxB,KAAA,QAA8CC,GAAA,CAAKO,MAAAlC,EAAAmC,gBAA2BU,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAqB,eAAA,MAAAL,SAAA,SAAAC,GAAmE/C,EAAAgD,KAAAhD,EAAA8B,SAAAqB,eAAA,QAAAnD,EAAAoD,GAAAL,KAA4DE,WAAA,mCAA6C7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,MAAA8B,KAAA,SAAAxB,KAAA,QAA4CC,GAAA,CAAKO,MAAAlC,EAAAmC,gBAA2BU,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAqB,eAAA,IAAAL,SAAA,SAAAC,GAAiE/C,EAAAgD,KAAAhD,EAAA8B,SAAAqB,eAAA,MAAAnD,EAAAoD,GAAAL,KAA0DE,WAAA,iCAA2C7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,SAAA8B,KAAA,SAAAxB,KAAA,QAA+CC,GAAA,CAAKO,MAAAlC,EAAAmC,gBAA2BU,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAqB,eAAA,OAAAL,SAAA,SAAAC,GAAoE/C,EAAAgD,KAAAhD,EAAA8B,SAAAqB,eAAA,SAAAnD,EAAAoD,GAAAL,KAA6DE,WAAA,oCAA8C7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,QAAA8B,KAAA,SAAAxB,KAAA,QAA8CC,GAAA,CAAKO,MAAAlC,EAAAmC,gBAA2BU,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAqB,eAAA,MAAAL,SAAA,SAAAC,GAAmE/C,EAAAgD,KAAAhD,EAAA8B,SAAAqB,eAAA,QAAAnD,EAAAoD,GAAAL,KAA4DE,WAAA,mCAA6C7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,OAAA8B,KAAA,SAAAxB,KAAA,QAA6CC,GAAA,CAAKO,MAAAlC,EAAAmC,gBAA2BU,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAqB,eAAA,KAAAL,SAAA,SAAAC,GAAkE/C,EAAAgD,KAAAhD,EAAA8B,SAAAqB,eAAA,OAAAnD,EAAAoD,GAAAL,KAA2DE,WAAA,mCAA4C,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,qDAA0D,GAAAhB,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,OAAA8B,KAAA,SAAAxB,KAAA,QAA6CC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAAC,SAAAE,WAAuCY,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAAsB,iBAAA,KAAAP,SAAA,SAAAC,GAA6E/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAAsB,iBAAA,OAAArD,EAAAoD,GAAAL,KAAsEE,WAAA,6CAAuD7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,QAAA8B,KAAA,SAAAxB,KAAA,QAA8CC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAAC,SAAAE,WAAuCY,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAAsB,iBAAA,MAAAP,SAAA,SAAAC,GAA8E/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAAsB,iBAAA,QAAArD,EAAAoD,GAAAL,KAAuEE,WAAA,8CAAwD7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,MAAA8B,KAAA,SAAAxB,KAAA,QAA4CC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAAC,SAAAE,WAAuCY,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAAsB,iBAAA,IAAAP,SAAA,SAAAC,GAA4E/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAAsB,iBAAA,MAAArD,EAAAoD,GAAAL,KAAqEE,WAAA,4CAAsD7C,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,SAAA8B,KAAA,SAAAxB,KAAA,QAA+CC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAAC,SAAAE,WAAuCY,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAAsB,iBAAA,OAAAP,SAAA,SAAAC,GAA+E/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAAsB,iBAAA,SAAArD,EAAAoD,GAAAL,KAAwEE,WAAA,gDAAyD,WAAAjD,EAAAsD,KAAAtD,EAAA,MAAAI,EAAA,UAAgDc,MAAA,CAAOC,KAAA,oBAA0B,CAAAf,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,OAAYE,YAAA,4BAAuC,CAAAF,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,OAAAmB,MAAA,QAA8BZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAyB,QAAA,YAAsCnD,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,IAAAmB,MAAA,OAA0BZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAyB,QAAA,qBAA+CnD,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,QAAAmB,MAAA,QAA+BZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAyB,QAAA,aAAuCnD,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,QAAAmB,MAAA,QAA+BZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAyB,QAAA,aAAuCnD,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,IAAAmB,MAAA,OAA0BZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAyB,QAAA,sBAAgDnD,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,MAAAmB,MAAA,QAA6BZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAyB,QAAA,WAAqCnD,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,IAAAmB,MAAA,OAA0BZ,GAAA,CAAKa,MAAA,SAAAG,GAAyB,OAAA3C,EAAA8B,SAAAyB,QAAA,qBAA8C,GAAAnD,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,OAAApB,EAAA8B,SAAA0B,OAAiCpD,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,IAAA,GAAAC,IAAA,IAAAC,KAAA,QAAiCC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAA,IAAAgB,SAAA,SAAAC,GAAkD/C,EAAAgD,KAAAhD,EAAA8B,SAAA,MAAAiB,IAAmCE,WAAA,mBAA4B,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,WAAiBhB,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCM,IAAA,EAAAC,IAAA,IAAAiC,eAAA,eAAAC,aAAA,aAAAjC,KAAA,QAA0FC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAA,MAAAgB,SAAA,SAAAC,GAAoD/C,EAAAgD,KAAAhD,EAAA8B,SAAA,QAAAiB,IAAqCE,WAAA,qBAA8B,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,gBAAsBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,KAAA,IAAAC,IAAA,IAAAiC,eAAA,eAAAE,cAAAC,SAAA7D,EAAA8B,SAAAlB,SAAAkD,GAAApC,KAAA,QAAmHC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAlB,SAAA,EAAAkC,SAAA,SAAAC,GAAyD/C,EAAAgD,KAAAhD,EAAA8B,SAAAlB,SAAA,IAAAmC,IAA0CE,WAAA,0BAAmC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,gBAAsBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,KAAA,IAAAC,IAAA,IAAAiC,eAAA,eAAAE,cAAAC,SAAA7D,EAAA8B,SAAAlB,SAAAmD,GAAArC,KAAA,QAAmHC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAlB,SAAA,EAAAkC,SAAA,SAAAC,GAAyD/C,EAAAgD,KAAAhD,EAAA8B,SAAAlB,SAAA,IAAAmC,IAA0CE,WAAA,0BAAmC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,gBAAsBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,KAAA,IAAAC,IAAA,IAAAiC,eAAA,eAAAE,cAAAC,SAAA7D,EAAA8B,SAAAlB,SAAAoD,GAAAtC,KAAA,QAAmHC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAlB,SAAA,EAAAkC,SAAA,SAAAC,GAAyD/C,EAAAgD,KAAAhD,EAAA8B,SAAAlB,SAAA,IAAAmC,IAA0CE,WAAA,0BAAmC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,cAAoBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,KAAA,IAAAC,IAAA,IAAAiC,eAAA,eAAAE,cAAAC,SAAA7D,EAAA8B,SAAAmC,OAAAH,GAAApC,KAAA,QAA+GC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAmC,OAAA,EAAAnB,SAAA,SAAAC,GAAuD/C,EAAAgD,KAAAhD,EAAA8B,SAAAmC,OAAA,IAAAlB,IAAwCE,WAAA,wBAAiC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,cAAoBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,KAAA,IAAAC,IAAA,IAAAiC,eAAA,eAAAE,cAAAC,SAAA7D,EAAA8B,SAAAmC,OAAAF,GAAArC,KAAA,QAA+GC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAmC,OAAA,EAAAnB,SAAA,SAAAC,GAAuD/C,EAAAgD,KAAAhD,EAAA8B,SAAAmC,OAAA,IAAAlB,IAAwCE,WAAA,wBAAiC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,cAAoBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,KAAA,IAAAC,IAAA,IAAAiC,eAAA,eAAAE,cAAAC,SAAA7D,EAAA8B,SAAAmC,OAAAD,GAAAtC,KAAA,QAA+GC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAA2B,iBAAoCZ,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAmC,OAAA,EAAAnB,SAAA,SAAAC,GAAuD/C,EAAAgD,KAAAhD,EAAA8B,SAAAmC,OAAA,IAAAlB,IAAwCE,WAAA,wBAAiC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,kBAAwBpB,EAAA8B,SAAAC,SAAAmC,YAAA,KAAA9D,EAAA,YAA4DE,YAAA,cAAAY,MAAA,CAAiCgC,KAAA,SAAA1B,KAAA2C,KAAAC,GAAA3C,IAAA0C,KAAAC,GAAAC,KAAA,IAAAX,eAAA,eAAAE,eAAA5D,EAAA8B,SAAAC,SAAA,UAAAuC,QAAA,GAAA5C,KAAA,QAAiKC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAAC,SAAAE,WAAuCY,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,UAAAe,SAAA,SAAAC,GAAiE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,YAAA/B,EAAAoD,GAAAL,KAA0DE,WAAA,iCAA2CjD,EAAAsD,MAAA,GAAAlD,EAAA,OAAyBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,gBAAsBpB,EAAA8B,SAAAC,SAAAwC,UAAA,KAAAnE,EAAA,YAA0DE,YAAA,cAAAY,MAAA,CAAiCgC,KAAA,SAAA1B,IAAA,EAAAC,IAAA0C,KAAAC,GAAAC,KAAA,IAAAX,eAAA,eAAAE,eAAA5D,EAAA8B,SAAAC,SAAA,QAAAuC,QAAA,GAAA5C,KAAA,QAAwJC,GAAA,CAAKO,MAAA,SAAAS,GAAyB,OAAA3C,EAAA8B,SAAAC,SAAAE,WAAuCY,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,QAAAe,SAAA,SAAAC,GAA+D/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,UAAA/B,EAAAoD,GAAAL,KAAwDE,WAAA,+BAAyCjD,EAAAsD,MAAA,GAAAlD,EAAA,OAAyBE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,aAAAmB,MAAA,SAAqCZ,GAAA,CAAKa,MAAAxC,EAAAwE,aAAuBpE,EAAA,OAAYE,YAAA,eAAyBF,EAAA,SAAcE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,aAAAmB,MAAA,OAAmCZ,GAAA,CAAKa,MAAAxC,EAAAyE,cAAuB,WAAAzE,EAAAsD,KAAAtD,EAAA,MAAAI,EAAA,UAAgDc,MAAA,CAAOC,KAAA,mBAAyB,CAAAf,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,OAAYE,YAAA,gBAA2B,CAAAF,EAAA,OAAYE,YAAA,4BAAuC,CAAAF,EAAA,YAAiBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,SAAAmB,MAAA,QAAAb,KAAA,QAA+CmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,QAAAe,SAAA,SAAAC,GAA+D/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,UAAAgB,IAAgDE,WAAA,+BAAyC7C,EAAA,YAAiBE,YAAA,aAAAY,MAAA,CAAgCE,MAAA,WAAAmB,MAAA,MAAAb,KAAA,QAA+CmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,SAAAe,SAAA,SAAAC,GAAgE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,WAAAgB,IAAiDE,WAAA,gCAA0C7C,EAAA,YAAiBc,MAAA,CAAOE,MAAA,QAAAmB,MAAA,MAAAb,KAAA,QAA4CmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,MAAAe,SAAA,SAAAC,GAA6D/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,QAAAgB,IAA8CE,WAAA,8BAAuC,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,iBAAuBhB,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCM,IAAA,EAAAC,IAAA0C,KAAAC,GAAAC,KAAA,IAAAK,mBAAA1E,EAAA8B,SAAAC,SAAA4C,YAAA3E,EAAA8B,SAAAC,SAAA4C,WAAA,IAAAL,QAAA,KAAAM,oBAAA5E,EAAA8B,SAAAC,SAAA4C,YAAA3E,EAAA8B,SAAAC,SAAA4C,WAAA,IAAAL,QAAA,KAAAZ,eAAA,eAAAC,aAAA,aAAAjC,KAAA,QAAmUmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,WAAAe,SAAA,SAAAC,GAAkE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,aAAAgB,IAAmDE,WAAA,mCAA4C,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,mBAAyBhB,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCM,KAAA2C,KAAAC,GAAA3C,IAAA0C,KAAAC,GAAAC,KAAA,IAAAK,mBAAA1E,EAAA8B,SAAAC,SAAA8C,cAAA7E,EAAA8B,SAAAC,SAAA8C,aAAA,IAAAP,QAAA,KAAAM,oBAAA5E,EAAA8B,SAAAC,SAAA8C,cAAA7E,EAAA8B,SAAAC,SAAA8C,aAAA,IAAAP,QAAA,KAAAZ,eAAA,eAAAC,aAAA,aAAAjC,KAAA,QAAkVmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,aAAAe,SAAA,SAAAC,GAAoE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,eAAAgB,IAAqDE,WAAA,qCAA8C,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,eAAqBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,IAAA,GAAAC,IAAA,EAAA4C,KAAA,GAAAX,eAAA,eAAAhC,KAAA,QAAyEmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,UAAAe,SAAA,SAAAC,GAAiE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,YAAAgB,IAAkDE,WAAA,kCAA2C,GAAA7C,EAAA,OAAgBE,YAAA,YAAuB,CAAAF,EAAA,WAAgBE,YAAA,cAAAY,MAAA,CAAiCE,MAAA,iBAAuBhB,EAAA,YAAiBE,YAAA,cAAAY,MAAA,CAAiCM,IAAA,GAAAC,IAAA,EAAA4C,KAAA,GAAAX,eAAA,eAAAhC,KAAA,QAAyEmB,MAAA,CAAQvB,MAAAtB,EAAA8B,SAAAC,SAAA,YAAAe,SAAA,SAAAC,GAAmE/C,EAAAgD,KAAAhD,EAAA8B,SAAAC,SAAA,cAAAgB,IAAoDE,WAAA,oCAA6C,WAAAjD,EAAAsD,MAAA,YACp4hB,IAAAwB,EAAA,gIC6XA,IAAAC,EAAA,CACAC,WAAAC,IAAA,GACAC,EAAA,MAGAC,KALA,SAAAA,IAMA,OACAC,MAAA,MAEA/D,MAAA,IACAE,UAAA,IACAa,MAAA,IACAC,UAAA,IACAgD,MAAA,GACAC,KAAA,KAEAC,MAAA,KACAC,MAAA,KACAC,OAAA,KAEAC,MAAA,EACAC,MAAA,EAEAC,SAAA,KACAC,MAAA,KACA/D,SAAA,KACAgE,QAAA,KACAC,UAAA,OAIAC,QA/BA,SAAAA,IA+BA,IAAAC,EAAAhG,KACAiG,EAAA,KAAAC,YAAAC,SAAAC,MAAA,MACApG,KAAAqG,QACArG,KAAAsG,iBACAtG,KAAAF,SAEAE,KAAAmF,MAAA,KAIA,IAAAoB,EAAA,MAEAvG,KAAAwG,WAAA,WACA,GAAAD,EAAA,CACAA,EAAA,MACAP,EAAAlG,SACAkG,EAAAnE,SAAAC,SAAAE,SAEAyE,OAAAC,sBAAAV,EAAAQ,aAGAxG,KAAAwG,aAEAxG,KAAA6B,SAAAC,SAAA6E,iBAAA,oBACAJ,EAAA,OAGAvG,KAAA6B,SAAAC,SAAA6E,iBAAA,oBACAX,EAAAlG,WAIAE,KAAA8F,UAAAa,iBAAA,oBACAX,EAAAlG,YAKA8G,QAAA,CACAP,MADA,SAAAA,IAEArG,KAAAwF,OAAAxF,KAAA6G,MAAAC,EACA9G,KAAAsF,MAAAtF,KAAA6G,MAAAvB,MACAtF,KAAAuF,MAAAvF,KAAA6G,MAAAtB,MACAvF,KAAA4F,MAAA,IAAAmB,EAAA,SACA/G,KAAA4F,MAAA7E,WAAA,IAAAgG,EAAA,kBACA/G,KAAA2F,SAAA,IAAAoB,EAAA,kBAAAvB,OAAAxF,KAAAwF,SAEAxF,KAAA6B,SAAA,IAAAmF,EAAA,KAAAhH,KAAAsF,MAAAtF,KAAA4F,OACA5F,KAAAiH,aAAA,IAAAF,EAAA,gBAAA/G,KAAA6B,SAAAqF,QACAlH,KAAA4F,MAAAuB,IAAAnH,KAAAiH,cACAjH,KAAA4F,MAAAuB,IAAAnH,KAAA6B,SAAAqF,QAEAlH,KAAA6F,QAAA,IAAAkB,EAAA,kCACA/G,KAAA6F,QAAAlF,SAAAyG,IAAA,aACApH,KAAA6F,QAAAwB,OAAA,OAEArH,KAAA8F,UAAA,IAAAiB,EAAA,iBAAA/G,KAAA6F,QAAA7F,KAAAuF,OACAvF,KAAA8F,UAAA9B,OAAAoD,IAAA,OACApH,KAAA8F,UAAA9D,UAGAsE,eAvBA,SAAAA,IAwBA,IAAAgB,EAAA,IAAAP,EAAA,4BACA,IAAAQ,EAAA,IAAAR,EAAA,sBAAAzE,MAAA,OAAAkF,QAAA,GAAAC,YAAA,QACAzH,KAAAqF,KAAA,IAAA0B,EAAA,QAAAO,EAAAC,GACAvH,KAAAqF,KAAAqC,MAAAN,IAAApH,KAAAoB,MAAApB,KAAAmC,MAAAnC,KAAAoF,OACApF,KAAAqF,KAAA1E,SAAAyG,IAAA,EAAApH,KAAAmC,MAAA,EAAAnC,KAAAoF,MAAA,GACApF,KAAA4F,MAAAuB,IAAAnH,KAAAqF,MAEA,IAAAsC,EAAA,IACAC,EAAA,GACA,IAAAC,EAAA,IAAAd,EAAA,YACA,IAAAe,EAAA,IAAAf,EAAA,sBACAzE,MAAA,SAEA,QAAAyF,GAAAJ,EAAAI,GAAAJ,EAAAI,GAAAH,EAAA,CACA,GAAAG,GAAA,GACAF,EAAAG,SAAAC,KAAA,IAAAlB,EAAA,YAAAY,GAAA,IAAAI,IACAF,EAAAG,SAAAC,KAAA,IAAAlB,EAAA,WAAAY,GAAA,IAAAI,IAEA,GAAAJ,EAAAI,GAAA,KACAF,EAAAG,SAAAC,KACA,IAAAlB,EAAA,YAAAY,IAAAI,EAAA,QAEAF,EAAAG,SAAAC,KACA,IAAAlB,EAAA,WAAAY,IAAAI,EAAA,QAIAF,EAAAG,SAAAC,KAAA,IAAAlB,EAAA,WAAAgB,GAAA,QACAF,EAAAG,SAAAC,KAAA,IAAAlB,EAAA,WAAAgB,GAAA,IAAAJ,IACAE,EAAAG,SAAAC,KAAA,IAAAlB,EAAA,WAAAgB,GAAA,QACAF,EAAAG,SAAAC,KAAA,IAAAlB,EAAA,WAAAgB,EAAA,YAEA,IAAAG,EAAA,IAAAnB,EAAA,QAAAc,EAAAC,EAAAf,EAAA,eACA/G,KAAA4F,MAAAuB,IAAAe,GACAlI,KAAA6B,SAAAK,eACA,CAAA2B,GAAA7D,KAAAoB,MAAA,EAAA0C,EAAA,EAAAC,EAAA,GACA,CAAAF,EAAA7D,KAAAoB,MAAA,EAAA0C,EAAA9D,KAAAmC,MAAA4B,EAAA/D,KAAAoF,SAKA+C,4BAjEA,SAAAA,EAiEAxC,GACA,IAAAH,EAAAG,EAAAyC,WACA,IAAA1H,EAAA8E,EAAA6C,YACA,IAAA9H,EAAAiF,EAAA8C,aACA,IAAAC,EACA/C,EAAA9E,WAAA8E,EAAAjF,WACA,GAAAgI,EAAA,CACA5C,EAAA6C,QAAA9H,EAAAH,EAAA,OAEA,OAAAgI,GAGAE,qBA7EA,SAAAA,EA6EAC,GACA,IAAAC,EAAA3I,KAAAwF,OAAAoD,wBACA,IAAAC,EAAAH,EAAAE,wBAEA,IAAAE,EACA5E,KAAA3C,IAAAsH,EAAAC,MAAAH,EAAAG,OAAAH,EAAA9H,KACA,IAAAA,EAAAqD,KAAA1C,IAAA,EAAAqH,EAAAhI,KAAA8H,EAAA9H,MACA,IAAAkI,EACA7E,KAAA3C,IAAAsH,EAAAE,OAAAJ,EAAAI,QAAAJ,EAAA/H,IACA,IAAAA,EAAAsD,KAAA1C,IAAA,EAAAqH,EAAAjI,IAAA+H,EAAA/H,KAEA,IAAAF,EAAAwD,KAAA3C,IAAAoH,EAAAjI,MAAAoI,EAAAjI,GACA,IAAAN,EAAA2D,KAAA3C,IAAAoH,EAAApI,OAAAwI,EAAAnI,GAEA,IAAAoI,EAAAL,EAAApI,OAAAwI,EACA/I,KAAA2F,SAAAsD,WAAApI,EAAAmI,EAAAtI,EAAAH,GACAP,KAAA2F,SAAAuD,YAAArI,EAAAmI,EAAAtI,EAAAH,GAEA,OAAAG,EAAAH,GAGAT,OAlGA,SAAAA,IAmGAqJ,QAAAC,IAAA,gBACApJ,KAAAmI,4BAAAnI,KAAA2F,UACA3F,KAAA2F,SAAA0D,eAAA,MAGA,IAAAC,EAAAtJ,KAAAyI,qBAAAzI,KAAAsF,OACAtF,KAAA6B,SAAAG,OAAAsH,GACAtJ,KAAAiH,aAAAjF,SACAhC,KAAAiH,aAAAsC,QAAA,MACAvJ,KAAA4F,MAAA7E,WAAAqG,IAAA,GACApH,KAAA2F,SAAA7F,OAAAE,KAAA4F,MAAA5F,KAAA6B,SAAAqF,QAGAoC,EAAAtJ,KAAAyI,qBAAAzI,KAAAuF,OACAvF,KAAA6F,QAAAyD,SACAtJ,KAAA6F,QAAA2D,yBACAxJ,KAAAiH,aAAAsC,QAAA,KACAvJ,KAAA4F,MAAA7E,WAAAqG,IAAA,IACApH,KAAA2F,SAAA7F,OAAAE,KAAA4F,MAAA5F,KAAA6F,UAGA3D,eAxHA,SAAAA,EAwHAd,EAAAe,GACA,GAAAf,IAAAiB,UAAArC,KAAAoB,QACA,GAAAe,IAAAE,UAAArC,KAAAmC,QACAnC,KAAAqF,KAAAqC,MAAAN,IAAApH,KAAAoB,MAAApB,KAAAmC,MAAAnC,KAAAoF,OACApF,KAAAqF,KAAA1E,SAAAmD,EAAA9D,KAAAmC,MAAA,EACAnC,KAAA6B,SAAAK,eACA,CAAA2B,GAAA7D,KAAAoB,MAAA,EAAA0C,EAAA,EAAAC,EAAA,GACA,CAAAF,EAAA7D,KAAAoB,MAAA,EAAA0C,EAAA9D,KAAAmC,MAAA4B,EAAA/D,KAAAoF,QAEApF,KAAAF,UAGA0C,kBApIA,SAAAA,IAqIA2G,QAAAC,IAAA,cACApJ,KAAA6B,SAAA4H,aACA,CAAA5F,GAAA7D,KAAAoB,MAAA,EAAA0C,EAAA,EAAAC,EAAA,GACA,CAAAF,EAAA7D,KAAAoB,MAAA,EAAA0C,EAAA9D,KAAAmC,MAAA4B,EAAA/D,KAAAoF,SAIA3C,sBA5IA,SAAAA,EA4IArB,EAAAe,GACAgH,QAAAC,IAAA,kBACApJ,KAAA6B,SAAA6H,iBACA,CAAA7F,GAAA,GAAAC,EAAA,EAAAC,EAAA,GACA,CAAAF,EAAA,GAAAC,EAAA9D,KAAAmC,MAAA4B,EAAA/D,KAAAoF,SAIAb,UApJA,SAAAA,IAqJAvE,KAAA2J,GAAAC,OAAA,CACAC,MAAA,SACAC,QAAAC,KAAAC,UAAAhK,KAAA6B,SAAAoI,kBACAC,GAAA,QAGA1F,UA3JA,SAAAA,IA4JA,IAAA2F,EAAAC,OAAA,wBACA,GAAAD,EAAAnK,KAAA6B,SAAAwI,eAAAN,KAAAO,MAAAH,OChmB6O,IAAAI,EAAA,kCCQ7O,IAAAC,EAAgBC,OAAAC,EAAA,KAAAD,CACdF,EACAzK,EACA+E,EACF,MACA,KACA,KACA,MAIe,IAAA8F,EAAAC,EAAA,WAAAJ,6CCnBf,IAAAK,EAAAC,EAAA,YAAAC,EAAAD,EAAAE,EAAAH,GAAglB,IAAAI,EAAAF,EAAG", "file": "js/f69ea88c.89908a52.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"main\",staticStyle:{\"height\":\"100%\"}},[_c('div',{staticStyle:{\"z-index\":\"1000\",\"display\":\"block\",\"height\":\"100vh\",\"width\":\"calc(100vw - 500px)\",\"position\":\"fixed\",\"top\":\"0px\",\"left\":\"60px\"}},[_c('div',{ref:\"view1\",staticStyle:{\"width\":\"40%\",\"height\":\"100vh\",\"display\":\"inline-block\"}}),_c('div',{ref:\"view2\",staticStyle:{\"width\":\"60%\",\"height\":\"100vh\",\"display\":\"inline-block\"}})]),_c('div',{staticClass:\"flex row\",staticStyle:{\"height\":\"100%\"}},[_c('div',{staticClass:\"col\",staticStyle:{\"background\":\"red\",\"width\":\"calc(100vw - 500px)\"}},[_c('canvas',{ref:\"c\",staticClass:\"col\",staticStyle:{\"height\":\"calc(100vh)\",\"width\":\"calc(100vw - 500px)\",\"position\":\"fixed\"}})]),_c('div',{staticClass:\"col\",staticStyle:{\"max-width\":\"440px\",\"background\":\"white\",\"height\":\"100%\"}},[_c('q-scroll-area',{staticStyle:{\"height\":\"100vh\"}},[_c('t-card',{attrs:{\"name\":\"Geometry size\"}},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-main\"},[_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-6\",attrs:{\"label\":'X:'+_vm.geomX}}),_c('q-slider',{staticClass:\"col-card-17\",attrs:{\"value\":_vm.geomXtemp,\"min\":40,\"max\":500,\"dark\":\"dark\"},on:{\"change\":function (val) { _vm.geomXtemp=val; _vm.tylkoCam.controls.geometryFixed = true;  _vm.tylkoCam.controls.update(); },\"input\":function (val) { _vm.tylkoCam.controls.geometryFixed = false; _vm.updateGeometry(val)}}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-6\",attrs:{\"label\":'Y:'+_vm.geomY}}),_c('q-slider',{staticClass:\"col-card-17\",attrs:{\"value\":_vm.geomYtemp,\"min\":20,\"max\":300,\"dark\":\"dark\"},on:{\"change\":function (val) { _vm.geomYtemp=val; _vm.tylkoCam.controls.geometryFixed = true; _vm.tylkoCam.controls.update(); },\"input\":function (val) { _vm.tylkoCam.controls.geometryFixed = false; _vm.updateGeometry(undefined, val)}}})],1),_c('div',{staticClass:\"card-row\"},[_c('div',{staticClass:\"col-card-1\"}),_c('q-btn',{staticClass:\"col-card-6\",attrs:{\"label\":\"Shelf\",\"color\":\"blue\"},on:{\"click\":_vm.switchToShelfView}}),_c('div',{staticClass:\"col-card-1\"}),_c('q-btn',{staticClass:\"col-card-6\",attrs:{\"label\":\"Component\",\"color\":\"blue\"},on:{\"click\":_vm.switchToComponentView}}),_c('div',{staticClass:\"col-card-1\"}),_c('q-btn',{staticClass:\"col-card-6\",attrs:{\"label\":\"Madness\",\"color\":\"red\"},on:{\"click\":function($event){return _vm.tylkoCam.setDefaultfView()}}})],1)])])])]),(_vm.ready)?_c('t-card',{attrs:{\"name\":\"Camera Automatic Zoom Calculations\"}},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-main\"},[_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-row q-pa-sm q-pb-md\"},[_c('q-toggle',{attrs:{\"label\":\"Disable AutoZoom\",\"color\":\"yellow\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.noAutoZoom),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"noAutoZoom\", $$v)},expression:\"tylkoCam.controls.noAutoZoom\"}}),_c('q-toggle',{attrs:{\"label\":\"Disable LifeZoom\",\"color\":\"yellow\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.noLifeZoom),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"noLifeZoom\", $$v)},expression:\"tylkoCam.controls.noLifeZoom\"}})],1),_c('div',{staticClass:\"card-row q-pa-sm q-pb-md\"},[_c('q-toggle',{attrs:{\"label\":\"Disable Animation\",\"color\":\"yellow\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.noTransitionAnimation),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"noTransitionAnimation\", $$v)},expression:\"tylkoCam.controls.noTransitionAnimation\"}}),_c('q-toggle',{attrs:{\"label\":\"Disable Snapping\",\"color\":\"yellow\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.noSnap),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"noSnap\", $$v)},expression:\"tylkoCam.controls.noSnap\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-24\",attrs:{\"label\":\"Geometry to ViewEdge Offsets in object cm:\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-input',{staticClass:\"col-card-4\",attrs:{\"label\":\"Left\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":_vm.updateGeometry},model:{value:(_vm.tylkoCam.geometryOffset.left),callback:function ($$v) {_vm.$set(_vm.tylkoCam.geometryOffset, \"left\", _vm._n($$v))},expression:\"tylkoCam.geometryOffset.left\"}}),_c('q-input',{staticClass:\"col-card-4\",attrs:{\"label\":\"Right\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":_vm.updateGeometry},model:{value:(_vm.tylkoCam.geometryOffset.right),callback:function ($$v) {_vm.$set(_vm.tylkoCam.geometryOffset, \"right\", _vm._n($$v))},expression:\"tylkoCam.geometryOffset.right\"}}),_c('q-input',{staticClass:\"col-card-4\",attrs:{\"label\":\"Top\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":_vm.updateGeometry},model:{value:(_vm.tylkoCam.geometryOffset.top),callback:function ($$v) {_vm.$set(_vm.tylkoCam.geometryOffset, \"top\", _vm._n($$v))},expression:\"tylkoCam.geometryOffset.top\"}}),_c('q-input',{staticClass:\"col-card-4\",attrs:{\"label\":\"Bottom\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":_vm.updateGeometry},model:{value:(_vm.tylkoCam.geometryOffset.bottom),callback:function ($$v) {_vm.$set(_vm.tylkoCam.geometryOffset, \"bottom\", _vm._n($$v))},expression:\"tylkoCam.geometryOffset.bottom\"}}),_c('q-input',{staticClass:\"col-card-4\",attrs:{\"label\":\"Front\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":_vm.updateGeometry},model:{value:(_vm.tylkoCam.geometryOffset.front),callback:function ($$v) {_vm.$set(_vm.tylkoCam.geometryOffset, \"front\", _vm._n($$v))},expression:\"tylkoCam.geometryOffset.front\"}}),_c('q-input',{staticClass:\"col-card-4\",attrs:{\"label\":\"Back\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":_vm.updateGeometry},model:{value:(_vm.tylkoCam.geometryOffset.back),callback:function ($$v) {_vm.$set(_vm.tylkoCam.geometryOffset, \"back\", _vm._n($$v))},expression:\"tylkoCam.geometryOffset.back\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-24\",attrs:{\"label\":\"Geometry to ViewEdge Offsets in screen pixels:\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-input',{staticClass:\"col-card-6\",attrs:{\"label\":\"Left\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.controls.update()}},model:{value:(_vm.tylkoCam.controls.screenEdgeOffset.left),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls.screenEdgeOffset, \"left\", _vm._n($$v))},expression:\"tylkoCam.controls.screenEdgeOffset.left\"}}),_c('q-input',{staticClass:\"col-card-6\",attrs:{\"label\":\"Right\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.controls.update()}},model:{value:(_vm.tylkoCam.controls.screenEdgeOffset.right),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls.screenEdgeOffset, \"right\", _vm._n($$v))},expression:\"tylkoCam.controls.screenEdgeOffset.right\"}}),_c('q-input',{staticClass:\"col-card-6\",attrs:{\"label\":\"Top\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.controls.update()}},model:{value:(_vm.tylkoCam.controls.screenEdgeOffset.top),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls.screenEdgeOffset, \"top\", _vm._n($$v))},expression:\"tylkoCam.controls.screenEdgeOffset.top\"}}),_c('q-input',{staticClass:\"col-card-6\",attrs:{\"label\":\"Bottom\",\"type\":\"number\",\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.controls.update()}},model:{value:(_vm.tylkoCam.controls.screenEdgeOffset.bottom),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls.screenEdgeOffset, \"bottom\", _vm._n($$v))},expression:\"tylkoCam.controls.screenEdgeOffset.bottom\"}})],1)])])])]):_vm._e(),(_vm.ready)?_c('t-card',{attrs:{\"name\":\"Camera controls\"}},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-main\"},[_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-row q-pa-sm q-pb-md\"},[_c('div',{staticClass:\"col-card-1\"}),_c('q-btn',{staticClass:\"col-card-3\",attrs:{\"label\":\"Left\",\"color\":\"blue\"},on:{\"click\":function($event){return _vm.tylkoCam.setView('left')}}}),_c('q-btn',{staticClass:\"col-card-1\",attrs:{\"label\":\"L\",\"color\":\"red\"},on:{\"click\":function($event){return _vm.tylkoCam.setView('left_straight')}}}),_c('div',{staticClass:\"col-card-2\"}),_c('q-btn',{staticClass:\"col-card-4\",attrs:{\"label\":\"Front\",\"color\":\"blue\"},on:{\"click\":function($event){return _vm.tylkoCam.setView('front')}}}),_c('div',{staticClass:\"col-card-2\"}),_c('q-btn',{staticClass:\"col-card-3\",attrs:{\"label\":\"Right\",\"color\":\"blue\"},on:{\"click\":function($event){return _vm.tylkoCam.setView('right')}}}),_c('q-btn',{staticClass:\"col-card-1\",attrs:{\"label\":\"R\",\"color\":\"red\"},on:{\"click\":function($event){return _vm.tylkoCam.setView('right_straight')}}}),_c('div',{staticClass:\"col-card-2\"}),_c('q-btn',{staticClass:\"col-card-3\",attrs:{\"label\":\"Top\",\"color\":\"blue\"},on:{\"click\":function($event){return _vm.tylkoCam.setView('top')}}}),_c('q-btn',{staticClass:\"col-card-1\",attrs:{\"label\":\"T\",\"color\":\"red\"},on:{\"click\":function($event){return _vm.tylkoCam.setView('top_straight')}}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-6\",attrs:{\"label\":'fov:'+_vm.tylkoCam.fov}}),_c('q-slider',{staticClass:\"col-card-17\",attrs:{\"min\":10,\"max\":180,\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.fov),callback:function ($$v) {_vm.$set(_vm.tylkoCam, \"fov\", $$v)},expression:\"tylkoCam.fov\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-6\",attrs:{\"label\":\"Range\"}}),_c('q-range',{staticClass:\"col-card-17\",attrs:{\"min\":1,\"max\":10000,\"label-always\":\"label-always\",\"drag-range\":\"drag-range\",\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.range),callback:function ($$v) {_vm.$set(_vm.tylkoCam, \"range\", $$v)},expression:\"tylkoCam.range\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Position X'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":-3000,\"max\":3000,\"label-always\":\"label-always\",\"label-value\":parseInt(_vm.tylkoCam.position.x),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.position.x),callback:function ($$v) {_vm.$set(_vm.tylkoCam.position, \"x\", $$v)},expression:\"tylkoCam.position.x\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Position Y'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":-3000,\"max\":3000,\"label-always\":\"label-always\",\"label-value\":parseInt(_vm.tylkoCam.position.y),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.position.y),callback:function ($$v) {_vm.$set(_vm.tylkoCam.position, \"y\", $$v)},expression:\"tylkoCam.position.y\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Position Z'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":-3000,\"max\":3000,\"label-always\":\"label-always\",\"label-value\":parseInt(_vm.tylkoCam.position.z),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.position.z),callback:function ($$v) {_vm.$set(_vm.tylkoCam.position, \"z\", $$v)},expression:\"tylkoCam.position.z\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Target X'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":-300,\"max\":300,\"label-always\":\"label-always\",\"label-value\":parseInt(_vm.tylkoCam.target.x),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.target.x),callback:function ($$v) {_vm.$set(_vm.tylkoCam.target, \"x\", $$v)},expression:\"tylkoCam.target.x\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Target Y'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":-300,\"max\":300,\"label-always\":\"label-always\",\"label-value\":parseInt(_vm.tylkoCam.target.y),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.target.y),callback:function ($$v) {_vm.$set(_vm.tylkoCam.target, \"y\", $$v)},expression:\"tylkoCam.target.y\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Target Z'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":-300,\"max\":300,\"label-always\":\"label-always\",\"label-value\":parseInt(_vm.tylkoCam.target.z),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.updateCamera()}},model:{value:(_vm.tylkoCam.target.z),callback:function ($$v) {_vm.$set(_vm.tylkoCam.target, \"z\", $$v)},expression:\"tylkoCam.target.z\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Rotate Theta'}}),(_vm.tylkoCam.controls.new_theta !== null)?_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"type\":\"number\",\"min\":-Math.PI,\"max\":Math.PI,\"step\":0.01,\"label-always\":\"label-always\",\"label-value\":+(_vm.tylkoCam.controls.new_theta).toFixed(3),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.controls.update()}},model:{value:(_vm.tylkoCam.controls.new_theta),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"new_theta\", _vm._n($$v))},expression:\"tylkoCam.controls.new_theta\"}}):_vm._e()],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'Rotate Phi'}}),(_vm.tylkoCam.controls.new_phi !== null)?_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"type\":\"number\",\"min\":0,\"max\":Math.PI,\"step\":0.01,\"label-always\":\"label-always\",\"label-value\":+(_vm.tylkoCam.controls.new_phi).toFixed(3),\"dark\":\"dark\"},on:{\"input\":function($event){return _vm.tylkoCam.controls.update()}},model:{value:(_vm.tylkoCam.controls.new_phi),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"new_phi\", _vm._n($$v))},expression:\"tylkoCam.controls.new_phi\"}}):_vm._e()],1),_c('div',{staticClass:\"card-row\"},[_c('div',{staticClass:\"col-card-4\"}),_c('q-btn',{staticClass:\"col-card-8\",attrs:{\"label\":\"get matrix\",\"color\":\"green\"},on:{\"click\":_vm.getMatrix}}),_c('div',{staticClass:\"col-card-2\"}),_c('q-btn',{staticClass:\"col-card-8\",attrs:{\"label\":\"set matrix\",\"color\":\"red\"},on:{\"click\":_vm.setMatrix}})],1)])])])]):_vm._e(),(_vm.ready)?_c('t-card',{attrs:{\"name\":\"Orbit Controls\"}},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-main\"},[_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-row q-pa-sm q-pb-md\"},[_c('q-toggle',{staticClass:\"col-card-7\",attrs:{\"label\":\"Enable\",\"color\":\"green\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.enabled),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"enabled\", $$v)},expression:\"tylkoCam.controls.enabled\"}}),_c('q-toggle',{staticClass:\"col-card-9\",attrs:{\"label\":\"NoRotate\",\"color\":\"red\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.noRotate),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"noRotate\", $$v)},expression:\"tylkoCam.controls.noRotate\"}}),_c('q-toggle',{attrs:{\"label\":\"NoPan\",\"color\":\"red\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.noPan),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"noPan\", $$v)},expression:\"tylkoCam.controls.noPan\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":\"Polar range\"}}),_c('q-range',{staticClass:\"col-card-13\",attrs:{\"min\":0,\"max\":Math.PI,\"step\":0.01,\"left-label-value\":_vm.tylkoCam.controls.polarAngle ? +(_vm.tylkoCam.controls.polarAngle.min).toFixed(3) : 0,\"right-label-value\":_vm.tylkoCam.controls.polarAngle ? +(_vm.tylkoCam.controls.polarAngle.max).toFixed(3) : 0,\"label-always\":\"label-always\",\"drag-range\":\"drag-range\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.polarAngle),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"polarAngle\", $$v)},expression:\"tylkoCam.controls.polarAngle\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":\"Azimuth range\"}}),_c('q-range',{staticClass:\"col-card-13\",attrs:{\"min\":-Math.PI,\"max\":Math.PI,\"step\":0.01,\"left-label-value\":_vm.tylkoCam.controls.azimuthAngle ? +(_vm.tylkoCam.controls.azimuthAngle.min).toFixed(3) : 0,\"right-label-value\":_vm.tylkoCam.controls.azimuthAngle ? +(_vm.tylkoCam.controls.azimuthAngle.max).toFixed(3) : 0,\"label-always\":\"label-always\",\"drag-range\":\"drag-range\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.azimuthAngle),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"azimuthAngle\", $$v)},expression:\"tylkoCam.controls.azimuthAngle\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'ZoomSpeed'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":0.1,\"max\":5,\"step\":0.1,\"label-always\":\"label-always\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.zoomSpeed),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"zoomSpeed\", $$v)},expression:\"tylkoCam.controls.zoomSpeed\"}})],1),_c('div',{staticClass:\"card-row\"},[_c('q-field',{staticClass:\"col-card-10\",attrs:{\"label\":'RotateSpeed'}}),_c('q-slider',{staticClass:\"col-card-13\",attrs:{\"min\":0.1,\"max\":5,\"step\":0.1,\"label-always\":\"label-always\",\"dark\":\"dark\"},model:{value:(_vm.tylkoCam.controls.rotateSpeed),callback:function ($$v) {_vm.$set(_vm.tylkoCam.controls, \"rotateSpeed\", $$v)},expression:\"tylkoCam.controls.rotateSpeed\"}})],1)])])])]):_vm._e()],1)],1)])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n    section(class=\"main\" style=\"height: 100%\")\n        div(style=\"z-index: 1000;display:block;height: 100vh; width: calc(100vw - 500px);position:fixed;top:0px;left:60px;\")\n            div(style=\"width:40%;height: 100vh;display:inline-block;\", ref=\"view1\")\n            div(style=\"width:60%;height: 100vh;display:inline-block;\", ref=\"view2\")\n        div.flex.row(style=\"height: 100%\")\n            div.col(style=\"background: red; width: calc(100vw - 500px);\")\n                canvas(class=\"col\" ref=\"c\" style=\"height: calc(100vh); width: calc(100vw - 500px); position: fixed;\")\n            div.col(style=\"max-width: 440px; background: white; height: 100%\")\n                q-scroll-area(style=\"height:100vh;\")\n                    t-card(name=\"Geometry size\")\n                        .card\n                            .card-main\n                                .card-content\n                                    .card-row\n                                        q-field(:label=\"'X:'+geomX\").col-card-6\n                                        q-slider(\n                                            :value=\"geomXtemp\",\n                                            :min=\"40\",\n                                            :max=\"500\",\n                                            @change=\"val => { geomXtemp=val; tylkoCam.controls.geometryFixed = true;  tylkoCam.controls.update(); }\",\n                                            @input=\"val => { tylkoCam.controls.geometryFixed = false; updateGeometry(val)}\",\n                                            dark).col-card-17\n                                    .card-row\n                                        q-field(:label=\"'Y:'+geomY\").col-card-6\n                                        q-slider(\n                                            :value=\"geomYtemp\",\n                                            :min=\"20\",\n                                            :max=\"300\",\n                                            @change=\"val => { geomYtemp=val; tylkoCam.controls.geometryFixed = true; tylkoCam.controls.update(); }\",\n                                            @input=\"val => { tylkoCam.controls.geometryFixed = false; updateGeometry(undefined, val)}\",\n                                            dark).col-card-17\n                                    .card-row\n                                        .col-card-1\n                                        q-btn(\n                                            label=\"Shelf\",\n                                            color=\"blue\",\n                                            @click=\"switchToShelfView\").col-card-6\n                                        .col-card-1\n                                        q-btn(\n                                            label=\"Component\",\n                                            color=\"blue\",\n                                            @click=\"switchToComponentView\").col-card-6\n                                        .col-card-1\n                                        q-btn(\n                                            label=\"Madness\",\n                                            color=\"red\",\n                                            @click=\"tylkoCam.setDefaultfView()\").col-card-6\n                    t-card(name=\"Camera Automatic Zoom Calculations\" v-if=\"ready\")\n                        .card\n                            .card-main\n                                .card-content\n                                    .card-row(class=\"q-pa-sm q-pb-md\")\n                                        q-toggle(\n                                            label=\"Disable AutoZoom\",\n                                            v-model=\"tylkoCam.controls.noAutoZoom\",\n                                            color=\"yellow\",\n                                            dark)\n                                        q-toggle(\n                                            label=\"Disable LifeZoom\",\n                                            v-model=\"tylkoCam.controls.noLifeZoom\",\n                                            color=\"yellow\",\n                                            dark)\n                                    .card-row(class=\"q-pa-sm q-pb-md\")\n                                        q-toggle(\n                                            label=\"Disable Animation\",\n                                            v-model=\"tylkoCam.controls.noTransitionAnimation\",\n                                            color=\"yellow\",\n                                            dark)\n                                        q-toggle(\n                                            label=\"Disable Snapping\",\n                                            v-model=\"tylkoCam.controls.noSnap\",\n                                            color=\"yellow\",\n                                            dark)\n                                    //.card-row\n                                        q-input(\n                                            label=\"Near/Far Offset\",\n                                            v-model.number=\"tylkoCam.controls.autoZoomObj.cameraPlanesOffset\",\n                                            type=\"number\",\n                                            dark).col-card-24\n                                    .card-row\n                                        q-field(label=\"Geometry to ViewEdge Offsets in object cm:\").col-card-24\n                                    .card-row\n                                        q-input(\n                                            label=\"Left\",\n                                            v-model.number=\"tylkoCam.geometryOffset.left\",\n                                            @input=\"updateGeometry\",\n                                            type=\"number\",\n                                            dark).col-card-4\n                                        q-input(\n                                            label=\"Right\",\n                                            v-model.number=\"tylkoCam.geometryOffset.right\",\n                                            @input=\"updateGeometry\",\n                                            type=\"number\",\n                                            dark).col-card-4\n                                        q-input(\n                                            label=\"Top\",\n                                            v-model.number=\"tylkoCam.geometryOffset.top\",\n                                            @input=\"updateGeometry\",\n                                            type=\"number\",\n                                            dark).col-card-4\n                                        q-input(\n                                            label=\"Bottom\",\n                                            v-model.number=\"tylkoCam.geometryOffset.bottom\",\n                                            @input=\"updateGeometry\",\n                                            type=\"number\",\n                                            dark).col-card-4\n                                        q-input(\n                                            label=\"Front\",\n                                            v-model.number=\"tylkoCam.geometryOffset.front\",\n                                            @input=\"updateGeometry\",\n                                            type=\"number\",\n                                            dark).col-card-4\n                                        q-input(\n                                            label=\"Back\",\n                                            v-model.number=\"tylkoCam.geometryOffset.back\",\n                                            @input=\"updateGeometry\",\n                                            type=\"number\",\n                                            dark).col-card-4    \n                                    .card-row\n                                        q-field(label=\"Geometry to ViewEdge Offsets in screen pixels:\").col-card-24\n                                    .card-row\n                                        q-input(\n                                            label=\"Left\",\n                                            v-model.number=\"tylkoCam.controls.screenEdgeOffset.left\",\n                                            @input=\"tylkoCam.controls.update()\",\n                                            type=\"number\",\n                                            dark).col-card-6\n                                        q-input(\n                                            label=\"Right\",\n                                            v-model.number=\"tylkoCam.controls.screenEdgeOffset.right\",\n                                            @input=\"tylkoCam.controls.update()\",\n                                            type=\"number\",\n                                            dark).col-card-6\n                                        q-input(\n                                            label=\"Top\",\n                                            v-model.number=\"tylkoCam.controls.screenEdgeOffset.top\",\n                                            @input=\"tylkoCam.controls.update()\",\n                                            type=\"number\",\n                                            dark).col-card-6\n                                        q-input(\n                                            label=\"Bottom\",\n                                            v-model.number=\"tylkoCam.controls.screenEdgeOffset.bottom\",\n                                            @input=\"tylkoCam.controls.update()\",\n                                            type=\"number\",\n                                            dark).col-card-6\n\n                    t-card(name=\"Camera controls\" v-if=\"ready\")\n                        .card\n                            .card-main\n                                .card-content\n                                    .card-row(class=\"q-pa-sm q-pb-md\")\n                                        .col-card-1\n                                        q-btn(\n                                            label=\"Left\",\n                                            color=\"blue\",\n                                            @click=\"tylkoCam.setView('left')\").col-card-3\n                                        q-btn(\n                                            label=\"L\",\n                                            color=\"red\",\n                                            @click=\"tylkoCam.setView('left_straight')\").col-card-1    \n                                        .col-card-2\n                                        q-btn(\n                                            label=\"Front\",\n                                            color=\"blue\",\n                                            @click=\"tylkoCam.setView('front')\").col-card-4\n                                        .col-card-2\n                                        q-btn(\n                                            label=\"Right\",\n                                            color=\"blue\",\n                                            @click=\"tylkoCam.setView('right')\").col-card-3\n                                        q-btn(\n                                            label=\"R\",\n                                            color=\"red\",\n                                            @click=\"tylkoCam.setView('right_straight')\").col-card-1    \n                                        .col-card-2\n                                        q-btn(\n                                            label=\"Top\",\n                                            color=\"blue\",\n                                            @click=\"tylkoCam.setView('top')\").col-card-3\n                                        q-btn(\n                                            label=\"T\",\n                                            color=\"red\",\n                                            @click=\"tylkoCam.setView('top_straight')\").col-card-1\n                                    .card-row\n                                        q-field(:label=\"'fov:'+tylkoCam.fov\").col-card-6\n                                        q-slider(\n                                            v-model=\"tylkoCam.fov\",\n                                            :min=\"10\",\n                                            :max=\"180\",\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-17\n                                    .card-row\n                                        q-field(label=\"Range\").col-card-6\n                                        q-range(\n                                            v-model=\"tylkoCam.range\",\n                                            :min=\"1\",\n                                            :max=\"10000\",\n                                            label-always,\n                                            drag-range,\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-17\n                                    .card-row\n                                        q-field(:label=\"'Position X'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.position.x\",\n                                            :min=\"-3000\",\n                                            :max=\"3000\",\n                                            label-always,\n                                            :label-value=\"parseInt(tylkoCam.position.x)\",\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'Position Y'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.position.y\",\n                                            :min=\"-3000\",\n                                            :max=\"3000\",\n                                            label-always,\n                                            :label-value=\"parseInt(tylkoCam.position.y)\",\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'Position Z'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.position.z\",\n                                            :min=\"-3000\",\n                                            :max=\"3000\",\n                                            label-always,\n                                            :label-value=\"parseInt(tylkoCam.position.z)\",\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'Target X'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.target.x\",\n                                            :min=\"-300\",\n                                            :max=\"300\",\n                                            label-always,\n                                            :label-value=\"parseInt(tylkoCam.target.x)\",\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'Target Y'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.target.y\",\n                                            :min=\"-300\",\n                                            :max=\"300\",\n                                            label-always,\n                                            :label-value=\"parseInt(tylkoCam.target.y)\",\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'Target Z'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.target.z\",\n                                            :min=\"-300\",\n                                            :max=\"300\",\n                                            label-always,\n                                            :label-value=\"parseInt(tylkoCam.target.z)\",\n                                            @input=\"tylkoCam.updateCamera()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'Rotate Theta'\").col-card-10\n                                        q-slider(\n                                            v-if=\"tylkoCam.controls.new_theta !== null\",\n                                            v-model.number=\"tylkoCam.controls.new_theta\",\n                                            type=\"number\",\n                                            :min=\"-Math.PI\",\n                                            :max=\"Math.PI\",\n                                            :step=\"0.01\",\n                                            label-always,\n                                            :label-value=\"+(tylkoCam.controls.new_theta).toFixed(3)\",\n                                            @input=\"tylkoCam.controls.update()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'Rotate Phi'\").col-card-10\n                                        q-slider(\n                                            v-if=\"tylkoCam.controls.new_phi !== null\",\n                                            v-model.number=\"tylkoCam.controls.new_phi\",\n                                            type=\"number\",\n                                            :min=\"0\",\n                                            :max=\"Math.PI\",\n                                            :step=\"0.01\",\n                                            label-always,\n                                            :label-value=\"+(tylkoCam.controls.new_phi).toFixed(3)\",\n                                            @input=\"tylkoCam.controls.update()\",\n                                            dark).col-card-13\n                                    .card-row\n                                        .col-card-4\n                                        q-btn(\n                                            label=\"get matrix\",\n                                            color=\"green\",\n                                            @click=\"getMatrix\").col-card-8\n                                        .col-card-2\n                                        q-btn(\n                                            label=\"set matrix\",\n                                            color=\"red\",\n                                            @click=\"setMatrix\").col-card-8            \n                    t-card(name=\"Orbit Controls\" v-if=\"ready\")\n                        .card\n                            .card-main\n                                .card-content\n                                    .card-row(class=\"q-pa-sm q-pb-md\")\n                                        q-toggle(\n                                            label=\"Enable\",\n                                            v-model=\"tylkoCam.controls.enabled\",\n                                            color=\"green\",\n                                            dark).col-card-7\n                                        q-toggle(\n                                            label=\"NoRotate\",\n                                            v-model=\"tylkoCam.controls.noRotate\",\n                                            color=\"red\",\n                                            dark).col-card-9\n                                        q-toggle(\n                                            label=\"NoPan\",\n                                            v-model=\"tylkoCam.controls.noPan\",\n                                            color=\"red\",\n                                            dark)\n                                    .card-row\n                                        q-field(label=\"Polar range\").col-card-10\n                                        q-range(\n                                            v-model=\"tylkoCam.controls.polarAngle\",\n                                            :min=\"0\",\n                                            :max=\"Math.PI\",\n                                            :step=\"0.01\",\n                                            :left-label-value=\"tylkoCam.controls.polarAngle ? +(tylkoCam.controls.polarAngle.min).toFixed(3) : 0\",\n                                            :right-label-value=\"tylkoCam.controls.polarAngle ? +(tylkoCam.controls.polarAngle.max).toFixed(3) : 0\",\n                                            label-always,\n                                            drag-range,\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(label=\"Azimuth range\").col-card-10\n                                        q-range(\n                                            v-model=\"tylkoCam.controls.azimuthAngle\",\n                                            :min=\"-Math.PI\",\n                                            :max=\"Math.PI\",\n                                            :step=\"0.01\",\n                                            :left-label-value=\"tylkoCam.controls.azimuthAngle ? +(tylkoCam.controls.azimuthAngle.min).toFixed(3) : 0\",\n                                            :right-label-value=\"tylkoCam.controls.azimuthAngle ? +(tylkoCam.controls.azimuthAngle.max).toFixed(3) : 0\",\n                                            label-always,\n                                            drag-range,\n                                            dark).col-card-13        \n                                    .card-row\n                                        q-field(:label=\"'ZoomSpeed'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.controls.zoomSpeed\",\n                                            :min=\"0.1\",\n                                            :max=\"5\",\n                                            :step=\"0.1\",\n                                            label-always,\n                                            dark).col-card-13\n                                    .card-row\n                                        q-field(:label=\"'RotateSpeed'\").col-card-10\n                                        q-slider(\n                                            v-model=\"tylkoCam.controls.rotateSpeed\",\n                                            :min=\"0.1\",\n                                            :max=\"5\",\n                                            :step=\"0.1\",\n                                            label-always,\n                                            dark).col-card-13\n\n</template>\n\n<style lang=\"scss\">\n@import '~@theme/card.scss';\n\n.ac {\n    width: 420px;\n    height: calc(100vh - 235px);\n    background: #131313;\n}\n</style>\n\n<script>\nimport * as THREE from 'three'\nimport './tylko-camera-orbit-perspective'\n\nimport { cape } from '@core/cape'\nimport { tylkoCamera } from './tylko-camera.js'\nimport { tylkoCapeComponents } from '@cape-ui'\n\nexport default {\n    components: {\n        ...tylkoCapeComponents,\n    },\n\n    data() {\n        return {\n            ready: false,\n\n            geomX: 240,\n            geomXtemp: 240,\n            geomY: 120,\n            geomYtemp: 120,\n            geomZ: 32,\n            geom: null,\n\n            view1: null,\n            view2: null,\n            canvas: null,\n\n            temp1: 0,\n            temp2: 0,\n\n            renderer: null,\n            scene: null,\n            tylkoCam: null,\n            camera2: null,\n            controls2: null,\n        }\n    },\n\n    mounted() {\n        cape.application.viewport.fixed(true)\n        this.setUp()\n        this.createGeometry()\n        this.render()\n        // requestAnimationFrame(this.render)\n        this.ready = true\n\n        // LEFT VIEW\n\n        let shouldRender = false;\n\n        this.renderLoop = () => {\n            if(shouldRender) {\n                shouldRender = false;\n                this.render();\n                this.tylkoCam.controls.update()\n            }\n            window.requestAnimationFrame(this.renderLoop)\n        };\n\n        this.renderLoop();\n\n        this.tylkoCam.controls.addEventListener('render', () => {\n            shouldRender = true;\n        });\n\n        this.tylkoCam.controls.addEventListener('change', () => {\n            this.render()\n        });\n\n        // RIGHT VIEW\n        this.controls2.addEventListener('change', () => {\n            this.render()\n        });\n\n    },\n\n    methods: {\n        setUp() {\n            this.canvas = this.$refs.c\n            this.view1 = this.$refs.view1\n            this.view2 = this.$refs.view2\n            this.scene = new THREE.Scene()\n            this.scene.background = new THREE.Color('black')\n            this.renderer = new THREE.WebGLRenderer({ canvas: this.canvas })\n\n            this.tylkoCam = new tylkoCamera(this.view1, this.scene)\n            this.cameraHelper = new THREE.CameraHelper(this.tylkoCam.camera)\n            this.scene.add(this.cameraHelper)\n            this.scene.add(this.tylkoCam.camera)\n\n            this.camera2 = new THREE.PerspectiveCamera(60, 2, 0.1, 50000)\n            this.camera2.position.set(800, 500, 700)\n            this.camera2.lookAt(0, 5, 0)\n\n            this.controls2 = new THREE.OrbitControls(this.camera2, this.view2)\n            this.controls2.target.set(0, 5, 0)\n            this.controls2.update()\n        },\n\n        createGeometry() {\n            const cubeGeo = new THREE.BoxBufferGeometry(1, 1, 1)\n            const cubeMat = new THREE.MeshPhongMaterial({ color: '#8AC', opacity: 0.5, transparent: false })\n            this.geom = new THREE.Mesh(cubeGeo, cubeMat)\n            this.geom.scale.set(this.geomX, this.geomY, this.geomZ)\n            this.geom.position.set(0, this.geomY / 2, this.geomZ / 2)\n            this.scene.add(this.geom)\n            // Grid\n            let size = 280,\n                steps = 20\n            let geometry = new THREE.Geometry()\n            let material = new THREE.LineBasicMaterial({\n                color: 'gray',\n            })\n            for (var i = -size; i <= size; i += steps) {\n                if (i >= 0) {\n                    geometry.vertices.push(new THREE.Vector3(-size, -0.04, i))\n                    geometry.vertices.push(new THREE.Vector3(size, -0.04, i))\n                }\n                if (size - i <= 320) {\n                    geometry.vertices.push(\n                        new THREE.Vector3(-size, size - i - 0.04, 0)\n                    )\n                    geometry.vertices.push(\n                        new THREE.Vector3(size, size - i - 0.04, 0)\n                    )\n                }\n\n                geometry.vertices.push(new THREE.Vector3(i, -0.04, 0))\n                geometry.vertices.push(new THREE.Vector3(i, -0.04, size))\n                geometry.vertices.push(new THREE.Vector3(i, -0.04, 0))\n                geometry.vertices.push(new THREE.Vector3(i, 320 - 0.04, 0))\n            }\n            var line = new THREE.Line(geometry, material, THREE.LinePieces)\n            this.scene.add(line)\n            this.tylkoCam.updateGeometry(\n                {x: -this.geomX/2, y: 0, z: 0},\n                {x: this.geomX/2, y: this.geomY, z: this.geomZ}\n            )\n            // this.tylkoCam.controls.update()\n        },\n\n        resizeRendererToDisplaySize(renderer) {\n            const canvas = renderer.domElement\n            const width = canvas.clientWidth\n            const height = canvas.clientHeight\n            const needResize =\n                canvas.width !== width || canvas.height !== height\n            if (needResize) {\n                renderer.setSize(width, height, false)\n            }\n            return needResize\n        },\n\n        setScissorForElement(elem) {\n            const canvasRect = this.canvas.getBoundingClientRect()\n            const elemRect = elem.getBoundingClientRect()\n\n            const right =\n                Math.min(elemRect.right, canvasRect.right) - canvasRect.left\n            const left = Math.max(0, elemRect.left - canvasRect.left)\n            const bottom =\n                Math.min(elemRect.bottom, canvasRect.bottom) - canvasRect.top\n            const top = Math.max(0, elemRect.top - canvasRect.top)\n\n            const width = Math.min(canvasRect.width, right - left)\n            const height = Math.min(canvasRect.height, bottom - top)\n\n            const positiveYUpBottom = canvasRect.height - bottom\n            this.renderer.setScissor(left, positiveYUpBottom, width, height)\n            this.renderer.setViewport(left, positiveYUpBottom, width, height)\n\n            return width / height\n        },\n\n        render () {\n            console.log(\">>>>> RENDER\")\n            this.resizeRendererToDisplaySize(this.renderer)\n            this.renderer.setScissorTest(true)\n\n            // render from the 1nd camera\n            let aspect = this.setScissorForElement(this.view1)\n            this.tylkoCam.update(aspect)\n            this.cameraHelper.update()\n            this.cameraHelper.visible = false\n            this.scene.background.set(0x000000)\n            this.renderer.render(this.scene, this.tylkoCam.camera)\n\n            // render from the 2nd camera\n            aspect = this.setScissorForElement(this.view2)\n            this.camera2.aspect = aspect\n            this.camera2.updateProjectionMatrix()\n            this.cameraHelper.visible = true\n            this.scene.background.set(0x000040)\n            this.renderer.render(this.scene, this.camera2)\n        },\n\n        updateGeometry(geomX, geomY) {\n            if (geomX !== undefined) this.geomX = geomX\n            if (geomY !== undefined) this.geomY = geomY\n            this.geom.scale.set(this.geomX, this.geomY, this.geomZ)\n            this.geom.position.y = this.geomY / 2\n            this.tylkoCam.updateGeometry(\n                {x: -this.geomX/2, y: 0, z: 0},\n                {x: this.geomX/2, y: this.geomY, z: this.geomZ}\n            )\n            this.render()\n        },\n\n        switchToShelfView() {\n            console.log(\">>>> Shelf\")\n            this.tylkoCam.setShelfView(\n                {x: -this.geomX/2, y: 0, z: 0},\n                {x: this.geomX/2, y: this.geomY, z: this.geomZ}\n            )\n        },\n\n        switchToComponentView(geomX, geomY) {\n            console.log(\">>>> Component\")\n            this.tylkoCam.setComponentView(\n                {x: -20, y: 0, z: 0},\n                {x: 40, y: this.geomY, z: this.geomZ}\n            )\n        },\n\n        getMatrix() {\n            this.$q.dialog({\n                title: 'Matrix',\n                message: JSON.stringify(this.tylkoCam.getCamSettings()),\n                ok: true,\n            })\n        },\n        setMatrix() {\n            let matrix = prompt('Enter matrix data', '')\n            if (matrix) this.tylkoCam.setCamSettings(JSON.parse(matrix))\n        },\n    },\n}\n</script>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./camera-helper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./camera-helper.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./camera-helper.vue?vue&type=template&id=6fa769ed&lang=pug&\"\nimport script from \"./camera-helper.vue?vue&type=script&lang=js&\"\nexport * from \"./camera-helper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./camera-helper.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./camera-helper.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./camera-helper.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}