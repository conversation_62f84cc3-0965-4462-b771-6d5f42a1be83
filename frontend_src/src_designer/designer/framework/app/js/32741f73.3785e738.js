(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["32741f73","5266d12a"],{"044b":function(e,t){
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return e!=null&&(r(e)||n(e)||!!e._isBuffer)};function r(e){return!!e.constructor&&typeof e.constructor.isBuffer==="function"&&e.constructor.isBuffer(e)}function n(e){return typeof e.readFloatLE==="function"&&typeof e.slice==="function"&&r(e.slice(0,0))}},"04a2":function(e,t,r){(function(t){var n=r("7a87");var a=new t([66,77,70,3]);e.exports=function(e){if(typeof e==="string")return e.substring(0,3)==="BMF";return e.length>4&&n(e.slice(0,4),a)}}).call(this,r("b639").Buffer)},"07bd":function(e,t,r){},"0f01":function(e,t,r){"use strict";var n=r("e9ac");var a=n("%Object%");var i=n("%TypeError%");var o=n("%String%");var s=r("c46d");var l=r("2057");var c=r("c612");var u=r("5975");var d=r("bb53");var f=r("21d0");var p=r("2f17");var h=r("a0d3");var v={ToPrimitive:p,ToBoolean:function e(t){return!!t},ToNumber:function e(t){return+t},ToInteger:function e(t){var r=this.ToNumber(t);if(l(r)){return 0}if(r===0||!c(r)){return r}return u(r)*Math.floor(Math.abs(r))},ToInt32:function e(t){return this.ToNumber(t)>>0},ToUint32:function e(t){return this.ToNumber(t)>>>0},ToUint16:function e(t){var r=this.ToNumber(t);if(l(r)||r===0||!c(r)){return 0}var n=u(r)*Math.floor(Math.abs(r));return d(n,65536)},ToString:function e(t){return o(t)},ToObject:function e(t){this.CheckObjectCoercible(t);return a(t)},CheckObjectCoercible:function e(t,r){if(t==null){throw new i(r||"Cannot call method on "+t)}return t},IsCallable:f,SameValue:function e(t,r){if(t===r){if(t===0){return 1/t===1/r}return true}return l(t)&&l(r)},Type:function e(t){if(t===null){return"Null"}if(typeof t==="undefined"){return"Undefined"}if(typeof t==="function"||typeof t==="object"){return"Object"}if(typeof t==="number"){return"Number"}if(typeof t==="boolean"){return"Boolean"}if(typeof t==="string"){return"String"}},IsPropertyDescriptor:function e(t){if(this.Type(t)!=="Object"){return false}var r={"[[Configurable]]":true,"[[Enumerable]]":true,"[[Get]]":true,"[[Set]]":true,"[[Value]]":true,"[[Writable]]":true};for(var n in t){if(h(t,n)&&!r[n]){return false}}var a=h(t,"[[Value]]");var o=h(t,"[[Get]]")||h(t,"[[Set]]");if(a&&o){throw new i("Property Descriptors may not be both accessor and data descriptors")}return true},IsAccessorDescriptor:function e(t){if(typeof t==="undefined"){return false}s(this,"Property Descriptor","Desc",t);if(!h(t,"[[Get]]")&&!h(t,"[[Set]]")){return false}return true},IsDataDescriptor:function e(t){if(typeof t==="undefined"){return false}s(this,"Property Descriptor","Desc",t);if(!h(t,"[[Value]]")&&!h(t,"[[Writable]]")){return false}return true},IsGenericDescriptor:function e(t){if(typeof t==="undefined"){return false}s(this,"Property Descriptor","Desc",t);if(!this.IsAccessorDescriptor(t)&&!this.IsDataDescriptor(t)){return true}return false},FromPropertyDescriptor:function e(t){if(typeof t==="undefined"){return t}s(this,"Property Descriptor","Desc",t);if(this.IsDataDescriptor(t)){return{value:t["[[Value]]"],writable:!!t["[[Writable]]"],enumerable:!!t["[[Enumerable]]"],configurable:!!t["[[Configurable]]"]}}else if(this.IsAccessorDescriptor(t)){return{get:t["[[Get]]"],set:t["[[Set]]"],enumerable:!!t["[[Enumerable]]"],configurable:!!t["[[Configurable]]"]}}else{throw new i("FromPropertyDescriptor must be called with a fully populated Property Descriptor")}},ToPropertyDescriptor:function e(t){if(this.Type(t)!=="Object"){throw new i("ToPropertyDescriptor requires an object")}var r={};if(h(t,"enumerable")){r["[[Enumerable]]"]=this.ToBoolean(t.enumerable)}if(h(t,"configurable")){r["[[Configurable]]"]=this.ToBoolean(t.configurable)}if(h(t,"value")){r["[[Value]]"]=t.value}if(h(t,"writable")){r["[[Writable]]"]=this.ToBoolean(t.writable)}if(h(t,"get")){var n=t.get;if(typeof n!=="undefined"&&!this.IsCallable(n)){throw new TypeError("getter must be a function")}r["[[Get]]"]=n}if(h(t,"set")){var a=t.set;if(typeof a!=="undefined"&&!this.IsCallable(a)){throw new i("setter must be a function")}r["[[Set]]"]=a}if((h(r,"[[Get]]")||h(r,"[[Set]]"))&&(h(r,"[[Value]]")||h(r,"[[Writable]]"))){throw new i("Invalid property descriptor. Cannot both specify accessors and a value or writable attribute")}return r}};e.exports=v},"0f7c":function(e,t,r){"use strict";var n=r("688e");e.exports=Function.prototype.bind||n},"189f":function(e,t,r){var n=r("f861");var a=r("1f03");var i=r("4509");var o=i("typed_array");var s=i("view");var l=!!(n.ArrayBuffer&&n.DataView);var c=l;var u=0;var d=9;var f;var p="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");while(u<d){if(f=n[p[u++]]){a(f.prototype,o,true);a(f.prototype,s,true)}else c=false}e.exports={ABV:l,CONSTR:c,TYPED:o,VIEW:s}},"1a91":function(e,t,r){var n=r("22fe");n(n.S,"Object",{setPrototypeOf:r("c246").set})},"1b7f":function(e,t,r){"use strict";var n=r("562e");var a="​";e.exports=function e(){if(String.prototype.trim&&a.trim()===a){return String.prototype.trim}return n}},"1bf1":function(e,t,r){var n=r("320c");e.exports=function e(t){t=t||{};var r=typeof t.opacity==="number"?t.opacity:1;var a=typeof t.alphaTest==="number"?t.alphaTest:1e-4;var i=t.precision||"highp";var o=t.color;var s=t.map;var l=typeof t.negate==="boolean"?t.negate:true;delete t.map;delete t.color;delete t.precision;delete t.opacity;delete t.negate;return n({uniforms:{opacity:{type:"f",value:r},map:{type:"t",value:s||new THREE.Texture},color:{type:"c",value:new THREE.Color(o)}},vertexShader:["attribute vec2 uv;","attribute vec4 position;","uniform mat4 projectionMatrix;","uniform mat4 modelViewMatrix;","varying vec2 vUv;","void main() {","vUv = uv;","gl_Position = projectionMatrix * modelViewMatrix * position;","}"].join("\n"),fragmentShader:["#ifdef GL_OES_standard_derivatives","#extension GL_OES_standard_derivatives : enable","#endif","precision "+i+" float;","uniform float opacity;","uniform vec3 color;","uniform sampler2D map;","varying vec2 vUv;","float median(float r, float g, float b) {","  return max(min(r, g), min(max(r, g), b));","}","void main() {","  vec3 sample = "+(l?"1.0 - ":"")+"texture2D(map, vUv).rgb;","  float sigDist = median(sample.r, sample.g, sample.b) - 0.5;","  float alpha = clamp(sigDist/fwidth(sigDist) + 0.5, 0.0, 1.0);","  gl_FragColor = vec4(color.xyz, alpha * opacity);",a===0?"":"  if (gl_FragColor.a < "+a+") discard;","}"].join("\n")},t)}},"1c33":function(e,t){const r=[[-1,-1],[+1,-1],[-1,+1],[-1,+1],[+1,-1],[+1,+1]];const n=[[0,0],[1,0],[0,1],[0,1],[1,0],[1,1]];const a=[[0,1,2],[3,4,5]];const i=`\n  precision mediump float;\n  attribute vec2 a_position;\n  attribute vec2 a_uv;\n\n  uniform float u_clip_y;\n\n  varying vec2 v_uv;\n  \n  void main() {\n    v_uv = a_uv;\n    gl_Position = vec4(a_position * vec2(1,u_clip_y), 0, 1);\n  }\n`;const o=`\n  precision mediump float;\n  varying vec2 v_uv;\n  uniform sampler2D u_tex;\n  void main () {\n    gl_FragColor = texture2D(u_tex,v_uv);\n  }\n`;const s=`\n  precision mediump float;\n  varying vec2 v_uv;\n  void main () {\n    gl_FragColor = vec4(v_uv,0,1);\n  }\n`;const l=`\n  precision mediump float;\n  attribute vec2 a_position;\n\n  uniform float u_clip_y;\n\n  varying vec2 v_uv;\n  \n  void main() {\n    gl_Position = vec4(a_position * vec2(1,u_clip_y), 0, 1);\n    v_uv = gl_Position.xy;\n  }\n`;const c=`\n  precision mediump float;\n  varying vec2 v_uv;\n  void main () {\n    gl_FragColor = vec4(v_uv,0,1);\n  }\n`;const u=`\ndata:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAA\nBACAIAAAAlC+aJAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQ\nUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAEbSURBVGhD7dhRDsIgEI\nRhjubNPHqlHUTAdjfRWRKa+UIirQnd376Z0vZZG1vQsfvB76WAa3\nEn3yug3GHD0HX6gIZCAaYaEGdSQM2g9yjApADfpIBhTzQvIIgCTA\nrwKcCkAJ8CTArwKcCkAN/56Y/8XAZCwH7AsS6sEDBseisEYF1YIW\nDY9Lq7eW6Mjk29/Bk/YD+vO7Bc/D/rKULAqSbj80tHrOehPC9mjY\n/krhkBeBF4HvZE6CgXRJgeW3wAPYMf0IwO1NO/RL2BhgJMCvApwK\nQAnwJMCvApwKQAnwJMCvApwNQGYE/vmRowbCgUYLpbQHvJMi8gSN\nTpmLsGxGWsH9Aq90gwfW1gwv9zx+qUr0mWD8hCps/uE5DSC/pgVD\nkvIARVAAAAAElFTkSuQmCC`.replace(/\s*/g,"");const d={directions:{uri:u}};e.exports={verts:r,indices:a,uvs:n,shader:{vert:i,frag:o},show:{uvs:{frag:s,vert:i},positions:{frag:c,vert:l}},bitmaps:d}},2057:function(e,t){e.exports=Number.isNaN||function e(t){return t!==t}},"21d0":function(e,t,r){"use strict";var n=Function.prototype.toString;var a=/^\s*class\b/;var i=function e(t){try{var r=n.call(t);return a.test(r)}catch(i){return false}};var o=function e(t){try{if(i(t)){return false}n.call(t);return true}catch(r){return false}};var s=Object.prototype.toString;var l="[object Function]";var c="[object GeneratorFunction]";var u=typeof Symbol==="function"&&typeof Symbol.toStringTag==="symbol";e.exports=function e(t){if(!t){return false}if(typeof t!=="function"&&typeof t!=="object"){return false}if(typeof t==="function"&&!t.prototype){return true}if(u){return o(t)}if(i(t)){return false}var r=s.call(t);return r===l||r===c}},"247b":function(e,t,r){"use strict";r("80dc")("fontsize",function(e){return function t(r){return e(this,"font","size",r)}})},"2a34":function(e,t,r){"use strict";var n=r("07bd");var a=r.n(n);var i=a.a},"2ae9":function(e,t,r){"use strict";var n=r("a911");var a=r("d744");var i=r("d7d0");e.exports=[].copyWithin||function e(t,r){var o=n(this);var s=i(o.length);var l=a(t,s);var c=a(r,s);var u=arguments.length>2?arguments[2]:undefined;var d=Math.min((u===undefined?s:a(u,s))-c,s-l);var f=1;if(c<l&&l<c+d){f=-1;c+=d-1;l+=d-1}while(d-- >0){if(c in o)o[l]=o[c];else delete o[l];l+=f;c+=f}return o}},"2f17":function(e,t,r){"use strict";var n=Object.prototype.toString;var a=r("4de8");var i=r("21d0");var o={"[[DefaultValue]]":function(e){var t;if(arguments.length>1){t=arguments[1]}else{t=n.call(e)==="[object Date]"?String:Number}if(t===String||t===Number){var r=t===String?["toString","valueOf"]:["valueOf","toString"];var o,s;for(s=0;s<r.length;++s){if(i(e[r[s]])){o=e[r[s]]();if(a(o)){return o}}}throw new TypeError("No default value")}throw new TypeError("invalid [[DefaultValue]] hint supplied")}};e.exports=function e(t){if(a(t)){return t}if(arguments.length>1){return o["[[DefaultValue]]"](t,arguments[1])}return o["[[DefaultValue]]"](t)}},"362c":function(e,t,r){"use strict";var n=r("d7e8");var a=r.n(n);var i=a.a},"37cd":function(e,t,r){var n=r("7831");e.exports=a;function a(e,t,r){if(!e)throw new TypeError("must specify data as first parameter");r=+(r||0)|0;if(Array.isArray(e)&&(e[0]&&typeof e[0][0]==="number")){var a=e[0].length;var i=e.length*a;var o,s,l,c;if(!t||typeof t==="string"){t=new(n(t||"float32"))(i+r)}var u=t.length-r;if(i!==u){throw new Error("source length "+i+" ("+a+"x"+e.length+")"+" does not match destination length "+u)}for(o=0,l=r;o<e.length;o++){for(s=0;s<a;s++){t[l++]=e[o][s]===null?NaN:e[o][s]}}}else{if(!t||typeof t==="string"){var d=n(t||"float32");if(Array.isArray(e)||t==="array"){t=new d(e.length+r);for(o=0,l=r,c=t.length;l<c;l++,o++){t[l]=e[o]===null?NaN:e[o]}}else{if(r===0){t=new d(e)}else{t=new d(e.length+r);t.set(e,r)}}}else{t.set(e,r)}}return t}},3880:function(e,t,r){"use strict";var n=r("a8bd");var a=r.n(n);var i=a.a},"39dd":function(e,t,r){"use strict";var n=r("daae");var a=r.n(n);var i=a.a},"3c35":function(e,t){(function(t){e.exports=t}).call(this,{})},"3d04":function(e,t,r){"use strict";var n=r("6db0");var a=r.n(n);var i=r("448a");var o=r.n(i);var s=r("970b");var l=r.n(s);var c=r("5bc3");var u=r.n(c);var d=r("9ec3");var f=r.n(d);var p=function(){function e(t){l()(this,e);this.sources=f.a.merge.apply(f.a,o()(t));this.state=""}u()(e,[{key:"filter",value:function e(t){var r=this;new Promise(function(e){console.log(r.sources);e(r)});return this}},{key:"pickFew",value:function e(t){var r=this;new Promise(function(e){console.log(r.sources);e(r)});return this}},{key:"pickOne",value:function e(t){var r=this;new Promise(function(e){console.log(r.sources);e(r)});return this}},{key:"result",value:function e(){var t=this;return new Promise(function(e){console.log(t.sources);e(t)})}}]);return e}();var h=function(){function e(){l()(this,e)}u()(e,[{key:"construct",value:function e(){}},{key:"add",value:function e(t){return new p(t)}}]);return e}();var v=new h},"3e8f":function(e,t){},"408b":function(e,t){e.exports=r;function r(){this.x=0;this.y=0;this.scale=1}},"42b6":function(e,t){var r=/\n/;var n="\n";var a=/\s/;e.exports=function(t,r){var n=e.exports.lines(t,r);return n.map(function(e){return t.substring(e.start,e.end)}).join("\n")};e.exports.lines=function e(t,r){r=r||{};if(r.width===0&&r.mode!=="nowrap")return[];t=t||"";var n=typeof r.width==="number"?r.width:Number.MAX_VALUE;var a=Math.max(0,r.start||0);var i=typeof r.end==="number"?r.end:t.length;var o=r.mode;var u=r.measure||c;if(o==="pre")return s(u,t,a,i,n);else return l(u,t,a,i,n,o)};function i(e,t,r,n){var a=e.indexOf(t,r);if(a===-1||a>n)return n;return a}function o(e){return a.test(e)}function s(e,t,n,a,i){var o=[];var s=n;for(var l=n;l<a&&l<t.length;l++){var c=t.charAt(l);var u=r.test(c);if(u||l===a-1){var d=u?l:l+1;var f=e(t,s,d,i);o.push(f);s=l+1}}return o}function l(e,t,r,a,s,l){var c=[];var u=s;if(l==="nowrap")u=Number.MAX_VALUE;while(r<a&&r<t.length){var d=i(t,n,r,a);while(r<d){if(!o(t.charAt(r)))break;r++}var f=e(t,r,d,u);var p=r+(f.end-f.start);var h=p+n.length;if(p<d){while(p>r){if(o(t.charAt(p)))break;p--}if(p===r){if(h>r+n.length)h--;p=h}else{h=p;while(p>r){if(!o(t.charAt(p-n.length)))break;p--}}}if(p>=r){var v=e(t,r,p,u);c.push(v)}r=h}return c}function c(e,t,r,n){var a=Math.min(n,r-t);return{start:t,end:t+a}}},"486c":function(e,t){e.exports=function e(){if(typeof self.DOMParser!=="undefined"){return function(e){var t=new self.DOMParser;return t.parseFromString(e,"application/xml")}}if(typeof self.ActiveXObject!=="undefined"&&new self.ActiveXObject("Microsoft.XMLDOM")){return function(e){var t=new self.ActiveXObject("Microsoft.XMLDOM");t.async="false";t.loadXML(e);return t}}return function(e){var t=document.createElement("div");t.innerHTML=e;return t}}()},"498a":function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("section",[r("t-card",{attrs:{name:"Configuration",active:+e.configuration>-1,"name-switch":e.subconfigurationSwitch,"name-model":e.nameModel}},[r("div",{staticClass:"_",attrs:{slot:"nameSwitch"},slot:"nameSwitch"},[r("q-list",e._l(e.nameStates,function(t){return r("q-item",{attrs:{flat:"flat",clickable:"clickable",color:"white"},on:{click:function(r){e.nameModel=t;e.model.is_subconfig=t.value;e.subconfigStateChanged("is_subconfig")}}},[r("q-item-section",{attrs:{avatar:"avatar"}},[r("q-icon",{attrs:{name:t.icon,color:"white"}})],1),r("q-item-section",[r("div",{staticClass:"force-title-size"},[e._v(e._s(t.name))])])],1)}),1)],1),e.configuration?r("div",{staticClass:"_"},[r("div",{staticClass:"card mid",attrs:{dark:"dark",inline:"inline"}},[r("div",{staticClass:"card-main"},[e.meshMode?r("div",{staticClass:"_"},[r("q-tabs",{attrs:{"indicator-color":"transparent","active-color":"white","inline-label":"inline-label","switch-indicator":"switch-indicator",align:"left"},model:{value:e.meshConfigTabs,callback:function(t){e.meshConfigTabs=t},expression:"meshConfigTabs"}},[r("q-tab",{attrs:{default:"default",label:"base",name:"mainTab",icon:"settings_input_component"}}),r("q-tab",{attrs:{label:"extra",name:"additionalTab",icon:"horizontal_split"}})],1),r("q-tab-panels",{model:{value:e.meshConfigTabs,callback:function(t){e.meshConfigTabs=t},expression:"meshConfigTabs"}},[r("q-tab-panel",{attrs:{name:"mainTab"}},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,neverEmpty:"neverEmpty","target-field":"division_ratio","section-label":"New Division Ratio",info:"New Division Ratio","update-model":"update-model"},on:{save:function(t){e.toggleButtonChanged("division_ratio",t,{isMesh:true});e.model.division_ratio=t}}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"division_distorted_ratio","section-label":"Division Distorted Ratio",info:"Division Distorted Ratio","update-model":"update-model"},on:{save:function(t){e.toggleButtonChanged("division_distorted_ratio",t,{isMesh:true});e.model.division_distorted_ratio=t}}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-select",{attrs:{"target-model":e.model,neverEmpty:"neverEmpty","target-field":"series",options:e.seriesChoices,"section-label":"Series pick:","update-model":"update-model","emit-value":"emit-value",up:"up"},on:{save:function(t){return e.toggleButtonChanged("series",t,{isMesh:true})}}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,neverEmpty:"neverEmpty","target-field":"channel","section-label":"Channel","update-model":"update-model"},on:{save:function(t){e.toggleButtonChanged("channel",t,{isMesh:true})}}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:[{label:"#",value:"#"},{label:"OFF",value:""},{label:"MIRROR",value:"1"}],big:"big","target-model":e.model,"target-field":"mirror","allow-arbitraty-constat":true,"section-label":"Mirror"},on:{toggleButtonChanged:function(t){return e.toggleButtonChanged("mirror",e.model.mirror,{isMesh:true})}}})],1)])]),r("q-tab-panel",{staticStyle:{padding:"0px"},attrs:{name:"additionalTab"}},[e.meshMode?r("div",{staticClass:"_"},[r("t-sections",[r("t-section",{attrs:{name:"Legs"}},[r("div",{staticClass:"card-content"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"legs_positions","section-label":"Position",info:"Position","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("legs_positions",t,{isMesh:true})}}})],1)])],1)],1):e._e()])],1),r("div",{staticClass:"card-main q-pl-sm margin-b"},[r("t-tags",{attrs:{"target-model":e.model,"target-field":"additional_params","section-label":"Additional parameters","update-model":"update-model"},on:{save:function(t){return e.broadcastNewParameters(e.model.additional_params,true)}}})],1)],1):!e.meshMode&&!e.model.component?r("div",{staticClass:"_"},[r("q-tabs",{attrs:{"indicator-color":"transparent","active-color":"white","inline-label":"inline-label","switch-indicator":"switch-indicator",align:"left"},model:{value:e.selectedTabCompConfig,callback:function(t){e.selectedTabCompConfig=t},expression:"selectedTabCompConfig"}},[r("q-tab",{attrs:{default:"default",label:"base",name:"mainCompConfigTab",icon:"settings_input_component"}}),r("q-tab",{attrs:{label:"exterior",name:"exteriorTab",icon:"border_outer"}}),r("q-tab",{attrs:{label:"interior",name:"interiorTab",icon:"border_inner"}})],1),r("q-tab-panels",{model:{value:e.selectedTabCompConfig,callback:function(t){e.selectedTabCompConfig=t},expression:"selectedTabCompConfig"}},[r("q-tab-panel",{attrs:{name:"mainCompConfigTab"}},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.typeConfig,"target-model":e.model,"target-field":"type","allow-arbitraty-constat":true,"section-label":"Part type"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)]),e.newHeight==="sub"?r("div",{staticClass:"row"},[e._v("Part section"),r("div",{staticClass:"_"},e._l(e.partsItems,function(t,n){return[0,1,2].indexOf(n)>-1?r("q-toggle",{key:n,staticClass:"col-4",attrs:{label:t.name,val:t.val},on:{input:e.changePartSelection},model:{value:e.partsSelection,callback:function(t){e.partsSelection=t},expression:"partsSelection"}}):e._e()}),1),r("div",{staticClass:"_"},e._l(e.partsItems,function(t,n){return[3,4].indexOf(n)>-1?r("q-toggle",{key:n,staticClass:"col-4",attrs:{label:t.name,val:t.val},on:{input:e.changePartSelection},model:{value:e.partsSelection,callback:function(t){e.partsSelection=t},expression:"partsSelection"}}):e._e()}),1),r("div",{staticClass:"_"},e._l(e.partsItems,function(t,n){return[5,6,7].indexOf(n)>-1?r("q-toggle",{key:n,staticClass:"col-4",attrs:{label:t.name,val:t.val},on:{input:e.changePartSelection},model:{value:e.partsSelection,callback:function(t){e.partsSelection=t},expression:"partsSelection"}}):e._e()}),1)]):e._e(),r("div",{staticClass:"row"},[e.newHeight!=="sub"&&!e.model.component?r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.heightConfig,"target-model":e.model,"target-field":"newHeight","allow-arbitraty-constat":true,"section-label":"Height",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1):e._e()]),r("div",{staticClass:"row"},[e.newHeight!=="sub"?r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:[{label:"#",value:"#"},{label:"OFF",value:""},{label:"MIRROR",value:"1"}],"target-model":e.model,"target-field":"mirror","allow-arbitraty-constat":true,"section-label":"Mirror",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1):e._e()]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"})]),r("div",{staticClass:"row"},[e.model.hide!=="1"&&e.newHeight==="sub"?r("div",{staticClass:"_ q-pt-none"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"start","section-label":"From","update-model":"update-model"},on:{updateField:e.updateField}}),r("t-input",{attrs:{"target-model":e.model,"target-field":"stop","section-label":"To","update-model":"update-model"},on:{updateField:e.updateField}})],1):e._e()]),r("div",{staticClass:"row"},[e.newHeight!=="sub"?r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"double_pos","allow-arbitraty-constat":true,"section-label":"Double",info:"Position","update-model":"update-model"},on:{updateField:e.updateField}})],1):e._e(),e.model.double_pos&&e.newHeight!=="sub"?r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"double_start","allow-arbitraty-constat":true,"section-label":"Double start",info:"Position","update-model":"update-model"},on:{updateField:e.updateField}}),r("t-input",{attrs:{"target-model":e.model,"target-field":"double_stop","allow-arbitraty-constat":true,"section-label":"Double stop",info:"Position","update-model":"update-model"},on:{updateField:e.updateField}})],1):e._e(),e.newHeight!=="sub"?r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"triple_pos","allow-arbitraty-constat":true,"section-label":"Tripple",info:"Position","update-model":"update-model"},on:{updateField:e.updateField}})],1):e._e(),e.model.triple_pos&&e.newHeight!=="sub"?r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"triple_start","allow-arbitraty-constat":true,"section-label":"Tripple start",info:"Position","update-model":"update-model"},on:{updateField:e.updateField}}),r("t-input",{attrs:{"target-model":e.model,"target-field":"triple_stop","allow-arbitraty-constat":true,"section-label":"Tripple stop",info:"Position","update-model":"update-model"},on:{updateField:e.updateField}})],1):e._e()])]),r("q-tab-panel",{staticStyle:{padding:"0px"},attrs:{name:"exteriorTab"}},[r("div",{staticClass:"card-content"},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.edgeConfigTop,"target-model":e.model,"target-field":"top",big:"big","allow-arbitraty-constat":true,"section-label":"Top"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.edgeConfig,"target-model":e.model,"target-field":"right","allow-arbitraty-constat":true,"section-label":"Right",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.edgeConfig,"target-model":e.model,"target-field":"left","allow-arbitraty-constat":true,"section-label":"Left",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.edgeConfig,"target-model":e.model,"target-field":"bottom",big:"big","allow-arbitraty-constat":true,"section-label":"Bottom"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.edgeConfigBack,"target-model":e.model,"target-field":"back","allow-arbitraty-constat":true,"section-label":"Back",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)])]),r("t-sections",[e.newHeight!=="sub"&&e.model.double_pos!==""?r("div",{staticClass:"_"},[r("t-section",{attrs:{name:"Part (from double)"}},[r("div",{staticClass:"card-content"},[r("t-presets",{attrs:{options:e.edgeConfig,"target-model":e.model,"target-field":"middle","allow-arbitraty-constat":true,"section-label":"Middle",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)])],1):e._e(),e.newHeight!="sub"&&e.model.triple_pos!==""?r("div",{staticClass:"_"},[r("t-section",{attrs:{name:"Part (from tripple)"}},[r("div",{staticClass:"card-content"},[r("t-presets",{attrs:{options:e.edgeConfig,"target-model":e.model,"target-field":"middle_left","allow-arbitraty-constat":true,"section-label":"Middle left",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}}),r("t-presets",{attrs:{options:e.edgeConfig,"target-model":e.model,"target-field":"middle_right","allow-arbitraty-constat":true,"section-label":"Middle right",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)])],1):e._e()])],1),r("q-tab-panel",{staticStyle:{padding:"0px"},attrs:{name:"interiorTab"}},[r("t-sections",[r("t-section",{attrs:{name:"Domains"}},[r("div",{staticClass:"card-content"},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("q-toggle",{staticClass:"col-5",attrs:{label:"add domains"},on:{input:function(t){return e.toggleButtonChanged("dom_exist",t)}},model:{value:e.model.dom_exist,callback:function(t){e.$set(e.model,"dom_exist",t)},expression:"model.dom_exist"}}),e.model.dom_exist?r("q-toggle",{staticClass:"col-5",attrs:{label:"share domains"},on:{input:function(t){return e.toggleButtonChanged("dom_share",t)}},model:{value:e.model.dom_share,callback:function(t){e.$set(e.model,"dom_share",t)},expression:"model.dom_share"}}):e._e()],1)]),r("div",{staticClass:"row"},[e.model.dom_exist?r("div",{staticClass:"card-row"},[r("div",{staticClass:"col-card-12"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"dom_x","allow-arbitraty-constat":true,"section-label":"DOM X:",info:"Position / more_horiz","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("dom_x",e.model.dom_x)}}})],1),r("div",{staticClass:"col-card-12"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"dom_y","allow-arbitraty-constat":true,"section-label":"DOM Y:",info:"Position / more_horiz","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("dom_y",e.model.dom_y)}}})],1)]):e._e()])])]),r("t-section",{attrs:{name:"Doors"}},[r("div",{staticClass:"card-content"},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"split","allow-arbitraty-constat":true,"section-label":"Split:","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("split",e.model.split)}}})],1)]),r("div",{staticClass:"row"},[e.model.split?r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"split_start","allow-arbitraty-constat":true,"section-label":"Split start:","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("split_start",e.model.split_start)}}})],1):e._e()]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:[{label:"#",value:"#"},{label:"OFF",value:""},{label:"FLIP",value:"1"}],big:"big","target-model":e.model,"target-field":"flip","allow-arbitraty-constat":true,"section-label":"Flip"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)])])]),r("t-section",{attrs:{name:"Drawers"}},[r("div",{staticClass:"card-content"},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"drawer_divisions","allow-arbitraty-constat":true,"section-label":"Divisions:","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("drawer_divisions",e.model.drawer_divisions)}}})],1)])])]),r("t-section",{attrs:{name:"Cable managment"}},[r("div",{staticClass:"card-content"},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"cable_pos_y","allow-arbitraty-constat":true,"section-label":"Cable Pos Y::","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("cable_pos_y",e.model.cable_pos_y)}}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"cable_height","allow-arbitraty-constat":true,"section-label":"Cable height:","update-model":"update-model"},on:{save:function(t){return e.toggleButtonChanged("cable_height",e.model.cable_height)}}})],1)])])])],1)],1)],1),r("div",{staticClass:"card-main q-pl-sm margin-b"},[r("t-tags",{attrs:{"target-model":e.model,"target-field":"additional_params","section-label":"Additional parameters","update-model":"update-model"},on:{save:function(t){return e.broadcastNewParameters(e.model.additional_params,true)}}})],1)],1):!e.meshMode&&e.model.component?r("div",{staticClass:"_"},[r("q-tabs",{attrs:{inverted:"inverted",align:"left",color:"black"},model:{value:e.selectedTabCompConfigAlternate,callback:function(t){e.selectedTabCompConfigAlternate=t},expression:"selectedTabCompConfigAlternate"}},[r("q-tab",{attrs:{slot:"title",default:"default",label:"base",name:"mainCompConfigTab",icon:"settings_input_component"},slot:"title"})],1),r("q-tab-panels",{model:{value:e.selectedTabCompConfigAlternate,callback:function(t){e.selectedTabCompConfigAlternate=t},expression:"selectedTabCompConfigAlternate"}},[r("q-tab-panel",{attrs:{name:"mainCompConfigTab"}},[r("div",{staticClass:"row"},[e.newHeight!=="sub"?r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.newHeight,"section-label":"Component Name",neverEmpty:"neverEmpty","update-model":"update-model"},on:{updateField:e.updateField}})],1):e._e()]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-select",{attrs:{"target-model":e.newHeight,options:Object.values(e.model.nested_object_choices).map(function(e){return{label:e,value:e}}).sort(function(e,t){return e.value>t.value?1:t.value>e.value?-1:0}),"section-label":"Component select",neverEmpty:"neverEmpty","update-model":"update-model"},on:{input:function(t){return e.toggleButtonChanged("newHeight",e.newHeight)}}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:[{label:"#",value:"#"},{label:"OFF",value:""},{label:"MIRROR",value:"1"}],"target-model":e.model,"target-field":"mirror","allow-arbitraty-constat":true,"section-label":"Mirror",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-presets",{attrs:{options:e.hideConfig,"target-model":e.model,"target-field":"hide","allow-arbitraty-constat":true,"section-label":"Active",big:"big"},on:{toggleButtonChanged:e.toggleButtonChanged}})],1)]),r("div",{staticClass:"row"},[e.model.hide!=="1"&&e.newHeight==="sub"?r("div",{staticClass:"_ q-pt-none"},[r("t-input",{attrs:{"target-model":e.model,"target-field":"start","section-label":"From","update-model":"update-model"},on:{updateField:e.updateField}}),r("t-input",{attrs:{"target-model":e.model,"target-field":"stop","section-label":"To","update-model":"update-model"},on:{updateField:e.updateField}})],1):e._e()]),r("div",{staticClass:"card-main q-pl-sm margin-b"},[r("t-tags",{attrs:{"target-model":e.model,"target-field":"additional_params","section-label":"Additional parameters","update-model":"update-model"},on:{save:function(t){return e.broadcastNewParameters(e.model.additional_params,true)}}})],1)])],1)],1):e._e()]),e.model?r("div",{staticClass:"card-main"},[e.model.info?r("div",{staticClass:"_"},[r("t-section",{attrs:{name:"Contents"}},[e.model.element?r("div",[r("q-chip",{staticClass:"q-mr-sm",attrs:{color:"grey-7"}},[r("span",{staticClass:"height"},[e._v("Element #"+e._s(e.model.element))])]),r("router-link",{staticClass:"q-mr-sm",attrs:{color:"black",dark:"true",to:"/preview/element/"+e.model.element}},[r("q-btn",{staticStyle:{color:"black"},attrs:{icon:"remove_red_eye",rounded:"rounded",color:"grey-8",inverted:"inverted"}})],1),r("a",{staticClass:"admin q-mr-sm",attrs:{color:"black",dark:"true",href:"/admin/webdesigner/componentconfiguration/"+e.model.element+"/change/"}},[r("q-btn",{staticStyle:{color:"black"},attrs:{icon:"edit",rounded:"rounded",color:"grey-8",inverted:"inverted"}})],1)],1):e._e(),e.model.mesh?r("div",[r("q-chip",{attrs:{color:"grey-7"}},[r("span",{staticClass:"height"},[e._v("Mesh #"+e._s(e.model.mesh))])])],1):e._e()])],1):e._e()]):e._e(),e.additionalParamsParsed.length>0?r("q-tabs",{attrs:{align:"left",swipeable:"true","no-pane-border":"true",color:"white",size:"sm"},model:{value:e.selectedAdditionalParam,callback:function(t){e.selectedAdditionalParam=t},expression:"selectedAdditionalParam"}},e._l(e.additionalParamsParsed,function(e,t){return r("q-tab",{key:t,attrs:{slot:"title",size:"sm",color:"black",label:e.name,name:t},slot:"title"})}),1):e._e(),e.additionalParamsParsed.length>0&&e.selectedAdditionalParamObject&&e.model.additional_params.indexOf(e.selectedAdditionalParamObject.name+"="+e.selectedAdditionalParamObject.value)>-1?r("div",{staticClass:"card-main"},[r("div",[r("q-input",{attrs:{value:this.selectedAdditionalParamObject.name+"="+e.selectedAdditionalParamObject.value},on:{input:e.updateParamFull}}),e._l(e.additionalParamParsed,function(t,n){return r("div",{staticClass:"param"},[r("q-field",{attrs:{label:n+1+"."}},[t.type=="percentage"?r("q-slider",{attrs:{snap:"snap",step:"1",min:-100,max:100,square:"square"},on:{input:function(r){return e.updateParam(r,t)}},model:{value:t.value,callback:function(r){e.$set(t,"value",r)},expression:"param.value"}}):e._e(),t.type=="string"?r("q-input",{on:{input:function(r){return e.updateParam(r,t)}},model:{value:t.value,callback:function(r){e.$set(t,"value",r)},expression:"param.value"}}):e._e(),t.type=="bool"?r("q-btn-toggle",{attrs:{size:"sm","toggle-color":"black",options:[{label:"True",value:"t"},{label:"False",value:"f"}]},on:{input:function(r){return e.updateParam(r,t)}},model:{value:t.value,callback:function(r){e.$set(t,"value",r)},expression:"param.value"}}):e._e()],1)],1)})],2)]):e._e(),r("div",{staticClass:"card-separator"}),e.model.info?r("q-collapsible",{attrs:{label:"Info"}},[r("pre",[e._v(e._s(e.model.info.info))])]):e._e(),r("div",{staticClass:"card-separator"})],1)]):e._e(),r("div",{staticClass:"_",attrs:{slot:"links"},slot:"links"},[e.model.component?r("div",{staticClass:"_ display-inline"},[r("router-link",{attrs:{tag:"a",to:"/components/"+e.$route.params.selectedCollection+"/"+e.model.component}},[r("q-btn",{attrs:{icon:"settings","text-color":"grey",size:"sm",filled:"filled",dense:"dense",color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Component ID: "+e._s(e.model.component))])],1)],1)],1):e._e(),e.meshMode?r("div",{staticClass:"_ display-inline"},[r("q-btn",{attrs:{icon:"build",type:"a",href:"/admin/webdesigner/meshconfiguration/"+e.configuration+"/change/","text-color":"grey",size:"sm",filled:"filled",dense:"dense",label:e.configuration,color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Configuration ID:"+e._s(e.configuration))])],1)],1):r("div",{staticClass:"_ display-inline"},[r("q-btn",{attrs:{icon:"build",type:"a",href:"/admin/webdesigner/componentconfiguration/"+e.configuration+"/change/",dense:"dense","text-color":"grey",size:"sm",color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Configuration ID:"+e._s(e.configuration))])],1)],1)]),r("div",{staticClass:"_",attrs:{slot:"options"},slot:"options"},[r("t-rpc-action",{attrs:{source:e.meshMode?"meshconfiguration_"+e.configuration:"componentconfiguration_"+e.configuration,buttonText:"Duplicate",methodName:"duplicate",withInput:"true",promptTitle:"Duplicate configuration",promptMessage:e.meshMode?"Enter new ratios":"Enter new heights"}}),r("q-btn",{attrs:{icon:"delete",label:"Delete",color:"red"},on:{click:e.remove}})],1)])],1)};var a=[];var i=r("e1c2");var o=r("9523");var s=r.n(o);var l=r("9af0");var c=r("5a51");var u=r("278c");var d=r.n(u);var f=r("9b8e");var p=r("1df5");var h=r("359c");var v=r("7341");var m=r("fb2b");var g=r("3156");var b=r.n(g);var y=r("d6b6");var w=r("7b01");var x=r("18a5");var _=r("9ec3");var C=r.n(_);var S=r("39fc");var k=[{label:"#",value:"#"},{label:"DEF",value:""},{label:"Open",value:"0"}];var A={props:{configuration:[String,Number],meshMode:[Boolean,String],subconfigurationSwitch:[Boolean]},components:b()({},S["j"]),computed:{seriesChoices:function e(){var t=this;return Object.keys(this.model.series_choices).map(function(e){return{label:t.model.series_choices[e],value:+e}})}},watch:{model:{handler:function e(t,r){console.log("valueupdate",t,r)},deep:true},newHeight:function e(){if(this.model.height!=this.newHeight)this.save()},selectedAdditionalParam:function e(){this.selectedAdditionalParamObject=this.additionalParamsParsed[this.selectedAdditionalParam]},selectedAdditionalParamObject:function e(){this.parseParameter()},"model.additional_params":function e(){this.parseParameters();this.parseParameter()},newWidthRatio:function e(){if(this.model.division_ratio!=this.newWidthRatio){this.save();this.$emit("reload")}},configuration:function e(){this.selectedAdditionalParamObject=null;this.paramsNotSet=[];this.load()}},methods:{changePartSelection:function e(){var t=this;x["a"].api.updateConfiguration(this.configuration,{part:this.partsSelection,isMesh:this.meshMode}).subscribe(function(e){x["a"].api.geo.update(t);console.log("re$",e)})},isConstant:function e(t){if(t===undefined){return false}else{return t.toString().startsWith("#")}},parseParameters:function e(){var t=this.model.additional_params;if(t==null)return;this.additionalParamsParsed=t.map(function(e,t){if(e===null)return;var r=e.split("="),n=d()(r,2),a=n[0],i=n[1];if(i!=null){return{id:t,name:a,value:i}}else{return false}}).filter(Boolean)},parseParameter:function e(t){if(!t)t=this.selectedAdditionalParamObject.value;this.additionalParamParsed=t.split(",").map(function(e,t){if(e!=null){var r="string";if(e.toLowerCase()=="t"||e.toLowerCase()=="f")r="bool";if(e.indexOf("%")>-1){r="percentage";e=parseInt(e)}return{id:t,type:r,value:e}}else{return false}}).filter(Boolean)},updateParamFull:function e(t){var r=this.model.additional_params;r[this.selectedAdditionalParamObject.id]=t;this.selectedAdditionalParamObject.value=t.split("=")[1];this.broadcastNewParameters(r);this.parseParameters();this.parseParameter()},updateParam:function e(t,r){var n="".concat(r.value);if(r.type=="percentage"){n+="%"}var a=this.additionalParamParsed.map(function(e){switch(e.type){case"percentage":return e.value+"%";break;case"string":return e.value;break;case"bool":return e.value;break}});a[r.id]=n;n=a.join(",");var i=this.model.additional_params;i[this.selectedAdditionalParamObject.id]="".concat(this.selectedAdditionalParamObject.name,"=").concat(n);this.selectedAdditionalParamObject.value=n;this.broadcastNewParameters(i);this.parseParameters();this.parseParameter()},reloadPreview:C.a.debounce(function(){this.$emit("newParameters")},500),_broadcastNewParameters:function e(t,r){var n=this;x["a"].api.updateConfiguration(+this.configuration,{additional_params:t,isMesh:this.meshMode}).subscribe(function(e){console.log("re$",e);n.additionalParamsParsed=[];n.additionalParamParsed=[];x["a"].api.geo.update(n);n.$emit("newParameters")})},broadcastNewParameters:C.a.throttle(function(e,t){this._broadcastNewParameters(e,t)},50),save:function e(){var t=this;var r;var n=parseInt(this.configuration);switch(this.meshMode.toString()){case"true":r=x["a"].api.updateConfiguration(n,{isMesh:true,division_ratio:this.newWidthRatio}).subscribe(function(e){t.postSave();x["a"].api.geo.update(t)});break;default:r=x["a"].api.updateConfiguration(n,{height:this.newHeight}).subscribe(function(e){t.postSave();x["a"].api.geo.update(t)});break}},postSave:function e(t){this.$emit("reloadPreview");this.$emit("newConfigHeight",{id:this.configuration,height:this.newHeight});return t},remove:function e(){var t=this;var r=parseInt(this.configuration);this.$q.dialog({title:"Remove configuration",message:"Remove configuration?",ok:"Yes",cancel:"Return"}).onOk(function(){x["a"].api.removeConfiguration(r,t.meshMode).subscribe(function(e){t.updateAfterAction()});TylkoNotifications.notify(t.$q,"Removing configuration #".concat(r,"..."))})},updateAfterAction:function e(){this.$emit("deletedConfiguration");this.$emit("reloadPreview");x["a"].api.geo.update(this)},fetch:function e(){var t=this;switch(this.model.element!=null){case true:x["a"].api.element(this.model.element).subscribe(function(e){t.nameFetched=e.name});break;case false:x["a"].api.componentSetup(this.meshMode?this.$route.params.setWidth:this.$route.params.setHeight).subscribe(function(e){t.nameFetched=e.name});break}},removeThisConfiguration:function e(){console("call remove..")},load:function e(){var t=this;var r=parseInt(this.configuration);this.fetching=true;x["a"].api.configuration(r,this.meshMode).subscribe(function(e){console.log("!!!!!!!",e);t.model=e;t.nameModel=t.model.is_subconfig?t.nameStates[1]:t.nameStates[0];t.partsSelection=e.part?e.part:[];t.newHeight=e.height;t.newWidthValue=e.starting_width;t.newWidthRatio=e.division_ratio;t.fetch();t.fetching=false})},toggleButtonChanged:function e(t,r,n){var a=this;if(!t)return;if(t=="newHeight")t="height";this.model[t]=r;x["a"].api.updateConfiguration(+this.configuration,b()(s()({},t,r),n)).subscribe(function(){x["a"].api.geo.update(a);a.$emit("newParameters")})},updateField:function e(t){var r=this;if(!t)return;console.log("WTFF",t);x["a"].api.updateConfiguration(+this.configuration,s()({},t.field,t.value)).subscribe(function(){x["a"].api.geo.update(r);r.$emit("newParameters")})},subconfigStateChanged:function e(t,r){var n,a,i=this;r=this.model.is_subconfig?true:false;this.newHeight=r===true?"sub":this.newHeight!=="sub"?this.newHeight:100;x["a"].api.updateConfiguration(+this.configuration,r?(n={},s()(n,t,r),s()(n,"height","sub"),n):(a={},s()(a,t,r),s()(a,"height","100"),a)).subscribe(function(){x["a"].api.geo.update(i);i.$emit("newParameters")})}},mounted:function e(){var t=this;x["a"].application.contextMenu.listenAction({action:"reload",context:"editor"}).subscribe(function(){t.load()})},data:function e(){Object.assign(this,{partsItems:[{name:"Single",val:"1"},{name:"NEUTRAL",val:"-1"},{name:"ALL",val:"0"},{name:"DoubleLeft",val:"2"},{name:"DoubleRright",val:"3"},{name:"TripleLeft",val:"4"},{name:"TripleMiddle",val:"5"},{name:"TripleRight",val:"6"}],edgeConfig:[].concat(k,[{label:"Solid",value:"1"},{label:"Insert",value:"2"}]),edgeConfigTop:[].concat(k,[{label:"Solid",value:"1"},{label:"Insert",value:"2"},{label:"Dziura",value:"3"}]),edgeConfigBack:[].concat(k,[{label:"Back",value:"1"},{label:"S_L",value:"2"},{label:"S_R",value:"3"},{label:"S_D",value:"4"}]),edgeConfigFront:[].concat(k,[{label:"Solid",value:"1"},{label:"Door",value:"2"},{label:"Drawer",value:"3"}]),typeConfig:[{label:"#",value:"#"},{label:"opening",value:""},{label:"door",value:"D"},{label:"drawer",value:"T"},{label:"front box",value:"FB"},{label:"top box",value:"TB"}],heightConfig:[{label:"A",value:"200"},{label:"B",value:"300"},{label:"C",value:"400"},{label:"D",value:"500"},{label:"E",value:"600"},{label:"F",value:"700"},{label:"G",value:"800"},{label:"H",value:"900"},{label:"I",value:"1000"},{label:"J",value:"1100"},{label:"K",value:"1200"}],sectionConfig:[{label:"#",value:"#"},{label:"single",value:"1"},{label:"double right",value:"3"}],hideConfig:[{label:"VISIBLE",value:""},{label:"HIDDEN",value:"1",toggleColor:"red"}],nameStates:[{icon:"border_all",name:"Configuration",value:false},{icon:"border_inner",name:"Subconfiguration",value:true}]});return{fetching:true,nameModel:{icon:"border_all",name:"Configuration",value:false},selectedTabMain:"mainTab",selectedTabCompConfig:"mainCompConfigTab",selectedTabCompConfigAlternate:"mainCompConfigTab",production:window.location.href.indexOf("localhost")==-1,partsSelection:[],model:{},meshConfigTabs:"mainTab",additionalParamsParsed:[],additionalParamParsed:[],selectedAdditionalParam:-1,selectedAdditionalParamObject:null,newHeight:0,newSubconfigState:false,nameFetched:"Loading...",newWidthValue:0,newWidthRatio:"0",paramsNotSet:[]}}};var E=A;var P=r("3880");var M=r("2877");var T=Object(M["a"])(E,n,a,false,null,null,null);var I=t["a"]=T.exports},"4dae":function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("section",{class:e.meshMode?"editor mesh":"editor",style:e.meshMode?"height: 145px;":"height: calc(100vh - 235px); position: relative;"})};var a=[];var i=r("6db0");var o=r("359c");var s=r("7341");var l=r("2f26");var c=r("a34a");var u=r.n(c);var d=r("192a");var f=r("c973");var p=r.n(f);var h=r("7b01");var v=r("9ec3");var m=r.n(v);var g=r("3e8f");var b=r("18a5");var y=r("7621");var w=r("9af0");var x=r("3156");var C=r.n(x);var S=r("970b");var k=r.n(S);var A=r("5bc3");var E=r.n(A);var P=r("912c");var M=r("b082");var T=r("b268");var I=function(){function e(){var t=this;k()(this,e);this.checked=false;this.bgDrawn=false;this.items=[];this.labels=[];this.sprites=[];this.orderHittest=[];this.getSortArray=[];window.addEventListener("resize",function(e){if(!t.pixiApp)return;var r=t.getSize(),n=r.width,a=r.height;t.pixiApp.renderer.resize(n,a);t.pixiApp.view.style["max-width"]="".concat(n,"px");t.backgroundContainer.clear();t.bgDrawn=false;t.draw()})}E()(e,[{key:"initializePixi",value:function e(){var t=this;var r=this.getSize(),n=r.width,a=r.height;if(this.pixiApp)return;this.pixiApp=new P["Application"](n,a,{backgroundColor:0,resolution:2});var i=this.pixiApp;this.pixiRoot=new P["Container"];this.pixiHitests=new P["Container"];this.pixiRoot=new P["Container"];this.drawingContainer=new P["Graphics"];this.backgroundContainer=new P["Graphics"];this.pixiRoot.addChild(this.backgroundContainer);this.pixiRoot.addChild(this.drawingContainer);this.pixiApp.stage.addChild(this.pixiRoot);this.pixiApp.stage.addChild(this.pixiHitests);this.pixiApp.view.style["max-width"]="".concat(n,"px");this.pixiApp.stop();M["a"].init(this.pixiApp).then(function(){t.draw()})}},{key:"setContext",value:function e(t){this.vue=t;this.initializePixi();this.vue.$el.appendChild(this.pixiApp.view)}},{key:"getSize",value:function e(){var t=this.vue.$el.getBoundingClientRect();var r={width:t.width,height:t.height};return{width:r.width,height:r.height}}},{key:"getElementPosition",value:function e(t){var r=this.vue.isHorizontal();var n=8;var a=r?120:300;var i=r?130:85;var o=10;var s=r?100:1;var l=1;var c=s*l;var u=function e(t){var r=Math.floor(t/c);var o=t-r*c;var s=Math.floor(o/l);var u=o-s*l;var d=Math.round(t/c);return{x:s*a+ +n/2,y:u*i+n/2+r*i*l}};return C()({},u(t),{w:a-n,h:i-n})}},{key:"addItem",value:function e(t,r){var n={id:t,type:r,order:this.vue.items.length+2};this.vue.items.unshift(n);this.vue.addConfiguration(n)}},{key:"drawItem",value:function e(t,r,n,a){var i=this;var o=this.vue.isHorizontal();var s=this.getElementPosition(t),l=s.x,c=s.y,u=s.w,d=s.h;if(o){this.orderHittest.push({x:l,w:u})}else{this.orderHittest.push({y:c,h:d})}var f=a?a:parseInt(this.vue.$route.params.selectedConfiguration);var p=new P["Graphics"];this.sprites[t]=p;p.order=t;p.height=d;p.width=u;p.beginFill(f==r.id?5592405:1118481,1).drawRect(0,0,u,d).endFill();Object(T["a"])(p,u,d,{item:r,type:n},"editor");var h=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"";return t||!t&&t===0?"".concat(r).concat(t,"\n"):""};var v=function e(t){return _.truncate(t,{omission:"...",length:u/8})};var m="";if(r.config_type==="component"){m+=r.subconfig?"  ":"H: ".concat(r.height," - ");m+="".concat(r.type==="element"?r.elem_type:r.name,"\n");if(r.double)m+="  Double: ".concat(r.double,"\n");if(r.triple)m+="  Triple: ".concat(r.double,"\n");if(r.start||r.stop)m+="  Active from ".concat(r.start||"-"," to ").concat(r.stop||"-","\n");if(r.subconfig)m+="  Parts: ".concat((r.parts||[]).join(", "),"\n")}else{m+="".concat(v(r.name),"\n");switch(r.type){case"table":m+="".concat(h(v(r.series)));m+=r.division_distorted_ratio?"".concat(r.ratio," - ").concat(r.division_distorted_ratio,"\n"):h(r.ratio);m+="".concat(h(r.channel,"Channel: "));m+="".concat(h(r.legs_positions,"Legs: "));break}}this.drawText(m,l,c,p);p.hitArea=new P["Rectangle"](0,0,u,d);p.position.x=l;p.position.y=c;p.interactive=true;p.buttonMode=true;p.cursor="pointer";p.on("mousedown",function(e){return i.onDragStart(r,t,p,e)}).on("mouseup",function(){return i.onDragEnd(r.id,p,r,t)}).on("mouseoutside",function(){return i.onDragEnd(r.id,p)}).on("mousemove",function(e){return i.onDragMove(r,t,p,e)}).on("rightdown",function(e){e.data.originalEvent.preventDefault();b["a"].application.contextMenu.show(e,r,i.vue.isHorizontal()?"mesh":"component-set")}).on("rightup",function(e){e.data.originalEvent.preventDefault()});window.addEventListener("contextmenu",function(e){e.stopPropagation();if(e.path[0].tagName==="CANVAS"){e.preventDefault()}});this.pixiHitests.addChild(p)}},{key:"transferControl",value:function e(){this.dragging=false;console.log("trasnfer")}},{key:"onDragStart",value:function e(t,r,n,a){n.data=a.data;n.alpha=.5;n.dragging=true;n.startPosition=n.data.getLocalPosition(this.pixiApp.stage)}},{key:"liveSortExecute",value:function e(t,r){var n=this;var a=this.vue.isHorizontal();var i=null;if(a){this.orderHittest.map(function(e,t){console.log(r,e.x,i);if(r>=e.x&&r<=e.x+e.w+16){i=t}})}else{this.orderHittest.map(function(e,t){if(r>=e.y&&r<=e.y+e.h+16){i=t}})}if(i===null){i=this.orderHittest.length}var o=null;var s=this.sprites.map(function(e,r){if(r!=t){return e}else{o=e}}).filter(function(e){return e?true:false});s.splice(i,0,o);s.map(function(e,t){if(t==i)return;if(a){var r=n.getElementPosition(t),o=r.x;e.position.x=o}else{var s=n.getElementPosition(t),l=s.y;e.position.y=l}});this.getSortArray=s;this.directionReverse=t>i}},{key:"onDragEnd",value:function e(t,r,n,a){var i=this;r.alpha=1;r.dragging=false;var o=0;if(!r.delta){this.vue.selectConfiguration(n,a);r.delta=false}else{var s=this.vue.items.map(function(e){return{order:e.order}});this.getSortArray.map(function(e,t){var r=e.order;var n=_.findIndex(s,{order:r});i.vue.items[n].order=t});var l=_.orderBy(this.vue.items,["order"],["desc"]);this.vue.items=l;this.vue.saveOrder()}}},{key:"onDragMove",value:function e(t,r,n,a){var i=this.vue.isHorizontal();if(n.dragging){var o=8;if(n.delta){var s=n.data.getLocalPosition(this.pixiApp.stage);var l;if(i){l=Math.ceil(s.x/o)*o;n.position.x=l-50}else{l=Math.ceil(s.y/o)*o;n.position.y=l-50}this.liveSortExecute(r,l)}else{var s=n.data.getLocalPosition(this.pixiApp.stage);var c=n.startPosition;var u=Math.sqrt(Math.pow(s.y-c.y,2)+Math.pow(s.x-c.x,2));if(u>o){n.delta=true}}}}},{key:"draw",value:function e(t){var r=this;this.labels.map(function(e,t){if(e){r.pixiApp.stage.removeChild(e);r.labels[t]=null;e=null}});this.sprites.map(function(e,t){if(e){e.removeAllListeners();r.pixiHitests.removeChild(e);r.sprites[t]=null;e=null}});this.labels=[];this.sprites=[];this.orderHittest=[];if(!this.bgDrawn){var n=this.getSize(),a=n.width,i=n.height;var o=8;for(var s=0;s<a/o;s++){for(var l=0;l<i/o;l++){this.backgroundContainer.lineStyle(1,35791401,1).drawRect(s*o,l*o,1,1)}}this.bgDrawn=true}var c=0;if(this.vue.items.length>0){_.orderBy(this.vue.items,["order"],["asc"]).map(function(e){r.drawItem(c++,e,e.type,t)})}this.pixiApp.start()}},{key:"drawText",value:function e(t,r,n,a){var i=this.pixiApp;var o=M["a"].create(t);this.labels.push(o);a.addChild(o);o.position.set(10,10)}}]);return e}();var O=new I;var D={ORIENTATION_VERTICAL:"vertical",ORIENTATION_HORIZONTAL:"horizontal"};var R={D:"Door",T:"Drawer",FB:"Front Box",TB:"Top Box"};var F=r("202d");var $={name:"TylkoEditor",props:{comp:Object,mesh:Object,orientation:String,meshMode:[Boolean,String],currentSetup:Object},data:function e(){return{checked:false,bgDrawn:false,items:[],labels:[],sprites:[],orderHittest:[],getSortArray:[]}},methods:{uuid:function e(){return F()},isHorizontal:function e(){return this.orientation=="horizontal"},lightup:function e(t){this.$emit("lightup",t)},selectConfiguration:function e(t,r){O.draw(t.id);var n="/".concat(this.meshMode?"meshes":"components","/").concat(this.$route.params.selectedCollection,"/").concat(this.$route.params.id,"/").concat(this.meshMode?this.$route.params.setWidth:this.$route.params.setHeight,"/").concat(t.id,"/");this.$router.push(n);console.log(n)},getHeightFromSetup:function e(){return 200},computeItems:function(){var e=p()(u.a.mark(function e(){var t=this;var r;return u.a.wrap(function e(n){while(1){switch(n.prev=n.next){case 0:r=null;if(!(this.currentSetup.id==null)){n.next=4;break}this.items=[];return n.abrupt("return");case 4:n.t0=this.meshMode;n.next=n.t0===true?7:n.t0==="true"?9:n.t0===false?11:13;break;case 7:r=b["a"].api.meshSetup(this.currentSetup.id);return n.abrupt("break",13);case 9:r=b["a"].api.meshSetup(this.currentSetup.id);return n.abrupt("break",13);case 11:r=b["a"].api.componentSetup(this.currentSetup.id);return n.abrupt("break",13);case 13:r.subscribe(function(e){if(e.thumbnails_data){t.$emit("setupData",e.thumbnails_data)}var r=e.configurations;var n=0;var a=r.map(function(e){var t={id:e.id,height:e.height,order:e.order,name:e.nested_object_name?e.nested_object_name:e.component?e.component:"",width:e.starting_width,config:e.id,ratio:e.division_ratio,division_distorted_ratio:e.division_distorted_ratio,data:e.component||e.element,elem_type:R[e.type]||e.type||"Opening",double:e.double_pos,triple:e.triple_pos,start:e.start,stop:e.stop,subconfig:e.is_subconfig||e.height==="sub",parts:e.part,channel:e.channel,series:e.series_choices?e.series_choices[e.series]:"",legs_positions:e.legs_positions,type:e.component?"component":e.table?"table":"element",config_type:e.hasOwnProperty("is_subconfig")?"component":"mesh",fetch:true};if(t.type=="element")t.name="";console.log("#####",e);return t});t.items=m.a.orderBy(a,["order"],[t.meshMode?"asc":"desc"]).map(function(e){e.order=n++;return e})});case 14:case"end":return n.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),reload:function e(){this.$forceUpdate()},saveOrder:function(){var e=p()(u.a.mark(function e(){var t=this;var r,n;return u.a.wrap(function e(a){while(1){switch(a.prev=a.next){case 0:r=false;n=this.items.length;a.next=4;return Promise.all(this.items.map(function(e){return new Promise(function(r){return b["a"].api.updateConfiguration(e.id,{isMesh:t.meshMode,order:t.meshMode?e.order:n-e.order}).subscribe(function(e){r()})})}));case 4:this.$emit("reloadPreview");case 5:case"end":return a.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),addConfiguration:function(){var e=p()(u.a.mark(function e(t){var r=this;var n,a=arguments;return u.a.wrap(function e(i){while(1){switch(i.prev=i.next){case 0:n=a.length>1&&a[1]!==undefined?a[1]:false;b["a"].api.createConfiguration({targetComponent:this.currentSetup.id,targetHeight:this.getHeightFromSetup(),objectType:t.type,objectId:t.id,order:this.meshMode?this.items.length:0,isMesh:this.meshMode}).then(function(e){r.$emit("reloadPreview");r.$emit("reloadAfterSave");r.$emit("reload");r.saveOrder()});case 2:case"end":return i.stop()}}},e,this)}));function t(t){return e.apply(this,arguments)}return t}(),build:function e(){if(O)O.draw()}},created:function e(){},mounted:function e(){var t=this;b["a"].application.dnd.addDestinationComponent({type:"compset-editor",instance:this,incomingHandler:function e(t){O.addItem(t.id,t.type)}});this.pixiApp=null;this.pixiRoot=null;this.drawingContainer=null;this.backgroundContainer=null;O.setContext(this);b["a"].application.contextMenu.listenAction({action:"reload",context:"editor"}).subscribe(p()(u.a.mark(function e(){return u.a.wrap(function e(r){while(1){switch(r.prev=r.next){case 0:r.next=2;return t.computeItems();case 2:t.$emit("reloadPreview");case 3:case"end":return r.stop()}}},e)})));this.computeItems();window.dispatchEvent(new Event("resize"))},watch:{items:function e(){this.build(1)},comp:function e(){this.computeItems()},currentSetup:function e(t,r){if(t==r)return;this.computeItems()},currentHeight:function e(){}}};var B=$;var j=r("78f9");var N=r("2877");var L=Object(N["a"])(B,n,a,false,null,null,null);var q=t["a"]=L.exports},"4de8":function(e,t){e.exports=function e(t){return t===null||typeof t!=="function"&&typeof t!=="object"}},"554b":function(e,t,r){"use strict";var n=r("fab3");var a=r.n(n);var i=a.a},"562e":function(e,t,r){"use strict";var n=r("0f7c");var a=r("0f01");var i=n.call(Function.call,String.prototype.replace);var o=/^[\x09\x0A\x0B\x0C\x0D\x20\xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF]+/;var s=/[\x09\x0A\x0B\x0C\x0D\x20\xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF]+$/;e.exports=function e(){var t=a.ToString(a.CheckObjectCoercible(this));return i(i(t,o,""),s,"")}},5647:function(e,t){e.exports=r;function r(e){var t=e instanceof HTMLElement;if(!t){throw new Error("svg element is required for svg.panzoom to work")}var r=e.parentElement;if(!r){throw new Error("Do not apply panzoom to the detached DOM element. ")}e.scrollTop=0;r.setAttribute("tabindex",1);var n={getBBox:i,getOwner:a,applyTransform:o};return n;function a(){return r}function i(){return{left:0,top:0,width:e.clientWidth,height:e.clientHeight}}function o(t){e.style.transformOrigin="0 0 0";e.style.transform="matrix("+t.scale+", 0, 0, "+t.scale+", "+t.x+", "+t.y+")"}}},5882:function(e,t){var r=Object.prototype.toString;e.exports=n;function n(e){return e.BYTES_PER_ELEMENT&&r.call(e.buffer)==="[object ArrayBuffer]"||Array.isArray(e)}},5975:function(e,t){e.exports=function e(t){return t>=0?1:-1}},"5b25":function(e,t,r){var n=r("37cd");var a=false;e.exports.attr=o;e.exports.index=i;function i(e,t,r,n){if(typeof r!=="number")r=1;if(typeof n!=="string")n="uint16";var a=!e.index&&typeof e.setIndex!=="function";var i=a?e.getAttribute("index"):e.index;var o=s(i,t,r,n);if(o){if(a)e.addAttribute("index",o);else e.index=o}}function o(e,t,r,n,a){if(typeof n!=="number")n=3;if(typeof a!=="string")a="float32";if(Array.isArray(r)&&Array.isArray(r[0])&&r[0].length!==n){throw new Error("Nested vertex array has unexpected size; expected "+n+" but found "+r[0].length)}var i=e.getAttribute(t);var o=s(i,r,n,a);if(o){e.addAttribute(t,o)}}function s(e,t,r,i){t=t||[];if(!e||l(e,t,r)){t=n(t,i);var o=e&&typeof e.setArray!=="function";if(!e||o){if(o&&!a){a=true;console.warn(["A WebGL buffer is being updated with a new size or itemSize, ","however this version of ThreeJS only supports fixed-size buffers.","\nThe old buffer may still be kept in memory.\n","To avoid memory leaks, it is recommended that you dispose ","your geometries and create new ones, or update to ThreeJS r82 or newer.\n","See here for discussion:\n","https://github.com/mrdoob/three.js/pull/9631"].join(""))}e=new THREE.BufferAttribute(t,r)}e.itemSize=r;e.needsUpdate=true;if(typeof e.setArray==="function"){e.setArray(t)}return e}else{n(t,e.array);e.needsUpdate=true;return null}}function l(e,t,r){if(e.itemSize!==r)return true;if(!e.array)return true;var n=e.array.length;if(Array.isArray(t)&&Array.isArray(t[0])){return n!==t.length*r}else{return n!==t.length}return false}},"60f6":function(e,t,r){r("d110")("Float32",4,function(e){return function t(r,n,a){return e(this,r,n,a)}})},6418:function(e,t){var r=2;var n={min:[0,0],max:[0,0]};function a(e){var t=e.length/r;n.min[0]=e[0];n.min[1]=e[1];n.max[0]=e[0];n.max[1]=e[1];for(var a=0;a<t;a++){var i=e[a*r+0];var o=e[a*r+1];n.min[0]=Math.min(i,n.min[0]);n.min[1]=Math.min(o,n.min[1]);n.max[0]=Math.max(i,n.max[0]);n.max[1]=Math.max(o,n.max[1])}}e.exports.computeBox=function(e,t){a(e);t.min.set(n.min[0],n.min[1],0);t.max.set(n.max[0],n.max[1],0)};e.exports.computeSphere=function(e,t){a(e);var r=n.min[0];var i=n.min[1];var o=n.max[0];var s=n.max[1];var l=o-r;var c=s-i;var u=Math.sqrt(l*l+c*c);t.center.set(r+l/2,i+c/2,0);t.radius=u/2}},6444:function(e,t,r){var n=r("ca9f"),a=r("d024"),i=function(e){return Object.prototype.toString.call(e)==="[object Array]"};e.exports=function(e){if(!e)return{};var t={};a(n(e).split("\n"),function(e){var r=e.indexOf(":"),a=n(e.slice(0,r)).toLowerCase(),o=n(e.slice(r+1));if(typeof t[a]==="undefined"){t[a]=o}else if(i(t[a])){t[a].push(o)}else{t[a]=[t[a],o]}});return t}},"688e":function(e,t,r){"use strict";var n="Function.prototype.bind called on incompatible ";var a=Array.prototype.slice;var i=Object.prototype.toString;var o="[object Function]";e.exports=function e(t){var r=this;if(typeof r!=="function"||i.call(r)!==o){throw new TypeError(n+r)}var s=a.call(arguments,1);var l;var c=function(){if(this instanceof l){var e=r.apply(this,s.concat(a.call(arguments)));if(Object(e)===e){return e}return this}else{return r.apply(t,s.concat(a.call(arguments)))}};var u=Math.max(0,r.length-s.length);var d=[];for(var f=0;f<u;f++){d.push("$"+f)}l=Function("binder","return function ("+d.join(",")+"){ return binder.apply(this,arguments); }")(c);if(r.prototype){var p=function e(){};p.prototype=r.prototype;l.prototype=new p;p.prototype=null}return l}},"6fa4":function(e,t,r){},"732b":function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("t-card",{attrs:{name:"Component Set"}},[r("div",{staticClass:"_",attrs:{slot:"options"},slot:"options"},[r("q-list",[r("q-item",[r("t-rpc-action",{attrs:{source:"component_"+e.comp.id,buttonText:"Duplicate",methodName:"duplicate",withInput:"withInput",promptTitle:"Copy component",promptMessage:"Enter new names"}})],1),r("q-item",[r("q-btn",{attrs:{icon:"delete",label:"Delete",color:"red"},on:{click:e.remove}})],1)],1)],1),r("div",{staticClass:"_",attrs:{slot:"links"},slot:"links"},[r("div",{staticClass:"_ display-inline"},[r("a",{attrs:{href:"/admin/webdesigner/componentset/"+e.comp.id+"/change/"}},[r("q-btn",{attrs:{icon:"build",dense:"dense","text-color":"grey",size:"sm",color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Component ID:"+e._s(e.comp.id))])],1)],1)])]),r("div",{staticClass:"card"},[r("div",{staticClass:"card-main"},[r("q-tabs",{attrs:{"indicator-color":"transparent",dense:"dense","inline-label":"inline-label","active-color":"white"},model:{value:e.selectedPanel,callback:function(t){e.selectedPanel=t},expression:"selectedPanel"}},[r("q-tab",{attrs:{default:"default",label:"base",name:"mainTab",icon:"settings_input_component"}}),r("q-tab",{attrs:{label:"usage",name:"treeTab",icon:"call_split"}}),r("q-tab",{attrs:{label:"info",name:"infoTab",icon:"assignment"}})],1),r("q-tab-panels",{model:{value:e.selectedPanel,callback:function(t){e.selectedPanel=t},expression:"selectedPanel"}},[r("q-tab-panel",{attrs:{name:"mainTab"}},[r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.comp,neverEmpty:"neverEmpty","target-field":"name","allow-arbitraty-constat":true,"section-label":"Name",info:"Name"},on:{save:e.apiUpdateComponentName}})],1)]),r("div",{staticClass:"row"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.comp,neverEmpty:"neverEmpty","target-field":"dim_x","allow-arbitraty-constat":true,"section-label":"dim_x",info:"dim_x","update-model":"update-model"},on:{save:function(t){return e.apiUpdateComponent("dim_x",t)}}})],1)])]),r("q-tab-panel",{attrs:{name:"treeTab"}},[r("q-tree",{attrs:{nodes:e.comp.tree,dark:"dark","node-key":"label","default-expand-all":"default-expand-all"}})],1),r("q-tab-panel",{attrs:{name:"infoTab"}},[r("pre",[e._v(e._s(e.comp.info.info))])])],1)],1),r("div",{staticClass:"card-main q-pl-sm margin-b"},[r("t-tags",{attrs:{"target-model":e.comp,"target-field":"additional_params","section-label":"Additional parameters","update-model":"update-model"},on:{save:function(t){return e.broadcastNewParameters(e.comp.additional_params,"additional_params")}}})],1)])])};var a=[];var i=r("9523");var o=r.n(i);var s=r("18a5");var l=r("39fc");var c={props:{comp:{type:Object,default:function e(){return{id:-1,additional_params:[]}}}},components:{"t-rpc-action":l["d"],"t-presets":l["c"],"t-input":l["b"],"t-card":l["a"],"t-sections":l["f"],"t-section":l["e"],"t-tags":l["i"]},watch:{comp:function e(){this.paramsNotSet=[]}},methods:{apiUpdateComponentName:function e(t){var r=this;s["a"].api.updateComponent(this.comp.id,{name:t}).subscribe(function(){return r.$emit("newParameters")})},apiUpdateComponent:function e(t,r,n){var a=this;s["a"].api.updateComponent(this.comp.id,o()({},t,r)).subscribe(n?n:function(){return a.$emit("newParameters")})},broadcastNewParameters:function e(t){var r=this;s["a"].api.updateComponent(this.comp.id,{additional_params:t}).subscribe(function(){r.$emit("newParameters")})},remove:function e(){var t=this;this.$q.dialog({title:"Delete",message:"This action will delete comp",ok:"Ok",cancel:"Cancel"}).onOk(function(){t.$emit("delete",true);TylkoNotifications.notify(t.$q,"Deleted!")})}},data:function e(){return{selectedPanel:"mainTab",name:"",paramsNotSet:[],newHeight:0}}};var u=c;var d=r("ac17");var f=r("2877");var p=Object(f["a"])(u,n,a,false,null,null,null);var h=t["a"]=p.exports},7708:function(e,t,r){var n=r("d138");var a={ease:n(.25,.1,.25,1),easeIn:n(.42,0,1,1),easeOut:n(0,0,.58,1),easeInOut:n(.42,0,.58,1),linear:n(0,0,1,1)};e.exports=i;e.exports.makeAggregateRaf=u;e.exports.sharedScheduler=u();function i(e,t,r){var n=Object.create(null);var i=Object.create(null);r=r||{};var l=typeof r.easing==="function"?r.easing:a[r.easing];if(!l){if(r.easing){console.warn("Unknown easing function in amator: "+r.easing)}l=a.ease}var c=typeof r.step==="function"?r.step:o;var u=typeof r.done==="function"?r.done:o;var d=s(r.scheduler);var f=Object.keys(t);f.forEach(function(r){n[r]=e[r];i[r]=t[r]-e[r]});var p=typeof r.duration==="number"?r.duration:400;var h=Math.max(1,p*.06);var v;var m=0;v=d.next(b);return{cancel:g};function g(){d.cancel(v);v=0}function b(){var t=l(m/h);m+=1;y(t);if(m<=h){v=d.next(b);c(e)}else{v=0;setTimeout(function(){u(e)},0)}}function y(t){f.forEach(function(r){e[r]=i[r]*t+n[r]})}}function o(){}function s(e){if(!e){var t=typeof window!=="undefined"&&window.requestAnimationFrame;return t?l():c()}if(typeof e.next!=="function")throw new Error("Scheduler is supposed to have next(cb) function");if(typeof e.cancel!=="function")throw new Error("Scheduler is supposed to have cancel(handle) function");return e}function l(){return{next:window.requestAnimationFrame.bind(window),cancel:window.cancelAnimationFrame.bind(window)}}function c(){return{next:function(e){return setTimeout(e,1e3/60)},cancel:function(e){return clearTimeout(e)}}}function u(){var e=new Set;var t=new Set;var r=0;return{next:a,cancel:a,clearAll:n};function n(){e.clear();t.clear();cancelAnimationFrame(r);r=0}function a(e){t.add(e);i()}function i(){if(!r)r=requestAnimationFrame(o)}function o(){r=0;var n=t;t=e;e=n;e.forEach(function(e){e()});e.clear()}function s(e){t.delete(e)}}},7831:function(e,t){e.exports=function(e){switch(e){case"int8":return Int8Array;case"int16":return Int16Array;case"int32":return Int32Array;case"uint8":return Uint8Array;case"uint16":return Uint16Array;case"uint32":return Uint32Array;case"float32":return Float32Array;case"float64":return Float64Array;case"array":return Array;case"uint8_clamped":return Uint8ClampedArray}}},"784b":function(e,t,r){r("d110")("Uint16",2,function(e){return function t(r,n,a){return e(this,r,n,a)}})},"78f9":function(e,t,r){"use strict";var n=r("b8c9");var a=r.n(n);var i=a.a},"7a69":function(e,t,r){var n=r("f202");var a=r("3fb5");var i=r("88a0");var o=r("5b25");var s=r("320c");var l=r("a3a2");var c=r("6418");var u=THREE.BufferGeometry;e.exports=function e(t){return new d(t)};function d(e){u.call(this);if(typeof e==="string"){e={text:e}}this._opt=s({},e);if(e)this.update(e)}a(d,u);d.prototype.update=function(e){if(typeof e==="string"){e={text:e}}e=s({},this._opt,e);if(!e.font){throw new TypeError("must specify a { font } in options")}this.layout=n(e);var t=e.flipY!==false;var r=e.font;var a=r.common.scaleW;var c=r.common.scaleH;var u=this.layout.glyphs.filter(function(e){var t=e.data;return t.width*t.height>0});this.visibleGlyphs=u;var d=l.positions(u);var f=l.uvs(u,a,c,t);var p=i({clockwise:true,type:"uint16",count:u.length});o.index(this,p,1,"uint16");o.attr(this,"position",d,2);o.attr(this,"uv",f,2);if(!e.multipage&&"page"in this.attributes){this.removeAttribute("page")}else if(e.multipage){var h=l.pages(u);o.attr(this,"page",h,1)}};d.prototype.computeBoundingSphere=function(){if(this.boundingSphere===null){this.boundingSphere=new THREE.Sphere}var e=this.attributes.position.array;var t=this.attributes.position.itemSize;if(!e||!t||e.length<2){this.boundingSphere.radius=0;this.boundingSphere.center.set(0,0,0);return}c.computeSphere(e,this.boundingSphere);if(isNaN(this.boundingSphere.radius)){console.error("THREE.BufferGeometry.computeBoundingSphere(): "+"Computed radius is NaN. The "+'"position" attribute is likely to have NaN values.')}};d.prototype.computeBoundingBox=function(){if(this.boundingBox===null){this.boundingBox=new THREE.Box3}var e=this.boundingBox;var t=this.attributes.position.array;var r=this.attributes.position.itemSize;if(!t||!r||t.length<2){e.makeEmpty();return}c.computeBox(t,e)}},"7a87":function(e,t,r){var n=r("b639").Buffer;e.exports=function(e,t){if(!n.isBuffer(e))return undefined;if(!n.isBuffer(t))return undefined;if(typeof e.equals==="function")return e.equals(t);if(e.length!==t.length)return false;for(var r=0;r<e.length;r++){if(e[r]!==t[r])return false}return true}},"7b2c":function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("section",{staticClass:"renderer",class:e.mode==0||e.mode==1?"":"hide-canvas"},[r("div",{staticClass:"row mainRow"},[r("div",{staticClass:"col tool-column"},[r("div",[r("div",{staticClass:"tool-entry settingsTool",on:{click:function(t){t.stopPropagation();t.preventDefault();return e.activeToolEntry(t)}}},[r("div",{staticClass:"icon"},[r("q-icon",{attrs:{name:"tune",color:"white"}}),r("div",{staticClass:"action special scroll"},[r("t-card",{attrs:{name:"Settings"}},[r("div",{staticClass:"card"},[r("div",{staticClass:"card-main"},[r("div",{staticClass:"card-content"},[e.meshMode?r("div",{staticClass:"_"},[r("t-select",{attrs:{"target-model":e.height,neverEmpty:"neverEmpty",options:e.parsedHeights,"section-label":"Height","update-model":"update-model"},on:{save:function(t){return e.height=t.value}}})],1):e._e(),r("div",{staticClass:"_"},[r("q-checkbox",{attrs:{label:"Freeze setup",dark:"dark"},on:{input:e.freeSetupToggle},model:{value:e.freeSetup,callback:function(t){e.freeSetup=t},expression:"freeSetup"}})],1),e.element?r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.model,neverEmpty:"neverEmpty","target-field":"choosenWidth","section-label":"Width","update-model":"update-model",deep:"deep"},on:{save:e.switchSetup}}),r("q-slider",{staticClass:"col-7",attrs:{dark:"dark",min:parseInt(e.element.dim_x.split("-")[0]),max:parseInt(e.element.dim_x.split("-")[1]),square:"square"},on:{input:e.switchSetup},model:{value:e.model.choosenWidth,callback:function(t){e.$set(e.model,"choosenWidth",t)},expression:"model.choosenWidth"}})],1):e._e()])])])])],1)],1)]),r("div",{staticClass:"tool-entry settingsTool",on:{click:function(t){t.stopPropagation();t.preventDefault();return e.activeToolEntry(t)}}},[r("div",{staticClass:"icon"},[r("q-icon",{attrs:{name:"videocam",color:"white"}}),r("div",{staticClass:"action"},[r("q-btn",{staticClass:"no-shadow specialbtn",attrs:{color:"black",size:"md",label:"Reset"},on:{click:function(t){return e.resetCamera()}}}),r("q-btn",{staticClass:"no-shadow specialbtn",attrs:{color:"black",size:"md",label:"Front"}}),r("q-field",{attrs:{label:"Camera lock"}},[r("q-toggle",{on:{input:e.handleCamera},model:{value:e.cameraLock,callback:function(t){e.cameraLock=t},expression:"cameraLock"}})],1),e.mode==0?r("q-field",{attrs:{label:"Render solid"}},[r("q-toggle",{attrs:{color:"grey-8"},model:{value:e.solidMode,callback:function(t){e.solidMode=t},expression:"solidMode"}})],1):e._e(),e.mode==1?r("q-field",{attrs:{label:"Colors"}},[r("q-select",{attrs:{dark:"dark",radio:"radio","emit-value":"emit-value",options:e.colors},model:{value:e.model.choosenColor,callback:function(t){e.$set(e.model,"choosenColor",t)},expression:"model.choosenColor"}})],1):e._e(),e.mode==1?r("q-field",{attrs:{label:"Select elements"}},[r("q-select",{attrs:{type:"checkbox",dark:"dark",radio:"radio",options:[{label:"Normal view",value:"all"},{label:"Without fronts",value:"without_fronts"},{label:"Opened drawers and doors",value:"all_opened"}]},model:{value:e.elements,callback:function(t){e.elements=t},expression:"elements"}})],1):e._e()],1)],1)]),r("div",{staticClass:"tool-entry"},[r("div",{staticClass:"icon"},[r("q-icon",{attrs:{name:"save_alt",color:"white"}}),r("div",{staticClass:"action"},[r("q-btn",{attrs:{dark:"dark",color:"black",size:"md",label:"SVG"},on:{click:function(t){return e.getFile("svg")}}}),r("q-btn",{attrs:{dark:"dark",color:"black",size:"md",label:"DXF"},on:{click:function(t){return e.getFile("dxf")}}}),this.mode<2?r("q-btn",{attrs:{dark:"dark",color:"black",size:"md",label:"OBJ"},on:{click:function(t){return e.getFile("obj")}}}):e._e()],1)],1)]),e._m(0),e._l(e.modesForView,function(t){return r("div",{key:t.label,staticClass:"tool-entry",attrs:{name:t.value},on:{click:function(r){return e.changeMode(t.value)}}},[r("div",{staticClass:"icon"},[r("q-icon",{attrs:{name:t.icon,color:"white"}}),r("div",{staticClass:"action"},[r("p",{staticStyle:{color:"white"}},[e._v(e._s(t.label))])])],1)])})],2)]),r("div",{staticClass:"col"},[10<e.mode?r("div",{staticClass:"svgContainer"},[r("div",{class:10<e.mode?"svg show":"svg",staticStyle:{"z-index":"-1"},attrs:{id:"svg"}})]):e._e(),r("div",{staticClass:"production-renderer-canvas",class:e.loading?"opaque":""}),r("div",{staticClass:"preview-spinner"},[e.loading&&e.mode==0?r("q-spinner",{attrs:{color:"white",size:"30"}}):e._e(),e.loading&&e.mode!=0?r("q-spinner",{attrs:{color:"white",size:"30"}}):e._e()],1)])])])};var a=[function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticStyle:{"margin-top":"25px"}},[r("span",[e._v("-")])])}];var i=r("7813");var o=r("a34a");var s=r.n(o);var l=r("192a");var c=r("c973");var u=r.n(c);var d=r("5a51");var f=r("f994");var p=r("e3f9");var h=r("3156");var v=r.n(h);var m=r("d6b6");var g=r("9ec3");var b=r.n(g);var y=r("2f62");var w=r("85a6");var x=r("1197");var _=r("ce62");var C=r("39fc");var S=r("18a5");var k=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"component-select"},e._l(e.configuration,function(t,n,a){return r("div",[e.optionsError(t)&&!t.options[0].value?r("q-field",{staticClass:"select",attrs:{label:"m_config_id ("+t.m_config_id+")"}},[r("p",[e._v(e._s(t.options[0].label))])]):e._e(),e.optionsError(t)?r("div",{staticClass:"row"},[r("q-field",{staticClass:"col-1 q-mt-sm"},[r("p",[e._v("C-"+e._s(n+1))])]),t.options[0].value?r("q-field",{staticClass:"col-4 q-mt-sm",attrs:{label:undefined,"label-width":"3"}},[r("q-radio",{attrs:{val:"left",label:"left"},on:{input:e.dispatchToConfiguration},model:{value:t.door_flip,callback:function(r){e.$set(t,"door_flip",r)},expression:"config.door_flip"}}),r("q-radio",{attrs:{val:"right",label:"right"},on:{input:e.dispatchToConfiguration},model:{value:t.door_flip,callback:function(r){e.$set(t,"door_flip",r)},expression:"config.door_flip"}})],1):e._e(),e.optionsError(t)?r("q-field",{staticClass:"col-7 select"},[t.options?r("q-select",{attrs:{options:t.options},on:{input:e.dispatchToConfiguration},model:{value:t.series_id,callback:function(r){e.$set(t,"series_id",r)},expression:"config.series_id"}}):e._e()],1):e._e()],1):e._e()],1)}),0)};var A=[];var E=r("9af0");var P=r("4082");var M=r.n(P);var T=r("9523");var I=r.n(T);var O=r("359c");var D={name:"Tylko-Component-Select",props:["setupID","components","height"],methods:{optionsError:function e(t){if(b.a.has(t,"options")){return true}else{return false}},dispatchToConfiguration:function e(){var t={};this.configuration.forEach(function(e){var r=e.m_config_id,n=e.door_flip,a=e.series_id;t=v()({},t,I()({},r,{door_flip:n,series_id:a}))});S["a"].application.bus.$emit("dispatchConfiguratorCustomParams",I()({},this.setupID,t))},processGeometry:function e(t){var r=t.configurator_data,n=r.table,a=r.component;var i={};for(var o in n){if(n.hasOwnProperty(o)){i=v()({},i,I()({},o,n[o][this.height]?n[o][this.height].map(function(e){var t=e.component_id,r=e.series_id;return v()({series_id:r},a[t])}):[{name:"empty",series_id:null}]))}}this.configuration=this.configuration.map(function(e){var t=e.table_id,r=M()(e,["table_id"]);return v()({},r,{options:i[t].map(function(e){var t=e.name,r=e.series_id,n=M()(e,["name","series_id"]);return v()({label:t,value:r},n)})})})}},watch:{components:function e(t){this.configuration=t.map(function(e){var t=e.m_config_id,r=e.door_flip,n=e.table_id,a=e.series_id;return{m_config_id:t,door_flip:r,table_id:n,series_id:a}});this.height=this.height.replace(/\s/g,"");S["a"].api.geo.mesh({type:"mesh",id:this.$route.params.id}).pipe(this.processGeometry,"preview")}},data:function e(){return{configuration:[]}}};var R=D;var F=r("ce2d");var $=r("2877");var B=Object($["a"])(R,k,A,false,null,null,null);var j=B.exports;var N=r("7d05");var L={props:{element:Object,mesh:Object,decoderType:[String,Number],decoderId:[String,Number],specialSettings:Array,decoderWidth:[String,Number],decoderHeight:[String,Number],vwidth:[String,Number],vheight:[String,Number],meshMode:[Boolean,String],decoderHeights:Array,lightup:Number,switchSetupTab:Function,selectedSetup:[Number,String],freeSetup:[Boolean,String],menu:[Boolean,String],distortionMode:[Boolean,String],setup:{type:[Number,String],default:null},distortion:{type:Number,default:100},density:{type:Number,default:100}},name:"Tylko-Designer-Renderer-View",components:v()({TylkoComponentSelect:j},C["j"]),computed:v()({},Object(y["c"])({densityMode:"renderer/densityMode",setupModel:"renderer/setupModel"}),{height:{get:function e(){return this.$store.state.renderer.height},set:function e(t){this.$store.dispatch("renderer/updateHeight",t)}},modesForView:function e(){var t=this.meshMode?"mesh":"component";return b.a.filter(this.modes,function(e){return!e.hasOwnProperty("availability")||e.availability.includes(t)})},parsedHeights:function e(){return this.parseHeights(this.decoderHeights)}}),data:function e(){return{configuratorCustomParams:null,forceReload:false,currentGeometry:null,checked:false,mode:"0",solidMode:false,selected:null,loading:false,model:{choosenWidth:0,choosenColor:"0:0",choosenLocalWidth:70},lastJson:null,cameraLock:false,svgDownloadLink:"",dxfDownloadLink:"",svgDownloadLinkName:"",dxfDownloadLinkName:"",setupID:null,components:[],elements:"all",colors:[{label:"T01 - white",value:"0:0"},{label:"T01 - black",value:"0:1"},{label:"T01 - grey",value:"0:3"},{label:"T01 - abuergine",value:"0:4"},{label:"T01 - fornir",value:"0:5"},{label:"T02 - white",value:"1:0"},{label:"T02 - color2",value:"1:1"},{label:"T02 - color3",value:"1:2"},{label:"T02 - color4",value:"1:3"},{label:"T02 - color5",value:"1:4"}],modes:[{label:"3D Wireframe",value:"0",icon:"3d_rotation"},{label:"3D Renderer",value:"1",icon:"photo_camera"},{label:"2D",value:"11",icon:"portrait"},{label:"Dimensions",value:"12",icon:"straighten"},{label:"Components",value:"13",icon:"border_all",availability:["mesh"]},{label:"Catalog",value:"14",icon:"view_comfy",availability:["mesh"]},{label:"Choices",value:"15",icon:"call_split",availability:["mesh"]},{label:"Channels",value:"16",icon:"swap_calls",availability:["mesh"]},{label:"Setups",value:"17",icon:"sort",availability:["mesh"]},{label:"Distortion & Density",value:"18",icon:"shuffle",availability:["mesh"]}]}},watch:{setupModel:function e(t){console.log(123,t);if(this.ready)this.updateDebounced()},density:function e(t){S["a"].application.settings.setObjectItem("densityStore",this.$route.params.id,t);this.updateDebounced()},distortion:function e(t){S["a"].application.settings.setObjectItem("distortionStore",this.$route.params.id,t);this.updateDebounced()},freeSetup:function e(t){var r=this;if(this.meshMode&&!t){var n=this.$route.params.id;S["a"].api.mesh(n).subscribe(function(e){r.changeElementWidth(e.setups.filter(function(e){return e.id==r.$route.params.setWidth})[0].dim)})}},mode:function e(){var t=this;if(this.mode==0){this.designerRenderer.then(function(e){e.clearScene();t.previewRenderer.resetScene()})}if(this.mode==1){this.previewRenderer.resetScene(true)}this.updateDebounced()},decoderWidth:function e(){this.updateDebounced()},decoderHeight:function e(){this.updateDebounced()},specialSettings:function e(){this.updateDebounced()},decoderId:function e(){this.updateDebounced()},decoderType:function e(){this.updateDebounced()},model:{handler:function e(){this.updateDebounced()},deep:true},height:function e(){this.updateDebounced()},elements:function e(){if(this.elements=="all"){this.designerRenderer.then(function(e){return e.setDesignerMode(1)})}else if(this.elements=="without_fronts"){this.designerRenderer.then(function(e){return e.setDesignerMode(2)})}else if(this.elements=="all_opened"){this.designerRenderer.then(function(e){return e.setDesignerMode(3)})}this.updateDebounced()},element:function e(){if(!this.meshMode){this.model.choosenWidth=this.element.dim_x.split("-").map(function(e){return+e}).reduce(function(e,t){return e+(t-e)/2})}},solidMode:function e(){if(this.ready)this.updateDebounced()},lightup:function e(){if(this.previewRenderer){this.previewRenderer.selectElement(this.lightup)}},"model.choosenColor":function e(){var t=this;this.designerRenderer.then(function(e){var r=Object(_["getGeometry"])({serialization:t.lastJson},{width:t.element?t.model.choosenWidth:t.decoderWidth,height:t.meshMode?t.height:null,depth:320,mesh_setup:!t.freeSetup&&t.meshMode?null:t.selectedSetup,distortion:t.meshMode?t.distortion:null,density:t.meshMode?t.density:null,geom_id:t.meshMode?t.decoderId:t.$route.params.setHeight,geom_type:t.decoderType==="component-set"?"component":t.decoderType},{format:"gallery"});e.displayShelf(r,t.model.choosenColor,t.previewRenderer.scene,t.previewRenderer.camera,t.previewRenderer.renderer)})}},methods:{activeToolEntry:function e(t){if(t.target.classList.contains("tool-entry")||t.target.classList.contains("q-icon")){t.currentTarget.classList.toggle("active")}},freeSetupToggle:function e(t){this.$emit("setFreeSetup",t)},changeElementWidth:function e(t){this.setup=t;if(!this.freeSetup){this.model.choosenWidth=t;this.update()}},saveResult:function e(t,r){console.log(t,r)},switchSetup:function e(t){var r=this;if(this.meshMode){var n=this.$route.params.id;S["a"].api.mesh(n).subscribe(function(e){r.setup=e.setups.filter(function(e){return e.id==r.$route.params.setWidth})[0].dim});if(!this.freeSetup){setTimeout(function(){r.$emit("switchSetupTab",t)},1e3)}}},resetCamera:function e(){this.previewRenderer.resetCamera()},parseHeights:function e(t){return t.map(function(e){return{label:e,value:e}})},changeMode:function e(t){this.mode=t},setMesh:function e(t,r){this.decoderId=t;this.decoderWidth=parseInt(r)},getFile:function e(t){console.log("calling api.file, geo: ",this.currentGeometry?true:false);var r=Object(_["getGeometryForPostPayload"])({serialization:this.currentGeometry},{width:this.element?this.model.choosenWidth:this.decoderWidth,height:this.meshMode?this.height:null,depth:320,mesh_setup:!this.freeSetup&&this.meshMode?null:this.selectedSetup,geom_id:this.meshMode?this.decoderId:this.$route.params.setHeight,geom_type:this.decoderType==="component-set"?"component":this.decoderType,distortion:this.meshMode?this.distortion:null,density:this.meshMode?this.density:null},this.mode);S["a"].api.file({fileType:t,geometry:JSON.stringify(r)}).then(function(){var e=u()(s.a.mark(function e(t){var r,n,a,i;return s.a.wrap(function e(o){while(1){switch(o.prev=o.next){case 0:o.next=2;return t.data;case 2:r=o.sent;n=new Blob([r]);a=t.filename;i=document.createElement("a");i.href=window.URL.createObjectURL(n);i.download="".concat(a.substr(a.indexOf("=")+1,a.length));i.click();case 9:case"end":return o.stop()}}},e)}));return function(t){return e.apply(this,arguments)}}())},drawSVG:function e(){var t=this;var r=function e(){var t=document.getElementById("svg").querySelector("svg");var r=t.getBBox(),n=r.x,a=r.width,i=r.height;t.style.width="100%";t.style.height="600px"};var n=Object(_["getGeometryForPostPayload"])({serialization:this.currentGeometry},{width:this.element?this.model.choosenWidth:this.decoderWidth,height:this.meshMode?this.height:null,depth:320,mesh_setup:!this.freeSetup&&this.meshMode?null:this.selectedSetup,geom_id:this.meshMode?this.decoderId:this.$route.params.setHeight,geom_type:this.decoderType==="component-set"?"component":this.decoderType,distortion:this.meshMode?this.distortion:null,density:this.meshMode?this.density:null},this.mode);S["a"].api.draw({geometry:JSON.stringify(n),mode:this.mode}).then(function(e){if(t.panzoomInstance)t.panzoomInstance.dispose();document.getElementById("svg").innerHTML=e.replace(/\\\"/g,'"');r();t.panzoomInstance=N(document.getElementById("svg"))})},processGeometry:function e(t){var r=this;console.log("geoRequest",t);if(t==null){return}this.currentGeometry=t;if(this.mode==0)this.lastJson=t.data;this.loading=false;switch(parseInt(this.mode)){case 0:var n=Object(_["getGeometry"])({serialization:t},{width:this.element?this.model.choosenWidth:this.decoderWidth,height:this.meshMode?this.height:this.decoderHeight,depth:320,mesh_setup:!this.freeSetup&&this.meshMode?null:this.selectedSetup,geom_id:this.meshMode?this.decoderId:this.$route.params.setHeight,geom_type:this.decoderType==="component-set"?"component":this.decoderType,distortion:this.meshMode?this.distortion:null,density:this.meshMode?this.density:null,configurator_custom_params:this.configuratorCustomParams?this.configuratorCustomParams:null},{format:"wireframe"});if(!this.freeSetup&&this.meshMode){this.$emit("selectSetupByDecoder",n.item.setup_id)}console.log("###",n);this.setupID=n.item.setup_id;this.components=n.item.components;this.previewRenderer.create(n,false,this.solidMode);break;case 1:this.designerRenderer.then(function(e){var n=Object(_["getGeometry"])({serialization:t},{width:r.element?r.model.choosenWidth:r.decoderWidth,height:r.meshMode?r.height:null,depth:320,mesh_setup:!r.freeSetup&&r.meshMode?null:r.selectedSetup,geom_id:r.meshMode?r.decoderId:r.$route.params.setHeight,geom_type:r.decoderType==="component-set"?"component":r.decoderType,distortion:r.meshMode?r.distortion:null,density:r.meshMode?r.density:null,configurator_custom_params:r.configuratorCustomParams?r.configuratorCustomParams:null},{format:"gallery"});r.lastJson=t;e.setScene(r.previewRenderer.scene);e.clearScene(r.previewRenderer.scene);e.displayShelf(n,r.model.choosenColor,r.previewRenderer.scene,r.previewRenderer.camera,r.previewRenderer.renderer);r.currentGeometry=n;if(!r.freeSetup&&r.meshMode){r.$emit("selectSetupByDecoder",n.item.setup_id)}});break;default:this.drawSVG();break}},update:function e(){if(!this.previewRenderer)return;this.previewRenderer.flush();this.loading=true;if(this.mode==0){this.previewRenderer.scene.opacity=.5}if(this.meshMode){var t=this.$route.params.id;this.distortion=S["a"].application.settings.get("distortionStore")[t]||100;this.density=S["a"].application.settings.get("densityStore")[t]||100}S["a"].api.geo.componentSet({type:this.decoderType,id:this.$route.params.id,width:this.element?this.model.choosenWidth:this.decoderWidth,height:this.meshMode?this.height:null,mode:parseInt(this.mode),elements:this.elements,special:this.specialSettings}).forceFetch(this.forceReload).pipe(this.processGeometry,"preview");this.forceReload=false},reload:function e(){this.forceReload=true;if(this.ready)this.updateDebounced()},updateDebounced:b.a.throttle(function(){this.update()},300),handleCamera:function e(t){S["a"].application.settings.set("configuratorCameraLock",t);this.initRenderer()()},initRenderer:function e(){if(this.$el.querySelector(".production-renderer-canvas").firstChild){this.$el.querySelector(".production-renderer-canvas").firstChild.remove()}this.designerRenderer=Object(x["a"])();var t=new w["a"]({});this.previewRenderer=t.set({container:this.$el.querySelector(".production-renderer-canvas"),width:this.vwidth,height:this.vheight,cameraLock:this.cameraLock});this.previewRenderer.resetScene();this.update();this.resize()}},updated:function e(){},created:function e(){},mounted:function e(){var t=this;if(this.meshMode){var r=this.$route.params.id;var n=S["a"].application.settings.get("distortionStore")[r];var a=S["a"].application.settings.get("densityStore")[r];if(n){this.distortion=n}if(a){this.density=a}S["a"].api.mesh(r).subscribe(function(e){t.changeElementWidth(e.setups.filter(function(e){return e.id==t.$route.params.setWidth})[0].dim)})}this.cameraLock=S["a"].application.settings.get("configuratorCameraLock");this.panzoomInstance=null;this.designerRenderer=Object(x["a"])();var i=new w["a"]({});this.previewRenderer=i.set({container:this.$el.querySelector(".production-renderer-canvas"),width:this.vwidth,height:this.vheight,cameraLock:this.cameraLock});this.previewRenderer.resetScene();this.resize=function(){var e=t.$el.getBoundingClientRect();t.previewRenderer.resize({width:e.width,height:e.height})};window.addEventListener("resize",function(e){var r=t.$el.getBoundingClientRect();t.previewRenderer.resize({width:r.width,height:r.height})});this.update();this.resize();if(this.element)this.model.choosenWidth=this.element.dim_x.split("-").map(function(e){return+e}).reduce(function(e,t){return e+(t-e)/2});if(this.meshMode)this.height=this.decoderHeights[0];this.ready=true;S["a"].application.bus.$on("dispatchConfiguratorCustomParams",function(e){t.configuratorCustomParams=e;t.updateDebounced()})}};var q=L;var z=r("ce89");var H=Object($["a"])(q,n,a,false,null,null,null);var W=t["a"]=H.exports},"7b3e":function(e,t,r){"use strict";var n=r("a3de");var a;if(n.canUseDOM){a=document.implementation&&document.implementation.hasFeature&&document.implementation.hasFeature("","")!==true}
/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @param {?boolean} capture Check if the capture phase is supported.
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */function i(e,t){if(!n.canUseDOM||t&&!("addEventListener"in document)){return false}var r="on"+e;var i=r in document;if(!i){var o=document.createElement("div");o.setAttribute(r,"return;");i=typeof o[r]==="function"}if(!i&&a&&e==="wheel"){i=document.implementation.hasFeature("Events.wheel","3.0")}return i}e.exports=i},"7d05":function(e,t,r){"use strict";var n=r("7d67");var a=r("7708");var i=r("e9ff");var o=r("c22a");var s=r("b8a5")();var l=r("408b");var c=r("c327");var u=r("5647");var d=.065;var f=1.75;var p=300;e.exports=h;function h(e,t){t=t||{};var r=t.controller;if(!r){if(e instanceof SVGElement){r=c(e)}if(e instanceof HTMLElement){r=u(e)}}if(!r){throw new Error("Cannot create panzoom for the current type of dom element")}var h=r.getOwner();var g={x:0,y:0};var w=false;var x=new l;if(r.initTransform){r.initTransform(x)}var _=typeof t.filterKey==="function"?t.filterKey:v;var C=typeof t.realPinch==="boolean"?t.realPinch:false;var S=t.bounds;var k=typeof t.maxZoom==="number"?t.maxZoom:Number.POSITIVE_INFINITY;var A=typeof t.minZoom==="number"?t.minZoom:0;var E=typeof t.boundsPadding==="number"?t.boundsPadding:.05;var P=typeof t.zoomDoubleClickSpeed==="number"?t.zoomDoubleClickSpeed:f;var M=t.beforeWheel||v;var T=typeof t.zoomSpeed==="number"?t.zoomSpeed:d;m(S);if(t.autocenter){G()}var I;var O=0;var D=false;var R=false;var F;var $;var B;var j;if("smoothScroll"in t&&!t.smoothScroll){j=y()}else{j=i(Z,le,t.smoothScroll)}var N;var L;var q;var z=false;ue();return{dispose:ce,moveBy:se,moveTo:J,centerOn:oe,zoomTo:Oe,zoomAbs:ie,smoothZoom:Ie,getTransform:Y,showRectangle:X,pause:H,resume:W,isPaused:U};function H(){de();z=true}function W(){if(z){ue();z=false}}function U(){return z}function X(e){var t=V(h.clientWidth,h.clientHeight);var r=e.right-e.left;var n=e.bottom-e.top;if(!Number.isFinite(r)||!Number.isFinite(n)){throw new Error("Invalid rectangle")}var a=t.x/r;var i=t.y/n;var o=Math.min(a,i);x.x=-(e.left+r/2)*o+t.x/2;x.y=-(e.top+n/2)*o+t.y/2;x.scale=o}function V(e,t){if(r.getScreenCTM){var n=r.getScreenCTM();var a=n.a;var i=n.d;var o=n.e;var s=n.f;g.x=e*a-o;g.y=t*i-s}else{g.x=e;g.y=t}return g}function G(){var e;var t;var n=0;var a=0;var i=ee();if(i){n=i.left;a=i.top;e=i.right-i.left;t=i.bottom-i.top}else{var o=h.getBoundingClientRect();e=o.width;t=o.height}var s=r.getBBox();if(s.width===0||s.height===0){return}var l=t/s.height;var c=e/s.width;var u=Math.min(c,l);x.x=-(s.left+s.width/2)*u+e/2+n;x.y=-(s.top+s.height/2)*u+t/2+a;x.scale=u}function Y(){return x}function Z(){return{x:x.x,y:x.y}}function J(e,t){x.x=e;x.y=t;Q();Be("pan");ne()}function K(e,t){J(x.x+e,x.y+t)}function Q(){var e=ee();if(!e)return;var t=false;var r=te();var n=e.left-r.right;if(n>0){x.x+=n;t=true}n=e.right-r.left;if(n<0){x.x+=n;t=true}n=e.top-r.bottom;if(n>0){x.y+=n;t=true}n=e.bottom-r.top;if(n<0){x.y+=n;t=true}return t}function ee(){if(!S)return;if(typeof S==="boolean"){var e=h.getBoundingClientRect();var t=e.width;var r=e.height;return{left:t*E,top:r*E,right:t*(1-E),bottom:r*(1-E)}}return S}function te(){var e=r.getBBox();var t=re(e.left,e.top);return{left:t.x,top:t.y,right:e.width*x.scale+t.x,bottom:e.height*x.scale+t.y}}function re(e,t){return{x:e*x.scale+x.x,y:t*x.scale+x.y}}function ne(){w=true;I=window.requestAnimationFrame(fe)}function ae(e,t,r){if(b(e)||b(t)||b(r)){throw new Error("zoom requires valid numbers")}var n=x.scale*r;if(n<A){if(x.scale===A)return;r=A/x.scale}if(n>k){if(x.scale===k)return;r=k/x.scale}var a=V(e,t);x.x=a.x-r*(a.x-x.x);x.y=a.y-r*(a.y-x.y);var i=Q();if(!i)x.scale*=r;Be("zoom");ne()}function ie(e,t,r){var n=r/x.scale;ae(e,t,n)}function oe(e){var t=e.ownerSVGElement;if(!t)throw new Error("ui element is required to be within the scene");var r=e.getBoundingClientRect();var n=r.left+r.width/2;var a=r.top+r.height/2;var i=t.getBoundingClientRect();var o=i.width/2-n;var s=i.height/2-a;se(o,s,true)}function se(e,t,r){if(!r){return K(e,t)}if(N)N.cancel();var n={x:0,y:0};var i={x:e,y:t};var o=0;var s=0;N=a(n,i,{step:function(e){K(e.x-o,e.y-s);o=e.x;s=e.y}})}function le(e,t){De();J(e,t)}function ce(){de()}function ue(){h.addEventListener("mousedown",Se);h.addEventListener("dblclick",Ce);h.addEventListener("touchstart",ve);h.addEventListener("keydown",he);n.addWheelListener(h,Me);ne()}function de(){n.removeWheelListener(h,Me);h.removeEventListener("mousedown",Se);h.removeEventListener("keydown",he);h.removeEventListener("dblclick",Ce);h.removeEventListener("touchstart",ve);if(I){window.cancelAnimationFrame(I);I=0}j.cancel();Ee();Pe();$e()}function fe(){if(w)pe()}function pe(){w=false;r.applyTransform(x);Be("transform");I=0}function he(e){var t=0,r=0,n=0;if(e.keyCode===38){r=1}else if(e.keyCode===40){r=-1}else if(e.keyCode===37){t=1}else if(e.keyCode===39){t=-1}else if(e.keyCode===189||e.keyCode===109){n=1}else if(e.keyCode===187||e.keyCode===107){n=-1}if(_(e,t,r,n)){return}if(t||r){e.preventDefault();e.stopPropagation();var a=h.getBoundingClientRect();var i=Math.min(a.width,a.height);var o=.05;var s=i*o*t;var l=i*o*r;se(s,l)}if(n){var c=Re(n);var u=h.getBoundingClientRect();Oe(u.width/2,u.height/2,c)}}function ve(e){me(e);if(e.touches.length===1){return be(e,e.touches[0])}else if(e.touches.length===2){B=_e(e.touches[0],e.touches[1]);q=true;ye()}}function me(e){if(t.onTouch&&!t.onTouch(e)){return}e.stopPropagation();e.preventDefault()}function ge(e){if(t.onDoubleClick&&!t.onDoubleClick(e)){return}e.preventDefault();e.stopPropagation()}function be(e){var t=e.touches[0];var r=Te(t);F=r.x;$=r.y;j.cancel();ye()}function ye(){if(!D){D=true;document.addEventListener("touchmove",we);document.addEventListener("touchend",xe);document.addEventListener("touchcancel",xe)}}function we(e){if(e.touches.length===1){e.stopPropagation();var t=e.touches[0];var r=Te(t);var n=r.x-F;var a=r.y-$;if(n!==0&&a!==0){Fe()}F=r.x;$=r.y;var i=V(n,a);se(i.x,i.y)}else if(e.touches.length===2){q=true;var o=e.touches[0];var s=e.touches[1];var l=_e(o,s);var c=1;if(C){c=l/B}else{var u=0;if(l<B){u=1}else if(l>B){u=-1}c=Re(u)}F=(o.clientX+s.clientX)/2;$=(o.clientY+s.clientY)/2;Oe(F,$,c);B=l;e.stopPropagation();e.preventDefault()}}function xe(e){if(e.touches.length>0){var t=Te(e.touches[0]);F=t.x;$=t.y}else{var r=new Date;if(r-O<p){Ie(F,$,P)}O=r;D=false;$e();Pe()}}function _e(e,t){return Math.sqrt((e.clientX-t.clientX)*(e.clientX-t.clientX)+(e.clientY-t.clientY)*(e.clientY-t.clientY))}function Ce(e){ge(e);var t=Te(e);Ie(t.x,t.y,P)}function Se(e){if(D){e.stopPropagation();return false}var t=e.button===1&&window.event!==null||e.button===0;if(!t)return;j.cancel();var r=Te(e);var n=V(r.x,r.y);F=n.x;$=n.y;document.addEventListener("mousemove",ke);document.addEventListener("mouseup",Ae);s.capture(e.target||e.srcElement);return false}function ke(e){if(D)return;Fe();var t=Te(e);var r=V(t.x,t.y);var n=r.x-F;var a=r.y-$;F=r.x;$=r.y;se(n,a)}function Ae(){s.release();$e();Ee()}function Ee(){document.removeEventListener("mousemove",ke);document.removeEventListener("mouseup",Ae);R=false}function Pe(){document.removeEventListener("touchmove",we);document.removeEventListener("touchend",xe);document.removeEventListener("touchcancel",xe);R=false;q=false}function Me(e){if(M(e))return;j.cancel();var t=Re(e.deltaY);if(t!==1){var r=Te(e);Oe(r.x,r.y,t);e.preventDefault()}}function Te(e){var t,r;var n=h.getBoundingClientRect();t=e.clientX-n.left;r=e.clientY-n.top;return{x:t,y:r}}function Ie(e,t,r){var n=x.scale;var i={scale:n};var o={scale:r*n};j.cancel();De();Be("zoom");L=a(i,o,{step:function(r){ie(e,t,r.scale)}})}function Oe(e,t,r){j.cancel();De();return ae(e,t,r)}function De(){if(L){L.cancel();L=null}}function Re(e){var t=1;if(e>0){t=1-T}else if(e<0){t=1+T}return t}function Fe(){if(!R){Be("panstart");R=true;j.start()}}function $e(){if(R){if(!q)j.stop();Be("panend")}}function Be(t){var r=o(t);e.dispatchEvent(r)}}function v(){}function m(e){var t=typeof e;if(t==="undefined"||t==="boolean")return;var r=g(e.left)&&g(e.top)&&g(e.bottom)&&g(e.right);if(!r)throw new Error("Bounds object is not valid. It can be: "+"undefined, boolean (true|false) or an object {left, top, right, bottom}")}function g(e){return Number.isFinite(e)}function b(e){if(Number.isNaN){return Number.isNaN(e)}return e!==e}function y(){return{start:v,stop:v,cancel:v}}function w(){if(typeof document==="undefined")return;var e=document.getElementsByTagName("script");if(!e)return;var t;Array.from(e).forEach(function(e){if(e.src&&e.src.match(/\bpanzoom(\.min)?\.js/)){t=e}});if(!t)return;var r=t.getAttribute("query");if(!r)return;var n=t.getAttribute("name")||"pz";var a=Date.now();i();function i(){var e=document.querySelector(r);if(!e){var s=Date.now();var l=s-a;if(l<2e3){setTimeout(i,100);return}console.error("Cannot find the panzoom element",n);return}var c=o(t);console.log(c);window[n]=h(e,c)}function o(e){var t=e.attributes;var r={};for(var n=0;n<t.length;++n){var a=t[n];var i=s(a);if(i){r[i.name]=i.value}}return r}function s(e){if(!e.name)return;var t=e.name[0]==="p"&&e.name[1]==="z"&&e.name[2]==="-";if(!t)return;var r=e.name.substr(3);var n=JSON.parse(e.value);return{name:r,value:n}}}w()},"7d67":function(e,t){e.exports=o;e.exports.addWheelListener=o;e.exports.removeWheelListener=s;var r="",n,a,i;u(typeof window!=="undefined"&&window,typeof document!=="undefined"&&document);function o(e,t,r){l(e,i,t,r);if(i=="DOMMouseScroll"){l(e,"MozMousePixelScroll",t,r)}}function s(e,t,r){c(e,i,t,r);if(i=="DOMMouseScroll"){c(e,"MozMousePixelScroll",t,r)}}function l(e,t,a,o){e[n](r+t,i=="wheel"?a:function(e){!e&&(e=window.event);var t={originalEvent:e,target:e.target||e.srcElement,type:"wheel",deltaMode:e.type=="MozMousePixelScroll"?0:1,deltaX:0,deltaY:0,deltaZ:0,clientX:e.clientX,clientY:e.clientY,preventDefault:function(){e.preventDefault?e.preventDefault():e.returnValue=false},stopPropagation:function(){if(e.stopPropagation)e.stopPropagation()},stopImmediatePropagation:function(){if(e.stopImmediatePropagation)e.stopImmediatePropagation()}};if(i=="mousewheel"){t.deltaY=-1/40*e.wheelDelta;e.wheelDeltaX&&(t.deltaX=-1/40*e.wheelDeltaX)}else{t.deltaY=e.detail}return a(t)},o||false)}function c(e,t,n,i){e[a](r+t,n,i||false)}function u(e,t){if(e&&e.addEventListener){n="addEventListener";a="removeEventListener"}else{n="attachEvent";a="detachEvent";r="on"}if(t){i="onwheel"in t.createElement("div")?"wheel":t.onmousewheel!==undefined?"mousewheel":"DOMMouseScroll"}else{i="wheel"}}},"7ece":function(e,t){e.exports=function e(t){if(!t)throw new Error("no data provided");t=t.toString().trim();var n={pages:[],chars:[],kernings:[]};var a=t.split(/\r\n?|\n/g);if(a.length===0)throw new Error("no data in BMFont file");for(var i=0;i<a.length;i++){var o=r(a[i],i);if(!o)continue;if(o.key==="page"){if(typeof o.data.id!=="number")throw new Error("malformed file at line "+i+" -- needs page id=N");if(typeof o.data.file!=="string")throw new Error("malformed file at line "+i+' -- needs page file="path"');n.pages[o.data.id]=o.data.file}else if(o.key==="chars"||o.key==="kernings"){}else if(o.key==="char"){n.chars.push(o.data)}else if(o.key==="kerning"){n.kernings.push(o.data)}else{n[o.key]=o.data}}return n};function r(e,t){e=e.replace(/\t+/g," ").trim();if(!e)return null;var r=e.indexOf(" ");if(r===-1)throw new Error("no named row at line "+t);var a=e.substring(0,r);e=e.substring(r+1);e=e.replace(/letter=[\'\"]\S+[\'\"]/gi,"");e=e.split("=");e=e.map(function(e){return e.trim().match(/(".*?"|[^"\s]+)+(?=\s*|\s*$)/g)});var i=[];for(var o=0;o<e.length;o++){var s=e[o];if(o===0){i.push({key:s[0],data:""})}else if(o===e.length-1){i[i.length-1].data=n(s[0])}else{i[i.length-1].data=n(s[0]);i.push({key:s[1],data:""})}}var l={key:a,data:{}};i.forEach(function(e){l.data[e.key]=e.data});return l}function n(e){if(!e||e.length===0)return"";if(e.indexOf('"')===0||e.indexOf("'")===0)return e.substring(1,e.length-1);if(e.indexOf(",")!==-1)return a(e);return parseInt(e,10)}function a(e){return e.split(",").map(function(e){return parseInt(e,10)})}},"82ed":function(e,t,r){},8362:function(e,t){e.exports=n;var r=Object.prototype.toString;function n(e){var t=r.call(e);return t==="[object Function]"||typeof e==="function"&&t!=="[object RegExp]"||typeof window!=="undefined"&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)}},8445:function(e,t,r){var n=r("e288");var a=r("d7d0");e.exports=function(e){if(e===undefined)return 0;var t=n(e);var r=a(t);if(t!==r)throw RangeError("Wrong length!");return r}},"85a6":function(e,t,r){"use strict";var n=r("247b");var a=r("9b8e");var i=r("359c");var o=r("7341");var s=r("1adf");var l=r("6db0");var c=r("970b");var u=r.n(c);var d=r("5bc3");var f=r.n(d);var p=r("e411");var h=r("e1c2");var v=r("498f");var m=r.n(v);var g=r("1c33");var b=r.n(g);var y="precision mediump float;\n#define GLSLIFY 1\nvarying vec3 rayPosition;varying vec3 rayDirection;uniform vec4 shadowSource;uniform float shadowPower;uniform vec3 shelfSize;uniform vec4 idents[20];uniform float shelfGap;float a(float b,float c){return min(b,c);}vec2 a(vec2 b,vec2 c){return (b.x<c.x)?b:c;}float d(vec3 e,vec3 f){vec3 g=abs(e)-f;return min(max(g.x,max(g.y,g.z)),0.0)+length(max(g,0.0));}float h(vec2 i){return max(i.x,i.y);}float j(vec2 k,vec2 l){return h(abs(k)-l);}float m(float n,float l,float o){vec2 p=max(vec2(o+n,o+l),vec2(0));return min(-o,max(n,l))+length(p);}vec2 q(vec3 k){k.y-=shelfSize.y-20.;k.z-=shelfSize.z;float r=0.;vec3 s=shelfSize;s.y-=20.;float t=d(k+vec3(0,0,0),s);float u=dot(k-vec3(0,-shelfSize.y-shelfGap,-shelfGap),vec3(0,1.,0));float v=dot(k-vec3(0,0,-(shelfSize.z+shelfGap)),vec3(0,0,1.));r=a(v,u);for(int w=0;w<20;w++){float x=dot(idents[w],vec4(1.0));if(x!=0.0){vec2 e=idents[w].xy;vec2 y=idents[w].zw;t=max(-j(k.xy+e,y),t);}}r=a(r,t);return vec2(r,0);}vec2 z(vec3 A,vec3 B,float C,float D){float E=D*2.0;float F=+0.0;float G=-1.0;vec2 H=vec2(-1.0,-1.0);for(int w=0;w<140;w++){if(E<D||F>C) break;vec2 I=q(A+B*F);E=I.x;G=I.y;F+=E;}if(F<C){H=vec2(F,G);}return H;}vec2 z(vec3 A,vec3 B){return z(A,B,20.0,0.001);}vec3 J(vec3 K,float L){const vec3 M=vec3(1.0,-1.0,-1.0);const vec3 N=vec3(-1.0,-1.0,1.0);const vec3 O=vec3(-1.0,1.0,-1.0);const vec3 P=vec3(1.0,1.0,1.0);return normalize(M*q(K+M*L).x+N*q(K+N*L).x+O*q(K+O*L).x+P*q(K+P*L).x);}vec3 J(vec3 K){return J(K,0.002);}float Q(vec3 R,vec3 S,float T,float U,float V){float H=2.5;float W=0.0;float X=0.1;for(int w=0;w<26;++w){float Y=q(R+S*X).x;if(Y<0.001) return .0;float Z=Y*Y/(2.*W);float g=sqrt(Y*Y-Z*Z);H=min(H,V*g/max(0.0,X-Z));W=Y;X+=Y;}return H;}vec3 ba(vec3 K,vec3 bb,vec4 bc){vec3 bd=bc.xyz-K;float be=length(bd);bd=normalize(bd);float bf=2.0;float bg=Q(K,bd,.625,be,shadowPower);return vec3(bg);}void main(){vec3 bh=vec3(0.,0.,0.);vec3 S,R;R=rayPosition;S=rayDirection;vec2 X=z(rayPosition,S,35000.,0.001);if(X.x>0.5){vec3 K=R+S*X.x;vec3 bi=J(K);vec3 bj=ba(K,bi,vec4(shadowSource.xyz,shadowPower));bh=bj;bh=vec3(dot(bh,vec3(.3,.9,.0)));}gl_FragColor.rgb=bh;gl_FragColor.a=1.0-smoothstep(1.0,0.0,length(bh));}";var w="precision mediump float;\n#define GLSLIFY 1\nvarying vec3 rayPosition;varying vec3 rayDirection;uniform vec4 shadowSource;uniform float shadowPower;uniform vec3 shelfSize;uniform vec4 idents[20];uniform float shelfGap;float a(float b,float c){return min(b,c);}vec2 a(vec2 b,vec2 c){return (b.x<c.x)?b:c;}float d(vec3 e,vec3 f){vec3 g=abs(e)-f;return min(max(g.x,max(g.y,g.z)),0.0)+length(max(g,0.0));}float h(vec2 i){return max(i.x,i.y);}float j(vec2 k,vec2 l){return h(abs(k)-l);}float m(float n,float l,float o){vec2 p=max(vec2(o+n,o+l),vec2(0));return min(-o,max(n,l))+length(p);}vec2 q(vec3 k){k.y-=shelfSize.y-20.;k.z-=shelfSize.z;float r=0.;vec3 s=shelfSize;s.y-=20.;float t=d(k+vec3(0,0,0),s);float u=dot(k-vec3(0,-shelfSize.y-shelfGap,-shelfGap),vec3(0,1.,0));float v=dot(k-vec3(0,0,-(shelfSize.z+shelfGap)),vec3(0,0,1.));r=a(v,u);r=a(r,t);return vec2(r,0);}vec2 w(vec3 x,vec3 y,float z,float A){float B=A*2.0;float C=+0.0;float D=-1.0;vec2 E=vec2(-1.0,-1.0);for(int F=0;F<10;F++){if(B<A||C>z) break;vec2 G=q(x+y*C);B=G.x;D=G.y;C+=B;}if(C<z){E=vec2(C,D);}return E;}vec2 w(vec3 x,vec3 y){return w(x,y,20.0,0.001);}vec3 H(vec3 I,float J){const vec3 K=vec3(1.0,-1.0,-1.0);const vec3 L=vec3(-1.0,-1.0,1.0);const vec3 M=vec3(-1.0,1.0,-1.0);const vec3 N=vec3(1.0,1.0,1.0);return normalize(K*q(I+K*J).x+L*q(I+L*J).x+M*q(I+M*J).x+N*q(I+N*J).x);}vec3 H(vec3 I){return H(I,0.002);}float O(vec3 P,vec3 Q,float R,float S,float T){float E=2.5;float U=0.0;float V=0.1;for(int F=0;F<26;++F){float W=q(P+Q*V).x;if(W<0.001) return .0;float X=W*W/(2.*U);float g=sqrt(W*W-X*X);E=min(E,T*g/max(0.0,V-X));U=W;V+=W;}return E;}vec3 Y(vec3 I,vec3 Z,vec4 ba){vec3 bb=ba.xyz-I;float bc=length(bb);bb=normalize(bb);float bd=2.0;float be=O(I,bb,.625,bc,shadowPower);return vec3(be);}void main(){vec3 bf=vec3(0.,0.,0.);vec3 Q,P;P=rayPosition;Q=rayDirection;vec2 V=w(rayPosition,Q,35000.,0.001);if(V.x>0.5){vec3 I=P+Q*V.x;vec3 bg=H(I);vec3 bh=shadowSource.xyz;vec3 bi=Y(I,bg,vec4(bh,shadowPower));bf=bi;bf=vec3(dot(bf,vec3(.3,.9,.0)));}gl_FragColor.rgb=bf;gl_FragColor.a=1.0-smoothstep(1.0,0.0,length(bf));}";var x="precision mediump float;\n#define GLSLIFY 1\n\n#define clip 35000.0\nattribute vec2 position;attribute vec2 uvs;varying vec4 ndc;varying vec3 rayDirection;varying vec3 rayPosition;uniform vec3 cameraPosition;uniform vec2 warp;void main(){vec2 a=uvs;a=(a*2.0)-vec2(1.0,0.0);ndc=vec4(a,1.0,1.0)*clip;rayDirection=vec3(0,0,-1);rayPosition=vec3(0);rayPosition.z+=cameraPosition.z;rayPosition.xy+=ndc.xy*warp;gl_Position=vec4(position,0.0,1.0);}";var _="precision mediump float;\n#define GLSLIFY 1\n\n#define clip 35000.0\nattribute vec2 position;attribute vec2 uvs;varying vec4 ndc;varying vec3 rayDirection;varying vec3 rayPosition;uniform vec3 cameraPosition;uniform vec2 warp;void main(){vec2 a=uvs;a=(a*2.0)-vec2(1.0,0.0);ndc=vec4(a,1.0,1.0)*clip;rayDirection=vec3(0,-1,0);rayPosition=vec3(0);rayPosition.y+=cameraPosition.z;rayPosition.xz+=ndc.xy*warp;gl_Position=vec4(position,0.0,1.0);}";var C=42;var S=new p["Matrix4"];var k=new p["Matrix4"];var A=new p["Matrix4"];var E=new p["Matrix4"];function P(e,t,r){var n=e.camera;var a=e.geo;var i={blend:{enable:true,func:{srcRGB:"src alpha",srcAlpha:0,dstRGB:"one minus src alpha",dstAlpha:1},equation:{rgb:"add",alpha:"add"},color:[0,0,0,0]}};var o=function t(){return[e.ivy.width/2,e.ivy.getHeight()/2,320/2]};var s=m()(t);var l={};for(var c=0;c<C;c++){l["idents["+c+"]"]=s.prop("idents"+c)}var u=Object.assign({cameraPosition:s.prop("cameraPosition"),shelfGap:s.prop("shelfGap"),shelfSize:s.prop("shelfSize")},l,{shadowSource:s.prop("shadowSource"),shadowPower:s.prop("shadowPower"),warp:s.prop("warp")});var d=s(Object.assign({frag:r?w:y,vert:r?_:x,attributes:{position:s.buffer(b.a.verts),uvs:s.buffer(b.a.uvs)},uniforms:u,count:6},i));var f=function e(t){var r=t.settings;s.clear({depth:1,color:[0,0,0,1]});var n=a.getIndents(o());var i={};for(var l=0;l<C;l++){if(n[l]){i["idents"+l]=n[l]}else{i["idents"+l]=[0,0,0,0]}}d(Object.assign({shelfSize:o(),shadowPower:r.power,shelfGap:r.shelfGap,cameraPosition:[0,0,r.cameraz*3],shadowSource:[r.x,r.y,r.z,0],warp:[r.warpx,r.warpy]},i))};var p=function e(){return false};var h=function e(){return false};return{render:f,updateIndents:p,updateSize:h}}var M=r("723b");var T=window.THREE||r("e411");var I=function e(t,r){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var a=this;var i={NONE:-1,ROTATE:0,ZOOM:1,PAN:2,TOUCH_ROTATE:3,TOUCH_ZOOM_PAN:4};this.object=t;this.domElement=r!==undefined?r:document;this.locked=n;this.enabled=true;this.screen={left:0,top:0,width:0,height:0};this.rotateSpeed=1;this.zoomSpeed=1.2;this.panSpeed=.3;this.noRotate=false;this.noZoom=false;this.noPan=false;this.staticMoving=false;this.dynamicDampingFactor=.2;this.minDistance=0;this.maxDistance=Infinity;this.keys=[65,83,68];this.target=new T.Vector3;var o=1e-6;var s=new T.Vector3;var l=i.NONE,c=i.NONE,u=new T.Vector3,d=new T.Vector2,f=new T.Vector2,p=new T.Vector3,h=0,v=new T.Vector2,m=new T.Vector2,g=0,b=0,y=new T.Vector2,w=new T.Vector2;this.target0=this.target.clone();this.position0=this.object.position.clone();this.up0=this.object.up.clone();var x={type:"change"};var _={type:"start"};var C={type:"end"};this.handleResize=function(){if(this.domElement===document){this.screen.left=0;this.screen.top=0;this.screen.width=window.innerWidth;this.screen.height=window.innerHeight}else{var e=this.domElement.getBoundingClientRect();var t=this.domElement.ownerDocument.documentElement;this.screen.left=e.left+window.pageXOffset-t.clientLeft;this.screen.top=e.top+window.pageYOffset-t.clientTop;this.screen.width=e.width;this.screen.height=e.height}};this.handleEvent=function(e){if(typeof this[e.type]=="function"){this[e.type](e)}};var S=function(){var e=new T.Vector2;return function t(r,n){e.set((r-a.screen.left)/a.screen.width,(n-a.screen.top)/a.screen.height);return e}}();var k=function(){var e=new T.Vector2;return function t(r,n){e.set((r-a.screen.width*.5-a.screen.left)/(a.screen.width*.5),(a.screen.height+2*(a.screen.top-n))/a.screen.width);return e}}();this.rotateCamera=function(){var e=new T.Vector3,t=new T.Quaternion,r=new T.Vector3,n=new T.Vector3,i=new T.Vector3,o=new T.Vector3,s;var l=0;function c(){var c=o.clone();o.set(f.x-d.x,f.y-d.y,0);s=o.length();var v=30;var m=false;l+=s*(e.y>0?1:-1);var g=l*(180/Math.PI);g=Math.max(-v,Math.min(g,v));if(s){u.copy(a.object.position).sub(a.target);r.copy(u).normalize();n.copy(a.object.up).normalize();i.crossVectors(n,r).normalize();n.setLength(f.y-d.y);i.setLength(f.x-d.x);if(this.locked){o.copy(i)}else{o.copy(n.add(i))}e.crossVectors(o,u).normalize();t.setFromAxisAngle(e,s);u.applyQuaternion(t);if(!this.locked)a.object.up.applyQuaternion(t);p.copy(e);h=s}else if(!a.staticMoving&&h){h*=Math.sqrt(1-a.dynamicDampingFactor);u.copy(a.object.position).sub(a.target);t.setFromAxisAngle(p,h);u.applyQuaternion(t);a.object.up.applyQuaternion(t)}d.copy(f)}return c}();this.zoomCamera=function(){var e;if(l===i.TOUCH_ZOOM_PAN){e=g/b;g=b;u.multiplyScalar(e)}else{e=1+(m.y-v.y)*a.zoomSpeed;if(e!==1&&e>0){u.multiplyScalar(e)}if(a.staticMoving){v.copy(m)}else{v.y+=(m.y-v.y)*this.dynamicDampingFactor}}};this.panCamera=function(){var e=new T.Vector2,t=new T.Vector3,r=new T.Vector3;return function n(){e.copy(w).sub(y);if(e.lengthSq()){e.multiplyScalar(u.length()*a.panSpeed);r.copy(u).cross(a.object.up).setLength(e.x);r.add(t.copy(a.object.up).setLength(e.y));a.object.position.add(r);a.target.add(r);if(a.staticMoving){y.copy(w)}else{y.add(e.subVectors(w,y).multiplyScalar(a.dynamicDampingFactor))}}}}();this.checkDistances=function(){if(!a.noZoom||!a.noPan){if(u.lengthSq()>a.maxDistance*a.maxDistance){a.object.position.addVectors(a.target,u.setLength(a.maxDistance));v.copy(m)}if(u.lengthSq()<a.minDistance*a.minDistance){a.object.position.addVectors(a.target,u.setLength(a.minDistance));v.copy(m)}}};this.update=function(){u.subVectors(a.object.position,a.target);if(!a.noRotate){a.rotateCamera()}if(!a.noZoom){a.zoomCamera()}if(!a.noPan){a.panCamera()}a.object.position.addVectors(a.target,u);a.checkDistances();a.object.lookAt(a.target);if(s.distanceToSquared(a.object.position)>o){a.dispatchEvent(x);s.copy(a.object.position)}};this.reset=function(){l=i.NONE;c=i.NONE;a.target.copy(a.target0);a.object.position.copy(a.position0);a.object.up.copy(a.up0);u.subVectors(a.object.position,a.target);a.object.lookAt(a.target);a.dispatchEvent(x);s.copy(a.object.position)};function A(e,t){if(Array.isArray(e)){return e.indexOf(t)!==-1}else{return e===t}}function E(e){if(a.enabled===false)return;window.removeEventListener("keydown",E);c=l;if(l!==i.NONE){}else if(A(a.keys[i.ROTATE],e.keyCode)&&!a.noRotate){l=i.ROTATE}else if(A(a.keys[i.ZOOM],e.keyCode)&&!a.noZoom){l=i.ZOOM}else if(A(a.keys[i.PAN],e.keyCode)&&!a.noPan){l=i.PAN}}function P(e){if(a.enabled===false)return;l=c;window.addEventListener("keydown",E,false)}function M(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();if(l===i.NONE){l=e.button}if(l===i.ROTATE&&!a.noRotate){f.copy(k(e.pageX,e.pageY));d.copy(f)}else if(l===i.ZOOM&&!a.noZoom){v.copy(S(e.pageX,e.pageY));m.copy(v)}else if(l===i.PAN&&!a.noPan){y.copy(S(e.pageX,e.pageY));w.copy(y)}document.addEventListener("mousemove",I,false);document.addEventListener("mouseup",O,false);a.dispatchEvent(_)}function I(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();if(l===i.ROTATE&&!a.noRotate){d.copy(f);f.copy(k(e.pageX,e.pageY))}else if(l===i.ZOOM&&!a.noZoom){m.copy(S(e.pageX,e.pageY))}else if(l===i.PAN&&!a.noPan){w.copy(S(e.pageX,e.pageY))}}function O(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();l=i.NONE;document.removeEventListener("mousemove",I);document.removeEventListener("mouseup",O);a.dispatchEvent(C)}function D(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();switch(e.deltaMode){case 2:v.y-=e.deltaY*.025;break;case 1:v.y-=e.deltaY*.01;break;default:v.y-=e.deltaY*25e-5;break}a.dispatchEvent(_);a.dispatchEvent(C)}function R(e){if(a.enabled===false)return;switch(e.touches.length){case 1:l=i.TOUCH_ROTATE;f.copy(k(e.touches[0].pageX,e.touches[0].pageY));d.copy(f);break;default:l=i.TOUCH_ZOOM_PAN;var t=e.touches[0].pageX-e.touches[1].pageX;var r=e.touches[0].pageY-e.touches[1].pageY;b=g=Math.sqrt(t*t+r*r);var n=(e.touches[0].pageX+e.touches[1].pageX)/2;var o=(e.touches[0].pageY+e.touches[1].pageY)/2;y.copy(S(n,o));w.copy(y);break}a.dispatchEvent(_)}function F(e){if(a.enabled===false)return;e.preventDefault();e.stopPropagation();switch(e.touches.length){case 1:d.copy(f);f.copy(k(e.touches[0].pageX,e.touches[0].pageY));break;default:var t=e.touches[0].pageX-e.touches[1].pageX;var r=e.touches[0].pageY-e.touches[1].pageY;b=Math.sqrt(t*t+r*r);var n=(e.touches[0].pageX+e.touches[1].pageX)/2;var i=(e.touches[0].pageY+e.touches[1].pageY)/2;w.copy(S(n,i));break}}function $(e){if(a.enabled===false)return;switch(e.touches.length){case 0:l=i.NONE;break;case 1:l=i.TOUCH_ROTATE;f.copy(k(e.touches[0].pageX,e.touches[0].pageY));d.copy(f);break}a.dispatchEvent(C)}function B(e){if(a.enabled===false)return;e.preventDefault()}this.dispose=function(){this.domElement.removeEventListener("contextmenu",B,false);this.domElement.removeEventListener("mousedown",M,false);this.domElement.removeEventListener("wheel",D,false);this.domElement.removeEventListener("touchstart",R,false);this.domElement.removeEventListener("touchend",$,false);this.domElement.removeEventListener("touchmove",F,false);document.removeEventListener("mousemove",I,false);document.removeEventListener("mouseup",O,false);window.removeEventListener("keydown",E,false);window.removeEventListener("keyup",P,false)};this.domElement.addEventListener("contextmenu",B,false);this.domElement.addEventListener("mousedown",M,false);this.domElement.addEventListener("wheel",D,false);this.domElement.addEventListener("touchstart",R,false);this.domElement.addEventListener("touchend",$,false);this.domElement.addEventListener("touchmove",F,false);window.addEventListener("keydown",E,false);window.addEventListener("keyup",P,false);this.handleResize();this.update()};function O(e){e.preventDefault()}I.prototype=Object.create(T.EventDispatcher.prototype);r.d(t,"a",function(){return N});window.THREE=p;var D=window.location.href.indexOf("localhost")>-1?"":"/r_static/webdesigner/";var R=r("7a69");var F=r("c9c8");var $=r("1bf1");var B=function e(){this.horizontals=true;this.verticals=true;this.supports=true;this.backs=true;this.doors=true;this.drawers=true;this.fills=true;this.legs=true;this.accessories=true;this.spacer=true;this.colorMode=0;this.filterMaterialKey="";this.filterMaterial="";this.filterEnable=false};var j=new B;var N=function(){function e(t){var r=t.container,n=t.width,a=t.height,i=t.cameraLock,o=t.updatedCameraCallback;u()(this,e);this.controls=null;this.cameraLock=i;this.container=r;if(r)this.init(n,a)}f()(e,[{key:"updatedCamera",value:function e(){console.log("camera-updates");if(this.updatedCameraCallbackFunction)this.updatedCameraCallbackFunction()}},{key:"set",value:function e(t){var r=t.container,n=t.width,a=t.height,i=t.cameraLock,o=t.updatedCameraCallback;this.cameraLock=i;this.container=r;this.init(n,a);this.resize({width:n,height:a});this.updatedCameraCallbackFunction=o;return this}},{key:"resize",value:function e(t){var r=t.width,n=t.height;this.camera.aspect=r/n;this.camera.updateProjectionMatrix();this.renderer.setSize(r,n);this.renderer.domElement.style.width="".concat(r,"px");this.renderer.domElement.style.height="".concat(n,"px")}},{key:"rotateScene",value:function e(t){var r=t.angle;console.log(r)}},{key:"create",value:function e(t,r,n){if(t==null)return this.flush();var a=this.filterElements(t.item.elements,j);this.drawElements(a,r,n);this.render()}},{key:"flush",value:function e(){this.resetItems()}},{key:"addInfo",value:function e(t){var r=this;var n=new p["TextureLoader"];return new Promise(function(e){F(D+"fonts/inter-msdf/font.json",function(a,i){var o=R({width:300,align:"left",font:i,multipage:true});o.update(t);n.load(D+"fonts/inter-msdf/sheet0.png",function(t){var n=new p["RawShaderMaterial"]($({map:t,precision:"highp",alphaTest:1e-8,side:p["DoubleSide"],transparent:true,color:"rgb(230, 230, 230)"}));var a=new p["Mesh"](o,n);r.scene.add(a);a.scale.set(1,-1,1);e(a)})})})}},{key:"fitCameraToObject",value:function e(t,r,n,a){n=n||1.25;var i=new p["Box3"];i.setFromObject(r);var o=i.getCenter();var s=i.getSize();var l=Math.max(s.x,s.y,s.z);var c=t.fov*(Math.PI/180);var u=Math.abs(l/4*Math.tan(c*2));u*=n;t.position.z=u;var d=i.min.z;var f=d<0?-d+u:u-d;t.far=f*3;t.updateProjectionMatrix();if(a){a.target=o;a.maxDistance=f*2;a.saveState()}else{t.lookAt(o)}}},{key:"fitOrtho",value:function e(){var t=(new p["Box3"]).setFromObject(this.scene);var r=this.canvasAbsoluteHeight;var n=this.canvasAbsoluteWidth;var a=new p["OrthographicCamera"](n/-2,n/2,r/2,r/-2,0,100);var t=(new p["Box3"]).setFromObject(this.scene);this.scene.position.multiplyScalar(-1);a.zoom=Math.min(n/(t.max.x-t.min.x),r/(t.max.y-t.min.y))*.9;a.updateProjectionMatrix();a.updateMatrix();return a}},{key:"init",value:function e(t,r){var n=this;var a=this.container;if(this.renderer){a.appendChild(this.renderer.domElement);return}var i,o;var s,l;this.canvasAbsoluteHeight=l=r;this.canvasAbsoluteWidth=s=t;var c=new p["WebGLRenderer"]({antialias:true,preserveDrawingBuffer:true,alpha:true});this.renderer=c;c.setPixelRatio(window.devicePixelRatio);c.setSize(s,l);a.appendChild(c.domElement);this.camera=i=new p["PerspectiveCamera"](20,t/r,1,2e4);i.position.z=12e3;i.position.x=400;i.position.y=800;var u=this.controls=new I(this.camera,c.domElement,this.cameraLock);u.rotateSpeed=1;u.zoomSpeed=1.2;u.panSpeed=.8;u.noZoom=false;u.noPan=false;u.staticMoving=true;u.dynamicDampingFactor=.3;u.target=new p["Vector3"](200,250,0);u.addEventListener("change",function(){n.updatedCamera()});this.scene=o=new p["Object3D"];this._scene=new p["Scene"];this._scene.add(this.scene);o.background=new p["Color"](1052688);var d=new p["GridHelper"](4400,100,7829367,4473924);d.position.y=0;d.position.x=0;o.add(d);o.rotation.y=.5;this.items=[];this.labels=[];var f=function e(){n.controls.update();n.render();requestAnimationFrame(e)};f()}},{key:"filterElements",value:function e(t,r){return t.filter(function(e){return e["elem_type"]==="H"&&r["horizontals"]||e["elem_type"]==="V"&&r["verticals"]||e["elem_type"]==="S"&&r["supports"]||e["elem_type"]==="B"&&r["backs"]||e["elem_type"]==="D"&&r["doors"]||e["elem_type"]==="O"||e["elem_type"]==="M"||e["elem_type"]==="L"&&r["legs"]||e["elem_type"]==="T"&&r["drawers"]||e["elem_type"]==="FILL"&&r["fills"]||e["elem_type"]==="ACC"&&r["accessories"]||e["elem_type"]==="SPACER"&&r["spacer"]})}},{key:"render",value:function e(){this.renderer.render(this._scene,this.camera)}},{key:"getCompoundBoundingBox",value:function e(t){var r=null;t.traverse(function(e){var t=e.geometry;if(t===undefined)return;t.computeBoundingBox();if(r===null){r=t.boundingBox}else{r.union(t.boundingBox)}});return r}},{key:"selectElement",value:function e(t){this.groups.map(function(e,r){e.element.visible=r==t})}},{key:"drawElements",value:function e(t,r,n){var a=this;this.resetItems();var i=this.scene;var o=this.items;var s=this.labels;this.groups=[];var l=function e(l){var c=t[l];if(c.components==null){return"continue"}var b=[(c.x_domain[1]+c.x_domain[0])/200,(c.y_domain[1]+c.y_domain[0])/200,(c.z_domain[1]+c.z_domain[0])/200];var y=[(c.x_domain[1]-c.x_domain[0])/100,(c.y_domain[1]-c.y_domain[0])/100,(c.z_domain[1]-c.z_domain[0])/100];for(var w=0;w<Object.values(c.components).length;w++){var x=Object.values(c.components)[w];if(j.filterEnable==true){if(!((x[j.filterMaterialKey]||"missing").toString().indexOf(j.filterMaterial)>-1)){continue}}var _=[(x.x_domain[1]-x.x_domain[0])/100,(x.y_domain[1]-x.y_domain[0])/100,(x.z_domain[1]-x.z_domain[0])/100];var C=[(x.x_domain[1]+x.x_domain[0])/200,(x.y_domain[1]+x.y_domain[0])/200,(x.z_domain[1]+x.z_domain[0])/200];u=c.elem_type=="L"?new p["CylinderGeometry"](_[0]/2,_[0]/2,_[1],12,2):new p["BoxGeometry"](_[0],_[1],_[2]);d=new p["MeshBasicMaterial"]({color:10066329,wireframe:false,transparent:true,polygonOffset:true,polygonOffsetFactor:1,polygonOffsetUnits:1,opacity:n?.95:.5});f=new p["Mesh"](u,d);f.position.x=C[0];f.position.y=C[1];f.position.z=C[2];f.item_data=c;h=new p["EdgesGeometry"](f.geometry);v=new p["LineBasicMaterial"]({color:r?11184810:16777215,linewidth:1});m=new p["LineSegments"](h,n?d:v);f.add(m);i.add(f);o.push(f);if(!a.groups[c.c_config_id]){a.groups[c.c_config_id]={group:new p["Group"],pos:new p["Vector3"]}}a.groups[c.c_config_id].group.add(f.clone());a.groups[c.c_config_id].pos.x=C[0];a.groups[c.c_config_id].pos.y=C[1];a.groups[c.c_config_id].pos.z=C[2]}if(c.surname!=""){g=a.makeTextSprite("".concat(c.surname),{fontsize:70,borderColor:{r:255,g:0,b:0,a:1},backgroundColor:{r:255,g:100,b:100,a:.2}});a.addInfo("".concat(c.surname)).then(function(e){e.position.set(b[0]-150,b[1],b[2]+y[2]/2);i.add(e);s.push(e)})}};for(var c=0;c<t.length;c++){var u;var d;var f;var h;var v;var m;var g;var b=l(c);if(b==="continue")continue}this.groups.map(function(e,t){var r=new p["MeshBasicMaterial"]({color:20991,wireframe:false,transparent:true,polygonOffset:true,polygonOffsetFactor:1,polygonOffsetUnits:1,opacity:.65});var n=a.getCompoundBoundingBox(e.group);var s=a.getCompoundBoundingBox(e.group);var l=new p["Vector3"];var c=new p["Vector3"];var u=new p["Vector3"];s.expandByScalar(25);s.getSize(c);n.getSize(l);var d=new p["BoxGeometry"](c.x,c.y,c.z);var f=new p["Mesh"](d,r);f.visible=false;f.position.x=e.pos.x;f.position.y=e.pos.y-l.y/2+15;f.position.z=e.pos.z;i.add(f);o.push(f);e.element=f})}},{key:"resetScene",value:function e(t){if(t){this._scene.background=new p["Color"](15790320);this._scene.opacity=1}else{this._scene.background=new p["Color"](1052688);this._scene.opacity=.5}var r=new p["GridHelper"](4400,100,7829367,4473924);r.position.y=0;r.position.x=0;this.scene.add(r)}},{key:"resetItems",value:function e(){var t=this.scene;var r=this.labels;var n=this.groups?this.groups:[];for(var a=0;a<this.items.length;a++){t.remove(this.items[a])}this.items=[];for(var i=0;i<this.labels.length;i++){t.remove(this.labels[i])}this.labels=[];for(var o=0;o<n.length;o++){if(n[o])t.remove(n[o].group)}this.groups=[]}},{key:"resetCamera",value:function e(){this.controls.reset()}},{key:"makeTextSprite",value:function e(t,r){if(r===undefined)r={};var n=r.hasOwnProperty("fontface")?r["fontface"]:"Arial";var a=r.hasOwnProperty("fontsize")?r["fontsize"]:18;var i=r.hasOwnProperty("borderThickness")?r["borderThickness"]:4;var o=r.hasOwnProperty("borderColor")?r["borderColor"]:{r:0,g:0,b:0,a:1};var s=r.hasOwnProperty("backgroundColor")?r["backgroundColor"]:{r:255,g:255,b:255,a:1};var l=document.createElement("canvas");if(t.length>10){l.width=t.length*35}var c=l.getContext("2d");c.font="Bold "+a+"px "+n;var u=c.measureText(t);var d=u.width;c.fillStyle="rgba("+s.r+","+s.g+","+s.b+","+s.a+")";c.strokeStyle="rgba("+o.r+","+o.g+","+o.b+","+o.a+")";c.lineWidth=i;c.fillStyle="rgba(255, 255, 2555, 1.0)";c.fillText(t,i,a+i);var f=new p["Texture"](l);f.needsUpdate=true;var h=new p["SpriteMaterial"]({map:f});var v=new p["Sprite"](h);v.scale.set(l.width==300?200:450,l.width==300?100:.016*l.width,1);return v}}]);return e}();var L=new N({})},"87e0":function(e,t,r){},"887f":function(e,t,r){"use strict";var n=r("87e0");var a=r.n(n);var i=a.a},"88a0":function(e,t,r){var n=r("7831");var a=r("5882");var i=r("044b");var o=[0,2,3];var s=[2,1,3];e.exports=function e(t,r){if(!t||!(a(t)||i(t))){r=t||{};t=null}if(typeof r==="number")r={count:r};else r=r||{};var l=typeof r.type==="string"?r.type:"uint16";var c=typeof r.count==="number"?r.count:1;var u=r.start||0;var d=r.clockwise!==false?o:s,f=d[0],p=d[1],h=d[2];var v=c*6;var m=t||new(n(l))(v);for(var g=0,b=0;g<v;g+=6,b+=4){var y=g+u;m[y+0]=b+0;m[y+1]=b+1;m[y+2]=b+2;m[y+3]=b+f;m[y+4]=b+p;m[y+5]=b+h}return m}},"8eb7":function(e,t){var r=false;var n,a,i,o,s;var l;var c,u,d,f;var p;var h,v,m;var g;function b(){if(r){return}r=true;var e=navigator.userAgent;var t=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e);var b=/(Mac OS X)|(Windows)|(Linux)/.exec(e);h=/\b(iPhone|iP[ao]d)/.exec(e);v=/\b(iP[ao]d)/.exec(e);f=/Android/i.exec(e);m=/FBAN\/\w+;/i.exec(e);g=/Mobile/i.exec(e);p=!!/Win64/.exec(e);if(t){n=t[1]?parseFloat(t[1]):t[5]?parseFloat(t[5]):NaN;if(n&&document&&document.documentMode){n=document.documentMode}var y=/(?:Trident\/(\d+.\d+))/.exec(e);l=y?parseFloat(y[1])+4:n;a=t[2]?parseFloat(t[2]):NaN;i=t[3]?parseFloat(t[3]):NaN;o=t[4]?parseFloat(t[4]):NaN;if(o){t=/(?:Chrome\/(\d+\.\d+))/.exec(e);s=t&&t[1]?parseFloat(t[1]):NaN}else{s=NaN}}else{n=a=i=s=o=NaN}if(b){if(b[1]){var w=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);c=w?parseFloat(w[1].replace("_",".")):true}else{c=false}u=!!b[2];d=!!b[3]}else{c=u=d=false}}var y={ie:function(){return b()||n},ieCompatibilityMode:function(){return b()||l>n},ie64:function(){return y.ie()&&p},firefox:function(){return b()||a},opera:function(){return b()||i},webkit:function(){return b()||o},safari:function(){return y.webkit()},chrome:function(){return b()||s},windows:function(){return b()||u},osx:function(){return b()||c},linux:function(){return b()||d},iphone:function(){return b()||h},mobile:function(){return b()||(h||v||f||g)},nativeApp:function(){return b()||m},android:function(){return b()||f},ipad:function(){return b()||v}};e.exports=y},"8f6d":function(e,t){e.exports=function e(t,r){return typeof t==="number"?t:typeof r==="number"?r:0}},"8fd2":function(e,t,r){"use strict";var n=r("82ed");var a=r.n(n);var i=a.a},"91e0":function(e,t,r){"use strict";var n=r("9a57");var a=r.n(n);var i=a.a},"998e":function(e,t,r){},"9a57":function(e,t,r){},"9c55":function(e,t,r){"use strict";(function(e){var t=r("7813");var n=r.n(t);var a=r("784b");var i=r.n(a);var o=r("60f6");var s=r.n(o);var l=r("359c");var c=r.n(l);var u=r("7341");var d=r.n(u);var f=r("fb2b");var p=r.n(f);var h=r("1a91");var v=r.n(h);var m=r("7037");var g=r.n(m);(function t(n,a){if((typeof exports==="undefined"?"undefined":g()(exports))==="object"&&(false?undefined:g()(e))==="object")e.exports=a();else if(typeof define==="function"&&r("3c35"))define([],a);else if((typeof exports==="undefined"?"undefined":g()(exports))==="object")exports["MSDFText"]=a();else n["MSDFText"]=a()})(typeof self!=="undefined"?self:undefined,function(){return function(e){var t={};function r(n){if(t[n]){return t[n].exports}var a=t[n]={i:n,l:false,exports:{}};e[n].call(a.exports,a,a.exports,r);a.l=true;return a.exports}r.m=e;r.c=t;r.d=function(e,t,n){if(!r.o(e,t)){Object.defineProperty(e,t,{configurable:false,enumerable:true,get:n})}};r.n=function(e){var t=e&&e.__esModule?function t(){return e["default"]}:function t(){return e};r.d(t,"a",t);return t};r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};r.p="";return r(r.s=0)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=r(1);t.MSDFText=n.MSDFText;var a=r(2);t.MSDFRenderer=a.MSDFRenderer},function(e,t,r){"use strict";var n=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t){if(t.hasOwnProperty(r))e[r]=t[r]}};return function(t,r){e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:true});var a=function(e){n(t,e);function t(t,r){var n=e.call(this,r.texture||PIXI.Texture.WHITE)||this;n._text=t;n._font={fontFace:r.fontFace,fontSize:r.fontSize,color:r.fillColor===undefined?16711680:r.fillColor,weight:r.weight===undefined?.5:1-r.weight,align:r.align,kerning:r.kerning===undefined?true:r.kerning,strokeColor:r.strokeColor||0,dropShadow:r.dropShadow||false,dropShadowColor:r.dropShadowColor||0,dropShadowAlpha:r.dropShadowAlpha===undefined?.5:r.dropShadowAlpha,dropShadowBlur:r.dropShadowBlur||0,dropShadowOffset:r.dropShadowOffset||new PIXI.Point(2,2),pxrange:r.pxrange===undefined?3:r.pxrange};if(r.strokeThickness===undefined||r.strokeThickness===0){n._font.strokeWeight=0}else{n._font.strokeWeight=n._font.weight-r.strokeThickness}n._baselineOffset=r.baselineOffset===undefined?0:r.baselineOffset;n._letterSpacing=r.letterSpacing===undefined?0:r.letterSpacing;n._lineSpacing=r.lineSpacing===undefined?0:r.lineSpacing;n._textWidth=n._textHeight=0;n._maxWidth=r.maxWidth||0;n._anchor=new PIXI.ObservablePoint(function(){n.dirty++},n,0,0);n._textMetricsBound=new PIXI.Rectangle;n._debugLevel=r.debugLevel||0;n.pluginName="msdf";n.dirty=0;n.updateText();return n}t.prototype.updateText=function(){this.removeChildren();var e=PIXI.extras.BitmapText.fonts[this._font.fontFace];if(!e)throw new Error("Invalid fontFace: "+this._font.fontFace);this._texture=this.getBitmapTexture(this._font.fontFace);this._font.rawSize=e.size;var t=this._font.fontSize/e.size;var r=new PIXI.Point(0,-this._baselineOffset*t);var n=[];var a=[];var i=this._texture.width;var o=this._texture.height;var s=-1;var l=0;var c=0;var u=0;var d=-1;var f=0;var p=0;var h=0;for(var v=0;v<this._text.length;v++){var m=this._text.charCodeAt(v);if(/(\s)/.test(this._text.charAt(v))){d=v;f=l}if(/(?:\r\n|\r|\n)/.test(this._text.charAt(v))){l-=this._letterSpacing;a.push(l);c=Math.max(c,l);u++;r.x=0;r.y+=e.lineHeight*t+this._lineSpacing*t;s=-1;continue}if(d!==-1&&this._maxWidth>0&&r.x>this._maxWidth){PIXI.utils.removeItems(n,d-p,v-d);v=d;d=-1;++p;f-=this._letterSpacing;a.push(f);c=Math.max(c,f);u++;r.x=0;r.y+=e.lineHeight*t+this._lineSpacing*t;s=-1;continue}var g=e.chars[m];if(!g)continue;if(this._font.kerning&&s!==-1&&g.kerning[s]){r.x+=g.kerning[s]*t}n.push({line:u,charCode:m,drawRect:new PIXI.Rectangle(r.x+g.xOffset*t,r.y+g.yOffset*t,g.texture.width*t,g.texture.height*t),rawRect:new PIXI.Rectangle(g.texture.orig.x,g.texture.orig.y,g.texture.width,g.texture.height)});r.x+=(g.xAdvance+this._letterSpacing)*t;l=r.x;h=Math.max(h,r.y+e.lineHeight*t);s=m}a.push(l);c=Math.max(c,l);var b=[];for(var v=0;v<=u;v++){var y=0;if(this._font.align==="right"){y=c-a[v]}else if(this._font.align==="center"){y=(c-a[v])/2}b.push(y)}var w=this.tint;var x=-1;for(var _=0,C=n;_<C.length;_++){var S=C[_];S.drawRect.x=S.drawRect.x+b[S.line];if(x!==S.line){x=S.line;if(this._debugLevel>1){this.drawGizmoRect(new PIXI.Rectangle(S.drawRect.x-e.chars[S.charCode].xOffset*t,S.drawRect.y-e.chars[S.charCode].yOffset*t,a[x],e.lineHeight*t),1,65280,.5)}}}if(this._debugLevel>0){this.drawGizmoRect(this.getLocalBounds(),1,16777215,.5)}this._textWidth=c;this._textHeight=h;this._textMetricsBound=new PIXI.Rectangle(0,0,c,h);this.vertices=this.toVertices(n);this.uvs=this.toUVs(n,i,o);this.indices=this.createIndicesForQuads(n.length);this.dirty++;this.indexDirty++};Object.defineProperty(t.prototype,"text",{get:function e(){return this._text},set:function e(t){this._text=this.unescape(t);this.updateText()},enumerable:true,configurable:true});Object.defineProperty(t.prototype,"fontData",{get:function e(){return this._font},enumerable:true,configurable:true});Object.defineProperty(t.prototype,"glDatas",{get:function e(){return this._glDatas},enumerable:true,configurable:true});Object.defineProperty(t.prototype,"textWidth",{get:function e(){return this._textWidth},enumerable:true,configurable:true});Object.defineProperty(t.prototype,"textHeight",{get:function e(){return this._textHeight},enumerable:true,configurable:true});Object.defineProperty(t.prototype,"maxWidth",{get:function e(){return this._maxWidth},enumerable:true,configurable:true});Object.defineProperty(t.prototype,"textMetric",{get:function e(){return this._textMetricsBound},enumerable:true,configurable:true});t.prototype.getBitmapTexture=function(e){var t=PIXI.extras.BitmapText.fonts[e];if(!t)return PIXI.Texture.EMPTY;var r=t.chars[Object.keys(t.chars)[0]].texture.baseTexture.imageUrl;return PIXI.utils.TextureCache[r]};t.prototype.toVertices=function(e){var t=this;var r=new Float32Array(e.length*4*2);var n=0;e.forEach(function(e){var a=e.drawRect.x;var i=e.drawRect.y;var o=e.drawRect.width;var s=e.drawRect.height;r[n++]=a;r[n++]=i;r[n++]=a;r[n++]=i+s;r[n++]=a+o;r[n++]=i+s;r[n++]=a+o;r[n++]=i;if(t._debugLevel>2)t.drawGizmoRect(e.drawRect,1,170,.5)});return r};t.prototype.toUVs=function(e,t,r){var n=new Float32Array(e.length*4*2);var a=0;e.forEach(function(e){var i=e.rawRect.x/t;var o=(e.rawRect.x+e.rawRect.width)/t;var s=(e.rawRect.y+e.rawRect.height)/r;var l=e.rawRect.y/r;n[a++]=i;n[a++]=l;n[a++]=i;n[a++]=s;n[a++]=o;n[a++]=s;n[a++]=o;n[a++]=l});return n};t.prototype.createIndicesForQuads=function(e){var t=e*6;var r=new Uint16Array(t);for(var n=0,a=0;n<t;n+=6,a+=4){r[n+0]=a+0;r[n+1]=a+1;r[n+2]=a+2;r[n+3]=a+0;r[n+4]=a+2;r[n+5]=a+3}return r};t.prototype.drawGizmoRect=function(e,t,r,n){if(t===void 0){t=1}if(r===void 0){r=16777215}if(n===void 0){n=1}var a=new PIXI.Graphics;a.nativeLines=true;a.lineStyle(t,r,n).drawRect(e.x,e.y,e.width,e.height);this.addChild(a)};t.prototype.unescape=function(e){return e.replace(/(\\n|\\r)/g,"\n")};return t}(PIXI.mesh.Mesh);t.MSDFText=a},function(e,t,r){"use strict";var n=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t){if(t.hasOwnProperty(r))e[r]=t[r]}};return function(t,r){e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:true});var a=r(3);var i=r(4);var o=function(e){n(t,e);function t(t){return e.call(this,t)||this}t.prototype.onContextChange=function(){var e=this.renderer.gl;this.shader=new PIXI.Shader(e,a,i)};t.prototype.render=function(e){var t=this.renderer;var r=e.texture;var n=e.fontData;var a=t.gl;if(!r||!r.valid||!n.rawSize)return;var i=e.glDatas[t.CONTEXT_UID];if(!i){i={shader:this.shader,vertexBuffer:PIXI.glCore.GLBuffer.createVertexBuffer(a,e.vertices,a.STREAM_DRAW),uvBuffer:PIXI.glCore.GLBuffer.createVertexBuffer(a,e.uvs,a.STREAM_DRAW),indexBuffer:PIXI.glCore.GLBuffer.createIndexBuffer(a,e.indices,a.STATIC_DRAW),vao:null,dirty:e.dirty,indexDirty:e.indexDirty};i.vao=new PIXI.glCore.VertexArrayObject(a).addIndex(i.indexBuffer).addAttribute(i.vertexBuffer,i.shader.attributes.aVertexPosition,a.FLOAT,false,2*4,0).addAttribute(i.uvBuffer,i.shader.attributes.aTextureCoord,a.FLOAT,false,2*4,0);e.glDatas[t.CONTEXT_UID]=i}t.bindVao(i.vao);if(e.dirty!==i.dirty){i.dirty=e.dirty;i.uvBuffer.upload(e.uvs)}if(e.indexDirty!==i.indexDirty){i.indexDirty=e.indexDirty;i.indexBuffer.upload(e.indices)}i.vertexBuffer.upload(e.vertices);t.bindShader(i.shader);i.shader.uniforms.uSampler=t.bindTexture(r);if(t.state)t.state.setBlendMode(e.blendMode);i.shader.uniforms.translationMatrix=e.worldTransform.toArray(true);i.shader.uniforms.u_alpha=e.worldAlpha;i.shader.uniforms.u_color=PIXI.utils.hex2rgb(n.color);i.shader.uniforms.u_fontSize=n.fontSize*e.scale.x;i.shader.uniforms.u_fontInfoSize=1;i.shader.uniforms.u_weight=n.weight;i.shader.uniforms.u_pxrange=n.pxrange;i.shader.uniforms.strokeWeight=n.strokeWeight;i.shader.uniforms.strokeColor=PIXI.utils.hex2rgb(n.strokeColor);i.shader.uniforms.tint=PIXI.utils.hex2rgb(e.tint);i.shader.uniforms.hasShadow=n.dropShadow;i.shader.uniforms.shadowOffset=new Float32Array([n.dropShadowOffset.x,n.dropShadowOffset.x]);i.shader.uniforms.shadowColor=PIXI.utils.hex2rgb(n.dropShadowColor);i.shader.uniforms.shadowAlpha=n.dropShadowAlpha;i.shader.uniforms.shadowSmoothing=n.dropShadowBlur;var o=e.drawMode=a.TRIANGLES;i.vao.draw(o,e.indices.length,0)};return t}(PIXI.ObjectRenderer);t.MSDFRenderer=o;PIXI.WebGLRenderer.registerPlugin("msdf",o)},function(e,t){e.exports="#define GLSLIFY 1\nattribute vec2 aVertexPosition;\nattribute vec2 aTextureCoord;\n\nuniform mat3 translationMatrix;\nuniform mat3 projectionMatrix;\nuniform float u_fontInfoSize;\n\nvarying vec2 vTextureCoord;\n\nvoid main(void)\n{\n    vTextureCoord = aTextureCoord;\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition * u_fontInfoSize, 1.0)).xy, 0.0, 1.0);\n}\n"},function(e,t){e.exports="#define GLSLIFY 1\nvarying vec2 vTextureCoord;\nuniform vec3 u_color;\nuniform sampler2D uSampler;\nuniform float u_alpha;\nuniform float u_fontSize;\nuniform float u_weight;\nuniform float u_pxrange;\n\nuniform vec3 tint;\n// Stroke effect parameters\nuniform float strokeWeight;\nuniform vec3 strokeColor;\n\n// Shadow effect parameters\nuniform bool hasShadow;\nuniform vec2 shadowOffset;\nuniform float shadowSmoothing;\nuniform vec3 shadowColor;\nuniform float shadowAlpha;\n\nfloat median(float r, float g, float b) {\n    return max(min(r, g), min(max(r, g), b));\n}\n\nvoid main(void)\n{\n    float smoothing = clamp(2.0 * u_pxrange / u_fontSize, 0.0, 0.5);\n\n    vec2 textureCoord = vTextureCoord * 2.;\n    vec3 sample = texture2D(uSampler, vTextureCoord).rgb;\n    float dist = median(sample.r, sample.g, sample.b);\n\n    float alpha;\n    vec3 color;\n\n    // dirty if statment, will change soon\n    if (strokeWeight > 0.0) {\n        alpha = smoothstep(strokeWeight - smoothing, strokeWeight + smoothing, dist);\n        float outlineFactor = smoothstep(u_weight - smoothing, u_weight + smoothing, dist);\n        color = mix(strokeColor, u_color, outlineFactor) * alpha;\n    } else {\n        alpha = smoothstep(u_weight - smoothing, u_weight + smoothing, dist);\n        color = u_color * alpha;\n    }\n    vec4 text = vec4(color * tint, alpha) * u_alpha;\n    if (hasShadow == false) {\n        gl_FragColor = text;\n    } else {\n        vec3 shadowSample = texture2D(uSampler, vTextureCoord - shadowOffset).rgb;\n        float shadowDist = median(shadowSample.r, shadowSample.g, shadowSample.b);\n        float distAlpha = smoothstep(0.5 - shadowSmoothing, 0.5 + shadowSmoothing, shadowDist);\n        vec4 shadow = vec4(shadowColor, shadowAlpha * distAlpha);\n        gl_FragColor = mix(shadow, text, text.a);\n    }\n}"}])})}).call(this,r("dd40")(e))},a0d3:function(e,t,r){"use strict";var n=r("0f7c");e.exports=n.call(Function.call,Object.prototype.hasOwnProperty)},a0e8:function(e,t,r){},a3a2:function(e,t){e.exports.pages=function e(t){var e=new Float32Array(t.length*4*1);var r=0;t.forEach(function(t){var n=t.data.page||0;e[r++]=n;e[r++]=n;e[r++]=n;e[r++]=n});return e};e.exports.uvs=function e(t,r,n,a){var e=new Float32Array(t.length*4*2);var i=0;t.forEach(function(t){var o=t.data;var s=o.x+o.width;var l=o.y+o.height;var c=o.x/r;var u=o.y/n;var d=s/r;var f=l/n;if(a){u=(n-o.y)/n;f=(n-l)/n}e[i++]=c;e[i++]=u;e[i++]=c;e[i++]=f;e[i++]=d;e[i++]=f;e[i++]=d;e[i++]=u});return e};e.exports.positions=function e(t){var e=new Float32Array(t.length*4*2);var r=0;t.forEach(function(t){var n=t.data;var a=t.position[0]+n.xoffset;var i=t.position[1]+n.yoffset;var o=n.width;var s=n.height;e[r++]=a;e[r++]=i;e[r++]=a;e[r++]=i+s;e[r++]=a+o;e[r++]=i+s;e[r++]=a+o;e[r++]=i});return e}},a3de:function(e,t,r){"use strict";var n=!!(typeof window!=="undefined"&&window.document&&window.document.createElement);var a={canUseDOM:n,canUseWorkers:typeof Worker!=="undefined",canUseEventListeners:n&&!!(window.addEventListener||window.attachEvent),canUseViewport:n&&!!window.screen,isInWorker:!n};e.exports=a},a592:function(e,t,r){},a5dd:function(e,t,r){"use strict";var n=r("998e");var a=r.n(n);var i=a.a},a8bd:function(e,t,r){},ac17:function(e,t,r){"use strict";var n=r("a0e8");var a=r.n(n);var i=a.a},b082:function(e,t,r){"use strict";r.d(t,"a",function(){return p});var n=r("6db0");var a=r.n(n);var i=r("970b");var o=r.n(i);var s=r("5bc3");var l=r.n(s);var c=r("9c55");var u=window.location.href.indexOf("localhost")>-1?"":"/r_static/webdesigner/";var d=function(){function e(t){o()(this,e);this.options=t;this.fontsLoaded=false}l()(e,[{key:"create",value:function e(t){return new MSDFText.MSDFText(t,this.options)}},{key:"init",value:function e(t,r){var n=this;return new Promise(function(e){if(n.fontsLoaded){e()}else{t.loader.add([u+"fonts/inter-msdf-xml/Inter-UI-Medium.fnt"]).load(function(t){console.log("loaded",t);e()})}})}}]);return e}();var f={fontFace:"Inter-UI-Medium",fontSize:14.2,fillColor:16777215,weight:.75,strokeThickness:0,dropShadow:false,align:"left",letterSpacing:0,baselineOffset:8,debugLevel:0};var p=new d(f)},b189:function(e,t,r){"use strict";var n;if(!Object.keys){var a=Object.prototype.hasOwnProperty;var i=Object.prototype.toString;var o=r("d4ab");var s=Object.prototype.propertyIsEnumerable;var l=!s.call({toString:null},"toString");var c=s.call(function(){},"prototype");var u=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"];var d=function(e){var t=e.constructor;return t&&t.prototype===e};var f={$applicationCache:true,$console:true,$external:true,$frame:true,$frameElement:true,$frames:true,$innerHeight:true,$innerWidth:true,$outerHeight:true,$outerWidth:true,$pageXOffset:true,$pageYOffset:true,$parent:true,$scrollLeft:true,$scrollTop:true,$scrollX:true,$scrollY:true,$self:true,$webkitIndexedDB:true,$webkitStorageInfo:true,$window:true};var p=function(){if(typeof window==="undefined"){return false}for(var e in window){try{if(!f["$"+e]&&a.call(window,e)&&window[e]!==null&&typeof window[e]==="object"){try{d(window[e])}catch(t){return true}}}catch(t){return true}}return false}();var h=function(e){if(typeof window==="undefined"||!p){return d(e)}try{return d(e)}catch(t){return false}};n=function e(t){var r=t!==null&&typeof t==="object";var n=i.call(t)==="[object Function]";var s=o(t);var d=r&&i.call(t)==="[object String]";var f=[];if(!r&&!n&&!s){throw new TypeError("Object.keys called on a non-object")}var p=c&&n;if(d&&t.length>0&&!a.call(t,0)){for(var v=0;v<t.length;++v){f.push(String(v))}}if(s&&t.length>0){for(var m=0;m<t.length;++m){f.push(String(m))}}else{for(var g in t){if(!(p&&g==="prototype")&&a.call(t,g)){f.push(String(g))}}}if(l){var b=h(t);for(var y=0;y<u.length;++y){if(!(b&&u[y]==="constructor")&&a.call(t,u[y])){f.push(u[y])}}}return f}}e.exports=n},b268:function(e,t,r){"use strict";r.d(t,"a",function(){return s});var n=r("912c");var a=r.n(n);var i=function e(t,r){var n=3;var a=10;r.graphics.beginFill(t).drawRoundedRect(r.width-a-2*n,r.height-a-2*n,a,a,3).endFill()};var o=function e(t,r){var n=3;var a=40;r.graphics.beginFill(t).drawRoundedRect(r.width-10-2*n,n*2,10,a,3).endFill()};function s(e,t,r,n,a){var s={width:t,height:r,graphics:e};console.log("BA",n);switch(n.type){case"element":i(65280,s);break;case"component":i(255,s);break;case"component-set":i(16777215,s);break;case"mesh":i(16711680,s);break;case"table":i(6636321,s);break;case"component-table":i(6636321,s);break;default:i(16777215,s);break}if(n.item.height=="sub"){o(16777215,s)}}},b2ec:function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"grid-bar",class:""+e.behaviour},[r("div",{staticClass:"flex row"},[e.palette!="false"?r("div",{staticClass:"col top"},[r("div",{staticClass:"row black"},[r("div",{staticClass:"col-12"},[r("div",{staticClass:"row"},[r("div",{staticClass:"col-1",staticStyle:{"padding-top":"7px"}},[r("q-input",{staticClass:"small-search",attrs:{label:"Search by name or ID",dense:"dense",debounced:"debounced",filled:"filled",dark:"dark"},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},[r("q-icon",{attrs:{slot:"before",name:"search",dense:"dense"},slot:"before"})],1)],1),r("div",{staticClass:"col-8 users",staticStyle:{"padding-top":"5px","padding-left":"5px"}},[r("q-select",{staticClass:"users",attrs:{dark:"dark","options-dark":"options-dark",filled:"filled",dense:"dense","items-aligned":"items-aligned","max-values":"2","use-chips":"use-chips","options-dense":"options-dense",options:e.selectOptions,multiple:"multiple"},model:{value:e.userFilter,callback:function(t){e.userFilter=t},expression:"userFilter"}},[r("q-icon",{attrs:{slot:"before",name:"account_circle",dense:"dense"},slot:"before"})],1)],1)])])]),r("new-palette",{attrs:{source:e.filteredSources,box:e.box,actAsNavigation:e.actAsNavigation}})],1):e._e()])])};var a=[];var i=r("3156");var o=r.n(i);var s=r("a34a");var l=r.n(s);var c=r("5dca");var u=r("9af0");var d=r("5a51");var f=r("192a");var p=r("c973");var h=r.n(p);var v=r("3d04");var m=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("div",{staticClass:"ruler-container"})};var g=[];var b=r("9b8e");var y=r("18a5");var w=r("912c");var x=r("b082");var C=r("c098");var S=r.n(C);var k=r("9ec3");var A=r.n(k);var E=r("0fe3");var P=r.n(E);var M=r("359c");var T=r("7341");var I=r("2f26");var O=r("8208");var D=r("970b");var R=r.n(D);var F=r("5bc3");var $=r.n(F);var B=r("b268");var j={ORIENTATION_VERTICAL:"vertical",ORIENTATION_HORIZONTAL:"horizontal"};var N=function(){function e(){var t=this;R()(this,e);this.checked=false;this.bgDrawn=false;this.items=[];this.labels=[];this.sprites=[];this.orderHittest=[];this.getSortArray=[];this.cache=[];window.addEventListener("resize",function(e){if(!t.pixiApp)return;var r=t.getSize(),n=r.width,a=r.height;t.pixiApp.renderer.resize(n,a);t.pixiApp.view.style["max-width"]="".concat(n,"px");t.backgroundContainer.clear();t.bgDrawn=false;t.draw()});this.box={width:120,height:90,cols:2,rows:2,gutter:8}}$()(e,[{key:"updateBox",value:function e(t){if(t&&t.width&&t.height&&t.rows&&t.cols&&t.gutter){this.box=t;if(this.tilecache)this.tilecache.clear();this.geocache=[];this.cache=[]}}},{key:"initializePixi",value:function e(){var t=this;if(this.pixiApp)return;this.labelsCache=new Map;this.labels=[];this.selected={id:-1};var r=this.getSize(),n=r.width,a=r.height;this.ready=false;this.pixiApp=new w["Application"](n,a,{interactionFrequency:3,backgroundColor:2105376,resolution:2});var i=this.pixiApp;this.pixiRoot=new w["Container"];this.pixiHitests=new w["Container"];this.pixiRoot=new w["Container"];this.drawingContainer=new w["Graphics"];this.backgroundContainer=new w["Graphics"];this.pixiRoot.addChild(this.backgroundContainer);this.pixiRoot.addChild(this.drawingContainer);this.pixiApp.stage.addChild(this.pixiRoot);this.pixiApp.stage.addChild(this.pixiHitests);this.vue.$el.appendChild(i.view);this.pixiApp.view.style["max-width"]="".concat(n,"px");this.delta=0;this.drawing=false;this.throttledDraw=A.a.throttle(this.draw,15);this.pixiApp.renderer.plugins.interaction.autoPreventDefault=false;this.pixiApp.stop();x["a"].init(this.pixiApp).then(function(){t.ready=true;t.draw()});this.geocache=[];this.tilecache=new Map}},{key:"git",value:function e(t){var r=t.clientX,n=t.clientY;var a=this.getXY(),i=a.x,o=a.y;var s=r-i;var l=n-o;this.hittests.map(function(e){console.log("rrr",e);if(s>=e.x&&s<=e.x+e.w&&l>=e.y&&l<=e.y+e.h){}});console.log("rrr",s,l)}},{key:"setContext",value:function e(t){var r=this;this.vue=t;this.initializePixi();this.vue.$el.appendChild(this.pixiApp.view);this.delta=0;this.vue.$el.addEventListener("mousewheel",function(e){var t=S()(e);r.delta+=t.pixelY;r.throttledDraw()});this.selected={id:this.vue.$route.params.id};this.draw()}},{key:"getXY",value:function e(){var t=this.vue.$el.getBoundingClientRect();return{x:t.top,y:t.left}}},{key:"getSize",value:function e(){var t=this.vue.$el.getBoundingClientRect();var r=90*2;switch(this.orientation){case j.ORIENTATION_VERTICAL:return{width:r,height:t.height};break;case j.ORIENTATION_HORIZONTAL:this.maxBlocks=Math.round(t.width/this.box.width)*this.box.rows;this.maxBlocks=this.maxBlocks%this.box.rows==0?this.maxBlocks:this.maxBlocks+this.box.rows;return{width:t.width,height:r};break;default:this.maxBlocks=Math.round(t.width/this.box.width)*this.box.rows;this.maxBlocks=this.maxBlocks%this.box.rows==0?this.maxBlocks:this.maxBlocks+this.box.rows;return{width:t.width,height:r};break}}},{key:"selectElement",value:function e(t){if(t.type=="element")return;var r=t;var n="/".concat(t.type=="mesh"?"meshes":"components","/").concat(this.vue.$route.params.selectedCollection,"/").concat(t.item.id,"/");if(t.type=="componentRelations"){n="/components/".concat(this.vue.$route.params.selectedCollection,"/").concat(t.item.parent,"/").concat(t.item.id,"/")}this.vue.$router.push(n);this.selected={id:t.item.id};this.draw();console.log(n)}},{key:"getElementPosition",value:function e(t){var r=this.box.gutter;var n=this.box.width;var a=this.box.height;var i=this.box.cols;var s=this.box.rows;var l=i*s;var c=function e(t){var o=Math.floor(t/l);var c=t-o*l;var u=Math.floor(c/s);var d=c-u*s;var f=Math.round(t/l);return{x:u*n+o*n*i+r/2,y:d*a+r/2}};return o()({},c(t),{w:n-r,h:a-r})}},{key:"drawItem",value:function e(t,r,n){var a=this;var i=new w["Graphics"];var o=this.getElementPosition(t),s=o.x,l=o.y,c=o.w,u=o.h;var d=r.id==this.vue.$route.params.id;if(!d&&this.vue.$route.params){}i.beginFill(d?4473924:1118481).drawRect(0,0,c,u).endFill();Object(B["a"])(i,c,u,{item:r,type:n},"palette");var f=A.a.truncate(r.name,{omission:"-",length:this.box.width/10});if(r.name.length>20){f+="\n"+r.name.substring(this.box.width/10,r.name.length)}i.addChild(this.drawText(f+"\n",0,0,"".concat(r.name).concat(r.id).concat(n).concat(d)));i.hitArea=new w["Rectangle"](0,0,c,u);i.position.x=s;i.position.y=l;i.interactive=true;i.buttonMode=true;i.cursor="pointer";var p={type:n,id:r.id};var h=P.a.h32("".concat(p.id).concat(p.type).concat(d),43981).toString(16);var v=function e(t,r){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;return y["a"].services.miniatures.add({data:t,id:r,fetched:n}).then(function(e){console.log("parsedGeo",e);var t=new w["BaseTexture"](e.painterState);t._sourceLoaded();var o=new w["Texture"](t);o.defaultAnchor={x:0,y:0};a.cache[h]=o;var s=new w["Sprite"](o);i.addChildAt(s,0);i.cacheAsBitmap=true;if(!n)y["a"].api.updateComponentThumb(r,{thumbnails_data:e.geo})})};if(this.geocache[h]){var m=new w["Sprite"](this.cache[h]);i.addChild(m)}else{var g=null;if(n=="componentRelations"||n=="component-mini"){console.log("componentRelations",r);if(r.thumbnails_data){var b=r.thumbnails_data;this.geocache[h]=b;v(b,r.id,true)}else{y["a"].api.geo.componentSet({type:"componentRelations",id:r.id,special:this.specialSettings}).forceFetch(true).pipe(function(e){console.log("RRR",e);a.geocache[h]=e;v(e,r.id)},"paletteRelations")}}else{this.geocache[h]=true;this.cache[h]=i;i.cacheAsBitmap=true}}var x=function e(){return function(e){var t=a.startSprite;if(!t)return;if(t.delta){}else if(t.delta===false&&a.pixiApp){var r=t.data;var n=t.data.getLocalPosition(a.pixiApp.stage);var i=t.startPosition;var o=Math.sqrt(Math.pow(n.y-i.y,2)+Math.pow(n.x-i.x,2));if(o>100){t.delta=true}}e.data.originalEvent.preventDefault()}};var _=function e(t,r){return function(){if(!a.startSprite)return;a.startSprite.dragging=false;if(a.startSprite.delta!=true&&a.startItem){if(a.actAsNavigation!=false){if(a.startSprite.wasClicked){a.selectElement({item:a.startItem,type:n});a.throttledDraw()}else{var e=a.startSprite;e.wasClicked=true;setTimeout(function(){e.wasClicked=false},300)}}}a.startItem=null;a.startSprite=null}};var C=function e(t,r,n,i){return function(e){console.log(123,e);a.startSprite=n;a.startItem=i;n.delta=false;n.data=e.data;n.startPosition=n.data.getLocalPosition(a.pixiApp.stage);a.selected={id:t};y["a"].application.dnd.dragElement(a.vue.dragId,t,r,e.data.originalEvent)}};i.on("mousedown",C(r.id,n,i,r)).on("mouseup",_(r,i)).on("mouseupoutside",_(r,i));i.on("mousemove",x(i));i.on("rightdown",function(e){e.data.originalEvent.preventDefault();e.data.originalEvent.stopPropagation();y["a"].application.contextMenu.show(e,r,"palette")});this.pixiHitests.addChild(i);this.sprites.push(i);return i}},{key:"transferControl",value:function e(){this.dragging=false}},{key:"draw",value:function e(){var t=this;if(!this.ready)return;this.drawing=true;this.sprites.map(function(e,r){if(e){t.pixiHitests.removeChild(e);t.sprites[r]=null}});this.sprites=this.sprites.filter(Boolean);this.drawingContainer.clear();var r=this.getSize(),n=r.width,a=r.height,i=r.rows;if(!this.bgDrawn){var o=10;for(var s=0;s<n/o;s++){for(var l=0;l<a/o;l++){this.backgroundContainer.lineStyle(1,0,1).drawRect(s*o,l*o,1,1)}}this.bgDrawn=true}this.drawing=false;if(this.vue.source){var c=0,u=this.maxBlocks+1,d=this.vue.source.length-1,f=Math.ceil(this.delta/50);f*=this.box.rows,f<0?(f=0,this.delta=0):false;this.hittests=[];for(var p=0,h=P.a.h32(4660);p<u-1;p++){if(f+c>d)return;var v=c-f;var m=this.vue.source[f+c];var g=m.item;var b={type:m.type};var y=this.getElementPosition(c),w=y.x,x=y.y,_=y.w,C=y.h;this.hittests.push({x:w,y:x,w:_,h:C,no:c});var S=h.update("".concat(g.id).concat(b.type).concat(g.id==this.selected.id)).digest().toString(16);if(this.tilecache.has(S)){var k=this.tilecache.get(S);this.sprites.push(k);var A=this.getElementPosition(c),E=A.x,M=A.y;k.position.x=E;k.position.y=M;this.pixiHitests.addChild(k)}else{var T=this.drawItem(c,g,b.type);this.tilecache.set(S,T)}c++}}}},{key:"drawText",value:function e(t,r,n,a){var i=this.pixiApp;var o;a=P.a.h32(a,57034).toString(16);if(this.labelsCache.has(a)){o=this.labelsCache.get(a)}else{o=x["a"].create(t);this.labelsCache.set(a,o)}o.position.set(r,n);return o}}]);return e}();var L=new N;var q=P.a.h32(43981);var z=q.update("abcd").digest().toString(16);var H={ORIENTATION_VERTICAL:"vertical",ORIENTATION_HORIZONTAL:"horizontal"};var W={name:"TylkoRuler",props:["box","actAsNavigation","source","orientation","steps","meshWidthBlend"],methods:{},watch:{source:function e(){if(L){L.draw();L.pixiApp.render();L.pixiApp.start()}},box:function e(){if(L){L.updateBox(this.box);L.draw()}},actAsNavigation:function e(){if(L){L.actAsNavigation=this.actAsNavigation;L.draw()}}},data:function e(){return{selectedStep:0,labels:[],sprites:[],cache:[]}},updated:function e(){L.pixiApp.render();L.pixiApp.start()},mounted:function e(){this.dragId=y["a"].application.dnd.addSourceComponent({type:"palette",instance:this}).dragSourceID;L.setContext(this);L.updateBox(this.box)},created:function e(){}};var U=W;var X=r("887f");var V=r("2877");var G=Object(V["a"])(U,m,g,false,null,"24fbf5f8",null);var Y=G.exports;var Z=r("7b01");var J={200:"A",300:"B",400:"C",500:"D",600:"E",700:"F",800:"G",900:"H",1000:"I",1100:"J",1200:"K"};var K={name:"Tylko-Bar",props:["typesToDisplay","box","actAsNavigation","mode","behaviour","selected","menu","setActiveCollection","palette"],data:function e(){return{projectFilter:"name-2",collections:[],selectOptions:[{label:"All",value:-1},{label:"Other",value:-2}],userFilter:[{label:"All",value:-1}],sources:[],users:[],elements:[],components:[],meshes:[],search:"",filteredSources:[]}},components:{"new-palette":Y},watch:{userFilter:function e(){this.build()},projectFilter:function e(){this.build()},search:function e(){this.build()},selected:function e(){this.build()}},methods:{reflesh:function e(){},reloadAfterCreation:function e(){this.load()},changeCollection:function e(){this.$emit("setActiveCollection",this.projectFilter);if(+this.$route.params.selectedCollection!=+this.projectFilter){this.$router.push("/collection/".concat(this.projectFilter))}},build:function(){var e=h()(l.a.mark(function e(){var t=this;var r,n,a;return l.a.wrap(function e(i){while(1){switch(i.prev=i.next){case 0:r=[];this.sources=[];r=[{list:this.meshes,type:"mesh"},{list:this.elements,type:"element"},{list:this.components,type:"component"},{list:this.tables,type:"component-table"},{list:this.relationComponents,type:"componentRelations"},{list:this.components,type:"component-mini"}];n=[];this.typesToDisplay.split(",").map(function(e){r.map(function(t){e==t.type?n.push(t):false})});r=n;a=function e(r,n){var a=-1;if(n!="element"){if(t.projectFilter!=-1)t.userFilter.map(function(e){console.log("matchid",e.value,r.owner);if(e.value==-1)a=1;if(e.value==-2&&r.owner==null)a=1;if(e.value==r.owner)a=1})}else{a=1}var i=r.name.toLowerCase().indexOf(t.search.toLowerCase())>-1;var o=String(r.id).indexOf(String(t.search))>-1;return i==1&&a==1||o};r=_.map(r,function(e){return{list:_.filter(e.list,function(t){return a(t,e.type)}),type:e.type}});r=r.map(function(e){var r=e.list.map(function(t){return{item:t,type:e.type}});t.sources=t.sources.concat(r)});this.filteredSources=this.sources;console.log("searchMatch",this.sources);this.$emit("searchMatch",this.search.length>0?this.sources.map(function(e){return e.item.id}):null);case 12:case"end":return i.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),loadObjects:function(){var e=h()(l.a.mark(function e(){var t=this;var r,n,a,i,s,c,u;return l.a.wrap(function e(l){while(1){switch(l.prev=l.next){case 0:l.next=2;return y["a"].api.palette.collection(this.projectFilter).componentSet.componentTable.meshes.fetch();case 2:r=l.sent;n=r.mesh;a=r.component;i=r["component-table"];s=r["component-set"];this.tables=i;this.meshes=n;this.components=s;this.build();l.next=13;return y["a"].api.palette.collection(this.projectFilter).components.thumbs.fetch();case 13:c=l.sent;u=c.component;this.relationComponents=u;this.relationComponents=this.relationComponents.map(function(e){return o()({},e,{name:"".concat(J[e.height]||e.height,": ").concat(e.name)})});console.log("rel",u);y["a"].api.elements.subscribe(function(e){t.elements=e;t.build()});case 19:case"end":return l.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),loadPage:function(){var e=h()(l.a.mark(function e(){var t,r,n;return l.a.wrap(function e(a){while(1){switch(a.prev=a.next){case 0:a.next=2;return y["a"].api.palette.collection().fetch();case 2:t=a.sent;r=t.collection;n=t.user;this.collections=r;n=n.map(function(e){return{label:e.username,value:e.id}});n.unshift({label:"All",value:-1},{label:"Other",value:-2});this.selectOptions=n;this.loadObjects();case 10:case"end":return a.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),load:function(){var e=h()(l.a.mark(function e(){var t=this;var r,n;return l.a.wrap(function e(a){while(1){switch(a.prev=a.next){case 0:a.next=2;return y["a"].api.palette.collection().fetch();case 2:r=a.sent;n=r.collection;this.collections=n;this.projectFilter=this.$route.params.selectedCollection||n[0].id;a.t0=this.$route.matched[1].name;a.next=a.t0==="components"?9:a.t0==="meshes"?11:13;break;case 9:y["a"].api.component(this.$route.params.id).subscribe(function(e){var r=e.collection;t.projectFilter=r;t.loadPage()});return a.abrupt("break",14);case 11:y["a"].api.mesh(this.$route.params.id).subscribe(function(e){var r=e.collection;t.projectFilter=r;t.loadPage()});return a.abrupt("break",14);case 13:this.loadPage();case 14:case"end":return a.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}()},updated:function e(){this.reflesh()},mounted:function e(){this.build();this.load()}};var Q=K;var ee=r("2a34");var te=Object(V["a"])(Q,n,a,false,null,null,null);var re=t["a"]=te.exports},b3fd:function(e,t,r){"use strict";var n=r("f367");var a=r("1b7f");e.exports=function e(){var t=a();n(String.prototype,{trim:t},{trim:function(){return String.prototype.trim!==t}});return t}},b5e0:function(e,t,r){"use strict";var n=r("a592");var a=r.n(n);var i=a.a},b636:function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return e.tagOptions.length?r("q-card-main",[r("div",{staticClass:"tag-select-wrapper"},[r("q-select",{attrs:{color:"blue","chips-color":"white","text-color":"white","chips-bg-color":"dark","inverted-light":"inverted-light",multiple:"multiple","use-chips":"use-chips",options:e.tagOptions,"float-label":"Tag collection"},model:{value:e.tagSelection,callback:function(t){e.tagSelection=t},expression:"tagSelection"}})],1)]):e._e()};var a=[];var i=r("f994");var o=r("e3f9");var s=r("a34a");var l=r.n(s);var c=r("4082");var u=r.n(c);var d=r("448a");var f=r.n(d);var p=r("192a");var h=r("c973");var v=r.n(h);var m=r("9523");var g=r.n(m);var b=r("3156");var y=r.n(b);var w=r("359c");var x=r("7341");var _=r("fb2b");var C=r("18a5");var S={name:"Tylko-Setup-Tags",props:["tag_configs","apiUpdateSetup","collection","id"],methods:{handleTagCollection:function e(t,r){var n=this;if(Object.keys(this.tagCollection).length===0)return;if(!this.tagCollection[t])return;var a=this.tagCollection[t],i=a.tag,o=a.id;if(r==="post"){C["a"].api.createTagComponent({id:o,tag:i,component:this.$route.params.setHeight}).then(function(e){n.tagIDs=y()({},n.tagIDs,g()({},e.tag,e.id))}).catch(function(e){return console.log(456,e)})}else if(r==="delete"){C["a"].api.removeTagComponent(this.tagIDs[t]);delete this.tagIDs[t]}}},watch:{id:function(){var e=v()(l.a.mark(function e(){var t,r;return l.a.wrap(function e(n){while(1){switch(n.prev=n.next){case 0:n.next=2;return C["a"].api.palette.collection(this.collection).tags.fetch();case 2:t=n.sent;r=t["tag-collection-config"];this.tagCollection=f()(r).reduce(function(e,t){var r=t.tag,n=u()(t,["tag"]);return y()({},e,g()({},r,y()({tag:r},n)))},{});this.tagOptions=r.map(function(e){var t=e.tag_name,r=e.tag;return{label:t,value:r}});case 6:case"end":return n.stop()}}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),tag_configs:function e(t){var r=this;if(t){this.tagSelection=t.map(function(e){var t=e.tag;return t});t.forEach(function(e){var t=e.tag,n=e.id;r.tagIDs=y()({},r.tagIDs,g()({},t,n))})}},tagSelection:function e(t,r){var n=r.length<t.length;this.handleTagCollection(n?t.filter(function(e){return!r.includes(e)})[0]:r.filter(function(e){return!t.includes(e)})[0],n?"post":"delete")}},data:function e(){return{tagCollection:{},tagOptions:[],tagSelection:[],tagIDs:{}}}};var k=S;var A=r("362c");var E=r("2877");var P=Object(E["a"])(k,n,a,false,null,null,null);var M=t["a"]=P.exports},b8a5:function(e,t){e.exports=r;function r(){var e;var t;var r;return{capture:a,release:i};function a(a){t=window.document.onselectstart;r=window.document.ondragstart;window.document.onselectstart=n;e=a;e.ondragstart=n}function i(){window.document.onselectstart=t;if(e)e.ondragstart=r}}function n(e){e.stopPropagation();return false}},b8c9:function(e,t,r){},bb53:function(e,t){e.exports=function e(t,r){var n=t%r;return Math.floor(n>=0?n:n+r)}},be09:function(e,t,r){(function(t){var r;if(typeof window!=="undefined"){r=window}else if(typeof t!=="undefined"){r=t}else if(typeof self!=="undefined"){r=self}else{r={}}e.exports=r}).call(this,r("c8ba"))},c098:function(e,t,r){e.exports=r("d4af")},c22a:function(e,t){e.exports=n;var r=typeof Event!=="function";function n(e){if(r){var t=document.createEvent("CustomEvent");t.initCustomEvent(e,true,true,undefined);return t}else{return new Event(e,{bubbles:true})}}},c327:function(e,t){e.exports=r;function r(e){var t=e instanceof SVGElement;if(!t){throw new Error("svg element is required for svg.panzoom to work")}var r=e.ownerSVGElement;if(!r){throw new Error("Do not apply panzoom to the root <svg> element. "+"Use its child instead (e.g. <g></g>). "+"As of March 2016 only FireFox supported transform on the root element")}r.setAttribute("tabindex",1);var n={getBBox:i,getScreenCTM:o,getOwner:a,applyTransform:l,initTransform:s};return n;function a(){return r}function i(){var t=e.getBBox();return{left:t.x,top:t.y,width:t.width,height:t.height}}function o(){return r.getScreenCTM()}function s(t){var n=e.getScreenCTM();t.x=n.e;t.y=n.f;t.scale=n.a;r.removeAttributeNS(null,"viewBox")}function l(t){e.setAttribute("transform","matrix("+t.scale+" 0 0 "+t.scale+" "+t.x+" "+t.y+")")}}},c46d:function(e,t,r){"use strict";var n=r("e9ac");var a=n("%TypeError%");var i=n("%SyntaxError%");var o=r("a0d3");var s={"Property Descriptor":function e(t,r){if(t.Type(r)!=="Object"){return false}var n={"[[Configurable]]":true,"[[Enumerable]]":true,"[[Get]]":true,"[[Set]]":true,"[[Value]]":true,"[[Writable]]":true};for(var i in r){if(o(r,i)&&!n[i]){return false}}var s=o(r,"[[Value]]");var l=o(r,"[[Get]]")||o(r,"[[Set]]");if(s&&l){throw new a("Property Descriptors may not be both accessor and data descriptors")}return true}};e.exports=function e(t,r,n,o){var l=s[r];if(typeof l!=="function"){throw new i("unknown record type: "+r)}if(!l(t,o)){throw new a(n+" must be a "+r)}console.log(l(t,o),o)}},c612:function(e,t){var r=Number.isNaN||function(e){return e!==e};e.exports=Number.isFinite||function(e){return typeof e==="number"&&!r(e)&&e!==Infinity&&e!==-Infinity}},c69a6:function(e,t){var r=[66,77,70];e.exports=function e(t){if(t.length<6)throw new Error("invalid buffer length for BMFont");var a=r.every(function(e,r){return t.readUInt8(r)===e});if(!a)throw new Error("BMFont missing BMF byte header");var i=3;var o=t.readUInt8(i++);if(o>3)throw new Error("Only supports BMFont Binary v3 (BMFont App v1.10)");var s={kernings:[],chars:[]};for(var l=0;l<5;l++)i+=n(s,t,i);return s};function n(e,t,r){if(r>t.length-1)return 0;var n=t.readUInt8(r++);var c=t.readInt32LE(r);r+=4;switch(n){case 1:e.info=a(t,r);break;case 2:e.common=i(t,r);break;case 3:e.pages=o(t,r,c);break;case 4:e.chars=s(t,r,c);break;case 5:e.kernings=l(t,r,c);break}return 5+c}function a(e,t){var r={};r.size=e.readInt16LE(t);var n=e.readUInt8(t+2);r.smooth=n>>7&1;r.unicode=n>>6&1;r.italic=n>>5&1;r.bold=n>>4&1;if(n>>3&1)r.fixedHeight=1;r.charset=e.readUInt8(t+3)||"";r.stretchH=e.readUInt16LE(t+4);r.aa=e.readUInt8(t+6);r.padding=[e.readInt8(t+7),e.readInt8(t+8),e.readInt8(t+9),e.readInt8(t+10)];r.spacing=[e.readInt8(t+11),e.readInt8(t+12)];r.outline=e.readUInt8(t+13);r.face=u(e,t+14);return r}function i(e,t){var r={};r.lineHeight=e.readUInt16LE(t);r.base=e.readUInt16LE(t+2);r.scaleW=e.readUInt16LE(t+4);r.scaleH=e.readUInt16LE(t+6);r.pages=e.readUInt16LE(t+8);var n=e.readUInt8(t+10);r.packed=0;r.alphaChnl=e.readUInt8(t+11);r.redChnl=e.readUInt8(t+12);r.greenChnl=e.readUInt8(t+13);r.blueChnl=e.readUInt8(t+14);return r}function o(e,t,r){var n=[];var a=c(e,t);var i=a.length+1;var o=r/i;for(var s=0;s<o;s++){n[s]=e.slice(t,t+a.length).toString("utf8");t+=i}return n}function s(e,t,r){var n=[];var a=r/20;for(var i=0;i<a;i++){var o={};var s=i*20;o.id=e.readUInt32LE(t+0+s);o.x=e.readUInt16LE(t+4+s);o.y=e.readUInt16LE(t+6+s);o.width=e.readUInt16LE(t+8+s);o.height=e.readUInt16LE(t+10+s);o.xoffset=e.readInt16LE(t+12+s);o.yoffset=e.readInt16LE(t+14+s);o.xadvance=e.readInt16LE(t+16+s);o.page=e.readUInt8(t+18+s);o.chnl=e.readUInt8(t+19+s);n[i]=o}return n}function l(e,t,r){var n=[];var a=r/10;for(var i=0;i<a;i++){var o={};var s=i*10;o.first=e.readUInt32LE(t+0+s);o.second=e.readUInt32LE(t+4+s);o.amount=e.readInt16LE(t+8+s);n[i]=o}return n}function c(e,t){var r=t;for(;r<e.length;r++){if(e[r]===0)break}return e.slice(t,r)}function u(e,t){return c(e,t).toString("utf8")}},c9c8:function(e,t,r){(function(t){var n=r("eec7");var a=function(){};var i=r("7ece");var o=r("fad7");var s=r("c69a6");var l=r("04a2");var c=r("53a8");var u=function e(){return self.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}();e.exports=function(e,r){r=typeof r==="function"?r:a;if(typeof e==="string")e={uri:e};else if(!e)e={};var c=e.binary;if(c)e=f(e);n(e,function(n,c,u){if(n)return r(n);if(!/^2/.test(c.statusCode))return r(new Error("http status code: "+c.statusCode));if(!u)return r(new Error("no body result"));var f=false;if(d(u)){var p=new Uint8Array(u);u=new t(p,"binary")}if(l(u)){f=true;if(typeof u==="string")u=new t(u,"binary")}if(!f){if(t.isBuffer(u))u=u.toString(e.encoding);u=u.trim()}var h;try{var v=c.headers["content-type"];if(f)h=s(u);else if(/json/.test(v)||u.charAt(0)==="{")h=JSON.parse(u);else if(/xml/.test(v)||u.charAt(0)==="<")h=o(u);else h=i(u)}catch(m){r(new Error("error parsing font "+m.message));r=a}r(null,h)})};function d(e){var t=Object.prototype.toString;return t.call(e)==="[object ArrayBuffer]"}function f(e){if(u)return c(e,{responseType:"arraybuffer"});if(typeof self.XMLHttpRequest==="undefined")throw new Error("your browser does not support XHR loading");var t=new self.XMLHttpRequest;t.overrideMimeType("text/plain; charset=x-user-defined");return c({xhr:t},e)}}).call(this,r("b639").Buffer)},ca9f:function(e,t,r){"use strict";var n=r("0f7c");var a=r("f367");var i=r("562e");var o=r("1b7f");var s=r("b3fd");var l=n.call(Function.call,o());a(l,{getPolyfill:o,implementation:i,shim:s});e.exports=l},cc6a:function(e,t,r){"use strict";var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return e.series.length?r("div",{staticClass:"card-main"},[r("h5",{staticClass:"series-heading"},[e._v("Series")]),r("div",{staticClass:"flex-series"},e._l(e.series,function(t,n){return r("q-select",{key:n,attrs:{"float-label":t.label,options:t.items,color:"white",leftTextColor:"white",leftColor:"white",rightTextColor:"white"},on:{input:e.dispatchItems},model:{value:e.select[n],callback:function(t){e.$set(e.select,n,t)},expression:"select[key]"}})}),1)]):e._e()};var a=[];var i=r("9523");var o=r.n(i);var s=r("3156");var l=r.n(s);var c=r("448a");var u=r.n(c);var d=r("9af0");var f=r("359c");var p=r("18a5");var h={name:"Tylko-Setup-Table",props:["configurations","apiUpdateSetup","series_pick"],methods:{dispatchItems:function e(){var t=[];for(var r in this.select){if(this.select.hasOwnProperty(r)){t.push(this.select[r])}}this.$emit("apiUpdateSetup","series_pick",t)}},watch:{configurations:function e(t){var r=this;this.series=[];this.select={};t.forEach(function(e,t){var n=e.component,a=e.table;if(!n&&a){p["a"].api.componentTable(a).subscribe(function(e){var n=e.name,a=e.configurations;r.series=[].concat(u()(r.series),[{label:n,items:[{label:"",value:null}].concat(u()(a.map(function(e){var t=e.nested_object_name,r=e.series;return{label:t,value:r}})))}]);r.select=l()({},r.select,o()({},t,r.series_pick.length?r.series_pick[t]:null))})}})}},mounted:function e(){console.log(this)},data:function e(){return{series:[],configurations:{},select:{}}}};var v=h;var m=r("8fd2");var g=r("2877");var b=Object(g["a"])(v,n,a,false,null,null,null);var y=t["a"]=b.exports},ce2d:function(e,t,r){"use strict";var n=r("d2f8");var a=r.n(n);var i=a.a},ce89:function(e,t,r){"use strict";var n=r("6fa4");var a=r.n(n);var i=a.a},d024:function(e,t,r){"use strict";var n=r("21d0");var a=Object.prototype.toString;var i=Object.prototype.hasOwnProperty;var o=function e(t,r,n){for(var a=0,o=t.length;a<o;a++){if(i.call(t,a)){if(n==null){r(t[a],a,t)}else{r.call(n,t[a],a,t)}}}};var s=function e(t,r,n){for(var a=0,i=t.length;a<i;a++){if(n==null){r(t.charAt(a),a,t)}else{r.call(n,t.charAt(a),a,t)}}};var l=function e(t,r,n){for(var a in t){if(i.call(t,a)){if(n==null){r(t[a],a,t)}else{r.call(n,t[a],a,t)}}}};var c=function e(t,r,i){if(!n(r)){throw new TypeError("iterator must be a function")}var c;if(arguments.length>=3){c=i}if(a.call(t)==="[object Array]"){o(t,r,c)}else if(typeof t==="string"){s(t,r,c)}else{l(t,r,c)}};e.exports=c},d110:function(e,t,r){"use strict";if(r("e2e5")){var n=r("8b78");var a=r("f861");var i=r("c0f6");var o=r("22fe");var s=r("189f");var l=r("edee");var c=r("0e26");var u=r("9a92");var d=r("5a3a");var f=r("1f03");var p=r("1385");var h=r("e288");var v=r("d7d0");var m=r("8445");var g=r("d744");var b=r("1008");var y=r("d8a8");var w=r("16d7");var x=r("2b84");var _=r("a911");var C=r("b1cb");var S=r("84c3");var k=r("b244");var A=r("ba1d").f;var E=r("2b52");var P=r("4509");var M=r("0536");var T=r("9724");var I=r("c33d");var O=r("508a");var D=r("7341");var R=r("781d");var F=r("5db0");var $=r("d01d");var B=r("afb7");var j=r("2ae9");var N=r("98ab");var L=r("dc2d");var q=N.f;var z=L.f;var H=a.RangeError;var W=a.TypeError;var U=a.Uint8Array;var X="ArrayBuffer";var V="Shared"+X;var G="BYTES_PER_ELEMENT";var Y="prototype";var Z=Array[Y];var J=l.ArrayBuffer;var K=l.DataView;var Q=T(0);var ee=T(2);var te=T(3);var re=T(4);var ne=T(5);var ae=T(6);var ie=I(true);var oe=I(false);var se=D.values;var le=D.keys;var ce=D.entries;var ue=Z.lastIndexOf;var de=Z.reduce;var fe=Z.reduceRight;var pe=Z.join;var he=Z.sort;var ve=Z.slice;var me=Z.toString;var ge=Z.toLocaleString;var be=M("iterator");var ye=M("toStringTag");var we=P("typed_constructor");var xe=P("def_constructor");var _e=s.CONSTR;var Ce=s.TYPED;var Se=s.VIEW;var ke="Wrong length!";var Ae=T(1,function(e,t){return Ie(O(e,e[xe]),t)});var Ee=i(function(){return new U(new Uint16Array([1]).buffer)[0]===1});var Pe=!!U&&!!U[Y].set&&i(function(){new U(1).set({})});var Me=function(e,t){var r=h(e);if(r<0||r%t)throw H("Wrong offset!");return r};var Te=function(e){if(x(e)&&Ce in e)return e;throw W(e+" is not a typed array!")};var Ie=function(e,t){if(!(x(e)&&we in e)){throw W("It is not a typed array constructor!")}return new e(t)};var Oe=function(e,t){return De(O(e,e[xe]),t)};var De=function(e,t){var r=0;var n=t.length;var a=Ie(e,n);while(n>r)a[r]=t[r++];return a};var Re=function(e,t,r){q(e,t,{get:function(){return this._d[r]}})};var Fe=function e(t){var r=_(t);var n=arguments.length;var a=n>1?arguments[1]:undefined;var i=a!==undefined;var o=E(r);var s,l,u,d,f,p;if(o!=undefined&&!C(o)){for(p=o.call(r),u=[],s=0;!(f=p.next()).done;s++){u.push(f.value)}r=u}if(i&&n>2)a=c(a,arguments[2],2);for(s=0,l=v(r.length),d=Ie(this,l);l>s;s++){d[s]=i?a(r[s],s):r[s]}return d};var $e=function e(){var t=0;var r=arguments.length;var n=Ie(this,r);while(r>t)n[t]=arguments[t++];return n};var Be=!!U&&i(function(){ge.call(new U(1))});var je=function e(){return ge.apply(Be?ve.call(Te(this)):Te(this),arguments)};var Ne={copyWithin:function e(t,r){return j.call(Te(this),t,r,arguments.length>2?arguments[2]:undefined)},every:function e(t){return re(Te(this),t,arguments.length>1?arguments[1]:undefined)},fill:function e(t){return B.apply(Te(this),arguments)},filter:function e(t){return Oe(this,ee(Te(this),t,arguments.length>1?arguments[1]:undefined))},find:function e(t){return ne(Te(this),t,arguments.length>1?arguments[1]:undefined)},findIndex:function e(t){return ae(Te(this),t,arguments.length>1?arguments[1]:undefined)},forEach:function e(t){Q(Te(this),t,arguments.length>1?arguments[1]:undefined)},indexOf:function e(t){return oe(Te(this),t,arguments.length>1?arguments[1]:undefined)},includes:function e(t){return ie(Te(this),t,arguments.length>1?arguments[1]:undefined)},join:function e(t){return pe.apply(Te(this),arguments)},lastIndexOf:function e(t){return ue.apply(Te(this),arguments)},map:function e(t){return Ae(Te(this),t,arguments.length>1?arguments[1]:undefined)},reduce:function e(t){return de.apply(Te(this),arguments)},reduceRight:function e(t){return fe.apply(Te(this),arguments)},reverse:function e(){var t=this;var r=Te(t).length;var n=Math.floor(r/2);var a=0;var i;while(a<n){i=t[a];t[a++]=t[--r];t[r]=i}return t},some:function e(t){return te(Te(this),t,arguments.length>1?arguments[1]:undefined)},sort:function e(t){return he.call(Te(this),t)},subarray:function e(t,r){var n=Te(this);var a=n.length;var i=g(t,a);return new(O(n,n[xe]))(n.buffer,n.byteOffset+i*n.BYTES_PER_ELEMENT,v((r===undefined?a:g(r,a))-i))}};var Le=function e(t,r){return Oe(this,ve.call(Te(this),t,r))};var qe=function e(t){Te(this);var r=Me(arguments[1],1);var n=this.length;var a=_(t);var i=v(a.length);var o=0;if(i+r>n)throw H(ke);while(o<i)this[r+o]=a[o++]};var ze={entries:function e(){return ce.call(Te(this))},keys:function e(){return le.call(Te(this))},values:function e(){return se.call(Te(this))}};var He=function(e,t){return x(e)&&e[Ce]&&typeof t!="symbol"&&t in e&&String(+t)==String(t)};var We=function e(t,r){return He(t,r=b(r,true))?d(2,t[r]):z(t,r)};var Ue=function e(t,r,n){if(He(t,r=b(r,true))&&x(n)&&y(n,"value")&&!y(n,"get")&&!y(n,"set")&&!n.configurable&&(!y(n,"writable")||n.writable)&&(!y(n,"enumerable")||n.enumerable)){t[r]=n.value;return t}return q(t,r,n)};if(!_e){L.f=We;N.f=Ue}o(o.S+o.F*!_e,"Object",{getOwnPropertyDescriptor:We,defineProperty:Ue});if(i(function(){me.call({})})){me=ge=function e(){return pe.call(this)}}var Xe=p({},Ne);p(Xe,ze);f(Xe,be,ze.values);p(Xe,{slice:Le,set:qe,constructor:function(){},toString:me,toLocaleString:je});Re(Xe,"buffer","b");Re(Xe,"byteOffset","o");Re(Xe,"byteLength","l");Re(Xe,"length","e");q(Xe,ye,{get:function(){return this[Ce]}});e.exports=function(e,t,r,l){l=!!l;var c=e+(l?"Clamped":"")+"Array";var d="get"+e;var p="set"+e;var h=a[c];var g=h||{};var b=h&&k(h);var y=!h||!s.ABV;var _={};var C=h&&h[Y];var E=function(e,r){var n=e._d;return n.v[d](r*t+n.o,Ee)};var P=function(e,r,n){var a=e._d;if(l)n=(n=Math.round(n))<0?0:n>255?255:n&255;a.v[p](r*t+a.o,n,Ee)};var M=function(e,t){q(e,t,{get:function(){return E(this,t)},set:function(e){return P(this,t,e)},enumerable:true})};if(y){h=r(function(e,r,n,a){u(e,h,c,"_d");var i=0;var o=0;var s,l,d,p;if(!x(r)){d=m(r);l=d*t;s=new J(l)}else if(r instanceof J||(p=w(r))==X||p==V){s=r;o=Me(n,t);var g=r.byteLength;if(a===undefined){if(g%t)throw H(ke);l=g-o;if(l<0)throw H(ke)}else{l=v(a)*t;if(l+o>g)throw H(ke)}d=l/t}else if(Ce in r){return De(h,r)}else{return Fe.call(h,r)}f(e,"_d",{b:s,o:o,l:l,e:d,v:new K(s)});while(i<d)M(e,i++)});C=h[Y]=S(Xe);f(C,"constructor",h)}else if(!i(function(){h(1)})||!i(function(){new h(-1)})||!F(function(e){new h;new h(null);new h(1.5);new h(e)},true)){h=r(function(e,r,n,a){u(e,h,c);var i;if(!x(r))return new g(m(r));if(r instanceof J||(i=w(r))==X||i==V){return a!==undefined?new g(r,Me(n,t),a):n!==undefined?new g(r,Me(n,t)):new g(r)}if(Ce in r)return De(h,r);return Fe.call(h,r)});Q(b!==Function.prototype?A(g).concat(A(b)):A(g),function(e){if(!(e in h))f(h,e,g[e])});h[Y]=C;if(!n)C.constructor=h}var T=C[be];var I=!!T&&(T.name=="values"||T.name==undefined);var O=ze.values;f(h,we,true);f(C,Ce,c);f(C,Se,true);f(C,xe,h);if(l?new h(1)[ye]!=c:!(ye in C)){q(C,ye,{get:function(){return c}})}_[c]=h;o(o.G+o.W+o.F*(h!=g),_);o(o.S,c,{BYTES_PER_ELEMENT:t});o(o.S+o.F*i(function(){g.of.call(h,1)}),c,{from:Fe,of:$e});if(!(G in C))f(C,G,t);o(o.P,c,Ne);$(c);o(o.P+o.F*Pe,c,{set:qe});o(o.P+o.F*!I,c,ze);if(!n&&C.toString!=me)C.toString=me;o(o.P+o.F*i(function(){new h(1).slice()}),c,{slice:Le});o(o.P+o.F*(i(function(){return[1,2].toLocaleString()!=new h([1,2]).toLocaleString()})||!i(function(){C.toLocaleString.call([1,2])})),c,{toLocaleString:je});R[c]=I?T:O;if(!n&&!I)f(C,be,O)}}else e.exports=function(){}},d138:function(e,t){var r=4;var n=.001;var a=1e-7;var i=10;var o=11;var s=1/(o-1);var l=typeof Float32Array==="function";function c(e,t){return 1-3*t+3*e}function u(e,t){return 3*t-6*e}function d(e){return 3*e}function f(e,t,r){return((c(t,r)*e+u(t,r))*e+d(t))*e}function p(e,t,r){return 3*c(t,r)*e*e+2*u(t,r)*e+d(t)}function h(e,t,r,n,o){var s,l,c=0;do{l=t+(r-t)/2;s=f(l,n,o)-e;if(s>0){r=l}else{t=l}}while(Math.abs(s)>a&&++c<i);return l}function v(e,t,n,a){for(var i=0;i<r;++i){var o=p(t,n,a);if(o===0){return t}var s=f(t,n,a)-e;t-=s/o}return t}function m(e){return e}e.exports=function e(t,r,a,i){if(!(0<=t&&t<=1&&0<=a&&a<=1)){throw new Error("bezier x values must be in [0, 1] range")}if(t===r&&a===i){return m}var c=l?new Float32Array(o):new Array(o);for(var u=0;u<o;++u){c[u]=f(u*s,t,a)}function d(e){var r=0;var i=1;var l=o-1;for(;i!==l&&c[i]<=e;++i){r+=s}--i;var u=(e-c[i])/(c[i+1]-c[i]);var d=r+u*s;var f=p(d,t,a);if(f>=n){return v(e,d,t,a)}else if(f===0){return d}else{return h(e,r,r+s,t,a)}}return function e(t){if(t===0){return 0}if(t===1){return 1}return f(d(t),r,i)}}},d2f8:function(e,t,r){},d4ab:function(e,t,r){"use strict";var n=Object.prototype.toString;e.exports=function e(t){var r=n.call(t);var a=r==="[object Arguments]";if(!a){a=r!=="[object Array]"&&t!==null&&typeof t==="object"&&typeof t.length==="number"&&t.length>=0&&n.call(t.callee)==="[object Function]"}return a}},d4af:function(e,t,r){"use strict";var n=r("8eb7");var a=r("7b3e");var i=10;var o=40;var s=800;function l(e){var t=0,r=0,n=0,a=0;if("detail"in e){r=e.detail}if("wheelDelta"in e){r=-e.wheelDelta/120}if("wheelDeltaY"in e){r=-e.wheelDeltaY/120}if("wheelDeltaX"in e){t=-e.wheelDeltaX/120}if("axis"in e&&e.axis===e.HORIZONTAL_AXIS){t=r;r=0}n=t*i;a=r*i;if("deltaY"in e){a=e.deltaY}if("deltaX"in e){n=e.deltaX}if((n||a)&&e.deltaMode){if(e.deltaMode==1){n*=o;a*=o}else{n*=s;a*=s}}if(n&&!t){t=n<1?-1:1}if(a&&!r){r=a<1?-1:1}return{spinX:t,spinY:r,pixelX:n,pixelY:a}}l.getEventType=function(){return n.firefox()?"DOMMouseScroll":a("wheel")?"wheel":"mousewheel"};e.exports=l},d6c7:function(e,t,r){"use strict";var n=Array.prototype.slice;var a=r("d4ab");var i=Object.keys;var o=i?function e(t){return i(t)}:r("b189");var s=Object.keys;o.shim=function e(){if(Object.keys){var t=function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2);if(!t){Object.keys=function e(t){if(a(t)){return s(n.call(t))}return s(t)}}}else{Object.keys=o}return Object.keys||o};e.exports=o},d7e8:function(e,t,r){},d9b5:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("section",{staticClass:"main"},[r("t-mesh-editor")],1)};var a=[];var i=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("section",{staticClass:"main"},[r("div",{staticClass:"flex row"}),r("div",[r("t-bar",{ref:"bar",attrs:{menu:"true",selected:this.$route.params.id,actAsNavigation:true,box:{width:170,height:44,cols:2,rows:4,gutter:8},typesToDisplay:"component-table"}})],1),r("div",{staticClass:"full-fixed-height flex row"},[r("div",{staticClass:"col"},[r("div",{staticClass:"flex row full-height items-stretch"},[r("div",{staticClass:"col-12 self-start",staticStyle:{height:"100%","max-height":"180px"}},[r("div",{staticClass:"flex row"},[r("t-setups-bar-mesh",{ref:"setupBar",attrs:{selectedSetupModel:e.selectedSetup,setupList:Object.entries(e.widths).map(function(e){return e[1]})},on:{add:e.addSetup,changeWidth:function(t){e.$refs.preview.model.choosenWidth=t},changeSetup:function(t){e.selectedSetup=t;e.$refs.setupCard.load()}}})],1),r("div",{staticClass:"col-12",staticStyle:{height:"230px"}},[r("Tylko-Editor",{ref:"editor",attrs:{orientation:"horizontal",currentSetup:e.selectedSetupObject,meshMode:"true",currentHeight:e.getSetupValue(e.selectedSetup),mesh:e.mesh},on:{reload:e.reload,reloadPreview:e.reloadPreviewNow,reloadPalette:e.reloadPalette}})],1)]),r("div",{staticClass:"col-12 self-start",staticStyle:{height:"100%"}},[r("Tylko-Designer-Renderer-View",{ref:"preview",attrs:{vwidth:"900",vheight:"500",element:e.mesh,decoderId:e.selectedId,decoderType:"mesh",selectedSetup:+e.selectedSetup,decoderWidth:parseInt(e.getSetupValue(e.selectedSetup)),meshMode:"meshMode",menu:"menu",freeSetup:e.freeSetup,decoderHeights:e.getAvialableHeights(),distortionMode:e.distortionMode},on:{selectSetupByDecoder:function(t){if(t!=null){e.$refs.setupBar.selectSetupById(t)}},setFreeSetup:e.setFreeSetup}})],1)])]),r("div",{staticClass:"col",staticStyle:{"max-width":"420px",background:"#222"}},[r("q-scroll-area",{staticClass:"cards-bar-area"},[r("div",[r("Tylko-Configuration",{attrs:{meshMode:true,configuration:this.$route.params.selectedConfiguration},on:{deletedConfiguration:e.reload,reloadPreview:e.reloadPreviewNow2,reload:e.reload}})],1),r("div",[r("t-mesh-setup",{ref:"setupCard",attrs:{setup:e.currentSetupIdFormRouter},on:{change:function(t){return e.reload(true)}}})],1),r("div",[r("Tylko-Mesh",{attrs:{meshMode:true},on:{delete:e.deleteit,newSetup:e.addSetup,save:e.saveCurrentConfigs,newParameters:function(t){return e.reload(true)}}})],1)])],1)])])};var o=[];var s=r("1a9d");var l=r("7813");var c=r("5a51");var u=r("d6b6");var d=r("b263");var f=r("3156");var p=r.n(f);var h=r("18a5");var v=r("2f62");var m=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return r("section",[r("t-card",{attrs:{name:"Mesh "+e.mesh.name}},[r("div",{staticClass:"_",attrs:{slot:"options"},slot:"options"},[r("q-list",[r("q-item",[r("t-rpc-action",{attrs:{source:"mesh_"+e.mesh.id,buttonText:"Duplicate",methodName:"duplicate",withInput:"withInput",promptTitle:"Duplicate mesh",promptMessage:"Enter new names"}})],1),r("q-item",[r("router-link",{attrs:{to:"/preview/mesh/"+e.mesh.id+"/"}},[r("q-btn",{attrs:{color:"blue",icon:"remove_red_eye",label:"Preview"}})],1)],1),r("q-item",[r("q-btn",{attrs:{icon:"delete",label:"DELETE",color:"red"},on:{click:e.deleteme}})],1)],1)],1),r("div",{staticClass:"_",attrs:{slot:"links"},slot:"links"},[r("div",{staticClass:"_ display-inline"},[r("router-link",{attrs:{tag:"a",to:"/preview/mesh/"+e.mesh.id+"/"}},[r("q-btn",{attrs:{icon:"remove_red_eye","text-color":"grey",size:"sm",filled:"filled",dense:"dense",color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Preview for mesh ID: "+e._s(e.mesh.id))])],1)],1)],1),r("div",{staticClass:"_ display-inline"},[r("router-link",{attrs:{tag:"a",to:"/configurator/mesh/"+e.mesh.id+"/"}},[r("q-btn",{attrs:{icon:"public","text-color":"grey",size:"sm",filled:"filled",dense:"dense",color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Configurator for mesh ID: "+e._s(e.mesh.id))])],1)],1)],1),r("div",{staticClass:"_ display-inline"},[r("a",{attrs:{href:"/admin/webdesigner/mesh/"+e.mesh.id+"/change/"}},[r("q-btn",{attrs:{icon:"build",dense:"dense","text-color":"grey",size:"sm",color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Mesh ID:"+e._s(e.mesh.id))])],1)],1)])]),r("div",{staticClass:"card mid"},[r("div",{staticClass:"card-main"},[r("q-tabs",{staticClass:"q-px-sm q-pt-none",attrs:{inverted:"inverted","indicator-color":"transparent","active-color":"white","inline-label":"inline-label","switch-indicator":"switch-indicator",align:"left",color:"black"},model:{value:e.meshCardSelectedPanel,callback:function(t){e.meshCardSelectedPanel=t},expression:"meshCardSelectedPanel"}},[r("q-tab",{attrs:{default:"default",label:"base",name:"mainTab",icon:"settings_input_component"}}),r("q-tab",{attrs:{label:"extra",name:"additionalTab",icon:"horizontal_split"}}),r("q-tab",{attrs:{label:"usage",name:"treeTab",icon:"call_split"}}),r("q-tab",{attrs:{label:"info",name:"infoTab",icon:"assignment"}})],1),r("q-tab-panels",{model:{value:e.meshCardSelectedPanel,callback:function(t){e.meshCardSelectedPanel=t},expression:"meshCardSelectedPanel"}},[r("q-tab-panel",{staticStyle:{overflow:"hidden"},attrs:{name:"mainTab"}},[r("t-input",{attrs:{"target-model":e.mesh,neverEmpty:"neverEmpty","target-field":"name","section-label":"Name"},on:{save:function(t){return e.broadcastNewParameters(t,"name")}}}),r("t-input",{attrs:{"target-model":e.mesh,neverEmpty:"neverEmpty","target-field":"dim_x","section-label":"Dim X:"},on:{save:function(t){return e.broadcastNewParameters(t,"dim_x")}}}),r("t-input",{attrs:{"target-model":e.mesh,neverEmpty:"neverEmpty","target-field":"dim_y","section-label":"Dim Y:"},on:{save:function(t){return e.broadcastNewParameters(t,"dim_y")}}}),r("t-input",{attrs:{"target-model":e.mesh,neverEmpty:"neverEmpty","target-field":"dim_z","section-label":"Dim Z:"},on:{save:function(t){return e.broadcastNewParameters(t,"dim_z")}}}),r("t-select",{attrs:{"target-model":e.mesh,neverEmpty:"neverEmpty","target-field":"density_mode",options:e.densityOptions,"section-label":"Density:","emit-value":"emit-value"},on:{save:function(t){return e.broadcastNewParameters(t,"density_mode")}}}),r("t-select",{attrs:{"target-model":e.mesh,neverEmpty:"neverEmpty","target-field":"distortion_mode",options:e.distortionOptions,"section-label":"Distortion:","emit-value":"emit-value"},on:{save:function(t){return e.broadcastNewParameters(t,"distortion_mode")}}}),r("t-select",{attrs:{"target-model":e.mesh,neverEmpty:"neverEmpty","target-field":"object_type",options:e.objectTypeOptions,"section-label":"Type:",up:"up","emit-value":"emit-value"},on:{save:function(t){return e.broadcastNewParameters(t,"object_type")}}})],1),r("q-tab-panel",{staticStyle:{padding:"0px"},attrs:{name:"additionalTab",dark:"dark"}},[r("t-sections",[r("t-section",{attrs:{name:"Legs"}},[r("div",{staticClass:"card-content"},[r("t-input",{attrs:{"target-model":e.mesh,"target-field":"legs_height","section-label":"Legs height:"},on:{save:function(t){return e.broadcastNewParameters(t,"legs_height")}}}),r("t-input",{attrs:{"target-model":e.mesh,"target-field":"legs_offset_z","section-label":"Legs Offset Z:"},on:{save:function(t){return e.broadcastNewParameters(t,"legs_offset_z")}}})],1)])],1)],1),r("q-tab-panel",{attrs:{name:"treeTab",dark:"dark"}},[r("q-tree",{attrs:{nodes:e.mesh.tree,dark:"dark","node-key":"label"}})],1),r("q-tab-panel",{attrs:{name:"infoTab",dark:"dark"}},[r("pre",[e._v(e._s(e.mesh.info.info))])])],1)],1),r("div",{staticClass:"card-main q-pl-sm margin-b"},[r("t-tags",{attrs:{"target-model":e.mesh,"target-field":"additional_params","section-label":"Additional parameters","update-model":"update-model"},on:{save:function(t){return e.broadcastNewParameters(e.mesh.additional_params,"additional_params")}}})],1)])])],1)};var g=[];var b=r("39fc");var y={name:"Tylko-Mesh",props:{},components:p()({},b["j"]),watch:{mesh:function e(){this.paramsNotSet=[]}},computed:p()({},Object(v["c"])({mesh:"renderer/mesh"})),methods:p()({},Object(v["b"])({updateMesh:"renderer/updateMesh"}),{broadcastNewParameters:function e(t,r){this.updateMesh({param:r,source:t})},addWidth:function e(){this.$emit("width",this.newWidth)},addSetup:function e(){this.$emit("newSetup")},deleteme:function e(){var t=this;this.$q.dialog({title:"Delete",message:"This action will delete comp",ok:"Ok",cancel:"Cancel"}).then(function(){t.$emit("delete",true);TylkoNotifications.notify(t.$q,"Agreed!")}).catch(function(){TylkoNotifications.notify(t.$q,"Disagreed...")})}}),mounted:function e(){this.objectType=this.mesh.object_type},data:function e(){return{meshCardSelectedPanel:"mainTab",newWidth:0,checked:false,error:true,warning:false,paramsNotSet:[],objectType:"",objectTypeOptions:[{label:"type_01",value:"type_01"},{label:"type_02",value:"type_02"}],densityOptions:[{label:"Off",value:"off"},{label:"Setup range - slider",value:"setup_range_slider"},{label:"Setup range - stepper",value:"setup_range_stepper"}],distortionOptions:[{label:"Off",value:"off"},{label:"Distort like Gradient",value:"gradient"},{label:"Distort by Ratio",value:"ratio"},{label:"Distort Local - X",value:"local_x"},{label:"Distort Local - ALL",value:"local_all"},{label:"Distort by Edge",value:"edge"}]}}};var w=y;var x=r("a5dd");var _=r("2877");var C=Object(_["a"])(w,m,g,false,null,null,null);var S=C.exports;var k=r("732b");var A=function(){var e=this;var t=e.$createElement;var r=e._self._c||t;return e.setup?r("div",{staticClass:"_"},[r("t-card",{attrs:{name:"Setup"}},[r("div",{staticClass:"_",attrs:{slot:"options"},slot:"options"},[r("t-rpc-action",{attrs:{source:"meshsetup_"+e.setup,buttonText:"DUPLICATE",methodName:"duplicate",withInput:"withInput",promptTitle:"Duplicate setup",promptMessage:"Enter new name"}}),r("q-btn",{attrs:{icon:"delete",label:"Delete",color:"red"},on:{click:e.removeSetup}})],1),r("div",{staticClass:"_",attrs:{slot:"links"},slot:"links"},[r("div",{staticClass:"_ display-inline"},[r("a",{attrs:{href:"/admin/webdesigner/meshsetup/"+e.setup+"/change/"}},[r("q-btn",{attrs:{icon:"build","text-color":"grey",size:"sm",filled:"filled",dense:"dense",color:"dark"}},[r("q-tooltip",{attrs:{"content-style":"font-size: 16px",offset:[10,10]}},[e._v("Setup ID:"+e._s(e.setup))])],1)],1)])]),r("div",{staticClass:"card mid"},[r("div",{staticClass:"card-main"},[r("q-tabs",{attrs:{align:"left",dense:"dense","inline-label":"inline-label","indicator-color":"transparent","active-color":"white"},model:{value:e.selectedMode,callback:function(t){e.selectedMode=t},expression:"selectedMode"}},[r("q-tab",{attrs:{default:"default",label:"base",name:"mainTab",icon:"settings_input_component"}}),r("q-tab",{attrs:{label:"extra",name:"additionalTab",icon:"horizontal_split"}})],1),r("q-tab-panels",{model:{value:e.selectedMode,callback:function(t){e.selectedMode=t},expression:"selectedMode"}},[r("q-tab-panel",{attrs:{name:"mainTab"}},[r("t-input",{attrs:{"target-model":e.setupModel,neverEmpty:"neverEmpty","target-field":"name","allow-arbitraty-constat":true,"section-label":"Name",info:"New name for setup"},on:{save:function(t){return e.updatePropInMeshSetupModel({prop:"name",payload:t})}}}),r("t-input",{attrs:{"target-model":e.setupModel,neverEmpty:"neverEmpty","target-field":"dim_x","allow-arbitraty-constat":true,"section-label":"Dim X:",info:"New name for setup"},on:{save:function(t){return e.updatePropInMeshSetupModel({prop:"dim_x",payload:t})}}}),r("t-select",{attrs:{"target-model":e.setupModel,neverEmpty:"neverEmpty","target-field":"distortion_mode","section-label":"Distortion:",options:e.distortionOptions,info:"New name for setup","emit-value":"emit-value",up:"up"},on:{save:function(t){return e.updatePropInMeshSetupModel({prop:"distortion_mode",payload:t})}}})],1),r("q-tab-panel",{staticStyle:{padding:"0px"},attrs:{name:"additionalTab"}},[r("t-sections",[r("t-section",{attrs:{name:"Legs"}},[r("div",{staticClass:"card-content"},[r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.setupModel,"target-field":"legs_positions","allow-arbitraty-constat":true,"section-label":"Position:",info:"New name for setup"},on:{save:function(t){return e.updatePropInMeshSetupModel({prop:"legs_positions",payload:t})}}})],1),r("div",{staticClass:"_"},[r("t-input",{attrs:{"target-model":e.setupModel,"target-field":"legs_offset_x","allow-arbitraty-constat":true,"section-label":"OffsetX:",info:"New name for setup"},on:{save:function(t){return e.updatePropInMeshSetupModel({prop:"legs_offset_x",payload:t})}}})],1)])])],1)],1),r("q-tab-panel",{attrs:{name:"treeTab"}},[r("q-tree",{attrs:{nodes:e.setupModel.tree,dark:"dark","node-key":"label","default-expand-all":"default-expand-all"}})],1)],1)],1),r("div",{staticClass:"card-separator"}),r("div",{staticClass:"card-main q-pl-sm margin-b"},[r("t-tags",{attrs:{"target-model":e.setupModel,"target-field":"additional_params","section-label":"Additional parameters","update-model":"update-model"},on:{save:function(t){return e.updatePropInMeshSetupModel({prop:"additional_params",payload:t})}}})],1)])])],1):e._e()};var E=[];var P=r("e1c2");var M=r("9523");var T=r.n(M);var I=r("9af0");var O=r("cc6a");var D=r("b636");var R={name:"Tylko-Setup",props:{configuration:[String,Number],setup:[String,Number],setFreeSetup:[String,Boolean],freeSetup:[String,Boolean],activeCollection:[String,Boolean,Number]},components:p()({},b["j"],{TylkoSetupTable:O["a"],TylkoSetupTags:D["a"]}),watch:{setupModel:function e(t){console.log(123,t,"setupModel");this.additionalParams=t.additional_params;this.setupName=t.name},newHeight:function e(){},configuration:function e(){},setup:function e(){this.load()}},methods:p()({},Object(v["b"])({updateMeshSetupModel:"renderer/updateMeshSetupModel",updatePropInMeshSetupModel:"renderer/updatePropInMeshSetupModel",removeMeshSetup:"renderer/removeMeshSetup"}),{apiUpdateSetup:function e(t,r,n){var a=this;h["a"].api.updateMeshSetup(+this.setup,T()({},t,r)).subscribe(n?n:function(){a.$emit("change");a.load()})},removeSetup:function e(){var t=this;var r=parseInt(this.setup);this.$q.dialog({title:"Remove setup",message:"Remove setup?",ok:"Yes",cancel:"Return"}).onOk(function(){h["a"].api.removeMeshSetup(r).subscribe(function(e){console.log(123,e);t.$emit("removedSetup")});TylkoNotifications.notify(t.$q,"Removing setup #".concat(r,"..."))})},load:function e(){console.log(123,"działa setup model");this.updateMeshSetupModel(parseInt(this.setup))}}),computed:p()({},Object(v["c"])({setupModel:"renderer/setupModel"})),mounted:function e(){this.load()},data:function e(){Object.assign(this,{distortionOptions:[{label:"Off",value:"off"},{label:"Gradient",value:"gradient"},{label:"Ratio",value:"ratio"}]});return{selectedMode:"mainTab",newHeight:0,newWidthValue:0,newWidthRatio:0,setupName:"Loading...",additionalParams:[]}}};var F=R;var $=r("b5e0");var B=Object(_["a"])(F,A,E,false,null,null,null);var j=B.exports;var N=r("498a");var L=r("b2ec");var q=r("4dae");var z=r("7b2c");var H=r("9ec3");var W=r.n(H);var U={name:"PageComponenet",components:{"t-bar":L["a"],TylkoEditor:q["a"],TylkoDesignerRendererView:z["a"],TylkoConfiguration:N["a"],TylkoMesh:S,"t-mesh-setup":j,"t-setups-bar-mesh":b["h"]},computed:p()({},Object(v["c"])({mesh:"renderer/mesh",widths:"renderer/widths"}),{selectedSetup:{get:function e(){return this.$store.state.renderer.selectedSetup},set:function e(t){this.$store.dispatch("renderer/updateSelectedSetup",t)}},finalBreadcrumb:function e(){this.selectedWidth=this.$route.params.setHeight;return"/meshes/".concat(this.$route.params.selectedCollection,"/")+this.$route.params.id},currentSetupIdFormRouter:function e(){return this.$route.params.setWidth},selectedSetupObject:function e(){return{id:this.selectedSetup,label:this.getSetupValue(this.selectedSetup)}},selectedId:function e(){return this.$route.params.id}}),mounted:function e(){h["a"].application.viewport.fixed(true);this.updateIdParam(this.$route.params.id);this.load()},watch:{mesh:function e(){this.reloadPreviewNow()},$route:function e(t,r){if(t.params.id!=r.params.id){this.load()}},selectedSetup:function e(t,r){if(t==r)return;if(this.selectedSetup){this.$router.push({path:"/meshes/".concat(this.$route.params.selectedCollection,"/")+this.$route.params.id+"/"+this.selectedSetup})}}},methods:p()({},Object(v["b"])({loadMesh:"renderer/loadMesh",updateIdParam:"renderer/updateIdParam",updateMeshSetupModel:"renderer/updateMeshSetupModel"}),{distortionVisibility:function e(t){this.distortionMode=t==="off"?false:true},onSelectSetup:function e(t){var r=Number(this.widths.filter(function(e){return e.id===t})[0].label);this.$refs.preview.changeElementWidth(r)},setFreeSetup:function e(t){this.freeSetup=t;h["a"].application.settings.set("freeSetup",t)},switchSetupTab:function e(t){var r=this.widths.map(function(e){return Number(e.label)}).filter(function(e){return e<=t});var n=r.length?this.widths.filter(function(e){return e.label==r[r.length-1]})[0].id:this.widths[0].id;if(this.selectedSetup===n){}else{this.selectedSetup=n;this.updateMeshSetupModel()}},reload:function e(t){if(t===true){this.reloadPreviewNow()}this.load()},addSetup:function e(){var t=this;this.$q.dialog({title:"New Setup",message:"Enter setup name",position:"top",prompt:{model:"",type:"text"},cancel:true,color:"primary"}).onOk(function(e){h["a"].api.createMeshSetup({name:e,parent:t.mesh.id}).then(function(e){t.reload()});h["a"].application.notifcations.notify(t.$q,'Creating setup "'.concat(e,'"...'))})},reloadPalette:function e(){this.$refs.bar.restock()},getAvialableHeights:function e(){if(!this.mesh)return[];var t=this.mesh.dim_y.split(",").map(function(e){return e.replace(/\s/g,"")});return t},getSetupValue:function e(t){var r=W.a.find(this.widths,{id:t});return r?r.label:""},reloadPreviewNow:function e(){this.$refs.preview.reload()},reloadPreviewNow2:function e(){this.$refs.preview.reload()},saveCurrentConfigs:function e(){this.$refs.editor.saveCurrentConfigs()},deleteit:function e(){var t=this;h["a"].api.removeMesh(this.mesh.id).subscribe(function(e){t.$router.push("/")})},load:function e(t){this.loadMesh({meshId:this.$route.params.id,width:this.$route.params.setWidth,isNew:t})}}),data:function e(){return{freeSetup:h["a"].application.settings.get("freeSetup"),checked:false,selectedWidth:"0",distortionMode:false}}};var X=U;var V=r("554b");var G=r("39dd");var Y=Object(_["a"])(X,i,o,false,null,"3c83c282",null);var Z=Y.exports;var J={name:"PageComponenet",components:{"t-mesh-editor":Z},data:function e(){return{checked:false}}};var K=J;var Q=r("91e0");var ee=Object(_["a"])(K,n,a,false,null,"1f311292",null);var te=t["default"]=ee.exports},daae:function(e,t,r){},dd40:function(e,t){e.exports=function(e){if(!e.webpackPolyfill){var t=Object.create(e);if(!t.children)t.children=[];Object.defineProperty(t,"loaded",{enumerable:true,get:function(){return t.l}});Object.defineProperty(t,"id",{enumerable:true,get:function(){return t.i}});Object.defineProperty(t,"exports",{enumerable:true});t.webpackPolyfill=1}return t}},e9ac:function(e,t,r){"use strict";var n;var a=Object.getOwnPropertyDescriptor?function(){return Object.getOwnPropertyDescriptor(arguments,"callee").get}():function(){throw new TypeError};var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol";var o=Object.getPrototypeOf||function(e){return e.__proto__};var s;var l=s?o(s):n;var c;var u=c?c.constructor:n;var d;var f=d?o(d):n;var p=d?d():n;var h=typeof Uint8Array==="undefined"?n:o(Uint8Array);var v={"$ %Array%":Array,"$ %ArrayBuffer%":typeof ArrayBuffer==="undefined"?n:ArrayBuffer,"$ %ArrayBufferPrototype%":typeof ArrayBuffer==="undefined"?n:ArrayBuffer.prototype,"$ %ArrayIteratorPrototype%":i?o([][Symbol.iterator]()):n,"$ %ArrayPrototype%":Array.prototype,"$ %ArrayProto_entries%":Array.prototype.entries,"$ %ArrayProto_forEach%":Array.prototype.forEach,"$ %ArrayProto_keys%":Array.prototype.keys,"$ %ArrayProto_values%":Array.prototype.values,"$ %AsyncFromSyncIteratorPrototype%":n,"$ %AsyncFunction%":u,"$ %AsyncFunctionPrototype%":u?u.prototype:n,"$ %AsyncGenerator%":d?o(p):n,"$ %AsyncGeneratorFunction%":f,"$ %AsyncGeneratorPrototype%":f?f.prototype:n,"$ %AsyncIteratorPrototype%":p&&i&&Symbol.asyncIterator?p[Symbol.asyncIterator]():n,"$ %Atomics%":typeof Atomics==="undefined"?n:Atomics,"$ %Boolean%":Boolean,"$ %BooleanPrototype%":Boolean.prototype,"$ %DataView%":typeof DataView==="undefined"?n:DataView,"$ %DataViewPrototype%":typeof DataView==="undefined"?n:DataView.prototype,"$ %Date%":Date,"$ %DatePrototype%":Date.prototype,"$ %decodeURI%":decodeURI,"$ %decodeURIComponent%":decodeURIComponent,"$ %encodeURI%":encodeURI,"$ %encodeURIComponent%":encodeURIComponent,"$ %Error%":Error,"$ %ErrorPrototype%":Error.prototype,"$ %eval%":eval,"$ %EvalError%":EvalError,"$ %EvalErrorPrototype%":EvalError.prototype,"$ %Float32Array%":typeof Float32Array==="undefined"?n:Float32Array,"$ %Float32ArrayPrototype%":typeof Float32Array==="undefined"?n:Float32Array.prototype,"$ %Float64Array%":typeof Float64Array==="undefined"?n:Float64Array,"$ %Float64ArrayPrototype%":typeof Float64Array==="undefined"?n:Float64Array.prototype,"$ %Function%":Function,"$ %FunctionPrototype%":Function.prototype,"$ %Generator%":s?o(s()):n,"$ %GeneratorFunction%":l,"$ %GeneratorPrototype%":l?l.prototype:n,"$ %Int8Array%":typeof Int8Array==="undefined"?n:Int8Array,"$ %Int8ArrayPrototype%":typeof Int8Array==="undefined"?n:Int8Array.prototype,"$ %Int16Array%":typeof Int16Array==="undefined"?n:Int16Array,"$ %Int16ArrayPrototype%":typeof Int16Array==="undefined"?n:Int8Array.prototype,"$ %Int32Array%":typeof Int32Array==="undefined"?n:Int32Array,"$ %Int32ArrayPrototype%":typeof Int32Array==="undefined"?n:Int32Array.prototype,"$ %isFinite%":isFinite,"$ %isNaN%":isNaN,"$ %IteratorPrototype%":i?o(o([][Symbol.iterator]())):n,"$ %JSON%":JSON,"$ %JSONParse%":JSON.parse,"$ %Map%":typeof Map==="undefined"?n:Map,"$ %MapIteratorPrototype%":typeof Map==="undefined"||!i?n:o((new Map)[Symbol.iterator]()),"$ %MapPrototype%":typeof Map==="undefined"?n:Map.prototype,"$ %Math%":Math,"$ %Number%":Number,"$ %NumberPrototype%":Number.prototype,"$ %Object%":Object,"$ %ObjectPrototype%":Object.prototype,"$ %ObjProto_toString%":Object.prototype.toString,"$ %ObjProto_valueOf%":Object.prototype.valueOf,"$ %parseFloat%":parseFloat,"$ %parseInt%":parseInt,"$ %Promise%":typeof Promise==="undefined"?n:Promise,"$ %PromisePrototype%":typeof Promise==="undefined"?n:Promise.prototype,"$ %PromiseProto_then%":typeof Promise==="undefined"?n:Promise.prototype.then,"$ %Promise_all%":typeof Promise==="undefined"?n:Promise.all,"$ %Promise_reject%":typeof Promise==="undefined"?n:Promise.reject,"$ %Promise_resolve%":typeof Promise==="undefined"?n:Promise.resolve,"$ %Proxy%":typeof Proxy==="undefined"?n:Proxy,"$ %RangeError%":RangeError,"$ %RangeErrorPrototype%":RangeError.prototype,"$ %ReferenceError%":ReferenceError,"$ %ReferenceErrorPrototype%":ReferenceError.prototype,"$ %Reflect%":typeof Reflect==="undefined"?n:Reflect,"$ %RegExp%":RegExp,"$ %RegExpPrototype%":RegExp.prototype,"$ %Set%":typeof Set==="undefined"?n:Set,"$ %SetIteratorPrototype%":typeof Set==="undefined"||!i?n:o((new Set)[Symbol.iterator]()),"$ %SetPrototype%":typeof Set==="undefined"?n:Set.prototype,"$ %SharedArrayBuffer%":typeof SharedArrayBuffer==="undefined"?n:SharedArrayBuffer,"$ %SharedArrayBufferPrototype%":typeof SharedArrayBuffer==="undefined"?n:SharedArrayBuffer.prototype,"$ %String%":String,"$ %StringIteratorPrototype%":i?o(""[Symbol.iterator]()):n,"$ %StringPrototype%":String.prototype,"$ %Symbol%":i?Symbol:n,"$ %SymbolPrototype%":i?Symbol.prototype:n,"$ %SyntaxError%":SyntaxError,"$ %SyntaxErrorPrototype%":SyntaxError.prototype,"$ %ThrowTypeError%":a,"$ %TypedArray%":h,"$ %TypedArrayPrototype%":h?h.prototype:n,"$ %TypeError%":TypeError,"$ %TypeErrorPrototype%":TypeError.prototype,"$ %Uint8Array%":typeof Uint8Array==="undefined"?n:Uint8Array,"$ %Uint8ArrayPrototype%":typeof Uint8Array==="undefined"?n:Uint8Array.prototype,"$ %Uint8ClampedArray%":typeof Uint8ClampedArray==="undefined"?n:Uint8ClampedArray,"$ %Uint8ClampedArrayPrototype%":typeof Uint8ClampedArray==="undefined"?n:Uint8ClampedArray.prototype,"$ %Uint16Array%":typeof Uint16Array==="undefined"?n:Uint16Array,"$ %Uint16ArrayPrototype%":typeof Uint16Array==="undefined"?n:Uint16Array.prototype,"$ %Uint32Array%":typeof Uint32Array==="undefined"?n:Uint32Array,"$ %Uint32ArrayPrototype%":typeof Uint32Array==="undefined"?n:Uint32Array.prototype,"$ %URIError%":URIError,"$ %URIErrorPrototype%":URIError.prototype,"$ %WeakMap%":typeof WeakMap==="undefined"?n:WeakMap,"$ %WeakMapPrototype%":typeof WeakMap==="undefined"?n:WeakMap.prototype,"$ %WeakSet%":typeof WeakSet==="undefined"?n:WeakSet,"$ %WeakSetPrototype%":typeof WeakSet==="undefined"?n:WeakSet.prototype};e.exports=function e(t,r){if(arguments.length>1&&typeof r!=="boolean"){throw new TypeError('"allowMissing" argument must be a boolean')}var n="$ "+t;if(!(n in v)){throw new SyntaxError("intrinsic "+t+" does not exist!")}if(typeof v[n]==="undefined"&&!r){throw new TypeError("intrinsic "+t+" exists, but is not available. Please file an issue!")}return v[n]}},e9ff:function(e,t){e.exports=r;function r(e,t,r){if(typeof r!=="object"){r={}}var n=typeof r.minVelocity==="number"?r.minVelocity:5;var a=typeof r.amplitude==="number"?r.amplitude:.25;var i;var o;var s=342;var l;var c,u,d;var f,p,h;var v;return{start:g,stop:y,cancel:m};function m(){window.clearInterval(l);window.cancelAnimationFrame(v)}function g(){i=e();d=h=c=f=0;o=new Date;window.clearInterval(l);window.cancelAnimationFrame(v);l=window.setInterval(b,100)}function b(){var t=Date.now();var r=t-o;o=t;var n=e();var a=n.x-i.x;var s=n.y-i.y;i=n;var l=1e3/(1+r);c=.8*a*l+.2*c;f=.8*s*l+.2*f}function y(){window.clearInterval(l);window.cancelAnimationFrame(v);var t=e();u=t.x;p=t.y;o=Date.now();if(c<-n||c>n){d=a*c;u+=d}if(f<-n||f>n){h=a*f;p+=h}v=window.requestAnimationFrame(w)}function w(){var e=Date.now()-o;var r=false;var n=0;var a=0;if(d){n=-d*Math.exp(-e/s);if(n>.5||n<-.5)r=true;else n=d=0}if(h){a=-h*Math.exp(-e/s);if(a>.5||a<-.5)r=true;else a=h=0}if(r){t(u+n,p+a);v=window.requestAnimationFrame(w)}}}},edee:function(e,t,r){"use strict";var n=r("f861");var a=r("e2e5");var i=r("8b78");var o=r("189f");var s=r("1f03");var l=r("1385");var c=r("c0f6");var u=r("9a92");var d=r("e288");var f=r("d7d0");var p=r("8445");var h=r("ba1d").f;var v=r("98ab").f;var m=r("afb7");var g=r("fe4e");var b="ArrayBuffer";var y="DataView";var w="prototype";var x="Wrong length!";var _="Wrong index!";var C=n[b];var S=n[y];var k=n.Math;var A=n.RangeError;var E=n.Infinity;var P=C;var M=k.abs;var T=k.pow;var I=k.floor;var O=k.log;var D=k.LN2;var R="buffer";var F="byteLength";var $="byteOffset";var B=a?"_b":R;var j=a?"_l":F;var N=a?"_o":$;function L(e,t,r){var n=new Array(r);var a=r*8-t-1;var i=(1<<a)-1;var o=i>>1;var s=t===23?T(2,-24)-T(2,-77):0;var l=0;var c=e<0||e===0&&1/e<0?1:0;var u,d,f;e=M(e);if(e!=e||e===E){d=e!=e?1:0;u=i}else{u=I(O(e)/D);if(e*(f=T(2,-u))<1){u--;f*=2}if(u+o>=1){e+=s/f}else{e+=s*T(2,1-o)}if(e*f>=2){u++;f/=2}if(u+o>=i){d=0;u=i}else if(u+o>=1){d=(e*f-1)*T(2,t);u=u+o}else{d=e*T(2,o-1)*T(2,t);u=0}}for(;t>=8;n[l++]=d&255,d/=256,t-=8);u=u<<t|d;a+=t;for(;a>0;n[l++]=u&255,u/=256,a-=8);n[--l]|=c*128;return n}function q(e,t,r){var n=r*8-t-1;var a=(1<<n)-1;var i=a>>1;var o=n-7;var s=r-1;var l=e[s--];var c=l&127;var u;l>>=7;for(;o>0;c=c*256+e[s],s--,o-=8);u=c&(1<<-o)-1;c>>=-o;o+=t;for(;o>0;u=u*256+e[s],s--,o-=8);if(c===0){c=1-i}else if(c===a){return u?NaN:l?-E:E}else{u=u+T(2,t);c=c-i}return(l?-1:1)*u*T(2,c-t)}function z(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]}function H(e){return[e&255]}function W(e){return[e&255,e>>8&255]}function U(e){return[e&255,e>>8&255,e>>16&255,e>>24&255]}function X(e){return L(e,52,8)}function V(e){return L(e,23,4)}function G(e,t,r){v(e[w],t,{get:function(){return this[r]}})}function Y(e,t,r,n){var a=+r;var i=p(a);if(i+t>e[j])throw A(_);var o=e[B]._b;var s=i+e[N];var l=o.slice(s,s+t);return n?l:l.reverse()}function Z(e,t,r,n,a,i){var o=+r;var s=p(o);if(s+t>e[j])throw A(_);var l=e[B]._b;var c=s+e[N];var u=n(+a);for(var d=0;d<t;d++)l[c+d]=u[i?d:t-d-1]}if(!o.ABV){C=function e(t){u(this,C,b);var r=p(t);this._b=m.call(new Array(r),0);this[j]=r};S=function e(t,r,n){u(this,S,y);u(t,C,y);var a=t[j];var i=d(r);if(i<0||i>a)throw A("Wrong offset!");n=n===undefined?a-i:f(n);if(i+n>a)throw A(x);this[B]=t;this[N]=i;this[j]=n};if(a){G(C,F,"_l");G(S,R,"_b");G(S,F,"_l");G(S,$,"_o")}l(S[w],{getInt8:function e(t){return Y(this,1,t)[0]<<24>>24},getUint8:function e(t){return Y(this,1,t)[0]},getInt16:function e(t){var r=Y(this,2,t,arguments[1]);return(r[1]<<8|r[0])<<16>>16},getUint16:function e(t){var r=Y(this,2,t,arguments[1]);return r[1]<<8|r[0]},getInt32:function e(t){return z(Y(this,4,t,arguments[1]))},getUint32:function e(t){return z(Y(this,4,t,arguments[1]))>>>0},getFloat32:function e(t){return q(Y(this,4,t,arguments[1]),23,4)},getFloat64:function e(t){return q(Y(this,8,t,arguments[1]),52,8)},setInt8:function e(t,r){Z(this,1,t,H,r)},setUint8:function e(t,r){Z(this,1,t,H,r)},setInt16:function e(t,r){Z(this,2,t,W,r,arguments[2])},setUint16:function e(t,r){Z(this,2,t,W,r,arguments[2])},setInt32:function e(t,r){Z(this,4,t,U,r,arguments[2])},setUint32:function e(t,r){Z(this,4,t,U,r,arguments[2])},setFloat32:function e(t,r){Z(this,4,t,V,r,arguments[2])},setFloat64:function e(t,r){Z(this,8,t,X,r,arguments[2])}})}else{if(!c(function(){C(1)})||!c(function(){new C(-1)})||c(function(){new C;new C(1.5);new C(NaN);return C.name!=b})){C=function e(t){u(this,C);return new P(p(t))};var J=C[w]=P[w];for(var K=h(P),Q=0,ee;K.length>Q;){if(!((ee=K[Q++])in C))s(C,ee,P[ee])}if(!i)J.constructor=C}var te=new S(new C(2));var re=S[w].setInt8;te.setInt8(0,2147483648);te.setInt8(1,2147483649);if(te.getInt8(0)||!te.getInt8(1))l(S[w],{setInt8:function e(t,r){re.call(this,t,r<<24>>24)},setUint8:function e(t,r){re.call(this,t,r<<24>>24)}},true)}g(C,b);g(S,y);s(S[w],o.VIEW,true);t[b]=C;t[y]=S},eec7:function(e,t,r){"use strict";var n=r("be09");var a=r("8362");var i=r("6444");var o=r("53a8");e.exports=u;e.exports.default=u;u.XMLHttpRequest=n.XMLHttpRequest||p;u.XDomainRequest="withCredentials"in new u.XMLHttpRequest?u.XMLHttpRequest:n.XDomainRequest;s(["get","put","post","patch","head","delete"],function(e){u[e==="delete"?"del":e]=function(t,r,n){r=c(t,r,n);r.method=e.toUpperCase();return d(r)}});function s(e,t){for(var r=0;r<e.length;r++){t(e[r])}}function l(e){for(var t in e){if(e.hasOwnProperty(t))return false}return true}function c(e,t,r){var n=e;if(a(t)){r=t;if(typeof e==="string"){n={uri:e}}}else{n=o(t,{uri:e})}n.callback=r;return n}function u(e,t,r){t=c(e,t,r);return d(t)}function d(e){if(typeof e.callback==="undefined"){throw new Error("callback argument missing")}var t=false;var r=function r(n,a,i){if(!t){t=true;e.callback(n,a,i)}};function n(){if(c.readyState===4){setTimeout(s,0)}}function a(){var e=undefined;if(c.response){e=c.response}else{e=c.responseText||f(c)}if(y){try{e=JSON.parse(e)}catch(t){}}return e}function o(e){clearTimeout(w);if(!(e instanceof Error)){e=new Error(""+(e||"Unknown XMLHttpRequest Error"))}e.statusCode=0;return r(e,x)}function s(){if(p)return;var t;clearTimeout(w);if(e.useXDR&&c.status===undefined){t=200}else{t=c.status===1223?204:c.status}var n=x;var o=null;if(t!==0){n={body:a(),statusCode:t,method:v,headers:{},url:h,rawRequest:c};if(c.getAllResponseHeaders){n.headers=i(c.getAllResponseHeaders())}}else{o=new Error("Internal XMLHttpRequest Error")}return r(o,n,n.body)}var c=e.xhr||null;if(!c){if(e.cors||e.useXDR){c=new u.XDomainRequest}else{c=new u.XMLHttpRequest}}var d;var p;var h=c.url=e.uri||e.url;var v=c.method=e.method||"GET";var m=e.body||e.data;var g=c.headers=e.headers||{};var b=!!e.sync;var y=false;var w;var x={body:undefined,headers:{},statusCode:0,method:v,url:h,rawRequest:c};if("json"in e&&e.json!==false){y=true;g["accept"]||g["Accept"]||(g["Accept"]="application/json");if(v!=="GET"&&v!=="HEAD"){g["content-type"]||g["Content-Type"]||(g["Content-Type"]="application/json");m=JSON.stringify(e.json===true?m:e.json)}}c.onreadystatechange=n;c.onload=s;c.onerror=o;c.onprogress=function(){};c.onabort=function(){p=true};c.ontimeout=o;c.open(v,h,!b,e.username,e.password);if(!b){c.withCredentials=!!e.withCredentials}if(!b&&e.timeout>0){w=setTimeout(function(){if(p)return;p=true;c.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT";o(e)},e.timeout)}if(c.setRequestHeader){for(d in g){if(g.hasOwnProperty(d)){c.setRequestHeader(d,g[d])}}}else if(e.headers&&!l(e.headers)){throw new Error("Headers cannot be set on an XDomainRequest object")}if("responseType"in e){c.responseType=e.responseType}if("beforeSend"in e&&typeof e.beforeSend==="function"){e.beforeSend(c)}c.send(m||null);return c}function f(e){try{if(e.responseType==="document"){return e.responseXML}var t=e.responseXML&&e.responseXML.documentElement.nodeName==="parsererror";if(e.responseType===""&&!t){return e.responseXML}}catch(r){}return null}function p(){}},f1f5:function(e,t){var r="chasrset";e.exports=function e(t){if(r in t){t["charset"]=t[r];delete t[r]}for(var a in t){if(a==="face"||a==="charset")continue;else if(a==="padding"||a==="spacing")t[a]=n(t[a]);else t[a]=parseInt(t[a],10)}return t};function n(e){return e.split(",").map(function(e){return parseInt(e,10)})}},f202:function(e,t,r){var n=r("42b6");var a=r("53a8");var i=r("8f6d");var o=["x","e","a","o","n","s","r","c","u","m","v","w","z"];var s=["m","w"];var l=["H","I","N","E","F","K","L","T","U","V","W","X","Y","Z"];var c="\t".charCodeAt(0);var u=" ".charCodeAt(0);var d=0,f=1,p=2;e.exports=function e(t){return new h(t)};function h(e){this.glyphs=[];this._measure=this.computeMetrics.bind(this);this.update(e)}h.prototype.update=function(e){e=a({measure:this._measure},e);this._opt=e;this._opt.tabSize=i(this._opt.tabSize,4);if(!e.font)throw new Error("must provide a valid bitmap font");var t=this.glyphs;var r=e.text||"";var o=e.font;this._setupSpaceGlyphs(o);var s=n.lines(r,e);var l=e.width||0;t.length=0;var c=s.reduce(function(e,t){return Math.max(e,t.width,l)},0);var u=0;var d=0;var h=i(e.lineHeight,o.common.lineHeight);var v=o.common.base;var m=h-v;var g=e.letterSpacing||0;var y=h*s.length-m;var C=_(this._opt.align);d-=y;this._width=c;this._height=y;this._descender=h-v;this._baseline=v;this._xHeight=b(o);this._capHeight=w(o);this._lineHeight=h;this._ascender=h-m-this._xHeight;var S=this;s.forEach(function(e,n){var a=e.start;var i=e.end;var s=e.width;var l;for(var v=a;v<i;v++){var m=r.charCodeAt(v);var b=S.getGlyph(o,m);if(b){if(l)u+=x(o,l.id,b.id);var y=u;if(C===f)y+=(c-s)/2;else if(C===p)y+=c-s;t.push({position:[y,d],data:b,index:v,line:n});u+=b.xadvance+g;l=b}}d+=h;u=0});this._linesTotal=s.length};h.prototype._setupSpaceGlyphs=function(e){this._fallbackSpaceGlyph=null;this._fallbackTabGlyph=null;if(!e.chars||e.chars.length===0)return;var t=g(e,u)||y(e)||e.chars[0];var r=this._opt.tabSize*t.xadvance;this._fallbackSpaceGlyph=t;this._fallbackTabGlyph=a(t,{x:0,y:0,xadvance:r,id:c,xoffset:0,yoffset:0,width:0,height:0})};h.prototype.getGlyph=function(e,t){var r=g(e,t);if(r)return r;else if(t===c)return this._fallbackTabGlyph;else if(t===u)return this._fallbackSpaceGlyph;return null};h.prototype.computeMetrics=function(e,t,r,n){var a=this._opt.letterSpacing||0;var i=this._opt.font;var o=0;var s=0;var l=0;var c;var u;if(!i.chars||i.chars.length===0){return{start:t,end:t,width:0}}r=Math.min(e.length,r);for(var d=t;d<r;d++){var f=e.charCodeAt(d);var c=this.getGlyph(i,f);if(c){var p=c.xoffset;var h=u?x(i,u.id,c.id):0;o+=h;var v=o+c.xadvance+a;var m=o+c.width;if(m>=n||v>=n)break;o=v;s=m;u=c}l++}if(u)s+=u.xoffset;return{start:t,end:t+l,width:s}};["width","height","descender","ascender","xHeight","baseline","capHeight","lineHeight"].forEach(v);function v(e){Object.defineProperty(h.prototype,e,{get:m(e),configurable:true})}function m(e){return new Function(["return function "+e+"() {","  return this._"+e,"}"].join("\n"))()}function g(e,t){if(!e.chars||e.chars.length===0)return null;var r=C(e.chars,t);if(r>=0)return e.chars[r];return null}function b(e){for(var t=0;t<o.length;t++){var r=o[t].charCodeAt(0);var n=C(e.chars,r);if(n>=0)return e.chars[n].height}return 0}function y(e){for(var t=0;t<s.length;t++){var r=s[t].charCodeAt(0);var n=C(e.chars,r);if(n>=0)return e.chars[n]}return 0}function w(e){for(var t=0;t<l.length;t++){var r=l[t].charCodeAt(0);var n=C(e.chars,r);if(n>=0)return e.chars[n].height}return 0}function x(e,t,r){if(!e.kernings||e.kernings.length===0)return 0;var n=e.kernings;for(var a=0;a<n.length;a++){var i=n[a];if(i.first===t&&i.second===r)return i.amount}return 0}function _(e){if(e==="center")return f;else if(e==="right")return p;return d}function C(e,t,r){r=r||0;for(var n=r;n<e.length;n++){if(e[n].id===t){return n}}return-1}},f367:function(e,t,r){"use strict";var n=r("d6c7");var a=typeof Symbol==="function"&&typeof Symbol("foo")==="symbol";var i=Object.prototype.toString;var o=Array.prototype.concat;var s=Object.defineProperty;var l=function(e){return typeof e==="function"&&i.call(e)==="[object Function]"};var c=function(){var e={};try{s(e,"x",{enumerable:false,value:e});for(var t in e){return false}return e.x===e}catch(r){return false}};var u=s&&c();var d=function(e,t,r,n){if(t in e&&(!l(n)||!n())){return}if(u){s(e,t,{configurable:true,enumerable:false,value:r,writable:true})}else{e[t]=r}};var f=function(e,t){var r=arguments.length>2?arguments[2]:{};var i=n(t);if(a){i=o.call(i,Object.getOwnPropertySymbols(t))}for(var s=0;s<i.length;s+=1){d(e,i[s],t[i[s]],r[i[s]])}};f.supportsDescriptors=!!u;e.exports=f},fab3:function(e,t,r){},fad7:function(e,t,r){var n=r("f1f5");var a=r("486c");var i={scaleh:"scaleH",scalew:"scaleW",stretchh:"stretchH",lineheight:"lineHeight",alphachnl:"alphaChnl",redchnl:"redChnl",greenchnl:"greenChnl",bluechnl:"blueChnl"};e.exports=function e(t){t=t.toString();var r=a(t);var i={pages:[],chars:[],kernings:[]};["info","common"].forEach(function(e){var t=r.getElementsByTagName(e)[0];if(t)i[e]=n(o(t))});var s=r.getElementsByTagName("pages")[0];if(!s)throw new Error("malformed file -- no <pages> element");var l=s.getElementsByTagName("page");for(var c=0;c<l.length;c++){var u=l[c];var d=parseInt(u.getAttribute("id"),10);var f=u.getAttribute("file");if(isNaN(d))throw new Error('malformed file -- page "id" attribute is NaN');if(!f)throw new Error('malformed file -- needs page "file" attribute');i.pages[parseInt(d,10)]=f}["chars","kernings"].forEach(function(e){var t=r.getElementsByTagName(e)[0];if(!t)return;var a=e.substring(0,e.length-1);var s=t.getElementsByTagName(a);for(var l=0;l<s.length;l++){var c=s[l];i[e].push(n(o(c)))}});return i};function o(e){var t=s(e);return t.reduce(function(e,t){var r=l(t.nodeName);e[r]=t.nodeValue;return e},{})}function s(e){var t=[];for(var r=0;r<e.attributes.length;r++)t.push(e.attributes[r]);return t}function l(e){return i[e.toLowerCase()]||e}}}]);
//# sourceMappingURL=32741f73.3785e738.js.map