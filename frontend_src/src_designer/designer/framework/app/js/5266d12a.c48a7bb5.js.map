{"version": 3, "sources": ["webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/_typed.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.object.set-prototype-of.js", "webpack:///../app/@tylko/cape-palette/palette-bar.vue?4e12", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/_array-copy-within.js", "webpack:///(webpack)/buildin/amd-options.js", "webpack:///../app/@core/data/filter.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.typed.float32-array.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.typed.uint16-array.js", "webpack:///./node_modules/normalize-wheel/src/isEventSupported.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/_to-index.js", "webpack:///../app/@tylko/cape-palette/palette-container.vue?d064", "webpack:///./node_modules/normalize-wheel/src/UserAgent_DEPRECATED.js", "webpack:///webpack:///webpack/universalModuleDefinition", "webpack:///webpack:/webpack/bootstrap a5e42b138f430a23f178", "webpack:///webpack:///src/index.ts", "webpack:///webpack:///src/MSDFText.ts", "webpack:///webpack:///src/MSDFRenderer.ts", "webpack:///webpack:///src/msdf.vert", "webpack:///webpack:///src/msdf.frag", "webpack:///./node_modules/normalize-wheel/src/ExecutionEnvironment.js", "webpack:///../app/@core/gl/msdf-text/index.js", "webpack:///../app/@cape-ui/editors/renderers/box-apperance.js", "webpack:///../app/@tylko/cape-palette/palette-bar.vue?e6cf", "webpack:///../app/@tylko/cape-palette/palette-container.vue?0962", "webpack:///../app/@tylko/cape-palette/paleta-renderer.js", "webpack:///../app/@tylko/cape-palette/palette-container.vue", "webpack:///../app/@tylko/cape-palette/palette-container.vue?3ef2", "webpack:///../app/@tylko/cape-palette/palette-container.vue?0880", "webpack:///../app/@tylko/cape-palette/palette-bar.vue", "webpack:///../app/@tylko/cape-palette/palette-bar.vue?7a2c", "webpack:///../app/@tylko/cape-palette/palette-bar.vue?d84d", "webpack:///./node_modules/normalize-wheel/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/_typed-array.js", "webpack:///./node_modules/normalize-wheel/src/normalizeWheel.js", "webpack:///(webpack)/buildin/harmony-module.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/_typed-buffer.js"], "names": ["global", "__webpack_require__", "hide", "uid", "TYPED", "VIEW", "ABV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "CONSTR", "i", "l", "Typed", "TypedArrayConstructors", "split", "prototype", "module", "exports", "$export", "S", "setPrototypeOf", "set", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_palette_bar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_palette_bar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "n", "_unused_webpack_default_export", "toObject", "toAbsoluteIndex", "to<PERSON><PERSON><PERSON>", "copyWithin", "target", "start", "O", "this", "len", "length", "to", "from", "end", "arguments", "undefined", "count", "Math", "min", "inc", "__webpack_amd_options__", "<PERSON><PERSON><PERSON><PERSON>", "sources", "_Users_tylko_dev_tylko_cstm_frontend_src_src_designer_designer_framework_node_modules_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2___default", "_", "merge", "apply", "_Users_tylko_dev_tylko_cstm_frontend_src_src_designer_designer_framework_node_modules_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1___default", "state", "operation", "_this", "Promise", "resolve", "console", "log", "_this2", "_this3", "_this4", "FilteringManager", "Filter", "init", "Float32Array", "data", "byteOffset", "Uint16Array", "ExecutionEnvironment", "useHasFeature", "canUseDOM", "document", "implementation", "hasFeature", "isEventSupported", "eventNameSuffix", "capture", "eventName", "isSupported", "element", "createElement", "setAttribute", "toInteger", "it", "number", "RangeError", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_palette_container_vue_vue_type_style_index_0_id_24fbf5f8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_palette_container_vue_vue_type_style_index_0_id_24fbf5f8_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default", "_populated", "_ie", "_firefox", "_opera", "_webkit", "_chrome", "_ie_real_version", "_osx", "_windows", "_linux", "_android", "_win64", "_iphone", "_ipad", "_native", "_mobile", "_populate", "uas", "navigator", "userAgent", "agent", "exec", "os", "parseFloat", "NaN", "documentMode", "trident", "ver", "replace", "UserAgent_DEPRECATED", "ie", "ieCompatibilityMode", "ie64", "firefox", "opera", "webkit", "safari", "chrome", "windows", "osx", "linux", "iphone", "mobile", "nativeApp", "android", "ipad", "webpackUniversalModuleDefinition", "root", "factory", "_Users_tylko_dev_tylko_cstm_frontend_src_src_designer_designer_framework_node_modules_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_7___default", "define", "self", "installedModules", "moduleId", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "__esModule", "getDefault", "getModuleExports", "object", "property", "hasOwnProperty", "p", "s", "MSDFText_1", "MSDFText", "MSDFRenderer_1", "MSDF<PERSON><PERSON><PERSON>", "_super", "__extends", "text", "options", "texture", "PIXI", "Texture", "WHITE", "_text", "_font", "fontFace", "fontSize", "color", "fillColor", "weight", "align", "kerning", "strokeColor", "dropShadow", "dropShadowColor", "dropShadowAlpha", "dropShadowBlur", "dropShadowOffset", "Point", "pxrange", "strokeThickness", "strokeWeight", "_baselineOffset", "baselineOffset", "_letterSpacing", "letterSpacing", "_lineSpacing", "lineSpacing", "_textWidth", "_textHeight", "_maxWidth", "max<PERSON><PERSON><PERSON>", "_anchor", "ObservablePoint", "dirty", "_textMetricsBound", "Rectangle", "_debugLevel", "debugLevel", "pluginName", "updateText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fontData", "extras", "BitmapText", "fonts", "Error", "_texture", "getBitmapTexture", "rawSize", "size", "scale", "pos", "chars", "lineWidths", "tex<PERSON><PERSON><PERSON>", "width", "texHeight", "height", "prevCharCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "lastSpace", "lastSpaceWidth", "spacesRemoved", "maxLineHeight", "charCode", "charCodeAt", "test", "char<PERSON>t", "push", "max", "x", "y", "lineHeight", "utils", "removeItems", "char<PERSON><PERSON>", "drawRect", "xOffset", "yOffset", "rawRect", "orig", "xAdvance", "lineAlignOffsets", "alignOffset", "tint", "lineNo", "_i", "chars_1", "char", "drawGizmoRect", "getLocalBounds", "vertices", "toVertices", "uvs", "toUVs", "indices", "createIndicesForQuads", "indexDirty", "value", "unescape", "_glDatas", "EMPTY", "texturePath", "keys", "baseTexture", "imageUrl", "TextureCache", "positions", "for<PERSON>ach", "w", "h", "u0", "u1", "v0", "v1", "totalIndices", "j", "rect", "lineThickness", "lineColor", "lineAlpha", "gizmo", "Graphics", "nativeLines", "lineStyle", "<PERSON><PERSON><PERSON><PERSON>", "input", "mesh", "<PERSON><PERSON>", "vert<PERSON><PERSON><PERSON>", "fragS<PERSON>er", "renderer", "onContextChange", "gl", "shader", "Shader", "render", "msdfText", "font", "valid", "glData", "glDatas", "CONTEXT_UID", "vertexBuffer", "glCore", "GL<PERSON>uffer", "createVertexBuffer", "STREAM_DRAW", "uv<PERSON><PERSON><PERSON>", "indexBuffer", "createIndexBuffer", "STATIC_DRAW", "vao", "VertexArrayObject", "addIndex", "addAttribute", "attributes", "aVertexPosition", "FLOAT", "aTextureCoord", "<PERSON><PERSON><PERSON>", "upload", "<PERSON><PERSON><PERSON><PERSON>", "uniforms", "uSampler", "bindTexture", "setBlendMode", "blendMode", "translationMatrix", "worldTransform", "toArray", "u_alpha", "worldAlpha", "u_color", "hex2rgb", "u_fontSize", "u_fontInfoSize", "u_weight", "u_pxrange", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffset", "shadowColor", "shadowAlpha", "shadowSmoothing", "drawMode", "TRIANGLES", "draw", "O<PERSON><PERSON><PERSON><PERSON>", "WebGLRenderer", "registerPlugin", "window", "canUseWorkers", "Worker", "canUseEventListeners", "addEventListener", "attachEvent", "canUseViewport", "screen", "isInWorker", "ASSETS_PATH", "location", "href", "indexOf", "MsdfTextFactory", "_Users_tylko_dev_tylko_cstm_frontend_src_src_designer_designer_framework_node_modules_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1___default", "fontsLoaded", "copy", "pixiInstance", "callback", "loader", "add", "load", "e", "textOptions", "gltext", "__webpack_exports__", "boxApperance", "pixi_js__WEBPACK_IMPORTED_MODULE_0__", "pixi_js__WEBPACK_IMPORTED_MODULE_0___default", "drawTag", "t", "a", "graphics", "beginFill", "drawRoundedRect", "endFill", "drawLabel", "item", "ctx", "type", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "class", "behaviour", "palette", "staticStyle", "padding-top", "attrs", "label", "dense", "debounced", "filled", "dark", "model", "$$v", "search", "expression", "slot", "padding-left", "options-dark", "items-aligned", "max-values", "use-chips", "options-dense", "selectOptions", "multiple", "userFilter", "source", "filteredSources", "box", "actAsNavigation", "_e", "staticRenderFns", "palette_containervue_type_template_id_24fbf5f8_scoped_true_lang_pug_render", "palette_containervue_type_template_id_24fbf5f8_scoped_true_lang_pug_staticRenderFns", "Ruler", "ORIENTATION_VERTICAL", "ORIENTATION_HORIZONTAL", "Director", "classCallCheck_default", "checked", "bgDrawn", "items", "labels", "sprites", "orderHittest", "getSortArray", "cache", "pixiApp", "_this$getSize", "getSize", "resize", "view", "style", "concat", "<PERSON><PERSON><PERSON><PERSON>", "clear", "cols", "rows", "gutter", "tilecache", "geocache", "labelsCache", "Map", "selected", "id", "_this$getSize2", "ready", "interactionFrequency", "backgroundColor", "resolution", "pixiRoot", "pixiHitests", "drawingContainer", "stage", "vue", "$el", "append<PERSON><PERSON><PERSON>", "delta", "drawing", "throttledDraw", "throttle", "plugins", "interaction", "autoPreventDefault", "stop", "then", "clientX", "clientY", "_this$getXY", "getXY", "x2", "y2", "cx", "cy", "hittests", "map", "vueComponentInstance", "initializePixi", "event", "normalized", "normalizeWheel", "pixelY", "$route", "params", "getBoundingClientRect", "top", "left", "edge", "orientation", "maxBlocks", "round", "item2", "url", "selectedCollection", "parent", "$router", "no", "item_width", "item_height", "cols_in_block", "rows_in_block", "elements_in_block", "getPosition", "block", "floor", "index", "col", "row", "block2", "objectSpread_default", "sprite", "_this$getElementPosit", "getElementPosition", "truncate", "omission", "substring", "drawText", "hitArea", "position", "interactive", "buttonMode", "cursor", "opt", "hash", "XXH", "h32", "toString", "fetched", "cape", "services", "miniatures", "result", "bt", "painterState", "_sourceLoaded", "defaultAnchor", "sprite1", "addChildAt", "cacheAsBitmap", "api", "updateComponentThumb", "thumbnails_data", "geo", "json", "componentSet", "special", "specialSettings", "forceFetch", "pipe", "move", "startSprite", "newPosition", "getLocalPosition", "oldPosition", "startPosition", "r", "sqrt", "pow", "originalEvent", "preventDefault", "dragging", "startItem", "wasClicked", "selectElement", "setTimeout", "application", "dnd", "dragElement", "dragId", "on", "stopPropagation", "contextMenu", "show", "_this5", "<PERSON><PERSON><PERSON><PERSON>", "filter", "Boolean", "_this$getSize3", "edgeStep", "noInPalette", "ceil", "HashSprite", "pointer", "_this$getElementPosit2", "update", "digest", "has", "_this$getElementPosit3", "drawItem", "sampleText", "create", "H", "xxhashjs_lib_default", "palette_containervue_type_script_lang_js_h", "palette_containervue_type_script_lang_js_Ruler", "palette_containervue_type_script_lang_js_", "props", "methods", "watch", "updateBox", "selectedStep", "updated", "mounted", "addSourceComponent", "instance", "dragSourceID", "setContext", "created", "cape_palette_palette_containervue_type_script_lang_js_", "palette_container_component", "componentNormalizer", "palette_container", "heights_names", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "1100", "1200", "palette_barvue_type_script_lang_js_", "projectFilter", "collections", "users", "elements", "components", "meshes", "new-palette", "build", "reflesh", "reloadAfterCreation", "changeCollection", "$emit", "_build", "asyncToGenerator_default", "regenerator_default", "mark", "_callee", "choosenSources", "filterItem", "wrap", "_callee$", "_context", "prev", "next", "list", "tables", "relationComponents", "typesToDisplay", "user", "owner", "toLowerCase", "idMatch", "String", "loadObjects", "_loadObjects", "_callee2", "_ref", "component", "_ref2", "relationComponent", "_callee2$", "_context2", "collection", "componentTable", "fetch", "sent", "thumbs", "comp", "subscribe", "loadPage", "_loadPage", "_callee3", "_ref3", "_callee3$", "_context3", "username", "unshift", "_load", "_callee4", "_ref4", "_callee4$", "_context4", "t0", "matched", "_ref5", "abrupt", "_ref6", "cape_palette_palette_barvue_type_script_lang_js_", "palette_bar_component", "palette_bar", "LIBRARY", "fails", "$typed", "$buffer", "anInstance", "propertyDesc", "redefineAll", "toIndex", "toPrimitive", "classof", "isObject", "isArrayIter", "getPrototypeOf", "gOPN", "f", "getIterFn", "wks", "createArrayMethod", "createArrayIncludes", "speciesConstructor", "ArrayIterators", "Iterators", "$iterDetect", "setSpecies", "arrayFill", "arrayCopyWithin", "$DP", "$GOPD", "dP", "gOPD", "TypeError", "Uint8Array", "ARRAY_BUFFER", "SHARED_BUFFER", "BYTES_PER_ELEMENT", "PROTOTYPE", "ArrayProto", "Array", "$ArrayBuffer", "$DataView", "arrayForEach", "arrayFilter", "arraySome", "arrayEvery", "arrayFind", "arrayFindIndex", "arrayIncludes", "arrayIndexOf", "arrayValues", "values", "arrayKeys", "arrayEntries", "entries", "arrayLastIndexOf", "lastIndexOf", "arrayReduce", "reduce", "arrayReduceRight", "reduceRight", "arrayJoin", "join", "arraySort", "sort", "arraySlice", "slice", "arrayToString", "arrayToLocaleString", "toLocaleString", "ITERATOR", "TAG", "TYPED_CONSTRUCTOR", "DEF_CONSTRUCTOR", "ALL_CONSTRUCTORS", "TYPED_ARRAY", "WRONG_LENGTH", "$map", "allocate", "LITTLE_ENDIAN", "buffer", "FORCED_SET", "toOffset", "BYTES", "offset", "validate", "C", "speciesFromList", "fromList", "addGetter", "key", "internal", "_d", "$from", "aLen", "mapfn", "mapping", "iterFn", "step", "iterator", "done", "$of", "of", "TO_LOCALE_BUG", "$toLocaleString", "proto", "every", "callbackfn", "fill", "find", "predicate", "findIndex", "searchElement", "includes", "separator", "reverse", "that", "middle", "some", "comparefn", "subarray", "begin", "$begin", "$slice", "$set", "arrayLike", "src", "$iterators", "isTAIndex", "$getDesc", "getOwnPropertyDescriptor", "$setDesc", "desc", "writable", "F", "$TypedArrayPrototype$", "constructor", "KEY", "wrapper", "CLAMPED", "NAME", "GETTER", "SETTER", "TypedArray", "Base", "TAC", "FORCED", "TypedArrayPrototype", "v", "setter", "addElement", "$offset", "$length", "byteLength", "klass", "$len", "b", "iter", "Function", "$nativeIterator", "CORRECT_ITER_NAME", "$iterator", "G", "W", "P", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "axis", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "getEventType", "originalModule", "webpackPolyfill", "children", "DESCRIPTORS", "setToStringTag", "DATA_VIEW", "WRONG_INDEX", "Infinity", "BaseBuffer", "abs", "LN2", "BUFFER", "BYTE_LENGTH", "BYTE_OFFSET", "$BUFFER", "$LENGTH", "$OFFSET", "packIEEE754", "mLen", "nBytes", "eLen", "eMax", "eBias", "rt", "unpackIEEE754", "nBits", "unpackI32", "bytes", "packI8", "packI16", "packI32", "packF64", "packF32", "isLittleEndian", "numIndex", "intIndex", "store", "_b", "pack", "conversion", "bufferLength", "getInt8", "getUint8", "getInt16", "getUint16", "getInt32", "getUint32", "getFloat32", "getFloat64", "setInt8", "setUint8", "setInt16", "setUint16", "setInt32", "setUint32", "setFloat32", "setFloat64", "ArrayBufferProto", "$setInt8"], "mappings": "oHAAA,IAAAA,EAAaC,EAAQ,QACrB,IAAAC,EAAWD,EAAQ,QACnB,IAAAE,EAAUF,EAAQ,QAClB,IAAAG,EAAAD,EAAA,eACA,IAAAE,EAAAF,EAAA,QACA,IAAAG,KAAAN,EAAAO,aAAAP,EAAAQ,UACA,IAAAC,EAAAH,EACA,IAAAI,EAAA,EACA,IAAAC,EAAA,EACA,IAAAC,EAEA,IAAAC,EAAA,iHAEAC,MAAA,KAEA,MAAAJ,EAAAC,EAAA,CACA,GAAAC,EAAAZ,EAAAa,EAAAH,MAAA,CACAR,EAAAU,EAAAG,UAAAX,EAAA,MACAF,EAAAU,EAAAG,UAAAV,EAAA,WACGI,EAAA,MAGHO,EAAAC,QAAA,CACAX,MACAG,SACAL,QACAC,gCCzBA,IAAAa,EAAcjB,EAAQ,QACtBiB,IAAAC,EAAA,UAA8BC,eAAiBnB,EAAQ,QAAcoB,2CCFrE,IAAAC,EAAArB,EAAA,YAAAsB,EAAAtB,EAAAuB,EAAAF,GAA0iB,IAAAG,EAAAF,EAAG,uCCE7iB,IAAAG,EAAezB,EAAQ,QACvB,IAAA0B,EAAsB1B,EAAQ,QAC9B,IAAA2B,EAAe3B,EAAQ,QAEvBe,EAAAC,QAAA,GAAAY,YAAA,SAAAA,EAAAC,EAAAC,GACA,IAAAC,EAAAN,EAAAO,MACA,IAAAC,EAAAN,EAAAI,EAAAG,QACA,IAAAC,EAAAT,EAAAG,EAAAI,GACA,IAAAG,EAAAV,EAAAI,EAAAG,GACA,IAAAI,EAAAC,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,UACA,IAAAC,EAAAC,KAAAC,KAAAL,IAAAE,UAAAN,EAAAP,EAAAW,EAAAJ,IAAAG,EAAAH,EAAAE,GACA,IAAAQ,EAAA,EACA,GAAAP,EAAAD,KAAAC,EAAAI,EAAA,CACAG,GAAA,EACAP,GAAAI,EAAA,EACAL,GAAAK,EAAA,EAEA,MAAAA,KAAA,GACA,GAAAJ,KAAAL,IAAAI,GAAAJ,EAAAK,eACAL,EAAAI,GACAA,GAAAQ,EACAP,GAAAO,EACG,OAAAZ,0BCxBH,SAAAa,GACA7B,EAAAC,QAAA4B,4MCCMC,aACF,SAAAA,EAAYC,GAASC,IAAAf,KAAAa,GACjBb,KAAKc,QAAUE,IAAEC,MAAFC,MAAAF,IAACG,IAAUL,IAC1Bd,KAAKoB,MAAQ,yCAGVC,GAAW,IAAAC,EAAAtB,KACd,IAAIuB,QAAQ,SAACC,GACTC,QAAQC,IAAIJ,EAAKR,SACjBU,EAAQF,KAEZ,OAAOtB,uCAGHqB,GAAW,IAAAM,EAAA3B,KACf,IAAIuB,QAAQ,SAACC,GACTC,QAAQC,IAAIC,EAAKb,SACjBU,EAAQG,KAEZ,OAAO3B,uCAGHqB,GAAW,IAAAO,EAAA5B,KACf,IAAIuB,QAAQ,SAACC,GACTC,QAAQC,IAAIE,EAAKd,SACjBU,EAAQI,KAEZ,OAAO5B,wCAGF,IAAA6B,EAAA7B,KACL,OAAO,IAAIuB,QAAQ,SAACC,GAEhBC,QAAQC,IAAIG,EAAKf,SACjBU,EAAQK,yBAKdC,gHAKEhB,GACA,OAAO,IAAID,EAAUC,mBAI7B,IAAMiB,EAAS,IAAID,0BCnDnB9D,EAAQ,OAARA,CAAwB,qBAAAgE,GACxB,gBAAAC,EAAAC,EAAAC,EAAAjC,GACA,OAAA8B,EAAAhC,KAAAkC,EAAAC,EAAAjC,8BCFAlC,EAAQ,OAARA,CAAwB,oBAAAgE,GACxB,gBAAAI,EAAAF,EAAAC,EAAAjC,GACA,OAAA8B,EAAAhC,KAAAkC,EAAAC,EAAAjC,2CCWA,IAAAmC,EAA2BrE,EAAQ,QAEnC,IAAAsE,EACA,GAAAD,EAAAE,UAAA,CACAD,EACAE,SAAAC,gBACAD,SAAAC,eAAAC,YAGAF,SAAAC,eAAAC,WAAA;;;;;;;;;;;;;;GAiBA,SAAAC,EAAAC,EAAAC,GACA,IAAAR,EAAAE,WACAM,KAAA,qBAAAL,UAAA,CACA,aAGA,IAAAM,EAAA,KAAAF,EACA,IAAAG,EAAAD,KAAAN,SAEA,IAAAO,EAAA,CACA,IAAAC,EAAAR,SAAAS,cAAA,OACAD,EAAAE,aAAAJ,EAAA,WACAC,SAAAC,EAAAF,KAAA,WAGA,IAAAC,GAAAT,GAAAM,IAAA,SAEAG,EAAAP,SAAAC,eAAAC,WAAA,sBAGA,OAAAK,EAGAhE,EAAAC,QAAA2D,wBC7DA,IAAAQ,EAAgBnF,EAAQ,QACxB,IAAA2B,EAAe3B,EAAQ,QACvBe,EAAAC,QAAA,SAAAoE,GACA,GAAAA,IAAA7C,UAAA,SACA,IAAA8C,EAAAF,EAAAC,GACA,IAAAlD,EAAAP,EAAA0D,GACA,GAAAA,IAAAnD,EAAA,MAAAoD,WAAA,iBACA,OAAApD,iECRA,IAAAqD,EAAAvF,EAAA,YAAAwF,EAAAxF,EAAAuB,EAAAgE,GAAwkB,IAAA/D,EAAAgE,EAAG,wBC8C3kB,IAAAC,EAAA,MAGA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAGA,IAAAC,EAGA,IAAAC,EAAAC,EAAAC,EAAAC,EAGA,IAAAC,EAGA,IAAAC,EAAAC,EAAAC,EAEA,IAAAC,EAEA,SAAAC,IACA,GAAAhB,EAAA,CACA,OAGAA,EAAA,KAOA,IAAAiB,EAAAC,UAAAC,UACA,IAAAC,EAAA,iLAAAC,KAAAJ,GACA,IAAAK,EAAA,+BAAAD,KAAAJ,GAEAL,EAAA,qBAAAS,KAAAJ,GACAJ,EAAA,cAAAQ,KAAAJ,GACAP,EAAA,WAAAW,KAAAJ,GACAH,EAAA,cAAuBO,KAAAJ,GACvBF,EAAA,UAAAM,KAAAJ,GAOAN,IAAA,QAAAU,KAAAJ,GAEA,GAAAG,EAAA,CACAnB,EAAAmB,EAAA,GAAAG,WAAAH,EAAA,IACAA,EAAA,GAAAG,WAAAH,EAAA,IAAAI,IAEA,GAAAvB,GAAAlB,mBAAA0C,aAAA,CACAxB,EAAAlB,SAAA0C,aAGA,IAAAC,EAAA,yBAAAL,KAAAJ,GACAX,EAAAoB,EAAAH,WAAAG,EAAA,MAAAzB,EAEAC,EAAAkB,EAAA,GAAAG,WAAAH,EAAA,IAAAI,IACArB,EAAAiB,EAAA,GAAAG,WAAAH,EAAA,IAAAI,IACApB,EAAAgB,EAAA,GAAAG,WAAAH,EAAA,IAAAI,IACA,GAAApB,EAAA,CAIAgB,EAAA,yBAAAC,KAAAJ,GACAZ,EAAAe,KAAA,GAAAG,WAAAH,EAAA,IAAAI,QACK,CACLnB,EAAAmB,SAEG,CACHvB,EAAAC,EAAAC,EAAAE,EAAAD,EAAAoB,IAGA,GAAAF,EAAA,CACA,GAAAA,EAAA,IAMA,IAAAK,EAAA,iCAAAN,KAAAJ,GAEAV,EAAAoB,EAAAJ,WAAAI,EAAA,GAAAC,QAAA,mBACK,CACLrB,EAAA,MAEAC,IAAAc,EAAA,GACAb,IAAAa,EAAA,OACG,CACHf,EAAAC,EAAAC,EAAA,OAIA,IAAAoB,EAAA,CAQAC,GAAA,WACA,OAAAd,KAAAf,GASA8B,oBAAA,WACA,OAAAf,KAAAV,EAAAL,GASA+B,KAAA,WACA,OAAAH,EAAAC,MAAAnB,GASAsB,QAAA,WACA,OAAAjB,KAAAd,GAUAgC,MAAA,WACA,OAAAlB,KAAAb,GAUAgC,OAAA,WACA,OAAAnB,KAAAZ,GAOAgC,OAAA,WACA,OAAAP,EAAAM,UASAE,OAAA,WACA,OAAArB,KAAAX,GASAiC,QAAA,WACA,OAAAtB,KAAAR,GAUA+B,IAAA,WACA,OAAAvB,KAAAT,GAQAiC,MAAA,WACA,OAAAxB,KAAAP,GASAgC,OAAA,WACA,OAAAzB,KAAAJ,GAGA8B,OAAA,WACA,OAAA1B,MAAAJ,GAAAC,GAAAH,GAAAK,IAGA4B,UAAA,WAEA,OAAA3B,KAAAF,GAGA8B,QAAA,WACA,OAAA5B,KAAAN,GAGAmC,KAAA,WACA,OAAA7B,KAAAH,IAIAvF,EAAAC,QAAAsG,6RCzRA,SAAAiB,EAAAC,EAAAC,GACA,WAAAzH,UAAA,wBAAA0H,IAAA1H,YAAA,iBAAAuB,UAAAmG,IAAA3H,MAAA,SACAA,EAAAC,QAAAyH,SACA,UAAAE,SAAA,YAAA3I,EAAA,QACA2I,OAAA,GAAAF,QACA,WAAAzH,UAAA,wBAAA0H,IAAA1H,YAAA,SACAA,QAAA,YAAAyH,SAEAD,EAAA,YAAAC,KARA,QASCG,OAAA,YAAAA,KAAArG,UAAA,WACD,mBCTA,IAAAsG,EAAA,GAGA,SAAA7I,EAAA8I,GAGA,GAAAD,EAAAC,GAAA,CACA,OAAAD,EAAAC,GAAA9H,QAGA,IAAAD,EAAA8H,EAAAC,GAAA,CACArI,EAAAqI,EACApI,EAAA,MACAM,QAAA,IAIA+H,EAAAD,GAAAE,KAAAjI,EAAAC,QAAAD,IAAAC,QAAAhB,GAGAe,EAAAL,EAAA,KAGA,OAAAK,EAAAC,QAKAhB,EAAAiJ,EAAAF,EAGA/I,EAAAkJ,EAAAL,EAGA7I,EAAAmJ,EAAA,SAAAnI,EAAAoI,EAAAC,GACA,IAAArJ,EAAAsJ,EAAAtI,EAAAoI,GAAA,CACAG,OAAAC,eAAAxI,EAAAoI,EAAA,CACAK,aAAA,MACAC,WAAA,KACAC,IAAAN,MAMArJ,EAAAuB,EAAA,SAAAR,GACA,IAAAsI,EAAAtI,KAAA6I,WACA,SAAAC,IAA2B,OAAA9I,EAAA,YAC3B,SAAA+I,IAAiC,OAAA/I,GACjCf,EAAAmJ,EAAAE,EAAA,IAAAA,GACA,OAAAA,GAIArJ,EAAAsJ,EAAA,SAAAS,EAAAC,GAAsD,OAAAT,OAAAzI,UAAAmJ,eAAAjB,KAAAe,EAAAC,IAGtDhK,EAAAkK,EAAA,GAGA,OAAAlK,IAAAmK,EAAA,GDnDA,kFEVA,IAAAC,EAAApK,EAAA,GAASgB,EAAAqJ,SAAAD,EAAAC,SACT,IAAAC,EAAAtK,EAAA,GAASgB,EAAAuJ,aAAAD,EAAAC,uaC2BT,IAAAF,EAAA,SAAAG,GAA8BC,EAAAJ,EAAAG,GAoB1B,SAAAH,EAAYK,EAAcC,GAA1B,IAAArH,EACIkH,EAAAxB,KAAAhH,KAAM2I,EAAQC,SAAWC,KAAKC,QAAQC,QAAM/I,KAC5CsB,EAAK0H,MAAQN,EACbpH,EAAK2H,MAAQ,CACTC,SAAUP,EAAQO,SAClBC,SAAUR,EAAQQ,SAClBC,MAAOT,EAAQU,YAAc9I,UAAY,SAAWoI,EAAQU,UAC5DC,OAAQX,EAAQW,SAAW/I,UAAY,GAAM,EAAIoI,EAAQW,OACzDC,MAAOZ,EAAQY,MACfC,QAASb,EAAQa,UAAYjJ,UAAY,KAAOoI,EAAQa,QACxDC,YAAad,EAAQc,aAAe,EACpCC,WAAYf,EAAQe,YAAc,MAClCC,gBAAiBhB,EAAQgB,iBAAmB,EAC5CC,gBAAiBjB,EAAQiB,kBAAoBrJ,UAAY,GAAMoI,EAAQiB,gBACvEC,eAAgBlB,EAAQkB,gBAAkB,EAC1CC,iBAAkBnB,EAAQmB,kBAAoB,IAAIjB,KAAKkB,MAAM,EAAG,GAChEC,QAASrB,EAAQqB,UAAYzJ,UAAY,EAAIoI,EAAQqB,SAEzD,GAAIrB,EAAQsB,kBAAoB1J,WAAaoI,EAAQsB,kBAAoB,EAAG,CACxE3I,EAAK2H,MAAMiB,aAAe,MACvB,CACH5I,EAAK2H,MAAMiB,aAAe5I,EAAK2H,MAAMK,OAASX,EAAQsB,gBAI1D3I,EAAK6I,gBAAkBxB,EAAQyB,iBAAmB7J,UAAY,EAAIoI,EAAQyB,eAC1E9I,EAAK+I,eAAiB1B,EAAQ2B,gBAAkB/J,UAAY,EAAIoI,EAAQ2B,cACxEhJ,EAAKiJ,aAAe5B,EAAQ6B,cAAgBjK,UAAY,EAAIoI,EAAQ6B,YAEpElJ,EAAKmJ,WAAanJ,EAAKoJ,YAAc,EACrCpJ,EAAKqJ,UAAYhC,EAAQiC,UAAY,EACrCtJ,EAAKuJ,QAAU,IAAIhC,KAAKiC,gBAAgB,WAAQxJ,EAAKyJ,SAAYzJ,EAAM,EAAG,GAC1EA,EAAK0J,kBAAoB,IAAInC,KAAKoC,UAGlC3J,EAAK4J,YAAcvC,EAAQwC,YAAc,EACzC7J,EAAK8J,WAAa,OAClB9J,EAAKyJ,MAAQ,EACbzJ,EAAK+J,sBAGFhD,EAAAvJ,UAAAuM,WAAP,WAEIrL,KAAKsL,iBAEL,IAAMC,EAAW1C,KAAK2C,OAAOC,WAAWC,MAAM1L,KAAKiJ,MAAMC,UACzD,IAAKqC,EAAU,MAAM,IAAII,MAAM,qBAAuB3L,KAAKiJ,MAAMC,UAEjElJ,KAAK4L,SAAW5L,KAAK6L,iBAAiB7L,KAAKiJ,MAAMC,UACjDlJ,KAAKiJ,MAAM6C,QAAUP,EAASQ,KAE9B,IAAMC,EAAQhM,KAAKiJ,MAAME,SAAWoC,EAASQ,KAC7C,IAAME,EAAM,IAAIpD,KAAKkB,MAAM,GAAI/J,KAAKmK,gBAAkB6B,GACtD,IAAME,EAAQ,GACd,IAAMC,EAAuB,GAC7B,IAAMC,EAAWpM,KAAK4L,SAASS,MAC/B,IAAMC,EAAYtM,KAAK4L,SAASW,OAEhC,IAAIC,GAAgB,EACpB,IAAIC,EAAgB,EACpB,IAAIC,EAAe,EACnB,IAAIC,EAAO,EACX,IAAIC,GAAa,EACjB,IAAIC,EAAiB,EACrB,IAAIC,EAAgB,EACpB,IAAIC,EAAgB,EAEpB,IAAK,IAAItO,EAAI,EAAGA,EAAIuB,KAAKgJ,MAAM9I,OAAQzB,IAAK,CACxC,IAAMuO,EAAWhN,KAAKgJ,MAAMiE,WAAWxO,GAGvC,GAAI,OAAOyO,KAAKlN,KAAKgJ,MAAMmE,OAAO1O,IAAK,CACnCmO,EAAYnO,EACZoO,EAAiBJ,EAIrB,GAAI,iBAAiBS,KAAKlN,KAAKgJ,MAAMmE,OAAO1O,IAAK,CAC7CgO,GAAiBzM,KAAKqK,eACtB8B,EAAWiB,KAAKX,GAChBC,EAAejM,KAAK4M,IAAIX,EAAcD,GACtCE,IAEAV,EAAIqB,EAAI,EACRrB,EAAIsB,GAAKhC,EAASiC,WAAaxB,EAAQhM,KAAKuK,aAAeyB,EAC3DQ,GAAgB,EAChB,SAGJ,GAAII,KAAe,GAAK5M,KAAK2K,UAAY,GAAKsB,EAAIqB,EAAItN,KAAK2K,UAAW,CAClE9B,KAAK4E,MAAMC,YAAYxB,EAAOU,EAAYE,EAAerO,EAAImO,GAC7DnO,EAAImO,EACJA,GAAa,IACXE,EAEFD,GAAkB7M,KAAKqK,eACvB8B,EAAWiB,KAAKP,GAChBH,EAAejM,KAAK4M,IAAIX,EAAcG,GACtCF,IAEAV,EAAIqB,EAAI,EACRrB,EAAIsB,GAAKhC,EAASiC,WAAaxB,EAAQhM,KAAKuK,aAAeyB,EAC3DQ,GAAgB,EAChB,SAGJ,IAAMmB,EAAWpC,EAASW,MAAMc,GAEhC,IAAKW,EAAU,SAEf,GAAI3N,KAAKiJ,MAAMO,SAAWgD,KAAkB,GAAKmB,EAASnE,QAAQgD,GAAe,CAC7EP,EAAIqB,GAAKK,EAASnE,QAAQgD,GAAgBR,EAG9CE,EAAMkB,KAAK,CACPT,KAAIA,EACJK,SAAQA,EACRY,SAAU,IAAI/E,KAAKoC,UACfgB,EAAIqB,EAAIK,EAASE,QAAU7B,EAC3BC,EAAIsB,EAAII,EAASG,QAAU9B,EAC3B2B,EAAS/E,QAAQyD,MAAQL,EACzB2B,EAAS/E,QAAQ2D,OAASP,GAE9B+B,QAAS,IAAIlF,KAAKoC,UACd0C,EAAS/E,QAAQoF,KAAKV,EACtBK,EAAS/E,QAAQoF,KAAKT,EACtBI,EAAS/E,QAAQyD,MACjBsB,EAAS/E,QAAQ2D,UAIzBN,EAAIqB,IAAMK,EAASM,SAAWjO,KAAKqK,gBAAkB2B,EACrDS,EAAgBR,EAAIqB,EACpBP,EAAgBtM,KAAK4M,IAAIN,EAAed,EAAIsB,EAAIhC,EAASiC,WAAaxB,GACtEQ,EAAeQ,EAGnBb,EAAWiB,KAAKX,GAChBC,EAAejM,KAAK4M,IAAIX,EAAcD,GAEtC,IAAMyB,EAAmB,GACzB,IAAK,IAAIzP,EAAI,EAAGA,GAAKkO,EAAMlO,IAAK,CAC5B,IAAI0P,EAAc,EAElB,GAAInO,KAAKiJ,MAAMM,QAAU,QAAS,CAC9B4E,EAAczB,EAAeP,EAAW1N,QACrC,GAAIuB,KAAKiJ,MAAMM,QAAU,SAAU,CACtC4E,GAAezB,EAAeP,EAAW1N,IAAM,EAEnDyP,EAAiBd,KAAKe,GAG1B,IAAMC,EAAOpO,KAAKoO,KAGlB,IAAIC,GAAU,EACd,IAAmB,IAAAC,EAAA,EAAAC,EAAArC,EAAAoC,EAAAC,EAAArO,OAAAoO,IAAK,CAAnB,IAAME,EAAID,EAAAD,GACXE,EAAKZ,SAASN,EAAIkB,EAAKZ,SAASN,EAAIY,EAAiBM,EAAK7B,MAC1D,GAAI0B,IAAWG,EAAK7B,KAAM,CACtB0B,EAASG,EAAK7B,KAEd,GAAI3M,KAAKkL,YAAc,EAAG,CACtBlL,KAAKyO,cAAc,IAAI5F,KAAKoC,UACxBuD,EAAKZ,SAASN,EAAI/B,EAASW,MAAMsC,EAAKxB,UAAUa,QAAU7B,EAC1DwC,EAAKZ,SAASL,EAAIhC,EAASW,MAAMsC,EAAKxB,UAAUc,QAAU9B,EAC1DG,EAAWkC,GACX9C,EAASiC,WAAaxB,GACvB,EAAG,MAAU,MAK5B,GAAIhM,KAAKkL,YAAc,EAAG,CACtBlL,KAAKyO,cAAczO,KAAK0O,iBAAkB,EAAG,SAAU,IAG3D1O,KAAKyK,WAAaiC,EAClB1M,KAAK0K,YAAcqC,EACnB/M,KAAKgL,kBAAoB,IAAInC,KAAKoC,UAAU,EAAG,EAAGyB,EAAcK,GAEhE/M,KAAK2O,SAAW3O,KAAK4O,WAAW1C,GAChClM,KAAK6O,IAAM7O,KAAK8O,MAAM5C,EAAOE,EAAUE,GACvCtM,KAAK+O,QAAU/O,KAAKgP,sBAAsB9C,EAAMhM,QAEhDF,KAAK+K,QACL/K,KAAKiP,cAGT1H,OAAAC,eAAWa,EAAAvJ,UAAA,OAAI,KAAf,SAAA6I,IAA4B,OAAO3H,KAAKgJ,WACxC,SAAA5J,EAAgB8P,GAASlP,KAAKgJ,MAAQhJ,KAAKmP,SAASD,GAAQlP,KAAKqL,kDACjE9D,OAAAC,eAAWa,EAAAvJ,UAAA,WAAQ,KAAnB,SAAA6I,IAA6B,OAAO3H,KAAKiJ,2CACzC1B,OAAAC,eAAWa,EAAAvJ,UAAA,UAAO,KAAlB,SAAA6I,IAA4B,OAAO3H,KAAKoP,8CACxC7H,OAAAC,eAAWa,EAAAvJ,UAAA,YAAS,KAApB,SAAA6I,IAAiC,OAAO3H,KAAKyK,gDAC7ClD,OAAAC,eAAWa,EAAAvJ,UAAA,aAAU,KAArB,SAAA6I,IAAkC,OAAO3H,KAAK0K,iDAC9CnD,OAAAC,eAAWa,EAAAvJ,UAAA,WAAQ,KAAnB,SAAA6I,IAAgC,OAAO3H,KAAK2K,+CAC5CpD,OAAAC,eAAWa,EAAAvJ,UAAA,aAAU,KAArB,SAAA6I,IAA0C,OAAO3H,KAAKgL,uDAE9C3C,EAAAvJ,UAAA+M,iBAAR,SAAyB3C,GACrB,IAAMqC,EAAW1C,KAAK2C,OAAOC,WAAWC,MAAMxC,GAC9C,IAAKqC,EAAU,OAAO1C,KAAKC,QAAQuG,MAEnC,IAAMC,EAAsB/D,EAASW,MAAM3E,OAAOgI,KAAKhE,EAASW,OAAO,IAAItD,QAAQ4G,YAAYC,SAC/F,OAAO5G,KAAK4E,MAAMiC,aAAaJ,IAG3BjH,EAAAvJ,UAAA8P,WAAR,SAAmB1C,GAAnB,IAAA5K,EAAAtB,KACI,IAAM2P,EAAY,IAAI1N,aAAaiK,EAAMhM,OAAS,EAAI,GACtD,IAAIzB,EAAI,EACRyN,EAAM0D,QAAQ,SAAApB,GAEV,IAAMlB,EAAIkB,EAAKZ,SAASN,EACxB,IAAMC,EAAIiB,EAAKZ,SAASL,EAExB,IAAMsC,EAAIrB,EAAKZ,SAASvB,MACxB,IAAMyD,EAAItB,EAAKZ,SAASrB,OAGxBoD,EAAUlR,KAAO6O,EACjBqC,EAAUlR,KAAO8O,EAEjBoC,EAAUlR,KAAO6O,EACjBqC,EAAUlR,KAAO8O,EAAIuC,EAErBH,EAAUlR,KAAO6O,EAAIuC,EACrBF,EAAUlR,KAAO8O,EAAIuC,EAErBH,EAAUlR,KAAO6O,EAAIuC,EACrBF,EAAUlR,KAAO8O,EAGjB,GAAIjM,EAAK4J,YAAc,EAAG5J,EAAKmN,cAAcD,EAAKZ,SAAU,EAAG,IAAU,MAE7E,OAAO+B,GAGHtH,EAAAvJ,UAAAgQ,MAAR,SAAc5C,EAAcE,EAAkBE,GAC1C,IAAMuC,EAAM,IAAI5M,aAAaiK,EAAMhM,OAAS,EAAI,GAChD,IAAIzB,EAAI,EACRyN,EAAM0D,QAAQ,SAAApB,GAEV,IAAMuB,EAAKvB,EAAKT,QAAQT,EAAIlB,EAC5B,IAAM4D,GAAMxB,EAAKT,QAAQT,EAAIkB,EAAKT,QAAQ1B,OAASD,EACnD,IAAM6D,GAAMzB,EAAKT,QAAQR,EAAIiB,EAAKT,QAAQxB,QAAUD,EACpD,IAAM4D,EAAK1B,EAAKT,QAAQR,EAAIjB,EAE5BuC,EAAIpQ,KAAOsR,EACXlB,EAAIpQ,KAAOyR,EAEXrB,EAAIpQ,KAAOsR,EACXlB,EAAIpQ,KAAOwR,EAEXpB,EAAIpQ,KAAOuR,EACXnB,EAAIpQ,KAAOwR,EAEXpB,EAAIpQ,KAAOuR,EACXnB,EAAIpQ,KAAOyR,IAEf,OAAOrB,GAGHxG,EAAAvJ,UAAAkQ,sBAAR,SAA8BjD,GAE1B,IAAMoE,EAAepE,EAAO,EAC5B,IAAMgD,EAAU,IAAI3M,YAAY+N,GAGhC,IAAK,IAAI1R,EAAI,EAAG2R,EAAI,EAAG3R,EAAI0R,EAAc1R,GAAK,EAAG2R,GAAK,EAAG,CACrDrB,EAAQtQ,EAAI,GAAK2R,EAAI,EACrBrB,EAAQtQ,EAAI,GAAK2R,EAAI,EACrBrB,EAAQtQ,EAAI,GAAK2R,EAAI,EACrBrB,EAAQtQ,EAAI,GAAK2R,EAAI,EACrBrB,EAAQtQ,EAAI,GAAK2R,EAAI,EACrBrB,EAAQtQ,EAAI,GAAK2R,EAAI,EAEzB,OAAOrB,GAGH1G,EAAAvJ,UAAA2P,cAAR,SAAsB4B,EAAsBC,EAA2BC,EAA8BC,GAAzD,GAAAF,SAAA,GAAAA,EAAA,EAA2B,GAAAC,SAAA,GAAAA,EAAA,SAA8B,GAAAC,SAAA,GAAAA,EAAA,EACjG,IAAMC,EAAQ,IAAI5H,KAAK6H,SACvBD,EAAME,YAAc,KACpBF,EACCG,UAAUN,EAAeC,EAAWC,GACpC5C,SAASyC,EAAK/C,EAAG+C,EAAK9C,EAAG8C,EAAKhE,MAAOgE,EAAK9D,QAC3CvM,KAAK6Q,SAASJ,IAGVpI,EAAAvJ,UAAAqQ,SAAR,SAAiB2B,GACb,OAAOA,EAAMzL,QAAQ,aAAc,OAE3C,OAAAgD,EArTA,CAA8BQ,KAAKkI,KAAKC,MAA3BhS,EAAAqJ,qaCxBb,IAAM4I,EAAajT,EAAQ,GAC3B,IAAMkT,EAAalT,EAAQ,GAE3B,IAAAuK,EAAA,SAAAC,GAAkCC,EAAAF,EAAAC,GAI9B,SAAAD,EAAY4I,UACR3I,EAAAxB,KAAAhH,KAAMmR,IAASnR,KAGZuI,EAAAzJ,UAAAsS,gBAAP,WACI,IAAMC,EAAKrR,KAAKmR,SAASE,GACzBrR,KAAKsR,OAAS,IAAIzI,KAAK0I,OAAOF,EAAIJ,EAAYC,IAG3C3I,EAAAzJ,UAAA0S,OAAP,SAAcC,GACV,IAAMN,EAAWnR,KAAKmR,SACtB,IAAMvI,EAAU6I,EAAS7I,QACzB,IAAM8I,EAAOD,EAASlG,SACtB,IAAM8F,EAAKF,EAASE,GAGpB,IAAKzI,IAAYA,EAAQ+I,QAAUD,EAAK5F,QAAS,OAEjD,IAAI8F,EAASH,EAASI,QAAQV,EAASW,aAEvC,IAAKF,EAAQ,CAETA,EAAS,CACLN,OAAQtR,KAAKsR,OACbS,aAAclJ,KAAKmJ,OAAOC,SAASC,mBAAmBb,EAAII,EAAS9C,SAAU0C,EAAGc,aAChFC,SAAUvJ,KAAKmJ,OAAOC,SAASC,mBAAmBb,EAAII,EAAS5C,IAAKwC,EAAGc,aACvEE,YAAaxJ,KAAKmJ,OAAOC,SAASK,kBAAkBjB,EAAII,EAAS1C,QAASsC,EAAGkB,aAE7EC,IAAK,KACLzH,MAAO0G,EAAS1G,MAChBkE,WAAYwC,EAASxC,YAEzB2C,EAAOY,IAAM,IAAI3J,KAAKmJ,OAAOS,kBAAkBpB,GAC1CqB,SAASd,EAAOS,aAChBM,aAAaf,EAAOG,aAAcH,EAAON,OAAOsB,WAAWC,gBAAiBxB,EAAGyB,MAAO,MAAO,EAAI,EAAG,GACpGH,aAAaf,EAAOQ,SAAUR,EAAON,OAAOsB,WAAWG,cAAe1B,EAAGyB,MAAO,MAAO,EAAI,EAAG,GAEnGrB,EAASI,QAAQV,EAASW,aAAeF,EAG7CT,EAAS6B,QAAQpB,EAAOY,KAExB,GAAIf,EAAS1G,QAAU6G,EAAO7G,MAAO,CACjC6G,EAAO7G,MAAQ0G,EAAS1G,MACxB6G,EAAOQ,SAASa,OAAOxB,EAAS5C,KAGpC,GAAI4C,EAASxC,aAAe2C,EAAO3C,WAAY,CAC3C2C,EAAO3C,WAAawC,EAASxC,WAC7B2C,EAAOS,YAAYY,OAAOxB,EAAS1C,SAGvC6C,EAAOG,aAAakB,OAAOxB,EAAS9C,UAEpCwC,EAAS+B,WAAWtB,EAAON,QAE3BM,EAAON,OAAO6B,SAASC,SAAWjC,EAASkC,YAAYzK,GAEvD,GAAIuI,EAAS/P,MAAO+P,EAAS/P,MAAMkS,aAAa7B,EAAS8B,WAEzD3B,EAAON,OAAO6B,SAASK,kBAAoB/B,EAASgC,eAAeC,QAAQ,MAC3E9B,EAAON,OAAO6B,SAASQ,QAAUlC,EAASmC,WAC1ChC,EAAON,OAAO6B,SAASU,QAAUhL,KAAK4E,MAAMqG,QAAQpC,EAAKtI,OACzDwI,EAAON,OAAO6B,SAASY,WAAarC,EAAKvI,SAAWsI,EAASzF,MAAMsB,EACnEsE,EAAON,OAAO6B,SAASa,eAAiB,EACxCpC,EAAON,OAAO6B,SAASc,SAAWvC,EAAKpI,OACvCsI,EAAON,OAAO6B,SAASe,UAAYxC,EAAK1H,QACxC4H,EAAON,OAAO6B,SAASjJ,aAAewH,EAAKxH,aAC3C0H,EAAON,OAAO6B,SAAS1J,YAAcZ,KAAK4E,MAAMqG,QAAQpC,EAAKjI,aAC7DmI,EAAON,OAAO6B,SAAS/E,KAAOvF,KAAK4E,MAAMqG,QAAQrC,EAASrD,MAC1DwD,EAAON,OAAO6B,SAASgB,UAAYzC,EAAKhI,WACxCkI,EAAON,OAAO6B,SAASiB,aAAe,IAAInS,aAAa,CAACyP,EAAK5H,iBAAiBwD,EAAGoE,EAAK5H,iBAAiBwD,IACvGsE,EAAON,OAAO6B,SAASkB,YAAcxL,KAAK4E,MAAMqG,QAAQpC,EAAK/H,iBAC7DiI,EAAON,OAAO6B,SAASmB,YAAc5C,EAAK9H,gBAC1CgI,EAAON,OAAO6B,SAASoB,gBAAkB7C,EAAK7H,eAE9C,IAAM2K,EAAW/C,EAAS+C,SAAWnD,EAAGoD,UACxC7C,EAAOY,IAAIkC,KAAKF,EAAU/C,EAAS1C,QAAQ7O,OAAQ,IAE3D,OAAAqI,EAnFA,CAAkCM,KAAK8L,gBAA1B3V,EAAAuJ,eAqFbM,KAAK+L,cAAcC,eAAe,OAAQtM,kBC5F1CxJ,EAAAC,QAAA,+ZCAAD,EAAAC,QAAA,y3DCeA,IAAAuD,YACAuS,SAAA,aACAA,OAAAtS,UACAsS,OAAAtS,SAAAS,eASA,IAAAZ,EAAA,CAEAE,YAEAwS,qBAAAC,SAAA,YAEAC,qBACA1S,MAAAuS,OAAAI,kBAAAJ,OAAAK,aAEAC,eAAA7S,KAAAuS,OAAAO,OAEAC,YAAA/S,GAIAxD,EAAAC,QAAAqD,4KCxCA,IAAMkT,EAAcT,OAAOU,SAASC,KAAKC,QAAQ,cAAgB,EAC7D,GAAK,6BAGHC,aACF,SAAAA,EAAYhN,GAASiN,IAAA5V,KAAA2V,GACjB3V,KAAK2I,QAAUA,EACf3I,KAAK6V,YAAc,4CAGhBC,GACH,OAAO,IAAIzN,SAASA,SAASyN,EAAM9V,KAAK2I,wCAGvCoN,EAAcC,GAAU,IAAA1U,EAAAtB,KAEzB,OAAO,IAAIuB,QAAQ,SAACC,GAChB,GAAIF,EAAKuU,YAAa,CAClBrU,QACG,CACHuU,EACKE,OACAC,IAAI,CAACX,EAAc,6CACnBY,KAAK,SAACC,GACH3U,QAAQC,IAAI,SAAU0U,GACtB5U,yBAOxB,IAAM6U,EAAc,CAChBnN,SAAU,kBACVC,SAAU,KACVE,UAAW,SACXC,OAAQ,IACRW,gBAAiB,EAEjBP,WAAY,MAKZH,MAAO,OACPe,cAAe,EACfF,eAAgB,EAChBe,WAAY,GAIhB,IAAMmL,EAAS,IAAIX,EAAgBU,sCCpDnCrY,EAAAmJ,EAAAoP,EAAA,sBAAAC,IAAA,IAAAC,EAAAzY,EAAA,YAAA0Y,EAAA1Y,EAAAuB,EAAAkX,GAEA,IAAME,EAAU,SAAVA,EAAWvN,EAAOlH,GACpB,IAAI0U,EAAI,EACR,IAAIC,EAAI,GACR3U,EAAK4U,SAEAC,UAAU3N,GACV4N,gBAAgB9U,EAAKmK,MAAQwK,EAAI,EAAID,EAAG1U,EAAKqK,OAASsK,EAAI,EAAID,EAAGC,EAAGA,EAAG,GACvEI,WAwBT,IAAMC,EAAY,SAAZA,EAAa9N,EAAOlH,GAEtB,IAAI0U,EAAI,EACR,IAAIC,EAAI,GACR3U,EAAK4U,SAEAC,UAAU3N,GACV4N,gBAAgB9U,EAAKmK,MAAQ,GAAK,EAAIuK,EAAGA,EAAI,EAAG,GAAIC,EAAG,GACvDI,WAGT,SAAST,EAAaM,EAAUjH,EAAGC,EAAGqH,EAAMC,GAExC,IAAIlV,EAAO,CAAEmK,MAAOwD,EAAGtD,OAAQuD,EAAGgH,YAClCrV,QAAQC,IAAI,KAAMyV,GAClB,OAAQA,EAAKE,MACT,IAAK,UACDV,EAAQ,MAAUzU,GAClB,MACJ,IAAK,YACDyU,EAAQ,IAAUzU,GAClB,MACJ,IAAK,gBACDyU,EAAQ,SAAUzU,GAClB,MACJ,IAAK,OACDyU,EAAQ,SAAUzU,GAClB,MACJ,IAAK,QACDyU,EAAQ,QAAUzU,GAClB,MACJ,IAAK,kBACDyU,EAAQ,QAAUzU,GAClB,MACJ,QACIyU,EAAQ,SAAUzU,GAClB,MAGR,GAAIiV,EAAKA,KAAK5K,QAAU,MAAO,CAC3B2K,EAAU,SAAUhV,wCC1E5B,IAAAsP,EAAA,WAA0B,IAAA8F,EAAAtX,KAAa,IAAAuX,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,WAAAC,MAAA,GAAAN,EAAAO,WAAkD,CAAAJ,EAAA,OAAYE,YAAA,YAAuB,CAAAL,EAAAQ,SAAA,QAAAL,EAAA,OAAmCE,YAAA,WAAsB,CAAAF,EAAA,OAAYE,YAAA,aAAwB,CAAAF,EAAA,OAAYE,YAAA,UAAqB,CAAAF,EAAA,OAAYE,YAAA,OAAkB,CAAAF,EAAA,OAAYE,YAAA,QAAAI,YAAA,CAAiCC,cAAA,QAAqB,CAAAP,EAAA,WAAgBE,YAAA,eAAAM,MAAA,CAAkCC,MAAA,uBAAAC,MAAA,QAAAC,UAAA,YAAAC,OAAA,SAAAC,KAAA,QAAuGC,MAAA,CAAQrJ,MAAAoI,EAAA,OAAAtB,SAAA,SAAAwC,GAA4ClB,EAAAmB,OAAAD,GAAeE,WAAA,WAAsB,CAAAjB,EAAA,UAAeQ,MAAA,CAAOU,KAAA,SAAAvR,KAAA,SAAA+Q,MAAA,SAAgDQ,KAAA,YAAe,OAAAlB,EAAA,OAAoBE,YAAA,cAAAI,YAAA,CAAuCC,cAAA,MAAAY,eAAA,QAA0C,CAAAnB,EAAA,YAAiBE,YAAA,QAAAM,MAAA,CAA2BK,KAAA,OAAAO,eAAA,eAAAR,OAAA,SAAAF,MAAA,QAAAW,gBAAA,gBAAAC,aAAA,IAAAC,YAAA,YAAAC,gBAAA,gBAAAtQ,QAAA2O,EAAA4B,cAAAC,SAAA,YAAyOZ,MAAA,CAAQrJ,MAAAoI,EAAA,WAAAtB,SAAA,SAAAwC,GAAgDlB,EAAA8B,WAAAZ,GAAmBE,WAAA,eAA0B,CAAAjB,EAAA,UAAeQ,MAAA,CAAOU,KAAA,SAAAvR,KAAA,iBAAA+Q,MAAA,SAAwDQ,KAAA,YAAe,aAAAlB,EAAA,eAAkCQ,MAAA,CAAOoB,OAAA/B,EAAAgC,gBAAAC,IAAAjC,EAAAiC,IAAAC,gBAAAlC,EAAAkC,oBAAkF,GAAAlC,EAAAmC,UAC35C,IAAAC,EAAA,0KCDA,IAAIC,EAAM,WAAgB,IAAArC,EAAAtX,KAAa,IAAAuX,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,qBAC1G,IAAIiC,EAAe,oSCSnB,IAAIC,EAAQ,CACRC,qBAAsB,WACtBC,uBAAwB,kBAGtBC,aAEF,SAAAA,IAAc,IAAA1Y,EAAAtB,KAAAia,IAAAja,KAAAga,GAEVha,KAAKka,QAAU,MACfla,KAAKma,QAAU,MACfna,KAAKoa,MAAQ,GACbpa,KAAKqa,OAAS,GACdra,KAAKsa,QAAU,GACfta,KAAKua,aAAe,GACpBva,KAAKwa,aAAe,GAEpBxa,KAAKya,MAAQ,GAIb3F,OAAOI,iBAAiB,SAAU,SAACkB,GAC/B,IAAK9U,EAAKoZ,QAAS,OADkB,IAAAC,EAEbrZ,EAAKsZ,UAAvBvO,EAF+BsO,EAE/BtO,MAAOE,EAFwBoO,EAExBpO,OACbjL,EAAKoZ,QAAQvJ,SAAS0J,OAAOxO,EAAOE,GACpCjL,EAAKoZ,QAAQI,KAAKC,MAAM,aAAxB,GAAAC,OAA0C3O,EAA1C,MACA/K,EAAK2Z,oBAAoBC,QACzB5Z,EAAK6Y,QAAU,MACf7Y,EAAKoT,SAGT1U,KAAKuZ,IAAM,CAAElN,MAAO,IAAKE,OAAQ,GAAI4O,KAAM,EAAGC,KAAM,EAAGC,OAAQ,4CAGzD9B,GAEN,GAAIA,GAAOA,EAAIlN,OAASkN,EAAIhN,QAAUgN,EAAI6B,MAAQ7B,EAAI4B,MAAQ5B,EAAI8B,OAAQ,CACtErb,KAAKuZ,IAAMA,EACX,GAAIvZ,KAAKsb,UAAWtb,KAAKsb,UAAUJ,QACnClb,KAAKub,SAAW,GAChBvb,KAAKya,MAAQ,+CAIJ,IAAA9Y,EAAA3B,KAEb,GAAIA,KAAK0a,QAAS,OAElB1a,KAAKwb,YAAc,IAAIC,IACvBzb,KAAKqa,OAAS,GACdra,KAAK0b,SAAW,CAAEC,IAAK,GANV,IAAAC,EAQW5b,KAAK4a,UAAvBvO,EAROuP,EAQPvP,MAAOE,EARAqP,EAQArP,OAEbvM,KAAK6b,MAAQ,MAOb7b,KAAK0a,QAAU,IAAI7R,iBAAiBwD,EAAOE,EAAQ,CAAEuP,qBAAsB,EAAGC,gBAAiB,QAAUC,WAAY,IAErH,IAAItB,EAAU1a,KAAK0a,QAEnB1a,KAAKic,SAAW,IAAIpT,eACpB7I,KAAKkc,YAAc,IAAIrT,eACvB7I,KAAKic,SAAW,IAAIpT,eAEpB7I,KAAKmc,iBAAmB,IAAItT,cAC5B7I,KAAKib,oBAAsB,IAAIpS,cAE/B7I,KAAKic,SAASpL,SAAS7Q,KAAKib,qBAC5Bjb,KAAKic,SAASpL,SAAS7Q,KAAKmc,kBAE5Bnc,KAAK0a,QAAQ0B,MAAMvL,SAAS7Q,KAAKic,UACjCjc,KAAK0a,QAAQ0B,MAAMvL,SAAS7Q,KAAKkc,aAEjClc,KAAKqc,IAAIC,IAAIC,YAAY7B,EAAQI,MACjC9a,KAAK0a,QAAQI,KAAKC,MAAM,aAAxB,GAAAC,OAA0C3O,EAA1C,MAEArM,KAAKwc,MAAQ,EACbxc,KAAKyc,QAAU,MAEfzc,KAAK0c,cAAgB1b,IAAE2b,SAAS3c,KAAK0U,KAAM,IAC3C1U,KAAK0a,QAAQvJ,SAASyL,QAAQC,YAAYC,mBAAqB,MAE/D9c,KAAK0a,QAAQqC,OAEbzG,OAAOtU,KAAKhC,KAAK0a,SAASsC,KAAK,WAC3Brb,EAAKka,MAAQ,KAEbla,EAAK+S,SAGT1U,KAAKub,SAAW,GAChBvb,KAAKsb,UAAY,IAAIG,kCAMrBrF,GAAG,IACY9I,EAAkB8I,EAA3B6G,QAAqB1P,EAAM6I,EAAf8G,QADf,IAAAC,EAEoBnd,KAAKod,QAAnBC,EAFNF,EAEG7P,EAAUgQ,EAFbH,EAEU5P,EACb,IAAIgQ,EAAKjQ,EAAI+P,EACb,IAAIG,EAAKjQ,EAAI+P,EACbtd,KAAKyd,SAASC,IAAI,SAAAnE,GACd9X,QAAQC,IAAI,MAAO6X,GACnB,GAAIgE,GAAMhE,EAAIjM,GAAKiQ,GAAMhE,EAAIjM,EAAIiM,EAAI1J,GACjC2N,GAAMjE,EAAIhM,GAAKiQ,GAAMjE,EAAIhM,EAAIgM,EAAIzJ,EAAG,KAI5CrO,QAAQC,IAAI,MAAO6b,EAAIC,wCAGhBG,GAAsB,IAAA/b,EAAA5B,KAC7BA,KAAKqc,IAAMsB,EACX3d,KAAK4d,iBACL5d,KAAKqc,IAAIC,IAAIC,YAAYvc,KAAK0a,QAAQI,MACtC9a,KAAKwc,MAAQ,EAEbxc,KAAKqc,IAAIC,IAAIpH,iBAAiB,aAAc,SAAC2I,GACzC,IAAMC,EAAaC,IAAeF,GAClCjc,EAAK4a,OAASsB,EAAWE,OACzBpc,EAAK8a,kBAIT1c,KAAK0b,SAAW,CAAEC,GAAI3b,KAAKqc,IAAI4B,OAAOC,OAAOvC,IAC7C3b,KAAK0U,yCAIL,IAAI3I,EAAO/L,KAAKqc,IAAIC,IAAI6B,wBACxB,MAAO,CAAE7Q,EAAGvB,EAAKqS,IAAK7Q,EAAGxB,EAAKsS,0CAK9B,IAAItS,EAAO/L,KAAKqc,IAAIC,IAAI6B,wBACxB,IAAIG,EAAO,GAAK,EAChB,OAAQte,KAAKue,aACT,KAAK1E,EAAMC,qBACP,MAAO,CAAEzN,MAAOiS,EAAM/R,OAAQR,EAAKQ,QACnC,MACJ,KAAKsN,EAAME,uBACP/Z,KAAKwe,UAAY/d,KAAKge,MAAM1S,EAAKM,MAAQrM,KAAKuZ,IAAIlN,OAASrM,KAAKuZ,IAAI6B,KACpEpb,KAAKwe,UAAYxe,KAAKwe,UAAYxe,KAAKuZ,IAAI6B,MAAQ,EAAIpb,KAAKwe,UAAYxe,KAAKwe,UAAYxe,KAAKuZ,IAAI6B,KAClG,MAAO,CAAE/O,MAAON,EAAKM,MAAOE,OAAQ+R,GACpC,MACJ,QACIte,KAAKwe,UAAY/d,KAAKge,MAAM1S,EAAKM,MAAQrM,KAAKuZ,IAAIlN,OAASrM,KAAKuZ,IAAI6B,KACpEpb,KAAKwe,UAAYxe,KAAKwe,UAAYxe,KAAKuZ,IAAI6B,MAAQ,EAAIpb,KAAKwe,UAAYxe,KAAKwe,UAAYxe,KAAKuZ,IAAI6B,KAElG,MAAO,CAAE/O,MAAON,EAAKM,MAAOE,OAAQ+R,GACpC,+CAIEnH,GAEV,GAAIA,EAAKE,MAAQ,UAAW,OAE5B,IAAIqH,EAAQvH,EAEZ,IAAIwH,EAAG,IAAA3D,OAAO7D,EAAKE,MAAQ,OAAS,SAAW,aAAxC,KAAA2D,OAAwDhb,KAAKqc,IAAI4B,OAAOC,OAAOU,mBAA/E,KAAA5D,OAAqG7D,EAAKA,KAAKwE,GAA/G,KAEP,GAAIxE,EAAKE,MAAQ,qBAAsB,CACnCsH,EAAG,eAAA3D,OAAkBhb,KAAKqc,IAAI4B,OAAOC,OAAOU,mBAAzC,KAAA5D,OAA+D7D,EAAKA,KAAK0H,OAAzE,KAAA7D,OAAmF7D,EAAKA,KAAKwE,GAA7F,KAGP3b,KAAKqc,IAAIyC,QAAQ1R,KAAKuR,GACtB3e,KAAK0b,SAAW,CAAEC,GAAIxE,EAAKA,KAAKwE,IAChC3b,KAAK0U,OAELjT,QAAQC,IAAIid,gDAGGI,GAEf,IAAI1D,EAASrb,KAAKuZ,IAAI8B,OAEtB,IAAI2D,EAAahf,KAAKuZ,IAAIlN,MAC1B,IAAI4S,EAAcjf,KAAKuZ,IAAIhN,OAE3B,IAAI2S,EAAgBlf,KAAKuZ,IAAI4B,KAC7B,IAAIgE,EAAgBnf,KAAKuZ,IAAI6B,KAE7B,IAAIgE,EAAoBF,EAAgBC,EAExC,IAAME,EAAc,SAAdA,EAAeN,GAEjB,IAAIO,EAAQ7e,KAAK8e,MAAMR,EAAKK,GAE5B,IAAII,EAAQT,EAAKO,EAAQF,EACzB,IAAIK,EAAMhf,KAAK8e,MAAOC,EAASL,GAC/B,IAAIO,EAAMF,EAAQC,EAAMN,EACxB,IAAIQ,EAASlf,KAAKge,MAAMM,EAAKK,GAE7B,MAAO,CACH9R,EAAImS,EAAMT,EACLM,EAAQN,EAAaE,EAAiB7D,EAAS,EACpD9N,EAAGmS,EAAMT,EAAc5D,EAAS,IAIxC,OAAAuE,IAAA,GAAYP,EAAYN,GAAxB,CAA6BlP,EAAGmP,EAAa3D,EAAQvL,EAAGmP,EAAc5D,uCAGjE0D,EAAI5H,EAAME,GAAM,IAAAxV,EAAA7B,KAErB,IAAI6f,EAAS,IAAIhX,cAFI,IAAAiX,EAIA9f,KAAK+f,mBAAmBhB,GAAvCzR,EAJewS,EAIfxS,EAAGC,EAJYuS,EAIZvS,EAAGsC,EAJSiQ,EAITjQ,EAAGC,EAJMgQ,EAINhQ,EAEf,IAAI4L,EAAYvE,EAAKwE,IAAM3b,KAAKqc,IAAI4B,OAAOC,OAAOvC,GAElD,IAAKD,GAAY1b,KAAKqc,IAAI4B,OAAOC,OAAQ,EAIzC2B,EAEK9I,UACG2E,EAAW,QAAW,SACxB9N,SAAS,EAAG,EAAGiC,EAAGC,GAAGmH,UAE3BT,eAAaqJ,EAAQhQ,EAAGC,EAAG,CAAEqH,OAAME,QAAQ,WAE3C,IAAIjQ,EAAOpG,IAAEgf,SAAS7I,EAAK/P,KAAM,CAC7B6Y,SAAY,IACZ/f,OAAUF,KAAKuZ,IAAIlN,MAAQ,KAG/B,GAAI8K,EAAK/P,KAAKlH,OAAS,GAAI,CACvBkH,GAAQ,KAAO+P,EAAK/P,KAAK8Y,UAAUlgB,KAAKuZ,IAAIlN,MAAQ,GAAI8K,EAAK/P,KAAKlH,QAGtE2f,EAAOhP,SAAS7Q,KAAKmgB,SAAS/Y,EAAO,KAAM,EAAG,EAA9B,GAAA4T,OAAoC7D,EAAK/P,MAAzC4T,OAAgD7D,EAAKwE,IAArDX,OAA0D3D,GAA1D2D,OAAiEU,KAEjFmE,EAAOO,QAAU,IAAIvX,eAAe,EAAG,EAAGgH,EAAGC,GAC7C+P,EAAOQ,SAAS/S,EAAIA,EACpBuS,EAAOQ,SAAS9S,EAAIA,EACpBsS,EAAOS,YAAc,KACrBT,EAAOU,WAAa,KACpBV,EAAOW,OAAS,UAShB,IAAIC,EAAM,CACNpJ,OACAsE,GAAIxE,EAAKwE,IAGb,IAAI+E,EAAOC,IAAIC,IAAJ,GAAA5F,OAAWyF,EAAI9E,IAAfX,OAAoByF,EAAIpJ,MAAxB2D,OAA+BU,GAAY,OAAQmF,SAAS,IAEvE,IAAI3K,EAAM,SAANA,EAAOhU,EAAMyZ,GAAP,IAAWmF,EAAXxgB,UAAAJ,OAAA,GAAAI,UAAA,KAAAC,UAAAD,UAAA,GAAqB,MAArB,OACNygB,OAAKC,SAASC,WAAW/K,IAAI,CAAEhU,KAAMA,EAAMyZ,KAAImF,YAAW9D,KAAK,SAACkE,GAC5Dzf,QAAQC,IAAI,YAAawf,GAEzB,IAAIC,EAAK,IAAItY,iBAAiBqY,EAAOE,cACrCD,EAAGE,gBACH,IAAIzY,EAAU,IAAIC,aAAasY,GAC/BvY,EAAQ0Y,cAAgB,CAAEhU,EAAG,EAAGC,EAAG,GACnC1L,EAAK4Y,MAAMiG,GAAQ9X,EACnB,IAAI2Y,EAAU,IAAI1Y,YAAYD,GAC9BiX,EAAO2B,WAAWD,EAAS,GAE3B1B,EAAO4B,cAAgB,KAEvB,IAAKX,EAASC,OAAKW,IAAIC,qBAAqBhG,EAAI,CAAEiG,gBAAiBV,EAAOW,SAGlF,GAAI7hB,KAAKub,SAASmF,GAAO,CACrB,IAAIa,EAAU,IAAI1Y,YAAY7I,KAAKya,MAAMiG,IACzCb,EAAOhP,SAAS0Q,OACb,CACH,IAAIO,EAAO,KACX,GAAIzK,GAAQ,sBAAwBA,GAAQ,iBAAkB,CAE1D5V,QAAQC,IAAI,qBAAsByV,GAElC,GAAIA,EAAKyK,gBAAiB,CACtB,IAAIC,EAAM1K,EAAKyK,gBACf5hB,KAAKub,SAASmF,GAAQmB,EACtB3L,EAAI2L,EAAK1K,EAAKwE,GAAI,UACf,CACHoF,OAAKW,IAAIG,IAAIE,aAAa,CACtB1K,KAAM,qBACNsE,GAAIxE,EAAKwE,GACTqG,QAAShiB,KAAKiiB,kBAEbC,WAAW,MACXC,KAAK,SAACN,GACHpgB,QAAQC,IAAI,MAAOmgB,GACnBhgB,EAAK0Z,SAASmF,GAAQmB,EACtB3L,EAAI2L,EAAK1K,EAAKwE,KACf,yBAGR,CACH3b,KAAKub,SAASmF,GAAQ,KACtB1gB,KAAKya,MAAMiG,GAAQb,EACnBA,EAAO4B,cAAgB,MAO/B,IAAIW,EAAO,SAAPA,IACA,OAAO,SAAChM,GACJ,IAAIyJ,EAAShe,EAAKwgB,YAClB,IAAKxC,EAAQ,OACb,GAAIA,EAAOrD,MAAO,OAEX,GAAIqD,EAAOrD,QAAU,OAAS3a,EAAK6Y,QAAS,CAE/C,IAAIxY,EAAO2d,EAAO3d,KAClB,IAAIogB,EAAczC,EAAO3d,KAAKqgB,iBAAiB1gB,EAAK6Y,QAAQ0B,OAE5D,IAAIoG,EAAc3C,EAAO4C,cAEzB,IAAIC,EAAIjiB,KAAKkiB,KACTliB,KAAKmiB,IAAIN,EAAY/U,EAAIiV,EAAYjV,EAAG,GACxC9M,KAAKmiB,IAAIN,EAAYhV,EAAIkV,EAAYlV,EAAG,IAG5C,GAAIoV,EAAI,IAAK,CACT7C,EAAOrD,MAAQ,MAGvBpG,EAAElU,KAAK2gB,cAAcC,mBAI7B,IAAIziB,EAAM,SAANA,EAAO8W,EAAM0I,GACb,OAAO,WACH,IAAKhe,EAAKwgB,YAAa,OACvBxgB,EAAKwgB,YAAYU,SAAW,MAC5B,GAAIlhB,EAAKwgB,YAAY7F,OAAS,MAAQ3a,EAAKmhB,UAAW,CAClD,GAAInhB,EAAK2X,iBAAmB,MAAO,CAE/B,GAAI3X,EAAKwgB,YAAYY,WAAY,CAC7BphB,EAAKqhB,cAAc,CAAE/L,KAAMtV,EAAKmhB,UAAW3L,SAC3CxV,EAAK6a,oBACF,CACH,IAAIvU,EAAItG,EAAKwgB,YACbla,EAAE8a,WAAa,KACfE,WAAW,WACPhb,EAAE8a,WAAa,OAChB,OAIfphB,EAAKmhB,UAAY,KACjBnhB,EAAKwgB,YAAc,OAI3B,IAAIviB,EAAQ,SAARA,EAAS6b,EAAI/E,EAAGiJ,EAAQ1I,GACxB,OAAO,SAACf,GACJ3U,QAAQC,IAAI,IAAK0U,GACjBvU,EAAKwgB,YAAcxC,EACnBhe,EAAKmhB,UAAY7L,EACjB0I,EAAOrD,MAAQ,MACfqD,EAAO3d,KAAOkU,EAAElU,KAChB2d,EAAO4C,cAAgB5C,EAAO3d,KAAKqgB,iBAAiB1gB,EAAK6Y,QAAQ0B,OACjEva,EAAK6Z,SAAW,CAAEC,MAClBoF,OAAKqC,YAAYC,IAAIC,YAAYzhB,EAAKwa,IAAIkH,OAAQ5H,EAAI/E,EAAGR,EAAElU,KAAK2gB,iBAIxEhD,EAAO2D,GAAG,YAAa1jB,EAAMqX,EAAKwE,GAAItE,EAAMwI,EAAQ1I,IAC/CqM,GAAG,UAAWnjB,EAAI8W,EAAM0I,IACxB2D,GAAG,iBAAkBnjB,EAAI8W,EAAM0I,IAGpCA,EAAO2D,GAAG,YAAapB,EAAKvC,IAE5BA,EAAO2D,GAAG,YAAa,SAACpN,GACpBA,EAAElU,KAAK2gB,cAAcC,iBACrB1M,EAAElU,KAAK2gB,cAAcY,kBACrB1C,OAAKqC,YAAYM,YAAYC,KAAKvN,EAAGe,EAAM,aAI/CnX,KAAKkc,YAAYrL,SAASgP,GAC1B7f,KAAKsa,QAAQlN,KAAKyS,GAElB,OAAOA,8CAKP7f,KAAK+iB,SAAW,uCAGb,IAAAa,EAAA5jB,KAEH,IAAKA,KAAK6b,MAAO,OAIjB7b,KAAKyc,QAAU,KAUfzc,KAAKsa,QAAQoD,IAAI,SAACmC,EAAQphB,GACtB,GAAIohB,EAAQ,CACR+D,EAAK1H,YAAY2H,YAAYhE,GAE7B+D,EAAKtJ,QAAQ7b,GAAK,QAK1BuB,KAAKsa,QAAUta,KAAKsa,QAAQwJ,OAAOC,SAEnC/jB,KAAKmc,iBAAiBjB,QA3BnB,IAAA8I,EA8B2BhkB,KAAK4a,UAA7BvO,EA9BH2X,EA8BG3X,MAAOE,EA9BVyX,EA8BUzX,OAAQ6O,EA9BlB4I,EA8BkB5I,KACrB,IAAKpb,KAAKma,QAAS,CACf,IAAI8J,EAAW,GACf,IAAK,IAAIxlB,EAAI,EAAGA,EAAI4N,EAAQ4X,EAAUxlB,IAAK,CACvC,IAAK,IAAI2R,EAAI,EAAGA,EAAI7D,EAAS0X,EAAU7T,IAAK,CACxCpQ,KAAKib,oBAAoBrK,UAAU,EAAG,EAAG,GAAGhD,SAASnP,EAAIwlB,EAAU7T,EAAI6T,EAAU,EAAG,IAG5FjkB,KAAKma,QAAU,KAGnBna,KAAKyc,QAAU,MAEf,GAAIzc,KAAKqc,IAAIhD,OAAQ,CACjB,IAAI6K,EAAc,EAAG7W,EAAMrN,KAAKwe,UAAY,EAAG5H,EAAI5W,KAAKqc,IAAIhD,OAAOnZ,OAAS,EACxEJ,EAAQW,KAAK0jB,KAAKnkB,KAAKwc,MAAQ,IAG/B1c,GAASE,KAAKuZ,IAAI6B,KAClBtb,EAAQ,GAAKA,EAAQ,EAAGE,KAAKwc,MAAQ,GAAK,MAI9Cxc,KAAKyd,SAAW,GAEhB,IAAK,IAAIhf,EAAI,EAAG2lB,EAAazD,IAAIC,IAAI,MAASniB,EAAI4O,EAAM,EAAG5O,IAAK,CAC5D,GAAIqB,EAAQokB,EAActN,EAAG,OAC7B,IAAI4I,EAAQ0E,EAAcpkB,EAC1B,IAAIukB,EAAUrkB,KAAKqc,IAAIhD,OAAOvZ,EAAQokB,GACtC,IAAI/M,EAAOkN,EAAQlN,KACnB,IAAIkC,EAAS,CAAEhC,KAAMgN,EAAQhN,MAL+B,IAAAiN,EAOvCtkB,KAAK+f,mBAAmBmE,GAAvC5W,EAPsDgX,EAOtDhX,EAAGC,EAPmD+W,EAOnD/W,EAAGsC,EAPgDyU,EAOhDzU,EAAGC,EAP6CwU,EAO7CxU,EACf9P,KAAKyd,SAASrQ,KAAK,CAAEE,IAAGC,IAAGsC,IAAGC,IAAGiP,GAAImF,IAErC,IAAIxD,EAAO0D,EAAWG,OAAX,GAAAvJ,OACJ7D,EAAKwE,IADDX,OACM3B,EAAOhC,MADb2D,OACoB7D,EAAKwE,IAAM3b,KAAK0b,SAASC,KACtD6I,SAAS3D,SAAS,IAEpB,GAAI7gB,KAAKsb,UAAUmJ,IAAI/D,GAAO,CAC1B,IAAIb,EAAS7f,KAAKsb,UAAU3T,IAAI+Y,GAChC1gB,KAAKsa,QAAQlN,KAAKyS,GAFQ,IAAA6E,EAGX1kB,KAAK+f,mBAAmBmE,GAAjC5W,EAHoBoX,EAGpBpX,EAAGC,EAHiBmX,EAGjBnX,EACTsS,EAAOQ,SAAS/S,EAAIA,EACpBuS,EAAOQ,SAAS9S,EAAIA,EACpBvN,KAAKkc,YAAYrL,SAASgP,OACvB,CACH,IAAIA,EAAS7f,KAAK2kB,SAAST,EAAa/M,EAAMkC,EAAOhC,MACrDrX,KAAKsb,UAAUlc,IAAIshB,EAAMb,GAG7BqE,yCAOHxb,EAAM4E,EAAGC,EAAGmT,GACjB,IAAIhG,EAAU1a,KAAK0a,QACnB,IAAIkK,EAEJlE,EAAOC,IAAIC,IAAIF,EAAM,OAAQG,SAAS,IAEtC,GAAI7gB,KAAKwb,YAAYiJ,IAAI/D,GAAO,CAC5BkE,EAAa5kB,KAAKwb,YAAY7T,IAAI+Y,OAC/B,CACHkE,EAAatO,OAAOuO,OAAOnc,GAC3B1I,KAAKwb,YAAYpc,IAAIshB,EAAMkE,GAG/BA,EAAWvE,SAASjhB,IAAIkO,EAAGC,GAG3B,OAAOqX,kBAIf,IAAMzT,EAAW,IAAI6I,EC5frB,IAAA8K,EAAAC,EAAAlO,EAAA+J,IAAA,OACA,IAAAoE,EAAAF,EAAAP,OAAA,QACAC,SACA3D,SAAA,IAEA,IAAAoE,EAAA,CACAnL,qBAAA,WACAC,uBAAA,cAGA,IAAAmL,EAAA,CACA9d,KAAA,aAEA+d,MAAA,CACA,MACA,kBACA,SACA,cACA,QACA,kBAGAC,QAAA,GAEAC,MAAA,CACAhM,OADA,SAAAA,IAEA,GAAAlI,EAAA,CACAA,EAAAuD,OACAvD,EAAAuJ,QAAAlJ,SACAL,EAAAuJ,QAAA5a,UAGAyZ,IARA,SAAAA,IASA,GAAApI,EAAA,CACAA,EAAAmU,UAAAtlB,KAAAuZ,KACApI,EAAAuD,SAGA8E,gBAdA,SAAAA,IAeA,GAAArI,EAAA,CACAA,EAAAqI,gBAAAxZ,KAAAwZ,gBACArI,EAAAuD,UAKAxS,KApCA,SAAAA,IAqCA,OACAqjB,aAAA,EACAlL,OAAA,GACAC,QAAA,GACAG,MAAA,KAIA+K,QA7CA,SAAAA,IA8CArU,EAAAuJ,QAAAlJ,SACAL,EAAAuJ,QAAA5a,SAGA2lB,QAlDA,SAAAA,IAmDAzlB,KAAAujB,OAAAxC,EAAA,KAAAqC,YAAAC,IAAAqC,mBAAA,CACArO,KAAA,UACAsO,SAAA3lB,OACA4lB,aAEAzU,EAAA0U,WAAA7lB,MACAmR,EAAAmU,UAAAtlB,KAAAuZ,MAGAuM,QA5DA,SAAAA,OCzBqO,IAAAC,EAAA,kCCQrO,IAAIC,EAAYze,OAAA0e,EAAA,KAAA1e,CACdwe,EACApM,EACAC,EACF,MACA,KACA,WACA,MAIe,IAAAsM,EAAAF,0BCOf,IAAAG,EAAA,CACAC,IAAA,IACAC,IAAA,IACAC,IAAA,IACAC,IAAA,IACAC,IAAA,IACAC,IAAA,IACAC,IAAA,IACAC,IAAA,IACAC,KAAA,IACAC,KAAA,IACAC,KAAA,KASA,IAAAC,EAAA,CACA3f,KAAA,YACA+d,MAAA,CACA,iBACA,MACA,kBACA,OACA,YACA,WACA,OACA,sBACA,WAGAjjB,KAdA,SAAAA,IAeA,OACA8kB,cAAA,SACAC,YAAA,GAEA/N,cAAA,CACA,CAAAhB,MAAA,MAAAhJ,OAAA,GACA,CAAAgJ,MAAA,QAAAhJ,OAAA,IAGAkK,WAAA,EAAAlB,MAAA,MAAAhJ,OAAA,IACApO,QAAA,GACAomB,MAAA,GACAC,SAAA,GACAC,WAAA,GACAC,OAAA,GAEA5O,OAAA,GACAa,gBAAA,KAIA8N,WAAA,CACAE,cAAApB,GAGAb,MAAA,CACAjM,WADA,SAAAA,IAEApZ,KAAAunB,SAEAP,cAJA,SAAAA,IAKAhnB,KAAAunB,SAEA9O,OAPA,SAAAA,IAQAzY,KAAAunB,SAEA7L,SAVA,SAAAA,IAWA1b,KAAAunB,UAIAnC,QAAA,CACAoC,QADA,SAAAA,MAGAC,oBAHA,SAAAA,IAIAznB,KAAAmW,QAIAuR,iBARA,SAAAA,IAUA1nB,KAAA2nB,MAAA,sBAAA3nB,KAAAgnB,eACA,IAAAhnB,KAAAie,OAAAC,OAAAU,qBAAA5e,KAAAgnB,cAAA,CACAhnB,KAAA8e,QAAA1R,KAAA,eAAA4N,OAAAhb,KAAAgnB,kBAIAO,MAhBA,eAAAK,EAAAC,IAAAC,EAAAjR,EAAAkR,KAAA,SAAAC,IAAA,IAAA1mB,EAAAtB,KAAA,IAAAc,EAAAmnB,EAAAC,EAAA,OAAAJ,EAAAjR,EAAAsR,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAiBAznB,EAAA,GACAd,KAAAc,QAAA,GAEAA,EAAA,CACA,CAAA0nB,KAAAxoB,KAAAqnB,OAAAhQ,KAAA,QACA,CAAAmR,KAAAxoB,KAAAmnB,SAAA9P,KAAA,WACA,CAAAmR,KAAAxoB,KAAAonB,WAAA/P,KAAA,aACA,CAAAmR,KAAAxoB,KAAAyoB,OAAApR,KAAA,mBACA,CAAAmR,KAAAxoB,KAAA0oB,mBAAArR,KAAA,sBACA,CAAAmR,KAAAxoB,KAAAonB,WAAA/P,KAAA,mBAGA4Q,EAAA,GACAjoB,KAAA2oB,eAAA9pB,MAAA,KAAA6e,IAAA,SAAArG,GACAvW,EAAA4c,IAAA,SAAArE,GACAhC,GAAAgC,EAAAhC,KAAA4Q,EAAA7a,KAAAiM,GAAA,UAGAvY,EAAAmnB,EAEAC,EAAA,SAAAA,EAAA/Q,EAAAE,GACA,IAAAuR,GAAA,EACA,GAAAvR,GAAA,WACA,GAAA/V,EAAA0lB,gBAAA,EACA1lB,EAAA8X,WAAAsE,IAAA,SAAA/B,GACAla,QAAAC,IAAA,UAAAia,EAAAzM,MAAAiI,EAAA0R,OACA,GAAAlN,EAAAzM,QAAA,EAAA0Z,EAAA,EACA,GAAAjN,EAAAzM,QAAA,GAAAiI,EAAA0R,OAAA,KAAAD,EAAA,EACA,GAAAjN,EAAAzM,OAAAiI,EAAA0R,MAAAD,EAAA,QAEA,CACAA,EAAA,EAGA,IAAAxhB,EACA+P,EAAA/P,KAAA0hB,cAAApT,QAAApU,EAAAmX,OAAAqQ,gBACA,EAEA,IAAAC,EAAAC,OAAA7R,EAAAwE,IAAAjG,QAAAsT,OAAA1nB,EAAAmX,UAAA,EACA,OAAArR,GAAA,GAAAwhB,GAAA,GAAAG,GAGAjoB,EAAAE,EAAA0c,IAAA5c,EAAA,SAAAuY,GAAA,OACAmP,KAAAxnB,EAAA8iB,OAAAzK,EAAAmP,KAAA,SAAArR,GAAA,OACA+Q,EAAA/Q,EAAAkC,EAAAhC,QAEAA,KAAAgC,EAAAhC,QAGAvW,IAAA4c,IAAA,SAAArE,GACA,IAAAqJ,EAAArJ,EAAAmP,KAAA9K,IAAA,SAAAvG,GAAA,OAAAA,OAAAE,KAAAgC,EAAAhC,QACA/V,EAAAR,QAAAQ,EAAAR,QAAAka,OAAA0H,KAGA1iB,KAAAsZ,gBAAAtZ,KAAAc,QACAW,QAAAC,IAAA,cAAA1B,KAAAc,SACAd,KAAA2nB,MACA,cACA3nB,KAAAyY,OAAAvY,OAAA,EAAAF,KAAAc,QAAA4c,IAAA,SAAAjf,GAAA,OAAAA,EAAA0Y,KAAAwE,KAAA,MA3EA,yBAAA0M,EAAAtL,UAAAiL,EAAAhoB,SAAA,SAAAunB,IAAA,OAAAK,EAAA1mB,MAAAlB,KAAAM,WAAA,OAAAinB,EAAA,GA+EA0B,YA/EA,eAAAC,EAAArB,IAAAC,EAAAjR,EAAAkR,KAAA,SAAAoB,IAAA,IAAAxnB,EAAA3B,KAAA,IAAAopB,EAAArY,EAAAsY,EAAAZ,EAAA1G,EAAAuH,EAAAC,EAAA,OAAAzB,EAAAjR,EAAAsR,KAAA,SAAAqB,EAAAC,GAAA,gBAAAA,EAAAnB,KAAAmB,EAAAlB,MAAA,OAAAkB,EAAAlB,KAAA,SAqFAxH,EAAA,KAAAW,IAAA5J,QACA4R,WAAA1pB,KAAAgnB,eACAjF,aAAA4H,eAAAtC,OAAAuC,QAvFA,OAAAR,EAAAK,EAAAI,KAiFA9Y,EAjFAqY,EAiFArY,KACAsY,EAlFAD,EAkFAC,UACAZ,EAnFAW,EAmFA,mBACArH,EApFAqH,EAoFA,iBAKAppB,KAAAyoB,SACAzoB,KAAAqnB,OAAAtW,EACA/Q,KAAAonB,WAAArF,EACA/hB,KAAAunB,QA5FAkC,EAAAlB,KAAA,UAgGAxH,EAAA,KAAAW,IAAA5J,QACA4R,WAAA1pB,KAAAgnB,eACAI,WAAA0C,OAAAF,QAlGA,QAAAN,EAAAG,EAAAI,KA+FAN,EA/FAD,EA+FAD,UAKArpB,KAAA0oB,mBAAAa,EAEAvpB,KAAA0oB,mBAAA1oB,KAAA0oB,mBAAAhL,IAAA,SAAAqM,GACA,OAAAnK,IAAA,GACAmK,EADA,CAEA3iB,KAAA,GAAA4T,OAAAmL,EAAA4D,EAAAxd,SAAAwd,EAAAxd,OAAA,MAAAyO,OACA+O,EAAA3iB,UAKA3F,QAAAC,IAAA,MAAA6nB,GAEAxI,EAAA,KAAAW,IAAAyF,SAAA6C,UAAA,SAAA9I,GACAvf,EAAAwlB,SAAAjG,EACAvf,EAAA4lB,UAnHA,yBAAAkC,EAAA1M,UAAAoM,EAAAnpB,SAAA,SAAAipB,IAAA,OAAAC,EAAAhoB,MAAAlB,KAAAM,WAAA,OAAA2oB,EAAA,GAsHAgB,SAtHA,eAAAC,EAAArC,IAAAC,EAAAjR,EAAAkR,KAAA,SAAAoC,IAAA,IAAAC,EAAAV,EAAAd,EAAA,OAAAd,EAAAjR,EAAAsR,KAAA,SAAAkC,EAAAC,GAAA,gBAAAA,EAAAhC,KAAAgC,EAAA/B,MAAA,OAAA+B,EAAA/B,KAAA,SA0HAxH,EAAA,KAAAW,IAAA5J,QAAA4R,aAAAE,QA1HA,OAAAQ,EAAAE,EAAAT,KAwHAH,EAxHAU,EAwHAV,WACAd,EAzHAwB,EAyHAxB,KAIA5oB,KAAAinB,YAAAyC,EAGAd,IAAAlL,IAAA,SAAAkL,GAAA,OAAA1Q,MAAA0Q,EAAA2B,SAAArb,MAAA0Z,EAAAjN,MACAiN,EAAA4B,QACA,CAAAtS,MAAA,MAAAhJ,OAAA,GACA,CAAAgJ,MAAA,QAAAhJ,OAAA,IAEAlP,KAAAkZ,cAAA0P,EAEA5oB,KAAAipB,cAvIA,yBAAAqB,EAAAvN,UAAAoN,EAAAnqB,SAAA,SAAAiqB,IAAA,OAAAC,EAAAhpB,MAAAlB,KAAAM,WAAA,OAAA2pB,EAAA,GA0IA9T,KA1IA,eAAAsU,EAAA5C,IAAAC,EAAAjR,EAAAkR,KAAA,SAAA2C,IAAA,IAAA9oB,EAAA5B,KAAA,IAAA2qB,EAAAjB,EAAA,OAAA5B,EAAAjR,EAAAsR,KAAA,SAAAyC,EAAAC,GAAA,gBAAAA,EAAAvC,KAAAuC,EAAAtC,MAAA,OAAAsC,EAAAtC,KAAA,SA2IAxH,EAAA,KAAAW,IAAA5J,QAAA4R,aAAAE,QA3IA,OAAAe,EAAAE,EAAAhB,KA2IAH,EA3IAiB,EA2IAjB,WACA1pB,KAAAinB,YAAAyC,EACA1pB,KAAAgnB,cACAhnB,KAAAie,OAAAC,OAAAU,oBAAA8K,EAAA,GAAA/N,GA9IAkP,EAAAC,GA+IA9qB,KAAAie,OAAA8M,QAAA,GAAA3jB,KA/IAyjB,EAAAtC,KAAAsC,EAAAC,KAgJA,aAhJA,EAAAD,EAAAC,KAwJA,SAxJA,mBAiJA/J,EAAA,KAAAW,IACA2H,UAAArpB,KAAAie,OAAAC,OAAAvC,IACAqO,UAAA,SAAAgB,GAAA,IAAAtB,EAAAsB,EAAAtB,WACA9nB,EAAAolB,cAAA0C,EACA9nB,EAAAqoB,aArJA,OAAAY,EAAAI,OAAA,oBAyJAlK,EAAA,KAAAW,IACA3Q,KAAA/Q,KAAAie,OAAAC,OAAAvC,IACAqO,UAAA,SAAAkB,GAAA,IAAAxB,EAAAwB,EAAAxB,WACA9nB,EAAAolB,cAAA0C,EACA9nB,EAAAqoB,aA7JA,OAAAY,EAAAI,OAAA,oBAiKAjrB,KAAAiqB,WAjKA,yBAAAY,EAAA9N,UAAA2N,EAAA1qB,SAAA,SAAAmW,IAAA,OAAAsU,EAAAvpB,MAAAlB,KAAAM,WAAA,OAAA6V,EAAA,IAsKAqP,QA7NA,SAAAA,IA8NAxlB,KAAAwnB,WAIA/B,QAlOA,SAAAA,IAmOAzlB,KAAAunB,QACAvnB,KAAAmW,SClR+N,IAAAgV,EAAA,mBCQ/N,IAAIC,GAAY7jB,OAAA0e,EAAA,KAAA1e,CACd4jB,EACA3Z,EACAkI,EACF,MACA,KACA,KACA,MAIe,IAAA2R,GAAA9U,EAAA,KAAA6U,iCCnBfrsB,EAAAC,QAAiBhB,EAAQ,2CCCzB,GAAIA,EAAQ,QAAgB,CAC5B,IAAAstB,EAAgBttB,EAAQ,QACxB,IAAAD,EAAeC,EAAQ,QACvB,IAAAutB,EAAcvtB,EAAQ,QACtB,IAAAiB,EAAgBjB,EAAQ,QACxB,IAAAwtB,EAAextB,EAAQ,QACvB,IAAAytB,EAAgBztB,EAAQ,QACxB,IAAAoZ,EAAYpZ,EAAQ,QACpB,IAAA0tB,EAAmB1tB,EAAQ,QAC3B,IAAA2tB,EAAqB3tB,EAAQ,QAC7B,IAAAC,EAAaD,EAAQ,QACrB,IAAA4tB,EAAoB5tB,EAAQ,QAC5B,IAAAmF,EAAkBnF,EAAQ,QAC1B,IAAA2B,EAAiB3B,EAAQ,QACzB,IAAA6tB,EAAgB7tB,EAAQ,QACxB,IAAA0B,EAAwB1B,EAAQ,QAChC,IAAA8tB,EAAoB9tB,EAAQ,QAC5B,IAAAymB,EAAYzmB,EAAQ,QACpB,IAAA+tB,EAAgB/tB,EAAQ,QACxB,IAAAguB,EAAiBhuB,EAAQ,QACzB,IAAAyB,EAAiBzB,EAAQ,QACzB,IAAAiuB,EAAoBjuB,EAAQ,QAC5B,IAAA6mB,EAAe7mB,EAAQ,QACvB,IAAAkuB,EAAuBluB,EAAQ,QAC/B,IAAAmuB,EAAanuB,EAAQ,QAAgBouB,EACrC,IAAAC,EAAkBruB,EAAQ,QAC1B,IAAAE,EAAYF,EAAQ,QACpB,IAAAsuB,EAAYtuB,EAAQ,QACpB,IAAAuuB,EAA0BvuB,EAAQ,QAClC,IAAAwuB,EAA4BxuB,EAAQ,QACpC,IAAAyuB,EAA2BzuB,EAAQ,QACnC,IAAA0uB,EAAuB1uB,EAAQ,QAC/B,IAAA2uB,EAAkB3uB,EAAQ,QAC1B,IAAA4uB,EAAoB5uB,EAAQ,QAC5B,IAAA6uB,EAAmB7uB,EAAQ,QAC3B,IAAA8uB,EAAkB9uB,EAAQ,QAC1B,IAAA+uB,EAAwB/uB,EAAQ,QAChC,IAAAgvB,EAAYhvB,EAAQ,QACpB,IAAAivB,EAAcjvB,EAAQ,QACtB,IAAAkvB,EAAAF,EAAAZ,EACA,IAAAe,EAAAF,EAAAb,EACA,IAAA9oB,EAAAvF,EAAAuF,WACA,IAAA8pB,EAAArvB,EAAAqvB,UACA,IAAAC,EAAAtvB,EAAAsvB,WACA,IAAAC,EAAA,cACA,IAAAC,EAAA,SAAAD,EACA,IAAAE,EAAA,oBACA,IAAAC,EAAA,YACA,IAAAC,EAAAC,MAAAF,GACA,IAAAG,EAAAnC,EAAAntB,YACA,IAAAuvB,EAAApC,EAAAltB,SACA,IAAAuvB,EAAAvB,EAAA,GACA,IAAAwB,GAAAxB,EAAA,GACA,IAAAyB,GAAAzB,EAAA,GACA,IAAA0B,GAAA1B,EAAA,GACA,IAAA2B,GAAA3B,EAAA,GACA,IAAA4B,GAAA5B,EAAA,GACA,IAAA6B,GAAA5B,EAAA,MACA,IAAA6B,GAAA7B,EAAA,OACA,IAAA8B,GAAA5B,EAAA6B,OACA,IAAAC,GAAA9B,EAAAnd,KACA,IAAAkf,GAAA/B,EAAAgC,QACA,IAAAC,GAAAjB,EAAAkB,YACA,IAAAC,GAAAnB,EAAAoB,OACA,IAAAC,GAAArB,EAAAsB,YACA,IAAAC,GAAAvB,EAAAwB,KACA,IAAAC,GAAAzB,EAAA0B,KACA,IAAAC,GAAA3B,EAAA4B,MACA,IAAAC,GAAA7B,EAAA7M,SACA,IAAA2O,GAAA9B,EAAA+B,eACA,IAAAC,GAAApD,EAAA,YACA,IAAAqD,GAAArD,EAAA,eACA,IAAAsD,GAAA1xB,EAAA,qBACA,IAAA2xB,GAAA3xB,EAAA,mBACA,IAAA4xB,GAAAtE,EAAAhtB,OACA,IAAAuxB,GAAAvE,EAAArtB,MACA,IAAAC,GAAAotB,EAAAptB,KACA,IAAA4xB,GAAA,gBAEA,IAAAC,GAAA1D,EAAA,WAAAxsB,EAAAG,GACA,OAAAgwB,GAAAzD,EAAA1sB,IAAA8vB,KAAA3vB,KAGA,IAAAiwB,GAAA5E,EAAA,WAEA,WAAA8B,EAAA,IAAAjrB,YAAA,KAAAguB,QAAA,SAGA,IAAAC,KAAAhD,OAAAI,GAAAruB,KAAAmsB,EAAA,WACA,IAAA8B,EAAA,GAAAjuB,IAAA,MAGA,IAAAkxB,GAAA,SAAAltB,EAAAmtB,GACA,IAAAC,EAAArtB,EAAAC,GACA,GAAAotB,EAAA,GAAAA,EAAAD,EAAA,MAAAjtB,EAAA,iBACA,OAAAktB,GAGA,IAAAC,GAAA,SAAArtB,GACA,GAAA4oB,EAAA5oB,IAAA2sB,MAAA3sB,EAAA,OAAAA,EACA,MAAAgqB,EAAAhqB,EAAA,2BAGA,IAAA8sB,GAAA,SAAAQ,EAAAxwB,GACA,KAAA8rB,EAAA0E,IAAAd,MAAAc,GAAA,CACA,MAAAtD,EAAA,wCACK,WAAAsD,EAAAxwB,IAGL,IAAAywB,GAAA,SAAA5wB,EAAAyoB,GACA,OAAAoI,GAAAnE,EAAA1sB,IAAA8vB,KAAArH,IAGA,IAAAoI,GAAA,SAAAF,EAAAlI,GACA,IAAAhJ,EAAA,EACA,IAAAtf,EAAAsoB,EAAAtoB,OACA,IAAAghB,EAAAgP,GAAAQ,EAAAxwB,GACA,MAAAA,EAAAsf,EAAA0B,EAAA1B,GAAAgJ,EAAAhJ,KACA,OAAA0B,GAGA,IAAA2P,GAAA,SAAAztB,EAAA0tB,EAAAC,GACA7D,EAAA9pB,EAAA0tB,EAAA,CAAiBnpB,IAAA,WAAmB,OAAA3H,KAAAgxB,GAAAD,OAGpC,IAAAE,GAAA,SAAA7wB,EAAAiZ,GACA,IAAAtZ,EAAAN,EAAA4Z,GACA,IAAA6X,EAAA5wB,UAAAJ,OACA,IAAAixB,EAAAD,EAAA,EAAA5wB,UAAA,GAAAC,UACA,IAAA6wB,EAAAD,IAAA5wB,UACA,IAAA8wB,EAAAhF,EAAAtsB,GACA,IAAAtB,EAAAyB,EAAAquB,EAAArN,EAAAoQ,EAAAC,EACA,GAAAF,GAAA9wB,YAAA0rB,EAAAoF,GAAA,CACA,IAAAE,EAAAF,EAAArqB,KAAAjH,GAAAwuB,EAAA,GAAA9vB,EAAA,IAAyD6yB,EAAAC,EAAAhJ,QAAAiJ,KAAgC/yB,IAAA,CACzF8vB,EAAAnhB,KAAAkkB,EAAApiB,OACOnP,EAAAwuB,EAEP,GAAA6C,GAAAF,EAAA,EAAAC,EAAA/Z,EAAA+Z,EAAA7wB,UAAA,MACA,IAAA7B,EAAA,EAAAyB,EAAAP,EAAAI,EAAAG,QAAAghB,EAAAgP,GAAAlwB,KAAAE,GAA6EA,EAAAzB,EAAYA,IAAA,CACzFyiB,EAAAziB,GAAA2yB,EAAAD,EAAApxB,EAAAtB,MAAAsB,EAAAtB,GAEA,OAAAyiB,GAGA,IAAAuQ,GAAA,SAAAC,IACA,IAAAlS,EAAA,EACA,IAAAtf,EAAAI,UAAAJ,OACA,IAAAghB,EAAAgP,GAAAlwB,KAAAE,GACA,MAAAA,EAAAsf,EAAA0B,EAAA1B,GAAAlf,UAAAkf,KACA,OAAA0B,GAIA,IAAAyQ,KAAAtE,GAAA9B,EAAA,WAAyDiE,GAAAxoB,KAAA,IAAAqmB,EAAA,MAEzD,IAAAuE,GAAA,SAAAnC,IACA,OAAAD,GAAAtuB,MAAAywB,GAAAtC,GAAAroB,KAAAypB,GAAAzwB,OAAAywB,GAAAzwB,MAAAM,YAGA,IAAAuxB,GAAA,CACAjyB,WAAA,SAAAA,EAAAC,EAAAC,GACA,OAAAitB,EAAA/lB,KAAAypB,GAAAzwB,MAAAH,EAAAC,EAAAQ,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEAuxB,MAAA,SAAAA,EAAAC,GACA,OAAA9D,GAAAwC,GAAAzwB,MAAA+xB,EAAAzxB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEAyxB,KAAA,SAAAA,EAAA9iB,GACA,OAAA4d,EAAA5rB,MAAAuvB,GAAAzwB,MAAAM,YAEAwjB,OAAA,SAAAA,EAAAiO,GACA,OAAApB,GAAA3wB,KAAA+tB,GAAA0C,GAAAzwB,MAAA+xB,EACAzxB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,aAEA0xB,KAAA,SAAAA,EAAAC,GACA,OAAAhE,GAAAuC,GAAAzwB,MAAAkyB,EAAA5xB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEA4xB,UAAA,SAAAA,EAAAD,GACA,OAAA/D,GAAAsC,GAAAzwB,MAAAkyB,EAAA5xB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEAqP,QAAA,SAAAA,EAAAmiB,GACAjE,EAAA2C,GAAAzwB,MAAA+xB,EAAAzxB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEAmV,QAAA,SAAAA,EAAA0c,GACA,OAAA/D,GAAAoC,GAAAzwB,MAAAoyB,EAAA9xB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEA8xB,SAAA,SAAAA,EAAAD,GACA,OAAAhE,GAAAqC,GAAAzwB,MAAAoyB,EAAA9xB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEA2uB,KAAA,SAAAA,EAAAoD,GACA,OAAArD,GAAA/tB,MAAAuvB,GAAAzwB,MAAAM,YAEAsuB,YAAA,SAAAA,EAAAwD,GACA,OAAAzD,GAAAztB,MAAAuvB,GAAAzwB,MAAAM,YAEAod,IAAA,SAAAA,EAAAyT,GACA,OAAAlB,GAAAQ,GAAAzwB,MAAAmxB,EAAA7wB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEAuuB,OAAA,SAAAA,EAAAiD,GACA,OAAAlD,GAAA3tB,MAAAuvB,GAAAzwB,MAAAM,YAEA0uB,YAAA,SAAAA,EAAA+C,GACA,OAAAhD,GAAA7tB,MAAAuvB,GAAAzwB,MAAAM,YAEAiyB,QAAA,SAAAA,IACA,IAAAC,EAAAxyB,KACA,IAAAE,EAAAuwB,GAAA+B,GAAAtyB,OACA,IAAAuyB,EAAAhyB,KAAA8e,MAAArf,EAAA,GACA,IAAAsf,EAAA,EACA,IAAAtQ,EACA,MAAAsQ,EAAAiT,EAAA,CACAvjB,EAAAsjB,EAAAhT,GACAgT,EAAAhT,KAAAgT,IAAAtyB,GACAsyB,EAAAtyB,GAAAgP,EACO,OAAAsjB,GAEPE,KAAA,SAAAA,EAAAX,GACA,OAAA/D,GAAAyC,GAAAzwB,MAAA+xB,EAAAzxB,UAAAJ,OAAA,EAAAI,UAAA,GAAAC,YAEA6uB,KAAA,SAAAA,EAAAuD,GACA,OAAAxD,GAAAnoB,KAAAypB,GAAAzwB,MAAA2yB,IAEAC,SAAA,SAAAA,EAAAC,EAAAxyB,GACA,IAAAN,EAAA0wB,GAAAzwB,MACA,IAAAE,EAAAH,EAAAG,OACA,IAAA4yB,EAAApzB,EAAAmzB,EAAA3yB,GACA,WAAAusB,EAAA1sB,IAAA8vB,KAAA,CACA9vB,EAAAqwB,OACArwB,EAAAoC,WAAA2wB,EAAA/yB,EAAAytB,kBACA7tB,GAAAU,IAAAE,UAAAL,EAAAR,EAAAW,EAAAH,IAAA4yB,MAKA,IAAAC,GAAA,SAAAzD,EAAAxvB,EAAAO,GACA,OAAAswB,GAAA3wB,KAAAqvB,GAAAroB,KAAAypB,GAAAzwB,MAAAF,EAAAO,KAGA,IAAA2yB,GAAA,SAAA5zB,EAAA6zB,GACAxC,GAAAzwB,MACA,IAAAwwB,EAAAF,GAAAhwB,UAAA,MACA,IAAAJ,EAAAF,KAAAE,OACA,IAAAgzB,EAAAzzB,EAAAwzB,GACA,IAAAhzB,EAAAN,EAAAuzB,EAAAhzB,QACA,IAAAsf,EAAA,EACA,GAAAvf,EAAAuwB,EAAAtwB,EAAA,MAAAoD,EAAA0sB,IACA,MAAAxQ,EAAAvf,EAAAD,KAAAwwB,EAAAhR,GAAA0T,EAAA1T,MAGA,IAAA2T,GAAA,CACAzE,QAAA,SAAAA,IACA,OAAAD,GAAAznB,KAAAypB,GAAAzwB,QAEAuP,KAAA,SAAAA,IACA,OAAAif,GAAAxnB,KAAAypB,GAAAzwB,QAEAuuB,OAAA,SAAAA,IACA,OAAAD,GAAAtnB,KAAAypB,GAAAzwB,SAIA,IAAAozB,GAAA,SAAAvzB,EAAAixB,GACA,OAAA9E,EAAAnsB,IACAA,EAAAkwB,YACAe,GAAA,UACAA,KAAAjxB,GACAmpB,QAAA8H,IAAA9H,OAAA8H,IAEA,IAAAuC,GAAA,SAAAC,EAAAzzB,EAAAixB,GACA,OAAAsC,GAAAvzB,EAAAixB,EAAAhF,EAAAgF,EAAA,OACAnF,EAAA,EAAA9rB,EAAAixB,IACA3D,EAAAttB,EAAAixB,IAEA,IAAAyC,GAAA,SAAA/rB,EAAA3H,EAAAixB,EAAA0C,GACA,GAAAJ,GAAAvzB,EAAAixB,EAAAhF,EAAAgF,EAAA,QACA9E,EAAAwH,IACA/O,EAAA+O,EAAA,WACA/O,EAAA+O,EAAA,SACA/O,EAAA+O,EAAA,SAEAA,EAAA/rB,gBACAgd,EAAA+O,EAAA,aAAAA,EAAAC,aACAhP,EAAA+O,EAAA,eAAAA,EAAA9rB,YACA,CACA7H,EAAAixB,GAAA0C,EAAAtkB,MACA,OAAArP,EACK,OAAAqtB,EAAArtB,EAAAixB,EAAA0C,IAGL,IAAA1D,GAAA,CACA7C,EAAAb,EAAAiH,GACArG,EAAAZ,EAAAmH,GAGAt0B,IAAAC,EAAAD,EAAAy0B,GAAA5D,GAAA,UACAwD,yBAAAD,GACA7rB,eAAA+rB,KAGA,GAAAhI,EAAA,WAAyBgE,GAAAvoB,KAAA,MAA0B,CACnDuoB,GAAAC,GAAA,SAAA3O,IACA,OAAAoO,GAAAjoB,KAAAhH,OAIA,IAAA2zB,GAAA/H,EAAA,GAA4CiG,IAC5CjG,EAAA+H,GAAAR,IACAl1B,EAAA01B,GAAAjE,GAAAyD,GAAA5E,QACA3C,EAAA+H,GAAA,CACArE,MAAAyD,GACA3zB,IAAA4zB,GACAY,YAAA,aACA/S,SAAA0O,GACAE,eAAAmC,KAEAf,GAAA8C,GAAA,cACA9C,GAAA8C,GAAA,kBACA9C,GAAA8C,GAAA,kBACA9C,GAAA8C,GAAA,cACAzG,EAAAyG,GAAAhE,GAAA,CACAhoB,IAAA,WAAsB,OAAA3H,KAAA+vB,OAItBhxB,EAAAC,QAAA,SAAA60B,EAAAtD,EAAAuD,EAAAC,GACAA,MACA,IAAAC,EAAAH,GAAAE,EAAA,sBACA,IAAAE,EAAA,MAAAJ,EACA,IAAAK,EAAA,MAAAL,EACA,IAAAM,EAAAp2B,EAAAi2B,GACA,IAAAI,EAAAD,GAAA,GACA,IAAAE,EAAAF,GAAAjI,EAAAiI,GACA,IAAAG,GAAAH,IAAA3I,EAAAntB,IACA,IAAA0B,EAAA,GACA,IAAAw0B,EAAAJ,KAAA1G,GACA,IAAApmB,EAAA,SAAAmrB,EAAAhT,GACA,IAAAtd,EAAAswB,EAAAxB,GACA,OAAA9uB,EAAAsyB,EAAAP,GAAAzU,EAAA+Q,EAAAruB,EAAAoF,EAAA6oB,KAEA,IAAAsE,EAAA,SAAAjC,EAAAhT,EAAAtQ,GACA,IAAAhN,EAAAswB,EAAAxB,GACA,GAAA+C,EAAA7kB,KAAAzO,KAAAge,MAAAvP,IAAA,IAAAA,EAAA,QAAAA,EAAA,IACAhN,EAAAsyB,EAAAN,GAAA1U,EAAA+Q,EAAAruB,EAAAoF,EAAA4H,EAAAihB,KAEA,IAAAuE,EAAA,SAAAlC,EAAAhT,GACA0N,EAAAsF,EAAAhT,EAAA,CACA7X,IAAA,WACA,OAAAN,EAAArH,KAAAwf,IAEApgB,IAAA,SAAA8P,GACA,OAAAulB,EAAAz0B,KAAAwf,EAAAtQ,IAEAxH,WAAA,QAGA,GAAA4sB,EAAA,CACAH,EAAAL,EAAA,SAAAtB,EAAAtwB,EAAAyyB,EAAAC,GACAlJ,EAAA8G,EAAA2B,EAAAH,EAAA,MACA,IAAAxU,EAAA,EACA,IAAAgR,EAAA,EACA,IAAAJ,EAAAyE,EAAA30B,EAAA40B,EACA,IAAA9I,EAAA9pB,GAAA,CACAhC,EAAA2rB,EAAA3pB,GACA2yB,EAAA30B,EAAAqwB,EACAH,EAAA,IAAAxC,EAAAiH,QACS,GAAA3yB,aAAA0rB,IAAAkH,EAAA/I,EAAA7pB,KAAAorB,GAAAwH,GAAAvH,EAAA,CACT6C,EAAAluB,EACAsuB,EAAAF,GAAAqE,EAAApE,GACA,IAAAwE,EAAA7yB,EAAA2yB,WACA,GAAAD,IAAAr0B,UAAA,CACA,GAAAw0B,EAAAxE,EAAA,MAAAjtB,EAAA0sB,IACA6E,EAAAE,EAAAvE,EACA,GAAAqE,EAAA,QAAAvxB,EAAA0sB,QACW,CACX6E,EAAAl1B,EAAAi1B,GAAArE,EACA,GAAAsE,EAAArE,EAAAuE,EAAA,MAAAzxB,EAAA0sB,IAEA9vB,EAAA20B,EAAAtE,OACS,GAAAR,MAAA7tB,EAAA,CACT,OAAA0uB,GAAAuD,EAAAjyB,OACS,CACT,OAAA+uB,GAAAjqB,KAAAmtB,EAAAjyB,GAEAjE,EAAAu0B,EAAA,MACAwC,EAAA5E,EACA9oB,EAAAkpB,EACA9xB,EAAAm2B,EACAze,EAAAlW,EACAs0B,EAAA,IAAA3G,EAAAuC,KAEA,MAAA5Q,EAAAtf,EAAAw0B,EAAAlC,EAAAhT,OAEA+U,EAAAJ,EAAA1G,GAAA5I,EAAA8O,IACA11B,EAAAs2B,EAAA,cAAAJ,QACK,IAAA5I,EAAA,WACL4I,EAAA,OACK5I,EAAA,WACL,IAAA4I,GAAA,OACKvH,EAAA,SAAAqI,GACL,IAAAd,EACA,IAAAA,EAAA,MACA,IAAAA,EAAA,KACA,IAAAA,EAAAc,IACK,OACLd,EAAAL,EAAA,SAAAtB,EAAAtwB,EAAAyyB,EAAAC,GACAlJ,EAAA8G,EAAA2B,EAAAH,GACA,IAAAc,EAGA,IAAA9I,EAAA9pB,GAAA,WAAAkyB,EAAAvI,EAAA3pB,IACA,GAAAA,aAAA0rB,IAAAkH,EAAA/I,EAAA7pB,KAAAorB,GAAAwH,GAAAvH,EAAA,CACA,OAAAqH,IAAAr0B,UACA,IAAA6zB,EAAAlyB,EAAAouB,GAAAqE,EAAApE,GAAAqE,GACAD,IAAAp0B,UACA,IAAA6zB,EAAAlyB,EAAAouB,GAAAqE,EAAApE,IACA,IAAA6D,EAAAlyB,GAEA,GAAA6tB,MAAA7tB,EAAA,OAAA0uB,GAAAuD,EAAAjyB,GACA,OAAA+uB,GAAAjqB,KAAAmtB,EAAAjyB,KAEA4rB,EAAAuG,IAAAa,SAAAp2B,UAAAqtB,EAAAiI,GAAApZ,OAAAmR,EAAAkI,IAAAlI,EAAAiI,GAAA,SAAAtD,GACA,KAAAA,KAAAqD,GAAAl2B,EAAAk2B,EAAArD,EAAAsD,EAAAtD,MAEAqD,EAAA1G,GAAA8G,EACA,IAAAjJ,EAAAiJ,EAAAX,YAAAO,EAEA,IAAAgB,EAAAZ,EAAA7E,IACA,IAAA0F,IAAAD,IACAA,EAAA/tB,MAAA,UAAA+tB,EAAA/tB,MAAA7G,WACA,IAAA80B,EAAAlC,GAAA5E,OACAtwB,EAAAk2B,EAAAvE,GAAA,MACA3xB,EAAAs2B,EAAAxE,GAAAiE,GACA/1B,EAAAs2B,EAAAn2B,GAAA,MACAH,EAAAs2B,EAAA1E,GAAAsE,GAEA,GAAAJ,EAAA,IAAAI,EAAA,GAAAxE,KAAAqE,IAAArE,MAAA4E,GAAA,CACArH,EAAAqH,EAAA5E,GAAA,CACAhoB,IAAA,WAA0B,OAAAqsB,KAI1Bj0B,EAAAi0B,GAAAG,EAEAl1B,IAAAq2B,EAAAr2B,EAAAs2B,EAAAt2B,EAAAy0B,GAAAS,GAAAC,GAAAr0B,GAEAd,IAAAC,EAAA80B,EAAA,CACAxG,kBAAA+C,IAGAtxB,IAAAC,EAAAD,EAAAy0B,EAAAnI,EAAA,WAAuD6I,EAAA1C,GAAA1qB,KAAAmtB,EAAA,KAA+BH,EAAA,CACtF5zB,KAAA6wB,GACAS,GAAAD,KAGA,KAAAjE,KAAA+G,GAAAt2B,EAAAs2B,EAAA/G,EAAA+C,GAEAtxB,IAAAu2B,EAAAxB,EAAAnC,IAEAhF,EAAAmH,GAEA/0B,IAAAu2B,EAAAv2B,EAAAy0B,EAAArD,GAAA2D,EAAA,CAAuD50B,IAAA4zB,KAEvD/zB,IAAAu2B,EAAAv2B,EAAAy0B,GAAA0B,EAAApB,EAAAb,IAEA,IAAA7H,GAAAiJ,EAAA1T,UAAA0O,GAAAgF,EAAA1T,SAAA0O,GAEAtwB,IAAAu2B,EAAAv2B,EAAAy0B,EAAAnI,EAAA,WACA,IAAA4I,EAAA,GAAA7E,UACK0E,EAAA,CAAU1E,MAAAyD,KAEf9zB,IAAAu2B,EAAAv2B,EAAAy0B,GAAAnI,EAAA,WACA,YAAAkE,kBAAA,IAAA0E,EAAA,OAAA1E,qBACKlE,EAAA,WACLgJ,EAAA9E,eAAAzoB,KAAA,UACKgtB,EAAA,CAAWvE,eAAAmC,KAEhBjF,EAAAqH,GAAAoB,EAAAD,EAAAE,EACA,IAAA/J,IAAA8J,EAAAn3B,EAAAs2B,EAAA7E,GAAA2F,SAECt2B,EAAAC,QAAA,gDCjdD,IAAAsG,EAA2BtH,EAAQ,QAEnC,IAAA2E,EAAuB3E,EAAQ,QAI/B,IAAAy3B,EAAA,GACA,IAAAC,EAAA,GACA,IAAAC,EAAA,IAsGA,SAAA5X,EAAAF,GACA,IAAA+X,EAAA,EAAAC,EAAA,EACAC,EAAA,EAAAC,EAAA,EAGA,cAAAlY,EAAA,CAA+BgY,EAAAhY,EAAAmY,OAC/B,kBAAAnY,EAAA,CAA+BgY,GAAAhY,EAAAoY,WAAA,IAC/B,mBAAApY,EAAA,CAA+BgY,GAAAhY,EAAAqY,YAAA,IAC/B,mBAAArY,EAAA,CAA+B+X,GAAA/X,EAAAsY,YAAA,IAG/B,YAAAtY,KAAAuY,OAAAvY,EAAAwY,gBAAA,CACAT,EAAAC,EACAA,EAAA,EAGAC,EAAAF,EAAAH,EACAM,EAAAF,EAAAJ,EAEA,cAAA5X,EAAA,CAA0BkY,EAAAlY,EAAAyY,OAC1B,cAAAzY,EAAA,CAA0BiY,EAAAjY,EAAA0Y,OAE1B,IAAAT,GAAAC,IAAAlY,EAAA2Y,UAAA,CACA,GAAA3Y,EAAA2Y,WAAA,GACAV,GAAAJ,EACAK,GAAAL,MACK,CACLI,GAAAH,EACAI,GAAAJ,GAKA,GAAAG,IAAAF,EAAA,CAAkBA,EAAAE,EAAA,OAClB,GAAAC,IAAAF,EAAA,CAAkBA,EAAAE,EAAA,OAElB,OAAUU,MAAAb,EACVc,MAAAb,EACAc,OAAAb,EACA9X,OAAA+X,GASAhY,EAAA6Y,aAAA,WACA,OAAAtxB,EAAAI,UACA,iBACA/C,EAAA,SACA,QACA,cAGA5D,EAAAC,QAAA+e,sBCpLAhf,EAAAC,QAAA,SAAA63B,GACA,IAAAA,EAAAC,gBAAA,CACA,IAAA/3B,EAAAwI,OAAAsd,OAAAgS,GAEA,IAAA93B,EAAAg4B,SAAAh4B,EAAAg4B,SAAA,GACAxvB,OAAAC,eAAAzI,EAAA,UACA2I,WAAA,KACAC,IAAA,WACA,OAAA5I,EAAAL,KAGA6I,OAAAC,eAAAzI,EAAA,MACA2I,WAAA,KACAC,IAAA,WACA,OAAA5I,EAAAN,KAGA8I,OAAAC,eAAAzI,EAAA,WACA2I,WAAA,OAEA3I,EAAA+3B,gBAAA,EAEA,OAAA/3B,sCCrBA,IAAAhB,EAAaC,EAAQ,QACrB,IAAAg5B,EAAkBh5B,EAAQ,QAC1B,IAAAstB,EAActtB,EAAQ,QACtB,IAAAwtB,EAAaxtB,EAAQ,QACrB,IAAAC,EAAWD,EAAQ,QACnB,IAAA4tB,EAAkB5tB,EAAQ,QAC1B,IAAAutB,EAAYvtB,EAAQ,QACpB,IAAA0tB,EAAiB1tB,EAAQ,QACzB,IAAAmF,EAAgBnF,EAAQ,QACxB,IAAA2B,EAAe3B,EAAQ,QACvB,IAAA6tB,EAAc7tB,EAAQ,QACtB,IAAAmuB,EAAWnuB,EAAQ,QAAgBouB,EACnC,IAAAc,EAASlvB,EAAQ,QAAcouB,EAC/B,IAAAU,EAAgB9uB,EAAQ,QACxB,IAAAi5B,EAAqBj5B,EAAQ,QAC7B,IAAAsvB,EAAA,cACA,IAAA4J,EAAA,WACA,IAAAzJ,EAAA,YACA,IAAAuC,EAAA,gBACA,IAAAmH,EAAA,eACA,IAAAvJ,EAAA7vB,EAAAuvB,GACA,IAAAO,EAAA9vB,EAAAm5B,GACA,IAAAz2B,EAAA1C,EAAA0C,KACA,IAAA6C,EAAAvF,EAAAuF,WAEA,IAAA8zB,EAAAr5B,EAAAq5B,SACA,IAAAC,EAAAzJ,EACA,IAAA0J,EAAA72B,EAAA62B,IACA,IAAA1U,EAAAniB,EAAAmiB,IACA,IAAArD,EAAA9e,EAAA8e,MACA,IAAA7d,EAAAjB,EAAAiB,IACA,IAAA61B,EAAA92B,EAAA82B,IACA,IAAAC,EAAA,SACA,IAAAC,EAAA,aACA,IAAAC,EAAA,aACA,IAAAC,EAAAX,EAAA,KAAAQ,EACA,IAAAI,EAAAZ,EAAA,KAAAS,EACA,IAAAI,EAAAb,EAAA,KAAAU,EAGA,SAAAI,EAAA5oB,EAAA6oB,EAAAC,GACA,IAAA5H,EAAA,IAAAzC,MAAAqK,GACA,IAAAC,EAAAD,EAAA,EAAAD,EAAA,EACA,IAAAG,GAAA,GAAAD,GAAA,EACA,IAAAE,EAAAD,GAAA,EACA,IAAAE,EAAAL,IAAA,GAAAnV,EAAA,OAAAA,EAAA,SACA,IAAAnkB,EAAA,EACA,IAAA0J,EAAA+G,EAAA,GAAAA,IAAA,KAAAA,EAAA,MACA,IAAAkH,EAAAnP,EAAAC,EACAgI,EAAAooB,EAAApoB,GAEA,GAAAA,UAAAkoB,EAAA,CAEAnwB,EAAAiI,KAAA,IACAkH,EAAA8hB,MACG,CACH9hB,EAAAmJ,EAAA7d,EAAAwN,GAAAqoB,GACA,GAAAroB,GAAAhI,EAAA0b,EAAA,GAAAxM,IAAA,GACAA,IACAlP,GAAA,EAEA,GAAAkP,EAAA+hB,GAAA,GACAjpB,GAAAkpB,EAAAlxB,MACK,CACLgI,GAAAkpB,EAAAxV,EAAA,IAAAuV,GAEA,GAAAjpB,EAAAhI,GAAA,GACAkP,IACAlP,GAAA,EAEA,GAAAkP,EAAA+hB,GAAAD,EAAA,CACAjxB,EAAA,EACAmP,EAAA8hB,OACK,GAAA9hB,EAAA+hB,GAAA,GACLlxB,GAAAiI,EAAAhI,EAAA,GAAA0b,EAAA,EAAAmV,GACA3hB,IAAA+hB,MACK,CACLlxB,EAAAiI,EAAA0T,EAAA,EAAAuV,EAAA,GAAAvV,EAAA,EAAAmV,GACA3hB,EAAA,GAGA,KAAQ2hB,GAAA,EAAW3H,EAAA3xB,KAAAwI,EAAA,IAAAA,GAAA,IAAA8wB,GAAA,GACnB3hB,KAAA2hB,EAAA9wB,EACAgxB,GAAAF,EACA,KAAQE,EAAA,EAAU7H,EAAA3xB,KAAA2X,EAAA,IAAAA,GAAA,IAAA6hB,GAAA,GAClB7H,IAAA3xB,IAAA0J,EAAA,IACA,OAAAioB,EAEA,SAAAiI,EAAAjI,EAAA2H,EAAAC,GACA,IAAAC,EAAAD,EAAA,EAAAD,EAAA,EACA,IAAAG,GAAA,GAAAD,GAAA,EACA,IAAAE,EAAAD,GAAA,EACA,IAAAI,EAAAL,EAAA,EACA,IAAAx5B,EAAAu5B,EAAA,EACA,IAAA7vB,EAAAioB,EAAA3xB,KACA,IAAA2X,EAAAjO,EAAA,IACA,IAAAlB,EACAkB,IAAA,EACA,KAAQmwB,EAAA,EAAWliB,IAAA,IAAAga,EAAA3xB,OAAA65B,GAAA,GACnBrxB,EAAAmP,GAAA,IAAAkiB,GAAA,EACAliB,KAAAkiB,EACAA,GAAAP,EACA,KAAQO,EAAA,EAAWrxB,IAAA,IAAAmpB,EAAA3xB,OAAA65B,GAAA,GACnB,GAAAliB,IAAA,GACAA,EAAA,EAAA+hB,OACG,GAAA/hB,IAAA8hB,EAAA,CACH,OAAAjxB,EAAAhC,IAAAkD,GAAAivB,QACG,CACHnwB,IAAA2b,EAAA,EAAAmV,GACA3hB,IAAA+hB,EACG,OAAAhwB,GAAA,KAAAlB,EAAA2b,EAAA,EAAAxM,EAAA2hB,GAGH,SAAAQ,EAAAC,GACA,OAAAA,EAAA,OAAAA,EAAA,OAAAA,EAAA,MAAAA,EAAA,GAEA,SAAAC,EAAAr1B,GACA,OAAAA,EAAA,KAEA,SAAAs1B,EAAAt1B,GACA,OAAAA,EAAA,IAAAA,GAAA,OAEA,SAAAu1B,EAAAv1B,GACA,OAAAA,EAAA,IAAAA,GAAA,MAAAA,GAAA,OAAAA,GAAA,QAEA,SAAAw1B,EAAAx1B,GACA,OAAA00B,EAAA10B,EAAA,MAEA,SAAAy1B,EAAAz1B,GACA,OAAA00B,EAAA10B,EAAA,MAGA,SAAAytB,EAAAH,EAAAI,EAAAC,GACA7D,EAAAwD,EAAAjD,GAAAqD,EAAA,CAAyBnpB,IAAA,WAAmB,OAAA3H,KAAA+wB,MAG5C,SAAAppB,EAAAmT,EAAA0d,EAAAhZ,EAAAsZ,GACA,IAAAC,GAAAvZ,EACA,IAAAwZ,EAAAnN,EAAAkN,GACA,GAAAC,EAAAR,EAAA1d,EAAA8c,GAAA,MAAAt0B,EAAA6zB,GACA,IAAA8B,EAAAne,EAAA6c,GAAAuB,GACA,IAAAp5B,EAAAk5B,EAAAle,EAAA+c,GACA,IAAAsB,EAAAF,EAAA3J,MAAAxvB,IAAA04B,GACA,OAAAM,EAAAK,IAAA5G,UAEA,SAAAnzB,EAAA0b,EAAA0d,EAAAhZ,EAAA4Z,EAAAlqB,EAAA4pB,GACA,IAAAC,GAAAvZ,EACA,IAAAwZ,EAAAnN,EAAAkN,GACA,GAAAC,EAAAR,EAAA1d,EAAA8c,GAAA,MAAAt0B,EAAA6zB,GACA,IAAA8B,EAAAne,EAAA6c,GAAAuB,GACA,IAAAp5B,EAAAk5B,EAAAle,EAAA+c,GACA,IAAAsB,EAAAC,GAAAlqB,GACA,QAAAzQ,EAAA,EAAiBA,EAAA+5B,EAAW/5B,IAAAw6B,EAAAn5B,EAAArB,GAAA06B,EAAAL,EAAAr6B,EAAA+5B,EAAA/5B,EAAA,GAG5B,IAAA+sB,EAAAntB,IAAA,CACAuvB,EAAA,SAAAtvB,EAAA4B,GACAwrB,EAAA1rB,KAAA4tB,EAAAN,GACA,IAAAuH,EAAAhJ,EAAA3rB,GACAF,KAAAk5B,GAAApM,EAAA9lB,KAAA,IAAA2mB,MAAAkH,GAAA,GACA70B,KAAA43B,GAAA/C,GAGAhH,EAAA,SAAAtvB,EAAA6xB,EAAAjuB,EAAA0yB,GACAnJ,EAAA1rB,KAAA6tB,EAAAqJ,GACAxL,EAAA0E,EAAAxC,EAAAsJ,GACA,IAAAmC,EAAAjJ,EAAAwH,GACA,IAAApH,EAAArtB,EAAAhB,GACA,GAAAquB,EAAA,GAAAA,EAAA6I,EAAA,MAAA/1B,EAAA,iBACAuxB,MAAAt0B,UAAA84B,EAAA7I,EAAA7wB,EAAAk1B,GACA,GAAArE,EAAAqE,EAAAwE,EAAA,MAAA/1B,EAAA0sB,GACAhwB,KAAA23B,GAAAvH,EACApwB,KAAA63B,GAAArH,EACAxwB,KAAA43B,GAAA/C,GAGA,GAAAmC,EAAA,CACAnG,EAAAjD,EAAA6J,EAAA,MACA5G,EAAAhD,EAAA2J,EAAA,MACA3G,EAAAhD,EAAA4J,EAAA,MACA5G,EAAAhD,EAAA6J,EAAA,MAGA9L,EAAAiC,EAAAJ,GAAA,CACA6L,QAAA,SAAAA,EAAAn3B,GACA,OAAAwF,EAAA3H,KAAA,EAAAmC,GAAA,YAEAo3B,SAAA,SAAAA,EAAAp3B,GACA,OAAAwF,EAAA3H,KAAA,EAAAmC,GAAA,IAEAq3B,SAAA,SAAAA,EAAAr3B,GACA,IAAAq2B,EAAA7wB,EAAA3H,KAAA,EAAAmC,EAAA7B,UAAA,IACA,OAAAk4B,EAAA,MAAAA,EAAA,aAEAiB,UAAA,SAAAA,EAAAt3B,GACA,IAAAq2B,EAAA7wB,EAAA3H,KAAA,EAAAmC,EAAA7B,UAAA,IACA,OAAAk4B,EAAA,MAAAA,EAAA,IAEAkB,SAAA,SAAAA,EAAAv3B,GACA,OAAAo2B,EAAA5wB,EAAA3H,KAAA,EAAAmC,EAAA7B,UAAA,MAEAq5B,UAAA,SAAAA,EAAAx3B,GACA,OAAAo2B,EAAA5wB,EAAA3H,KAAA,EAAAmC,EAAA7B,UAAA,UAEAs5B,WAAA,SAAAA,EAAAz3B,GACA,OAAAk2B,EAAA1wB,EAAA3H,KAAA,EAAAmC,EAAA7B,UAAA,WAEAu5B,WAAA,SAAAA,EAAA13B,GACA,OAAAk2B,EAAA1wB,EAAA3H,KAAA,EAAAmC,EAAA7B,UAAA,WAEAw5B,QAAA,SAAAA,EAAA33B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAAs2B,EAAAvpB,IAEA6qB,SAAA,SAAAA,EAAA53B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAAs2B,EAAAvpB,IAEA8qB,SAAA,SAAAA,EAAA73B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAAu2B,EAAAxpB,EAAA5O,UAAA,KAEA25B,UAAA,SAAAA,EAAA93B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAAu2B,EAAAxpB,EAAA5O,UAAA,KAEA45B,SAAA,SAAAA,EAAA/3B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAAw2B,EAAAzpB,EAAA5O,UAAA,KAEA65B,UAAA,SAAAA,EAAAh4B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAAw2B,EAAAzpB,EAAA5O,UAAA,KAEA85B,WAAA,SAAAA,EAAAj4B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAA02B,EAAA3pB,EAAA5O,UAAA,KAEA+5B,WAAA,SAAAA,EAAAl4B,EAAA+M,GACA9P,EAAAY,KAAA,EAAAmC,EAAAy2B,EAAA1pB,EAAA5O,UAAA,WAGC,CACD,IAAAirB,EAAA,WACAqC,EAAA,OACGrC,EAAA,WACH,IAAAqC,GAAA,MACGrC,EAAA,WACH,IAAAqC,EACA,IAAAA,EAAA,KACA,IAAAA,EAAA3oB,KACA,OAAA2oB,EAAAxmB,MAAAkmB,IACG,CACHM,EAAA,SAAAtvB,EAAA4B,GACAwrB,EAAA1rB,KAAA4tB,GACA,WAAAyJ,EAAAxL,EAAA3rB,KAEA,IAAAo6B,EAAA1M,EAAAH,GAAA4J,EAAA5J,GACA,QAAAle,EAAA4c,EAAAkL,GAAAjnB,EAAA,EAAA0gB,GAAiDvhB,EAAArP,OAAAkQ,GAAiB,CAClE,MAAA0gB,GAAAvhB,EAAAa,QAAAwd,GAAA3vB,EAAA2vB,EAAAkD,GAAAuG,EAAAvG,KAEA,IAAAxF,EAAAgP,EAAA1G,YAAAhG,EAGA,IAAA9S,GAAA,IAAA+S,EAAA,IAAAD,EAAA,IACA,IAAA2M,GAAA1M,EAAAJ,GAAAqM,QACAhf,GAAAgf,QAAA,cACAhf,GAAAgf,QAAA,cACA,GAAAhf,GAAAwe,QAAA,KAAAxe,GAAAwe,QAAA,GAAA1N,EAAAiC,EAAAJ,GAAA,CACAqM,QAAA,SAAAA,EAAA33B,EAAA+M,GACAqrB,GAAAvzB,KAAAhH,KAAAmC,EAAA+M,GAAA,SAEA6qB,SAAA,SAAAA,EAAA53B,EAAA+M,GACAqrB,GAAAvzB,KAAAhH,KAAAmC,EAAA+M,GAAA,UAEG,MAEH+nB,EAAArJ,EAAAN,GACA2J,EAAApJ,EAAAqJ,GACAj5B,EAAA4vB,EAAAJ,GAAAjC,EAAAptB,KAAA,MACAY,EAAAsuB,GAAAM,EACA5uB,EAAAk4B,GAAArJ", "file": "js/5266d12a.c48a7bb5.js", "sourcesContent": ["var global = require('./_global');\nvar hide = require('./_hide');\nvar uid = require('./_uid');\nvar TYPED = uid('typed_array');\nvar VIEW = uid('view');\nvar ABV = !!(global.ArrayBuffer && global.DataView);\nvar CONSTR = ABV;\nvar i = 0;\nvar l = 9;\nvar Typed;\n\nvar TypedArrayConstructors = (\n  'Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array'\n).split(',');\n\nwhile (i < l) {\n  if (Typed = global[TypedArrayConstructors[i++]]) {\n    hide(Typed.prototype, TYPED, true);\n    hide(Typed.prototype, VIEW, true);\n  } else CONSTR = false;\n}\n\nmodule.exports = {\n  ABV: ABV,\n  CONSTR: CONSTR,\n  TYPED: TYPED,\n  VIEW: VIEW\n};\n", "// 19.1.3.19 Object.setPrototypeOf(O, proto)\nvar $export = require('./_export');\n$export($export.S, 'Object', { setPrototypeOf: require('./_set-proto').set });\n", "import mod from \"-!../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-bar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-bar.vue?vue&type=style&index=0&lang=scss&\"", "// 22.1.3.3 Array.prototype.copyWithin(target, start, end = this.length)\n'use strict';\nvar toObject = require('./_to-object');\nvar toAbsoluteIndex = require('./_to-absolute-index');\nvar toLength = require('./_to-length');\n\nmodule.exports = [].copyWithin || function copyWithin(target /* = 0 */, start /* = 0, end = @length */) {\n  var O = toObject(this);\n  var len = toLength(O.length);\n  var to = toAbsoluteIndex(target, len);\n  var from = toAbsoluteIndex(start, len);\n  var end = arguments.length > 2 ? arguments[2] : undefined;\n  var count = Math.min((end === undefined ? len : toAbsoluteIndex(end, len)) - from, len - to);\n  var inc = 1;\n  if (from < to && to < from + count) {\n    inc = -1;\n    from += count - 1;\n    to += count - 1;\n  }\n  while (count-- > 0) {\n    if (from in O) O[to] = O[from];\n    else delete O[to];\n    to += inc;\n    from += inc;\n  } return O;\n};\n", "/* globals __webpack_amd_options__ */\nmodule.exports = __webpack_amd_options__;\n", "import _ from 'lodash';\n\nclass FilterJob {\n    constructor(sources) {\n        this.sources = _.merge(...sources);\n        this.state = '';\n    }\n\n    filter(operation) {\n        new Promise((resolve) => {\n            console.log(this.sources);\n            resolve(this);\n        });\n        return this;\n    }\n\n    pickFew(operation) {\n        new Promise((resolve) => {\n            console.log(this.sources);\n            resolve(this);\n        });\n        return this;\n    }\n\n    pickOne(operation) {\n        new Promise((resolve) => {\n            console.log(this.sources);\n            resolve(this);\n        });\n        return this;\n    }\n\n    result() {\n        return new Promise((resolve) => {\n\n            console.log(this.sources);\n            resolve(this);\n        });\n    }\n}\n\nclass FilteringManager {\n    construct() {\n\n    }\n\n    add(sources) {\n        return new FilterJob(sources);\n    }\n}\n\nconst Filter = new FilteringManager();\n\nexport { Filter };", "require('./_typed-array')('Float32', 4, function (init) {\n  return function Float32Array(data, byteOffset, length) {\n    return init(this, data, byteOffset, length);\n  };\n});\n", "require('./_typed-array')('Uint16', 2, function (init) {\n  return function Uint16Array(data, byteOffset, length) {\n    return init(this, data, byteOffset, length);\n  };\n});\n", "/**\n * Copyright 2013-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule isEventSupported\n */\n\n'use strict';\n\nvar ExecutionEnvironment = require('./ExecutionEnvironment');\n\nvar useHasFeature;\nif (ExecutionEnvironment.canUseDOM) {\n  useHasFeature =\n    document.implementation &&\n    document.implementation.hasFeature &&\n    // always returns true in newer browsers as per the standard.\n    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n    document.implementation.hasFeature('', '') !== true;\n}\n\n/**\n * Checks if an event is supported in the current execution environment.\n *\n * NOTE: This will not work correctly for non-generic events such as `change`,\n * `reset`, `load`, `error`, and `select`.\n *\n * Borrows from Modernizr.\n *\n * @param {string} eventNameSuffix Event name, e.g. \"click\".\n * @param {?boolean} capture Check if the capture phase is supported.\n * @return {boolean} True if the event is supported.\n * @internal\n * @license Modernizr 3.0.0pre (Custom Build) | MIT\n */\nfunction isEventSupported(eventNameSuffix, capture) {\n  if (!ExecutionEnvironment.canUseDOM ||\n      capture && !('addEventListener' in document)) {\n    return false;\n  }\n\n  var eventName = 'on' + eventNameSuffix;\n  var isSupported = eventName in document;\n\n  if (!isSupported) {\n    var element = document.createElement('div');\n    element.setAttribute(eventName, 'return;');\n    isSupported = typeof element[eventName] === 'function';\n  }\n\n  if (!isSupported && useHasFeature && eventNameSuffix === 'wheel') {\n    // This is the only way to test support for the `wheel` event in IE9+.\n    isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n  }\n\n  return isSupported;\n}\n\nmodule.exports = isEventSupported;\n", "// https://tc39.github.io/ecma262/#sec-toindex\nvar toInteger = require('./_to-integer');\nvar toLength = require('./_to-length');\nmodule.exports = function (it) {\n  if (it === undefined) return 0;\n  var number = toInteger(it);\n  var length = toLength(number);\n  if (number !== length) throw RangeError('Wrong length!');\n  return length;\n};\n", "import mod from \"-!../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-container.vue?vue&type=style&index=0&id=24fbf5f8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-container.vue?vue&type=style&index=0&id=24fbf5f8&lang=scss&scoped=true&\"", "/**\n * Copyright 2004-present Facebook. All Rights Reserved.\n *\n * @providesModule UserAgent_DEPRECATED\n */\n\n/**\n *  Provides entirely client-side User Agent and OS detection. You should prefer\n *  the non-deprecated UserAgent module when possible, which exposes our\n *  authoritative server-side PHP-based detection to the client.\n *\n *  Usage is straightforward:\n *\n *    if (UserAgent_DEPRECATED.ie()) {\n *      //  IE\n *    }\n *\n *  You can also do version checks:\n *\n *    if (UserAgent_DEPRECATED.ie() >= 7) {\n *      //  IE7 or better\n *    }\n *\n *  The browser functions will return NaN if the browser does not match, so\n *  you can also do version compares the other way:\n *\n *    if (UserAgent_DEPRECATED.ie() < 7) {\n *      //  IE6 or worse\n *    }\n *\n *  Note that the version is a float and may include a minor version number,\n *  so you should always use range operators to perform comparisons, not\n *  strict equality.\n *\n *  **Note:** You should **strongly** prefer capability detection to browser\n *  version detection where it's reasonable:\n *\n *    http://www.quirksmode.org/js/support.html\n *\n *  Further, we have a large number of mature wrapper functions and classes\n *  which abstract away many browser irregularities. Check the documentation,\n *  grep for things, or <NAME_EMAIL> before writing yet\n *  another copy of \"event || window.event\".\n *\n */\n\nvar _populated = false;\n\n// Browsers\nvar _ie, _firefox, _opera, _webkit, _chrome;\n\n// Actual IE browser for compatibility mode\nvar _ie_real_version;\n\n// Platforms\nvar _osx, _windows, _linux, _android;\n\n// Architectures\nvar _win64;\n\n// Devices\nvar _iphone, _ipad, _native;\n\nvar _mobile;\n\nfunction _populate() {\n  if (_populated) {\n    return;\n  }\n\n  _populated = true;\n\n  // To work around buggy JS libraries that can't handle multi-digit\n  // version numbers, Opera 10's user agent string claims it's Opera\n  // 9, then later includes a Version/X.Y field:\n  //\n  // Opera/9.80 (foo) Presto/2.2.15 Version/10.10\n  var uas = navigator.userAgent;\n  var agent = /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(uas);\n  var os    = /(Mac OS X)|(Windows)|(Linux)/.exec(uas);\n\n  _iphone = /\\b(iPhone|iP[ao]d)/.exec(uas);\n  _ipad = /\\b(iP[ao]d)/.exec(uas);\n  _android = /Android/i.exec(uas);\n  _native = /FBAN\\/\\w+;/i.exec(uas);\n  _mobile = /Mobile/i.exec(uas);\n\n  // Note that the IE team blog would have you believe you should be checking\n  // for 'Win64; x64'.  But MSDN then reveals that you can actually be coming\n  // from either x64 or ia64;  so ultimately, you should just check for Win64\n  // as in indicator of whether you're in 64-bit IE.  32-bit IE on 64-bit\n  // Windows will send 'WOW64' instead.\n  _win64 = !!(/Win64/.exec(uas));\n\n  if (agent) {\n    _ie = agent[1] ? parseFloat(agent[1]) : (\n          agent[5] ? parseFloat(agent[5]) : NaN);\n    // IE compatibility mode\n    if (_ie && document && document.documentMode) {\n      _ie = document.documentMode;\n    }\n    // grab the \"true\" ie version from the trident token if available\n    var trident = /(?:Trident\\/(\\d+.\\d+))/.exec(uas);\n    _ie_real_version = trident ? parseFloat(trident[1]) + 4 : _ie;\n\n    _firefox = agent[2] ? parseFloat(agent[2]) : NaN;\n    _opera   = agent[3] ? parseFloat(agent[3]) : NaN;\n    _webkit  = agent[4] ? parseFloat(agent[4]) : NaN;\n    if (_webkit) {\n      // We do not add the regexp to the above test, because it will always\n      // match 'safari' only since 'AppleWebKit' appears before 'Chrome' in\n      // the userAgent string.\n      agent = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(uas);\n      _chrome = agent && agent[1] ? parseFloat(agent[1]) : NaN;\n    } else {\n      _chrome = NaN;\n    }\n  } else {\n    _ie = _firefox = _opera = _chrome = _webkit = NaN;\n  }\n\n  if (os) {\n    if (os[1]) {\n      // Detect OS X version.  If no version number matches, set _osx to true.\n      // Version examples:  10, 10_6_1, 10.7\n      // Parses version number as a float, taking only first two sets of\n      // digits.  If only one set of digits is found, returns just the major\n      // version number.\n      var ver = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(uas);\n\n      _osx = ver ? parseFloat(ver[1].replace('_', '.')) : true;\n    } else {\n      _osx = false;\n    }\n    _windows = !!os[2];\n    _linux   = !!os[3];\n  } else {\n    _osx = _windows = _linux = false;\n  }\n}\n\nvar UserAgent_DEPRECATED = {\n\n  /**\n   *  Check if the UA is Internet Explorer.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  ie: function() {\n    return _populate() || _ie;\n  },\n\n  /**\n   * Check if we're in Internet Explorer compatibility mode.\n   *\n   * @return bool true if in compatibility mode, false if\n   * not compatibility mode or not ie\n   */\n  ieCompatibilityMode: function() {\n    return _populate() || (_ie_real_version > _ie);\n  },\n\n\n  /**\n   * Whether the browser is 64-bit IE.  Really, this is kind of weak sauce;  we\n   * only need this because Skype can't handle 64-bit IE yet.  We need to remove\n   * this when we don't need it -- tracked by #601957.\n   */\n  ie64: function() {\n    return UserAgent_DEPRECATED.ie() && _win64;\n  },\n\n  /**\n   *  Check if the UA is Firefox.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  firefox: function() {\n    return _populate() || _firefox;\n  },\n\n\n  /**\n   *  Check if the UA is Opera.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  opera: function() {\n    return _populate() || _opera;\n  },\n\n\n  /**\n   *  Check if the UA is WebKit.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  webkit: function() {\n    return _populate() || _webkit;\n  },\n\n  /**\n   *  For Push\n   *  WILL BE REMOVED VERY SOON. Use UserAgent_DEPRECATED.webkit\n   */\n  safari: function() {\n    return UserAgent_DEPRECATED.webkit();\n  },\n\n  /**\n   *  Check if the UA is a Chrome browser.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  chrome : function() {\n    return _populate() || _chrome;\n  },\n\n\n  /**\n   *  Check if the user is running Windows.\n   *\n   *  @return bool `true' if the user's OS is Windows.\n   */\n  windows: function() {\n    return _populate() || _windows;\n  },\n\n\n  /**\n   *  Check if the user is running Mac OS X.\n   *\n   *  @return float|bool   Returns a float if a version number is detected,\n   *                       otherwise true/false.\n   */\n  osx: function() {\n    return _populate() || _osx;\n  },\n\n  /**\n   * Check if the user is running Linux.\n   *\n   * @return bool `true' if the user's OS is some flavor of Linux.\n   */\n  linux: function() {\n    return _populate() || _linux;\n  },\n\n  /**\n   * Check if the user is running on an iPhone or iPod platform.\n   *\n   * @return bool `true' if the user is running some flavor of the\n   *    iPhone OS.\n   */\n  iphone: function() {\n    return _populate() || _iphone;\n  },\n\n  mobile: function() {\n    return _populate() || (_iphone || _ipad || _android || _mobile);\n  },\n\n  nativeApp: function() {\n    // webviews inside of the native apps\n    return _populate() || _native;\n  },\n\n  android: function() {\n    return _populate() || _android;\n  },\n\n  ipad: function() {\n    return _populate() || _ipad;\n  }\n};\n\nmodule.exports = UserAgent_DEPRECATED;\n", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"MSDFText\"] = factory();\n\telse\n\t\troot[\"MSDFText\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap a5e42b138f430a23f178", "export { MSDFText, MSDFTextOption } from \"./MSDFText\";\r\nexport { MSDFRenderer } from \"./MSDFRenderer\";\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/index.ts", "\nexport interface MSDFTextOption {\n    // Basic\n    fontFace: string;\n    fontSize: number;\n    fillColor?: number;\n    weight?: number;\n    // Effect\n    texture?: PIXI.Texture;\n    strokeColor?: number;\n    strokeThickness?: number;\n    dropShadow?: boolean;\n    dropShadowColor?: number;\n    dropShadowAlpha?: number;\n    dropShadowOffset?: PIXI.Point;\n    dropShadowBlur?: number;\n    // Layout\n    align?: \"left\" | \"right\" | \"center\";\n    baselineOffset?: number;\n    letterSpacing?: number;\n    kerning?: boolean;\n    lineSpacing?: number;\n    maxWidth?: number;\n    // Debug\n    debugLevel?: 1 | 2 | 3;\n    pxrange?: number;\n}\n\nexport class MSDFText extends PIXI.mesh.Mesh {\n\n    private _text: string;\n    // Font data passed to renderer\n    private _font: any;\n    private _textWidth: number;\n    private _textHeight: number;\n    private _maxWidth: number;\n    private _anchor: PIXI.ObservablePoint;\n\n    // TODO: add Effect & Layout\n    private _baselineOffset: number;\n    private _letterSpacing: number;\n    private _lineSpacing: number;\n\n    // Debug\n    private _debugLevel: number;\n    // TODO: Metrics object\n    private _textMetricsBound: PIXI.Rectangle;\n\n    constructor(text: string, options: MSDFTextOption) {\n        super(options.texture || PIXI.Texture.WHITE);\n        this._text = text;\n        this._font = {\n            fontFace: options.fontFace,\n            fontSize: options.fontSize,\n            color: options.fillColor === undefined ? 0xFF0000 : options.fillColor,\n            weight: options.weight === undefined ? 0.5 : 1 - options.weight,\n            align: options.align,\n            kerning: options.kerning === undefined ? true : options.kerning,\n            strokeColor: options.strokeColor || 0,\n            dropShadow: options.dropShadow || false,\n            dropShadowColor: options.dropShadowColor || 0,\n            dropShadowAlpha: options.dropShadowAlpha === undefined ? 0.5 : options.dropShadowAlpha,\n            dropShadowBlur: options.dropShadowBlur || 0,\n            dropShadowOffset: options.dropShadowOffset || new PIXI.Point(2, 2),\n            pxrange: options.pxrange === undefined ? 3 : options.pxrange,\n        };\n        if (options.strokeThickness === undefined || options.strokeThickness === 0) {\n            this._font.strokeWeight = 0;\n        } else {\n            this._font.strokeWeight = this._font.weight - options.strokeThickness;\n        }\n\n        // TODO: layout option initialze\n        this._baselineOffset = options.baselineOffset === undefined ? 0 : options.baselineOffset;\n        this._letterSpacing = options.letterSpacing === undefined ? 0 : options.letterSpacing;\n        this._lineSpacing = options.lineSpacing === undefined ? 0 : options.lineSpacing;\n\n        this._textWidth = this._textHeight = 0;\n        this._maxWidth = options.maxWidth || 0;\n        this._anchor = new PIXI.ObservablePoint(() => { this.dirty++; }, this, 0, 0);\n        this._textMetricsBound = new PIXI.Rectangle();\n\n        // Debug initialize\n        this._debugLevel = options.debugLevel || 0;\n        this.pluginName = `msdf`;\n        this.dirty = 0;\n        this.updateText();\n    }\n\n    public updateText() {\n        // clear all gizmo\n        this.removeChildren();\n\n        const fontData = PIXI.extras.BitmapText.fonts[this._font.fontFace];\n        if (!fontData) throw new Error(\"Invalid fontFace: \" + this._font.fontFace);\n        // No beauty way to get bitmap font texture\n        this._texture = this.getBitmapTexture(this._font.fontFace);\n        this._font.rawSize = fontData.size;\n\n        const scale = this._font.fontSize / fontData.size;\n        const pos = new PIXI.Point(0, -this._baselineOffset * scale);\n        const chars = [];\n        const lineWidths: number[] = [];\n        const texWidth = this._texture.width;\n        const texHeight = this._texture.height;\n\n        let prevCharCode = -1;\n        let lastLineWidth = 0;\n        let maxLineWidth = 0;\n        let line = 0;\n        let lastSpace = -1;\n        let lastSpaceWidth = 0;\n        let spacesRemoved = 0;\n        let maxLineHeight = 0;\n\n        for (let i = 0; i < this._text.length; i++) {\n            const charCode = this._text.charCodeAt(i);\n\n            // If char is space, cache to lastSpace\n            if (/(\\s)/.test(this._text.charAt(i))) {\n                lastSpace = i;\n                lastSpaceWidth = lastLineWidth;\n            }\n\n            // If char is return\n            if (/(?:\\r\\n|\\r|\\n)/.test(this._text.charAt(i))) {\n                lastLineWidth -= this._letterSpacing;\n                lineWidths.push(lastLineWidth);\n                maxLineWidth = Math.max(maxLineWidth, lastLineWidth);\n                line++;\n\n                pos.x = 0;\n                pos.y += fontData.lineHeight * scale + this._lineSpacing * scale;\n                prevCharCode = -1;\n                continue;\n            }\n\n            if (lastSpace !== -1 && this._maxWidth > 0 && pos.x > this._maxWidth) {\n                PIXI.utils.removeItems(chars, lastSpace - spacesRemoved, i - lastSpace);\n                i = lastSpace;\n                lastSpace = -1;\n                ++spacesRemoved;\n\n                lastSpaceWidth -= this._letterSpacing;\n                lineWidths.push(lastSpaceWidth);\n                maxLineWidth = Math.max(maxLineWidth, lastSpaceWidth);\n                line++;\n\n                pos.x = 0;\n                pos.y += fontData.lineHeight * scale + this._lineSpacing * scale;\n                prevCharCode = -1;\n                continue;\n            }\n\n            const charData = fontData.chars[charCode];\n\n            if (!charData) continue;\n\n            if (this._font.kerning && prevCharCode !== -1 && charData.kerning[prevCharCode]) {\n                pos.x += charData.kerning[prevCharCode] * scale;\n            }\n\n            chars.push({\n                line,\n                charCode,\n                drawRect: new PIXI.Rectangle(\n                    pos.x + charData.xOffset * scale,\n                    pos.y + charData.yOffset * scale,\n                    charData.texture.width * scale,\n                    charData.texture.height * scale,\n                ),\n                rawRect: new PIXI.Rectangle(\n                    charData.texture.orig.x,\n                    charData.texture.orig.y,\n                    charData.texture.width,\n                    charData.texture.height,\n                ),\n            });\n            // lastLineWidth = pos.x + (charData.texture.width * scale + charData.xOffset);\n            pos.x += (charData.xAdvance + this._letterSpacing) * scale;\n            lastLineWidth = pos.x;\n            maxLineHeight = Math.max(maxLineHeight, pos.y + fontData.lineHeight * scale);\n            prevCharCode = charCode;\n        }\n\n        lineWidths.push(lastLineWidth);\n        maxLineWidth = Math.max(maxLineWidth, lastLineWidth);\n\n        const lineAlignOffsets = [];\n        for (let i = 0; i <= line; i++) {\n            let alignOffset = 0;\n\n            if (this._font.align === \"right\") {\n                alignOffset = maxLineWidth - lineWidths[i];\n            } else if (this._font.align === \"center\") {\n                alignOffset = (maxLineWidth - lineWidths[i]) / 2;\n            }\n            lineAlignOffsets.push(alignOffset);\n        }\n\n        const tint = this.tint;\n\n        // Update line alignment and fontSize\n        let lineNo = -1;\n        for (const char of chars) {\n            char.drawRect.x = char.drawRect.x + lineAlignOffsets[char.line];\n            if (lineNo !== char.line) {\n                lineNo = char.line;\n                // draw line gizmo\n                if (this._debugLevel > 1) {\n                    this.drawGizmoRect(new PIXI.Rectangle(\n                        char.drawRect.x - fontData.chars[char.charCode].xOffset * scale,\n                        char.drawRect.y - fontData.chars[char.charCode].yOffset * scale,\n                        lineWidths[lineNo],\n                        fontData.lineHeight * scale\n                    ), 1, 0x00FF00, 0.5);\n                }\n            }\n        }\n        // draw text bound gizmo\n        if (this._debugLevel > 0) {\n            this.drawGizmoRect(this.getLocalBounds(), 1, 0xFFFFFF, 0.5);\n        }\n\n        this._textWidth = maxLineWidth;\n        this._textHeight = maxLineHeight;\n        this._textMetricsBound = new PIXI.Rectangle(0, 0, maxLineWidth, maxLineHeight);\n\n        this.vertices = this.toVertices(chars);\n        this.uvs = this.toUVs(chars, texWidth, texHeight);\n        this.indices = this.createIndicesForQuads(chars.length);\n\n        this.dirty++;\n        this.indexDirty++;\n    }\n\n    public get text(): string { return this._text; }\n    public set text(value) { this._text = this.unescape(value); this.updateText(); }\n    public get fontData(): any { return this._font; }\n    public get glDatas(): any { return this._glDatas; }\n    public get textWidth(): number { return this._textWidth; }\n    public get textHeight(): number { return this._textHeight; }\n    public get maxWidth(): number { return this._maxWidth; }\n    public get textMetric(): PIXI.Rectangle { return this._textMetricsBound; }\n\n    private getBitmapTexture(fontFace: string): PIXI.Texture {\n        const fontData = PIXI.extras.BitmapText.fonts[fontFace];\n        if (!fontData) return PIXI.Texture.EMPTY;\n        // No beauty way to get bitmap font texture, hack needed\n        const texturePath: string = fontData.chars[Object.keys(fontData.chars)[0]].texture.baseTexture.imageUrl;\n        return PIXI.utils.TextureCache[texturePath];\n    }\n\n    private toVertices(chars: any[]): Float32Array {\n        const positions = new Float32Array(chars.length * 4 * 2);\n        let i = 0;\n        chars.forEach(char => {\n            // top left position\n            const x = char.drawRect.x;\n            const y = char.drawRect.y;\n            // quad size\n            const w = char.drawRect.width;\n            const h = char.drawRect.height;\n\n            // BL\n            positions[i++] = x;\n            positions[i++] = y;\n            // TL\n            positions[i++] = x;\n            positions[i++] = y + h;\n            // TR\n            positions[i++] = x + w;\n            positions[i++] = y + h;\n            // BR\n            positions[i++] = x + w;\n            positions[i++] = y;\n\n            // draw Gizmo\n            if (this._debugLevel > 2) this.drawGizmoRect(char.drawRect, 1, 0x0000AA, 0.5);\n        });\n        return positions;\n    }\n\n    private toUVs(chars: any[], texWidth: number, texHeight: number): Float32Array {\n        const uvs = new Float32Array(chars.length * 4 * 2);\n        let i = 0;\n        chars.forEach(char => {\n            // Note: v coordinate is reversed 2D space Y coordinate\n            const u0 = char.rawRect.x / texWidth;\n            const u1 = (char.rawRect.x + char.rawRect.width) / texWidth;\n            const v0 = (char.rawRect.y + char.rawRect.height) / texHeight;\n            const v1 = char.rawRect.y / texHeight;\n            // BL\n            uvs[i++] = u0;\n            uvs[i++] = v1;\n            // TL\n            uvs[i++] = u0;\n            uvs[i++] = v0;\n            // TR\n            uvs[i++] = u1;\n            uvs[i++] = v0;\n            // BR\n            uvs[i++] = u1;\n            uvs[i++] = v1;\n        });\n        return uvs;\n    }\n\n    private createIndicesForQuads(size: number): Uint16Array {\n        // the total number of indices in our array, there are 6 points per quad.\n        const totalIndices = size * 6;\n        const indices = new Uint16Array(totalIndices);\n\n        // fill the indices with the quads to draw\n        for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4) {\n            indices[i + 0] = j + 0;\n            indices[i + 1] = j + 1;\n            indices[i + 2] = j + 2;\n            indices[i + 3] = j + 0;\n            indices[i + 4] = j + 2;\n            indices[i + 5] = j + 3;\n        }\n        return indices;\n    }\n\n    private drawGizmoRect(rect: PIXI.Rectangle, lineThickness: number = 1, lineColor: number = 0xFFFFFF, lineAlpha: number = 1): void {\n        const gizmo = new PIXI.Graphics();\n        gizmo.nativeLines = true;\n        gizmo\n        .lineStyle(lineThickness, lineColor, lineAlpha)\n        .drawRect(rect.x, rect.y, rect.width, rect.height);\n        this.addChild(gizmo);\n    }\n\n    private unescape(input: string): string {\n        return input.replace(/(\\\\n|\\\\r)/g, \"\\n\");\n    }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/MSDFText.ts", "import { MSDFText, MSDFTextOption } from \"./MSDFText\";\nimport { glCore } from \"pixi.js\";\n\n// Shader loader\nconst vertShader = require(\"./msdf.vert\");\nconst fragShader = require(\"./msdf.frag\");\n\nexport class MSDFRenderer extends PIXI.ObjectRenderer {\n\n    private shader: PIXI.Shader;\n\n    constructor(renderer: PIXI.WebGLRenderer) {\n        super(renderer);\n    }\n\n    public onContextChange(): void {\n        const gl = this.renderer.gl;\n        this.shader = new PIXI.Shader(gl, vertShader, fragShader);\n    }\n\n    public render(msdfText: MSDFText) {\n        const renderer = this.renderer;\n        const texture = msdfText.texture;\n        const font = msdfText.fontData;\n        const gl = renderer.gl;\n\n        // validate\n        if (!texture || !texture.valid || !font.rawSize) return;\n\n        let glData = msdfText.glDatas[renderer.CONTEXT_UID];\n\n        if (!glData) {\n            // renderer.bindVao(null);\n            glData = {\n                shader: this.shader,\n                vertexBuffer: PIXI.glCore.GLBuffer.createVertexBuffer(gl, msdfText.vertices, gl.STREAM_DRAW),\n                uvBuffer: PIXI.glCore.GLBuffer.createVertexBuffer(gl, msdfText.uvs, gl.STREAM_DRAW),\n                indexBuffer: PIXI.glCore.GLBuffer.createIndexBuffer(gl, msdfText.indices, gl.STATIC_DRAW),\n                // build the vao object that will render..\n                vao: null,\n                dirty: msdfText.dirty,\n                indexDirty: msdfText.indexDirty,\n            };\n            glData.vao = new PIXI.glCore.VertexArrayObject(gl)\n                .addIndex(glData.indexBuffer)\n                .addAttribute(glData.vertexBuffer, glData.shader.attributes.aVertexPosition, gl.FLOAT, false, 2 * 4, 0)\n                .addAttribute(glData.uvBuffer, glData.shader.attributes.aTextureCoord, gl.FLOAT, false, 2 * 4, 0);\n\n            msdfText.glDatas[renderer.CONTEXT_UID] = glData;\n        }\n\n        renderer.bindVao(glData.vao);\n\n        if (msdfText.dirty !== glData.dirty) {\n            glData.dirty = msdfText.dirty;\n            glData.uvBuffer.upload(msdfText.uvs);\n        }\n\n        if (msdfText.indexDirty !== glData.indexDirty) {\n            glData.indexDirty = msdfText.indexDirty;\n            glData.indexBuffer.upload(msdfText.indices);\n        }\n\n        glData.vertexBuffer.upload(msdfText.vertices);\n\n        renderer.bindShader(glData.shader);\n\n        glData.shader.uniforms.uSampler = renderer.bindTexture(texture);\n\n        if (renderer.state) renderer.state.setBlendMode(msdfText.blendMode);\n\n        glData.shader.uniforms.translationMatrix = msdfText.worldTransform.toArray(true);\n        glData.shader.uniforms.u_alpha = msdfText.worldAlpha;\n        glData.shader.uniforms.u_color = PIXI.utils.hex2rgb(font.color);\n        glData.shader.uniforms.u_fontSize = font.fontSize * msdfText.scale.x;\n        glData.shader.uniforms.u_fontInfoSize = 1;\n        glData.shader.uniforms.u_weight = font.weight;\n        glData.shader.uniforms.u_pxrange = font.pxrange;\n        glData.shader.uniforms.strokeWeight = font.strokeWeight;\n        glData.shader.uniforms.strokeColor = PIXI.utils.hex2rgb(font.strokeColor);\n        glData.shader.uniforms.tint = PIXI.utils.hex2rgb(msdfText.tint);\n        glData.shader.uniforms.hasShadow = font.dropShadow;\n        glData.shader.uniforms.shadowOffset = new Float32Array([font.dropShadowOffset.x, font.dropShadowOffset.x]);\n        glData.shader.uniforms.shadowColor = PIXI.utils.hex2rgb(font.dropShadowColor);\n        glData.shader.uniforms.shadowAlpha = font.dropShadowAlpha;\n        glData.shader.uniforms.shadowSmoothing = font.dropShadowBlur;\n\n        const drawMode = msdfText.drawMode = gl.TRIANGLES;\n        glData.vao.draw(drawMode, msdfText.indices.length, 0);\n    }\n}\n\nPIXI.WebGLRenderer.registerPlugin(\"msdf\", MSDFRenderer);\n\n\n\n// WEBPACK FOOTER //\n// ./src/MSDFRenderer.ts", "module.exports = \"#define GLSLIFY 1\\nattribute vec2 aVertexPosition;\\nattribute vec2 aTextureCoord;\\n\\nuniform mat3 translationMatrix;\\nuniform mat3 projectionMatrix;\\nuniform float u_fontInfoSize;\\n\\nvarying vec2 vTextureCoord;\\n\\nvoid main(void)\\n{\\n    vTextureCoord = aTextureCoord;\\n    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition * u_fontInfoSize, 1.0)).xy, 0.0, 1.0);\\n}\\n\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/msdf.vert\n// module id = 3\n// module chunks = 0", "module.exports = \"#define GLSLIFY 1\\nvarying vec2 vTextureCoord;\\nuniform vec3 u_color;\\nuniform sampler2D uSampler;\\nuniform float u_alpha;\\nuniform float u_fontSize;\\nuniform float u_weight;\\nuniform float u_pxrange;\\n\\nuniform vec3 tint;\\n// Stroke effect parameters\\nuniform float strokeWeight;\\nuniform vec3 strokeColor;\\n\\n// Shadow effect parameters\\nuniform bool hasShadow;\\nuniform vec2 shadowOffset;\\nuniform float shadowSmoothing;\\nuniform vec3 shadowColor;\\nuniform float shadowAlpha;\\n\\nfloat median(float r, float g, float b) {\\n    return max(min(r, g), min(max(r, g), b));\\n}\\n\\nvoid main(void)\\n{\\n    float smoothing = clamp(2.0 * u_pxrange / u_fontSize, 0.0, 0.5);\\n\\n    vec2 textureCoord = vTextureCoord * 2.;\\n    vec3 sample = texture2D(uSampler, vTextureCoord).rgb;\\n    float dist = median(sample.r, sample.g, sample.b);\\n\\n    float alpha;\\n    vec3 color;\\n\\n    // dirty if statment, will change soon\\n    if (strokeWeight > 0.0) {\\n        alpha = smoothstep(strokeWeight - smoothing, strokeWeight + smoothing, dist);\\n        float outlineFactor = smoothstep(u_weight - smoothing, u_weight + smoothing, dist);\\n        color = mix(strokeColor, u_color, outlineFactor) * alpha;\\n    } else {\\n        alpha = smoothstep(u_weight - smoothing, u_weight + smoothing, dist);\\n        color = u_color * alpha;\\n    }\\n    vec4 text = vec4(color * tint, alpha) * u_alpha;\\n    if (hasShadow == false) {\\n        gl_FragColor = text;\\n    } else {\\n        vec3 shadowSample = texture2D(uSampler, vTextureCoord - shadowOffset).rgb;\\n        float shadowDist = median(shadowSample.r, shadowSample.g, shadowSample.b);\\n        float distAlpha = smoothstep(0.5 - shadowSmoothing, 0.5 + shadowSmoothing, shadowDist);\\n        vec4 shadow = vec4(shadowColor, shadowAlpha * distAlpha);\\n        gl_FragColor = mix(shadow, text, text.a);\\n    }\\n}\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/msdf.frag\n// module id = 4\n// module chunks = 0", "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule ExecutionEnvironment\n */\n\n/*jslint evil: true */\n\n'use strict';\n\nvar canUseDOM = !!(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\n/**\n * Simple, lightweight module assisting with the detection and context of\n * Worker. Helps avoid circular dependencies and allows code to reason about\n * whether or not they are in a Worker, even if they never include the main\n * `ReactWorker` dependency.\n */\nvar ExecutionEnvironment = {\n\n  canUseDOM: canUseDOM,\n\n  canUseWorkers: typeof Worker !== 'undefined',\n\n  canUseEventListeners:\n    canUseDOM && !!(window.addEventListener || window.attachEvent),\n\n  canUseViewport: canUseDOM && !!window.screen,\n\n  isInWorker: !canUseDOM // For now, this is true - might change in the future.\n\n};\n\nmodule.exports = ExecutionEnvironment;\n", "import 'libs/pixi-msdf-text/dist/pixi-msdf-text.js';\n\nconst ASSETS_PATH = window.location.href.indexOf('localhost') > -1 ?\n    '' : '/r_static/webdesigner/';\n\n\nclass MsdfTextFactory {\n    constructor(options) {\n        this.options = options;\n        this.fontsLoaded = false;\n    }\n\n    create(copy) {\n        return new MSDFText.MSDFText(copy, this.options);\n    }\n\n    init(pixiInstance, callback) {\n\n        return new Promise((resolve) => {\n            if (this.fontsLoaded) {\n                resolve();\n            } else {\n                pixiInstance\n                    .loader\n                    .add([ASSETS_PATH + \"fonts/inter-msdf-xml/Inter-UI-Medium.fnt\"])\n                    .load((e) => {\n                        console.log(\"loaded\", e);\n                        resolve();\n                    });\n            }\n        });\n    }\n}\n\nconst textOptions = {\n    fontFace: \"Inter-UI-Medium\",\n    fontSize: 14.2,\n    fillColor: 0xffffff,\n    weight: 0.75,\n    strokeThickness: 0,\n    // strokeColor: 0x051855,\n    dropShadow: false,\n    /*dropShadowColor: 0x000000,\n    dropShadowOffset: new PIXI.Point(0.004, 0.004),\n    dropShadowAlpha: 0.4,\n    dropShadowBlur: 0.1,*/\n    align: \"left\",\n    letterSpacing: 0,\n    baselineOffset: 8,\n    debugLevel: 0\n};\n\n\nconst gltext = new MsdfTextFactory(textOptions);\n\nexport { gltext }", "\nimport * as PIXI from 'pixi.js';\n\nconst drawTag = (color, data) => {\n    let t = 3;\n    let a = 10;\n    data.graphics\n        //.lineStyle(2, 0xffffff, selected?1:0.2)\n        .beginFill(color)\n        .drawRoundedRect(data.width - a - 2 * t, data.height - a - 2 * t, a, a, 3)\n        .endFill();\n};\n/*\nsprite.addChild(this.drawText(item.name +\"\\n\" +item.id, 0, 0, `${item.name}${item.id}${type}${selected}`));\n\ndrawText(text, x,y, hash) {\n    let pixiApp = this.pixiApp;\n    let sampleText;\n\n    hash = XXH.h32(hash, 0xDECA ).toString(16);\n\n    if(this.labelsCache.has(hash)) {\n        sampleText = this.labelsCache.get(hash);\n    } else {\n        sampleText = gltext.create(text);\n        this.labelsCache.set(hash, sampleText);\n    }\n    //sampleText.pivot.set(sampleText.textWidth / 2, sampleText.textHeight / 2);\n    sampleText.position.set(x, y);\n    //sampleText.interactive = true;\n    //sampleText.buttonMode = true;\n    return sampleText;\n}*/\n\nconst drawLabel = (color, data) => {\n\n    let t = 3;\n    let a = 40;\n    data.graphics\n        //.lineStyle(2, 0xffffff, selected?1:0.2)\n        .beginFill(color)\n        .drawRoundedRect(data.width - 10 - 2 * t, t * 2, 10, a, 3)\n        .endFill();\n};\n\nfunction boxApperance(graphics, w, h, item, ctx) {\n\n    let data = { width: w, height: h, graphics };\n    console.log(\"BA\", item);\n    switch (item.type) {\n        case \"element\":\n            drawTag(0x00ff00, data);\n            break;\n        case \"component\":\n            drawTag(0x0000ff, data);\n            break;\n        case \"component-set\":\n            drawTag(0xffffff, data);\n            break;\n        case \"mesh\":\n            drawTag(0xff0000, data);\n            break;\n        case \"table\":\n            drawTag(0x654321, data);\n            break;\n        case \"component-table\":\n            drawTag(0x654321, data);\n            break;\n        default:\n            drawTag(0xffffff, data);\n            break;\n    }\n\n    if (item.item.height == \"sub\") {\n        drawLabel(0xffffff, data);\n    }\n}\n\nexport { boxApperance }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"grid-bar\",class:(\"\" + _vm.behaviour)},[_c('div',{staticClass:\"flex row\"},[(_vm.palette!='false')?_c('div',{staticClass:\"col top\"},[_c('div',{staticClass:\"row black\"},[_c('div',{staticClass:\"col-12\"},[_c('div',{staticClass:\"row\"},[_c('div',{staticClass:\"col-1\",staticStyle:{\"padding-top\":\"7px\"}},[_c('q-input',{staticClass:\"small-search\",attrs:{\"label\":\"Search by name or ID\",\"dense\":\"dense\",\"debounced\":\"debounced\",\"filled\":\"filled\",\"dark\":\"dark\"},model:{value:(_vm.search),callback:function ($$v) {_vm.search=$$v},expression:\"search\"}},[_c('q-icon',{attrs:{\"slot\":\"before\",\"name\":\"search\",\"dense\":\"dense\"},slot:\"before\"})],1)],1),_c('div',{staticClass:\"col-8 users\",staticStyle:{\"padding-top\":\"5px\",\"padding-left\":\"5px\"}},[_c('q-select',{staticClass:\"users\",attrs:{\"dark\":\"dark\",\"options-dark\":\"options-dark\",\"filled\":\"filled\",\"dense\":\"dense\",\"items-aligned\":\"items-aligned\",\"max-values\":\"2\",\"use-chips\":\"use-chips\",\"options-dense\":\"options-dense\",\"options\":_vm.selectOptions,\"multiple\":\"multiple\"},model:{value:(_vm.userFilter),callback:function ($$v) {_vm.userFilter=$$v},expression:\"userFilter\"}},[_c('q-icon',{attrs:{\"slot\":\"before\",\"name\":\"account_circle\",\"dense\":\"dense\"},slot:\"before\"})],1)],1)])])]),_c('new-palette',{attrs:{\"source\":_vm.filteredSources,\"box\":_vm.box,\"actAsNavigation\":_vm.actAsNavigation}})],1):_vm._e()])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"ruler-container\"})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import { cape } from '@core/cape';\n\nimport * as PIXI from 'pixi.js';\nimport { gltext } from '@core/gl/msdf-text';\nimport normalizeWheel from 'normalize-wheel';\nimport _ from 'lodash';\nimport XXH from 'xxhashjs';\n\nimport { boxApperance } from '@cape-ui/editors/renderers/box-apperance';\n\nlet Ruler = {\n    ORIENTATION_VERTICAL: 'vertical',\n    ORIENTATION_HORIZONTAL: 'horizontal'\n};\n\nclass Director {\n\n    constructor() {\n\n        this.checked = false;\n        this.bgDrawn = false;\n        this.items = [];\n        this.labels = [];\n        this.sprites = [];\n        this.orderHittest = [];\n        this.getSortArray = [];\n\n        this.cache = [];\n\n        //   this.actAsNavigation = true;\n\n        window.addEventListener(\"resize\", (e) => {\n            if (!this.pixiApp) return;\n            let { width, height } = this.getSize();\n            this.pixiApp.renderer.resize(width, height);\n            this.pixiApp.view.style[\"max-width\"] = `${width}px`;\n            this.backgroundContainer.clear();\n            this.bgDrawn = false;\n            this.draw();\n        });\n\n        this.box = { width: 120, height: 90, cols: 2, rows: 2, gutter: 8 };\n    }\n\n    updateBox(box) {\n\n        if (box && box.width && box.height && box.rows && box.cols && box.gutter) {\n            this.box = box;\n            if (this.tilecache) this.tilecache.clear();\n            this.geocache = [];\n            this.cache = [];\n        }\n    }\n\n    initializePixi() {\n\n        if (this.pixiApp) return;\n\n        this.labelsCache = new Map();\n        this.labels = [];\n        this.selected = { id: -1 };\n\n        let { width, height } = this.getSize();\n\n        this.ready = false;\n\n        //   PIXI.settings.RESOLUTION = 1.1;\n\n        // Disable interpolation when scaling, will make texture be pixelated\n        //   PIXI.settings.SCALE_MODE = PIXI.SCALE_MODES.NEAREST;\n\n        this.pixiApp = new PIXI.Application(width, height, { interactionFrequency: 3, backgroundColor: 0x202020, resolution: 2 });\n\n        let pixiApp = this.pixiApp;\n\n        this.pixiRoot = new PIXI.Container();\n        this.pixiHitests = new PIXI.Container();\n        this.pixiRoot = new PIXI.Container();\n\n        this.drawingContainer = new PIXI.Graphics();\n        this.backgroundContainer = new PIXI.Graphics();\n\n        this.pixiRoot.addChild(this.backgroundContainer);\n        this.pixiRoot.addChild(this.drawingContainer);\n\n        this.pixiApp.stage.addChild(this.pixiRoot);\n        this.pixiApp.stage.addChild(this.pixiHitests);\n\n        this.vue.$el.appendChild(pixiApp.view);\n        this.pixiApp.view.style[\"max-width\"] = `${width}px`;\n\n        this.delta = 0;\n        this.drawing = false;\n\n        this.throttledDraw = _.throttle(this.draw, 15);\n        this.pixiApp.renderer.plugins.interaction.autoPreventDefault = false;\n\n        this.pixiApp.stop();\n\n        gltext.init(this.pixiApp).then(() => {\n            this.ready = true;\n            //    this.pixiApp.start();\n            this.draw();\n        });\n\n        this.geocache = [];\n        this.tilecache = new Map();\n\n        //   document.addEventListener(\"click\", this.git.bind(this));\n\n    }\n\n    git(e) {\n        let { clientX: x, clientY: y } = e;\n        let { x: x2, y: y2 } = this.getXY();\n        let cx = x - x2;\n        let cy = y - y2;\n        this.hittests.map(box => {\n            console.log('rrr', box);\n            if (cx >= box.x && cx <= box.x + box.w &&\n                cy >= box.y && cy <= box.y + box.h) {\n                //alert(box.no);\n            }\n        });\n        console.log(\"rrr\", cx, cy);\n    }\n\n    setContext(vueComponentInstance) {\n        this.vue = vueComponentInstance;\n        this.initializePixi();\n        this.vue.$el.appendChild(this.pixiApp.view);\n        this.delta = 0;\n\n        this.vue.$el.addEventListener('mousewheel', (event) => {\n            const normalized = normalizeWheel(event);\n            this.delta += normalized.pixelY;\n            this.throttledDraw();\n            //this.pixiApp.render();\n        });\n\n        this.selected = { id: this.vue.$route.params.id };\n        this.draw();\n    }\n\n    getXY() {\n        let size = this.vue.$el.getBoundingClientRect();\n        return { x: size.top, y: size.left };\n    }\n\n    getSize() {\n\n        let size = this.vue.$el.getBoundingClientRect();\n        let edge = 90 * 2;\n        switch (this.orientation) {\n            case Ruler.ORIENTATION_VERTICAL:\n                return { width: edge, height: size.height };\n                break;\n            case Ruler.ORIENTATION_HORIZONTAL:\n                this.maxBlocks = Math.round(size.width / this.box.width) * this.box.rows;\n                this.maxBlocks = this.maxBlocks % this.box.rows == 0 ? this.maxBlocks : this.maxBlocks + this.box.rows;\n                return { width: size.width, height: edge };\n                break;\n            default:\n                this.maxBlocks = Math.round(size.width / this.box.width) * this.box.rows;\n                this.maxBlocks = this.maxBlocks % this.box.rows == 0 ? this.maxBlocks : this.maxBlocks + this.box.rows;\n\n                return { width: size.width, height: edge };\n                break;\n        }\n    }\n\n    selectElement(item) {\n\n        if (item.type == 'element') return;\n\n        let item2 = item;\n\n        let url = `/${item.type == 'mesh' ? \"meshes\" : \"components\"}/${this.vue.$route.params.selectedCollection}/${item.item.id}/`;\n\n        if (item.type == \"componentRelations\") {\n            url = `/components/${this.vue.$route.params.selectedCollection}/${item.item.parent}/${item.item.id}/`;\n        }\n\n        this.vue.$router.push(url);\n        this.selected = { id: item.item.id };\n        this.draw();\n\n        console.log(url);\n    }\n\n    getElementPosition(no) {\n\n        let gutter = this.box.gutter;\n\n        let item_width = this.box.width;\n        let item_height = this.box.height;\n\n        let cols_in_block = this.box.cols;\n        let rows_in_block = this.box.rows;\n\n        let elements_in_block = cols_in_block * rows_in_block;\n\n        const getPosition = (no) => {\n\n            let block = Math.floor(no / elements_in_block);\n\n            let index = no - block * elements_in_block;\n            let col = Math.floor((index) / rows_in_block);\n            let row = index - col * rows_in_block;\n            let block2 = Math.round(no / elements_in_block);\n\n            return {\n                x: (col * item_width) +\n                    (block * item_width * cols_in_block) + gutter / 2,\n                y: row * item_height + gutter / 2\n            };\n        };\n\n        return { ...getPosition(no), w: item_width - gutter, h: item_height - gutter };\n    }\n\n    drawItem(no, item, type) {\n\n        var sprite = new PIXI.Graphics();\n\n        let { x, y, w, h } = this.getElementPosition(no);\n\n        let selected = (item.id == this.vue.$route.params.id);\n\n        if (!selected && this.vue.$route.params) {\n            //    selected = item.id.toString() == this.vue.$route.params.id.toString();\n        }\n\n        sprite\n            //.lineStyle(2, 0xffffff, selected?1:0.2)\n            .beginFill(\n                selected ? 0x444444 : 0x111111\n            ).drawRect(0, 0, w, h).endFill();\n\n        boxApperance(sprite, w, h, { item, type }, \"palette\");\n\n        let name = _.truncate(item.name, {\n            'omission': '-',\n            'length': this.box.width / 10\n        });\n\n        if (item.name.length > 20) {\n            name += \"\\n\" + item.name.substring(this.box.width / 10, item.name.length);\n        }\n\n        sprite.addChild(this.drawText(name + \"\\n\", 0, 0, `${item.name}${item.id}${type}${selected}`));\n\n        sprite.hitArea = new PIXI.Rectangle(0, 0, w, h);\n        sprite.position.x = x;\n        sprite.position.y = y;\n        sprite.interactive = true;\n        sprite.buttonMode = true;\n        sprite.cursor = 'pointer';\n\n\n        /*  if(this.cache[options.hash]) {\n            var sprite1 = new PIXI.Sprite(this.cache[options.hash]);\n            sprite.addChild(sprite1);\n            // sprite.texture.update();\n        } else {*/\n\n        var opt = {\n            type,\n            id: item.id\n        };\n\n        let hash = XXH.h32(`${opt.id}${opt.type}${selected}`, 0xABCD).toString(16);\n\n        let add = (data, id, fetched = false) =>\n            cape.services.miniatures.add({ data: data, id, fetched }).then((result) => {\n                console.log(\"parsedGeo\", result);\n                // result: ImageBitmap from worker (OffscreenCanvas)\n                var bt = new PIXI.BaseTexture(result.painterState);\n                bt._sourceLoaded();\n                var texture = new PIXI.Texture(bt);\n                texture.defaultAnchor = { x: 0, y: 0 };\n                this.cache[hash] = texture;\n                var sprite1 = new PIXI.Sprite(texture);\n                sprite.addChildAt(sprite1, 0);\n                //  sprite1.texture.update();\n                sprite.cacheAsBitmap = true;\n                //   this.pixiApp.render();\n                if (!fetched) cape.api.updateComponentThumb(id, { thumbnails_data: result.geo });\n            });\n\n        if (this.geocache[hash]) {\n            var sprite1 = new PIXI.Sprite(this.cache[hash]);\n            sprite.addChild(sprite1);\n        } else {\n            let json = null;\n            if (type == \"componentRelations\" || type == \"component-mini\") {\n\n                console.log(\"componentRelations\", item);\n\n                if (item.thumbnails_data) {\n                    let geo = item.thumbnails_data;\n                    this.geocache[hash] = geo;\n                    add(geo, item.id, true);\n                } else {\n                    cape.api.geo.componentSet({\n                        type: 'componentRelations',\n                        id: item.id, // component-set id\n                        special: this.specialSettings\n                    })\n                        .forceFetch(true)\n                        .pipe((geo) => {\n                            console.log(\"RRR\", geo);\n                            this.geocache[hash] = geo;\n                            add(geo, item.id);\n                        }, \"paletteRelations\");\n                }\n\n            } else {\n                this.geocache[hash] = true;\n                this.cache[hash] = sprite;\n                sprite.cacheAsBitmap = true;\n            }\n        }\n        // sprite.cacheAsBitmap = true;\n\n        //   }\n\n        let move = () => {\n            return (e) => {\n                let sprite = this.startSprite;\n                if (!sprite) return;\n                if (sprite.delta) {\n\n                } else if (sprite.delta === false && this.pixiApp) {\n\n                    var data = sprite.data;\n                    var newPosition = sprite.data.getLocalPosition(this.pixiApp.stage);\n\n                    var oldPosition = sprite.startPosition;\n\n                    let r = Math.sqrt(\n                        Math.pow(newPosition.y - oldPosition.y, 2) +\n                        Math.pow(newPosition.x - oldPosition.x, 2)\n                    );\n\n                    if (r > 100) {\n                        sprite.delta = true;\n                    }\n                }\n                e.data.originalEvent.preventDefault();\n            }\n        };\n\n        let end = (item, sprite) => {\n            return () => {\n                if (!this.startSprite) return;\n                this.startSprite.dragging = false;\n                if (this.startSprite.delta != true && this.startItem) {\n                    if (this.actAsNavigation != false) {\n                        //alert(this.startSprite.wasClicked);\n                        if (this.startSprite.wasClicked) {\n                            this.selectElement({ item: this.startItem, type });\n                            this.throttledDraw();\n                        } else {\n                            let s = this.startSprite;\n                            s.wasClicked = true;\n                            setTimeout(() => {\n                                s.wasClicked = false;\n                            }, 300);\n                        }\n                    }\n                }\n                this.startItem = null;\n                this.startSprite = null;\n            }\n        };\n\n        let start = (id, t, sprite, item) => {\n            return (e) => {\n                console.log(123, e);\n                this.startSprite = sprite;\n                this.startItem = item;\n                sprite.delta = false;\n                sprite.data = e.data;\n                sprite.startPosition = sprite.data.getLocalPosition(this.pixiApp.stage);\n                this.selected = { id };\n                cape.application.dnd.dragElement(this.vue.dragId, id, t, e.data.originalEvent);\n            };\n        };\n\n        sprite.on('mousedown', start(item.id, type, sprite, item))\n            .on('mouseup', end(item, sprite))\n            .on('mouseupoutside', end(item, sprite));\n        //   .on('mouseoutside', end(item, sprite));\n\n        sprite.on('mousemove', move(sprite));\n\n        sprite.on('rightdown', (e) => {\n            e.data.originalEvent.preventDefault();\n            e.data.originalEvent.stopPropagation();\n            cape.application.contextMenu.show(e, item, \"palette\");\n            //e.data.originalEvent.preventDefault();\n        });\n\n        this.pixiHitests.addChild(sprite);\n        this.sprites.push(sprite);\n\n        return sprite;\n\n    }\n\n    transferControl() {\n        this.dragging = false;\n    }\n\n    draw() {\n\n        if (!this.ready) return;\n        //   if(this.drawing) return;\n\n\n        this.drawing = true;\n\n        /*    this.labels.map((label, i) => {\n                if(label) {\n                    this.pixiApp.stage.removeChild(label);\n                    this.labels[i] = null;\n                    label = null;\n                }\n            });*/\n\n        this.sprites.map((sprite, i) => {\n            if (sprite) {\n                this.pixiHitests.removeChild(sprite);\n                //     sprite.destroy({children:true, texture:true, baseTexture:true});\n                this.sprites[i] = null;\n                //sprite = null;\n            }\n        });\n\n        this.sprites = this.sprites.filter(Boolean);\n\n        this.drawingContainer.clear();\n        //let { width, height } = this.getSize();\n\n        let { width, height, rows } = this.getSize();\n        if (!this.bgDrawn) {\n            let edgeStep = 10;\n            for (var i = 0; i < width / edgeStep; i++) {\n                for (var j = 0; j < height / edgeStep; j++) {\n                    this.backgroundContainer.lineStyle(1, 0, 1).drawRect(i * edgeStep, j * edgeStep, 1, 1);\n                }\n            }\n            this.bgDrawn = true;\n        }\n\n        this.drawing = false;\n\n        if (this.vue.source) {\n            var noInPalette = 0, max = this.maxBlocks + 1, t = this.vue.source.length - 1,\n                start = Math.ceil(this.delta / 50);\n\n            (\n                start *= this.box.rows,\n                start < 0 ? (start = 0, this.delta = 0) : false\n                //    start+max > this.vue.source.length-1 ? (start = 0) : false\n            );\n\n            this.hittests = [];\n\n            for (let i = 0, HashSprite = XXH.h32(0x1234); i < max - 1; i++) {\n                if (start + noInPalette > t) return;\n                let index = noInPalette - start;\n                let pointer = this.vue.source[start + noInPalette];\n                let item = pointer.item;\n                let source = { type: pointer.type };\n\n                let { x, y, w, h } = this.getElementPosition(noInPalette);\n                this.hittests.push({ x, y, w, h, no: noInPalette });\n\n                let hash = HashSprite.update(\n                    `${item.id}${source.type}${item.id == this.selected.id}`\n                ).digest().toString(16);\n\n                if (this.tilecache.has(hash)) {\n                    let sprite = this.tilecache.get(hash);\n                    this.sprites.push(sprite);\n                    let { x, y } = this.getElementPosition(noInPalette);\n                    sprite.position.x = x;\n                    sprite.position.y = y;\n                    this.pixiHitests.addChild(sprite);\n                } else {\n                    let sprite = this.drawItem(noInPalette, item, source.type);\n                    this.tilecache.set(hash, sprite);\n                }\n\n                noInPalette++;\n            }\n            //   this.pixiApp.render();\n        }\n\n    }\n\n    drawText(text, x, y, hash) {\n        let pixiApp = this.pixiApp;\n        let sampleText;\n\n        hash = XXH.h32(hash, 0xDECA).toString(16);\n\n        if (this.labelsCache.has(hash)) {\n            sampleText = this.labelsCache.get(hash);\n        } else {\n            sampleText = gltext.create(text);\n            this.labelsCache.set(hash, sampleText);\n        }\n        //sampleText.pivot.set(sampleText.textWidth / 2, sampleText.textHeight / 2);\n        sampleText.position.set(x, y);\n        //sampleText.interactive = true;\n        //sampleText.buttonMode = true;\n        return sampleText;\n    }\n}\n\nconst renderer = new Director();\n\nexport {\n    renderer\n}", "<template lang=\"pug\">\n    div.ruler-container\n        //TylkoContextMenu\n</template>\n<script>\nimport { cape } from '@core/cape'\nimport * as PIXI from 'pixi.js'\nimport { gltext } from '@core/gl/msdf-text'\nimport normalizeWheel from 'normalize-wheel'\nimport _ from 'lodash'\n\nimport XXH from 'xxhashjs'\n\nimport { renderer } from './paleta-renderer'\n\nvar H = XXH.h32(0xabcd) // seed = 0xABCD\nvar h = H.update('abcd')\n    .digest()\n    .toString(16)\n\nlet Ruler = {\n    ORIENTATION_VERTICAL: 'vertical',\n    ORIENTATION_HORIZONTAL: 'horizontal',\n}\n\nexport default {\n    name: 'TylkoRuler',\n\n    props: [\n        'box',\n        'actAsNavigation',\n        'source',\n        'orientation',\n        'steps',\n        'meshWidthBlend',\n    ],\n\n    methods: {},\n\n    watch: {\n        source() {\n            if (renderer) {\n                renderer.draw()\n                renderer.pixiApp.render()\n                renderer.pixiApp.start()\n            }\n        },\n        box() {\n            if (renderer) {\n                renderer.updateBox(this.box)\n                renderer.draw()\n            }\n        },\n        actAsNavigation() {\n            if (renderer) {\n                renderer.actAsNavigation = this.actAsNavigation\n                renderer.draw()\n            }\n        },\n    },\n\n    data() {\n        return {\n            selectedStep: 0,\n            labels: [],\n            sprites: [],\n            cache: [],\n        }\n    },\n\n    updated() {\n        renderer.pixiApp.render()\n        renderer.pixiApp.start()\n    },\n\n    mounted() {\n        this.dragId = cape.application.dnd.addSourceComponent({\n            type: 'palette',\n            instance: this,\n        }).dragSourceID\n\n        renderer.setContext(this)\n        renderer.updateBox(this.box)\n    },\n\n    created() {\n        // this.initMiniatures();\n    },\n}\n</script>\n<style lang=\"scss\" scoped>\n.ruler-container {\n    width: auto;\n    height: 180px;\n}\n</style>\n", "import mod from \"-!../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-container.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-container.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./palette-container.vue?vue&type=template&id=24fbf5f8&scoped=true&lang=pug&\"\nimport script from \"./palette-container.vue?vue&type=script&lang=js&\"\nexport * from \"./palette-container.vue?vue&type=script&lang=js&\"\nimport style0 from \"./palette-container.vue?vue&type=style&index=0&id=24fbf5f8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"24fbf5f8\",\n  null\n  \n)\n\nexport default component.exports", "<template lang=\"pug\">\n    div.grid-bar(:class=\"`${behaviour}`\")\n        div.flex.row\n            div.col.top(v-if=\"palette!='false'\")\n                div.row.black\n                    div.col-12\n                        div.row\n                            div.col-1(style=\"padding-top: 7px;\")\n                                q-input.small-search(v-model=\"search\", label=\"Search by name or ID\", dense, debounced, filled, dark)\n                                    q-icon(name=\"search\", dense, slot=\"before\")\n                            div.col-8.users(style=\"padding-top: 5px; padding-left: 5px;\")\n                                q-select.users(dark,\n                                    options-dark,\n                                    filled,\n                                    dense,\n                                    items-aligned,\n                                    max-values=\"2\",\n                                    use-chips,\n                                    options-dense,\n                                    :options=\"selectOptions\",\n                                    v-model=\"userFilter\",\n                                    multiple)\n                                    q-icon(name=\"account_circle\", dense, slot=\"before\")\n                new-palette(:source=\"filteredSources\", :box=\"box\", :actAsNavigation=\"actAsNavigation\")\n</template>\n<script>\nconst heights_names = {\n    '200': 'A',\n    '300': 'B',\n    '400': 'C',\n    '500': 'D',\n    '600': 'E',\n    '700': 'F',\n    '800': 'G',\n    '900': 'H',\n    '1000': 'I',\n    '1100': 'J',\n    '1200': 'K',\n}\n\nimport { Filter } from '@core/data/filter'\nimport TylkoPanel from '@tylko/cape-palette/palette-container'\nimport { cape } from '@core/cape'\nimport XXH from 'xxhashjs'\nimport Vue from 'vue'\n\nexport default {\n    name: 'Tylko-Bar',\n    props: [\n        'typesToDisplay',\n        'box',\n        'actAsNavigation',\n        'mode',\n        'behaviour',\n        'selected',\n        'menu',\n        'setActiveCollection',\n        'palette',\n    ],\n\n    data() {\n        return {\n            projectFilter: 'name-2',\n            collections: [],\n\n            selectOptions: [\n                { label: 'All', value: -1 },\n                { label: 'Other', value: -2 },\n            ],\n\n            userFilter: [{ label: 'All', value: -1 }],\n            sources: [],\n            users: [],\n            elements: [],\n            components: [],\n            meshes: [],\n\n            search: '',\n            filteredSources: [],\n        }\n    },\n\n    components: {\n        'new-palette': TylkoPanel,\n    },\n\n    watch: {\n        userFilter() {\n            this.build()\n        },\n        projectFilter() {\n            this.build()\n        },\n        search() {\n            this.build()\n        },\n        selected() {\n            this.build()\n        },\n    },\n\n    methods: {\n        reflesh() {},\n\n        reloadAfterCreation() {\n            this.load()\n            //this.$emit('reloadAfterCreation');\n        },\n\n        changeCollection() {\n            //#endregionthis.loadObjects();\n            this.$emit('setActiveCollection', this.projectFilter)\n            if (+this.$route.params.selectedCollection != +this.projectFilter) {\n                this.$router.push(`/collection/${this.projectFilter}`)\n            }\n        },\n\n        async build() {\n            let sources = []\n            this.sources = []\n\n            sources = [\n                { list: this.meshes, type: 'mesh' },\n                { list: this.elements, type: 'element' },\n                { list: this.components, type: 'component' },\n                { list: this.tables, type: 'component-table' },\n                { list: this.relationComponents, type: 'componentRelations' },\n                { list: this.components, type: 'component-mini' },\n            ]\n\n            let choosenSources = []\n            this.typesToDisplay.split(',').map(type => {\n                sources.map(source => {\n                    type == source.type ? choosenSources.push(source) : false\n                })\n            })\n            sources = choosenSources\n\n            const filterItem = (item, type) => {\n                let user = -1\n                if (type != 'element') {\n                    if (this.projectFilter != -1)\n                        this.userFilter.map(id => {\n                            console.log('matchid', id.value, item.owner)\n                            if (id.value == -1) user = 1\n                            if (id.value == -2 && item.owner == null) user = 1\n                            if (id.value == item.owner) user = 1\n                        })\n                } else {\n                    user = 1\n                }\n\n                let name =\n                    item.name.toLowerCase().indexOf(this.search.toLowerCase()) >\n                    -1\n\n                let idMatch = String(item.id).indexOf(String(this.search)) > -1\n                return (name == 1 && user == 1) || idMatch\n            }\n\n            sources = _.map(sources, source => ({\n                list: _.filter(source.list, item =>\n                    filterItem(item, source.type)\n                ),\n                type: source.type,\n            }))\n\n            sources = sources.map(source => {\n                let r = source.list.map(item => ({ item, type: source.type }))\n                this.sources = this.sources.concat(r)\n            })\n\n            this.filteredSources = this.sources\n            console.log('searchMatch', this.sources)\n            this.$emit(\n                'searchMatch',\n                this.search.length > 0 ? this.sources.map(i => i.item.id) : null\n            )\n        },\n\n        async loadObjects() {\n            let {\n                mesh,\n                component,\n                ['component-table']: tables,\n                ['component-set']: componentSet,\n            } = await cape.api.palette\n                .collection(this.projectFilter)\n                .componentSet.componentTable.meshes.fetch()\n\n            this.tables = tables\n            this.meshes = mesh\n            this.components = componentSet\n            this.build()\n\n            let {\n                component: relationComponent,\n            } = await cape.api.palette\n                .collection(this.projectFilter)\n                .components.thumbs.fetch()\n\n            this.relationComponents = relationComponent\n\n            this.relationComponents = this.relationComponents.map(comp => {\n                return {\n                    ...comp,\n                    name: `${heights_names[comp.height] || comp.height}: ${\n                        comp.name\n                    }`,\n                }\n            })\n\n            console.log('rel', relationComponent)\n\n            cape.api.elements.subscribe(result => {\n                this.elements = result\n                this.build()\n            })\n        },\n        async loadPage() {\n            let {\n                collection,\n                user,\n            } = await cape.api.palette.collection().fetch()\n\n            // collections\n            this.collections = collection\n\n            // users\n            user = user.map(user => ({ label: user.username, value: user.id }))\n            user.unshift(\n                { label: 'All', value: -1 },\n                { label: 'Other', value: -2 }\n            )\n            this.selectOptions = user\n\n            this.loadObjects()\n        },\n\n        async load() {\n            let { collection } = await cape.api.palette.collection().fetch()\n            this.collections = collection\n            this.projectFilter =\n                this.$route.params.selectedCollection || collection[0].id\n            switch (this.$route.matched[1].name) {\n                case 'components':\n                    cape.api\n                        .component(this.$route.params.id)\n                        .subscribe(({ collection }) => {\n                            this.projectFilter = collection\n                            this.loadPage()\n                        })\n                    break\n                case 'meshes':\n                    cape.api\n                        .mesh(this.$route.params.id)\n                        .subscribe(({ collection }) => {\n                            this.projectFilter = collection\n                            this.loadPage()\n                        })\n                    break\n                default:\n                    this.loadPage()\n            }\n        },\n    },\n\n    updated() {\n        this.reflesh()\n        //this.build();\n    },\n\n    mounted() {\n        this.build()\n        this.load()\n    },\n\n    // created() {\n    //     this.load()\n    // }\n}\n</script>\n<style lang=\"scss\">\n@import '~@theme/elements-bar.scss';\n\n.users {\n    width: 400px !important;\n}\n</style>\n", "import mod from \"-!../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./palette-bar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./palette-bar.vue?vue&type=template&id=3483c5fc&lang=pug&\"\nimport script from \"./palette-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./palette-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./palette-bar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "module.exports = require('./src/normalizeWheel.js');\n", "'use strict';\nif (require('./_descriptors')) {\n  var LIBRARY = require('./_library');\n  var global = require('./_global');\n  var fails = require('./_fails');\n  var $export = require('./_export');\n  var $typed = require('./_typed');\n  var $buffer = require('./_typed-buffer');\n  var ctx = require('./_ctx');\n  var anInstance = require('./_an-instance');\n  var propertyDesc = require('./_property-desc');\n  var hide = require('./_hide');\n  var redefineAll = require('./_redefine-all');\n  var toInteger = require('./_to-integer');\n  var toLength = require('./_to-length');\n  var toIndex = require('./_to-index');\n  var toAbsoluteIndex = require('./_to-absolute-index');\n  var toPrimitive = require('./_to-primitive');\n  var has = require('./_has');\n  var classof = require('./_classof');\n  var isObject = require('./_is-object');\n  var toObject = require('./_to-object');\n  var isArrayIter = require('./_is-array-iter');\n  var create = require('./_object-create');\n  var getPrototypeOf = require('./_object-gpo');\n  var gOPN = require('./_object-gopn').f;\n  var getIterFn = require('./core.get-iterator-method');\n  var uid = require('./_uid');\n  var wks = require('./_wks');\n  var createArrayMethod = require('./_array-methods');\n  var createArrayIncludes = require('./_array-includes');\n  var speciesConstructor = require('./_species-constructor');\n  var ArrayIterators = require('./es6.array.iterator');\n  var Iterators = require('./_iterators');\n  var $iterDetect = require('./_iter-detect');\n  var setSpecies = require('./_set-species');\n  var arrayFill = require('./_array-fill');\n  var arrayCopyWithin = require('./_array-copy-within');\n  var $DP = require('./_object-dp');\n  var $GOPD = require('./_object-gopd');\n  var dP = $DP.f;\n  var gOPD = $GOPD.f;\n  var RangeError = global.RangeError;\n  var TypeError = global.TypeError;\n  var Uint8Array = global.Uint8Array;\n  var ARRAY_BUFFER = 'ArrayBuffer';\n  var SHARED_BUFFER = 'Shared' + ARRAY_BUFFER;\n  var BYTES_PER_ELEMENT = 'BYTES_PER_ELEMENT';\n  var PROTOTYPE = 'prototype';\n  var ArrayProto = Array[PROTOTYPE];\n  var $ArrayBuffer = $buffer.ArrayBuffer;\n  var $DataView = $buffer.DataView;\n  var arrayForEach = createArrayMethod(0);\n  var arrayFilter = createArrayMethod(2);\n  var arraySome = createArrayMethod(3);\n  var arrayEvery = createArrayMethod(4);\n  var arrayFind = createArrayMethod(5);\n  var arrayFindIndex = createArrayMethod(6);\n  var arrayIncludes = createArrayIncludes(true);\n  var arrayIndexOf = createArrayIncludes(false);\n  var arrayValues = ArrayIterators.values;\n  var arrayKeys = ArrayIterators.keys;\n  var arrayEntries = ArrayIterators.entries;\n  var arrayLastIndexOf = ArrayProto.lastIndexOf;\n  var arrayReduce = ArrayProto.reduce;\n  var arrayReduceRight = ArrayProto.reduceRight;\n  var arrayJoin = ArrayProto.join;\n  var arraySort = ArrayProto.sort;\n  var arraySlice = ArrayProto.slice;\n  var arrayToString = ArrayProto.toString;\n  var arrayToLocaleString = ArrayProto.toLocaleString;\n  var ITERATOR = wks('iterator');\n  var TAG = wks('toStringTag');\n  var TYPED_CONSTRUCTOR = uid('typed_constructor');\n  var DEF_CONSTRUCTOR = uid('def_constructor');\n  var ALL_CONSTRUCTORS = $typed.CONSTR;\n  var TYPED_ARRAY = $typed.TYPED;\n  var VIEW = $typed.VIEW;\n  var WRONG_LENGTH = 'Wrong length!';\n\n  var $map = createArrayMethod(1, function (O, length) {\n    return allocate(speciesConstructor(O, O[DEF_CONSTRUCTOR]), length);\n  });\n\n  var LITTLE_ENDIAN = fails(function () {\n    // eslint-disable-next-line no-undef\n    return new Uint8Array(new Uint16Array([1]).buffer)[0] === 1;\n  });\n\n  var FORCED_SET = !!Uint8Array && !!Uint8Array[PROTOTYPE].set && fails(function () {\n    new Uint8Array(1).set({});\n  });\n\n  var toOffset = function (it, BYTES) {\n    var offset = toInteger(it);\n    if (offset < 0 || offset % BYTES) throw RangeError('Wrong offset!');\n    return offset;\n  };\n\n  var validate = function (it) {\n    if (isObject(it) && TYPED_ARRAY in it) return it;\n    throw TypeError(it + ' is not a typed array!');\n  };\n\n  var allocate = function (C, length) {\n    if (!(isObject(C) && TYPED_CONSTRUCTOR in C)) {\n      throw TypeError('It is not a typed array constructor!');\n    } return new C(length);\n  };\n\n  var speciesFromList = function (O, list) {\n    return fromList(speciesConstructor(O, O[DEF_CONSTRUCTOR]), list);\n  };\n\n  var fromList = function (C, list) {\n    var index = 0;\n    var length = list.length;\n    var result = allocate(C, length);\n    while (length > index) result[index] = list[index++];\n    return result;\n  };\n\n  var addGetter = function (it, key, internal) {\n    dP(it, key, { get: function () { return this._d[internal]; } });\n  };\n\n  var $from = function from(source /* , mapfn, thisArg */) {\n    var O = toObject(source);\n    var aLen = arguments.length;\n    var mapfn = aLen > 1 ? arguments[1] : undefined;\n    var mapping = mapfn !== undefined;\n    var iterFn = getIterFn(O);\n    var i, length, values, result, step, iterator;\n    if (iterFn != undefined && !isArrayIter(iterFn)) {\n      for (iterator = iterFn.call(O), values = [], i = 0; !(step = iterator.next()).done; i++) {\n        values.push(step.value);\n      } O = values;\n    }\n    if (mapping && aLen > 2) mapfn = ctx(mapfn, arguments[2], 2);\n    for (i = 0, length = toLength(O.length), result = allocate(this, length); length > i; i++) {\n      result[i] = mapping ? mapfn(O[i], i) : O[i];\n    }\n    return result;\n  };\n\n  var $of = function of(/* ...items */) {\n    var index = 0;\n    var length = arguments.length;\n    var result = allocate(this, length);\n    while (length > index) result[index] = arguments[index++];\n    return result;\n  };\n\n  // iOS Safari 6.x fails here\n  var TO_LOCALE_BUG = !!Uint8Array && fails(function () { arrayToLocaleString.call(new Uint8Array(1)); });\n\n  var $toLocaleString = function toLocaleString() {\n    return arrayToLocaleString.apply(TO_LOCALE_BUG ? arraySlice.call(validate(this)) : validate(this), arguments);\n  };\n\n  var proto = {\n    copyWithin: function copyWithin(target, start /* , end */) {\n      return arrayCopyWithin.call(validate(this), target, start, arguments.length > 2 ? arguments[2] : undefined);\n    },\n    every: function every(callbackfn /* , thisArg */) {\n      return arrayEvery(validate(this), callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    fill: function fill(value /* , start, end */) { // eslint-disable-line no-unused-vars\n      return arrayFill.apply(validate(this), arguments);\n    },\n    filter: function filter(callbackfn /* , thisArg */) {\n      return speciesFromList(this, arrayFilter(validate(this), callbackfn,\n        arguments.length > 1 ? arguments[1] : undefined));\n    },\n    find: function find(predicate /* , thisArg */) {\n      return arrayFind(validate(this), predicate, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    findIndex: function findIndex(predicate /* , thisArg */) {\n      return arrayFindIndex(validate(this), predicate, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    forEach: function forEach(callbackfn /* , thisArg */) {\n      arrayForEach(validate(this), callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    indexOf: function indexOf(searchElement /* , fromIndex */) {\n      return arrayIndexOf(validate(this), searchElement, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    includes: function includes(searchElement /* , fromIndex */) {\n      return arrayIncludes(validate(this), searchElement, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    join: function join(separator) { // eslint-disable-line no-unused-vars\n      return arrayJoin.apply(validate(this), arguments);\n    },\n    lastIndexOf: function lastIndexOf(searchElement /* , fromIndex */) { // eslint-disable-line no-unused-vars\n      return arrayLastIndexOf.apply(validate(this), arguments);\n    },\n    map: function map(mapfn /* , thisArg */) {\n      return $map(validate(this), mapfn, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    reduce: function reduce(callbackfn /* , initialValue */) { // eslint-disable-line no-unused-vars\n      return arrayReduce.apply(validate(this), arguments);\n    },\n    reduceRight: function reduceRight(callbackfn /* , initialValue */) { // eslint-disable-line no-unused-vars\n      return arrayReduceRight.apply(validate(this), arguments);\n    },\n    reverse: function reverse() {\n      var that = this;\n      var length = validate(that).length;\n      var middle = Math.floor(length / 2);\n      var index = 0;\n      var value;\n      while (index < middle) {\n        value = that[index];\n        that[index++] = that[--length];\n        that[length] = value;\n      } return that;\n    },\n    some: function some(callbackfn /* , thisArg */) {\n      return arraySome(validate(this), callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n    },\n    sort: function sort(comparefn) {\n      return arraySort.call(validate(this), comparefn);\n    },\n    subarray: function subarray(begin, end) {\n      var O = validate(this);\n      var length = O.length;\n      var $begin = toAbsoluteIndex(begin, length);\n      return new (speciesConstructor(O, O[DEF_CONSTRUCTOR]))(\n        O.buffer,\n        O.byteOffset + $begin * O.BYTES_PER_ELEMENT,\n        toLength((end === undefined ? length : toAbsoluteIndex(end, length)) - $begin)\n      );\n    }\n  };\n\n  var $slice = function slice(start, end) {\n    return speciesFromList(this, arraySlice.call(validate(this), start, end));\n  };\n\n  var $set = function set(arrayLike /* , offset */) {\n    validate(this);\n    var offset = toOffset(arguments[1], 1);\n    var length = this.length;\n    var src = toObject(arrayLike);\n    var len = toLength(src.length);\n    var index = 0;\n    if (len + offset > length) throw RangeError(WRONG_LENGTH);\n    while (index < len) this[offset + index] = src[index++];\n  };\n\n  var $iterators = {\n    entries: function entries() {\n      return arrayEntries.call(validate(this));\n    },\n    keys: function keys() {\n      return arrayKeys.call(validate(this));\n    },\n    values: function values() {\n      return arrayValues.call(validate(this));\n    }\n  };\n\n  var isTAIndex = function (target, key) {\n    return isObject(target)\n      && target[TYPED_ARRAY]\n      && typeof key != 'symbol'\n      && key in target\n      && String(+key) == String(key);\n  };\n  var $getDesc = function getOwnPropertyDescriptor(target, key) {\n    return isTAIndex(target, key = toPrimitive(key, true))\n      ? propertyDesc(2, target[key])\n      : gOPD(target, key);\n  };\n  var $setDesc = function defineProperty(target, key, desc) {\n    if (isTAIndex(target, key = toPrimitive(key, true))\n      && isObject(desc)\n      && has(desc, 'value')\n      && !has(desc, 'get')\n      && !has(desc, 'set')\n      // TODO: add validation descriptor w/o calling accessors\n      && !desc.configurable\n      && (!has(desc, 'writable') || desc.writable)\n      && (!has(desc, 'enumerable') || desc.enumerable)\n    ) {\n      target[key] = desc.value;\n      return target;\n    } return dP(target, key, desc);\n  };\n\n  if (!ALL_CONSTRUCTORS) {\n    $GOPD.f = $getDesc;\n    $DP.f = $setDesc;\n  }\n\n  $export($export.S + $export.F * !ALL_CONSTRUCTORS, 'Object', {\n    getOwnPropertyDescriptor: $getDesc,\n    defineProperty: $setDesc\n  });\n\n  if (fails(function () { arrayToString.call({}); })) {\n    arrayToString = arrayToLocaleString = function toString() {\n      return arrayJoin.call(this);\n    };\n  }\n\n  var $TypedArrayPrototype$ = redefineAll({}, proto);\n  redefineAll($TypedArrayPrototype$, $iterators);\n  hide($TypedArrayPrototype$, ITERATOR, $iterators.values);\n  redefineAll($TypedArrayPrototype$, {\n    slice: $slice,\n    set: $set,\n    constructor: function () { /* noop */ },\n    toString: arrayToString,\n    toLocaleString: $toLocaleString\n  });\n  addGetter($TypedArrayPrototype$, 'buffer', 'b');\n  addGetter($TypedArrayPrototype$, 'byteOffset', 'o');\n  addGetter($TypedArrayPrototype$, 'byteLength', 'l');\n  addGetter($TypedArrayPrototype$, 'length', 'e');\n  dP($TypedArrayPrototype$, TAG, {\n    get: function () { return this[TYPED_ARRAY]; }\n  });\n\n  // eslint-disable-next-line max-statements\n  module.exports = function (KEY, BYTES, wrapper, CLAMPED) {\n    CLAMPED = !!CLAMPED;\n    var NAME = KEY + (CLAMPED ? 'Clamped' : '') + 'Array';\n    var GETTER = 'get' + KEY;\n    var SETTER = 'set' + KEY;\n    var TypedArray = global[NAME];\n    var Base = TypedArray || {};\n    var TAC = TypedArray && getPrototypeOf(TypedArray);\n    var FORCED = !TypedArray || !$typed.ABV;\n    var O = {};\n    var TypedArrayPrototype = TypedArray && TypedArray[PROTOTYPE];\n    var getter = function (that, index) {\n      var data = that._d;\n      return data.v[GETTER](index * BYTES + data.o, LITTLE_ENDIAN);\n    };\n    var setter = function (that, index, value) {\n      var data = that._d;\n      if (CLAMPED) value = (value = Math.round(value)) < 0 ? 0 : value > 0xff ? 0xff : value & 0xff;\n      data.v[SETTER](index * BYTES + data.o, value, LITTLE_ENDIAN);\n    };\n    var addElement = function (that, index) {\n      dP(that, index, {\n        get: function () {\n          return getter(this, index);\n        },\n        set: function (value) {\n          return setter(this, index, value);\n        },\n        enumerable: true\n      });\n    };\n    if (FORCED) {\n      TypedArray = wrapper(function (that, data, $offset, $length) {\n        anInstance(that, TypedArray, NAME, '_d');\n        var index = 0;\n        var offset = 0;\n        var buffer, byteLength, length, klass;\n        if (!isObject(data)) {\n          length = toIndex(data);\n          byteLength = length * BYTES;\n          buffer = new $ArrayBuffer(byteLength);\n        } else if (data instanceof $ArrayBuffer || (klass = classof(data)) == ARRAY_BUFFER || klass == SHARED_BUFFER) {\n          buffer = data;\n          offset = toOffset($offset, BYTES);\n          var $len = data.byteLength;\n          if ($length === undefined) {\n            if ($len % BYTES) throw RangeError(WRONG_LENGTH);\n            byteLength = $len - offset;\n            if (byteLength < 0) throw RangeError(WRONG_LENGTH);\n          } else {\n            byteLength = toLength($length) * BYTES;\n            if (byteLength + offset > $len) throw RangeError(WRONG_LENGTH);\n          }\n          length = byteLength / BYTES;\n        } else if (TYPED_ARRAY in data) {\n          return fromList(TypedArray, data);\n        } else {\n          return $from.call(TypedArray, data);\n        }\n        hide(that, '_d', {\n          b: buffer,\n          o: offset,\n          l: byteLength,\n          e: length,\n          v: new $DataView(buffer)\n        });\n        while (index < length) addElement(that, index++);\n      });\n      TypedArrayPrototype = TypedArray[PROTOTYPE] = create($TypedArrayPrototype$);\n      hide(TypedArrayPrototype, 'constructor', TypedArray);\n    } else if (!fails(function () {\n      TypedArray(1);\n    }) || !fails(function () {\n      new TypedArray(-1); // eslint-disable-line no-new\n    }) || !$iterDetect(function (iter) {\n      new TypedArray(); // eslint-disable-line no-new\n      new TypedArray(null); // eslint-disable-line no-new\n      new TypedArray(1.5); // eslint-disable-line no-new\n      new TypedArray(iter); // eslint-disable-line no-new\n    }, true)) {\n      TypedArray = wrapper(function (that, data, $offset, $length) {\n        anInstance(that, TypedArray, NAME);\n        var klass;\n        // `ws` module bug, temporarily remove validation length for Uint8Array\n        // https://github.com/websockets/ws/pull/645\n        if (!isObject(data)) return new Base(toIndex(data));\n        if (data instanceof $ArrayBuffer || (klass = classof(data)) == ARRAY_BUFFER || klass == SHARED_BUFFER) {\n          return $length !== undefined\n            ? new Base(data, toOffset($offset, BYTES), $length)\n            : $offset !== undefined\n              ? new Base(data, toOffset($offset, BYTES))\n              : new Base(data);\n        }\n        if (TYPED_ARRAY in data) return fromList(TypedArray, data);\n        return $from.call(TypedArray, data);\n      });\n      arrayForEach(TAC !== Function.prototype ? gOPN(Base).concat(gOPN(TAC)) : gOPN(Base), function (key) {\n        if (!(key in TypedArray)) hide(TypedArray, key, Base[key]);\n      });\n      TypedArray[PROTOTYPE] = TypedArrayPrototype;\n      if (!LIBRARY) TypedArrayPrototype.constructor = TypedArray;\n    }\n    var $nativeIterator = TypedArrayPrototype[ITERATOR];\n    var CORRECT_ITER_NAME = !!$nativeIterator\n      && ($nativeIterator.name == 'values' || $nativeIterator.name == undefined);\n    var $iterator = $iterators.values;\n    hide(TypedArray, TYPED_CONSTRUCTOR, true);\n    hide(TypedArrayPrototype, TYPED_ARRAY, NAME);\n    hide(TypedArrayPrototype, VIEW, true);\n    hide(TypedArrayPrototype, DEF_CONSTRUCTOR, TypedArray);\n\n    if (CLAMPED ? new TypedArray(1)[TAG] != NAME : !(TAG in TypedArrayPrototype)) {\n      dP(TypedArrayPrototype, TAG, {\n        get: function () { return NAME; }\n      });\n    }\n\n    O[NAME] = TypedArray;\n\n    $export($export.G + $export.W + $export.F * (TypedArray != Base), O);\n\n    $export($export.S, NAME, {\n      BYTES_PER_ELEMENT: BYTES\n    });\n\n    $export($export.S + $export.F * fails(function () { Base.of.call(TypedArray, 1); }), NAME, {\n      from: $from,\n      of: $of\n    });\n\n    if (!(BYTES_PER_ELEMENT in TypedArrayPrototype)) hide(TypedArrayPrototype, BYTES_PER_ELEMENT, BYTES);\n\n    $export($export.P, NAME, proto);\n\n    setSpecies(NAME);\n\n    $export($export.P + $export.F * FORCED_SET, NAME, { set: $set });\n\n    $export($export.P + $export.F * !CORRECT_ITER_NAME, NAME, $iterators);\n\n    if (!LIBRARY && TypedArrayPrototype.toString != arrayToString) TypedArrayPrototype.toString = arrayToString;\n\n    $export($export.P + $export.F * fails(function () {\n      new TypedArray(1).slice();\n    }), NAME, { slice: $slice });\n\n    $export($export.P + $export.F * (fails(function () {\n      return [1, 2].toLocaleString() != new TypedArray([1, 2]).toLocaleString();\n    }) || !fails(function () {\n      TypedArrayPrototype.toLocaleString.call([1, 2]);\n    })), NAME, { toLocaleString: $toLocaleString });\n\n    Iterators[NAME] = CORRECT_ITER_NAME ? $nativeIterator : $iterator;\n    if (!LIBRARY && !CORRECT_ITER_NAME) hide(TypedArrayPrototype, ITERATOR, $iterator);\n  };\n} else module.exports = function () { /* empty */ };\n", "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule normalizeWheel\n * @typechecks\n */\n\n'use strict';\n\nvar UserAgent_DEPRECATED = require('./UserAgent_DEPRECATED');\n\nvar isEventSupported = require('./isEventSupported');\n\n\n// Reasonable defaults\nvar PIXEL_STEP  = 10;\nvar LINE_HEIGHT = 40;\nvar PAGE_HEIGHT = 800;\n\n/**\n * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n * complicated, thus this doc is long and (hopefully) detailed enough to answer\n * your questions.\n *\n * If you need to react to the mouse wheel in a predictable way, this code is\n * like your bestest friend. * hugs *\n *\n * As of today, there are 4 DOM event types you can listen to:\n *\n *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n *\n * So what to do?  The is the best:\n *\n *   normalizeWheel.getEventType();\n *\n * In your event callback, use this code to get sane interpretation of the\n * deltas.  This code will return an object with properties:\n *\n *   spinX   -- normalized spin speed (use for zoom) - x plane\n *   spinY   -- \" - y plane\n *   pixelX  -- normalized distance (to pixels) - x plane\n *   pixelY  -- \" - y plane\n *\n * Wheel values are provided by the browser assuming you are using the wheel to\n * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n * significantly on different platforms and browsers, forgetting that you can\n * scroll at different speeds.  Some devices (like trackpads) emit more events\n * at smaller increments with fine granularity, and some emit massive jumps with\n * linear speed or acceleration.\n *\n * This code does its best to normalize the deltas for you:\n *\n *   - spin is trying to normalize how far the wheel was spun (or trackpad\n *     dragged).  This is super useful for zoom support where you want to\n *     throw away the chunky scroll steps on the PC and make those equal to\n *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n *     resolve a single slow step on a wheel to 1.\n *\n *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n *     get the crazy differences between browsers, but at least it'll be in\n *     pixels!\n *\n *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n *     should translate to positive value zooming IN, negative zooming OUT.\n *     This matches the newer 'wheel' event.\n *\n * Why are there spinX, spinY (or pixels)?\n *\n *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n *     with a mouse.  It results in side-scrolling in the browser by default.\n *\n *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n *\n *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n *     probably is by browsers in conjunction with fancy 3D controllers .. but\n *     you know.\n *\n * Implementation info:\n *\n * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n * average mouse:\n *\n *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n *\n * On the trackpad:\n *\n *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n *\n * On other/older browsers.. it's more complicated as there can be multiple and\n * also missing delta values.\n *\n * The 'wheel' event is more standard:\n *\n * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n *\n * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n * backward compatibility with older events.  Those other values help us\n * better normalize spin speed.  Example of what the browsers provide:\n *\n *                          | event.wheelDelta | event.detail\n *        ------------------+------------------+--------------\n *          Safari v5/OS X  |       -120       |       0\n *          Safari v5/Win7  |       -120       |       0\n *         Chrome v17/OS X  |       -120       |       0\n *         Chrome v17/Win7  |       -120       |       0\n *                IE9/Win7  |       -120       |   undefined\n *         Firefox v4/OS X  |     undefined    |       1\n *         Firefox v4/Win7  |     undefined    |       3\n *\n */\nfunction normalizeWheel(/*object*/ event) /*object*/ {\n  var sX = 0, sY = 0,       // spinX, spinY\n      pX = 0, pY = 0;       // pixelX, pixelY\n\n  // Legacy\n  if ('detail'      in event) { sY = event.detail; }\n  if ('wheelDelta'  in event) { sY = -event.wheelDelta / 120; }\n  if ('wheelDeltaY' in event) { sY = -event.wheelDeltaY / 120; }\n  if ('wheelDeltaX' in event) { sX = -event.wheelDeltaX / 120; }\n\n  // side scrolling on FF with DOMMouseScroll\n  if ( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {\n    sX = sY;\n    sY = 0;\n  }\n\n  pX = sX * PIXEL_STEP;\n  pY = sY * PIXEL_STEP;\n\n  if ('deltaY' in event) { pY = event.deltaY; }\n  if ('deltaX' in event) { pX = event.deltaX; }\n\n  if ((pX || pY) && event.deltaMode) {\n    if (event.deltaMode == 1) {          // delta in LINE units\n      pX *= LINE_HEIGHT;\n      pY *= LINE_HEIGHT;\n    } else {                             // delta in PAGE units\n      pX *= PAGE_HEIGHT;\n      pY *= PAGE_HEIGHT;\n    }\n  }\n\n  // Fall-back if spin cannot be determined\n  if (pX && !sX) { sX = (pX < 1) ? -1 : 1; }\n  if (pY && !sY) { sY = (pY < 1) ? -1 : 1; }\n\n  return { spinX  : sX,\n           spinY  : sY,\n           pixelX : pX,\n           pixelY : pY };\n}\n\n\n/**\n * The best combination if you prefer spinX + spinY normalization.  It favors\n * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n * 'wheel' event, making spin speed determination impossible.\n */\nnormalizeWheel.getEventType = function() /*string*/ {\n  return (UserAgent_DEPRECATED.firefox())\n           ? 'DOMMouseScroll'\n           : (isEventSupported('wheel'))\n               ? 'wheel'\n               : 'mousewheel';\n};\n\nmodule.exports = normalizeWheel;\n", "module.exports = function(originalModule) {\n\tif (!originalModule.webpackPolyfill) {\n\t\tvar module = Object.create(originalModule);\n\t\t// module.parent = undefined by default\n\t\tif (!module.children) module.children = [];\n\t\tObject.defineProperty(module, \"loaded\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.l;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"id\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.i;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"exports\", {\n\t\t\tenumerable: true\n\t\t});\n\t\tmodule.webpackPolyfill = 1;\n\t}\n\treturn module;\n};\n", "'use strict';\nvar global = require('./_global');\nvar DESCRIPTORS = require('./_descriptors');\nvar LIBRARY = require('./_library');\nvar $typed = require('./_typed');\nvar hide = require('./_hide');\nvar redefineAll = require('./_redefine-all');\nvar fails = require('./_fails');\nvar anInstance = require('./_an-instance');\nvar toInteger = require('./_to-integer');\nvar toLength = require('./_to-length');\nvar toIndex = require('./_to-index');\nvar gOPN = require('./_object-gopn').f;\nvar dP = require('./_object-dp').f;\nvar arrayFill = require('./_array-fill');\nvar setToStringTag = require('./_set-to-string-tag');\nvar ARRAY_BUFFER = 'ArrayBuffer';\nvar DATA_VIEW = 'DataView';\nvar PROTOTYPE = 'prototype';\nvar WRONG_LENGTH = 'Wrong length!';\nvar WRONG_INDEX = 'Wrong index!';\nvar $ArrayBuffer = global[ARRAY_BUFFER];\nvar $DataView = global[DATA_VIEW];\nvar Math = global.Math;\nvar RangeError = global.RangeError;\n// eslint-disable-next-line no-shadow-restricted-names\nvar Infinity = global.Infinity;\nvar BaseBuffer = $ArrayBuffer;\nvar abs = Math.abs;\nvar pow = Math.pow;\nvar floor = Math.floor;\nvar log = Math.log;\nvar LN2 = Math.LN2;\nvar BUFFER = 'buffer';\nvar BYTE_LENGTH = 'byteLength';\nvar BYTE_OFFSET = 'byteOffset';\nvar $BUFFER = DESCRIPTORS ? '_b' : BUFFER;\nvar $LENGTH = DESCRIPTORS ? '_l' : BYTE_LENGTH;\nvar $OFFSET = DESCRIPTORS ? '_o' : BYTE_OFFSET;\n\n// IEEE754 conversions based on https://github.com/feross/ieee754\nfunction packIEEE754(value, mLen, nBytes) {\n  var buffer = new Array(nBytes);\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? pow(2, -24) - pow(2, -77) : 0;\n  var i = 0;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  var e, m, c;\n  value = abs(value);\n  // eslint-disable-next-line no-self-compare\n  if (value != value || value === Infinity) {\n    // eslint-disable-next-line no-self-compare\n    m = value != value ? 1 : 0;\n    e = eMax;\n  } else {\n    e = floor(log(value) / LN2);\n    if (value * (c = pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * pow(2, 1 - eBias);\n    }\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * pow(2, eBias - 1) * pow(2, mLen);\n      e = 0;\n    }\n  }\n  for (; mLen >= 8; buffer[i++] = m & 255, m /= 256, mLen -= 8);\n  e = e << mLen | m;\n  eLen += mLen;\n  for (; eLen > 0; buffer[i++] = e & 255, e /= 256, eLen -= 8);\n  buffer[--i] |= s * 128;\n  return buffer;\n}\nfunction unpackIEEE754(buffer, mLen, nBytes) {\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = eLen - 7;\n  var i = nBytes - 1;\n  var s = buffer[i--];\n  var e = s & 127;\n  var m;\n  s >>= 7;\n  for (; nBits > 0; e = e * 256 + buffer[i], i--, nBits -= 8);\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n  for (; nBits > 0; m = m * 256 + buffer[i], i--, nBits -= 8);\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : s ? -Infinity : Infinity;\n  } else {\n    m = m + pow(2, mLen);\n    e = e - eBias;\n  } return (s ? -1 : 1) * m * pow(2, e - mLen);\n}\n\nfunction unpackI32(bytes) {\n  return bytes[3] << 24 | bytes[2] << 16 | bytes[1] << 8 | bytes[0];\n}\nfunction packI8(it) {\n  return [it & 0xff];\n}\nfunction packI16(it) {\n  return [it & 0xff, it >> 8 & 0xff];\n}\nfunction packI32(it) {\n  return [it & 0xff, it >> 8 & 0xff, it >> 16 & 0xff, it >> 24 & 0xff];\n}\nfunction packF64(it) {\n  return packIEEE754(it, 52, 8);\n}\nfunction packF32(it) {\n  return packIEEE754(it, 23, 4);\n}\n\nfunction addGetter(C, key, internal) {\n  dP(C[PROTOTYPE], key, { get: function () { return this[internal]; } });\n}\n\nfunction get(view, bytes, index, isLittleEndian) {\n  var numIndex = +index;\n  var intIndex = toIndex(numIndex);\n  if (intIndex + bytes > view[$LENGTH]) throw RangeError(WRONG_INDEX);\n  var store = view[$BUFFER]._b;\n  var start = intIndex + view[$OFFSET];\n  var pack = store.slice(start, start + bytes);\n  return isLittleEndian ? pack : pack.reverse();\n}\nfunction set(view, bytes, index, conversion, value, isLittleEndian) {\n  var numIndex = +index;\n  var intIndex = toIndex(numIndex);\n  if (intIndex + bytes > view[$LENGTH]) throw RangeError(WRONG_INDEX);\n  var store = view[$BUFFER]._b;\n  var start = intIndex + view[$OFFSET];\n  var pack = conversion(+value);\n  for (var i = 0; i < bytes; i++) store[start + i] = pack[isLittleEndian ? i : bytes - i - 1];\n}\n\nif (!$typed.ABV) {\n  $ArrayBuffer = function ArrayBuffer(length) {\n    anInstance(this, $ArrayBuffer, ARRAY_BUFFER);\n    var byteLength = toIndex(length);\n    this._b = arrayFill.call(new Array(byteLength), 0);\n    this[$LENGTH] = byteLength;\n  };\n\n  $DataView = function DataView(buffer, byteOffset, byteLength) {\n    anInstance(this, $DataView, DATA_VIEW);\n    anInstance(buffer, $ArrayBuffer, DATA_VIEW);\n    var bufferLength = buffer[$LENGTH];\n    var offset = toInteger(byteOffset);\n    if (offset < 0 || offset > bufferLength) throw RangeError('Wrong offset!');\n    byteLength = byteLength === undefined ? bufferLength - offset : toLength(byteLength);\n    if (offset + byteLength > bufferLength) throw RangeError(WRONG_LENGTH);\n    this[$BUFFER] = buffer;\n    this[$OFFSET] = offset;\n    this[$LENGTH] = byteLength;\n  };\n\n  if (DESCRIPTORS) {\n    addGetter($ArrayBuffer, BYTE_LENGTH, '_l');\n    addGetter($DataView, BUFFER, '_b');\n    addGetter($DataView, BYTE_LENGTH, '_l');\n    addGetter($DataView, BYTE_OFFSET, '_o');\n  }\n\n  redefineAll($DataView[PROTOTYPE], {\n    getInt8: function getInt8(byteOffset) {\n      return get(this, 1, byteOffset)[0] << 24 >> 24;\n    },\n    getUint8: function getUint8(byteOffset) {\n      return get(this, 1, byteOffset)[0];\n    },\n    getInt16: function getInt16(byteOffset /* , littleEndian */) {\n      var bytes = get(this, 2, byteOffset, arguments[1]);\n      return (bytes[1] << 8 | bytes[0]) << 16 >> 16;\n    },\n    getUint16: function getUint16(byteOffset /* , littleEndian */) {\n      var bytes = get(this, 2, byteOffset, arguments[1]);\n      return bytes[1] << 8 | bytes[0];\n    },\n    getInt32: function getInt32(byteOffset /* , littleEndian */) {\n      return unpackI32(get(this, 4, byteOffset, arguments[1]));\n    },\n    getUint32: function getUint32(byteOffset /* , littleEndian */) {\n      return unpackI32(get(this, 4, byteOffset, arguments[1])) >>> 0;\n    },\n    getFloat32: function getFloat32(byteOffset /* , littleEndian */) {\n      return unpackIEEE754(get(this, 4, byteOffset, arguments[1]), 23, 4);\n    },\n    getFloat64: function getFloat64(byteOffset /* , littleEndian */) {\n      return unpackIEEE754(get(this, 8, byteOffset, arguments[1]), 52, 8);\n    },\n    setInt8: function setInt8(byteOffset, value) {\n      set(this, 1, byteOffset, packI8, value);\n    },\n    setUint8: function setUint8(byteOffset, value) {\n      set(this, 1, byteOffset, packI8, value);\n    },\n    setInt16: function setInt16(byteOffset, value /* , littleEndian */) {\n      set(this, 2, byteOffset, packI16, value, arguments[2]);\n    },\n    setUint16: function setUint16(byteOffset, value /* , littleEndian */) {\n      set(this, 2, byteOffset, packI16, value, arguments[2]);\n    },\n    setInt32: function setInt32(byteOffset, value /* , littleEndian */) {\n      set(this, 4, byteOffset, packI32, value, arguments[2]);\n    },\n    setUint32: function setUint32(byteOffset, value /* , littleEndian */) {\n      set(this, 4, byteOffset, packI32, value, arguments[2]);\n    },\n    setFloat32: function setFloat32(byteOffset, value /* , littleEndian */) {\n      set(this, 4, byteOffset, packF32, value, arguments[2]);\n    },\n    setFloat64: function setFloat64(byteOffset, value /* , littleEndian */) {\n      set(this, 8, byteOffset, packF64, value, arguments[2]);\n    }\n  });\n} else {\n  if (!fails(function () {\n    $ArrayBuffer(1);\n  }) || !fails(function () {\n    new $ArrayBuffer(-1); // eslint-disable-line no-new\n  }) || fails(function () {\n    new $ArrayBuffer(); // eslint-disable-line no-new\n    new $ArrayBuffer(1.5); // eslint-disable-line no-new\n    new $ArrayBuffer(NaN); // eslint-disable-line no-new\n    return $ArrayBuffer.name != ARRAY_BUFFER;\n  })) {\n    $ArrayBuffer = function ArrayBuffer(length) {\n      anInstance(this, $ArrayBuffer);\n      return new BaseBuffer(toIndex(length));\n    };\n    var ArrayBufferProto = $ArrayBuffer[PROTOTYPE] = BaseBuffer[PROTOTYPE];\n    for (var keys = gOPN(BaseBuffer), j = 0, key; keys.length > j;) {\n      if (!((key = keys[j++]) in $ArrayBuffer)) hide($ArrayBuffer, key, BaseBuffer[key]);\n    }\n    if (!LIBRARY) ArrayBufferProto.constructor = $ArrayBuffer;\n  }\n  // iOS Safari 7.x bug\n  var view = new $DataView(new $ArrayBuffer(2));\n  var $setInt8 = $DataView[PROTOTYPE].setInt8;\n  view.setInt8(0, 2147483648);\n  view.setInt8(1, 2147483649);\n  if (view.getInt8(0) || !view.getInt8(1)) redefineAll($DataView[PROTOTYPE], {\n    setInt8: function setInt8(byteOffset, value) {\n      $setInt8.call(this, byteOffset, value << 24 >> 24);\n    },\n    setUint8: function setUint8(byteOffset, value) {\n      $setInt8.call(this, byteOffset, value << 24 >> 24);\n    }\n  }, true);\n}\nsetToStringTag($ArrayBuffer, ARRAY_BUFFER);\nsetToStringTag($DataView, DATA_VIEW);\nhide($DataView[PROTOTYPE], $typed.VIEW, true);\nexports[ARRAY_BUFFER] = $ArrayBuffer;\nexports[DATA_VIEW] = $DataView;\n"], "sourceRoot": ""}