{"version": 3, "sources": ["webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/tylko-camera.js", "webpack:///./node_modules/@babel/runtime/helpers/toArray.js", "webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/tylko-camera-autozoom.js", "webpack:///../app/@tylko/cape-entrypoints/experimental/cape-camera/tylko-camera-orbit-perspective.js"], "names": ["__webpack_require__", "d", "__webpack_exports__", "tylkoCamera", "three__WEBPACK_IMPORTED_MODULE_0__", "_tylko_camera_orbit_perspective__WEBPACK_IMPORTED_MODULE_1__", "view", "scene", "this", "fov", "range", "min", "max", "target", "x", "y", "z", "position", "geometryOffset", "left", "right", "top", "bottom", "front", "back", "geometryBox", "THREE", "camera", "controls", "polarAngle", "Math", "PI", "azimuthAngle", "dynamicTarget", "init", "set", "add", "new_theta", "new_phi", "update", "noAutoZoom", "noLifeZoom", "noTransitionAnimation", "noSnap", "noSnapReal", "updatePoints", "o", "coor", "points", "updateGeometry", "new_target", "updateAspect", "aspect", "updateProjectionMatrix", "updateParams", "matrix", "elements", "updateCamera", "parseInt", "near", "far", "setDefaultfView", "noZoom", "noPan", "noRotate", "mouseButtons", "ORBIT", "LEFT", "ZOOM", "MIDDLE", "PAN", "RIGHT", "touchMode", "ZOOMPAN", "setShelfViewFinal", "console", "log", "animationStepParameters", "distance", "snapTo", "theta", "phi", "screenEdgeOffset", "undefined", "setPipViewFinal", "setComponentViewFinal", "center", "side", "setShelfView", "setComponent<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "view_name", "updateCameraRange", "setCamSettings", "params", "getCamSettings", "rotation", "arrayWithHoles", "iterableToArray", "nonIterableRest", "_toArray", "arr", "module", "exports", "tylkoCameraAutoZoom", "frustum", "horizontalPlane", "verticalPlane", "cameraPlanesOffset", "tempPoint1", "tempPoint2", "deg2rad", "calculateNearFarPosition", "pointDistances", "for<PERSON>ach", "p", "push", "distanceTo", "camDistance", "maxDistance", "apply", "updatePlanes", "setFromMatrix", "multiplyMatrices", "projectionMatrix", "matrixWorldInverse", "planes", "projectPoint", "setFromCoplanarPoints", "calculateDistances", "point", "angle", "camPos", "targetPos", "planeA", "planeB", "distTop", "distTarget", "distPointToCam", "distTargetToCam", "distZoom", "tan", "calculateZoom", "_this", "zoomDistances", "object", "dom<PERSON>lement", "document", "enabled", "directionLock", "noYPan", "targetOffset", "zoomSpeed", "rotateSpeed", "targetDistance", "zoomRange", "Infinity", "geometryFixed", "scope", "EPS", "rotateStart", "rotateEnd", "<PERSON><PERSON><PERSON><PERSON>", "panStart", "panEnd", "panDelta", "panOffset", "offset", "dolly<PERSON><PERSON><PERSON>", "dollyEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snapped", "zoom", "newZoom", "newTheta", "newPhi", "newTarget", "thetaDelta", "phi<PERSON><PERSON><PERSON>", "scale", "pan", "tempPoint", "animationParameters", "counter", "thetaDiff", "phiDiff", "zoomDiff", "STATE", "NONE", "ROTATE", "DOLLY", "TOUCH_ROTATE", "TOUCH_DOLLY", "TOUCH_PAN", "TOUCH_ZOOMPAN", "ANIMATE", "state", "quat", "setFromUnitVectors", "up", "quatInverse", "clone", "inverse", "changeEvent", "type", "animateEvent", "panLeft", "te", "multiplyScalar", "panUp", "deltaX", "deltaY", "element", "body", "sub", "length", "clientHeight", "dollyIn", "dollyScale", "getZoomScale", "dollyOut", "dist", "checkRange", "distT", "distC", "calculateAcutalCamParams", "copy", "applyQuaternion", "atan2", "sqrt", "calculateStaticCamParams", "snap", "getClosestSnap", "checkCamDiff", "updateZoom", "calculateRealZoom", "pixelWidth", "clientWidth", "pixelHeight", "distX", "distY", "project", "filter", "isFinite", "toConsumableArray_default", "concat", "abs", "restrictCamParams", "testTheta", "testPhi", "testZoom", "testTarget", "t", "tr", "calculateAnimationFirstStep", "animationSpeed", "round", "calculateAnimationNextStep", "calculateDynamicCamParams", "setCamParams", "sin", "cos", "lookAt", "dispatchEvent", "getPolarAngle", "getAzimuthalAngle", "pow", "currentPhi", "currentTheta", "availbleSnappingStates", "closest", "diff", "optionDiff", "_availbleSnappingStat", "_availbleSnappingStat2", "toArray_default", "slice", "option", "onMouseDown", "event", "preventDefault", "button", "clientX", "clientY", "addEventListener", "onMouseMove", "onMouseUp", "subVectors", "removeEventListener", "onMouseWheel", "stopPropagation", "delta", "wheelDelta", "detail", "touchstart", "touches", "pageX", "pageY", "dx", "dy", "lockDirection", "setY", "setX", "touchmove", "touchend", "prototype", "Object", "create", "constructor"], "mappings": "wGAAAA,EAAAC,EAAAC,EAAA,sBAAAC,IAAA,IAAAC,EAAAJ,EAAA,YAAAK,EAAAL,EAAA,QAgBO,SAASG,EAAYG,EAAMC,GAChCC,KAAKC,IAAM,GACXD,KAAKE,MAAQ,CAAEC,IAAK,EAAGC,IAAK,KAC5BJ,KAAKK,OAAS,CAAEC,EAAG,EAAGC,EAAG,EAAGC,EAAG,GAE/BR,KAAKS,SAAW,CAAEH,EAAG,EAAGC,EAAG,IAAMC,EAAG,KACpCR,KAAKU,eAAiB,CAACC,KAAM,GAAIC,MAAO,EAAGC,IAAK,EAAGC,OAAQ,GAAIC,MAAO,EAAGC,KAAM,GAE/EhB,KAAKiB,YAAc,IAAIC,UAEvBlB,KAAKmB,OAAS,IAAID,uBAChBlB,KAAKC,IACL,EACAD,KAAKE,MAAMC,IACXH,KAAKE,MAAME,KAEbJ,KAAKoB,SAAW,IAAIF,mBAAoBlB,KAAKmB,OAAQrB,EAAMC,GAC3DC,KAAKoB,SAASC,WAAa,CAAElB,IAAKmB,KAAKC,GAAK,EAAGnB,IAAKkB,KAAKC,GAAK,GAC9DvB,KAAKoB,SAASI,aAAe,CAAErB,KAAMmB,KAAKC,GAAK,EAAGnB,IAAKkB,KAAKC,GAAK,GACjEvB,KAAKyB,cAAgB,KAErBzB,KAAK0B,KAAO,WACV1B,KAAKmB,OAAOV,SAASkB,IAAI,IAAM,IAAM,KACrC3B,KAAKmB,OAAOS,IAAI,IAAIV,gBAAiB,SAAU,IAC/ClB,KAAKoB,SAASf,OAAOsB,IAAI3B,KAAKK,OAAOC,EAAGN,KAAKK,OAAOE,EAAGP,KAAKK,OAAOG,GACnER,KAAKoB,SAASS,WAAa,GAC3B7B,KAAKoB,SAASU,QAAU,IACxB9B,KAAKoB,SAASW,SACd/B,KAAKoB,SAASY,WAAa,MAC3BhC,KAAKoB,SAASa,WAAa,KAC3BjC,KAAKoB,SAASc,sBAAwB,MACtClC,KAAKoB,SAASe,OAAS,MACvBnC,KAAKoB,SAASgB,WAAa,OAG7BpC,KAAKqC,aAAe,SAASlC,EAAKC,GAChC,IAAIkC,EAAItC,KAAKU,eACb,IAAI6B,EAAO,CACTpC,EAAIG,EAAIgC,EAAE3B,KACVP,EAAIE,EAAIgC,EAAE1B,MACVT,EAAII,EAAI+B,EAAExB,OACVV,EAAIG,EAAI+B,EAAEzB,IACVV,EAAIK,EAAI8B,EAAEtB,KACVZ,EAAII,EAAI8B,EAAEvB,OAEZf,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,IACnDvC,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,IACnDvC,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,IACnDvC,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,IACnDvC,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,IACnDvC,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,IACnDvC,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,IACnDvC,KAAKoB,SAASoB,OAAO,GAAGb,IAAIY,EAAK,GAAIA,EAAK,GAAIA,EAAK,KAGrDvC,KAAKyC,eAAiB,SAAStC,EAAKC,GAOlC,GAAIJ,KAAKyB,cAAe,CACtBzB,KAAKK,OAAOE,EAAI,GAAMH,EAAIG,EAAI,GAC9BP,KAAKoB,SAASsB,WAAWf,IAAI3B,KAAKK,OAAOC,EAAGN,KAAKK,OAAOE,EAAGP,KAAKK,OAAOG,GAGzER,KAAKqC,aAAalC,EAAKC,GAEvBJ,KAAKoB,SAASW,UAKhB/B,KAAK2C,aAAe,SAAUC,GAC5B5C,KAAKmB,OAAOyB,OAASA,EACrB5C,KAAKmB,OAAO0B,0BAKd7C,KAAK+B,OAAS,SAAUa,GACtB5C,KAAK2C,aAAaC,GAClB5C,KAAK8C,gBAIP9C,KAAK8C,aAAe,WAClB,IAAIC,EAAS/C,KAAKmB,OAAO4B,OAAOC,SAEhChD,KAAKS,SAAW,CAAEH,EAAGyC,EAAO,IAAKxC,EAAGwC,EAAO,IAAKvC,EAAGuC,EAAO,KAE1D/C,KAAKK,OAASL,KAAKoB,SAASf,QAK9BL,KAAKiD,aAAe,WAClBjD,KAAKmB,OAAOlB,IAAMiD,SAASlD,KAAKC,KAChCD,KAAKmB,OAAOgC,KAAOD,SAASlD,KAAKE,MAAMC,KACvCH,KAAKmB,OAAOiC,IAAMF,SAASlD,KAAKE,MAAME,KACtCJ,KAAKmB,OAAOV,SAASkB,IAAI3B,KAAKS,SAASH,EAAGN,KAAKS,SAASF,EAAGP,KAAKS,SAASD,GAEzER,KAAKoB,SAASf,OAAOsB,IAAI3B,KAAKK,OAAOC,EAAGN,KAAKK,OAAOE,EAAGP,KAAKK,OAAOG,GACnER,KAAKoB,SAASW,UAGhB/B,KAAKqD,gBAAkB,WACrBrD,KAAKoB,SAASkC,OAAS,MACvBtD,KAAKoB,SAASmC,MAAQ,MACtBvD,KAAKoB,SAASoC,SAAW,MACzBxD,KAAKoB,SAASY,WAAa,MAC3BhC,KAAKoB,SAASa,WAAa,MAC3BjC,KAAKoB,SAASqC,aAAe,CAAEC,MAAOxC,WAAYyC,KAAMC,KAAM1C,WAAY2C,OAAQC,IAAK5C,WAAY6C,OACnG/D,KAAKoB,SAAS4C,UAAY,CAAEN,MAAO,EAAGE,KAAM,EAAGE,IAAK,EAAGG,QAAS,IAGlEjE,KAAKkE,kBAAoB,SAAS/D,EAAKC,GACrC+D,QAAQC,IAAI,mBACZpE,KAAKoB,SAASc,sBAAwB,KACtClC,KAAKoB,SAASkC,OAAS,KACvBtD,KAAKoB,SAASmC,MAAQ,KACtBvD,KAAKoB,SAASoC,SAAW,MACzBxD,KAAKoB,SAASa,WAAa,KAC3BjC,KAAKoB,SAASY,WAAa,MAC3BhC,KAAKoB,SAASiD,wBAAwBC,SAAW,IACjDtE,KAAKoB,SAASI,aAAe,CAAErB,KAAMmB,KAAKC,GAAK,EAAI,EAAGnB,IAAKkB,KAAKC,GAAK,EAAI,GACzEvB,KAAKmB,OAAOgC,KAAO,GACnBnD,KAAKmB,OAAOiC,IAAM,IAClBpD,KAAKmB,OAAO0B,yBACZ7C,KAAKoB,SAASqC,aAAe,CACzBC,MAAOxC,WAAYyC,KAAMC,KAAM1C,WAAY2C,OAAQC,IAAK5C,WAAY6C,OACxE/D,KAAKoB,SAAS4C,UAAY,CACtBN,MAAO,EAAGE,KAAM,EAAGE,IAAK,EAAGG,QAAS,GACxCjE,KAAKoB,SAASmD,OAAS,CACnB,CAAEC,MAAO,IAAMC,IAAK,KACpB,CAAED,OAAQ,IAAMC,IAAK,KACrB,CAAED,MAAO,EAAGC,IAAK,MAErBzE,KAAKU,eAAiB,CAACC,KAAM,GAAIC,MAAO,GAAIC,IAAK,GAAIC,OAAQ,GAAIC,MAAO,EAAGC,KAAM,GACjFhB,KAAKoB,SAASsD,iBAAmB,CAAC/D,KAAM,GAAIC,MAAO,GAAIC,IAAK,EAAGC,OAAQ,GACvE,GAAIX,IAAQwE,UAAW3E,KAAKyC,eAAetC,EAAKC,GAChDJ,KAAKoB,SAASc,sBAAwB,OAGxClC,KAAK4E,gBAAkB,SAASzE,EAAKC,GAIjCJ,KAAKoB,SAASe,OAAS,KACvBnC,KAAKoB,SAASgB,WAAa,KAC3BpC,KAAKoB,SAASc,sBAAwB,KACtClC,KAAKoB,SAASa,WAAa,KAK3BjC,KAAKmB,OAAOyB,OAAS,IAAI,IACzB5C,KAAKmB,OAAOgC,KAAO,GACnBnD,KAAKmB,OAAOiC,IAAM,IAClBpD,KAAKmB,OAAO0B,yBAGZ7C,KAAKoB,SAASsD,iBAAmB,CAAC/D,KAAM,EAAGC,MAAO,EAAGC,IAAK,EAAGC,OAAQ,GAMrEd,KAAKoB,SAASS,UAAY,EAC1B7B,KAAKoB,SAASU,QAAU,IACxB,GAAI3B,IAAQwE,UAAW,CACrB3E,KAAKqC,aAAalC,EAAKC,GAGzBJ,KAAKoB,SAASW,UAIhB/B,KAAK6E,sBAAwB,SAAS1E,EAAKC,EAAK0E,GAC9CA,EAASA,IAAWH,UAAYxE,GAAOC,EAAMD,GAAO,EAAI2E,EACxD,IAAIC,EAAOzD,KAAKlB,IAAI0E,EAAS3E,EAAI,GAAIC,EAAI,GAAK0E,GAC9C9E,KAAKoB,SAASc,sBAAwB,KACtClC,KAAKoB,SAASa,WAAa,KAC3BjC,KAAKoB,SAASY,WAAa,KAE3BhC,KAAKmB,OAAOgC,KAAO,GACnBnD,KAAKmB,OAAOiC,IAAM,IAClBpD,KAAKmB,OAAO0B,yBAEZ7C,KAAKoB,SAASsD,iBAAmB,CAAC/D,KAAM,GAAIC,MAAO,GAAIC,IAAK,EAAGC,OAAQ,GACvEd,KAAKU,eAAiB,CAACC,KAAM,EAAGC,MAAO,EAAGC,IAAK,GAAIC,OAAQ,GAAIC,MAAO,EAAGC,KAAM,GAC/EhB,KAAKoB,SAASf,OAAOC,EAAIwE,EAAS9E,KAAKU,eAAeE,MAAQZ,KAAKU,eAAeC,KAClFX,KAAKoB,SAASf,OAAOE,GACjBJ,EAAI,GAAKC,EAAI,IACT,GACAJ,KAAKU,eAAeG,IAClBb,KAAKU,eAAeI,QAE9Bd,KAAKoB,SAASmD,OAAS,CACnB,CAAEC,MAAO,EAAGC,IAAK,MAErBzE,KAAKoB,SAASW,SACd/B,KAAKoB,SAASY,WAAa,MAG3BhC,KAAKqC,aACD,CAAC/B,EAAGwE,EAASC,EAAMxE,EAAGJ,EAAI,GAAIK,EAAGL,EAAI,IACrC,CAACG,EAAGwE,EAASC,EAAMxE,EAAGH,EAAI,GAAII,EAAGJ,EAAI,KAMzCJ,KAAKoB,SAASW,SAId/B,KAAKoB,SAASkC,OAAS,KACvBtD,KAAKoB,SAASmC,MAAQ,MAEtBvD,KAAKoB,SAASoC,SAAW,KACzBxD,KAAKoB,SAASY,WAAa,KAC3BhC,KAAKoB,SAASqC,aAAe,CAAEK,IAAK5C,WAAYyC,KAAMC,KAAM1C,WAAY2C,OAAQH,MAAOxC,WAAY6C,OACnG/D,KAAKoB,SAAS4C,UAAY,CAAEN,MAAO,EAAGE,KAAM,EAAGE,IAAK,EAAGG,QAAS,GAChEjE,KAAKoB,SAASc,sBAAwB,OAkB1ClC,KAAKgF,aAAe,SAAS7E,EAAKC,GAChC+D,QAAQC,IAAI,oBACZpE,KAAKoB,SAASkC,OAAS,KACvBtD,KAAKoB,SAASmC,MAAQ,KACtBvD,KAAKoB,SAASoC,SAAW,MACzBxD,KAAKoB,SAASY,WAAa,MAC3BhC,KAAKoB,SAASqC,aAAe,CAAEC,MAAOxC,WAAYyC,KAAMC,KAAM1C,WAAY2C,OAAQC,IAAK5C,WAAY6C,OACnG/D,KAAKoB,SAAS4C,UAAY,CAAEN,MAAO,EAAGE,KAAM,EAAGE,IAAK,EAAGG,QAAS,GAChE,GAAI9D,IAAQwE,UAAW3E,KAAKyC,eAAetC,EAAKC,IAGlDJ,KAAKiF,iBAAmB,SAAS9E,EAAKC,GACpC+D,QAAQC,IAAI,uBACZpE,KAAKoB,SAASkC,OAAS,KACvBtD,KAAKoB,SAASmC,MAAQ,KACtBvD,KAAKoB,SAASoC,SAAW,MACzBxD,KAAKoB,SAASY,WAAa,MAC3BhC,KAAKoB,SAASqC,aAAe,CAAEC,MAAOxC,WAAYyC,KAAMC,KAAM1C,WAAY2C,OAAQC,IAAK5C,WAAY6C,OACnG/D,KAAKoB,SAAS4C,UAAY,CAAEN,MAAO,EAAGE,KAAM,EAAGE,IAAK,EAAGG,QAAS,GAMhEjE,KAAKoB,SAASS,UAAY,EAC1B7B,KAAKoB,SAASU,QAAUR,KAAKC,GAAK,EAGlC,GAAIpB,IAAQwE,UAAW3E,KAAKyC,eAAetC,EAAKC,IAGlDJ,KAAKkF,QAAU,SAAUC,GACvB,OAAQA,GACN,IAAK,QACHnF,KAAKoB,SAASU,QAAUR,KAAKC,GAAK,EAClCvB,KAAKoB,SAASS,UAAY,EAC1B,MACF,IAAK,OACH7B,KAAKoB,SAASU,QAAU,IACxB9B,KAAKoB,SAASS,WAAa,GAC3B,MACF,IAAK,gBACH7B,KAAKoB,SAASU,QAAUR,KAAKC,GAAK,EAClCvB,KAAKoB,SAASS,WAAaP,KAAKC,GAAK,EACrC,MACF,IAAK,QACHvB,KAAKoB,SAASU,QAAU,GACxB9B,KAAKoB,SAASS,UAAY,EAC1B,MACF,IAAK,iBACH7B,KAAKoB,SAASU,QAAUR,KAAKC,GAAK,EAClCvB,KAAKoB,SAASS,UAAYP,KAAKC,GAAK,EACpC,MACF,IAAK,MACHvB,KAAKoB,SAASU,QAAU,GACxB9B,KAAKoB,SAASS,UAAY,EAC1B,MACF,IAAK,eACH7B,KAAKoB,SAASU,QAAU,EACxB9B,KAAKoB,SAASS,UAAY,EAC1B,MAEJ7B,KAAKoB,SAASgB,WAAa,KAC3BpC,KAAKoB,SAASf,OAAOsB,IAAI,EAAG,EAAG,GAC/B3B,KAAKoB,SAASgE,oBACdpF,KAAKoB,SAASW,UAIhB/B,KAAKqF,eAAiB,SAAUC,GAC9BtF,KAAKC,IAAMqF,EAAOrF,IAClBD,KAAKE,MAAQoF,EAAOpF,MACpBF,KAAKK,OAASiF,EAAOjF,OACrBL,KAAKwE,MAAQxE,KAAKoB,SAASS,UAC3B7B,KAAKyE,IAAMzE,KAAKoB,SAASU,QACzB9B,KAAKS,SAAW6E,EAAO7E,SAGvBT,KAAKiD,gBAGPjD,KAAKuF,eAAiB,WAEpB,MAAO,CACLtF,IAAKD,KAAKC,IACVC,MAAOF,KAAKE,MACZG,OAAQL,KAAKK,OACbmF,SAAUxF,KAAKwF,SACf/E,SAAUT,KAAKS,WAInBT,KAAK0B,gCC/VP,IAAA+D,EAAqBjG,EAAQ,QAE7B,IAAAkG,EAAsBlG,EAAQ,QAE9B,IAAAmG,EAAsBnG,EAAQ,QAE9B,SAAAoG,EAAAC,GACA,OAAAJ,EAAAI,IAAAH,EAAAG,IAAAF,IAGAG,EAAAC,QAAAH,+ICFO,SAASI,EAAoBjG,GAChCC,KAAKwC,OAAS,CAAG,IAAItB,cAAe,KAAM,KAAM,KAAQ,IAAIA,aAAc,IAAM,IAAM,MACtFlB,KAAKiG,QAAU,IAAI/E,aACnBlB,KAAKkG,gBAAkB,IAAIhF,WAC3BlB,KAAKmG,cAAgB,IAAIjF,WACzBlB,KAAKoG,mBAAqB,GAE1B,IAAIC,EAAa,IAAInF,aACrB,IAAIoF,EAAa,IAAIpF,aAErB,IAAIqF,EAAUjF,KAAKC,GAAG,IA6BtBvB,KAAKwG,yBAA2B,SAASrF,EAAQC,GAC7C,IAAIqF,EAAiB,GACrBzG,KAAKwC,OAAOkE,QAAQ,SAAAC,GAChBF,EAAeG,KAAKD,EAAEE,WAAWzF,EAASf,WAE9C,IAAIyG,EAAc3F,EAAOV,SAASoG,WAAWzF,EAASf,QACtD,IAAI0G,EAAczF,KAAKlB,IAAL4G,MAAA1F,KAAYmF,GAC9B,MAAO,CACHnF,KAAKlB,IAAI,EAAG0G,EAAcC,EAAc/G,KAAKoG,oBAC5CU,EAAcC,EAAc/G,KAAKoG,qBAI1CpG,KAAKqC,aAAe,SAASG,GACzBxC,KAAKwC,OAASA,GAYlBxC,KAAKiH,aAAe,SAAS9F,EAAQC,GACjCpB,KAAKiG,QAAQiB,eAAe,IAAIhG,cAAgBiG,iBAAkBhG,EAAOiG,iBAAkBjG,EAAOkG,qBAClGrH,KAAKiG,QAAQqB,OAAO,GAAGC,aAAcnG,EAASf,OAAQgG,GACtDrG,KAAKiG,QAAQqB,OAAO,GAAGC,aAAcnG,EAASf,OAAQiG,GAEtDtG,KAAKkG,gBAAgBsB,sBAAuBpG,EAASf,OAAQc,EAAOV,SAAU4F,GAC9ErG,KAAKmG,cAAcqB,sBAAuBpG,EAASf,OAAQc,EAAOV,SAAU6F,IAGhFtG,KAAKyH,mBAAqB,SAASC,EAAOC,EAAOC,EAAQC,EAAWC,EAAQC,GAExED,EAAOP,aAAcG,EAAOrB,GAE5B0B,EAAOR,aAAclB,EAAYC,GAIjC,IAAI0B,EAAU3B,EAAWQ,WAAWP,GACpC,IAAI2B,EAAaJ,EAAUhB,WAAWP,GACtC,IAAI4B,EAAiBN,EAAOf,WAAWP,GACvC,IAAI6B,EAAkBP,EAAOf,WAAWgB,GACxCI,EAAaC,EAAiBC,EAAkBF,GAAcA,EAE9D,IAAIG,EAAWJ,EAAU1G,KAAK+G,IAAIV,GAASM,EAc3C,OAAOG,GAGXpI,KAAKsI,cAAgB,SAASnH,EAAQC,GAAU,IAAAmH,EAAAvI,KAE5C,IAAIwI,EAAgB,GACpB,IAAIb,EAAQxG,EAAOlB,IAAM,EAAIsG,EAC7BvG,KAAKwC,OAAOkE,QAAQ,SAAAC,GAChB6B,EAAc5B,KACV2B,EAAKd,mBACDd,EAAGgB,EAAOxG,EAAOV,SAAUW,EAASf,OACpCkI,EAAKpC,cAAeoC,EAAKrC,oBAIrCyB,GAASxG,EAAOyB,OAChB5C,KAAKwC,OAAOkE,QAAQ,SAAAC,GAChB6B,EAAc5B,KACV2B,EAAKd,mBACDd,EAAGgB,EAAOxG,EAAOV,SAAUW,EAASf,OACpCkI,EAAKrC,gBAAiBqC,EAAKpC,kBAKvC,OAAO7E,KAAKlB,IAAL4G,MAAA1F,KAAYkH,IC9H3BtH,mBAAsB,SAAUuH,EAAQC,EAAY3I,GAEnDC,KAAKyI,OAASA,EACdzI,KAAK0I,WAAcA,IAAe/D,UAAa+D,EAAaC,SAE5D3I,KAAKwC,OAAS,CACb,IAAItB,cAAe,KAAM,KAAM,KAC/B,IAAIA,aAAc,EAAG,EAAG,GACxB,IAAIA,aAAc,EAAG,EAAG,GACxB,IAAIA,aAAc,EAAG,EAAG,GACxB,IAAIA,aAAc,EAAG,EAAG,GACxB,IAAIA,aAAc,EAAG,EAAG,GACxB,IAAIA,aAAc,EAAG,EAAG,GACxB,IAAIA,aAAc,IAAM,IAAM,MAG/BlB,KAAK4I,QAAU,KACf5I,KAAKsD,OAAS,MACdtD,KAAKgC,WAAa,KAClBhC,KAAKiC,WAAa,KAClBjC,KAAKkC,sBAAwB,KAC7BlC,KAAKwD,SAAW,MAChBxD,KAAKuD,MAAQ,MACbvD,KAAK6I,cAAgB,MACrB7I,KAAK8I,OAAS,MACd9I,KAAKmC,OAAS,KACdnC,KAAKoC,WAAapC,KAAKmC,OAEvBnC,KAAKK,OAAS,IAAIa,aAClBlB,KAAK+I,aAAe,IAAI7H,aAAc,GAAI,IAAK,GAC/ClB,KAAKuE,OAAS,CACb,CAAEC,MAAO,IAAMC,IAAK,KACpB,CAAED,OAAQ,GAAKC,IAAK,KACpB,CAAED,MAAO,EAAGC,IAAKnD,KAAKC,GAAK,IAG5BvB,KAAKgJ,UAAY,EACjBhJ,KAAKiJ,YAAc,EACnBjJ,KAAKqE,wBAA0B,CAC9BC,SAAU,GACV4E,eAAgB,EAChBvB,MAAO,KAGR3H,KAAKmJ,UAAY,CAAChJ,IAAI,EAAGC,IAAKgJ,UAC9BpJ,KAAKqB,WAAa,CAAElB,IAAK,EAAGC,IAAKkB,KAAKC,IACtCvB,KAAKwB,aAAe,CAAErB,KAAMiJ,SAAUhJ,IAAKgJ,UAE3CpJ,KAAKyD,aAAe,CAAEC,MAAOxC,WAAYyC,KAAMC,KAAM1C,WAAY2C,OAAQC,IAAK5C,WAAY6C,OAC1F/D,KAAKgE,UAAY,CAAEN,MAAO,EAAGE,KAAM,EAAGE,IAAK,EAAGG,QAAS,GAGvDjE,KAAK6B,UAAY,KACjB7B,KAAK8B,QAAU,KACf9B,KAAK0C,WAAa,IAAIxB,aAGtBlB,KAAKqJ,cAAgB,KAErBrJ,KAAK0E,iBAAmB,CACvB/D,KAAM,EACNC,MAAO,EACPC,IAAK,EACLC,OAAQ,GAMT,IAAIwI,EAAQtJ,KAEZ,IAAIuJ,EAAM,KAEV,IAAIC,EAAc,IAAItI,aACtB,IAAIuI,EAAY,IAAIvI,aACpB,IAAIwI,EAAc,IAAIxI,aAEtB,IAAIyI,EAAW,IAAIzI,aACnB,IAAI0I,EAAS,IAAI1I,aACjB,IAAI2I,EAAW,IAAI3I,aACnB,IAAI4I,EAAY,IAAI5I,aAEpB,IAAI6I,EAAS,IAAI7I,aAEjB,IAAI8I,EAAa,IAAI9I,aACrB,IAAI+I,EAAW,IAAI/I,aACnB,IAAIgJ,EAAa,IAAIhJ,aAErB,IAAIiJ,EAEJ,IAAIC,EACJ,IAAI5F,EACJ,IAAIC,EAEJ,IAAI4F,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAIC,EAEJ,IAAIC,EAAa,EACjB,IAAIC,EAAW,EAEf,IAAIC,EAAQ,EACZ,IAAIC,EAAM,IAAI1J,aACd,IAAI2J,EAAY,IAAI3J,aAEpB,IAAI4J,EAAsB,CACzBC,QAAS,EACTC,UAAW,EACXC,QAAS,EACTC,SAAU,EACV1G,MAAO,EACPC,IAAK,EACL2F,KAAM,GAGP,IAAIe,EAAQ,CAAEC,MAAO,EAAGC,OAAQ,EAAGC,MAAO,EAAGxH,IAAK,EAAGyH,aAAc,EAAGC,YAAa,EAAGC,UAAW,EAAGC,cAAe,EAAGC,QAAS,GAC/H,IAAIC,EAAQT,EAAMC,KAGlB,IAAIS,GAAO,IAAI3K,iBAAmB4K,mBAAmBrD,EAAOsD,GAAI,IAAI7K,aAAc,EAAG,EAAG,IACxF,IAAI8K,EAAcH,EAAKI,QAAQC,UAK/B,IAAIC,EAAc,CAAEC,KAAM,UAC1B,IAAIC,EAAe,CAAED,KAAM,UAG3BpM,KAAKsM,QAAU,SAAUhI,GACxB,IAAIiI,EAAKvM,KAAKyI,OAAO1F,OAAOC,SAE5B8G,EAAUnI,IAAI4K,EAAG,GAAIA,EAAG,GAAIA,EAAG,IAC/BzC,EAAU0C,gBAAiBlI,GAC3BsG,EAAIhJ,IAAIkI,IAIT9J,KAAKyM,MAAQ,SAAUnI,GACtB,IAAIiI,EAAKvM,KAAKyI,OAAO1F,OAAOC,SAE5B8G,EAAUnI,IAAI4K,EAAG,GAAIA,EAAG,GAAIA,EAAG,IAC/BzC,EAAU0C,eAAelI,GACzBsG,EAAIhJ,IAAIkI,IAKT9J,KAAK4K,IAAM,SAAU8B,EAAQC,GAC5B,IAAIC,EAAUtD,EAAMZ,aAAeC,SAAWW,EAAMZ,WAAWmE,KAAOvD,EAAMZ,WAE5E,IAAIjI,EAAW6I,EAAMb,OAAOhI,SAC5B,IAAIsJ,EAAStJ,EAASwL,QAAQa,IAAIxD,EAAMjJ,QACxC,IAAI6I,EAAiBa,EAAOgD,SAE5B7D,GAAkB5H,KAAK+G,IAAKiB,EAAMb,OAAOxI,IAAM,EAAKqB,KAAKC,GAAK,KAE9D+H,EAAMgD,QAAQ,EAAII,EAASxD,EAAiB0D,EAAQI,cACpD,IAAK1D,EAAMR,OAAQQ,EAAMmD,MAAM,EAAIE,EAASzD,EAAiB0D,EAAQI,eAGtEhN,KAAKiN,QAAU,SAAUC,GACxB,GAAIA,IAAevI,UAAWuI,EAAaC,IAC3CxC,GAASuC,GAGVlN,KAAKoN,SAAW,SAAUF,GACzB,GAAIA,IAAevI,UAAWuI,EAAaC,IAC3CxC,GAASuC,GAGVlN,KAAKoF,kBAAoB,WACxB,IAAIiI,EAAO,EACL/D,EAAM9G,OAAOkE,QAAQ,SAAAC,GAC1B0G,EAAO/L,KAAKlB,IAAIkJ,EAAMjJ,OAAOwG,WAAWF,GAAI0G,KAG7C,IAAIlF,EAAkBmB,EAAMb,OAAOhI,SAASoG,WAAWyC,EAAMjJ,QAC7DL,KAAKyI,OAAOtF,KAAO7B,KAAKlB,IAAI+H,EAAkBkF,EAAM,IACpDrN,KAAKyI,OAAOrF,IAAM9B,KAAKlB,IAAI+H,EAAkBkF,EAAM,KACnDrN,KAAKyI,OAAO5F,0BAGb,SAASyK,IACR,IAAIC,EAAQ,IACZ,IAAIC,EAAQ,IACZlE,EAAM9G,OAAOkE,QAAQ,SAAAC,GACpB4G,EAAQjM,KAAKnB,IAAImJ,EAAMjJ,OAAOwG,WAAWF,GAAI4G,GAC7CC,EAAQlM,KAAKnB,IAAImJ,EAAMb,OAAOhI,SAASoG,WAAWF,GAAI6G,KAEvDrJ,QAAQC,IAAI,UAAWmJ,EAAOC,EAAO,KAAMlE,EAAMb,OAAOtF,KAAMmG,EAAMb,OAAOrF,IAAKkG,EAAMb,OAAOhI,SAASoG,WAAWyC,EAAMjJ,SAGxH,SAASoN,IACR1D,EAAO2D,KAAKpE,EAAMb,OAAOhI,UAAUqM,IAAIxD,EAAMjJ,QAC7C0J,EAAO4D,gBAAgB9B,GACvBrH,EAAQlD,KAAKsM,MAAM7D,EAAOzJ,EAAGyJ,EAAOvJ,GACpCiE,EAAMnD,KAAKsM,MAAMtM,KAAKuM,KAAK9D,EAAOzJ,EAAIyJ,EAAOzJ,EAAIyJ,EAAOvJ,EAAIuJ,EAAOvJ,GAAIuJ,EAAOxJ,GAE9E4J,EAAU,MACVC,EAAOL,EAAOgD,SACd1C,EAAUD,EACPE,EAAW9F,EACd+F,EAAS9F,EACT+F,EAAYlB,EAAMjJ,OAGnB,SAASyN,IACR,IAAIC,EAAOC,EAAevJ,EAAKD,EAAO8E,EAAM/E,QAE5C,IAAM+E,EAAMlH,YAAc6L,EAAaF,EAAKvJ,MAAOuJ,EAAKtJ,KAAO,CAC9D6F,EAAWyD,EAAKvJ,MAChB+F,EAASwD,EAAKtJ,IACd0F,EAAU,UAEJ,GAAK8D,EAAa3E,EAAMzH,UAAWyH,EAAMxH,SAAW,CAC1DwI,EAAWhB,EAAMzH,UACjB0I,EAASjB,EAAMxH,QAEhB,GAAIwH,EAAM5G,aAAe,KAAM8H,EAAYlB,EAAM5G,WACjD2H,EAAUD,EAAOO,EAIlB3K,KAAKkO,WAAa,WACjBC,IACAnO,KAAK+B,UAGN,SAASoM,IACR,IAAIC,EAAa,EAAI9E,EAAMZ,WAAW2F,YACtC,IAAIC,EAAc,EAAIhF,EAAMZ,WAAWsE,aAEvC,IAAIuB,EAAQ,GACZ,IAAIC,EAAQ,GAENlF,EAAM9G,OAAOkE,QAAQ,SAAAC,GACjBkE,EAAU6C,KAAK/G,GAAG8H,QAAQnF,EAAMb,QACzC8F,EAAM3H,KAAKiE,EAAUvK,GAAKuK,EAAUvK,EAAI,GAAK8N,EAAa9E,EAAM5E,iBAAiB/D,KAAOyN,EAAa9E,EAAM5E,iBAAiB9D,QAC5H4N,EAAM5H,KAAKiE,EAAUtK,GAAKsK,EAAUtK,EAAI,GAAK+N,EAAchF,EAAM5E,iBAAiB5D,OAASwN,EAAchF,EAAM5E,iBAAiB7D,QAEjI0N,EAAQA,EAAMG,OAAOC,UACrBH,EAAQA,EAAME,OAAOC,UACf,IAAIxG,EAAkBmB,EAAMb,OAAOhI,SAASoG,WAAWyC,EAAMjJ,QACnE,IAAID,EAAMkB,KAAKlB,IAAL4G,MAAA1F,KAAIsN,IAAQL,GAARM,OAAAD,IAAkBJ,GAAlB,CAAyBlN,KAAKwN,IAAIxN,KAAKnB,IAAL6G,MAAA1F,KAAIsN,IAAQL,GAARM,OAAAD,IAAkBJ,SACtErG,EAAkBA,EAAkB/H,EAEpCiK,EAAUlC,GAAmBiC,EAuB9B,SAAS2E,IAERzE,EAAWhJ,KAAKlB,IAAIkJ,EAAM9H,aAAarB,IAAKmB,KAAKnB,IAAImJ,EAAM9H,aAAapB,IAAKkK,IAE7EC,EAASjJ,KAAKlB,IAAIkJ,EAAMjI,WAAWlB,IAAKmB,KAAKnB,IAAImJ,EAAMjI,WAAWjB,IAAKmK,IAEvEF,EAAU/I,KAAKlB,IAAIkJ,EAAMH,UAAUhJ,IAAKmB,KAAKnB,IAAImJ,EAAMH,UAAU/I,IAAKiK,IAGvE,SAAS4D,EAAae,EAAWC,EAASC,EAAUC,GACnD,IAAIC,EAAIJ,IAAcrK,UAAYrD,KAAKwN,IAAItK,EAAQwK,GAAa1F,EAAMjF,wBAAwBsD,MAAQ,MACtG,IAAIhB,EAAIsI,IAAYtK,UAAYrD,KAAKwN,IAAIrK,EAAMwK,GAAW3F,EAAMjF,wBAAwBsD,MAAQ,MAChG,IAAInH,EAAI0O,IAAavK,UAAYrD,KAAKwN,IAAI1E,EAAO8E,GAAY,GAAK,MAClE,IAAIG,EAAKF,IAAexK,UAAYrD,KAAKwN,IAAIxF,EAAMjJ,OAAOC,EAAI6O,EAAW7O,GAAK,GAAKgB,KAAKwN,IAAIxF,EAAMjJ,OAAOE,EAAI4O,EAAW5O,GAAK,GAAKe,KAAKwN,IAAIxF,EAAMjJ,OAAOG,EAAI2O,EAAW3O,GAAK,EAAI,MAChL,OAAO4O,GAAKzI,GAAKnG,GAAK6O,EAGvB,SAASC,IAER,IAAIC,EAAiBjO,KAAKkO,MAAMlO,KAAKlB,IACpCkB,KAAKwN,IAAIxE,EAAW9F,GAAS8E,EAAMjF,wBAAwBsD,MAC3DrG,KAAKwN,IAAIvE,EAAS9F,GAAO6E,EAAMjF,wBAAwBsD,MACvDrG,KAAKwN,IAAIzE,EAAUD,GAAQd,EAAMjF,wBAAwBC,SACzD,IAEDsH,EAAQT,EAAMQ,QACdb,EAAsB,CACrBC,QAASwE,EACTvE,WAAYV,EAAW9F,GAAS+K,EAChCtE,SAAUV,EAAS9F,GAAO8K,EAC1BrE,UAAWb,EAAUD,GAAQmF,EAC7B/K,MAAO8F,EACP7F,IAAK8F,EACLH,KAAMC,GAGPoF,IAID,SAASC,IACRpF,EAAWhJ,KAAKsM,MAAM7D,EAAOzJ,EAAGyJ,EAAOvJ,GAAKiK,EAC5CF,EAASjJ,KAAKsM,MAAMtM,KAAKuM,KAAK9D,EAAOzJ,EAAIyJ,EAAOzJ,EAAIyJ,EAAOvJ,EAAIuJ,EAAOvJ,GAAIuJ,EAAOxJ,GAAKmK,EACtFL,EAAUD,EAAOO,EACjBH,EAAYlB,EAAMjJ,OAAO4L,QACzBzB,EAAU5I,IAAIgJ,GAGf,SAAS6E,IAMR,GAAI3E,EAAoBC,SAAW,EAAG,CACrCT,EAAWQ,EAAoBtG,MAC/B+F,EAASO,EAAoBrG,IAC7B4F,EAAUS,EAAoBV,KAC9BwB,EAAQT,EAAMC,SACR,CACNd,EAAW9F,EAAQsG,EAAoBE,UACvCT,EAAS9F,EAAMqG,EAAoBG,QACnCZ,EAAUD,EAAOU,EAAoBI,SACrCJ,EAAoBC,SAAW,GAIjC,SAAS4E,IAGRpF,EAASjJ,KAAKlB,IAAImJ,EAAKjI,KAAKnB,IAAImB,KAAKC,GAAKgI,EAAKgB,IAE/CR,EAAOzJ,EAAI+J,EAAU/I,KAAKsO,IAAIrF,GAAUjJ,KAAKsO,IAAItF,GACjDP,EAAOxJ,EAAI8J,EAAU/I,KAAKuO,IAAItF,GAC9BR,EAAOvJ,EAAI6J,EAAU/I,KAAKsO,IAAIrF,GAAUjJ,KAAKuO,IAAIvF,GACjDP,EAAO4D,gBAAgB3B,GAEvB1C,EAAMjJ,OAASmK,EAEflB,EAAMb,OAAOhI,SAASiN,KAAKpE,EAAMjJ,QAAQuB,IAAImI,GAC7CT,EAAMb,OAAOtF,KAAO7B,KAAKlB,IAAI,EAAGkJ,EAAMb,OAAOtF,MAAQiH,EAAOC,IAC5Df,EAAMb,OAAOrF,IAAMkG,EAAMb,OAAOrF,KAAOgH,EAAOC,GAC9Cf,EAAMb,OAAOqH,OAAOxG,EAAMjJ,QAG1BoK,EAAa,EACbC,EAAW,EACXpB,EAAMzH,UAAYyI,EAClBhB,EAAMxH,QAAUyI,EAChBjB,EAAM5G,WAAWf,IAAI6I,EAAUlK,EAAGkK,EAAUjK,EAAGiK,EAAUhK,GACzDmK,EAAQ,EACRC,EAAIjJ,IAAI,EAAG,EAAG,GAGf3B,KAAK+B,OAAS,WAEb,IAAK/B,KAAKqJ,eAAiBrJ,KAAKiC,WAAY,gCAC5CwL;IACA,OAAQ7B,GACP,KAAKT,EAAMC,KACV0C,IACA,IACE3D,KAGCnK,KAAKgC,aACDhC,KAAKiC,YAAcjC,KAAKqJ,gBAAkB,OAC1CrJ,KAAKiC,YAAc2J,IAAUT,EAAMC,MAExC,CACD+C,IAED,IAAKnO,KAAKkC,uBAAyB+L,EAAa3D,EAAUC,EAAQF,GAAU,CAC3EiF,IAED,MAED,KAAKnE,EAAMQ,QACV8D,IACA,MAED,QACCC,IACAX,IACA,IAAM/O,KAAKgC,aAAehC,KAAKiC,WAAakM,IAE9CwB,IAEA,OAAQ/D,GAKP,KAAKT,EAAMQ,QACVrC,EAAMyG,cAAc1D,GACpB,MACD,QACC/C,EAAMyG,cAAc5D,KAIvBnM,KAAKgQ,cAAgB,WACpB,OAAOvL,GAGRzE,KAAKiQ,kBAAoB,WACxB,OAAOzL,GAGR,SAAS2I,IACR,OAAO7L,KAAK4O,IAAI,IAAM5G,EAAMN,WAG7B,SAASgF,EAAemC,EAAYC,EAAcC,GACjD,IAAIC,EAASC,EAAMC,EADsD,IAAAC,EAEjCJ,EAFiC,IAAAK,EAAAC,IAAAF,GAEvEH,EAFuEI,EAAA,GAE3DL,EAF2DK,EAAAE,MAAA,GAGzEL,EAAOjP,KAAKwN,IAAIwB,EAAQ9L,MAAQ4L,GAChCC,EAAuB3J,QAAQ,SAASmK,GACvCL,EAAalP,KAAKwN,IAAI+B,EAAOrM,MAAQ4L,GACrC,GAAIG,EAAOC,EAAY,CACtBF,EAAUO,EACVN,EAAOC,KAGT,OAAOF,EAGR,SAASQ,EAAYC,GAEpB,GAAIzH,EAAMV,UAAY,MAAO,OAC7BmI,EAAMC,iBAEN,GAAID,EAAME,SAAW3H,EAAM7F,aAAaC,MAAO,CAC9C,GAAI4F,EAAM9F,WAAa,KAAM,OAE7BoI,EAAQT,EAAME,OACd7B,EAAY7H,IAAIoP,EAAMG,QAASH,EAAMI,cAE/B,GAAIJ,EAAME,SAAW3H,EAAM7F,aAAaG,KAAM,CACpD,GAAI0F,EAAMhG,SAAW,KAAM,OAE3BsI,EAAQT,EAAMG,MACdtB,EAAWrI,IAAIoP,EAAMG,QAASH,EAAMI,cAE9B,GAAIJ,EAAME,SAAW3H,EAAM7F,aAAaK,IAAK,CACnD,GAAIwF,EAAM/F,QAAU,KAAM,OAE1BqI,EAAQT,EAAMrH,IACd6F,EAAShI,IAAIoP,EAAMG,QAASH,EAAMI,SAGnC,GAAIvF,IAAUT,EAAMC,KAAM,CACzBzC,SAASyI,iBAAiB,YAAaC,EAAa,OACpD1I,SAASyI,iBAAiB,UAAWE,EAAW,QAMlD,SAASD,EAAYN,GAEpB,GAAIzH,EAAMV,UAAY,MAAO,OAE7BmI,EAAMC,iBAEN,IAAIpE,EAAUtD,EAAMZ,aAAeC,SAAWW,EAAMZ,WAAWmE,KAAOvD,EAAMZ,WAE5E,GAAIkD,IAAUT,EAAME,OAAQ,CAE3B,GAAI/B,EAAM9F,WAAa,KAAM,OAE7BiG,EAAU9H,IAAIoP,EAAMG,QAASH,EAAMI,SACnCzH,EAAY6H,WAAW9H,EAAWD,GAGlCiB,GAAc,EAAInJ,KAAKC,GAAKmI,EAAYpJ,EAAIsM,EAAQyB,YAAc/E,EAAML,YAGxEyB,GAAY,EAAIpJ,KAAKC,GAAKmI,EAAYnJ,EAAIqM,EAAQI,aAAe1D,EAAML,YAEvEO,EAAYkE,KAAKjE,QAEX,GAAImC,IAAUT,EAAMG,MAAO,CAEjC,GAAIhC,EAAMhG,SAAW,KAAM,OAE3B2G,EAAStI,IAAIoP,EAAMG,QAASH,EAAMI,SAClCjH,EAAWqH,WAAWtH,EAAUD,GAEhC,GAAIE,EAAW3J,EAAI,EAAG,CAErB+I,EAAM2D,eAEA,GAAI/C,EAAW3J,EAAI,EAAG,CAE5B+I,EAAM8D,WAIPpD,EAAW0D,KAAKzD,QAEV,GAAI2B,IAAUT,EAAMrH,IAAK,CAE/B,GAAIwF,EAAM/F,QAAU,KAAM,OAE1BqG,EAAOjI,IAAIoP,EAAMG,QAASH,EAAMI,SAChCtH,EAAS0H,WAAW3H,EAAQD,GAE5BL,EAAMsB,IAAIf,EAASvJ,EAAGuJ,EAAStJ,GAE/BoJ,EAAS+D,KAAK9D,GAGf,GAAIgC,IAAUT,EAAMC,KAAM,CACzB9B,EAAMvH,SACNuH,EAAMlH,WAAakH,EAAMnH,QAK3B,SAASmP,IACR,GAAIhI,EAAMV,UAAY,MAAO,OAE7BD,SAAS6I,oBAAoB,YAAaH,EAAa,OACvD1I,SAAS6I,oBAAoB,UAAWF,EAAW,OAEnD1F,EAAQT,EAAMC,KACd9B,EAAMvH,SAIP,SAAS0P,GAAaV,GACrB,GAAIzH,EAAMV,UAAY,OAASU,EAAMhG,SAAW,MAAQsI,IAAUT,EAAMC,KAAM,OAE9E2F,EAAMC,iBACND,EAAMW,kBACN,IAAIC,EAAQ,EAEZ,GAAIZ,EAAMa,aAAejN,UAAW,CAEnCgN,EAAQZ,EAAMa,gBAER,GAAIb,EAAMc,SAAWlN,UAAW,CAEtCgN,GAAUZ,EAAMc,OAGjB,GAAIF,EAAQ,EAAG,CAEdrI,EAAM8D,gBAEA,GAAIuE,EAAQ,EAAG,CAErBrI,EAAM2D,UAGP3D,EAAMlH,WAAakH,EAAMnH,OACzBmH,EAAMvH,SAMP,SAAS+P,GAAWf,GACnB,GAAIzH,EAAMV,UAAY,MAAO,OAE7B,OAAQmI,EAAMgB,QAAQhF,QAErB,KAAKzD,EAAMtF,UAAUN,MAEpB,GAAI4F,EAAM9F,WAAa,KAAM,OAE7BoI,EAAQT,EAAMI,aAEd/B,EAAY7H,IAAIoP,EAAMgB,QAAQ,GAAGC,MAAOjB,EAAMgB,QAAQ,GAAGE,OACzD,MAED,KAAK3I,EAAMtF,UAAUJ,KAEpB,GAAI0F,EAAMhG,SAAW,KAAM,OAE3BsI,EAAQT,EAAMK,YAEd,IAAI0G,EAAKnB,EAAMgB,QAAQ,GAAGC,MAAQjB,EAAMgB,QAAQ,GAAGC,MACnD,IAAIG,EAAKpB,EAAMgB,QAAQ,GAAGE,MAAQlB,EAAMgB,QAAQ,GAAGE,MACnD,IAAI3N,EAAWhD,KAAKuM,KAAKqE,EAAKA,EAAKC,EAAKA,GACxCnI,EAAWrI,IAAI,EAAG2C,GAClB,MAED,KAAKgF,EAAMtF,UAAUF,IAEpB,GAAIwF,EAAM/F,QAAU,KAAM,OAE1BqI,EAAQT,EAAMM,UAEd9B,EAAShI,IAAIoP,EAAMgB,QAAQ,GAAGC,MAAOjB,EAAMgB,QAAQ,GAAGE,OACtD,MAED,KAAK3I,EAAMtF,UAAUC,QACpB2H,EAAQT,EAAMO,cACdpC,EAAMT,cAAgB,KAEtBc,EAAShI,IAAIoP,EAAMgB,QAAQ,GAAGC,MAAOjB,EAAMgB,QAAQ,GAAGE,OACtD,MAED,QAECrG,EAAQT,EAAMC,MAQjB,SAASgH,GAAcT,GACtB,OAAQrI,EAAMT,eACb,KAAK,MACJ,OAAO8I,EACR,IAAK,aACJA,EAAMU,KAAK,GACX,OAAOV,EACR,IAAK,YACJA,EAAMW,KAAK,GACX,OAAOX,EACR,QACCrI,EAAMT,cAAgBvH,KAAKwN,IAAI6C,EAAMrR,GAAKgB,KAAKwN,IAAI6C,EAAMpR,GAAK,aAAe,YAC7E,OAAO6R,GAAcT,IAKxB,SAASY,GAAUxB,GAElB,GAAIzH,EAAMV,UAAY,MAAO,OAE7BmI,EAAMC,iBACND,EAAMW,kBAEN,IAAI9E,EAAUtD,EAAMZ,aAAeC,SAAWW,EAAMZ,WAAWmE,KAAOvD,EAAMZ,WAE5E,OAAQqI,EAAMgB,QAAQhF,QAErB,KAAKzD,EAAMtF,UAAUN,MAEpB,GAAI4F,EAAM9F,WAAa,KAAM,OAC7B,GAAIoI,IAAUT,EAAMI,aAAc,OAElC9B,EAAU9H,IAAIoP,EAAMgB,QAAQ,GAAGC,MAAOjB,EAAMgB,QAAQ,GAAGE,OACvDvI,EAAY6H,WAAW9H,EAAWD,GAClCE,EAAc0I,GAAc1I,GAG5Be,GAAc,EAAInJ,KAAKC,GAAKmI,EAAYpJ,EAAIsM,EAAQyB,YAAc/E,EAAML,YAExEyB,GAAY,EAAIpJ,KAAKC,GAAKmI,EAAYnJ,EAAIqM,EAAQI,aAAe1D,EAAML,YAEvEO,EAAYkE,KAAKjE,GAEjBH,EAAMlH,WAAakH,EAAMnH,OACzBmH,EAAMvH,SACN,MAED,KAAKuH,EAAMtF,UAAUJ,KAEpB,GAAI0F,EAAMhG,SAAW,KAAM,OAC3B,GAAIsI,IAAUT,EAAMK,YAAa,OAEjC,IAAI0G,EAAKnB,EAAMgB,QAAQ,GAAGC,MAAQjB,EAAMgB,QAAQ,GAAGC,MACnD,IAAIG,EAAKpB,EAAMgB,QAAQ,GAAGE,MAAQlB,EAAMgB,QAAQ,GAAGE,MACnD,IAAI3N,EAAWhD,KAAKuM,KAAKqE,EAAKA,EAAKC,EAAKA,GAExClI,EAAStI,IAAI,EAAG2C,GAChB4F,EAAWqH,WAAWtH,EAAUD,GAEhC,GAAIE,EAAW3J,EAAI,EAAG,CAErB+I,EAAM8D,gBAEA,GAAIlD,EAAW3J,EAAI,EAAG,CAE5B+I,EAAM2D,UAIPjD,EAAW0D,KAAKzD,GAEhBX,EAAMlH,WAAakH,EAAMnH,OACzBmH,EAAMvH,SACN,MAED,KAAKuH,EAAMtF,UAAUF,IACpB,GAAIwF,EAAM/F,QAAU,KAAM,OAC1B,GAAIqI,IAAUT,EAAMM,UAAW,OAE/B7B,EAAOjI,IAAIoP,EAAMgB,QAAQ,GAAGC,MAAOjB,EAAMgB,QAAQ,GAAGE,OACpDpI,EAAS0H,WAAW3H,EAAQD,GAC5BE,EAAWuI,GAAcvI,GAEzBP,EAAMsB,IAAIf,EAASvJ,EAAGuJ,EAAStJ,GAC/BoJ,EAAS+D,KAAK9D,GAEdN,EAAMlH,WAAakH,EAAMnH,OACzBmH,EAAMvH,SACN,MAED,KAAKuH,EAAMtF,UAAUC,QACpB,GAAIqF,EAAM/F,QAAU,KAAM,OAC1B,GAAIqI,IAAUT,EAAMO,cAAe,OACnC9B,EAAOjI,IAAIoP,EAAMgB,QAAQ,GAAGC,MAAOjB,EAAMgB,QAAQ,GAAGE,OACpDpI,EAAS0H,WAAW3H,EAAQD,GAC5BE,EAAWuI,GAAcvI,GACzB,OAAQP,EAAMT,eACb,KAAK,MACL,IAAK,aACJS,EAAMsB,IAAIf,EAASvJ,EAAGuJ,EAAStJ,GAC/BoJ,EAAS+D,KAAK9D,GAEdN,EAAMlH,WAAakH,EAAMnH,OACzBmH,EAAMvH,SACN,MACD,IAAK,YAEJ0I,GAAc,EAAInJ,KAAKC,GAAKsI,EAASvJ,EAAIsM,EAAQyB,YAAc/E,EAAML,YAErEyB,GAAY,EAAIpJ,KAAKC,GAAKsI,EAAStJ,EAAIqM,EAAQI,aAAe1D,EAAML,YAEpEU,EAAS+D,KAAK9D,GAEdN,EAAMlH,WAAakH,EAAMnH,OACzBmH,EAAMvH,SACN,MACD,QACC6J,EAAQT,EAAMC,KACd,MAGF,MAED,QACCQ,EAAQT,EAAMC,MAMjB,SAASoH,KACR,GAAIlJ,EAAMV,UAAY,MAAO,OAE7B,GAAIU,EAAMT,gBAAkB,MAAOS,EAAMT,cAAgB,KACzD+C,EAAQT,EAAMC,KACd9B,EAAMvH,SAIP/B,KAAK0I,WAAW0I,iBAAiB,cAAe,SAAUL,GAASA,EAAMC,kBAAqB,OAC9FhR,KAAK0I,WAAW0I,iBAAiB,YAAaN,EAAa,OAC3D9Q,KAAK0I,WAAW0I,iBAAiB,aAAcK,GAAc,OAC7DzR,KAAK0I,WAAW0I,iBAAiB,iBAAkBK,GAAc,OAEjEzR,KAAK0I,WAAW0I,iBAAiB,aAAcU,GAAY,OAC3D9R,KAAK0I,WAAW0I,iBAAiB,WAAYoB,GAAU,OACvDxS,KAAK0I,WAAW0I,iBAAiB,YAAamB,GAAW,OAGzDvS,KAAK+B,UAINb,mBAAoBuR,UAAYC,OAAOC,OAAOzR,qBAAsBuR,WACpEvR,mBAAoBuR,UAAUG,YAAc1R", "file": "js/bbfdea08.4364390d.js", "sourcesContent": ["import * as THREE from 'three'\nimport './tylko-camera-orbit-perspective'\n\n// Obiekt odpowiada za:\n// - inicjalizacje kamery oraz controls\n// - importem i eksportem stanu kamery\n// - przechowywanie ustawien kamery i widoku na potrzeby karty z ustawieniami\n// - obliczanie, na podstawie dostarczonej geometrii (szafki):\n//   - punktow (naroznikow) ktore controls maja utrzymac wewnatrz frustrum\n//   - wejsciowych parametrow kamery (kat + target) \n//       \"dla szafki 120cmx200cm poprawne katy to a/b i target to x/y/z\"\n// Obiekt NIE odpowiada za:\n// - operowanie kamera sama w sobie (z wyjatkiem ustawiania predefiniowanych widokow)\n// - obliczaniem kompletu parametrow, przesuniec, transformacji kamery\n// - obsluga manipulowania kamera przez uzytkownika\n\nexport function tylkoCamera(view, scene) {\n  this.fov = 20;\n  this.range = { min: 1, max: 50000 };\n  this.target = { x: 0, y: 5, z: 0 };\n  // this.rotation = { xz: 0, xy: 0, };\n  this.position = { x: 0, y: 1000, z: 2000 };\n  this.geometryOffset = {left: 10, right: 0, top: 0, bottom: 10, front: 0, back: 0}\n  \n  this.geometryBox = new THREE.Box3()\n  \n  this.camera = new THREE.PerspectiveCamera(\n    this.fov,\n    2, // aspect,\n    this.range.min,\n    this.range.max\n  )\n  this.controls = new THREE.OrbitControls(this.camera, view, scene)\n  this.controls.polarAngle = { min: Math.PI / 4, max: Math.PI / 2 }\n  this.controls.azimuthAngle = { min: -Math.PI / 3, max: Math.PI / 3 }\n  this.dynamicTarget = true;\n\n  this.init = function () {\n    this.camera.position.set(1000, 1000, 1000)\n    this.camera.add(new THREE.PointLight(0xffffff, 1));\n    this.controls.target.set(this.target.x, this.target.y, this.target.z)\n    this.controls.new_theta = -0.4;\n    this.controls.new_phi = 1.4;\n    this.controls.update()\n    this.controls.noAutoZoom = false;\n    this.controls.noLifeZoom = true;\n    this.controls.noTransitionAnimation = false;\n    this.controls.noSnap = false;\n    this.controls.noSnapReal = false;\n  }\n\n  this.updatePoints = function(min, max){\n    let o = this.geometryOffset\n    let coor = [\n      min.x - o.left,\n      max.x + o.right,\n      min.y - o.bottom,\n      max.y + o.top,\n      min.z - o.back,\n      max.z + o.front,\n    ]\n    this.controls.points[0].set(coor[0], coor[2], coor[4]) // 000\n    this.controls.points[1].set(coor[1], coor[3], coor[5]) // 111\n    this.controls.points[2].set(coor[0], coor[2], coor[5]) // 001\n    this.controls.points[3].set(coor[0], coor[3], coor[4]) // 010\n    this.controls.points[4].set(coor[0], coor[3], coor[5]) // 011\n    this.controls.points[5].set(coor[1], coor[2], coor[4]) // 100\n    this.controls.points[6].set(coor[1], coor[2], coor[5]) // 101\n    this.controls.points[7].set(coor[1], coor[3], coor[4]) // 110\n  }\n\n  this.updateGeometry = function(min, max) {\n\n    // -- Obliczanie punktow do \"utrzymania wewnatrz\" frustrum\n    // Dla grupy obiektow chyba lepiej tak niż prosty gabaryt :)\n    // this.geometryBox.setFromObject(geometry)\n    // let min = this.geometryBox.min\n    // let max = this.geometryBox.max\n    if (this.dynamicTarget) {\n      this.target.y = 0.5 * max.y + 55\n      this.controls.new_target.set(this.target.x, this.target.y, this.target.z)\n    }\n  \n    this.updatePoints(min, max)\n\n    this.controls.update()\n    // this.controls.updateCameraRange()\n\n  }\n\n  this.updateAspect = function (aspect) {\n    this.camera.aspect = aspect\n    this.camera.updateProjectionMatrix()\n    // this.controls.update();\n  }\n\n  // -- Glowna funkcja uaktualnienia kamery - odpalana w render()\n  this.update = function (aspect) {\n    this.updateAspect(aspect)\n    this.updateParams() // <- gwarantuje poprawne wartosci parametrow niezaleznie od zrodla zmian\n  }\n\n  // -- Uaktualnia zmienne tylkoCamera na bazie stanu kamery\n  this.updateParams = function () {\n    let matrix = this.camera.matrix.elements\n    // Uaktualniam pozycje i target\n    this.position = { x: matrix[12], y: matrix[13], z: matrix[14] };\n    // this.target = {x: matrix[8], y: matrix[9], z: matrix[10]};\n    this.target = this.controls.target\n\n  }\n\n  // -- Uaktualnia ustawienia kamery na bazie zmiennych tylkoCamera -> wywolywana przez slidery\n  this.updateCamera = function () {\n    this.camera.fov = parseInt(this.fov);\n    this.camera.near = parseInt(this.range.min);\n    this.camera.far = parseInt(this.range.max);\n    this.camera.position.set(this.position.x, this.position.y, this.position.z)\n    // this.camera.lookAt(new THREE.Vector3(this.camParams.target.x, this.camParams.target.y, this.camParams.target.z));\n    this.controls.target.set(this.target.x, this.target.y, this.target.z);\n    this.controls.update()\n  }\n\n  this.setDefaultfView = function() {\n    this.controls.noZoom = false;\n    this.controls.noPan = false;\n    this.controls.noRotate = false;\n    this.controls.noAutoZoom = false;\n    this.controls.noLifeZoom = false;\n    this.controls.mouseButtons = { ORBIT: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.RIGHT }\n    this.controls.touchMode = { ORBIT: 1, ZOOM: 2, PAN: 3, ZOOMPAN: 4 };\n  }\n\n  this.setShelfViewFinal = function(min, max) {\n    console.log(\">>>> SHELF VIEW\")\n    this.controls.noTransitionAnimation = true;\n    this.controls.noZoom = true;\n    this.controls.noPan = true;\n    this.controls.noRotate = false;\n    this.controls.noLifeZoom = true;\n    this.controls.noAutoZoom = false;\n    this.controls.animationStepParameters.distance = 120;\n    this.controls.azimuthAngle = { min: -Math.PI / 6 * 5, max: Math.PI / 6 * 5 }\n    this.camera.near = 0.1;\n    this.camera.far = 50000;\n    this.camera.updateProjectionMatrix()\n    this.controls.mouseButtons = {\n        ORBIT: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.RIGHT }\n    this.controls.touchMode = {\n        ORBIT: 1, ZOOM: 2, PAN: 3, ZOOMPAN: 4 };\n    this.controls.snapTo = [\n        { theta: 0.52, phi: 1.4},\n        { theta: -0.52, phi: 1.4},\n        { theta: 0, phi: 1.4},\n    ]\n    this.geometryOffset = {left: 50, right: 50, top: 50, bottom: 50, front: 0, back: 0}\n    this.controls.screenEdgeOffset = {left: 15, right: 15, top: 0, bottom: 0}\n    if (min !== undefined) this.updateGeometry(min, max);\n    this.controls.noTransitionAnimation = false;\n    }\n\n  this.setPipViewFinal = function(min, max) {\n      // this.controls.noZoom = true;\n      // this.controls.noPan = true;\n      // this.controls.noRotate = true;\n      this.controls.noSnap = true;\n      this.controls.noSnapReal = true;\n      this.controls.noTransitionAnimation = true;\n      this.controls.noLifeZoom = true;\n      // this.controls.noAutoZoom = false;\n      // this.controls.snapTo = [\n      //   { theta: 0, phi: 1.4},\n      // ]\n      this.camera.aspect = 160/100\n      this.camera.near = 0.1;\n      this.camera.far = 50000;\n      this.camera.updateProjectionMatrix()\n\n      // this.geometryOffset = {left: 0, right: 0, top: 0, bottom: 1000, front: 0, back: 0}\n      this.controls.screenEdgeOffset = {left: 5, right: 5, top: 5, bottom: 5}\n      // this.controls.target.x = 0;\n      // this.controls.target.y = -200;\n      // this.controls.update()\n\n\n      this.controls.new_theta = 0;\n      this.controls.new_phi = 1.4;\n      if (min !== undefined) {\n        this.updatePoints(min, max)\n      }\n\n      this.controls.update()\n\n    }\n\n    this.setComponentViewFinal = function(min, max, center) {\n      center = center === undefined ? min + (max - min) / 2 : center;\n      let side = Math.max(center - min[0], max[0] - center)\n      this.controls.noTransitionAnimation = true;\n      this.controls.noLifeZoom = true;\n      this.controls.noAutoZoom = true;\n\n      this.camera.near = 0.1;\n      this.camera.far = 50000;\n      this.camera.updateProjectionMatrix()\n\n      this.controls.screenEdgeOffset = {left: 15, right: 15, top: 0, bottom: 0}\n      this.geometryOffset = {left: 0, right: 0, top: 50, bottom: 50, front: 0, back: 0}\n      this.controls.target.x = center + this.geometryOffset.right - this.geometryOffset.left;\n      this.controls.target.y = (\n          min[1] + max[1]\n          ) / 2 + (\n              this.geometryOffset.top\n              - this.geometryOffset.bottom\n              )\n      this.controls.snapTo = [\n          { theta: 0, phi: 1.4},\n      ]\n      this.controls.update()\n      this.controls.noAutoZoom = false;\n      // this.controls.new_theta = 0;\n      // this.controls.new_phi = 1.4; //1.4 Math.PI / 2;\n      this.updatePoints(\n          {x: center - side, y: min[1], z: min[2]},\n          {x: center + side, y: max[1], z: max[2]},\n      )\n\n      // this.controls.noSnap = true;\n      // this.controls.noSnapReal = true;\n\n      this.controls.update()\n\n      // this.controls.updateZoom()\n\n      this.controls.noZoom = true;\n      this.controls.noPan = false;\n      // this.controls.noYPan = true;\n      this.controls.noRotate = true;\n      this.controls.noAutoZoom = true;\n      this.controls.mouseButtons = { PAN: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, ORBIT: THREE.MOUSE.RIGHT }\n      this.controls.touchMode = { ORBIT: 4, ZOOM: 2, PAN: 3, ZOOMPAN: 1 };\n      this.controls.noTransitionAnimation = false;\n      // this.updateGeometry(\n      //     {x: min[0], y: min[1], z: min[2]},\n      //     {x: max[0], y: max[1], z: max[2]},\n      // )\n      // this.controls.noAutoZoom = true;\n          // proxy.initedCam = true;\n      // } else {\n      //     this.controls.target.x = (min[0] + max[0]) / 2\n      //     this.controls.update()\n      //     this.updateGeometry(\n      //         {x: min[0], y: min[1], z: min[2]},\n      //         {x: max[0], y: max[1], z: max[2]},\n      //     )\n      // }\n\n    }  \n\n  this.setShelfView = function(min, max) {\n    console.log(\">>>> SHELF VIEW2\")\n    this.controls.noZoom = true;\n    this.controls.noPan = true;\n    this.controls.noRotate = false;\n    this.controls.noAutoZoom = false;\n    this.controls.mouseButtons = { ORBIT: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.RIGHT }\n    this.controls.touchMode = { ORBIT: 1, ZOOM: 2, PAN: 3, ZOOMPAN: 4 };\n    if (min !== undefined) this.updateGeometry(min, max);\n  }\n\n  this.setComponentView = function(min, max) {\n    console.log(\">>>> COMPONENT VIEW\")\n    this.controls.noZoom = true;\n    this.controls.noPan = true;\n    this.controls.noRotate = false;\n    this.controls.noAutoZoom = false;\n    this.controls.mouseButtons = { ORBIT: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.RIGHT }\n    this.controls.touchMode = { ORBIT: 1, ZOOM: 2, PAN: 3, ZOOMPAN: 4 };\n    // this.controls.noZoom = true;\n    // this.controls.noPan = false;\n    // this.controls.noYPan = true;\n    // this.controls.noRotate = true;\n    // this.controls.noAutoZoom = true;\n    this.controls.new_theta = 0;\n    this.controls.new_phi = Math.PI / 2;\n    // this.controls.mouseButtons = { ORBIT: THREE.MOUSE.RIGHT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.LEFT }\n    // this.controls.touchMode = { ORBIT: 4, ZOOM: 2, PAN: 3, ZOOMPAN: 1 };\n    if (min !== undefined) this.updateGeometry(min, max);\n  }\n\n  this.setView = function (view_name) {\n    switch (view_name) {\n      case 'front':\n        this.controls.new_phi = Math.PI / 2\n        this.controls.new_theta = 0\n        break;\n      case 'left':\n        this.controls.new_phi = 1.1\n        this.controls.new_theta = -0.8\n        break;\n      case 'left_straight':\n        this.controls.new_phi = Math.PI / 2\n        this.controls.new_theta = -Math.PI / 2\n        break;\n      case 'right':\n        this.controls.new_phi = 0.8\n        this.controls.new_theta = 1\n        break;\n      case 'right_straight':\n        this.controls.new_phi = Math.PI / 2\n        this.controls.new_theta = Math.PI / 2\n        break;\n      case 'top':\n        this.controls.new_phi = 0.3\n        this.controls.new_theta = 0\n        break;\n      case 'top_straight':\n        this.controls.new_phi = 0\n        this.controls.new_theta = 0\n        break;\n    }\n    this.controls.noSnapReal = true;\n    this.controls.target.set(0, 0, 0);\n    this.controls.updateCameraRange()\n    this.controls.update()\n  }\n\n  // camera.projectionMatrix nie działało więc zrobiłem na piechotę :(\n  this.setCamSettings = function (params) {\n    this.fov = params.fov;\n    this.range = params.range;\n    this.target = params.target;\n    this.theta = this.controls.new_theta;\n    this.phi = this.controls.new_phi;\n    this.position = params.position;\n    // this.camera.projectionMatrix.set(...params);\n    // this.scene.updateMatrixWorld();\n    this.updateCamera()\n  }\n\n  this.getCamSettings = function () {\n    // this.camera.matrix.elements\n    return {\n      fov: this.fov,\n      range: this.range,\n      target: this.target,\n      rotation: this.rotation, // TODO\n      position: this.position,\n    };\n  }\n\n  this.init();\n}", "var arrayWithHoles = require(\"./arrayWithHoles\");\n\nvar iterableToArray = require(\"./iterableToArray\");\n\nvar nonIterableRest = require(\"./nonIterableRest\");\n\nfunction _toArray(arr) {\n  return arrayWithHoles(arr) || iterableToArray(arr) || nonIterableRest();\n}\n\nmodule.exports = _toArray;", "import * as THREE from 'three'\n\n// Obiekt odpowiada za:\n// - obliczanie poprawnej wartosci ZOOM gwarantujacej utrzymanie zadanych punktow wewnatrz frustrum\n// Obiekt NIE odpowiada za:\n// - manipulacje kamera, obliczaniem jej katow i transformacji\n\n\nexport function tylkoCameraAutoZoom(scene) {\n    this.points = [  new THREE.Vector3(-1000,-1000,-1000),  new THREE.Vector3(1000, 1000, 1000)];\n    this.frustum = new THREE.Frustum();\n    this.horizontalPlane = new THREE.Plane()\n    this.verticalPlane = new THREE.Plane()\n    this.cameraPlanesOffset = 50\n    \n    var tempPoint1 = new THREE.Vector3()\n    var tempPoint2 = new THREE.Vector3()\n    \n    var deg2rad = Math.PI/180;\n\n    // TEMP\n    // let helper_h = new THREE.PlaneHelper( this.horizontalPlane, 1000, 0xffff00 );  // Pionowa os\n    // let helper_v = new THREE.PlaneHelper( this.verticalPlane, 1000, 0xaaaa00 );  // Pionowa os\n\n    // this.distTop = 0\n    // this.distTarget = 0\n    // this.distOffset = 0\n    \n    // this.pointMin = new THREE.Vector3();\n    // this.pointMax = new THREE.Vector3();\n    // let mat = new THREE.MeshLambertMaterial({color: 0xCC0000})\n    // this.sphereMin = new THREE.Mesh( new THREE.SphereGeometry(4, 4, 4), mat );\n    // this.sphereMax = new THREE.Mesh( new THREE.SphereGeometry(4, 4, 4), mat );\n    // mat = new THREE.MeshLambertMaterial({color: 0xAAAA00})\n    // var sphereTempPoint = new THREE.Mesh( new THREE.SphereGeometry(15, 16, 16), mat );\n    // var sphereTempProject = new THREE.Mesh( new THREE.SphereGeometry(15, 16, 16), mat );\n    \n    // if (scene !== undefined) {\n    //     scene.add(helper_h);\n    //     scene.add(helper_v);\n    //     scene.add( this.sphereMin );\n    //     scene.add( this.sphereMax );\n    //     scene.add( sphereTempPoint );\n    //     scene.add( sphereTempProject );\n    // }\n\n\n    this.calculateNearFarPosition = function(camera, controls) {\n        let pointDistances = []\n        this.points.forEach(p => {\n            pointDistances.push(p.distanceTo(controls.target))\n        });\n        let camDistance = camera.position.distanceTo(controls.target)\n        let maxDistance = Math.max(...pointDistances)\n        return [\n            Math.max(0, camDistance - maxDistance - this.cameraPlanesOffset),\n             camDistance + maxDistance + this.cameraPlanesOffset\n            ]\n    }\n\n    this.updatePoints = function(points) {\n        this.points = points;\n\n        // this.pointMin = points[0]\n        // this.sphereMin.position.x = this.pointMin.x\n        // this.sphereMin.position.y = this.pointMin.y\n        // this.sphereMin.position.z = this.pointMin.z\n        // this.pointMax = points[1]\n        // this.sphereMax.position.x = this.pointMax.x\n        // this.sphereMax.position.y = this.pointMax.y\n        // this.sphereMax.position.z = this.pointMax.z\n    }\n\n    this.updatePlanes = function(camera, controls) {\n        this.frustum.setFromMatrix( new THREE.Matrix4().multiplyMatrices( camera.projectionMatrix, camera.matrixWorldInverse ) );\n        this.frustum.planes[1].projectPoint( controls.target, tempPoint1)\n        this.frustum.planes[2].projectPoint( controls.target, tempPoint2)\n        \n        this.horizontalPlane.setFromCoplanarPoints( controls.target, camera.position, tempPoint1)\n        this.verticalPlane.setFromCoplanarPoints( controls.target, camera.position, tempPoint2)\n    }\n\n    this.calculateDistances = function(point, angle, camPos, targetPos, planeA, planeB) {\n        // Map point to vertical Plane\n        planeA.projectPoint( point, tempPoint1)\n        // Map vertical projection to horizontal Plane\n        planeB.projectPoint( tempPoint1, tempPoint2)\n        // Calc distances\n        // let distTop = this.horizontalPlane.distanceToPoint(tempPoint1)\n        // Calc distances\n        let distTop = tempPoint1.distanceTo(tempPoint2)\n        let distTarget = targetPos.distanceTo(tempPoint2)\n        let distPointToCam = camPos.distanceTo(tempPoint2)\n        let distTargetToCam = camPos.distanceTo(targetPos)\n        distTarget = distPointToCam < distTargetToCam ? distTarget : -distTarget\n\n        let distZoom = distTop / Math.tan(angle) + distTarget\n\n        // this.distOffset = distZoom\n        // this.distTarget = distTarget\n        // this.distTop = distTop\n        // this.distCamera = targetPos.distanceTo(camPos)\n\n        // sphereTempPoint.position.x = tempPoint1.x\n        // sphereTempPoint.position.y = tempPoint1.y\n        // sphereTempPoint.position.z = tempPoint1.z\n        // sphereTempProject.position.x = tempPoint2.x\n        // sphereTempProject.position.y = tempPoint2.y\n        // sphereTempProject.position.z = tempPoint2.z\n\n        return distZoom\n    }\n\n    this.calculateZoom = function(camera, controls) {\n        // Calc frustrum Angles\n        let zoomDistances = []\n        let angle = camera.fov / 2 * deg2rad  // frustrum vertical angle\n        this.points.forEach(p => {\n            zoomDistances.push(\n                this.calculateDistances(\n                    p, angle, camera.position, controls.target,\n                    this.verticalPlane, this.horizontalPlane\n                )\n            )\n        });\n        angle *= camera.aspect\n        this.points.forEach(p => {\n            zoomDistances.push(\n                this.calculateDistances(\n                    p, angle, camera.position, controls.target,\n                    this.horizontalPlane, this.verticalPlane\n                )\n            )\n        });\n\n        return Math.max(...zoomDistances) // Zoom\n\n    }\n\n}  ", "import * as THREE from 'three';\nimport { tylkoCameraAutoZoom } from './tylko-camera-autozoom'\n\n// Obiekt odpowiada za:\n// - operowanie kamera i obsluge manupilacji kamera przez uzytkownika\n// - obliczanie przejsc kamery pomiedzy stanami\n// Obiekt NIE odpowiada za:\n// - obliczanie zoomu kamery dla zadanych punktow ktore maja byc wewnatrz frustrum\n\nTHREE.OrbitControls = function (object, domElement, scene) {\n\n\tthis.object = object;\n\tthis.domElement = (domElement !== undefined) ? domElement : document;\n\t// this.autoZoomObj = new tylkoCameraAutoZoom(scene);\n\tthis.points = [\n\t\tnew THREE.Vector3(-1000,-1000,-1000),\n\t\tnew THREE.Vector3(0, 0, 0),\n\t\tnew THREE.Vector3(0, 0, 0),\n\t\tnew THREE.Vector3(0, 0, 0),\n\t\tnew THREE.Vector3(0, 0, 0),\n\t\tnew THREE.Vector3(0, 0, 0),\n\t\tnew THREE.Vector3(0, 0, 0),\n\t\tnew THREE.Vector3(1000, 1000, 1000)\n\t];\n\t\n\tthis.enabled = true;\n\tthis.noZoom = false;\n\tthis.noAutoZoom = true;\n\tthis.noLifeZoom = true;\n\tthis.noTransitionAnimation = true;\n\tthis.noRotate = false;\n\tthis.noPan = false;\n\tthis.directionLock = false;\n\tthis.noYPan = false;\n\tthis.noSnap = true;\n\tthis.noSnapReal = this.noSnap; // faktyczna kontrola snappingu tymczasowo nadpisywana przy ustawianiu widoku\n\n\tthis.target = new THREE.Vector3();\n\tthis.targetOffset = new THREE.Vector3(0, -100, 0); // Pozycja kamery do obliczania pozycji Zoom\n\tthis.snapTo = [\n\t\t{ theta: 0.62, phi: 1.1},\n\t\t{ theta: -0.4, phi: 1.4},\n\t\t{ theta: 0, phi: Math.PI / 2},\n\t]\n\n\tthis.zoomSpeed = 1.0;\n\tthis.rotateSpeed = 2.0;\n\tthis.animationStepParameters = {\n\t\tdistance: 70,  // \"cm\" - skala jak w scenie\n\t\ttargetDistance: 1,  // \"cm\" - skala jak w scenie\n\t\tangle: 0.08,  // rad\n\t}\n\n\tthis.zoomRange = {min:0, max: Infinity}\n\tthis.polarAngle = { min: 0, max: Math.PI }\n\tthis.azimuthAngle = { min: -Infinity, max: Infinity }\n\n\tthis.mouseButtons = { ORBIT: THREE.MOUSE.LEFT, ZOOM: THREE.MOUSE.MIDDLE, PAN: THREE.MOUSE.RIGHT };\n\tthis.touchMode = { ORBIT: 1, ZOOM: 2, PAN: 3, ZOOMPAN: 4 };\n\n\t// interfejs do narzucania katow obrotu\n\tthis.new_theta = null; // AzimuthalAngle\n\tthis.new_phi = null;\n\tthis.new_target = new THREE.Vector3();\n\n\t// okresla czy geometria jest w trakcie zmiany - do scalenia z `state` ?\n\tthis.geometryFixed = true\n\n\tthis.screenEdgeOffset = { // Offset krawedzi w pixelach\n\t\tleft: 0,\n\t\tright: 0,\n\t\ttop: 0,\n\t\tbottom: 0,\n\t}\n\n\t////////////\n\t// internals\n\n\tvar scope = this;\n\n\tvar EPS = 0.000001;\n\n\tvar rotateStart = new THREE.Vector2();\n\tvar rotateEnd = new THREE.Vector2();\n\tvar rotateDelta = new THREE.Vector2();\n\n\tvar panStart = new THREE.Vector2();\n\tvar panEnd = new THREE.Vector2();\n\tvar panDelta = new THREE.Vector2();\n\tvar panOffset = new THREE.Vector3();\n\n\tvar offset = new THREE.Vector3();\n\n\tvar dollyStart = new THREE.Vector2();\n\tvar dollyEnd = new THREE.Vector2();\n\tvar dollyDelta = new THREE.Vector2();\n\n\tvar snapped;\n\n\tvar zoom;\n\tvar theta; // AzimuthalAngle\n\tvar phi;  // PolarAngle\n\t\n\tvar newZoom;\n\tvar newTheta;\n\tvar newPhi;\n\tvar newTarget;\n\t\n\tvar thetaDelta = 0;\n\tvar phiDelta = 0;\n\t\n\tvar scale = 1;\n\tvar pan = new THREE.Vector3();\n\tvar tempPoint = new THREE.Vector3();\n\n\tvar animationParameters = {\n\t\tcounter: 0,\n\t\tthetaDiff: 0, \n\t\tphiDiff: 0,\n\t\tzoomDiff: 0,\n\t\ttheta: 0, \n\t\tphi: 0,\n\t\tzoom: 0,\n\t}\n\n\tvar STATE = { NONE: -1, ROTATE: 0, DOLLY: 1, PAN: 2, TOUCH_ROTATE: 3, TOUCH_DOLLY: 4, TOUCH_PAN: 5, TOUCH_ZOOMPAN: 6, ANIMATE: 7 };\n\tvar state = STATE.NONE;\n\n\t// so camera.up is the orbit axis\n\tvar quat = new THREE.Quaternion().setFromUnitVectors(object.up, new THREE.Vector3(0, 1, 0));\n\tvar quatInverse = quat.clone().inverse();\n\n\t// events\n\t// var startEvent = { type: 'start' };\n\t// var endEvent = { type: 'end' };\n\tvar changeEvent = { type: 'change' };\n\tvar animateEvent = { type: 'render' };\n\n\t// pass in distance in world space to move left\n\tthis.panLeft = function (distance) {\n\t\tvar te = this.object.matrix.elements;\n\t\t// get X column of matrix\n\t\tpanOffset.set(te[0], te[1], te[2]);\n\t\tpanOffset.multiplyScalar(- distance);\n\t\tpan.add(panOffset);\n\t};\n\n\t// pass in distance in world space to move up\n\tthis.panUp = function (distance) {\n\t\tvar te = this.object.matrix.elements;\n\t\t// get Y column of matrix\n\t\tpanOffset.set(te[4], te[5], te[6]);\n\t\tpanOffset.multiplyScalar(distance);\n\t\tpan.add(panOffset);\n\t};\n\n\t// pass in x,y of change desired in pixel space,\n\t// right and down are positive\n\tthis.pan = function (deltaX, deltaY) {\n\t\tvar element = scope.domElement === document ? scope.domElement.body : scope.domElement;\n\n\t\tvar position = scope.object.position;\n\t\tvar offset = position.clone().sub(scope.target);\n\t\tvar targetDistance = offset.length();\n\t\t// half of the fov is center to top of screen\n\t\ttargetDistance *= Math.tan((scope.object.fov / 2) * Math.PI / 180.0);\n\t\t// we actually don't use screenWidth, since perspective camera is fixed to screen height\n\t\tscope.panLeft(2 * deltaX * targetDistance / element.clientHeight);\n\t\tif (!scope.noYPan) scope.panUp(2 * deltaY * targetDistance / element.clientHeight);\n\t};\n\n\tthis.dollyIn = function (dollyScale) {\n\t\tif (dollyScale === undefined) dollyScale = getZoomScale();\n\t\tscale /= dollyScale;\n\t};\n\n\tthis.dollyOut = function (dollyScale) {\n\t\tif (dollyScale === undefined) dollyScale = getZoomScale();\n\t\tscale *= dollyScale;\n\t};\n\n\tthis.updateCameraRange = function () {\n\t\tlet dist = 0;\n        scope.points.forEach(p => {\n\t\t\tdist = Math.max(scope.target.distanceTo(p), dist)\n        });\n\t\t\n\t\tlet distTargetToCam = scope.object.position.distanceTo(scope.target)\n\t\tthis.object.near = Math.max(distTargetToCam - dist, 0.1);\n\t\tthis.object.far = Math.max(distTargetToCam + dist, 1000);\n\t\tthis.object.updateProjectionMatrix()\n\t}\n\n\tfunction checkRange() {\n\t\tlet distT = 100000;\n\t\tlet distC = 100000;\n\t\tscope.points.forEach(p => {\n\t\t\tdistT = Math.min(scope.target.distanceTo(p), distT)\n\t\t\tdistC = Math.min(scope.object.position.distanceTo(p), distC)\n\t\t});\n\t\tconsole.log(\">>>> CR\", distT, distC, \"||\", scope.object.near, scope.object.far, scope.object.position.distanceTo(scope.target))\n\t}\n\n\tfunction calculateAcutalCamParams() {\n\t\toffset.copy(scope.object.position).sub(scope.target);\n\t\toffset.applyQuaternion(quat);  // rotate offset to \"y-axis-is-up\" space\n\t\ttheta = Math.atan2(offset.x, offset.z)\n\t\tphi = Math.atan2(Math.sqrt(offset.x * offset.x + offset.z * offset.z), offset.y)\n\n\t\tsnapped = false\n\t\tzoom = offset.length()\n\t\tnewZoom = zoom\n\t    newTheta = theta\n\t\tnewPhi = phi\n\t\tnewTarget = scope.target\n\t}\n\n\tfunction calculateStaticCamParams () {\n\t\tlet snap = getClosestSnap(phi, theta, scope.snapTo)\n\t\t\n\t\tif ( !scope.noSnapReal && checkCamDiff(snap.theta, snap.phi) ) {\n\t\t\tnewTheta = snap.theta;\n\t\t\tnewPhi = snap.phi;\n\t\t\tsnapped = true\n\t\t\n\t\t} else if ( checkCamDiff(scope.new_theta, scope.new_phi) ) {\n\t\t\tnewTheta = scope.new_theta\n\t\t\tnewPhi = scope.new_phi\n\t\t}\n\t\tif (scope.new_target !== null) newTarget = scope.new_target;\n\t\tnewZoom = zoom * scale;\n\n\t}\n\n\tthis.updateZoom = function () {\n\t\tcalculateRealZoom()\n\t\tthis.update()\n\t}\n\n\tfunction calculateRealZoom() {\n\t\tlet pixelWidth = 2 / scope.domElement.clientWidth;\n\t\tlet pixelHeight = 2 / scope.domElement.clientHeight;\n\n\t\tlet distX = [];\n\t\tlet distY = [];\n\t\t\n        scope.points.forEach(p => {\n            tempPoint.copy(p).project(scope.object);\n\t\t\tdistX.push(tempPoint.x + (tempPoint.x < 0 ? -pixelWidth * scope.screenEdgeOffset.left : pixelWidth * scope.screenEdgeOffset.right ))\n\t\t\tdistY.push(tempPoint.y + (tempPoint.y < 0 ? -pixelHeight * scope.screenEdgeOffset.bottom : pixelHeight * scope.screenEdgeOffset.top ))\n        });\n\t\tdistX = distX.filter(isFinite)\n\t\tdistY = distY.filter(isFinite)\n        let distTargetToCam = scope.object.position.distanceTo(scope.target)\n\t\tlet max = Math.max(...distX, ...distY, Math.abs(Math.min(...distX, ...distY)))\n\t\tdistTargetToCam = distTargetToCam * max\n\n\t\tnewZoom = distTargetToCam || zoom\n\t}\n\n\t// this.updateUpfrontZoom = function() {\n\t// \t// calculateAcutalCamParams()\n\t// \t// scope.object.position.copy(scope.target).add(scope.targetOffset);\n\t// \t// scope.object.position.set(0, 0, 100)\n\t// \t// scope.object.updateProjectionMatrix()\n\t// \t// scope.object.lookAt(0, 0, 0)\n\t// \t// console.log(newZoom, scope.object.position, scope.object)\n\n\t// \t// this.updateZoom()\n\t// \tnewZoom = calculateRealZoom() || zoom\n\t\n\t// \t// newTheta = tempTheta\n\t// \t// newPhi = tempPhi\n\t// }\n\n\t// this.updateZoom = function() {\n\t// \tthis.autoZoomObj.updatePlanes(this.object, this)\n\t// \tnewZoom = this.autoZoomObj.calculateZoom(this.object, this)\n\t// }\n\n\tfunction restrictCamParams() {\n\t\t// restrict theta to be between desired limits\n\t\tnewTheta = Math.max(scope.azimuthAngle.min, Math.min(scope.azimuthAngle.max, newTheta));\n\t\t// restrict phi to be between desired limits\n\t\tnewPhi = Math.max(scope.polarAngle.min, Math.min(scope.polarAngle.max, newPhi));\n\t\t// restrict newZoom to be between desired limits\n\t\tnewZoom = Math.max(scope.zoomRange.min, Math.min(scope.zoomRange.max, newZoom));\n\t}\n\n\tfunction checkCamDiff(testTheta, testPhi, testZoom, testTarget) {\n\t\tlet t = testTheta !== undefined ? Math.abs(theta - testTheta) > scope.animationStepParameters.angle : false;\n\t\tlet p = testPhi !== undefined ? Math.abs(phi - testPhi) > scope.animationStepParameters.angle : false;\n\t\tlet z = testZoom !== undefined ? Math.abs(zoom - testZoom) > 10 : false;\n\t\tlet tr = testTarget !== undefined ? Math.abs(scope.target.x - testTarget.x) > 1 || Math.abs(scope.target.y - testTarget.y) > 1 || Math.abs(scope.target.z - testTarget.z) > 1 : false;\n\t\treturn t || p || z || tr\n\t}\n\n\tfunction calculateAnimationFirstStep() {\n\t\t// Obliczam parametry\n\t\tlet animationSpeed = Math.round(Math.max(\n\t\t\tMath.abs(newTheta - theta) / scope.animationStepParameters.angle,\n\t\t\tMath.abs(newPhi - phi) / scope.animationStepParameters.angle,\n\t\t\tMath.abs(newZoom - zoom) / scope.animationStepParameters.distance,\n\t\t\t5,  // Nie mniej niż 5 klatek\n\t\t))\n\t\tstate = STATE.ANIMATE\n\t\tanimationParameters = {\n\t\t\tcounter: animationSpeed,\n\t\t\tthetaDiff: (newTheta - theta) / animationSpeed, \n\t\t\tphiDiff: (newPhi - phi) / animationSpeed,\n\t\t\tzoomDiff: (newZoom - zoom) / animationSpeed,\n\t\t\ttheta: newTheta,\n\t\t\tphi: newPhi,\n\t\t\tzoom: newZoom,\n\t\t}\n\t\t// Obliczam pierwszy krok\n\t\tcalculateAnimationNextStep()\n\t}\n\n\n\tfunction calculateDynamicCamParams() {\n\t\tnewTheta = Math.atan2(offset.x, offset.z) + thetaDelta\n\t\tnewPhi = Math.atan2(Math.sqrt(offset.x * offset.x + offset.z * offset.z), offset.y) + phiDelta\n\t\tnewZoom = zoom * scale;\n\t\tnewTarget = scope.target.clone()\n\t\tnewTarget.add(pan);\n\t}\n\n\tfunction calculateAnimationNextStep () {\n\t\t// if (animationParameters.first === true){\n\t\t// \tanimationParameters.zoomDiff = (newZoom - zoom) / (animationParameters.counter)\n\t\t// \tanimationParameters.zoom = newZoom\n\t\t// \tanimationParameters.first = false\n\t\t// }\n\t\tif (animationParameters.counter <= 0) {\n\t\t\tnewTheta = animationParameters.theta\n\t\t\tnewPhi = animationParameters.phi\n\t\t\tnewZoom = animationParameters.zoom\n\t\t\tstate = STATE.NONE\n\t\t} else {\n\t\t\tnewTheta = theta + animationParameters.thetaDiff\n\t\t\tnewPhi = phi + animationParameters.phiDiff\n\t\t\tnewZoom = zoom + animationParameters.zoomDiff\n\t\t\tanimationParameters.counter -= 1\n\t\t}\n\t}\n\n\tfunction setCamParams() {\n\t\t// ---------- Ustawiam kamerę ------------------------------------------------\n\t\t// Ograniczam phi zgodnie z parametrem EPS\n\t\tnewPhi = Math.max(EPS, Math.min(Math.PI - EPS, newPhi));\n\t\t// Kalkuluje poprawny offset\n\t\toffset.x = newZoom * Math.sin(newPhi) * Math.sin(newTheta);\n\t\toffset.y = newZoom * Math.cos(newPhi);\n\t\toffset.z = newZoom * Math.sin(newPhi) * Math.cos(newTheta);\n\t\toffset.applyQuaternion(quatInverse); // rotate offset back to \"camera-up-vector-is-up\" space\n\t\t// Move target to panned location\n\t\tscope.target = newTarget;\n\t\t// Set cam params\n\t\tscope.object.position.copy(scope.target).add(offset);\n\t\tscope.object.near = Math.max(1, scope.object.near - (zoom - newZoom));\n\t\tscope.object.far = scope.object.far - (zoom - newZoom);\n\t\tscope.object.lookAt(scope.target);\n\n\t\t// Reset values\n\t\tthetaDelta = 0;\n\t\tphiDelta = 0;\n\t\tscope.new_theta = newTheta;\n\t\tscope.new_phi = newPhi;\n\t\tscope.new_target.set(newTarget.x, newTarget.y, newTarget.z);\n\t\tscale = 1;\n\t\tpan.set(0, 0, 0);\n\t}\n\n\tthis.update = function () {\n\t\t// Brak zmian, jesli geometria jest w trakcie zmian oraz jest wylaczony AutoZoom\n\t\tif (!this.geometryFixed && this.noLifeZoom) return; //!this.geometryFixed && \n\t\tcalculateAcutalCamParams()  // Uaktualniam stan zmiennych offset, theta, phi\n\t\tswitch (state) {\n\t\t\tcase STATE.NONE:\n\t\t\t\tcalculateStaticCamParams();\n\t\t\t\tif (\n\t\t\t\t\t!snapped\n\t\t\t\t\t&& \n\t\t\t\t\t(\n\t\t\t\t\t\t!this.noAutoZoom\n\t\t\t\t\t\t|| (!this.noLifeZoom && this.geometryFixed === true)\n\t\t\t\t\t\t|| (!this.noLifeZoom && state === STATE.NONE)\n\t\t\t\t\t)\n\t\t\t\t) {\n\t\t\t\t\tcalculateRealZoom();\n\t\t\t\t}\n\t\t\t\tif (!this.noTransitionAnimation && checkCamDiff(newTheta, newPhi, newZoom)) {\n\t\t\t\t\tcalculateAnimationFirstStep()\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t\n\t\t\tcase STATE.ANIMATE:\n\t\t\t\tcalculateAnimationNextStep();\t\n\t\t\t\tbreak\n\n\t\t\tdefault: // W trakcie pracy z kamera przez usera\n\t\t\t\tcalculateDynamicCamParams()\n\t\t\t\trestrictCamParams();\n\t\t\t\tif ( !this.noAutoZoom && !this.noLifeZoom ) calculateRealZoom();\n\t\t}\n\t\tsetCamParams()\n\t\t// checkRange()\n\t\tswitch (state) {\n\t\t\t// case STATE.NONE:\n\t\t\t// \tconsole.log(\">>>>> END\")\n\t\t\t// \tscope.dispatchEvent(endEvent);\n\t\t\t// \tbreak;\n\t\t\tcase STATE.ANIMATE:\n\t\t\t\tscope.dispatchEvent(animateEvent);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tscope.dispatchEvent(changeEvent)\n\t\t}\n\t};\n\n\tthis.getPolarAngle = function () {\n\t\treturn phi;\n\t};\n\n\tthis.getAzimuthalAngle = function () {\n\t\treturn theta\n\t};\n\n\tfunction getZoomScale() {\n\t\treturn Math.pow(0.95, scope.zoomSpeed);\n\t}\n\n\tfunction getClosestSnap(currentPhi, currentTheta, availbleSnappingStates) {\n\t\tlet closest, diff, optionDiff;\n\t\t([closest, ...availbleSnappingStates] = availbleSnappingStates);\n\t\tdiff = Math.abs(closest.theta - currentTheta) // + Math.abs(closest.phi - currentPhi)\n\t\tavailbleSnappingStates.forEach(function(option) {\n\t\t\toptionDiff = Math.abs(option.theta - currentTheta) // + Math.abs(option.phi - currentPhi)\n\t\t\tif (diff > optionDiff) {\n\t\t\t\tclosest = option;\n\t\t\t\tdiff = optionDiff;\n\t\t\t}\n\t\t  });\n\t\treturn closest\n\t}\n\n\tfunction onMouseDown(event) {\n\n\t\tif (scope.enabled === false) return;\n\t\tevent.preventDefault();\n\n\t\tif (event.button === scope.mouseButtons.ORBIT) {\n\t\t\tif (scope.noRotate === true) return;\n\n\t\t\tstate = STATE.ROTATE;\n\t\t\trotateStart.set(event.clientX, event.clientY);\n\n\t\t} else if (event.button === scope.mouseButtons.ZOOM) {\n\t\t\tif (scope.noZoom === true) return;\n\n\t\t\tstate = STATE.DOLLY;\n\t\t\tdollyStart.set(event.clientX, event.clientY);\n\n\t\t} else if (event.button === scope.mouseButtons.PAN) {\n\t\t\tif (scope.noPan === true) return;\n\n\t\t\tstate = STATE.PAN;\n\t\t\tpanStart.set(event.clientX, event.clientY);\n\n\t\t}\n\t\tif (state !== STATE.NONE) {\n\t\t\tdocument.addEventListener('mousemove', onMouseMove, false);\n\t\t\tdocument.addEventListener('mouseup', onMouseUp, false);\n\t\t\t// scope.dispatchEvent(startEvent);\n\t\t}\n\n\t}\n\n\tfunction onMouseMove(event) {\n\n\t\tif (scope.enabled === false) return;\n\n\t\tevent.preventDefault();\n\n\t\tvar element = scope.domElement === document ? scope.domElement.body : scope.domElement;\n\n\t\tif (state === STATE.ROTATE) {\n\n\t\t\tif (scope.noRotate === true) return;\n\n\t\t\trotateEnd.set(event.clientX, event.clientY);\n\t\t\trotateDelta.subVectors(rotateEnd, rotateStart);\n\n\t\t\t// rotating across whole screen goes 360 degrees around\n\t\t\tthetaDelta -= 2 * Math.PI * rotateDelta.x / element.clientWidth * scope.rotateSpeed;\n\n\t\t\t// rotating up and down along whole screen attempts to go 360, but limited to 180\n\t\t\tphiDelta -= 2 * Math.PI * rotateDelta.y / element.clientHeight * scope.rotateSpeed;\n\n\t\t\trotateStart.copy(rotateEnd);\n\n\t\t} else if (state === STATE.DOLLY) {\n\n\t\t\tif (scope.noZoom === true) return;\n\n\t\t\tdollyEnd.set(event.clientX, event.clientY);\n\t\t\tdollyDelta.subVectors(dollyEnd, dollyStart);\n\n\t\t\tif (dollyDelta.y > 0) {\n\n\t\t\t\tscope.dollyIn();\n\n\t\t\t} else if (dollyDelta.y < 0) {\n\n\t\t\t\tscope.dollyOut();\n\n\t\t\t}\n\n\t\t\tdollyStart.copy(dollyEnd);\n\n\t\t} else if (state === STATE.PAN) {\n\n\t\t\tif (scope.noPan === true) return;\n\n\t\t\tpanEnd.set(event.clientX, event.clientY);\n\t\t\tpanDelta.subVectors(panEnd, panStart);\n\n\t\t\tscope.pan(panDelta.x, panDelta.y);\n\n\t\t\tpanStart.copy(panEnd);\n\n\t\t}\n\t\tif (state !== STATE.NONE) {\n\t\t\tscope.update();\n\t\t\tscope.noSnapReal = scope.noSnap\n\t\t}\n\n\t}\n\n\tfunction onMouseUp( /* event */) {\n\t\tif (scope.enabled === false) return;\n\n\t\tdocument.removeEventListener('mousemove', onMouseMove, false);\n\t\tdocument.removeEventListener('mouseup', onMouseUp, false);\n\t\t// scope.dispatchEvent(endEvent);\n\t\tstate = STATE.NONE;\n\t\tscope.update()\n\n\t}\n\n\tfunction onMouseWheel(event) {\n\t\tif (scope.enabled === false || scope.noZoom === true || state !== STATE.NONE) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t\tvar delta = 0;\n\n\t\tif (event.wheelDelta !== undefined) { // WebKit / Opera / Explorer 9\n\n\t\t\tdelta = event.wheelDelta;\n\n\t\t} else if (event.detail !== undefined) { // Firefox\n\n\t\t\tdelta = - event.detail;\n\n\t\t}\n\t\tif (delta > 0) {\n\n\t\t\tscope.dollyOut();\n\n\t\t} else if (delta < 0) {\n\n\t\t\tscope.dollyIn();\n\n\t\t}\n\t\tscope.noSnapReal = scope.noSnap\n\t\tscope.update();\n\t\t// scope.dispatchEvent(startEvent);\n\t\t// scope.dispatchEvent(endEvent);\n\n\t}\n\n\tfunction touchstart(event) {\n\t\tif (scope.enabled === false) return;\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase scope.touchMode.ORBIT:\t// one-fingered touch: rotate\n\n\t\t\t\tif (scope.noRotate === true) return;\n\n\t\t\t\tstate = STATE.TOUCH_ROTATE;\n\n\t\t\t\trotateStart.set(event.touches[0].pageX, event.touches[0].pageY);\n\t\t\t\tbreak;\n\n\t\t\tcase scope.touchMode.ZOOM:\t// two-fingered touch: dolly\n\n\t\t\t\tif (scope.noZoom === true) return;\n\n\t\t\t\tstate = STATE.TOUCH_DOLLY;\n\n\t\t\t\tvar dx = event.touches[0].pageX - event.touches[1].pageX;\n\t\t\t\tvar dy = event.touches[0].pageY - event.touches[1].pageY;\n\t\t\t\tvar distance = Math.sqrt(dx * dx + dy * dy);\n\t\t\t\tdollyStart.set(0, distance);\n\t\t\t\tbreak;\n\n\t\t\tcase scope.touchMode.PAN: // three-fingered touch: pan\n\n\t\t\t\tif (scope.noPan === true) return;\n\n\t\t\t\tstate = STATE.TOUCH_PAN;\n\n\t\t\t\tpanStart.set(event.touches[0].pageX, event.touches[0].pageY);\n\t\t\t\tbreak;\n\n\t\t\tcase scope.touchMode.ZOOMPAN: // three-fingered touch: pan\n\t\t\t\tstate = STATE.TOUCH_ZOOMPAN;\n\t\t\t\tscope.directionLock = true;\n\n\t\t\t\tpanStart.set(event.touches[0].pageX, event.touches[0].pageY);\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\n\t\t\t\tstate = STATE.NONE;\n\n\t\t}\n\n\t\t// if (state !== STATE.NONE) scope.dispatchEvent(startEvent);\n\n\t}\n\n\tfunction lockDirection(delta) {\n\t\tswitch (scope.directionLock) {\n\t\t\tcase false:\n\t\t\t\treturn delta\n\t\t\tcase 'horizontal':\n\t\t\t\tdelta.setY(0)\n\t\t\t\treturn delta\n\t\t\tcase 'vertrical':\n\t\t\t\tdelta.setX(0)\n\t\t\t\treturn delta\n\t\t\tdefault:\n\t\t\t\tscope.directionLock = Math.abs(delta.x) > Math.abs(delta.y) ? 'horizontal' : 'vertrical';\n\t\t\t\treturn lockDirection(delta)\n\n\t\t}\n\t}\n\n\tfunction touchmove(event) {\n\n\t\tif (scope.enabled === false) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tvar element = scope.domElement === document ? scope.domElement.body : scope.domElement;\n\n\t\tswitch (event.touches.length) {\n\n\t\t\tcase scope.touchMode.ORBIT: // one-fingered touch: rotate\n\n\t\t\t\tif (scope.noRotate === true) return;\n\t\t\t\tif (state !== STATE.TOUCH_ROTATE) return;\n\n\t\t\t\trotateEnd.set(event.touches[0].pageX, event.touches[0].pageY);\n\t\t\t\trotateDelta.subVectors(rotateEnd, rotateStart);\n\t\t\t\trotateDelta = lockDirection(rotateDelta)\n\n\t\t\t\t// rotating across whole screen goes 360 degrees around\n\t\t\t\tthetaDelta -= 2 * Math.PI * rotateDelta.x / element.clientWidth * scope.rotateSpeed;\n\t\t\t\t// rotating up and down along whole screen attempts to go 360, but limited to 180\n\t\t\t\tphiDelta -= 2 * Math.PI * rotateDelta.y / element.clientHeight * scope.rotateSpeed;\n\n\t\t\t\trotateStart.copy(rotateEnd);\n\n\t\t\t\tscope.noSnapReal = scope.noSnap\n\t\t\t\tscope.update();\n\t\t\t\tbreak;\n\n\t\t\tcase scope.touchMode.ZOOM: // two-fingered touch: dolly\n\n\t\t\t\tif (scope.noZoom === true) return;\n\t\t\t\tif (state !== STATE.TOUCH_DOLLY) return;\n\n\t\t\t\tvar dx = event.touches[0].pageX - event.touches[1].pageX;\n\t\t\t\tvar dy = event.touches[0].pageY - event.touches[1].pageY;\n\t\t\t\tvar distance = Math.sqrt(dx * dx + dy * dy);\n\n\t\t\t\tdollyEnd.set(0, distance);\n\t\t\t\tdollyDelta.subVectors(dollyEnd, dollyStart);\n\n\t\t\t\tif (dollyDelta.y > 0) {\n\n\t\t\t\t\tscope.dollyOut();\n\n\t\t\t\t} else if (dollyDelta.y < 0) {\n\n\t\t\t\t\tscope.dollyIn();\n\n\t\t\t\t}\n\n\t\t\t\tdollyStart.copy(dollyEnd);\n\n\t\t\t\tscope.noSnapReal = scope.noSnap\n\t\t\t\tscope.update();\n\t\t\t\tbreak;\n\n\t\t\tcase scope.touchMode.PAN: // three-fingered touch: pan\n\t\t\t\tif (scope.noPan === true) return;\n\t\t\t\tif (state !== STATE.TOUCH_PAN) return;\n\n\t\t\t\tpanEnd.set(event.touches[0].pageX, event.touches[0].pageY);\n\t\t\t\tpanDelta.subVectors(panEnd, panStart);\n\t\t\t\tpanDelta = lockDirection(panDelta)\n\n\t\t\t\tscope.pan(panDelta.x, panDelta.y);\n\t\t\t\tpanStart.copy(panEnd);\n\n\t\t\t\tscope.noSnapReal = scope.noSnap\n\t\t\t\tscope.update();\n\t\t\t\tbreak;\n\n\t\t\tcase scope.touchMode.ZOOMPAN:\n\t\t\t\tif (scope.noPan === true) return;\n\t\t\t\tif (state !== STATE.TOUCH_ZOOMPAN) return;\n\t\t\t\tpanEnd.set(event.touches[0].pageX, event.touches[0].pageY);\n\t\t\t\tpanDelta.subVectors(panEnd, panStart);\n\t\t\t\tpanDelta = lockDirection(panDelta)\n\t\t\t\tswitch (scope.directionLock) {\n\t\t\t\t\tcase false:\n\t\t\t\t\tcase 'horizontal':\n\t\t\t\t\t\tscope.pan(panDelta.x, panDelta.y);\n\t\t\t\t\t\tpanStart.copy(panEnd);\n\t\t\n\t\t\t\t\t\tscope.noSnapReal = scope.noSnap\n\t\t\t\t\t\tscope.update();\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'vertrical':\n\t\t\t\t\t\t// rotating across whole screen goes 360 degrees around\n\t\t\t\t\t\tthetaDelta -= 2 * Math.PI * panDelta.x / element.clientWidth * scope.rotateSpeed;\n\t\t\t\t\t\t// rotating up and down along whole screen attempts to go 360, but limited to 180\n\t\t\t\t\t\tphiDelta -= 2 * Math.PI * panDelta.y / element.clientHeight * scope.rotateSpeed;\n\n\t\t\t\t\t\tpanStart.copy(panEnd);\n\n\t\t\t\t\t\tscope.noSnapReal = scope.noSnap\n\t\t\t\t\t\tscope.update();\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tstate = STATE.NONE;\n\t\t\t\t\t\tbreak;\t\t\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\tstate = STATE.NONE;\n\n\t\t}\n\n\t}\n\n\tfunction touchend( /* event */) {\n\t\tif (scope.enabled === false) return;\n\t\t// scope.dispatchEvent(endEvent);\n\t\tif (scope.directionLock !== false) scope.directionLock = true\n\t\tstate = STATE.NONE;\n\t\tscope.update();\n\n\t}\n\n\tthis.domElement.addEventListener('contextmenu', function (event) { event.preventDefault(); }, false);\n\tthis.domElement.addEventListener('mousedown', onMouseDown, false);\n\tthis.domElement.addEventListener('mousewheel', onMouseWheel, false);\n\tthis.domElement.addEventListener('DOMMouseScroll', onMouseWheel, false); // firefox\n\n\tthis.domElement.addEventListener('touchstart', touchstart, false);\n\tthis.domElement.addEventListener('touchend', touchend, false);\n\tthis.domElement.addEventListener('touchmove', touchmove, false);\n\n\t// force an update at start\n\tthis.update();\n\n};\n\nTHREE.OrbitControls.prototype = Object.create(THREE.EventDispatcher.prototype);\nTHREE.OrbitControls.prototype.constructor = THREE.OrbitControls;"], "sourceRoot": ""}