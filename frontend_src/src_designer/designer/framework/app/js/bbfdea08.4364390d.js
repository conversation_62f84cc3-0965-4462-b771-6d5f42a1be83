(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["bbfdea08"],{"480d":function(t,e,o){"use strict";o.d(e,"a",function(){return i});var n=o("e411");var a=o("5926");function i(t,e){this.fov=20;this.range={min:1,max:5e4};this.target={x:0,y:5,z:0};this.position={x:0,y:1e3,z:2e3};this.geometryOffset={left:10,right:0,top:0,bottom:10,front:0,back:0};this.geometryBox=new n["Box3"];this.camera=new n["PerspectiveCamera"](this.fov,2,this.range.min,this.range.max);this.controls=new n["OrbitControls"](this.camera,t,e);this.controls.polarAngle={min:Math.PI/4,max:Math.PI/2};this.controls.azimuthAngle={min:-Math.PI/3,max:Math.PI/3};this.dynamicTarget=true;this.init=function(){this.camera.position.set(1e3,1e3,1e3);this.camera.add(new n["PointLight"](16777215,1));this.controls.target.set(this.target.x,this.target.y,this.target.z);this.controls.new_theta=-.4;this.controls.new_phi=1.4;this.controls.update();this.controls.noAutoZoom=false;this.controls.noLifeZoom=true;this.controls.noTransitionAnimation=false;this.controls.noSnap=false;this.controls.noSnapReal=false};this.updatePoints=function(t,e){var o=this.geometryOffset;var n=[t.x-o.left,e.x+o.right,t.y-o.bottom,e.y+o.top,t.z-o.back,e.z+o.front];this.controls.points[0].set(n[0],n[2],n[4]);this.controls.points[1].set(n[1],n[3],n[5]);this.controls.points[2].set(n[0],n[2],n[5]);this.controls.points[3].set(n[0],n[3],n[4]);this.controls.points[4].set(n[0],n[3],n[5]);this.controls.points[5].set(n[1],n[2],n[4]);this.controls.points[6].set(n[1],n[2],n[5]);this.controls.points[7].set(n[1],n[3],n[4])};this.updateGeometry=function(t,e){if(this.dynamicTarget){this.target.y=.5*e.y+55;this.controls.new_target.set(this.target.x,this.target.y,this.target.z)}this.updatePoints(t,e);this.controls.update()};this.updateAspect=function(t){this.camera.aspect=t;this.camera.updateProjectionMatrix()};this.update=function(t){this.updateAspect(t);this.updateParams()};this.updateParams=function(){var t=this.camera.matrix.elements;this.position={x:t[12],y:t[13],z:t[14]};this.target=this.controls.target};this.updateCamera=function(){this.camera.fov=parseInt(this.fov);this.camera.near=parseInt(this.range.min);this.camera.far=parseInt(this.range.max);this.camera.position.set(this.position.x,this.position.y,this.position.z);this.controls.target.set(this.target.x,this.target.y,this.target.z);this.controls.update()};this.setDefaultfView=function(){this.controls.noZoom=false;this.controls.noPan=false;this.controls.noRotate=false;this.controls.noAutoZoom=false;this.controls.noLifeZoom=false;this.controls.mouseButtons={ORBIT:n["MOUSE"].LEFT,ZOOM:n["MOUSE"].MIDDLE,PAN:n["MOUSE"].RIGHT};this.controls.touchMode={ORBIT:1,ZOOM:2,PAN:3,ZOOMPAN:4}};this.setShelfViewFinal=function(t,e){console.log(">>>> SHELF VIEW");this.controls.noTransitionAnimation=true;this.controls.noZoom=true;this.controls.noPan=true;this.controls.noRotate=false;this.controls.noLifeZoom=true;this.controls.noAutoZoom=false;this.controls.animationStepParameters.distance=120;this.controls.azimuthAngle={min:-Math.PI/6*5,max:Math.PI/6*5};this.camera.near=.1;this.camera.far=5e4;this.camera.updateProjectionMatrix();this.controls.mouseButtons={ORBIT:n["MOUSE"].LEFT,ZOOM:n["MOUSE"].MIDDLE,PAN:n["MOUSE"].RIGHT};this.controls.touchMode={ORBIT:1,ZOOM:2,PAN:3,ZOOMPAN:4};this.controls.snapTo=[{theta:.52,phi:1.4},{theta:-.52,phi:1.4},{theta:0,phi:1.4}];this.geometryOffset={left:50,right:50,top:50,bottom:50,front:0,back:0};this.controls.screenEdgeOffset={left:15,right:15,top:0,bottom:0};if(t!==undefined)this.updateGeometry(t,e);this.controls.noTransitionAnimation=false};this.setPipViewFinal=function(t,e){this.controls.noSnap=true;this.controls.noSnapReal=true;this.controls.noTransitionAnimation=true;this.controls.noLifeZoom=true;this.camera.aspect=160/100;this.camera.near=.1;this.camera.far=5e4;this.camera.updateProjectionMatrix();this.controls.screenEdgeOffset={left:5,right:5,top:5,bottom:5};this.controls.new_theta=0;this.controls.new_phi=1.4;if(t!==undefined){this.updatePoints(t,e)}this.controls.update()};this.setComponentViewFinal=function(t,e,o){o=o===undefined?t+(e-t)/2:o;var a=Math.max(o-t[0],e[0]-o);this.controls.noTransitionAnimation=true;this.controls.noLifeZoom=true;this.controls.noAutoZoom=true;this.camera.near=.1;this.camera.far=5e4;this.camera.updateProjectionMatrix();this.controls.screenEdgeOffset={left:15,right:15,top:0,bottom:0};this.geometryOffset={left:0,right:0,top:50,bottom:50,front:0,back:0};this.controls.target.x=o+this.geometryOffset.right-this.geometryOffset.left;this.controls.target.y=(t[1]+e[1])/2+(this.geometryOffset.top-this.geometryOffset.bottom);this.controls.snapTo=[{theta:0,phi:1.4}];this.controls.update();this.controls.noAutoZoom=false;this.updatePoints({x:o-a,y:t[1],z:t[2]},{x:o+a,y:e[1],z:e[2]});this.controls.update();this.controls.noZoom=true;this.controls.noPan=false;this.controls.noRotate=true;this.controls.noAutoZoom=true;this.controls.mouseButtons={PAN:n["MOUSE"].LEFT,ZOOM:n["MOUSE"].MIDDLE,ORBIT:n["MOUSE"].RIGHT};this.controls.touchMode={ORBIT:4,ZOOM:2,PAN:3,ZOOMPAN:1};this.controls.noTransitionAnimation=false};this.setShelfView=function(t,e){console.log(">>>> SHELF VIEW2");this.controls.noZoom=true;this.controls.noPan=true;this.controls.noRotate=false;this.controls.noAutoZoom=false;this.controls.mouseButtons={ORBIT:n["MOUSE"].LEFT,ZOOM:n["MOUSE"].MIDDLE,PAN:n["MOUSE"].RIGHT};this.controls.touchMode={ORBIT:1,ZOOM:2,PAN:3,ZOOMPAN:4};if(t!==undefined)this.updateGeometry(t,e)};this.setComponentView=function(t,e){console.log(">>>> COMPONENT VIEW");this.controls.noZoom=true;this.controls.noPan=true;this.controls.noRotate=false;this.controls.noAutoZoom=false;this.controls.mouseButtons={ORBIT:n["MOUSE"].LEFT,ZOOM:n["MOUSE"].MIDDLE,PAN:n["MOUSE"].RIGHT};this.controls.touchMode={ORBIT:1,ZOOM:2,PAN:3,ZOOMPAN:4};this.controls.new_theta=0;this.controls.new_phi=Math.PI/2;if(t!==undefined)this.updateGeometry(t,e)};this.setView=function(t){switch(t){case"front":this.controls.new_phi=Math.PI/2;this.controls.new_theta=0;break;case"left":this.controls.new_phi=1.1;this.controls.new_theta=-.8;break;case"left_straight":this.controls.new_phi=Math.PI/2;this.controls.new_theta=-Math.PI/2;break;case"right":this.controls.new_phi=.8;this.controls.new_theta=1;break;case"right_straight":this.controls.new_phi=Math.PI/2;this.controls.new_theta=Math.PI/2;break;case"top":this.controls.new_phi=.3;this.controls.new_theta=0;break;case"top_straight":this.controls.new_phi=0;this.controls.new_theta=0;break}this.controls.noSnapReal=true;this.controls.target.set(0,0,0);this.controls.updateCameraRange();this.controls.update()};this.setCamSettings=function(t){this.fov=t.fov;this.range=t.range;this.target=t.target;this.theta=this.controls.new_theta;this.phi=this.controls.new_phi;this.position=t.position;this.updateCamera()};this.getCamSettings=function(){return{fov:this.fov,range:this.range,target:this.target,rotation:this.rotation,position:this.position}};this.init()}},"480f":function(t,e,o){var n=o("c135");var a=o("11b0");var i=o("c240");function s(t){return n(t)||a(t)||i()}t.exports=s},5926:function(t,e,o){"use strict";var n=o("480f");var a=o.n(n);var i=o("448a");var s=o.n(i);var r=o("359c");var h=o("723b");var c=o("e411");function l(t){this.points=[new c["Vector3"](-1e3,-1e3,-1e3),new c["Vector3"](1e3,1e3,1e3)];this.frustum=new c["Frustum"];this.horizontalPlane=new c["Plane"];this.verticalPlane=new c["Plane"];this.cameraPlanesOffset=50;var e=new c["Vector3"];var o=new c["Vector3"];var n=Math.PI/180;this.calculateNearFarPosition=function(t,e){var o=[];this.points.forEach(function(t){o.push(t.distanceTo(e.target))});var n=t.position.distanceTo(e.target);var a=Math.max.apply(Math,o);return[Math.max(0,n-a-this.cameraPlanesOffset),n+a+this.cameraPlanesOffset]};this.updatePoints=function(t){this.points=t};this.updatePlanes=function(t,n){this.frustum.setFromMatrix((new c["Matrix4"]).multiplyMatrices(t.projectionMatrix,t.matrixWorldInverse));this.frustum.planes[1].projectPoint(n.target,e);this.frustum.planes[2].projectPoint(n.target,o);this.horizontalPlane.setFromCoplanarPoints(n.target,t.position,e);this.verticalPlane.setFromCoplanarPoints(n.target,t.position,o)};this.calculateDistances=function(t,n,a,i,s,r){s.projectPoint(t,e);r.projectPoint(e,o);var h=e.distanceTo(o);var c=i.distanceTo(o);var l=a.distanceTo(o);var u=a.distanceTo(i);c=l<u?c:-c;var f=h/Math.tan(n)+c;return f};this.calculateZoom=function(t,e){var o=this;var a=[];var i=t.fov/2*n;this.points.forEach(function(n){a.push(o.calculateDistances(n,i,t.position,e.target,o.verticalPlane,o.horizontalPlane))});i*=t.aspect;this.points.forEach(function(n){a.push(o.calculateDistances(n,i,t.position,e.target,o.horizontalPlane,o.verticalPlane))});return Math.max.apply(Math,a)}}c["OrbitControls"]=function(t,e,o){this.object=t;this.domElement=e!==undefined?e:document;this.points=[new c["Vector3"](-1e3,-1e3,-1e3),new c["Vector3"](0,0,0),new c["Vector3"](0,0,0),new c["Vector3"](0,0,0),new c["Vector3"](0,0,0),new c["Vector3"](0,0,0),new c["Vector3"](0,0,0),new c["Vector3"](1e3,1e3,1e3)];this.enabled=true;this.noZoom=false;this.noAutoZoom=true;this.noLifeZoom=true;this.noTransitionAnimation=true;this.noRotate=false;this.noPan=false;this.directionLock=false;this.noYPan=false;this.noSnap=true;this.noSnapReal=this.noSnap;this.target=new c["Vector3"];this.targetOffset=new c["Vector3"](0,-100,0);this.snapTo=[{theta:.62,phi:1.1},{theta:-.4,phi:1.4},{theta:0,phi:Math.PI/2}];this.zoomSpeed=1;this.rotateSpeed=2;this.animationStepParameters={distance:70,targetDistance:1,angle:.08};this.zoomRange={min:0,max:Infinity};this.polarAngle={min:0,max:Math.PI};this.azimuthAngle={min:-Infinity,max:Infinity};this.mouseButtons={ORBIT:c["MOUSE"].LEFT,ZOOM:c["MOUSE"].MIDDLE,PAN:c["MOUSE"].RIGHT};this.touchMode={ORBIT:1,ZOOM:2,PAN:3,ZOOMPAN:4};this.new_theta=null;this.new_phi=null;this.new_target=new c["Vector3"];this.geometryFixed=true;this.screenEdgeOffset={left:0,right:0,top:0,bottom:0};var n=this;var i=1e-6;var r=new c["Vector2"];var h=new c["Vector2"];var l=new c["Vector2"];var u=new c["Vector2"];var f=new c["Vector2"];var p=new c["Vector2"];var m=new c["Vector3"];var d=new c["Vector3"];var g=new c["Vector2"];var M=new c["Vector2"];var O=new c["Vector2"];var v;var P;var E;var b;var w;var y;var T;var A;var x=0;var I=0;var S=1;var N=new c["Vector3"];var Z=new c["Vector3"];var L={counter:0,thetaDiff:0,phiDiff:0,zoomDiff:0,theta:0,phi:0,zoom:0};var R={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_DOLLY:4,TOUCH_PAN:5,TOUCH_ZOOMPAN:6,ANIMATE:7};var _=R.NONE;var V=(new c["Quaternion"]).setFromUnitVectors(t.up,new c["Vector3"](0,1,0));var z=V.clone().inverse();var D={type:"change"};var k={type:"render"};this.panLeft=function(t){var e=this.object.matrix.elements;m.set(e[0],e[1],e[2]);m.multiplyScalar(-t);N.add(m)};this.panUp=function(t){var e=this.object.matrix.elements;m.set(e[4],e[5],e[6]);m.multiplyScalar(t);N.add(m)};this.pan=function(t,e){var o=n.domElement===document?n.domElement.body:n.domElement;var a=n.object.position;var i=a.clone().sub(n.target);var s=i.length();s*=Math.tan(n.object.fov/2*Math.PI/180);n.panLeft(2*t*s/o.clientHeight);if(!n.noYPan)n.panUp(2*e*s/o.clientHeight)};this.dollyIn=function(t){if(t===undefined)t=q();S/=t};this.dollyOut=function(t){if(t===undefined)t=q();S*=t};this.updateCameraRange=function(){var t=0;n.points.forEach(function(e){t=Math.max(n.target.distanceTo(e),t)});var e=n.object.position.distanceTo(n.target);this.object.near=Math.max(e-t,.1);this.object.far=Math.max(e+t,1e3);this.object.updateProjectionMatrix()};function j(){var t=1e5;var e=1e5;n.points.forEach(function(o){t=Math.min(n.target.distanceTo(o),t);e=Math.min(n.object.position.distanceTo(o),e)});console.log(">>>> CR",t,e,"||",n.object.near,n.object.far,n.object.position.distanceTo(n.target))}function U(){d.copy(n.object.position).sub(n.target);d.applyQuaternion(V);E=Math.atan2(d.x,d.z);b=Math.atan2(Math.sqrt(d.x*d.x+d.z*d.z),d.y);v=false;P=d.length();w=P;y=E;T=b;A=n.target}function C(){var t=Q(b,E,n.snapTo);if(!n.noSnapReal&&Y(t.theta,t.phi)){y=t.theta;T=t.phi;v=true}else if(Y(n.new_theta,n.new_phi)){y=n.new_theta;T=n.new_phi}if(n.new_target!==null)A=n.new_target;w=P*S}this.updateZoom=function(){B();this.update()};function B(){var t=2/n.domElement.clientWidth;var e=2/n.domElement.clientHeight;var o=[];var a=[];n.points.forEach(function(i){Z.copy(i).project(n.object);o.push(Z.x+(Z.x<0?-t*n.screenEdgeOffset.left:t*n.screenEdgeOffset.right));a.push(Z.y+(Z.y<0?-e*n.screenEdgeOffset.bottom:e*n.screenEdgeOffset.top))});o=o.filter(isFinite);a=a.filter(isFinite);var i=n.object.position.distanceTo(n.target);var r=Math.max.apply(Math,s()(o).concat(s()(a),[Math.abs(Math.min.apply(Math,s()(o).concat(s()(a))))]));i=i*r;w=i||P}function H(){y=Math.max(n.azimuthAngle.min,Math.min(n.azimuthAngle.max,y));T=Math.max(n.polarAngle.min,Math.min(n.polarAngle.max,T));w=Math.max(n.zoomRange.min,Math.min(n.zoomRange.max,w))}function Y(t,e,o,a){var i=t!==undefined?Math.abs(E-t)>n.animationStepParameters.angle:false;var s=e!==undefined?Math.abs(b-e)>n.animationStepParameters.angle:false;var r=o!==undefined?Math.abs(P-o)>10:false;var h=a!==undefined?Math.abs(n.target.x-a.x)>1||Math.abs(n.target.y-a.y)>1||Math.abs(n.target.z-a.z)>1:false;return i||s||r||h}function F(){var t=Math.round(Math.max(Math.abs(y-E)/n.animationStepParameters.angle,Math.abs(T-b)/n.animationStepParameters.angle,Math.abs(w-P)/n.animationStepParameters.distance,5));_=R.ANIMATE;L={counter:t,thetaDiff:(y-E)/t,phiDiff:(T-b)/t,zoomDiff:(w-P)/t,theta:y,phi:T,zoom:w};G()}function X(){y=Math.atan2(d.x,d.z)+x;T=Math.atan2(Math.sqrt(d.x*d.x+d.z*d.z),d.y)+I;w=P*S;A=n.target.clone();A.add(N)}function G(){if(L.counter<=0){y=L.theta;T=L.phi;w=L.zoom;_=R.NONE}else{y=E+L.thetaDiff;T=b+L.phiDiff;w=P+L.zoomDiff;L.counter-=1}}function W(){T=Math.max(i,Math.min(Math.PI-i,T));d.x=w*Math.sin(T)*Math.sin(y);d.y=w*Math.cos(T);d.z=w*Math.sin(T)*Math.cos(y);d.applyQuaternion(z);n.target=A;n.object.position.copy(n.target).add(d);n.object.near=Math.max(1,n.object.near-(P-w));n.object.far=n.object.far-(P-w);n.object.lookAt(n.target);x=0;I=0;n.new_theta=y;n.new_phi=T;n.new_target.set(A.x,A.y,A.z);S=1;N.set(0,0,0)}this.update=function(){if(!this.geometryFixed&&this.noLifeZoom)return;//!this.geometryFixed && 
U();switch(_){case R.NONE:C();if(!v&&(!this.noAutoZoom||!this.noLifeZoom&&this.geometryFixed===true||!this.noLifeZoom&&_===R.NONE)){B()}if(!this.noTransitionAnimation&&Y(y,T,w)){F()}break;case R.ANIMATE:G();break;default:X();H();if(!this.noAutoZoom&&!this.noLifeZoom)B()}W();switch(_){case R.ANIMATE:n.dispatchEvent(k);break;default:n.dispatchEvent(D)}};this.getPolarAngle=function(){return b};this.getAzimuthalAngle=function(){return E};function q(){return Math.pow(.95,n.zoomSpeed)}function Q(t,e,o){var n,i,s;var r=o;var h=a()(r);n=h[0];o=h.slice(1);i=Math.abs(n.theta-e);o.forEach(function(t){s=Math.abs(t.theta-e);if(i>s){n=t;i=s}});return n}function J(t){if(n.enabled===false)return;t.preventDefault();if(t.button===n.mouseButtons.ORBIT){if(n.noRotate===true)return;_=R.ROTATE;r.set(t.clientX,t.clientY)}else if(t.button===n.mouseButtons.ZOOM){if(n.noZoom===true)return;_=R.DOLLY;g.set(t.clientX,t.clientY)}else if(t.button===n.mouseButtons.PAN){if(n.noPan===true)return;_=R.PAN;u.set(t.clientX,t.clientY)}if(_!==R.NONE){document.addEventListener("mousemove",K,false);document.addEventListener("mouseup",$,false)}}function K(t){if(n.enabled===false)return;t.preventDefault();var e=n.domElement===document?n.domElement.body:n.domElement;if(_===R.ROTATE){if(n.noRotate===true)return;h.set(t.clientX,t.clientY);l.subVectors(h,r);x-=2*Math.PI*l.x/e.clientWidth*n.rotateSpeed;I-=2*Math.PI*l.y/e.clientHeight*n.rotateSpeed;r.copy(h)}else if(_===R.DOLLY){if(n.noZoom===true)return;M.set(t.clientX,t.clientY);O.subVectors(M,g);if(O.y>0){n.dollyIn()}else if(O.y<0){n.dollyOut()}g.copy(M)}else if(_===R.PAN){if(n.noPan===true)return;f.set(t.clientX,t.clientY);p.subVectors(f,u);n.pan(p.x,p.y);u.copy(f)}if(_!==R.NONE){n.update();n.noSnapReal=n.noSnap}}function $(){if(n.enabled===false)return;document.removeEventListener("mousemove",K,false);document.removeEventListener("mouseup",$,false);_=R.NONE;n.update()}function tt(t){if(n.enabled===false||n.noZoom===true||_!==R.NONE)return;t.preventDefault();t.stopPropagation();var e=0;if(t.wheelDelta!==undefined){e=t.wheelDelta}else if(t.detail!==undefined){e=-t.detail}if(e>0){n.dollyOut()}else if(e<0){n.dollyIn()}n.noSnapReal=n.noSnap;n.update()}function et(t){if(n.enabled===false)return;switch(t.touches.length){case n.touchMode.ORBIT:if(n.noRotate===true)return;_=R.TOUCH_ROTATE;r.set(t.touches[0].pageX,t.touches[0].pageY);break;case n.touchMode.ZOOM:if(n.noZoom===true)return;_=R.TOUCH_DOLLY;var e=t.touches[0].pageX-t.touches[1].pageX;var o=t.touches[0].pageY-t.touches[1].pageY;var a=Math.sqrt(e*e+o*o);g.set(0,a);break;case n.touchMode.PAN:if(n.noPan===true)return;_=R.TOUCH_PAN;u.set(t.touches[0].pageX,t.touches[0].pageY);break;case n.touchMode.ZOOMPAN:_=R.TOUCH_ZOOMPAN;n.directionLock=true;u.set(t.touches[0].pageX,t.touches[0].pageY);break;default:_=R.NONE}}function ot(t){switch(n.directionLock){case false:return t;case"horizontal":t.setY(0);return t;case"vertrical":t.setX(0);return t;default:n.directionLock=Math.abs(t.x)>Math.abs(t.y)?"horizontal":"vertrical";return ot(t)}}function nt(t){if(n.enabled===false)return;t.preventDefault();t.stopPropagation();var e=n.domElement===document?n.domElement.body:n.domElement;switch(t.touches.length){case n.touchMode.ORBIT:if(n.noRotate===true)return;if(_!==R.TOUCH_ROTATE)return;h.set(t.touches[0].pageX,t.touches[0].pageY);l.subVectors(h,r);l=ot(l);x-=2*Math.PI*l.x/e.clientWidth*n.rotateSpeed;I-=2*Math.PI*l.y/e.clientHeight*n.rotateSpeed;r.copy(h);n.noSnapReal=n.noSnap;n.update();break;case n.touchMode.ZOOM:if(n.noZoom===true)return;if(_!==R.TOUCH_DOLLY)return;var o=t.touches[0].pageX-t.touches[1].pageX;var a=t.touches[0].pageY-t.touches[1].pageY;var i=Math.sqrt(o*o+a*a);M.set(0,i);O.subVectors(M,g);if(O.y>0){n.dollyOut()}else if(O.y<0){n.dollyIn()}g.copy(M);n.noSnapReal=n.noSnap;n.update();break;case n.touchMode.PAN:if(n.noPan===true)return;if(_!==R.TOUCH_PAN)return;f.set(t.touches[0].pageX,t.touches[0].pageY);p.subVectors(f,u);p=ot(p);n.pan(p.x,p.y);u.copy(f);n.noSnapReal=n.noSnap;n.update();break;case n.touchMode.ZOOMPAN:if(n.noPan===true)return;if(_!==R.TOUCH_ZOOMPAN)return;f.set(t.touches[0].pageX,t.touches[0].pageY);p.subVectors(f,u);p=ot(p);switch(n.directionLock){case false:case"horizontal":n.pan(p.x,p.y);u.copy(f);n.noSnapReal=n.noSnap;n.update();break;case"vertrical":x-=2*Math.PI*p.x/e.clientWidth*n.rotateSpeed;I-=2*Math.PI*p.y/e.clientHeight*n.rotateSpeed;u.copy(f);n.noSnapReal=n.noSnap;n.update();break;default:_=R.NONE;break}break;default:_=R.NONE}}function at(){if(n.enabled===false)return;if(n.directionLock!==false)n.directionLock=true;_=R.NONE;n.update()}this.domElement.addEventListener("contextmenu",function(t){t.preventDefault()},false);this.domElement.addEventListener("mousedown",J,false);this.domElement.addEventListener("mousewheel",tt,false);this.domElement.addEventListener("DOMMouseScroll",tt,false);this.domElement.addEventListener("touchstart",et,false);this.domElement.addEventListener("touchend",at,false);this.domElement.addEventListener("touchmove",nt,false);this.update()};c["OrbitControls"].prototype=Object.create(c["EventDispatcher"].prototype);c["OrbitControls"].prototype.constructor=c["OrbitControls"]}}]);
//# sourceMappingURL=bbfdea08.4364390d.js.map