{"version": 3, "sources": ["webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.simulator/lib/spring.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.forcelayout/index.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/globalInput.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.graph/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.simulator/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.simulator/lib/eulerIntegrator.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.merge/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.simulator/lib/springForce.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/cape-graph.vue?1b7e", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.simulator/lib/dragForce.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.generators/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.events/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.quadtreebh/insertStack.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.quadtreebh/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.quadtreebh/isSamePosition.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.generators/node_modules/ngraph.random/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.random/index.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/pixiGraphics.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.expose/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.forcelayout/node_modules/ngraph.events/index.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.primitives/index.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/cape-graph.vue?8afb", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/cape-graph.vue", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/cape-graph.vue?2be3", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/cape-graph.vue?f866", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.simulator/lib/bounds.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.quadtreebh/node.js", "webpack:////Users/<USER>/tylko/cstm/frontend_src/src_designer/node_modules/ngraph.physics.simulator/lib/createBody.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-graph/lib/addWheelListener.js"], "names": ["module", "exports", "Spring", "fromBody", "toBody", "length", "coeff", "weight", "this", "from", "to", "main", "con<PERSON><PERSON>", "graph", "require", "balancedBinTree", "layout", "createLayout", "createPixiGraphics", "graphics", "bindGlobalInput", "renderFrame", "requestAnimationFrame", "spring<PERSON>ength", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "gravity", "theta", "simulator", "__webpack_require__", "eventify", "physicsSettings", "Error", "createSimulator", "physicsSimulator", "nodeMass", "defaultNodeMass", "nodeBodies", "Object", "create", "springs", "bodiesCount", "springTransform", "settings", "noop", "initPhysics", "listenToEvents", "wasStable", "api", "step", "lastMove", "fire", "ratio", "isStableNow", "onStableChanged", "getNodePosition", "nodeId", "getInitializedBody", "pos", "setNodePosition", "body", "setPosition", "apply", "Array", "prototype", "slice", "call", "arguments", "invalidate<PERSON><PERSON>", "getLinkPosition", "linkId", "spring", "getGraphRect", "getBBox", "forEachBody", "pinNode", "node", "isPinned", "id", "isNodePinned", "dispose", "off", "onGraphChanged", "getBody", "getSpring", "cb", "keys", "for<PERSON>ach", "bodyId", "fromId", "toId", "undefined", "link", "hasLink", "on", "isStable", "changes", "i", "change", "changeType", "initBody", "initLink", "releaseNode", "releaseLink", "getNodesCount", "forEachNode", "forEachLink", "getNode", "position", "neighbors", "getNeighborBodies", "getBestNewBodyPosition", "addBodyAt", "updateBodyMass", "isNodeOriginallyPinned", "removeBody", "addSpring", "removeSpring", "links", "max<PERSON><PERSON><PERSON><PERSON>s", "Math", "min", "otherBody", "push", "mass", "Number", "isNaN", "data", "getLinks", "addWheelListener", "graphGraphics", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "zoom", "clientX", "clientY", "deltaY", "addDragNDrop", "getGraphCoordinates", "ctx", "global", "x", "y", "PIXI", "InteractionData", "getLocalPosition", "isZoomIn", "direction", "factor", "scale", "beforeTransform", "updateTransform", "afterTransform", "stage", "interactive", "isDragging", "nodeUnderCursor", "prevX", "prevY", "moveData", "graphPos", "getNodeAt", "mousemove", "dx", "dy", "mouseup", "moveDate", "draggingNode", "createGraph", "options", "console", "warn", "multigraph", "uniqueLinkId", "nodes", "multiEdges", "nodesCount", "suspendEvents", "createNodeIterator", "createLink", "createUniqueLink", "createSingleLink", "recordLinkChange", "recordNodeChange", "enterModification", "exitModification", "graphPart", "addNode", "addLink", "removeLink", "removeNode", "getLinksCount", "forEachLinkedNode", "beginUpdate", "endUpdate", "clear", "getLink", "hasNode", "monitorSubscribers", "realOn", "enterModificationReal", "exitModificationReal", "recordLinkChangeReal", "recordNodeChangeReal", "Node", "prevLinks", "fromNode", "toNode", "addLinkToNode", "makeLinkId", "Link", "isMultiEdge", "hasOwnProperty", "suffix", "idx", "indexOfElementInArray", "splice", "fromNodeId", "toNodeId", "callback", "oriented", "forEachOrientedLink", "forEachNonOrientedLink", "quitFast", "linkedNodeId", "objectKeysIterator", "forInIterator", "element", "array", "indexOf", "len", "hashCode", "str", "hash", "chr", "charCodeAt", "toString", "expose", "merge", "timeStep", "createQuadTree", "createBounds", "createDragForce", "createSpringForce", "integrate", "integrator", "createBody", "bodies", "quadTree", "bounds", "springForce", "dragForce", "bboxNeedsUpdate", "totalMovement", "publicApi", "accumulateForces", "movement", "update", "addBody", "reset", "body1", "body2", "springWeight", "springCoefficient", "getTotalMovement", "getBestNewPosition", "box", "value", "insertBodies", "force", "updateBodyForce", "tx", "ty", "max", "velocity", "vx", "vy", "v", "sqrt", "abs", "target", "key", "targetHasIt", "optionsValueType", "shouldReplace", "random", "r", "nextDouble", "d", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_cape_graph_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_cape_graph_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default", "n", "_unused_webpack_default_export", "factory", "ladder", "complete", "completeBipartite", "path", "circularLadder", "grid", "grid3", "noLinks", "wattsStrogatz", "cliqueCircle", "g", "j", "m", "z", "k", "level", "count", "pow", "root", "left", "right", "cliqueCount", "cliqueSize", "appendClique", "size", "p", "seed", "neighborsSize", "floor", "newTo", "next", "needsRewire", "subject", "validateSubject", "eventsStorage", "createEventsStorage", "registeredEvents", "eventName", "handlers", "wantToRemoveAll", "deleteAllCallbacksForEvent", "callbacks", "fireArguments", "callbackInfo", "reservedWords", "InsertStack", "stack", "popIdx", "isEmpty", "item", "InsertStackElement", "pop", "isSamePosition", "updateQueue", "insertStack", "nodesCache", "currentInCache", "newNode", "getRoot", "newOptions", "quad0", "quad1", "quad2", "quad3", "massX", "massY", "top", "bottom", "sourceBody", "queue", "fx", "fy", "queueLength", "shiftIdx", "pushIdx", "differentBody", "x1", "MAX_VALUE", "y1", "x2", "MIN_VALUE", "y2", "insert", "newBody", "stackItem", "quadIdx", "child", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "oldBody", "retriesCount", "offset", "point1", "point2", "randomIterator", "inputSeed", "Date", "Generator", "uniform", "gaussian", "log", "maxValue", "customRandom", "localRandom", "shuffle", "t", "randomFunc", "NODE_WIDTH", "width", "window", "innerWidth", "height", "innerHeight", "Stage", "renderer", "autoDetect<PERSON><PERSON><PERSON>", "view", "style", "display", "append<PERSON><PERSON><PERSON>", "Graphics", "<PERSON><PERSON><PERSON><PERSON>", "nodePositions", "getNodeByIndex", "linkPositions", "drawGraph", "render", "half", "insideNode", "beginFill", "lineStyle", "moveTo", "lineTo", "drawRect", "exposeProperties", "filter", "needsFilter", "augment", "source", "Body", "Vector2d", "Body3d", "Vector3d", "prevPos", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "ref", "staticRenderFns", "cape_graphvue_type_script_lang_js_", "components", "mounted", "cape_graph_default", "a", "$refs", "here", "methods", "meshes", "cape_graph_cape_graphvue_type_script_lang_js_", "component", "componentNormalizer", "cape_graph_cape_graph", "__webpack_exports__", "boundingBox", "updateBoundingBox", "graphRect", "baseX", "baseY", "physics", "prefix", "_addEventListener", "onwheel", "support", "addEventListener", "document", "createElement", "onmousew<PERSON><PERSON>", "elem", "useCapture", "_addWheelListener", "originalEvent", "event", "srcElement", "type", "deltaMode", "deltaX", "delatZ", "preventDefault", "returnValue", "wheelDelta", "wheelDeltaX", "detail"], "mappings": "uFAAAA,EAAAC,QAAAC,EAMA,SAAAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GACAC,KAAAC,KAAAN,EACAK,KAAAE,GAAAN,EACAI,KAAAH,SACAG,KAAAF,QAEAE,KAAAD,kBAAA,SAAAA,EAAA,2BCZAP,EAAOC,QAAQU,KAAO,SAAUC,GAC5B,IAAIC,EAAQC,EAAQ,QAAqBC,gBAAgB,GACrDC,EAASC,EAAaJ,GAE1B,IAAIK,EAAqBJ,EAAQ,QACjC,IAAIK,EAAWD,EAAmBN,EAAWC,EAAOG,GAGpD,IAAII,EAAkBN,EAAQ,QAC9BM,EAAgBD,EAAUH,GAG1BK,IAEA,SAASA,IACPF,EAASE,cACTC,sBAAsBD,KAI1B,SAASJ,EAAaJ,GACpB,IAAIG,EAASF,EAAQ,QAErB,OAAOE,EAAOH,EAAO,CACbU,aAAc,GACdC,YAAa,KACbC,UAAW,IACXC,SAAU,IACVC,MAAO,oDC5BnB3B,EAAAC,QAAAgB,EACAjB,EAAAC,QAAA2B,UAA2BC,EAAQ,QAEnC,IAAAC,EAAeD,EAAQ,QAUvB,SAAAZ,EAAAJ,EAAAkB,GACA,IAAAlB,EAAA,CACA,UAAAmB,MAAA,uCAGA,IAAAC,EAAwBJ,EAAQ,QAChC,IAAAK,EAAAD,EAAAF,GAEA,IAAAI,EAAAC,EACA,GAAAL,YAAAI,WAAA,YACAA,EAAAJ,EAAAI,SAGA,IAAAE,EAAAC,OAAAC,OAAA,MACA,IAAAC,EAAA,GACA,IAAAC,EAAA,EAEA,IAAAC,EAAAR,EAAAS,SAAAD,iBAAAE,EAGAC,IACAC,IAEA,IAAAC,EAAA,MAEA,IAAAC,EAAA,CAOAC,KAAA,WACA,GAAAR,IAAA,cAEA,IAAAS,EAAAhB,EAAAe,OAIAD,EAAAE,WAGAF,EAAAG,KAAA,QAEA,IAAAC,EAAAF,EAAAT,EACA,IAAAY,EAAAD,GAAA,IAEA,GAAAL,IAAAM,EAAA,CACAN,EAAAM,EACAC,EAAAD,GAGA,OAAAA,GAMAE,gBAAA,SAAAC,GACA,OAAAC,EAAAD,GAAAE,KAUAC,gBAAA,SAAAH,GACA,IAAAI,EAAAH,EAAAD,GACAI,EAAAC,YAAAC,MAAAF,EAAAG,MAAAC,UAAAC,MAAAC,KAAAC,UAAA,IACAjC,EAAAkC,kBAQAC,gBAAA,SAAAC,GACA,IAAAC,EAAA/B,EAAA8B,GACA,GAAAC,EAAA,CACA,OACA9D,KAAA8D,EAAA9D,KAAAiD,IACAhD,GAAA6D,EAAA7D,GAAAgD,OAUAc,aAAA,WACA,OAAAtC,EAAAuC,WAMAC,cAOAC,QAAA,SAAAC,EAAAC,GACA,IAAAjB,EAAAH,EAAAmB,EAAAE,IACAlB,EAAAiB,cAMAE,aAAA,SAAAH,GACA,OAAAnB,EAAAmB,EAAAE,IAAAD,UAMAG,QAAA,WACAnE,EAAAoE,IAAA,UAAAC,GACAlC,EAAAG,KAAA,aAOAgC,UAUAC,YAKAxD,UAAAM,EAKArB,QAKAqC,SAAA,GAGApB,EAAAkB,GAEA,OAAAA,EAEA,SAAA0B,EAAAW,GACA/C,OAAAgD,KAAAjD,GAAAkD,QAAA,SAAAC,GACAH,EAAAhD,EAAAmD,QAIA,SAAAJ,EAAAK,EAAAC,GACA,IAAApB,EACA,GAAAoB,IAAAC,UAAA,CACA,UAAAF,IAAA,UAEAnB,EAAAmB,MACO,CAEPnB,EAAAmB,EAAAX,QAEK,CAEL,IAAAc,EAAA/E,EAAAgF,QAAAJ,EAAAC,GACA,IAAAE,EAAA,OACAtB,EAAAsB,EAAAd,GAGA,OAAAtC,EAAA8B,GAGA,SAAAa,EAAA3B,GACA,OAAAnB,EAAAmB,GAGA,SAAAV,IACAjC,EAAAiF,GAAA,UAAAZ,GAGA,SAAA5B,EAAAyC,GACA/C,EAAAG,KAAA,SAAA4C,GAGA,SAAAb,EAAAc,GACA,QAAAC,EAAA,EAAmBA,EAAAD,EAAA3F,SAAoB4F,EAAA,CACvC,IAAAC,EAAAF,EAAAC,GACA,GAAAC,EAAAC,aAAA,OACA,GAAAD,EAAAtB,KAAA,CACAwB,EAAAF,EAAAtB,KAAAE,IAEA,GAAAoB,EAAAN,KAAA,CACAS,EAAAH,EAAAN,YAEO,GAAAM,EAAAC,aAAA,UACP,GAAAD,EAAAtB,KAAA,CACA0B,EAAAJ,EAAAtB,MAEA,GAAAsB,EAAAN,KAAA,CACAW,EAAAL,EAAAN,QAIAnD,EAAA5B,EAAA2F,gBAGA,SAAA3D,IACAJ,EAAA,EAEA5B,EAAA4F,YAAA,SAAA7B,GACAwB,EAAAxB,EAAAE,IACArC,GAAA,IAGA5B,EAAA6F,YAAAL,GAGA,SAAAD,EAAA5C,GACA,IAAAI,EAAAvB,EAAAmB,GACA,IAAAI,EAAA,CACA,IAAAgB,EAAA/D,EAAA8F,QAAAnD,GACA,IAAAoB,EAAA,CACA,UAAA5C,MAAA,8CAGA,IAAA0B,EAAAkB,EAAAgC,SACA,IAAAlD,EAAA,CACA,IAAAmD,EAAAC,EAAAlC,GACAlB,EAAAxB,EAAA6E,uBAAAF,GAGAjD,EAAA1B,EAAA8E,UAAAtD,GACAE,EAAAkB,GAAAtB,EAEAnB,EAAAmB,GAAAI,EACAqD,EAAAzD,GAEA,GAAA0D,EAAAtC,GAAA,CACAhB,EAAAiB,SAAA,OAKA,SAAAyB,EAAA1B,GACA,IAAApB,EAAAoB,EAAAE,GACA,IAAAlB,EAAAvB,EAAAmB,GACA,GAAAI,EAAA,CACAvB,EAAAmB,GAAA,YACAnB,EAAAmB,GAEAtB,EAAAiF,WAAAvD,IAIA,SAAAyC,EAAAT,GACAqB,EAAArB,EAAAH,QACAwB,EAAArB,EAAAF,MAEA,IAAAvF,EAAAkC,EAAAuD,EAAAH,QACArF,EAAAiC,EAAAuD,EAAAF,MACAnB,EAAArC,EAAAkF,UAAAjH,EAAAC,EAAAwF,EAAAvF,QAEAqC,EAAAkD,EAAArB,GAEA/B,EAAAoD,EAAAd,IAAAP,EAGA,SAAAgC,EAAAX,GACA,IAAArB,EAAA/B,EAAAoD,EAAAd,IACA,GAAAP,EAAA,CACA,IAAA9D,EAAAI,EAAA8F,QAAAf,EAAAH,QACA/E,EAAAG,EAAA8F,QAAAf,EAAAF,MAEA,GAAAjF,EAAAwG,EAAAxG,EAAAqE,IACA,GAAApE,EAAAuG,EAAAvG,EAAAoE,WAEAtC,EAAAoD,EAAAd,IAEA5C,EAAAmF,aAAA9C,IAIA,SAAAuC,EAAAlC,GAEA,IAAAiC,EAAA,GACA,IAAAjC,EAAA0C,MAAA,CACA,OAAAT,EAEA,IAAAU,EAAAC,KAAAC,IAAA7C,EAAA0C,MAAAjH,OAAA,GACA,QAAA4F,EAAA,EAAmBA,EAAAsB,IAAkBtB,EAAA,CACrC,IAAAL,EAAAhB,EAAA0C,MAAArB,GACA,IAAAyB,EAAA9B,EAAAH,SAAAb,EAAAE,GAAAzC,EAAAuD,EAAAH,QAAApD,EAAAuD,EAAAF,MACA,GAAAgC,KAAAhE,IAAA,CACAmD,EAAAc,KAAAD,IAIA,OAAAb,EAGA,SAAAI,EAAAzD,GACA,IAAAI,EAAAvB,EAAAmB,GACAI,EAAAgE,KAAAzF,EAAAqB,GACA,GAAAqE,OAAAC,MAAAlE,EAAAgE,MAAA,CACA,UAAA5F,MAAA,iCAYA,SAAAkF,EAAAtC,GACA,OAAAA,MAAAC,UAAAD,EAAAmD,MAAAnD,EAAAmD,KAAAlD,UAGA,SAAApB,EAAAD,GACA,IAAAI,EAAAvB,EAAAmB,GACA,IAAAI,EAAA,CACAwC,EAAA5C,GACAI,EAAAvB,EAAAmB,GAEA,OAAAI,EASA,SAAAxB,EAAAoB,GACA,IAAA8D,EAAAzG,EAAAmH,SAAAxE,GACA,IAAA8D,EAAA,SACA,SAAAA,EAAAjH,OAAA,GAIA,SAAAuC,4BCtXA5C,EAAOC,QAAU,SAAUkB,EAAUH,GACjC,IAAIiH,EAAmBnH,EAAQ,QAC/B,IAAIoH,EAAgB/G,EAAS+G,cAE7BD,EAAiB9G,EAASgH,aAAc,SAAUC,GAChDC,EAAKD,EAAEE,QAASF,EAAEG,QAASH,EAAEI,OAAS,KAGxCC,IAEA,IAAIC,EAAuB,WACzB,IAAIC,EAAM,CACRC,OAAQ,CAAEC,EAAG,EAAGC,EAAG,IAGrB,OAAO,SAAUD,EAAGC,GAClBH,EAAIC,OAAOC,EAAIA,EAAGF,EAAIC,OAAOE,EAAIA,EACjC,OAAOC,KAAKC,gBAAgBhF,UAAUiF,iBAAiB/E,KAAKyE,EAAKT,IAP1C,GAW3B,SAASG,EAAKQ,EAAGC,EAAGI,GAClBC,UAAYD,EAAW,GAAK,EAC5B,IAAIE,EAAU,EAAID,UAAY,GAC9BjB,EAAcmB,MAAMR,GAAKO,EACzBlB,EAAcmB,MAAMP,GAAKM,EAIzB,IAAIE,EAAkBZ,EAAoBG,EAAGC,GAC7CZ,EAAcqB,kBACd,IAAIC,EAAiBd,EAAoBG,EAAGC,GAE5CZ,EAActB,SAASiC,IAAMW,EAAeX,EAAIS,EAAgBT,GAAKX,EAAcmB,MAAMR,EACzFX,EAActB,SAASkC,IAAMU,EAAeV,EAAIQ,EAAgBR,GAAKZ,EAAcmB,MAAMP,EACzFZ,EAAcqB,kBAGhB,SAASd,IACP,IAAIgB,EAAQtI,EAASsI,MACrBA,EAAMC,YAAc,KAGpB,IAAIC,EAAa,MACbC,EACAC,EAAOC,EAEXL,EAAM3D,GAAG,YAAa,SAAUiE,GAC9B,IAAIrG,EAAMqG,EAASnB,OACnB,IAAIoB,EAAWtB,EAAoBhF,EAAImF,EAAGnF,EAAIoF,GAC9Cc,EAAkBzI,EAAS8I,UAAUD,EAASnB,EAAGmB,EAASlB,GAC1D,GAAIc,EAAiB,CAGnB5I,EAAO2D,QAAQiF,EAAiB,MAGlCC,EAAQnG,EAAImF,EAAGiB,EAAQpG,EAAIoF,EAC3Ba,EAAa,OAGfF,EAAMS,UAAY,SAAUH,GAC1B,IAAKJ,EAAY,CACf,OAEF,IAAIjG,EAAMqG,EAASnB,OAEnB,GAAIgB,EAAiB,CACnB,IAAII,EAAWtB,EAAoBhF,EAAImF,EAAGnF,EAAIoF,GAC9C9H,EAAO2C,gBAAgBiG,EAAgB9E,GAAIkF,EAASnB,EAAGmB,EAASlB,OAC3D,CACL,IAAIqB,EAAKzG,EAAImF,EAAIgB,EACjB,IAAIO,EAAK1G,EAAIoF,EAAIgB,EACjBD,EAAQnG,EAAImF,EAAGiB,EAAQpG,EAAIoF,EAC3BZ,EAActB,SAASiC,GAAKsB,EAC5BjC,EAActB,SAASkC,GAAKsB,IAIhCX,EAAMY,QAAU,SAAUC,GACxBX,EAAa,MACb,GAAIC,EAAiB,CACnBW,aAAe,KACfvJ,EAAO2D,QAAQiF,EAAiB,mCCpE1C5J,EAAAC,QAAAuK,EAEA,IAAA1I,EAAeD,EAAQ,QAKvB,SAAA2I,EAAAC,GAMAA,KAAA,GACA,oBAAAA,EAAA,CACAC,QAAAC,KACA,2EACA,oCACA,KACA,2EACA,0EAGAF,EAAAG,WAAAH,EAAAI,aAMA,GAAAJ,EAAAG,aAAAjF,UAAA8E,EAAAG,WAAA,MAEA,IAAAE,SAAAxI,OAAAC,SAAA,WAAAD,OAAAC,OAAA,SACA+E,EAAA,GAEAyD,EAAA,GACAC,EAAA,EACAC,EAAA,EAEAxE,EAAAyE,IACAC,EAAAV,EAAAG,WAAAQ,EAAAC,EAaArF,EAAA,GACAsF,EAAA1I,EACA2I,EAAA3I,EACA4I,EAAA5I,EACA6I,EAAA7I,EAGA,IAAA8I,EAAA,CAWAC,UAaAC,UASAC,aAUAC,aASAnF,UAOAH,cAAA,WACA,OAAAwE,GAMAe,cAAA,WACA,OAAAzE,EAAAjH,QAYA2H,WAQAvB,cAUAuF,oBAaAtF,cAMAuF,YAAAT,EAMAU,UAAAT,EAKAU,QASAtG,QAAAuG,EAUAC,QAAA1F,EAWAyF,WAIAtK,EAAA4J,GAEAY,IAEA,OAAAZ,EAEA,SAAAY,IACA,IAAAC,EAAAb,EAAA5F,GAIA4F,EAAA5F,KAEA,SAAAA,IAEA4F,EAAAO,YAAAT,EAAAgB,EACAd,EAAAQ,UAAAT,EAAAgB,EACAnB,EAAAoB,EACAnB,EAAAoB,EAGAjB,EAAA5F,GAAAyG,EAEA,OAAAA,EAAAzI,MAAA4H,EAAAvH,YAIA,SAAAuI,EAAA9G,EAAAO,GACAH,EAAA2B,KAAA,CACA/B,OACAO,eAIA,SAAAwG,EAAA/H,EAAAuB,GACAH,EAAA2B,KAAA,CACA/C,OACAuB,eAIA,SAAAwF,EAAAnI,EAAAuE,GACA,GAAAvE,IAAAmC,UAAA,CACA,UAAA3D,MAAA,2BAGAwJ,IAEA,IAAA5G,EAAA+B,EAAAnD,GACA,IAAAoB,EAAA,CACAA,EAAA,IAAAgI,EAAApJ,EAAAuE,GACAiD,IACAO,EAAA3G,EAAA,WACK,CACLA,EAAAmD,OACAwD,EAAA3G,EAAA,UAGAkG,EAAAtH,GAAAoB,EAEA6G,IACA,OAAA7G,EAGA,SAAA+B,EAAAnD,GACA,OAAAsH,EAAAtH,GAGA,SAAAsI,EAAAtI,GACA,IAAAoB,EAAA+B,EAAAnD,GACA,IAAAoB,EAAA,CACA,aAGA4G,IAEA,IAAAqB,EAAAjI,EAAA0C,MACA,GAAAuF,EAAA,CACAjI,EAAA0C,MAAA,KACA,QAAArB,EAAA,EAAoBA,EAAA4G,EAAAxM,SAAsB4F,EAAA,CAC1C4F,EAAAgB,EAAA5G,YAIA6E,EAAAtH,GACAwH,IAEAO,EAAA3G,EAAA,UAEA6G,IAEA,YAIA,SAAAG,EAAAnG,EAAAC,EAAAqC,GACAyD,IAEA,IAAAsB,EAAAnG,EAAAlB,IAAAkG,EAAAlG,GACA,IAAAsH,EAAApG,EAAAjB,IAAAiG,EAAAjG,GAEA,IAAAE,EAAAuF,EAAA1F,EAAAC,EAAAqC,GAEAT,EAAAK,KAAA/B,GAGAoH,EAAAF,EAAAlH,GACA,GAAAH,IAAAC,EAAA,CAEAsH,EAAAD,EAAAnH,GAGA0F,EAAA1F,EAAA,OAEA6F,IAEA,OAAA7F,EAGA,SAAAyF,EAAA5F,EAAAC,EAAAqC,GACA,IAAAzD,EAAA2I,EAAAxH,EAAAC,GACA,WAAAwH,EAAAzH,EAAAC,EAAAqC,EAAAzD,GAGA,SAAA8G,EAAA3F,EAAAC,EAAAqC,GAEA,IAAAzD,EAAA2I,EAAAxH,EAAAC,GACA,IAAAyH,EAAApC,EAAAqC,eAAA9I,GACA,GAAA6I,GAAAf,EAAA3G,EAAAC,GAAA,CACA,IAAAyH,EAAA,CACApC,EAAAzG,GAAA,EAEA,IAAA+I,EAAA,OAAAtC,EAAAzG,GACAA,EAAA2I,EAAAxH,EAAA4H,EAAA3H,EAAA2H,GAGA,WAAAH,EAAAzH,EAAAC,EAAAqC,EAAAzD,GAGA,SAAA0D,EAAAxE,GACA,IAAAoB,EAAA+B,EAAAnD,GACA,OAAAoB,IAAA0C,MAAA,KAGA,SAAAuE,EAAAjG,GACA,IAAAA,EAAA,CACA,aAEA,IAAA0H,EAAAC,EAAA3H,EAAA0B,GACA,GAAAgG,EAAA,GACA,aAGA9B,IAEAlE,EAAAkG,OAAAF,EAAA,GAEA,IAAAR,EAAAnG,EAAAf,EAAAH,QACA,IAAAsH,EAAApG,EAAAf,EAAAF,MAEA,GAAAoH,EAAA,CACAQ,EAAAC,EAAA3H,EAAAkH,EAAAxF,OACA,GAAAgG,GAAA,GACAR,EAAAxF,MAAAkG,OAAAF,EAAA,IAIA,GAAAP,EAAA,CACAO,EAAAC,EAAA3H,EAAAmH,EAAAzF,OACA,GAAAgG,GAAA,GACAP,EAAAzF,MAAAkG,OAAAF,EAAA,IAIAhC,EAAA1F,EAAA,UAEA6F,IAEA,YAGA,SAAAW,EAAAqB,EAAAC,GAEA,IAAA9I,EAAA+B,EAAA8G,GACAxH,EACA,IAAArB,MAAA0C,MAAA,CACA,YAGA,IAAArB,EAAA,EAAeA,EAAArB,EAAA0C,MAAAjH,SAAuB4F,EAAA,CACtC,IAAAL,EAAAhB,EAAA0C,MAAArB,GACA,GAAAL,EAAAH,SAAAgI,GAAA7H,EAAAF,OAAAgI,EAAA,CACA,OAAA9H,GAIA,YAGA,SAAAuG,IACAX,IACA/E,EAAA,SAAA7B,GACAkH,EAAAlH,EAAAE,MAEA2G,IAGA,SAAA/E,EAAAiH,GACA,IAAA1H,EAAA5F,EACA,UAAAsN,IAAA,YACA,IAAA1H,EAAA,EAAA5F,EAAAiH,EAAAjH,OAAwC4F,EAAA5F,IAAY4F,EAAA,CACpD0H,EAAArG,EAAArB,MAKA,SAAA+F,EAAAxI,EAAAmK,EAAAC,GACA,IAAAhJ,EAAA+B,EAAAnD,GAEA,GAAAoB,KAAA0C,cAAAqG,IAAA,YACA,GAAAC,EAAA,CACA,OAAAC,EAAAjJ,EAAA0C,MAAA9D,EAAAmK,OACO,CACP,OAAAG,EAAAlJ,EAAA0C,MAAA9D,EAAAmK,KAKA,SAAAG,EAAAxG,EAAA9D,EAAAmK,GACA,IAAAI,EACA,QAAA9H,EAAA,EAAmBA,EAAAqB,EAAAjH,SAAkB4F,EAAA,CACrC,IAAAL,EAAA0B,EAAArB,GACA,IAAA+H,EAAApI,EAAAH,SAAAjC,EAAAoC,EAAAF,KAAAE,EAAAH,OAEAsI,EAAAJ,EAAA7C,EAAAkD,GAAApI,GACA,GAAAmI,EAAA,CACA,cAKA,SAAAF,EAAAvG,EAAA9D,EAAAmK,GACA,IAAAI,EACA,QAAA9H,EAAA,EAAmBA,EAAAqB,EAAAjH,SAAkB4F,EAAA,CACrC,IAAAL,EAAA0B,EAAArB,GACA,GAAAL,EAAAH,SAAAjC,EAAA,CACAuK,EAAAJ,EAAA7C,EAAAlF,EAAAF,MAAAE,GACA,GAAAmI,EAAA,CACA,eAQA,SAAAnL,KAGA,SAAA4J,IACAvB,GAAA,EAGA,SAAAwB,IACAxB,GAAA,EACA,GAAAA,IAAA,GAAAjF,EAAA3F,OAAA,GACAqL,EAAAvI,KAAA,UAAA6C,GACAA,EAAA3F,OAAA,GAIA,SAAA6K,IAIA,OAAA5I,OAAAgD,KAAA2I,EAAAC,EAGA,SAAAD,EAAAN,GACA,UAAAA,IAAA,YACA,OAGA,IAAArI,EAAAhD,OAAAgD,KAAAwF,GACA,QAAA7E,EAAA,EAAmBA,EAAAX,EAAAjF,SAAiB4F,EAAA,CACpC,GAAA0H,EAAA7C,EAAAxF,EAAAW,KAAA,CACA,cAKA,SAAAiI,EAAAP,GACA,UAAAA,IAAA,YACA,OAEA,IAAA/I,EAEA,IAAAA,KAAAkG,EAAA,CACA,GAAA6C,EAAA7C,EAAAlG,IAAA,CACA,eAOA,SAAA2I,EAAAY,EAAAC,GACA,IAAAA,EAAA,SAEA,GAAAA,EAAAC,QAAA,CACA,OAAAD,EAAAC,QAAAF,GAGA,IAAAG,EAAAF,EAAA/N,OACA4F,EAEA,IAAAA,EAAA,EAAaA,EAAAqI,EAASrI,GAAA,GACtB,GAAAmI,EAAAnI,KAAAkI,EAAA,CACA,OAAAlI,GAIA,SAMA,SAAA2G,EAAA9H,EAAAiD,GACAvH,KAAAsE,KACAtE,KAAA8G,MAAA,KACA9G,KAAAuH,OAGA,SAAAiF,EAAApI,EAAAgB,GACA,GAAAhB,EAAA0C,MAAA,CACA1C,EAAA0C,MAAAK,KAAA/B,OACG,CACHhB,EAAA0C,MAAA,CAAA1B,IAOA,SAAAsH,EAAAzH,EAAAC,EAAAqC,EAAAjD,GACAtE,KAAAiF,SACAjF,KAAAkF,OACAlF,KAAAuH,OACAvH,KAAAsE,KAGA,SAAAyJ,EAAAC,GACA,IAAAC,EAAA,EAAAxI,EAAAyI,EAAAJ,EACA,GAAAE,EAAAnO,QAAA,SAAAoO,EACA,IAAAxI,EAAA,EAAAqI,EAAAE,EAAAnO,OAA+B4F,EAAAqI,EAASrI,IAAA,CACxCyI,EAAAF,EAAAG,WAAA1I,GACAwI,MAAA,GAAAA,EAAAC,EACAD,GAAA,EAEA,OAAAA,EAGA,SAAAxB,EAAAxH,EAAAC,GACA,OAAAD,EAAAmJ,WAAA,MAAAlJ,EAAAkJ,oCCplBA5O,EAAAC,QAAAiC,EAEA,SAAAA,EAAAS,GACA,IAAAzC,EAAe2B,EAAQ,QACvB,IAAAgN,EAAehN,EAAQ,QACvB,IAAAiN,EAAcjN,EAAQ,QACtB,IAAAC,EAAiBD,EAAQ,QAEzBc,EAAAmM,EAAAnM,EAAA,CAIApB,aAAA,GAKAC,YAAA,KAMAE,SAAA,IAQAC,MAAA,GAMAF,UAAA,IAKAsN,SAAA,KAIA,IAAAC,EAAArM,EAAAqM,gBAAkDnN,EAAQ,QAC1D,IAAAoN,EAAAtM,EAAAsM,cAA8CpN,EAAQ,QACtD,IAAAqN,EAAAvM,EAAAuM,iBAAoDrN,EAAQ,QAC5D,IAAAsN,EAAAxM,EAAAwM,mBAAwDtN,EAAQ,QAChE,IAAAuN,EAAAzM,EAAA0M,YAAyCxN,EAAQ,QACjD,IAAAyN,EAAA3M,EAAA2M,YAA0CzN,EAAQ,QAElD,IAAA0N,EAAA,GACA/M,EAAA,GACAgN,EAAAR,EAAArM,GACA8M,EAAAR,EAAAM,EAAA5M,GACA+M,EAAAP,EAAAxM,GACAgN,EAAAT,EAAAvM,GAEA,IAAAiN,EAAA,KACA,IAAAC,EAAA,EAEA,IAAAC,EAAA,CAOAP,SAEAC,WAQAhN,UAKAG,WAOAM,KAAA,WACA8M,IAEA,IAAAC,EAAAZ,EAAAG,EAAA5M,EAAAoM,UACAU,EAAAQ,SAEA,OAAAD,GAUAE,QAAA,SAAAtM,GACA,IAAAA,EAAA,CACA,UAAA5B,MAAA,oBAEAuN,EAAA5H,KAAA/D,GAEA,OAAAA,GAUAoD,UAAA,SAAAtD,GACA,IAAAA,EAAA,CACA,UAAA1B,MAAA,6BAEA,IAAA4B,EAAA0L,EAAA5L,GACA6L,EAAA5H,KAAA/D,GAEA,OAAAA,GAUAuD,WAAA,SAAAvD,GACA,IAAAA,EAAA,CAAkB,OAElB,IAAA0J,EAAAiC,EAAAlB,QAAAzK,GACA,GAAA0J,EAAA,GAAoB,OAEpBiC,EAAA/B,OAAAF,EAAA,GACA,GAAAiC,EAAAlP,SAAA,GACAoP,EAAAU,QAEA,aASA/I,UAAA,SAAAgJ,EAAAC,EAAA9O,EAAA+O,EAAAC,GACA,IAAAH,IAAAC,EAAA,CACA,UAAArO,MAAA,6CAGA,UAAAT,IAAA,UACAA,GAAA,EAGA,IAAAgD,EAAA,IAAArE,EAAAkQ,EAAAC,EAAA9O,EAAAgP,GAAA,EAAAA,GAAA,EAAAD,GACA9N,EAAAmF,KAAApD,GAGA,OAAAA,GAMAiM,iBAAA,WACA,OAAAX,GAUAxI,aAAA,SAAA9C,GACA,IAAAA,EAAA,CAAoB,OACpB,IAAA+I,EAAA9K,EAAA6L,QAAA9J,GACA,GAAA+I,GAAA,GACA9K,EAAAgL,OAAAF,EAAA,GACA,cAIAvG,uBAAA,SAAAF,GACA,OAAA4I,EAAAgB,mBAAA5J,IAMApC,QAAA,WACA,GAAAmL,EAAA,CACAH,EAAAQ,SACAL,EAAA,MAEA,OAAAH,EAAAiB,KAGAtM,eAAA,WACAwL,EAAA,MAGAlO,QAAA,SAAAiP,GACA,GAAAA,IAAAhL,UAAA,CACAhD,EAAAjB,QAAAiP,EACAnB,EAAA/E,QAAA,CAA0B/I,QAAAiP,IAC1B,OAAAnQ,SACO,CACP,OAAAmC,EAAAjB,UAIAC,MAAA,SAAAgP,GACA,GAAAA,IAAAhL,UAAA,CACAhD,EAAAhB,MAAAgP,EACAnB,EAAA/E,QAAA,CAA0B9I,MAAAgP,IAC1B,OAAAnQ,SACO,CACP,OAAAmC,EAAAhB,SAMAkN,EAAAlM,EAAAmN,GAEAhO,EAAAgO,GAEA,OAAAA,EAEA,SAAAC,IAEA,IAAAnM,EACAqC,EAAAsJ,EAAAlP,OAEA,GAAA4F,EAAA,CAEAuJ,EAAAoB,aAAArB,GACA,MAAAtJ,IAAA,CACArC,EAAA2L,EAAAtJ,GAGA,IAAArC,EAAAiB,SAAA,CACAjB,EAAAiN,MAAAV,QAEAX,EAAAsB,gBAAAlN,GACA+L,EAAAM,OAAArM,KAKAqC,EAAAzD,EAAAnC,OACA,MAAA4F,IAAA,CACAyJ,EAAAO,OAAAzN,EAAAyD,6BCzQAjG,EAAAC,QAAAmP,EAEA,SAAAA,EAAAG,EAAAR,GACA,IAAA5E,EAAA,EAAA4G,EAAA,EACA3G,EAAA,EAAA4G,EAAA,EACA/K,EACAgL,EAAA1B,EAAAlP,OAEA,GAAA4Q,IAAA,GACA,SAGA,IAAAhL,EAAA,EAAaA,EAAAgL,IAAShL,EAAA,CACtB,IAAArC,EAAA2L,EAAAtJ,GACA3F,EAAAyO,EAAAnL,EAAAgE,KAEAhE,EAAAsN,SAAArI,GAAAvI,EAAAsD,EAAAiN,MAAAhI,EACAjF,EAAAsN,SAAApI,GAAAxI,EAAAsD,EAAAiN,MAAA/H,EACA,IAAAqI,EAAAvN,EAAAsN,SAAArI,EACAuI,EAAAxN,EAAAsN,SAAApI,EACAuI,EAAA7J,KAAA8J,KAAAH,IAAAC,KAEA,GAAAC,EAAA,GACAzN,EAAAsN,SAAArI,EAAAsI,EAAAE,EACAzN,EAAAsN,SAAApI,EAAAsI,EAAAC,EAGAlH,EAAA4E,EAAAnL,EAAAsN,SAAArI,EACAuB,EAAA2E,EAAAnL,EAAAsN,SAAApI,EAEAlF,EAAAF,IAAAmF,GAAAsB,EACAvG,EAAAF,IAAAoF,GAAAsB,EAEA2G,GAAAvJ,KAAA+J,IAAApH,GAAuB6G,GAAAxJ,KAAA+J,IAAAnH,GAGvB,OAAA2G,IAAAC,KAAAC,yBC3CAjR,EAAAC,QAAA6O,EASA,SAAAA,EAAA0C,EAAA/G,GACA,IAAAgH,EACA,IAAAD,EAAA,CAAgBA,EAAA,GAChB,GAAA/G,EAAA,CACA,IAAAgH,KAAAhH,EAAA,CACA,GAAAA,EAAA2C,eAAAqE,GAAA,CACA,IAAAC,EAAAF,EAAApE,eAAAqE,GACAE,SAAAlH,EAAAgH,GACAG,GAAAF,UAAAF,EAAAC,KAAAE,EAEA,GAAAC,EAAA,CACAJ,EAAAC,GAAAhH,EAAAgH,QACS,GAAAE,IAAA,UAETH,EAAAC,GAAA3C,EAAA0C,EAAAC,GAAAhH,EAAAgH,OAMA,OAAAD,2BCrBAxR,EAAAC,QAAA,SAAAwK,GACA,IAAAqE,EAAcjN,EAAQ,QACtB,IAAAgQ,EAAehQ,EAAQ,QAAegQ,OAAA,IACtC,IAAAhD,EAAehN,EAAQ,QAEvB4I,EAAAqE,EAAArE,EAAA,CACAjJ,YAAA,KACAD,aAAA,KAGA,IAAAyB,EAAA,CAIAiN,OAAA,SAAA1L,GACA,IAAA6L,EAAA7L,EAAA9D,KACA4P,EAAA9L,EAAA7D,GACAL,EAAAkE,EAAAlE,OAAA,EAAAoK,EAAAlJ,aAAAgD,EAAAlE,OACA8J,EAAAkG,EAAA3M,IAAAmF,EAAAuH,EAAA1M,IAAAmF,EACAuB,EAAAiG,EAAA3M,IAAAoF,EAAAsH,EAAA1M,IAAAoF,EACAgJ,EAAAtK,KAAA8J,KAAAnH,IAAAC,KAEA,GAAA0H,IAAA,GACA3H,GAAA0H,EAAAE,aAAA,OACA3H,GAAAyH,EAAAE,aAAA,OACAD,EAAAtK,KAAA8J,KAAAnH,IAAAC,KAGA,IAAA4H,EAAAF,EAAAzR,EACA,IAAAC,IAAAiE,EAAAjE,OAAAiE,EAAAjE,MAAA,EAAAmK,EAAAjJ,YAAA+C,EAAAjE,OAAA0R,EAAAF,EAAAvN,EAAAhE,OAEA6P,EAAAS,MAAAhI,GAAAvI,EAAA6J,EACAiG,EAAAS,MAAA/H,GAAAxI,EAAA8J,EAEAiG,EAAAQ,MAAAhI,GAAAvI,EAAA6J,EACAkG,EAAAQ,MAAA/H,GAAAxI,EAAA8J,IAIAyE,EAAApE,EAAAzH,EAAA,gCACA,OAAAA,wCChDA,IAAAiP,EAAApQ,EAAA,YAAAqQ,EAAArQ,EAAAsQ,EAAAF,GAA6kB,IAAAG,EAAAF,EAAG,wBCOhlBlS,EAAAC,QAAA,SAAAwK,GACA,IAAAqE,EAAcjN,EAAQ,QACtBgN,EAAehN,EAAQ,QAEvB4I,EAAAqE,EAAArE,EAAA,CACAhJ,UAAA,MAGA,IAAAuB,EAAA,CACAiN,OAAA,SAAArM,GACAA,EAAAiN,MAAAhI,GAAA4B,EAAAhJ,UAAAmC,EAAAsN,SAAArI,EACAjF,EAAAiN,MAAA/H,GAAA2B,EAAAhJ,UAAAmC,EAAAsN,SAAApI,IAKA+F,EAAApE,EAAAzH,EAAA,eAEA,OAAAA,2BCzBA,IAAAwH,EAAkB3I,EAAQ,QAE1B7B,EAAAC,QAAAoS,EAAA7H,GAGAxK,EAAAC,QAAAoS,UAEA,SAAAA,EAAA7H,GACA,OACA8H,SACAC,WACAC,oBACAzR,kBACA0R,OACAC,iBACAC,OACAC,QACAC,UACAC,gBACAC,gBAIA,SAAAT,EAAAH,GAKA,IAAAA,KAAA,GACA,UAAAnQ,MAAA,2BAGA,IAAAgR,EAAAxI,IACAvE,EAEA,IAAAA,EAAA,EAAeA,EAAAkM,EAAA,IAAWlM,EAAA,CAC1B+M,EAAApH,QAAA3F,IAAA,GAEA+M,EAAApH,QAAAuG,EAAAlM,EAAAkM,EAAAlM,EAAA,GAEA+M,EAAApH,QAAA3F,EAAAkM,EAAAlM,GAIA+M,EAAApH,QAAAuG,EAAA,IAAAA,EAAA,GAGA,OAAAa,EAGA,SAAAN,EAAAP,GAMA,IAAAA,KAAA,GACA,UAAAnQ,MAAA,2BAGA,IAAAgR,EAAAV,EAAAH,GAEAa,EAAApH,QAAA,EAAAuG,EAAA,GACAa,EAAApH,QAAAuG,EAAA,EAAAA,EAAA,GACA,OAAAa,EAGA,SAAAT,EAAAJ,GAMA,IAAAA,KAAA,GACA,UAAAnQ,MAAA,sDAGA,IAAAgR,EAAAxI,IACAvE,EACAgN,EAEA,IAAAhN,EAAA,EAAeA,EAAAkM,IAAOlM,EAAA,CACtB,IAAAgN,EAAAhN,EAAA,EAAqBgN,EAAAd,IAAOc,EAAA,CAC5B,GAAAhN,IAAAgN,EAAA,CACAD,EAAApH,QAAA3F,EAAAgN,KAKA,OAAAD,EAGA,SAAAR,EAAAL,EAAAe,GAQA,IAAAf,IAAAe,GAAAf,EAAA,GAAAe,EAAA,GACA,UAAAlR,MAAA,4FAGA,IAAAgR,EAAAxI,IACAvE,EAAAgN,EAEA,IAAAhN,EAAA,EAAeA,EAAAkM,IAAOlM,EAAA,CACtB,IAAAgN,EAAAd,EAAiBc,EAAAd,EAAAe,IAAWD,EAAA,CAC5BD,EAAApH,QAAA3F,EAAAgN,IAIA,OAAAD,EAGA,SAAAP,EAAAN,GAMA,IAAAA,KAAA,GACA,UAAAnQ,MAAA,2BAGA,IAAAgR,EAAAxI,IACAvE,EAEA+M,EAAArH,QAAA,GAEA,IAAA1F,EAAA,EAAeA,EAAAkM,IAAOlM,EAAA,CACtB+M,EAAApH,QAAA3F,EAAA,EAAAA,GAGA,OAAA+M,EAIA,SAAAL,EAAAR,EAAAe,GAOA,GAAAf,EAAA,GAAAe,EAAA,GACA,UAAAlR,MAAA,yCAEA,IAAAgR,EAAAxI,IACAvE,EACAgN,EACA,GAAAd,IAAA,GAAAe,IAAA,GACAF,EAAArH,QAAA,GACA,OAAAqH,EAGA,IAAA/M,EAAA,EAAeA,EAAAkM,IAAOlM,EAAA,CACtB,IAAAgN,EAAA,EAAiBA,EAAAC,IAAOD,EAAA,CACxB,IAAArO,EAAAqB,EAAAgN,EAAAd,EACA,GAAAlM,EAAA,GAAoB+M,EAAApH,QAAAhH,EAAAqB,EAAA,EAAAgN,EAAAd,GACpB,GAAAc,EAAA,GAAoBD,EAAApH,QAAAhH,EAAAqB,GAAAgN,EAAA,GAAAd,KAIpB,OAAAa,EAGA,SAAAJ,EAAAT,EAAAe,EAAAC,GAQA,GAAAhB,EAAA,GAAAe,EAAA,GAAAC,EAAA,GACA,UAAAnR,MAAA,0CAEA,IAAAgR,EAAAxI,IACAvE,EAAAgN,EAAAG,EAEA,GAAAjB,IAAA,GAAAe,IAAA,GAAAC,IAAA,GACAH,EAAArH,QAAA,GACA,OAAAqH,EAGA,IAAAI,EAAA,EAAeA,EAAAD,IAAOC,EAAA,CACtB,IAAAnN,EAAA,EAAiBA,EAAAkM,IAAOlM,EAAA,CACxB,IAAAgN,EAAA,EAAmBA,EAAAC,IAAOD,EAAA,CAC1B,IAAAI,EAAAD,EAAAjB,EAAAe,EACA,IAAAtO,EAAAqB,EAAAgN,EAAAd,EAAAkB,EACA,GAAApN,EAAA,GAAsB+M,EAAApH,QAAAhH,EAAAqB,EAAA,EAAAgN,EAAAd,EAAAkB,GACtB,GAAAJ,EAAA,GAAsBD,EAAApH,QAAAhH,EAAAqB,GAAAgN,EAAA,GAAAd,EAAAkB,GACtB,GAAAD,EAAA,GAAsBJ,EAAApH,QAAAhH,EAAAqB,EAAAgN,EAAAd,GAAAiB,EAAA,GAAAjB,EAAAe,MAKtB,OAAAF,EAGA,SAAAjS,EAAAoR,GAMA,GAAAA,EAAA,GACA,UAAAnQ,MAAA,4CAEA,IAAAgR,EAAAxI,IACA8I,EAAA9L,KAAA+L,IAAA,EAAApB,GACAkB,EAEA,GAAAlB,IAAA,GACAa,EAAArH,QAAA,GAGA,IAAA0H,EAAA,EAAmBA,EAAAC,IAAeD,EAAA,CAClC,IAAAG,EAAAH,EACAI,EAAAD,EAAA,EACAE,EAAAF,EAAA,IAEAR,EAAApH,QAAA4H,EAAAC,GACAT,EAAApH,QAAA4H,EAAAE,GAGA,OAAAV,EAGA,SAAAH,EAAAV,GAMA,GAAAA,EAAA,GACA,UAAAnQ,MAAA,kCAGA,IAAAgR,EAAAxI,IAAAvE,EACA,IAAAA,EAAA,EAAeA,EAAAkM,IAAOlM,EAAA,CACtB+M,EAAArH,QAAA1F,GAGA,OAAA+M,EAGA,SAAAD,EAAAY,EAAAC,GAQA,GAAAD,EAAA,YAAA3R,MAAA,iDACA,GAAA4R,EAAA,YAAA5R,MAAA,gDAEA,IAAAnB,EAAA2J,IAEA,QAAAvE,EAAA,EAAmBA,EAAA0N,IAAiB1N,EAAA,CACpC4N,EAAAD,EAAA3N,EAAA2N,GAEA,GAAA3N,EAAA,GACApF,EAAA+K,QAAA3F,EAAA2N,EAAA3N,EAAA2N,EAAA,IAGA/S,EAAA+K,QAAA,EAAA/K,EAAA2F,gBAAA,GAEA,OAAA3F,EAEA,SAAAgT,EAAAC,EAAArT,GACA,QAAAwF,EAAA,EAAqBA,EAAA6N,IAAU7N,EAAA,CAC/BpF,EAAA8K,QAAA1F,EAAAxF,GAGA,QAAAwF,EAAA,EAAqBA,EAAA6N,IAAU7N,EAAA,CAC/B,QAAAgN,EAAAhN,EAAA,EAA2BgN,EAAAa,IAAUb,EAAA,CACrCpS,EAAA+K,QAAA3F,EAAAxF,EAAAwS,EAAAxS,MAMA,SAAAqS,EAAAX,EAAAiB,EAAAW,EAAAC,GAUA,GAAAZ,GAAAjB,EAAA,UAAAnQ,MAAA,oEAGA,IAAA6P,EAAiBhQ,EAAQ,QAAegQ,OAAAmC,GAAA,IAExC,IAAAhB,EAAAxI,IAAAvE,EAAAvF,EACA,IAAAuF,EAAA,EAAeA,EAAAkM,IAAOlM,EAAA,CACtB+M,EAAArH,QAAA1F,GAIA,IAAAgO,EAAAzM,KAAA0M,MAAAd,EAAA,KACA,QAAAH,EAAA,EAAmBA,EAAAgB,IAAmBhB,EAAA,CACtC,IAAAhN,EAAA,EAAiBA,EAAAkM,IAAOlM,EAAA,CACxBvF,GAAAuS,EAAAhN,GAAAkM,EACAa,EAAApH,QAAA3F,EAAAvF,IAOA,IAAAuS,EAAA,EAAeA,EAAAgB,IAAmBhB,EAAA,CAClC,IAAAhN,EAAA,EAAiBA,EAAAkM,IAAOlM,EAAA,CACxB,GAAA4L,EAAAE,aAAAgC,EAAA,CACA,IAAAtT,EAAAwF,EACAvF,GAAAuS,EAAAhN,GAAAkM,EAEA,IAAAgC,EAAAtC,EAAAuC,KAAAjC,GACA,IAAAkC,EAAAF,IAAA1T,GAAAuS,EAAAnN,QAAApF,EAAA0T,GACA,GAAAE,GAAArB,EAAAhL,SAAAvH,GAAAJ,SAAA8R,EAAA,GAEA,SAGA,MAAAkC,EAAA,CACAF,EAAAtC,EAAAuC,KAAAjC,GACAkC,EAAAF,IAAA1T,GAAAuS,EAAAnN,QAAApF,EAAA0T,GAEA,IAAAvO,EAAAoN,EAAAnN,QAAApF,EAAAC,GACAsS,EAAAnH,WAAAjG,GACAoN,EAAApH,QAAAnL,EAAA0T,KAKA,OAAAnB,0BCtVAhT,EAAAC,QAAA,SAAAqU,GACAC,EAAAD,GAEA,IAAAE,EAAAC,EAAAH,GACAA,EAAAxO,GAAA0O,EAAA1O,GACAwO,EAAArP,IAAAuP,EAAAvP,IACAqP,EAAAnR,KAAAqR,EAAArR,KACA,OAAAmR,GAGA,SAAAG,EAAAH,GAMA,IAAAI,EAAApS,OAAAC,OAAA,MAEA,OACAuD,GAAA,SAAA6O,EAAAhH,EAAAhF,GACA,UAAAgF,IAAA,YACA,UAAA3L,MAAA,yCAEA,IAAA4S,EAAAF,EAAAC,GACA,IAAAC,EAAA,CACAA,EAAAF,EAAAC,GAAA,GAEAC,EAAAjN,KAAA,CAAqBgG,WAAAhF,QAErB,OAAA2L,GAGArP,IAAA,SAAA0P,EAAAhH,GACA,IAAAkH,SAAAF,IAAA,YACA,GAAAE,EAAA,CAEAH,EAAApS,OAAAC,OAAA,MACA,OAAA+R,EAGA,GAAAI,EAAAC,GAAA,CACA,IAAAG,SAAAnH,IAAA,WACA,GAAAmH,EAAA,QACAJ,EAAAC,OACS,CACT,IAAAI,EAAAL,EAAAC,GACA,QAAA1O,EAAA,EAAyBA,EAAA8O,EAAA1U,SAAsB4F,EAAA,CAC/C,GAAA8O,EAAA9O,GAAA0H,aAAA,CACAoH,EAAAvH,OAAAvH,EAAA,MAMA,OAAAqO,GAGAnR,KAAA,SAAAwR,GACA,IAAAI,EAAAL,EAAAC,GACA,IAAAI,EAAA,CACA,OAAAT,EAGA,IAAAU,EACA,GAAA7Q,UAAA9D,OAAA,GACA2U,EAAAjR,MAAAC,UAAAwJ,OAAAtJ,KAAAC,UAAA,GAEA,QAAA8B,EAAA,EAAoBA,EAAA8O,EAAA1U,SAAsB4F,EAAA,CAC1C,IAAAgP,EAAAF,EAAA9O,GACAgP,EAAAtH,SAAA7J,MAAAmR,EAAAtM,IAAAqM,GAGA,OAAAV,IAKA,SAAAC,EAAAD,GACA,IAAAA,EAAA,CACA,UAAAtS,MAAA,sDAEA,IAAAkT,EAAA,oBACA,QAAAjP,EAAA,EAAiBA,EAAAiP,EAAA7U,SAA0B4F,EAAA,CAC3C,GAAAqO,EAAAlH,eAAA8H,EAAAjP,IAAA,CACA,UAAAjE,MAAA,gEAAAkT,EAAAjP,GAAA,8BCpFAjG,EAAAC,QAAAkV,EAOA,SAAAA,IACA3U,KAAA4U,MAAA,GACA5U,KAAA6U,OAAA,EAGAF,EAAAnR,UAAA,CACAsR,QAAA,WACA,OAAA9U,KAAA6U,SAAA,GAEA1N,KAAA,SAAA/C,EAAAhB,GACA,IAAA2R,EAAA/U,KAAA4U,MAAA5U,KAAA6U,QACA,IAAAE,EAAA,CAGA/U,KAAA4U,MAAA5U,KAAA6U,QAAA,IAAAG,EAAA5Q,EAAAhB,OACS,CACT2R,EAAA3Q,OACA2Q,EAAA3R,SAEApD,KAAA6U,QAEAI,IAAA,WACA,GAAAjV,KAAA6U,OAAA,GACA,OAAA7U,KAAA4U,QAAA5U,KAAA6U,UAGAlF,MAAA,WACA3P,KAAA6U,OAAA,IAIA,SAAAG,EAAA5Q,EAAAhB,GACApD,KAAAoE,OACApE,KAAAoD,gCCjCA5D,EAAAC,QAAA,SAAAwK,GACAA,KAAA,GACAA,EAAA/I,eAAA+I,EAAA/I,UAAA,SAAA+I,EAAA/I,SAAA,EACA+I,EAAA9I,aAAA8I,EAAA9I,QAAA,SAAA8I,EAAA9I,MAAA,GAGA,IAAAkQ,EAAehQ,EAAQ,QAAegQ,OAAA,MACtCjF,EAAW/K,EAAQ,QACnBsT,EAAkBtT,EAAQ,QAC1B6T,EAAqB7T,EAAQ,QAE7B,IAAAH,EAAA+I,EAAA/I,QACAiU,EAAA,GACAC,EAAA,IAAAT,EACAxT,EAAA8I,EAAA9I,MAEAkU,EAAA,GACAC,EAAA,EACAtC,EAAAuC,IAEA,OACAnF,eAIAoF,QAAA,WACA,OAAAxC,GAEA1C,gBAAAb,EACAxF,QAAA,SAAAwL,GACA,GAAAA,EAAA,CACA,UAAAA,EAAAvU,UAAA,UACAA,EAAAuU,EAAAvU,QAEA,UAAAuU,EAAAtU,QAAA,UACAA,EAAAsU,EAAAtU,MAGA,OAAAnB,KAGA,OACAkB,UACAC,WAKA,SAAAoU,IAEA,IAAAnR,EAAAiR,EAAAC,GACA,GAAAlR,EAAA,CACAA,EAAAsR,MAAA,KACAtR,EAAAuR,MAAA,KACAvR,EAAAwR,MAAA,KACAxR,EAAAyR,MAAA,KACAzR,EAAAhB,KAAA,KACAgB,EAAAgD,KAAAhD,EAAA0R,MAAA1R,EAAA2R,MAAA,EACA3R,EAAA6O,KAAA7O,EAAA8O,MAAA9O,EAAA4R,IAAA5R,EAAA6R,OAAA,MACK,CACL7R,EAAA,IAAAgI,EACAiJ,EAAAC,GAAAlR,IAGAkR,EACA,OAAAlR,EAGA,SAAAqL,EAAAyG,GACA,IAAAC,EAAAhB,EACAtE,EACAlH,EACAC,EACA0H,EAAA8E,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EAEAL,EAAA,GAAAnD,EAEA,MAAAsD,EAAA,CACA,IAAAlS,EAAA+R,EAAAI,GACAnT,EAAAgB,EAAAhB,KAEAkT,GAAA,EACAC,GAAA,EACA,IAAAE,EAAArT,IAAA8S,EACA,GAAA9S,GAAAqT,EAAA,CAIA9M,EAAAvG,EAAAF,IAAAmF,EAAA6N,EAAAhT,IAAAmF,EACAuB,EAAAxG,EAAAF,IAAAoF,EAAA4N,EAAAhT,IAAAoF,EACAgJ,EAAAtK,KAAA8J,KAAAnH,IAAAC,KAEA,GAAA0H,IAAA,GAEA3H,GAAA0H,EAAAE,aAAA,OACA3H,GAAAyH,EAAAE,aAAA,OACAD,EAAAtK,KAAA8J,KAAAnH,IAAAC,KAKAiH,EAAA3P,EAAAkC,EAAAgE,KAAA8O,EAAA9O,MAAAkK,OACA8E,GAAAvF,EAAAlH,EACA0M,GAAAxF,EAAAjH,OACO,GAAA6M,EAAA,CAIP9M,EAAAvF,EAAA0R,MAAA1R,EAAAgD,KAAA8O,EAAAhT,IAAAmF,EACAuB,EAAAxF,EAAA2R,MAAA3R,EAAAgD,KAAA8O,EAAAhT,IAAAoF,EACAgJ,EAAAtK,KAAA8J,KAAAnH,IAAAC,KAEA,GAAA0H,IAAA,GAGA3H,GAAA0H,EAAAE,aAAA,OACA3H,GAAAyH,EAAAE,aAAA,OACAD,EAAAtK,KAAA8J,KAAAnH,IAAAC,KAIA,IAAAxF,EAAA8O,MAAA9O,EAAA6O,MAAA3B,EAAAnQ,EAAA,CAIA0P,EAAA3P,EAAAkD,EAAAgD,KAAA8O,EAAA9O,MAAAkK,OACA8E,GAAAvF,EAAAlH,EACA0M,GAAAxF,EAAAjH,MACS,CAIT,GAAAxF,EAAAsR,MAAA,CACAS,EAAAK,GAAApS,EAAAsR,MACAY,GAAA,EACAE,GAAA,EAEA,GAAApS,EAAAuR,MAAA,CACAQ,EAAAK,GAAApS,EAAAuR,MACAW,GAAA,EACAE,GAAA,EAEA,GAAApS,EAAAwR,MAAA,CACAO,EAAAK,GAAApS,EAAAwR,MACAU,GAAA,EACAE,GAAA,EAEA,GAAApS,EAAAyR,MAAA,CACAM,EAAAK,GAAApS,EAAAyR,MACAS,GAAA,EACAE,GAAA,KAMAN,EAAA7F,MAAAhI,GAAA+N,EACAF,EAAA7F,MAAA/H,GAAA+N,EAGA,SAAAjG,EAAArB,GACA,IAAA2H,EAAArP,OAAAsP,UACAC,EAAAvP,OAAAsP,UACAE,EAAAxP,OAAAyP,UACAC,EAAA1P,OAAAyP,UACArR,EACAgL,EAAA1B,EAAAlP,OAGA4F,EAAAgL,EACA,MAAAhL,IAAA,CACA,IAAA4C,EAAA0G,EAAAtJ,GAAAvC,IAAAmF,EACA,IAAAC,EAAAyG,EAAAtJ,GAAAvC,IAAAoF,EACA,GAAAD,EAAAqO,EAAA,CACAA,EAAArO,EAEA,GAAAA,EAAAwO,EAAA,CACAA,EAAAxO,EAEA,GAAAC,EAAAsO,EAAA,CACAA,EAAAtO,EAEA,GAAAA,EAAAyO,EAAA,CACAA,EAAAzO,GAKA,IAAAqB,EAAAkN,EAAAH,EACA9M,EAAAmN,EAAAH,EACA,GAAAjN,EAAAC,EAAA,CACAmN,EAAAH,EAAAjN,MACK,CACLkN,EAAAH,EAAA9M,EAGA0L,EAAA,EACAtC,EAAAuC,IACAvC,EAAAC,KAAAyD,EACA1D,EAAAE,MAAA2D,EACA7D,EAAAgD,IAAAY,EACA5D,EAAAiD,OAAAc,EAEAtR,EAAAgL,EAAA,EACA,GAAAhL,GAAA,GACAuN,EAAA5P,KAAA2L,EAAAtJ,GAEA,MAAAA,IAAA,CACAuR,EAAAjI,EAAAtJ,GAAAuN,IAIA,SAAAgE,EAAAC,GACA7B,EAAAzF,QACAyF,EAAAjO,KAAA6L,EAAAiE,GAEA,OAAA7B,EAAAN,UAAA,CACA,IAAAoC,EAAA9B,EAAAH,MACA7Q,EAAA8S,EAAA9S,KACAhB,EAAA8T,EAAA9T,KAEA,IAAAgB,EAAAhB,KAAA,CAEA,IAAAiF,EAAAjF,EAAAF,IAAAmF,EACA,IAAAC,EAAAlF,EAAAF,IAAAoF,EACAlE,EAAAgD,KAAAhD,EAAAgD,KAAAhE,EAAAgE,KACAhD,EAAA0R,MAAA1R,EAAA0R,MAAA1S,EAAAgE,KAAAiB,EACAjE,EAAA2R,MAAA3R,EAAA2R,MAAA3S,EAAAgE,KAAAkB,EAIA,IAAA6O,EAAA,EACAlE,EAAA7O,EAAA6O,KACAC,GAAA9O,EAAA8O,MAAAD,GAAA,EACA+C,EAAA5R,EAAA4R,IACAC,GAAA7R,EAAA6R,OAAAD,GAAA,EAEA,GAAA3N,EAAA6K,EAAA,CACAiE,IAAA,EACAlE,EAAAC,EACAA,EAAA9O,EAAA8O,MAEA,GAAA5K,EAAA2N,EAAA,CACAkB,IAAA,EACAnB,EAAAC,EACAA,EAAA7R,EAAA6R,OAGA,IAAAmB,EAAAC,EAAAjT,EAAA+S,GACA,IAAAC,EAAA,CAGAA,EAAA7B,IACA6B,EAAAnE,OACAmE,EAAApB,MACAoB,EAAAlE,QACAkE,EAAAnB,SACAmB,EAAAhU,OAEAkU,EAAAlT,EAAA+S,EAAAC,OACS,CAEThC,EAAAjO,KAAAiQ,EAAAhU,QAEO,CAIP,IAAAmU,EAAAnT,EAAAhB,KACAgB,EAAAhB,KAAA,KAEA,GAAA8R,EAAAqC,EAAArU,IAAAE,EAAAF,KAAA,CAGA,IAAAsU,EAAA,EACA,GACA,IAAAC,EAAApG,EAAAE,aACA,IAAA5H,GAAAvF,EAAA8O,MAAA9O,EAAA6O,MAAAwE,EACA,IAAA7N,GAAAxF,EAAA6R,OAAA7R,EAAA4R,KAAAyB,EAEAF,EAAArU,IAAAmF,EAAAjE,EAAA6O,KAAAtJ,EACA4N,EAAArU,IAAAoF,EAAAlE,EAAA4R,IAAApM,EACA4N,GAAA,QAEWA,EAAA,GAAAtC,EAAAqC,EAAArU,IAAAE,EAAAF,MAEX,GAAAsU,IAAA,GAAAtC,EAAAqC,EAAArU,IAAAE,EAAAF,KAAA,CAKA,QAIAkS,EAAAjO,KAAA/C,EAAAmT,GACAnC,EAAAjO,KAAA/C,EAAAhB,OAMA,SAAAiU,EAAAjT,EAAA0I,GACA,GAAAA,IAAA,SAAA1I,EAAAsR,MACA,GAAA5I,IAAA,SAAA1I,EAAAuR,MACA,GAAA7I,IAAA,SAAA1I,EAAAwR,MACA,GAAA9I,IAAA,SAAA1I,EAAAyR,MACA,YAGA,SAAAyB,EAAAlT,EAAA0I,EAAAsK,GACA,GAAAtK,IAAA,EAAA1I,EAAAsR,MAAA0B,OACA,GAAAtK,IAAA,EAAA1I,EAAAuR,MAAAyB,OACA,GAAAtK,IAAA,EAAA1I,EAAAwR,MAAAwB,OACA,GAAAtK,IAAA,EAAA1I,EAAAyR,MAAAuB,yBCrUA5X,EAAAC,QAAA,SAAAyV,EAAAwC,EAAAC,GACA,IAAAhO,EAAA3C,KAAA+J,IAAA2G,EAAArP,EAAAsP,EAAAtP,GACA,IAAAuB,EAAA5C,KAAA+J,IAAA2G,EAAApP,EAAAqP,EAAArP,GAEA,OAAAqB,EAAA,MAAAC,EAAA,4BCJApK,EAAAC,QAAA4R,EAGA7R,EAAAC,QAAA4R,SACA7R,EAAAC,QAAAmY,iBAMA,SAAAvG,EAAAwG,GACA,IAAArE,SAAAqE,IAAA,SAAAA,GAAA,IAAAC,KACA,WAAAC,EAAAvE,GAGA,SAAAuE,EAAAvE,GACAxT,KAAAwT,OAQAuE,EAAAvU,UAAAoQ,OAMAmE,EAAAvU,UAAA+N,aAKAwG,EAAAvU,UAAAwU,QAAAzG,EAEAwG,EAAAvU,UAAAyU,WAEA,SAAAA,IAGA,IAAA3G,EAAAjJ,EAAAC,EACA,GACAD,EAAArI,KAAAuR,aAAA,IACAjJ,EAAAtI,KAAAuR,aAAA,IACAD,EAAAjJ,IAAAC,UACGgJ,GAAA,GAAAA,IAAA,GAEH,OAAAjJ,EAAArB,KAAA8J,MAAA,EAAA9J,KAAAkR,IAAA5G,MAGA,SAAAC,IACA,IAAAiC,EAAAxT,KAAAwT,KAEAA,IAAA,YAAAA,GAAA,eACAA,KAAA,WAAAA,IAAA,eACAA,IAAA,WAAAA,GAAA,cACAA,KAAA,WAAAA,GAAA,cACAA,IAAA,YAAAA,GAAA,cACAA,KAAA,WAAAA,IAAA,eACAxT,KAAAwT,OACA,OAAAA,EAAA,qBAGA,SAAAI,EAAAuE,GACA,OAAAnR,KAAA0M,MAAA1T,KAAAuR,aAAA4G,GAOA,SAAAP,EAAAhK,EAAAwK,GACA,IAAAC,EAAAD,GAAA/G,IACA,UAAAgH,EAAAzE,OAAA,YACA,UAAApS,MAAA,wEAGA,OACAuD,UAKAuT,WAGA,SAAAA,IACA,IAAA7S,EAAAgN,EAAA8F,EACA,IAAA9S,EAAAmI,EAAA/N,OAAA,EAA8B4F,EAAA,IAAOA,EAAA,CACrCgN,EAAA4F,EAAAzE,KAAAnO,EAAA,GACA8S,EAAA3K,EAAA6E,GACA7E,EAAA6E,GAAA7E,EAAAnI,GACAmI,EAAAnI,GAAA8S,EAGA,OAAA3K,EAGA,SAAA7I,EAAAoI,GACA,IAAA1H,EAAAgN,EAAA8F,EACA,IAAA9S,EAAAmI,EAAA/N,OAAA,EAA8B4F,EAAA,IAAOA,EAAA,CACrCgN,EAAA4F,EAAAzE,KAAAnO,EAAA,GACA8S,EAAA3K,EAAA6E,GACA7E,EAAA6E,GAAA7E,EAAAnI,GACAmI,EAAAnI,GAAA8S,EAEApL,EAAAoL,GAGA,GAAA3K,EAAA/N,OAAA,CACAsN,EAAAS,EAAA,6BChHApO,EAAAC,QAAA,CACA4R,SACAuG,kBAOA,SAAAvG,EAAAwG,GACA,IAAArE,SAAAqE,IAAA,SAAAA,GAAA,IAAAC,KACA,IAAAU,EAAA,WAEAhF,IAAA,YAAAA,GAAA,eACAA,KAAA,WAAAA,IAAA,eACAA,IAAA,WAAAA,GAAA,cACAA,KAAA,WAAAA,GAAA,cACAA,IAAA,YAAAA,GAAA,cACAA,KAAA,WAAAA,IAAA,eACA,OAAAA,EAAA,sBAGA,OAMAI,KAAA,SAAAuE,GACA,OAAAnR,KAAA0M,MAAA8E,IAAAL,IAOA5G,WAAA,WACA,OAAAiH,MASA,SAAAZ,EAAAhK,EAAAwK,GACA,IAAAC,EAAAD,GAAA/G,IACA,UAAAgH,EAAAzE,OAAA,YACA,UAAApS,MAAA,wEAGA,OACAuD,QAAA,SAAAoI,GACA,IAAA1H,EAAAgN,EAAA8F,EACA,IAAA9S,EAAAmI,EAAA/N,OAAA,EAAsC4F,EAAA,IAAOA,EAAA,CAC7CgN,EAAA4F,EAAAzE,KAAAnO,EAAA,GACA8S,EAAA3K,EAAA6E,GACA7E,EAAA6E,GAAA7E,EAAAnI,GACAmI,EAAAnI,GAAA8S,EAEApL,EAAAoL,GAGA,GAAA3K,EAAA/N,OAAA,CACAsN,EAAAS,EAAA,MAOA0K,QAAA,WACA,IAAA7S,EAAAgN,EAAA8F,EACA,IAAA9S,EAAAmI,EAAA/N,OAAA,EAAsC4F,EAAA,IAAOA,EAAA,CAC7CgN,EAAA4F,EAAAzE,KAAAnO,EAAA,GACA8S,EAAA3K,EAAA6E,GACA7E,EAAA6E,GAAA7E,EAAAnI,GACAmI,EAAAnI,GAAA8S,EAGA,OAAA3K,2BCjFA,IAAI6K,EAAa,GACjB,IAAIlQ,EAAOjI,EAAQ,QACnBd,EAAOC,QAAU,SAAUW,EAAWC,EAAOG,GAC3C,IAAIkY,EAAQC,OAAOC,WACfC,EAASF,OAAOG,YAEpB,IAAI7P,EAAQ,IAAIV,EAAKwQ,MAAM,SAAU,MACrC,IAAIC,EAAWzQ,EAAK0Q,mBAAmBP,EAAOG,EAAQ,KAAM,MAAO,MACnEG,EAASE,KAAKC,MAAMC,QAAU,QAC9BhZ,EAAUiZ,YAAYL,EAASE,MAE/B,IAAIvY,EAAW,IAAI4H,EAAK+Q,SACxB3Y,EAASyF,SAASiC,EAAIqQ,EAAM,EAC5B/X,EAASyF,SAASkC,EAAIuQ,EAAO,EAE7BlY,EAASkI,MAAMR,EAAI,EACnB1H,EAASkI,MAAMP,EAAI,EACnBW,EAAMsQ,SAAS5Y,GAIf,IAAI6Y,EAAgB,GAChBC,EAAiB,GACjBC,EAAgB,GAEpBrZ,EAAM4F,YAAY,SAAS7B,GACzBoV,EAAcrS,KAAK3G,EAAOuC,gBAAgBqB,EAAKE,KAC/CmV,EAAeD,EAAc3Z,OAAS,GAAKuE,IAG7C/D,EAAM6F,YAAY,SAASd,GACzBsU,EAAcvS,KAAK3G,EAAOqD,gBAAgBuB,EAAKd,OAGjD,MAAO,CACLzD,YAAa,SAAAA,IACXL,EAAOiC,OACPkX,EAAUhZ,EAAU6Y,EAAeE,GACnCV,EAASY,OAAO3Q,IAElBtB,aAAcqR,EAASE,KACvBxR,cAAe/G,EACfsI,MAAOA,EAEPQ,UAAW,SAAAA,EAAUpB,EAAGC,GACtB,IAAIuR,EAAOpB,EAAW,EAGtB,IAAK,IAAIhT,EAAI,EAAGA,EAAI+T,EAAc3Z,SAAU4F,EAAG,CAC7C,IAAIvC,EAAMsW,EAAc/T,GACxB,IAAIqU,EAAa5W,EAAImF,EAAIwR,EAAOxR,GAAKA,EAAInF,EAAImF,EAAIwR,GAChC3W,EAAIoF,EAAIuR,EAAOvR,GAAKA,EAAIpF,EAAIoF,EAAIuR,EAEjD,GAAIC,EAAY,CACd,OAAOL,EAAehU,QAOhC,SAASkU,EAAUhZ,EAAU6Y,EAAeE,GAE1C/Y,EAASgL,QACThL,EAASoZ,UAAU,UACnB,IAAItU,EAAG4C,EAAGC,EAAGoO,EAAIE,EAEjBjW,EAASqZ,UAAU,EAAG,SAAU,GAChC,IAAIvU,EAAI,EAAGA,EAAIiU,EAAc7Z,SAAU4F,EAAG,CACxC,IAAIL,EAAOsU,EAAcjU,GACzB9E,EAASsZ,OAAO7U,EAAKnF,KAAKoI,EAAGjD,EAAKnF,KAAKqI,GACvC3H,EAASuZ,OAAO9U,EAAKlF,GAAGmI,EAAGjD,EAAKlF,GAAGoI,GAGrC3H,EAASqZ,UAAU,GACnB,IAAIH,EAAOpB,EAAW,EACtB,IAAKhT,EAAI,EAAGA,EAAI+T,EAAc3Z,SAAU4F,EAAG,CACzC4C,EAAImR,EAAc/T,GAAG4C,EAAIwR,EACzBvR,EAAIkR,EAAc/T,GAAG6C,EAAIuR,EACzBlZ,EAASwZ,SAAS9R,EAAGC,EAAGmQ,EAAYA,yBC/ExCjZ,EAAAC,QAAA2a,EAgBA,SAAAA,EAAAjY,EAAA6O,EAAAqJ,GACA,IAAAC,EAAAxY,OAAA0B,UAAA4K,SAAA1K,KAAA2W,KAAA,iBACA,GAAAC,EAAA,CACA,QAAA7U,EAAA,EAAmBA,EAAA4U,EAAAxa,SAAmB4F,EAAA,CACtC8U,EAAApY,EAAA6O,EAAAqJ,EAAA5U,SAEG,CACH,QAAAwL,KAAA9O,EAAA,CACAoY,EAAApY,EAAA6O,EAAAC,KAKA,SAAAsJ,EAAAC,EAAAxJ,EAAAC,GACA,GAAAuJ,EAAA5N,eAAAqE,GAAA,CACA,UAAAD,EAAAC,KAAA,YAEA,OAEAD,EAAAC,GAAA,SAAAd,GACA,GAAAA,IAAAhL,UAAA,CACAqV,EAAAvJ,GAAAd,EACA,OAAAa,EAEA,OAAAwJ,EAAAvJ,0BCxCAzR,EAAAC,QAAA,SAAAqU,GACAC,EAAAD,GAEA,IAAAE,EAAAC,EAAAH,GACAA,EAAAxO,GAAA0O,EAAA1O,GACAwO,EAAArP,IAAAuP,EAAAvP,IACAqP,EAAAnR,KAAAqR,EAAArR,KACA,OAAAmR,GAGA,SAAAG,EAAAH,GAMA,IAAAI,EAAApS,OAAAC,OAAA,MAEA,OACAuD,GAAA,SAAA6O,EAAAhH,EAAAhF,GACA,UAAAgF,IAAA,YACA,UAAA3L,MAAA,yCAEA,IAAA4S,EAAAF,EAAAC,GACA,IAAAC,EAAA,CACAA,EAAAF,EAAAC,GAAA,GAEAC,EAAAjN,KAAA,CAAqBgG,WAAAhF,QAErB,OAAA2L,GAGArP,IAAA,SAAA0P,EAAAhH,GACA,IAAAkH,SAAAF,IAAA,YACA,GAAAE,EAAA,CAEAH,EAAApS,OAAAC,OAAA,MACA,OAAA+R,EAGA,GAAAI,EAAAC,GAAA,CACA,IAAAG,SAAAnH,IAAA,WACA,GAAAmH,EAAA,QACAJ,EAAAC,OACS,CACT,IAAAI,EAAAL,EAAAC,GACA,QAAA1O,EAAA,EAAyBA,EAAA8O,EAAA1U,SAAsB4F,EAAA,CAC/C,GAAA8O,EAAA9O,GAAA0H,aAAA,CACAoH,EAAAvH,OAAAvH,EAAA,MAMA,OAAAqO,GAGAnR,KAAA,SAAAwR,GACA,IAAAI,EAAAL,EAAAC,GACA,IAAAI,EAAA,CACA,OAAAT,EAGA,IAAAU,EACA,GAAA7Q,UAAA9D,OAAA,GACA2U,EAAAjR,MAAAC,UAAAwJ,OAAAtJ,KAAAC,UAAA,GAEA,QAAA8B,EAAA,EAAoBA,EAAA8O,EAAA1U,SAAsB4F,EAAA,CAC1C,IAAAgP,EAAAF,EAAA9O,GACAgP,EAAAtH,SAAA7J,MAAAmR,EAAAtM,IAAAqM,GAGA,OAAAV,IAKA,SAAAC,EAAAD,GACA,IAAAA,EAAA,CACA,UAAAtS,MAAA,sDAEA,IAAAkT,EAAA,oBACA,QAAAjP,EAAA,EAAiBA,EAAAiP,EAAA7U,SAA0B4F,EAAA,CAC3C,GAAAqO,EAAAlH,eAAA8H,EAAAjP,IAAA,CACA,UAAAjE,MAAA,gEAAAkT,EAAAjP,GAAA,4BCpFAjG,EAAAC,QAAA,CACAgb,OACAC,WACAC,SACAC,YAGA,SAAAH,EAAApS,EAAAC,GACAtI,KAAAkD,IAAA,IAAAwX,EAAArS,EAAAC,GACAtI,KAAA6a,QAAA,IAAAH,EAAArS,EAAAC,GACAtI,KAAAqQ,MAAA,IAAAqK,EACA1a,KAAA0Q,SAAA,IAAAgK,EACA1a,KAAAoH,KAAA,EAGAqT,EAAAjX,UAAAH,YAAA,SAAAgF,EAAAC,GACAtI,KAAA6a,QAAAxS,EAAArI,KAAAkD,IAAAmF,IACArI,KAAA6a,QAAAvS,EAAAtI,KAAAkD,IAAAoF,KAGA,SAAAoS,EAAArS,EAAAC,GACA,GAAAD,cAAA,UAEArI,KAAAqI,eAAA,SAAAA,IAAA,EACArI,KAAAsI,SAAAD,EAAAC,IAAA,SAAAD,EAAAC,EAAA,MACG,CACHtI,KAAAqI,aAAA,SAAAA,EAAA,EACArI,KAAAsI,aAAA,SAAAA,EAAA,GAIAoS,EAAAlX,UAAAmM,MAAA,WACA3P,KAAAqI,EAAArI,KAAAsI,EAAA,GAGA,SAAAqS,EAAAtS,EAAAC,EAAAqK,GACA3S,KAAAkD,IAAA,IAAA0X,EAAAvS,EAAAC,EAAAqK,GACA3S,KAAA6a,QAAA,IAAAD,EAAAvS,EAAAC,EAAAqK,GACA3S,KAAAqQ,MAAA,IAAAuK,EACA5a,KAAA0Q,SAAA,IAAAkK,EACA5a,KAAAoH,KAAA,EAGAuT,EAAAnX,UAAAH,YAAA,SAAAgF,EAAAC,EAAAqK,GACA3S,KAAA6a,QAAAxS,EAAArI,KAAAkD,IAAAmF,IACArI,KAAA6a,QAAAvS,EAAAtI,KAAAkD,IAAAoF,IACAtI,KAAA6a,QAAAlI,EAAA3S,KAAAkD,IAAAyP,KAGA,SAAAiI,EAAAvS,EAAAC,EAAAqK,GACA,GAAAtK,cAAA,UAEArI,KAAAqI,eAAA,SAAAA,IAAA,EACArI,KAAAsI,SAAAD,EAAAC,IAAA,SAAAD,EAAAC,EAAA,EACAtI,KAAA2S,SAAAtK,EAAAsK,IAAA,SAAAtK,EAAAsK,EAAA,MACG,CACH3S,KAAAqI,aAAA,SAAAA,EAAA,EACArI,KAAAsI,aAAA,SAAAA,EAAA,EACAtI,KAAA2S,aAAA,SAAAA,EAAA,GAIAiI,EAAApX,UAAAmM,MAAA,WACA3P,KAAAqI,EAAArI,KAAAsI,EAAAtI,KAAA2S,EAAA,6CC/DA,IAAAiH,EAAA,WAA0B,IAAAkB,EAAA9a,KAAa,IAAA+a,EAAAD,EAAAE,eAA0B,IAAAC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,YAAA,WAAsB,CAAAF,EAAA,OAAYG,IAAA,OAAAD,YAAA,aAChJ,IAAAE,EAAA,gFCUA,IAAAC,EAAA,CACAC,WAAA,GAGAC,QAJA,SAAAA,IAKAC,EAAAC,EAAAvb,KAAAH,KAAA2b,MAAAC,OAGAC,QAAA,GAGAtU,KAXA,SAAAA,IAYA,OACAuU,OAAA,MCxB0O,IAAAC,EAAA,kCCQ1O,IAAAC,EAAgBla,OAAAma,EAAA,KAAAna,CACdia,EACAnC,EACAyB,EACF,MACA,KACA,KACA,MAIe,IAAAa,EAAAC,EAAA,WAAAH,gCCnBfxc,EAAAC,QAAA,SAAAsP,EAAA5M,GACA,IAAAkP,EAAehQ,EAAQ,QAAegQ,OAAA,IACtC,IAAA+K,EAAA,CAAsB1F,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,GAEtB,OACA7G,IAAAkM,EAEA3M,OAAA4M,EAEA1M,MAAA,WACAyM,EAAA1F,GAAA0F,EAAAxF,GAAA,EACAwF,EAAAvF,GAAAuF,EAAArF,GAAA,GAGA9G,mBAAA,SAAA5J,GACA,IAAAiW,EAAAF,EAEA,IAAAG,EAAA,EAAAC,EAAA,EAEA,GAAAnW,EAAAxG,OAAA,CACA,QAAA4F,EAAA,EAAuBA,EAAAY,EAAAxG,SAAsB4F,EAAA,CAC7C8W,GAAAlW,EAAAZ,GAAAvC,IAAAmF,EACAmU,GAAAnW,EAAAZ,GAAAvC,IAAAoF,EAGAiU,GAAAlW,EAAAxG,OACA2c,GAAAnW,EAAAxG,WACO,CACP0c,GAAAD,EAAA5F,GAAA4F,EAAAzF,IAAA,EACA2F,GAAAF,EAAA1F,GAAA0F,EAAAvF,IAAA,EAGA,IAAAhW,EAAAoB,EAAApB,aACA,OACAsH,EAAAkU,EAAAlL,EAAAuC,KAAA7S,KAAA,EACAuH,EAAAkU,EAAAnL,EAAAuC,KAAA7S,KAAA,KAKA,SAAAsb,IACA,IAAA5W,EAAAsJ,EAAAlP,OACA,GAAA4F,IAAA,GAAkB,OAElB,IAAAiR,EAAArP,OAAAsP,UACAC,EAAAvP,OAAAsP,UACAE,EAAAxP,OAAAyP,UACAC,EAAA1P,OAAAyP,UAEA,MAAArR,IAAA,CAGA,IAAArC,EAAA2L,EAAAtJ,GACA,GAAArC,EAAAiB,SAAA,CACAjB,EAAAF,IAAAmF,EAAAjF,EAAAyX,QAAAxS,EACAjF,EAAAF,IAAAoF,EAAAlF,EAAAyX,QAAAvS,MACO,CACPlF,EAAAyX,QAAAxS,EAAAjF,EAAAF,IAAAmF,EACAjF,EAAAyX,QAAAvS,EAAAlF,EAAAF,IAAAoF,EAEA,GAAAlF,EAAAF,IAAAmF,EAAAqO,EAAA,CACAA,EAAAtT,EAAAF,IAAAmF,EAEA,GAAAjF,EAAAF,IAAAmF,EAAAwO,EAAA,CACAA,EAAAzT,EAAAF,IAAAmF,EAEA,GAAAjF,EAAAF,IAAAoF,EAAAsO,EAAA,CACAA,EAAAxT,EAAAF,IAAAoF,EAEA,GAAAlF,EAAAF,IAAAoF,EAAAyO,EAAA,CACAA,EAAA3T,EAAAF,IAAAoF,GAIA8T,EAAA1F,KACA0F,EAAAvF,KACAuF,EAAAxF,KACAwF,EAAArF,2BC1EAvX,EAAAC,QAAA,SAAA2M,IAGApM,KAAAoD,KAAA,KAMApD,KAAA0V,MAAA,KACA1V,KAAA2V,MAAA,KACA3V,KAAA4V,MAAA,KACA5V,KAAA6V,MAAA,KAGA7V,KAAAoH,KAAA,EAGApH,KAAA8V,MAAA,EACA9V,KAAA+V,MAAA,EAGA/V,KAAAiT,KAAA,EACAjT,KAAAgW,IAAA,EACAhW,KAAAiW,OAAA,EACAjW,KAAAkT,MAAA,yBC5BA,IAAAuJ,EAAcpb,EAAQ,QAEtB7B,EAAAC,QAAA,SAAAyD,GACA,WAAAuZ,EAAAhC,KAAAvX,wBCGA1D,EAAOC,QAAUgI,EAEjB,IAAIiV,EAAS,GAAIC,EAAmBC,EAASC,EAG7C,GAAKlE,OAAOmE,iBAAmB,CAC3BH,EAAoB,uBACjB,CACHA,EAAoB,cACpBD,EAAS,KAIbG,EAAU,YAAaE,SAASC,cAAc,OAAS,QAC7CD,SAASE,eAAiB9X,UAAY,aACtC,iBAEV,SAASsC,EAAkByV,EAAM/P,EAAUgQ,GACvCC,EAAmBF,EAAML,EAAS1P,EAAUgQ,GAG5C,GAAIN,GAAW,iBAAmB,CAC9BO,EAAmBF,EAAM,sBAAuB/P,EAAUgQ,IAIlE,SAASC,EAAmBF,EAAM/I,EAAWhH,EAAUgQ,GACnDD,EAAMP,GAAqBD,EAASvI,EAAW0I,GAAW,QAAU1P,EAAW,SAAUkQ,IACpFA,IAAmBA,EAAgB1E,OAAO2E,OAG3C,IAAIA,EAAQ,CAERD,cAAeA,EACfrM,OAAQqM,EAAcrM,QAAUqM,EAAcE,WAC9CC,KAAM,QACNC,UAAWJ,EAAcG,MAAQ,sBAAwB,EAAI,EAC7DE,OAAQ,EACRC,OAAQ,EACRC,eAAgB,SAAAA,IACZP,EAAcO,eACVP,EAAcO,iBACdP,EAAcQ,YAAc,QAKxC,GAAKhB,GAAW,aAAe,CAC3BS,EAAMtV,QAAW,EAAE,GAAKqV,EAAcS,WAEtCT,EAAcU,cAAiBT,EAAMI,QAAW,EAAE,GAAKL,EAAcU,iBAClE,CACHT,EAAMtV,OAASqV,EAAcW,OAIjC,OAAO7Q,EAAUmQ,IAElBH,GAAc", "file": "js/02829d02.7e3b8aee.js", "sourcesContent": ["module.exports = Spring;\n\n/**\n * Represents a physical spring. Spring connects two bodies, has rest length\n * stiffness coefficient and optional weight\n */\nfunction Spring(fromBody, toBody, length, coeff, weight) {\n    this.from = fromBody;\n    this.to = toBody;\n    this.length = length;\n    this.coeff = coeff;\n\n    this.weight = typeof weight === 'number' ? weight : 1;\n};\n", "module.exports.main = function (conainter) {\n    var graph = require('ngraph.generators').balancedBinTree(9),\n        layout = createLayout(graph);\n  \n    var createPixiGraphics = require('./pixiGraphics');\n    var graphics = createPixiGraphics(conainter, graph, layout);\n  \n    // Listen to mouse events and update graph acoordingly:\n    var bindGlobalInput = require('./globalInput');\n    bindGlobalInput(graphics, layout);\n  \n    // begin animation loop:\n    renderFrame();\n  \n    function renderFrame() {\n      graphics.renderFrame();\n      requestAnimationFrame(renderFrame);\n    }\n  }\n  \n  function createLayout(graph) {\n    var layout = require('ngraph.forcelayout');\n  \n    return layout(graph, {\n            springLength: 30,\n            springCoeff: 0.0008,\n            dragCoeff: 0.01,\n            gravity: -1.2,\n            theta: 1\n          });\n  }", "module.exports = createLayout;\nmodule.exports.simulator = require('ngraph.physics.simulator');\n\nvar eventify = require('ngraph.events');\n\n/**\n * Creates force based layout for a given graph.\n *\n * @param {ngraph.graph} graph which needs to be laid out\n * @param {object} physicsSettings if you need custom settings\n * for physics simulator you can pass your own settings here. If it's not passed\n * a default one will be created.\n */\nfunction createLayout(graph, physicsSettings) {\n  if (!graph) {\n    throw new Error('Graph structure cannot be undefined');\n  }\n\n  var createSimulator = require('ngraph.physics.simulator');\n  var physicsSimulator = createSimulator(physicsSettings);\n\n  var nodeMass = defaultNodeMass\n  if (physicsSettings && typeof physicsSettings.nodeMass === 'function') {\n    nodeMass = physicsSettings.nodeMass\n  }\n\n  var nodeBodies = Object.create(null);\n  var springs = {};\n  var bodiesCount = 0;\n\n  var springTransform = physicsSimulator.settings.springTransform || noop;\n\n  // Initialize physics with what we have in the graph:\n  initPhysics();\n  listenToEvents();\n\n  var wasStable = false;\n\n  var api = {\n    /**\n     * Performs one step of iterative layout algorithm\n     *\n     * @returns {boolean} true if the system should be considered stable; Flase otherwise.\n     * The system is stable if no further call to `step()` can improve the layout.\n     */\n    step: function() {\n      if (bodiesCount === 0) return true; // TODO: This will never fire 'stable'\n\n      var lastMove = physicsSimulator.step();\n\n      // Save the movement in case if someone wants to query it in the step\n      // callback.\n      api.lastMove = lastMove;\n\n      // Allow listeners to perform low-level actions after nodes are updated.\n      api.fire('step');\n\n      var ratio = lastMove/bodiesCount;\n      var isStableNow = ratio <= 0.01; // TODO: The number is somewhat arbitrary...\n\n      if (wasStable !== isStableNow) {\n        wasStable = isStableNow;\n        onStableChanged(isStableNow);\n      }\n\n      return isStableNow;\n    },\n\n    /**\n     * For a given `nodeId` returns position\n     */\n    getNodePosition: function (nodeId) {\n      return getInitializedBody(nodeId).pos;\n    },\n\n    /**\n     * Sets position of a node to a given coordinates\n     * @param {string} nodeId node identifier\n     * @param {number} x position of a node\n     * @param {number} y position of a node\n     * @param {number=} z position of node (only if applicable to body)\n     */\n    setNodePosition: function (nodeId) {\n      var body = getInitializedBody(nodeId);\n      body.setPosition.apply(body, Array.prototype.slice.call(arguments, 1));\n      physicsSimulator.invalidateBBox();\n    },\n\n    /**\n     * @returns {Object} Link position by link id\n     * @returns {Object.from} {x, y} coordinates of link start\n     * @returns {Object.to} {x, y} coordinates of link end\n     */\n    getLinkPosition: function (linkId) {\n      var spring = springs[linkId];\n      if (spring) {\n        return {\n          from: spring.from.pos,\n          to: spring.to.pos\n        };\n      }\n    },\n\n    /**\n     * @returns {Object} area required to fit in the graph. Object contains\n     * `x1`, `y1` - top left coordinates\n     * `x2`, `y2` - bottom right coordinates\n     */\n    getGraphRect: function () {\n      return physicsSimulator.getBBox();\n    },\n\n    /**\n     * Iterates over each body in the layout simulator and performs a callback(body, nodeId)\n     */\n    forEachBody: forEachBody,\n\n    /*\n     * Requests layout algorithm to pin/unpin node to its current position\n     * Pinned nodes should not be affected by layout algorithm and always\n     * remain at their position\n     */\n    pinNode: function (node, isPinned) {\n      var body = getInitializedBody(node.id);\n       body.isPinned = !!isPinned;\n    },\n\n    /**\n     * Checks whether given graph's node is currently pinned\n     */\n    isNodePinned: function (node) {\n      return getInitializedBody(node.id).isPinned;\n    },\n\n    /**\n     * Request to release all resources\n     */\n    dispose: function() {\n      graph.off('changed', onGraphChanged);\n      api.fire('disposed');\n    },\n\n    /**\n     * Gets physical body for a given node id. If node is not found undefined\n     * value is returned.\n     */\n    getBody: getBody,\n\n    /**\n     * Gets spring for a given edge.\n     *\n     * @param {string} linkId link identifer. If two arguments are passed then\n     * this argument is treated as formNodeId\n     * @param {string=} toId when defined this parameter denotes head of the link\n     * and first argument is trated as tail of the link (fromId)\n     */\n    getSpring: getSpring,\n\n    /**\n     * [Read only] Gets current physics simulator\n     */\n    simulator: physicsSimulator,\n\n    /**\n     * Gets the graph that was used for layout\n     */\n    graph: graph,\n\n    /**\n     * Gets amount of movement performed during last step opeartion\n     */\n    lastMove: 0\n  };\n\n  eventify(api);\n\n  return api;\n\n  function forEachBody(cb) {\n    Object.keys(nodeBodies).forEach(function(bodyId) {\n      cb(nodeBodies[bodyId], bodyId);\n    });\n  }\n\n  function getSpring(fromId, toId) {\n    var linkId;\n    if (toId === undefined) {\n      if (typeof fromId !== 'object') {\n        // assume fromId as a linkId:\n        linkId = fromId;\n      } else {\n        // assume fromId to be a link object:\n        linkId = fromId.id;\n      }\n    } else {\n      // toId is defined, should grab link:\n      var link = graph.hasLink(fromId, toId);\n      if (!link) return;\n      linkId = link.id;\n    }\n\n    return springs[linkId];\n  }\n\n  function getBody(nodeId) {\n    return nodeBodies[nodeId];\n  }\n\n  function listenToEvents() {\n    graph.on('changed', onGraphChanged);\n  }\n\n  function onStableChanged(isStable) {\n    api.fire('stable', isStable);\n  }\n\n  function onGraphChanged(changes) {\n    for (var i = 0; i < changes.length; ++i) {\n      var change = changes[i];\n      if (change.changeType === 'add') {\n        if (change.node) {\n          initBody(change.node.id);\n        }\n        if (change.link) {\n          initLink(change.link);\n        }\n      } else if (change.changeType === 'remove') {\n        if (change.node) {\n          releaseNode(change.node);\n        }\n        if (change.link) {\n          releaseLink(change.link);\n        }\n      }\n    }\n    bodiesCount = graph.getNodesCount();\n  }\n\n  function initPhysics() {\n    bodiesCount = 0;\n\n    graph.forEachNode(function (node) {\n      initBody(node.id);\n      bodiesCount += 1;\n    });\n\n    graph.forEachLink(initLink);\n  }\n\n  function initBody(nodeId) {\n    var body = nodeBodies[nodeId];\n    if (!body) {\n      var node = graph.getNode(nodeId);\n      if (!node) {\n        throw new Error('initBody() was called with unknown node id');\n      }\n\n      var pos = node.position;\n      if (!pos) {\n        var neighbors = getNeighborBodies(node);\n        pos = physicsSimulator.getBestNewBodyPosition(neighbors);\n      }\n\n      body = physicsSimulator.addBodyAt(pos);\n      body.id = nodeId;\n\n      nodeBodies[nodeId] = body;\n      updateBodyMass(nodeId);\n\n      if (isNodeOriginallyPinned(node)) {\n        body.isPinned = true;\n      }\n    }\n  }\n\n  function releaseNode(node) {\n    var nodeId = node.id;\n    var body = nodeBodies[nodeId];\n    if (body) {\n      nodeBodies[nodeId] = null;\n      delete nodeBodies[nodeId];\n\n      physicsSimulator.removeBody(body);\n    }\n  }\n\n  function initLink(link) {\n    updateBodyMass(link.fromId);\n    updateBodyMass(link.toId);\n\n    var fromBody = nodeBodies[link.fromId],\n        toBody  = nodeBodies[link.toId],\n        spring = physicsSimulator.addSpring(fromBody, toBody, link.length);\n\n    springTransform(link, spring);\n\n    springs[link.id] = spring;\n  }\n\n  function releaseLink(link) {\n    var spring = springs[link.id];\n    if (spring) {\n      var from = graph.getNode(link.fromId),\n          to = graph.getNode(link.toId);\n\n      if (from) updateBodyMass(from.id);\n      if (to) updateBodyMass(to.id);\n\n      delete springs[link.id];\n\n      physicsSimulator.removeSpring(spring);\n    }\n  }\n\n  function getNeighborBodies(node) {\n    // TODO: Could probably be done better on memory\n    var neighbors = [];\n    if (!node.links) {\n      return neighbors;\n    }\n    var maxNeighbors = Math.min(node.links.length, 2);\n    for (var i = 0; i < maxNeighbors; ++i) {\n      var link = node.links[i];\n      var otherBody = link.fromId !== node.id ? nodeBodies[link.fromId] : nodeBodies[link.toId];\n      if (otherBody && otherBody.pos) {\n        neighbors.push(otherBody);\n      }\n    }\n\n    return neighbors;\n  }\n\n  function updateBodyMass(nodeId) {\n    var body = nodeBodies[nodeId];\n    body.mass = nodeMass(nodeId);\n    if (Number.isNaN(body.mass)) {\n      throw new Error('Node mass should be a number')\n    }\n  }\n\n  /**\n   * Checks whether graph node has in its settings pinned attribute,\n   * which means layout algorithm cannot move it. Node can be preconfigured\n   * as pinned, if it has \"isPinned\" attribute, or when node.data has it.\n   *\n   * @param {Object} node a graph node to check\n   * @return {Boolean} true if node should be treated as pinned; false otherwise.\n   */\n  function isNodeOriginallyPinned(node) {\n    return (node && (node.isPinned || (node.data && node.data.isPinned)));\n  }\n\n  function getInitializedBody(nodeId) {\n    var body = nodeBodies[nodeId];\n    if (!body) {\n      initBody(nodeId);\n      body = nodeBodies[nodeId];\n    }\n    return body;\n  }\n\n  /**\n   * Calculates mass of a body, which corresponds to node with given id.\n   *\n   * @param {String|Number} nodeId identifier of a node, for which body mass needs to be calculated\n   * @returns {Number} recommended mass of the body;\n   */\n  function defaultNodeMass(nodeId) {\n    var links = graph.getLinks(nodeId);\n    if (!links) return 1;\n    return 1 + links.length / 3.0;\n  }\n}\n\nfunction noop() { }\n", "module.exports = function (graphics, layout) {\n    var addWheelListener = require('./lib/addWheelListener');\n    var graphGraphics = graphics.graphGraphics;\n  \n    addWheelListener(graphics.domContainer, function (e) {\n      zoom(e.clientX, e.clientY, e.deltaY < 0);\n    });\n  \n    addDragNDrop();\n  \n    var getGraphCoordinates = (function () {\n      var ctx = {\n        global: { x: 0, y: 0} // store it inside closure to avoid GC pressure\n      };\n  \n      return function (x, y) {\n        ctx.global.x = x; ctx.global.y = y;\n        return PIXI.InteractionData.prototype.getLocalPosition.call(ctx, graphGraphics);\n      }\n    }());\n  \n    function zoom(x, y, isZoomIn) {\n      direction = isZoomIn ? 1 : -1;\n      var factor = (1 + direction * 0.1);\n      graphGraphics.scale.x *= factor;\n      graphGraphics.scale.y *= factor;\n  \n      // Technically code below is not required, but helps to zoom on mouse\n      // cursor, instead center of graphGraphics coordinates\n      var beforeTransform = getGraphCoordinates(x, y);\n      graphGraphics.updateTransform();\n      var afterTransform = getGraphCoordinates(x, y);\n  \n      graphGraphics.position.x += (afterTransform.x - beforeTransform.x) * graphGraphics.scale.x;\n      graphGraphics.position.y += (afterTransform.y - beforeTransform.y) * graphGraphics.scale.y;\n      graphGraphics.updateTransform();\n    }\n  \n    function addDragNDrop() {\n      var stage = graphics.stage;\n      stage,interactive = true;\n      //stage.setInteractive(true);\n  \n      var isDragging = false,\n          nodeUnderCursor,\n          prevX, prevY;\n  \n      stage.on('mousedown', function (moveData) {\n        var pos = moveData.global;\n        var graphPos = getGraphCoordinates(pos.x, pos.y);\n        nodeUnderCursor = graphics.getNodeAt(graphPos.x, graphPos.y);\n        if (nodeUnderCursor) {\n          // just to make sure layouter will not attempt to move this node\n          // based on physical forces. Now it's completely under our control:\n          layout.pinNode(nodeUnderCursor, true);\n        }\n  \n        prevX = pos.x; prevY = pos.y;\n        isDragging = true;\n      });\n  \n      stage.mousemove = function (moveData) {\n        if (!isDragging) {\n          return;\n        }\n        var pos = moveData.global;\n  \n        if (nodeUnderCursor) {\n          var graphPos = getGraphCoordinates(pos.x, pos.y);\n          layout.setNodePosition(nodeUnderCursor.id, graphPos.x, graphPos.y);\n        } else {\n          var dx = pos.x - prevX;\n          var dy = pos.y - prevY;\n          prevX = pos.x; prevY = pos.y;\n          graphGraphics.position.x += dx;\n          graphGraphics.position.y += dy;\n        }\n      };\n  \n      stage.mouseup = function (moveDate) {\n        isDragging = false;\n        if (nodeUnderCursor) {\n          draggingNode = null;\n          layout.pinNode(nodeUnderCursor, false);\n        }\n      };\n    }\n  }", "/**\n * @fileOverview Contains definition of the core graph object.\n */\n\n// TODO: need to change storage layer:\n// 1. Be able to get all nodes O(1)\n// 2. Be able to get number of links O(1)\n\n/**\n * @example\n *  var graph = require('ngraph.graph')();\n *  graph.addNode(1);     // graph has one node.\n *  graph.addLink(2, 3);  // now graph contains three nodes and one link.\n *\n */\nmodule.exports = createGraph;\n\nvar eventify = require('ngraph.events');\n\n/**\n * Creates a new graph\n */\nfunction createGraph(options) {\n  // Graph structure is maintained as dictionary of nodes\n  // and array of links. Each node has 'links' property which\n  // hold all links related to that node. And general links\n  // array is used to speed up all links enumeration. This is inefficient\n  // in terms of memory, but simplifies coding.\n  options = options || {};\n  if ('uniqueLinkId' in options) {\n    console.warn(\n      'ngraph.graph: Starting from version 0.14 `uniqueLinkId` is deprecated.\\n' +\n      'Use `multigraph` option instead\\n',\n      '\\n',\n      'Note: there is also change in default behavior: From now on each graph\\n'+\n      'is considered to be not a multigraph by default (each edge is unique).'\n    );\n\n    options.multigraph = options.uniqueLinkId;\n  }\n\n  // Dear reader, the non-multigraphs do not guarantee that there is only\n  // one link for a given pair of node. When this option is set to false\n  // we can save some memory and CPU (18% faster for non-multigraph);\n  if (options.multigraph === undefined) options.multigraph = false;\n\n  var nodes = typeof Object.create === 'function' ? Object.create(null) : {},\n    links = [],\n    // Hash of multi-edges. Used to track ids of edges between same nodes\n    multiEdges = {},\n    nodesCount = 0,\n    suspendEvents = 0,\n\n    forEachNode = createNodeIterator(),\n    createLink = options.multigraph ? createUniqueLink : createSingleLink,\n\n    // Our graph API provides means to listen to graph changes. Users can subscribe\n    // to be notified about changes in the graph by using `on` method. However\n    // in some cases they don't use it. To avoid unnecessary memory consumption\n    // we will not record graph changes until we have at least one subscriber.\n    // Code below supports this optimization.\n    //\n    // Accumulates all changes made during graph updates.\n    // Each change element contains:\n    //  changeType - one of the strings: 'add', 'remove' or 'update';\n    //  node - if change is related to node this property is set to changed graph's node;\n    //  link - if change is related to link this property is set to changed graph's link;\n    changes = [],\n    recordLinkChange = noop,\n    recordNodeChange = noop,\n    enterModification = noop,\n    exitModification = noop;\n\n  // this is our public API:\n  var graphPart = {\n    /**\n     * Adds node to the graph. If node with given id already exists in the graph\n     * its data is extended with whatever comes in 'data' argument.\n     *\n     * @param nodeId the node's identifier. A string or number is preferred.\n     * @param [data] additional data for the node being added. If node already\n     *   exists its data object is augmented with the new one.\n     *\n     * @return {node} The newly added node or node with given id if it already exists.\n     */\n    addNode: addNode,\n\n    /**\n     * Adds a link to the graph. The function always create a new\n     * link between two nodes. If one of the nodes does not exists\n     * a new node is created.\n     *\n     * @param fromId link start node id;\n     * @param toId link end node id;\n     * @param [data] additional data to be set on the new link;\n     *\n     * @return {link} The newly created link\n     */\n    addLink: addLink,\n\n    /**\n     * Removes link from the graph. If link does not exist does nothing.\n     *\n     * @param link - object returned by addLink() or getLinks() methods.\n     *\n     * @returns true if link was removed; false otherwise.\n     */\n    removeLink: removeLink,\n\n    /**\n     * Removes node with given id from the graph. If node does not exist in the graph\n     * does nothing.\n     *\n     * @param nodeId node's identifier passed to addNode() function.\n     *\n     * @returns true if node was removed; false otherwise.\n     */\n    removeNode: removeNode,\n\n    /**\n     * Gets node with given identifier. If node does not exist undefined value is returned.\n     *\n     * @param nodeId requested node identifier;\n     *\n     * @return {node} in with requested identifier or undefined if no such node exists.\n     */\n    getNode: getNode,\n\n    /**\n     * Gets number of nodes in this graph.\n     *\n     * @return number of nodes in the graph.\n     */\n    getNodesCount: function () {\n      return nodesCount;\n    },\n\n    /**\n     * Gets total number of links in the graph.\n     */\n    getLinksCount: function () {\n      return links.length;\n    },\n\n    /**\n     * Gets all links (inbound and outbound) from the node with given id.\n     * If node with given id is not found null is returned.\n     *\n     * @param nodeId requested node identifier.\n     *\n     * @return Array of links from and to requested node if such node exists;\n     *   otherwise null is returned.\n     */\n    getLinks: getLinks,\n\n    /**\n     * Invokes callback on each node of the graph.\n     *\n     * @param {Function(node)} callback Function to be invoked. The function\n     *   is passed one argument: visited node.\n     */\n    forEachNode: forEachNode,\n\n    /**\n     * Invokes callback on every linked (adjacent) node to the given one.\n     *\n     * @param nodeId Identifier of the requested node.\n     * @param {Function(node, link)} callback Function to be called on all linked nodes.\n     *   The function is passed two parameters: adjacent node and link object itself.\n     * @param oriented if true graph treated as oriented.\n     */\n    forEachLinkedNode: forEachLinkedNode,\n\n    /**\n     * Enumerates all links in the graph\n     *\n     * @param {Function(link)} callback Function to be called on all links in the graph.\n     *   The function is passed one parameter: graph's link object.\n     *\n     * Link object contains at least the following fields:\n     *  fromId - node id where link starts;\n     *  toId - node id where link ends,\n     *  data - additional data passed to graph.addLink() method.\n     */\n    forEachLink: forEachLink,\n\n    /**\n     * Suspend all notifications about graph changes until\n     * endUpdate is called.\n     */\n    beginUpdate: enterModification,\n\n    /**\n     * Resumes all notifications about graph changes and fires\n     * graph 'changed' event in case there are any pending changes.\n     */\n    endUpdate: exitModification,\n\n    /**\n     * Removes all nodes and links from the graph.\n     */\n    clear: clear,\n\n    /**\n     * Detects whether there is a link between two nodes.\n     * Operation complexity is O(n) where n - number of links of a node.\n     * NOTE: this function is synonim for getLink()\n     *\n     * @returns link if there is one. null otherwise.\n     */\n    hasLink: getLink,\n\n    /**\n     * Detects whether there is a node with given id\n     * \n     * Operation complexity is O(1)\n     * NOTE: this function is synonim for getNode()\n     *\n     * @returns node if there is one; Falsy value otherwise.\n     */\n    hasNode: getNode,\n\n    /**\n     * Gets an edge between two nodes.\n     * Operation complexity is O(n) where n - number of links of a node.\n     *\n     * @param {string} fromId link start identifier\n     * @param {string} toId link end identifier\n     *\n     * @returns link if there is one. null otherwise.\n     */\n    getLink: getLink\n  };\n\n  // this will add `on()` and `fire()` methods.\n  eventify(graphPart);\n\n  monitorSubscribers();\n\n  return graphPart;\n\n  function monitorSubscribers() {\n    var realOn = graphPart.on;\n\n    // replace real `on` with our temporary on, which will trigger change\n    // modification monitoring:\n    graphPart.on = on;\n\n    function on() {\n      // now it's time to start tracking stuff:\n      graphPart.beginUpdate = enterModification = enterModificationReal;\n      graphPart.endUpdate = exitModification = exitModificationReal;\n      recordLinkChange = recordLinkChangeReal;\n      recordNodeChange = recordNodeChangeReal;\n\n      // this will replace current `on` method with real pub/sub from `eventify`.\n      graphPart.on = realOn;\n      // delegate to real `on` handler:\n      return realOn.apply(graphPart, arguments);\n    }\n  }\n\n  function recordLinkChangeReal(link, changeType) {\n    changes.push({\n      link: link,\n      changeType: changeType\n    });\n  }\n\n  function recordNodeChangeReal(node, changeType) {\n    changes.push({\n      node: node,\n      changeType: changeType\n    });\n  }\n\n  function addNode(nodeId, data) {\n    if (nodeId === undefined) {\n      throw new Error('Invalid node identifier');\n    }\n\n    enterModification();\n\n    var node = getNode(nodeId);\n    if (!node) {\n      node = new Node(nodeId, data);\n      nodesCount++;\n      recordNodeChange(node, 'add');\n    } else {\n      node.data = data;\n      recordNodeChange(node, 'update');\n    }\n\n    nodes[nodeId] = node;\n\n    exitModification();\n    return node;\n  }\n\n  function getNode(nodeId) {\n    return nodes[nodeId];\n  }\n\n  function removeNode(nodeId) {\n    var node = getNode(nodeId);\n    if (!node) {\n      return false;\n    }\n\n    enterModification();\n\n    var prevLinks = node.links;\n    if (prevLinks) {\n      node.links = null;\n      for(var i = 0; i < prevLinks.length; ++i) {\n        removeLink(prevLinks[i]);\n      }\n    }\n\n    delete nodes[nodeId];\n    nodesCount--;\n\n    recordNodeChange(node, 'remove');\n\n    exitModification();\n\n    return true;\n  }\n\n\n  function addLink(fromId, toId, data) {\n    enterModification();\n\n    var fromNode = getNode(fromId) || addNode(fromId);\n    var toNode = getNode(toId) || addNode(toId);\n\n    var link = createLink(fromId, toId, data);\n\n    links.push(link);\n\n    // TODO: this is not cool. On large graphs potentially would consume more memory.\n    addLinkToNode(fromNode, link);\n    if (fromId !== toId) {\n      // make sure we are not duplicating links for self-loops\n      addLinkToNode(toNode, link);\n    }\n\n    recordLinkChange(link, 'add');\n\n    exitModification();\n\n    return link;\n  }\n\n  function createSingleLink(fromId, toId, data) {\n    var linkId = makeLinkId(fromId, toId);\n    return new Link(fromId, toId, data, linkId);\n  }\n\n  function createUniqueLink(fromId, toId, data) {\n    // TODO: Get rid of this method.\n    var linkId = makeLinkId(fromId, toId);\n    var isMultiEdge = multiEdges.hasOwnProperty(linkId);\n    if (isMultiEdge || getLink(fromId, toId)) {\n      if (!isMultiEdge) {\n        multiEdges[linkId] = 0;\n      }\n      var suffix = '@' + (++multiEdges[linkId]);\n      linkId = makeLinkId(fromId + suffix, toId + suffix);\n    }\n\n    return new Link(fromId, toId, data, linkId);\n  }\n\n  function getLinks(nodeId) {\n    var node = getNode(nodeId);\n    return node ? node.links : null;\n  }\n\n  function removeLink(link) {\n    if (!link) {\n      return false;\n    }\n    var idx = indexOfElementInArray(link, links);\n    if (idx < 0) {\n      return false;\n    }\n\n    enterModification();\n\n    links.splice(idx, 1);\n\n    var fromNode = getNode(link.fromId);\n    var toNode = getNode(link.toId);\n\n    if (fromNode) {\n      idx = indexOfElementInArray(link, fromNode.links);\n      if (idx >= 0) {\n        fromNode.links.splice(idx, 1);\n      }\n    }\n\n    if (toNode) {\n      idx = indexOfElementInArray(link, toNode.links);\n      if (idx >= 0) {\n        toNode.links.splice(idx, 1);\n      }\n    }\n\n    recordLinkChange(link, 'remove');\n\n    exitModification();\n\n    return true;\n  }\n\n  function getLink(fromNodeId, toNodeId) {\n    // TODO: Use sorted links to speed this up\n    var node = getNode(fromNodeId),\n      i;\n    if (!node || !node.links) {\n      return null;\n    }\n\n    for (i = 0; i < node.links.length; ++i) {\n      var link = node.links[i];\n      if (link.fromId === fromNodeId && link.toId === toNodeId) {\n        return link;\n      }\n    }\n\n    return null; // no link.\n  }\n\n  function clear() {\n    enterModification();\n    forEachNode(function(node) {\n      removeNode(node.id);\n    });\n    exitModification();\n  }\n\n  function forEachLink(callback) {\n    var i, length;\n    if (typeof callback === 'function') {\n      for (i = 0, length = links.length; i < length; ++i) {\n        callback(links[i]);\n      }\n    }\n  }\n\n  function forEachLinkedNode(nodeId, callback, oriented) {\n    var node = getNode(nodeId);\n\n    if (node && node.links && typeof callback === 'function') {\n      if (oriented) {\n        return forEachOrientedLink(node.links, nodeId, callback);\n      } else {\n        return forEachNonOrientedLink(node.links, nodeId, callback);\n      }\n    }\n  }\n\n  function forEachNonOrientedLink(links, nodeId, callback) {\n    var quitFast;\n    for (var i = 0; i < links.length; ++i) {\n      var link = links[i];\n      var linkedNodeId = link.fromId === nodeId ? link.toId : link.fromId;\n\n      quitFast = callback(nodes[linkedNodeId], link);\n      if (quitFast) {\n        return true; // Client does not need more iterations. Break now.\n      }\n    }\n  }\n\n  function forEachOrientedLink(links, nodeId, callback) {\n    var quitFast;\n    for (var i = 0; i < links.length; ++i) {\n      var link = links[i];\n      if (link.fromId === nodeId) {\n        quitFast = callback(nodes[link.toId], link);\n        if (quitFast) {\n          return true; // Client does not need more iterations. Break now.\n        }\n      }\n    }\n  }\n\n  // we will not fire anything until users of this library explicitly call `on()`\n  // method.\n  function noop() {}\n\n  // Enter, Exit modification allows bulk graph updates without firing events.\n  function enterModificationReal() {\n    suspendEvents += 1;\n  }\n\n  function exitModificationReal() {\n    suspendEvents -= 1;\n    if (suspendEvents === 0 && changes.length > 0) {\n      graphPart.fire('changed', changes);\n      changes.length = 0;\n    }\n  }\n\n  function createNodeIterator() {\n    // Object.keys iterator is 1.3x faster than `for in` loop.\n    // See `https://github.com/anvaka/ngraph.graph/tree/bench-for-in-vs-obj-keys`\n    // branch for perf test\n    return Object.keys ? objectKeysIterator : forInIterator;\n  }\n\n  function objectKeysIterator(callback) {\n    if (typeof callback !== 'function') {\n      return;\n    }\n\n    var keys = Object.keys(nodes);\n    for (var i = 0; i < keys.length; ++i) {\n      if (callback(nodes[keys[i]])) {\n        return true; // client doesn't want to proceed. Return.\n      }\n    }\n  }\n\n  function forInIterator(callback) {\n    if (typeof callback !== 'function') {\n      return;\n    }\n    var node;\n\n    for (node in nodes) {\n      if (callback(nodes[node])) {\n        return true; // client doesn't want to proceed. Return.\n      }\n    }\n  }\n}\n\n// need this for old browsers. Should this be a separate module?\nfunction indexOfElementInArray(element, array) {\n  if (!array) return -1;\n\n  if (array.indexOf) {\n    return array.indexOf(element);\n  }\n\n  var len = array.length,\n    i;\n\n  for (i = 0; i < len; i += 1) {\n    if (array[i] === element) {\n      return i;\n    }\n  }\n\n  return -1;\n}\n\n/**\n * Internal structure to represent node;\n */\nfunction Node(id, data) {\n  this.id = id;\n  this.links = null;\n  this.data = data;\n}\n\nfunction addLinkToNode(node, link) {\n  if (node.links) {\n    node.links.push(link);\n  } else {\n    node.links = [link];\n  }\n}\n\n/**\n * Internal structure to represent links;\n */\nfunction Link(fromId, toId, data, id) {\n  this.fromId = fromId;\n  this.toId = toId;\n  this.data = data;\n  this.id = id;\n}\n\nfunction hashCode(str) {\n  var hash = 0, i, chr, len;\n  if (str.length == 0) return hash;\n  for (i = 0, len = str.length; i < len; i++) {\n    chr   = str.charCodeAt(i);\n    hash  = ((hash << 5) - hash) + chr;\n    hash |= 0; // Convert to 32bit integer\n  }\n  return hash;\n}\n\nfunction makeLinkId(fromId, toId) {\n  return fromId.toString() + '👉 ' + toId.toString();\n}\n", "/**\n * Manages a simulation of physical forces acting on bodies and springs.\n */\nmodule.exports = physicsSimulator;\n\nfunction physicsSimulator(settings) {\n  var Spring = require('./lib/spring');\n  var expose = require('ngraph.expose');\n  var merge = require('ngraph.merge');\n  var eventify = require('ngraph.events');\n\n  settings = merge(settings, {\n      /**\n       * Ideal length for links (springs in physical model).\n       */\n      springLength: 30,\n\n      /**\n       * Hook's law coefficient. 1 - solid spring.\n       */\n      springCoeff: 0.0008,\n\n      /**\n       * <PERSON><PERSON><PERSON>'s law coefficient. It's used to repel nodes thus should be negative\n       * if you make it positive nodes start attract each other :).\n       */\n      gravity: -1.2,\n\n      /**\n       * Theta coefficient from Barnes Hut simulation. Ranged between (0, 1).\n       * The closer it's to 1 the more nodes algorithm will have to go through.\n       * Setting it to one makes Barnes Hut simulation no different from\n       * brute-force forces calculation (each node is considered).\n       */\n      theta: 0.8,\n\n      /**\n       * Drag force coefficient. Used to slow down system, thus should be less than 1.\n       * The closer it is to 0 the less tight system will be.\n       */\n      dragCoeff: 0.02,\n\n      /**\n       * Default time step (dt) for forces integration\n       */\n      timeStep : 20,\n  });\n\n  // We allow clients to override basic factory methods:\n  var createQuadTree = settings.createQuadTree || require('ngraph.quadtreebh');\n  var createBounds = settings.createBounds || require('./lib/bounds');\n  var createDragForce = settings.createDragForce || require('./lib/dragForce');\n  var createSpringForce = settings.createSpringForce || require('./lib/springForce');\n  var integrate = settings.integrator || require('./lib/eulerIntegrator');\n  var createBody = settings.createBody || require('./lib/createBody');\n\n  var bodies = [], // Bodies in this simulation.\n      springs = [], // Springs in this simulation.\n      quadTree =  createQuadTree(settings),\n      bounds = createBounds(bodies, settings),\n      springForce = createSpringForce(settings),\n      dragForce = createDragForce(settings);\n\n  var bboxNeedsUpdate = true;\n  var totalMovement = 0; // how much movement we made on last step\n\n  var publicApi = {\n    /**\n     * Array of bodies, registered with current simulator\n     *\n     * Note: To add new body, use addBody() method. This property is only\n     * exposed for testing/performance purposes.\n     */\n    bodies: bodies,\n\n    quadTree: quadTree,\n\n    /**\n     * Array of springs, registered with current simulator\n     *\n     * Note: To add new spring, use addSpring() method. This property is only\n     * exposed for testing/performance purposes.\n     */\n    springs: springs,\n\n    /**\n     * Returns settings with which current simulator was initialized\n     */\n    settings: settings,\n\n    /**\n     * Performs one step of force simulation.\n     *\n     * @returns {boolean} true if system is considered stable; False otherwise.\n     */\n    step: function () {\n      accumulateForces();\n\n      var movement = integrate(bodies, settings.timeStep);\n      bounds.update();\n\n      return movement;\n    },\n\n    /**\n     * Adds body to the system\n     *\n     * @param {ngraph.physics.primitives.Body} body physical body\n     *\n     * @returns {ngraph.physics.primitives.Body} added body\n     */\n    addBody: function (body) {\n      if (!body) {\n        throw new Error('Body is required');\n      }\n      bodies.push(body);\n\n      return body;\n    },\n\n    /**\n     * Adds body to the system at given position\n     *\n     * @param {Object} pos position of a body\n     *\n     * @returns {ngraph.physics.primitives.Body} added body\n     */\n    addBodyAt: function (pos) {\n      if (!pos) {\n        throw new Error('Body position is required');\n      }\n      var body = createBody(pos);\n      bodies.push(body);\n\n      return body;\n    },\n\n    /**\n     * Removes body from the system\n     *\n     * @param {ngraph.physics.primitives.Body} body to remove\n     *\n     * @returns {Boolean} true if body found and removed. falsy otherwise;\n     */\n    removeBody: function (body) {\n      if (!body) { return; }\n\n      var idx = bodies.indexOf(body);\n      if (idx < 0) { return; }\n\n      bodies.splice(idx, 1);\n      if (bodies.length === 0) {\n        bounds.reset();\n      }\n      return true;\n    },\n\n    /**\n     * Adds a spring to this simulation.\n     *\n     * @returns {Object} - a handle for a spring. If you want to later remove\n     * spring pass it to removeSpring() method.\n     */\n    addSpring: function (body1, body2, springLength, springWeight, springCoefficient) {\n      if (!body1 || !body2) {\n        throw new Error('Cannot add null spring to force simulator');\n      }\n\n      if (typeof springLength !== 'number') {\n        springLength = -1; // assume global configuration\n      }\n\n      var spring = new Spring(body1, body2, springLength, springCoefficient >= 0 ? springCoefficient : -1, springWeight);\n      springs.push(spring);\n\n      // TODO: could mark simulator as dirty.\n      return spring;\n    },\n\n    /**\n     * Returns amount of movement performed on last step() call\n     */\n    getTotalMovement: function () {\n      return totalMovement;\n    },\n\n    /**\n     * Removes spring from the system\n     *\n     * @param {Object} spring to remove. Spring is an object returned by addSpring\n     *\n     * @returns {Boolean} true if spring found and removed. falsy otherwise;\n     */\n    removeSpring: function (spring) {\n      if (!spring) { return; }\n      var idx = springs.indexOf(spring);\n      if (idx > -1) {\n        springs.splice(idx, 1);\n        return true;\n      }\n    },\n\n    getBestNewBodyPosition: function (neighbors) {\n      return bounds.getBestNewPosition(neighbors);\n    },\n\n    /**\n     * Returns bounding box which covers all bodies\n     */\n    getBBox: function () {\n      if (bboxNeedsUpdate) {\n        bounds.update();\n        bboxNeedsUpdate = false;\n      }\n      return bounds.box;\n    },\n\n    invalidateBBox: function () {\n      bboxNeedsUpdate = true;\n    },\n\n    gravity: function (value) {\n      if (value !== undefined) {\n        settings.gravity = value;\n        quadTree.options({gravity: value});\n        return this;\n      } else {\n        return settings.gravity;\n      }\n    },\n\n    theta: function (value) {\n      if (value !== undefined) {\n        settings.theta = value;\n        quadTree.options({theta: value});\n        return this;\n      } else {\n        return settings.theta;\n      }\n    }\n  };\n\n  // allow settings modification via public API:\n  expose(settings, publicApi);\n\n  eventify(publicApi);\n\n  return publicApi;\n\n  function accumulateForces() {\n    // Accumulate forces acting on bodies.\n    var body,\n        i = bodies.length;\n\n    if (i) {\n      // only add bodies if there the array is not empty:\n      quadTree.insertBodies(bodies); // performance: O(n * log n)\n      while (i--) {\n        body = bodies[i];\n        // If body is pinned there is no point updating its forces - it should\n        // never move:\n        if (!body.isPinned) {\n          body.force.reset();\n\n          quadTree.updateBodyForce(body);\n          dragForce.update(body);\n        }\n      }\n    }\n\n    i = springs.length;\n    while(i--) {\n      springForce.update(springs[i]);\n    }\n  }\n};\n", "/**\n * Performs forces integration, using given timestep. Uses Euler method to solve\n * differential equation (http://en.wikipedia.org/wiki/Euler_method ).\n *\n * @returns {Number} squared distance of total position updates.\n */\n\nmodule.exports = integrate;\n\nfunction integrate(bodies, timeStep) {\n  var dx = 0, tx = 0,\n      dy = 0, ty = 0,\n      i,\n      max = bodies.length;\n\n  if (max === 0) {\n    return 0;\n  }\n\n  for (i = 0; i < max; ++i) {\n    var body = bodies[i],\n        coeff = timeStep / body.mass;\n\n    body.velocity.x += coeff * body.force.x;\n    body.velocity.y += coeff * body.force.y;\n    var vx = body.velocity.x,\n        vy = body.velocity.y,\n        v = Math.sqrt(vx * vx + vy * vy);\n\n    if (v > 1) {\n      body.velocity.x = vx / v;\n      body.velocity.y = vy / v;\n    }\n\n    dx = timeStep * body.velocity.x;\n    dy = timeStep * body.velocity.y;\n\n    body.pos.x += dx;\n    body.pos.y += dy;\n\n    tx += Math.abs(dx); ty += Math.abs(dy);\n  }\n\n  return (tx * tx + ty * ty)/max;\n}\n", "module.exports = merge;\n\n/**\n * Augments `target` with properties in `options`. Does not override\n * target's properties if they are defined and matches expected type in \n * options\n *\n * @returns {Object} merged object\n */\nfunction merge(target, options) {\n  var key;\n  if (!target) { target = {}; }\n  if (options) {\n    for (key in options) {\n      if (options.hasOwnProperty(key)) {\n        var targetHasIt = target.hasOwnProperty(key),\n            optionsValueType = typeof options[key],\n            shouldReplace = !targetHasIt || (typeof target[key] !== optionsValueType);\n\n        if (shouldReplace) {\n          target[key] = options[key];\n        } else if (optionsValueType === 'object') {\n          // go deep, don't care about loops here, we are simple API!:\n          target[key] = merge(target[key], options[key]);\n        }\n      }\n    }\n  }\n\n  return target;\n}\n", "/**\n * Represents spring force, which updates forces acting on two bodies, conntected\n * by a spring.\n *\n * @param {Object} options for the spring force\n * @param {Number=} options.springCoeff spring force coefficient.\n * @param {Number=} options.springLength desired length of a spring at rest.\n */\nmodule.exports = function (options) {\n  var merge = require('ngraph.merge');\n  var random = require('ngraph.random').random(42);\n  var expose = require('ngraph.expose');\n\n  options = merge(options, {\n    springCoeff: 0.0002,\n    springLength: 80\n  });\n\n  var api = {\n    /**\n     * Upsates forces acting on a spring\n     */\n    update : function (spring) {\n      var body1 = spring.from,\n          body2 = spring.to,\n          length = spring.length < 0 ? options.springLength : spring.length,\n          dx = body2.pos.x - body1.pos.x,\n          dy = body2.pos.y - body1.pos.y,\n          r = Math.sqrt(dx * dx + dy * dy);\n\n      if (r === 0) {\n          dx = (random.nextDouble() - 0.5) / 50;\n          dy = (random.nextDouble() - 0.5) / 50;\n          r = Math.sqrt(dx * dx + dy * dy);\n      }\n\n      var d = r - length;\n      var coeff = ((!spring.coeff || spring.coeff < 0) ? options.springCoeff : spring.coeff) * d / r * spring.weight;\n\n      body1.force.x += coeff * dx;\n      body1.force.y += coeff * dy;\n\n      body2.force.x -= coeff * dx;\n      body2.force.y -= coeff * dy;\n    }\n  };\n\n  expose(options, api, ['springCoeff', 'springLength']);\n  return api;\n}\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-graph.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-graph.vue?vue&type=style&index=0&lang=scss&\"", "/**\n * Represents drag force, which reduces force value on each step by given\n * coefficient.\n *\n * @param {Object} options for the drag force\n * @param {Number=} options.dragCoeff drag force coefficient. 0.1 by default\n */\nmodule.exports = function (options) {\n  var merge = require('ngraph.merge'),\n      expose = require('ngraph.expose');\n\n  options = merge(options, {\n    dragCoeff: 0.02\n  });\n\n  var api = {\n    update : function (body) {\n      body.force.x -= options.dragCoeff * body.velocity.x;\n      body.force.y -= options.dragCoeff * body.velocity.y;\n    }\n  };\n\n  // let easy access to dragCoeff:\n  expose(options, api, ['dragCoeff']);\n\n  return api;\n};\n", "var createGraph = require('ngraph.graph');\n\nmodule.exports = factory(createGraph);\n\n// Allow other developers have their own createGraph\nmodule.exports.factory = factory;\n\nfunction factory(createGraph) {\n  return {\n    ladder: ladder,\n    complete: complete,\n    completeBipartite: completeBipartite,\n    balancedBinTree: balancedBinTree,\n    path: path,\n    circularLadder: circularLadder,\n    grid: grid,\n    grid3: grid3,\n    noLinks: noLinks,\n    wattsStrogatz: wattsStrogatz,\n    cliqueCircle: cliqueCircle\n  };\n\n\n  function ladder(n) {\n  /**\n  * Ladder graph is a graph in form of ladder\n  * @param {Number} n Represents number of steps in the ladder\n  */\n    if (!n || n < 0) {\n      throw new Error(\"Invalid number of nodes\");\n    }\n\n    var g = createGraph(),\n        i;\n\n    for (i = 0; i < n - 1; ++i) {\n      g.addLink(i, i + 1);\n      // first row\n      g.addLink(n + i, n + i + 1);\n      // second row\n      g.addLink(i, n + i);\n      // ladder's step\n    }\n\n    g.addLink(n - 1, 2 * n - 1);\n    // last step in the ladder;\n\n    return g;\n  }\n\n  function circularLadder(n) {\n  /**\n  * Circular ladder with n steps.\n  *\n  * @param {Number} n of steps in the ladder.\n  */\n      if (!n || n < 0) {\n          throw new Error(\"Invalid number of nodes\");\n      }\n\n      var g = ladder(n);\n\n      g.addLink(0, n - 1);\n      g.addLink(n, 2 * n - 1);\n      return g;\n  }\n\n  function complete(n) {\n  /**\n  * Complete graph Kn.\n  *\n  * @param {Number} n represents number of nodes in the complete graph.\n  */\n    if (!n || n < 1) {\n      throw new Error(\"At least two nodes are expected for complete graph\");\n    }\n\n    var g = createGraph(),\n        i,\n        j;\n\n    for (i = 0; i < n; ++i) {\n      for (j = i + 1; j < n; ++j) {\n        if (i !== j) {\n          g.addLink(i, j);\n        }\n      }\n    }\n\n    return g;\n  }\n\n  function completeBipartite (n, m) {\n  /**\n  * Complete bipartite graph K n,m. Each node in the\n  * first partition is connected to all nodes in the second partition.\n  *\n  * @param {Number} n represents number of nodes in the first graph partition\n  * @param {Number} m represents number of nodes in the second graph partition\n  */\n    if (!n || !m || n < 0 || m < 0) {\n      throw new Error(\"Graph dimensions are invalid. Number of nodes in each partition should be greater than 0\");\n    }\n\n    var g = createGraph(),\n        i, j;\n\n    for (i = 0; i < n; ++i) {\n      for (j = n; j < n + m; ++j) {\n        g.addLink(i, j);\n      }\n    }\n\n    return g;\n  }\n\n  function path(n) {\n  /**\n  * Path graph with n steps.\n  *\n  * @param {Number} n number of nodes in the path\n  */\n    if (!n || n < 0) {\n      throw new Error(\"Invalid number of nodes\");\n    }\n\n    var g = createGraph(),\n        i;\n\n    g.addNode(0);\n\n    for (i = 1; i < n; ++i) {\n      g.addLink(i - 1, i);\n    }\n\n    return g;\n  }\n\n\n  function grid(n, m) {\n  /**\n  * Grid graph with n rows and m columns.\n  *\n  * @param {Number} n of rows in the graph.\n  * @param {Number} m of columns in the graph.\n  */\n    if (n < 1 || m < 1) {\n      throw new Error(\"Invalid number of nodes in grid graph\");\n    }\n    var g = createGraph(),\n        i,\n        j;\n    if (n === 1 && m === 1) {\n      g.addNode(0);\n      return g;\n    }\n\n    for (i = 0; i < n; ++i) {\n      for (j = 0; j < m; ++j) {\n        var node = i + j * n;\n        if (i > 0) { g.addLink(node, i - 1 + j * n); }\n        if (j > 0) { g.addLink(node, i + (j - 1) * n); }\n      }\n    }\n\n    return g;\n  }\n\n  function grid3(n, m, z) {\n  /**\n  * 3D grid with n rows and m columns and z levels.\n  *\n  * @param {Number} n of rows in the graph.\n  * @param {Number} m of columns in the graph.\n  * @param {Number} z of levels in the graph.\n  */\n    if (n < 1 || m < 1 || z < 1) {\n      throw new Error(\"Invalid number of nodes in grid3 graph\");\n    }\n    var g = createGraph(),\n        i, j, k;\n\n    if (n === 1 && m === 1 && z === 1) {\n      g.addNode(0);\n      return g;\n    }\n\n    for (k = 0; k < z; ++k) {\n      for (i = 0; i < n; ++i) {\n        for (j = 0; j < m; ++j) {\n          var level = k * n * m;\n          var node = i + j * n + level;\n          if (i > 0) { g.addLink(node, i - 1 + j * n + level); }\n          if (j > 0) { g.addLink(node, i + (j - 1) * n + level); }\n          if (k > 0) { g.addLink(node, i + j * n + (k - 1) * n * m ); }\n        }\n      }\n    }\n\n    return g;\n  }\n\n  function balancedBinTree(n) {\n  /**\n  * Balanced binary tree with n levels.\n  *\n  * @param {Number} n of levels in the binary tree\n  */\n    if (n < 0) {\n      throw new Error(\"Invalid number of nodes in balanced tree\");\n    }\n    var g = createGraph(),\n        count = Math.pow(2, n),\n        level;\n\n    if (n === 0) {\n      g.addNode(1);\n    }\n\n    for (level = 1; level < count; ++level) {\n      var root = level,\n        left = root * 2,\n        right = root * 2 + 1;\n\n      g.addLink(root, left);\n      g.addLink(root, right);\n    }\n\n    return g;\n  }\n\n  function noLinks(n) {\n  /**\n  * Graph with no links\n  *\n  * @param {Number} n of nodes in the graph\n  */\n    if (n < 0) {\n      throw new Error(\"Number of nodes should be >= 0\");\n    }\n\n    var g = createGraph(), i;\n    for (i = 0; i < n; ++i) {\n      g.addNode(i);\n    }\n\n    return g;\n  }\n\n  function cliqueCircle(cliqueCount, cliqueSize) {\n  /**\n  * A circular graph with cliques instead of individual nodes\n  *\n  * @param {Number} cliqueCount number of cliques inside circle\n  * @param {Number} cliqueSize number of nodes inside each clique\n  */\n\n    if (cliqueCount < 1) throw new Error('Invalid number of cliqueCount in cliqueCircle');\n    if (cliqueSize < 1) throw new Error('Invalid number of cliqueSize in cliqueCircle');\n\n    var graph = createGraph();\n\n    for (var i = 0; i < cliqueCount; ++i) {\n      appendClique(cliqueSize, i * cliqueSize)\n\n      if (i > 0) {\n        graph.addLink(i * cliqueSize, i * cliqueSize - 1);\n      }\n    }\n    graph.addLink(0, graph.getNodesCount() - 1);\n\n    return graph;\n\n    function appendClique(size, from) {\n      for (var i = 0; i < size; ++i) {\n        graph.addNode(i + from)\n      }\n\n      for (var i = 0; i < size; ++i) {\n        for (var j = i + 1; j < size; ++j) {\n          graph.addLink(i + from, j + from)\n        }\n      }\n    }\n  }\n\n  function wattsStrogatz(n, k, p, seed) {\n  /**\n  * Watts-Strogatz small-world graph.\n  *\n  * @param {Number} n The number of nodes\n  * @param {Number} k Each node is connected to k nearest neighbors in ring topology\n  * @param {Number} p The probability of rewiring each edge\n\n  * @see https://github.com/networkx/networkx/blob/master/networkx/generators/random_graphs.py\n  */\n    if (k >= n) throw new Error('Choose smaller `k`. It cannot be larger than number of nodes `n`');\n\n\n    var random = require('ngraph.random').random(seed || 42);\n\n    var g = createGraph(), i, to;\n    for (i = 0; i < n; ++i) {\n      g.addNode(i);\n    }\n\n    // connect each node to k/2 neighbors\n    var neighborsSize = Math.floor(k/2 + 1);\n    for (var j = 1; j < neighborsSize; ++j) {\n      for (i = 0; i < n; ++i) {\n        to = (j + i) % n;\n        g.addLink(i, to);\n      }\n    }\n\n    // rewire edges from each node\n    // loop over all nodes in order (label) and neighbors in order (distance)\n    // no self loops or multiple edges allowed\n    for (j = 1; j < neighborsSize; ++j) {\n      for (i = 0; i < n; ++i) {\n        if (random.nextDouble() < p) {\n          var from = i;\n          to = (j + i) % n;\n\n          var newTo = random.next(n);\n          var needsRewire = (newTo === from || g.hasLink(from, newTo));\n          if (needsRewire && g.getLinks(from).length === n - 1) {\n            // we cannot rewire this node, it has too many links.\n            continue;\n          }\n          // Enforce no self-loops or multiple edges\n          while (needsRewire) {\n            newTo = random.next(n);\n            needsRewire = (newTo === from || g.hasLink(from, newTo));\n          }\n          var link = g.hasLink(from, to);\n          g.removeLink(link);\n          g.addLink(from, newTo);\n        }\n      }\n    }\n\n    return g;\n  }\n}\n", "module.exports = function(subject) {\n  validateSubject(subject);\n\n  var eventsStorage = createEventsStorage(subject);\n  subject.on = eventsStorage.on;\n  subject.off = eventsStorage.off;\n  subject.fire = eventsStorage.fire;\n  return subject;\n};\n\nfunction createEventsStorage(subject) {\n  // Store all event listeners to this hash. Key is event name, value is array\n  // of callback records.\n  //\n  // A callback record consists of callback function and its optional context:\n  // { 'eventName' => [{callback: function, ctx: object}] }\n  var registeredEvents = Object.create(null);\n\n  return {\n    on: function (eventName, callback, ctx) {\n      if (typeof callback !== 'function') {\n        throw new Error('callback is expected to be a function');\n      }\n      var handlers = registeredEvents[eventName];\n      if (!handlers) {\n        handlers = registeredEvents[eventName] = [];\n      }\n      handlers.push({callback: callback, ctx: ctx});\n\n      return subject;\n    },\n\n    off: function (eventName, callback) {\n      var wantToRemoveAll = (typeof eventName === 'undefined');\n      if (wantToRemoveAll) {\n        // Killing old events storage should be enough in this case:\n        registeredEvents = Object.create(null);\n        return subject;\n      }\n\n      if (registeredEvents[eventName]) {\n        var deleteAllCallbacksForEvent = (typeof callback !== 'function');\n        if (deleteAllCallbacksForEvent) {\n          delete registeredEvents[eventName];\n        } else {\n          var callbacks = registeredEvents[eventName];\n          for (var i = 0; i < callbacks.length; ++i) {\n            if (callbacks[i].callback === callback) {\n              callbacks.splice(i, 1);\n            }\n          }\n        }\n      }\n\n      return subject;\n    },\n\n    fire: function (eventName) {\n      var callbacks = registeredEvents[eventName];\n      if (!callbacks) {\n        return subject;\n      }\n\n      var fireArguments;\n      if (arguments.length > 1) {\n        fireArguments = Array.prototype.splice.call(arguments, 1);\n      }\n      for(var i = 0; i < callbacks.length; ++i) {\n        var callbackInfo = callbacks[i];\n        callbackInfo.callback.apply(callbackInfo.ctx, fireArguments);\n      }\n\n      return subject;\n    }\n  };\n}\n\nfunction validateSubject(subject) {\n  if (!subject) {\n    throw new Error('Eventify cannot use falsy object as events subject');\n  }\n  var reservedWords = ['on', 'fire', 'off'];\n  for (var i = 0; i < reservedWords.length; ++i) {\n    if (subject.hasOwnProperty(reservedWords[i])) {\n      throw new Error(\"Subject cannot be eventified, since it already has property '\" + reservedWords[i] + \"'\");\n    }\n  }\n}\n", "module.exports = InsertStack;\n\n/**\n * Our implmentation of QuadTree is non-recursive to avoid GC hit\n * This data structure represent stack of elements\n * which we are trying to insert into quad tree.\n */\nfunction InsertStack () {\n    this.stack = [];\n    this.popIdx = 0;\n}\n\nInsertStack.prototype = {\n    isEmpty: function() {\n        return this.popIdx === 0;\n    },\n    push: function (node, body) {\n        var item = this.stack[this.popIdx];\n        if (!item) {\n            // we are trying to avoid memory pressue: create new element\n            // only when absolutely necessary\n            this.stack[this.popIdx] = new InsertStackElement(node, body);\n        } else {\n            item.node = node;\n            item.body = body;\n        }\n        ++this.popIdx;\n    },\n    pop: function () {\n        if (this.popIdx > 0) {\n            return this.stack[--this.popIdx];\n        }\n    },\n    reset: function () {\n        this.popIdx = 0;\n    }\n};\n\nfunction InsertStackElement(node, body) {\n    this.node = node; // QuadTree node\n    this.body = body; // physical body which needs to be inserted to node\n}\n", "/**\n * This is Barnes <PERSON> simulation algorithm for 2d case. Implementation\n * is highly optimized (avoids recusion and gc pressure)\n *\n * http://www.cs.princeton.edu/courses/archive/fall03/cs126/assignments/barnes-hut.html\n */\n\nmodule.exports = function(options) {\n  options = options || {};\n  options.gravity = typeof options.gravity === 'number' ? options.gravity : -1;\n  options.theta = typeof options.theta === 'number' ? options.theta : 0.8;\n\n  // we require deterministic randomness here\n  var random = require('ngraph.random').random(1984),\n    Node = require('./node'),\n    InsertStack = require('./insertStack'),\n    isSamePosition = require('./isSamePosition');\n\n  var gravity = options.gravity,\n    updateQueue = [],\n    insertStack = new InsertStack(),\n    theta = options.theta,\n\n    nodesCache = [],\n    currentInCache = 0,\n    root = newNode();\n\n  return {\n    insertBodies: insertBodies,\n    /**\n     * Gets root node if its present\n     */\n    getRoot: function() {\n      return root;\n    },\n    updateBodyForce: update,\n    options: function(newOptions) {\n      if (newOptions) {\n        if (typeof newOptions.gravity === 'number') {\n          gravity = newOptions.gravity;\n        }\n        if (typeof newOptions.theta === 'number') {\n          theta = newOptions.theta;\n        }\n\n        return this;\n      }\n\n      return {\n        gravity: gravity,\n        theta: theta\n      };\n    }\n  };\n\n  function newNode() {\n    // To avoid pressure on GC we reuse nodes.\n    var node = nodesCache[currentInCache];\n    if (node) {\n      node.quad0 = null;\n      node.quad1 = null;\n      node.quad2 = null;\n      node.quad3 = null;\n      node.body = null;\n      node.mass = node.massX = node.massY = 0;\n      node.left = node.right = node.top = node.bottom = 0;\n    } else {\n      node = new Node();\n      nodesCache[currentInCache] = node;\n    }\n\n    ++currentInCache;\n    return node;\n  }\n\n  function update(sourceBody) {\n    var queue = updateQueue,\n      v,\n      dx,\n      dy,\n      r, fx = 0,\n      fy = 0,\n      queueLength = 1,\n      shiftIdx = 0,\n      pushIdx = 1;\n\n    queue[0] = root;\n\n    while (queueLength) {\n      var node = queue[shiftIdx],\n        body = node.body;\n\n      queueLength -= 1;\n      shiftIdx += 1;\n      var differentBody = (body !== sourceBody);\n      if (body && differentBody) {\n        // If the current node is a leaf node (and it is not source body),\n        // calculate the force exerted by the current node on body, and add this\n        // amount to body's net force.\n        dx = body.pos.x - sourceBody.pos.x;\n        dy = body.pos.y - sourceBody.pos.y;\n        r = Math.sqrt(dx * dx + dy * dy);\n\n        if (r === 0) {\n          // Poor man's protection against zero distance.\n          dx = (random.nextDouble() - 0.5) / 50;\n          dy = (random.nextDouble() - 0.5) / 50;\n          r = Math.sqrt(dx * dx + dy * dy);\n        }\n\n        // This is standard gravition force calculation but we divide\n        // by r^3 to save two operations when normalizing force vector.\n        v = gravity * body.mass * sourceBody.mass / (r * r * r);\n        fx += v * dx;\n        fy += v * dy;\n      } else if (differentBody) {\n        // Otherwise, calculate the ratio s / r,  where s is the width of the region\n        // represented by the internal node, and r is the distance between the body\n        // and the node's center-of-mass\n        dx = node.massX / node.mass - sourceBody.pos.x;\n        dy = node.massY / node.mass - sourceBody.pos.y;\n        r = Math.sqrt(dx * dx + dy * dy);\n\n        if (r === 0) {\n          // Sorry about code duplucation. I don't want to create many functions\n          // right away. Just want to see performance first.\n          dx = (random.nextDouble() - 0.5) / 50;\n          dy = (random.nextDouble() - 0.5) / 50;\n          r = Math.sqrt(dx * dx + dy * dy);\n        }\n        // If s / r < θ, treat this internal node as a single body, and calculate the\n        // force it exerts on sourceBody, and add this amount to sourceBody's net force.\n        if ((node.right - node.left) / r < theta) {\n          // in the if statement above we consider node's width only\n          // because the region was squarified during tree creation.\n          // Thus there is no difference between using width or height.\n          v = gravity * node.mass * sourceBody.mass / (r * r * r);\n          fx += v * dx;\n          fy += v * dy;\n        } else {\n          // Otherwise, run the procedure recursively on each of the current node's children.\n\n          // I intentionally unfolded this loop, to save several CPU cycles.\n          if (node.quad0) {\n            queue[pushIdx] = node.quad0;\n            queueLength += 1;\n            pushIdx += 1;\n          }\n          if (node.quad1) {\n            queue[pushIdx] = node.quad1;\n            queueLength += 1;\n            pushIdx += 1;\n          }\n          if (node.quad2) {\n            queue[pushIdx] = node.quad2;\n            queueLength += 1;\n            pushIdx += 1;\n          }\n          if (node.quad3) {\n            queue[pushIdx] = node.quad3;\n            queueLength += 1;\n            pushIdx += 1;\n          }\n        }\n      }\n    }\n\n    sourceBody.force.x += fx;\n    sourceBody.force.y += fy;\n  }\n\n  function insertBodies(bodies) {\n    var x1 = Number.MAX_VALUE,\n      y1 = Number.MAX_VALUE,\n      x2 = Number.MIN_VALUE,\n      y2 = Number.MIN_VALUE,\n      i,\n      max = bodies.length;\n\n    // To reduce quad tree depth we are looking for exact bounding box of all particles.\n    i = max;\n    while (i--) {\n      var x = bodies[i].pos.x;\n      var y = bodies[i].pos.y;\n      if (x < x1) {\n        x1 = x;\n      }\n      if (x > x2) {\n        x2 = x;\n      }\n      if (y < y1) {\n        y1 = y;\n      }\n      if (y > y2) {\n        y2 = y;\n      }\n    }\n\n    // Squarify the bounds.\n    var dx = x2 - x1,\n      dy = y2 - y1;\n    if (dx > dy) {\n      y2 = y1 + dx;\n    } else {\n      x2 = x1 + dy;\n    }\n\n    currentInCache = 0;\n    root = newNode();\n    root.left = x1;\n    root.right = x2;\n    root.top = y1;\n    root.bottom = y2;\n\n    i = max - 1;\n    if (i >= 0) {\n      root.body = bodies[i];\n    }\n    while (i--) {\n      insert(bodies[i], root);\n    }\n  }\n\n  function insert(newBody) {\n    insertStack.reset();\n    insertStack.push(root, newBody);\n\n    while (!insertStack.isEmpty()) {\n      var stackItem = insertStack.pop(),\n        node = stackItem.node,\n        body = stackItem.body;\n\n      if (!node.body) {\n        // This is internal node. Update the total mass of the node and center-of-mass.\n        var x = body.pos.x;\n        var y = body.pos.y;\n        node.mass = node.mass + body.mass;\n        node.massX = node.massX + body.mass * x;\n        node.massY = node.massY + body.mass * y;\n\n        // Recursively insert the body in the appropriate quadrant.\n        // But first find the appropriate quadrant.\n        var quadIdx = 0, // Assume we are in the 0's quad.\n          left = node.left,\n          right = (node.right + left) / 2,\n          top = node.top,\n          bottom = (node.bottom + top) / 2;\n\n        if (x > right) { // somewhere in the eastern part.\n          quadIdx = quadIdx + 1;\n          left = right;\n          right = node.right;\n        }\n        if (y > bottom) { // and in south.\n          quadIdx = quadIdx + 2;\n          top = bottom;\n          bottom = node.bottom;\n        }\n\n        var child = getChild(node, quadIdx);\n        if (!child) {\n          // The node is internal but this quadrant is not taken. Add\n          // subnode to it.\n          child = newNode();\n          child.left = left;\n          child.top = top;\n          child.right = right;\n          child.bottom = bottom;\n          child.body = body;\n\n          setChild(node, quadIdx, child);\n        } else {\n          // continue searching in this quadrant.\n          insertStack.push(child, body);\n        }\n      } else {\n        // We are trying to add to the leaf node.\n        // We have to convert current leaf into internal node\n        // and continue adding two nodes.\n        var oldBody = node.body;\n        node.body = null; // internal nodes do not cary bodies\n\n        if (isSamePosition(oldBody.pos, body.pos)) {\n          // Prevent infinite subdivision by bumping one node\n          // anywhere in this quadrant\n          var retriesCount = 3;\n          do {\n            var offset = random.nextDouble();\n            var dx = (node.right - node.left) * offset;\n            var dy = (node.bottom - node.top) * offset;\n\n            oldBody.pos.x = node.left + dx;\n            oldBody.pos.y = node.top + dy;\n            retriesCount -= 1;\n            // Make sure we don't bump it out of the box. If we do, next iteration should fix it\n          } while (retriesCount > 0 && isSamePosition(oldBody.pos, body.pos));\n\n          if (retriesCount === 0 && isSamePosition(oldBody.pos, body.pos)) {\n            // This is very bad, we ran out of precision.\n            // if we do not return from the method we'll get into\n            // infinite loop here. So we sacrifice correctness of layout, and keep the app running\n            // Next layout iteration should get larger bounding box in the first step and fix this\n            return;\n          }\n        }\n        // Next iteration should subdivide node further.\n        insertStack.push(node, oldBody);\n        insertStack.push(node, body);\n      }\n    }\n  }\n};\n\nfunction getChild(node, idx) {\n  if (idx === 0) return node.quad0;\n  if (idx === 1) return node.quad1;\n  if (idx === 2) return node.quad2;\n  if (idx === 3) return node.quad3;\n  return null;\n}\n\nfunction setChild(node, idx, child) {\n  if (idx === 0) node.quad0 = child;\n  else if (idx === 1) node.quad1 = child;\n  else if (idx === 2) node.quad2 = child;\n  else if (idx === 3) node.quad3 = child;\n}\n", "module.exports = function isSamePosition(point1, point2) {\n    var dx = Math.abs(point1.x - point2.x);\n    var dy = Math.abs(point1.y - point2.y);\n\n    return (dx < 1e-8 && dy < 1e-8);\n};\n", "module.exports = random;\n\n// TODO: Deprecate?\nmodule.exports.random = random,\nmodule.exports.randomIterator = randomIterator\n\n/**\n * Creates seeded PRNG with two methods:\n *   next() and nextDouble()\n */\nfunction random(inputSeed) {\n  var seed = typeof inputSeed === 'number' ? inputSeed : (+new Date());\n  return new Generator(seed)\n}\n\nfunction Generator(seed) {\n  this.seed = seed;\n}\n\n/**\n  * Generates random integer number in the range from 0 (inclusive) to maxValue (exclusive)\n  *\n  * @param maxValue Number REQUIRED. Omitting this number will result in NaN values from PRNG.\n  */\nGenerator.prototype.next = next;\n\n/**\n  * Generates random double number in the range from 0 (inclusive) to 1 (exclusive)\n  * This function is the same as Math.random() (except that it could be seeded)\n  */\nGenerator.prototype.nextDouble = nextDouble;\n\n/**\n * Returns a random real number uniformly in [0, 1)\n */\nGenerator.prototype.uniform = nextDouble;\n\nGenerator.prototype.gaussian = gaussian;\n\nfunction gaussian() {\n  // use the polar form of the Box-Muller transform\n  // based on https://introcs.cs.princeton.edu/java/23recursion/StdRandom.java\n  var r, x, y;\n  do {\n    x = this.nextDouble() * 2 - 1;\n    y = this.nextDouble() * 2 - 1;\n    r = x * x + y * y;\n  } while (r >= 1 || r === 0);\n\n  return x * Math.sqrt(-2 * Math.log(r)/r);\n}\n\nfunction nextDouble() {\n  var seed = this.seed;\n  // Robert Jenkins' 32 bit integer hash function.\n  seed = ((seed + 0x7ed55d16) + (seed << 12)) & 0xffffffff;\n  seed = ((seed ^ 0xc761c23c) ^ (seed >>> 19)) & 0xffffffff;\n  seed = ((seed + 0x165667b1) + (seed << 5)) & 0xffffffff;\n  seed = ((seed + 0xd3a2646c) ^ (seed << 9)) & 0xffffffff;\n  seed = ((seed + 0xfd7046c5) + (seed << 3)) & 0xffffffff;\n  seed = ((seed ^ 0xb55a4f09) ^ (seed >>> 16)) & 0xffffffff;\n  this.seed = seed;\n  return (seed & 0xfffffff) / 0x10000000;\n}\n\nfunction next(maxValue) {\n  return Math.floor(this.nextDouble() * maxValue);\n}\n\n/*\n * Creates iterator over array, which returns items of array in random order\n * Time complexity is guaranteed to be O(n);\n */\nfunction randomIterator(array, customRandom) {\n  var localRandom = customRandom || random();\n  if (typeof localRandom.next !== 'function') {\n    throw new Error('customRandom does not match expected API: next() function is missing');\n  }\n\n  return {\n    forEach: forEach,\n\n    /**\n     * Shuffles array randomly, in place.\n     */\n    shuffle: shuffle\n  };\n\n  function shuffle() {\n    var i, j, t;\n    for (i = array.length - 1; i > 0; --i) {\n      j = localRandom.next(i + 1); // i inclusive\n      t = array[j];\n      array[j] = array[i];\n      array[i] = t;\n    }\n\n    return array;\n  }\n\n  function forEach(callback) {\n    var i, j, t;\n    for (i = array.length - 1; i > 0; --i) {\n      j = localRandom.next(i + 1); // i inclusive\n      t = array[j];\n      array[j] = array[i];\n      array[i] = t;\n\n      callback(t);\n    }\n\n    if (array.length) {\n      callback(array[0]);\n    }\n  }\n}", "module.exports = {\n  random: random,\n  randomIterator: randomIterator\n};\n\n/**\n * Creates seeded PRNG with two methods:\n *   next() and nextDouble()\n */\nfunction random(inputSeed) {\n  var seed = typeof inputSeed === 'number' ? inputSeed : (+ new Date());\n  var randomFunc = function() {\n      // <PERSON>' 32 bit integer hash function.\n      seed = ((seed + 0x7ed55d16) + (seed << 12))  & 0xffffffff;\n      seed = ((seed ^ 0xc761c23c) ^ (seed >>> 19)) & 0xffffffff;\n      seed = ((seed + 0x165667b1) + (seed << 5))   & 0xffffffff;\n      seed = ((seed + 0xd3a2646c) ^ (seed << 9))   & 0xffffffff;\n      seed = ((seed + 0xfd7046c5) + (seed << 3))   & 0xffffffff;\n      seed = ((seed ^ 0xb55a4f09) ^ (seed >>> 16)) & 0xffffffff;\n      return (seed & 0xfffffff) / 0x10000000;\n  };\n\n  return {\n      /**\n       * Generates random integer number in the range from 0 (inclusive) to maxValue (exclusive)\n       *\n       * @param maxValue Number REQUIRED. Ommitting this number will result in NaN values from PRNG.\n       */\n      next : function (maxValue) {\n          return Math.floor(randomFunc() * maxValue);\n      },\n\n      /**\n       * Generates random double number in the range from 0 (inclusive) to 1 (exclusive)\n       * This function is the same as Math.random() (except that it could be seeded)\n       */\n      nextDouble : function () {\n          return randomFunc();\n      }\n  };\n}\n\n/*\n * Creates iterator over array, which returns items of array in random order\n * Time complexity is guaranteed to be O(n);\n */\nfunction randomIterator(array, customRandom) {\n    var localRandom = customRandom || random();\n    if (typeof localRandom.next !== 'function') {\n      throw new Error('customRandom does not match expected API: next() function is missing');\n    }\n\n    return {\n        forEach : function (callback) {\n            var i, j, t;\n            for (i = array.length - 1; i > 0; --i) {\n                j = localRandom.next(i + 1); // i inclusive\n                t = array[j];\n                array[j] = array[i];\n                array[i] = t;\n\n                callback(t);\n            }\n\n            if (array.length) {\n                callback(array[0]);\n            }\n        },\n\n        /**\n         * Shuffles array randomly, in place.\n         */\n        shuffle : function () {\n            var i, j, t;\n            for (i = array.length - 1; i > 0; --i) {\n                j = localRandom.next(i + 1); // i inclusive\n                t = array[j];\n                array[j] = array[i];\n                array[i] = t;\n            }\n\n            return array;\n        }\n    };\n}\n", "var NODE_WIDTH = 20;\nvar PIXI = require('pixi.js');\nmodule.exports = function (conainter, graph, layout) {\n  var width = window.innerWidth,\n      height = window.innerHeight;\n\n  var stage = new PIXI.Stage(0xFFFFFF, true);\n  var renderer = PIXI.autoDetectRenderer(width, height, null, false, true);\n  renderer.view.style.display = \"block\";\n  conainter.appendChild(renderer.view);\n\n  var graphics = new PIXI.Graphics();\n  graphics.position.x = width/2;\n  graphics.position.y = height/2;\n\n  graphics.scale.x = 1;\n  graphics.scale.y = 1;\n  stage.addChild(graphics);\n\n  // Store node and link positions into arrays for quicker access within\n  // animation loop:\n  var nodePositions = [],\n      getNodeByIndex = {},\n      linkPositions = [];\n\n  graph.forEachNode(function(node) {\n    nodePositions.push(layout.getNodePosition(node.id));\n    getNodeByIndex[nodePositions.length - 1] = node;\n  });\n\n  graph.forEachLink(function(link) {\n    linkPositions.push(layout.getLinkPosition(link.id));\n  });\n\n  return {\n    renderFrame: function () {\n      layout.step();\n      drawGraph(graphics, nodePositions, linkPositions);\n      renderer.render(stage);\n    },\n    domContainer: renderer.view,\n    graphGraphics: graphics,\n    stage: stage,\n\n    getNodeAt: function (x, y) {\n      var half = NODE_WIDTH/2;\n      // currently it's a linear search, but nothing stops us from refactoring\n      // this into spatial lookup data structure in future:\n      for (var i = 0; i < nodePositions.length; ++i) {\n        var pos = nodePositions[i];\n        var insideNode = pos.x - half < x && x < pos.x + half &&\n                         pos.y - half < y && y < pos.y + half;\n\n        if (insideNode) {\n          return getNodeByIndex[i];\n        }\n      }\n    }\n  };\n}\n\nfunction drawGraph(graphics, nodePositions, linkPositions) {\n  // No magic at all: Iterate over positions array and render nodes/links\n  graphics.clear();\n  graphics.beginFill(0xffffff);\n  var i, x, y, x1, y1;\n\n  graphics.lineStyle(1, 0xffffff, 1);\n  for(i = 0; i < linkPositions.length; ++i) {\n    var link = linkPositions[i];\n    graphics.moveTo(link.from.x, link.from.y);\n    graphics.lineTo(link.to.x, link.to.y);\n  }\n\n  graphics.lineStyle(0);\n  var half = NODE_WIDTH/2;\n  for (i = 0; i < nodePositions.length; ++i) {\n    x = nodePositions[i].x - half;\n    y = nodePositions[i].y - half;\n    graphics.drawRect(x, y, NODE_WIDTH, NODE_WIDTH);\n  }\n}", "module.exports = exposeProperties;\n\n/**\n * Augments `target` object with getter/setter functions, which modify settings\n *\n * @example\n *  var target = {};\n *  exposeProperties({ age: 42}, target);\n *  target.age(); // returns 42\n *  target.age(24); // make age 24;\n *\n *  var filteredTarget = {};\n *  exposeProperties({ age: 42, name: '<PERSON>'}, filteredTarget, ['name']);\n *  filteredTarget.name(); // returns 'John'\n *  filteredTarget.age === undefined; // true\n */\nfunction exposeProperties(settings, target, filter) {\n  var needsFilter = Object.prototype.toString.call(filter) === '[object Array]';\n  if (needsFilter) {\n    for (var i = 0; i < filter.length; ++i) {\n      augment(settings, target, filter[i]);\n    }\n  } else {\n    for (var key in settings) {\n      augment(settings, target, key);\n    }\n  }\n}\n\nfunction augment(source, target, key) {\n  if (source.hasOwnProperty(key)) {\n    if (typeof target[key] === 'function') {\n      // this accessor is already defined. Ignore it\n      return;\n    }\n    target[key] = function (value) {\n      if (value !== undefined) {\n        source[key] = value;\n        return target;\n      }\n      return source[key];\n    }\n  }\n}\n", "module.exports = function(subject) {\n  validateSubject(subject);\n\n  var eventsStorage = createEventsStorage(subject);\n  subject.on = eventsStorage.on;\n  subject.off = eventsStorage.off;\n  subject.fire = eventsStorage.fire;\n  return subject;\n};\n\nfunction createEventsStorage(subject) {\n  // Store all event listeners to this hash. Key is event name, value is array\n  // of callback records.\n  //\n  // A callback record consists of callback function and its optional context:\n  // { 'eventName' => [{callback: function, ctx: object}] }\n  var registeredEvents = Object.create(null);\n\n  return {\n    on: function (eventName, callback, ctx) {\n      if (typeof callback !== 'function') {\n        throw new Error('callback is expected to be a function');\n      }\n      var handlers = registeredEvents[eventName];\n      if (!handlers) {\n        handlers = registeredEvents[eventName] = [];\n      }\n      handlers.push({callback: callback, ctx: ctx});\n\n      return subject;\n    },\n\n    off: function (eventName, callback) {\n      var wantToRemoveAll = (typeof eventName === 'undefined');\n      if (wantToRemoveAll) {\n        // Killing old events storage should be enough in this case:\n        registeredEvents = Object.create(null);\n        return subject;\n      }\n\n      if (registeredEvents[eventName]) {\n        var deleteAllCallbacksForEvent = (typeof callback !== 'function');\n        if (deleteAllCallbacksForEvent) {\n          delete registeredEvents[eventName];\n        } else {\n          var callbacks = registeredEvents[eventName];\n          for (var i = 0; i < callbacks.length; ++i) {\n            if (callbacks[i].callback === callback) {\n              callbacks.splice(i, 1);\n            }\n          }\n        }\n      }\n\n      return subject;\n    },\n\n    fire: function (eventName) {\n      var callbacks = registeredEvents[eventName];\n      if (!callbacks) {\n        return subject;\n      }\n\n      var fireArguments;\n      if (arguments.length > 1) {\n        fireArguments = Array.prototype.splice.call(arguments, 1);\n      }\n      for(var i = 0; i < callbacks.length; ++i) {\n        var callbackInfo = callbacks[i];\n        callbackInfo.callback.apply(callbackInfo.ctx, fireArguments);\n      }\n\n      return subject;\n    }\n  };\n}\n\nfunction validateSubject(subject) {\n  if (!subject) {\n    throw new Error('Eventify cannot use falsy object as events subject');\n  }\n  var reservedWords = ['on', 'fire', 'off'];\n  for (var i = 0; i < reservedWords.length; ++i) {\n    if (subject.hasOwnProperty(reservedWords[i])) {\n      throw new Error(\"Subject cannot be eventified, since it already has property '\" + reservedWords[i] + \"'\");\n    }\n  }\n}\n", "module.exports = {\n  Body: Body,\n  Vector2d: Vector2d,\n  Body3d: Body3d,\n  Vector3d: Vector3d\n};\n\nfunction Body(x, y) {\n  this.pos = new Vector2d(x, y);\n  this.prevPos = new Vector2d(x, y);\n  this.force = new Vector2d();\n  this.velocity = new Vector2d();\n  this.mass = 1;\n}\n\nBody.prototype.setPosition = function (x, y) {\n  this.prevPos.x = this.pos.x = x;\n  this.prevPos.y = this.pos.y = y;\n};\n\nfunction Vector2d(x, y) {\n  if (x && typeof x !== 'number') {\n    // could be another vector\n    this.x = typeof x.x === 'number' ? x.x : 0;\n    this.y = typeof x.y === 'number' ? x.y : 0;\n  } else {\n    this.x = typeof x === 'number' ? x : 0;\n    this.y = typeof y === 'number' ? y : 0;\n  }\n}\n\nVector2d.prototype.reset = function () {\n  this.x = this.y = 0;\n};\n\nfunction Body3d(x, y, z) {\n  this.pos = new Vector3d(x, y, z);\n  this.prevPos = new Vector3d(x, y, z);\n  this.force = new Vector3d();\n  this.velocity = new Vector3d();\n  this.mass = 1;\n}\n\nBody3d.prototype.setPosition = function (x, y, z) {\n  this.prevPos.x = this.pos.x = x;\n  this.prevPos.y = this.pos.y = y;\n  this.prevPos.z = this.pos.z = z;\n};\n\nfunction Vector3d(x, y, z) {\n  if (x && typeof x !== 'number') {\n    // could be another vector\n    this.x = typeof x.x === 'number' ? x.x : 0;\n    this.y = typeof x.y === 'number' ? x.y : 0;\n    this.z = typeof x.z === 'number' ? x.z : 0;\n  } else {\n    this.x = typeof x === 'number' ? x : 0;\n    this.y = typeof y === 'number' ? y : 0;\n    this.z = typeof z === 'number' ? z : 0;\n  }\n};\n\nVector3d.prototype.reset = function () {\n  this.x = this.y = this.z = 0;\n};\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"cape-bg\"},[_c('div',{ref:\"here\",staticClass:\"graph\"})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n    section.cape-bg\n        div.graph(ref=\"here\")\n</template>\n\n<script>\nimport { cape } from '@core/cape'\nimport { getGeometry, getGeometryForPostPayload } from 'dna/dnaToolsWD.js'\nimport { tylkoCapeComponents } from '@cape-ui'\nimport GraphRenderer from './index.js';\n\nexport default {\n    components: {\n    },\n\n    mounted() {\n        GraphRenderer.main(this.$refs.here);\n    },\n\n    methods: {\n    },\n\n    data() {\n        return {\n            meshes: [],\n        }\n    },\n}\n</script>\n<style lang=\"scss\">\n.graph{\n    width: 100vw;\n    height: 100vh;\n}\n.inline {\n    display: inline-block;\n}\n.cape-bg {\n    position: fixed;\n    height: 102vh;\n    overflow: scroll;\n    width: 100%;\n    min-height: 100vh;\n    overflow: scroll;\n\n    background-color: rgb(19, 19, 19);\n    $color: rgba(220, 220, 220, 0.04);\n    $size: 40px;\n\n    background-image: -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px);\n    background-size: $size * 4 $size * 4, $size * 4 $size * 4, $size $size,\n        $size $size, $size/2 $size/2, $size/2 $size/2;\n    background-position-x: -6px;\n}\n</style>", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-graph.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./cape-graph.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./cape-graph.vue?vue&type=template&id=b85b4c26&lang=pug&\"\nimport script from \"./cape-graph.vue?vue&type=script&lang=js&\"\nexport * from \"./cape-graph.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cape-graph.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "module.exports = function (bodies, settings) {\n  var random = require('ngraph.random').random(42);\n  var boundingBox =  { x1: 0, y1: 0, x2: 0, y2: 0 };\n\n  return {\n    box: boundingBox,\n\n    update: updateBoundingBox,\n\n    reset : function () {\n      boundingBox.x1 = boundingBox.y1 = 0;\n      boundingBox.x2 = boundingBox.y2 = 0;\n    },\n\n    getBestNewPosition: function (neighbors) {\n      var graphRect = boundingBox;\n\n      var baseX = 0, baseY = 0;\n\n      if (neighbors.length) {\n        for (var i = 0; i < neighbors.length; ++i) {\n          baseX += neighbors[i].pos.x;\n          baseY += neighbors[i].pos.y;\n        }\n\n        baseX /= neighbors.length;\n        baseY /= neighbors.length;\n      } else {\n        baseX = (graphRect.x1 + graphRect.x2) / 2;\n        baseY = (graphRect.y1 + graphRect.y2) / 2;\n      }\n\n      var springLength = settings.springLength;\n      return {\n        x: baseX + random.next(springLength) - springLength / 2,\n        y: baseY + random.next(springLength) - springLength / 2\n      };\n    }\n  };\n\n  function updateBoundingBox() {\n    var i = bodies.length;\n    if (i === 0) { return; } // don't have to wory here.\n\n    var x1 = Number.MAX_VALUE,\n        y1 = Number.MAX_VALUE,\n        x2 = Number.MIN_VALUE,\n        y2 = Number.MIN_VALUE;\n\n    while(i--) {\n      // this is O(n), could it be done faster with quadtree?\n      // how about pinned nodes?\n      var body = bodies[i];\n      if (body.isPinned) {\n        body.pos.x = body.prevPos.x;\n        body.pos.y = body.prevPos.y;\n      } else {\n        body.prevPos.x = body.pos.x;\n        body.prevPos.y = body.pos.y;\n      }\n      if (body.pos.x < x1) {\n        x1 = body.pos.x;\n      }\n      if (body.pos.x > x2) {\n        x2 = body.pos.x;\n      }\n      if (body.pos.y < y1) {\n        y1 = body.pos.y;\n      }\n      if (body.pos.y > y2) {\n        y2 = body.pos.y;\n      }\n    }\n\n    boundingBox.x1 = x1;\n    boundingBox.x2 = x2;\n    boundingBox.y1 = y1;\n    boundingBox.y2 = y2;\n  }\n}\n", "/**\n * Internal data structure to represent 2D QuadTree node\n */\nmodule.exports = function Node() {\n  // body stored inside this node. In quad tree only leaf nodes (by construction)\n  // contain boides:\n  this.body = null;\n\n  // Child nodes are stored in quads. Each quad is presented by number:\n  // 0 | 1\n  // -----\n  // 2 | 3\n  this.quad0 = null;\n  this.quad1 = null;\n  this.quad2 = null;\n  this.quad3 = null;\n\n  // Total mass of current node\n  this.mass = 0;\n\n  // Center of mass coordinates\n  this.massX = 0;\n  this.massY = 0;\n\n  // bounding box coordinates\n  this.left = 0;\n  this.top = 0;\n  this.bottom = 0;\n  this.right = 0;\n};\n", "var physics = require('ngraph.physics.primitives');\n\nmodule.exports = function(pos) {\n  return new physics.Body(pos);\n}\n", "/**\n * This module unifies handling of mouse whee event accross different browsers\n *\n * See https://developer.mozilla.org/en-US/docs/Web/Reference/Events/wheel?redirectlocale=en-US&redirectslug=DOM%2FMozilla_event_reference%2Fwheel\n * for more details\n */\nmodule.exports = addWheelListener;\n\nvar prefix = \"\", _addEventListener, onwheel, support;\n\n// detect event model\nif ( window.addEventListener ) {\n    _addEventListener = \"addEventListener\";\n} else {\n    _addEventListener = \"attachEvent\";\n    prefix = \"on\";\n}\n\n// detect available wheel event\nsupport = \"onwheel\" in document.createElement(\"div\") ? \"wheel\" : // Modern browsers support \"wheel\"\n          document.onmousewheel !== undefined ? \"mousewheel\" : // Webkit and IE support at least \"mousewheel\"\n          \"DOMMouseScroll\"; // let's assume that remaining browsers are older Firefox\n\nfunction addWheelListener( elem, callback, useCapture ) {\n    _addWheelListener( elem, support, callback, useCapture );\n\n    // handle MozMousePixelScroll in older Firefox\n    if( support == \"DOMMouseScroll\" ) {\n        _addWheelListener( elem, \"MozMousePixelScroll\", callback, useCapture );\n    }\n};\n\nfunction _addWheelListener( elem, eventName, callback, useCapture ) {\n    elem[ _addEventListener ]( prefix + eventName, support == \"wheel\" ? callback : function( originalEvent ) {\n        !originalEvent && ( originalEvent = window.event );\n\n        // create a normalized event object\n        var event = {\n            // keep a ref to the original event object\n            originalEvent: originalEvent,\n            target: originalEvent.target || originalEvent.srcElement,\n            type: \"wheel\",\n            deltaMode: originalEvent.type == \"MozMousePixelScroll\" ? 0 : 1,\n            deltaX: 0,\n            delatZ: 0,\n            preventDefault: function() {\n                originalEvent.preventDefault ?\n                    originalEvent.preventDefault() :\n                    originalEvent.returnValue = false;\n            }\n        };\n\n        // calculate deltaY (and deltaX) according to the event\n        if ( support == \"mousewheel\" ) {\n            event.deltaY = - 1/40 * originalEvent.wheelDelta;\n            // Webkit also support wheelDeltaX\n            originalEvent.wheelDeltaX && ( event.deltaX = - 1/40 * originalEvent.wheelDeltaX );\n        } else {\n            event.deltaY = originalEvent.detail;\n        }\n\n        // it's time to fire the callback\n        return callback( event );\n\n    }, useCapture || false );\n}"], "sourceRoot": ""}