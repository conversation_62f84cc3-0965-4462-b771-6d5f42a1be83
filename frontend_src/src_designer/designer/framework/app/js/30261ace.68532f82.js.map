{"version": 3, "sources": ["webpack:///../app/@cape-ui/TylkoMiniature.vue?ae86", "webpack:///../app/@cape-ui/TylkoMiniature.vue", "webpack:///../app/@cape-ui/TylkoMiniature.vue?fd3c", "webpack:///../app/@cape-ui/TylkoMiniature.vue?74e7", "webpack:////Users/<USER>/tylko/cstm/frontend_src/node_modules/core-js/modules/es6.regexp.search.js", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-page/collection-page.vue?fc59", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-page/collection-page.vue?b272", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-page/collection-page.vue", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-page/collection-page.vue?f356", "webpack:///../app/@tylko/cape-entrypoints/summary/cape-collection-page/collection-page.vue?27b2", "webpack:///../app/@cape-ui/TylkoMiniature.vue?44bc"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "ref", "staticRenderFns", "TylkoMiniaturevue_type_script_lang_js_", "props", "id", "String", "Number", "geo", "Object", "Array", "bgColor", "update", "methods", "load", "_this", "geo<PERSON><PERSON><PERSON>", "cape", "api", "componentSet", "type", "queryForMiniature", "pipe", "console", "log", "build", "fetched", "_this2", "services", "miniatures", "add", "data", "base64", "colorSet", "then", "_ref", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "result", "image", "wrap", "_callee$", "_context", "prev", "next", "$refs", "mini", "hasChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Image", "src", "painterState", "append<PERSON><PERSON><PERSON>", "stop", "_x", "apply", "arguments", "created", "watch", "mounted", "_cape_ui_TylkoMiniaturevue_type_script_lang_js_", "component", "componentNormalizer", "TylkoMiniature", "__webpack_exports__", "__webpack_require__", "defined", "SEARCH", "$search", "search", "regexp", "O", "fn", "undefined", "call", "RegExp", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_page_vue_vue_type_style_index_0_id_3aee603f_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_collection_page_vue_vue_type_style_index_0_id_3aee603f_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default", "n", "_unused_webpack_default_export", "choosenCollection", "_l", "collection", "key", "attrs", "dark", "inverted-light", "inline", "color", "to", "_v", "_s", "name", "staticStyle", "font-size", "padding-left", "margin-bottom", "description", "_e", "padding-top", "counters", "label", "disable", "size", "floating", "tags", "readonly", "model", "value", "callback", "$$v", "$set", "expression", "height_group", "height", "components", "length", "comp", "$route", "params", "bg-color", "thumbnails_data", "collection_pagevue_type_script_lang_js_", "collections", "componentsByHeight", "selectedTab", "t-bar", "palette_bar", "t-mini", "from", "selectedCollection", "choosenCollectionId", "loadComponentsList", "loadAllCollections", "application", "viewport", "fixed", "_loadAllCollections", "allCollections", "palette", "fetch", "sent", "_loadComponentsList", "_callee2", "objects", "_callee2$", "_context2", "thumbs", "values", "reduce", "_ref2", "rest", "objectWithoutProperties_default", "push", "objectSpread_default", "setActiveCollection", "settings", "set", "activeCollection", "onResize", "rendererHeight", "cape_collection_page_collection_pagevue_type_script_lang_js_", "collection_page", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoMiniature_vue_vue_type_style_index_0_id_360dcb88_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__", "_framework_node_modules_mini_css_extract_plugin_dist_loader_js_ref_7_oneOf_2_0_framework_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_2_1_framework_node_modules_vue_loader_lib_loaders_stylePostLoader_js_framework_node_modules_postcss_loader_src_index_js_ref_7_oneOf_2_2_framework_node_modules_sass_loader_lib_loader_js_ref_7_oneOf_2_3_framework_node_modules_vue_loader_lib_index_js_vue_loader_options_TylkoMiniature_vue_vue_type_style_index_0_id_360dcb88_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default"], "mappings": "wGAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,aAAwB,CAAAF,EAAA,OAAYG,IAAA,OAAAD,YAAA,YAC9I,IAAAE,EAAA,6GCMA,IAAAC,EAAA,CACAC,MAAA,CACAC,GAAA,CAAAC,OAAAC,QACAC,IAAA,CAAAF,OAAAG,OAAAC,OACAC,QAAA,CAAAL,QACAM,OAAA,CAAAN,SAGAO,QAAA,CACAC,KADA,SAAAA,EACAT,GAAA,IAAAU,EAAApB,KACA,IAAAqB,EAAAC,EAAA,KAAAC,IAAAV,IAAAW,aAAA,CACAC,KAAA,qBACAf,KAEAgB,kBAAA,OAEAL,EAAAM,KACA,SAAAd,GACAe,QAAAC,IAAAhB,GACAO,EAAAU,MAAAjB,EAAAH,EAAA,QAEA,qBAIAoB,MAjBA,SAAAA,EAiBAjB,EAAAH,EAAAqB,GAAA,IAAAC,EAAAhC,KACAsB,EAAA,KAAAW,SAAAC,WACAC,IAAA,CACAC,KAAAvB,EACAH,KACAqB,UACAM,OAAA,KACAC,SAAA,aACAtB,QAAAhB,KAAAgB,UAEAuB,KATA,eAAAC,EAAAC,IAAQC,EAAAC,EAAAC,KASR,SAAAC,EAAAC,GAAA,IAAAC,EAAA,OAAAL,EAAAC,EAAAK,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OACA,MAAApB,EAAAqB,MAAAC,MAAAtB,EAAAqB,MAAAC,KAAAC,gBAAA,CACAvB,EAAAqB,MAAAC,KAAAE,YAAAxB,EAAAqB,MAAAC,KAAAG,YACAV,EAAA,IAAAW,MACAX,EAAAY,IAAAb,EAAAc,aACA5B,EAAAqB,MAAAC,KAAAO,YAAAd,GALA,wBAAAG,EAAAY,UAAAjB,MATA,gBAAAkB,GAAA,OAAAvB,EAAAwB,MAAAhE,KAAAiE,YAAA,MAmBAC,QA7CA,SAAAA,MA+CAC,MAAA,CACAtD,IADA,SAAAA,IAEAb,KAAA8B,MAAA9B,KAAAa,IAAAb,KAAAU,GAAA,QAIA0D,QArDA,SAAAA,IAsDA,GAAApE,KAAAa,IAAA,CACAb,KAAA8B,MAAA9B,KAAAa,IAAAb,KAAAU,GAAA,MACA,OAEA,GAAAV,KAAAU,GAAA,CACAV,KAAAmB,KAAAnB,KAAAU,OClE4N,IAAA2D,EAAA,kCCQ5N,IAAAC,EAAgBxD,OAAAyD,EAAA,KAAAzD,CACduD,EACAvE,EACAS,EACF,MACA,KACA,WACA,MAIe,IAAAiE,EAAAC,EAAA,KAAAH,kCClBfI,EAAQ,OAARA,CAAuB,oBAAAC,EAAAC,EAAAC,GAEvB,gBAAAC,EAAAC,GACA,aACA,IAAAC,EAAAL,EAAA3E,MACA,IAAAiF,EAAAF,GAAAG,oBAAAH,EAAAH,GACA,OAAAK,IAAAC,UAAAD,EAAAE,KAAAJ,EAAAC,GAAA,IAAAI,OAAAL,GAAAH,GAAAjE,OAAAqE,KACGH,sFCRH,IAAAQ,EAAAX,EAAA,YAAAY,EAAAZ,EAAAa,EAAAF,GAA0mB,IAAAG,EAAAF,EAAG,4CCA7mB,IAAAxF,EAAA,WAA0B,IAAAC,EAAAC,KAAa,IAAAC,EAAAF,EAAAG,eAA0B,IAAAC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,yBAAoC,CAAAF,EAAA,eAAAJ,EAAA0F,kBAAAtF,EAAA,MAAAJ,EAAA2F,GAAA3F,EAAA,qBAAA4F,GAAmG,OAAAxF,EAAA,OAAiByF,IAAAD,EAAAjF,GAAAL,YAAA,kCAAAwF,MAAA,CAAuEC,KAAA,OAAAC,iBAAA,iBAAAC,OAAA,SAAAC,MAAA,YAAqF,CAAA9F,EAAA,eAAoBE,YAAA,kBAAAwF,MAAA,CAAqCK,GAAA,eAAAP,EAAA,KAAyC,CAAAxF,EAAA,gBAAqBE,YAAA,WAAsB,CAAAN,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAU,OAAAV,EAAA,YAAAxF,EAAA,KAAmEE,YAAA,oBAAAiG,YAAA,CAA6CL,MAAA,QAAAM,YAAA,OAAAC,eAAA,OAAAC,gBAAA,QAAgF,CAAA1G,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAe,gBAAA3G,EAAA4G,OAAAxG,EAAA,wBAAAA,EAAA,OAA2FE,YAAA,iBAAAiG,YAAA,CAA0CM,cAAA,QAAqB,CAAAzG,EAAA,OAAYE,YAAA,OAAkB,CAAAsF,EAAAkB,SAAA,aAAA1G,EAAA,SAAiDE,YAAA,uBAAAwF,MAAA,CAA0CiB,MAAA,OAAAC,QAAA,UAAAd,MAAA,UAAAe,KAAA,OAAkE,CAAA7G,EAAA,UAAe0F,MAAA,CAAOoB,SAAA,WAAAhB,MAAA,WAAwC,CAAAlG,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAkB,SAAA,qBAAA9G,EAAA4G,KAAAhB,EAAAkB,SAAA,iBAAA1G,EAAA,SAAqHE,YAAA,iBAAAwF,MAAA,CAAoCiB,MAAA,MAAAC,QAAA,UAAAd,MAAA,UAAAe,KAAA,OAAiE,CAAA7G,EAAA,UAAe0F,MAAA,CAAOoB,SAAA,WAAAhB,MAAA,WAAwC,CAAAlG,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAkB,SAAA,yBAAA9G,EAAA4G,KAAAhB,EAAAkB,SAAA,oBAAA1G,EAAA,SAA4HE,YAAA,iBAAAwF,MAAA,CAAoCiB,MAAA,QAAAC,QAAA,UAAAd,MAAA,UAAAe,KAAA,OAAmE,CAAA7G,EAAA,UAAe0F,MAAA,CAAOoB,SAAA,WAAAhB,MAAA,YAAyC,CAAAlG,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAkB,SAAA,4BAAA9G,EAAA4G,KAAAhB,EAAAkB,SAAA,mBAAA1G,EAAA,SAA8HE,YAAA,iBAAAwF,MAAA,CAAoCiB,MAAA,QAAAC,QAAA,UAAAd,MAAA,UAAAe,KAAA,OAAmE,CAAA7G,EAAA,UAAe0F,MAAA,CAAOoB,SAAA,WAAAhB,MAAA,YAAyC,CAAAlG,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAkB,SAAA,2BAAA9G,EAAA4G,KAAAhB,EAAAkB,SAAA,QAAA1G,EAAA,SAAkHE,YAAA,iBAAAwF,MAAA,CAAoCiB,MAAA,OAAAC,QAAA,UAAAd,MAAA,cAAAe,KAAA,OAAsE,CAAA7G,EAAA,UAAe0F,MAAA,CAAOoB,SAAA,WAAAhB,MAAA,gBAA6C,CAAAlG,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAkB,SAAA,gBAAA9G,EAAA4G,KAAAhB,EAAAkB,SAAA,aAAA1G,EAAA,SAA4GE,YAAA,iBAAAwF,MAAA,CAAoCiB,MAAA,YAAAC,QAAA,UAAAd,MAAA,iBAAAe,KAAA,OAA8E,CAAA7G,EAAA,UAAe0F,MAAA,CAAOoB,SAAA,WAAAhB,MAAA,kBAA+C,CAAAlG,EAAAoG,GAAApG,EAAAqG,GAAAT,EAAAkB,SAAA,qBAAA9G,EAAA4G,MAAA,GAAAhB,EAAAuB,KAAA,OAAA/G,EAAA,OAAAA,EAAA,OAAmHE,YAAA,2BAAqCsF,EAAAuB,KAAA,OAAA/G,EAAA,iBAA+C0F,MAAA,CAAOsB,SAAA,WAAAJ,QAAA,WAA0CK,MAAA,CAAQC,MAAA1B,EAAA,KAAA2B,SAAA,SAAAC,GAAiDxH,EAAAyH,KAAA7B,EAAA,OAAA4B,IAAkCE,WAAA,qBAA+B1H,EAAA4G,MAAA,GAAA5G,EAAA4G,OAAAxG,EAAA,OAAoCE,YAAA,oBAA6B,KAAM,GAAAF,EAAA,OAAAA,EAAA,OAAyBE,YAAA,yBAAAwF,MAAA,CAA4CC,KAAA,OAAAC,iBAAA,iBAAAC,OAAA,SAAAC,MAAA,WAAoF,CAAA9F,EAAA,gBAAqBE,YAAA,UAAAiG,YAAA,CAAmCL,MAAA,UAAiB,CAAAlG,EAAAoG,GAAA,gBAAAhG,EAAA,OAAmCE,YAAA,mBAA6BF,EAAA,OAAYE,YAAA,oCAA+CN,EAAA2F,GAAA3F,EAAA,4BAAA2H,GAAwD,OAAAvH,EAAA,MAAAA,EAAA,OAA0BE,YAAA,oBAA+B,CAAAF,EAAA,gBAAqBE,YAAA,UAAAiG,YAAA,CAAmCL,MAAA,UAAiB,CAAAlG,EAAAoG,GAAA,MAAApG,EAAAqG,GAAAsB,EAAAC,QAAA,MAAA5H,EAAAqG,GAAAsB,EAAAE,WAAAC,WAAA9H,EAAA2F,GAAAgC,EAAA,oBAAAI,GAA2I,OAAA3H,EAAA,MAAAA,EAAA,eAAkC0F,MAAA,CAAOK,GAAA,eAAAnG,EAAAgI,OAAAC,OAAA,uBAAAF,EAAA,WAAAA,EAAA,KAAwG,CAAA3H,EAAA,UAAeyF,IAAAkC,EAAApH,GAAAL,YAAA,SAAAwF,MAAA,CAAwCnF,GAAAoH,EAAApH,GAAAuH,WAAA,UAAApH,IAAAiH,EAAAI,oBAA8D,UAAU,OAAQ,kBACp7H,IAAA3H,EAAA,uQCyLA,IAAA4H,EAAA,CACA9B,KAAA,kBAEAjE,KAHA,SAAAA,IAIA,OACAgG,YAAA,GACA3C,kBAAA,KAEA4C,mBAAA,GAEAC,YAAA,MAIAV,WAAA,CACAW,QAAAC,EAAA,KACAC,SAAAjE,EAAA,MAGAL,MAAA,CACA4D,OADA,SAAAA,EACA7B,EAAAwC,GACA,IAAAhI,EAAAwF,EAAA8B,OAAAW,mBACA,GAAAjI,EAAA,CACAV,KAAAyF,kBAAA,KACAzF,KAAA4I,oBAAAlI,EACAV,KAAA6I,yBACA,CACA7I,KAAAyF,kBAAA,MACAzF,KAAA8I,uBAIArD,kBAbA,SAAAA,OAgBArB,QAnCA,SAAAA,IAoCA9C,EAAA,KAAAyH,YAAAC,SAAAC,MAAA,MACA,GAAAjJ,KAAA+H,OAAAC,OAAAW,mBAAA,CACA3I,KAAAyF,kBAAA,KACAzF,KAAA6I,yBACA,CACA7I,KAAA8I,uBAIA5H,QAAA,CACA4H,mBADA,eAAAI,EAAAzG,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAL,EAAA2G,EAAA,OAAAzG,EAAAC,EAAAK,KAAA,SAAAC,EAAAC,GAAA,gBAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAAAF,EAAAE,KAAA,SAIA9B,EAAA,KAAAC,IAAA6H,QAAAzD,aAAA0D,QAJA,OAAA7G,EAAAU,EAAAoG,KAGAH,EAHA3G,EAGAmD,WAaA3F,KAAAoI,YAAAe,EAhBA,wBAAAjG,EAAAY,UAAAjB,EAAA7C,SAAA,SAAA8I,IAAA,OAAAI,EAAAlF,MAAAhE,KAAAiE,WAAA,OAAA6E,EAAA,GAmBAD,mBAnBA,eAAAU,EAAA9G,IAAAC,EAAAC,EAAAC,KAAA,SAAA4G,IAAA,IAAAC,EAAA,OAAA/G,EAAAC,EAAAK,KAAA,SAAA0G,EAAAC,GAAA,gBAAAA,EAAAxG,KAAAwG,EAAAvG,MAAA,OAAAuG,EAAAvG,KAAA,SAoBA9B,EAAA,KAAAC,IAAA6H,QACAzD,WAAA3F,KAAA+H,OAAAC,OAAAW,oBACAnH,aACAoG,WAAAgC,OAAA/C,SAAAwC,QAvBA,OAoBAI,EApBAE,EAAAL,KA2BAtJ,KAAAqI,mBAAAvH,OAAA+I,OACAJ,EAAAnF,UAAAwF,OAAA,SAAAhH,EAAAiH,GAAA,IAAApC,EAAAoC,EAAApC,OAAAqC,EAAAC,IAAAF,EAAA,YACA,IAAAjH,EAAA6E,GACA7E,EAAA6E,GAAA,CACAA,SACAC,WAAA,IAEA9E,EAAA6E,GAAAC,WAAAsC,KAAAC,IAAA,CACAxC,UACAqC,IAEA,OAAAlH,GACA,KAvCA,wBAAA6G,EAAA7F,UAAA0F,EAAAxJ,SAAA,SAAA6I,IAAA,OAAAU,EAAAvF,MAAAhE,KAAAiE,WAAA,OAAA4E,EAAA,GA4CAuB,oBA5CA,SAAAA,EA4CA1J,GACAY,EAAA,KAAAyH,YAAAsB,SAAAC,IAAA,kBAAA5J,GACAV,KAAAuK,iBAAA7J,GAEA8J,SAhDA,SAAAA,EAgDAxD,GACAhH,KAAAyK,eAAAzD,EAAAW,OAAA,OCxR+O,IAAA+C,EAAA,kCCQ/O,IAAApG,EAAgBxD,OAAAyD,EAAA,KAAAzD,CACd4J,EACA5K,EACAS,EACF,MACA,KACA,WACA,MAIe,IAAAoK,EAAAlG,EAAA,WAAAH,6CCnBf,IAAAsG,EAAAlG,EAAA,YAAAmG,EAAAnG,EAAAa,EAAAqF,GAAmjB,IAAApF,EAAAqF,EAAG", "file": "js/30261ace.68532f82.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"container\"},[_c('div',{ref:\"mini\",staticClass:\"mini\"})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\n    div.container()\n        div.mini(ref=\"mini\")\n</template>\n<script>\nimport { cape } from '@core/cape'\n\nexport default {\n    props: {\n        id: [String, Number],\n        geo: [String, Object, Array],\n        bgColor: [String],\n        update: [String],\n    },\n\n    methods: {\n        load(id) {\n            let geoQuery = cape.api.geo.componentSet({\n                type: 'componentRelations',\n                id: id, // component id\n\n                queryForMiniature: true,\n            })\n            geoQuery.pipe(\n                geo => {\n                    console.log(geo)\n                    this.build(geo, id, false)\n                },\n                'paletteRelations'\n            )\n        },\n\n        build(geo, id, fetched) {\n            cape.services.miniatures\n                .add({\n                    data: geo,\n                    id,\n                    fetched,\n                    base64: true,\n                    colorSet: 'production',\n                    bgColor: this.bgColor,\n                })\n                .then(async result => {\n                    while (this.$refs.mini && this.$refs.mini.hasChildNodes())\n                        this.$refs.mini.removeChild(this.$refs.mini.firstChild)\n                    var image = new Image()\n                    image.src = result.painterState\n                    this.$refs.mini.appendChild(image)\n                })\n        },\n    },\n\n    created() {},\n\n    watch: {\n        geo() {\n            this.build(this.geo, this.id, true)\n        },\n    },\n\n    mounted() {\n        if (this.geo) {\n            this.build(this.geo, this.id, true)\n            return\n        }\n        if (this.id) {\n            this.load(this.id)\n        }\n    },\n}\n</script>\n<style lang=\"scss\" scoped>\n.mini {\n    width: 100px;\n    height: 100px;\n}\n</style>\n\n", "import mod from \"-!../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TylkoMiniature.vue?vue&type=template&id=360dcb88&scoped=true&lang=pug&\"\nimport script from \"./TylkoMiniature.vue?vue&type=script&lang=js&\"\nexport * from \"./TylkoMiniature.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TylkoMiniature.vue?vue&type=style&index=0&id=360dcb88&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"360dcb88\",\n  null\n  \n)\n\nexport default component.exports", "// @@search logic\nrequire('./_fix-re-wks')('search', 1, function (defined, SEARCH, $search) {\n  // ********* String.prototype.search(regexp)\n  return [function search(regexp) {\n    'use strict';\n    var O = defined(this);\n    var fn = regexp == undefined ? undefined : regexp[SEARCH];\n    return fn !== undefined ? fn.call(regexp, O) : new RegExp(regexp)[SEARCH](String(O));\n  }, $search];\n});\n", "import mod from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-page.vue?vue&type=style&index=0&id=3aee603f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../../../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../../../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../../../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-page.vue?vue&type=style&index=0&id=3aee603f&lang=scss&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"main\"},[_c('div',{staticClass:\"collection-details sc\"},[_c('keep-alive',[(!_vm.choosenCollection)?_c('div',_vm._l((_vm.collections),function(collection){return _c('div',{key:collection.id,staticClass:\"card-wrapper collection-summary\",attrs:{\"dark\":\"dark\",\"inverted-light\":\"inverted-light\",\"inline\":\"inline\",\"color\":\"grey-10\"}},[_c('router-link',{staticClass:\"collection-link\",attrs:{\"to\":(\"/collection/\" + (collection.id))}},[_c('q-card-title',{staticClass:\"q-py-sm\"},[_vm._v(_vm._s(collection.name)),(collection.description)?_c('p',{staticClass:\"text-weight-light\",staticStyle:{\"color\":\"white\",\"font-size\":\"13px\",\"padding-left\":\"15px\",\"margin-bottom\":\"0px\"}},[_vm._v(_vm._s(collection.description))]):_vm._e()]),_c('q-card-separator')],1),_c('div',{staticClass:\"card-container\",staticStyle:{\"padding-top\":\"5px\"}},[_c('div',{staticClass:\"row\"},[(collection.counters['component'])?_c('q-btn',{staticClass:\"button_counter_large\",attrs:{\"label\":\"comp\",\"disable\":\"disable\",\"color\":\"cyan-10\",\"size\":\"sm\"}},[_c('q-chip',{attrs:{\"floating\":\"floating\",\"color\":\"cyan-9\"}},[_vm._v(_vm._s(collection.counters[\"component\"]))])],1):_vm._e(),(collection.counters['component_set'])?_c('q-btn',{staticClass:\"button_counter\",attrs:{\"label\":\"set\",\"disable\":\"disable\",\"color\":\"cyan-10\",\"size\":\"sm\"}},[_c('q-chip',{attrs:{\"floating\":\"floating\",\"color\":\"cyan-9\"}},[_vm._v(_vm._s(collection.counters[\"component_set\"]))])],1):_vm._e(),(collection.counters['component_series'])?_c('q-btn',{staticClass:\"button_counter\",attrs:{\"label\":\"serie\",\"disable\":\"disable\",\"color\":\"brown-9\",\"size\":\"sm\"}},[_c('q-chip',{attrs:{\"floating\":\"floating\",\"color\":\"brown-7\"}},[_vm._v(_vm._s(collection.counters[\"component_series\"]))])],1):_vm._e(),(collection.counters['component_table'])?_c('q-btn',{staticClass:\"button_counter\",attrs:{\"label\":\"table\",\"disable\":\"disable\",\"color\":\"brown-9\",\"size\":\"sm\"}},[_c('q-chip',{attrs:{\"floating\":\"floating\",\"color\":\"brown-7\"}},[_vm._v(_vm._s(collection.counters[\"component_table\"]))])],1):_vm._e(),(collection.counters['mesh'])?_c('q-btn',{staticClass:\"button_counter\",attrs:{\"label\":\"mesh\",\"disable\":\"disable\",\"color\":\"blue-grey-8\",\"size\":\"sm\"}},[_c('q-chip',{attrs:{\"floating\":\"floating\",\"color\":\"blue-grey-6\"}},[_vm._v(_vm._s(collection.counters[\"mesh\"]))])],1):_vm._e(),(collection.counters['container'])?_c('q-btn',{staticClass:\"button_counter\",attrs:{\"label\":\"container\",\"disable\":\"disable\",\"color\":\"deep-orange-10\",\"size\":\"sm\"}},[_c('q-chip',{attrs:{\"floating\":\"floating\",\"color\":\"deep-orange-8\"}},[_vm._v(_vm._s(collection.counters[\"container\"]))])],1):_vm._e()],1),(collection.tags.length)?_c('div',[_c('div',{staticClass:\"card-separator q-mt-md\"}),(collection.tags.length)?_c('q-chips-input',{attrs:{\"readonly\":\"readonly\",\"disable\":\"disable\"},model:{value:(collection.tags),callback:function ($$v) {_vm.$set(collection, \"tags\", $$v)},expression:\"collection.tags\"}}):_vm._e()],1):_vm._e()]),_c('div',{staticClass:\"card-separator\"})],1)}),0):_c('div',[_c('div',{staticClass:\"card-component-summary\",attrs:{\"dark\":\"dark\",\"inverted-light\":\"inverted-light\",\"inline\":\"inline\",\"color\":\"grey-1\"}},[_c('q-card-title',{staticClass:\"q-py-sm\",staticStyle:{\"color\":\"white\"}},[_vm._v(\"Components\")]),_c('div',{staticClass:\"card-separator\"}),_c('div',{staticClass:\"card-container section-component\"},_vm._l((_vm.componentsByHeight),function(height_group){return _c('aa',[_c('div',{staticClass:\"column-component\"},[_c('q-card-title',{staticClass:\"q-py-sm\",staticStyle:{\"color\":\"white\"}},[_vm._v(\"H: \"+_vm._s(height_group.height)+\" | \"+_vm._s(height_group.components.length))]),_vm._l((height_group.components),function(comp){return _c('aa',[_c('router-link',{attrs:{\"to\":(\"/components/\" + (_vm.$route.params.selectedCollection) + \"/\" + (comp.parent) + \"/\" + (comp.id))}},[_c('t-mini',{key:comp.id,staticClass:\"inline\",attrs:{\"id\":comp.id,\"bg-color\":\"#aaaaaa\",\"geo\":comp.thumbnails_data}})],1)],1)})],2)])}),1)],1)])])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template lang=\"pug\">\nsection(class=\"main\")\n    div.collection-details.sc\n        keep-alive\n            div(v-if=\"!choosenCollection\")\n                div.card-wrapper.collection-summary(\n                    v-for=\"collection in collections\", \n                    :key=\"collection.id\",\n                    dark, inverted-light, inline, color=\"grey-10\" )\n                    \n                    router-link.collection-link(:to=\"`/collection/${collection.id}`\")\n                        q-card-title(class=\"q-py-sm\") {{ collection.name }}\n                            p( v-if=\"collection.description\", class=\"text-weight-light\"\n                                style=\"color: white; font-size: 13px; padding-left: 15px; margin-bottom: 0px \"\n                                ) {{ collection.description }}\n                        q-card-separator\n                    div.card-container(style=\"padding-top: 5px;\")\n                        .row\n                            q-btn.button_counter_large(label=\"comp\", disable, color=\"cyan-10\", size=\"sm\", v-if=\"collection.counters['component']\")\n                                q-chip(floating, color=\"cyan-9\") {{collection.counters[\"component\"]}}\n                            q-btn.button_counter(label=\"set\", disable, color=\"cyan-10\", size=\"sm\", v-if=\"collection.counters['component_set']\")\n                                q-chip(floating, color=\"cyan-9\") {{collection.counters[\"component_set\"]}}\n                            q-btn.button_counter(label=\"serie\", disable, color=\"brown-9\", size=\"sm\", v-if=\"collection.counters['component_series']\")\n                                q-chip(floating, color=\"brown-7\") {{collection.counters[\"component_series\"]}}\n                            q-btn.button_counter(label=\"table\", disable, color=\"brown-9\", size=\"sm\", v-if=\"collection.counters['component_table']\")\n                                q-chip(floating, color=\"brown-7\") {{collection.counters[\"component_table\"]}}\n                            q-btn.button_counter(label=\"mesh\", disable, color=\"blue-grey-8\", size=\"sm\", v-if=\"collection.counters['mesh']\")\n                                q-chip(floating, color=\"blue-grey-6\") {{collection.counters[\"mesh\"]}}\n                            q-btn.button_counter(label=\"container\", disable, color=\"deep-orange-10\", size=\"sm\", v-if=\"collection.counters['container']\")\n                                q-chip(floating, color=\"deep-orange-8\",) {{collection.counters[\"container\"]}}\n                        div(v-if=\"collection.tags.length\")\n                            div.card-separator(class=\"q-mt-md\")\n                            q-chips-input(v-if=\"collection.tags.length\", v-model=\"collection.tags\", readonly, disable)\n                    div.card-separator\n\n            div(v-else)\n                //- div.card-wrapper.component-summary(dark, inverted-light, inline, color=\"grey-10\")\n                //-     div.card-title\n                //-         | Components by height\n                //-     div.card-separator\n                //-     div.card-container\n                //-         q-tabs.tabs-panel-bar(v-model=\"selectedTab\",\n                //-                 indicator-color=\"transparent\",\n                //-                 active-color=\"white\")\n                //-             q-tab(name=\"0\", label=\"hide\", default)\n                //-             q-tab(v-for=\"height_group in componentsByHeight\", :name=\"height_group.height\",\n                //-                 :label=\"'H:' + height_group.height + '->' + height_group.components.length + 'c'\")\n                //-         q-tab-panels.tabs-panel-color(v-model=\"selectedTab\", animated,)\n                //-             q-tab-panel(name=\"0\", default)\n                //-             q-tab-panel(v-for=\"height_group in componentsByHeight\", :name=\"height_group.height\")\n                //-                 aa(v-for=\"comp in height_group.components\")\n                //-                     router-link(:to=\"`/components/${$route.params.selectedCollection}/${comp.parent}/${comp.id}`\")\n                //-                         t-mini.inline(:id=\"comp.id\",bg-color=\"#aaaaaa\", :geo=\"comp.thumbnails_data\", :key=\"comp.id\")\n\n                //- aa(v-for=\"height_group in componentsByHeight\")\n                //-     div.component-summary(dark, inverted-light, inline, color=\"grey-10\")\n                //-         q-card-title(class=\"q-py-sm\" style=\"color: white;\") Height {{ height_group.height }}\n                //-         div.card-separator\n                //-         div.card-container\n                //-             aa(v-for=\"comp in height_group.components\")\n                //-                 router-link(:to=\"`/components/${$route.params.selectedCollection}/${comp.parent}/${comp.id}`\")\n                //-                     t-mini.inline(:id=\"comp.id\",bg-color=\"#aaaaaa\", :geo=\"comp.thumbnails_data\", :key=\"comp.id\")                        \n                div.card-component-summary(dark, inverted-light, inline, color=\"grey-1\")\n                    q-card-title(class=\"q-py-sm\" style=\"color: white;\") Components\n                    div.card-separator\n                    div.card-container.section-component\n                        aa(v-for=\"height_group in componentsByHeight\")\n                            div.column-component\n                                q-card-title(class=\"q-py-sm\" style=\"color: white;\") H: {{ height_group.height }} | {{ height_group.components.length }}\n                                aa(v-for=\"comp in height_group.components\")\n                                    router-link(:to=\"`/components/${$route.params.selectedCollection}/${comp.parent}/${comp.id}`\")\n                                        t-mini.inline(:id=\"comp.id\",bg-color=\"#aaaaaa\", :geo=\"comp.thumbnails_data\", :key=\"comp.id\")                        \n</template>\n\n<style lang=\"scss\" scoped>\n.tabs-panel-bar {\n    background: rgb(29, 29, 29);\n    color: #ccc;\n}\n\n.tabs-panel-color {\n    background: rgb(19, 19, 19);\n}\n\n.card-wrapper {\n    display: inline-block;\n    max-width: 300px;\n\n    .card-spearator {\n    }\n\n    .card-container {\n    }\n}\n\n.collection-summary {\n    /*max-width: 935px;*/\n    margin: 10px;\n}\n\n.card-component-summary {\n    display: inline-block;\n    max-width: 1800px;\n    max-height: 935px;\n    margin: 10px;\n}\n.section-component {\n    display: flex;\n}\n.column-component {\n    display: inline-block;\n    max-width: 100px;\n    max-height: 935px;\n    margin: 10px;\n}\n\n.collection-details,\n.cape-bg {\n    background-color: rgb(19, 19, 19);\n    $color: rgba(220, 220, 220, 0.04);\n    $size: 40px;\n\n    background-image: -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px);\n    background-size: $size * 4 $size * 4, $size * 4 $size * 4, $size $size,\n        $size $size, $size/2 $size/2, $size/2 $size/2;\n    background-position-x: -6px;\n}\n\n.inline {\n    display: inline-block;\n}\n\n.sc {\n    overflow: auto;\n    position: relative;\n    display: block;\n    height: 100vh;\n\n    &::-webkit-scrollbar {\n        display: none;\n    }\n\n    &::-webkit-scrollbar-track {\n        width: 10px;\n        //  background-color: rgb(63, 63, 63); /* or add it to the track */\n    }\n\n    &::-webkit-scrollbar-button {\n        background-color: white;\n    }\n\n    &:-webkit-scrollbar-thumb {\n        background: black;\n    }\n}\n\n.collection-link {\n    text-decoration: none;\n    color: white;\n}\n\n.button_counter_large {\n    padding-right: 28px;\n    margin-right: 7px;\n    margin-top: 8px;\n}\n\n.button_counter {\n    padding-right: 18px;\n    margin-right: 7px;\n    margin-top: 8px;\n}\n</style>\n\n<script>\nimport _ from 'lodash'\nimport { cape } from '@core/cape'\n\nimport TylkoBar from '@tylko/cape-palette/palette-bar'\nimport TylkoWidgetMiniature from '@cape-ui/TylkoMiniature'\n\nexport default {\n    name: 'PageCollections',\n\n    data() {\n        return {\n            collections: [],\n            choosenCollection: null,\n            // componentsForCollection: [],\n            componentsByHeight: {},\n            // componentsSets: [],\n            selectedTab: '0',\n        }\n    },\n\n    components: {\n        't-bar': TylkoBar,\n        't-mini': TylkoWidgetMiniature,\n    },\n\n    watch: {\n        $route(to, from) {\n            let id = to.params.selectedCollection\n            if (id) {\n                this.choosenCollection = true\n                this.choosenCollectionId = id\n                this.loadComponentsList()\n            } else {\n                this.choosenCollection = false\n                this.loadAllCollections()\n            }\n        },\n\n        choosenCollection() {},\n    },\n\n    mounted() {\n        cape.application.viewport.fixed(true)\n        if (this.$route.params.selectedCollection) {\n            this.choosenCollection = true\n            this.loadComponentsList()\n        } else {\n            this.loadAllCollections()\n        }\n    },\n\n    methods: {\n        async loadAllCollections() {\n            var {\n                collection: allCollections,\n            } = await cape.api.palette.collection().fetch()\n\n            /*\n              componentsOfCollections = allCollections.map((collection) => {\n                return api.palette.relations(collection.id).component.thumbs.fetch();\n              });\n\n              for await (let collectionComponents of componentsOfCollections) {\n                  allComponents.push(collectionComponents);\n              }\n              */\n\n            this.collections = allCollections\n        },\n\n        async loadComponentsList() {\n            let objects = await cape.api.palette\n                .collection(this.$route.params.selectedCollection)\n                .componentSet // .meshes\n                .components.thumbs.counters.fetch()\n            // console.log(\">>>>\", objects);\n            // let { component: relationComponent } = aaa;\n            // this.componentsForCollection = objects.component.slice(0, 10);\n            this.componentsByHeight = Object.values(\n                objects.component.reduce((result, { height, ...rest }) => {\n                    if (!result[height])\n                        result[height] = {\n                            height,\n                            components: [],\n                        }\n                    result[height].components.push({\n                        height,\n                        ...rest,\n                    })\n                    return result\n                }, {})\n            )\n            // console.log(\">>>>\", objects, this.componentsByHeight);\n        },\n\n        setActiveCollection(id) {\n            cape.application.settings.set('collectionIndex', id)\n            this.activeCollection = id\n        },\n        onResize(size) {\n            this.rendererHeight = size.height - 230\n        },\n    },\n}\n</script>\n", "import mod from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-page.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../framework/node_modules/babel-loader/lib/index.js??ref--1-0!../../../../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./collection-page.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./collection-page.vue?vue&type=template&id=3aee603f&scoped=true&lang=pug&\"\nimport script from \"./collection-page.vue?vue&type=script&lang=js&\"\nexport * from \"./collection-page.vue?vue&type=script&lang=js&\"\nimport style0 from \"./collection-page.vue?vue&type=style&index=0&id=3aee603f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../framework/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3aee603f\",\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=style&index=0&id=360dcb88&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../framework/node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-2-0!../../framework/node_modules/css-loader/dist/cjs.js??ref--7-oneOf-2-1!../../framework/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../framework/node_modules/postcss-loader/src/index.js??ref--7-oneOf-2-2!../../framework/node_modules/sass-loader/lib/loader.js??ref--7-oneOf-2-3!../../framework/node_modules/vue-loader/lib/index.js??vue-loader-options!./TylkoMiniature.vue?vue&type=style&index=0&id=360dcb88&lang=scss&scoped=true&\""], "sourceRoot": ""}