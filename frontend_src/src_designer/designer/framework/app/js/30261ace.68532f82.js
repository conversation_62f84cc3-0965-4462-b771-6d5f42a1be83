(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["30261ace"],{"07c3":function(t,e,n){"use strict";var a=function(){var t=this;var e=t.$createElement;var n=t._self._c||e;return n("div",{staticClass:"container"},[n("div",{ref:"mini",staticClass:"mini"})])};var i=[];var o=n("a34a");var s=n.n(o);var r=n("192a");var c=n("c973");var l=n.n(c);var u=n("d6b6");var d=n("18a5");var p={props:{id:[String,Number],geo:[String,Object,Array],bgColor:[String],update:[String]},methods:{load:function t(e){var n=this;var a=d["a"].api.geo.componentSet({type:"componentRelations",id:e,queryForMiniature:true});a.pipe(function(t){console.log(t);n.build(t,e,false)},"paletteRelations")},build:function t(e,n,a){var i=this;d["a"].services.miniatures.add({data:e,id:n,fetched:a,base64:true,colorSet:"production",bgColor:this.bgColor}).then(function(){var t=l()(s.a.mark(function t(e){var n;return s.a.wrap(function t(a){while(1){switch(a.prev=a.next){case 0:while(i.$refs.mini&&i.$refs.mini.hasChildNodes()){i.$refs.mini.removeChild(i.$refs.mini.firstChild)}n=new Image;n.src=e.painterState;i.$refs.mini.appendChild(n);case 4:case"end":return a.stop()}}},t)}));return function(e){return t.apply(this,arguments)}}())}},created:function t(){},watch:{geo:function t(){this.build(this.geo,this.id,true)}},mounted:function t(){if(this.geo){this.build(this.geo,this.id,true);return}if(this.id){this.load(this.id)}}};var h=p;var v=n("f699");var f=n("2877");var m=Object(f["a"])(h,a,i,false,null,"360dcb88",null);var b=e["a"]=m.exports},"5dca":function(t,e,n){n("d2c4")("search",1,function(t,e,n){return[function n(a){"use strict";var i=t(this);var o=a==undefined?undefined:a[e];return o!==undefined?o.call(a,i):new RegExp(a)[e](String(i))},n]})},ac1b:function(t,e,n){},ad9c:function(t,e,n){},b3c2:function(t,e,n){"use strict";var a=n("ad9c");var i=n.n(a);var o=i.a},c0a7:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this;var e=t.$createElement;var n=t._self._c||e;return n("section",{staticClass:"main"},[n("div",{staticClass:"collection-details sc"},[n("keep-alive",[!t.choosenCollection?n("div",t._l(t.collections,function(e){return n("div",{key:e.id,staticClass:"card-wrapper collection-summary",attrs:{dark:"dark","inverted-light":"inverted-light",inline:"inline",color:"grey-10"}},[n("router-link",{staticClass:"collection-link",attrs:{to:"/collection/"+e.id}},[n("q-card-title",{staticClass:"q-py-sm"},[t._v(t._s(e.name)),e.description?n("p",{staticClass:"text-weight-light",staticStyle:{color:"white","font-size":"13px","padding-left":"15px","margin-bottom":"0px"}},[t._v(t._s(e.description))]):t._e()]),n("q-card-separator")],1),n("div",{staticClass:"card-container",staticStyle:{"padding-top":"5px"}},[n("div",{staticClass:"row"},[e.counters["component"]?n("q-btn",{staticClass:"button_counter_large",attrs:{label:"comp",disable:"disable",color:"cyan-10",size:"sm"}},[n("q-chip",{attrs:{floating:"floating",color:"cyan-9"}},[t._v(t._s(e.counters["component"]))])],1):t._e(),e.counters["component_set"]?n("q-btn",{staticClass:"button_counter",attrs:{label:"set",disable:"disable",color:"cyan-10",size:"sm"}},[n("q-chip",{attrs:{floating:"floating",color:"cyan-9"}},[t._v(t._s(e.counters["component_set"]))])],1):t._e(),e.counters["component_series"]?n("q-btn",{staticClass:"button_counter",attrs:{label:"serie",disable:"disable",color:"brown-9",size:"sm"}},[n("q-chip",{attrs:{floating:"floating",color:"brown-7"}},[t._v(t._s(e.counters["component_series"]))])],1):t._e(),e.counters["component_table"]?n("q-btn",{staticClass:"button_counter",attrs:{label:"table",disable:"disable",color:"brown-9",size:"sm"}},[n("q-chip",{attrs:{floating:"floating",color:"brown-7"}},[t._v(t._s(e.counters["component_table"]))])],1):t._e(),e.counters["mesh"]?n("q-btn",{staticClass:"button_counter",attrs:{label:"mesh",disable:"disable",color:"blue-grey-8",size:"sm"}},[n("q-chip",{attrs:{floating:"floating",color:"blue-grey-6"}},[t._v(t._s(e.counters["mesh"]))])],1):t._e(),e.counters["container"]?n("q-btn",{staticClass:"button_counter",attrs:{label:"container",disable:"disable",color:"deep-orange-10",size:"sm"}},[n("q-chip",{attrs:{floating:"floating",color:"deep-orange-8"}},[t._v(t._s(e.counters["container"]))])],1):t._e()],1),e.tags.length?n("div",[n("div",{staticClass:"card-separator q-mt-md"}),e.tags.length?n("q-chips-input",{attrs:{readonly:"readonly",disable:"disable"},model:{value:e.tags,callback:function(n){t.$set(e,"tags",n)},expression:"collection.tags"}}):t._e()],1):t._e()]),n("div",{staticClass:"card-separator"})],1)}),0):n("div",[n("div",{staticClass:"card-component-summary",attrs:{dark:"dark","inverted-light":"inverted-light",inline:"inline",color:"grey-1"}},[n("q-card-title",{staticClass:"q-py-sm",staticStyle:{color:"white"}},[t._v("Components")]),n("div",{staticClass:"card-separator"}),n("div",{staticClass:"card-container section-component"},t._l(t.componentsByHeight,function(e){return n("aa",[n("div",{staticClass:"column-component"},[n("q-card-title",{staticClass:"q-py-sm",staticStyle:{color:"white"}},[t._v("H: "+t._s(e.height)+" | "+t._s(e.components.length))]),t._l(e.components,function(e){return n("aa",[n("router-link",{attrs:{to:"/components/"+t.$route.params.selectedCollection+"/"+e.parent+"/"+e.id}},[n("t-mini",{key:e.id,staticClass:"inline",attrs:{id:e.id,"bg-color":"#aaaaaa",geo:e.thumbnails_data}})],1)],1)})],2)])}),1)],1)])])],1)])};var i=[];var o=n("3156");var s=n.n(o);var r=n("4082");var c=n.n(r);var l=n("359c");var u=n("7341");var d=n("1adf");var p=n("a34a");var h=n.n(p);var v=n("192a");var f=n("c973");var m=n.n(f);var b=n("b263");var g=n("9ec3");var C=n("18a5");var _=n("b2ec");var y=n("07c3");var w={name:"PageCollections",data:function t(){return{collections:[],choosenCollection:null,componentsByHeight:{},selectedTab:"0"}},components:{"t-bar":_["a"],"t-mini":y["a"]},watch:{$route:function t(e,n){var a=e.params.selectedCollection;if(a){this.choosenCollection=true;this.choosenCollectionId=a;this.loadComponentsList()}else{this.choosenCollection=false;this.loadAllCollections()}},choosenCollection:function t(){}},mounted:function t(){C["a"].application.viewport.fixed(true);if(this.$route.params.selectedCollection){this.choosenCollection=true;this.loadComponentsList()}else{this.loadAllCollections()}},methods:{loadAllCollections:function(){var t=m()(h.a.mark(function t(){var e,n;return h.a.wrap(function t(a){while(1){switch(a.prev=a.next){case 0:a.next=2;return C["a"].api.palette.collection().fetch();case 2:e=a.sent;n=e.collection;this.collections=n;case 5:case"end":return a.stop()}}},t,this)}));function e(){return t.apply(this,arguments)}return e}(),loadComponentsList:function(){var t=m()(h.a.mark(function t(){var e;return h.a.wrap(function t(n){while(1){switch(n.prev=n.next){case 0:n.next=2;return C["a"].api.palette.collection(this.$route.params.selectedCollection).componentSet.components.thumbs.counters.fetch();case 2:e=n.sent;this.componentsByHeight=Object.values(e.component.reduce(function(t,e){var n=e.height,a=c()(e,["height"]);if(!t[n])t[n]={height:n,components:[]};t[n].components.push(s()({height:n},a));return t},{}));case 4:case"end":return n.stop()}}},t,this)}));function e(){return t.apply(this,arguments)}return e}(),setActiveCollection:function t(e){C["a"].application.settings.set("collectionIndex",e);this.activeCollection=e},onResize:function t(e){this.rendererHeight=e.height-230}}};var q=w;var k=n("b3c2");var x=n("2877");var S=Object(x["a"])(q,a,i,false,null,"3aee603f",null);var $=e["default"]=S.exports},f699:function(t,e,n){"use strict";var a=n("ac1b");var i=n.n(a);var o=i.a}}]);
//# sourceMappingURL=30261ace.68532f82.js.map