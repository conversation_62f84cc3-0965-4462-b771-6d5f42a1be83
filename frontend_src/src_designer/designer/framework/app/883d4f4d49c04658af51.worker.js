(function(e){var r={};function t(n){if(r[n]){return r[n].exports}var a=r[n]={i:n,l:false,exports:{}};e[n].call(a.exports,a,a.exports,t);a.l=true;return a.exports}t.m=e;t.c=r;t.d=function(e,r,n){if(!t.o(e,r)){Object.defineProperty(e,r,{enumerable:true,get:n})}};t.r=function(e){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})};t.t=function(e,r){if(r&1)e=t(e);if(r&8)return e;if(r&4&&typeof e==="object"&&e&&e.__esModule)return e;var n=Object.create(null);t.r(n);Object.defineProperty(n,"default",{enumerable:true,value:e});if(r&2&&typeof e!="string")for(var a in e)t.d(n,a,function(r){return e[r]}.bind(null,a));return n};t.n=function(e){var r=e&&e.__esModule?function r(){return e["default"]}:function r(){return e};t.d(r,"a",r);return r};t.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)};t.p="/admin/webdesigner_app/";return t(t.s="3fc2")})({"0043":function(e,r){e.exports=t;function t(e,r,t){var n=r[0],a=r[1];e[0]=t[0]*n+t[4]*a+t[12];e[1]=t[1]*n+t[5]*a+t[13];return e}},"0283":function(e,r,t){var n=t("22fe");n(n.S,"Number",{isNaN:function e(r){return r!=r}})},"033c":function(e,r){e.exports=t;function t(e,r){e[0]=-r[0];e[1]=-r[1];return e}},"0536":function(e,r,t){var n=t("2679")("wks");var a=t("4509");var i=t("f861").Symbol;var o=typeof i=="function";var f=e.exports=function(e){return n[e]||(n[e]=o&&i[e]||(o?i:a)("Symbol."+e))};f.store=n},"0676":function(e,r){function t(){throw new TypeError("Invalid attempt to spread non-iterable instance")}e.exports=t},"06ed":function(e,r){e.exports=t;function t(e,r,t){e[0]=r[0]/t[0];e[1]=r[1]/t[1];return e}},"086a":function(e,r,t){e.exports=t("339b")},"0aae":function(e,r){e.exports=t;function t(e,r){e[0]=Math.floor(r[0]);e[1]=Math.floor(r[1]);return e}},"0b70":function(e,r){e.exports=t;function t(e,r){var t=r[0]-e[0],n=r[1]-e[1];return t*t+n*n}},"0de4":function(e,r,t){var n=t("22fe");var a=t("963b")(true);n(n.S,"Object",{entries:function e(r){return a(r)}})},"0e26":function(e,r,t){var n=t("9544");e.exports=function(e,r,t){n(e);if(r===undefined)return e;switch(t){case 1:return function(t){return e.call(r,t)};case 2:return function(t,n){return e.call(r,t,n)};case 3:return function(t,n,a){return e.call(r,t,n,a)}}return function(){return e.apply(r,arguments)}}},1008:function(e,r,t){var n=t("2b84");e.exports=function(e,r){if(!n(e))return e;var t,a;if(r&&typeof(t=e.toString)=="function"&&!n(a=t.call(e)))return a;if(typeof(t=e.valueOf)=="function"&&!n(a=t.call(e)))return a;if(!r&&typeof(t=e.toString)=="function"&&!n(a=t.call(e)))return a;throw TypeError("Can't convert object to primitive value")}},"11b0":function(e,r){function t(e){if(Symbol.iterator in Object(e)||Object.prototype.toString.call(e)==="[object Arguments]")return Array.from(e)}e.exports=t},"11da":function(e,r,t){var n=t("2b84");e.exports=function(e,r){if(!n(e)||e._t!==r)throw TypeError("Incompatible receiver, "+r+" required!");return e}},"123d":function(e,r){e.exports=t;function t(){var e=new Float32Array(2);e[0]=0;e[1]=0;return e}},1385:function(e,r,t){var n=t("a85c");e.exports=function(e,r,t){for(var a in r)n(e,a,r[a],t);return e}},"14a2":function(e,r,t){e.exports=a;var n=t("123d")();function a(e,r,t,a,i,o){var f,u;if(!r){r=2}if(!t){t=0}if(a){u=Math.min(a*r+t,e.length)}else{u=e.length}for(f=t;f<u;f+=r){n[0]=e[f];n[1]=e[f+1];i(n,n,o);e[f]=n[0];e[f+1]=n[1]}return e}},"160f":function(e,r,t){var n=t("2b84");var a=t("a2ce");var i=t("0536")("match");e.exports=function(e){var r;return n(e)&&((r=e[i])!==undefined?!!r:a(e)=="RegExp")}},"167a":function(e,r,t){var n=t("0536")("match");e.exports=function(e){var r=/./;try{"/./"[e](r)}catch(t){try{r[n]=false;return!"/./"[e](r)}catch(a){}}return true}},"16d7":function(e,r,t){var n=t("a2ce");var a=t("0536")("toStringTag");var i=n(function(){return arguments}())=="Arguments";var o=function(e,r){try{return e[r]}catch(t){}};e.exports=function(e){var r,t,f;return e===undefined?"Undefined":e===null?"Null":typeof(t=o(r=Object(e),a))=="string"?t:i?n(r):(f=n(r))=="Object"&&typeof r.callee=="function"?"Arguments":f}},1788:function(e,r,t){e.exports=a;var n=t("3443");function a(e,r){var t=e[0];var a=e[1];var i=r[0];var o=r[1];return Math.abs(t-i)<=n*Math.max(1,Math.abs(t),Math.abs(i))&&Math.abs(a-o)<=n*Math.max(1,Math.abs(a),Math.abs(o))}},"1a7e":function(e,r,t){"use strict";var n=t("c0f6");e.exports=function(e,r){return!!e&&n(function(){r?e.call(null,function(){},1):e.call(null)})}},"1a9d":function(e,r,t){"use strict";var n=t("22fe");var a=t("9724")(5);var i="find";var o=true;if(i in[])Array(1)[i](function(){o=false});n(n.P+n.F*o,"Array",{find:function e(r){return a(this,r,arguments.length>1?arguments[1]:undefined)}});t("c853")(i)},"1adf":function(e,r,t){var n=t("22fe");var a=t("963b")(false);n(n.S,"Object",{values:function e(r){return a(r)}})},"1d69":function(e,r,t){var n=t("e288");var a=t("f7b2");e.exports=function(e){return function(r,t){var i=String(a(r));var o=n(t);var f=i.length;var u,c;if(o<0||o>=f)return e?"":undefined;u=i.charCodeAt(o);return u<55296||u>56319||o+1===f||(c=i.charCodeAt(o+1))<56320||c>57343?e?i.charAt(o):u:e?i.slice(o,o+2):(u-55296<<10)+(c-56320)+65536}}},"1df5":function(e,r,t){"use strict";var n=t("22fe");var a=t("d7d0");var i=t("bbff");var o="startsWith";var f=""[o];n(n.P+n.F*t("167a")(o),"String",{startsWith:function e(r){var t=i(this,r,o);var n=a(Math.min(arguments.length>1?arguments[1]:undefined,t.length));var u=String(r);return f?f.call(t,u,n):t.slice(n,n+u.length)===u}})},"1f03":function(e,r,t){var n=t("98ab");var a=t("5a3a");e.exports=t("e2e5")?function(e,r,t){return n.f(e,r,a(1,t))}:function(e,r,t){e[r]=t;return e}},2236:function(e,r){function t(e){if(Array.isArray(e)){for(var r=0,t=new Array(e.length);r<e.length;r++){t[r]=e[r]}return t}}e.exports=t},"22fe":function(e,r,t){var n=t("f861");var a=t("e4e6");var i=t("1f03");var o=t("a85c");var f=t("0e26");var u="prototype";var c=function(e,r,t){var l=e&c.F;var s=e&c.G;var v=e&c.S;var p=e&c.P;var h=e&c.B;var d=s?n:v?n[r]||(n[r]={}):(n[r]||{})[u];var m=s?a:a[r]||(a[r]={});var g=m[u]||(m[u]={});var b,y,_,x;if(s)t=r;for(b in t){y=!l&&d&&d[b]!==undefined;_=(y?d:t)[b];x=h&&y?f(_,n):p&&typeof _=="function"?f(Function.call,_):_;if(d)o(d,b,_,e&c.U);if(m[b]!=_)i(m,b,x);if(p&&g[b]!=_)g[b]=_}};n.core=a;c.F=1;c.G=2;c.S=4;c.P=8;c.B=16;c.W=32;c.U=64;c.R=128;e.exports=c},"233f":function(e,r,t){"use strict";var n=t("22fe");var a=t("9544");var i=t("a911");var o=t("c0f6");var f=[].sort;var u=[1,2,3];n(n.P+n.F*(o(function(){u.sort(undefined)})||!o(function(){u.sort(null)})||!t("1a7e")(f)),"Array",{sort:function e(r){return r===undefined?f.call(i(this)):f.call(i(this),a(r))}})},2679:function(e,r,t){var n=t("e4e6");var a=t("f861");var i="__core-js_shared__";var o=a[i]||(a[i]={});(e.exports=function(e,r){return o[e]||(o[e]=r!==undefined?r:{})})("versions",[]).push({version:n.version,mode:t("8b78")?"pure":"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})},"278c":function(e,r,t){var n=t("c135");var a=t("9b42");var i=t("c240");function o(e,r){return n(e)||a(e,r)||i()}e.exports=o},"2b52":function(e,r,t){var n=t("16d7");var a=t("0536")("iterator");var i=t("781d");e.exports=t("e4e6").getIteratorMethod=function(e){if(e!=undefined)return e[a]||e["@@iterator"]||i[n(e)]}},"2b84":function(e,r){e.exports=function(e){return typeof e==="object"?e!==null:typeof e==="function"}},"2c89":function(e,r,t){var n=t("22fe");var a=t("f861").isFinite;n(n.S,"Number",{isFinite:function e(r){return typeof r=="number"&&a(r)}})},"2f26":function(e,r,t){"use strict";var n=t("1d69")(true);t("a695")(String,"String",function(e){this._t=String(e);this._i=0},function(){var e=this._t;var r=this._i;var t;if(r>=e.length)return{value:undefined,done:true};t=n(e,r);this._i+=t.length;return{value:t,done:false}})},"2f6e":function(e,r){e.exports=t;function t(e,r){r=r||1;var t=Math.random()*2*Math.PI;e[0]=Math.cos(t)*r;e[1]=Math.sin(t)*r;return e}},3014:function(e,r,t){"use strict";t.r(r);t.d(r,"painter",function(){return k});var n=t("970b");var a=t.n(n);var i=t("5bc3");var o=t.n(i);var f=t("9ec3");var u=t("6149");var c=t("ce62").getComponentMiniatureData;var l=t("e213");var s,v,p,h,d,m,g=3,b=false;var y;var _={1:"#A0A09F",2:"#FFFFFF",3:"#A0A09F",4:"#FFFFFF",5:"#000000",6:"#A0A09F",7:"#000000",8:"#5C5C5C"};var x=_;var w=function(){function e(){a()(this,e)}o()(e,[{key:"initialize",value:function e(r){b=this.startPainter(r.data.canvas,r.data.style)}},{key:"startPainter",value:function e(r,n){if(!r.getContext)return false;this.canvas=m=r;s=t("498f")({extensions:"angle_instanced_arrays",gl:m.getContext("webgl")});y=s({primitive:s.prop("style"),count:6,attributes:{position:function e(r,t){var n=t.geo;return[[n.topLeft.x,n.topLeft.y],[n.bottomRight.x,n.bottomRight.y],[n.bottomLeft.x,n.bottomLeft.y],[n.topLeft.x,n.topLeft.y],[n.topRight.x,n.topRight.y],[n.bottomRight.x,n.bottomRight.y]]}},uniforms:{scale:1.5,color:s.prop("color"),tick:function e(r){var t=r.tick;return t},viewportWidth:s.context("viewportWidth"),viewportHeight:s.context("viewportHeight")},frag:"\n                precision mediump float;\n                uniform vec4 color;\n                void main() {\n                    gl_FragColor = vec4(color);\n                }",vert:"\n                precision mediump float;\n                attribute vec2 position;\n                uniform vec2 offset;\n                uniform vec2 scale;\n                uniform float viewportWidth;\n                uniform float viewportHeight;\n                void main() {\n                    float r = (viewportWidth) / (viewportHeight);\n                    gl_Position = vec4(position.xy, 0, 1);\n                }"});return true}},{key:"draw",value:function e(r){if(!b)return;var t;var n=r.data.data;s.poll();s.clear({color:[0,0,0,0],depth:1});if(n.fetched){t=n.data}else{if(!n.data.superior_object_type){self.postMessage({finishedJob:true,finishedJobID:r.data.id,bitmap:m.transferToImageBitmap()});return}try{t=c({temporaryfix:true,serialization:n.data,id:n.id})}catch(a){console.log("mini data error",a);self.postMessage({finishedJob:true,error:true,finishedJobID:r.data.id,bitmap:m.transferToImageBitmap()});return}}if(t&&t[0]){t=t.map(function(e){var r=[.5,.5,.5,.01];var t=null;if(e.mode==0){var a=e.geom[0].map(function(e){return e});var i=e.geom[1].map(function(e){return e});var o=0;if(a[0]==i[0]){o=1}var c=.0175;var l;switch(o){case 0:l={topLeft:{x:a[0],y:a[1]-c},topRight:{x:i[0],y:a[1]-c},bottomLeft:{x:a[0],y:a[1]+c},bottomRight:{x:i[0],y:a[1]+c}};break;case 1:l={topLeft:{x:a[0]-c,y:a[1]},topRight:{x:a[0]+c,y:a[1]},bottomLeft:{x:a[0]-c,y:i[1]},bottomRight:{x:a[0]+c,y:i[1]}};break}}else if(e.mode==1){var s=.03;var l;l={topLeft:{x:e.x[0],y:e.y[0]},topRight:{x:e.x[1],y:e.y[0]},bottomLeft:{x:e.x[0],y:e.y[1]},bottomRight:{x:e.x[1],y:e.y[1]}}}else if(e.mode==2){var v=.025;var l;l={topLeft:{x:e.geom[3][0],y:e.geom[3][1]},topRight:{x:e.geom[2][0],y:e.geom[2][1]},bottomLeft:{x:e.geom[0][0],y:e.geom[0][1]},bottomRight:{x:e.geom[1][0],y:e.geom[1][1]}};t=f.cloneDeep(l);t.topLeft.x+=v;t.topLeft.y-=v;t.topRight.x-=v;t.topRight.y-=v;t.bottomLeft.x+=v;t.bottomLeft.y+=v;t.bottomRight.x-=v;t.bottomRight.y+=v}var p=function r(t){e.color=t;return u(n.colorSet=="production"?x[t]:_[t]).gl()};switch(e.color){case 0:r=[1,1,1,0];break;case 1:r=p(1);break;case 2:r=p(2);break;case 3:r=p(3);break;case 4:r=p(4);break;case 5:r=p(5);break;case 6:r=p(6);break;case 7:r=p(7);break;case 8:r=p(8);break;default:r=e.color==undefined?[1,1,1,1]:u(e.color).gl();break}if(l){if(t&&(e.color==2||e.color==4)){y({geo:t,color:r,style:n.style});y({geo:l,color:[0,0,0,1],style:n.style})}else{y({geo:l,color:r,style:n.style})}}return e})}return t}}]);return e}();var k=new w},"305e":function(e,r,t){e.exports=t("bbaa")},3156:function(e,r,t){var n=t("9523");function a(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};var a=Object.keys(t);if(typeof Object.getOwnPropertySymbols==="function"){a=a.concat(Object.getOwnPropertySymbols(t).filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))}a.forEach(function(r){n(e,r,t[r])})}return e}e.exports=a},"332e":function(e,r){e.exports=t;function t(e,r,t){e[0]=r[0]*t;e[1]=r[1]*t;return e}},"339b":function(e,r){e.exports=t;function t(e,r){var t=r[0]-e[0],n=r[1]-e[1];return Math.sqrt(t*t+n*n)}},3443:function(e,r){e.exports=1e-6},3546:function(e,r,t){var n=t("7aae");var a=t("499f");e.exports=Object.keys||function e(r){return n(r,a)}},"359c":function(e,r,t){var n=t("7341");var a=t("3546");var i=t("a85c");var o=t("f861");var f=t("1f03");var u=t("781d");var c=t("0536");var l=c("iterator");var s=c("toStringTag");var v=u.Array;var p={CSSRuleList:true,CSSStyleDeclaration:false,CSSValueList:false,ClientRectList:false,DOMRectList:false,DOMStringList:false,DOMTokenList:true,DataTransferItemList:false,FileList:false,HTMLAllCollection:false,HTMLCollection:false,HTMLFormElement:false,HTMLSelectElement:false,MediaList:true,MimeTypeArray:false,NamedNodeMap:false,NodeList:true,PaintRequestList:false,Plugin:false,PluginArray:false,SVGLengthList:false,SVGNumberList:false,SVGPathSegList:false,SVGPointList:false,SVGStringList:false,SVGTransformList:false,SourceBufferList:false,StyleSheetList:true,TextTrackCueList:false,TextTrackList:false,TouchList:false};for(var h=a(p),d=0;d<h.length;d++){var m=h[d];var g=p[m];var b=o[m];var y=b&&b.prototype;var _;if(y){if(!y[l])f(y,l,v);if(!y[s])f(y,s,m);u[m]=v;if(g)for(_ in n)if(!y[_])i(y,_,n[_],true)}}},3761:function(e,r){e.exports=t;function t(e,r,t){var n=r[0]*t[1]-r[1]*t[0];e[0]=e[1]=0;e[2]=n;return e}},3955:function(e,r,t){var n=t("2b84");e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},"39c6":function(e,r){e.exports=function(e,r){return{value:r,done:!!e}}},"3b5a":function(e,r,t){var n=t("22fe");n(n.P,"Array",{fill:t("afb7")});t("c853")("fill")},"3cc3":function(e,r){e.exports=t;function t(e,r){e[0]=r[0];e[1]=r[1];return e}},"3ef8":function(e,r,t){"use strict";var n=t("22fe");var a=t("d7d0");var i=t("bbff");var o="endsWith";var f=""[o];n(n.P+n.F*t("167a")(o),"String",{endsWith:function e(r){var t=i(this,r,o);var n=arguments.length>1?arguments[1]:undefined;var u=a(t.length);var c=n===undefined?u:Math.min(a(n),u);var l=String(r);return f?f.call(t,l,c):t.slice(c-l.length,c)===l}})},"3fc2":function(e,r,t){var n=t("3014").painter;self.addEventListener("message",function(e){switch(e.data.action){case"initialize":n.initialize(e);break;case"draw":var r=e.data.data;var t=n.draw(e);self.postMessage({finishedJob:true,finishedJobID:e.data.id,isBase64:r.isBase64,bitmap:n.canvas.transferToImageBitmap(),geo:t});break;default:self.postMessage({finishedJob:true,finishedJobID:e.data.id,bitmap:n.canvas.transferToImageBitmap()});break}})},"404a":function(e,r,t){var n=t("4509")("meta");var a=t("2b84");var i=t("d8a8");var o=t("98ab").f;var f=0;var u=Object.isExtensible||function(){return true};var c=!t("c0f6")(function(){return u(Object.preventExtensions({}))});var l=function(e){o(e,n,{value:{i:"O"+ ++f,w:{}}})};var s=function(e,r){if(!a(e))return typeof e=="symbol"?e:(typeof e=="string"?"S":"P")+e;if(!i(e,n)){if(!u(e))return"F";if(!r)return"E";l(e)}return e[n].i};var v=function(e,r){if(!i(e,n)){if(!u(e))return true;if(!r)return false;l(e)}return e[n].w};var p=function(e){if(c&&h.NEED&&u(e)&&!i(e,n))l(e);return e};var h=e.exports={KEY:n,NEED:false,fastKey:s,getWeak:v,onFreeze:p}},"40ec":function(e,r){e.exports=t;function t(e,r){e[0]=1/r[0];e[1]=1/r[1];return e}},"448a":function(e,r,t){var n=t("2236");var a=t("11b0");var i=t("0676");function o(e){return n(e)||a(e)||i()}e.exports=o},4509:function(e,r){var t=0;var n=Math.random();e.exports=function(e){return"Symbol(".concat(e===undefined?"":e,")_",(++t+n).toString(36))}},4516:function(e,r){e.exports=t;function t(e,r){return e[0]===r[0]&&e[1]===r[1]}},"45a9":function(e,r,t){"use strict";var n=t("3955");e.exports=function(){var e=n(this);var r="";if(e.global)r+="g";if(e.ignoreCase)r+="i";if(e.multiline)r+="m";if(e.unicode)r+="u";if(e.sticky)r+="y";return r}},"46ab":function(e,r,t){var n=t("5f45");var a=t("f7b2");e.exports=function(e){return n(a(e))}},4869:function(e,r){e.exports=t;function t(e){var r=e[0],t=e[1];return r*r+t*t}},"498f":function(e,r,t){(function(r,t){true?e.exports=t():undefined})(this,function(){"use strict";var e=function(e){return e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Float32Array||e instanceof Float64Array||e instanceof Uint8ClampedArray};var r=function(e,r){var t=Object.keys(r);for(var n=0;n<t.length;++n){e[t[n]]=r[t[n]]}return e};var t="\n";function n(e){if(typeof atob!=="undefined"){return atob(e)}return"base64:"+e}function a(e){var r=new Error("(regl) "+e);console.error(r);throw r}function i(e,r){if(!e){a(r)}}function o(e){if(e){return": "+e}return""}function f(e,r,t){if(!(e in r)){a("unknown parameter ("+e+")"+o(t)+". possible values: "+Object.keys(r).join())}}function u(r,t){if(!e(r)){a("invalid parameter type"+o(t)+". must be a typed array")}}function c(e,r,t){if(typeof e!==r){a("invalid parameter type"+o(t)+". expected "+r+", got "+typeof e)}}function l(e,r){if(!(e>=0&&(e|0)===e)){a("invalid parameter type, ("+e+")"+o(r)+". must be a nonnegative integer")}}function s(e,r,t){if(r.indexOf(e)<0){a("invalid value"+o(t)+". must be one of: "+r)}}var v=["gl","canvas","container","attributes","pixelRatio","extensions","optionalExtensions","profile","onDone"];function p(e){Object.keys(e).forEach(function(e){if(v.indexOf(e)<0){a('invalid regl constructor argument "'+e+'". must be one of '+v)}})}function h(e,r){e=e+"";while(e.length<r){e=" "+e}return e}function d(){this.name="unknown";this.lines=[];this.index={};this.hasErrors=false}function m(e,r){this.number=e;this.line=r;this.errors=[]}function g(e,r,t){this.file=e;this.line=r;this.message=t}function b(){var e=new Error;var r=(e.stack||e).toString();var t=/compileProcedure.*\n\s*at.*\((.*)\)/.exec(r);if(t){return t[1]}var n=/compileProcedure.*\n\s*at\s+(.*)(\n|$)/.exec(r);if(n){return n[1]}return"unknown"}function y(){var e=new Error;var r=(e.stack||e).toString();var t=/at REGLCommand.*\n\s+at.*\((.*)\)/.exec(r);if(t){return t[1]}var n=/at REGLCommand.*\n\s+at\s+(.*)\n/.exec(r);if(n){return n[1]}return"unknown"}function _(e,r){var t=e.split("\n");var a=1;var i=0;var o={unknown:new d,0:new d};o.unknown.name=o[0].name=r||b();o.unknown.lines.push(new m(0,""));for(var f=0;f<t.length;++f){var u=t[f];var c=/^\s*\#\s*(\w+)\s+(.+)\s*$/.exec(u);if(c){switch(c[1]){case"line":var l=/(\d+)(\s+\d+)?/.exec(c[2]);if(l){a=l[1]|0;if(l[2]){i=l[2]|0;if(!(i in o)){o[i]=new d}}}break;case"define":var s=/SHADER_NAME(_B64)?\s+(.*)$/.exec(c[2]);if(s){o[i].name=s[1]?n(s[2]):s[2]}break}}o[i].lines.push(new m(a++,u))}Object.keys(o).forEach(function(e){var r=o[e];r.lines.forEach(function(e){r.index[e.number]=e})});return o}function x(e){var r=[];e.split("\n").forEach(function(e){if(e.length<5){return}var t=/^ERROR\:\s+(\d+)\:(\d+)\:\s*(.*)$/.exec(e);if(t){r.push(new g(t[1]|0,t[2]|0,t[3].trim()))}else if(e.length>0){r.push(new g("unknown",0,e))}});return r}function w(e,r){r.forEach(function(r){var t=e[r.file];if(t){var n=t.index[r.line];if(n){n.errors.push(r);t.hasErrors=true;return}}e.unknown.hasErrors=true;e.unknown.lines[0].errors.push(r)})}function k(e,r,n,a,o){if(!e.getShaderParameter(r,e.COMPILE_STATUS)){var f=e.getShaderInfoLog(r);var u=a===e.FRAGMENT_SHADER?"fragment":"vertex";T(n,"string",u+" shader source must be a string",o);var c=_(n,o);var l=x(f);w(c,l);Object.keys(c).forEach(function(e){var r=c[e];if(!r.hasErrors){return}var n=[""];var a=[""];function i(e,r){n.push(e);a.push(r||"")}i("file number "+e+": "+r.name+"\n","color:red;text-decoration:underline;font-weight:bold");r.lines.forEach(function(e){if(e.errors.length>0){i(h(e.number,4)+"|  ","background-color:yellow; font-weight:bold");i(e.line+t,"color:red; background-color:yellow; font-weight:bold");var r=0;e.errors.forEach(function(n){var a=n.message;var o=/^\s*\'(.*)\'\s*\:\s*(.*)$/.exec(a);if(o){var f=o[1];a=o[2];switch(f){case"assign":f="=";break}r=Math.max(e.line.indexOf(f,r),0)}else{r=0}i(h("| ",6));i(h("^^^",r+3)+t,"font-weight:bold");i(h("| ",6));i(a+t,"font-weight:bold")});i(h("| ",6)+t)}else{i(h(e.number,4)+"|  ");i(e.line+t,"color:red")}});if(typeof document!=="undefined"&&!window.chrome){a[0]=n.join("%c");console.log.apply(console,a)}else{console.log(n.join(""))}});i.raise("Error compiling "+u+" shader, "+c[0].name)}}function z(e,r,n,a,o){if(!e.getProgramParameter(r,e.LINK_STATUS)){var f=e.getProgramInfoLog(r);var u=_(n,o);var c=_(a,o);var l='Error linking program with vertex shader, "'+c[0].name+'", and fragment shader "'+u[0].name+'"';if(typeof document!=="undefined"){console.log("%c"+l+t+"%c"+f,"color:red;text-decoration:underline;font-weight:bold","color:red")}else{console.log(l+t+f)}i.raise(l)}}function E(e){e._commandRef=b()}function S(e,r,t,n){E(e);function a(e){if(e){return n.id(e)}return 0}e._fragId=a(e.static.frag);e._vertId=a(e.static.vert);function i(e,r){Object.keys(r).forEach(function(r){e[n.id(r)]=true})}var o=e._uniformSet={};i(o,r.static);i(o,r.dynamic);var f=e._attributeSet={};i(f,t.static);i(f,t.dynamic);e._hasCount="count"in e.static||"count"in e.dynamic||"elements"in e.static||"elements"in e.dynamic}function A(e,r){var t=y();a(e+" in command "+(r||b())+(t==="unknown"?"":" called from "+t))}function j(e,r,t){if(!e){A(r,t||b())}}function O(e,r,t,n){if(!(e in r)){A("unknown parameter ("+e+")"+o(t)+". possible values: "+Object.keys(r).join(),n||b())}}function T(e,r,t,n){if(typeof e!==r){A("invalid parameter type"+o(t)+". expected "+r+", got "+typeof e,n||b())}}function M(e){e()}function D(e,r,t){if(e.texture){s(e.texture._texture.internalformat,r,"unsupported texture format for attachment")}else{s(e.renderbuffer._renderbuffer.format,t,"unsupported renderbuffer format for attachment")}}var I=33071;var C=9728;var P=9984;var F=9985;var N=9986;var L=9987;var R=5120;var B=5121;var W=5122;var U=5123;var q=5124;var G=5125;var $=5126;var V=32819;var H=32820;var Y=33635;var X=34042;var Q=36193;var J={};J[R]=J[B]=1;J[W]=J[U]=J[Q]=J[Y]=J[V]=J[H]=2;J[q]=J[G]=J[$]=J[X]=4;function K(e,r){if(e===H||e===V||e===Y){return 2}else if(e===X){return 4}else{return J[e]*r}}function Z(e){return!(e&e-1)&&!!e}function ee(e,r,t){var n;var a=r.width;var o=r.height;var f=r.channels;i(a>0&&a<=t.maxTextureSize&&o>0&&o<=t.maxTextureSize,"invalid texture shape");if(e.wrapS!==I||e.wrapT!==I){i(Z(a)&&Z(o),"incompatible wrap mode for texture, both width and height must be power of 2")}if(r.mipmask===1){if(a!==1&&o!==1){i(e.minFilter!==P&&e.minFilter!==N&&e.minFilter!==F&&e.minFilter!==L,"min filter requires mipmap")}}else{i(Z(a)&&Z(o),"texture must be a square power of 2 to support mipmapping");i(r.mipmask===(a<<1)-1,"missing or incomplete mipmap data")}if(r.type===$){if(t.extensions.indexOf("oes_texture_float_linear")<0){i(e.minFilter===C&&e.magFilter===C,"filter not supported, must enable oes_texture_float_linear")}i(!e.genMipmaps,"mipmap generation not supported with float textures")}var u=r.images;for(n=0;n<16;++n){if(u[n]){var c=a>>n;var l=o>>n;i(r.mipmask&1<<n,"missing mipmap data");var s=u[n];i(s.width===c&&s.height===l,"invalid shape for mip images");i(s.format===r.format&&s.internalformat===r.internalformat&&s.type===r.type,"incompatible type for mip image");if(s.compressed){}else if(s.data){var v=Math.ceil(K(s.type,f)*c/s.unpackAlignment)*s.unpackAlignment;i(s.data.byteLength===v*l,"invalid data for image, buffer size is inconsistent with image format")}else if(s.element){}else if(s.copy){}}else if(!e.genMipmaps){i((r.mipmask&1<<n)===0,"extra mipmap data")}}if(r.compressed){i(!e.genMipmaps,"mipmap generation for compressed images not supported")}}function re(e,r,t,n){var a=e.width;var o=e.height;var f=e.channels;i(a>0&&a<=n.maxTextureSize&&o>0&&o<=n.maxTextureSize,"invalid texture shape");i(a===o,"cube map must be square");i(r.wrapS===I&&r.wrapT===I,"wrap mode not supported by cube map");for(var u=0;u<t.length;++u){var c=t[u];i(c.width===a&&c.height===o,"inconsistent cube map face shape");if(r.genMipmaps){i(!c.compressed,"can not generate mipmap for compressed textures");i(c.mipmask===1,"can not specify mipmaps and generate mipmaps")}else{}var l=c.images;for(var s=0;s<16;++s){var v=l[s];if(v){var p=a>>s;var h=o>>s;i(c.mipmask&1<<s,"missing mipmap data");i(v.width===p&&v.height===h,"invalid shape for mip images");i(v.format===e.format&&v.internalformat===e.internalformat&&v.type===e.type,"incompatible type for mip image");if(v.compressed){}else if(v.data){i(v.data.byteLength===p*h*Math.max(K(v.type,f),v.unpackAlignment),"invalid data for image, buffer size is inconsistent with image format")}else if(v.element){}else if(v.copy){}}}}}var te=r(i,{optional:M,raise:a,commandRaise:A,command:j,parameter:f,commandParameter:O,constructor:p,type:c,commandType:T,isTypedArray:u,nni:l,oneOf:s,shaderError:k,linkError:z,callSite:y,saveCommandRef:E,saveDrawInfo:S,framebufferFormat:D,guessCommand:b,texture2D:ee,textureCube:re});var ne=0;var ae=0;function ie(e,r){this.id=ne++;this.type=e;this.data=r}function oe(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}function fe(e){if(e.length===0){return[]}var r=e.charAt(0);var t=e.charAt(e.length-1);if(e.length>1&&r===t&&(r==='"'||r==="'")){return['"'+oe(e.substr(1,e.length-2))+'"']}var n=/\[(false|true|null|\d+|'[^']*'|"[^"]*")\]/.exec(e);if(n){return fe(e.substr(0,n.index)).concat(fe(n[1])).concat(fe(e.substr(n.index+n[0].length)))}var a=e.split(".");if(a.length===1){return['"'+oe(e)+'"']}var i=[];for(var o=0;o<a.length;++o){i=i.concat(fe(a[o]))}return i}function ue(e){return"["+fe(e).join("][")+"]"}function ce(e,r){return new ie(e,ue(r+""))}function le(e){return typeof e==="function"&&!e._reglType||e instanceof ie}function se(e,r){if(typeof e==="function"){return new ie(ae,e)}return e}var ve={DynamicVariable:ie,define:ce,isDynamic:le,unbox:se,accessor:ue};var pe={next:typeof requestAnimationFrame==="function"?function(e){return requestAnimationFrame(e)}:function(e){return setTimeout(e,16)},cancel:typeof cancelAnimationFrame==="function"?function(e){return cancelAnimationFrame(e)}:clearTimeout};var he=typeof performance!=="undefined"&&performance.now?function(){return performance.now()}:function(){return+new Date};function de(){var e={"":0};var r=[""];return{id:function(t){var n=e[t];if(n){return n}n=e[t]=r.length;r.push(t);return n},str:function(e){return r[e]}}}function me(e,t,n){var a=document.createElement("canvas");r(a.style,{border:0,margin:0,padding:0,top:0,left:0});e.appendChild(a);if(e===document.body){a.style.position="absolute";r(e.style,{margin:0,padding:0})}function i(){var t=window.innerWidth;var i=window.innerHeight;if(e!==document.body){var o=e.getBoundingClientRect();t=o.right-o.left;i=o.bottom-o.top}a.width=n*t;a.height=n*i;r(a.style,{width:t+"px",height:i+"px"})}window.addEventListener("resize",i,false);function o(){window.removeEventListener("resize",i);e.removeChild(a)}i();return{canvas:a,onDestroy:o}}function ge(e,r){function t(t){try{return e.getContext(t,r)}catch(n){return null}}return t("webgl")||t("experimental-webgl")||t("webgl-experimental")}function be(e){return typeof e.nodeName==="string"&&typeof e.appendChild==="function"&&typeof e.getBoundingClientRect==="function"}function ye(e){return typeof e.drawArrays==="function"||typeof e.drawElements==="function"}function _e(e){if(typeof e==="string"){return e.split()}te(Array.isArray(e),"invalid extension array");return e}function xe(e){if(typeof e==="string"){te(typeof document!=="undefined","not supported outside of DOM");return document.querySelector(e)}return e}function we(e){var r=e||{};var t,n,a,i;var o={};var f=[];var u=[];var c=typeof window==="undefined"?1:window.devicePixelRatio;var l=false;var s=function(e){if(e){te.raise(e)}};var v=function(){};if(typeof r==="string"){te(typeof document!=="undefined","selector queries only supported in DOM enviroments");t=document.querySelector(r);te(t,"invalid query string for element")}else if(typeof r==="object"){if(be(r)){t=r}else if(ye(r)){i=r;a=i.canvas}else{te.constructor(r);if("gl"in r){i=r.gl}else if("canvas"in r){a=xe(r.canvas)}else if("container"in r){n=xe(r.container)}if("attributes"in r){o=r.attributes;te.type(o,"object","invalid context attributes")}if("extensions"in r){f=_e(r.extensions)}if("optionalExtensions"in r){u=_e(r.optionalExtensions)}if("onDone"in r){te.type(r.onDone,"function","invalid or missing onDone callback");s=r.onDone}if("profile"in r){l=!!r.profile}if("pixelRatio"in r){c=+r.pixelRatio;te(c>0,"invalid pixel ratio")}}}else{te.raise("invalid arguments to regl")}if(t){if(t.nodeName.toLowerCase()==="canvas"){a=t}else{n=t}}if(!i){if(!a){te(typeof document!=="undefined","must manually specify webgl context outside of DOM environments");var p=me(n||document.body,s,c);if(!p){return null}a=p.canvas;v=p.onDestroy}i=ge(a,o)}if(!i){v();s("webgl not supported, try upgrading your browser or graphics drivers http://get.webgl.org");return null}return{gl:i,canvas:a,container:n,extensions:f,optionalExtensions:u,pixelRatio:c,profile:l,onDone:s,onDestroy:v}}function ke(e,r){var t={};function n(r){te.type(r,"string","extension name must be string");var n=r.toLowerCase();var a;try{a=t[n]=e.getExtension(n)}catch(i){}return!!a}for(var a=0;a<r.extensions.length;++a){var i=r.extensions[a];if(!n(i)){r.onDestroy();r.onDone('"'+i+'" extension is not supported by the current WebGL context, try upgrading your system or a different browser');return null}}r.optionalExtensions.forEach(n);return{extensions:t,restore:function(){Object.keys(t).forEach(function(e){if(t[e]&&!n(e)){throw new Error("(regl): error restoring extension "+e)}})}}}function ze(e,r){var t=Array(e);for(var n=0;n<e;++n){t[n]=r(n)}return t}var Ee=5120;var Se=5121;var Ae=5122;var je=5123;var Oe=5124;var Te=5125;var Me=5126;function De(e){for(var r=16;r<=1<<28;r*=16){if(e<=r){return r}}return 0}function Ie(e){var r,t;r=(e>65535)<<4;e>>>=r;t=(e>255)<<3;e>>>=t;r|=t;t=(e>15)<<2;e>>>=t;r|=t;t=(e>3)<<1;e>>>=t;r|=t;return r|e>>1}function Ce(){var e=ze(8,function(){return[]});function r(r){var t=De(r);var n=e[Ie(t)>>2];if(n.length>0){return n.pop()}return new ArrayBuffer(t)}function t(r){e[Ie(r.byteLength)>>2].push(r)}function n(e,t){var n=null;switch(e){case Ee:n=new Int8Array(r(t),0,t);break;case Se:n=new Uint8Array(r(t),0,t);break;case Ae:n=new Int16Array(r(2*t),0,t);break;case je:n=new Uint16Array(r(2*t),0,t);break;case Oe:n=new Int32Array(r(4*t),0,t);break;case Te:n=new Uint32Array(r(4*t),0,t);break;case Me:n=new Float32Array(r(4*t),0,t);break;default:return null}if(n.length!==t){return n.subarray(0,t)}return n}function a(e){t(e.buffer)}return{alloc:r,free:t,allocType:n,freeType:a}}var Pe=Ce();Pe.zero=Ce();var Fe=3408;var Ne=3410;var Le=3411;var Re=3412;var Be=3413;var We=3414;var Ue=3415;var qe=33901;var Ge=33902;var $e=3379;var Ve=3386;var He=34921;var Ye=36347;var Xe=36348;var Qe=35661;var Je=35660;var Ke=34930;var Ze=36349;var er=34076;var rr=34024;var tr=7936;var nr=7937;var ar=7938;var ir=35724;var or=34047;var fr=36063;var ur=34852;var cr=3553;var lr=34067;var sr=34069;var vr=33984;var pr=6408;var hr=5126;var dr=5121;var mr=36160;var gr=36053;var br=36064;var yr=16384;var _r=function(e,r){var t=1;if(r.ext_texture_filter_anisotropic){t=e.getParameter(or)}var n=1;var a=1;if(r.webgl_draw_buffers){n=e.getParameter(ur);a=e.getParameter(fr)}var i=!!r.oes_texture_float;if(i){var o=e.createTexture();e.bindTexture(cr,o);e.texImage2D(cr,0,pr,1,1,0,pr,hr,null);var f=e.createFramebuffer();e.bindFramebuffer(mr,f);e.framebufferTexture2D(mr,br,cr,o,0);e.bindTexture(cr,null);if(e.checkFramebufferStatus(mr)!==gr)i=false;else{e.viewport(0,0,1,1);e.clearColor(1,0,0,1);e.clear(yr);var u=Pe.allocType(hr,4);e.readPixels(0,0,1,1,pr,hr,u);if(e.getError())i=false;else{e.deleteFramebuffer(f);e.deleteTexture(o);i=u[0]===1}Pe.freeType(u)}}var c=true;var l=e.createTexture();var s=Pe.allocType(dr,36);e.activeTexture(vr);e.bindTexture(lr,l);e.texImage2D(sr,0,pr,3,3,0,pr,dr,s);Pe.freeType(s);e.bindTexture(lr,null);e.deleteTexture(l);c=!e.getError();return{colorBits:[e.getParameter(Ne),e.getParameter(Le),e.getParameter(Re),e.getParameter(Be)],depthBits:e.getParameter(We),stencilBits:e.getParameter(Ue),subpixelBits:e.getParameter(Fe),extensions:Object.keys(r).filter(function(e){return!!r[e]}),maxAnisotropic:t,maxDrawbuffers:n,maxColorAttachments:a,pointSizeDims:e.getParameter(qe),lineWidthDims:e.getParameter(Ge),maxViewportDims:e.getParameter(Ve),maxCombinedTextureUnits:e.getParameter(Qe),maxCubeMapSize:e.getParameter(er),maxRenderbufferSize:e.getParameter(rr),maxTextureUnits:e.getParameter(Ke),maxTextureSize:e.getParameter($e),maxAttributes:e.getParameter(He),maxVertexUniforms:e.getParameter(Ye),maxVertexTextureUnits:e.getParameter(Je),maxVaryingVectors:e.getParameter(Xe),maxFragmentUniforms:e.getParameter(Ze),glsl:e.getParameter(ir),renderer:e.getParameter(nr),vendor:e.getParameter(tr),version:e.getParameter(ar),readFloat:i,npotTextureCube:c}};function xr(r){return!!r&&typeof r==="object"&&Array.isArray(r.shape)&&Array.isArray(r.stride)&&typeof r.offset==="number"&&r.shape.length===r.stride.length&&(Array.isArray(r.data)||e(r.data))}var wr=function(e){return Object.keys(e).map(function(r){return e[r]})};var kr={shape:Or,flatten:jr};function zr(e,r,t){for(var n=0;n<r;++n){t[n]=e[n]}}function Er(e,r,t,n){var a=0;for(var i=0;i<r;++i){var o=e[i];for(var f=0;f<t;++f){n[a++]=o[f]}}}function Sr(e,r,t,n,a,i){var o=i;for(var f=0;f<r;++f){var u=e[f];for(var c=0;c<t;++c){var l=u[c];for(var s=0;s<n;++s){a[o++]=l[s]}}}}function Ar(e,r,t,n,a){var i=1;for(var o=t+1;o<r.length;++o){i*=r[o]}var f=r[t];if(r.length-t===4){var u=r[t+1];var c=r[t+2];var l=r[t+3];for(o=0;o<f;++o){Sr(e[o],u,c,l,n,a);a+=i}}else{for(o=0;o<f;++o){Ar(e[o],r,t+1,n,a);a+=i}}}function jr(e,r,t,n){var a=1;if(r.length){for(var i=0;i<r.length;++i){a*=r[i]}}else{a=0}var o=n||Pe.allocType(t,a);switch(r.length){case 0:break;case 1:zr(e,r[0],o);break;case 2:Er(e,r[0],r[1],o);break;case 3:Sr(e,r[0],r[1],r[2],o,0);break;default:Ar(e,r,0,o,0)}return o}function Or(e){var r=[];for(var t=e;t.length;t=t[0]){r.push(t.length)}return r}var Tr={"[object Int8Array]":5120,"[object Int16Array]":5122,"[object Int32Array]":5124,"[object Uint8Array]":5121,"[object Uint8ClampedArray]":5121,"[object Uint16Array]":5123,"[object Uint32Array]":5125,"[object Float32Array]":5126,"[object Float64Array]":5121,"[object ArrayBuffer]":5121};var Mr=5120;var Dr=5122;var Ir=5124;var Cr=5121;var Pr=5123;var Fr=5125;var Nr=5126;var Lr=5126;var Rr={int8:Mr,int16:Dr,int32:Ir,uint8:Cr,uint16:Pr,uint32:Fr,float:Nr,float32:Lr};var Br=35048;var Wr=35040;var Ur={dynamic:Br,stream:Wr,static:35044};var qr=kr.flatten;var Gr=kr.shape;var $r=35044;var Vr=35040;var Hr=5121;var Yr=5126;var Xr=[];Xr[5120]=1;Xr[5122]=2;Xr[5124]=4;Xr[5121]=1;Xr[5123]=2;Xr[5125]=4;Xr[5126]=4;function Qr(e){return Tr[Object.prototype.toString.call(e)]|0}function Jr(e,r){for(var t=0;t<r.length;++t){e[t]=r[t]}}function Kr(e,r,t,n,a,i,o){var f=0;for(var u=0;u<t;++u){for(var c=0;c<n;++c){e[f++]=r[a*u+i*c+o]}}}function Zr(r,t,n,a){var i=0;var o={};function f(e){this.id=i++;this.buffer=r.createBuffer();this.type=e;this.usage=$r;this.byteLength=0;this.dimension=1;this.dtype=Hr;this.persistentData=null;if(n.profile){this.stats={size:0}}}f.prototype.bind=function(){r.bindBuffer(this.type,this.buffer)};f.prototype.destroy=function(){p(this)};var u=[];function c(e,r){var t=u.pop();if(!t){t=new f(e)}t.bind();v(t,r,Vr,0,1,false);return t}function l(e){u.push(e)}function s(e,t,n){e.byteLength=t.byteLength;r.bufferData(e.type,t,n)}function v(r,t,n,a,i,o){var f;r.usage=n;if(Array.isArray(t)){r.dtype=a||Yr;if(t.length>0){var u;if(Array.isArray(t[0])){f=Gr(t);var c=1;for(var l=1;l<f.length;++l){c*=f[l]}r.dimension=c;u=qr(t,f,r.dtype);s(r,u,n);if(o){r.persistentData=u}else{Pe.freeType(u)}}else if(typeof t[0]==="number"){r.dimension=i;var v=Pe.allocType(r.dtype,t.length);Jr(v,t);s(r,v,n);if(o){r.persistentData=v}else{Pe.freeType(v)}}else if(e(t[0])){r.dimension=t[0].length;r.dtype=a||Qr(t[0])||Yr;u=qr(t,[t.length,t[0].length],r.dtype);s(r,u,n);if(o){r.persistentData=u}else{Pe.freeType(u)}}else{te.raise("invalid buffer data")}}}else if(e(t)){r.dtype=a||Qr(t);r.dimension=i;s(r,t,n);if(o){r.persistentData=new Uint8Array(new Uint8Array(t.buffer))}}else if(xr(t)){f=t.shape;var p=t.stride;var h=t.offset;var d=0;var m=0;var g=0;var b=0;if(f.length===1){d=f[0];m=1;g=p[0];b=0}else if(f.length===2){d=f[0];m=f[1];g=p[0];b=p[1]}else{te.raise("invalid shape")}r.dtype=a||Qr(t.data)||Yr;r.dimension=m;var y=Pe.allocType(r.dtype,d*m);Kr(y,t.data,d,m,g,b,h);s(r,y,n);if(o){r.persistentData=y}else{Pe.freeType(y)}}else{te.raise("invalid buffer data")}}function p(e){t.bufferCount--;for(var n=0;n<a.state.length;++n){var i=a.state[n];if(i.buffer===e){r.disableVertexAttribArray(n);i.buffer=null}}var f=e.buffer;te(f,"buffer must not be deleted already");r.deleteBuffer(f);e.buffer=null;delete o[e.id]}function h(a,i,u,c){t.bufferCount++;var l=new f(i);o[l.id]=l;function s(t){var a=$r;var i=null;var o=0;var f=0;var u=1;if(Array.isArray(t)||e(t)||xr(t)){i=t}else if(typeof t==="number"){o=t|0}else if(t){te.type(t,"object","buffer arguments must be an object, a number or an array");if("data"in t){te(i===null||Array.isArray(i)||e(i)||xr(i),"invalid data for buffer");i=t.data}if("usage"in t){te.parameter(t.usage,Ur,"invalid buffer usage");a=Ur[t.usage]}if("type"in t){te.parameter(t.type,Rr,"invalid buffer type");f=Rr[t.type]}if("dimension"in t){te.type(t.dimension,"number","invalid dimension");u=t.dimension|0}if("length"in t){te.nni(o,"buffer length must be a nonnegative integer");o=t.length|0}}l.bind();if(!i){if(o)r.bufferData(l.type,o,a);l.dtype=f||Hr;l.usage=a;l.dimension=u;l.byteLength=o}else{v(l,i,a,f,u,c)}if(n.profile){l.stats.size=l.byteLength*Xr[l.dtype]}return s}function h(e,t){te(t+e.byteLength<=l.byteLength,"invalid buffer subdata call, buffer is too small. "+" Can't write data of size "+e.byteLength+" starting from offset "+t+" to a buffer of size "+l.byteLength);r.bufferSubData(l.type,t,e)}function d(r,t){var n=(t||0)|0;var a;l.bind();if(e(r)){h(r,n)}else if(Array.isArray(r)){if(r.length>0){if(typeof r[0]==="number"){var i=Pe.allocType(l.dtype,r.length);Jr(i,r);h(i,n);Pe.freeType(i)}else if(Array.isArray(r[0])||e(r[0])){a=Gr(r);var o=qr(r,a,l.dtype);h(o,n);Pe.freeType(o)}else{te.raise("invalid buffer data")}}}else if(xr(r)){a=r.shape;var f=r.stride;var u=0;var c=0;var v=0;var p=0;if(a.length===1){u=a[0];c=1;v=f[0];p=0}else if(a.length===2){u=a[0];c=a[1];v=f[0];p=f[1]}else{te.raise("invalid shape")}var d=Array.isArray(r.data)?l.dtype:Qr(r.data);var m=Pe.allocType(d,u*c);Kr(m,r.data,u,c,v,p,r.offset);h(m,n);Pe.freeType(m)}else{te.raise("invalid data for buffer subdata")}return s}if(!u){s(a)}s._reglType="buffer";s._buffer=l;s.subdata=d;if(n.profile){s.stats=l.stats}s.destroy=function(){p(l)};return s}function d(){wr(o).forEach(function(e){e.buffer=r.createBuffer();r.bindBuffer(e.type,e.buffer);r.bufferData(e.type,e.persistentData||e.byteLength,e.usage)})}if(n.profile){t.getTotalBufferSize=function(){var e=0;Object.keys(o).forEach(function(r){e+=o[r].stats.size});return e}}return{create:h,createStream:c,destroyStream:l,clear:function(){wr(o).forEach(p);u.forEach(p)},getBuffer:function(e){if(e&&e._buffer instanceof f){return e._buffer}return null},restore:d,_initBuffer:v}}var et=0;var rt=0;var tt=1;var nt=1;var at=4;var it=4;var ot={points:et,point:rt,lines:tt,line:nt,triangles:at,triangle:it,"line loop":2,"line strip":3,"triangle strip":5,"triangle fan":6};var ft=0;var ut=1;var ct=4;var lt=5120;var st=5121;var vt=5122;var pt=5123;var ht=5124;var dt=5125;var mt=34963;var gt=35040;var bt=35044;function yt(r,t,n,a){var i={};var o=0;var f={uint8:st,uint16:pt};if(t.oes_element_index_uint){f.uint32=dt}function u(e){this.id=o++;i[this.id]=this;this.buffer=e;this.primType=ct;this.vertCount=0;this.type=0}u.prototype.bind=function(){this.buffer.bind()};var c=[];function l(e){var r=c.pop();if(!r){r=new u(n.create(null,mt,true,false)._buffer)}v(r,e,gt,-1,-1,0,0);return r}function s(e){c.push(e)}function v(a,i,o,f,u,c,l){a.buffer.bind();if(i){var s=l;if(!l&&(!e(i)||xr(i)&&!e(i.data))){s=t.oes_element_index_uint?dt:pt}n._initBuffer(a.buffer,i,o,s,3)}else{r.bufferData(mt,c,o);a.buffer.dtype=v||st;a.buffer.usage=o;a.buffer.dimension=3;a.buffer.byteLength=c}var v=l;if(!l){switch(a.buffer.dtype){case st:case lt:v=st;break;case pt:case vt:v=pt;break;case dt:case ht:v=dt;break;default:te.raise("unsupported type for element array")}a.buffer.dtype=v}a.type=v;te(v!==dt||!!t.oes_element_index_uint,"32 bit element buffers not supported, enable oes_element_index_uint first");var p=u;if(p<0){p=a.buffer.byteLength;if(v===pt){p>>=1}else if(v===dt){p>>=2}}a.vertCount=p;var h=f;if(f<0){h=ct;var d=a.buffer.dimension;if(d===1)h=ft;if(d===2)h=ut;if(d===3)h=ct}a.primType=h}function p(e){a.elementsCount--;te(e.buffer!==null,"must not double destroy elements");delete i[e.id];e.buffer.destroy();e.buffer=null}function h(r,t){var i=n.create(null,mt,true);var o=new u(i._buffer);a.elementsCount++;function c(r){if(!r){i();o.primType=ct;o.vertCount=0;o.type=st}else if(typeof r==="number"){i(r);o.primType=ct;o.vertCount=r|0;o.type=st}else{var t=null;var n=bt;var a=-1;var u=-1;var l=0;var s=0;if(Array.isArray(r)||e(r)||xr(r)){t=r}else{te.type(r,"object","invalid arguments for elements");if("data"in r){t=r.data;te(Array.isArray(t)||e(t)||xr(t),"invalid data for element buffer")}if("usage"in r){te.parameter(r.usage,Ur,"invalid element buffer usage");n=Ur[r.usage]}if("primitive"in r){te.parameter(r.primitive,ot,"invalid element buffer primitive");a=ot[r.primitive]}if("count"in r){te(typeof r.count==="number"&&r.count>=0,"invalid vertex count for elements");u=r.count|0}if("type"in r){te.parameter(r.type,f,"invalid buffer type");s=f[r.type]}if("length"in r){l=r.length|0}else{l=u;if(s===pt||s===vt){l*=2}else if(s===dt||s===ht){l*=4}}}v(o,t,n,a,u,l,s)}return c}c(r);c._reglType="elements";c._elements=o;c.subdata=function(e,r){i.subdata(e,r);return c};c.destroy=function(){p(o)};return c}return{create:h,createStream:l,destroyStream:s,getElements:function(e){if(typeof e==="function"&&e._elements instanceof u){return e._elements}return null},clear:function(){wr(i).forEach(p)}}}var _t=new Float32Array(1);var xt=new Uint32Array(_t.buffer);var wt=5123;function kt(e){var r=Pe.allocType(wt,e.length);for(var t=0;t<e.length;++t){if(isNaN(e[t])){r[t]=65535}else if(e[t]===Infinity){r[t]=31744}else if(e[t]===-Infinity){r[t]=64512}else{_t[0]=e[t];var n=xt[0];var a=n>>>31<<15;var i=(n<<1>>>24)-127;var o=n>>13&(1<<10)-1;if(i<-24){r[t]=a}else if(i<-14){var f=-14-i;r[t]=a+(o+(1<<10)>>f)}else if(i>15){r[t]=a+31744}else{r[t]=a+(i+15<<10)+o}}}return r}function zt(r){return Array.isArray(r)||e(r)}var Et=function(e){return!(e&e-1)&&!!e};var St=34467;var At=3553;var jt=34067;var Ot=34069;var Tt=6408;var Mt=6406;var Dt=6407;var It=6409;var Ct=6410;var Pt=32854;var Ft=32855;var Nt=36194;var Lt=32819;var Rt=32820;var Bt=33635;var Wt=34042;var Ut=6402;var qt=34041;var Gt=35904;var $t=35906;var Vt=36193;var Ht=33776;var Yt=33777;var Xt=33778;var Qt=33779;var Jt=35986;var Kt=35987;var Zt=34798;var en=35840;var rn=35841;var tn=35842;var nn=35843;var an=36196;var on=5121;var fn=5123;var un=5125;var cn=5126;var ln=10242;var sn=10243;var vn=10497;var pn=33071;var hn=33648;var dn=10240;var mn=10241;var gn=9728;var bn=9729;var yn=9984;var _n=9985;var xn=9986;var wn=9987;var kn=33170;var zn=4352;var En=4353;var Sn=4354;var An=34046;var jn=3317;var On=37440;var Tn=37441;var Mn=37443;var Dn=37444;var In=33984;var Cn=[yn,xn,_n,wn];var Pn=[0,It,Ct,Dt,Tt];var Fn={};Fn[It]=Fn[Mt]=Fn[Ut]=1;Fn[qt]=Fn[Ct]=2;Fn[Dt]=Fn[Gt]=3;Fn[Tt]=Fn[$t]=4;function Nn(e){return"[object "+e+"]"}var Ln=Nn("HTMLCanvasElement");var Rn=Nn("CanvasRenderingContext2D");var Bn=Nn("ImageBitmap");var Wn=Nn("HTMLImageElement");var Un=Nn("HTMLVideoElement");var qn=Object.keys(Tr).concat([Ln,Rn,Bn,Wn,Un]);var Gn=[];Gn[on]=1;Gn[cn]=4;Gn[Vt]=2;Gn[fn]=2;Gn[un]=4;var $n=[];$n[Pt]=2;$n[Ft]=2;$n[Nt]=2;$n[qt]=4;$n[Ht]=.5;$n[Yt]=.5;$n[Xt]=1;$n[Qt]=1;$n[Jt]=.5;$n[Kt]=1;$n[Zt]=1;$n[en]=.5;$n[rn]=.25;$n[tn]=.5;$n[nn]=.25;$n[an]=.5;function Vn(e){return Array.isArray(e)&&(e.length===0||typeof e[0]==="number")}function Hn(e){if(!Array.isArray(e)){return false}var r=e.length;if(r===0||!zt(e[0])){return false}return true}function Yn(e){return Object.prototype.toString.call(e)}function Xn(e){return Yn(e)===Ln}function Qn(e){return Yn(e)===Rn}function Jn(e){return Yn(e)===Bn}function Kn(e){return Yn(e)===Wn}function Zn(e){return Yn(e)===Un}function ea(e){if(!e){return false}var r=Yn(e);if(qn.indexOf(r)>=0){return true}return Vn(e)||Hn(e)||xr(e)}function ra(e){return Tr[Object.prototype.toString.call(e)]|0}function ta(e,r){var t=r.length;switch(e.type){case on:case fn:case un:case cn:var n=Pe.allocType(e.type,t);n.set(r);e.data=n;break;case Vt:e.data=kt(r);break;default:te.raise("unsupported texture type, must specify a typed array")}}function na(e,r){return Pe.allocType(e.type===Vt?cn:e.type,r)}function aa(e,r){if(e.type===Vt){e.data=kt(r);Pe.freeType(r)}else{e.data=r}}function ia(e,r,t,n,a,i){var o=e.width;var f=e.height;var u=e.channels;var c=o*f*u;var l=na(e,c);var s=0;for(var v=0;v<f;++v){for(var p=0;p<o;++p){for(var h=0;h<u;++h){l[s++]=r[t*p+n*v+a*h+i]}}}aa(e,l)}function oa(e,r,t,n,a,i){var o;if(typeof $n[e]!=="undefined"){o=$n[e]}else{o=Fn[e]*Gn[r]}if(i){o*=6}if(a){var f=0;var u=t;while(u>=1){f+=o*u*u;u/=2}return f}else{return o*t*n}}function fa(t,n,a,i,o,f,u){var c={"don't care":zn,"dont care":zn,nice:Sn,fast:En};var l={repeat:vn,clamp:pn,mirror:hn};var s={nearest:gn,linear:bn};var v=r({mipmap:wn,"nearest mipmap nearest":yn,"linear mipmap nearest":_n,"nearest mipmap linear":xn,"linear mipmap linear":wn},s);var p={none:0,browser:Dn};var h={uint8:on,rgba4:Lt,rgb565:Bt,"rgb5 a1":Rt};var d={alpha:Mt,luminance:It,"luminance alpha":Ct,rgb:Dt,rgba:Tt,rgba4:Pt,"rgb5 a1":Ft,rgb565:Nt};var m={};if(n.ext_srgb){d.srgb=Gt;d.srgba=$t}if(n.oes_texture_float){h.float32=h.float=cn}if(n.oes_texture_half_float){h["float16"]=h["half float"]=Vt}if(n.webgl_depth_texture){r(d,{depth:Ut,"depth stencil":qt});r(h,{uint16:fn,uint32:un,"depth stencil":Wt})}if(n.webgl_compressed_texture_s3tc){r(m,{"rgb s3tc dxt1":Ht,"rgba s3tc dxt1":Yt,"rgba s3tc dxt3":Xt,"rgba s3tc dxt5":Qt})}if(n.webgl_compressed_texture_atc){r(m,{"rgb atc":Jt,"rgba atc explicit alpha":Kt,"rgba atc interpolated alpha":Zt})}if(n.webgl_compressed_texture_pvrtc){r(m,{"rgb pvrtc 4bppv1":en,"rgb pvrtc 2bppv1":rn,"rgba pvrtc 4bppv1":tn,"rgba pvrtc 2bppv1":nn})}if(n.webgl_compressed_texture_etc1){m["rgb etc1"]=an}var g=Array.prototype.slice.call(t.getParameter(St));Object.keys(m).forEach(function(e){var r=m[e];if(g.indexOf(r)>=0){d[e]=r}});var b=Object.keys(d);a.textureFormats=b;var y=[];Object.keys(d).forEach(function(e){var r=d[e];y[r]=e});var _=[];Object.keys(h).forEach(function(e){var r=h[e];_[r]=e});var x=[];Object.keys(s).forEach(function(e){var r=s[e];x[r]=e});var w=[];Object.keys(v).forEach(function(e){var r=v[e];w[r]=e});var k=[];Object.keys(l).forEach(function(e){var r=l[e];k[r]=e});var z=b.reduce(function(e,r){var t=d[r];if(t===It||t===Mt||t===It||t===Ct||t===Ut||t===qt){e[t]=t}else if(t===Ft||r.indexOf("rgba")>=0){e[t]=Tt}else{e[t]=Dt}return e},{});function E(){this.internalformat=Tt;this.format=Tt;this.type=on;this.compressed=false;this.premultiplyAlpha=false;this.flipY=false;this.unpackAlignment=1;this.colorSpace=Dn;this.width=0;this.height=0;this.channels=0}function S(e,r){e.internalformat=r.internalformat;e.format=r.format;e.type=r.type;e.compressed=r.compressed;e.premultiplyAlpha=r.premultiplyAlpha;e.flipY=r.flipY;e.unpackAlignment=r.unpackAlignment;e.colorSpace=r.colorSpace;e.width=r.width;e.height=r.height;e.channels=r.channels}function A(e,r){if(typeof r!=="object"||!r){return}if("premultiplyAlpha"in r){te.type(r.premultiplyAlpha,"boolean","invalid premultiplyAlpha");e.premultiplyAlpha=r.premultiplyAlpha}if("flipY"in r){te.type(r.flipY,"boolean","invalid texture flip");e.flipY=r.flipY}if("alignment"in r){te.oneOf(r.alignment,[1,2,4,8],"invalid texture unpack alignment");e.unpackAlignment=r.alignment}if("colorSpace"in r){te.parameter(r.colorSpace,p,"invalid colorSpace");e.colorSpace=p[r.colorSpace]}if("type"in r){var t=r.type;te(n.oes_texture_float||!(t==="float"||t==="float32"),"you must enable the OES_texture_float extension in order to use floating point textures.");te(n.oes_texture_half_float||!(t==="half float"||t==="float16"),"you must enable the OES_texture_half_float extension in order to use 16-bit floating point textures.");te(n.webgl_depth_texture||!(t==="uint16"||t==="uint32"||t==="depth stencil"),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures.");te.parameter(t,h,"invalid texture type");e.type=h[t]}var i=e.width;var o=e.height;var f=e.channels;var u=false;if("shape"in r){te(Array.isArray(r.shape)&&r.shape.length>=2,"shape must be an array");i=r.shape[0];o=r.shape[1];if(r.shape.length===3){f=r.shape[2];te(f>0&&f<=4,"invalid number of channels");u=true}te(i>=0&&i<=a.maxTextureSize,"invalid width");te(o>=0&&o<=a.maxTextureSize,"invalid height")}else{if("radius"in r){i=o=r.radius;te(i>=0&&i<=a.maxTextureSize,"invalid radius")}if("width"in r){i=r.width;te(i>=0&&i<=a.maxTextureSize,"invalid width")}if("height"in r){o=r.height;te(o>=0&&o<=a.maxTextureSize,"invalid height")}if("channels"in r){f=r.channels;te(f>0&&f<=4,"invalid number of channels");u=true}}e.width=i|0;e.height=o|0;e.channels=f|0;var c=false;if("format"in r){var l=r.format;te(n.webgl_depth_texture||!(l==="depth"||l==="depth stencil"),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures.");te.parameter(l,d,"invalid texture format");var s=e.internalformat=d[l];e.format=z[s];if(l in h){if(!("type"in r)){e.type=h[l]}}if(l in m){e.compressed=true}c=true}if(!u&&c){e.channels=Fn[e.format]}else if(u&&!c){if(e.channels!==Pn[e.format]){e.format=e.internalformat=Pn[e.channels]}}else if(c&&u){te(e.channels===Fn[e.format],"number of channels inconsistent with specified format")}}function j(e){t.pixelStorei(On,e.flipY);t.pixelStorei(Tn,e.premultiplyAlpha);t.pixelStorei(Mn,e.colorSpace);t.pixelStorei(jn,e.unpackAlignment)}function O(){E.call(this);this.xOffset=0;this.yOffset=0;this.data=null;this.needsFree=false;this.element=null;this.needsCopy=false}function T(r,t){var n=null;if(ea(t)){n=t}else if(t){te.type(t,"object","invalid pixel data type");A(r,t);if("x"in t){r.xOffset=t.x|0}if("y"in t){r.yOffset=t.y|0}if(ea(t.data)){n=t.data}}te(!r.compressed||n instanceof Uint8Array,"compressed texture data must be stored in a uint8array");if(t.copy){te(!n,"can not specify copy and data field for the same texture");var i=o.viewportWidth;var f=o.viewportHeight;r.width=r.width||i-r.xOffset;r.height=r.height||f-r.yOffset;r.needsCopy=true;te(r.xOffset>=0&&r.xOffset<i&&r.yOffset>=0&&r.yOffset<f&&r.width>0&&r.width<=i&&r.height>0&&r.height<=f,"copy texture read out of bounds")}else if(!n){r.width=r.width||1;r.height=r.height||1;r.channels=r.channels||4}else if(e(n)){r.channels=r.channels||4;r.data=n;if(!("type"in t)&&r.type===on){r.type=ra(n)}}else if(Vn(n)){r.channels=r.channels||4;ta(r,n);r.alignment=1;r.needsFree=true}else if(xr(n)){var u=n.data;if(!Array.isArray(u)&&r.type===on){r.type=ra(u)}var c=n.shape;var l=n.stride;var s,v,p,h,d,m;if(c.length===3){p=c[2];m=l[2]}else{te(c.length===2,"invalid ndarray pixel data, must be 2 or 3D");p=1;m=1}s=c[0];v=c[1];h=l[0];d=l[1];r.alignment=1;r.width=s;r.height=v;r.channels=p;r.format=r.internalformat=Pn[p];r.needsFree=true;ia(r,u,h,d,m,n.offset)}else if(Xn(n)||Qn(n)){if(Xn(n)){r.element=n}else{r.element=n.canvas}r.width=r.element.width;r.height=r.element.height;r.channels=4}else if(Jn(n)){r.element=n;r.width=n.width;r.height=n.height;r.channels=4}else if(Kn(n)){r.element=n;r.width=n.naturalWidth;r.height=n.naturalHeight;r.channels=4}else if(Zn(n)){r.element=n;r.width=n.videoWidth;r.height=n.videoHeight;r.channels=4}else if(Hn(n)){var g=r.width||n[0].length;var b=r.height||n.length;var y=r.channels;if(zt(n[0][0])){y=y||n[0][0].length}else{y=y||1}var _=kr.shape(n);var x=1;for(var w=0;w<_.length;++w){x*=_[w]}var k=na(r,x);kr.flatten(n,_,"",k);aa(r,k);r.alignment=1;r.width=g;r.height=b;r.channels=y;r.format=r.internalformat=Pn[y];r.needsFree=true}if(r.type===cn){te(a.extensions.indexOf("oes_texture_float")>=0,"oes_texture_float extension not enabled")}else if(r.type===Vt){te(a.extensions.indexOf("oes_texture_half_float")>=0,"oes_texture_half_float extension not enabled")}}function M(e,r,n){var a=e.element;var o=e.data;var f=e.internalformat;var u=e.format;var c=e.type;var l=e.width;var s=e.height;var v=e.channels;j(e);if(a){t.texImage2D(r,n,u,u,c,a)}else if(e.compressed){t.compressedTexImage2D(r,n,f,l,s,0,o)}else if(e.needsCopy){i();t.copyTexImage2D(r,n,u,e.xOffset,e.yOffset,l,s,0)}else{var p=!o;if(p){o=Pe.zero.allocType(c,l*s*v)}t.texImage2D(r,n,u,l,s,0,u,c,o);if(p&&o){Pe.zero.freeType(o)}}}function D(e,r,n,a,o){var f=e.element;var u=e.data;var c=e.internalformat;var l=e.format;var s=e.type;var v=e.width;var p=e.height;j(e);if(f){t.texSubImage2D(r,o,n,a,l,s,f)}else if(e.compressed){t.compressedTexSubImage2D(r,o,n,a,c,v,p,u)}else if(e.needsCopy){i();t.copyTexSubImage2D(r,o,n,a,e.xOffset,e.yOffset,v,p)}else{t.texSubImage2D(r,o,n,a,v,p,l,s,u)}}var I=[];function C(){return I.pop()||new O}function P(e){if(e.needsFree){Pe.freeType(e.data)}O.call(e);I.push(e)}function F(){E.call(this);this.genMipmaps=false;this.mipmapHint=zn;this.mipmask=0;this.images=Array(16)}function N(e,r,t){var n=e.images[0]=C();e.mipmask=1;n.width=e.width=r;n.height=e.height=t;n.channels=e.channels=4}function L(e,r){var t=null;if(ea(r)){t=e.images[0]=C();S(t,e);T(t,r);e.mipmask=1}else{A(e,r);if(Array.isArray(r.mipmap)){var n=r.mipmap;for(var a=0;a<n.length;++a){t=e.images[a]=C();S(t,e);t.width>>=a;t.height>>=a;T(t,n[a]);e.mipmask|=1<<a}}else{t=e.images[0]=C();S(t,e);T(t,r);e.mipmask=1}}S(e,e.images[0]);if(e.compressed&&e.internalformat===Ht||e.internalformat===Yt||e.internalformat===Xt||e.internalformat===Qt){te(e.width%4===0&&e.height%4===0,"for compressed texture formats, mipmap level 0 must have width and height that are a multiple of 4")}}function R(e,r){var t=e.images;for(var n=0;n<t.length;++n){if(!t[n]){return}M(t[n],r,n)}}var B=[];function W(){var e=B.pop()||new F;E.call(e);e.mipmask=0;for(var r=0;r<16;++r){e.images[r]=null}return e}function U(e){var r=e.images;for(var t=0;t<r.length;++t){if(r[t]){P(r[t])}r[t]=null}B.push(e)}function q(){this.minFilter=gn;this.magFilter=gn;this.wrapS=pn;this.wrapT=pn;this.anisotropic=1;this.genMipmaps=false;this.mipmapHint=zn}function G(e,r){if("min"in r){var t=r.min;te.parameter(t,v);e.minFilter=v[t];if(Cn.indexOf(e.minFilter)>=0&&!("faces"in r)){e.genMipmaps=true}}if("mag"in r){var n=r.mag;te.parameter(n,s);e.magFilter=s[n]}var i=e.wrapS;var o=e.wrapT;if("wrap"in r){var f=r.wrap;if(typeof f==="string"){te.parameter(f,l);i=o=l[f]}else if(Array.isArray(f)){te.parameter(f[0],l);te.parameter(f[1],l);i=l[f[0]];o=l[f[1]]}}else{if("wrapS"in r){var u=r.wrapS;te.parameter(u,l);i=l[u]}if("wrapT"in r){var p=r.wrapT;te.parameter(p,l);o=l[p]}}e.wrapS=i;e.wrapT=o;if("anisotropic"in r){var h=r.anisotropic;te(typeof h==="number"&&h>=1&&h<=a.maxAnisotropic,"aniso samples must be between 1 and ");e.anisotropic=r.anisotropic}if("mipmap"in r){var d=false;switch(typeof r.mipmap){case"string":te.parameter(r.mipmap,c,"invalid mipmap hint");e.mipmapHint=c[r.mipmap];e.genMipmaps=true;d=true;break;case"boolean":d=e.genMipmaps=r.mipmap;break;case"object":te(Array.isArray(r.mipmap),"invalid mipmap type");e.genMipmaps=false;d=true;break;default:te.raise("invalid mipmap type")}if(d&&!("min"in r)){e.minFilter=yn}}}function $(e,r){t.texParameteri(r,mn,e.minFilter);t.texParameteri(r,dn,e.magFilter);t.texParameteri(r,ln,e.wrapS);t.texParameteri(r,sn,e.wrapT);if(n.ext_texture_filter_anisotropic){t.texParameteri(r,An,e.anisotropic)}if(e.genMipmaps){t.hint(kn,e.mipmapHint);t.generateMipmap(r)}}var V=0;var H={};var Y=a.maxTextureUnits;var X=Array(Y).map(function(){return null});function Q(e){E.call(this);this.mipmask=0;this.internalformat=Tt;this.id=V++;this.refCount=1;this.target=e;this.texture=t.createTexture();this.unit=-1;this.bindCount=0;this.texInfo=new q;if(u.profile){this.stats={size:0}}}function J(e){t.activeTexture(In);t.bindTexture(e.target,e.texture)}function K(){var e=X[0];if(e){t.bindTexture(e.target,e.texture)}else{t.bindTexture(At,null)}}function Z(e){var r=e.texture;te(r,"must not double destroy texture");var n=e.unit;var a=e.target;if(n>=0){t.activeTexture(In+n);t.bindTexture(a,null);X[n]=null}t.deleteTexture(r);e.texture=null;e.params=null;e.pixels=null;e.refCount=0;delete H[e.id];f.textureCount--}r(Q.prototype,{bind:function(){var e=this;e.bindCount+=1;var r=e.unit;if(r<0){for(var n=0;n<Y;++n){var a=X[n];if(a){if(a.bindCount>0){continue}a.unit=-1}X[n]=e;r=n;break}if(r>=Y){te.raise("insufficient number of texture units")}if(u.profile&&f.maxTextureUnits<r+1){f.maxTextureUnits=r+1}e.unit=r;t.activeTexture(In+r);t.bindTexture(e.target,e.texture)}return r},unbind:function(){this.bindCount-=1},decRef:function(){if(--this.refCount<=0){Z(this)}}});function ee(e,r){var n=new Q(At);H[n.id]=n;f.textureCount++;function i(e,r){var t=n.texInfo;q.call(t);var o=W();if(typeof e==="number"){if(typeof r==="number"){N(o,e|0,r|0)}else{N(o,e|0,e|0)}}else if(e){te.type(e,"object","invalid arguments to regl.texture");G(t,e);L(o,e)}else{N(o,1,1)}if(t.genMipmaps){o.mipmask=(o.width<<1)-1}n.mipmask=o.mipmask;S(n,o);te.texture2D(t,o,a);n.internalformat=o.internalformat;i.width=o.width;i.height=o.height;J(n);R(o,At);$(t,At);K();U(o);if(u.profile){n.stats.size=oa(n.internalformat,n.type,o.width,o.height,t.genMipmaps,false)}i.format=y[n.internalformat];i.type=_[n.type];i.mag=x[t.magFilter];i.min=w[t.minFilter];i.wrapS=k[t.wrapS];i.wrapT=k[t.wrapT];return i}function o(e,r,t,a){te(!!e,"must specify image data");var o=r|0;var f=t|0;var u=a|0;var c=C();S(c,n);c.width=0;c.height=0;T(c,e);c.width=c.width||(n.width>>u)-o;c.height=c.height||(n.height>>u)-f;te(n.type===c.type&&n.format===c.format&&n.internalformat===c.internalformat,"incompatible format for texture.subimage");te(o>=0&&f>=0&&o+c.width<=n.width&&f+c.height<=n.height,"texture.subimage write out of bounds");te(n.mipmask&1<<u,"missing mipmap data");te(c.data||c.element||c.needsCopy,"missing image data");J(n);D(c,At,o,f,u);K();P(c);return i}function c(e,r){var a=e|0;var o=r|0||a;if(a===n.width&&o===n.height){return i}i.width=n.width=a;i.height=n.height=o;J(n);var f;var c=n.channels;var l=n.type;for(var s=0;n.mipmask>>s;++s){var v=a>>s;var p=o>>s;if(!v||!p)break;f=Pe.zero.allocType(l,v*p*c);t.texImage2D(At,s,n.format,v,p,0,n.format,n.type,f);if(f)Pe.zero.freeType(f)}K();if(u.profile){n.stats.size=oa(n.internalformat,n.type,a,o,false,false)}return i}i(e,r);i.subimage=o;i.resize=c;i._reglType="texture2d";i._texture=n;if(u.profile){i.stats=n.stats}i.destroy=function(){n.decRef()};return i}function re(e,r,n,i,o,c){var l=new Q(jt);H[l.id]=l;f.cubeCount++;var s=new Array(6);function v(e,r,t,n,i,o){var f;var c=l.texInfo;q.call(c);for(f=0;f<6;++f){s[f]=W()}if(typeof e==="number"||!e){var p=e|0||1;for(f=0;f<6;++f){N(s[f],p,p)}}else if(typeof e==="object"){if(r){L(s[0],e);L(s[1],r);L(s[2],t);L(s[3],n);L(s[4],i);L(s[5],o)}else{G(c,e);A(l,e);if("faces"in e){var h=e.faces;te(Array.isArray(h)&&h.length===6,"cube faces must be a length 6 array");for(f=0;f<6;++f){te(typeof h[f]==="object"&&!!h[f],"invalid input for cube map face");S(s[f],l);L(s[f],h[f])}}else{for(f=0;f<6;++f){L(s[f],e)}}}}else{te.raise("invalid arguments to cube map")}S(l,s[0]);if(!a.npotTextureCube){te(Et(l.width)&&Et(l.height),"your browser does not support non power or two texture dimensions")}if(c.genMipmaps){l.mipmask=(s[0].width<<1)-1}else{l.mipmask=s[0].mipmask}te.textureCube(l,c,s,a);l.internalformat=s[0].internalformat;v.width=s[0].width;v.height=s[0].height;J(l);for(f=0;f<6;++f){R(s[f],Ot+f)}$(c,jt);K();if(u.profile){l.stats.size=oa(l.internalformat,l.type,v.width,v.height,c.genMipmaps,true)}v.format=y[l.internalformat];v.type=_[l.type];v.mag=x[c.magFilter];v.min=w[c.minFilter];v.wrapS=k[c.wrapS];v.wrapT=k[c.wrapT];for(f=0;f<6;++f){U(s[f])}return v}function p(e,r,t,n,a){te(!!r,"must specify image data");te(typeof e==="number"&&e===(e|0)&&e>=0&&e<6,"invalid face");var i=t|0;var o=n|0;var f=a|0;var u=C();S(u,l);u.width=0;u.height=0;T(u,r);u.width=u.width||(l.width>>f)-i;u.height=u.height||(l.height>>f)-o;te(l.type===u.type&&l.format===u.format&&l.internalformat===u.internalformat,"incompatible format for texture.subimage");te(i>=0&&o>=0&&i+u.width<=l.width&&o+u.height<=l.height,"texture.subimage write out of bounds");te(l.mipmask&1<<f,"missing mipmap data");te(u.data||u.element||u.needsCopy,"missing image data");J(l);D(u,Ot+e,i,o,f);K();P(u);return v}function h(e){var r=e|0;if(r===l.width){return}v.width=l.width=r;v.height=l.height=r;J(l);for(var n=0;n<6;++n){for(var a=0;l.mipmask>>a;++a){t.texImage2D(Ot+n,a,l.format,r>>a,r>>a,0,l.format,l.type,null)}}K();if(u.profile){l.stats.size=oa(l.internalformat,l.type,v.width,v.height,false,true)}return v}v(e,r,n,i,o,c);v.subimage=p;v.resize=h;v._reglType="textureCube";v._texture=l;if(u.profile){v.stats=l.stats}v.destroy=function(){l.decRef()};return v}function ne(){for(var e=0;e<Y;++e){t.activeTexture(In+e);t.bindTexture(At,null);X[e]=null}wr(H).forEach(Z);f.cubeCount=0;f.textureCount=0}if(u.profile){f.getTotalTextureSize=function(){var e=0;Object.keys(H).forEach(function(r){e+=H[r].stats.size});return e}}function ae(){for(var e=0;e<Y;++e){var r=X[e];if(r){r.bindCount=0;r.unit=-1;X[e]=null}}wr(H).forEach(function(e){e.texture=t.createTexture();t.bindTexture(e.target,e.texture);for(var r=0;r<32;++r){if((e.mipmask&1<<r)===0){continue}if(e.target===At){t.texImage2D(At,r,e.internalformat,e.width>>r,e.height>>r,0,e.internalformat,e.type,null)}else{for(var n=0;n<6;++n){t.texImage2D(Ot+n,r,e.internalformat,e.width>>r,e.height>>r,0,e.internalformat,e.type,null)}}}$(e.texInfo,e.target)})}return{create2D:ee,createCube:re,clear:ne,getTexture:function(e){return null},restore:ae}}var ua=36161;var ca=32854;var la=32855;var sa=36194;var va=33189;var pa=36168;var ha=34041;var da=35907;var ma=34836;var ga=34842;var ba=34843;var ya=[];ya[ca]=2;ya[la]=2;ya[sa]=2;ya[va]=2;ya[pa]=1;ya[ha]=4;ya[da]=4;ya[ma]=16;ya[ga]=8;ya[ba]=6;function _a(e,r,t){return ya[e]*r*t}var xa=function(e,r,t,n,a){var i={rgba4:ca,rgb565:sa,"rgb5 a1":la,depth:va,stencil:pa,"depth stencil":ha};if(r.ext_srgb){i["srgba"]=da}if(r.ext_color_buffer_half_float){i["rgba16f"]=ga;i["rgb16f"]=ba}if(r.webgl_color_buffer_float){i["rgba32f"]=ma}var o=[];Object.keys(i).forEach(function(e){var r=i[e];o[r]=e});var f=0;var u={};function c(e){this.id=f++;this.refCount=1;this.renderbuffer=e;this.format=ca;this.width=0;this.height=0;if(a.profile){this.stats={size:0}}}c.prototype.decRef=function(){if(--this.refCount<=0){l(this)}};function l(r){var t=r.renderbuffer;te(t,"must not double destroy renderbuffer");e.bindRenderbuffer(ua,null);e.deleteRenderbuffer(t);r.renderbuffer=null;r.refCount=0;delete u[r.id];n.renderbufferCount--}function s(r,f){var l=new c(e.createRenderbuffer());u[l.id]=l;n.renderbufferCount++;function s(r,n){var f=0;var u=0;var c=ca;if(typeof r==="object"&&r){var v=r;if("shape"in v){var p=v.shape;te(Array.isArray(p)&&p.length>=2,"invalid renderbuffer shape");f=p[0]|0;u=p[1]|0}else{if("radius"in v){f=u=v.radius|0}if("width"in v){f=v.width|0}if("height"in v){u=v.height|0}}if("format"in v){te.parameter(v.format,i,"invalid renderbuffer format");c=i[v.format]}}else if(typeof r==="number"){f=r|0;if(typeof n==="number"){u=n|0}else{u=f}}else if(!r){f=u=1}else{te.raise("invalid arguments to renderbuffer constructor")}te(f>0&&u>0&&f<=t.maxRenderbufferSize&&u<=t.maxRenderbufferSize,"invalid renderbuffer size");if(f===l.width&&u===l.height&&c===l.format){return}s.width=l.width=f;s.height=l.height=u;l.format=c;e.bindRenderbuffer(ua,l.renderbuffer);e.renderbufferStorage(ua,c,f,u);te(e.getError()===0,"invalid render buffer format");if(a.profile){l.stats.size=_a(l.format,l.width,l.height)}s.format=o[l.format];return s}function v(r,n){var i=r|0;var o=n|0||i;if(i===l.width&&o===l.height){return s}te(i>0&&o>0&&i<=t.maxRenderbufferSize&&o<=t.maxRenderbufferSize,"invalid renderbuffer size");s.width=l.width=i;s.height=l.height=o;e.bindRenderbuffer(ua,l.renderbuffer);e.renderbufferStorage(ua,l.format,i,o);te(e.getError()===0,"invalid render buffer format");if(a.profile){l.stats.size=_a(l.format,l.width,l.height)}return s}s(r,f);s.resize=v;s._reglType="renderbuffer";s._renderbuffer=l;if(a.profile){s.stats=l.stats}s.destroy=function(){l.decRef()};return s}if(a.profile){n.getTotalRenderbufferSize=function(){var e=0;Object.keys(u).forEach(function(r){e+=u[r].stats.size});return e}}function v(){wr(u).forEach(function(r){r.renderbuffer=e.createRenderbuffer();e.bindRenderbuffer(ua,r.renderbuffer);e.renderbufferStorage(ua,r.format,r.width,r.height)});e.bindRenderbuffer(ua,null)}return{create:s,clear:function(){wr(u).forEach(l)},restore:v}};var wa=36160;var ka=36161;var za=3553;var Ea=34069;var Sa=36064;var Aa=36096;var ja=36128;var Oa=33306;var Ta=36053;var Ma=36054;var Da=36055;var Ia=36057;var Ca=36061;var Pa=36193;var Fa=5121;var Na=5126;var La=6407;var Ra=6408;var Ba=6402;var Wa=[La,Ra];var Ua=[];Ua[Ra]=4;Ua[La]=3;var qa=[];qa[Fa]=1;qa[Na]=4;qa[Pa]=2;var Ga=32854;var $a=32855;var Va=36194;var Ha=33189;var Ya=36168;var Xa=34041;var Qa=35907;var Ja=34836;var Ka=34842;var Za=34843;var ei=[Ga,$a,Va,Qa,Ka,Za,Ja];var ri={};ri[Ta]="complete";ri[Ma]="incomplete attachment";ri[Ia]="incomplete dimensions";ri[Da]="incomplete, missing attachment";ri[Ca]="unsupported";function ti(e,t,n,a,i,o){var f={cur:null,next:null,dirty:false,setFBO:null};var u=["rgba"];var c=["rgba4","rgb565","rgb5 a1"];if(t.ext_srgb){c.push("srgba")}if(t.ext_color_buffer_half_float){c.push("rgba16f","rgb16f")}if(t.webgl_color_buffer_float){c.push("rgba32f")}var l=["uint8"];if(t.oes_texture_half_float){l.push("half float","float16")}if(t.oes_texture_float){l.push("float","float32")}function s(e,r,t){this.target=e;this.texture=r;this.renderbuffer=t;var n=0;var a=0;if(r){n=r.width;a=r.height}else if(t){n=t.width;a=t.height}this.width=n;this.height=a}function v(e){if(e){if(e.texture){e.texture._texture.decRef()}if(e.renderbuffer){e.renderbuffer._renderbuffer.decRef()}}}function p(e,r,t){if(!e){return}if(e.texture){var n=e.texture._texture;var a=Math.max(1,n.width);var i=Math.max(1,n.height);te(a===r&&i===t,"inconsistent width/height for supplied texture");n.refCount+=1}else{var o=e.renderbuffer._renderbuffer;te(o.width===r&&o.height===t,"inconsistent width/height for renderbuffer");o.refCount+=1}}function h(r,t){if(t){if(t.texture){e.framebufferTexture2D(wa,r,t.target,t.texture._texture.texture,0)}else{e.framebufferRenderbuffer(wa,r,ka,t.renderbuffer._renderbuffer.renderbuffer)}}}function d(e){var r=za;var t=null;var n=null;var a=e;if(typeof e==="object"){a=e.data;if("target"in e){r=e.target|0}}te.type(a,"function","invalid attachment data");var i=a._reglType;if(i==="texture2d"){t=a;te(r===za)}else if(i==="textureCube"){t=a;te(r>=Ea&&r<Ea+6,"invalid cube map target")}else if(i==="renderbuffer"){n=a;r=ka}else{te.raise("invalid regl object for attachment")}return new s(r,t,n)}function m(e,r,t,n,o){if(t){var f=a.create2D({width:e,height:r,format:n,type:o});f._texture.refCount=0;return new s(za,f,null)}else{var u=i.create({width:e,height:r,format:n});u._renderbuffer.refCount=0;return new s(ka,null,u)}}function g(e){return e&&(e.texture||e.renderbuffer)}function b(e,r,t){if(e){if(e.texture){e.texture.resize(r,t)}else if(e.renderbuffer){e.renderbuffer.resize(r,t)}e.width=r;e.height=t}}var y=0;var _={};function x(){this.id=y++;_[this.id]=this;this.framebuffer=e.createFramebuffer();this.width=0;this.height=0;this.colorAttachments=[];this.depthAttachment=null;this.stencilAttachment=null;this.depthStencilAttachment=null}function w(e){e.colorAttachments.forEach(v);v(e.depthAttachment);v(e.stencilAttachment);v(e.depthStencilAttachment)}function k(r){var t=r.framebuffer;te(t,"must not double destroy framebuffer");e.deleteFramebuffer(t);r.framebuffer=null;o.framebufferCount--;delete _[r.id]}function z(r){var t;e.bindFramebuffer(wa,r.framebuffer);var a=r.colorAttachments;for(t=0;t<a.length;++t){h(Sa+t,a[t])}for(t=a.length;t<n.maxColorAttachments;++t){e.framebufferTexture2D(wa,Sa+t,za,null,0)}e.framebufferTexture2D(wa,Oa,za,null,0);e.framebufferTexture2D(wa,Aa,za,null,0);e.framebufferTexture2D(wa,ja,za,null,0);h(Aa,r.depthAttachment);h(ja,r.stencilAttachment);h(Oa,r.depthStencilAttachment);var i=e.checkFramebufferStatus(wa);if(!e.isContextLost()&&i!==Ta){te.raise("framebuffer configuration not supported, status = "+ri[i])}e.bindFramebuffer(wa,f.next?f.next.framebuffer:null);f.cur=f.next;e.getError()}function E(e,a){var i=new x;o.framebufferCount++;function s(e,r){var a;te(f.next!==i,"can not update framebuffer which is currently in use");var o=0;var v=0;var h=true;var b=true;var y=null;var _=true;var x="rgba";var k="uint8";var E=1;var S=null;var A=null;var j=null;var O=false;if(typeof e==="number"){o=e|0;v=r|0||o}else if(!e){o=v=1}else{te.type(e,"object","invalid arguments for framebuffer");var T=e;if("shape"in T){var M=T.shape;te(Array.isArray(M)&&M.length>=2,"invalid shape for framebuffer");o=M[0];v=M[1]}else{if("radius"in T){o=v=T.radius}if("width"in T){o=T.width}if("height"in T){v=T.height}}if("color"in T||"colors"in T){y=T.color||T.colors;if(Array.isArray(y)){te(y.length===1||t.webgl_draw_buffers,"multiple render targets not supported")}}if(!y){if("colorCount"in T){E=T.colorCount|0;te(E>0,"invalid color buffer count")}if("colorTexture"in T){_=!!T.colorTexture;x="rgba4"}if("colorType"in T){k=T.colorType;if(!_){if(k==="half float"||k==="float16"){te(t.ext_color_buffer_half_float,"you must enable EXT_color_buffer_half_float to use 16-bit render buffers");x="rgba16f"}else if(k==="float"||k==="float32"){te(t.webgl_color_buffer_float,"you must enable WEBGL_color_buffer_float in order to use 32-bit floating point renderbuffers");x="rgba32f"}}else{te(t.oes_texture_float||!(k==="float"||k==="float32"),"you must enable OES_texture_float in order to use floating point framebuffer objects");te(t.oes_texture_half_float||!(k==="half float"||k==="float16"),"you must enable OES_texture_half_float in order to use 16-bit floating point framebuffer objects")}te.oneOf(k,l,"invalid color type")}if("colorFormat"in T){x=T.colorFormat;if(u.indexOf(x)>=0){_=true}else if(c.indexOf(x)>=0){_=false}else{if(_){te.oneOf(T.colorFormat,u,"invalid color format for texture")}else{te.oneOf(T.colorFormat,c,"invalid color format for renderbuffer")}}}}if("depthTexture"in T||"depthStencilTexture"in T){O=!!(T.depthTexture||T.depthStencilTexture);te(!O||t.webgl_depth_texture,"webgl_depth_texture extension not supported")}if("depth"in T){if(typeof T.depth==="boolean"){h=T.depth}else{S=T.depth;b=false}}if("stencil"in T){if(typeof T.stencil==="boolean"){b=T.stencil}else{A=T.stencil;h=false}}if("depthStencil"in T){if(typeof T.depthStencil==="boolean"){h=b=T.depthStencil}else{j=T.depthStencil;h=false;b=false}}}var D=null;var I=null;var C=null;var P=null;if(Array.isArray(y)){D=y.map(d)}else if(y){D=[d(y)]}else{D=new Array(E);for(a=0;a<E;++a){D[a]=m(o,v,_,x,k)}}te(t.webgl_draw_buffers||D.length<=1,"you must enable the WEBGL_draw_buffers extension in order to use multiple color buffers.");te(D.length<=n.maxColorAttachments,"too many color attachments, not supported");o=o||D[0].width;v=v||D[0].height;if(S){I=d(S)}else if(h&&!b){I=m(o,v,O,"depth","uint32")}if(A){C=d(A)}else if(b&&!h){C=m(o,v,false,"stencil","uint8")}if(j){P=d(j)}else if(!S&&!A&&b&&h){P=m(o,v,O,"depth stencil","depth stencil")}te(!!S+!!A+!!j<=1,"invalid framebuffer configuration, can specify exactly one depth/stencil attachment");var F=null;for(a=0;a<D.length;++a){p(D[a],o,v);te(!D[a]||D[a].texture&&Wa.indexOf(D[a].texture._texture.format)>=0||D[a].renderbuffer&&ei.indexOf(D[a].renderbuffer._renderbuffer.format)>=0,"framebuffer color attachment "+a+" is invalid");if(D[a]&&D[a].texture){var N=Ua[D[a].texture._texture.format]*qa[D[a].texture._texture.type];if(F===null){F=N}else{te(F===N,"all color attachments much have the same number of bits per pixel.")}}}p(I,o,v);te(!I||I.texture&&I.texture._texture.format===Ba||I.renderbuffer&&I.renderbuffer._renderbuffer.format===Ha,"invalid depth attachment for framebuffer object");p(C,o,v);te(!C||C.renderbuffer&&C.renderbuffer._renderbuffer.format===Ya,"invalid stencil attachment for framebuffer object");p(P,o,v);te(!P||P.texture&&P.texture._texture.format===Xa||P.renderbuffer&&P.renderbuffer._renderbuffer.format===Xa,"invalid depth-stencil attachment for framebuffer object");w(i);i.width=o;i.height=v;i.colorAttachments=D;i.depthAttachment=I;i.stencilAttachment=C;i.depthStencilAttachment=P;s.color=D.map(g);s.depth=g(I);s.stencil=g(C);s.depthStencil=g(P);s.width=i.width;s.height=i.height;z(i);return s}function v(e,r){te(f.next!==i,"can not resize a framebuffer which is currently in use");var t=Math.max(e|0,1);var n=Math.max(r|0||t,1);if(t===i.width&&n===i.height){return s}var a=i.colorAttachments;for(var o=0;o<a.length;++o){b(a[o],t,n)}b(i.depthAttachment,t,n);b(i.stencilAttachment,t,n);b(i.depthStencilAttachment,t,n);i.width=s.width=t;i.height=s.height=n;z(i);return s}s(e,a);return r(s,{resize:v,_reglType:"framebuffer",_framebuffer:i,destroy:function(){k(i);w(i)},use:function(e){f.setFBO({framebuffer:s},e)}})}function S(e){var i=Array(6);function o(e){var n;te(i.indexOf(f.next)<0,"can not update framebuffer which is currently in use");var c={color:null};var s=0;var v=null;var p="rgba";var h="uint8";var d=1;if(typeof e==="number"){s=e|0}else if(!e){s=1}else{te.type(e,"object","invalid arguments for framebuffer");var m=e;if("shape"in m){var g=m.shape;te(Array.isArray(g)&&g.length>=2,"invalid shape for framebuffer");te(g[0]===g[1],"cube framebuffer must be square");s=g[0]}else{if("radius"in m){s=m.radius|0}if("width"in m){s=m.width|0;if("height"in m){te(m.height===s,"must be square")}}else if("height"in m){s=m.height|0}}if("color"in m||"colors"in m){v=m.color||m.colors;if(Array.isArray(v)){te(v.length===1||t.webgl_draw_buffers,"multiple render targets not supported")}}if(!v){if("colorCount"in m){d=m.colorCount|0;te(d>0,"invalid color buffer count")}if("colorType"in m){te.oneOf(m.colorType,l,"invalid color type");h=m.colorType}if("colorFormat"in m){p=m.colorFormat;te.oneOf(m.colorFormat,u,"invalid color format for texture")}}if("depth"in m){c.depth=m.depth}if("stencil"in m){c.stencil=m.stencil}if("depthStencil"in m){c.depthStencil=m.depthStencil}}var b;if(v){if(Array.isArray(v)){b=[];for(n=0;n<v.length;++n){b[n]=v[n]}}else{b=[v]}}else{b=Array(d);var y={radius:s,format:p,type:h};for(n=0;n<d;++n){b[n]=a.createCube(y)}}c.color=Array(b.length);for(n=0;n<b.length;++n){var _=b[n];te(typeof _==="function"&&_._reglType==="textureCube","invalid cube map");s=s||_.width;te(_.width===s&&_.height===s,"invalid cube map shape");c.color[n]={target:Ea,data:b[n]}}for(n=0;n<6;++n){for(var x=0;x<b.length;++x){c.color[x].target=Ea+n}if(n>0){c.depth=i[0].depth;c.stencil=i[0].stencil;c.depthStencil=i[0].depthStencil}if(i[n]){i[n](c)}else{i[n]=E(c)}}return r(o,{width:s,height:s,color:b})}function c(e){var r;var t=e|0;te(t>0&&t<=n.maxCubeMapSize,"invalid radius for cube fbo");if(t===o.width){return o}var a=o.color;for(r=0;r<a.length;++r){a[r].resize(t)}for(r=0;r<6;++r){i[r].resize(t)}o.width=o.height=t;return o}o(e);return r(o,{faces:i,resize:c,_reglType:"framebufferCube",destroy:function(){i.forEach(function(e){e.destroy()})}})}function A(){f.cur=null;f.next=null;f.dirty=true;wr(_).forEach(function(r){r.framebuffer=e.createFramebuffer();z(r)})}return r(f,{getFramebuffer:function(e){if(typeof e==="function"&&e._reglType==="framebuffer"){var r=e._framebuffer;if(r instanceof x){return r}}return null},create:E,createCube:S,clear:function(){wr(_).forEach(k)},restore:A})}var ni=5126;function ai(){this.state=0;this.x=0;this.y=0;this.z=0;this.w=0;this.buffer=null;this.size=0;this.normalized=false;this.type=ni;this.offset=0;this.stride=0;this.divisor=0}function ii(e,r,t,n){var a=t.maxAttributes;var i=new Array(a);for(var o=0;o<a;++o){i[o]=new ai}return{Record:ai,scope:{},state:i}}var oi=35632;var fi=35633;var ui=35718;var ci=35721;function li(e,r,t,n){var a={};var i={};function o(e,r,t,n){this.name=e;this.id=r;this.location=t;this.info=n}function f(e,r){for(var t=0;t<e.length;++t){if(e[t].id===r.id){e[t].location=r.location;return}}e.push(r)}function u(t,n,o){var f=t===oi?a:i;var u=f[n];if(!u){var c=r.str(n);u=e.createShader(t);e.shaderSource(u,c);e.compileShader(u);te.shaderError(e,u,c,t,o);f[n]=u}return u}var c={};var l=[];var s=0;function v(e,r){this.id=s++;this.fragId=e;this.vertId=r;this.program=null;this.uniforms=[];this.attributes=[];if(n.profile){this.stats={uniformsCount:0,attributesCount:0}}}function p(t,a){var i,c;var l=u(oi,t.fragId);var s=u(fi,t.vertId);var v=t.program=e.createProgram();e.attachShader(v,l);e.attachShader(v,s);e.linkProgram(v);te.linkError(e,v,r.str(t.fragId),r.str(t.vertId),a);var p=e.getProgramParameter(v,ui);if(n.profile){t.stats.uniformsCount=p}var h=t.uniforms;for(i=0;i<p;++i){c=e.getActiveUniform(v,i);if(c){if(c.size>1){for(var d=0;d<c.size;++d){var m=c.name.replace("[0]","["+d+"]");f(h,new o(m,r.id(m),e.getUniformLocation(v,m),c))}}else{f(h,new o(c.name,r.id(c.name),e.getUniformLocation(v,c.name),c))}}}var g=e.getProgramParameter(v,ci);if(n.profile){t.stats.attributesCount=g}var b=t.attributes;for(i=0;i<g;++i){c=e.getActiveAttrib(v,i);if(c){f(b,new o(c.name,r.id(c.name),e.getAttribLocation(v,c.name),c))}}}if(n.profile){t.getMaxUniformsCount=function(){var e=0;l.forEach(function(r){if(r.stats.uniformsCount>e){e=r.stats.uniformsCount}});return e};t.getMaxAttributesCount=function(){var e=0;l.forEach(function(r){if(r.stats.attributesCount>e){e=r.stats.attributesCount}});return e}}function h(){a={};i={};for(var e=0;e<l.length;++e){p(l[e])}}return{clear:function(){var r=e.deleteShader.bind(e);wr(a).forEach(r);a={};wr(i).forEach(r);i={};l.forEach(function(r){e.deleteProgram(r.program)});l.length=0;c={};t.shaderCount=0},program:function(e,r,n){te.command(e>=0,"missing vertex shader",n);te.command(r>=0,"missing fragment shader",n);var a=c[r];if(!a){a=c[r]={}}var i=a[e];if(!i){i=new v(r,e);t.shaderCount++;p(i,n);a[e]=i;l.push(i)}return i},restore:h,shader:u,frag:-1,vert:-1}}var si=6408;var vi=5121;var pi=3333;var hi=5126;function di(r,t,n,a,i,o,f){function u(u){var c;if(t.next===null){te(i.preserveDrawingBuffer,'you must create a webgl context with "preserveDrawingBuffer":true in order to read pixels from the drawing buffer');c=vi}else{te(t.next.colorAttachments[0].texture!==null,"You cannot read from a renderbuffer");c=t.next.colorAttachments[0].texture._texture.type;if(o.oes_texture_float){te(c===vi||c===hi,"Reading from a framebuffer is only allowed for the types 'uint8' and 'float'");if(c===hi){te(f.readFloat,"Reading 'float' values is not permitted in your browser. For a fallback, please see: https://www.npmjs.com/package/glsl-read-float")}}else{te(c===vi,"Reading from a framebuffer is only allowed for the type 'uint8'")}}var l=0;var s=0;var v=a.framebufferWidth;var p=a.framebufferHeight;var h=null;if(e(u)){h=u}else if(u){te.type(u,"object","invalid arguments to regl.read()");l=u.x|0;s=u.y|0;te(l>=0&&l<a.framebufferWidth,"invalid x offset for regl.read");te(s>=0&&s<a.framebufferHeight,"invalid y offset for regl.read");v=(u.width||a.framebufferWidth-l)|0;p=(u.height||a.framebufferHeight-s)|0;h=u.data||null}if(h){if(c===vi){te(h instanceof Uint8Array,"buffer must be 'Uint8Array' when reading from a framebuffer of type 'uint8'")}else if(c===hi){te(h instanceof Float32Array,"buffer must be 'Float32Array' when reading from a framebuffer of type 'float'")}}te(v>0&&v+l<=a.framebufferWidth,"invalid width for read pixels");te(p>0&&p+s<=a.framebufferHeight,"invalid height for read pixels");n();var d=v*p*4;if(!h){if(c===vi){h=new Uint8Array(d)}else if(c===hi){h=h||new Float32Array(d)}}te.isTypedArray(h,"data buffer for regl.read() must be a typedarray");te(h.byteLength>=d,"data buffer for regl.read() too small");r.pixelStorei(pi,4);r.readPixels(l,s,v,p,si,c,h);return h}function c(e){var r;t.setFBO({framebuffer:e.framebuffer},function(){r=u(e)});return r}function l(e){if(!e||!("framebuffer"in e)){return u(e)}else{return c(e)}}return l}function mi(e){return Array.prototype.slice.call(e)}function gi(e){return mi(e).join("")}function bi(){var e=0;var t=[];var n=[];function a(r){for(var a=0;a<n.length;++a){if(n[a]===r){return t[a]}}var i="g"+e++;t.push(i);n.push(r);return i}function i(){var t=[];function n(){t.push.apply(t,mi(arguments))}var a=[];function i(){var r="v"+e++;a.push(r);if(arguments.length>0){t.push(r,"=");t.push.apply(t,mi(arguments));t.push(";")}return r}return r(n,{def:i,toString:function(){return gi([a.length>0?"var "+a+";":"",gi(t)])}})}function o(){var e=i();var t=i();var n=e.toString;var a=t.toString;function o(r,n){t(r,n,"=",e.def(r,n),";")}return r(function(){e.apply(e,mi(arguments))},{def:e.def,entry:e,exit:t,save:o,set:function(r,t,n){o(r,t);e(r,t,"=",n,";")},toString:function(){return n()+a()}})}function f(){var e=gi(arguments);var t=o();var n=o();var a=t.toString;var i=n.toString;return r(t,{then:function(){t.apply(t,mi(arguments));return this},else:function(){n.apply(n,mi(arguments));return this},toString:function(){var r=i();if(r){r="else{"+r+"}"}return gi(["if(",e,"){",a(),"}",r])}})}var u=i();var c={};function l(e,t){var n=[];function a(){var e="a"+n.length;n.push(e);return e}t=t||0;for(var i=0;i<t;++i){a()}var f=o();var u=f.toString;var l=c[e]=r(f,{arg:a,toString:function(){return gi(["function(",n.join(),"){",u(),"}"])}});return l}function s(){var e=['"use strict";',u,"return {"];Object.keys(c).forEach(function(r){e.push('"',r,'":',c[r].toString(),",")});e.push("}");var r=gi(e).replace(/;/g,";\n").replace(/}/g,"}\n").replace(/{/g,"{\n");var a=Function.apply(null,t.concat(r));return a.apply(null,n)}return{global:u,link:a,block:i,proc:l,scope:o,cond:f,compile:s}}var yi="xyzw".split("");var _i=5121;var xi=1;var wi=2;var ki=0;var zi=1;var Ei=2;var Si=3;var Ai=4;var ji="dither";var Oi="blend.enable";var Ti="blend.color";var Mi="blend.equation";var Di="blend.func";var Ii="depth.enable";var Ci="depth.func";var Pi="depth.range";var Fi="depth.mask";var Ni="colorMask";var Li="cull.enable";var Ri="cull.face";var Bi="frontFace";var Wi="lineWidth";var Ui="polygonOffset.enable";var qi="polygonOffset.offset";var Gi="sample.alpha";var $i="sample.enable";var Vi="sample.coverage";var Hi="stencil.enable";var Yi="stencil.mask";var Xi="stencil.func";var Qi="stencil.opFront";var Ji="stencil.opBack";var Ki="scissor.enable";var Zi="scissor.box";var eo="viewport";var ro="profile";var to="framebuffer";var no="vert";var ao="frag";var io="elements";var oo="primitive";var fo="count";var uo="offset";var co="instances";var lo="Width";var so="Height";var vo=to+lo;var po=to+so;var ho=eo+lo;var mo=eo+so;var go="drawingBuffer";var bo=go+lo;var yo=go+so;var _o=[Di,Mi,Xi,Qi,Ji,Vi,eo,Zi,qi];var xo=34962;var wo=34963;var ko=35632;var zo=35633;var Eo=3553;var So=34067;var Ao=2884;var jo=3042;var Oo=3024;var To=2960;var Mo=2929;var Do=3089;var Io=32823;var Co=32926;var Po=32928;var Fo=5126;var No=35664;var Lo=35665;var Ro=35666;var Bo=5124;var Wo=35667;var Uo=35668;var qo=35669;var Go=35670;var $o=35671;var Vo=35672;var Ho=35673;var Yo=35674;var Xo=35675;var Qo=35676;var Jo=35678;var Ko=35680;var Zo=4;var ef=1028;var rf=1029;var tf=2304;var nf=2305;var af=32775;var of=32776;var ff=519;var uf=7680;var cf=0;var lf=1;var sf=32774;var vf=513;var pf=36160;var hf=36064;var df={0:0,1:1,zero:0,one:1,"src color":768,"one minus src color":769,"src alpha":770,"one minus src alpha":771,"dst color":774,"one minus dst color":775,"dst alpha":772,"one minus dst alpha":773,"constant color":32769,"one minus constant color":32770,"constant alpha":32771,"one minus constant alpha":32772,"src alpha saturate":776};var mf=["constant color, constant alpha","one minus constant color, constant alpha","constant color, one minus constant alpha","one minus constant color, one minus constant alpha","constant alpha, constant color","constant alpha, one minus constant color","one minus constant alpha, constant color","one minus constant alpha, one minus constant color"];var gf={never:512,less:513,"<":513,equal:514,"=":514,"==":514,"===":514,lequal:515,"<=":515,greater:516,">":516,notequal:517,"!=":517,"!==":517,gequal:518,">=":518,always:519};var bf={0:0,zero:0,keep:7680,replace:7681,increment:7682,decrement:7683,"increment wrap":34055,"decrement wrap":34056,invert:5386};var yf={frag:ko,vert:zo};var _f={cw:tf,ccw:nf};function xf(r){return Array.isArray(r)||e(r)||xr(r)}function wf(e){return e.sort(function(e,r){if(e===eo){return-1}else if(r===eo){return 1}return e<r?-1:1})}function kf(e,r,t,n){this.thisDep=e;this.contextDep=r;this.propDep=t;this.append=n}function zf(e){return e&&!(e.thisDep||e.contextDep||e.propDep)}function Ef(e){return new kf(false,false,false,e)}function Sf(e,r){var t=e.type;if(t===ki){var n=e.data.length;return new kf(true,n>=1,n>=2,r)}else if(t===Ai){var a=e.data;return new kf(a.thisDep,a.contextDep,a.propDep,r)}else{return new kf(t===Si,t===Ei,t===zi,r)}}var Af=new kf(false,false,false,function(){});function jf(e,r,t,n,a,i,o,f,u,c,l,s,v,p,h){var d=c.Record;var m={add:32774,subtract:32778,"reverse subtract":32779};if(t.ext_blend_minmax){m.min=af;m.max=of}var g=t.angle_instanced_arrays;var b=t.webgl_draw_buffers;var y={dirty:true,profile:h.profile};var _={};var x=[];var w={};var k={};function z(e){return e.replace(".","_")}function E(e,r,t){var n=z(e);x.push(e);_[n]=y[n]=!!t;w[n]=r}function S(e,r,t){var n=z(e);x.push(e);if(Array.isArray(t)){y[n]=t.slice();_[n]=t.slice()}else{y[n]=_[n]=t}k[n]=r}E(ji,Oo);E(Oi,jo);S(Ti,"blendColor",[0,0,0,0]);S(Mi,"blendEquationSeparate",[sf,sf]);S(Di,"blendFuncSeparate",[lf,cf,lf,cf]);E(Ii,Mo,true);S(Ci,"depthFunc",vf);S(Pi,"depthRange",[0,1]);S(Fi,"depthMask",true);S(Ni,Ni,[true,true,true,true]);E(Li,Ao);S(Ri,"cullFace",rf);S(Bi,Bi,nf);S(Wi,Wi,1);E(Ui,Io);S(qi,"polygonOffset",[0,0]);E(Gi,Co);E($i,Po);S(Vi,"sampleCoverage",[1,false]);E(Hi,To);S(Yi,"stencilMask",-1);S(Xi,"stencilFunc",[ff,0,-1]);S(Qi,"stencilOpSeparate",[ef,uf,uf,uf]);S(Ji,"stencilOpSeparate",[rf,uf,uf,uf]);E(Ki,Do);S(Zi,"scissor",[0,0,e.drawingBufferWidth,e.drawingBufferHeight]);S(eo,eo,[0,0,e.drawingBufferWidth,e.drawingBufferHeight]);var A={gl:e,context:v,strings:r,next:_,current:y,draw:s,elements:i,buffer:a,shader:l,attributes:c.state,uniforms:u,framebuffer:f,extensions:t,timer:p,isBufferArgs:xf};var j={primTypes:ot,compareFuncs:gf,blendFuncs:df,blendEquations:m,stencilOps:bf,glTypes:Rr,orientationType:_f};te.optional(function(){A.isArrayLike=zt});if(b){j.backBuffer=[rf];j.drawBuffer=ze(n.maxDrawbuffers,function(e){if(e===0){return[0]}return ze(e,function(e){return hf+e})})}var O=0;function T(){var e=bi();var t=e.link;var n=e.global;e.id=O++;e.batchId="0";var a=t(A);var i=e.shared={props:"a0"};Object.keys(A).forEach(function(e){i[e]=n.def(a,".",e)});te.optional(function(){e.CHECK=t(te);e.commandStr=te.guessCommand();e.command=t(e.commandStr);e.assert=function(e,r,n){e("if(!(",r,"))",this.CHECK,".commandRaise(",t(n),",",this.command,");")};j.invalidBlendCombinations=mf});var o=e.next={};var f=e.current={};Object.keys(k).forEach(function(e){if(Array.isArray(y[e])){o[e]=n.def(i.next,".",e);f[e]=n.def(i.current,".",e)}});var u=e.constants={};Object.keys(j).forEach(function(e){u[e]=n.def(JSON.stringify(j[e]))});e.invoke=function(r,n){switch(n.type){case ki:var a=["this",i.context,i.props,e.batchId];return r.def(t(n.data),".call(",a.slice(0,Math.max(n.data.length+1,4)),")");case zi:return r.def(i.props,n.data);case Ei:return r.def(i.context,n.data);case Si:return r.def("this",n.data);case Ai:n.data.append(e,r);return n.data.ref}};e.attribCache={};var l={};e.scopeAttrib=function(e){var n=r.id(e);if(n in l){return l[n]}var a=c.scope[n];if(!a){a=c.scope[n]=new d}var i=l[n]=t(a);return i};return e}function M(e){var r=e.static;var t=e.dynamic;var n;if(ro in r){var a=!!r[ro];n=Ef(function(e,r){return a});n.enable=a}else if(ro in t){var i=t[ro];n=Sf(i,function(e,r){return e.invoke(r,i)})}return n}function D(e,r){var t=e.static;var n=e.dynamic;if(to in t){var a=t[to];if(a){a=f.getFramebuffer(a);te.command(a,"invalid framebuffer object");return Ef(function(e,r){var t=e.link(a);var n=e.shared;r.set(n.framebuffer,".next",t);var i=n.context;r.set(i,"."+vo,t+".width");r.set(i,"."+po,t+".height");return t})}else{return Ef(function(e,r){var t=e.shared;r.set(t.framebuffer,".next","null");var n=t.context;r.set(n,"."+vo,n+"."+bo);r.set(n,"."+po,n+"."+yo);return"null"})}}else if(to in n){var i=n[to];return Sf(i,function(e,r){var t=e.invoke(r,i);var n=e.shared;var a=n.framebuffer;var o=r.def(a,".getFramebuffer(",t,")");te.optional(function(){e.assert(r,"!"+t+"||"+o,"invalid framebuffer object")});r.set(a,".next",o);var f=n.context;r.set(f,"."+vo,o+"?"+o+".width:"+f+"."+bo);r.set(f,"."+po,o+"?"+o+".height:"+f+"."+yo);return o})}else{return null}}function I(e,r,t){var n=e.static;var a=e.dynamic;function i(e){if(e in n){var i=n[e];te.commandType(i,"object","invalid "+e,t.commandStr);var o=true;var f=i.x|0;var u=i.y|0;var c,l;if("width"in i){c=i.width|0;te.command(c>=0,"invalid "+e,t.commandStr)}else{o=false}if("height"in i){l=i.height|0;te.command(l>=0,"invalid "+e,t.commandStr)}else{o=false}return new kf(!o&&r&&r.thisDep,!o&&r&&r.contextDep,!o&&r&&r.propDep,function(e,r){var t=e.shared.context;var n=c;if(!("width"in i)){n=r.def(t,".",vo,"-",f)}var a=l;if(!("height"in i)){a=r.def(t,".",po,"-",u)}return[f,u,n,a]})}else if(e in a){var s=a[e];var v=Sf(s,function(r,t){var n=r.invoke(t,s);te.optional(function(){r.assert(t,n+"&&typeof "+n+'==="object"',"invalid "+e)});var a=r.shared.context;var i=t.def(n,".x|0");var o=t.def(n,".y|0");var f=t.def('"width" in ',n,"?",n,".width|0:","(",a,".",vo,"-",i,")");var u=t.def('"height" in ',n,"?",n,".height|0:","(",a,".",po,"-",o,")");te.optional(function(){r.assert(t,f+">=0&&"+u+">=0","invalid "+e)});return[i,o,f,u]});if(r){v.thisDep=v.thisDep||r.thisDep;v.contextDep=v.contextDep||r.contextDep;v.propDep=v.propDep||r.propDep}return v}else if(r){return new kf(r.thisDep,r.contextDep,r.propDep,function(e,r){var t=e.shared.context;return[0,0,r.def(t,".",vo),r.def(t,".",po)]})}else{return null}}var o=i(eo);if(o){var f=o;o=new kf(o.thisDep,o.contextDep,o.propDep,function(e,r){var t=f.append(e,r);var n=e.shared.context;r.set(n,"."+ho,t[2]);r.set(n,"."+mo,t[3]);return t})}return{viewport:o,scissor_box:i(Zi)}}function C(e){var t=e.static;var n=e.dynamic;function a(e){if(e in t){var a=r.id(t[e]);te.optional(function(){l.shader(yf[e],a,te.guessCommand())});var i=Ef(function(){return a});i.id=a;return i}else if(e in n){var o=n[e];return Sf(o,function(r,t){var n=r.invoke(t,o);var a=t.def(r.shared.strings,".id(",n,")");te.optional(function(){t(r.shared.shader,".shader(",yf[e],",",a,",",r.command,");")});return a})}return null}var i=a(ao);var o=a(no);var f=null;var u;if(zf(i)&&zf(o)){f=l.program(o.id,i.id);u=Ef(function(e,r){return e.link(f)})}else{u=new kf(i&&i.thisDep||o&&o.thisDep,i&&i.contextDep||o&&o.contextDep,i&&i.propDep||o&&o.propDep,function(e,r){var t=e.shared.shader;var n;if(i){n=i.append(e,r)}else{n=r.def(t,".",ao)}var a;if(o){a=o.append(e,r)}else{a=r.def(t,".",no)}var f=t+".program("+a+","+n;te.optional(function(){f+=","+e.command});return r.def(f+")")})}return{frag:i,vert:o,progVar:u,program:f}}function P(e,r){var t=e.static;var n=e.dynamic;function a(){if(io in t){var e=t[io];if(xf(e)){e=i.getElements(i.create(e,true))}else if(e){e=i.getElements(e);te.command(e,"invalid elements",r.commandStr)}var a=Ef(function(r,t){if(e){var n=r.link(e);r.ELEMENTS=n;return n}r.ELEMENTS=null;return null});a.value=e;return a}else if(io in n){var o=n[io];return Sf(o,function(e,r){var t=e.shared;var n=t.isBufferArgs;var a=t.elements;var i=e.invoke(r,o);var f=r.def("null");var u=r.def(n,"(",i,")");var c=e.cond(u).then(f,"=",a,".createStream(",i,");").else(f,"=",a,".getElements(",i,");");te.optional(function(){e.assert(c.else,"!"+i+"||"+f,"invalid elements")});r.entry(c);r.exit(e.cond(u).then(a,".destroyStream(",f,");"));e.ELEMENTS=f;return f})}return null}var o=a();function f(){if(oo in t){var e=t[oo];te.commandParameter(e,ot,"invalid primitve",r.commandStr);return Ef(function(r,t){return ot[e]})}else if(oo in n){var a=n[oo];return Sf(a,function(e,r){var t=e.constants.primTypes;var n=e.invoke(r,a);te.optional(function(){e.assert(r,n+" in "+t,"invalid primitive, must be one of "+Object.keys(ot))});return r.def(t,"[",n,"]")})}else if(o){if(zf(o)){if(o.value){return Ef(function(e,r){return r.def(e.ELEMENTS,".primType")})}else{return Ef(function(){return Zo})}}else{return new kf(o.thisDep,o.contextDep,o.propDep,function(e,r){var t=e.ELEMENTS;return r.def(t,"?",t,".primType:",Zo)})}}return null}function u(e,a){if(e in t){var i=t[e]|0;te.command(!a||i>=0,"invalid "+e,r.commandStr);return Ef(function(e,r){if(a){e.OFFSET=i}return i})}else if(e in n){var f=n[e];return Sf(f,function(r,t){var n=r.invoke(t,f);if(a){r.OFFSET=n;te.optional(function(){r.assert(t,n+">=0","invalid "+e)})}return n})}else if(a&&o){return Ef(function(e,r){e.OFFSET="0";return 0})}return null}var c=u(uo,true);function l(){if(fo in t){var e=t[fo]|0;te.command(typeof e==="number"&&e>=0,"invalid vertex count",r.commandStr);return Ef(function(){return e})}else if(fo in n){var a=n[fo];return Sf(a,function(e,r){var t=e.invoke(r,a);te.optional(function(){e.assert(r,"typeof "+t+'==="number"&&'+t+">=0&&"+t+"===("+t+"|0)","invalid vertex count")});return t})}else if(o){if(zf(o)){if(o){if(c){return new kf(c.thisDep,c.contextDep,c.propDep,function(e,r){var t=r.def(e.ELEMENTS,".vertCount-",e.OFFSET);te.optional(function(){e.assert(r,t+">=0","invalid vertex offset/element buffer too small")});return t})}else{return Ef(function(e,r){return r.def(e.ELEMENTS,".vertCount")})}}else{var i=Ef(function(){return-1});te.optional(function(){i.MISSING=true});return i}}else{var f=new kf(o.thisDep||c.thisDep,o.contextDep||c.contextDep,o.propDep||c.propDep,function(e,r){var t=e.ELEMENTS;if(e.OFFSET){return r.def(t,"?",t,".vertCount-",e.OFFSET,":-1")}return r.def(t,"?",t,".vertCount:-1")});te.optional(function(){f.DYNAMIC=true});return f}}return null}return{elements:o,primitive:f(),count:l(),instances:u(co,false),offset:c}}function F(e,r){var t=e.static;var a=e.dynamic;var i={};x.forEach(function(e){var o=z(e);function f(r,n){if(e in t){var f=r(t[e]);i[o]=Ef(function(){return f})}else if(e in a){var u=a[e];i[o]=Sf(u,function(e,r){return n(e,r,e.invoke(r,u))})}}switch(e){case Li:case Oi:case ji:case Hi:case Ii:case Ki:case Ui:case Gi:case $i:case Fi:return f(function(t){te.commandType(t,"boolean",e,r.commandStr);return t},function(r,t,n){te.optional(function(){r.assert(t,"typeof "+n+'==="boolean"',"invalid flag "+e,r.commandStr)});return n});case Ci:return f(function(t){te.commandParameter(t,gf,"invalid "+e,r.commandStr);return gf[t]},function(r,t,n){var a=r.constants.compareFuncs;te.optional(function(){r.assert(t,n+" in "+a,"invalid "+e+", must be one of "+Object.keys(gf))});return t.def(a,"[",n,"]")});case Pi:return f(function(e){te.command(zt(e)&&e.length===2&&typeof e[0]==="number"&&typeof e[1]==="number"&&e[0]<=e[1],"depth range is 2d array",r.commandStr);return e},function(e,r,t){te.optional(function(){e.assert(r,e.shared.isArrayLike+"("+t+")&&"+t+".length===2&&"+"typeof "+t+'[0]==="number"&&'+"typeof "+t+'[1]==="number"&&'+t+"[0]<="+t+"[1]","depth range must be a 2d array")});var n=r.def("+",t,"[0]");var a=r.def("+",t,"[1]");return[n,a]});case Di:return f(function(e){te.commandType(e,"object","blend.func",r.commandStr);var t="srcRGB"in e?e.srcRGB:e.src;var n="srcAlpha"in e?e.srcAlpha:e.src;var a="dstRGB"in e?e.dstRGB:e.dst;var i="dstAlpha"in e?e.dstAlpha:e.dst;te.commandParameter(t,df,o+".srcRGB",r.commandStr);te.commandParameter(n,df,o+".srcAlpha",r.commandStr);te.commandParameter(a,df,o+".dstRGB",r.commandStr);te.commandParameter(i,df,o+".dstAlpha",r.commandStr);te.command(mf.indexOf(t+", "+a)===-1,"unallowed blending combination (srcRGB, dstRGB) = ("+t+", "+a+")",r.commandStr);return[df[t],df[a],df[n],df[i]]},function(r,t,n){var a=r.constants.blendFuncs;te.optional(function(){r.assert(t,n+"&&typeof "+n+'==="object"',"invalid blend func, must be an object")});function i(i,o){var f=t.def('"',i,o,'" in ',n,"?",n,".",i,o,":",n,".",i);te.optional(function(){r.assert(t,f+" in "+a,"invalid "+e+"."+i+o+", must be one of "+Object.keys(df))});return f}var o=i("src","RGB");var f=i("dst","RGB");te.optional(function(){var e=r.constants.invalidBlendCombinations;r.assert(t,e+".indexOf("+o+'+", "+'+f+") === -1 ","unallowed blending combination for (srcRGB, dstRGB)")});var u=t.def(a,"[",o,"]");var c=t.def(a,"[",i("src","Alpha"),"]");var l=t.def(a,"[",f,"]");var s=t.def(a,"[",i("dst","Alpha"),"]");return[u,l,c,s]});case Mi:return f(function(t){if(typeof t==="string"){te.commandParameter(t,m,"invalid "+e,r.commandStr);return[m[t],m[t]]}else if(typeof t==="object"){te.commandParameter(t.rgb,m,e+".rgb",r.commandStr);te.commandParameter(t.alpha,m,e+".alpha",r.commandStr);return[m[t.rgb],m[t.alpha]]}else{te.commandRaise("invalid blend.equation",r.commandStr)}},function(r,t,n){var a=r.constants.blendEquations;var i=t.def();var o=t.def();var f=r.cond("typeof ",n,'==="string"');te.optional(function(){function t(e,t,n){r.assert(e,n+" in "+a,"invalid "+t+", must be one of "+Object.keys(m))}t(f.then,e,n);r.assert(f.else,n+"&&typeof "+n+'==="object"',"invalid "+e);t(f.else,e+".rgb",n+".rgb");t(f.else,e+".alpha",n+".alpha")});f.then(i,"=",o,"=",a,"[",n,"];");f.else(i,"=",a,"[",n,".rgb];",o,"=",a,"[",n,".alpha];");t(f);return[i,o]});case Ti:return f(function(e){te.command(zt(e)&&e.length===4,"blend.color must be a 4d array",r.commandStr);return ze(4,function(r){return+e[r]})},function(e,r,t){te.optional(function(){e.assert(r,e.shared.isArrayLike+"("+t+")&&"+t+".length===4","blend.color must be a 4d array")});return ze(4,function(e){return r.def("+",t,"[",e,"]")})});case Yi:return f(function(e){te.commandType(e,"number",o,r.commandStr);return e|0},function(e,r,t){te.optional(function(){e.assert(r,"typeof "+t+'==="number"',"invalid stencil.mask")});return r.def(t,"|0")});case Xi:return f(function(t){te.commandType(t,"object",o,r.commandStr);var n=t.cmp||"keep";var a=t.ref||0;var i="mask"in t?t.mask:-1;te.commandParameter(n,gf,e+".cmp",r.commandStr);te.commandType(a,"number",e+".ref",r.commandStr);te.commandType(i,"number",e+".mask",r.commandStr);return[gf[n],a,i]},function(e,r,t){var n=e.constants.compareFuncs;te.optional(function(){function a(){e.assert(r,Array.prototype.join.call(arguments,""),"invalid stencil.func")}a(t+"&&typeof ",t,'==="object"');a('!("cmp" in ',t,")||(",t,".cmp in ",n,")")});var a=r.def('"cmp" in ',t,"?",n,"[",t,".cmp]",":",uf);var i=r.def(t,".ref|0");var o=r.def('"mask" in ',t,"?",t,".mask|0:-1");return[a,i,o]});case Qi:case Ji:return f(function(t){te.commandType(t,"object",o,r.commandStr);var n=t.fail||"keep";var a=t.zfail||"keep";var i=t.zpass||"keep";te.commandParameter(n,bf,e+".fail",r.commandStr);te.commandParameter(a,bf,e+".zfail",r.commandStr);te.commandParameter(i,bf,e+".zpass",r.commandStr);return[e===Ji?rf:ef,bf[n],bf[a],bf[i]]},function(r,t,n){var a=r.constants.stencilOps;te.optional(function(){r.assert(t,n+"&&typeof "+n+'==="object"',"invalid "+e)});function i(i){te.optional(function(){r.assert(t,'!("'+i+'" in '+n+")||"+"("+n+"."+i+" in "+a+")","invalid "+e+"."+i+", must be one of "+Object.keys(bf))});return t.def('"',i,'" in ',n,"?",a,"[",n,".",i,"]:",uf)}return[e===Ji?rf:ef,i("fail"),i("zfail"),i("zpass")]});case qi:return f(function(e){te.commandType(e,"object",o,r.commandStr);var t=e.factor|0;var n=e.units|0;te.commandType(t,"number",o+".factor",r.commandStr);te.commandType(n,"number",o+".units",r.commandStr);return[t,n]},function(r,t,n){te.optional(function(){r.assert(t,n+"&&typeof "+n+'==="object"',"invalid "+e)});var a=t.def(n,".factor|0");var i=t.def(n,".units|0");return[a,i]});case Ri:return f(function(e){var t=0;if(e==="front"){t=ef}else if(e==="back"){t=rf}te.command(!!t,o,r.commandStr);return t},function(e,r,t){te.optional(function(){e.assert(r,t+'==="front"||'+t+'==="back"',"invalid cull.face")});return r.def(t,'==="front"?',ef,":",rf)});case Wi:return f(function(e){te.command(typeof e==="number"&&e>=n.lineWidthDims[0]&&e<=n.lineWidthDims[1],"invalid line width, must be a positive number between "+n.lineWidthDims[0]+" and "+n.lineWidthDims[1],r.commandStr);return e},function(e,r,t){te.optional(function(){e.assert(r,"typeof "+t+'==="number"&&'+t+">="+n.lineWidthDims[0]+"&&"+t+"<="+n.lineWidthDims[1],"invalid line width")});return t});case Bi:return f(function(e){te.commandParameter(e,_f,o,r.commandStr);return _f[e]},function(e,r,t){te.optional(function(){e.assert(r,t+'==="cw"||'+t+'==="ccw"',"invalid frontFace, must be one of cw,ccw")});return r.def(t+'==="cw"?'+tf+":"+nf)});case Ni:return f(function(e){te.command(zt(e)&&e.length===4,"color.mask must be length 4 array",r.commandStr);return e.map(function(e){return!!e})},function(e,r,t){te.optional(function(){e.assert(r,e.shared.isArrayLike+"("+t+")&&"+t+".length===4","invalid color.mask")});return ze(4,function(e){return"!!"+t+"["+e+"]"})});case Vi:return f(function(e){te.command(typeof e==="object"&&e,o,r.commandStr);var t="value"in e?e.value:1;var n=!!e.invert;te.command(typeof t==="number"&&t>=0&&t<=1,"sample.coverage.value must be a number between 0 and 1",r.commandStr);return[t,n]},function(e,r,t){te.optional(function(){e.assert(r,t+"&&typeof "+t+'==="object"',"invalid sample.coverage")});var n=r.def('"value" in ',t,"?+",t,".value:1");var a=r.def("!!",t,".invert");return[n,a]})}});return i}function N(e,r){var t=e.static;var n=e.dynamic;var a={};Object.keys(t).forEach(function(e){var n=t[e];var i;if(typeof n==="number"||typeof n==="boolean"){i=Ef(function(){return n})}else if(typeof n==="function"){var o=n._reglType;if(o==="texture2d"||o==="textureCube"){i=Ef(function(e){return e.link(n)})}else if(o==="framebuffer"||o==="framebufferCube"){te.command(n.color.length>0,'missing color attachment for framebuffer sent to uniform "'+e+'"',r.commandStr);i=Ef(function(e){return e.link(n.color[0])})}else{te.commandRaise('invalid data for uniform "'+e+'"',r.commandStr)}}else if(zt(n)){i=Ef(function(r){var t=r.global.def("[",ze(n.length,function(t){te.command(typeof n[t]==="number"||typeof n[t]==="boolean","invalid uniform "+e,r.commandStr);return n[t]}),"]");return t})}else{te.commandRaise('invalid or missing data for uniform "'+e+'"',r.commandStr)}i.value=n;a[e]=i});Object.keys(n).forEach(function(e){var r=n[e];a[e]=Sf(r,function(e,t){return e.invoke(t,r)})});return a}function L(e,t){var n=e.static;var i=e.dynamic;var o={};Object.keys(n).forEach(function(e){var i=n[e];var f=r.id(e);var u=new d;if(xf(i)){u.state=xi;u.buffer=a.getBuffer(a.create(i,xo,false,true));u.type=0}else{var c=a.getBuffer(i);if(c){u.state=xi;u.buffer=c;u.type=0}else{te.command(typeof i==="object"&&i,"invalid data for attribute "+e,t.commandStr);if("constant"in i){var l=i.constant;u.buffer="null";u.state=wi;if(typeof l==="number"){u.x=l}else{te.command(zt(l)&&l.length>0&&l.length<=4,"invalid constant for attribute "+e,t.commandStr);yi.forEach(function(e,r){if(r<l.length){u[e]=l[r]}})}}else{if(xf(i.buffer)){c=a.getBuffer(a.create(i.buffer,xo,false,true))}else{c=a.getBuffer(i.buffer)}te.command(!!c,'missing buffer for attribute "'+e+'"',t.commandStr);var s=i.offset|0;te.command(s>=0,'invalid offset for attribute "'+e+'"',t.commandStr);var v=i.stride|0;te.command(v>=0&&v<256,'invalid stride for attribute "'+e+'", must be integer betweeen [0, 255]',t.commandStr);var p=i.size|0;te.command(!("size"in i)||p>0&&p<=4,'invalid size for attribute "'+e+'", must be 1,2,3,4',t.commandStr);var h=!!i.normalized;var m=0;if("type"in i){te.commandParameter(i.type,Rr,"invalid type for attribute "+e,t.commandStr);m=Rr[i.type]}var b=i.divisor|0;if("divisor"in i){te.command(b===0||g,'cannot specify divisor for attribute "'+e+'", instancing not supported',t.commandStr);te.command(b>=0,'invalid divisor for attribute "'+e+'"',t.commandStr)}te.optional(function(){var r=t.commandStr;var n=["buffer","offset","divisor","normalized","type","size","stride"];Object.keys(i).forEach(function(t){te.command(n.indexOf(t)>=0,'unknown parameter "'+t+'" for attribute pointer "'+e+'" (valid parameters are '+n+")",r)})});u.buffer=c;u.state=xi;u.size=p;u.normalized=h;u.type=m||c.dtype;u.offset=s;u.stride=v;u.divisor=b}}}o[e]=Ef(function(e,r){var t=e.attribCache;if(f in t){return t[f]}var n={isStream:false};Object.keys(u).forEach(function(e){n[e]=u[e]});if(u.buffer){n.buffer=e.link(u.buffer);n.type=n.type||n.buffer+".dtype"}t[f]=n;return n})});Object.keys(i).forEach(function(e){var r=i[e];function t(t,n){var a=t.invoke(n,r);var i=t.shared;var o=i.isBufferArgs;var f=i.buffer;te.optional(function(){t.assert(n,a+"&&(typeof "+a+'==="object"||typeof '+a+'==="function")&&('+o+"("+a+")||"+f+".getBuffer("+a+")||"+f+".getBuffer("+a+".buffer)||"+o+"("+a+".buffer)||"+'("constant" in '+a+"&&(typeof "+a+'.constant==="number"||'+i.isArrayLike+"("+a+".constant))))",'invalid dynamic attribute "'+e+'"')});var u={isStream:n.def(false)};var c=new d;c.state=xi;Object.keys(c).forEach(function(e){u[e]=n.def(""+c[e])});var l=u.buffer;var s=u.type;n("if(",o,"(",a,")){",u.isStream,"=true;",l,"=",f,".createStream(",xo,",",a,");",s,"=",l,".dtype;","}else{",l,"=",f,".getBuffer(",a,");","if(",l,"){",s,"=",l,".dtype;",'}else if("constant" in ',a,"){",u.state,"=",wi,";","if(typeof "+a+'.constant === "number"){',u[yi[0]],"=",a,".constant;",yi.slice(1).map(function(e){return u[e]}).join("="),"=0;","}else{",yi.map(function(e,r){return u[e]+"="+a+".constant.length>"+r+"?"+a+".constant["+r+"]:0;"}).join(""),"}}else{","if(",o,"(",a,".buffer)){",l,"=",f,".createStream(",xo,",",a,".buffer);","}else{",l,"=",f,".getBuffer(",a,".buffer);","}",s,'="type" in ',a,"?",i.glTypes,"[",a,".type]:",l,".dtype;",u.normalized,"=!!",a,".normalized;");function v(e){n(u[e],"=",a,".",e,"|0;")}v("size");v("offset");v("stride");v("divisor");n("}}");n.exit("if(",u.isStream,"){",f,".destroyStream(",l,");","}");return u}o[e]=Sf(r,t)});return o}function R(e){var r=e.static;var t=e.dynamic;var n={};Object.keys(r).forEach(function(e){var t=r[e];n[e]=Ef(function(e,r){if(typeof t==="number"||typeof t==="boolean"){return""+t}else{return e.link(t)}})});Object.keys(t).forEach(function(e){var r=t[e];n[e]=Sf(r,function(e,t){return e.invoke(t,r)})});return n}function B(e,r,t,n,a){var i=e.static;var o=e.dynamic;te.optional(function(){var e=[to,no,ao,io,oo,uo,fo,co,ro].concat(x);function r(r){Object.keys(r).forEach(function(r){te.command(e.indexOf(r)>=0,'unknown parameter "'+r+'"',a.commandStr)})}r(i);r(o)});var f=D(e,a);var u=I(e,f,a);var c=P(e,a);var l=F(e,a);var s=C(e,a);function v(e){var r=u[e];if(r){l[e]=r}}v(eo);v(z(Zi));var p=Object.keys(l).length>0;var h={framebuffer:f,draw:c,shader:s,state:l,dirty:p};h.profile=M(e,a);h.uniforms=N(t,a);h.attributes=L(r,a);h.context=R(n,a);return h}function W(e,r,t){var n=e.shared;var a=n.context;var i=e.scope();Object.keys(t).forEach(function(n){r.save(a,"."+n);var o=t[n];i(a,".",n,"=",o.append(e,r),";")});r(i)}function U(e,r,t,n){var a=e.shared;var i=a.gl;var o=a.framebuffer;var f;if(b){f=r.def(a.extensions,".webgl_draw_buffers")}var u=e.constants;var c=u.drawBuffer;var l=u.backBuffer;var s;if(t){s=t.append(e,r)}else{s=r.def(o,".next")}if(!n){r("if(",s,"!==",o,".cur){")}r("if(",s,"){",i,".bindFramebuffer(",pf,",",s,".framebuffer);");if(b){r(f,".drawBuffersWEBGL(",c,"[",s,".colorAttachments.length]);")}r("}else{",i,".bindFramebuffer(",pf,",null);");if(b){r(f,".drawBuffersWEBGL(",l,");")}r("}",o,".cur=",s,";");if(!n){r("}")}}function q(e,r,t){var n=e.shared;var a=n.gl;var i=e.current;var o=e.next;var f=n.current;var u=n.next;var c=e.cond(f,".dirty");x.forEach(function(r){var n=z(r);if(n in t.state){return}var l,s;if(n in o){l=o[n];s=i[n];var v=ze(y[n].length,function(e){return c.def(l,"[",e,"]")});c(e.cond(v.map(function(e,r){return e+"!=="+s+"["+r+"]"}).join("||")).then(a,".",k[n],"(",v,");",v.map(function(e,r){return s+"["+r+"]="+e}).join(";"),";"))}else{l=c.def(u,".",n);var p=e.cond(l,"!==",f,".",n);c(p);if(n in w){p(e.cond(l).then(a,".enable(",w[n],");").else(a,".disable(",w[n],");"),f,".",n,"=",l,";")}else{p(a,".",k[n],"(",l,");",f,".",n,"=",l,";")}}});if(Object.keys(t.state).length===0){c(f,".dirty=false;")}r(c)}function G(e,r,t,n){var a=e.shared;var i=e.current;var o=a.current;var f=a.gl;wf(Object.keys(t)).forEach(function(a){var u=t[a];if(n&&!n(u)){return}var c=u.append(e,r);if(w[a]){var l=w[a];if(zf(u)){if(c){r(f,".enable(",l,");")}else{r(f,".disable(",l,");")}}else{r(e.cond(c).then(f,".enable(",l,");").else(f,".disable(",l,");"))}r(o,".",a,"=",c,";")}else if(zt(c)){var s=i[a];r(f,".",k[a],"(",c,");",c.map(function(e,r){return s+"["+r+"]="+e}).join(";"),";")}else{r(f,".",k[a],"(",c,");",o,".",a,"=",c,";")}})}function $(e,r){if(g){e.instancing=r.def(e.shared.extensions,".angle_instanced_arrays")}}function V(e,r,t,n,a){var i=e.shared;var o=e.stats;var f=i.current;var u=i.timer;var c=t.profile;function l(){if(typeof performance==="undefined"){return"Date.now()"}else{return"performance.now()"}}var s,v;function h(e){s=r.def();e(s,"=",l(),";");if(typeof a==="string"){e(o,".count+=",a,";")}else{e(o,".count++;")}if(p){if(n){v=r.def();e(v,"=",u,".getNumPendingQueries();")}else{e(u,".beginQuery(",o,");")}}}function d(e){e(o,".cpuTime+=",l(),"-",s,";");if(p){if(n){e(u,".pushScopeStats(",v,",",u,".getNumPendingQueries(),",o,");")}else{e(u,".endQuery();")}}}function m(e){var t=r.def(f,".profile");r(f,".profile=",e,";");r.exit(f,".profile=",t,";")}var g;if(c){if(zf(c)){if(c.enable){h(r);d(r.exit);m("true")}else{m("false")}return}g=c.append(e,r);m(g)}else{g=r.def(f,".profile")}var b=e.block();h(b);r("if(",g,"){",b,"}");var y=e.block();d(y);r.exit("if(",g,"){",y,"}")}function H(e,r,t,n,a){var i=e.shared;function o(e){switch(e){case No:case Wo:case $o:return 2;case Lo:case Uo:case Vo:return 3;case Ro:case qo:case Ho:return 4;default:return 1}}function f(t,n,a){var o=i.gl;var f=r.def(t,".location");var u=r.def(i.attributes,"[",f,"]");var c=a.state;var l=a.buffer;var s=[a.x,a.y,a.z,a.w];var v=["buffer","normalized","offset","stride"];function p(){r("if(!",u,".buffer){",o,".enableVertexAttribArray(",f,");}");var t=a.type;var i;if(!a.size){i=n}else{i=r.def(a.size,"||",n)}r("if(",u,".type!==",t,"||",u,".size!==",i,"||",v.map(function(e){return u+"."+e+"!=="+a[e]}).join("||"),"){",o,".bindBuffer(",xo,",",l,".buffer);",o,".vertexAttribPointer(",[f,i,t,a.normalized,a.stride,a.offset],");",u,".type=",t,";",u,".size=",i,";",v.map(function(e){return u+"."+e+"="+a[e]+";"}).join(""),"}");if(g){var c=a.divisor;r("if(",u,".divisor!==",c,"){",e.instancing,".vertexAttribDivisorANGLE(",[f,c],");",u,".divisor=",c,";}")}}function h(){r("if(",u,".buffer){",o,".disableVertexAttribArray(",f,");","}if(",yi.map(function(e,r){return u+"."+e+"!=="+s[r]}).join("||"),"){",o,".vertexAttrib4f(",f,",",s,");",yi.map(function(e,r){return u+"."+e+"="+s[r]+";"}).join(""),"}")}if(c===xi){p()}else if(c===wi){h()}else{r("if(",c,"===",xi,"){");p();r("}else{");h();r("}")}}n.forEach(function(n){var i=n.name;var u=t.attributes[i];var c;if(u){if(!a(u)){return}c=u.append(e,r)}else{if(!a(Af)){return}var l=e.scopeAttrib(i);te.optional(function(){e.assert(r,l+".state","missing attribute "+i)});c={};Object.keys(new d).forEach(function(e){c[e]=r.def(l,".",e)})}f(e.link(n),o(n.info.type),c)})}function Y(e,t,n,a,i){var o=e.shared;var f=o.gl;var u;for(var c=0;c<a.length;++c){var l=a[c];var s=l.name;var v=l.info.type;var p=n.uniforms[s];var h=e.link(l);var d=h+".location";var m;if(p){if(!i(p)){continue}if(zf(p)){var g=p.value;te.command(g!==null&&typeof g!=="undefined",'missing uniform "'+s+'"',e.commandStr);if(v===Jo||v===Ko){te.command(typeof g==="function"&&(v===Jo&&(g._reglType==="texture2d"||g._reglType==="framebuffer")||v===Ko&&(g._reglType==="textureCube"||g._reglType==="framebufferCube")),"invalid texture for uniform "+s,e.commandStr);var b=e.link(g._texture||g.color[0]._texture);t(f,".uniform1i(",d,",",b+".bind());");t.exit(b,".unbind();")}else if(v===Yo||v===Xo||v===Qo){te.optional(function(){te.command(zt(g),"invalid matrix for uniform "+s,e.commandStr);te.command(v===Yo&&g.length===4||v===Xo&&g.length===9||v===Qo&&g.length===16,"invalid length for matrix uniform "+s,e.commandStr)});var y=e.global.def("new Float32Array(["+Array.prototype.slice.call(g)+"])");var _=2;if(v===Xo){_=3}else if(v===Qo){_=4}t(f,".uniformMatrix",_,"fv(",d,",false,",y,");")}else{switch(v){case Fo:te.commandType(g,"number","uniform "+s,e.commandStr);u="1f";break;case No:te.command(zt(g)&&g.length===2,"uniform "+s,e.commandStr);u="2f";break;case Lo:te.command(zt(g)&&g.length===3,"uniform "+s,e.commandStr);u="3f";break;case Ro:te.command(zt(g)&&g.length===4,"uniform "+s,e.commandStr);u="4f";break;case Go:te.commandType(g,"boolean","uniform "+s,e.commandStr);u="1i";break;case Bo:te.commandType(g,"number","uniform "+s,e.commandStr);u="1i";break;case $o:te.command(zt(g)&&g.length===2,"uniform "+s,e.commandStr);u="2i";break;case Wo:te.command(zt(g)&&g.length===2,"uniform "+s,e.commandStr);u="2i";break;case Vo:te.command(zt(g)&&g.length===3,"uniform "+s,e.commandStr);u="3i";break;case Uo:te.command(zt(g)&&g.length===3,"uniform "+s,e.commandStr);u="3i";break;case Ho:te.command(zt(g)&&g.length===4,"uniform "+s,e.commandStr);u="4i";break;case qo:te.command(zt(g)&&g.length===4,"uniform "+s,e.commandStr);u="4i";break}t(f,".uniform",u,"(",d,",",zt(g)?Array.prototype.slice.call(g):g,");")}continue}else{m=p.append(e,t)}}else{if(!i(Af)){continue}m=t.def(o.uniforms,"[",r.id(s),"]")}if(v===Jo){t("if(",m,"&&",m,'._reglType==="framebuffer"){',m,"=",m,".color[0];","}")}else if(v===Ko){t("if(",m,"&&",m,'._reglType==="framebufferCube"){',m,"=",m,".color[0];","}")}te.optional(function(){function r(r,n){e.assert(t,r,'bad data or missing for uniform "'+s+'".  '+n)}function n(e){r("typeof "+m+'==="'+e+'"',"invalid type, expected "+e)}function a(t,n){r(o.isArrayLike+"("+m+")&&"+m+".length==="+t,"invalid vector, should have length "+t,e.commandStr)}function i(t){r("typeof "+m+'==="function"&&'+m+'._reglType==="texture'+(t===Eo?"2d":"Cube")+'"',"invalid texture type",e.commandStr)}switch(v){case Bo:n("number");break;case Wo:a(2,"number");break;case Uo:a(3,"number");break;case qo:a(4,"number");break;case Fo:n("number");break;case No:a(2,"number");break;case Lo:a(3,"number");break;case Ro:a(4,"number");break;case Go:n("boolean");break;case $o:a(2,"boolean");break;case Vo:a(3,"boolean");break;case Ho:a(4,"boolean");break;case Yo:a(4,"number");break;case Xo:a(9,"number");break;case Qo:a(16,"number");break;case Jo:i(Eo);break;case Ko:i(So);break}});var x=1;switch(v){case Jo:case Ko:var w=t.def(m,"._texture");t(f,".uniform1i(",d,",",w,".bind());");t.exit(w,".unbind();");continue;case Bo:case Go:u="1i";break;case Wo:case $o:u="2i";x=2;break;case Uo:case Vo:u="3i";x=3;break;case qo:case Ho:u="4i";x=4;break;case Fo:u="1f";break;case No:u="2f";x=2;break;case Lo:u="3f";x=3;break;case Ro:u="4f";x=4;break;case Yo:u="Matrix2fv";break;case Xo:u="Matrix3fv";break;case Qo:u="Matrix4fv";break}t(f,".uniform",u,"(",d,",");if(u.charAt(0)==="M"){var k=Math.pow(v-Yo+2,2);var z=e.global.def("new Float32Array(",k,")");t("false,(Array.isArray(",m,")||",m," instanceof Float32Array)?",m,":(",ze(k,function(e){return z+"["+e+"]="+m+"["+e+"]"}),",",z,")")}else if(x>1){t(ze(x,function(e){return m+"["+e+"]"}))}else{t(m)}t(");")}}function X(e,r,t,n){var a=e.shared;var i=a.gl;var o=a.draw;var f=n.draw;function u(){var a=f.elements;var u;var c=r;if(a){if(a.contextDep&&n.contextDynamic||a.propDep){c=t}u=a.append(e,c)}else{u=c.def(o,".",io)}if(u){c("if("+u+")"+i+".bindBuffer("+wo+","+u+".buffer.buffer);")}return u}function c(){var a=f.count;var i;var u=r;if(a){if(a.contextDep&&n.contextDynamic||a.propDep){u=t}i=a.append(e,u);te.optional(function(){if(a.MISSING){e.assert(r,"false","missing vertex count")}if(a.DYNAMIC){e.assert(u,i+">=0","missing vertex count")}})}else{i=u.def(o,".",fo);te.optional(function(){e.assert(u,i+">=0","missing vertex count")})}return i}var l=u();function s(a){var i=f[a];if(i){if(i.contextDep&&n.contextDynamic||i.propDep){return i.append(e,t)}else{return i.append(e,r)}}else{return r.def(o,".",a)}}var v=s(oo);var p=s(uo);var h=c();if(typeof h==="number"){if(h===0){return}}else{t("if(",h,"){");t.exit("}")}var d,m;if(g){d=s(co);m=e.instancing}var b=l+".type";var y=f.elements&&zf(f.elements);function _(){function e(){t(m,".drawElementsInstancedANGLE(",[v,h,b,p+"<<(("+b+"-"+_i+")>>1)",d],");")}function r(){t(m,".drawArraysInstancedANGLE(",[v,p,h,d],");")}if(l){if(!y){t("if(",l,"){");e();t("}else{");r();t("}")}else{e()}}else{r()}}function x(){function e(){t(i+".drawElements("+[v,h,b,p+"<<(("+b+"-"+_i+")>>1)"]+");")}function r(){t(i+".drawArrays("+[v,p,h]+");")}if(l){if(!y){t("if(",l,"){");e();t("}else{");r();t("}")}else{e()}}else{r()}}if(g&&(typeof d!=="number"||d>=0)){if(typeof d==="string"){t("if(",d,">0){");_();t("}else if(",d,"<0){");x();t("}")}else{_()}}else{x()}}function Q(e,r,t,n,a){var i=T();var o=i.proc("body",a);te.optional(function(){i.commandStr=r.commandStr;i.command=i.link(r.commandStr)});if(g){i.instancing=o.def(i.shared.extensions,".angle_instanced_arrays")}e(i,o,t,n);return i.compile().body}function J(e,r,t,n){$(e,r);H(e,r,t,n.attributes,function(){return true});Y(e,r,t,n.uniforms,function(){return true});X(e,r,r,t)}function K(e,r){var t=e.proc("draw",1);$(e,t);W(e,t,r.context);U(e,t,r.framebuffer);q(e,t,r);G(e,t,r.state);V(e,t,r,false,true);var n=r.shader.progVar.append(e,t);t(e.shared.gl,".useProgram(",n,".program);");if(r.shader.program){J(e,t,r,r.shader.program)}else{var a=e.global.def("{}");var i=t.def(n,".id");var o=t.def(a,"[",i,"]");t(e.cond(o).then(o,".call(this,a0);").else(o,"=",a,"[",i,"]=",e.link(function(t){return Q(J,e,r,t,1)}),"(",n,");",o,".call(this,a0);"))}if(Object.keys(r.state).length>0){t(e.shared.current,".dirty=true;")}}function Z(e,r,t,n){e.batchId="a1";$(e,r);function a(){return true}H(e,r,t,n.attributes,a);Y(e,r,t,n.uniforms,a);X(e,r,r,t)}function ee(e,r,t,n){$(e,r);var a=t.contextDep;var i=r.def();var o="a0";var f="a1";var u=r.def();e.shared.props=u;e.batchId=i;var c=e.scope();var l=e.scope();r(c.entry,"for(",i,"=0;",i,"<",f,";++",i,"){",u,"=",o,"[",i,"];",l,"}",c.exit);function s(e){return e.contextDep&&a||e.propDep}function v(e){return!s(e)}if(t.needsContext){W(e,l,t.context)}if(t.needsFramebuffer){U(e,l,t.framebuffer)}G(e,l,t.state,s);if(t.profile&&s(t.profile)){V(e,l,t,false,true)}if(!n){var p=e.global.def("{}");var h=t.shader.progVar.append(e,l);var d=l.def(h,".id");var m=l.def(p,"[",d,"]");l(e.shared.gl,".useProgram(",h,".program);","if(!",m,"){",m,"=",p,"[",d,"]=",e.link(function(r){return Q(Z,e,t,r,2)}),"(",h,");}",m,".call(this,a0[",i,"],",i,");")}else{H(e,c,t,n.attributes,v);H(e,l,t,n.attributes,s);Y(e,c,t,n.uniforms,v);Y(e,l,t,n.uniforms,s);X(e,c,l,t)}}function re(e,r){var t=e.proc("batch",2);e.batchId="0";$(e,t);var n=false;var a=true;Object.keys(r.context).forEach(function(e){n=n||r.context[e].propDep});if(!n){W(e,t,r.context);a=false}var i=r.framebuffer;var o=false;if(i){if(i.propDep){n=o=true}else if(i.contextDep&&n){o=true}if(!o){U(e,t,i)}}else{U(e,t,null)}if(r.state.viewport&&r.state.viewport.propDep){n=true}function f(e){return e.contextDep&&n||e.propDep}q(e,t,r);G(e,t,r.state,function(e){return!f(e)});if(!r.profile||!f(r.profile)){V(e,t,r,false,"a1")}r.contextDep=n;r.needsContext=a;r.needsFramebuffer=o;var u=r.shader.progVar;if(u.contextDep&&n||u.propDep){ee(e,t,r,null)}else{var c=u.append(e,t);t(e.shared.gl,".useProgram(",c,".program);");if(r.shader.program){ee(e,t,r,r.shader.program)}else{var l=e.global.def("{}");var s=t.def(c,".id");var v=t.def(l,"[",s,"]");t(e.cond(v).then(v,".call(this,a0,a1);").else(v,"=",l,"[",s,"]=",e.link(function(t){return Q(ee,e,r,t,2)}),"(",c,");",v,".call(this,a0,a1);"))}}if(Object.keys(r.state).length>0){t(e.shared.current,".dirty=true;")}}function ne(e,t){var n=e.proc("scope",3);e.batchId="a2";var a=e.shared;var i=a.current;W(e,n,t.context);if(t.framebuffer){t.framebuffer.append(e,n)}wf(Object.keys(t.state)).forEach(function(r){var i=t.state[r];var o=i.append(e,n);if(zt(o)){o.forEach(function(t,a){n.set(e.next[r],"["+a+"]",t)})}else{n.set(a.next,"."+r,o)}});V(e,n,t,true,true);[io,uo,fo,co,oo].forEach(function(r){var i=t.draw[r];if(!i){return}n.set(a.draw,"."+r,""+i.append(e,n))});Object.keys(t.uniforms).forEach(function(i){n.set(a.uniforms,"["+r.id(i)+"]",t.uniforms[i].append(e,n))});Object.keys(t.attributes).forEach(function(r){var a=t.attributes[r].append(e,n);var i=e.scopeAttrib(r);Object.keys(new d).forEach(function(e){n.set(i,"."+e,a[e])})});function o(r){var i=t.shader[r];if(i){n.set(a.shader,"."+r,i.append(e,n))}}o(no);o(ao);if(Object.keys(t.state).length>0){n(i,".dirty=true;");n.exit(i,".dirty=true;")}n("a1(",e.shared.context,",a0,",e.batchId,");")}function ae(e){if(typeof e!=="object"||zt(e)){return}var r=Object.keys(e);for(var t=0;t<r.length;++t){if(ve.isDynamic(e[r[t]])){return true}}return false}function ie(e,r,t){var n=r.static[t];if(!n||!ae(n)){return}var a=e.global;var i=Object.keys(n);var o=false;var f=false;var u=false;var c=e.global.def("{}");i.forEach(function(r){var t=n[r];if(ve.isDynamic(t)){if(typeof t==="function"){t=n[r]=ve.unbox(t)}var i=Sf(t,null);o=o||i.thisDep;u=u||i.propDep;f=f||i.contextDep}else{a(c,".",r,"=");switch(typeof t){case"number":a(t);break;case"string":a('"',t,'"');break;case"object":if(Array.isArray(t)){a("[",t.join(),"]")}break;default:a(e.link(t));break}a(";")}});function l(e,r){i.forEach(function(t){var a=n[t];if(!ve.isDynamic(a)){return}var i=e.invoke(r,a);r(c,".",t,"=",i,";")})}r.dynamic[t]=new ve.DynamicVariable(Ai,{thisDep:o,contextDep:f,propDep:u,ref:c,append:l});delete r.static[t]}function oe(e,r,t,n,a){var i=T();i.stats=i.link(a);Object.keys(r.static).forEach(function(e){ie(i,r,e)});_o.forEach(function(r){ie(i,e,r)});var o=B(e,r,t,n,i);K(i,o);ne(i,o);re(i,o);return i.compile()}return{next:_,current:y,procs:function(){var e=T();var r=e.proc("poll");var t=e.proc("refresh");var a=e.block();r(a);t(a);var i=e.shared;var o=i.gl;var f=i.next;var u=i.current;a(u,".dirty=false;");U(e,r);U(e,t,null,true);var c;if(g){c=e.link(g)}for(var l=0;l<n.maxAttributes;++l){var s=t.def(i.attributes,"[",l,"]");var v=e.cond(s,".buffer");v.then(o,".enableVertexAttribArray(",l,");",o,".bindBuffer(",xo,",",s,".buffer.buffer);",o,".vertexAttribPointer(",l,",",s,".size,",s,".type,",s,".normalized,",s,".stride,",s,".offset);").else(o,".disableVertexAttribArray(",l,");",o,".vertexAttrib4f(",l,",",s,".x,",s,".y,",s,".z,",s,".w);",s,".buffer=null;");t(v);if(g){t(c,".vertexAttribDivisorANGLE(",l,",",s,".divisor);")}}Object.keys(w).forEach(function(n){var i=w[n];var c=a.def(f,".",n);var l=e.block();l("if(",c,"){",o,".enable(",i,")}else{",o,".disable(",i,")}",u,".",n,"=",c,";");t(l);r("if(",c,"!==",u,".",n,"){",l,"}")});Object.keys(k).forEach(function(n){var i=k[n];var c=y[n];var l,s;var v=e.block();v(o,".",i,"(");if(zt(c)){var p=c.length;l=e.global.def(f,".",n);s=e.global.def(u,".",n);v(ze(p,function(e){return l+"["+e+"]"}),");",ze(p,function(e){return s+"["+e+"]="+l+"["+e+"];"}).join(""));r("if(",ze(p,function(e){return l+"["+e+"]!=="+s+"["+e+"]"}).join("||"),"){",v,"}")}else{l=a.def(f,".",n);s=a.def(u,".",n);v(l,");",u,".",n,"=",l,";");r("if(",l,"!==",s,"){",v,"}")}t(v)});return e.compile()}(),compile:oe}}function Of(){return{bufferCount:0,elementsCount:0,framebufferCount:0,shaderCount:0,textureCount:0,cubeCount:0,renderbufferCount:0,maxTextureUnits:0}}var Tf=34918;var Mf=34919;var Df=35007;var If=function(e,r){if(!r.ext_disjoint_timer_query){return null}var t=[];function n(){return t.pop()||r.ext_disjoint_timer_query.createQueryEXT()}function a(e){t.push(e)}var i=[];function o(e){var t=n();r.ext_disjoint_timer_query.beginQueryEXT(Df,t);i.push(t);p(i.length-1,i.length,e)}function f(){r.ext_disjoint_timer_query.endQueryEXT(Df)}function u(){this.startQueryIndex=-1;this.endQueryIndex=-1;this.sum=0;this.stats=null}var c=[];function l(){return c.pop()||new u}function s(e){c.push(e)}var v=[];function p(e,r,t){var n=l();n.startQueryIndex=e;n.endQueryIndex=r;n.sum=0;n.stats=t;v.push(n)}var h=[];var d=[];function m(){var e,t;var n=i.length;if(n===0){return}d.length=Math.max(d.length,n+1);h.length=Math.max(h.length,n+1);h[0]=0;d[0]=0;var o=0;e=0;for(t=0;t<i.length;++t){var f=i[t];if(r.ext_disjoint_timer_query.getQueryObjectEXT(f,Mf)){o+=r.ext_disjoint_timer_query.getQueryObjectEXT(f,Tf);a(f)}else{i[e++]=f}h[t+1]=o;d[t+1]=e}i.length=e;e=0;for(t=0;t<v.length;++t){var u=v[t];var c=u.startQueryIndex;var l=u.endQueryIndex;u.sum+=h[l]-h[c];var p=d[c];var m=d[l];if(m===p){u.stats.gpuTime+=u.sum/1e6;s(u)}else{u.startQueryIndex=p;u.endQueryIndex=m;v[e++]=u}}v.length=e}return{beginQuery:o,endQuery:f,pushScopeStats:p,update:m,getNumPendingQueries:function(){return i.length},clear:function(){t.push.apply(t,i);for(var e=0;e<t.length;e++){r.ext_disjoint_timer_query.deleteQueryEXT(t[e])}i.length=0;t.length=0},restore:function(){i.length=0;t.length=0}}};var Cf=16384;var Pf=256;var Ff=1024;var Nf=34962;var Lf="webglcontextlost";var Rf="webglcontextrestored";var Bf=1;var Wf=2;var Uf=3;function qf(e,r){for(var t=0;t<e.length;++t){if(e[t]===r){return t}}return-1}function Gf(e){var t=we(e);if(!t){return null}var n=t.gl;var a=n.getContextAttributes();var i=n.isContextLost();var o=ke(n,t);if(!o){return null}var f=de();var u=Of();var c=o.extensions;var l=If(n,c);var s=he();var v=n.drawingBufferWidth;var p=n.drawingBufferHeight;var h={tick:0,time:0,viewportWidth:v,viewportHeight:p,framebufferWidth:v,framebufferHeight:p,drawingBufferWidth:v,drawingBufferHeight:p,pixelRatio:t.pixelRatio};var d={};var m={elements:null,primitive:4,count:-1,offset:0,instances:-1};var g=_r(n,c);var b=ii(n,c,g,f);var y=Zr(n,u,t,b);var _=yt(n,c,y,u);var x=li(n,f,u,t);var w=fa(n,c,g,function(){E.procs.poll()},h,u,t);var k=xa(n,c,g,u,t);var z=ti(n,c,g,w,k,u);var E=jf(n,f,c,g,y,_,w,z,d,b,x,m,h,l,t);var S=di(n,z,E.procs.poll,h,a,c,g);var A=E.next;var j=n.canvas;var O=[];var T=[];var M=[];var D=[t.onDestroy];var I=null;function C(){if(O.length===0){if(l){l.update()}I=null;return}I=pe.next(C);V();for(var e=O.length-1;e>=0;--e){var r=O[e];if(r){r(h,null,0)}}n.flush();if(l){l.update()}}function P(){if(!I&&O.length>0){I=pe.next(C)}}function F(){if(I){pe.cancel(C);I=null}}function N(e){e.preventDefault();i=true;F();T.forEach(function(e){e()})}function L(e){n.getError();i=false;o.restore();x.restore();y.restore();w.restore();k.restore();z.restore();if(l){l.restore()}E.procs.refresh();P();M.forEach(function(e){e()})}if(j){j.addEventListener(Lf,N,false);j.addEventListener(Rf,L,false)}function R(){O.length=0;F();if(j){j.removeEventListener(Lf,N);j.removeEventListener(Rf,L)}x.clear();z.clear();k.clear();w.clear();_.clear();y.clear();if(l){l.clear()}D.forEach(function(e){e()})}function B(e){te(!!e,"invalid args to regl({...})");te.type(e,"object","invalid args to regl({...})");function t(e){var t=r({},e);delete t.uniforms;delete t.attributes;delete t.context;if("stencil"in t&&t.stencil.op){t.stencil.opBack=t.stencil.opFront=t.stencil.op;delete t.stencil.op}function n(e){if(e in t){var r=t[e];delete t[e];Object.keys(r).forEach(function(n){t[e+"."+n]=r[n]})}}n("blend");n("depth");n("cull");n("stencil");n("polygonOffset");n("scissor");n("sample");return t}function n(e){var r={};var t={};Object.keys(e).forEach(function(n){var a=e[n];if(ve.isDynamic(a)){t[n]=ve.unbox(a,n)}else{r[n]=a}});return{dynamic:t,static:r}}var a=n(e.context||{});var o=n(e.uniforms||{});var f=n(e.attributes||{});var u=n(t(e));var c={gpuTime:0,cpuTime:0,count:0};var l=E.compile(u,f,o,a,c);var s=l.draw;var v=l.batch;var p=l.scope;var h=[];function d(e){while(h.length<e){h.push(null)}return h}function m(e,r){var t;if(i){te.raise("context lost")}if(typeof e==="function"){return p.call(this,null,e,0)}else if(typeof r==="function"){if(typeof e==="number"){for(t=0;t<e;++t){p.call(this,null,r,t)}return}else if(Array.isArray(e)){for(t=0;t<e.length;++t){p.call(this,e[t],r,t)}return}else{return p.call(this,e,r,0)}}else if(typeof e==="number"){if(e>0){return v.call(this,d(e|0),e|0)}}else if(Array.isArray(e)){if(e.length){return v.call(this,e,e.length)}}else{return s.call(this,e)}}return r(m,{stats:c})}var W=z.setFBO=B({framebuffer:ve.define.call(null,Bf,"framebuffer")});function U(e,r){var t=0;E.procs.poll();var a=r.color;if(a){n.clearColor(+a[0]||0,+a[1]||0,+a[2]||0,+a[3]||0);t|=Cf}if("depth"in r){n.clearDepth(+r.depth);t|=Pf}if("stencil"in r){n.clearStencil(r.stencil|0);t|=Ff}te(!!t,"called regl.clear with no buffer specified");n.clear(t)}function q(e){te(typeof e==="object"&&e,"regl.clear() takes an object as input");if("framebuffer"in e){if(e.framebuffer&&e.framebuffer_reglType==="framebufferCube"){for(var t=0;t<6;++t){W(r({framebuffer:e.framebuffer.faces[t]},e),U)}}else{W(e,U)}}else{U(null,e)}}function G(e){te.type(e,"function","regl.frame() callback must be a function");O.push(e);function r(){var r=qf(O,e);te(r>=0,"cannot cancel a frame twice");function t(){var e=qf(O,t);O[e]=O[O.length-1];O.length-=1;if(O.length<=0){F()}}O[r]=t}P();return{cancel:r}}function $(){var e=A.viewport;var r=A.scissor_box;e[0]=e[1]=r[0]=r[1]=0;h.viewportWidth=h.framebufferWidth=h.drawingBufferWidth=e[2]=r[2]=n.drawingBufferWidth;h.viewportHeight=h.framebufferHeight=h.drawingBufferHeight=e[3]=r[3]=n.drawingBufferHeight}function V(){h.tick+=1;h.time=Y();$();E.procs.poll()}function H(){$();E.procs.refresh();if(l){l.update()}}function Y(){return(he()-s)/1e3}H();function X(e,r){te.type(r,"function","listener callback must be a function");var t;switch(e){case"frame":return G(r);case"lost":t=T;break;case"restore":t=M;break;case"destroy":t=D;break;default:te.raise("invalid event, must be one of frame,lost,restore,destroy")}t.push(r);return{cancel:function(){for(var e=0;e<t.length;++e){if(t[e]===r){t[e]=t[t.length-1];t.pop();return}}}}}var Q=r(B,{clear:q,prop:ve.define.bind(null,Bf),context:ve.define.bind(null,Wf),this:ve.define.bind(null,Uf),draw:B({}),buffer:function(e){return y.create(e,Nf,false,false)},elements:function(e){return _.create(e,false)},texture:w.create2D,cube:w.createCube,renderbuffer:k.create,framebuffer:z.create,framebufferCube:z.createCube,attributes:a,frame:G,on:X,limits:g,hasExtension:function(e){return g.extensions.indexOf(e.toLowerCase())>=0},read:S,destroy:R,_gl:n,_refresh:H,poll:function(){V();if(l){l.update()}},now:Y,stats:u});t.onDone(null,Q);return Q}return Gf})},"499f":function(e,r){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},"49ca":function(e,r,t){var n=t("2b84");var a=Math.floor;e.exports=function e(r){return!n(r)&&isFinite(r)&&a(r)===r}},"4a46":function(e,r){e.exports=t;function t(e,r){e[0]=Math.round(r[0]);e[1]=Math.round(r[1]);return e}},"4af6":function(e,r,t){e.exports=t("06ed")},"4bcc":function(e,r){e.exports=t;function t(e){var r=new Float32Array(2);r[0]=e[0];r[1]=e[1];return r}},"4ce8":function(e,r,t){var n=t("22fe");var a=t("f7b2");var i=t("c0f6");var o=t("ad4b");var f="["+o+"]";var u="​";var c=RegExp("^"+f+f+"*");var l=RegExp(f+f+"*$");var s=function(e,r,t){var a={};var f=i(function(){return!!o[e]()||u[e]()!=u});var c=a[e]=f?r(v):o[e];if(t)a[t]=c;n(n.P+n.F*f,"String",a)};var v=s.trim=function(e,r){e=String(a(e));if(r&1)e=e.replace(c,"");if(r&2)e=e.replace(l,"");return e};e.exports=s},"4f06":function(e,r){e.exports=t;function t(e,r,t){var n=r[0],a=r[1];e[0]=t[0]*n+t[3]*a+t[6];e[1]=t[1]*n+t[4]*a+t[7];return e}},"4f29":function(e,r,t){e.exports=!t("e2e5")&&!t("c0f6")(function(){return Object.defineProperty(t("da3a")("div"),"a",{get:function(){return 7}}).a!=7})},"50e2":function(e,r,t){var n=t("22fe");n(n.S,"Number",{isInteger:t("49ca")})},5159:function(e,r){r.f={}.propertyIsEnumerable},"533a":function(e,r,t){"use strict";var n=t("84c3");var a=t("5a3a");var i=t("fe4e");var o={};t("1f03")(o,t("0536")("iterator"),function(){return this});e.exports=function(e,r,t){e.prototype=n(o,{next:a(1,t)});i(e,r+" Iterator")}},"58a4":function(e,r,t){"use strict";var n=t("f861");var a=t("22fe");var i=t("a85c");var o=t("1385");var f=t("404a");var u=t("d2b4");var c=t("9a92");var l=t("2b84");var s=t("c0f6");var v=t("5db0");var p=t("fe4e");var h=t("c249");e.exports=function(e,r,t,d,m,g){var b=n[e];var y=b;var _=m?"set":"add";var x=y&&y.prototype;var w={};var k=function(e){var r=x[e];i(x,e,e=="delete"?function(e){return g&&!l(e)?false:r.call(this,e===0?0:e)}:e=="has"?function e(t){return g&&!l(t)?false:r.call(this,t===0?0:t)}:e=="get"?function e(t){return g&&!l(t)?undefined:r.call(this,t===0?0:t)}:e=="add"?function e(t){r.call(this,t===0?0:t);return this}:function e(t,n){r.call(this,t===0?0:t,n);return this})};if(typeof y!="function"||!(g||x.forEach&&!s(function(){(new y).entries().next()}))){y=d.getConstructor(r,e,m,_);o(y.prototype,t);f.NEED=true}else{var z=new y;var E=z[_](g?{}:-0,1)!=z;var S=s(function(){z.has(1)});var A=v(function(e){new y(e)});var j=!g&&s(function(){var e=new y;var r=5;while(r--)e[_](r,r);return!e.has(-0)});if(!A){y=r(function(r,t){c(r,y,e);var n=h(new b,r,y);if(t!=undefined)u(t,m,n[_],n);return n});y.prototype=x;x.constructor=y}if(S||j){k("delete");k("has");m&&k("get")}if(j||E)k(_);if(g&&x.clear)delete x.clear}p(y,e);w[e]=y;a(a.G+a.W+a.F*(y!=b),w);if(!g)d.setStrong(y,e,m);return y}},"5a3a":function(e,r){e.exports=function(e,r){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:r}}},"5a51":function(e,r,t){t("d2c4")("split",2,function(e,r,n){"use strict";var a=t("160f");var i=n;var o=[].push;var f="split";var u="length";var c="lastIndex";if("abbc"[f](/(b)*/)[1]=="c"||"test"[f](/(?:)/,-1)[u]!=4||"ab"[f](/(?:ab)*/)[u]!=2||"."[f](/(.?)(.?)/)[u]!=4||"."[f](/()()/)[u]>1||""[f](/.?/)[u]){var l=/()??/.exec("")[1]===undefined;n=function(e,r){var t=String(this);if(e===undefined&&r===0)return[];if(!a(e))return i.call(t,e,r);var n=[];var f=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":"");var s=0;var v=r===undefined?**********:r>>>0;var p=new RegExp(e.source,f+"g");var h,d,m,g,b;if(!l)h=new RegExp("^"+p.source+"$(?!\\s)",f);while(d=p.exec(t)){m=d.index+d[0][u];if(m>s){n.push(t.slice(s,d.index));if(!l&&d[u]>1)d[0].replace(h,function(){for(b=1;b<arguments[u]-2;b++)if(arguments[b]===undefined)d[b]=undefined});if(d[u]>1&&d.index<t[u])o.apply(n,d.slice(1));g=d[0][u];s=m;if(n[u]>=v)break}if(p[c]===d.index)p[c]++}if(s===t[u]){if(g||!p.test(""))n.push("")}else n.push(t.slice(s));return n[u]>v?n.slice(0,v):n}}else if("0"[f](undefined,0)[u]){n=function(e,r){return e===undefined&&r===0?[]:i.call(this,e,r)}}return[function t(a,i){var o=e(this);var f=a==undefined?undefined:a[r];return f!==undefined?f.call(a,o,i):n.call(String(o),a,i)},n]})},"5bc3":function(e,r){function t(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}function n(e,r,n){if(r)t(e.prototype,r);if(n)t(e,n);return e}e.exports=n},"5c84":function(e,r,t){var n=t("3546");var a=t("8be2");var i=t("5159");e.exports=function(e){var r=n(e);var t=a.f;if(t){var o=t(e);var f=i.f;var u=0;var c;while(o.length>u)if(f.call(e,c=o[u++]))r.push(c)}return r}},"5d0d":function(e,r,t){var n=t("98ab");var a=t("3955");var i=t("3546");e.exports=t("e2e5")?Object.defineProperties:function e(r,t){a(r);var o=i(t);var f=o.length;var u=0;var c;while(f>u)n.f(r,c=o[u++],t[c]);return r}},"5db0":function(e,r,t){var n=t("0536")("iterator");var a=false;try{var i=[7][n]();i["return"]=function(){a=true};Array.from(i,function(){throw 2})}catch(o){}e.exports=function(e,r){if(!r&&!a)return false;var t=false;try{var i=[7];var f=i[n]();f.next=function(){return{done:t=true}};i[n]=function(){return f};e(i)}catch(o){}return t}},"5e86":function(e,r,t){t("fd04")("asyncIterator")},"5f45":function(e,r,t){var n=t("a2ce");e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return n(e)=="String"?e.split(""):Object(e)}},6149:function(e,r,t){
/**
 * chroma.js - JavaScript library for color conversions
 *
 * Copyright (c) 2011-2018, Gregor Aisch
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. The name Gregor Aisch may not be used to endorse or promote products
 * derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL GREGOR AISCH OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * -------------------------------------------------------
 *
 * chroma.js includes colors from colorbrewer2.org, which are released under
 * the following license:
 *
 * Copyright (c) 2002 Cynthia Brewer, Mark Harrower,
 * and The Pennsylvania State University.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific
 * language governing permissions and limitations under the License.
 *
 * ------------------------------------------------------
 *
 * Named colors are taken from X11 Color Names.
 * http://www.w3.org/TR/css3-color/#svg-color
 *
 * @preserve
 */
(function(r,t){true?e.exports=t():undefined})(this,function(){"use strict";var e=function(e,r,t){if(r===void 0)r=0;if(t===void 0)t=1;return e<r?r:e>t?t:e};var r=function(r){r._clipped=false;r._unclipped=r.slice(0);for(var t=0;t<=3;t++){if(t<3){if(r[t]<0||r[t]>255){r._clipped=true}r[t]=e(r[t],0,255)}else if(t===3){r[t]=e(r[t],0,1)}}return r};var t={};for(var n=0,a=["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"];n<a.length;n+=1){var i=a[n];t["[object "+i+"]"]=i.toLowerCase()}var o=function(e){return t[Object.prototype.toString.call(e)]||"object"};var f=function(e,r){if(r===void 0)r=null;if(e.length>=3){return Array.prototype.slice.call(e)}if(o(e[0])=="object"&&r){return r.split("").filter(function(r){return e[0][r]!==undefined}).map(function(r){return e[0][r]})}return e[0]};var u=function(e){if(e.length<2){return null}var r=e.length-1;if(o(e[r])=="string"){return e[r].toLowerCase()}return null};var c=Math.PI;var l={clip_rgb:r,limit:e,type:o,unpack:f,last:u,PI:c,TWOPI:c*2,PITHIRD:c/3,DEG2RAD:c/180,RAD2DEG:180/c};var s={format:{},autodetect:[]};var v=l.last;var p=l.clip_rgb;var h=l.type;var d=function e(){var r=[],t=arguments.length;while(t--)r[t]=arguments[t];var n=this;if(h(r[0])==="object"&&r[0].constructor&&r[0].constructor===this.constructor){return r[0]}var a=v(r);var i=false;if(!a){i=true;if(!s.sorted){s.autodetect=s.autodetect.sort(function(e,r){return r.p-e.p});s.sorted=true}for(var o=0,f=s.autodetect;o<f.length;o+=1){var u=f[o];a=u.test.apply(u,r);if(a){break}}}if(s.format[a]){var c=s.format[a].apply(null,i?r:r.slice(0,-1));n._rgb=p(c)}else{throw new Error("unknown format: "+r)}if(n._rgb.length===3){n._rgb.push(1)}};d.prototype.toString=function e(){if(h(this.hex)=="function"){return this.hex()}return"["+this._rgb.join(",")+"]"};var m=d;var g=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(g.Color,[null].concat(e)))};g.Color=m;g.version="2.0.3";var b=g;var y=l.unpack;var _=Math.max;var x=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=y(e,"rgb");var n=t[0];var a=t[1];var i=t[2];n=n/255;a=a/255;i=i/255;var o=1-_(n,_(a,i));var f=o<1?1/(1-o):0;var u=(1-n-o)*f;var c=(1-a-o)*f;var l=(1-i-o)*f;return[u,c,l,o]};var w=x;var k=l.unpack;var z=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=k(e,"cmyk");var t=e[0];var n=e[1];var a=e[2];var i=e[3];var o=e.length>4?e[4]:1;if(i===1){return[0,0,0,o]}return[t>=1?0:255*(1-t)*(1-i),n>=1?0:255*(1-n)*(1-i),a>=1?0:255*(1-a)*(1-i),o]};var E=z;var S=l.unpack;var A=l.type;m.prototype.cmyk=function(){return w(this._rgb)};b.cmyk=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["cmyk"])))};s.format.cmyk=E;s.autodetect.push({p:2,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=S(e,"cmyk");if(A(e)==="array"&&e.length===4){return"cmyk"}}});var j=l.unpack;var O=l.last;var T=function(e){return Math.round(e*100)/100};var M=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=j(e,"hsla");var n=O(e)||"lsa";t[0]=T(t[0]||0);t[1]=T(t[1]*100)+"%";t[2]=T(t[2]*100)+"%";if(n==="hsla"||t.length>3&&t[3]<1){t[3]=t.length>3?t[3]:1;n="hsla"}else{t.length=3}return n+"("+t.join(",")+")"};var D=M;var I=l.unpack;var C=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=I(e,"rgba");var t=e[0];var n=e[1];var a=e[2];t/=255;n/=255;a/=255;var i=Math.min(t,n,a);var o=Math.max(t,n,a);var f=(o+i)/2;var u,c;if(o===i){u=0;c=Number.NaN}else{u=f<.5?(o-i)/(o+i):(o-i)/(2-o-i)}if(t==o){c=(n-a)/(o-i)}else if(n==o){c=2+(a-t)/(o-i)}else if(a==o){c=4+(t-n)/(o-i)}c*=60;if(c<0){c+=360}if(e.length>3&&e[3]!==undefined){return[c,u,f,e[3]]}return[c,u,f]};var P=C;var F=l.unpack;var N=l.last;var L=Math.round;var R=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=F(e,"rgba");var n=N(e)||"rgb";if(n.substr(0,3)=="hsl"){return D(P(t),n)}t[0]=L(t[0]);t[1]=L(t[1]);t[2]=L(t[2]);if(n==="rgba"||t.length>3&&t[3]<1){t[3]=t.length>3?t[3]:1;n="rgba"}return n+"("+t.slice(0,n==="rgb"?3:4).join(",")+")"};var B=R;var W=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;var U=/^#?([A-Fa-f0-9]{8})$/;var q=function(e){if(e.match(W)){if(e.length===4||e.length===7){e=e.substr(1)}if(e.length===3){e=e.split("");e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]}var r=parseInt(e,16);var t=r>>16;var n=r>>8&255;var a=r&255;return[t,n,a,1]}if(e.match(U)){if(e.length===9){e=e.substr(1)}var i=parseInt(e,16);var o=i>>24&255;var f=i>>16&255;var u=i>>8&255;var c=Math.round((i&255)/255*100)/100;return[o,f,u,c]}throw new Error("unknown hex color: "+e)};var G=q;var $=l.unpack;var V=Math.round;var H=function(){var e;var r=[],t=arguments.length;while(t--)r[t]=arguments[t];r=$(r,"hsl");var n=r[0];var a=r[1];var i=r[2];var o,f,u;if(a===0){o=f=u=i*255}else{var c=[0,0,0];var l=[0,0,0];var s=i<.5?i*(1+a):i+a-i*a;var v=2*i-s;var p=n/360;c[0]=p+1/3;c[1]=p;c[2]=p-1/3;for(var h=0;h<3;h++){if(c[h]<0){c[h]+=1}if(c[h]>1){c[h]-=1}if(6*c[h]<1){l[h]=v+(s-v)*6*c[h]}else if(2*c[h]<1){l[h]=s}else if(3*c[h]<2){l[h]=v+(s-v)*(2/3-c[h])*6}else{l[h]=v}}e=[V(l[0]*255),V(l[1]*255),V(l[2]*255)],o=e[0],f=e[1],u=e[2]}if(r.length>3){return[o,f,u,r[3]]}return[o,f,u,1]};var Y=H;var X={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflower:"#6495ed",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};var Q=X;var J=/^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/;var K=/^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/;var Z=/^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/;var ee=/^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/;var re=/^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/;var te=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/;var ne=Math.round;var ae=function(e){e=e.toLowerCase().trim();if(Q[e]){return G(Q[e])}var r;if(r=e.match(J)){var t=r.slice(1,4);for(var n=0;n<3;n++){t[n]=+t[n]}t[3]=1;return t}if(r=e.match(K)){var a=r.slice(1,5);for(var i=0;i<4;i++){a[i]=+a[i]}return a}if(r=e.match(Z)){var o=r.slice(1,4);for(var f=0;f<3;f++){o[f]=ne(o[f]*2.55)}o[3]=1;return o}if(r=e.match(ee)){var u=r.slice(1,5);for(var c=0;c<3;c++){u[c]=ne(u[c]*2.55)}u[3]=+u[3];return u}if(r=e.match(re)){var l=r.slice(1,4);l[1]*=.01;l[2]*=.01;var s=Y(l);s[3]=1;return s}if(r=e.match(te)){var v=r.slice(1,4);v[1]*=.01;v[2]*=.01;var p=Y(v);p[3]=+r[4];return p}};ae.test=function(e){return J.test(e)||K.test(e)||Z.test(e)||ee.test(e)||re.test(e)||te.test(e)};var ie=ae;var oe=l.type;m.prototype.css=function(e){return B(this._rgb,e)};b.css=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["css"])))};s.format.css=ie;s.autodetect.push({p:5,test:function(e){var r=[],t=arguments.length-1;while(t-- >0)r[t]=arguments[t+1];if(!r.length&&oe(e)==="string"&&ie.test(e)){return"css"}}});var fe=l.unpack;s.format.gl=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=fe(e,"rgba");t[0]*=255;t[1]*=255;t[2]*=255;return t};b.gl=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["gl"])))};m.prototype.gl=function(){var e=this._rgb;return[e[0]/255,e[1]/255,e[2]/255,e[3]]};var ue=l.unpack;var ce=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=ue(e,"rgb");var n=t[0];var a=t[1];var i=t[2];var o=Math.min(n,a,i);var f=Math.max(n,a,i);var u=f-o;var c=u*100/255;var l=o/(255-u)*100;var s;if(u===0){s=Number.NaN}else{if(n===f){s=(a-i)/u}if(a===f){s=2+(i-n)/u}if(i===f){s=4+(n-a)/u}s*=60;if(s<0){s+=360}}return[s,c,l]};var le=ce;var se=l.unpack;var ve=Math.floor;var pe=function(){var e,r,t,n,a,i;var o=[],f=arguments.length;while(f--)o[f]=arguments[f];o=se(o,"hcg");var u=o[0];var c=o[1];var l=o[2];var s,v,p;l=l*255;var h=c*255;if(c===0){s=v=p=l}else{if(u===360){u=0}if(u>360){u-=360}if(u<0){u+=360}u/=60;var d=ve(u);var m=u-d;var g=l*(1-c);var b=g+h*(1-m);var y=g+h*m;var _=g+h;switch(d){case 0:e=[_,y,g],s=e[0],v=e[1],p=e[2];break;case 1:r=[b,_,g],s=r[0],v=r[1],p=r[2];break;case 2:t=[g,_,y],s=t[0],v=t[1],p=t[2];break;case 3:n=[g,b,_],s=n[0],v=n[1],p=n[2];break;case 4:a=[y,g,_],s=a[0],v=a[1],p=a[2];break;case 5:i=[_,g,b],s=i[0],v=i[1],p=i[2];break}}return[s,v,p,o.length>3?o[3]:1]};var he=pe;var de=l.unpack;var me=l.type;m.prototype.hcg=function(){return le(this._rgb)};b.hcg=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["hcg"])))};s.format.hcg=he;s.autodetect.push({p:1,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=de(e,"hcg");if(me(e)==="array"&&e.length===3){return"hcg"}}});var ge=l.unpack;var be=l.last;var ye=Math.round;var _e=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=ge(e,"rgba");var n=t[0];var a=t[1];var i=t[2];var o=t[3];var f=be(e)||"auto";if(o===undefined){o=1}if(f==="auto"){f=o<1?"rgba":"rgb"}n=ye(n);a=ye(a);i=ye(i);var u=n<<16|a<<8|i;var c="000000"+u.toString(16);c=c.substr(c.length-6);var l="0"+ye(o*255).toString(16);l=l.substr(l.length-2);switch(f.toLowerCase()){case"rgba":return"#"+c+l;case"argb":return"#"+l+c;default:return"#"+c}};var xe=_e;var we=l.type;m.prototype.hex=function(e){return xe(this._rgb,e)};b.hex=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["hex"])))};s.format.hex=G;s.autodetect.push({p:4,test:function(e){var r=[],t=arguments.length-1;while(t-- >0)r[t]=arguments[t+1];if(!r.length&&we(e)==="string"&&[3,4,6,7,8,9].includes(e.length)){return"hex"}}});var ke=l.unpack;var ze=l.TWOPI;var Ee=Math.min;var Se=Math.sqrt;var Ae=Math.acos;var je=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=ke(e,"rgb");var n=t[0];var a=t[1];var i=t[2];n/=255;a/=255;i/=255;var o;var f=Ee(n,a,i);var u=(n+a+i)/3;var c=u>0?1-f/u:0;if(c===0){o=NaN}else{o=(n-a+(n-i))/2;o/=Se((n-a)*(n-a)+(n-i)*(a-i));o=Ae(o);if(i>a){o=ze-o}o/=ze}return[o*360,c,u]};var Oe=je;var Te=l.unpack;var Me=l.limit;var De=l.TWOPI;var Ie=l.PITHIRD;var Ce=Math.cos;var Pe=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=Te(e,"hsi");var t=e[0];var n=e[1];var a=e[2];var i,o,f;if(isNaN(t)){t=0}if(isNaN(n)){n=0}if(t>360){t-=360}if(t<0){t+=360}t/=360;if(t<1/3){f=(1-n)/3;i=(1+n*Ce(De*t)/Ce(Ie-De*t))/3;o=1-(f+i)}else if(t<2/3){t-=1/3;i=(1-n)/3;o=(1+n*Ce(De*t)/Ce(Ie-De*t))/3;f=1-(i+o)}else{t-=2/3;o=(1-n)/3;f=(1+n*Ce(De*t)/Ce(Ie-De*t))/3;i=1-(o+f)}i=Me(a*i*3);o=Me(a*o*3);f=Me(a*f*3);return[i*255,o*255,f*255,e.length>3?e[3]:1]};var Fe=Pe;var Ne=l.unpack;var Le=l.type;m.prototype.hsi=function(){return Oe(this._rgb)};b.hsi=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["hsi"])))};s.format.hsi=Fe;s.autodetect.push({p:2,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=Ne(e,"hsi");if(Le(e)==="array"&&e.length===3){return"hsi"}}});var Re=l.unpack;var Be=l.type;m.prototype.hsl=function(){return P(this._rgb)};b.hsl=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["hsl"])))};s.format.hsl=Y;s.autodetect.push({p:2,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=Re(e,"hsl");if(Be(e)==="array"&&e.length===3){return"hsl"}}});var We=l.unpack;var Ue=Math.min;var qe=Math.max;var Ge=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=We(e,"rgb");var t=e[0];var n=e[1];var a=e[2];var i=Ue(t,n,a);var o=qe(t,n,a);var f=o-i;var u,c,l;l=o/255;if(o===0){u=Number.NaN;c=0}else{c=f/o;if(t===o){u=(n-a)/f}if(n===o){u=2+(a-t)/f}if(a===o){u=4+(t-n)/f}u*=60;if(u<0){u+=360}}return[u,c,l]};var $e=Ge;var Ve=l.unpack;var He=Math.floor;var Ye=function(){var e,r,t,n,a,i;var o=[],f=arguments.length;while(f--)o[f]=arguments[f];o=Ve(o,"hsv");var u=o[0];var c=o[1];var l=o[2];var s,v,p;l*=255;if(c===0){s=v=p=l}else{if(u===360){u=0}if(u>360){u-=360}if(u<0){u+=360}u/=60;var h=He(u);var d=u-h;var m=l*(1-c);var g=l*(1-c*d);var b=l*(1-c*(1-d));switch(h){case 0:e=[l,b,m],s=e[0],v=e[1],p=e[2];break;case 1:r=[g,l,m],s=r[0],v=r[1],p=r[2];break;case 2:t=[m,l,b],s=t[0],v=t[1],p=t[2];break;case 3:n=[m,g,l],s=n[0],v=n[1],p=n[2];break;case 4:a=[b,m,l],s=a[0],v=a[1],p=a[2];break;case 5:i=[l,m,g],s=i[0],v=i[1],p=i[2];break}}return[s,v,p,o.length>3?o[3]:1]};var Xe=Ye;var Qe=l.unpack;var Je=l.type;m.prototype.hsv=function(){return $e(this._rgb)};b.hsv=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["hsv"])))};s.format.hsv=Xe;s.autodetect.push({p:2,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=Qe(e,"hsv");if(Je(e)==="array"&&e.length===3){return"hsv"}}});var Ke={Kn:18,Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452};var Ze=l.unpack;var er=Math.pow;var rr=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=Ze(e,"rgb");var n=t[0];var a=t[1];var i=t[2];var o=ar(n,a,i);var f=o[0];var u=o[1];var c=o[2];var l=116*u-16;return[l<0?0:l,500*(f-u),200*(u-c)]};var tr=function(e){if((e/=255)<=.04045){return e/12.92}return er((e+.055)/1.055,2.4)};var nr=function(e){if(e>Ke.t3){return er(e,1/3)}return e/Ke.t2+Ke.t0};var ar=function(e,r,t){e=tr(e);r=tr(r);t=tr(t);var n=nr((.4124564*e+.3575761*r+.1804375*t)/Ke.Xn);var a=nr((.2126729*e+.7151522*r+.072175*t)/Ke.Yn);var i=nr((.0193339*e+.119192*r+.9503041*t)/Ke.Zn);return[n,a,i]};var ir=rr;var or=l.unpack;var fr=Math.pow;var ur=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=or(e,"lab");var t=e[0];var n=e[1];var a=e[2];var i,o,f,u,c,l;o=(t+16)/116;i=isNaN(n)?o:o+n/500;f=isNaN(a)?o:o-a/200;o=Ke.Yn*lr(o);i=Ke.Xn*lr(i);f=Ke.Zn*lr(f);u=cr(3.2404542*i-1.5371385*o-.4985314*f);c=cr(-.969266*i+1.8760108*o+.041556*f);l=cr(.0556434*i-.2040259*o+1.0572252*f);return[u,c,l,e.length>3?e[3]:1]};var cr=function(e){return 255*(e<=.00304?12.92*e:1.055*fr(e,1/2.4)-.055)};var lr=function(e){return e>Ke.t1?e*e*e:Ke.t2*(e-Ke.t0)};var sr=ur;var vr=l.unpack;var pr=l.type;m.prototype.lab=function(){return ir(this._rgb)};b.lab=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["lab"])))};s.format.lab=sr;s.autodetect.push({p:2,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=vr(e,"lab");if(pr(e)==="array"&&e.length===3){return"lab"}}});var hr=l.unpack;var dr=l.RAD2DEG;var mr=Math.sqrt;var gr=Math.atan2;var br=Math.round;var yr=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=hr(e,"lab");var n=t[0];var a=t[1];var i=t[2];var o=mr(a*a+i*i);var f=(gr(i,a)*dr+360)%360;if(br(o*1e4)===0){f=Number.NaN}return[n,o,f]};var _r=yr;var xr=l.unpack;var wr=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=xr(e,"rgb");var n=t[0];var a=t[1];var i=t[2];var o=ir(n,a,i);var f=o[0];var u=o[1];var c=o[2];return _r(f,u,c)};var kr=wr;var zr=l.unpack;var Er=l.DEG2RAD;var Sr=Math.sin;var Ar=Math.cos;var jr=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=zr(e,"lch");var n=t[0];var a=t[1];var i=t[2];if(isNaN(i)){i=0}i=i*Er;return[n,Ar(i)*a,Sr(i)*a]};var Or=jr;var Tr=l.unpack;var Mr=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=Tr(e,"lch");var t=e[0];var n=e[1];var a=e[2];var i=Or(t,n,a);var o=i[0];var f=i[1];var u=i[2];var c=sr(o,f,u);var l=c[0];var s=c[1];var v=c[2];return[l,s,v,e.length>3?e[3]:1]};var Dr=Mr;var Ir=l.unpack;var Cr=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=Ir(e,"hcl").reverse();return Dr.apply(void 0,t)};var Pr=Cr;var Fr=l.unpack;var Nr=l.type;m.prototype.lch=function(){return kr(this._rgb)};m.prototype.hcl=function(){return kr(this._rgb).reverse()};b.lch=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["lch"])))};b.hcl=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["hcl"])))};s.format.lch=Dr;s.format.hcl=Pr;["lch","hcl"].forEach(function(e){return s.autodetect.push({p:2,test:function(){var r=[],t=arguments.length;while(t--)r[t]=arguments[t];r=Fr(r,e);if(Nr(r)==="array"&&r.length===3){return e}}})});var Lr=l.type;m.prototype.name=function(){var e=xe(this._rgb,"rgb");for(var r=0,t=Object.keys(Q);r<t.length;r+=1){var n=t[r];if(Q[n]===e){return n.toLowerCase()}}return e};s.format.named=function(e){e=e.toLowerCase();if(Q[e]){return G(Q[e])}throw new Error("unknown color name: "+e)};s.autodetect.push({p:5,test:function(e){var r=[],t=arguments.length-1;while(t-- >0)r[t]=arguments[t+1];if(!r.length&&Lr(e)==="string"&&Q[e.toLowerCase()]){return"named"}}});var Rr=l.unpack;var Br=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=Rr(e,"rgb");var n=t[0];var a=t[1];var i=t[2];return(n<<16)+(a<<8)+i};var Wr=Br;var Ur=l.type;var qr=function(e){if(Ur(e)=="number"&&e>=0&&e<=16777215){var r=e>>16;var t=e>>8&255;var n=e&255;return[r,t,n,1]}throw new Error("unknown num color: "+e)};var Gr=qr;var $r=l.type;m.prototype.num=function(){return Wr(this._rgb)};b.num=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["num"])))};s.format.num=Gr;s.autodetect.push({p:5,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];if(e.length===1&&$r(e[0])==="number"&&e[0]>=0&&e[0]<=16777215){return"num"}}});var Vr=l.unpack;var Hr=l.type;var Yr=Math.round;m.prototype.rgb=function(e){if(e===void 0)e=true;if(e===false){return this._rgb.slice(0,3)}return this._rgb.slice(0,3).map(Yr)};m.prototype.rgba=function(e){if(e===void 0)e=true;return this._rgb.slice(0,4).map(function(r,t){return t<3?e===false?r:Yr(r):r})};b.rgb=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["rgb"])))};s.format.rgb=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=Vr(e,"rgba");if(t[3]===undefined){t[3]=1}return t};s.autodetect.push({p:3,test:function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];e=Vr(e,"rgba");if(Hr(e)==="array"&&(e.length===3||e.length===4&&Hr(e[3])=="number"&&e[3]>=0&&e[3]<=1)){return"rgb"}}});var Xr=Math.log;var Qr=function(e){var r=e/100;var t,n,a;if(r<66){t=255;n=-155.25485562709179-.44596950469579133*(n=r-2)+104.49216199393888*Xr(n);a=r<20?0:-254.76935184120902+.8274096064007395*(a=r-10)+115.67994401066147*Xr(a)}else{t=351.97690566805693+.114206453784165*(t=r-55)-40.25366309332127*Xr(t);n=325.4494125711974+.07943456536662342*(n=r-50)-28.0852963507957*Xr(n);a=255}return[t,n,a,1]};var Jr=Qr;var Kr=l.unpack;var Zr=Math.round;var et=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];var t=Kr(e,"rgb");var n=t[0],a=t[2];var i=1e3;var o=4e4;var f=.4;var u;while(o-i>f){u=(o+i)*.5;var c=Jr(u);if(c[2]/c[0]>=a/n){o=u}else{i=u}}return Zr(u)};var rt=et;m.prototype.temp=m.prototype.kelvin=m.prototype.temperature=function(){return rt(this._rgb)};b.temp=b.kelvin=b.temperature=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];return new(Function.prototype.bind.apply(m,[null].concat(e,["temp"])))};s.format.temp=s.format.kelvin=s.format.temperature=Jr;var tt=l.type;m.prototype.alpha=function(e,r){if(r===void 0)r=false;if(e!==undefined&&tt(e)==="number"){if(r){this._rgb[3]=e;return this}return new m([this._rgb[0],this._rgb[1],this._rgb[2],e],"rgb")}return this._rgb[3]};m.prototype.clipped=function(){return this._rgb._clipped||false};m.prototype.darken=function(e){if(e===void 0)e=1;var r=this;var t=r.lab();t[0]-=Ke.Kn*e;return new m(t,"lab").alpha(r.alpha(),true)};m.prototype.brighten=function(e){if(e===void 0)e=1;return this.darken(-e)};m.prototype.darker=m.prototype.darken;m.prototype.brighter=m.prototype.brighten;m.prototype.get=function(e){var r=e.split(".");var t=r[0];var n=r[1];var a=this[t]();if(n){var i=t.indexOf(n);if(i>-1){return a[i]}throw new Error("unknown channel "+n+" in mode "+t)}else{return a}};var nt=l.type;var at=Math.pow;var it=1e-7;var ot=20;m.prototype.luminance=function(e){if(e!==undefined&&nt(e)==="number"){if(e===0){return new m([0,0,0,this._rgb[3]],"rgb")}if(e===1){return new m([255,255,255,this._rgb[3]],"rgb")}var r=this.luminance();var t="rgb";var n=ot;var a=function(r,i){var o=r.interpolate(i,.5,t);var f=o.luminance();if(Math.abs(e-f)<it||!n--){return o}return f>e?a(r,o):a(o,i)};var i=(r>e?a(new m([0,0,0]),this):a(this,new m([255,255,255]))).rgb();return new m(i.concat([this._rgb[3]]))}return ft.apply(void 0,this._rgb.slice(0,3))};var ft=function(e,r,t){e=ut(e);r=ut(r);t=ut(t);return.2126*e+.7152*r+.0722*t};var ut=function(e){e/=255;return e<=.03928?e/12.92:at((e+.055)/1.055,2.4)};var ct={};var lt=l.type;var st=function(e,r,t){if(t===void 0)t=.5;var n=[],a=arguments.length-3;while(a-- >0)n[a]=arguments[a+3];var i=n[0]||"lrgb";if(!ct[i]&&!n.length){i=Object.keys(ct)[0]}if(!ct[i]){throw new Error("interpolation mode "+i+" is not defined")}if(lt(e)!=="object"){e=new m(e)}if(lt(r)!=="object"){r=new m(r)}return ct[i](e,r,t).alpha(e.alpha()+t*(r.alpha()-e.alpha()))};m.prototype.mix=m.prototype.interpolate=function(e,r){if(r===void 0)r=.5;var t=[],n=arguments.length-2;while(n-- >0)t[n]=arguments[n+2];return st.apply(void 0,[this,e,r].concat(t))};m.prototype.premultiply=function(e){if(e===void 0)e=false;var r=this._rgb;var t=r[3];if(e){this._rgb=[r[0]*t,r[1]*t,r[2]*t,t];return this}else{return new m([r[0]*t,r[1]*t,r[2]*t,t],"rgb")}};m.prototype.saturate=function(e){if(e===void 0)e=1;var r=this;var t=r.lch();t[1]+=Ke.Kn*e;if(t[1]<0){t[1]=0}return new m(t,"lch").alpha(r.alpha(),true)};m.prototype.desaturate=function(e){if(e===void 0)e=1;return this.saturate(-e)};var vt=l.type;m.prototype.set=function(e,r,t){if(t===void 0)t=false;var n=e.split(".");var a=n[0];var i=n[1];var o=this[a]();if(i){var f=a.indexOf(i);if(f>-1){if(vt(r)=="string"){switch(r.charAt(0)){case"+":o[f]+=+r;break;case"-":o[f]+=+r;break;case"*":o[f]*=+r.substr(1);break;case"/":o[f]/=+r.substr(1);break;default:o[f]=+r}}else if(vt(r)==="number"){o[f]=r}else{throw new Error("unsupported value for Color.set")}var u=new m(o,a);if(t){this._rgb=u._rgb;return this}return u}throw new Error("unknown channel "+i+" in mode "+a)}else{return o}};var pt=function(e,r,t){var n=e._rgb;var a=r._rgb;return new m(n[0]+t*(a[0]-n[0]),n[1]+t*(a[1]-n[1]),n[2]+t*(a[2]-n[2]),"rgb")};ct.rgb=pt;var ht=Math.sqrt;var dt=Math.pow;var mt=function(e,r,t){var n=e._rgb;var a=n[0];var i=n[1];var o=n[2];var f=r._rgb;var u=f[0];var c=f[1];var l=f[2];return new m(ht(dt(a,2)*(1-t)+dt(u,2)*t),ht(dt(i,2)*(1-t)+dt(c,2)*t),ht(dt(o,2)*(1-t)+dt(l,2)*t),"rgb")};ct.lrgb=mt;var gt=function(e,r,t){var n=e.lab();var a=r.lab();return new m(n[0]+t*(a[0]-n[0]),n[1]+t*(a[1]-n[1]),n[2]+t*(a[2]-n[2]),"lab")};ct.lab=gt;var bt=function(e,r,t,n){var a,i;var o,f;if(n==="hsl"){o=e.hsl();f=r.hsl()}else if(n==="hsv"){o=e.hsv();f=r.hsv()}else if(n==="hcg"){o=e.hcg();f=r.hcg()}else if(n==="hsi"){o=e.hsi();f=r.hsi()}else if(n==="lch"||n==="hcl"){n="hcl";o=e.hcl();f=r.hcl()}var u,c,l,s,v,p;if(n.substr(0,1)==="h"){a=o,u=a[0],l=a[1],v=a[2];i=f,c=i[0],s=i[1],p=i[2]}var h,d,g,b;if(!isNaN(u)&&!isNaN(c)){if(c>u&&c-u>180){b=c-(u+360)}else if(c<u&&u-c>180){b=c+360-u}else{b=c-u}d=u+t*b}else if(!isNaN(u)){d=u;if((p==1||p==0)&&n!="hsv"){h=l}}else if(!isNaN(c)){d=c;if((v==1||v==0)&&n!="hsv"){h=s}}else{d=Number.NaN}if(h===undefined){h=l+t*(s-l)}g=v+t*(p-v);return new m([d,h,g],n)};var yt=function(e,r,t){return bt(e,r,t,"lch")};ct.lch=yt;ct.hcl=yt;var _t=function(e,r,t){var n=e.num();var a=r.num();return new m(n+t*(a-n),"num")};ct.num=_t;var xt=function(e,r,t){return bt(e,r,t,"hcg")};ct.hcg=xt;var wt=function(e,r,t){return bt(e,r,t,"hsi")};ct.hsi=wt;var kt=function(e,r,t){return bt(e,r,t,"hsl")};ct.hsl=kt;var zt=function(e,r,t){return bt(e,r,t,"hsv")};ct.hsv=zt;var Et=l.clip_rgb;var St=Math.pow;var At=Math.sqrt;var jt=Math.PI;var Ot=Math.cos;var Tt=Math.sin;var Mt=Math.atan2;var Dt=function(e,r){if(r===void 0)r="lrgb";var t=e.length;e=e.map(function(e){return new m(e)});if(r==="lrgb"){return It(e)}var n=e.shift();var a=n.get(r);var i=[];var o=0;var f=0;for(var u=0;u<a.length;u++){a[u]=a[u]||0;i.push(isNaN(a[u])?0:1);if(r.charAt(u)==="h"&&!isNaN(a[u])){var c=a[u]/180*jt;o+=Ot(c);f+=Tt(c)}}var l=n.alpha();e.forEach(function(e){var t=e.get(r);l+=e.alpha();for(var n=0;n<a.length;n++){if(!isNaN(t[n])){i[n]++;if(r.charAt(n)==="h"){var u=t[n]/180*jt;o+=Ot(u);f+=Tt(u)}else{a[n]+=t[n]}}}});for(var s=0;s<a.length;s++){if(r.charAt(s)==="h"){var v=Mt(f/i[s],o/i[s])/jt*180;while(v<0){v+=360}while(v>=360){v-=360}a[s]=v}else{a[s]=a[s]/i[s]}}l/=t;return new m(a,r).alpha(l>.99999?1:l,true)};var It=function(e){var r=e.length;var t=1/r;var n=[0,0,0,0];for(var a=0,i=e;a<i.length;a+=1){var o=i[a];var f=o._rgb;n[0]+=St(f[0],2)*t;n[1]+=St(f[1],2)*t;n[2]+=St(f[2],2)*t;n[3]+=f[3]*t}n[0]=At(n[0]);n[1]=At(n[1]);n[2]=At(n[2]);if(n[3]>.9999999){n[3]=1}return new m(Et(n))};var Ct=l.type;var Pt=Math.pow;var Ft=function(e){var r="rgb";var t=b("#ccc");var n=0;var a=[0,1];var i=[];var o=[0,0];var f=false;var u=[];var c=false;var l=0;var s=1;var v=false;var p={};var h=true;var d=1;var m=function(e){e=e||["#fff","#000"];if(e&&Ct(e)==="string"&&b.brewer&&b.brewer[e.toLowerCase()]){e=b.brewer[e.toLowerCase()]}if(Ct(e)==="array"){if(e.length===1){e=[e[0],e[0]]}e=e.slice(0);for(var r=0;r<e.length;r++){e[r]=b(e[r])}i.length=0;for(var t=0;t<e.length;t++){i.push(t/(e.length-1))}}x();return u=e};var g=function(e){if(f!=null){var r=f.length-1;var t=0;while(t<r&&e>=f[t]){t++}return t-1}return 0};var y=function(e){return e};var _=function(e,n){var a,c;if(n==null){n=false}if(isNaN(e)||e===null){return t}if(!n){if(f&&f.length>2){var v=g(e);c=v/(f.length-2)}else if(s!==l){c=(e-l)/(s-l)}else{c=1}}else{c=e}if(!n){c=y(c)}if(d!==1){c=Pt(c,d)}c=o[0]+c*(1-o[0]-o[1]);c=Math.min(1,Math.max(0,c));var m=Math.floor(c*1e4);if(h&&p[m]){a=p[m]}else{if(Ct(u)==="array"){for(var _=0;_<i.length;_++){var x=i[_];if(c<=x){a=u[_];break}if(c>=x&&_===i.length-1){a=u[_];break}if(c>x&&c<i[_+1]){c=(c-x)/(i[_+1]-x);a=b.interpolate(u[_],u[_+1],c,r);break}}}else if(Ct(u)==="function"){a=u(c)}if(h){p[m]=a}}return a};var x=function(){return p={}};m(e);var w=function(e){var r=b(_(e));if(c&&r[c]){return r[c]()}else{return r}};w.classes=function(e){if(e!=null){if(Ct(e)==="array"){f=e;a=[e[0],e[e.length-1]]}else{var r=b.analyze(a);if(e===0){f=[r.min,r.max]}else{f=b.limits(r,"e",e)}}return w}return f};w.domain=function(e){if(!arguments.length){return a}l=e[0];s=e[e.length-1];i=[];var r=u.length;if(e.length===r&&l!==s){for(var t=0,n=Array.from(e);t<n.length;t+=1){var o=n[t];i.push((o-l)/(s-l))}}else{for(var f=0;f<r;f++){i.push(f/(r-1))}}a=[l,s];return w};w.mode=function(e){if(!arguments.length){return r}r=e;x();return w};w.range=function(e,r){m(e,r);return w};w.out=function(e){c=e;return w};w.spread=function(e){if(!arguments.length){return n}n=e;return w};w.correctLightness=function(e){if(e==null){e=true}v=e;x();if(v){y=function(e){var r=_(0,true).lab()[0];var t=_(1,true).lab()[0];var n=r>t;var a=_(e,true).lab()[0];var i=r+(t-r)*e;var o=a-i;var f=0;var u=1;var c=20;while(Math.abs(o)>.01&&c-- >0){(function(){if(n){o*=-1}if(o<0){f=e;e+=(u-e)*.5}else{u=e;e+=(f-e)*.5}a=_(e,true).lab()[0];return o=a-i})()}return e}}else{y=function(e){return e}}return w};w.padding=function(e){if(e!=null){if(Ct(e)==="number"){e=[e,e]}o=e;return w}else{return o}};w.colors=function(r,t){if(arguments.length<2){t="hex"}var n=[];if(arguments.length===0){n=u.slice(0)}else if(r===1){n=[w(.5)]}else if(r>1){var i=a[0];var o=a[1]-i;n=Nt(0,r,false).map(function(e){return w(i+e/(r-1)*o)})}else{e=[];var c=[];if(f&&f.length>2){for(var l=1,s=f.length,v=1<=s;v?l<s:l>s;v?l++:l--){c.push((f[l-1]+f[l])*.5)}}else{c=a}n=c.map(function(e){return w(e)})}if(b[t]){n=n.map(function(e){return e[t]()})}return n};w.cache=function(e){if(e!=null){h=e;return w}else{return h}};w.gamma=function(e){if(e!=null){d=e;return w}else{return d}};w.nodata=function(e){if(e!=null){t=b(e);return w}else{return t}};return w};function Nt(e,r,t){var n=[];var a=e<r;var i=!t?r:a?r+1:r-1;for(var o=e;a?o<i:o>i;a?o++:o--){n.push(o)}return n}var Lt=function(e){var r,t,n;var a,i,o,f;e=e.map(function(e){return new m(e)});if(e.length===2){r=e.map(function(e){return e.lab()}),i=r[0],o=r[1];a=function(e){var r=[0,1,2].map(function(r){return i[r]+e*(o[r]-i[r])});return new m(r,"lab")}}else if(e.length===3){t=e.map(function(e){return e.lab()}),i=t[0],o=t[1],f=t[2];a=function(e){var r=[0,1,2].map(function(r){return(1-e)*(1-e)*i[r]+2*(1-e)*e*o[r]+e*e*f[r]});return new m(r,"lab")}}else if(e.length===4){var u;n=e.map(function(e){return e.lab()}),i=n[0],o=n[1],f=n[2],u=n[3];a=function(e){var r=[0,1,2].map(function(r){return(1-e)*(1-e)*(1-e)*i[r]+3*(1-e)*(1-e)*e*o[r]+3*(1-e)*e*e*f[r]+e*e*e*u[r]});return new m(r,"lab")}}else if(e.length===5){var c=Lt(e.slice(0,3));var l=Lt(e.slice(2,5));a=function(e){if(e<.5){return c(e*2)}else{return l((e-.5)*2)}}}return a};var Rt=function(e){var r=Lt(e);r.scale=function(){return Ft(r)};return r};var Bt=function(e,r,t){if(!Bt[t]){throw new Error("unknown blend mode "+t)}return Bt[t](e,r)};var Wt=function(e){return function(r,t){var n=b(t).rgb();var a=b(r).rgb();return b.rgb(e(n,a))}};var Ut=function(e){return function(r,t){var n=[];n[0]=e(r[0],t[0]);n[1]=e(r[1],t[1]);n[2]=e(r[2],t[2]);return n}};var qt=function(e){return e};var Gt=function(e,r){return e*r/255};var $t=function(e,r){return e>r?r:e};var Vt=function(e,r){return e>r?e:r};var Ht=function(e,r){return 255*(1-(1-e/255)*(1-r/255))};var Yt=function(e,r){return r<128?2*e*r/255:255*(1-2*(1-e/255)*(1-r/255))};var Xt=function(e,r){return 255*(1-(1-r/255)/(e/255))};var Qt=function(e,r){if(e===255){return 255}e=255*(r/255)/(1-e/255);return e>255?255:e};Bt.normal=Wt(Ut(qt));Bt.multiply=Wt(Ut(Gt));Bt.screen=Wt(Ut(Ht));Bt.overlay=Wt(Ut(Yt));Bt.darken=Wt(Ut($t));Bt.lighten=Wt(Ut(Vt));Bt.dodge=Wt(Ut(Qt));Bt.burn=Wt(Ut(Xt));var Jt=Bt;var Kt=l.type;var Zt=l.clip_rgb;var en=l.TWOPI;var rn=Math.pow;var tn=Math.sin;var nn=Math.cos;var an=function(e,r,t,n,a){if(e===void 0)e=300;if(r===void 0)r=-1.5;if(t===void 0)t=1;if(n===void 0)n=1;if(a===void 0)a=[0,1];var i=0,o;if(Kt(a)==="array"){o=a[1]-a[0]}else{o=0;a=[a,a]}var f=function(f){var u=en*((e+120)/360+r*f);var c=rn(a[0]+o*f,n);var l=i!==0?t[0]+f*i:t;var s=l*c*(1-c)/2;var v=nn(u);var p=tn(u);var h=c+s*(-.14861*v+1.78277*p);var d=c+s*(-.29227*v-.90649*p);var m=c+s*(+1.97294*v);return b(Zt([h*255,d*255,m*255,1]))};f.start=function(r){if(r==null){return e}e=r;return f};f.rotations=function(e){if(e==null){return r}r=e;return f};f.gamma=function(e){if(e==null){return n}n=e;return f};f.hue=function(e){if(e==null){return t}t=e;if(Kt(t)==="array"){i=t[1]-t[0];if(i===0){t=t[1]}}else{i=0}return f};f.lightness=function(e){if(e==null){return a}if(Kt(e)==="array"){a=e;o=e[1]-e[0]}else{a=[e,e];o=0}return f};f.scale=function(){return b.scale(f)};f.hue(t);return f};var on="0123456789abcdef";var fn=Math.floor;var un=Math.random;var cn=function(){var e="#";for(var r=0;r<6;r++){e+=on.charAt(fn(un()*16))}return new m(e,"hex")};var ln=Math.log;var sn=Math.pow;var vn=Math.floor;var pn=Math.abs;var hn=function(e,r){if(r===void 0)r=null;var t={min:Number.MAX_VALUE,max:Number.MAX_VALUE*-1,sum:0,values:[],count:0};if(o(e)==="object"){e=Object.values(e)}e.forEach(function(e){if(r&&o(e)==="object"){e=e[r]}if(e!==undefined&&e!==null&&!isNaN(e)){t.values.push(e);t.sum+=e;if(e<t.min){t.min=e}if(e>t.max){t.max=e}t.count+=1}});t.domain=[t.min,t.max];t.limits=function(e,r){return dn(t,e,r)};return t};var dn=function(e,r,t){if(r===void 0)r="equal";if(t===void 0)t=7;if(o(e)=="array"){e=hn(e)}var n=e.min;var a=e.max;var i=e.values.sort(function(e,r){return e-r});if(t===1){return[n,a]}var f=[];if(r.substr(0,1)==="c"){f.push(n);f.push(a)}if(r.substr(0,1)==="e"){f.push(n);for(var u=1;u<t;u++){f.push(n+u/t*(a-n))}f.push(a)}else if(r.substr(0,1)==="l"){if(n<=0){throw new Error("Logarithmic scales are only possible for values > 0")}var c=Math.LOG10E*ln(n);var l=Math.LOG10E*ln(a);f.push(n);for(var s=1;s<t;s++){f.push(sn(10,c+s/t*(l-c)))}f.push(a)}else if(r.substr(0,1)==="q"){f.push(n);for(var v=1;v<t;v++){var p=(i.length-1)*v/t;var h=vn(p);if(h===p){f.push(i[h])}else{var d=p-h;f.push(i[h]*(1-d)+i[h+1]*d)}}f.push(a)}else if(r.substr(0,1)==="k"){var m;var g=i.length;var b=new Array(g);var y=new Array(t);var _=true;var x=0;var w=null;w=[];w.push(n);for(var k=1;k<t;k++){w.push(n+k/t*(a-n))}w.push(a);while(_){for(var z=0;z<t;z++){y[z]=0}for(var E=0;E<g;E++){var S=i[E];var A=Number.MAX_VALUE;var j=void 0;for(var O=0;O<t;O++){var T=pn(w[O]-S);if(T<A){A=T;j=O}y[j]++;b[E]=j}}var M=new Array(t);for(var D=0;D<t;D++){M[D]=null}for(var I=0;I<g;I++){m=b[I];if(M[m]===null){M[m]=i[I]}else{M[m]+=i[I]}}for(var C=0;C<t;C++){M[C]*=1/y[C]}_=false;for(var P=0;P<t;P++){if(M[P]!==w[P]){_=true;break}}w=M;x++;if(x>200){_=false}}var F={};for(var N=0;N<t;N++){F[N]=[]}for(var L=0;L<g;L++){m=b[L];F[m].push(i[L])}var R=[];for(var B=0;B<t;B++){R.push(F[B][0]);R.push(F[B][F[B].length-1])}R=R.sort(function(e,r){return e-r});f.push(R[0]);for(var W=1;W<R.length;W+=2){var U=R[W];if(!isNaN(U)&&f.indexOf(U)===-1){f.push(U)}}}return f};var mn={analyze:hn,limits:dn};var gn=function(e,r){e=new m(e);r=new m(r);var t=e.luminance();var n=r.luminance();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)};var bn=Math.sqrt;var yn=Math.atan2;var _n=Math.abs;var xn=Math.cos;var wn=Math.PI;var kn=function(e,r,t,n){if(t===void 0)t=1;if(n===void 0)n=1;e=new m(e);r=new m(r);var a=Array.from(e.lab());var i=a[0];var o=a[1];var f=a[2];var u=Array.from(r.lab());var c=u[0];var l=u[1];var s=u[2];var v=bn(o*o+f*f);var p=bn(l*l+s*s);var h=i<16?.511:.040975*i/(1+.01765*i);var d=.0638*v/(1+.0131*v)+.638;var g=v<1e-6?0:yn(f,o)*180/wn;while(g<0){g+=360}while(g>=360){g-=360}var b=g>=164&&g<=345?.56+_n(.2*xn(wn*(g+168)/180)):.36+_n(.4*xn(wn*(g+35)/180));var y=v*v*v*v;var _=bn(y/(y+1900));var x=d*(_*b+1-_);var w=i-c;var k=v-p;var z=o-l;var E=f-s;var S=z*z+E*E-k*k;var A=w/(t*h);var j=k/(n*d);var O=x;return bn(A*A+j*j+S/(O*O))};var zn=function(e,r,t){if(t===void 0)t="lab";e=new m(e);r=new m(r);var n=e.get(t);var a=r.get(t);var i=0;for(var o in n){var f=(n[o]||0)-(a[o]||0);i+=f*f}return Math.sqrt(i)};var En=function(){var e=[],r=arguments.length;while(r--)e[r]=arguments[r];try{new(Function.prototype.bind.apply(m,[null].concat(e)));return true}catch(t){return false}};var Sn={cool:function e(){return Ft([b.hsl(180,1,.9),b.hsl(250,.7,.4)])},hot:function e(){return Ft(["#000","#f00","#ff0","#fff"],[0,.25,.75,1]).mode("rgb")}};var An={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]};for(var jn=0,On=Object.keys(An);jn<On.length;jn+=1){var Tn=On[jn];An[Tn.toLowerCase()]=An[Tn]}var Mn=An;b.average=Dt;b.bezier=Rt;b.blend=Jt;b.cubehelix=an;b.mix=b.interpolate=st;b.random=cn;b.scale=Ft;b.analyze=mn.analyze;b.contrast=gn;b.deltaE=kn;b.distance=zn;b.limits=mn.limits;b.valid=En;b.scales=Sn;b.colors=Q;b.brewer=Mn;var Dn=b;return Dn})},"62bf":function(e,r,t){e.exports=t("4869")},"62e4":function(e,r){e.exports=function(e){if(!e.webpackPolyfill){e.deprecate=function(){};e.paths=[];if(!e.children)e.children=[];Object.defineProperty(e,"loaded",{enumerable:true,get:function(){return e.l}});Object.defineProperty(e,"id",{enumerable:true,get:function(){return e.i}});e.webpackPolyfill=1}return e}},"6ac8":function(e,r,t){var n=t("3955");e.exports=function(e,r,t,a){try{return a?r(n(t)[0],t[1]):r(t)}catch(o){var i=e["return"];if(i!==undefined)n(i.call(e));throw o}}},"6bc6":function(e,r){e.exports=t;function t(e,r){return e[0]*r[0]+e[1]*r[1]}},"6e53":function(e,r){e.exports=t;function t(e,r,t){var n=r[0],a=r[1];e[0]=t[0]*n+t[2]*a+t[4];e[1]=t[1]*n+t[3]*a+t[5];return e}},"6f2f":function(e,r,t){"use strict";var n=t("3546");var a=t("8be2");var i=t("5159");var o=t("a911");var f=t("5f45");var u=Object.assign;e.exports=!u||t("c0f6")(function(){var e={};var r={};var t=Symbol();var n="abcdefghijklmnopqrst";e[t]=7;n.split("").forEach(function(e){r[e]=e});return u({},e)[t]!=7||Object.keys(u({},r)).join("")!=n})?function e(r,t){var u=o(r);var c=arguments.length;var l=1;var s=a.f;var v=i.f;while(c>l){var p=f(arguments[l++]);var h=s?n(p).concat(s(p)):n(p);var d=h.length;var m=0;var g;while(d>m)if(v.call(p,g=h[m++]))u[g]=p[g]}return u}:u},"706c":function(e,r){e.exports=t;function t(e,r,t){e[0]=r[0]-t[0];e[1]=r[1]-t[1];return e}},7225:function(e,r,t){var n=t("f861").document;e.exports=n&&n.documentElement},7341:function(e,r,t){"use strict";var n=t("c853");var a=t("39c6");var i=t("781d");var o=t("46ab");e.exports=t("a695")(Array,"Array",function(e,r){this._t=o(e);this._i=0;this._k=r},function(){var e=this._t;var r=this._k;var t=this._i++;if(!e||t>=e.length){this._t=undefined;return a(1)}if(r=="keys")return a(0,t);if(r=="values")return a(0,e[t]);return a(0,[t,e[t]])},"values");i.Arguments=i.Array;n("keys");n("values");n("entries")},"74e5":function(e,r){e.exports=t;function t(e,r,t){e[0]=Math.min(r[0],t[0]);e[1]=Math.min(r[1],t[1]);return e}},"760f":function(e,r,t){var n=t("a2ce");e.exports=Array.isArray||function e(r){return n(r)=="Array"}},7621:function(e,r,t){"use strict";var n=t("22fe");var a=t("9724")(6);var i="findIndex";var o=true;if(i in[])Array(1)[i](function(){o=false});n(n.P+n.F*o,"Array",{findIndex:function e(r){return a(this,r,arguments.length>1?arguments[1]:undefined)}});t("c853")(i)},7813:function(e,r,t){t("d2c4")("replace",2,function(e,r,t){return[function n(a,i){"use strict";var o=e(this);var f=a==undefined?undefined:a[r];return f!==undefined?f.call(a,o,i):t.call(String(o),a,i)},t]})},"781d":function(e,r){e.exports={}},"7aae":function(e,r,t){var n=t("d8a8");var a=t("46ab");var i=t("c33d")(false);var o=t("7f4f")("IE_PROTO");e.exports=function(e,r){var t=a(e);var f=0;var u=[];var c;for(c in t)if(c!=o)n(t,c)&&u.push(c);while(r.length>f)if(n(t,c=r[f++])){~i(u,c)||u.push(c)}return u}},"7f4f":function(e,r,t){var n=t("2679")("keys");var a=t("4509");e.exports=function(e){return n[e]||(n[e]=a(e))}},"84c3":function(e,r,t){var n=t("3955");var a=t("5d0d");var i=t("499f");var o=t("7f4f")("IE_PROTO");var f=function(){};var u="prototype";var c=function(){var e=t("da3a")("iframe");var r=i.length;var n="<";var a=">";var o;e.style.display="none";t("7225").appendChild(e);e.src="javascript:";o=e.contentWindow.document;o.open();o.write(n+"script"+a+"document.F=Object"+n+"/script"+a);o.close();c=o.F;while(r--)delete c[u][i[r]];return c()};e.exports=Object.create||function e(r,t){var i;if(r!==null){f[u]=n(r);i=new f;f[u]=null;i[o]=r}else i=c();return t===undefined?i:a(i,t)}},"8b78":function(e,r){e.exports=false},"8be2":function(e,r){r.f=Object.getOwnPropertySymbols},9523:function(e,r){function t(e,r,t){if(r in e){Object.defineProperty(e,r,{value:t,enumerable:true,configurable:true,writable:true})}else{e[r]=t}return e}e.exports=t},9544:function(e,r){e.exports=function(e){if(typeof e!="function")throw TypeError(e+" is not a function!");return e}},"963b":function(e,r,t){var n=t("3546");var a=t("46ab");var i=t("5159").f;e.exports=function(e){return function(r){var t=a(r);var o=n(t);var f=o.length;var u=0;var c=[];var l;while(f>u)if(i.call(t,l=o[u++])){c.push(e?[l,t[l]]:t[l])}return c}}},"970b":function(e,r){function t(e,r){if(!(e instanceof r)){throw new TypeError("Cannot call a class as a function")}}e.exports=t},9724:function(e,r,t){var n=t("0e26");var a=t("5f45");var i=t("a911");var o=t("d7d0");var f=t("bee2");e.exports=function(e,r){var t=e==1;var u=e==2;var c=e==3;var l=e==4;var s=e==6;var v=e==5||s;var p=r||f;return function(r,f,h){var d=i(r);var m=a(d);var g=n(f,h,3);var b=o(m.length);var y=0;var _=t?p(r,b):u?p(r,0):undefined;var x,w;for(;b>y;y++)if(v||y in m){x=m[y];w=g(x,y,d);if(e){if(t)_[y]=w;else if(w)switch(e){case 3:return true;case 5:return x;case 6:return y;case 2:_.push(x)}else if(l)return false}}return s?-1:c||l?l:_}}},"97bb":function(e,r,t){"use strict";var n=t("98ab").f;var a=t("84c3");var i=t("1385");var o=t("0e26");var f=t("9a92");var u=t("d2b4");var c=t("a695");var l=t("39c6");var s=t("d01d");var v=t("e2e5");var p=t("404a").fastKey;var h=t("11da");var d=v?"_s":"size";var m=function(e,r){var t=p(r);var n;if(t!=="F")return e._i[t];for(n=e._f;n;n=n.n){if(n.k==r)return n}};e.exports={getConstructor:function(e,r,t,c){var l=e(function(e,n){f(e,l,r,"_i");e._t=r;e._i=a(null);e._f=undefined;e._l=undefined;e[d]=0;if(n!=undefined)u(n,t,e[c],e)});i(l.prototype,{clear:function e(){for(var t=h(this,r),n=t._i,a=t._f;a;a=a.n){a.r=true;if(a.p)a.p=a.p.n=undefined;delete n[a.i]}t._f=t._l=undefined;t[d]=0},delete:function(e){var t=h(this,r);var n=m(t,e);if(n){var a=n.n;var i=n.p;delete t._i[n.i];n.r=true;if(i)i.n=a;if(a)a.p=i;if(t._f==n)t._f=a;if(t._l==n)t._l=i;t[d]--}return!!n},forEach:function e(t){h(this,r);var n=o(t,arguments.length>1?arguments[1]:undefined,3);var a;while(a=a?a.n:this._f){n(a.v,a.k,this);while(a&&a.r)a=a.p}},has:function e(t){return!!m(h(this,r),t)}});if(v)n(l.prototype,"size",{get:function(){return h(this,r)[d]}});return l},def:function(e,r,t){var n=m(e,r);var a,i;if(n){n.v=t}else{e._l=n={i:i=p(r,true),k:r,v:t,p:a=e._l,n:undefined,r:false};if(!e._f)e._f=n;if(a)a.n=n;e[d]++;if(i!=="F")e._i[i]=n}return e},getEntry:m,setStrong:function(e,r,t){c(e,r,function(e,t){this._t=h(e,r);this._k=t;this._l=undefined},function(){var e=this;var r=e._k;var t=e._l;while(t&&t.r)t=t.p;if(!e._t||!(e._l=t=t?t.n:e._t._f)){e._t=undefined;return l(1)}if(r=="keys")return l(0,t.k);if(r=="values")return l(0,t.v);return l(0,[t.k,t.v])},t?"entries":"values",!t,true);s(r)}}},"98ab":function(e,r,t){var n=t("3955");var a=t("4f29");var i=t("1008");var o=Object.defineProperty;r.f=t("e2e5")?Object.defineProperty:function e(r,t,f){n(r);t=i(t,true);n(f);if(a)try{return o(r,t,f)}catch(u){}if("get"in f||"set"in f)throw TypeError("Accessors not supported!");if("value"in f)r[t]=f.value;return r}},"9a92":function(e,r){e.exports=function(e,r,t,n){if(!(e instanceof r)||n!==undefined&&n in e){throw TypeError(t+": incorrect invocation!")}return e}},"9af0":function(e,r,t){var n=t("98ab").f;var a=Function.prototype;var i=/^\s*function ([^ (]*)/;var o="name";o in a||t("e2e5")&&n(a,o,{configurable:true,get:function(){try{return(""+this).match(i)[1]}catch(e){return""}}})},"9b42":function(e,r){function t(e,r){var t=[];var n=true;var a=false;var i=undefined;try{for(var o=e[Symbol.iterator](),f;!(n=(f=o.next()).done);n=true){t.push(f.value);if(r&&t.length===r)break}}catch(u){a=true;i=u}finally{try{if(!n&&o["return"]!=null)o["return"]()}finally{if(a)throw i}}return t}e.exports=t},"9b8e":function(e,r,t){"use strict";t("b44a");var n=t("3955");var a=t("45a9");var i=t("e2e5");var o="toString";var f=/./[o];var u=function(e){t("a85c")(RegExp.prototype,o,e,true)};if(t("c0f6")(function(){return f.call({source:"a",flags:"b"})!="/a/b"})){u(function e(){var r=n(this);return"/".concat(r.source,"/","flags"in r?r.flags:!i&&r instanceof RegExp?a.call(r):undefined)})}else if(f.name!=o){u(function e(){return f.call(this)})}},"9ec3":function(e,r,t){(function(e,n){var a;
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright JS Foundation and other contributors <https://js.foundation/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */(function(){var i;var o="4.17.11";var f=200;var u="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",c="Expected a function";var l="__lodash_hash_undefined__";var s=500;var v="__lodash_placeholder__";var p=1,h=2,d=4;var m=1,g=2;var b=1,y=2,_=4,x=8,w=16,k=32,z=64,E=128,S=256,A=512;var j=30,O="...";var T=800,M=16;var D=1,I=2,C=3;var P=1/0,F=9007199254740991,N=1.7976931348623157e308,L=0/0;var R=**********,B=R-1,W=R>>>1;var U=[["ary",E],["bind",b],["bindKey",y],["curry",x],["curryRight",w],["flip",A],["partial",k],["partialRight",z],["rearg",S]];var q="[object Arguments]",G="[object Array]",$="[object AsyncFunction]",V="[object Boolean]",H="[object Date]",Y="[object DOMException]",X="[object Error]",Q="[object Function]",J="[object GeneratorFunction]",K="[object Map]",Z="[object Number]",ee="[object Null]",re="[object Object]",te="[object Promise]",ne="[object Proxy]",ae="[object RegExp]",ie="[object Set]",oe="[object String]",fe="[object Symbol]",ue="[object Undefined]",ce="[object WeakMap]",le="[object WeakSet]";var se="[object ArrayBuffer]",ve="[object DataView]",pe="[object Float32Array]",he="[object Float64Array]",de="[object Int8Array]",me="[object Int16Array]",ge="[object Int32Array]",be="[object Uint8Array]",ye="[object Uint8ClampedArray]",_e="[object Uint16Array]",xe="[object Uint32Array]";var we=/\b__p \+= '';/g,ke=/\b(__p \+=) '' \+/g,ze=/(__e\(.*?\)|\b__t\)) \+\n'';/g;var Ee=/&(?:amp|lt|gt|quot|#39);/g,Se=/[&<>"']/g,Ae=RegExp(Ee.source),je=RegExp(Se.source);var Oe=/<%-([\s\S]+?)%>/g,Te=/<%([\s\S]+?)%>/g,Me=/<%=([\s\S]+?)%>/g;var De=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ie=/^\w*$/,Ce=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;var Pe=/[\\^$.*+?()[\]{}|]/g,Fe=RegExp(Pe.source);var Ne=/^\s+|\s+$/g,Le=/^\s+/,Re=/\s+$/;var Be=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,We=/\{\n\/\* \[wrapped with (.+)\] \*/,Ue=/,? & /;var qe=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;var Ge=/\\(\\)?/g;var $e=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g;var Ve=/\w*$/;var He=/^[-+]0x[0-9a-f]+$/i;var Ye=/^0b[01]+$/i;var Xe=/^\[object .+?Constructor\]$/;var Qe=/^0o[0-7]+$/i;var Je=/^(?:0|[1-9]\d*)$/;var Ke=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g;var Ze=/($^)/;var er=/['\n\r\u2028\u2029\\]/g;var rr="\\ud800-\\udfff",tr="\\u0300-\\u036f",nr="\\ufe20-\\ufe2f",ar="\\u20d0-\\u20ff",ir=tr+nr+ar,or="\\u2700-\\u27bf",fr="a-z\\xdf-\\xf6\\xf8-\\xff",ur="\\xac\\xb1\\xd7\\xf7",cr="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",lr="\\u2000-\\u206f",sr=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vr="A-Z\\xc0-\\xd6\\xd8-\\xde",pr="\\ufe0e\\ufe0f",hr=ur+cr+lr+sr;var dr="['’]",mr="["+rr+"]",gr="["+hr+"]",br="["+ir+"]",yr="\\d+",_r="["+or+"]",xr="["+fr+"]",wr="[^"+rr+hr+yr+or+fr+vr+"]",kr="\\ud83c[\\udffb-\\udfff]",zr="(?:"+br+"|"+kr+")",Er="[^"+rr+"]",Sr="(?:\\ud83c[\\udde6-\\uddff]){2}",Ar="[\\ud800-\\udbff][\\udc00-\\udfff]",jr="["+vr+"]",Or="\\u200d";var Tr="(?:"+xr+"|"+wr+")",Mr="(?:"+jr+"|"+wr+")",Dr="(?:"+dr+"(?:d|ll|m|re|s|t|ve))?",Ir="(?:"+dr+"(?:D|LL|M|RE|S|T|VE))?",Cr=zr+"?",Pr="["+pr+"]?",Fr="(?:"+Or+"(?:"+[Er,Sr,Ar].join("|")+")"+Pr+Cr+")*",Nr="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Lr="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Rr=Pr+Cr+Fr,Br="(?:"+[_r,Sr,Ar].join("|")+")"+Rr,Wr="(?:"+[Er+br+"?",br,Sr,Ar,mr].join("|")+")";var Ur=RegExp(dr,"g");var qr=RegExp(br,"g");var Gr=RegExp(kr+"(?="+kr+")|"+Wr+Rr,"g");var $r=RegExp([jr+"?"+xr+"+"+Dr+"(?="+[gr,jr,"$"].join("|")+")",Mr+"+"+Ir+"(?="+[gr,jr+Tr,"$"].join("|")+")",jr+"?"+Tr+"+"+Dr,jr+"+"+Ir,Lr,Nr,yr,Br].join("|"),"g");var Vr=RegExp("["+Or+rr+ir+pr+"]");var Hr=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;var Yr=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"];var Xr=-1;var Qr={};Qr[pe]=Qr[he]=Qr[de]=Qr[me]=Qr[ge]=Qr[be]=Qr[ye]=Qr[_e]=Qr[xe]=true;Qr[q]=Qr[G]=Qr[se]=Qr[V]=Qr[ve]=Qr[H]=Qr[X]=Qr[Q]=Qr[K]=Qr[Z]=Qr[re]=Qr[ae]=Qr[ie]=Qr[oe]=Qr[ce]=false;var Jr={};Jr[q]=Jr[G]=Jr[se]=Jr[ve]=Jr[V]=Jr[H]=Jr[pe]=Jr[he]=Jr[de]=Jr[me]=Jr[ge]=Jr[K]=Jr[Z]=Jr[re]=Jr[ae]=Jr[ie]=Jr[oe]=Jr[fe]=Jr[be]=Jr[ye]=Jr[_e]=Jr[xe]=true;Jr[X]=Jr[Q]=Jr[ce]=false;var Kr={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"};var Zr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};var et={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"};var rt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"};var tt=parseFloat,nt=parseInt;var at=typeof e=="object"&&e&&e.Object===Object&&e;var it=typeof self=="object"&&self&&self.Object===Object&&self;var ot=at||it||Function("return this")();var ft=true&&r&&!r.nodeType&&r;var ut=ft&&typeof n=="object"&&n&&!n.nodeType&&n;var ct=ut&&ut.exports===ft;var lt=ct&&at.process;var st=function(){try{var e=ut&&ut.require&&ut.require("util").types;if(e){return e}return lt&&lt.binding&&lt.binding("util")}catch(r){}}();var vt=st&&st.isArrayBuffer,pt=st&&st.isDate,ht=st&&st.isMap,dt=st&&st.isRegExp,mt=st&&st.isSet,gt=st&&st.isTypedArray;function bt(e,r,t){switch(t.length){case 0:return e.call(r);case 1:return e.call(r,t[0]);case 2:return e.call(r,t[0],t[1]);case 3:return e.call(r,t[0],t[1],t[2])}return e.apply(r,t)}function yt(e,r,t,n){var a=-1,i=e==null?0:e.length;while(++a<i){var o=e[a];r(n,o,t(o),e)}return n}function _t(e,r){var t=-1,n=e==null?0:e.length;while(++t<n){if(r(e[t],t,e)===false){break}}return e}function xt(e,r){var t=e==null?0:e.length;while(t--){if(r(e[t],t,e)===false){break}}return e}function wt(e,r){var t=-1,n=e==null?0:e.length;while(++t<n){if(!r(e[t],t,e)){return false}}return true}function kt(e,r){var t=-1,n=e==null?0:e.length,a=0,i=[];while(++t<n){var o=e[t];if(r(o,t,e)){i[a++]=o}}return i}function zt(e,r){var t=e==null?0:e.length;return!!t&&Ft(e,r,0)>-1}function Et(e,r,t){var n=-1,a=e==null?0:e.length;while(++n<a){if(t(r,e[n])){return true}}return false}function St(e,r){var t=-1,n=e==null?0:e.length,a=Array(n);while(++t<n){a[t]=r(e[t],t,e)}return a}function At(e,r){var t=-1,n=r.length,a=e.length;while(++t<n){e[a+t]=r[t]}return e}function jt(e,r,t,n){var a=-1,i=e==null?0:e.length;if(n&&i){t=e[++a]}while(++a<i){t=r(t,e[a],a,e)}return t}function Ot(e,r,t,n){var a=e==null?0:e.length;if(n&&a){t=e[--a]}while(a--){t=r(t,e[a],a,e)}return t}function Tt(e,r){var t=-1,n=e==null?0:e.length;while(++t<n){if(r(e[t],t,e)){return true}}return false}var Mt=Bt("length");function Dt(e){return e.split("")}function It(e){return e.match(qe)||[]}function Ct(e,r,t){var n;t(e,function(e,t,a){if(r(e,t,a)){n=t;return false}});return n}function Pt(e,r,t,n){var a=e.length,i=t+(n?1:-1);while(n?i--:++i<a){if(r(e[i],i,e)){return i}}return-1}function Ft(e,r,t){return r===r?vn(e,r,t):Pt(e,Lt,t)}function Nt(e,r,t,n){var a=t-1,i=e.length;while(++a<i){if(n(e[a],r)){return a}}return-1}function Lt(e){return e!==e}function Rt(e,r){var t=e==null?0:e.length;return t?Gt(e,r)/t:L}function Bt(e){return function(r){return r==null?i:r[e]}}function Wt(e){return function(r){return e==null?i:e[r]}}function Ut(e,r,t,n,a){a(e,function(e,a,i){t=n?(n=false,e):r(t,e,a,i)});return t}function qt(e,r){var t=e.length;e.sort(r);while(t--){e[t]=e[t].value}return e}function Gt(e,r){var t,n=-1,a=e.length;while(++n<a){var o=r(e[n]);if(o!==i){t=t===i?o:t+o}}return t}function $t(e,r){var t=-1,n=Array(e);while(++t<e){n[t]=r(t)}return n}function Vt(e,r){return St(r,function(r){return[r,e[r]]})}function Ht(e){return function(r){return e(r)}}function Yt(e,r){return St(r,function(r){return e[r]})}function Xt(e,r){return e.has(r)}function Qt(e,r){var t=-1,n=e.length;while(++t<n&&Ft(r,e[t],0)>-1){}return t}function Jt(e,r){var t=e.length;while(t--&&Ft(r,e[t],0)>-1){}return t}function Kt(e,r){var t=e.length,n=0;while(t--){if(e[t]===r){++n}}return n}var Zt=Wt(Kr);var en=Wt(Zr);function rn(e){return"\\"+rt[e]}function tn(e,r){return e==null?i:e[r]}function nn(e){return Vr.test(e)}function an(e){return Hr.test(e)}function on(e){var r,t=[];while(!(r=e.next()).done){t.push(r.value)}return t}function fn(e){var r=-1,t=Array(e.size);e.forEach(function(e,n){t[++r]=[n,e]});return t}function un(e,r){return function(t){return e(r(t))}}function cn(e,r){var t=-1,n=e.length,a=0,i=[];while(++t<n){var o=e[t];if(o===r||o===v){e[t]=v;i[a++]=t}}return i}function ln(e){var r=-1,t=Array(e.size);e.forEach(function(e){t[++r]=e});return t}function sn(e){var r=-1,t=Array(e.size);e.forEach(function(e){t[++r]=[e,e]});return t}function vn(e,r,t){var n=t-1,a=e.length;while(++n<a){if(e[n]===r){return n}}return-1}function pn(e,r,t){var n=t+1;while(n--){if(e[n]===r){return n}}return n}function hn(e){return nn(e)?gn(e):Mt(e)}function dn(e){return nn(e)?bn(e):Dt(e)}var mn=Wt(et);function gn(e){var r=Gr.lastIndex=0;while(Gr.test(e)){++r}return r}function bn(e){return e.match(Gr)||[]}function yn(e){return e.match($r)||[]}var _n=function e(r){r=r==null?ot:xn.defaults(ot.Object(),r,xn.pick(ot,Yr));var t=r.Array,n=r.Date,a=r.Error,qe=r.Function,rr=r.Math,tr=r.Object,nr=r.RegExp,ar=r.String,ir=r.TypeError;var or=t.prototype,fr=qe.prototype,ur=tr.prototype;var cr=r["__core-js_shared__"];var lr=fr.toString;var sr=ur.hasOwnProperty;var vr=0;var pr=function(){var e=/[^.]+$/.exec(cr&&cr.keys&&cr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();var hr=ur.toString;var dr=lr.call(tr);var mr=ot._;var gr=nr("^"+lr.call(sr).replace(Pe,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var br=ct?r.Buffer:i,yr=r.Symbol,_r=r.Uint8Array,xr=br?br.allocUnsafe:i,wr=un(tr.getPrototypeOf,tr),kr=tr.create,zr=ur.propertyIsEnumerable,Er=or.splice,Sr=yr?yr.isConcatSpreadable:i,Ar=yr?yr.iterator:i,jr=yr?yr.toStringTag:i;var Or=function(){try{var e=$o(tr,"defineProperty");e({},"",{});return e}catch(r){}}();var Tr=r.clearTimeout!==ot.clearTimeout&&r.clearTimeout,Mr=n&&n.now!==ot.Date.now&&n.now,Dr=r.setTimeout!==ot.setTimeout&&r.setTimeout;var Ir=rr.ceil,Cr=rr.floor,Pr=tr.getOwnPropertySymbols,Fr=br?br.isBuffer:i,Nr=r.isFinite,Lr=or.join,Rr=un(tr.keys,tr),Br=rr.max,Wr=rr.min,Gr=n.now,$r=r.parseInt,Vr=rr.random,Hr=or.reverse;var Kr=$o(r,"DataView"),Zr=$o(r,"Map"),et=$o(r,"Promise"),rt=$o(r,"Set"),at=$o(r,"WeakMap"),it=$o(tr,"create");var ft=at&&new at;var ut={};var lt=Mf(Kr),st=Mf(Zr),Mt=Mf(et),Dt=Mf(rt),Wt=Mf(at);var vn=yr?yr.prototype:i,gn=vn?vn.valueOf:i,bn=vn?vn.toString:i;function _n(e){if(zl(e)&&!fl(e)&&!(e instanceof En)){if(e instanceof zn){return e}if(sr.call(e,"__wrapped__")){return If(e)}}return new zn(e)}var wn=function(){function e(){}return function(r){if(!kl(r)){return{}}if(kr){return kr(r)}e.prototype=r;var t=new e;e.prototype=i;return t}}();function kn(){}function zn(e,r){this.__wrapped__=e;this.__actions__=[];this.__chain__=!!r;this.__index__=0;this.__values__=i}_n.templateSettings={escape:Oe,evaluate:Te,interpolate:Me,variable:"",imports:{_:_n}};_n.prototype=kn.prototype;_n.prototype.constructor=_n;zn.prototype=wn(kn.prototype);zn.prototype.constructor=zn;function En(e){this.__wrapped__=e;this.__actions__=[];this.__dir__=1;this.__filtered__=false;this.__iteratees__=[];this.__takeCount__=R;this.__views__=[]}function Sn(){var e=new En(this.__wrapped__);e.__actions__=ro(this.__actions__);e.__dir__=this.__dir__;e.__filtered__=this.__filtered__;e.__iteratees__=ro(this.__iteratees__);e.__takeCount__=this.__takeCount__;e.__views__=ro(this.__views__);return e}function An(){if(this.__filtered__){var e=new En(this);e.__dir__=-1;e.__filtered__=true}else{e=this.clone();e.__dir__*=-1}return e}function jn(){var e=this.__wrapped__.value(),r=this.__dir__,t=fl(e),n=r<0,a=t?e.length:0,i=Qo(0,a,this.__views__),o=i.start,f=i.end,u=f-o,c=n?f:o-1,l=this.__iteratees__,s=l.length,v=0,p=Wr(u,this.__takeCount__);if(!t||!n&&a==u&&p==u){return Fi(e,this.__actions__)}var h=[];e:while(u--&&v<p){c+=r;var d=-1,m=e[c];while(++d<s){var g=l[d],b=g.iteratee,y=g.type,_=b(m);if(y==I){m=_}else if(!_){if(y==D){continue e}else{break e}}}h[v++]=m}return h}En.prototype=wn(kn.prototype);En.prototype.constructor=En;function On(e){var r=-1,t=e==null?0:e.length;this.clear();while(++r<t){var n=e[r];this.set(n[0],n[1])}}function Tn(){this.__data__=it?it(null):{};this.size=0}function Mn(e){var r=this.has(e)&&delete this.__data__[e];this.size-=r?1:0;return r}function Dn(e){var r=this.__data__;if(it){var t=r[e];return t===l?i:t}return sr.call(r,e)?r[e]:i}function In(e){var r=this.__data__;return it?r[e]!==i:sr.call(r,e)}function Cn(e,r){var t=this.__data__;this.size+=this.has(e)?0:1;t[e]=it&&r===i?l:r;return this}On.prototype.clear=Tn;On.prototype["delete"]=Mn;On.prototype.get=Dn;On.prototype.has=In;On.prototype.set=Cn;function Pn(e){var r=-1,t=e==null?0:e.length;this.clear();while(++r<t){var n=e[r];this.set(n[0],n[1])}}function Fn(){this.__data__=[];this.size=0}function Nn(e){var r=this.__data__,t=ua(r,e);if(t<0){return false}var n=r.length-1;if(t==n){r.pop()}else{Er.call(r,t,1)}--this.size;return true}function Ln(e){var r=this.__data__,t=ua(r,e);return t<0?i:r[t][1]}function Rn(e){return ua(this.__data__,e)>-1}function Bn(e,r){var t=this.__data__,n=ua(t,e);if(n<0){++this.size;t.push([e,r])}else{t[n][1]=r}return this}Pn.prototype.clear=Fn;Pn.prototype["delete"]=Nn;Pn.prototype.get=Ln;Pn.prototype.has=Rn;Pn.prototype.set=Bn;function Wn(e){var r=-1,t=e==null?0:e.length;this.clear();while(++r<t){var n=e[r];this.set(n[0],n[1])}}function Un(){this.size=0;this.__data__={hash:new On,map:new(Zr||Pn),string:new On}}function qn(e){var r=qo(this,e)["delete"](e);this.size-=r?1:0;return r}function Gn(e){return qo(this,e).get(e)}function $n(e){return qo(this,e).has(e)}function Vn(e,r){var t=qo(this,e),n=t.size;t.set(e,r);this.size+=t.size==n?0:1;return this}Wn.prototype.clear=Un;Wn.prototype["delete"]=qn;Wn.prototype.get=Gn;Wn.prototype.has=$n;Wn.prototype.set=Vn;function Hn(e){var r=-1,t=e==null?0:e.length;this.__data__=new Wn;while(++r<t){this.add(e[r])}}function Yn(e){this.__data__.set(e,l);return this}function Xn(e){return this.__data__.has(e)}Hn.prototype.add=Hn.prototype.push=Yn;Hn.prototype.has=Xn;function Qn(e){var r=this.__data__=new Pn(e);this.size=r.size}function Jn(){this.__data__=new Pn;this.size=0}function Kn(e){var r=this.__data__,t=r["delete"](e);this.size=r.size;return t}function Zn(e){return this.__data__.get(e)}function ea(e){return this.__data__.has(e)}function ra(e,r){var t=this.__data__;if(t instanceof Pn){var n=t.__data__;if(!Zr||n.length<f-1){n.push([e,r]);this.size=++t.size;return this}t=this.__data__=new Wn(n)}t.set(e,r);this.size=t.size;return this}Qn.prototype.clear=Jn;Qn.prototype["delete"]=Kn;Qn.prototype.get=Zn;Qn.prototype.has=ea;Qn.prototype.set=ra;function ta(e,r){var t=fl(e),n=!t&&ol(e),a=!t&&!n&&vl(e),i=!t&&!n&&!a&&Rl(e),o=t||n||a||i,f=o?$t(e.length,ar):[],u=f.length;for(var c in e){if((r||sr.call(e,c))&&!(o&&(c=="length"||a&&(c=="offset"||c=="parent")||i&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||af(c,u)))){f.push(c)}}return f}function na(e){var r=e.length;return r?e[di(0,r-1)]:i}function aa(e,r){return jf(ro(e),ha(r,0,e.length))}function ia(e){return jf(ro(e))}function oa(e,r,t){if(t!==i&&!nl(e[r],t)||t===i&&!(r in e)){va(e,r,t)}}function fa(e,r,t){var n=e[r];if(!(sr.call(e,r)&&nl(n,t))||t===i&&!(r in e)){va(e,r,t)}}function ua(e,r){var t=e.length;while(t--){if(nl(e[t][0],r)){return t}}return-1}function ca(e,r,t,n){_a(e,function(e,a,i){r(n,e,t(e),i)});return n}function la(e,r){return e&&to(r,xs(r),e)}function sa(e,r){return e&&to(r,ws(r),e)}function va(e,r,t){if(r=="__proto__"&&Or){Or(e,r,{configurable:true,enumerable:true,value:t,writable:true})}else{e[r]=t}}function pa(e,r){var n=-1,a=r.length,o=t(a),f=e==null;while(++n<a){o[n]=f?i:ds(e,r[n])}return o}function ha(e,r,t){if(e===e){if(t!==i){e=e<=t?e:t}if(r!==i){e=e>=r?e:r}}return e}function da(e,r,t,n,a,o){var f,u=r&p,c=r&h,l=r&d;if(t){f=a?t(e,n,a,o):t(e)}if(f!==i){return f}if(!kl(e)){return e}var s=fl(e);if(s){f=Zo(e);if(!u){return ro(e,f)}}else{var v=Xo(e),m=v==Q||v==J;if(vl(e)){return $i(e,u)}if(v==re||v==q||m&&!a){f=c||m?{}:ef(e);if(!u){return c?ao(e,sa(f,e)):no(e,la(f,e))}}else{if(!Jr[v]){return a?e:{}}f=rf(e,v,u)}}o||(o=new Qn);var g=o.get(e);if(g){return g}o.set(e,f);if(Fl(e)){e.forEach(function(n){f.add(da(n,r,t,n,e,o))});return f}if(El(e)){e.forEach(function(n,a){f.set(a,da(n,r,t,a,e,o))});return f}var b=l?c?Lo:No:c?ws:xs;var y=s?i:b(e);_t(y||e,function(n,a){if(y){a=n;n=e[a]}fa(f,a,da(n,r,t,a,e,o))});return f}function ma(e){var r=xs(e);return function(t){return ga(t,e,r)}}function ga(e,r,t){var n=t.length;if(e==null){return!n}e=tr(e);while(n--){var a=t[n],o=r[a],f=e[a];if(f===i&&!(a in e)||!o(f)){return false}}return true}function ba(e,r,t){if(typeof e!="function"){throw new ir(c)}return zf(function(){e.apply(i,t)},r)}function ya(e,r,t,n){var a=-1,i=zt,o=true,u=e.length,c=[],l=r.length;if(!u){return c}if(t){r=St(r,Ht(t))}if(n){i=Et;o=false}else if(r.length>=f){i=Xt;o=false;r=new Hn(r)}e:while(++a<u){var s=e[a],v=t==null?s:t(s);s=n||s!==0?s:0;if(o&&v===v){var p=l;while(p--){if(r[p]===v){continue e}}c.push(s)}else if(!i(r,v,n)){c.push(s)}}return c}var _a=fo(Oa);var xa=fo(Ta,true);function wa(e,r){var t=true;_a(e,function(e,n,a){t=!!r(e,n,a);return t});return t}function ka(e,r,t){var n=-1,a=e.length;while(++n<a){var o=e[n],f=r(o);if(f!=null&&(u===i?f===f&&!Ll(f):t(f,u))){var u=f,c=o}}return c}function za(e,r,t,n){var a=e.length;t=Hl(t);if(t<0){t=-t>a?0:a+t}n=n===i||n>a?a:Hl(n);if(n<0){n+=a}n=t>n?0:Yl(n);while(t<n){e[t++]=r}return e}function Ea(e,r){var t=[];_a(e,function(e,n,a){if(r(e,n,a)){t.push(e)}});return t}function Sa(e,r,t,n,a){var i=-1,o=e.length;t||(t=nf);a||(a=[]);while(++i<o){var f=e[i];if(r>0&&t(f)){if(r>1){Sa(f,r-1,t,n,a)}else{At(a,f)}}else if(!n){a[a.length]=f}}return a}var Aa=uo();var ja=uo(true);function Oa(e,r){return e&&Aa(e,r,xs)}function Ta(e,r){return e&&ja(e,r,xs)}function Ma(e,r){return kt(r,function(r){return _l(e[r])})}function Da(e,r){r=Wi(r,e);var t=0,n=r.length;while(e!=null&&t<n){e=e[Tf(r[t++])]}return t&&t==n?e:i}function Ia(e,r,t){var n=r(e);return fl(e)?n:At(n,t(e))}function Ca(e){if(e==null){return e===i?ue:ee}return jr&&jr in tr(e)?Vo(e):bf(e)}function Pa(e,r){return e>r}function Fa(e,r){return e!=null&&sr.call(e,r)}function Na(e,r){return e!=null&&r in tr(e)}function La(e,r,t){return e>=Wr(r,t)&&e<Br(r,t)}function Ra(e,r,n){var a=n?Et:zt,o=e[0].length,f=e.length,u=f,c=t(f),l=Infinity,s=[];while(u--){var v=e[u];if(u&&r){v=St(v,Ht(r))}l=Wr(v.length,l);c[u]=!n&&(r||o>=120&&v.length>=120)?new Hn(u&&v):i}v=e[0];var p=-1,h=c[0];e:while(++p<o&&s.length<l){var d=v[p],m=r?r(d):d;d=n||d!==0?d:0;if(!(h?Xt(h,m):a(s,m,n))){u=f;while(--u){var g=c[u];if(!(g?Xt(g,m):a(e[u],m,n))){continue e}}if(h){h.push(m)}s.push(d)}}return s}function Ba(e,r,t,n){Oa(e,function(e,a,i){r(n,t(e),a,i)});return n}function Wa(e,r,t){r=Wi(r,e);e=_f(e,r);var n=e==null?e:e[Tf(au(r))];return n==null?i:bt(n,e,t)}function Ua(e){return zl(e)&&Ca(e)==q}function qa(e){return zl(e)&&Ca(e)==se}function Ga(e){return zl(e)&&Ca(e)==H}function $a(e,r,t,n,a){if(e===r){return true}if(e==null||r==null||!zl(e)&&!zl(r)){return e!==e&&r!==r}return Va(e,r,t,n,$a,a)}function Va(e,r,t,n,a,i){var o=fl(e),f=fl(r),u=o?G:Xo(e),c=f?G:Xo(r);u=u==q?re:u;c=c==q?re:c;var l=u==re,s=c==re,v=u==c;if(v&&vl(e)){if(!vl(r)){return false}o=true;l=false}if(v&&!l){i||(i=new Qn);return o||Rl(e)?Io(e,r,t,n,a,i):Co(e,r,u,t,n,a,i)}if(!(t&m)){var p=l&&sr.call(e,"__wrapped__"),h=s&&sr.call(r,"__wrapped__");if(p||h){var d=p?e.value():e,g=h?r.value():r;i||(i=new Qn);return a(d,g,t,n,i)}}if(!v){return false}i||(i=new Qn);return Po(e,r,t,n,a,i)}function Ha(e){return zl(e)&&Xo(e)==K}function Ya(e,r,t,n){var a=t.length,o=a,f=!n;if(e==null){return!o}e=tr(e);while(a--){var u=t[a];if(f&&u[2]?u[1]!==e[u[0]]:!(u[0]in e)){return false}}while(++a<o){u=t[a];var c=u[0],l=e[c],s=u[1];if(f&&u[2]){if(l===i&&!(c in e)){return false}}else{var v=new Qn;if(n){var p=n(l,s,c,e,r,v)}if(!(p===i?$a(s,l,m|g,n,v):p)){return false}}}return true}function Xa(e){if(!kl(e)||lf(e)){return false}var r=_l(e)?gr:Xe;return r.test(Mf(e))}function Qa(e){return zl(e)&&Ca(e)==ae}function Ja(e){return zl(e)&&Xo(e)==ie}function Ka(e){return zl(e)&&wl(e.length)&&!!Qr[Ca(e)]}function Za(e){if(typeof e=="function"){return e}if(e==null){return Ov}if(typeof e=="object"){return fl(e)?ii(e[0],e[1]):ai(e)}return Uv(e)}function ei(e){if(!vf(e)){return Rr(e)}var r=[];for(var t in tr(e)){if(sr.call(e,t)&&t!="constructor"){r.push(t)}}return r}function ri(e){if(!kl(e)){return gf(e)}var r=vf(e),t=[];for(var n in e){if(!(n=="constructor"&&(r||!sr.call(e,n)))){t.push(n)}}return t}function ti(e,r){return e<r}function ni(e,r){var n=-1,a=cl(e)?t(e.length):[];_a(e,function(e,t,i){a[++n]=r(e,t,i)});return a}function ai(e){var r=Go(e);if(r.length==1&&r[0][2]){return hf(r[0][0],r[0][1])}return function(t){return t===e||Ya(t,e,r)}}function ii(e,r){if(ff(e)&&pf(r)){return hf(Tf(e),r)}return function(t){var n=ds(t,e);return n===i&&n===r?gs(t,e):$a(r,n,m|g)}}function oi(e,r,t,n,a){if(e===r){return}Aa(r,function(o,f){if(kl(o)){a||(a=new Qn);fi(e,r,f,t,oi,n,a)}else{var u=n?n(wf(e,f),o,f+"",e,r,a):i;if(u===i){u=o}oa(e,f,u)}},ws)}function fi(e,r,t,n,a,o,f){var u=wf(e,t),c=wf(r,t),l=f.get(c);if(l){oa(e,t,l);return}var s=o?o(u,c,t+"",e,r,f):i;var v=s===i;if(v){var p=fl(c),h=!p&&vl(c),d=!p&&!h&&Rl(c);s=c;if(p||h||d){if(fl(u)){s=u}else if(ll(u)){s=ro(u)}else if(h){v=false;s=$i(c,true)}else if(d){v=false;s=Qi(c,true)}else{s=[]}}else if(Il(c)||ol(c)){s=u;if(ol(u)){s=Ql(u)}else if(!kl(u)||_l(u)){s=ef(c)}}else{v=false}}if(v){f.set(c,s);a(s,c,n,o,f);f["delete"](c)}oa(e,t,s)}function ui(e,r){var t=e.length;if(!t){return}r+=r<0?t:0;return af(r,t)?e[r]:i}function ci(e,r,t){var n=-1;r=St(r.length?r:[Ov],Ht(Uo()));var a=ni(e,function(e,t,a){var i=St(r,function(r){return r(e)});return{criteria:i,index:++n,value:e}});return qt(a,function(e,r){return Ki(e,r,t)})}function li(e,r){return si(e,r,function(r,t){return gs(e,t)})}function si(e,r,t){var n=-1,a=r.length,i={};while(++n<a){var o=r[n],f=Da(e,o);if(t(f,o)){xi(i,Wi(o,e),f)}}return i}function vi(e){return function(r){return Da(r,e)}}function pi(e,r,t,n){var a=n?Nt:Ft,i=-1,o=r.length,f=e;if(e===r){r=ro(r)}if(t){f=St(e,Ht(t))}while(++i<o){var u=0,c=r[i],l=t?t(c):c;while((u=a(f,l,u,n))>-1){if(f!==e){Er.call(f,u,1)}Er.call(e,u,1)}}return e}function hi(e,r){var t=e?r.length:0,n=t-1;while(t--){var a=r[t];if(t==n||a!==i){var i=a;if(af(a)){Er.call(e,a,1)}else{Ii(e,a)}}}return e}function di(e,r){return e+Cr(Vr()*(r-e+1))}function mi(e,r,n,a){var i=-1,o=Br(Ir((r-e)/(n||1)),0),f=t(o);while(o--){f[a?o:++i]=e;e+=n}return f}function gi(e,r){var t="";if(!e||r<1||r>F){return t}do{if(r%2){t+=e}r=Cr(r/2);if(r){e+=e}}while(r);return t}function bi(e,r){return Ef(yf(e,r,Ov),e+"")}function yi(e){return na(Bs(e))}function _i(e,r){var t=Bs(e);return jf(t,ha(r,0,t.length))}function xi(e,r,t,n){if(!kl(e)){return e}r=Wi(r,e);var a=-1,o=r.length,f=o-1,u=e;while(u!=null&&++a<o){var c=Tf(r[a]),l=t;if(a!=f){var s=u[c];l=n?n(s,c,u):i;if(l===i){l=kl(s)?s:af(r[a+1])?[]:{}}}fa(u,c,l);u=u[c]}return e}var wi=!ft?Ov:function(e,r){ft.set(e,r);return e};var ki=!Or?Ov:function(e,r){return Or(e,"toString",{configurable:true,enumerable:false,value:Ev(r),writable:true})};function zi(e){return jf(Bs(e))}function Ei(e,r,n){var a=-1,i=e.length;if(r<0){r=-r>i?0:i+r}n=n>i?i:n;if(n<0){n+=i}i=r>n?0:n-r>>>0;r>>>=0;var o=t(i);while(++a<i){o[a]=e[a+r]}return o}function Si(e,r){var t;_a(e,function(e,n,a){t=r(e,n,a);return!t});return!!t}function Ai(e,r,t){var n=0,a=e==null?n:e.length;if(typeof r=="number"&&r===r&&a<=W){while(n<a){var i=n+a>>>1,o=e[i];if(o!==null&&!Ll(o)&&(t?o<=r:o<r)){n=i+1}else{a=i}}return a}return ji(e,r,Ov,t)}function ji(e,r,t,n){r=t(r);var a=0,o=e==null?0:e.length,f=r!==r,u=r===null,c=Ll(r),l=r===i;while(a<o){var s=Cr((a+o)/2),v=t(e[s]),p=v!==i,h=v===null,d=v===v,m=Ll(v);if(f){var g=n||d}else if(l){g=d&&(n||p)}else if(u){g=d&&p&&(n||!h)}else if(c){g=d&&p&&!h&&(n||!m)}else if(h||m){g=false}else{g=n?v<=r:v<r}if(g){a=s+1}else{o=s}}return Wr(o,B)}function Oi(e,r){var t=-1,n=e.length,a=0,i=[];while(++t<n){var o=e[t],f=r?r(o):o;if(!t||!nl(f,u)){var u=f;i[a++]=o===0?0:o}}return i}function Ti(e){if(typeof e=="number"){return e}if(Ll(e)){return L}return+e}function Mi(e){if(typeof e=="string"){return e}if(fl(e)){return St(e,Mi)+""}if(Ll(e)){return bn?bn.call(e):""}var r=e+"";return r=="0"&&1/e==-P?"-0":r}function Di(e,r,t){var n=-1,a=zt,i=e.length,o=true,u=[],c=u;if(t){o=false;a=Et}else if(i>=f){var l=r?null:Ao(e);if(l){return ln(l)}o=false;a=Xt;c=new Hn}else{c=r?[]:u}e:while(++n<i){var s=e[n],v=r?r(s):s;s=t||s!==0?s:0;if(o&&v===v){var p=c.length;while(p--){if(c[p]===v){continue e}}if(r){c.push(v)}u.push(s)}else if(!a(c,v,t)){if(c!==u){c.push(v)}u.push(s)}}return u}function Ii(e,r){r=Wi(r,e);e=_f(e,r);return e==null||delete e[Tf(au(r))]}function Ci(e,r,t,n){return xi(e,r,t(Da(e,r)),n)}function Pi(e,r,t,n){var a=e.length,i=n?a:-1;while((n?i--:++i<a)&&r(e[i],i,e)){}return t?Ei(e,n?0:i,n?i+1:a):Ei(e,n?i+1:0,n?a:i)}function Fi(e,r){var t=e;if(t instanceof En){t=t.value()}return jt(r,function(e,r){return r.func.apply(r.thisArg,At([e],r.args))},t)}function Ni(e,r,n){var a=e.length;if(a<2){return a?Di(e[0]):[]}var i=-1,o=t(a);while(++i<a){var f=e[i],u=-1;while(++u<a){if(u!=i){o[i]=ya(o[i]||f,e[u],r,n)}}}return Di(Sa(o,1),r,n)}function Li(e,r,t){var n=-1,a=e.length,o=r.length,f={};while(++n<a){var u=n<o?r[n]:i;t(f,e[n],u)}return f}function Ri(e){return ll(e)?e:[]}function Bi(e){return typeof e=="function"?e:Ov}function Wi(e,r){if(fl(e)){return e}return ff(e,r)?[e]:Of(Kl(e))}var Ui=bi;function qi(e,r,t){var n=e.length;t=t===i?n:t;return!r&&t>=n?e:Ei(e,r,t)}var Gi=Tr||function(e){return ot.clearTimeout(e)};function $i(e,r){if(r){return e.slice()}var t=e.length,n=xr?xr(t):new e.constructor(t);e.copy(n);return n}function Vi(e){var r=new e.constructor(e.byteLength);new _r(r).set(new _r(e));return r}function Hi(e,r){var t=r?Vi(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.byteLength)}function Yi(e){var r=new e.constructor(e.source,Ve.exec(e));r.lastIndex=e.lastIndex;return r}function Xi(e){return gn?tr(gn.call(e)):{}}function Qi(e,r){var t=r?Vi(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)}function Ji(e,r){if(e!==r){var t=e!==i,n=e===null,a=e===e,o=Ll(e);var f=r!==i,u=r===null,c=r===r,l=Ll(r);if(!u&&!l&&!o&&e>r||o&&f&&c&&!u&&!l||n&&f&&c||!t&&c||!a){return 1}if(!n&&!o&&!l&&e<r||l&&t&&a&&!n&&!o||u&&t&&a||!f&&a||!c){return-1}}return 0}function Ki(e,r,t){var n=-1,a=e.criteria,i=r.criteria,o=a.length,f=t.length;while(++n<o){var u=Ji(a[n],i[n]);if(u){if(n>=f){return u}var c=t[n];return u*(c=="desc"?-1:1)}}return e.index-r.index}function Zi(e,r,n,a){var i=-1,o=e.length,f=n.length,u=-1,c=r.length,l=Br(o-f,0),s=t(c+l),v=!a;while(++u<c){s[u]=r[u]}while(++i<f){if(v||i<o){s[n[i]]=e[i]}}while(l--){s[u++]=e[i++]}return s}function eo(e,r,n,a){var i=-1,o=e.length,f=-1,u=n.length,c=-1,l=r.length,s=Br(o-u,0),v=t(s+l),p=!a;while(++i<s){v[i]=e[i]}var h=i;while(++c<l){v[h+c]=r[c]}while(++f<u){if(p||i<o){v[h+n[f]]=e[i++]}}return v}function ro(e,r){var n=-1,a=e.length;r||(r=t(a));while(++n<a){r[n]=e[n]}return r}function to(e,r,t,n){var a=!t;t||(t={});var o=-1,f=r.length;while(++o<f){var u=r[o];var c=n?n(t[u],e[u],u,t,e):i;if(c===i){c=e[u]}if(a){va(t,u,c)}else{fa(t,u,c)}}return t}function no(e,r){return to(e,Ho(e),r)}function ao(e,r){return to(e,Yo(e),r)}function io(e,r){return function(t,n){var a=fl(t)?yt:ca,i=r?r():{};return a(t,e,Uo(n,2),i)}}function oo(e){return bi(function(r,t){var n=-1,a=t.length,o=a>1?t[a-1]:i,f=a>2?t[2]:i;o=e.length>3&&typeof o=="function"?(a--,o):i;if(f&&of(t[0],t[1],f)){o=a<3?i:o;a=1}r=tr(r);while(++n<a){var u=t[n];if(u){e(r,u,n,o)}}return r})}function fo(e,r){return function(t,n){if(t==null){return t}if(!cl(t)){return e(t,n)}var a=t.length,i=r?a:-1,o=tr(t);while(r?i--:++i<a){if(n(o[i],i,o)===false){break}}return t}}function uo(e){return function(r,t,n){var a=-1,i=tr(r),o=n(r),f=o.length;while(f--){var u=o[e?f:++a];if(t(i[u],u,i)===false){break}}return r}}function co(e,r,t){var n=r&b,a=vo(e);function i(){var r=this&&this!==ot&&this instanceof i?a:e;return r.apply(n?t:this,arguments)}return i}function lo(e){return function(r){r=Kl(r);var t=nn(r)?dn(r):i;var n=t?t[0]:r.charAt(0);var a=t?qi(t,1).join(""):r.slice(1);return n[e]()+a}}function so(e){return function(r){return jt(_v(Hs(r).replace(Ur,"")),e,"")}}function vo(e){return function(){var r=arguments;switch(r.length){case 0:return new e;case 1:return new e(r[0]);case 2:return new e(r[0],r[1]);case 3:return new e(r[0],r[1],r[2]);case 4:return new e(r[0],r[1],r[2],r[3]);case 5:return new e(r[0],r[1],r[2],r[3],r[4]);case 6:return new e(r[0],r[1],r[2],r[3],r[4],r[5]);case 7:return new e(r[0],r[1],r[2],r[3],r[4],r[5],r[6])}var t=wn(e.prototype),n=e.apply(t,r);return kl(n)?n:t}}function po(e,r,n){var a=vo(e);function o(){var f=arguments.length,u=t(f),c=f,l=Wo(o);while(c--){u[c]=arguments[c]}var s=f<3&&u[0]!==l&&u[f-1]!==l?[]:cn(u,l);f-=s.length;if(f<n){return Eo(e,r,go,o.placeholder,i,u,s,i,i,n-f)}var v=this&&this!==ot&&this instanceof o?a:e;return bt(v,this,u)}return o}function ho(e){return function(r,t,n){var a=tr(r);if(!cl(r)){var o=Uo(t,3);r=xs(r);t=function(e){return o(a[e],e,a)}}var f=e(r,t,n);return f>-1?a[o?r[f]:f]:i}}function mo(e){return Fo(function(r){var t=r.length,n=t,a=zn.prototype.thru;if(e){r.reverse()}while(n--){var o=r[n];if(typeof o!="function"){throw new ir(c)}if(a&&!f&&Bo(o)=="wrapper"){var f=new zn([],true)}}n=f?n:t;while(++n<t){o=r[n];var u=Bo(o),l=u=="wrapper"?Ro(o):i;if(l&&cf(l[0])&&l[1]==(E|x|k|S)&&!l[4].length&&l[9]==1){f=f[Bo(l[0])].apply(f,l[3])}else{f=o.length==1&&cf(o)?f[u]():f.thru(o)}}return function(){var e=arguments,n=e[0];if(f&&e.length==1&&fl(n)){return f.plant(n).value()}var a=0,i=t?r[a].apply(this,e):n;while(++a<t){i=r[a].call(this,i)}return i}})}function go(e,r,n,a,o,f,u,c,l,s){var v=r&E,p=r&b,h=r&y,d=r&(x|w),m=r&A,g=h?i:vo(e);function _(){var i=arguments.length,b=t(i),y=i;while(y--){b[y]=arguments[y]}if(d){var x=Wo(_),w=Kt(b,x)}if(a){b=Zi(b,a,o,d)}if(f){b=eo(b,f,u,d)}i-=w;if(d&&i<s){var k=cn(b,x);return Eo(e,r,go,_.placeholder,n,b,k,c,l,s-i)}var z=p?n:this,E=h?z[e]:e;i=b.length;if(c){b=xf(b,c)}else if(m&&i>1){b.reverse()}if(v&&l<i){b.length=l}if(this&&this!==ot&&this instanceof _){E=g||vo(E)}return E.apply(z,b)}return _}function bo(e,r){return function(t,n){return Ba(t,e,r(n),{})}}function yo(e,r){return function(t,n){var a;if(t===i&&n===i){return r}if(t!==i){a=t}if(n!==i){if(a===i){return n}if(typeof t=="string"||typeof n=="string"){t=Mi(t);n=Mi(n)}else{t=Ti(t);n=Ti(n)}a=e(t,n)}return a}}function _o(e){return Fo(function(r){r=St(r,Ht(Uo()));return bi(function(t){var n=this;return e(r,function(e){return bt(e,n,t)})})})}function xo(e,r){r=r===i?" ":Mi(r);var t=r.length;if(t<2){return t?gi(r,e):r}var n=gi(r,Ir(e/hn(r)));return nn(r)?qi(dn(n),0,e).join(""):n.slice(0,e)}function wo(e,r,n,a){var i=r&b,o=vo(e);function f(){var r=-1,u=arguments.length,c=-1,l=a.length,s=t(l+u),v=this&&this!==ot&&this instanceof f?o:e;while(++c<l){s[c]=a[c]}while(u--){s[c++]=arguments[++r]}return bt(v,i?n:this,s)}return f}function ko(e){return function(r,t,n){if(n&&typeof n!="number"&&of(r,t,n)){t=n=i}r=Vl(r);if(t===i){t=r;r=0}else{t=Vl(t)}n=n===i?r<t?1:-1:Vl(n);return mi(r,t,n,e)}}function zo(e){return function(r,t){if(!(typeof r=="string"&&typeof t=="string")){r=Xl(r);t=Xl(t)}return e(r,t)}}function Eo(e,r,t,n,a,o,f,u,c,l){var s=r&x,v=s?f:i,p=s?i:f,h=s?o:i,d=s?i:o;r|=s?k:z;r&=~(s?z:k);if(!(r&_)){r&=~(b|y)}var m=[e,r,a,h,v,d,p,u,c,l];var g=t.apply(i,m);if(cf(e)){kf(g,m)}g.placeholder=n;return Sf(g,e,r)}function So(e){var r=rr[e];return function(e,t){e=Xl(e);t=t==null?0:Wr(Hl(t),292);if(t){var n=(Kl(e)+"e").split("e"),a=r(n[0]+"e"+(+n[1]+t));n=(Kl(a)+"e").split("e");return+(n[0]+"e"+(+n[1]-t))}return r(e)}}var Ao=!(rt&&1/ln(new rt([,-0]))[1]==P)?Nv:function(e){return new rt(e)};function jo(e){return function(r){var t=Xo(r);if(t==K){return fn(r)}if(t==ie){return sn(r)}return Vt(r,e(r))}}function Oo(e,r,t,n,a,o,f,u){var l=r&y;if(!l&&typeof e!="function"){throw new ir(c)}var s=n?n.length:0;if(!s){r&=~(k|z);n=a=i}f=f===i?f:Br(Hl(f),0);u=u===i?u:Hl(u);s-=a?a.length:0;if(r&z){var v=n,p=a;n=a=i}var h=l?i:Ro(e);var d=[e,r,t,n,a,v,p,o,f,u];if(h){mf(d,h)}e=d[0];r=d[1];t=d[2];n=d[3];a=d[4];u=d[9]=d[9]===i?l?0:e.length:Br(d[9]-s,0);if(!u&&r&(x|w)){r&=~(x|w)}if(!r||r==b){var m=co(e,r,t)}else if(r==x||r==w){m=po(e,r,u)}else if((r==k||r==(b|k))&&!a.length){m=wo(e,r,t,n)}else{m=go.apply(i,d)}var g=h?wi:kf;return Sf(g(m,d),e,r)}function To(e,r,t,n){if(e===i||nl(e,ur[t])&&!sr.call(n,t)){return r}return e}function Mo(e,r,t,n,a,o){if(kl(e)&&kl(r)){o.set(r,e);oi(e,r,i,Mo,o);o["delete"](r)}return e}function Do(e){return Il(e)?i:e}function Io(e,r,t,n,a,o){var f=t&m,u=e.length,c=r.length;if(u!=c&&!(f&&c>u)){return false}var l=o.get(e);if(l&&o.get(r)){return l==r}var s=-1,v=true,p=t&g?new Hn:i;o.set(e,r);o.set(r,e);while(++s<u){var h=e[s],d=r[s];if(n){var b=f?n(d,h,s,r,e,o):n(h,d,s,e,r,o)}if(b!==i){if(b){continue}v=false;break}if(p){if(!Tt(r,function(e,r){if(!Xt(p,r)&&(h===e||a(h,e,t,n,o))){return p.push(r)}})){v=false;break}}else if(!(h===d||a(h,d,t,n,o))){v=false;break}}o["delete"](e);o["delete"](r);return v}function Co(e,r,t,n,a,i,o){switch(t){case ve:if(e.byteLength!=r.byteLength||e.byteOffset!=r.byteOffset){return false}e=e.buffer;r=r.buffer;case se:if(e.byteLength!=r.byteLength||!i(new _r(e),new _r(r))){return false}return true;case V:case H:case Z:return nl(+e,+r);case X:return e.name==r.name&&e.message==r.message;case ae:case oe:return e==r+"";case K:var f=fn;case ie:var u=n&m;f||(f=ln);if(e.size!=r.size&&!u){return false}var c=o.get(e);if(c){return c==r}n|=g;o.set(e,r);var l=Io(f(e),f(r),n,a,i,o);o["delete"](e);return l;case fe:if(gn){return gn.call(e)==gn.call(r)}}return false}function Po(e,r,t,n,a,o){var f=t&m,u=No(e),c=u.length,l=No(r),s=l.length;if(c!=s&&!f){return false}var v=c;while(v--){var p=u[v];if(!(f?p in r:sr.call(r,p))){return false}}var h=o.get(e);if(h&&o.get(r)){return h==r}var d=true;o.set(e,r);o.set(r,e);var g=f;while(++v<c){p=u[v];var b=e[p],y=r[p];if(n){var _=f?n(y,b,p,r,e,o):n(b,y,p,e,r,o)}if(!(_===i?b===y||a(b,y,t,n,o):_)){d=false;break}g||(g=p=="constructor")}if(d&&!g){var x=e.constructor,w=r.constructor;if(x!=w&&("constructor"in e&&"constructor"in r)&&!(typeof x=="function"&&x instanceof x&&typeof w=="function"&&w instanceof w)){d=false}}o["delete"](e);o["delete"](r);return d}function Fo(e){return Ef(yf(e,i,Hf),e+"")}function No(e){return Ia(e,xs,Ho)}function Lo(e){return Ia(e,ws,Yo)}var Ro=!ft?Nv:function(e){return ft.get(e)};function Bo(e){var r=e.name+"",t=ut[r],n=sr.call(ut,r)?t.length:0;while(n--){var a=t[n],i=a.func;if(i==null||i==e){return a.name}}return r}function Wo(e){var r=sr.call(_n,"placeholder")?_n:e;return r.placeholder}function Uo(){var e=_n.iteratee||Tv;e=e===Tv?Za:e;return arguments.length?e(arguments[0],arguments[1]):e}function qo(e,r){var t=e.__data__;return uf(r)?t[typeof r=="string"?"string":"hash"]:t.map}function Go(e){var r=xs(e),t=r.length;while(t--){var n=r[t],a=e[n];r[t]=[n,a,pf(a)]}return r}function $o(e,r){var t=tn(e,r);return Xa(t)?t:i}function Vo(e){var r=sr.call(e,jr),t=e[jr];try{e[jr]=i;var n=true}catch(o){}var a=hr.call(e);if(n){if(r){e[jr]=t}else{delete e[jr]}}return a}var Ho=!Pr?Vv:function(e){if(e==null){return[]}e=tr(e);return kt(Pr(e),function(r){return zr.call(e,r)})};var Yo=!Pr?Vv:function(e){var r=[];while(e){At(r,Ho(e));e=wr(e)}return r};var Xo=Ca;if(Kr&&Xo(new Kr(new ArrayBuffer(1)))!=ve||Zr&&Xo(new Zr)!=K||et&&Xo(et.resolve())!=te||rt&&Xo(new rt)!=ie||at&&Xo(new at)!=ce){Xo=function(e){var r=Ca(e),t=r==re?e.constructor:i,n=t?Mf(t):"";if(n){switch(n){case lt:return ve;case st:return K;case Mt:return te;case Dt:return ie;case Wt:return ce}}return r}}function Qo(e,r,t){var n=-1,a=t.length;while(++n<a){var i=t[n],o=i.size;switch(i.type){case"drop":e+=o;break;case"dropRight":r-=o;break;case"take":r=Wr(r,e+o);break;case"takeRight":e=Br(e,r-o);break}}return{start:e,end:r}}function Jo(e){var r=e.match(We);return r?r[1].split(Ue):[]}function Ko(e,r,t){r=Wi(r,e);var n=-1,a=r.length,i=false;while(++n<a){var o=Tf(r[n]);if(!(i=e!=null&&t(e,o))){break}e=e[o]}if(i||++n!=a){return i}a=e==null?0:e.length;return!!a&&wl(a)&&af(o,a)&&(fl(e)||ol(e))}function Zo(e){var r=e.length,t=new e.constructor(r);if(r&&typeof e[0]=="string"&&sr.call(e,"index")){t.index=e.index;t.input=e.input}return t}function ef(e){return typeof e.constructor=="function"&&!vf(e)?wn(wr(e)):{}}function rf(e,r,t){var n=e.constructor;switch(r){case se:return Vi(e);case V:case H:return new n(+e);case ve:return Hi(e,t);case pe:case he:case de:case me:case ge:case be:case ye:case _e:case xe:return Qi(e,t);case K:return new n;case Z:case oe:return new n(e);case ae:return Yi(e);case ie:return new n;case fe:return Xi(e)}}function tf(e,r){var t=r.length;if(!t){return e}var n=t-1;r[n]=(t>1?"& ":"")+r[n];r=r.join(t>2?", ":" ");return e.replace(Be,"{\n/* [wrapped with "+r+"] */\n")}function nf(e){return fl(e)||ol(e)||!!(Sr&&e&&e[Sr])}function af(e,r){var t=typeof e;r=r==null?F:r;return!!r&&(t=="number"||t!="symbol"&&Je.test(e))&&(e>-1&&e%1==0&&e<r)}function of(e,r,t){if(!kl(t)){return false}var n=typeof r;if(n=="number"?cl(t)&&af(r,t.length):n=="string"&&r in t){return nl(t[r],e)}return false}function ff(e,r){if(fl(e)){return false}var t=typeof e;if(t=="number"||t=="symbol"||t=="boolean"||e==null||Ll(e)){return true}return Ie.test(e)||!De.test(e)||r!=null&&e in tr(r)}function uf(e){var r=typeof e;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?e!=="__proto__":e===null}function cf(e){var r=Bo(e),t=_n[r];if(typeof t!="function"||!(r in En.prototype)){return false}if(e===t){return true}var n=Ro(t);return!!n&&e===n[0]}function lf(e){return!!pr&&pr in e}var sf=cr?_l:Hv;function vf(e){var r=e&&e.constructor,t=typeof r=="function"&&r.prototype||ur;return e===t}function pf(e){return e===e&&!kl(e)}function hf(e,r){return function(t){if(t==null){return false}return t[e]===r&&(r!==i||e in tr(t))}}function df(e){var r=Rc(e,function(e){if(t.size===s){t.clear()}return e});var t=r.cache;return r}function mf(e,r){var t=e[1],n=r[1],a=t|n,i=a<(b|y|E);var o=n==E&&t==x||n==E&&t==S&&e[7].length<=r[8]||n==(E|S)&&r[7].length<=r[8]&&t==x;if(!(i||o)){return e}if(n&b){e[2]=r[2];a|=t&b?0:_}var f=r[3];if(f){var u=e[3];e[3]=u?Zi(u,f,r[4]):f;e[4]=u?cn(e[3],v):r[4]}f=r[5];if(f){u=e[5];e[5]=u?eo(u,f,r[6]):f;e[6]=u?cn(e[5],v):r[6]}f=r[7];if(f){e[7]=f}if(n&E){e[8]=e[8]==null?r[8]:Wr(e[8],r[8])}if(e[9]==null){e[9]=r[9]}e[0]=r[0];e[1]=a;return e}function gf(e){var r=[];if(e!=null){for(var t in tr(e)){r.push(t)}}return r}function bf(e){return hr.call(e)}function yf(e,r,n){r=Br(r===i?e.length-1:r,0);return function(){var a=arguments,i=-1,o=Br(a.length-r,0),f=t(o);while(++i<o){f[i]=a[r+i]}i=-1;var u=t(r+1);while(++i<r){u[i]=a[i]}u[r]=n(f);return bt(e,this,u)}}function _f(e,r){return r.length<2?e:Da(e,Ei(r,0,-1))}function xf(e,r){var t=e.length,n=Wr(r.length,t),a=ro(e);while(n--){var o=r[n];e[n]=af(o,t)?a[o]:i}return e}function wf(e,r){if(r=="__proto__"){return}return e[r]}var kf=Af(wi);var zf=Dr||function(e,r){return ot.setTimeout(e,r)};var Ef=Af(ki);function Sf(e,r,t){var n=r+"";return Ef(e,tf(n,Df(Jo(n),t)))}function Af(e){var r=0,t=0;return function(){var n=Gr(),a=M-(n-t);t=n;if(a>0){if(++r>=T){return arguments[0]}}else{r=0}return e.apply(i,arguments)}}function jf(e,r){var t=-1,n=e.length,a=n-1;r=r===i?n:r;while(++t<r){var o=di(t,a),f=e[o];e[o]=e[t];e[t]=f}e.length=r;return e}var Of=df(function(e){var r=[];if(e.charCodeAt(0)===46){r.push("")}e.replace(Ce,function(e,t,n,a){r.push(n?a.replace(Ge,"$1"):t||e)});return r});function Tf(e){if(typeof e=="string"||Ll(e)){return e}var r=e+"";return r=="0"&&1/e==-P?"-0":r}function Mf(e){if(e!=null){try{return lr.call(e)}catch(r){}try{return e+""}catch(r){}}return""}function Df(e,r){_t(U,function(t){var n="_."+t[0];if(r&t[1]&&!zt(e,n)){e.push(n)}});return e.sort()}function If(e){if(e instanceof En){return e.clone()}var r=new zn(e.__wrapped__,e.__chain__);r.__actions__=ro(e.__actions__);r.__index__=e.__index__;r.__values__=e.__values__;return r}function Cf(e,r,n){if(n?of(e,r,n):r===i){r=1}else{r=Br(Hl(r),0)}var a=e==null?0:e.length;if(!a||r<1){return[]}var o=0,f=0,u=t(Ir(a/r));while(o<a){u[f++]=Ei(e,o,o+=r)}return u}function Pf(e){var r=-1,t=e==null?0:e.length,n=0,a=[];while(++r<t){var i=e[r];if(i){a[n++]=i}}return a}function Ff(){var e=arguments.length;if(!e){return[]}var r=t(e-1),n=arguments[0],a=e;while(a--){r[a-1]=arguments[a]}return At(fl(n)?ro(n):[n],Sa(r,1))}var Nf=bi(function(e,r){return ll(e)?ya(e,Sa(r,1,ll,true)):[]});var Lf=bi(function(e,r){var t=au(r);if(ll(t)){t=i}return ll(e)?ya(e,Sa(r,1,ll,true),Uo(t,2)):[]});var Rf=bi(function(e,r){var t=au(r);if(ll(t)){t=i}return ll(e)?ya(e,Sa(r,1,ll,true),i,t):[]});function Bf(e,r,t){var n=e==null?0:e.length;if(!n){return[]}r=t||r===i?1:Hl(r);return Ei(e,r<0?0:r,n)}function Wf(e,r,t){var n=e==null?0:e.length;if(!n){return[]}r=t||r===i?1:Hl(r);r=n-r;return Ei(e,0,r<0?0:r)}function Uf(e,r){return e&&e.length?Pi(e,Uo(r,3),true,true):[]}function qf(e,r){return e&&e.length?Pi(e,Uo(r,3),true):[]}function Gf(e,r,t,n){var a=e==null?0:e.length;if(!a){return[]}if(t&&typeof t!="number"&&of(e,r,t)){t=0;n=a}return za(e,r,t,n)}function $f(e,r,t){var n=e==null?0:e.length;if(!n){return-1}var a=t==null?0:Hl(t);if(a<0){a=Br(n+a,0)}return Pt(e,Uo(r,3),a)}function Vf(e,r,t){var n=e==null?0:e.length;if(!n){return-1}var a=n-1;if(t!==i){a=Hl(t);a=t<0?Br(n+a,0):Wr(a,n-1)}return Pt(e,Uo(r,3),a,true)}function Hf(e){var r=e==null?0:e.length;return r?Sa(e,1):[]}function Yf(e){var r=e==null?0:e.length;return r?Sa(e,P):[]}function Xf(e,r){var t=e==null?0:e.length;if(!t){return[]}r=r===i?1:Hl(r);return Sa(e,r)}function Qf(e){var r=-1,t=e==null?0:e.length,n={};while(++r<t){var a=e[r];n[a[0]]=a[1]}return n}function Jf(e){return e&&e.length?e[0]:i}function Kf(e,r,t){var n=e==null?0:e.length;if(!n){return-1}var a=t==null?0:Hl(t);if(a<0){a=Br(n+a,0)}return Ft(e,r,a)}function Zf(e){var r=e==null?0:e.length;return r?Ei(e,0,-1):[]}var eu=bi(function(e){var r=St(e,Ri);return r.length&&r[0]===e[0]?Ra(r):[]});var ru=bi(function(e){var r=au(e),t=St(e,Ri);if(r===au(t)){r=i}else{t.pop()}return t.length&&t[0]===e[0]?Ra(t,Uo(r,2)):[]});var tu=bi(function(e){var r=au(e),t=St(e,Ri);r=typeof r=="function"?r:i;if(r){t.pop()}return t.length&&t[0]===e[0]?Ra(t,i,r):[]});function nu(e,r){return e==null?"":Lr.call(e,r)}function au(e){var r=e==null?0:e.length;return r?e[r-1]:i}function iu(e,r,t){var n=e==null?0:e.length;if(!n){return-1}var a=n;if(t!==i){a=Hl(t);a=a<0?Br(n+a,0):Wr(a,n-1)}return r===r?pn(e,r,a):Pt(e,Lt,a,true)}function ou(e,r){return e&&e.length?ui(e,Hl(r)):i}var fu=bi(uu);function uu(e,r){return e&&e.length&&r&&r.length?pi(e,r):e}function cu(e,r,t){return e&&e.length&&r&&r.length?pi(e,r,Uo(t,2)):e}function lu(e,r,t){return e&&e.length&&r&&r.length?pi(e,r,i,t):e}var su=Fo(function(e,r){var t=e==null?0:e.length,n=pa(e,r);hi(e,St(r,function(e){return af(e,t)?+e:e}).sort(Ji));return n});function vu(e,r){var t=[];if(!(e&&e.length)){return t}var n=-1,a=[],i=e.length;r=Uo(r,3);while(++n<i){var o=e[n];if(r(o,n,e)){t.push(o);a.push(n)}}hi(e,a);return t}function pu(e){return e==null?e:Hr.call(e)}function hu(e,r,t){var n=e==null?0:e.length;if(!n){return[]}if(t&&typeof t!="number"&&of(e,r,t)){r=0;t=n}else{r=r==null?0:Hl(r);t=t===i?n:Hl(t)}return Ei(e,r,t)}function du(e,r){return Ai(e,r)}function mu(e,r,t){return ji(e,r,Uo(t,2))}function gu(e,r){var t=e==null?0:e.length;if(t){var n=Ai(e,r);if(n<t&&nl(e[n],r)){return n}}return-1}function bu(e,r){return Ai(e,r,true)}function yu(e,r,t){return ji(e,r,Uo(t,2),true)}function _u(e,r){var t=e==null?0:e.length;if(t){var n=Ai(e,r,true)-1;if(nl(e[n],r)){return n}}return-1}function xu(e){return e&&e.length?Oi(e):[]}function wu(e,r){return e&&e.length?Oi(e,Uo(r,2)):[]}function ku(e){var r=e==null?0:e.length;return r?Ei(e,1,r):[]}function zu(e,r,t){if(!(e&&e.length)){return[]}r=t||r===i?1:Hl(r);return Ei(e,0,r<0?0:r)}function Eu(e,r,t){var n=e==null?0:e.length;if(!n){return[]}r=t||r===i?1:Hl(r);r=n-r;return Ei(e,r<0?0:r,n)}function Su(e,r){return e&&e.length?Pi(e,Uo(r,3),false,true):[]}function Au(e,r){return e&&e.length?Pi(e,Uo(r,3)):[]}var ju=bi(function(e){return Di(Sa(e,1,ll,true))});var Ou=bi(function(e){var r=au(e);if(ll(r)){r=i}return Di(Sa(e,1,ll,true),Uo(r,2))});var Tu=bi(function(e){var r=au(e);r=typeof r=="function"?r:i;return Di(Sa(e,1,ll,true),i,r)});function Mu(e){return e&&e.length?Di(e):[]}function Du(e,r){return e&&e.length?Di(e,Uo(r,2)):[]}function Iu(e,r){r=typeof r=="function"?r:i;return e&&e.length?Di(e,i,r):[]}function Cu(e){if(!(e&&e.length)){return[]}var r=0;e=kt(e,function(e){if(ll(e)){r=Br(e.length,r);return true}});return $t(r,function(r){return St(e,Bt(r))})}function Pu(e,r){if(!(e&&e.length)){return[]}var t=Cu(e);if(r==null){return t}return St(t,function(e){return bt(r,i,e)})}var Fu=bi(function(e,r){return ll(e)?ya(e,r):[]});var Nu=bi(function(e){return Ni(kt(e,ll))});var Lu=bi(function(e){var r=au(e);if(ll(r)){r=i}return Ni(kt(e,ll),Uo(r,2))});var Ru=bi(function(e){var r=au(e);r=typeof r=="function"?r:i;return Ni(kt(e,ll),i,r)});var Bu=bi(Cu);function Wu(e,r){return Li(e||[],r||[],fa)}function Uu(e,r){return Li(e||[],r||[],xi)}var qu=bi(function(e){var r=e.length,t=r>1?e[r-1]:i;t=typeof t=="function"?(e.pop(),t):i;return Pu(e,t)});function Gu(e){var r=_n(e);r.__chain__=true;return r}function $u(e,r){r(e);return e}function Vu(e,r){return r(e)}var Hu=Fo(function(e){var r=e.length,t=r?e[0]:0,n=this.__wrapped__,a=function(r){return pa(r,e)};if(r>1||this.__actions__.length||!(n instanceof En)||!af(t)){return this.thru(a)}n=n.slice(t,+t+(r?1:0));n.__actions__.push({func:Vu,args:[a],thisArg:i});return new zn(n,this.__chain__).thru(function(e){if(r&&!e.length){e.push(i)}return e})});function Yu(){return Gu(this)}function Xu(){return new zn(this.value(),this.__chain__)}function Qu(){if(this.__values__===i){this.__values__=$l(this.value())}var e=this.__index__>=this.__values__.length,r=e?i:this.__values__[this.__index__++];return{done:e,value:r}}function Ju(){return this}function Ku(e){var r,t=this;while(t instanceof kn){var n=If(t);n.__index__=0;n.__values__=i;if(r){a.__wrapped__=n}else{r=n}var a=n;t=t.__wrapped__}a.__wrapped__=e;return r}function Zu(){var e=this.__wrapped__;if(e instanceof En){var r=e;if(this.__actions__.length){r=new En(this)}r=r.reverse();r.__actions__.push({func:Vu,args:[pu],thisArg:i});return new zn(r,this.__chain__)}return this.thru(pu)}function ec(){return Fi(this.__wrapped__,this.__actions__)}var rc=io(function(e,r,t){if(sr.call(e,t)){++e[t]}else{va(e,t,1)}});function tc(e,r,t){var n=fl(e)?wt:wa;if(t&&of(e,r,t)){r=i}return n(e,Uo(r,3))}function nc(e,r){var t=fl(e)?kt:Ea;return t(e,Uo(r,3))}var ac=ho($f);var ic=ho(Vf);function oc(e,r){return Sa(dc(e,r),1)}function fc(e,r){return Sa(dc(e,r),P)}function uc(e,r,t){t=t===i?1:Hl(t);return Sa(dc(e,r),t)}function cc(e,r){var t=fl(e)?_t:_a;return t(e,Uo(r,3))}function lc(e,r){var t=fl(e)?xt:xa;return t(e,Uo(r,3))}var sc=io(function(e,r,t){if(sr.call(e,t)){e[t].push(r)}else{va(e,t,[r])}});function vc(e,r,t,n){e=cl(e)?e:Bs(e);t=t&&!n?Hl(t):0;var a=e.length;if(t<0){t=Br(a+t,0)}return Nl(e)?t<=a&&e.indexOf(r,t)>-1:!!a&&Ft(e,r,t)>-1}var pc=bi(function(e,r,n){var a=-1,i=typeof r=="function",o=cl(e)?t(e.length):[];_a(e,function(e){o[++a]=i?bt(r,e,n):Wa(e,r,n)});return o});var hc=io(function(e,r,t){va(e,t,r)});function dc(e,r){var t=fl(e)?St:ni;return t(e,Uo(r,3))}function mc(e,r,t,n){if(e==null){return[]}if(!fl(r)){r=r==null?[]:[r]}t=n?i:t;if(!fl(t)){t=t==null?[]:[t]}return ci(e,r,t)}var gc=io(function(e,r,t){e[t?0:1].push(r)},function(){return[[],[]]});function bc(e,r,t){var n=fl(e)?jt:Ut,a=arguments.length<3;return n(e,Uo(r,4),t,a,_a)}function yc(e,r,t){var n=fl(e)?Ot:Ut,a=arguments.length<3;return n(e,Uo(r,4),t,a,xa)}function _c(e,r){var t=fl(e)?kt:Ea;return t(e,Bc(Uo(r,3)))}function xc(e){var r=fl(e)?na:yi;return r(e)}function wc(e,r,t){if(t?of(e,r,t):r===i){r=1}else{r=Hl(r)}var n=fl(e)?aa:_i;return n(e,r)}function kc(e){var r=fl(e)?ia:zi;return r(e)}function zc(e){if(e==null){return 0}if(cl(e)){return Nl(e)?hn(e):e.length}var r=Xo(e);if(r==K||r==ie){return e.size}return ei(e).length}function Ec(e,r,t){var n=fl(e)?Tt:Si;if(t&&of(e,r,t)){r=i}return n(e,Uo(r,3))}var Sc=bi(function(e,r){if(e==null){return[]}var t=r.length;if(t>1&&of(e,r[0],r[1])){r=[]}else if(t>2&&of(r[0],r[1],r[2])){r=[r[0]]}return ci(e,Sa(r,1),[])});var Ac=Mr||function(){return ot.Date.now()};function jc(e,r){if(typeof r!="function"){throw new ir(c)}e=Hl(e);return function(){if(--e<1){return r.apply(this,arguments)}}}function Oc(e,r,t){r=t?i:r;r=e&&r==null?e.length:r;return Oo(e,E,i,i,i,i,r)}function Tc(e,r){var t;if(typeof r!="function"){throw new ir(c)}e=Hl(e);return function(){if(--e>0){t=r.apply(this,arguments)}if(e<=1){r=i}return t}}var Mc=bi(function(e,r,t){var n=b;if(t.length){var a=cn(t,Wo(Mc));n|=k}return Oo(e,n,r,t,a)});var Dc=bi(function(e,r,t){var n=b|y;if(t.length){var a=cn(t,Wo(Dc));n|=k}return Oo(r,n,e,t,a)});function Ic(e,r,t){r=t?i:r;var n=Oo(e,x,i,i,i,i,i,r);n.placeholder=Ic.placeholder;return n}function Cc(e,r,t){r=t?i:r;var n=Oo(e,w,i,i,i,i,i,r);n.placeholder=Cc.placeholder;return n}function Pc(e,r,t){var n,a,o,f,u,l,s=0,v=false,p=false,h=true;if(typeof e!="function"){throw new ir(c)}r=Xl(r)||0;if(kl(t)){v=!!t.leading;p="maxWait"in t;o=p?Br(Xl(t.maxWait)||0,r):o;h="trailing"in t?!!t.trailing:h}function d(r){var t=n,o=a;n=a=i;s=r;f=e.apply(o,t);return f}function m(e){s=e;u=zf(y,r);return v?d(e):f}function g(e){var t=e-l,n=e-s,a=r-t;return p?Wr(a,o-n):a}function b(e){var t=e-l,n=e-s;return l===i||t>=r||t<0||p&&n>=o}function y(){var e=Ac();if(b(e)){return _(e)}u=zf(y,g(e))}function _(e){u=i;if(h&&n){return d(e)}n=a=i;return f}function x(){if(u!==i){Gi(u)}s=0;n=l=a=u=i}function w(){return u===i?f:_(Ac())}function k(){var e=Ac(),t=b(e);n=arguments;a=this;l=e;if(t){if(u===i){return m(l)}if(p){u=zf(y,r);return d(l)}}if(u===i){u=zf(y,r)}return f}k.cancel=x;k.flush=w;return k}var Fc=bi(function(e,r){return ba(e,1,r)});var Nc=bi(function(e,r,t){return ba(e,Xl(r)||0,t)});function Lc(e){return Oo(e,A)}function Rc(e,r){if(typeof e!="function"||r!=null&&typeof r!="function"){throw new ir(c)}var t=function(){var n=arguments,a=r?r.apply(this,n):n[0],i=t.cache;if(i.has(a)){return i.get(a)}var o=e.apply(this,n);t.cache=i.set(a,o)||i;return o};t.cache=new(Rc.Cache||Wn);return t}Rc.Cache=Wn;function Bc(e){if(typeof e!="function"){throw new ir(c)}return function(){var r=arguments;switch(r.length){case 0:return!e.call(this);case 1:return!e.call(this,r[0]);case 2:return!e.call(this,r[0],r[1]);case 3:return!e.call(this,r[0],r[1],r[2])}return!e.apply(this,r)}}function Wc(e){return Tc(2,e)}var Uc=Ui(function(e,r){r=r.length==1&&fl(r[0])?St(r[0],Ht(Uo())):St(Sa(r,1),Ht(Uo()));var t=r.length;return bi(function(n){var a=-1,i=Wr(n.length,t);while(++a<i){n[a]=r[a].call(this,n[a])}return bt(e,this,n)})});var qc=bi(function(e,r){var t=cn(r,Wo(qc));return Oo(e,k,i,r,t)});var Gc=bi(function(e,r){var t=cn(r,Wo(Gc));return Oo(e,z,i,r,t)});var $c=Fo(function(e,r){return Oo(e,S,i,i,i,r)});function Vc(e,r){if(typeof e!="function"){throw new ir(c)}r=r===i?r:Hl(r);return bi(e,r)}function Hc(e,r){if(typeof e!="function"){throw new ir(c)}r=r==null?0:Br(Hl(r),0);return bi(function(t){var n=t[r],a=qi(t,0,r);if(n){At(a,n)}return bt(e,this,a)})}function Yc(e,r,t){var n=true,a=true;if(typeof e!="function"){throw new ir(c)}if(kl(t)){n="leading"in t?!!t.leading:n;a="trailing"in t?!!t.trailing:a}return Pc(e,r,{leading:n,maxWait:r,trailing:a})}function Xc(e){return Oc(e,1)}function Qc(e,r){return qc(Bi(r),e)}function Jc(){if(!arguments.length){return[]}var e=arguments[0];return fl(e)?e:[e]}function Kc(e){return da(e,d)}function Zc(e,r){r=typeof r=="function"?r:i;return da(e,d,r)}function el(e){return da(e,p|d)}function rl(e,r){r=typeof r=="function"?r:i;return da(e,p|d,r)}function tl(e,r){return r==null||ga(e,r,xs(r))}function nl(e,r){return e===r||e!==e&&r!==r}var al=zo(Pa);var il=zo(function(e,r){return e>=r});var ol=Ua(function(){return arguments}())?Ua:function(e){return zl(e)&&sr.call(e,"callee")&&!zr.call(e,"callee")};var fl=t.isArray;var ul=vt?Ht(vt):qa;function cl(e){return e!=null&&wl(e.length)&&!_l(e)}function ll(e){return zl(e)&&cl(e)}function sl(e){return e===true||e===false||zl(e)&&Ca(e)==V}var vl=Fr||Hv;var pl=pt?Ht(pt):Ga;function hl(e){return zl(e)&&e.nodeType===1&&!Il(e)}function dl(e){if(e==null){return true}if(cl(e)&&(fl(e)||typeof e=="string"||typeof e.splice=="function"||vl(e)||Rl(e)||ol(e))){return!e.length}var r=Xo(e);if(r==K||r==ie){return!e.size}if(vf(e)){return!ei(e).length}for(var t in e){if(sr.call(e,t)){return false}}return true}function ml(e,r){return $a(e,r)}function gl(e,r,t){t=typeof t=="function"?t:i;var n=t?t(e,r):i;return n===i?$a(e,r,i,t):!!n}function bl(e){if(!zl(e)){return false}var r=Ca(e);return r==X||r==Y||typeof e.message=="string"&&typeof e.name=="string"&&!Il(e)}function yl(e){return typeof e=="number"&&Nr(e)}function _l(e){if(!kl(e)){return false}var r=Ca(e);return r==Q||r==J||r==$||r==ne}function xl(e){return typeof e=="number"&&e==Hl(e)}function wl(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=F}function kl(e){var r=typeof e;return e!=null&&(r=="object"||r=="function")}function zl(e){return e!=null&&typeof e=="object"}var El=ht?Ht(ht):Ha;function Sl(e,r){return e===r||Ya(e,r,Go(r))}function Al(e,r,t){t=typeof t=="function"?t:i;return Ya(e,r,Go(r),t)}function jl(e){return Dl(e)&&e!=+e}function Ol(e){if(sf(e)){throw new a(u)}return Xa(e)}function Tl(e){return e===null}function Ml(e){return e==null}function Dl(e){return typeof e=="number"||zl(e)&&Ca(e)==Z}function Il(e){if(!zl(e)||Ca(e)!=re){return false}var r=wr(e);if(r===null){return true}var t=sr.call(r,"constructor")&&r.constructor;return typeof t=="function"&&t instanceof t&&lr.call(t)==dr}var Cl=dt?Ht(dt):Qa;function Pl(e){return xl(e)&&e>=-F&&e<=F}var Fl=mt?Ht(mt):Ja;function Nl(e){return typeof e=="string"||!fl(e)&&zl(e)&&Ca(e)==oe}function Ll(e){return typeof e=="symbol"||zl(e)&&Ca(e)==fe}var Rl=gt?Ht(gt):Ka;function Bl(e){return e===i}function Wl(e){return zl(e)&&Xo(e)==ce}function Ul(e){return zl(e)&&Ca(e)==le}var ql=zo(ti);var Gl=zo(function(e,r){return e<=r});function $l(e){if(!e){return[]}if(cl(e)){return Nl(e)?dn(e):ro(e)}if(Ar&&e[Ar]){return on(e[Ar]())}var r=Xo(e),t=r==K?fn:r==ie?ln:Bs;return t(e)}function Vl(e){if(!e){return e===0?e:0}e=Xl(e);if(e===P||e===-P){var r=e<0?-1:1;return r*N}return e===e?e:0}function Hl(e){var r=Vl(e),t=r%1;return r===r?t?r-t:r:0}function Yl(e){return e?ha(Hl(e),0,R):0}function Xl(e){if(typeof e=="number"){return e}if(Ll(e)){return L}if(kl(e)){var r=typeof e.valueOf=="function"?e.valueOf():e;e=kl(r)?r+"":r}if(typeof e!="string"){return e===0?e:+e}e=e.replace(Ne,"");var t=Ye.test(e);return t||Qe.test(e)?nt(e.slice(2),t?2:8):He.test(e)?L:+e}function Ql(e){return to(e,ws(e))}function Jl(e){return e?ha(Hl(e),-F,F):e===0?e:0}function Kl(e){return e==null?"":Mi(e)}var Zl=oo(function(e,r){if(vf(r)||cl(r)){to(r,xs(r),e);return}for(var t in r){if(sr.call(r,t)){fa(e,t,r[t])}}});var es=oo(function(e,r){to(r,ws(r),e)});var rs=oo(function(e,r,t,n){to(r,ws(r),e,n)});var ts=oo(function(e,r,t,n){to(r,xs(r),e,n)});var ns=Fo(pa);function as(e,r){var t=wn(e);return r==null?t:la(t,r)}var is=bi(function(e,r){e=tr(e);var t=-1;var n=r.length;var a=n>2?r[2]:i;if(a&&of(r[0],r[1],a)){n=1}while(++t<n){var o=r[t];var f=ws(o);var u=-1;var c=f.length;while(++u<c){var l=f[u];var s=e[l];if(s===i||nl(s,ur[l])&&!sr.call(e,l)){e[l]=o[l]}}}return e});var os=bi(function(e){e.push(i,Mo);return bt(Ss,i,e)});function fs(e,r){return Ct(e,Uo(r,3),Oa)}function us(e,r){return Ct(e,Uo(r,3),Ta)}function cs(e,r){return e==null?e:Aa(e,Uo(r,3),ws)}function ls(e,r){return e==null?e:ja(e,Uo(r,3),ws)}function ss(e,r){return e&&Oa(e,Uo(r,3))}function vs(e,r){return e&&Ta(e,Uo(r,3))}function ps(e){return e==null?[]:Ma(e,xs(e))}function hs(e){return e==null?[]:Ma(e,ws(e))}function ds(e,r,t){var n=e==null?i:Da(e,r);return n===i?t:n}function ms(e,r){return e!=null&&Ko(e,r,Fa)}function gs(e,r){return e!=null&&Ko(e,r,Na)}var bs=bo(function(e,r,t){if(r!=null&&typeof r.toString!="function"){r=hr.call(r)}e[r]=t},Ev(Ov));var ys=bo(function(e,r,t){if(r!=null&&typeof r.toString!="function"){r=hr.call(r)}if(sr.call(e,r)){e[r].push(t)}else{e[r]=[t]}},Uo);var _s=bi(Wa);function xs(e){return cl(e)?ta(e):ei(e)}function ws(e){return cl(e)?ta(e,true):ri(e)}function ks(e,r){var t={};r=Uo(r,3);Oa(e,function(e,n,a){va(t,r(e,n,a),e)});return t}function zs(e,r){var t={};r=Uo(r,3);Oa(e,function(e,n,a){va(t,n,r(e,n,a))});return t}var Es=oo(function(e,r,t){oi(e,r,t)});var Ss=oo(function(e,r,t,n){oi(e,r,t,n)});var As=Fo(function(e,r){var t={};if(e==null){return t}var n=false;r=St(r,function(r){r=Wi(r,e);n||(n=r.length>1);return r});to(e,Lo(e),t);if(n){t=da(t,p|h|d,Do)}var a=r.length;while(a--){Ii(t,r[a])}return t});function js(e,r){return Ts(e,Bc(Uo(r)))}var Os=Fo(function(e,r){return e==null?{}:li(e,r)});function Ts(e,r){if(e==null){return{}}var t=St(Lo(e),function(e){return[e]});r=Uo(r);return si(e,t,function(e,t){return r(e,t[0])})}function Ms(e,r,t){r=Wi(r,e);var n=-1,a=r.length;if(!a){a=1;e=i}while(++n<a){var o=e==null?i:e[Tf(r[n])];if(o===i){n=a;o=t}e=_l(o)?o.call(e):o}return e}function Ds(e,r,t){return e==null?e:xi(e,r,t)}function Is(e,r,t,n){n=typeof n=="function"?n:i;return e==null?e:xi(e,r,t,n)}var Cs=jo(xs);var Ps=jo(ws);function Fs(e,r,t){var n=fl(e),a=n||vl(e)||Rl(e);r=Uo(r,4);if(t==null){var i=e&&e.constructor;if(a){t=n?new i:[]}else if(kl(e)){t=_l(i)?wn(wr(e)):{}}else{t={}}}(a?_t:Oa)(e,function(e,n,a){return r(t,e,n,a)});return t}function Ns(e,r){return e==null?true:Ii(e,r)}function Ls(e,r,t){return e==null?e:Ci(e,r,Bi(t))}function Rs(e,r,t,n){n=typeof n=="function"?n:i;return e==null?e:Ci(e,r,Bi(t),n)}function Bs(e){return e==null?[]:Yt(e,xs(e))}function Ws(e){return e==null?[]:Yt(e,ws(e))}function Us(e,r,t){if(t===i){t=r;r=i}if(t!==i){t=Xl(t);t=t===t?t:0}if(r!==i){r=Xl(r);r=r===r?r:0}return ha(Xl(e),r,t)}function qs(e,r,t){r=Vl(r);if(t===i){t=r;r=0}else{t=Vl(t)}e=Xl(e);return La(e,r,t)}function Gs(e,r,t){if(t&&typeof t!="boolean"&&of(e,r,t)){r=t=i}if(t===i){if(typeof r=="boolean"){t=r;r=i}else if(typeof e=="boolean"){t=e;e=i}}if(e===i&&r===i){e=0;r=1}else{e=Vl(e);if(r===i){r=e;e=0}else{r=Vl(r)}}if(e>r){var n=e;e=r;r=n}if(t||e%1||r%1){var a=Vr();return Wr(e+a*(r-e+tt("1e-"+((a+"").length-1))),r)}return di(e,r)}var $s=so(function(e,r,t){r=r.toLowerCase();return e+(t?Vs(r):r)});function Vs(e){return yv(Kl(e).toLowerCase())}function Hs(e){e=Kl(e);return e&&e.replace(Ke,Zt).replace(qr,"")}function Ys(e,r,t){e=Kl(e);r=Mi(r);var n=e.length;t=t===i?n:ha(Hl(t),0,n);var a=t;t-=r.length;return t>=0&&e.slice(t,a)==r}function Xs(e){e=Kl(e);return e&&je.test(e)?e.replace(Se,en):e}function Qs(e){e=Kl(e);return e&&Fe.test(e)?e.replace(Pe,"\\$&"):e}var Js=so(function(e,r,t){return e+(t?"-":"")+r.toLowerCase()});var Ks=so(function(e,r,t){return e+(t?" ":"")+r.toLowerCase()});var Zs=lo("toLowerCase");function ev(e,r,t){e=Kl(e);r=Hl(r);var n=r?hn(e):0;if(!r||n>=r){return e}var a=(r-n)/2;return xo(Cr(a),t)+e+xo(Ir(a),t)}function rv(e,r,t){e=Kl(e);r=Hl(r);var n=r?hn(e):0;return r&&n<r?e+xo(r-n,t):e}function tv(e,r,t){e=Kl(e);r=Hl(r);var n=r?hn(e):0;return r&&n<r?xo(r-n,t)+e:e}function nv(e,r,t){if(t||r==null){r=0}else if(r){r=+r}return $r(Kl(e).replace(Le,""),r||0)}function av(e,r,t){if(t?of(e,r,t):r===i){r=1}else{r=Hl(r)}return gi(Kl(e),r)}function iv(){var e=arguments,r=Kl(e[0]);return e.length<3?r:r.replace(e[1],e[2])}var ov=so(function(e,r,t){return e+(t?"_":"")+r.toLowerCase()});function fv(e,r,t){if(t&&typeof t!="number"&&of(e,r,t)){r=t=i}t=t===i?R:t>>>0;if(!t){return[]}e=Kl(e);if(e&&(typeof r=="string"||r!=null&&!Cl(r))){r=Mi(r);if(!r&&nn(e)){return qi(dn(e),0,t)}}return e.split(r,t)}var uv=so(function(e,r,t){return e+(t?" ":"")+yv(r)});function cv(e,r,t){e=Kl(e);t=t==null?0:ha(Hl(t),0,e.length);r=Mi(r);return e.slice(t,t+r.length)==r}function lv(e,r,t){var n=_n.templateSettings;if(t&&of(e,r,t)){r=i}e=Kl(e);r=rs({},r,n,To);var a=rs({},r.imports,n.imports,To),o=xs(a),f=Yt(a,o);var u,c,l=0,s=r.interpolate||Ze,v="__p += '";var p=nr((r.escape||Ze).source+"|"+s.source+"|"+(s===Me?$e:Ze).source+"|"+(r.evaluate||Ze).source+"|$","g");var h="//# sourceURL="+("sourceURL"in r?r.sourceURL:"lodash.templateSources["+ ++Xr+"]")+"\n";e.replace(p,function(r,t,n,a,i,o){n||(n=a);v+=e.slice(l,o).replace(er,rn);if(t){u=true;v+="' +\n__e("+t+") +\n'"}if(i){c=true;v+="';\n"+i+";\n__p += '"}if(n){v+="' +\n((__t = ("+n+")) == null ? '' : __t) +\n'"}l=o+r.length;return r});v+="';\n";var d=r.variable;if(!d){v="with (obj) {\n"+v+"\n}\n"}v=(c?v.replace(we,""):v).replace(ke,"$1").replace(ze,"$1;");v="function("+(d||"obj")+") {\n"+(d?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(u?", __e = _.escape":"")+(c?", __j = Array.prototype.join;\n"+"function print() { __p += __j.call(arguments, '') }\n":";\n")+v+"return __p\n}";var m=xv(function(){return qe(o,h+"return "+v).apply(i,f)});m.source=v;if(bl(m)){throw m}return m}function sv(e){return Kl(e).toLowerCase()}function vv(e){return Kl(e).toUpperCase()}function pv(e,r,t){e=Kl(e);if(e&&(t||r===i)){return e.replace(Ne,"")}if(!e||!(r=Mi(r))){return e}var n=dn(e),a=dn(r),o=Qt(n,a),f=Jt(n,a)+1;return qi(n,o,f).join("")}function hv(e,r,t){e=Kl(e);if(e&&(t||r===i)){return e.replace(Re,"")}if(!e||!(r=Mi(r))){return e}var n=dn(e),a=Jt(n,dn(r))+1;return qi(n,0,a).join("")}function dv(e,r,t){e=Kl(e);if(e&&(t||r===i)){return e.replace(Le,"")}if(!e||!(r=Mi(r))){return e}var n=dn(e),a=Qt(n,dn(r));return qi(n,a).join("")}function mv(e,r){var t=j,n=O;if(kl(r)){var a="separator"in r?r.separator:a;t="length"in r?Hl(r.length):t;n="omission"in r?Mi(r.omission):n}e=Kl(e);var o=e.length;if(nn(e)){var f=dn(e);o=f.length}if(t>=o){return e}var u=t-hn(n);if(u<1){return n}var c=f?qi(f,0,u).join(""):e.slice(0,u);if(a===i){return c+n}if(f){u+=c.length-u}if(Cl(a)){if(e.slice(u).search(a)){var l,s=c;if(!a.global){a=nr(a.source,Kl(Ve.exec(a))+"g")}a.lastIndex=0;while(l=a.exec(s)){var v=l.index}c=c.slice(0,v===i?u:v)}}else if(e.indexOf(Mi(a),u)!=u){var p=c.lastIndexOf(a);if(p>-1){c=c.slice(0,p)}}return c+n}function gv(e){e=Kl(e);return e&&Ae.test(e)?e.replace(Ee,mn):e}var bv=so(function(e,r,t){return e+(t?" ":"")+r.toUpperCase()});var yv=lo("toUpperCase");function _v(e,r,t){e=Kl(e);r=t?i:r;if(r===i){return an(e)?yn(e):It(e)}return e.match(r)||[]}var xv=bi(function(e,r){try{return bt(e,i,r)}catch(t){return bl(t)?t:new a(t)}});var wv=Fo(function(e,r){_t(r,function(r){r=Tf(r);va(e,r,Mc(e[r],e))});return e});function kv(e){var r=e==null?0:e.length,t=Uo();e=!r?[]:St(e,function(e){if(typeof e[1]!="function"){throw new ir(c)}return[t(e[0]),e[1]]});return bi(function(t){var n=-1;while(++n<r){var a=e[n];if(bt(a[0],this,t)){return bt(a[1],this,t)}}})}function zv(e){return ma(da(e,p))}function Ev(e){return function(){return e}}function Sv(e,r){return e==null||e!==e?r:e}var Av=mo();var jv=mo(true);function Ov(e){return e}function Tv(e){return Za(typeof e=="function"?e:da(e,p))}function Mv(e){return ai(da(e,p))}function Dv(e,r){return ii(e,da(r,p))}var Iv=bi(function(e,r){return function(t){return Wa(t,e,r)}});var Cv=bi(function(e,r){return function(t){return Wa(e,t,r)}});function Pv(e,r,t){var n=xs(r),a=Ma(r,n);if(t==null&&!(kl(r)&&(a.length||!n.length))){t=r;r=e;e=this;a=Ma(r,xs(r))}var i=!(kl(t)&&"chain"in t)||!!t.chain,o=_l(e);_t(a,function(t){var n=r[t];e[t]=n;if(o){e.prototype[t]=function(){var r=this.__chain__;if(i||r){var t=e(this.__wrapped__),a=t.__actions__=ro(this.__actions__);a.push({func:n,args:arguments,thisArg:e});t.__chain__=r;return t}return n.apply(e,At([this.value()],arguments))}}});return e}function Fv(){if(ot._===this){ot._=mr}return this}function Nv(){}function Lv(e){e=Hl(e);return bi(function(r){return ui(r,e)})}var Rv=_o(St);var Bv=_o(wt);var Wv=_o(Tt);function Uv(e){return ff(e)?Bt(Tf(e)):vi(e)}function qv(e){return function(r){return e==null?i:Da(e,r)}}var Gv=ko();var $v=ko(true);function Vv(){return[]}function Hv(){return false}function Yv(){return{}}function Xv(){return""}function Qv(){return true}function Jv(e,r){e=Hl(e);if(e<1||e>F){return[]}var t=R,n=Wr(e,R);r=Uo(r);e-=R;var a=$t(n,r);while(++t<e){r(t)}return a}function Kv(e){if(fl(e)){return St(e,Tf)}return Ll(e)?[e]:ro(Of(Kl(e)))}function Zv(e){var r=++vr;return Kl(e)+r}var ep=yo(function(e,r){return e+r},0);var rp=So("ceil");var tp=yo(function(e,r){return e/r},1);var np=So("floor");function ap(e){return e&&e.length?ka(e,Ov,Pa):i}function ip(e,r){return e&&e.length?ka(e,Uo(r,2),Pa):i}function op(e){return Rt(e,Ov)}function fp(e,r){return Rt(e,Uo(r,2))}function up(e){return e&&e.length?ka(e,Ov,ti):i}function cp(e,r){return e&&e.length?ka(e,Uo(r,2),ti):i}var lp=yo(function(e,r){return e*r},1);var sp=So("round");var vp=yo(function(e,r){return e-r},0);function pp(e){return e&&e.length?Gt(e,Ov):0}function hp(e,r){return e&&e.length?Gt(e,Uo(r,2)):0}_n.after=jc;_n.ary=Oc;_n.assign=Zl;_n.assignIn=es;_n.assignInWith=rs;_n.assignWith=ts;_n.at=ns;_n.before=Tc;_n.bind=Mc;_n.bindAll=wv;_n.bindKey=Dc;_n.castArray=Jc;_n.chain=Gu;_n.chunk=Cf;_n.compact=Pf;_n.concat=Ff;_n.cond=kv;_n.conforms=zv;_n.constant=Ev;_n.countBy=rc;_n.create=as;_n.curry=Ic;_n.curryRight=Cc;_n.debounce=Pc;_n.defaults=is;_n.defaultsDeep=os;_n.defer=Fc;_n.delay=Nc;_n.difference=Nf;_n.differenceBy=Lf;_n.differenceWith=Rf;_n.drop=Bf;_n.dropRight=Wf;_n.dropRightWhile=Uf;_n.dropWhile=qf;_n.fill=Gf;_n.filter=nc;_n.flatMap=oc;_n.flatMapDeep=fc;_n.flatMapDepth=uc;_n.flatten=Hf;_n.flattenDeep=Yf;_n.flattenDepth=Xf;_n.flip=Lc;_n.flow=Av;_n.flowRight=jv;_n.fromPairs=Qf;_n.functions=ps;_n.functionsIn=hs;_n.groupBy=sc;_n.initial=Zf;_n.intersection=eu;_n.intersectionBy=ru;_n.intersectionWith=tu;_n.invert=bs;_n.invertBy=ys;_n.invokeMap=pc;_n.iteratee=Tv;_n.keyBy=hc;_n.keys=xs;_n.keysIn=ws;_n.map=dc;_n.mapKeys=ks;_n.mapValues=zs;_n.matches=Mv;_n.matchesProperty=Dv;_n.memoize=Rc;_n.merge=Es;_n.mergeWith=Ss;_n.method=Iv;_n.methodOf=Cv;_n.mixin=Pv;_n.negate=Bc;_n.nthArg=Lv;_n.omit=As;_n.omitBy=js;_n.once=Wc;_n.orderBy=mc;_n.over=Rv;_n.overArgs=Uc;_n.overEvery=Bv;_n.overSome=Wv;_n.partial=qc;_n.partialRight=Gc;_n.partition=gc;_n.pick=Os;_n.pickBy=Ts;_n.property=Uv;_n.propertyOf=qv;_n.pull=fu;_n.pullAll=uu;_n.pullAllBy=cu;_n.pullAllWith=lu;_n.pullAt=su;_n.range=Gv;_n.rangeRight=$v;_n.rearg=$c;_n.reject=_c;_n.remove=vu;_n.rest=Vc;_n.reverse=pu;_n.sampleSize=wc;_n.set=Ds;_n.setWith=Is;_n.shuffle=kc;_n.slice=hu;_n.sortBy=Sc;_n.sortedUniq=xu;_n.sortedUniqBy=wu;_n.split=fv;_n.spread=Hc;_n.tail=ku;_n.take=zu;_n.takeRight=Eu;_n.takeRightWhile=Su;_n.takeWhile=Au;_n.tap=$u;_n.throttle=Yc;_n.thru=Vu;_n.toArray=$l;_n.toPairs=Cs;_n.toPairsIn=Ps;_n.toPath=Kv;_n.toPlainObject=Ql;_n.transform=Fs;_n.unary=Xc;_n.union=ju;_n.unionBy=Ou;_n.unionWith=Tu;_n.uniq=Mu;_n.uniqBy=Du;_n.uniqWith=Iu;_n.unset=Ns;_n.unzip=Cu;_n.unzipWith=Pu;_n.update=Ls;_n.updateWith=Rs;_n.values=Bs;_n.valuesIn=Ws;_n.without=Fu;_n.words=_v;_n.wrap=Qc;_n.xor=Nu;_n.xorBy=Lu;_n.xorWith=Ru;_n.zip=Bu;_n.zipObject=Wu;_n.zipObjectDeep=Uu;_n.zipWith=qu;_n.entries=Cs;_n.entriesIn=Ps;_n.extend=es;_n.extendWith=rs;Pv(_n,_n);_n.add=ep;_n.attempt=xv;_n.camelCase=$s;_n.capitalize=Vs;_n.ceil=rp;_n.clamp=Us;_n.clone=Kc;_n.cloneDeep=el;_n.cloneDeepWith=rl;_n.cloneWith=Zc;_n.conformsTo=tl;_n.deburr=Hs;_n.defaultTo=Sv;_n.divide=tp;_n.endsWith=Ys;_n.eq=nl;_n.escape=Xs;_n.escapeRegExp=Qs;_n.every=tc;_n.find=ac;_n.findIndex=$f;_n.findKey=fs;_n.findLast=ic;_n.findLastIndex=Vf;_n.findLastKey=us;_n.floor=np;_n.forEach=cc;_n.forEachRight=lc;_n.forIn=cs;_n.forInRight=ls;_n.forOwn=ss;_n.forOwnRight=vs;_n.get=ds;_n.gt=al;_n.gte=il;_n.has=ms;_n.hasIn=gs;_n.head=Jf;_n.identity=Ov;_n.includes=vc;_n.indexOf=Kf;_n.inRange=qs;_n.invoke=_s;_n.isArguments=ol;_n.isArray=fl;_n.isArrayBuffer=ul;_n.isArrayLike=cl;_n.isArrayLikeObject=ll;_n.isBoolean=sl;_n.isBuffer=vl;_n.isDate=pl;_n.isElement=hl;_n.isEmpty=dl;_n.isEqual=ml;_n.isEqualWith=gl;_n.isError=bl;_n.isFinite=yl;_n.isFunction=_l;_n.isInteger=xl;_n.isLength=wl;_n.isMap=El;_n.isMatch=Sl;_n.isMatchWith=Al;_n.isNaN=jl;_n.isNative=Ol;_n.isNil=Ml;_n.isNull=Tl;_n.isNumber=Dl;_n.isObject=kl;_n.isObjectLike=zl;_n.isPlainObject=Il;_n.isRegExp=Cl;_n.isSafeInteger=Pl;_n.isSet=Fl;_n.isString=Nl;_n.isSymbol=Ll;_n.isTypedArray=Rl;_n.isUndefined=Bl;_n.isWeakMap=Wl;_n.isWeakSet=Ul;_n.join=nu;_n.kebabCase=Js;_n.last=au;_n.lastIndexOf=iu;_n.lowerCase=Ks;_n.lowerFirst=Zs;_n.lt=ql;_n.lte=Gl;_n.max=ap;_n.maxBy=ip;_n.mean=op;_n.meanBy=fp;_n.min=up;_n.minBy=cp;_n.stubArray=Vv;_n.stubFalse=Hv;_n.stubObject=Yv;_n.stubString=Xv;_n.stubTrue=Qv;_n.multiply=lp;_n.nth=ou;_n.noConflict=Fv;_n.noop=Nv;_n.now=Ac;_n.pad=ev;_n.padEnd=rv;_n.padStart=tv;_n.parseInt=nv;_n.random=Gs;_n.reduce=bc;_n.reduceRight=yc;_n.repeat=av;_n.replace=iv;_n.result=Ms;_n.round=sp;_n.runInContext=e;_n.sample=xc;_n.size=zc;_n.snakeCase=ov;_n.some=Ec;_n.sortedIndex=du;_n.sortedIndexBy=mu;_n.sortedIndexOf=gu;_n.sortedLastIndex=bu;_n.sortedLastIndexBy=yu;_n.sortedLastIndexOf=_u;_n.startCase=uv;_n.startsWith=cv;_n.subtract=vp;_n.sum=pp;_n.sumBy=hp;_n.template=lv;_n.times=Jv;_n.toFinite=Vl;_n.toInteger=Hl;_n.toLength=Yl;_n.toLower=sv;_n.toNumber=Xl;_n.toSafeInteger=Jl;_n.toString=Kl;_n.toUpper=vv;_n.trim=pv;_n.trimEnd=hv;_n.trimStart=dv;_n.truncate=mv;_n.unescape=gv;_n.uniqueId=Zv;_n.upperCase=bv;_n.upperFirst=yv;_n.each=cc;_n.eachRight=lc;_n.first=Jf;Pv(_n,function(){var e={};Oa(_n,function(r,t){if(!sr.call(_n.prototype,t)){e[t]=r}});return e}(),{chain:false});_n.VERSION=o;_t(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){_n[e].placeholder=_n});_t(["drop","take"],function(e,r){En.prototype[e]=function(t){t=t===i?1:Br(Hl(t),0);var n=this.__filtered__&&!r?new En(this):this.clone();if(n.__filtered__){n.__takeCount__=Wr(t,n.__takeCount__)}else{n.__views__.push({size:Wr(t,R),type:e+(n.__dir__<0?"Right":"")})}return n};En.prototype[e+"Right"]=function(r){return this.reverse()[e](r).reverse()}});_t(["filter","map","takeWhile"],function(e,r){var t=r+1,n=t==D||t==C;En.prototype[e]=function(e){var r=this.clone();r.__iteratees__.push({iteratee:Uo(e,3),type:t});r.__filtered__=r.__filtered__||n;return r}});_t(["head","last"],function(e,r){var t="take"+(r?"Right":"");En.prototype[e]=function(){return this[t](1).value()[0]}});_t(["initial","tail"],function(e,r){var t="drop"+(r?"":"Right");En.prototype[e]=function(){return this.__filtered__?new En(this):this[t](1)}});En.prototype.compact=function(){return this.filter(Ov)};En.prototype.find=function(e){return this.filter(e).head()};En.prototype.findLast=function(e){return this.reverse().find(e)};En.prototype.invokeMap=bi(function(e,r){if(typeof e=="function"){return new En(this)}return this.map(function(t){return Wa(t,e,r)})});En.prototype.reject=function(e){return this.filter(Bc(Uo(e)))};En.prototype.slice=function(e,r){e=Hl(e);var t=this;if(t.__filtered__&&(e>0||r<0)){return new En(t)}if(e<0){t=t.takeRight(-e)}else if(e){t=t.drop(e)}if(r!==i){r=Hl(r);t=r<0?t.dropRight(-r):t.take(r-e)}return t};En.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()};En.prototype.toArray=function(){return this.take(R)};Oa(En.prototype,function(e,r){var t=/^(?:filter|find|map|reject)|While$/.test(r),n=/^(?:head|last)$/.test(r),a=_n[n?"take"+(r=="last"?"Right":""):r],o=n||/^find/.test(r);if(!a){return}_n.prototype[r]=function(){var r=this.__wrapped__,f=n?[1]:arguments,u=r instanceof En,c=f[0],l=u||fl(r);var s=function(e){var r=a.apply(_n,At([e],f));return n&&v?r[0]:r};if(l&&t&&typeof c=="function"&&c.length!=1){u=l=false}var v=this.__chain__,p=!!this.__actions__.length,h=o&&!v,d=u&&!p;if(!o&&l){r=d?r:new En(this);var m=e.apply(r,f);m.__actions__.push({func:Vu,args:[s],thisArg:i});return new zn(m,v)}if(h&&d){return e.apply(this,f)}m=this.thru(s);return h?n?m.value()[0]:m.value():m}});_t(["pop","push","shift","sort","splice","unshift"],function(e){var r=or[e],t=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",n=/^(?:pop|shift)$/.test(e);_n.prototype[e]=function(){var e=arguments;if(n&&!this.__chain__){var a=this.value();return r.apply(fl(a)?a:[],e)}return this[t](function(t){return r.apply(fl(t)?t:[],e)})}});Oa(En.prototype,function(e,r){var t=_n[r];if(t){var n=t.name+"",a=ut[n]||(ut[n]=[]);a.push({name:r,func:t})}});ut[go(i,y).name]=[{name:"wrapper",func:i}];En.prototype.clone=Sn;En.prototype.reverse=An;En.prototype.value=jn;_n.prototype.at=Hu;_n.prototype.chain=Yu;_n.prototype.commit=Xu;_n.prototype.next=Qu;_n.prototype.plant=Ku;_n.prototype.reverse=Zu;_n.prototype.toJSON=_n.prototype.valueOf=_n.prototype.value=ec;_n.prototype.first=_n.prototype.head;if(Ar){_n.prototype[Ar]=Ju}return _n};var xn=_n();if(true){ot._=xn;!(a=function(){return xn}.call(r,t,r,n),a!==i&&(n.exports=a))}else{}}).call(this)}).call(this,t("c8ba"),t("62e4")(e))},a2ce:function(e,r){var t={}.toString;e.exports=function(e){return t.call(e).slice(8,-1)}},a654:function(e,r,t){e.exports=t("0b70")},a695:function(e,r,t){"use strict";var n=t("8b78");var a=t("22fe");var i=t("a85c");var o=t("1f03");var f=t("781d");var u=t("533a");var c=t("fe4e");var l=t("b244");var s=t("0536")("iterator");var v=!([].keys&&"next"in[].keys());var p="@@iterator";var h="keys";var d="values";var m=function(){return this};e.exports=function(e,r,t,g,b,y,_){u(t,r,g);var x=function(e){if(!v&&e in E)return E[e];switch(e){case h:return function r(){return new t(this,e)};case d:return function r(){return new t(this,e)}}return function r(){return new t(this,e)}};var w=r+" Iterator";var k=b==d;var z=false;var E=e.prototype;var S=E[s]||E[p]||b&&E[b];var A=S||x(b);var j=b?!k?A:x("entries"):undefined;var O=r=="Array"?E.entries||S:S;var T,M,D;if(O){D=l(O.call(new e));if(D!==Object.prototype&&D.next){c(D,w,true);if(!n&&typeof D[s]!="function")o(D,s,m)}}if(k&&S&&S.name!==d){z=true;A=function e(){return S.call(this)}}if((!n||_)&&(v||z||!E[s])){o(E,s,A)}f[r]=A;f[w]=m;if(b){T={values:k?A:x(d),keys:y?A:x(h),entries:j};if(_)for(M in T){if(!(M in E))i(E,M,T[M])}else a(a.P+a.F*(v||z),r,T)}return T}},a85c:function(e,r,t){var n=t("f861");var a=t("1f03");var i=t("d8a8");var o=t("4509")("src");var f="toString";var u=Function[f];var c=(""+u).split(f);t("e4e6").inspectSource=function(e){return u.call(e)};(e.exports=function(e,r,t,f){var u=typeof t=="function";if(u)i(t,"name")||a(t,"name",r);if(e[r]===t)return;if(u)i(t,o)||a(t,o,e[r]?""+e[r]:c.join(String(r)));if(e===n){e[r]=t}else if(!f){delete e[r];a(e,r,t)}else if(e[r]){e[r]=t}else{a(e,r,t)}})(Function.prototype,f,function e(){return typeof this=="function"&&this[o]||u.call(this)})},a911:function(e,r,t){var n=t("f7b2");e.exports=function(e){return Object(n(e))}},a9f6:function(e,r,t){var n=t("46ab");var a=t("ba1d").f;var i={}.toString;var o=typeof window=="object"&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];var f=function(e){try{return a(e)}catch(r){return o.slice()}};e.exports.f=function e(r){return o&&i.call(r)=="[object Window]"?f(r):a(n(r))}},ad4b:function(e,r){e.exports="\t\n\v\f\r   ᠎    "+"         　\u2028\u2029\ufeff"},afb7:function(e,r,t){"use strict";var n=t("a911");var a=t("d744");var i=t("d7d0");e.exports=function e(r){var t=n(this);var o=i(t.length);var f=arguments.length;var u=a(f>1?arguments[1]:undefined,o);var c=f>2?arguments[2]:undefined;var l=c===undefined?o:a(c,o);while(l>u)t[u++]=r;return t}},b1cb:function(e,r,t){var n=t("781d");var a=t("0536")("iterator");var i=Array.prototype;e.exports=function(e){return e!==undefined&&(n.Array===e||i[a]===e)}},b23e:function(e,r){e.exports=t;function t(e,r,t){var n=Math.cos(t),a=Math.sin(t);var i=r[0],o=r[1];e[0]=i*n-o*a;e[1]=i*a+o*n;return e}},b244:function(e,r,t){var n=t("d8a8");var a=t("a911");var i=t("7f4f")("IE_PROTO");var o=Object.prototype;e.exports=Object.getPrototypeOf||function(e){e=a(e);if(n(e,i))return e[i];if(typeof e.constructor=="function"&&e instanceof e.constructor){return e.constructor.prototype}return e instanceof Object?o:null}},b44a:function(e,r,t){if(t("e2e5")&&/./g.flags!="g")t("98ab").f(RegExp.prototype,"flags",{configurable:true,get:t("45a9")})},b544:function(e,r,t){"use strict";var n=t("f861");var a=t("d8a8");var i=t("e2e5");var o=t("22fe");var f=t("a85c");var u=t("404a").KEY;var c=t("c0f6");var l=t("2679");var s=t("fe4e");var v=t("4509");var p=t("0536");var h=t("d118");var d=t("fd04");var m=t("5c84");var g=t("760f");var b=t("3955");var y=t("2b84");var _=t("46ab");var x=t("1008");var w=t("5a3a");var k=t("84c3");var z=t("a9f6");var E=t("dc2d");var S=t("98ab");var A=t("3546");var j=E.f;var O=S.f;var T=z.f;var M=n.Symbol;var D=n.JSON;var I=D&&D.stringify;var C="prototype";var P=p("_hidden");var F=p("toPrimitive");var N={}.propertyIsEnumerable;var L=l("symbol-registry");var R=l("symbols");var B=l("op-symbols");var W=Object[C];var U=typeof M=="function";var q=n.QObject;var G=!q||!q[C]||!q[C].findChild;var $=i&&c(function(){return k(O({},"a",{get:function(){return O(this,"a",{value:7}).a}})).a!=7})?function(e,r,t){var n=j(W,r);if(n)delete W[r];O(e,r,t);if(n&&e!==W)O(W,r,n)}:O;var V=function(e){var r=R[e]=k(M[C]);r._k=e;return r};var H=U&&typeof M.iterator=="symbol"?function(e){return typeof e=="symbol"}:function(e){return e instanceof M};var Y=function e(r,t,n){if(r===W)Y(B,t,n);b(r);t=x(t,true);b(n);if(a(R,t)){if(!n.enumerable){if(!a(r,P))O(r,P,w(1,{}));r[P][t]=true}else{if(a(r,P)&&r[P][t])r[P][t]=false;n=k(n,{enumerable:w(0,false)})}return $(r,t,n)}return O(r,t,n)};var X=function e(r,t){b(r);var n=m(t=_(t));var a=0;var i=n.length;var o;while(i>a)Y(r,o=n[a++],t[o]);return r};var Q=function e(r,t){return t===undefined?k(r):X(k(r),t)};var J=function e(r){var t=N.call(this,r=x(r,true));if(this===W&&a(R,r)&&!a(B,r))return false;return t||!a(this,r)||!a(R,r)||a(this,P)&&this[P][r]?t:true};var K=function e(r,t){r=_(r);t=x(t,true);if(r===W&&a(R,t)&&!a(B,t))return;var n=j(r,t);if(n&&a(R,t)&&!(a(r,P)&&r[P][t]))n.enumerable=true;return n};var Z=function e(r){var t=T(_(r));var n=[];var i=0;var o;while(t.length>i){if(!a(R,o=t[i++])&&o!=P&&o!=u)n.push(o)}return n};var ee=function e(r){var t=r===W;var n=T(t?B:_(r));var i=[];var o=0;var f;while(n.length>o){if(a(R,f=n[o++])&&(t?a(W,f):true))i.push(R[f])}return i};if(!U){M=function e(){if(this instanceof M)throw TypeError("Symbol is not a constructor!");var r=v(arguments.length>0?arguments[0]:undefined);var t=function(e){if(this===W)t.call(B,e);if(a(this,P)&&a(this[P],r))this[P][r]=false;$(this,r,w(1,e))};if(i&&G)$(W,r,{configurable:true,set:t});return V(r)};f(M[C],"toString",function e(){return this._k});E.f=K;S.f=Y;t("ba1d").f=z.f=Z;t("5159").f=J;t("8be2").f=ee;if(i&&!t("8b78")){f(W,"propertyIsEnumerable",J,true)}h.f=function(e){return V(p(e))}}o(o.G+o.W+o.F*!U,{Symbol:M});for(var re="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),te=0;re.length>te;)p(re[te++]);for(var ne=A(p.store),ae=0;ne.length>ae;)d(ne[ae++]);o(o.S+o.F*!U,"Symbol",{for:function(e){return a(L,e+="")?L[e]:L[e]=M(e)},keyFor:function e(r){if(!H(r))throw TypeError(r+" is not a symbol!");for(var t in L)if(L[t]===r)return t},useSetter:function(){G=true},useSimple:function(){G=false}});o(o.S+o.F*!U,"Object",{create:Q,defineProperty:Y,defineProperties:X,getOwnPropertyDescriptor:K,getOwnPropertyNames:Z,getOwnPropertySymbols:ee});D&&o(o.S+o.F*(!U||c(function(){var e=M();return I([e])!="[null]"||I({a:e})!="{}"||I(Object(e))!="{}"})),"JSON",{stringify:function e(r){var t=[r];var n=1;var a,i;while(arguments.length>n)t.push(arguments[n++]);i=a=t[1];if(!y(a)&&r===undefined||H(r))return;if(!g(a))a=function(e,r){if(typeof i=="function")r=i.call(this,e,r);if(!H(r))return r};t[1]=a;return I.apply(D,t)}});M[C][F]||t("1f03")(M[C],F,M[C].valueOf);s(M,"Symbol");s(Math,"Math",true);s(n.JSON,"JSON",true)},b6c5:function(e,r,t){"use strict";var n=t("97bb");var a=t("11da");var i="Set";e.exports=t("58a4")(i,function(e){return function r(){return e(this,arguments.length>0?arguments[0]:undefined)}},{add:function e(r){return n.def(a(this,i),r=r===0?0:r,r)}},n)},b7c3:function(e,r){e.exports=t;function t(e,r){var t=r[0],n=r[1];var a=t*t+n*n;if(a>0){a=1/Math.sqrt(a);e[0]=r[0]*a;e[1]=r[1]*a}return e}},b8f0:function(e,r,t){e.exports=t("706c")},ba1d:function(e,r,t){var n=t("7aae");var a=t("499f").concat("length","prototype");r.f=Object.getOwnPropertyNames||function e(r){return n(r,a)}},bbaa:function(e,r){e.exports=t;function t(e,r,t){e[0]=r[0]*t[0];e[1]=r[1]*t[1];return e}},bbff:function(e,r,t){var n=t("160f");var a=t("f7b2");e.exports=function(e,r,t){if(n(r))throw TypeError("String#"+t+" doesn't accept regex!");return String(a(e))}},bdb0:function(e,r,t){var n=t("22fe");var a=t("e4e6");var i=t("c0f6");e.exports=function(e,r){var t=(a.Object||{})[e]||Object[e];var o={};o[e]=r(t);n(n.S+n.F*i(function(){t(1)}),"Object",o)}},be2a:function(e,r,t){e.exports=t("c848")},bee2:function(e,r,t){var n=t("cf9e");e.exports=function(e,r){return new(n(e))(r)}},c0f6:function(e,r){e.exports=function(e){try{return!!e()}catch(r){return true}}},c135:function(e,r){function t(e){if(Array.isArray(e))return e}e.exports=t},c240:function(e,r){function t(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}e.exports=t},c246:function(e,r,t){var n=t("2b84");var a=t("3955");var i=function(e,r){a(e);if(!n(r)&&r!==null)throw TypeError(r+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,r,n){try{n=t("0e26")(Function.call,t("dc2d").f(Object.prototype,"__proto__").set,2);n(e,[]);r=!(e instanceof Array)}catch(a){r=true}return function e(t,a){i(t,a);if(r)t.__proto__=a;else n(t,a);return t}}({},false):undefined),check:i}},c249:function(e,r,t){var n=t("2b84");var a=t("c246").set;e.exports=function(e,r,t){var i=r.constructor;var o;if(i!==t&&typeof i=="function"&&(o=i.prototype)!==t.prototype&&n(o)&&a){a(e,o)}return e}},c33d:function(e,r,t){var n=t("46ab");var a=t("d7d0");var i=t("d744");e.exports=function(e){return function(r,t,o){var f=n(r);var u=a(f.length);var c=i(o,u);var l;if(e&&t!=t)while(u>c){l=f[c++];if(l!=l)return true}else for(;u>c;c++)if(e||c in f){if(f[c]===t)return e||c||0}return!e&&-1}}},c435:function(e,r){e.exports=t;function t(e,r,t,n){var a=r[0],i=r[1];e[0]=a+n*(t[0]-a);e[1]=i+n*(t[1]-i);return e}},c848:function(e,r){e.exports=t;function t(e){var r=e[0],t=e[1];return Math.sqrt(r*r+t*t)}},c853:function(e,r,t){var n=t("0536")("unscopables");var a=Array.prototype;if(a[n]==undefined)t("1f03")(a,n,{});e.exports=function(e){a[n][e]=true}},c8ba:function(e,r){var t;t=function(){return this}();try{t=t||new Function("return this")()}catch(n){if(typeof window==="object")t=window}e.exports=t},c994:function(e,r,t){(function(e,t){var n=200;var a="__lodash_hash_undefined__";var i=9007199254740991;var o="[object Arguments]",f="[object Array]",u="[object Boolean]",c="[object Date]",l="[object Error]",s="[object Function]",v="[object GeneratorFunction]",p="[object Map]",h="[object Number]",d="[object Object]",m="[object Promise]",g="[object RegExp]",b="[object Set]",y="[object String]",_="[object Symbol]",x="[object WeakMap]";var w="[object ArrayBuffer]",k="[object DataView]",z="[object Float32Array]",E="[object Float64Array]",S="[object Int8Array]",A="[object Int16Array]",j="[object Int32Array]",O="[object Uint8Array]",T="[object Uint8ClampedArray]",M="[object Uint16Array]",D="[object Uint32Array]";var I=/[\\^$.*+?()[\]{}|]/g;var C=/\w*$/;var P=/^\[object .+?Constructor\]$/;var F=/^(?:0|[1-9]\d*)$/;var N={};N[o]=N[f]=N[w]=N[k]=N[u]=N[c]=N[z]=N[E]=N[S]=N[A]=N[j]=N[p]=N[h]=N[d]=N[g]=N[b]=N[y]=N[_]=N[O]=N[T]=N[M]=N[D]=true;N[l]=N[s]=N[x]=false;var L=typeof e=="object"&&e&&e.Object===Object&&e;var R=typeof self=="object"&&self&&self.Object===Object&&self;var B=L||R||Function("return this")();var W=true&&r&&!r.nodeType&&r;var U=W&&typeof t=="object"&&t&&!t.nodeType&&t;var q=U&&U.exports===W;function G(e,r){e.set(r[0],r[1]);return e}function $(e,r){e.add(r);return e}function V(e,r){var t=-1,n=e?e.length:0;while(++t<n){if(r(e[t],t,e)===false){break}}return e}function H(e,r){var t=-1,n=r.length,a=e.length;while(++t<n){e[a+t]=r[t]}return e}function Y(e,r,t,n){var a=-1,i=e?e.length:0;if(n&&i){t=e[++a]}while(++a<i){t=r(t,e[a],a,e)}return t}function X(e,r){var t=-1,n=Array(e);while(++t<e){n[t]=r(t)}return n}function Q(e,r){return e==null?undefined:e[r]}function J(e){var r=false;if(e!=null&&typeof e.toString!="function"){try{r=!!(e+"")}catch(t){}}return r}function K(e){var r=-1,t=Array(e.size);e.forEach(function(e,n){t[++r]=[n,e]});return t}function Z(e,r){return function(t){return e(r(t))}}function ee(e){var r=-1,t=Array(e.size);e.forEach(function(e){t[++r]=e});return t}var re=Array.prototype,te=Function.prototype,ne=Object.prototype;var ae=B["__core-js_shared__"];var ie=function(){var e=/[^.]+$/.exec(ae&&ae.keys&&ae.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();var oe=te.toString;var fe=ne.hasOwnProperty;var ue=ne.toString;var ce=RegExp("^"+oe.call(fe).replace(I,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var le=q?B.Buffer:undefined,se=B.Symbol,ve=B.Uint8Array,pe=Z(Object.getPrototypeOf,Object),he=Object.create,de=ne.propertyIsEnumerable,me=re.splice;var ge=Object.getOwnPropertySymbols,be=le?le.isBuffer:undefined,ye=Z(Object.keys,Object);var _e=Sr(B,"DataView"),xe=Sr(B,"Map"),we=Sr(B,"Promise"),ke=Sr(B,"Set"),ze=Sr(B,"WeakMap"),Ee=Sr(Object,"create");var Se=Fr(_e),Ae=Fr(xe),je=Fr(we),Oe=Fr(ke),Te=Fr(ze);var Me=se?se.prototype:undefined,De=Me?Me.valueOf:undefined;function Ie(e){var r=-1,t=e?e.length:0;this.clear();while(++r<t){var n=e[r];this.set(n[0],n[1])}}function Ce(){this.__data__=Ee?Ee(null):{}}function Pe(e){return this.has(e)&&delete this.__data__[e]}function Fe(e){var r=this.__data__;if(Ee){var t=r[e];return t===a?undefined:t}return fe.call(r,e)?r[e]:undefined}function Ne(e){var r=this.__data__;return Ee?r[e]!==undefined:fe.call(r,e)}function Le(e,r){var t=this.__data__;t[e]=Ee&&r===undefined?a:r;return this}Ie.prototype.clear=Ce;Ie.prototype["delete"]=Pe;Ie.prototype.get=Fe;Ie.prototype.has=Ne;Ie.prototype.set=Le;function Re(e){var r=-1,t=e?e.length:0;this.clear();while(++r<t){var n=e[r];this.set(n[0],n[1])}}function Be(){this.__data__=[]}function We(e){var r=this.__data__,t=ir(r,e);if(t<0){return false}var n=r.length-1;if(t==n){r.pop()}else{me.call(r,t,1)}return true}function Ue(e){var r=this.__data__,t=ir(r,e);return t<0?undefined:r[t][1]}function qe(e){return ir(this.__data__,e)>-1}function Ge(e,r){var t=this.__data__,n=ir(t,e);if(n<0){t.push([e,r])}else{t[n][1]=r}return this}Re.prototype.clear=Be;Re.prototype["delete"]=We;Re.prototype.get=Ue;Re.prototype.has=qe;Re.prototype.set=Ge;function $e(e){var r=-1,t=e?e.length:0;this.clear();while(++r<t){var n=e[r];this.set(n[0],n[1])}}function Ve(){this.__data__={hash:new Ie,map:new(xe||Re),string:new Ie}}function He(e){return Er(this,e)["delete"](e)}function Ye(e){return Er(this,e).get(e)}function Xe(e){return Er(this,e).has(e)}function Qe(e,r){Er(this,e).set(e,r);return this}$e.prototype.clear=Ve;$e.prototype["delete"]=He;$e.prototype.get=Ye;$e.prototype.has=Xe;$e.prototype.set=Qe;function Je(e){this.__data__=new Re(e)}function Ke(){this.__data__=new Re}function Ze(e){return this.__data__["delete"](e)}function er(e){return this.__data__.get(e)}function rr(e){return this.__data__.has(e)}function tr(e,r){var t=this.__data__;if(t instanceof Re){var a=t.__data__;if(!xe||a.length<n-1){a.push([e,r]);return this}t=this.__data__=new $e(a)}t.set(e,r);return this}Je.prototype.clear=Ke;Je.prototype["delete"]=Ze;Je.prototype.get=er;Je.prototype.has=rr;Je.prototype.set=tr;function nr(e,r){var t=Br(e)||Rr(e)?X(e.length,String):[];var n=t.length,a=!!n;for(var i in e){if((r||fe.call(e,i))&&!(a&&(i=="length"||Dr(i,n)))){t.push(i)}}return t}function ar(e,r,t){var n=e[r];if(!(fe.call(e,r)&&Lr(n,t))||t===undefined&&!(r in e)){e[r]=t}}function ir(e,r){var t=e.length;while(t--){if(Lr(e[t][0],r)){return t}}return-1}function or(e,r){return e&&wr(r,Yr(r),e)}function fr(e,r,t,n,a,i,f){var u;if(n){u=i?n(e,a,i,f):n(e)}if(u!==undefined){return u}if(!Vr(e)){return e}var c=Br(e);if(c){u=Or(e);if(!r){return xr(e,u)}}else{var l=jr(e),p=l==s||l==v;if(qr(e)){return pr(e,r)}if(l==d||l==o||p&&!i){if(J(e)){return i?e:{}}u=Tr(p?{}:e);if(!r){return kr(e,or(u,e))}}else{if(!N[l]){return i?e:{}}u=Mr(e,l,fr,r)}}f||(f=new Je);var h=f.get(e);if(h){return h}f.set(e,u);if(!c){var m=t?zr(e):Yr(e)}V(m||e,function(a,i){if(m){i=a;a=e[i]}ar(u,i,fr(a,r,t,n,i,e,f))});return u}function ur(e){return Vr(e)?he(e):{}}function cr(e,r,t){var n=r(e);return Br(e)?n:H(n,t(e))}function lr(e){return ue.call(e)}function sr(e){if(!Vr(e)||Cr(e)){return false}var r=Gr(e)||J(e)?ce:P;return r.test(Fr(e))}function vr(e){if(!Pr(e)){return ye(e)}var r=[];for(var t in Object(e)){if(fe.call(e,t)&&t!="constructor"){r.push(t)}}return r}function pr(e,r){if(r){return e.slice()}var t=new e.constructor(e.length);e.copy(t);return t}function hr(e){var r=new e.constructor(e.byteLength);new ve(r).set(new ve(e));return r}function dr(e,r){var t=r?hr(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.byteLength)}function mr(e,r,t){var n=r?t(K(e),true):K(e);return Y(n,G,new e.constructor)}function gr(e){var r=new e.constructor(e.source,C.exec(e));r.lastIndex=e.lastIndex;return r}function br(e,r,t){var n=r?t(ee(e),true):ee(e);return Y(n,$,new e.constructor)}function yr(e){return De?Object(De.call(e)):{}}function _r(e,r){var t=r?hr(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)}function xr(e,r){var t=-1,n=e.length;r||(r=Array(n));while(++t<n){r[t]=e[t]}return r}function wr(e,r,t,n){t||(t={});var a=-1,i=r.length;while(++a<i){var o=r[a];var f=n?n(t[o],e[o],o,t,e):undefined;ar(t,o,f===undefined?e[o]:f)}return t}function kr(e,r){return wr(e,Ar(e),r)}function zr(e){return cr(e,Yr,Ar)}function Er(e,r){var t=e.__data__;return Ir(r)?t[typeof r=="string"?"string":"hash"]:t.map}function Sr(e,r){var t=Q(e,r);return sr(t)?t:undefined}var Ar=ge?Z(ge,Object):Xr;var jr=lr;if(_e&&jr(new _e(new ArrayBuffer(1)))!=k||xe&&jr(new xe)!=p||we&&jr(we.resolve())!=m||ke&&jr(new ke)!=b||ze&&jr(new ze)!=x){jr=function(e){var r=ue.call(e),t=r==d?e.constructor:undefined,n=t?Fr(t):undefined;if(n){switch(n){case Se:return k;case Ae:return p;case je:return m;case Oe:return b;case Te:return x}}return r}}function Or(e){var r=e.length,t=e.constructor(r);if(r&&typeof e[0]=="string"&&fe.call(e,"index")){t.index=e.index;t.input=e.input}return t}function Tr(e){return typeof e.constructor=="function"&&!Pr(e)?ur(pe(e)):{}}function Mr(e,r,t,n){var a=e.constructor;switch(r){case w:return hr(e);case u:case c:return new a(+e);case k:return dr(e,n);case z:case E:case S:case A:case j:case O:case T:case M:case D:return _r(e,n);case p:return mr(e,n,t);case h:case y:return new a(e);case g:return gr(e);case b:return br(e,n,t);case _:return yr(e)}}function Dr(e,r){r=r==null?i:r;return!!r&&(typeof e=="number"||F.test(e))&&(e>-1&&e%1==0&&e<r)}function Ir(e){var r=typeof e;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?e!=="__proto__":e===null}function Cr(e){return!!ie&&ie in e}function Pr(e){var r=e&&e.constructor,t=typeof r=="function"&&r.prototype||ne;return e===t}function Fr(e){if(e!=null){try{return oe.call(e)}catch(r){}try{return e+""}catch(r){}}return""}function Nr(e){return fr(e,true,true)}function Lr(e,r){return e===r||e!==e&&r!==r}function Rr(e){return Ur(e)&&fe.call(e,"callee")&&(!de.call(e,"callee")||ue.call(e)==o)}var Br=Array.isArray;function Wr(e){return e!=null&&$r(e.length)&&!Gr(e)}function Ur(e){return Hr(e)&&Wr(e)}var qr=be||Qr;function Gr(e){var r=Vr(e)?ue.call(e):"";return r==s||r==v}function $r(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=i}function Vr(e){var r=typeof e;return!!e&&(r=="object"||r=="function")}function Hr(e){return!!e&&typeof e=="object"}function Yr(e){return Wr(e)?nr(e):vr(e)}function Xr(){return[]}function Qr(){return false}t.exports=Nr}).call(this,t("c8ba"),t("62e4")(e))},ccbc:function(e,r){e.exports=t;function t(e,r){e[0]=Math.ceil(r[0]);e[1]=Math.ceil(r[1]);return e}},ce62:function(e,r,t){"use strict";t.r(r);var n=t("448a");var a=t.n(n);var i=t("7813");var o=t("d6b6");var f=t("e3f9");var u=t("9b8e");var c=t("5a51");var l=t("fb2b");var s=t("233f");var v=t("7621");var p=t("f994");var h=t("0de4");var d=t("7341");var m=t("1adf");var g=t("1a9d");var b=t("5e86");var y=t("b544");var _=t("359c");var x=t("3156");var w=t.n(x);var k=t("9af0");var z=t("0283");var E=t("278c");var S=t.n(E);var A=t("e1c2");var j=t("50e2");var O=t("2c89");var T=t("3ef8");var M=t("1df5");var D=t("3b5a");function I(e,r){var t=e.mesh[r].parameters.size_x;var n={variable:"width",label:"Width",default:1500,min:t[0],max:t[1]};var a=e.mesh[r].parameters.size_y;var i={variable:"height",label:"Height",default:300,min:a[0],max:a[a.length-1],steps:a};var o={variable:"depth",label:"Depths",default:320,options:[{value:320,label:"32cm"},{value:400,label:"40cm"}]};var f={variable:"plinth",label:"Plinth",default:true,options:[{value:false,label:"Plinth"},{value:true,label:"Feet"}]};var u={widthSlider:n,heightSlider:i,depthToogle:o,plinthToogle:f};inputData["uiConfigParams"]=u;return u}function C(e){if(e.components){e.components.forEach(function(e){e["uiLocalConfigParams"]={flipDoorToggle:{available:false,state:null,variable:"door_flip",label:"Doors direction",options:[{value:"left",label:"Left"},{value:"right",label:"Right"}]},cablesToggle:{available:false,state:false,variable:"cables",label:"Cable opening",options:[{value:false,label:"Off"},{value:true,label:"On"}]}};if(e.door_flip){e.uiLocalConfigParams.flipDoorToggle.available=true;e.uiLocalConfigParams.flipDoorToggle.state=e.door_flip}if(e.cables_available){e.uiLocalConfigParams.cablesToggle.available=true;e.uiLocalConfigParams.cablesToggle.state=e.cables}})}}function P(e,r,t){var n={};if(r==="setup_range_stepper"||t==="edge"){if(t==="edge"&&e.lines){var a={};Object.keys(e.lines).forEach(function(r){a[r]={variable:"configurator_custom_params.lines",label:"Local Edge",state:e.lines[r].value,min:e.lines[r].v_min,max:e.lines[r].v_max,x:e.lines[r].x,y:e.lines[r].y}});n["localEdgeDistortion"]=a}if(r==="setup_range_stepper"&&e.density_options&&e.density_options.length>0){var i={variable:"density",label:"Columns",options:[]};e.density_options.forEach(function(e){i.options.push({value:e[0],label:e[1]})});n["densityStepper"]=i}}else n=null;e["uiRelativeConfigParams"]=n}var F=18;var N=18;var L=125;var R="B";var B="Fr";var W="C";var U="D";var q="Df";var G="Ds";var $="T";var V="H";var H="Ih";var Y="Iv";var X="Eh";var Q="Ev";var J="Eb";var K="L";var Z="M";var ee="O";var re="P";var te="X";var ne="S";var ae="V";var ie="TB";var oe="FB";var fe="Si";var ue="So";var ce="Sr";var le="Sl";var se={verticals:ae,horizontals:V,backs:R,fronts:B,components:W,doors:U,doors_front:q,sliders:G,drawers:$,legs:K,mark:Z,opening:ee,plinth:re,spacers:te,supports:ne,inserts_horizontal:H,inserts_vertical:Y,erasers_horizontal:X,erasers_vertical:Q,erasers_back:J,shadows_inner:fe,shadows_outer:ue,shadows_right:ce,shadows_left:le};var ve=Object.keys(se).reduce(function(e,r){e[se[r]]=r;return e},{});var pe=function e(r){return typeof r==="string"||r instanceof String};var he=function e(r,t){var n=Array(10).fill([2,0,0,2,1,1]);if(t===null)return n;var a=[];t.reduce(function(e,r,t){return a[t]=e+r},9);a.unshift(9);var i=a.map(function(e){return r.doors.filter(function(r,t,n){return r["y1"]===e})});var o=a.map(function(e){return r.drawers.filter(function(r,t,n){return r["y1"]===e})});var f=a.map(function(e){return r.shadows_inner.filter(function(r,t,n){return r["y1"]===e})});a.forEach(function(e,r){var t=i[r].length;var a=o[r].length;var u=f[r].length;n[r]=[t===0?2:1,u===1?0:t!==0&&u!==t?2:1,u===t?2:1,a===0?2:1,u===1?0:a!==0&&u!==a?2:1,u===a?2:1]});return n};var de=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;var a={setup_id:null,config_slots:{}};if(r.components)r.components.forEach(function(e){a.setup_id=e.m_setup_id||a.setup_id;a.config_slots[e.m_config_id||0]={table_id:e.table_id||null,series_id:e.series_id||null,door_flip:e.door_flip||null,order:e.order||0}});return a};var me=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;var a={};r.components.reduce(function(e,i){if(e){var o=[e.channel_id,i.channel_id].join("_");var f=e.x2+t;var u=e.line_right||0;var c=Math.min(i.x2-i.x1+u-i.w_min,e.w_max-(e.x2-e.x1-u));var l=-Math.min(e.x2-e.x1-u-e.w_min,i.w_max-(i.x2-i.x1+u));a[o]={x:f,value:u,v_min:l,v_max:c,y:r.height/2+n}}return i});return a};var ge=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var n="";if(r.hasOwnProperty("linesX")&&r.hasOwnProperty("linesY")&&t){n+=r.linesX.reduce(function(e,r){return e+" "+r},"X")+"\n";n+=r.linesY.reduce(function(e,r){return e+" "+r},"Y")+"\n"}Object.entries(r.geometry).filter(function(e){return!e[0].includes("shadows")&&e[1].length>0&&"p1"in e[1][0]}).forEach(function(e){return n+=e[1].reduce(function(e,r){return e+" "+r.p1.i+":"+r.p3.i},se[e[0]])+"\n"});return n};var be=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;var i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;var o=arguments.length>4&&arguments[4]!==undefined?arguments[4]:false;var f=arguments.length>5&&arguments[5]!==undefined?arguments[5]:null;var u=arguments.length>6&&arguments[6]!==undefined?arguments[6]:2;t=t||-r.width/2;n=0;var c={true:1,false:0,1:1,0:0};var l={components:[],horizontals:[],verticals:[],supports:[],backs:[],legs:[],doors:[],drawers:[],plinth:[],boxes:[],buttons:[],inserts:[],material:0,height:r.height,additional_height:[F,12],modules:[],lines:{},row_styles:Array(10).fill(1),rows:Array(10).fill(190),setup_id:null,density_options:[0]};if(r.geometry){r.geometry.plinth.forEach(function(e){return e.components.filter(function(e){return e.type==="Pz"||e.type==="Px"}).forEach(function(e){l.additional_height[1]=Math.max(l.additional_height[1],e.y2-e.y1+12);l.plinth.push({x1:t+e.x1,x2:t+e.x2,y1:n+e.y1,y2:n+e.y2,z1:e.z2,z2:e.z1,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,type:e.type})})})}if(r.components){l.lines=me(r,t,n+l.additional_height[1]-12);r.components.forEach(function(e,i){l.setup_id=e.m_setup_id||l.setup_id;if("density_options"in e)l.density_options=e.density_options;var o=r.geometry.opening.filter(function(r){return r.m_config_id===e.m_config_id});o=o.map(function(r){return{type:r.type,width:{value:Math.abs(r.x2-r.x1),x:(r.x2+r.x1)/2+t,y:Math.min(r.y2,r.y1)+n,z:Math.max(e.z2,e.z1)},height:{value:Math.abs(r.y2-r.y1),x:Math.min(r.x2,r.x1)+t,y:(r.y2+r.y1)/2+n,z:Math.max(e.z2,e.z1)}}});var f=i>0?{x1:t-F,x2:t+e.x1,y1:e.y1+n-F-150,y2:e.y2+n+F,z1:e.z2+F,z2:e.z1-F}:null;var u=i<r.components.length-1?{x1:t+e.x2,x2:t+Math.max.apply(Math,a()(r.components.map(function(e){return e.x2})))+F,y1:e.y1+n-F-150,y2:e.y2+n+F,z1:e.z2+F,z2:e.z1-F}:null;var c=Object.keys(l.lines).filter(function(r){return r.split("_")[1]===""+e.channel_id})[0];var s=Object.keys(l.lines).filter(function(r){return r.split("_")[0]===""+e.channel_id})[0];var v={x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z2,z2:e.z1,hover_left:f,hover_right:u,line_left:c||null,line_right:s||null,m_config_id:e.m_config_id||null,series_id:e.series_id||null,table_id:e.table_id||null,door_flip:e.door_flip||null,door_flippable:e.door_flippable||false,cables:e.cables,cables_available:e.cables_available,channel_id:e.channel_id,order:e.order||0,component_id:e.id,distortion:e.distortion,compartments:o};l.components.push(v);var p=10;var h=-150+n;var d=t+(e.x1+e.x2)/2;l.buttons.push(w()({},v,{x1:d-p,x2:d+p,y2:h-p,y1:h+p,z1:e.z2,z2:e.z2-2*p,r:p}))})}r.geometry.horizontals.forEach(function(e){l.horizontals.push({x1:t+e.x1,x2:t+e.x2,y1:Math.abs(e.y1+e.y2)/2+n,y2:Math.abs(e.y1+e.y2)/2+n,z1:e.z2,z2:e.z1,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})});r.geometry.verticals.forEach(function(e){l.verticals.push({x1:t+Math.abs(e.x1+e.x2)/2,x2:t+Math.abs(e.x1+e.x2)/2,y1:e.y1+n,y2:e.y2+n,z1:e.z2,z2:e.z1,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})});r.geometry.supports.forEach(function(e){l.supports.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:12,z2:0,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})});r.geometry.backs.forEach(function(e){l.backs.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:12,z2:0,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,cable_visible:e.cable_visible||0})});r.geometry.doors.concat(r.geometry.doors_front).forEach(function(e){l.doors.push({x1:t+e.x1+u,x2:t+e.x2-u,y1:e.y1+n+u,y2:e.y2+n-u,z1:e.z2,z2:e.z2-19,flip:c[e.flip]||0,type:e.handle,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,innerOffset:u})});r.geometry.drawers.forEach(function(e){var r=Math.abs(e.y2-e.y1);var a=Math.abs(e.z2-e.z1);var i=Math.abs(e.x2-e.x1);l.drawers.push({x1:t+e.x1+u,x2:t+e.x2-u,y1:e.y1+n+u,y2:e.y2+n-u,z1:e.z2,z2:e.z2-19,type:e.shelf_type==="type_02"?1:0,door_handler_width:130,door_handler_height:20,drawer_cutout:28,bottom_offset:9+13,bottom_thickness:13,bottom_depth:a-50,back_height:r-92,sides_length:a-50,sides_height:r-72,front_handling_size:20,divisions:e.divisions||null,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,innerOffset:u})});r.geometry.plinth.forEach(function(e){return e.components.filter(function(e){return e.type==="Pz"||e.type==="Px"}).forEach(function(e){l.additional_height[1]=Math.max(l.additional_height[1],e.y2-e.y1+12);l.plinth.push({x1:t+e.x1,x2:t+e.x2,y1:n+e.y1,y2:n+e.y2,z1:e.z2,z2:e.z1,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null,type:e.type})})});r.geometry.inserts_horizontal.filter(function(e){return e.type==="Ih"}).forEach(function(e){l.inserts.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z2,z2:e.z1,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})});r.geometry.inserts_vertical.filter(function(e){return e.type==="Iv"}).forEach(function(e){l.inserts.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z2,z2:e.z1,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})});r.geometry.legs.forEach(function(e){l.legs.push({x1:t+Math.abs(e.x1+e.x2)/2,x2:t+Math.abs(e.x1+e.x2)/2,y1:e.y1+n,y2:e.y2+n,z1:Math.abs(e.z1+e.z2)/2,z2:Math.abs(e.z1+e.z2)/2,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null})});if(i===true){l.additional_elements={styles:he(r.geometry,f),shadow_left:[],shadow_middle:[],shadow_right:[],shadow_side:[]};r.geometry.shadows_outer.forEach(function(e){l.additional_elements.shadow_middle.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2})});if(o===true){r.geometry.shadows_inner.forEach(function(e){l.additional_elements.shadow_middle.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2})})}r.geometry.shadows_left.forEach(function(e){l.additional_elements.shadow_left.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2})});r.geometry.shadows_right.forEach(function(e){l.additional_elements.shadow_right.push({x1:t+e.x1,x2:t+e.x2,y1:e.y1+n,y2:e.y2+n,z1:e.z1,z2:e.z2})})}C(l);P(l,r.density_mode,r.distortion_mode);l["boundingBoxForCamera"]=ye(l);return l};function ye(e){var r=F/2;var t=Math.max.apply(Math,a()(e.components.map(function(e){return e.x2})))+r;var n=Math.min.apply(Math,a()(e.components.map(function(e){return e.x1})))-r;var i=Math.max.apply(Math,a()(e.components.map(function(e){return e.y2})))+r;var o=0-r;var f=Math.max.apply(Math,a()(e.components.map(function(e){return e.z2})))-r;var u=Math.min.apply(Math,a()(e.components.map(function(e){return e.z1})))+r;return{p1:[n,o,u],p2:[n,i,u],p3:[t,i,u],p4:[t,o,u],p5:[n,o,f],p6:[n,i,f],p7:[t,i,f],p8:[t,o,f],pMin:[n,o,u],pMax:[t,i,f]}}function _e(e,r,t,n,a){var i=[];if(["horizontals","verticals","supports"].includes(r)){if("opening"in e&&e.opening.length>0){var o=e.opening.sort(function(e){return e[0]});o.push([e.x2,null]);i.push({x_domain:[e.x1*100+t,o[0][0]*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,a*100]});for(var f=0;f<o.length-1;f+=1){var u=o[f];var c=o[f+1];i.push({x_domain:[u[0]*100+t,u[1]*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,e.z1*100+n]},{x_domain:[u[0]*100+t,u[1]*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z2*100-n,e.z2*100]},{x_domain:[u[1]*100+t,c[0]*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,a*100]})}}else{i.push({x_domain:[e.x1*100+t,e.x2*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,a*100],type:r})}}else if(["inserts_vertical","inserts_horizontal"].includes(r)){i.push({x_domain:[e.x1*100+t,e.x2*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,a*100],type:r})}else if(["backs","fronts"].includes(r)){i.push({x_domain:[e.x1*100+t,e.x2*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100+n+200,e.z1*100+200],type:r})}else if(r==="drawers"){var l=e.shelf_type==="type_02"?2:1;var s=l!==2?1640:n;var v=[3300,3300,450][l];i.push({x_domain:[e.x1*100+t+450,e.x2*100+t-450],y_domain:[e.y1*100+300,e.y2*100-v],z_domain:[e.z2*100-100,e.z2*100-100-s],type:"T_front"});i.push({x_domain:[e.x1*100+t+800,e.x1*100+t+800+n],y_domain:[e.y1*100+1650,e.y2*100-5350],z_domain:[e.z2*100-100-s,e.z2*100-100-s-24e3],type:"T_side1"});i.push({x_domain:[e.x2*100+t-800,e.x2*100+t-800-n],y_domain:[e.y1*100+1650,e.y2*100-5350],z_domain:[e.z2*100-100-s,e.z2*100-100-s-24e3],type:"T_side2"});i.push({x_domain:[e.x1*100+t+450,e.x2*100+t-450],y_domain:[e.y1*100+3050,e.y2*100-3600],z_domain:[e.z2*100-100-24e3-s,e.z2*100-100-s-24e3-n],type:"T_back"});i.push({x_domain:[e.x1*100+t+800+n,e.x2*100+t-800-n],y_domain:[e.y1*100+3050,e.y1*100+3050+n],z_domain:[e.z2*100-100-s,e.z2*100-100-24e3-s],type:"T_bottom"});if(l!==2){i.push({x_domain:[e.x1*100+t+450,e.x2*100+t-450],y_domain:[e.y2*100-v,e.y2*100-300],z_domain:[e.z2*100-100,e.z2*100-100-s],type:"T_handle"})}else{i.push({x_domain:[e.x1*100+t+450,e.x1*100+t+450+13e3],y_domain:[e.y2*100-450,e.y2*100-300],z_domain:[e.z2*100-s,e.z2*100-s+3900],type:"T_handle"})}if(e.drawer_divisions){e.drawer_divisions.forEach(function(r){return i.push({x_domain:[r*100+t-n/2,r*100+t+n/2],y_domain:[e.y1*100+3050+n,e.y2*100-5350],z_domain:[e.z2*100-100-s,e.z2*100-100-24e3-s],type:"T_division"})})}}else if(r==="doors"){var p=e.shelf_type==="type_02"?2+e.handle:e.handle;var h=p<2?1640:n;var d=[3300,1e3,0,0][p];if(e.flip==1){if(d===3300&&e.double===1)d=1e3;var m=d!==1e3?t:t+300;i.push({x_domain:[e.x1*100+t+300,e.x2*100+m-d-300],y_domain:[e.y1*100+300,e.y2*100-300],z_domain:[e.z2*100,e.z2*100-h],door_type:e.flip,type:"door"});if(d){i.push({x_domain:[e.x2*100+m-d-300,e.x2*100+m-300],y_domain:[e.y1*100+300,e.y2*100-300],z_domain:[e.z2*100,e.z2*100-h],type:"D_handle"})}else if(p===2){i.push({x_domain:[e.x2*100-13e3+t,e.x2*100+t],y_domain:[e.y2*100-300,e.y2*100-150],z_domain:[e.z2*100-h,e.z2*100-h+3900],type:"D_handle"})}}else{i.push({x_domain:[e.x1*100+t+d,e.x2*100+t-300],y_domain:[e.y1*100+300,e.y2*100-300],z_domain:[e.z2*100,e.z2*100-h],door_type:e.flip,type:"door"});if(d){i.push({x_domain:[e.x1*100+t+300,e.x1*100+t+d],y_domain:[e.y1*100+300,e.y2*100-300],z_domain:[e.z2*100,e.z2*100-h],type:"D_handle"})}else{i.push({x_domain:[e.x1*100+t,e.x1*100+t+13e3],y_domain:[e.y2*100-300,e.y2*100-150],z_domain:[e.z2*100-h,e.z2*100-h+3900],type:"D_handle"})}}}else if(r==="plinth"){e.components.forEach(function(r){i.push({x_domain:[r.x1*100+t,r.x2*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[r.z1*100,r.z2*100],type:r.type})})}else if(r==="legs"){i.push({x_domain:[e.x1*100+t,e.x2*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,e.z2*100],type:e.type})}else if(r==="components"){if(e!=={}){i.push({x_domain:[e.x1*100+t,e.x2*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,e.z2*100],type:e.type})}else{console.log("EMPTY ELEM")}}else if(r==="mark"){if(e!=={}){i.push({x_domain:[e.x1*100+t,e.x2*100+t],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,e.z2*100],type:e.type})}}else if(r==="cube"){var g=(e.x1*100+t+e.x2*100+t)/2;var b=1e3;var y=-5e3;if(e!=={}){i.push({elem_type:Z,x_domain:[g-b,g+b],y_domain:[y-b,y+b],z_domain:[e.z2*100,e.z2*100-b*2]})}}return i}var xe=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1800;console.log("xOffset",t);var a=[];var i=[];var o=[];var f=[];var u={doors:"Door",drawers:"Drawer",opening:"Opening",mark:"Mark",plinth:"Plinth",inserts_vertical:"Insert",inserts_horizontal:"Insert",legs:"Leg",components:"Component"};var c=[];Object.entries(r.geometry).forEach(function(e){var r=S()(e,2),i=r[0],l=r[1];var s=u.elemType||"";var v=c.includes(i)?.2:0;l.forEach(function(e){var r=e.z2-Math.abs(Number(e.z1-e.z2)*v);var u=e.proper||true;var c=i.includes("plinth")||i.includes("insert")?"supports":i;var l={components:_e(e,i,t*100,n,r),elem_type:se[c]||"S",surname:e.surname||u?s:"improper",x_domain:[e.x1*100+t*100,e.x2*100+t*100],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,e.z2*100],c_subconfig_id:e.c_subconfig_id||null,c_config_id:e.c_config_id||null,m_config_id:e.m_config_id||null};o.push(l["x_domain"][0],l["x_domain"][1]);f.push(l["y_domain"][0],l["y_domain"][1]);if(e.flip){l.flip=e.flip?1:0}a.push(l)})});var l=null;r.components.forEach(function(e){var r=c.includes("components")?.2:0;var o=e.z2-Math.abs(Number(e.z1-e.z2)*r);var f="components";l=e.m_setup_id||l;var u={components:_e(e,f,t*100,n,o),elem_type:se[f]||"improper",x_domain:[e.x1*100+t*100,e.x2*100+t*100],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,e.z2*100],c_subconfig_id:e.c_subconfig_id||null,c_config_id:e.config_id||null,m_config_id:e.m_config_id||null,table_id:e.table_id||null,series_id:e.series_id||null,door_flip:e.door_flip||null,door_flippable:e.door_flippable||false,cables_available:e.cables_available||false,channel_id:e.channel_id,order:e.order||0};i.push(u);var s=w()({},u,{components:_e(e,"cube",t*100,n,o),elem_type:Z,x_domain:[e.x1*100+t*100,e.x2*100+t*100],y_domain:[e.y1*100,e.y2*100],z_domain:[e.z1*100,e.z2*100],surname:""});a.push(s)});var s=Math.max.apply(Math,o)-Math.min.apply(Math,o);var v=Math.max.apply(Math,f)-Math.min.apply(Math,f);var p=ge(r);var h={item:{elements:a,components:i,width:s,height:v,setup_id:l}};C(h);P(h,r.density_mode,r.distortion_mode);return h};function we(e,r){var t=e.serialization.serialization?e.serialization.serialization:e.serialization;var n=t.mesh[r].parameters.size_x;var a={variable:"width",label:"Width",default:1500,min:n[0],max:n[1]};var i=t.mesh[r].parameters.size_y;var o={variable:"height",label:"Height",default:300,min:i[0],max:i[i.length-1],steps:i};var f={variable:"depth",label:"Depths",default:320,options:[{value:320,label:"32cm"},{value:400,label:"40cm"}]};var u={variable:"plinth",label:"Plinth",default:true,options:[{value:true,label:"Feet"},{value:false,label:"Plinth"}]};var c={widthSlider:a,heightSlider:o,depthToogle:f,plinthToogle:u};e["uiConfigParams"]=c;return c}function ke(e){var r=e.component;if(e.mesh){var t=e.component_series;Object.keys(t).forEach(function(e){var n=[];var i=t[e].setups;Object.values(i).forEach(function(e){var t=r[e].parameters.dim_x.split("-").map(function(e){return Number(e)});n.push(t)});var o=Math.max.apply(Math,a()(n.map(function(e){return e[0]})));var f=Math.min.apply(Math,a()(n.map(function(e){return e[1]})));t[e]["dim_x"]="".concat(o,"-").concat(f)});var n=e.component_table;Object.keys(n).forEach(function(e){var r=[];var i=n[e].configs;Object.values(i).forEach(function(e){var n=t[e].dim_x.split("-").map(function(e){return Number(e)});r.push(n)});var o=Math.max.apply(Math,a()(r.map(function(e){return e[0]})));var f=Math.min.apply(Math,a()(r.map(function(e){return e[1]})));n[e]["dim_x"]="".concat(o,"-").concat(f)});var i=e.mesh;Object.keys(i).forEach(function(e){var r=i[e].setups;Object.keys(r).forEach(function(e){r[e].configs.forEach(function(e){e.parameters["calc_table_dim_x"]=n[e.parameters.table].dim_x})})})}}var ze={"#aa":100,"#a":200,"#b":300,"#c":400,"#d":500,"#e":600,"#f":700,"#g":800,"#h":900,"#i":1e3,"#j":1100,"#height_aa":100,"#height_a":200,"#height_b":300,"#height_c":400,"#height_d":500,"#height_e":600,"#height_f":700,"#height_g":800,"#height_h":900,"#height_i":1e3,"#height_j":1100,"#default_back":0,"#mat":18,"#insert_thickness":18,"#support_width":125,"#plinth_height":90,"#elemDoor":283,"#elemDrawer":282,"#elemOpen":285};var Ee=["t","u","v","w","x","y","z"];var Se=function e(r,t){var n=r.map(function(e){return Math.floor(e)});var a=n.reduce(function(e,r){return e+r},0);for(var i=0;i<t-a;i++){n[i%r.length]+=1}return n};var Ae=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null;var i=function e(r){var t=Ee.filter(function(e){return r.some(function(r){return pe(r)&&r.includes(e)&&!r.startsWith("#")})}).length;var n=r.filter(function(e){return pe(e)&&(e.startsWith("max")||e.startsWith("*"))}).length;if(t===0&&n===0)return"fixed";if(t===1&&n===0)return"ratio";if(t===0&&n===1)return"flexible";return"other"};var o=function e(r,t,n,a){var i=Ee.find(function(e){return r.some(function(r){return pe(r)&&!r.startsWith("#")&&r.includes(e)})});var o=function e(r){return pe(r)&&!r.startsWith("#")&&r.includes(i)};var f=r.filter(function(e){return!o(e)}).map(function(e){return Ae(e,0,n,a)});var u=r.filter(function(e){return o(e)}).map(function(e){return Number(e.replace(i,""))||1});var c=(n-f.reduce(function(e,r){return e+r},0))/u.reduce(function(e,r){return e+r},0);u=u.map(function(e){return e*c});u=Se(u,n-f.reduce(function(e,r){return e+r},0));r=r.slice().reverse();return r.map(function(e){return o(e)?t+u.pop():t+f.pop()}).reverse()};var f=function e(r,t,n,a){var i=["*","min","max"];var o=function e(r){return pe(r)&&!r.startsWith("#")&&i.some(function(e){return r.includes(e)})};var f=r.filter(function(e){return!o(e)}).map(function(e){return Ae(e,0,n,a)[0]}).reduce(function(e,r){return e+r},0);return r.map(function(e){if(o(e)){return Ae(e,t,n-f,a)[0]}else{return Ae(e,0,n,a)[0]}})};var u=["","middle","single","double","triple","quadruple","quint","sex","sept","gradient",R,B,U,q,V,H,Y,Z,ee,re,ne,$,ae,te,W,ie,oe];if(r===undefined||r===null||typeof r==="boolean"||u.indexOf(r)>-1){return r}a=w()({},ze,a);if(r.constructor===Array){switch(i(r)){case"fixed":return r.map(function(e){return Ae(e,t,n,a)});case"ratio":return o(r,t,n,a);case"flexible":return f(r,t,n,a);default:throw new Error("Cannot parse list: ".concat(r))}}if(typeof r==="string"||r instanceof String){r=r.toLowerCase().replace(/ /g,"").replace(/--/g,"");if(r.includes(",")){return r.split(",").map(function(e){return Ae(e,t,n,a)})}if(/^\d+\.?\d+$/.test(r)){return t+Math.round(r)}if(/^-\d+\.?\d+$/.test(r)){return t+n+Math.round(r)}if(r.startsWith("#")){return Ae(a[r],t,n,a)}if(r.startsWith("-#")){return Ae("-".concat(a[r.substr(1)]),t,n,a)}if(r==="x"){r=n}else if(r==="t"){return true}else if(r==="f"){return false}else if(r==="r"||r==="top"){r=n}else if(r==="l"||r==="bottom"){r=t}else if(r==="m"){r=n/2}else if(r.endsWith("x")){r=parseInt(r.replace(/x/g,""),10)}else if(r.endsWith("mm")){r=parseInt(r.replace(/mm/g,""),10)}else if(r.endsWith("cm")){r=parseInt(r.replace(/cm/g,""),10)*10}else if(r.endsWith("dm")){r=parseInt(r.replace(/dm/g,""),10)*100}else if(r.endsWith("%")){r=parseFloat(r.replace(/%/g,""))*.01*n}else if(r.includes("/")){r=parseInt(r.split("/")[0],10)*n/parseInt(r.split("/")[1],10)}if(!isNaN(r)){r=Number(r)}}if(typeof r==="number"&&Number.isFinite(r)&&r>=0){return t+Math.round(r)}if(typeof r==="number"&&Number.isFinite(r)&&r<0){return t+n+Math.round(r)}if(typeof r==="number"){return r}throw new Error("Cannot parse value -> ".concat(r))};var je=function e(r,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;var i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:false;var f=arguments.length>6&&arguments[6]!==undefined?arguments[6]:null;var u=arguments.length>7&&arguments[7]!==undefined?arguments[7]:null;var c=function e(r){if(r===undefined){return[r]}switch(r.constructor){case Array:return r;case Object:throw new Error("Obiekt w parserze!");default:return[r]}};var l;if(r===undefined||!r||!(r in t)||t[r]===undefined||u!==null&&t[r]===u){l=c(Ae(o,a,i,n));return l}l=t[r];if(l===true&&f!==null){l=f}if(typeof l==="string"||l instanceof String){l=l.replace(/ /g,"")}var s=Ae(l,a,i,n);s=c(s);return s};var Oe=function e(r,t,n){var a=function e(r,t,n,a){var i={};switch(r.type){default:["x1","x2"].forEach(function(e){if(!(e in r)||Number.isInteger(r[e]))return;if(e in t&&r[e]in t[e]){i[e]=t[e][r[e]];return}if(!(e in t))t[e]={};var o=Math.round(r[e]);if(e==="x1"&&Math.abs(n-o)<1)o=n;if(e==="x2"&&Math.abs(a-o)<1)o=a;t[e][r[e]]=o;i[e]=t[e][r[e]]})}return i};var i=[];var o={};var f={x1:o,x2:o};r.forEach(function(e){i.push(w()({},e,a(e,f,t,n)))});return i};var Te=function e(r,t,n,a,i,o){var f=r;var u=r+t;var c=n;var l=n+a;var s=i;var v=i+o;return{x1:f,x2:u,y1:c,y2:l,z1:s,z2:v}};var Me=function e(r,t,n,a,i,o,f,u,c,l,s,v,p,h){var d=[];var m=Te(r,t,n,a,i,o),g=m.x1,b=m.x2,y=m.y1,_=m.y2,x=m.z1,k=m.z2;if(l>=0){d.push(w()({x1:g,x2:b,y1:y,z1:x,z2:k-(l===2?h:0)},p,{y2:y,type:[X,V,H][l]}))}d.push(w()({x1:g,x2:b,y1:y,y2:_,z1:x,z2:k},p,{type:ee}));if(f>=0){d.push(w()({x1:g,y1:y,y2:_,z1:x,z2:k-(f===2?h:0)},p,{x2:g,type:[Q,ae,Y][f]}))}if(c>=0){d.push(w()({x2:b,y1:y,y2:_,z1:x,z2:k-(c===2?h:0)},p,{x1:b,type:[Q,ae,Y][c]}))}if(u>=0){var z=u===3?[[g+F/2,b-F/2]]:[];d.push(w()({x1:g,x2:b,y2:_,z1:x,z2:k-(u===2?h:0)},p,{y1:_,type:[X,V,H,V][u],opening:z}))}d.push(w()({x1:g,x2:b,y1:y,y2:_,z1:x,z2:k},p,{type:ue}));return d};var De=function e(r,t,n,a,i,o,f,u,c,l){var s=arguments.length>10&&arguments[10]!==undefined?arguments[10]:null;var v=arguments.length>11&&arguments[11]!==undefined?arguments[11]:false;var p=arguments.length>12&&arguments[12]!==undefined?arguments[12]:null;var h=arguments.length>13&&arguments[13]!==undefined?arguments[13]:false;var d=arguments.length>14&&arguments[14]!==undefined?arguments[14]:"type_02";var m=arguments.length>15&&arguments[15]!==undefined?arguments[15]:false;var g=arguments.length>16&&arguments[16]!==undefined?arguments[16]:null;var b=arguments.length>17&&arguments[17]!==undefined?arguments[17]:50;var y=arguments.length>18&&arguments[18]!==undefined?arguments[18]:null;var _=[];var x=Te(r,t,n,a,i,o),k=x.x1,z=x.x2,E=x.y1,S=x.y2,A=x.z1,j=x.z2;var O=w()({x1:k,x2:z,y1:E,y2:S,z1:A,z2:j},l,{type:c,base_e_id:s,shelf_type:d});if(h&&c!==$){var T=Object.assign({},O);T.x1=h;_.push(T);O.x2=h;O.flip=1}if(v){O.flip=v}if(p!==null){O.custom_flip=p}if(c===$&&y){O.drawer_divisions=y.filter(function(e){return k<e&&e<z})}if(c!==B&&c!==ee){_.push(O)}if(u>0){O.z1=j-F-4;_.push(w()({x1:k,y1:E,y2:S,z1:j-F-4},l,{x2:z,z2:j,type:B}))}var M=false,D=false;switch(f){case 4:if(t>L*2+F/2){M=true;D=true}else if(t>L+F/2){D=true}break;case 3:if(t>L+F/2)D=true;break;case 2:if(t>L+F/2)M=true;break;case 1:_.push(w()({x1:k,x2:z,y1:E,y2:S,z1:A},l,{z2:A+F,type:R}));if(m[0]!==false){m.forEach(function(e,r){var t=b[r]||b[b.length-1];var n=t<0;_.push(w()({x1:k,x2:z,y1:E+e,y2:E+e+t,size:t,flip:n,z1:A},l,{z2:A+F,type:J,active:g===true}))})}break}if(M===true){_.push(w()({x1:k,y1:E,y2:S,z1:A},l,{x2:k,z2:A+F,size:L,flip:false,type:ne}))}if(D===true){_.push(w()({x2:z,y1:E,y2:S,z1:A},l,{x1:z,size:L,flip:true,z2:A+F,type:ne}))}_.push(w()({x1:k,x2:z,y1:E,y2:S,z1:A,z2:j},l,{type:fe}));return _};var Ie=function e(r,t,n,a,i,o){var f=function e(r){var t=r[0]===null||r[0]===false?[null]:[];var n=(r[r.length-1]===null||r[r.length-1]===false)&&r.length>1?[null]:[];r=t.concat(r.slice(t.length,r.length-n.length).sort(function(e,r){return e-r}),n);r=r.length!==1?r:[null,r[0],null];return r};var u=function e(r,t,n){var a=function e(r){return r[2]};var i=function e(r){return r[2]+r[3]};var o=function e(r){return r[4]};var f=function e(r){return r[4]+r[5]};t.forEach(function(e){var t=[];r.forEach(function(r){if(n==="x"&&a(r)<e[1]&&e[1]<i(r)&&e[0]<f(r)&&e[2]>o(r)){t.push.apply(t,[[a(r),e[1]-a(r),r[4],r[5]],[e[1],i(r)-e[1],r[4],r[5]]])}else if(n==="y"&&o(r)<e[1]&&e[1]<f(r)&&e[0]<i(r)&&e[2]>a(r)){t.push.apply(t,[[r[2],r[3],r[4],e[1]-o(r)],[r[2],r[3],e[1],f(r)-e[1]]])}else{t.push(r)}});r=t});return r};var c=[],l=[],s=[];if(!("insert__dom_x"in r)&&!("insert__dom_y"in r)){c=[[0,0,n,a,i,o]];return{subsections:c,lines_x:l,lines_y:s}}var v=f(je("insert__dom_x",r,t,n,a,[null,null]));var p=f(je("insert__dom_y",r,t,i,o,[null,null]));var h=[v.filter(function(e){return e}),v[0]||n,v[v.length-1]||n+a],d=h[0],m=h[1],g=h[2];var b=[p.filter(function(e){return e}),p[0]||i,p[p.length-1]||i+o],y=b[0],_=b[1],x=b[2];l=d.map(function(e){return[_,e,x]}).sort(function(e,r){return e-r});s=y.map(function(e){return[m,e,g]}).sort(function(e,r){return e-r});c=[[m,g-m,_,x-_]];c=u(c,l,"x");c=u(c,s,"y");return{subsections:c,lines_x:l,lines_y:s}};var Ce=function e(r,t,n,i,o,f,u,c,l,s,v,p,h,d,m,g,b,y,_,x){var k=je("insert__offset",r,t,0,0,30),z=S()(k,1),E=z[0];var A=je("type",r,t,0,0,ee)[0];if([U,$,oe].includes(A))b=1;var j=je("fill__flip",r,t)[0]||0;var O=x&&"door_flip"in x&&x["door_flip"]!==null?{left:0,right:1}[x["door_flip"]]:null;var T=x&&"cables"in x&&x["cables"]!==null?x["cables"]:null;var M=t["#object_type"]||"type_02";var D=[];D.push.apply(D,a()(Me(n,i,o,f,u,c,h,d,m,g,y,l,_,E)));var I=Ie(r,t,n,i,o,f),C=I.subsections,P=I.lines_x,F=I.lines_y;if((P===undefined||P.length===0)&&(F===undefined||F.length===0)){E=0}var N=E;if(!je("insert__dom_share",r,t,0,0,false)[0]){C=[[n,i,o,f]];N=0;if(A===U){A=q}}C.forEach(function(e){var o=S()(e,4),f=o[0],l=o[1],s=o[2],v=o[3];var p=je("fill__split",r,t,n,i,false,"50%")[0];var h=je("cable__pos_y",r,t,0,v,false);var d=je("cable__height",r,t,0,v,50);var m=je("drawer__divisions",r,t,f,l,[]);if(je("fill__split_start",r,t,0,0,0)[0]>=l||l>=je("fill__split_end",r,t,0,0,Number.POSITIVE_INFINITY)[0]||f+l<p||f>p)p=false;D.push.apply(D,a()(De(f,l,s,v,u,c-N,b,y,A,_,null,j,O,p,M,h,T,d,m)))});P.forEach(function(e){var r=S()(e,3),t=r[0],n=r[1],a=r[2];D.push(w()({},_,{x1:n,x2:n,y1:t,y2:a,z1:u,z2:c-E,type:Y}))});F.forEach(function(e){var r=S()(e,3),t=r[0],n=r[1],a=r[2];D.push(w()({},_,{x1:t,x2:a,y1:n,y2:n,z1:u,z2:c-E,type:H}))});return D};var Pe={O:{left:1,right:1,top:1,bottom:1,back:-1,front:-1},D:{left:1,right:1,top:1,bottom:1,back:1,front:-1},T:{left:1,right:1,top:1,bottom:1,back:1,front:-1},FB:{left:1,right:1,top:1,bottom:1,back:1,front:-1},TB:{left:1,right:1,top:3,bottom:1,back:1,front:1},0:{left:1,right:1,top:1,bottom:1,back:-1,front:-1},1:{left:1,right:1,top:1,bottom:1,back:1,front:-1},2:{left:1,right:1,top:1,bottom:1,back:1,front:-1},3:{left:1,right:1,top:1,bottom:1,back:1,front:-1},4:{left:1,right:1,top:3,bottom:1,back:1,front:1}};var Fe=function e(r,t,n,a){var i=je("type",r,t,0,0,"O")[0];var o=Pe[i];var f=je("face__s_left",r,t,0,0,o.left)[0];var u=je("face__s_right",r,t,0,0,o.right)[0];var c=je("face__s_top",r,t,0,0,o.top)[0];var l=je("face__s_bottom",r,t,0,0,o.bottom)[0];var s=o.back===1?1:je("face__s_back",r,t,0,0,o.back)[0];var v=je("face__s_front",r,t,0,0,o.front)[0];var p=[[-1,n,n+a,f,c,u,l,s,v]];if(je("triple__dim",r,t,n,a)[0]&&je("triple__start",r,t,0,0,0)[0]<=a&&a<je("triple__stop",r,t,0,0,Number.POSITIVE_INFINITY)[0]){var h=je("face__t_top_l",r,t,0,0,c)[0];var d=je("face__t_top_m",r,t,0,0,c)[0];var m=je("face__t_top_r",r,t,0,0,c)[0];var g=je("face__t_bottom_l",r,t,0,0,l)[0];var b=je("face__t_bottom_m",r,t,0,0,l)[0];var y=je("face__t_bottom_r",r,t,0,0,l)[0];var _=je("face__t_middle_l",r,t,0,0,1)[0];var x=je("face__t_middle_r",r,t,0,0,1)[0];var w=je("face__t_back_l",r,t,0,0,s)[0];var k=je("face__t_back_m",r,t,0,0,s)[0];var z=je("face__t_back_r",r,t,0,0,s)[0];var E=je("face__t_front_l",r,t,0,0,v)[0];var A=je("face__t_front_m",r,t,0,0,v)[0];var j=je("face__t_front_r",r,t,0,0,v)[0];var O=je("triple__dim",r,t,n,a);var T=S()(O,2),M=T[0],D=M===void 0?null:M,I=T[1],C=I===void 0?null:I;D=!Number.isNaN(parseInt(D,10))?D:n+a/3;C=!Number.isNaN(parseInt(C,10))?C:n+a/3*2;if(C<D){var P=[C,D];D=P[0];C=P[1]}p.push.apply(p,[[4,n,D,f,h,_,g,w,E],[5,D,C,_,d,x,b,k,A],[6,C,n+a,x,m,u,y,z,j]])}else if(je("double__dim",r,t,n,a)[0]&&je("double__start",r,t,0,0,0)[0]<=a&&a<je("double__stop",r,t,0,0,Number.POSITIVE_INFINITY)[0]){var F=je("face__d_top_l",r,t,0,0,c)[0];var N=je("face__d_top_r",r,t,0,0,c)[0];var L=je("face__d_bottom_l",r,t,0,0,l)[0];var R=je("face__d_bottom_r",r,t,0,0,l)[0];var B=je("face__d_middle",r,t,0,0,1)[0];var W=je("face__d_back_l",r,t,0,0,s)[0];var U=je("face__d_back_r",r,t,0,0,s)[0];var q=je("face__d_front_l",r,t,0,0,v)[0];var G=je("face__d_front_r",r,t,0,0,v)[0];var $=je("double__dim",r,t,n,a,null,a/2)[0];p.push.apply(p,[[2,n,$,f,F,B,L,W,q],[3,$,n+a,B,N,u,R,U,G]])}else{p.push([1,n,n+a,f,c,u,l,s,v])}return p.sort(function(e,r){return e-r})};var Ne=function e(r,t,n,a,i,o,f,u){if(window&&window.TylkoDesignerInterface)window.TylkoDesignerInterface.displayError(u);console.error(u);return w()({},f,{x1:r,x2:r+a,y1:t,y2:t+i,z1:n,z2:n+o,type:Z,message:u})};var Le=function e(r,t,n,i,o,f,u,c,l,s,v,p,h){var d=[];var m=w()({},r.constants,p);var g=Fe(r.parameters,m,i,t);var b=je("e_size_y",r.parameters,m,0,0,0)[0];g.forEach(function(e){var t=S()(e,9),i=t[0],p=t[1],g=t[2],y=t[3],_=t[4],x=t[5],k=t[6],z=t[7],E=t[8];var A=g-p;var j=[];r.subconfigs.forEach(function(e){var r=w()({},e.constants,m);var t=je("part__value",e.parameters,r,0,0,[0]);if(t.indexOf(i)<0&&(t.indexOf(0)<0||i===-1))return;if(A<je("trans__start",e.parameters,r,0,0,0)[0]||A>je("trans__stop",e.parameters,r,0,0,Number.POSITIVE_INFINITY)[0])return;j.push(e)});if([-1,0].indexOf(i)===-1&&j.length===0){d.push.apply(d,a()(Ce(r.parameters,m,p,A,o,b,f,n,u,c,l,s,y,_,x,k,z,E,w()({},v,{c_config_id:r.parameters.c_config_id,c_subconfig_id:r.parameters.c_config_id}),h)))}j.forEach(function(e){var t=w()({},e.constants,m);var i=je("type",e.parameters,t,0,0,"O")[0];var g=Pe[i],y=g.left,_=g.top,x=g.right,k=g.bottom,z=g.back,E=g.front;var S=je("face__s_left",e.parameters,t,0,0,y)[0];var j=je("face__s_right",e.parameters,t,0,0,x)[0];var O=je("face__s_top",e.parameters,t,0,0,_)[0];var T=je("face__s_bottom",e.parameters,t,0,0,k)[0];var M=je("face__s_back",e.parameters,t,0,0,z)[0];var D=je("face__s_front",e.parameters,t,0,0,E)[0];d.push.apply(d,a()(Ce(e.parameters,t,p,A,o,b,f,n,u,c,l,s,S,O,j,T,M,D,w()({},v,{c_config_id:r.parameters.c_config_id,c_subconfig_id:e.parameters.c_config_id}),h)))})});return d};var Re=function e(r,t,n,i){var o=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;var f=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;var u=arguments.length>6&&arguments[6]!==undefined?arguments[6]:0;var c=arguments.length>7&&arguments[7]!==undefined?arguments[7]:true;var l=arguments.length>8&&arguments[8]!==undefined?arguments[8]:true;var s=arguments.length>9&&arguments[9]!==undefined?arguments[9]:null;var v=arguments.length>10&&arguments[10]!==undefined?arguments[10]:null;var p=arguments.length>11&&arguments[11]!==undefined?arguments[11]:null;var h=arguments.length>12&&arguments[12]!==undefined?arguments[12]:null;var d=arguments.length>13&&arguments[13]!==undefined?arguments[13]:null;var m=arguments.length>14&&arguments[14]!==undefined?arguments[14]:null;var g=arguments.length>15&&arguments[15]!==undefined?arguments[15]:null;if(r.configs===undefined||r.configs.length===0){return[Ne(o,f,u,t,300,n,s,"Missing component configs for component -> ".concat(r.name))]}var b=[];var y=f;var _=r.dim_x&&(""+r.dim_x).split("-").length===2?r.dim_x:"100-1000";r.configs.forEach(function(e,i){var f=Le(e,t,n,o,y,u,i===0,i>=r.configs.length-1,c,l,{m_config_id:s?s.m_config_id:null},v,p);if(f.length>0){if(je("trans__mirror",e.parameters,w()({},e.constants,v))[0]){Ye(f)}b.push.apply(b,a()(f));y=Math.max.apply(Math,a()(f.map(function(e){return e.y2})))}});b.push(w()({x1:o,x2:o+t,y1:f,y2:y===f?f+100:y,z1:u,z2:u+n,id:i,type:W},s,{w_min:m||Number(_.split("-")[0]),w_max:g||Number(_.split("-")[1]),door_flip:p?p["door_flip"]:null,cables:p?p["cables"]:null,line_left:h,line_right:d}));return b};var Be=function e(r,t,n,a,i,o){var f=arguments.length>6&&arguments[6]!==undefined?arguments[6]:0;var u=arguments.length>7&&arguments[7]!==undefined?arguments[7]:0;var c=arguments.length>8&&arguments[8]!==undefined?arguments[8]:0;var l=arguments.length>9&&arguments[9]!==undefined?arguments[9]:true;var s=arguments.length>10&&arguments[10]!==undefined?arguments[10]:true;var v=arguments.length>11&&arguments[11]!==undefined?arguments[11]:null;var p=arguments.length>12&&arguments[12]!==undefined?arguments[12]:null;var h=arguments.length>13&&arguments[13]!==undefined?arguments[13]:null;var d=arguments.length>14&&arguments[14]!==undefined?arguments[14]:null;var m=arguments.length>15&&arguments[15]!==undefined?arguments[15]:null;var g=arguments.length>16&&arguments[16]!==undefined?arguments[16]:null;var b=arguments.length>17&&arguments[17]!==undefined?arguments[17]:null;var y=r.setups?r.setups[a]:undefined;var _=t[y];if(_===undefined){return[Ne(f,u,c,n,300,i,v,"Missing Component in Series ".concat(r.parameters.name," for height -> ").concat(a,". Available: ").concat(Object.keys(r.setups||{}).join(", ")))]}return Re(_,n,i,y,f,u,c,l,s,w()({},v,{series_id:r.parameters.series_id}),p,h,d,m,g,b)};var We=function e(r,t,n,a,i,o){var f=i;if(r.configs[o].parameters.series_pick)f=r.configs[o].parameters.series_pick;var u=null;if(r.configs[0]&&"series"in r.configs[0].parameters)u=r.configs[0].parameters["series"][o]||r.configs[0].parameters["series"];else if(r&&"series"in r.parameters)u=r.parameters["series"][o]||r.parameters["series"];if(u&&""+i in t)if(""+t[i].configs[u]in n)f=t[i].configs[u];else f=Object.values(t[i].configs)[0];var c=r.configs[o].parameters.config_id;var l=r.configs[o].parameters.channel;if(a&&c in a)f=a[c].series_id||f;if(a&&l in a)f=a[l].series_id||f;return f in n?n[f]:Object.values(n)[0]};var Ue=function e(r,t,n){function a(e,r){var t=arguments.length>2&&arguments[2]!==undefined?arguments[2]:.4;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:.9;var a=[0];for(var i=1;i<e;i++){var o=1/e*i;var f=t+(n-t)*(o<r?(r-o)/r:(o-r)/(1-r));a.push(r+(o-r)*f)}a.push(1);return a}var i=r.filter(function(e){return e.includes("x")}).map(function(e){return Number(e.replace("x",""))});var o=r.filter(function(e){return!e.includes("x")}).map(function(e){return Ae(e,0,t)});var f=i.reduce(function(e,r){return r+e},0);var u=o.reduce(function(e,r){return r+e},0);var c=a(f,n).map(function(e){return e*(t-u)});c=Se(c,t-u);var l=[];var s=0;var v=0;for(var p=0;p<r.length;p++){var h=r[p];if(h.includes("x")){var d=i.shift();v+=d;l.push([s,c[v]-c[v-d]])}else{l.push([s,o.shift()])}s=l[p][0]+l[p][1]}return l};var qe=function e(r,t){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;var a=Ae(r,n,t);var i=[];a.reduce(function(e,r,t){return i[t]=e+r},0);i.unshift(0);return a.map(function(e,r){return[i[r],e]})};var Ge=function e(r,t,n){var a=qe(r.map(function(e){return e.ratio}),t,n);if(a.length===1)return a;var i=a.map(function(e){return e[1]});var o=i.map(function(e,t){return r[t].local_ratio?r[t].local_ratio*i[t]/100:0});var f=1;while(f!==0){f=0;i=i.map(function(e,t){var n=o.reduce(function(e,r,n){return n===t?e:e+r},0);var a=e-n/(i.length-1)+o[t];if(a<r[t].min){f+=Math.floor(r[t].min-a);o[t]-=Math.floor(r[t].min-a)}if(a>r[t].max){f+=Math.floor(a-r[t].max);o[t]-=Math.floor(a-r[t].max)}a=Math.min(Math.max(a,r[t].min),r[t].max);return a});if(f>0){(function(){var e=o.filter(function(e,t){return e+i[t]<r[t].max}).length;o=o.map(function(t,n){return t+i[n]<r[n].max?t+f/e:t})})()}else if(f<0){(function(){var e=o.filter(function(e,t){return e+i[t]>r[t].min}).length;o=o.map(function(t,n){return t+i[n]>r[n].min?t+f/e:t})})()}}a=qe(i,t,n);return a};var $e=function e(r,t){t.reduce(function(e,t,n){if(r[n-1][1]>e.max){var a=r[n-1][1]-e.max;r[n-1][1]-=a;e.line_right-=a;t.line_left-=a;r[n][0]-=a;r[n][1]+=a}else if(r[n-1][1]<e.min){var i=e.min-r[n-1][1];r[n-1][1]+=i;e.line_right+=i;t.line_left+=i;r[n][0]+=i;r[n][1]-=i}return t});t.reduceRight(function(e,t,n){if(n===0)return t;if(r[n+1][1]>e.max){var a=r[n+1][1]-e.max;r[n+1][0]+=a;r[n+1][1]-=a;e.line_left+=a;t.line_right+=a;r[n][1]+=a}else if(r[n+1][1]<e.min){var i=e.min-r[n+1][1];r[n+1][0]-=i;r[n+1][1]+=i;e.line_left-=i;t.line_right-=i;r[n][1]-=i}return t});return[r,t]};var Ve=function e(r,t,n){var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;var i=arguments.length>4&&arguments[4]!==undefined?arguments[4]:null;var o=arguments.length>5&&arguments[5]!==undefined?arguments[5]:null;var f=arguments.length>6&&arguments[6]!==undefined?arguments[6]:null;var u=[];r.configs.forEach(function(e,r){var t=e.parameters,n=t.division_ratio,a=t.distorted_ratio,i=t.comp_id,f=t.channel,c=t.table_dim_x,l=t.calc_table_dim_x;a=a||n;a=(""+a).split(",");var s=o&&o[f]?o[f].distortion:0;var v=l?(""+l).split("-")[0]:null;var p=l?(""+l).split("-")[1]:null;(""+n).split(",").forEach(function(e,t){u.push({ratio:(""+e).toLowerCase(),distorted_ratio:(""+(a[t]||a[0])).toLowerCase(),local_ratio:!!Number(s)?s:0,max:p||840,min:v||320,line_right:null,line_left:null,channel:f,comp_id:i,index:r})})});var c=[];switch(i){case"gradient":c=Ue(u.map(function(e){return e.ratio}),t,n);break;case"ratio":c=qe(u.map(function(e){if(e.ratio.includes("x")&&e.distorted_ratio.includes("x"))return Number(e.ratio.replace("x",""))*(1-n)+Number(e.distorted_ratio.replace("x",""))*n+"x";else if(e.ratio.includes("%")&&e.distorted_ratio.includes("%"))return Number(e.ratio.replace("%",""))*(1-n)+Number(e.distorted_ratio.replace("%",""))*n+"%";else return e.ratio*(1-n)+e.distorted_ratio*n}),t,a);break;case"local_x":c=qe(u.map(function(e){if(e.ratio.includes("x"))return Number(e.ratio.replace("x",""))*(e.local_ratio?e.local_ratio/100+1:1)+"x";else return e.ratio*(e.local_ratio?e.local_ratio/100+1:1)}),t,a);break;case"local_all":c=qe(u.map(function(e){return e.ratio}),t,a);if(c.length===1)break;var l=c.map(function(e){return e[1]});var s=l.map(function(e,r){return u[r].local_ratio?u[r].local_ratio*l[r]/100:0});l=l.map(function(e,r){var t=s.reduce(function(e,t,n){return n===r?e:e+t},0);return e-t/(l.length-1)+s[r]});c=qe(l,t,a);break;case"proportional":c=Ge(u,t,a);break;case"edge":c=qe(u.map(function(e){return e.ratio}),t,a);c=c.map(function(e,r){var t=u[r].channel;var n=u[r-1]?u[r-1].channel:null;var a=u[r+1]?u[r+1].channel:null;var i=f?Math.round(f[n+"_"+t])||null:null;var o=f?Math.round(f[t+"_"+a])||null:null;u[r].line_left=i;u[r].line_right=o;if(i){e[1]-=i;e[0]+=i}if(o)e[1]+=o;return e});var v=$e(c,u);var p=S()(v,2);c=p[0];u=p[1];break;default:c=qe(u.map(function(e){return e.ratio}),t,a)}return c.map(function(e,r){var t=S()(e,2),n=t[0],a=t[1];var i=u[r],o=i.comp_id,f=i.index,c=i.line_right,l=i.line_left,s=i.min,v=i.max;return{mc_start_x:n,mc_size_x:a,comp_id:o,index:f,line_right:c,line_left:l,min:s,max:v}})};var He=function e(r,t,n,a,i){if(i){for(var o in r){if(r[o].parameters.setup_id===i){return[r[o],[0]]}}}switch(a){case"setup_range_slider":case"setup_range_stepper":var f=Object.keys(r).filter(function(e){return"parameters"in r[e]&&(!("dim_x"in r[e].parameters)||r[e].parameters.dim_x===null)||"parameters"in r[e]&&"x_range"in r[e].parameters&&r[e].parameters.x_range[0]<=t&&t<r[e].parameters.x_range[1]||"parameters"in r[e]&&"dim_x"in r[e].parameters&&r[e].parameters.dim_x[0]<=t&&t<r[e].parameters.dim_x[1]||!("parameters"in r[e])||r[e].parameters.x_range&&r[e].parameters.dim_x}).sort(function(e,r){return e-r});var u=Math.max(Math.ceil(n/(100/f.length)-1),0);var c=f.map(function(e,t){return[Math.min(Math.ceil(t*100/f.length+1),100),r[e].configs.length]});c[u][0]=n||0;return[r[f[u]],c];default:var l=Object.keys(r).filter(function(e){return e<=t}).sort(function(e,r){return e-r}).reverse()[0];if(l===undefined){return null}return[r[l],[0]]}};var Ye=function e(r){var t=r.reduce(function(e,r){e.push(r.x1,r.x2);return e},[]).sort(function(e,r){return e-r});var n=t[0]+t.pop();r.forEach(function(e){var r=n-e.x1;var t=n-e.x2;e.x1=r<t?r:t;e.x2=r>t?r:t;if(e.hasOwnProperty("opening")&&e.opening.length>0){e.opening=e.opening.map(function(e){var r=S()(e,2),t=r[0],a=r[1];t=n-t;a=n-a;return t<a?[t,a]:[a,t]}).sort(function(e,r){return e[0]-r[0]})}if(e.type==="S"){e.flip=!e.flip}});return r};var Xe=function e(r,t,n,a,i,o,f){var u;function c(e,r,t,n){if(v==="type_02")return;e.push({x1:t,x2:n,y1:0,y2:p,z1:(a-i)/2,z2:(a+i)/2,type:"Px"});if(n-t>b*3){r.push({x1:t+b-10,x2:t+b+10,y1:-10,y2:0,z1:(a-i)/2-10,z2:(a+i)/2+10,type:K});r.push({x1:n-b-10,x2:n-b+10,y1:-10,y2:0,z1:(a-i)/2-10,z2:(a+i)/2+10,type:K})}else if(n-t>b*2){r.push({x1:(t+n)/2-10,x2:(t+n)/2+10,y1:-10,y2:0,z1:(a-i)/2-10,z2:(a+i)/2+10,type:K})}}function l(e,r,t,o){var f=t-i/2;var u=t+i/2;var c=f-g<-i/2?-i/2:f-g;if(c<o[0])o[0]=c;var l=f+g>n+i/2?n+i/2:f+g;if(l>o[1])o[1]=l;if(e.some(function(e){return e.x1===f&&e.x2===u}))return;if(v==="type_01"){e.push({x1:f,x2:u,y1:0,y2:p,z1:0+h,z2:a-h,type:"Pz"});r.push({x1:f,x2:u,y1:-10,y2:0,z1:0+h+y-10,z2:0+h+y+10,type:K});r.push({x1:f,x2:u,y1:-10,y2:0,z1:a-h-y-10,z2:a-h-y+10,type:K})}else{r.push({x1:f,x2:u,y1:-10,y2:p,z1:0+h+y-10,z2:0+h+y+10,type:K});r.push({x1:f,x2:u,y1:-10,y2:p,z1:a-h-y-10,z2:a-h-y+10,type:K})}}function s(e,r,t){var a=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;if(a&&(t===true||t==="")&&e<=d)l(_,x,d,w);if(a&&(t===true||t==="")&&r>=n-d)l(_,x,n-d,w);else if(typeof t!=="boolean"&&t!=="")l(_,x,je("x",{x:t},f,e,r-e)[0],w)}var v=f["#object_type"]||"type_02";var p=je("plinth__height",o,f,0,0,100)[0];var h=je("plinth__offset_z",o,f,0,0,30)[0];var d=je("plinth__offset_x",o,f,0,0,320)[0];var m=je("plinth__setup",o,f,0,n,[]);var g=je("plinth__offset_long",o,f,0,0,130)[0];var b=je("plinth__leg_offset_x",o,f,0,0,140)[0];var y=je("plinth__leg_offset_z",o,f,0,0,30)[0];if(m[0]===false||!t.some(function(e){return e.plinth[0]!==""&&e.plinth[0]!==false}))return r;var _=[];var x=[];var w=[d-g,n-d+g+i/2];t.forEach(function(e){var r=e.x1,t=e.x2,n=e.plinth;n.forEach(function(e){return s(r,t,e,m.length===0)})});m.forEach(function(e){return s(0,n-i,e)});var k=_.length;var z=[];_.sort(function(e,r){return e.x1-r.x1}).reduce(function(e,r,t){if(!e&&!r)return null;if(t===0&&r.x1>w[0])c(z,x,w[0],r.x1);else if(e&&r)c(z,x,e.x2,r.x1);if(t===k-1&&r.x2<w[1])c(z,x,r.x2,w[1]);return r},null);r.forEach(function(e){e.y1+=p;e.y2+=p});r=(u=r).concat.apply(u,x);r=r.concat({x1:w[0],x2:w[1],y1:0,y2:p-i/2,z1:0+h,z2:a-h,components:[].concat(_,z),type:re});return r};var Qe=function e(r,t,n,i,o,f,u,c){var l=arguments.length>8&&arguments[8]!==undefined?arguments[8]:0;var s=arguments.length>9&&arguments[9]!==undefined?arguments[9]:0;var v=arguments.length>10&&arguments[10]!==undefined?arguments[10]:0;var p=arguments.length>11&&arguments[11]!==undefined?arguments[11]:null;var h=arguments.length>12&&arguments[12]!==undefined?arguments[12]:null;var d=arguments.length>13&&arguments[13]!==undefined?arguments[13]:null;var m=arguments.length>14&&arguments[14]!==undefined?arguments[14]:null;var g=arguments.length>15&&arguments[15]!==undefined?arguments[15]:null;var b=arguments.length>16&&arguments[16]!==undefined?arguments[16]:true;var y=arguments.length>17&&arguments[17]!==undefined?arguments[17]:null;var _=r.parameters?r.parameters.density_mode:undefined;g=g?g:d;m=m?m:d;if(o<100&&Object.keys(r.setups).includes(""+o))o=r.setups[o].parameters.dim_x[0]||o;var x=He(r.setups,o,m,_,h),k=S()(x,2),z=k[0],E=k[1];if(z===null||z===undefined){return[Ne(l,s,v,o,f,u,{},"Missing mesh setup for width -> ".concat(o))]}if(z.configs===undefined||z.configs.length===0){return[Ne(l,s,v,o,f,u,{},"Missing mesh configs for width -> ".concat(o))]}var A=r.parameters?r.parameters.distortion_mode:undefined;var j=null;var O=null;var T=null;if(y){if(y.channels)O=y.channels;if(y.lines)T=y.lines}var M=je("motion",r.parameters,p,0,0)[0];var D=je("distortion",r.parameters,p)[0];var I=Ve(z,o-F,(g||M)*.01,0,A||D,O,T);var C=[];var P=[];I.forEach(function(e){var r;var o=e.mc_start_x,c=e.mc_size_x,l=e.comp_id,h=e.index,d=e.line_left,m=e.line_right,g=e.min,b=e.max;var y=We(z,t,n,O||j,l,h);var _=null;var x=z.configs[h].parameters.channel;if(j)_=j[z.configs[h].parameters.config_id]||null;else if(O)_=O[x]||null;var k=Be(y,i,c,f,u,l,o,s,v,h===0,h===I.length-1,{m_config_id:z.configs[h].parameters.config_id,m_setup_id:z.parameters.setup_id,table_id:z.configs[h].parameters.table||null,channel_id:z.configs[h].parameters.channel,order:h,distortion:_&&_.distortion?_.distortion:0,density_options:E},w()({},z.configs[h].constants,p||{}),_,d,m,g,b);if(je("trans__mirror",z.configs[h].parameters,p)[0]){k=Ye(k)}(r=P).push.apply(r,a()(k));C.push({x1:o,x2:o+c,plinth:je("plinth__value",z.configs[h].parameters,p,0,c,true)})});if(b!==false)P=Xe(P,C,o-F,u,F,w()({},r.parameters,z.parameters),w()({},r.constants,p));if(je("trans__mirror",r.parameters,p)[0]){P=Ye(P)}return P};var Je=function e(r,t,n,i,o,f,u,c,l){var s=arguments.length>9&&arguments[9]!==undefined?arguments[9]:0;var v=arguments.length>10&&arguments[10]!==undefined?arguments[10]:0;var p=arguments.length>11&&arguments[11]!==undefined?arguments[11]:0;var h=arguments.length>12?arguments[12]:undefined;var d=[];var m=v;var g=[278,398,300,400];var b=1578;var y=r.setups[u];var _=r.parameters.density_mode;var x=r.parameters.distortion_mode;var k=h.row_amount,z=h.row_heights,E=h.row_styles,S=h.motion;var A=y.configs.length;for(var j=0;j<k;j++){var O=y.configs[A>j?j:j%A];var T=z[j];var M=E[j];M=M<10?M+10:M;if(g.indexOf(T)<0){M=Number(String(M)[0]+"1")}if(m+T>b){M=Number("1"+String(M)[1])}var D=w()({},O.parameters.row_style_table[M],O.constants);if(O.parameters.height.startsWith("#")){D[O.parameters.height]=T}var I=Qe(t[O.parameters.mesh_id],n,i,o,f,O.parameters.mesh_setup,c,O.parameters.mesh_id,s,m,p,D,null,S);d.push.apply(d,a()(I));m=Math.max.apply(Math,a()(I.map(function(e){return e.y2})))}return d};var Ke=function e(r,t,n,a,i,o,f,u,c){var l=arguments.length>9&&arguments[9]!==undefined?arguments[9]:0;var s=arguments.length>10&&arguments[10]!==undefined?arguments[10]:0;var v=arguments.length>11&&arguments[11]!==undefined?arguments[11]:0;var p=arguments.length>12&&arguments[12]!==undefined?arguments[12]:null;var h=arguments.length>13?arguments[13]:undefined;var d="params"in r&&"container_mode"in r.parameters?r.parameters.container_mode:null;switch(d){case"old_configurator":return Je(r,t,n,a,i,o,f,u,c,l,s,v,p,h);default:return null}};var Ze=function e(r,t){var n;ke(r);switch(t.geom_type){case"container":var a={row_amount:t.row_amount,row_heights:t.row_heights,row_styles:t.row_styles,motion:t.motion};n=Ke(r.container[t.geom_id],r.mesh,r.component_table,r.component_series,r.component,t.width,t.height,t.depth,t.geom_id,0,0,0,a);break;case"mesh":n=Qe(r.mesh[t.geom_id],r.component_table,r.component_series,r.component,t.width,t.height,t.depth,t.geom_id,0,0,0,null,t.mesh_setup,t.motion,t.density,t.distortion,t.plinth,t.configurator_custom_params);break;case"component-set":case"component-table":case"component-series":n=Be(r.component_series[t.geom_id],r.component,t.width,t.height,t.depth,t.geom_id);break;case"component":n=Re(r.component[t.geom_id],t.width,t.depth,t.geom_id,0,0,0,true,true,null,null,w()({cables:true},t.custom_configurator_params));break;default:throw new Error("Nieznany typ geometrii!")}n=Oe(n);var i=function e(r,t,n){var a={geometry:{},width:t,height:n};Object.keys(se).forEach(function(e){a.geometry[e]=r.filter(function(r){return r.type===se[e]})});return a};n=i(n,t.width,t.height);if(t.geom_type==="mesh"){n["density_mode"]=r.mesh[t.geom_id].parameters.density_mode;n["distortion_mode"]=r.mesh[t.geom_id].parameters.distortion_mode}return n};var er=t("9523");var rr=t.n(er);var tr=t("2f26");var nr=t("b6c5");var ar=18;function ir(e){var r=new Set;var t=new Set;Object.values(e).forEach(function(e){return e.forEach(function(e){if([re,Z,K,W].includes(e.type))return;r.add(Number(e.x1));r.add(Number(e.x2));t.add(Number(e.y1));t.add(Number(e.y2))})});var n=function e(r,t){return r-t};return[a()(r).sort(n),a()(t).sort(n)]}function or(e,r){var t=0;return{linesX:e,linesY:r,points:e.reduce(function(e,n){e[n]=r.reduce(function(e,r){e[r]={x:n,y:r,i:t};t++;return e},{});return e},{})}}function fr(e,r,t){return e.points[r][t]}function ur(e,r,t,n){return e.linesX.filter(function(e){return e>=r&&e<=t}).map(function(r){return e.points[r][n]})}function cr(e,r,t,n){return e.linesY.filter(function(e){return e>=t&&e<=n}).map(function(t){return e.points[r][t]})}function lr(e,r,t,n,a){var i=e.linesY.filter(function(e){return e>=n&&e<=a});return e.linesX.filter(function(e){return e>=r&&e<=t}).map(function(r){return i.map(function(t){return e.points[r][t]})})}function sr(e,r,t,n,a){var i=ur(r,e.x1,e.x2,e.y1);if(i.length===0)return t;var o=i.map(function(e){return!(e.top&&e.bottom)?e.left||e.right||null:null}).reduce(function(e,r){return e||r},null);if(o!=null&&o.x2>e.x2){return t}if(o!=null&&t.includes(o)&&o.x2<e.x2){o.x2=e.x2+(a?n/2:-n/2);if(e.opening)o.opening=(o.opening||[]).concat(e.opening);i.forEach(function(e,r){if(r>0)e.left=o;if(r<i.length-1)e.right=o});o.p3=i[i.length-1];return t}var f=i[0].top||i[0].bottom;var u=i[i.length-1].top||i[i.length-1].bottom;e.y1-=n/2;e.y2+=n/2;e.x1=f?f.x2:e.x1-(a?n/2:-n/2);e.x2=u?u.x1:e.x2+(a?n/2:-n/2);i.forEach(function(r,t){if(t>0)r.left=e;if(t<i.length-1)r.right=e});e.p1=i[0];e.p3=i[i.length-1];t.push(e);return t}function vr(e,r,t,n){var a=cr(r,e.x1,e.y1,e.y2);e.x1-=n/2;e.x2+=n/2;e.y1-=n/2;e.y2+=n/2;e.p1=a[0];e.p3=a[a.length-1];var i=[e];for(var o=0;o<a.length;o+=1){var f=a[o];var u=f.left!==undefined&&f.left.x2>e.x1?f.left:null;var c=f.right!==undefined&&f.right.x1<e.x2?f.right:null;var l=u||c;var s=i[i.length-1];var v=!!f.bottom&&f.bottom!==s&&t.includes(f.bottom)?f.bottom:null;var p=!!f.bottom&&f.bottom!==s&&!t.includes(f.bottom)?f.bottom:null;if(!!l&&s.y1<l.y1){var h=w()({},s);if(!!v||!!p)i.splice(i.indexOf(s),1);else{s.y2=l.y1;s.p3=f;f.bottom=s}if(h.y2>l.y2){s.p1=f;h.y1=l.y2;i.push(h);f.top=h}}else if(!!l&&s.y1>=l.y1){s.p1=f;s.y1=l.y2;f.top=s}else if(!l&&!!v){i.splice(i.indexOf(s),1);i.push(v);v.y2=s.y2;v.p3=s.p3;t.splice(t.indexOf(v),1)}else if(!!p&&!!s&&p.y1<=s.y1&&p.y2>=s.y2){i.splice(i.indexOf(s),1)}else if(!!p&&!!s&&p.y1<=s.y1&&p.y2<=s.y2){s.y1=p.y2;s.p1=f}else if(!!p&&!!s&&p.y1>=s.y1&&p.y2>=s.y2){s.p3=f;s.y2=p.y1}else{f.bottom=s;f.top=s}}t=t.concat(i);return t}function pr(e,r,t,n){var a=cr(r,e.x1,e.y1,e.y2);e.x1-=e.flip?e.size+n/2:-n/2;e.x2+=e.flip?-n/2:e.size+n/2;e.p1=a[0];e.p3=a[a.length-1];var i=[e];for(var o=0;o<a.length;o+=1){var f=a[o];var u=f.left!==undefined&&f.left.x2>e.x1?f.left:null;var c=f.right!==undefined&&f.right.x1<e.x2?f.right:null;var l=e.flip?u:c;var s=i[i.length-1];var v=null;var p=null;if(e.flip){v=!!f.backbottomleft&&f.backbottomleft!==s&&t.includes(f.backbottomleft)?f.backbottomleft:null;p=!!f.backbottomleft&&f.backbottomleft!==s&&!t.includes(f.backbottomleft)?f.backbottomleft:null}else{v=!!f.backbottomright&&f.backbottomright!==s&&t.includes(f.backbottomright)?f.backbottomright:null;p=!!f.backbottomright&&f.backbottomright!==s&&!t.includes(f.backbottomright)?f.backbottomright:null}if(!!l&&s.y1<l.y1){var h=w()({},s);if(!!v||!!p)i.splice(i.indexOf(s),1);else{s.y2=l.y1;s.p3=f;if(e.flip)f.backbottomleft=s;else f.backbottomright=s}if(h.y2>l.y2){s.p1=f;h.y1=l.y2;i.push(h);if(e.flip)f.backtopleft=s;else f.backtopright=s}}else if(!!l&&s.y1>=l.y1){s.p1=f;s.y1=l.y2;if(e.flip)f.backtopleft=s;else f.backtopright=s}else if(!l&&!!v){i.splice(i.indexOf(s),1);i.push(v);v.y2=s.y2;v.p3=s.p3;t.splice(t.indexOf(v),1)}else if(!!p){i.splice(i.indexOf(s),1)}else{if(e.flip){f.backbottomleft=s;f.backtopleft=s}else{f.backbottomright=s;f.backtopright=s}}}t=t.concat(i);return t}function hr(e,r,t,n,a){var i=ur(r,e.x1,e.x2,e.y1);e.x1-=a/2;e.x2+=a/2;var o=[];var f=[];i.reduce(function(e,r){if(r.left&&!e.includes(r.left))e.push(r.left);if(r.right&&!e.includes(r.right))e.push(r.right);return e},[]).forEach(function(r){if(r.x1>=e.x1&&r.x2<=e.x2)o.push(r);else if(r.x1>=e.x1&&r.x2>e.x2){r.x1=e.x2-a;r.p1=i[i.length-1]}else if(r.x1<e.x1&&r.x2<=e.x2){r.x2=e.x1+a;r.p3=i[0]}else if(r.x1<e.x1&&r.x2>e.x2){var t=w()({},r);t.x1=e.x2-a;t.p1=i[i.length-1];r.x2=e.x1+a;r.p3=i[0];f.push(t)}});f.forEach(function(e){ur(r,e.x1+a/2,e.x2-a/2,e.y1+a/2).forEach(function(r,t){if(t>0)r.left=e;if(t<i.length-1)r.right=e})});i.forEach(function(e,r){if(r>0)e.left=undefined;if(r<i.length-1)e.right=undefined});n.push(w()({},e,{p1:i[0],p3:i[i.length-1]}));t=t.filter(function(e){return!o.includes(e)}).concat(f);return[t,n]}function dr(e,r,t,n,a){var i=cr(r,e.x1,e.y1,e.y2);var o=i[0].left!==undefined||i[0].right!==undefined;var f=i[i.length-1].left!==undefined||i[i.length-1].right!==undefined;e.y1-=a/2;e.y2+=a/2;var u=[];var c=[];i.reduce(function(e,r){if(r.bottom&&!e.includes(r.bottom))e.push(r.bottom);if(r.top&&!e.includes(r.top))e.push(r.top);return e},[]).forEach(function(r){if(r.y1>=e.y1&&r.y2<=e.y2)u.push(r);else if(r.y1>=e.y1&&r.y2>e.y2){r.y1=e.y2-(f?0:a);r.p1=i[i.length-1]}else if(r.y1<e.y1&&r.y2<=e.y2){r.y2=e.y1+(o?0:a);r.p3=i[0]}else if(r.y1<e.y1&&r.y2>e.y2){var t=w()({},r);t.y1=e.y2-(f?0:a);t.p1=i[i.length-1];r.y2=e.y1+(o?0:a);r.p3=i[0];c.push(t)}});c.forEach(function(e){cr(r,e.x1+a/2,e.y1+a/2,e.y1-a/2).forEach(function(r,t){if(t>0)r.bottom=e;if(t<i.length-1)r.top=e})});i.forEach(function(e,r){if(r>0)e.bottom=undefined;if(r<i.length-1)e.top=undefined});n.push(w()({},e,{p1:i[0],p3:i[i.length-1]}));t=t.filter(function(e){return!u.includes(e)}).concat(c);return[t,n]}function mr(e,r,t,n,a){var i=lr(r,e.x1,e.x2,e.y1,e.y2);var o=[];i.forEach(function(r){r.forEach(function(r,n){var a=r.backtopleft;if(e.flip)a=r.backbottomright;var i=!o.includes(a)&&t.includes(a);if(a&&i&&a.m_config_id===e.m_config_id)o.push(a)})});o.forEach(function(n){n.cables_available=true;if(e.active&&e.m_config_id===n.m_config_id){if(e.y1>n.y1&&e.y2<n.y2){var a=Object.assign({},n);gr(r,n,e,false);gr(r,a,e,true);t.push(a)}else{if(e.y1===n.y1){gr(r,n,e,false)}if(e.y2===n.y2){gr(r,n,e,true)}}}});n.push(w()({},e,{p1:i[0][0],p3:i[i.length-1][i[0].length-1]}));return[t,n]}function gr(e,r,t){var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;if(n){r.y2=t.y1;r.p1=fr(e,r.x1,r.y1);r.p2=fr(e,r.x1,t.y1);r.p3=fr(e,r.x2,t.y1);r.p4=fr(e,r.x2,r.y1)}else{r.y1=t.y2;r.p1=fr(e,r.x1,t.y2);r.p2=fr(e,r.x1,r.y2);r.p3=fr(e,r.x2,r.y2);r.p4=fr(e,r.x2,t.y2)}r.cable_visible=t.size}function br(e,r,t){var n=t==="front"?[[fr(r,e.x1,e.y1),fr(r,e.x1,e.y2)],[fr(r,e.x2,e.y1),fr(r,e.x2,e.y2)]]:lr(r,e.x1,e.x2,e.y1,e.y2);var a=n.length;var i=a!==0?n[0].length:0;var o=[];for(var f=1;f<i;f+=1){for(var u=1;u<a;u+=1){var c=JSON.parse(JSON.stringify(e));if(t==="back"){var l=n[u-1][f-1].backtopright;if(!(l&&l.type===e.type)){n[u-1][f-1].backtopright=c;n[u-1][f].backbottomright=c;n[u][f-1].backtopleft=c;n[u][f].backbottomleft=c}}else{var s=n[u-1][f-1].topright;if(!(s&&s.type===e.type)){n[u-1][f-1].topright=c;n[u-1][f].bottomright=c;n[u][f-1].topleft=c;n[u][f].bottomleft=c}}c.p1=n[u-1][f-1];c.p2=n[u-1][f];c.p3=n[u][f];c.p4=n[u][f-1];o.push(c)}}return o}function yr(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var t=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;r=r||[];var n=[];var i=function i(o){var f=e[o];var u=f.p1;var c=t?u.backtopleft:u.topleft;if(!r.includes(c)&&!n.includes(c))c=null;if(!u.top&&c&&c.y1===f.y1&&c.y2===f.y2&&c.flip===f.flip){c.x2=f.x2;c.p3=f.p3;c.p4=f.p4;c.type=f.type;if(t){f.p1.backtopright=c;f.p2.backbottomright=c;f.p3.backbottomleft=c;f.p4.backtopleft=c}else{f.p2.bottomright=c;f.p4.topleft=c}if(c.drawer_divisions&&f.drawer_divisions)c.drawer_divisions=[].concat(a()(c.drawer_divisions),a()(f.drawer_divisions.filter(function(e){return!(e in c.drawer_divisions)})))}else{n.push(f)}};for(var o=0;o<e.length;o+=1){i(o)}return n}function _r(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var t=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;r=r||[];var n=[];for(var a=0;a<e.length;a+=1){var i=e[a];var o=i.p1;var f=t?o.backbottomright:o.bottomright;if(!r.includes(f)&&!n.includes(f))f=null;if(!o.right&&f&&f.x1===i.x1&&f.x2===i.x2){f.y2=i.y2;f.p3=i.p3;f.p2=i.p2;f.type=i.type;if(t){i.p1.backtopright=f;i.p2.backbottomright=f;i.p3.backbottomleft=f;i.p4.backtopleft=f}else{i.p2.bottomright=f;i.p4.topleft=f}}else{n.push(i)}}return n}function xr(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;var t=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var n=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null;r=r||[];var a=[];for(var i=0;i<e.length;i+=1){var o=e[i];var f=o.p1;var u=kr(f,t,n);if(!r.includes(u)&&!a.includes(u))u=null;var c=u?zr(u,o,f,n):false;if(c)wr(u,o,t,n);else a.push(o)}return a}function wr(e,r,t,n){e.x2=r.x2;e.p1=r.p1;e.p2=r.p2;e.p3=r.p3;e.p4=r.p4;e.type=r.type;if(t){r.p1.backtopright=e;r.p2.backbottomright=e;r.p3.backbottomleft=e;r.p4.backtopleft=e}else{r.p1.topright=e;r.p2.bottomright=e;r.p3.bottomleft=e;r.p4.topleft=e}if(n==="horizontal"&&e.drawer_divisions&&r.drawer_divisions)e.drawer_divisions=[].concat(a()(e.drawer_divisions),a()(r.drawer_divisions.filter(function(r){return!(r in e.drawer_divisions)})))}function kr(e,r,t){switch(t){case"horizontal":return r?e.backtopleft:e.topleft;case"vertical":return r?e.backbottomright:e.bottomright;default:return r?e.backtopright:e.topright}}function zr(e,r,t,n){var a=e.x1===r.x1&&e.x2===r.x2;var i=e.y1===r.y1&&e.y2===r.y2;var o=!t.right;var f=!t.top;switch(n){case"horizontal":return i&&f&&e.flip===r.flip;case"vertical":return a&&o;default:return a&&i}}function Er(e){var r=[];var t=[];var n=[];e[ue].forEach(function(e){if(e.p1.top&&e.p3.bottom)r.push(e);else if(e.p1.top){e.x2+=F/2;n.push(e)}else if(e.p3.bottom){e.x1-=F/2;t.push(e)}});e[ue]=r;e[ce]=n;e[le]=t;return e}function Sr(e,r){var t=e[r];t.forEach(function(e){var t=e.p1.top&&(r!==q||e.p1.top.type!==Y)?null:e.p1.topleft;var n=e.p3.bottom&&(r!==q||e.p3.bottom.type!==Y)?null:e.p3.bottomright;e.door_parity="single";if(t&&t.type===r)if(t.flip===1&&e.flip!==1)e.door_parity="double";else if(t.flip!==1&&e.flip===1)e.door_parity="wrong";if(n&&n.type===r)if(n.flip!==1&&e.flip===1)e.door_parity="double";else if(n.flip===1&&e.flip!==1)e.door_parity="wrong";if(e.door_parity==="single"&&"custom_flip"in e)e.flip=e.custom_flip;if(t&&t.type===r&&t.door_parity==="wrong"&&e.door_parity==="wrong"){t.flip=1;t.door_parity="double";t.handle=1;e.flip=0;e.door_parity="double"}e.handle=e.flip===1&&e.door_parity==="double"?1:0;delete e.custom_flip});return e}function Ar(e,r){e.forEach(function(e){e.door_flippable=r[U].filter(function(r){return r.m_config_id===e.m_config_id}).reduce(function(r,t){if(t.door_parity==="single")e.door_flip=["left","right"][t.flip||0];return r||t.door_parity==="single"},false);e.cables_available=r[R].filter(function(r){return r.m_config_id===e.m_config_id}).reduce(function(r,t){if(t.cables_available&&!e.cables)e.cables=!!t.cable_visible;return r||t.cables_available===true},false)});return e}function jr(e,r){r.forEach(function(r){return e[r].forEach(function(e){if("p1"in e){if(e.p1.right)e.y1=e.p1.right.y2;if(e.p1.top&&!(e.type===q&&e.p1.top.type===Y)&&!(e.type===ne&&e.flip===true))e.x1=e.p1.top.x2}if("p3"in e){if(e.p3.left)e.y2=e.p3.left.y1;if(e.p3.bottom&&!(e.type===q&&e.p3.bottom.type===Y)&&!(e.type===ne&&e.flip===false))e.x2=e.p3.bottom.x1}})});return e}function Or(e){Object.values(e).forEach(function(e){return e.forEach(function(e){if("p1"in e)e.p1={i:e.p1.i};if("p2"in e)e.p2={i:e.p2.i};if("p3"in e)e.p3={i:e.p3.i};if("p4"in e)e.p4={i:e.p4.i}})});return e}function Tr(e,r){r.forEach(function(r){return e[r].forEach(function(e){if(!!e.flip){var r=[e.p3,e.p1];e.p1=r[0];e.p3=r[1]}})});return e}function Mr(e,r){Object.entries(r).forEach(function(r){var t=S()(r,2),n=t[0],a=t[1];e[n].forEach(function(r){r.type=a;e[a].push(r)});e[n]=[]});return e}function Dr(e,r,t,n){var a=arguments.length>4&&arguments[4]!==undefined?arguments[4]:null;var i=br(e,r,a);if(a!=="front"){i=xr(i,null,a==="back");i=xr(i,null,a==="back","horizontal");i=xr(i,null,a==="back","vertical")}i=xr(i,n.concat(t),a==="back");i=xr(i,n.concat(t),a==="back","horizontal");i=xr(i,n.concat(t),a==="back","vertical");t=t.concat(i);return t}function Ir(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;return function(t,n){var a=Number(t[e])-Number(n[e]);return a!==0||r===null?a:Number(t[r])-Number(n[r])}}function Cr(e,r,t){var n=[];var a=e.sort(Ir("x1"));for(var i=0;i<a.length;i+=1){n=sr(a[i],r,n,F,true)}t[V]=n;return t}function Pr(e,r,t){var n=[];var a=e.sort(Ir("y1"));for(var i=0;i<a.length;i+=1){n=vr(a[i],r,n,F)}t[ae]=n;return t}function Fr(e,r,t){var n=[];var a=e.sort(Ir("x1"));for(var i=0;i<a.length;i+=1){n=sr(a[i],r,n,ar,false)}t[H]=n;return t}function Nr(e,r,t){var n=[];var a=e.sort(Ir("y1"));for(var i=0;i<a.length;i+=1){n=vr(a[i],r,n,ar)}t[Y]=n;return t}function Lr(e,r,t){var n=t[V];var a=[];var i=e.sort(Ir("x1"));for(var o=0;o<i.length;o+=1){var f=hr(i[o],r,n,a,F);var u=S()(f,2);n=u[0];a=u[1]}t[V]=n;t[X]=a;return t}function Rr(e,r,t){var n=t[ae];var a=[];var i=e.sort(Ir("y1"));for(var o=0;o<i.length;o+=1){var f=dr(i[o],r,n,a,F);var u=S()(f,2);n=u[0];a=u[1]}t[ae]=n;t[Q]=a;return t}function Br(e,r,t){var n=t[R];var a=[];var i=e.sort(Ir("x1","y1"));for(var o=0;o<i.length;o+=1){var f=mr(i[o],r,n,a,F);var u=S()(f,2);n=u[0];a=u[1]}t[R]=n;t[J]=a;return t}function Wr(e,r,t){var n=[];var a=e.sort(Ir("x1","y1"));for(var i=0;i<a.length;i+=1){n=pr(a[i],r,n,F)}t[ne]=n;return t}function Ur(e,r,t,n){var a=arguments.length>4&&arguments[4]!==undefined?arguments[4]:[];var i=arguments.length>5&&arguments[5]!==undefined?arguments[5]:null;var o=a.reduce(function(e,r){return e.concat(t[r])},[]);var f=[];var u=e[ve[n]].sort(Ir("x1","y1"));for(var c=0;c<u.length;c+=1){f=Dr(u[c],r,f,o,i)}t[n]=f;return t}function qr(e){if(e[V].length===0||e[ae].length===0||e[K].length>0)return e;var r=20;var t=130;var n=600;var a=20;var i=20;var o=e[V].map(function(e){return e.y2}).sort(function(e,r){return e-r})[0];var f=e[V].filter(function(e){return e.y2===o});var u=e[ae].filter(function(e){return e.y1===o});var c=[];f.forEach(function(e){var a=e.x1,i=e.x2;var o=u.map(function(e){return(e.x1+e.x2)/2}).sort(function(e,r){return e-r}).filter(function(e){return e>=a+r&&e<=i-r});o=o.length>0?o:[a+r];o.forEach(function(e,f){if(f===0&&e-a-r>t)c.push(a+r);if(c.length===0)c.push(e);else{var u=e-c[c.length-1];if(u>n)c.push(parseInt(Math.floor(e-u/2)));if(u>t)c.push(e)}if(f===o.length-1){var l=i-r-c[c.length-1];if(l>n)c.push(parseInt(Math.floor(e+l/2)));if(l>t)c.push(i-r)}})});var l=[];c.forEach(function(e){[f[0].z1+i,f[0].z2-i].forEach(function(r){l.push({x1:e-10,x2:e+10,y1:f[0].y1-a,y2:f[0].y1,z1:r-10,z2:r+10,type:K})})});e[K]=l;return e}var Gr=function e(r){var t;var n=r.geometry,a=r.width,i=r.height;var o=ir(n),f=S()(o,2),u=f[0],c=f[1];var l=or(u,c);var s={};s=Cr(n.horizontals,l,s);s=Lr(n.erasers_horizontal,l,s);s=Pr(n.verticals,l,s);s=Rr(n.erasers_vertical,l,s);s=Fr(n.inserts_horizontal,l,s);s=Nr(n.inserts_vertical,l,s);s=Ur(n,l,s,ee);s=Ur(n,l,s,B,[],"front");s=Ur(n,l,s,U,[ee]);s=Ur(n,l,s,$,[ee,U]);s=Ur(n,l,s,q,[ee],"front");s=Ur(n,l,s,ue,[],"front");s=Ur(n,l,s,fe,[],"front");s=Ur(n,l,s,le,[],"front");s=Ur(n,l,s,ce,[],"front");s=Ur(n,l,s,R,[],"back");s=Wr(n.supports,l,s);s=Br(n.erasers_back,l,s);s[re]=n.plinth;s[K]=n.legs;s[Z]=n.mark;s=Er(s);s=Sr(s,U);s=Sr(s,q);s=jr(s,[ee,B,U,$,q,R,ne,ue,fe,le,ce]);s=Tr(s,[U,q,ne]);s=Mr(s,(t={},rr()(t,q,U),rr()(t,B,R),t));s=Or(s);s=qr(s);var v=Ar(n.components,s);var p={width:a,height:i,components:v,linesX:u,linesY:c,geometry:Object.entries(se).reduce(function(e,r){var t=S()(r,2),n=t[0],a=t[1];e[n]=s[a]||[];return e},{})};if(Object.keys(n).includes("horizontals")&&Object.keys(n).includes("verticals")){p.height=n.horizontals.reduce(function(e,r){return r.y2>e?r.y2:e},n.verticals.reduce(function(e,r){return r.y2>e?r.y2:e},0))}p["density_mode"]=r.density_mode;p["distortion_mode"]=r.distortion_mode;return p};t.d(r,"get_elements",function(){return Vr});t.d(r,"getChoicesWithThumbnails",function(){return Hr});t.d(r,"getThumbnailsForMeshConfig",function(){return Yr});t.d(r,"getGeometry",function(){return et});t.d(r,"getGeometryForPostPayload",function(){return tt});t.d(r,"getComponentMiniatureData",function(){return nt});t.d(r,"generateIsoForGeometry",function(){return at});var $r={width:100,height:600,components:{},geometry:{verticals:[{type:"V",x1:300,x2:400,y1:250,y2:350,z1:0,z2:120},{type:"V",x1:300,x2:400,y1:450,y2:550,z1:0,z2:120},{type:"V",x1:700,x2:800,y1:150,y2:250,z1:0,z2:120},{type:"V",x1:700,x2:800,y1:300,y2:650,z1:0,z2:120}],horizontals:[{type:"H",x1:300,x2:600,y1:150,y2:250,z1:0,z2:120},{type:"H",x1:300,x2:600,y1:350,y2:450,z1:0,z2:120},{type:"H",x1:300,x2:600,y1:550,y2:650,z1:0,z2:120}],backs:[],fronts:[],components:[],doors:[],doors_front:[],drawers:[],legs:[],opening:[],plinth:[],spacers:[],supports:[],inserts_horizontal:[],inserts_vertical:[],shadows_inner:[],shadows_outer:[],shadows_right:[],shadows_left:[]}};var Vr=function e(r,t,n,a,i,o){var f=arguments.length>6&&arguments[6]!==undefined?arguments[6]:320;var u=arguments.length>7&&arguments[7]!==undefined?arguments[7]:null;var c=arguments.length>8&&arguments[8]!==undefined?arguments[8]:null;var l={serialization:JSON.stringify(r)};if(r["geom_type"]==="component")r["geom_type"]="component-set";var s=w()({},{geom_id:r["geom_id"],geom_type:r["geom_type"],height:1},{width:n,row_amount:a,row_heights:i,row_styles:o,motion:t});s["depth"]=f;var v={format:"raw"};var p=et(r,s,v);var h=be(p,null,null,true,false,i);if(u==="flat"){h=w()({},h,h["additional_elements"]);delete h.additional_elements;var d=["horizontals","verticals","supports","backs","legs","doors","drawers","plinth_y","plinth_x","boxes"];d.forEach(function(e){if(h[e]!==undefined)h[e].forEach(function(e){delete e.c_config_id})})}return h};var Hr=function e(r,t,n){if(!t.serialization.hasOwnProperty("configurator_data"))return null;var a={};var i=true;var o=false;var f=undefined;try{for(var u=r.components[Symbol.iterator](),c;!(i=(c=u.next()).done);i=true){var l=c.value,s=l.table_id,v=l.series_id,p=l.m_config_id,h=l.x1,d=l.x2,m=l.door_flip,g=l.channel_id,b=l.cables;a[p]=Yr(r,t,p)}}catch(y){o=true;f=y}finally{try{if(!i&&u.return!=null){u.return()}}finally{if(o){throw f}}}return a};var Yr=function e(r,t,n){if(!t.serialization.hasOwnProperty("configurator_data"))return null;var a=r.components.find(function(e){return e.m_config_id===n});if(!a)null;var i=a.table_id,o=a.series_id,f=a.x1,u=a.x2,c=a.door_flip,l=a.y1,s=a.y2,v=a.cables;var p={};var h=s-l;t.serialization.configurator_data.table[i][h].forEach(function(e){var r=e.component_id,t=e.series_id,n=e.series_name,a=e.order,i=e.inconsequent;if(o!==t&&i)return true;if(p.hasOwnProperty(r)&&o!==t)return true;p[r]={component_id:r,series_id:t,series_name:n,order:a,inconsequent:i}});var d=u-f;var m={door_flip:c,cables:v};var g=me(r);var b=Zr(a,g,r.width);var y=Xr(t,p,d,m);var _=Qr(y);return Kr(d,_,b)};function Xr(e,r,t,n){var a={};Object.values(r).forEach(function(r){var i={width:t,geom_id:r.component_id,geom_type:"component",generate_thumbnails:false,custom_configurator_params:n};var o={format:"raw"};var f=et(e,i,o);var u=Jr(f);var c=ge(f,false);a["".concat(r.component_id)]=w()({},r,{rawGeometry:f,strGeometry:c,score:u})});return a}function Qr(e){var r=[];var t=[];var n=[];Object.entries(e).forEach(function(e){var a=e[1].strGeometry;if(!r.includes(a)){r.push(a);t.push(e[0])}else{n.push(e[0])}});n.forEach(function(r){var n=e[r];var a=t.findIndex(function(r){return e[r].strFormat===n.strFormat});var i=e[t[a]];if(i.inconsequent&&!n.inconsequent)t.splice(a,1,r)});return t.map(function(r){return e[r]})}function Jr(e){var r=0;r+=e.geometry.inserts_horizontal.length*75;r+=e.geometry.inserts_vertical.length*75;r+=e.geometry.doors.length*300;r+=e.geometry.drawers.length*704;return r}function Kr(e,r,t){var n=[];Object.values(r).sort(function(e,r){return e.score-r.score}).forEach(function(r){var a=r.component_id,i=r.series_id,o=r.series_name,f=r.order,u=r.rawGeometry;n.push({component_id:a,series_id:i,series_name:o,order:f,thumbnail:nt(u,true,[0,1.5],e,true),boundingBox:t})});return n}function Zr(e,r,t){var n=e.x1-F/2;var a=e.x2+F/2;var i=0-F/2;var o=e.y2+F/2;var f=e.z1-F/2;var u=e.z2+F/2;if(r){Object.keys(r).forEach(function(t){var i=t.split("_").indexOf(e.channel_id.toString());if(i===0)a+=r[t].v_max-r[t].value;if(i===1)n-=Math.abs(r[t].v_min-r[t].value)})}n-=t/2;a-=t/2;return{p1:[n,i,f],p2:[n,o,f],p3:[a,o,f],p4:[a,i,f],p5:[n,i,u],p6:[n,o,u],p7:[a,o,u],p8:[a,i,u],pMin:[n,i,f],pMax:[a,o,u]}}var et=function e(r,t,n){t=w()({},{width:null,height:null,depth:320,motion:null,density:null,distortion:null,mesh_setup:null,generate_thumbnails:false,thumbsForChannel:null,configurator_custom_params:null,geom_id:r.geom_id,geom_type:r.geom_type},t);n=w()({},{format:"gallery"},n);if(t.configurator_custom_params&&t.configurator_custom_params.hasOwnProperty("setups")){console.log("!!!#!!!#!!! --- WARNING --- !!!#!!!#!!!");console.log("--- setups in configuration_custom_params are not needed ---");console.log("!!!#!!!#!!! --- WARNING --- !!!#!!!#!!!")}var a=r.serialization;var i=Ze(a.serialization?a.serialization:a,t);var o=Gr(i);var f=t.generate_thumbnails?Hr(o,r,t):null;switch(n.format){case"gallery":var u=be(o);if(t.generate_thumbnails){u["thumbnails"]=f}return u;case"raw":return o;case"production":default:var c=xe(o,-t.width/2);if(t.generate_thumbnails){c["thumbnails"]=f}return c}};function rt(e,r){var t=e.geometry.erasers_back.map(function(e){return[e.p1,e.p3]});e.geometry.backs.forEach(function(e){if(t.length>0){var n=t.map(function(e){return e[0].i});var a=t.map(function(e){return e[1].i});var i=[e.p1.i,e.p2.i,e.p3.i,e.p4.i];var o=n.some(function(e){return i.includes(e)});var f=a.some(function(e){return i.includes(e)});if(f)e.y1=e.y1;else e.y1=e.y1-r;if(o)e.y2=e.y2;else e.y2=e.y2+r}else{e.y1=e.y1-r;e.y2=e.y2+r}e.x1=e.x1-r;e.x2=e.x2+r})}var tt=function e(r,n){var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;var i={};var o={};var f;var u;var c;var l={};var s=t("c994");switch(Number(a)){case 0:case 1:f=2;break;case 11:f=0;break;case 12:f=1;break;case 13:f=2;break;case 14:f=3;break;case 15:f=4;break;case 16:f=5;break;case 17:f=6;break;case 18:f=7;break}(function(){switch(f){case 0:case 1:case 2:case 5:i=et(r,n,{format:"raw"});o=xe(i,-n.width/2);break;case 9:break;case 6:case 3:u=r["serialization"]["serialization"]["mesh"];c=Object.values(u)[0];var e=Object.keys(c["setups"]);var t=c["parameters"]["size_y"];var a=c["parameters"]["size_x"];l["mesh_x_domain"]=c["parameters"]["size_x"];l["setups_x_domains"]={};l["distortion_mode"]={};l["config_count"]={};e.forEach(function(e){l["config_count"][e]=0;var a=c["setups"][e]["parameters"]["dim_x"];l["setups_x_domains"][e]=a;if("distortion_mode"in c["setups"][e]["parameters"]){l["distortion_mode"][e]=c["setups"][e]["parameters"]["distortion_mode"]}var f=parseInt(e);i[f]={};o[f]={};var u={};t.forEach(function(e){var t=parseInt(e);var c=s(n);c["height"]=t;c["width"]=f;u={};var l;var v;try{l=et(r,c,{format:"raw"})}catch(p){l={}}try{v=xe(l,-c.width/2)}catch(p){v={item:{elements:[],components:[]},x_domain:a}}i[f][t]=l;o[f][t]=v});l["config_count"][f]=Object.keys(Object.values(i[f])[0]["components"]).length});break;case 4:var v;var p;try{v=et(r,n,{format:"raw"})}catch(A){v={}}try{p=xe(v,-n.width/2)}catch(A){p={item:{elements:[],components:[]}}}l["original_mesh_geom"]=p;var h=r.serialization.configurator_data.table;var d=r.serialization.serialization.component_series;var m=n["height"].toString().replace(" ","");var g=p.item.components;var b={};var y={};var _=[];var x=[];var w=function e(t){i[t]=[];o[t]=[];x.push([]);var a=g[t];var f=a["table_id"];var u=a["series_id"];var c=d[u]["setups"][m];var l=a["components"][0]["x_domain"];var v=(l[1]-l[0])/100;b[t]={table_id:f,comp_index:t,series_id:u,comp_id:c,width:v};var p=h[f][m];p.forEach(function(e){x[t].push(e["component_id"]);var a=s(n);a["geom_type"]="component";a["geom_id"]=e["component_id"].toString();a["width"]=v;var f=et(r,a,{format:"raw"});var u=xe(f,-a.width/2);i[t].push(f);o[t].push(u)})};for(var k=0;k<g.length;k++){w(k)}break;case 7:var z=[0,50,100];var E=[0,50,100];var S;z.forEach(function(e){o[e]={};E.forEach(function(t){S=s(n);S["density"]=e;S["distortion"]=t;try{v=et(r,S,{format:"raw"})}catch(A){v={}}try{p=xe(v,-n.width/2)}catch(A){p={item:{elements:[],components:[]}}}o[e][t]=p})})}})();return{mode:f,production_geom:o,geom_id:n.geom_id,geom_type:n.geom_type,width:n.width,height:n.height||0,additional_info:l}};var nt=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:[0,1.5];var i=arguments.length>3&&arguments[3]!==undefined?arguments[3]:550;var o=arguments.length>4&&arguments[4]!==undefined?arguments[4]:true;var f=null;if(r.temporaryfix){var u={width:i,geom_id:r.id,geom_type:"component",generate_thumbnails:false,custom_configurator_params:null};var c={format:"raw"};var l=et(r.serialization,u,c);f=be(l)}else{f=be(r)}var s=function e(r,t,n){return(r-t[0])*(n[1]-n[0])/(t[1]-t[0])+n[0]};var v=[0,1200];var p=0;var h={lines:[],backs:[],fronts:[]};var d={keysPoly:{horizontals:5,verticals:7},keysPolyInserts:{inserts:8},keysRect:{backs:1,doors:2,supports:3,drawers:4}};var m=Object.keys(d.keysPoly).concat(Object.keys(d.keysPolyInserts)).concat(Object.keys(d.keysRect));var g=[];var b=Object.entries(f).filter(function(e){return m.includes(e[0])});b.forEach(function(e){e[1].forEach(function(e){g.push(Math.max(e.y1,e.y2))})});var y=Math.max.apply(Math,g)/2;var _=Object.entries(f).filter(function(e){return Object.keys(d.keysPoly).includes(e[0])});var x=Object.entries(f).filter(function(e){return Object.keys(d.keysPolyInserts).includes(e[0])});var w=Object.entries(f).filter(function(e){return Object.keys(d.keysRect).includes(e[0])});_.forEach(function(e){e[1].forEach(function(r){var a={geom:[[r.x1+p/2,r.y1-y],[r.x2+p/2,r.y2-y]],color:d.keysPoly[e[0]],mode:0};if(t===true){a.geom[0]=a.geom[0].map(function(e){return s(e,v,n)});a.geom[1]=a.geom[1].map(function(e){return s(e,v,n)})}h.lines.push(a)})});x.forEach(function(e){e[1].forEach(function(r){var a,i,o,f;if(r.x2-r.x1!==18){a=r.x1;i=r.x2;o=(r.y1+r.y1)/2-y;f=o-y}else{a=(r.x1+r.x2)/2;i=a;o=r.y1-y;f=r.y2-y}var u={geom:[[a+p/2,o],[i+p/2,f]],color:d.keysPolyInserts[e[0]],mode:0};if(t===true){u.geom[0]=u.geom[0].map(function(e){return s(e,v,n)});u.geom[1]=u.geom[1].map(function(e){return s(e,v,n)})}h.lines.push(u)})});w.forEach(function(e){var r=e[0];e[1].forEach(function(e){var a;if(o===true&&(r==="doors"||r==="drawers"||r==="backs")){a=at({elemType:r,elem:e,componentSize:[i,y]})}else{a={geom:[[e.x1+p/2,e.y1-y],[e.x2+p/2,e.y1-y],[e.x2+p/2,e.y2-y],[e.x1+p/2,e.y2-y]]}}a.color=d.keysRect[r];a.mode=2;if(t===true){a.geom[0]=a.geom[0].map(function(e){return s(e,v,n)});a.geom[1]=a.geom[1].map(function(e){return s(e,v,n)});a.geom[2]=a.geom[2].map(function(e){return s(e,v,n)});a.geom[3]=a.geom[3].map(function(e){return s(e,v,n)})}switch(r){case"doors":case"drawers":h.fronts.push(a);break;case"backs":case"supports":h.backs.push(a);break;default:break}})});return[].concat(a()(h.fronts),a()(h.lines),a()(h.backs))};var at=function e(r){var t={};var n=r.componentSize[0];var a=r.componentSize[1];if(r.elemType==="drawers"){var i=25;var o=45;t={geom:[[r.elem.x1-o,r.elem.y1-a-i],[r.elem.x2+o,r.elem.y1-a-i],[r.elem.x2+o,r.elem.y2-a-i],[r.elem.x1-o,r.elem.y2-a-i]]}}else if(r.elemType==="doors"){var f=r.elem.flip;var u=45;var c=15;var l=0;var s=r.elem.x2-r.elem.x1;var v;if(s<200){v=100}else if(s<400){v=150}else{v=200}if(f===1){t={geom:[[r.elem.x1,r.elem.y1-a-c],[r.elem.x1+v-l,r.elem.y1-a-u-c],[r.elem.x1+v-l,r.elem.y2-a-u+c],[r.elem.x1,r.elem.y2-a+c]]}}else{t={geom:[[r.elem.x2-v+l,r.elem.y1-a-u-c],[r.elem.x2,r.elem.y1-a-c],[r.elem.x2,r.elem.y2-a+c],[r.elem.x2-v+l,r.elem.y2-a-u+c]]}}}else if(r.elemType==="backs"){var p=50;if(r.elem.cable_up){t={geom:[[r.elem.x1,r.elem.y1-a],[r.elem.x2,r.elem.y1-a],[r.elem.x2,r.elem.y2-a-p],[r.elem.x1,r.elem.y2-a-p]]}}else if(r.elem.cable_down){t={geom:[[r.elem.x1,r.elem.y1-a+p],[r.elem.x2,r.elem.y1-a+p],[r.elem.x2,r.elem.y2-a],[r.elem.x1,r.elem.y2-a]]}}else{t={geom:[[r.elem.x1,r.elem.y1-a],[r.elem.x2,r.elem.y1-a],[r.elem.x2,r.elem.y2-a],[r.elem.x1,r.elem.y2-a]]}}}return t}},cf9e:function(e,r,t){var n=t("2b84");var a=t("760f");var i=t("0536")("species");e.exports=function(e){var r;if(a(e)){r=e.constructor;if(typeof r=="function"&&(r===Array||a(r.prototype)))r=undefined;if(n(r)){r=r[i];if(r===null)r=undefined}}return r===undefined?Array:r}},d01d:function(e,r,t){"use strict";var n=t("f861");var a=t("98ab");var i=t("e2e5");var o=t("0536")("species");e.exports=function(e){var r=n[e];if(i&&r&&!r[o])a.f(r,o,{configurable:true,get:function(){return this}})}},d118:function(e,r,t){r.f=t("0536")},d2b4:function(e,r,t){var n=t("0e26");var a=t("6ac8");var i=t("b1cb");var o=t("3955");var f=t("d7d0");var u=t("2b52");var c={};var l={};var r=e.exports=function(e,r,t,s,v){var p=v?function(){return e}:u(e);var h=n(t,s,r?2:1);var d=0;var m,g,b,y;if(typeof p!="function")throw TypeError(e+" is not iterable!");if(i(p))for(m=f(e.length);m>d;d++){y=r?h(o(g=e[d])[0],g[1]):h(e[d]);if(y===c||y===l)return y}else for(b=p.call(e);!(g=b.next()).done;){y=a(b,h,g.value,r);if(y===c||y===l)return y}};r.BREAK=c;r.RETURN=l},d2c4:function(e,r,t){"use strict";var n=t("1f03");var a=t("a85c");var i=t("c0f6");var o=t("f7b2");var f=t("0536");e.exports=function(e,r,t){var u=f(e);var c=t(o,u,""[e]);var l=c[0];var s=c[1];if(i(function(){var r={};r[u]=function(){return 7};return""[e](r)!=7})){a(String.prototype,e,l);n(RegExp.prototype,u,r==2?function(e,r){return s.call(e,this,r)}:function(e){return s.call(e,this)})}}},d6b6:function(e,r,t){"use strict";var n=t("f861");var a=t("d8a8");var i=t("a2ce");var o=t("c249");var f=t("1008");var u=t("c0f6");var c=t("ba1d").f;var l=t("dc2d").f;var s=t("98ab").f;var v=t("4ce8").trim;var p="Number";var h=n[p];var d=h;var m=h.prototype;var g=i(t("84c3")(m))==p;var b="trim"in String.prototype;var y=function(e){var r=f(e,false);if(typeof r=="string"&&r.length>2){r=b?r.trim():v(r,3);var t=r.charCodeAt(0);var n,a,i;if(t===43||t===45){n=r.charCodeAt(2);if(n===88||n===120)return NaN}else if(t===48){switch(r.charCodeAt(1)){case 66:case 98:a=2;i=49;break;case 79:case 111:a=8;i=55;break;default:return+r}for(var o=r.slice(2),u=0,c=o.length,l;u<c;u++){l=o.charCodeAt(u);if(l<48||l>i)return NaN}return parseInt(o,a)}}return+r};if(!h(" 0o1")||!h("0b1")||h("+0x1")){h=function e(r){var t=arguments.length<1?0:r;var n=this;return n instanceof h&&(g?u(function(){m.valueOf.call(n)}):i(n)!=p)?o(new d(y(t)),n,h):y(t)};for(var _=t("e2e5")?c(d):("MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,"+"EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,"+"MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger").split(","),x=0,w;_.length>x;x++){if(a(d,w=_[x])&&!a(h,w)){s(h,w,l(d,w))}}h.prototype=m;m.constructor=h;t("a85c")(n,p,h)}},d744:function(e,r,t){var n=t("e288");var a=Math.max;var i=Math.min;e.exports=function(e,r){e=n(e);return e<0?a(e+r,0):i(e,r)}},d7d0:function(e,r,t){var n=t("e288");var a=Math.min;e.exports=function(e){return e>0?a(n(e),9007199254740991):0}},d865:function(e,r){e.exports=t;function t(e,r){var t=new Float32Array(2);t[0]=e;t[1]=r;return t}},d8a8:function(e,r){var t={}.hasOwnProperty;e.exports=function(e,r){return t.call(e,r)}},da3a:function(e,r,t){var n=t("2b84");var a=t("f861").document;var i=n(a)&&n(a.createElement);e.exports=function(e){return i?a.createElement(e):{}}},dc2d:function(e,r,t){var n=t("5159");var a=t("5a3a");var i=t("46ab");var o=t("1008");var f=t("d8a8");var u=t("4f29");var c=Object.getOwnPropertyDescriptor;r.f=t("e2e5")?c:function e(r,t){r=i(r);t=o(t,true);if(u)try{return c(r,t)}catch(l){}if(f(r,t))return a(!n.f.call(r,t),r[t])}},df4b:function(e,r){e.exports=t;function t(e,r,t){var n=r[0],a=r[1];e[0]=t[0]*n+t[2]*a;e[1]=t[1]*n+t[3]*a;return e}},e079:function(e,r){e.exports=t;function t(e,r,t){e[0]=r;e[1]=t;return e}},e1c2:function(e,r,t){var n=t("22fe");n(n.S+n.F,"Object",{assign:t("6f2f")})},e213:function(e,r,t){e.exports={EPSILON:t("3443"),create:t("123d"),clone:t("4bcc"),fromValues:t("d865"),copy:t("3cc3"),set:t("e079"),equals:t("1788"),exactEquals:t("4516"),add:t("fac2"),subtract:t("706c"),sub:t("b8f0"),multiply:t("bbaa"),mul:t("305e"),divide:t("06ed"),div:t("4af6"),inverse:t("40ec"),min:t("74e5"),max:t("f80d"),rotate:t("b23e"),floor:t("0aae"),ceil:t("ccbc"),round:t("4a46"),scale:t("332e"),scaleAndAdd:t("fa7f"),distance:t("339b"),dist:t("086a"),squaredDistance:t("0b70"),sqrDist:t("a654"),length:t("c848"),len:t("be2a"),squaredLength:t("4869"),sqrLen:t("62bf"),negate:t("033c"),normalize:t("b7c3"),dot:t("6bc6"),cross:t("3761"),lerp:t("c435"),random:t("2f6e"),transformMat2:t("df4b"),transformMat2d:t("6e53"),transformMat3:t("4f06"),transformMat4:t("0043"),forEach:t("14a2"),limit:t("e6b9")}},e288:function(e,r){var t=Math.ceil;var n=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?n:t)(e)}},e2e5:function(e,r,t){e.exports=!t("c0f6")(function(){return Object.defineProperty({},"a",{get:function(){return 7}}).a!=7})},e3f9:function(e,r,t){"use strict";var n=t("22fe");var a=t("bbff");var i="includes";n(n.P+n.F*t("167a")(i),"String",{includes:function e(r){return!!~a(this,r,i).indexOf(r,arguments.length>1?arguments[1]:undefined)}})},e4e6:function(e,r){var t=e.exports={version:"2.5.7"};if(typeof __e=="number")__e=t},e6b9:function(e,r){e.exports=t;function t(e,r,t){var n=r[0]*r[0]+r[1]*r[1];if(n>t*t){var a=Math.sqrt(n);e[0]=r[0]/a*t;e[1]=r[1]/a*t}else{e[0]=r[0];e[1]=r[1]}return e}},f7b2:function(e,r){e.exports=function(e){if(e==undefined)throw TypeError("Can't call method on  "+e);return e}},f80d:function(e,r){e.exports=t;function t(e,r,t){e[0]=Math.max(r[0],t[0]);e[1]=Math.max(r[1],t[1]);return e}},f861:function(e,r){var t=e.exports=typeof window!="undefined"&&window.Math==Math?window:typeof self!="undefined"&&self.Math==Math?self:Function("return this")();if(typeof __g=="number")__g=t},f994:function(e,r,t){"use strict";var n=t("22fe");var a=t("c33d")(true);n(n.P,"Array",{includes:function e(r){return a(this,r,arguments.length>1?arguments[1]:undefined)}});t("c853")("includes")},fa7f:function(e,r){e.exports=t;function t(e,r,t,n){e[0]=r[0]+t[0]*n;e[1]=r[1]+t[1]*n;return e}},fac2:function(e,r){e.exports=t;function t(e,r,t){e[0]=r[0]+t[0];e[1]=r[1]+t[1];return e}},fb2b:function(e,r,t){var n=t("a911");var a=t("3546");t("bdb0")("keys",function(){return function e(r){return a(n(r))}})},fd04:function(e,r,t){var n=t("f861");var a=t("e4e6");var i=t("8b78");var o=t("d118");var f=t("98ab").f;e.exports=function(e){var r=a.Symbol||(a.Symbol=i?{}:n.Symbol||{});if(e.charAt(0)!="_"&&!(e in r))f(r,e,{value:o.f(e)})}},fe4e:function(e,r,t){var n=t("98ab").f;var a=t("d8a8");var i=t("0536")("toStringTag");e.exports=function(e,r,t){if(e&&!a(e=t?e:e.prototype,i))n(e,i,{configurable:true,value:r})}}});
//# sourceMappingURL=883d4f4d49c04658af51.worker.js.map