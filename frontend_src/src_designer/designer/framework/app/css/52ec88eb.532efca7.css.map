{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/tylko-presets.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/tylko-slider.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/tylko-card.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/tylko-section.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/tylko-tags.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/rpc/rpc-actions.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/TylkoMiniature.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/setups-bar/setups-bar-mesh.vue"], "names": [], "mappings": "AA8BA,qBACI,mBAAoB,CCdxB,4DACI,kBAAmB,CCWvB,eACI,UAAY,CAEhB,aACI,kBAA2B,CAE/B,kBACI,cAAe,CACf,eAAgB,CAChB,UAAY,CC/BhB,aACI,+BAAwC,CAE5C,yBAEQ,+BAA4C,CAC5C,gBAAiB,CCQzB,UACI,eAAmB,CCavB,uBACI,UAAW,CACX,mBAAoB,CAExB,yBACI,WAAY,CACZ,YAAa,CAEjB,6BAII,wBAAyB,CAJ7B,8BAEQ,UAAY,CC2BpB,uBACI,WAAY,CACZ,YAAa,CC/CjB,wCACI,2BAAgC,CAEpC,sBACI,kBAA2B,CAE/B,YACI,UAAW,CAEf,gBACI,0BAA2B,CAC3B,UAAW", "file": "52ec88eb.532efca7.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.tylko-preset-picker {\n    padding-bottom: 10px;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.tylko-input {\n    padding-bottom: 4px;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.card-disabled {\n    opacity: 0.6;\n}\n.card-action {\n    background: rgb(14, 14, 14);\n}\n.force-title-size {\n    font-size: 16px;\n    font-weight: 200;\n    color: white;\n}\n", "\n\n\n\n\n\n\n.bottom-line {\n    border-bottom: 1px solid rgb(24, 24, 24);\n}\n.expansion {\n    .card-content {\n        background-color: rgb(17, 17, 17) !important;\n        padding-top: 16px;\n    }\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.standout {\n    background: #cccccc;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.full {\n    width: 100%;\n    padding-bottom: 10px;\n}\n.global {\n    width: 300px;\n    padding: 20px;\n}\n.modal-main {\n    * {\n        color: white;\n    }\n    background-color: #1a1a1a;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.mini {\n    width: 100px;\n    height: 100px;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.q-field--dark .q-field__control:before {\n    border-color: #333333 !important;\n}\n.setup-list-container {\n    background: rgb(19, 19, 19);\n}\n.setup-tabs {\n    color: #ccc;\n}\n.cards-bar-area {\n    height: calc(100vh - 230px);\n    width: 100%;\n}\n"]}