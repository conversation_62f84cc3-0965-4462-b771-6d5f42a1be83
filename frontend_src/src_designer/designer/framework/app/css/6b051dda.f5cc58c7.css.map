{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/cape-exploded-view-mesh.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/summary/cape-collection-visualisation/collection-visuallisation.vue"], "names": [], "mappings": "AA+FA,aACI,WAAY,CACZ,YAAa,CClDjB,QACI,oBAAqB,CAEzB,SACI,cAAe,CACf,YAAa,CAEb,UAAW,CACX,gBAAiB,CACjB,eAAgB,CAEhB,wBAAiC,CAIjC,+ZAK8D,CAC9D,+EACiD,CACjD,0BAA2B", "file": "6b051dda.f5cc58c7.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.mesh-widget {\n    width: 600px;\n    height: 400px;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.inline {\n    display: inline-block;\n}\n.cape-bg {\n    position: fixed;\n    height: 102vh;\n    overflow: scroll;\n    width: 100%;\n    min-height: 100vh;\n    overflow: scroll;\n\n    background-color: rgb(19, 19, 19);\n    $color: rgba(220, 220, 220, 0.04);\n    $size: 40px;\n\n    background-image: -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px);\n    background-size: $size * 4 $size * 4, $size * 4 $size * 4, $size $size,\n        $size $size, $size/2 $size/2, $size/2 $size/2;\n    background-position-x: -6px;\n}\n"]}