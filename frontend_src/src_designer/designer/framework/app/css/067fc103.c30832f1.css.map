{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/views/root-index-page.vue"], "names": [], "mappings": "AA6DA,wBACI,WAAY,CACZ,YAAa,CAGjB,uBACI,qBAAuB,CAIvB,+ZAK8D,CAC9D,+EACiD,CAZrD,2BAeQ,yBAA2B", "file": "067fc103.c30832f1.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.block {\n    width: 400px;\n    height: 600px;\n}\n\n.main {\n    background-color: black;\n    $color: rgba(220, 220, 220, 0.04);\n    $size: 90px;\n\n    background-image: -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px);\n    background-size: $size * 4 $size * 4, $size * 4 $size * 4, $size $size,\n        $size $size, $size/2 $size/2, $size/2 $size/2;\n\n    svg {\n        margin-top: -2em !important;\n    }\n}\n"]}