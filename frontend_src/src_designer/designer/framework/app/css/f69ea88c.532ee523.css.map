{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@theme/card.scss", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/experimental/cape-camera/camera-helper.vue"], "names": [], "mappings": "AAAA,QACI,kBAA2B,CAG/B,SAOI,aAAc,CACd,qBAAsB,CAEtB,kBAA2B,CAC3B,+BAAwC,CAX5C,0BAIQ,mBAAoB,CAJ5B,WAaU,UAAY,CAbtB,wBAgBQ,UAAY,CACZ,WAAY,CACZ,kBAA2B,CAC3B,UAAW,CACX,cAAe,CAUf,YAAa,CA9BrB,+BAsBY,WAAY,CAEZ,cAAe,CACf,sBAAuB,CACvB,oBAAqB,CACrB,kBAAmB,CACnB,eAAgB,CA5B5B,uBAkCQ,aAAc,CAlCtB,kDAqCgB,kBAA2B,CAC3B,UAAY,CAtC5B,wCA2CY,eAAmB,CACnB,UAAY,CACZ,iBAAkB,CAClB,kBAAmB,CACnB,iBAAkB,CA/C9B,2BA0DY,aAAc,CA1D1B,sDA6DoB,eAA2B,CAC3B,UAAY,CA9DhC,4BAmEY,UAAY,CACZ,WAAY,CACZ,4BAAqC,CACrC,kBAA2B,CCqSvC,IACI,WAAY,CACZ,0BAA2B,CAC3B,kBAAmB", "file": "f69ea88c.532ee523.css", "sourcesContent": [".t-card {\n    background: rgb(27, 27, 27);\n}\n\ndiv.card {\n\n\n    .tylko-field-box {\n        padding-bottom: 10px;\n    }\n\n    display: block;\n    box-sizing: border-box;\n\n    background: rgb(25, 25, 25);\n    border-bottom: 1px solid rgb(39, 39, 39);\n\n    > * { color: white; }\n\n    div.card-title {\n        color: white;\n        height: 60px;  \n        background: rgb(19, 19, 19);\n        width: 100%;\n        font-size: 16px;\n        p.lead {\n            padding: 6px;\n\n            max-width: 100%;\n            text-overflow: ellipsis;\n            display: inline-block;\n            white-space: nowrap;\n            overflow: hidden;\n        }\n        padding: 12px;\n    }\n\n    div.card-main {\n        color: #a5a5a5;\n        .q-tab-panels {\n            .q-tab-panel {\n                background: rgb(19, 19, 19);\n                color: white;\n            }\n        }\n\n        div.card-content {\n            background: #222222;\n            color: white;\n            padding-left: 32px;\n            padding-right: 18px;\n            margin-left: -16px;\n        }\n    }\n\n    div.card-sepearator {\n\n    }\n\n\n    &.mid {\n        div.card-main {\n            color: #a5a5a5;\n            .q-tab-panels {\n                .q-tab-panel {\n                    background: rgb(34, 34, 34);\n                    color: white;\n                }\n            }\n        }\n        div.card-title {\n            color: white;\n            height: 80px;  \n            border-top: 2px solid rgb(24, 24, 24);\n            background: rgb(25, 25, 25);\n        }\n    }\n\n}", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n@import '~@theme/card.scss';\n\n.ac {\n    width: 420px;\n    height: calc(100vh - 235px);\n    background: #131313;\n}\n"]}