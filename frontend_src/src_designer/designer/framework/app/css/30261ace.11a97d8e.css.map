{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/TylkoMiniature.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/summary/cape-collection-page/collection-page.vue"], "names": [], "mappings": "AAwEA,uBACI,WAAY,CACZ,YAAa,CCCjB,iCACI,kBAA2B,CAC3B,UAAW,CAGf,mCACI,kBAA2B,CAG/B,+BACI,oBAAqB,CACrB,eAAgB,CASpB,qCAEI,WAAY,CAGhB,yCACI,oBAAqB,CACrB,gBAAiB,CACjB,gBAAiB,CACjB,WAAY,CAEhB,oCACI,YAAa,CAEjB,mCACI,oBAAqB,CACrB,eAAgB,CAChB,gBAAiB,CACjB,WAAY,CAGhB,+DAEI,wBAAiC,CAIjC,+ZAK8D,CAC9D,+EACiD,CACjD,0BAA2B,CAG/B,yBACI,oBAAqB,CAGzB,qBACI,aAAc,CACd,iBAAkB,CAClB,aAAc,CACd,YAAa,CAJjB,wCAOQ,YAAa,CAPrB,8CAWQ,UAAW,CAXnB,+CAgBQ,qBAAuB,CAhB/B,6CAoBQ,eAAiB,CAIzB,kCACI,oBAAqB,CACrB,UAAY,CAGhB,uCACI,kBAAmB,CACnB,gBAAiB,CACjB,cAAe,CAGnB,iCACI,kBAAmB,CACnB,gBAAiB,CACjB,cAAe", "file": "30261ace.11a97d8e.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.mini {\n    width: 100px;\n    height: 100px;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.tabs-panel-bar {\n    background: rgb(29, 29, 29);\n    color: #ccc;\n}\n\n.tabs-panel-color {\n    background: rgb(19, 19, 19);\n}\n\n.card-wrapper {\n    display: inline-block;\n    max-width: 300px;\n\n    .card-spearator {\n    }\n\n    .card-container {\n    }\n}\n\n.collection-summary {\n    /*max-width: 935px;*/\n    margin: 10px;\n}\n\n.card-component-summary {\n    display: inline-block;\n    max-width: 1800px;\n    max-height: 935px;\n    margin: 10px;\n}\n.section-component {\n    display: flex;\n}\n.column-component {\n    display: inline-block;\n    max-width: 100px;\n    max-height: 935px;\n    margin: 10px;\n}\n\n.collection-details,\n.cape-bg {\n    background-color: rgb(19, 19, 19);\n    $color: rgba(220, 220, 220, 0.04);\n    $size: 40px;\n\n    background-image: -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px);\n    background-size: $size * 4 $size * 4, $size * 4 $size * 4, $size $size,\n        $size $size, $size/2 $size/2, $size/2 $size/2;\n    background-position-x: -6px;\n}\n\n.inline {\n    display: inline-block;\n}\n\n.sc {\n    overflow: auto;\n    position: relative;\n    display: block;\n    height: 100vh;\n\n    &::-webkit-scrollbar {\n        display: none;\n    }\n\n    &::-webkit-scrollbar-track {\n        width: 10px;\n        //  background-color: rgb(63, 63, 63); /* or add it to the track */\n    }\n\n    &::-webkit-scrollbar-button {\n        background-color: white;\n    }\n\n    &:-webkit-scrollbar-thumb {\n        background: black;\n    }\n}\n\n.collection-link {\n    text-decoration: none;\n    color: white;\n}\n\n.button_counter_large {\n    padding-right: 28px;\n    margin-right: 7px;\n    margin-top: 8px;\n}\n\n.button_counter {\n    padding-right: 18px;\n    margin-right: 7px;\n    margin-top: 8px;\n}\n"]}