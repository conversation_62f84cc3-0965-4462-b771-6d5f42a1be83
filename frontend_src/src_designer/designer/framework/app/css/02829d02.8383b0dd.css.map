{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/summary/cape-graph/cape-graph.vue"], "names": [], "mappings": "AA8BA,OACI,WAAY,CACZ,YAAa,CAEjB,QACI,oBAAqB,CAEzB,SACI,cAAe,CACf,YAAa,CAEb,UAAW,CACX,gBAAiB,CACjB,eAAgB,CAEhB,wBAAiC,CAIjC,+ZAK8D,CAC9D,+EACiD,CACjD,0BAA2B", "file": "02829d02.8383b0dd.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.graph{\n    width: 100vw;\n    height: 100vh;\n}\n.inline {\n    display: inline-block;\n}\n.cape-bg {\n    position: fixed;\n    height: 102vh;\n    overflow: scroll;\n    width: 100%;\n    min-height: 100vh;\n    overflow: scroll;\n\n    background-color: rgb(19, 19, 19);\n    $color: rgba(220, 220, 220, 0.04);\n    $size: 40px;\n\n    background-image: -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px),\n        -webkit-linear-gradient($color 1px, transparent 1px),\n        -webkit-linear-gradient(0deg, $color 1px, transparent 1px);\n    background-size: $size * 4 $size * 4, $size * 4 $size * 4, $size $size,\n        $size $size, $size/2 $size/2, $size/2 $size/2;\n    background-position-x: -6px;\n}\n"]}