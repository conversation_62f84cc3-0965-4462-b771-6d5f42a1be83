{"version": 3, "sources": ["/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cards/editors/table-setup-card.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cards/editors/tags-card.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cards/editors/setup-card-mesh.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@theme/card.scss", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cards/editors/configuration-card.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-palette/palette-container.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@theme/elements-bar.scss", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-palette/palette-bar.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@cape-ui/editors/setup-editor.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/component-select.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/configurators/cape-preview-configurator/renderer-view.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/editors/cape-mesh-editor/meshes-page-set.vue", "/Users/<USER>/tylko/cstm/frontend_src/src_designer/designer/app/@tylko/cape-entrypoints/editors/cape-mesh-editor/meshes-page.vue"], "names": [], "mappings": "AA4FA,gBACI,eAAiB,CACjB,QAAS,CACT,cAAe,CAGnB,aACI,YAAa,CACb,cAAe,CAFnB,0DAKQ,UAAY,CALpB,uBAQQ,qBAAsB,CAR9B,uCAUY,oBAAuB,CAVnC,sCAaY,iBAAkB,CCtB9B,iKAIQ,UAAY,CCdpB,4DAGQ,YAAa,CAGrB,QACI,oBAAuB,CACvB,eAAgB,CAChB,wBAA0B,CAH9B,sBAMQ,eAAgB,CANxB,wBASQ,eAAkB,CAClB,qBAAsB,CAV9B,kBAaQ,kBAAmB,CAb3B,4BAgBQ,eAAgB,CAhBxB,gCAkBY,wBAA2B,CAlBvC,8BAqBY,wBAA2B,CAC3B,WAAY,CC3GxB,QACI,kBAA2B,CAG/B,SAOI,aAAc,CACd,qBAAsB,CAEtB,kBAA2B,CAC3B,+BAAwC,CAX5C,0BAIQ,mBAAoB,CAJ5B,WAaU,UAAY,CAbtB,wBAgBQ,UAAY,CACZ,WAAY,CACZ,kBAA2B,CAC3B,UAAW,CACX,cAAe,CAUf,YAAa,CA9BrB,+BAsBY,WAAY,CAEZ,cAAe,CACf,sBAAuB,CACvB,oBAAqB,CACrB,kBAAmB,CACnB,eAAgB,CA5B5B,uBAkCQ,aAAc,CAlCtB,kDAqCgB,kBAA2B,CAC3B,UAAY,CAtC5B,wCA2CY,eAAmB,CACnB,UAAY,CACZ,iBAAkB,CAClB,kBAAmB,CACnB,iBAAkB,CA/C9B,2BA0DY,aAAc,CA1D1B,sDA6DoB,eAA2B,CAC3B,UAAY,CA9DhC,4BAmEY,UAAY,CACZ,WAAY,CACZ,4BAAqC,CACrC,kBAA2B,CCuCvC,GACI,UAAW,CADf,kBAGQ,cAAe,CAEf,oBAAiB,CAIzB,oBACI,cAAe,CADnB,6BAGQ,YAAa,CACb,iBAAkB,CAClB,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,wBAAyB,CARjC,8BAWQ,gBAAiB,CACjB,iBAAkB,CAI1B,OACI,oBAAqB,CACrB,UAAY,CACZ,cAAe,CAGnB,uBAEQ,gBAAiB,CCvDzB,kCACI,UAAW,CACX,YAAa,CC7FjB,eAEI,yBAA2B,CAC3B,eAAgB,CAChB,wBAAyB,CAJ7B,4DAOM,oBAAuB,CACvB,wBAAyB,CACzB,eAAgB,CAChB,wBAA0B,CAVhC,mCAgBI,2CAAoC,CAApC,mCAAoC,CAhBxC,aAoBI,oBAAuB,CApB3B,oBAsBM,WAAY,CAtBlB,0BA0BM,oBAAuB,CACvB,eAAgB,CAChB,cAAe,CAKrB,OACE,eAAgB,CAChB,WAAY,CACZ,kBAAwB,CAH1B,kBAMM,6BAA+B,CAKrC,oBACE,UAAY,CACZ,SAAU,CACV,mBAAoB,CACpB,iBAAkB,CAClB,UAAW,CAGb,cACE,eAAgB,CAGlB,OACE,KAAQ,CACR,cAAe,CACf,WAAY,CACZ,UAAW,CACX,UAAW,CACX,YAAa,CAGf,gBACE,QAIkB,CAGpB,kCANE,OAAU,CACV,QAAS,CACT,WAAY,CACZ,iBAQkB,CALpB,kBACE,WAIkB,CAGpB,kBACE,YAAa,CACb,OAAU,CACV,QAAS,CACT,WAAY,CACZ,iBAAkB,CAGpB,yBACE,iBAAkB,CAClB,gBAAiB,CACjB,UAAW,CAGb,eACE,UAAW,CACX,YAAa,CAFf,wBAII,WAAY,CACZ,qBAAuB,CAI3B,MACE,cAAe,CACf,aAAc,CACd,iBAAkB,CAClB,eAAgB,CAChB,WAAY,CACZ,UAAW,CACX,SAAU,CACV,UAAW,CAGb,eAEI,oBAAsB,CAI1B,gCAEI,UAAY,CCkKhB,OACI,qBAAuB,CCZ3B,uBACI,2BAA6B,CAC7B,KAAQ,CACR,UAAY,CACZ,gBAAiB,CCpKrB,kBACI,eAAgB,CADpB,0BAGQ,uCAA+B,CAC/B,cAAe,CAJvB,4BAOQ,iBAAkB,CAP1B,oEAWQ,UAAY,CCsxBpB,SACI,UAAW,CAGf,cACI,UAAW,CACX,WAAY,CACZ,eAAgB,CAChB,iBAAkB,CAGtB,mCAEQ,UAAY,CAIpB,QACI,mBAAoB,CAGxB,gBACI,wBAA0B,CAC1B,WAAY,CACZ,eAAgB,CAGpB,aACI,wBAA0B,CAC1B,eAAiB,CAFrB,4BAYQ,iBAAkB,CAClB,kBAAmB,CACnB,sBAAuB,CACvB,YAAa,CACb,WAVQ,CANhB,iDASgB,YAAe,CAT/B,iFAqBgB,kBAA2B,CArB3C,iGAuBoB,aAAc,CAvBlC,yCA6BgB,wBAAyB,CA7BzC,kCA0DY,iBAAkB,CAClB,cAAe,CAEf,kBAA2B,CA7DvC,oCAmCgB,cAAe,CAnC/B,0CAuCgB,KAAQ,CACR,iBAAkB,CAClB,UAAW,CACX,YAAa,CACb,eAAiB,CACjB,YAAa,CACb,SAxCA,CAyCA,eAxCA,CAyCA,eAAgB,CAChB,eAAgB,CAhDhC,iDAkDoB,gBAAiB,CACjB,iBAAkB,CAnDtC,kDAsDoB,WAAY,CACZ,UAAY,CAWhC,UACI,WAAY,CACZ,eAAgB,CAChB,YAAa,CAGjB,iBACI,cAAe,CACf,QAAS,CACT,UAAW,CACX,iBAAkB,CAGtB,iBACI,WAAY,CACZ,WAAY,CAGhB,cACI,WAAY,CAGhB,4BACI,WAAY,CAGhB,kBACI,eAAiB,CAGrB,OACI,aAAc,CACd,eAAgB,CAGhB,iBAAkB,CAClB,eAAgB,CAChB,gBAAiB,CAGrB,eAPI,UAAW,CACX,WAauB,CAP3B,QAEI,gBAAiB,CACjB,eAAgB,CAGhB,oBAAqB,CACrB,oBAAuB,CAP3B,eAUQ,mBAAqB,CAI7B,cAEQ,oBAAqB,CACrB,UAAW,CAInB,UACI,iBAAkB,CADtB,iBAGQ,UAAW,CASnB,kCALY,YAQG,CAHf,KAEI,iBAAkB,CAClB,UAAW,CAHf,UAMQ,aAAc,CAItB,oBACI,OAAU,CACV,iBAAkB,CAClB,KAAM,CACN,WAAY,CACZ,iBAAkB,CAClB,WAAY,CACZ,qBAAuB,CACvB,aAAiB,CACjB,YAAa,CAGjB,aACI,SAAU,CACV,eAAgB,CAChB,YAAa,CAGjB,cACI,oBAAqB,CAGzB,wBACI,aAAc,CACd,cAAe,CAGnB,oBACI,UAAW,CAGf,oBACI,SAAU,CAGd,oBACI,UAAW,CAGf,qBACI,UAAW,CAGf,iBACI,aAAgB,CAGpB,qBACI,aAA2B,CAG/B,2BACI,aAAoB,CAGxB,0BACI,aAAkB,CAGtB,eACI,cAAe,CACf,MAAO,CACP,KAAM,CACN,YAAa,CC9jCjB,uBACI,eAAgB,CAChB,gBAAiB,CAOrB,kEACI,YAAa,CAGjB,iCACI,0BACW,CA8Of,6BACI,kBAA2B,CAC3B,UAAW,CAGf,iCACI,0BAA2B,CAC3B,UAAW,CCnUf,uBACI,eAAgB,CAChB,gBAAiB,CAOrB,kEACI,YAAa,CAGjB,iCACI,0BAA2B,CAC3B,UAAW", "file": "32741f73.98d23890.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.series-heading {\n    font-weight: bold;\n    margin: 0;\n    font-size: 16px;\n}\n\n.flex-series {\n    display: flex;\n    flex-wrap: wrap;\n    .q-input-target,\n    .q-input-shadow {\n        color: white;\n    }\n    .q-select {\n        width: calc(50% - 5px);\n        .q-input-target {\n            color: white !important;\n        }\n        &:nth-child(odd) {\n            margin-right: 10px;\n        }\n    }\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.tag-select-wrapper {\n    .q-if-inverted-light .q-if-label,\n    .q-if-inverted-light .q-if-addon,\n    .q-if-inverted-light .q-if-control {\n        color: white;\n    }\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.input-flex {\n    .q-field-label-inner,\n    .q-field-label {\n        display: flex;\n    }\n}\n.q-card {\n    font-family: 'Inter UI';\n    font-weight: 600;\n    font-size: 13px !important;\n\n    .q-card-title {\n        font-size: 1.2em;\n    }\n    .q-btn-dropdown {\n        padding-right: 0px;\n        margin: 2px 12px 6px 0;\n    }\n    .margin-b {\n        margin-bottom: 24px;\n    }\n    .q-card-title-extra {\n        font-size: 1.2em;\n        > div {\n            font-size: 0.6em !important;\n        }\n        i {\n            font-size: 0.7em !important;\n            padding: 3px;\n        }\n    }\n}\n", ".t-card {\n    background: rgb(27, 27, 27);\n}\n\ndiv.card {\n\n\n    .tylko-field-box {\n        padding-bottom: 10px;\n    }\n\n    display: block;\n    box-sizing: border-box;\n\n    background: rgb(25, 25, 25);\n    border-bottom: 1px solid rgb(39, 39, 39);\n\n    > * { color: white; }\n\n    div.card-title {\n        color: white;\n        height: 60px;  \n        background: rgb(19, 19, 19);\n        width: 100%;\n        font-size: 16px;\n        p.lead {\n            padding: 6px;\n\n            max-width: 100%;\n            text-overflow: ellipsis;\n            display: inline-block;\n            white-space: nowrap;\n            overflow: hidden;\n        }\n        padding: 12px;\n    }\n\n    div.card-main {\n        color: #a5a5a5;\n        .q-tab-panels {\n            .q-tab-panel {\n                background: rgb(19, 19, 19);\n                color: white;\n            }\n        }\n\n        div.card-content {\n            background: #222222;\n            color: white;\n            padding-left: 32px;\n            padding-right: 18px;\n            margin-left: -16px;\n        }\n    }\n\n    div.card-sepearator {\n\n    }\n\n\n    &.mid {\n        div.card-main {\n            color: #a5a5a5;\n            .q-tab-panels {\n                .q-tab-panel {\n                    background: rgb(34, 34, 34);\n                    color: white;\n                }\n            }\n        }\n        div.card-title {\n            color: white;\n            height: 80px;  \n            border-top: 2px solid rgb(24, 24, 24);\n            background: rgb(25, 25, 25);\n        }\n    }\n\n}", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n@import '~@theme/card.scss';\n\n._ {\n    width: 100%;\n    &.display-inline {\n        display: inline;\n        padding: 2px;\n        padding-top: 14px;\n    }\n}\n\n.switches-container {\n    padding: 16px 0;\n    .heading {\n        margin-top: 0;\n        margin-bottom: 8px;\n        color: #979797;\n        font-size: 13px;\n        font-weight: 600;\n        text-transform: uppercase;\n    }\n    .q-option {\n        margin-right: 5px;\n        margin-bottom: 3px;\n    }\n}\n\n.admin {\n    text-decoration: none;\n    color: black;\n    font-size: 18px;\n}\n\n.config-actions {\n    .q-btn {\n        margin-right: 5px;\n    }\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.ruler-container {\n    width: auto;\n    height: 180px;\n}\n", ".top {\n  .q-select {\n    min-height: 44px !important;\n    max-height: 50px;\n    text-transform: uppercase;\n    .q-item-label,\n    .q-input-target{\n      font-family: \"Inter UI\";\n      text-transform: uppercase;\n      font-weight: 600;\n      font-size: 13px !important;\n    }\n  }\n\n  .q-if:before,\n  .q-if:after {\n    transform: scaleY(1) translateY(4px);\n  }\n\n  .family {\n    color: white !important;;\n    .q-tab {\n      padding: 5px;\n    }\n\n    .q-tab-label {\n      font-family: \"Inter UI\";\n      font-weight: 600;\n      font-size: 13px;\n    }\n  }\n}\n\n.black {\n  max-height: 60px;\n  height: 54px;\n  background: rgb(8, 8, 8);\n  .col-6 {\n    > div {\n      display: inline-flex !important;\n    }\n  }\n}\n\n.fake-container-bar {\n  opacity: 0.3;\n  z-index: 1;\n  pointer-events: none;\n  position: absolute;\n  width: 100%;\n}\n\n.small-search {\n  max-width: 200px;\n}\n\n.notop {\n  top: 0px;\n  position: fixed;\n  z-index: 100;\n  right: -5px;\n  width: 70px;\n  height: 184px;\n}\n\n.bottom-btn-bar {\n  bottom: 0px;\n  right: 0px;\n  top: auto;\n  margin: 20px;\n  position: absolute;\n}\n\n.bottom-btn-bar-2 {\n  bottom: 50px;\n  right: 0px;\n  top: auto;\n  margin: 20px;\n  position: absolute;\n}\n\n.bottom-btn-bar-3 {\n  bottom: 100px;\n  right: 0px;\n  top: auto;\n  margin: 20px;\n  position: absolute;\n}\n\n.draggable-container-bar {\n  position: relative;\n  min-height: 200px;\n  width: 100%;\n}\n\n.grid-bar-area {\n  width: 100%;\n  height: 190px;\n  &.vertical {\n    width: 320px;\n    height: 100% !important;\n  }\n}\n\n.item {\n  cursor: pointer;\n  display: block;\n  position: absolute;\n  min-width: 100px;\n  height: auto;\n  margin: 2px;\n  z-index: 1;\n  color: #fff;\n}\n\n.top-con {\n  .item {\n    width: auto !important;\n  }\n}\n\n.restocking {\n  .fake-container-bar {\n    opacity: 0.9;\n  }\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n@import '~@theme/elements-bar.scss';\n\n.users {\n    width: 400px !important;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.main-editor-container {\n    position: absolute !important;\n    top: 0px;\n    opacity: 0.2;\n    min-height: 400px;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.component-select {\n    margin-top: 15px;\n    .select {\n        border-top: 1px solid rgba(#fff, 0.3);\n        margin-top: 5px;\n    }\n    .q-option {\n        margin-right: 10px;\n    }\n    .q-input-target,\n    .q-input-shadow {\n        color: white;\n    }\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.mainRow {\n    width: 100%;\n}\n\n.svgContainer {\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    position: absolute;\n}\n\n.white-color-input {\n    .q-input-target {\n        color: white;\n    }\n}\n\n.opaque {\n    pointer-events: none;\n}\n\n.ruler-vertical {\n    max-width: 30px !important;\n    height: 100%;\n    overflow: hidden;\n}\n\n.tool-column {\n    max-width: 44px !important;\n    background: black;\n\n    div.tool-entry {\n        $w: 44px;\n        $h: 50px;\n        &.settingsTool {\n            .action {\n                margin-top: 0px; // -300\n            }\n        }\n        position: relative;\n        align-items: center;\n        justify-content: center;\n        display: flex;\n        height: $h;\n\n        &.active,\n        &:hover {\n            .icon {\n                background: rgb(15, 15, 15);\n                .action {\n                    display: block;\n                }\n            }\n        }\n        &.active {\n            > .icon {\n                background-color: #0051ff;\n            }\n        }\n\n        .icon {\n            i {\n                font-size: 26px;\n            }\n\n            .action {\n                top: 0px;\n                position: absolute;\n                width: auto;\n                z-index: 1000;\n                background: black;\n                display: none;\n                left: $w;\n                min-height: $h;\n                text-align: left;\n                min-width: 250px;\n                &.scroll {\n                    max-height: 500px;\n                    overflow-y: scroll;\n                }\n                &.special {\n                    width: 500px;\n                    color: white;\n                }\n            }\n            text-align: center;\n            cursor: pointer;\n\n            background: rgb(12, 12, 12);\n        }\n    }\n}\n\n.renderer {\n    height: 100%;\n    overflow: hidden;\n    display: flex;\n}\n\n.preview-spinner {\n    position: fixed;\n    left: 7px;\n    bottom: 9px;\n    z-index: 100000000;\n}\n\n.bottom-settings {\n    bottom: 20px;\n    width: 300px;\n}\n\n.top-settings {\n    width: 506px;\n}\n\n.production-renderer-canvas {\n    height: 100%;\n}\n\n.vertical-toolbar {\n    background: black;\n}\n\n.t-tab {\n    display: block;\n    background: #333;\n    width: 100%;\n    height: 100%;\n    padding-right: 5px;\n    padding-top: 9px;\n    padding-left: 7px;\n}\n\n.t-dark {\n    padding: 0px;\n    margin-right: 5px;\n    padding-top: 5px;\n    width: 100%;\n    height: 100%;\n    display: inline-block;\n    opacity: 0.7 !important;\n\n    &.active {\n        opacity: 1 !important;\n    }\n}\n\n.specialbtn {\n    a {\n        text-decoration: none;\n        color: #fff;\n    }\n}\n\n.renderer {\n    position: relative;\n    canvas {\n        width: 100%;\n    }\n    &.hide-canvas {\n        canvas {\n            display: none;\n        }\n    }\n}\n\n.svg {\n    display: none;\n    position: relative;\n    width: 100%;\n\n    &.show {\n        display: block;\n    }\n}\n\n.description_column {\n    right: 0px;\n    position: absolute;\n    top: 0;\n    width: 400px;\n    overflow-y: scroll;\n    height: 100%;\n    background-color: black;\n    color: whitesmoke;\n    display: none;\n}\n\n#description {\n    top: 120px;\n    min-height: 50px;\n    display: none;\n}\n\n.renderjson a {\n    text-decoration: none;\n}\n\n.renderjson .disclosure {\n    color: crimson;\n    font-size: 150%;\n}\n\n.renderjson .syntax {\n    color: grey;\n}\n\n.renderjson .string {\n    color: red;\n}\n\n.renderjson .number {\n    color: cyan;\n}\n\n.renderjson .boolean {\n    color: plum;\n}\n\n.renderjson .key {\n    color: lightblue;\n}\n\n.renderjson .keyword {\n    color: lightgoldenrodyellow;\n}\n\n.renderjson .object.syntax {\n    color: lightseagreen;\n}\n\n.renderjson .array.syntax {\n    color: lightsalmon;\n}\n\n#gui-container {\n    position: fixed;\n    left: 0;\n    top: 0;\n    display: none;\n}\n", "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.setup-tabs {\n    background: rgb(19, 19, 19);\n    color: #ccc;\n}\n\n.cards-bar-area {\n    height: calc(100vh - 230px);\n    width: 100%;\n}\n", "\n\n\n\n\n.main {\n    overflow: hidden;\n    max-height: 100vh;\n}\n\n.full-fixed-height {\n    height: 100vh;\n}\n\n.full-height {\n    height: 100vh;\n}\n\n.cards-bar-area {\n    height: calc(100vh - 245px);\n    width: 100%;\n}\n"]}