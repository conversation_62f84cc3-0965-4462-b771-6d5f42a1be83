/* Libraries & Plugins */

const path = require('path');
const Webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');
const BundleTracker = require('webpack-bundle-tracker');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const BrowserSyncPlugin = require('browser-sync-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const MergeJsonWebpackPlugin = require('merge-jsons-webpack-plugin');

/* Constants */
const IS_PRODUCTION = (process.env.NODE_ENV === 'production');
const IS_PRODUCTION_PART_ONE = (process.env.npm_lifecycle_event === 'prod:part1');
const IS_PRODUCTION_PART_FINAL = (process.env.npm_lifecycle_event === 'prod:part3');
const IS_DEVELOPMENT = !IS_PRODUCTION;
const IS_WATCHING = (process.argv.includes('--watch') || process.argv.includes('--hot'));
const IS_NOT_CLEANING = (process.argv.includes('--no-clean'));
const IS_ES5 = (process.argv.includes('--es5'));
const { BUILD_CHUNK } = process.env;
const MODE = IS_PRODUCTION ? 'production' : 'development';
const DEVTOOL = '#eval-source-map';
const STYLES_REGEX = /\.(sa|sc|c)ss$/;

console.log({
  argv: process.argv,
  BUILD_CHUNK,
  IS_PRODUCTION,
  IS_DEVELOPMENT,
  IS_WATCHING,
  IS_NOT_CLEANING,
  MODE,
});

/* Helpers */

function getPlugins() {
  const plugins = [
    new VueLoaderPlugin(),
    new Webpack.ProvidePlugin({
      $: 'jquery',
      jQuery: 'jquery',
    }),
    new MiniCssExtractPlugin({
      filename: IS_DEVELOPMENT ? '[name].css' : '[name].[hash].css',
      chunkFilename: IS_DEVELOPMENT ? '[id].css' : '[id].[hash].css',
    }),
  ];

  if (!IS_NOT_CLEANING) {
    plugins.push(
      new CleanWebpackPlugin(
        path.resolve('../src/frontend_cms/static/dist_vue/'),
        {
          root: '/',
          dry: false,
        },
      ),
    );
  }

  if (IS_WATCHING) {
    plugins.push(
      new BrowserSyncPlugin(
        {
          open: false,
          host: 'localhost',
          port: 3010,
          proxy: 'localhost:8000',
          files: [
            'frontend_src/scss/**/*.scss',
            'frontend_src/src_vue/**/*.js',
            'frontend_src/src/js/**/*.js',
          ],
        },
        {
          reload: true,
        },
      ),
    );
  }

  if (IS_PRODUCTION && IS_PRODUCTION_PART_FINAL) {
    plugins.push(
      new BundleTracker({
        filename: '../src/frontend_cms/static/dist_vue/webpack-stats-part3.json',
      }),
      new MergeJsonWebpackPlugin({
        files: [
          '../src/frontend_cms/static/dist_vue/webpack-stats-part1.json',
          '../src/frontend_cms/static/dist_vue/webpack-stats-part2.json',
        ],
        output: {
          fileName: 'webpack-stats.json',
        },
      }),
    );
  } else if (IS_PRODUCTION && !IS_PRODUCTION_PART_FINAL) {
    plugins.push(
      new BundleTracker({
        filename: `../src/frontend_cms/static/dist_vue/webpack-stats${IS_PRODUCTION_PART_ONE ? '-part1' : '-part2'}.json`,
      }),
    );
  } else {
    plugins.push(
      new BundleTracker({
        filename: `../src/frontend_cms/static/dist_vue/webpack-stats${IS_ES5 ? '-es5' : ''}.json`,
      }),
    );
  }

  return plugins;
}

function getEntries() {
  const entries = {
    atupale_2: './libs/atupale_2',
    blackShelf3dHero: './src/js/blackShelf3dHero/',
    elevatorPitchAnimation: './src/js/elevatorPitchAnimation/',
    empty: './src/js/empty',
    feedsAddToCategoryVue: './src_admin/js/feedsAddToCategoryTool',
    feedsViewCategory: './src_admin/js/gridViewCategoryTool',
    items: './src_webgl/items/index',
    priceFormatAutoInit: './libs/priceFormat/auto_init',
    priceFormatAutoInitMultiple: './libs/priceFormat/auto_init_multiple',
    priceFormatCore: './libs/priceFormat/core',
    ratingTool: './src_admin/js/ratingTool',
    vueConsents: './src_vue/CookieConsent',
    vueCplusV2: './src_vue/Configurators/FrontCplusV2/main',
    vueCrowV2: './src_vue/Configurators/FrontCrowV2/main',
    vueDeliveryTimeFrames: './src_vue/DeliveryTimeFrames',
    vueEmail24: './src_vue/Email24',
    wardrobes: './src_vue/Configurators/wardrobes/main',
    offscreen: './src_webgl/offscreenTool',
  };

  if (BUILD_CHUNK) {
    if (BUILD_CHUNK.includes(',')) {
      const output = {};
      const string = `["${BUILD_CHUNK}"]`.replace(/,/g, '", "');
      JSON.parse(string).forEach(item => output[item] = entries[item]);
      return output;
    } else {
      return {
        [BUILD_CHUNK]: entries[BUILD_CHUNK],
      };
    }
  }

  // eslint-disable-next-line no-console
  console.warn(`Webpack is going to build all (${Object.keys(entries).length}) entry points.`);
  return entries;
}

function getOptimization() {
  if (IS_WATCHING) {
    return {};
  }

  return {
    minimize: true,
    minimizer: [
      new OptimizeCSSAssetsPlugin({}),
      new TerserPlugin({
        parallel: true,
        sourceMap: true,
        cache: true,
        terserOptions: {
          ecma: undefined,
          warnings: false,
          parse: {},
          mangle: true,
          module: false,
          output: null,
          toplevel: false,
          nameCache: null,
          ie8: false,
          keep_classnames: undefined,
          keep_fnames: false,
          safari10: true,
        },
      }),
    ],
  };
}

/* Config */

module.exports = {
  mode: MODE,
  context: __dirname,
  devtool: IS_DEVELOPMENT && DEVTOOL,
  entry: getEntries(),
  plugins: getPlugins(),
  optimization: getOptimization(),
  output: {
    path: path.resolve('../src/frontend_cms/static/dist_vue/'),
    filename: `[name]-${IS_ES5 ? 'es5' : 'es6'}${IS_PRODUCTION ? '-[hash]' : ''}.js`,
    publicPath: '/r_static/dist_vue/',
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        use: [
          {
            loader: 'vue-loader',
          },
          {
            loader: 'vue-svg-inline-loader',
          },
        ],
      },
      IS_ES5
        ? {
          test: /\.js$/,
          loader: 'babel-loader',
          options: {
            presets: [
              [
                '@babel/preset-env',
                {
                  modules: 'commonjs',
                  useBuiltIns: 'usage',
                  corejs: 3,
                  targets: {
                    chrome: '50',
                    ie: '10',
                  },
                },
              ],
            ],
            plugins: [
              '@babel/plugin-transform-destructuring',
              '@babel/plugin-proposal-object-rest-spread',
              '@babel/plugin-transform-arrow-functions',
            ],
          },
          exclude: /node_modules/,
        }
        : {
          test: /\.js$/,
          loader: 'babel-loader',
          options: {
            presets: [
              [
                '@babel/preset-env',
                {
                  modules: 'commonjs',
                  useBuiltIns: 'usage',
                  corejs: 3,
                  targets: {
                    chrome: 68,
                    firefox: 62,
                    safari: 12,
                    edge: 79,
                  },
                },
              ],
            ],
            plugins: [],
          },
          exclude: /node_modules/,
        },
      {
        test: STYLES_REGEX,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              hmr: IS_DEVELOPMENT,
            },
          },
          'css-loader',
          'sass-loader',
        ],
      },
      {
        test: /\.svg$/,
        loader: 'vue-svg-inline-loader',
      },
      {
        test: /\.ts$/,
        loader: 'ts-loader',
      },
      {
        test: /\.(png|jpe?g|gif|webp)$/,
        loader: 'file-loader',
        options: {
          limit: 5000,
        },
      },
    ],
  },
  resolve: {
    alias: {
      vue$: 'vue/dist/vue.esm.js',
      '@root': path.resolve(__dirname, './'),
      '@tylko_ui': path.resolve(__dirname, './src_vue/tylko_ui'),
      '@frontCheckout': path.resolve(__dirname, './src_vue/FrontCheckout'),
      '@vue-locale': path.resolve(__dirname, './src_vue/vue-locale/'),
      '@tylko': path.resolve(__dirname, './src_designer/designer/app/@tylko/'),
      '@configurator-common': path.resolve(__dirname, './src_vue/@configurator-common'),
      '@cape-ui': path.resolve(__dirname, './src_designer/designer/app/@cape-ui/'),
      '@src_vue': path.resolve(__dirname, './src_vue/'),
      '@src_webgl': path.resolve(__dirname, './src_webgl/'),
      '@componentsConfigurator': path.resolve(__dirname, './src_vue/tylko_ui/components/configurator'),
      '@libs': path.resolve(__dirname, './libs/'),
      dna: path.resolve(__dirname, './src_webgl/webdesigner/'),
    },
    extensions: [
      '*',
      '.ts',
      '.js',
      '.vue',
      '.json',
    ],
  },
  performance: {
    hints: false,
  },
};
