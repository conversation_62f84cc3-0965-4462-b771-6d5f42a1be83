# See https://pre-commit.com for more information

files: '^src/'
repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.7
    hooks:
      - id: ruff-format
      - id: ruff-check
        args: [ --fix ]

  - repo: local
    hooks:
      - id: check-migrations
        name: Check Django migrations
        entry: bash ./check_migrations.sh
        language: system
        pass_filenames: true
        files: '(^.*models\.py$)|(^.*models/.*$)'

  - repo: https://github.com/gauge-sh/tach-pre-commit
    rev: v0.29.0
    hooks:
      - id: tach
